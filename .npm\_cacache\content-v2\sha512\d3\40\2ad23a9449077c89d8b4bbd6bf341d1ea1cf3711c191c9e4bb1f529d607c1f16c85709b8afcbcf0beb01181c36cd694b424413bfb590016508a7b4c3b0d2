{"_id": "ws", "_rev": "641-83c69ba4cac8597a07eb61f010c2a91b", "name": "ws", "dist-tags": {"latest": "8.18.3"}, "versions": {"0.2.6": {"name": "ws", "version": "0.2.6", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.2.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "aec644a272a71228f7cc86d41167a6ec855d8a12", "tarball": "https://registry.npmjs.org/ws/-/ws-0.2.6.tgz", "integrity": "sha512-X8UBKX1qUKWv4qWFlD9e7hdak68SygQEiW7UvkekwXY0tLAR5qLeu+a4OSn1mhJkY9pCiFGOC93VvDfYBGiIcg==", "signatures": [{"sig": "MEYCIQCCCcXnJ8Q3fNNHebTmwlVgdQO5yTXauWNTyR9+Majx6QIhAOSQHv2MDKLFcoN24a8MIWN6ytZ3kH0qzvyzsywUlFjf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "~0.6.0"}, "scripts": {"test": "make test", "preinstall": "make validator"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.0-alpha-6", "description": "simple and very fast websocket protocol client for node.js", "directories": {}, "_nodeVersion": "v0.6.4", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.0.x", "should": "0.3.2"}, "_engineSupported": true}, "0.2.8": {"name": "ws", "version": "0.2.8", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.2.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "861f66649e5019e0b188c380da99ec09fc078f95", "tarball": "https://registry.npmjs.org/ws/-/ws-0.2.8.tgz", "integrity": "sha512-+y5P9TVfp4J3NA3IL89MYAAlNReBuv8S5QcRxJcX/5to0P4PVlWMeOFCSIt+rpfjZ3AJt7r52fckv3JReDQaug==", "signatures": [{"sig": "MEUCIEkZGoatAPDzs+0dzGFCrhjK8k4YgbWu2kjJuFghVfa/AiEAih3PFSeOcIuks7WV75hpPGXljdOJ+lhii3lCivBimuQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">0.4.0"}, "scripts": {"test": "make test", "preinstall": "make validator"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "simple and very fast websocket protocol client for node.js", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.0.x", "should": "0.3.2"}, "_engineSupported": true}, "0.2.9": {"name": "ws", "version": "0.2.9", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.2.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "7f32270036409863d1401d6da6e58653b572b18b", "tarball": "https://registry.npmjs.org/ws/-/ws-0.2.9.tgz", "integrity": "sha512-RYUhotLNmjKkR1cpkMT8Ybj+Oa0H21c8ql8W42COp9QRr+dAvvDaeZia63ckximC/PEPty8YV3VCGbXmKuiQEA==", "signatures": [{"sig": "MEYCIQD2eHjMOOKsNl6gwddq/G2WH1PFXSmO7rcj2BkCF4GJlQIhAOXzPsQIzkaN9OcGwiu6/tHItijyqVML275DKF195Hx9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">0.4.0"}, "scripts": {"test": "make test", "preinstall": "make validator"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "simple and very fast websocket protocol client for node.js", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.0.x", "should": "0.3.2"}, "_engineSupported": true}, "0.3.0": {"name": "ws", "version": "0.3.0", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "c67c62352261fa6f8b1eb5dbcf2f9be969525aaa", "tarball": "https://registry.npmjs.org/ws/-/ws-0.3.0.tgz", "integrity": "sha512-Ykgm4wlflId1SgVK8HNFsp21vuINgn58hjVZd6OLmo/gEUMflh+IMIj1oqOvTu+Ykz3XT1nr+ZejKURSFx6+Vg==", "signatures": [{"sig": "MEUCIQCnTlRGjdloIZbW+3bDX1os3yGUkyClySYFACFuXoPoWAIgE8QfZnka230qEfqpe7df56tEIWCkQNbOvexQxZGypzw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make validator"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.0-alpha-6", "description": "simple and very fast websocket protocol client for node.js", "directories": {}, "_nodeVersion": "v0.6.5", "dependencies": {"commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.0.x", "should": "0.3.2"}, "_engineSupported": true}, "0.3.1": {"name": "ws", "version": "0.3.1", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "73f5d1b310f72594857ecaf0e5f79c7af86385a9", "tarball": "https://registry.npmjs.org/ws/-/ws-0.3.1.tgz", "integrity": "sha512-curlXtjxeXlso7N71SAmzW2HFKxRRQVqfGuc5z3QC/pGpA4wGs5gX12Dy1tFrihV0XdwJRKl9YbqpZEYUJ5FcQ==", "signatures": [{"sig": "MEUCIQD8AuqyCMoiZa7YruQZQGD3n/3sHZfiWL5ZMSaWpesG8wIgVZTDB9wKuxzkNEhJHjr/ZTWSTQqUEyustV6Olf393Yc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make validator"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.0-alpha-6", "description": "simple and very fast websocket protocol client for node.js", "directories": {}, "_nodeVersion": "v0.6.5", "dependencies": {"commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.3.x", "should": "0.3.2"}, "_engineSupported": true}, "0.3.2": {"name": "ws", "version": "0.3.2", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "c57326cba08f76231002b3e4fa595676938d39af", "tarball": "https://registry.npmjs.org/ws/-/ws-0.3.2.tgz", "integrity": "sha512-eua0MSSM/bxEWAUggpQA5YZSXQFoqlJBuV5k21w7lFyVlFPc8uvWqaMPlwtweg1PeeB6EVvS611vTyzLiJLD1A==", "signatures": [{"sig": "MEQCIAaCFZGHbePaYX9nYP4kHn95sq0XlvWleHKz/r7ZHFr1AiAsC/MLbLCjL2UttHLVjO3mvgHV2AzFR1Et0UKltrWNnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make validator"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.0-alpha-6", "description": "simple and very fast websocket protocol client for node.js", "directories": {}, "_nodeVersion": "v0.6.5", "dependencies": {"commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.3.x", "should": "0.3.2", "benchmark": "0.3.x", "tinycolor": "0.x"}, "_engineSupported": true}, "0.3.3": {"name": "ws", "version": "0.3.3", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.3.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "a506abb667a903e5a8a281009be3e976a1e17643", "tarball": "https://registry.npmjs.org/ws/-/ws-0.3.3.tgz", "integrity": "sha512-8x6/86AS8cTpUnkVU3izLKN/AXoC5jpv9bw++OOt/TW9nTHParBBhwYOW8Y0ImMoge0uZL3+Z1jcISMU+fAmeA==", "signatures": [{"sig": "MEUCIQCty/2e0fNP7/OHKMb5s9x7HIXdsaDhXAtwH1RGsyGKRAIgIF47XDEYqpAjahMoNmO19l88QmKvhXK/sRJ1GV81Gig=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make validator"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.0.106", "description": "simple and very fast websocket protocol client for node.js", "directories": {}, "_nodeVersion": "v0.4.12", "dependencies": {"commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.3.x", "should": "0.3.2", "benchmark": "0.3.x", "tinycolor": "0.x"}, "_engineSupported": true}, "0.3.4": {"name": "ws", "version": "0.3.4", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.3.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "2252c1b3a6d646d899d5bab7b31bd979b139fadd", "tarball": "https://registry.npmjs.org/ws/-/ws-0.3.4.tgz", "integrity": "sha512-Plnf99N5VnaHpEAuwLbEjqDHxyQnXQEDx8IKuwvwqV8FIJbwBu0n0yGIn/GHSwcpyYw5J4pKu+bryjxV37Lcfw==", "signatures": [{"sig": "MEUCIFKYTnyafyeYFBsb9ucv9I9DH31JK9tJSDR34InVsgzdAiEAlljaz8pBy/j5v8tFAQJHIAG6kx0bIU2CcdqA3sM9+CQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make validator"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.0-alpha-6", "description": "simple and very fast websocket protocol client for node.js", "directories": {}, "_nodeVersion": "v0.6.5", "dependencies": {"commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.3.x", "should": "0.3.2", "benchmark": "0.3.x", "tinycolor": "0.x"}, "_engineSupported": true}, "0.3.4-2": {"name": "ws", "version": "0.3.4-2", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.3.4-2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "8ff01a3ed0bee94ef4f4c6b7bb1b868c6a58e5fa", "tarball": "https://registry.npmjs.org/ws/-/ws-0.3.4-2.tgz", "integrity": "sha512-bkI5Aw0J8f5tnMV0PRAI0Y7VlpNohqAE7qP7ZwTY7n0PpQMuX4wCK+JM+whpuwaGgejsQ35OmYNkw+58MMkVYw==", "signatures": [{"sig": "MEUCIG/CFYXM709tzxJd/z4ZVi4k/XZuETbWkIe4rdfGB7gUAiEAqHpd2mW+J9p44q9BhA3erAaZeUUsbwB/B7ooMJ3C1gI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make validator"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.0-alpha-6", "description": "simple and very fast websocket protocol client for node.js", "directories": {}, "_nodeVersion": "v0.6.5", "dependencies": {"commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.3.x", "should": "0.3.2", "benchmark": "0.3.x", "tinycolor": "0.x"}, "_engineSupported": true}, "0.3.5": {"name": "ws", "version": "0.3.5", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.3.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "cdda02de927eaec577b4a67604075ec16c145527", "tarball": "https://registry.npmjs.org/ws/-/ws-0.3.5.tgz", "integrity": "sha512-IK767nXTSDgvoi+KB8pFn7eWir69x5NxCFdrgIaJ9qbaXWO2505Opyi+WawG/1Sk+PxR9vvWpi+5gt5jNbKDjQ==", "signatures": [{"sig": "MEUCIBzKSNzLCZkxy0fwcboO0wD9yLrtudrXJzP62HfcMzAZAiEA4MXz+t5NICIu5G44f0HUfzN7YqNaJFq4Pr57Vws+esM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make validator"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.0-alpha-6", "description": "simple and very fast websocket protocol client for node.js", "directories": {}, "_nodeVersion": "v0.6.5", "dependencies": {"commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.3.x", "should": "0.3.2", "benchmark": "0.3.x", "tinycolor": "0.x"}, "_engineSupported": true}, "0.3.5-2": {"name": "ws", "version": "0.3.5-2", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.3.5-2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "0e58b65ffb2eb597a85d08d5c975c54014f57f65", "tarball": "https://registry.npmjs.org/ws/-/ws-0.3.5-2.tgz", "integrity": "sha512-3xURHoC8uRaVMh4f1bWZRKOCkMh1/y4oK/wdLKyEUAysqE/MPnE8HNNlbSb7bWtO0DkV9sFTPGyzYb8vkVr8nA==", "signatures": [{"sig": "MEUCIAmv///tHPS69XdUcL4RryUq64ptl+s8HzOr5DW29C8lAiEAssExe6fidk7xLE5dUGvVQ4Kcql1TKuhYUtoGzFeREx8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make validator"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.0-alpha-6", "description": "simple and very fast websocket protocol client for node.js", "directories": {}, "_nodeVersion": "v0.6.5", "dependencies": {"commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.3.x", "should": "0.3.2", "benchmark": "0.3.x", "tinycolor": "0.x"}, "_engineSupported": true}, "0.3.5-3": {"name": "ws", "version": "0.3.5-3", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.3.5-3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "84afdaf8a4ed524ff41e38c18b7231ee5a98749f", "tarball": "https://registry.npmjs.org/ws/-/ws-0.3.5-3.tgz", "integrity": "sha512-hZTNq0A/ZFAN1979KBYZWHMz0YnHd/AHYDPYXmVxjJE+w65UGh4U2uu3ILdw20rAfB+FV/NalQp0Yp5Ed4NVSg==", "signatures": [{"sig": "MEYCIQCX4dhUreYB6qyS7gOJfNYotgy6OhXb+bO++mJKOZWbDAIhAKpoAi5hGiGmdP0vVagcJEyTtLDGcHyhEijlnXnmqsNV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make validator"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.0-alpha-6", "description": "simple and very fast websocket protocol client for node.js", "directories": {}, "_nodeVersion": "v0.6.5", "dependencies": {"commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.3.x", "should": "0.3.2", "benchmark": "0.3.x", "tinycolor": "0.x"}, "_engineSupported": true}, "0.3.5-4": {"name": "ws", "version": "0.3.5-4", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.3.5-4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "d4bd8a2e3659a85e7784a061088a9410fa70f1ae", "tarball": "https://registry.npmjs.org/ws/-/ws-0.3.5-4.tgz", "integrity": "sha512-n3iNJzb7qClM5f5BpATbdWpazxV3bWclr3MvwVIuJ3vcIQ7t1CKKqmxi0uOHCIQX1JfxzhrnhkvO9rM0c0a/SQ==", "signatures": [{"sig": "MEQCIArdbpDI4nUv2SlHnRdIoNi122koXrVmiHcSKmbBMOvNAiB1w7E9OI17m0Rl5Gf98DAxLoRJY55gm79+QUVOD5IESg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make validator"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.0-alpha-6", "description": "simple and very fast websocket protocol client for node.js", "directories": {}, "_nodeVersion": "v0.6.5", "dependencies": {"commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.3.x", "should": "0.3.2", "benchmark": "0.3.x", "tinycolor": "0.x"}, "_engineSupported": true}, "0.3.6": {"name": "ws", "version": "0.3.6", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.3.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "9a5f590afaf25b07c8068a3dca27a8ce53f94723", "tarball": "https://registry.npmjs.org/ws/-/ws-0.3.6.tgz", "integrity": "sha512-YXF+wQfuhgHj5ZQEbyf7bdiXnXI97ySF57KvW3KgVomgdV7mYQTHTC7l+qvRLjdjOUBnY61MdeNYnvsBHRpTcQ==", "signatures": [{"sig": "MEUCIQCLcvlvdrf8d1Hc/P4QjKdFcOR/62aqEoQGezCX9oGjrwIgXnUfhLUogNqS0xQ0q6GrUZHBJNP9kt7T/6PWR/3T6ig=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make validator"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.0-alpha-6", "description": "simple and very fast websocket protocol client for node.js", "directories": {}, "_nodeVersion": "v0.6.5", "dependencies": {"options": "latest", "commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.3.x", "should": "0.3.2", "benchmark": "0.3.x", "tinycolor": "0.x"}, "_engineSupported": true}, "0.3.7": {"name": "ws", "version": "0.3.7", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.3.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "8e4495cd48fffb5789792711b70af3a24a562921", "tarball": "https://registry.npmjs.org/ws/-/ws-0.3.7.tgz", "integrity": "sha512-GTsWIUlU3SRb7QbXCjyfvDcbEJXN4RBij+Oe3h0CRutObjXn0EKQ7Cl+gNrHsSUOCyYYulm5GzwtaIsKKhapOA==", "signatures": [{"sig": "MEUCIAzQ1MkUSWFUzOvXnYPrMMsAEkUz5ErxZpb027V8kqQuAiEArEPKeLxL8ADaZS7zCpnl+cNKM4sle4ao2mFxofRuL/k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make validator"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.0-beta-4", "description": "simple and very fast websocket protocol client for node.js", "directories": {}, "_nodeVersion": "v0.6.6", "dependencies": {"options": "latest", "commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.3.x", "should": "0.3.2", "benchmark": "0.3.x", "tinycolor": "0.x"}, "_engineSupported": true}, "0.3.8": {"name": "ws", "version": "0.3.8", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.3.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "34bcb09d0c3a32d7c2ba6fd1ee900662c9e35e23", "tarball": "https://registry.npmjs.org/ws/-/ws-0.3.8.tgz", "integrity": "sha512-LCh6ZODxCUyO9949p6v6Y7+MWFvCrUz9KmJbF6J/9KvLBEB4INI+bDVOWK14Snz0djjLg7IZwEfpf+63KEsZWA==", "signatures": [{"sig": "MEYCIQDNriyfvvTZoYoJ0d5/IexMAOKrmN7Pe3l90wMMeU1zggIhAK1kWtxP4CaAO9bM5uDZeqtOfc0Sp4K2l5E/Q9Xq+AnU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make validator"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.0-beta-4", "description": "simple and very fast websocket protocol client for node.js", "directories": {}, "_nodeVersion": "v0.6.6", "dependencies": {"options": "latest", "commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.3.x", "should": "0.3.2", "benchmark": "0.3.x", "tinycolor": "0.x"}, "_engineSupported": true}, "0.3.9": {"name": "ws", "version": "0.3.9", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.3.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "4884b921daced4f35b0f821652bb11dec47cda3c", "tarball": "https://registry.npmjs.org/ws/-/ws-0.3.9.tgz", "integrity": "sha512-kqvk/oR9VEYq590d9uGK6zPBgCHschTHn2eBVPlgFclhcIbNMDDQVR7vjXiEz/bbVbyO4ILyNnIcVyhEHKwbTw==", "signatures": [{"sig": "MEUCIQDw7H9k3W4zVEgEoiRKYRIamNeE+7yWqJmRRZDghDKd0wIgTJDXmhO9RylNgPe3kZvLOLqDgTeydaU3HHYG1HKgFpQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.0-beta-4", "description": "simple and very fast websocket protocol client for node.js", "directories": {}, "_nodeVersion": "v0.6.6", "dependencies": {"options": "latest", "commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.8.x", "should": "0.4.2", "benchmark": "0.3.x", "tinycolor": "0.x"}, "_engineSupported": true}, "0.4.0": {"name": "ws", "version": "0.4.0", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "b98d30cc64bc38b0c122aed85a712665af456b5a", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.0.tgz", "integrity": "sha512-2vweoJlK/RWFyU/PEYWwIRfktqimwEdoE4kG3auVk+3xkNaWMwEQGHqYVEHpKIfJVYvZM3pmw8bvbsCUUO8niA==", "signatures": [{"sig": "MEUCIQC3fMvFYNf4LD/K0Tjwhby2Tc1qKp7kA7kHQp0qxWhO3wIgFq+cCmYW7bDZzTVNGUjDEy6xYdHPeC49ugaUblOh1ac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.0-beta-4", "description": "simple and very fast websocket protocol client for node.js", "directories": {}, "_nodeVersion": "v0.6.6", "dependencies": {"options": "latest", "commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.8.x", "should": "0.4.2", "benchmark": "0.3.x", "tinycolor": "0.x"}, "_engineSupported": true}, "0.4.1": {"name": "ws", "version": "0.4.1", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "aaf0f86deb0b38af0f6f7e880d8f4dc61b2c553d", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.1.tgz", "integrity": "sha512-uAjEwMM/O1j8GYHewfwSc5iol9/unuuJkdMsFT8tYBkbAaUTcDuZniBJdooxtCNIuxa7mXlbvlD4WQ1tGzspMQ==", "signatures": [{"sig": "MEQCIALMcvaFB6fNXBwqun5+C3/OxeexNg5Mhk5xUJBZZYiDAiApGhG3wSRL91MwBxmx7R2nXiJKWqvwWC3N4jH/YSYBdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.0-2", "description": "simple and very fast websocket protocol client for node.js", "directories": {}, "_nodeVersion": "v0.6.8", "dependencies": {"options": "latest", "commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.8.x", "should": "0.4.2", "benchmark": "0.3.x", "tinycolor": "0.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.2": {"name": "ws", "version": "0.4.2", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "1744041779964c117cc5d2afe50a85b4ec2946c6", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.2.tgz", "integrity": "sha512-C95LEd/K5m4M1o0f2+ypFrk7dzWau8ciklNXb0Ltejqrf55a1u23nGoRcTDQFJ8EIYh9AoKZq7DsiyeRbAIAHw==", "signatures": [{"sig": "MEUCIHIOZ1TETEn5dLkcLSmmFzojkwCI/S+XpfvHBdKIn6uWAiEAnrkq81fWQGOPxmrbnuDqDr5exORFo/c+ImkwWZRb6I0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.0-2", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "v0.6.8", "dependencies": {"options": "latest", "commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.8.x", "should": "0.4.2", "benchmark": "0.3.x", "tinycolor": "0.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.3": {"name": "ws", "version": "0.4.3", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "ae6c2edf997d2a1f4a2855958edfe70ec42e24bc", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.3.tgz", "integrity": "sha512-eGCOsdeNNcqlht53qgqEqlLaZ5MJSY94cLC6+5MEBbvyNNElqzTsAU1P0HifXjayF6qPUZTHDEx7nsS7cAqmAA==", "signatures": [{"sig": "MEUCIQDFCd5AG/koNmHmhN7YKSoDohyPTAqPxF+1aYZ7fHrg+gIgLIChSXTsw8C1pkunABBBFYgv5vcji6AY31dR9qBw/1Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.0-2", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "v0.6.8", "dependencies": {"options": "latest", "commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.8.x", "should": "0.4.2", "benchmark": "0.3.x", "tinycolor": "0.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.5": {"name": "ws", "version": "0.4.5", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "1aaf999a59bce58e4f1f37280b878588003ed5d0", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.5.tgz", "integrity": "sha512-/6O+jja5z0LgML1GDplY0ssyEhDW4mDe48C1Mfvs+yqAYz8TeCEXsSTULxkKdmh31iawgK/pzWjABVYo6hAllA==", "signatures": [{"sig": "MEYCIQD04+j7ioFvKwcj/S5zsSJLt5VCL5VbDNJvhyiQVvfF5QIhAK7WGc9SP/Iv3zEknaaMhpH1D3YKOLF18DhCiRHfd0b6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.0-2", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "v0.6.8", "dependencies": {"options": "latest", "commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.8.x", "should": "0.4.2", "benchmark": "0.3.x", "tinycolor": "0.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.6": {"name": "ws", "version": "0.4.6", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "bd494be81d06329c50fbacb78345ea2b03bf595d", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.6.tgz", "integrity": "sha512-XgThRfKAtNQwG4wZaY+R8Jm5jx9Fo9WqG2ZHbH460LiFBUTGWogganImHStmvlcEuCfW4Ghqj/ad2SKHIasl5w==", "signatures": [{"sig": "MEUCIQDNS2qtUftK0pYWOCc7VHiR9z2OLUqn6DLoYsJO3HqTEQIgbFn71+hsI2LMxaziqB5kajbJ1XJrxktDm8cWN0FC1HI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.0-2", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "v0.6.8", "dependencies": {"options": "latest", "commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.8.x", "should": "0.4.2", "benchmark": "0.3.x", "tinycolor": "0.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.7": {"name": "ws", "version": "0.4.7", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "e299d45764627a6cce89378b0aca2bff6bb896d9", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.7.tgz", "integrity": "sha512-mprv+StVE0Qf3AiILAjLPEPoZ7+nJWyFe7CAMQc4F/A9JB6NOskD3XNSq6dpwEaxQK364B18XXR2snrUfoY+YQ==", "signatures": [{"sig": "MEUCIQCCNWVvYFpr9/49q4TkM5HLExL/9jOTosVoYT8pOzCYJwIgUZjjjWAzXwPlFruyZKtM1opQRMNtLQ4spLhoHe43tq8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.1", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "v0.6.11", "dependencies": {"options": "latest", "commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"mocha": "0.8.x", "should": "0.4.2", "benchmark": "0.3.x", "tinycolor": "0.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.8": {"name": "ws", "version": "0.4.8", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "f52dc246ca1cb8adde0947e2dd4646363989bcc1", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.8.tgz", "integrity": "sha512-WlC/3OJ2d6wgrIXNSbp2Wfisam6HoHsfVqIxEjmW/6N6ZjsiJVqGgK+9baYNABYxua+XQZEe1noJHEANDkywSw==", "signatures": [{"sig": "MEUCIAk7EXgC+HKYKMYwrxu/SJV4Sqwftiv48L2wtlq+VXvOAiEAi+Jx/yALqvgp3O9fmK+RWJDNDRp00V9s7Rxq1kZe1f0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.1", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "v0.6.11", "dependencies": {"options": "latest", "commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"ansi": "latest", "mocha": "0.8.x", "should": "0.4.2", "benchmark": "0.3.x", "tinycolor": "0.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.9": {"name": "ws", "version": "0.4.9", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "5a37b4607a8d15ea71cf5700f97bc1b35356b17b", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.9.tgz", "integrity": "sha512-OaC2vbYRFiAnhF+/KVnwIpOq4zQWUdcn7ZrX+NsL+e5ngDTGYshkV3037G97THZKKC/4/xfRc99PxROseAyN/g==", "signatures": [{"sig": "MEYCIQD5BlysJi4RvwrNOjy45Les5gLCBDMqXHurJXDwlA3hIwIhAJ9beaM3q3//97UjsxUSfFuy7oyUFpcjuxKKpYQxMH63", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.1", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "v0.6.11", "dependencies": {"options": "latest", "commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"ansi": "latest", "mocha": "0.8.x", "should": "0.4.2", "benchmark": "0.3.x", "tinycolor": "0.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.10": {"name": "ws", "version": "0.4.10", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "7b7b9d75eae61359fac376f361682ab57a61480b", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.10.tgz", "integrity": "sha512-Yoe8fMhf38HV3/quMojzrUL/A6/AdIU9BphaU0QYdaWb3SG6HP55lFK23qvsY9jN64pCdrHyn/HAfjLA0/OEfw==", "signatures": [{"sig": "MEUCIQC2zStRz0wnSdtXCMygujnWPZiLwlPG9K0CUPG+e+VcMAIgcpkYGGVJEsJP7ik6VaulfLEzxRu9b4wXKQXMFpBVC4k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.1", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "v0.6.11", "dependencies": {"options": "latest", "commander": "0.5.0"}, "_defaultsLoaded": true, "devDependencies": {"ansi": "latest", "mocha": "0.8.x", "should": "0.4.2", "benchmark": "0.3.x", "expect.js": "0.1.2", "tinycolor": "0.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.11": {"name": "ws", "version": "0.4.11", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "20025e169457fc07f4062117b9c59a1cb8e5d3e5", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.11.tgz", "integrity": "sha512-E4UqLBqhKjiQvdR4+nEO9cE6RdJU6S5EWpOuAZ9pkUCy2wZDC2nyp8BWk3lnZiv9ewSotaOo7rMlRAqEJoQlDA==", "signatures": [{"sig": "MEUCIDh/oy79YRxpd89apjtYWcmFdcHkUm0wYnnYU3ZAx/yAAiEAoECnS+5s3iSMLyUvjvfC9sYf9OVKKi28rJc5ZXNzb/I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.1", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "v0.6.11", "dependencies": {"options": "latest", "commander": "0.5.x"}, "_defaultsLoaded": true, "devDependencies": {"ansi": "latest", "mocha": "0.8.x", "should": "0.4.2", "benchmark": "0.3.x", "expect.js": "0.1.2", "tinycolor": "0.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.12": {"name": "ws", "version": "0.4.12", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.12", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "2aefe4e7e8a135cce0b04e31a4f57bdda7a3b4e3", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.12.tgz", "integrity": "sha512-gCZPjPLwgxpHPXoBZ2TdnEFDEr+d9p/kWzmprubzSr7p8X7QCZhITUlmfImwVZ7Q8mVYG2FGR5PopVGaBa/hqQ==", "signatures": [{"sig": "MEUCIGq/QsI7ozp/LI5dl6rnRp2vQPcqsZLcaBJ8BRuyRq1rAiEAkpeyyWU0u8bAZBkGXjIuKwQ0P2Rzh/qq8hz9rZMPFFw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.12", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {"options": "latest", "commander": "0.5.x"}, "_defaultsLoaded": true, "devDependencies": {"ansi": "latest", "mocha": "0.8.x", "should": "0.4.2", "benchmark": "0.3.x", "expect.js": "0.1.2", "tinycolor": "0.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.13": {"name": "ws", "version": "0.4.13", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.13", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "7edd1a8b16ac223bb6255dc054abe8071ab33036", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.13.tgz", "integrity": "sha512-iN9BKgzyCoo6so7590wN+/6vgolIo3l+zo2DEq4Ko6RYLluaJRKvDdrDMg4AR4//A13/LWEgjnDkuOnqIFOfGA==", "signatures": [{"sig": "MEUCIGW2UE8a4CTmwRc4DqBWtEiUj2vbWtrD4+lg3YlqcjHmAiEAt6oOZDC41QClbH8RrOHXhIHtuy3KP3OnFz953jnJktI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "preinstall": "make"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.12", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "v0.6.14", "dependencies": {"options": "latest", "commander": "0.5.x"}, "_defaultsLoaded": true, "devDependencies": {"ansi": "latest", "mocha": "0.8.x", "should": "0.4.2", "benchmark": "0.3.x", "expect.js": "0.1.2", "tinycolor": "0.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.14": {"name": "ws", "version": "0.4.14", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.14", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "b2e8ae12dd2abdf7b845ccf7c7b6d39f2ae86a0b", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.14.tgz", "integrity": "sha512-oJ3Bw7Xc2mrZ3z3td4dIM0nxQzNhBfqmeWO1hjb+qudtQ6ycJUpxUg67mp+8eYewk1ETw/lpyhD+fXu9WB7pqw==", "signatures": [{"sig": "MEQCIEue4t6Yiot/+Xs1kx0ZUATrXFTPq8nYSWPrp1nsiqM2AiBc2Zc1lW4qvx4megA4a+Mu6hHAiHghsRi9muR1/rIJSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "config": {"native": false}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "install": "node install.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.16", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "v0.6.15", "dependencies": {"options": "latest", "commander": "0.5.x"}, "_defaultsLoaded": true, "devDependencies": {"ansi": "latest", "mocha": "0.8.x", "should": "0.4.2", "benchmark": "0.3.x", "expect.js": "0.1.2", "tinycolor": "0.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.15": {"name": "ws", "version": "0.4.15", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.15", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "ff61ef4e86ae686a12d68e86b8be2d405e6f1a5a", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.15.tgz", "integrity": "sha512-YdK<PERSON>+A9bQ6/WF70BKOVCCae7N2lLYpdlVO59YTzguMjAtRVpj+6f1CN9ems9AIvqFaDAWR66TaF9P8t4lGUmaw==", "signatures": [{"sig": "MEQCIC+zZp+D38IAhuZovUEvZJnIjEkOOHK41MB/8faFLS9kAiAzKUWSlfH7WRiXNadk7JOs01LjSK2J7BFKBXaYMoitUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "config": {"native": false}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "install": "node install.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.21", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "v0.6.18", "dependencies": {"options": "latest", "commander": "0.5.x"}, "_defaultsLoaded": true, "devDependencies": {"ansi": "latest", "mocha": "0.8.x", "should": "0.4.2", "benchmark": "0.3.x", "expect.js": "0.1.2", "tinycolor": "0.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.16": {"name": "ws", "version": "0.4.16", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.16", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "e85feca7f265c00bbea2a9ff93a077e1dada1036", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.16.tgz", "integrity": "sha512-69Lk+AkrrSTz/SkYzaiaJK4HSES0f8YZIU4L/EESFBNCagm7QOJw4tvscf4weg4kGrYZ6v4QW06m5OiVYLyD+A==", "signatures": [{"sig": "MEUCIFZk5jYUCFk4W87eytn544myVHYc0n9rZGQRs3fgpLxbAiEA8ijepcgOoFng16gzGce3sKGLXNw2Lif2qzg14E0Os9c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "config": {"native": false}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "install": "node install.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.21", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "v0.6.18", "dependencies": {"options": "latest", "commander": "0.5.x"}, "_defaultsLoaded": true, "devDependencies": {"ansi": "latest", "mocha": "0.8.x", "should": "0.4.2", "benchmark": "0.3.x", "expect.js": "0.1.2", "tinycolor": "0.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.17": {"name": "ws", "version": "0.4.17", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.17", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "7846c12fb7dd7c5f1185cef1ae4e70d2bcf1aaa4", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.17.tgz", "integrity": "sha512-ase9X4Iiz7/mP/W3HeoYSz5TdoBB3LH2qqVRnS1IJj/GuS8MayiTxkAe5mCxqjogEttm860/KAuAqyiH7mCWrA==", "signatures": [{"sig": "MEYCIQC/IplYaeYodMcOgxhu9baCKimHp8IMcIixPz7Grjg+uwIhAOpunGOhf4JtwwzL7ZO7DOnAePJvZxrT0v5STzFwn78k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "config": {"native": false}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "install": "node install.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "v0.7.11-pre", "dependencies": {"options": "latest", "commander": "0.5.x"}, "_defaultsLoaded": true, "devDependencies": {"ansi": "latest", "mocha": "1.1.x", "should": "0.6.x", "benchmark": "0.3.x", "expect.js": "0.1.x", "tinycolor": "0.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.18": {"name": "ws", "version": "0.4.18", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.18", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "0561be7753e4863045939574f1c76d50a5070a7e", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.18.tgz", "integrity": "sha512-U9Tk1uE4RlUAawrevOiFy/sxDvTHxzK/82yfW0NfDd0Nu37NbVPaIkx3rZZLVWdGF6DypGkVxbhoY+KESwhMDQ==", "signatures": [{"sig": "MEQCIHjpIfo5840qFV6s6LltYWnnnS/xezQNQf75h6AeIVSgAiBgLtZaWQt2AtJX5z3FFrh9GcwdTfu/6PFAZAsH1sS8Yg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "config": {"verbose": false}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "install": "node install.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.25", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "v0.7.10", "dependencies": {"options": "latest", "commander": "0.5.x", "tinycolor": "0.x"}, "_defaultsLoaded": true, "devDependencies": {"ansi": "latest", "mocha": "1.1.x", "should": "0.6.x", "benchmark": "0.3.x", "expect.js": "0.1.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.19": {"name": "ws", "version": "0.4.19", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.19", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "3e2330568d07a46802226c09c5e26a7a31a80d7a", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.19.tgz", "integrity": "sha512-lotSOW8zeASjcBmF4EJR/A6NNGVjzUA+i7HWzPdTq+VsROorubLgFxefOYOFtoNFfRQ4RJs9F7XL7/eLkdLLSg==", "signatures": [{"sig": "MEUCIQDK+JYxjCf44gBkM0F/KVLtN9GdVgEgcCvIR+0QdM+trAIgJhqPKf73yJBf2JOAdxP1wD/XxXYg2ruKlents/CoId4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "config": {"verbose": false}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "install": "node install.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "v0.7.12-pre", "dependencies": {"options": "latest", "commander": "0.5.x", "tinycolor": "0.x"}, "_defaultsLoaded": true, "devDependencies": {"ansi": "latest", "mocha": "1.1.x", "should": "0.6.x", "benchmark": "0.3.x", "expect.js": "0.1.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.20": {"name": "ws", "version": "0.4.20", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.20", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "f44b63f46b9edfc457309c720bcc0f83f2fc5874", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.20.tgz", "integrity": "sha512-tViqnQPBIxGUvY9hdWc6peJtkITHicZnxwBQCAnMx1ADp2zTjKEE2l9usSHU98oGazq/UirOATTuLSektBFpxQ==", "signatures": [{"sig": "MEQCIGBQ5yGb+90UfYl+p1D1W5xX4pvmZjYC57UI3rTOoq2mAiAnHAiFGYd1FMq6HnfE66BNDdCsYyMF3h/ZkonmNlBM+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "config": {"verbose": false}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "install": "node install.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.24", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "v0.6.19", "dependencies": {"options": "latest", "commander": "~0.6.1", "tinycolor": "0.x"}, "_defaultsLoaded": true, "devDependencies": {"ansi": "latest", "mocha": "~1.2.1", "should": "0.6.x", "benchmark": "0.3.x", "expect.js": "0.1.x"}, "_engineSupported": true, "optionalDependencies": {}}, "0.4.21": {"name": "ws", "version": "0.4.21", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.21", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "15a50f53fe69b73fe3986f6cbd07270aecce62fc", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.21.tgz", "integrity": "sha512-Mtk6yl30fTUNN+hE/w3UDS1f/H8ArAZqoaGt4qCboBW+1CfZ2ckUxx9dTFuCrIb71a9J95u4IB7J2Y8TBYfKYw==", "signatures": [{"sig": "MEUCIQDPaXUNRzUvPyz+7dmpue9iPfCuxOyEB4W3QN9AdyBsUAIgRxHts2tyS57VnPUD7PjD9Suw+1tKgswAm6cy/tvZmms=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "config": {"verbose": false}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "install": "node install.js"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "dependencies": {"options": "latest", "commander": "~0.6.1", "tinycolor": "0.x"}, "devDependencies": {"ansi": "latest", "mocha": "~1.2.1", "should": "0.6.x", "benchmark": "0.3.x", "expect.js": "0.1.x"}}, "0.4.22": {"name": "ws", "version": "0.4.22", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.22", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "12b19ed222ce3a9f81858de6d8f41ed553ca56ae", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.22.tgz", "integrity": "sha512-tVoum0N1q2K+dmY7C2HX1rTUOBvTfkwQ65fb9bX9ojBwoIPboGys7RqCtZLYKIN4WOWqBodsqNwBcMJYyvnnWQ==", "signatures": [{"sig": "MEUCIQCrcg6IPs768QDTeCnAzdasL9p3cu4MfasUVRbxqU52TwIgE96QogWj/PhMsTiQn0E8wxZ7BAIYxjv7jZUDEsT1yO4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "config": {"verbose": false}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "install": "node install.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.62", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "dependencies": {"options": "latest", "commander": "~0.6.1", "tinycolor": "0.x"}, "devDependencies": {"ansi": "latest", "mocha": "~1.2.1", "should": "0.6.x", "benchmark": "0.3.x", "expect.js": "0.1.x"}}, "0.4.23": {"name": "ws", "version": "0.4.23", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.23", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "deba9d34b8a19e33091d1b6a79fc8709f5c5f2fc", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.23.tgz", "integrity": "sha512-ucOFWiJp6gQV1H847hrCedpuJ/zPyar41j0MOxWzpeBUaYUPm/cp1CtE2W1FMPyvYiXlQvdQbqGqsdhPsKugJw==", "signatures": [{"sig": "MEUCIQCrUUZVV4Nk0tYuEk4BMYxgGxh4rUx7bAMbtSnvp/+mqAIgXanGkHRU8hAi6v4X2LAS3ypt61pRbc3H8W2PoS7QslI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "config": {"verbose": false}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "install": "node install.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.62", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "dependencies": {"options": "latest", "commander": "~0.6.1", "tinycolor": "0.x"}, "devDependencies": {"ansi": "latest", "mocha": "~1.2.1", "should": "0.6.x", "benchmark": "0.3.x", "expect.js": "0.1.x"}}, "0.4.24": {"name": "ws", "version": "0.4.24", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.24", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "2d23335de727aad6d26b84f32817864fdfda5e38", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.24.tgz", "integrity": "sha512-hbnYjO2C/tz+UrT698emZyAxkDGWBFTH+kYEApVeNFEEeXMwIHmVM/+6lYteqzLHKOivL7vx46G1vs13oCvYVA==", "signatures": [{"sig": "MEUCIGmTv/pfXvVLUemnhZXSEnFz0IIjlmQctAZvN7sVjlsRAiEAkafQNQRS2zU1K6cvB6Y9YWdZ0vni0sYLC6mEvHYrVXM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "config": {"verbose": false}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "install": "node install.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.66", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "dependencies": {"options": "latest", "commander": "~0.6.1", "tinycolor": "0.x"}, "devDependencies": {"ansi": "latest", "mocha": "~1.2.1", "should": "0.6.x", "benchmark": "0.3.x", "expect.js": "0.1.x"}}, "0.4.25": {"name": "ws", "version": "0.4.25", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.25", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "3dca06feddc25944af780d7b01da2cf63da7acc8", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.25.tgz", "integrity": "sha512-ASP1k38vOs5Wfqm7s3344g1l87J74byfyrFQIf0nsUcofnMJ2sQCD4DokP/n7oHVBh5xQ71rRJi18PQhi0fYPQ==", "signatures": [{"sig": "MEQCIHrCyDW5+IiqQVqOxx7UotdiuIlFTE5oVzSlFar09XcoAiBarqY+t1Uf9Jj80/KrikkfDza0Mc4QnalmWTjAjPaS5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "browser": {"./index.js": "./lib/browser.js"}, "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "install": "(node-gyp rebuild 2> builderror.log) || (exit 0)"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.1.66", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "dependencies": {"options": "latest", "commander": "~0.6.1", "tinycolor": "0.x"}, "devDependencies": {"ansi": "latest", "mocha": "~1.2.1", "should": "0.6.x", "benchmark": "0.3.x", "expect.js": "0.1.x"}}, "0.4.27": {"name": "ws", "version": "0.4.27", "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.27", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/einaros/ws/issues"}, "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "077d3a48b6e0b5a96f68f3b38a94ea1ec72c2555", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.27.tgz", "integrity": "sha512-bvdv8O9FHpMbFmFk8fHTA12OCXSLJoP1DBBz8rY2EZs0I8NoSAquOgNVQJMLqmJGZ6DdFUi1Cx7t/NuRueLl3g==", "signatures": [{"sig": "MEYCIQD3uNgiWcijdL0vsSOz34e7t33fmp8tc5OerhnH56MypgIhANb7Wp7zGGtc7urEcnHcV1Nnin8DmFyqUs1najquKeen", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "browser": "./lib/browser.js", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "install": "(node-gyp rebuild 2> builderror.log) || (exit 0)"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.2.32", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "dependencies": {"options": ">=0.0.5", "commander": "~0.6.1", "tinycolor": "0.x"}, "devDependencies": {"ansi": "latest", "mocha": "~1.2.1", "should": "0.6.x", "benchmark": "0.3.x", "expect.js": "0.1.x"}}, "0.4.28": {"name": "ws", "version": "0.4.28", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.28", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "V1", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/einaros/ws/issues"}, "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "03bcea020195847d1184c6c08f45baaf12322eee", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.28.tgz", "integrity": "sha512-XAY7ULnrJD0i2rX++fFO6E6m95GQ3HKFPjfKSzrJnm5EVVLOXcARF9UNaGp6ClnVAqddBi7xAR8OsPF4NeW/jg==", "signatures": [{"sig": "MEQCIDmr7mo4Lw3my/QSqDRHQG1wHKRLArNg3tDhCKRNLkr/AiAM0Vyj099IHCF3Ikr5JwzB/cNOrNXKPF0Ru0UoRa2vTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "browser": "./lib/browser.js", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "make test", "install": "(node-gyp rebuild 2> builderror.log) || (exit 0)"}, "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.3.5", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "dependencies": {"options": ">=0.0.5", "commander": "~0.6.1", "tinycolor": "0.x"}, "devDependencies": {"ansi": "latest", "mocha": "1.12.0", "should": "1.2.x", "benchmark": "0.3.x", "expect.js": "0.2.x"}}, "0.4.29": {"name": "ws", "version": "0.4.29", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.29", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "V1", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/einaros/ws/issues"}, "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "4b79ef62b4f3f782a05ba56b41b122d1252d4f90", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.29.tgz", "integrity": "sha512-XO5IdqNoPWbHSpKWC7yN1ApT+8Pg5dVp7uR5SIx1UXmbPXy4w5EsPvl8WRdsXehqu9D2L6eqwlpdsMpmhIulEg==", "signatures": [{"sig": "MEQCIAdZstgCAWJvNC9z8p07x3bEmtjkek8ezea5ZtvqBklhAiB4MkBx+pbYZ28Mn959MbSmyZf+/LEu0XchvYF1WOz8+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "browser": "./lib/browser.js", "engines": {"node": ">=0.4.0"}, "gypfile": true, "scripts": {"test": "make test", "install": "(node-gyp rebuild 2> builderror.log) || (exit 0)"}, "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.3.4", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "dependencies": {"nan": "~0.3.0", "options": ">=0.0.5", "commander": "~0.6.1", "tinycolor": "0.x"}, "devDependencies": {"ansi": "latest", "mocha": "1.12.0", "should": "1.2.x", "benchmark": "0.3.x", "expect.js": "0.2.x"}}, "0.4.30": {"name": "ws", "version": "0.4.30", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.30", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "V1", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/einaros/ws/issues"}, "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "5e2c18b7bb7ee0f9c9fcc3d3ec50f513ba5f99e8", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.30.tgz", "integrity": "sha512-gtF76uDlbeBsM4YdX40cVuDxe1B6Pals0eiBibkbVu5qbAaAxRzqdw3t0KkKtIKABrMXp9LHFXH3x7BiMHotTA==", "signatures": [{"sig": "MEYCIQCKEv1+FjFQE33Eupwle9rAjixtrluztXmqmhF3j9HrzgIhANVA9yH2yNM3IOOWUbBrDS24izRLW/8Yi/SIjAFA3NvU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "browser": "./lib/browser.js", "engines": {"node": ">=0.4.0"}, "gypfile": true, "scripts": {"test": "make test", "install": "(node-gyp rebuild 2> builderror.log) || (exit 0)"}, "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.3.5", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "dependencies": {"nan": "~0.3.0", "options": ">=0.0.5", "commander": "~0.6.1", "tinycolor": "0.x"}, "devDependencies": {"ansi": "latest", "mocha": "1.12.0", "should": "1.2.x", "benchmark": "0.3.x", "expect.js": "0.2.x"}}, "0.4.31": {"name": "ws", "version": "0.4.31", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.31", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "V1", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/einaros/ws/issues"}, "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "5a4849e7a9ccd1ed5a81aeb4847c9fedf3122927", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.31.tgz", "integrity": "sha512-mWiVQ9qZGPXvLxQ4xGy58Ix5Bw0L99SB+hDT8L59bty4fbnQczaGl4YEWR7AzLQGbvPn/30r9/o41dPiSuUmYw==", "signatures": [{"sig": "MEUCIBC15NWVG0DYh4p/5Xdq8o5KZNxCrs7ONnnVjpz9A7hWAiEAp3MghVIx+nbETmMLtZyLWJL1/AnlvJf752YnQd359G0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "browser": "./lib/browser.js", "engines": {"node": ">=0.4.0"}, "gypfile": true, "scripts": {"test": "make test", "install": "(node-gyp rebuild 2> builderror.log) || (exit 0)"}, "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "component": {"scripts": {"ws/index.js": "./lib/browser.js"}}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.3.5", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "dependencies": {"nan": "~0.3.0", "options": ">=0.0.5", "commander": "~0.6.1", "tinycolor": "0.x"}, "devDependencies": {"ansi": "latest", "mocha": "1.12.0", "should": "1.2.x", "benchmark": "0.3.x", "expect.js": "0.2.x"}}, "0.4.32": {"name": "ws", "version": "0.4.32", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "ws@0.4.32", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "V1", "email": "<EMAIL>"}], "homepage": "https://github.com/einaros/ws", "bugs": {"url": "https://github.com/einaros/ws/issues"}, "bin": {"wscat": "./bin/wscat"}, "dist": {"shasum": "787a6154414f3c99ed83c5772153b20feb0cec32", "tarball": "https://registry.npmjs.org/ws/-/ws-0.4.32.tgz", "integrity": "sha512-htqsS0U9Z9lb3ITjidQkRvkLdVhQePrMeu475yEfOWkAYvJ6dSjQp1tOH6ugaddzX5b7sQjMPNtY71eTzrV/kA==", "signatures": [{"sig": "MEQCIGCNl3SesdWkq+nwarSSi5ZD9xuhXBd/dvEBI5ORQSi9AiBNm4E+BTkZzBX7c+QjcyVxGHAdZKJ2XDoWVk7d52HP1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "787a6154414f3c99ed83c5772153b20feb0cec32", "browser": "./lib/browser.js", "engines": {"node": ">=0.4.0"}, "gypfile": true, "scripts": {"test": "make test", "install": "(node-gyp rebuild 2> builderror.log) || (exit 0)"}, "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "component": {"scripts": {"ws/index.js": "./lib/browser.js"}}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.4.10", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "dependencies": {"nan": "~1.0.0", "options": ">=0.0.5", "commander": "~2.1.0", "tinycolor": "0.x"}, "devDependencies": {"ansi": "latest", "mocha": "1.12.0", "should": "1.2.x", "benchmark": "0.3.x", "expect.js": "0.2.x"}}, "0.5.0": {"name": "ws", "version": "0.5.0", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@0.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "V1", "email": "<EMAIL>"}], "homepage": "https://github.com/einaros/ws", "bugs": {"url": "https://github.com/einaros/ws/issues"}, "dist": {"shasum": "b3980391dc4777d83974718aa361e808d86cf9ca", "tarball": "https://registry.npmjs.org/ws/-/ws-0.5.0.tgz", "integrity": "sha512-ewMTtzuS3WRFwMw6k+yvOHzl3JRjN5yZzwAkq3TYOViPYMrVjHzmyj6ni+ihNcgoFhxI2f33RvshqqOU3qdxgw==", "signatures": [{"sig": "MEUCIEipQ4du+sdAzjnNtuSbqaZW7Ks8unEHUhtLOZoj4kjIAiEA+434NXbIKBuFScl4/IHRrJmI7D2iVyEEwv9J84GA8dA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "b3980391dc4777d83974718aa361e808d86cf9ca", "browser": "./lib/browser.js", "gitHead": "d242d2b8ddaa32f7f8a9c61abe74615767a91db4", "gypfile": true, "scripts": {"test": "make test", "install": "(node-gyp rebuild 2> builderror.log) || (exit 0)"}, "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "component": {"scripts": {"ws/index.js": "./lib/browser.js"}}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "dependencies": {"nan": "1.4.x", "ultron": "1.0.x", "options": ">=0.0.5"}, "devDependencies": {"ansi": "0.3.x", "mocha": "2.0.x", "should": "4.3.x", "benchmark": "0.3.x", "expect.js": "0.3.x", "tinycolor": "0.0.x"}}, "0.6.0": {"name": "ws", "version": "0.6.0", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@0.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "V1", "email": "<EMAIL>"}], "homepage": "https://github.com/einaros/ws", "bugs": {"url": "https://github.com/einaros/ws/issues"}, "dist": {"shasum": "4559337acb3619392aecf775f9ac749bb59c752d", "tarball": "https://registry.npmjs.org/ws/-/ws-0.6.0.tgz", "integrity": "sha512-A3GlNcefwupPAqFdiY/wujUGOEPnG1oTSOGGOMuJNfT0pkoycpGWSfRLr34855qQFYwD5OBBW4xVXVWTSJwX9A==", "signatures": [{"sig": "MEUCIQCPTTbxS8Wzsp/XwdCESn9RczdtrKNiJG/X+zz0E/IEoQIgVHZlmM9CK9La5TtAAZ/Fi1fcjI82IpqJE+2fP1mT+20=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "4559337acb3619392aecf775f9ac749bb59c752d", "browser": "./lib/browser.js", "gitHead": "b41ed62b95b2e1cf22ca779e7841c5d5dd8a6816", "gypfile": true, "scripts": {"test": "make test", "install": "(node-gyp rebuild 2> builderror.log) || (exit 0)"}, "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "component": {"scripts": {"ws/index.js": "./lib/browser.js"}}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "dependencies": {"nan": "1.4.x", "ultron": "1.0.x", "options": ">=0.0.5"}, "devDependencies": {"ansi": "0.3.x", "mocha": "2.0.x", "should": "4.3.x", "benchmark": "0.3.x", "expect.js": "0.3.x", "tinycolor": "0.0.x"}}, "0.6.1": {"name": "ws", "version": "0.6.1", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@0.6.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "V1", "email": "<EMAIL>"}], "homepage": "https://github.com/einaros/ws", "bugs": {"url": "https://github.com/einaros/ws/issues"}, "dist": {"shasum": "e239e8269f187f022d9da5f2262beb2ea36a9209", "tarball": "https://registry.npmjs.org/ws/-/ws-0.6.1.tgz", "integrity": "sha512-fLrHlNfPHJsKEG0fBy3V/L0JVTVJj3h/8186pRjZtRbAMFX5gEsMB6YAn+68Ah6H8rHSRYkNA48wFEuDcgl4dQ==", "signatures": [{"sig": "MEUCIQD/UYzbB+kez3zQfrCLy/c5fbMUB9YRfNUjiCUgQNvNvQIgDTTLu/9MQLFx9Dpt+RXQ906yGlcdBr/zl/BNK9xFky4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "e239e8269f187f022d9da5f2262beb2ea36a9209", "browser": "./lib/browser.js", "gitHead": "695b0d1fea264a7b364344133044ca519ea0c57c", "gypfile": true, "scripts": {"test": "make test", "install": "(node-gyp rebuild 2> builderror.log) || (exit 0)"}, "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "component": {"scripts": {"ws/index.js": "./lib/browser.js"}}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "dependencies": {"nan": "1.4.x", "ultron": "1.0.x", "options": ">=0.0.5"}, "devDependencies": {"ansi": "0.3.x", "mocha": "2.0.x", "should": "4.3.x", "benchmark": "0.3.x", "expect.js": "0.3.x", "tinycolor": "0.0.x"}}, "0.6.2": {"name": "ws", "version": "0.6.2", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@0.6.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "V1", "email": "<EMAIL>"}], "homepage": "https://github.com/einaros/ws", "bugs": {"url": "https://github.com/einaros/ws/issues"}, "dist": {"shasum": "7b2c943d01b9f02491481bc39532d63893634370", "tarball": "https://registry.npmjs.org/ws/-/ws-0.6.2.tgz", "integrity": "sha512-yPlgyLDWef6/hlZlVHQVQ9roP5oSTpuAct+iy6dR0OJSqVSMdUzCzVr6gw4A85BP0gri9frFsY9Bgc39N0vzQA==", "signatures": [{"sig": "MEYCIQC+O5YSbqC0pUukQ5QITW8Kecg3+C0YC4Q+U+Y2hH82+gIhAJ9Ebyds0CglG7oxwWAXS+P1CJ+r9vGnH0vNwMNA0yoB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "7b2c943d01b9f02491481bc39532d63893634370", "browser": "./lib/browser.js", "gitHead": "4711f98f5e243409c015629cd30a6ead0dbb3d8e", "gypfile": true, "scripts": {"test": "make test", "install": "(node-gyp rebuild 2> builderror.log) || (exit 0)"}, "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "component": {"scripts": {"ws/index.js": "./lib/browser.js"}}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "dependencies": {"nan": "1.4.x", "ultron": "1.0.x", "options": ">=0.0.5"}, "devDependencies": {"ansi": "0.3.x", "mocha": "2.0.x", "should": "4.3.x", "benchmark": "0.3.x", "expect.js": "0.3.x", "tinycolor": "0.0.x"}}, "0.6.3": {"name": "ws", "version": "0.6.3", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@0.6.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "V1", "email": "<EMAIL>"}], "homepage": "https://github.com/einaros/ws", "bugs": {"url": "https://github.com/einaros/ws/issues"}, "dist": {"shasum": "777949e228ad868adb73709899c81079315d904c", "tarball": "https://registry.npmjs.org/ws/-/ws-0.6.3.tgz", "integrity": "sha512-PobW3tsQVXVT75R/nxtVbT1KCd/QdOGESTsotVdbf57mM20koAJ0r6SIrGCPbkGvXZp0TvfTKxd1Xso/stgSrw==", "signatures": [{"sig": "MEQCIBdZ4KT5MSdfNSJlQ5I+HhY56h9zdWvXWXIoDw2/UJWBAiBPD6ohB2QO0TefvWBNYLU5N908pBcU2lmC7VXq/j65Ig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "777949e228ad868adb73709899c81079315d904c", "browser": "./lib/browser.js", "gitHead": "4605ac39a977863acc22396c36aa216daabf7730", "gypfile": true, "scripts": {"test": "make test", "install": "(node-gyp rebuild 2> builderror.log) || (exit 0)"}, "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "component": {"scripts": {"ws/index.js": "./lib/browser.js"}}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "dependencies": {"nan": "1.4.x", "ultron": "1.0.x", "options": ">=0.0.5"}, "devDependencies": {"ansi": "0.3.x", "mocha": "2.0.x", "should": "4.3.x", "benchmark": "0.3.x", "expect.js": "0.3.x", "tinycolor": "0.0.x"}}, "0.6.4": {"name": "ws", "version": "0.6.4", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@0.6.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "V1", "email": "<EMAIL>"}], "homepage": "https://github.com/einaros/ws", "bugs": {"url": "https://github.com/einaros/ws/issues"}, "dist": {"shasum": "****************************************", "tarball": "https://registry.npmjs.org/ws/-/ws-0.6.4.tgz", "integrity": "sha512-6xzkWM7++RnTUYGDeBQjrlLIUAYEzfDg9tBjX7xWCQRLAolFOP2BWPZKph8BkL/6ZIgfF1lop4XgsovVbtCpcg==", "signatures": [{"sig": "MEUCICw2om7eH1PDt1bK0yTYDU/4mvPy/R8pv+S8a9u6W730AiEA+Bo4AryF3EkUF7oRyNLBBy3C8SpxUa+OF9uUOMo9ifE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "****************************************", "browser": "./lib/browser.js", "gitHead": "c7f1b4eb44ce45f152f1923b4ba18446f83d0dff", "gypfile": true, "scripts": {"test": "make test", "install": "(node-gyp rebuild 2> builderror.log) || (exit 0)"}, "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "component": {"scripts": {"ws/index.js": "./lib/browser.js"}}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "dependencies": {"nan": "1.4.x", "ultron": "1.0.x", "options": ">=0.0.5"}, "devDependencies": {"ansi": "0.3.x", "mocha": "2.0.x", "should": "4.3.x", "benchmark": "0.3.x", "expect.js": "0.3.x", "tinycolor": "0.0.x"}}, "0.6.5": {"name": "ws", "version": "0.6.5", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@0.6.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "V1", "email": "<EMAIL>"}], "homepage": "https://github.com/einaros/ws", "bugs": {"url": "https://github.com/einaros/ws/issues"}, "dist": {"shasum": "f00844001ca393b003681ff32838e72a560dafd4", "tarball": "https://registry.npmjs.org/ws/-/ws-0.6.5.tgz", "integrity": "sha512-USH6S6I3kVLbZQ3+MwoC1gSGeQ9NTT5SCqhbtqLLJjPa+nv0I7zzzPyQSVAm10NzyMHa2rD4ZGnIbnFgarSQWg==", "signatures": [{"sig": "MEYCIQD+5c3GzwSiTVr5+Vl813sfcuwPC896eOw7YElUkSALvwIhALwa1lB/TEtP3Vz7xlHBeCIspcyWx8jrnIUfowHXUgoP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "f00844001ca393b003681ff32838e72a560dafd4", "browser": "./lib/browser.js", "gitHead": "552dddaf9bcc5304c22415b81aa748384d82837c", "gypfile": true, "scripts": {"test": "make test", "install": "(node-gyp rebuild 2> builderror.log) || (exit 0)"}, "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "component": {"scripts": {"ws/index.js": "./lib/browser.js"}}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "dependencies": {"nan": "1.4.x", "ultron": "1.0.x", "options": ">=0.0.5"}, "devDependencies": {"ansi": "0.3.x", "mocha": "2.0.x", "should": "4.3.x", "benchmark": "0.3.x", "expect.js": "0.3.x", "tinycolor": "0.0.x"}}, "0.7.0": {"name": "ws", "version": "0.7.0", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@0.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "V1", "email": "<EMAIL>"}], "homepage": "https://github.com/einaros/ws", "bugs": {"url": "https://github.com/einaros/ws/issues"}, "dist": {"shasum": "d215f9f8350a40d78c72810c59aa99d67a8504e6", "tarball": "https://registry.npmjs.org/ws/-/ws-0.7.0.tgz", "integrity": "sha512-HZE6Mqk28YlY+BpkiBXCBlSl65gwWrsviKMMAor11b+B0OYWzhTp2MT+ARF4jGhHhoBuWRrIlxslTOrmFeo3Qg==", "signatures": [{"sig": "MEUCIQC9Y5uPKgnBsJ2Pb2vaA80RMYGrblHaBB/JmUbwkTVK4gIgcdm4CrntuuxDK8nIejXBBlj9daJ48+PaS62KlIYw1Z8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "d215f9f8350a40d78c72810c59aa99d67a8504e6", "browser": "./lib/browser.js", "gitHead": "419b11f042a6fa07986f296ac40eae57eb9d6d8c", "gypfile": true, "scripts": {"test": "make test", "install": "(node-gyp rebuild 2> builderror.log) || (exit 0)"}, "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "component": {"scripts": {"ws/index.js": "./lib/browser.js"}}, "repository": {"url": "git://github.com/einaros/ws.git", "type": "git"}, "_npmVersion": "2.2.0", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "1.0.3", "dependencies": {"nan": "1.5.x", "ultron": "1.0.x", "options": ">=0.0.5"}, "devDependencies": {"ansi": "0.3.x", "mocha": "2.0.x", "should": "4.3.x", "benchmark": "0.3.x", "expect.js": "0.3.x", "tinycolor": "0.0.x"}}, "0.7.1": {"name": "ws", "version": "0.7.1", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@0.7.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "8f1c7864ca08081be3cd0ac330df0d29c5fcd0da", "tarball": "https://registry.npmjs.org/ws/-/ws-0.7.1.tgz", "integrity": "sha512-nOSACUKRR3JwtVYHD0B9yq5SZ4i/YFmMzyU/tjbpZOfWEaajdp2RIGLJzgILIRRKCOzw08qgw7hWQS2UOz06yA==", "signatures": [{"sig": "MEUCIQDAWv01m9pZ0cPwQsj6HJDd+0gRNn5SqS5GXYuWX4U5vQIgGxkTwqHkv1a7cn6TJ3pUARC6xo5E6w1Rhl4kY2ILbn0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "8f1c7864ca08081be3cd0ac330df0d29c5fcd0da", "browser": "./lib/browser.js", "gitHead": "608df82a333de45905f05ca60f18a625b4c3293b", "gypfile": true, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"ws/index.js": "./lib/browser.js"}}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "0.10.35", "dependencies": {"ultron": "1.0.x", "options": ">=0.0.5", "bufferutil": "1.0.x", "utf-8-validate": "1.0.x"}, "devDependencies": {"ansi": "0.3.x", "mocha": "2.0.x", "should": "4.3.x", "benchmark": "0.3.x", "expect.js": "0.3.x", "tinycolor": "0.0.x"}, "optionalDependencies": {"bufferutil": "1.0.x", "utf-8-validate": "1.0.x"}}, "0.7.2": {"name": "ws", "version": "0.7.2", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@0.7.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "438c560bdfa2b7da3dd5b6b46ed61325c24699d8", "tarball": "https://registry.npmjs.org/ws/-/ws-0.7.2.tgz", "integrity": "sha512-8mJ1Ku743qD/PKmO9Dg+y7BXTwzUgKdXguecfIyOVHFmez4JMqMF+V+M684btmQXHlwzyrJqRl3NYDltGDf6CQ==", "signatures": [{"sig": "MEUCIQCF3hBfeugfbWiywpq3MX+5Q1DIDgVtnCnpM05dqiPNJwIgd7Dvj1pa+hoIYV2tKvv3jBpmAUvdyHl396ptW53iFoQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "438c560bdfa2b7da3dd5b6b46ed61325c24699d8", "browser": "./lib/browser.js", "gitHead": "62c154c7ba97eaf6ac346f855af956c8b96f0ead", "gypfile": true, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"ws/index.js": "./lib/browser.js"}}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "2.7.5", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "0.10.35", "dependencies": {"ultron": "1.0.x", "options": ">=0.0.5", "bufferutil": "1.1.x", "utf-8-validate": "1.1.x"}, "devDependencies": {"ansi": "0.3.x", "mocha": "2.2.x", "should": "4.3.x", "benchmark": "0.3.x", "expect.js": "0.3.x", "tinycolor": "0.0.x"}, "optionalDependencies": {"bufferutil": "1.1.x", "utf-8-validate": "1.1.x"}}, "0.8.0": {"name": "ws", "version": "0.8.0", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@0.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws#readme", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "ac60ebad312121d01e16cc3383d7ec67ad0f0f1f", "tarball": "https://registry.npmjs.org/ws/-/ws-0.8.0.tgz", "integrity": "sha512-JRxYZsisQGuKfRb+QohqX+CZhH3CE++Zz/BZpouhGbf/MLvL9qQ1i7Zp/uLGMxs67z6IX/3/aymoAVuoJzRieQ==", "signatures": [{"sig": "MEUCIQDBL2RqcPbGJBcG/moZ+kAQ8dKM7fqnKvmMl+KMhSABMAIgREC7L6a7z2t6ozTSUgwS4hYi8ofGOoWRrSHbxHwk5HU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "ac60ebad312121d01e16cc3383d7ec67ad0f0f1f", "browser": "./lib/browser.js", "gitHead": "21383fdcacdf47eb96775e51748e51b258e07dd4", "gypfile": true, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"ws/index.js": "./lib/browser.js"}}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "2.9.1", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "0.12.3", "dependencies": {"ultron": "1.0.x", "options": ">=0.0.5", "bufferutil": "1.2.x", "utf-8-validate": "1.2.x"}, "devDependencies": {"ansi": "0.3.x", "mocha": "2.2.x", "should": "4.3.x", "benchmark": "0.3.x", "expect.js": "0.3.x", "tinycolor": "0.0.x"}, "optionalDependencies": {"bufferutil": "1.2.x", "utf-8-validate": "1.2.x"}}, "0.8.1": {"name": "ws", "version": "0.8.1", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@0.8.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws#readme", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "6b65273b99193c5f067a4cf5809598f777e3b759", "tarball": "https://registry.npmjs.org/ws/-/ws-0.8.1.tgz", "integrity": "sha512-nDxDQDUMPnPqHYO4sWI9ln+ssUz1gxXCAFpczkv3LPQMmbC5vpfX1VZid6hqAaqNfI9bEvctLFNdZJBQPtdYCQ==", "signatures": [{"sig": "MEQCICMjOXIZs8Tcyt2GMnzMeQ0hcQ60vNSA7uBMPe+CLb29AiBNLscPiyBJPssRtZVA6KWu1h65KCDIXI+ZfSaTVBwWdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "6b65273b99193c5f067a4cf5809598f777e3b759", "browser": "./lib/browser.js", "gitHead": "74f567e0221a14071bb40eb1902e946524a11862", "gypfile": true, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "component": {"scripts": {"ws/index.js": "./lib/browser.js"}}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"ultron": "1.0.x", "options": ">=0.0.5", "bufferutil": "1.2.x", "utf-8-validate": "1.2.x"}, "devDependencies": {"ansi": "0.3.x", "mocha": "2.2.x", "should": "4.3.x", "benchmark": "0.3.x", "expect.js": "0.3.x", "tinycolor": "0.0.x"}, "optionalDependencies": {"bufferutil": "1.2.x", "utf-8-validate": "1.2.x"}}, "1.0.0": {"name": "ws", "version": "1.0.0", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws#readme", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "2bd1290456daf0a9e88c56f616b0bdc090668b48", "tarball": "https://registry.npmjs.org/ws/-/ws-1.0.0.tgz", "integrity": "sha512-mOFHfXdAsTUCK9AyzJYmzABqjsZugJ0nfRAg9p7q4eX1x2Kfm7e8zs4nq0jMYS4mD11SxUQq1F072kx+GosOnQ==", "signatures": [{"sig": "MEUCIAx3Xz+jt0j6BS/dHuAsbesCUkkEx6LqzR7kU9FyyqyvAiEA48+QP6oA9YBVCBjuwXp/euv0n5ETMZD7TXwlnls+c5g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "2bd1290456daf0a9e88c56f616b0bdc090668b48", "gitHead": "753937ff1ddc0938513267b4d6d5139a6ad41746", "gypfile": true, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "3.5.1", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "4.2.3", "dependencies": {"ultron": "1.0.x", "options": ">=0.0.5"}, "devDependencies": {"ansi": "0.3.x", "mocha": "2.3.x", "should": "8.0.x", "benchmark": "0.3.x", "expect.js": "0.3.x", "tinycolor": "0.0.x", "bufferutil": "1.2.x", "utf-8-validate": "1.2.x"}}, "1.0.1": {"name": "ws", "version": "1.0.1", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws#readme", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "7d0b2a2e58cddd819039c29c9de65045e1b310e9", "tarball": "https://registry.npmjs.org/ws/-/ws-1.0.1.tgz", "integrity": "sha512-JcrURpj0uO3Kn30PZitCOmD2InWHIt6skFKKtXHAZ/2IK9hVzFXW89b3JDenT/diXyV8qdRGmNR0OA210QMnYQ==", "signatures": [{"sig": "MEQCIQDDWhaudSHrCl+E+xSepLOVOomODR6Uog8/FkKV64HnrgIfWXJo3kSm17CELQo5c8QnIm9dk1HHSy9hRg8qr5JrsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "7d0b2a2e58cddd819039c29c9de65045e1b310e9", "gitHead": "40a9d686288b5d0be13f2bf2f3f5da07afc8cda2", "gypfile": true, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "3.5.1", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "4.2.3", "dependencies": {"ultron": "1.0.x", "options": ">=0.0.5"}, "devDependencies": {"ansi": "0.3.x", "mocha": "2.3.x", "should": "8.0.x", "benchmark": "0.3.x", "expect.js": "0.3.x", "tinycolor": "0.0.x", "bufferutil": "1.2.x", "utf-8-validate": "1.2.x"}}, "1.1.0": {"name": "ws", "version": "1.1.0", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws#readme", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "c1d6fd1515d3ceff1f0ae2759bf5fd77030aad1d", "tarball": "https://registry.npmjs.org/ws/-/ws-1.1.0.tgz", "integrity": "sha512-ajJ6rNx3OY60ETwAtHIN0ezBEatMmmWx+KBUab9gfL1iBI1Ku4W0+oL1u5UanjUjMcvwCq5TfUM+H4hCWz21Gg==", "signatures": [{"sig": "MEUCIQD29pKSIRY/y7sBRA6gpo/MDRyUOLETx9uTaX2PYvulxgIgYcIpNL4Gi57YtZj0/MbPPo9xxFE1Ql5otie984CEjPA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "c1d6fd1515d3ceff1f0ae2759bf5fd77030aad1d", "gitHead": "4263f26d4dbe27e781c41a1ddfe3dab87dd9e1dc", "gypfile": true, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "3.8.0", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "4.3.1", "dependencies": {"ultron": "1.0.x", "options": ">=0.0.5"}, "devDependencies": {"ansi": "0.3.x", "mocha": "2.3.x", "should": "8.0.x", "istanbul": "^0.4.1", "benchmark": "0.3.x", "expect.js": "0.3.x", "tinycolor": "0.0.x", "bufferutil": "1.2.x", "utf-8-validate": "1.2.x"}, "_npmOperationalInternal": {"tmp": "tmp/ws-1.1.0.tgz_1460376022305_0.992860296042636", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.1": {"name": "ws", "version": "1.1.1", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@1.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws#readme", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "082ddb6c641e85d4bb451f03d52f06eabdb1f018", "tarball": "https://registry.npmjs.org/ws/-/ws-1.1.1.tgz", "integrity": "sha512-TRtCup+Fxoy1sW9funE4kPxA0KfaMc7g68DoKN+Uu9Ej+zr9We3DWVJ2XgiGtXlibqA7IWOV+Xe6xlUxPputfg==", "signatures": [{"sig": "MEQCIEKhq7TMyPBFNdky+9DzBzOd6LywyKFvFLvrUyOosZCLAiBsU1uIMDVvYk+pfezehM+MmyqkQQ1ZJN+6H5/VeiYifg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "082ddb6c641e85d4bb451f03d52f06eabdb1f018", "gitHead": "c7bb7306cb0e1d17df141f61a220056eaa5e3502", "gypfile": true, "scripts": {"test": "make test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "3.8.0", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "4.3.1", "dependencies": {"ultron": "1.0.x", "options": ">=0.0.5"}, "devDependencies": {"ansi": "0.3.x", "mocha": "2.3.x", "should": "8.0.x", "istanbul": "^0.4.1", "benchmark": "0.3.x", "expect.js": "0.3.x", "tinycolor": "0.0.x", "bufferutil": "1.2.x", "utf-8-validate": "1.2.x"}, "_npmOperationalInternal": {"tmp": "tmp/ws-1.1.1.tgz_1466770957650_0.7956828831229359", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.0-beta.0": {"name": "ws", "version": "2.0.0-beta.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@2.0.0-beta.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws#readme", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "47eae6d1a9a2a04cdb0c7b9d033cbbf7861009ef", "tarball": "https://registry.npmjs.org/ws/-/ws-2.0.0-beta.0.tgz", "integrity": "sha512-6UNP0UFsYWpFc4YwqiAUgthVQQJDa68+QEB9YZ4R/3fC/GnEdDbOBsacAEEyCrnMrb+cwIONCYuhQX/Pzhk5yQ==", "signatures": [{"sig": "MEUCIQD5qLf5DF0QcjcbtZGU2KsrwIOIkhdQ66jfo8/PWJLsiQIgfqsKuSJURD7XkYufLbwuRT9Vtd7k6EbIUnooi6ZwObI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "47eae6d1a9a2a04cdb0c7b9d033cbbf7861009ef", "gitHead": "19ce183fad0e826a025bf9709eef48e279a1cb75", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha test/*.test.js", "coverage": "istanbul cover _mocha --report html -- test/*.test.js", "integration": "npm run lint && mocha test/*.integration.js", "test-travis": "npm run lint && istanbul cover _mocha --report lcovonly -- test/*.test.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "7.4.0", "dependencies": {"ultron": "~1.1.0"}, "devDependencies": {"mocha": "~3.2.0", "eslint": "~3.13.0", "istanbul": "~0.4.5", "benchmark": "~2.1.2", "bufferutil": "~1.3.0", "utf-8-validate": "~2.0.0", "eslint-plugin-promise": "~3.4.0", "eslint-config-standard": "~6.2.1", "eslint-plugin-standard": "~2.0.1", "eslint-config-semistandard": "~7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws-2.0.0-beta.0.tgz_1484058513328_0.05468851304613054", "host": "packages-18-east.internal.npmjs.com"}}, "2.0.0-beta.1": {"name": "ws", "version": "2.0.0-beta.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@2.0.0-beta.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws#readme", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "148696794af6e8766699d55228166fd0dfbe3cc2", "tarball": "https://registry.npmjs.org/ws/-/ws-2.0.0-beta.1.tgz", "integrity": "sha512-Vq1cdZNpXzfiWYEaP4OKygAFiUwxFCVItODNDhVvoFpwUS8//ljAnCNWaqbvVlbizXcnRjfZ5j3yW5rgtKhL8w==", "signatures": [{"sig": "MEUCIQCa/pC63dRQEso4BlpQlQ8v1kviTTQ7FQtZzDPt+116mAIgeTicaYmav0aM38uetO7bQ8LWYqncWP+uV/ZqMAY4Kg0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "148696794af6e8766699d55228166fd0dfbe3cc2", "gitHead": "e62b9ba0c89aaf7b3a2d17084c23f4f983acf339", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha test/*.test.js", "coverage": "istanbul cover _mocha --report html -- test/*.test.js", "integration": "npm run lint && mocha test/*.integration.js", "test-travis": "npm run lint && istanbul cover _mocha --report lcovonly -- test/*.test.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "7.4.0", "dependencies": {"ultron": "~1.1.0"}, "devDependencies": {"mocha": "~3.2.0", "eslint": "~3.13.0", "istanbul": "~0.4.5", "benchmark": "~2.1.2", "bufferutil": "~1.3.0", "utf-8-validate": "~2.0.0", "eslint-plugin-promise": "~3.4.0", "eslint-config-standard": "~6.2.1", "eslint-plugin-standard": "~2.0.1", "eslint-config-semistandard": "~7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws-2.0.0-beta.1.tgz_1484411376728_0.27804289758205414", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.0-beta.2": {"name": "ws", "version": "2.0.0-beta.2", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@2.0.0-beta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws#readme", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "8d5cc5dab90ad208419f0c140afe4f162ed5e30a", "tarball": "https://registry.npmjs.org/ws/-/ws-2.0.0-beta.2.tgz", "integrity": "sha512-x348TPICEspSU7CotVQF/ZyvyVQqe+j0oMWe/xFzXDAA34bbluOZUTOxYXokP9qRyD8m4KOSUskqPiR8Ye7nVQ==", "signatures": [{"sig": "MEUCIQCjLw2lyTVXDEFxMemOZd+6cGkwGzan4LOITh1x6MBsbQIgW+njRNSDYvJisa1CLyMUybGTpfWugvkh6yufNRbY9n8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "8d5cc5dab90ad208419f0c140afe4f162ed5e30a", "gitHead": "236ea222f8ddde18fbac0e234ec7297cfd66f4ab", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha test/*.test.js", "coverage": "istanbul cover _mocha --report html -- test/*.test.js", "integration": "npm run lint && mocha test/*.integration.js", "test-travis": "npm run lint && istanbul cover _mocha --report lcovonly -- test/*.test.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "7.4.0", "dependencies": {"ultron": "~1.1.0"}, "devDependencies": {"mocha": "~3.2.0", "eslint": "~3.14.0", "istanbul": "~0.4.5", "benchmark": "~2.1.2", "bufferutil": "~1.3.0", "utf-8-validate": "~2.0.0", "eslint-plugin-promise": "~3.4.0", "eslint-config-standard": "~6.2.1", "eslint-plugin-standard": "~2.0.1", "eslint-config-semistandard": "~7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws-2.0.0-beta.2.tgz_1485334367535_0.708880404708907", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.0": {"name": "ws", "version": "2.0.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws#readme", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "31bd7fc93c3dc8940ca812e36aef0d0754450f77", "tarball": "https://registry.npmjs.org/ws/-/ws-2.0.0.tgz", "integrity": "sha512-F/hMlyMxKvFzdusu6RnJaNzClMnjXgdEhvZBlF++JKEcz21H3zHu9CDzYLmFChEAW7ywIFdQsvMmNBBeXUqf1w==", "signatures": [{"sig": "MEYCIQCSFC9uW0AA2CoHMHIYXoDopNYl5zGmpL86cZ81xx+OKwIhAIjJd8Kn7Imef9sq6gFgQeERW56kXL9odOGZO3FxN9Xg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "31bd7fc93c3dc8940ca812e36aef0d0754450f77", "gitHead": "cb50a2958523770735be0a4118027ef6d1262328", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha test/*.test.js", "coverage": "istanbul cover _mocha --report html -- test/*.test.js", "integration": "npm run lint && mocha test/*.integration.js", "test-travis": "npm run lint && istanbul cover _mocha --report lcovonly -- test/*.test.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "7.4.0", "dependencies": {"ultron": "~1.1.0"}, "devDependencies": {"mocha": "~3.2.0", "eslint": "~3.14.0", "istanbul": "~0.4.5", "benchmark": "~2.1.2", "bufferutil": "~1.3.0", "utf-8-validate": "~2.0.0", "eslint-plugin-promise": "~3.4.0", "eslint-config-standard": "~6.2.1", "eslint-plugin-standard": "~2.0.1", "eslint-config-semistandard": "~7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws-2.0.0.tgz_1485777464564_0.8814913851674646", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.1": {"name": "ws", "version": "2.0.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@2.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws#readme", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "0d3498dcb29dbee9fa229e61ebffeba67316a827", "tarball": "https://registry.npmjs.org/ws/-/ws-2.0.1.tgz", "integrity": "sha512-AlGYQtk28aZB6+J9IrRz8R/B/ThGTjFIza1aRxlf59NAb8YWItfRx48IhNa9ECexl1mIyLksOFKpAY4ecGUDjg==", "signatures": [{"sig": "MEUCIAH181fOuLTC8FhzH3D15e/wOmizhjT9LDXcKsM+V/cAAiEA7sCjwQNu+BYkCzoAehy950qtB29zZCl/SLarNWH9UEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0d3498dcb29dbee9fa229e61ebffeba67316a827", "gitHead": "a8d21d40b3852f49e8a902ddc95055e9a1957130", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha test/*.test.js", "coverage": "istanbul cover _mocha --report html -- test/*.test.js", "integration": "npm run lint && mocha test/*.integration.js", "test-travis": "npm run lint && istanbul cover _mocha --report lcovonly -- test/*.test.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "7.4.0", "dependencies": {"ultron": "~1.1.0"}, "devDependencies": {"mocha": "~3.2.0", "eslint": "~3.14.0", "istanbul": "~0.4.5", "benchmark": "~2.1.2", "bufferutil": "~1.3.0", "utf-8-validate": "~2.0.0", "eslint-plugin-promise": "~3.4.0", "eslint-config-standard": "~6.2.1", "eslint-plugin-standard": "~2.0.1", "eslint-config-semistandard": "~7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws-2.0.1.tgz_1485945607140_0.13587458501569927", "host": "packages-18-east.internal.npmjs.com"}}, "2.0.2": {"name": "ws", "version": "2.0.2", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@2.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws#readme", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "6257d1a679f0cb23658cba3dcad1316e2b1000c5", "tarball": "https://registry.npmjs.org/ws/-/ws-2.0.2.tgz", "integrity": "sha512-jk0evnbSK6XrmrGXhAu7pSVHg9ZUdg4P7WQk7wefo9DCuBtDcqdbJghReZW9xR9BssJhUKZejJ6jBEcNd+/r8g==", "signatures": [{"sig": "MEUCIQDKmil0jfOuBoBpCSl2FuBytLOPAcjFTxuSEJONJp987wIgAzwZlnhE2yXPwM1ffmjQg9IFixptH0bC0j1rsNiu50U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "6257d1a679f0cb23658cba3dcad1316e2b1000c5", "gitHead": "f0d03cc79fb55d2df438120dedab01e016ba67b2", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha test/*.test.js", "coverage": "istanbul cover _mocha --report html -- test/*.test.js", "integration": "npm run lint && mocha test/*.integration.js", "test-travis": "npm run lint && istanbul cover _mocha --report lcovonly -- test/*.test.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "7.5.0", "dependencies": {"ultron": "~1.1.0"}, "devDependencies": {"mocha": "~3.2.0", "eslint": "~3.14.0", "istanbul": "~0.4.5", "benchmark": "~2.1.2", "bufferutil": "~1.3.0", "utf-8-validate": "~2.0.0", "eslint-plugin-promise": "~3.4.0", "eslint-config-standard": "~6.2.1", "eslint-plugin-standard": "~2.0.1", "eslint-config-semistandard": "~7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws-2.0.2.tgz_1486142978606_0.31241320190019906", "host": "packages-18-east.internal.npmjs.com"}}, "2.0.3": {"name": "ws", "version": "2.0.3", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@2.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws#readme", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "532fd499c3f7d7d720e543f1f807106cfc57d9cb", "tarball": "https://registry.npmjs.org/ws/-/ws-2.0.3.tgz", "integrity": "sha512-f8p1uiVmsJaFKGnEEkVrTlAJKX0d8AB8kJPkL6Rpr7KE/1L4cNlyBcoyPN1ArxU9qj5Xeg8XIa495lhQnAVEAA==", "signatures": [{"sig": "MEYCIQDOV/OMJIjBqh7rnPAgDY2OwCWnzoUh5EXT0YKpxAmQRwIhAMxQgRxkQLRjM4+i9uQONhCADnHliR/T/ehl+IE+nWJS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "532fd499c3f7d7d720e543f1f807106cfc57d9cb", "gitHead": "3918e11d200e574beca9d5abd61fbe3020434aed", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha test/*.test.js", "coverage": "istanbul cover _mocha --report html -- test/*.test.js", "integration": "npm run lint && mocha test/*.integration.js", "test-travis": "npm run lint && istanbul cover _mocha --report lcovonly -- test/*.test.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "7.5.0", "dependencies": {"ultron": "~1.1.0"}, "devDependencies": {"mocha": "~3.2.0", "eslint": "~3.15.0", "istanbul": "~0.4.5", "benchmark": "~2.1.2", "bufferutil": "~2.0.0", "utf-8-validate": "~3.0.0", "eslint-plugin-promise": "~3.4.0", "eslint-config-standard": "~6.2.1", "eslint-plugin-standard": "~2.0.1", "eslint-config-semistandard": "~7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws-2.0.3.tgz_1486561489812_0.7949141394346952", "host": "packages-18-east.internal.npmjs.com"}}, "1.1.2": {"name": "ws", "version": "1.1.2", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@1.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws#readme", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "8a244fa052401e08c9886cf44a85189e1fd4067f", "tarball": "https://registry.npmjs.org/ws/-/ws-1.1.2.tgz", "integrity": "sha512-lobrh3Dhp6tD1hv7NAIMx+oX/rsH/yd6/4krpBmJ/6ulsMZgQMuttlWTuYVWLV6ZjlpWIOjz55KbQbcKSQywEQ==", "signatures": [{"sig": "MEUCIQDzemfqbOnKLYwqbJxatMx6FwXXwRV5KY4ziVql31FMfQIgNRn12udTQZjTqJYPOJDuaj0o/Gf8n4CkH4qInPzIqt0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "8a244fa052401e08c9886cf44a85189e1fd4067f", "gitHead": "98f0d21f49c0d2c2daa175f840bc36c44d2729b1", "gypfile": true, "scripts": {"test": "make test"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "7.5.0", "dependencies": {"ultron": "1.0.x", "options": ">=0.0.5"}, "devDependencies": {"ansi": "0.3.x", "mocha": "2.3.x", "should": "8.0.x", "istanbul": "^0.4.1", "benchmark": "0.3.x", "expect.js": "0.3.x", "tinycolor": "0.0.x", "bufferutil": "1.2.x", "utf-8-validate": "1.2.x"}, "_npmOperationalInternal": {"tmp": "tmp/ws-1.1.2.tgz_1486968203380_0.8274557164404541", "host": "packages-12-west.internal.npmjs.com"}}, "2.1.0": {"name": "ws", "version": "2.1.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@2.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws#readme", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "b24eaed9609f8632dd51e3f7698619a90fddcc92", "tarball": "https://registry.npmjs.org/ws/-/ws-2.1.0.tgz", "integrity": "sha512-QuEzGPisagnyiEbaYKopaBC7ZbHy7eE/qBe05N1Niqz3nDFM+llWQcQ9MoJbshDcW2CsoJdreQqcxX9nahvpRA==", "signatures": [{"sig": "MEQCICnwj5EUiaKJa34i5ewPcHL83Tve6jXpkVn8hys/Xi+/AiBG3myexjXX8+4285jfOw1ecUXqTZS6aHjjgc8uJCHXow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "b24eaed9609f8632dd51e3f7698619a90fddcc92", "gitHead": "5bccfe59252923b85e7cffd56e3ddc1bd742f378", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha test/*.test.js", "coverage": "istanbul cover _mocha --report html -- test/*.test.js", "integration": "npm run lint && mocha test/*.integration.js", "test-travis": "npm run lint && istanbul cover _mocha --report lcovonly -- test/*.test.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "7.5.0", "dependencies": {"ultron": "~1.1.0"}, "devDependencies": {"mocha": "~3.2.0", "eslint": "~3.15.0", "istanbul": "~0.4.5", "benchmark": "~2.1.2", "bufferutil": "~2.0.0", "utf-8-validate": "~3.0.0", "eslint-plugin-promise": "~3.4.0", "eslint-config-standard": "~6.2.1", "eslint-plugin-standard": "~2.0.1", "eslint-config-semistandard": "~7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws-2.1.0.tgz_1487402689581_0.007632075110450387", "host": "packages-18-east.internal.npmjs.com"}}, "2.2.0": {"name": "ws", "version": "2.2.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@2.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws#readme", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "3218a7b1ebd15a09c56bb12a3e943a960eb7bde5", "tarball": "https://registry.npmjs.org/ws/-/ws-2.2.0.tgz", "integrity": "sha512-+uKOlLgmmL4l6v01DxpUN+4+TG9kRZTmiEnFH4EukJaskIFJCXV0iCi59gENV4ccDjLtvuiF9V4o9U7qmox1mw==", "signatures": [{"sig": "MEUCIA6tmPxF1e+lFO89nA24Qs7eOBeSF4ZBTv6/uS/kE78eAiEAniJSh8ijXJ8XqMJaPheUI7I0sV9vXvypORr42ScIzYw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "3218a7b1ebd15a09c56bb12a3e943a960eb7bde5", "gitHead": "ae42166ec5966e4924d5995c84514d58d206a7ea", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha test/*.test.js", "coverage": "istanbul cover _mocha --report html -- test/*.test.js", "integration": "npm run lint && mocha test/*.integration.js", "test-travis": "npm run lint && istanbul cover _mocha --report lcovonly -- test/*.test.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "7.6.0", "dependencies": {"ultron": "~1.1.0"}, "devDependencies": {"mocha": "~3.2.0", "eslint": "~3.16.0", "istanbul": "~0.4.5", "benchmark": "~2.1.2", "bufferutil": "~2.0.0", "utf-8-validate": "~3.0.0", "eslint-plugin-promise": "~3.4.0", "eslint-config-standard": "~6.2.1", "eslint-plugin-standard": "~2.0.1", "eslint-config-semistandard": "~7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws-2.2.0.tgz_1488274175845_0.12055460130795836", "host": "packages-18-east.internal.npmjs.com"}}, "1.1.3": {"name": "ws", "version": "1.1.3", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@1.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws#readme", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "67c3fd0dded7abd3c3316d281b7c968c3a2f4a3e", "tarball": "https://registry.npmjs.org/ws/-/ws-1.1.3.tgz", "integrity": "sha512-HXjfFU50CwX6msLgURf+NYwTd4gTTKx1TUzzkHoOYVBLGq+ud33MscO2TvdQiiEv34fUiCv4YR4Elsni2cEQKg==", "signatures": [{"sig": "MEQCIBo3RqD0FwEx21D9zvxIJahjJ7rayDJcYf2qOmyt+EY3AiANLmjpN8FFga6udtpG1km81QRTNfGxoqf0M+j0hqZR4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "67c3fd0dded7abd3c3316d281b7c968c3a2f4a3e", "gitHead": "32132056da3a4223f86f4337ef633cebaebea9b0", "gypfile": true, "scripts": {"test": "make test"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "7.7.2", "dependencies": {"ultron": "1.0.x", "options": ">=0.0.5"}, "devDependencies": {"ansi": "0.3.x", "mocha": "2.3.x", "should": "8.0.x", "istanbul": "^0.4.1", "benchmark": "0.3.x", "expect.js": "0.3.x", "tinycolor": "0.0.x", "bufferutil": "1.2.x", "utf-8-validate": "1.2.x"}, "_npmOperationalInternal": {"tmp": "tmp/ws-1.1.3.tgz_1489230068240_0.24778611189685762", "host": "packages-18-east.internal.npmjs.com"}}, "1.1.4": {"name": "ws", "version": "1.1.4", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@1.1.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws#readme", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "57f40d036832e5f5055662a397c4de76ed66bf61", "tarball": "https://registry.npmjs.org/ws/-/ws-1.1.4.tgz", "integrity": "sha512-oPppAGuy/CRHmbhtWg1vH0rah72PUDrqg0w/SsssTeQfK3nWxzIpkJ1R7nDUTohzVDuDZU2W4mkRdji6+g9IJA==", "signatures": [{"sig": "MEYCIQDkJPr+pYFwEHs6UNgZP14TWhQPeFxrc8ksOwwY50AywgIhAMV6d989XOupYFNH279zL2gQOgs5uWeNHYKf8HVokKNF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "57f40d036832e5f5055662a397c4de76ed66bf61", "gitHead": "19106a14d1782bed6f3b12a612e1a73a4970fdbe", "gypfile": true, "scripts": {"test": "make test"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "simple to use, blazing fast and thoroughly tested websocket client, server and console for node.js, up-to-date against RFC-6455", "directories": {}, "_nodeVersion": "7.7.2", "dependencies": {"ultron": "1.0.x", "options": ">=0.0.5"}, "devDependencies": {"ansi": "0.3.x", "mocha": "2.3.x", "should": "8.0.x", "istanbul": "^0.4.1", "benchmark": "0.3.x", "expect.js": "0.3.x", "tinycolor": "0.0.x", "bufferutil": "1.2.x", "utf-8-validate": "1.2.x"}, "_npmOperationalInternal": {"tmp": "tmp/ws-1.1.4.tgz_1489230923644_0.4469207131769508", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.1": {"name": "ws", "version": "2.2.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@2.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws#readme", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "f5ecbd4d47fb55a251d1a275223d47d693d3a8f2", "tarball": "https://registry.npmjs.org/ws/-/ws-2.2.1.tgz", "integrity": "sha512-Kxfc16zKwRv4MoBOyetZKvS005fOY1KfuavUUMF1BW0wdFr0ALzr4kamPqHDNgPmJ7KugmMkrNcD9jhR1a0nmg==", "signatures": [{"sig": "MEUCIAQa7BrT+EhHLArAglyrn+QDQ+GvPQVDXN9vxR4YfApDAiEA9QZAK0YlTJH5MYI+WQt6N0W6aOB8bAFtfykTn1FDm3w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f5ecbd4d47fb55a251d1a275223d47d693d3a8f2", "gitHead": "08eb82725afce71f495f82e5f24d3e375a1670d2", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha test/*.test.js", "coverage": "istanbul cover _mocha --report html -- test/*.test.js", "integration": "npm run lint && mocha test/*.integration.js", "test-travis": "npm run lint && istanbul cover _mocha --report lcovonly -- test/*.test.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "7.7.2", "dependencies": {"ultron": "~1.1.0"}, "devDependencies": {"mocha": "~3.2.0", "eslint": "~3.17.0", "istanbul": "~0.4.5", "benchmark": "~2.1.2", "bufferutil": "~3.0.0", "utf-8-validate": "~3.0.0", "eslint-plugin-node": "~4.2.0", "eslint-plugin-import": "~2.2.0", "eslint-plugin-promise": "~3.5.0", "eslint-config-standard": "~8.0.0-beta.1", "eslint-plugin-standard": "~2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws-2.2.1.tgz_1489388739570_0.1363778745289892", "host": "packages-18-east.internal.npmjs.com"}}, "2.2.2": {"name": "ws", "version": "2.2.2", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@2.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws#readme", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "aa26daf39c52b20ed716e3447f8641494a726b01", "tarball": "https://registry.npmjs.org/ws/-/ws-2.2.2.tgz", "integrity": "sha512-fMDPJcasvWPAcjXL+F1Nm2nELCtekRoqOOLjx1YyFSXfYUule0Ru7wxZ1SIoqwGxgblPriGhJ790Vr9QZPcGOA==", "signatures": [{"sig": "MEYCIQCysACfEz/A0HI2pMdCWFXR4DJJW3XdZ8Xp0xfBZk/4ywIhAJ+GQxuPA7iGzU+x6ev4XKGkpGPZ1dfq03Lr7dCFf16Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "aa26daf39c52b20ed716e3447f8641494a726b01", "gitHead": "286d513c1239bc2f1c9d23d997de1c954d3c8615", "scripts": {"lint": "eslint .", "test": "npm run lint && mocha test/*.test.js", "coverage": "istanbul cover _mocha --report html -- test/*.test.js", "integration": "npm run lint && mocha test/*.integration.js", "test-travis": "npm run lint && istanbul cover _mocha --report lcovonly -- test/*.test.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "7.7.3", "dependencies": {"ultron": "~1.1.0"}, "devDependencies": {"mocha": "~3.2.0", "eslint": "~3.18.0", "istanbul": "~0.4.5", "benchmark": "~2.1.2", "bufferutil": "~3.0.0", "utf-8-validate": "~3.0.0", "eslint-plugin-node": "~4.2.0", "eslint-plugin-import": "~2.2.0", "eslint-plugin-promise": "~3.5.0", "eslint-config-standard": "~8.0.0-beta.1", "eslint-plugin-standard": "~2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws-2.2.2.tgz_1490130412316_0.2044788005296141", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.3": {"name": "ws", "version": "2.2.3", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@2.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws#readme", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "f36c9719a56dff813f455af912a2078145bbd940", "tarball": "https://registry.npmjs.org/ws/-/ws-2.2.3.tgz", "integrity": "sha512-EMHbIpTjSE8/J4pjdCrIplDgJ67E4f1ng4xOkowMkeZUyrk0JG3igce2Xqh12N6qIES4/DidqdM7+ivxkU/fgQ==", "signatures": [{"sig": "MEUCIFzMLhvR34afI+6T/FaatVA4GwFvdyB6B99qhuhD0yUSAiEAmY8hdZBQ+zhqVt/ZE+HNok3/hl0lgWjfCVpqL2vbk+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f36c9719a56dff813f455af912a2078145bbd940", "gitHead": "212c7aab04a5f23d89111c1722371211efa2dd89", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "7.8.0", "dependencies": {"ultron": "~1.1.0", "safe-buffer": "~5.0.1"}, "devDependencies": {"nyc": "~10.2.0", "mocha": "~3.2.0", "eslint": "~3.19.0", "benchmark": "~2.1.2", "bufferutil": "~3.0.0", "utf-8-validate": "~3.0.0", "eslint-plugin-node": "~4.2.0", "eslint-plugin-import": "~2.2.0", "eslint-plugin-promise": "~3.5.0", "eslint-config-standard": "~8.0.0-beta.1", "eslint-plugin-standard": "~2.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws-2.2.3.tgz_1491214217857_0.5180311135482043", "host": "packages-12-west.internal.npmjs.com"}}, "2.3.0": {"name": "ws", "version": "2.3.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@2.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "459f482239b88e49b4ee17e8787c1bd43629aaaa", "tarball": "https://registry.npmjs.org/ws/-/ws-2.3.0.tgz", "integrity": "sha512-uAx5FvKfXJeEuci4ZpLkSG/7+cRQ8L2oM06R917JzHLth8Q0xSL+ydtU8TrqsQguBHrLWQDi1PbIw8Hdrr0gZA==", "signatures": [{"sig": "MEUCIDUpOoukOYIGNLit+OJf1+bQ8OIcPFVIJpDCJPpiVOyKAiEAmHLl6zPJPis7J06ZrLxxNzYD0wDzYpXNF412bNR7JX0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib"], "_shasum": "459f482239b88e49b4ee17e8787c1bd43629aaaa", "gitHead": "309d77f27ac2a505261c9d17dcfcf1a7ad0b1cae", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "7.9.0", "dependencies": {"ultron": "~1.1.0", "safe-buffer": "~5.0.1"}, "devDependencies": {"nyc": "~10.2.0", "mocha": "~3.2.0", "eslint": "~3.19.0", "benchmark": "~2.1.2", "bufferutil": "~3.0.0", "utf-8-validate": "~3.0.0", "eslint-plugin-node": "~4.2.0", "eslint-plugin-import": "~2.2.0", "eslint-plugin-promise": "~3.5.0", "eslint-config-standard": "~10.2.0", "eslint-plugin-standard": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws-2.3.0.tgz_1492697123735_0.0666230337228626", "host": "packages-12-west.internal.npmjs.com"}}, "2.3.1": {"name": "ws", "version": "2.3.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@2.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "6b94b3e447cb6a363f785eaf94af6359e8e81c80", "tarball": "https://registry.npmjs.org/ws/-/ws-2.3.1.tgz", "integrity": "sha512-61a+9LgtYZxTq1hAonhX8Xwpo2riK4IOR/BIVxioFbCfc3QFKmpE4x9dLExfLHKtUfVZigYa36tThVhO57erEw==", "signatures": [{"sig": "MEYCIQC6cUVv7mFfWNV85Ir2q/C4PRn8hSB4UlBGa06BEjSdKQIhAK5emUcRwgv+jIlABbT5gFLj+FYeIDCFqZas+m3ItpwV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib"], "_shasum": "6b94b3e447cb6a363f785eaf94af6359e8e81c80", "gitHead": "732aaf06b76700f104eeff2740e1896be4e88199", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "7.9.0", "dependencies": {"ultron": "~1.1.0", "safe-buffer": "~5.0.1"}, "devDependencies": {"nyc": "~10.2.0", "mocha": "~3.2.0", "eslint": "~3.19.0", "benchmark": "~2.1.2", "bufferutil": "~3.0.0", "utf-8-validate": "~3.0.0", "eslint-plugin-node": "~4.2.0", "eslint-plugin-import": "~2.2.0", "eslint-plugin-promise": "~3.5.0", "eslint-config-standard": "~10.2.0", "eslint-plugin-standard": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws-2.3.1.tgz_1492711201097_0.04034068179316819", "host": "packages-12-west.internal.npmjs.com"}}, "3.0.0": {"name": "ws", "version": "3.0.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@3.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "98ddb00056c8390cb751e7788788497f99103b6c", "tarball": "https://registry.npmjs.org/ws/-/ws-3.0.0.tgz", "integrity": "sha512-sjCOvLIEgRVT+inhGpm/f/YeusxCEg5BENrIj31YcOR+GTLcqIJ029uTmLVFNDJBCBvCxhkWFZrR6iMppq/s2A==", "signatures": [{"sig": "MEUCIEl47tVH8MLC0QO4Z0JFfvAJtA7XlzwXHEeh+c9wquR6AiEA/DPNzRq0gYEogYIKQZlVC60M0ogwpdXcKZJX0rZJwg0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib"], "_shasum": "98ddb00056c8390cb751e7788788497f99103b6c", "gitHead": "38df5a330aa91123851d8b49c231adf6c337ea77", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"ultron": "~1.1.0", "safe-buffer": "~5.0.1"}, "devDependencies": {"nyc": "~10.3.0", "mocha": "~3.4.1", "eslint": "~3.19.0", "benchmark": "~2.1.2", "bufferutil": "~3.0.0", "utf-8-validate": "~3.0.0", "eslint-plugin-node": "~4.2.0", "eslint-plugin-import": "~2.2.0", "eslint-plugin-promise": "~3.5.0", "eslint-config-standard": "~10.2.0", "eslint-plugin-standard": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws-3.0.0.tgz_1495006769812_0.34835603553801775", "host": "packages-18-east.internal.npmjs.com"}}, "3.1.0": {"name": "ws", "version": "3.1.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@3.1.0", "maintainers": [{"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "8afafecdeab46d572e5397ee880739367aa2f41c", "tarball": "https://registry.npmjs.org/ws/-/ws-3.1.0.tgz", "integrity": "sha512-TU4/qKFlyQFqNITNWiqPCUY9GqlAhEotlzfcZcve6VT1YEngQl1dDMqwQQS3eMYruJ5r/UD3lcsWib6iVMDGDw==", "signatures": [{"sig": "MEUCICFlp08UL3pTSLLyPMxrSCPY3yulZdBQw+FbgmaHJjRkAiEAn0fruXqS+gqK2QBZJfuTO4f5tjHxNXbzSDL9vfLd3yg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js", "lib"], "gitHead": "4e3ada1291b58c2a46303744835e5d466fcbc9b1", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"ultron": "~1.1.0", "safe-buffer": "~5.1.0"}, "devDependencies": {"nyc": "~11.0.1", "mocha": "~3.4.1", "eslint": "~4.3.0", "benchmark": "~2.1.2", "bufferutil": "~3.0.0", "utf-8-validate": "~3.0.0", "eslint-plugin-node": "~5.1.0", "eslint-plugin-import": "~2.7.0", "eslint-plugin-promise": "~3.5.0", "eslint-config-standard": "~10.2.0", "eslint-plugin-standard": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws-3.1.0.tgz_1501136212711_0.6941964158322662", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "ws", "version": "3.2.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@3.2.0", "maintainers": [{"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "d5d3d6b11aff71e73f808f40cc69d52bb6d4a185", "tarball": "https://registry.npmjs.org/ws/-/ws-3.2.0.tgz", "integrity": "sha512-hTS3mkXm/j85jTQOIcwVz3yK3up9xHgPtgEhDBOH3G18LDOZmSAG1omJeXejLKJakx+okv8vS1sopgs7rw0kVw==", "signatures": [{"sig": "MEQCIDq9u+EdFrKyASJlxETTy/FhAEDYrJY9kZ+CCaF/KIaBAiAxKopOm+50LsAyXzqjzGMtkUUsSqz2XI0SmYYjYuCEaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js", "lib"], "gitHead": "4082e695cd56cbd7a2e4bd385b77a78acd2a5b53", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "8.5.0", "dependencies": {"ultron": "~1.1.0", "safe-buffer": "~5.1.0", "async-limiter": "~1.0.0"}, "devDependencies": {"nyc": "~11.2.0", "mocha": "~3.5.0", "eslint": "~4.6.0", "benchmark": "~2.1.2", "bufferutil": "~3.0.0", "utf-8-validate": "~3.0.0", "eslint-plugin-node": "~5.1.0", "eslint-plugin-import": "~2.7.0", "eslint-plugin-promise": "~3.5.0", "eslint-config-standard": "~10.2.0", "eslint-plugin-standard": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws-3.2.0.tgz_1505456536249_0.6044613341800869", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "ws", "version": "3.3.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@3.3.0", "maintainers": [{"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "f8b948a1378af7efa702f5513da08dd516897c31", "tarball": "https://registry.npmjs.org/ws/-/ws-3.3.0.tgz", "integrity": "sha512-XPwGf44sJI6zgBpiWk44HQG6pK7HABl4F77Uydtb6BcgTC8fFpXHKM8bGu4AdBMtIjREDbNlvGitRZnwi0vXCA==", "signatures": [{"sig": "MEQCICQEHa+kLaciHtB1WQkv5Cta6geJeTdcX63CcAIyr7vlAiAcmxcDBXZbmFcp3PXy2ryt8oKFY2V6E8IMtMkFeRSvCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js", "lib"], "gitHead": "56f80625399de02abfe6c0d718ea5a8939969318", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "8.9.0", "dependencies": {"ultron": "~1.1.0", "safe-buffer": "~5.1.0", "async-limiter": "~1.0.0"}, "devDependencies": {"nyc": "~11.3.0", "mocha": "~4.0.0", "eslint": "~4.10.0", "benchmark": "~2.1.2", "bufferutil": "~3.0.0", "utf-8-validate": "~3.0.0", "eslint-plugin-node": "~5.2.0", "eslint-plugin-import": "~2.8.0", "eslint-plugin-promise": "~3.6.0", "eslint-config-standard": "~10.2.0", "eslint-plugin-standard": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws-3.3.0.tgz_1509788626202_0.664791889488697", "host": "s3://npm-registry-packages"}}, "1.1.5": {"name": "ws", "version": "1.1.5", "keywords": ["<PERSON><PERSON><PERSON>", "HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@1.1.5", "maintainers": [{"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "cbd9e6e75e09fc5d2c90015f21f0c40875e0dd51", "tarball": "https://registry.npmjs.org/ws/-/ws-1.1.5.tgz", "integrity": "sha512-o3KqipXNUdS7wpQzBHSe180lBGO60SoK0yVo3CYJgb2MkobuWuBX6dhkYP5ORCLd55y+SaflMOV5fqAB53ux4w==", "signatures": [{"sig": "MEUCID2EH9m0Ni0PI8but6RALvlEHuZRnNMqZqXVnh6H699JAiEAw13uXEe8FHngkvtPc+HtnNfDAmtTOix9pBfzCBc37MA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js", "lib"], "gitHead": "24edef58a0aab05e8220f76bd2377614dd4eee85", "scripts": {"test": "make test"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"ultron": "1.0.x", "options": ">=0.0.5"}, "devDependencies": {"ansi": "0.3.x", "mocha": "2.3.x", "should": "8.0.x", "istanbul": "^0.4.1", "benchmark": "0.3.x", "expect.js": "0.3.x", "tinycolor": "0.0.x", "bufferutil": "1.2.x", "utf-8-validate": "1.2.x"}, "_npmOperationalInternal": {"tmp": "tmp/ws-1.1.5.tgz_1510160102092_0.6812816471792758", "host": "s3://npm-registry-packages"}}, "3.3.1": {"name": "ws", "version": "3.3.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@3.3.1", "maintainers": [{"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "d97e34dee06a1190c61ac1e95f43cb60b78cf939", "tarball": "https://registry.npmjs.org/ws/-/ws-3.3.1.tgz", "integrity": "sha512-8A/uRMnQy8KCQsmep1m7Bk+z/+LIkeF7w+TDMLtX1iZm5Hq9HsUDmgFGaW1ACW5Cj0b2Qo7wCvRhYN2ErUVp/A==", "signatures": [{"sig": "MEQCIG4l4ecCjnfAancXbrnecC9oaOBtrti2lnyWLapN/XNRAiAtZpnOKVtm+lujOnXI/jG6HiuTRxPKXR4qrMqra02JXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js", "lib"], "gitHead": "70eb3b2f6284a361768ea518acb072d13986dade", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"ultron": "~1.1.0", "safe-buffer": "~5.1.0", "async-limiter": "~1.0.0"}, "devDependencies": {"nyc": "~11.3.0", "mocha": "~4.0.0", "eslint": "~4.10.0", "benchmark": "~2.1.2", "bufferutil": "~3.0.0", "utf-8-validate": "~3.0.0", "eslint-plugin-node": "~5.2.0", "eslint-plugin-import": "~2.8.0", "eslint-plugin-promise": "~3.6.0", "eslint-config-standard": "~10.2.0", "eslint-plugin-standard": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws-3.3.1.tgz_1510160314255_0.38848688220605254", "host": "s3://npm-registry-packages"}}, "3.3.2": {"name": "ws", "version": "3.3.2", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@3.3.2", "maintainers": [{"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "96c1d08b3fefda1d5c1e33700d3bfaa9be2d5608", "tarball": "https://registry.npmjs.org/ws/-/ws-3.3.2.tgz", "integrity": "sha512-t+WGpsNxhMR4v6EClXS8r8km5ZljKJzyGhJf7goJz9k5Ye3+b5Bvno5rjqPuIBn5mnn5GBb7o8IrIWHxX1qOLQ==", "signatures": [{"sig": "MEUCIQCttGFdA4+32ZQFz/fLCc0VjwpSZX4I95UsT/xFBOb2oAIgdTJKERYaKcMSZ7Rt8xNZFNDh4FsMxvg9npuAYqeIKFc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js", "lib"], "gitHead": "46b25476b13fdd24e1ab2c85d304b05bea75287e", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"ultron": "~1.1.0", "safe-buffer": "~5.1.0", "async-limiter": "~1.0.0"}, "devDependencies": {"nyc": "~11.3.0", "mocha": "~4.0.0", "eslint": "~4.11.0", "benchmark": "~2.1.2", "bufferutil": "~3.0.0", "utf-8-validate": "~3.0.0", "eslint-plugin-node": "~5.2.0", "eslint-plugin-import": "~2.8.0", "eslint-plugin-promise": "~3.6.0", "eslint-config-standard": "~10.2.0", "eslint-plugin-standard": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws-3.3.2.tgz_1511248761785_0.644444438861683", "host": "s3://npm-registry-packages"}}, "3.3.3": {"name": "ws", "version": "3.3.3", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@3.3.3", "maintainers": [{"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "f1cf84fe2d5e901ebce94efaece785f187a228f2", "tarball": "https://registry.npmjs.org/ws/-/ws-3.3.3.tgz", "integrity": "sha512-nnWLa/NwZSt4KQJu51MYlCcSQ5g7INpOrOMt4XV8j4dqTXdmlUmSHQ8/oLC069ckre0fRsgfvsKwbTdtKLCDkA==", "signatures": [{"sig": "MEUCIFK0sQcVx3ZijS93okHmw8T72hMW3ZSLufBxtpjHvz5pAiEApCSCHdIMQYraA69fJAcgHYCgmyRqi8UqWgijJ4tYC30=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js", "lib"], "gitHead": "157f58a73250674eb57c505a522c7e5fe5fb3bee", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"ultron": "~1.1.0", "safe-buffer": "~5.1.0", "async-limiter": "~1.0.0"}, "devDependencies": {"nyc": "~11.3.0", "mocha": "~4.0.0", "eslint": "~4.13.0", "benchmark": "~2.1.2", "bufferutil": "~3.0.0", "utf-8-validate": "~4.0.0", "eslint-plugin-node": "~5.2.0", "eslint-plugin-import": "~2.8.0", "eslint-plugin-promise": "~3.6.0", "eslint-config-standard": "~10.2.0", "eslint-plugin-standard": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws-3.3.3.tgz_1513504156433_0.36967517668381333", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "ws", "version": "4.0.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@4.0.0", "maintainers": [{"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "bfe1da4c08eeb9780b986e0e4d10eccd7345999f", "tarball": "https://registry.npmjs.org/ws/-/ws-4.0.0.tgz", "integrity": "sha512-QYslsH44bH8O7/W2815u5DpnCpXWpEK44FmaHffNwgJI4JMaSZONgPBTOfrxJ29mXKbXak+LsJ2uAkDTYq2ptQ==", "signatures": [{"sig": "MEUCIQDANyu2zeDBMBFLvvb/u2IG8qEdJMqVzbSHacGmHwuhdwIgBl9anliebkjDPvZySHnKpPFR7/PnyUPeflQdue7D09s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js", "lib"], "gitHead": "a04d9855a5a3d9bab62f9d22bcf5d1ee8185726b", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"ultron": "~1.1.0", "safe-buffer": "~5.1.0", "async-limiter": "~1.0.0"}, "devDependencies": {"nyc": "~11.4.1", "mocha": "~4.1.0", "eslint": "~4.14.0", "benchmark": "~2.1.2", "bufferutil": "~3.0.0", "utf-8-validate": "~4.0.0", "eslint-plugin-node": "~5.2.0", "eslint-plugin-import": "~2.8.0", "eslint-plugin-promise": "~3.6.0", "eslint-config-standard": "~10.2.0", "eslint-plugin-standard": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws-4.0.0.tgz_1515146653954_0.1453788885846734", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "ws", "version": "4.1.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@4.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "a979b5d7d4da68bf54efe0408967c324869a7289", "tarball": "https://registry.npmjs.org/ws/-/ws-4.1.0.tgz", "fileCount": 15, "integrity": "sha512-ZGh/8kF9rrRNffkLFV4AzhvooEclrOH0xaugmqGsIfFgOE/pIz4fMc4Ef+5HSQqTEug2S9JZIWDR47duDSLfaA==", "signatures": [{"sig": "MEUCIG8cceikyJWdjGyysPFpTq6CayIpuXJJM9ET5OU26SbgAiEAw/dkG+BwPZcWVUH9aGsGOYVxIQUIzl6CgJj4ZYhwYjY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103770}, "main": "index.js", "files": ["index.js", "lib"], "gitHead": "d390dc5b34c269669d5fa4450cd394d957055296", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "9.5.0", "dependencies": {"safe-buffer": "~5.1.0", "async-limiter": "~1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "~11.4.1", "mocha": "~5.0.0", "eslint": "~4.18.0", "benchmark": "~2.1.2", "bufferutil": "~3.0.0", "utf-8-validate": "~4.0.0", "eslint-plugin-node": "~6.0.0", "eslint-plugin-import": "~2.9.0", "eslint-plugin-promise": "~3.6.0", "eslint-config-standard": "~11.0.0", "eslint-plugin-standard": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws_4.1.0_1519286882886_0.7921800651154554", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "ws", "version": "5.0.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@5.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "fb4ede3fddcff99b157d292a1069ace8d6e04db9", "tarball": "https://registry.npmjs.org/ws/-/ws-5.0.0.tgz", "fileCount": 15, "integrity": "sha512-XXG4S0b771C68AeTHebBsJJBZMguxj7Em+D657RViuj6ppRd3tfuOhIK8eGwZGNb76C8MjQfCTfH2NN50rJN4w==", "signatures": [{"sig": "MEUCIQCawQ6C6KAoEZ4F3HYLQpq1W/Ku+ORjl3AddqVpqk1f9wIgPXwghju+gTgKujfDgg8YfFsDVvMMYmPFFN0oPJjoiIk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105211}, "main": "index.js", "files": ["index.js", "lib"], "gitHead": "d3af50627de62b0d8b9c42d915e8c6a426238363", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "9.7.1", "dependencies": {"async-limiter": "~1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "~11.4.1", "mocha": "~5.0.0", "eslint": "~4.18.0", "benchmark": "~2.1.2", "bufferutil": "~3.0.0", "utf-8-validate": "~4.0.0", "eslint-plugin-node": "~6.0.0", "eslint-plugin-import": "~2.9.0", "eslint-plugin-promise": "~3.6.0", "eslint-config-standard": "~11.0.0", "eslint-plugin-standard": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws_5.0.0_1520346850995_0.14732481956460153", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "ws", "version": "5.1.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@5.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "ad7f95a65c625d47c24f2b8e5928018cf965e2a6", "tarball": "https://registry.npmjs.org/ws/-/ws-5.1.0.tgz", "fileCount": 15, "integrity": "sha512-7KU/qkUXtJW9aa5WRKlo0puE1ejEoAgDb0D/Pt+lWpTkKF7Kp+MqFOtwNFwnuiYeeDpFjp0qyMniE84OjKIEqQ==", "signatures": [{"sig": "MEYCIQCXOT8NgTCp/E3ymbmVXeOg61PHabk693liLRlH/SRsLQIhAPVxXf+p30Rv1bToVYiX+SyKiTDj/duiINDnxYJUqkXs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103665}, "main": "index.js", "files": ["index.js", "lib"], "gitHead": "7c74567b974b79253ac32aac8f7d3dcb3e6d17e8", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "9.8.0", "dependencies": {"async-limiter": "~1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "~11.6.0", "mocha": "~5.0.0", "eslint": "~4.19.0", "benchmark": "~2.1.2", "bufferutil": "~3.0.0", "utf-8-validate": "~4.0.0", "eslint-plugin-node": "~6.0.0", "eslint-plugin-import": "~2.9.0", "eslint-plugin-promise": "~3.7.0", "eslint-config-standard": "~11.0.0", "eslint-plugin-standard": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws_5.1.0_1521481849018_0.6275278896809415", "host": "s3://npm-registry-packages"}}, "5.1.1": {"name": "ws", "version": "5.1.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@5.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "1d43704689711ac1942fd2f283e38f825c4b8b95", "tarball": "https://registry.npmjs.org/ws/-/ws-5.1.1.tgz", "fileCount": 15, "integrity": "sha512-bOusvpCb09TOBLbpMKszd45WKC2KPtxiyiHanv+H2DE3Az+1db5a/L7sVJZVDPUC1Br8f0SKRr1KjLpD1U/IAw==", "signatures": [{"sig": "MEYCIQC5YfwLyjPsZ679S17Md4QVIe79UrlERq2kiWfroCp6dQIhAPJO+LQW4ahu4s1Zfe92BX5+CQFv9lfRwm3oLIX95X9f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104066}, "main": "index.js", "files": ["index.js", "lib"], "gitHead": "10c92fff16c53be18c7be05c8a4c65d25cae8088", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "9.10.1", "dependencies": {"async-limiter": "~1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "~11.6.0", "mocha": "~5.0.0", "eslint": "~4.19.0", "benchmark": "~2.1.2", "bufferutil": "~3.0.0", "utf-8-validate": "~4.0.0", "eslint-plugin-node": "~6.0.0", "eslint-plugin-import": "~2.10.0", "eslint-plugin-promise": "~3.7.0", "eslint-config-standard": "~11.0.0", "eslint-plugin-standard": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws_5.1.1_1522676448860_0.5792033954157976", "host": "s3://npm-registry-packages"}}, "5.2.0": {"name": "ws", "version": "5.2.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@5.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "9fd95e3ac7c76f6ae8bcc868a0e3f11f1290c33e", "tarball": "https://registry.npmjs.org/ws/-/ws-5.2.0.tgz", "fileCount": 14, "integrity": "sha512-c18dMeW+PEQdDFzkhDsnBAlS4Z8KGStBQQUcQ5mf7Nf689jyGk0594L+i9RaQuf4gog6SvWLJorz2NfSaqxZ7w==", "signatures": [{"sig": "MEYCIQClaMynX+tOOJDmkbNBvgl2vSMkbjd0J43EIQcaqNzw/gIhAIWlzHpnSFZAAJ2eat5V+0VWeqdTtrF/6KAhiaN0vKWO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98827, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAxwVCRA9TVsSAnZWagAA9VQP/39iXeH1zQCzfpOJLSzn\noeVFUFTlKbVEvIQT1FD01yjCUp2T7gGHyjC4XnaFJ3pRXDa8+O6MWGnWYhtw\nssZ7osNJV/9r2AI+xrW+lcit5qp5qg1l2/zvc3OGSVyt/3ITlnfxYJE13dCn\n23hf9veQKZvigi6OGf73h6sGqfxVcQcI3Aeez0rp28khcZrZKyhOA12s6LyS\nbrhixmvClFPZeCjDO7iP0Fy0qWYvLLOagqK3Vb7FUrObHuEl9JGFqUNW0egS\ndsVxnyhhmPHzVbV+lxhNfOPHwKPWWE1ngCu3CTqBT6zkUhCbAl+SqZkbQbwu\ncGs/WsLmAoX773kDf9PfwWIr1psvp72LLN7VBOfmTPBaO7dvvGM4Fo84DmHz\neaubp5Hgad3zpqeVnXTLJAQ6gzwdiWqF0u6l9+gKMHmXhB71CgOpF1dhgGkh\noGve5bMFFORzbYdq/Cc/NTROQIY6QEy2/ASs0BjZIEd97IiEPFbtjrcqlyr8\n9v9jkP2u8GKrOVDMc2sZu8hMyTGu1np82UEFMSDg3g4tAbVvuGDupHzgrexB\nKWWFLcarSSQsdILHjVTyTuJqZ36zGmAzLPvRPTitOn0esUaC/+MQ3r5chySk\nuagTH9F4vTGf2YbwydNYsWWxZ1+tCf8bHegjvtkO3ybm1AaCuF8huU15zqOZ\n1jRV\r\n=Ikvp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js", "lib"], "gitHead": "e4d032c383dd2931de7dfbe81a0a8185cdffbf52", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "10.1.0", "dependencies": {"async-limiter": "~1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "~11.8.0", "mocha": "~5.2.0", "eslint": "~4.19.0", "benchmark": "~2.1.2", "bufferutil": "~3.0.0", "utf-8-validate": "~4.0.0", "eslint-plugin-node": "~6.0.0", "eslint-plugin-import": "~2.12.0", "eslint-plugin-promise": "~3.7.0", "eslint-config-standard": "~11.0.0", "eslint-plugin-standard": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws_5.2.0_1526930451284_0.23351796067266828", "host": "s3://npm-registry-packages"}}, "5.2.1": {"name": "ws", "version": "5.2.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@5.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "37827a0ba772d072a843c3615b0ad38bcdb354eb", "tarball": "https://registry.npmjs.org/ws/-/ws-5.2.1.tgz", "fileCount": 15, "integrity": "sha512-2NkHdPKjDBj3CHdnAGNpmlliryKqF+n9MYXX7/wsVC4yqYocKreKNjydPDvT3wShAZnndlM0RytEfTALCDvz7A==", "signatures": [{"sig": "MEQCIAhquwgdcHYa1emLkJRRucCTqH/xe4lHC7VJ4MLRU9iJAiBvF9NxsaqwBNFmT3CaGSor73H0pbu0wVBxPirUwQt9HQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbLnFLCRA9TVsSAnZWagAAHjAP/j1h2rW5glAQeQyZs9/i\nbLlM8N6XXz2dv81aEzpXTV/VQWp7WmyV130OUzqEqzAivqAcPWeGOqUyx2JR\nGp30PlS5LtU3IeqAk9kJNPUEmP3CkWGsJYW80Bv9eww3iKVj/O5XDu1RgQ8K\nigfwJQcHwhLjdugBrIvijGk2TALL6QC6CDjr6NWVxL0aRpJazG83NOLL9rNz\n/apAQhh+x5ANNRXi4eF+kY2pH7wTyrq/qOFAKamgcT22sTP08TeDv2Q0bpEh\n79l5yqSyaEJ0lwFL1PMEhCY7X1e/1ecGzfkNqPJsKm6TmhMRwkFLdJTMA0dH\nh8nTqMFBArmXihOn7804li8vEDkrPcvmVp7jS+rIDR0G7fUpKH8SAZ0CqQTi\nBGYaAQ1UTjSZ78KtstzzMIsbaBooBn/n+DLcg21zmBLvdKwBY99ndhlUOVki\n5N6JEHXa7b3Vck0HSb0fQ9BgMQPnjDZ3u1FJdw4rFbBrm/yoJvb41MYTlKxX\n0nyMLtzpuZ2+U4zuXe6oILI+ZDhlDEdkHts0ZjDbZEcXWkW02DoE8Qt+2fds\nDrz12QW2BosLOuiuvQwkfPiOvutWo0Q0hUgfjtekjEdQ4E4L4e8NpkgxlSWT\nHe66UBpdlRaiWjYEocv9KTuV21QcSrPUwyJY1xnJ28NNWEPJC0YTo+5zUn5E\nCzLX\r\n=10rz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js", "lib"], "gitHead": "175ce4605b80d610e558c858b0f8d74599a16db1", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "10.5.0", "dependencies": {"async-limiter": "~1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "~12.0.2", "mocha": "~5.2.0", "eslint": "~4.19.0", "benchmark": "~2.1.2", "bufferutil": "~3.0.0", "utf-8-validate": "~4.0.0", "eslint-plugin-node": "~6.0.0", "eslint-plugin-import": "~2.12.0", "eslint-plugin-promise": "~3.8.0", "eslint-config-standard": "~11.0.0", "eslint-plugin-standard": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws_5.2.1_1529770315889_0.5222187438823442", "host": "s3://npm-registry-packages"}}, "5.2.2": {"name": "ws", "version": "5.2.2", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@5.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "dffef14866b8e8dc9133582514d1befaf96e980f", "tarball": "https://registry.npmjs.org/ws/-/ws-5.2.2.tgz", "fileCount": 14, "integrity": "sha512-jaHFD6PFv6UgoIVda6qZllptQsMlDEJkTQcybzzXDYM1XO9Y8em691FGMPmM46WGyLU4z9KMgQN+qrux/nhlHA==", "signatures": [{"sig": "MEUCIFTkh2zOSQblSgFhq0zmxD1XFkKLUGfAhN5MTEOYEndUAiEAlPZuTfVmJjE9RYr01kMqIZqIes8GTZ/BEJriJX19R/M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99219, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRmFaCRA9TVsSAnZWagAAZH0P/iB0EIYIHqFAJznwT4el\n8xU2FN/na7yK3k+nV0cNYD+gKdOcTphij2IJGnQBM8hG4SlPBf+NBqy7/VBo\na3cmV3Rat395nmI3lhTgb9EDMFgYRQty3ORS3KAf2KEpFFA4QlTjOttjYsCq\nZN/j3GMnsnH47RxToPE9wTyC8d+cgIfdQHLN1k+5YaN5OtBCyKIXGbl+QJli\n2YLGAU1mp+yM+CF8CV+q6aodokoF/89D8LnJ7N5LjIgYGHTohB9c/fY/7v/5\nQLqd35RTo8OXMfiujUy2EhyGP5SyiTUzttAmXuSOxG3KQTtzss0dHMBxFeXJ\nO6ZDh124WW1VJYhdPKwfaHwszfmB6a95K2Gmu7xtvlq48qMq6Rfi9WQ1/rlc\nYyeyXAX1a/ykbEza4mm9oPfZpkPKSYM4s4fYufxyG3sAz3vKaOy4MQNA6gOC\n49sJBGT7kdTlPgHuE832t9T+J8ByCGNl/o2zJDDYLq6RLZqtgaSqAWtIBaFe\neW/yEVklhm2InF8e1yAiHg4au/6OKf4PFfKpcjdKvDbMZO8fFm6VDRYqFlI5\nnF2XzxK7p86sIe+YeFqVAzc4kMGcvYzrD7RhA25n+NBbjHaYChCLibgEDG7E\n2gqUv1T9BU2ihq845gapZ9h1b7/dpfqOKZCf5kvMxZLjp/rL8msRRPd4GIw9\nPXXp\r\n=QnaD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js", "lib"], "gitHead": "5d55e52529167c25f4fec35cb4753294e75bf9f2", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "10.6.0", "dependencies": {"async-limiter": "~1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "~12.0.2", "mocha": "~5.2.0", "eslint": "~4.19.0", "benchmark": "~2.1.2", "bufferutil": "~3.0.0", "utf-8-validate": "~4.0.0", "eslint-plugin-node": "~6.0.0", "eslint-plugin-import": "~2.12.0", "eslint-plugin-promise": "~3.8.0", "eslint-config-standard": "~11.0.0", "eslint-plugin-standard": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws_5.2.2_1531339098131_0.05866997625683701", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "ws", "version": "6.0.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@6.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "eaa494aded00ac4289d455bac8d84c7c651cef35", "tarball": "https://registry.npmjs.org/ws/-/ws-6.0.0.tgz", "fileCount": 15, "integrity": "sha512-c2UlYcAZp1VS8AORtpq6y4RJIkJ9dQz18W32SpR/qXGfLDZ2jU4y4wKvvZwqbi7U6gxFQTeE+urMbXU/tsDy4w==", "signatures": [{"sig": "MEQCIAS+fdpC010NNOwlDWVdjcm+ahAqbvdabiZfjLRlzRxdAiBqlfLOk5EQc+LCs03bNbuvFHcgGmpV7P2HwDZLFrp5/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99254, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUzj6CRA9TVsSAnZWagAAzt4P/34SG+VwqdtTCepV7vHg\nIZdyPTg4bMSXC2h1xsNsSPLa87m1OGenRSc7QyLuBwBmXV9PXEFwLs1c7rt2\ndMmVoOUx2nz6cV34LvhwlWitnurrTdCfuyc/jSjaDCYkVFreC1eCB4FqJcWv\nQO+Muvs79jK/JFFtxuQWJvrwlvghh7WdnA99YA0Dp2hG84blFrU3DTnvNaeJ\nPAFbmsT4VWpCncX5QAwVZdJkyf/Me9ePEtajo8B5VDXCKTcTW9rsaUD8+3gB\niALBZQjhEkQ4V57IioqeUvjx90c4+4XBfIBaW2DlL1sJAfBRcjz5y8GPLn0x\nEvTop4Q6AcFJYzh748h7Fbc1+K6BjQEmZbpjkWnNeIveaz4qSzsv/R3wPzVE\n+ICCwcEED5XIvU3hV4+uXFaPuq53j1z40eOtNLz3ELnh06KuiCAFI8iEMvUA\ne+a5052P4QTthG4ssO30PzngkocTQRXcBA/Ht1KVrjntA/AK5s7fq6SmtMRN\nzRSRqpgBjvfI9HDHwCx+/RWPqbV99pAxRResUY4YCctxu6y8cQCDZdwHusr2\njpe3nn/exxcofffYkJTimwC5VKTj81q2I4bMbFPLo88Ik9Sz2d3mVwQm4s+l\n3HoRlHEs7GWqFbACjETAKquTET7Oy9JzZIHNklGUk1OYhTsSG4yNUuaRk3TQ\nkAg1\r\n=xeQT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["browser.js", "index.js", "lib"], "browser": "browser.js", "gitHead": "1ee42fd67d365409096c11af0d6bc70fbe292c60", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "10.7.0", "dependencies": {"async-limiter": "~1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "~12.0.2", "mocha": "~5.2.0", "eslint": "~5.0.0", "benchmark": "~2.1.2", "bufferutil": "~4.0.0", "utf-8-validate": "~5.0.0", "eslint-plugin-node": "~7.0.0", "eslint-plugin-import": "~2.13.0", "eslint-plugin-promise": "~3.8.0", "eslint-config-standard": "~11.0.0", "eslint-plugin-standard": "~3.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws_6.0.0_1532180730113_0.2445653039826483", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "ws", "version": "6.1.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@6.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "119a9dbf92c54e190ec18d10e871d55c95cf9373", "tarball": "https://registry.npmjs.org/ws/-/ws-6.1.0.tgz", "fileCount": 15, "integrity": "sha512-H3dGVdGvW2H8bnYpIDc3u3LH8Wue3Qh+Zto6aXXFzvESkTVT6rAfKR6tR/+coaUvxs8yHtmNV0uioBF62ZGSTg==", "signatures": [{"sig": "MEUCIQDS1mo+upctpRQoZlErPIEzWP5D5cX49pmkOXFnyQOsxAIgTEib44FgIK9gbAdLmfCs13Vejzt3f+AVG5FFYRCGJD4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99539}, "main": "index.js", "browser": "browser.js", "gitHead": "b9ce38d80f847a843c05edfe1a907278bcffde0c", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {"async-limiter": "~1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "~13.0.1", "mocha": "~5.2.0", "eslint": "~5.6.1", "benchmark": "~2.1.2", "bufferutil": "~4.0.0", "utf-8-validate": "~5.0.0", "eslint-plugin-node": "~7.0.0", "eslint-plugin-import": "~2.14.0", "eslint-plugin-promise": "~4.0.0", "eslint-config-standard": "~12.0.0", "eslint-plugin-standard": "~4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws_6.1.0_1538722328829_0.473546842393884", "host": "s3://npm-registry-packages"}}, "6.1.1": {"name": "ws", "version": "6.1.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@6.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "3f5a17e51059272dc18cf039d3ef29eee5d3fdc6", "tarball": "https://registry.npmjs.org/ws/-/ws-6.1.1.tgz", "fileCount": 15, "integrity": "sha512-tfSg0NBK1w9FkVhIftPp2lWPoFu3AInndGyydLPTNSIA4o1V7GveE2fCTF7kFmHZ/+NGVObBptvFXwzF7ALXDQ==", "signatures": [{"sig": "MEQCIE+u4iGKqiDqtXwCZWNP0Ckw+E8ihMNeltnI2YQG2J6QAiBj10EtaRvR2P2bzeRL9hQ6CTDe9hMAjBDEJJKh+HEu/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb779dCRA9TVsSAnZWagAAlBMP/0rQR0ky6Shi0489pjNH\nkWd0vNvdBUyTGPfPEwCzvPbkNnTFw0Mp5B6X+mKFQUtTMF8OPSPQyAJ8YPRG\n85pqTxd248JlWpzXD3gc9rP0kX/oDfqJKgbWwy0o7N6MhfRjgto8z8TMyWKG\nWqDlEep8XLFuTidosbn5YSEjh8XM9/MYLxCv+iOwUFSjRS/7zPMPpDQcNNyd\nQZ2xiwhzHaTgNQSUJ8lZuSOjKjwgf4g1OCYE8KlZRCVcTA/CvY9HhcXt/S43\n4qYNx46kl5jlZB6SX5ta1X+OQJLF+OchLUhUeNIv9RtjvqAOF14ddfWXqAvU\n5780iME+RAj0yiMIlI147Zegg0y26kPcYbMzNyIh56p5UaleoXhSA7D/HktN\nMaqyl/eNpmMHoAbbVdy+IUbN1W8S0UoPN3EC/PFNtfOm6bcUqsZJvB2FVRDo\nz45qMWi68W4lcIYv/Bq9IJLdqaLDJJAtgSBuq5jB9SC6jhZzCqgc3zF2UszM\nRcHQ9mcEoDwTWbozvFJHjEzfY1R8rU62wdluIea2sDjiHvUiF6vnedZBaVGQ\n7Mso1OofEaAaSNOnEFQgdlZi63qoA9dHjAUMBWraFz0IX5wg0kDndliXJRP/\n8LGhcMUdK0pxMLDbgE0i57q0xdpB8zcvrM8ytbjIIYxDIAcRyKHbLQFECh08\n8Z4y\r\n=pErf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "gitHead": "029de0cc87bc33da460b81321713f04af2d98c56", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "11.1.0", "dependencies": {"async-limiter": "~1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "~13.1.0", "mocha": "~5.2.0", "eslint": "~5.9.0", "benchmark": "~2.1.4", "bufferutil": "~4.0.0", "utf-8-validate": "~5.0.0", "eslint-plugin-node": "~8.0.0", "eslint-plugin-import": "~2.14.0", "eslint-plugin-promise": "~4.0.1", "eslint-config-standard": "~12.0.0", "eslint-plugin-standard": "~4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws_6.1.1_1542438748701_0.29350249793936745", "host": "s3://npm-registry-packages"}}, "6.1.2": {"name": "ws", "version": "6.1.2", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@6.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "3cc7462e98792f0ac679424148903ded3b9c3ad8", "tarball": "https://registry.npmjs.org/ws/-/ws-6.1.2.tgz", "fileCount": 15, "integrity": "sha512-rfUqzvz0WxmSXtJpPMX2EeASXabOrSMk1ruMOV3JBTBjo4ac2lDjGGsbQSyxj8Odhw5fBib8ZKEjDNvgouNKYw==", "signatures": [{"sig": "MEQCID27twHSyISj8wP7lF77OP0HNl5Uqv5HU3lIZXIkd67fAiBi71MysdG9pfYgcB4KLz2iRtHFFYJssn4LiuPLE9zbjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101241, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb8HRXCRA9TVsSAnZWagAA+eQP/j8yGGNE4WomL+EdKWmn\nTIjjcekIj2v1sqeT5huJZ2IREDk82hrRSA0UF7OpyZZwIJrOC4bFvMxNRHYR\nwzTJ/k2FIlEJ3r8OtBPnrHdXYYml6II4yW3qya1fN5AANz/LiWe3mgh3GjZp\nZMHeUTAA1n1Ms46nAibXTsi4m7xm94azyTeFeMdYFfrGoq4iTO9DVbux/G/b\nY5bxJzzciXCvu7ZmL5AUktghiTMp+sjuDKP+WSpvYU7O5p1C88LjTzMO1G4l\n15Ye23Mx1Oj9n47ZSSqF/uvS6yihejnnFs+Daqx5oY4+k6ik5etYSuP9uj5E\nvuMDGWclD86E6v/aHFZVCs3jQo3QDan14Ck5lXuPvXLkRCl8Rdlw4KnmgnnI\niicjtpQY8V1KWU6s34WHoySRzPOS3ruYFD+WUupUzl8Gk3ssxm8bSw/8AH1t\naWVMwWaaVqCRadl/rCnnMWpF3B4JgzQWC1O0DPxmiZq4Ftic2wSYbP8JZb6d\nZ2CO08WMgT67NwzA8KVUUlauBLfaI58+ZlesE0KuY6HbhTCCJsVTyJDr7v5o\nU0AlBZl5En+HxllkZnwV8C0QFVRqQFHqLXZbuLA3iTB4V62ti3payzHowWaC\nB7K7wWcnW77dvJbSB18YT6yL53enuWJ9f6TpobeKg42gTFqk63dxo7PjSfNn\nNko2\r\n=k/S4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "gitHead": "14d9088391ac4495d04e64d76c3b83d4e75f80e2", "scripts": {"lint": "eslint . --ignore-path .gitignore && prettylint '**/*.{json,md}' --ignore-path .gitignore", "test": "npm run lint && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "npm run lint && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "11.1.0", "dependencies": {"async-limiter": "~1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "~13.1.0", "mocha": "~5.2.0", "eslint": "~5.9.0", "prettier": "~1.15.2", "benchmark": "~2.1.4", "bufferutil": "~4.0.0", "prettylint": "~1.0.0", "utf-8-validate": "~5.0.0", "eslint-config-prettier": "~3.3.0", "eslint-plugin-prettier": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws_6.1.2_1542485078627_0.02483811487025278", "host": "s3://npm-registry-packages"}}, "6.1.3": {"name": "ws", "version": "6.1.3", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@6.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "d2d2e5f0e3c700ef2de89080ebc0ac6e1bf3a72d", "tarball": "https://registry.npmjs.org/ws/-/ws-6.1.3.tgz", "fileCount": 15, "integrity": "sha512-tbSxiT+qJI223AP4iLfQbkbxkwdFcneYinM2+x46Gx2wgvbaOMO36czfdfVUBRTHvzAMRhDd98sA5d/BuWbQdg==", "signatures": [{"sig": "MEYCIQDWVrsbbyJJs+daxaqUgHxXaNlVL+7p/k4Frn8aQq+AVQIhANJwHbiARn4xgmh/dcP/cw8pLDGLRnK3LYi57wGfANRd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100829, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSWUfCRA9TVsSAnZWagAAfwIP+wc/E134+PtKAQyUt98N\nJg0s/p5NAcA2NjnDUIro11T3h8BX0n/pYjGChh0sbREBYCD1Yc9sH+3mNgQg\nIO/X54RHcVtgbzXud/GBc+8xBuE9ZztK+bnyxwYDeRABY7ZC+h7IapybvVhR\nmVt68fesQBmvA37AhKHejHMQgndai/6YZTdjKevOJrSSYd4XtwCcV5/lhP3I\nUm/ZA1oynIuKo6NdT+fJZ1VdvtCxwaW8eJ1IXF0xmeadtCtX7wQ6AF3mLnT1\nhXmMz/QYI3Hw/68k5b38lK4ni32yxlCYK3rs/BIrIjBnqMC+HjFV5f6MjXoA\nR8WihWoxAtkiRRC4mEmXqI/0spOEdLIvehgj+zCyLO0SP7D3svvS4JrI71he\n4AMWdWJl/xeGDcNzyi3GvIuZqzbMLJ/291NCN82Q0IHlQHzg8bIW+316qzfL\nZREhySLXUGc3g5LPHxtiFotRc/pK21qPdMIU1JHQYLh1btjtFWDP3KINBaw2\nkWrML3nPrdMEiWN+wyaL5h1fYoOYWz8pXZXOya56teEHYMqw7oXB3aW6ebqd\nQYkS0RqaqcCNAi8cThQePFRtlHaNLKS3jJJV58K26nt7AAUp36/x9KO58KyK\nodsOZ+LcqJS1FXOUvRYS8qKKfReSf0CterL8I2QPtbxQ1fHqD8ev2lQoQ6Bg\nPTqd\r\n=MjyM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "gitHead": "6fa6b8b388ad002a7d754d9ccdfac73d2bf73bc2", "scripts": {"lint": "eslint . --ignore-path .gitignore && prettylint '**/*.{json,md}' --ignore-path .gitignore", "test": "npm run lint && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "npm run lint && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "11.7.0", "dependencies": {"async-limiter": "~1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "~13.1.0", "mocha": "~5.2.0", "eslint": "~5.12.0", "prettier": "~1.16.1", "benchmark": "~2.1.4", "bufferutil": "~4.0.0", "prettylint": "~1.0.0", "utf-8-validate": "~5.0.0", "eslint-config-prettier": "~3.6.0", "eslint-plugin-prettier": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws_6.1.3_1548313886381_0.9476643579223558", "host": "s3://npm-registry-packages"}}, "6.1.4": {"name": "ws", "version": "6.1.4", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@6.1.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "5b5c8800afab925e94ccb29d153c8d02c1776ef9", "tarball": "https://registry.npmjs.org/ws/-/ws-6.1.4.tgz", "fileCount": 15, "integrity": "sha512-eqZfL+NE/YQc1/ZynhojeV8q+H050oR8AZ2uIev7RU10svA9ZnJUddHcOUZTJLinZ9yEfdA2kSATS2qZK5fhJA==", "signatures": [{"sig": "MEUCIDd+jGLzmgVypJcgYskl6XJkvna9kUWBfJOu1MPGZvKEAiEA9u2KIJIXJe6xTjVqFEEu+69khzqxyCZBjDUl3Wi8UWQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100922, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcaENICRA9TVsSAnZWagAAJOgQAIIYsDq4idx3DQFPWO0b\n96kAU/3kHf7+wDwhojNWz2FSZKCSaFXvZnPPEY009yp3bJTxawUmFaj5MUaf\np+Vl9lm8AsGqIHbxnQin06AU5UpEugn2yZrxuZ4+ePBAo0An6BvCSBfC4Qa8\nUjrKH0S3U7Evxbo5xRFPr5YPqaTCzIdyEXXpvgiJE0X7NBO/puy1yGl3qgLJ\nN4ptfnHP6d+EDTfGdWZXDC9oP9yiiwGlWUG53wKJIMEKcBgDGlMxDRs1eTxD\nbV0DrNJPP71FMOQdY68twHAM6rVTsZWw0AXt0I7reDNrjplVJmznVlloukFi\n8Km72uqz0fXMt43AAGKPTIWAnJS/z9gozqwfJeuc2ZXB53wgkNHhH6FM0zO7\neOfpeBExHQavSFb2Dl26KCus/rBoeKnAhOFaX6csSE1hA5YF7m2Qqgj8eoCl\nSdHXOD3P5A2Nh8MfuKrkeDJ6PRzB2Ut6P85n1Q6Ood9D+fct6pmAIRxec2zr\nHWZWnvPAKoUL4BaYsnuYLhuclcepniEdmY2LAUU7XGvHTL82UynrL8hPCF1d\neu3Ag6bHaq94+5CKY5FPh4/tJX1/qiCg5wIfKOWTsd+P8PZBOMbawSSIq034\n2Mu7tC7JvRqlPnpTEEKm49cLEu3wXZoahyi+7lBDbczSmNvoM2pH73ITrOih\nv9LM\r\n=PIG+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "gitHead": "dc745cc10918eec23bd0680bcc7bce26101b8e2d", "scripts": {"lint": "eslint . --ignore-path .gitignore && prettylint '**/*.{json,md}' --ignore-path .gitignore", "test": "npm run lint && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "npm run lint && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "11.10.0", "dependencies": {"async-limiter": "~1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "~13.3.0", "mocha": "~5.2.0", "eslint": "~5.14.0", "prettier": "~1.16.1", "benchmark": "~2.1.4", "bufferutil": "~4.0.0", "prettylint": "~1.0.0", "utf-8-validate": "~5.0.0", "eslint-config-prettier": "~4.0.0", "eslint-plugin-prettier": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws_6.1.4_1550336840117_0.867098687765907", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "ws", "version": "6.2.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@6.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "13806d9913b2a5f3cbb9ba47b563c002cbc7c526", "tarball": "https://registry.npmjs.org/ws/-/ws-6.2.0.tgz", "fileCount": 15, "integrity": "sha512-deZYUNlt2O4buFCa3t5bKLf8A7FPP/TVjwOeVNpw818Ma5nk4MLXls2eoEGS39o8119QIYxTrTDoPQ5B/gTD6w==", "signatures": [{"sig": "MEQCIDvxOhL57qYMmssJlvRBG2Ogw5l2HS95QBJJiQwQKjFdAiA9xzB+kYLGp2CG/kl94AKEnqAEaofH19GFZnyvShAZQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101597, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcf3jyCRA9TVsSAnZWagAAhRUP/jPLHHJyOx/g4umSgYIq\nkTKGLea0hXp7174Fd6tWaQPCF0afRGYJZ/szTCkOtcshU1OMSFsmUSxVokK/\nlEftgoO+reDS69YW/fIavKyzXEhNXsRkSm4ZJVmijZTI+rmr4W7hmcw+N38k\nAJWGr+3y5Ul6WxTPyVSOrWnY0YW3ZTdy5na5BwGbc+o2WAVml/jaDK3gXZPF\nUpHsJ4Vo1b1pxR4jZkVCCm3qO8X5ijmAB0g038bT1y3zXRVpYkittjhRkweH\nbzME40Jwk3rBhfjMiS1ZWpUK+6JebVkAHDlXMawtwH93Wmaj7wAKxjb7ubyF\nrmtD/6QjYFBMOCuJwAPZj2IW5OQXDivSH4GouVAvAH10AM2mijZPw+vE4J7Y\nKVyCGYQ5uoJlFz3AuK6ifaihdDML5fxK5fwHAHdBeQGELKCSBd36vGTU+avm\nrj9I2R2WOhwSSnp9FBX1jhVQgAfXNbElOe1Mhu5TUqfIT/f4UsY0SJAc5VCx\niChmvzA5lSgsVOZQRo6xa7TH7FLODxt5BvJckd1h0k8NDm50YzhitKg03d1/\nfuG/JSc0b1bmvtpTQ6Xra4dqkH24y2NnOCUBVnqn8NlmHjQ336iLgrB3wnvJ\n8yadZtlgEn0ekb7nnqXjb9XqOyap5TMHBZ9asQvf0aHwQnA0mDh3HANbmWnq\n2Rgi\r\n=xp5G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "gitHead": "eb6f8b05a5862b4e422c98395afff4584c9f1839", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yml}\"", "test": "npm run lint && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "npm run lint && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "11.10.1", "dependencies": {"async-limiter": "~1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "~13.3.0", "mocha": "~6.0.0", "eslint": "~5.15.0", "prettier": "~1.16.1", "benchmark": "~2.1.4", "coveralls": "~3.0.3", "bufferutil": "~4.0.0", "utf-8-validate": "~5.0.0", "eslint-config-prettier": "~4.1.0", "eslint-plugin-prettier": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws_6.2.0_1551857905570_0.09344192216650282", "host": "s3://npm-registry-packages"}}, "6.2.1": {"name": "ws", "version": "6.2.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@6.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "442fdf0a47ed64f59b6a5d8ff130f4748ed524fb", "tarball": "https://registry.npmjs.org/ws/-/ws-6.2.1.tgz", "fileCount": 15, "integrity": "sha512-GIyAXC2cB7LjvpgMt9EKS2ldqr0MTrORaleiOno6TweZ6r3TKtoFQWay/2PceJ3RuBasOHzXNn5Lrw1X0bEjqA==", "signatures": [{"sig": "MEUCIFvCUddmhDwe7hbktpJXIuFDRgeme6/n6BU3XrPuU3QsAiEAjPuzexcgn8PtEkBdSmpfDLXN5eAHE1uIT1LzMQ+1ooY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101449, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcmzoLCRA9TVsSAnZWagAAtEIP/RkNfhGX2vND8luKumDK\n+yGhSf84CsOxXBmfkVLkgQATR6gTeGLpsH1VnLqUcRzBL1sQdNM5bzlhhIHc\npd88Wdsocs0KB8+AootOv+WFz7As6gp0Y4CVEnrTr1jU4QSxbhxb/RbVVwyA\n4WiBvgQPp+AFGtWt1FfUtZkZTURyj9yRDP7xqCmGIuAIa6kZvGV65m82cAve\nP9F65FEcfZGM5996u2kw24pPzJ/aVg0lNrsgiUujHiom73UvIW0qP3u73XpE\nmolxbiAdx0oePTau42100B3Yzod4/uTR3LGvVayl7Jd+4mYaUt08DiXfTJB/\nsVK+W6Wkx/0OyH9sg32EEqCT6f2B4N8PEHvEv9xNst7PnQzpCPufHGlfzu5X\nlCuc27/08KbkpltLghmA1DRAno2ahjpxCIb8hQw5omv63zsuSUMqn9urZEeB\nGGgHLl1NkxbDFrKPpmeP54O5f16/uJQz0Fh+iIN7OV565GLNxQ92qep2+XkU\nV7dpiP2pWky+GTYMmd96FOIp1rj1EsOF8BbwknLZQnMXXOGyEFcMU5qoo0R6\nFRufPIwJvyUQxMyYvlm8BN7OGxzi9DkxFRDKzRF1AbULDDHdHmlGiYe0Iw5P\nt1EeVGEqj52f/1H9z4f2oVnuk7iqSs5KQjs532Bk1Ap177l+g7xF189EEbft\nA0mX\r\n=r0jJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "gitHead": "d57db27daf0e610590e3168266a214201a8c1d3a", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yml}\"", "test": "npm run lint && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "npm run lint && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"async-limiter": "~1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "~13.3.0", "mocha": "~6.0.0", "eslint": "~5.15.0", "prettier": "~1.16.1", "benchmark": "~2.1.4", "coveralls": "~3.0.3", "bufferutil": "~4.0.0", "utf-8-validate": "~5.0.0", "eslint-config-prettier": "~4.1.0", "eslint-plugin-prettier": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws_6.2.1_1553676810270_0.01539710580745246", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "ws", "version": "7.0.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "79351cbc3f784b3c20d0821baf4b4ff809ffbf51", "tarball": "https://registry.npmjs.org/ws/-/ws-7.0.0.tgz", "fileCount": 15, "integrity": "sha512-cknCal4k0EAOrh1SHHPPWWh4qm93g1IuGGGwBjWkXmCG7LsDtL8w9w+YVfaF+KSVwiHQKDIMsSLBVftKf9d1pg==", "signatures": [{"sig": "MEUCIQDcolZ+Sf75H0R5OyKFmUxNLUAxhQjKFfUkykeeniQSwQIgVGgSWaVSTAtjAEmHq+PDLmx4i7ae92U7XTfNEKILPVg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101063, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyHPeCRA9TVsSAnZWagAA62QP/RDg0q0DWhTXKjcDCKJj\nLX16DfGhpjj1uCCk+VlKjGQ+QZ1B5hFQVXQwpgO9Wv8/CUgTmY3DNSWNkBiD\ny+F/uS7+TKvoPsO/JKLnGTOnfsbmxhNm8eo1Gh22K0M9wnJjEbCMrBj+QmoR\nTQAZPBrMXBqnM+DB5FNyAYVaRPzFUenYphMYAx5Dz8qq2R3Zvy2q+zMRqVZJ\nQFrg12tv4L752nRzikFi9kjbJ2wq+/BYjvanuh+80YMkaIM6W3uWfgDJOIuK\n8PBLxO6hggNxTrWInLEw6qN5hMnQDPRBD7sTalJ/56huOGDi1XD9NPjVc4OI\nV6smLPUYVl2kw+fVpRADrljAXmaOJmvgZ7vzjzC8AKPvovEFMhSUxpXWeRag\n8AAoj6CemHAi4g7hXxTjJcqVSvb+efl7KmZx5sCsaX6ImGnq2QKqyKueLrcN\nDZ3D/ZyEKR9eJcCzWktZpUcp7D2++RZtfg76EGGaST531xuvY3L5e919BYzh\nEQnYhPImXgjM2Krb5b5ZblsJXWVTjXHg2HHrU4SbmDff1QCnU1NAssREgxF/\nEr/1lz1KYoGSX24/StLdGlaho9AWIgKg5zrs5wUD5Fa0oPWZjwZZ70cT4XNA\n89EHgSboPx3DeD8n/Wutnc5GYVZ57APQ5DMs8hqf5Wjd+2yF6NlXGgp2U5Y8\nta+i\r\n=51J7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "gitHead": "092a822a41eb22f6d6745c18bc29b9c40715680f", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "npm run lint && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "npm run lint && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "greenkeeper": {"commitMessages": {"dependencyUpdate": "[pkg] Update ${dependency} to version ${version}", "devDependencyUpdate": "[pkg] Update ${dependency} to version ${version}"}}, "_nodeVersion": "12.1.0", "dependencies": {"async-limiter": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "mocha": "^6.1.3", "eslint": "^5.16.0", "prettier": "^1.17.0", "benchmark": "^2.1.4", "coveralls": "^3.0.3", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^4.1.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.0.0_1556640734048_0.3888991947866549", "host": "s3://npm-registry-packages"}}, "7.0.1": {"name": "ws", "version": "7.0.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "1a04e86cc3a57c03783f4910fdb090cf31b8e165", "tarball": "https://registry.npmjs.org/ws/-/ws-7.0.1.tgz", "fileCount": 15, "integrity": "sha512-ILHfMbuqLJvnSgYXLgy4kMntroJpe8hT41dOVWM8bxRuw6TK4mgMp9VJUNsZTEc5Bh+Mbs0DJT4M0N+wBG9l9A==", "signatures": [{"sig": "MEQCIGZN/6QoTT1/RMDF7yXeP1lUoGv0t5hQBYOjkv/IZIeuAiBsW6boeSbV++vVwtWZywRo6Vbc21ClRg8gyPZv6mmxjw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101173, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdB7zVCRA9TVsSAnZWagAAZaMP/0fE9/JMko6mytPovF4S\nINT3I7c6IjgHGuTPOeZDy1phNLVdWPpnuqvHN0Oj7gFV19+NPLpsJNmdqj13\nW3ad6psXDkWl+7gQp36G7g4AsZwME8wia3DrIgCAdNW3dVWLJjztohY0w2c0\nse3BsB+NcD1YjeE+smJtZyNAVZ1eilvzxDjyrMbKU4gYjTq5Tr+yJMGUv0xe\nxS5jtne8Zh1iinyYCy+PZsELvrelxlULLSoVSCga/g4R3x7HPA+p9ubY8C4y\nbkJ8wOkrztLKHrgIyNdbkc9PtgWQO0puZwYBpY4YokNAhWMfMm00dJ9qGgs/\n+B3COQ8ZnL9vxm0AFhYunCMAx+4gY1PP6bkMnckeFmeq4fuJCCEFuQEEIsft\neQfZjcBOE2z7k4nrgqKFF7pZmQoXoBPGy61JufziS3kwJs/+yuRESUq9eR5M\nNs0biaJP3fyEZyygUwdgxTK8kZhfzQedrvH8Fz5jlDSETwwIXMLsEGbBbPvP\nHXNz3tU1jM5RjiPlJewf3oXuT9cAtJOzw1hBgOcVlEQ4J6UtVkr0rSQECYUZ\nYuaECbc80+Fd5hXOlnvVYSgpzfCRFrXeRf9c+//hXtoQS8asoZHMPXRTUOVq\ndgo343oQ1Frp9wJWpLfy/9inKA6OuYETa99KSnYFmzt2nGLYzx87asePaLtX\nmQ28\r\n=YSYq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "gitHead": "38d3bf24a0caa2f504361926582ed679a22e08f8", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "npm run lint && nyc --reporter=html --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "npm run lint && mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "greenkeeper": {"commitMessages": {"dependencyUpdate": "[pkg] Update ${dependency} to version ${version}", "devDependencyUpdate": "[pkg] Update ${dependency} to version ${version}"}}, "_nodeVersion": "12.4.0", "dependencies": {"async-limiter": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "mocha": "^6.1.3", "eslint": "^5.16.0", "prettier": "^1.17.0", "benchmark": "^2.1.4", "coveralls": "^3.0.3", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^5.0.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.0.1_1560788180167_0.06845587057585378", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "ws", "version": "7.1.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "0395646c6fcc3ac56abf61ce1a42039637a6bd98", "tarball": "https://registry.npmjs.org/ws/-/ws-7.1.0.tgz", "fileCount": 16, "integrity": "sha512-Swie2C4fs7CkwlHu1glMePLYJJsWjzhl1vm3ZaLplD0h7OMkZyZ6kLTB/OagiU923bZrPFXuDTeEqaEN4NWG4g==", "signatures": [{"sig": "MEYCIQDTyeof0hdR7afXm7yfHtNsXl7FxKNNW2efH93D54njQQIhAPaHKLjqF4m5yA8tCq5RTIXWjKy+fINE0xCOaS9ae40M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdI2srCRA9TVsSAnZWagAAaIUP/1+xbr9vMFO2lf6EeAZo\n6okOcB6C3OQFoq5LlPIBCzf9lZQXqj4NYv+p8LH7VIb9CzvUu7tpibu7ailg\nV3++ymB5rl8kvZeFOvIMNUmPM1tmG/EuUw4SBI6/U3o2eV/mgx1/cib6W7D5\nUBJGgtDpFDNs/CabvCu8PRd577I5rrGTfVxO/Re7Y7w3sQtm0OyX4wbEiBSN\nlWx+BmevMp5YCjshjxOrHeT3PeGGQS+LWggcNCxgvpQn+nX76S97s8FWI394\nkKh3k78JB+/gWdLFyA/SoHeMOucw7AIWrl1+Py63EphVRpwQ92gn64cJkUjQ\n4o2plZUGU/G4bul5ICcMnWuSqVN0FjpiqzV5hk5FOuWtOdVvDc/qt7qdddM3\n2KBO4eujib2R7eQnvBHwT4hjzQlH/GsF6+29ALoYNcso2u9MZfR2TBdjwe7h\n/VvZYxNaTx6DY614ED0nEOkZHKsaCtiP8npp/CpUUt0AtHC8QmsOQzXxsGNL\nQIZnDuhONkAcW66Xj6JSWMR3y8Z3HSb/kjtJeH/pGicc2BUfx5f40lpRCwp4\nBe7gSXrs/yxpew9ArNN/XmjhSqwB1KFEzGc1Yqhi4jjs/GPyVoV+tpnC+dC8\ndUYroPkDXhdZq/cfCW4SU5PE+4cSYKEVSr/DUWeLk17S9Yxiu8v3rjmPjGbF\n9fza\r\n=zgVn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "gitHead": "dd42c8bc3ac215668bbfe1788b3dab197219df2a", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "npm run lint && nyc --reporter=html --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "npm run lint && mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "greenkeeper": {"commitMessages": {"dependencyUpdate": "[pkg] Update ${dependency} to version ${version}", "devDependencyUpdate": "[pkg] Update ${dependency} to version ${version}"}}, "_nodeVersion": "12.5.0", "dependencies": {"async-limiter": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "mocha": "^6.1.3", "eslint": "^6.0.0", "prettier": "^1.17.0", "benchmark": "^2.1.4", "coveralls": "^3.0.3", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^6.0.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.1.0_1562602282978_0.037493817730248846", "host": "s3://npm-registry-packages"}}, "7.1.1": {"name": "ws", "version": "7.1.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "f9942dc868b6dffb72c14fd8f2ba05f77a4d5983", "tarball": "https://registry.npmjs.org/ws/-/ws-7.1.1.tgz", "fileCount": 16, "integrity": "sha512-o41D/WmDeca0BqYhsr3nJzQyg9NF5X8l/UdnFNux9cS3lwB+swm8qGWX5rn+aD6xfBU3rGmtHij7g7x6LxFU3A==", "signatures": [{"sig": "MEUCIHo7sRzQLp5ANUPrq0OmWd/sFUXH8S3a68o5aG9V1hZ+AiEAhgdp7Ok8j5Ua5ep7ah0WkMGs0AmvBQfGpvwmc4971ng=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdMdfgCRA9TVsSAnZWagAAAWEP/ivL7q0N824EkUmkrg4h\nkGrzLlxzjnZJ4ViNHtrfTQAUyPEb1xfTR02MKwlUwPJ6/Nmb9+3SO13obOnS\ndqG0y/kdV3Jk77giHoBNZkB+Ss1MjskaVMa/Kv9S6m12vGLAedrapzT21Fcs\nTwVFcdsw9hNHCZROx9g7pxrtSJmSRPX0bCZ3BnK3nsm2ZsJZg16mmH2HE1Wr\no0llUE60QmMDlkqLknno18Hc66gwO/DgKPPtZeHymfgqROyu0lgXiZPkxOK6\nPyooMEJcWoZuBQU/V+ZOm5+3aIEyJb1bqyi6h5VDAEBiKoWFHH0t74LMG0d1\nH3wcdiC+PLyXUdnTBLWolKBmOJ8o1YIBL9xT7JCWPdcDsxkz78SZ6Arm/gAj\nJuSsaJFLhf1vcZeMWR04lBg5yuD38/qloh42pypkUvxYFruY+IeOt7R28j8C\nc4mlG2il6q8/vXYMmkkE1bfKiFhPD72ZLD3S/uR7t2dnbzlFS0ogT2GcMC/E\nvm/tebOdLcWALktW5BGcRrqD2MuxMMnVfDVJhve/LpnpcULnjnafwnlEfNgb\n5frbAa7y81JqtfhqGa7MKQ4WayfnJ2NnOrKJnGBD+p9h0BfW/cP4XpAGoGYJ\n89xtYW/FoZxi5PY7D/GDoBlO9IcsUIh1X5VsdEU5dEp0JbhSKj63nJr1dono\nvtsM\r\n=ZWPJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "gitHead": "cf467dbed312e7dc6e7318c08666b72ea3213b76", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "npm run lint && nyc --reporter=html --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "npm run lint && mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "greenkeeper": {"commitMessages": {"dependencyUpdate": "[pkg] Update ${dependency} to version ${version}", "devDependencyUpdate": "[pkg] Update ${dependency} to version ${version}"}}, "_nodeVersion": "12.6.0", "dependencies": {"async-limiter": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "mocha": "^6.1.3", "eslint": "^6.0.0", "prettier": "^1.17.0", "benchmark": "^2.1.4", "coveralls": "^3.0.3", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^6.0.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.1.1_1563547615630_0.3154288392297395", "host": "s3://npm-registry-packages"}}, "7.1.2": {"name": "ws", "version": "7.1.2", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "c672d1629de8bb27a9699eb599be47aeeedd8f73", "tarball": "https://registry.npmjs.org/ws/-/ws-7.1.2.tgz", "fileCount": 16, "integrity": "sha512-gftXq3XI81cJCgkUiAVixA0raD9IVmXqsylCrjRygw4+UOOGzPoxnQ6r/CnVL9i+mDncJo94tSkyrtuuQVBmrg==", "signatures": [{"sig": "MEUCIEvgORFCa9aI8UYJwoFKNG5GBkVs3cKIjB6ExutbFC6cAiEAsjGj2IA2RE2OG4CbCEb40G10DRR2Ieb0tpLFiPRoFLQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107031, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdUYqLCRA9TVsSAnZWagAAjvQQAJbD1uawtWjhX1Et5uQ8\nrDp4cKDpVoJFdUslHKbK35Kgo48E9g9FDZ4UfYclBPzU4gKg5e2L/1u8XX+K\nDSrmP1QRLllkhxoq/kzK8ugFC6tO5FyUoxNolC8fdwgddMPrbPH/8y2bLuaE\n45k8dDz3zbHX7vz1Ti3Ja8wMbw9ISMerg3i+dJE4XtuM/qIudnfhE3ekFq19\nSJCaTJ+a7FsPYDM4rQR7rXhd2Br584xdIjkUF/9YFPpOx3qU9VZbF9+3kYc2\nCzMZsWAjWDiHz99e87gyWGVKuVr+nm09tf8rQ9O0GkGXNsttWoM20N6GHJxD\n+SVgKQOzfDlwcKnz4pH9PI118TGQrv3icb39If0JoNv7nbggPyLuL5M4l1ua\nDgZoD2eoLdN5HT7C/Sfci28rA+Un+DXjjG9esqUgsEaXXC4aXkkmZGjJiOJE\neI1T/rPlUle2Y3WXFgY99lP54VKR/4VjMmmh1uk4nf7Dp0wuw+wgK4rtIzq0\njY9NMcPwufO137KJVyZwZQBarnYYfEmx8ThX2EvT2P3Y9pubi5GnRftScetm\npMaCio7XFbjOiXFs0qcqsVlmXHZYQta6fpiaS5PSeBwW/+y7ILnF1MlN/cta\np5+V/e6Hw6S+aaueTukWwE9FYaSx5bed3jypEamMrTpLgzHRfvRl347TELW2\nRhXB\r\n=f5CZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "gitHead": "74bac8e592ff5dbbdda1190d1d4b209c504979d4", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "npm run lint && nyc --reporter=html --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "npm run lint && mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "greenkeeper": {"commitMessages": {"dependencyUpdate": "[pkg] Update ${dependency} to version ${version}", "devDependencyUpdate": "[pkg] Update ${dependency} to version ${version}"}}, "_nodeVersion": "12.8.0", "dependencies": {"async-limiter": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "mocha": "^6.1.3", "eslint": "^6.0.0", "prettier": "^1.17.0", "benchmark": "^2.1.4", "coveralls": "^3.0.3", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^6.0.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.1.2_1565624970505_0.3914743185094143", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "ws", "version": "7.2.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "422eda8c02a4b5dba7744ba66eebbd84bcef0ec7", "tarball": "https://registry.npmjs.org/ws/-/ws-7.2.0.tgz", "fileCount": 16, "integrity": "sha512-+SqNqFbwTm/0DC18KYzIsMTnEWpLwJsiasW/O17la4iDRRIO9uaHbvKiAS3AHgTiuuWerK/brj4O6MYZkei9xg==", "signatures": [{"sig": "MEUCIAqnQM5DhN7as1wx/5vWZ0lkA6hr34n74kUJiOWjXEqiAiEAu9LZDgR0p13jMpyU/QwZZbcMiTCOxYgJW7aiWG0yGLM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107115, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdqxyzCRA9TVsSAnZWagAAVBgP/jP+ViVBra9QaZJe2hlR\nK3rn9Du0thtxJ3fKPRxRpDDogaA8CC9KFtjhgid5yqkkvgd2FPc1ohDC4ovq\nedVVBIP6i9R1uZtFRHoY/I2VQDG2yF2RHwRmlJwe2PWvVLjlRuq1uYjrYW3s\nO5De/3xFl2RrRyV5TPZGFbKyJcPHuy91pbisYOmDD5gAYAegQgJDRvw9pYIn\nmHTu1z4SLqQwxKsyymUYMqaXRPBX2huSpxp39tZQj93q8x1HCx0/zVyiHpan\nrkOdaE4RU/CnlWGvVIu96KzI/Z9gpSYYO5QOff5KXinbk+j5r7jVN9wK0mIy\nN1wikpL61nv6nPHnHH2KK85LViFMUXBIjcAlQxoqQXmbr2r1J24cfSarWlBT\niwtMHg67xyvSf7DdmgJGP/N3Yofd28UEXYHkG7Npqddx3nWVlouYGC91OrP2\nBRPwu+sr9odXlDVXWUxjDNfdgGdB15la87uNi/nKjH+gfmNRPg3p8ZRx2c3c\nusnFJhGV8hwJ6YfYgItilEg6qjUHE7cMjUbQc0hEMwuFrD12TpdMXnYGJ+ml\nC45dYx9lsGIH6ZNTNdWjugX2AWpvjGo54La22BjHuivV2f2bh4QB3dwXo6iU\nwfYHus2hYqkWtiT8PgXG9dMX+3UxgM22uJEhHwuaXBqDKrMCf6wqlqUpmkR1\nXgZ9\r\n=A4o4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "gitHead": "65f7800d4ed5d6f2076b5bd00b3c4cabfa4d22ef", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "npm run lint && nyc --reporter=html --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "npm run lint && mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "greenkeeper": {"commitMessages": {"dependencyUpdate": "[pkg] Update ${dependency} to version ${version}", "devDependencyUpdate": "[pkg] Update ${dependency} to version ${version}"}}, "_nodeVersion": "12.12.0", "dependencies": {"async-limiter": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "mocha": "^6.1.3", "eslint": "^6.0.0", "prettier": "^1.17.0", "benchmark": "^2.1.4", "coveralls": "^3.0.3", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^6.0.0", "eslint-plugin-prettier": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.2.0_1571495090326_0.4486534033801446", "host": "s3://npm-registry-packages"}}, "7.2.1": {"name": "ws", "version": "7.2.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "03ed52423cd744084b2cf42ed197c8b65a936b8e", "tarball": "https://registry.npmjs.org/ws/-/ws-7.2.1.tgz", "fileCount": 17, "integrity": "sha512-sucePNSafamSKoOqoNfBd8V0StlkzJKL2ZAhGQinCfNQ+oacw+Pk7lcdAElecBF2VkLNZRiIb5Oi1Q5lVUVt2A==", "signatures": [{"sig": "MEUCIQCKRthf56RHTyDDI9WWd5W/UqBqr2rBo23RNnKmVuFO7AIgf58inrf2OBpYdwSrXVmQIUhWqE32IizYfjvNqzPI4ns=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108109, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd9Ke7CRA9TVsSAnZWagAA20AP/1vc9IQ+Su9cXPin6jxK\n2QPH46YhtrZHON4LYwTV2T/Fx9wNpckuw12wvZs1IWf7qiPGCs7PKLbNOIAz\np4FjvVFmLTQg6LIn88twVtKbL91mhI3FIwSB9VUtMztFOJ7WsJeTNGR/c0EF\n0WrzZ3LLxB7K+AuyOPqIWW1Kx8fJzYSwLBBcryunGmzlp3TtTzpavb2/svlu\nn73VGQdldq/Ee6+I73wBpPZi/z6GoYFj/gbg2I9Qs1a+aeU8Jq4iuNqGSh2Y\nGAaiqw9ZMp7tAIXjgFoIRAY9WlcLCKtwvxiVJAVUNaOtRkPEmP/ZYq6rfta1\nqGjHDwxvGMYHPZtDnvLQvxEVhsgv9Dr5MqYsjNRNxx2i+L5+48OztUga0LNv\nv5XthfTeV1SuA1IZ7I5KEluXgdqIH5VrqfX+W3amuUmtKUCxg/UxrqKmcK3M\nkgRnX3NpWvq3aRFDwXnPnKt/l9alChIF0gtD10p4grQOX9ycunpijVaestlm\nqMXoupKRnPs2Ox1gJyS1aFENA5/tyC2JZ2AuSomdlSvBhC79w5RlrwGaOHh6\nWHjDz8rfRlmDLLF6FfJw1P8YvEYMm6lnNM1LkVO9U2XdXMj/y26gNjKxWl3L\n8pM0/fhNshL5FoCtnxPBMxvDObT+PI2tVq6uhUnONtNL/d5dseg8e4CzyQzV\nbYh1\r\n=fiDW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "9531cd0f20be0b0cd059443c31ea4bda2670c7f1", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "npm run lint && nyc --reporter=html --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "npm run lint && mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.13.2", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "greenkeeper": {"commitMessages": {"dependencyUpdate": "[pkg] Update ${dependency} to version ${version}", "devDependencyUpdate": "[pkg] Update ${dependency} to version ${version}"}}, "_nodeVersion": "13.3.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "mocha": "^6.1.3", "eslint": "^6.0.0", "prettier": "^1.17.0", "benchmark": "^2.1.4", "coveralls": "^3.0.3", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^6.0.0", "eslint-plugin-prettier": "^3.0.1"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.2.1_1576314811483_0.598351831751051", "host": "s3://npm-registry-packages"}}, "7.2.2": {"name": "ws", "version": "7.2.2", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "36df62f68f0d1a6ec66d3f880a02476f3a81f24f", "tarball": "https://registry.npmjs.org/ws/-/ws-7.2.2.tgz", "fileCount": 17, "integrity": "sha512-2qj/tYkDPDSVf7JiHanwEBwkhxi7DchFewIsSnR33MQtG3O/BPAJjqs4g6XEuayuRqIExSQMHZlmyDLbuSrXYw==", "signatures": [{"sig": "MEUCIQCiSDmemMDA+y/Z6XfoBRnMBq54+oFgNtXedZktn3FVxgIgKiF8KDPIt/y2qL8m2vA2f4VgcGJ+CMnsVNINXVmwrAk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109439, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeZJaiCRA9TVsSAnZWagAAMC0P/1SD1epfzQB4PlEbbkKf\n5zNBhRUPCT7qcQUV8LRJYUVNEMkGLrm7CmejoGST78O7XWjAzH8blOhuF2fp\njKmiwn2XZa5YHzH4IHtD9XiS6dbYF4QPK1/499Vx5TBuvU2cYMncxzsh5peS\nRjABzNVjT1ITW/NX3A+nGZi9RnCOlnX9XAhGaPwoJ5PPfUTcHSc/UzhI14rn\nKxpEM1o9Qw0J+POW1j+QIFaFdE7TwVscXTgCw1DrYlwWx71rMOUlKoXu715i\nAWuEHjmQZAptIOQTI6+Q1NruvhHtQpKaRUT+EYZ2VfHB9PPMRuX79WEORwKn\nEu82h545KXPSL92piGP/IRPgSHG14dpcSdvvtv92s3rKJAko1gU/ZRis0zLs\n/t9Y6GFOmMIFv5iLIuwkJXgwP/UkfzFL52iXOnsVlL3h/gJipIvLKIO0w+Gi\nF7+vtIR4jDYPS3QkCI4jE61e4l0Cxfmt/y2WXV2p7QpobiamTNRFMTjXmtD8\nL/gtjrKV2PYRLxarA41qutq3Buz5MldDE8bpPxP/g5vyfPPmwkbdv7hy0dPX\noewkvmHvYma1D7Ls+lejXkkahexVUYjGofS8Bdq9K3ETjmY2Xf69awRWiZtm\nTMNahgHQ6uya+wwW0J9qCDZrFibkYcK6keXpZOy6Su376dFoiUQfxN2Ty2+T\nwxZN\r\n=kr1j\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "af4f7227a31090a69c282cd18d5c42fb42554b03", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "npm run lint && nyc --reporter=html --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "npm run lint && mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "greenkeeper": {"commitMessages": {"dependencyUpdate": "[pkg] Update ${dependency} to version ${version}", "devDependencyUpdate": "[pkg] Update ${dependency} to version ${version}"}}, "_nodeVersion": "13.10.1", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^6.0.0", "prettier": "^1.17.0", "benchmark": "^2.1.4", "coveralls": "^3.0.3", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^6.0.0", "eslint-plugin-prettier": "^3.0.1"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.2.2_1583650466015_0.39667904587810177", "host": "s3://npm-registry-packages"}}, "7.2.3": {"name": "ws", "version": "7.2.3", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "a5411e1fb04d5ed0efee76d26d5c46d830c39b46", "tarball": "https://registry.npmjs.org/ws/-/ws-7.2.3.tgz", "fileCount": 17, "integrity": "sha512-HTDl9G9hbkNDk98naoR/cHDws7+EyYMOdL1BmjsZXRUjf7d+MficC4B7HLUPlSiho0vg+CWKrGIt/VJBd1xunQ==", "signatures": [{"sig": "MEUCIQCRJQwwhy0esPGqeN61a4XIfUhK672AsZXn1z2HXLgBoQIgGUmEDie4iPUOIdVecpG9exhg4tsVuGGXXVgap0NZwBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeZnyFCRA9TVsSAnZWagAAUvwP/jAz5ASUvvHJjDMOS+r/\npDkuV2MoMwz6ccV+qfbVRWUKq9DqnK7bZBApyNlzV2I+APDqLOVU+2BazNKh\nqoTcVHYcBYe5oBIYUvEeSK2qyBCNJUcz2f79lVxWagdcax5nl5tOcW3B8v4X\n9T2hSvSuDNz8vg0uhCnLboZ+F4SPXd3lHwwfcZFkNSjTcRog4W4QNQTEaPbi\noqPawUw/NXDJMFILS2m/rerFnEyrDl4VpoAS9GyitCE31g22N03bCbsmaUMo\nHmFmefATEhmtIExXMDW9JGLL9FtTwVEIAJEfAWqjLt3JeAuWHvYKKd7rU/y1\nwPUQJxwGM1HDYaxbDLXRBVYkOja3YHB5txB8d2lHLt98c5DQ/lZCz1XQIRao\nPv48dpOX2Qg69IpMzSHktoRUJpt+0KkLWUhb7XGVY79kCgqt6ZqYxBs7coae\nOeReUBaKc+yuM3k45sHxyqiKCsw/qejgzScPnmU/BaC32vEdjqpysRNDtfZj\nNmizo+cLoiO8stLVCHGBwpIxegM8aGj3/C8emNEsSFi4rXT6LGHaWvNfv3FR\nVY2/4QZFL/ddUQ3yAOKF4znsHBY2qfbpPJVnW7IyxZ2h39HX3PpITCcYDPzu\nj4LV/wXFESBSre9RANqfPfkJC+A3k0V8eAB0bYQJKImeUAiqkOkYMyrX+2Ea\nHUeL\r\n=aUaH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "5fcdc42ef76d2d0ab920aa37631468e6f1066bcd", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "npm run lint && nyc --reporter=html --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "npm run lint && mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "greenkeeper": {"commitMessages": {"dependencyUpdate": "[pkg] Update ${dependency} to version ${version}", "devDependencyUpdate": "[pkg] Update ${dependency} to version ${version}"}}, "_nodeVersion": "13.10.1", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^6.0.0", "prettier": "^1.17.0", "benchmark": "^2.1.4", "coveralls": "^3.0.3", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^6.0.0", "eslint-plugin-prettier": "^3.0.1"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.2.3_1583774852485_0.5683086237203219", "host": "s3://npm-registry-packages"}}, "7.2.5": {"name": "ws", "version": "7.2.5", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.2.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "abb1370d4626a5a9cd79d8de404aa18b3465d10d", "tarball": "https://registry.npmjs.org/ws/-/ws-7.2.5.tgz", "fileCount": 17, "integrity": "sha512-C34cIU4+DB2vMyAbmEKossWq2ZQDr6QEyuuCzWrM9zfw1sGc0mYiJ0UnG9zzNykt49C2Fi34hvr2vssFQRS6EA==", "signatures": [{"sig": "MEUCIQDSmZSXeQ2c0nehEvX5gBVwRN/lFY4NXUFUT37AkkY0hAIgYAhvP6QBNMnT2JrSS2TvXGsyyIrAE1phAnWqiyutg30=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepBaJCRA9TVsSAnZWagAArqAQAKUTurNgpRC/zpN3XTPA\nQZhkGYq05pr/ztJ8dJpT+G7dx8hgh3sPMPyiQ1JPp/EoJ9xHMj4RBin43iQp\nqxQyWbSZHYgF9fB2wF5kFkWcxfFs46KGi/rfv2/uBRzaYRQbjXSXyexS1ZHI\nTUp2ZodbreXOrvkUOFPxlEAG/Mj1BLpvxpStsnj1DQi00q0vJ6sgiMwKZVND\nsucRMU82gc8j3Fk9GzKEETG+Vf2QotccE0XMAciodpTH7eKbP9W3mUzx2xlZ\nwrmrHtr32NfPE0IbHY6ARamolSJkGThLTeA/vFgbEwM1eLmOazgu23ib4wcl\nLRcJoJWguKqfZvxTwm1wCRioBLsWb++D1KaD68aMg+wxkPlIf8glsSmZ1vXf\nKe3Ru6w0fSflhz68gZXE9Csyo53HzkvrMQTrZ6HAkoMccwVV2v/OnLjvBEdf\nGwPbmXKfbqhI5gPyz4cSP7h1zSW/oVkhr33nn09Er0i+BRwHoQ/8NB8buHNb\nA0QLvgOwyCN05FNiOaSeB1lskTxNM5NhCV4ukTXixtd9epzIiqwIEp41NgCJ\nleMmqbEstxc8l4igGZcClaEq5ReKtwWH+coTB5pK2rcQ+l2XYiS1ddBZM+Ha\nJFIkofhETLQ30boJdu3GNex0dbR2FNf526MuvEkaQLyf7WVrstLXsYd6nbju\nTRda\r\n=ev6E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "67983cbba428d3b55c6f97d4cf17d29e32a113d0", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "npm run lint && nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "npm run lint && mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "greenkeeper": {"commitMessages": {"dependencyUpdate": "[pkg] Update ${dependency} to version ${version}", "devDependencyUpdate": "[pkg] Update ${dependency} to version ${version}"}}, "_nodeVersion": "14.0.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^6.0.0", "prettier": "^1.17.0", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^6.0.0", "eslint-plugin-prettier": "^3.0.1"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.2.5_1587811977237_0.15290584517613803", "host": "s3://npm-registry-packages"}}, "7.3.0": {"name": "ws", "version": "7.3.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "4b2f7f219b3d3737bc1a2fbf145d825b94d38ffd", "tarball": "https://registry.npmjs.org/ws/-/ws-7.3.0.tgz", "fileCount": 17, "integrity": "sha512-iFtXzngZVXPGgpTlP1rBqsUK82p9tKqsWRPg5L56egiljujJT3vGAYnHANvFxBieXrTFavhzhxW52jnaWV+w2w==", "signatures": [{"sig": "MEYCIQD1jybklUOl3kNUoqnAMOAAOJM0YbPlZsaEGfJqwJ/sWAIhAL/TsKzW9/SJYx7k1qmMjdGSeV6alQwjMGBFvE4xDTbT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109903, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJet5LqCRA9TVsSAnZWagAA2l0P+wYqJmHokCLMTvcLA+Q8\nJJSXoNNThM2fvd6n5gdzNQdgH2q34vYX2DAPF7wJGrj46BlIBDAhn172qSIs\nOYOM+CkUIAAeXm5KWKKxTrXkP+BBk0R+x+bWey5LUnfsKc8NI2PxNulb12hw\n6zFa+FdES0t+BJ2mJ7Ye23kl/gR8VVyTApeCjwWZ4q2j0UzcZbJmtRPAbok5\n36R8qwgD+mvrlCCQKPXnnCjnZ+dg9T5HuInhHKR7pexW5rlA39RAxgc1kmDF\nlL1FOQnykw2omicRm+EkrNHiLa0zKB77QmLOpxoycHjFa3jKT/o6fVgB4XmX\nwduH2/4u6mskWsOSe0URKNMhu41cZWUo2EK9bElVNJl4sKSNL4QUKNv18kaN\n92yjm0udd8An4sxE01/G0ZedE9m0ddHDYLSxL8C0DHkgxAqtLidvADqWdoa4\n5S8Ohx8PyUS+QXw3jktrBSkHN77qzD3/Scb3uk3Hddb6919DsCZCdzkuMSgA\nGUpPKMInPd1AZ/v3oZULU4rdAOy7mk4v6dFkqnW6C/NsBoCUTF6cm6ZJdL6g\nQuOFNzwUwVpMJppybugnub802Dmf63C6P8pPMdQe9QWs29suifFAlX/U0grO\n6n0qweXjqtf5kqU13Rb8TFH1AqEpmWOq5LE6XirUtOdpLtYbsLalM5Df9GXP\n4erP\r\n=HYUw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "41f5e4ff11e6447250dc0860336e368276ac08bd", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "npm run lint && nyc --reporter=html --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "npm run lint && mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "greenkeeper": {"commitMessages": {"dependencyUpdate": "[pkg] Update ${dependency} to version ${version}", "devDependencyUpdate": "[pkg] Update ${dependency} to version ${version}"}}, "_nodeVersion": "14.2.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^6.0.0", "prettier": "^1.17.0", "benchmark": "^2.1.4", "coveralls": "^3.0.3", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^6.0.0", "eslint-plugin-prettier": "^3.0.1"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.3.0_1589089001862_0.3504831019713297", "host": "s3://npm-registry-packages"}}, "7.3.1": {"name": "ws", "version": "7.3.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "d0547bf67f7ce4f12a72dfe31262c68d7dc551c8", "tarball": "https://registry.npmjs.org/ws/-/ws-7.3.1.tgz", "fileCount": 17, "integrity": "sha512-D3RuNkynyHmEJIpD2qrgVkc9DQ23OrN/moAwZX4L8DfvszsJxpjQuUq3LMx6HoYji9fbIOBY18XWBsAux1ZZUA==", "signatures": [{"sig": "MEYCIQDjVn7pbwkPBRL9bm2kL6XPY8ss3+R96yl4MNz5BReeWAIhAIId5HKyk35hLcOxasee88mlB6MODTFAhaef5UuyIezm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfAWWpCRA9TVsSAnZWagAAgcoP/RLI81NN2qDopAbD2FZ2\nNOlPxJdiy93XpkApJUObOX733GOAVuqzaahSCbe0aZO40yWchy0+HDSKulTw\n0Myz5YFQcKQa7QNCjij7GpWwmUsLOlPHicPnI1rD0oijtZEqxWexkF0EVXTi\nrYCvgT6h7DYZ073ZCrexoKiPDDithT7GfdcuQY/Aoxl0ac1DXPSMdT3jAB8L\n8riRGm3/HgPecINFe8XOTSaqHs+6wKcwRgp6dH+JpRYv0uEGJ5SLGsbj/qb+\naoV8BBCGYdAfFCjsfh27bnIwYQBr2O32gOGo/yPPxituQvvg4MNIBN3s5QZ+\nJYTFG/fQDJR4velQQqIzow6n3pxZUGQojFj+kZjcAwt9dzc+7D0VGmjv7AXM\nrD4gX8eIMoI2ykPQtk926wlKbWiHm7zZ7bOYFBsDF+n9kFU7P2whgWeYu4ra\nXuPmOsWxdrZ+ZGQujg91duVRsBksN6z7/T5+VgiThAnfGoUlltusrdri2B2s\nwOl2lu1p8FuWlVMBMSS/pgg4lZii5axjRGvAOP5gjlPM5QHKAvjgdUzayIjm\nJWYSBibuPz5fmjmYLQkiOmiymCjsZczJC3UjKJgqV7kVdg67yIhOPgTSOCDQ\nDuhpFnh0EuNC71FoeXWDuQY0hxXVleTtsQw658CRacvxKSsX4hWCg3YuVotL\nFyMZ\r\n=fX1K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "d09daaf67c282e301eeebe21797215ddffd819c5", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=html --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "14.5.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "coveralls": "^3.0.3", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^6.0.0", "eslint-plugin-prettier": "^3.0.1"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.3.1_1593927081429_0.2702926620323156", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "ws", "version": "7.4.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "a5dd76a24197940d4a8bb9e0e152bb4503764da7", "tarball": "https://registry.npmjs.org/ws/-/ws-7.4.0.tgz", "fileCount": 17, "integrity": "sha512-kyFwXuV/5ymf+IXhS6f0+eAFvydbaBW3zjpT6hUdAh/hbVjTIB5EHBGi0bPoCLSK2wcuz3BrEkB9LrYv1Nm4NQ==", "signatures": [{"sig": "MEYCIQCNmcx2eUicQ48v/RDr/TTV/9pKI2gNVvNWsUEtIHrlMwIhAPkWOgZcaubHrkuIWYZPQhUpHLq4KPEoy3lbcx4qComL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110918, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfp5ntCRA9TVsSAnZWagAAiMQQAIFi7QQZ7YXN7qO3u5rU\ny4p7TNe+yShxtLCtiw6zmJ9aaRBYTAE2B3t0YykLv+oxH87IG62upWzLpef1\nHB4ocRqwiOIwxgGFfDo3JRp7MH+440TB65OCTr/MeVE1NbFmejOYpUY+ui43\nPmSbJjCAlIghtDM1wMFw2PUp9WK/TtA5VOq+VWxqmaMT4qHWv16lNzW65pP+\nqaaQKnkeK/2mB1yti1Sac9QriY6E7PW9kKS3xCR1LrnDYVH00V4ZDIRGEBOp\nkgjqTX+FFVpci8jdjtDh3iB+5O7tgRnCezqrZj9i1b4ANyVn5c90sQGp2UDg\nUVm8jIXHK07IcA9Zu0XWiCDrC1bgurIxJ2puiPFk+qE098dXPxPRnhnQh4kx\nPKtkzySynCP2vQ+WuyicylZAOYA8Wy/5nTx/6PrbV46erd+YnRk7mQArAkR2\nQ4IFWC0zOvRZXsp4Bf9PJKDjWNUtlpzDs1wFrjbAwcnIaJwd3Hzzkul/o58o\nMUk1N6BtZEiVpZxfwyyaZ7y7PPy7N9x8jAyDSPrWL+8s1AlxbsHGYiuWX01d\nebKkUylEj06o3rlLs0ZQC0pcZrkP4qP+aPiigZVPPytGsMK7MfaOEH+xLHQC\nT/zFQEB/jhJB5c1HS7DP9DCYF5mmLDjaHf2ZKvXEF02S+j44BXhyIHAO/wL+\nSGq9\r\n=E9jd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "eb36a63183bfaeb130eb288d8e1374533cd7dfbe", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=html --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "15.1.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "coveralls": "^3.0.3", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^6.0.0", "eslint-plugin-prettier": "^3.0.1"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.4.0_1604819437028_0.51538579100163", "host": "s3://npm-registry-packages"}}, "7.4.1": {"name": "ws", "version": "7.4.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "a333be02696bd0e54cea0434e21dcc8a9ac294bb", "tarball": "https://registry.npmjs.org/ws/-/ws-7.4.1.tgz", "fileCount": 17, "integrity": "sha512-pTsP8UAfhy3sk1lSk/O/s4tjD0CRwvMnzvwr4OKGX7ZvqZtUyx4KIJB5JWbkykPoc55tixMGgTNoh3k4FkNGFQ==", "signatures": [{"sig": "MEUCIGn0tGhtSG2U4Z/GkPG3YtB3+kc49OuxcGnFBC13+PNKAiEAo+lRygnB4XwX8IGPHxSues5Boc5jDZRzaFRF+KCWQqs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110923, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfyqCHCRA9TVsSAnZWagAA+GQQAIs7fk81l7J1T2qfvXcB\nwfRbhmwbR4MCn1T0GoBkH7MqxUE44M5UA+g3HRb2G9zLnAXABLxVUVuN9/vy\nL85mGhGwsyUiU8gz9O0WVR1xSBDWwoQyl5ZJhCnaBfxcfUoauzAODVrQ8Zlh\nmutMMFKnbUnGNex9MI4gnctB7cTpXX06bi4H3+fqIgSQ/Qa3MDNY4dtTEo90\nspzjgdoDzhn6AI8zTqQfPa+urGYnY6FOdCLn9kuMfBJrbqRqayUDAynzy0pL\niBAZyFQJuPk4wi+9C73qrZjnnj9gM+uwi/ODYr4LTFceht1TsedBHM1q/5NP\nxGv+vkJ0R0s1iBF/OK+BL7iYkUmKsm/W31kJHuSJRLsxwYKk+4/4yKdkgg3e\no/S3MYy7Pct3Y919jj+lWp8xqy1Fx42BrStn391a71zb1z3Xy//uebu8HQF1\nnmv/b5yjdQ6iZC1xCYpr4leoWzYafVqBWMaFXqmRjyG2gmpgtGCZp3gQiPNR\n+SSUdCUJYVVI61B9GY8tiymZROGXqcLIfsqWiyKyFV4rTRmYhVqQ7fWZI5ka\n7GMqToo2QRke/BhBIYhkzEBAAFw5LqkLo3RlyunEpTtb/D5LcLI0B8NQlSRj\n6wVNqptMuArSAWUc1PAAPfGSHVc21nPrBKcW9FaB8aHDc6HZ315/UoGjLzuj\nANH2\r\n=UHxs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "c171962844e1862cadff27804700e00e2f2adbf5", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=html --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "7.0.14", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "15.3.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "coveralls": "^3.0.3", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^6.0.0", "eslint-plugin-prettier": "^3.0.1"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.4.1_1607114887043_0.784404948081022", "host": "s3://npm-registry-packages"}}, "7.4.2": {"name": "ws", "version": "7.4.2", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "782100048e54eb36fe9843363ab1c68672b261dd", "tarball": "https://registry.npmjs.org/ws/-/ws-7.4.2.tgz", "fileCount": 17, "integrity": "sha512-T4tewALS3+qsrpGI/8dqNMLIVdq/g/85U98HPMa6F0m6xTbvhXU6RCQLqPH3+SlomNV/LdY6RXEbBpMH6EOJnA==", "signatures": [{"sig": "MEUCIAwLAi642rlspuM1xL5wZJT9UzbiysbUWhXMdIDb0iF/AiEA9k/KckL9Gyc1GEkpVr0erxML/adW51RrbtR5Mhb18/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf649bCRA9TVsSAnZWagAA9Z0QAJoHCljY0H+MxDdsGKFP\nJtW0ZGHDRw0pDclhO418teDmCXDtGv2IMkS/VSFW8a/yJUOzb0fapBkLNtil\n0BBLXHXbZ445EKYQVZL3UbhICBAJrT+91+8ei8SLcJ86O658TUAJ3aZJO5T7\nFGWw8KvvcLUYVhctYV0vI16AOZn9eiJ2h/dFO/3oFJW6cQ4PpIjWCYL2t05b\nYqZbsxDHV1aOT2MuU4umP7zC0mvHAutYbB67b21e9mm0MVY3TjQHuailfx/9\nDK3vmHR7nvJzZ2Y4604jefA0/3YmThE4wQaSJOhTl1py7SPJeAsDRnBbHnuV\np4KdpVeFdX5MvTh9ch9jztDcpUpFiDAVm85SYDxtDt0rAmiBDbUefdNpQeY7\nqw7az5FE52E/qQcN+JyTMsj5mslgI0jfsqbm00wna371zPX476ZI5tiuMEyv\nVHaDSIY3ySmjy18G5NmErqU9FIcqzcJ3rLQsmMra953HQ9Q8fVO3rCgafNEe\nvI8qfaaOW/46BvQy1TIdZ75XAEXGfRyXi4iCAnhzGzzN1wsQxYgCZ4Z8dcjI\nuNxM+2FucOWNJs2Qk1zA7MckeU/URUozDcorbfmX3TgUtAVYWYKu2HavIqvc\nMmwatuzxXsFfxJLb2WqpQVh38Iz5mjci4UScL9gbUHEwYvHhNM4go60+5uco\nhdzx\r\n=uDD3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "d1a8af4ddb1b24a4ee23acf66decb0ed0e0d8862", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=html --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "7.3.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "15.5.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "coveralls": "^3.0.3", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^7.1.0", "eslint-plugin-prettier": "^3.0.1"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.4.2_1609273179091_0.4467771632695996", "host": "s3://npm-registry-packages"}}, "7.4.3": {"name": "ws", "version": "7.4.3", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "1f9643de34a543b8edb124bdcbc457ae55a6e5cd", "tarball": "https://registry.npmjs.org/ws/-/ws-7.4.3.tgz", "fileCount": 17, "integrity": "sha512-hr6vCR76GsossIRsr8OLR9acVVm1jyfEWvhbNjtgPOrfvAlKzvyeg/P6r8RuDjRyrcQoPQT7K0DGEPc7Ae6jzA==", "signatures": [{"sig": "MEYCIQDeVvbfmcAeCbOyl7nYTk7Y2Hn2fHz26bab7G4/8GRx/wIhAK0RSJ0+pfhJmukKZtuEq5xGhl9UnGQMSZmP1OEPdoP7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110849, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGaZOCRA9TVsSAnZWagAAdAcQAJklfG4Oj7D0vG+j69sI\n9SmkgGHCffychd5LwyTccWOPBP7gr8CujKBOdAAJTAA753yo3SmNN2pv8gmn\nn4inrdBDr987SUZ1HLMA6C+n+2zhQhq3cDghaZg4SM68aOqtElFcGFYvEd0o\nmR7Owo+Xx7g69rZQt89HozFarSsKM/rciuI7QccOZvA7AHTMN2on+NVVNXq6\nuMCAWUyD1HwhHP4DEJOvU8NZ6xwgr/Ekb3Sn6r3eHxzTMwcgvjpZlTutEGrQ\ngrSYbju3I6zIB6o8BxCjxBZR/BoL5u1t1HKUH3j8VtInnniNQNOB5tQPu7rz\nWVd+TdTsYC5XJVBibqvkdKyBr5k9t+zhx3o6Oa9Zn766chR2ak7GDyXKLtsM\nqV3mvnU6dNlDfCnQ8dtlktp2go3AXJH2fXZWYoDvfGES6aB7c95h93oxpUdZ\nqSn3rJfNLBAY1RaV+mSuOfT2xL0W88xj8Z+LbWgZsDMgOPVCRaoFJZNjBJwf\nAfCTPHADZZZgtmc5zWvTcYzjxInA0mVU4lXAEElf1stHQXbeC5eSLlyDGel4\noTwm86q3m0aad5EMgEpXf0jbmy1PxJqcMKXxGJ8vkpr3G9wRelIE0MaemyuF\nZPNZfL7sr6ngVo2YmUUUg3Yzm1TzZIXIuBniKPcidwtnf4ToKEB9dp9KtLiw\nkwoX\r\n=VZbe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "223194e5af389d1ab8019010cd54baccb79f0916", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=html --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "7.4.3", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "15.7.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "coveralls": "^3.0.3", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^7.1.0", "eslint-plugin-prettier": "^3.0.1"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.4.3_1612293710015_0.6965529435353632", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "ws", "version": "7.4.4", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.4.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "383bc9742cb202292c9077ceab6f6047b17f2d59", "tarball": "https://registry.npmjs.org/ws/-/ws-7.4.4.tgz", "fileCount": 17, "integrity": "sha512-Qm8k8ojNQIMx7S+Zp8u/uHOx7Qazv3Yv4q68MiWWWOJhiwG5W3x7iqmRtJo8xxrciZUY4vRxUTJCKuRnF28ZZw==", "signatures": [{"sig": "MEYCIQDjv0wwR+wUCRVMW9KdQk5npcUGVgqYmGBMA5Dv/zRnVwIhAMuso8fTmlQLJX1xztRnCxMT9UvscZZHHPaCmsEpqVTK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgQ+pMCRA9TVsSAnZWagAAZ/8P/1b6s7XR/oIUWaDIDp+4\n0kzWV/sOtdGmxkBClaVe9nnb/2XJAAsEmn/+HoBhfJC4X5efzS+qArb4w4Rd\nx+GvyKKsXh8CnQxaCSjfD4PbbHIrPy3OskQhj4056oNV0yKYy0+5WaujkpwA\nXlXKHGAkPa65MK8jw5CfkoPlR/aPpaZujHc5+/bB4c143qYm953PpIbXSqMI\n9XBF6gjTQ7l2A44iXoXe8pyRDE529n26hGb32JVzaqJXyzbpJRmaZ+gUozD/\nWrfmFMcNLWnH0G+qejKGQWRwiF8qwSLpSIB9COdsAk05CdTJUwNKtY3kzTUC\n7GDVocxJk6s/RteDJy7QLiOe30QAUrTy7+RUX84KzbmrKWB5z9tAxrB5PxS6\nhjlTR/1MxxjWtd2UaTjpCWqyRViFku4tqfT0SyvMTAkIEuu5lhjpl4jfRltj\nNU1wDURKuppb29cWM+VqUKeKKKn2KmufvrRA4QXO/aRJumuwQDEsMlFvWZ7x\npEq0u6T4pz+f7bHhfuaqshJmmUeyRCBDEqWKDWPTFyDE+D7IRTZrIm9KOZyq\nRlLC6Vz+HLgI4/sMnEc8sud7CE2wnzQenRrWBFQKaxb2Sx35iQvRhbOYaEB9\nhm8662ktA7ulNp8Y3sJSbNFk64Sy1D4sRJ5UfwRsLyBIq2AKkfJnhbEOLTnk\nUEi6\r\n=sACG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "a74dd2ee88ca87e1e0af7062331996bc35f311a6", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "7.6.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "15.11.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.0.1"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.4.4_1615063628360_0.11837430243994396", "host": "s3://npm-registry-packages"}}, "7.4.5": {"name": "ws", "version": "7.4.5", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.4.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "a484dd851e9beb6fdb420027e3885e8ce48986c1", "tarball": "https://registry.npmjs.org/ws/-/ws-7.4.5.tgz", "fileCount": 17, "integrity": "sha512-xzyu3hFvomRfXKH8vOFMU3OguG6oOvhXMo3xsGy3xWExqaM2dxBbVxuD99O7m3ZUFMvvscsZDqxfgMaRr/Nr1g==", "signatures": [{"sig": "MEUCIEBycoww3amaSF+2eosoKlsvWQ5Gwt2kfgL2lAlled5xAiEAkKPa/cVCPyeQEb1vHIeQrH4AXsoCtPn56GboU6FwCy8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113125, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJge+xACRA9TVsSAnZWagAAO9YP/2/w3EaLUACI5er1U3l4\nxt0f2RfokximhGzjmoOYyxFFS6QIuZSAHagUrUlH6wodYpLz+aL8R9QJGK1F\nzAFiULT8uoz0DPc5r8+yt1gYEszUoVouo0RiQhpslQScNb4zBFLqrucSRf/b\n2nd757uOY1DBUg9F6XAppZfZO9NPJNOTkKkgMHY5C2DWzmHnK+2urlwxIa7f\nXL+Mv8Pp4aOlwwQO7fz+6vny3VJgNbjgfAC8uKZWPVVj/KgPL8z3eghmgqZw\ny3IV5Ou2r4yniiQr4CLD2GH1mL14HGUWax/WVT/aJrbTpo7/4nJpsTyGdck6\nOXF+jZieJk3543LCULlVugAuoWX2EzhcSdl6KHVKvyn8+NEtPYvErIhGLLA1\nGaPQMDGjSZf7liCZ6XDuyQcevg8TSMRi7zkl7hmiYbNhEuDFoEshSzgQOQS8\nSw7Nbyt6L2oPmgiFq1QN6sKpShrJr4sY+w36GafTwRLP3qxjrBztA8HvZmHe\nLdWUU9DPKkgGdyeLw1bE/+iL9VbMIEn67TK+DT1YGWyZpj+adS0ZVUDXT0OO\n0lxE65FB8+70GcY+AFt9E1d1FUWprkNTWbVj/l59yDGK44yQ+wrYn3yccuRU\ntIdXAVt3raoiy0C11nVR02LoCi1qkl1+Ea/Roxy9NXZwSJerDXoLmppqwWpx\nhKEZ\r\n=MzsC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "f67271079755e79a1ac2b40f3f4efb94ca024539", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "15.14.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.0.1"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.4.5_1618734143851_0.052866789201809095", "host": "s3://npm-registry-packages"}}, "7.4.6": {"name": "ws", "version": "7.4.6", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.4.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "5654ca8ecdeee47c33a9a4bf6d28e2be2980377c", "tarball": "https://registry.npmjs.org/ws/-/ws-7.4.6.tgz", "fileCount": 17, "integrity": "sha512-YmhHDO4MzaDLB+M9ym/mDA5z0naX8j7SIlT8f8z+I0VtzsRbekxEutHSme7NPS2qE8StCYQNUnfWdXta/Yu85A==", "signatures": [{"sig": "MEUCIAdW7M0w8/ZS0+gC/o+qmosGv14j4FPnzFofTjs1PulwAiEA1JMZ2nM+lxhvwJvq+M01Cw1VlLRz7C/m8Sl69VmxiVM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113359, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrSYHCRA9TVsSAnZWagAApMoP/ig+CqLojND9JmG+Ibgm\nBn2/SHruCtrieAZ6plCAU3KxW+f18U/leb/LxcD9vp2yy0qX49RVUiG/ImPi\n0n66dfg/w7G+WXKD+/Br/SICinTMyjvBtDMCsM6KXDeJTx8w837JBtXay0qW\nJ+PUyzT0XrlEdgWFakcfLUExfDbVJPhu0UvAS3bl/98kxGNiXgVoWy0M6rJb\nNznvfXZZ2B0XJhzFhVtgOn1fJZkRFMURfhyv5ZGNgKDUB5JkUdyWUkXBkrhP\n3pkpJ0RTG5/SINWDQnS3A4Ci0Wp1BjAnPj9ZRUNuT2pmhQJvmsQx6nwvu5GY\n2X9kZttj9SwEFB14Di7SDwGumCxWTmvjU0mn1reQ/pFbeXBCWBDeP4yD/8Kg\nx7FUfRD+xD5WVi1smbGOpcx2mRFaWqBVQAyis3bHYHVc/lIt+eYzopQjvcTT\nnid6hAwYYpZaciYyIMgDm1Cc1aD/T3hmDh8cDz50ijX9jSFJ/kQxBKZeIpeV\nGxWYDrOP5YtYrgidCetT4ITtza8QS67uGhal7Ahju3UvY5AUuzk0JSOfIxXv\n3aQtu5/hwukifYkRLiZkLI+ydHNULfJ8xk2ky11Ws25Pk2ahCo9NYhhY9KZ1\nTlxG5E3QBaSAwFyCnFHf7pFXqXyDPf0OaH3egm2ufDHxgPy7/BQtNPFMkH6H\nlCOy\r\n=QprN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "f5297f7090f6a628832a730187c5b3a06a247f00", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "7.13.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "16.2.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.0.1"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.4.6_1621960198610_0.47702363242698165", "host": "s3://npm-registry-packages"}}, "6.2.2": {"name": "ws", "version": "6.2.2", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@6.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "dd5cdbd57a9979916097652d78f1cc5faea0c32e", "tarball": "https://registry.npmjs.org/ws/-/ws-6.2.2.tgz", "fileCount": 15, "integrity": "sha512-zmhltoSR8u1cnDsD43TX59mzoMZsLKqUweyYBAIvTngR3shc0W6aOZylZmq/7hqyVxPdi+5Ud2QInblgyE72fw==", "signatures": [{"sig": "MEYCIQDTi3OXyBRVRRDbt1DkzzQvNJvHj01yrckBeI61CKkihAIhAJ20N24tK5ntCd8n0EL5JZEQVtwt2nYggGUWmIgr9pVY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101735, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgtn3NCRA9TVsSAnZWagAAxFsP/jSEBOvCrZ5dtS/nj0ua\nqsCbU/Y1wqG/M0edJS6b9hM2SgKdmHPH9byqgrBs6SlEBSi8v0H0XEhvUjd0\nW3SNXps8SA0j7O1v6Sbb9YbJm0RU14Cq0gOngxJ/uad6XOOwCSsQqV9FfA9R\nnzx9QBXnJVks+q/LS3qDJ2XbvBDD4nA+YiIPJHu9nry21Z+lC48lsMQinLVZ\n4pAvCHAYNzgUg0J6B0qQQ+wc+i9Ml/3CRUmZMJ32h/yx2zzdwQ5zR5PDl3xb\nKhROfEE2kybahk7bYMIwBCVNPhe+WeoOKBJlu7V/Mzx3x/RnsSLUu6pF2alI\nvNfb4cS9B0ik9p8OdC9ULauDVKwHusfoxNjtmDFqDmxgh0zEnCQidhaWep0k\nwg30GmEkx3lzGWgjOJ8/1svW0TFVUUojGvQQc0qmgM+9Rn2tqcWkdV+xNm6A\nymtt9ZDpPPFBPmtipqbcRcLU7JuLM9PUz6xqRBCYyerBc1aqO0/oIevFuC/F\nifB8ZNPVvVwUJq2gz0KTkrnqpa1GDvaP4abFihpuXeodhN9QEF1YvCItdoAp\n4kWSOpVVpLfmJAxEgtHhkCKwfYJEP8lf7mjS+ilnkeP0cKO6Aq/N693fnH1W\nWE12+K2HXSmpWzoyKiFyU7psS9afANp4QbQsVh/mhQKSGRyIl4wmU1emJT9b\nv8eQ\r\n=1LXy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "gitHead": "9bdb58070d64c33a9beeac7c732aac0f4e7e18b7", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yml}\"", "test": "npm run lint && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "npm run lint && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "7.13.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "16.2.0", "dependencies": {"async-limiter": "~1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "~13.3.0", "mocha": "~6.0.0", "eslint": "~5.15.0", "prettier": "~1.16.1", "benchmark": "~2.1.4", "coveralls": "~3.0.3", "bufferutil": "~4.0.0", "utf-8-validate": "~5.0.0", "eslint-config-prettier": "~4.1.0", "eslint-plugin-prettier": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws_6.2.2_1622572492507_0.791522012984119", "host": "s3://npm-registry-packages"}}, "5.2.3": {"name": "ws", "version": "5.2.3", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@5.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "05541053414921bc29c63bee14b8b0dd50b07b3d", "tarball": "https://registry.npmjs.org/ws/-/ws-5.2.3.tgz", "fileCount": 14, "integrity": "sha512-jZArVERrMsKUatIdnLzqvcfydI85dvd/Fp1u/VOpfdDWQ4c9qWXe+VIeAbQ5FrDwciAkr+lzofXLz3Kuf26AOA==", "signatures": [{"sig": "MEQCIBtcPR5+wys0mYXHXRSl6dNiWBYXmiGsUo7uYZDICOphAiB91O21RThjPF2mpU66CllEvVhEqQQKtNct0oGIGySGWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99505, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgv8SPCRA9TVsSAnZWagAAriYP/iSXFrEyNi301FEkiKq+\nhLVkPJo8P/Zf3PntFH3C2XgqBuTn0pGyv3tJRqqRNUp1jOFv7m4kRlMjRZ11\n2HNznOgiJP35pr8yT0wbvzczUPMdsVzMbv5HOYNbNVjveE3oBS8PhJZMnXVG\nbcJOONpbh8qO8DXPzQYJqG7/mYxtC0XhogNR95VxZUUY8SOlByuPjhEEmDsP\nE6PjKAvbP/xcJCNYJ8cc9RXK5XwjtFk4PchGoFsuhmE3pVwsM1E20L+ZTuVf\nC4Mvf+pozHSymBjB2CEmffv0OLJOvM7l8aj77D7OEBnrs3aJyQ1odPFEsNg4\nQfuytlNgIExuezwxOWwWO3lsuzdn0sXOylYkbChbuXfiX0a2qxMO2daLMlAn\nLWQRKAqkROet5X4lGrdWx1iajakaQmkJolmWkDVyBs7JROh4P+zkx/UHMqeG\n/amMdrtlPtgR9Ow8VIHmXw5QHWHvJ2tnSuvekl4s5ZFxjvTbOBBiFq6SRmLd\nrSiWPpCPAUIIMe5uuszk/tJCOSlaP/OUXE2f/85Jr87Uq+HMLkDAu4G3XwuY\nIByPQJBWHB3epxTF/iZ2t3/C6iMnieUOyvlbT9KV/p0RQdXTN46G6srQUVox\nNTIKAdz89XG0RszYTqdZO95FjQJKADagD7oh4Jijw0ie3yVp9Z2vxgPupYB0\nw8CB\r\n=NZr8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "6dd88e7e968ef2416445d8f8620c17d99b15c77c", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "7.15.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "16.3.0", "dependencies": {"async-limiter": "~1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "~12.0.2", "mocha": "~5.2.0", "eslint": "~4.19.0", "benchmark": "~2.1.2", "bufferutil": "~3.0.0", "utf-8-validate": "~4.0.0", "eslint-plugin-node": "~6.0.0", "eslint-plugin-import": "~2.12.0", "eslint-plugin-promise": "~3.8.0", "eslint-config-standard": "~11.0.0", "eslint-plugin-standard": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws_5.2.3_1623180431646_0.26332762869067117", "host": "s3://npm-registry-packages"}}, "7.5.0": {"name": "ws", "version": "7.5.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "0033bafea031fb9df041b2026fc72a571ca44691", "tarball": "https://registry.npmjs.org/ws/-/ws-7.5.0.tgz", "fileCount": 17, "integrity": "sha512-6ezXvzOZupqKj4jUqbQ9tXuJNo+BR2gU8fFRk3XCP3e0G6WT414u5ELe6Y0vtp7kmSJ3F7YWObSNr1ESsgi4vw==", "signatures": [{"sig": "MEUCIF/Afx47YBSM2vr9aPTKYP4p4kBJ9F3vlFF28K23Y5v5AiEAt+A+z4XIily3tJM0IpJ3FTTCVHlkrVuDJh1ug2K4NAE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115608, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyfoUCRA9TVsSAnZWagAAyJ0P/3ORyWoErN1XZkKF3tpT\nq7Qp94bcbs+v78nvhwODatUA6DfHpiuJJ9P9i3qVgeyTlZd3kc+1VeZt0yMc\n9FFPlv/CposeEfYLyCMuTPj62utl3kZqzLtCNnVcHLxx7XWZhBtuBO/18hiH\neD28Nu+vT8gpUxhpBTBJcNR0vw2jCggK6XRKvGQcMVpmyoYadY/3HQ18l4Hq\nxA64QT+IIbDtkYX64Sx8zW6vDH/GDCP+CpKYf5gNooR7PPosDKdpSAMNXVMP\ni8ocO+qXoGAIPeZgg56a66YTgTe+VoiJ2/2JLWvHBv+qaVbdAd3ttzxxYse1\nVgH7uG2aarVJDWweEgRSLotOTt1Hl4vW+tSWG+5EMKrT+u+GDkbplzUgZrr9\nnS74FD+2B/fFGwNu/pSQPl1nxPiQ8iFEfcu+Mp2d1EJNW1cmfjcfAm1DK3Yn\nhyyoVe2ay8ELcnATkaasIbKvd8Qg3buDR0b7eg9PSFEDYcp5TPHz5y6F8r1G\nV764k8Nkx5agfvSw00tNJmLo5HdzNX9hG4n4hCWN9Lw+ZnIKQJmx1BkELui1\n50cwkbET9mT0+uDy9EsuxE+MLEMbnVzeJBodxfD3OpPz31kPffrC97o23cnU\njHp2Xcxk3UOgUCGgGJKN2YWwbpAtAsiQxaCYC7klzgcLBVPmBH5FlZFyKnRy\niZHA\r\n=BIkS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "e3f0c1720aab640fe78dc578907046fb84422ccd", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "7.15.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "16.3.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.0.1"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.5.0_1623849492132_0.3037056566843295", "host": "s3://npm-registry-packages"}}, "7.5.1": {"name": "ws", "version": "7.5.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "44fc000d87edb1d9c53e51fbc69a0ac1f6871d66", "tarball": "https://registry.npmjs.org/ws/-/ws-7.5.1.tgz", "fileCount": 17, "integrity": "sha512-2c6faOUH/nhoQN6abwMloF7Iyl0ZS2E9HGtsiLrWn0zOOMWlhtDmdf/uihDt6jnuCxgtwGBNy6Onsoy2s2O2Ow==", "signatures": [{"sig": "MEUCIQCK/r/JChKe3yGE4E1IMU/PKSUiwKYba4vUnFidCK+X2QIgeQ+Cjnm+jyy3d0Zo6g/2Z+1fm0M+2vPwJKA8jn425LE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2q0GCRA9TVsSAnZWagAAPSkQAIleFFQnQ45sqsi2FfF9\nwwFiYUt9mt8+HsdaCoTRapbkViJds9RzxWtc9XDuyl1dEk9TgUBf4w0vVqy/\nKctrpjD/KoXNDtV3+pEvzmukhqrKRgxmueG5B9ToRG6nxN1DdrD9z/bneQwt\nIRpa3SyF6lr8DNojjM6K556CWjr+T9oOs2SOeAUWc7wEIynIy1vLBOR5k8E4\nF70yMxGaMMMIdxh3BfhTFF8rhOklI9dJQqzMOj01A9mn5iba5ZL1BdUrB0Rv\nwar9IV260fdqAAQ6EcbSA21OZzXKEUTJvrj1KLsIg0CSaBWdaOmSQdMa01iI\nCmdq5W1tNXtNLokpLlaB/6yKLvAkAGEAcoqSl3rTe6dW3XHWQnq7AH0SXLMq\nbvy3ZnH9gwmcU6hBLkJMUpzja0qed0H9b/VuDyjmXfFhBz2nCjyzzrs1L4Sa\n4Yyx4mMxbqj2ENJvRHHxODqAQ6q3GE52/aG6q/rpMch/nS7T25E4ZM2Th3n3\nawIr20e+5GjyKbqnBI1zLZ05D2oui8CzfzJURauYFDJXz11VNhDfn2gJ0XkC\nl6ndsEKtOPalQP/e+ogHWWUtcWRGkH5vzHn+w8PbVq9duFqOIaqa/eyIMdwy\neFAvngz2HmuNQZ/6EUQe6VZrbmXc0n02Tu0QvxmcNf72HlJcI3PKmqguGN5s\n860J\r\n=3M7v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "38c6c734daf8e15d5cd902ed3e47b8651fd1032c", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "16.4.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.0.1"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.5.1_1624943877906_0.5268756377222341", "host": "s3://npm-registry-packages"}}, "7.5.2": {"name": "ws", "version": "7.5.2", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.5.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "09cc8fea3bec1bc5ed44ef51b42f945be36900f6", "tarball": "https://registry.npmjs.org/ws/-/ws-7.5.2.tgz", "fileCount": 17, "integrity": "sha512-lkF7AWRicoB9mAgjeKbGqVUekLnSNO4VjKVnuPHpQeOxZOErX6BPXwJk70nFslRCEEA8EVW7ZjKwXaP9N+1sKQ==", "signatures": [{"sig": "MEUCIQCA1hsdDFWNBC0Iqtmu4bzowoVKciHz+OBN/JtZXZcGUwIgPdV8Rjt9pcBL4MH1FCzcnKyU0Hpn7FP2uBASUJKqSuE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 116644, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg4UkFCRA9TVsSAnZWagAA0iEQAIlkR/J4Vo/HZ89VHT1M\neOpDeGDq6HK2n8tbNG6g7wR10Fr0tebTQHfPohU4G36m3fbYDJC7QBL0qBIW\ndUu4SBJu/Kt/f9GjU8c97ecbzxyZCH7ZdAgg0+evovMAUqJXxV0gZTxK6x2H\nYsBHnrUN8aq7nOyUI8O/UXIbiNGYwal0I5lyrranoNdSoVC1A9HR4WbIuRMQ\n2YrolJ/mr+WftsvNCUduuTe4vcyZoURgBU8uI/0SwWzGor+cQ7n9gGLIrjA5\nKhdnTTelob7igi1eLSHafZe0h6QhguzUtbKOkgVpmdeFJAzp2WR8HCXc+xGx\n6rxxMBew047+lWTPWmyVxf2OQ5YpMFWXw9LeSM5tnmYfzoxpH+eZC6k+wc34\n7s/PfkL+bkJcezahTY8Jh9NjfFKjsyTt7zhhnqLFN3YMRWEuHKxtVNnIP0/g\nHF6jJNrp5mGCWSYBSkoJYRH6GL7+OWyE2rNbibMskNYtHJgnLVA5fuBL8vHa\nU4rKbrLwnCod3KOqg7ZB9CfkcUaKDFQv1Zc1qNHtXNA+7jIahTy1W8q1ToJ1\nDTLsk6WqRt2/6Qxty/LjGAslCgJNJWpYwaCVg1Iw2Z0m07N5cglQFqeKHpnq\nGvtkwwb4LOF2aF9VEQiv5AQ99LV8IWf1jNTEEZGs4XBEmbcH8XXUaFzlIw51\ni4F1\r\n=xNca\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "0ad1f9d6a48ed1b30bda09b958cb142c1e09cced", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "16.4.1", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.0.1"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.5.2_1625377028703_0.8476967779524347", "host": "s3://npm-registry-packages"}}, "7.5.3": {"name": "ws", "version": "7.5.3", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.5.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "160835b63c7d97bfab418fc1b8a9fced2ac01a74", "tarball": "https://registry.npmjs.org/ws/-/ws-7.5.3.tgz", "fileCount": 17, "integrity": "sha512-kQ/dHIzuLrS6Je9+uv81ueZomEwH0qVYstcAQ4/Z93K8zeko9gtAbttJWzoC5ukqXY1PpoouV3+VSOqEAFt5wg==", "signatures": [{"sig": "MEUCICMqAt7uYKxMDMnIV5jBnBj9JmIZqcWc0eoeR1rLliFdAiEAjLP9nG3mneKmaTkhVRYrYuiHlOTpmX5OKYpOBuBfi4w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119149, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg6TfGCRA9TVsSAnZWagAAuKAP/1vfZr7o+IMf4ruZTLaB\ngfXg0ooA3fsnrHcpB09FcCM5FFIIEPxIuqM/MxnLsD7d4bXsFCGSof/ZelRr\nLcSesB61rkCgtoJe4C/HSYmo6JhbcUAUx3OHGbigcq/HrF8t5cO75MwlNQcF\nBoC0X3xyuDnOBxeNc3Vvp6oPGHVCvFD59kpr8iP1ln52A+K+vYiOPakvqqqK\n6AswbcxzcvrtpRNGFJw7yfP1jRqVUAznMNEI7MgbjwgC6bI//YZ9WyRR3xCD\nxpF6wplId7PcPTKcVMRnaBh/+EYqzbsx8kH6QccSppasTQcOubUlVGfNMK9I\ntBmQ2zWLsBcBZpfZ0mkY5D+B1HBfMIIp5GJf6JJuZypPET18Z6x53KnlBsP7\nZxL39SBbM3QAB2Z/X1RIbGNNabkuDBE6xo8eV1r3jL9fU8HrRZOSw+n/MO+A\ncP+IyzQVRtJXkcai10p33dhMyFAmj7wh2S1WOzftsqsfRiymGDbMmCe9bj3+\nQPi5qaeOGNb8rXiOdaKR3kb/4lqNs88cvyLbqK4LmZQY67nyJiEjGdYH14LU\n/WKW5oev39EqXWCFc+5MuEE37IKJnEDiZCzPT5uinlMTMl8tvmQpTqx8LxOF\nniqY/VUUZT1+7gWUQNB+jtn/vDOw+/8ekk/qfXIZTv8bsEpfgy9crygfquoU\nZ+IA\r\n=SlIz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "4c1849a61e773fe0ce016f6eb59bc3877f09aeee", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "16.4.2", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.0.1"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.5.3_1625896902282_0.6213850556865725", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "ws", "version": "8.0.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "550605d13dfc1437c9ec1396975709c6d7ffc57d", "tarball": "https://registry.npmjs.org/ws/-/ws-8.0.0.tgz", "fileCount": 19, "integrity": "sha512-6AcSIXpBlS0QvCVKk+3cWnWElLsA6SzC0lkQ43ciEglgXJXiCWK3/CGFEJ+Ybgp006CMibamAsqOlxE9s4AvYA==", "signatures": [{"sig": "MEUCIEibC9XNkO3OpkRScMgoQYQDEKGKhyG4jcG9dIqTJTVWAiEAwwuanatrubXeofSsnmbN97yD9uWSYOhK681xvhgOr5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 124665, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhAZ5QCRA9TVsSAnZWagAAt0EP/iC3bsZsKOv318w6zAZP\nIrTS2tkbvN8d2G3aOU+THJziW7PES42Ce5Emw1Xw0ispamulAJMjbtoRT2Ya\nIAgAyW8dHihn13Mxvahvnvdd6sAdP7gYoVy25HPDo9hVi6M7wdwLEJrh6yrD\nemLxgOtkubOAaqe/r0RFbBlvn/SY47s7M0f9DDRY2CgEZ5zYbukxFonLCumZ\n0yo+WeWHihWFy5wg+3j5g+eWrclc5GJ5nzI1nBdJZq1aHDyDoJcPeAtEogbx\nx1sMNabDrqsGSoPVr0Fa8mPmR1cw3cB7jrsHXXsPINZ0izH7SA8kgeKSU2VH\nJPqo9E5yaM4eCLuXiafsnSdFQa6zuN3iuZFluyuhsf2P0J518dza6HuHBrsm\njDbSUlgTWJQrJY2OOGmQ6gtCupLa35yesiLr5ma4uB16xy5CgoJxgqMhhU2V\n6r9UI8qTB+xCWZBMypFCAcxbCgjkY1TQ2XI0R6iuWa3ONoqPUbCmNiYPZ7vt\nC/W1HKglFu8RAbZNYYlPZIUTNwVj7n4DeGJFaxPuNFVHPP0On83HnpjRNsbS\ndQawVLmMmDwzruBZfnK2SUW8lbwmttLGd0ODok5XkKNzwjlWq4pj4sNdPV4/\nhbiRvsVwGFj396tJxtbz9NPvSBKdlbe/nV94lqU4PV1hPcpI+Ear2eSb+31S\nFJjQ\r\n=jh+0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {"import": "./wrapper.mjs", "require": "./index.js"}, "gitHead": "bba3351287870c7328f4e4219dee3b242fbe2eb4", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "16.5.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.0.1"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.0.0_1627496016618_0.8665221851470684", "host": "s3://npm-registry-packages"}}, "8.1.0": {"name": "ws", "version": "8.1.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "75e5ec608f66d3d3934ec6dbc4ebc8a34a68638c", "tarball": "https://registry.npmjs.org/ws/-/ws-8.1.0.tgz", "fileCount": 19, "integrity": "sha512-0UWlCD2s3RSclw8FN+D0zDTUyMO+1kHwJQQJzkgUh16S8d3NYON0AKCEQPffE0ez4JyRFu76QDA9KR5bOG/7jw==", "signatures": [{"sig": "MEUCICLnCu6hHyFxkp+PuYCVkXOebeJwpEqplJ6DL6t0p5TXAiEAycLxQd3BLsgB3hSCLW05PKxgvsneMhmW/gVbaog/P7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFC1eCRA9TVsSAnZWagAA5poP/07bz40yZU9d+RsGm2cs\nGontw8EfC3HoDNlQ6+Nx2vzYHAMoQZ3hm0wPCQPw8JyM+4SnbO/Vgfq3sI/0\npzU+K3EB+oGeTauGha7b6K15m0ZfV7SsQIQYKWIjgYhrNtyIK5nTUsXFJDxS\nOsYj/dKfwoi3CNS0tJHAH+ngdciHNkWdtf9tYDfrE4L7qEXSG6kHv6D74xY1\nuCn0lZt+DH3PY6/vljm9I267xa6a25B8q0tGtrnOjuooNRpo0XST4NaUqpgf\n9uXjFYR91i+eL9OLeUjOYav/3bMypwdl4gDjQnmgH11v7OWcayEpF0lXbJUV\n+vFJ3I2dR/6HCgEwSAzOVFvLR97D3R4PM7ne/izqaoAx9Z7FG6KA4NNbMiqP\nXL3eUooxIHKb4ZkMzUf2foTZsvJ2NL6g7ArRUKwXCItdtIaMzfsK2U78qKjP\nTHFwglO3LUv1ioP3Y1xC04T4hJ3EAOTPRoa7SYewgv+fo2DwMwQFwLzOi7Xl\ngvShMPiYaRcA3B8xKK3BJ2hPU1hpk/t/bFGXsBAVFgDdbxYh/g5cEJw+qw9z\ndhptR2EAJ18WJcrYutnHGDeDuycSyV2haFzk3hhvC9wnVJdZWw4BNwZmSdgk\npr+iSZGj5I3FI/wS00Vl+3bRKR/F2u5z9Smwnq3szUs1MPFYG/FD1Su4Rgn0\nMyJI\r\n=JiDC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {"import": "./wrapper.mjs", "require": "./index.js"}, "gitHead": "142f0911b550f85741297f68f33af0dc72a7f043", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "16.6.1", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.0.1"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.1.0_1628712285665_0.9608081711341085", "host": "s3://npm-registry-packages"}}, "8.2.0": {"name": "ws", "version": "8.2.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "0b738cd484bfc9303421914b11bb4011e07615bb", "tarball": "https://registry.npmjs.org/ws/-/ws-8.2.0.tgz", "fileCount": 19, "integrity": "sha512-uYhVJ/m9oXwEI04iIVmgLmugh2qrZihkywG9y5FfZV2ATeLIzHf93qs+tUNqlttbQK957/VX3mtwAS+UfIwA4g==", "signatures": [{"sig": "MEQCIDJiR/rZfLKRUOqrIaXGWIx6TOBzRCoicjaNF1A9JiOFAiBVzWtAd67z076k91oFnsPcgQ8KpMrMSOHpR9J0BVZuJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126064, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhHJmVCRA9TVsSAnZWagAAsc4P/A9FJsRjE+AIlY6IzJ4L\nuaPyX0+bdeD2x8oYSvktNu/y0+aDUVKQqZOJVV2NlBLqo+BqAaNHo9qOVIqN\npMB+qBCwJ9M+PCwvT1tuQr76zkT6zLB+v5n0PMIhAczW6tT6RspEIRKTrl0y\nqD8066RL9sWu018zuwSHB0DHWxDCepWAWWukGtMfZEBH0SyWIfi/v+fgqaPp\nIUE9K4H2W52ywsOpuUasTkR4QgbZ8Bvlyoq+Js+h2Ko4OZTHOA9CPwSeMyJi\nrqnfgUQPz5FDAt0GKGuGvZmf7A11RhEBc8c9OCsFjW31tUycSHALYsFXo214\nndAykUwwb7KsuAA0dz4B1gBkSMnZABXHvfELKnTvEHveURio4eUnIeTECaCd\n8+PFKPQHnYkxEKvP7JKlWvBY8NkBWABycWlwLj6Sct9h4gtzQCcgtw4czudM\ni6LdnSx6Aww0OVZgY46Ousf/9zkFvZsYOJnY7cIOt0v1bYjh9KZVwQiaVz6M\nZ6+9ul9n5p/H5S3Qi4mEgWOEIUAh2A+5NeNyOljc4Kmdj6Pi6FBgMdwK6YLw\nQZ8NJvvlgleUqdooImT0o4vwsbdCPpwy0YuTioKrwc8ugS6e8TA/bZazJBxC\ngqUQk10UwoeheAdFa4GQptGLenxeSJzowgnKDkO3jCMzevekgLEi/dOyIk6H\n3l1t\r\n=kdYg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {"import": "./wrapper.mjs", "require": "./index.js"}, "gitHead": "7647a8920b6a7ada107c28be68e4e82393dac893", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "16.7.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.0.1"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.2.0_1629264277254_0.6106707469445793", "host": "s3://npm-registry-packages"}}, "7.5.4": {"name": "ws", "version": "7.5.4", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.5.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "56bfa20b167427e138a7795de68d134fe92e21f9", "tarball": "https://registry.npmjs.org/ws/-/ws-7.5.4.tgz", "fileCount": 17, "integrity": "sha512-zP9z6GXm6zC27YtspwH99T3qTG7bBFv2VIkeHstMLrLlDJuzA7tQ5ls3OJ1hOGGCzTQPniNJoHXIAOS0Jljohg==", "signatures": [{"sig": "MEQCIBZOxBWfriOiIm45RVuWVX144uzTSsGlpYkxCVl+VCwjAiAt+rF2ba/honpCbnHsfh/2zN+1oNFsycTm/v9xrfqmfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119751, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKl/vCRA9TVsSAnZWagAAj5MP/2tunNGgjYWR+U2cK59V\niA9kgz5UoyCrmjdXlHno9Z0jddEF/fvkAWCUfEHEnV2M9jsA4LE/7zKOWgqa\nJStzCmuBA+HQbFiTCY5tI9gFO3pJKyCPE9Kb9DAk05zxKLChd5CDeeHO2P+Y\n4hfRhYyERI23TznpEv3Z8CiQM6D0hXowSw3SOyklhrPScNToSv9dG93fbfuM\nRQSL3zmCO/qJgJada4RoO6NO+jaQitUe3VU4GxcIzUU4nUI0F0wOEx5rfLtN\nQLU6VjhiT8q0/XtmrZpP3NjjeofGB3b5oeJj2W1p5WW2sMcnYd/2wzojqNW5\npgH2ohS7yhCxi3ZT8XfCfjhugcPzKJB6yXOMTtPFrvxWqQofaipjcRMLLDGO\nCzSYchKXFZrlw6c1QpiXJIvaugyqodAhpxxipvwHbrz2vN/kG0qzCGQFLPQq\nHoZJnsHLIA5t7htze93N3WJaFHTMyP5ozVOKle5Qynp+g7eSyanBhD3Ss18v\ng1a7YffBDt4WoBLMnPLwHtYhgea+DpWepu0jA+NejdDs4EeEU2NR3Bz+5Sbh\nQ20skz6OpGbv4j7C3Cc2ZNpJLKRtQBIzRM8FCFtWYHeT7S4D9V7vSNO20VN1\nDxJ+eo699LCMG05SllHeDCrwApQ5qKWCIf+WubbFyJrx1gNkb1f3zCvuN0bU\nYpF4\r\n=kisf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "075752d2bb8dcffa0254da5c407542f9f155d0d7", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "7.21.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "16.8.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.0.1"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.5.4_1630167022900_0.07004249270339136", "host": "s3://npm-registry-packages"}}, "8.2.1": {"name": "ws", "version": "8.2.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "bdd92b3c56fdb47d2379b5ae534281922cc5bd12", "tarball": "https://registry.npmjs.org/ws/-/ws-8.2.1.tgz", "fileCount": 19, "integrity": "sha512-XkgWpJU3sHU7gX8f13NqTn6KQ85bd1WU7noBHTT8fSohx7OS1TPY8k+cyRPCzFkia7C4mM229yeHr1qK9sM4JQ==", "signatures": [{"sig": "MEUCIQCO7gf+EjFc75ku+l/Q5cBJTqny2Vft7mlz1i7lzYGqdAIgMD6z1D2TO2/yH61hXhGqIk2yG/+9W3/vPU6bV18Ul2c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126501, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKmAsCRA9TVsSAnZWagAASEgP/izVkrkQDn9K/PA+KP8d\nTTIZM/sn+KeoNKQNfQADT2dUb2NU9/98zb9tMkCLmk0vCWFuF4b/Yn59px+8\nVyBth3jtnUH2jnnWS8a6sBhGwNj/l2Vmt7oAnqdeRsWm3vlkz2ByQHAVlFEl\nxVl+/wsikjiQJU5/K4ULRxYg3BziDJBWeacQ/ZNTiW6YwbRsH/wFXBIT3O9a\nAO07NqshoqOIuAmVkZB3HJXVI8j5/1Eu/oCYu9Zt7SayNO5yd4XRV/zCnDpo\nT0RLHPIzObFyQY4evJ1U3gTLhV5O72kgspF7KIrI0Ghb2jqdF8aiLCdnMoxO\nChdgpXVEC1QQPkvHPrRem8YgH86gTeGezug2uWsw1flRZp62QBgRi1Yg6aLu\nsWLU4d3gspn2XMt9PJ6hiNaoND1jRG6fz46eE++P9F1NTxaO7iR2YDmtLa54\nnd0Do/AicZ0SdsZpsxKLTFCDyECimlLNkwkLsdqrdU0okpq7yv5BOqm6/yoK\nRIls4tneoU0NgHKna9XjFcJj5GbVnqgPs5JxFHIj63FZrw08O3sP+h6Oh8xF\ndyaaZ8o5HSjQ6O7g0YnL79pRy9ouXfAwd1lwc3/KftfAqq1KXQ3X785PP/4b\nDxVVh2P2HvXxzPei7yyzVCuaQ1/0w0blePLEiPc+kAzbSnDKuDBlmzYMSb9d\ngdUe\r\n=k2S8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {"import": "./wrapper.mjs", "require": "./index.js"}, "gitHead": "cc7a7798b749cf263636abcba4ba19532161c3ea", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "7.21.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "16.8.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.0.1"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.2.1_1630167084662_0.7302790861939714", "host": "s3://npm-registry-packages"}}, "7.5.5": {"name": "ws", "version": "7.5.5", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.5.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "8b4bc4af518cfabd0473ae4f99144287b33eb881", "tarball": "https://registry.npmjs.org/ws/-/ws-7.5.5.tgz", "fileCount": 17, "integrity": "sha512-BAkMFcAzl8as1G/hArkxOxq3G7pjUqQ3gzYbLL0/5zNkph70e+lCoxBGnm6AW1+/aiNeV4fnKqZ8m4GZewmH2w==", "signatures": [{"sig": "MEUCIQDtc94LUvzDRd2Bp9TH1aQwEdtabxuYOEVzO6nd3tBbygIgCCqG0wws14hiBq2dvd6OFNEojQXFFDLkNW+gESTg3EM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhORPXCRA9TVsSAnZWagAAVmAQAJyaFTxgijG5O3tL0DT8\nzU0quq1ka9talN6Lkl4oHdJrmO3Wy8nn0WtTmx0yxxmesq0uJHHjam0WF+eg\nEXmOraOSIz4Q7Rjp5WicwyNM9kVje5UYKld5Vl61g1oBvgMweTFOc6SlAvK+\nnIoDxb8+XMaR2uTGejJfxL2vXnpubD6XQAmLQAtOI/54+V2WeUDOJKB5J5XQ\nd1rGr6HiVd0e5I9juquJaZS3uTPZEIkXlIFTJlfp3CJd/abGPk4+oQABuRf3\nDyrm9C6lZfC7fIAK79wVnLhMt6CJDkiQTWB+7WAVjAfHiwUl54iBhT7O2BiO\n6crfz00/hDXew35oELUMRz+3B4frzhpLFIrGCT9m7RXBX9O927Qy6jJLX10w\ni4BTrHvsdWqknDIqbHZU53S94fLnBgDx1kDS+E/cDW9ZmSS0m+8owY0ts/36\n3eRdNXOoUyBsfh12thalE1gKjiDZ2TPkUjJyn06SuYUTy3aqK1WcxAi+wk++\nC2aeeMgNsgPxlazHmWgWn5sDpIEAhUmG7DmYa6+oYeh1XkDDY1legpJs6TMv\npWAnI55B5BOA8XG6v1wHlrw/sKe0NprHT5Iqg5gVtx0EM79PzkWG1fr6uql9\n+K5lhwwjOvxXfDvJoSddxSufZbmzT96XwT5bgg/aUwTdrH4LHf5fXmutXMQG\nHnJZ\r\n=SKQx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "2d968a6d85f560620840f85cf580eb7fa9cd2813", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "7.21.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "16.9.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.5.5_1631130583619_0.26216107858395765", "host": "s3://npm-registry-packages"}}, "8.2.2": {"name": "ws", "version": "8.2.2", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "ca684330c6dd6076a737250ed81ac1606cb0a63e", "tarball": "https://registry.npmjs.org/ws/-/ws-8.2.2.tgz", "fileCount": 19, "integrity": "sha512-Q6B6H2oc8QY3llc3cB8kVmQ6pnJWVQbP7Q5algTcIxx7YEpc0oU4NBVHlztA7Ekzfhw2r0rPducMUiCGWKQRzw==", "signatures": [{"sig": "MEQCIHYNyKw9EMPx33q2LNvPyDCdGYNU10wzMT+vdOm480xpAiB79yw8kOQK/ZLSMAMcfpT2KaeM65rXEkNgFwRz1mEKYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126490, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhORPwCRA9TVsSAnZWagAAkhQP/3cooXTQFuSKXJraGmQ7\nYtmzIQwbsq6lMeyjPdYvPNcZzGH9u0z4i5rK6/S1ru4XvvUr2LK5jL299QaB\n5UGaMqWuSgLAX/1t7MFTlfRniXcJYYYVdNGhmS3VQG1cXv+E/VKVgTOk4bmy\nbNIVvUCnWHFvBskOWw51eGZbDC6Wezl4ruMV3gCeb8vzN5ph+D5ZXaf5eEXS\nqRdxA9cL9g1XZ214uSFegboPpwsVSCL086EhGZUzJ3xrNbQTCExeKtnG8waA\nN2P3Q5AlRc0DrFoIyT2d3MBHMxLcOsfGAb4w6+ueauScgNGY5jCKFZW0ZgVw\nhderiq1fUypnG9bSVt///FyvovHgJTEPsSFLoF7S62O2X9+a4whk1BK3F9Zl\nxWihocIG8rdaphhoIDZ9l/3dSsjsipPFHrT2f+IGJOSD30kz4mYJgUPRrtZO\nm6yY0QGrcGNry/fQ6sgbzvHSIxy5npZ37tMxA+2a4RoejVM+IfUVGmW4KO+m\nhfXkK0tXc7tih6VQKLl5NO+L0NNioo1bbVM0Z2o3g8/KJuAc8cvT7aaw+kwM\nfKUda/ktLMt1fHQFyg2w0vAHDhDLwKBRziC0vyZByqASb/P8UHrvITnLoaly\n4M+05GZ3aay2YQX8WHIil+3kDj/Fa/+2zecpVnvs9wwf9E6/fPufC6iVmdHK\nqgs7\r\n=n2WY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {"import": "./wrapper.mjs", "require": "./index.js"}, "gitHead": "72296e54cad6b105f901b82174f86837b4bcd414", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "7.21.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "16.9.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.2.2_1631130608407_0.4325140295070782", "host": "s3://npm-registry-packages"}}, "8.2.3": {"name": "ws", "version": "8.2.3", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "63a56456db1b04367d0b721a0b80cae6d8becbba", "tarball": "https://registry.npmjs.org/ws/-/ws-8.2.3.tgz", "fileCount": 19, "integrity": "sha512-wBuoj1BDpC6ZQ1B7DWQBYVLphPWkm8i9Y0/3YdHjHKHiohOJ1ws+3OccDWtH+PoC9DZD5WOTrJvNbWvjS6JWaA==", "signatures": [{"sig": "MEUCIFGO2y1efxHLvGP/mL9UVldyrS1cxFkYkWhe/9Zcx0nbAiEA8D1VYFctnDdQtY37F83uhFQLHfNGtuUmjSdI+yH5vMU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126725}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {"import": "./wrapper.mjs", "require": "./index.js"}, "gitHead": "cfd99b6309d59da5c35c4087520b480ec060cbd9", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "7.24.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "16.10.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.2.3_1633200009823_0.047061142143574264", "host": "s3://npm-registry-packages"}}, "8.3.0": {"name": "ws", "version": "8.3.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "7185e252c8973a60d57170175ff55fdbd116070d", "tarball": "https://registry.npmjs.org/ws/-/ws-8.3.0.tgz", "fileCount": 19, "integrity": "sha512-Gs5EZtpqZzLvmIM59w4igITU57lrtYVFneaa434VROv4thzJyV6UjIL3D42lslWlI+D4KzLYnxSwtfuiO79sNw==", "signatures": [{"sig": "MEUCIQCda+IsADdfdaKKyoeZJy/KeNRiryHzmAn8TXmGiJj4ogIgEhCxAaUefcgF/u7hH/1bcOn98KkRTDoJdJEU0HF4ABk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127471, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhnSxlCRA9TVsSAnZWagAAhVgP+gK4xHQA3dO1Jdu4kpjZ\nEq11PtBr5ptouBEPVVT1EpzSTOv+bp+/eTjecHacBqx+hMbmtJhe/vKMy/Z/\nBakO603titw9JYqEN75fmQ7Qnxi09gi+vd6c8ZBUGozYIx5tyinRFfuIbiwe\nwMuQmv9TrvwSWFC3efy4MCJ0CqGl/SI2+Nf7UV9NdikjqH6VVCVOcB5k/HAV\nkdNLlh5gEghEZy9KwNkB0lJyijwSHxlALnnsq3bTf71lbbOd+rGX4dMR0zcx\nskbf9R3udJO9NVto8MtvKSvGr2KAfc9jGrE8xDf1TYbcWw2n+9mkjleguH+s\nyKmKwk2mBwO5kdmpKGtD5THWmfA4vVR1dDcU9/GjRQbh/Bwg3lLrDxUWGbo2\nAAVi90IeGvTQJpMCy85TzQiflpofBUCG9WpDtzDdaW63wQm2XMUAot/1iE0z\n6m2m+nDDji9b4cJP7cHn3d/qN4noEsKqTLlZcz9DKkpnX0mqHwnYyFc01uzp\nFOEvxvLbrzVGMLYccXWijdnrlc4kjp5y6vHlpxT3nAlymoiBbc8yX5xsvToZ\njj9YhE4opXi34VkGJGJRsY5pxh2CPfhbhgxNloDaCsdq6HuOadRQv9K3aha1\nOoMN91X6k4+SEyuK1t5s+ABh8BFYIzjUCD53olUimtEmBkJG5pymp8FHkyC0\nUEyT\r\n=PnkN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {"import": "./wrapper.mjs", "require": "./index.js"}, "gitHead": "c82b08737fbe142dd910fc7e429399e23b95c6d6", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "17.0.1", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^8.0.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.3.0_1637690469650_0.8528558438719476", "host": "s3://npm-registry-packages"}}, "7.5.6": {"name": "ws", "version": "7.5.6", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.5.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "e59fc509fb15ddfb65487ee9765c5a51dec5fe7b", "tarball": "https://registry.npmjs.org/ws/-/ws-7.5.6.tgz", "fileCount": 17, "integrity": "sha512-6GLgCqo2cy2A2rjCNFlxQS6ZljG/coZfZXclldI8FB/1G3CCI36Zd8xy2HrFVACi8tfk5XrgLQEk+P0Tnz9UcA==", "signatures": [{"sig": "MEQCIG7QdrREedpzVCZbN+09jyOql1WySC4hAI0DDqXgqCDfAiBF7U4feMo26SfiXORqarUK53khz+xB9+rm+18aSpRh8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120280, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhnUTmCRA9TVsSAnZWagAAgBUP/iScvpdpvXQs6B7gUq7t\nWCxTBf9f5YmZkTH0Cnf7pHIu9rraZjcgag4WAn18elOEI3++N1WmdkRXV19l\nLFifi3tkVypV86pX/4rcMFq/gIpUJqhfnKWr5lE41ywXVj54A4JiyIB/CmEL\nnAnjNnkIiDmBwSpcpRB6m74bQ3mn7NdInPiRfNhz2KGUb4+qEm0j6fKmeL4k\nMfobjyXfWySBAHxgHur9Y/1hk3KOam7ODzRRAn4ZqmUer6IYECVCfFMrxRi3\nj7SVnh7JfJpMR+VWU3l5NTzzMGCD6ZQXP/5h09J2WF3mjyoNzoFnqtywGIZx\nIuI1at9/asni1l2U7qx6dlL+nyfvfDrMgTRuDgCwHwGSh2hJMGNQ+typ9az3\n1y7Q3tgfPl9Hrp97nFdCFNEKGSzGGXtwvluiSlUMp85ST6xDKQ3Wy2wbzbJ4\nu+h9Hy0oX9BIPoXyFo+tsSVVBUMyCsjgy5RXDWpNV/TJlgTt2VMgZkPggh2W\npcB0eH4xTkmbjJB4t4nmoPGRRZVZSBWBCo8+z9azfQZWlDnobt5VsKVb+dpK\n1R9DAQ0H5AD9voOkEwLo7G+2jMFB8ZWvDRBhZaawLK4/B4HNdHEKG8uphQfF\nJemw1aBqd6IzYiNP5MyqlpliDl7IzO7OrZS2zUn//Ka5v579/vdTDcTnISNf\nkN4n\r\n=Xl0b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "8ecd890800dfbe210298438ab9bb8dbe328f3e0b", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "17.0.1", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.5.6_1637696742368_0.019730101634330977", "host": "s3://npm-registry-packages"}}, "8.4.0": {"name": "ws", "version": "8.4.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "f05e982a0a88c604080e8581576e2a063802bed6", "tarball": "https://registry.npmjs.org/ws/-/ws-8.4.0.tgz", "fileCount": 19, "integrity": "sha512-IHVsKe2pjajSUIl4KYMQOdlyliovpEPquKkqbwswulszzI7r0SfQrxnXdWAEqOlDCLrVSJzo+O1hAwdog2sKSQ==", "signatures": [{"sig": "MEQCIEMKxjxjWnXtl20opFhAjsTzzJBTSnfJG4ZKEJFUefEZAiBdj7SW7jqNHhkLdUnO4XviZHU0AgLzJff3CllnxhhXyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 129225, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhwOPzCRA9TVsSAnZWagAA5ZoQAITeFY6k+3fSzno1Cuem\n18Had/dw/2gD9MfgEjRIWCVEHfMlDqdof5RmvgH0+JI4lgO8XEmgQchq9Q16\n5PkKFgBq9gKXPChdmqdNKJegZTJDzkj2Q6CKFTzuux0xBpED5APvbDZxI9gI\nhVccmMSRxWzhHG4WMV1bH7aKZKuAEqi6eBHay/euel5tOwj4ipiDDL2sZvBI\nU2XbEY5J7CJF5KMHIPx3KND+IwOgPDTxfgY2L97TCmqhi45SHQ9R5Uo0iJLP\nPi0Dp+vMDZy9KlcpVsN4NnHaMupltHzzy1xBj5yfFVETK0qKVrWFIdPy8ojg\nbGgDB2t1VNJlWVxYk0L6pKJx8bePbbNl4rcmhfnrLc1kAtUOSJTAQ2m/bP/h\nUm5XfynPGTiK3lZbPQhXi3fSCeQHYtH0QNlO8IgTOXBeBbiEmhM6NV4LjjqP\nyBGInxLhLX32a5Os9EHFxEhGi9RO4HZVTHLBR5E3qCqU9LVAOZzSWpQ6EazT\n46uYncPf/WWrVgduCRybAqbsiAYoVKUCGgOb5Qo0Qd3QSzh9VUp+dJAz4kva\nuepBo8qUdRG/hT2J/glooS6/i5/Wfo5ezdrLVOtcZgfx2pMtV3muVmNDsK8W\nXfzZdfq+vnBxFYgyNTcwFvUwYwHWf8NGcj1e4vhR9pnk/nh4m1l0otRGmBqT\nhuJT\r\n=30hx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {"import": "./wrapper.mjs", "require": "./index.js"}, "gitHead": "00c34d726dca1c558fe5ee5e346979159b2297fe", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "17.2.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^8.0.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.4.0_1640031219627_0.5146573657703075", "host": "s3://npm-registry-packages"}}, "8.4.1": {"name": "ws", "version": "8.4.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "ce1a17e553d2b794e017fa94887808db5c67f614", "tarball": "https://registry.npmjs.org/ws/-/ws-8.4.1.tgz", "fileCount": 19, "integrity": "sha512-6eqQ4yN2y2xv8b+BgbkUzPPyfo/PDl3VOWb06ZE0jIFYwuHMsMQN6F7o84yxJYCblfCRAxzpU59We4Rr4w0Luw==", "signatures": [{"sig": "MEYCIQD8T7tTDpobpSZNXyjCcTuXEMKxXMR7wHiIA03yU3XiqgIhAI5k+o6G1v8tE0mu+Zi0W4p/OsbAGH5Xvy1rh6GZvPML", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 129316, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4Ib2CRA9TVsSAnZWagAAejMQAKUWHcSvRMyIOFc6FDrI\npBKJBWvAANKFAhZ8DvHo9onvmBeESPKMMBiVHjXrRwzXaYIpK7q8jbFJZQCZ\nDvzjPrDF2p5ovEmbqWGrEEg8rDupMGDU47bxKTfJUSXAcoAKm/cjOQM7GLRr\nF3yBS9h3ZzF9KIQLUSCW7kzz1imkyNCTLfUNNBN5Yi0mNIgXs2/REQ2IN9cs\n9JbkuGG1ShrlBMFeFMBZK/TEUpOQTeKbf1PiHKkQqHV9BLttYsSPWXjCeQYe\npwrLAuzlbW1wVD+vE+Kg+VpLO2U91gWp/8v9GwHs/iOwMbLtOhjRiXMYVorb\nMniQ3ToW/ANhxN98y+vd24P66FAF1PHI7/aigyy416xom/ZXqRKSX1IzhgXE\nW9QP0tx6BKsFsVAIxU5IA8s63oWbT8LtT/WFKhyJ1cSKpwRHrbGdJ80zJ5Qr\nhY24gvecS3msUniuFSe3zvVUtYGxpAVG6/iXlGhvqnUkGo1q3lViKTWKaXE7\nAKm7RuHK7e0kIwsQ0548R9BnaCyZ6dO5f7IEwp4QTzJ3YQTmQKFMBffDs0gR\no3e57HzyetoWD2Ji3/dnllIQciLDcyrkynvfEfRuuAUred3j1tfXziMlYvuG\nG7qGc4HxgJ3li6iVYFajmKMDClGEYomvy0uyHPWwkySHrisybmzrz0QXBHln\nkJIX\r\n=i53n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {"import": "./wrapper.mjs", "require": "./index.js"}, "gitHead": "6ebfeb8be70aee060852134c953794864ccf31dd", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "17.3.1", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^8.0.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.4.1_1642104566233_0.058446882365017494", "host": "s3://npm-registry-packages"}}, "8.4.2": {"name": "ws", "version": "8.4.2", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "18e749868d8439f2268368829042894b6907aa0b", "tarball": "https://registry.npmjs.org/ws/-/ws-8.4.2.tgz", "fileCount": 19, "integrity": "sha512-Kbk4Nxyq7/ZWqr/tarI9yIt/+iNNFOjBXEWgTb4ydaNHBNGgvf2QHbS9fdfsndfjFlFwEd4Al+mw83YkaD10ZA==", "signatures": [{"sig": "MEYCIQCk5SBIYWaRz584Dy+7X+Pxp3u39JyuhWJ+mRWKMYnLjAIhAKp217Rck6JTIkoru8VzMRKlXn/u4XwauQedLU9h04JX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 129312, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4YriCRA9TVsSAnZWagAAMioP/icOIdLcC9afREr0V8zg\nd/EYGk3NR+iw4wbozoZPCYxMh3tfoWHpCxzzMVUZumOlC0WhBQy6hYJPW5zK\nEFglMmUHSfAXXk6WMqNM/R7/T6hVuhdgZFn2N0B3EhemNKUqlhe+QJ4GIW3u\nkvnf+bfk43OsxH+glP8d5Hgtr/ZfDF6OowApu7fMaBxCgwl8yyhGXGQXDn3r\nreWyf6G6cwzc9+qhczmQKcrCJEDfGCI/qqWa6itonNhtu3wThGcvk2YTGPEm\n1G/cuoZ+sCn0N+q6Y4Eoys/8px1WKVgZVjiZrqIsMjLchcMhReIMgHU8rakZ\nGF2P1M/y4VugQ909u0JOsO/RvNwJFLvkCRZ5sLtrbfixh88g3EFvE38Z7+Y1\nX17COX3PJ5xbuhycn4Vn1VwN0LPZHB1Rd8mtXqwybZJh+UqGjj5xBpYNVWZH\n8t3v8U6oupb0CG118GgPGYYWbvF5L1KSJdosF5foUpLbh8vOHOTUACP7Hizm\nmzJ3vDZ3BdBSmKlGuy4g2cH6hQvyM8pfTdvMc/8rxwNTWHAbKyF1ehz+GaPF\nTH+nkkmOqiJ3PvZjAk2Earm+NPwa9Wt/C7t+bQ+Cyke8j1CyUub6X1Fgl3Yp\nZoYhkz6F5+ZDpMYSctWgic8gvaz1LN712etUAMisgnFQ0uBRKta/vFQA3+vB\niKF/\r\n=sGSb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {"import": "./wrapper.mjs", "require": "./index.js"}, "gitHead": "33fd1016ec4266464027ab8d4a6e06649c93e938", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "17.3.1", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^8.0.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.4.2_1642171105920_0.344498816868291", "host": "s3://npm-registry-packages"}}, "7.5.7": {"name": "ws", "version": "7.5.7", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.5.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "9e0ac77ee50af70d58326ecff7e85eb3fa375e67", "tarball": "https://registry.npmjs.org/ws/-/ws-7.5.7.tgz", "fileCount": 17, "integrity": "sha512-KMvVuFzpKBuiIXW3E4u3mySRO2/mCHSyZDJQM5NQ9Q9KHWHWh0NHgfbRMLLrceUK5qAL4ytALJbpRMjixFZh8A==", "signatures": [{"sig": "MEYCIQDvzYTvwW29giCL3WOt6A5oOLgTS3M0sI1LqtkFFKJ3ngIhAKpksv+pvSkJAesJQvNHub7trjap4F2se2p8Z4Sb2cVH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121534, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAXZrCRA9TVsSAnZWagAA7dcP/Ai9sZqq4JGzOfNbmjW9\nEoCP/55r9AnDcFe8UDlYNCStqnCmrKJjDvt/SXDHFzQb+ABi1FZpLZfk58/L\nqZqm8HJrJV5zAH+bbcOiqKkNv7Hsq6Kkk8+N1LxHKVNfGg+PRH3ZkK3RJG5S\nf9es+usRJh9Ao2Dz+L6ekzYIsVZ7GzsW39JmgQhbIvRo+lJnPyfUQio6+tmP\nQ1pUG6BhTBCxqdBtvi8DQb0yChP8S7hR06EwiM0xlYLWkkphDxtjqs0ov6MK\nn3pkHwUOrEpNGrpYrd7j5c9PdxsqYasR1kOHlHWNPkkUxMpLqh38plDVvnCF\n+Yd5dahTpROuF6UpqBg5ruzR2s1ODBq9u+KxXTxKt6bF6TioCrUdpJNx5iTI\nCg5PWguiJ4RPqhAqJiYYKoF3Bsolz4yDJR/sB7eltNs/xFp+jpmqH67ZZn2L\nR1Pzv4WQEJ9x+I+mvnFYoLGBq/hpPXgyG3M7I1CUkWpregckNl5eHoIyDXsm\n1P83ZcRWlo2US6Hxf8nxXrDyJ4dNfJ1AzYsldZRD2BGvwp7tzqu86ORFcQdX\nCU/pJ1ymEgAmxO/QMRkjcKgkJmANsefs5P39pVUqE3VFHRdQkIc0BFWOBH1N\ncfy1E1n18nsKsi5d/vbrWyLB9L3bq2sTupdOIA4pixaitVtPV/HgclXzvPxh\nPoiw\r\n=7Zeb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "a370613fab74b82990582fa7728e130c5e87ee4c", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "17.4.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.5.7_1644263019283_0.6218860244042734", "host": "s3://npm-registry-packages"}}, "8.5.0": {"name": "ws", "version": "8.5.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "bfb4be96600757fe5382de12c670dab984a1ed4f", "tarball": "https://registry.npmjs.org/ws/-/ws-8.5.0.tgz", "fileCount": 19, "integrity": "sha512-BWX0SWVgLPzYwF8lTzEy1egjhS4S4OEAHfsO8o65WOVsrnSRGaSiUaa9e0ggGlkMTtBlmOpEXiie9RUcBO86qg==", "signatures": [{"sig": "MEQCIG3/3RvX3AU/QHxVWc2toR5zLujvK2HAhS5Q5HRqVmEJAiAtu9G7bDcaITPTa6E0lHM7WYRbOny1eYROmWOrNgHlgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130756, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAXaWCRA9TVsSAnZWagAAo/UP/2f2dad5vn5FSKOIQxli\nQT+Pd2yf5i64+Bq4z7btvKpcgfiZdE4WDtjixwaKH4Chkfr9oytW8wzNeZ8f\ncGt0HtRVd6o4D++ujg7rfzDZ8Z07yHaiC+tdvdcw3fq9SaGPoMulJL9seB8M\nCH8v+XgyqWKAvycvcWIjNx0u0RozkVcKBxhA9HgfMovzCYTr2NHU/JLHEce8\nt2eL66o6JuRCvN9KMAYJ1/hR6apG1JXNlckzWKg80cj/ikZ29P7w0dSpHQ75\nnisxIBZ9p2F/vREOdeOcFbRnoLMzAB5pzf28sUW5Oy++Mq3Jdkn8qmzMzhgZ\nNUiayrAwIkDF3nm+9Kc+0dKuaQ1Aa/EmW9Ngk7FGETrd6BWKj2d6xLy7ieDE\nFSRdLBARUjfPHXvDVVqY5pPpJ/dDmanxg7xJBnpYXJzWXb0gnCusYn+3npGw\nwBlDif1MQN3LREgYcV06NnA60Fsr/Y7ucpL/tBVQsBL/FFx2GC/7hn1SBEEP\ncekjAayrbQlvWuYFyTKa/9i91uELT0TzYwLWsqnJu1Yhef56Y3UQSwbd86TT\nTZ73eOBIGms65OeqnYOAr0q6vIR80Trh0hj5/4IJtw2QJOxnpeyDvOCR8/ac\nh2Lxxlp6bBpYVwq6HDJzZA+3HhJLNx5tVA5+Cju1IprBmkcFpCCADoRhwlDq\n5vOb\r\n=Tkoq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {"import": "./wrapper.mjs", "require": "./index.js"}, "gitHead": "c9d5436500fad16493a2cc62a0ce6daed83c9129", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "8.3.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "17.4.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^8.0.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.5.0_1644263061987_0.13834376887551025", "host": "s3://npm-registry-packages"}}, "8.6.0": {"name": "ws", "version": "8.6.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "e5e9f1d9e7ff88083d0c0dd8281ea662a42c9c23", "tarball": "https://registry.npmjs.org/ws/-/ws-8.6.0.tgz", "fileCount": 19, "integrity": "sha512-AzmM3aH3gk0aX7/rZLYvjdvZooofDu3fFOzGqcSnQ1tOcTWwhM/o+q++E8mAyVVIyUdajrkzWUGftaVSDLn1bw==", "signatures": [{"sig": "MEQCIAb/MPcINHO3MilRNIazrzVIK5l9f71dCcYdC+0EztxsAiB6FlAzvmjgibnj2O1dQgNDMI+PC5+f7utZbj8CWFpdzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131559, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibtrvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrWzw//RtW8wBCb1QuZGF+OWZVNuVU7uCra2U2JG9vG4v2qEAtOeibm\r\nQCR9WGCoxqu7gKS8xPblEF8eV5lTSIKHEeXrHYgAaGi9/8/GAoF43BY1qYQ3\r\nhSD8qnAGS2TicIsvwnCEWn+vrRG4yJKiCxIia4u9/CR9qh1mpsYbHNS7GBfN\r\ne75pSneMTFE++R6jg6E7ZDZZmSNFJedd5BJTSYTsbPpT/a1b6TdGC4WmcnlG\r\nvqD3ceT6exjW9Dm+Z2iZVdwRxRioVPC8oviZ2OJvgrfzRBcwTf+eqgrysupn\r\n89eraBACiysiCCCBgbbO0ns4iv2II1STgRXkWhJ8opUkGuBeBLwcmvHEgDZK\r\n2qpa8OfGYDVr8yu5jH38slU4QSw4dm90iYGUGtDkAxWBtujfrnJcSGDvju1f\r\nDFsfhQTfVNzbLAEdB+oxJvlUqGOkuu7yoW824w3h/uD17rePfv3zpSsgTmfh\r\nor44qi+QFkcfl255b22FZksPrDSIfVZeOuS+4iC4gRNw+uJ0OP2flcAwDZbX\r\nhZNQehD778c7SC88f0Depm3htkchunkx6pfwCKfh9J00j4VP80a9YPk5afJ8\r\nMG20mG2LAO00r5nwNX+AqEHWLe2PY/+W6qo4Q0+zQ27A5tyKoIbKd37LBuFA\r\nvAZjSenRB6YeDovJJ7tKaJ2i3ZUHEU5UDoE=\r\n=wOOG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {"import": "./wrapper.mjs", "require": "./index.js"}, "gitHead": "ba214d96939737304fb76f662700e9df01bb4418", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "18.0.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^8.0.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.6.0_1651432175764_0.4694362180774898", "host": "s3://npm-registry-packages"}}, "7.5.8": {"name": "ws", "version": "7.5.8", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.5.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "ac2729881ab9e7cbaf8787fe3469a48c5c7f636a", "tarball": "https://registry.npmjs.org/ws/-/ws-7.5.8.tgz", "fileCount": 17, "integrity": "sha512-ri1Id1WinAX5Jqn9HejiGb8crfRio0Qgu8+MtL36rlTA6RLsMdWt1Az/19A2Qij6uSHUMphEFaTKa4WG+UNHNw==", "signatures": [{"sig": "MEUCIQCdLOCfgHebSQuMLdmN3vwCNRJfEtuZdXdi9OUnN+FmawIgQHKs3mQvBaL3x8JBufNJMn/OjDGSHxVWgKvVPZuF4Lc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121861, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJij7kXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr3ixAAgmNAlBLLdVRmIaqCgi8F0jlsO4wUra810NpuwFCEIuZ77qYg\r\nhx99hx/z5ouYJmUIcA05BVs2FgrPhwR9XzlriqgHEe9e4/O8/o2SJiSfAGws\r\nzLXpRgzBWL/ZgAAFMsJr1GdOEP2ty7avRbX2u1/SraIW6Kkck0QeYdN4OXCD\r\nw1Ph7t94epnwxc1Jfye/1LuD6Yt3tvjlqSOJTCl9UszLXBhwlq+tbnVKsVmy\r\nyu+K2n24GC5DZDgn4x6CEVmhVfF1EeV/qGNgadNaoTmBVpT9pcgQ01F9KLDM\r\nB84namQwuDJyRnxcZzD5dUlKlS9gIAPhPgloqr54mSs81hdkO+kNSDl3JKt4\r\nTpF34OK/aYMZWESlCm6CPSW8a17lmqLGyy93t+gAM8PHvXHXP58BgsZhYEO/\r\n3G4LCpZOQJ9Zuej5+XmxSHnQmACw/+9igwuv42GjY+80F4b/xCRZiJaXpAdq\r\nK9mm01Rh+W/jcvP5sQUWkuIpsqoJ8jixwWRt87OL3L3F/EY3fZNMrqLi3Bn7\r\nAPhYs/3T4XWY3dXrHb0oLcpyldlPyVI+VjETqgGsM2sqLYxdeXR+/hirgfQZ\r\nSLZQvE0G0RIBywJT6GulLYcrGSsIjqgy53Dg+iauFCelDE/g7pHpFty/5SAo\r\n+BLcuMt0f00HVBQaCF0uOOCI7gnrhTB/5cY=\r\n=cpxN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "4271f07cfc95cf7e1936388fb69e22a3731fa260", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "8.9.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "18.2.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.5.8_1653586199443_0.47817007924291555", "host": "s3://npm-registry-packages"}}, "8.7.0": {"name": "ws", "version": "8.7.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "eaf9d874b433aa00c0e0d8752532444875db3957", "tarball": "https://registry.npmjs.org/ws/-/ws-8.7.0.tgz", "fileCount": 19, "integrity": "sha512-c2gsP0PRwcLFzUiA8Mkr37/MI7ilIlHQxaEAtd0uNMbVMoy8puJyafRlm0bV9MbGSabUPeLrRRaqIBcFcA2Pqg==", "signatures": [{"sig": "MEUCIQCHzHS1D6MopvcaOR1yZ1pZdUIzF5D4jSUdhUdz2lOFgAIgA8nuRf+b9DJ4aDvmbYYknnz/9kNvWFkm0Em8FJYOQTI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133810, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJij7kyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoy/g/+OiDG7ErIHhCIvelzYx1LJcNY8UdT/HpvHVAWc8Rm5WyfbQKJ\r\nGY0r+N1wdUuMIkwNYDcaSErSCSGzKBLfEuc/vn1FVugUGBQbc9Fxz1sZm/Hd\r\n21X1qjHE42mCXE6bfZ53+06CyU4TGyOYR/75zk9+6YIzRlbordKiJDywSQXc\r\n30VQSD3RYWiR7E+RMWgZxgpjUPmXX68rzexUfUf2kAUQV8806qGgkgb0Pvf9\r\nsz8EEarAcBcC/1PQm+DwHXkpIBhlPCjjY4sY5R18yHZBjdSMfytJwuxaLLv7\r\nhPteIhrnwErQX9u4eptzeE6Ji1X9JCTHWetVqYKJHuGYuB+rsz226g+R/2Eo\r\nopAKVjPAnAhApvAUP9NEpHrOXh+VQsBAY5aWiwJrrHWrKL4R9VwFdMfDgR/O\r\na1hoezod3P2qnAHl/vCVEameiNvbms7MyrPDkKGCIqyxcdKZh/+czJQTkExg\r\ne9tD5dyrFDhOn35CsA/JQ1Mt2ws6mh6PL4q4DJ3x2iQe3c2Std1N+292cQLO\r\nKf9GfKn7kMjYki3FjWipQmcuj2WZ29M8f4FWtZBRpQCzd/r6NlMRYVFFSWiK\r\ncZ0SPjq+A9UfG+Pr2itJx2HQJyU1YK/Zi2ZojzwkYCWuVPEKtaNxxVZIyQPb\r\nOSxo7J8xFGIcyCaLanBmt76Ssj6N+IIvT2g=\r\n=5Mvu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {"import": "./wrapper.mjs", "require": "./index.js"}, "gitHead": "4b62fbf945cee92f0b06535d8249b0a1d5037cea", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "8.9.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "18.2.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^8.0.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.7.0_1653586226742_0.3111733805265069", "host": "s3://npm-registry-packages"}}, "8.8.0": {"name": "ws", "version": "8.8.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "8e71c75e2f6348dbf8d78005107297056cb77769", "tarball": "https://registry.npmjs.org/ws/-/ws-8.8.0.tgz", "fileCount": 19, "integrity": "sha512-JDAgSYQ1ksuwqfChJusw1LSJ8BizJ2e/vVu5Lxjq3YvNJNlROv1ui4i+c/kUUrPheBvQl4c5UbERhTwKa6QBJQ==", "signatures": [{"sig": "MEQCIBUtVUwFG+uMKhhP5ktvxd3TmFOmTng80cZekT9X0awgAiBC6iSD+YqljqQLXgz5IfW/j9pKQU/wKQHqHLgVVXJCeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134267, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiokNUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKzg//WJqNkhsJPjd0zmjxVg2xOpq5lgPK6AKXyO9QzoDNmnWGOU69\r\nCKbOdkvJRWxug4AEaAMb5Nu7vzlTLbI+AFW/rdM/EPxF7xjciTOV74a0//sQ\r\n7yeV10o5XDmPkybXBcIq+bCNJshvyVU99IExgypbyPqsWhVNzDKjJ7evl2Ab\r\nrnkCjrkQZy/kvKge5MRmxWaOhOuS2rk+NjpsjdcPajQdiiROoehhHZZRobYK\r\nVNlS/6B3/IFVH1FTpsUnxgBZ1ZJsOTwqLo7QiZOnb9+LJbnQIXUKgd7KqnOc\r\nZRldAcQN+nSgT2EYIoCoJ8ij63qdclttoPwyL5NpnEOEQmCZX+XD+PfOP94O\r\nc5MJgTjdoMdzwqVgGIwOW3na76LCo+jmXMcZpDEegRxea7k/xDE4USKo4EB4\r\nBb0I6ALkB5wg0Kwf8Ivpw8tsmPid3qOTYa7Hbb6olN+dTh/VYGy9rCkFMOIR\r\n5aydwDCLiOr1OsW2wzTSXRMXam31GTVt91SNbtd5ng5Pdc5UBn+caP9kwU6T\r\nNSEUgBh90FqdGF/n2sg4ll0MebsGA1LFctMAvtMgMu12dzKXHfxIGcFZVvfE\r\nh82NbBTsUT9FyvcdTRWNhR9FsDR0OTna7daa6TYo0l+hguqqli6Wpe8c49PX\r\nnkbiJTk5ZtLhzShzbmomVXM5CorAU/5yUt8=\r\n=beso\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {"import": "./wrapper.mjs", "require": "./index.js"}, "gitHead": "982b7826f940b7caec5dd7ea82386dc531c5fdd4", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "18.3.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^8.0.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.8.0_1654801236728_0.01735788011393158", "host": "s3://npm-registry-packages"}}, "7.5.9": {"name": "ws", "version": "7.5.9", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.5.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "54fa7db29f4c7cec68b1ddd3a89de099942bb591", "tarball": "https://registry.npmjs.org/ws/-/ws-7.5.9.tgz", "fileCount": 17, "integrity": "sha512-F+P9Jil7UiSKSkppIiD94dN07AwvFixvLIj1Og1Rl9GGMuNipJnV9JzjD6XuqmAeiswGvUmNLjr5cFuXwNS77Q==", "signatures": [{"sig": "MEUCIQCpvoo9ORkB5bcbmL8pFbE1W+7m4Ku76873M0AJQqlOLwIgLY9SoKoajeDpqk6LnpQI6aqrAdqvL7YJMbM79VcVWGU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122191, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi0aCeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVTg/+JzUwXutzHz5Ciuw9z30WR2KSdzUttqHtLMVU/r7LzQMuPhHI\r\nZtyTSv9NEUnAt5nnfSGmLaPYJd1F61EYkvEV8aR4WmJL3eKAkU+cylNEhlNG\r\n03pRgpCBeQ8CnhRaaHUfubS6n+SWw6KD/KBJvm4vzlY5Yoql+ybtKL+rYwiu\r\nhQgG4v0W3RiN9dQPg0h1YisvZHwjH2uDgkXApHR92PPgYHVH8zIKvx2H16eu\r\nf3Dh9vdb9O4sVeV/dkTL+M9x3uUZng+i4Gdao8eyZMoagUvTvTGCGW3ZpN5s\r\n6StiAI0NuBePEnGxqHIYOgY6zY/49Q4xDzGXOc3WT2/bwzDFvXrXsBrxTln0\r\nSIEjEYNMWXzhYzY+7i1yZgdRIXxy7gRKT31jgH0fTLlkkVr+Mmq6eKwDKbCP\r\njSKkvcg1kXhIf2ruljhXLFGbm1np5oXAOKoedcSum2ObFOMR5hkbMSIa4bS2\r\n1Dn5GYy5u0c9T18s3kcO5BZAVApiHFkEV5fbZOMabQPD9VwsquZ2y68F/sVC\r\nY0yY0ppfeV68UiLMD/P+eIpq6KtIeCJPRlcRAU2BuelJnC2/Iq6lCKN001wh\r\nGOEir/JPq+YSef8uTZIm13TAV2Rz8X+0WcwU7Tzytw4AxnFjqbRhbLZq1Oxj\r\n5iIcq82UbrIen95CFDchq5kSMQIfiiae8f8=\r\n=CPXB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "8a78f8770618cc5a1ade485a7445cb6d6f46e2f2", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "8.13.2", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "18.6.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.5.9_1657905310431_0.7299860311960082", "host": "s3://npm-registry-packages"}}, "8.8.1": {"name": "ws", "version": "8.8.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.8.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "5dbad0feb7ade8ecc99b830c1d77c913d4955ff0", "tarball": "https://registry.npmjs.org/ws/-/ws-8.8.1.tgz", "fileCount": 19, "integrity": "sha512-bGy2JzvzkPowEJV++hF07hAD6niYSr0JzBNo/J29WsB57A2r7Wlc1UFcTR9IzrPvuNVO4B8LGqF8qcpsVOhJCA==", "signatures": [{"sig": "MEQCIBgQTHd+/PulDUMvZdn0yraF9iK0xJWV02WkRjzgYv2mAiAnLE64QkWYGQycyWYvj43Mmv3MBShST++SkTtaLriYmw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134597, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi0aCvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpX8Q//dQdmcF15xVWJc9Pl3GR8ewHFkqAucIqUV8caGzVWQc0rDASk\r\ntZHd9ZYGxC3C8FrfG4i582PG2VWHCtGZbSSz2bs+suJ8fKg9v+j7SeW4tcKl\r\nr3nSP/ugYZkuAgb//xtfOMKRjZ6Mlfg4o7T4RQ1eiiswqbv63Q5bTTCFigUA\r\n5IMBI/dV1+he4FDepHg2muPW2U/BIysylT/JuhWRZCgts8kBORxdwqOgPfw7\r\nijdiBJdXNAL5ZSBO+0EqhXO1K0AYn/fuRWlpW5IdbZzjdXNwdGdbwThBK+ul\r\n2PLZujHJ8EaA/Cq+f5bV25Usclz2DMkCEQRMMbse6sUeuY3+64X5dngGyKgp\r\nNrZYK621+B+mNIw464HDZIm6unoQhEN+woOAcIfAyDY/5E7Ix7lxeMLcNcYM\r\nIF0KUaGvA6HSxKOMY83dP4JHXN9a/4zB7w0F7UxKwpSl0NpPn862Q0I/f4jM\r\nW6s0uN5wStXHEJJQQFgT++oS6cmGZaywA/DMEOHzwRCAO/yTpM3m1Hlbtjgw\r\nU7VI1RAAZLuLC5kyklDtlSj8iYDATSE4C+D2OtEE9330neFnS7CbqWFs2fjh\r\njOc9yC9+JQjsdbr3BkraYwi5xg1Ft3RSmF2PYF9vT3zw83TxfzOdZDTOUgcX\r\nD6fHkw+1pRKwtzg0PddLtWvC1SyTn/waCSc=\r\n=5q7I\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {"import": "./wrapper.mjs", "require": "./index.js"}, "gitHead": "975382178f8a9355a5a564bb29cb1566889da9ba", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "8.13.2", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "18.6.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^8.0.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.8.1_1657905327602_0.6923117651796722", "host": "s3://npm-registry-packages"}}, "8.9.0": {"name": "ws", "version": "8.9.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "2a994bb67144be1b53fe2d23c53c028adeb7f45e", "tarball": "https://registry.npmjs.org/ws/-/ws-8.9.0.tgz", "fileCount": 19, "integrity": "sha512-Ja7nszREasGaYUYCI2k4lCKIRTt+y7XuqVoHR44YpI49TtryyqbqvDMn5eqfW7e6HzTukDRIsXqzVHScqRcafg==", "signatures": [{"sig": "MEUCIEUgnqVD4SeP8LuZFJ8F/TDOTD+bSDKnsJsqjdnvGu5DAiEA6vAdKNDvnkvkgg6Oj7rERimjpXrgfnL3a4DLgbmHaws=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134548, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjLLicACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrS4Q/9FIVJr8Vy7ZvPDeAkRgMm16tB90ujeTWTiXb43HKpOXDC7/01\r\nzIHz+srzpmCkDcPpDeBRHlJHD+UhQYndEqTdBwvfgraKViP9ie94ykqVLKom\r\nWb1kSeyocKFd0wXOYpl/65284qAZM2RR7+Zig5nfhNcppiTi9kPbYZVjffaQ\r\na+r+hNyq7B6Itm2Y4fy+bgO6ef7J4tK4HMzFx3lJb+xzxOKQ97IeGEoernbM\r\nHqqxrDlWx6fz51e2bL+ObqJ78/6AMhnqOPbHDTNG52qaiQeeUj01PicTX2Gc\r\n3ThfKzzm3VqA2PTHcdvhhi4eEy3cHxmUM6UBFinzVYZhnv6MLxw3HgP5N5kk\r\nUK1PKUxlqhAPn8SxxxjuUhVRhybsOYoI1F/VtzQdJyFhaSZO/AWHFTscI6Sv\r\naadsP/Iq3ulTT9UOXo30RFfTVCKryxRYFAccHhz02JqIsw+5pBp1M7oTCf2o\r\nt2pSdpV3AiqrH5R/xmi4wzLcGsuZGMjP4UC9+Z8PMjqA75Fw3U52UUnR+6wn\r\nzHcK/c1pkXQ94Qpz/b2+iRkFg618ibG1yR3vu/oAF3xIdJfs0rQJllZeRt2y\r\nkZKUKtqip+yBic8Kv/1TdnDddp7jugzf/iadnEHDn9+jHzekLXhmmKIk4zAe\r\n3qxdIbEZ5G3Y1bcsAdA7En/58rrQnZQ8mJw=\r\n=z8BL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {"import": "./wrapper.mjs", "require": "./index.js"}, "gitHead": "34522ab8e53d7c9d785604903ff9475ee55529b8", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "8.19.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "18.9.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^8.0.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.9.0_1663875228127_0.1602401554860211", "host": "s3://npm-registry-packages"}}, "8.10.0": {"name": "ws", "version": "8.10.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.10.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "00a28c09dfb76eae4eb45c3b565f771d6951aa51", "tarball": "https://registry.npmjs.org/ws/-/ws-8.10.0.tgz", "fileCount": 19, "integrity": "sha512-+s49uSmZpvtAsd2h37vIPy1RBusaLawVe8of+GyEPsaJTCMpj/2v8NpeK1SHXjBlQ95lQTmQofOJnFiLoaN3yw==", "signatures": [{"sig": "MEYCIQDT4KfpXkgOZZvW0uGm5lNHinuXqRsUIK3E/7f5AlQWhQIhALGNuFJPHo4tD8Kqup/AOaRhceRGIvPw87VPF2H0AqOX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134610, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjVuLaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJEg//d2w0/hM0af0rsbyHT57d1m6f7qwuUXmFV3nKJgCFiNtIVLI/\r\n2Jx3WK+PI1g04iAEq3HuGdk1KIFX9YtPEnZ6YhHXd0w1/aEi7AbluDzaMKgU\r\ne19uuBmBBkP1HpmqhmGTonrtJg9RRloDseobLjywlz3KDPrSTKtm88z9dsIZ\r\nrvXEgkVEBYWD99ZM3KU6E3zXM3tDhY64YVoTOGwC10PTTBTZgTOXNJ2dJczs\r\nexQsWVRH9gVHKvy4ZSdrthiEbStCbKlUkdadO+fqfP7XnoBKbSo7JdCF+6zS\r\nFMibeld7T9oxlGQwwBi0Qmy7tWQa8Qe9zP8urIqyuXZixA7us8kIfHuJox/c\r\nmrD5Hj8RWwq1CZaiz9khQI4AAqlFvKINLTt5cxaoBtHQpYB3WsXeb7ZOCD8v\r\nqm9Ys3p10r/q7YoE5MsxVhScc3GrHj9gQgdo91j99CRnCetbBdWWyaI2WahK\r\nPnNz6P3G0Fu5uLP9UKx9WMQU3QH8MahHbmKyAtmjSCUsT3DNDEXZr2RiRQVi\r\n6OZdAX3BfcQzRqCc3imaOz4gZJlxuBYEjKgoOIHPuSUCfWKbpmsjo5m1wtbq\r\nyRThIZ2MqxjBU21O2DiIxWb1Nnoanv+RxFKtMA5n4AETUs1O9vcdcVJVffL/\r\ndmy2g4TNdYRBGs2zao2A3SSc4RtkgqLWFcY=\r\n=ZlOy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {".": {"import": "./wrapper.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "cdca711ad434fe4f691392807cba3f83a515eebe", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "18.11.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^8.0.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.10.0_1666638553994_0.7040921717862032", "host": "s3://npm-registry-packages"}}, "8.11.0": {"name": "ws", "version": "8.11.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.11.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "6a0d36b8edfd9f96d8b25683db2f8d7de6e8e143", "tarball": "https://registry.npmjs.org/ws/-/ws-8.11.0.tgz", "fileCount": 19, "integrity": "sha512-HPG3wQd9sNQoT9xHyNCXoDUa+Xw/VevmY9FoHyQ+g+rrMn4j6FB4np7Z0OhdTgjx6MgQLK7jwSy1YecU1+4Asg==", "signatures": [{"sig": "MEQCIGfOLAh84aPAtc0GNTKNsIaIc0zNLx1wTGY5Gv53chKLAiA0B/CgVbM6GDdWekSnpDWb4bsrixHuLOU5jYzrdKOOEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaBLvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmozyA//QVbDZd5ogRkJ6IiAXxNMmq8oh8uYogdZYIN4cDRd1NIQ+7iG\r\nrJcY9GsdbyDeTCUQapX/17Q37TlDLraErxS38X9lyS72lnrcAxmpbP39IeiO\r\n32D2rDcg5n+L3zDe/2OgmlJ/+92DJci/X/4xJ/7CSIYWxHWVXVvE5WXy/o89\r\nKlqpB9tFy4llhT/Kq5s8l86d7RbbiThrO2d6Ch2aPT4Adtr1CO0hyAkbqHD9\r\nKocCq3s91LuWMa80vA4GNAeArOHtlbD53JywH9vQQDSlZS/4MuJNLCKmEzI0\r\n+BMJmf9xPXzb0ljqsZ9xHsF38YunWcKy5ZAfH+8s4ohVgr4ARI4oWEYy4QIy\r\n9g2Fja03AexxLObH7RUKpl5skcMjXIdQavk0np65KiYyWjVRxDUc+eeFi8Bf\r\nHmOetTEAQzENv6c6xjE3415w3MIMjyvEqZ07h5Hf5Kntedq+EH8YG5ijxUi4\r\nOD4a+kitrVmbbb2agfzUnD6U6PKDnQ6FCAvEAPzFxn2GyJXC83apXyNaM7C7\r\na+tCtGuOebGKnzSU1mTOK8vd0WG383eV0tIvtqYWHoG3DnTopk8Q5rD3kVz9\r\nXgW/D48fOJBdn6sFW6n/Uiz9q2FIT5F9d7ZhWEbJXdnNvF+Y4Zyli5Q2ixjx\r\nFfdwKej0MKQrTBhaXAC7f6LfTiH7g0it1zY=\r\n=R7Nr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {".": {"import": "./wrapper.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "afd8c6269bf5056a052281c543e9f19c7d88673d", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "18.11.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^8.0.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.11.0_1667764975059_0.1865619802807117", "host": "s3://npm-registry-packages"}}, "8.12.0": {"name": "ws", "version": "8.12.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.12.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "485074cc392689da78e1828a9ff23585e06cddd8", "tarball": "https://registry.npmjs.org/ws/-/ws-8.12.0.tgz", "fileCount": 19, "integrity": "sha512-kU62emKIdKVeEIOIKVegvqpXMSTAMLJozpHZaJNDYqBjzlSYXQGviYwN1osDLJ9av68qHd4a2oSjd7yD4pacig==", "signatures": [{"sig": "MEUCIQD3a0AHkQTyRBkOorhKdBH5bB6Mat7TWW1Z8Olhy8vcFAIgLUCXOWv9kR6tUpm17QaaO7ILbJiE3qLY3jcOImqbf7Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136353, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjucvzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqiyw//VGKBNMwRHNO+EGHMVUvFfxvYNfy/eUZNOok5YTYkGUYX29Ny\r\ncBspq3dET1bZMHhq3+lLLeUaXcMEcvEicuYOjgQGi3YOstvEPTenvsNyMJlQ\r\nVfUryo2t/fMrA/cpiN8Mee7qu409dnZNUlL263sdcDJwAy6825kaomEY3yGe\r\nFdErEnGfXGy1Bn9IuGDSHql34PDsUr1Frysu9OmxVTDwYRjSmJce57XszHkp\r\n+ckG3Q2Qeye59IcFZgPUQPDggA0QJMI7nIp4QGzRwUp3CqdaArrIdjduuKpY\r\nf8uIXuj1WRXpRHZrRBt3sARM2OuWjyTb9CQU7luzH3q0PEOrNp5IlmIYYjJs\r\nWI4wLatuM2jWMK/xBYEx26G4zT95CK27jTxj+QSS0nGxpI5f5VAf7dQGusrg\r\nWqenVMZiXCwRFF4mGOXBYWDdKKRPkrWY82JIp4BGoBgtmx9G9Fqvg6DI0A83\r\njXzFN0RQOnqvnbQcj7zksO8Xes+PRwqw2VIiQKyCZA/JjPoifYodHDT6hasO\r\nJITx7YxblOuJZ2FWl8IaxXD4l4y0OA2Yuapg+izL3kUJEUpMR6XtJtDGetYf\r\nNeG0KInA04dZgsunckMKQL52NqerYn22It0r3X9Rr+87bqxgaQZQ5Hds1L5I\r\nOE2t77C/n9ArMrbyhrE4GkyKiqZNY/s6RPY=\r\n=cwMQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {".": {"import": "./wrapper.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "a3214d31b63acee8e31065be9f5ce3dd89203055", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "19.4.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^8.0.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^6.0.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.12.0_1673120755620_0.1264999663450994", "host": "s3://npm-registry-packages"}}, "8.12.1": {"name": "ws", "version": "8.12.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.12.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "c51e583d79140b5e42e39be48c934131942d4a8f", "tarball": "https://registry.npmjs.org/ws/-/ws-8.12.1.tgz", "fileCount": 19, "integrity": "sha512-1qo+M9Ba+xNhPB+YTWUlK6M17brTut5EXbcBaMRN5pH5dFrXz7lzz1ChFSUq3bOUl8yEvSenhHmYUNJxFzdJew==", "signatures": [{"sig": "MEUCIQDs5xuIj5P9hw0u293IRsXzL1lcEZxvnmkkWltT40B/ywIgJ0J2wCcEIHfIhdt7PaeEeEaFniO9coZXKv0yOm92xJs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6p/NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqUEg//ZmtNggq2qnFGycpyPnyOqMZ61zdTAJiQy+DIioZ4VqCxkpP6\r\nV0RWdEZXjE1MGt1fIKgbNOoHw0VQZoxOGpzhdCkB3Z7MeX1/Lz6K/yfufn/z\r\n6UGDfFBlcBBj7q+8T86LYtCCQxiuHI0eqtjNhxonDcSiqg3lOt5epbBKXWVC\r\nCy7rnnmZHLcPzP16//b00QdUprB/Oj7s2keEFScfI0H7KatT94eir1OAFjl/\r\nFu6EgDFchiKuovwRFt1TVfCnbUKh2JL4VK2IJGbgTDkb+G/tpi4VjYr9uy0u\r\nQAg60HqOOU+o8t3sYL3MdD3gpe0fkJQprE+Ns/W3ccdlUcLoL5peZZNbnFhS\r\nVrkgBbnvoQfwP/XXPTlvwqEpxhfHRL2Az7a+EP60tS9HkKAwlMNhW/53bgP+\r\nPy3cuB77SC9GzAEPMA5c7RO5nhnqPTK/Q36EqPgyooczB3PRu13qRgzuDyas\r\nzQzo4L/9WfXgLQLaglv/Ew4Zq0gz5hn/0tYmydRMhsF7zJgfsyr4DylRFruf\r\n8hq60KR9ILQd5+EZuXgxmLfeicZaq+WIT23aeAijbv30//D/bgju5JIi6TEz\r\n2urRza6xESIsFySzl2vU+9V+Dl0UkORN15sEj+9F2GHqPie/u5ooT+cMlE6v\r\nSEc6P/Gt/PlWBruIWixPVA9J3SwLgSIysjs=\r\n=w/24\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {".": {"import": "./wrapper.mjs", "browser": "./browser.js", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "a04578e36611998d089fbb7c6057d1363a5d5754", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "9.4.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "19.6.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^8.0.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^6.0.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.12.1_1676320717479_0.9575021440747828", "host": "s3://npm-registry-packages"}}, "8.13.0": {"name": "ws", "version": "8.13.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.13.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "9a9fb92f93cf41512a0735c8f4dd09b8a1211cd0", "tarball": "https://registry.npmjs.org/ws/-/ws-8.13.0.tgz", "fileCount": 19, "integrity": "sha512-x9vcZYTrFPC7aSIbj7sRCYo7L/Xb8Iy+pW0ng0wt2vCJv7M9HOMy0UoN3rr+IFC7hb7vXoqS+P9ktyLLLhO+LA==", "signatures": [{"sig": "MEUCID4OmHKpQN3aHuheHREw75fI9caUNy14+nmVZrCtoOXNAiEA5maB+S2PH4x0smZ5AXPzArtkYX7nYUvWzW2mM6MEC58=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137122, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkC2+FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrBrA/+OoUh5rQIHG0OnaRQKVhkUHDXDiV0YYEHOLyHCw+wPcG4yxPR\r\nWIA2TIFLTUCFKP+GfPGOZeEgbSvfOwYflclHeZ67YPtelj7W7bZ6sU79h2Xw\r\nrPZQRC9F5brBDqllmrfnNHcR7S2NyKi4WGR4sk8uhlanvBssOVdxWNKy487X\r\nhpCyPNLRfq1Uxmp+ZoezZUoTDT0Yzaj1D3/yYVqT9MNZIgcWsLkkKXyJ20K3\r\nziQfopKueH5fIegFUE076k4KmkPMAsSN+A1euwwBKwBPZQhNnGAA8GhwMKH2\r\nsRm+M4kUCJurWtuBs+YcK8urWthp0ppBVh7nKJGNZgjeGWaNtUFFjTq/Ne3j\r\nK2pklo3sWHnmqDpQ53jxF7Wb5E1C2BVm8QsctbLbCcCBREezxbB71meLruGp\r\nP2ETDhwEiMYzzxcJGyVvuX/U2yHvpQXQgs/AJ2h0Nppwn0WtVf7wmsn6c0Nw\r\nj9z76I0mehjds6oRuyXD0/oOtv4KDYVnpJjeD703Bpi50kPxI7+1Hz14JSzL\r\nNfU5ps4TxOpfW/ZmrsBo9Wa8CC36fR1N12eYfR/ZA1Q19x6Rcvt+4elPvh9Y\r\nhj+0fP54l7blutkn8r9Es+rNgb84pdiVbImh8rzOBgbmcAlLx8DFk0+Jzcua\r\nF0x8Qi1w6MVpO0tCD4pXrdRP+bf1fCcmSBs=\r\n=ftVH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {".": {"import": "./wrapper.mjs", "browser": "./browser.js", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "45e17acea791d865df6b255a55182e9c42e5877a", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "19.7.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^8.0.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^6.0.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.13.0_1678471045450_0.5555033997015613", "host": "s3://npm-registry-packages"}}, "8.14.0": {"name": "ws", "version": "8.14.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.14.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "6c5792c5316dc9266ba8e780433fc45e6680aecd", "tarball": "https://registry.npmjs.org/ws/-/ws-8.14.0.tgz", "fileCount": 19, "integrity": "sha512-WR0RJE9Ehsio6U4TuM+LmunEsjQ5ncHlw4sn9ihD6RoJKZrVyH9FWV3dmnwu8B2aNib1OvG2X6adUCyFpQyWcg==", "signatures": [{"sig": "MEUCIAUC2xjimZfyHWmg3h5M6kjjgmWkM4YboO1OncEjCU52AiEAxs3CJlXtLQZWjVmAWy9+RBJ3zewCnfLUb7PvJDlg9SM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137722}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {".": {"import": "./wrapper.mjs", "browser": "./browser.js", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "d30768405fc295f0365c4bad8b7e14a9ad54c64b", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "20.6.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^8.0.0", "prettier": "^3.0.0", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^6.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.14.0_1694009115647_0.9200893637328544", "host": "s3://npm-registry-packages"}}, "8.14.1": {"name": "ws", "version": "8.14.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.14.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "4b9586b4f70f9e6534c7bb1d3dc0baa8b8cf01e0", "tarball": "https://registry.npmjs.org/ws/-/ws-8.14.1.tgz", "fileCount": 19, "integrity": "sha512-4OOseMUq8AzRBI/7SLMUwO+FEDnguetSk7KMb1sHwvF2w2Wv5Hoj0nlifx8vtGsftE/jWHojPy8sMMzYLJ2G/A==", "signatures": [{"sig": "MEQCIBGoqwyVJIu99Z2C+phuD8TiZTIQf0iwRd8LNDUrPwZ7AiBPIP4YfLWFAiVqqltDQi/23wj70S9H0Q/kppd9gzEtAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137785}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {".": {"import": "./wrapper.mjs", "browser": "./browser.js", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "7460049ff0a61bef8d5eda4b1d5c8170bc7d6b6f", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "20.6.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^8.0.0", "prettier": "^3.0.0", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^6.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.14.1_1694189084693_0.5094417396331195", "host": "s3://npm-registry-packages"}}, "8.14.2": {"name": "ws", "version": "8.14.2", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.14.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "6c249a806eb2db7a20d26d51e7709eab7b2e6c7f", "tarball": "https://registry.npmjs.org/ws/-/ws-8.14.2.tgz", "fileCount": 19, "integrity": "sha512-wEBG1ftX4jcglPxgFCMJmZ2PLtSbJ2Peg6TmpJFTbe9GZYOQCDPdMYu/Tm0/bGZkw8paZnJY45J4K2PZrLYq8g==", "signatures": [{"sig": "MEYCIQDJ9f2uds4r1Rum6W35O/wsnE/QxuThGjpUQthA6WCLawIhAOfJhZPQmnFbtfh+Wa0cpTBaKJeDxe0Os9yaJ2cDvOcc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138276}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {".": {"import": "./wrapper.mjs", "browser": "./browser.js", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "d8dd4852b81982fc0a6d633673968dff90985000", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "20.6.1", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^8.0.0", "prettier": "^3.0.0", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^6.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.14.2_1695137564809_0.8393578737113812", "host": "s3://npm-registry-packages"}}, "8.15.0": {"name": "ws", "version": "8.15.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.15.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "db080a279260c5f532fc668d461b8346efdfcf86", "tarball": "https://registry.npmjs.org/ws/-/ws-8.15.0.tgz", "fileCount": 19, "integrity": "sha512-H/Z3H55mrcrgjFwI+5jKavgXvwQLtfPCUEp6pi35VhoB0pfcHnSoyuTzkBEZpzq49g1193CUEwIvmsjcotenYw==", "signatures": [{"sig": "MEYCIQC65wmidOeF/6ISPZIrgwwqZB/lS2nKsflqswc6/4B8xwIhAJSN4E46cHm97bMhIOWa7AMSPT9xs72pG1Jd4aRpjoU+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139675}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {".": {"import": "./wrapper.mjs", "browser": "./browser.js", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "297fff8eded6328e4386fda735002b9c4d17b537", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "21.4.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^8.0.0", "prettier": "^3.0.0", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^6.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.15.0_1702145556735_0.36938328299333323", "host": "s3://npm-registry-packages"}}, "8.15.1": {"name": "ws", "version": "8.15.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.15.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "271ba33a45ca0cc477940f7f200cd7fba7ee1997", "tarball": "https://registry.npmjs.org/ws/-/ws-8.15.1.tgz", "fileCount": 19, "integrity": "sha512-W5<PERSON>ZiCjXEmk0yZ66ZN82beM5Sz7l7coYxpRkzS+p9PP+ToQry8szKh+61eNktr7EA9DOwvFGhfC605jDHbP6QQ==", "signatures": [{"sig": "MEQCICS1l8VVm3AE8903CJS9D/oD5X3RzXpCigJsjvtYY7FrAiBslxLJkaYw0F89r2mhb/WiceTqj9Vp5B0W6O2EI8zFkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140743}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {".": {"import": "./wrapper.mjs", "browser": "./browser.js", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "a57e963f946860f6418baaa55b307bfa7d0bc143", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "21.4.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^8.0.0", "prettier": "^3.0.0", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^6.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.15.1_1702412029101_0.2842220523454666", "host": "s3://npm-registry-packages"}}, "8.16.0": {"name": "ws", "version": "8.16.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.16.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "d1cd774f36fbc07165066a60e40323eab6446fd4", "tarball": "https://registry.npmjs.org/ws/-/ws-8.16.0.tgz", "fileCount": 19, "integrity": "sha512-HS0c//TP7Ina87TfiPUz1rQzMhHrl/SG2guqRcTOIUYD2q8uhUdNHZYJUaQ8aTGPzCh+c6oawMKW35nFl1dxyQ==", "signatures": [{"sig": "MEQCIHKElm7CEjVP6YflTqUKMpSQrKt4wjijWzRc9K0sbRV5AiAH9nNeGnYQDWqA0HKLCujGEOCapFadgIDWRlKw2knneQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141298}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {".": {"import": "./wrapper.mjs", "browser": "./browser.js", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "d343a0cf7bba29a4e14217cb010446bec8fdf444", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "21.5.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^8.0.0", "prettier": "^3.0.0", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^6.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.16.0_1703605704285_0.7385040408160342", "host": "s3://npm-registry-packages"}}, "8.17.0": {"name": "ws", "version": "8.17.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.17.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "d145d18eca2ed25aaf791a183903f7be5e295fea", "tarball": "https://registry.npmjs.org/ws/-/ws-8.17.0.tgz", "fileCount": 19, "integrity": "sha512-uJq6108EgZMAl20KagGkzCKfMEjxmKvZHG7Tlq0Z6nOky7YF7aq4mOx6xK8TJ/i1LeK4Qus7INktacctDgY8Ow==", "signatures": [{"sig": "MEUCIQDFTz3lMjrxmAILms8Agwync/zRrPHXMTlcuLnoMX5NzgIgVXmzy8BsKoDDM/b++4PWsgTzpA64QK4Yov3VfsRh578=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140510}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {".": {"import": "./wrapper.mjs", "browser": "./browser.js", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "b73b11828d166e9692a9bffe9c01a7e93bab04a8", "scripts": {"lint": "eslint . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "10.5.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "22.0.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^9.0.0", "globals": "^15.0.0", "prettier": "^3.0.0", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^6.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.17.0_1714283577833_0.06254995466962132", "host": "s3://npm-registry-packages"}}, "5.2.4": {"name": "ws", "version": "5.2.4", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@5.2.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "c7bea9f1cfb5f410de50e70e82662e562113f9a7", "tarball": "https://registry.npmjs.org/ws/-/ws-5.2.4.tgz", "fileCount": 14, "integrity": "sha512-fFCejsuC8f9kOSu9FYaOw8CdO68O3h5v0lg4p74o8JqWpwTf9tniOD+nOB78aWoVSS6WptVUmDrp/KPsMVBWFQ==", "signatures": [{"sig": "MEUCIG17wDLyumHSvlbg0aSPqn1Ze7MfPs+MOcRRP11PtaHdAiEAh0qkrM4UlenX10y5WDBLMs4uZoWu1/PJipcUNJ6CLaM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99566}, "main": "index.js", "gitHead": "aa8fe0a93f65a5aad9e9724d14c09950682c46c4", "scripts": {"lint": "eslint .", "test": "eslint . && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "eslint . && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "22.3.0", "dependencies": {"async-limiter": "~1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "~12.0.2", "mocha": "~5.2.0", "eslint": "~4.19.0", "benchmark": "~2.1.2", "bufferutil": "~3.0.0", "utf-8-validate": "~4.0.0", "eslint-plugin-node": "~6.0.0", "eslint-plugin-import": "~2.12.0", "eslint-plugin-promise": "~3.8.0", "eslint-config-standard": "~11.0.0", "eslint-plugin-standard": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws_5.2.4_1718549260218_0.005752261215565602", "host": "s3://npm-registry-packages"}}, "6.2.3": {"name": "ws", "version": "6.2.3", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@6.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "ccc96e4add5fd6fedbc491903075c85c5a11d9ee", "tarball": "https://registry.npmjs.org/ws/-/ws-6.2.3.tgz", "fileCount": 15, "integrity": "sha512-jmTjYU0j60B+vHey6TfR3Z7RD61z/hmxBS3VMSGIrroOWXQEneK1zNuotOUrGyBHQj0yrpsLHPWtigEFd13ndA==", "signatures": [{"sig": "MEUCIGkFeRy7x3qJ0GdsVZoLrWeazphtTFM49T+LRO/G9AcFAiEA3jl2K3Odt4dkDQAyhR+w62xms2cVrmgfvhxrVPeTkeE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101795}, "main": "index.js", "browser": "browser.js", "gitHead": "d87f3b6d3a00513af9bbb74f45ba9183af4e5f43", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yml}\"", "test": "npm run lint && nyc --reporter=html --reporter=text mocha test/*.test.js", "integration": "npm run lint && mocha test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "22.3.0", "dependencies": {"async-limiter": "~1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "~13.3.0", "mocha": "~6.0.0", "eslint": "~5.15.0", "prettier": "~1.16.1", "benchmark": "~2.1.4", "coveralls": "~3.0.3", "bufferutil": "~4.0.0", "utf-8-validate": "~5.0.0", "eslint-config-prettier": "~4.1.0", "eslint-plugin-prettier": "~3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/ws_6.2.3_1718549289923_0.7479194024779352", "host": "s3://npm-registry-packages"}}, "7.5.10": {"name": "ws", "version": "7.5.10", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@7.5.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "58b5c20dc281633f6c19113f39b349bd8bd558d9", "tarball": "https://registry.npmjs.org/ws/-/ws-7.5.10.tgz", "fileCount": 17, "integrity": "sha512-+dbF1tHwZpXcbOJdVOkzLDxZP1ailvSxM6ZweXTegylPny803bFhA+vqBYw4s31NSAk4S2Qz+AKXK9a4wkdjcQ==", "signatures": [{"sig": "MEUCIQDEfeTzpq5+pxz5mP4aqRG0Z6ts9EVyynh3fJynEqGNhQIgN9LMoUBrcSwcgMDf1vSWHkEAB6awYe1wFJrYH3rqA14=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122307}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=8.3.0"}, "gitHead": "d962d70649e393841ee1ed726a8f7ffbe90d0c06", "scripts": {"lint": "eslint --ignore-path .gitignore . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "22.3.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^7.0.0", "eslint": "^7.2.0", "prettier": "^2.0.5", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_7.5.10_1718549308411_0.26075081266851785", "host": "s3://npm-registry-packages"}}, "8.17.1": {"name": "ws", "version": "8.17.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.17.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "9293da530bb548febc95371d90f9c878727d919b", "tarball": "https://registry.npmjs.org/ws/-/ws-8.17.1.tgz", "fileCount": 19, "integrity": "sha512-6XQFvXTkbfUOZOKKILFG1PDK2NDQs4azKQl26T0YS5CxqWLgXajbPZ+h4gZekJyRqFU8pvnbAbbs/3TgRPy+GQ==", "signatures": [{"sig": "MEYCIQDMcIDlznTsRt60C2zj0FWpgV8Zgx07ZBhfTRXpZy7TIwIhAMU0ZFoJR62n0mCuHC6ZemvJqTbQ/4hyBBGEYkLAWZ8a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141358}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {".": {"import": "./wrapper.mjs", "browser": "./browser.js", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "3c56601092872f7d7566989f0e379271afd0e4a1", "scripts": {"lint": "eslint . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "22.3.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^9.0.0", "globals": "^15.0.0", "prettier": "^3.0.0", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^6.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.17.1_1718549350021_0.3756465293663589", "host": "s3://npm-registry-packages"}}, "8.18.0": {"name": "ws", "version": "8.18.0", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.18.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "0d7505a6eafe2b0e712d232b42279f53bc289bbc", "tarball": "https://registry.npmjs.org/ws/-/ws-8.18.0.tgz", "fileCount": 19, "integrity": "sha512-8VbfWfHLbbwu3+N6OKsOMpBdT4kXPDDB9cJk2bJ6mh9ucxdlnNvH1e+roYkKmN9Nxw2yjz7VzeO9oOz2zJ04Pw==", "signatures": [{"sig": "MEYCIQChHsLT6n2I3h8LTFy0sAvEobM7S9JbzbmlfScXzuZP3wIhANBm9/IYxF2yXDx0RxYuWiTFYDP6HPRuJTumbd2sj3A2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 146647}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {".": {"import": "./wrapper.mjs", "browser": "./browser.js", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "976c53c4065c49ede73bfba824caf5a6e0f290cb", "scripts": {"lint": "eslint . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "22.4.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^9.0.0", "globals": "^15.0.0", "prettier": "^3.0.0", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^6.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.18.0_1720025131056_0.4984306819755626", "host": "s3://npm-registry-packages"}}, "8.18.1": {"name": "ws", "version": "8.18.1", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.18.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "ea131d3784e1dfdff91adb0a4a116b127515e3cb", "tarball": "https://registry.npmjs.org/ws/-/ws-8.18.1.tgz", "fileCount": 19, "integrity": "sha512-RKW2aJZMXeMxVpnZ6bck+RswznaxmzdULiBr6KY7XkTnW8uvt0iT9H5DkHUChXrc+uurzwa0rVI16n/Xzjdz1w==", "signatures": [{"sig": "MEYCIQC21L97h4sm2uQcnkG0zH0ceK/1blLCzRsH38XTuVKmrwIhAJX40hyQShZ4piLmikArG7+uJv5D+slp9EIvoyDs6O/t", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 146780}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {".": {"import": "./wrapper.mjs", "browser": "./browser.js", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "b92745a9d6760e6b4b2394bfac78cbcd258a8c8d", "scripts": {"lint": "eslint . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "23.8.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^9.0.0", "globals": "^15.0.0", "prettier": "^3.0.0", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^6.0.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.18.1_1740131026083_0.7213421268590259", "host": "s3://npm-registry-packages-npm-production"}}, "8.18.2": {"name": "ws", "version": "8.18.2", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "ws@8.18.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "dist": {"shasum": "42738b2be57ced85f46154320aabb51ab003705a", "tarball": "https://registry.npmjs.org/ws/-/ws-8.18.2.tgz", "fileCount": 19, "integrity": "sha512-DMricUmwGZUVr++AEAe2uiVM7UoO9MAVZMDu05UQOaUII0lp+zOzLLU4Xqh/JvTqklB1T4uELaaPBKyjE1r4fQ==", "signatures": [{"sig": "MEYCIQDFENJmj95hT9SYHgXsddFImbY04RgWfvrrLHiRkoeeoQIhAJrMYu3DklZI1HFT/JrNqsbVCAF2RlW6I8Ka+ueuqbVT", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 147181}, "main": "index.js", "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "exports": {".": {"import": "./wrapper.mjs", "browser": "./browser.js", "require": "./index.js"}, "./package.json": "./package.json"}, "gitHead": "0eb8535f9b0dc11ac30a9ccb8824c9fc8388f1fd", "scripts": {"lint": "eslint . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\"", "test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/ws.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "directories": {}, "_nodeVersion": "23.11.0", "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "mocha": "^8.4.0", "eslint": "^9.0.0", "globals": "^16.0.0", "prettier": "^3.0.0", "benchmark": "^2.1.4", "bufferutil": "^4.0.1", "utf-8-validate": "^6.0.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/ws_8.18.2_1746249888601_0.7003455953913489", "host": "s3://npm-registry-packages-npm-production"}}, "8.18.3": {"name": "ws", "version": "8.18.3", "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "homepage": "https://github.com/websockets/ws", "bugs": {"url": "https://github.com/websockets/ws/issues"}, "repository": {"type": "git", "url": "git+https://github.com/websockets/ws.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://2x.io"}, "license": "MIT", "main": "index.js", "exports": {".": {"browser": "./browser.js", "import": "./wrapper.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "browser": "browser.js", "engines": {"node": ">=10.0.0"}, "scripts": {"test": "nyc --reporter=lcov --reporter=text mocha --throw-deprecation test/*.test.js", "integration": "mocha --throw-deprecation test/*.integration.js", "lint": "eslint . && prettier --check --ignore-path .gitignore \"**/*.{json,md,yaml,yml}\""}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}, "devDependencies": {"benchmark": "^2.1.4", "bufferutil": "^4.0.1", "eslint": "^9.0.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.0.0", "globals": "^16.0.0", "mocha": "^8.4.0", "nyc": "^15.0.0", "prettier": "^3.0.0", "utf-8-validate": "^6.0.0"}, "_id": "ws@8.18.3", "gitHead": "dabbdec92f4c1f1777689733d477344e3c6c2e67", "_nodeVersion": "24.2.0", "_npmVersion": "11.3.0", "dist": {"integrity": "sha512-PEIGCY5tSlUt50cqyMXfCzX+oOPqN0vuGqWzbcJ2xvnkzkq46oOpz7dQaTDBdfICb4N14+GARUDw2XV2N4tvzg==", "shasum": "b56b88abffde62791c639170400c93dcb0c95472", "tarball": "https://registry.npmjs.org/ws/-/ws-8.18.3.tgz", "fileCount": 19, "unpackedSize": 147320, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCOI8Ahuznntqkj7aQHeI0py7meINUYMVQuSqJPveXQEwIgAbA99veIj4WX6LEVQWYmY8Lb5rch0QLMXRs34KiB4VQ="}]}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com", "actor": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/ws_8.18.3_1751117318410_0.24803190573819767"}, "_hasShrinkwrap": false}}, "time": {"created": "2011-12-04T10:32:12.839Z", "modified": "2025-06-28T13:28:38.787Z", "0.2.6": "2011-12-04T10:32:14.627Z", "0.2.7": "2011-12-07T12:28:11.002Z", "0.2.8": "2011-12-07T12:52:53.588Z", "0.2.9": "2011-12-07T14:42:17.238Z", "0.3.0": "2011-12-08T13:30:10.031Z", "0.3.1": "2011-12-08T19:39:18.407Z", "0.3.2": "2011-12-11T22:40:27.266Z", "0.3.3": "2011-12-12T08:02:31.982Z", "0.3.4": "2011-12-12T10:09:11.389Z", "0.3.4-2": "2011-12-12T11:31:05.010Z", "0.3.5": "2011-12-13T11:55:41.773Z", "0.3.5-2": "2011-12-13T14:53:45.350Z", "0.3.5-3": "2011-12-13T18:32:37.432Z", "0.3.5-4": "2011-12-13T22:21:57.061Z", "0.3.6": "2011-12-18T15:11:35.570Z", "0.3.7": "2011-12-25T17:19:41.266Z", "0.3.8": "2011-12-27T12:11:50.105Z", "0.3.9": "2012-01-01T18:30:20.877Z", "0.4.0": "2012-01-02T15:36:57.279Z", "0.4.1": "2012-01-26T14:20:32.517Z", "0.4.2": "2012-02-04T14:38:48.103Z", "0.4.3": "2012-02-04T16:14:50.288Z", "0.4.4": "2012-02-06T22:32:59.238Z", "0.4.5": "2012-02-07T08:16:04.845Z", "0.4.6": "2012-02-09T13:34:33.670Z", "0.4.7": "2012-02-21T21:54:57.903Z", "0.4.8": "2012-02-29T14:06:15.918Z", "0.4.9": "2012-03-21T11:40:30.002Z", "0.4.10": "2012-03-23T07:37:54.654Z", "0.4.11": "2012-03-24T17:22:27.603Z", "0.4.12": "2012-03-30T19:41:51.096Z", "0.4.13": "2012-04-12T12:04:01.268Z", "0.4.14": "2012-04-30T23:19:41.222Z", "0.4.15": "2012-05-20T10:28:38.221Z", "0.4.16": "2012-06-01T09:35:19.744Z", "0.4.17": "2012-06-13T11:08:11.928Z", "0.4.18": "2012-06-14T12:02:03.774Z", "0.4.19": "2012-06-19T16:45:21.369Z", "0.4.20": "2012-06-26T16:32:36.414Z", "0.4.21": "2012-07-14T15:22:34.585Z", "0.4.22": "2012-10-03T12:42:28.801Z", "0.4.23": "2012-11-19T20:28:09.515Z", "0.4.24": "2012-12-11T19:49:53.640Z", "0.4.25": "2012-12-17T20:55:26.473Z", "0.4.27": "2013-06-27T20:01:33.222Z", "0.4.28": "2013-08-16T16:15:28.415Z", "0.4.29": "2013-08-23T07:26:29.761Z", "0.4.30": "2013-08-30T21:07:28.653Z", "0.4.31": "2013-09-23T06:55:10.020Z", "0.4.32": "2014-08-06T11:23:54.914Z", "0.5.0": "2014-11-20T21:44:01.711Z", "0.6.0": "2014-12-05T15:11:49.451Z", "0.6.1": "2014-12-06T21:53:40.640Z", "0.6.2": "2014-12-06T21:55:13.987Z", "0.6.3": "2014-12-08T21:21:03.063Z", "0.6.4": "2014-12-28T13:49:40.206Z", "0.6.5": "2015-01-05T17:07:37.686Z", "0.7.0": "2015-01-22T16:29:53.723Z", "0.7.1": "2015-01-29T12:06:29.379Z", "0.7.2": "2015-05-14T20:21:05.058Z", "0.8.0": "2015-08-21T11:57:09.538Z", "0.8.1": "2015-11-29T19:24:31.667Z", "1.0.0": "2015-12-30T16:35:14.225Z", "1.0.1": "2016-01-04T12:36:08.428Z", "1.1.0": "2016-04-11T12:00:23.506Z", "1.1.1": "2016-06-24T12:22:40.082Z", "2.0.0-beta.0": "2017-01-10T14:28:34.020Z", "2.0.0-beta.1": "2017-01-14T16:29:38.848Z", "2.0.0-beta.2": "2017-01-25T08:52:49.450Z", "2.0.0": "2017-01-30T11:57:46.414Z", "2.0.1": "2017-02-01T10:40:07.816Z", "2.0.2": "2017-02-03T17:29:39.305Z", "2.0.3": "2017-02-08T13:44:50.506Z", "1.1.2": "2017-02-13T06:43:25.287Z", "2.1.0": "2017-02-18T07:24:50.320Z", "2.2.0": "2017-02-28T09:29:36.473Z", "1.1.3": "2017-03-11T11:01:09.134Z", "1.1.4": "2017-03-11T11:15:25.657Z", "2.2.1": "2017-03-13T07:05:40.303Z", "2.2.2": "2017-03-21T21:06:54.249Z", "2.2.3": "2017-04-03T10:10:19.890Z", "2.3.0": "2017-04-20T14:05:25.723Z", "2.3.1": "2017-04-20T18:00:03.846Z", "3.0.0": "2017-05-17T07:39:31.433Z", "3.1.0": "2017-07-27T06:16:53.879Z", "3.2.0": "2017-09-15T06:22:17.336Z", "3.3.0": "2017-11-04T09:43:47.287Z", "1.1.5": "2017-11-08T16:55:03.162Z", "3.3.1": "2017-11-08T16:58:34.343Z", "3.3.2": "2017-11-21T07:19:22.938Z", "3.3.3": "2017-12-17T09:49:16.567Z", "4.0.0": "2018-01-05T10:04:15.095Z", "4.1.0": "2018-02-22T08:08:03.018Z", "5.0.0": "2018-03-06T14:34:11.077Z", "5.1.0": "2018-03-19T17:50:49.143Z", "5.1.1": "2018-04-02T13:40:49.002Z", "5.2.0": "2018-05-21T19:20:51.402Z", "5.2.1": "2018-06-23T16:11:55.991Z", "5.2.2": "2018-07-11T19:58:18.186Z", "6.0.0": "2018-07-21T13:45:30.262Z", "6.1.0": "2018-10-05T06:52:08.987Z", "6.1.1": "2018-11-17T07:12:28.865Z", "6.1.2": "2018-11-17T20:04:38.782Z", "6.1.3": "2019-01-24T07:11:26.491Z", "6.1.4": "2019-02-16T17:07:20.241Z", "6.2.0": "2019-03-06T07:38:25.808Z", "6.2.1": "2019-03-27T08:53:30.408Z", "7.0.0": "2019-04-30T16:12:14.228Z", "7.0.1": "2019-06-17T16:16:20.281Z", "7.1.0": "2019-07-08T16:11:23.119Z", "7.1.1": "2019-07-19T14:46:55.813Z", "7.1.2": "2019-08-12T15:49:30.657Z", "7.2.0": "2019-10-19T14:24:50.430Z", "7.2.1": "2019-12-14T09:13:31.580Z", "7.2.2": "2020-03-08T06:54:26.258Z", "7.2.3": "2020-03-09T17:27:32.677Z", "7.2.5": "2020-04-25T10:52:57.382Z", "7.3.0": "2020-05-10T05:36:42.029Z", "7.3.1": "2020-07-05T05:31:21.536Z", "7.4.0": "2020-11-08T07:10:37.181Z", "7.4.1": "2020-12-04T20:48:07.177Z", "7.4.2": "2020-12-29T20:19:39.250Z", "7.4.3": "2021-02-02T19:21:50.145Z", "7.4.4": "2021-03-06T20:47:08.570Z", "7.4.5": "2021-04-18T08:22:24.041Z", "7.4.6": "2021-05-25T16:29:58.730Z", "6.2.2": "2021-06-01T18:34:52.685Z", "5.2.3": "2021-06-08T19:27:11.834Z", "7.5.0": "2021-06-16T13:18:12.356Z", "7.5.1": "2021-06-29T05:17:58.072Z", "7.5.2": "2021-07-04T05:37:08.825Z", "7.5.3": "2021-07-10T06:01:42.420Z", "8.0.0": "2021-07-28T18:13:36.766Z", "8.1.0": "2021-08-11T20:04:46.035Z", "8.2.0": "2021-08-18T05:24:37.452Z", "7.5.4": "2021-08-28T16:10:23.070Z", "8.2.1": "2021-08-28T16:11:24.840Z", "7.5.5": "2021-09-08T19:49:43.776Z", "8.2.2": "2021-09-08T19:50:08.556Z", "8.2.3": "2021-10-02T18:40:10.039Z", "8.3.0": "2021-11-23T18:01:09.784Z", "7.5.6": "2021-11-23T19:45:42.539Z", "8.4.0": "2021-12-20T20:13:39.761Z", "8.4.1": "2022-01-13T20:09:26.459Z", "8.4.2": "2022-01-14T14:38:26.067Z", "7.5.7": "2022-02-07T19:43:39.492Z", "8.5.0": "2022-02-07T19:44:22.138Z", "8.6.0": "2022-05-01T19:09:35.949Z", "7.5.8": "2022-05-26T17:29:59.661Z", "8.7.0": "2022-05-26T17:30:26.965Z", "8.8.0": "2022-06-09T19:00:36.908Z", "7.5.9": "2022-07-15T17:15:10.677Z", "8.8.1": "2022-07-15T17:15:27.842Z", "8.9.0": "2022-09-22T19:33:48.342Z", "8.10.0": "2022-10-24T19:09:14.174Z", "8.11.0": "2022-11-06T20:02:55.232Z", "8.12.0": "2023-01-07T19:45:55.760Z", "8.12.1": "2023-02-13T20:38:37.660Z", "8.13.0": "2023-03-10T17:57:25.728Z", "8.14.0": "2023-09-06T14:05:15.836Z", "8.14.1": "2023-09-08T16:04:44.943Z", "8.14.2": "2023-09-19T15:32:45.009Z", "8.15.0": "2023-12-09T18:12:36.901Z", "8.15.1": "2023-12-12T20:13:49.299Z", "8.16.0": "2023-12-26T15:48:24.452Z", "8.17.0": "2024-04-28T05:52:58.033Z", "5.2.4": "2024-06-16T14:47:40.369Z", "6.2.3": "2024-06-16T14:48:10.064Z", "7.5.10": "2024-06-16T14:48:28.590Z", "8.17.1": "2024-06-16T14:49:10.217Z", "8.18.0": "2024-07-03T16:45:31.280Z", "8.18.1": "2025-02-21T09:43:46.292Z", "8.18.2": "2025-05-03T05:24:48.780Z", "8.18.3": "2025-06-28T13:28:38.606Z"}, "bugs": {"url": "https://github.com/websockets/ws/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://2x.io"}, "license": "MIT", "homepage": "https://github.com/websockets/ws", "keywords": ["HyBi", "<PERSON><PERSON>", "RFC-6455", "WebSocket", "WebSockets", "real-time"], "repository": {"type": "git", "url": "git+https://github.com/websockets/ws.git"}, "description": "Simple to use, blazing fast and thoroughly tested websocket client and server for Node.js", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# ws: a Node.js WebSocket library\n\n[![Version npm](https://img.shields.io/npm/v/ws.svg?logo=npm)](https://www.npmjs.com/package/ws)\n[![CI](https://img.shields.io/github/actions/workflow/status/websockets/ws/ci.yml?branch=master&label=CI&logo=github)](https://github.com/websockets/ws/actions?query=workflow%3ACI+branch%3Amaster)\n[![Coverage Status](https://img.shields.io/coveralls/websockets/ws/master.svg?logo=coveralls)](https://coveralls.io/github/websockets/ws)\n\nws is a simple to use, blazing fast, and thoroughly tested WebSocket client and\nserver implementation.\n\nPasses the quite extensive Autobahn test suite: [server][server-report],\n[client][client-report].\n\n**Note**: This module does not work in the browser. The client in the docs is a\nreference to a backend with the role of a client in the WebSocket communication.\nBrowser clients must use the native\n[`WebSocket`](https://developer.mozilla.org/en-US/docs/Web/API/WebSocket)\nobject. To make the same code work seamlessly on Node.js and the browser, you\ncan use one of the many wrappers available on npm, like\n[isomorphic-ws](https://github.com/heineiuo/isomorphic-ws).\n\n## Table of Contents\n\n- [Protocol support](#protocol-support)\n- [Installing](#installing)\n  - [Opt-in for performance](#opt-in-for-performance)\n    - [Legacy opt-in for performance](#legacy-opt-in-for-performance)\n- [API docs](#api-docs)\n- [WebSocket compression](#websocket-compression)\n- [Usage examples](#usage-examples)\n  - [Sending and receiving text data](#sending-and-receiving-text-data)\n  - [Sending binary data](#sending-binary-data)\n  - [Simple server](#simple-server)\n  - [External HTTP/S server](#external-https-server)\n  - [Multiple servers sharing a single HTTP/S server](#multiple-servers-sharing-a-single-https-server)\n  - [Client authentication](#client-authentication)\n  - [Server broadcast](#server-broadcast)\n  - [Round-trip time](#round-trip-time)\n  - [Use the Node.js streams API](#use-the-nodejs-streams-api)\n  - [Other examples](#other-examples)\n- [FAQ](#faq)\n  - [How to get the IP address of the client?](#how-to-get-the-ip-address-of-the-client)\n  - [How to detect and close broken connections?](#how-to-detect-and-close-broken-connections)\n  - [How to connect via a proxy?](#how-to-connect-via-a-proxy)\n- [Changelog](#changelog)\n- [License](#license)\n\n## Protocol support\n\n- **HyBi drafts 07-12** (Use the option `protocolVersion: 8`)\n- **HyBi drafts 13-17** (Current default, alternatively option\n  `protocolVersion: 13`)\n\n## Installing\n\n```\nnpm install ws\n```\n\n### Opt-in for performance\n\n[bufferutil][] is an optional module that can be installed alongside the ws\nmodule:\n\n```\nnpm install --save-optional bufferutil\n```\n\nThis is a binary addon that improves the performance of certain operations such\nas masking and unmasking the data payload of the WebSocket frames. Prebuilt\nbinaries are available for the most popular platforms, so you don't necessarily\nneed to have a C++ compiler installed on your machine.\n\nTo force ws to not use bufferutil, use the\n[`WS_NO_BUFFER_UTIL`](./doc/ws.md#ws_no_buffer_util) environment variable. This\ncan be useful to enhance security in systems where a user can put a package in\nthe package search path of an application of another user, due to how the\nNode.js resolver algorithm works.\n\n#### Legacy opt-in for performance\n\nIf you are running on an old version of Node.js (prior to v18.14.0), ws also\nsupports the [utf-8-validate][] module:\n\n```\nnpm install --save-optional utf-8-validate\n```\n\nThis contains a binary polyfill for [`buffer.isUtf8()`][].\n\nTo force ws not to use utf-8-validate, use the\n[`WS_NO_UTF_8_VALIDATE`](./doc/ws.md#ws_no_utf_8_validate) environment variable.\n\n## API docs\n\nSee [`/doc/ws.md`](./doc/ws.md) for Node.js-like documentation of ws classes and\nutility functions.\n\n## WebSocket compression\n\nws supports the [permessage-deflate extension][permessage-deflate] which enables\nthe client and server to negotiate a compression algorithm and its parameters,\nand then selectively apply it to the data payloads of each WebSocket message.\n\nThe extension is disabled by default on the server and enabled by default on the\nclient. It adds a significant overhead in terms of performance and memory\nconsumption so we suggest to enable it only if it is really needed.\n\nNote that Node.js has a variety of issues with high-performance compression,\nwhere increased concurrency, especially on Linux, can lead to [catastrophic\nmemory fragmentation][node-zlib-bug] and slow performance. If you intend to use\npermessage-deflate in production, it is worthwhile to set up a test\nrepresentative of your workload and ensure Node.js/zlib will handle it with\nacceptable performance and memory usage.\n\nTuning of permessage-deflate can be done via the options defined below. You can\nalso use `zlibDeflateOptions` and `zlibInflateOptions`, which is passed directly\ninto the creation of [raw deflate/inflate streams][node-zlib-deflaterawdocs].\n\nSee [the docs][ws-server-options] for more options.\n\n```js\nimport WebSocket, { WebSocketServer } from 'ws';\n\nconst wss = new WebSocketServer({\n  port: 8080,\n  perMessageDeflate: {\n    zlibDeflateOptions: {\n      // See zlib defaults.\n      chunkSize: 1024,\n      memLevel: 7,\n      level: 3\n    },\n    zlibInflateOptions: {\n      chunkSize: 10 * 1024\n    },\n    // Other options settable:\n    clientNoContextTakeover: true, // Defaults to negotiated value.\n    serverNoContextTakeover: true, // Defaults to negotiated value.\n    serverMaxWindowBits: 10, // Defaults to negotiated value.\n    // Below options specified as default values.\n    concurrencyLimit: 10, // Limits zlib concurrency for perf.\n    threshold: 1024 // Size (in bytes) below which messages\n    // should not be compressed if context takeover is disabled.\n  }\n});\n```\n\nThe client will only use the extension if it is supported and enabled on the\nserver. To always disable the extension on the client, set the\n`perMessageDeflate` option to `false`.\n\n```js\nimport WebSocket from 'ws';\n\nconst ws = new WebSocket('ws://www.host.com/path', {\n  perMessageDeflate: false\n});\n```\n\n## Usage examples\n\n### Sending and receiving text data\n\n```js\nimport WebSocket from 'ws';\n\nconst ws = new WebSocket('ws://www.host.com/path');\n\nws.on('error', console.error);\n\nws.on('open', function open() {\n  ws.send('something');\n});\n\nws.on('message', function message(data) {\n  console.log('received: %s', data);\n});\n```\n\n### Sending binary data\n\n```js\nimport WebSocket from 'ws';\n\nconst ws = new WebSocket('ws://www.host.com/path');\n\nws.on('error', console.error);\n\nws.on('open', function open() {\n  const array = new Float32Array(5);\n\n  for (var i = 0; i < array.length; ++i) {\n    array[i] = i / 2;\n  }\n\n  ws.send(array);\n});\n```\n\n### Simple server\n\n```js\nimport { WebSocketServer } from 'ws';\n\nconst wss = new WebSocketServer({ port: 8080 });\n\nwss.on('connection', function connection(ws) {\n  ws.on('error', console.error);\n\n  ws.on('message', function message(data) {\n    console.log('received: %s', data);\n  });\n\n  ws.send('something');\n});\n```\n\n### External HTTP/S server\n\n```js\nimport { createServer } from 'https';\nimport { readFileSync } from 'fs';\nimport { WebSocketServer } from 'ws';\n\nconst server = createServer({\n  cert: readFileSync('/path/to/cert.pem'),\n  key: readFileSync('/path/to/key.pem')\n});\nconst wss = new WebSocketServer({ server });\n\nwss.on('connection', function connection(ws) {\n  ws.on('error', console.error);\n\n  ws.on('message', function message(data) {\n    console.log('received: %s', data);\n  });\n\n  ws.send('something');\n});\n\nserver.listen(8080);\n```\n\n### Multiple servers sharing a single HTTP/S server\n\n```js\nimport { createServer } from 'http';\nimport { WebSocketServer } from 'ws';\n\nconst server = createServer();\nconst wss1 = new WebSocketServer({ noServer: true });\nconst wss2 = new WebSocketServer({ noServer: true });\n\nwss1.on('connection', function connection(ws) {\n  ws.on('error', console.error);\n\n  // ...\n});\n\nwss2.on('connection', function connection(ws) {\n  ws.on('error', console.error);\n\n  // ...\n});\n\nserver.on('upgrade', function upgrade(request, socket, head) {\n  const { pathname } = new URL(request.url, 'wss://base.url');\n\n  if (pathname === '/foo') {\n    wss1.handleUpgrade(request, socket, head, function done(ws) {\n      wss1.emit('connection', ws, request);\n    });\n  } else if (pathname === '/bar') {\n    wss2.handleUpgrade(request, socket, head, function done(ws) {\n      wss2.emit('connection', ws, request);\n    });\n  } else {\n    socket.destroy();\n  }\n});\n\nserver.listen(8080);\n```\n\n### Client authentication\n\n```js\nimport { createServer } from 'http';\nimport { WebSocketServer } from 'ws';\n\nfunction onSocketError(err) {\n  console.error(err);\n}\n\nconst server = createServer();\nconst wss = new WebSocketServer({ noServer: true });\n\nwss.on('connection', function connection(ws, request, client) {\n  ws.on('error', console.error);\n\n  ws.on('message', function message(data) {\n    console.log(`Received message ${data} from user ${client}`);\n  });\n});\n\nserver.on('upgrade', function upgrade(request, socket, head) {\n  socket.on('error', onSocketError);\n\n  // This function is not defined on purpose. Implement it with your own logic.\n  authenticate(request, function next(err, client) {\n    if (err || !client) {\n      socket.write('HTTP/1.1 401 Unauthorized\\r\\n\\r\\n');\n      socket.destroy();\n      return;\n    }\n\n    socket.removeListener('error', onSocketError);\n\n    wss.handleUpgrade(request, socket, head, function done(ws) {\n      wss.emit('connection', ws, request, client);\n    });\n  });\n});\n\nserver.listen(8080);\n```\n\nAlso see the provided [example][session-parse-example] using `express-session`.\n\n### Server broadcast\n\nA client WebSocket broadcasting to all connected WebSocket clients, including\nitself.\n\n```js\nimport WebSocket, { WebSocketServer } from 'ws';\n\nconst wss = new WebSocketServer({ port: 8080 });\n\nwss.on('connection', function connection(ws) {\n  ws.on('error', console.error);\n\n  ws.on('message', function message(data, isBinary) {\n    wss.clients.forEach(function each(client) {\n      if (client.readyState === WebSocket.OPEN) {\n        client.send(data, { binary: isBinary });\n      }\n    });\n  });\n});\n```\n\nA client WebSocket broadcasting to every other connected WebSocket clients,\nexcluding itself.\n\n```js\nimport WebSocket, { WebSocketServer } from 'ws';\n\nconst wss = new WebSocketServer({ port: 8080 });\n\nwss.on('connection', function connection(ws) {\n  ws.on('error', console.error);\n\n  ws.on('message', function message(data, isBinary) {\n    wss.clients.forEach(function each(client) {\n      if (client !== ws && client.readyState === WebSocket.OPEN) {\n        client.send(data, { binary: isBinary });\n      }\n    });\n  });\n});\n```\n\n### Round-trip time\n\n```js\nimport WebSocket from 'ws';\n\nconst ws = new WebSocket('wss://websocket-echo.com/');\n\nws.on('error', console.error);\n\nws.on('open', function open() {\n  console.log('connected');\n  ws.send(Date.now());\n});\n\nws.on('close', function close() {\n  console.log('disconnected');\n});\n\nws.on('message', function message(data) {\n  console.log(`Round-trip time: ${Date.now() - data} ms`);\n\n  setTimeout(function timeout() {\n    ws.send(Date.now());\n  }, 500);\n});\n```\n\n### Use the Node.js streams API\n\n```js\nimport WebSocket, { createWebSocketStream } from 'ws';\n\nconst ws = new WebSocket('wss://websocket-echo.com/');\n\nconst duplex = createWebSocketStream(ws, { encoding: 'utf8' });\n\nduplex.on('error', console.error);\n\nduplex.pipe(process.stdout);\nprocess.stdin.pipe(duplex);\n```\n\n### Other examples\n\nFor a full example with a browser client communicating with a ws server, see the\nexamples folder.\n\nOtherwise, see the test cases.\n\n## FAQ\n\n### How to get the IP address of the client?\n\nThe remote IP address can be obtained from the raw socket.\n\n```js\nimport { WebSocketServer } from 'ws';\n\nconst wss = new WebSocketServer({ port: 8080 });\n\nwss.on('connection', function connection(ws, req) {\n  const ip = req.socket.remoteAddress;\n\n  ws.on('error', console.error);\n});\n```\n\nWhen the server runs behind a proxy like NGINX, the de-facto standard is to use\nthe `X-Forwarded-For` header.\n\n```js\nwss.on('connection', function connection(ws, req) {\n  const ip = req.headers['x-forwarded-for'].split(',')[0].trim();\n\n  ws.on('error', console.error);\n});\n```\n\n### How to detect and close broken connections?\n\nSometimes, the link between the server and the client can be interrupted in a\nway that keeps both the server and the client unaware of the broken state of the\nconnection (e.g. when pulling the cord).\n\nIn these cases, ping messages can be used as a means to verify that the remote\nendpoint is still responsive.\n\n```js\nimport { WebSocketServer } from 'ws';\n\nfunction heartbeat() {\n  this.isAlive = true;\n}\n\nconst wss = new WebSocketServer({ port: 8080 });\n\nwss.on('connection', function connection(ws) {\n  ws.isAlive = true;\n  ws.on('error', console.error);\n  ws.on('pong', heartbeat);\n});\n\nconst interval = setInterval(function ping() {\n  wss.clients.forEach(function each(ws) {\n    if (ws.isAlive === false) return ws.terminate();\n\n    ws.isAlive = false;\n    ws.ping();\n  });\n}, 30000);\n\nwss.on('close', function close() {\n  clearInterval(interval);\n});\n```\n\nPong messages are automatically sent in response to ping messages as required by\nthe spec.\n\nJust like the server example above, your clients might as well lose connection\nwithout knowing it. You might want to add a ping listener on your clients to\nprevent that. A simple implementation would be:\n\n```js\nimport WebSocket from 'ws';\n\nfunction heartbeat() {\n  clearTimeout(this.pingTimeout);\n\n  // Use `WebSocket#terminate()`, which immediately destroys the connection,\n  // instead of `WebSocket#close()`, which waits for the close timer.\n  // Delay should be equal to the interval at which your server\n  // sends out pings plus a conservative assumption of the latency.\n  this.pingTimeout = setTimeout(() => {\n    this.terminate();\n  }, 30000 + 1000);\n}\n\nconst client = new WebSocket('wss://websocket-echo.com/');\n\nclient.on('error', console.error);\nclient.on('open', heartbeat);\nclient.on('ping', heartbeat);\nclient.on('close', function clear() {\n  clearTimeout(this.pingTimeout);\n});\n```\n\n### How to connect via a proxy?\n\nUse a custom `http.Agent` implementation like [https-proxy-agent][] or\n[socks-proxy-agent][].\n\n## Changelog\n\nWe're using the GitHub [releases][changelog] for changelog entries.\n\n## License\n\n[MIT](LICENSE)\n\n[`buffer.isutf8()`]: https://nodejs.org/api/buffer.html#bufferisutf8input\n[bufferutil]: https://github.com/websockets/bufferutil\n[changelog]: https://github.com/websockets/ws/releases\n[client-report]: http://websockets.github.io/ws/autobahn/clients/\n[https-proxy-agent]: https://github.com/TooTallNate/node-https-proxy-agent\n[node-zlib-bug]: https://github.com/nodejs/node/issues/8871\n[node-zlib-deflaterawdocs]:\n  https://nodejs.org/api/zlib.html#zlib_zlib_createdeflateraw_options\n[permessage-deflate]: https://tools.ietf.org/html/rfc7692\n[server-report]: http://websockets.github.io/ws/autobahn/servers/\n[session-parse-example]: ./examples/express-session-parse\n[socks-proxy-agent]: https://github.com/TooTallNate/node-socks-proxy-agent\n[utf-8-validate]: https://github.com/websockets/utf-8-validate\n[ws-server-options]: ./doc/ws.md#new-websocketserveroptions-callback\n", "readmeFilename": "README.md", "users": {"V1": true, "ajk": true, "kss": true, "luk": true, "vbv": true, "bcoe": true, "bret": true, "dofy": true, "dwqs": true, "hain": true, "meeh": true, "mktj": true, "mo2c": true, "shan": true, "clemo": true, "doruk": true, "hanhq": true, "liigo": true, "miloc": true, "nguru": true, "ph3nx": true, "raojs": true, "romac": true, "segen": true, "sopov": true, "stany": true, "suncn": true, "456wyc": true, "71emj1": true, "akarem": true, "apopek": true, "brettv": true, "broofa": true, "chimit": true, "dankle": true, "deemsk": true, "dkblay": true, "dudley": true, "emarcs": true, "ethan_": true, "frankg": true, "gabeio": true, "h02e56": true, "hitesh": true, "hyteer": true, "joe.li": true, "kastor": true, "laklin": true, "maxidr": true, "maysay": true, "maziar": true, "mihaiv": true, "saru95": true, "satoru": true, "sirrah": true, "smituk": true, "snarky": true, "sobear": true, "tcrowe": true, "vladan": true, "windyh": true, "yangzw": true, "yunxao": true, "zlatip": true, "alectic": true, "anoubis": true, "antanst": true, "antitim": true, "barenko": true, "chalker": true, "da5atar": true, "enhezzz": true, "farhadi": true, "fatelei": true, "funroll": true, "gazilla": true, "huytard": true, "io2work": true, "itsjimi": true, "ivansky": true, "jerrywu": true, "jez9999": true, "kahboom": true, "maxwang": true, "mdrobny": true, "mygoare": true, "nilz3ro": true, "nunogee": true, "sahilsk": true, "sharper": true, "shmeeny": true, "taseenb": true, "tondy67": true, "tsxuehu": true, "wouldgo": true, "writech": true, "x0000ff": true, "xfloops": true, "zarkone": true, "1cr18ni9": true, "abarroso": true, "adaliszk": true, "alikadir": true, "bagpommy": true, "behajian": true, "bfattori": true, "ckross01": true, "ctaggart": true, "dimonfox": true, "djviolin": true, "e23jiang": true, "esundahl": true, "gchudnov": true, "gigerlin": true, "gvr37leo": true, "harry-sm": true, "hollobit": true, "huiyifyj": true, "ifeature": true, "jhuckaby": true, "jimknopf": true, "jpillora": true, "kenlimmj": true, "koskokos": true, "leggiero": true, "losymear": true, "makediff": true, "masayuki": true, "nestfall": true, "npmrud5g": true, "pddivine": true, "piascikj": true, "rochejul": true, "ronin161": true, "santihbc": true, "sedoyjan": true, "sekwah41": true, "shawnt22": true, "suemcnab": true, "superjoe": true, "suprsidr": true, "tonyleen": true, "wozhizui": true, "ycjcl868": true, "zalithka": true, "zeusdeux": true, "abalandin": true, "appastair": true, "blitzprog": true, "blunt1337": true, "brave_cgx": true, "ezcabrera": true, "fgribreau": true, "guananddu": true, "heartnett": true, "jesusgoku": true, "jhohlfeld": true, "jonlailam": true, "juriwiens": true, "kermit666": true, "largepuma": true, "magicmind": true, "maxorelus": true, "mojaray2k": true, "mr-smiley": true, "restmount": true, "shakakira": true, "sunnylost": true, "travis346": true, "ukrbublik": true, "webnicola": true, "xxsnake28": true, "ashish.npm": true, "cestrensem": true, "clhynfield": true, "donvercety": true, "f124275809": true, "from-nibly": true, "genediazjr": true, "guzhongren": true, "hengkiardo": true, "henrytseng": true, "huangssssx": true, "jonabasque": true, "kreshikhin": true, "lewisbrown": true, "maxmaximov": true, "metakermit": true, "nickleefly": true, "oneapm.com": true, "quocnguyen": true, "rocket0191": true, "seangenabe": true, "tangweikun": true, "tomitoivio": true, "amirmehmood": true, "arkanciscan": true, "evert-arias": true, "frknbasaran": true, "galenandrew": true, "garenyondem": true, "ioncreature": true, "mseminatore": true, "ne_sachirou": true, "onsentamago": true, "paulferrett": true, "phoenix-xsy": true, "psychollama": true, "raisiqueira": true, "reergymerej": true, "sethbergman": true, "ww522413622": true, "xinwangwang": true, "yujiikebata": true, "bobjohnson23": true, "conantonakos": true, "eventhorizon": true, "felixmcfelix": true, "guardsystems": true, "ivangaravito": true, "jamescostian": true, "justintormey": true, "loselovegirl": true, "nickeltobias": true, "pickledonion": true, "prabhash1785": true, "shaomingquan": true, "thomas.miele": true, "zhenguo.zhao": true, "anudeepakumar": true, "chinawolf_wyp": true, "danielpavelic": true, "david-ruzicka": true, "diegorbaquero": true, "jasonwang1888": true, "korekara88730": true, "pablo.tavarez": true, "scottfreecode": true, "serge-nikitin": true, "tjholowaychuk": true, "bigglesatlarge": true, "danielbankhead": true, "johnsmithcoder": true, "kamikadze4game": true, "philliphenslee": true, "shanewholloway": true, "andygreenegrass": true, "mechanicalhuman": true, "pensierinmusica": true, "valeriu-zdrobau": true, "animustechnology": true, "horrorandtropics": true, "volodymyr.sichka": true, "scott.m.sarsfield": true, "timur.shemsedinov": true}}