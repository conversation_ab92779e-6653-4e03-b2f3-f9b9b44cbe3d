{"_id": "@types/graceful-fs", "_rev": "582-6d529d001f1f93e4dbdca242f975977e", "name": "@types/graceful-fs", "dist-tags": {"ts2.0": "4.1.1", "ts2.1": "4.1.1", "ts2.2": "4.1.3", "ts2.3": "4.1.3", "ts2.4": "4.1.3", "ts2.5": "4.1.3", "ts2.6": "4.1.3", "ts2.7": "4.1.3", "ts2.8": "4.1.3", "ts2.9": "4.1.3", "ts3.0": "4.1.3", "ts3.1": "4.1.3", "ts3.2": "4.1.4", "ts3.3": "4.1.4", "ts3.4": "4.1.4", "ts3.5": "4.1.4", "ts3.6": "4.1.4", "ts3.7": "4.1.4", "ts3.8": "4.1.4", "ts3.9": "4.1.5", "ts4.0": "4.1.5", "ts4.1": "4.1.5", "ts4.2": "4.1.6", "ts4.3": "4.1.6", "ts4.4": "4.1.6", "ts5.8": "4.1.9", "ts5.7": "4.1.9", "latest": "4.1.9", "ts4.5": "4.1.9", "ts4.6": "4.1.9", "ts4.7": "4.1.9", "ts4.8": "4.1.9", "ts4.9": "4.1.9", "ts5.0": "4.1.9", "ts5.1": "4.1.9", "ts5.2": "4.1.9", "ts5.3": "4.1.9", "ts5.4": "4.1.9", "ts5.5": "4.1.9", "ts5.6": "4.1.9", "ts5.9": "4.1.9"}, "versions": {"2.0.15-alpha": {"name": "@types/graceful-fs", "version": "2.0.15-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/Bartvds"}, "license": "MIT", "_id": "@types/graceful-fs@2.0.15-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "247129cb8979991256ce44af47e3968383f8d7e0", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-2.0.15-alpha.tgz", "integrity": "sha512-El3KdS5MjwApFVS4zcNCUGJkifwmDd9WphJ7u2f+5b955YFo6QbD/Riei8PH+7uArzs5eceBX221VCkHLovTfg==", "signatures": [{"sig": "MEYCIQCIAMtL22EFOw5uaUwmm2+027fxyGY7UyPImyJCQeTuQAIhAM0O6/FCS+FxepyE5aR0pAB0haIJzyc116jCKxUgnSrk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\graceful-fs", "_shasum": "247129cb8979991256ce44af47e3968383f8d7e0", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\graceful-fs", "_npmVersion": "3.8.2", "description": "Type definitions for graceful-fs 2.0.0 from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"equire('fs'": "*"}, "_npmOperationalInternal": {"tmp": "tmp/graceful-fs-2.0.15-alpha.tgz_1463461373661_0.39980470086447895", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.16-alpha": {"name": "@types/graceful-fs", "version": "2.0.16-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/Bartvds"}, "license": "MIT", "_id": "@types/graceful-fs@2.0.16-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "b29497a24f5521440e3c490dc2ab773fe9611765", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-2.0.16-alpha.tgz", "integrity": "sha512-WRXLir6XQF4TM1eqIznDAVLQdSzsiedRkJQdzRxL9vWbHmoLRF7kKFaV+vh0cEudIUgfTNpP+U9P1NpJ3O2OZw==", "signatures": [{"sig": "MEUCIC0bjs9UIMkNbvDELeW9/fw2Posj+L4lQ5SC1KeKTwYjAiEA5RRWeEaCZaWUYsMF88SdGdGZYd+nwgC30XlbDoN0d0w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\graceful-fs", "_shasum": "b29497a24f5521440e3c490dc2ab773fe9611765", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\graceful-fs", "_npmVersion": "3.8.2", "description": "Type definitions for graceful-fs 2.0.0 from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"@types/node": "*", "equire('fs'": "*"}, "_npmOperationalInternal": {"tmp": "tmp/graceful-fs-2.0.16-alpha.tgz_1463691648830_0.9923840221017599", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.21-alpha": {"name": "@types/graceful-fs", "version": "2.0.21-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/Bartvds"}, "license": "MIT", "_id": "@types/graceful-fs@2.0.21-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "39f41c64d6018859cee8ec8d3ef18220779106ae", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-2.0.21-alpha.tgz", "integrity": "sha512-uT9ahLBAGeOt0Z+VNAzFTzoltVc4GbxC+LF59yIKV0QKARvYH4h9ww0mT/fGz34snbU/gV8knu9V2JB2xfBACw==", "signatures": [{"sig": "MEUCIE7qm/jqdUo8WbE/mYgokYXq5smRiNHM4U5MqeS/PWuZAiEA7HtCelukKu3eRUzxL2cBpGjXXBl7a005QIyNGssouJI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\graceful-fs", "_shasum": "39f41c64d6018859cee8ec8d3ef18220779106ae", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\graceful-fs", "_npmVersion": "3.8.2", "description": "TypeScript definitions for graceful-fs 2.0.0", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/graceful-fs-2.0.21-alpha.tgz_1463773279929_0.14598631928674877", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.22-alpha": {"name": "@types/graceful-fs", "version": "2.0.22-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/Bartvds"}, "license": "MIT", "_id": "@types/graceful-fs@2.0.22-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "015e491de2f6469f9b1d5cea93f9b42afc3c91c7", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-2.0.22-alpha.tgz", "integrity": "sha512-ga8HRrm5IIIEc2pluAlJ6yx/vG76JbPPBldgoMdA4D6cdXwqpSaOHmU9+YVpoKe+wkXa1LCUIa1D3lgDg+kHQA==", "signatures": [{"sig": "MEUCIQDL1krjmoKYjvHL8UkVzMxhltKKvqRYx2UVqb/ndBDdjAIgKc0e+XGJyE5woQCEJB0Mm8/XRqC73ySI//EklK5hf5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\graceful-fs", "_shasum": "015e491de2f6469f9b1d5cea93f9b42afc3c91c7", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\graceful-fs", "_npmVersion": "3.8.2", "description": "TypeScript definitions for graceful-fs 2.0.0", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/graceful-fs-2.0.22-alpha.tgz_1464152365922_0.30040201521478593", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.23-alpha": {"name": "@types/graceful-fs", "version": "2.0.23-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/Bartvds"}, "license": "MIT", "_id": "@types/graceful-fs@2.0.23-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "ef6096772d557f6e793c32cd89d3b1fe35758af3", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-2.0.23-alpha.tgz", "integrity": "sha512-gr7kdgkhtdjSwSBFPZIRZlN3MuJ06yTnSljdiFnckYrr2733FFbXXqj1JTmvAkz85sKaC1igvx7adnDfB8Uhdg==", "signatures": [{"sig": "MEUCIEL0J1cyBC1p0rZUmmf0X9AXdOugfsB1CJfxWbpRK7gqAiEApQZuHEjPconL5l2bvE3tWa7NJSpVEG36zzQd3cVyTcc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\graceful-fs", "_shasum": "ef6096772d557f6e793c32cd89d3b1fe35758af3", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\graceful-fs", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.9.5", "description": "TypeScript definitions for graceful-fs 2.0.0", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {"@types/node": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/graceful-fs-2.0.23-alpha.tgz_1467401208282_0.5643368826713413", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.24-alpha": {"name": "@types/graceful-fs", "version": "2.0.24-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/Bartvds"}, "license": "MIT", "_id": "@types/graceful-fs@2.0.24-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "70aaa873c0eee2a5ea72d162b1f55168b7c7b3de", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-2.0.24-alpha.tgz", "integrity": "sha512-NwIFhPjfMqXZyKov1WJswzC+2DUP9KcIs5iLcmlpAOJ5cBI+0vuHPJRdfkXue6KS8JVDA1XGwgTcvinFOijX/g==", "signatures": [{"sig": "MEQCIDTisONbaKevLy5mHPGUjsKZATbKWO0JQ4E0NZWtR5caAiBhohHkqwRgpOx4BMwJIK+JjdLy8OQSAo8szH+mhw6FYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\graceful-fs", "_shasum": "70aaa873c0eee2a5ea72d162b1f55168b7c7b3de", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\graceful-fs", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for graceful-fs 2.0.0", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/node": "4.0.23-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/graceful-fs-2.0.24-alpha.tgz_1467413265880_0.07840733765624464", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.25-alpha": {"name": "@types/graceful-fs", "version": "2.0.25-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/Bartvds"}, "license": "MIT", "_id": "@types/graceful-fs@2.0.25-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "e09969067c16bd17c40a29f8a10d8f2f20d1aaee", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-2.0.25-alpha.tgz", "integrity": "sha512-Y/tN83dwZVOez/Dz6n4LEalhtknBjxJGDds8Il3jfWQ2iT0KRonNmOoKfxWTfDhRPYuL7eXiaArakD9PUZ/Vmw==", "signatures": [{"sig": "MEQCIGjjVWIaqsuc+YAmWfPxwWQ0uBt7xM2XsOaZTXxHVk/mAiBhG1/89VGA4CBjrsavy/Bz9xsxwthbBT5xoZ+T+J+L+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\graceful-fs", "_shasum": "e09969067c16bd17c40a29f8a10d8f2f20d1aaee", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\graceful-fs", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for graceful-fs 2.0.0", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/node": "4.0.24-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/graceful-fs-2.0.25-alpha.tgz_1467426413111_0.4270408316515386", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.26-alpha": {"name": "@types/graceful-fs", "version": "2.0.26-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/Bartvds"}, "license": "MIT", "_id": "@types/graceful-fs@2.0.26-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "5b96fcc4269ad580326bcd421fc9e31e0d76263d", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-2.0.26-alpha.tgz", "integrity": "sha512-qvDiGstdC291X07ZHtql6BziRHl9mr281u1UFpQmbla78PX00CwhQ+Neh0dHyoasdVzUxtJnHi0hiMVZpfovxA==", "signatures": [{"sig": "MEUCIHun+naaljhlPAJA19B/JVnojIvwAMtALMHtHTAD70/QAiEAsqYq9RC2d4E8oSJhb6koNzUtqjiBPo4OlCrXY0SuQs4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\graceful-fs", "_shasum": "5b96fcc4269ad580326bcd421fc9e31e0d76263d", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\graceful-fs", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for graceful-fs 2.0.0", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/node": "4.0.26-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/graceful-fs-2.0.26-alpha.tgz_1467591414130_0.38295444077812135", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.27-alpha": {"name": "@types/graceful-fs", "version": "2.0.27-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/Bartvds"}, "license": "MIT", "_id": "@types/graceful-fs@2.0.27-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "2abc7fd1c14b537d75036f5c1c0c4de51b343ca6", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-2.0.27-alpha.tgz", "integrity": "sha512-bzg0vDW+4zojDhdqrLKAyQegnzvqudUKtsx8H9ToBjIi0y0Fb6Tok2xT+xmBWE9onJz7uki+WdJZ/5w37X+E7w==", "signatures": [{"sig": "MEUCIEYvyI8+N1jajkf6PowE758zIW8bUtshgVrynNBxSR9+AiEAuyuxEQ4nUHX/zFddrsmZIywyB+x+kJ9h0OGdZUSNXuk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\graceful-fs", "_shasum": "2abc7fd1c14b537d75036f5c1c0c4de51b343ca6", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\graceful-fs", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for graceful-fs 2.0.0", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"@types/node": "4.0.27-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/graceful-fs-2.0.27-alpha.tgz_1468008934460_0.1257450000848621", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.28": {"name": "@types/graceful-fs", "version": "2.0.28", "author": {"name": "<PERSON>", "email": "https://github.com/Bartvds"}, "license": "MIT", "_id": "@types/graceful-fs@2.0.28", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "84e53e2711c638e9a789521a111801a0647475c0", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-2.0.28.tgz", "integrity": "sha512-SRtG1y+cqYgw+3oxdM4hYrLRf2SMa2Oc+sOmT5Ujyjln7vqROn7RZBcuyVyUWN7FlVvkacZ0m0fcmSBvvMOq1A==", "signatures": [{"sig": "MEUCIGOxtQVvKg0IRPhBvO1Urt7fRIMpvYW6zrxnPYL9nOOOAiEA6u9bqFN/SVjGIivRqFp0vHRwXgj7Z23x0xA9ApLUCIM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\graceful-fs", "_shasum": "84e53e2711c638e9a789521a111801a0647475c0", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\graceful-fs", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for graceful-fs 2.0.0", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"@types/node": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/graceful-fs-2.0.28.tgz_1468507438527_0.26661557611078024", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.29": {"name": "@types/graceful-fs", "version": "2.0.29", "author": "<PERSON> <https://github.com/Bartvds>", "license": "MIT", "_id": "@types/graceful-fs@2.0.29", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "f9832236cef906b501b184dc3d58bdf454d37b01", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-2.0.29.tgz", "integrity": "sha512-wROowKUPwqpPn8ksDxnEYMoDXF+YKqOSHS/Iz799jC29XiLHz7vUNof/k/dJB0VfpKI+JFX3TMnjTTrBIoG0FQ==", "signatures": [{"sig": "MEUCID7TzwHv5/VrC4tlBYw1PCN96W1O3+eIhVPc0rBgq/16AiEA2UkPrMMuj1WORQixesVx8YO67oaveTWG/f8RsIDAXNM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for graceful-fs 2.0.0", "directories": {}, "dependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/graceful-fs-2.0.29.tgz_1474306669814_0.6425043647177517", "host": "packages-16-east.internal.npmjs.com"}, "typesPublisherContentHash": "17692aae0305497bee4e6d9ed179bf3e11e68d8dbeb8d6b26546669ad7e598fb"}, "2.0.30": {"name": "@types/graceful-fs", "version": "2.0.30", "license": "MIT", "_id": "@types/graceful-fs@2.0.30", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Bartvds", "name": "<PERSON>"}], "dist": {"shasum": "631f423874075ecf33ca824fc85294a0ea486592", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-2.0.30.tgz", "integrity": "sha512-VKSIyjNcY4vxMZmHwN7YoYmwAeHONUursU41jlzP89S9raHse7v+AQE9bTvvNx3ZtbOj6/zpcGiMTfgkbawP9w==", "signatures": [{"sig": "MEUCIQCsmv3JNbSDrfWIUKBc1DZGtM81jry26OlCKppbaLLmeQIgX2ei+krFijFgW0mehJdKZLcn98LUG+nbbqqZTncs69g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for graceful-fs", "directories": {}, "dependencies": {"@types/node": "*"}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/graceful-fs-2.0.30.tgz_1503594954985_0.7835798517335206", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "dee0b8df4beff3e6d36b05971030dfed91bfcce5c99b156d1f636aba6254a6f3"}, "4.1.0": {"name": "@types/graceful-fs", "version": "4.1.0", "license": "MIT", "_id": "@types/graceful-fs@4.1.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Bartvds", "name": "<PERSON>"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "aa94f6fb3e9fef07aed3923f78decb9a4ddb74dd", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.0.tgz", "integrity": "sha512-G2z8ToQ1Z6WBljMO/FMQ+/bZad/VjUEQmj3dMgoLr+HEIFHJyS6Jo1OxklFCjepLJmDraoJybKmhrkXpI6RgrA==", "signatures": [{"sig": "MEYCIQCW9TlEDc9pA/6Df7tiWAkNumB7e7t47lAi3498I9XPZAIhALO8V1ewQENbkkqzYgSgfs4PAgBXZFygHWM/FdPfKTID", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for graceful-fs", "directories": {}, "dependencies": {"@types/node": "*"}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/graceful-fs-4.1.0.tgz_1503594989268_0.801017748657614", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "07143150a1d41510c84d5534603c9508a9fa0484bccfbf4fbf3485203f98cb3f"}, "4.1.1": {"name": "@types/graceful-fs", "version": "4.1.1", "license": "MIT", "_id": "@types/graceful-fs@4.1.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Bartvds", "name": "<PERSON>", "githubUsername": "Bartvds"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "841a2c426b3ad82c976e8077745cc218101068e2", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.1.tgz", "integrity": "sha512-KhKd0AAzPV4uQHHMKlpji/6zxWQSpWhjyHgLHqyoTSrklT7OnrwWOqo8c6ZNQPpBN9hU+UsxapzPzd6XraddkQ==", "signatures": [{"sig": "MEYCIQDZIdkaXdJjHK/jMQ/7ftQA0zVrhU+8IFLLcNlF+/WjTwIhAOlyr5KUpLF4kE93J4ive5ojs/9QvZEgbn/RRP11ap49", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for graceful-fs", "directories": {}, "dependencies": {"@types/node": "*"}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/graceful-fs-4.1.1.tgz_1506450972726_0.4548055974300951", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "952797bbc14ecbc666a55dfbc1789a2d871b590b6c9f22ac8e19e1eefea0a49d"}, "4.1.2": {"name": "@types/graceful-fs", "version": "4.1.2", "license": "MIT", "_id": "@types/graceful-fs@4.1.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Bartvds", "name": "<PERSON>", "githubUsername": "Bartvds"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "fbc9575dbcc6d1d91dd768d30c5fc0c19f6c50bd", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.2.tgz", "integrity": "sha512-epDhsJAVxJsWfeqpzEDFhLnhHMbHie/VMFY+2Hvt5p7FemeW5ELM+6gcVYL/ZsUwdu3zrWpDE3VUTddXW+EMYg==", "signatures": [{"sig": "MEUCIQDT+X7VQISVo5BEa1OIFffu9e/9Y7a5MQmFD7FMIFxgzgIgWvWKrq6WVseozikAvJLD0LNbl8FNtbg1o9n5tKGMjaU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for graceful-fs", "directories": {}, "dependencies": {"@types/node": "*"}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/graceful-fs-4.1.2.tgz_1510181026529_0.4345751858782023", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "499b610f1d378788392d28a26c4a4d8bba6e2cc79a2f837bba1a865927d5877e"}, "4.1.3": {"name": "@types/graceful-fs", "version": "4.1.3", "license": "MIT", "_id": "@types/graceful-fs@4.1.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Bartvds", "name": "<PERSON>", "githubUsername": "Bartvds"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "039af35fe26bec35003e8d86d2ee9c586354348f", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.3.tgz", "fileCount": 4, "integrity": "sha512-AiHRaEB50LQg0pZmm659vNBb9f4SJ0qrAnteuzhSeAUcJKxoYgEnprg/83kppCnc2zvtCKbdZry1a5pVY3lOTQ==", "signatures": [{"sig": "MEUCIF6y4xJSBX6ywcb+tTEuXGkqNqrWsoOfLT8Zeg62h1HFAiEA5ww/dkXrMctChEPGEGiqB85oQiEg9seHkPfPBU84csc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6938, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZGklCRA9TVsSAnZWagAA25QP/jax2FUvVLYEDm57g22g\nwqjY/9yA+Pc5/k1c/Ik7VBLO2ai7+TgqYwyTrCZ7TWrSteW4KHSMTvpdFqZO\nHHQV9TykjHPkiY0aeBSwdehO6iOILM7bGLK962knVhGyVO/7936XUft1lbfX\nfIw/RDmkCXRdaTXpSm6JEb0GVkKSy5X4/Y4Enwu9aCneeMUTXE3Jqz9Z/93z\nFVxe8dE+PM2Ipe1ivwUBVX8/HeSy2yHoceOKZovxaQSYBqmkfOazuq9n+p+Q\nbWl57Jt7cj9zUk9hmZhD4bl8yuRMFKs4Vlk/ruvLfcTemA8ow6Z9YoS95R4i\ni2Dbyo8VmLGtPMIAM8QUZvn3qPvCbeDhRfgCceYHGA802/SYF+baZXMCaS6p\nzzQvb/awKZAU0GXKh8xE8QkguEUVarAxk05fs3HZXTfOppUQl9GJ0l0l6E9z\nuAVm+6AmtOBjCX0zGKl/thFNVX62CB1fyKn8AUtzhCQzo+Iclwha+ORjZJWZ\naojNGt9X6Hr6TeoKdtvAFGqd9ZRehsDAr6735xQZbhagXldWYWNwrpMhfkAC\nFnsNRwQbeSfsP4Nvp8cs6A9ld4JYSdXaLzaRPUz4BrhgLJBnYUw1xRtDn2Tq\nzpCP8/6Hs+M4ad4XSQ2V59pEXkMKy5Nc4KCy4Mykbpe4X5n5D6SW2TmGUcmX\nqQyH\r\n=+IAK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for graceful-fs", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/graceful-fs_4.1.3_1550084389328_0.9919620059146721", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7e459d70acc49c2972a15118724b3a8c901016e65a92fdb945c73b657dfcaf2d"}, "2.0.31": {"name": "@types/graceful-fs", "version": "2.0.31", "license": "MIT", "_id": "@types/graceful-fs@2.0.31", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Bartvds", "name": "<PERSON>", "githubUsername": "Bartvds"}], "dist": {"shasum": "df7d5add3aa3dd1549caf44e041dfb2338b197f2", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-2.0.31.tgz", "fileCount": 4, "integrity": "sha512-0ybNbCd0JqU7QirWreOUn06ai1SgMOmEQNmSTR3kGe9rZBl4dlqQ2D+ifzBhnw23yXBq10G2UfkoSY1U7EuTqg==", "signatures": [{"sig": "MEYCIQDPZdpQIXHJZD8axmEjX6neuK2uxbraqwic9G4IhVvr5AIhAIGj0bMxYOq4NyPmzSJ9YZ3FejCPdSzup9oHVTyPycCK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2763, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZUXGCRA9TVsSAnZWagAAG/0P/0tzcJsi1rKEIpF7H9XQ\nZV9W2yq3d2DUdT/fraKiJvQnlVWKaSc7bhk/Z2YXu1+AwgKXbni7nSM0O1Ab\nybxK8fn6rAdBhaqeMAYwgdyp7LP/oncqSygMQTQwcn6KZa5B642QOl6I1NlS\n5OcskQjSScLGRzxxsQdYDubhQnVxXhUk4Sd5GITOZl/yFk8BZWmd2lpujh1d\nh+pCZjQCogiYHwV4B+D9sBwt/EU8NsAz4+3UuS/+dXJEy7Nx1soCRQuXVPQA\nBgAVNe/JhuFYNmGhjROaD72GrXP+E4ZVFt60dz7xmJlcq7yCwY66vBOj+bab\nM2rrj7xWB8W1UBN38IIZXiq6UlvDYq7JltO0loPmO2te2AkyLKFEmuIvdrkn\n3kKZSatsqk3AvgEYOyEnfxIE99i98mHa5WmuKWnZMIgH9STl0005wBKU4ric\nOnwTBFrohh0Z+Vxqr4udKekcDQW5r9jRBOCyfiewqK66Ww350fl4E4/jy7SV\n7A8Hhz+nJ5IHAZMte+uV9uMLESoIs2sD1ouUQ9WgoH8r5yd2uY+aP85NOJ0Z\nfNwA3+msqzICMf0I3fcmTDH2YylimtSTK1nGMbKWK+AbA/NtL+BLvg0GgPOu\nsO9oOV0eNh+RoHKiYc6VUEPwc3WDCnwonjX/kwwMIkM8US8v9+//+c84jA7l\nBvgZ\r\n=T/Wv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/graceful-fs"}, "description": "TypeScript definitions for graceful-fs", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/graceful-fs_2.0.31_1600472517784_0.648023229092979", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d49de5636ea55e972471a1da936f79d9eb966cbb1060b740b98a067db17d4658"}, "4.1.4": {"name": "@types/graceful-fs", "version": "4.1.4", "license": "MIT", "_id": "@types/graceful-fs@4.1.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Bartvds", "name": "<PERSON>", "githubUsername": "Bartvds"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "4ff9f641a7c6d1a3508ff88bc3141b152772e753", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.4.tgz", "fileCount": 4, "integrity": "sha512-mWA/4zFQhfvOA8zWkXobwJvBD7vzcxgrOQ0J5CH1votGqdq9m7+FwtGaqyCZqC3NyyBkc9z4m+iry4LlqcMWJg==", "signatures": [{"sig": "MEUCIQD3+BYHhthNEjUo4wANtezF05u7HBs7lME8ctVIzlSTMgIgaFucx7vpPyJkrgMVZGQ9SZBrB9ZbDUcVQe0DOIP2xE8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3503, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkKH/CRA9TVsSAnZWagAAitUQAJtvA6TbWhDGJyeryU9o\nyP0SzBlUBeYncfYxjxa72ReSIvfxEnlv0u9qToxAXX5t5MxFiryu1D+3mwfz\nri12dOedODGQ+LVrbA5/YyVPXwJjwlUU7y7E/KwcG1QSQy25VI8fNsI9Lmsu\nnM1TUMTCvflQw9vi31ic7u4zJZIipruiSAYiZ7LqniT7Z34Mx76ksV0NYsy8\n7kS4+R3OX6rH3GATD4LWy6DROAQ9NuI5Q6trdI6OKctmva7TeahFLr8abNWZ\nIAD6GJ9b1ltk7I4pFfwzRw8sYhxfEQN8BZyjALI5fD5J2M4zF38Ws4cY+zCk\nb0ITUFOOWAuYArpy6r5X0l2s0OQSThk0jSmoeDRHvasTEmMV8aCY5WYkw7ut\noE1tnAUHBuBTjdYcP44zFfrQ3TkBS1Bb9SiqgKoqxxvjE8IctTUT37r/Yw9O\nas95WxeZCcV3Z8V6SUuROstOqsAlIKS2pcYhinDrE6OJn7VkP/QwIKTxJzPT\n2t8qehGM9bcK1lBi9cxxAn1fg7VkWbCs+Ay0xokt+yw1udmSNF93a6CT5pen\n2Pu1ymeJjdo8WK3OImgPlCS1csthiF9Yjky3n7PPfo8S/M9pD8plJGkfYBcw\nTWgdqyfC8wrBIUjsGExkd8pQrmGUaa0tqOFqaVfz9Rk23Fek4rH8oz1XZdgJ\njdkk\r\n=v8yg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/graceful-fs"}, "description": "TypeScript definitions for graceful-fs", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/graceful-fs_4.1.4_1603314174654_0.4313866330750724", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "056a1225be9e1e75f3159d214868c57eeb9f4c81385dcac88a3c56971b088b9d"}, "4.1.5": {"name": "@types/graceful-fs", "version": "4.1.5", "license": "MIT", "_id": "@types/graceful-fs@4.1.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Bartvds", "name": "<PERSON>", "githubUsername": "Bartvds"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "21ffba0d98da4350db64891f92a9e5db3cdb4e15", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.5.tgz", "fileCount": 4, "integrity": "sha512-anKkLmZZ+xm4p8JWBf4hElkM4XR+EZeA2M9BAkkTldmcyDY4mbdIJnRghDJH3Ov5ooY7/UAoENtmdMSkaAd7Cw==", "signatures": [{"sig": "MEUCIE2jA+pCdOaCifA1I4RxCas/KVv6JQvdgcZR4JqI4vSyAiEA4t2Nl/lgclpsdK2Nea/VOWk++tRa+CG9KPCkKo+5H7c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJaZOCRA9TVsSAnZWagAAQhUP/2oY1tycjp63F6G+/H+/\nN2VviXiDwKBd7QL6iftJoQHo9vWbLjW3yJIVdTpkVPQpGN2E4e+npCK6P1w6\n5Nm+6VY23RaGDm3THEgNU4zI/trxfqWbqMMqVtoAy+mBFU7cJhK24h6A+4D3\nXQQOfJOsYPNgOTZthCKDgum8g81iYYVpgUTsypy9ihdOSlVfSw3QtWROfqIN\nnB6Iq0XCOp1FsHMXXSZG0FvXZAKnc8jmNtEo9tSmogkwoSgsAHQpQeXJTgL9\nLn7swvBGy0j9i1X2rwjdsSyYpQGZtZAqrNcONaz207bkshVUaMJYhW43VlIg\n/iAdhB0rvipqJfoFyRbfTIOETjDjxsyJLNyzOwq7ZNFWPn7xIHgGN5wGncxX\n1X1Eoe6Qza0yIAvNKDLVUyyawUEXmwpciab86bqrB5nUx05n3Yp3wJimcIaY\nVZvGgemJcbRrIZp7dQ0bYZwWMCJTyORinjpabrfre33O+BQDCL22iXmjUERP\nAqvB8kZ3fMZtTU1+RhG1ad8SuWXxbtgqiOtAwN6Nnx1ldA1cRikqoU4oOxRB\nRRL8GbUmn2W2wHJjvEtIHIq2nrTdh6iZNBiKjFwyzBjpGEf3H1K2GAl77Arw\nDhZN2zLWLXVoBJRCuvvbjtR+0YGGAS6yUazVFajTo9M7lke/blKRZfYDNATI\nNoRh\r\n=wpQp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/graceful-fs"}, "description": "TypeScript definitions for graceful-fs", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.9", "_npmOperationalInternal": {"tmp": "tmp/graceful-fs_4.1.5_1613080142016_0.9349025729306102", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3e053765471f17a5b5cdd161ea0773a8d7dc77e032922e2c4cae1393e2c66ac4"}, "4.1.6": {"name": "@types/graceful-fs", "version": "4.1.6", "license": "MIT", "_id": "@types/graceful-fs@4.1.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Bartvds", "name": "<PERSON>", "githubUsername": "Bartvds"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/graceful-fs", "dist": {"shasum": "e14b2576a1c25026b7f02ede1de3b84c3a1efeae", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.6.tgz", "fileCount": 5, "integrity": "sha512-Sig0SNORX9fdW+bQuTEovKj3uHcUL6LQKbCrrqb1X7J6/ReAbhCXRAhc+SMejhLELFj2QcyuxmUooZ4bt5ReSw==", "signatures": [{"sig": "MEUCIQCPhM8bYDBwslEKRwZy+WggQKbBsiWqGkWY0IHUpesZvAIga+zT2dRoRZx5vrhgFR94R/9o2NFrE6VVHgCFojJEMpY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4521, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJju1mrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoKKg//Sf6eBZKA7JGwUGLLS0zylw8IY6qWKXgQRiPfAEKBzobPiCha\r\nFGTW9rb37MeG2lxpcJ/FOonxF+Sq6ITzvnujfVAzYOuLq9hiEtMbji0kxVXH\r\nNpv5TsO98SFI0e+Wbz3coZ9uGBil06JViiyhXOT1WdNjh1VXCWx1AUODjyIp\r\noZhFurmNrwUS819IVsJ+SAktgs9VALx3fuZDW2JD4sPXc5h3j1JbzbqLERyX\r\nSXQh3MiPw4bxOo0DL6yFIXKWKkr2+Iro2BwJso7SkHR344LZH9Ug9Tzazb2g\r\nHG+ceKxXg+yBjlTc5/6ZOl228d+ZesAsliXgCSXBfYWL51MlT9F143ltwq7i\r\nyvvUVuoxd+F3I0IxxKHnzNivVyTpcNczUzO8qeW/RuAzy+/nOHCXG1QKkOV4\r\n0LUSFsnhKy64KXlrWY+T2GKB0vGlKUi5AFmFrFar8GNqGuS8WV7xe3eDiUjs\r\nwpYnkVmgwrEXUZ+pFJYtHYEM+tjmav8Fw08W9YOOkYpUfRtZs6dFvfDhk7nu\r\n/c8x42AZmBVKwGEXomZD3UtLABCRn3jCp4lzoIq0+PUgEL+WK/IsYFbCFlMP\r\nBZOfmBTQixRozxBgyFo7drH7n6QVb9qxdDCd4WTsl5VHuTfFOCwfajtYUA+5\r\nf15sBxAPat6ejItZmKDdoTjho7hGNHMWTAg=\r\n=hgXQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/graceful-fs"}, "description": "TypeScript definitions for graceful-fs", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/graceful-fs_4.1.6_1673222570882_0.009132819745423237", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "4255ab80de2aeb0256f2dab80bad292bcac1ab7cfeb4ed6ef220583929363140"}, "4.1.7": {"name": "@types/graceful-fs", "version": "4.1.7", "license": "MIT", "_id": "@types/graceful-fs@4.1.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Bartvds", "name": "<PERSON>", "githubUsername": "Bartvds"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/graceful-fs", "dist": {"shasum": "30443a2e64fd51113bc3e2ba0914d47109695e2a", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.7.tgz", "fileCount": 5, "integrity": "sha512-MhzcwU8aUygZroVwL2jeYk6JisJrPl/oov/gsgGCue9mkgl9wjGbzReYQClxiUgFDnib9FuHqTndccKeZKxTRw==", "signatures": [{"sig": "MEYCIQD+0++9KcOqpI9AtBuMhBO8l7qXe5ZuYgJywrQwzWqIAgIhAKH5ba2a4eSHdViUQ0HTa6L8qformNaZ1n8loT3camm2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4521}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/graceful-fs"}, "description": "TypeScript definitions for graceful-fs", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/graceful-fs_4.1.7_1695232378751_0.7357239898175714", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3bed272905d6e257d44cd4146007159bed88eb167f7d433fc1359395d9b04a86"}, "4.1.8": {"name": "@types/graceful-fs", "version": "4.1.8", "license": "MIT", "_id": "@types/graceful-fs@4.1.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Bartvds", "name": "<PERSON>", "githubUsername": "Bartvds"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/graceful-fs", "dist": {"shasum": "417e461e4dc79d957dc3107f45fe4973b09c2915", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.8.tgz", "fileCount": 5, "integrity": "sha512-NhRH7YzWq8WiNKVavKPBmtLYZHxNY19Hh+az28O/phfp68CF45pMFud+ZzJ8ewnxnC5smIdF3dqFeiSUQ5I+pw==", "signatures": [{"sig": "MEUCIGN1gXPX/FbRqXzw4ecArZ5JY8hYNH40hvg7xHLjzL79AiEAxCYpOlR6ETLjZkZOnwek2HYL3qgahNVNrjeg3Zdbr6U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3899}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/graceful-fs"}, "description": "TypeScript definitions for graceful-fs", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/graceful-fs_4.1.8_1697602175258_0.34675519262965016", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "44fb8bb11ed0743fa77d3970e8ddbd1bca237a456e51818c2aad07e402710547"}, "4.1.9": {"name": "@types/graceful-fs", "version": "4.1.9", "license": "MIT", "_id": "@types/graceful-fs@4.1.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Bartvds", "name": "<PERSON>", "githubUsername": "Bartvds"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/graceful-fs", "dist": {"shasum": "2a06bc0f68a20ab37b3e36aa238be6abdf49e8b4", "tarball": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.9.tgz", "fileCount": 5, "integrity": "sha512-olP3sd1qOEe5dXTSaFvQG+02VdRXcdytWLAZsAq1PecU8uqQAhkrnbli7DagjtXKW/Bl7YJbUsa8MPcuc8LHEQ==", "signatures": [{"sig": "MEQCIHn28BzvuHNzXIC0Gwf6m4MAj1rQsD+7orDbKBNMxpwNAiA7vG9rn/8/AqWOu007cqSN12vVO66tdXrix473Xw1mZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3899}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/graceful-fs"}, "description": "TypeScript definitions for graceful-fs", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/graceful-fs_4.1.9_1699339058330_0.5330916017262843", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "4e85fab24364f5c04bc484efb612d1c679702932e21e6f4f30c297aa14e21b36"}}, "time": {"created": "2016-05-17T05:02:54.219Z", "modified": "2025-02-23T06:55:53.581Z", "2.0.15-alpha": "2016-05-17T05:02:54.219Z", "2.0.16-alpha": "2016-05-19T21:00:49.550Z", "2.0.21-alpha": "2016-05-20T19:41:22.162Z", "2.0.22-alpha": "2016-05-25T04:59:28.423Z", "2.0.23-alpha": "2016-07-01T19:26:52.023Z", "2.0.24-alpha": "2016-07-01T22:47:46.498Z", "2.0.25-alpha": "2016-07-02T02:26:53.647Z", "2.0.26-alpha": "2016-07-04T00:16:54.711Z", "2.0.27-alpha": "2016-07-08T20:15:36.008Z", "2.0.28": "2016-07-14T14:44:01.185Z", "2.0.29": "2016-09-19T17:37:52.731Z", "2.0.30": "2017-08-24T17:15:55.072Z", "4.1.0": "2017-08-24T17:16:29.354Z", "4.1.1": "2017-09-26T18:36:12.816Z", "4.1.2": "2017-11-08T22:43:46.603Z", "4.1.3": "2019-02-13T18:59:49.470Z", "2.0.31": "2020-09-18T23:41:57.885Z", "4.1.4": "2020-10-21T21:02:54.779Z", "4.1.5": "2021-02-11T21:49:02.172Z", "4.1.6": "2023-01-09T00:02:51.051Z", "4.1.7": "2023-09-20T17:52:58.951Z", "4.1.8": "2023-10-18T04:09:35.500Z", "4.1.9": "2023-11-07T06:37:38.558Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/graceful-fs", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/graceful-fs"}, "description": "TypeScript definitions for graceful-fs", "contributors": [{"url": "https://github.com/Bartvds", "name": "<PERSON>", "githubUsername": "Bartvds"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}