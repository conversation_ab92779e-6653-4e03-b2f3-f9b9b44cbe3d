{"_id": "kleur", "_rev": "25-84c8fe50f07789b15b75be3923b3fcdb", "name": "kleur", "dist-tags": {"latest": "4.1.5"}, "versions": {"0.0.0": {"name": "kleur", "version": "0.0.0", "repository": {"type": "git", "url": "git+https://github.com/lukeed/kleur.git"}, "description": "WIP", "license": "MIT", "files": ["*.js"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "lukeed.com"}, "engines": {"node": ">=4"}, "gitHead": "a9041634f44ef35cecbad8aaa2af28abf908ae54", "bugs": {"url": "https://github.com/lukeed/kleur/issues"}, "homepage": "https://github.com/lukeed/kleur#readme", "_id": "kleur@0.0.0", "scripts": {}, "_shasum": "c005b67b896a4fbd30803b152f0444d521a54a0e", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.1", "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "dist": {"shasum": "c005b67b896a4fbd30803b152f0444d521a54a0e", "tarball": "https://registry.npmjs.org/kleur/-/kleur-0.0.0.tgz", "fileCount": 4, "unpackedSize": 1697, "integrity": "sha512-5aoWBLiZSvJ0DBpbIAH31fCIghURL6qlWy1qU8ldlCeqg1LLLQDzwA9DUQ7sA1vEvObZtcMozh3uO4wRKnlHsg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD6wHlri///1NHNHEu3u7DzJdnCRlyx4cVtmU36ksLIVgIgdui36UbDOhGX62RVhwx6tA4EiBBkue1UUY44q5q2yBo="}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kleur_0.0.0_1531078937649_0.9124052414314574"}, "_hasShrinkwrap": false, "deprecated": "Please upgrade to kleur@3 or migrate to 'ansi-colors' if you prefer the old syntax. Visit <https://github.com/lukeed/kleur/releases/tag/v3.0.0\\> for migration path(s)."}, "1.0.0": {"name": "kleur", "version": "1.0.0", "repository": {"type": "git", "url": "git+https://github.com/lukeed/kleur.git"}, "description": "The fastest Node.js library for formatting terminal text with ANSI colors~!", "license": "MIT", "files": ["*.js"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "lukeed.com"}, "scripts": {"test": "tape test/*.js | tap-spec"}, "engines": {"node": ">=6"}, "keywords": ["ansi", "cli", "color", "colors", "console", "terminal"], "devDependencies": {"tap-spec": "^5.0.0", "tape": "^4.9.1"}, "gitHead": "8a7f9809a5b3cd9bda382ef0c5aa1f8319e884b3", "bugs": {"url": "https://github.com/lukeed/kleur/issues"}, "homepage": "https://github.com/lukeed/kleur#readme", "_id": "kleur@1.0.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.0", "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-jPbbXUdOoA78rPYOiMFAfawag2gJiRYsmvMPLAAe9R9KtoHqGYuZDEeMW/ggezDLf8DNM3ulUDitxExYyAhYpQ==", "shasum": "3c4f3f60172a2b20b783fa7d0294fa5724a7d7b7", "tarball": "https://registry.npmjs.org/kleur/-/kleur-1.0.0.tgz", "fileCount": 5, "unpackedSize": 9070, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRN+tCRA9TVsSAnZWagAAypIP/3egEnFDNs5cJResCq4m\nHY1Lckd0/2m3dL2jgAHpRN3MwfOpNFiKbXVMWa2djRuKOPXPoZve42iqrARZ\nYawU5wjuz0cEhYOuQ5SbzPgLl3yjRkN5rcfkkLxh11luKedDzhTL9+CHDKe9\nGuZI6heU+PcA8fBqL/T8MwlKJbB11MsFL/EUR43nVQTPUpTzVr2NHAH/Vkcy\nDGDTImRCKV3D00dzeRuufcfFLP8plIaj4QvWGBQ0Q4e6zKlzmo0Ess458sxl\nK0iK2TeJpeLsj628JZPg0SlEHVmXj5kb1WYDDUeXXPiIVv02pSluMC0YuF8n\nu0ql+C2Y1QPRK04z/56Lm7qEQ63o/0zF7HaLkpDvsm2hRh3kjkhofPdhK9Hm\nSnLzUyEfStgNli4Tuqri951QpWA8uR5EnjGfkaSvJxT5g5HYnG5+sxjsb7tJ\nhzjdQ/r7IeSWURVzYU5GMgrRD1Y7XxQx8VvepU3mzxJgg3dXERHy9ecrlxxl\nRKgmao2vak1a9rx1WWvHyz1aLftTvMEpBnU6MK5UtHR/A0rLl2bfnka5b4Rc\nhNJ/3TlAlO6DqCRzO5v08kphx+IJpyzHWXy207rJRi8glpeZ7+kdK748GjKO\n8VZwvH4e9+Qc7EvenVEuWkxA+EhNVlaK1oWW/hR+vj3xJ5LyjHXRWdz51oMQ\n4oN5\r\n=azlV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC9mWJsB5ui3OkkupeAgbGZw+HsZjga4T9QT7wYb7pKsgIgXhcjloWQZ/bMX98bRxkpYaUfDNdbim9ozBk1cQW5A8A="}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kleur_1.0.0_1531240365207_0.4497079869938323"}, "_hasShrinkwrap": false, "deprecated": "Please upgrade to kleur@3 or migrate to 'ansi-colors' if you prefer the old syntax. Visit <https://github.com/lukeed/kleur/releases/tag/v3.0.0\\> for migration path(s)."}, "1.0.1": {"name": "kleur", "version": "1.0.1", "repository": {"type": "git", "url": "git+https://github.com/lukeed/kleur.git"}, "description": "The fastest Node.js library for formatting terminal text with ANSI colors~!", "license": "MIT", "files": ["*.js"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "lukeed.com"}, "scripts": {"test": "tape test/*.js | tap-spec"}, "engines": {"node": ">=6"}, "keywords": ["ansi", "cli", "color", "colors", "console", "terminal"], "devDependencies": {"tap-spec": "^5.0.0", "tape": "^4.9.1"}, "gitHead": "ded788eec79017c3d7ba57ae6ee19b39c368bc8b", "bugs": {"url": "https://github.com/lukeed/kleur/issues"}, "homepage": "https://github.com/lukeed/kleur#readme", "_id": "kleur@1.0.1", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.1", "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-8srIZ5BK5PCJw1L/JN741xgNfSjuQNK9ImYbYzv7ZUD3WPfuywaY+yd7lQOphJ+2vwXnMLnRZoAh5X+orRt4LQ==", "shasum": "6b64a4a42c7226fc0319fec35904f824ad945f7e", "tarball": "https://registry.npmjs.org/kleur/-/kleur-1.0.1.tgz", "fileCount": 5, "unpackedSize": 9017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRnxVCRA9TVsSAnZWagAAUtAP/1CGoL2sKG+ShTCa8606\nO2MjG1OhY33PebhJpPUGbLTX1XcN8ymAdd1xSTiW6EBTM8IAYGUv7PnOhgY0\nnFUvp1WWw7C3hp7E2E4ZGnHlX3EK1zxMbAIYu3ke6O28aSlWI8w5PY/4tH08\nWgbdc3Qu4RklvopZpRo9UXBQMFMO5DDTaWopSIRA6S9TFOL9T/Q8RTkY5jcX\noR5Dn83y5gkUIgexAQwNOYFAnSf7rtZpRTS4CWG/ODqBP5FmlZL2VyRS7zCi\n9GPkvlpQBnhVNNaGiPDWQF669iAMACdnOEmWPEclhkE/vL8wiVLVGhhrZvlD\ny1UiLdVgzDBwhF3XHnRrFrzsA4hYGTbzFHOzshFJaI1MKbFPVgSGEYdMCT09\nkFs/NtI2L/IL87PB8t0zAJkXR7fq6xrRSfzOc7aS2SUJNc0FEyPaql/W2q7Z\n6Q0VZdpzpvx0nWEYZcEv5XMCS3WYsHHpJ2W98SwwTKUz3efZ6WrtiFIn64hV\nnj3nUTdZm+/7npsUU2XEXojHjHgCCEEg5Dl8HW09Ua3LzJVUfhKO+GzoQA/E\n+0Crgj/Wu7P5ToQrtZtyzkFxe7zKieTf2Y5goitVEW73NAqBeuIx52MpL1+E\nvkiQvr/kiYNASHAu0QTfSsw9aSP7QJFjVZ7aGEPke6v0JIuXlmwGpVA44Fk5\nlrYj\r\n=VO96\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDDnhA99AyBdCRqOel/KKSqV7yJ4V7IWpO4TffF52WNRQIgXMc+y7vFS1Es9Z263p/NArI0kHaMrberEOnc3tsIDxQ="}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kleur_1.0.1_1531346005728_0.8264265949882945"}, "_hasShrinkwrap": false, "deprecated": "Please upgrade to kleur@3 or migrate to 'ansi-colors' if you prefer the old syntax. Visit <https://github.com/lukeed/kleur/releases/tag/v3.0.0\\> for migration path(s)."}, "1.0.2": {"name": "kleur", "version": "1.0.2", "repository": {"type": "git", "url": "git+https://github.com/lukeed/kleur.git"}, "description": "The fastest Node.js library for formatting terminal text with ANSI colors~!", "license": "MIT", "files": ["*.js", "*.d.ts"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "lukeed.com"}, "scripts": {"test": "tape test/*.js | tap-spec"}, "engines": {"node": ">=6"}, "keywords": ["ansi", "cli", "color", "colors", "console", "terminal"], "types": "kleur.d.ts", "devDependencies": {"tap-spec": "^5.0.0", "tape": "^4.9.1"}, "gitHead": "6a3633d9647d8878c2bf682da87b4070a2c201b3", "bugs": {"url": "https://github.com/lukeed/kleur/issues"}, "homepage": "https://github.com/lukeed/kleur#readme", "_id": "kleur@1.0.2", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.0", "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-4u2TF1/mKmiawrkjzCxRKszdCvqRsPgTJwjmZZt0RE4OiZMzvFfb4kwqfFP/p0gvakH1lhQOfCMYXUOYI9dTgA==", "shasum": "637f126d3cda40a423b1297da88cf753bd04ebdd", "tarball": "https://registry.npmjs.org/kleur/-/kleur-1.0.2.tgz", "fileCount": 7, "unpackedSize": 14237, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTlDmCRA9TVsSAnZWagAAWV0P/2ZdpdQZo/KJC8g55VD4\nlC/YgzvapAc8Ar7/2YPlirHpEmGonhCNpNrYRDclDlC1a+pb7tYevQLeFP3o\nsZ2kxMq2fkJCQ1eDUE3+tcCJxzs0c+fd4NKUHyX6lXt9KYRfeAxN51MBrTTV\neWL8fjM5oY4HRnT2g6WkN1F0knhTpfKyXK8d7DWjFpp1WCdZCueH8AjEFggR\nZ2i6EncvfxdM1nvqPJEx52jWyNpFjJrRBSRXj672czdQIW8u4oBjfpTexmEr\nIVjEqkPzdgf/eS++I7N5KGBAnfGNQX6yZ5BBM7E7eFnd/XmkZVQBMian27p4\n8l25eVNNSu5HtBNykGLx3ebuONxgmxs/NNLKiamXc07ToEuNoByigW1QbTMx\nob5dwY6gfK2o3CByqSYRlniEbw4mRIxVcRpheFEWom83CkCUA97zNet5M+ul\nAUx3ZPjRpvJJFYWtObhz8OWiRjapbx+bPTY5nHT8SkBXylUvATqWWCBhfp+g\nXbVgTnOF12j9aV5Qxi+rdh2AvGTx08lwhplPPomk5+fm2NrC4jLPBG9hpdJT\nD4g6gxg9BaOcmYzXh/LP0xVvOOEj5R2PMJPZcUmUiEsFZG/+nWAe9Hic8CMf\n/xlyw6nU4ST2WCgmauYMBAPaRBWkMHxC3SZskzvk9cZfTGT2vNNr0k8u3Tpx\n1t/f\r\n=aTZx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEqBhVyUUDryHcQYiirueIzb1/BxEEgVJ1P7PgJd/tIKAiEAogrf7cZOaYAEvqj0iAYkCSo3PmAH8BnN+EQ/JSxaJtA="}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kleur_1.0.2_1531859174324_0.950043479885095"}, "_hasShrinkwrap": false, "deprecated": "Please upgrade to kleur@3 or migrate to 'ansi-colors' if you prefer the old syntax. Visit <https://github.com/lukeed/kleur/releases/tag/v3.0.0\\> for migration path(s)."}, "2.0.0": {"name": "kleur", "version": "2.0.0", "repository": {"type": "git", "url": "git+https://github.com/lukeed/kleur.git"}, "description": "The fastest Node.js library for formatting terminal text with ANSI colors~!", "license": "MIT", "files": ["*.js", "*.d.ts"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "lukeed.com"}, "scripts": {"test": "tape test/*.js | tap-spec"}, "engines": {"node": ">=6"}, "keywords": ["ansi", "cli", "color", "colors", "console", "terminal"], "types": "kleur.d.ts", "devDependencies": {"tap-spec": "^5.0.0", "tape": "^4.9.1"}, "gitHead": "157fb4698a7ecc7db1b489f3614725221ce2dcb5", "bugs": {"url": "https://github.com/lukeed/kleur/issues"}, "homepage": "https://github.com/lukeed/kleur#readme", "_id": "kleur@2.0.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.4.1", "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-/qttLvZoBrppPL02D0H/Su5sbwFUeqODNbkv5/3JNOF1MQGjauatvbOKWvMC5dFG39ppVmq3c1JnYj6fuE9diQ==", "shasum": "07f94bb6a94f87c0e23db2e70886f72fa785e0c2", "tarball": "https://registry.npmjs.org/kleur/-/kleur-2.0.0.tgz", "fileCount": 5, "unpackedSize": 10256, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUis6CRA9TVsSAnZWagAAS60P+QEHCSwr9Clnx84XR/b4\nMqsgRL3IOIT5g6JAB34gfENjEHOd9I0fQwIatC/uEjzB+x+PjjLnu33T4aCV\n1VKNxe4qJNJ2oiVPw0Qg+XPuFi9bWz4CyiFsXKnvmU+KJ/Ra3hymPnGAbPJQ\n+MKgBomhPC0su/9TKJvBdnEx+VczklquRqkMdInhy82FkUsCrIoruQQLAvP6\nnvOSmN+d7Q9zO9aqsPu3HfOx3JzCIRAKE4ZNLwt91SutVRKxXPigpKP04fO/\nYNZ0VC598PJ/d7VTl3cuzmoGj0qwTLgASWKDCjLS31Aok4LR1Z9Ky/RKqd1q\nAzcoGhzwZaW0Brb0fFQo9V1Z+dFGHxzNBsK1Pw41KIO5mkcuLAbJA5MhZP95\nKHS7lEGO5vc9OE7Dqe/GfiSGmdOTCFuDlf9+2MpnSn0COvH98m3QoY9nNoaA\nDaQP4Zo1EoInDmB+9oVybtVzJzJTNGolZFvVIKSQ5NFIFujcLSL45QDgA8ME\nLymI2GcviH24j9pNW2cmu0hUlg4UFgCLJAH+Isb7PosTzhz31dFo0wF0r1ZR\nJgkWR0P/jOl8/VyzOXXudZtHGBmHJndHo54VzAP8Qi5AgQA+XAbIRlA+eFnC\nZipU58fSX8AT8/47AcfbIVbI25wT0SspBR8NU4OVzujZRhNUjWwe9wsZz5Zv\nqbmH\r\n=N7Sf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDbl4FXtXUjLjV0eT7jAOV09SOWzxYarfIHKSveROeKYAiAjhTqw8uGJwXTnb+qdZJXdrH+cx5FLzF+7ZEUbn/ndBg=="}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kleur_2.0.0_1532111674041_0.7748577010078599"}, "_hasShrinkwrap": false, "deprecated": "Please upgrade to kleur@3 or migrate to 'ansi-colors' if you prefer the old syntax. Visit <https://github.com/lukeed/kleur/releases/tag/v3.0.0\\> for migration path(s)."}, "2.0.1": {"name": "kleur", "version": "2.0.1", "repository": {"type": "git", "url": "git+https://github.com/lukeed/kleur.git"}, "description": "The fastest Node.js library for formatting terminal text with ANSI colors~!", "license": "MIT", "files": ["*.js", "*.d.ts"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "lukeed.com"}, "scripts": {"test": "tape test/*.js | tap-spec"}, "engines": {"node": ">=6"}, "keywords": ["ansi", "cli", "color", "colors", "console", "terminal"], "types": "kleur.d.ts", "devDependencies": {"tap-spec": "^5.0.0", "tape": "^4.9.1"}, "gitHead": "37c6203dd6faf30e2df85fccd89d61e14aa1fbbc", "bugs": {"url": "https://github.com/lukeed/kleur/issues"}, "homepage": "https://github.com/lukeed/kleur#readme", "_id": "kleur@2.0.1", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.0", "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Zq/jyANIJ2uX8UZjWlqLwbyhcxSXJtT/Y89lClyeZd3l++3ztL1I5SSCYrbcbwSunTjC88N3WuMk0kRDQD6gzA==", "shasum": "7cc64b0d188d0dcbc98bdcdfdda2cc10619ddce8", "tarball": "https://registry.npmjs.org/kleur/-/kleur-2.0.1.tgz", "fileCount": 5, "unpackedSize": 10269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUr/ECRA9TVsSAnZWagAAV+AP/RWfNS2DDdNEvdMLeGrQ\nmNgofCkve1seuVn/Au4YKLiboRMyctQhTQKda1Wwd+58R1qn9AAOHO/Fpth0\nUMgKYNffr2dztaE92p1Bu2bx98yCMARSEO79N6aZ3yYr/HLkeqoemxa+W6aF\nKncQRXChyn1mf0GWGYpxu0c/MsoBlNWhJg3sGghPwH9xBPowLKb0WxtXIYv2\nJl4TX6zPmGf3abFqkilijcLnInt3A8E+hLtaK6Z1z+LhjTYFpQtvukkAvX3h\np6uFGfw5GN4xHg2oFM8NjjsMPXsyYwhOyOpI4f2lt898kOJtbkO+jvBxcwaf\n7pCW0mrrrMfImSN6FA5TncSFM0z9q49BqOpNuPN5VgxKtO31REYQYHQyyOBq\nfcFRrv+FvVyeeAv1MEQR96GLiAVTwuLdwEoyzMeDcmSI2e4Mcv0q3qGHmGpI\nbijVpdf5Gqs4hHhRf3Zh1LULgW1GEgIm+mePxteeKPgo/QMte/AHqmloG5+I\njqKocS3hWgP/dXR00WWw9rFB+0VTmJZyTxz5ut8qK8/jYsYn3w72RuJupmAv\na8ooWQ+bQlmO2sGr9ny7lMK6LjqxjWgA/BK3Jwro7v0OqH/Q+yUZ0yGBr5J7\n1lTSVCEaT1sDlxRptdWGpwaV3T3t1W+zOb9D/CChCm7HhzoYpE5cz3gFpFUy\n59c/\r\n=Gf29\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6W9x3/NL2dXtgqOavOazwhAJYRbTARKHomLcY6CqToQIhANhfgZ+AgOtEOo2/7+dknBRE7TaV9/MNYepKqpMtciu7"}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kleur_2.0.1_1532149700014_0.4890486241669678"}, "_hasShrinkwrap": false, "deprecated": "Please upgrade to kleur@3 or migrate to 'ansi-colors' if you prefer the old syntax. Visit <https://github.com/lukeed/kleur/releases/tag/v3.0.0\\> for migration path(s)."}, "2.0.2": {"name": "kleur", "version": "2.0.2", "repository": {"type": "git", "url": "git+https://github.com/lukeed/kleur.git"}, "description": "The fastest Node.js library for formatting terminal text with ANSI colors~!", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "lukeed.com"}, "scripts": {"test": "tape test/*.js | tap-spec"}, "engines": {"node": ">=6"}, "keywords": ["ansi", "cli", "color", "colors", "console", "terminal"], "types": "kleur.d.ts", "devDependencies": {"tap-spec": "^5.0.0", "tape": "^4.9.1"}, "gitHead": "6c0b80086d630cf7887254265ce42f5739dabeb7", "bugs": {"url": "https://github.com/lukeed/kleur/issues"}, "homepage": "https://github.com/lukeed/kleur#readme", "_id": "kleur@2.0.2", "_shasum": "b704f4944d95e255d038f0cb05fb8a602c55a300", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.1", "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "dist": {"shasum": "b704f4944d95e255d038f0cb05fb8a602c55a300", "tarball": "https://registry.npmjs.org/kleur/-/kleur-2.0.2.tgz", "integrity": "sha512-77XF9iTllATmG9lSlIv0qdQ2BQ/h9t0bJllHlbvsQ0zUWfU7Yi0S8L5JXzPZgkefIiajLmBJJ4BsMJmqcf7oxQ==", "fileCount": 5, "unpackedSize": 9319, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhcvwCRA9TVsSAnZWagAApNUP/22BHLlawRFdNkXJk8mf\nSbJ0poGEaDkFdJfJsahguBaBEDTrEM94pYfA48hb4crMYeDqk2VM5OgM3qfe\nu3V5jHPxo8uRVfzIdQDplDJPTw33rYUx6uqzFU3OMa0AfzMQtsMtmxV7Rb24\nThXGkZixXzj6zLjGF395Jbg9KAdgLrVQoB59TxqI6fjJrswtbq+43nYMOFjJ\nbm0KkWbGsE5YlpKv2Bk2ikRXGzlM4Co34BLZ+7n1EpxEjIsDHg2oPPynmWiD\nczQdrRhLTyiIE5Qp8a9qZ1E+ZKCWh9C0UJpMzV4tmkVkkMJ+1tFwsZ8z0KJr\n0wYAhMAn47e/+7HyPfAQcptoG72FosWVOYcEb6e3fLFDZZm9Pe/WoVbZH3AG\n9KeoHg3GpkekGx2ZYKRpKBY9bGl43OUi/M2XK0clnHdz8a7LA/Uo45Knk+He\n50vKmcfC2sjI3S0vfPgkSvbCtSmh23aCN8wi0W3gaduz99uj9o11OHmz10Lw\n/DPbSKne2+83yKpL08s5xpL5yxBYZyBd+w7yWoURVkrlVdtYlj+z060+Inhu\nEK6Mk1gSMpEZ68gDpBeQNd6oMZJQcM3nI8sfBibixbV90zVeTjG4MyFeuRtz\n1epIfpdghbcR5OI6S0s+KKgDt1Jy7mxWIWM+eN9JQ1lBPCW9Wc/Nw+od+W1d\n5Uzk\r\n=4g+C\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC3EIZZfWy4kUHzlWbYyDtReg9V3w51STNOkK68vNlksgIhALCH37LxTQYfTKLu2M7go2R3gV9nh9H+FOsz5TU2qQ1b"}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kleur_2.0.2_1535495151707_0.6351566051956061"}, "_hasShrinkwrap": false, "deprecated": "Please upgrade to kleur@3 or migrate to 'ansi-colors' if you prefer the old syntax. Visit <https://github.com/lukeed/kleur/releases/tag/v3.0.0\\> for migration path(s)."}, "3.0.0": {"name": "kleur", "version": "3.0.0", "repository": {"type": "git", "url": "git+https://github.com/lukeed/kleur.git"}, "description": "The fastest Node.js library for formatting terminal text with ANSI colors~!", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "lukeed.com"}, "scripts": {"test": "tape test/*.js | tap-spec"}, "engines": {"node": ">=6"}, "keywords": ["ansi", "cli", "color", "colors", "console", "terminal"], "types": "kleur.d.ts", "devDependencies": {"tap-spec": "^5.0.0", "tape": "^4.9.1"}, "gitHead": "7528bcd17017d2c30e038b6abe55f0423ed24f2b", "bugs": {"url": "https://github.com/lukeed/kleur/issues"}, "homepage": "https://github.com/lukeed/kleur#readme", "_id": "kleur@3.0.0", "_shasum": "ae185c2e0641ffb2f8e3d182df13239df9d8d3b2", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.1", "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "dist": {"shasum": "ae185c2e0641ffb2f8e3d182df13239df9d8d3b2", "tarball": "https://registry.npmjs.org/kleur/-/kleur-3.0.0.tgz", "integrity": "sha512-acHc4xKlqTNuAGmbvtd3KuNi1bnlHsdPg6Os1P5s/Ii/6g8MY3caVPDp4md04/twbh4wwPvdpnol1bc9zFsI3w==", "fileCount": 5, "unpackedSize": 9880, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb7eTrCRA9TVsSAnZWagAAyhEP/0vvvi9F6UFz6ApRBtry\n9F3UaK4z9dLpUryigbKQQmhNYrDTFWX91+u0EUzAsC4unCZYdHjKU/+9RvKX\nBX8jaBBAbPbg80rteTWDJGbSvjwl1Pw6TGxhkr81mUWeo9rDLYQqMj3+vk33\nVYfa2//6KrX1dD6Db61aleTiUiax/qKifdTES3PWGo6InfZx5qQpbGd5zDpK\nU0Sn6A9MJy0Xi93G+PU55Z/W4aLyzOKGeRWuVJ+oENBIlUcEfeztWxokg14T\nrcQ69OD71xMuple++oKlwpPGeZI2q66bIQRirrWD8lJ0CnZskFRnIEXeVC+w\nwcTUW5vBDCgEmUS4J0lChTUaTDJNAKj1fvrVPPrI09fj8gsaRTJhZoaZwMyL\nUAWxeyX9SZsN7rIs9Y9xZgztJwAEXae4caUP2NtM5KOKUMYuZTQIwF0ghEZb\nF5KIJ30tV+vb5MxqH0V+MDs+anIPm+LJ4IyfFDbGQeUPr7MG7urf8tp7orwH\nqVx3sA5QWFY7rvS+Io48VwIcxBDf0xAfig74wzEPGQtzgXTNwVH4Gl3Znio7\nJ0DcF7G4H5SKE0C0ggAG7Kf9UA1qrbK+yT7WHynsNpdEIuwV1CQ3GWarYlCh\nuoF7Hj9TvfGh2/XfGfSf9dd/lVk/1vqTkcjqrX/8yK6dcWaah0bb/1bYcWJU\nsK6n\r\n=YxFe\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCkpcqP+kbn7Iqkt4F4DpeK8HkTPZuA/r4l6NNbnud4qgIgJbo7itT95Ju6+PMmF1I7oqHevUITqkNc2TkUJBVwCFw="}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kleur_3.0.0_1542317290413_0.33217020643028583"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "kleur", "version": "3.0.1", "repository": {"type": "git", "url": "git+https://github.com/lukeed/kleur.git"}, "description": "The fastest Node.js library for formatting terminal text with ANSI colors~!", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "lukeed.com"}, "scripts": {"test": "tape test/*.js | tap-spec"}, "engines": {"node": ">=6"}, "keywords": ["ansi", "cli", "color", "colors", "console", "terminal"], "types": "kleur.d.ts", "devDependencies": {"tap-spec": "^5.0.0", "tape": "^4.9.1"}, "gitHead": "59d7d74f461a338141f3bd16ea21d8d1f814d367", "bugs": {"url": "https://github.com/lukeed/kleur/issues"}, "homepage": "https://github.com/lukeed/kleur#readme", "_id": "kleur@3.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-P3kRv+B+Ra070ng2VKQqW4qW7gd/v3iD8sy/zOdcYRsfiD+QBokQNOps/AfP6Hr48cBhIIBFWckB9aO+IZhrWg==", "shasum": "4f5b313f5fa315432a400f19a24db78d451ede62", "tarball": "https://registry.npmjs.org/kleur/-/kleur-3.0.1.tgz", "fileCount": 5, "unpackedSize": 9752, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcCsmrCRA9TVsSAnZWagAA3AAP/iPIIMcBRXCA8aDFfRFL\n0zy74H+CkH7qQXuEsxD46Ck/SRuCoIONnFby50FSwHl2qM4RKKXQx64CZC43\n15fsjelWp/0F313sH0J6SoeqQgVd8+6A0SaK1ZOA0hp+ScmDGl878NBtoAGC\n75xsQYckTD6AE+6YTpXZuVfiiodfQThWrQXgXm8GVlfRRiT64FCsdtiR3cJ2\nfproMnU/BX4dvzPY/KyFeKbStq/XD5gyTpIXnnDaibdmgtzD+qzc9pfGksXy\n6voMZmPX+rFFfg49TUpUXRjgo9ior/06BzNyMMDw5daaE8f1j33brzXyWn9Q\nj+y/4bna7FUCDZmcN0YbiQ/YSMxgCxxC6B/z+G/Sw9my6r7zZ8HSvIJLUvqJ\nNOynp4mB1qIzsN0l4plS7z+Ku2aHYHL5yEtLufolJnCL/MVBOr7XKQeMKb+B\nwVg5brERPUdjfPuLw5X0XmHzHsO/VXseroT6985fvSS+EHIy+bJmFbaTyxo2\n4d2uWdMeeohnxQ0JMWBzeO6eIJI4xe0ZmRpKVOOF6WHh0F8OMozvXVy6pRlV\n5wVDUsMVjaPdNbEU/uWJ2gWppqnG6cXG/5cHJUnZPqMcV8vVf3inoDu4Lzt4\nOXPFiWUDb35yvwyzCgcs3zbSvG2Ye9NwQi+eMKk2YxO2gNoaKXuTH1IaTvNm\n6zIq\r\n=8/oe\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDKQZGw+HalYNDDA4uBwkgGZwQao/drWOsCDzEb3yx8NgIgVxo1Sy1w6UvfWQq5RfHWCUXr1NRxN6wLjzmoV0B4D9w="}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kleur_3.0.1_1544210858263_0.6552051127407896"}, "_hasShrinkwrap": false}, "3.0.2": {"name": "kleur", "version": "3.0.2", "repository": {"type": "git", "url": "git+https://github.com/lukeed/kleur.git"}, "description": "The fastest Node.js library for formatting terminal text with ANSI colors~!", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "lukeed.com"}, "scripts": {"test": "tape test/*.js | tap-spec"}, "engines": {"node": ">=6"}, "keywords": ["ansi", "cli", "color", "colors", "console", "terminal"], "types": "kleur.d.ts", "devDependencies": {"tap-spec": "^5.0.0", "tape": "^4.9.1"}, "gitHead": "24eeba40e31d9811f6979baffaedc2dc44b5de15", "bugs": {"url": "https://github.com/lukeed/kleur/issues"}, "homepage": "https://github.com/lukeed/kleur#readme", "_id": "kleur@3.0.2", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-3h7B2WRT5LNXOtQiAaWonilegHcPSf9nLVXlSTci8lu1dZUuui61+EsPEZqSVxY7rXYmB2DVKMQILxaO5WL61Q==", "shasum": "83c7ec858a41098b613d5998a7b653962b504f68", "tarball": "https://registry.npmjs.org/kleur/-/kleur-3.0.2.tgz", "fileCount": 5, "unpackedSize": 9836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcXKHhCRA9TVsSAnZWagAALVgP+waTJ8YIbRLTe3yIBPrx\nfXPg/hSmXR1TiG2louLXJVZk7+BvGhWH8piVSZ7n/KJEDKKiWe6XkTX8jFrH\nMVO6EnL356iSMPJjfluRWi01z6e6k5kFE5FQ7U12+8+zJZbf2aEBZGabliEJ\nflIYAWRofGJCa/JSMjtqle5DU9s5KTEvo09Q5oEF65yRK7KhhkB5jx+w9RG3\nwjU79ZjDbwzQn+rORRqnTw23/BFbQ8/NzqFNugd0NS7F2ZePZLb5OXJ1XRCv\n17BkLcEFjOq91GzxesiZnIPy+Wy6ru3WMRqBABmYnk2dxbvSW4n1hA3sVEih\nFDtp/DztLDA9uzfl6y9Oz2NTtn52TFf9kx3EWQkik87BwqQo+eSkjfF1wboe\nU/RByVqPIY+X9z1ms4o24i+lHuYaYZHtHrdExyEbebTkPdILIkRYKR+7VIJw\nax26CDiqLnDBQpBSmPJjiPvJ+Y58jx0VJ80sfSjixt3dfz1oYC4aLFQggxER\nrY6gjUgOs/ElpU7AfbKRrBvi5K9Cp1fAUjmhpfn3N16JJNHXJuyjny7oWEfP\nviprA0DL9oz7livevmuGE+LfWxI4wSBBNeohtx9F6N0b9KpAYvEbfJR3f7+O\nyin8bjRcaWnANpg5+Mq2VGeUnalGPfGHMDt1wn3u9O2o1L5s5AqkXvuzw2Mn\nwaX3\r\n=VoJw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHwzKOEIvTfyNRu/kgicI/A+sBrPbRyXTG7T5mlWSWe/AiA2umoqS5kBesulIhyBuEDrngM5HHNQnA3vmHR7S8LKUg=="}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kleur_3.0.2_1549574625186_0.8031216169885154"}, "_hasShrinkwrap": false}, "3.0.3": {"name": "kleur", "version": "3.0.3", "repository": {"type": "git", "url": "git+https://github.com/lukeed/kleur.git"}, "description": "The fastest Node.js library for formatting terminal text with ANSI colors~!", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "lukeed.com"}, "scripts": {"test": "tape test/*.js | tap-spec"}, "engines": {"node": ">=6"}, "keywords": ["ansi", "cli", "color", "colors", "console", "terminal"], "types": "kleur.d.ts", "devDependencies": {"tap-spec": "^5.0.0", "tape": "^4.9.1"}, "gitHead": "92cc1ba0c1fd396ea565efcd445f1bc3cc170a00", "bugs": {"url": "https://github.com/lukeed/kleur/issues"}, "homepage": "https://github.com/lukeed/kleur#readme", "_id": "kleur@3.0.3", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==", "shasum": "a79c9ecc86ee1ce3fa6206d1216c501f147fc07e", "tarball": "https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz", "fileCount": 5, "unpackedSize": 9893, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqnNZCRA9TVsSAnZWagAAiHoQAJ8cA5/56ZETtUjK3rps\npH1y0bdjBRPxlAlIztdZSnJ3K9/epAyGGusRfspw5KgFAM0rqNi4+e0IABYm\nQ5mRzKOc1ViCs6IBarS1KOPS71BQi9Xs3+SOWH26GXHD+7Suxt0WRAc5z6BI\n2tJER7O4drA1gpcEyvQvm1Weu8GKcLO44yeGAgnBhCKlWMhryzC3Vgvo20JD\nS+d+ZwJpaM/DDCkEsqjMfZ3b8IqRqxPJ+ptB5KnBL3uFcq6tDttghELJbnvn\nbY1Ee+Pc7H1sBWOdN3vB1D2tM8GuqgATygFlrTGnNPkLbuZf+ZaA9R35PRZs\n8ZxiZB9IiXi6aIyQGZ44l6+7qAXTWRpTOTrrTWJcxqrmthROXAEJ44uhZnEp\nGh/0X8TCgCnXRmQj8MUT5x404YJCqye0bmCIG+Vc0TXrDrUpwyZRxvqM3YR7\nEdBnMAN2LzR+zL/l+sn/UZPzb9rP6uU9x9VtQYkoGOViMxcH6H+qQm0rM72z\nlz8fUo/nWNs0g2KQU98IxqLXOabYEzq6GoXus94+IgK75xHE48+Dax+fiz9D\nfKIrrqiYWFNcqcdQRO96uHBxmKUoBL5Uqv2KCeO7Ft7Bg3v+8lE6MNWgaqq+\nREpC+ZnbysiOSZfz8DMQsC2sHLzY+KW0x252Llu/RnLUW+qBHcD8tMMxhHLG\n2Sve\r\n=uy2Q\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC7xZnY8+7f4kELP9/xAmLKUXIq7y8hvkTjZZty4B3XoAiBOL62EmbT2a2lgrdXhlt1nKRxyCUFfPW+ZfA8Hkp/DAw=="}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kleur_3.0.3_1554674520986_0.24494671632555454"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "kleur", "version": "4.0.0", "repository": {"type": "git", "url": "git+https://github.com/lukeed/kleur.git"}, "description": "The fastest Node.js library for formatting terminal text with ANSI colors~!", "module": "index.mjs", "main": "index.js", "types": "kleur.d.ts", "license": "MIT", "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./colors": {"import": "./colors.mjs", "require": "./colors.js"}}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "scripts": {"build": "node build", "test": "uvu -r esm -i utils"}, "engines": {"node": ">=10"}, "keywords": ["ansi", "cli", "color", "colors", "console", "terminal"], "devDependencies": {"esm": "3.2.25", "uvu": "0.0.11"}, "gitHead": "741318f9ae3f36964eff018411d4417bfe86405f", "bugs": {"url": "https://github.com/lukeed/kleur/issues"}, "homepage": "https://github.com/lukeed/kleur#readme", "_id": "kleur@4.0.0", "_nodeVersion": "14.4.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-D8qk4wvB8ofBeLAsRNwnCkjbwdrcKYsMC/56pFQMOSfmmYJkG1O9/oBgXzD05K7biYAwnHjMI2abZ3YiuMY0Pg==", "shasum": "6da4e7a800e33eb28c53fb91902d623450a980d6", "tarball": "https://registry.npmjs.org/kleur/-/kleur-4.0.0.tgz", "fileCount": 8, "unpackedSize": 18393, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6YjTCRA9TVsSAnZWagAATgEP/2XCZ4YanNM0hcB9gaUp\nZ2DDnQQpKol4fUVPHFyGh3OF5aqjh9WMEyE9LzPLvRUMbT/kfSV1P4vKdght\nHxSTVb+ECFwsLTudtHgOwGKfx62EnpuuzI+k3F5/+J85x9PoKtDUe7qtHMqv\nNTloEzLKUs+c7hhCBJxAdwtzydRxmEdZ1P2w0qylZGQt2TsmXpureeZuFU5B\nVKv/VszyyC7DEVbDsB/LNdK+ad0ieISqBrYElc6wAFXX30izONTgztJdjHtN\n0JrEsObZYaZbfGgO5CRcNr7qMOXJFzQM7Wqut2CGUlOnkCv1vcqOZNZnNq9h\nV5zolA8IFTdl4hiBUauZepXV6ClHeP14kpDgg5a4bgkpWmYU7MbZeMwrXgUR\nCmbWlfstqqLVQPCk6G86ABsB5ib6dhdrEFTLY6W3y5AGLFSyeJglXuI87p5Q\nCfPwN3YNSJ7KDRy43k3nU/nU9U5Y6a4ySnynVaS7pAoj3HtfjUAG+hylHsv7\noe4deMf5Yk3rfiZTWH0UnMM8sdCpErMfiC71ew9snCuyhTHelmyH1al7D2aq\nNa/tuni6xQ3Nf2bCB5MCRa65pIAHe4aBDcyIXMnocSJ7EZj/GiMAA08cOvT1\nRNVvD4pLYG/x6q88ptIrH3TqtMMId8FNX/caccvxa9uPOWVfSAtxU4T2KM9B\nsXqn\r\n=smQC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICyYA/EwpDXFbjxTnlfjkFXci4spRK10qNHWx2wzdQTGAiAy3KrOmPDgdm8nNkKqEUrEqIK7LL8qg1aPDQkQ48MLlQ=="}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kleur_4.0.0_1592363219285_0.09016312230048529"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "kleur", "version": "4.0.1", "repository": {"type": "git", "url": "git+https://github.com/lukeed/kleur.git"}, "description": "The fastest Node.js library for formatting terminal text with ANSI colors~!", "module": "index.mjs", "main": "index.js", "types": "kleur.d.ts", "license": "MIT", "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./colors": {"import": "./colors.mjs", "require": "./colors.js"}}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "scripts": {"build": "node build", "test": "uvu -r esm -i utils"}, "engines": {"node": ">=6"}, "keywords": ["ansi", "cli", "color", "colors", "console", "terminal"], "devDependencies": {"esm": "3.2.25", "uvu": "0.0.11"}, "gitHead": "4f2e8aae73dcc33285bfef829f90be0f43400c61", "bugs": {"url": "https://github.com/lukeed/kleur/issues"}, "homepage": "https://github.com/lukeed/kleur#readme", "_id": "kleur@4.0.1", "_nodeVersion": "12.18.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-Qs6SqCLm63rd0kNVh+wO4XsWLU6kgfwwaPYsLiClWf0Tewkzsa6MvB21bespb8cz+ANS+2t3So1ge3gintzhlw==", "shasum": "3d4948534b666e2578f93b6fafb62108e64f05ef", "tarball": "https://registry.npmjs.org/kleur/-/kleur-4.0.1.tgz", "fileCount": 9, "unpackedSize": 18363, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7EJ1CRA9TVsSAnZWagAABZ8P/379aA288xqm8yWkioVw\nYVPwuEI6LMoNfyOs2tRmbqSVUkHqgyrhYSQuElSK2i1Jrfk+5o85RRCVxPFi\n9f/T/3EW1xsXUs/pYez+74WGVKjl5Vnw2l2QGHFKIg45Ueqc3A+4i2gvYIpj\n34gKYJMbRr5h2GwfDwgYTjDwtH/N2vtjDEt4qt/HAbLFAukMBIM2aQugVC6X\n2aalfB7rAXgzHS6HLKfetNxkxFa0V+SJsVqJpia64hlr8Cd7D++i+VtZXacK\nNC70Ud0mTprOY2RKPC4WIKKxmrZZcjB8tWDw76W/fke24cL90/q6uZbw60kl\n6++bvCYmGw70nzbvKXJHJCSQP+klXSyHc+i86dsZQsylHtRXAUANJIJqhhSS\nsHd5w8a1t2P0K8tw/UXisrRi8uWCWeTZXVRbGbFOzTelwXk60CwRt/5sgBII\navIP+vqk7+xCEEzkFmi9txxuJk2QbmOSjPcUS7mE5V/IHLqWjwcnN5NfnrE1\nbv3rdAEzwz9JSakmZY6tecKIp1IBNyR+PLAqYeSkVgBOufWyE+S/B7qLmkOv\nr3wk1p8ZDngtZNDxshDs42/NJpL+sL9ihLR7kOmxF1mlPq3rGOHXtaZEDlqc\ntxp7+N7hMSJoSIAl8wmIS9ty0leU5TmGsSXmmmhO0PnUxzCsiOc3UggxiUMG\nJ+YQ\r\n=Lff0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIACjtXKDm8ylE+q5v5eqwmm0pqilSNIdnpVIYcKmtOxpAiEAx2p4CiOR8aS5cYbmH0ujanfXQzHeN7iHLRIjRFOx0lc="}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kleur_4.0.1_1592541813094_0.7411315904484708"}, "_hasShrinkwrap": false}, "4.0.2": {"name": "kleur", "version": "4.0.2", "repository": {"type": "git", "url": "git+https://github.com/lukeed/kleur.git"}, "description": "The fastest Node.js library for formatting terminal text with ANSI colors~!", "module": "index.mjs", "main": "index.js", "types": "kleur.d.ts", "license": "MIT", "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./colors": {"import": "./colors.mjs", "require": "./colors.js"}}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "scripts": {"build": "node build", "test": "uvu -r esm -i utils -i xyz"}, "engines": {"node": ">=6"}, "keywords": ["ansi", "cli", "color", "colors", "console", "terminal"], "devDependencies": {"esm": "3.2.25", "uvu": "0.0.16"}, "gitHead": "a9b0ac49c25071a04fcaaf5e467a8c15a8e488ca", "bugs": {"url": "https://github.com/lukeed/kleur/issues"}, "homepage": "https://github.com/lukeed/kleur#readme", "_id": "kleur@4.0.2", "_nodeVersion": "12.18.1", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-FGCCxczbrZuF5CtMeO0xfnjhzkVZSXfcWK90IPLucDWZwskrpYN7pmRIgvd8muU0mrPrzy4A2RBGuwCjLHI+nw==", "shasum": "57b36cc5235601f824c33e6e45db10cd5493dbf5", "tarball": "https://registry.npmjs.org/kleur/-/kleur-4.0.2.tgz", "fileCount": 8, "unpackedSize": 19204, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8+B7CRA9TVsSAnZWagAAqPQP/1GwjcqjjxItX3UIsHIR\nKmGXFVqSKEP1M8e50WRBpPFR83UdoyRHAOl9Q9NDzisbuutnzdho0bYiMwPy\nCOmtmrJgAY/bfCETcjVTsYAlXBfcu3fqU70SfrTDJVs98AIlQoeEYKe8E7IG\n/qVvCaOnkIzigaYgwSHE3RYeW+ASU0pXp4ehkzZWJQXPdx9Yw5Cd0DYjloPl\nwX67/QSwRou52x3rzeTZObOfB7kNEc7z/cpLaws9316TLI1BJvfN3eVfoLGl\n6pYwwgDaaBH1aC1AkCwBlA01cffEwjtcIQJUDeVlccHesz9sETOJCZ988esR\nfF0kpjvEWTZLNtrhhzmGYTM+4BkT7GVocJJF+PBT2KSapzLl3Z55Vu+NB5PQ\nq8oK9vjWRGFg1XwdN4+Zr1rXax+4PFWk9QyrRq2AF6L0sbQLl+Orwr0EF0ah\nFMfEobXmpXT8zbicmuK2ULCAWEmrZ4QMBC4O8D4V5q1EcyaOBTvVJEO7Fe+N\n6j4legyV2aojm4AbNy3PZkLKQEoR+OIaRgjb2PEVi0otSojogSiWHRkvpw5D\n/r3ok7UnWQFCiNI2qoO66UIcEh9ADO1kdM4ugJTufNo3j2QsMQBUMpjJcPfe\n+voFHGhwz/EW6ojNrzssCz8P4H49m1MaF1h+PtVyECEZ8x8DKwKzWfTvH1Gj\nVU+c\r\n=g/iD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDJwCUi8GNn3njR8fDOltIklKGiD3N7eKUriliXzqrv6AIhAPGcfLaWzXD17wnxulj7DIU7goIdIwFAl6brIGEznAE1"}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kleur_4.0.2_1593041018717_0.0023426753575697123"}, "_hasShrinkwrap": false}, "4.0.3": {"name": "kleur", "version": "4.0.3", "repository": {"type": "git", "url": "git+https://github.com/lukeed/kleur.git"}, "description": "The fastest Node.js library for formatting terminal text with ANSI colors~!", "module": "index.mjs", "main": "index.js", "types": "kleur.d.ts", "license": "MIT", "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./colors": {"import": "./colors.mjs", "require": "./colors.js"}}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "scripts": {"build": "node build", "test": "uvu -r esm -i utils -i xyz"}, "engines": {"node": ">=6"}, "keywords": ["ansi", "cli", "color", "colors", "console", "terminal"], "devDependencies": {"esm": "3.2.25", "uvu": "0.0.16"}, "gitHead": "6607ca4a9ac6ba1409a756b3ae28e8856b9d82dc", "bugs": {"url": "https://github.com/lukeed/kleur/issues"}, "homepage": "https://github.com/lukeed/kleur#readme", "_id": "kleur@4.0.3", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-s84+52u0qkMmOI+eFJSxOCS/2Xt4j2A7Uacg8j+Ja1uFrED2zV3bILKtdAmpJCER4ufTej7p1OwuHbUz5ZVUyg==", "shasum": "c66f27b804dfb89f804de5f44290c9001979e685", "tarball": "https://registry.npmjs.org/kleur/-/kleur-4.0.3.tgz", "fileCount": 8, "unpackedSize": 19640, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfJF+VCRA9TVsSAnZWagAAyRcP/0I0HY6cKza+XZ4P4eT7\nTDyCoLTMkMs5TKqDHCJRgzXFR/jKOLue5IoWLvgCkRiXNYFpTHmnXBHTykTE\nxq2Xg1/cjUnUNjZkdWuNDlKDJoTJ5ap3x7+ueCUdkHsZ77c0f1XM4m+LRiMl\nuM6RVsWeDz7Wm4R6bCCdaaK7u6GmLJ9i2EQfozA0oZ4ckS6rDMJeknkzvlws\ndObpjbzbXHfDPM9xV5b+6cvwKgipZPU5su0V96LPFA3UUc28kMJFWrdTnV+U\nSQzjbaXjEN/DaOE3zO7Fb+e7sGmdLrbZfBwCPgt8O8JU4g1oteY9Lezor/Cj\nZIb1yO5suKoxmVyjwG/8PsOnttp04iS4tC8KZ3XjWY1hxAyqbXfDIdKwZsof\nAggNejk9XuaKG7ENfyvASv+dX5OPZtn+mybL0MT44/pKGDIC1MdX6SvehaPB\n9E53qlg2qceRQayQkqYjUS8fHSs/OSMQaTd0naxwIgTV/XoIK/KwPrVfWFry\nUQJo2+xFVMJokkcfDcxEi64sFIEWWMTPVBWldZuAFB9gJcTeMq6yHXTTuIu+\nDSsel8Vh4NdOeVqJbIe92M+gv84g3mZ8VUl8tNNnXYmCvaU6ZzE0ZR/9BrK9\nXBBFi0NIa4yCTaZu8VzOitAud/1XPHort0f68wMISt3gNTmbV2NuqgMwUGTB\nAmTj\r\n=xHd3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDyAejySst9huxBPepgPhtYwJjaHMjwmyBU9tXluVn/pgIgRM1qRMPq8k0M8ZXCrx+a5jN77fJcteMeOMaPCggXCgM="}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kleur_4.0.3_1596219285011_0.8002054529634959"}, "_hasShrinkwrap": false}, "4.1.0": {"name": "kleur", "version": "4.1.0", "repository": {"type": "git", "url": "git+https://github.com/lukeed/kleur.git"}, "description": "The fastest Node.js library for formatting terminal text with ANSI colors~!", "module": "index.mjs", "main": "index.js", "types": "kleur.d.ts", "license": "MIT", "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./colors": {"import": "./colors.mjs", "require": "./colors.js"}}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "scripts": {"build": "node build", "test": "uvu -r esm -i utils -i xyz"}, "engines": {"node": ">=6"}, "keywords": ["ansi", "cli", "color", "colors", "console", "terminal"], "devDependencies": {"esm": "3.2.25", "uvu": "0.0.16"}, "gitHead": "609312459c14e59d02664ede01be71b4a7276a65", "bugs": {"url": "https://github.com/lukeed/kleur/issues"}, "homepage": "https://github.com/lukeed/kleur#readme", "_id": "kleur@4.1.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-s9jD+1dPhJVOzEAGjuabDC1NJdNr1UGj84SftcGukJDpQaLbwuJTakb7FqAhm4cFkhaIvS2DV092FXMyUgPt+w==", "shasum": "eea37aa8c210a06c42a591ee56a13f137a13add3", "tarball": "https://registry.npmjs.org/kleur/-/kleur-4.1.0.tgz", "fileCount": 8, "unpackedSize": 19772, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNK1OCRA9TVsSAnZWagAAoIUP/iO42ALgqm5kxIKbtJNQ\nG4EYrQfqFzdz6dg8TlutSlojaYgB5YbpMHSF+NNqBeGUhV2k5noqwMhzEsmi\ngRXajOuPhKPM5ZlRh7L+qU9eviWB7FMRSQXfnf/OdgKkPlwkXJryTLvqhrgp\noDoLoo3MV2SVw4yaZTVmeSfGHAw+4Yq30z8ijzQugUEWVSTFv+jI40Ou2SDP\nRk0C7Dt2LJDweqg6xak7hlfjN9W7vHtKZtLFjht9RBwRKKJfFqv/3RZ2tfjw\nWEwRY3UL3gad0vCibWnw3N7hHxGW2bIlCCTJbIzs2jDhsYMQXL3pTXGX67l3\nErv5SpYlI865EgYprP0Kjdta9G0K0HMNziE5OHlSQ8qzZXauzUps7ywKNDF2\nls5hJgRL6zAypjsqtYhy9h6kK+lq+rVnGaDsYcakJF8iWB0BYGfvDhVlFwI7\nR0IwSHCiOi3kAiiBtso5CSL6Xbqs/D4C6xnjmesascN4NSg2k/d/NS+pbaIR\nAg0ObzKGfg0lYTHGNmQVK9PQyfwcsC0JgL894kiRxZbkGAzN7HOoViek8dS+\nKxuaxNjnVI4CL39cJwwIdYYz2ODH6NyUsh3LQai9TgxKtdZ0+PLqwQiHrOKg\n8ExqXp08QrJyGKdSxQwXfkqIdymJAXE0RtkI1WlrjlngSeK4HSgs/PVqoX6E\nffjf\r\n=of/V\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFKx+esAsIl5PMke3yL/G3cln9d9c10+udPaEeN85X6oAiEArOrI7QYciOiv2ss6KyKHbMekArb5zLivjZHfAQO5NcE="}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kleur_4.1.0_1597287757400_0.22475606815782823"}, "_hasShrinkwrap": false}, "4.1.1": {"name": "kleur", "version": "4.1.1", "repository": {"type": "git", "url": "git+https://github.com/lukeed/kleur.git"}, "description": "The fastest Node.js library for formatting terminal text with ANSI colors~!", "module": "index.mjs", "types": "index.d.ts", "main": "index.js", "license": "MIT", "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./colors": {"import": "./colors.mjs", "require": "./colors.js"}}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "scripts": {"build": "node build", "test": "uvu -r esm -i utils -i xyz"}, "engines": {"node": ">=6"}, "keywords": ["ansi", "cli", "color", "colors", "console", "terminal"], "devDependencies": {"esm": "3.2.25", "uvu": "0.0.16"}, "gitHead": "b08ad63ca21b9f28100c1d4c9517a77009fddd0c", "bugs": {"url": "https://github.com/lukeed/kleur/issues"}, "homepage": "https://github.com/lukeed/kleur#readme", "_id": "kleur@4.1.1", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-BsNhM6T/yTWFG580CRnYhT3LfUuPK7Hwrm+W2H0G8lK/nogalP5Nsrh/cHjxVVkzl0sFm7z8b8rNcZCfKxeoxA==", "shasum": "80b49dd7d1afeba41b8dcdf4ecfff9252205fc52", "tarball": "https://registry.npmjs.org/kleur/-/kleur-4.1.1.tgz", "fileCount": 9, "unpackedSize": 20052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPL+/CRA9TVsSAnZWagAAXGkP/RzmmcDGzBTGmgS+DjED\n++irfjwD4ZWJmB+b1P5ZLx45xFq9uHj6nc5Rpg4Xf6oQ5JAbCDvE960SnaYH\nsYNFkBlaOxZdraPprst8z8ephy13x0bDQd/7V41hnv9FW77lPx24/Mr+jrG/\nb/f9FuSmHpyhkM+88cHlk+xXH0nBUxq273af02a75lvbbTnwrTsAaX8ZuPdR\nG1OkN6e/tBspEjQ6maVmRVTqAIsLhDuLfvQW5QQe82euXfhe4QS1m9Szq4WO\nbwEHOaRjSjokjHddT0r39bjmuaSNU+2yIY5lXqalI1cNvsqkcwN3mt92luPY\nV7bYFhiEWcDzmGKbGwXruLUgC9M7j+IWGq5Kb9bMIFBpDOEPPMEiv+UPfNWn\nQZYiu82S1pcTcKxyqWgIsFE2aoFEm6wiQ0NYgSmyq8yY1GvkfcB4hxODLYCX\n4iiDgv5Nq+ZuVmVh8kxwigaK7g2yv9O6KcEEOZnX7BlUZqwYVZjrme0S2e7z\nn6+p38kqyRUyS790q7PpRdpgSaomtY3fAWsEfwGqSAn9N2Qb3t1kDbTZIesf\ncGI9k/JQVDbiU4Q/H26/2Z8S0X8a4P+VEBu3lNLTJdBUnGT/+EQ+Y2xb5P1m\nV2qNUUeegyAuwu5ehs0xtISn7vG5iuePAhsTqXjH3aYsyVsIjFXAAPHTQnoC\nVgMQ\r\n=07nM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD1YTlNef437MRsmHHkJQ93n3yq2di6IN/lbOoZgJHAQgIgWzeH62jJeqTwCAEtFVMCmOXsNQKePL9/cc9ttVZViV0="}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kleur_4.1.1_1597816767456_0.6956323849939232"}, "_hasShrinkwrap": false}, "4.1.2": {"name": "kleur", "version": "4.1.2", "repository": {"type": "git", "url": "git+https://github.com/lukeed/kleur.git"}, "description": "The fastest Node.js library for formatting terminal text with ANSI colors~!", "module": "index.mjs", "types": "index.d.ts", "main": "index.js", "license": "MIT", "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./colors": {"import": "./colors.mjs", "require": "./colors.js"}}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "scripts": {"build": "node build", "test": "uvu -r esm -i utils -i xyz"}, "engines": {"node": ">=6"}, "keywords": ["ansi", "cli", "color", "colors", "console", "terminal"], "devDependencies": {"esm": "3.2.25", "uvu": "0.3.3"}, "gitHead": "2cc92dd7b71f6d1233f708e34cf2f10a2b291cc4", "bugs": {"url": "https://github.com/lukeed/kleur/issues"}, "homepage": "https://github.com/lukeed/kleur#readme", "_id": "kleur@4.1.2", "_nodeVersion": "14.10.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-GDy+aV+1kr510OLi3Rkb81+t3orUyX5iHjNFEmHw6WJg6PB71HPhCLHOh2pWNMKLd7L7ywI9rzpSR664E20TQQ==", "shasum": "aa58ce5e76b84fbd571307f1d1b77f74db8593f3", "tarball": "https://registry.npmjs.org/kleur/-/kleur-4.1.2.tgz", "fileCount": 9, "unpackedSize": 20079, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfb4ejCRA9TVsSAnZWagAAUMgQAIjXy56XgAQCHlqh88tU\nnPUDGpvOSUcaRoAHo3XQgQ0uUch37L3Kg8a+9QhhCIVWX7kG2zqhHGlh5qh7\nIty/QFZjCvCVpPnUZRfoQBDcpCZ2ceJpdGEFfIZkqvuowq7f62UYiVj6/1Wh\nyuB3myim8LfhwWwZuJKNuOw5FuFfS8Qfxd40KEOZ7if2JWDlwtkPRYBaF3ho\nNcnd5XzZE+H150EdChk3QjVCSCKETSYuMQxgGfQ7VTu4xU6YPgDdKz68+LJd\nvoCmnivEUTWFB8s0bSZGc2PNmCAPHFz7dcaMdzJZKBanLBnlREEK+KQKKPea\nlKtfFpGRKdgH1W25mUvT8P60am1sHIkfErn9NW66tQJig8bm4v3/qLNBZsIy\n3SlQ+/oXpZPqOp1x03sOiSUNyaRVbj+cd7x3o2/mEoo9Hr1Y3JiVoyrazKvQ\nMZVovev6kOYLBnaIExHz8xNzsGNa/DWwYeJuzxWkQ5QKJ0wzb5aA1OFfJRpy\nVHujE+JGmGG334cDyiLWgPmjdN8ygrTRmOpgtHfXJxzKWffz4DaNNvJe6vFi\n2csfvjupLAB2nVZqq7smbW6GoEWfQu67b1kttTkMee7gnToCUxP6kvvduaec\nhUxdDbHS3AnlavbdfRgUZ602C9dl8B85fc2GS/m6i6av73kf4c3k46r7Q/ok\nz/u2\r\n=PpWw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCWwprIr8yp4JHzsbM2mflxL00WWKKsbl26pDJQB1eBbQIhANwfUkgof7W81fwQR/uNBICfuPZduhOhLoXdaU907192"}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kleur_4.1.2_1601144738543_0.06430214167603787"}, "_hasShrinkwrap": false}, "4.1.3": {"name": "kleur", "version": "4.1.3", "repository": {"type": "git", "url": "git+https://github.com/lukeed/kleur.git"}, "description": "The fastest Node.js library for formatting terminal text with ANSI colors~!", "module": "index.mjs", "types": "index.d.ts", "main": "index.js", "license": "MIT", "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./colors": {"import": "./colors.mjs", "require": "./colors.js"}}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "scripts": {"build": "node build", "test": "uvu -r esm -i utils -i xyz"}, "engines": {"node": ">=6"}, "keywords": ["ansi", "cli", "color", "colors", "console", "terminal"], "devDependencies": {"esm": "3.2.25", "uvu": "0.3.3"}, "gitHead": "fc9cc256441363af0e0246e729e08069d3afe48c", "bugs": {"url": "https://github.com/lukeed/kleur/issues"}, "homepage": "https://github.com/lukeed/kleur#readme", "_id": "kleur@4.1.3", "_nodeVersion": "14.12.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-H1tr8QP2PxFTNwAFM74Mui2b6ovcY9FoxJefgrwxY+OCJcq01k5nvhf4M/KnizzrJvLRap5STUy7dgDV35iUBw==", "shasum": "8d262a56d79a137ee1b706e967c0b08a7fef4f4c", "tarball": "https://registry.npmjs.org/kleur/-/kleur-4.1.3.tgz", "fileCount": 9, "unpackedSize": 20151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdAo7CRA9TVsSAnZWagAAoIIP/RN0jyKXHBTZL+ALAB5N\nFQbwIVqdcahnPD2+5PGnIxF1LxEwlA/i3XpQYP2JWA3evvEV5U1ZyJ4P1pcw\nuBzlxeddJk0lv5pCKLHv49KYxML230Pb+8gjm6hqkW+z3N4JEWVvAp2ymse8\nN200YeuSdvKQV8Nosex7x44y22Wpj/ECggvh83PuZfuXUdnlL+RC74mB32J2\nkPrOeB6nCAaofK1B3dTUe8nrQk6Zv1X1WC8lRYv+ZLaFIkhmsuEqBqe5gex/\nODi6j9+X7ZJrDl4UEOfkb3nzZ+Y9RUA9SaFGhdwWmcBM1HnpRReHwCNbAK8k\n6KE+CKW2o2JRbfnmOd3UW4uhn7K2D2YmKKvkkJJh9SovqLvpvzg9/1aPNZFV\nuyQXyk82+ZYSmckkeFSUrSn71YJJk0HwK22TeHkeWDCpLdnFPfsHchWsmMQf\nkMWa1y6GQl28IHDZZjPDWVDpeehvEmxk0LfyUe+NupFdoGQkVsC4bSaE/PxB\n3yMfcaGYPYsT5ufRcOP5Y8z7PxRBcTGZ9Dn5YgOh3Nq81W3xm6YWhunMaN+1\nKnjjO7M3wvRtUl4ock6/38dkeYJ9kDlECfLNA9BQAq/7Y4r7v0zMc7SzgDx2\nSDyz9c1zr1dJq/2UUrgd0IaFa4+NXwYV0EXx69yd/Z1l442Vjaf83nKHggWV\nKlcT\r\n=Yehn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDrIzPuuxrMy5iP9xNiJ+zJwJAwwtjzViYIwV3JyJhqZgIhAOWCWmm/MUnR3EpNfgd+pAMt7yCet4DXbZSCyzqqPttt"}]}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kleur_4.1.3_1601440315215_0.9725884717785824"}, "_hasShrinkwrap": false}, "4.1.4": {"name": "kleur", "version": "4.1.4", "repository": {"type": "git", "url": "git+https://github.com/lukeed/kleur.git"}, "description": "The fastest Node.js library for formatting terminal text with ANSI colors~!", "module": "index.mjs", "types": "index.d.ts", "main": "index.js", "license": "MIT", "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./colors": {"import": "./colors.mjs", "require": "./colors.js"}}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "scripts": {"build": "node build", "test": "uvu -r esm -i utils -i xyz"}, "engines": {"node": ">=6"}, "keywords": ["ansi", "cli", "color", "colors", "console", "terminal"], "devDependencies": {"esm": "3.2.25", "uvu": "0.3.3"}, "gitHead": "598f24cb7f5054b7c504e9ab89251305311131b1", "bugs": {"url": "https://github.com/lukeed/kleur/issues"}, "homepage": "https://github.com/lukeed/kleur#readme", "_id": "kleur@4.1.4", "_nodeVersion": "14.15.3", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-8QADVssbrFjivHWQU7KkMgptGTl6WAcSdlbBPY4uNF+mWr6DGcKrvY2w4FQJoXch7+fKMjj0dRrL75vk3k23OA==", "shasum": "8c202987d7e577766d039a8cd461934c01cda04d", "tarball": "https://registry.npmjs.org/kleur/-/kleur-4.1.4.tgz", "fileCount": 9, "unpackedSize": 20163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCzKdCRA9TVsSAnZWagAAtZ8P/RrHhrLpXiuT0zY5jxS1\nrU+NRbUFCF35zavgBMVM8+Eew/PEP7lDmphbc9O5yhhWSdW9NgQadI+C/BGA\nJAb84ey5L4qa57Kzkf+jos+Z4b8piL+KkBsSH9vMAiE0FPxnNF17wvzYf4s2\nfieeC0NYlt/b28XZQBaGepbWag+LF3lSh7nPrljKn/XbVrTUywV/Tk75npzX\nYe30b9erJQOUm0wWLiQtnFts7mA/KpfVy75VbCkXjMozhRkA+WLoYLXhIrDw\nU8yZtyvUJKjFlWwFRf5F9aCpkB1Wu6cQuT5e7Ztyzg1Bta69KLJ+HoCBt3/Q\n5ZpNYeH92coWd40mMakNo60CVCaoL1ped1L1pRRcI/wgz2HFF2t3chUYphPU\nxV6QicwZZYoWPbC/W+pjWioHH918RV2+LjVOY62Jml62hm7tUMgcohmWKkny\nNgZnuUjn7fvvFJ9RnMR6xmm2Y3m4ZflfeJ09zoKA+zZUGgHRdmcUgSvAVZa0\n7Vzn6LSB1GIlwJ6nhlLNaJm3fvwHD077fV/Fpg7MSPrvUO5JLF3rqC648VJ3\nvI5qeXXQjo5dQ3CcfZJaJpUU23EjjTjbjlHGrk3u/USitP+37Q1FAxtxr9wg\nppF+tTL5rjauEeBGnrfWFE1LWpxrVXUHcm0pzn5+j4UwhMayk+ocb6lPDgZD\necWA\r\n=kLLW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC4V3LodHTvANLXSWatEd/cVu17GAofq83BZb6LLjR8EQIgCRHELKCOP8PgLlSXhl3oKpDHByFI/ISIFN0JmYERv/8="}]}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kleur_4.1.4_1611346589170_0.2558171946819896"}, "_hasShrinkwrap": false}, "4.1.5": {"name": "kleur", "version": "4.1.5", "repository": {"type": "git", "url": "git+https://github.com/lukeed/kleur.git"}, "description": "The fastest Node.js library for formatting terminal text with ANSI colors~!", "module": "index.mjs", "types": "index.d.ts", "main": "index.js", "license": "MIT", "exports": {".": {"types": "./index.d.ts", "import": "./index.mjs", "require": "./index.js"}, "./colors": {"types": "./colors.d.ts", "import": "./colors.mjs", "require": "./colors.js"}}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "scripts": {"build": "node build", "test": "uvu -r esm -i utils -i xyz"}, "engines": {"node": ">=6"}, "keywords": ["ansi", "cli", "color", "colors", "console", "terminal"], "devDependencies": {"esm": "3.2.25", "uvu": "0.3.3"}, "gitHead": "fa3454483899ddab550d08c18c028e6db1aab0e5", "bugs": {"url": "https://github.com/lukeed/kleur/issues"}, "homepage": "https://github.com/lukeed/kleur#readme", "_id": "kleur@4.1.5", "_nodeVersion": "18.3.0", "_npmVersion": "8.11.0", "dist": {"integrity": "sha512-o+NO+8WrRiQEE4/7nwRJhN1HWpVmJm511pBHUxPLtp0BUISzlBplORYSmTclCnJvQq2tKu/sgl3xVpkc7ZWuQQ==", "shasum": "95106101795f7050c6c650f350c683febddb1780", "tarball": "https://registry.npmjs.org/kleur/-/kleur-4.1.5.tgz", "fileCount": 9, "unpackedSize": 20250, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDPAZSN/0O920C++E5rrlzym6iGQU+IbdBC3o633vqFTAiAK0Bv3hVTtLI7OAkTMBCMp9gmLlzb744i/imOTn190rg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuKpkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrj/g//e5m5iJ46HfrSfjCQ4XfSGH/LfB4/Yy1MpT9kzRvKwlHfmYQ6\r\nv+ojjMMH0asp77IQjgnarEd4w512K6C+y4Dy4nID6VsWKRqHEAvdAhIUOor1\r\nmkHk/K5SYc26AmQIq98mPfcY7MiIdWCYB8keDgUR/ZrnnZI3uezxD1FGHKC9\r\n9QBjCyudS+85zi/UK3Qk89p5Q99qI19Opz8+D+gxijvsUS5+6HdJkLfjACJb\r\nt+2JsAN37ZKn838bA4vkFZefIXp7Aggckn3czv9ImfviFzJM4h0nXkVrXG1p\r\nwAzsafbY8S/l6ZR6n3ydLCdrVgFczEUSVeBCNnpJsgnVe9Jwado3IFoF3nCf\r\nLCmrAF11eQy/H0G7dtf7N8UCnDQVz/EjvAZpqtS33AZ1DtBR1uxk3bZf8VAL\r\nZRrSgkvwCwo/WnYg5fsHLSKjU4CiQwdKXivquVaZL+PfEk6k0dfASDT6DSJ/\r\n7Tvx4dKjgdTK3GZjoraDjpGvvarSpXtApZXKJHKOXIS09JTeTr1xhMIm4hDP\r\n0PhQfHI80W3SrlqH1KfG2nGny1hBXQ7Z4uu/LnU0XQQNlsczMWsMoW6JHSua\r\njj4Ots4C0pSAeDKbB/ylfpOIbkmMixruPaeJjT1axB02YXvXMdUcN1IShM8b\r\nAo/EC66aITpTYKz+pCor0/39ZUzR9/v6YUI=\r\n=2FRA\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/kleur_4.1.5_1656269412367_0.9792781413391329"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-07-08T19:42:17.649Z", "0.0.0": "2018-07-08T19:42:17.741Z", "modified": "2023-07-09T22:07:55.660Z", "1.0.0": "2018-07-10T16:32:45.285Z", "1.0.1": "2018-07-11T21:53:25.824Z", "1.0.2": "2018-07-17T20:26:14.397Z", "2.0.0": "2018-07-20T18:34:34.146Z", "2.0.1": "2018-07-21T05:08:20.066Z", "2.0.2": "2018-08-28T22:25:51.805Z", "3.0.0": "2018-11-15T21:28:10.562Z", "3.0.1": "2018-12-07T19:27:38.402Z", "3.0.2": "2019-02-07T21:23:45.304Z", "3.0.3": "2019-04-07T22:02:01.149Z", "4.0.0": "2020-06-17T03:06:59.425Z", "4.0.1": "2020-06-19T04:43:33.205Z", "4.0.2": "2020-06-24T23:23:39.054Z", "4.0.3": "2020-07-31T18:14:45.372Z", "4.1.0": "2020-08-13T03:02:37.572Z", "4.1.1": "2020-08-19T05:59:27.607Z", "4.1.2": "2020-09-26T18:25:38.710Z", "4.1.3": "2020-09-30T04:31:55.368Z", "4.1.4": "2021-01-22T20:16:29.328Z", "4.1.5": "2022-06-26T18:50:12.559Z"}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "description": "The fastest Node.js library for formatting terminal text with ANSI colors~!", "homepage": "https://github.com/lukeed/kleur#readme", "repository": {"type": "git", "url": "git+https://github.com/lukeed/kleur.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "bugs": {"url": "https://github.com/lukeed/kleur/issues"}, "license": "MIT", "readme": "<div align=\"center\">\n  <img src=\"shots/logo.png\" alt=\"kleur\" height=\"120\" />\n</div>\n\n<div align=\"center\">\n  <a href=\"https://npmjs.org/package/kleur\">\n    <img src=\"https://badgen.now.sh/npm/v/kleur\" alt=\"version\" />\n  </a>\n  <a href=\"https://github.com/lukeed/kleur/actions?query=workflow%3ACI\">\n    <img src=\"https://github.com/lukeed/kleur/workflows/CI/badge.svg?event=push\" alt=\"CI\" />\n  </a>\n  <a href=\"https://npmjs.org/package/kleur\">\n    <img src=\"https://badgen.now.sh/npm/dm/kleur\" alt=\"downloads\" />\n  </a>\n  <a href=\"https://packagephobia.now.sh/result?p=kleur\">\n    <img src=\"https://packagephobia.now.sh/badge?p=kleur\" alt=\"install size\" />\n  </a>\n</div>\n\n<div align=\"center\">The fastest Node.js library for formatting terminal text with ANSI colors~!</div>\n\n## Features\n\n* No dependencies\n* Super [lightweight](#load-time) & [performant](#performance)\n* Supports [nested](#nested-methods) & [chained](#chained-methods) colors\n* No `String.prototype` modifications\n* Conditional [color support](#conditional-support)\n* [Fully treeshakable](#individual-colors)\n* Familiar [API](#api)\n\n---\n\nAs of `v3.0` the Chalk-style syntax (magical getter) is no longer used.<br>Please visit [History](#history) for migration paths supporting that syntax.\n\n---\n\n\n## Install\n\n```\n$ npm install --save kleur\n```\n\n\n## Usage\n\n```js\nimport kleur from 'kleur';\n\n// basic usage\nkleur.red('red text');\n\n// chained methods\nkleur.blue().bold().underline('howdy partner');\n\n// nested methods\nkleur.bold(`${ white().bgRed('[ERROR]') } ${ kleur.red().italic('Something happened')}`);\n```\n\n### Chained Methods\n\n```js\nconst { bold, green } = require('kleur');\n\nconsole.log(bold().red('this is a bold red message'));\nconsole.log(bold().italic('this is a bold italicized message'));\nconsole.log(bold().yellow().bgRed().italic('this is a bold yellow italicized message'));\nconsole.log(green().bold().underline('this is a bold green underlined message'));\n```\n\n<img src=\"shots/1.png\" width=\"300\" />\n\n### Nested Methods\n\n```js\nconst { yellow, red, cyan } = require('kleur');\n\nconsole.log(yellow(`foo ${red().bold('red')} bar ${cyan('cyan')} baz`));\nconsole.log(yellow('foo ' + red().bold('red') + ' bar ' + cyan('cyan') + ' baz'));\n```\n\n<img src=\"shots/2.png\" width=\"300\" />\n\n\n### Conditional Support\n\nToggle color support as needed; `kleur` includes simple auto-detection which may not cover all cases.\n\n> **Note:** Both `kleur` and `kleur/colors` share the same detection logic.\n\n```js\nimport kleur from 'kleur';\n\n// manually disable\nkleur.enabled = false;\n\n// or use another library to detect support\nkleur.enabled = require('color-support').level > 0;\n\nconsole.log(kleur.red('I will only be colored red if the terminal supports colors'));\n```\n\n> **Important:** <br>Colors will be disabled automatically in non [TTY contexts](https://nodejs.org/api/process.html#process_a_note_on_process_i_o). For example, spawning another process or piping output into another process will disable colorization automatically. To force colors in your piped output, you may do so with the `FORCE_COLOR=1` environment variable:\n\n```sh\n$ node app.js #=> COLORS\n$ node app.js > log.txt  #=> NO COLORS\n$ FORCE_COLOR=1 node app.js > log.txt #=> COLORS\n$ FORCE_COLOR=0 node app.js > log.txt #=> NO COLORS\n```\n\n## API\n\nAny `kleur` method returns a `String` when invoked with input; otherwise chaining is expected.\n\n> It's up to the developer to pass the output to destinations like `console.log`, `process.stdout.write`, etc.\n\nThe methods below are grouped by type for legibility purposes only. They each can be [chained](#chained-methods) or [nested](#nested-methods) with one another.\n\n***Colors:***\n> black &mdash; red &mdash; green &mdash; yellow &mdash; blue &mdash; magenta &mdash; cyan &mdash; white &mdash; gray &mdash; grey\n\n***Backgrounds:***\n> bgBlack &mdash; bgRed &mdash; bgGreen &mdash; bgYellow &mdash; bgBlue &mdash; bgMagenta &mdash; bgCyan &mdash; bgWhite\n\n***Modifiers:***\n> reset &mdash; bold &mdash; dim &mdash; italic* &mdash; underline &mdash; inverse &mdash; hidden &mdash; strikethrough*\n\n<sup>* <em>Not widely supported</em></sup>\n\n\n## Individual Colors\n\nWhen you only need a few colors, it doesn't make sense to import _all_ of `kleur` because, as small as it is, `kleur` is not treeshakeable, and so most of its code will be doing nothing. In order to fix this, you can import from the `kleur/colors` submodule which _fully_ supports tree-shaking.\n\nThe caveat with this approach is that color functions **are not** chainable~!<br>Each function receives and colorizes its input. You may combine colors, backgrounds, and modifiers by nesting function calls within other functions.\n\n```js\n// or: import * as kleur from 'kleur/colors';\nimport { red, underline, bgWhite } from 'kleur/colors';\n\nred('red text');\n//~> kleur.red('red text');\n\nunderline(red('red underlined text'));\n//~> kleur.underline().red('red underlined text');\n\nbgWhite(underline(red('red underlined text w/ white background')));\n//~> kleur.bgWhite().underline().red('red underlined text w/ white background');\n```\n\n> **Note:** All the same [colors, backgrounds, and modifiers](#api) are available.\n\n***Conditional Support***\n\nThe `kleur/colors` submodule also allows you to toggle color support, as needed.<br>\nIt includes the same initial assumptions as `kleur`, in an attempt to have colors enabled by default.\n\nUnlike `kleur`, this setting exists as `kleur.$.enabled` instead of `kleur.enabled`:\n\n```js\nimport * as kleur from 'kleur/colors';\n// or: import { $, red } from 'kleur/colors';\n\n// manually disabled\nkleur.$.enabled = false;\n\n// or use another library to detect support\nkleur.$.enabled = require('color-support').level > 0;\n\nconsole.log(red('I will only be colored red if the terminal supports colors'));\n```\n\n\n## Benchmarks\n\n> Using Node v10.13.0\n\n### Load time\n\n```\nchalk        :: 5.303ms\nkleur        :: 0.488ms\nkleur/colors :: 0.369ms\nansi-colors  :: 1.504ms\n```\n\n### Performance\n\n```\n# All Colors\n  ansi-colors      x 177,625 ops/sec ±1.47% (92 runs sampled)\n  chalk            x 611,907 ops/sec ±0.20% (92 runs sampled)\n  kleur            x 742,509 ops/sec ±1.47% (93 runs sampled)\n  kleur/colors     x 881,742 ops/sec ±0.19% (98 runs sampled)\n\n# Stacked colors\n  ansi-colors      x  23,331 ops/sec ±1.81% (94 runs sampled)\n  chalk            x 337,178 ops/sec ±0.20% (98 runs sampled)\n  kleur            x  78,299 ops/sec ±1.01% (97 runs sampled)\n  kleur/colors     x 104,431 ops/sec ±0.22% (97 runs sampled)\n\n# Nested colors\n  ansi-colors      x  67,181 ops/sec ±1.15% (92 runs sampled)\n  chalk            x 116,361 ops/sec ±0.63% (94 runs sampled)\n  kleur            x 139,514 ops/sec ±0.76% (95 runs sampled)\n  kleur/colors     x 145,716 ops/sec ±0.97% (97 runs sampled)\n```\n\n\n## History\n\nThis project originally forked [`ansi-colors`](https://github.com/doowb/ansi-colors).\n\nBeginning with `kleur@3.0`, the Chalk-style syntax (magical getter) has been replaced with function calls per key:\n\n```js\n// Old:\nc.red.bold.underline('old');\n\n// New:\nc.red().bold().underline('new');\n```\n> <sup><em>As I work more with Rust, the newer syntax feels so much better & more natural!</em></sup>\n\nIf you prefer the old syntax, you may migrate to `ansi-colors` or newer `chalk` releases.<br>Versions below `kleur@3.0` have been officially deprecated.\n\n\n## License\n\nMIT © [Luke Edwards](https://lukeed.com)\n", "readmeFilename": "readme.md", "keywords": ["ansi", "cli", "color", "colors", "console", "terminal"], "users": {"flumpus-dev": true}}