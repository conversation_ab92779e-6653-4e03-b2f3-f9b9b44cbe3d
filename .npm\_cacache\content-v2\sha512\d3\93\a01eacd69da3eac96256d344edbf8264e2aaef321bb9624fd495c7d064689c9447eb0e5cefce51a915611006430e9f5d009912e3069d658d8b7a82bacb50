{"_id": "@babel/helper-module-imports", "_rev": "125-f2609a687aa87b24dbd0d366f5fab7d9", "name": "@babel/helper-module-imports", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "8d9f4db6e91bc9c0e09779f980ca8500148e2126", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.4.tgz", "integrity": "sha512-t/JVIP+26xVqgq4vdxyWfegtyGL/jFhgP6khyJZcc5uGaApZCaDafKTotUNrnua+brIEgoPs1itCA11IGlKx/A==", "signatures": [{"sig": "MEUCIDVcruHkG1TAEK2/KCfAY+1yvD6daqrI7qyjRvLVhIXiAiEA0vA/PLpvoP21wPcoGQrJapbELuM4xaUZWLulo64/+48=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.4"}, "devDependencies": {"@babel/core": "7.0.0-beta.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports-7.0.0-beta.4.tgz_1509388505688_0.029584058094769716", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "4c5bbf7c765a5451d796b433b67fa7b266ed0b6a", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.5.tgz", "integrity": "sha512-6dvXCvwhlswHeRYNuMvXywkJfB8AbSGvVw+yVHwzGkjzqS5Ri5v+Vu8XA7BrW85U9m4tqc7UM1xGes0QaMafVA==", "signatures": [{"sig": "MEQCIHo5j/p7ktq7rCz3FKjLLCDOeCZGw+e4As41KY2myRLiAiBJgh9wTea0gnGJlcBqaKTRzeXCdXdNj6/P2wtfnIQ82w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.5"}, "devDependencies": {"@babel/core": "7.0.0-beta.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports-7.0.0-beta.5.tgz_1509397005425_0.2761860373429954", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.31", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "75d246ab1d7528a571a5b326b5380cd3e3c103c0", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.31.tgz", "integrity": "sha512-T9msmkkMkNMY6mCjGXsVCF1ew4xuoNC0O4IPL++/Iahi/Gro7h7snDN7XQOr6iyhEptbsDBVxHE96vLAMYT/+g==", "signatures": [{"sig": "MEQCIAK6QFUjHnK5he+VktSwdrbYxZdHstgDNQqc8d9/TFoCAiAxKEfDRb1AHTmEdfzP+mOpZuADfTdOsT3djY/YmwYLVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports-7.0.0-beta.31.tgz_1509739422131_0.5947783410083503", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.32", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "8126fc024107c226879841b973677a4f4e510a03", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.32.tgz", "integrity": "sha512-9jxfqCBrwCIa0p5ZIy1sakzKKm8x8tn0C52qpPr0M0WJ/k9gpD4ilS/mTV2v0tgmw4agjYdUXQ8slq51/5oOzQ==", "signatures": [{"sig": "MEUCIFdncg0d0+/tpcGEjlT2mR9ZX8SvgOYS3SQ8vdnl98mZAiEA//KhkRth8Ev45Lf+DJ1ncR1RCPSH56BibH+yG3Ck03M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports-7.0.0-beta.32.tgz_1510493613637_0.9686924740672112", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.33", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "c0790f2f3efed787fdc07fe008ae5ab571fb4c6c", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.33.tgz", "integrity": "sha512-WJpmMkixRk8U8uTJ2sKWbA3SoR9g9Ms23B5h2oeVqPnmznCF+ldWzKvhmTWP9uMPVeVqRNVNY2HfbOxbC0C06w==", "signatures": [{"sig": "MEYCIQDgPbSbtAZky/Ea3vXZG/6WLZ5MmwkhGQgYUbilBakh5AIhANQveQwMcEscNvKnK4diLPqyu9IuEk2hQQvI8FNgORtV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports-7.0.0-beta.33.tgz_1512138520013_0.7883058008737862", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.34", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "f3c3da95a8994dfe103dbc137592d8558db500f3", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.34.tgz", "integrity": "sha512-wlVrm72pIv+Vw4GF3KdQnklzCkL8nDOEyjnTtmeYpSAfawQwXXE/tvXw7uUW2Ut8PCde+nGuqfATMaD4hIUSRw==", "signatures": [{"sig": "MEQCIH8uJ/VOeka99u6pQfnxlmYH7T0LyCWHXZbapRhTkb7kAiBlYEft247TCdhM8FmFXx/mKXZ0oq3HvwRP9vvemt/Y3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports-7.0.0-beta.34.tgz_1512225579764_0.22493622172623873", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.35", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "308e350e731752cdb4d0f058df1d704925c64e0a", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.35.tgz", "integrity": "sha512-vaC1KyIZSuyWb3Lj277fX0pxivyHwuDU4xZsofqgYAbkDxNieMg2vuhzP5AgMweMY7fCQUMTi+BgPqTLjkxXFg==", "signatures": [{"sig": "MEQCIDn0ZTmPeyGCHiy1keIJ6Lx3vdH2KAjri405r3OktOehAiBQ9gZORLsuHjhyH8rloS6UxG9lNhn46WLZqIePmnJHvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports-7.0.0-beta.35.tgz_1513288082114_0.8088115761056542", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.36", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "a35c09ec39bbee47553e9688e444d30a12e51346", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.36.tgz", "integrity": "sha512-SNNOWnWMbgD1ilJ3RfayD87lLGNvrsIQCUBYrk1q3jFh3Al218XOlY1N+sV2l+vFOo43qMJUNGRXTH5w1WjfYw==", "signatures": [{"sig": "MEUCIQDm0Bg6Mae+onFiFB84SlkBP05FOiIByK20odcXJsRfKQIgRiGD98Y9iGYuhFHvkqQDhTuWF4JDheUAlDwsOMg24S0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports-7.0.0-beta.36.tgz_1514228700375_0.0777071937918663", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.37", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "e0f34cfbe7eb0d70dc02e1481a236e26d127fb43", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.37.tgz", "integrity": "sha512-whcLj/TfNA5IwY+MRWHcXKKOkkxR8eWci+hqJqGKplUmxhCCZnUQo1p3glKFNHiXBYTNx8dEDSHzul2+ljTSyA==", "signatures": [{"sig": "MEYCIQDBiY+3unB6Cy51pU0qbIpAjlNQ3Ydl54qBXFkX28eApwIhAOA4jVroScZjmgXvqeuEXxNCNpsVFZGQpWnDPVt8zZTX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports-7.0.0-beta.37.tgz_1515427363410_0.8909982016775757", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.38", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "1b3a6dd9105cc0c7fc0d67d44a08706eb4a506a1", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.38.tgz", "integrity": "sha512-A/KUU0SC25pgI7k0K4habJqOYu9sp0UsMz3lRl26cIWhaLb5e9QRfCn1nmPnkBgCjc6IZl7pOxu4511Qfo/YxQ==", "signatures": [{"sig": "MEQCHxtkv+FEYZzpgEnCgqvZz999h2NtPshoLpKdgIRtq2YCIQCqyIAlNGLpVYTl3QPlSVMyNz/qbSL3VktmfG0fSqDFwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports-7.0.0-beta.38.tgz_1516206731875_0.987313118763268", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.39", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "39e1fd4b8f5982b05417f73250a620070541192e", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.39.tgz", "integrity": "sha512-9rX3HHqjq1ZIeZgfUm9HEfZc3A/HzjCwnRHn611h0Ou936o81e7ple/NBl0nHLSI1jcJxep8Cp0hNExhM8klJg==", "signatures": [{"sig": "MEUCIQDOr4/fPGierekMuWs2Y60RtzzJ6zQXeQQZkbOT2aRstAIgJL+ly6EONe33munu+vyysHezhKeWzMvQWlARWenONDk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports-7.0.0-beta.39.tgz_1517344064691_0.3283668376971036", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.40", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "251cbb6404599282e8f7356a5b32c9381bef5d2d", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.40.tgz", "fileCount": 6, "integrity": "sha512-QFOskAKWbqJSBbGIl/Y1igJI4mW0A+wD5NFqsgDJj85KSvj/dHM4wNGIeqCi85nN9aMa4DgTBBrzUK4zSMsN2Q==", "signatures": [{"sig": "MEQCIF0WK/y4M84MrU+0JLSRgTkBToLYAzsk7vFNctAvwZmYAiAv0PYL8POdEGJ/Bl/931E69mutgZ70u6Ad5IWE1KOc8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18456}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0-beta.40_1518453716435_0.7161505842531071", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.41", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "c639ad715483bb6ec585eb08793d3220a062bc70", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.41.tgz", "fileCount": 6, "integrity": "sha512-bLY4VUMT4N+3rk3k6QNZNvqKYcOZjU9Vd2TrLO4UJFOo56IskGQJdo6uDKdTRA0BBX5gb51M/fvAM6bWpAQFcA==", "signatures": [{"sig": "MEUCIDHzrl61m9n46xr/l2C72lI92KRJslXRNixOsVxXIUnRAiEA+zj/nd7EN4xT/tGLmr1D98mmfhI6Zqg1ochZtQ9MEOw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18055}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0-beta.41_1521044744627_0.8211225462741092", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.42", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "4de334b42fa889d560f15122f66c3bfe1f30cb77", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.42.tgz", "fileCount": 6, "integrity": "sha512-0kTX0cjuVKUKDJmHjmAb504kNrwae0Ja32hGii7zSHDKm0tVZvvpT8Cc1yYHo6UsIkUmzEvfGwIrNYemx1jTtQ==", "signatures": [{"sig": "MEQCIBzQ9Yep6KQ7svjZvy7ZuXsUMXUGNyh193YIKbXmVxQaAiAjdnaVXR/VQjBRMQ1RaTzg+qWdeFmqLDqGSA5fzHamQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18055}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0-beta.42_1521147019383_0.21153700994802604", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.43", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "fdb439d52757c2466de3c39943a2aabc06a70956", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.43.tgz", "fileCount": 6, "integrity": "sha512-UMyBjVnxysZGJyMTxAoxt9FzHdyN+fntOSYWQ4ZJOosM4w0WeYUKl6fBj93i3Njw/rMMyFi/i6B7ZdFCYo24VA==", "signatures": [{"sig": "MEUCIGzBgpgg4yvLgIe26/Fn4iupeweONyY0ELEixcgUNks4AiEAs844gD0B5rOQQ7Zx0K9wFqwMNoA4s0TUSes+cmBDPPg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17410}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0-beta.43_1522687688827_0.08744641274566733", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.44", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "60edc68cdf17e13eaca5be813c96127303085133", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.44.tgz", "fileCount": 6, "integrity": "sha512-V95wi6rCffcLM46XdaUJHRc3D/XSvfsQshedaX1riHQCbs0uVopdswXrykwB6E/QEPfUGxXfs7l5GubupOi+Cw==", "signatures": [{"sig": "MEMCHyB3egoIvf6KnbcbWT6YYkxl5dgeVBRlBXyeGiGnQMMCIHgv60gMM0QG2R/po6L74OFx2R8vtplVB5AwrmruZpOi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18769}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0-beta.44_1522707590705_0.6093864831961788", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.45", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "f1e059d9f1393c91b17137c354482da84cd8fe17", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.45.tgz", "fileCount": 6, "integrity": "sha512-HfKhd1Rb0aJKJBnb6kW8cddUMGqFoaIbhCpstHexnUlOHq3adJ6BUuag8EtDN62UCq+6QzsZf3dgt4y61INekw==", "signatures": [{"sig": "MEUCIQDKYjNYAY08w3jUGNNTGOzlVDElD+9ha3n3s3lMFN+g3wIgN21xYw2bgBZY7MHL6bg7xsGLVqoHqrpM1mtx2bmOzlU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18769, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T0pCRA9TVsSAnZWagAAZiwP/jss6hzAM+FARbYzxx0R\nWO8OALyB9ENbv5/EF4brj1HRJRSucb3gFypu2JRf4tj1wOlwg0Il8SmHQ6P/\nSMh25V4S7BrskuSA0lcwu78c5fDpkr5O+/wLFBVeF/I+CVGVghSWQcF46WB9\nz620/2JJlhQUP1dYDN0yes+NWJntNwbsuFXOXAp4GGPfGZe9xc+m95KrDD1p\nj2G43MkC0UCndBgv5C84IbAwZCRSoNeTYuKYQdamqefLUI++ic5IvRJAbNnU\nm3bCbbd0s4FUaHX0kUYRXO432PMq/ZdNnQaSewWUBNW0wSE9EJ+pgpMw/Qpn\nx2ar9R+/sct6KikGsBiix03h8M70Vwqjq4Ll96gfOTtcH78gTMwvJEUXu7f/\nddBoUoUsSsKO4LTlUAJ2Y1nM/BbbHzgoYF90F1a06NEPjbxbqXagB50ZMGeS\n5HnaW9Vxc4TP5BRMx8BF0mq9ZkCBrPvF3lx37eTfno0x1OMcF8xDNwMgxaI4\nL55IM0e+TzSb0SGYMvmWI1zeOq9H3Urj35B1fBHrxByHcvxrctGv9jMZNxhq\n1NnUFYsi7kGluVxY14PiNhLJ/fgqj1W5ueXxGfVwPNII/j9oLHKvjP/qxbLi\n1nQqLyM9bFg8mfoz+zH1hPo9nroiziSAEenAvoZhrWAxR3Sth6rVKw7/lQDz\ndT6L\r\n=tx+V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0-beta.45_1524448552839_0.6233883417338897", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.46", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "8bd2e1fcfae883d28149a350e31ce606aa24eda6", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.46.tgz", "fileCount": 6, "integrity": "sha512-xjgpwrqHiKCZgAcqsNIpZ9kOCC5Ty/VYN1H07v21HbAf/dl0/HeUA0taz3EFv6/7lRgS3qThawTSG0POJQX9vQ==", "signatures": [{"sig": "MEQCIH8dt8bVvjhC5/8r5R6yN55ACp25bW1WSD0pmHSUtcQVAiAaHjH/ZzJBYtByBkaNU9DGAtsYV0BEhvljDe2eg6e73Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18769, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WFaCRA9TVsSAnZWagAApIQQAJvn+OTtWphZX/rMRMaO\n0onjjS0GvJkoGnVh2y9F2M2Hs6hGwD75KlY327GpJZEafUO9uLUzARmhVijU\n+9qen22OnIXZ6P7HKdHe+5zlhijowy/RaLFnksGvxVovGA/c3tiJ0OVIEJz+\nzfHBOdqBgJWtJSU8WGXvaB/16fYQ70RjV4DBPnPI1I93t4i0cm2eQcCANcVz\nLUnWAO4Tq0gLEEnp0OJt9Gg1YOoF04FanJS0D1pt7fo4KDYlyf5y+OZcn9RB\nou95UuJwZKevogkf2RDSeb+ltMA/2gGAM1zMW2IjmpK4Xd6njiMBoDNLV9oU\nANyeU5Mvaan/vBd6i/b/8TTtmf8XySZ6K0r8VOtL/2K7uCiJkfjSda2GqUdb\ng/Nt3tjal1Fk6Bn4t/YRdSNaQuibICvyFFEKAbJEoghFEkuW/HpnqAigK9zs\nGV+Yu3fSLq/6rptuQBEWGb1g6AkuRIwMgJWeIRg3wVSHV0xmp3Fhzy2scYmW\nXjO+UVHwehIkQddfugRw9TOKbkGkZfNRGXnuFaQm9IhbpdTFVTEEf5ML5X0k\nU2TSKTXYCfyIr6Xw5wQ/DCREc06Oe5OXhrFPONGck5hjRM+HUya2v/RPxluV\nROeOKQYlr41J6MFD5gCS/qMRxIBE0W6zRQGrGWYMS3lFPUTiPn1ElPrW9Hw6\n9hHn\r\n=NqRq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"lodash": "^4.2.0", "@babel/types": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0-beta.46_1524457817204_0.8093815564844387", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.47", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "5af072029ffcfbece6ffbaf5d9984c75580f3f04", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.47.tgz", "fileCount": 6, "integrity": "sha512-Rk43Ty+a6npu9znK22IqFlseStRGWxEHi2cjmLbbi63VGiseofdUtIJI65F9MTCuMTXAX7VbY/ghef1Jp5qpvw==", "signatures": [{"sig": "MEYCIQDaM/VR73pmQCtX8mXVbqotdtZEADLZWwZrFR7cA7fuxgIhAJKdjC7LT81h0RT28T8sPYu7/XiW8ivbqwF2PIkqeujQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17732, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iTPCRA9TVsSAnZWagAAHVMP/1gJzXAAqqH8ShwgT84o\n5sWvV2glb+Xb6dBHu559NatX8uxV+ZdM5cbRlq12rEJZHrCwWbCdg05el/2x\nIv7JEn/CMZGprgu9DemxzzT8VpNwZ51RHG/OuNJ+Eqqbp4Q8q6fd9EQaRsI9\nEk3sw0t+5jDfk7L8fOSa5wFocXwyCnxfCxkeM8JbmKDgSYCfiA35K59IaLIn\nFA0sGPLCmCkdSce8e3PjoL1HofRaFd5bj5hJUOJO/an3tADCurkckWdx/PhJ\nti5MppyF73cKNTDOsBehrTg60mNIUHVad8F9BY4pFtoDMjRyB9E3zRnuhIfG\nOzjxC8DmSbDRXQjragbMgg7Unn0cd1tHAEUAaTANMsY34eCAVjXKS60DAhT7\niU/KHBNj8A1iQZrd/ymAnH/Cw9VeT7mJWBqAJ7USqSMsNAElriDkAAS30i+0\n8yrZZr+0KLQJKVnff78MiqgwtseCdcldWqAfKwpz8g+sn4Aus9wkWpX2Lebf\nP5heTPWvYjnu8k1D8br1E1byGy3PJQBJfz5mDCwjc+uflrHApJmvhj2JGtX1\n2o0LauFMYWvRfIcSreHzKDlbDTHKD6AgTrCqz+9CoKpWTu8lK4v8QKxs8yvn\n556nDJ3hz1FUOzdTtUmeUprC0oAHizAADpI0jOnCx+52x//GOwPpi3dhylrI\n9UAu\r\n=QyfU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0-beta.47_1526342863224_0.5545050451667972", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.48", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "04016e1aa5c600eee42677eb649cc6f0497e8151", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.48.tgz", "fileCount": 6, "integrity": "sha512-uuxpRHHTP7+xVAtKro9z09P4bSkBcA4B+JoMc6FsT61GGnE3iRIbqb2QxQm6dFDMshlZc51/EfOainbimZRqCw==", "signatures": [{"sig": "MEUCIQCQJvYQk3d8ZklxLN9iITL7VpwoUgBwPxO7B+V3hRS74wIgIMlMtrPWfKIzB/S/KUroH533NusWPVEmJwLHKTuR9es=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17411, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxCfCRA9TVsSAnZWagAAtgIQAI00lT0iQR2a6IdgpFZ/\nLYK86I1a8k7NOoiIjY4U9T1D9sZLJNpWTV1pQmFTsm/8v3N4/iSf+H6Qm540\ngRIsKeRTRakyZ2TIf1kxF9734caQTQXcXGCkAVErbH41xZM6iVqQyb67RyLG\nYKU0A5xl71cvUwcXIrx1TTjMnPVw30+2Gy4jD1t5vuKMktiqlEz4NFIKRPDI\nhnPrg2B6Q0s2jinibMIP3cf9ptiPxwCpQ1gDVHicV828fK4PUP7T0FeJgA/T\nIDlKmrD238E597PjwQU6LwF7g/NJBWMiiRJ65vivZq1ML+U3AsLNpxev+vTJ\n4Jp6bVxPLOuYFhMv/1T5r44So0GednHQ+/NzIpoWlQ//p2v/bOjeE9690DnP\n+JH5t0XnIjmMxRUP6C99tpk4IdJArxXv9ECRUqY2MPYSEd24VpSuGgdeUJD/\nOjJDkWDcaS1TEBE8rQDeKdJL1WWnEPa3kR5ElkU4eaturFf1X5y4/cPbJ6CL\nQBzTS43x5em9YYwXQG7b5S2rPBRwP9caPUnc/C2GTCcmCq7Wnk1Tvlc69Nzp\nZbfbTNx4yLxQ+iTrWQGdrxNGxaF406amEHmty9wnOwz2rd42ImEeTWQvyRgu\nrk0cVOb7vdWCs43gg0ZRERDeOh8NqsiA0PdJ/lD4F/WTsmaXhSmio9GFt+8P\nNeKv\r\n=AWF2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0-beta.48_1527189662781_0.7118729005426319", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.49", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "41d7d59891016c493432a46f7464446552890c75", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.49.tgz", "fileCount": 7, "integrity": "sha512-TXWVVi5B47f2rM8T/rDCNd0PXCjojFGtcP72/pN01PnkhP+6lQ0Gsy1FhpxVROVyiUqVbGu2wqzvr/V3FIKpqw==", "signatures": [{"sig": "MEUCIHUluQgwk/P05iuEr0tqt/rWA6s4QGIv5hcQm6fUR79dAiEA1SWc4Gg4LH+mppVkHvh9TUEkT2Y0pXGTe5MLryn8TsE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17426, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDMvCRA9TVsSAnZWagAAfMUQAJaod+RJPbNlBiEiPhjg\noFJiqL8qr8P7ygyg6QidR2dP6ZPcwfgFbdZVSrtmcQduZZfx94elNPxtvAJ5\nrb/zCXusrV1CgK/J9WeZ2wOQphORdZXeUoVrTpqzKICo/cG0ixB4d+hYMEOZ\n+ATznZgbf9pL4a/NqGjNQkJBrjMpBs0Tg2FfTGd6j6GeWO2s0iNqYGjLpqeP\n48RM0wRZTAHQrlXNqYpXEphShIsyvshknix2f3Cr8iqYks6IncM04024zmkr\n/4616Qsz0W2Y5pWANwlM8Pac2JKLLltNaBmXbH4bRXMqom2UKZpurk18BF4Y\nWCzqIK2sZ4dPkvyrPNU+kylUWo03oHkw3Ec3dvUd4f61Tr7vhJ1UjpQlX7OO\n4XDI9/mVVXxgmW8UPGeiTAa5RBn5zL6cUA11pnkb8EB55x+VCq1sK0Zh1hhr\nb8SjpF3YlFY5KUMuR5t/6TSzI3AH6kn6acmNCkyCwy0X+t2fnaGWrqsFENv+\nx3tKZaPmZICTHnT5nJ0WLPmfizjZAhjQTsfRwvejToDh0FSOWs8kX+ziGrmH\nUIlBECfpWah7YWj2SieyWYERQZE6lgZSG9Pe9xjMJEq7zMm/bDpO6uTDa4xY\nAF8gSrChdTDdNOrexPqMTEs9PMKuzavZOIPuBjat2K9fZEksJ6IImdvM6OS0\nr7LI\r\n=zb7g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "41d7d59891016c493432a46f7464446552890c75", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "3.10.10", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0-beta.49_1527264046815_0.3839645940574028", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.50", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "c8a2252fc9b03ef27e5659116c1ec628793c7ea9", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.50.tgz", "fileCount": 8, "integrity": "sha512-9LGbyo+wdO7Hc99ygrPxtLFCeOfLocVSGnJkY6UyCsXcp8By+TH3WjDl2gqWwqXlNnh/mo5U34h9VZ66uQeY/w==", "signatures": [{"sig": "MEUCIA1uDeIvNBc2lX6dC6CPlmzBKX4Y6g0BgIDWBeaebepBAiEA9yNnEBcIBT2GjVopMAJkRAs+c8OkWanX4Q2khaVZlF8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16237}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0-beta.50_1528832813803_0.4025142473717416", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.51", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "ce00428045fbb7d5ebc0ea7bf835789f15366ab2", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.51.tgz", "fileCount": 8, "integrity": "sha512-9wcJUiA45vjxOv0EhU/hYzGJkTu4IhBxNZI/dPr7ELTSJbVzxDz8WMCK3UM3GlqiU0t3PSxMRM6clItnuaUm3A==", "signatures": [{"sig": "MEUCIDioqOi9z/4Ou+z1xA0m06GYLjHtz2AxtUr27slc5kovAiEAi9VSBVNCkyG0TXmFt5oE3QTBOQBLPelzaTOg8UM7MmA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16237}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0-beta.51_1528838358784_0.9381228957405678", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.52", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "70840e83ae891f94702c6c613787c48ee3c965bb", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.52.tgz", "fileCount": 8, "integrity": "sha512-L/uMFHezpF+7mxzZRW5sCDeBCnmXYLoiRaLKcNKxAflptcx6P6kT+3hb8/WcKVMK3awsZhotu6NA170D8fhbAA==", "signatures": [{"sig": "MEQCIA2lDzqfkVX/7ZLb9S+6oY2UE2LaIoE0es9620qMwUZnAiAM2mfb1hq2pod3mqT7aLZqBXOiWa6576NBT6MoKH7gjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16236}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0-beta.52_1530838753520_0.6723865948965972", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.53", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "e735e6aa30a504b0f9d85c38a6d470a9f4aa81d9", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.53.tgz", "fileCount": 8, "integrity": "sha512-nyyERQH7kRCy0OR2Ek0+sD+wxZEhCmaLAVE7SylPYmCce1Dq8XGmibT1eQVekRkr78utXnDKMe4A269SBVlIRA==", "signatures": [{"sig": "MEUCIARYvK14TC9h1VCR9hWku4fbOko7+FlQkUjzXGe76DjUAiEAhs6HrypgIxtwitXjmip9waSQSukYSTPIPGoKYiU/7bM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16236}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0-beta.53_1531316403077_0.4822326282099436", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.54", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "c2d8e14ff034225bf431356db77ef467b8d35aac", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.54.tgz", "fileCount": 8, "integrity": "sha512-MiCMre2Mfk2jgF1YK+qNQiEtetsQtDYUHAuTEjZALNCab3/6qgMUcZ6njhSb1xVExpfWE9S8lkYtAL/CM1ZvpQ==", "signatures": [{"sig": "MEUCIDH6JU9VFW90qwvlEQ1lOzya32JJQmf6J2KUpGc5SkTtAiEApe0e0tF5k2KyX43GlqqQGJmaWxgM6w6hz+TvZRH8KVw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16236}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"lodash": "^4.17.5", "@babel/types": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0-beta.54_1531763993150_0.8258119403472781", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.55", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "93f927c6631d0689b8bbd1991d3fb2aa63eeb3f2", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.55.tgz", "fileCount": 8, "integrity": "sha512-oCu9P/F4D5YqPgtNuPmBHGsWpgM/rJau2Cbhh4BNhO/X2nIUGA8TDqqwSPX/F3BtcKje8fcwCe0mn2rQblKapQ==", "signatures": [{"sig": "MEYCIQDp/nQNBjcxIUtpww+XoDqBdpUHXj0jPVJcq3njz5yr+AIhAIC/P/DMGTrtzOOxpj3cHteD76l/FwGiMuMXs6FSHMi6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16237}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"lodash": "^4.17.10", "@babel/types": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0-beta.55_1532815617374_0.5645399753663842", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/helper-module-imports", "version": "7.0.0-beta.56", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "edf9494ef1f36674bb19cec9ea142b70f186406d", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-beta.56.tgz", "fileCount": 8, "integrity": "sha512-iVWFscU+yIu6DIo5IWkMgVXd74/d3z/ZomwF/QJNGFwFP/lNA282rpjsky56fSxS7oT7wAlXoYoHVCOOaL7tbg==", "signatures": [{"sig": "MEYCIQDCTikvyZUnu3QkN8ejyrnmX+Gr2aRtcInyT08O4grTqgIhAOiEsWvcuFeiF9QpQ6fCtRdDHsk4f8K/y6IOi3+6jJr6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16237, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPt1CRA9TVsSAnZWagAA/soP/0hVEu9gacn6jV5huk7i\ntmxcgJZbb8+H8w6KiixRzNtXb5pdXyITP91z1Jou2fXoNnPzBGo5Kb2MNfdF\nD1Ye0NnDtJSQbcjatPoDWcCZYKROlt4NMvAMdrRC/X48lS6omU1vo8el1MGD\nOCKGPp+ZVdeGCRazaSrEG4/ZPpdaBm+sB4SHGxp9/zT2DsJ+9OrKXE/Uh8gh\nLMahsBN1ZjscWQVC6AII13CUb74WlHho+Hi1qfD5jDizqN93YzitSq8pt0ml\nGVtWh2BLgIVEevWQUFimHkD2T5wXRH7Tr3s6xC2pTSFxE+Xpg+FG4ec6lACA\nUKAlW/CV85zjLckQJi4aRklxT5G+JCdT7GqNRynuYq68Yp2n8OCfTLTrRKBF\nbwtwJJAvxC029wjxH+JNx4lba9ZRQMqgCIkO13KOGZLf2dPRbO5n2yrarWXr\nSLUIMkAzhprQGK8yrqpLjT9fbBvGwMXMVfkKbcIJjeO9ZJ8fONKa107cJE3w\nQ3PuctaDJa0lzaY1eiZPuZq0/LVjYDM1ws7PK+YGaPacX1ufa54sr+xGFxDi\nP6hCNztAX5EFNX0FrdrrOn5J8WHt1Dx9erppo2+CkBXuGrx9LEnoKph58HTk\nkBQKfMDMzaERnifDz2Y4rFXZ+1hih3ty4SfGkzaQMco8K9YC8yECPpKo8y4X\n80H1\r\n=jBOd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"lodash": "^4.17.10", "@babel/types": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0-beta.56_1533344629418_0.4333563658015027", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/helper-module-imports", "version": "7.0.0-rc.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "83e462000e1b0714ddb892959b70e4fbbfd5c647", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-rc.0.tgz", "fileCount": 8, "integrity": "sha512-CLY3oqpoDghhc/Zd7vrJylbOmXKhG69/1vlgi1Adzi1KQUqF+15GKCMo156QOfCIdZYXWMe2i/xG8bUfBP9Org==", "signatures": [{"sig": "MEUCIGtyyHZU5bbpyy6urfVUQ50HOEz8QMfoj6A+ecFQg8bXAiEAowmTvFtllV82Eo/7BKlFKJJFoAlzYC6JpZ5xRE6COwo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGRHCRA9TVsSAnZWagAACIQP/j3fWNJadvEAV6P/S8t8\nMTxyg2hjqkU6GGyjX3iZG6ItvLLQUJ09zfuJQRn7CH98x51zcEPsEs/Fuysg\nLT8kSwShI3p5920LTkDAIIFTU2U4EBKrW0m0Dr3l63uhCUKbEq9Vfh94DS2n\nWZPFbpmUzhZ0WBLF700dWoXSh2RuR29qiteQtPo784G5bNq+8Pr1nSy6BsWf\n4GFvgIryaWFHgRdrdAuh7QCnxVPSo53GgOH0cDD6mN4OcAdfL7Rl4PQ7Srer\nVfqVTlBjVA5JKG/hmEUmo3L2iSbAbISQnJQkyFAoxpSPcVRyXx4LZkZcYBtH\n7uhZAvFRPFmxtAyEsMqvMt6yaDjP7kADDsoU0OFOhQrEdwvCF2vOiCJtgbdK\nYsihQQp6/47M37IFlGMOxVgGKeFPD41Tu4iH403LFbfFH2ensmbb+uwkcS3f\n+oEejnSLXNLHv0Ko+dJ9UFn+tE3JWQe1LsDVBdUrd6XnMdlKa2Kw9bXrsyDh\n1tkIqiKdhA0N5lfqagL5Y7ywzNOtY4y5zJzMqypzvzzWfbo/b1vfjUFIqPtU\nHjAuGwwwkzJzNDUjujrXKaV1cCD/wLRKnlPDTilWXVZ8V8eeYDxBOkM7Xouv\niskB+qZvI2P+brhk9/QQ9MMRo4MnrnMqLbK1XbtTwsU6wHljnybUwcq/kiK0\ntSai\r\n=J1Li\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"lodash": "^4.17.10", "@babel/types": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0-rc.0_1533830215461_0.797422331809011", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/helper-module-imports", "version": "7.0.0-rc.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "c6269fa9dc451152895f185f0339d45f32c52e75", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-eA8RzanjsZw4X2Cqh3WgVG7zwf1wdSUfXvZOH8Azx1rpwE0hzJ276jDZ3gSOJShsxPVvopHa4h+c2WfEUjW4+Q==", "signatures": [{"sig": "MEYCIQDtw6btOTp5zJTjAeI3fxj6FHn1mQ3WEwdR1vdZz2A6fQIhAKqCeA654uyvdFt613zSTXnQcZhNAUpTtvmxNZl/guSc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ7ZCRA9TVsSAnZWagAA61YQAJWJ+yCFK+fghbGzxgs4\nXpcRQANcpNRwYe8cKNZvtVyEr23tqCzn7m4qnQT0HQo6smUr8CDtU/X/f7sO\nUYngxUoGx/d2jZDmVpn7ZXAjtj0Nf5D+5KnvpN59BZHBW9mHPuOo+hIUW9SH\nblgen6Q5KdA9kTM8pSL3rQicoT7A0LRz43sB6NZLrY7meCEZJ1M+VE8+DT1c\nmyqjpcPbLO0DutsnsTr67v9Io6VmMp0y8s5q4FzOftDteujWVQKiVMVMwgXe\n02HVTLzIBWtaPhgVtz8SmaVsQTbW91lE5npslhWLtd3dBH2D108E/d316d9+\nHBxTXuosPK9KNUhT9r5qdUgaz/GAuEsFQhec1m/DrMu3qaiZ6Dr31VwjF7zn\nlXxrsOpTnMXjBEE29h6nygQpnSAAd6t/+QXY+ZoxknTNbK9hPIhwvjw5ZVun\neAjJ/TyqGIFGJVWVnbr/+/E9yUKgmBj00GnX4d/BxgZWQCITyy4VBvCcazhK\nSaN8GE0gI7BUY0WyRmcHDRTL8cTZNBODh0Kb+KK7uETxNkqFqcEKg6Z5oKIF\n1GvIBCQ+1yRkZY2xzhUcx80p7ftCUq5k3pGLa+SXIXkjGOX3ZWhBqt+pLuZC\nxwsJtpErva/G2/XdnzgYuTS5fCdsC7nsY3WtgCtk4c6yXpThoGtyjvUv+aAh\n/fOS\r\n=6BSj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"lodash": "^4.17.10", "@babel/types": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0-rc.1_1533845208863_0.18956137633572778", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/helper-module-imports", "version": "7.0.0-rc.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "982f30e71431d3ea7e00b1b48da774c60470a21d", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-4HmUjJ7dwnhhTNOoxOxHe9e24nnzd9VjXNXLcKdw98aRxiFQhBxhOk2t8kX2XMcGVJrFHob5zfVEjgMvnkCmHw==", "signatures": [{"sig": "MEYCIQCK8ZcrcwK3sDZ+XsFKIiywTTs9T9w7HhOFFIWUgm40TQIhAIs0jkxS1pp0On7NA3CPeqm2AHPtjIYd/QJzObfesJTH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGaLCRA9TVsSAnZWagAA4vYP+gO4j5+LzJHs9Dzadnwi\n3Z+vOMWub5EqLypexbHzO5idTl20xlONId3y1EWRZWm4xsU9Os8CNSDtcBXo\negkRLKiUy8fpbYV+005d9Ya9bCFr2q4CWOJbh9Jo6OrIaFxWmUvqpP8+TFX7\nU1EQxDfk11dmKP662fi/xR6NPv5zpQtaXI0mx1eCDaG6qZJrUdW5yMBSNNtO\n0uuUwPfsvUWiRzhUvJLeKBm4+Eo9aC56QYQUgwNsMeqD38udEHBTT8MSZggd\nXHT6rK+AQsgjmqiiEuKHVp7EWiggiyYvoJPj9X6yk52zj+qwpk6nxJLwbWgW\n7XnK+YN4kXvRT50dIDbeLhgtpadWKHC8DFLpnq+7JFx2ToJzNvJXigezvHad\n4pMcXg7HpBOk79gCHoLFGBeuwZkzYn0OfxQy7EDoyVOciUo2usWxJL6FwCFD\nT78PswQqNar34LlNVXrnC81F/NwnSmrZ81bOLDbqWqfr8xan999s08Ar57tJ\n/ONhNH4AUT3uhBYTWBtO9QkNrfj3IZozq6O9jvQnnh7oIWnwiLAy8RFNC7FJ\nOfRAlA4veQ3IGfvbjactPehSimJHmiT/n7WVn/BICujTQ3ssKp+6iQKnrVXf\nCUCTm8YKmGnkBVzevesVcEywjap8Ylxw/7IICi4fFhQUfEVaJtHA1GluTMDh\ndRMb\r\n=1vIs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0-rc.2_1534879370793_0.3151241364352799", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/helper-module-imports", "version": "7.0.0-rc.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "09207a1d2c528abddd74c259af1836bf34194ff5", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-rc.3.tgz", "fileCount": 9, "integrity": "sha512-kJ2hlhdDSOEkiXSM53v+/ytdY1vnuxO5jNNReQ8PcTyjmPyhfMi93TKjpGldDgKJgJuJhDkAhXJ27IZCItMR3Q==", "signatures": [{"sig": "MEUCIGfiJ5YvarPzEaUzVqx+6OsBlQhKsuxoJ0a7wlknZOLFAiEApxsOUtpjEZLNk59kbw3SaOGT8qzHz/Bfm+lPTxJtCeQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17288, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEk2CRA9TVsSAnZWagAABVwQAJJuBXiBpdIiYHo3ksrY\n9dQNpH2z1KvH0rOaHYM+MShMSbiVsZJSuubBpVsi7Dqfd2Sk3twM34XvCxHY\nLHphMllNfZI3iPRNuti/RwQAiCebnR1VwCK7gq8Rgbc7qyI+cNgT3Jj77b+G\nYxaFGnbHvs/xxWUKCEXNV3QvTX6Ywi2lqUYMOw41cjzcUVKULgnIECrlnzqf\njUPC11sbkvYhNV4B/jQWGhKP3RBDfeAzkkZSK8IoMV6CPy740sUuKd1uIO49\nXMFiCf9hyjeITKpa/t5MZ0jMfadqb5kXwUUNGM2CFdUrEljnsGR8VFdOKzdr\nMdMHcVm3+LdJSkmDVxlcBoBmQEJr3+gu23rv0snEW3t4EWFb74jkWpsPszSt\np+fxKjDfyatrbTkmRG6GXKfcZkP1g5qmv1rRuNupUxdq2ObbM6hwXUeLKTmv\nJyg3t0WmwZHV+0IQ+/8RknBfM0SMm+oshWVWdichnGl61Fus9NZ1Wx1gi/cZ\nMlDoE3rsqym/5/TGshNeDyqg6kHEHVqf/KU5LMw37uobvh1DEL5qBZOse/cW\nX7shFJqUrx+Znlk2cuYk/1JxsGn+/RzvjgqknxB4NiM+F+Sl3A2ba1il1Q+2\naRlSIqbNUh11CW/90eyUaHOVPovGz5KzPnwVFY+vc+rxyJDgvpWCmhiGY1HJ\nYFGD\r\n=nWCt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0-rc.3_1535134005949_0.3605019780829566", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/helper-module-imports", "version": "7.0.0-rc.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "b524ed4e814c4b79e93ef538d71d2e562254852e", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0-rc.4.tgz", "fileCount": 9, "integrity": "sha512-MeEqAEdJJOwME/KLrXiYkCk6AOCBJJMl47/zgKqjuz/M2fYM6eveXD/50xLCMYWdXFT7U0WKTj9JNUhDg3nm1Q==", "signatures": [{"sig": "MEUCIEta+2p5v94qHU0dWbw1jTSWNrM2kfeVBZRr+WgI1W+eAiEA4C4YwTG1BxEAIxcpnXXclSDeeNFrVJn0UnjHicfwmdc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17290, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCoRCRA9TVsSAnZWagAAO2kP+gOQM7+96wN1LbtcQVxO\nJQJfbB3jg9mGv+Om/Jn+sDjHcEC55nWVKkq3jDfqimnREBTleC3iXby/huUe\nrtDHCw2RsRGIbLkyYi8PMmgx3gPuYek04w2JRGieus2V7Npzm7CBL5Dj6rpu\nKdcCou8S90eQHER4gomgCddXS2H0Uc6vT8FcVXmhUwTWognJon+2ggdRokNi\nguvs+MdS7kUfLIIwFMI84KrsT+ku2cdVE8roLepBoHwpxmzmIw5uQ0nKtnSI\nlTMI4OnLqBX7hd6ybjX79zASdr3nLIcFqxIO3P15PEsw+Koj9+bqU3o9n07b\ndC8gwf6OSVkMWCityxq0u/IpMiLau8Xqt52NRWZHZpyKHSMtzjaEP+f4d5Sb\nhAT4TTzjGQTbW9mCuYVGoFosowwmxjKH6DTJM3V7vFo6G/bTFgu9g938sE2e\nYJkSR5G8ESXJMcn20IoOE3nla+twMH0dAcUwjul0iAlhRZzuxH5UxkA2LHS2\nXnAQs9479Tyl+IrEwLef7P7mgh9ge4GC9lyStVM5yBomQ7qmllwzgE/BqGXH\nA3fel1K89VOHi+oNrsyg1/YHZbHa29Zll020nkEtLsYFss5hI6UT2kR8XX0n\n3MVitfRefKdrqGTFyixeAHNJGqs3DAZ0hmyQoeg1HS4FaJHLpDT9i/qQOgir\nPKdH\r\n=l/gl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0-rc.4_1535388176860_0.07225190593865105", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/helper-module-imports", "version": "7.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "96081b7111e486da4d2cd971ad1a4fe216cc2e3d", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.0.0.tgz", "fileCount": 9, "integrity": "sha512-aP/hlLq01DWNEiDg4Jn23i+CXxW/owM4WpDLFUbpjxe4NS3BhLVZQ5i7E0ZrxuQ/vwekIeciyamgB1UIYxxM6A==", "signatures": [{"sig": "MEYCIQCUhZA5oSTYIuzRDgeSt8uCrpsirhKM5Rwfu8FVbp1C3AIhAKoOlhR5iSZNNgBaUJEEU+BoefLOg+l6pVfFBlSZxTgo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17275, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHAyCRA9TVsSAnZWagAA0v8P/1rotv7XPoIHhyfNgiWQ\n1CSaqGKCV3uiNxqQ/w2UCUr69A+s0mKSgo9Woqw3jmprm7WpOdD5JkmHvNAf\n3EZo0UefA/AR0QbfjzCBHW+++2mjmsh//kKTu+0OVmFivkacYfGiR6kJEH0Z\nsq9+x18Fdn+BHw/z7p9sSl3GnFjLl248gpSZjJ6jLzsGZoNqYIk3GdyiIgWk\nwz2WIcnSoYvbQoprh29pKYB6TuMsm7ZaATS89RFb+q+dMrvlOFV0QNbsI0h1\nY2lsvAry4t+/+IjHPsYKN49ucDJu5etKmSmxbGuQicZX3Nzu9SiMkamPHway\n8ZqlOnU3baoJzf1SsdK2mTsELLTf6aMw8jf0z8F4qJAo2keQyfLrzvJAIowC\nOrE+j3DZLrT1Tx5I4xhefeizdeXYOqFJQJ/cyM/zX+8lRqLVPGpTRTLBG1Dv\nO7FU7FDmTJR5pl/PaBiX+lZvRVm+zy1fj9JxVZJPeHV2HjxIqRlemTS1vheh\neCYJiO+lkCdt/9epniIkzFtIfbeho9NX/OAyuA51wLmds+jLC1uVl8r2YUV0\nOInp3laJA53fk4z5SQafbgGjql7u0dC6pAUiLcnL9VNcamS/CixgBlSmrvPq\nDjQ/l99R1l/i1lN+3dsXkEPiyfi6iC7QnujOxEBNezqvsPHvAB7WFEwYf+JI\nH52Y\r\n=t8Qv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/types": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.0.0_1535406129403_0.08727470574335872", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/helper-module-imports", "version": "7.7.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "99c095889466e5f7b6d66d98dffc58baaf42654d", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.7.0.tgz", "fileCount": 7, "integrity": "sha512-Dv3hLKIC1jyfTkClvyEkYP2OlkzNvWs5+Q8WgPbxM5LMeorons7iPP91JM+DU7tRbhqA1ZeooPaMFvQrn23RHw==", "signatures": [{"sig": "MEUCIQC28Qm6WPK+9xhpFqJ/GqcKKIHufy8jCuB/ZjGWysb0pgIgLII9wmZH6Hk5jmQ4wUlLw9oGxYGTTMb/jIURU5p1WEA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVSpCRA9TVsSAnZWagAAjsgP/0f5TjmeTMWxg697YqCa\njmWnO7NHPS8nvm/Cqeii5Sjmry2HWikEFyFIMOcCc9Q50NNaR/rDXE+94pWR\n12vnPL3zYN1dFGNClwngYO2TLpjb55PZpigWy2AhM0vpGg5pEI/y+OHnut0u\nSF6CumSMTKXPuJASu5DwpSYNAxll71ztlFuNP+ZRSWJ2ik4QitFAShpVwvFT\nZao7AUF6jU5B999sRQ1Lh6MtfQlj1eo0ngTLUn7CNHqnx/r2v/vv3fjITT3f\nre7nks+PjpEONXkTyTPQhYDsBEAUUslW4k5PlkzODXe2j8IHuZUO+k8C4kM6\nZVYzVxAiYvneAR7wVO38axvT46qpa3MqPDUsay+CZzcAfjIpb7MVpocw5Su4\nSEW9s0UedbOPEvectBwBF70FIg/7M/RDjWP92fDBWYzSR4IG670c+4Tot8yC\n6L/wqqopZUHI2S2BjV2GS2jN+5ORQyKrHWbXNrAyQIMwtF1FtwqoTGdW7+6j\ngOqU/4OMGeBLhPrhXrka8bSeodOEOznvdHiX2oKI9gyFzb+mg815pu/QVhat\nUdOeGJ9JvEj1p9gV92BPkMuyFUgzjbmLkpfK+8G0UK7hwVLQTDxpVKjjDFaz\nyx/VZ8SDZGQWVoWxkVfKyPwElJCwvwiKqWGtC2Qcc+XJil4D6BdarZY1IjFc\numGo\r\n=6yvk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"@babel/types": "^7.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.7.0_1572951209288_0.4833687018388355", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/helper-module-imports", "version": "7.7.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "e5a92529f8888bf319a6376abfbd1cebc491ad91", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.7.4.tgz", "fileCount": 7, "integrity": "sha512-dGcrX6K9l8258WFjyDLJwuVKxR4XZfU0/vTUgOQYWEnRD8mgr+p4d6fCUMq/ys0h4CCt/S5JhbvtyErjWouAUQ==", "signatures": [{"sig": "MEQCIGHaHW7wULcKneNRo2HejwZwsIW2qasH1oX8gtg/Y5WCAiAUYRudWPzzGwlBcqQpUvnwpX11UlCSrOMSRTVCgeQYGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17877, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HBDCRA9TVsSAnZWagAAcaAP/2ySC5EaJugGndJ0o9Nh\nyL4wl5Xm9+qswmZwHgPqwIYRz2kbq6jzWXJIfl0m3Y0VcELoZ8zsM2RcR++c\n+WX0363g3bQokuR85iyP8bKQSZTBHqDUGaD2Y3PGFd39vYe9cYObvy396U09\nRYsSMNU512cfAVsk4kCd7A/N6mYZu5LNqX7NYW7Peg/+7o0Q8Hqwj82LeBzo\nHn2kHo1DEnJeMXAolk7Xc+TzaQEJ7NpMiYc1R53oSkmDbXuOE4D2mcRstw+9\nCc0EB/swyWXZBf1N55Iiif2c1Z7hlwW7tvQ1i4gqKipUMYJCGD2Mo5249QMJ\n+oZ7dg8AyAP4+J6G/dddmb/Tu1/jkVIjVilEqMZ5TsvE26xQPph9gLGvbxjY\n2aukqwLUFiGZjsziIG9kSbfUkWuYQKC20e0csm6TcXfyWbf/s/GNoU1lnZaK\nhOzWCR0RhyJAxwRkCg/0f5LfzQUOf4ELEaDZZqFYKaMTYwjmHW43WwOBMyTG\nZNU6AHE80pxqb5nA63/JGYLPzy3OhcTKCk8vZrKFXy58ytF8+9R1a+EaCFCW\n5i/YYUu3Q5ZCQakVjtTdlboIwOH3iGmWBeHQI90K3RPPSRjgZGgxinBro385\nQDRLIW5NCwaZHaNfSW24pTVVaITx3U4Nf40+MNBdA3eOoxFi7II+rPBbHKAG\n6zWE\r\n=JF+O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/types": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.7.4_1574465602820_0.2748318738649771", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/helper-module-imports", "version": "7.8.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "076edda55d8cd39c099981b785ce53f4303b967e", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.8.0.tgz", "fileCount": 7, "integrity": "sha512-ylY9J6ZxEcjmJaJ4P6aVs/fZdrZVctCGnxxfYXwCnSMapqd544zT8lWK2qI/vBPjE5gS0o2jILnH+AkpsPauEQ==", "signatures": [{"sig": "MEUCIQC/40J+nm/VW45ilsrSgaXNkSjXyN4TdsWcQruRi+/02gIgLzUea0tEPXFlnph5/+Khj53s6ProaG6to9+98FOuWZ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVvCRA9TVsSAnZWagAAWPoP/RgvcbOKopxKU5+M/W6o\nWvQOASk5vxgPo+ZZJMxrrhuMi/tN7GVuXEzgoD8HjPr4m646PuA4UgOmFVyz\nWPNc4uzDsE5VWKdBCAXPAdml71d2ZDtLrFcX84QhuvCAwNlOk2UwGg3ZxgoQ\nH027WgAIHNWg+0gUH4Pbt3wS4Jki4SeBhfukc1J8bZQAHott7gfqm18Xa7os\n/MvuNxZO9wMAcEh7gL5OcrJayYJlFQl0w6rd1BRBD/xcHS2lGO8rrHYukbd6\nEppVc6v0aDbRiGkayJNZmwDstKFYUVo9h3hqy2QvUMKtBAJ/1ACjRY++kyb8\nIdjfxbRkV2zS5i0sCA4a368tUGDZB9WZUWvfIxFvCeeLn161QThssDJjeUYy\nwqaLsi7rnv7mObAw69PWyEEzYUBoXh9mf8GERpE2mqiOqOaLDRupLnS4Clpq\npRP0OY3RFJ52EvRp1wgsk5AqwtjkXUx9IyFC9ocTmx9zwmO8M20ObWHUiWMI\nNXMB674e/KDW/xFpkjAN+nwGsgejEbFju7kBV7EWeIph7LZ1Ork0xyD8pY+T\n2OA1d0KduliYO20wTPdUW95Gz7HdGWp7Ick3XL/wilTVbzuKiMS5cCgalRlq\n5qTfL+ZF89Fdwo91hVbdulNvX1DFUsziCk0ATJbV6THn8WWyWE3QuVtrLGYr\n4z1f\r\n=ZZAl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/types": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.8.0_1578788207315_0.9842982382826129", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/helper-module-imports", "version": "7.8.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "7fe39589b39c016331b6b8c3f441e8f0b1419498", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.8.3.tgz", "fileCount": 7, "integrity": "sha512-R0Bx3jippsbAEtzkpZ/6FIiuzOURPcMjHp+Z6xPe6DtApDJx+w7UYyOLanZqO8+wKR9G10s/FmHXvxaMd9s6Kg==", "signatures": [{"sig": "MEYCIQC7r2DHkz75Hkp16wHK94HQNy8py9quelTIq580rgl1gQIhAIwgJIpVioYVcDiox/J1pSSM2HonOFbzGHcsVVoT12PP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17877, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQfCRA9TVsSAnZWagAASlcP/1wFxncovKnsSKcT3aLT\n55pp8BEwyunOGMAY79wwy6DGVMSMZh0OMRyHpWUeEk8uaBlTLkMtGuRhiKxJ\nQ7T19FgjPI+jsCnOblJe6M4k1JSCz+S47C4LbtKeP+1MFWJFs+b8WK4LD4wt\noJnCqan9NcaOiBppZUuI3oOnObh3XhSj02Q4tEX18ONKPJL4i7DZQwB3TUCO\nTIFIeZNYiZrla5K7ker1xRWtwwv8xHC/Ke1QFjycFRVFB0lsQSLaykOUSVFK\noHXXxdiwgtEz7huJGD0f3ngTZTKsuBRXonflKiGR1pLqimfInrCeJyXid0rF\nKwpHusdmehejGUnGqYQzwAHd4kKIAr4o4fiHLMOYf++/mTAIhJXtDwxzTXRj\no8bJbdDjgk37rUzeawo52nCZZ/vBVstuC5LHkzPRFV0/EPGw5wsg8Lp015Fs\nh+McTsaiwA4pxGic8CoFVAwJNaYr7j9llxtqbU2rNUE1TCHaThMgeJM1NudT\ncE1EKpu+H+D5lIII1SxSJmMnc6ZaatCB7m9G2UfMFyrrJFg1eC5GsxoSZ1e7\nVqq5jmUoBzFwYYaArLUdoDVa6qxEpa5EZPPpDOONXS3zHHgpzGWt8eSCHL6A\nwVdQqQl21NUyLCpSjNCNGoVxDZGHtFcLc/NJyZIXAorYqrs5ftyKOMqOsd9C\nEK7r\r\n=VN6w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-helper-module-imports", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/types": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.8.3_1578951710996_0.3407353571405187", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/helper-module-imports", "version": "7.10.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "dd331bd45bccc566ce77004e9d05fe17add13876", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.10.1.tgz", "fileCount": 7, "integrity": "sha512-SFxgwYmZ3HZPyZwJRiVNLRHWuW2OgE5k2nrVs6D9Iv4PPnXVffuEHy83Sfx/l4SqF+5kyJXjAyUmrG7tNm+qVg==", "signatures": [{"sig": "MEYCIQDO879KsjCDXhqGBhITN9lKXzVLbo7IrAOfRazavMkEuwIhAL02m/df1TOwm1IShl+kPw1bpG0HacLX+d0wxlDTCwgm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17928, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuS4CRA9TVsSAnZWagAAkNEP/3tYys9Qm8e8BvcCJDME\nZ8sQUS5n+9lf0vZlyp2nApEZtZIThvwagN5VgyPO/bBEKeBHoBm7HS7jkLu8\nqrv9pxHnsfrxQGCPqWFmVrPxPC/nWLVu7LyGhJcO/e3FE9dzpverJeLaExcG\nx/g5CSk0fAkXxXiXjsTrjUmRoJ63AXnbvGMMxsOQrZXAvzna6G+B2cT7O4Ou\nCsf3TmpZ0ICQOXljrqbVN/DLApv0/A7Q2voc5gHQqGfZWAKyc03AM/e2ekOY\nukkT7fanVOLSH+tkEb5Fj0F/UJIUZ3S5S7AtfMlPdd//hyJOaVxP50EiSXyI\nGp3KTm5il4aczcK2Ek4c+JlYLdeFZQdyirPYcOZOOtv8/8VPcubRuSIT85IO\niUOoRHPgfgzZX+B8mkH6cBCxjOiovfYPIsoyIUgDNaJkQJJuySogmdTAb31k\nJxFEX7K+x8W5wFqqIJQeJnTlsQw4ve2U77x/qtaOX6RXGrQ6PnEfPLjJgVtB\ncMLcZj3Gh3R/tzVfBVmKUv+1vQmAgkYp/8J7ZNQMaOfVm8y327+2SGTcu9Wv\n03AT5tOYvmxkYtWOpH35gyYUIQwTFXjyEgip+5PcWQ6mCr/OdDrzkhhuewRg\nu5eV6WZpwUa4pLlBffaZNKmMqceizwJ7wvPTa3PAtN5JI538AOjC7Jhbi3eQ\nEHch\r\n=jzO4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/types": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.10.1_1590617272097_0.0037621506649407888", "host": "s3://npm-registry-packages"}}, "7.10.3": {"name": "@babel/helper-module-imports", "version": "7.10.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.10.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "766fa1d57608e53e5676f23ae498ec7a95e1b11a", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.10.3.tgz", "fileCount": 7, "integrity": "sha512-Jtqw5M9pahLSUWA+76nhK9OG8nwYXzhQzVIGFoNaHnXF/r4l7kz4Fl0UAW7B6mqC5myoJiBP5/YQlXQTMfHI9w==", "signatures": [{"sig": "MEQCIE5KXRGqgh9+GFLJpmCTJHrHrZjYWDQFvLCJ/yh5ExbgAiAcICYy+JBJ52kLc2VRF4/Fws5+Iys1F6r383XVCsrKPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17928, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7SYHCRA9TVsSAnZWagAAkuAP/0pZvyM9SzNRQg+ao0Cu\nwq3cL5wU1E/IWMCKm2m0yNnJI5XryRw7Tkwvg3/x4/KB/phsgJ7KDkBJJR48\nwwP/3klkKji7GnOeeleKHRQPwe+/W9qRknNp2jI9Wx6VigoX6AcpeigHrE22\nO3IA1Iaz2AyKZP2FbEil+2VuwtDqtv6ITrCUbupRj053kr8bA2YWeRi2yzeS\n5X6MT9n3NzwyN1w+6W2pj8mz5Xwn8sdl3ecqLvGiFbOvRlCkaFLj4KkmOmdm\n5Yy2LgP7QS3tzmMZa2vPBwOYS6M3rxHYE6mrkxruTWav9wS4jDgVWW3SqQtq\nvQFelcEukgdKs7o+G2rkVG03jWOjAquM85nI30gtlvNth4IRbciBpWGH8y98\nf+Owc70QsLT65LIWY8CLHc35W5PfCklAFEvJN6zLBQ+4t3kzbC1dXQVHydfU\nRameS8v/4kG92d4rkRAldCMcSpwa/niLEnb4ThYk/ttw2/2lhcg2gPuu1ppv\nM3r6my3k234kFv0GLGD9kqSMYyhfeOp4wiras5C7LJWtU5ez4dI+/irtpffN\nliVnMubbARIjugE6k7KcHKSI0YG1aex6wxJnEaHGjJ9qsEk0pZDJHxNOrcNn\noigf58pNwnasDVSRWepb3sCZ/RK4o1lawwiZz5/BvkXW8/ia+JuAKhdyjsNW\nWcjg\r\n=6fz4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2787ee2f967b6d8e1121fca00a8d578d75449a53", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/types": "^7.10.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.10.3_1592600071538_0.7371921953419951", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/helper-module-imports", "version": "7.10.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/helper-module-imports@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "4c5c54be04bd31670a7382797d75b9fa2e5b5620", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.10.4.tgz", "fileCount": 7, "integrity": "sha512-nEQJHqYavI217oD9+s5MUBzk6x1IlvoS9WTPfgG43CbMEeStE0v+r+TucWdx8KFGowPGvyOkDT9+7DHedIDnVw==", "signatures": [{"sig": "MEUCIQCz/1YzqPQr9gLZ8tr0fYiJi/ZI4FYZBErvZ1Cua/PMCgIgYkIvxMO1qokjSbX3zovZPk++4+5uSOSC4SVTxfAUfog=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17928, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zo3CRA9TVsSAnZWagAArjkP/RjEuffSm94I8XMWmdHm\nHpSfvtgJFBDOZ77U4mWANH6vllUwDDUovkbHMl+FPdvzqgCTPz2LczZPlOjN\nhPNDY/mzMc3EEX2UuSKLiqiVfjVkiFgCJx4ohyE1tKdI+xVnF+ZkmcrgyTrq\nihPwJIvTfh7wwvmJf7cyJibtcblWqCa220/gGF25yEAVGVgKkcntCkOZ4QpL\n3iulC99PkJAFW8XeeL4Azu0WJ6U0JgwBt3EYCCBw73E5L7ibDz6nP9Tro0cj\n46cIFNL4mQOz/1mvKAdK6DnkMvBz8GBb/h1WrmTGKKKE8+/MS9bHthGqwOI3\ndWKO+66TMM6WfRlzrCZdwhdo9hWWs4VAFehKjHodXN/yS0NYdrvb9AxEUW71\nceGeIhzUhaWMhtlHHDJtj/0XWgSeev9sDtE+ScetVoLNiN8C73h66aafPS2C\nb5GUvEHRtKN9Hacj2+Jirmaikeqd1pWORe8suo+FbE9Ly5LwaPsN0AbpQeD2\nbU8PqVW5wi0+ifH5vWrIicnFCQpskS1N4uA+e8UwlPXgsSwiVLzECbtXBf9J\nv8y2sN7SPzQhOa5bCwqj3iS/u6jvimKMRjVdnRLCPxxeT0zSd2CoUfy301Ga\n8bBwbljDoO3HRli4zgv3FU1bba3DKuyQHeGdilvH9WUzUM/37lruWAe6zxmP\ndO87\r\n=E/gi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Babel helper functions for inserting module loads", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/types": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.10.4_1593522742580_0.3328129846573955", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/helper-module-imports", "version": "7.12.1", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/helper-module-imports@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "1644c01591a15a2f084dd6d092d9430eb1d1216c", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.12.1.tgz", "fileCount": 7, "integrity": "sha512-ZeC1TlMSvikvJNy1v/wPIazCu3NdOwgYZLIkmIyAsGhqkNpiDoQQRmaCK8YP4Pq3GPTLPV9WXaPCJKvx06JxKA==", "signatures": [{"sig": "MEUCIHphlrpasJ6rYPwslhXYi5g81Qyz1y6Y+4NdAVKXZSU5AiEA/0evT8HNm2ts9iMqsTxi4LK8DNyoJ3FCr1rQtRMtNy4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/5CRA9TVsSAnZWagAAozIP/RPzx4z3eEq8c5I/0Ajk\n/lb5i1kTkyUIdOOK0Gpz4UagA35ATNl6dO4GFE+llpI3IHZi58Vq98+o441M\nW88bg9Vdjk5Zyea8wOEghWRw0kaPSEEky0FmXTT+6GWFWU2ifnbqF0M+d+AA\nCdn3ewuoLGmXrE4odBpnglw5EWlsFfa2tL2ToccwzFa0yEJOx4NRyogvU3+6\nkRq155yIS28lM60zw6Ub+ddRNFT97Kvk87zF84W4ZFIUyDhQf4p+qO6qBQYW\nbBbp5ZLCPecNV1QRuaTk1gvH/GCSFXjnLIb2+Gs6t6BCnUJzOJe1oUZ+Gvqd\nn6JDAn+QBzaTvpMMFrhXLY3LYvu2b6v51KdnV4H9B/LqNe5r+vuabLvS+qte\nChO5qZ3R63fT781WvaW1wXMqsuKwGhVcncKEeO+Y2fwEiw6KZNBEBgMncahe\nPAVUdUMn1JdLM34HN/vrob1pL7StH9mlUK3K9ehc4FO2/vK/91C52t7gus0k\nW6h42zAKiWqq8ZzqVI56z++7JbQyDC/OjqzZM+QsL5ogRbBZDd1A6uVhDmfW\nAWMU1kjWE2qpe159kF1ZGfjDWiX6M9rLr/infSXDpLASdzy7W9PV/vtcDLxz\nsjKTwZtv5IgZVj0sxtzN9Gl0Cjq0GlcIU2A22SMgMvzySIWeG5Ef9EvmGJAB\ntTOs\r\n=spUh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.12.1_1602801656740_0.06228997245327128", "host": "s3://npm-registry-packages"}}, "7.12.5": {"name": "@babel/helper-module-imports", "version": "7.12.5", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/helper-module-imports@7.12.5", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "1bfc0229f794988f76ed0a4d4e90860850b54dfb", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.12.5.tgz", "fileCount": 7, "integrity": "sha512-SR713Ogqg6++uexFRORf/+nPXMmWIn80TALu0uaFb+iQIUoR7bOC7zBWyzBs5b3tBBJXuyD0cRu1F15GyzjOWA==", "signatures": [{"sig": "MEUCIQDYwAncMsEcRUEOsAThY3m29m/zfJ9qgkmsrvV9r+8CSAIgLepdpPPSuM4xAGYct/otdZNCwgu8OcFR4bp3jOqWiOY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17859, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfodr0CRA9TVsSAnZWagAAIjAP/2vjMzH6AM+j4jRmppDv\njZUdHdKxleblavr+LP0UWJ4Fw/tEiSDjyRzrd/AglronDb5mFGkBsA0avdmZ\nWFHx7SPYWmYqXJG2sFXgOSuRbBhBS5ZASo8ObTBPbkjq8eQ5Pi6OAA9kY7M1\n81VSRwIPSuUgcsb0/N6RHKulWkr3k6yW0+RUj3EVdizyAMhBnKDD5gOHgGVi\nR/Mr+Q8/YGrnnx9y5HV2ycXt80h3xKnu3ycjdOVVjjpHE0TeEDo2eADXLAd9\nyNf1HHEeir5RDf2EqntC+x2FsOwuW5TTvZXBc9ZqO7cSp8zoMiRjrLIqvuz0\nGyhwLtSFdELB+Rf6Wf0hU0mODbm1RZ9FYsd1xsFoL/XEQo9w8g4ILRwlSsTQ\nBqa7DFEsuWvFRGW8OB1iGpGNslUkbWRzByG6as128t+JrUL5Vp1fQjZukt2g\nopIJAudwutsUMqoKX9XvR7RwzAZ66gKtzs1Wv4AM9ucj0zx/Dv6bfWl5liDW\n5H60INcC9mSSrDFzobiPyFWqoZ9t4PH0wngS0Mttx64c2OYrUQ81U+BfSSCI\nlIWiF2MHLzH4jQtgErWSo1bBt3n/hPoxLUAQSp9Sllr4ZtY+Pglok8IhLYS9\nipRJRj56Zs84L+maYtLgY+MKaOq4yfjA9GKUAh8LZMIxFN2q30ZxN6ZpaYrc\nmDbV\r\n=bk2K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^7.12.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.12.5_1604442868268_0.6711869993614985", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/helper-module-imports", "version": "7.12.13", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/helper-module-imports@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "ec67e4404f41750463e455cc3203f6a32e93fcb0", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.12.13.tgz", "fileCount": 7, "integrity": "sha512-NGmfvRp9Rqxy0uHSSVP+SRIW1q31a7Ji10cLBcqSDUngGentY4FRiHOFZFE1CLU5eiL0oE8reH7Tg1y99TDM/g==", "signatures": [{"sig": "MEUCIQC8pQrWWec+BWvUg9JNglOQLOQa/a91bksYIBr2dJOWlQIgTvlYxed5Un+doz0Uwkx4vx49OdJc6fVCHLSnS38EZsM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18412, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGff1CRA9TVsSAnZWagAA9kwQAIp4FLmcoBj/ICuGHUW1\nv01948mJaApVzY4vAW4swV67cDpwrEUl4p6g0cEpfXtRP4/EMGKY1p1nRy73\nHY3qBLY7RzzziXMinlDE8lhg/UWtGC47KBGxwJNlDzdIvsO1PoYQaYgkTV4L\nVX26W7S+rCzsiP1B8FoeMo03fn1xWSkyNzrVSVPZ39tIAyt8bjPfQ5sa5WaE\nWGB63sPgynCR5K0P4tN71wVPW6kj9QWud3+rUs9EY9mGAcmV0m8oerUN6890\nsRkrDlBFVKkevj5q5c7jNT7IYhzbDcY7oMn+7ixzOB8CWJjYgtthjYCV348k\nS2uESMfO5I/6X1fFntAJzJUul7T9U4t5rFzDJ0YjQejhcgEw7NuTjDUQHggB\nWDp/vf/lqkRM+u10QiNg5d9PGM3jtJ6fn+R6S0d9QhgfXIEi7uKT+lW64xTO\nSzVQndFAc58vE6wdvVV3jMHubZ0QpAWGwIfl+l/CuPpPuRc4/kYIIeYeEFG+\nJ0dRde0V3LW0FSfrKJrAZjzmocjY9i9bJXp8s0JSrXhVlITQyHOA7psuSd9E\nmn/Hals3YNwF4CeVsUaotbi4Q3d4RGhiwroXCcE9CdVfU29MF1zhSQTferNr\nmulE9D8NYGASfrovvnwXUf4xB9JmvnCYUPglRcBgcPK4MXA7HhVtuHfShvsd\ni5bC\r\n=iTFX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.12.13_1612314613413_0.5940379557713835", "host": "s3://npm-registry-packages"}}, "7.13.12": {"name": "@babel/helper-module-imports", "version": "7.13.12", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/helper-module-imports@7.13.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "c6a369a6f3621cb25da014078684da9196b61977", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.13.12.tgz", "fileCount": 7, "integrity": "sha512-4cVvR2/1B693IuOvSI20xqqa/+bl7lqAMR59R4iu39R9aOX8/JoYY1sFaNvUMyMBGnHdwvJgUrzNLoUZxXypxA==", "signatures": [{"sig": "MEUCIQDIDOMIFzg3s64EfJTh0FBtvx0BkZTRexeKg/zjx/sEDQIgWfAksr6YOY7zubbhGLgKY7wZfgwqbWJ8qpK6AVTLuek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWLwACRA9TVsSAnZWagAAYcMQAKUioMqIw8FZYOzvd2Xu\nnqtNPemU36lLcb6OYrcKu6bLBz/TJvR+ASlOuGYfh6kcySKmBhR0FdFUuYOT\nWroJU3Bm+aPAaHOyTSNedB/CTHk2Egy5JVsuyPEJKobKAgmAKzV3OWCsrwb5\ns6GXhc5+nRraedT29BLWVrGHjv6ZpdL3j+1iNCcKJIZNNx5N3Wn++XaUWFgy\nSqmXveEA/MCbeQIILoUGxOyhpV4W85OB+9tqFvoFFmh9948vGp8/Vjb0H0Fn\nmI2R61RoAoKhJCWbhlArB4B8PM3a1yqFQSWOAkY01jX3hmCWx3QUmVfIV4AJ\niiUdTC9yr9R2FhYiq1zWN7t86qKvcDAmQ0epwkJdsnCD1ix1N2pJWHXTa0ar\nvQhBN9Jfl6bjD/Bw5wr+xOCxKXACXpxRQLC/oVL+Ie0dkVlgXLQ9bqJqRZQF\nsIqK7DJwPX5dpt8TANojR4xoBOeO08iQ8vrHnwvjPwc0w6FC0RZo0eayTdQ/\ngDxnlgO445Aa0f5aK9Fd0RlPuK5homhadseiojSp7owh+XyGzidYiA/hsepO\n1+JPWg9pjMeQu7exGrQPSK+ztKz1ybVEAJT/t8xDiZ3neNWGLkgHqJz/gNjW\nYf1m8JDZOYRyiPj8+m/EbuQHCTxcjAvIpZeoZHHxAHs4K1HcgGZrIy6u8frt\nVtxZ\r\n=IMmg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^7.13.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.13.10", "@babel/traverse": "7.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.13.12_1616428032463_0.02650905314837826", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/helper-module-imports", "version": "7.14.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "6d1a44df6a38c957aa7c312da076429f11b422f3", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.14.5.tgz", "fileCount": 7, "integrity": "sha512-SwrNHu5QWS84XlHwGYPDtCxcA0hrSlL2yhWYLgeOc0w7ccOl2qv4s/nARI0aYZW+bSwAL5CukeXA47B/1NKcnQ==", "signatures": [{"sig": "MEUCIQDa86rnLwR05m7W4bvGYNOqcsdQr143t/Tn4B65QZAH7AIgM2l3v1JKira7XiBtCXoz27MGV6bOItimLje7S2n8Yyw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16039, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUraCRA9TVsSAnZWagAAQRcP/RAeOLlSE1pcGjSSvqX8\nUukUYrefJU7bU0UTqB2BLrXAivoBwu7wiabKytzTrTyp1h7CFqRD6vvfaSto\nSUedrYEpcN51//vHONGkMDeRvSWqSLojH+bVp3VgLu2M5RjzlaQMxTopvloZ\njX3XtI1I679YBujrJwlwT2ZCy4btsWZH4MA4ZeZC03kHRYGOKAsT2SXPnh4T\nmfKOLP5m7qAhblHYUZFuof1GaMqQKcW5MJJo9VfzfTtXXP0uomSip0c7//7L\npOzKd5XIHcikYPaJh70NQE2bf2i7gGI5Dca1jfONHBAo50wq/U4Fv2mxyEHe\nimNZKMBY8t6vO4w6kMUBWRhSdGv/MZNo5H8Jq/TBGXHVMtN2d2qNuEclfGJk\n6PGmEI0e4X1pjO/o5cWSFpLydcIFxTxHJntEEscesCSw022eAYhYnd9LqCSV\nZIRUWMcX/oIRVi3pXXxn2wQzv4yC2J5YhTfPSG+BOodN91KUrZqysjA70u8L\nRMf3Rxf4pizYkoGugXJhcK5a6JjAYG0kUADQPFIoG5zCso7yasaN0NjsBHgc\nDqLev0ZHfxP4eSjRpaAupM4DbpEY5Pd/tv3+xDRl9nOEaGG5xyMG+dUyLKIS\nDXoC0i4a2rbYENQ4irEQMXu2rd056g5J6PudkqIdCs7x6PWe42T3W7tAt8D9\nTbYV\r\n=hHRl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5", "@babel/traverse": "7.14.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.14.5_1623280346316_0.274281853309557", "host": "s3://npm-registry-packages"}}, "7.15.4": {"name": "@babel/helper-module-imports", "version": "7.15.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@7.15.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "e18007d230632dea19b47853b984476e7b4e103f", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.15.4.tgz", "fileCount": 7, "integrity": "sha512-jeAHZbzUwdW/xHgHQ3QmWR4Jg6j15q4w/gCfwZvtqOxoo5DKtLHk8Bsf4c5RZRC7NmLEs+ohkdq8jFefuvIxAA==", "signatures": [{"sig": "MEQCIF1e00J2Jk8pMeyhQ5xMswUTsOU86gncHeU0XuI8qtyDAiBx3wnVQyJHquaI6p/ut02dp0eiiuwBW9Jaf9V9TnqJLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16303, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUSWCRA9TVsSAnZWagAATj0QAJf3AdbCu/99/UU8LYM6\n7DsJ9MRBfaozf0NFdc0aXzx9oaUlLMC2ykSgfEFvl/PpzsjeomwKGpPRvt9b\nTdgobgtpT9uY+RtxRcUGiIsdSLjefwWfeXnnkjaDRSjsD1cIXdk5AUqzTISM\nO3PpxUth4z//rBrxYJK+jYW02B/y6UfLwByXu9NlXZM5HWP4QYgMzY9oGaUg\nigW//QRDzk/ybCPi5alcCWCZk1IFDO62MXg+MZulEMvAXfPmPg5wkrVR7hTC\nnZhxWMOEfiLj/5qKzex5bMwqYpn3ARngdByq3rpqwcBICErDZbvSoJxdk8jw\nVlxbg7S8/4xW/tiovxy6jjcy72WEC2dTB4PQ4/ffmC1tNns5D6iCMUgQbm62\n7NqKGKHfk880+6lYYgQ2/qHeVMiZ4+SIywavoT1GTg2OcoHPl/3Y7DuxHvxS\nUOwapZLjFTVcmVrUfGdEg3zDhQWvDz3tGBHiNwOwL4lvBuq68t0pHEUp7WRE\nU+m77RKzLYWf3iKeUr0aQfj8r2+qW8u29dJJ6L3cZEN8xwtMGKKkRhhlncir\nOgrv1NrmAVU8x6XQPE5x3K+3H6LxB5hpOgBdY/MHFdjPg03HxmQbDx3wA39a\nQ979R4c7UsyRkUgBjxyN/hVbTdtqJ8y2wEkTLii05gFQvPEsG+6jf8PFcJnH\nIBnT\r\n=MTwz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^7.15.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.15.4", "@babel/traverse": "7.15.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.15.4_1630618774261_0.39277790360949294", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/helper-module-imports", "version": "7.16.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "90538e60b672ecf1b448f5f4f5433d37e79a3ec3", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.16.0.tgz", "fileCount": 7, "integrity": "sha512-kkH7sWzKPq0xt3H1n+ghb4xEMP8k0U7XV3kkB+ZGy69kDk2ySFW1qPi06sjKzFY3t1j6XbJSqr4mF9L7CYVyhg==", "signatures": [{"sig": "MEYCIQDrwViq4Am4tw4haiQZH1c52kCNNqGVmRl9gaZenJm+ngIhAM22bbwHe4JYGWhiS+3Jw4/xg0rdC8wRO40tlrR86PfF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzHDJCRA9TVsSAnZWagAA8CAP/jGj974v9YT8TNF13qcx\nYoW+SA7ZWlmGVMmYAtSBQqqUhLPHLP98qUsvQo/ALABNolTefImtGaWq9/rC\nIRBHiGqAa3yCUSrZwkR/Q99/BVhj+acFINWkWg6j2UzL4oRgQ447mxHK31tm\n5pzTD0xCmULaYHZlmeCO7rSa+OBvoWaPjospMvzeLu4GLRKrvbRe+5D7G5wQ\nPVv9fsghv4QtiOxFGWX0yF7hCG/m0r6ajpbOCluqLoTDa4EEQthWupXXH+Lu\n27Y1GRntUrITww0Gn6g88BIkqmiehyyYGM7Dj93jAsy9kIllMVco7f/UMTeD\njFyrni6lhnxsbUWOtC4v7HJohvZri6W9g1pksvC7y44N/8GuWvIH7yCyANVz\ncFSeH/cVX41s7fpYrmG//lWzRDo3mS5MgYPtSY2k/reZPo7LMUTt0w0+gvI2\nOkSbkeg7gzCLBVEdvGI5HIgfCVWSm4GYsBYY3Dzyg719Q4UZuD4i7FxS943M\nX874rkY7/51iFUd9coqypBRPVIRL5Th7F+QHnxgAc0Z3K5ru+/XeksU859h2\nBLtoi6bhZKZabpqRwn6quG1rWxElKX2++bs5UHRMc3nmViFJBs16IWyZHEP6\nSQJ8Xps+wCw9JChcRdasQLA77s3ampdvhPhq4uuROMGlOoyZC8Ghx8k24sJg\nwu1O\r\n=13Zg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0", "@babel/traverse": "^7.16.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.16.0_1635551262554_0.4163393745408204", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/helper-module-imports", "version": "7.16.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "25612a8091a999704461c8a222d0efec5d091437", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.16.7.tgz", "fileCount": 7, "integrity": "sha512-LVtS6TqjJHFc+nYeITRo6VLXve70xmq7wPhWTqDJusJEgGmkAACWwMiTNrvfoQo6hEhFwAIixNkvB0jPXDL8Wg==", "signatures": [{"sig": "MEUCIQChjJX52GJGsiWEY8Pe4W5AxXT6GDgE4ttppDSNEo6cNwIgV7SI/NK+6Jsr7coG1H2lzNLOVUi6BhGvzpUeqj8d6/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16295, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0+CRA9TVsSAnZWagAAi20QAJmdnwYZ24EzyOq/1J8k\nXGjJRN+bldoamJHIximjHDYRejbwWuK6aoStySn7hJSJyvoyXsrEfcFX+N8R\ns1BJsLxu3L3+G1gWxcUoLelXurTdeyfcsEfbQGEjfQZWPkKjm5abY3UeePZ6\n4DtDuiRIdHDeLBbhnxQJBU8DdY3ad4pJk8n2gdZAB0UwMEYm5V44UEYCTj5e\nBU2QQLEGom2G7qOwzUyw/W9zNGwlUFkujo5HC0h4Ip5ZMqzXwDeFnhxRu0Zn\nhPeLAKxIemsStmKrM+6C+ImT50V+cmOas2oMgXTupeGpuE+kOtAfNk/5CVKr\nOeBMhhwQ/awSGlI4vzcOVP1vFqp2DsLxeiFVFoF8wwsBaN7Ya0fBR/X9EXVH\nbzBwEoqQAK+l4/7BET69rGzVZMDBZEGGg61AEYRzu2kGXKiBsqQrNbBAbPPi\nc6sus/kHyIuErwRgX9I4qNiGlOGPXtYQNyt9/Qoyn+6K4f+Lap1sq7n+HRIQ\nsEOOfIsbT9P098DR8vVUAs0H+lczZbuIOvrfWYmSE2df1FkDFD6Ful+IvGgZ\n4hzpXlojKfEUTy2c8l9Fy5LrComA/yk1iam7pPqtkdL9aVCFUSS2TapMDkJS\nG8Ifnctk6vPLRVq1Lz6c5qq5OaFrJ4Lx9ChhSY7oB3Yhrx4NICnsh8xg6hIj\nI/W+\r\n=MbXI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7", "@babel/traverse": "^7.16.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.16.7_1640910142491_0.6296452001621233", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/helper-module-imports", "version": "7.18.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "1e3ebdbbd08aad1437b428c50204db13c5a3ca6e", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.18.6.tgz", "fileCount": 7, "integrity": "sha512-0NFvs3VkuSYbFi1x2Vd6tKrywq+z/cLeYC/RJNFrIX/30Bf5aiGYbtvGXolEktzJH8o5E5KJ3tT+nkxuuZFVlA==", "signatures": [{"sig": "MEUCIQC12Ifm2kFvfBpYkwY20RAVbggrMPUSPMgQrq5ck2tyFAIgHsiBYA1fFsnbgv0sYzyX1/bdNmo+dmhXwOoqujQUxko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16068, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpghw/+IdXvaSNxtHk0VNJuDUY7fSCTaxH7CI5McD4MDTYKAOUN8UT9\r\n3gkoLddf/E343svSTsd8I/1Lt/MwcOGs964CMrAsrxcSn7qkvWN1RB5jN0QC\r\nnxiC2QJTy5gZnRM0bCunz4KKPhW+rr1s9RsdlnRXL0U6mKhbh70mfBGDgfLp\r\n58h1vYyztG5QmQy0a5A1ngEiQ3qfq+aoHUQOHu5dV55aat2OGSzkyHVvHL+6\r\nFFAIsZW1UZ3cTyfZuKNpj5gSmcFeTTqj9kLk3vOV9QBPqX37f+Te7UQlXoCH\r\nBLAu0mpI5KA+74CYrivcJOeWU3VOy/eJtcaocLV0efTQKDCwKm3SsA5EVnY5\r\nGeR3HFbgWh1VDAeV3ggZM68SbsJxUVSUb1rFQ8ZvVtKl7v3U9wtO5rT+AJM2\r\n1T9pCCn1lJJyjpPfrgHz0kLCoMU0tj0LmiT8KHtju1x3m/6Ywp8wUbub86j2\r\nWyJ0ndiC5QLFhhzhHmcC79vxXpzz9OEtCa1DQlSkwTuMvwpwfJ7GwojhtYJq\r\nfREsTN21g0aUNfnFQf1JGRPfeJnvSgs23/V6mj0nJm/ZuKNol2yIaP/z3ayZ\r\nCjaS+iNqNsKe62KTD1g5r6juRt62QaCq3Qjll9SGXki9/WhHLynNDVA0RqZ9\r\nW9ikKqQJUf78bwCubBrrAN7falB/i3RFdZA=\r\n=g9ep\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6", "@babel/traverse": "^7.18.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.18.6_1656359409539_0.14281590625710505", "host": "s3://npm-registry-packages"}}, "7.21.4": {"name": "@babel/helper-module-imports", "version": "7.21.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@7.21.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "ac88b2f76093637489e718a90cec6cf8a9b029af", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.21.4.tgz", "fileCount": 11, "integrity": "sha512-orajc5T2PsRYUN3ZryCEFeMDYwyw09c/pZeaQEZPH0MpKzSvn3e0uXsDBu3k03VI+9DBiRo+l22BfKTpKwa/Wg==", "signatures": [{"sig": "MEQCIG0vwkjGG3alNVGS8ub/e6PMBrWOWRb3/ettpsx9044nAiBNL/7fR5xnuTzc0S4XYws46XMoNsbd+Lz2k3Ra8jRdRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJqGEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmohQhAAkzrmKRxXZdUbFgLB8sx8F9XobbVTdcJA5Ba4+kj7DukNVI4f\r\nkhgOANVRA2H2RoRjpqSnO2pET9rOu1WY6XCvKvKmZ9eR3kLEdfTDenPYq0Gb\r\nq5gLbdk4Z4jFbpW62QfNq0hSSQupUR7tvdUySIHnD7Ps4Nxv98wnqVh2l2KN\r\nSOXcV7hI0EoDnA8rBMhoAcYI/duWofUHVrP/VFZUt2Vx/w4bj+8Ow/bCDyQ+\r\ngQRKfOJeUH/TwDFuXcqeMN6mdjONv7UrJy8w/47Lk2tviyo3xJozuxeeoO5R\r\nYyD0iNEK8jRr0Pk/n9sfJrI2HuH679G8lmMR7u8BAWwROY6fuML3Wq36wHFk\r\noVlq3PYKmmw/mhy/vf4XfZwDmyFouiA26vLCLM9arBHYPcDQLx4ffFyDoy1F\r\nEtoSSm4i5vhZ956p1hYb9lSEBtyBK4TpFrgVDMG8rS69AC2T6T6JBVdfK41V\r\nZUOAfbz4dMBnf3Zaq2uv1pOyaeTXFLqJMLyi4JlGqsFVMShsKyaGp+g3jF8k\r\nc2/UxqNu6SeMVQxieAhgFBOmrSJt1JXWhUycKZAHYHeu9GE5RlORwGFZF2bc\r\n957ucEcikvpEu00jXl9ZgA2W/ZE+lFsKlTxowSsz8RS6eIpiwSbXV9iO+P9y\r\nxVBRSSy/39iCyKa/PmpCZ9UkJqYTuDPkXdQ=\r\n=+p6v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^7.21.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4", "@babel/traverse": "^7.21.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.21.4_1680253316617_0.8920970742323691", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/helper-module-imports", "version": "7.21.4-esm", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "f3a9996ee9696f4dfb4d1eb054d88c6b1eaa202d", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.21.4-esm.tgz", "fileCount": 12, "integrity": "sha512-WUKwr6YgQ4YMsrCYqqNm1ZZ6/ev9pioqk0AfAHrXH5Gun37I5VFsWV/Y+s4P8oV3OooKBhT3VuPSL4rmjHvC4A==", "signatures": [{"sig": "MEQCIGZZGOjqmj5oJgYYewRvuMLVukUUHKm/YYa7qWjPq+2MAiASi9W4Ysj9B3FuEB1siE9lpDPcLA6bbljqpeOqHpWGXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+fACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9UA/8DgaOcaCBnAw7CR9lnefOVT4wYRhslgNkJ9xkoeYkcRhysUv5\r\nmnbW64mIo+PeBfNpsCG82MmWF4sx4buTWAoAdimHc+qVEV9hcaBy27i4MLPw\r\ny3yzTn/WY//YuOqQECN0j+JT2v1guDpvC9yTzSACLBLI8uVWaEYNKy/QGbLF\r\n1gygFRJBDgvqiNr9M7WA6AHrAKtKpbvjgiO5KBSsocWRCuRGrL5N6FporNz9\r\nBRKfY8CGO4xyMTifsWOZ8zvCRMPR2ZvHhnyPz5P2aj3BmQoH7zo9velCX4hC\r\nOKQVNCPxs62LffVqiHpprKBBf7zv5nyJuIUfxGSxufBTN8IxplSiBAuWL2tC\r\ncHXhY4rJINLGMkdLbyal7DEJI5vCyKU4g6FQspHl2P0osxvQM8MdHyuz0UtI\r\njC31J1k6wUpgQ1uX/fie3h3NJrzPNB4FYza4O2G8ar5dTXmEbiv558W5ytWp\r\n/+uu4Peqq9DEfYAO/iXnBO84E9efH+ekt8wAmbhM9gOTyjkF4Cj4S+PZAbmp\r\n4OtydHaZAw+bW7x5eiX+SRHT4U1UqRbdkEDVcknb3B2BGXFT2oSGiGSxBb24\r\ndCnn9GZl7xiRqMRzaAN64ZkXEDcnaxXATzvp9ZIZp44okYNhpTMC7QyH8iD8\r\nn5cWu1QMTMxaCGAF0q25zAFEkfG0X5s5BOQ=\r\n=9SvT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/traverse": "^7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.21.4-esm_1680617375391_0.6232372815402896", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/helper-module-imports", "version": "7.21.4-esm.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "a19317153161d674d60f52881f4d4788920775cb", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.21.4-esm.1.tgz", "fileCount": 12, "integrity": "sha512-6ZB4LUg9FU0FHMaJ+cZpYZF7C5uYMRR41X1h8v+GFQkBOzkys9/M2KNQHGJ0LMGr1Xw9x7eRt/+MXsSodY/how==", "signatures": [{"sig": "MEUCIFbWK0hyODMEek7+512tmNuiEaG/f7kt+jK0pVqW8bUVAiEA5A7eXlrBponKCDIO9GZzvHGv6qWbfh36cBGlMCoHT04=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrCvg//dqo9/6cTASHwSPYfoMKlrCojgMzl9+sSu+PifGMbnB69WsLH\r\nybBbkRQPNISS7DjWT1M4EtvpHJUjDBby0o0+6QM6hVW8OSUdDJXk+73sih0H\r\nMjP+6e6htqQh9GiGacrcmAieYSZUEK9XjGWi2wn8UDOcLPAemtuFaFFFdedE\r\ndp7oA8OL3LpjiLxb+De2xrKeXN09XAkdSFpe/7I2ybHBKrEDIT6p4aKnk2GT\r\ng3e7QWLCkwIijNn0PHIixDEyfwGJeoDy8fzgY8EnPyQf4FuGSdArxMeJMvKO\r\nmBTTZ8aaUk35DcLqY1NyIgckBcujPG4h4/+jWu/YRPLnAdKvkqE82CMmta0g\r\nCcOJcThuU0/giz0lcMqZGjqYYFqloi5d7W/vngEugL13GJjlsAAnFyHPC6RO\r\nSc37cfazkCosEg84oZIu+kM/ZKlokdJcItRY8FoTglfMrp3pMhqAYLdqo5v9\r\nMkdiO7f0GnQAxg0FEXrAwn4ubmzK3KleC3rWBKO1yw1Mu9qn1QXyT6jQRXYt\r\nL0xWxFRhtoG2DMvMAJsnb8B8LL8JZOq3IB3ogt8vE6B0eZk+yWXhxTUqtFea\r\nrJTSlGkY6vCYHqD001alEAeUBdwBlvPio94lx5MJKASqR5xK2tNYhovKpZIU\r\nXZI+jCTNDo4dnwelNmGEqXmULYYsjTyCVFI=\r\n=7pZv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/traverse": "^7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.21.4-esm.1_1680618087569_0.7950741259625886", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/helper-module-imports", "version": "7.21.4-esm.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "5e1f6d9ef759830aff577e0ec18ec28747aa3d64", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.21.4-esm.2.tgz", "fileCount": 11, "integrity": "sha512-oVztWin+cF8BIrcpXU2YF1CzAHXsDQf1cWwD8xHMvpNb6jNcnZ8TAfQqIVv/AstAG/a/s8qBu85B6JG9HJWwbw==", "signatures": [{"sig": "MEUCIQCpKQd4yKGCnH4ZyTuM1H020/U6nuOSa9ZkZLEOwAQXYAIgWVmEn4NYIcubYPOoMjd0cqtrcpwySD2DumEju/fzI2M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53311, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDagACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpktw//bz1JbfHTMpH31IS7Q+6Nv4jEK2eLvr/2euL2RFSr0+a8B/cs\r\ncjtG5njUmyw8AYoP4V3o35QKzQcmtcDmh48y4sDdUmU6EXyiGk7mR1STSSNz\r\n58nEcYIcGxT1Nah4AHB4Oy+UYrWqfkbxlKWJy9hRi0LsnWJBtMkVzJuZR/L+\r\n73o/6yRvs+KnMsDa1S/CiuDiBz2j+zaT00cMLxiCb2roARCVolwx1U0VZBSw\r\n4rd/zlRB0zeyrdls3C8Fg51Z6L86G+ri9nHD+f8+hJHF2sXdBvQyjemW5fl1\r\niH752sVZLMkI0ExeMT2IZZr3zpfeV2wCivvNX2GDgaysdGRiXfv+7iShurpX\r\ndwdAW0jhlag7+dDiUemFMw2clIjYDS7Vj+qAD3g3Y1RIx5nH9xT6EW+SCxrr\r\nzYPPBRw4Wm8VoZ+QOAWIm5h3iXhd9YwumTR+i4LNjAmRSjl8CeKjpBOEXod6\r\nCcmNY5JTuO+brWiazGCFLh5BtLeETaE31ZiIa6+E5eUk4+ieemc0izZ4aOII\r\n23Wi8feA3S+ZYT4fcQAUaFRR+Cg2vD3JXpE4/ztDG3wvBXNLNP0umIT0/3T9\r\nkk4BqMKuuNuvio1GWwLiriZEjg7us5Ns1fTbisdm2M00hCQCUmLzIODqQnrq\r\n1qgXhIfYVtfCq98GBrh4EE0eBCo4crXR+KI=\r\n=MmbR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/traverse": "7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.21.4-esm.2_1680619168654_0.5570048781311521", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/helper-module-imports", "version": "7.21.4-esm.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "d450c14700c08453e9ff116ddd8033f53379fcde", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.21.4-esm.3.tgz", "fileCount": 11, "integrity": "sha512-gL8d+eMJdfzTwPEjifux9UO1G6tdSL7oYnXxUXTSQj4KBKEE9WFWrtNR/P46piuYDErSh7cAT2jBd9oRtvjZLQ==", "signatures": [{"sig": "MEQCIDSOdz9wpUjLXqngZ38Yl2ht+lTQ7XTcgFrjC/LlJX/rAiB+QHvc9W6fotgkEszKJ4/7/AsJneqjHvu3phsUuaSccw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqKZQ//TrssxmcQ8xanT8fJVMraVy9CnUUhPpXUp1f5BS1Y4C2iJhXu\r\n1HK9PGQ9zSq3/jzcQci/K9OZgRTQtogkkZHNpcKbaF2WL9PfqfWSA95Zggkt\r\n3ks4je0yixrTZ2gWfmAGzQKpALBrSl6CKNz0664pUpK9I+3DCJ0vrBc7jKEb\r\nKF4om7xx6Vq3m5PRIboAImqjT6WgKnarQO7ISYHrLZ1kuLJMuzwYTE3pCpV+\r\nywd3F7l7qCTWEiDUco/MZIaG9/0xdNCdgxSu5JLv0oUt+afAENx4OrJ2anNs\r\n7lb/Ki8ieXYuxUhAbja8HF74GTLFVQvypmihtVlrOlGXB7Ixw394anzq4vE3\r\nUn2I64BHQxC7JYHdWFO3ukZy763sL9jMOWOEC9Lz5Owuc/UULah8vsEeGb9S\r\n1ShgXsuu17cMiIphfG4a/5CCl/ZrDkMW8osL3gQYK2XAa3LHSfkzTJfj9Igh\r\nCi5HCORbrwbYbkzL/j4LRn6enl+zpBfdRR+/Uqv3flUEuJU9BCdG7Fx6Fiv2\r\n2G29WpW1DUuDuTyXqywE6aFRu9qAWidmKbcigYnzV6Za+j9R45o+Ind/jXGu\r\nEt3lwOa2H9f/oibohN4KWqaJZjZ7EEVqsUZC1gknTQV+5GybCqrRhNUA0x8b\r\nWVWZaqJIgQ1T6KMyL87WcqgLcCgJdw4H96Q=\r\n=/vDD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/traverse": "7.21.4-esm.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.21.4-esm.3_1680620177508_0.6088865603584066", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/helper-module-imports", "version": "7.21.4-esm.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "c1e39c0038219798e4b0b92b5252daa0875679b8", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.21.4-esm.4.tgz", "fileCount": 12, "integrity": "sha512-kIKWl04AHSArvDolVoIHulx9IN90kHEYxpwUOi2n0opxl3TloeIMvJfvLLe0xu8XFvqyvcXAOa2awRP0iWccqA==", "signatures": [{"sig": "MEYCIQDMDhzQRqFAEj1QxFTFDhtmI8qdYB5zLYUR+zgcrgDvzQIhAMjUGPaR4olV85yoaZZg/hlk+bLYV+ibjRe9sry3pHws", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53331, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqcmBAAlOAcT+45+eUlHPt/NF31p7MRWESx0SGWKeCl999l9JQ7Q2es\r\nI2r//jZD4v0ppKJikdscx3wDKVPFuna2N06zqBcJvPJRMJj2xey7zOSx9cgS\r\n02OKaGNGCSS6rQhFAkQVGoilT8cg3KVU1OAadveSOR0mKaCH9ys/krdniNoE\r\nCHeULv15Pst7tooUCv98rSLsPBvfZVGQggBhrbhZIdoRyd3WWY69tZ4uUs4h\r\npEuoHLH9jd7sUnmdhD5Q4NOIAe7OgpYydhxtNDWlsxer/6ShEVgmfo9gb6lG\r\nb0buoq1IRmW7AHJ56sdtDY6s0xNBYEYwE1KX8SoWidzf7HkN5O54YlqzbPuM\r\ne+WOPfqZJ41imHxs6SMsBzeuZcxB6mh1Ydwbo54prV4XssZq9KeFWi2YVByz\r\nvljwzNB47+TE8OTrmfQrU0JC9ct3U3dNrh6b6BrIJ/0w1wYTQbpjWTMLa+83\r\n4DYi4GI6Wk6KN1JOszN3c6uhmj3id/RA+EORu14rKLI3g+39s+vkOheYHbIX\r\n28Vel9HuuwAmH+3ODM0QrvaDWw0AJ1cHjnM2RCTeorrZQ0jtdktXjiOdU9GR\r\nr+GdHKZE3i4TjnOLmNooXq/4sBMC6/+zXfGJkbOPyAyD1MtgID2kYdQaELdc\r\naC+lNJBA8s+K4ToEW/7kRnpE+3ooabigT34=\r\n=IB/c\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/traverse": "7.21.4-esm.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.21.4-esm.4_1680621208982_0.15256266707453636", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/helper-module-imports", "version": "7.22.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "1a8f4c9f4027d23f520bd76b364d44434a72660c", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.22.5.tgz", "fileCount": 11, "integrity": "sha512-8Dl6+HD/cKifutF5qGd/8ZJi84QeAKh+CEe1sBzz8UayBBGg1dAIJrdHOcOM5b2MpzWL2yuotJTtGjETq0qjXg==", "signatures": [{"sig": "MEUCIF4vQxPT+447Jy2Ck5QjkBiQDrYdPYMlRbiClqy+uyLSAiEAtDvgJ5OVOwU88GbPza+hdfioNxCJLuUPBwO4OFFbFMw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54328}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5", "@babel/traverse": "^7.22.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.22.5_1686248486956_0.8336319957517604", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/helper-module-imports", "version": "8.0.0-alpha.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "17c5349299e5791b5961cd1ea0350add72c8af4c", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-8.0.0-alpha.0.tgz", "fileCount": 11, "integrity": "sha512-Y69UrBkg8N82otUP0FffVt5foeWvZqE9QUL8wJN290UrDPGsnYTJIeYkeavqVnXISu8gZ2kq9W1nYyO0rJSkRA==", "signatures": [{"sig": "MEUCIG5iw71Grxkl1JUlB3aZ7ONxtuRI7qL+J0/pKcaN4I8gAiEAw/XL/9hN72fDk+nZ41XCYiU4iMvi6UpXbBkr+++Ly90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101280}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/traverse": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_8.0.0-alpha.0_1689861604590_0.330302095427385", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/helper-module-imports", "version": "8.0.0-alpha.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "083bb4f622929f4fe68c06eef2aed36c850259f9", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-8.0.0-alpha.1.tgz", "fileCount": 11, "integrity": "sha512-R/G/DOWMSQNK6ZzqgtwwTc0iseOUfhf+4iE2IzuoDh8u92XR3T1iMoDLo3j7CQQm1sYcdm6EtwhCCgAUguhq9Q==", "signatures": [{"sig": "MEUCIQC+bYng4sRXNGqYVbkETg7X/J6XhTjlqRYeUF8kGd/vIAIgXTg4zBUa8PhjwWbpowg2hNIRJW7LGLG5maQJVIBb6R4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101280}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/traverse": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_8.0.0-alpha.1_1690221132424_0.8164199551276641", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/helper-module-imports", "version": "8.0.0-alpha.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "529dc359ecd2e9728bd68801002e97b065a6a950", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-8.0.0-alpha.2.tgz", "fileCount": 11, "integrity": "sha512-bgG9fjRQN8csx98b2KF4mSntJ9+phGAgBZExxfWFkYdg8zujhR08Dsb4jUwAqCcPPA8PKLLrchIbUBUGIvkoMw==", "signatures": [{"sig": "MEQCIAQjWRwqwknbdHuKpkKSDr2Ziw1fe7y8gylGHtqNphGOAiBFXNJl47ikrBhIFoeSxdzvF5udgyT5c5rRDuyCSYCDhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101280}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/traverse": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_8.0.0-alpha.2_1691594103502_0.18563448860622578", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/helper-module-imports", "version": "7.22.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "16146307acdc40cc00c3b2c647713076464bdbf0", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.22.15.tgz", "fileCount": 11, "integrity": "sha512-0pYVBnDKZO2fnSPCrgM/6WMc7eS20Fbok+0r88fp+YtWVLZrp4CkafFGIp+W0VKw4a22sgebPT99y+FDNMdP4w==", "signatures": [{"sig": "MEUCIQCIocPpXp5U9MgtL0Sy/OQQ8d5rJpDfRsXyhPoUulZ1fwIgP5grFvHTVjqatnfSEPMD/cxyc0tIRCy1k5dBePnpe0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54353}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^7.22.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.15", "@babel/traverse": "^7.22.15"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.22.15_1693830316998_0.33784558376801876", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/helper-module-imports", "version": "8.0.0-alpha.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "525c806a96ea150b4c9b61b978881da2fd19487b", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-USmfemY3FKOz7MZ5F5Ms/SQ31ls7o9yhY2bUVJ/AEA+HKgXQ90Q4R+E6m09gpRWG/SXkCiP8mrzhLCwqANRNYg==", "signatures": [{"sig": "MEQCIEp9MHlXGU3ALsnYmzBVNuMzBlSy6KnNnRHoyIr19EJAAiBLVty7tfnsQFWWKpszoEwBlQ6wvbfwGBVR8mkU/jR0yQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54059}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/traverse": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_8.0.0-alpha.3_1695740226826_0.24385425802802896", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/helper-module-imports", "version": "8.0.0-alpha.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "fe8d0b856a64fac6d223d8a33e2bf77b0f3aa0c4", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-kwfWgpk570pAEX5D0EPZ/q2YGFlTzSMa8WAw9Q2R5/141n3tt6LFJpy1Uda4j3ytsxT4728VRAMl/RAmqS4XZw==", "signatures": [{"sig": "MEYCIQCy7gR4s3OVafstzXD8osZ6DC45ROu8OMYnh0gzrvMtyAIhAPp+yNjrPPVnjFEKplweBJfeAtJ5LK4HFO8y8UCry/7R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54059}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/traverse": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_8.0.0-alpha.4_1697076387500_0.9580218359522739", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/helper-module-imports", "version": "8.0.0-alpha.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "aa17a8a355405eb5c59ca21f3511d85d791809e5", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-+J5n9Eph0EaNGGAl3gRUmEMyxPf6eieTBZXGr16q0pxP7cSAE+LgXOE+WOZAhZKikxwwomQYgaSgTLjfugzKcQ==", "signatures": [{"sig": "MEYCIQC4T1U+Vu5a//Diwk125tVIbBwHu7b/887jDOQDUp3/wQIhAMpUJ04UIOIijT51N61PLbzUebgtexK5V8+Xwcpbkhi+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54059}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/traverse": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_8.0.0-alpha.5_1702307945125_0.21452778445912157", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/helper-module-imports", "version": "8.0.0-alpha.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "06d0b7bf90c378435d1ee9796b14e5ad15b85c7f", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-/BZ4df4w4904Rp/ZCvjziO2I29uHmLgttC5OFxla5AjNu9eaINhSLSWGYK/4PhY3zCa2cc7an/BjS59jrzmjMA==", "signatures": [{"sig": "MEUCIQCE3gFDwx+BI9ziPSZD7kYjAobmj05xpTTv8G/5qsUaEQIgaSO2hR6xwy1ZgHaqXtgpbENY4CuOpK588JjDHuhe2qI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54059}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/traverse": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_8.0.0-alpha.6_1706285655641_0.9619315027276436", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/helper-module-imports", "version": "8.0.0-alpha.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "47d7cc07c35bd829c082957a6602598087e8ab8a", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-sSYbGhu48TGKtXvH1ymrnZIC/8sxyS8p7VmCNwXjU30sU4qJuk6f3UD8AZnKYoYo2beHYPYm+QZOG8w+/tu5Pw==", "signatures": [{"sig": "MEYCIQC7z5E+7G0L1AT+3Ds/36xtyDr5SQEqMfNIhftrQ4iK5wIhAJ6Y4mI7vN+foGuLjo7ro0IEunM6ZDjnA2QF0qD05R35", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54059}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/traverse": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_8.0.0-alpha.7_1709129106319_0.6653896546755851", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/helper-module-imports", "version": "7.24.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "961ea2c12aad6cfc75b8c396c81608a08283027b", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.24.1.tgz", "fileCount": 11, "integrity": "sha512-HfEWzysMyOa7xI5uQHc/OcZf67/jc+xe/RZlznWQHhbb8Pg1SkRdbK4yEi61aY8wxQA7PkSfoojtLQP/Kpe3og==", "signatures": [{"sig": "MEUCIQCdcWJQflXKPhQdLCBS7spi3/t5Zxpte8cjH1lZUOqvLwIgWGvf3DdnwYeCWOfVMdZc9Dpv5QrJy4KpTGg86zL+KHw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63641}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1", "@babel/traverse": "^7.24.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.24.1_1710841656649_0.21747189631528596", "host": "s3://npm-registry-packages"}}, "7.24.3": {"name": "@babel/helper-module-imports", "version": "7.24.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@7.24.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "6ac476e6d168c7c23ff3ba3cf4f7841d46ac8128", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.24.3.tgz", "fileCount": 11, "integrity": "sha512-viKb0F9f2s0BCS22QSF308z/+1YWKV/76mwt61NBzS5izMzDPwdq1pTrzf+Li3npBWX9KdQbkeCt1jSAM7lZqg==", "signatures": [{"sig": "MEUCIDOWEYji10CT0ZGqhxXdLvyftY6CPb3/yxNfU3r6uKmEAiEA+R1SIjBECx+2uyn2ZT2Bzoy53GRPk1DPCTM+PLvo75Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63796}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.3", "@babel/traverse": "^7.24.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.24.3_1710936535017_0.7797760509524578", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/helper-module-imports", "version": "8.0.0-alpha.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "6a7b67e5ff9948537a42c58ee056a298f24d7e0d", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-<PERSON><PERSON>+ZGFSleDKEY2+ZosWvG3Wuec5zzvGEfKeJa6Z3nL0YUDkcHRkxEKXs6wbMEl0aSz4l5wOEY6Pt2juedLhSQ==", "signatures": [{"sig": "MEUCIQC8VwMhEobsQLJnL2EQ37Bd8QJ/ru/QlJ13nfxASbK5KQIgSU0zl+CmrZthH4fWsh+PtfiIBvg4UCEv4rYq2+vhvGs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63726}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/traverse": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_8.0.0-alpha.8_1712236798297_0.7949338573008042", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/helper-module-imports", "version": "7.24.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "65e54ffceed6a268dc4ce11f0433b82cfff57852", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.24.6.tgz", "fileCount": 11, "integrity": "sha512-a26dmxFJBF62rRO9mmpgrfTLsAuyHk4e1hKTUkD/fcMfynt8gvEKwQPQDVxWhca8dHoDck+55DFt42zV0QMw5g==", "signatures": [{"sig": "MEQCIAkNvCA6GuLHIzTHmT5Q23UXS7pTC88xSphVDqn5rHAvAiBWbqrKiZJtaXT9md2Qmb9Yd6aNc3EDFTTa5thHMMZgsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63796}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6", "@babel/traverse": "^7.24.6"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.24.6_1716553481256_0.6711684941659142", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/helper-module-imports", "version": "8.0.0-alpha.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "cef55586759e4f8a032d3eb4b0251f2ae3ef39ac", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-8.0.0-alpha.9.tgz", "fileCount": 6, "integrity": "sha512-7+jwbh1dV/PCuj9t0V/EAPwI7PFrtzapY7aDfxUvk5WwW5XsY+PZ1QHwkOVeQIXwT/gOhLeU3hWbVqqfI5I6Uw==", "signatures": [{"sig": "MEYCIQCjdH0Y7KYv127WRlU3t9u9tKLFf+LvvIAmySMyPBeNBQIhAJN36dySUBo9Mji45CZngn45PgAiXcqp2uOsqofSsZvg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70096}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.9", "@babel/traverse": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_8.0.0-alpha.9_1717423495145_0.9390434570668496", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/helper-module-imports", "version": "8.0.0-alpha.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "f8707c18159aaa982ff17549607fd89599ffe85b", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-8.0.0-alpha.10.tgz", "fileCount": 6, "integrity": "sha512-E3whcl1trmKLBXobEuQuhUZL3DIjVXol5hw3LAugBtbbe4b0ZiWWuKvkojvK2YC5q8tcs9gafhHWUaIkd3Pc2g==", "signatures": [{"sig": "MEUCIDCMeCbPoeuFxW9tmeJm/u/k4IHL699+KE6IRSuCLI8dAiEAvwzcMuDF69ofcfd7fLHabknBGfSM1JnvTsqylpFE3/4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70100}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.10", "@babel/traverse": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_8.0.0-alpha.10_1717500031590_0.628190037176843", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/helper-module-imports", "version": "7.24.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "f2f980392de5b84c3328fc71d38bd81bbb83042b", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.24.7.tgz", "fileCount": 11, "integrity": "sha512-8AyH3C+74cgCVVXow/myrynrAGv+nTVg5vKu2nZph9x7RcRwzmh0VFallJuFTZ9mx6u4eSdXZfcOzSqTUm0HCA==", "signatures": [{"sig": "MEQCIFUb/eFrv4flJVZjU/GoQd4zsoQ/OmInuqMa9XpMlCazAiBrolOg2/fvZC+ln88gf7uS0s/QdVXqUgnl1u7cSm65ug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63715}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^7.24.7", "@babel/traverse": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.24.7_1717593345972_0.5905276526606829", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/helper-module-imports", "version": "8.0.0-alpha.11", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "54762c5b4560f9759292b9a1854f5a3dc405a3ed", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-8.0.0-alpha.11.tgz", "fileCount": 6, "integrity": "sha512-cHqPwaYSzgfr6x5XWTjoE/1cbsJzPpv5dj/69UV5q1QOLf6nYBGxSvq3lTdl7YTB+ey9T7tFihU8UvdCeCTkqA==", "signatures": [{"sig": "MEYCIQCLHhsTq/Uc/HMla1J2PhjexlE1ZHckUQux2qqo+iulRQIhAL8r0EInNuccXEgpTZHXZewq7tubQ5+BSsVePh5XFv53", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70100}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.11", "@babel/traverse": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_8.0.0-alpha.11_1717751756247_0.28275281590663814", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/helper-module-imports", "version": "8.0.0-alpha.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "a4d2335fe2a8e73996648eafd5e2dc0f772eeb34", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-8.0.0-alpha.12.tgz", "fileCount": 6, "integrity": "sha512-kN5SKYng9RE/przIe7ir4b22QyuI2pg/wmyyI0UV9/lp693P4ivXxWxpVjWgYCkxGNbkZIqaMeRcq+gItCEMtw==", "signatures": [{"sig": "MEUCIFAM1FbOckHQOYWJCSowVSWqX+bN6VqlMNZAInpA0m98AiEAxkG03Z3GO+4i7buTZOJQGIzNLq1j0lJ2VcEmkJYOXpY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70099}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.12", "@babel/traverse": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_8.0.0-alpha.12_1722015228998_0.33617088758104674", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/helper-module-imports", "version": "7.25.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "dba00d9523539152906ba49263e36d7261040472", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.25.7.tgz", "fileCount": 13, "integrity": "sha512-o0xCgpNmRohmnoWKQ0Ij8IdddjyBFE4T2kagL/x6M3+4zUgc+4qTOUBoNe4XxDskt1HPKO007ZPiMgLDq2s7Kw==", "signatures": [{"sig": "MEUCIDJQbAitfXCoZPHBVq6jbUdQkJE0w0E16VbNatQcpT0SAiEAyCi2GNYXWixPvNx3rhlKbD8dw/vzo3TY7MltHHVSXmc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122098}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^7.25.7", "@babel/traverse": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.25.7_1727882116531_0.3014432467612833", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/helper-module-imports", "version": "7.25.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "e7f8d20602ebdbf9ebbea0a0751fb0f2a4141715", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.25.9.tgz", "fileCount": 11, "integrity": "sha512-tnUA4RsrmflIM6W6RFTLFSXITtl0wKjgpnLgXyowocVPrbYrLUXSBXDgTs8BlbmIzIdlBySRQjINYs2BAkiLtw==", "signatures": [{"sig": "MEYCIQCxEwsLcXi+nEagoFA3Fl5kYDV/igwgfnGIf85Id5i5iAIhAMHY8kKSePkflZQCBIRzcWisLm3+tOmFcDB2XtReEhsM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63714}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^7.25.9", "@babel/traverse": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.25.9_1729610492090_0.8188044671151649", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/helper-module-imports", "version": "8.0.0-alpha.13", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "924807343af7251b934d67ea66a6677d220105db", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-pacAfF5ftcR5Uhx/VqZmeR7FljWv6BAHtkKWdepkhqVJlrkFXCUd4pCDf+ptdbYWG+nUzRVn1r8kMTrWasmNEA==", "signatures": [{"sig": "MEQCIGJdMcFLrVERb8mY13K7NBZrx0UvwW1lmXFKYwKgefovAiAqNpM1s0NRFLyngg7+TQOHiy0PvD/rHk1aUOfHc0KtVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70099}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.13", "@babel/traverse": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_8.0.0-alpha.13_1729864472695_0.0004273252853579379", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/helper-module-imports", "version": "8.0.0-alpha.14", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "c003625cdcda67b2807b1f269e13fe5d7cb02818", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-U/A0mg3WtM6363WrKCki8UHAeHCvDvp5kR+yRi9lD3aT3GM5tOVmLECkWA8d1DTUqV7zKTPlvpkqLBzj8HxmIQ==", "signatures": [{"sig": "MEUCIQCsEZshObRYT6Zvgju8/sVW7M9OALmy2Cyd0l8lV1Bk8QIgcLbxnGP4g/U/42NJAnaeHW2AISanyc4/GabaMFNhm7I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70099}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.14", "@babel/traverse": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_8.0.0-alpha.14_1733504062572_0.7518156086184562", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/helper-module-imports", "version": "8.0.0-alpha.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "f56e0ee800f49c336f6c959ecdcd3ce5cbbd417b", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-PK1YdxAaGx3J9AwDmF+2kXW5jRVN5YiGxgZYrP5X34SKwId3FDS5f3TuP0zM9dTB30TQ+belUewZqA1cjuNuzQ==", "signatures": [{"sig": "MEYCIQDK+8gQQneP51BtXvHwx5SjpQvuQcTZslCqttrrd3XpJQIhALMTym4IT94hEXwD/S1keRbBWEjxdSOf+uyWde4riPSl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70099}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.15", "@babel/traverse": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_8.0.0-alpha.15_1736529890590_0.46705671922854", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/helper-module-imports", "version": "8.0.0-alpha.16", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "185e44b477248aba89c601ee0d939f36a72249e0", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-l0b5ekcKjPl9GZSube767pfwdwtDIfyn3OWM/lv/5l4Q1AQmAuCVoqlnLI12zn9e+b+h0LyYuJxtYyvRiILRNQ==", "signatures": [{"sig": "MEUCIQDvvEyoFxoM110Q9YHohaCCKBSuJ4TEVFcr7R8EKflY+wIgWKNnRCy1y4JnGkCGOfk6ACo809I+scw42kEiq/OmmHQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 70099}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.16", "@babel/traverse": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_8.0.0-alpha.16_1739534367871_0.6910747242122512", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/helper-module-imports", "version": "8.0.0-alpha.17", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "d8b7e18aef00e20ab4362b350d97f48d418a86e1", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-7TMB7IbU0tF0x3PlBO128X0QpIP/9J+EYdwBLXR+3Ul9oLix9zwyr3gcvjInmrr7TO7s0ZK5XooxMTlqyu0h+g==", "signatures": [{"sig": "MEYCIQDpVMXjLlNWBD7/ScIz4p02Sotcmh92WFGIhAJ1HFN0nwIhAK9RuLanI3rwXyfJXOZnL/KvIcdtq+VjByEYaNTUHUas", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 70099}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-alpha.17", "@babel/traverse": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_8.0.0-alpha.17_1741717520076_0.5997901492671136", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/helper-module-imports", "version": "7.27.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "7ef769a323e2655e126673bb6d2d6913bbead204", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "fileCount": 11, "integrity": "sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==", "signatures": [{"sig": "MEUCIQCD0+XYSrqOw9D0M+u8D2TZtFSBu1AU/JVHbUgYdaWgogIgSsjQZNbDn2btFMDcIMVmSCOZ7mJUcnKJH442GnusE2g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 63724}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^7.27.1", "@babel/traverse": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_7.27.1_1746025756021_0.2106155216273784", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/helper-module-imports", "version": "8.0.0-beta.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-module-imports@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "dist": {"shasum": "5aed920e58033232e605832cbbc2999897822dd4", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-RSfMoF9LXSgNPSLxl0/y+TtjFCPlK49ricJogUvPEdyQVjiK3Vh6+W2WFeA9Cv9RyImu+Lb6NnSLaUCOborpgQ==", "signatures": [{"sig": "MEYCIQDcuZpzqowh/V7oM4ZSluUcqcI/INMHwnPd2JZvWgKhuQIhALCMO6uDDwjT16oH67PSyHtNf5h1yxOKgISEyaYaBebP", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 70095}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "directories": {}, "dependencies": {"@babel/types": "^8.0.0-beta.0", "@babel/traverse": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-module-imports_8.0.0-beta.0_1748620292587_0.004008845626423341", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/helper-module-imports", "version": "8.0.0-beta.1", "description": "Babel helper functions for inserting module loads", "author": "The Babel Team (https://babel.dev/team)", "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-module-imports"}, "main": "./lib/index.js", "dependencies": {"@babel/traverse": "^8.0.0-beta.1", "@babel/types": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/helper-module-imports@8.0.0-beta.1", "dist": {"shasum": "3b62c6a298d16ede2c339828eeecd957c01bd910", "integrity": "sha512-t6TX7NwONHsI4leUj/fKDp+4aZfC42QZtqF/PcRcFiJ6GU3WyoScfzDNHJjVMWNXBMyudL4x6nSfmVccwZ0aUQ==", "tarball": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 70095, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDYp6wgQIJ2M5B7wZ0wTtu70FiU1kXpYPqGjVe9T7pqagIgRTiMQ61xn+rsSzJOUPms10Jn7kGUEM2oEHxuMwYmLkg="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/helper-module-imports_8.0.0-beta.1_1751447076199_0.7487311688018559"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:35:05.745Z", "modified": "2025-07-02T09:04:36.597Z", "7.0.0-beta.4": "2017-10-30T18:35:05.745Z", "7.0.0-beta.5": "2017-10-30T20:56:46.143Z", "7.0.0-beta.31": "2017-11-03T20:03:42.307Z", "7.0.0-beta.32": "2017-11-12T13:33:34.509Z", "7.0.0-beta.33": "2017-12-01T14:28:40.908Z", "7.0.0-beta.34": "2017-12-02T14:39:39.828Z", "7.0.0-beta.35": "2017-12-14T21:48:02.672Z", "7.0.0-beta.36": "2017-12-25T19:05:01.204Z", "7.0.0-beta.37": "2018-01-08T16:02:43.782Z", "7.0.0-beta.38": "2018-01-17T16:32:11.978Z", "7.0.0-beta.39": "2018-01-30T20:27:44.777Z", "7.0.0-beta.40": "2018-02-12T16:41:56.480Z", "7.0.0-beta.41": "2018-03-14T16:25:44.688Z", "7.0.0-beta.42": "2018-03-15T20:50:19.442Z", "7.0.0-beta.43": "2018-04-02T16:48:08.878Z", "7.0.0-beta.44": "2018-04-02T22:19:50.823Z", "7.0.0-beta.45": "2018-04-23T01:55:52.894Z", "7.0.0-beta.46": "2018-04-23T04:30:17.279Z", "7.0.0-beta.47": "2018-05-15T00:07:43.303Z", "7.0.0-beta.48": "2018-05-24T19:21:03.061Z", "7.0.0-beta.49": "2018-05-25T16:00:46.868Z", "7.0.0-beta.50": "2018-06-12T19:46:53.846Z", "7.0.0-beta.51": "2018-06-12T21:19:18.832Z", "7.0.0-beta.52": "2018-07-06T00:59:13.615Z", "7.0.0-beta.53": "2018-07-11T13:40:03.218Z", "7.0.0-beta.54": "2018-07-16T17:59:53.214Z", "7.0.0-beta.55": "2018-07-28T22:06:57.449Z", "7.0.0-beta.56": "2018-08-04T01:03:49.527Z", "7.0.0-rc.0": "2018-08-09T15:56:55.540Z", "7.0.0-rc.1": "2018-08-09T20:06:48.953Z", "7.0.0-rc.2": "2018-08-21T19:22:51.081Z", "7.0.0-rc.3": "2018-08-24T18:06:46.037Z", "7.0.0-rc.4": "2018-08-27T16:42:56.934Z", "7.0.0": "2018-08-27T21:42:09.502Z", "7.7.0": "2019-11-05T10:53:29.461Z", "7.7.4": "2019-11-22T23:33:22.931Z", "7.8.0": "2020-01-12T00:16:47.402Z", "7.8.3": "2020-01-13T21:41:51.152Z", "7.10.1": "2020-05-27T22:07:52.219Z", "7.10.3": "2020-06-19T20:54:31.687Z", "7.10.4": "2020-06-30T13:12:22.756Z", "7.12.1": "2020-10-15T22:40:56.865Z", "7.12.5": "2020-11-03T22:34:28.374Z", "7.12.13": "2021-02-03T01:10:13.577Z", "7.13.12": "2021-03-22T15:47:12.590Z", "7.14.5": "2021-06-09T23:12:26.485Z", "7.15.4": "2021-09-02T21:39:34.391Z", "7.16.0": "2021-10-29T23:47:42.687Z", "7.16.7": "2021-12-31T00:22:22.580Z", "7.18.6": "2022-06-27T19:50:09.669Z", "7.21.4": "2023-03-31T09:01:56.752Z", "7.21.4-esm": "2023-04-04T14:09:35.546Z", "7.21.4-esm.1": "2023-04-04T14:21:27.752Z", "7.21.4-esm.2": "2023-04-04T14:39:28.837Z", "7.21.4-esm.3": "2023-04-04T14:56:17.663Z", "7.21.4-esm.4": "2023-04-04T15:13:29.114Z", "7.22.5": "2023-06-08T18:21:27.189Z", "8.0.0-alpha.0": "2023-07-20T14:00:04.808Z", "8.0.0-alpha.1": "2023-07-24T17:52:12.656Z", "8.0.0-alpha.2": "2023-08-09T15:15:03.836Z", "7.22.15": "2023-09-04T12:25:17.156Z", "8.0.0-alpha.3": "2023-09-26T14:57:06.988Z", "8.0.0-alpha.4": "2023-10-12T02:06:27.740Z", "8.0.0-alpha.5": "2023-12-11T15:19:05.321Z", "8.0.0-alpha.6": "2024-01-26T16:14:16.007Z", "8.0.0-alpha.7": "2024-02-28T14:05:06.472Z", "7.24.1": "2024-03-19T09:47:36.835Z", "7.24.3": "2024-03-20T12:08:55.184Z", "8.0.0-alpha.8": "2024-04-04T13:19:58.442Z", "7.24.6": "2024-05-24T12:24:41.498Z", "8.0.0-alpha.9": "2024-06-03T14:04:55.281Z", "8.0.0-alpha.10": "2024-06-04T11:20:31.742Z", "7.24.7": "2024-06-05T13:15:46.100Z", "8.0.0-alpha.11": "2024-06-07T09:15:56.402Z", "8.0.0-alpha.12": "2024-07-26T17:33:49.178Z", "7.25.7": "2024-10-02T15:15:16.775Z", "7.25.9": "2024-10-22T15:21:32.318Z", "8.0.0-alpha.13": "2024-10-25T13:54:32.918Z", "8.0.0-alpha.14": "2024-12-06T16:54:22.729Z", "8.0.0-alpha.15": "2025-01-10T17:24:50.808Z", "8.0.0-alpha.16": "2025-02-14T11:59:28.032Z", "8.0.0-alpha.17": "2025-03-11T18:25:20.359Z", "7.27.1": "2025-04-30T15:09:16.213Z", "8.0.0-beta.0": "2025-05-30T15:51:32.756Z", "8.0.0-beta.1": "2025-07-02T09:04:36.355Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-module-imports"}, "description": "Babel helper functions for inserting module loads", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}