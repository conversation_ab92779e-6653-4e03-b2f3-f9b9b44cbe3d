{"_id": "lodash.snakecase", "_rev": "19-02fb9f9acbbd04a478b9f6ca7833520c", "name": "lodash.snakecase", "description": "The lodash method `_.snakeCase` exported as a module.", "dist-tags": {"latest": "4.1.1"}, "versions": {"3.0.0": {"name": "lodash.snakecase", "version": "3.0.0", "description": "The modern build of lodash’s `_.snakeCase` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._createcompounder": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.snakecase@3.0.0", "_shasum": "5a9f2435b1c033d03c54b262f6e1d33f4bd760a6", "_from": ".", "_npmVersion": "2.3.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5a9f2435b1c033d03c54b262f6e1d33f4bd760a6", "tarball": "https://registry.npmjs.org/lodash.snakecase/-/lodash.snakecase-3.0.0.tgz", "integrity": "sha512-iDei7v8dSTFbIHNbs8Z/5qinMA/BAGD7GNmXgoLGq6g7LvQsHgom0027MaKk8EGoHl07CCmfiRrmkpI4qQO9rw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDX+xpZNp3WPdP8bQrgQcMUSRZdXFWFZnAOCaigaAd2yAIgJ5L1N4tSkjg0Mq1hopi/8YUncywpYk/d9A4utyisvvM="}]}}, "3.0.1": {"name": "lodash.snakecase", "version": "3.0.1", "description": "The modern build of lodash’s `_.snakeCase` as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://d10.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.iceddev.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://kitcambridge.be/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/lodash/lodash"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash._createcompounder": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.snakecase@3.0.1", "_shasum": "62445e048a7dbb05bb2ca13f2cc89a78945b6d40", "_from": ".", "_npmVersion": "2.3.0", "_nodeVersion": "0.10.35", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "d10", "email": "<EMAIL>"}, {"name": "kitcambridge", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "dist": {"shasum": "62445e048a7dbb05bb2ca13f2cc89a78945b6d40", "tarball": "https://registry.npmjs.org/lodash.snakecase/-/lodash.snakecase-3.0.1.tgz", "integrity": "sha512-mooQnRqEzN7kY6dO8HXgObUxXgM72MO2A3r/Ml14jSUDH36/MLMO3xg5g/rNkSU09OFgBkGZiyls7arNvU1IJg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFhGPeE9fGxkp+qX9eXRNc7w5qRfy6/rb3vl0ZWa7+tsAiBDazCQv/C10EriO+Oxsmh2Uxqf023BaO/469cCPjn14g=="}]}}, "3.1.0": {"name": "lodash.snakecase", "version": "3.1.0", "description": "The lodash method `_.snakeCase` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util", "snakecase"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash.deburr": "^3.0.0", "lodash.words": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.snakecase@3.1.0", "_shasum": "b80f5f8b38c35690e2bb08453f57108e93b409e3", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b80f5f8b38c35690e2bb08453f57108e93b409e3", "tarball": "https://registry.npmjs.org/lodash.snakecase/-/lodash.snakecase-3.1.0.tgz", "integrity": "sha512-IEyUHR4zk08jtHRO8BKLIkdyXoifnrgMZZtBCl8iH5QTmuR2xLIs4TbpYWd9lWu9T5tf+2XH2ysMM1DjSA+Zow==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICJnrjYtnNCFgAIu5d+8r5i2KtfZ/r8Gjch5CRtodbgUAiEAnu7KMSxbmmQI2r2Sd5QnDEmQspGawMA9781MeJ7kvw0="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "3.1.1": {"name": "lodash.snakecase", "version": "3.1.1", "description": "The lodash method `_.snakeCase` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash", "lodash-modularized", "stdlib", "util", "snakecase"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash.deburr": "^3.0.0", "lodash.words": "^3.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.snakecase@3.1.1", "_shasum": "c0db98e080796f0cc47b7744b168bdf8e23c27d3", "_from": ".", "_npmVersion": "2.14.15", "_nodeVersion": "5.4.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c0db98e080796f0cc47b7744b168bdf8e23c27d3", "tarball": "https://registry.npmjs.org/lodash.snakecase/-/lodash.snakecase-3.1.1.tgz", "integrity": "sha512-VBQR0JeNXR6Fp2xocaFDOJD6DI8Ir72MT5tf3UKl2aJaCwR+xspxmy8uDAmuxYRzo0+XPZyrTX667HwUNy6wiQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGRd581VYwXvDjOkTVFw0y+8s87FQA+aSFVM1TouhKE0AiAriUenC73EnpzNIbmhUcI4fzgzeQz8V+xiq88/zRreqw=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}]}, "4.0.0": {"name": "lodash.snakecase", "version": "4.0.0", "description": "The lodash method `_.snakeCase` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "snakecase"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash.deburr": "^4.0.0", "lodash.words": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.snakecase@4.0.0", "_shasum": "cba121e31f02a7620d1a227c5ee8f272ef4acd69", "_from": ".", "_npmVersion": "2.14.18", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "cba121e31f02a7620d1a227c5ee8f272ef4acd69", "tarball": "https://registry.npmjs.org/lodash.snakecase/-/lodash.snakecase-4.0.0.tgz", "integrity": "sha512-cwO6tcmrnpfp1xpr4oGKyWffjMPNi6cxo7H4UblfcJGbgzKR0CKXJ2tmOc0v4jvu8RNJqsoHgVYnPZaNrIfxTA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEgxSp0p92Tp2IJRz9Hso1jRMuh3QXGq9YCDFr5BxgXoAiB9su3vA75KdfS7nOgOt0G0o2wldNFzxFmvftiKmF1Xew=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/lodash.snakecase-4.0.0.tgz_1455615613339_0.5427748612128198"}}, "4.0.1": {"name": "lodash.snakecase", "version": "4.0.1", "description": "The lodash method `_.snakeCase` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "snakecase"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "dependencies": {"lodash.deburr": "^4.0.0", "lodash.words": "^4.0.0"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.snakecase@4.0.1", "_shasum": "bd012e5d2f93f7b58b9303e9a7fbfd5db13d6281", "_from": ".", "_npmVersion": "2.15.3", "_nodeVersion": "5.5.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "bd012e5d2f93f7b58b9303e9a7fbfd5db13d6281", "tarball": "https://registry.npmjs.org/lodash.snakecase/-/lodash.snakecase-4.0.1.tgz", "integrity": "sha512-Kq4JuZwsr5yBf3KAV8ThoKR0ake9zCJj127kVJAdIjykvSGeegvcCpGsp9E/pNbo4FKZtsNd1w5Od+7FFvS0FA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDoq7yYc7SzyhhzD4znob8C+sH8k7JuauUYb4rpVC212QIhAKqLK38pzhz70Ic97p0cXYG/Oau+YbMIwsR+VkUkYHu3"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash.snakecase-4.0.1.tgz_1460562093461_0.3072226410731673"}}, "4.1.0": {"name": "lodash.snakecase", "version": "4.1.0", "description": "The lodash method `_.snakeCase` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "snakecase"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.snakecase@4.1.0", "_shasum": "b418a5b0b30c316b2e27897c9f3a1d9761a8158b", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.2.4", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b418a5b0b30c316b2e27897c9f3a1d9761a8158b", "tarball": "https://registry.npmjs.org/lodash.snakecase/-/lodash.snakecase-4.1.0.tgz", "integrity": "sha512-zDJFv1MlVpu6lqK8hiHEEVU90cCQosLlaRAbaOVIMp1iOjsAyYB3oq9bYmRLovN1z4zkFviYPIIh4kusDi56cg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHKFLSQa1g9S5RPq8Fg9n1eHy+aj8cueqK8x+X8gN87iAiEA+0jokdggCOxBEt+xZTnzjAREEyvNo/2znXmikrQSyoQ="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/lodash.snakecase-4.1.0.tgz_1469458237335_0.5712232096120715"}}, "4.1.1": {"name": "lodash.snakecase", "version": "4.1.1", "description": "The lodash method `_.snakeCase` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "license": "MIT", "keywords": ["lodash-modularized", "snakecase"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "_id": "lodash.snakecase@4.1.1", "_shasum": "****************************************", "_from": ".", "_npmVersion": "2.15.10", "_nodeVersion": "4.4.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "****************************************", "tarball": "https://registry.npmjs.org/lodash.snakecase/-/lodash.snakecase-4.1.1.tgz", "integrity": "sha512-QZ1d4xoBHYUeuouhEq3lk3Uq7ldgyFXGBhg04+oRLnIz8o9T65Eh+8YdroUwn846zchkA9yDsDl5CVVaV2nqYw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCYHX4F+wwogvgBlR61UdPNb/Cs4avMCrvIcTYHT40WCgIhAOJVAGkI+jtxrWlp5kfzbGzqbeKFbVFJTMbkPVnqSMrq"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}, {"name": "phated", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/lodash.snakecase-4.1.1.tgz_1471110270302_0.40282662957906723"}}}, "readme": "# lodash.snakecase v4.1.1\n\nThe [lodash](https://lodash.com/) method `_.snakeCase` exported as a [Node.js](https://nodejs.org/) module.\n\n## Installation\n\nUsing npm:\n```bash\n$ {sudo -H} npm i -g npm\n$ npm i --save lodash.snakecase\n```\n\nIn Node.js:\n```js\nvar snakeCase = require('lodash.snakecase');\n```\n\nSee the [documentation](https://lodash.com/docs#snakeCase) or [package source](https://github.com/lodash/lodash/blob/4.1.1-npm-packages/lodash.snakecase) for more details.\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>ias", "email": "<EMAIL>"}], "time": {"modified": "2022-06-19T13:37:51.947Z", "created": "2015-01-26T15:30:08.059Z", "3.0.0": "2015-01-26T15:30:08.059Z", "3.0.1": "2015-02-03T17:17:43.921Z", "3.1.0": "2016-01-13T11:13:37.345Z", "3.1.1": "2016-01-26T08:56:26.521Z", "4.0.0": "2016-02-16T09:40:17.018Z", "4.0.1": "2016-04-13T15:41:36.231Z", "4.1.0": "2016-07-25T14:50:40.583Z", "4.1.1": "2016-08-13T17:44:30.535Z"}, "homepage": "https://lodash.com/", "keywords": ["lodash-modularized", "snakecase"], "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/phated"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://mathiasbynens.be/"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "license": "MIT", "readmeFilename": "README.md"}