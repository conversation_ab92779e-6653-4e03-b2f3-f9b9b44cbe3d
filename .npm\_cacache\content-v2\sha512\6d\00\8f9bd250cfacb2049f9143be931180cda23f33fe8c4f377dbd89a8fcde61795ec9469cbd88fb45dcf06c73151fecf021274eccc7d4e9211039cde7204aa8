{"_id": "istanbul-lib-report", "_rev": "44-cf6c6404bda514e0aa4d5fcd0bba7a2e", "name": "istanbul-lib-report", "description": "Base reporting library for istanbul", "dist-tags": {"latest": "3.0.1"}, "versions": {"1.0.0-alpha.0": {"name": "istanbul-lib-report", "version": "1.0.0-alpha.0", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"fast": "mocha test/", "pretest": "jshint index.js lib/ test/", "test": "istanbul cover --include-all-sources --print=both _mocha --  test/", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "dependencies": {"async": "^1.4.2", "istanbul-lib-coverage": "^1.0.0-alpha", "mkdirp": "^0.5.1", "path-parse": "^1.0.5", "rimraf": "^2.4.3", "supports-color": "^3.1.2"}, "devDependencies": {"chai": "^3.0.0", "coveralls": "^2.11.4", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^2.3.3"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-report/issues"}, "homepage": "https://github.com/istanbuljs/istanbul-lib-report", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbul-lib-report.git"}, "keywords": ["istanbul", "report", "api", "lib"], "gitHead": "48d8403a151d67a013c4388f8c36a666a3033af4", "_id": "istanbul-lib-report@1.0.0-alpha.0", "_shasum": "a971b930d5e3b338f547deee97a76715746209f0", "_from": ".", "_npmVersion": "2.12.0", "_nodeVersion": "0.10.26", "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "dist": {"shasum": "a971b930d5e3b338f547deee97a76715746209f0", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-1.0.0-alpha.0.tgz", "integrity": "sha512-LkhJBkxRZaAdxaGwULl08iT+HnQdF/xzjWFzztMWU3Peuy3by9TYM0dr+pXNVHU9KAdJY0+4PuE019RCVYWADA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDPmxVF8R9njHbbQYmesgpto6DAl760P7uQZ408JTfGMQIgcqtFgP48d2IfiHWw1VSdwN7t8yAy5IdrbPChRD+8A18="}]}, "directories": {}}, "1.0.0-alpha.1": {"name": "istanbul-lib-report", "version": "1.0.0-alpha.1", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"fast": "mocha test/", "pretest": "jshint index.js lib/ test/", "test": "istanbul cover --include-all-sources --print=both _mocha --  test/", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "dependencies": {"async": "^1.4.2", "istanbul-lib-coverage": "^1.0.0-alpha", "mkdirp": "^0.5.1", "path-parse": "^1.0.5", "rimraf": "^2.4.3", "supports-color": "^3.1.2"}, "devDependencies": {"chai": "^3.0.0", "coveralls": "^2.11.4", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^2.3.3"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-report/issues"}, "homepage": "https://github.com/istanbuljs/istanbul-lib-report", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbul-lib-report.git"}, "keywords": ["istanbul", "report", "api", "lib"], "gitHead": "4f426c4f095dfe6f22e70bc0e7dda29a3ac59a98", "_id": "istanbul-lib-report@1.0.0-alpha.1", "_shasum": "ef62e4544bb072c228e05c12339896abb35693cf", "_from": ".", "_npmVersion": "2.12.0", "_nodeVersion": "0.10.26", "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "dist": {"shasum": "ef62e4544bb072c228e05c12339896abb35693cf", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-1.0.0-alpha.1.tgz", "integrity": "sha512-mEY0+iXdsVb1+sTe5CiJlJMyVPKu7VvgSqLPtqJmFBS/OAem1zi2gKt0U5XjYeVPoRqY6tnV7ZzvQXNDONRwhg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCHq46EgwVlHnq/vwzY3iCc6oPPaqUulsdA+KofFOxpqAIgcHrlUd99HDdWUhh0QxOyCVXvSrLwlujnRozVPHiow9Q="}]}, "directories": {}}, "1.0.0-alpha.2": {"name": "istanbul-lib-report", "version": "1.0.0-alpha.2", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"fast": "mocha test/", "pretest": "jshint index.js lib/ test/", "test": "istanbul cover -x 'docs/**' --include-all-sources --print=both _mocha --  test/", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "dependencies": {"async": "^1.4.2", "istanbul-lib-coverage": "^1.0.0-alpha", "mkdirp": "^0.5.1", "path-parse": "^1.0.5", "rimraf": "^2.4.3", "supports-color": "^3.1.2"}, "devDependencies": {"chai": "^3.0.0", "coveralls": "^2.11.4", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^2.3.3"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-report/issues"}, "homepage": "https://github.com/istanbuljs/istanbul-lib-report", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbul-lib-report.git"}, "keywords": ["istanbul", "report", "api", "lib"], "gitHead": "1c6eb1b63b4b82acf89902f3413e22659597c6f5", "_id": "istanbul-lib-report@1.0.0-alpha.2", "_shasum": "edec8e8d38b9840c1ba46be6f3ffb3a25ad8a41a", "_from": ".", "_npmVersion": "2.12.0", "_nodeVersion": "0.10.26", "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "dist": {"shasum": "edec8e8d38b9840c1ba46be6f3ffb3a25ad8a41a", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-1.0.0-alpha.2.tgz", "integrity": "sha512-/h5ck2o5pHYxYglDcHcT0N6N0rhA2FX4XwQbco9RufNzbWXqYjURBtsBMghiHWnd9+beKrh/Fspqb7rI1l++vw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICYlGEdj16vgGqNgwLM3Pp04enjHuGFaV2ul7vOoHro8AiEA+9GF9Jt+JABGrd1uWbQmhrGzCk/cPQ+LPDXBic9F3DY="}]}, "directories": {}}, "1.0.0-alpha.3": {"name": "istanbul-lib-report", "version": "1.0.0-alpha.3", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"fast": "mocha test/", "pretest": "jshint index.js lib/ test/", "test": "istanbul cover -x 'docs/**' --include-all-sources --print=both _mocha --  test/", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "dependencies": {"async": "^1.4.2", "istanbul-lib-coverage": "^1.0.0-alpha", "mkdirp": "^0.5.1", "path-parse": "^1.0.5", "rimraf": "^2.4.3", "supports-color": "^3.1.2"}, "devDependencies": {"chai": "^3.0.0", "coveralls": "^2.11.4", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^2.3.3"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-report/issues"}, "homepage": "https://github.com/istanbuljs/istanbul-lib-report", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbul-lib-report.git"}, "keywords": ["istanbul", "report", "api", "lib"], "gitHead": "f41bab23a361810d42dc9ede5ed51c6af497a50c", "_id": "istanbul-lib-report@1.0.0-alpha.3", "_shasum": "32d5f6ec7f33ca3a602209e278b2e6ff143498af", "_from": ".", "_npmVersion": "2.12.0", "_nodeVersion": "0.10.26", "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "dist": {"shasum": "32d5f6ec7f33ca3a602209e278b2e6ff143498af", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-1.0.0-alpha.3.tgz", "integrity": "sha512-mHoUzYCFv/921VJVTh1NltT0VCJMBc8Ijvh7X52YdZdmT5eY2/NBhlGFcJnWNReerAwrKIXNgCOYUpzYLBUH1w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCcgNzXH+l3irtBpGIyvXSE9/HUbQ/uIWoHs0w7J3C71QIhAO0q6/4RfbmKrXGB6qTsYWGnmxIHyQH3+ICWBh9eoVW2"}]}, "directories": {}}, "1.0.0": {"name": "istanbul-lib-report", "version": "1.0.0", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"pretest": "jshint index.js lib/ test/", "test": "mocha"}, "dependencies": {"istanbul-lib-coverage": "^1.0.2", "mkdirp": "^0.5.1", "path-parse": "^1.0.5", "supports-color": "^3.1.2"}, "devDependencies": {"chai": "^3.0.0", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^3.2.0", "rimraf": "^2.4.3"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-report/issues"}, "homepage": "https://github.com/istanbuljs/istanbul-lib-report", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbul-lib-report.git"}, "keywords": ["istanbul", "report", "api", "lib"], "_id": "istanbul-lib-report@1.0.0", "_shasum": "d83dac7f26566b521585569367fe84ccfc7aaecb", "_from": ".", "_npmVersion": "4.4.1", "_nodeVersion": "6.9.5", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "d83dac7f26566b521585569367fe84ccfc7aaecb", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-1.0.0.tgz", "integrity": "sha512-y+UOPFqCvEfBgAzdRYlbWlEVRpiWiWuZ5Odr58UhvG4lz7Z2vLdd5TuQw3/rLH4goG3/GeLPqfljzpOvyEyaZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG873W8WcSXsafWQtGRkcSKHMPC/IGQkiub7XOb9HnKVAiBdgxJd6w0c06ivLqOkgPZWii/8NQEKsGtIq9oSjkyfYg=="}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/istanbul-lib-report-1.0.0.tgz_1490593887156_0.6450473961886019"}, "directories": {}}, "1.1.0": {"name": "istanbul-lib-report", "version": "1.1.0", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "files": ["lib", "index.js"], "scripts": {"pretest": "jshint index.js lib/ test/", "test": "mocha"}, "dependencies": {"istanbul-lib-coverage": "^1.1.0", "mkdirp": "^0.5.1", "path-parse": "^1.0.5", "supports-color": "^3.1.2"}, "devDependencies": {"chai": "^3.0.0", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^3.2.0", "rimraf": "^2.6.1"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-report/issues"}, "homepage": "https://github.com/istanbuljs/istanbul-lib-report", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbul-lib-report.git"}, "keywords": ["istanbul", "report", "api", "lib"], "_id": "istanbul-lib-report@1.1.0", "_shasum": "444c4ecca9afa93cf584f56b10f195bf768c0770", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.1.0", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "444c4ecca9afa93cf584f56b10f195bf768c0770", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-1.1.0.tgz", "integrity": "sha512-fEymNrzqKwahKTwSWbVTuCUaTQlLXRN0ViUcofiupC4Yn5XPbUk1uZKW1xU4y9YCUTVjx9huU/x58uQcgtxTFQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGVCMfgeNgV3l4dyncpFc2BexDuo7ULeUbO9dNP57eYeAiBYPhRHQ6YezlC9ikYZKcXQDOj5EuKYZTUMSpRZYInLmw=="}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/istanbul-lib-report-1.1.0.tgz_1493442009637_0.3394668174441904"}, "directories": {}}, "1.1.1": {"name": "istanbul-lib-report", "version": "1.1.1", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "files": ["lib", "index.js"], "scripts": {"pretest": "jshint index.js lib/ test/", "test": "mocha"}, "dependencies": {"istanbul-lib-coverage": "^1.1.1", "mkdirp": "^0.5.1", "path-parse": "^1.0.5", "supports-color": "^3.1.2"}, "devDependencies": {"chai": "^3.0.0", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^3.2.0", "rimraf": "^2.6.1"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "report", "api", "lib"], "_id": "istanbul-lib-report@1.1.1", "_npmVersion": "5.0.0", "_nodeVersion": "7.1.0", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-tvF+YmCmH4thnez6JFX06ujIA19WPa9YUiwjc1uALF2cv5dmE3It8b5I8Ob7FHJ70H9Y5yF+TDkVa/mcADuw1Q==", "shasum": "f0e55f56655ffa34222080b7a0cd4760e1405fc9", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-1.1.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGV+m9zLYKG/yvVHvz3kLtWBXY/vMyzhixMhVpnnShMfAiEAztngOx7+aq4mRVywWHWYsPDC8o9Nbjt7gmldKU1ZHuE="}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-lib-report-1.1.1.tgz_1495919581346_0.9511885098181665"}, "directories": {}}, "1.1.2": {"name": "istanbul-lib-report", "version": "1.1.2", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "files": ["lib", "index.js"], "scripts": {"pretest": "jshint index.js lib/ test/", "test": "mocha"}, "dependencies": {"istanbul-lib-coverage": "^1.1.1", "mkdirp": "^0.5.1", "path-parse": "^1.0.5", "supports-color": "^3.1.2"}, "devDependencies": {"chai": "^3.0.0", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^3.2.0", "rimraf": "^2.6.1"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "report", "api", "lib"], "_id": "istanbul-lib-report@1.1.2", "_npmVersion": "5.5.1", "_nodeVersion": "8.7.0", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-UTv4VGx+HZivJQwAo1wnRwe1KTvFpfi/NYwN7DcsrdzMXwpRT/Yb6r4SBPoHWj4VuQPakR32g4PUUeyKkdDkBA==", "shasum": "922be27c13b9511b979bd1587359f69798c1d425", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-1.1.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDR+5A7f/pjAiipZvZrWiFp9KG7knLJau4gkZ6VM6MWkAiAt3qH9qsSOrHtZrPnNXUrlwjQ/r9Qx7jN1A3oNewArbg=="}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-lib-report-1.1.2.tgz_1508612368727_0.7462781711947173"}, "directories": {}}, "1.1.3": {"name": "istanbul-lib-report", "version": "1.1.3", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "files": ["lib", "index.js"], "scripts": {"pretest": "jshint index.js lib/ test/", "test": "mocha"}, "dependencies": {"istanbul-lib-coverage": "^1.1.2", "mkdirp": "^0.5.1", "path-parse": "^1.0.5", "supports-color": "^3.1.2"}, "devDependencies": {"chai": "^3.0.0", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^3.2.0", "rimraf": "^2.6.1"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "report", "api", "lib"], "_id": "istanbul-lib-report@1.1.3", "_npmVersion": "5.4.2", "_nodeVersion": "8.8.1", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-D4jVbMDtT2dPmloPJS/rmeP626N5Pr3Rp+SovrPn1+zPChGHcggd/0sL29jnbm4oK9W0wHjCRsdch9oLd7cm6g==", "shasum": "2df12188c0fa77990c0d2176d2d0ba3394188259", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-1.1.3.tgz", "fileCount": 12, "unpackedSize": 33392, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBEUn3jDIokCrBs7C5fBJoc+xCGxUNJ/udPrFHm9tlFeAiBuROgoTbw9owLFZTAcb9YuQnGY/WO/rdzkyW7/8MFQgA=="}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-lib-report_1.1.3_1518500921481_0.8517524607564233"}, "_hasShrinkwrap": false}, "1.1.4": {"name": "istanbul-lib-report", "version": "1.1.4", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "files": ["lib", "index.js"], "scripts": {"pretest": "jshint index.js lib/ test/", "test": "mocha"}, "dependencies": {"istanbul-lib-coverage": "^1.2.0", "mkdirp": "^0.5.1", "path-parse": "^1.0.5", "supports-color": "^3.1.2"}, "devDependencies": {"chai": "^3.0.0", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^3.2.0", "rimraf": "^2.6.1"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "report", "api", "lib"], "readme": "istanbul-lib-report\n===================\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-lib-report.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-report.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-report)\n\nCore reporting utilities for istanbul. Docs to follow.\n\n", "readmeFilename": "README.md", "_id": "istanbul-lib-report@1.1.4", "_npmVersion": "5.4.2", "_nodeVersion": "8.8.1", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Azqvq5tT0U09nrncK3q82e/Zjkxa4tkFZv7E6VcqP0QCPn6oNljDPfrZEC/umNXds2t7b8sRJfs6Kmpzt8m2kA==", "shasum": "e886cdf505c4ebbd8e099e4396a90d0a28e2acb5", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-1.1.4.tgz", "fileCount": 12, "unpackedSize": 33606, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEANr6w51+U6G9zm6ajrVtzyG82o2m94czcBBT8KLDKDAiAptWWuf41hzc026wKNQIM/fUA1+qpesC7OiU4Kpw8fAQ=="}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-lib-report_1.1.4_1520188978712_0.2509076110192774"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "istanbul-lib-report", "version": "2.0.0", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "files": ["lib", "index.js"], "scripts": {"pretest": "jshint index.js lib/ test/", "test": "mocha"}, "dependencies": {"istanbul-lib-coverage": "^2.0.0", "make-dir": "^1.3.0", "supports-color": "^5.4.0"}, "devDependencies": {"chai": "^4.1.2", "istanbul": "^0.4.5", "jshint": "^2.9.5", "mocha": "^5.2.0", "rimraf": "^2.6.2"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "report", "api", "lib"], "readme": "istanbul-lib-report\n===================\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-lib-report.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-report.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-report)\n\nCore reporting utilities for istanbul. Docs to follow.\n\n", "readmeFilename": "README.md", "_id": "istanbul-lib-report@2.0.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.2.1", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-RiELmy9oIRYUv36ITOAhVum9PUvuj6bjyXVEKEHNiD1me6qXtxfx7vSEJWnjOGk2QmYw/GRFjLXWJv3qHpLceQ==", "shasum": "99c161979ec202f4a638abd8dc77fab8f9abce53", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-2.0.0.tgz", "fileCount": 12, "unpackedSize": 34046, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbFy/JCRA9TVsSAnZWagAAGHIP/jd6o7SML0f9N2bQZUJq\nr40OL8f4ROMfuaCoYVbKzAjWOSP1xwjMm7LBO8cAZ7LWxROzuiVWOuVPwdQu\n+IoPO9/YKk4NCcPPd9R0sFQISWAoEe0ZCn0opJOW1l2vZtNnEnMDBltQbkKd\ni8Je4kYus5IE0X3AFsKIB+VsCsw+zK4yL6VJvvGW7yfAmBnW8/dJTtzLx2Z4\nBj8eLdQJdhsmIK308xbMtZjHmjhf/xY8CvOgVUtEpYT9bA7wySORd6Kzwl0M\n2vWES0fa3O7cQ9UsfY63pyUhoCs/N/SVghxlUPRpwLJHRMuBt6ZZ0otOZ5Yj\nQzGlYJfWe7cpRyB6Hzx355v3QqeOb/G/apa0PMRkX8TRDZoq1PVqvP0YClgU\nbW5LkQTGn2xaz9GUY9eo8Hf2XKCav21mI30PIriPiRnuCslqOaKCZpbLx326\nDtnQJLRNJXH07HBvu7Hxsq7jC0YAe0ncgJHLqd+QpnQq7Jd5OWDOhQupbhxx\ngnFF5JvfKriUGedPqOyBZb6jQ4pAjOM43EcszYP92xBvYMCVHhgd9VZ2AC92\nHFFLGNo/6FhkKPJxjSeVaqXBFKRKZ6RSZ3K9QfqBdFy9Cvq4+1E2uJxDz1Nl\nqfuLoaZV3rJmEj9CH7ZBSYR2l80VMZ5M5qENOIpL6erVwU1UzDP3iGJTU+7N\nDq1e\r\n=FjhQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCQHQycAo53KFwzmKY67DJumn47Lptw7t142rSDj5oYrAIgUbQjoPpLc2qmLj3OrMZHrJ72nPd91GIwntMdINHctyQ="}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-lib-report_2.0.0_1528246217515_0.9823928073041641"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "istanbul-lib-report", "version": "2.0.1", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "files": ["lib", "index.js"], "scripts": {"pretest": "jshint index.js lib/ test/", "test": "mocha"}, "dependencies": {"istanbul-lib-coverage": "^2.0.1", "make-dir": "^1.3.0", "supports-color": "^5.4.0"}, "devDependencies": {"chai": "^4.1.2", "istanbul": "^0.4.5", "jshint": "^2.9.5", "mocha": "^5.2.0", "rimraf": "^2.6.2"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "report", "api", "lib"], "engines": {"node": ">=6"}, "_id": "istanbul-lib-report@2.0.1", "_npmVersion": "6.1.0", "_nodeVersion": "10.2.1", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-pXYOWwpDNc5AHIY93WjFTuxzkDOOZ7B8eSa0cBHTmTnKRst5ccc/xBfWu/5wcNJqB6/Qy0lDMhpn+Uy0qyyUjA==", "shasum": "64a0a08f42676b9c801b841b9dc3311017c6ae09", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-2.0.1.tgz", "fileCount": 12, "unpackedSize": 34298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbQQ3ECRA9TVsSAnZWagAAU28QAKJUnQFcvs1aJelTlsOR\nywl4QqIq/lR3CJoq8VrqmzKBa1U5Nbme9GhmWwSlhLL2sHNtcixLLB2oOLmn\nP69z8pPc3GeSm26GfqdDVEk4RYavTO/eERGtppATl9uZwfavbeh9AaASYqjD\nVUxwsPhpNnmY5Z/KHPn7EoCMCCqecaKqa5SgeofckVNqcHCDKGmEr/m9ZGQn\nE2Qwts2zE/n7QNuHE4KvVPPlxME/a7rrt1g8/uBj+c7MkcFfKxcPJ41LgMyq\nPt04/SLCC/ML9GBv2q7jqZAAKXqTVsmPPgkZFZQ7KuJWmRlpjFFW9m5VlSfk\noX6BCHEcMHlVn8FCOcIdDNLgC7xzVqJsJVOWMOK3YeqO5kO0HVExGGpiMvFJ\nYe6lLV6wdJnLVIdJ8K4f8I7wAMninbJl9F41n3A7tggh/t3CA/kW64NMojmA\nFezILWCYd2xNA6rwcVYS3BNOvd6U2s/cD+yO7ypMZdEfOkEmCHA1fOBtVtEw\n409iTuZRw0szPSQ/ZUysfcDaMFVbnvlQdgt/3RbmrGmeY/W/x4NnuEH8cVRz\nd/LbxtYzkF4lKbo0K2VGFaDwnUGM9honmxRDk+HPLex7gdck52dt8eUhQKnR\ny8tQstdbQ8ubT/8A7aX1/zJkt1j3HOIGKi7R4YcCccQTKVJ4mLU71DKRicBx\nbPzz\r\n=kd0E\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDstIaOCezqo7ShWQugForsJFpoNJreZKLRRgv3qA4FfQIhAIkEbWMHjBxIp0qw4i5oNrnN2zPAwXUjogb7I+CY/IKX"}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-lib-report_2.0.1_1530990019930_0.22959042000689434"}, "_hasShrinkwrap": false}, "1.1.5": {"name": "istanbul-lib-report", "version": "1.1.5", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"pretest": "jshint index.js lib/ test/", "test": "mocha"}, "dependencies": {"istanbul-lib-coverage": "^1.2.1", "mkdirp": "^0.5.1", "path-parse": "^1.0.5", "supports-color": "^3.1.2"}, "devDependencies": {"chai": "^3.0.0", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^3.2.0", "rimraf": "^2.6.1"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "report", "api", "lib"], "_id": "istanbul-lib-report@1.1.5", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "dist": {"integrity": "sha512-UsYfRMoi6QO/doUshYNqcKJqVmFe9w51GZz8BS3WB0lYxAllQYklka2wP9+dGZeHYaWIdcXUx8JGdbqaoXRXzw==", "shasum": "f2a657fc6282f96170aaf281eb30a458f7f4170c", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-1.1.5.tgz", "fileCount": 12, "unpackedSize": 34250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbkFiSCRA9TVsSAnZWagAAwUcP/3sOOc43shSqExHuPg5t\n7vihy4038PKX9TGizATWMCxN0YMJQplpy8cx3bzR3/2K0ltR3AjpOql2vQt9\nw+b5lLnc0lfVIk3QKxog6np4cQuCjbGBoihQcoANpuyUC8KY/21BxUruVKpD\n9L+On7MLa8y0EVUx6JFpy84T46Wj1IOiQqbtVivQNBsLY2Z+n310IRNBopio\nCaXXkC9dxr4Vft4l2SzDxluIariHJ64TIu+xQa6m47xtdHcliUzuYfBVUo3n\n4uo7+wTpQiZUUum1bnNdausuJ/WXebfGFfotdBkWt6hICDWwSiB9bl7oCGZL\nXKlBGUKSRmP8uMNmDgDBFcoFyjA79Zzr8jq8wCUJureuM78WgBFfZTX2aVqe\ntWibEQ/TPEsrlBLpTBHW9O+Gd7H9aEsVenb0WibzX/bMty8exvQstGn3NOvk\nXTYZi4o2Bkyudt3mjPl9bpc9u8YBOO6G8f8VtqUd/4muc1CStONmCjR13CVs\nSSGgU2FrwArrq/hfZbpL+znL+c7uT9pq+yq9mUDY4Ays0Zqo49llF2p7/C3R\n6Fhka3pYIn+iNgVbvWRmCAqFel+cq/1bxrwDsc+gSD+lkpecTUrQ8HAN7E6j\nMTPit5pKDjWu2Ic/fj7TBojBFy3rqBubOqR90yq49KzONgyNQXUI11lzMtfN\n+p/P\r\n=2DRX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAJrNzv/DIRtj3jJ/HeC9/MV34NELyscRRw3q9m6wT87AiATBGiTr3ceri/YCdvUkxYvalMfcoloT4Njh52BDvxueQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-lib-report_1.1.5_1536186513526_0.13931284816047795"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "istanbul-lib-report", "version": "2.0.2", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"pretest": "jshint index.js lib/ test/", "test": "mocha"}, "dependencies": {"istanbul-lib-coverage": "^2.0.1", "make-dir": "^1.3.0", "supports-color": "^5.4.0"}, "devDependencies": {"chai": "^4.1.2", "istanbul": "^0.4.5", "jshint": "^2.9.5", "mocha": "^5.2.0", "rimraf": "^2.6.2"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "report", "api", "lib"], "engines": {"node": ">=6"}, "_id": "istanbul-lib-report@2.0.2", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "dist": {"integrity": "sha512-rJ8uR3peeIrwAxoDEbK4dJ7cqqtxBisZKCuwkMtMv0xYzaAnsAi3AHrHPAAtNXzG/bcCgZZ3OJVqm1DTi9ap2Q==", "shasum": "430a2598519113e1da7af274ba861bd42dd97535", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-2.0.2.tgz", "fileCount": 12, "unpackedSize": 34691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbkHp+CRA9TVsSAnZWagAA/vIP/A2VPFfTPQ79ScbTXMDy\n2SDWx3X/v+T0fyFTHi5di0AxLSOFqI1s99kMACgaEtkzcH5Ews2htlwMLItb\nwX/D8wsk37tr5EuSzv3fvlTI6QMI8aOzw/BE64RKaatS7xR6ueoRlefVve6J\nkJXft9kBUNXRNOWzCgS2PqAhHXPwFquV4XHN/o34Me2jRyi/q0htJB6Obyio\nMTbr5ikLJ3PBDMqMs6ZKhAKmb/oudFuem0VzhvU/OGa9sHC130Wc/VrPlAXy\nvay4YDu7nu0xBz4VwHUzvEHSKSi8MjRTfL3y65uw0jFtjZ+37kfFVYchu32a\nFsCbq4N3rnz+Ia14WFuD/ZF7DkJK6KWbd7GYsUR3a67myMRYOG9f57aP/QHL\ndoao6chh5FBhrJLJEb4GgWrbpJ07RXnkv6F4zdzak9UaU4H9DwamWo2X9RH4\n8nGTHGJNStsfc/Sejt1DMebY8wz8nBD6f406pnnX7X+zbLUg7Gcrf0nhhGJn\nF78CeufTnnJ8H0hM7QwYkNRHpgq9CMsAeUWH9UNbhn0yFvgSrFiGGDlH4Qji\nsZLP+fLNzn89hgxmi+KqYMIeEv8rKCGPzdrn3Oahki//Msjmd46Axw5E3xRo\nGgFzGOdHZFNTdOW8wHne1cNsXXbcZbuDU+aD4hQGIvLlegZ8dKpd8skmmYDQ\nNo2j\r\n=Ar6H\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID9kP3ca0lfqP2jSj67Kr/CWFJHA7id0V1EijsoCmP2CAiAQR6dDg0RHIb5liIYnkyeo9aigrjKjNs6sTkHZOVfMmQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-lib-report_2.0.2_1536195198044_0.6921130723239006"}, "_hasShrinkwrap": false}, "2.0.3": {"name": "istanbul-lib-report", "version": "2.0.3", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "mocha"}, "dependencies": {"istanbul-lib-coverage": "^2.0.2", "make-dir": "^1.3.0", "supports-color": "^5.4.0"}, "devDependencies": {"chai": "^4.1.2", "istanbul": "^0.4.5", "mocha": "^5.2.0", "rimraf": "^2.6.2"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "report", "api", "lib"], "engines": {"node": ">=6"}, "readme": "istanbul-lib-report\n===================\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-lib-report.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-report.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-report)\n\nCore reporting utilities for istanbul. Docs to follow.\n\n", "readmeFilename": "README.md", "_id": "istanbul-lib-report@2.0.3", "_npmVersion": "6.4.1", "_nodeVersion": "11.5.0", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-25gX27Mbd3MjM41hwGl5lWcQEqaPaMP79YDFS20xuTUujItNmHgTBS3WRZvzyzLE0IAKaL+JpLrryou2WlZNMw==", "shasum": "8e22534766e9cc8e20ae96283331b4405da9dce9", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-2.0.3.tgz", "fileCount": 12, "unpackedSize": 35174, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcIXvoCRA9TVsSAnZWagAAqqIQAJs+urnbeBg4GUsXcJ99\nwU6aMxDheZ8IDNlBQhWS8pHvrmVkWz3JVqq2meOuDp0LZZjHmNsC7/ThrpE/\nLWr8x3Q3YUXwbK4Sx93aF0l1mjbfvQIJxyJbjAvC+tKOrVfH1s4c8TNoFZKo\nfQn75zYph3rv3tTh5S4k41CknA2sTTX+Yc9UOztO5rNa0K26xhRtuLQVqKon\ntFbdZhlgNwJHMs+Hjq84qZDdYvbeVYGZpgbGqkSukCcI2BoRtX5GBXkWsvCh\nQZ1LzaxHTxiDHwmweHoSCqLdm8RyNsUZhV0uDFi2hT89xXrSNHewKkCxqFXz\n8Y70BsvUOQlUhxhbjAbFXYadHSaqWQ5+b5uSPe5+xDdOLIiBU5LYtebIMnYj\nybBTga8JbqMfHDNLtQQaAcnD2gfp7InKk/eYkqJXz8EIQRKy4Ko4+7vyCw6J\n0H/mIsuzG6KmX3AGeHpKH/JTi+J2qFGhcPC+0JXBy3s19A5TRIWBUHoKFflV\nMcEFYHHpYZm/knqAUAsngaREsWE42eL6VFyut/oW8jL7GxBzcJ0+MdQABRFT\nL4+6OP5X6Xs9Ta92VWKCHK8BnZnf4TLd5V44h/Ij1t6lMzkynpllAxraybTj\nvF118LwUVV9LxFLA8SW6swuKKlPL6jzQVQVKBSwT94/NyUbQRe9unrayNvqC\n2Ed/\r\n=Pt4J\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD6fm2fbI8ZI3FsCGY60HifIw7IQP94zHes3Pxah39CJgIgI3+u7fOftxryldYmqMYigs7usVLqMAfzq7Yqg9w0UN8="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-lib-report_2.0.3_1545698279790_0.8971117972464635"}, "_hasShrinkwrap": false}, "2.0.4": {"name": "istanbul-lib-report", "version": "2.0.4", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "mocha"}, "dependencies": {"istanbul-lib-coverage": "^2.0.3", "make-dir": "^1.3.0", "supports-color": "^6.0.0"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "report", "api", "lib"], "engines": {"node": ">=6"}, "gitHead": "7875defdc3c3640787ac5d83700246de119e8b83", "_id": "istanbul-lib-report@2.0.4", "_nodeVersion": "10.14.2", "_npmVersion": "lerna/3.10.5/node@v10.14.2+x64 (linux)", "dist": {"integrity": "sha512-sOiLZLAWpA0+3b5w5/dq0cjm2rrNdAfHWaGhmn7XEFW6X++IV9Ohn+pnELAl9K3rfpaeBfbmH9JU5sejacdLeA==", "shasum": "bfd324ee0c04f59119cb4f07dab157d09f24d7e4", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-2.0.4.tgz", "fileCount": 12, "unpackedSize": 35440, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcS8iUCRA9TVsSAnZWagAAbPQQAIavw/H/Pr4ePDQWPAWg\n1vYDr89WSLP97v8x8DEffBdDkwHmcYlWwVMOKvOK6vJVHosNeAJxHdsVOKkD\nVOfh9mf6Lrdz/eVoFawqgK03hMe6l0oP5oyhMQIfZ+hVPWu3EVZTRmXAU0fI\nOVmgEjHydL3P5fJn3iWK+eL3dNaoolWP596qYiEb4eBRI3O6YAzO2/IYFftQ\nj5a9nkvQGQNFNMA711k8RfEdpA9KFA2PdVSRMq5lVwrouDdmFKTgJ/nERvK6\n3PfoLstKLt6QL7UZ1Oa9g+aeMl/N2ol+HKS5GKzmGypwl5dT9k3qcR6HOuuh\nEXKcJiJeQYZkpgW8nq7FQ2Ak2Aw70n/XfEZhJSm2+Z3Oh0z41kreTN6ux12N\nm+oQkfkPI9JgpRHSh45TbLpYmDm4ykCfvhCAosyc0BEwmJLpMYjkHPE4ikXo\n+iL/KdEMKlJiIT97kQdCNoI8jTrPMEvQI4iaa3aisGNugPvNo+TFBXbiiIhy\nAL2SkAzVJLBM5U5fUY7XUA8GSbMHlLL5BLOmaJqccRKH7MI9QNz8ypAfMt1W\nEgXlisVvAbeTN6rGvn90rqqSrrMyp19DTyMZtWbZLqGxR/tLKTEvAKOJswMy\nrEFz9l4flbYA+TzCCoyR97VTxRfVTA/FmaI5UHiHlrKtaaTVwN4NdQt3bu6U\nIfJw\r\n=J9zm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDw9BYL1clCNTguoWA+ngmChcbGg2j9UG6lNKVnqwXrLgIgHa4CeoiJHuzu09ZN99WHPZa+synvNXkf8Sh3h3IMe8o="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-lib-report_2.0.4_1548470419893_0.7787598743101318"}, "_hasShrinkwrap": false}, "2.0.5": {"name": "istanbul-lib-report", "version": "2.0.5", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "mocha"}, "dependencies": {"istanbul-lib-coverage": "^2.0.4", "make-dir": "^2.1.0", "supports-color": "^6.0.0"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "report", "api", "lib"], "engines": {"node": ">=6"}, "gitHead": "c81b051d83217947dfd97d8d06532bd5013e98c3", "readme": "istanbul-lib-report\n===================\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-lib-report.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-report.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-report)\n\nCore reporting utilities for istanbul. Docs to follow.\n\n", "readmeFilename": "README.md", "_id": "istanbul-lib-report@2.0.5", "_nodeVersion": "10.15.3", "_npmVersion": "lerna/3.13.0/node@v10.15.3+x64 (linux)", "dist": {"integrity": "sha512-stE6Ky+R2b9Ioysax4AgGCeUsEYFUkjy+wWa+NbekB/oRAnuV/HDSuIr7RESWHDf5+F85e+UgIyFluMw/90kLg==", "shasum": "fb83869fefd778a8fce679737cf7e3ae7949972e", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-2.0.5.tgz", "fileCount": 12, "unpackedSize": 34955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchwfxCRA9TVsSAnZWagAA7XEP/jRZ5zhfZAt5Ops+tMny\nO0yMzKSZzWEzSSlPaQBFJuXCmSjuamFXpK0INbTM0/X3GRgki11qmBi6BYaG\ntaOkDnbk8IPTFnm46YV26k2cYIKEf52pmsk4jqi24RLA+iObGrYDrtWNbL4t\nKRUh2xrLvnDbW1i8dHuJyQcHfFvQOujJqiGAqBP88mh+Y4P9Zm18bVQU2B9p\nIvS0GGCFLUE3UdPJKxHsdA3o2S03VJAiuGM15LX5XzDLVTGps/FxrN/886t1\n59DJE9MTUoYnejUYt6VlYiru1RRb4AumBKAuSjyfVA1bIbMpWSfa4SrxQ8Cw\nf4dctIqnBjjaBTKA5DmjV+b9m41PprLXyrITIXrppMm02qs0bgfl2VFB1fLX\nzZRE5wTy10zbl4+QJvQ9GDFx2ciVlxV86MRpoDjkrxZWkRVKSrjNcOs2cF3y\nWdTwhr9+tt64lQ1lF34FfVJ/CWjpH1ggifXj3Udh2Nuxf7/+Bw5z1N3I4uiY\nVaICWQFO7ddBqKfpa5D6cxF22nVFkRKnrh0B2+WEo5sAJnTpRQoWcCepgPlc\nbuaWXndIqJtMM80N/uGVlqp6K5E3lVydQYb1QzneFX0yjkrTvULZLZUhDaGv\nklEIWddjJR2lwz6SuQc9gqearbAzRebnjFCdbKuYWd1zU9e9NyCnb6CwfE/l\nyVqb\r\n=uNHx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDXPV8x4HxxTQzHr69XTf7JftkCplmxmpyflymSZQhMEgIhAO410xY1OmppeDPU7/xHY0tR/4nL5jE73GsXNoE8vm6h"}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-lib-report_2.0.5_1552353264575_0.004904485855063312"}, "_hasShrinkwrap": false}, "2.0.6": {"name": "istanbul-lib-report", "version": "2.0.6", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "mocha"}, "dependencies": {"istanbul-lib-coverage": "^2.0.4", "make-dir": "^2.1.0", "supports-color": "^6.0.0"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "report", "api", "lib"], "engines": {"node": ">=6"}, "gitHead": "e8063c799d0854341cb3daaf91c58acd06bd501c", "readme": "istanbul-lib-report\n===================\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-lib-report.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-report.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-report)\n\nCore reporting utilities for istanbul. Docs to follow.\n\n", "readmeFilename": "README.md", "_id": "istanbul-lib-report@2.0.6", "_nodeVersion": "10.15.3", "_npmVersion": "lerna/3.13.1/node@v10.15.3+x64 (linux)", "dist": {"integrity": "sha512-kgjdxu1H3vdsp2Z1AE679izvRZKll3d2MtXpSb6T94tfEcEl8dpGHbcfvtUN2yI+MK5EZcXYCJPWLD3OL74ffQ==", "shasum": "2be80298330e83e20c1d8f51bd4c83ff9ed1e61c", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-2.0.6.tgz", "fileCount": 12, "unpackedSize": 35419, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpPXCCRA9TVsSAnZWagAA9c0P/1KK8ivhheKKRVjmpSXz\nifvUetS8H3/d/c4dtfOH3sqv2NoLrtb3rnGkKy5xBE0NMR8W23lLX8ZfsjWb\n/MqhwsAavW1hwkWCglvny8Ji+Hpp1hLGjeAJrpXJLBU8rhPOiQ262lhKJNyq\ni4oTleIvDxzK5Iy2D6toZKCl2F5xthn94rDdZuR63fAtq3SquYh3kmheNlJg\nCKvwKYUs6XRoWE4URhOnMkBNCzp5xB1k5bs8og9oyPwsyLNVR62T76cAiOES\nXKgXb6/zLpknRAUTp+ExgHkmVw0+lwYRaoGwNjKy2+2sd6doMixeq0fXa/M4\nTxb1a1hg0OFxbl4jMrSXdKx0QEqJDvMKYb/ziJJkkAuFGGvqURpMYN+Xgoag\n4T3OI+i3OYNU5TnCoDF/HJ7Qfc+c+woBsDtXbD9jN03J0ci2DRkh89L0MyzD\n12nrWB1qc+OF1pQLmzHdUHXjjU9z9PUS/2wkjveJnJ7ueJ4tbXQYE2P1wLfk\nqyxE96+uTMNVf4swX7Ovq4WHEeuHC87yyqiAy5fOx1wjDJNWB5QxRw+RfED0\nGLfeviMpA0TGVq/OajI+tDs9OsvQSqYNxwLf0YKSQikvc1EsCipsKd0ffXW1\nEHT7AfIC2En1z4F4Z1mAPF29tBMgQGx3Jh0zE6OLgGGa0uMnj9Rfvr5j0D/E\n9a5+\r\n=DTmy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDSnRTdqKVLRUbrkSawsXGYii4ljNH1vUyEUhGcWe/rgAiEA7yFRDW7YNmGbvc9k4W/p449lK4Mac7w6B18ziS622f8="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-lib-report_2.0.6_1554314689763_0.49830095110450334"}, "_hasShrinkwrap": false}, "2.0.7": {"name": "istanbul-lib-report", "version": "2.0.7", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "mocha"}, "dependencies": {"istanbul-lib-coverage": "^2.0.4", "make-dir": "^2.1.0", "supports-color": "^6.0.0"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "report", "api", "lib"], "engines": {"node": ">=6"}, "gitHead": "9f8aebf1f08159df20358d77fe98c809d2027c5f", "_id": "istanbul-lib-report@2.0.7", "_nodeVersion": "10.15.3", "_npmVersion": "lerna/3.13.2/node@v10.15.3+x64 (linux)", "dist": {"integrity": "sha512-wLH6beJBFbRBLiTlMOBxmb85cnVM1Vyl36N48e4e/aTKSM3WbOx7zbVIH1SQ537fhhsPbX0/C5JB4qsmyRXXyA==", "shasum": "370d80d433c4dbc7f58de63618f49599c74bd954", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-2.0.7.tgz", "fileCount": 12, "unpackedSize": 35613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrSjsCRA9TVsSAnZWagAARzAP/0hkdSoRzW2/6zFBC7yS\n9+qek00PMla4muJBOpcBH+qNZd5w+JBGlfQxOiB6YowtSIfJFZDCO7dj97Pk\nL0nGevJyeJVTkIKLe4Fdw/LV+Vw1hFI5eN+12g2CnH5lAMvdso/Kd1N9HQat\nXzrWVEH2xbgQ+BJUyag9aaCGAf2o6MfR/i4naVqR+lcILvzC4epVbE0HWSeE\naPV5e6PA/17OdJ2Uysn3HZrZPy+oDbt6gxb0mIcoqtjzyY4nYf3iMvOJmqnB\n/bXFVrEThxIbdSVMMOnEyHXgYWZx/W+H/eExbVyLDh/Kaw2TBqk/3mlLG5y/\nUlnzp8J0gFwa7mMJ08TPbcPTI/DkxiWg3NxWs5KBbapMvVbE/f87+LGGHvqB\nrtauvKElEMYeVehR5wShmGJ2y4GX3j1JPNz/SSyXk73Qflwh/+6h84UEErfG\nAdtzs1T49hPh/Rpx6R3U21Z+CVVsk6qJcC5T5LuFxvAilp3H1Zs2QEh+us2F\npAnX1ZHft1PssECI1LlRz6TamibfbMlH6aCqRisBwXGKebt8oK0eqqKOSe8r\n3Jqu50zmeLO04I1gr13Py5L4h2u21y+8fmQATxgJzOoLUMyYbaRoUFKHLV3b\n1YpOD7jHM/dsU3SIcTe80Jy2/DSTd5wQfMXMpNSMHPuQL5v8xjPM2JEO3zmE\nptt0\r\n=6PI+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHNXh52agzfAs9L0FUhVEZ5IwoMrlxebBpOCz/xx3K6gAiBZGALCSWhGjYIPbgNaVRIup/u6BSBp0P2QLgiH4zEfEA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-lib-report_2.0.7_1554852076113_0.9106071888185114"}, "_hasShrinkwrap": false}, "2.0.8": {"name": "istanbul-lib-report", "version": "2.0.8", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "mocha"}, "dependencies": {"istanbul-lib-coverage": "^2.0.5", "make-dir": "^2.1.0", "supports-color": "^6.1.0"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "report", "api", "lib"], "engines": {"node": ">=6"}, "gitHead": "90e60cc47833bb780680f916488ca24f0be36ca2", "_id": "istanbul-lib-report@2.0.8", "_nodeVersion": "12.0.0", "_npmVersion": "lerna/3.13.3/node@v12.0.0+x64 (linux)", "dist": {"integrity": "sha512-fHBeG573EIihhAblwgxrSenp0Dby6tJMFR/HvlerBsrCTD5bkUuoNtn3gVh29ZCS824cGGBPn7Sg7cNk+2xUsQ==", "shasum": "5a8113cd746d43c4889eba36ab10e7d50c9b4f33", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-2.0.8.tgz", "fileCount": 12, "unpackedSize": 35788, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcwMrFCRA9TVsSAnZWagAALOAP/RdlHTZnQvYz/oJQ3TAo\nXzlyHvBn9/mQpUdvb3bvyWfzQMrnBoz6GBrLQXvrB+0o4Zva8vRj/LWj+T4x\nhTwoXlB+4hp4+mGCswF0I3MSiL4LDP1MLBj+ZqkqAK6fIp8ODIlWgX7zSQ9W\n0wxyqL2EUhOKbBkzpjh6ESY7OpvPiZuk5phY6O2+tWxaim/GisYa5cTtYr/4\nW7vo554gO/Xb2hXWmXaovnpM1ERj3+al4vKhOP7D2uRteUAqLaS67VB60DqD\nKKDvZyx82I7ijcY6tUkzghMkmwRdKEcknGrPHqXTNX5/0JQmziWriMRj0ttX\npZd/zblS3Ev6xlKdjmaebH++Aaw9JXqbwIlfN4u+nSPj02KEzadCIKzPP3v8\nCe+BNZfviWPOhSCcc/A6vr24pV09NBrE2BekBgo4HsGLS4vJ+Awu+40IJqmB\nUY0fblf14s+904DJqU0ZehZruxeKYEUc2AdWj0AuXSNgoMwdsrSux8YwJvef\ns1eOwkiK6K3HEKfsnZZoDZIh/QYiio5NjD8Bjr9mZtbVz485w7MawTLmekwi\nkGZPhTGQ6Hs+adQNGrJ7L0qdBrOPcFoyy7COCPoewbk8C2BUdCbh+gJfMWHQ\nKYjv8CxTsNuTOYx5RrhGEe6qoPWxJyHax0XRgLaNyu6CFag2H6k8cJT+XS+r\nFgr+\r\n=qVMU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDOZwz0N/UZWFXKhJG64nIeDZj7EY1E3fsjV1cwyKWLNAiAL2UV0CwWBf7kiFQ9i5nkwf9UGjXQXsDF6hrNwYSfGiw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-lib-report_2.0.8_1556138692532_0.3774214882995657"}, "_hasShrinkwrap": false}, "3.0.0-alpha.0": {"name": "istanbul-lib-report", "version": "3.0.0-alpha.0", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json mocha"}, "dependencies": {"istanbul-lib-coverage": "^3.0.0-alpha.0", "make-dir": "^3.0.0", "supports-color": "^7.0.0"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^6.1.4", "nyc": "^14.1.1", "rimraf": "^2.6.3"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "report", "api", "lib"], "engines": {"node": ">=8"}, "gitHead": "2e885073a9398806c9b8763dd39418398182ca34", "readme": "# istanbul-lib-report\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-lib-report.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-report.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-report)\n\nCore reporting utilities for istanbul. Docs to follow.\n", "readmeFilename": "README.md", "_id": "istanbul-lib-report@3.0.0-alpha.0", "_nodeVersion": "12.3.1", "_npmVersion": "lerna/3.15.0/node@v12.3.1+x64 (linux)", "dist": {"integrity": "sha512-WF2vFLJ10jeSBpvNOvWcXoZgLxGqHY12ScgRzq0ZiEx7NbbtFIFDgo6JRmhMOXXcgrMvbNp7oERaTGsuzPhtrQ==", "shasum": "373edaf1eeb18a9305d118ea225939cca3eaafb9", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.0-alpha.0.tgz", "fileCount": 13, "unpackedSize": 35810, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCifKCRA9TVsSAnZWagAA5vIQAJUwWLTCyK2OKa6hHur0\nWwx05jvFiz5LlDn1lh/wn+WiogLWsupSAKuhPLZoo8Y3BrEUzweloEmvCII7\n3L67TMsV2vJhCLumxqEvXWJBp3tAKV1MMt8FCckNYabsEsmLgDd9+gC2k9SS\nKduKlxYu6IzgGQc1cQZq1lNLBqgk0MP7ZOjLn6GyqJ0zjFUNN4Rikp4Duqvr\nbQ4DzThRnvE/edWNAVH4GVJPCAMhYuwdO2d4vEAT6AXyjexJLluYC+C5Hokh\nn+8kbuWtnOR4+NTr15o/38qEz6vljxkPq3JYq4RJJM1jDKPhN7MfZ52JewuG\neY0GEsEgLJyuXmZZK3fM/tLfGSNQ9i3Qtmt94GuHHvlwsN1j5YmIyb5GP8rl\nE7IT6eJwCfGQCgxP3cHfxCmv9+eLoFQIHUBZxuHfRO49K7SUrT3S/jERl4Ea\ndlBZ+YwKXtuu2QdjQKypZUc5XzSba30RCf+PYXqpYnd7ELRGNRHqEs59V6GU\nE1+sjZDOFWfQY7xwaaysQPQH7O9a1QoTAAtw96a0GW6FfBIdMa2K80/ew2mR\n1rCEw0mpnmGCm/mhK5KFbiq+v5LIRmh02T7unmIJSSV59vdncurNYsmwBAmr\nvxvCyYx0c44qbmNS+jvfpgn82zXqjUpmWuKBrp+KomKWcRo91pIqfMzXxUda\nSZZ8\r\n=SLuz\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGUfeGmi/SpKiy2BMpKt9bVpBI7Uy7KXiMe5x6iJAXciAiBSBPfb+MlrUEytaiv1oM761sCbMy0pLWYIMPJcGCifDg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-lib-report_3.0.0-alpha.0_1560946633502_0.7447753365634453"}, "_hasShrinkwrap": false}, "3.0.0-alpha.1": {"name": "istanbul-lib-report", "version": "3.0.0-alpha.1", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-full.js mocha"}, "dependencies": {"istanbul-lib-coverage": "^3.0.0-alpha.1", "make-dir": "^3.0.0", "supports-color": "^7.1.0"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^6.2.1", "nyc": "^14.1.1", "rimraf": "^3.0.0"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-lib-report"}, "keywords": ["istanbul", "report", "api", "lib"], "engines": {"node": ">=8"}, "gitHead": "4d5e777a9bc4847d178ad31f379307124cdd1e4f", "readme": "# istanbul-lib-report\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-lib-report.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-report.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-report)\n\nCore reporting utilities for istanbul.\n\n## Example usage\n\n```js\nconst libReport = require('istanbul-lib-report');\nconst reports = require('istanbul-reports');\n\n// coverageMap, for instance, obtained from istanbul-lib-coverage\nconst coverageMap;\n\nconst configWatermarks = {\n  statements: [50, 80],\n  functions: [50, 80],\n  branches: [50, 80],\n  lines: [50, 80]\n};\n\n// create a context for report generation\nconst context = libReport.createContext({\n  dir: 'report/output/dir',\n  // The summarizer to default to (may be overridden by some reports)\n  // values can be nested/flat/pkg. Defaults to 'pkg'\n  defaultSummarizer: 'nested',\n  watermarks: configWatermarks,\n  coverageMap,\n})\n\n// create an instance of the relevant report class, passing the\n// report name e.g. json/html/html-spa/text\nconst report = reports.create('json', {\n  skipEmpty: configSkipEmpty,\n  skipFull: configSkipFull\n})\n\n// call execute to synchronously create and write the report to disk\nreport.execute(context)\n```\n", "readmeFilename": "README.md", "_id": "istanbul-lib-report@3.0.0-alpha.1", "_nodeVersion": "12.11.0", "_npmVersion": "lerna/3.16.4/node@v12.11.0+x64 (linux)", "dist": {"integrity": "sha512-EeRtb2Frb5Ttt+TNvmIJ5oXHr6rogCpmKbNJskORRqjv6/ajCzm9ZO7tVegwZQNNyKAWi7JocxOF8IoTa3OZhw==", "shasum": "d28b90a20c8cc179fc878f52249d972f43f869f9", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.0-alpha.1.tgz", "fileCount": 13, "unpackedSize": 37043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmT4DCRA9TVsSAnZWagAA5awP/RyZkBukAwn6XQBp38kg\nsA9/VbFSwrwUYR/poArlxwcVAIMw8XWnujnYeSjIxJk+p9tPA7e/QQ6rUADH\nH1xN5o4JePB+IpgtmC2EHgpuaOurOlZKp9/ab8buH3Cym2AYCfBjhU7u6FSh\nD/RqUPiPf/U/kWRbIsYa9rSweKQRYxhFqxh9cNCBon2LsXzSix9X7YhNNucm\npctcK3/fTN64JO907zgvKaned2wihVYqDC5/j2fOd3W8l2qIoTGVn8s3W7hd\n6mjHCYv/aFtQAciR6sQAEXm6bz8y+NxHDvjuA21BSN4NUbPnxhrAsU4WrH7I\nfQYqAmZvWCy8qCoTBOD8CtqXx6o7BU/kz+aSSSjrDpHqZ95Iu7gJeZkO+Idg\nbbao0edVRZkA6f88HQ++zSFPGibq3qG4tKvx17XA7IZfAe5E5yr1hTdMTl3t\nRmhm3PMKbgFIdvc3HQJOWPOXJwyAts4iVv/dyDJq9yyMEfzHxeY199QS2ihX\nHfgsg0ZrmHKSxOF6bDnh1hXMrOUCtj1XXnvNSHQsuKCKUz37EWp6AyoIxXd2\nO5fpkzimRH8Rq4LSQIz2bGmldS4TTBhKptCFx5po0MrzYdw+z65X7UovhDyg\nE3iwZYnW2qSz7lsVAsn9CymaCRuBw0jFCkEkedi9LMYveBmzBTqZ1ZxBBPsg\n6Nqz\r\n=YSg+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBaEFKrNSzGC2/qMQm3U6YaoBS7yPz6y6ZP5Dh5Rf9eEAiBppm35PCQ+c/Rs9hpFh7YDiJvcA60X79fXeGe2nq/ZGQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-lib-report_3.0.0-alpha.1_1570323970716_0.8409115022015143"}, "_hasShrinkwrap": false}, "3.0.0-alpha.2": {"name": "istanbul-lib-report", "version": "3.0.0-alpha.2", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-full.js mocha"}, "dependencies": {"istanbul-lib-coverage": "^3.0.0-alpha.2", "make-dir": "^3.0.0", "supports-color": "^7.1.0"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^6.2.2", "nyc": "^15.0.0-beta.2", "rimraf": "^3.0.0"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-lib-report"}, "keywords": ["istanbul", "report", "api", "lib"], "engines": {"node": ">=8"}, "gitHead": "9546946f0e4bc80714a5b318c59e459781f05550", "readme": "# istanbul-lib-report\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-lib-report.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-report.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-report)\n\nCore reporting utilities for istanbul.\n\n## Example usage\n\n```js\nconst libReport = require('istanbul-lib-report');\nconst reports = require('istanbul-reports');\n\n// coverageMap, for instance, obtained from istanbul-lib-coverage\nconst coverageMap;\n\nconst configWatermarks = {\n  statements: [50, 80],\n  functions: [50, 80],\n  branches: [50, 80],\n  lines: [50, 80]\n};\n\n// create a context for report generation\nconst context = libReport.createContext({\n  dir: 'report/output/dir',\n  // The summarizer to default to (may be overridden by some reports)\n  // values can be nested/flat/pkg. Defaults to 'pkg'\n  defaultSummarizer: 'nested',\n  watermarks: configWatermarks,\n  coverageMap,\n})\n\n// create an instance of the relevant report class, passing the\n// report name e.g. json/html/html-spa/text\nconst report = reports.create('json', {\n  skipEmpty: configSkipEmpty,\n  skipFull: configSkipFull\n})\n\n// call execute to synchronously create and write the report to disk\nreport.execute(context)\n```\n", "readmeFilename": "README.md", "_id": "istanbul-lib-report@3.0.0-alpha.2", "_nodeVersion": "13.3.0", "_npmVersion": "lerna/3.19.0/node@v13.3.0+x64 (linux)", "dist": {"integrity": "sha512-dXMNi5B6EWYiYZNM5vcSw50JSwGTuAxMYw9dccrfI8QHkyiqvZXahGmUsBKplOvcQ4SK1G82JfMBpcASvn34gQ==", "shasum": "e81ccd7e2e2cfca2f17bfa94ff8e6edebe5731a0", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.0-alpha.2.tgz", "fileCount": 13, "unpackedSize": 37267, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd69ZVCRA9TVsSAnZWagAAXQ0P/1GJM3QhbBjnMqL+96dM\npcxoEKd1+Y+CNDy89dZiMTU9EC/I55c2m1zRtBgXQtiI3qVfjuG3z2UnFXQY\n7vn0cfpnLMY6kTjTztQDmlpA2s+q1eNBPzzln1+035i2sGuHnd3wSb0aV6iO\ng+s4+e+TWwePVWWwrOQMsJgJsvczBl6kpLLJdvYLoYdwINIfLHTaMI8ZeRUl\njg0A1Uswh9Bs6DpOB/S4X/vPfO4+DUAtsYxdVIZhGggbQEqwxh9a5mWxrjvD\nhgiV5KZXVhZIeLHrEmP1EIQS+ZtsALSVVhog4qJYByTm+5+gj74XVvXzsnhU\nLbijUH7uFG4GbG9aLzh63IMF0o664wWberPaFY8KcNI4+n0srBRx1YhUNIOz\nv0qAzVdVPxc4eCVbV6xd+w/OMjeO11G8AkSThciBM9POuKzpb4GhE22JEJDi\nnpycdjlf6Q4p6kAA8KYJQk6U0SWJrColLXQWplI25Rkpi+qJCQUaQXuBy2X9\nWTjuGcmFPMMRGUOITcFD9SSzbhYvllSQLZ8VxJ9Vcck3sSjuUH7ZBgvJnyxY\nHntrtoFrQUpqwKrpqE4YyuRsLfx5hIQ/cjttx5tGPagG4uyLL4AtaXEJEnSs\nKkOu7SFSmTrqi/hKDeO9nbpDWwy8O3MA2WYtNOcQJet7wJmV9FMmiolZDYB7\nt8m6\r\n=Nn2F\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCPpbWonEhJeXz6lToN6OO6RhIksSLCWmicQ7aMU/k3wwIhAMkzTef4jax/qQRIBW26iPyv58FdPNXRKRAFb+3lXifh"}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-lib-report_3.0.0-alpha.2_1575736917388_0.4632795598098458"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "istanbul-lib-report", "version": "3.0.0", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-full.js mocha"}, "dependencies": {"istanbul-lib-coverage": "^3.0.0", "make-dir": "^3.0.0", "supports-color": "^7.1.0"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^6.2.2", "nyc": "^15.0.0-beta.2", "rimraf": "^3.0.0"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-lib-report"}, "keywords": ["istanbul", "report", "api", "lib"], "engines": {"node": ">=8"}, "gitHead": "5319df684b508ff6fb19fe8b9a6147a3c5924e4b", "readme": "# istanbul-lib-report\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-lib-report.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-report.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-report)\n\nCore reporting utilities for istanbul.\n\n## Example usage\n\n```js\nconst libReport = require('istanbul-lib-report');\nconst reports = require('istanbul-reports');\n\n// coverageMap, for instance, obtained from istanbul-lib-coverage\nconst coverageMap;\n\nconst configWatermarks = {\n  statements: [50, 80],\n  functions: [50, 80],\n  branches: [50, 80],\n  lines: [50, 80]\n};\n\n// create a context for report generation\nconst context = libReport.createContext({\n  dir: 'report/output/dir',\n  // The summarizer to default to (may be overridden by some reports)\n  // values can be nested/flat/pkg. Defaults to 'pkg'\n  defaultSummarizer: 'nested',\n  watermarks: configWatermarks,\n  coverageMap,\n})\n\n// create an instance of the relevant report class, passing the\n// report name e.g. json/html/html-spa/text\nconst report = reports.create('json', {\n  skipEmpty: configSkipEmpty,\n  skipFull: configSkipFull\n})\n\n// call execute to synchronously create and write the report to disk\nreport.execute(context)\n```\n", "readmeFilename": "README.md", "_id": "istanbul-lib-report@3.0.0", "_nodeVersion": "13.3.0", "_npmVersion": "lerna/3.19.0/node@v13.3.0+x64 (linux)", "dist": {"integrity": "sha512-wcdi+uAKzfiGT2abPpKZ0hSU1rGQjUQnLvtY5MpQ7QCTahD3VODhcu4wcfY1YtkGaDD5yuydOLINXsfbus9ROw==", "shasum": "7518fe52ea44de372f460a76b5ecda9ffb73d8a6", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.0.tgz", "fileCount": 13, "unpackedSize": 37452, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd/TUOCRA9TVsSAnZWagAA8L8P/jSX2cPk2QXmgQlWk63z\nVmf33Hou1Rb+1TJSpj9yJENy8X7nuU3ui4GBP4SrX+2fRkye3wPJS3n4a52G\nEkfewi23yv2U8D9zBhggeOCtw72hFa5AJLHi2IBLWOmca9bwrONLG5Dy9EOS\n4vEY4Cffe7mXe6siYXJ+2HHZ7tAs9cvHBSTXuiOIKIoTjhyPBXBHNxdNk9Pl\njV675lb/NQM3RPnZm0MEL2VaMtE6RBSHsovM4eqeayl3wSb0KjwEyMOmUD0J\n/dTcuxTkenQ31rG+gd0KgMiP1ShLVCSVdyrNRHS5tbloOpnG2owHmwThWkEh\nHte4ZQh+rygJDUY+cqFcwLPGd2ulk9Uz/zw4WUD2P7fiDYRJLuDrANMrqaAb\n8ztdTXmmhqOAGztVPssf9zUeIon4nlVO6/97SejhSms31FYbfdvfpNGBWw37\n40vPsyNQ86oe+sMSGLCvVpoVIeylMJ+eCAqK/AsIm/FEWSXQV/GO8vv8lEi0\nYCP0bWvvf2KtPRuy+qWadsl+zDE2Ra9QXmPVRZwsbT+b92zM2w//F/g2r44E\nWc9g0FuhAV/eHdl5HPfA0ZjmO3WOKuwtvcybavK1QhklMBbxV0HdJtVti6Qm\n1DIqNe0SHy23qM7io+o6y3JjZKMSnKlS4a4w5vOlccOMgD/+2tPiEXSElfdy\nFk7h\r\n=3SWy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDk3xT1OcGr0Vbr/Lqhyp1ayJFGLuQWdFWPBy0nKFa8HAIgDkLbrWUzySPexKaxJQvMjWC8GM+lZ+qQuITyw7+Wgdk="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-lib-report_3.0.0_1576875278544_0.19109667818694676"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "istanbul-lib-report", "version": "3.0.1", "description": "Base reporting library for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc mocha"}, "dependencies": {"istanbul-lib-coverage": "^3.0.0", "make-dir": "^4.0.0", "supports-color": "^7.1.0"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^6.2.2", "nyc": "^15.0.0-beta.2", "rimraf": "^3.0.0"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-lib-report"}, "keywords": ["istanbul", "report", "api", "lib"], "engines": {"node": ">=10"}, "_id": "istanbul-lib-report@3.0.1", "_nodeVersion": "14.21.3", "_npmVersion": "6.14.18", "dist": {"integrity": "sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==", "shasum": "908305bac9a5bd175ac6a74489eafd0fc2445a7d", "tarball": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz", "fileCount": 13, "unpackedSize": 37641, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCCvaAL46mWIk1ayGfF9uAq3ZH5ioLvrQzupN/WhHWu8gIgRh/WP+V6zDliZiRSDN/m6RCqscLQGxSHmCH7Y2L6Jz8="}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-lib-report_3.0.1_1690295498222_0.14603155054781447"}, "_hasShrinkwrap": false}}, "readme": "# istanbul-lib-report\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-lib-report.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-lib-report.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-lib-report)\n\nCore reporting utilities for istanbul.\n\n## Example usage\n\n```js\nconst libReport = require('istanbul-lib-report');\nconst reports = require('istanbul-reports');\n\n// coverageMap, for instance, obtained from istanbul-lib-coverage\nconst coverageMap;\n\nconst configWatermarks = {\n  statements: [50, 80],\n  functions: [50, 80],\n  branches: [50, 80],\n  lines: [50, 80]\n};\n\n// create a context for report generation\nconst context = libReport.createContext({\n  dir: 'report/output/dir',\n  // The summarizer to default to (may be overridden by some reports)\n  // values can be nested/flat/pkg. Defaults to 'pkg'\n  defaultSummarizer: 'nested',\n  watermarks: configWatermarks,\n  coverageMap,\n})\n\n// create an instance of the relevant report class, passing the\n// report name e.g. json/html/html-spa/text\nconst report = reports.create('json', {\n  skipEmpty: configSkipEmpty,\n  skipFull: configSkipFull\n})\n\n// call execute to synchronously create and write the report to disk\nreport.execute(context)\n```\n", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "time": {"modified": "2023-07-25T14:31:38.537Z", "created": "2015-11-22T06:51:48.997Z", "1.0.0-alpha.0": "2015-11-22T06:51:48.997Z", "1.0.0-alpha.1": "2015-11-26T08:20:46.800Z", "1.0.0-alpha.2": "2015-11-27T00:03:43.341Z", "1.0.0-alpha.3": "2015-11-28T08:40:42.493Z", "1.0.0": "2017-03-27T05:51:27.819Z", "1.1.0": "2017-04-29T05:00:11.399Z", "1.1.1": "2017-05-27T21:13:01.462Z", "1.1.2": "2017-10-21T18:59:28.819Z", "1.1.3": "2018-02-13T05:48:41.587Z", "1.1.4": "2018-03-04T18:42:58.790Z", "2.0.0": "2018-06-06T00:50:17.658Z", "2.0.1": "2018-07-07T19:00:20.046Z", "1.1.5": "2018-09-05T22:28:33.701Z", "2.0.2": "2018-09-06T00:53:18.184Z", "2.0.3": "2018-12-25T00:37:59.940Z", "2.0.4": "2019-01-26T02:40:20.028Z", "2.0.5": "2019-03-12T01:14:24.713Z", "2.0.6": "2019-04-03T18:04:49.906Z", "2.0.7": "2019-04-09T23:21:16.284Z", "2.0.8": "2019-04-24T20:44:52.658Z", "3.0.0-alpha.0": "2019-06-19T12:17:13.670Z", "3.0.0-alpha.1": "2019-10-06T01:06:10.830Z", "3.0.0-alpha.2": "2019-12-07T16:41:57.524Z", "3.0.0": "2019-12-20T20:54:38.672Z", "3.0.1": "2023-07-25T14:31:38.404Z"}, "homepage": "https://istanbul.js.org/", "keywords": ["istanbul", "report", "api", "lib"], "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-lib-report"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "license": "BSD-3-<PERSON><PERSON>", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}