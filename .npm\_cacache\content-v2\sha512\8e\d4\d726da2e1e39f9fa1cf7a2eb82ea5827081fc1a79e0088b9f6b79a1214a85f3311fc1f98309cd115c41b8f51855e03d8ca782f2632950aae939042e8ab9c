{"_id": "@types/qs", "_rev": "767-f4afce18ed0303683204a23257b98e6e", "name": "@types/qs", "dist-tags": {"ts2.2": "6.5.3", "ts2.0": "6.5.3", "ts2.1": "6.5.3", "ts2.4": "6.5.3", "ts2.3": "6.5.3", "ts2.6": "6.5.3", "ts2.5": "6.5.3", "ts2.7": "6.5.3", "ts2.8": "6.9.1", "ts2.9": "6.9.2", "ts3.0": "6.9.4", "ts3.1": "6.9.4", "ts3.4": "6.9.5", "ts3.2": "6.9.5", "ts3.3": "6.9.5", "ts3.5": "6.9.6", "ts3.9": "6.9.7", "ts3.7": "6.9.7", "ts3.6": "6.9.7", "ts4.2": "6.9.7", "ts3.8": "6.9.7", "ts4.0": "6.9.7", "ts4.1": "6.9.7", "ts4.4": "6.9.8", "ts4.3": "6.9.8", "ts4.5": "6.9.10", "ts4.6": "6.9.12", "ts4.7": "6.9.15", "ts4.9": "6.9.17", "ts4.8": "6.9.17", "ts5.9": "6.14.0", "ts5.0": "6.9.18", "ts5.8": "6.14.0", "ts5.7": "6.14.0", "ts5.4": "6.14.0", "ts5.5": "6.14.0", "ts5.6": "6.14.0", "latest": "6.14.0", "ts5.1": "6.14.0", "ts5.2": "6.14.0", "ts5.3": "6.14.0"}, "versions": {"0.0.13-alpha": {"name": "@types/qs", "version": "0.0.13-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/RWander"}, "license": "MIT", "_id": "@types/qs@0.0.13-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "fcce54e59b5f71ebdc4f7ac73b13087ba3bca42f", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-0.0.13-alpha.tgz", "integrity": "sha512-mGJ5k/EauYbxFcC9XzVuNBMveY1oF5tiGr53F5u1BSYcKdaYyPh6qmxpbWAWwa1pFuQuPdaW1IhVpiOz7X5Ing==", "signatures": [{"sig": "MEQCIAi+/I7s0FgbYLqkK+NtDxnsfAxeH3QhViQvDQ08KWQbAiB2HR+oezIILGduNQbUcXbkZ7FeQMR2CmaFOrUoq4qlTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\qs", "_shasum": "fcce54e59b5f71ebdc4f7ac73b13087ba3bca42f", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\qs", "_npmVersion": "3.8.2", "description": "Type definitions for qs from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/qs-0.0.13-alpha.tgz_1463510492532_0.9493680358864367", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.14-alpha": {"name": "@types/qs", "version": "0.0.14-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/RWander"}, "license": "MIT", "_id": "@types/qs@0.0.14-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "925e193d56b1cbe4483b096c4aea16aa19293c2c", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-0.0.14-alpha.tgz", "integrity": "sha512-n1oKUaJDg10kWUP3/gFcWLkDyo8mzxUxW/EaY/81Ewkmg2JHMVmdIj2zBVV0w8hyANkNlk2WLKWdxVK2WiAQsw==", "signatures": [{"sig": "MEYCIQDSFtCvFSqrasQNL2byjCwjmxIzqcBU2sxths/ZzTyHPgIhAJnKg5CurirhYsUltruBwmxrhtFNUQTogTNYWbaCW9Dz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\qs", "_shasum": "925e193d56b1cbe4483b096c4aea16aa19293c2c", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\qs", "_npmVersion": "3.8.2", "description": "Type definitions for qs from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/qs-0.0.14-alpha.tgz_1463695552659_0.2905996909830719", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.19-alpha": {"name": "@types/qs", "version": "0.0.19-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/RWander"}, "license": "MIT", "_id": "@types/qs@0.0.19-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "54a15195ae1a689c07ee681b90f392ddcebccffe", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-0.0.19-alpha.tgz", "integrity": "sha512-RIhX1x5xllV2+Tzsr04Hq8h90syxIg4gqIrFeawCGSVBLp3A8xhv443M3yW0Kkr3kNE0vDPT7hYupVFcn05Z+Q==", "signatures": [{"sig": "MEYCIQCm66s32rs5ynm7QSNlcHIcJQsb9RDaC56NB6GSZomwOgIhAPzNj2KdWJN5EwPmAwoL7skgOSyQ2ZZ8igbRY/VBlyF5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\qs", "_shasum": "54a15195ae1a689c07ee681b90f392ddcebccffe", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\qs", "_npmVersion": "3.8.2", "description": "TypeScript definitions for qs", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/qs-0.0.19-alpha.tgz_1463776130502_0.8722766658756882", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.20-alpha": {"name": "@types/qs", "version": "0.0.20-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/RWander"}, "license": "MIT", "_id": "@types/qs@0.0.20-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "8b1234ce533191a38721628aa3f76c6cc576f2d6", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-0.0.20-alpha.tgz", "integrity": "sha512-N1r5Gnp4RDaeW7zsGNtbKOA6pWB9/J3+Ab<PERSON>+ynqZ892IESKjfzziNRaJ5SxQcAadL5qMnLzCXt+zXf0gYF/w==", "signatures": [{"sig": "MEYCIQD94X+/3+5tBjY7npPZd/Id30Kiy0fIEx70NUu8j8jMPAIhANQjkfLZtcIcSwI+mWfsA3uWypZGf/MItJt3YqHtZHMQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\qs", "_shasum": "8b1234ce533191a38721628aa3f76c6cc576f2d6", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\qs", "_npmVersion": "3.8.2", "description": "TypeScript definitions for qs", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/qs-0.0.20-alpha.tgz_1464155094419_0.9490445414558053", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.21-alpha": {"name": "@types/qs", "version": "0.0.21-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/RWander"}, "license": "MIT", "_id": "@types/qs@0.0.21-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "f1b3eaf470272cbb997b14d4e16e4d942c444e55", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-0.0.21-alpha.tgz", "integrity": "sha512-qLaC7hxG2VShSra/D3w03kfGAe/3Bo29b3o6DbS+uGeB367jOr8mzS+xzvN1Zi3colicKiIdNnbsD8+NJ0Z+ag==", "signatures": [{"sig": "MEYCIQDh4BSK7tHg3gcaNiJKLmkGKCAnDupeHc3ARXUAdT7b4gIhAIXlkYBuTXXSKZMPW6H9/SwNH1SK9kTcmmiZBzYGDp0H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\qs", "_shasum": "f1b3eaf470272cbb997b14d4e16e4d942c444e55", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\qs", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.9.5", "description": "TypeScript definitions for qs", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/qs-0.0.21-alpha.tgz_1467404709235_0.5510075448546559", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.22-alpha": {"name": "@types/qs", "version": "0.0.22-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/RWander"}, "license": "MIT", "_id": "@types/qs@0.0.22-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "b5eceeae66b562dbc7220743cfd2136cf01df4d1", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-0.0.22-alpha.tgz", "integrity": "sha512-j0w3qhLLjRP2LbppnLFS0gCK1mSnZxf/EDGhFMmwz/+Jxi2nvL3YU9XEp0EhesLoy5mIBWH70B2ULrdOcYArnQ==", "signatures": [{"sig": "MEQCIELfn6m9635QpbBIBsq8YRWfXTguvPxBnAwv5LpTuA5rAiBMV5uPJtLIZZkadQPWQYBzVAi6lYZd5E0qrZzoU7LxIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\qs", "_shasum": "b5eceeae66b562dbc7220743cfd2136cf01df4d1", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\qs", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for qs", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/qs-0.0.22-alpha.tgz_1467416805301_0.15239203325472772", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.23-alpha": {"name": "@types/qs", "version": "0.0.23-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/RWander"}, "license": "MIT", "_id": "@types/qs@0.0.23-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "799a3146baaf80b2ecff4df865c5fac976ccf483", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-0.0.23-alpha.tgz", "integrity": "sha512-7s9wEG+XhgBnLA8QbF3jjGEXY5UOmy9F0Hq7w+6IJ9aqQXcBYRUABL2rOYF5jPZHpCvNrgzwx/fyRgyDdg+g8w==", "signatures": [{"sig": "MEUCIAFtgyxiHhzFyWCgdtHg5WrKHCWpGjBJTRyLcE8ga7HiAiEA7LZptKOj3+tVDpNhxjfjGTCTMKb7zNDn5Qtpq6xGgDk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\qs", "_shasum": "799a3146baaf80b2ecff4df865c5fac976ccf483", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\qs", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for qs", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/qs-0.0.23-alpha.tgz_1467429334088_0.9419318749569356", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.24-alpha": {"name": "@types/qs", "version": "0.0.24-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/RWander"}, "license": "MIT", "_id": "@types/qs@0.0.24-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "240c77803c13c90ee612f648d455d5f0cfa8422e", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-0.0.24-alpha.tgz", "integrity": "sha512-hca8okYZaCBgvsEkZ5uwl8ZKElVAobJ7m23gZipJjOCG6adfSguasSnGKqqPP1jdl2/jUDFI2bVF1QpFdt4egg==", "signatures": [{"sig": "MEYCIQCpxRcIdW/APbPaN2/fJ9HeJ1qjpBf3x315Xiv3vW3wkQIhALfP1s+KhjWVDnJCBUnVegLYxgozwYh5bfaglKVvT2W0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\qs", "_shasum": "240c77803c13c90ee612f648d455d5f0cfa8422e", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\qs", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for qs", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/qs-0.0.24-alpha.tgz_1467594747459_0.32902303943410516", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.25-alpha": {"name": "@types/qs", "version": "0.0.25-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/RWander"}, "license": "MIT", "_id": "@types/qs@0.0.25-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "dcfb110268eab8271692504cd115802d64fff816", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-0.0.25-alpha.tgz", "integrity": "sha512-TyWKO1bE7jSzAqd934N8tWlSGX/Fa78glKRWf7W6sbMpVF352cAk7zmfakqxXeyOXUtbzkA3JXlTCUfTliIqbQ==", "signatures": [{"sig": "MEUCIQC09rPG6DRvwl4ft7ipd9/Za+vkQaGbXmasJJcYQ0CYGgIgekdRFpT5tmplNyn3LL+DAjrKkkX3qGQPEAgbBY46fVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\qs", "_shasum": "dcfb110268eab8271692504cd115802d64fff816", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\qs", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for qs", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/qs-0.0.25-alpha.tgz_1468012390094_0.05250539514236152", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.26": {"name": "@types/qs", "version": "0.0.26", "author": {"name": "<PERSON>", "email": "https://github.com/RWander"}, "license": "MIT", "_id": "@types/qs@0.0.26", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "ccdd11a2708f960921e4bc4c278b4130c0801cd5", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-0.0.26.tgz", "integrity": "sha512-IYhm/chRkt5HOzKdlFzuFuYBne654XkexV6usgD5lVAYid0Mw3TM4YKsf5+aK5edtY5C18YXFlggXd5/zSEnjQ==", "signatures": [{"sig": "MEUCIQDxO2oh/betJEs/8k4SjPjublq/KAIEJMfTyZdbzFmjHAIgCy/1iY4hCQ6O9LVSDTgYZFD/PBNt5pnN1nWp+Udg9uI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\qs", "_shasum": "ccdd11a2708f960921e4bc4c278b4130c0801cd5", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\qs", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for qs", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/qs-0.0.26.tgz_1468511285770_0.5999755023512989", "host": "packages-16-east.internal.npmjs.com"}}, "6.2.27": {"name": "@types/qs", "version": "6.2.27", "author": "<PERSON> <https://github.com/RWander>, <PERSON> <https://github.com/leonyu>", "license": "MIT", "_id": "@types/qs@6.2.27", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "657d7d24bcab16a65013daf93637f11530068d6f", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.2.27.tgz", "integrity": "sha512-iyPbi9TPHGGaBr5CRIFXGCSrBwvofgFJSe2k8pGzY/1bI4LU6sibVYlGfBmVcEiZW93HI2JQl5qztD94aMleQw==", "signatures": [{"sig": "MEUCIQDDKteX9e02CbU7SQyKnkIL2BNaZQ1DLmTk6I7FqbHpmQIgM6drBT/cOyJlIh98098amSA4Vy13xE49UOn3250v3Kc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for qs 6.2.0", "directories": {}, "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/qs-6.2.27.tgz_1470153915856_0.4501737637910992", "host": "packages-16-east.internal.npmjs.com"}}, "6.2.28": {"name": "@types/qs", "version": "6.2.28", "author": "<PERSON> <https://github.com/RWander>, <PERSON> <https://github.com/leonyu>", "license": "MIT", "_id": "@types/qs@6.2.28", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "fa456179672948b489040a9f8cfece2c2a726b0b", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.2.28.tgz", "integrity": "sha512-dspOCW54TXTTsIt02IjygJ1FIk+xOxoMpUed6dwHsh4SJkcnHUh/pG9aeKV2/0LsqW3Bh5mP4EvLGWlrAey0kA==", "signatures": [{"sig": "MEUCIEk8r2JAlZNiVLoIef9P3ca6Hc7sBdQl5behPxYhGxCBAiEAz1sJ2KUG24hx7ZbMtyy//5611Kny2wu1Dmt+AHk68aA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for qs 6.2.0", "directories": {}, "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/qs-6.2.28.tgz_1471621074131_0.7587569085881114", "host": "packages-12-west.internal.npmjs.com"}}, "6.2.29": {"name": "@types/qs", "version": "6.2.29", "author": "<PERSON> <https://github.com/RWander>, <PERSON> <https://github.com/leonyu>", "license": "MIT", "_id": "@types/qs@6.2.29", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "6a96daaa88f338fef83ebfa63bbe2080abbb5024", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.2.29.tgz", "integrity": "sha512-daFW7YCRKkuzXeBB9w22rzv56hAuk67LmMgfVr7RUkalBiV8oxWBHQeQ8MCVEn+9L0nLfMGw5zALfOT/6WPDig==", "signatures": [{"sig": "MEYCIQDNDwaloCaD/rGEZMNIFGJLI0xndwN+pdJPy3WpYyCSbAIhAKkI1TLModL9YZqw6JbiBb6VMDC8RcU23nWizxX6At/3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for qs 6.2.0", "directories": {}, "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/qs-6.2.29.tgz_1472151246113_0.9667742482852191", "host": "packages-12-west.internal.npmjs.com"}}, "6.2.30": {"name": "@types/qs", "version": "6.2.30", "author": "<PERSON> <https://github.com/RWander>, <PERSON> <https://github.com/leonyu>", "license": "MIT", "_id": "@types/qs@6.2.30", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "139e247511ee3b9be539cdd553c8fdb33c1f624e", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.2.30.tgz", "integrity": "sha512-b9t3LvflbPKQDoWa9SVun64hBJFJV87J28tNLgfxJQEm0gFK9rrq2iO7b4Ot1N2OgnQaRrAqowJw09D0kUZwMw==", "signatures": [{"sig": "MEUCIQC2t0upr0RIOJ/kzjDBVM1yC3WwyxxucuM+WvW44WTfTQIgUv+G3PFwuzj+lbe9uJIeWdMrMIS/1OEGdW7ExJarFgc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for qs 6.2.0", "directories": {}, "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/qs-6.2.30.tgz_1474308278249_0.40291365422308445", "host": "packages-16-east.internal.npmjs.com"}, "typesPublisherContentHash": "c147281b90dbbf0ece118d3dd3fca9b1f09be8841470e0e6e7d85c12afdd3f99"}, "6.2.31": {"name": "@types/qs", "version": "6.2.31", "license": "MIT", "_id": "@types/qs@6.2.31", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>"}], "dist": {"shasum": "7d929bd877f9cd3ece6415c602b7cf9b077133f1", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.2.31.tgz", "integrity": "sha512-8jiXc8Yoe5tL/K6wvAvXUmgk0i0yOtSv1wsm0AvjMqliB3sXlIE+BODf5ZTZZYeTCk3g1+BBo6bOSNPKMkjBuw==", "signatures": [{"sig": "MEUCIQDR24fr9Oy0tKLx5VqnOusIMp9O1heqialXdTCn7Y4zhQIgHmbDgRFJq3WfURpYJu2zh1yEqwLbZKIoXXEAoBTN7/8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/qs-6.2.31.tgz_1490142844192_0.45917141158133745", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "2c1b15c8980e966cb10231b9636ba421bb3e5bb9a7c0beb7d7bfd83ef0c4afdb"}, "6.4.0": {"name": "@types/qs", "version": "6.4.0", "license": "MIT", "_id": "@types/qs@6.4.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>"}], "dist": {"shasum": "beff9f6a794302e6e07370cc1670049f322d9a32", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.4.0.tgz", "integrity": "sha512-i0lBxkBCA0RE53DiIzupsnVTf946UEmX2DeafTMT37lrlKeu+pVeUD7lQ6O8VnVvHAqx/cdJ6CQBIvmmw6KGsw==", "signatures": [{"sig": "MEYCIQCEFPnEaZlCDqdqXCSPxOThQcvwBtC5i/4emOdMSKVYxQIhAPJ5phZEqrHJkw9HCe36J3qXXFCgrTmaAIIa/PO4dp1u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/qs-6.4.0.tgz_1498859186668_0.19269120157696307", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7d8fe9da45754879a02e884d6d8e6d5ee8f4a45039a04eaff2076b0cf945fa88"}, "6.5.0": {"name": "@types/qs", "version": "6.5.0", "license": "MIT", "_id": "@types/qs@6.5.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>"}], "dist": {"shasum": "14514e8ee24a4d0672c0c0da5e01a4f11da60d71", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.5.0.tgz", "integrity": "sha512-KCvdV85GNkZAyWOYxUUR+3XNairGv60Niu42GvVy9JlXRPEIZHXQuk0xMi/mla/25pLZzdY7uzLhBK/B81KZBg==", "signatures": [{"sig": "MEUCICm4+xnuT/J5HqQprt1hdnhgSXXWcu6umzzYS/kbpGK0AiEAhIW0xIJaNf6+HlbVLu1ouaMaYdKPTp4lvsIuSuDOal4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/qs-6.5.0.tgz_1499265099734_0.744535295991227", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3b36c396217f35b49132c8abebc0a481a5103f79bf764a7401976e31c13dc77e"}, "6.5.1": {"name": "@types/qs", "version": "6.5.1", "license": "MIT", "_id": "@types/qs@6.5.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "a38f69c62528d56ba7bd1f91335a8004988d72f7", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.5.1.tgz", "integrity": "sha512-mNhVdZHdtKHMMxbqzNK3RzkBcN1cux3AvuCYGTvjEIQT2uheH3eCAyYsbMbh2Bq8nXkeOWs1kyDiF7geWRFQ4Q==", "signatures": [{"sig": "MEQCIFJUvws12eQwQR08uEIzHeGni9UW7MaDHVwJFE/FEqnCAiABPK0/Z+MGpumBkrhJmvo636qf3zQ33ivFydb4amLWng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/qs-6.5.1.tgz_1508948541515_0.9812304803635925", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "95a1ffa7727211a2cb92c8a426f7c630833d679c2566dc382b1af9c0126c7318"}, "6.5.2": {"name": "@types/qs", "version": "6.5.2", "license": "MIT", "_id": "@types/qs@6.5.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "7f347062655056662845ba4bb79dcbfdc382cd61", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.5.2.tgz", "fileCount": 4, "integrity": "sha512-47kAAs3yV/hROraCTQYDMh4p/6zI9+gtssjD0kq9OWsGdLcBge59rl49FnCuJ+iWxEKiqFz6KXzeGH5DRVjNJA==", "signatures": [{"sig": "MEUCIQCHJNPEtVM6t7sK7JUlQ90LDbtTskyQHmcEORJXdS+g9QIgfT9mke/0iHWrmX5SQZMCO7yZBqtfdk4UtP6YIlxHvDM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5007, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcedfXCRA9TVsSAnZWagAAbNMP/RndGbFs6D0Dc6XlDL2O\ni5aA+kEDoekEkY7Sa4MrV9A20iZdfbB2JP5d3V37wqYsuuPmQvrH6WJPrX+D\ngYa9WHmUI2QevR/9WBPDDFdwLXQeUGZS51i2Oq26+iRNR+4Lc+o0q6Xl99sR\nekcTkIC+R89dfVjbpK4ScAzOmLMvzXnaarP6H76tWEOyKcyVBO2C71vVBpvU\nyuRKqjubB/vTYUegB3cyhnO99RDwmsFKikj4oYrQDmhNgfchl9B+YMMtHJjz\nCB/Qdm8+OgkFVso910ZJFYdLfxIMz7Hd8Sat71Dvj65VIneptxK2akg6NVYV\n1oX0EJNmD6Ssmd/IxGor87AeNMvevUZIkCDIovGN7cJ+a/1gOxDTw3cAkmD9\nkw6Fkw9XC5kP33/aQOvBzNQGuC44G8ILihVNhrDDKuqA20pQubQQjQMYeUpN\noSs1n7G1oisc/YxHBJZ7SRQ+xI/sNvbXPGZhmHntjBHZimtX2txAzx03EXc/\nOcoV4cf9XJ1CirGlWOEC91j0RFsBnizA+E9+h5ssoco9uzaboHV7kHznhrln\nN3hWAqIdmdaOvjWw67yIXdRM2NB7IcaOL7di6I3bt2Xk16Dc1rkyr1vyzkLA\nruoTOiIo++uHo3NIAAs9nEUmhAJLNGiFKRN2AehydR8rQnKfBjgQvXwP3Guk\n3bon\r\n=MmWI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/qs_6.5.2_1551488982678_0.7751073909187627", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "669fb437595aff9b360ad8d02c1bf6f69468a6778dadc3a08ca6e132b55c740a"}, "6.5.3": {"name": "@types/qs", "version": "6.5.3", "license": "MIT", "_id": "@types/qs@6.5.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dpsmith3", "name": "<PERSON>", "githubUsername": "dpsmith3"}], "dist": {"shasum": "1c3b71b091eaeaf5924538006b7f70603ce63d38", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.5.3.tgz", "fileCount": 4, "integrity": "sha512-Jugo5V/1bS0fRhy2z8+cUAHEyWOATaz4rbyLVvcFs7+dXp5HfwpEwzF1Q11bB10ApUqHf+yTauxI0UXQDwGrbA==", "signatures": [{"sig": "MEUCIQDWjRDvgK00pMJC2B4uRjzg7VD1KbRJH3vuCELOUJbEvwIgEvQA/sG7blpa2VfDgM70jhgkRqv1HwJJgEBZci657mw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5275, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrNSOCRA9TVsSAnZWagAAJOMP/2ckD27C1hH72NO9WJiH\n/pH5Tx4sRiRPs8IPtVizO7XtHQ1MwtX/FyjZjmXGnJIYYJGebTlQOZsg0Qnm\nWlDD7hiTpSaxRxAJoZ21Z/wZAvmqjUtzdju3lj27iHjx2gx+VJPUaGMlUT4f\nwIK3ozLxm7f2hMZ/TJN/FtSlitSZn+xvtDQkqsGxvIMt/MDvhFpBG4C+uKi0\nA5tpO5lH4YfQR0fxM1uWyijtbiJeI1jw1dD/o/PnV5A3qQxSaKRUv9tkiD+Q\n/K6VWMIDcc4MARLqt/h76r3rNlhOUj8qKde8MEb7vQpzwI5nzh//1p7QPt5b\nTNqHzDNpSf8go45S7VAOMXsAqWNd7ng11mPSmjwKEUqb5UeuQRg8j4ltZ4gI\nuOrNfothT1UspUSOY8Bjv52/o5xeUtvAoemLciL/yFRSJX2SDOz3uQcBcm3N\nPCOdHnWK6uvF0m39T+uWm5g/6FtAz3Cu12KjwcPSI+P5r6KcMYPkqMO9meYg\nETzV79FVqsG4DIS3qeGAndflvv0N4cuIwJ2exaAvrj3qgCfayaqd0MX2g0yu\nHV3+wulQauk4L1uPrzBmR+XTl2fe/HQ5HUW2hxdst2QqCPKmryqsQ/ql7IqY\nAgdR+4oqrmXlLsNtFMtJrHuZhsIFRK6rX0YiAa7D8f/khJv/J76Qbf5ADwki\nLzQt\r\n=Yr7G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/qs_6.5.3_1554830477926_0.1822251065721252", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "faba65fe1700af3d6d1582b1ece4d0889a09d5b9d2f7d622a0782c78aa05c699"}, "6.9.0": {"name": "@types/qs", "version": "6.9.0", "license": "MIT", "_id": "@types/qs@6.9.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dpsmith3", "name": "<PERSON>", "githubUsername": "dpsmith3"}, {"url": "https://github.com/hperrin", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON>rin"}], "dist": {"shasum": "2a5fa918786d07d3725726f7f650527e1cfeaffd", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.9.0.tgz", "fileCount": 4, "integrity": "sha512-c4zji5CjWv1tJxIZkz1oUtGcdOlsH3aza28Nqmm+uNDWBRHoMsjooBEN4czZp1V3iXPihE/VRUOBqg+4Xq0W4g==", "signatures": [{"sig": "MEUCIF00Hk+VJCjCUggwogKZyHytvzOequ4jWnjm0zDIibEDAiEAqLYTV8wSCGkiDoYk7FWqfBJCuIfOWVY8K829yFCSXTs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6058, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdxIg2CRA9TVsSAnZWagAAhoYP/3hJwwk4iRVFba1QMyGD\nbZB8pWDYfUIlm/eMUDtvnbbiz35SMp7mJlLjxDhrQp4QExZoKbKZPa4JRs99\n0iIYj8SHChmSvr1wfegFWX/9Ya5aNm+wMrOhUy3MQ9+TI7307JhYkqv1tGSM\nCGMsblQgzOiCLsQXwKYj3AzmonVBAkrQcxVkP/AI+fcZJ1p+4I9qzKtiYUM1\ncthnADNyB9aSSgkEvBIXVEZrW4kI+IjIWMz5hgcLeja9U1vfEpFVGmjfsu1T\nw1MXLzWMoVt7PemPdGLDBUooFhxKGP2effo5IGXaCk3bwSSwt/qBLgcy4y/x\nq5af4ZeAJhC3B23952m8VGwlPAeuJmUGqTBxhMLj/Pxp8JJRRnuzYa8v1x4R\nQHYtfb19W2NYmdfHkpp79rm8ICeXC1+ZlGqH+iS0oHL4CNThg5pquu00DUud\nvPEp/oQ3kIc4JvXWSDJb8KsIvjw20ReGRBWTZpxbKHa0Nzq8t5hrP5sQMDu4\nKiyfsEcBfKwJ5PII8i55DShQwXF/cdPPM7zkU/gK1SgOuGRIgnjMWO5Ex7yL\nfsTVtoH1p0U5K1/u40TT2Ush33C+4o91W82UbHIlZtU+0IZg6JsLXHWdZLix\nyx2tGR5ROqu2WeVSZb7slaq0AMHudh9ZMkmIBU7SAh+XdbPeRrd4IRpRORPU\n/cXH\r\n=KVfj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.0_1573161014086_0.6510885201593517", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "01d1eaa081866ad9ac523018dc2189ede33eb5ab2c5a891d4ab4a3f90f08ac5e"}, "6.9.1": {"name": "@types/qs", "version": "6.9.1", "license": "MIT", "_id": "@types/qs@6.9.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dpsmith3", "name": "<PERSON>", "githubUsername": "dpsmith3"}, {"url": "https://github.com/hperrin", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON>rin"}, {"url": "https://github.com/ljharb", "name": "<PERSON>", "githubUsername": "lj<PERSON>b"}], "dist": {"shasum": "937fab3194766256ee09fcd40b781740758617e7", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.9.1.tgz", "fileCount": 4, "integrity": "sha512-lhbQXx9HKZAPgBkISrBcmAcMpZsmpe/Cd/hY7LGZS5OfkySUBItnPZHgQPssWYUET8elF+yCFBbP1Q0RZPTdaw==", "signatures": [{"sig": "MEUCIQCLQKsK3wL5Qd2o7NxUxkLjmZ097VjYT109fM8GZNdz9gIgOveRgtAEFwWoTx7CcnqCZZNzdqJ3UE1PF03qWQCdV18=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6317, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJePazWCRA9TVsSAnZWagAAT98P/2wL9Xc5q6c1xImoVnGd\ntJydVG1Tw3LIdKtnjaDLC3fwOMLrUBHGFX6fTl45JukOQPE5S0ysDlQARAOs\nok3wYsTXTv7CQ/B6g6PDGx5GCvWC4nTlqSEwNJCbMeDa0cc/sDtLv+00Ocjq\n7/c2NQaLVEwKNrQps22SMnmy5tPTdMS4I96euDu54k0T1uZVplFv2cmnoFFK\nq9R4I195ZoirmoyYdwAFntu0f1yNoCXK4sfOrDQrwI6tJLpvlIrClfQP4bmu\nivBU9VPfyaKJHiAgPSQpaE3qgPf+3VaUnZfrgl5QxjLVH7Zj4KVOk7HZKfQi\nqe9KTbxlZfkzqv90PfNXzo6SC3S5yeCbhdtOIQFZ9GUhRsaJt+3M1ZkWXUy3\njyZXTnrokXhLQxXEqSJJ7Fl92R6JK5DQmxxdTP1mS4CJME1JLcUxR/pYMqFf\ny53VI+R1h1KlVD6XcJ3TIwg5OgmsQSaxPpdaAPsCIGJBwAWDGwPIc3EHb2ig\n9IMasSteBAQDnRf8fjOciTxD27EVMIHlPSYyn2VSq8aBQeB42O9+zFpdxIo7\nLrNp1WO7NcCTyuGFcPgjd84QCWccaQskmWfieSswbCOKW3qKI2ABiSKHpH6E\nWuKHB+nBIGP6c/J9WjiPk/tLR7KzHvofLof4Hp2dZlY73RTTcBS9vxSAn7WD\ngLMk\r\n=2UzZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.1_1581100246563_0.9208238833836064", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "2dd3b4ff02c2d1e8d50c50d81e4bdc93231041cc7c8bfe36b5cce4558ae16bcf"}, "6.9.2": {"name": "@types/qs", "version": "6.9.2", "license": "MIT", "_id": "@types/qs@6.9.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dpsmith3", "name": "<PERSON>", "githubUsername": "dpsmith3"}, {"url": "https://github.com/hperrin", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON>rin"}, {"url": "https://github.com/ljharb", "name": "<PERSON>", "githubUsername": "lj<PERSON>b"}], "dist": {"shasum": "faab98ec4f96ee72c829b7ec0983af4f4d343113", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.9.2.tgz", "fileCount": 4, "integrity": "sha512-a9bDi4Z3zCZf4Lv1X/vwnvbbDYSNz59h3i3KdyuYYN+YrLjSeJD0dnphdULDfySvUv6Exy/O0K6wX/kQpnPQ+A==", "signatures": [{"sig": "MEQCIFufXbINIUOGLKuPaHJ0eVB8rhhEk23lDI5xYRGDMgE+AiB+3hrGvWMXiXU1KiwbWIBXHc8/fZTLJSVjTLBxSX4IqA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6688, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesZi0CRA9TVsSAnZWagAA6doP/jbNFiGUj+qggco3eQpm\nJ7xmBJgeDeqMbL2PDg4U5vf7sHQF9Ip1d1aMC+bzGpVtyIoxzgZdExjaBx/a\np8Nyt3zaEHL7E5B1Nzv2cl0m+UA/NmuXiz5tg1TfCtAjUKmdfWAFYzNb9I/n\nG/8ywuu99GRwCGq6wl1pIsAenmk5Z7crDnNWEbHIOylSUd7GcDhTM+PQ23Fc\nG/9lTT0R5yolMNZAruNy2HTWV4jeORMP88jbuaGNUHAfQDTybSqrWAGEVSid\n959T8P+24G31s9F71zBLLPXeV9CpcCCnzJ8TmTh/oxm8UBTXSx+b7+d+MSc<PERSON>\nKod3VOFn1genVWY5ezktNMLATAuI5UDKDWWNUx57PwzvOPFN0+BpmomuAGIR\nE3kAbEa+0K7pbEsZ2hk3x6VAlrBq+s8OWGWmLaqWWqMeS+TXXUvl2RkALoDq\n1l0BWbj2TxikKi3YLfPuA8u3WOH26XM9DK1Xxd2+PjwA5qGRxsMNAaHWeo5G\ne9Oa6uqX+SDD7qZyPWkq0bjGOj1GNFq9QRkVT1+h3wzjhAbut5s+ALjaSGn9\nhrFcjSShucIlb5sV9SBIWsopuZOcJ8kbLc5KGx/a4FYMGUdTeQNVyEfI0Ltl\nVwI1os1ZUB1NE+FvAwgvf57CJ22fLFb8P5H1mfgwxHKOKvAbW9eY3aTsciix\n6uFY\r\n=IlpQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.9", "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.2_1588697267780_0.0036578068519343976", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "6ca7866c8cbb7803dc311abd6a2d41bc9926b3646819fb2cec2e0d32d0b5b29a"}, "6.9.3": {"name": "@types/qs", "version": "6.9.3", "license": "MIT", "_id": "@types/qs@6.9.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dpsmith3", "name": "<PERSON>", "githubUsername": "dpsmith3"}, {"url": "https://github.com/hperrin", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON>rin"}, {"url": "https://github.com/ljharb", "name": "<PERSON>", "githubUsername": "lj<PERSON>b"}], "dist": {"shasum": "b755a0934564a200d3efdf88546ec93c369abd03", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.9.3.tgz", "fileCount": 4, "integrity": "sha512-7s9EQWupR1fTc2pSMtXRQ9w9gLOcrJn+h7HOXw4evxyvVqMi4f+q7d2tnFe3ng3SNHjtK+0EzGMGFUQX4/AQRA==", "signatures": [{"sig": "MEQCICE4lXM8bSYIfKaweGHYJehEt0tdDE8PcCeoJkpWBCdzAiAfrOcGtpKDtqHTpfGIXMfPxOLQ5XJy/KATM3wSeYohrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6700, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJexBs4CRA9TVsSAnZWagAAALQP/3y4qHElRksyvw3b8iQR\nUZzCrTXJwcXW9Et7r8SSFahbCshOqM8w9GdMvhULCcLreC8p/L+sMZCiiFkE\n0pTENEBLMDFqnVkiEwYusXwxbwFDlbIi3YkmiK5a/4XE2pI5tg20512flOze\nFkPppzLYkGQX1y6qAet7buUeG0FkKPvUfbkiqniyPYhZ4Pw7a9h2OZaCwHu/\nEbv/R0BvrV7c32ukFrHYid2xnSn9RcnAGzl+wcN5+aZNHbEO0PWtcZap4t/7\nkHqb+hdtpMoniaNXVYYbNgih6gPBsQXD9Tza91jL3vR/1LqrnzK58wNE6bGo\nNcyb0fqgWl/sAtQbHhOseftuSFrbNrrg+Ijh5N08+MlF4hPEX3DoVLEfjzOe\ndJhN1UoItzU6jdNqpv/iaMgHs/EXMmnQItXvfTI4uSbGB3isAXUhwa01io/9\nCs88+mLc1IMuvxZWbIkZV9xpCdxzaYOe2F8n5DRoRDrgEnUBg2tNsBl7La6J\naBoYmrddo38v98GvTfRue5t1tUK9yEzdF6qzNQt6JoCaXrci4yvune0Ngp7F\nLZS6SpBBKwpwWSa/hGegbFJPxMNkVe0diug0dz6QU5lBJ+m8XkUYYufGQ5je\nPLMUhefZ5PK0JemhvleYizTvQ0WsOJyFyonPCkWJo/dsys856MKQWQTUmV6/\n/46L\r\n=U52i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.3_1589910328530_0.0676490795697342", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "28ce4a78d1d9136225b1d31700c036c2721ea028ceac960f4a8a3427effec34e"}, "6.9.4": {"name": "@types/qs", "version": "6.9.4", "license": "MIT", "_id": "@types/qs@6.9.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dpsmith3", "name": "<PERSON>", "githubUsername": "dpsmith3"}, {"url": "https://github.com/hperrin", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON>rin"}, {"url": "https://github.com/ljharb", "name": "<PERSON>", "githubUsername": "lj<PERSON>b"}], "dist": {"shasum": "a59e851c1ba16c0513ea123830dd639a0a15cb6a", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.9.4.tgz", "fileCount": 4, "integrity": "sha512-+wYo+L6ZF6BMoEjtf8zB2esQsqdV6WsjRK/GP9WOgLPrq87PbNWgIxS76dS5uvl/QXtHGakZmwTznIfcPXcKlQ==", "signatures": [{"sig": "MEUCIHqW1UU2tmDT6jKM8E5ieTAUrKe8tmK+FL6kg+6g54sbAiEA89SnEStkEKM/yv/VX+5INUrxw07oKBoNknloY2q4iuE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6871, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfG1UxCRA9TVsSAnZWagAAOYMP/joOHjlHg2bFvo49u43h\nBC0Xr1b23A07ptQvZGWdsviFDx2GJx6tnNTW3O1TMu64XTS+kaRO/IS0lqpf\nMjDREp9IUl3fVs09iF+olhUJrkpQB7k3wWe+yQY6UZeuBNJiYC2D4xgvcN0Y\nz1zn+M03vQpjbPiicOMk7hqhAf5afjuIdTLKAPEkSfuZIxHul/UycMCcIw9Z\nBK2d5u7iJDC/ETPMreNY8dc0+2pyhboxi5oXq6EFovniIiZPJwhTAav6CUyA\nH9iI9/qpcW2YOhxQcPAkRm5W22pz3ReDW0GY1tPeq49RDksPr1+oOU4uzKez\nbzMsMWxUamLbafUu+nnX5JcwYLlleX6T0dz87xq1xMHWP1kb+n6No1DVvX7S\nXLtcNbMasKzugzL6WUTlbO6V0+zF+CbhPJQUHpbVcIZUQe5CfEFOlpRjqSaF\n942buw84mkT40JqUrDh3dARZfhWKZW7Of4pYS0kRvk+NHCT/mqsu3CL0NF1h\nTQXN739Z3KYiORMvo7LtD0ZCeCPSxvt9R+KlH/gKF+0mW9VNMsnE1C8q6i70\n70SUMFu/68fgSqByjtdYnzXF9gQ4L0cGpnAhK2Kfh+4yQURSoAGQrkJay7cJ\nm6w1mFkIv5BkfbJsSIY9904qtCCNon3FHJOh2mP726YU6on86o1PBxnOyBhd\nS0Re\r\n=Kt0I\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.4_1595626801033_0.06675818988454085", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "439bfa44155bda6199294078b4c73c6e38042c2b583f470b75d740a50e4c7125"}, "6.9.5": {"name": "@types/qs", "version": "6.9.5", "license": "MIT", "_id": "@types/qs@6.9.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dpsmith3", "name": "<PERSON>", "githubUsername": "dpsmith3"}, {"url": "https://github.com/hperrin", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON>rin"}, {"url": "https://github.com/ljharb", "name": "<PERSON>", "githubUsername": "lj<PERSON>b"}], "dist": {"shasum": "434711bdd49eb5ee69d90c1d67c354a9a8ecb18b", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.9.5.tgz", "fileCount": 4, "integrity": "sha512-/JHkVHtx/REVG0VVToGRGH2+23hsYLHdyG+GrvoUGlGAd0ErauXDyvHtRI/7H7mzLm+tBCKA7pfcpkQ1lf58iQ==", "signatures": [{"sig": "MEUCIQCs5ZMdx1pMRPVpMsTxSjIsGm6jjNbWI8TQwB+fKytoLQIgNfVMxM7whYJk77ZVDyMQ87uRF+Ca4/Ekb+zBZjNtC1o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6493, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYppgCRA9TVsSAnZWagAAajwP/0J2jzqtzqP+JQzlklTw\npYmPfTX7ZbhNCWk07HDILHXty1gcCkRlrgzx6dlfKrpieDAJoy/XTPmHBAzC\n+Uex/vGXw5kCcV/USf9DHOzKaqTkQC49CCmBpnX2SCJr4/I7zRwy8bZEED+2\nC+yKRVAM4Ziva3TfyzK143xc2JVHG/URwspSqMr81VPASXUsVs0FWme0mVPv\n5vUCmlgbI0aaleAmYmcyOLecjRJgDjCU0zu3c0G/dbQt1tpuIQy2vXrFRIrh\nAi180F7FLiyY14/n1WQj4tpgR3mIHVn6OQCEUmHjBCpMBbCUtVqqGgugl14X\n3GekE2F9aBfRYUV8rTV47tpMFFzouoz8fo9iMtAzgcd/ZqxrXBZwjhvBzwBB\nyotA67L15stgpGqBizwqfGSwvKxfzmvu+aBu1Pnft0jhb6E967OfpbE31XI2\n9bfg6j6YdE7dBuiFjno3cD8A/TNx8RR230vYK7NpcCiaqON6Vhea5qvnv4DS\nN+aSM7kur3u1RjnHs7QKGZKXDpxZovGW+QqE9pEGvdtDhzonvwFF/gNwJLTi\nGngNCYSmGY5SC0BffmVAf8THMTjhR2ba5gdb5u2/tnX2K9il2rrEhv1XhWPz\ni296KY34x6JAdqrQGaUUOamKtiH589RfwET03+pJ7E7l3OEBqjpLmX026z6I\nQhLK\r\n=X3td\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.5_1600297567496_0.8975438368869579", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "1a8820a6aece2344fa333148c105b71a132db5e68f839c47934a78889cd44574"}, "6.9.6": {"name": "@types/qs", "version": "6.9.6", "license": "MIT", "_id": "@types/qs@6.9.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dpsmith3", "name": "<PERSON>", "githubUsername": "dpsmith3"}, {"url": "https://github.com/hperrin", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON>rin"}, {"url": "https://github.com/ljharb", "name": "<PERSON>", "githubUsername": "lj<PERSON>b"}], "dist": {"shasum": "df9c3c8b31a247ec315e6996566be3171df4b3b1", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.9.6.tgz", "fileCount": 4, "integrity": "sha512-0/HnwIfW4ki2D8L8c9GVcG5I72s9jP5GSLVF0VIXDW00kmIpA6O33G7a8n59Tmh7Nz0WUC3rSb7PTY/sdW2JzA==", "signatures": [{"sig": "MEUCID8SnglkWhg8KXDTeqJHJTeSrEpcuAJVpmJI7sTwvhjYAiEA+MfM0DesLcJR6/xCy0DFDPuYcv3fsbEKsSt9e43OwG4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6518, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgRJwoCRA9TVsSAnZWagAAo/YP/jHg/i9RgWfGocwk715e\nC5A5/Ll3QbRzmOS93CNJeTh90tr59xziY1/rfWZ30dn1rlCRGdeuuqd8klku\n+htFsWVcZWzU0Mv1eBZuQ/D7VLBkZrZw4J2ba42lOWyvz1OwSXMawwTpLNM/\nPrQTuCoc7Z7igeguUB2Thl4xjsgBTJLn61j4Ls0b5YY9HA/oYDxSno82sGk9\nlKq1jXoBQ8abNlnh1chmzxV0GrO/pmz1Zqsi8MVLpp8oXequBXRk0nQMKBB7\nAWWD9el/qKZuPFpgBqzZZKWwCkNc8j6MpVNiNMbiMSU2sWrHTcuPWEhfK/2+\nkTzYe9LKqYPQpYtxbbu0A5GeNBMA/vLn4Euwg8FspS2S9QD2I+QlDrLwvM8l\nlcluyk5JD+szj0WLyQ+nEK7FyaW2NFUGtfObtQijmHi6NiNF1+n60XhOZzuF\nZRCjn47nv+sRh4bb0mbS81TpKrEKgB+aTzSHlmVnpXJy0UyMgXd67ux96Cnv\nlogUjy8IOyqrb3wwLmyKE25bpW64Muti9AjdCT6QI0zbPYImhb+pg7bbUDmh\ngHCVu53sdZcB3cSJwtZFB3PEI6qnc/IryTY9nERSfVCszqp8ZGg0uinKb+nY\n2pi9xWaeteRi2BfPFsopnveMid5tdkKs/5+FkOzV8hgwq0XqNWDC07Z4rPdk\nmgYU\r\n=TjFL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.5", "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.6_1615109159559_0.781636968957756", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ed1e4bee2cc164dc642a0181f4dc67b58920c0255611cd679ffdeb6ba5ea7ff9"}, "6.9.7": {"name": "@types/qs", "version": "6.9.7", "license": "MIT", "_id": "@types/qs@6.9.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dpsmith3", "name": "<PERSON>", "githubUsername": "dpsmith3"}, {"url": "https://github.com/hperrin", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON>rin"}, {"url": "https://github.com/ljharb", "name": "<PERSON>", "githubUsername": "lj<PERSON>b"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/qs", "dist": {"shasum": "63bb7d067db107cc1e457c303bc25d511febf6cb", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.9.7.tgz", "fileCount": 4, "integrity": "sha512-FGa1F62FT09qcrueBA6qYTrJPVDzah9a+493+o2PCXsesWHIn27G98TsSMs3WPNbZIEj4+VJf6saSFpvD+3Zsw==", "signatures": [{"sig": "MEUCIA+B9JeoNp54+O6zw9DU38ywJMfIrT1K7sbzZLewSqP8AiEAi/s0fAFNQ0XMPginwPHwJQGvP+8YBvAlG3+sT4jb168=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5eKACRA9TVsSAnZWagAA4zwP/1SubwjLZHLe8nLYiXjc\nOGOSOUlLUD3CvXfTE7peOeLcQqBjMTYCvPld2INS7TlmjSRLH5PYalqjJGnU\nobPzJTd8LKLANt6l/ibKsPa0QkXgYmYjnAWqBhxYsytDsaJEykIGAyoskR4C\nuZVy8KkVL7pIAlExw0/wkDOzKCq/xmbjmrR1u/WwGq5aAu4hn2PcJp1wuvyO\ny99GDhEPjsSRxStC+dIPXUPvsZydBZ5OSNLcV2uMj6smcj8AaR1mCz1mCFoY\n95h6goncRmRS/i/pTV+A4cXIaS99h0hV1IC2P7Y2wJ2UoF/gApE++awrhHZU\nMx15F001v8z+iMwroryYJfhlm8/o0PzzArR2Do4qNZL3T8FfH/9jFCE82E8C\n7wpsivuVPOxY6isyEAR2aMS/g8s5wlLEB5k5QZWJf0qN/0IWGT/blSNBqQlr\nthhdR+uoCyHRnLm0qL1R8el0wVR+ilrC7RwEvDMBgVj+pAwdRXYe9TOPUY2/\nMzWmVJl0x/GqCUXtyyuUZCqrxqgvPknniRP12ajdf9RKcJ2INpIYJzIWPqM9\n7X3XlrQFYUHocMIDTeDZxCPuTbUXkrseY7qecKxXT/BXXHsqgy24n/5wogi4\nQMgiZnoiDTjcgBOTtRQT8PhfrdYFZHokXelyGhpwuq2JGPCC2cO4msSpNHPJ\n+EXp\r\n=9IOJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.7_1625678464228_0.5940608070278717", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b33fed3eed022f94c7db53593571f370eaa77aa17b3e302dc1bd77304f03e56c"}, "6.9.8": {"name": "@types/qs", "version": "6.9.8", "license": "MIT", "_id": "@types/qs@6.9.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dpsmith3", "name": "<PERSON>", "githubUsername": "dpsmith3"}, {"url": "https://github.com/hperrin", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON>rin"}, {"url": "https://github.com/ljharb", "name": "<PERSON>", "githubUsername": "lj<PERSON>b"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/qs", "dist": {"shasum": "f2a7de3c107b89b441e071d5472e6b726b4adf45", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.9.8.tgz", "fileCount": 5, "integrity": "sha512-u95svzDlTysU5xecFNTgfFG5RUWu1A9P0VzgpcIiGZA9iraHOdSzcxMxQ55DyeRaGCSxQi7LxXDI4rzq/MYfdg==", "signatures": [{"sig": "MEUCIChYMoTIt++tEwT2gHwkjmfFa9cRhttwv27ZRoyFn8QiAiEA9zDLJa+70vHHismrmCJMFPux90VuifueJF+kK/dqA1c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7066}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.8_1693425728404_0.20733305282164816", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b958a7001e037ec7b965c8c760bb8a500c4f6e4d9cc575bd5f8bead56fa20afd"}, "6.9.9": {"name": "@types/qs", "version": "6.9.9", "license": "MIT", "_id": "@types/qs@6.9.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dpsmith3", "name": "<PERSON>", "githubUsername": "dpsmith3"}, {"url": "https://github.com/hperrin", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON>rin"}, {"url": "https://github.com/ljharb", "name": "<PERSON>", "githubUsername": "lj<PERSON>b"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/qs", "dist": {"shasum": "66f7b26288f6799d279edf13da7ccd40d2fa9197", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.9.9.tgz", "fileCount": 5, "integrity": "sha512-wYLxw35euwqGvTDx6zfY1vokBFnsK0HNrzc6xNHchxfO2hpuRg74GbkEW7e3sSmPvj0TjCDT1VCa6OtHXnubsg==", "signatures": [{"sig": "MEUCIQDEWGakYwS3rnGXJzkAQYG+8Ddt++VTu/m2njyBRSVRlgIgJdeY4SCxJ0LqCJBkJV3LpeqLSsr1nyN/1U/bTBY2lNc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8928}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.9_1697629203597_0.6437899944023764", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "85b611d9e94f698fb82ab3268ba91cc09cfb2b698e784ce665eb08c083cb5895"}, "6.9.10": {"name": "@types/qs", "version": "6.9.10", "license": "MIT", "_id": "@types/qs@6.9.10", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dpsmith3", "name": "<PERSON>", "githubUsername": "dpsmith3"}, {"url": "https://github.com/hperrin", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON>rin"}, {"url": "https://github.com/ljharb", "name": "<PERSON>", "githubUsername": "lj<PERSON>b"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/qs", "dist": {"shasum": "0af26845b5067e1c9a622658a51f60a3934d51e8", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.9.10.tgz", "fileCount": 5, "integrity": "sha512-3Gnx08Ns1sEoCrWssEgTSJs/rsT2vhGP+Ja9cnnk9k4ALxinORlQneLXFeFKOTJMOeZUFD1s7w+w2AphTpvzZw==", "signatures": [{"sig": "MEUCIAX2pnhaefVLdHXvXp6yCf4Zm3/8XJZZhGuJU7YYSwwhAiEAyUAif+BInHTfzD4aDBIkWBljUVFERt9C9bXH8Do3wU4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8929}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.10_1699364366944_0.6570772195360051", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d935324b07497b49e62643ab51abd2ea063f34314b2f5a415424fdda6a6e8714"}, "6.9.11": {"name": "@types/qs", "version": "6.9.11", "license": "MIT", "_id": "@types/qs@6.9.11", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dpsmith3", "name": "<PERSON>", "githubUsername": "dpsmith3"}, {"url": "https://github.com/hperrin", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON>rin"}, {"url": "https://github.com/ljharb", "name": "<PERSON>", "githubUsername": "lj<PERSON>b"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/qs", "dist": {"shasum": "208d8a30bc507bd82e03ada29e4732ea46a6bbda", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.9.11.tgz", "fileCount": 5, "integrity": "sha512-oGk0gmhnEJK4Yyk+oI7EfXsLayXatCWPHary1MtcmbAifkobT9cM9yutG/hZKIseOU0MqbIwQ/u2nn/Gb+ltuQ==", "signatures": [{"sig": "MEYCIQDjaemlb6hWVbts3UKGdZpq7C5aZxd8YEtL/mRJU3d4KwIhAJKkBaYLEXHMlcS/wRKEopWg4WtcpzQU91C074Xz9yCM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6394}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.6", "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.11_1703192819413_0.7318624006904766", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "287e7b46b4d9d15d0cfed0047833433c5f412f05b83bac6095af50e2a091edc8"}, "6.9.12": {"name": "@types/qs", "version": "6.9.12", "license": "MIT", "_id": "@types/qs@6.9.12", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dpsmith3", "name": "<PERSON>", "githubUsername": "dpsmith3"}, {"url": "https://github.com/hperrin", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON>rin"}, {"url": "https://github.com/ljharb", "name": "<PERSON>", "githubUsername": "lj<PERSON>b"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/qs", "dist": {"shasum": "afa96b383a3a6fdc859453a1892d41b607fc7756", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.9.12.tgz", "fileCount": 5, "integrity": "sha512-bZcOkJ6uWrL0Qb2NAWKa7TBU+mJHPzhx9jjLL1KHF+XpzEcR7EXHvjbHlGtR/IsP1vyPrehuS6XqkmaePy//mg==", "signatures": [{"sig": "MEUCIQCY57kLbH5QdC3XmRDWlwu+Hmbm9GzQg6sv8m2q8G1NvQIgBsnqLox8OgIYZjyC34QiVphHCeAtz2nSoYKl94C7rVE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6400}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.6", "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.12_1709062552157_0.489822956887068", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "5fedfdf3039bda4d290d252e351dbac564e0e82461e995f5f0db05ae2f93f46a"}, "6.9.13": {"name": "@types/qs", "version": "6.9.13", "license": "MIT", "_id": "@types/qs@6.9.13", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dpsmith3", "name": "<PERSON>", "githubUsername": "dpsmith3"}, {"url": "https://github.com/hperrin", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON>rin"}, {"url": "https://github.com/ljharb", "name": "<PERSON>", "githubUsername": "lj<PERSON>b"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/qs", "dist": {"shasum": "c7e2406bdc6bd512f8a3651632568c72d43eb1e7", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.9.13.tgz", "fileCount": 5, "integrity": "sha512-iLR+1vTTJ3p0QaOUq6ACbY1mzKTODFDT/XedZI8BksOotFmL4ForwDfRQ/DZeuTHR7/2i4lI1D203gdfxuqTlA==", "signatures": [{"sig": "MEUCIHILDXXkOVkK1ko+DBOho3jpjeDoheGaza0tZYB8vqbnAiEAt8VGaGNWbsUE3LY/o0b9UH77yMIuFFECmqqSahfwldY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7213}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.7", "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.13_1710788802541_0.3935225226742731", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3f7e2ee4ebf0be590fb4d4090adb4a99e54efe73ff69ce25e4e9c454fa15405b"}, "6.9.14": {"name": "@types/qs", "version": "6.9.14", "license": "MIT", "_id": "@types/qs@6.9.14", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dpsmith3", "name": "<PERSON>", "githubUsername": "dpsmith3"}, {"url": "https://github.com/hperrin", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON>rin"}, {"url": "https://github.com/ljharb", "name": "<PERSON>", "githubUsername": "lj<PERSON>b"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/qs", "dist": {"shasum": "169e142bfe493895287bee382af6039795e9b75b", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.9.14.tgz", "fileCount": 5, "integrity": "sha512-5khscbd3SwWMhFqylJBLQ0zIu7c1K6Vz0uBIt915BI3zV0q1nfjRQD3RqSBcPaO6PHEF4ov/t9y89fSiyThlPA==", "signatures": [{"sig": "MEUCIHlPPxAAMHaKzUJmq9RhYsHNX6kzyZorG9/UHWP5FoQbAiEA74U90oMVHydJ8Fm7NSNkFRUvm/+uZi97MR18/apSnK0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7292}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.7", "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.14_1710950916594_0.9105484311468239", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "06e68a992b8160a3fe1a7cf06328d871215880d4874d9255acf6d516f10e52f6"}, "6.9.15": {"name": "@types/qs", "version": "6.9.15", "license": "MIT", "_id": "@types/qs@6.9.15", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dpsmith3", "name": "<PERSON>", "githubUsername": "dpsmith3"}, {"url": "https://github.com/hperrin", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON>rin"}, {"url": "https://github.com/ljharb", "name": "<PERSON>", "githubUsername": "lj<PERSON>b"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/qs", "dist": {"shasum": "adde8a060ec9c305a82de1babc1056e73bd64dce", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.9.15.tgz", "fileCount": 5, "integrity": "sha512-uXHQKES6DQKKCLh441Xv/dwxOq1TVS3JPUMlEqoEglvlhR6Mxnlew/Xq/LRVHpLyk7iK3zODe1qYHIMltO7XGg==", "signatures": [{"sig": "MEUCIQC0NpPAijhJTyXvFl5L/RcsOd9s3hqe7+WtpqCgBhQkHAIgUsai8H9VTdiqPbhc7WLCS/5/MxHYmRPCYeLI8S+UVuQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7340}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.7", "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.15_1713240463783_0.21156453499901406", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "1f1cbc913fb092d4ffa9230a21894f18d2826d04431244517086c6fc477dbf12"}, "6.9.16": {"name": "@types/qs", "version": "6.9.16", "license": "MIT", "_id": "@types/qs@6.9.16", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dpsmith3", "name": "<PERSON>", "githubUsername": "dpsmith3"}, {"url": "https://github.com/hperrin", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON>rin"}, {"url": "https://github.com/ljharb", "name": "<PERSON>", "githubUsername": "lj<PERSON>b"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/qs", "dist": {"shasum": "52bba125a07c0482d26747d5d4947a64daf8f794", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.9.16.tgz", "fileCount": 5, "integrity": "sha512-7i+zxXdPD0T4cKDuxCUXJ4wHcsJLwENa6Z3dCu8cfCK743OGy5Nu1RmAGqDPsoTDINVEcdXKRvR/zre+P2Ku1A==", "signatures": [{"sig": "MEYCIQCJHmxqkK7F/686e5H2u81JJg1Os/5nl33tsz+TvsybbQIhAP592EMYdDm32RGQ90MMFsrvWVL6W3ijgq83pcPex3Sq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7386}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.8", "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.16_1726282048212_0.358020927034703", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a1c6974752cb85bef9651c5d9da8b5ba4f2ed34e8cea6cdaede247cf333eec2c"}, "6.9.17": {"name": "@types/qs", "version": "6.9.17", "license": "MIT", "_id": "@types/qs@6.9.17", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dpsmith3", "name": "<PERSON>", "githubUsername": "dpsmith3"}, {"url": "https://github.com/hperrin", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON>rin"}, {"url": "https://github.com/ljharb", "name": "<PERSON>", "githubUsername": "lj<PERSON>b"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/qs", "dist": {"shasum": "fc560f60946d0aeff2f914eb41679659d3310e1a", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.9.17.tgz", "fileCount": 5, "integrity": "sha512-rX4/bPcfmvxHDv0XjfJELTTr+iB+tn032nPILqHm5wbthUUUuVtNGGqzhya9XUxjTP8Fpr0qYgSZZKxGY++svQ==", "signatures": [{"sig": "MEYCIQCacVN0WHJ/iOSQIjRg/myIs2KSMtX7WWARH4oV23SWCQIhAOWowq1VGYm19ujosvCywUMkep65+bJfF4kF0CQBCFJH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7457}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "4.8", "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.17_1730828084632_0.9492181534813229", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "00acee13c313ecab590a15553dd9a5639247613217ce1666a302dc6328e18050"}, "6.9.18": {"name": "@types/qs", "version": "6.9.18", "license": "MIT", "_id": "@types/qs@6.9.18", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dpsmith3", "name": "<PERSON>", "githubUsername": "dpsmith3"}, {"url": "https://github.com/hperrin", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON>rin"}, {"url": "https://github.com/ljharb", "name": "<PERSON>", "githubUsername": "lj<PERSON>b"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/qs", "dist": {"shasum": "877292caa91f7c1b213032b34626505b746624c2", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.9.18.tgz", "fileCount": 5, "integrity": "sha512-kK7dgTYDyGqS+e2Q4aK9X3D7q234CIZ1Bv0q/7Z5IwRDoADNU81xXJK/YVyLbLTZCoIwUoDoffFeF+p/eIklAA==", "signatures": [{"sig": "MEUCIQCf98yI2+EkJWgrCQ2WQdREqYiQUf2uvSdezvuLxAc/VwIgMXSzO+SQOUYX5ePTN5A/9O8oYaJgMQeUxxf0MIE0YWY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7457}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.0", "_npmOperationalInternal": {"tmp": "tmp/qs_6.9.18_1736818227724_0.08822976662425175", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "2b3ee00ab119740c8d250fb641a3ab35b4a40a86c735d92f5d6ca12894a1ec0f"}, "6.14.0": {"name": "@types/qs", "version": "6.14.0", "license": "MIT", "_id": "@types/qs@6.14.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dpsmith3", "name": "<PERSON>", "githubUsername": "dpsmith3"}, {"url": "https://github.com/hperrin", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON>rin"}, {"url": "https://github.com/ljharb", "name": "<PERSON>", "githubUsername": "lj<PERSON>b"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/qs", "dist": {"shasum": "d8b60cecf62f2db0fb68e5e006077b9178b85de5", "tarball": "https://registry.npmjs.org/@types/qs/-/qs-6.14.0.tgz", "fileCount": 5, "integrity": "sha512-eOunJqu0K1923aExK6y8p6fsihYEn/BYuQ4g0CxAAgFc4b/ZLN4CrsRZ55srTdqoiLzU2B2evC+apEIxprEzkQ==", "signatures": [{"sig": "MEYCIQDlmzGdGcDr7kHyjjaFjXrD/7xDgcbdSaAgSWZKSSaNjwIhALTs1sOa6MZg5YYBWjMTWDrZXbc5wkg2nZ4QdgDPARyK", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7509}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/qs_6.14.0_1747456615274_0.7940192403306188", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "7ce8128acabe5d960292bd50615bb46af79b7ae86cf61e48a5398fcc34410644"}}, "time": {"created": "2016-05-17T18:41:36.530Z", "modified": "2025-05-17T04:37:01.793Z", "0.0.13-alpha": "2016-05-17T18:41:36.530Z", "0.0.14-alpha": "2016-05-19T22:05:57.274Z", "0.0.19-alpha": "2016-05-20T20:28:52.846Z", "0.0.20-alpha": "2016-05-25T05:44:54.902Z", "0.0.21-alpha": "2016-07-01T20:25:11.349Z", "0.0.22-alpha": "2016-07-01T23:46:45.826Z", "0.0.23-alpha": "2016-07-02T03:15:37.259Z", "0.0.24-alpha": "2016-07-04T01:12:28.038Z", "0.0.25-alpha": "2016-07-08T21:13:11.495Z", "0.0.26": "2016-07-14T15:48:07.848Z", "6.2.27": "2016-08-02T16:05:16.889Z", "6.2.28": "2016-08-19T15:37:55.861Z", "6.2.29": "2016-08-25T18:54:07.856Z", "6.2.30": "2016-09-19T18:04:41.513Z", "6.2.31": "2017-03-22T00:34:04.439Z", "6.4.0": "2017-06-30T21:46:26.774Z", "6.5.0": "2017-07-05T14:31:39.821Z", "6.5.1": "2017-10-25T16:22:21.588Z", "6.5.2": "2019-03-02T01:09:42.952Z", "6.5.3": "2019-04-09T17:21:18.067Z", "6.9.0": "2019-11-07T21:10:14.204Z", "6.9.1": "2020-02-07T18:30:46.688Z", "6.9.2": "2020-05-05T16:47:48.124Z", "6.9.3": "2020-05-19T17:45:28.687Z", "6.9.4": "2020-07-24T21:40:01.190Z", "6.9.5": "2020-09-16T23:06:07.664Z", "6.9.6": "2021-03-07T09:25:59.720Z", "6.9.7": "2021-07-07T17:21:04.478Z", "6.9.8": "2023-08-30T20:02:08.528Z", "6.9.9": "2023-10-18T11:40:03.806Z", "6.9.10": "2023-11-07T13:39:27.116Z", "6.9.11": "2023-12-21T21:06:59.592Z", "6.9.12": "2024-02-27T19:35:52.344Z", "6.9.13": "2024-03-18T19:06:42.673Z", "6.9.14": "2024-03-20T16:08:36.735Z", "6.9.15": "2024-04-16T04:07:43.973Z", "6.9.16": "2024-09-14T02:47:28.399Z", "6.9.17": "2024-11-05T17:34:44.823Z", "6.9.18": "2025-01-14T01:30:27.914Z", "6.14.0": "2025-05-17T04:36:55.452Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/qs", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/qs"}, "description": "TypeScript definitions for qs", "contributors": [{"url": "https://github.com/RWander", "name": "<PERSON>", "githubUsername": "R<PERSON><PERSON>"}, {"url": "https://github.com/leonyu", "name": "<PERSON>", "githubUsername": "leonyu"}, {"url": "https://github.com/tehbelinda", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/zyml", "name": "<PERSON>", "githubUsername": "zyml"}, {"url": "https://github.com/artursvonda", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dpsmith3", "name": "<PERSON>", "githubUsername": "dpsmith3"}, {"url": "https://github.com/hperrin", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON>rin"}, {"url": "https://github.com/ljharb", "name": "<PERSON>", "githubUsername": "lj<PERSON>b"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": "", "users": {"marcobraak": true}}