{"_id": "is-extglob", "_rev": "13-eb77a763a244337e6a653dd522eb0b29", "name": "is-extglob", "time": {"modified": "2023-06-22T16:32:24.580Z", "created": "2015-03-06T20:58:24.527Z", "1.1.3": "2015-03-06T20:58:24.527Z", "1.0.0": "2015-03-06T21:00:23.847Z", "2.0.0": "2016-09-03T19:12:39.702Z", "2.1.0": "2016-10-12T22:33:10.000Z", "2.1.1": "2016-12-11T04:04:24.390Z"}, "maintainers": [{"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}], "dist-tags": {"latest": "2.1.1"}, "description": "Returns true if a string has an extglob.", "readme": "# is-extglob [![NPM version](https://img.shields.io/npm/v/is-extglob.svg?style=flat)](https://www.npmjs.com/package/is-extglob) [![NPM downloads](https://img.shields.io/npm/dm/is-extglob.svg?style=flat)](https://npmjs.org/package/is-extglob) [![Build Status](https://img.shields.io/travis/jonschlinkert/is-extglob.svg?style=flat)](https://travis-ci.org/jonschlinkert/is-extglob)\n\n> Returns true if a string has an extglob.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save is-extglob\n```\n\n## Usage\n\n```js\nvar isExtglob = require('is-extglob');\n```\n\n**True**\n\n```js\nisExtglob('?(abc)');\nisExtglob('@(abc)');\nisExtglob('!(abc)');\nisExtglob('*(abc)');\nisExtglob('+(abc)');\n```\n\n**False**\n\nEscaped extglobs:\n\n```js\nisExtglob('\\\\?(abc)');\nisExtglob('\\\\@(abc)');\nisExtglob('\\\\!(abc)');\nisExtglob('\\\\*(abc)');\nisExtglob('\\\\+(abc)');\n```\n\nEverything else...\n\n```js\nisExtglob('foo.js');\nisExtglob('!foo.js');\nisExtglob('*.js');\nisExtglob('**/abc.js');\nisExtglob('abc/*.js');\nisExtglob('abc/(aaa|bbb).js');\nisExtglob('abc/[a-z].js');\nisExtglob('abc/{a,b}.js');\nisExtglob('abc/?.js');\nisExtglob('abc.js');\nisExtglob('abc/def/ghi.js');\n```\n\n## History\n\n**v2.0**\n\nAdds support for escaping. Escaped exglobs no longer return true.\n\n## About\n\n### Related projects\n\n* [has-glob](https://www.npmjs.com/package/has-glob): Returns `true` if an array has a glob pattern. | [homepage](https://github.com/jonschlinkert/has-glob \"Returns `true` if an array has a glob pattern.\")\n* [is-glob](https://www.npmjs.com/package/is-glob): Returns `true` if the given string looks like a glob pattern or an extglob pattern… [more](https://github.com/jonschlinkert/is-glob) | [homepage](https://github.com/jonschlinkert/is-glob \"Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a bet\")\n* [micromatch](https://www.npmjs.com/package/micromatch): Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. | [homepage](https://github.com/jonschlinkert/micromatch \"Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.\")\n\n### Contributing\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n### Building docs\n\n_(This document was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme) (a [verb](https://github.com/verbose/verb) generator), please don't edit the readme directly. Any changes to the readme must be made in [.verb.md](.verb.md).)_\n\nTo generate the readme and API documentation with [verb](https://github.com/verbose/verb):\n\n```sh\n$ npm install -g verb verb-generate-readme && verb\n```\n\n### Running tests\n\nInstall dev dependencies:\n\n```sh\n$ npm install -d && npm test\n```\n\n### Author\n\n**Jon Schlinkert**\n\n* [github/jonschlinkert](https://github.com/jonschlinkert)\n* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)\n\n### License\n\nCopyright © 2016, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT license](https://github.com/jonschlinkert/is-extglob/blob/master/LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.1.31, on October 12, 2016._", "versions": {"1.0.0": {"name": "is-extglob", "description": "Returns true if a string has an extglob.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/is-extglob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/is-extglob"}, "bugs": {"url": "https://github.com/jonschlinkert/is-extglob/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["bash", "braces", "check", "exec", "extglob", "expression", "glob", "globbing", "globstar", "match", "matches", "pattern", "regex", "regular", "string", "test"], "_id": "is-extglob@1.0.0", "_shasum": "ac468177c4943405a092fc8f29760c6ffc6206c0", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ac468177c4943405a092fc8f29760c6ffc6206c0", "tarball": "https://registry.npmjs.org/is-extglob/-/is-extglob-1.0.0.tgz", "integrity": "sha512-7Q+VbVafe6x2T+Tu6NcOf6sRklazEPmBoB3IWk3WdGZM2iGUwU/Oe3Wtq5lSEkDTTlpp8yx+5t4pzO/i9Ty1ww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFAKYBUDetEAgYMaAmOxmHOt97cJqOW0kYU7xl/uv4/7AiAMqw23QpJQmdBYpPGn+/FATiNGUyubmGHw1x+7perO6A=="}]}, "directories": {}}, "2.0.0": {"name": "is-extglob", "description": "Returns true if a string has an extglob.", "version": "2.0.0", "homepage": "https://github.com/jonschlinkert/is-extglob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-extglob.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-extglob/issues"}, "license": "MIT", "files": ["index.js", "LICENSE", "README.md"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"gulp-format-md": "^0.1.10", "mocha": "^3.0.2"}, "keywords": ["bash", "braces", "check", "exec", "expression", "extglob", "glob", "globbing", "globstar", "is", "match", "matches", "pattern", "regex", "regular", "string", "test"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-glob", "micromatch", "has-glob"]}, "reflinks": ["verb", "verb-generate-readme"], "lint": {"reflinks": true}}, "gitHead": "fd18206880e6f1d7fa586d1cd51c4e0371008637", "_id": "is-extglob@2.0.0", "_shasum": "a9b92c1ae2d7a975ad307be0722049c7e4ea2f13", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a9b92c1ae2d7a975ad307be0722049c7e4ea2f13", "tarball": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.0.0.tgz", "integrity": "sha512-URj06aNCQLE/LnczW9/rzcj7VpYMSptll3manycWiGc6XbwpOvQ/EJ8Iw0MrsJzlILipHuRm5p+Z1RyvGvTn4g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC07BTrAArWdqTqb4WYia5PjR8FZBlxphselF76yHPqNAiEAgGz2NOTi0wmx9mR1KlwNFIJ0bGH35YtEQnUD+w/t0HA="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/is-extglob-2.0.0.tgz_1472929957338_0.5954867014661431"}, "directories": {}}, "2.1.0": {"name": "is-extglob", "description": "Returns true if a string has an extglob.", "version": "2.1.0", "homepage": "https://github.com/jonschlinkert/is-extglob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-extglob.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-extglob/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"gulp-format-md": "^0.1.10", "mocha": "^3.0.2"}, "keywords": ["bash", "braces", "check", "exec", "expression", "extglob", "glob", "globbing", "globstar", "is", "match", "matches", "pattern", "regex", "regular", "string", "test"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["has-glob", "is-glob", "micromatch"]}, "reflinks": ["verb", "verb-generate-readme"], "lint": {"reflinks": true}}, "gitHead": "8c3c7f47635920c31fcb9ccc1d2b6e40037bef54", "_id": "is-extglob@2.1.0", "_shasum": "33411a482b046bf95e6b0cb27ee2711af4cf15ad", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.7.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "33411a482b046bf95e6b0cb27ee2711af4cf15ad", "tarball": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.0.tgz", "integrity": "sha512-exZJG+UDbrT6ynQs5eEZ37cyR6UW3SxI8IVHlYdfUnnQ0lJcfu9fP3QAYChrM4D6bZ2+Wo4E8Gmu89m4HBr92A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDSSZLMW/iVT3yIlO4bP71yIncMkIQZHKsn0AnwHTn8zwIhAKq1TEfCB92NaZAgfF/kBCKoLNzRvhUPRu7cve80eZJL"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/is-extglob-2.1.0.tgz_1476311588224_0.632759055821225"}, "directories": {}}, "2.1.1": {"name": "is-extglob", "description": "Returns true if a string has an extglob.", "version": "2.1.1", "homepage": "https://github.com/jonschlinkert/is-extglob", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-extglob.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-extglob/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"gulp-format-md": "^0.1.10", "mocha": "^3.0.2"}, "keywords": ["bash", "braces", "check", "exec", "expression", "extglob", "glob", "globbing", "globstar", "is", "match", "matches", "pattern", "regex", "regular", "string", "test"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["has-glob", "is-glob", "micromatch"]}, "reflinks": ["verb", "verb-generate-readme"], "lint": {"reflinks": true}}, "gitHead": "10a74787acbe79abf02141c5d487950d1b197b15", "_id": "is-extglob@2.1.1", "_shasum": "a88c02535791f02ed37c76a1b9ea9773c833f8c2", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a88c02535791f02ed37c76a1b9ea9773c833f8c2", "tarball": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICPtj4zZ9S/mf6GvfHKfl7bVjtRkzPbYtfDKP8P/djsTAiBjHBICbllx2p3tvLZ8EA3e/KMhQMhNUz8kLJDV0ydSqQ=="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/is-extglob-2.1.1.tgz_1481429063759_0.*****************"}, "directories": {}}}, "homepage": "https://github.com/jonschlinkert/is-extglob", "keywords": ["bash", "braces", "check", "exec", "expression", "extglob", "glob", "globbing", "globstar", "is", "match", "matches", "pattern", "regex", "regular", "string", "test"], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-extglob.git"}, "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/is-extglob/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"arteffeckt": true, "flumpus-dev": true}}