{"_id": "babel-plugin-istanbul", "_rev": "62-a2ef4fe3dbe8e3983838ada35b35ac6d", "name": "babel-plugin-istanbul", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "dist-tags": {"latest": "7.0.0"}, "versions": {"1.0.0-alpha.1": {"name": "babel-plugin-istanbul", "version": "1.0.0-alpha.1", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@1.0.0-alpha.1", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "dist": {"shasum": "6186a66628dd94e11aaa9aa82be4b6bbde8c9cfd", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-1.0.0-alpha.1.tgz", "integrity": "sha512-DAJhYVI3JKsM3bhiSfa8sO+XRc6tmoA/X+LX7IXrs09wvKLUVA8llfIRw22tDR1aKdP3a8skW8dRYaM1GJ/wDg==", "signatures": [{"sig": "MEUCIQDZsGDj9Gf7A4QahYX9tznJjB1ZnzkcQxTyRTD5V6nyNAIgL1pC6pGuCfqe4Flz+OEzcaWM3C6LjELfKBaByRKlBVY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "6186a66628dd94e11aaa9aa82be4b6bbde8c9cfd", "gitHead": "e0653a1e769a79cd2cf268c906fbec32023f07f0", "scripts": {"test": "echo no tests yet", "release": "babel src --out-dir lib", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "3.9.3", "description": "A babel plugin that adds istanbul instrumentation to ES6 code — Edit", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {}, "devDependencies": {"babel-cli": "^6.3.17", "babel-core": "^6.3.21", "babel-preset-es2015": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-1.0.0-alpha.1.tgz_1466693634626_0.768921805312857", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.0": {"name": "babel-plugin-istanbul", "version": "1.0.0", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@1.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-core/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "1d57aba4510a8f5958624c7e4ae74030a8765c67", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-1.0.0.tgz", "integrity": "sha512-4tJ5qdP+Vga1FQKhzOMqthiUD4WOMKimI7vJzYb1h6rnVTzBQ0jj5J+Hvm5vbartSeMPuF+rATlXir04+0bbTA==", "signatures": [{"sig": "MEUCIQCQnG4yQS4Swl9Aekwsf1T1J6k7DGVqRnL6sf1tKZqbPgIgMwEEV7pAJNZcGXSV6ez4/VVJ10YHtYWI1KWaN1dU4iY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "1d57aba4510a8f5958624c7e4ae74030a8765c67", "gitHead": "dab7681a586aebf43268435b91a9160a424b90f1", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"find-up": "^1.1.2", "test-exclude": "^1.1.0", "istanbul-lib-instrument": "^1.1.0-alpha.1"}, "devDependencies": {"nyc": "^6.6.1", "chai": "^3.5.0", "mocha": "^2.5.3", "standard": "^7.1.2", "babel-cli": "^6.3.17", "coveralls": "^2.11.9", "cross-env": "^1.0.8", "babel-core": "^6.3.21", "standard-version": "^2.3.1", "babel-preset-es2015": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-1.0.0.tgz_1467089234538_0.7045440345536917", "host": "packages-16-east.internal.npmjs.com"}}, "1.0.1": {"name": "babel-plugin-istanbul", "version": "1.0.1", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@1.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-core/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "77c93fc478f638cd3ef40e8da459a74a57d8634f", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-1.0.1.tgz", "integrity": "sha512-UlQAAKclm8rKIZgMo8sRu4ATz0ComsMnZUa2jcIDLOii66r4Hxw1fjst6OGiZW+m3zUhxZR6l2Oq1kCr7g2uXg==", "signatures": [{"sig": "MEUCIQCY8rBxn9ThWXbBx8XBkkTvKIBacQAub1Up0wONqTcW/QIgd9v6b0spTkmMf6HKGNjNliiDxr3GpBBzuQ7pLIqufSg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "77c93fc478f638cd3ef40e8da459a74a57d8634f", "gitHead": "320c3df5b6347d82ffb5cdb4b1047273d115fe06", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"find-up": "^1.1.2", "test-exclude": "^1.1.0", "istanbul-lib-instrument": "^1.1.0-alpha.2"}, "devDependencies": {"nyc": "^6.6.1", "chai": "^3.5.0", "mocha": "^2.5.3", "standard": "^7.1.2", "babel-cli": "^6.3.17", "coveralls": "^2.11.9", "cross-env": "^1.0.8", "babel-core": "^6.3.21", "standard-version": "^2.3.1", "babel-preset-es2015": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-1.0.1.tgz_1467328643456_0.8961003853473812", "host": "packages-16-east.internal.npmjs.com"}}, "1.0.2": {"name": "babel-plugin-istanbul", "version": "1.0.2", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@1.0.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-core/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "c1f6c64de8cc661e387c63535f563effa6c45651", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-1.0.2.tgz", "integrity": "sha512-hWbBUQS+KmUauW4f0wcpNIh61UFH1e1WZNjP9Cfl77Qq/b+K1T4xqBdKYpp0CF5C8Q4vqEc7vu4L+vYNZdc5bQ==", "signatures": [{"sig": "MEQCIHRWcoqCkxHkPwsPfrVnWxrTIPIIKEw2JgQW3bi5C2biAiBhyDnxfvrNC1clEi/gBk4n0Hek2RN+wwLoGOYzQa21JQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "c1f6c64de8cc661e387c63535f563effa6c45651", "gitHead": "10f64d7067633f8845d480826ff9bb77b6b73ae5", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"find-up": "^1.1.2", "test-exclude": "^1.1.0", "istanbul-lib-instrument": "^1.1.0-alpha.2"}, "devDependencies": {"nyc": "^6.6.1", "chai": "^3.5.0", "mocha": "^2.5.3", "standard": "^7.1.2", "babel-cli": "^6.3.17", "coveralls": "^2.11.9", "cross-env": "^1.0.8", "babel-core": "^6.3.21", "standard-version": "^2.3.1", "babel-preset-es2015": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-1.0.2.tgz_1467571936153_0.09335809457115829", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.3": {"name": "babel-plugin-istanbul", "version": "1.0.3", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@1.0.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-core/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "5de44de71cc6c58cb8ffed2ddeadeac9dcaca9df", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-1.0.3.tgz", "integrity": "sha512-1tSlHAZxW6AJMacWUQHUXAIWOGw5XCxYgUTFpgvIxr6dZlw79gs9zHwYIsFJdDWY5IgBf4CFqrnwI4/eDnXMLw==", "signatures": [{"sig": "MEUCIHsey13TU3zt1KvlDBaJGgdUXGXcQsrnd+J0G3w9n5C3AiEAlsboBKawfVyVISwue4oSojghMgyWuah6+WvHxIRhnok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "5de44de71cc6c58cb8ffed2ddeadeac9dcaca9df", "gitHead": "ccbc518875e0478eb0b99385b40ba39f44b5e6ba", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"find-up": "^1.1.2", "test-exclude": "^1.1.0", "istanbul-lib-instrument": "^1.1.0-alpha.2"}, "devDependencies": {"nyc": "^6.6.1", "chai": "^3.5.0", "mocha": "^2.5.3", "standard": "^7.1.2", "babel-cli": "^6.3.17", "coveralls": "^2.11.9", "cross-env": "^1.0.8", "babel-core": "^6.3.21", "standard-version": "^2.3.1", "babel-preset-es2015": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-1.0.3.tgz_1468046923774_0.8789181299507618", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.0": {"name": "babel-plugin-istanbul", "version": "1.1.0", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@1.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-core/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "e7bf886beb5e0ac2dad25805185bab777de3530c", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-1.1.0.tgz", "integrity": "sha512-wdTtBQiE7u3OcLqFw82BcZeFBdCpObWpFBdF/Jru9SQYKn2lSuebeSfIs29O4XHRVnOHV7xyMXjruh/8JONuRQ==", "signatures": [{"sig": "MEYCIQCk2G4rcjUoc2SXptvs/3FPGRQOYjyduukVVmR6wvD1DQIhALteKkKCZaMaeZsSOu5mJ/YdDj2pOXUUm+WSdNzqHar1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "e7bf886beb5e0ac2dad25805185bab777de3530c", "gitHead": "3a6935a2c57097a7a90a02b0b8ca4415715919e1", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"find-up": "^1.1.2", "test-exclude": "^1.1.0", "istanbul-lib-instrument": "^1.1.0-alpha.4"}, "devDependencies": {"nyc": "^7.0.0", "chai": "^3.5.0", "mocha": "^2.5.3", "standard": "^7.1.2", "babel-cli": "^6.3.17", "coveralls": "^2.11.9", "cross-env": "^2.0.0", "babel-core": "^6.3.21", "standard-version": "^2.3.1", "babel-preset-es2015": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-1.1.0.tgz_1469075537717_0.07378975534811616", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.0": {"name": "babel-plugin-istanbul", "version": "2.0.0", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@2.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-core/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "c5ccf64dd11c24e23f29555d8d5356100f93335f", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-2.0.0.tgz", "integrity": "sha512-sWm1XyOaQ6+QFqOd4A0A//e+bC/nhklX2FB66UpXMGQ+WDDJktdTORDSeRFBk8Odc9vqj9KPiiBS37Rvls3cSQ==", "signatures": [{"sig": "MEQCICeFYCuzR1DOLuBd+n1NPh0qdNV+fH0HE8qE2Sfz09YhAiArqJt6V7Yg2wr7uQIW8hZzxonpv4qgo+bm/EWAk45j/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "c5ccf64dd11c24e23f29555d8d5356100f93335f", "gitHead": "a344dff0adfaadfa320c590cd7f2f4ea6bc298c3", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "3.10.6", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"find-up": "^1.1.2", "test-exclude": "^2.1.1", "istanbul-lib-instrument": "^1.1.0-alpha.4"}, "devDependencies": {"nyc": "^7.0.0", "chai": "^3.5.0", "mocha": "^3.0.2", "standard": "^7.1.2", "babel-cli": "^6.3.17", "coveralls": "^2.11.9", "cross-env": "^2.0.0", "babel-core": "^6.3.21", "standard-version": "^2.3.1", "babel-preset-es2015": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-2.0.0.tgz_1471162679350_0.27002689987421036", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.1": {"name": "babel-plugin-istanbul", "version": "2.0.1", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@2.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-core/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "c384916f081ceaeb9d4b44b6a7db84e07ad67418", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-2.0.1.tgz", "integrity": "sha512-zAhRNmhWj4N8noowHgG3DZfpBbEYWsYpbHKqqE1RV0nVSB80zVlQ9DmjwTqV/7P7tGlAyyCaK3IC3/t5Ao8c5g==", "signatures": [{"sig": "MEUCIF//gdRK+YTVzxdFYPNIGNTxApxB2jD+U1sXSSzGslAAAiEAnzy7j9iMC3cIYNydGHv+IW4qPyq7tjOvCsZOEIOsd4A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "c384916f081ceaeb9d4b44b6a7db84e07ad67418", "gitHead": "0bd16cf06bedcb28a93d621a6bd5ce113acbcccd", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "_nodeVersion": "6.4.0", "dependencies": {"find-up": "^1.1.2", "test-exclude": "^2.1.1", "istanbul-lib-instrument": "^1.1.1"}, "devDependencies": {"nyc": "^8.1.0", "chai": "^3.5.0", "mocha": "^3.0.2", "standard": "^8.0.0", "babel-cli": "^6.3.17", "coveralls": "^2.11.9", "cross-env": "^2.0.0", "babel-core": "^6.3.21", "standard-version": "^2.3.1", "babel-preset-es2015": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-2.0.1.tgz_1472833584634_0.47891264013014734", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.2": {"name": "babel-plugin-istanbul", "version": "2.0.2", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@2.0.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-core/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "66c957fd3b2ace69215955c0ea5920980958cbf3", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-2.0.2.tgz", "integrity": "sha512-ertCB0gskvoH5MijMPe5SBWL6iZnYCHsitaAc0UgQ3PbZnq3PFlH68vCf11fIuM6QKwm5HBFPBlwnJbPjtOtZw==", "signatures": [{"sig": "MEUCIQDZdBbvOb9elmejxBWrsKFH7vjOX6/nC+rCB2xQMMAJwQIgB2WF5aUCAnZJXaEb5/RuS+6O2ov8Fq5DVU9+ByZDp/k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "66c957fd3b2ace69215955c0ea5920980958cbf3", "gitHead": "d3cb73e28a7fe6f58427685f29f8cd3bb1a3cb9f", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"find-up": "^1.1.2", "test-exclude": "^2.1.1", "lodash.assign": "^4.2.0", "istanbul-lib-instrument": "^1.1.1"}, "devDependencies": {"nyc": "^8.1.0", "chai": "^3.5.0", "mocha": "^3.0.2", "pmock": "^0.2.3", "mock-fs": "^3.11.0", "standard": "^8.0.0", "babel-cli": "^6.3.17", "coveralls": "^2.11.9", "cross-env": "^2.0.0", "babel-core": "^6.3.21", "standard-version": "^2.3.1", "babel-preset-es2015": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-2.0.2.tgz_1473304802929_0.6378689480479807", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.3": {"name": "babel-plugin-istanbul", "version": "2.0.3", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@2.0.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-core/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "266b304b9109607d60748474394676982f660df4", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-2.0.3.tgz", "integrity": "sha512-JGofIg9i4uReQiYzEZuFUcHIheHG6FTEqaewpfa9+NbPmuVrGoDdLJR0u/fp7oJ0V9eRSRRAXM9ZFCdnXJgjWw==", "signatures": [{"sig": "MEUCIBSj+CqMaaa3iVLxTuB5mlq96vaVfwK59oARLlH7t0weAiEAniF8/kMXCtQZeKArUOoIP15+3UsBotjIIVtd3P8gj7I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "266b304b9109607d60748474394676982f660df4", "gitHead": "9cb75e23e9cfc47044b9617e7f3db966ec3b09d7", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha --require=${FS_MOCK:-mock-fs} test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "greenkeeper": {"ignore": ["find-up", "cross-env"]}, "_nodeVersion": "6.5.0", "dependencies": {"find-up": "^1.1.2", "test-exclude": "^2.1.1", "object-assign": "^4.1.0", "istanbul-lib-instrument": "^1.1.4"}, "devDependencies": {"nyc": "^8.1.0", "chai": "^3.5.0", "mocha": "^3.0.2", "pmock": "^0.2.3", "mock-fs": "^3.11.0", "standard": "^8.0.0", "babel-cli": "^6.3.17", "coveralls": "^2.11.9", "cross-env": "^2.0.1", "babel-core": "^6.3.21", "standard-version": "^3.0.0", "babel-preset-es2015": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-2.0.3.tgz_1476687800569_0.42528436449356377", "host": "packages-16-east.internal.npmjs.com"}}, "3.0.0": {"name": "babel-plugin-istanbul", "version": "3.0.0", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@3.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-core/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "da7324520ae0b8a44b6a078e72e883374a9fab76", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-3.0.0.tgz", "integrity": "sha512-K995O1Jbv1ne+XD1RNfo3uNtoO9PXMr7z42SQNzf6R6e9mP6S3wU71iSzSHWdZjxnPLClBAAqeomivjZ7dIf1w==", "signatures": [{"sig": "MEUCIC4sjYnUSRzp1j0JPIPaSNBbFvknsXYF3E4kDetsZ71hAiEAj0CWJ1bvclY/jOn/7koQNcXYKFn9Pg/CSL5YyfOfNBw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "da7324520ae0b8a44b6a078e72e883374a9fab76", "gitHead": "fb0cbfda4b23c7dd14ff015f71bbd6a1d4402ae9", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha --require=${FS_MOCK:-mock-fs} test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "4.0.0", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "greenkeeper": {"ignore": ["find-up", "cross-env"]}, "_nodeVersion": "6.5.0", "dependencies": {"find-up": "^1.1.2", "test-exclude": "^3.2.2", "object-assign": "^4.1.0", "istanbul-lib-instrument": "^1.1.4"}, "devDependencies": {"nyc": "^8.1.0", "chai": "^3.5.0", "mocha": "^3.0.2", "pmock": "^0.2.3", "mock-fs": "^3.11.0", "standard": "^8.0.0", "babel-cli": "^6.3.17", "coveralls": "^2.11.9", "cross-env": "^2.0.1", "babel-core": "^6.3.21", "standard-version": "^3.0.0", "babel-preset-es2015": "^6.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-3.0.0.tgz_1479167724408_0.9131758976727724", "host": "packages-18-east.internal.npmjs.com"}}, "3.1.0-candidate.0": {"name": "babel-plugin-istanbul", "version": "3.1.0-candidate.0", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@3.1.0-candidate.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-core/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "5a6132eeb93de10eb919504228ebede9b1e094b3", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-3.1.0-candidate.0.tgz", "integrity": "sha512-6HkYGWq1LNgiRh9BR+KSUrrjPjAcWeG/uRjgC0nAf2MDUUcelq8LEzVyZ8vH3MRavga6uJd5FMlGdiBuOnICHw==", "signatures": [{"sig": "MEQCIGOga/iDrBxZWMpY4wGP5YUu4/Ukhig9KD5CSYJnT080AiBortc2/7RN34Z9h8Z4HI9MdXRCNfKV2NktzaWYq1B21g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "5a6132eeb93de10eb919504228ebede9b1e094b3", "gitHead": "2e0af61fa690f01a20226e9aadb18eb29973393f", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha --require=${FS_MOCK:-mock-fs} test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["fixtures/has-inline-source-map.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "greenkeeper": {"ignore": ["find-up", "cross-env"]}, "_nodeVersion": "7.1.0", "dependencies": {"find-up": "^1.1.2", "test-exclude": "^3.3.0", "object-assign": "^4.1.0", "istanbul-lib-instrument": "^1.3.1"}, "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "mocha": "^3.2.0", "pmock": "^0.2.3", "mock-fs": "^3.12.1", "standard": "^8.6.0", "babel-cli": "^6.18.0", "coveralls": "^2.11.15", "cross-env": "^2.0.1", "babel-core": "^6.21.0", "standard-version": "^4.0.0", "babel-preset-es2015": "^6.18.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-3.1.0-candidate.0.tgz_1482823491577_0.3856146391481161", "host": "packages-18-east.internal.npmjs.com"}}, "3.1.1-candidate.0": {"name": "babel-plugin-istanbul", "version": "3.1.1-candidate.0", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@3.1.1-candidate.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-core/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "80238427ffa779ab2e12f8c48210288911984b92", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-3.1.1-candidate.0.tgz", "integrity": "sha512-4rtYYIEgpLPEr0wuwvnQIy8mN8JnPj8sWxV1nBTP/0ZKZFV9C2f8xbIYrydslP84SNx+QwpJ+nDrrc5fYwvAbg==", "signatures": [{"sig": "MEYCIQDluowVKBMhBpjCsdzjGVXJTfEiKvIyyBR/q/s9fV+xJgIhANJDlbHxQfrvkmqlY/7HAph9F8J/21BD6NEd3VqXIyqg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "80238427ffa779ab2e12f8c48210288911984b92", "gitHead": "9818c25e5f4b85d3dd2813ccaf022dfda9a2dee1", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha --require=${FS_MOCK:-mock-fs} test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["fixtures/has-inline-source-map.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "greenkeeper": {"ignore": ["find-up", "cross-env"]}, "_nodeVersion": "7.1.0", "dependencies": {"find-up": "^1.1.2", "test-exclude": "^3.3.0", "object-assign": "^4.1.0", "istanbul-lib-instrument": "^1.4.0-candidate.0"}, "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "mocha": "^3.2.0", "pmock": "^0.2.3", "mock-fs": "^3.12.1", "standard": "^8.6.0", "babel-cli": "^6.18.0", "coveralls": "^2.11.15", "cross-env": "^2.0.1", "babel-core": "^6.21.0", "standard-version": "^4.0.0", "babel-preset-es2015": "^6.18.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-3.1.1-candidate.0.tgz_1483393667154_0.34580974210985005", "host": "packages-12-west.internal.npmjs.com"}}, "3.1.1": {"name": "babel-plugin-istanbul", "version": "3.1.1", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@3.1.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-core/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "8c3690b5ffa14d2800f1aa543e5a8078717f8768", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-3.1.1.tgz", "integrity": "sha512-oeVz5esDuRQPSGjT+siV/Sbx9mNX9da4f6UMINLHqMGc5IuiOsXotIYrVcS+DGNTai0CR5JEun4fEY7uQslWmw==", "signatures": [{"sig": "MEQCIE+5u6VK+kou1TqQ/ZMmOwVqcNxsKQW28eOYujy7z++ZAiAtY8euHHh4QHlGqrhbPXzsVPnLDz3DLmqXCIGgsDT4KA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "8c3690b5ffa14d2800f1aa543e5a8078717f8768", "gitHead": "73a4247806eed1879deafeaf273a9dec5d35507e", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha --require=${FS_MOCK:-mock-fs} test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["fixtures/has-inline-source-map.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "greenkeeper": {"ignore": ["find-up", "cross-env"]}, "_nodeVersion": "7.1.0", "dependencies": {"find-up": "^1.1.2", "test-exclude": "^3.3.0", "object-assign": "^4.1.0", "istanbul-lib-instrument": "^1.4.0"}, "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "mocha": "^3.2.0", "pmock": "^0.2.3", "mock-fs": "^3.12.1", "standard": "^8.6.0", "babel-cli": "^6.18.0", "coveralls": "^2.11.15", "cross-env": "^2.0.1", "babel-core": "^6.21.0", "standard-version": "^4.0.0", "babel-preset-es2015": "^6.18.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-3.1.1.tgz_1483494106797_0.9998535302001983", "host": "packages-12-west.internal.npmjs.com"}}, "3.1.2-candidate.0": {"name": "babel-plugin-istanbul", "version": "3.1.2-candidate.0", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@3.1.2-candidate.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-core/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "c058eea0e4aba3e7189dfb442059e1bf1fc320a7", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-3.1.2-candidate.0.tgz", "integrity": "sha512-ctzCQfIJTQikvVLr0UTUXdXEzwahk95piDWl624bk/98l0S88M+Q8sdu1++E+N3E7UBOGyGZZHSLeyAfhODQWg==", "signatures": [{"sig": "MEQCIF8H6hIsfcZub/DGbfQhjvzZmocZvaHKIhexNwWKVPUvAiAW7cjLBmGwDJ81wXFh+hMxkI91OIxmQBI2DNn17L0nuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "c058eea0e4aba3e7189dfb442059e1bf1fc320a7", "gitHead": "3eaa7fb86df7138b875f868840274322b8d8187d", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha --require=${FS_MOCK:-mock-fs} test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["fixtures/has-inline-source-map.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "greenkeeper": {"ignore": ["find-up", "cross-env"]}, "_nodeVersion": "7.1.0", "dependencies": {"find-up": "^1.1.2", "test-exclude": "^3.3.0", "object-assign": "^4.1.0", "istanbul-lib-instrument": "^1.4.1"}, "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "mocha": "^3.2.0", "pmock": "^0.2.3", "mock-fs": "^3.12.1", "standard": "^8.6.0", "babel-cli": "^6.18.0", "coveralls": "^2.11.15", "cross-env": "^2.0.1", "babel-core": "^6.21.0", "standard-version": "^4.0.0", "babel-preset-es2015": "^6.18.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-3.1.2-candidate.0.tgz_1483520370556_0.219689778983593", "host": "packages-12-west.internal.npmjs.com"}}, "3.1.2": {"name": "babel-plugin-istanbul", "version": "3.1.2", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@3.1.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-core/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "11d5abde18425ec24b5d648c7e0b5d25cd354a22", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-3.1.2.tgz", "integrity": "sha512-2JhVigOPM/hqUonjl8otcjQD7z/SgXCVQEMCtOxP1jTzLYl0QPY/YR9H4hDtoG3w5l/YGSktJlA0tPPs0waeLg==", "signatures": [{"sig": "MEQCIER7clgxNk3RgNTHF1/ZspH8S6dJpJPiV9Yg5UNe0GrBAiBKiitRDbsvO16MAEbyM2Gks+uXNYhP8bKlcxa4CnDNYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "11d5abde18425ec24b5d648c7e0b5d25cd354a22", "gitHead": "86c1c0968ddea451179c4f164ce92d1f9ea29317", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha --require=${FS_MOCK:-mock-fs} test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["fixtures/has-inline-source-map.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "greenkeeper": {"ignore": ["find-up", "cross-env"]}, "_nodeVersion": "7.1.0", "dependencies": {"find-up": "^1.1.2", "test-exclude": "^3.3.0", "object-assign": "^4.1.0", "istanbul-lib-instrument": "^1.4.2"}, "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "mocha": "^3.2.0", "pmock": "^0.2.3", "mock-fs": "^3.12.1", "standard": "^8.6.0", "babel-cli": "^6.18.0", "coveralls": "^2.11.15", "cross-env": "^2.0.1", "babel-core": "^6.21.0", "standard-version": "^4.0.0", "babel-preset-es2015": "^6.18.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-3.1.2.tgz_1483526040664_0.4630613827612251", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.0": {"name": "babel-plugin-istanbul", "version": "4.0.0", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@4.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-core/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "36bde8fbef4837e5ff0366531a2beabd7b1ffa10", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-4.0.0.tgz", "integrity": "sha512-l4xoI+/dbPUbp0LOaWuGjaJfAnmRx8zngvD89+1hZlBNSJ7uwpljsgdweKFyGy43M5rkO5kEJOTNKkDwKzHNzA==", "signatures": [{"sig": "MEUCIACGVddNLCNno4IxSizP5wdhbB5Fgs8mf1oeTTL8C5f1AiEAnDvtC3bC21FowJvOcKTA6rxUnbqohbzn4Ul+ALxt8KY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "36bde8fbef4837e5ff0366531a2beabd7b1ffa10", "gitHead": "ac8abab753587dd1ce9b8094132e59d604d4a054", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["fixtures/has-inline-source-map.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "greenkeeper": {"ignore": ["find-up", "cross-env"]}, "_nodeVersion": "7.1.0", "dependencies": {"find-up": "^2.1.0", "test-exclude": "^4.0.0", "istanbul-lib-instrument": "^1.4.2"}, "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "mocha": "^3.2.0", "pmock": "^0.2.3", "standard": "^8.6.0", "babel-cli": "^6.18.0", "coveralls": "^2.11.16", "cross-env": "^3.1.4", "babel-core": "^6.21.0", "standard-version": "^4.0.0", "babel-preset-es2015": "^6.18.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-4.0.0.tgz_1486443774043_0.41790366754867136", "host": "packages-12-west.internal.npmjs.com"}}, "4.1.0-candidate.0": {"name": "babel-plugin-istanbul", "version": "4.1.0-candidate.0", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@4.1.0-candidate.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-core/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "24fe0781a84186029c68f402bdecd9482822fc83", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-4.1.0-candidate.0.tgz", "integrity": "sha512-Sc5LFQV3O8bRyBho+Puw2g4dKf94nqtg3EASTcQedxBeAybqPH1/Xd1UV+2YNtuQtB8XcK+KXgVmdWIMzVipqw==", "signatures": [{"sig": "MEUCIQCDoT/DTsBzhsC6Q+lKDNE8DkR5tYyyfCXoAFwedVwlrgIgTGZcgrG7nk8LJKbgfyUkJhLxMbESJ6aYgkRrOEVKTbA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "24fe0781a84186029c68f402bdecd9482822fc83", "gitHead": "4a0261d71b9a3f4813f505ad9677efa3bcad908d", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["fixtures/has-inline-source-map.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "greenkeeper": {"ignore": ["find-up", "cross-env"]}, "_nodeVersion": "7.1.0", "dependencies": {"find-up": "^2.1.0", "test-exclude": "^4.0.3", "istanbul-lib-instrument": "^1.6.1"}, "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "mocha": "^3.2.0", "pmock": "^0.2.3", "standard": "^8.6.0", "babel-cli": "^6.18.0", "coveralls": "^2.11.16", "cross-env": "^3.1.4", "babel-core": "^6.21.0", "standard-version": "^4.0.0", "babel-preset-es2015": "^6.18.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-4.1.0-candidate.0.tgz_1490078636445_0.5849248312879354", "host": "packages-18-east.internal.npmjs.com"}}, "4.1.1": {"name": "babel-plugin-istanbul", "version": "4.1.1", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@4.1.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-core/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "c12de0fc6fe42adfb16be56f1ad11e4a9782eca9", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-4.1.1.tgz", "integrity": "sha512-sAI67hh7X83AJOjOne7UHPW3YgjGKApRjSyr+JnH7xNHmcsyt6w/fri9imYtq11IE5xj6lEOjM6tlby+O7Hgxg==", "signatures": [{"sig": "MEUCIQDJ0ayXOI4HLsJpYeasIvVe0Qka59kz16NQHLdmQWsoVgIgbr5WKSLYT+knD+eTiaP071jJkNq//phm13FKhyNMTZU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "c12de0fc6fe42adfb16be56f1ad11e4a9782eca9", "gitHead": "eeb33282d12128882ffde866e7f314e1b0d52125", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["fixtures/has-inline-source-map.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "greenkeeper": {"ignore": ["find-up", "cross-env"]}, "_nodeVersion": "7.1.0", "dependencies": {"find-up": "^2.1.0", "test-exclude": "^4.0.3", "istanbul-lib-instrument": "^1.6.2"}, "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "mocha": "^3.2.0", "pmock": "^0.2.3", "standard": "^8.6.0", "babel-cli": "^6.18.0", "coveralls": "^2.11.16", "cross-env": "^3.1.4", "babel-core": "^6.21.0", "standard-version": "^4.0.0", "babel-preset-es2015": "^6.18.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-4.1.1.tgz_1490199059910_0.6427886805031449", "host": "packages-12-west.internal.npmjs.com"}}, "4.1.2-candidate.0": {"name": "babel-plugin-istanbul", "version": "4.1.2-candidate.0", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@4.1.2-candidate.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "0e25f89a977ff9ffb247b54626a584c86e371982", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-4.1.2-candidate.0.tgz", "integrity": "sha512-uuo7wzHQ0xt2vmuxtmCmqHfLfK9lp90Ej3vTLxUvCtngqkZtu9FIOSp/JpUdnvr6r2XRElle7B2Qmx45qX2wig==", "signatures": [{"sig": "MEYCIQDzii0AS6ai/qQk2GesdlSbds90ylsZ4EDXJJSV1TNN7AIhANg+bVlC9Jeeo6WLend1hoDIa5uc3t+rUWGpaq56okyJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "0e25f89a977ff9ffb247b54626a584c86e371982", "gitHead": "476a455333f6d33a0fe1fc69aa0af89e3ebae8f6", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["fixtures/has-inline-source-map.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "4.4.1", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "greenkeeper": {"ignore": ["find-up", "cross-env"]}, "_nodeVersion": "6.9.5", "dependencies": {"find-up": "^2.1.0", "test-exclude": "^4.0.3", "istanbul-lib-instrument": "^1.7.0"}, "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "mocha": "^3.2.0", "pmock": "^0.2.3", "standard": "^9.0.2", "babel-cli": "^6.18.0", "coveralls": "^2.11.16", "cross-env": "^3.1.4", "babel-core": "^6.24.0", "babel-register": "^6.24.0", "standard-version": "^4.0.0", "babel-preset-es2015": "^6.18.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-4.1.2-candidate.0.tgz_1490596354030_0.3777030461933464", "host": "packages-18-east.internal.npmjs.com"}}, "4.1.3": {"name": "babel-plugin-istanbul", "version": "4.1.3", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@4.1.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "6ee6280410dcf59c7747518c3dfd98680958f102", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-4.1.3.tgz", "integrity": "sha512-pXmfOHq36VLEvsMo0ILOmfmlTE61fQtM5qujVLvb4ea05F3guTkFOrREOPuu+BhRy8K/57RYZo5cMw+kKH/B/Q==", "signatures": [{"sig": "MEUCIQClmYm8tv+z1FEtlcj0dwGBm8vHxlTQego3e3k/RSrGYAIgKvUA2l1AE3+1f1NyitBvQ+ZFVo+oOQaXG6KcmAU1Zkw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "6ee6280410dcf59c7747518c3dfd98680958f102", "gitHead": "2bdc94bdde205bfbfc55781ba4aa18692fec7584", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["fixtures/has-inline-source-map.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "greenkeeper": {"ignore": ["find-up", "cross-env"]}, "_nodeVersion": "7.1.0", "dependencies": {"find-up": "^2.1.0", "test-exclude": "^4.1.0", "istanbul-lib-instrument": "^1.7.1"}, "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "mocha": "^3.2.0", "pmock": "^0.2.3", "standard": "^9.0.2", "babel-cli": "^6.18.0", "coveralls": "^2.11.16", "cross-env": "^3.1.4", "babel-core": "^6.24.0", "babel-register": "^6.24.0", "standard-version": "^4.0.0", "babel-preset-es2015": "^6.18.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-4.1.3.tgz_1493443317516_0.35226250998675823", "host": "packages-12-west.internal.npmjs.com"}}, "4.1.4": {"name": "babel-plugin-istanbul", "version": "4.1.4", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@4.1.4", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "18dde84bf3ce329fddf3f4103fae921456d8e587", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-4.1.4.tgz", "integrity": "sha512-y0RwMTUGg6T2UBJE13gzi48MpRqq66vlpxw7YGt3SecqqjZMDV4mHDWqhQ/guznVaVDpN9tz/818jKFYOv9qrA==", "signatures": [{"sig": "MEQCIFYVgREuA75XzLHIqOlrq6Wz7Y6rYahNoBAt9QBcqQZCAiBfIci5rzoF2Zy6ZezOxtq6aq4NHIjdmKqavcph7vhciw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "18dde84bf3ce329fddf3f4103fae921456d8e587", "gitHead": "fca6a458b0d449bf09542ffd26086841676b3b92", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["fixtures/has-inline-source-map.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "greenkeeper": {"ignore": ["find-up", "cross-env"]}, "_nodeVersion": "7.3.0", "dependencies": {"find-up": "^2.1.0", "test-exclude": "^4.1.1", "istanbul-lib-instrument": "^1.7.2"}, "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "mocha": "^3.2.0", "pmock": "^0.2.3", "standard": "^9.0.2", "babel-cli": "^6.18.0", "coveralls": "^2.11.16", "cross-env": "^3.1.4", "babel-core": "^6.24.0", "babel-register": "^6.24.0", "standard-version": "^4.0.0", "babel-preset-es2015": "^6.18.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-4.1.4.tgz_1495921510550_0.4654520933981985", "host": "s3://npm-registry-packages"}}, "4.1.5": {"name": "babel-plugin-istanbul", "version": "4.1.5", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@4.1.5", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "6760cdd977f411d3e175bb064f2bc327d99b2b6e", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-4.1.5.tgz", "integrity": "sha512-uOxwl1iYngetP9Ci36B5XC29bZfkiXs3bi4NTc5wxhtH365/kFRTBW04chlyx/6heRVTnOfL08JYEiLeKI2oXA==", "signatures": [{"sig": "MEYCIQCvD8NFjBNCry0Sb4v5D30iX6gYq/ivvDa9xSLYDYis4gIhAMQd5SqqYolUhXO2CAwBEjYaTsv67BNGr5xqjPtJcPvj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "6760cdd977f411d3e175bb064f2bc327d99b2b6e", "gitHead": "34c4c726dbd72d0b944bcf816fd68be48ee02ed6", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["fixtures/has-inline-source-map.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "greenkeeper": {"ignore": ["find-up", "cross-env"]}, "_nodeVersion": "7.1.0", "dependencies": {"find-up": "^2.1.0", "test-exclude": "^4.1.1", "istanbul-lib-instrument": "^1.7.5"}, "devDependencies": {"nyc": "^11.1.0", "chai": "^4.1.0", "mocha": "^3.2.0", "pmock": "^0.2.3", "standard": "^9.0.2", "babel-cli": "^6.18.0", "coveralls": "^2.11.16", "cross-env": "^3.1.4", "babel-core": "^6.24.0", "babel-register": "^6.24.0", "standard-version": "^4.0.0", "babel-preset-es2015": "^6.18.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul-4.1.5.tgz_1505578014391_0.4694260179530829", "host": "s3://npm-registry-packages"}}, "4.1.6": {"name": "babel-plugin-istanbul", "version": "4.1.6", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@4.1.6", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["babel-register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "36c59b2192efce81c5b378321b74175add1c9a45", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-4.1.6.tgz", "fileCount": 5, "integrity": "sha512-PWP9FQ1AhZhS01T/4qLSKoHGY/xvkZdVBGlKM/HuxxS3+sC66HhTNR7+MpbO/so/cz/wY94MeSWJuP1hXIPfwQ==", "signatures": [{"sig": "MEUCIQDdOCBgzZoC31daspTUSSHRtrfbvfumx7Wc+N/R6IjGvgIgFA8uut2FQYSVeNqRXbx/M3a1vFT+UEA0QCB2OBFycoA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19506}, "main": "lib/index.js", "files": ["lib"], "gitHead": "321740f7b25d803f881466ea819d870f7ed6a254", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["fixtures/has-inline-source-map.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "greenkeeper": {"ignore": ["find-up", "cross-env"]}, "_nodeVersion": "8.8.1", "dependencies": {"find-up": "^2.1.0", "test-exclude": "^4.2.1", "istanbul-lib-instrument": "^1.10.1", "babel-plugin-syntax-object-rest-spread": "^6.13.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.1.0", "chai": "^4.1.0", "mocha": "^4.0.0", "pmock": "^0.2.3", "standard": "^9.0.2", "babel-cli": "^6.18.0", "coveralls": "^3.0.0", "cross-env": "^3.1.4", "babel-core": "^6.24.0", "babel-register": "^6.24.0", "babel-preset-env": "^1.6.1", "standard-version": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul_4.1.6_1520635775116_0.9113064063097747", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "babel-plugin-istanbul", "version": "5.0.0", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@5.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["@babel/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "e23d36efd7882bd39493e4a2cbea073ca2d927ac", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-qeCAG9Dn+L0s32kBDHwIsPPvc5D6fR1qT7XsNpqBhs03tm8EJA54d7GNttIy80qZBDdEc7dQF2NIfCE01s5Liw==", "signatures": [{"sig": "MEQCIElE6mFkstyYYZBSHTi+iEl+ho66diu2O9Ac5cMhs0pLAiBEE7FU4HlEVwUcm/dl7wNjzEqTBlWN7YXMDv+Tr2nKAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbMt31CRA9TVsSAnZWagAAgMUP/3yc1GLOf1mQmQaNN48/\nyIPfnDR1RbKGgpZdYIvbQes8kCf6VSfTdbB9CEijJG6ckmXIl0+TFU4CBkZS\nBeDnXIGbwWCYRsWg6sQUQLaVESt82TRBl1qr0tD0ztAb/wgWIr+MoM2Imwox\nzbrFjx1PQIm/L9jysy6gryLT9cmWAgM7ZbgfD2bL6sJ+P4IhvVMNCL8b+Bsd\nnW1KIRfFjZqpWJ+vwIXZ9hkDULHdzKaClCbQe+l3wccXOLPtkCcPyI5o2gC1\n8Rb9Ey2ewVcCZAuWooUdy2h6MEsUEhyd9bNJHgJ1swLcEssFRxcFb1QRaqaG\nMsdFzoerwAySv8+LAsgUOS/cHXDYyUpl8P5OAU2vg7Buuqx47wOoTYVYNt9g\nW/g6/vQHQmZLwOYar+parxLrmC1bUtauMvXiMgnyuN5vrDVXy+FQdj+2pDxJ\nD5W9qLpPjpT7yITXzQ9bGlFjkedDvYxoc78VPmQDjHnRihnObz07azz59PSK\ntC/1P+kz5cOO7grjeJgEZ2SuQ1DwyjQu0WQ8srSgIaAQgeP7T/eG16lB76Wk\ng3mKOIyZJC46Dd0ailTHXV8E1+UNQOwycelyVnqdDESgaPmfxBf5WztUm8aB\ntQTFn58egCGss+KhDqQVoeTcIMp8YePg1BrRkrfvsWaALhBp8IvFwy8VgYI/\nmXJR\r\n=8t+7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "files": ["lib"], "readme": "# babel-plugin-istanbul\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/babel-plugin-istanbul.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/babel-plugin-istanbul.svg?branch=master)](https://travis-ci.org/istanbuljs/babel-plugin-istanbul)\n[![Coverage Status](https://coveralls.io/repos/github/istanbuljs/babel-plugin-istanbul/badge.svg?branch=master)](https://coveralls.io/github/istanbuljs/babel-plugin-istanbul?branch=master)\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n[![community slack](http://devtoolscommunity.herokuapp.com/badge.svg)](http://devtoolscommunity.herokuapp.com)\n\n_Having problems? want to contribute? join our [community slack](http://devtoolscommunity.herokuapp.com)_.\n\nA Babel plugin that instruments your code with Istanbul coverage.\nIt can instantly be used with [karma-coverage](https://github.com/karma-runner/karma-coverage) and mocha on Node.js (through [nyc](https://github.com/bcoe/nyc)).\n\n__Note:__ This plugin does not generate any report or save any data to any file;\nit only adds instrumenting code to your JavaScript source code.\nTo integrate with testing tools, please see the [Integrations](#integrations) section.\n\n## Usage\n\nInstall it:\n\n```\nnpm install --save-dev babel-plugin-istanbul\n```\n\nAdd it to `.babelrc` in test mode:\n\n```js\n{\n  \"env\": {\n    \"test\": {\n      \"plugins\": [ \"istanbul\" ]\n    }\n  }\n}\n```\n\nOptionally, use [cross-env](https://www.npmjs.com/package/cross-env) to set\n`NODE_ENV=test`:\n\n```json\n{\n  \"scripts\": {\n    \"test\": \"cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js\"\n  }\n}\n```\n\n## Integrations\n\n### karma\n\nIt _just works_ with Karma. First, make sure that the code is already transpiled by Babel (either using `karma-babel-preprocessor`, `karma-webpack`, or `karma-browserify`). Then, simply set up [karma-coverage](https://github.com/karma-runner/karma-coverage) according to the docs, but __don’t add the `coverage` preprocessor.__ This plugin has already instrumented your code, and Karma should pick it up automatically.\n\nIt has been tested with [bemusic/bemuse](https://codecov.io/github/bemusic/bemuse) project, which contains ~2400 statements.\n\n### mocha on node.js (through nyc)\n\nConfigure Mocha to transpile JavaScript code using Babel, then you can run your tests with [`nyc`](https://github.com/bcoe/nyc), which will collect all the coverage report.\n\nbabel-plugin-istanbul respects the `include`/`exclude` configuration options from nyc,\nbut you also need to __configure NYC not to instrument your code__ by adding these settings in your `package.json`:\n\n```js\n  \"nyc\": {\n    \"sourceMap\": false,\n    \"instrument\": false\n  },\n```\n\n## Ignoring files\n\nYou don't want to cover your test files as this will skew your coverage results. You can configure this by providing plugin options matching nyc's [`exclude`/`include` rules](https://github.com/bcoe/nyc#excluding-files):\n\n```json\n{\n  \"env\": {\n    \"test\": {\n      \"plugins\": [\n        [\"istanbul\", {\n          \"exclude\": [\n            \"**/*.spec.js\"\n          ]\n        }]\n      ]\n    }\n  }\n}\n```\n\nIf you don't provide options in your Babel config, the plugin will look for `exclude`/`include` config under an `\"nyc\"` key in `package.json`.\n\nYou can also use [istanbul's ignore hints](https://github.com/gotwarlost/istanbul/blob/master/ignoring-code-for-coverage.md#ignoring-code-for-coverage-purposes) to specify specific lines of code to skip instrumenting.\n\n## Source Maps\n\nBy default, this plugin will pick up inline source maps and attach them to the instrumented code such that code coverage can be remapped back to the original source, even for multi-step build processes. This can be memory intensive. Set `useInlineSourceMaps` to prevent this behavior.\n\n```json\n{\n  \"env\": {\n    \"test\": {\n      \"plugins\": [\n        [\"istanbul\", {\n          \"useInlineSourceMaps\": false\n        }]\n      ]\n    }\n  }\n}\n```\n\nIf you're instrumenting code programatically, you can pass a source map explicitly.\n```js\nimport babelPluginIstanbul from 'babel-plugin-istanbul';\n\nfunction instrument(sourceCode, sourceMap, fileName) {\n  return babel.transform(sourceCode, {\n    filename,\n    plugins: [\n      [babelPluginIstanbul, {\n        inputSourceMap: sourceMap\n      }]\n    ]\n  })\n}\n```\n\n## Credit where credit is due\n\nThe approach used in `babel-plugin-istanbul` was inspired by [Thai Pangsakulyanont](https://github.com/dtinth)'s original library [`babel-plugin-__coverage__`](https://github.com/dtinth/babel-plugin-__coverage__).\n", "gitHead": "9f006e92bb46b46f64c54f0f17775e6108778d2a", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "standard": {"ignore": ["fixtures/has-inline-source-map.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "6.1.0-next.0", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"find-up": "^3.0.0", "test-exclude": "^4.2.2", "istanbul-lib-instrument": "^2.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^12.0.2", "chai": "^4.1.2", "mocha": "^5.2.0", "pmock": "^0.2.3", "standard": "^11.0.1", "coveralls": "^3.0.1", "cross-env": "^5.1.6", "@babel/cli": "7.0.0-beta.51", "@babel/core": "7.0.0-beta.51", "@babel/register": "7.0.0-beta.51", "standard-version": "^4.4.0", "@babel/preset-env": "7.0.0-beta.51"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul_5.0.0_1530060277124_0.2752766090599166", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "babel-plugin-istanbul", "version": "5.0.1", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@5.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["@babel/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "2ce7bf211f0d9480ff7fd294bd05e2fa555e31ea", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-5.0.1.tgz", "fileCount": 5, "integrity": "sha512-MQXE+9sTKevc0S6pfYUdpF5aA9mktCg9Jh9hIl/RriGEuUbUqPOK94VBBAlHsz88yIoQSRfPeblA3cPuudMs6Q==", "signatures": [{"sig": "MEUCIEiTKDF3HOn0PljepsqHjxJNI1JpU9cuNugsbtGVyPbSAiEAkHPKA7PtmYADwpvlN1sSl7WZ6YR831BRUyQRPIyWSo8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19812, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbV65TCRA9TVsSAnZWagAAxZkP/3cGAv2swXYQSRfMnDwe\nfn3oX2qk6oROZBZ4som1kJdWrF3smPm50gg051BiigcG/MlQEvnuVvgsPz6m\nk95vPMN5MYj48Yp0VlU87iCyQSyqcBlPz2c+D9/XBE/ZzOZJt5xLaenuh2As\nj3oMIl5X7TWlV/rqzcu0EFqJbTc4tv+cmTFSG2irOPbtCLzHJlNrSmKXUP64\ndPZZ6JG7uzWdwqNCBNJpiWe1tZuBSa5lg5KqQu2ArW2SUvvSWKWKDYQGQ/Sz\nroHIwBEz+clM0uB3747ODdghez5CcP7+aqqlY2hxqrDuHt4sj+LGjgPiW36r\nbOFonXf0P5zFVimBvbHE7dA99ZRz0OtLUKkDh09p1JthjiACEPqAJ+nDnxN9\nimRiS3qeqbgCcueCL6/cQgMEZXDFXfFZfLAaMBxibc74NwJwOOlvjQz3c1wr\nS8lbnCwU6YHEtxNyB4ifJpA5h2t4Fosb21yp3+4z+fqGppjTods4HMNmDUnj\nbEBlbnUhLwWFIlsFOuYYLkpp4IA0CWXfPrTX0+YapQdRR/uh56gH/o7mk3Ls\nfwJvgBjbXT75rSLWouXXfhx0q9Pk1dAd9lHIPeLJZgQUbAQ2TFFOFYupX+ig\nWDurlC24iUdOHPYeFg0+STAWaMb6pIqNtdJe5VPANIwYNw8Miv8cfcRjruMh\nVvL5\r\n=RTnR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "files": ["lib"], "engines": {"node": ">=6"}, "gitHead": "ad1177e09f579e1044e31102610dc42a48c08264", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "standard": {"ignore": ["fixtures/has-inline-source-map.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "_nodeVersion": "10.6.0", "dependencies": {"find-up": "^3.0.0", "test-exclude": "^5.0.0", "istanbul-lib-instrument": "^2.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.0.0", "chai": "^4.1.2", "mocha": "^5.2.0", "pmock": "^0.2.3", "standard": "^11.0.1", "coveralls": "^3.0.1", "cross-env": "^5.1.6", "@babel/cli": "7.0.0-beta.51", "@babel/core": "7.0.0-beta.51", "@babel/register": "7.0.0-beta.51", "standard-version": "^4.4.0", "@babel/preset-env": "7.0.0-beta.51"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul_5.0.1_1532472915092_0.21293280825719885", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "babel-plugin-istanbul", "version": "5.1.0", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@5.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["@babel/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "6892f529eff65a3e2d33d87dc5888ffa2ecd4a30", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-5.1.0.tgz", "fileCount": 5, "integrity": "sha512-CLoXPRSUWiR8yao8bShqZUIC6qLfZVVY3X1wj+QPNXu0wfmrRRfarh1LYy+dYMVI+bDj0ghy3tuqFFRFZmL1Nw==", "signatures": [{"sig": "MEQCIEVpt1M/L7SVCIlC9d/7oQUDKCYM+r9cLZiQm7v9HsmxAiBhT+H/t+765YB03e4lOJKyK/B6Z1SeFvjAMoANMk6kqA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20025, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbksJlCRA9TVsSAnZWagAA9VAP/RDmiQ/p2/L2wrL+cHeW\n00v/2mrQpJ27vBc/TpTvSyHBYXyz0Kj1XyB80r0BfKQafHvwcsgHl37jJy2n\nlEX7ftY2nNj5widia8/mndb9YNVK1gAuJSidsMPMPzQNK+O6R99y3I1M12Uq\n2FfKexV8I9rCbSwch+5VfeporXGHx2NMZ5mOUZyiF1qIkdXLTCiybkDwMutQ\nerF/tb/vCJkE4e70axWn7zdXE1AwNMotdnV42x0vtEjDDOkr9rAXl2Kiki4H\nORkFmsZCK0rRKokJDJHrGxQSiPqns/xa4SHBeCBHVs1ucJqvZGlPmG/Pcyu6\n/4G6kXtI3yHgq4P0lqhcDA4EXbcBo5DhIHAZpFDdVOBCg16/5creVAnSDd3J\nJQdxR0NvO450cAzShZz/iaCBCCxo8jYQuT2Tws+NXm2uEnUvItAkYpBC/juI\npuzwDjibUMqOPOgISCI4eAF2VI3QcAvy/c0cA1EoTLcXrGxioApTc/4e5gVK\n+DvrpXS8lEJr3SmODETgwCebjj2geppesTAgCgb32aP1QWlRfQ+NLU+imqCC\n1P/S7r1h5i7O8I2/Vsnxnpju/Dn9uKnrWdGJMpF1ZtzimLkCSCJ48iNT7cbB\nFNJfqrg0c2hzOI6Y/d1XcmEWtx1VadlldiJ9MSHB0p+hoLSzJuSntvCmXsXb\ntqeN\r\n=qifh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=6"}, "gitHead": "61d3c44f5c1f653be6e1d9f76ea6abe87c3782e2", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "standard": {"ignore": ["fixtures/has-inline-source-map.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {"find-up": "^3.0.0", "test-exclude": "^5.0.0", "istanbul-lib-instrument": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.1.0", "chai": "^4.1.2", "mocha": "^5.2.0", "pmock": "^0.2.3", "standard": "^11.0.1", "coveralls": "^3.0.1", "cross-env": "^5.1.6", "@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/register": "^7.0.0", "standard-version": "^4.4.0", "@babel/preset-env": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul_5.1.0_1536344677220_0.896009530541457", "host": "s3://npm-registry-packages"}}, "5.1.1": {"name": "babel-plugin-istanbul", "version": "5.1.1", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@5.1.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["@babel/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "7981590f1956d75d67630ba46f0c22493588c893", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-5.1.1.tgz", "fileCount": 5, "integrity": "sha512-RNNVv2lsHAXJQsEJ5jonQwrJVWK8AcZpG1oxhnjCUaAjL7xahYLANhPUZbzEQHjKy1NMYUwn+0NPKQc8iSY4xQ==", "signatures": [{"sig": "MEYCIQDmzqD7LC2TZhivV0w3/N2a+8qnoZAAYDkSQT7UiZE5KwIhAKoHdKWxXWn3MXonByfkEHutOxSZsKLVRyj966p0rMm2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20384, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcN/6fCRA9TVsSAnZWagAAfKgP/3TVIF2JZWlfHiyNzgNc\nUnoASCVmqGNd8Lm8KP0/8gWJxUdEwY913C9cqYO5DlWUmfDSjwphfdsne6Dx\nqQGVCJLn4yZ+AqW0E0oCrHWUPj0xOi2VOyuGCAA42pCb7mkz9Z/AsYhBKEeh\nEf4iSXSQNY/9ZrjSpyxLl4VsGOw716AY1geU/HevLUy9HhTLh22WA33pWBX9\nuncaHsTq7NcBosCiw79ZxZFtazZxCCitOPy4VQSvIUGvKLV7wlhDYcWvAfvI\nsgecH3sgftZsSxUj818wPe3rM7zMAz+vAiWxPlKBRrCMz6z4DuLSdMokXMmn\nSMq+GO9kzsEbmantQpA+YkzglMOSGbTJu/SwM0IPkx2tO7MQK9XbSewFvLuK\n7LKjyvdwYFK7MId2EmOkD8yQQqA+Zv5u9uiNOINcMmj90cHTowEAFwNOtQpD\nqQ6xsmmdQvw0xbqO2iUDlb7+ox5k+xPcPu0u5AxIwX0M4zcpPypksJQSpdhI\n5y8h/1fyUONOAa26d2L48gjO7Eh2Fi2e0jv0wlhEEnqwGdmXhmCa7O1owe1F\nYSt+U8k6G6Jfntt400kO7jKRMACJUjbcCqlePC22d09XJf2qP6L9qZKJf/S+\n47VbGgYyzafHTRwC4wFoMpAieu494iOfCkVkftE+RkMj96jY6qMaPsDwJDvE\nTztT\r\n=PDKY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=6"}, "gitHead": "de543208fa6c2ba503f7a88f6e6ec8d7b2577fe5", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "standard": {"ignore": ["fixtures/has-inline-source-map.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "_nodeVersion": "10.14.2", "dependencies": {"find-up": "^3.0.0", "test-exclude": "^5.0.0", "istanbul-lib-instrument": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.1.0", "chai": "^4.2.0", "mocha": "^5.2.0", "pmock": "^0.2.3", "standard": "^12.0.1", "coveralls": "^3.0.2", "cross-env": "^5.2.0", "@babel/cli": "^7.2.3", "@babel/core": "^7.2.2", "@babel/register": "^7.0.0", "standard-version": "^4.4.0", "@babel/preset-env": "^7.2.3"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul_5.1.1_1547173535532_0.17412667264951742", "host": "s3://npm-registry-packages"}}, "5.1.2": {"name": "babel-plugin-istanbul", "version": "5.1.2", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@5.1.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["@babel/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "d8c2e2e83f72695d6bfdcd297719c66161d5f0f9", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-5.1.2.tgz", "fileCount": 5, "integrity": "sha512-U3ZVajC+Z69Gim7ZzmD4Wcsq76i/1hqDamBfowc1tWzWjybRy70iWfngP2ME+1CrgcgZ/+muIbPY/Yi0dxdIkQ==", "signatures": [{"sig": "MEQCIFnw3dt+MsX0v0tHOrVz2uXf9fs0jSEw9lNnwiP5/TDYAiAS0cDZDHlXcoMhkn/6tcZr63o1Xps1nAL62/Fuw1gUVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20919, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrTPpCRA9TVsSAnZWagAAFZ8P/AxYGuJ6vreiCPnPvSxo\nBdWZdBaraJeAukpQF1f0y8ZNen+qnrpZPXyLQuPY7VopFGKNmgrLviQD0jjd\nua9zlLflrnfXXKrM3K6z2myhyj16UgB3YxKJ+6sM4ie8q4f0Y4HY2dmYTXIp\nvUT9g8DG+ZlWsw3BCWsEAoAOGyP++w9uGuEimEX/NvTYNKMGwzSYckOHx0uR\n4NUterpgr9wDmnHmcsdSOAY8OFhkEJLlG9x5rHm7sv/mYSd75Cq68WMF+0ci\ngFcW0vncsyldXscjzG7icYwrlPpkTJYr2UaMiAlZAsQWKTNTLxZs3W+/iwdN\np7rZjAEPZv/As7dkSIwgpWzQi8CBGhIsIITwBjwaG7CyidA+NbpERmg1kyrb\nNwzlpe63rsa5GHGZuFNYiRsRp6ZstoOMzqckeh5XcmUszV4l5p4NjLZ18voA\n8yk7PQMfuPqBemVvMf7o08jNcXXKbqzdNoLi0r9kzNAOXlqDYDvyVNCeE9oT\n0yqKAHKHC+4VPmdTBezQOCdR3Iw2mOpWP5TCOk18yhi8stugCqXuX975gH4Y\n4+WP/dsN/zDwK0jgZUGxZc6oU/8MH0CueLLfGknkkBOv4i1z7EJ7ash53DYi\nFJ5ge/FKoC8XVWviivlgGQXZmjG6uWY0XSplM5n9upLf7X0MqE30CEzyHkEw\nKIOR\r\n=5f6Z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "readme": "# babel-plugin-istanbul\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/babel-plugin-istanbul.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/babel-plugin-istanbul.svg?branch=master)](https://travis-ci.org/istanbuljs/babel-plugin-istanbul)\n[![Coverage Status](https://coveralls.io/repos/github/istanbuljs/babel-plugin-istanbul/badge.svg?branch=master)](https://coveralls.io/github/istanbuljs/babel-plugin-istanbul?branch=master)\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n[![community slack](http://devtoolscommunity.herokuapp.com/badge.svg)](http://devtoolscommunity.herokuapp.com)\n\n_Having problems? want to contribute? join our [community slack](http://devtoolscommunity.herokuapp.com)_.\n\nA Babel plugin that instruments your code with Istanbul coverage.\nIt can instantly be used with [karma-coverage](https://github.com/karma-runner/karma-coverage) and mocha on Node.js (through [nyc](https://github.com/bcoe/nyc)).\n\n__Note:__ This plugin does not generate any report or save any data to any file;\nit only adds instrumenting code to your JavaScript source code.\nTo integrate with testing tools, please see the [Integrations](#integrations) section.\n\n## Usage\n\nInstall it:\n\n```\nnpm install --save-dev babel-plugin-istanbul\n```\n\nAdd it to `.babelrc` in test mode:\n\n```js\n{\n  \"env\": {\n    \"test\": {\n      \"plugins\": [ \"istanbul\" ]\n    }\n  }\n}\n```\n\nOptionally, use [cross-env](https://www.npmjs.com/package/cross-env) to set\n`NODE_ENV=test`:\n\n```json\n{\n  \"scripts\": {\n    \"test\": \"cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js\"\n  }\n}\n```\n\n## Integrations\n\n### karma\n\nIt _just works_ with Karma. First, make sure that the code is already transpiled by Babel (either using `karma-babel-preprocessor`, `karma-webpack`, or `karma-browserify`). Then, simply set up [karma-coverage](https://github.com/karma-runner/karma-coverage) according to the docs, but __don’t add the `coverage` preprocessor.__ This plugin has already instrumented your code, and Karma should pick it up automatically.\n\nIt has been tested with [bemusic/bemuse](https://codecov.io/github/bemusic/bemuse) project, which contains ~2400 statements.\n\n### mocha on node.js (through nyc)\n\nConfigure Mocha to transpile JavaScript code using Babel, then you can run your tests with [`nyc`](https://github.com/bcoe/nyc), which will collect all the coverage report.\n\nbabel-plugin-istanbul respects the `include`/`exclude` configuration options from nyc,\nbut you also need to __configure NYC not to instrument your code__ by adding these settings in your `package.json`:\n\n```js\n  \"nyc\": {\n    \"sourceMap\": false,\n    \"instrument\": false\n  },\n```\n\n## Ignoring files\n\nYou don't want to cover your test files as this will skew your coverage results. You can configure this by providing plugin options matching nyc's [`exclude`/`include` rules](https://github.com/bcoe/nyc#excluding-files):\n\n```json\n{\n  \"env\": {\n    \"test\": {\n      \"plugins\": [\n        [\"istanbul\", {\n          \"exclude\": [\n            \"**/*.spec.js\"\n          ]\n        }]\n      ]\n    }\n  }\n}\n```\n\nIf you don't provide options in your Babel config, the plugin will look for `exclude`/`include` config under an `\"nyc\"` key in `package.json`.\n\nYou can also use [istanbul's ignore hints](https://github.com/gotwarlost/istanbul/blob/master/ignoring-code-for-coverage.md#ignoring-code-for-coverage-purposes) to specify specific lines of code to skip instrumenting.\n\n## Source Maps\n\nBy default, this plugin will pick up inline source maps and attach them to the instrumented code such that code coverage can be remapped back to the original source, even for multi-step build processes. This can be memory intensive. Set `useInlineSourceMaps` to prevent this behavior.\n\n```json\n{\n  \"env\": {\n    \"test\": {\n      \"plugins\": [\n        [\"istanbul\", {\n          \"useInlineSourceMaps\": false\n        }]\n      ]\n    }\n  }\n}\n```\n\nIf you're instrumenting code programatically, you can pass a source map explicitly.\n```js\nimport babelPluginIstanbul from 'babel-plugin-istanbul';\n\nfunction instrument(sourceCode, sourceMap, fileName) {\n  return babel.transform(sourceCode, {\n    filename,\n    plugins: [\n      [babelPluginIstanbul, {\n        inputSourceMap: sourceMap\n      }]\n    ]\n  })\n}\n```\n\n## Credit where credit is due\n\nThe approach used in `babel-plugin-istanbul` was inspired by [Thai Pangsakulyanont](https://github.com/dtinth)'s original library [`babel-plugin-__coverage__`](https://github.com/dtinth/babel-plugin-__coverage__).\n", "engines": {"node": ">=6"}, "gitHead": "1a274e2571e6d44592d7fa7d956a25366471c3ac", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "standard": {"ignore": ["fixtures/has-inline-source-map.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"find-up": "^3.0.0", "test-exclude": "^5.2.2", "istanbul-lib-instrument": "^3.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^14.0.0-rc.1", "chai": "^4.2.0", "mocha": "^6.1.2", "pmock": "^0.2.3", "standard": "^12.0.1", "coveralls": "^3.0.3", "cross-env": "^5.2.0", "@babel/cli": "^7.4.3", "@babel/core": "^7.4.3", "@babel/register": "^7.4.0", "standard-version": "^5.0.2", "@babel/preset-env": "^7.4.3", "babel-plugin-lodash": "^3.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul_5.1.2_1554854888876_0.7084804445618365", "host": "s3://npm-registry-packages"}}, "5.1.3": {"name": "babel-plugin-istanbul", "version": "5.1.3", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@5.1.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["@babel/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "202d20ffc96a821c68a3964412de75b9bdeb48c7", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-5.1.3.tgz", "fileCount": 5, "integrity": "sha512-IFyehbvRRwdBlI1lDp+FaMsWNnEndEk7065IB8NhzBX+ZKLPwPodgk4I5Gobw/8SNUUzso2Dv3hbqRh88eiSCQ==", "signatures": [{"sig": "MEQCIFWH/pllAyVOY8rhc5FWe7CS4oClh3oVnWXCrHCVUYVYAiAHEe8nMtBArVDTnY7E/ai6oP+2X/h26MYisaBvv0ffKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcvvURCRA9TVsSAnZWagAAns8P/RLS31JKshBjfLHs0fRt\nsLvnaR5xunouTMOpCiV4fx7nGLhBTHB7EM4Hb2sfibPmjzzJbpK9EjUQ/eSF\nzTiJeaWVcfg2GfJGUo4CJ7DZhuV3llhkbCh2B4BpGiMHXd7bLUnvKNvTNFzJ\nqVXSuQVdEFhv4ZNN/p1aAGJMIh6omQ+rQnJnJS7t1LxByqOlpM5vdq0fGHtL\na7OZ7RWai0Kb8ZTMF+zAyCY+G07x9g0lef2THojPvpp+6MfldCb2yg8OpMqS\n/wwyOjmTPmiG/LSLl3Jnb7MOGZo/EnLoj0d8Ib7dKSoHvbShsvTE7Leb6dBF\nPAogRrV1yYSU0jL748CFPQWBQvRiQplli40kxoA7RXmE/KeLgWPBdFldbZ0B\npkR2r+dPdN86ijhn73caHfJ1asiwjuFLMfqTYzSjGTrZHbmzm45IqlOScVxq\n/HWqKS14upw6IQsQMUmw0kjnAlB6Kzgy4jRybuGwo2EFeXero5CS9YLKj2fd\n8KZJ6qwwFqMh/Rgmnp1c9qOHrWTt46V3hUjnbaUKYtmqvAgL6HyccWIvgq53\nFEPCAQWgoQSe+gOjiRUI6uXCLXQg39HqIviP4ErbnpM5r5Dn7iuk7NUhKyyE\nZpRYP4TRyhp7s0VQ82CwLr/7QjH3jJHbosvg3nozkJmIEIczTl8grnUQJ1Zr\nolt8\r\n=VgDA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=6"}, "gitHead": "047809a2c0303ec4b559e1c79328e77a2ec3ab24", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "standard": {"ignore": ["fixtures/has-inline-source-map.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"find-up": "^3.0.0", "test-exclude": "^5.2.2", "istanbul-lib-instrument": "^3.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.0.0", "chai": "^4.2.0", "mocha": "^6.1.4", "pmock": "^0.2.3", "standard": "^12.0.1", "coveralls": "^3.0.3", "cross-env": "^5.2.0", "@babel/cli": "^7.4.3", "@babel/core": "^7.4.3", "@babel/register": "^7.4.0", "standard-version": "^5.0.2", "@babel/preset-env": "^7.4.3", "@babel/plugin-transform-modules-commonjs": "^7.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul_5.1.3_1556018448372_0.8711725242529766", "host": "s3://npm-registry-packages"}}, "5.1.4": {"name": "babel-plugin-istanbul", "version": "5.1.4", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@5.1.4", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["@babel/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "841d16b9a58eeb407a0ddce622ba02fe87a752ba", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-5.1.4.tgz", "fileCount": 5, "integrity": "sha512-dySz4VJMH+dpndj0wjJ8JPs/7i1TdSPb1nRrn56/92pKOF9VKC1FMFJmMXjzlGGusnCAqujP6PBCiKq0sVA+YQ==", "signatures": [{"sig": "MEUCIQCBr4UYDEIPqH5hsyVqhg1w9oAvl6zE8+AvSieqw+SmPgIgWNZ3zN9vhjRiYuGKksvKWxtmGUtQt9nWulIYyOCN8d8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21258, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcwcKbCRA9TVsSAnZWagAAU0QP/jleLE3PgarfBpd42zns\nCtQDI2BvwRsUthr4OhwHHYU7emYo4rr5WX1npS+aNeN0i0u8QJHBDbMz9MyQ\nzsNJ5djDDnLh4UU4ycWG+tLwYjcaM44mZ7fqmWGS2qclam8pHXwzAcieyo3G\neSS5d8l15Rpj/fFpbt5/1Lq2Lln1BemYS1OSA16F6NAK0U1q+oOrULXwISMt\nhAAsvekNEXwM4l+ioHp0M5CI6FbLit2rRtH1HZ+BJRwMXY46gNc4edFdk+Fi\n8oJYcb/baEXnOxa8qwUvWqX2TGzcEbUeDD/I1kTUuex0o8qsTjTJ3RdbZgBt\n/v7HopbrR0hq1llDTMI2OBSecAqVQWshS5Stct7uttTySL270YAwS0/PfiNW\n2qTFyj4hyZbKoJbNJbHr4Kp2q7yy+OLZZkqKtiPkVSp8NM2NFbs8huSfHFmP\n1bsjlW/eeFmoSPED2BL1M+QYTRXZ5pwVXRbvsu1md1FgDd8CESh9ltO6SrpY\nb/Wge8DRZ+Q9C32kTyTVRrp7lNgkSZpB3uqGPGdaJpBmWHq4KjJ+jjyEXtXE\nzdKZ/cmik5ZlZGWFPKP3GOcFCo5unFkJM7PMY4PJ87yXYsq4cq1atnhRBzRp\nKrFlRzgdU63x/dAW6MEvl7j0XLrUdmgSv11WrfunPtua6JbPU9RHcc2ptg7q\nkLwi\r\n=kMDD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=6"}, "gitHead": "882049e851dc561f1bd35bdfdcc89f1965c5506b", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "standard": {"ignore": ["fixtures/has-inline-source-map.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "_nodeVersion": "12.0.0", "dependencies": {"find-up": "^3.0.0", "test-exclude": "^5.2.3", "istanbul-lib-instrument": "^3.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.0", "chai": "^4.2.0", "mocha": "^6.1.4", "pmock": "^0.2.3", "standard": "^12.0.1", "coveralls": "^3.0.3", "cross-env": "^5.2.0", "@babel/cli": "^7.4.3", "@babel/core": "^7.4.3", "@babel/register": "^7.4.0", "standard-version": "^5.0.2", "@babel/preset-env": "^7.4.3", "@babel/plugin-transform-modules-commonjs": "^7.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul_5.1.4_1556202138369_0.3206324701520795", "host": "s3://npm-registry-packages"}}, "5.2.0": {"name": "babel-plugin-istanbul", "version": "5.2.0", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@5.2.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["@babel/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "df4ade83d897a92df069c4d9a25cf2671293c854", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-5.2.0.tgz", "fileCount": 5, "integrity": "sha512-5LphC0USA8t4i1zCtjbbNb6jJj/9+X6P37Qfirc/70EQ34xKlMW+a1RHGwxGI+SwWpNwZ27HqvzAobeqaXwiZw==", "signatures": [{"sig": "MEQCID5Y3RDhVoYXSo1cWaSwtDoUfTyHz2zvmrgzOl5WAlHCAiBneuctL1DCo/Jh2UvVPRLdZE7OhZYHJRXdZVOWohMIvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22049, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdL/oLCRA9TVsSAnZWagAADYMQAKEQFKy8GO6Q2Ac2Ayhe\nR8eTBqqcL75znrEGUkzJ/k6rV0PSQw1nymx5yLJeRTgpKGMxDk+O4xeLT4Dk\nZUWcQLmmp35pgbXkPi8AtLBmm7R+rM8mlDo2FatnZevmP6SNzDj9xvDzEO39\nqqqozhDcxLbFjQoElab49a8Jnbb5Oxl9ck6bCFYoq8opufpRYmVQlm8auQ7b\nJfMtvx0XT3Vg0ZeDQZNnt6tg+Xd1L5M/I1cL5q+99fjZ6gyEOleG5NjyeE6D\nKWKLklXLt2F08Pmbh1C9Ex8I/ywFZs39rfqJ7YAl/qznFUAum4/Iml+4LHcl\nugZ0MFpalpLAtpWK17elwYqnDn7SCiNbAp8LrPNdnwIx+IlrFdJZWrDjC9FZ\nSUP5EjM/L0ouyALTgm7QrfMgr1GqN9Eia6P3hX/KwTv55RWK6Xe3asLFYsIn\n7MVczLDtgSi46TQVZx6m0eoUSldU6oj4dR4NT93FbMt6RPKUEClkCeFDa8so\n5Kd55FfXLAouQuXF5QPnc2vSYKQDo79kWUL/VMyqYXTZe5I7X13dKdgDiLQ0\nDkYSLGpio+vqz8Pe/yqT8+c/gHI7UfR+nh8ktGU7fvVWBhine9YZWvb/9BSV\nziVlF/TglLo4ACDiip6L/tw0wQ4w9RnUKI8cDnW6TYh4u4Zsz3TI/uPEPe3l\neYhD\r\n=uHnP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=6"}, "gitHead": "4cd34dcd2456c418bdb4373c7e4d7ef3ac5a284e", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "standard": {"ignore": ["fixtures/has-inline-source-map.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"find-up": "^3.0.0", "test-exclude": "^5.2.3", "istanbul-lib-instrument": "^3.3.0", "@babel/helper-plugin-utils": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.0", "chai": "^4.2.0", "mocha": "^6.1.4", "pmock": "^0.2.3", "standard": "^12.0.1", "coveralls": "^3.0.3", "cross-env": "^5.2.0", "@babel/cli": "^7.4.3", "@babel/core": "^7.4.3", "@babel/register": "^7.4.0", "standard-version": "^6.0.1", "@babel/plugin-transform-modules-commonjs": "^7.4.3"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul_5.2.0_1563425290686_0.27774697933752135", "host": "s3://npm-registry-packages"}}, "6.0.0-beta.0": {"name": "babel-plugin-istanbul", "version": "6.0.0-beta.0", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@6.0.0-beta.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["@babel/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "503308b1edaa324b3e0c0d6638ba94cf6a705c42", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-HwHIN30J7P94fqW4fzTh2CBEBncRmn9SD7J/+Ta2syHKF39TmrV280eOqmGxXrrjllBr0BHCJ6pQOFDnkFY1Hw==", "signatures": [{"sig": "MEQCIGk0OWNqBEpHLpL9t8154pYcqBFCXEjplJl1sPjYM8aRAiAQuUD/3NSuQ1PvnV39jl+8po5ChdRxOD1zO7Xxc5/MNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24777, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnUNdCRA9TVsSAnZWagAAPVcP/2ZlNFTHo6qkDbvW3rWa\n+Lg5oxR03jqLYqYgKPg1vBmm9vVOldDQvWCq7110z8Vr2yrydR8kjPv0H1T8\nivk5z6uPkh+RYip2X1MRq01aEbAWjctMyiQnQpb6eUNGc84+WassZp7w3PaQ\nSJytDpD8BA56owBE4F2NSasNHkz0R6/slHnS+YeXemJ78W7GtSVhp3TiqCUz\nd4weFZKTriFs1nihHQRMc56du1ZirCec41nI/oMsVecvTemaVOaqf7gEB3lv\nVO4vUn1WOF2j7uX5YgqtVd4GuPqVjdgugMg7OmVd3EOPtRZT9Ac3H+vhg2Y0\nkd2bcHGbQkqTodhLSEL/KOmguXY2r8LV9P/7AHDUHRb7158NNeAkvnlbHvEb\nYxvuz/4973k73OHDvuKhpDTazpaCVV50oFWbVuAcMVS8h/V7ujcmq2aXa/Xi\ng84SsekV4zaXGfcpXf9PfK1CRHVY4k9NixquKj9GhdK8p5Bh54tMUQpdM8Vg\nUfI01qADZhL0LqM9pTTwUQTxV1syc2djIE232PyUJHDRhl9i0MFuSq99ZIly\n7l0fCeTYH6sLhLL4paz01jx5Xs/wuHOPeYAV7LCevmBxbWCyl5OPA8WG3G1Y\nu+S92wSagPZa00em4dVbDYtStv9GHEUYeRrwLYIn4Klo54qTczyAzUOWo473\nN1ub\r\n=TNFq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "readme": "# babel-plugin-istanbul\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/babel-plugin-istanbul.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/babel-plugin-istanbul.svg?branch=master)](https://travis-ci.org/istanbuljs/babel-plugin-istanbul)\n[![Coverage Status](https://coveralls.io/repos/github/istanbuljs/babel-plugin-istanbul/badge.svg?branch=master)](https://coveralls.io/github/istanbuljs/babel-plugin-istanbul?branch=master)\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n[![community slack](http://devtoolscommunity.herokuapp.com/badge.svg)](http://devtoolscommunity.herokuapp.com)\n\n_Having problems? want to contribute? join our [community slack](http://devtoolscommunity.herokuapp.com)_.\n\nA Babel plugin that instruments your code with Istanbul coverage.\nIt can instantly be used with [karma-coverage](https://github.com/karma-runner/karma-coverage) and mocha on Node.js (through [nyc](https://github.com/bcoe/nyc)).\n\n__Note:__ This plugin does not generate any report or save any data to any file;\nit only adds instrumenting code to your JavaScript source code.\nTo integrate with testing tools, please see the [Integrations](#integrations) section.\n\n## Usage\n\nInstall it:\n\n```\nnpm install --save-dev babel-plugin-istanbul\n```\n\nAdd it to `.babelrc` in test mode:\n\n```js\n{\n  \"env\": {\n    \"test\": {\n      \"plugins\": [ \"istanbul\" ]\n    }\n  }\n}\n```\n\nOptionally, use [cross-env](https://www.npmjs.com/package/cross-env) to set\n`NODE_ENV=test`:\n\n```json\n{\n  \"scripts\": {\n    \"test\": \"cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js\"\n  }\n}\n```\n\n## Integrations\n\n### karma\n\nIt _just works_ with Karma. First, make sure that the code is already transpiled by Babel (either using `karma-babel-preprocessor`, `karma-webpack`, or `karma-browserify`). Then, simply set up [karma-coverage](https://github.com/karma-runner/karma-coverage) according to the docs, but __don’t add the `coverage` preprocessor.__ This plugin has already instrumented your code, and Karma should pick it up automatically.\n\nIt has been tested with [bemusic/bemuse](https://codecov.io/github/bemusic/bemuse) project, which contains ~2400 statements.\n\n### mocha on node.js (through nyc)\n\nConfigure Mocha to transpile JavaScript code using Babel, then you can run your tests with [`nyc`](https://github.com/bcoe/nyc), which will collect all the coverage report.\n\nbabel-plugin-istanbul respects the `include`/`exclude` configuration options from nyc,\nbut you also need to __configure NYC not to instrument your code__ by adding these settings in your `package.json`:\n\n```js\n  \"nyc\": {\n    \"sourceMap\": false,\n    \"instrument\": false\n  },\n```\n\n## Ignoring files\n\nYou don't want to cover your test files as this will skew your coverage results. You can configure this by providing plugin options matching nyc's [`exclude`/`include` rules](https://github.com/bcoe/nyc#excluding-files):\n\n```json\n{\n  \"env\": {\n    \"test\": {\n      \"plugins\": [\n        [\"istanbul\", {\n          \"exclude\": [\n            \"**/*.spec.js\"\n          ]\n        }]\n      ]\n    }\n  }\n}\n```\n\nIf you don't provide options in your Babel config, the plugin will look for `exclude`/`include` config under an `\"nyc\"` key in `package.json`.\n\nYou can also use [istanbul's ignore hints](https://github.com/gotwarlost/istanbul/blob/master/ignoring-code-for-coverage.md#ignoring-code-for-coverage-purposes) to specify specific lines of code to skip instrumenting.\n\n## Source Maps\n\nBy default, this plugin will pick up inline source maps and attach them to the instrumented code such that code coverage can be remapped back to the original source, even for multi-step build processes. This can be memory intensive. Set `useInlineSourceMaps` to prevent this behavior.\n\n```json\n{\n  \"env\": {\n    \"test\": {\n      \"plugins\": [\n        [\"istanbul\", {\n          \"useInlineSourceMaps\": false\n        }]\n      ]\n    }\n  }\n}\n```\n\nIf you're instrumenting code programatically, you can pass a source map explicitly.\n```js\nimport babelPluginIstanbul from 'babel-plugin-istanbul';\n\nfunction instrument(sourceCode, sourceMap, fileName) {\n  return babel.transform(sourceCode, {\n    filename,\n    plugins: [\n      [babelPluginIstanbul, {\n        inputSourceMap: sourceMap\n      }]\n    ]\n  })\n}\n```\n\n## Credit where credit is due\n\nThe approach used in `babel-plugin-istanbul` was inspired by [Thai Pangsakulyanont](https://github.com/dtinth)'s original library [`babel-plugin-__coverage__`](https://github.com/dtinth/babel-plugin-__coverage__).\n", "engines": {"node": ">=8"}, "gitHead": "a5ffae78ac593fef3c48421dbaa5edeca6cdcff2", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha --timeout 5000 test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "standard": {"ignore": ["fixtures/*.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "_nodeVersion": "12.11.0", "dependencies": {"test-exclude": "^6.0.0-alpha.1", "@istanbuljs/schema": "^0.1.1", "istanbul-lib-instrument": "^4.0.0-alpha.1", "@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0-alpha.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^15.0.0-alpha.0", "chai": "^4.2.0", "mocha": "^6.2.1", "pmock": "^0.2.3", "standard": "^14.3.1", "coveralls": "^3.0.6", "cross-env": "^6.0.3", "@babel/cli": "^7.6.2", "@babel/core": "^7.6.2", "@babel/register": "^7.6.2", "standard-version": "^7.0.0", "@babel/plugin-transform-modules-commonjs": "^7.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul_6.0.0-beta.0_1570587484582_0.22423224109219175", "host": "s3://npm-registry-packages"}}, "6.0.0-beta.1": {"name": "babel-plugin-istanbul", "version": "6.0.0-beta.1", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@6.0.0-beta.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["@babel/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "5733ca2c41697b3921a5613609d6e7937f1c2118", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.0.0-beta.1.tgz", "fileCount": 6, "integrity": "sha512-mLkelL89KSs7RQWTK/u68wh+FsGH6xdHw4nBZLJbnVZVemHE/C1OT/+6DoVL+zlauC+Im1A75ChruoZjReFv/g==", "signatures": [{"sig": "MEUCIHnY1N5PoZdXSiUelQukQVbtFeFoqQOfnCjssIjfqVIoAiEAt4wcnJ+Ydcat52GvjMCuq15vsoyZ6FEaMDs5NltFKVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24777, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6vzdCRA9TVsSAnZWagAAMSkP/1W1KD9N/Et9KsjkI0Oo\nN+F9T67ilzuEP1c5WmawHLu9w7CIfZO4E+7/7cQaK7R6RZtYXZkk5T+i9APw\n2dG1aTxLJELofN5YAUzAOow46a28tByqsickEAHpDr5Mty0hbxgoYm3ostp5\nHokzVY1YjSVIdkp8cLtYrg6qqCgkfky9eckj2cy/6DfB6s8hNLxHX+8YTPGo\nRR1bPjtIpVZMxbb5ahIzASUMyYD4vUaMp8Wdqxf1YHpEFGi8uHidTdeHg026\noI73JK2yvHV/Jpo30e1LYQ6YDzRMw31qYuh1GCaB9XU6pSZZKEgU78JGFUQR\nuMEM525kaBZPJnX1CjrSoeouOV2cEP9wBGdGo1dk7Iou0lUUrMl5U7zSeAvr\ngNA5c+IToGxVXCnOzw21V/2vDSMi4OWWDEpQFZ8I/zmO3TbePYdeCXnXrP9P\nldSRnN6iFKtZ4KBGsBkw/+2fzFKaK/ciUHB4yP4WN8EV1x6rAfEUpuej5Yf6\n0SYHUl3jBiiSD8oT6BC0Oxt3/5UXuNTI6BdlEJaNRglMoIb2m52LjPnv5TF6\njWmHPgeJ/PgJ7grQbu1Y8I0E1bJdci6EqgFSlpgL/go0+Y6yVGEAg7fYWmmQ\n9pBGETKO54mlQnEEsxDCoQHwmoqvplnPuArFbhtm8XiIrWusknvj07p2HfKL\n8g/6\r\n=nv+K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "readme": "# babel-plugin-istanbul\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/babel-plugin-istanbul.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/babel-plugin-istanbul.svg?branch=master)](https://travis-ci.org/istanbuljs/babel-plugin-istanbul)\n[![Coverage Status](https://coveralls.io/repos/github/istanbuljs/babel-plugin-istanbul/badge.svg?branch=master)](https://coveralls.io/github/istanbuljs/babel-plugin-istanbul?branch=master)\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n[![community slack](http://devtoolscommunity.herokuapp.com/badge.svg)](http://devtoolscommunity.herokuapp.com)\n\n_Having problems? want to contribute? join our [community slack](http://devtoolscommunity.herokuapp.com)_.\n\nA Babel plugin that instruments your code with Istanbul coverage.\nIt can instantly be used with [karma-coverage](https://github.com/karma-runner/karma-coverage) and mocha on Node.js (through [nyc](https://github.com/bcoe/nyc)).\n\n__Note:__ This plugin does not generate any report or save any data to any file;\nit only adds instrumenting code to your JavaScript source code.\nTo integrate with testing tools, please see the [Integrations](#integrations) section.\n\n## Usage\n\nInstall it:\n\n```\nnpm install --save-dev babel-plugin-istanbul\n```\n\nAdd it to `.babelrc` in test mode:\n\n```js\n{\n  \"env\": {\n    \"test\": {\n      \"plugins\": [ \"istanbul\" ]\n    }\n  }\n}\n```\n\nOptionally, use [cross-env](https://www.npmjs.com/package/cross-env) to set\n`NODE_ENV=test`:\n\n```json\n{\n  \"scripts\": {\n    \"test\": \"cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js\"\n  }\n}\n```\n\n## Integrations\n\n### karma\n\nIt _just works_ with Karma. First, make sure that the code is already transpiled by Babel (either using `karma-babel-preprocessor`, `karma-webpack`, or `karma-browserify`). Then, simply set up [karma-coverage](https://github.com/karma-runner/karma-coverage) according to the docs, but __don’t add the `coverage` preprocessor.__ This plugin has already instrumented your code, and Karma should pick it up automatically.\n\nIt has been tested with [bemusic/bemuse](https://codecov.io/github/bemusic/bemuse) project, which contains ~2400 statements.\n\n### mocha on node.js (through nyc)\n\nConfigure Mocha to transpile JavaScript code using Babel, then you can run your tests with [`nyc`](https://github.com/bcoe/nyc), which will collect all the coverage report.\n\nbabel-plugin-istanbul respects the `include`/`exclude` configuration options from nyc,\nbut you also need to __configure NYC not to instrument your code__ by adding these settings in your `package.json`:\n\n```js\n  \"nyc\": {\n    \"sourceMap\": false,\n    \"instrument\": false\n  },\n```\n\n## Ignoring files\n\nYou don't want to cover your test files as this will skew your coverage results. You can configure this by providing plugin options matching nyc's [`exclude`/`include` rules](https://github.com/bcoe/nyc#excluding-files):\n\n```json\n{\n  \"env\": {\n    \"test\": {\n      \"plugins\": [\n        [\"istanbul\", {\n          \"exclude\": [\n            \"**/*.spec.js\"\n          ]\n        }]\n      ]\n    }\n  }\n}\n```\n\nIf you don't provide options in your Babel config, the plugin will look for `exclude`/`include` config under an `\"nyc\"` key in `package.json`.\n\nYou can also use [istanbul's ignore hints](https://github.com/gotwarlost/istanbul/blob/master/ignoring-code-for-coverage.md#ignoring-code-for-coverage-purposes) to specify specific lines of code to skip instrumenting.\n\n## Source Maps\n\nBy default, this plugin will pick up inline source maps and attach them to the instrumented code such that code coverage can be remapped back to the original source, even for multi-step build processes. This can be memory intensive. Set `useInlineSourceMaps` to prevent this behavior.\n\n```json\n{\n  \"env\": {\n    \"test\": {\n      \"plugins\": [\n        [\"istanbul\", {\n          \"useInlineSourceMaps\": false\n        }]\n      ]\n    }\n  }\n}\n```\n\nIf you're instrumenting code programatically, you can pass a source map explicitly.\n```js\nimport babelPluginIstanbul from 'babel-plugin-istanbul';\n\nfunction instrument(sourceCode, sourceMap, fileName) {\n  return babel.transform(sourceCode, {\n    filename,\n    plugins: [\n      [babelPluginIstanbul, {\n        inputSourceMap: sourceMap\n      }]\n    ]\n  })\n}\n```\n\n## Credit where credit is due\n\nThe approach used in `babel-plugin-istanbul` was inspired by [Thai Pangsakulyanont](https://github.com/dtinth)'s original library [`babel-plugin-__coverage__`](https://github.com/dtinth/babel-plugin-__coverage__).\n", "engines": {"node": ">=8"}, "gitHead": "77b34334eaaf2fe8f2afcb9eb956106f88059cf0", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha --timeout 5000 test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "standard": {"ignore": ["fixtures/*.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "6.13.2", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "_nodeVersion": "13.3.0", "dependencies": {"test-exclude": "=6.0.0-alpha.1", "@istanbuljs/schema": "^0.1.1", "istanbul-lib-instrument": "^4.0.0-alpha.1", "@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0-alpha.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^15.0.0-alpha.0", "chai": "^4.2.0", "mocha": "^6.2.1", "pmock": "^0.2.3", "standard": "^14.3.1", "coveralls": "^3.0.6", "cross-env": "^6.0.3", "@babel/cli": "^7.6.2", "@babel/core": "^7.6.2", "@babel/register": "^7.6.2", "standard-version": "^7.0.0", "@babel/plugin-transform-modules-commonjs": "^7.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul_6.0.0-beta.1_1575681245284_0.21682094735516255", "host": "s3://npm-registry-packages"}}, "6.0.0-beta.2": {"name": "babel-plugin-istanbul", "version": "6.0.0-beta.2", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@6.0.0-beta.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["@babel/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "278ac38d28ae0d6a31b8b572779321dca233cbb5", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.0.0-beta.2.tgz", "fileCount": 6, "integrity": "sha512-x2/8KqERJAheZuY1ob1D9xYRa2DoBs7cCM89jBXRYHlX3C3ZC7k9fi6YA89ayxPHGn/D+aD93c/RbiFh1lgKPg==", "signatures": [{"sig": "MEUCIQCKKTkjleWrCKM+F78q54IRoxCqMIfo2bBgwNoJjHf45gIgUaayo52Gc0XCEA+gPQxD1hje0bCF8qSmjHFAKzB72i4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd7aCfCRA9TVsSAnZWagAAkNwP/34xuMKVaJ5E2Xh1dAD8\n24DSn3Zv2OWTEe6SRTq/dsM/GDAS3aet/T1yhWaojcTPdHprOL9kHTGdQ8aT\n5GVcK2rkG8tm6gyozlD31GBtVCtKkt6RA82G3MZyXUhKGMCT7Hn09P1WwmBS\n5Jx7itwSkeMtIrGjkm+tbBCbRhrQwJcpweL/HrkApaMMJKoVkWCWDhbF80Gw\nKUk6EK//YQ0doe5KYOwzoB8q4o+kvFd+FK46fgyuLKt+E5Hr1FTS3fcYn3nj\nV+CBQcZOOBsZOCE1tjBjSoo0o8gM1O8oWyQMBY/dmD25zk/fGK/2HjjABWEG\nDUwaZcVRWP1LvE7+yh/5CsnOre0VdTVktwQUMqcBW+lvTWZjQtHBNal7Pvri\n/HxGS0mBi4kyOvUmsfO1vvTLoLex5qKVyk4zoMVk0V74Um/J64o3bWtBe0yp\nZdr/XfH/Y+NLBI9I83MqqzynSSo9RPnF8EwxAIe6JZl9VHv/C+59cQmHMSfu\nR/iD0D0/g9B4/BDiWxRHy8wMMFntnPjZ/QQ3R6WZDrgBB8mX5jO6S8ZMAtu2\nKq4X8T8o/P2kfp0Lktnvs9g5XwWnxPDcvrxRGqQlp+tCSqf1AfzwROj0/ThP\nnudNM4vqX4wXkqXjCirojvXN5I3Rt2AwxnlDlZ8kVKvyyfyZMt9BHmDfZXMA\nvNRQ\r\n=d6AB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "readme": "# babel-plugin-istanbul\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/babel-plugin-istanbul.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/babel-plugin-istanbul.svg?branch=master)](https://travis-ci.org/istanbuljs/babel-plugin-istanbul)\n[![Coverage Status](https://coveralls.io/repos/github/istanbuljs/babel-plugin-istanbul/badge.svg?branch=master)](https://coveralls.io/github/istanbuljs/babel-plugin-istanbul?branch=master)\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n[![community slack](http://devtoolscommunity.herokuapp.com/badge.svg)](http://devtoolscommunity.herokuapp.com)\n\n_Having problems? want to contribute? join our [community slack](http://devtoolscommunity.herokuapp.com)_.\n\nA Babel plugin that instruments your code with Istanbul coverage.\nIt can instantly be used with [karma-coverage](https://github.com/karma-runner/karma-coverage) and mocha on Node.js (through [nyc](https://github.com/bcoe/nyc)).\n\n__Note:__ This plugin does not generate any report or save any data to any file;\nit only adds instrumenting code to your JavaScript source code.\nTo integrate with testing tools, please see the [Integrations](#integrations) section.\n\n## Usage\n\nInstall it:\n\n```\nnpm install --save-dev babel-plugin-istanbul\n```\n\nAdd it to `.babelrc` in test mode:\n\n```js\n{\n  \"env\": {\n    \"test\": {\n      \"plugins\": [ \"istanbul\" ]\n    }\n  }\n}\n```\n\nOptionally, use [cross-env](https://www.npmjs.com/package/cross-env) to set\n`NODE_ENV=test`:\n\n```json\n{\n  \"scripts\": {\n    \"test\": \"cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js\"\n  }\n}\n```\n\n## Integrations\n\n### karma\n\nIt _just works_ with Karma. First, make sure that the code is already transpiled by Babel (either using `karma-babel-preprocessor`, `karma-webpack`, or `karma-browserify`). Then, simply set up [karma-coverage](https://github.com/karma-runner/karma-coverage) according to the docs, but __don’t add the `coverage` preprocessor.__ This plugin has already instrumented your code, and Karma should pick it up automatically.\n\nIt has been tested with [bemusic/bemuse](https://codecov.io/github/bemusic/bemuse) project, which contains ~2400 statements.\n\n### mocha on node.js (through nyc)\n\nConfigure Mocha to transpile JavaScript code using Babel, then you can run your tests with [`nyc`](https://github.com/bcoe/nyc), which will collect all the coverage report.\n\nbabel-plugin-istanbul respects the `include`/`exclude` configuration options from nyc,\nbut you also need to __configure NYC not to instrument your code__ by adding these settings in your `package.json`:\n\n```js\n  \"nyc\": {\n    \"sourceMap\": false,\n    \"instrument\": false\n  },\n```\n\n## Ignoring files\n\nYou don't want to cover your test files as this will skew your coverage results. You can configure this by providing plugin options matching nyc's [`exclude`/`include` rules](https://github.com/bcoe/nyc#excluding-files):\n\n```json\n{\n  \"env\": {\n    \"test\": {\n      \"plugins\": [\n        [\"istanbul\", {\n          \"exclude\": [\n            \"**/*.spec.js\"\n          ]\n        }]\n      ]\n    }\n  }\n}\n```\n\nIf you don't provide options in your Babel config, the plugin will look for `exclude`/`include` config under an `\"nyc\"` key in `package.json`.\n\nYou can also use [istanbul's ignore hints](https://github.com/gotwarlost/istanbul/blob/master/ignoring-code-for-coverage.md#ignoring-code-for-coverage-purposes) to specify specific lines of code to skip instrumenting.\n\n## Source Maps\n\nBy default, this plugin will pick up inline source maps and attach them to the instrumented code such that code coverage can be remapped back to the original source, even for multi-step build processes. This can be memory intensive. Set `useInlineSourceMaps` to prevent this behavior.\n\n```json\n{\n  \"env\": {\n    \"test\": {\n      \"plugins\": [\n        [\"istanbul\", {\n          \"useInlineSourceMaps\": false\n        }]\n      ]\n    }\n  }\n}\n```\n\nIf you're instrumenting code programatically, you can pass a source map explicitly.\n```js\nimport babelPluginIstanbul from 'babel-plugin-istanbul';\n\nfunction instrument(sourceCode, sourceMap, fileName) {\n  return babel.transform(sourceCode, {\n    filename,\n    plugins: [\n      [babelPluginIstanbul, {\n        inputSourceMap: sourceMap\n      }]\n    ]\n  })\n}\n```\n\n## Credit where credit is due\n\nThe approach used in `babel-plugin-istanbul` was inspired by [Thai Pangsakulyanont](https://github.com/dtinth)'s original library [`babel-plugin-__coverage__`](https://github.com/dtinth/babel-plugin-__coverage__).\n\n## `babel-plugin-istanbul` for enterprise\n\nAvailable as part of the Tidelift Subscription.\n\nThe maintainers of `babel-plugin-istanbul` and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-babel-plugin-istanbul?utm_source=npm-babel-plugin-istanbul&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n", "engines": {"node": ">=8"}, "gitHead": "01aa17a5a9bf6d59b9a27b994ed1b8a6c6201dde", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha --timeout 5000 test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "standard": {"ignore": ["fixtures/*.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "6.13.2", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "_nodeVersion": "13.3.0", "dependencies": {"test-exclude": "^6.0.0-alpha.3", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-instrument": "^4.0.0-alpha.3", "@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0-alpha.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^15.0.0-beta.3", "chai": "^4.2.0", "mocha": "^6.2.2", "pmock": "^0.2.3", "standard": "^14.3.1", "coveralls": "^3.0.9", "cross-env": "^6.0.3", "@babel/cli": "^7.7.5", "@babel/core": "^7.7.5", "@babel/register": "^7.7.4", "standard-version": "^7.1.0", "@babel/plugin-transform-modules-commonjs": "^7.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul_6.0.0-beta.2_1575854239304_0.10910339333007579", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "babel-plugin-istanbul", "version": "6.0.0", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@6.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["@babel/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "e159ccdc9af95e0b570c75b4573b7c34d671d765", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.0.0.tgz", "fileCount": 6, "integrity": "sha512-A<PERSON>55rZXpe7trmEylbaE1Gv54wn6rwU03aptvRoVIGP8YykoSxqdVLV1TfwflBCE/QtHmqtP8SWlTENqbK8GCSQ==", "signatures": [{"sig": "MEUCIQCNPPNVGgbAyqn4+Zgymw5UIKl0T8gh5TkRKAVRh71hegIgUxoy1++np7yf5MvIWXtAAMRmGjYzaJ8KTvLkfkPWBsc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd/UZXCRA9TVsSAnZWagAArhYP/jFC5j7srCQ5Iyzm6ysC\nRO2GHZmo6cLmKaHlIAd0RuDRgXHjdJy80RKmIP2VGw/EafApehOFB/QNRIvC\nS/Z3pZjcqoXUubB9VBoIsoMYQ5sjwcViBVuAJcs+XU0hnwbm2sYo9mzIYQKk\nmcUe4m+PxX9Jp/hnz/axRDc1cXFULl/YYFSUBdlb9C49t7hUtXHMMr4W92ou\nhe6+AJvuvCI8Ukitw/LS/HlQoHpniObNBdmxRG6n99Pe3p+meGCRl/y0pj39\ncNfVKTeDMb1bE049dTmIGXI64Xb2vxy/DXrLkjPzsVcYg9WdAdizo08nrwEq\n/DeWgaujRLHFb1wkmZe0MkHIfmbqEw85Nyk9BJGS9V3XwGRx6bWTYsQCbuhK\nmHV0p7jC31JjPHrq0RHnpesEEFdwiyJmb3icI74kda5+1xRcIapYZ8pPNMPx\ngCfzmpJhLZoXtCkopC8hqqYi1FRhsa9P9jTDomjMvQkxmRmLh0hdMRsfx918\nm+xCGPTKUjbcucr6JagahF9Mt/pR4h5cmpX0+5aIdk41HHS//9apliFVr7WY\nB1ouhv81NSxZHwkO74NeMdBCI6nv38qhcR/Pm6Eyxlgm+QVg2Mi11Dtv8NR/\nLpLd1qw9g7jw08ei/xvKJeKr9mxrtUiAS9mLbss1Mxo/BYvZZariqQtH60a8\n/5fM\r\n=17jV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "readme": "# babel-plugin-istanbul\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/babel-plugin-istanbul.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/babel-plugin-istanbul.svg?branch=master)](https://travis-ci.org/istanbuljs/babel-plugin-istanbul)\n[![Coverage Status](https://coveralls.io/repos/github/istanbuljs/babel-plugin-istanbul/badge.svg?branch=master)](https://coveralls.io/github/istanbuljs/babel-plugin-istanbul?branch=master)\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n[![community slack](http://devtoolscommunity.herokuapp.com/badge.svg)](http://devtoolscommunity.herokuapp.com)\n\n_Having problems? want to contribute? join our [community slack](http://devtoolscommunity.herokuapp.com)_.\n\nA Babel plugin that instruments your code with Istanbul coverage.\nIt can instantly be used with [karma-coverage](https://github.com/karma-runner/karma-coverage) and mocha on Node.js (through [nyc](https://github.com/bcoe/nyc)).\n\n__Note:__ This plugin does not generate any report or save any data to any file;\nit only adds instrumenting code to your JavaScript source code.\nTo integrate with testing tools, please see the [Integrations](#integrations) section.\n\n## Usage\n\nInstall it:\n\n```\nnpm install --save-dev babel-plugin-istanbul\n```\n\nAdd it to `.babelrc` in test mode:\n\n```js\n{\n  \"env\": {\n    \"test\": {\n      \"plugins\": [ \"istanbul\" ]\n    }\n  }\n}\n```\n\nOptionally, use [cross-env](https://www.npmjs.com/package/cross-env) to set\n`NODE_ENV=test`:\n\n```json\n{\n  \"scripts\": {\n    \"test\": \"cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js\"\n  }\n}\n```\n\n## Integrations\n\n### karma\n\nIt _just works_ with Karma. First, make sure that the code is already transpiled by Babel (either using `karma-babel-preprocessor`, `karma-webpack`, or `karma-browserify`). Then, simply set up [karma-coverage](https://github.com/karma-runner/karma-coverage) according to the docs, but __don’t add the `coverage` preprocessor.__ This plugin has already instrumented your code, and Karma should pick it up automatically.\n\nIt has been tested with [bemusic/bemuse](https://codecov.io/github/bemusic/bemuse) project, which contains ~2400 statements.\n\n### mocha on node.js (through nyc)\n\nConfigure Mocha to transpile JavaScript code using Babel, then you can run your tests with [`nyc`](https://github.com/bcoe/nyc), which will collect all the coverage report.\n\nbabel-plugin-istanbul respects the `include`/`exclude` configuration options from nyc,\nbut you also need to __configure NYC not to instrument your code__ by adding these settings in your `package.json`:\n\n```js\n  \"nyc\": {\n    \"sourceMap\": false,\n    \"instrument\": false\n  },\n```\n\n## Ignoring files\n\nYou don't want to cover your test files as this will skew your coverage results. You can configure this by providing plugin options matching nyc's [`exclude`/`include` rules](https://github.com/bcoe/nyc#excluding-files):\n\n```json\n{\n  \"env\": {\n    \"test\": {\n      \"plugins\": [\n        [\"istanbul\", {\n          \"exclude\": [\n            \"**/*.spec.js\"\n          ]\n        }]\n      ]\n    }\n  }\n}\n```\n\nIf you don't provide options in your Babel config, the plugin will look for `exclude`/`include` config under an `\"nyc\"` key in `package.json`.\n\nYou can also use [istanbul's ignore hints](https://github.com/gotwarlost/istanbul/blob/master/ignoring-code-for-coverage.md#ignoring-code-for-coverage-purposes) to specify specific lines of code to skip instrumenting.\n\n## Source Maps\n\nBy default, this plugin will pick up inline source maps and attach them to the instrumented code such that code coverage can be remapped back to the original source, even for multi-step build processes. This can be memory intensive. Set `useInlineSourceMaps` to prevent this behavior.\n\n```json\n{\n  \"env\": {\n    \"test\": {\n      \"plugins\": [\n        [\"istanbul\", {\n          \"useInlineSourceMaps\": false\n        }]\n      ]\n    }\n  }\n}\n```\n\nIf you're instrumenting code programatically, you can pass a source map explicitly.\n```js\nimport babelPluginIstanbul from 'babel-plugin-istanbul';\n\nfunction instrument(sourceCode, sourceMap, fileName) {\n  return babel.transform(sourceCode, {\n    filename,\n    plugins: [\n      [babelPluginIstanbul, {\n        inputSourceMap: sourceMap\n      }]\n    ]\n  })\n}\n```\n\n## Credit where credit is due\n\nThe approach used in `babel-plugin-istanbul` was inspired by [Thai Pangsakulyanont](https://github.com/dtinth)'s original library [`babel-plugin-__coverage__`](https://github.com/dtinth/babel-plugin-__coverage__).\n\n## `babel-plugin-istanbul` for enterprise\n\nAvailable as part of the Tidelift Subscription.\n\nThe maintainers of `babel-plugin-istanbul` and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-babel-plugin-istanbul?utm_source=npm-babel-plugin-istanbul&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n", "engines": {"node": ">=8"}, "gitHead": "2fb91f54c8271936f016fb521b080213bbdb77f7", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha --timeout 5000 test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "standard": {"ignore": ["fixtures/*.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "_nodeVersion": "13.4.0", "dependencies": {"test-exclude": "^6.0.0", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-instrument": "^4.0.0", "@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^15.0.0", "chai": "^4.2.0", "mocha": "^6.2.2", "pmock": "^0.2.3", "standard": "^14.3.1", "coveralls": "^3.0.9", "cross-env": "^6.0.3", "@babel/cli": "^7.7.5", "@babel/core": "^7.7.5", "@babel/register": "^7.7.4", "standard-version": "^7.1.0", "@babel/plugin-transform-modules-commonjs": "^7.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul_6.0.0_1576879703563_0.003695922904944471", "host": "s3://npm-registry-packages"}}, "6.1.1": {"name": "babel-plugin-istanbul", "version": "6.1.1", "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-istanbul@6.1.1", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["@babel/register"], "sourceMap": false, "instrument": false}, "dist": {"shasum": "fa88ec59232fd9b4e36dbbc540a8ec9a9b47da73", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz", "fileCount": 6, "integrity": "sha512-Y1IQok9821cC9onCx5otgFfRm7Lm+I+wwxOx738M/WLPZ9Q42m4IG5W0FNX8WLL2gYMZo3JkuXIH2DOpWM+qwA==", "signatures": [{"sig": "MEYCIQCPvdrVhE6ynjHvLYTq6zGnU8LbwNojxw/XemXf9X5IhgIhANPUDIN51Bbc2GNG8/1w58ToHBdnsy5zoEIQzN9e62j9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2kQsCRA9TVsSAnZWagAAiikP/jNZTZR6B2GAER9GOwDQ\nbEhnZH9HZMSj/pyOudmw296POa+d3CjTkz05ibhebREetbPlQpTnZZFY1LDm\npV7g+bxLKLAc8Q7KevmvOOc6OP0Lmthe7Rn0ajtF2FdOIqiwIKYyQND1RTog\n02tYjR68ENjIYqgkMzXy7Evi/Rfvnq6MEGIPpYV+KgdlClI0sk2CmLyHfPfP\nXMw7RLicxdlkDXuOxjpkfukGkZWeC2RGESfMiH0tHFRYPqvU4vzNF1/51H/A\nR/Wq8yKGjMSrUTFQi/0foHypZYw79pAQxHapoWhly5zY6Ouqrtl5dHEbTiJj\n+84gVR5ftPVXnVLwQNfZ7MuPU5tGxsG1Pxz3p7kn6xLQRYtLJ4cFSQ9tqf+j\nyyenUqezJAdA225FSHUK1NRgvZC3KuUrjVsFif0kE/20ox5ghfolM5N0U/dN\n5DQhEfqqdx541sulwMOt+JmIGOaiS4DBiHtn30adWFHLIv5ZGnAIE0NfDkW4\ndE0pA/SfKrA54dPOxO0dTHSMmmxFzFc1PPiVQR+8sobH4E1YRVmv5VMGx4Vh\nUb5qc1YzOTCR56C6tk0H2JbCPBQ0dP8l2ltiBMEFg9pAtKveClmxtVY8xwAg\nhJN5asH5yId/ALwOJ0QrTI/O0OkC3MqdR2caR8bGxUP3LnyOt5Rc/QZEzLN8\nx9UC\r\n=cXqw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": ">=8"}, "gitHead": "d58c92a7de5e8ac84c598e02325b05f5b9800107", "scripts": {"test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha --timeout 5000 test/*.js", "pretest": "standard && npm run release", "release": "babel src --out-dir lib", "coverage": "nyc report --reporter=text-lcov | coveralls", "prepublish": "npm test && npm run release"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standard": {"ignore": ["fixtures/*.js"]}, "repository": {"url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "directories": {}, "_nodeVersion": "14.18.0", "dependencies": {"test-exclude": "^6.0.0", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-instrument": "^5.0.4", "@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0", "chai": "^4.2.0", "mocha": "^6.2.2", "pmock": "^0.2.3", "standard": "^14.3.1", "coveralls": "^3.0.9", "cross-env": "^6.0.3", "@babel/cli": "^7.7.5", "@babel/core": "^7.7.5", "@babel/register": "^7.7.4", "@babel/plugin-transform-modules-commonjs": "^7.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-istanbul_6.1.1_1634417766470_0.500793105562086", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "babel-plugin-istanbul", "version": "7.0.0", "author": {"name": "Thai Pangsakulyanont @dtinth"}, "license": "BSD-3-<PERSON><PERSON>", "description": "A babel plugin that adds istanbul instrumentation to ES6 code", "main": "lib/index.js", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0", "@istanbuljs/schema": "^0.1.3", "istanbul-lib-instrument": "^6.0.2", "test-exclude": "^6.0.0"}, "devDependencies": {"@babel/cli": "^7.24.1", "@babel/core": "^7.24.1", "@babel/plugin-transform-block-scoping": "^7.24.1", "@babel/plugin-transform-modules-commonjs": "^7.24.1", "@babel/register": "^7.23.7", "chai": "^4.2.0", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "mocha": "^6.2.2", "nyc": "^15.0.0", "pmock": "^0.2.3", "standard": "^14.3.1"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "release": "babel src --out-dir lib", "pretest": "standard && npm run release", "test": "cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha --timeout 5000 test/*.js", "prepublish": "npm test && npm run release"}, "standard": {"ignore": ["fixtures/*.js"]}, "repository": {"type": "git", "url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git"}, "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "nyc": {"include": ["src/*.js", "fixtures/should-cover.js"], "require": ["@babel/register"], "sourceMap": false, "instrument": false}, "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "engines": {"node": ">=12"}, "gitHead": "37bb294cf433ca327572a2c3e1e4e6c32484c16e", "_id": "babel-plugin-istanbul@7.0.0", "_nodeVersion": "14.21.3", "_npmVersion": "6.14.18", "dist": {"integrity": "sha512-C5OzENSx/A+gt7t4VH1I2XsflxyPUmXRFPKBxt33xncdOmq7oROVM3bZv9Ysjjkv8OJYDMa+tKuKMvqU/H3xdw==", "shasum": "629a178f63b83dc9ecee46fd20266283b1f11280", "tarball": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-7.0.0.tgz", "fileCount": 6, "unpackedSize": 26442, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFahxS/FdH14dWCzr+TjRS9LSqrweZzm5UNvE6prwL4HAiAFC6woD3HzzTrqCsTRmaYRGwv/JJgU63a9E+tw4EvwKA=="}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-istanbul_7.0.0_1720180572638_0.37848173651766115"}, "_hasShrinkwrap": false}}, "time": {"created": "2016-06-23T14:53:55.134Z", "modified": "2024-07-05T11:56:12.950Z", "1.0.0-alpha.1": "2016-06-23T14:53:55.134Z", "1.0.0": "2016-06-28T04:47:15.568Z", "1.0.1": "2016-06-30T23:17:26.619Z", "1.0.2": "2016-07-03T18:52:16.731Z", "1.0.3": "2016-07-09T06:48:46.225Z", "1.1.0": "2016-07-21T04:32:19.388Z", "2.0.0": "2016-08-14T08:17:59.608Z", "2.0.1": "2016-09-02T16:26:26.473Z", "2.0.2": "2016-09-08T03:20:04.088Z", "2.0.3": "2016-10-17T07:03:23.125Z", "3.0.0": "2016-11-14T23:55:24.943Z", "3.1.0-candidate.0": "2016-12-27T07:24:52.189Z", "3.1.1-candidate.0": "2017-01-02T21:47:49.166Z", "3.1.1": "2017-01-04T01:41:47.059Z", "3.1.2-candidate.0": "2017-01-04T08:59:30.792Z", "3.1.2": "2017-01-04T10:34:02.445Z", "4.0.0": "2017-02-07T05:02:54.292Z", "4.1.0-candidate.0": "2017-03-21T06:43:57.016Z", "4.1.1": "2017-03-22T16:11:00.174Z", "4.1.2-candidate.0": "2017-03-27T06:32:36.083Z", "4.1.3": "2017-04-29T05:21:59.659Z", "4.1.4": "2017-05-27T21:45:10.676Z", "4.1.5": "2017-09-16T16:06:54.510Z", "4.1.6": "2018-03-09T22:49:35.184Z", "5.0.0": "2018-06-27T00:44:37.186Z", "5.0.1": "2018-07-24T22:55:15.243Z", "5.1.0": "2018-09-07T18:24:37.391Z", "5.1.1": "2019-01-11T02:25:35.622Z", "5.1.2": "2019-04-10T00:08:09.093Z", "5.1.3": "2019-04-23T11:20:48.496Z", "5.1.4": "2019-04-25T14:22:18.505Z", "5.2.0": "2019-07-18T04:48:10.816Z", "6.0.0-beta.0": "2019-10-09T02:18:04.687Z", "6.0.0-beta.1": "2019-12-07T01:14:05.472Z", "6.0.0-beta.2": "2019-12-09T01:17:19.542Z", "6.0.0": "2019-12-20T22:08:23.704Z", "6.1.1": "2021-10-16T20:56:06.611Z", "7.0.0": "2024-07-05T11:56:12.790Z"}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "author": {"name": "Thai Pangsakulyanont @dtinth"}, "repository": {"type": "git", "url": "git+https://github.com/istanbuljs/babel-plugin-istanbul.git"}, "keywords": ["istanbul", "babel", "plugin", "instrumentation"], "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://github.com/istanbuljs/babel-plugin-istanbul#readme", "bugs": {"url": "https://github.com/istanbuljs/babel-plugin-istanbul/issues"}, "readme": "# babel-plugin-istanbul\n\n[![Coverage Status](https://coveralls.io/repos/github/istanbuljs/babel-plugin-istanbul/badge.svg?branch=master)](https://coveralls.io/github/istanbuljs/babel-plugin-istanbul?branch=master)\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n[![community slack](http://devtoolscommunity.herokuapp.com/badge.svg)](http://devtoolscommunity.herokuapp.com)\n\n_Having problems? want to contribute? join our [community slack](http://devtoolscommunity.herokuapp.com)_.\n\nA Babel plugin that instruments your code with Istanbul coverage.\nIt can instantly be used with [karma-coverage](https://github.com/karma-runner/karma-coverage) and mocha on Node.js (through [nyc](https://github.com/bcoe/nyc)).\n\n__Note:__ This plugin does not generate any report or save any data to any file;\nit only adds instrumenting code to your JavaScript source code.\nTo integrate with testing tools, please see the [Integrations](#integrations) section.\n\n## Usage\n\nInstall it:\n\n```\nnpm install --save-dev babel-plugin-istanbul\n```\n\nAdd it to `.babelrc` in test mode:\n\n```js\n{\n  \"env\": {\n    \"test\": {\n      \"plugins\": [ \"istanbul\" ]\n    }\n  }\n}\n```\n\nOptionally, use [cross-env](https://www.npmjs.com/package/cross-env) to set\n`NODE_ENV=test`:\n\n```json\n{\n  \"scripts\": {\n    \"test\": \"cross-env NODE_ENV=test nyc --reporter=lcov --reporter=text mocha test/*.js\"\n  }\n}\n```\n\n## Integrations\n\n### karma\n\nIt _just works_ with Karma. First, make sure that the code is already transpiled by Babel (either using `karma-babel-preprocessor`, `karma-webpack`, or `karma-browserify`). Then, simply set up [karma-coverage](https://github.com/karma-runner/karma-coverage) according to the docs, but __don’t add the `coverage` preprocessor.__ This plugin has already instrumented your code, and Karma should pick it up automatically.\n\nIt has been tested with [bemusic/bemuse](https://codecov.io/github/bemusic/bemuse) project, which contains ~2400 statements.\n\n### mocha on node.js (through nyc)\n\nConfigure Mocha to transpile JavaScript code using Babel, then you can run your tests with [`nyc`](https://github.com/bcoe/nyc), which will collect all the coverage report.\n\nbabel-plugin-istanbul respects the `include`/`exclude` configuration options from nyc,\nbut you also need to __configure NYC not to instrument your code__ by adding these settings in your `package.json`:\n\n```js\n  \"nyc\": {\n    \"sourceMap\": false,\n    \"instrument\": false\n  },\n```\n\n## Ignoring files\n\nYou don't want to cover your test files as this will skew your coverage results. You can configure this by providing plugin options matching nyc's [`exclude`/`include` rules](https://github.com/bcoe/nyc#excluding-files):\n\n```json\n{\n  \"env\": {\n    \"test\": {\n      \"plugins\": [\n        [\"istanbul\", {\n          \"exclude\": [\n            \"**/*.spec.js\"\n          ]\n        }]\n      ]\n    }\n  }\n}\n```\n\nIf you don't provide options in your Babel config, the plugin will look for `exclude`/`include` config under an `\"nyc\"` key in `package.json`.\n\nYou can also use [istanbul's ignore hints](https://github.com/gotwarlost/istanbul/blob/master/ignoring-code-for-coverage.md#ignoring-code-for-coverage-purposes) to specify specific lines of code to skip instrumenting.\n\n## Source Maps\n\nBy default, this plugin will pick up inline source maps and attach them to the instrumented code such that code coverage can be remapped back to the original source, even for multi-step build processes. This can be memory intensive. Set `useInlineSourceMaps` to prevent this behavior.\n\n```json\n{\n  \"env\": {\n    \"test\": {\n      \"plugins\": [\n        [\"istanbul\", {\n          \"useInlineSourceMaps\": false\n        }]\n      ]\n    }\n  }\n}\n```\n\nIf you're instrumenting code programatically, you can pass a source map explicitly.\n```js\nimport babelPluginIstanbul from 'babel-plugin-istanbul';\n\nfunction instrument(sourceCode, sourceMap, filename) {\n  return babel.transform(sourceCode, {\n    filename,\n    plugins: [\n      [babelPluginIstanbul, {\n        inputSourceMap: sourceMap\n      }]\n    ]\n  })\n}\n```\n\n## Credit where credit is due\n\nThe approach used in `babel-plugin-istanbul` was inspired by [Thai Pangsakulyanont](https://github.com/dtinth)'s original library [`babel-plugin-__coverage__`](https://github.com/dtinth/babel-plugin-__coverage__).\n\n## `babel-plugin-istanbul` for enterprise\n\nAvailable as part of the Tidelift Subscription.\n\nThe maintainers of `babel-plugin-istanbul` and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-babel-plugin-istanbul?utm_source=npm-babel-plugin-istanbul&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n", "readmeFilename": "README.md", "users": {"mrzmmr": true, "ziflex": true, "fakefarm": true, "xueboren": true, "azazeln28": true, "finnhvman": true, "flumpus-dev": true}}