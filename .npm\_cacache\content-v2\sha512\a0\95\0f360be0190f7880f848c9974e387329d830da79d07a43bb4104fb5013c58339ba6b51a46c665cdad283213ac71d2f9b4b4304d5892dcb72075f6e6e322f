{"_id": "@babel/plugin-syntax-class-properties", "_rev": "73-f3bfb607080ccb07b30ede4e20e3e8d3", "name": "@babel/plugin-syntax-class-properties", "description": "Allow parsing of class properties", "dist-tags": {"latest": "7.12.13"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.4", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.4", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ge6f/nyLY/XYu8orq/Maa35ZkT9qyTRRSfNpF6Q47OewJHoYSqot1GkCN936AAvrvsHjmS8QwzPKCc8Oz67n3Q==", "shasum": "0a2835ead5806986af7046d61ba1e9343188508e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.4.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFQImWxZv192cTKguRzP25VoUmp2AhMsTl59srX/f3idAiAXakYG6ub3yE+FHFadymJMwTL5ctM75Lg5AAbFV/Rhlw=="}]}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties-7.0.0-beta.4.tgz_1509388450772_0.3150237905792892"}, "directories": {}}, "7.0.0-beta.5": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.5", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.5", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ood5Bv0Rk8MM738zjgnyK97gdzujQQtC/UGvWGxtICvkWm6g7yUvfUSs9E/PiG8WqsmrskAafTDKwrIa0Cmsig==", "shasum": "01ebbb0c2d216cd45b01e46a7d6691c78847e93d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.5.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD68EltIwGw/yQZOhl7OyKGic98ggVflyetqwNNJtPugQIgCndu9tR/e0aFrZpDa8Ss3N5QvSvQwEHOtpK+sfFN7q4="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties-7.0.0-beta.5.tgz_1509396956722_0.44726330507546663"}, "directories": {}}, "7.0.0-beta.31": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.31", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.31", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-smv85gKtr+pvZshNjPY9BYf9bVzDa/v/GrCr/iXEIh/y9XkvhAUl3hbQf2G6uLQpYOQJwhtiablNtkuc6KLxcA==", "shasum": "e6dd7250f9ebc94b06973d3c334ffec7073010a8", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.31.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAfSUc1Gk6ytL1hH98kF56tczn5dJ9YMD7lQNtMJVDafAiANFVDSoqAFWnf7Gp70MJUWdJ/isijSN2CisxsyDXwHXA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties-7.0.0-beta.31.tgz_1509739389887_0.8759429934434593"}, "directories": {}}, "7.0.0-beta.32": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.32", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.32", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-9PF0kjGQDnpszttGbDuypjqB6rCbu3WTSpBXkCmmgwHmDMXiCXxwAUpZFjrtCgjxVA0q4rVwl2tCfCjVkgBk8w==", "shasum": "5e47dae03ae307da350c28b6631f060d4b2e2988", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.32.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG3pyaZOkb5sGKsIGS+PpoORHJKb+bmLZEKDvt8E/bYtAiAd45YGHWkDqNgcD+2UV5pn2NTV6Axf4RyNBYV/tesdZQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties-7.0.0-beta.32.tgz_1510493578618_0.4181300865020603"}, "directories": {}}, "7.0.0-beta.33": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.33", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.33", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-WaNxNdRyvYr0hnE6w9V53Em2gg3GxBKESwWhNp0kschCRH86Wflr/aJGAElKWw8gaz1DBkjiKIPifAqGwLs1PQ==", "shasum": "1f48adfd4275c865233963463b33eab12d4dbe7a", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.33.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAMgiit+mic1hrRWB9OIdVDOjKqF//VZWLUJ1vKbZvPQAiAtOjGFodAFHnQWI2K/RXBIITt6cpt85reGDH++EWbQUQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties-7.0.0-beta.33.tgz_1512137825262_0.18179701222106814"}, "directories": {}}, "7.0.0-beta.34": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.34", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.34", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-XuTrpRivb04Pd8Knmduuo1ejgrmpK2dX5fftVmgVT+noX9/MC1s+A0lAqmAISRy029uth5hM34oappUYz0LrFw==", "shasum": "d345b262622ff9500d9ad039aa0b77a94b4b8dd5", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.34.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCXlNO392h31VBoVL9XSI2kzdII8A3jeHzlaqh4e2jc6wIgKKLWzQhO+aax5HYiIx1DB+3ddS1lqjDwDWNVMQvC8Ug="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties-7.0.0-beta.34.tgz_1512225539277_0.5028581840451807"}, "directories": {}}, "7.0.0-beta.35": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.35", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.35", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-kweQkDw2GRuPAmU/w/H8DdjpF633hQ/MJ2ndshIxuMWqjTymsjKsK7eEV8BkagnURW6zMcI2DLWHzzfLJHwYGw==", "shasum": "adefdbb84dfcfc8674d0819216363d983ee494ee", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.35.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHaqvpqUiJrl91xJQKvCLtLBX7y1dZWqrA2rXm5kZSD7AiEAhBqfhJLvzGp3Igl3wvy/XVatOOmyMOpifGgxGttWJ3A="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties-7.0.0-beta.35.tgz_1513288051297_0.14780653291381896"}, "directories": {}}, "7.0.0-beta.36": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.36", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.36", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Xw383kt3/htX5JjiwoSvjpoVs6n7Vk6COG1DfdDY4uJ8ccsJ8UZT09iJ4s7ckPgVeQCFgcfOb4xAUoAqLdOizQ==", "shasum": "a0002fa66dbb43e82402b4cc947dac0e07058818", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.36.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAVGipJsNlewQYaveaQmUJrkeqfmCIHPdf6Hi3xJc1JaAiBUeNAXufMUYVV+rqMkzg9M2G7VWBhArdNQv8/U2yz7ZQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "kentcdodds"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "not-an-a<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "mysticatea"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties-7.0.0-beta.36.tgz_1514228655632_0.47712706541642547"}, "directories": {}}, "7.0.0-beta.37": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.37", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.37", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-lZ7bswfBRs/FeeMq8XqHvXBWEv8NoqLgbVAVCsl7s5J/cq+imMKBDAZhU29e9looyywRFKoRiZplHacuW7NR1Q==", "shasum": "5d4e4712ee57815ebe59e63fb084353a27e2671d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.37.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCo0MyMRYPaHuPc9FMiRS+kQUXYiiO2rP75f9a+xSTddAIhAKrimXuM3EZs+f38YQOmY0QTD+p8aEcv87KJ80fbmVCP"}]}, "maintainers": [{"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties-7.0.0-beta.37.tgz_1515427338269_0.5794945524539798"}, "directories": {}}, "7.0.0-beta.38": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.38", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.38", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-hxA1M1CryWjbtnMWIpsOe4agySBW7K00H33cWDYPg4w4fCk824P+E/TXyh6ptGnYuGtfboge4DXS5NlF/NWTpw==", "shasum": "e06a9bf6446611d78dfc71f84cddfe9219fb2829", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.38.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFFGsC4SP1j82GGGths1ruEbKkyRbSfmizjLpmQnb2zJAiB1c5cIAkV+le+jJ8KysQxAszcA/gIXAmoyIqlJ/LeKKQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties-7.0.0-beta.38.tgz_1516206696931_0.39616700098849833"}, "directories": {}}, "7.0.0-beta.39": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.39", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.39", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-aADIdmsmKwiDOL+569ioR3Zh5YSB9/Lv1lGuh8B1K6Y/NJWXOMZdCOgxseq2tABcuPV1v4xQ+gavFW+mZf7ldQ==", "shasum": "a1a0e89c6042635cd21aafbdec3f006f06c368aa", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.39.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDYGakRqH/5G4sso0MFMf1mmKpAlUxkqO11bP8WOfxYoQIgPkWkSKEJmkTxMpO2qFwCH89dv4nRLsb+vQyDsGo2gfU="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties-7.0.0-beta.39.tgz_1517344043432_0.02715396136045456"}, "directories": {}}, "7.0.0-beta.40": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.40", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "devDependencies": {"@babel/core": "7.0.0-beta.40"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.40", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-JQUvaacmUpzXDU6BVE1l7hMES6AOJLAyGuEapTop3hD1BDvKwLOIrzwZxawBmp3GOA02S3m4VdUyIfWS5DATjw==", "shasum": "ff82c04c6d97cdb947dc64e3f3d4bc791e85a16f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.40.tgz", "fileCount": 3, "unpackedSize": 1217, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIErCJsvghwMCc+m1vQWxAy41JWZVZMTOcLRVsIXrOE9jAiBOC8LDU3ZWvlr85xvURgLwI2vrT38BxJoGxTFeIvbnPw=="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0-beta.40_1518453405441_0.5946548904181632"}, "_hasShrinkwrap": false}, "7.0.0-beta.41": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.41", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "devDependencies": {"@babel/core": "7.0.0-beta.41"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.41", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-uYWGgRTWpZUXxv8ww7PBWzqktJwCOckpy5c7IfZuDBZ+SN3i8gywMvPn1tparc2hVSppJOv+PODpDLo/DP+SCQ==", "shasum": "8ee660cf0115a1b9e24404acb6437bc58bf16be6", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.41.tgz", "fileCount": 3, "unpackedSize": 1452, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICp2xiN/qpBjnP+0XSpT8lTeAxFUZbYB10gIIIxoTqBFAiEA6785sUF/osakQsUOo0AXnqMxOvGeSBw4WkL39a05Dy0="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0-beta.41_1521044749057_0.830515232764131"}, "_hasShrinkwrap": false}, "7.0.0-beta.42": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.42", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "devDependencies": {"@babel/core": "7.0.0-beta.42"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.42", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-nG0XCeuni6GgjxOqtxVtnfSoRFeXdqY6cja83cmFpC1klw8f6XShbeDmK7xX1mUYBHkEqLTDurlX+fuua9siCg==", "shasum": "80ccce27907f22d0ffb49721e9d2cde311b41459", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.42.tgz", "fileCount": 3, "unpackedSize": 1452, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDqyww8bRnqKbLLMjDifbT4dSmrNkdYk0iqxHe/ZgySSgIgfL1GxjwXKUdzz3tSfH6UHStqczBI4TvsesAbwxx5oKw="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0-beta.42_1521147024116_0.38184186863610714"}, "_hasShrinkwrap": false}, "7.0.0-beta.43": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.43", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "devDependencies": {"@babel/core": "7.0.0-beta.43"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.43", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Ze0/MYR+diO7KdLZbkoVhWebZqj8GzbdUs85Ndyhfnz9wBN0fI45cTZrZ2HE/IHcjwirMefPdlJz37R8zK2Syw==", "shasum": "9562ff1cc10950669564c41998c1a521cce4903a", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.43.tgz", "fileCount": 3, "unpackedSize": 1557, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCrfvYnvUKSNjGOHuTZ6AwVnH7UmQjh3/jES5Y+Uvr4DQIhAJN5jkJPwPg4rmcR7Rd+lYh1Pjxh335K/x0HXwArHVzq"}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0-beta.43_1522687692125_0.35594368571573987"}, "_hasShrinkwrap": false}, "7.0.0-beta.44": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.44", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "devDependencies": {"@babel/core": "7.0.0-beta.44"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.44", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-lyojjfVoXuL+Oa95TSVWyf5fMO+JzvMpbFQaZN+voWajMt+Cq3lx8N5JszjlcQzBOZOffqjzMxlAKhNVw95ufA==", "shasum": "1e4e67ef6d7101a3a7d2ae5f60e580cbf4b7750f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.44.tgz", "fileCount": 3, "unpackedSize": 1608, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBxECtskFryKxlpuOzhD/JQaSrQbOeCssfyT4Dm9f6BkAiBFTBbwnH/rGsrcPvfcY7QGUpV527svJS8zEhyGj8oQcg=="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0-beta.44_1522707593756_0.15030332138747116"}, "_hasShrinkwrap": false}, "7.0.0-beta.45": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.45", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "devDependencies": {"@babel/core": "7.0.0-beta.45"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.45", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-SQLVQeUdpzI/s5jJkAIO7sjPiIgmzlPoRvugyK/870I/v76QVZY6k4ALmp7JuhgAf3+4dy8xzFCo8cwbWsHbEg==", "shasum": "d2fcb6bd1dca6c6fbe8eb4f859a5a4b235c1291b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.45.tgz", "fileCount": 3, "unpackedSize": 1634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T0yCRA9TVsSAnZWagAAdPMP/3xy1SSeFI8coIug/esm\nochUd7JTD37jCQysZVXU2cUg9v24XM/mGw4IvGBuDPXeSA2WfzdYakfHWhTJ\n81KF52IGeGIm1YoiNGuisugxt7WO8XZt4XUD6oZnZ1m/FUA8HFNhR0pGyDd/\nUBotWi/8bUFdbUto1KtNl7VxrlFkZjCPOYVRHpCSbwYTz752NEHwTXXv+exG\nbUujAZZvh7AJXUMM/VuFinFIueDP3uMRzo2IYlkANBRJuksTi6OOUfWp4Hhd\nrh9QZeqD3vxvPSlJ6iCMvtpuFezf54kwo01HRZdsiFJ/fB36Rafcgkn/hyAF\nAPfrQswrs2HfxacGaVIzXGhcwR2ZTVSP2obW36lEP8PPeUDMhpkynl9jdUk0\nDYRO/QorM5A+GRC8bS2Sal2R572rtZ5KL1b+CY7CDVOwN+GzLUYvYteQb5yM\newkrmyegbbwA4EFaXLRIA/ZPi3Fys317xy5wFsoMxBrsYo9ZFYtS6NRL2rLW\n7DA/tF1V0s3q8rkDNOMgkbZsikoVQbWTTv+S6Kp5r3iGy9+I3gdObmUAtByJ\ng29i+4fydbkuIzlQNbzLjCgP00vOSp/cuRxcXBPw8kLF7Kyva5Efrfm/NJFg\nhPwKpT0v644F8hz6jsC/fGSegjSL5OM/onoWaZSeJx5BOpu1vIXr1/zEi8g1\ntOth\r\n=xYfo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIErf6dRlp+YV54H9Pq/CHTYgqTbFPdkogryx1ggOeAaPAiEA51WcUeih4d8Ri8I3NL7H+54dTg6Sj0Wxnkn/fa/SMx8="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0-beta.45_1524448562484_0.4788732286742723"}, "_hasShrinkwrap": false}, "7.0.0-beta.46": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.46", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "devDependencies": {"@babel/core": "7.0.0-beta.46"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.46", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-NwtgTQ+I8B2eo5h1mZF64nloLaGQuPM4M/c/swvyvqHoWLissHhm94rOE2Ghte8WMgQ/Nw3bqJd59kpbckqmdQ==", "shasum": "dad4df6c31b65ba359fec3b02fb8413896e75efc", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.46.tgz", "fileCount": 3, "unpackedSize": 1634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WFkCRA9TVsSAnZWagAAP6gP/1AHZzY5Q2B3e/7s2PA+\nDqFr3qoOzrJ85unRHkleoYDfRqHBprwrUDh7PNfORnFxQNq90/5o/kIG0/7v\n8M1QRkDKj+e3rvNaA573ZOiE8U1c83P/tAlQx+mbpMj5n6LQGmbPfnP6jL/z\nSx0zcLM4uMSNO7HBh0vuKnuP1UFOhuFwUoufrcIkOntdTfSHKUoyo3OZFsMe\nkx450TO1+BbAJqhQcVLVMupxPWbTF74xzLbvfZ2mlfhaDqO1MY9zmZv7RYDc\nC/q0n2yQxj529JHeDNIDqdpuIbxqlpLRjnnz7vJm3S7IjLjVLNCypToAhRZQ\nxzvOihpdApL776O0QO/qpbVqrPaOcw771XY51VFr1Xa4RTYjcVTSqQHnIpSk\nx3ZgrxpUyTGhP1c6ES8c/gPwTszlvb2OX6IebtmHR2lToZ3D86+1YG0s8hGN\n1upu4c8pqzifOfgZoanQwBxcsTGBYtw9V8byDKH+WmtHwu2I3tbhKh4BIeY9\n/BYwOSvkxGXVwv9YyCU11vY7ocleKXp9U6kxi7TCBV7cClOupPp76KFTLyN3\naIZ5e+HdylCdRt7fpYmOm5Gt/xfRaXe44frJRWnR+tGKX2AeaR/w/s5Tasck\nTJ/hywLm4Qh64fyFOnRcA1tNzxJlNPFr760/lgANa5PJShs8eknFD9siUCtP\nRqrr\r\n=RZtR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6gIO+e+QWS2OxDqMYY8pGxFCGdWZWJVDCEoXe5QTLGgIhAOItfUVJInjD20Or57sgrRfj+463pIPI58Theg8QNEU2"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0-beta.46_1524457827647_0.5043689161623954"}, "_hasShrinkwrap": false}, "7.0.0-beta.47": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.47", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "devDependencies": {"@babel/core": "7.0.0-beta.47"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.47", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-vLoAuLSjHSenX3TQmri6ttQWZp3rEtGcRp4LgYEBQ012fN5h+KmcssvkCAqm6V6ozS5KzUWpBlZ6t7YhZG6oBw==", "shasum": "de52bed12fd472c848e1562f57dd4a202fe27f11", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.47.tgz", "fileCount": 3, "unpackedSize": 1601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iTjCRA9TVsSAnZWagAA08kP/2oGm7N7h19G/6+MH8Zu\nWZ7oYIOiTtkMRXYH++uEyJdpqpuuX4wWsizb5exzbCzDY2IiBxElnqXGsyZW\nNqrlfUkFRFkoGmG3ptRgdBJn/2wi2uQNBgFodMVv2it5XW3AMV6LEmcaVTX2\nQ4QTyMB/YX3otOSVP9uDXmqxoeP0S+FNRT/A2Db3kL5/g6dlhyfB3Fuwni5U\n6lg2JLe42UXVTjCP7FCSZa5D7KlKIqCBLbuzWJyp4+evhojtQa7oXccSsca6\nZCIJXYPdHLU4mjBV7hxSccZgVcUWw/wN+d80mAPfaNLJe/lDdNnGNvuS+j8L\n7V8c/HDh4LK8i4uKGD7dmKWHL5LbnSbdEMeZfL6gHhy0WuouQKjcRBLGXns1\n5dIonCZ0EZ224EVFjyn/CBb8LhXgQxMjZSvuvj9a8jZ1SRudyk3QssHKU80X\n+R3FKP0oJxVDOTsgDcZX9Rah7zZqwbAuhoEEjRZNtQ5OKU4unwK2zHgHC0AH\n/cDiB0m2DBkY821YKY/MVCzCqoo9/KFaGdGSYs4qQ8tF/xOVD3KAKp+xFExP\nJXd3OUgrs9sLHGnYXoLf1NZiRAbDMxvNteQ+UDmilotV2++JUkddkykYVJf3\nDOpxieDyq1ktRM2ZCQCm7EJdl48ZRt92ufXUwCX9WAOL7ABAH55XjcojfZFD\ngYxt\r\n=gag8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAopLfS5zZA/alBrsNy297iZhsPQ/bS9SPO08NgB/FsDAiAQxYqZOjpDTmacqlWP9L8UGaK7MwVPlJMr8Zu2c5REMA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0-beta.47_1526342882655_0.7763040815778759"}, "_hasShrinkwrap": false}, "7.0.0-beta.48": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.48", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "devDependencies": {"@babel/core": "7.0.0-beta.48"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.48", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-pKieyemp6fw0LAMro/jJZe78UnypA+5xNAv4cmHZ1cNjGblcnC3WS2H18ruXOpHL1qQ4ITkcQZh18wFG78KEng==", "shasum": "a87a9f0a5975ba4cd174fc55e22413c71b714e33", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.48.tgz", "fileCount": 3, "unpackedSize": 1583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxC1CRA9TVsSAnZWagAAzvkP+wRhkXQxoBLtbs+qfhMt\nwgP2lomxXlY0ridjOqsXffGgM9mK/kQPJdN0ZrVw0WEdKWQsS3CM6YMZIkxy\nVktanqt7crbBb3r4G78jfgcbS3yRcbWOm3xvjx+U1HZy24oYutRffFf8whob\nrqYRp9poKYXzyDFrKlhBBWaDf0SkVK9eoWn0LFrQexG6yRbv9IizqUdwAlaQ\nddGhShzwaCx2fmnx+HprmPVt9/5dqaRhe1ja/IsjODM4Ss996FscK4IBt4lk\n/69P8bpQnIg0ZJR8JTFXUJFtZwlgvd5d8PQCS+kSlrWKIET1lOuJ5MqT7xC6\n5SYBjC4bjjpMuLl7MjJ38g177yXXr4GVPdTcHDR79YJjTskqLc7wJHv0KSZk\n2fFwkBY7Q6oYbDNTxIEa1Oqh+zyT6e4T87dJSpap+17e8CA01ujdjPuO3s/E\ncrKqAQkHuitN63c/V8YYbVccNKvugkcQUM7Fa7DzLa+afSMR5BGJb6kS62N+\nST8j48AGzlLcEJzKkzbaSwauqf+KC7sOWngzPGTuwSGc3weE/Q0sagA8gdWX\nQ+uHeSjjcxofsHSKLAdUuN+YdB7e8pBX0mh+4zJOy8yoqB009fr5yBxYkBQD\nJ/rctWbdfRIy+XmbFzvLhxBMnuoCyMMIlv7qNA79sjZSLHv4E7OamWXfxKSf\na/y+\r\n=4CA5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIClvcU3aL13/2OWFChjlJrGODRA2gTuBOyaRqjCSfxEUAiEArpdyeeQx26QgJhc3eeiK7rKSpgbrtBNggMSHAzFjZqg="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0-beta.48_1527189685729_0.5063372417213146"}, "_hasShrinkwrap": false}, "7.0.0-beta.49": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.49", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.49"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.49", "scripts": {}, "_shasum": "6a14fa47ceaa32b53e14e6648326e52dab306904", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.12.3", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "6a14fa47ceaa32b53e14e6648326e52dab306904", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.49.tgz", "fileCount": 4, "unpackedSize": 1598, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDM6CRA9TVsSAnZWagAAA5IP/REloC+dq9VMwdKkIGUD\nnR8vC8uSvm5fw7zBL8rBRK7sUaIpRlDH6REOT+izLLk4ctfSqBlBV1mS7TDJ\nxXYpglf48m0Fqyz6Qlc0JPETvANPzf8pis70aQE7rq25kxkLcVMraM9ljM96\nWuM7EqV+Um45qmRN6EA9t6mnxiLFUVpjqGbziqRvgKyuA+DZ045/JimX5RDl\nQIQoFh6JRIe6ke7SeTkv+CrPY2dJqQpNVVMfE1qVh8u2eN1+d9wLz2+kWYhm\nokUbrN06wEq4pD97/rjJHauPSSWWusDcYSW98T/SL9wrlQVBqgOAC8e1FljJ\nxY6JmKn4jlJADZQSVeKsDDLlp7wWRomJb6nF92EC1n9AQ6HBsZ/GyZDV92YC\ncCpi/i1JOov9t+VBPihiWpJx7mbigXJZ9RIOe8xcRB9mJGneaPCgrwWsgxk3\nXLfnjiIS7OqzkopY7+/Zxf9nXRKLWM0csI5JGdJEYL+zkFV9DK+uFCkPggg6\nTY23P4X7IhkF2BBHliz4ViDNw0NYhPzLa9dlY3Gk5BdFaF3ryaYt0IxFkGrn\nKA/4dGfEPrgL4bbFHkmdMMuV6M95FS4GQlL7f8m+j0SUCKclEYe1b8QPnDbY\nZrgx04eCgUzenRCxeXmHhiha41RiFU9fysd3lNQ2pHPXfg2GLNvAELjwkPEc\nt8mb\r\n=TcjR\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-3qm7HJlYj8TwwF1EYrwvI1E6wMdUN68LIqG88pfXekqaItdT/WhDF12qYhOUbLIB8tZjoH9BdgMRAhRC8YGnDA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGeEOpVdCmCG0Zjz9V6iArkM9zB17B42g9NeqXl0UzN9AiEA68aX8R9Ms/xTLRi089e7CVelcB5PwH9aGS1j6uAbI0w="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0-beta.49_1527264057918_0.40619120007048726"}, "_hasShrinkwrap": false}, "7.0.0-beta.50": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.50", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.50"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.50", "dist": {"shasum": "5569b47d9e9ef5b703506829508f50bc73ac1274", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.50.tgz", "fileCount": 5, "unpackedSize": 1469, "integrity": "sha512-gpXE/G75z7Z77WGF4/QHzPXOAKECS66moEkC++q5BkmMskeq5gz2YNxmSueGpeLtOh2ER3JbA8v9TdgBZhWu1Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICOTD/lO78YvB107oDKyPDF5OarKtL03XZ9glSjpz2ZbAiBKECi8Wxeb1fusdDGLiPPnMRj+Ydm7fUXityQZ0bkW1g=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0-beta.50_1528832818126_0.6309819802076808"}, "_hasShrinkwrap": false}, "7.0.0-beta.51": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.51", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.51"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.51", "dist": {"shasum": "f0cbf6f22a879c593a07e8e141c908e087701e91", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.51.tgz", "fileCount": 5, "unpackedSize": 1483, "integrity": "sha512-cd1ZJd/Der3rdb7nWGGToAjve5FA1i9SPLNZbdM5Jc/fnvvkO7HJM2iG2YYyT5dTmSl1s2xE465SPSaak8CkTQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGZPdrhXPd4UyIy/sRQWuXhfujNT0k49gmBkysRE4V7vAiBKgEaYOcjnPBD22821OJ4bV1SpwM/VIPkZVN5OQNQUrA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0-beta.51_1528838366971_0.02355594621931023"}, "_hasShrinkwrap": false}, "7.0.0-beta.52": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.52", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.52"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.52", "dist": {"shasum": "db43035fc9785f310d53202bc1fce2f375cca220", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.52.tgz", "fileCount": 5, "unpackedSize": 1482, "integrity": "sha512-0jQrvzJAfk8T4b5OUCiJ2SY8Cb5HV9Tv0nRlRKpXvLOBd5fSizX4JqwQ7yg0hC+STWzeOQdeJlCBDhuuc+Yb3A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDqCDWF7X0jtJLCqxZM2tO2C+aMvUyhu4RJz9rMKW6T1AiBOyNfvY3bFcCAFpsGcqh58TKjX+rsYTzL6C195kWicjQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0-beta.52_1530838755993_0.2892458364549104"}, "_hasShrinkwrap": false}, "7.0.0-beta.53": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.53", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.53"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.53", "dist": {"shasum": "0c673f3a6e5192fc8027bc2cf4dbfaf70dfbff72", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.53.tgz", "fileCount": 5, "unpackedSize": 1482, "integrity": "sha512-bQKCbnbSKzkw+NLB9aHKLsXPNGjw2mFfoXfKNQ72u4wgDjtv4yCkJsLjkxGdo45QYqQbqcXsZMLCO/5KLiHbBQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCNvxpk4H43/zsIM+WE2mpmj26r1RX3zEMC/Tm56cC3/gIhAO0c8kPHxx+uaJ7/NEs+e9qNhmUuM0klKdz0G4vTN4PX"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0-beta.53_1531316405740_0.9264601259588554"}, "_hasShrinkwrap": false}, "7.0.0-beta.54": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.54", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.54"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.54", "dist": {"shasum": "5e70f22dc3628c1d35402b63ff1a8f8e005bd871", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.54.tgz", "fileCount": 5, "unpackedSize": 1482, "integrity": "sha512-YTdreNZ4RhHbQI5wIxA9KMQUQyuEqIEKKb4uKneQATeESAbOe+oWlgOrTgtjqiPWfLWn9elELail95+glfyqmw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCrBnfgZeb3Uk+tzy4mri8ybi9S7Xnx3t+7g+kJ4pGaJwIhAOLiwuGjwXzxox8kB8S2m8BDC3clFxQx232xL3VcL7Wq"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0-beta.54_1531763996862_0.8792557166371882"}, "_hasShrinkwrap": false}, "7.0.0-beta.55": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.55", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.55"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.55", "dist": {"shasum": "ecef94fba98b5ecba8a49991c0ab30de6723dc94", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.55.tgz", "fileCount": 5, "unpackedSize": 1482, "integrity": "sha512-82BWfhUcAtD0M/XI6oz3SALHOf5c9iT6Ie2ok+MxIigB/tQ73z6efST7l+x83hNHSBjmIzonIgVrsbCPBlJzNQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBWo9eY464s3w/tG5o+1HRc3E70kl37m1ksOAnibvjIpAiA+cF1rmo2ySbz5/Umo1NDKn1lcnlGCvNkaGf82dtE/4g=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0-beta.55_1532815619864_0.09467772272727637"}, "_hasShrinkwrap": false}, "7.0.0-beta.56": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-beta.56", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.56"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-beta.56", "dist": {"shasum": "ab0cfbb1aa681810fb109e73ae125d3682837c33", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-beta.56.tgz", "integrity": "sha512-7aq+tQjiOb6pNl5sFn1lSi6sM+LIWw4eRocFZKhbr1GX9hW9I8jV9G+lxvfswiKniyYRyeAua7yIz/VuCH/tfQ==", "fileCount": 5, "unpackedSize": 1482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPuECRA9TVsSAnZWagAAzT8P/jpSKyub+bfX4/sdekh6\nC7YY0bWoNsc5rLHUjczfg++DhQOk4+s3oHFVXoTEB78XrSR9vdwHQu2QM3lc\nW9V3w1MFaCExgn1VAdXRYmpDNrDlBIELzd8ZF9Vu4yLAPh6lmONnPDXfvR78\nxgQ+VrE+N4gg64mSjeyBOtySi1GHbfs5KNY6yoeCsWMQBcUwCwu+iKnB2J9o\nZxsnmxB5ocW/DawUi5oyILV121xueKsYjn9kmEibsS20A6uS/+7PC+CIJI0x\nASgBVKkbslQYFuImiBgXeqLReVQyV64L8y86zIM+73x9RwwF8QuP7tcoIjvo\nRShrg6PFoSWDccpiMPO1/z0TNqLaOUKFUS8N3ogUttaEYxKMXCNx6yF2stbL\nNpoaxtvmMA6URlQY72Iuhy7lmcdJlUBJ+Wxm9j6IN9vC5kLKQk/7hEkMdXXA\nPIeTPqC65g28WIqCKMD3Sp5qJIGfQoBv9jibIxaixUKbkQv6L2L7BWlgV+K+\nzFne11gCgCj9YsmC62dskiC3ZAlhz+DIvfGFfjTBQ+Mbjh6VzkkbB4elrEsX\nLPvyYJj6Bpod5EMOied7f5CU7/iGauxXDagbR43JH67Fd5DGAthil1plAbiB\nVlymX5aiIyzBMglUnikKSuf6WFlrw9pdkIKdlWUiscTJ+j7k7eHRrOyGv6bI\njyd7\r\n=X1kh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID/E4DiwUDt79P1SjEle8bWhm0XEDf7t0iHYbpNbD2dPAiBSXBTg33fITFyst78J1L1h2Rj/1RGhVA06RawxjmP7Ng=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0-beta.56_1533344643867_0.054374059505136296"}, "_hasShrinkwrap": false}, "7.0.0-rc.0": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-rc.0", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-rc.0"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-rc.0", "dist": {"shasum": "e1b60184432e8f12a487bace1b83d1e17aeac290", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-rc.0.tgz", "integrity": "sha512-Yi/1QlkhQEYcjrQRcsaJW3vPC92sP3QFH4GD7EIC36O2sRu+nd0RlGFbPeTdLwY6H6SVxwSpqO23fvf0i85rXw==", "fileCount": 5, "unpackedSize": 1473, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGRhCRA9TVsSAnZWagAAtDUP/3eLXJsGY86NsBgY2DYV\nh6Z/VEud5ygy5du4+F6Sav6WOwJflXbB7DzGOZbSnfBTGwFL2Loy48X37zwD\nzOcO/G836TeBZNi/0FSFlWHBvl0kFk26cohb7ofnfmabZ8JIK3jGTGb17hu+\n0Dcr7aL9q/W4jxtYkFF0nnIPG3pYDU2Xn5aniUqWAt3UVs2tWNHy8CAqGlFw\nmj7hxmweqUSEJdMPj3Vs1eMVbexfm4jXOcPL+opbpq421Ix+3eW2hVhhOMYK\nKsg0Mq9cChRUtXDh+Xj4lM0vbhYaqu+2SaaGpSb7MC9iFsR4IxZDOQPDg/nF\nvgOLMM6mbdI/z/cU5qbmObAtILt69nBUHOCW6bxWYzH5qyw53N+5EmKFLGK4\nVZBf7k6ji5PUD/mMDU97LOXXTAIXbjSX0KBcBMAmrGftjBXU7Bow1MPS0Gkc\n4SUKqh06U4lmUtWoX72SCXqCRgcY11BGdUxoVxm+eo7D9nyWpD3sKU1VEG3w\noCoWVhLSB7E4RzjClFdutGLc9sKwcRVApywoPYb6HhhSa1qvVTTmURHvijd7\nETZPsBx9wbl5g2oO5ZQjGvB9DM2Pg+PetnUxColm0gJDqzhvOVlxAptz3XF/\n+AkjLjp5HdETNxW1r5PqTagth1AZQHhz0KWWEmeCJj+hrtTLLAe3lhBD0893\nR3QH\r\n=451c\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC0YO5xYFnYym2lKt0KJiQ1ulpbWiliIvAhhqhrpAdb6gIgZkl28EtFdNJQSQrlwsznmbqJUWMye5C7DGxnmCjXFXc="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0-rc.0_1533830240461_0.10708561838935804"}, "_hasShrinkwrap": false}, "7.0.0-rc.1": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-rc.1", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.1"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-rc.1", "dist": {"shasum": "155343e256c84d127496e46675a3049636d311ff", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-rc.1.tgz", "integrity": "sha512-mWUD9BevSNhbsgwLgHZmd89keY4lgCoSbOeDo3ZiyyMc5y4fjSm+2LTHi/GeRyO6AnBbqTbPmFlznPdq15k7/g==", "fileCount": 5, "unpackedSize": 1454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ7lCRA9TVsSAnZWagAAgIIP/A/jQNw5B59Rq81Ww4/P\npCiFB79p6hkAvyvKlMJ4sYMKNWtiS+kN1whujS2mQElMOChQ/CkAPmdcIuiJ\n4lLjcCkah97HoKXTJEw4yLZN6d8O/8KeOLTZs8BDdB/dbT99c0euVPIbXAQ9\nPR/81D0NDMWL03lZpnURlZ3Y67vCN+1ekhtPjXyzsT60e8z1AO+TV0n9DuAB\ns0O+1bOAWWKagVJvEMfWjJ0Pbz6mRZAG18i94j+L8X/p+le/V0EJn48xUHqe\nyir3UAPBeXRUccSMIN0wyPqo8MmUKNgPfmpTQ8UDuhToExz+odBwQmWePbj2\nUg1dLtgz8sUq59OlW7aD2TFOj9yHEAM4g+4dleaG5DyKsBln9LZkuHDS25UE\nM73BSJSLA2qHDrajzlXiQF5loXft7eZTDVgs7cxvfKCQlXTzXps2ZIpehaND\np0Lo6uBjQGzckcj3Xz0I4t3r19FDZjdwidQ4KK/8sc7tQ5lV1DNav8xMnv8F\nF+t95SN7MqcHgmaDLFHHn9tjbo7HmvJR5H4WQ4PYrLToerY1w9TpsqDsUIRz\nywMW/Bll3v3D9CekNKxltXRrAGWynl7DhSX4MoQQE192BD2MwVHLdxOUmsRT\nSQIXPWHc+0DtjZlwdpfGBagxT0tuYMh8PaB95+fxmh0KY5gk4/Fcx+CpOdUP\nhhXZ\r\n=iWrg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG35JnOtPJds49S4jupKQJh9Gd4nhlSZsefa5v/5U/NHAiEA4yVDHNXsutUIghwJP8ftDZ9udZlmKqS1sLylGBxBFYU="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0-rc.1_1533845221338_0.8310894296497298"}, "_hasShrinkwrap": false}, "7.0.0-rc.2": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-rc.2", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.2"}, "_id": "@babel/plugin-syntax-class-properties@7.0.0-rc.2", "dist": {"shasum": "3ecbb8ba2878f07fdc350f7b7bf4bb88d071e846", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-rc.2.tgz", "integrity": "sha512-+keEGhwq+H6b/FiYXMq7vthTHs24L+KWr6kU3mpNXdUJMGCgAMHkZHCGlMFeZI5ScgrelhjaWUOc+hT2Je7qgw==", "fileCount": 5, "unpackedSize": 1454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGaWCRA9TVsSAnZWagAAhF8P/jUsgEyBzPekTVsn2Wol\na+NJdHD0tPHXr8f0fzY6cr8XGi7lfs0GVjrLRSbMqoW5AzZDwS7NC4Aocr3N\nTdT6mdKBOKfdFNDSvAQktamWB7b5Sa1k044pSV5s1O5TAA3EubkgNiZPI7Qy\nDORlLn6R+4WH+EvSBS86YiA7ZpYCo172q9vwxbJPbhweirD3eBx6gbu2OKtE\ngfc+e6uaTx/8qFIVfPgJ0G7boLj7efhas09YpX/Gs73Dm5RU1sRke+cnZNBh\nT/+pIQ3U+R130tBAh/3fyb2Y02XUgoA9LCjdYPgLhZZ3nkWho4De4tVXIsN6\nAQL0fvRRKXRKXW8YTTe2/UVoefMmxS/6Oeneg1iM+SZfOnPCdSn8nOTMMZM5\ndIkLv19RkGKQC4KBCeH7pjvj/3G/dRBz4RhpfxnbqnAdIPfom4EiLf7a6Wdp\n0Xi84Ns8CjdlIuZABu/ozmAeTdVkZ+eM2oJ0SH67k3VeE0p/kdpHVrHebO5Q\nJvuc2mdlcFObrzXGp5cnNDqgP8ZsuNr+dXQ4gSjIqQXIJIrvO8AGMixY1A/U\nn/JRqEFR2vbJGf5a7N7WNw0wmYKETvaJTqrBZPdKPyQHIj3abQLkC8IJsGiE\nW59qpoK8UO+CCApToO/LPQqyoWo62GuR9TgnB+IUJpCsUp16eAfAlgKXm/kW\n29hr\r\n=R2C+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDap19tPwKB1D3DJ04J6Qts1rDafoq/W4G2X3J0glju9AIgDVViMhiFV4shdP12HLslQMISfnIy6e9ptp+r0QWqRgM="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0-rc.2_1534879382189_0.2788409514003105"}, "_hasShrinkwrap": false}, "7.0.0-rc.3": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-rc.3", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.3"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-class-properties@7.0.0-rc.3", "dist": {"shasum": "6a2986f0c12fecded9ceda0fd368811421431d89", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-rc.3.tgz", "integrity": "sha512-eIGBAl6/NK3Ps3dVgjppKmHlem/ImAEPb7fBDo9uhVGUqOIdZuzFTAxohF9gseqqtIz6gWKZhFh89n6J1WwLdw==", "fileCount": 6, "unpackedSize": 2553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgElECRA9TVsSAnZWagAA8tUP/18nxAk0fsnAmjjVc0dU\nNWJbeRFjbWFR6WEYz9APVNpYe+sfHnzhq4w4NaI+/+sQe35xj22iHtduH7uD\nTOu+8cdSb79GQz8PRTzDyvHITVvOpKw2zLn2CWvokQcYjGgMIvgdoJ430dZw\nMSSBKv5JxtNP7rcONgD3YnPsB8EpAG6xEoxomIqk3bNpa9pGctSZkERFWQXT\nWHvd/QxqU1MauuOhxtDddsHeMIsVed0RyPpeyK2vMfvH6u8fn3/C10RNhA8O\n/RBrsAo2javCe37j/nP4S5U7gmv3VwG+OawUmlYNbpWcJyrwsKeFUDnQDKEo\nxw+1bYwjaDispb5/nGURX2n8fijeu/54TOa1nCzSB0c2qeRiwjnDKG2CwUh4\nO4TMkKTyNzKVg62a/KDE7al+YA9GmAAKsQ7h/sBF5ZLjT5ni7QIsWbAEgIBX\nR1gT8cSOcAG5y/rcZU7wbSM96zy72+VutlnppSoSlcGTG6sH3F/D5tZOBqJN\nOLWibx7xzDmrAayFTLkqJ75AfIomiUST6qwEQOoSttr1LFS2plWZH1eUdNZY\nsiLZD4D/t9NYuxWZQ0w5pZ/+HS+4ZWW/VzyqrRHwMcEGsDM1U93KOTMT4Nz8\ndhJrBj8byOFcaC5GvmVRjoIw1sK72bGPDOnqyHJArRejcLPeoHN/Q4VnEFJM\nNG06\r\n=ExLY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCo7jR4HhSKIulkoYvMqtbX63d6zdRnkRleTumPj4Di/AIgJtkGasXGo9QptSsWXHOfGAtOm7+lMHQHPTeulgRo8YQ="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0-rc.3_1535134019498_0.5176885954768407"}, "_hasShrinkwrap": false}, "7.0.0-rc.4": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0-rc.4", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0-rc.4"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-class-properties@7.0.0-rc.4", "dist": {"shasum": "c658d32fac81a6b424bc7b6516ebf26101aff7b1", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0-rc.4.tgz", "integrity": "sha512-sVRE3zYu1bMigVxP36j/ICm6rhnb6ubBqQbYO9YsjcRVe9z8CsWODUa3gQvs7o7jWZUKLR4iitI0Au2qrVVzvw==", "fileCount": 6, "unpackedSize": 2555, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCofCRA9TVsSAnZWagAA0EgP+gIHyGRRSYOLeGZQTZt6\nlZyCfdLYfV+Z5/zwUPuneF2zi8DPNCjq4bqsgBiTPt92y+6Xbbe+zJA5dh2G\nMUmtccaINHDnCJ282djFqVo00GqS4CJVuQDNlikeVS30CeFDYWHhlcqaP5UO\nlV5LF0vr3lCbbjSIP0/BF0vREMLB+acwOoQ5kCqDY+aan2JhwaNHuvFP1a3C\n8FQ6qHtwdzdME13Oyh3v/mwrF67Mmuo0byBnOCPc74390aoswCcY7Vz3DkUU\n1iSVs9ZB3ejVz4dPD+NSN2+UBOVV2jQrrmbpilWLW7jkVblREqBBOvZciSK5\n7JH6TDV1Al1e+HuSQ2EosImd7655zc3WChW69oK/y+VI/QOQ8ahmcFEnX5Mt\nPgmmuyWjcjcqFwIHHi1pAkBBu2MpzmlG6ePpaTGcD5Mu1ZVXNCMQX1P+9VHZ\n7Yh1wlsPMtiWBtgE3sl809qK203UmbQESf8vxgn62TDw8nu58mNV7OirCaMA\n86ny2pcgcvjv6BzR1lJwhLimyeycQCv98PRcq8lCj2CEACw7zOBQ8RgrNEGq\nJWzk5aZW6pDOCqFfxlnVaq/YRDi4h5dj8oLrG3ghqBps8wHEttx30Ip12hfX\ngjLNB18ZxUsjXClsD6Iv0fweWs8yeRB5b7auB+v7i3Nm6MV8EdevRkJK7z72\n2yuh\r\n=3PAH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGLYp8jzAamZLUcHLCe+Qg6KC1ItouaqpLHjuHw74W6uAiEAv0y5Nl8INcl31JO4krP3/y6OHYFvFBuhYOfoqCqYPv4="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0-rc.4_1535388190991_0.5855246793186895"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "@babel/plugin-syntax-class-properties", "version": "7.0.0", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0"}, "readmeFilename": "README.md", "readme": "# @babel/plugin-syntax-class-properties\n\n> Allow parsing of class properties\n\nSee our website [@babel/plugin-syntax-class-properties](https://babeljs.io/docs/en/next/babel-plugin-syntax-class-properties.html) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-syntax-class-properties\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-syntax-class-properties --dev\n```\n", "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-class-properties@7.0.0", "dist": {"shasum": "e051af5d300cbfbcec4a7476e37a803489881634", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.0.0.tgz", "integrity": "sha512-cR12g0Qzn4sgkjrbrzWy2GE7m9vMl/sFkqZ3gIpAQdrvPDnLM8180i+ANDFIXfjHo9aqp0ccJlQ0QNZcFUbf9w==", "fileCount": 6, "unpackedSize": 2540, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHA+CRA9TVsSAnZWagAAuMoQAIAgYTrYWhNOMCfccxSK\nFI4WB2wJ0Qgy84NZEp8UNMgqhZCNteJmYQKa6DgWGSl3zTbq5bJQypmlxWjS\nY4LY4tsQmb1hPFH1ExNpstarQgw1v8JoadilcXGTbvtFePkCjZ9rpwgKvB3/\nD+xoQgmQRfhLej3TZG51nhF+cMBDb8lwTdZjYixw0OrzBl+BB3ogZmpoMXx/\nA5P/LBPRYbpC6Pnx8FQs1CMj5CzTCUJp54RayvNtWJs55gncpWw+fGrBvi+Y\nBGMx/m/SAAoYbwaEzMoSM8NfpbLDEDtwiJfk4+spWHt6C1QgGajczPFKt4Q3\nEbRay/xaDV2+25hjGj7kBbQydR7EBKWqCThPLFiuo7YwtCOlDyC1JIp7G24U\n5o8gtTHtmz99WGtpK9MBbmaBzoNWD1sgyWy/X+QmGYdnh+rp4v8SnTrWx48b\nCzRU/zB7NCRzb4AjLIO64FAFrI8DLnJm5CLN9thJwU27cQmgQyyyI3ugQTsS\nohwTKaIdsMqXoQxuwodFDQMJ1/4aJmSCqYaJsGELgeVocPjRUI9dSOrHWWJg\ndg+FodXs/WXKzSWj7jRStdES+MjtXuB+55DFJJjJMOLvcaE+INWX8seD8Hy4\nJRpWGGU2BctSr/pIu8LR00x5JGCKwd7WoUyd+YSXjKZLx9FgIpKyXBj1gFT7\nBzsS\r\n=6PFu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF2HVW1hk/yhq6CNM6SSbpXkOQBj+G239fpbXumJFCu7AiEAqb/Hx195GBdNT+O/M4Pv7Tjc5DgMsNEgffFHkSyD+HQ="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.0.0_1535406141477_0.9585261067881299"}, "_hasShrinkwrap": false}, "7.2.0": {"name": "@babel/plugin-syntax-class-properties", "version": "7.2.0", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.2.0"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-class-properties@7.2.0", "dist": {"shasum": "23b3b7b9bcdabd73672a9149f728cd3be6214812", "integrity": "sha512-UxYaGXYQ7rrKJS/PxIKRkv3exi05oH7rokBAsmCSsCxz1sVPZ7Fu6FzKoGgUvmY+0YgSkYHgUoCh5R5bCNBQlw==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.2.0.tgz", "fileCount": 6, "unpackedSize": 2654, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX1aCRA9TVsSAnZWagAARYYP/0pmhj/Z3kr/BPK4G2Y2\nnLXWexlyV0iv4BTap0WUKQ7EPw+zTA3BQrw32ZpDoxpGNLkDnkuivIQDI2VY\nNWk32ceSg/ZlxV1lcl0tUX7rAIIZGKjE23tgxymZO8gvlO8aF33ievdK9zxm\nOaskphP3M3opSv4mpgpQbvYN2Y+O14oN/+pbAOkrib4KSfJGMD6ZDwdq0VLQ\nqMxlUZD5qlYFFDfcOzSq0HmN/oX0lW3WJtu53cAFvqBXdm1W2yh2Vbq+PqCd\nhnWg8bsQvU7Iu56kMoEVEtTjB97uoKKKWoMmbhArg2QdWLaUFsEtUwccmcKd\nl2roLisoFmbp1stt7oXgQFFBKh5p9UwqUx8MOozRTsYUiyuXkZ4hn4AnPlAI\n/UWgKsLIrxXWQP1+rn5UelTIyHMgIXnTUrjb7i6LGe8B208F4XQlgcbtrWU7\nSyccxR1Sk3Im/xfL1ihAYY+vworONkWDXG+PNRERVoa+I8tcVu7WpeUR2IAG\n5hgGYtwxrVK93mkgob0nBkg3jiPDczByBLIxtL/WSkCcDPHJwJZZB6sIaGEU\nwyi1QBBzuny6HbEcjEHONkyJNRx4siqKFSiLnLEThTTKU4i1ifI3ucQqEBjx\nxyoyGTrQW+Ek2EVaF0E3/dt2X12yzPS2d5+6wgxx8GjsOpEWlLS6aufEDglo\nbYBM\r\n=tXvZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFmDqP1y0yGtNJfnaermQ/sCeTK33Oe5RejfIe5STzYhAiEA1PfPb9MicnQqhbHFKaR9GEoEmwVrxTzqzzlPfZSJFQI="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.2.0_1543863641645_0.8543619166912537"}, "_hasShrinkwrap": false}, "7.7.4": {"name": "@babel/plugin-syntax-class-properties", "version": "7.7.4", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_id": "@babel/plugin-syntax-class-properties@7.7.4", "_nodeVersion": "13.1.0", "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "dist": {"integrity": "sha512-JH3v5ZOeKT0qqdJ9BeBcZTFQiJOMax8RopSr1bH6ASkZKo2qWsvBML7W1mp89sszBRDBBRO8snqcByGdrMTdMg==", "shasum": "6048c129ea908a432a1ff85f1dc794dc62ddaa5e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.7.4.tgz", "fileCount": 4, "unpackedSize": 2612, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/jCRA9TVsSAnZWagAAO5cP/iTnNQ4wU3uwbsToYmz5\nLT/kxaQ6K9SmPihVl7HQA3c2m4mvNisv0UYcc3absewgEF5E903IAlPVYzzS\nBhllO9lNhFrdFQtiQYw0+fEgaAqeKxqxmqlkvSA2OIKIUHnsbO4vSVnug0YW\nAv9IVC+vLFanmz3Ck7OMV6rdSc+Z1kaEzxCMTeFPyY4P4RM8Bc8R0kT5gSGx\nTJK/mOMjZeibqSwk6+E/vSl9OISr7R2bYAE0JH1zQVWd270uYX74g0A2NnR4\nJdlARHrcEkCi4QlJajMbPQNlZqs6BR98ZeaCR3BbCczHXRUQV8Ub+r/h1CaR\n4Dq9XUvQa0Pg6CGZeINKXoVjjdI793AXYE9bFiyflffUzOcEbA8j4ZWbcoYE\nHFj2e2uCeIL6QAN+UGP6roANasT6EYndpevrePZ4PA4SvXqq81g0KZtbnTNt\nUQ8t/YjczpMsle5pyz/04XvM3x5xfthgFPPz5tlTmpgawUNg6lPSQlScBdD/\nw4+hJ9dU46OI9jGutFjXHRQNmxT/RcYT7Gsro9N2hgh3m6QTovn3pLnhMs4r\n73bRGzg8rRcMwYCM79EECll0FX+yCrISl9lwqs3csEUn8nbbmxUz2UGeNRTz\nHF3fkJYUeW/EirdJEORAembooboeYzpRa+ynxWNpvGuv+HgyZffbKqqPR32K\n59sT\r\n=JemI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAXX1wKUMLWltoHTPyz68Wrt5T1VWBd4UGxdg5laksvLAiBkTd/bB7Ajn3JNM1jnmPIIIXXVznkASvDVF8KW+qrNfA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.7.4_1574465506881_0.5552907591684182"}, "_hasShrinkwrap": false}, "7.8.0": {"name": "@babel/plugin-syntax-class-properties", "version": "7.8.0", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "type": "commonjs", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0"}, "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_id": "@babel/plugin-syntax-class-properties@7.8.0", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"integrity": "sha512-Ygw7R0yM6MjoqPFG4tlX2/p0ZlPAF7mvSrVASsvsRPi5Exad0quQ5tRPFTQI/2baPHlfeGZp7Qv5SMaWk3Lpyg==", "shasum": "a46f3a088d96715bd498349aad7a7350f773d30f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.8.0.tgz", "fileCount": 4, "unpackedSize": 2634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVJCRA9TVsSAnZWagAAF7EP/2hW6Wrwhgf5CXz2dQmK\ncJC41lW3KgrhEzUfa1mPBs8LUiruTZc338AIHHchUiTfSaprgLLh2gi0Y0j0\nkG4v4zgngA08nvUKGEXrZTs505xB4dODsq4RlnW6FjPLGbUB4H7yBlVylfHj\n4mq88aicL0zBHKA3kWoTVZVnpBjYQPytIezM9zamcRKi95LlX2sUXCpWg/d9\nO83mWOJ+15D8DSqUTqR9AsD4KH/V0VjsebbzEnDljacSJEdAA8jcnUbwCgrS\nzdrkJ+X/0j5sTKXSJySS0F9RAxDPFFPdmpFTfeQbR1/De87DYNdrh8n4qf6r\nNOAFbZvOBvxz3GJEIxBYpPM3Hlqda3cg2fCM8ndJ2G//D4AvdgElA7jkERIq\nqSJnGiGoKryc30SlE4UW3WyAAz7EsyhDb8iUSYtaBNJM1gsnSAn72pFHm0ty\nNZGJrGUEEjtRG3hbK8TTNfyi2HZiOirifGpjiZEEskYviNVQ0CcE2Qb9lMs+\n0svVvOUSUaFI4agTKQ6LP5oYi61MWAaPcfRGB1TQ6klaYl9psDap6xGcsLwD\nh9AR+bxeP7lk60bf6dcFPZV3UuTlhz9FX9Q4ppRh0aC5Bpm0uxFHQATdTRh1\nVx49Tp1i7TEfyxLHNEmkjGqStH3nRaX/tgDiX5nE7zCvIunJw1IoCbkeN0Rm\n20G0\r\n=J/H1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID2rJl0JMbnbt0p5KrNaKFC5pK5S1D8U6sdQGZjmuDvvAiBi2TVwEUr4PHU+O4qfJwUDdn+hsbpzUC9xa1RW9Q5b2g=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.8.0_1578788169561_0.7742601011646986"}, "_hasShrinkwrap": false}, "7.8.3": {"name": "@babel/plugin-syntax-class-properties", "version": "7.8.3", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_id": "@babel/plugin-syntax-class-properties@7.8.3", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"integrity": "sha512-UcAyQWg2bAN647Q+O811tG9MrJ38Z10jjhQdKNAL8fsyPzE3cCN/uT+f55cFVY4aGO4jqJAvmqsuY3GQDwAoXg==", "shasum": "6cb933a8872c8d359bfde69bbeaae5162fd1e8f7", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.8.3.tgz", "fileCount": 4, "unpackedSize": 2612, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOP5CRA9TVsSAnZWagAAyqMP+wYANBP2A6v+x0B+f1ah\nyJZym2qL6p25FvD5FsBdNLN+EN2p8jZ8ERMjIPlrAm9lyCFowmY2iZT0Q3Cr\nOvUmc5jUvQZ+JeG4OmXOqsnHH74AkSqdLdlxqQy20u5WyDecddElfnVS0Dfd\nxwl6w4jYukJ/J3lPUm8grp2tpmHQQiKq52umCqkA4gbBYW4EyLDAWw01Qhpz\nlREM8MIALhQULD+Sfx/WCRA68pX+e2AdABNiWW7iNB7rhWVTa8SZqVPgrvPw\nf2nJk+b/AS64DF6L68DIjnbZyfpkkMJ3P7Regd7DtmD38j/ixRi53QYZeoUT\n3RuI7ffVwWPVvnNiFcRVuOgoF3Z1PL4XnM51tPFm7wLMUVHBrgjBnMr7zHAp\nYCPji9uY4KHwbdE44wA11IQUiC5WWOrKO41W5iQh7MGTdRYxi9jLp77EsSaK\ntNOznDUX+8uIWDJwIDHJJkgcjFpwpZYwHFUy1opQ9SMd+iIGRDGPxTqUY5gv\nWKkM7WCav8dQJ6j+hclygrXVnfX2/fUQ2Ai3j2ztc3uNgNWqBfIZmot+lAum\nzSYHd0XJXK0tYzwWSY5W59XtRXyGaYfDcuHDxbNo8M0g0Tb834atJ8ngRW2O\nW15g0R/ZuGJITmfaIUX2fB4U3DukW22tIueD3j2aJnzDpWqQ9cDvsF9U7mp5\nxmkz\r\n=26XO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqI/gfpAuqsuVN8QngME4t9/hnWTJ8DlUj1UuBuoZ5pgIhAMKNnv5gyFCsE53blvpaZpzRD2IZPvuXb0YQ0KBHctbi"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.8.3_1578951673383_0.9712414803698004"}, "_hasShrinkwrap": false}, "7.10.1": {"name": "@babel/plugin-syntax-class-properties", "version": "7.10.1", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.1"}, "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/plugin-syntax-class-properties@7.10.1", "_nodeVersion": "12.16.3", "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "dist": {"integrity": "sha512-Gf2Yx/iRs1JREDtVZ56OrjjgFHCaldpTnuy9BHla10qyVT3YkIIGEtoDWhyop0ksu1GvNjHIoYRBqm3zoR1jyQ==", "shasum": "d5bc0645913df5b17ad7eda0fa2308330bde34c5", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.10.1.tgz", "fileCount": 4, "unpackedSize": 2663, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSGCRA9TVsSAnZWagAAT7AQAJmnoJ5ftd/iJZ/LwQee\nRQDCWKdf5G+clFCDTDVj9O7jwy/R+NgOjSjOrSNjxRyoXJUCr/YwCdT+tPoO\nCWsR4+4Jfa7a1HwMXBtxQPVjCfdxofWHfhRcXmo5Q0lTGROda38QI1+VTpGq\nzMALNCvG0pcerO+ERMr8wDrppKXIms1pav5l0LAc70/Zn1B1Q+lvsy9WWt/O\ngWZEcWIKUXUKEjc69zeCbIrTeSsw59JZiYwz8d3pxYLG4306moXWftU7oaXx\ncXtXL+j0P3TPYYmi/J/c0OrLvjihRYJpssdy251Cc8re+HpCL93Idwf76fHE\nFsvDYqyqV2hNUDU2d3nyYvTEvC16X3kYkeUu75VvOiULjc5Fg81/rW3clNaZ\nCO5NwujrPWreYktkb0nJdIzs5JFpxJvcbzLnCyD8kAkUzxco8lFmjiikAB1I\nKUVCiX4hvYHbRr8TK5PwJa8RoeqX09IU1h5uclKP8Dqkn89saUnf4PeBdv1q\nkKcgeW+zYRCI2kg2xOXaj14CnUbL9aYpG6RYEmk3xUM+sCUQIvrgOgFxrPqx\nem36X+IMzwKbuf4DwnMaX7QJzggL1Phiiu4EY7tQE9iARdEjcJX1/+6NCZgf\n4DNMeQou0q6c4MKFr0QA0gd+M5LI0XsGy+h/r/Pc/XB0HQ2677ERYII85l8q\nRimz\r\n=V41l\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDrYHWdxM6zogh2lm9k80Pkw5f3XTtsV3Rn+cEsW1ed2AiEA9aOrtrrvHaLRAZo8JiYbl01mZqUiFRYqxKPBotkdJKc="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.10.1_1590617221409_0.32744027946749044"}, "_hasShrinkwrap": false}, "7.10.4": {"name": "@babel/plugin-syntax-class-properties", "version": "7.10.4", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/plugin-syntax-class-properties@7.10.4", "_nodeVersion": "14.4.0", "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "dist": {"integrity": "sha512-GCSBF7iUle6rNugfURwNmCGG3Z/2+opxAMLs1nND4bhEG5PuxTIggDBoeYYSujAlLtsupzOHYJQgPS3pivwXIA==", "shasum": "6644e6a0baa55a61f9e3231f6c9eeb6ee46c124c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.10.4.tgz", "fileCount": 4, "unpackedSize": 2663, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zoLCRA9TVsSAnZWagAAG6IP/0+I6EKRNL+T0FJaEZ+r\n0KZEQuRBoJmyJKTLnuky523JmpF6zvFBHp0k0ZslqwE/QtMpo535OsnZUs8R\n2x70yDdbZXuK1Bn/uIxQ6JaM1RmG+4j79elaibF/7dY4/s5t/vWBAiBQXX9Y\niDbhtM14yXqXZJ8Cc/8NVPjzZNs3FFbhZGsuCUllydrFo1jQpZV7IJEw52Gr\nyOSnzg3q3PZEVZsLe6FykD96ApgzuRfVEwjIZAnsOu7vGZT77m14k+S0aj4H\nW4FipIuWOccZYzUeVQI9LaAc7E2axw0dULeQwA9pu6oB9Lm40fY1z6RllRVf\nVCr2Gi0wZ1nXtrEjlTUIICmmOXliyZSDHhy29terCWT81VJzXwOPlZcSSozY\n6ohU1Zmil4UNDKGu7a51+lgZEQXEKquz43dqo9tJn60VVIbGHKZi0rLLovS8\nCbZTaAWiNZkM752+mScPCX40JImSwe1z0cd/ncvYZoGN1jRRoJ0n+GWxPhyB\nBn5NI39oxYxM2DJoYxHNzdUxGBShFIF3df3JsBSDOJmvI3QCwdYoXuV6ckW1\nA/S7yel48e2q9RfslK4y7YLrBPsNcsFKFhD5MPyYtPSdudCIapGfHx5QD6AB\ngKhrwoXhtl9SWaaPVu33Tt2BpH9Bbe/JU4vXGPhahbxq4X8KCruYPW9dJwEB\n4DGx\r\n=zi96\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCdW3dURuuJe1uNFJqJr2w71UfF/Q4HkHzo2JHDd0ubVAIgf60uxXcG+YV4+D82jXaYq4UBCfzFYcMnksU/8xrGApQ="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "jlhwung"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.10.4_1593522699155_0.3402108078405255"}, "_hasShrinkwrap": false}, "7.12.1": {"name": "@babel/plugin-syntax-class-properties", "version": "7.12.1", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.12.1"}, "_id": "@babel/plugin-syntax-class-properties@7.12.1", "dist": {"shasum": "bcb297c5366e79bebadef509549cd93b04f19978", "integrity": "sha512-U40A76x5gTwmESz+qiqssqmeEsKvcSyvtgktrm0uzcARAmM9I1jR221f6Oq+GmHrcD+LvZDag1UTOTe2fL3TeA==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.1.tgz", "fileCount": 4, "unpackedSize": 2605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM+jCRA9TVsSAnZWagAAcSYP/j4MPk+XV4Ru5+c6zzt4\nf6tdHNKYm0lr8kYVv1R1BWCncg2+jxMQvAeT/3eaCp0CZldwuwTnNiGcTbNQ\nvKqHGx6V0B+VogaC3x230J4/eFPov9SH8jzfD4hDgYXF3Zu58krWvD1HpJbN\n3uDWMXtziDk+S195uc3xUNXkkJToJpnNK5ZS2cZxPBEFc3rlMxs4aztEbs0R\n5hsQLyPjN9YmKQ2Vg0hLiLKK64uCpt9EYm/Dkb0Nu3aaltReyyzZ7XxcmpnU\n5aFIJsa3RcOhk1Cw5oQIRHAbsL5ZtLrR8ysLmBXh2zCaX9VolBqWmApf3Uue\neVlL4NGVoa5QClXaimFe3WOWcVGOuKFYqR8MmquX53WCwLbIEoANBvF0KTra\nmbGv26xHW27Nn4ciqJxN2YU+ht9GyvVVdud7XEUakzaT8liY/AvQKaSZmnN6\nJO9M9kqr+OJCaLrAESe9nCI2XhzWL8/ejidM9jpvXQfoM7LXYcTlzzfe7idK\nIHIipIlxuQ19H1MDqOfpVNSieok4akEbBCfRWzrXiYIFxjGvn8yoIsx2XSx4\nFgswqIfGOL1e3vMt9TV73Y/KdoFjvaOK+Z/aAjATmyn+2IX1ZxWLF0q1iuLE\nbUjTjWG3fQu7Myve5ZZzIDk8G/T6a6ioRLBN5EEO9+BCldVkJ+rYeFrgDh8O\nLiF8\r\n=IONi\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDxJ6K+x+njnsDy3rYhXBUF+BAmhzUx3yoftrmUjrUJ4QIgRf6y7XfDWGlJx+mGAuwOCWqhg8O+tuAxjQo+sTQ+kOU="}]}, "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.12.1_1602801570734_0.935732011284335"}, "_hasShrinkwrap": false}, "7.12.13": {"name": "@babel/plugin-syntax-class-properties", "version": "7.12.13", "description": "Allow parsing of class properties", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-class-properties"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-class-properties", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.12.13"}, "_id": "@babel/plugin-syntax-class-properties@7.12.13", "dist": {"shasum": "b5c987274c4a3a82b89714796931a6b53544ae10", "integrity": "sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz", "fileCount": 4, "unpackedSize": 2682, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfgGCRA9TVsSAnZWagAAqmAQAIDzB7FsViTAphdwN/lO\nPwYzhxs+bEjo+n9OaAhhmAjrz/LB8Bn3AnFE09fH16CHC/BPe6jlsDUVezK5\nogHteuPLa1GNGStZBTqRFQXjmUtXT0Q0i7p9fLVMA2LAGvrtPvwj1J1LYVq4\niO9BDLsVE6+bhGwFCw0/ic+v8wK43ZZoL3MfGJSbdvc71ImjeHlzuZRLtbtl\njrM4Doi3OSfg5VPSDjDgWlZIrQn5NdIaE23kNx1c77FSAJ1VYg2a2sSLL0q9\nYfAQd9cjBop0JDc6Kf20o30rGoxEmF6jK2Af4HV1LrRn0nnVXcPWOzGKVSN/\nlHxNe1SXVd+yedh9mySJZIPVNAyLlEVyadqGQymuL9/RikCu+lO4mriwvNnU\nv/AxCpllbBKcIcRcIFtIvTUD5FN/jLn72Ovq03E5UMgwMmeJX1DmC3CFvx4I\n9/yqFAtGeP4Rqe1m2QuiAVeqt8reaocDHPyXiS4ukNio9e2nUq73pf9LQtNT\nL2i9KaYyWj8Z/mFqnyLMo/X/NcHXZA+UEfCtDRVkcOIxhhdfoE/X3G31RFdh\nvE+1u1R7+LFK/vkuM7yKu7O2cuCZVMt4rHjyW3wZo6JHMTxBirg0SmBa+/63\n1clMyLOs+3ISmsGd4MfNY7qqJK7J6tERIyflo8oL1DTMl26V8CK9GcLU8gLi\nFcXf\r\n=RdzQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICZzEYDIaur/V98RgClz62qwt+ziv3u6fQ5F89grDLCgAiEAyTtO8E6wrjbvDVnbtRdGodMwDzLiwJ/cVL2IQjYTdmI="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-properties_7.12.13_1612314630451_0.6856082719755787"}, "_hasShrinkwrap": false}}, "readme": "", "maintainers": [{"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}, {"email": "<EMAIL>", "name": "jlhwung"}], "time": {"modified": "2022-06-12T15:00:58.942Z", "created": "2017-10-30T18:34:10.824Z", "7.0.0-beta.4": "2017-10-30T18:34:10.824Z", "7.0.0-beta.5": "2017-10-30T20:55:56.784Z", "7.0.0-beta.31": "2017-11-03T20:03:09.956Z", "7.0.0-beta.32": "2017-11-12T13:32:59.502Z", "7.0.0-beta.33": "2017-12-01T14:17:06.213Z", "7.0.0-beta.34": "2017-12-02T14:39:00.259Z", "7.0.0-beta.35": "2017-12-14T21:47:31.401Z", "7.0.0-beta.36": "2017-12-25T19:04:16.497Z", "7.0.0-beta.37": "2018-01-08T16:02:18.984Z", "7.0.0-beta.38": "2018-01-17T16:31:37.009Z", "7.0.0-beta.39": "2018-01-30T20:27:23.511Z", "7.0.0-beta.40": "2018-02-12T16:36:45.489Z", "7.0.0-beta.41": "2018-03-14T16:25:49.106Z", "7.0.0-beta.42": "2018-03-15T20:50:24.181Z", "7.0.0-beta.43": "2018-04-02T16:48:12.264Z", "7.0.0-beta.44": "2018-04-02T22:19:53.812Z", "7.0.0-beta.45": "2018-04-23T01:56:02.554Z", "7.0.0-beta.46": "2018-04-23T04:30:27.764Z", "7.0.0-beta.47": "2018-05-15T00:08:02.715Z", "7.0.0-beta.48": "2018-05-24T19:21:25.772Z", "7.0.0-beta.49": "2018-05-25T16:00:58.064Z", "7.0.0-beta.50": "2018-06-12T19:47:00.492Z", "7.0.0-beta.51": "2018-06-12T21:19:28.370Z", "7.0.0-beta.52": "2018-07-06T00:59:16.055Z", "7.0.0-beta.53": "2018-07-11T13:40:05.794Z", "7.0.0-beta.54": "2018-07-16T17:59:56.904Z", "7.0.0-beta.55": "2018-07-28T22:06:59.939Z", "7.0.0-beta.56": "2018-08-04T01:04:03.984Z", "7.0.0-rc.0": "2018-08-09T15:57:20.620Z", "7.0.0-rc.1": "2018-08-09T20:07:01.436Z", "7.0.0-rc.2": "2018-08-21T19:23:02.293Z", "7.0.0-rc.3": "2018-08-24T18:06:59.533Z", "7.0.0-rc.4": "2018-08-27T16:43:11.075Z", "7.0.0": "2018-08-27T21:42:21.802Z", "7.2.0": "2018-12-03T19:00:41.768Z", "7.7.4": "2019-11-22T23:31:47.024Z", "7.8.0": "2020-01-12T00:16:09.671Z", "7.8.3": "2020-01-13T21:41:13.496Z", "7.10.1": "2020-05-27T22:07:01.911Z", "7.10.4": "2020-06-30T13:11:39.311Z", "7.12.1": "2020-10-15T22:39:30.856Z", "7.12.13": "2021-02-03T01:10:30.638Z"}, "keywords": ["babel-plugin"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-class-properties"}, "license": "MIT", "readmeFilename": "", "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-class-properties"}