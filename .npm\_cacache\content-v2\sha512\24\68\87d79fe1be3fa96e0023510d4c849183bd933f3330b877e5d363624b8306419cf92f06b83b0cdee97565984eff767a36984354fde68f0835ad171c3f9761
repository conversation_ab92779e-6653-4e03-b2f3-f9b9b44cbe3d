{"_id": "yallist", "_rev": "26-8f1070118cfbb87bb1467414ae67fc74", "name": "yallist", "description": "Yet Another Linked List", "dist-tags": {"latest": "5.0.0"}, "versions": {"1.0.0": {"name": "yallist", "version": "1.0.0", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^2.3.2"}, "scripts": {"test": "tap test/*.js --cov"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "8d387d3d66afcd9045a98c86f320016c4e1fee9c", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@1.0.0", "_shasum": "478c1272847a661519938e3a43b98d00faa12f8a", "_from": ".", "_npmVersion": "3.3.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "478c1272847a661519938e3a43b98d00faa12f8a", "tarball": "https://registry.npmjs.org/yallist/-/yallist-1.0.0.tgz", "integrity": "sha512-KNi+WzRCAoXcBT3z+UFoYSubPzBDvm/Jh5GlLaaTdvo/NcJn/0DokB8Oci5xc6N5bVnZoWjS9hsfxJ5Wn35fXA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCRDYHf9TOtha1ACYUtC0CLT2CeNvVyFB58icbW2SppUgIhAMHzGjvYvzK8S3b7H4OVqkZkOoQh7gWaVTPGZymPqvsb"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}]}, "1.0.1": {"name": "yallist", "version": "1.0.1", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^2.3.2"}, "scripts": {"test": "tap test/*.js --cov"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "071c8f329cb87dd996b360955a1a1a5199d1381a", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@1.0.1", "_shasum": "daa8df3166c72bc78c3d307ea8195c1c14a1430c", "_from": ".", "_npmVersion": "3.3.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "daa8df3166c72bc78c3d307ea8195c1c14a1430c", "tarball": "https://registry.npmjs.org/yallist/-/yallist-1.0.1.tgz", "integrity": "sha512-34n0AanmymrvNSUcdwQAQ1RmvRoPkSKHmXR70ln4+2L3JZaKQBXyhxXUY7bRIqPKqb5jgncxFRbdCtTJqCYWDg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAr1D4CJQyK+gfuNAN3UHF1BHk6c7ZxmpnXkDCorUDBUAiEA9035dEzDkHpmMF8IHmB7tyHYOws1x5U6hCef5SrVV4o="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}]}, "1.0.2": {"name": "yallist", "version": "1.0.2", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^2.3.2"}, "scripts": {"test": "tap test/*.js --cov"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "56757373ff05e416992e323184946019db198759", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@1.0.2", "_shasum": "2805e1ba9e78bac506a19c4e028d7da6d031adef", "_from": ".", "_npmVersion": "3.3.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "2805e1ba9e78bac506a19c4e028d7da6d031adef", "tarball": "https://registry.npmjs.org/yallist/-/yallist-1.0.2.tgz", "integrity": "sha512-m81cHiLhQWWHr9ewXLJ+zvFnk41LRiDC2+raYPdYRW7ohwC1tWz6xmfdFOxz3AaXQrIjk1aFSsSygfn7V0nCXg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC/oRG9jyuZnCke/gJy2TiHom6IJ/mfvrRDSnIK10T8mAiBRk2QvcedotPbSM26b5tc30Fwvxi9v2rALGSoSTWCFxg=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}]}, "1.1.0": {"name": "yallist", "version": "1.1.0", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^2.3.2"}, "scripts": {"test": "tap test/*.js --cov"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "2f324037e84a1ffb140d485e3b18f7e915937132", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@1.1.0", "_shasum": "f1878f30e2955a6da83ac6f179782344cd6ea4fe", "_from": ".", "_npmVersion": "3.3.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "f1878f30e2955a6da83ac6f179782344cd6ea4fe", "tarball": "https://registry.npmjs.org/yallist/-/yallist-1.1.0.tgz", "integrity": "sha512-2pmuDf+Bn8FjaPPavD52OKuG+8rsSYkgDTHNXI6+YwRIUGNz7//deuhhy/3ON1jKvc7KExp0dQ8iNlHc6DhKog==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICO1sZkl5xCtUy30s4v7Pll42g0YXguu6oQitnx8oj9lAiEA+Skfkse9DYq/IJmuNjVMWhRb+WgHwp1v4Eb96tv3JrY="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}]}, "2.0.0": {"name": "yallist", "version": "2.0.0", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^2.3.2"}, "scripts": {"test": "tap test/*.js --cov"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "702eaba87deefa9f8fc2f8e36cb225bc2141fdc3", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@2.0.0", "_shasum": "306c543835f09ee1a4cb23b7bce9ab341c91cdd4", "_from": ".", "_npmVersion": "3.3.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "306c543835f09ee1a4cb23b7bce9ab341c91cdd4", "tarball": "https://registry.npmjs.org/yallist/-/yallist-2.0.0.tgz", "integrity": "sha512-l446SSVnGyIspyBekF2U4g2cUMLwBJLu3IvXvqDSwmJFWtAgQ9B8CtHArNluFgI7nPm5+EEbH12fwPIwSXPQyg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDXyw1IJYcIEi0lBOPW+uRSkwhtma4RqrGUnLdTtYgHMwIhAM29+pFrDOKyk4Ghzz/+FvC+qA/8QlaBX6yXH+CBlEGr"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}]}, "2.0.1": {"name": "yallist", "version": "2.0.1", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"standard": "^5.4.1", "tap": "^2.3.2"}, "scripts": {"test": "tap test/*.js --cov", "posttest": "standard -F *.js test/*.js", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "64016921aae7ec4bd6d2d3e8c9f49d02529d9c3c", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@2.0.1", "_shasum": "6c11279640abd6ed0a86dd32ed56b03caa9df4f1", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "6c11279640abd6ed0a86dd32ed56b03caa9df4f1", "tarball": "https://registry.npmjs.org/yallist/-/yallist-2.0.1.tgz", "integrity": "sha512-nvYoTNlZ+T0MBdHpdrJMyI1AY/s2NeSqukeXuwiaTPZjbkzsg2M9f0OmmPwyIr1DFu88Xd7jzUj2+SEotaEuog==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDUwkhQe6Di3z+78ztRX6Nr2LnprmCB088SoTKxLuMvrwIhAMjYpv9QmSd34QF7H+IZ1O9phy6UpgDcBDbBgomeOWZV"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/yallist-2.0.1.tgz_1489262957872_0.8242232573684305"}}, "2.1.0": {"name": "yallist", "version": "2.1.0", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "files": ["yallist.js", "iterator.js"], "dependencies": {}, "devDependencies": {"tap": "^10.3.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "ce5fe86dc1e550835941d33ce134e0632e7b6287", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@2.1.0", "_shasum": "3a0f3b45f42cb60f822c92f69ade2bb88beb1ae0", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "3a0f3b45f42cb60f822c92f69ade2bb88beb1ae0", "tarball": "https://registry.npmjs.org/yallist/-/yallist-2.1.0.tgz", "integrity": "sha512-bw4zKj62wAeQuEpEdtr5a8EWAhVdmCqzogKTo/3ZtdPCeu1yKOM+/DzzSdcs4BqOcovXxff8W/CAaNzp5qSzXw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGpqpbjIg4BLOmc3khatdAkheLMG+X3Z068fOrvV5JyXAiEA5uXR4anxdGjDT0jAdlap5EyN9aNb8B+KpFi0ewCP3V0="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/yallist-2.1.0.tgz_1489370626750_0.8869737309869379"}}, "2.1.1": {"name": "yallist", "version": "2.1.1", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "files": ["yallist.js", "iterator.js"], "dependencies": {}, "devDependencies": {"tap": "^10.3.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "9493a5b7460dd38dc3d3d488c92eae8a395efce3", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@2.1.1", "_shasum": "08309c7044b1761d5e1591dc12c67629271b6ac3", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "08309c7044b1761d5e1591dc12c67629271b6ac3", "tarball": "https://registry.npmjs.org/yallist/-/yallist-2.1.1.tgz", "integrity": "sha512-wJXW2iOHgovkGFfG0dBkFufZ+QbrPRe4H7f2p4CSIOgmGB+32iuMV+5mjQUi+KOYEvukoQTxz+wYvraUdCNgfw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBCrW5PF20efeh01J5khErAd/4dCJJCIuxcRD8hwYabFAiEA1zaMlKFMUO7YGkBXBwSCu2MJB5hGeistgxByplE4vyE="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/yallist-2.1.1.tgz_1489424134677_0.7357181606348604"}}, "3.0.0": {"name": "yallist", "version": "3.0.0", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "files": ["yallist.js", "iterator.js"], "dependencies": {}, "devDependencies": {"tap": "^10.3.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "68fb6bd20e99f78dcd8cc299ebe8ba98fc726ff0", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@3.0.0", "_shasum": "717369f9df727e5a03cc69c9ca94d7d94579f58c", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "717369f9df727e5a03cc69c9ca94d7d94579f58c", "tarball": "https://registry.npmjs.org/yallist/-/yallist-3.0.0.tgz", "integrity": "sha512-e8<PERSON>sK5rgly0muU1LeKbPMpiJoLWDEfe5+hanR9wljH7NJqmQdtKIYFQkxdLENYpl59mqg5g42bYS2tX59Gu4w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAsp+fD/c+kidvGrfB+BAVu47PHdsXlGmP3Rb3fdomFMAiAajJpSDsmduiON+nuK10DKgLMs0bgL1qJjwnhpv9jxiw=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/yallist-3.0.0.tgz_1489424212365_0.014271438587456942"}}, "3.0.1": {"name": "yallist", "version": "3.0.1", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "files": ["yallist.js", "iterator.js"], "dependencies": {}, "devDependencies": {"tap": "^10.3.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "a0c17647ac41f86adb875af76d2f522462785ffb", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@3.0.1", "_shasum": "4affa89763ea5c2aeb9e2ed98387ceada34f4c4d", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "4affa89763ea5c2aeb9e2ed98387ceada34f4c4d", "tarball": "https://registry.npmjs.org/yallist/-/yallist-3.0.1.tgz", "integrity": "sha512-XZPrO9OnL1B6IOPVEgcH6Ex3pOd6KMqtGlXjUSr/VR5nSWOYujm5z5y1iVxoVt9hrusLrKiWzGyqpQFILhtGIw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD+cWLJdcGXmhbWhS5XQ0NpLE6Uxmjl86GRV76lq9ufpgIhAJ+F2PQzm2mUH14OIBcRuhizOo3LqRWqBWoWTkoLZNck"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/yallist-3.0.1.tgz_1489443153380_0.10298502212390304"}}, "2.1.2": {"name": "yallist", "version": "2.1.2", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "files": ["yallist.js", "iterator.js"], "dependencies": {}, "devDependencies": {"tap": "^10.3.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "566cd4cd1e2ce57ffa84e295981cd9aa72319391", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@2.1.2", "_shasum": "1c11f9218f076089a47dd512f93c6699a6a81d52", "_from": ".", "_npmVersion": "4.3.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "1c11f9218f076089a47dd512f93c6699a6a81d52", "tarball": "https://registry.npmjs.org/yallist/-/yallist-2.1.2.tgz", "integrity": "sha512-ncTzHV7NvsQZkYe1DW7cbDLm0YpzHmZF5r/iyP3ZnQtMiJ+pjzisCiMNI+Sj+xQF5pXhSHxSB3uDbsBTzY/c2A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC+TNJuIqGHzo8Bvfepcs0WQiDozvoOqZnQRPZlvhcbvAiEAv+4EA2UZc8xlaE//S6Kb2OOZhP7gffW+kKojB/TbVKo="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/yallist-2.1.2.tgz_1489443365033_0.47744474792853"}}, "3.0.2": {"name": "yallist", "version": "3.0.2", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "files": ["yallist.js", "iterator.js"], "dependencies": {}, "devDependencies": {"tap": "^10.3.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "d692d0f974934858b76c3b1f7f0973d0450c5c87", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@3.0.2", "_shasum": "8452b4bb7e83c7c188d8041c1a837c773d6d8bb9", "_from": ".", "_npmVersion": "4.5.0", "_nodeVersion": "8.0.0-pre", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "8452b4bb7e83c7c188d8041c1a837c773d6d8bb9", "tarball": "https://registry.npmjs.org/yallist/-/yallist-3.0.2.tgz", "integrity": "sha512-U+iKQ8rDYMRmvEpvDUIWZ3CtM9/imlAc+c1yJ7YV0vu+HNtP82sAkXzuDXPLkIPoLZohnXFSs9wf2E17xk5yZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFNGjxMhXXtzoa07tErhxxrUkrYuIq0l4omD8xmOT7OIAiEA2OtStH/5XJXBacd6Rx9xPByqKGJKeDHnIqyVNFWmJAI="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/yallist-3.0.2.tgz_1493150377027_0.22160566318780184"}}, "3.0.3": {"name": "yallist", "version": "3.0.3", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^12.1.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "47ab1ce288032985c90ee568681b35ad8978be41", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@3.0.3", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-S+Zk8DEWE6oKpV+vI3qWkaK+jSbIK86pCwe2IF/xwIpQ8jEuxpw9NyaGjmp9+BoJv5FV2piqCDcoCtStppiq2A==", "shasum": "b4b049e314be545e3ce802236d6cd22cd91c3de9", "tarball": "https://registry.npmjs.org/yallist/-/yallist-3.0.3.tgz", "fileCount": 5, "unpackedSize": 13744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb9ei9CRA9TVsSAnZWagAA9zIP/ijnxiQ7otRl2x3s1hy3\nzi7QbMsTJzIafuxtZNPxIctL9ePQkyl5ccfvAGamlNDJKaWZZocHfCghCaCw\n//TLvHHISid3zVbs6iYiy/xGP45UFbRf+4/qEmBE5JVV9ZHyjwaNDwf19HQ5\n3Lcfofdwo3kkR/xIWsYA2S2vmkdwviPYPWsPMwyQIGmCnEV61WUuuvedXA5m\nBd5W6l6ofe8qGQPQVaniEFaqViUZlNxSyiwSiKcUNve0sWvT0BVr9QmvpDFi\nmcGrYbcUhMNtxexdUDdJ4QB0BNLEPymm2iCB2iaq+WZiZcCShDFl1M9PJKdB\nNQsKddC1xTfWfdtIbtw0rQDwBOYJpw5sFFnurCaAz2/JNEaYZSqxwzwgud0s\nh7/AaTV/1Knpk0qEXskogYMej8kO4AIcel4iFINxlIy+5yDWHcThEm2aMDNZ\n63wRojSH/crwKdFYAwVxCUTRQGcLre/0NqNIL9F6cPZI/JmzzG+nfp6UEf7K\n6E8zeY7xPf6fJ2KmKyP8nq6rNY50JexlLwY30LZY9g9shaPBQ9lAUSTaIBvN\n3oDin3U7V/+Reqlw8tSOtjOUP1sqSP66Wv8Z0NfWUgv1jStv0cduD1rAvMHE\nCD2VxLsFsl1rv8uGuW7NfNkcrBtqp7oFGsoBDAeHVUCjRh5ZDrZdmbY7zabD\n2S3K\r\n=UzPp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEOR/DV23WzacyszfQhg09nw3Ny9djU1dzEAvs3ZULNTAiBQclKbl9EXuu9+CRH+TKZBQlaGZDHW/1FyzfJ/5qLnHw=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yallist_3.0.3_1542842556417_0.767908228373708"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "yallist", "version": "3.1.0", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^12.1.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "ec6f8c1551f730a593bd658413e68ba779976602", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@3.1.0", "_nodeVersion": "12.8.1", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-6gpP93MR+VOOehKbCPchro3wFZNSNmek8A2kbkOAZLIZAYx1KP/zAqwO0sOHi3xJEb+UBz8NaYt/17UNit1Q9w==", "shasum": "906cc2100972dc2625ae78f566a2577230a1d6f7", "tarball": "https://registry.npmjs.org/yallist/-/yallist-3.1.0.tgz", "fileCount": 5, "unpackedSize": 14752, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjPHZCRA9TVsSAnZWagAAuDEP/1O6n2B3Kc7y61cvdPyD\n4VMIJMEXwhGEBX4tGY1dMDlpgzzB36WYeGNrTLwDFAG3ylB4GnUuBqzULcBw\nSqPFwWaBIQr42no90xufaH8eYOaFa7CGZocQr/5DLNkWPCRJFbz5bxGzQw8A\nWE2/5JrWKFRmAP+0T4zHLix109VGcAtsVZR4hYlyZI57uQTrmH4MMXapRdeM\n3THrHhUaCb9muAFET1wPpJ2ElBYVj9yxJhLWArjtX70/+4RFCNhtT3sIQvcB\nm6l+ntmxYGEL5ctRMuO0Zp6HJmNFe9LUDzORb+yIcYMBElHTw9x8dm1Mf7bA\nwy4S/ltoYb1JQc7R/WdRzAQ/7ydQxPi58+qitwg3dRVC2g7QLRlQ/qnGo3ua\nhbhp18s4pyc3fCpVfBcrm9wRprF6+3a+jkxoCCD520+Dk39wikPywe5erXeh\nXXtmjJuelQ5NTncwPoKgSwUEUTxxu5SM4U8ZCrmLm11NeoSuFZAlGhzZ2RY7\n7Hbuxgeebem1iXINKs+2wKqz8lDqynl6B73dr+RKvo1a7n//wnryv7RdQt7I\nUnXuai2w1nyF0ujnB5eY1hWHOI98S5Wfhd4ixSO+NJXfDmSSkYEy8nt8zZ1q\nWRPb+OCU764+aaIMnou3+HeFgFabfOxQQZjyV5IOklaJr4P9Qc3ScLeW2ejF\n/s0O\r\n=uzdV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEPx4WzSbryVu0CMhLYbdkmKSsfap8EuN96AhwLjBs7QAiEArhd19awNw5mqGUcFtiUK9C9T8WuLZv6BcQJR5GtTn1E="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yallist_3.1.0_1569518041046_0.5437509782914414"}, "_hasShrinkwrap": false}, "3.1.1": {"name": "yallist", "version": "3.1.1", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^12.1.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "ab350a79eb94c80f7146662748a12b6fcd63729c", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@3.1.1", "_nodeVersion": "12.8.1", "_npmVersion": "6.12.0-next.0", "dist": {"integrity": "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==", "shasum": "dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd", "tarball": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz", "fileCount": 5, "unpackedSize": 14766, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdkmB2CRA9TVsSAnZWagAAWgoP/0j+iDVcsa3byazx3CgE\nm/aBzB3GflrXZAtMZ11Q/VfAa+gNDWDSJ2NOFmX/GxhKTm1zeyhl14WgDAZN\nE/DIF6bZkAbr01gz0k9Xa7/sMprHei90zQ6oNxmAONiiR0N1E7HRwN7Qu32C\nKMYGky23RxnXOmcX3Ij+dITQPNT/3525ehO0F7otC1b6AUwHybawJYSv7ILq\nR2x6w7X9RE9cizxLADzFM7PwXHic/20b8lgCnaA7P7qqSauVrlmdiaejmvAW\n+F6/S2QCfWGsXfLMkvJbBAjcX4+mWE90/gz/qC4+DXlDjRAJ2LCVKCcXsszk\nsz1mzU7FDfN3tp60kd0XtEQ6xuagICLxy+lit2maTcwVQSYiKko4T7aFAFr+\nYKwEsFl1+PcnSDqnuc9+6K9K7dYYqAL2/4L509joWusG6cGWdWGd0j5IGosG\naXKFW0zB54WRRbZTsNrfpy7f8wSlXZSMZyeC/n84rUKAx/HtPycBkoJTvFdD\nkIaCnm5orykEaT8JSAqqG9o5ndF4Cwba79DUzHPWZscSR43A34iWJGKb83QU\nRlNLkxo8mgAFMNdA808wr0tyh/bbm2d5q6ZCKnTmrlktPCBcsbi854ol9WAH\nir/yNg3bKNVGgvMLKm/qngACxszUnIOy0Fsi0I5eUGg392CNbgAf7myATWlY\nWXcr\r\n=gXWc\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD4S0jeQXl5F21QIAwOE5yjXumczmgvEYzxql0ZH3+R2wIgNuJW5JkGMAXm598C9Bi+MvGe319f3bz2D1eR6wgDDpg="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yallist_3.1.1_1569874038188_0.3036372642366818"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "yallist", "version": "4.0.0", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^12.1.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "gitHead": "1649cc57394b5affeca2c573943ebe3ed7d39119", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_id": "yallist@4.0.0", "_nodeVersion": "12.8.1", "_npmVersion": "6.12.0-next.0", "dist": {"integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==", "shasum": "9bb92790d9c0effec63be73519e11a35019a3a72", "tarball": "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz", "fileCount": 5, "unpackedSize": 14752, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdkmCpCRA9TVsSAnZWagAAkVgP/3zxsW6EFohtKYJOFO0J\nlkdsbK2ee8965Oy4UNyD9F+d13p47zFmrNztw+zVS+gEt9AkSpjwMomHdQAV\nJ9NBsGaG7ppjaeNNfp8CwGO2El0rILWeEr4YdhoL6wfnxa/yuQ6HeuJty097\nOZ/FP3CXPCxj8Jlos0NwjLig/Yemtt1PNNlufcDhcKX+wzftExCMir+lBBna\nq5NpnUnOQnr3+QUFqeRHXWgPh4nJLGd9NuAmu/HcrlCnu1FEd3nWrmKtEvVg\n3GZn8lK8FLteiAxS4rHaQb8QX0W9r/RGLxuvla/KXq9LfxucTStI5pLhTj3A\nZlOxZDI/v+0wXAfr3+Bcsq5sJefk0+N5h0sWc9+hl+L2yvbMiZr2e8Z67msS\n9Hl8uwvjMwtEOCTMIyBdvBVy0OCzvEHjm+2tYwd/e3VTTWzEc9zIdUirWVU/\nqcCgSMnN+8MjV+8KLdLHVjDuxQSHFlm0LcmaU5ILlKX57rKFVOVWt8ZJ3wep\nuAzfUOkmrwWR+lN58w/MIbhdwqHcMpLmgXBJEHEtbfh7Qk6yK+A/Q237YBNq\nYIIB24k7z0vcAPN82s3LxxfLxbQBzFB1mwT8NcKQCG5XJbef8V3LLJeD78iZ\n1NvS7tl8VpSUg5Vc3C5OJvmbxbFiVPZXio/nFnwwR2jNe6ukFJ/YKlSo5MrA\ny6Ty\r\n=3Dpm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCYUnufgWErBDMXe8KslrNmcyrPV1kGU2qlaK4ZeXoazwIhAKBpS5djrxLEclmS9SbzcNwhJJW8F7NXnArMOSGiGSCv"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yallist_4.0.0_1569874088837_0.19971227214997667"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "yallist", "version": "5.0.0", "description": "Yet Another Linked List", "devDependencies": {"prettier": "^3.2.5", "tap": "^18.7.2", "tshy": "^1.13.1", "typedoc": "^0.25.13"}, "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --loglevel warn --ignore-path ../../.prettierignore --cache", "typedoc": "typedoc"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "BlueOak-1.0.0", "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "type": "module", "prettier": {"semi": false, "printWidth": 70, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "engines": {"node": ">=18"}, "_id": "yallist@5.0.0", "gitHead": "a082c2dd872bb3cd8fb92359ed66605064957cd4", "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "homepage": "https://github.com/isaacs/yallist#readme", "_nodeVersion": "20.11.0", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-YgvUTfwqyc7UXVMrB+SImsVYSmTS8X/tSrtdNZMImM+n7+QTriRXyXim0mBrTXNeqzVF0KWGgHPeiyViFFrNDw==", "shasum": "00e2de443639ed0d78fd87de0d27469fbcffb533", "tarball": "https://registry.npmjs.org/yallist/-/yallist-5.0.0.tgz", "fileCount": 13, "unpackedSize": 80297, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD6e4G4ytvhoms+0w0K9/h69xTaFaNhKrrQtsadvn51egIgFDoo7A3fA8NgnwexxWK9dLURqTrJhW0gluarpuWEJhI="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/yallist_5.0.0_1712690504676_0.029932400139861093"}, "_hasShrinkwrap": false}}, "readme": "# yallist\n\nYet Another Linked List\n\nThere are many doubly-linked list implementations like it, but this\none is mine.\n\nFor when an array would be too big, and a Map can't be iterated in\nreverse order.\n\n## basic usage\n\n```js\nimport { Yallist } from 'yallist'\nvar myList = new Yallist([1, 2, 3])\nmyList.push('foo')\nmyList.unshift('bar')\n// of course pop() and shift() are there, too\nconsole.log(myList.toArray()) // ['bar', 1, 2, 3, 'foo']\nmyList.forEach(function (k) {\n  // walk the list head to tail\n})\nmyList.forEachReverse(function (k, index, list) {\n  // walk the list tail to head\n})\nvar myDoubledList = myList.map(function (k) {\n  return k + k\n})\n// now myDoubledList contains ['barbar', 2, 4, 6, 'foofoo']\n// mapReverse is also a thing\nvar myDoubledListReverse = myList.mapReverse(function (k) {\n  return k + k\n}) // ['foofoo', 6, 4, 2, 'barbar']\n\nvar reduced = myList.reduce(function (set, entry) {\n  set += entry\n  return set\n}, 'start')\nconsole.log(reduced) // 'startfoo123bar'\n```\n\n## api\n\nThe whole API is considered \"public\".\n\nFunctions with the same name as an Array method work more or less the\nsame way.\n\nThere's reverse versions of most things because that's the point.\n\n### Yallist\n\nDefault export, the class that holds and manages a list.\n\nCall it with either a forEach-able (like an array) or a set of\narguments, to initialize the list.\n\nThe Array-ish methods all act like you'd expect.  No magic length,\nthough, so if you change that it won't automatically prune or add\nempty spots.\n\n### Yallist.create(..)\n\nAlias for Yallist function.  Some people like factories.\n\n#### yallist.head\n\nThe first node in the list\n\n#### yallist.tail\n\nThe last node in the list\n\n#### yallist.length\n\nThe number of nodes in the list.  (Change this at your peril.  It is\nnot magic like Array length.)\n\n#### yallist.toArray()\n\nConvert the list to an array.\n\n#### yallist.forEach(fn, [thisp])\n\nCall a function on each item in the list.\n\n#### yallist.forEachReverse(fn, [thisp])\n\nCall a function on each item in the list, in reverse order.\n\n#### yallist.get(n)\n\nGet the data at position `n` in the list.  If you use this a lot,\nprobably better off just using an Array.\n\n#### yallist.getReverse(n)\n\nGet the data at position `n`, counting from the tail.\n\n#### yallist.map(fn, thisp)\n\nCreate a new Yallist with the result of calling the function on each\nitem.\n\n#### yallist.mapReverse(fn, thisp)\n\nSame as `map`, but in reverse.\n\n#### yallist.pop()\n\nGet the data from the list tail, and remove the tail from the list.\n\n#### yallist.push(item, ...)\n\nInsert one or more items to the tail of the list.\n\n#### yallist.reduce(fn, initialValue)\n\nLike Array.reduce.\n\n#### yallist.reduceReverse\n\nLike Array.reduce, but in reverse.\n\n#### yallist.reverse\n\nReverse the list in place.\n\n#### yallist.shift()\n\nGet the data from the list head, and remove the head from the list.\n\n#### yallist.slice([from], [to])\n\nJust like Array.slice, but returns a new Yallist.\n\n#### yallist.sliceReverse([from], [to])\n\nJust like yallist.slice, but the result is returned in reverse.\n\n#### yallist.splice(start, deleteCount, ...)\n\nLike Array.splice.\n\n#### yallist.toArray()\n\nCreate an array representation of the list.\n\n#### yallist.toArrayReverse()\n\nCreate a reversed array representation of the list.\n\n#### yallist.unshift(item, ...)\n\nInsert one or more items to the head of the list.\n\n#### yallist.unshiftNode(node)\n\nMove a Node object to the front of the list.  (That is, pull it out of\nwherever it lives, and make it the new head.)\n\nIf the node belongs to a different list, then that list will remove it\nfirst.\n\n#### yallist.pushNode(node)\n\nMove a Node object to the end of the list.  (That is, pull it out of\nwherever it lives, and make it the new tail.)\n\nIf the node belongs to a list already, then that list will remove it\nfirst.\n\n#### yallist.removeNode(node)\n\nRemove a node from the list, preserving referential integrity of head\nand tail and other nodes.\n\nWill throw an error if you try to have a list remove a node that\ndoesn't belong to it.\n\n### Yallist.Node\n\nThe class that holds the data and is actually the list.\n\nCall with `const n = new Node(value, previousNode, nextNode)`\n\nNote that if you do direct operations on Nodes themselves, it's very\neasy to get into weird states where the list is broken.  Be careful :)\n\n#### node.next\n\nThe next node in the list.\n\n#### node.prev\n\nThe previous node in the list.\n\n#### node.value\n\nThe data the node contains.\n\n#### node.list\n\nThe list to which this node belongs.  (Null if it does not belong to\nany list.)\n", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "time": {"modified": "2024-04-09T19:21:45.008Z", "created": "2015-12-18T08:02:25.789Z", "1.0.0": "2015-12-18T08:02:25.789Z", "1.0.1": "2015-12-18T09:29:58.326Z", "1.0.2": "2015-12-18T23:20:23.258Z", "1.1.0": "2015-12-19T00:32:18.820Z", "2.0.0": "2015-12-19T19:55:29.661Z", "2.0.1": "2017-03-11T20:09:18.123Z", "2.1.0": "2017-03-13T02:03:46.998Z", "2.1.1": "2017-03-13T16:55:34.896Z", "3.0.0": "2017-03-13T16:56:52.633Z", "3.0.1": "2017-03-13T22:12:34.074Z", "2.1.2": "2017-03-13T22:16:05.251Z", "3.0.2": "2017-04-25T19:59:38.913Z", "3.0.3": "2018-11-21T23:22:36.619Z", "3.1.0": "2019-09-26T17:14:01.323Z", "3.1.1": "2019-09-30T20:07:18.343Z", "4.0.0": "2019-09-30T20:08:08.970Z", "5.0.0": "2024-04-09T19:21:44.844Z"}, "homepage": "https://github.com/isaacs/yallist#readme", "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "license": "BlueOak-1.0.0", "readmeFilename": "README.md", "users": {"joaquin.briceno": true, "f3lang": true, "ssljivic": true, "soenkekluth": true, "flumpus-dev": true}}