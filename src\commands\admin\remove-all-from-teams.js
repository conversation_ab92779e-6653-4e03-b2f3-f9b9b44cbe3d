const { <PERSON><PERSON><PERSON><PERSON>mandBuilder, EmbedBuilder, PermissionFlagsBits } = require('discord.js');
const { getTeamManager } = require('../../database/teamManager');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('remove-all-from-teams')
        .setDescription('Remove ALL members from their teams while preserving their points and stats (Admin only)')
        .addBooleanOption(option =>
            option.setName('confirm')
                .setDescription('Confirm that you want to remove ALL members from their teams')
                .setRequired(true))
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

    async execute(interaction) {
        try {
            const confirm = interaction.options.getBoolean('confirm');
            
            if (!confirm) {
                const warningEmbed = new EmbedBuilder()
                    .setColor('#FFA500')
                    .setTitle('⚠️ Confirmation Required')
                    .setDescription('You must set `confirm` to `true` to proceed with removing all members from their teams.')
                    .addFields({
                        name: '🔄 What this command does:',
                        value: '• Removes ALL members from their teams\n• Removes team roles from Discord\n• **Preserves** all points, levels, XP, and stats\n• Does NOT delete teams themselves',
                        inline: false
                    })
                    .setTimestamp();

                return await interaction.reply({ embeds: [warningEmbed], ephemeral: true });
            }

            await interaction.deferReply();

            const teamManager = getTeamManager();

            // Use the TeamManager method to remove all members from teams
            const result = await teamManager.removeAllMembersFromTeams(interaction.guild);

            if (result.removedCount === 0) {
                const noMembersEmbed = new EmbedBuilder()
                    .setColor('#FFA500')
                    .setTitle('ℹ️ No Members in Teams')
                    .setDescription('There are currently no members assigned to any teams.')
                    .setTimestamp();

                return await interaction.editReply({ embeds: [noMembersEmbed] });
            }

            // Create summary embed
            const summaryEmbed = new EmbedBuilder()
                .setColor('#00FF00')
                .setTitle('✅ Bulk Team Removal Complete')
                .setDescription(`Successfully removed ${result.removedCount} members from their teams.`)
                .addFields(
                    {
                        name: '📊 Summary',
                        value: `• **Members processed:** ${result.totalMembers}\n• **Successfully removed:** ${result.removedCount}\n• **Teams affected:** ${result.teamsAffected}\n• **Role removal errors:** ${result.roleRemovalErrors}`,
                        inline: false
                    },
                    {
                        name: '💾 Data Preserved',
                        value: '• Points and levels\n• Voice time and message counts\n• Daily reward history\n• All other member statistics',
                        inline: true
                    },
                    {
                        name: '🏆 Teams Status',
                        value: '• Teams still exist\n• Team roles still exist\n• Team points reset to 0\n• Ready for new members',
                        inline: true
                    }
                )
                .setTimestamp();

            // Add team breakdown if not too many teams
            if (result.teamsAffected <= 10 && result.teamGroups) {
                const teamBreakdown = Object.entries(result.teamGroups)
                    .map(([teamName, data]) => `**${teamName}:** ${data.members.length} members`)
                    .join('\n');

                summaryEmbed.addFields({
                    name: '📋 Teams Affected',
                    value: teamBreakdown,
                    inline: false
                });
            }

            if (result.roleRemovalErrors > 0) {
                summaryEmbed.addFields({
                    name: '⚠️ Role Removal Warnings',
                    value: `${result.roleRemovalErrors} role removals failed. This may be due to:\n• Members who left the server\n• Missing role permissions\n• Deleted roles\n\nDatabase cleanup was still successful.`,
                    inline: false
                });
            }

            await interaction.editReply({ embeds: [summaryEmbed] });

        } catch (error) {
            console.error('Error in remove-all-from-teams command:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Error')
                .setDescription('An error occurred while removing members from teams.')
                .addFields({
                    name: 'Error Details',
                    value: error.message || 'Unknown error',
                    inline: false
                })
                .setTimestamp();

            if (interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }
};
