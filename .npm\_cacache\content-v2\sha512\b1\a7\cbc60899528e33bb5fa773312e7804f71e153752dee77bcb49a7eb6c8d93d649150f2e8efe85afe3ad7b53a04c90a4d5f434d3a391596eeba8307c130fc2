{"_id": "touch", "_rev": "40-d55902bdd34a2322b98c2e70a18b3366", "name": "touch", "description": "like touch(1) in node", "dist-tags": {"latest": "3.1.1"}, "versions": {"0.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "touch", "description": "like touch(1) in node", "version": "0.0.1", "repository": {"type": "git", "url": "git://github.com/isaacs/node-touch.git"}, "main": "touch.js", "engines": {"node": "~0.5.8 || 0.6"}, "dependencies": {"nopt": "~1.0.10"}, "devDependencies": {}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "_id": "touch@0.0.1", "_engineSupported": true, "_npmVersion": "1.0.93", "_nodeVersion": "v0.5.9-pre", "_defaultsLoaded": true, "dist": {"shasum": "c58742ef5dedcf0a238b39cbcadaa0e0e404e1b6", "tarball": "https://registry.npmjs.org/touch/-/touch-0.0.1.tgz", "integrity": "sha512-8RTPP+jH8W/ZIC1xL1fvdZVxR7te0PnLfKQhr0+NAD9kn3M73YywvUJIYodyW6EEii0jt4EuIzRDVKnqZnaueg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDzfPJXOIFl6UMXv+a/zQ2o/SQs50fBg12kB9CzNqyBHQIhANzv7vufE7iiKzz7K1ybXg71SXn/wMLMs61Y5bOvdixu"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}}, "0.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "touch", "description": "like touch(1) in node", "version": "0.0.2", "repository": {"type": "git", "url": "git://github.com/isaacs/node-touch.git"}, "main": "touch.js", "engines": {"node": ">=0.6"}, "dependencies": {"nopt": "~1.0.10"}, "_id": "touch@0.0.2", "dist": {"shasum": "a65a777795e5cbbe1299499bdc42281ffb21b5f4", "tarball": "https://registry.npmjs.org/touch/-/touch-0.0.2.tgz", "integrity": "sha512-JUyt3LWZRqkmKH0paN3IqVqhLV11FuXMz0c+/OZED9Abs+sFxSdtgBLknwqZmicSBtue+RGX4jXvbCIjipDMCA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHFgoFUeND3v8yiRdO7DGUe6Gdh5xlN/BpcwyL/prmcgAiEAqbV+AxEv3rJ5SfiFsvxbhDWlLl1a0WTnDmbZjFQJDSg="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}}, "0.0.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "touch", "description": "like touch(1) in node", "version": "0.0.3", "repository": {"type": "git", "url": "git://github.com/isaacs/node-touch.git"}, "main": "touch.js", "engines": {"node": ">=0.6"}, "dependencies": {"nopt": "~1.0.10"}, "license": "ISC", "bugs": {"url": "https://github.com/isaacs/node-touch/issues"}, "homepage": "https://github.com/isaacs/node-touch", "_id": "touch@0.0.3", "dist": {"shasum": "51aef3d449571d4f287a5d87c9c8b49181a0db1d", "tarball": "https://registry.npmjs.org/touch/-/touch-0.0.3.tgz", "integrity": "sha512-/LQ54KM9rPf3rGXGo2UPQWx3ol242Zg6Whq27H5DEmZhCJo+pm9N5BzRGepO9vTVhYxpXJdcc1+3uaYt9NyeKg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC9zsdEFs5238PVQOT9nkM6dEFkSYAwU05iLjPsqp5/eAiAG6pdBJbfqAkyKwTu61hJ0Iab8odYD0VQYl2Rz9JJdFg=="}]}, "_from": ".", "_npmVersion": "1.3.25", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}}, "0.0.4": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "touch", "description": "like touch(1) in node", "version": "0.0.4", "repository": {"type": "git", "url": "git://github.com/isaacs/node-touch.git"}, "main": "touch.js", "engines": {"node": ">=0.6"}, "bin": {"touch": "./bin/touch.js"}, "dependencies": {"nopt": "~1.0.10"}, "license": "ISC", "gitHead": "adedd503eff5859a8e775fd2bdb311e8ac183172", "bugs": {"url": "https://github.com/isaacs/node-touch/issues"}, "homepage": "https://github.com/isaacs/node-touch#readme", "_id": "touch@0.0.4", "scripts": {}, "_shasum": "fc1a7103cbe0ecee713d1384d775e2f9e21caae2", "_from": ".", "_npmVersion": "3.1.0", "_nodeVersion": "2.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "fc1a7103cbe0ecee713d1384d775e2f9e21caae2", "tarball": "https://registry.npmjs.org/touch/-/touch-0.0.4.tgz", "integrity": "sha512-Az8J2DmJM/H07TusRFofwCuPy21xA6Hw52QcF708s3c6Qs33eR5vqB078pJB4SnWBHaRoQ1YOQUwb3zbygFrLg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYrmsERljRo8yFqP/x81LFEHTYkLhM0t94l4Z/zPQPHAIhAPpcGTeElkfIXGUEk5m+DW4OlqjMCAyA5onFUuYcpyPg"}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "touch", "description": "like touch(1) in node", "version": "1.0.0", "repository": {"type": "git", "url": "git://github.com/isaacs/node-touch.git"}, "main": "touch.js", "engines": {"node": ">=0.6"}, "bin": {"touch": "./bin/touch.js"}, "dependencies": {"nopt": "~1.0.10"}, "license": "ISC", "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "^1.3.1"}, "gitHead": "f73938c01bd10fe70fae5af3f37fc8c6162e9852", "bugs": {"url": "https://github.com/isaacs/node-touch/issues"}, "homepage": "https://github.com/isaacs/node-touch#readme", "_id": "touch@1.0.0", "_shasum": "449cbe2dbae5a8c8038e30d71fa0ff464947c4de", "_from": ".", "_npmVersion": "3.1.0", "_nodeVersion": "2.2.1", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"shasum": "449cbe2dbae5a8c8038e30d71fa0ff464947c4de", "tarball": "https://registry.npmjs.org/touch/-/touch-1.0.0.tgz", "integrity": "sha512-7PLLVW4cfMVYXtMgX0ynJvyZDiupF2rzGzzf0yATTzgCgNK98DHjRSTm5zU1bFUtRjFdXNrEynn9+vhflVpC0A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAZ++JeX9/pJekYMjVq9r3vWnNIbgYwj943kBqntxNVmAiBPyCiXOBHIa3ob/wAdnreToMFgnNIpMdOcpJdTGcN9gQ=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "touch", "description": "like touch(1) in node", "version": "2.0.0", "repository": {"type": "git", "url": "git://github.com/isaacs/node-touch.git"}, "main": "touch.js", "engines": {"node": ">=0.6"}, "bin": {"nodetouch": "./bin/touch.js"}, "dependencies": {"nopt": "~1.0.10"}, "license": "ISC", "scripts": {"test": "tap test/*.js --lines=100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "devDependencies": {"mutate-fs": "^1.1.0", "tap": "^10.7.0"}, "gitHead": "17e3ed142f107374735c79f4791d03bee3485a88", "bugs": {"url": "https://github.com/isaacs/node-touch/issues"}, "homepage": "https://github.com/isaacs/node-touch#readme", "_id": "touch@2.0.0", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-7La7NUl/nljZ5NB/BzVG15D69PIMwiiAirRmAgyAtPe4bKP/C2qG/7xqA4xn9QZFJ98teYAWw/GQLwU7PODCfQ==", "shasum": "056ee6a54f784ee3e956d002f69e8ae603e5c753", "tarball": "https://registry.npmjs.org/touch/-/touch-2.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCqgMfTxi2K/cg83zII1byGrmkTKNbplXj7gIMtMgHZyAIgYs1ZWaj4RoTApZ/Lm3TK/LfGzNsj2vNrgnf0qDvknLE="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/touch-2.0.0.tgz_1498584180321_0.8908176447730511"}, "directories": {}}, "2.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "touch", "description": "like touch(1) in node", "version": "2.0.1", "repository": {"type": "git", "url": "git://github.com/isaacs/node-touch.git"}, "main": "touch.js", "engines": {"node": ">=0.6"}, "bin": {"nodetouch": "./bin/nodetouch.js"}, "dependencies": {"nopt": "~1.0.10"}, "license": "ISC", "scripts": {"test": "tap test/*.js --lines=100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "devDependencies": {"mutate-fs": "^1.1.0", "tap": "^10.7.0"}, "gitHead": "9c6f4b453496ba6e2c3f4ae57d604a54f273b9f3", "bugs": {"url": "https://github.com/isaacs/node-touch/issues"}, "homepage": "https://github.com/isaacs/node-touch#readme", "_id": "touch@2.0.1", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-umyBVYaKchtUko58rDiLYNT6GnbhJkE2oC5JyVjpL6NUoDG76n9kvtTerRyGcV2PTXEiOgip+KziFLnNOUDegg==", "shasum": "1d438b889244424ee1038307e8714fd5202c3abf", "tarball": "https://registry.npmjs.org/touch/-/touch-2.0.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEpn3qe8VVcSTmMIe7kkGsLFz71B8omMESOpxpGh5WYCAiEA9lFBHPptBanItTkSbj0aCPAjiZ4a2S+FZNxl0cUPpRc="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/touch-2.0.1.tgz_1498584226420_0.9335975097492337"}, "directories": {}}, "2.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "touch", "description": "like touch(1) in node", "version": "2.0.2", "repository": {"type": "git", "url": "git://github.com/isaacs/node-touch.git"}, "main": "touch.js", "engines": {"node": ">=0.6"}, "bin": {"nodetouch": "./bin/nodetouch.js"}, "dependencies": {"nopt": "~1.0.10"}, "license": "ISC", "scripts": {"test": "tap test/*.js --lines=100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "devDependencies": {"mutate-fs": "^1.1.0", "tap": "^10.7.0"}, "files": ["touch.js", "bin/nodetouch.js"], "gitHead": "0ed4f84b7c8997e899c3fa8869f394318c6180f0", "bugs": {"url": "https://github.com/isaacs/node-touch/issues"}, "homepage": "https://github.com/isaacs/node-touch#readme", "_id": "touch@2.0.2", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-qjNtvsFXTRq7IuMLweVgFxmEuQ6gLbRs2jQxL80TtZ31dEKWYIxRXquij6w6VimyDek5hD3PytljHmEtAs2u0A==", "shasum": "ca0b2a3ae3211246a61b16ba9e6cbf1596287164", "tarball": "https://registry.npmjs.org/touch/-/touch-2.0.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDii8tRYFisd7Sbix+RmCCF1v7tPyItxRISqQBEbf7W4wIga+COQgUfIj6/TLQJQokiPrCnGwPJDRv/dhLTs+SAxe8="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/touch-2.0.2.tgz_1498585020231_0.906999156344682"}, "directories": {}}, "3.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "touch", "description": "like touch(1) in node", "version": "3.0.0", "repository": {"type": "git", "url": "git://github.com/isaacs/node-touch.git"}, "bin": {"nodetouch": "./bin/nodetouch.js"}, "dependencies": {"nopt": "~1.0.10"}, "license": "ISC", "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "devDependencies": {"mutate-fs": "^1.1.0", "tap": "^10.7.0"}, "files": ["index.js", "bin/nodetouch.js"], "gitHead": "7f92ca07f96cbbd9bdc4974b6cd151401f7cddf9", "bugs": {"url": "https://github.com/isaacs/node-touch/issues"}, "homepage": "https://github.com/isaacs/node-touch#readme", "_id": "touch@3.0.0", "_npmVersion": "5.0.3", "_nodeVersion": "8.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-mWfl5b6n12BUGu25NVmD/Kaws0kHdHkHqYeINJOxx+RbQh2Lgr9D8L4PVC4zedzIRRxJqPfzyaEa89MIw/Gpag==", "shasum": "6d88b3bd2d5bc7c2d074a8e481111daccb244363", "tarball": "https://registry.npmjs.org/touch/-/touch-3.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF1KSQ4BeqD+HRcYz55mry12wy3sRuWTt1r/hBeZHlkmAiASUNC4yTX+Y5IEFXEf+e3Gy3ycFU1bGJK1HKPHqMtYwQ=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/touch-3.0.0.tgz_1498784393795_0.594521732069552"}, "directories": {}}, "3.1.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "touch", "description": "like touch(1) in node", "version": "3.1.0", "repository": {"type": "git", "url": "git://github.com/isaacs/node-touch.git"}, "bin": {"nodetouch": "./bin/nodetouch.js"}, "dependencies": {"nopt": "~1.0.10"}, "license": "ISC", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "devDependencies": {"mutate-fs": "^1.1.0", "tap": "^10.7.0"}, "files": ["index.js", "bin/nodetouch.js"], "gitHead": "7706a0d249c72edd90adbeb37ec8d4ca2b9a9a7f", "bugs": {"url": "https://github.com/isaacs/node-touch/issues"}, "homepage": "https://github.com/isaacs/node-touch#readme", "_id": "touch@3.1.0", "_npmVersion": "5.0.4", "_nodeVersion": "8.0.0", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-WBx8Uy5TLtOSRtIq+M03/sKDrXCLHxwDcquSP2c43Le03/9serjQBIztjRz6FkJez9D/hleyAXTBGLwwZUw9lA==", "shasum": "fe365f5f75ec9ed4e56825e0bb76d24ab74af83b", "tarball": "https://registry.npmjs.org/touch/-/touch-3.1.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHv1bMoGGIWMHcoao7Bes+tVG+nQ+pVXXFq4dQtblq9oAiBQNAutYpnsX60fpQHP8C67/2lL+vndilFFO3p/ivIjPw=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/touch-3.1.0.tgz_1498866039484_0.26272376789711416"}, "directories": {}}, "3.1.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "name": "touch", "description": "like touch(1) in node", "version": "3.1.1", "repository": {"type": "git", "url": "git://github.com/isaacs/node-touch.git"}, "bin": {"nodetouch": "bin/nodetouch.js"}, "license": "ISC", "scripts": {"test": "tap test/*.js --100 -J", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "devDependencies": {"mutate-fs": "^1.1.0", "tap": "^10.7.0"}, "_id": "touch@3.1.1", "gitHead": "cb0ad3931175ce5bd27cf4622e13d9f3f4beb5cd", "bugs": {"url": "https://github.com/isaacs/node-touch/issues"}, "homepage": "https://github.com/isaacs/node-touch#readme", "_nodeVersion": "20.11.0", "_npmVersion": "10.7.0", "dist": {"integrity": "sha512-r0eojU4bI8MnHr8c5bNo7lJDdI2qXlWWJk6a9EAFG7vbhTjElYhBVS3/miuE0uOuoLdb8Mc/rVfsmm6eo5o9GA==", "shasum": "097a23d7b161476435e5c1344a95c0f75b4a5694", "tarball": "https://registry.npmjs.org/touch/-/touch-3.1.1.tgz", "fileCount": 5, "unpackedSize": 10163, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGiTzClJn5tOiX+aWcwL6DK6K7VukyQ2qHqiCBG5cIHzAiEA1kJE5w40jOmL3ezWwSPQPuyqbNyf1kk6BNUDcmbIAew="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/touch_3.1.1_1715700171299_0.13362097320585575"}, "_hasShrinkwrap": false}}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "time": {"modified": "2024-05-14T15:22:51.625Z", "created": "2011-10-05T22:13:24.358Z", "0.0.1": "2011-10-05T22:13:25.389Z", "0.0.2": "2012-07-12T21:41:21.259Z", "0.0.3": "2014-01-25T01:57:35.319Z", "0.0.4": "2015-07-15T18:34:56.019Z", "1.0.0": "2015-07-17T00:38:40.004Z", "2.0.0": "2017-06-27T17:23:00.394Z", "2.0.1": "2017-06-27T17:23:46.567Z", "2.0.2": "2017-06-27T17:37:00.354Z", "3.0.0": "2017-06-30T00:59:54.118Z", "3.1.0": "2017-06-30T23:40:39.606Z", "3.1.1": "2024-05-14T15:22:51.464Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "repository": {"type": "git", "url": "git://github.com/isaacs/node-touch.git"}, "users": {"wenbing": true, "robermac": true, "icyflame": true, "rochejul": true, "programmer.severson": true, "abuelwafa": true, "krabello": true, "boopathisakthivel.in": true, "joris-van-der-wel": true, "axelrindle": true, "akinjide": true, "joaquin.briceno": true, "ackhub": true, "geeksunny": true, "centiball": true}, "readme": "# node-touch\n\nFor all your node touching needs.\n\n## Installing\n\n```bash\nnpm install touch\n```\n\n## CLI Usage:\n\nSee `man touch`\n\nThis package exports a binary called `nodetouch` that works mostly\nlike the unix builtin `touch(1)`.\n\n## API Usage:\n\n```javascript\nvar touch = require(\"touch\")\n```\n\nGives you the following functions:\n\n* `touch(filename, options, cb)`\n* `touch.sync(filename, options)`\n* `touch.ftouch(fd, options, cb)`\n* `touch.ftouchSync(fd, options)`\n\nAll the `options` objects are optional.\n\nAll the async functions return a Promise.  If a callback function is\nprovided, then it's attached to the Promise.\n\n## Options\n\n* `force` like `touch -f` Boolean\n* `time` like `touch -t <date>` Can be a Date object, or any parseable\n  Date string, or epoch ms number.\n* `atime` like `touch -a` Can be either a Boolean, or a Date.\n* `mtime` like `touch -m` Can be either a Boolean, or a Date.\n* `ref` like `touch -r <file>` Must be path to a file.\n* `nocreate` like `touch -c` Boolean\n\nIf neither `atime` nor `mtime` are set, then both values are set.  If\none of them is set, then the other is not.\n\n## cli\n\nThis package creates a `nodetouch` command line executable that works\nvery much like the unix builtin `touch(1)`\n", "readmeFilename": "README.md", "homepage": "https://github.com/isaacs/node-touch#readme", "bugs": {"url": "https://github.com/isaacs/node-touch/issues"}, "license": "ISC"}