{"_id": "fs", "_rev": "65-52c5d33bd2f8f3c375c6632fdc4683be", "name": "fs", "time": {"modified": "2023-07-10T04:51:05.229Z", "created": "2014-06-02T02:18:51.732Z", "0.0.1": "2014-06-02T02:18:51.732Z", "0.0.0": "2014-09-12T06:06:20.404Z", "0.0.2": "2014-09-12T06:09:38.952Z", "0.0.1-security": "2016-08-23T17:56:58.976Z"}, "maintainers": [{"name": "npm", "email": "<EMAIL>"}], "dist-tags": {"latest": "0.0.1-security"}, "description": "This package name is not currently in use, but was formerly occupied by another package. To avoid malicious use, npm is hanging on to the package name, but loosely, and we'll probably give it to you if you want it.", "readme": "# Security holding package\n\nThis package name is not currently in use, but was formerly occupied\nby another package. To avoid malicious use, npm is hanging on to the\npackage name, but loosely, and we'll probably give it to you if you\nwant it.\n\nYou may adopt this package <NAME_EMAIL> and\nrequesting the name.\n", "versions": {"0.0.1-security": {"name": "fs", "version": "0.0.1-security", "description": "This package name is not currently in use, but was formerly occupied by another package. To avoid malicious use, npm is hanging on to the package name, but loosely, and we'll probably give it to you if you want it.", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/npm/security-holder.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/npm/security-holder/issues"}, "homepage": "https://github.com/npm/security-holder#readme", "_id": "fs@0.0.1-security", "_shasum": "8a7bd37186b6dddf3813f23858b57ecaaf5e41d4", "_from": ".", "_npmVersion": "3.10.5", "_nodeVersion": "4.1.2", "_npmUser": {"name": "eh<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "8a7bd37186b6dddf3813f23858b57ecaaf5e41d4", "tarball": "https://registry.npmjs.org/fs/-/fs-0.0.1-security.tgz", "integrity": "sha512-3XY9e1pP0CVEUCdj5BmfIZxRBTSDycnbqhIOGec9QYtmVH2fbLpj86CFWkrNOkt/Fvty4KZG5lTglL9j/gJ87w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC1aY7fjQfdY41GBF/eg3bOFiTXmX4hbWt1xW096SMD7AiEAgF3+FFCghgiuMGhEje//GyDHLK5vMFU3mt84a/j6DXg="}]}, "maintainers": [{"name": "eh<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.0": {"name": "fs", "version": "0.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "MIT", "_id": "fs@0.0.0", "_npmVersion": "0.0.0-fake", "_nodeVersion": "0.0.0-fake", "_shasum": "c86a5c9c19886759e385bb2c6b34f1d30a5295ce", "_npmUser": {"name": "npm", "email": "<EMAIL>"}, "_from": ".", "dist": {"shasum": "c86a5c9c19886759e385bb2c6b34f1d30a5295ce", "tarball": "https://registry.npmjs.org/fs/-/fs-0.0.0.tgz", "integrity": "sha512-LhS8l0UA1/ihKq/H4xLorYr9ffVxncmIu4U8VNS8H0Jhdi684qqd+mcuvRsp8KaJ/biRI2grv1uCflzm14fa+A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICJluW8tBiaI717J7l4gONWyfvk9Q4WArWGto/bbqXt/AiEApyPIJUYSxe3uzmX2Qu/XrwttmxK9m1tYf0WE9154+u4="}]}, "maintainers": [{"name": "npm", "email": "<EMAIL>"}], "directories": {}}, "0.0.2": {"name": "fs", "version": "0.0.2", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "MIT", "_id": "fs@0.0.2", "_npmVersion": "0.0.0-fake", "_nodeVersion": "0.0.0-fake", "_shasum": "e1f244ef3933c1b2a64bd4799136060d0f5914f8", "_npmUser": {"name": "npm", "email": "<EMAIL>"}, "_from": ".", "dist": {"shasum": "e1f244ef3933c1b2a64bd4799136060d0f5914f8", "tarball": "https://registry.npmjs.org/fs/-/fs-0.0.2.tgz", "integrity": "sha512-YAiVokMCrSIFZiroB1oz51hPiPRVcUtSa4x2U5RYXyhS9VAPdiFigKbPTnOSq7XY8wd3FIVPYmXpo5lMzFmxgg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEHckausnKg1Ci8vm0PI50bjl6iehv4KRvKwBasb01bOAiBSLB2aYnw1XsC1yi7t7+jusJIHw84lKZcg451xC+JaOg=="}]}, "maintainers": [{"name": "npm", "email": "<EMAIL>"}], "directories": {}}}, "homepage": "https://github.com/npm/security-holder#readme", "keywords": [], "repository": {"type": "git", "url": "git+https://github.com/npm/security-holder.git"}, "bugs": {"url": "https://github.com/npm/security-holder/issues"}, "license": "ISC", "readmeFilename": "README.md", "users": {"fgmnts": true, "chinawolf_wyp": true, "dhanya-kr": true, "dailepro": true, "joey.dossche": true, "wxttxw125": true, "ololo-1": true, "karnavpargi": true, "lionel86": true, "cfleschhut": true, "sir_dubzz": true, "code.abdullahahmed": true, "flumpus-dev": true, "dash0908": true}}