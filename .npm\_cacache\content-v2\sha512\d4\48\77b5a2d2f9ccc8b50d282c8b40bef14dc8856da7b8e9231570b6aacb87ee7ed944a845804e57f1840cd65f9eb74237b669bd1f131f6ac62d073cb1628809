{"_id": "base64-js", "_rev": "67-57cdbc79ebbdc879da1bd48de5e147a2", "name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "dist-tags": {"latest": "1.5.1"}, "versions": {"0.0.1": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "version": "0.0.1", "repository": {"type": "git", "url": "git://github.com/beatgammit/deflate-js.git"}, "main": "lib/b64.js", "scripts": {"test": "cd test; node runner.js; cd -"}, "engines": {"node": ">= 0.4"}, "dependencies": {}, "devDependencies": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "base64-js@0.0.1", "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.2", "_defaultsLoaded": true, "dist": {"shasum": "a0094eb63d2c01b094187f51ac9d82f2256a71ae", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-0.0.1.tgz", "integrity": "sha512-hS7xBk3PWWXw8RMEDliYIMkCPlEvd1itCN8XrVvd7nq93bU3ZqF6zfPPzGvy7MnSml8NMFBllFLvCryPYq4vMw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8cVWK08yal8MhziYNmZgDK0Ir6n7a243HIWdX2Re8+AIgBakiRVZCk2YzOJ69epMW80JeGJinVikDQHvMpuPjOZI="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.2": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "version": "0.0.2", "repository": {"type": "git", "url": "git://github.com/beatgammit/deflate-js.git"}, "main": "lib/b64.js", "scripts": {"test": "cd test; node runner.js; cd -"}, "engines": {"node": ">= 0.4"}, "dependencies": {}, "devDependencies": {}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "base64-js@0.0.2", "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.2", "_defaultsLoaded": true, "dist": {"shasum": "024f0f72afa25b75f9c0ee73cd4f55ec1bed9784", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-0.0.2.tgz", "integrity": "sha512-Pj9L87dCdGcKlSqPVUjD+q96pbIx1zQQLb2CUiWURfjiBELv84YX+0nGnKmyT/9KkC7PQk7UN1w+Al8bBozaxQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQWAl3rBrga7vvttx+oi8J2hMooskQaSCQd5z6LgNIugIgQ4PyziXFR6Iv9pE509xKFgZ2lynZv6OrJCNdcbNnM1E="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.3": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "version": "0.0.3", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "main": "lib/b64.js", "scripts": {"test": "cd test; node runner.js; cd -"}, "engines": {"node": ">= 0.4"}, "license": "MIT", "dependencies": {}, "devDependencies": {}, "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "_id": "base64-js@0.0.3", "dist": {"shasum": "6c3bfa8886c7b8d41b934544d5856aa4f13fbf5c", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-0.0.3.tgz", "integrity": "sha512-TESZIWIHHHa1gAJpkdt4yQts7yJZDCfZqixT0xp0/eca8AH0aB+W7BgGMhNP8bgSklfWeLOTjWwBJEUFvaw//Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDffUyzXbgRA4926J8sc0iWh8RaBy3W6Q9OIKbnGPZcqwIhAIYWXx0Jo2cZCg5J+J6M+pivfDL96qH2P6YmAQG2+2FP"}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.4": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "version": "0.0.4", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "main": "lib/b64.js", "scripts": {"test": "cd test; node runner.js; cd -"}, "engines": {"node": ">= 0.4"}, "license": "MIT", "dependencies": {}, "devDependencies": {}, "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "_id": "base64-js@0.0.4", "dist": {"shasum": "8eac03d51ff44cf297b4e5802168ddbec0dd1673", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-0.0.4.tgz", "integrity": "sha512-uB1lnnFBfuQLaQLY+AKUekjTZISo/f1PXceZ+Od91i52mAMiWpH0Y2X6O5IYp8bk9sN6G7U/ZJpQU1axrpuYaQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFUloDUJdT+kf6EuU0rGzCa8+ndW0nlU7LT/HjyMJ4qXAiEA988kmKuLfnvfpZbZ4hcCvINmrKrPgerXmekii8EAevE="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.5": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "version": "0.0.5", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "main": "lib/b64.js", "scripts": {"test": "tape test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "engines": {"node": ">= 0.4"}, "license": "MIT", "dependencies": {}, "devDependencies": {"tape": "~2.3.2"}, "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "homepage": "https://github.com/beatgammit/base64-js", "_id": "base64-js@0.0.5", "dist": {"shasum": "ec6d92297da0ef0eb4fa6aaeb0f67e74869c9731", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-0.0.5.tgz", "integrity": "sha512-1mc7U6hLOMdbRwrZOzwZbZjgF9qhfd0KIS1L/zKc34QxMRB4LzgwGWk6Kmx24pbTHvy3eidGg4OkS5GHHj+wdQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhBRdu0CT+QOq3UeG4dTqkW/NXFc3UBQd8lfyEoo5fpAIgdAxJCVTokQ3+LKnpCQ9+HW8MNKZmvtceeGn8cvM98Yg="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}}, "0.0.6": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "version": "0.0.6", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "main": "lib/b64.js", "scripts": {"test": "tape test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "engines": {"node": ">= 0.4"}, "license": "MIT", "dependencies": {}, "devDependencies": {"tape": "~2.3.2"}, "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "homepage": "https://github.com/beatgammit/base64-js", "_id": "base64-js@0.0.6", "dist": {"shasum": "7b859f79f0bbbd55867ba67a7fab397e24a20947", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-0.0.6.tgz", "integrity": "sha512-fsyA7iUnTsMJtsDqcL/8BqXAp66zldDas+PP1lDY6fVjcXxjQllGSmuT58evJbqmLK1WutPTq/lmQqFCN2eftw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZ9qR0HZvVFD1VByb6EhdTV/a+VHeD/LSlxsgOOwEVFQIhAPNCP558MgC+U+/QaYlGwLEWeChqB/coCvDY1PqBsqQo"}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}}, "0.0.7": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "version": "0.0.7", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "main": "lib/b64.js", "scripts": {"test": "tape test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "engines": {"node": ">= 0.4"}, "license": "MIT", "dependencies": {}, "devDependencies": {"tape": "~2.3.2"}, "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "homepage": "https://github.com/beatgammit/base64-js", "_id": "base64-js@0.0.7", "_shasum": "54400dc91d696cec32a8a47902f971522fee8f48", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "54400dc91d696cec32a8a47902f971522fee8f48", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-0.0.7.tgz", "integrity": "sha512-0nMfGOwe+glKQmfi9trLwlSMeLuTkupKQ6scwrlRP4TdfZR87kwZwMBNYOz8xdtXqefa2uI7rQy6n8GxxtYFvw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG8ZfK83rZOT+m5GDTticY2hZc7Fz7RerWELXVtnvviKAiEAkP/B0LPQg1wJsbs8PFQ0fXIHIKKyE/wq8nEVkALjbY8="}]}, "directories": {}}, "0.0.8": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "version": "0.0.8", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "main": "lib/b64.js", "scripts": {"test": "tape test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "engines": {"node": ">= 0.4"}, "license": "MIT", "dependencies": {}, "devDependencies": {"tape": "~2.3.2"}, "gitHead": "b4a8a5fa9b0caeddb5ad94dd1108253d8f2a315f", "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "homepage": "https://github.com/beatgammit/base64-js", "_id": "base64-js@0.0.8", "_shasum": "1101e9544f4a76b1bc3b26d452ca96d7a35e7978", "_from": ".", "_npmVersion": "2.1.16", "_nodeVersion": "0.10.35", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "1101e9544f4a76b1bc3b26d452ca96d7a35e7978", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-0.0.8.tgz", "integrity": "sha512-3XSA2cR/h/73EzlXXdU6YNycmYI7+kicTxks4eJg2g39biHR84slg2+des+p7iHYhbRg/udIS4TD53WabcOUkw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDjrMXScYp4Fmz62ghcTMTMUMf2ce0zs/a/c+S58JFu/AiAFjhY8+KFLjFe00vk5bxS+NCr8GraXZT3YFoEYZGTKZw=="}]}, "directories": {}}, "1.0.1": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "version": "1.0.1", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "main": "lib/b64.js", "scripts": {"test": "standard && tape test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "engines": {"node": ">= 0.4"}, "license": "MIT", "dependencies": {}, "devDependencies": {"standard": "^5.2.2", "tape": "4.x"}, "gitHead": "e89d48501fc2a976249e890a2ea8ec5b758a107d", "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "homepage": "https://github.com/beatgammit/base64-js#readme", "_id": "base64-js@1.0.1", "_shasum": "6926d1b194fbc737b8eed513756de2fcda7ea408", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.3", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "6926d1b194fbc737b8eed513756de2fcda7ea408", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-1.0.1.tgz", "integrity": "sha512-szaCFWShkkvQJbikxtLbeBCmscFw+8e47xdZJUR/s4t90miensQO4kEqVaIOIAE+xzccWxGaYphGqIs1tLvSfw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDtcO3E8FEro235zyO5RO/ime1htdH+p6d8dFzaAgnsGgIgZneOEqeQrZZ9g7RcciO8NxsYdpBjrAaQTz6Zh/ayaZM="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}], "directories": {}}, "1.0.2": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "version": "1.0.2", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "main": "lib/b64.js", "scripts": {"test": "standard && tape test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "engines": {"node": ">= 0.4"}, "license": "MIT", "dependencies": {}, "devDependencies": {"standard": "^5.2.2", "tape": "4.x"}, "gitHead": "160c83a130b0acb848f6ac47f79c2a16dcbf20ec", "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "homepage": "https://github.com/beatgammit/base64-js#readme", "_id": "base64-js@1.0.2", "_shasum": "474211c95e6cf2a547db461e4f6778b51d08fa65", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.3", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "474211c95e6cf2a547db461e4f6778b51d08fa65", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-1.0.2.tgz", "integrity": "sha512-ZXBDPMt/v/8fsIqn+Z5VwrhdR6jVka0bYobHdGia0Nxi7BJ9i/Uvml3AocHIBtIIBhZjBw5MR0aR4ROs/8+SNg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD94nEHBtX3CleG/CGuH80E4JGCUuYBRMRDz3Xsrjih8gIgSIP5h81BkmNYhC0CBOwH6U2ZGHTGhJr+20FnvguKhoQ="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}], "directories": {}}, "1.0.3": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "version": "1.0.3", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "main": "lib/b64.js", "scripts": {"test": "standard && tape test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "engines": {"node": ">= 0.4"}, "license": "MIT", "dependencies": {}, "devDependencies": {"standard": "^6.0.5", "tape": "4.x"}, "gitHead": "07a89130e6d9f064026535b9fa35b5483ed2e922", "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "homepage": "https://github.com/beatgammit/base64-js#readme", "_id": "base64-js@1.0.3", "_shasum": "1b0516f70bfc666868c89dccafb49290ecea562c", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.3.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "1b0516f70bfc666868c89dccafb49290ecea562c", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-1.0.3.tgz", "integrity": "sha512-qj6+aKzmvHUkof8zB+7UFi3MNGY3GUvtNk59m+t2nyL0ZAYQqDwp5aXyOwNBdvrl94WtbALPqhjwEPPzVY40gQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+QS5SPL/CNTQ8jYWsAm5NRKvovrfd4KVfC1tK93J+2AIhAMoX56C/Kgxpx/ryjTyu45CjZtIg+3p8mh3d6n4VV4Ve"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/base64-js-1.0.3.tgz_1455305751190_0.34513800288550556"}, "directories": {}}, "1.0.4": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "version": "1.0.4", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "main": "lib/b64.js", "scripts": {"test": "standard && tape test/*.js"}, "license": "MIT", "dependencies": {}, "devDependencies": {"standard": "^6.0.5", "tape": "4.x"}, "gitHead": "f486d4d183b9b66cf291d537771b918de3d007e4", "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "homepage": "https://github.com/beatgammit/base64-js#readme", "_id": "base64-js@1.0.4", "_shasum": "6fbe874ff18e28822b84cc9fdc22d7dc5aad77c7", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.3.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "6fbe874ff18e28822b84cc9fdc22d7dc5aad77c7", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-1.0.4.tgz", "integrity": "sha512-Msz/M9q6MQWPNfRWZ76SQTMrqi6eH1u96q1UElzIxPYcbmGF804zj2lrfQqaKetTEd00FHv0gr4I6IKqIM3EuQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC4xlNLGOg/GGSE3of5ZfvAYvcD1+NJ1Woz2//zMCxTGwIhAIZ0WSlKTMnqgTsc6gq1Li+6v9PJt76iBcH/px+ZElmJ"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/base64-js-1.0.4.tgz_1455322912489_0.5761241426225752"}, "directories": {}}, "1.1.0": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "version": "1.1.0", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "main": "lib/b64.js", "scripts": {"test": "standard && tape test/*.js"}, "license": "MIT", "dependencies": {}, "devDependencies": {"benchmark": "^2.1.0", "standard": "^6.0.5", "tape": "4.x"}, "gitHead": "ef6f7131ff4cf59ae7ed1695e2985372f01a3290", "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "homepage": "https://github.com/beatgammit/base64-js#readme", "_id": "base64-js@1.1.0", "_shasum": "5f91b0f64cdd2e20aa2f31f2b0e00a4198ed9271", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.3.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "5f91b0f64cdd2e20aa2f31f2b0e00a4198ed9271", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-1.1.0.tgz", "integrity": "sha512-LwdYloS/BZm3MmZsucd0/2YBdHzy/zz1D5vJV8LG50AaXxDYI1fcPqIvkfzobDPwpeyOY94Ql+/f6zbIloOF1Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICakipiXFgkvkClSTuZ7T/jHx36jv51aLE1ygniWfaePAiAzjO4nYowUnBdjd3zF6lwuBUfEdAStYQ4kvmX7uoz/Dw=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/base64-js-1.1.0.tgz_1456766218667_0.0795334242284298"}, "directories": {}}, "1.1.1": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "version": "1.1.1", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "main": "lib/b64.js", "scripts": {"build": "browserify -s base64js -r ./ | uglifyjs -m > base64js.min.js", "test": "standard && tape test/*.js"}, "license": "MIT", "dependencies": {}, "devDependencies": {"benchmark": "^2.1.0", "browserify": "^13.0.0", "standard": "^6.0.5", "tape": "4.x", "uglify-js": "^2.6.2"}, "gitHead": "638643fcef095be78b4004807421e397af8588a3", "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "homepage": "https://github.com/beatgammit/base64-js#readme", "_id": "base64-js@1.1.1", "_shasum": "91c13c7038671cd4529c8646fde60bf7e91bc808", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.3.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "91c13c7038671cd4529c8646fde60bf7e91bc808", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-1.1.1.tgz", "integrity": "sha512-rV/5tLtyxeIe6+5kubWzvunD2uOR3PbcJTTls2fV8Y064a0G60YkZ3rFtyNf5vK48QLOIAb2EkEEHBCIrLGrag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHGS/n+/eohyky3ZJ89xbKBm1VYkwSorpqEqG3mRndkkAiEA0cpyDGZxit3U01DQDeuMLeIBkhw3xb6jPy9O/xXv1qI="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/base64-js-1.1.1.tgz_1456878579861_0.16074915719218552"}, "directories": {}}, "1.1.2": {"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "version": "1.1.2", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "main": "lib/b64.js", "scripts": {"build": "browserify -s base64js -r ./ | uglifyjs -m > base64js.min.js", "test": "standard && tape test/*.js"}, "license": "MIT", "dependencies": {}, "devDependencies": {"benchmark": "^2.1.0", "browserify": "^13.0.0", "standard": "^6.0.5", "tape": "4.x", "uglify-js": "^2.6.2"}, "gitHead": "75f9f00f6baeeae9c51e21f43c2a9c918978d52c", "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "homepage": "https://github.com/beatgammit/base64-js#readme", "_id": "base64-js@1.1.2", "_shasum": "d6400cac1c4c660976d90d07a04351d89395f5e8", "_from": ".", "_npmVersion": "2.14.20", "_nodeVersion": "4.4.1", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "d6400cac1c4c660976d90d07a04351d89395f5e8", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-1.1.2.tgz", "integrity": "sha512-AIxxJSNK6fMJTnRuY14y/+86h+R4Ybztcchea+Al8aPIPFa6LvDSV90VN5EH81DVXQmh6YjIqpLyG/ljQDoqeQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEMCHwt5lcKNcqYm6W7RCvyyZJLneq3LaaKKskmtCRseC9ECIBit2I7sJAP0XYcSHNR7V73HJsbl3j7pFP5ANZ1lgfdf"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/base64-js-1.1.2.tgz_1458813298889_0.6030721089337021"}, "directories": {}}, "1.2.0": {"name": "base64-js", "version": "1.2.0", "description": "Base64 encoding/decoding in pure JS", "keywords": ["base64"], "homepage": "https://github.com/beatgammit/base64-js", "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "files": ["test", "index.js", "base64js.min.js"], "main": "index.js", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "scripts": {"build": "browserify -s base64js -r ./ | uglifyjs -m > base64js.min.js", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/*.js"}, "devDependencies": {"benchmark": "^2.1.0", "browserify": "^13.0.0", "standard": "*", "tape": "4.x", "uglify-js": "^2.6.2"}, "gitHead": "18bb7b2f20af653e60ae186bd879d3c4e6e6d8e6", "_id": "base64-js@1.2.0", "_shasum": "a39992d723584811982be5e290bb6a53d86700f1", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.5.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "a39992d723584811982be5e290bb6a53d86700f1", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-1.2.0.tgz", "integrity": "sha512-hURVuTTGLOppKhjSe9lZy4NCjnvaIAF/juwazv4WtHwsk5rxKrU1WbxN+XtwKDSvkrNbIIaTBQd9wUsSwruZUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDRMGeutnjRHN8eTuMRGSCrmDIRqiBmskiSTQUGf2cN1AIhALhBpKM0i5N4yx9381zhTFMnczMzOq71dN2qOtBWSnpn"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/base64-js-1.2.0.tgz_1474574119286_0.4763944323640317"}, "directories": {}}, "1.2.1": {"name": "base64-js", "version": "1.2.1", "description": "Base64 encoding/decoding in pure JS", "keywords": ["base64"], "homepage": "https://github.com/beatgammit/base64-js", "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "files": ["test", "index.js", "base64js.min.js"], "main": "index.js", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "scripts": {"build": "browserify -s base64js -r ./ | uglifyjs -m > base64js.min.js", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/*.js"}, "devDependencies": {"benchmark": "^2.1.4", "browserify": "^14.0.0", "standard": "*", "tape": "4.x", "uglify-js": "^2.8.29"}, "gitHead": "13d56bffa289ae3f406cb932c927461442a434ba", "_id": "base64-js@1.2.1", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.2", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-dwVUVIXsBZXwTuwnXI9RK8sBmgq09NDHzyR9SAph9eqk76gKK2JSQmZARC2zRC81JC2QTtxD0ARU5qTS25gIGw==", "shasum": "a91947da1f4a516ea38e5b4ec0ec3773675e0886", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-1.2.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFqHCqnCUdKVsk5YWaD7Zn6A4M9Ga7DK3L/jo029oNbZAiEAnNAVv+5N1LJZsPrA2Q3RTRJZNsErZku97HjMzSyIwdQ="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/base64-js-1.2.1.tgz_1498100565880_0.40269751008599997"}, "directories": {}}, "1.2.2": {"name": "base64-js", "version": "1.2.2", "description": "Base64 encoding/decoding in pure JS", "keywords": ["base64"], "homepage": "https://github.com/beatgammit/base64-js", "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "files": ["test", "index.js", "base64js.min.js"], "main": "index.js", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "scripts": {"build": "browserify -s base64js -r ./ | uglifyjs -m > base64js.min.js", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/*.js"}, "devDependencies": {"benchmark": "^2.1.4", "browserify": "^14.0.0", "standard": "*", "tape": "4.x", "uglify-js": "^2.8.29"}, "gitHead": "3fe859849b7a6b6db8b1fe43df00e52aaf5a7da1", "_id": "base64-js@1.2.2", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Luyxwa4AD/yXqN4fa+0Z6oYZMIVudy3b+6My0/22vxwiuMw+aEG4KzXpjb2LxKlMoOu8u/RG24dCKurDtnHRPA==", "shasum": "6e3b03a8f091affdbe297453268ca26570ae51f0", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-1.2.2.tgz", "fileCount": 8, "unpackedSize": 11900, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFTh3MWSrpOVBBcZlKTvRSncS3+KgfZlBaNpKkox74rLAiEA/xTJdcM4FYgSrX+CZR+lLg5jtNFGACmSL9o9Cy3pUb8="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/base64-js_1.2.2_1518748732205_0.5429508600757593"}, "_hasShrinkwrap": false}, "1.2.3": {"name": "base64-js", "version": "1.2.3", "description": "Base64 encoding/decoding in pure JS", "keywords": ["base64"], "homepage": "https://github.com/beatgammit/base64-js", "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "files": ["test", "index.js", "base64js.min.js"], "main": "index.js", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "scripts": {"build": "browserify -s base64js -r ./ | uglifyjs -m > base64js.min.js", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/*.js"}, "devDependencies": {"benchmark": "^2.1.4", "browserify": "^14.0.0", "standard": "*", "tape": "4.x", "uglify-js": "^2.8.29"}, "gitHead": "fa0905c66fe77b02fe20488639402c52b5c22343", "_id": "base64-js@1.2.3", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-MsAhsUW1GxCdgYSO6tAfZrNapmUKk7mWx/k5mFY/A1gBtkaCaNapTg+FExCw1r9yeaZhqx/xPg43xgTFH6KL5w==", "shasum": "fb13668233d9614cf5fb4bce95a9ba4096cdf801", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-1.2.3.tgz", "fileCount": 8, "unpackedSize": 12024, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCRkxIFd79VKHZoDFI0ee/EPx7x7edJfpm2YCVMHybe9AIhAPTv58JXIhpm1UtepEhxBPxOaEjeRDTGl6z5jzEbwUCY"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/base64-js_1.2.3_1518749129931_0.1986327306540996"}, "_hasShrinkwrap": false}, "1.3.0": {"name": "base64-js", "version": "1.3.0", "description": "Base64 encoding/decoding in pure JS", "keywords": ["base64"], "homepage": "https://github.com/beatgammit/base64-js", "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "license": "MIT", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "files": ["test", "index.js", "base64js.min.js"], "main": "index.js", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "scripts": {"build": "browserify -s base64js -r ./ | uglifyjs -m > base64js.min.js", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/*.js"}, "devDependencies": {"benchmark": "^2.1.4", "browserify": "^14.0.0", "standard": "*", "tape": "4.x", "uglify-js": "^2.8.29"}, "gitHead": "09b98d0ffa6669aec30c2cbc52e84effae7be66b", "_id": "base64-js@1.3.0", "_npmVersion": "5.8.0", "_nodeVersion": "8.11.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ccav/yGvoa80BQDljCxsmmQ3Xvx60/UpBIij5QN21W3wBi/hhIC9OoO+KLpu9IJTS9j4DRVJ3aDDF9cMSoa2lw==", "shasum": "cab1e6118f051095e58b5281aea8c1cd22bfc0e3", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-1.3.0.tgz", "fileCount": 9, "unpackedSize": 13019, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1n8/CRA9TVsSAnZWagAA8n8P/RZu7TXBAZ0H4jbzP/19\nhvX9fXm4NKjQG9nTLs9ebEdSBCqXoCf2vmqXdGVNthpxDOUlT1Md2R2h4omD\nTSk6mhMjI1mLybssv1YLPtlfSwt5Tuym3MIieC3i54wlxkH+Je5gOSyvQgFI\nobY57uli+cuii9iCgiJxvH0xePXne5aZGtLqWcqZpRbsg7KmPR7ACBj2PxRG\nlU0IZybTbVkFHvQEdNjGcBt+eHNB5ZWZADskXIfBGXr2nptQIIr7NDnEaSNK\nFmbirTKi9LZE5jq9ekNRx0vLIoYXnsL8LqaiNHXusWQRQhCp605KJuNT7sI8\nv/lp0+a4tUzRxrf6+IrQQ65SYRpIzU8t9xxAqpgLlGJN2bSmQCJggsuWmBGq\nJvIfxCuJTOT8adZeb2XnJrYbRVFAVWlGfTaRM6qEA1KKbnxPcmC4JsXe7J0A\n/VoBKNtUQ3h4+dPeKQF6m4DqHyvV3R2vYl7comah4YOP8nFwMhntcVpdhQTy\naQhTMArDeh1UevY7Y0/07OL4XWtNQXrpC9RzNtfpuH+OK1XpnBga327tuBTY\n4KRRaXBKHGccjHXwVh64fVqpEDA8pw3YLX5h0CmlD+6N0NaPm9xC2YWL/RBb\nFjDNM4Hoj3fR6hw0B2W6zIT8ouO8WHljKpdHfwVfc6J5xk7G+/rcT3+gNqpn\nabJ+\r\n=uE/8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCCY2TH8SKODb6l0s+9K5WcJtLtEXSZQDkMpuoUoTs9pAIhAOr6D8QiYuUwEwJWHIUEnMXxaOothHwLaRKxMHiNFjXE"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/base64-js_1.3.0_1524006718050_0.18945706841846088"}, "_hasShrinkwrap": false}, "1.3.1": {"name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "version": "1.3.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "devDependencies": {"benchmark": "^2.1.4", "browserify": "^16.3.0", "standard": "*", "tape": "4.x", "uglify-js": "^3.6.0"}, "homepage": "https://github.com/beatgammit/base64-js", "keywords": ["base64"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "scripts": {"build": "browserify -s base64js -r ./ | uglifyjs -m > base64js.min.js", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/*.js"}, "gitHead": "08a344d6ca13772acc42df2515312ceac75b5456", "_id": "base64-js@1.3.1", "_nodeVersion": "10.16.0", "_npmVersion": "6.10.2", "dist": {"integrity": "sha512-mLQ4i2QO1ytvGWFWmcngKO//JXAQueZvwEKtjgQFM4jIK0kU+ytMfplL8j+n5mspOfjHwoAg+9yhb7BwAHm36g==", "shasum": "58ece8cb75dd07e71ed08c736abc5fac4dbf8df1", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-1.3.1.tgz", "fileCount": 5, "unpackedSize": 9181, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdSQSCCRA9TVsSAnZWagAAXxkP/Rww2syGgfx2jR5jRkDe\nw4Y7Si6d0NbTJCM6hp2NNay1QIwudUqnLQ3+PZuCP+x0miXqugpSWA5f/bfz\nb8eWCkcOTEzx4LDutyU5V2LPJr+zTM59MMhmFtnoYuAe0lLFi6scMCYllgMr\nb/0YXz//JveRXRoZQxvnNv/Ls3zoYMTEBt3hIAoZBtK6J767U0idnHuJKNH6\nhtpq33DYFCnkW/nJcaDZLrP3L91RKQnaymA2/5K61aP0kr2Z+S+PFC6apMrY\nK4JOmpfVAeaFNeEylPuhS7tBvIGJIhIKqYvivPXISFpUY8uh9YMuBV3lGyiz\nFAeycuk3hTuyfby9XYstClsiH22MB2yr4UwcsF1vA1V9kOPri8DGJjVEVHgX\n8ABVsgmre9t2dp+xA6XH4gB7c/b349odpdympgO2woRq65vL1fL61nU/dYkO\nmPIqDDTPrd6KkY/G1s7K5wnSgG1SxN2zALdADzQWm2we1rFoYcCvGNCALqRr\nql5fE3Eya/C0tfSB2i82NSM2izyqmNfMjYwzqRh6InbI4l2HVKKr8UJ4t2b/\nkqxm51/+JThsBov+u+2woRKTpKDlO9V2XlpEO95CGcSBZ7XhB5OwfHONGbN/\nIKqa2oRJNaui5oYaf5BNI0//yqRkW2rghFpjIcS5uI8Lxwc22GU0SKEpEfFj\nZzHU\r\n=QxgS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDws6HH2rz5vOGNFNRtU/9WtsBT3AnlXne+t6XVaE4gJgIhAOt/yZo7FXb18hXXlq6wm274LB/vdsfv/14DsRX3GLsJ"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}], "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/base64-js_1.3.1_1565066369546_0.47886809614728953"}, "_hasShrinkwrap": false}, "1.5.0": {"name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "version": "1.5.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "typings": "index.d.ts", "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "devDependencies": {"babel-minify": "^0.5.1", "benchmark": "^2.1.4", "browserify": "^16.3.0", "standard": "*", "tape": "4.x"}, "homepage": "https://github.com/beatgammit/base64-js", "keywords": ["base64"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "scripts": {"build": "browserify -s base64js -r ./ | minify > base64js.min.js", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/*.js"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "gitHead": "806f6c14983f059b09a858dfbab722ee1f2c4ebc", "_id": "base64-js@1.5.0", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-Jrdy04F2EKcNggUDfubMUPNAZg2vMquLQSm8sKLYJvz40ClFL1S8GKyDshGkNsbNNE5Z+fQavzU7nSK1I9JUGA==", "shasum": "2d03045876d9e2b68a7a0f87d6bd163595e3b6af", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.0.tgz", "fileCount": 6, "unpackedSize": 9708, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfq07gCRA9TVsSAnZWagAA9zYP/1RxDp+0U5SidtoDmoWs\ntbiLKPMY6gAK42TtzTAx82niBmsAv7x5eizg4Kn2ormxccTUh5cpC1Jm7sJy\nJxc1K0CiQjye/RL3Pp2hJK0kLK2ptDg16z2xrdoxvpVno4qJiC7LQqBJ5pun\nJkZ9gtFZG1bb0faz7IvtO5ArgWgw0JDQj1PKTuoV26Mji9t40tLRPz80vJKN\nQE6ZinkvN36zIblK1G6RXjXw2EB/Nk8StwMSUg1DelpUBeuzxfVXwmIfKmGR\njGMqlHR5qT1Rk8HVOFRPLBDqgRUpe3gq8BEy0cWFLgZ2NB8lwdiJmbA3iRL0\noIfUjW6NrOOskJ4Zkh/njp+LiCkhStsfqA6KahVB6Gxk5QfwIt7juJLZMG0v\noC1xeyk/Q6coxeWE6Lp/XIResUy9IJgT6cel3TX3hAWWc+2/O0r8Yfj6BK97\noAdQOFg4D7jEZ2bUl4bGj+YcdTSISj7PNe8Fa2CoM3a0/7L9J9h4RjwW8fo4\nGyeIlTy27A6MiMcXe6NF3a7YXKuE0n379Llg0CLmCmNT9LqyCJKa1OChjUrB\ntpRwVrzMVaHHP5ErzYJWxvrTZw7rz5vftqlDL5Usuf1XXX5+NMea5yQW7mTL\nrpCojqGHeCtygPbCO/ImqwOqlHJCTR9sX+syHWDfzziTlXUjgBe5pIwNqVdy\n/GW7\r\n=PH8h\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDwLVBbS2BmmZFqGYHAoBtlpt5ozZIhy692bfNmsyiM1AiEAnOi0fWl2mCGFug7SruCuco+S4updvoJKJ6KBt47cP8g="}]}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/base64-js_1.5.0_1605062367755_0.5675037817908752"}, "_hasShrinkwrap": false}, "1.5.1": {"name": "base64-js", "description": "Base64 encoding/decoding in pure JS", "version": "1.5.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "typings": "index.d.ts", "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "devDependencies": {"babel-minify": "^0.5.1", "benchmark": "^2.1.4", "browserify": "^16.3.0", "standard": "*", "tape": "4.x"}, "homepage": "https://github.com/beatgammit/base64-js", "keywords": ["base64"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "scripts": {"build": "browserify -s base64js -r ./ | minify > base64js.min.js", "lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/*.js"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "gitHead": "88957c9943c7e2a0f03cdf73e71d579e433627d3", "_id": "base64-js@1.5.1", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==", "shasum": "1b1b440160a5bf7ad40b650f095963481903930a", "tarball": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "fileCount": 6, "unpackedSize": 9624, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfrDycCRA9TVsSAnZWagAA4K4P/jUkg1tL6K4cH2vhbIAa\nlpaYlBFb6Gyr8HFuzdCUMrCJJdMo+dD36yOliFfGOUJFGrLg24T0OGt28VFK\nYSnAJkWKZf9e7pcJdwjXpzk/qlnGieN271x5VPSUrt7WDjEExV7icyldgnvs\niUzl68M3gq6e4AIAJEid5HXWDHE+FqZRLVQyw5gu8mo+AhFv2PHgZJ/6iUiA\nHCUrebU0+U2QcHkrmMs+NiLC+ND9wFg5z6cjP+NLyyWxhHyAD8cFXYzZmju2\ndGwJ2RGTmMhHt4wbK0St88xNh3LD8b1gohwlUFrl9dRXTnqrDL7io9cC9FIK\nd4qUsB2MPvT7QriDNx/wTzU0HTuHiLRjLi1ZRkBDuVwmmK7rerTKvQzblZTG\nhBPxuVebPqYd8826yK0ms2REW9yMx6MetBgo8aN35FWD68y0g+S1mABZpUJ4\nAxrbg78AvhvpmXYi4q3WEDWhW+KeNhJfzWutslsl3icpHHcS2fvOT/6GGQ6X\nT+rhFyB+AVQztxyGPRYzNtzn0W0TdCrrQQiIYL9f+VTRF9wI5aabRlsJYT5m\nnRxC5otbtLY6BcH09UbnrnMl8xbqXiRDgqHFZ+FK6B04MHMgZLjBpN6GZ6ur\nDF4OXCnjBiOG6uhm33YpSQ+uyToPQ213Uzu0wD8ADYv4pYrdBaPNnRrRUtnm\nDV1r\r\n=Eusy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICpALIW/fap3pR3PHVh2gwoLxd43KF3JMPKBjO1vOVyDAiBiu9wQ2nXI/OcbW7lvvpBhNSOcD37Y9k3NQ2oiEhVFxQ=="}]}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/base64-js_1.5.1_1605123228082_0.7874673993922778"}, "_hasShrinkwrap": false}}, "readme": "base64-js\n=========\n\n`base64-js` does basic base64 encoding/decoding in pure JS.\n\n[![build status](https://secure.travis-ci.org/beatgammit/base64-js.png)](http://travis-ci.org/beatgammit/base64-js)\n\nMany browsers already have base64 encoding/decoding functionality, but it is for text data, not all-purpose binary data.\n\nSometimes encoding/decoding binary data in the browser is useful, and that is what this module does.\n\n## install\n\nWith [npm](https://npmjs.org) do:\n\n`npm install base64-js` and `var base64js = require('base64-js')`\n\nFor use in web browsers do:\n\n`<script src=\"base64js.min.js\"></script>`\n\n[Get supported base64-js with the Tidelift Subscription](https://tidelift.com/subscription/pkg/npm-base64-js?utm_source=npm-base64-js&utm_medium=referral&utm_campaign=readme)\n\n## methods\n\n`base64js` has three exposed functions, `byteLength`, `toByteArray` and `fromByteArray`, which both take a single argument.\n\n* `byteLength` - Takes a base64 string and returns length of byte array\n* `toByteArray` - Takes a base64 string and returns a byte array\n* `fromByteArray` - Takes a byte array and returns a base64 string\n\n## license\n\nMIT\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "feross", "email": "<EMAIL>"}], "time": {"modified": "2023-07-22T20:56:09.394Z", "created": "2011-11-26T00:08:51.996Z", "0.0.1": "2011-11-26T00:08:55.134Z", "0.0.2": "2011-11-26T22:06:33.304Z", "0.0.3": "2013-09-21T22:30:59.736Z", "0.0.4": "2013-09-21T22:42:04.050Z", "0.0.5": "2014-01-08T03:30:14.309Z", "0.0.6": "2014-01-08T07:32:26.633Z", "0.0.7": "2014-06-06T21:52:52.474Z", "0.0.8": "2014-12-31T02:56:22.275Z", "1.0.1": "2016-01-05T22:43:49.607Z", "1.0.2": "2016-01-09T02:51:50.377Z", "1.0.3": "2016-02-12T19:35:54.596Z", "1.0.4": "2016-02-13T00:21:55.643Z", "1.1.0": "2016-02-29T17:17:00.146Z", "1.1.1": "2016-03-02T00:29:40.487Z", "1.1.2": "2016-03-24T09:54:59.307Z", "1.2.0": "2016-09-22T19:55:21.036Z", "1.2.1": "2017-06-22T03:02:45.961Z", "1.2.2": "2018-02-16T02:38:52.391Z", "1.2.3": "2018-02-16T02:45:30.027Z", "1.3.0": "2018-04-17T23:11:58.124Z", "1.3.1": "2019-08-06T04:39:29.775Z", "1.5.0": "2020-11-11T02:39:27.839Z", "1.5.1": "2020-11-11T19:33:48.234Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/beatgammit/base64-js.git"}, "users": {"garrickcheung": true, "goodseller": true, "vbv": true, "pandao": true, "loglo": true, "gkucmierz": true, "xueboren": true, "shuoshubao": true, "chinawolf_wyp": true, "zixinliango": true, "shanewholloway": true, "panlw": true, "dillonace": true, "thejeshgn.com": true, "thejeshgn": true, "codeif": true, "mik1986": true, "koulmomo": true, "kkho595": true, "zuojiang": true, "nisimjoseph": true, "flumpus-dev": true}, "homepage": "https://github.com/beatgammit/base64-js", "bugs": {"url": "https://github.com/beatgammit/base64-js/issues"}, "license": "MIT", "readmeFilename": "README.md", "keywords": ["base64"]}