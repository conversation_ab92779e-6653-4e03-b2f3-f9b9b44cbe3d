{"_id": "collect-v8-coverage", "_rev": "3-6fd0c3051681f890f080ea16dde5c35b", "name": "collect-v8-coverage", "dist-tags": {"latest": "1.0.2"}, "versions": {"1.0.0": {"name": "collect-v8-coverage", "version": "1.0.0", "main": "index.js", "types": "index.d.ts", "license": "MIT", "devDependencies": {"@commitlint/cli": "^8.2.0", "@commitlint/config-conventional": "^8.2.0", "@semantic-release/changelog": "^3.0.6", "@semantic-release/git": "^7.0.18", "husky": "^3.0.9", "lint-staged": "^9.4.2", "prettier": "^1.19.1", "semantic-release": "^15.13.31"}, "prettier": {"singleQuote": true, "trailingComma": "all"}, "lint-staged": {"*.{js,ts,md,json}": ["prettier --write", "git add"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "husky": {"hooks": {"commit-msg": "commitlint -e $HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git", "@semantic-release/github"]}, "gitHead": "1514d2c057303cdf3f857302d48fc8a446a5f28e", "description": "Use this module to start and stop the V8 inspector manually and collect precise coverage.", "_id": "collect-v8-coverage@1.0.0", "_nodeVersion": "12.13.1", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-VKIhJgvk8E1W28m5avZ2Gv2Ruv5YiF56ug2oclvaG9md69BuZImMG2sk9g7QNKLUbtYAKQjXjYxbYZVUlMMKmQ==", "shasum": "150ee634ac3650b71d9c985eb7f608942334feb1", "tarball": "https://registry.npmjs.org/collect-v8-coverage/-/collect-v8-coverage-1.0.0.tgz", "fileCount": 6, "unpackedSize": 3840, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd9+2FCRA9TVsSAnZWagAAtE0P/3iSqPyH1H3n7BG3OXCf\nAPzgzaImQ7fmHm0GH1kgru/o9tyWnMDc+nSOJWsUvX/dubX+qicK3bqmxPik\nE3JaA+YWoDFfLe8YvyZZJ9D1ECtVEMb3PdWU2to8S30hnjN/DmXBhotr/0GX\nkkCMxW3G3lzYdrA+XYScxhlBde7i6pLstBmg5d3iHJce2KWSdrajp0oE6NTr\nFeLzNf36roEPw4nJ8nBobAo9RaHMxqbx1WHrmqZDzyU3CnOoMVIsWs4iUe9P\nIfdQCmy4R9GCwhwui/eac1yLjV13JCAq5n3xB/Em/Z7C2ne3RpsFGsZI4fNc\nqaBtZmg6CUNlj6ND+xwkU+BxBitUzjh+JHEXfFiHcPWBCj3Umkp0gBVVbKYi\n7eL7ccnnfM6uyeI7OTMJDPHFXq51rvu3HCvQO3/Al2k2m47bKGY3Cntnrjtd\nERkSfsjv48FL7PqWpjFzSGJuKy7Ja3SCBfjXN4E7d+ys1mP/8c0YA84MRZ0h\nipapFEOjRdBt3uQrgrEEMe4eaxqutlhKKuQCyq7/v5cm62EL0Hq1nusvu447\ny8UVVQBty5odrcNTSDuTmpFsKY/NHCpgb1I8MvK+IrQ5KBUri7X+wEUJW3ic\nM8On33JnpzwVqswqojamtXBVat/IXvV7LAIQtZQIhKk8dgJGygv9HtiwJljm\nVpUd\r\n=ONVe\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFS3Iv3Gn22yFKVLzZy6pJpzjBvrvwf2UGZhe990Iw/dAiEA6QGUSCj7yerrL0h+/T//LWkvTArgyDP82iijYFmEFi4="}]}, "maintainers": [{"name": "simenb", "email": "<EMAIL>"}], "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/collect-v8-coverage_1.0.0_1576529284938_0.9585828598393289"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "collect-v8-coverage", "version": "1.0.1", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/SimenB/collect-v8-coverage.git"}, "license": "MIT", "devDependencies": {"@commitlint/cli": "^8.2.0", "@commitlint/config-conventional": "^8.2.0", "@semantic-release/changelog": "^3.0.6", "@semantic-release/git": "^7.0.18", "husky": "^3.0.9", "lint-staged": "^9.4.2", "prettier": "^1.19.1", "semantic-release": "^15.13.31"}, "prettier": {"singleQuote": true, "trailingComma": "all"}, "lint-staged": {"*.{js,ts,md,json}": ["prettier --write", "git add"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "husky": {"hooks": {"commit-msg": "commitlint -e $HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git", "@semantic-release/github"]}, "gitHead": "88e2e6ad81d2216f76601da2e6af44dae60d93ea", "description": "Use this module to start and stop the V8 inspector manually and collect precise coverage.", "bugs": {"url": "https://github.com/SimenB/collect-v8-coverage/issues"}, "homepage": "https://github.com/SimenB/collect-v8-coverage#readme", "_id": "collect-v8-coverage@1.0.1", "_nodeVersion": "12.16.1", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-iBPtljfCNcTKNAto0KEtDfZ3qzjJvqE3aTGZsbhjSBlorqpXJlaWWtPO35D+ZImoC3KWejX64o+yPGxhWSTzfg==", "shasum": "cc2c8e94fc18bbdffe64d6534570c8a673b27f59", "tarball": "https://registry.npmjs.org/collect-v8-coverage/-/collect-v8-coverage-1.0.1.tgz", "fileCount": 6, "unpackedSize": 4138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehdVSCRA9TVsSAnZWagAAVcwP/2QL4ljaGCBjtjOWlija\nu7kHPZKtLCQ1gjzgJGXw+/vspgrbqcvil8+OBMSz8pHfjqJJA8jDBmPxDdZz\n5YQXSldoA62sotYZXo3JX2W2w4m5SzHGrSbMfX8cwWSV9A/Xcv5/uwk3q080\n4MzwGnaFbH3p8RLUlxYT5qMyoNkv8cnWn5G/hKxw0aFXWpqepFLapDG3L7ks\nSWBZ2zihG7LCpDh0cUy13LmOrCsffCVf/iK8soKc2exg89s5j0bN6qJ5zrha\n+LyWhF5oOf4P0AAp0Nk6DKhA05zCSW1I9AVRuAtBJaj5NZ4IooQhe1iPbD4d\nBTo8ZKk0hkuQE6CRogl83sO6dId5Fyi1LZE0UsyxHCK4a+WjtAyGCLPCEsdG\nk7WZf6IGfJo/oN9AJrOZFcodgdyDSwbf2/frydq2UIcU1317odq9MSvqSdoX\n0Qk6K89rVQX4poQh2AGzY4yB7OnSoxbximt2H8ClB/WqXabE2aqJIetnQD9+\nNwzJFsAHuW7+Oe9sHnUX445XzM2GDL0CYjcFQWQ83VvxmV1OJQWAD8JE+yix\n1BVxrjPixjSjzjDMc6YMjt5mzvkaBxPXe3LhYrjiUio4r7LhU5hCsXh90oCp\nQpG0Gn+fM7+nqJz+5CN6YtCb7tg0eFj/NRjRPqb9+cWSiub9/e93tZUpxYw8\nziqb\r\n=W/oR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHfWO7khgWj0WFGF+BtSw7IwJJ/5qEC4zcARTl1eKe2lAiA/ngkkUA7P3pl9nvXPUOTWRW9PfCV5y/xM0ea6GO+MZQ=="}]}, "maintainers": [{"name": "simenb", "email": "<EMAIL>"}], "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/collect-v8-coverage_1.0.1_1585829194610_0.7075952513675121"}, "_hasShrinkwrap": false}, "1.0.2": {"name": "collect-v8-coverage", "version": "1.0.2", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/SimenB/collect-v8-coverage.git"}, "license": "MIT", "devDependencies": {"@commitlint/cli": "^17.0.0", "@commitlint/config-conventional": "^17.0.0", "@semantic-release/changelog": "^6.0.0", "@semantic-release/git": "^10.0.0", "husky": "^8.0.0", "lint-staged": "^13.0.0", "prettier": "^2.2.1", "semantic-release": "^21.0.0"}, "prettier": {"singleQuote": true, "trailingComma": "all"}, "lint-staged": {"*.{js,ts,md,json}": "prettier --write"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "release": {"branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git", "@semantic-release/github"]}, "scripts": {"prepare": "husky install"}, "packageManager": "yarn@3.6.0", "gitHead": "a47767a9631f64b907d5f975fc8ca50ea133f0f5", "description": "Use this module to start and stop the V8 inspector manually and collect precise coverage.", "bugs": {"url": "https://github.com/SimenB/collect-v8-coverage/issues"}, "homepage": "https://github.com/SimenB/collect-v8-coverage#readme", "_id": "collect-v8-coverage@1.0.2", "_nodeVersion": "18.16.1", "_npmVersion": "9.7.2", "dist": {"integrity": "sha512-lHl4d5/ONEbLlJvaJNtsF/Lz+WvB07u2ycqTYbdrq7UypDXailES4valYb2eWiJFxZlVmpGekfqoxQhzyFdT4Q==", "shasum": "c0b29bcd33bcd0779a1344c2136051e6afd3d9e9", "tarball": "https://registry.npmjs.org/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz", "fileCount": 6, "unpackedSize": 5136, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDbVQiCt6iPicOQGRERSksvQtAaQJQqUt2t6mMEjiTEzAIhAP6FgPDT1PzPTx+eW5JzUP3vCWU7m11u60kaBu4Ius55"}]}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "simenb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/collect-v8-coverage_1.0.2_1688565430887_0.7737884196193994"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-12-16T20:48:04.937Z", "1.0.0": "2019-12-16T20:48:05.170Z", "modified": "2023-07-05T13:57:11.158Z", "1.0.1": "2020-04-02T12:06:34.755Z", "1.0.2": "2023-07-05T13:57:11.058Z"}, "maintainers": [{"name": "simenb", "email": "<EMAIL>"}], "description": "Use this module to start and stop the V8 inspector manually and collect precise coverage.", "license": "MIT", "readme": "# collect-v8-coverage\n\nUse this module to start and stop the V8 inspector manually and collect precise coverage.\n\n```js\nconst {CoverageInstrumenter} = require('collect-v8-coverage');\n\nconst instrumenter = new CoverageInstrumenter();\n\nawait instrumenter.startInstrumenting();\n\n// require some modules, run some code\n\nconst coverage = await instrumenter.stopInstrumenting();\n```\n", "readmeFilename": "README.md", "homepage": "https://github.com/SimenB/collect-v8-coverage#readme", "repository": {"type": "git", "url": "git+https://github.com/SimenB/collect-v8-coverage.git"}, "bugs": {"url": "https://github.com/SimenB/collect-v8-coverage/issues"}}