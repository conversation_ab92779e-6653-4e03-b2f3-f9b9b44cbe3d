{"_id": "get-caller-file", "_rev": "15-6fbb506624f501ab37643bce49ed2912", "name": "get-caller-file", "description": "[![Build Status](https://travis-ci.org/stefanpenner/get-caller-file.svg?branch=master)](https://travis-ci.org/stefanpenner/get-caller-file) [![Build status](https://ci.appveyor.com/api/projects/status/ol2q94g1932cy14a/branch/master?svg=true)](https://ci.a", "dist-tags": {"latest": "2.0.5"}, "versions": {"1.0.0": {"name": "get-caller-file", "version": "1.0.0", "description": "[![Build Status](https://travis-ci.org/ember-cli/ember-cli.svg?branch=master)](https://travis-ci.org/ember-cli/ember-cli) [![Build status](https://ci.appveyor.com/api/projects/status/ol2q94g1932cy14a/branch/master?svg=true)](https://ci.appveyor.com/projec", "main": "index.js", "directories": {"test": "tests"}, "scripts": {"test": "mocha test", "test:debug": "mocha test"}, "repository": {"type": "git", "url": "git+https://github.com/stefanpenner/get-caller-file.git"}, "author": {"name": "<PERSON>"}, "license": "ISC", "bugs": {"url": "https://github.com/stefanpenner/get-caller-file/issues"}, "homepage": "https://github.com/stefanpenner/get-caller-file#readme", "devDependencies": {"chai": "^3.4.1", "ensure-posix-path": "^1.0.1", "mocha": "^2.3.4"}, "gitHead": "4a78c55d0f877c75ff7739f403724887c3230ec6", "_id": "get-caller-file@1.0.0", "_shasum": "2215e877ed56cdad79c9d693f5732436964803c9", "_from": ".", "_npmVersion": "3.5.2", "_nodeVersion": "5.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "2215e877ed56cdad79c9d693f5732436964803c9", "tarball": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-1.0.0.tgz", "integrity": "sha512-Rmvo5SF3tofV1DPgc8m/f4NnySWC2NleNAWYQCBqUY6yF2V5LWa8ipt/9OnH2jOU4Q2Oa/sPRVnucRLlvfQLmg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICSwneluT92CCEaK79EFtCjD4U5ocs9BeF4JcTVhEzjbAiBhz0yd4V05TVKfQPuF42RJRHe2TSQm/YYuq8ZRsYcRGw=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "1.0.1": {"name": "get-caller-file", "version": "1.0.1", "description": "[![Build Status](https://travis-ci.org/ember-cli/ember-cli.svg?branch=master)](https://travis-ci.org/ember-cli/ember-cli) [![Build status](https://ci.appveyor.com/api/projects/status/ol2q94g1932cy14a/branch/master?svg=true)](https://ci.appveyor.com/projec", "main": "index.js", "directories": {"test": "tests"}, "scripts": {"test": "mocha test", "test:debug": "mocha test"}, "repository": {"type": "git", "url": "git+https://github.com/stefanpenner/get-caller-file.git"}, "author": {"name": "<PERSON>"}, "license": "ISC", "bugs": {"url": "https://github.com/stefanpenner/get-caller-file/issues"}, "homepage": "https://github.com/stefanpenner/get-caller-file#readme", "devDependencies": {"chai": "^3.4.1", "ensure-posix-path": "^1.0.1", "mocha": "^2.3.4"}, "gitHead": "f37118eb1f54cacee2ff0c39276fa15c56dd3b4b", "_id": "get-caller-file@1.0.1", "_shasum": "aa6ff7b98a1b22dc0c8b3b905fab32b552f5ac41", "_from": ".", "_npmVersion": "3.5.2", "_nodeVersion": "5.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "aa6ff7b98a1b22dc0c8b3b905fab32b552f5ac41", "tarball": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-1.0.1.tgz", "integrity": "sha512-evqJP7KfI4NlbouMvZyPRgglPUT1r1QegupN7b6XdBPafi8n+owOs9gp+zehFWgLsf2JpWYg53UXVyS/CCDH7g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDT5JAQ63+IVEurb5dkWGG4uXHn+raOcCtwsvJNfcPw8AIhAOsl4ehdyzIfmIWk/dCpNmAeZie2k8P1LTa+Vh4EDUFr"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}]}, "1.0.2": {"name": "get-caller-file", "version": "1.0.2", "description": "[![Build Status](https://travis-ci.org/ember-cli/ember-cli.svg?branch=master)](https://travis-ci.org/ember-cli/ember-cli) [![Build status](https://ci.appveyor.com/api/projects/status/ol2q94g1932cy14a/branch/master?svg=true)](https://ci.appveyor.com/projec", "main": "index.js", "directories": {"test": "tests"}, "files": ["index.js"], "scripts": {"test": "mocha test", "test:debug": "mocha test"}, "repository": {"type": "git", "url": "git+https://github.com/stefanpenner/get-caller-file.git"}, "author": {"name": "<PERSON>"}, "license": "ISC", "bugs": {"url": "https://github.com/stefanpenner/get-caller-file/issues"}, "homepage": "https://github.com/stefanpenner/get-caller-file#readme", "devDependencies": {"chai": "^3.4.1", "ensure-posix-path": "^1.0.1", "mocha": "^2.3.4"}, "gitHead": "7545c5b0b7f55a1bfffea4a57e3671d771372025", "_id": "get-caller-file@1.0.2", "_shasum": "f702e63127e7e231c160a80c1554acb70d5047e5", "_from": ".", "_npmVersion": "3.10.5", "_nodeVersion": "6.3.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "f702e63127e7e231c160a80c1554acb70d5047e5", "tarball": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-1.0.2.tgz", "integrity": "sha512-A6srK23btrgde1mUYEzplvRPjdwkZXrHsIRNRZnG5p8ZEJHG+QB8ENw16MtH7NWiyDGiSF2giAlJpcls/y2wxQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDRnJFfv9UBSMH6bG7+jn86igdyBp8hXaYAh95/BUDtoQIhANzh0qwf5HIzVKfGreLvhk9sjF+hO2rwuj9zz7xlVc1t"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/get-caller-file-1.0.2.tgz_1470703947025_0.16134989843703806"}}, "1.0.3": {"name": "get-caller-file", "version": "1.0.3", "description": "[![Build Status](https://travis-ci.org/stefanpenner/get-caller-file.svg?branch=master)](https://travis-ci.org/stefanpenner/get-caller-file) [![Build status](https://ci.appveyor.com/api/projects/status/ol2q94g1932cy14a/branch/master?svg=true)](https://ci.a", "main": "index.js", "directories": {"test": "tests"}, "files": ["index.js"], "scripts": {"test": "mocha test", "test:debug": "mocha test"}, "repository": {"type": "git", "url": "git+https://github.com/stefanpenner/get-caller-file.git"}, "author": {"name": "<PERSON>"}, "license": "ISC", "bugs": {"url": "https://github.com/stefanpenner/get-caller-file/issues"}, "homepage": "https://github.com/stefanpenner/get-caller-file#readme", "devDependencies": {"chai": "^4.1.2", "ensure-posix-path": "^1.0.1", "mocha": "^5.2.0"}, "gitHead": "8519eaca478b51b44e575617759a503dcf13a345", "_id": "get-caller-file@1.0.3", "_npmVersion": "6.1.0", "_nodeVersion": "10.6.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-3t6rVToeoZfYSGd8YoLFR2DJkiQrIiUrGcjvFX2mDw3bn6k2OtwHN0TNCLbBO+w8qTvimhDkv+LSscbJY1vE6w==", "shasum": "f978fa4c90d1dfe7ff2d6beda2a515e713bdcf4a", "tarball": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-1.0.3.tgz", "fileCount": 4, "unpackedSize": 2479, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRhDSCRA9TVsSAnZWagAABCsQAISfJUg79/mpbXNlCcnf\nx5XH8U998GO/e1jUn+D+tJw6PqhPYMRjjfcWvDApP5XijXEPN1VxedAW6cCD\nQYNFH1M+KMQ/APV6vB6w5aqcDmrkY7F8kD0KdWen/xoOs7aeBhIlgJOIJRmI\nxPvT3JQB9tKaBu5Jit/TQfRsA2YtF1WjwvoRowu8v8Lg0facAeE9Pvt++mGm\ngKl5e0jgEzHvl4RKoit5HqC6c7N8MziTsgVOoIQb+jpCggJTPzxfu6h5hJcU\nQw4pqxYdt0vWvzxiG1owgt015O14dq2F9CvInYELcCjsjKdskBEI4iiXFa4v\n2KFxKfUy8v1He51AlEhCVEzTI53rc2EUUedxgcC6x6Z0yXEwoyF+jbtugDTn\nApmL268vD5yW7bGi3hMT11QzJidNrioi4itEc4zg/BaZC/UTW1Y9HNe6fPOE\n42MDIfd95tTHJhXVPDKjo5p85aZj2cg506PiyMgb6z9ONMsDbXlp4/x1YuzE\nfWVHefdm1Nx0fIWwVapoiEa2ZGqDLpVtxPJt1tLnmutfCR+j/GlfQr+BJ1XS\nSFQEMw0DTpBZRbXrytfO6OMjwNajlHq2FH2cSgq7bHzV4RfwI900TRudwp+S\nK9H7JpbJxWMS4x41erfD65O5NM0iLITnEeN49wC+SheDhVk/bXUoIx1sPfJj\npQXC\r\n=ofq4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGmXceJn3rcOnjD+T6U33Fuftpj/5Cshl74f7zz68lgvAiEAxd6UOqmbTF9B8kQyznobZ6lCLbsrS8/u9wGgC2omOTI="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/get-caller-file_1.0.3_1531318482467_0.29196568362695974"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "get-caller-file", "version": "2.0.0", "description": "[![Build Status](https://travis-ci.org/stefanpenner/get-caller-file.svg?branch=master)](https://travis-ci.org/stefanpenner/get-caller-file) [![Build status](https://ci.appveyor.com/api/projects/status/ol2q94g1932cy14a/branch/master?svg=true)](https://ci.a", "main": "index.js", "directories": {"test": "tests"}, "files": ["index.js"], "scripts": {"test": "mocha test", "test:debug": "mocha test"}, "repository": {"type": "git", "url": "git+https://github.com/stefanpenner/get-caller-file.git"}, "author": {"name": "<PERSON>"}, "license": "ISC", "bugs": {"url": "https://github.com/stefanpenner/get-caller-file/issues"}, "homepage": "https://github.com/stefanpenner/get-caller-file#readme", "devDependencies": {"chai": "^4.1.2", "ensure-posix-path": "^1.0.1", "mocha": "^5.2.0"}, "engines": {"node": "6.* || 8.* || >= 10.*"}, "gitHead": "82a600d4d720645bd6e129518f0daa2d61ef5509", "_id": "get-caller-file@2.0.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.6.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-cF41L/f/7nXpSwMMHMY0FIurpTPZq/eHwJdeh2+0kKYhL9eD7RqsgMujd3qdqvWdjGIHjwvd/iEMTNECl2EhzA==", "shasum": "1e119be08623cdb28fb6b2873e671a758aa2b6eb", "tarball": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.0.tgz", "fileCount": 4, "unpackedSize": 2736, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRiE7CRA9TVsSAnZWagAA0sYP/3DWx4Gjv6wzxuaRFG7T\niPU8Tqf/1xA4R1oJpg/MiPBaTmKyENzcJokRVSmfu+GprVtvXoVekou6TQM/\no/03NUIOja0lLHdN/owyIo7dHHNJu+XKfKWnm+OixCLcJTsIgW6G0uLHIoxh\nYIKv6PLMhYgSUx3fgjDcdTGrRa+NI2Xab7tkbHrouTXKj/3eTt3BwlatPvs7\nFnI8nraO1orXO8ja8WCERqIkT0vA6ORh8S2/EmcLIra6N2F+lVq2yPmQSUgd\n8PeyHIcvF7MdJgKmGkB52INLxgvI7uMGLDPJFm0jfIMGGpdX/hIzmujativF\ny01uzrsvbfict2bz4PJ8jEgN1v2Hrpzk5TSz/IrJa/DvD7ri9WOaTjuhWd55\nUquu3JcoJfTFEFSp6nVrnizlpN8fOcD39MXAuBhS7K8AEm87K3FBow1YFPaA\nPIxyePUDUjdUaXInQdnck9sPFhY2QoMSLlU7/zDqkwULnO8VVlFy9M0OMQvE\ntZbwN0KwOgomq5g5V6z4x5GWE9Sfy0MArHYxF2OS7y6rgYt4PALmE+yL41QS\nUDCTD6oFzkT8pA5yhxhPaSg33m4QcbFFT4G1C/ljIRdkpGloSiAGC0ZsXRq5\nVxpJVeH+uyiX61k7c3K4KMBTfkpZ6tpjTuk1kO3MYmTPA6bakE/Erd97fmj/\nbo2g\r\n=OmW1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCq9Tv5wLlY9ipfFayDtQ3A4+I/KLgW67ZwZ08W646OFgIhANIkOLUX/zBTJ/fRLsQhQ4O2smdr+4/FhaK7bixOtiu0"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/get-caller-file_2.0.0_1531322683282_0.1393367369930969"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "get-caller-file", "version": "2.0.1", "description": "[![Build Status](https://travis-ci.org/stefanpenner/get-caller-file.svg?branch=master)](https://travis-ci.org/stefanpenner/get-caller-file) [![Build status](https://ci.appveyor.com/api/projects/status/ol2q94g1932cy14a/branch/master?svg=true)](https://ci.a", "main": "index.js", "directories": {"test": "tests"}, "scripts": {"test": "mocha test", "test:debug": "mocha test"}, "repository": {"type": "git", "url": "git+https://github.com/stefanpenner/get-caller-file.git"}, "author": {"name": "<PERSON>"}, "license": "ISC", "bugs": {"url": "https://github.com/stefanpenner/get-caller-file/issues"}, "homepage": "https://github.com/stefanpenner/get-caller-file#readme", "devDependencies": {"chai": "^4.1.2", "ensure-posix-path": "^1.0.1", "mocha": "^5.2.0"}, "engines": {"node": "6.* || 8.* || >= 10.*"}, "gitHead": "b0968745fc849de776807c68e0b716bd8f9e7e0d", "_id": "get-caller-file@2.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-SpOZHfz845AH0wJYVuZk2jWDqFmu7Xubsx+ldIpwzy5pDUpu7OJHK7QYNSA2NPlDSKQwM1GFaAkciOWjjW92Sg==", "shasum": "25835260d3a2b9665fcdbb08cb039a7bbf7011c0", "tarball": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.1.tgz", "fileCount": 4, "unpackedSize": 3470, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcEAcYCRA9TVsSAnZWagAAcDsP/0rTijq/Hrr314fpdEQ6\nOscA0xplFjBUN92xrZkFRTr3eGq9bIyGoDEF2F4xvvVoK68L6X0NZp9N5PM/\nuafO9QcKwrKfrFSHmmQUju7LyMuTPX0vLiD2ly7WjaLClwOWnkXzI4StMooJ\nZ8yoxhmG7NyZspB2CLbP8SJMp+56Q3wOOgAt7aYpwC/6ThRcKLhTVxDnCnL1\n2nTTOWNhDysC4BEbFW6oMTC7vfyukU3GYBSWactKSxtj4aJ9Mi9q1GdQ4DFp\nANSxEEQcHFq7RCGDRjJukF/nezQRYgSI8cPK0UFjTqiYiaLCtz02N1qxG5cB\nOzjGD3yET9cMfM4rc2ipcwvpXOY+ZkaGkH3RYLfvvSPGcCHAuXd+glgwhglR\n0rLOQhlMzfxvkrJmJ6X3J5cgRpfWSXPDY9Q2+3iJ0/G2FTn83cBx+DyF22Sl\nC6vjjQqhY3VFydpTOuzDyIyLtnPh2nW1+GdH7WHxoYxeSU+W/Ttq7157Cx5+\nmt9bPTzVkN+ono/PHjyWkcqahDw3SNP8tQ0akya2EHH/25Cszi1rJsLqpoHz\nRB702BWmrKHkd0Ad0hNiDBDfJWOcptumh+PmhMEEgyWbfk2t4SA2K+68veW5\nEjheWb7UfXs7H6+jnufO8iTvmrD/p5YzYPi+2Rc0fa9oYZrTD6X5ipjXqQE4\nfeey\r\n=YFJg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDiqpV40ChzZfe27KwL/hcJQmBt1MhGRQD6NFryukekwwIhAJ5lWLXUkvFEAJHng9bFvumvATSyONra3gQQ6eVac/kS"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/get-caller-file_2.0.1_1544554263914_0.918847719521797"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "get-caller-file", "version": "2.0.2", "description": "[![Build Status](https://travis-ci.org/stefanpenner/get-caller-file.svg?branch=master)](https://travis-ci.org/stefanpenner/get-caller-file) [![Build status](https://ci.appveyor.com/api/projects/status/ol2q94g1932cy14a/branch/master?svg=true)](https://ci.a", "main": "index.js", "directories": {"test": "tests"}, "scripts": {"prepare": "tsc", "test": "mocha test", "test:debug": "mocha test"}, "repository": {"type": "git", "url": "git+https://github.com/stefanpenner/get-caller-file.git"}, "author": {"name": "<PERSON>"}, "license": "ISC", "bugs": {"url": "https://github.com/stefanpenner/get-caller-file/issues"}, "homepage": "https://github.com/stefanpenner/get-caller-file#readme", "devDependencies": {"@types/chai": "^4.1.7", "@types/ensure-posix-path": "^1.0.0", "@types/mocha": "^5.2.6", "chai": "^4.1.2", "ensure-posix-path": "^1.0.1", "mocha": "^5.2.0", "typescript": "^3.3.3333"}, "engines": {"node": "6.* || 8.* || >= 10.*"}, "dependencies": {"@types/node": "^11.10.5"}, "gitHead": "09122abff66fc418c6d390a791cc0130af173663", "_id": "get-caller-file@2.0.2", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-82iVSg4LHRlzeGwTE4zbtbx1mlI4ZZvInyDngMKuIqKWrVdXB5iKGSivhx2dyfI8NCngSoyOWgJJRc4qufO2JQ==", "shasum": "823e4a222500fb0d059fb01d79b9fc7c6e531859", "tarball": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.2.tgz", "fileCount": 6, "unpackedSize": 4743, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgzxqCRA9TVsSAnZWagAA3yAP/0vmdPixQo+9DQDVPkX6\njVFzjvI8awzM2h4zPUODm8JZDR+wkuwZLJ2BVMYMEONCu5yu+c68ZQNvUb2B\nbwkZxHMVtG92PoHzvJUeiD9LRvel0yW7jd0Us2YqtakQ2nwE1pMYwtK/WY55\nTg5QJ1fxMbnjDie4L7p7qb9n0A3uQHiRNHMQXSz/OaG1O5SlvMtf109Hgc92\nTxNPFaOrVmevrX0uIWQyqv5gDQM4C5hTSufijELWpfZ1RSEZ5ibGEPPBjC1t\nZt7sJK4Wt3Cs4iVeu+4J8/k5Vit8TCKSzdAbScqQWPHjQx2KtrxU+KaNwW+S\nECNMi4oK38J7DYxnEQamEtGWNxodORu6clSgOEfzpIekTW2atPj5lo14MRAH\nYKR1AxQnE6bBSyzBTOywa/WecF1ANiHQQt6DN3vvn2Mm1LmIfKJNye9hfWy/\npNi2wFPno2O3wk+1/ELaGdNtbvv5XptmCfFRyQkVLrJEnLjPVgJFWZvN7jvZ\npcZL3LY0RUH00B0VCKforbhoZ4QpSuKvOtOGFZmgkctKn6ptZW4t/13R4L9h\nHaRLKWK1NWg7ScpGgRBvi63SJL39KJRLdiK/eOJ1l6L4fkQo4vlMQ1nLAQLt\nkZQXd9bg9eQX/v2D6A5bwh+SZp3vkjGD015hdYJlB6phrSOMfsdGkpO+hxiM\nM2xt\r\n=2pNl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFbXgqGn4EsQ/HOmUfAJy8y/g19PxrpX5QI9uEqfQ0N3AiEAiWksm4KqxswCT8SXX7tprDEoSgd1K33vGZEwew2C8Gk="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/get-caller-file_2.0.2_1552104553673_0.23167214839187245"}, "_hasShrinkwrap": false}, "2.0.3": {"name": "get-caller-file", "version": "2.0.3", "description": "[![Build Status](https://travis-ci.org/stefanpenner/get-caller-file.svg?branch=master)](https://travis-ci.org/stefanpenner/get-caller-file) [![Build status](https://ci.appveyor.com/api/projects/status/ol2q94g1932cy14a/branch/master?svg=true)](https://ci.a", "main": "index.js", "directories": {"test": "tests"}, "scripts": {"prepare": "tsc", "test": "mocha test", "test:debug": "mocha test"}, "repository": {"type": "git", "url": "git+https://github.com/stefanpenner/get-caller-file.git"}, "author": {"name": "<PERSON>"}, "license": "ISC", "bugs": {"url": "https://github.com/stefanpenner/get-caller-file/issues"}, "homepage": "https://github.com/stefanpenner/get-caller-file#readme", "devDependencies": {"@types/chai": "^4.1.7", "@types/ensure-posix-path": "^1.0.0", "@types/mocha": "^5.2.6", "chai": "^4.1.2", "ensure-posix-path": "^1.0.1", "mocha": "^5.2.0", "typescript": "^3.3.3333"}, "engines": {"node": "6.* || 8.* || >= 10.*"}, "dependencies": {"@types/node": "^11.10.5"}, "gitHead": "0640ed23fff1924d999ff44ce78fc783b6fbb793", "_id": "get-caller-file@2.0.3", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-uNORi43TS/2dSjrlkjcEKPsich6T24jReMdsYAqXsoshESYDRX0Lb/30It1YAHsApfjeBw6Kr6JTNJnxQouj7g==", "shasum": "26aba20816dc79f546e808aedf12e46ff83e85df", "tarball": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.3.tgz", "fileCount": 6, "unpackedSize": 4743, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgz+eCRA9TVsSAnZWagAA3JgP/io2aq/ZGPHAitPwVfyQ\n/Ozx4FMhWYx6IZAr67etVLsz2KmdLx96G67rdZt/cBFq44JZr1Vi5kx0Hz3/\nuTt7xCWv5bxwXjX62xybQAZc4pKkbhSkiiXjJXhIrM+kFgm1YtLM9YX1yufI\nPcxa2qvH0U0kCKgXWIWwC7i3vZfSO9flc68vYB7c43JXB+L4JuQeThXDqFYK\nssIdS1sT3Pr8QUz5QEvhrr0o32GB1pQBGzEpVDSX6Lu1FONjJw/7LaJy7rpz\nWpdzk2MlOI71kzzFkVsGhT9qXyP27XjAqnHJ+3ZLWNIoKKDNOhE7GVeTFynv\nQOAk7XZg3HrMWLBz/BPQ7DcJCsV2wQ069AM7kUe6Q3iyUVZzBEMX0jqMkEcy\nOoNI0Ih6CB98LkVq+Dfrdbk7aqeghKZ6wzWtjY9cqU/gElcDJyl1dcThNsMG\nVJwA4EcJrMq36Sf8Ks+cq3xc2Qr54d8uXZegQl4tFgoQdIuf8bLSHcowmeon\nAcB3ij8fdS4W9vGgLYv5Y2Dd//jwrExCi8675oT2OuKPR892FsLbXVO3L7+V\nLkF8LCBk4X7NOnmThOcqN85aHYWr8c6ovhwvbnw7P5xSgYl3qpOtex++Hhkv\nX/IMjYLl6O2sClysFQ27ywwFsCWWeaJ+A2dEGVhDTaJWFULJz5iuoYRxDWjA\niZ39\r\n=H2Hd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEoXt0TcDRlw6M8Ch0Jcu13mKZ8mP1YGMwEELgGN1w4wAiEA46XI9/OxPbIybc0pZwxqGlr8FcWch9TBE4uXbywoTtU="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/get-caller-file_2.0.3_1552105373904_0.9808998025668827"}, "_hasShrinkwrap": false}, "2.0.4": {"name": "get-caller-file", "version": "2.0.4", "description": "[![Build Status](https://travis-ci.org/stefanpenner/get-caller-file.svg?branch=master)](https://travis-ci.org/stefanpenner/get-caller-file) [![Build status](https://ci.appveyor.com/api/projects/status/ol2q94g1932cy14a/branch/master?svg=true)](https://ci.a", "main": "index.js", "directories": {"test": "tests"}, "scripts": {"prepare": "tsc", "test": "mocha test", "test:debug": "mocha test"}, "repository": {"type": "git", "url": "git+https://github.com/stefanpenner/get-caller-file.git"}, "author": {"name": "<PERSON>"}, "license": "ISC", "bugs": {"url": "https://github.com/stefanpenner/get-caller-file/issues"}, "homepage": "https://github.com/stefanpenner/get-caller-file#readme", "devDependencies": {"@types/chai": "^4.1.7", "@types/ensure-posix-path": "^1.0.0", "@types/mocha": "^5.2.6", "chai": "^4.1.2", "ensure-posix-path": "^1.0.1", "mocha": "^5.2.0", "typescript": "^3.3.3333"}, "engines": {"node": "6.* || 8.* || >= 10.*"}, "dependencies": {"@types/node": "^11.10.5"}, "gitHead": "27d3f01d09093efb577058fcc765ed4f8dc062e5", "_id": "get-caller-file@2.0.4", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-asZ7znBEyqkVHAjMI14GLeskxIJjn1Gplsvh1neaiHXgqRasKDR4uFnkMQXLMb7+0wn221u5vdY6QBkhgxkx0Q==", "shasum": "b096b4a108afa0a955d62e0572ef3e9fa6c729b9", "tarball": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.4.tgz", "fileCount": 6, "unpackedSize": 4743, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcg0ArCRA9TVsSAnZWagAA6UMP/A1TOpFFJdtY3FrOxQMR\n6mKYsQC9JGpAecs5FC7tk9kIimma+SVl0D2bx0V4QI3QNkScppkZxCtF6DeF\nCob8JC7BaeeKhxVJXsjL4kqq37YflsMCI8FHmsNMbG0nOOOMF6GVPcPYjVIB\nzcbEyix9lPgTjPq5/ZKrWE7Fohf2OBBARlfhBng3qMhdHIhTwFH6vEtaef90\n5VFwN/EhPEbd5P3aaA3h1Ab5tLkCsf2GKK8wBs+iXercaS1Qet88welR/q8k\nfElWXmn/+sDtBrIUJQCIJQRyTVqoMXdT5axxKEwyj2bgNoCf1xELTaif38SI\nR8d4Q/w+F25ZD2Ms5TXccEt18aJShScaCZAP2ePsMzvEvoQP+IlgKxh8ob+l\nzGh3hoJYy9CeW8F5jYTq6L9khYTMusxaTNBkA3E9UJstw01eCzgVD/aLhOac\nYANfusb6Nea8LFEflC7Bwfhr9ATKaVzxIlqYHwX922ko7hDN1T8wnQ22W+7Z\npdP9cClbEklYPXNB2Kl5TDp8PWj0FTOkknyW1cifK8QGJBIE/FklNvQFH3Fz\n+MkszjJEVvz3SoCZggWyIRQB2VMuGvXBFiNt90OQe8q8fdTjobi6feab7Pf9\njgUoAD0DLxshrou2kyCo/gIdsYt+gwa0bT2YXLejO38WBNNpMpSVcisv2Rk1\nWePt\r\n=FlRI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFVoXzymBuIg56KGBjTewy0Uq7k9MBKrlKgVrFrJt4PDAiAveoEpExIX76zQyMNRdmR+xCTtppXV3ZM/nzVhczg+QA=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/get-caller-file_2.0.4_1552105514768_0.5677630345641587"}, "_hasShrinkwrap": false}, "2.0.5": {"name": "get-caller-file", "version": "2.0.5", "description": "[![Build Status](https://travis-ci.org/stefanpenner/get-caller-file.svg?branch=master)](https://travis-ci.org/stefanpenner/get-caller-file) [![Build status](https://ci.appveyor.com/api/projects/status/ol2q94g1932cy14a/branch/master?svg=true)](https://ci.a", "main": "index.js", "directories": {"test": "tests"}, "scripts": {"prepare": "tsc", "test": "mocha test", "test:debug": "mocha test"}, "repository": {"type": "git", "url": "git+https://github.com/stefanpenner/get-caller-file.git"}, "author": {"name": "<PERSON>"}, "license": "ISC", "bugs": {"url": "https://github.com/stefanpenner/get-caller-file/issues"}, "homepage": "https://github.com/stefanpenner/get-caller-file#readme", "devDependencies": {"@types/chai": "^4.1.7", "@types/ensure-posix-path": "^1.0.0", "@types/mocha": "^5.2.6", "@types/node": "^11.10.5", "chai": "^4.1.2", "ensure-posix-path": "^1.0.1", "mocha": "^5.2.0", "typescript": "^3.3.3333"}, "engines": {"node": "6.* || 8.* || >= 10.*"}, "gitHead": "2383bf9e98ed3c568ff69d7586cf59c0f1dcb9d3", "_id": "get-caller-file@2.0.5", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==", "shasum": "4f94412a82db32f36e3b0b9741f8a97feb031f7e", "tarball": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "fileCount": 6, "unpackedSize": 4719, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchDSvCRA9TVsSAnZWagAA+fkP/2U1MPEJqJbpnldZojNu\n2nIJyOVpRXH0N89M8DLqJdG0WL91oWdxkKgPowueS6WSTazZidlvBW+mbVSV\nDR05kIMKl+FPSErdaJRCRia9n8KE5QMHswLou+gGp3B/ih8s+EtATgkbRjWJ\nBHSY1s/Kk7zfg9HN3AAZNnqgbbU7UkEA2CdIazeZ20Pdbv9FI4VeRU0pec03\nJhGy1rz9g84KtdWpd/v7zTHSAvzPNTPHEYAD1WwiiSs8k2V5F4PBGSp3ibE3\nnjSjbvxXq71GZOoL/wyPwoOMp6cnLsWejITymECCHzMh7FdXS7jvfNGP9tIR\nMYOzEekjfmzNKQqFKsEb/PGDZJ3glxdODZYssdNPy7adeHJgLzNyn7lJvlny\nbRtCHv0NaqldlrEP4b67Di/25K/Zm/gBBlz39rR/XwYz8WHAH+mm0CIiDGhG\nM6tRFxV2/FUAQDhOtp1jb+GM+kkCDUewuNyCQnyv7Yjijz/0q+0bPTCP/145\n/YQZctFMjfO7EJV9jZEDkBvuEfnuPUEPMAI5Asnqofvs0X3R1W1DoX5aZpoD\n1ZtfgZmHOj3c+Qg5TlucPjZ1BlR3mD0lCGElM9UwFMKCfza5J53xOjA4fY0T\n5rVVqbCOSM3tTYjaeL04N7xnHy2pikgplCfgZpRK7NmTVARih5Ri+m4jYr0s\nWucD\r\n=ppms\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG9u4KIkcDrP0Rl0wG1LBp0MZzbdp85Alg0IQbkB8jTEAiEA7/PnKH2QuHJAJlbfouS2tFzziPkSi6SMskyl0I6A9FI="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/get-caller-file_2.0.5_1552168110399_0.9644168203659611"}, "_hasShrinkwrap": false}}, "readme": "# get-caller-file\n\n[![Build Status](https://travis-ci.org/stefanpenner/get-caller-file.svg?branch=master)](https://travis-ci.org/stefanpenner/get-caller-file)\n[![Build status](https://ci.appveyor.com/api/projects/status/ol2q94g1932cy14a/branch/master?svg=true)](https://ci.appveyor.com/project/embercli/get-caller-file/branch/master)\n\nThis is a utility, which allows a function to figure out from which file it was invoked. It does so by inspecting v8's stack trace at the time it is invoked.\n\nInspired by http://stackoverflow.com/questions/13227489\n\n*note: this relies on Node/V8 specific APIs, as such other runtimes may not work*\n\n## Installation\n\n```bash\nyarn add get-caller-file\n```\n\n## Usage\n\nGiven:\n\n```js\n// ./foo.js\nconst getCallerFile = require('get-caller-file');\n\nmodule.exports = function() {\n  return getCallerFile(); // figures out who called it\n};\n```\n\n```js\n// index.js\nconst foo = require('./foo');\n\nfoo() // => /full/path/to/this/file/index.js\n```\n\n\n## Options:\n\n* `getCallerFile(position = 2)`: where position is stack frame whos fileName we want.\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-09-10T02:37:49.754Z", "created": "2015-12-30T21:16:55.872Z", "1.0.0": "2015-12-30T21:16:55.872Z", "1.0.1": "2015-12-30T22:51:03.441Z", "1.0.2": "2016-08-09T00:52:30.463Z", "1.0.3": "2018-07-11T14:14:42.542Z", "2.0.0": "2018-07-11T15:24:43.385Z", "2.0.1": "2018-12-11T18:51:04.123Z", "2.0.2": "2019-03-09T04:09:13.843Z", "2.0.3": "2019-03-09T04:22:54.035Z", "2.0.4": "2019-03-09T04:25:14.891Z", "2.0.5": "2019-03-09T21:48:30.527Z"}, "homepage": "https://github.com/stefanpenner/get-caller-file#readme", "repository": {"type": "git", "url": "git+https://github.com/stefanpenner/get-caller-file.git"}, "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/stefanpenner/get-caller-file/issues"}, "license": "ISC", "readmeFilename": "README.md", "users": {"insomniaqc": true, "flumpus-dev": true}}