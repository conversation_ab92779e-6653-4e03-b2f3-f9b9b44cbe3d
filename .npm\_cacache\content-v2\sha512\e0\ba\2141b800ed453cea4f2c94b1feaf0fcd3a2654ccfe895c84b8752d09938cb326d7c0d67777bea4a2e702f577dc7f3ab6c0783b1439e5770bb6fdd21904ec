{"_id": "@types/express-serve-static-core", "_rev": "1472-5f4a9c5cf93a6051e817b7b32eecb21d", "name": "@types/express-serve-static-core", "dist-tags": {"ts2.0": "4.0.53", "ts2.1": "4.0.57", "ts2.2": "4.16.8", "ts2.3": "4.16.11", "ts2.4": "4.16.11", "ts2.7": "4.16.11", "ts2.5": "4.16.11", "ts2.6": "4.16.11", "ts2.8": "4.17.5", "ts2.9": "4.17.7", "ts3.0": "4.17.9", "ts3.1": "4.17.12", "ts3.2": "4.17.13", "ts3.3": "4.17.18", "ts3.4": "4.17.18", "ts3.5": "4.17.20", "ts3.6": "4.17.24", "ts3.7": "4.17.25", "ts3.9": "4.17.28", "ts3.8": "4.17.28", "ts4.0": "4.17.30", "ts4.1": "4.17.31", "ts4.2": "4.17.33", "ts4.3": "4.17.36", "ts4.4": "4.17.36", "ts4.5": "4.17.41", "ts4.6": "4.17.43", "ts4.7": "4.19.5", "ts4.8": "5.0.1", "ts4.9": "5.0.2", "ts5.9": "5.0.7", "ts5.0": "5.0.6", "ts5.4": "5.0.7", "latest": "5.0.7", "ts5.3": "5.0.7", "ts5.5": "5.0.7", "ts5.6": "5.0.7", "ts5.8": "5.0.7", "ts5.2": "5.0.7", "ts5.7": "5.0.7", "ts5.1": "5.0.7"}, "versions": {"4.0.15-alpha": {"name": "@types/express-serve-static-core", "version": "4.0.15-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "_id": "@types/express-serve-static-core@4.0.15-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "daa0c22b62e68c16737a651f517c119a27ee3f7d", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.15-alpha.tgz", "integrity": "sha512-p0aWTVHOaCdlKv8Gr6F1/aTdfKYxjwupSt8Z9PqN6wXZU7VRM7YW8RHXt2jA8j+2lksPRr89yn4sGqhLdgtBdw==", "signatures": [{"sig": "MEYCIQDYZDbDnJTLi9oa9ZqS+hK4pc/P49rAxxjFB21DfBciOAIhANWXRmCMxu4jKk2R3+ebA+nMg7w9aPNxPp7GAX1am8Ii", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\express-serve-static-core", "_shasum": "daa0c22b62e68c16737a651f517c119a27ee3f7d", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\express-serve-static-core", "_npmVersion": "3.8.2", "description": "Type definitions for Express 4.x from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.15-alpha.tgz_1463460832416_0.7022680549416691", "host": "packages-16-east.internal.npmjs.com"}}, "4.0.16-alpha": {"name": "@types/express-serve-static-core", "version": "4.0.16-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "_id": "@types/express-serve-static-core@4.0.16-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "c055cfd310badfadc02a317b8c5d295c7d132d20", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.16-alpha.tgz", "integrity": "sha512-xDS6c2DB7KkP+Gg/Z7Jx3PBh8/3Z85KRZ8Ns9XiZQPoziFJTtLLFbW9arJa3AstNpEGGc22kd+o9fLJKMVtmYw==", "signatures": [{"sig": "MEUCIQCXyjx8bSB6VEAV3PdZDN/5ZUOB0cqFvQ2MNrEEK81WWgIgYsFAf4wzZ9yk7Hj3h2hvgRKlcuvq8cWDeYxH1ktuu/A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\express-serve-static-core", "_shasum": "c055cfd310badfadc02a317b8c5d295c7d132d20", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\express-serve-static-core", "_npmVersion": "3.8.2", "description": "Type definitions for Express 4.x from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.16-alpha.tgz_1463691010738_0.1024921340867877", "host": "packages-16-east.internal.npmjs.com"}}, "4.0.21-alpha": {"name": "@types/express-serve-static-core", "version": "4.0.21-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "_id": "@types/express-serve-static-core@4.0.21-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "0a45584ae2802d59b595c86eaec7f4697ba088ec", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.21-alpha.tgz", "integrity": "sha512-hO6RDkXeQ97PLFMufUgcF6ZfatXspDhSFIsTZPC07jjaHVsx/ooqbetRTRfe8r9QFbg+luHNo9msOMQIVQ4ZoA==", "signatures": [{"sig": "MEYCIQDPErtG+wI248ibdwzi5i1R7TIqQ98o0c4TdPKVqRsSlwIhAKYxTMg/Z/xeTJOzn56RgRU4kXBIf8A0jiT8O9ssX5pz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\express-serve-static-core", "_shasum": "0a45584ae2802d59b595c86eaec7f4697ba088ec", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\express-serve-static-core", "_npmVersion": "3.8.2", "description": "TypeScript definitions for Express 4.x", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.21-alpha.tgz_1463772833980_0.547733178595081", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.22-alpha": {"name": "@types/express-serve-static-core", "version": "4.0.22-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "_id": "@types/express-serve-static-core@4.0.22-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "492b06c5d68f93d57bf82f9a9c048ed7dd174851", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.22-alpha.tgz", "integrity": "sha512-0hJklplohluJ0x8JCIovZCXhfkntijU5M8BQgJgBIXByoIA6TzdY7PQUy/86Wt4v+178fx3BjL/CbDVQ9n8MZg==", "signatures": [{"sig": "MEUCIQCBT+g1o7ERCP8vKa8+kwF+SRpLg0t5lOYlFmCOXSN5ZAIgaFjGiBIwwHkdT5AQrB4w9fI6Jcf02etKkORAVtVgAmk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\express-serve-static-core", "_shasum": "492b06c5d68f93d57bf82f9a9c048ed7dd174851", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\express-serve-static-core", "_npmVersion": "3.8.2", "description": "TypeScript definitions for Express 4.x", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.22-alpha.tgz_1464151936750_0.08951015071943402", "host": "packages-16-east.internal.npmjs.com"}}, "4.0.23-alpha": {"name": "@types/express-serve-static-core", "version": "4.0.23-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "_id": "@types/express-serve-static-core@4.0.23-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "98422ba398c3814e0b539e233ba94b476a2ab734", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.23-alpha.tgz", "integrity": "sha512-OYbbtHzn5HgEg//FY1czqnVTqbmxMpmep/FrxU6yLegMAu5BwL246e+KuJMlUwGYvcw9CqVprqyAM39Jol4RVQ==", "signatures": [{"sig": "MEUCIC1NHKsI/rKRDgn7QU7NAOBFwPtidkIv0P8dAr5ZN2HkAiEAlBL3tmApxM2XctL7P1S/EH14cLugW7GWOI+8jWyF+vQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\express-serve-static-core", "_shasum": "98422ba398c3814e0b539e233ba94b476a2ab734", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\express-serve-static-core", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.9.5", "description": "TypeScript definitions for Express 4.x", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {"@types/node": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.23-alpha.tgz_1467400858415_0.06366645777598023", "host": "packages-16-east.internal.npmjs.com"}}, "4.0.24-alpha": {"name": "@types/express-serve-static-core", "version": "4.0.24-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "_id": "@types/express-serve-static-core@4.0.24-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "a4615fe1c5e27b107b7a46d4e10a20ca32d76dc0", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.24-alpha.tgz", "integrity": "sha512-1R67MnBj5nzbNUcdajsCvJ+llstKdEJZwGNBRj/4mOoylfJ9bkXnRwhhuUfoAqtVm3pcWmKHhzA3wVxSD6EsAQ==", "signatures": [{"sig": "MEUCIQD+zaLUFL/NvGs21LHcRC4dpKaBUTTHvxfWbWXWvd2UpQIgFz/qXFqXBMsILUMJb6qWcuvVVvDw0TLJ5jlf6QYbb7w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\express-serve-static-core", "_shasum": "a4615fe1c5e27b107b7a46d4e10a20ca32d76dc0", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\express-serve-static-core", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for Express 4.x", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/node": "4.0.23-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.24-alpha.tgz_1467412872200_0.6832083272747695", "host": "packages-16-east.internal.npmjs.com"}}, "4.0.25-alpha": {"name": "@types/express-serve-static-core", "version": "4.0.25-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "_id": "@types/express-serve-static-core@4.0.25-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "ba4cd6cd019b7d2a027918fe23127b802229415a", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.25-alpha.tgz", "integrity": "sha512-5yFjK91/SYig19sW4lnT+xAZGxuhp+HKxmI1Q9V2asDH1Dvac0IoIjP6VG2NjUftLHQ3GQkIkYtPICabNRD5Dw==", "signatures": [{"sig": "MEUCIGHrt8gHW9KGxtoLQJJGsKx5QRv+owCr+s/q10o8RRJsAiEA5OdFAAxiWM12d5RWdehh0qqDtpNTE4frmtyeheN7ceM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\express-serve-static-core", "_shasum": "ba4cd6cd019b7d2a027918fe23127b802229415a", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\express-serve-static-core", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for Express 4.x", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/node": "4.0.24-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.25-alpha.tgz_1467426241742_0.5896518013905734", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.26-alpha": {"name": "@types/express-serve-static-core", "version": "4.0.26-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "_id": "@types/express-serve-static-core@4.0.26-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "4124b3989636eed97ceaef8c2833082dedc688c7", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.26-alpha.tgz", "integrity": "sha512-mzn5I6rXdsBgCCqIb089cKVhJ1LfKydxd8XxeN5+qKlzivKrpQbYRuEAZkS+q7Zgpvmy8CZPGanQBXu8iGCmsg==", "signatures": [{"sig": "MEUCIF/GRnnvGEafcLuF1ytgodatESyB0Uao9Xhq2jwX+E5CAiEA5j0OlsF4P/zcu7a1Y6UA5RLXmtfsDl0mlx4jXsrsEIc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\express-serve-static-core", "_shasum": "4124b3989636eed97ceaef8c2833082dedc688c7", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\express-serve-static-core", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for Express 4.x", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/node": "4.0.26-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.26-alpha.tgz_1467588155160_0.4565274037886411", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.27-alpha": {"name": "@types/express-serve-static-core", "version": "4.0.27-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "_id": "@types/express-serve-static-core@4.0.27-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "02e5acddb2659d8fedcdae9d4660c3dd4e6c3b41", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.27-alpha.tgz", "integrity": "sha512-GsmF0rQuoR5SZ1Kj4qLkUsue1cOeieVgG4HAzCpS5VwQFkZ6H5fYvOx9UdIK9OkkxRrmG0a9MmS+YEguYvKhdQ==", "signatures": [{"sig": "MEUCIQCDCguKFux0ArQc8X8YyS9ttwez1Ko+Ox2oX2vmVRZW1wIgaiVUHMm+vc67cSgjTOC+Ln2S7M9l2GPD5FwAW0VnSNY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\express-serve-static-core", "_shasum": "02e5acddb2659d8fedcdae9d4660c3dd4e6c3b41", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\express-serve-static-core", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for Express 4.x", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"@types/node": "4.0.27-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.27-alpha.tgz_1468008159665_0.7746470659039915", "host": "packages-16-east.internal.npmjs.com"}}, "4.0.28-alpha": {"name": "@types/express-serve-static-core", "version": "4.0.28-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "_id": "@types/express-serve-static-core@4.0.28-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "d5b8c82b2a01546682ce6d2a5a70d0083b04be06", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.28-alpha.tgz", "integrity": "sha512-omnvdhytfdns+xy9wfZ3seQiABxqeNXe4oJf68W2oUh4OQVIP3NQGs0Tz7TbsevY2UKsrJGG3ZvQhpCdRIi6bA==", "signatures": [{"sig": "MEUCIGZtst9mbbEI1fbwGJbnHYfEKToyBsSdmzefpU/xdgFpAiEA68y3+l2lX13mP6Wu5fzeCgvPx5poJ1pPrCvrZLYHuug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\express-serve-static-core", "_shasum": "d5b8c82b2a01546682ce6d2a5a70d0083b04be06", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\express-serve-static-core", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for Express 4.x (core shared with serve-static)", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"@types/node": "4.0.27-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.28-alpha.tgz_1468276117486_0.5808218123856932", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.29": {"name": "@types/express-serve-static-core", "version": "4.0.29", "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "_id": "@types/express-serve-static-core@4.0.29", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "1de0c92cf07dfa11040c3f1857f2ab8fb2461cfc", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.29.tgz", "integrity": "sha512-3Am1xQry9fRkfv7z1a7JNLfe5Bccm4ctoHAiSzjX0OrOmASK2h3BQzDCBIPpzOWiiIIYPRNIkxLLHUxnLChpoA==", "signatures": [{"sig": "MEUCIQCc6jwT6X0zZd+YtT4lJ9nytX6hgc+DdgvExhc1DdtyWQIgQDSbTpaSFg2tjF+OrBSa6PIlOckfUS5OnEaZVi9erC8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\express-serve-static-core", "_shasum": "1de0c92cf07dfa11040c3f1857f2ab8fb2461cfc", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\express-serve-static-core", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for Express 4.x (core shared with serve-static)", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"@types/node": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.29.tgz_1468506762157_0.04530668770894408", "host": "packages-16-east.internal.npmjs.com"}}, "4.0.30": {"name": "@types/express-serve-static-core", "version": "4.0.30", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.30", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "b5a20e5761a44d78913d30e23c6067d5918cdd95", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.30.tgz", "integrity": "sha512-om+15Uqfxannae4C9QaBWW+4UPG3OfEZimYJDEkd3w9Uq1FGiRsImFOEbUCyHEzt/8exykSztFosqysquqZMBA==", "signatures": [{"sig": "MEQCID4V/DB9ImHE7l1xBV+doslC2mU5CH8Jx96WeZW0QLqDAiBoUCExVxmVJ217rWW/uQPft0OFbobQM6ASb4DIcR+jCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express 4.x (core shared with serve-static)", "directories": {}, "dependencies": {"@types/node": "6.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.30.tgz_1470153130827_0.7111015364062041", "host": "packages-16-east.internal.npmjs.com"}}, "4.0.31": {"name": "@types/express-serve-static-core", "version": "4.0.31", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.31", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "dd0619b6f177242230155fcbb67a0c25ba235472", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.31.tgz", "integrity": "sha512-WU1hbRDutICR3EOAJW8frhVzLabqw9ukMLOlubaA3prgukjknUW4kFaWlauYNPEI88ENPgcP79Q6PhttfzUkkQ==", "signatures": [{"sig": "MEUCIQCNHFi3O1mIH+OVbNSjvsp7mOfXUMQ1b3deEJfgVOTA5AIgJwjI9+zSG+wx2vlUTcgdoMBLMTwRGjGCBnX0Y1QAa2w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express 4.x (core shared with serve-static)", "directories": {}, "dependencies": {"@types/node": "6.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.31.tgz_1470951838660_0.4865524796769023", "host": "packages-16-east.internal.npmjs.com"}}, "4.0.32": {"name": "@types/express-serve-static-core", "version": "4.0.32", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.32", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "18759cd2d52949f2e0d4473e94f7ad2f7dbb6aab", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.32.tgz", "integrity": "sha512-VcfEOoIV0uLuNrZPZ4nbLiTj36UZF9QSFZvLR3SGKNnikWNb1q637++V38uDNaP9s1vey0raqwPczQD8xVp+2g==", "signatures": [{"sig": "MEUCIAs3jfSL03dM6EFWAJijGBnl1TTq63dcINmpmkvI9QYpAiEA7HeLInOgeD5jAhGhU84F5KQSje+pdCOdevpaUYL38b4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express 4.x (core shared with serve-static)", "directories": {}, "dependencies": {"@types/node": "6.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.32.tgz_1471888675726_0.609777293400839", "host": "packages-16-east.internal.npmjs.com"}}, "4.0.33": {"name": "@types/express-serve-static-core", "version": "4.0.33", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.33", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "e232e30756b866b65be455c4ae06a07e7a360f9b", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.33.tgz", "integrity": "sha512-5Y<PERSON>lsk2P6POfVjiVsKFZHq/vI1ZdTwG4Ab3hC+dryEuImA5Xq887T1BMaczjxEfiQENoIyMTn0UgyyxXQUMhcw==", "signatures": [{"sig": "MEMCIBcL79H4UhGiksI1qHAQ5z3tGA4TiKG403+xlHhn0xIbAh8GNIX663j1gyD0ApDs7IECRKSS04hlk8v8rpcjL7/S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express 4.x (core shared with serve-static)", "directories": {}, "dependencies": {"@types/node": "6.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.33.tgz_1472150497624_0.3928104543592781", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.34": {"name": "@types/express-serve-static-core", "version": "4.0.34", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.34", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "e37facc4c6c63f7ac0d244c8bbc207f1e94452fd", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.34.tgz", "integrity": "sha512-tlWJUuBYQnobWKkHtNTcWEatUCT9BBrIoVfNQ+lbK2rI7uS7hnBiJXtcxEy6H9XYCbopabbTmbQ/O7Y+8DFOFA==", "signatures": [{"sig": "MEUCIBgO2LUBLenbMaY/nCQsfGnh7PERViqEugRspv/xeeGcAiEAt/KEhsiP7BV8NWaKEH2K8gODJxG5xj4Nh8O14JBgziI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express 4.x", "directories": {}, "dependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.34.tgz_1474306383520_0.551261450862512", "host": "packages-16-east.internal.npmjs.com"}, "typesPublisherContentHash": "191be51098afcf2c203c1bc49ed2de29aa0523315d9752051d2e6461abcc6c83"}, "4.0.35": {"name": "@types/express-serve-static-core", "version": "4.0.35", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.35", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "959f1ac2bf3c978f578079dab25336a5682964a1", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.35.tgz", "integrity": "sha512-TURrNkby4ItNrL/pnRch5wmSku6c46xJ2eBHjD61ArplxsZjWesthVz8PHacucvzJHo61nQK9H1Q6F7s+7Y8NQ==", "signatures": [{"sig": "MEUCIGLiFPfxqEiReB1N+SKsMjLB9Z3X2pChhJKKBFf7g8M5AiEAgRLLees+Xtkd7MBR+w8u+9xpXtoXP43e/g+BTAl+w3Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express 4.x", "directories": {}, "dependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.35.tgz_1474489629543_0.5862499882932752", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "3b644d6b10b70565db9bc4dd4d304c5416e910dd370a31d84efb6cde9cf260e1"}, "4.0.36": {"name": "@types/express-serve-static-core", "version": "4.0.36", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.36", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "1f4ab59653ce1bbb79816f4aa3b10e388ad1a331", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.36.tgz", "integrity": "sha512-nhNOayVKFx8dblUGwC90gHyKBtbw6f+0q7YLv+pt8jduw6suRjW1kSfC7+w8EtmSKTczw5XyeTLJf6VO/T0zlg==", "signatures": [{"sig": "MEUCIQCe61j/1+pqoBqPEUhBo4qLVUCWTNkLcWVw0RbfI5lsXQIgf5cjKmGqv6XN0q3YK6vjavYqIVkGGVP+92A/+vJGiAI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express 4.x", "directories": {}, "dependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.36.tgz_1474653571132_0.07099775760434568", "host": "packages-16-east.internal.npmjs.com"}, "typesPublisherContentHash": "82b9b1a34a1e7e3e3519730de18f8203be2803ee859fdee8f42af237daf73fb0"}, "4.0.37": {"name": "@types/express-serve-static-core", "version": "4.0.37", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.37", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "fa15cddc040aa62d76027ce54bab3f287b592a18", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.37.tgz", "integrity": "sha512-MKA6fcjpWQ6mEj3UN2kdhui++J+LiNZBMh8QW/2WCvres5mIzfjN6rNnrQFodok2g2S3oqIIOFg7wdsT/BtECw==", "signatures": [{"sig": "MEUCIQD+Rlg7ceFRLEXLAE2n4SZtos0wGeriHJVkOt2Q9cV7+gIgEwV6Ke34G/j+PZfzOjbD9yRUryAihzLGF6aBVszSsFI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express 4.x", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.37.tgz_1475701010542_0.015708627877756953", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "e173c12f7ee074d75d86b55a0ce504f6c00d5c4064b1203e4fd7f57c899225c6"}, "4.0.38": {"name": "@types/express-serve-static-core", "version": "4.0.38", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.38", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "129e99aaebd97190ff5a14fe690a49a917e6506a", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.38.tgz", "integrity": "sha512-mzHu5sv9/11GE5gFatB0JHMfuTR29ikcN9aNyCV16UtA3fd06x4HLj0Aevghd9o7r7IRAL937jYFwZ6oG4Hrhw==", "signatures": [{"sig": "MEYCIQDcnahU8Rr7MTxUgrmAnww8zpkK8C+Y+Sn9i8cy+MCh8wIhALKAA5UtTcde+ZkSdoKd7gc8KZvEQTNsrJFhZcNPaAgO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express 4.x", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.38.tgz_1477509939782_0.3422345919534564", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "ec87c673a0861b84e8669ce3c4e5d8b4ef0fd8bdd043cac57e77ae102b8e02a0"}, "4.0.39": {"name": "@types/express-serve-static-core", "version": "4.0.39", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.39", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "45157f96480d46f254648f45b2c6d70bd9fc9f54", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.39.tgz", "integrity": "sha512-s+QiQ87Avme1wHifH8FTW0Iz+mDW7KvAVAgwsQeApdE7u1HfbpwUr1PX325SMVwJ3VJhQs/879OLd9ZxvUCi9A==", "signatures": [{"sig": "MEQCIE0eDNGqHgnWXYi/D9NU/K0CLGC9natGcZS1qMgXQKsCAiADvNhohRn+PmN29NpOmCqZRIdWDC/Rg37iuwlz4W9Hmg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express 4.x", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.39.tgz_1478194987844_0.9196334085427225", "host": "packages-18-east.internal.npmjs.com"}, "typesPublisherContentHash": "15926afeb252ccc96e54d2132ab7ef6b3515bf9c172ead7177160a3945cc8ea5"}, "4.0.40": {"name": "@types/express-serve-static-core", "version": "4.0.40", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.40", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "168e82978bffc81ee7737bc60728d64733a4f37b", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.40.tgz", "integrity": "sha512-5xkp7bRwnNULo4Z4qRm2nPCZ229l0HJZHodWUk2J633m6j7VqKuMF8lDC9J9+CSWktigUfadQbh61yNgiVyw0A==", "signatures": [{"sig": "MEUCIQCpWwuflkPfwZ4g3qsPrmrHzU+sVQelwXggOfltQ6YVJwIgKhwx9o2F+K3X7t8bcQqK3G3oFDjrHEM/MH5TgVosw2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.40.tgz_1482886448974_0.32687022583559155", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "893ac8eb217f16cec1d9018c68fcc08501bd4a6e0948d8a8b3659f764d320693"}, "4.0.41": {"name": "@types/express-serve-static-core", "version": "4.0.41", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.41", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON><PERSON>/", "name": "<PERSON>"}], "dist": {"shasum": "05df354cbbe5069b4c089320065870033f41e670", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.41.tgz", "integrity": "sha512-f18rChKpwN6y4j+FrJuSTyvenUJU/9OAjMsGwULYbAVgo+MTACjICkdT0gsq0IeU0qT/vpBTda1ZHsnux4Cizw==", "signatures": [{"sig": "MEQCID6QZ8cSdm7f+xjCfe7OMpyir/03hTzj9CP352YqQfnJAiBe3wpJXlQAVBwq1kHHRnSVAgCE9IC7cdLwzrf5dxyIXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.41.tgz_1488997411758_0.9980792026035488", "host": "packages-18-east.internal.npmjs.com"}, "typesPublisherContentHash": "69e001bb2eaf94ad14ed226df91b0dab94a905a47e8ee01414e788fc858a5817"}, "4.0.42": {"name": "@types/express-serve-static-core", "version": "4.0.42", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.42", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON><PERSON>/", "name": "<PERSON>"}], "dist": {"shasum": "e79fa1827ebd04e741adedd0ddd1f1708c80366c", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.42.tgz", "integrity": "sha512-oMbG71EiPI3s/guADRmXlVy/ITwJKVrwFLifJdZotYhCXEBLlUgR9HELmUrpFaByZljEtCChUUktP6QPINmv8g==", "signatures": [{"sig": "MEUCIDDs6OLBDckdvrhz1oazlX12pKSJ+9VPFp9t29IwvaR1AiEAxmLzuEb21lCwpGnhrwZWQxobV5pqNPz8j+IS6Maop84=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.42.tgz_1490141588201_0.9212554851546884", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "e4ba95604fc9b19807a6a48b7c75bb485a227d2beba71da81ee7559aa3f9cf58"}, "4.0.43": {"name": "@types/express-serve-static-core", "version": "4.0.43", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.43", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON><PERSON>/", "name": "<PERSON>"}], "dist": {"shasum": "22e08fafb9481efd9a502e78a0bb7a2668b45034", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.43.tgz", "integrity": "sha512-GF/jjXTmoXxGn8wnRfr8wvhHFW5jraDTLeKaoq127EdkQQO+YwcDGpDLqjr8rCarwSFWXSzsmmpvgf9NKtUF+w==", "signatures": [{"sig": "MEQCIET8VdxigiHNv+pkjHWjexVMecljaz32jlF1/eOkYjAuAiBL7M2Kqs5uamaMAPIF3xH64jJ00L7KGLNtkASaNssFUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.43.tgz_1490370650080_0.7851104929577559", "host": "packages-18-east.internal.npmjs.com"}, "typesPublisherContentHash": "4839d2c1e2ddd861a0bdd5e4474b623323eee7f7c7b8399338c49d310905eca2"}, "4.0.44": {"name": "@types/express-serve-static-core", "version": "4.0.44", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.44", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON><PERSON>/", "name": "<PERSON>"}], "dist": {"shasum": "a1c3bd5d80e93c72fba91a03f5412c47f21d4ae7", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.44.tgz", "integrity": "sha512-FW4JhuBy9jxBhbFoNpbLN5dSOWVXziVwLMIlmJxSRPB/p6F6EWC+qTzE8ohhxpU0jix1rpuvbsd8Z+W5HcT9fw==", "signatures": [{"sig": "MEQCIBr7tKevHmBRbchvLPM/p0VtoMreBEjdtM8pBS3l2VxfAiAYOdDHX0TMWUSGOAUdYvXNAOs1FBMrQ7Sompic7eDNUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.44.tgz_1490373028582_0.6681200370658189", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "aed3b8def21f69ea83eb72035ed89f7bf45e88d683f6aa3ec8379bb79cf26114"}, "4.0.45": {"name": "@types/express-serve-static-core", "version": "4.0.45", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.45", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON><PERSON>/", "name": "<PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "71bb1f87d7187482d0d8851f5b294458e1c78667", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.45.tgz", "integrity": "sha512-qZvTzHvrBeZlzpjj3dROcUjOlwphQFwlRzWdxM6B0lB/E2qF1V/BISlbRvIoGQp3ixtqV1uME6qw4KP1kNoqQQ==", "signatures": [{"sig": "MEYCIQDqXnzS+nLoq0/8Y1mUPoOHXWJilfVacYbe87bzJt+o6wIhANbk+VhmMBUiyWededBRCJUlME9Y4Q8U48eJ2WY6IbEq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.45.tgz_1495226850192_0.4774082398507744", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "25e11a4da540d31f3bb528fba4ce5f253b26eac7fe7d459ae4953e5a16b6a4dc"}, "4.0.46": {"name": "@types/express-serve-static-core", "version": "4.0.46", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.46", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON><PERSON>/", "name": "<PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "52040d5e37da132296e333be79e3befa1b02b34a", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.46.tgz", "integrity": "sha512-dtJos9GpTYqX/LBvpu8xHxOQTeAXEgFzZkQmcLgxj/ZbNv0v+cpM3A2ZAiOljgNAbHeguPivFn2wHbNYvLJVcA==", "signatures": [{"sig": "MEYCIQCiK7fA8BAmNs87KNKJbU2z/cRZmtarWJOqxhbBy70YPAIhALR5vVyw2PcFLHKGe8cnsaMFS8YheWZKFFTKRK4AyMec", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.46.tgz_1497305744220_0.04068646812811494", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a02ac3a49e2d71bb41c2dd90dcdce09ec7511d005bf94f68ee01f27d2652cfd0"}, "4.0.47": {"name": "@types/express-serve-static-core", "version": "4.0.47", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.47", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON><PERSON>/", "name": "<PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "71b3c6b060ba8aea97d205171ff3f3a69d3db465", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.47.tgz", "integrity": "sha512-yDjD<PERSON>iac4PRYQ3FUKoQmyTKpkRaux4E6xyAtoPcH2TBVa6eKZ3iE1nSdI1myUt5l6jyT1GoZmWWZOIA9C4o3Nw==", "signatures": [{"sig": "MEQCIDWQAjUJ+GARA6xs0lxjXuuLANEhFZH6MTvxjo9IQoaZAiASIN850pbe7rpJiGV3w++icXPLsGQwUfDU42hz79qKEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.47.tgz_1497557618520_0.9046027346048504", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "57b2a52fda2584f5a65722964049fd5983a8663b0d8dd38505181cf4f991cf82"}, "4.0.48": {"name": "@types/express-serve-static-core", "version": "4.0.48", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.48", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON><PERSON>/", "name": "<PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "b4fa06b0fce282e582b4535ff7fac85cc90173e9", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.48.tgz", "integrity": "sha512-+W+fHO/hUI6JX36H8FlgdMHU3Dk4a/Fn08fW5qdd7MjPP/wJlzq9fkCrgaH0gES8vohVeqwefHwPa4ylVKyYIg==", "signatures": [{"sig": "MEYCIQDmtijjVFaQarotKsPZdEDi4ZKinVSvjzfWxTFCKjATlgIhAJpWrwln+CoYe54Qoh6/vvmG1D/FzRvmtH6whLU0YPC+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.48.tgz_1497631128899_0.4534889643546194", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a6e49f2331dc842cfdced6390e6feeff5e00bf464a2c52725a7ad73fda217ec4"}, "4.0.49": {"name": "@types/express-serve-static-core", "version": "4.0.49", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.49", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON><PERSON>/", "name": "<PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "3438d68d26e39db934ba941f18e3862a1beeb722", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.49.tgz", "integrity": "sha512-b7mVHoURu1xaP/V6xw1sYwyv9V0EZ7euyi+sdnbnTZxEkAh4/hzPsI6Eflq+ZzHQ/Tgl7l16Jz+0oz8F46MLnA==", "signatures": [{"sig": "MEUCIH7bw9h/7zq7+2+vhOVpKQl1zokP+Gco5u7OY1uHPbhRAiEAwA3FBdMMzCtJUmAN05gvyRmjpNsUF3lRT5t8CvaRT4o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.49.tgz_1499349922168_0.469447516836226", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "897d1035de65b3471a2ff9e8bb3f935a7ca20f092c4380bf1558ba477865403a"}, "4.0.50": {"name": "@types/express-serve-static-core", "version": "4.0.50", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.50", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "c5a139b5d29d2305aae6d982f69cef36120beacf", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.50.tgz", "integrity": "sha512-0n1YgeUfZEIaMMu82LuOFIFDyMtFtcEP0yjQKihJlNjpCiygDVri7C26DC7jaUOwFXL6ZU2x4tGtNYNEgeO3tw==", "signatures": [{"sig": "MEUCIQCU74eB2+ZH9va2NIbh4pelO4N4eKKkTW4D/l+tiIBflgIgGQ2lNdSXYhGFn4mhI+92H+FB6mHXV9bezBIzfoG4BCA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*"}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.50.tgz_1503352308106_0.47286982531659305", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "cadbf0b227c00f61ea3ed7ceb642108253d416ae97b7ff13b98d0435b57a8d8e"}, "4.0.51": {"name": "@types/express-serve-static-core", "version": "4.0.51", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.51", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}], "dist": {"shasum": "6b436955b8d01b9bc0908f59785c9d44268e91fc", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.51.tgz", "integrity": "sha512-NfN/6Og4KlW8cQDmG8Fxdt0clDk5p0A5an91UPyzHM8zD5oNe2zEPWZKzv9RUB719tT6HvLNEkE0uBUjB9zTHw==", "signatures": [{"sig": "MEQCIEA31SyJzF2/mYHp/hul53+pqNoXUzbC6wxnjZMgkJucAiA3SdNuu7aH3B4xbv2OdT9LYg8lG1ZaS9L8FdMiA9bS5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*"}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.51.tgz_1504822197681_0.6528591024689376", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "27829d10d0e2be270c8a3829017ebbb53fd702c4145638ef64e426e077032374"}, "4.0.52": {"name": "@types/express-serve-static-core", "version": "4.0.52", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.52", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}], "dist": {"shasum": "77ab67fffc402ff6e480c87c71d799ba79880223", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.52.tgz", "integrity": "sha512-UpN389YLcQEIn1t4Kxc8TlCrg43r6o8IcF57LvmbCGNhWzz0dEg4AaUsN6IHrrSjPzPmmJ1FLYXGPP/expXOWg==", "signatures": [{"sig": "MEUCICjJScPXc7ERmO5+UPdgUcR9YjeoN8XKP8JObhAjK98ZAiEAlAPl0plcb/LvsO4AgQcdtW06f17I+I6YW7IqxVN6J4M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*"}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.52.tgz_1504904818195_0.5224128421396017", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ae12ba2dae7207850e6eda2cfa3a95cb8abb8c97b238564fb3ecfd4a42310868"}, "4.0.53": {"name": "@types/express-serve-static-core", "version": "4.0.53", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.53", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}], "dist": {"shasum": "1723a35d1447f2c55e13c8721eab3448e42f4d82", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.53.tgz", "integrity": "sha512-zaGeOpEYp5G2EhjaUFdVwysDrfEYc6Q6iPhd3Kl4ip30x0tvVv7SuJvY3yzCUSuFlzAG8N5KsyY6BJg93/cn+Q==", "signatures": [{"sig": "MEUCIEm+NKvAWzRePn2fgSyzuBWQnCJZfsjO+TbNZRt8x+0IAiEA9ESxZP4Ds+mGAspq82hKFDg/DDDEvvySWZNwF7t1U0w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*"}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.53.tgz_1505743745620_0.022078112699091434", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0a3db62a41ac04abd6fd98c2c1fae5653c26d9d1c6039d1af2cb7770462a71fe"}, "4.0.54": {"name": "@types/express-serve-static-core", "version": "4.0.54", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.54", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}], "dist": {"shasum": "c4f8574d6bc5a616ccd7d60a196baef052f0c0b4", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.54.tgz", "integrity": "sha512-SAwkKnz0z3VcUyT3eiZRM2pDiZ4i2xhOXHTZHdoXu6UCC1o33lnzJMftXCYTRmLvFOUxryov8c9E0JLlY2ylNw==", "signatures": [{"sig": "MEUCIAErDeBFwLLS3RkBW0pbioa09ign+DDqRHwawy+SAi4cAiEA73WBWwT3zOTu4vfq8M9jTALZcpWtOIJHaAcC4l6azmE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*"}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.54.tgz_1508890933477_0.4125602061394602", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "4358ca3c91c39c4b679388ceaab3fdd194169bdda066744fed94633a56d98ec0"}, "4.0.55": {"name": "@types/express-serve-static-core", "version": "4.0.55", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.55", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}], "dist": {"shasum": "f53868838a955f98b380819ec9134f5df7d9482f", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.55.tgz", "integrity": "sha512-sDFubLXpgiluVW+2txOp877cQp7FyWYB9eMJTm3oAsX4HlGqIKiPcNpJq5XDKCTOMuVbnNMALYmayaj9KlRntg==", "signatures": [{"sig": "MEUCIAr+EMjcQkCJfJ6XOoE9A2D6hqUNSZm5F5XaNiDlzjLMAiEApVvAXgJwOXQ4NyD2qKu+HTajEJuP1dGsSBwezN8+Yuw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*"}, "typeScriptVersion": "2.1", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.55.tgz_1509046339664_0.40974475373513997", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "4b63a9e3bc7cc13d8859d0acbb4ebf201f1d53c03e433108c5775ef4577b36ff"}, "4.0.56": {"name": "@types/express-serve-static-core", "version": "4.0.56", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.56", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}], "dist": {"shasum": "4ed556dcff9012cce6b016e214fdc5ef6e99db7d", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.56.tgz", "integrity": "sha512-/0nwIzF1Bd4KGwW4lhDZYi5StmCZG1DIXXMfQ/zjORzlm4+F1eRA4c6yJQrt4hqX//TDtPULpSlYwmSNyCMeMg==", "signatures": [{"sig": "MEUCIAeP8tcamIfLeCv0QjnmFtxBy0pbKuGcWlbKmSLyl+dZAiEAljodRI45BLAfSbQ3iR5bhFhcyEE0lbS8cpKj539JxaY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*"}, "typeScriptVersion": "2.1", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.56.tgz_1509390392478_0.43631473695859313", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "85545352e283cc99d46ece06537f18c923c17821427bd3f4fc15219b7bb34a7d"}, "4.0.57": {"name": "@types/express-serve-static-core", "version": "4.0.57", "license": "MIT", "_id": "@types/express-serve-static-core@4.0.57", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}], "dist": {"shasum": "3cd8ab4b11d5ecd70393bada7fc1c480491537dd", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.0.57.tgz", "integrity": "sha512-QLAHjdLwEICm3thVbXSKRoisjfgMVI4xJH/HU8F385BR2HI7PmM6ax4ELXf8Du6sLmSpySXMYaI+xc//oQ/IFw==", "signatures": [{"sig": "MEUCIQC5qJQxLl2KA3Kc/mfZvkZsKoG6eGhCU3U/yLf5//dZPAIgJ21U/m+9CIB39hXMjFGLJ4YXAIFiPiElaa5QgfBAikg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*"}, "typeScriptVersion": "2.1", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.0.57.tgz_1511288537415_0.292545702541247", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "6792ccd7b89e2a8656b41584395b2569c2309d17304cc70561c41beabe4c1664"}, "4.11.0": {"name": "@types/express-serve-static-core", "version": "4.11.0", "license": "MIT", "_id": "@types/express-serve-static-core@4.11.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "aaaf472777191c3e56ec7aa160034c6b55ebdd59", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.11.0.tgz", "integrity": "sha512-hOi1QNb+4G+UjDt6CEJ6MjXHy+XceY7AxIa28U9HgJ80C+3gIbj7h5dJNxOI7PU3DO1LIhGP5Bs47Dbf5l8+MA==", "signatures": [{"sig": "MEUCIQDBT/PnrskywWFsoig0LXMCzG7e1b7y7vrs2Jmr2IFgsAIgcijAr8gjNdtxOQVUL1gXyAPWkKBE4siA9ObTBXSUqnQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*"}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.11.0.tgz_1513781513658_0.6800596702378243", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "037182ac3082bbbe4909d1021590a2a4cc83cd28325117512df9c7fbc74d01fb"}, "4.11.1": {"name": "@types/express-serve-static-core", "version": "4.11.1", "license": "MIT", "_id": "@types/express-serve-static-core@4.11.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}], "dist": {"shasum": "f6f7212382d59b19d696677bcaa48a37280f5d45", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.11.1.tgz", "integrity": "sha512-<PERSON>ehCl3tpuqiM8RUb+0255M8PhhSwTtLfmO7zBBdv0ay/VTd/zmrqDfQdZFsa5z/PVMbH2yCMZPXsnrImpATyIw==", "signatures": [{"sig": "MEQCICMIVORtxHyOEhrdj9+3hgUms1Mde9fBlwll98qDK7aWAiBYmRxHDmZV3SzM3xZ10nm60a27Oku1opQIPUzBJvDV2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*", "@types/events": "*"}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core-4.11.1.tgz_1516402442416_0.4394925069063902", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "cedfc7bb9468f0bcd4bce271c718540b3b87acdc775200e7a4bf979843d160c1"}, "4.11.2": {"name": "@types/express-serve-static-core", "version": "4.11.2", "license": "MIT", "_id": "@types/express-serve-static-core@4.11.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}], "dist": {"shasum": "b3c4bd7d45f765dbb782842fa80200967ae14eba", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.11.2.tgz", "fileCount": 4, "integrity": "sha512-5ukJmirhZqJh/jEDFn40GANZYtO95C7Pu3Xd9s8hHCtGhZORDVXiFtKLHKDE/s8T72Uvy4BZSTqsgFQMWGg/RA==", "signatures": [{"sig": "MEUCIFo+HuL27mB3dLqW67ZIwTIJYbS+Jj5ECWR5bV2gONV8AiEA/LthSXOm8cr3S6A71yvMRcuVBknIcLpbD/qj6cdjLyk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36727, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCr0tCRA9TVsSAnZWagAAU8cP+wWrhfV760bKZi3q7+FZ\nZAqg2DzBauYuyrvaUnrpL01JE1RkvqzgtlAWAcYhAuoGY2cFgt8dQDOQAnpD\nHSU13u6eawReJcTqtqU7CtyAhJy6+fVPAZkoLFLsrzaDmghrapjWwwGjL9Ns\nJG3cmrQzivYxy8xGILopGHknQxn+fGNZFXzX9qGljlF9AvBTwTBetVlTyC0r\nPlzpWnJQhO4LYRECdzNQXhNHmJyQJ1rAEBnX042dpXhaNmX8lHVJGlhSg78u\ndwE4Zgf/a3TwszZwhAgc1OIqYA0TdeUhsPs3dKmx6sauVMxETvQfIWiHYhkd\nROv6UYr7wmKPwMFRvPcASgu+77arZ7UVehXillA0d8Ni+iI1NjqtMKeJGLFb\n2DY62VnEpbhUH4AsfJ5QDV8gwZKnM7Yaq+t/U+sM9kzNw5auU/31JMa9VihZ\nd//s5t07Ufd727ZhoqZCdonHqjCEtRN3K8w1xftM8BllmEp1Ju8JG8eAovYY\nkbsOtTz9PQnCyBCk7k9Fhc7BoYo88G++qlmXVwzFTLEAjiYdk/I2e6urdp3M\nTAvV6OYf1S/M7epfVZ58zQ2Xkgs+I81rbcSY1XUhDR0EgKHa354hSkhpuSX/\neVSTBSuPlZjilg1Bq6P47R5XJN1fVLLwEKmK7TTcAtTQL47+1X8dJb88Gkyj\nbcfi\r\n=pLju\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*", "@types/events": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.11.2_1527430444944_0.6291717766791185", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "49b05b82fffeb99118d348d45f8071f8cedf0fdecd2f3050befe543b02842799"}, "4.16.0": {"name": "@types/express-serve-static-core", "version": "4.16.0", "license": "MIT", "_id": "@types/express-serve-static-core@4.16.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}], "dist": {"shasum": "fdfe777594ddc1fe8eb8eccce52e261b496e43e7", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.16.0.tgz", "fileCount": 4, "integrity": "sha512-lTeoCu5NxJU4OD9moCgm0ESZzweAx0YqsAcab6OB0EB3+As1OaHtKnaGJvcngQxYsi9UNv0abn4/DRavrRxt4w==", "signatures": [{"sig": "MEQCIDeXNxaVdxqk9RrIDl8w2uOjnUVjMy4xepLMz+JZTKKEAiBCc9UGNH6soNWNJqgPQRpfi01PEcnh2NqJgtGI/eq/vA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37933, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbFdNcCRA9TVsSAnZWagAAsXwP+gOOlJTHPY7yoZLLznpa\n8UI6BozqP4clRO5WuLODGlfjAvs7HN3ohFPHFLj4rSmBeBfuUYkSCCLEc9Kw\nkfdWz6g/TqwYivKK0hPXyP/Vrh3g66STlgayJK2t/ESgSi9zk6OnbwnYZuYQ\nMiXDfDABcwpgt7F4FClhrPKWlfe1Tj/nXSpVjKpROSRDZk+Kt3joSeFu4yto\nyzNyTmBG1ZPnVel8QdZp4yT+Te+7iVhAIMDJU07g9YCsgJmNkcrlxD6LjHC5\nXMTbNtMwGkyYpFUpZijC8nLXlEUWgraH/BIL9i90SU0sTGs62eyCTFzw45Xz\n8C0Z/bA683DzNYm95gLXfkU4GfReDRHPISe2Irj9JAGF0uEjJw7NmG8XYnQl\nVB5zMpJSmhXlNgs0EIjmE0GIsrKlu3Rao84W0HH7l+9zx9rRk4/lw/NiUidE\njdpfpvtybTYgKro4di2QcRbi3c9sUx1VwmZegvskJfMo/v225rY/USjXVUQx\nyCl8DHsrnSGzx4tUZz5/4MNPHCfbKCr7aephDVNAXa585l3kELWttk+JDfZl\nHA9P36TDfZ8e11Rtwh+ZN0kdMDlIW4U/0Dbpn1YNW+0uqf/CqAA4wA2DmJGQ\nzU4mNx+l5SPnanhYkCIKXQYpC8jzEQQKvpum4UIRrg53BEfTrXSnqrDHVxch\nxFhz\r\n=34Yu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*", "@types/events": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.16.0_1528157019844_0.4253760297767568", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8be76390251659a06700a35c7e64af43c3207cbd3c16a34cc7491535b5535aa8"}, "4.16.1": {"name": "@types/express-serve-static-core", "version": "4.16.1", "license": "MIT", "_id": "@types/express-serve-static-core@4.16.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}], "dist": {"shasum": "35df7b302299a4ab138a643617bd44078e74d44e", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.16.1.tgz", "fileCount": 4, "integrity": "sha512-QgbIMRU1EVRry5cIu1ORCQP4flSYqLM1lS5LYyGWfKnFT3E58f0gKto7BR13clBFVrVZ0G0rbLZ1hUpSkgQQOA==", "signatures": [{"sig": "MEQCIDrk/hayjCtGs/PzasJ6dpEOc2v7RgkxNcpfGOVfWnKIAiBrEqVPb5PQMaxHuKDGJg9457udykfjmOhXrsV1tbpztw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38002, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcR7IwCRA9TVsSAnZWagAA/GIQAIbNBrsNZ6CCnkgU+/Nk\ngREM0jj57fQ47Id8jZ8CGTBO0MUVAXhE7dN6pZmA0KaKsrtCiRUUwV2fo2sM\nOoIPsRImE4eVqk50P1ZWBGOQI9BgbDPaV5jfYKkeFFdjvYyd58cK7uvNaRuK\nQVKwYYEahIIBpOS9wQAAJc48hYQmZ/yUP2uC1iQULmfe3ZHj4oTf7yfe5Cbc\nf2Zd99EzsCiGYLyvY2lI4LJ3swpmrK1gCpntw0Rs7q/IFHis+CCTjC2r97fv\nFcaNjZf3+QBim2lNuMrwiqgmcPT7xkqPcR8xFnvlFec+4o7I35IJ4ZFsFx0D\nUnIT7JBvkArXFy0EFqw2T9I93eTSVxeQdDufAurTbfsMuv1gLDOLcBklqurE\n0pYW3aZ60z8B3YHMJAwMzb8txarckgGToWzv2SPYIm18x0ODkNDZKFAa2D+o\nqMNfsvzBVyKNkjC48gim27ZJYF9RLWz24PwAnSTLPB6R0eXeK24GgF40HAed\n8esw64wAGhyMNcPuvRZbMIYdLZ+yKGuQm0Ocs77ytV4pS3+G3516tvvuLwba\nT1R0LrgA/DeVngdhk78HkKjRG1WUUrVh30k/9IBWZC/JZzqWCzZ6l0jturCD\nWzQWoOHEe2TZ6tMB3iWE8zFu7pDV+gMe/KdVHgFYnNQ0efgJlmAJLKGaSJAd\noRbE\r\n=Eh0P\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.16.1_1548202543511_0.3978290536999096", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ef73af54cdea2de98ee67df4b117bdef269cc432b4d58b6bfcbd790fabe3d5df"}, "4.16.2": {"name": "@types/express-serve-static-core", "version": "4.16.2", "license": "MIT", "_id": "@types/express-serve-static-core@4.16.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}], "dist": {"shasum": "5ee8a22e602005be6767df6b2cba9879df3f75aa", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.16.2.tgz", "fileCount": 4, "integrity": "sha512-qgc8tjnDrc789rAQed8NoiFLV5VGcItA4yWNFphqGU0RcuuQngD00g3LHhWIK3HQ2XeDgVCmlNPDlqi3fWBHnQ==", "signatures": [{"sig": "MEMCIEVIRlZcnPBHkKk00vwGMO9ye7dfwcZfk1vS83Qgvnk4Ah9AR9L1yhUEV9QLYlbIgNivv1a1iopWb7DzsAaBbPRa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcj8KjCRA9TVsSAnZWagAAO+YP/jBa59GbkgCvSzbkNHMs\nzdrx5Qat0T3tUOrqkUpSKy9khhc7Usmzca+90LOTP4ng6KLzvtyAuNtUBGEp\nsUD0tDKay4lqlImvVRpjr4DEV+m6Nk47oXnazU/d5lP9bRb6rZGY4sg1aTuY\n6xw24ZopHZ9lU/kwCi1YooutO/0+sXnrznLPcI12HxKqef+8ECrpVp5mQWUL\nDUFduEWDiEkZl4gvWxpHitJwYKXTQVgfDB0GTjgNcKW9IpB47mBndJp+PYgG\nGD0rSrntm2SGUZTuEcR9rCetfQoGB0jcZR0tkesvCqqU9FUfWytk5HfqSu2q\n2pjFzlsCLit3B+dzLN80uhIDhUEDXueMpP1PodPVUEwYn6S6c0SHGvD2LA+v\n8AWeepErIYF/6GHoLGYa+SNUbfPtY9U6AnJK3rqrnip60eeyKzd/VncVE8Bv\nZQNR4ukRgJ+iWjwSwX7eMG1W/B1wIF583VmsuObDhNptDlETCOHD9AtXDGm/\ne0PiHkTtY+3rVtilqObDXf03WRho7ozELkLmUUurAS8VlJeew19BCPGiRITy\nJil7NeoJW+X/QRRoMuP5iZKRt7l0SYCpI+lMARh0mXKHrdcuOMYPtXYPZyUy\nikSubjW0Kq//vwEAMva7hPxSb8EwMNnuYZdNmCS17NOnQU4JMot60YvIOQ4A\nQwa5\r\n=zN1O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.16.2_1552925346496_0.8930732998925603", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b73823fd6e6cbf665d9367d5022dbfed56a35cf02cc465ec6c0fdd1586f3cf37"}, "4.16.3": {"name": "@types/express-serve-static-core", "version": "4.16.3", "license": "MIT", "_id": "@types/express-serve-static-core@4.16.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}], "dist": {"shasum": "49d9cea50e801f8bf757702060752fe65f169ba7", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.16.3.tgz", "fileCount": 4, "integrity": "sha512-HFgBmRDTvdnrRFXqBr2NM2NUCu6fIpzJsUTlRVENF8lxvstof7cl9Fxfwq5S0kJbO/FsPVcjlxpOM3ZxIkn7Rw==", "signatures": [{"sig": "MEYCIQC1F9jxPpgIxzI0nBW13IyjvWpPQCtZDa9dgqVIg2DPUgIhAJMjG8AFlz8gv9Y9+uuAXOmT1AbO3yuAQO/ph1z8+3aZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38290, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcv1qvCRA9TVsSAnZWagAAqSoP/RNOr/Bf1mptW0ndqUYp\nsPd8Ms8YZCLaj6VymnAQJw+m5T2U4yDZFnyclMyfYY7G+NznADmndkhag6+Y\nxDXfKpXAlOpbNNahQQmqdMk8p39EqtBB2uhY+e5vyE9N9eDD6P4mC9D7qSXH\nV9TRLUGnd/Kxy+XijIpSYnRliXy8aJpcoQU1rZYoElt8AK0ak8fOYHn+tQ7Z\nx4I4TTZ+RaqCvtFC2Z6K5JaK0OQCVwbjn9VbEwmhGXqqEeAGARXo4os/qiRY\nU<PERSON>zO2xveV/ya/HhB25XRZseUkCbSKyKhNWaARGKtDTRtnmKnYvIi764Bxml\nYXGZ+Utgu47gFMnCWMXjAtZc3Vfyy/c7leMDkCWjV2jYEWRek9CzNT6JgLvM\nJkRZUwdMTl7wBFIUe4m6eEud2NzUwjnkQNZsBaFNdTZgyHR9HXMTJggsoTZ+\nIQCWVwb9/oGTYiNXIGXxv85Gw39TMPnzjwBJ0UETnzQCSH+FYwJgQXkcOu99\nebwG5lHEjoPtoww/D0LB4vADuCF2M1VFqh9+TMxhP23tmUYjkCGoobCJes+W\nLXF0smwAHOvIJHGIE8wfIW2j3j3xAI3rhhhtIoHR60Nfuf6nyWGjSQngYCC+\nqFGC2T6IUgV+/xgp2PzcBDMQET0hyIZtntcUDPbaIvb/GOKidIXxp57ZKeTm\nlfid\r\n=DRFZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.16.3_1556044463113_0.6433033964455086", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "1bc3a25fa69dc001106c2584c5b030a187e2e932f63d7610d6729fa51b8fe6ac"}, "4.16.4": {"name": "@types/express-serve-static-core", "version": "4.16.4", "license": "MIT", "_id": "@types/express-serve-static-core@4.16.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}], "dist": {"shasum": "56bb8be4559401d68af4a3624ae9dd3166103e60", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.16.4.tgz", "fileCount": 4, "integrity": "sha512-x/8h6FHm14rPWnW2HP5likD/rsqJ3t/77OWx2PLxym0hXbeBWQmcPyHmwX+CtCQpjIfgrUdEoDFcLPwPZWiqzQ==", "signatures": [{"sig": "MEYCIQDjPOnBBWgB/+gHdStWzbPo9lmkcDYsu5AKqccocRAb7QIhAMdR4vtlObaJ+ONOlRM7SZU/HzbsG/EaC3AVQN4QIoPS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38127, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcxpj5CRA9TVsSAnZWagAAYSgP/2bkxsEP32xA24/FmCl/\nIrqcw3JAlLHE21vXVRRMZUwjuFX4RxeSqgV7eC0+xPoh1caqLamltd2sX5Hq\nTyHQVTiBXDag+rAx+DpWdp78HmhHID0xEmd9Io8/PzBF6pJBwy6834dEGzFR\nJx8Ly/BSKZIEGFBnoNElJjeOn5DC31vdMA8+vofOiB6NioD+KnnTrUg255wn\ngOC4aTmt0JfkZ3CejvNktjTSUILhMSsgNkQCFv1CRN75JwJqhvxeMkLvPKPv\nVUVso2lbyonai/7jEj5ea/6h0oOEo07gF/aX8vQbvYYcW9JpmRR+dFjsIeUx\n4rDhlvIf62famr2JPk4/9O0hF+GB6dMGW9nVinZe4pvjxmDGZMLl1gd0h+mV\nI6P4sgwUcVG+xAJqu03Qn0kF5oDxl+H9e5/yf8ZAGNH80Sjx7+veoDoJn7zu\nIEeFwH+fwX2YFtxlqyTd/Fk+tGkj7UhVEFnUViAUsYtiorMBSq6A2zuvdi4/\noqnBpoXmgS7gbsXBpZP+liFk8tuTPrsCffXXTXaIYLpG43/mxm6KOX0ofBSG\nZHCemIiGCb/0c0k3g+LqTX0M5IA+MheJgdTMlXRCUZ5uIQWceIFgCN92vfGr\nU4tZf1SdhVNqw8THvpwN4zsvP+tmYq6cfu940zDLW3dH0I1o9AOyZ0jt4ATT\nxBkI\r\n=F9I8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.16.4_1556519160341_0.759189909174077", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "986c1c351361309e7e029b54a4589d921a18dfc3ebbed317ea1fb68556ee0b72"}, "4.16.5": {"name": "@types/express-serve-static-core", "version": "4.16.5", "license": "MIT", "_id": "@types/express-serve-static-core@4.16.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}], "dist": {"shasum": "0e473cceae141c43320759b7ff4af0d4428b9cdd", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.16.5.tgz", "fileCount": 4, "integrity": "sha512-T8oej2B9TVALoCK4epWXRNWaFaqDyOaEQ3ntph+tSw5QoKnZfxgCXefIm3+nGp2XCRrkX4X7U5CSTMnwKJcOjQ==", "signatures": [{"sig": "MEUCIQCnOG+m4OdCRVAMJ9d/gw2jI6ifaZ2DMtW4po358f3PfAIgOUVUt0BqZZP4tQQE4NmVzut7tFHY9Kx2xMq0Sx72vBg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc5XFRCRA9TVsSAnZWagAAD7cP/2Ozwnzg7jSe/srZ5Y9t\nMvdFX5MbJuG+OMXHUqNFUXfFZvku542tVEumR0Bd6U4uK3MUgzLRh4ypLsvD\ndvEuY8BNWQeaF02oTuOBSV3sT/wKRmcmtDl0M645scb3sqBPJ8UIBB7e0sAZ\nT/hsQmE86VO44Pf4EuD7yMQQb8YEiCukf9KmLUuG1zGDnYdCt0JL0YNh9oFe\niuZAzkHZl79eibxE4rrUKvGfCxS7whXAOvmXENQMJEMwBq+MoKdjitloGD9n\nhxV0l5gN9XGOYeSVKueYgx38rSir4LET3rlHadlCyu17orZz18wqp8aWwW3G\nBJ+DeYqj63aHOdUDXqkb+z9UY2kvfdfU4eP5pdPe5cNonR1zRd/yTKhm4YcZ\nrV7X10YAVWFmfiI3p+rkiBSLDx1laVYUwAOwyZwmdhlXYIME6AKDN420m/rc\nwJeYuS9uDB86BZWRchis1l3qGjvQ1zNjzu/FypmvecUG9uIJOBXxE8rAwP/N\nKGQPTECELY6enUSpF02hUpby5kexVXLpdz0p/TNqy6LXVJk+8EYOyNxh9T8q\nzEPMLUrGLfs0tTSG6w9CFtZHyhYvC48opSfkVpWeUp1f6RZlhWPBRRatIGU0\nvFIuq6/X04DerP0Q02LWhZ7YGJy5JWfpOMoQr9aXVZDgiiui2V2E1/9pXWiW\nD+UZ\r\n=b47Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.16.5_1558540624989_0.25947177582225445", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0ad889bd4bd9844f00ec9aecb0865098afdd4d5a564893cd1d0d9da9c2f9abf7"}, "4.16.6": {"name": "@types/express-serve-static-core", "version": "4.16.6", "license": "MIT", "_id": "@types/express-serve-static-core@4.16.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}], "dist": {"shasum": "66d4b29ece3e2fb6e5aac2232723002426e651bd", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.16.6.tgz", "fileCount": 4, "integrity": "sha512-8wr3CA/EMybyb6/V8qvTRKiNkPmgUA26uA9XWD6hlA0yFDuqi4r2L0C2B0U2HAYltJamoYJszlkaWM31vrKsHg==", "signatures": [{"sig": "MEQCIHP5d7zl6MAHuQd40oUXSo9W452/ynvBWoFm46CpBg15AiAVP6hPiVAQZybDAbESF/vmCginXeOWPI6WfiXDTORZjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36751, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc6CDeCRA9TVsSAnZWagAA2DIP/js63QAQ3LorcAiFHeGI\nWxAfAHiWFh5Xqfs2XIAFAdDIYCi0vg8fcqgBK26xbHnGlrYo6s6qqXl4LxCe\nlCz6U7AzGFxaxxN03YSq+bybk1C4uC0a7+hZ0Yrah/WKgvuIqbYrNI3CKDCa\nRu8kZh/K9qBNZDSk3FZ2qJCKTXJalCXsBpanlcsqHxHTShMeHFJqg3XnAYJh\nTsfJ5frWYy5OZQS1eK+GW3x5y51qt2u2GFzHjoEm9H8I9Ew26YXm+1YPXY6B\nolAWCYTsQwH8tO/Z0KnOoUQG8qBtbZYlrnN+Gp3W5ZN25Co1n70pOsx691X/\nbH0TZWE0D5b4LTyofN1o7UI+LDGIcYoaHca44zoepML/13ZLtevLDUa9O4ws\nBeeKZUMT44UaW5TDmJbKcJn/4CqxPL09Oe7ajNVYyjfQ7+PVIchS5UJrd3bT\nFhkfB80aaapXTQ7DrG0gvOYXvR4bIILfz5nddpJ+V7SH7PSi6kbsQxx9Ud86\nLlpI3hGQc0PMIvKzsFfBsRwchrk0T9BO83ygnWusdAaGegTZA+C0OOG3052V\nlc5FSdRBoxi8bWU3jtu2R0J0FR9DwSrK2NjrRbN/jki1/ZEr51jfP9TCRoJR\ncjl1xS+fqEieWeb4OkRzmPfwbhuGOLlvPuizFpxnRM8M3b9nmdk6NZ4vySjE\njuHa\r\n=5WdD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.16.6_1558716637721_0.2945893480292534", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "fb335b1a2080efd82c3aa2df74fb71618a25a041ebda63a52cd6336f354eea23"}, "4.16.7": {"name": "@types/express-serve-static-core", "version": "4.16.7", "license": "MIT", "_id": "@types/express-serve-static-core@4.16.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/aereal", "name": "aereal", "githubUsername": "aereal"}], "dist": {"shasum": "50ba6f8a691c08a3dd9fa7fba25ef3133d298049", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.16.7.tgz", "fileCount": 4, "integrity": "sha512-847KvL8Q1y3TtFLRTXcVakErLJQgdpFSaq+k043xefz9raEf0C7HalpSY7OW5PyjCnY8P7bPW5t/Co9qqp+USg==", "signatures": [{"sig": "MEUCIQCJUIvbT/O5oRo9U36crNjyUY+QT/Ddd9iNAOvyizj+8gIgUOGmqFrZBUKqSu6/cPIvHLSSRS5q4HAhlTvJwQk1rS8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37049, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc+XEkCRA9TVsSAnZWagAAml8P/2Hunkr/dRasluIeFXuz\noRWIUInKVz4O5HY5WXQgJzXmEhL+kQa/N2LNMwaxkmXdFgjqZwu5QyAY+qky\nulmsZ0/P9yxdMWcP1rv9qgQ8jsStsI9iSuoW0w7sinT/4jCMVgpu0GxrEzYy\nh9Uk4Fc2jCt46NqwSt49RbH6tLGrUjtYUaQBI6u/AE1eC+6qGgjDy96ZhC4Z\n0vBvGEM8v5KwIw4xczoRD3K/IHWVKsWyikmpSs/BrgD4QTXQHMEaYpMEMZja\naK/7KzfxCM/CJig2+oZS2kSysMDV0qQJlTy75Ku9DhZVyyPI0dWwExjYLEgq\n3upOqcJJT+48avbphclKgjfaLqujwW4PN4f2PD/Fj1kxgwpPhVG9ujlhEb95\n/XZdPGh8+4nITKWcWwbAfP9prqCJnA/g8aHe85ZdsvHYMoC3fvOLswrt8oMC\nw6t0eJleeSN7BEYMTmnH/rB9XdWvkNFr+i6H9MJC/86MaGR2IBuXJCSuV/MI\nys687A5htGJW4Xtr2FontMeUYYNxoXyAUMtzN/zJ0/AdKuiJv8/XC32Y+OHn\n5YqhjTPejWh8Pr3r2N0rdqycvgoR++LkC70ZtiAehhqrZVjl/NXCHCq1qT5i\nHAlIRLz/E4vkaCr96RPuMQ8h3oi+AMr9bWV4S1NuDmZI8akkcS8/NlR+NtM4\nG9yE\r\n=lFic\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.16.7_1559851300338_0.7075590201778963", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "675a45c00d761f3a8ec3acbababe994b54da2f7018e29cac55e2d4e989fc57b9"}, "4.16.8": {"name": "@types/express-serve-static-core", "version": "4.16.8", "license": "MIT", "_id": "@types/express-serve-static-core@4.16.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/aereal", "name": "aereal", "githubUsername": "aereal"}], "dist": {"shasum": "17c3bc1d1fc0eec2b4c278677c7fb38c4e0d53e5", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.16.8.tgz", "fileCount": 4, "integrity": "sha512-5iLrUAEje8R1Jw6Em7ryETfZbhGc2CAO51Xphnlw7qmGI79f8sG8qMnvMk3M/IxNdoELYalib7ziuD6kUTk7sQ==", "signatures": [{"sig": "MEUCIGnHGu0gfDzvT0xLoM5e3V3F4vhnDIqYVaXTVVzUSvBPAiEAq3vUEYd/cBl0hB0KXjHd0b/A1IbU9TdYGAonG+E3lbk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVvEkCRA9TVsSAnZWagAAmdgP/0DJfJh6B8fmMYoY9eez\nsjijgGx+I9su5B+xlNxtzggYyVSHMfY5zW0/OACfqBb9fWz4Ei0M8QHVcMzB\nOUld4XMXtiwHOn48BFeRImogvN8ubIGEdfCT7qeVbP6HLP2mm/Xc4oebhk+6\nagBL+7u83DwLaA+mlDe7angH+GidKv8JKt65o/HiRIEfse6Zt1B6ill50xOU\ngy2tEnbZSFhu87ZWmx4LxAn5FRc9oLzKSdOsb1i+KoMqs01jXtS2FjOh5+1Z\nmL/mUqMrZvX6mKPiCrk6pdjERZ+bDgvoKkdv8hjnxyUkvewL4d9Nvxkrdd4Y\nw+fr4Y5/i2nxXbgm1plqj3P37DeRikNa/5z1RLxUXX28DdGuzCtIlW3mGWlu\n48h5ok3Mk1zp0DZKG8wpr0RMGKLHI2bAUdNyw5hZhA0a+9QdX0E/I4zo8AII\nHzRE2MBs0opJ+zP2MZT+wyjuD/S8r63ysfm+74NkzQGsmAsb+Ni39EYwmXl9\nlIWksezr5gJMIic7fv1/KhfJ7g/BgESzso4b6FLmMpiRrLBWXdM5GDNCHij8\nXVhd3c/QkxwTDh5KMeO1HxqdJHl36cVnfha7KmYe7qInJt5w+9smfCdLHoaG\nmMPBwUVjNy4zICXjsAPhQmcoXvaTtzKwCHew7ij/8jbRh5SJmIU53UflzmUl\nKOYT\r\n=A8H3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.16.8_1565978915740_0.42913486990386596", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "06aa0807abd919511e19704863598a79a877aa1ec570b1c73e31388bed898bbb"}, "4.16.9": {"name": "@types/express-serve-static-core", "version": "4.16.9", "license": "MIT", "_id": "@types/express-serve-static-core@4.16.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/aereal", "name": "aereal", "githubUsername": "aereal"}], "dist": {"shasum": "69e00643b0819b024bdede95ced3ff239bb54558", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.16.9.tgz", "fileCount": 4, "integrity": "sha512-GqpaVWR0DM8FnRUJYKlWgyARoBUAVfRIeVDZQKOttLFp5SmhhF9YFIYeTPwMd/AXfxlP7xVO2dj1fGu0Q+krKQ==", "signatures": [{"sig": "MEYCIQDaqU9ZNs3GQtNUfgzhgmZ/ZIcbBOr/Ssz6+M1HIWwOhgIhAIoHmBjSEfH0Q3cn9acyWZ1/gD2EEQdnN89UNLA1INGF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdWfVdCRA9TVsSAnZWagAAKqQP/1u4JL2yES0MJ36yLQWZ\nduUJUfxjRAZip/oFPTm2JSYgz238Q1hz1i+91vCZgzazXaaK/NSB06R9O1cJ\n/ktVlTJdR2iLy4POPdSUE8HSAhMClKzz+LELG+i0eU5BxH/chB0gyn5uI6uW\nGpCzW4TW+oDIIQzosT1aJvYbOXBbluW9iarAnOCr3ENqYPuNKyaQ0uV9h6lx\nG5UnnE4uxIv6z+4nl/YvalvUptGuLrhN0WCzfFHZvqpSxG44LqAgR5meHsLt\ncP1oBcyIBnL4WJPScT7UwztyWyud/yzNdgHJ47EqR4dm/AjxzpPGdofJIMpS\n6sQ2CYNCi+rzgMw3j5eav8aIODe0JxcZK1UYffgtjbvDP3kebR4i+Ic42NOL\nIMn42A6psP6Eq6OJqr5d7FJ8cVljzpGSdcfukYaVtNhyui44xpXYJqB7CwHj\n+dBnXcmbon8jYbbwTKY5HlkoVZHTp5HFbT2N3U3dJ4jfGP/DCuHPk76hk5fX\n87XSMQt3Lb3T5JA9eu4nHh9EXcMiBHS0hU8vkNwg25R9Y2ckbQid6SP47j8S\nxMd+64YJQfugtfSpc8uxbo8UJrL14ZrdQyYl98tSneMOYNDmfMysGurQw4iH\nrM4Cx+4qSlvLqOdjqQlDbvk7uGG/XgloLFyPgRzWLs1nA71Q808Ywxmeubuh\ntlP0\r\n=7/i6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.16.9_1566176605320_0.9776648609798371", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "1aa7520ab937b684d55d4d6134d9f894a792398e4db7c4550308361ae771abc0"}, "4.16.10": {"name": "@types/express-serve-static-core", "version": "4.16.10", "license": "MIT", "_id": "@types/express-serve-static-core@4.16.10", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/aereal", "name": "aereal", "githubUsername": "aereal"}], "dist": {"shasum": "3c1313c6e6b75594561b473a286f016a9abf2132", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.16.10.tgz", "fileCount": 4, "integrity": "sha512-gM6evDj0OvTILTRKilh9T5dTaGpv1oYiFcJAfgSejuMJgGJUsD9hKEU2lB4aiTNy4WwChxRnjfYFuBQsULzsJw==", "signatures": [{"sig": "MEYCIQCbhbxPypn71t2jHxX8hkNReqldh13dkoAYnAjAtTezmwIhAKgcELUutsZpAHb86l4djzoYaoAlvp4BjB+m0MOtgTSq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdpjaXCRA9TVsSAnZWagAATrcP/RawtV2/Db3vH6FUMer/\nHTpZJizbRq6wSk2Z090MUCwWUT9oYPk19vD0oKegVFSyXoJUucye4lUfdC3T\nktzjtMPw6ao3FshfxsXJmK2WxnxyKsORpftzwYHWjKwYypsU95nh5XttkifO\n713QvuF5j8yQC1/qF6bOaPM3puxG29Wlgm3r9GSEATv0hiLtPAnmlS57xLSs\nwTzE2UpUNZUgYbpnHSoPan7BoVrwYIbAA3eKLgWXGJv6fi8aQqMjmDE8oFp/\nQBsjl13pz4pvLC1elupgRkZNWWR8KuzVVLpw9DVFjaTGk1oQ/Au1h0Z/ww1L\nWkBxY8SfI0fbZ9LCR6Ip06I4k32IfHXdRwEdD7p0viTsiztT6lThUDOUYZZJ\nPWUnTbSz1OGqbTLxlYN5+W2/u8mUAMiWEYHdqKtKAtjUYINb8YlXa3lmqls2\nnWNXkpBERc1yfjvS5EZNGLFBTp8rfVmRqpfcK64pI8KVukUY0wVh31XNbGB4\nh7NKDL7j8YaAb8s2JjDySJpKCwgQiHph2WXHmG6gH9eYWqj5RsA0hpRVscLa\nAZHfopz9ehiJBDhMbp/JWmQ+7waRi5CF/YqaIanPT4yam47f0M4rWE6yXzi6\nEPMN2stmL1Q20UJp9ORyy/owvWwf7ldo8O92iORln+O73D/FHx5kHTDtyHKf\nwfkP\r\n=kFpW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.16.10_1571174039065_0.11752322333663812", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d0e8b8726ad354ab6bc05b5f9807d56ea1d8f90aef9cb0270e9070a18caec63a"}, "4.16.11": {"name": "@types/express-serve-static-core", "version": "4.16.11", "license": "MIT", "_id": "@types/express-serve-static-core@4.16.11", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/aereal", "name": "aereal", "githubUsername": "aereal"}], "dist": {"shasum": "46e8cb091de19d51731a05c2581e515855979dad", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.16.11.tgz", "fileCount": 4, "integrity": "sha512-K8d2M5t3tBQimkyaYTXxtHYyoJPUEhy2/omVRnTAKw5FEdT+Ft6lTaTOpoJdHeG+mIwQXXtqiTcYZ6IR8LTzjQ==", "signatures": [{"sig": "MEQCICenxYJZBHrmGUJfynmCbjS+ysGUu7eZq96JJoIhlIfbAiB3DAK94zB058kR6A6IbPD8yPyH3u6n1n3z5JCUv7vOyA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38140, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdvGqBCRA9TVsSAnZWagAA/0kQAIP6Lf8CmDr3lGLM7yXj\n1l7SqIZD/fyIlvDnc7orAXyQZrLew0IT+biDBmmAfg/t6VKCeOXCY3FRVN/f\nEoKt3kSolKGNgtShqHqMfGbP9Nof9MV2Uppuw0Vez4No+ITYyR2iQ9PxDhFS\nhA98MDGQ1+Lu78l3Vwb5eGVg5qPOf3JGDrQjtQt9uphwgUfMjzvMA6QCG4qx\nSXEdV81N21QrpCq5efJ83NxNGqPtlqjIjhTKfwMtXG/zWEIfPozwscNuPePF\nC9Nt1QBuDDj+8BV5240kJZ94fkkGuRlZFGEM65pcDmkXeheZOndK00/sOvpa\nD0P2mitYlTtknE8gYX/jnL/5ee4uVkbbn4vwij8dtbtwBQU4W0t7JbJ6aHMH\np9cVf/IGBTa1xTPzkhMvKDBfK4TifETMBzV4BCJ8+UHqsH4cMRJ+5fQ2PLgA\nfPIgJerwm5aKjTbATVQG+5x2cUQpQJeVF6yjtHkrfyVQkHEzVbRejxudfeip\nKpbPMNqhtgjeHzpAnPu4oEXfh+qvoZUEM82cQ4hTW32PWBZHVmmae6o56xHY\nbm7GuPnbuIDN+T5ZYbOZdmqm4NtjZQ2NFALhhjriJghx9BFdUXyVXnuJ/Yfu\nnTOoY5eLhSHAYN/JBdx0lC445q6qRFfy7pPwVUU4tRuSupL64PSoLIsIScNf\nCrVg\r\n=+OBr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.16.11_1572629120814_0.11267362129790004", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "cf0a8fdb1c3e5d905989b7e04f3ef58a3de201f6fb30d4ec59f919a4177a6ca2"}, "4.17.0": {"name": "@types/express-serve-static-core", "version": "4.17.0", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/aereal", "name": "aereal", "githubUsername": "aereal"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}], "dist": {"shasum": "e80c25903df5800e926402b7e8267a675c54a281", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.0.tgz", "fileCount": 4, "integrity": "sha512-Xnub7w57uvcBqFdIGoRg1KhNOeEj0vB6ykUM7uFWyxvbdE89GFyqgmUcanAriMr4YOxNFZBAWkfcWIb4WBPt3g==", "signatures": [{"sig": "MEUCIAJfOvMnP0aAfHly5gxU58e18bxtFJ4DD8B/1JcA+twPAiEAt2XWS73kRgf4mgqC9X1C8G+kIMulqJiG0qppItphJbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38904, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdzxyUCRA9TVsSAnZWagAAFEEP/A09biMRehxhhMWFO81s\n9O5d+7m9v9gbWRuHWqfaT1f1S+RGZDmEhssr9izSgS/O48Q3cgj2uLi99Mv2\nvqh9qPmQl19zEaAqf34/X/AxyODBujCqrQKYL2bUbmuVzsheAECWgZqmUA+2\nSJcHZVAOCl79V1Me/E4kVQDyxyy2eAwMq9d6yAdr/lJtzj/ws7MWWbNu8OhX\nzsY8vacNfMG7GUsY2FdPL6ZsvemC7lG65CQAheVnQzJ/MSglFkmMzsiX/ZmE\nMXZewuzjz7z3+dDY0th7qA/1m0e5D7+7qk4Td2GuOQ8Ytjtjp8BOnsS/sIMQ\n/xdz1JwCKsLvxFixgSNypMMhYMCxHr0ZK3rV+sAf/YaTGYtR8Ju5NFSnc0qZ\n9+bl8sA2MXhHYXFkWFuzqaZPORM9UC+lAa2NQx1s8Y87F7eyJqyJL2H+NLdN\nG0dcl3tTP0di1UA24oBkGUUrZtRJiq1sEfpT4ntGep3qOuBietMx5a0CbFIj\ncIVB729z593a0f6+kXcpEDFeCJZ/x4ayhpTKlVubPUFc2A4AM8+GNinvPN83\n/7ZmB+zbZigvriIvLWIEw2VhwNBoYP4PhxUQ2LNYebFuc+FYPyxqN/BnMs6Q\n+DAYiWnEEcBRgEPMX/irCVIZrxnAvpvtaEXmHrEv0s52NpNRBqA4XqaqcLC0\nQWdV\r\n=7psr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.0_1573854355372_0.19272498599564925", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0f26c7a2c15b8dfa4210c3a5f55de334441a85ec7bf9736a5b0a4a204ac39048"}, "4.17.1": {"name": "@types/express-serve-static-core", "version": "4.17.1", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/aereal", "name": "aereal", "githubUsername": "aereal"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}], "dist": {"shasum": "82be64a77211b205641e0209096fd3afb62481d3", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.1.tgz", "fileCount": 4, "integrity": "sha512-9e7jj549ZI+RxY21Cl0t8uBnWyb22HzILupyHZjYEVK//5TT/1bZodU+yUbLnPdoYViBBnNWbxp4zYjGV0zUGw==", "signatures": [{"sig": "MEQCIEapxDwG1EcrUiDmMLQunHxM3kUdkYM1xgX7lw//HDDRAiBr4UTQ592wDWxAc9upDj5MvoZ2ERHofPcqZA7TlDHsow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38923, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd/GdUCRA9TVsSAnZWagAAWIkQAI+YFTpCpxpxFvNPENhE\nQXDQ+UIIo1Fe5H6Af/Jt8BFGIYEQBH8U+rqL6uLzaqfY0gLyElN1jWaYs6sJ\nKdLBw2QvmBCG57USbwzo6fgD6uF69JnWc9KheEcKnOjWD2kLctEO+oz+DEl6\nKtxzF4X+aXnGY9vELVuJbr4qKDAjBK6gkparznLZk+2+9bv4WhReHg5wQxrZ\nABqiqplExAfcKZ2LBO+M2KYjlP3mR24/u5iQS8SGvZsa3F6K3q58MzBX/g6W\nPNntCf27rVQrfgBaOVxaSJeOK1F0vc0xiEEjJqR/3VatWyfYBgS7IQhcX51s\nDlnReID9OahPkVP+9LI1/zBinsNe8skBgsvLFWxZLmSlZbzAF5oE8JYLkP9j\njyPMk2pY3169kkc8HGWtBdXL8sUmCwiWNwqdVov45Fq8RbtRhRBM/9sibhwO\nqgT4ZOqPtsebqp/MhuA35draj4YcolyZaf5Crs7zqiCJGJMa+Lh3tgKNbtZ9\nq28FRhpFPSObcpfIRxNyQyLm06HMgKcY1pEr+DaFYfMsm9BCXO76dNB1kFoT\nXIb/c+UarkcJODRTPnXYdAU8D/lJmt2y2GVCwJyLdq+++Lk2ISPNRpbGk9Wi\n8eehzQr5drT9ot0CLj+keUT+ZSx6s5726VKA20HrTpQXdBFiK4J6Ah5oFg6G\nlVtn\r\n=GzgS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.1_1576822612141_0.6855273292047916", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "2fab9cdf58fc02bc5bdc48383c1e5dec42b899aba53ae87c7818aa8f825f1d0b"}, "4.17.2": {"name": "@types/express-serve-static-core", "version": "4.17.2", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/aereal", "name": "aereal", "githubUsername": "aereal"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}], "dist": {"shasum": "f6f41fa35d42e79dbf6610eccbb2637e6008a0cf", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.2.tgz", "fileCount": 4, "integrity": "sha512-El9yMpctM6tORDAiBwZVLMcxoTMcqqRO9dVyYcn7ycLWbvR8klrDn8CAOwRfZujZtWD7yS/mshTdz43jMOejbg==", "signatures": [{"sig": "MEUCIGoVoDJVU5foJshf5YJVOrmtHIMRgsCG18/podQAzvkIAiEAjaFq0/Zpl7AHfjYoGv6NBL+DHdSGiT461MzdTo5xk0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ27nCRA9TVsSAnZWagAAW0MP/jT85t5YYBEX5tFBAXFs\n0Opj0HBIkWcLHULV1x1ZrNzFZoXdbM8MD/dkq6i7UDeR9r42BrTbpiJEPJTm\nkdxawWcqQSaHj4HzItQTr+gLSj9Ior7ckv955or1FZprpqFXB3v3DilO+KD8\nRyYpg+3Wcv7G174Qg/J169U4CaYHyyR2jFGgdyPvRHgZ5rqdYpwbv66l8imK\n5p8mkneKSUE0oY0z900TLBvDqfn1oCBKX3yyeQGo5HqCvvpH2dnHmtfwmP9t\nKlfJpM+KYyrrP0BV2b2pgTmBHyAnH/6U3YJTuMyhXzDHV1Q0DeamGGA+Izr9\nXUnqdjxaY+d7o8pPKtXo/fGE7BT09/XZ/YDJXSrwrelmjQ1ojaco0v/b/yki\n9ei5tPX3B2xcOlnfnzGmY7Ah6vamNerswxeive7VyY0HLvrGSBPAVjbHs7Kv\n4COGpPGBjYuXq6ATTvWOCnFU2/pjm7MDjzTiMcvD31nE/i6M0lLr5tBiTcSM\noeopJ2YTyDAkP8pLBMW3HDvlfcVY6ge8zpXNjGLMsOKr1wX6qivqlbaTzGmV\nQc7Va5Z30GLomnUwGDn6ZfA8tEyuPXP6pkI8pMrjo/3kMx1OWMHOS1CwbhMU\nNmfpoy1hE2d4hup4EOZHEQaL2i9Q/OGclmDxkjhYUXcPhXRyDjzKvUXMCecC\nV/Zs\r\n=HHwS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.2_1579642599032_0.7637241742696408", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "2e948ce4c0c2ac0209b85d1df5138f77c06e0c5013ab175e37b78c25ffae438d"}, "4.17.3": {"name": "@types/express-serve-static-core", "version": "4.17.3", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/aereal", "name": "aereal", "githubUsername": "aereal"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "dist": {"shasum": "dc8068ee3e354d7fba69feb86b3dfeee49b10f09", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.3.tgz", "fileCount": 4, "integrity": "sha512-sHEsvEzjqN+zLbqP+8OXTipc10yH1QLR+hnr5uw29gi9AhCAAAdri8ClNV7iMdrJrIzXIQtlkPvq8tJGhj3QJQ==", "signatures": [{"sig": "MEUCIQDIrU9Pcr1LGTRwRc+kS7sqxtCrITIeX4Nch38lbzANHwIgCYI04gP7seDaQfWgu3BIRWRIUBzIBA2a9E36vB8talM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeeNe9CRA9TVsSAnZWagAAzHMQAJSlEuiPGmOepRVsIq3P\nTXLbJZ564hJXSf2m7d5pd5cImpS711CJ3S9Ew4foQikhI6PsLwR1S+GCqBRJ\nonl1RZeVIBCNfjBlSGf+Ap8PTGD1E9dVnixpwef406b2geiY6RLesQxNR5gr\nIRAxkgsmGLMD9DN1hyGnIcd/P2wZZP0l8Urel1IQwizC9Mo9ZyzXaDpNVoan\nLPf2vdxyPFM2tXGolzSg74KaF4cUOQm1MmPs1myTptZnFaMbHPnuq3ojdGCB\noohoHm7LrD6hh5shtCEQpxIQYHayjPqLzantIy+zGMmSnv1/+p3KTSCXLi3y\n5f+Sw1zrf8hukmNB3n/qw8EKAeBd0sT+L5qQkY15yS6UwEfMn5xbbXbkd/w+\neKevauFOMiRP/vjGooPlxyM0aWyGQlWJekrY46E+Rx1/hyTUptJd8y5w8CB5\n+944ygN4gr3RNoFDuLYWHqAfV5Dm8rAJ9MC+VhX9OxOkcUy3Onb7gmSUQytb\nxxxPl2qHJSe6Ywp43v2jfq7jjmjBKlbLA4Zdoz5nbmOpyGRMG8Hz4syfos1S\n6NEuQ+2vJC8r+uX+3H4uuRbjdzxFYQcqv5tKerPEhGVYfrdyeOEB1HlKEoeb\n7Jd33t6oGkK9XP1wKm9S8utIQB7x69CHjONpYayMH9H+AUuFUCYL0mM+xt6G\nTy3L\r\n=tbzA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.3_1584977852632_0.8009140119236138", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "4509fd2d6a9b450688a25deac6ee8e0e863e8a3de39a6bb845ec6fd953c3762c"}, "4.17.4": {"name": "@types/express-serve-static-core", "version": "4.17.4", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/aereal", "name": "aereal", "githubUsername": "aereal"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "dist": {"shasum": "157c79c2d28b632d6418497c57c93185e392e444", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.4.tgz", "fileCount": 4, "integrity": "sha512-dPs6CaRWxsfHbYDVU51VjEJaUJEcli4UI0fFMT4oWmgCvHj+j7oIxz5MLHVL0Rv++N004c21ylJNdWQvPkkb5w==", "signatures": [{"sig": "MEUCIAZJLF3i/8ZSJrVJylVRc7hEE5l1RjErsQSevzJLVulcAiEAmV9xFMTwrFCYhCRmC2nc1ZwAxk+t7cvvrJHf5xBuEWc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39982, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejf5nCRA9TVsSAnZWagAAR2cP/jyJ+B5uBcnPDKD7+Jck\n/YGAOyl44mQGBREpQsAWDKgmix/VXFLArRQCpdzIJmisq/5CVSfXxRC9Rchv\nNAmAA/QGqIdtvjISdYfgv1IsxbDzHVVTqznat4xBFr/FQ3iS1nfhV2YQspIH\nOyTglq+pMFg4LHqmP2YtaXIvlqxHHdC6bKSTwfrsGK/zCjKHbahXNWZ/RFc8\nYSNl6RZ1dkXKF3+PVUQxeiKaz6XRCcvKuT2Uc4F6ZkwwDIP2mxSur3Xx+Y7y\nLWKQFjdwShTOAIp7H6LpfzVwRS9P22gfvMyMp1u40EIHG81inrrwwmHvgjty\n1qZVoymHyfsgaLnteyxocq2Bgl2MNZwiynsyMRYOORTdaGBo/FHZ+759EwOi\n7aH2th5rHldedBlAVl5QwGqTF45va4Fm5etU/VJWyXDnu5z0NNkjgIKF/HMM\nDlpLywIJzj1fa3hPqBguaEsk2TjFqFOB/yDZxAAfUyIwwQ17pe3R8/I80LYm\n4/w5bOmk71xMVH3ywDJYjSsZksXOqk5f1OOqc7CgXDhWU6rMhKLvhPV//0XE\n4o4m13DbzFUhn3sgEvRBLsbQgABvIlUKLjW5x0Kz4OYZ3te5n/BH+DY8l1vh\nbtQULnoKWOjpAB6jyHWdt+vLVBTSeh4D4tdzkkaj2dzkN5Qku8jR6j3MKDto\nNoZh\r\n=FNpR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.4_1586364006854_0.42612895797428374", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "6038623f6cdb2f445f18fe48374ba3e8703f40eb317a68e684a62313bf77af4d"}, "4.17.5": {"name": "@types/express-serve-static-core", "version": "4.17.5", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/aereal", "name": "aereal", "githubUsername": "aereal"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "dist": {"shasum": "a00ac7dadd746ae82477443e4d480a6a93ea083c", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.5.tgz", "fileCount": 4, "integrity": "sha512-578YH5Lt88AKoADy0b2jQGwJtrBxezXtVe/MBqWXKZpqx91SnC0pVkVCcxcytz3lWW+cHBYDi3Ysh0WXc+rAYw==", "signatures": [{"sig": "MEYCIQCW+xqAADhtXNDqsC08YTT2tNv/lImqkB77Pd1mK/ozQgIhANMni6YxB5YacHqzmiQdbaTvcF3CjO0ch5gomnQkROLI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39943, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeloDUCRA9TVsSAnZWagAAq28P/0mCZ2amGlX6fdOOKf/3\nxyWDgZp61fG5N5uANEWpgtS1P2oFY3H1CmkHGxfTnIuTtWfxIc23NTHLF0sO\ncAEm/z8nXQ24AwBEObs7GKzblsfuZns3b0GyyeG2qPyGrs4l4NeKiOkOqTmj\nrw1dd37QAdjaQTozbQjX83CLvgjXgrafYSDYpzzpYyJM2P6B28xXuFHqUHqZ\nW5eSG/N5D6esn0228DAr4M3p46QahB/LPsnC93bwgAHgP63uLxWfoYf1lNEF\nl0YTG9/QqWwNi6UK00PCzbQld4a+IJZyJPDI16EUmXfhYh8Ti1T8XWmOr9G1\naMwy3VAMKfcIgEGX2vB0Qa7mOxGsooJQKMNz7e6kdGlhXsxKehJipbfEaET2\nnoeCKyVDxI4ZUHtnf/Hb8tDAip7FGKtfE8N93gmxpI4lUCAhk5Im2nskbb7S\n17wtTEpOYqpHtsIOj4diM6HqFyvozT4UmJBkqO5/4P7nBpO9uPU/cckKB7iR\n6gXYFCfH6ORzir+u2X8iB0vt1WniNz65st3SXZLHO0XuHQyqWqSwBH01HWUm\n4KFiClyN8BYzgnJc84kuAfeOL1Z9xv5URGvpMCM81OmMqE9qvyTom5fRoBi9\nYAVaDJI50oIAlRSipLDfPgFl8ryDQdOUuRvZCIE18QFmNijSAc+mwHyIvsmp\njmOW\r\n=fReu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.5_1586921683753_0.5552827000831542", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "6f11b0a320663b711f0a20e1d54b8ddeff4658036983278ea3c5f628de4950e3"}, "4.17.6": {"name": "@types/express-serve-static-core", "version": "4.17.6", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/aereal", "name": "aereal", "githubUsername": "aereal"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "dist": {"shasum": "ec825455acb075e7fc804f4f7b7734e043003f43", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.6.tgz", "fileCount": 4, "integrity": "sha512-U2oynuRIB17GIbEdvjFrrEACOy7GQkzsX7bPEBz1H41vZYEU4j0fLL97sawmHDwHUXpUQDBMHIyM9vejqP9o1A==", "signatures": [{"sig": "MEUCIQDxve0RzMm4kyVujDJAd2Olw9e/3aZlTmao7i2LWmx9lAIgWmkNRORaoNRbhZYYyegc6VBO5LKd6spQ1HuY7Rnl7hc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39904, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesZhSCRA9TVsSAnZWagAAHdYP/2TALJ9KqnKtkIBtTL4y\nIpNPxu7ctYt0shwt5+MuJUIMgurXIW7nBcCV6MgemqFX1oWUU1PPhDG8yq7U\nbwBtXV1/WpmP1f7T0JdqWnztNPktKfcC6zjrPAU+410tLtJW5q01XdTBW08G\n2ThCkcXeLVD5GQjzaHGAUoBUljjLiJz3oqIn3vOM4z+/mIvDQd9dsAztmyVn\nnrDKmpRdc3OMIYm2Pjw0JFoHIA2l+W4Q3+LHl4ZjeOAHKfuYW7O3yi5TUfCn\n1iHSV4hBXoOidre9X065tY9xVj674IAqC3TKAMsBv3DnS26+WHIB6T5EVarI\n1TjURf3maIFt86WR+0yIyd9xESRWP3I7rLRWiHesZU1LRKUQ//zG7VX5B+Vc\nu87JTN0VUv3UrvcVESQ0d6OneK0ERyZEr6HwNku8vGkcJ0ahMc2aglQsGVII\nQKN9ylCDVvS+mCaQy/c+LfZFGj9v1lWP5GS2eFH525mBX4RdnqC2gC4bzvk/\nUrAgVx4+5z3K9VTP7ROwNgyZ8prSkuzGbi7XK5rJiY3z+sFpCAFqJXxU1RfF\nDpQsQblc6JGn2rKMn7Wg/gUg1gV+uPvvxf/3w1ZF7/CpcjZtib1XL86Ww1Vt\nFLH+cHAJWOJDVUcibcRsqnmATMg4D7S7/nTmgVTb2dRz/X6gMfZDiyF3aWvT\nLA79\r\n=sFTT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.9", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.6_1588697169613_0.8778146442642254", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "184fe9ace554efe986c5b1e2644baa356cce7f6a053e8e146704b3bf68b5c0d3"}, "4.17.7": {"name": "@types/express-serve-static-core", "version": "4.17.7", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/aereal", "name": "aereal", "githubUsername": "aereal"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "dist": {"shasum": "dfe61f870eb549dc6d7e12050901847c7d7e915b", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.7.tgz", "fileCount": 4, "integrity": "sha512-EMgTj/DF9qpgLXyc+Btimg+XoH7A2liE8uKul8qSmMTHCeNYzydDKFdsJskDvw42UsesCnhO63dO0Grbj8J4Dw==", "signatures": [{"sig": "MEUCIDDIboAH9wWc+JMpynatHmkeU/g9OlA0f3B2AlW4VQFyAiEA6WGKcYLPZdBXqxNj6vd452eN0yXpOvT94lpZqmVVrJI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39994, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetHByCRA9TVsSAnZWagAAt1UQAKPF/kvKt4MIS7RD/kKK\nZ/qtsUGlTq8ADztCaWGsh/11RqXjuP+SzMtBLfTSaGZvyUDf2IbfGvuPuFrG\nNcIqMMd8WX/A3hWbTGUTX7Z79anNYKHyO1cv3hHuitH/mMeeyoYBl+yQ5O8A\nXA7KauVTNeGfPWnCakTw7tUbF6ELKXx6pdCyoA2lrR5RqSDCJxiLMU3MKC+d\n0JGBZs5n8pMGcz/1/VgcRIKzkDzpY7L2Pe3qpAKW5syVKbnmicjtzfVCMKAW\nv5MYfS7BwoULb5q8zjbFQ2B/jK/gIjSpm7llawNb9MYkPUzzJLVsHfeaLABr\nDAWNjj/mZim+Q1+JSQ6sLPQP2tFHnrGLc3eu0HTlU8WeEpPTqzarJTpNLvYU\nQHCI6n2fCIGTq+yWJamrqpoUxQ7SkIXm1EGLKydVvRVSftbtG1GmRTjIEj/5\ntWoG6Sp8ms3oySr32e2sOBy8pxUIkFNYvtXIcRLE4qOmdNsZOrhO+27SJWpY\nbx7bALxumURFmnJA2dxgmKemnOui7xx2qa7bAPjYP3iVq11gNFR+4ll37sdB\nTbTSUkEgpRL1S8bE5V9XFLGkDmKocq+9iu9R6fRO/UdXSFVI5LUM9MVasrKF\nvP4xFRjWjz5PQmVX/6MLEtcAO3lYG4/A/TZKxn9/h/hNnKPD0gfKfV3zTp8h\nr3Gg\r\n=uO42\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.9", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.7_1588883570159_0.9492715828684146", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0fb17d543edecbce65088b45ab8c72be4c2039074fea5a9da768facac6d43d42"}, "4.17.8": {"name": "@types/express-serve-static-core", "version": "4.17.8", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/aereal", "name": "aereal", "githubUsername": "aereal"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "dist": {"shasum": "b8f7b714138536742da222839892e203df569d1c", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.8.tgz", "fileCount": 4, "integrity": "sha512-1SJ<PERSON>+R3Q/7mLkOD9ewCBDYD2k0WyZQtWYqF/2VvoNN2/uhI49J9CDN4OAm+wGMA0DbArA4ef27xl4+JwMtGggw==", "signatures": [{"sig": "MEUCIBoxDz/Ggd744NxmS7rlj25V6B3Dg3He9ZhXoM0A2CNDAiEAmbYJHV4WWOmDfS8pphQyMNld75U5vKOpmc4CKmb2xXg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39995, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8vM1CRA9TVsSAnZWagAA/98QAIzVL5JqJAOVVbNcSvg9\nyqj5KvlG5xMRcmlwvVwjOwwzlwDGsO0Ocey0XISnjpbekVM4ez73KS9onlRH\nT2wfkJdlDmIwD3u1dgt7m+dLzh85is+qeNHkF1sPYQDjbo74+I5PFpMt006K\ne+GH9cRbnpJ+SIpabgoR5opgkOAkk++9iOquzi2HdzFg6SIfj/L4A58i4rJi\nwa8lNUqMxWRBFOHN54aebm1H6F+oxJhUi0D3Mh7WARqQ8IQT+oDEYLF2cfKj\nzZw09CfIM+vfPm+j7PX0xL3dgCSx8B9Fd6L+u9siRDNKy2qjo9dVNmYPuZze\nyCvjMxAln+dBHgt1vt63eplEM4zcgD5IW+ALqMf8XpqzlXH/XorjGj8eSEgZ\nVJ7lTwAwEjka+ozabwhf/0VBjbXbjLioSFcrEVTElfjw2dGPjE3XAy0lHfhr\nR+clzssGzX2sryyuDwOsR9z0P9qNSJGB/tZjUgF7mlo4l0b44EHPY31s7avS\nf159J4kMJy60ncmrTACqvTygSYuMWADpmMQx/lFIIimb1UZqbo+s4Ei+JDag\nUE6dkAHwYT/GN/iET8c7x0M5TUKE1rpDi9PyXYoi+JaVkDjUGqCsQHoirGyA\nGmVOtCvsDsmjbdjLY+U7PAnyGfugjCKLlXfqnLJ0TRx6XPbxDkjjEQUXIkeu\nbXK9\r\n=N7X7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.8_1592980277164_0.20564565381325806", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a51fcca851b720190b4e13fcb813a7294380c38ee599b85363df6af9ec844105"}, "4.17.9": {"name": "@types/express-serve-static-core", "version": "4.17.9", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/aereal", "name": "aereal", "githubUsername": "aereal"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "dist": {"shasum": "2d7b34dcfd25ec663c25c85d76608f8b249667f1", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.9.tgz", "fileCount": 4, "integrity": "sha512-DG0BYg6yO+ePW+XoDENYz8zhNGC3jDDEpComMYn7WJc4mY1Us8Rw9ax2YhJXxpyk2SF47PQAoQ0YyVT1a0bEkA==", "signatures": [{"sig": "MEYCIQCGtxXmjRserTcHMcBlxEMtNfDxhLrc2AVvjYD30GqOwAIhAPoW2BZKWAZqU8WmeKyjKhyzGCVdYyRdQ/jsTn8T6lx6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40027, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGBcWCRA9TVsSAnZWagAAQ3sP+wTsTCtjsbO5BKziM/+k\n3VDab5HJIdk84eUChGbStSFW3JCWJpUtpBHoD8nII/yhwRegcMDv0mdPVPWG\nMC2D/l7yN1G+vSSLWHw0rBTMtKPgo1ac/kUdnezCd9FLOMJ+T5LYWu4GnRGC\n8QZMoZADjyif2u9hhQo8o1OdISQy5I5L+jZAi5ratcgM5LQtXbBe2A5IcpIl\nIns6bNd31ISWa9ZdfbQPtp058O7md913xpYX0ivjbLJzAVL6esD1InvMm3P6\nbCDu784Ij3oIEPpI/FCeLbT/ODUr08EWsFehZFhfBNWxRGXqC2f51+adqtos\nhsKP+JZe5uUMMEh0CsQPUcx9hSPyOjHUg1zuOT4PbGWvmcsddrQu/QydyAE0\naa7rguhxQLLeBnXjfN80FPruv9Tc06TdVkYntNUMZj1HFQyAYb4vArb904xF\n0xgb1TbXz3coDJa0QFc8MbmIFt1fDvGFsKlDpHP7z0YB23TgVerhb899+KNS\nbGE/rbs14aXCvMrOoohMvUyrdqXoSLvmfd4O7K1xn9wPOvQZ4fqzyLklcL5R\n/cpTsalZrxvJzaQXczyKcC8bbWGiLLZELsLD8coSEPfxLbocjLv+BtzX6Q5+\ncsGTnsyechxqR9QDqj8xBpsCaIuSShxLFpfPNTBERtmJc4SRzdEB7By8LtVZ\nTfPc\r\n=nvYb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.9_1595414293527_0.4778027407391343", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "5b617ec9e6cef860b9c63bc7c7344557257824bd487c9caec9003f0ea908e4d3"}, "4.17.10": {"name": "@types/express-serve-static-core", "version": "4.17.10", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.10", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "dist": {"shasum": "2b6a49f28f7aaa3c875338c65c41ef55cd8ae62a", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.10.tgz", "fileCount": 4, "integrity": "sha512-hkM+0ydGOwKRuxcDSjFas/zpC6htqcIcYG5gdPOP1bAhszBi+eB2PlMfUUXkI0EQSG1Xbgn7IZ+yD6GVYsqB2Q==", "signatures": [{"sig": "MEQCIHfkqwenwdSG4g3TVUiUdrYpcXjJPV3tlyaIi3x0wAqGAiA1czEXF/yfgNmAx/D1fQWZKa+bP55xFhkiiYaCpZC6Rg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTgFGCRA9TVsSAnZWagAAE5IP/1dit1tul0JLQLO5yyjx\nr4AytfD09AWHB+5UwD93kWcyNSEJN52XKgaRJ/pKYnCGM4UzroMZd6viUp24\njOFksePuc1Y3W/ba1b7GSRFGe0zNEzh4THM80aV0EysDZwKloeK/M087nelY\nwPUVcgDR556a/Ot8LONvNtmgx8GBcMGa/t5/BcYZUTFsW/xaj7G28aWFhw8O\nkZOp+iC+bTluAPJSdNMnRvZ2OxcKMuVoqUGmqE50GjwqD+LbAHltkW6XC21Z\njkpxPYGEOQHblV5YcEnJEgY4OvE6o3ijvoCu8Mckjn7LY1lY4LcO6Fa3wGmI\nwcqUHxbEoPsPQ5Jg5HGFuTIN/N9p2SH2KHWUFhjyBmJQbKEXNQvoD2y6BZas\ntXFU+GKKmlmo+CrPViSjdHLJolFl3nbRwlI58gE+beysGcoupgiQQRNzP2H0\ngtaDy/0z3yjJXu/okrvoEgERZd4xCCYkkZc2ApDL3xRh9uQ0A0BbXQCjqh15\nUaAjDhiBB5V3mdQ73lIzo99wzRNqPIk80CA1t8ylDU7G07DjXzt6dHYfunwt\ns0zgFOcQNQYQhACdovGLgkpGN6bltCwJmoa95zhiFIniRePNCkZlj1oE+KH7\n5jO4dFdVe2S1tQc1q4GKQGwdqtXphd7ne8N2ZqJ5gUo8DdywKBQQ++mH+SJ2\nHSvh\r\n=Em5R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.1", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.10_1598947654110_0.7210206672962904", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ad0c1d3bc2a1806a423b7e94564ddc06e9e1f2533560acd78db7277657e6d78f"}, "4.17.11": {"name": "@types/express-serve-static-core", "version": "4.17.11", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.11", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "dist": {"shasum": "35b8b3b17fc89c9ecd4dec64367c32c43b859c4f", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.11.tgz", "fileCount": 4, "integrity": "sha512-LdKPVG7AgbeOnqhdPvq1U/lHUm/pAxMMmYp53WKnV5YeMVizhVx7ntZhdDKlD28BFEGoCLV2AK/pwNtC+KduUg==", "signatures": [{"sig": "MEUCIQDeaJCDcHl9aQdAdQn+ohX46BRfNF9f7BXYFpkhoD5oRQIge5OCHw1roLBTvyNiUTWTG7ztfqjKtUwuolKuYnchcLI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39639, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTlDICRA9TVsSAnZWagAAX4IQAJ2xNaZeRMQGZmB2hKa3\nDXmTsXgFJaqzOAdHNv5p6jYAp18VHLvNRRF5UEYNC6s0V6WB8CANkz1MQHxk\nrevdzoHRSoLNo5ZHFJXbQGERBvmHApT0s6E9ix3iniStCGn6vY4RHpjf4HUa\nkDY8RUsaejboOTFiODK2Wt7OUbYbCjwHaQGt77ve41c74ce8o0gCbtGDEIyJ\nQ6quXlvQxEJfnzEJZkRdIQ+yAdkd97myVHJcLcc2rAPLU9l2Ltctf4xPU7wy\nPWLQltPS6kc5f82RtsTaJQC/Otzbl/mwCwEKj8ipE+KSM1jEbvSZrcaJS0wx\nYSkPZczfedbkSEaQnMJ5vf7PNmLztqU/pgwtFnCh+2tbImaIV8aY4TIf8SSn\nevtRKxix+hsoZkAi6grWj39HE5ARV/mOD5mI4RYPeSUfUH4jj/FrbFFDcMsx\nP5CnW+VDNJtB2Cl37EcDfL9LAxHDQCFY1r+OOYeSou79QzH4yhXRpkVtTr4x\nmB///NxJgM1V9gi1J9O8vjqPRSQAhk4UHNmCQxmfFIjRGJnxfjfAFYjQJLq2\nvTvO5RUSEFBi6KNRHsTt0/USeKjfq3fW6AccLOOOzPCcDJy/NlQgIXUjjZI3\nERtqJ53YdJEc5iBrFoiENQzYNlQPb7SHmZD10HdoAC/toV1Z/aKG+9HJi7gw\nU/7i\r\n=nUQU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.1", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.11_1598968007920_0.5134278521376952", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "5b39b78883b4871e710b7d30057175f1acd16f714976238876a92d164bb299e0"}, "4.17.12": {"name": "@types/express-serve-static-core", "version": "4.17.12", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.12", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "dist": {"shasum": "9a487da757425e4f267e7d1c5720226af7f89591", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.12.tgz", "fileCount": 4, "integrity": "sha512-EaEdY+Dty1jEU7U6J4CUWwxL+hyEGMkO5jan5gplfegUgCUsIUWqXxqw47uGjimeT4Qgkz/XUfwoau08+fgvKA==", "signatures": [{"sig": "MEYCIQCeiU75VdVAq7Ya1GBqUt0ClU19Q4iu/mXt7se4CKkQvgIhALyqfvqtXVBU/ZvFI6NPKvGmxWAFpr+tr34ReJZiea9h", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39683, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTnS6CRA9TVsSAnZWagAAx64QAI1uE6MjSNZyulr2UVxu\np9SMzcO+Ky7OMxQyj+tEY4HNW6iIL8IVuQsicyyJbeg2UqJ+M+uhlglvT6HS\nNHz5iuaaSQz0IyKq+0k1WvvFHcY4QxPiEp342fRB6/p+pIDaOP5PLWCrUcSS\nlE3yie7blsEjn9+3jNM630Ydlk6IarIW4z5lo18xqgG9uOFOP44rV/GV8zQy\nYKfJPQuBXFEgdz+j+DYJoUr1Acb/XfVwD+chzhjdp1tO/PWR6yTRgVtVxoYd\nqgQFw3ZjBCtQLo1Ich+KaZdV0iCxUCcSYersqQP9SH75GHD/ePDLESdw+67P\n7lLmgkmUY54jiTAXO9+a/AUIdQAc7SAadTLVYXCYe3SbFB3o1mxX2hr3Jugp\nd0Fd3iaXXeXM4nd/NHr7mHjEDxUu1oPKXVhSlZNUCedfszSp1yq+V+36J6G1\nGJ3rsn+5DPskaQrjfZER67PBqBIq/DzEExk2cQrY0WC2U5e5QKhqvr6WkO47\nUOB5GJAS2saJ9bBjqg6VjiQWMJf3KihH7wuLYaEErqrfKFSoMi7dUA58oNTU\nidhEfdU7IYeZFvXAzr5BqIWLMbcjVjtQ9rzlPOZOvDTAcBAv635LGMAC12Id\nZDk3Z2dzmZokb/MzUurhj7/qs5Szy28Ub1eaurfqn+Reso+jQlcf0sKyrrjl\n6YgF\r\n=D323\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.1", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.12_1598977207134_0.08722306139579072", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "94e77393c3c9cb9c62b0161f497fe943eca7f2100b4b24925de8185efb727931"}, "4.17.13": {"name": "@types/express-serve-static-core", "version": "4.17.13", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.13", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "dist": {"shasum": "d9af025e925fc8b089be37423b8d1eac781be084", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.13.tgz", "fileCount": 4, "integrity": "sha512-RgDi5a4nuzam073lRGKTUIaL3eF2+H7LJvJ8eUnCI0wA6SNjXc44DCmWNiTLs/AZ7QlsFWZiw/gTG3nSQGL0fA==", "signatures": [{"sig": "MEUCIQCCDjpQIreAM5ktb2WW3M+k2RL2PXR3T+x3UBgGmcx0+AIgKXplW5ul5c1ttk3j0Y+Y4EX5YYgcf8q2xKPibGsr7Ic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39685, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfaUQwCRA9TVsSAnZWagAAttAP/R5pjba/AiRrkkQvVYAU\nwjOCk7by94IzpaDSIl2RRz/ceuqTgrI6UJOgDwNueXB+kzosXgwW20w1rjXH\n4Ii3CiOuSrzgmOZ7/m8fLy0+bRcT4Ff+ErqNE+ULjPXV7vPRNtJgdGRg9UFh\n7r3hQXUoTBV7uUt8I9MQVfkxu5DxodCeIPykL25C1LqdNy2i+udqtApbfCvO\nl3kKjvvJJUUmtei/LTKRjgNTXJL0UWXYzdgbOemaZVsk4gwvfRrVBZgvsnJW\nfrMGeSvvPfIM3nd0PWLbYpgKbNyWo743zsM9ffQlEwRle/dnAUOehxwMBRhk\n4YBfnOYiKhnVu1/wtzke1GmdwIVZnwtTzzZUYeNvu89iuxuvMWBaIfzOBjhP\nK5xXcIbdPugGv4Ir53x7MrF2Z+QV8/Sk7TkZ79lNpn5tNmLmKGnnRLYUyWdP\nLUFvOZ6MAhNR6Wq/0HJLGXGk6W6fev10EyujLKFVt4/G6cWIfFgR32DC210Q\nEkTZWCwLhQP2uYogYxwgorlv0Ij5Yh78lFIPJymrAo1uPxkvTsrRpyzI3IMH\nkSbAirmLYneCopyOCRweNA/Xklm2357P5PhvGgUMNbg/boMZNeH8a0qGJ/RW\nPa/n3rH/EYAy4rTa4mkU5CXSnodduzgv6vfgol6uW2vX3h7OIQVs25xgE0yl\nMwK5\r\n=F7ka\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.13_1600734256440_0.25547805617788866", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "eb61bf4b2c6fe989b551b73f79148f4f208efb4aedcd8076a5262332bddb9300"}, "4.17.14": {"name": "@types/express-serve-static-core", "version": "4.17.14", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.14", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "dist": {"shasum": "cabf91debeeb3cb04b798e2cff908864e89b6106", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.14.tgz", "fileCount": 4, "integrity": "sha512-uFTLwu94TfUFMToXNgRZikwPuZdOtDgs3syBtAIr/OXorL1kJqUJT9qCLnRZ5KBOWfZQikQ2xKgR2tnDj1OgDA==", "signatures": [{"sig": "MEUCIQCEHv7i2HJqo9hQRNQ7JQ8FfVLyrzQ13r9Ws6D8qEk6FQIgeLmMEucgIX0YBkIehSgH/Yl0DmkKzUiyHWmVYr7iWhM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39687, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfvCBaCRA9TVsSAnZWagAAuNsP/0I1ksI+RbcSNIipM2Dx\nn6kvs9xzXmojF6CPlSSui0D1egWA9rLpi0uwBJUZ3DGeMZ8t9gov9EXZQsL1\nPFdc4ZkYSzOH5EeCAscpqTOZRl1UKem5qVIeW+9tJt1J9M1iUaud1Mb5IGpE\nQeWs8dj0PvCFBQ8kHguHScJ3XWLFZGqyhTQIriPovYBOj7rSn0AgBO31qKp3\nec/99SHMYjm5AovNichDnMh7j7hp1qKXsnrPr+9qIvJHzUoqNl2427Xy74gq\nTtE2Qp+gPT736lmBmS0dWTNR+iPTvtq8qUueGeNaArKDkcJCgLaDrWl2qipk\n6QbK9dBa08pHBUfmTEfgc5Xn3S5Jp6s0npNq/KdLZ9V/1/FHjc3WyxghdYWs\nwD0Mv+Gi3reu/1TzNqX5fNHp3j61jzHVL0BhXnIm7WOBqYL2lknnq/+/dOnn\nfjFVQQeo3NlODwyt7BpGgcFKyTD/FGhY3mZqT/R9TEWt/KtgMrqbt0cbc/yr\nw2UEQOm6jo9/KFfMYkMCJMpNt5OjBTHQ5st6loo0aClsSwjGfN/vnBXaTowa\n7Cvr+WPDw/gOfN1E8H4eWY9be8+xqRYX+bCsRqO2EuzxXz2VdKz4AmGxElSK\n1jVGjOkGofG0ujY/Ra/qjlu3V9MmpH4aM3HCYp0AB1yb383j5DNil603NIZP\ndIp3\r\n=8Ejk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.3", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.14_1606164569802_0.14385044096893917", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3f4e3ad6901fa7efa969bb72f3f2e79f3bb4536bab4e4dc75d2f452a361d5cc2"}, "4.17.15": {"name": "@types/express-serve-static-core", "version": "4.17.15", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.15", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "dist": {"shasum": "7c3d37829a991da9a507c1efd44d97532e8909e3", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.15.tgz", "fileCount": 4, "integrity": "sha512-pb71P0BrBAx7cQE+/7QnA1HTQUkdBKMlkPY7lHUMn0YvPJkL2UA+KW3BdWQ309IT+i9En/qm45ZxpjIcpgEhNQ==", "signatures": [{"sig": "MEQCIDtJJqGEoWXAYKKEUgHkbyeMnVZVBdYMgQs9Dd9KSypUAiAuiEhi0XXrZ++29Tvm4J6iacXIoWmAaltxo0V1fDAmPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfz/K+CRA9TVsSAnZWagAAZkoP/iu77uWo8AJbuPXfqmin\n1P6IGYTY6OxwGTUk2RHzPyVguzywlu1cKqJEPBLf8lnQNukXXbPBOfNT3JrQ\ns3B3MMH765Yhty4RkHK8VJe918eRRSZ6rqzzz5XoZ20xcZ3xCJUivEUdTo5c\nNzsTeGW+Z/poOpW3CbtcOq0ulb9+RzxovlqSsSmrGwL5cXVA1wM8tdbv+yfd\n/G8pQjp+0hYnosscZWhuOSc6kB02C0eQhBLLguFdDdCd58iXXZsZ4TPzTQrB\nsdW7FphOtVQDtZYwe4VFpMzRkbmDl9DANweW3XWvoOGlvgY2nLpOoHD++fSC\nx5UgTo8qv5THybalu2yWgxRIIicNBsNVsperAyvES1GyxD0xgBTuBMP14Lkj\nSlU+MoZF8gBY5xeRsZYTlTCXrTO2t6Y5+G5Sla49h9+YinRPx5eFhWMlBENL\nOhpvnbMPw2Njw5RcbPkdVS2da2J3w+NTAJblz7MJ/JLdkr+1lpWSnnqCBWL6\nV+CTZEKSa7wm2+oVdMk0007e4qs26+3+aZH9esvjt0/R4wOVd95kVTULHMrg\nD9BOJ9r86vI8TcaKQyucbtKMBOmF4kexQu1aqGmY6T3ewMojlXKlLRLl0Bxt\nOZ0QlMNU1+tBwPzZ721w3wUMnj46NQLFbTvVjodRC8DoEp4lVEHAYQzj43RJ\nCLPD\r\n=XM0M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.3", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.15_1607463614299_0.6851432072061638", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "36facd3fd1b51fb2f20fa6908cb3954df34cd8391338ec2c8942314d2f321999"}, "4.17.16": {"name": "@types/express-serve-static-core", "version": "4.17.16", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.16", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "dist": {"shasum": "56626e2f60bdd9a5193bc84a1042a506eeb69da7", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.16.tgz", "fileCount": 4, "integrity": "sha512-hGGiDMatRhcRF2F0EsxSX93iKQJom6No2RwwCm/dvVtNt6FMuq+A/Y4qUZZGqIjqe0LE/jN0wzW86yDFSJwn7A==", "signatures": [{"sig": "MEUCIQDFscs4X+XbtlcLQ/6enmwnQSEpqL/PWH/Drq9K457VBwIgLJyrOo3x2cSHY3RcQ3y3pmKhyIy/+R71vFMpEfTJCJo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40201, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf16lSCRA9TVsSAnZWagAA3FwP/0J+kBtKlrH0g+NpygQi\nWWGuXjS85pIjMC0PuyLdPMD9VISwXKqXWxPmeyK5BEgN17hnNu2AGFDnS/aA\nSkMi97on3rCFwyDT2z6RH14a9YOc+jctBUVNAcmrBbz9/MQ3LlCwXlZcnDeg\nvS01EbfPUZRZkYmIOHSxsqS9ryWfjCx1liAAxqQyBs6M/XLw2pEGFo1gPiMF\nb/xdsuoXF4Vs5NjYoFCQGQJBq03agF7LvvzPTFhsSdmLj2TCpkLexT4jkEHF\nDZSDqnPleg9H/RjW/ps+QX1NamWBW0YU+vWbx2jjLkwL7y+IqibQxHJU1gYJ\nXUnWF+J5gD11EhRZOGR4K6Zq+gAc0R+KqkPsYT8r4psGIcLOV2Tk/ORLlqdT\nAErtMosd12PSa7Gc/epeJjujX/WhOPhAhFCBo2zNmoWBoHAmCURbEBGuz8Fi\nNxrTJqbebhuV2xjQnuWLF9sIlZBUBKqdg+YTSCKN0B09qwfbMW+FxOC8oXn7\nuWZt605tBOLrR47CmWktFc3r8DiP0q6c/SbUG0f20CH92vzGTuvav+uyTp9S\ntvsEhITIiZ5P+GKdTBqm7f/pzPkFs8zCfmi6rfAbBK0aQ2DxPB4Y16stwM4s\nQdnlU24LAFL5oY6kmkOzU9I+qp5LhsdweGNPIY8OKjgw6J7qVD1/EGrI4uLb\ndy9v\r\n=B1kf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.3", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.16_1607969105713_0.2405274569956366", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f7033eb872882e58363ee913b09e61ea4ff9ba1571ab6b8b89e97c6d6adb16c7"}, "4.17.17": {"name": "@types/express-serve-static-core", "version": "4.17.17", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.17", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "dist": {"shasum": "6ba02465165b6c9c3d8db3a28def6b16fc9b70f5", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.17.tgz", "fileCount": 4, "integrity": "sha512-YYlVaCni5dnHc+bLZfY908IG1+x5xuibKZMGv8srKkvtul3wUuanYvpIj9GXXoWkQbaAdR+kgX46IETKUALWNQ==", "signatures": [{"sig": "MEQCIH37k4sA2WRXd/y7vdwmkC0z7UrEB2VoYGSrx+kojC8zAiApBbTbgOnCgN4g1qxDk+m+GoNpGLV1YigmN7UNIijC4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40202, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2PMKCRA9TVsSAnZWagAAXtAP/AqD9lAGYBhDI1pYnZBo\nAACFb/Z9F0rHXkn/KONk5yv4m7OXcdJ2tfM5+oEHHkBAd0IUm1QuTcM6Wgfa\n/3Uz+vxii5hBaVywXRNBOTmpWtnYsfkYKPQEErEhiuuA/e1NL9Ox/T1ZhZDi\nhwW1j2QLE7KlUC+n9VmZd2YND8Ln/SqtPEAcxckoP3kidYLqWw21TgyNaSPX\n30tRPMNvFo691rttdk5/FmGXv1El/tcYDADWYLciypalz+W6RxWy4Rf6wjVw\nPm3e5a6brDNPDE0fKhQzk/b8zuPgGnMxp6AqLrqiTmAo8yM4zHXEZ5ghABbi\nrIZ3cHK9BTdlf/qe5yQ6egi4N7yC7Gq5w1rJOTC1rzy5m8CAUNODvO2SI6Ob\n1ycihJ17PnAC585isTnZekeSIolI0fhVOdHnW2H/1yttdq1MX5wloXA6R8hb\nkogR2c+nEJE/Ik2k4vyRaP2wKQ6/J/vn1VeXVmErEINriN4rLEKzmb9Rr6JK\nMv0dZTd5eBaMw4eSKZXKPkmB5/rcOH8UJC6DDSuG9XsJa6CSO7BJ2iQ5D3dK\n2Jo8vUJsmJ+HgQkF3L/1d+XX3rYI6fWxCcb0mRnep9R1hoxMJQ228y4kwvBl\njEH6XXgMeeCSU/xofINEj1Dk2/Cmekr0PtCt5HJC3fQzkTN8jtK3g/5IUfYw\naSUR\r\n=34Ss\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.3", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.17_1608053514462_0.7238705550839908", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "6a2ede4cf42aedc8c6cd545ef9b4687b688370023e3a3e34aae2169ab97b5b01"}, "4.17.18": {"name": "@types/express-serve-static-core", "version": "4.17.18", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.18", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "dist": {"shasum": "8371e260f40e0e1ca0c116a9afcd9426fa094c40", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.18.tgz", "fileCount": 4, "integrity": "sha512-m4JTwx5RUBNZvky/JJ8swEJPKFd8si08pPF2PfizYjGZOKr/svUWPcoUmLow6MmPzhasphB7gSTINY67xn3JNA==", "signatures": [{"sig": "MEYCIQCpqNMEQHUIKDdbdHb4Xywo8wTpDk7TD4AocRa7daFnuwIhALjlfeZi44SFHBqywl9GnF5yuXXlewrnDYmxfkSVZod3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41284, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/M4NCRA9TVsSAnZWagAA8TMP/RYmZDH7qzGbf/wY4ypM\n+uGAVgS8ari/oD5BvpqJtnopXpHvqJ81Q1k1KTBAO6Mz5txYXbNLYZJCJXuW\nHs5QsjHofLL2reJI5nzg9fhRnah1b3VayyTlyK1t1KZpNz0/Bkqvv5BaPGwK\nGVAOysE7bn/imQfbjWCG/Jg+ltyQqhFQ/ne02APoe7HHaUx31DnE5ckiMHzk\n0OUzlavDAnRHsSERsCaiw3d3vOxhSJNBsY1w+U/dssjPrVu2YIKA4V7skZSS\nPpf7cKaAtB6059hwU5xs4Mm2JJSIaayvtrh94sAomuU62ENlruG4fffJfCmH\nXU7zXOrJPsEhPKiwELvVPEuDSBnlsVh/R0mbUzCiMdKG6kzo0f00lH6dVq8Y\n35Dtl2u60K0Fz+NHDNrl2ALnNxvgTwD8UlUBWszxL9tHvUddy0chR+RYaQia\nvOehWQhnJLSiSHOaZJedbQACa5Dw1ev4U9A77U9IVvdAvPhwP9V+XTUhRS3p\nPvPABfchjTrAuDlmJY/OTVvLIVXNetvgxwO689Z1md+25fcKRtuYDPwSWGA6\n2TEOf55i9KsPt7JMsRxmlq7rJKv4omrNeWBofNcADILS+G2fq796Fj92Xzq4\n8qg3Q2Wj9HkHMHpLbgdVqzswf07z65fJd9KvpWSw0yc1uJxHj8y6VhleUNOc\nC29t\r\n=8uY1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.3", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.18_1610403341553_0.1570909337265769", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "60873d177898f88d23027366f6d52aec9d27759ca997fea4f7c6b9729356fc20"}, "4.17.19": {"name": "@types/express-serve-static-core", "version": "4.17.19", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.19", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "dist": {"shasum": "00acfc1632e729acac4f1530e9e16f6dd1508a1d", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.19.tgz", "fileCount": 4, "integrity": "sha512-DJOSHzX7pCiSElWaGR8kCprwibCB/3yW6vcT8VG3P0SJjnv19gnWG/AZMfM60Xj/YJIp/YCaDHyvzsFVeniARA==", "signatures": [{"sig": "MEUCIQDl3xc+qOT3B0Od7tQGduqxe0sa1X/Eu1l1acm1LeRmBQIgbCXdm6SloGfJKDKU18nuLHNbcO/BpOjuY5pcLuzPaBw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgT53SCRA9TVsSAnZWagAAJCsP/1PbxPLLqi0Rh75ym79D\nN6T3j7FG7uh5lneFTL3yyHkAH5jBUuRS5m53Bzi3H3qaKXPlLVXSMCJmS9UH\nz7+FdE8tNGOMwewcFIoq9HoW4QvzV2CbvjhxHnQUuyoYg1rRDWQP+v7uKW9x\nbLsONtsTHg2LRJLTZAXvKVXGZMufcvL5KhQa2R1iwJlsNTySRawowR0m2vJP\nx+HJNTi7VMebr4Y+QgS+G5vyQW8uo9wcWG5nCkL3dbg1cHjMOYtIVSYHjp2U\nNdG9p5ixtIZal3cHD1J2t3XvAzP0DUCH8Pz7HZKcX/+fFAIjbV69GLofMjSn\nqkUl2YtmCfxFf0C+PKfYf98VAt6FHQNca7g82JUlTBxFTavhNs+rm/2DF0Hk\n3Ez7UKqtnqI25mSYKlqZJ+fcHSUcDulSP9YhKmhjWxlHgLqGC8EiJfmxS21C\nRnOKzhQX6zM9VkYPs9hb2esnpsXUnjrdVQ/I/42uWYnLPlpV97A6qSP/Qvv/\nVUeeWft3OZLFSmF7fAJQN59AIRYWiU1961R/dBSvP0V+JHscOtXZ3lyFr7VU\nFifs6RZLrgBUSAhm3bsBHgdP5v9kn80BVvcEunCmgyc7sDKxLoQSRgEvA2q5\nbmpXjCYl+N5omLLT++bVJX0siAG7sOmZlvQS5ZZqpDxAJCWszVCa3LBqRqLV\nbzjZ\r\n=EvOl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.5", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.19_1615830481646_0.2675170674488112", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "04a7fdec6461c3c59dd526fd0aa05b6e1cd8df4b4a0d0327c834087003ffbbbb"}, "4.17.20": {"name": "@types/express-serve-static-core", "version": "4.17.20", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.20", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "dist": {"shasum": "44caee029f2c26c46711da5e845cdc12167ad72d", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.20.tgz", "fileCount": 6, "integrity": "sha512-8qqFN4W53IEWa9bdmuVrUcVkFemQWnt5DKPQ/oa8xKDYgtjCr2OO6NX5TIK49NLFr3mPYU2cLh92DQquC3oWWQ==", "signatures": [{"sig": "MEQCIE+LMi3O+0yCGlJ/v4vLhuT/PvLgvK4/4sAv1bCdgXUiAiAauC/26+D2kRasctDWe1FB6fPFqxuHv0mImnBdnUJqTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80822, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrS2BCRA9TVsSAnZWagAACC4P/0e0LO6QheXkooQuNdii\nU8LaBI3qfHIjsSFh1jMewzARuM94xb/J8FX9AtEB3vrUfG3OixDEfRwTSws8\navxg5AsdshTDEgTshp7xuyy1X10JPiMpFp0MV28fSkcPa/Guz95bbcCHt5A/\n4briPdRNiZx7vnzCxNEK2jYsur+/bv6L9HyACj0xMxJo4W4dTmyEvadCqGfn\nDUwBWzfCwn2o8JodcOam8JD/QMUBRlOXMTw9mdy56OlJu9c9vZ0+4vhUOVMb\nPvCQrMmpR47EywH845nXx7D+GvVkcPCe5IZ47P2mJ1GaPV/k1fpcjUKvjMsK\nM84B7Lg/zjbFvWDgPLFzps6KF3CMvkwh/kZdmpt92RYYDCLMPRvDc5dhhyn2\ngOmiV3rI0O2GaM7TnaotSSEsAgKCQEHMkoqOK4Any/Uc2kALDa8j3b8hEjG6\nY9I/Eh9Xs2IwszrreBEsIopLwMcW+FMpEZRpSgeFKdkxgnRAGRyew1M2YbTA\nF4z+PYD48FcvIyekkGaw3U+VBAcgG9nJHHwSGLuQZrneK6+PIdTHtKXkRKpZ\n2YWFWq+osmH/QP7/hdV/2lDoCYFyunczWIyH4DpD0xzdYPYRx4D2rG/Vri1W\nULB0HREIwkQ5361ker7lkzxvwrvtRvKsaNsq3uvYIZGkl8viwo0uJVbNCuqj\nbBd+\r\n=wfAB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.5", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.20_1621962113282_0.8058594199180864", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "19ec0e1ea3e2b0c3ec37b2a7294a7f0a0eaee0ffab6470a4781a08a6d796db2c"}, "4.17.21": {"name": "@types/express-serve-static-core", "version": "4.17.21", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.21", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "a427278e106bca77b83ad85221eae709a3414d42", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.21.tgz", "fileCount": 6, "integrity": "sha512-gwCiEZqW6f7EoR8TTEfalyEhb1zA5jQJnRngr97+3pzMaO1RKoI1w2bw07TK72renMUVWcWS5mLI6rk1NqN0nA==", "signatures": [{"sig": "MEUCIQDyitzevzehJKFhJB7zDdtMWflVlAUHeVG9FFns0K1I4wIgA0wS9J3/rG8EOi3H7lOTERZ1j/C/IQyqfdjd73bBaVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80934, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJguAbXCRA9TVsSAnZWagAAzBIQAJbAKYfZ2Bj5UU0CkiEG\nKkBdnSPHNjz8/prPBytfdWdV3a1u7YB1uNPaJoYbhuU22yuFAfxwSrizpXgc\nBJzsMzmDu+5nByFWjD7J/MSDk1OxZwKuc5SW7Ym2SGDHGtKTR6LH5ECQ6THV\n1fgk5RdCihWwRdZlUSkZ2BMIs0xb+OUQYsPtVagAHLxLzCMWKewuGx3lMvy5\ndOKSkO7XfP8GLRtK1iuaYu1ZEKRyoZVBd8Qql49i+1avLaqYMeQHrSnyzKEB\n9yCvkt/MRJDqcK1cQ6Lk4lOX9XqEVH82UVZJuZTUrF415FJhH/JJBrKmyY62\nPOjEtMSRM8T4jfjh0Sr1tuaOOgps5H6gzG4Vqp8mjiLT9NS9LVr1haVquCSy\nboO4qHGio0IHnK7/PfVMzKUbzs8zW//EqhyZzDoLw3l1wHgzmKHXQlKGoZ3d\nO1AhBCJYg/+nDAxiENeRkFNFJiuoEvri2x6W6NzBJ7dIvKtF23qnAM4PI3fk\nwNw7EzKrPjYFnHQN6IHt32G83RYHNSBvsajrFe43JTQss998QAs1YIPj4Uam\nskmBa8UGRTce2bTq8ZHbmjQw4sC2UjQhS7F/WtWgHsPnTKIo9CXmJFABR6Dc\nKfoKPHo/ck7NUfQiBZ6an+fLqz3dSRevoxJtLl6knCLY/iCqwMhM3BkLoJYI\nt8dQ\r\n=nxll\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.21_1622673111120_0.06086197947664562", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e68a76ea27f6c5f791f7d488b0976574016c3cf4e15c97c1e96cc45ddde7a4a3"}, "4.17.22": {"name": "@types/express-serve-static-core", "version": "4.17.22", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.22", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "e011c55de3f17ddf1161f790042a15c5a218744d", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.22.tgz", "fileCount": 6, "integrity": "sha512-WdqmrUsRS4ootGha6tVwk/IVHM1iorU8tGehftQD2NWiPniw/sm7xdJOIlXLwqdInL9wBw/p7oO8vaYEF3NDmA==", "signatures": [{"sig": "MEUCID4bkNWSG0erESTxJ40qdy0EF3nU5Ac4sKskDk+RFY9GAiEAjomd5svhy2Mtz2SqbUpdRuflzhttjlKkB2cE3D+G6R4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81368, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2S10CRA9TVsSAnZWagAAIlcP/jJ9y3vT0kwmjQ5e/fks\nfFmGi+UGjmnuSji7kqDpvVaNUtVrjUC90mqMmmuonqqLLJgE9AbAzzdTJzL1\nP+ct3dakem2M3/IT5eO6c+4/AMjZMqc9BAcXt/lAL0c2nX9TCjnr29Q+4Q35\nk+bo0+aT55EkysJDFxTr5O2863QIS/RvWwafKBTEVmoWSHqkJwVh9EWCRUOO\nfjYqFcwyDRoMwaQ0fYardyw8IrkCXwLqqjPJcYJ/+PugW7sPmhJcignIhwU8\nxmpvHfoAt9ZEkU084d3MyacvdMorKCUpkPGZqprCIMz1lbJQB+kha9QVpgOm\nKJZzcYk23ugI7Mf943T5T9H9LAA7PDx/hsc8LHIqd1bL6/pvaA2GsUz1GpD9\nEdkTzLS0cOlHMam5SM1NTUIh6OlNF8DZFKsa8OTFA5jy7KHgbA6LeAuQdWnf\n3K8XmreJ7d3pWAWyIpmVut2fHWcglyVTqyjPo0pgEJdqVLYs57tZM7VmNyFr\nbgdpc18sqU8wwykAYi5lAGfKic0UhwUqFVph1sUb+eK8KutLw3PTGSbJCAk+\n+LCo6g24BwY9s+2KmdXyh7KmiTnsISFdYWMz7AE+QMVrKSiduNaDisYq0kta\nUToPz7ciu/nok6uNnVCIKUUXPTgtQR5jH3NeQAk4xaqmXsFTmQl7d0Z1GLub\nOezK\r\n=EFR0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.22_1624845684400_0.26771567789669737", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "06bd40d3b56db4a02e7596f6c5b3277089237d87a399564db2d44731c563dddf"}, "4.17.23": {"name": "@types/express-serve-static-core", "version": "4.17.23", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.23", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "721c371fc53fe7b3ea40d8977b209b90cb275f58", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.23.tgz", "fileCount": 6, "integrity": "sha512-WYqTtTPTJn9kXMdnAH5HPPb7ctXvBpP4PfuOb8MV4OHPQWHhDZixGlhgR159lJPpKm23WOdoCkt2//cCEaOJkw==", "signatures": [{"sig": "MEQCIB4VWX7HUamGj5ZaXclpE+RuQ+HHa6N7SBepQ/PK7CgvAiAAt/Jgv4KE9lnkWBbxFHy/4efmMOflTcODP4MIjhK6cg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81502, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5MISCRA9TVsSAnZWagAA6VIP/0oojyilLD/lqqWPp6TF\nhLqMVflA7YtiKKQzMh+pBH2P1U+SG/cZDQHBhhPnY5coOXMJVuF1YdsXM0fO\nFdBXZjv64uLN/Zz5kfMm7qOhJhM/Q9+QaNiiVekS2pHH+qqC/fnH7I8CKcxl\nP9+akWT8kacOdJAdOljTSxCgnmDnxFkttr9asLB5iUQR/UY7eVbkujcDkRjn\nqB/vsdsf9UEVfTOnvqC6gu625sij5C02B5zdiPx3Ar2W9rniv6mS1yEF8zrx\nOBF413Ew8LVcorFMTPoNQwEHtGerj0zg/xuZ0bwSmWGiQBEEUOP4W3RzDk2A\nqvB6AeddncEtIduGKObDyicc5WRNaiiRQ1VJr2Odi3j8iXwZycZQibGQdkQX\nH4ySwZR4pJHum/CkSJdScQziqQgKQEK94hMCJ4xcoPD8Qm5beWyjlDmtBWiR\nurP2k/Ag26AT4OWgm2y+4GGfHQ0jaE2UyXI5ELGDQ9PXAH2Jr00ADFnouLwF\nvq+YQ74UBkHfBAQeaGTXC/3hN3Ahj6Qfh67DEnVdlS4RExUviEw5uvlk1jcK\n31psVm3agaLZrKJOAyA16/MQVUl+LGl+3ZShI8Hnm+iTIJ/JTESwxYiyuctH\nBipe4BlMJ8z2EiV1uTGee3H1mI81gFhvy32xDMS3M17f0hb/fI8FLZCrzFjn\noESu\r\n=Bmgr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.23_1625604625942_0.7630952210361375", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "838014f17e1719c9403ba5200322f7f8f6ee8a1547de6fcace8b9dd1c612cef6"}, "4.17.24": {"name": "@types/express-serve-static-core", "version": "4.17.24", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.24", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "ea41f93bf7e0d59cd5a76665068ed6aab6815c07", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.24.tgz", "fileCount": 6, "integrity": "sha512-3UJuW+Qxhzwjq3xhwXm2onQcFHn76frIYVbTu+kn24LFxI+dEhdfISDFovPB8VpEgW8oQCTpRuCe+0zJxB7NEA==", "signatures": [{"sig": "MEQCIDlgiDNqmT4XmqDXNTz/qQlK/ivsnfJ6uYODWbl5o5SrAiAHNQvK/SXu/q5wBDxoW46fg/tp5Y7pw9BsaCXPY/N9kA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81641, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5yhpCRA9TVsSAnZWagAAkMUP/0Zn3YBGTI5RQyLQIQdq\nzNa02bkUMr3NjEm4PLHN1BArOKoFMsfXHDD7Z96XptR2Zc6wlVl+FZQcXSly\nw32wa7ZZtVQcOXsNay1dR3gWGNtYfkFVzIuRVO7wgPEF+m4sgvAOK0fPrOpO\nUhlCjBYHOao7TCN3/iT3OH96Wqf3ZXPu97MJLvRvlfEuN6bIPWADOegThvnz\np1B9jmy8xoOSPSpzfmg37MgFJu+BmOUKIF7YcUMKXwMaSISS6qu8iGb/lOjz\n+QsPEjUMPZhVXbBHFbJZ9f1yxgJlCu2fuL1qUK8n1rxqnhALTRa2JCF0xMp6\noMqwW9YUAr6qV14jFx2hQqNmj9kVfKVB1OF4TQowclk919lGjzWuN9Y9jzBh\nSo87FAnfessRyKjkHY2DJWk53teZyVST136GxjW03AK71R08V5LPBdjYf0H0\nHhNDkjwb9z94TDZPlPgE/MwsoaxWkQ+x7B1XcQhLsuQhVx7cJp+20q8RMLWo\nWBggR7IiPHLK3wcpIEx0H+lrx6vZJOFH8nXpm2sXdJnANqAScyQ2vmTCdwRC\nEnyUEyUUEhOS5OT7+oIoDi3YhDBVm7U8KiVHWWUgyQju3uwJtoIE7o8ediTA\n0NaVNpfpsrIo0OQveVZE0heuw/pSWzGADNeObkPNmaa4SYmIURwqAHMfeVt6\nYvLN\r\n=ANlY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.24_1625761897003_0.48888773419826403", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "28c771b9232b87dd5e3f8dc2f41428960d69db364050502917a529870640f2eb"}, "4.17.25": {"name": "@types/express-serve-static-core", "version": "4.17.25", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.25", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "e42f7046adc65ece2eb6059b77aecfbe9e9f82e0", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.25.tgz", "fileCount": 6, "integrity": "sha512-OUJIVfRMFijZukGGwTpKNFprqCCXk5WjNGvUgB/CxxBR40QWSjsNK86+yvGKlCOGc7sbwfHLaXhkG+NsytwBaQ==", "signatures": [{"sig": "MEYCIQDVsB8B71P7W49svDdNrXIdbqCVof3pK4huoLvM62dDOAIhAP/5lt+kgMeKzjebsD7qlCNGsT1ia76U+ItNis2KT7Ay", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81743}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.7", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.25_1636452118760_0.607745353946503", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "27e92d4781ac4cfd1732e3265ee8d997fe2cd34690a15a56530c354585b90be9"}, "4.17.26": {"name": "@types/express-serve-static-core", "version": "4.17.26", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.26", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/kacepe", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "ka<PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "5d9a8eeecb9d5f9d7fc1d85f541512a84638ae88", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.26.tgz", "fileCount": 6, "integrity": "sha512-zeu3tpouA043RHxW0gzRxwCHchMgftE8GArRsvYT0ByDMbn19olQHx5jLue0LxWY6iYtXb7rXmuVtSkhy9YZvQ==", "signatures": [{"sig": "MEUCIDhvI+Nlw2XHO/rKtIBovJnpumDuTd1rdIvjoMe6ri6KAiEArxfh/w0UNVGQQOtZ91+WslBRc75avJGjGXgHzNl9T94=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81763, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpXfuCRA9TVsSAnZWagAAYzoP/A0l3uCuUX5iXr7C5egR\nRaIcS+mP+pmwzhd/6cn5A0Or/Hs0nUQbv+2TwTpQYlyFqVul843jRCGIgUNI\n2RTDfQX4k2EmmBnReq6/58M7P/djpCjX4SBEGrunnwESFC8U90sm+Ohdktdt\nAGJMUn10O4EWNcvoGMl8NGaToPC/Qfvg2DE0Wm/2m6j2eS3jJt+dHClWhHkg\ntr0p6WVtOoR+BQeSLE3F1tgeJEtVZCvdKPdgrZ/ZwAN5e7mgZqfv0ed5Cwr5\nTJ1mZ7rJwM/ai/qbv49z81EOZ2mkNlVIqOs8XJMwZUDSOuLAwkOJ7LM81TTp\nRckC5GUkaK+2+CTslGeRu003wn5m4HmsSeT0ula5lrDVIqgbmF7l0rhGtkgT\nhk9ng871hlfQxxj/xxAZqQzCZfjMEbDNW68doYCP8SbyzBKtTyd5ZUSRILck\nGC1ywWLCwVE609PBqATnRND1IVnv+xgaWGafWEw6u2B37bl3PdplbMLrpQL4\n6o+lUlZoo3Dzu3uMX/hXtmJ/IkvI8683Hd/JI8lHH34MjGvr8lMFyqhiuQn5\nnQGaTT8QdpoILEFV2ceSRb/lxWEasDJIFREKzBoHoUOB/C2I9HwA3nZqBCSK\nUeU46B4Ea5Z3FG4p6N2vBeekrH2+JIpJzorS3HgBuX4QnQWOWZknvpHEfVV/\nLetr\r\n=NZa8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.8", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.26_1638234094603_0.05321975375673671", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "af1a1483cb4061f6cea5ccf7d028b1fa9edc8d6da8dab118844e29ce3beb9bd4"}, "4.17.27": {"name": "@types/express-serve-static-core", "version": "4.17.27", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.27", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/19majkel94", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "19majkel94"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "7a776191e47295d2a05962ecbb3a4ce97e38b401", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.27.tgz", "fileCount": 6, "integrity": "sha512-e/sVallzUTPdyOTiqi8O8pMdBBphscvI6E4JYaKlja4Lm+zh7UFSSdW5VMkRbhDtmrONqOUHOXRguPsDckzxNA==", "signatures": [{"sig": "MEUCIEg22X5gye1TtjVjuomdn6kwaN9bIK8WMxok6YZfVPJNAiEArvhhStMRTCxiDapv72R1IHybsJnrHZgbcw4aauJx/Go=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81516, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhxQtiCRA9TVsSAnZWagAAcT4P/i5aOf0jbXJuIVDbVBtf\nPFNvoa6Fdl7Wmp0zcb5+bPTkMm8eFGefB2GKhXjT0tYdP83mDmeEv5mxg5Tf\n3VO2XiLGHGISoraPYWL9mLvDNXgJdFtfe0HOpNAhwTaTdJD2Dwy0KnKmqyl0\nxi62SnHPdGA7OD3GNo4EhctZgk0Emo1J4HPK5M8dih8QSskdh/YTyU7r3uyb\nkBWsSFYjGY1Bg4U8e+G4E0ulgoncrOZLw+2G7RmMpJusfO4c6hYGhFsDWMOA\n/Id9HBbmAjDLENJM8iCMXut8Yd3DyINTseI1lW9nU/cqt9O4ttOeaapVDc9Q\nrQ5dWTJFPi5jozGm9TK8+N+Xg2cbZKa1MjBfyVjv1XYSK6bkezDE377SRZMh\nx8K38QJC3A/S5Bb/bb7AOqSvEx1vFXU2yq1kaurVnW1eTNbCzQR27XEoXVkt\nCumydl2lhm7ApL0vzrPnZpg0m91z0LTcrjDI61aue6j9hG1/89Q9qw+bVzD7\n5A48a3dXKDTKfp666ppBH78bMUooNY7GFdY5xkof7rqhDN3FB0V8TB54/YJr\n1Px/BeU42S4PeC8CSWHZpgDUTfNGK0aQbLw7YD+I7iHHR6Uobw2UI4lDGhgs\nJedGML3i3g/44Pu8dqoEjfW0ERabW7I17x/G9oFLFw6Z7c/4uhjnum5kR+a/\nrPCX\r\n=61xA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.8", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.27_1640303458428_0.5946786656709853", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "9dd0cf3cb51afc07203ed9ae63769824b5a7c2ecdcba7d08dbb702178ccf8907"}, "4.17.28": {"name": "@types/express-serve-static-core", "version": "4.17.28", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.28", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "c47def9f34ec81dc6328d0b1b5303d1ec98d86b8", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.28.tgz", "fileCount": 6, "integrity": "sha512-P1BJAEAW3E2DJUlkgq4tOL3RyMunoWXqbSCygWo5ZIWTjUgN1YnaXWW4VWl/oc8vs/XoYibEGBKP0uZyF4AHig==", "signatures": [{"sig": "MEYCIQD0ThGldv6u99B9DpW/OWuUjGHlycW/AO4H/1BPeloV3wIhALxOoJ6vXcNxx4sipafTLx7F2QORH6b18TLajt7x3dkr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3gWVCRA9TVsSAnZWagAA4WQP/2MC8CzlgBtUnEMoPfre\naK95VySMzPm7SnYWmnLXfbYH9/qA9o4oY7/qqfYuHS1npp6yw5ip87zQttmt\nPHmyUjPU7z3dLOj9wlBKIEyiyJbLKjESymxyuxwvbILNJzx4FM7DE51j9F35\nWu8y0gz1e0ymRMa4YhTEo15KqFJEK8df/wbT0OmkZnuHBmkWmxKC1f3p//GW\nX7HEoZj+d2IP+WwD0Gf7D7hwYazv1YEkLUOnvnnP7tLN/AlNWzS57vBhpmew\nTPRi3olpYThYWqFkU3nhsegCKtbfeOnVDyaO1FzzI5wjWmIL/Pl2XytwmQ0z\ngVFfutjlz2KHveQZWqxPdlfR/cuudY8468ElSpyooMX2SOy2uW5oxYL8mOGA\n3q6V4fxlG+Zd2DtSho/iR546xX6uBFywTJ6k+0gLDMn52WGsqagKDrbi2PEB\n6w5MisJKScDXPAnLSyuBkDu/ppTk8wZr+6pnetspxT4aXG4Cs/AyGK2JA+wk\neJ1GRWgKWLKvnbh8/TyhEcaTbOA33zZqoFIQOdKRV0gxdS8Bl/GriDKM4J2N\nEluXZlVuwQZ8CowrsrBvu35/1z1lPdciMDdfyVAun2DRJpwpe0U3Deu/uJRh\nOmP70bblelFGPdI4lyZyLxCmvPhpYZnB8Y4DwGKnVSRm7MCNT1GyKANVNDzB\nxLDF\r\n=RqNw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.8", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.28_1641940372870_0.1194192705694439", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e8b75bce16c2593d8d5f9521ddcf5db42875f8406701ed9eca5178f1b74e91b9"}, "4.17.29": {"name": "@types/express-serve-static-core", "version": "4.17.29", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.29", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "2a1795ea8e9e9c91b4a4bbe475034b20c1ec711c", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.29.tgz", "fileCount": 7, "integrity": "sha512-uMd++6dMKS32EOuw1Uli3e3BPgdLIXmezcfHv7N4c1s3gkhikBplORPpMq3fuWkxncZN1reb16d5n8yhQ80x7Q==", "signatures": [{"sig": "MEQCIGAiAu5ertshY4HBKRtOVeJkp7S13auJ3zo5el5qYhQvAiA2NbENCZI6uHBA2PAkMuOM4WJWHPb9IXLSRi0pO0qHTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81272, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqMX+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpifA//VEnWmVPay8CvckWFe5iafDWhldb57dRNm7yWQgiL1CcYvniE\r\njvpQs61+sJkPrYDeph64AZaF9dueZuLY4hfFKqjKfkUwyCdUchAobBhjxyvu\r\nTZGwKEFgsO8uSO3uOLAm5zC73QtH8emjFcE57uWzPj8lA6AFVJ8uYvo6n9hj\r\n/hUZBlyeh4rGKprU9hfvERxgNtVvtikF0lPvUTSGVAtmpaV2jXx3RQFTzyW5\r\nVh/eU0lw/+IfRo59bEVJcZEKoVn4r8gQ+qEfgPVw32w5V6/Ixyf38dScucH0\r\nK+TZElEpsYZtUWLm5jtYDqoFQblmTSl443TKzgNChJ3zi3Rf2dnMF66je4f6\r\nj/C7UkE+sHxv8/xyj6Zhr5J/RWmbrsaxMYBCZl0pXl5/sYhDs46wNfaUYRqS\r\nRSYBUbR5SUa2ArDovh6M8N4WRrBPZR2MlQFkmMmS4h+jhcK7WXb9Xm/rXJtY\r\niVUvPhceyG32oaH5Qr4wTw6cIzdOyyaCOdT7HXrDpF6Bu81Z82/Npu73MT8z\r\nFQ4Sn7WfZ4fMbVGRYoAVp7mxpHf7LjMKYfMOjEWH0eoj9bF4nNxae07O5Qpr\r\nC0UvFrwMPWffW6dq66De9RIQTg6FzvDP3gZ5qUdWZQdQpRH429outmOmHKNr\r\np95oFtDbp/Hr5EhugO01o/GCumhsl66RuPo=\r\n=E0Sy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "4.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.29_1655227902047_0.6109494642054745", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "9bccb33b6c7bda362e66b886d7b6830ee08d84b290259f722fc00ee1e1ba486e"}, "4.17.30": {"name": "@types/express-serve-static-core", "version": "4.17.30", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.30", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "0f2f99617fa8f9696170c46152ccf7500b34ac04", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.30.tgz", "fileCount": 7, "integrity": "sha512-gstzbTWro2/nFed1WXtf+TtrpwxH7Ggs4RLYTLbeVgIkUQOI3WG/JKjgeOU1zXDvezllupjrf8OPIdvTbIaVOQ==", "signatures": [{"sig": "MEUCIB8Aaf+nQWuhpvNUd4wVjnLTXRkerBooOCZnwcPe2+3BAiEA4QcSVjUW9QMwN2i6E/lRtnXI7o0a/zNsxOJ7G5x5BTY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81365, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi4HmIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmolXg//WqN2idwMcKGOPu9VXLXGJuYy1zxgPv3I7CbJ+4kj8djilnR1\r\njZmjdeivWFL4SE4Po/ngs4oakKtoP7QVNEVzs3FMBkLfI17g3BBoh7/f/mOA\r\nzT4wc7ZbNTkP1/Z/jUBREpDJINlASH//zX5iubvlgvQNh9jojZkpAPYFa+Mt\r\nbbVCrCO/xb2kGR6vumwH/Nx24yl8Qn7R0+Jz4BFlgL6PomGfw27MqSff0t6Y\r\n6L6XsFRITEV7G8Pzxb1hlsyC5W7mG5P+zGoO2R2maAyBU9/0Mwo7ox8MDeNB\r\nT1AOSy2BibPU/0BUTb0RrBZyU9ZJZ0zdDZxrRmNyI/wNYYgVE5moIV3r5Vjl\r\nclwXd+gcFlOkNWMeJDKpfaJNZPFow6RYPEaUuZ6d5dwrO4zq9pOJFEE+svCF\r\nnXMIEweVcILI/E1ZU8KZkjOmkCY0pNw7wvqyDxrpimeZFXpgqBwoGdmAJAe0\r\nW4hoiwEQOGKpuSmOuS8pCYI0x5Wuye9aEgMCfFxjpmK7ujIMrR4o5WcJxTi3\r\nmZhbprQtN78Yhx8rLfrCrHm2b3UbAdMNttVdNloFi4+W9Vexpom+4AsjEMJ6\r\nOJzuwAl6azDh8iKujgc9S++G8yzjuS83d1Qpt5WD2Q9yCBqeNNsgFChig0n8\r\nOqUEVnfltTPJvuw3/QcBuQgdXqYtapPq/N0=\r\n=uS8+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "4.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.30_1658878344209_0.5489270206698502", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "665a90ee320582584fb9fdabf29a723892097c34c8eca8053e96b7368751cbc4"}, "4.17.31": {"name": "@types/express-serve-static-core", "version": "4.17.31", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.31", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "a1139efeab4e7323834bb0226e62ac019f474b2f", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.31.tgz", "fileCount": 5, "integrity": "sha512-DxMhY+NAsTwMMFHBTtJFNp5qiHKJ7TeqOo23zVEM9alT1Ml27Q3xcTH0xwxn7Q0BbMcVEJOs/7aQtUWupUQN3Q==", "signatures": [{"sig": "MEUCIQDWy4mHg71Eik18W2i7GhDxHJmx6glUsz8F/tv01iLSLAIgTtVAAXjJ9cKjpv5us8Y6dQnddry9cmuUZo2tjI4QTdM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIMtbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoq4xAAnfVG7LW9KEIt9HZFUDcQohxtUUS78/czvoecCznY1jwfvLHA\r\nV5Gw+IzDyQEbR6g1KBnT5AFhJEaO+iQ54ylbjDdAC3Lb5S7xReyMIia/Egrf\r\nMAg6UQRFqSbEl7N3P4M+1THpSv57C42xKWEqJZzD0ajI+BE8qh2HCKYbyhLz\r\n3NvdryDSCy87bU1sM2LAb3Wgc9SHBof3+KDfCHWXVtQgiZd0tZ9Yfk1qR3eq\r\nYcfQ028MekibvGNYWtiZuGgLr/zmW8EGa93vEsGS5EKdOLOFuS4SScCMy7HO\r\nPsbLk4DBlNhROOT9Ro6vHxVou/wV9FW4frYY2ELHBxT6bwgeSRclKpXwmzFW\r\nxWDTncVlaPqL/q0JcI0Lo/KBnZdaHGkUuYlCeDC/Q3Mo9hGmD2T7PG+pfFUq\r\nWUQprCoWfO6Lxs2gqteo1LKKiwQ0dOJsLZeR2w8DSOGFoLV2i3AuxrNK2Tv3\r\nH3qB3XYZIhkZvYZA9Ggna7dK9kNGRt5SMbZObo1UA+7i4DOWy/3KUOFTyUM3\r\nhS5f3GmkmCpCvZnRmr+Hp94WurUQ+osMIrbmT3pRQm4uJYXpIULde5a/w9rK\r\nGZHmQn0hejJgJB7z9FnJee6GY+YTm6nhpqefD/dSfmtH0qy+n/zyqn59pwL8\r\ntjaZBSbJCFF5fnECfnvZSZuHThhRBVB679o=\r\n=5wx2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.1", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.31_1663093594810_0.43519659785349796", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "9f714068b422a8e3ba9eac20ddf48fbcaa24a940fcd22920c164887ab8fe2b64"}, "4.17.32": {"name": "@types/express-serve-static-core", "version": "4.17.32", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.32", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "93dda387f5516af616d8d3f05f2c4c79d81e1b82", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.32.tgz", "fileCount": 5, "integrity": "sha512-aI5h/VOkxOF2Z1saPy0Zsxs5avets/iaiAJYznQFm5By/pamU31xWKL//epiF4OfUA2qTOc9PV6tCUjhO8wlZA==", "signatures": [{"sig": "MEQCIGuNZ81ceJDVFNXTshXVpYbAiOh9Av9QThOXIW/TjzkuAiBfuzXgSCk61zANgFNkZfqgLVK7THzb+yU2XZQX6twcxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjr3CdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmra1Q//YxEYqILg6avF4lf0p3Pb9cRJspyj04nd7Mnd4N3kXKQs8uGf\r\nW0/XCktmyH1AD9AGf779JXOaNxuvSDic+m18mCB9XGyI+TrG5lEND4VL4hdZ\r\nzlXULO0a/81/B3nqc/R9Al3hXlG3Sho32FBcjS6h2KncZUuGVSGLcL+AnWDC\r\n5fY8wdbIgRlLplHlXAQizPcqwQ66ga44abnot9vDO/yWw8eQPApLd4Q/qXGc\r\niDZbBdu7UovmzLFcRByTQmYehILS/yUejleYty8uw9o+hclScSmLCf1o8Ssz\r\nBOKCvZGJHMZ+12ZrNSBDhR7rh2zYX6TG4qpW/u4A8he1XHiH8FHE/zgBLRC3\r\nT4UN4Zdg1C572EALvGuZH6re1wdkfNDDsfHW/OTpFeoRBaF3Y8UNosOavUo9\r\nfZvGgIqMg0TCsAI3vltdpB/X89vGOPT0cPJcm43vI1PWLjSzblz6rGGzST31\r\nkQPXdFHhZL4RHti3kmSaDONIjfoo9srOlpY+Sa5c+gsYitAZ8rMaScCYRMrP\r\n84dfMxvNPYKonQlXfGFjRNlIB+spPEG/CU2QyROkS3cu4x9pphX2tnYapS4g\r\nt+RQwLwqLzhGzMWU3QyenfK8uX1fso/UfWCjBtxCPrVYYKupEhdwb4eYgWqj\r\n1qzPNWhTlNSfBql8fkkJ7n1x+rQzCJOtzRw=\r\n=6aQo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.32_1672442013623_0.2951176035438601", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e778331bb638d0ec18ce0e0b88173b62d735975962ce8e4d7a8568feb9f468b4"}, "4.17.33": {"name": "@types/express-serve-static-core", "version": "4.17.33", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.33", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "de35d30a9d637dc1450ad18dd583d75d5733d543", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.33.tgz", "fileCount": 5, "integrity": "sha512-TPBqmR/HRYI3eC2E5hmiivIzv+bidAfXofM+sbonAGvyDhySGw9/PQZFt2BLOrjUUR++4eJVpx6KnLQK1Fk9tA==", "signatures": [{"sig": "MEUCIQDTbYr0a4NyoEQVmQIMFpmQ1B0va+n1mkTDUcz1vonq6wIgX8V0GfuOLtrAbL5k5ovebFFy/9Wefjv06xcNzRiUsQY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44881, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzv0FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrb1xAAgFrqv/DvnXWUnGjgb1tN3zytj9gqOsBlIjQq7W0ZMOy/ZK0X\r\nPUoxZpydnUBsdKryLpd8BnoH5w/NSKBeqqh2mXtVGe/Z1NI5TpMiPNR4uQmR\r\ng71KH4PXBonFu/RLAs/ceWAmNCO9UsQxtExNvSbjOttyS5mywsGBZI5/rTl6\r\nSDqD3JwiMb1BF8UgP9niB40QpB3mTO6vRwrHMjOcw17Otzxs35J562+Yc/pp\r\nhEIDMddWs4+g/jSOXn+vZWRJBdNm56vWill/KjSs+gqwPG7mIwDMGyeBFq/Q\r\npJcUS8qJHKddGg9e4oInq1+8LFZ3rTjAxkM7F5e2MB1I4cm18PKRdaV1gQ4K\r\n5lSBF7bq5QGt1PP/fTIyzl5BShmemaHJMYXCZSoOz9X+Pn9H0pqGRiOdOEBV\r\nXF7ZAnLJGrEcxoM1EwKnfr51GCvqTdCyynPSUEhwy9MZUdJDTGWQWUuU0T/g\r\nxYhC+SoLjgQkjHWd+spfclZSKyPtP3w3THeXums4sPgWy5+WO/g8Aj1sVQWx\r\ntos8+3dFqnD5BDoDj0mm0S7lKswQztL++ZCLtI7o46g/6j1AM1xHAwUaNw7l\r\n4da4NmY7I+fP4yfnt/NvVNK0dlryaiKCyZzfFpznfleOUe6U/6MJXdRXMQ0t\r\no9N7B4D3KfRwQxtjS55iW3nGumSi1Tu238w=\r\n=5tB6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.33_1674509573455_0.09071100333238813", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0a827d8ef2f7f9ef4c3093ee5f8b4a148b276b25d09a337a636065cf58d32616"}, "4.17.34": {"name": "@types/express-serve-static-core", "version": "4.17.34", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.34", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "c119e85b75215178bc127de588e93100698ab4cc", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.34.tgz", "fileCount": 5, "integrity": "sha512-fvr49XlCGoUj2Pp730AItckfjat4WNb0lb3kfrLWffd+RLeoGAMsq7UOy04PAPtoL01uKwcp6u8nhzpgpDYr3w==", "signatures": [{"sig": "MEQCIAeZ5F2GnQZCfFKyN/Qz0uoNZYRYVTPJFJwFLWpKthd9AiAVmXhw5YppalYEu3Fp67olz4I1cIR6+wS4kwZD2GCZBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45452, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkSRLmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+7BAAktLXqCa4Np1HTg/WiERxEa1rgIlNKoACdpzlJhIHFZbSm4nu\r\neNH5je4rYgzTZAcfBqNXQtXrBVtYbdk3MScrtM/tubx6HVW+aNOi+WJBNF35\r\nzc1coelzhhMLBWuebj9hk56H5B3MICWA4Ur4D4KqEkEFqw+1AdNtbwUWImzl\r\nwMcyM0GFD1/SjwEzlbZLrbtjeCjhNNTUpoA7XExbdDrb9YXk+Qrl8evBN84Y\r\nUTmYMVDSdQROocsMwNJ7L7Na5xUMLb/0rvbAX3munZCRUmXWOjIX6cf8tF+b\r\n4Iz/xyuakGF7oeief9nLhB9jt2idJj4SwukWeqdg1SAdd0i6t1Kaa6OLs4eN\r\niqyAFTBGMWNzZNxiD7JnOg8gYriyDU4JYTx1NnmpIQkS9zfgoxXmz39djgQ3\r\n+IgldfAmVjEC7psSNXpJ15vqnXH9cXrouEscYAQPDFz+Bkqf8jC7eTPodNKC\r\nSJapRjjX2qXnqVtDfR7kv1ACQMUN0TfDIaT5Y0yzo5T/jime315u69rW34w+\r\nlsaLVNh+KEfU6LLel9FjbuQItSBNdHB6uiSycrX3TrhUIE4bbxpkf8E3wzY2\r\nUxnvfEBtRUves/I7MlcUbPPi3jfBAcLMS6K5uDxWeYVnx57Rnq9fVLelRQgB\r\nP/PVCKkbuxR/NRIxkBnd7Z9Xxkt49UoP2t0=\r\n=j0vc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.34_1682510566624_0.9609630049705449", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "6792485dd3029af4dba08266548c43224ed5f3fe85bf657031f049fc464ed451"}, "4.17.35": {"name": "@types/express-serve-static-core", "version": "4.17.35", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.35", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "c95dd4424f0d32e525d23812aa8ab8e4d3906c4f", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.35.tgz", "fileCount": 5, "integrity": "sha512-wALWQwrgiB2AWTT91CB62b6Yt0sNHpznUXeZEcnPU3DRdlDIz74x8Qg1UUYKSVFi+va5vKOLYRBI1bRKiLLKIg==", "signatures": [{"sig": "MEQCICUw/o7XgfrSGRP2UakM4ToCHQSM5no0O/0dQUMcZS0iAiBIAJPdD3W6PiO0a4MinezG8Mh4VWDoTlLF38NcYksALA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45518}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.35_1683948772959_0.38513096788666434", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ff8dd9048ee34bf16a6700ed4e90e394e1b8262392fc0c2dfa2e9ea6246bde19"}, "4.17.36": {"name": "@types/express-serve-static-core", "version": "4.17.36", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.36", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "baa9022119bdc05a4adfe740ffc97b5f9360e545", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.36.tgz", "fileCount": 5, "integrity": "sha512-zbivROJ0ZqLAtMzgzIUC4oNqDG9iF0lSsAqpOD9kbs5xcIM3dTiyuHvBc7R8MtWBp3AAWGaovJa+wzWPjLYW7Q==", "signatures": [{"sig": "MEUCIQC5Hc6Qvr/DiawmfE2QPUCNKJ7Ca7KnDsC0JZB0ZLeQBgIgAbKM2cUsb/9f8khknAsov1ceLWHZKNm9amfdLRQHXuU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45688}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.36_1692728042709_0.8462003319715758", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3498224809b5b549d64382dd3a046fced0f58273994525024be43043bfce7586"}, "4.17.37": {"name": "@types/express-serve-static-core", "version": "4.17.37", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.37", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "7e4b7b59da9142138a2aaa7621f5abedce8c7320", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.37.tgz", "fileCount": 5, "integrity": "sha512-ZohaCYTgGFcOP7u6aJOhY9uIZQgZ2vxC2yWoArY+FeDXlqeH66ZVBjgvg+RLVAS/DWNq4Ap9ZXu1+SUQiiWYMg==", "signatures": [{"sig": "MEUCIQD9sE2l6PYKMV/gJERJv1Pzgcw4nari40gVme6YazhEmwIgEq4MAoDijFlYRahAIdE3pIdnsQkCt2HJdaoXpMZalG4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45602}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.37_1695490250613_0.5389730759276297", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "4cf131d0413990220a982c55eaefd4457977c30e42f92c975a87630465f6b13c"}, "4.17.38": {"name": "@types/express-serve-static-core", "version": "4.17.38", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.38", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "d9c1d3a134a1226d84ec8e40c182f960f969d5a4", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.38.tgz", "fileCount": 5, "integrity": "sha512-hXOtc0tuDHZPFwwhuBJXPbjemWtXnJjbvuuyNH2Y5Z6in+iXc63c4eXYDc7GGGqHy+iwYqAJMdaItqdnbcBKmg==", "signatures": [{"sig": "MEQCIByvD3La+o8kx1/S372XH3dYSaGN43V3nsJFf7odOR24AiBMIQDWaHQjhtLuCDpLJg3VYm20hU/GQZwYGIsvyosH/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45061}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for express-serve-static-core", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.38_1697595267178_0.9726176829323441", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "27bb166edea0e7936e25cef032d1f4bfdc10876a28c83301addb8813e77c9a2c"}, "4.17.39": {"name": "@types/express-serve-static-core", "version": "4.17.39", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.39", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "2107afc0a4b035e6cb00accac3bdf2d76ae408c8", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.39.tgz", "fileCount": 5, "integrity": "sha512-BiEUfAiGCOllomsRAZOiMFP7LAnrifHpt56pc4Z7l9K6ACyN06Ns1JLMBxwkfLOjJRlSf06NwWsT7yzfpaVpyQ==", "signatures": [{"sig": "MEQCIHffqkcsqBFmow4V5tlQtn+y8sTEnQGvdewY2njWObZvAiBZ98KxqPWnp/kyzlibuqloXS7fk/eMawjIsJKk9zV67g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45193}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for express-serve-static-core", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.39_1697735855425_0.4976584796022012", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d5c7ccf133c44633f4c38bf4ae01e30755af3e96994de20698658b1715be77bd"}, "4.17.40": {"name": "@types/express-serve-static-core", "version": "4.17.40", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.40", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "3cac0230ca1225d4490e1a0e9266093aa4ed6a0d", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.40.tgz", "fileCount": 5, "integrity": "sha512-dzQWNQktgK3AyMpPeIeWbnR/ve2wU0bDSfdhf+RSt1ivelrO3hwfrKjTZvJDK4IyGWlDoRj+knNSePnL7OUqOA==", "signatures": [{"sig": "MEUCIAs+K+7hySrefhFUBwJfhEtnuVl6XTumI3dXYKbOjmUyAiEApisDJwtN2mbGsAOf7p6Ir23ZvJ7pamww46KqkKO4FOw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44854}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for express-serve-static-core", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.40_1699299720505_0.48996939268086526", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7b5122b93bc76f6174d4842d78858418a4469672f09701d37c18b01ed189cc0d"}, "4.17.41": {"name": "@types/express-serve-static-core", "version": "4.17.41", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.41", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "5077defa630c2e8d28aa9ffc2c01c157c305bef6", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.41.tgz", "fileCount": 5, "integrity": "sha512-OaJ7XLaelTgrvlZD8/aa0vvvxZdUmlCn6MtWeB7TkiKW70BQLc9XEPpDLPdbo52ZhXUCrznlWdCHWxJWtdyajA==", "signatures": [{"sig": "MEQCIH3RjQPngGNjrPiCNeSUspjq2AMJc4/Z5WXJWbrvvjBVAiAqxIBhx5yGi1wO3Tfd7p/Vffdn/+yGS6k4/Cs7KjFH3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44854}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for express-serve-static-core", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.41_1699326964556_0.7066039601649492", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "5c546c267bbf60fff3ec26d28c47a1c607526f56c28ea2a149efcb2f1a8e0198"}, "4.17.42": {"name": "@types/express-serve-static-core", "version": "4.17.42", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.42", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "2a276952acc73d1b8dc63fd4210647abbc553a71", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.42.tgz", "fileCount": 5, "integrity": "sha512-ckM3jm2bf/MfB3+spLPWYPUH573plBFwpOhqQ2WottxYV85j1HQFlxmnTq57X1yHY9awZPig06hL/cLMgNWHIQ==", "signatures": [{"sig": "MEYCIQC8YhJ4qD2ZuIOoskeo0qiTWx64ozRUNVxV6uefuwpWuwIhAP5VGvf5hNWfoVyQBFugZeThgzhs6c/R3d2N4gtLuWUf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45949}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for express-serve-static-core", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.6", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.42_1706224040249_0.16002582889598438", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "70f4b79096fe72ad9b6605a554090daac1497bc59526c8903d01258a646cbdf1"}, "4.17.43": {"name": "@types/express-serve-static-core", "version": "4.17.43", "license": "MIT", "_id": "@types/express-serve-static-core@4.17.43", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "10d8444be560cb789c4735aea5eac6e5af45df54", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.43.tgz", "fileCount": 5, "integrity": "sha512-oaYtiBirUOPQGSWNGPWnzyAFJ0BP3cwvN4oWZQY+zUBwpVIGsKUkpBpSztp74drYcjavs7SKFZ4DX1V2QeN8rg==", "signatures": [{"sig": "MEUCIQD9szP+RdPgSIeCGrCSYKf+VDv3F2V+uqHsPKhvxDa7QQIgcjTWqSkQ41yL/M7/9z36bEKpImzoPcHtR6o2b839T38=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45945}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for express-serve-static-core", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.6", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.17.43_1706965639977_0.43612288659650256", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "78d46eee40ea01a0a5a181e980560de1263e59fb351c3ff23dc4b43718d9e6f6"}, "4.19.0": {"name": "@types/express-serve-static-core", "version": "4.19.0", "license": "MIT", "_id": "@types/express-serve-static-core@4.19.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "3ae8ab3767d98d0b682cda063c3339e1e86ccfaa", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.19.0.tgz", "fileCount": 5, "integrity": "sha512-bGyep3JqPCRry1wq+O5n7oiBgGWmeIJXPjXXCo8EK0u8duZGSYar7cGqd3ML2JUsLGeB7fmc06KYo9fLGWqPvQ==", "signatures": [{"sig": "MEUCIBqSz3OcsjD6q73csaOrmc+e9HUI5fXd0Q5cwxgtXVcuAiEAkI75aeC4boZ4bYVWu0pmk66Dk0laFaO+B/dpDDNhNNk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46037}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for express-serve-static-core", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.7", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.19.0_1712270145279_0.06685996931618532", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a69ff76a4af51a72b9c21aef5507a22ae657992a35a887436806eeedff4c5348"}, "4.19.1": {"name": "@types/express-serve-static-core", "version": "4.19.1", "license": "MIT", "_id": "@types/express-serve-static-core@4.19.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "57d34698bb580720fd6e3c360d4b2fdef579b979", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.19.1.tgz", "fileCount": 5, "integrity": "sha512-ej0phymbFLoCB26dbbq5PGScsf2JAJ4IJHjG10LalgUV36XKTmA4GdA+PVllKvRk0sEKt64X8975qFnkSi0hqA==", "signatures": [{"sig": "MEYCIQD+dAsM79HOViDchKEuUDxbfDxPBnp0P1HEmUumhnTHwQIhAMPGylfsfiq/Y1eohNbBEMOle4Rceqc6FXFIeafMJxls", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46136}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for express-serve-static-core", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.7", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.19.1_1716322090018_0.7036248266710032", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a2675271698531b5c0a794fd29a9ae4e62d231058d59c57312f67b44c1fe73c4"}, "4.19.2": {"name": "@types/express-serve-static-core", "version": "4.19.2", "license": "MIT", "_id": "@types/express-serve-static-core@4.19.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/samijaber", "name": "<PERSON>", "githubUsername": "sami<PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "ca09a37ffbdc66c584c305af0044b8ad3aa7b9ef", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.19.2.tgz", "fileCount": 5, "integrity": "sha512-dPSEQElyVJ97BuGduAqQjpBocZWAs0GR94z+ptL7JXQJeJdHw2WBG3EWdFrK36b8Q6j8P4cXOMhgUoi0IIfIsg==", "signatures": [{"sig": "MEUCIE32NGz+7EKZBVBcyflV8gNs06XjYMCUYOp+5RBfQAogAiEAjvAOgO8MBtroxNjVmFVyuF4BehjdqZqlhbxck8rmx8s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46410}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for express-serve-static-core", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.7", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.19.2_1717006093350_0.40660363550527934", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "bd6855861500fccef8c8300a403417400ef1c4a13546297547845b2521e6ac49"}, "4.19.3": {"name": "@types/express-serve-static-core", "version": "4.19.3", "license": "MIT", "_id": "@types/express-serve-static-core@4.19.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "e469a13e4186c9e1c0418fb17be8bc8ff1b19a7a", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.19.3.tgz", "fileCount": 5, "integrity": "sha512-KOzM7MhcBFlmnlr/fzISFF5vGWVSvN6fTd4T+ExOt08bA/dA5kpSzY52nMsI1KDFmUREpJelPYyuslLRSjjgCg==", "signatures": [{"sig": "MEQCIE1c9abvXF1ef2uFoRI9VkfjsXUM55TWCbsuyL0WvN7vAiAYR1XzqNcWFMkiUDsWDL03eR+CA3zFGbJFo153L0hJvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46218}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for express-serve-static-core", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.7", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.19.3_1717092527238_0.31199717602978017", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7851d080d9b3c122ba245a1cfebd5ae4939c12e88f52b66385cdfd02a0a916da"}, "4.19.4": {"name": "@types/express-serve-static-core", "version": "4.19.4", "license": "MIT", "_id": "@types/express-serve-static-core@4.19.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "36283c0447e182d1a362aa5cb0ab4d4dd6e00655", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.19.4.tgz", "fileCount": 5, "integrity": "sha512-ZQSvOi1hrdICnPt6e2eiFGx4iPIqySC5pEWkWfofkyEi7A+ujNy+4W2lzM8n2B/8LBm4d54oZyIOUdCFpMCCAA==", "signatures": [{"sig": "MEUCIDDWGfRJF2bR0ab7sE+dtTqkiSMeB08sUNrAqueN3c8bAiEAg93/6+wba64YoZiAQNoWEH/YmtY8W4UteKfF3tLlMs8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46250}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for express-serve-static-core", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.7", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.19.4_1718818546984_0.5139919621540534", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f8014ba557d11addc8e65e05178b3f8cea4e0176b71e1c77658ab25d7e136b98"}, "4.19.5": {"name": "@types/express-serve-static-core", "version": "4.19.5", "license": "MIT", "_id": "@types/express-serve-static-core@4.19.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "218064e321126fcf9048d1ca25dd2465da55d9c6", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.19.5.tgz", "fileCount": 5, "integrity": "sha512-y6W03tvrACO72aijJ5uF02FRq5cgDR9lUxddQ8vyF+GvmjJQqbzDcJngEjURc+ZsG31VI3hODNZJ2URj86pzmg==", "signatures": [{"sig": "MEQCIGBHIylhPzVteN2MBYGMIBoD+DDYQjuQSiolYhalx6DeAiB/1Oc91orauqgdzt6YvcgdeOko4wC13W2Zj0aoV8im1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46218}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for express-serve-static-core", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.7", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.19.5_1718824025941_0.43726788166898967", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7851d080d9b3c122ba245a1cfebd5ae4939c12e88f52b66385cdfd02a0a916da"}, "5.0.0": {"name": "@types/express-serve-static-core", "version": "5.0.0", "license": "MIT", "_id": "@types/express-serve-static-core@5.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "91f06cda1049e8f17eeab364798ed79c97488a1c", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-AbXMTZGt40T+KON9/Fdxx0B2WK5hsgxcfXJLr5bFpZ7b4JCex2WyQPTEKdXqfHiY5nKKBScZ7yCoO6Pvgxfvnw==", "signatures": [{"sig": "MEUCIQDosSQyvOWnSn71ZE4OZROkSnISL4iJgYHp3zsN5LzIwQIgEdsZb9wM3ODAQA8O8W6DHED8mKxNzrT0dbeA15q1wyE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46249}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for express-serve-static-core", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.8", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_5.0.0_1727292038964_0.6542132606808158", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ee9429aeec4a4336c65597cc84eaab4896dfe17ef5441d8f14f05fa8669a84f6"}, "4.19.6": {"name": "@types/express-serve-static-core", "version": "4.19.6", "license": "MIT", "_id": "@types/express-serve-static-core@4.19.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "e01324c2a024ff367d92c66f48553ced0ab50267", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.19.6.tgz", "fileCount": 5, "integrity": "sha512-N4LZ2xG7DatVqhCZzOGb1Yi5lMbXSZcmdLDe9EzSndPV2HpWYWzRbaerl2n27irrm94EPpprqa8KpskPT085+A==", "signatures": [{"sig": "MEYCIQC+XngaquzuzuZuB47iuRFcGIv3HBYug0zbw3HFXDfltgIhAOILC282VYC74wPh5KJJQmnlAFb5p+7h5zJoz6S22tRW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46221}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for express-serve-static-core", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.8", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_4.19.6_1727292049635_0.7938950340471789", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a6eae9098d851d3877b61f9dc806634a6174740520432b72c16dc4fdebca21a7"}, "5.0.1": {"name": "@types/express-serve-static-core", "version": "5.0.1", "license": "MIT", "_id": "@types/express-serve-static-core@5.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "3c9997ae9d00bc236e45c6374e84f2596458d9db", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-5.0.1.tgz", "fileCount": 5, "integrity": "sha512-CRICJIl0N5cXDONAdlTv5ShATZ4HEwk6kDDIW2/w9qOWKg+NU/5F8wYRWCrONad0/UKkloNSmmyN/wX4rtpbVA==", "signatures": [{"sig": "MEUCIHNJvsZHnp987tVQXEivd9LEks739IKHE2ndjtNm9aTCAiEAyMPMMciZTob54m5KA/jh+29ThkNt7uY7gEvmgOL9uZg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46317}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for express-serve-static-core", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "4.8", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_5.0.1_1729778577193_0.6265722163302718", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e478b74f9c05846ab15f9b3e91c6154b0b5ea7cca0c789eb7af911e20f105ad9"}, "5.0.2": {"name": "@types/express-serve-static-core", "version": "5.0.2", "license": "MIT", "_id": "@types/express-serve-static-core@5.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "812d2871e5eea17fb0bd5214dda7a7b748c0e12a", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-5.0.2.tgz", "fileCount": 5, "integrity": "sha512-vluaspfvWEtE4vcSDlKRNer52DvOGrB2xv6diXy6UKyKW0lqZiWHGNApSyxOv+8DE5Z27IzVvE7hNkxg7EXIcg==", "signatures": [{"sig": "MEUCICF4hFLObcFbzzuC2LxXy1flAIFcSYqS25Mxe+kgwPMlAiEA8B+6o+ihFYqvYutOcYFv0LgZ0ua0rBKljSA0OyTKPPo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44399}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for express-serve-static-core", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "4.9", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_5.0.2_1732478566988_0.057546852698404205", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "821db632897304bf827a1f78c5ce9984a7eaa4b0683addac5c74d74c49ee5c73"}, "5.0.3": {"name": "@types/express-serve-static-core", "version": "5.0.3", "license": "MIT", "_id": "@types/express-serve-static-core@5.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "04174d3f0836863467b7fbcbbbcd69441d205715", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-5.0.3.tgz", "fileCount": 5, "integrity": "sha512-JEhMNwUJt7bw728CydvYzntD0XJeTmDnvwLlbfbAhE7Tbslm/ax6bdIiUwTgeVlZTsJQPwZwKpAkyDtIjsvx3g==", "signatures": [{"sig": "MEUCIQD5kyspMKmaMBY4iV70mKksyMhfORG1vBjpLJqZ2RJHkAIgLLFWulIln6RqbSMx6lIbV1oMPL2Fai139+POxi+0ftI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44399}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for express-serve-static-core", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_5.0.3_1735692432118_0.4558528630674392", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "62d0c8692a5bebbe1ca7d34c8f1bdac9596689848550097fd322c587bbc36ab4"}, "5.0.4": {"name": "@types/express-serve-static-core", "version": "5.0.4", "license": "MIT", "_id": "@types/express-serve-static-core@5.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "88c29e3052cec3536d64b6ce5015a30dfcbefca7", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-5.0.4.tgz", "fileCount": 5, "integrity": "sha512-5kz9ScmzBdzTgB/3susoCgfqNDzBjvLL4taparufgSvlwjdLy6UyUy9T/tCpYd2GIdIilCatC4iSQS0QSYHt0w==", "signatures": [{"sig": "MEYCIQCBFtzZsLkJaSGekD5fISBHu/BP7CRGcnWEuugEtT/MJgIhAL52rGfgV2B5TtIyiYsp3PnebtjF0Hn9VfHPSgWQzQhA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44726}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for express-serve-static-core", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_5.0.4_1736287344444_0.6768308730077501", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "438bf09e58d4b7d2b7e20d3141b43d68eae211a53da6035fde3146c5d5fd7ed3"}, "5.0.5": {"name": "@types/express-serve-static-core", "version": "5.0.5", "license": "MIT", "_id": "@types/express-serve-static-core@5.0.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "f6a851c7fd512e5da087f6f20d29f44b162a6a95", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-5.0.5.tgz", "fileCount": 5, "integrity": "sha512-GLZPrd9ckqEBFMcVM/qRFAP0Hg3qiVEojgEFsx/N/zKXsBzbGF6z5FBDpZ0+Xhp1xr+qRZYjfGr1cWHB9oFHSA==", "signatures": [{"sig": "MEUCIDHgUZAhSjn1gMXjFnPkDvAnm9GRI4U/c3rwGdDOO1gaAiEAgHB2IBpVv57T3D+L/n+AAeD0Ki4cUtLEeEn4iWv3rWM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44804}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for express-serve-static-core", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_5.0.5_1736877782377_0.07554288140304721", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "e061bf71b1c59858f509d77a4a9fce9cac8356ceeee00981ad6ef163d8eba2f1"}, "5.0.6": {"name": "@types/express-serve-static-core", "version": "5.0.6", "license": "MIT", "_id": "@types/express-serve-static-core@5.0.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "41fec4ea20e9c7b22f024ab88a95c6bb288f51b8", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-5.0.6.tgz", "fileCount": 5, "integrity": "sha512-3xhRnjJPkULekpSzgtoNYYcTWgEZkp4myc+Saevii5JPnHNvHMRlBSHDbs7Bh1iPPoVTERHEZXyhyLbMEsExsA==", "signatures": [{"sig": "MEQCIF4FbvP6LRKOAnToTS0g/PSwQGeGlWrtovOneVem2CibAiBMEFFXKcm/lVF4JAEifb1wfvdG4s6pbUofRQq2lrWpXg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44804}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for express-serve-static-core", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.0", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_5.0.6_1738040549535_0.24858518763978088", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "44d5cdf58e5ee1073bddfbf6110c8e09cc6e0712ad27c9ed54d367643bee193b"}, "5.0.7": {"name": "@types/express-serve-static-core", "version": "5.0.7", "license": "MIT", "_id": "@types/express-serve-static-core@5.0.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "dist": {"shasum": "2fa94879c9d46b11a5df4c74ac75befd6b283de6", "tarball": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-5.0.7.tgz", "fileCount": 5, "integrity": "sha512-R+33OsgWw7rOhD1emjU7dzCDHucJrgJXMA5PYCzJxVil0dsyx5iBEPHqpPfiKNJQb7lZ1vxwoLR4Z87bBUpeGQ==", "signatures": [{"sig": "MEQCIF7g8ZA8NQ3nKziRGxiRV+dts0qHi+QEnU3bVBtIJ0d/AiB3ajT6yTtXQO7FKT4TL/Spc44e6QCA52q6uCgHi3oAsQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44778}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "actor": {"name": "types", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for express-serve-static-core", "directories": {}, "dependencies": {"@types/qs": "*", "@types/node": "*", "@types/send": "*", "@types/range-parser": "*"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/express-serve-static-core_5.0.7_1751997751158_0.14968708671573894", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "3a3c2a16800d428a2e5e9bc836ca4baa3fa4e62740b6db3276496d4816a406bb"}}, "time": {"created": "2016-05-17T04:53:56.002Z", "modified": "2025-07-08T18:02:37.350Z", "4.0.15-alpha": "2016-05-17T04:53:56.002Z", "4.0.16-alpha": "2016-05-19T20:50:14.121Z", "4.0.21-alpha": "2016-05-20T19:33:54.370Z", "4.0.22-alpha": "2016-05-25T04:52:19.128Z", "4.0.23-alpha": "2016-07-01T19:21:01.823Z", "4.0.24-alpha": "2016-07-01T22:41:14.244Z", "4.0.25-alpha": "2016-07-02T02:24:02.266Z", "4.0.26-alpha": "2016-07-03T23:22:35.655Z", "4.0.27-alpha": "2016-07-08T20:02:41.087Z", "4.0.28-alpha": "2016-07-11T22:28:37.963Z", "4.0.29": "2016-07-14T14:32:44.193Z", "4.0.30": "2016-08-02T15:52:11.833Z", "4.0.31": "2016-08-11T21:44:00.978Z", "4.0.32": "2016-08-22T17:57:58.948Z", "4.0.33": "2016-08-25T18:41:39.689Z", "4.0.34": "2016-09-19T17:33:06.674Z", "4.0.35": "2016-09-21T20:27:09.760Z", "4.0.36": "2016-09-23T17:59:33.612Z", "4.0.37": "2016-10-05T20:56:50.790Z", "4.0.38": "2016-10-26T19:25:40.030Z", "4.0.39": "2016-11-03T17:43:09.817Z", "4.0.40": "2016-12-28T00:54:09.210Z", "4.0.41": "2017-03-08T18:23:33.524Z", "4.0.42": "2017-03-22T00:13:08.440Z", "4.0.43": "2017-03-24T15:50:52.181Z", "4.0.44": "2017-03-24T16:30:28.808Z", "4.0.45": "2017-05-19T20:47:30.319Z", "4.0.46": "2017-06-12T22:15:44.457Z", "4.0.47": "2017-06-15T20:13:38.638Z", "4.0.48": "2017-06-16T16:38:49.920Z", "4.0.49": "2017-07-06T14:05:22.269Z", "4.0.50": "2017-08-21T21:51:48.176Z", "4.0.51": "2017-09-07T22:09:57.848Z", "4.0.52": "2017-09-08T21:06:58.306Z", "4.0.53": "2017-09-18T14:09:06.558Z", "4.0.54": "2017-10-25T00:22:13.557Z", "4.0.55": "2017-10-26T19:32:19.805Z", "4.0.56": "2017-10-30T19:06:32.547Z", "4.0.57": "2017-11-21T18:22:17.547Z", "4.11.0": "2017-12-20T14:51:53.761Z", "4.11.1": "2018-01-19T22:54:02.542Z", "4.11.2": "2018-05-27T14:14:05.027Z", "4.16.0": "2018-06-05T00:03:39.907Z", "4.16.1": "2019-01-23T00:15:43.641Z", "4.16.2": "2019-03-18T16:09:06.598Z", "4.16.3": "2019-04-23T18:34:23.315Z", "4.16.4": "2019-04-29T06:26:00.526Z", "4.16.5": "2019-05-22T15:57:05.118Z", "4.16.6": "2019-05-24T16:50:37.874Z", "4.16.7": "2019-06-06T20:01:40.453Z", "4.16.8": "2019-08-16T18:08:35.946Z", "4.16.9": "2019-08-19T01:03:25.492Z", "4.16.10": "2019-10-15T21:13:59.257Z", "4.16.11": "2019-11-01T17:25:21.029Z", "4.17.0": "2019-11-15T21:45:56.072Z", "4.17.1": "2019-12-20T06:16:52.340Z", "4.17.2": "2020-01-21T21:36:39.192Z", "4.17.3": "2020-03-23T15:37:32.788Z", "4.17.4": "2020-04-08T16:40:07.224Z", "4.17.5": "2020-04-15T03:34:44.106Z", "4.17.6": "2020-05-05T16:46:09.942Z", "4.17.7": "2020-05-07T20:32:50.257Z", "4.17.8": "2020-06-24T06:31:17.306Z", "4.17.9": "2020-07-22T10:38:13.718Z", "4.17.10": "2020-09-01T08:07:34.261Z", "4.17.11": "2020-09-01T13:46:48.054Z", "4.17.12": "2020-09-01T16:20:07.308Z", "4.17.13": "2020-09-22T00:24:16.591Z", "4.17.14": "2020-11-23T20:49:29.956Z", "4.17.15": "2020-12-08T21:40:14.574Z", "4.17.16": "2020-12-14T18:05:05.861Z", "4.17.17": "2020-12-15T17:31:54.654Z", "4.17.18": "2021-01-11T22:15:41.664Z", "4.17.19": "2021-03-15T17:48:01.876Z", "4.17.20": "2021-05-25T17:01:53.473Z", "4.17.21": "2021-06-02T22:31:51.301Z", "4.17.22": "2021-06-28T02:01:24.559Z", "4.17.23": "2021-07-06T20:50:26.108Z", "4.17.24": "2021-07-08T16:31:37.164Z", "4.17.25": "2021-11-09T10:01:58.882Z", "4.17.26": "2021-11-30T01:01:34.789Z", "4.17.27": "2021-12-23T23:50:58.605Z", "4.17.28": "2022-01-11T22:32:53.087Z", "4.17.29": "2022-06-14T17:31:42.244Z", "4.17.30": "2022-07-26T23:32:24.390Z", "4.17.31": "2022-09-13T18:26:34.998Z", "4.17.32": "2022-12-30T23:13:33.826Z", "4.17.33": "2023-01-23T21:32:53.642Z", "4.17.34": "2023-04-26T12:02:46.798Z", "4.17.35": "2023-05-13T03:32:53.150Z", "4.17.36": "2023-08-22T18:14:02.908Z", "4.17.37": "2023-09-23T17:30:50.812Z", "4.17.38": "2023-10-18T02:14:27.410Z", "4.17.39": "2023-10-19T17:17:35.630Z", "4.17.40": "2023-11-06T19:42:00.654Z", "4.17.41": "2023-11-07T03:16:04.744Z", "4.17.42": "2024-01-25T23:07:20.437Z", "4.17.43": "2024-02-03T13:07:20.157Z", "4.19.0": "2024-04-04T22:35:45.439Z", "4.19.1": "2024-05-21T20:08:10.193Z", "4.19.2": "2024-05-29T18:08:13.566Z", "4.19.3": "2024-05-30T18:08:47.430Z", "4.19.4": "2024-06-19T17:35:47.159Z", "4.19.5": "2024-06-19T19:07:06.142Z", "5.0.0": "2024-09-25T19:20:39.162Z", "4.19.6": "2024-09-25T19:20:49.926Z", "5.0.1": "2024-10-24T14:02:57.408Z", "5.0.2": "2024-11-24T20:02:47.258Z", "5.0.3": "2025-01-01T00:47:12.291Z", "5.0.4": "2025-01-07T22:02:24.639Z", "5.0.5": "2025-01-14T18:03:02.631Z", "5.0.6": "2025-01-28T05:02:29.838Z", "5.0.7": "2025-07-08T18:02:31.367Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express-serve-static-core", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express-serve-static-core"}, "description": "TypeScript definitions for express-serve-static-core", "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/micksatana", "name": "<PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JoseLion", "name": "<PERSON>", "githubUsername": "JoseLion"}, {"url": "https://github.com/dwrss", "name": "<PERSON>", "githubUsername": "dwrss"}, {"url": "https://github.com/andoshin11", "name": "<PERSON>", "githubUsername": "andoshin11"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}