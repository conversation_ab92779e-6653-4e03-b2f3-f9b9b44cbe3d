{"_id": "@jridgewell/gen-mapping", "_rev": "24-3e6b78d461496bbf477c054d61f6e83b", "name": "@jridgewell/gen-mapping", "dist-tags": {"beta": "0.3.6-beta.4", "latest": "0.3.12"}, "versions": {"0.1.0": {"name": "@jridgewell/gen-mapping", "version": "0.1.0", "keywords": ["source", "map"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/gen-mapping@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/gen-mapping#readme", "bugs": {"url": "https://github.com/jridgewell/gen-mapping/issues"}, "dist": {"shasum": "b30fd66b5426bb7f44bb6043ab127b20ee404063", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.1.0.tgz", "fileCount": 10, "integrity": "sha512-YH+BnkvuCiPR+MUOY6JIArdTIGrRtsxnLaIxPRy4CpGJ/V6OO6Gq/1J+FJEc4j5e5h6Bcy3/K7prlMrm93BJoA==", "signatures": [{"sig": "MEUCIE8LCis1wibHAp8m7ylvT4oV6ZoKAyXKzyaEDJoFXSPHAiEA7PoJ1C/LkFHU7AQmR0qEfBcD1Djk5GEE78yuapFlT6Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43086, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaJXYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXKw/+Jamfdm8Me/onrhC89agEYNrPe9sQxdU0K+BZxKeqk5KgprY2\r\nsM7ZyVQVGyKJAaFw1OXNmWAX6D7ogfp9JZltFze4P/r1yeA8erYwYEozFmcX\r\nUzWk2ta/WihuMJRMukxc+3z23DLg+DTV1okHKdTgazyWLSbCHtL++AgTZX2/\r\n12dKNa320XczYrgGxRhAOJDjJ93U4tiSXXhPCXdB+7edrU17drwSA7VLRDBJ\r\n679CIFDZ6MY9TUSrHCDwytcR13Lvsnpj9S0qDC3uv0J4CObhg116mQxFxTgL\r\nIS0xb/8dTlpAZtYRwh/f3RL2vgLRuSx4KbW7RVYfg80Bykhbcn2nLB8vfYsN\r\nyH9CDZig06TzuHb2TSqnoJDDGXEDhhtBFpcWELwGc5Z17jSpzpvO81PjUMzA\r\nN0kdni7iYTd++QNYJZGgWunDqyRctOjYIPde9mRvqXCThdc8OKJbgLyo5EC0\r\nXXD/daseYd6ujP/RXCOhek7dETS6aDZ654MafrrvOEnnyLYwvfMdh8qOc8RA\r\nbRsGQ8wtEy/OB3B3qtzigxiE0nfXH7kP7v6L9hZb+nte+2GKwZ4wKxiMhPCG\r\no8fdIpMjb9UUcaPrfFPnmQMLqcw6zpjSw+4AMWOrmROVW4VEKwTnQFXWS3i+\r\n3kf1MHH9l14iB5QsZEoDMq+5m3tYLYFVN6Y=\r\n=98+U\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/gen-mapping.umd.js", "module": "dist/gen-mapping.mjs", "engines": {"node": ">=6.0.0"}, "exports": {".": {"import": "./dist/gen-mapping.mjs", "browser": "./dist/gen-mapping.umd.js", "require": "./dist/gen-mapping.umd.js"}, "./package.json": "./package.json"}, "gitHead": "22ef3ca68112cbf8780ae9f5b73a65061d6da26c", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:coverage", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "pretest": "run-s build:rollup", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "test:lint": "run-s -n test:lint:*", "test:only": "mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "run-p 'build:rollup -- --watch' 'test:only -- --watch'", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "test:coverage": "c8 mocha", "prepublishOnly": "npm run preversion", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "typings": "dist/types/gen-mapping.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/gen-mapping.git", "type": "git"}, "_npmVersion": "8.5.1", "description": "Generate source maps", "directories": {}, "_nodeVersion": "17.6.0", "dependencies": {"@jridgewell/set-array": "1.0.0", "@jridgewell/sourcemap-codec": "^1.4.10"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "7.11.0", "mocha": "9.2.0", "eslint": "8.7.0", "rollup": "2.66.0", "prettier": "2.5.1", "typescript": "4.5.5", "@types/node": "17.0.28", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.3.0", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/parser": "5.10.0", "@typescript-eslint/eslint-plugin": "5.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/gen-mapping_0.1.0_1651021272687_0.49895110307354074", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "@jridgewell/gen-mapping", "version": "0.1.1", "keywords": ["source", "map"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/gen-mapping@0.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/gen-mapping#readme", "bugs": {"url": "https://github.com/jridgewell/gen-mapping/issues"}, "dist": {"shasum": "e5d2e450306a9491e3bd77e323e38d7aff315996", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.1.1.tgz", "fileCount": 10, "integrity": "sha512-sQXCasFk+U8lWYEe66WxRDOE9PjVz4vSM51fTu3Hw+ClTpUSQb718772vH3pyS5pShp6lvQM7SxgIDXXXmOX7w==", "signatures": [{"sig": "MEUCIQCxFajn4WxQBcvGauk/zCQIVpao7hMCQs6Dk9/vwvObFQIgc05CdcnkJt2iwL/UolCMJ8xxe4D7Eb52vfZ2gm8Ev5g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52350, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaVScACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmou9g//Qlejq4PF0fUpsSoaBLyNnkUwXHtwsj9xJ1M7NU2MFc6S0cKb\r\nvCNoFMxBzongHTEyWwJ9/zd/cirZMXVS5k2ecZPM6r3DgR7bitsBTjW89xO+\r\nDiwsgJ2yMlufZIbd1pXv8ZMsyfV/SxJPSPT74udOeU7qu1s8MI46vRylnUIi\r\nMVhl8WFw8RxD/ojjrgQfECLb6TIEtK1LpLj1z6trsCCPla3fEiSe7kHcp81X\r\n3DYgAHnDVKpSxNAjbgG4KUYKKo1//+2WuvyRf+E3o9FSoEpS64FBjErX80OY\r\nGzqIDBlJzFVP8AZFT5Y2zdballRfSBYBjyYslIDnRFFI4FD5uAS+XuSYme/E\r\nnHHHpdcuiyj8AotD003ASoZ+7k0eXasJ22idV/gsovg288XRTWCctQf7LOWu\r\nEVa7bw5VpV044XM0vVg43WZb6543zaHJxiCP/Eu9rrOY9GJJk85RQJLhWBIf\r\nECoTTCzcomv4i0ghgkHgdmHRzJbH5DOSbYc1Y86rc+A4FfKIgo9nl4h70pSQ\r\nNenYKooVdtG51B1RoaFAmwCrPvxz4FLGgYVJBi6vEdXdyLixsekvwL18OHYt\r\nWcYkjjoadwJrtBfGovi5v5UAijfFSZX/iRsf62ApoJcRvqsCrZTy05yQEk2S\r\nEPTIwNmDDDfNXuWsF4NqLFTtPOgJUHXaMpk=\r\n=d+en\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/gen-mapping.umd.js", "module": "dist/gen-mapping.mjs", "engines": {"node": ">=6.0.0"}, "exports": {".": {"import": "./dist/gen-mapping.mjs", "browser": "./dist/gen-mapping.umd.js", "require": "./dist/gen-mapping.umd.js"}, "./package.json": "./package.json"}, "gitHead": "2464c6cbd4b98bbbd6d97bbf1889edd1a647adcc", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:coverage", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "pretest": "run-s build:rollup", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:*", "test:lint": "run-s -n test:lint:*", "test:only": "mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "run-p 'build:rollup -- --watch' 'test:only -- --watch'", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "test:coverage": "c8 mocha", "benchmark:only": "node benchmark/index.mjs", "prepublishOnly": "npm run preversion", "benchmark:install": "cd benchmark && npm install", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "typings": "dist/types/gen-mapping.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/gen-mapping.git", "type": "git"}, "_npmVersion": "8.5.1", "description": "Generate source maps", "directories": {}, "_nodeVersion": "17.6.0", "dependencies": {"@jridgewell/set-array": "^1.0.0", "@jridgewell/sourcemap-codec": "^1.4.10"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "7.11.2", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/gen-mapping_0.1.1_1651070108663_0.687055510323439", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "@jridgewell/gen-mapping", "version": "0.2.0", "keywords": ["source", "map"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/gen-mapping@0.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/gen-mapping#readme", "bugs": {"url": "https://github.com/jridgewell/gen-mapping/issues"}, "dist": {"shasum": "8b4d9da3c3a75e06187d1e926f8f8b7a4fab12d0", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.2.0.tgz", "fileCount": 10, "integrity": "sha512-wcKrTKpG3DIUivSPbzsG/YauY77Ljfhe0OnMRvzveOTZ5mJEwT7h2NhquJ9vEIkxMeeyqHz9YnRYXGPbMeriUw==", "signatures": [{"sig": "MEQCIFGCXYHUQOBERh7/JHxgsp1f7lW86j9eHtYp8owMGs9qAiBH/E2NJuB99Bqy/7Fywu7kINx4X8XYRjMk9iQ9fNeGoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57095, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaZFDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmonpw//ZFCKPmI8yNmftl64Rl7w1AQHwW6EgIqaGi7uWtx7mD6ldV5U\r\nNhPFInURW5e4Klq1t6XKxSKA+KeAhXUdJi0WZYWqHsw6R6VKIQvXn14tB71z\r\ntR1gENaJ4Vzhw3yU1/pIlDhB++/n252VY5zwNW/1ZnLZ9MiRbfmrm5OErn0K\r\nixqKAGh8a4LrrYcBPRM/ZxHKliEeKBrdaX94uLGAaOMcQWA7Vi3xnKMZou4B\r\nNZIYuVD9wO0oqU33/kL5FYT8n56Uu3+TgvR5IrpA2R+FNtn0o+3Bp+PypZt/\r\nbJQg85eqUQ7jh8p3mAQZ23JWKjs0pyG3ZAHN/svEG26BjE0Qf845axrQgHf3\r\nktdmEHrM/oTgVEa02RND3cf8GQY/s7pJ6ir+c0go63j0m2zL8SQCCKkqe04O\r\nozW0/6fSwPEFgiqquZoXVStTNCWr+y0+6A/75JN7oPNntqdoGdHMhyTsYQ6M\r\ntQpq8uH3WM1uzOuUIubgnEK7AMd1FUUCMXZJl1irynaJ20ufFIOkSHBIjDE7\r\nfj1EQ3fuyVPs89e4xHYeo0g7qrW8c6D/U43RwR7vSKHsMjRmL+JMtj75qkgL\r\n7l2emGGwUfxZSnLwqHzclPr3z7jCw4F48xUh42v+LXqgZAlVLyi0E0NcQ8h6\r\nir7r4WiTuAD5vzpxVtku5fxcnMeUkrDwyyU=\r\n=Vd+0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/gen-mapping.umd.js", "module": "dist/gen-mapping.mjs", "engines": {"node": ">=6.0.0"}, "exports": {".": {"import": "./dist/gen-mapping.mjs", "browser": "./dist/gen-mapping.umd.js", "require": "./dist/gen-mapping.umd.js"}, "./package.json": "./package.json"}, "gitHead": "89f096a56f878be21ddd83d5a2fdae17c6e7c09d", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:coverage", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "pretest": "run-s build:rollup", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:*", "test:lint": "run-s -n test:lint:*", "test:only": "mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "run-p 'build:rollup -- --watch' 'test:only -- --watch'", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "test:coverage": "c8 mocha", "benchmark:only": "node benchmark/index.mjs", "prepublishOnly": "npm run preversion", "benchmark:install": "cd benchmark && npm install", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "typings": "dist/types/gen-mapping.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/gen-mapping.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "Generate source maps", "directories": {}, "_nodeVersion": "18.0.0", "dependencies": {"@jridgewell/set-array": "^1.0.0", "@jridgewell/trace-mapping": "^0.3.9", "@jridgewell/sourcemap-codec": "^1.4.10"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "7.11.2", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/gen-mapping_0.2.0_1651085634968_0.5773922306947608", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "@jridgewell/gen-mapping", "version": "0.3.0", "keywords": ["source", "map"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/gen-mapping@0.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/gen-mapping#readme", "bugs": {"url": "https://github.com/jridgewell/gen-mapping/issues"}, "dist": {"shasum": "62d7a390ad9f98be6af7daddb327174a3f295acc", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.0.tgz", "fileCount": 10, "integrity": "sha512-bFDbLEZ84DtNwXILbufXeQMz3tcGSfy+ROgvnB9jUm+t48G4Po+1UhhyFh6GIsFOE2R31Ab0ddeMv+Z9gcYC4g==", "signatures": [{"sig": "MEUCICyFmtOMI2HQhw0pfl8jWdgVIrC/AjsbSikAE96FYvMqAiEAtBu18L046jyqskwPgZpaIdpk4oI8NsCBj7C2piRVwLA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibLpzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrcXw/6A7qXzgeim9f247UMhLdlpznjDfMOUN8OdhrFEhO67T6nigPw\r\nMRLpfKuAutBCFKpqyf5KrI5L00LPxReyz/0Sg3l780SBo2aK8oQA4euDUjyS\r\nRzU0/yJxAHf2YJ1BaUNZYcAX1dcHUsIpKjMfjQg/d7ReWkYN2B7i7hKj8niu\r\nFjaUhwRCSgObbLoz8TqVhg2//faTZnh7/5UPA4PVw4IO6z4danTSaE9iWvQq\r\npCf5JMs50J2x55hCZyRxQ1+rrCjAY+7gGtB0myquBBrrzQWoaOiphixVyvfi\r\n+MhampLnc3ZAo4GZ3rwGC/lpYeKwzv4d1ek1Kuj2vQVd/V74BduiWLZ9P4hQ\r\nFnB/cKeAftWaPDhKCqQ/FUhda9u1sKc1wd0aFLkEI24ROyaQkARADbkjsvQR\r\nzZsipRPZWICpCLz+E9Q0DVa3x3PZtF7mcRzeqX1f2sKCikr6wuMcApmEuwuG\r\nHg6xKsmBcxn02rcDy44+QzT9wiTrX85FE8CgLQaJ3O9r7RHROH4iRhKqpOLV\r\ntuG71UniHw3NiRe5BIeZzFBecOawGjRKWxQFdIjX5Yba8N+GpQQ1A3EqDv6e\r\nDt0Y0h1OEPO+ejLeGbdmSohlcUSKPY1zHnrgMDCAfmJ6BMeC2CcfnLqHUfDL\r\nxWel3VVzYVyDkxs2pxCAaa4avRor6fusEyU=\r\n=AiyA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/gen-mapping.umd.js", "module": "dist/gen-mapping.mjs", "engines": {"node": ">=6.0.0"}, "exports": {".": {"import": "./dist/gen-mapping.mjs", "browser": "./dist/gen-mapping.umd.js", "require": "./dist/gen-mapping.umd.js"}, "./package.json": "./package.json"}, "gitHead": "55e0a5877c310746ec94172ff6cb26dd408c1bcf", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:coverage", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "pretest": "run-s build:rollup", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:*", "test:lint": "run-s -n test:lint:*", "test:only": "mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "run-p 'build:rollup -- --watch' 'test:only -- --watch'", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "test:coverage": "c8 mocha", "benchmark:only": "node benchmark/index.mjs", "prepublishOnly": "npm run preversion", "benchmark:install": "cd benchmark && npm install", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "typings": "dist/types/gen-mapping.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/gen-mapping.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "Generate source maps", "directories": {}, "_nodeVersion": "18.0.0", "dependencies": {"@jridgewell/set-array": "^1.0.0", "@jridgewell/trace-mapping": "^0.3.9", "@jridgewell/sourcemap-codec": "^1.4.10"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "7.11.2", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/gen-mapping_0.3.0_1651292787138_0.8645226309239749", "host": "s3://npm-registry-packages"}}, "0.3.1": {"name": "@jridgewell/gen-mapping", "version": "0.3.1", "keywords": ["source", "map"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/gen-mapping@0.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/gen-mapping#readme", "bugs": {"url": "https://github.com/jridgewell/gen-mapping/issues"}, "dist": {"shasum": "cf92a983c83466b8c0ce9124fadeaf09f7c66ea9", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.1.tgz", "fileCount": 10, "integrity": "sha512-GcHwniMlA2z+WFPWuY8lp3fsza0I8xPFMWL5+n8LYyP6PSvPrXf4+n8stDHZY2DM0zy9sVkRDy1jDI4XGzYVqg==", "signatures": [{"sig": "MEYCIQDF63oilzcbNgK+ZOZtxvTAm/rw7dfZg3ZGZ9NEMWLSvgIhAJGtj13MHxy6fVUEVkDDljJX0b/CVtCcSY6wTiN2m3S2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidEjDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUZg//Uet8QJcEEcg9oK0h+wtwJ1swUD8BY415IMZkyc7gy/nvm3Py\r\nAGYu0+0AO5yoRd3xETitt3OWea+sKkiEKUw7OGYzBEq/09xM5p6Z08vkrz9b\r\nx/h9OL73hIwWUVSeq7a4rmVoXXfHZEeEzOOjnk9L/pCDvjSzh+a+d8jwdAnv\r\nj8WA64bL2urh8OHMaGVe6tZa7wDiCSiA1OOjgMAVNkTHEY3MJhMkgO8lP29l\r\nx0OPsNpdRRpZ+fMZ49fSFGQT0GcwAilEs0Xcywb+m7Kag+niA3n2ZOGkqezH\r\n3cdZ1AIoFFF8S2n3JK9iGfRKMnCgolxsnoCKV5+UPhizcKgRv+q0MPb/cBIF\r\ntf6AWBmyEaA4hbEWLpJ6q2EdeBp5t3t0HVxjMcxIQ1oXWe/OuFxwneglGTGK\r\nk1lEDbefPqCmLyOIam0h4cRKDf4WxBPAe6DjrJYOa2A3Dk9VDod6F1iSlc0w\r\nv3Ioe58EzQ0SPRlHkhtC7KnqJW6oYYJnWUjDM/DuEVWpqtp6uhIkdVREwbo7\r\neTVp7G0VS0bFHgMar8dIv6ABI46yn5325JKUPA0Tz4AHdPFiZWPSsCFCBMyH\r\ngqRAid7GhlF5Qc78RW4bECgfeRaJdeyOXeskNUT1dKtPtJOvEE84nGp2smoR\r\nJ9wYNFQXLgERzxWfamEKGNnjlsO4TMq6e/I=\r\n=crJb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/gen-mapping.umd.js", "module": "dist/gen-mapping.mjs", "engines": {"node": ">=6.0.0"}, "exports": {".": {"types": "./dist/types/gen-mapping.d.ts", "import": "./dist/gen-mapping.mjs", "browser": "./dist/gen-mapping.umd.js", "require": "./dist/gen-mapping.umd.js"}, "./package.json": "./package.json"}, "gitHead": "b6aa06065065960be0465bcc6d2a586a7251c519", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:coverage", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "pretest": "run-s build:rollup", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:*", "test:lint": "run-s -n test:lint:*", "test:only": "mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "run-p 'build:rollup -- --watch' 'test:only -- --watch'", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "test:coverage": "c8 mocha", "benchmark:only": "node benchmark/index.mjs", "prepublishOnly": "npm run preversion", "benchmark:install": "cd benchmark && npm install", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "typings": "dist/types/gen-mapping.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/gen-mapping.git", "type": "git"}, "_npmVersion": "8.5.1", "description": "Generate source maps", "directories": {}, "_nodeVersion": "17.6.0", "dependencies": {"@jridgewell/set-array": "^1.0.0", "@jridgewell/trace-mapping": "^0.3.9", "@jridgewell/sourcemap-codec": "^1.4.10"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "7.11.2", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/gen-mapping_0.3.1_1651787971397_0.35654190154543586", "host": "s3://npm-registry-packages"}}, "0.3.2": {"name": "@jridgewell/gen-mapping", "version": "0.3.2", "keywords": ["source", "map"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/gen-mapping@0.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/gen-mapping#readme", "bugs": {"url": "https://github.com/jridgewell/gen-mapping/issues"}, "dist": {"shasum": "c1aedc61e853f2bb9f5dfe6d4442d3b565b253b9", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.2.tgz", "fileCount": 13, "integrity": "sha512-mh65xKQAzI6iBcFzwv28KVWSmCkdRBWoOh+bYQGW3+6OZvbbN3TqMGo5hqYxQniRcH9F2VZIoJCm4pa3BPDK/A==", "signatures": [{"sig": "MEYCIQCkHcj7Kz+CGFdhrZb78rbL2v+aoKA/terBPdEPvgBtSwIhAJArXPn+VaKXaw8QzGOWUgn27lVTFamUoNBM6q3oKlxq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuIAhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoauA//WHClHK3DAb90491O82xa/s+PeNjCvEYeOtBgJeWajZ7qC66U\r\n1ao5+lHKNWSgoH0sSRTUa8JdMREf/yLsoa+p+MWpYY7tiBjozMCADdRF6DY0\r\nLDEm2CdMBSMHGtj2AkWKuBO3c4AhXOXpShJD8qy5AgoplOSQFXjWJgAVj+l2\r\nJcXN7ATOOaxsBT2s4oDojf8oIfIBHQNnvjUsBeMvuXr+31yukVLL3ZNqpoyw\r\nzGZTkgWJTTDgUajvL1CpCWO1zexH4D13vr8ioytcg2hcgZBJWanlQ4vurAQn\r\nHC5bJo2gaibKnm/MR6k2sChYZBFT/P9k9AB20jEEbJ8aLFBNMuE9IMAf5tr0\r\nfery99/tnoxgylR3xX27f9EzZVMTWYY6ZLSydhHHCAYRPaNwIyPWM0C6HLi/\r\ntxwv4N2xldRb1ppjcCbJA5lMgUDCLLeohINHYbx6lSO3ynaIrwdQkuP0lhI5\r\nRfUwQXSto3qTUTwGSa5c24Z3xBcxDKgZ5agGdYkKVi7Ee0osBY8QIZnZMje0\r\nEAX+lc1UjzFyf1pNZGplPPi3QEjXcoThTuoUBHbRN417tC/7JeeR3M4uXHuM\r\nc5A2zf3/VWquo/VT5NY8Lgm/L7SlQNlWe12G8cww3BmeDNMuRIBwkEdMwwXa\r\nA7nKAoF4kL5nhmaeFJwaTJJVPlo6b1lDBNg=\r\n=dVFM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/gen-mapping.umd.js", "module": "dist/gen-mapping.mjs", "engines": {"node": ">=6.0.0"}, "exports": {".": [{"types": "./dist/types/gen-mapping.d.ts", "import": "./dist/gen-mapping.mjs", "browser": "./dist/gen-mapping.umd.js", "require": "./dist/gen-mapping.umd.js"}, "./dist/gen-mapping.umd.js"], "./package.json": "./package.json"}, "gitHead": "a44a71dd0aa2c4eac4a8577f3e150dbc3aafdb80", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:coverage", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "pretest": "run-s build:rollup", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:*", "test:lint": "run-s -n test:lint:*", "test:only": "mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "run-p 'build:rollup -- --watch' 'test:only -- --watch'", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "test:coverage": "c8 mocha", "benchmark:only": "node benchmark/index.mjs", "prepublishOnly": "npm run preversion", "benchmark:install": "cd benchmark && npm install", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "typings": "dist/types/gen-mapping.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/gen-mapping.git", "type": "git"}, "_npmVersion": "8.5.1", "description": "Generate source maps", "directories": {}, "_nodeVersion": "17.6.0", "dependencies": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/trace-mapping": "^0.3.9", "@jridgewell/sourcemap-codec": "^1.4.10"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "7.11.2", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/gen-mapping_0.3.2_1656258593635_0.1210398085343849", "host": "s3://npm-registry-packages"}}, "0.3.3": {"name": "@jridgewell/gen-mapping", "version": "0.3.3", "keywords": ["source", "map"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/gen-mapping@0.3.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/gen-mapping#readme", "bugs": {"url": "https://github.com/jridgewell/gen-mapping/issues"}, "dist": {"shasum": "7e02e6eb5df901aaedb08514203b096614024098", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.3.tgz", "fileCount": 10, "integrity": "sha512-HLhSWOLRi875zjjMG/r+Nv0oCW8umGb0BgEhyX3dDX3egwZtB8PqLnjz3yedt8R5StBrzcg4aBpnh8UA9D1BoQ==", "signatures": [{"sig": "MEUCID/RBvg06WdLrqJIqxHUYCFRnV2Pe6ib7iQ7LhM5kB2+AiEAkJxeaNM1iEgu7b2sSTDaFHGMffbmy3wbYBxrq4i1dMM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkL4MnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqjcg//aIoa0GhUePPWVXJf9kaSyG6PwlH9pFki00HtHzQXAJrKA5Rl\r\nLt9xuCN9JqcvhdyseSiFeKeRcGU77EN2XXbSYRQ6qfstBCMr4Ol493vyRjUp\r\njhgVJvIXUzSm/0xuraXTed2zqVnQMnVfv0DabFmoR07zMlObfnwqrkw0KlfS\r\n/M6FHpYoCr5x0qZiAFRm6tdiu59BaAcMUrmEEKWtlQWcW1l7C0Crdd2NGsPN\r\nhK56yc7q3oHhlz5as+3KC6B5ouWMWUmKQt6OKj+KkH2An8OrD3K2lsbKvd50\r\n8UG1nHGcZ8p1lB+iHGk7hOLCOADkjPjf3GI1TagdvcL9izlrWnTGiaG9rhZY\r\nrYwqSy4qPRW274YV1+Mkh4EYUTpBLs6ZPtllJh3LQcF2f3/EfaYxUrLBu/91\r\nV6Bpmp3nOAjFkomfTSY4wZ4czpzqMmkZGbjUR8KWuA2BYxr5efODYzTihTUW\r\n2bTbzP61A4JczJ3zx7uhC+3ue3at+S6RlsHb8Yg/Jnom2pD6aXcLUOy3V3v2\r\nwDtGD5H2HBbUILGhgF8zdDNSMj0wkBLYfjSeJQtKD+T9QOXGrxh0dWJt3PO1\r\n6HSSun5A0c0eLP4K/eoAhMj26yi6bZbWqGsm7bd+F2yPVKDeZS4RSYGO/Xf4\r\nolsDo4qQQM6N0HkFvLSOUZ7rCyJCg1C2j8k=\r\n=bKff\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/gen-mapping.umd.js", "types": "dist/types/gen-mapping.d.ts", "module": "dist/gen-mapping.mjs", "engines": {"node": ">=6.0.0"}, "exports": {".": [{"types": "./dist/types/gen-mapping.d.ts", "import": "./dist/gen-mapping.mjs", "browser": "./dist/gen-mapping.umd.js", "require": "./dist/gen-mapping.umd.js"}, "./dist/gen-mapping.umd.js"], "./package.json": "./package.json"}, "gitHead": "7626d310002df682012dc29bdfa4d674027f1538", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:coverage", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "pretest": "run-s build:rollup", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:*", "test:lint": "run-s -n test:lint:*", "test:only": "mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "run-p 'build:rollup -- --watch' 'test:only -- --watch'", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "test:coverage": "c8 mocha", "benchmark:only": "node benchmark/index.mjs", "prepublishOnly": "npm run preversion", "benchmark:install": "cd benchmark && npm install", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/gen-mapping.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Generate source maps", "directories": {}, "_nodeVersion": "18.15.0", "dependencies": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/trace-mapping": "^0.3.9", "@jridgewell/sourcemap-codec": "^1.4.10"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "7.11.2", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/gen-mapping_0.3.3_1680835367395_0.5518499539233093", "host": "s3://npm-registry-packages"}}, "0.3.4": {"name": "@jridgewell/gen-mapping", "version": "0.3.4", "keywords": ["source", "map"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/gen-mapping@0.3.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/gen-mapping#readme", "bugs": {"url": "https://github.com/jridgewell/gen-mapping/issues"}, "dist": {"shasum": "9b18145d26cf33d08576cf4c7665b28554480ed7", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.4.tgz", "fileCount": 10, "integrity": "sha512-Oud2QPM5dHviZNn4y/WhhYKSXksv+1xLEIsNrAbGcFzUN3ubqWRFT5gwPchNc5NuzILOU4tPBDTZ4VwhL8Y7cw==", "signatures": [{"sig": "MEYCIQC1RousGmGX2TqpU4Jn101c+LAQ+4yMvoq9o/fDn8L+eAIhAMe6URtOMuG7cbUb1dD/XA3PalwHcNoT5ir9U//MUx07", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77305}, "main": "dist/gen-mapping.umd.js", "types": "dist/types/gen-mapping.d.ts", "module": "dist/gen-mapping.mjs", "engines": {"node": ">=6.0.0"}, "exports": {".": [{"types": "./dist/types/gen-mapping.d.ts", "import": "./dist/gen-mapping.mjs", "browser": "./dist/gen-mapping.umd.js", "require": "./dist/gen-mapping.umd.js"}, "./dist/gen-mapping.umd.js"], "./package.json": "./package.json"}, "gitHead": "b9d92dc2036d9bcb20ecddc6d81f76bdbe3745e4", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:only", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:*", "test:lint": "run-s -n test:lint:*", "test:only": "c8 mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "mocha --watch", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "benchmark:only": "node benchmark/index.mjs", "prepublishOnly": "npm run preversion", "benchmark:install": "cd benchmark && npm install", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/gen-mapping.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Generate source maps", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/trace-mapping": "^0.3.9", "@jridgewell/sourcemap-codec": "^1.4.10"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "7.11.2", "tsx": "4.7.1", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/gen-mapping_0.3.4_1708766676013_0.4063755897520389", "host": "s3://npm-registry-packages"}}, "0.3.5": {"name": "@jridgewell/gen-mapping", "version": "0.3.5", "keywords": ["source", "map"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/gen-mapping@0.3.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/gen-mapping#readme", "bugs": {"url": "https://github.com/jridgewell/gen-mapping/issues"}, "dist": {"shasum": "dcce6aff74bdf6dad1a95802b69b04a2fcb1fb36", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.5.tgz", "fileCount": 10, "integrity": "sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==", "signatures": [{"sig": "MEYCIQDeR9RZQLHph7BRYaG73JvLbl9cQVxZ9bFziFanwoCrUgIhAIwauVzEpz+zTXlmCHDLmW+RKcXr38SETZYVlqq2j086", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81596}, "main": "dist/gen-mapping.umd.js", "types": "dist/types/gen-mapping.d.ts", "module": "dist/gen-mapping.mjs", "engines": {"node": ">=6.0.0"}, "exports": {".": [{"types": "./dist/types/gen-mapping.d.ts", "import": "./dist/gen-mapping.mjs", "browser": "./dist/gen-mapping.umd.js", "require": "./dist/gen-mapping.umd.js"}, "./dist/gen-mapping.umd.js"], "./package.json": "./package.json"}, "gitHead": "75eff9d01ef5da5477e316ec0f4f917ebe08dd96", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:only", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:*", "test:lint": "run-s -n test:lint:*", "test:only": "c8 mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "mocha --watch", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "benchmark:only": "node benchmark/index.mjs", "prepublishOnly": "npm run preversion", "benchmark:install": "cd benchmark && npm install", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/gen-mapping.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Generate source maps", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/trace-mapping": "^0.3.24", "@jridgewell/sourcemap-codec": "^1.4.10"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "7.11.2", "tsx": "4.7.1", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/gen-mapping_0.3.5_1709278747728_0.2539192790874143", "host": "s3://npm-registry-packages"}}, "0.3.6-beta.0": {"name": "@jridgewell/gen-mapping", "version": "0.3.6-beta.0", "keywords": ["source", "map"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/gen-mapping@0.3.6-beta.0", "homepage": "https://github.com/jridgewell/gen-mapping#readme", "bugs": {"url": "https://github.com/jridgewell/gen-mapping/issues"}, "dist": {"shasum": "a86ab183cd73712d7a13ee481de343aa13ee4bdf", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.6-beta.0.tgz", "fileCount": 10, "integrity": "sha512-c01tGUmxYTkK35boqusPP6pYhf1E38oJ7cTtUApXQSGEhDLuWLfUtt7aqi8NPWiSjI/0OuJYeIevlA51Yzh0GQ==", "signatures": [{"sig": "MEQCICUYvF1Uu0TetGnCtV5IKnSBZoaNwRHZHPB8MOmUcq4yAiBZZBSiZpPGumcup9wpxUenzTXC6X9Wd9Dgvp8zEyTIog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104990}, "main": "dist/gen-mapping.umd.js", "types": "dist/types/gen-mapping.d.ts", "module": "dist/gen-mapping.mjs", "engines": {"node": ">=6.0.0"}, "exports": {".": [{"types": "./dist/types/gen-mapping.d.ts", "import": "./dist/gen-mapping.mjs", "browser": "./dist/gen-mapping.umd.js", "require": "./dist/gen-mapping.umd.js"}, "./dist/gen-mapping.umd.js"], "./package.json": "./package.json"}, "gitHead": "948d6438995b664c031cb45bd6046d82284567fe", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:only", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:*", "test:lint": "run-s -n test:lint:*", "test:only": "c8 mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "mocha --watch", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "benchmark:only": "node benchmark/index.mjs", "prepublishOnly": "npm run preversion", "benchmark:install": "cd benchmark && npm install", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/gen-mapping.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Generate source maps", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/trace-mapping": "^0.3.24", "@jridgewell/sourcemap-codec": "1.4.16-beta.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "7.11.2", "tsx": "4.7.1", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/gen-mapping_0.3.6-beta.0_1719314950686_0.09059741030028734", "host": "s3://npm-registry-packages"}}, "0.3.6-beta.1": {"name": "@jridgewell/gen-mapping", "version": "0.3.6-beta.1", "keywords": ["source", "map"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/gen-mapping@0.3.6-beta.1", "homepage": "https://github.com/jridgewell/gen-mapping#readme", "bugs": {"url": "https://github.com/jridgewell/gen-mapping/issues"}, "dist": {"shasum": "585cea7c2d3ec86824f7643168e9b1b8da6bcdda", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.6-beta.1.tgz", "fileCount": 10, "integrity": "sha512-HLhGll6NZkn1dvNy5IwEUnTaGOaZFQljViMrWXsNmTdRtA4CA9ZybHEBVUv4jOUJF9TB8ioV0LvzK0D6jshKZQ==", "signatures": [{"sig": "MEYCIQC9fb5FHw7Gj2wv5BK/PWAgPxpmlWlIQHJZJD2iALKftAIhAP+MKLC5UxJyoGborj8hiVSAfbGBIjv0BV63yk6ZKPAl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104963}, "main": "dist/gen-mapping.umd.js", "types": "dist/types/gen-mapping.d.ts", "module": "dist/gen-mapping.mjs", "engines": {"node": ">=6.0.0"}, "exports": {".": [{"types": "./dist/types/gen-mapping.d.ts", "import": "./dist/gen-mapping.mjs", "browser": "./dist/gen-mapping.umd.js", "require": "./dist/gen-mapping.umd.js"}, "./dist/gen-mapping.umd.js"], "./package.json": "./package.json"}, "gitHead": "47b0caab89668a033ace103cd9041f453dcdcd2b", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:only", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:*", "test:lint": "run-s -n test:lint:*", "test:only": "c8 mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "mocha --watch", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "benchmark:only": "node benchmark/index.mjs", "prepublishOnly": "npm run preversion", "benchmark:install": "cd benchmark && npm install", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/gen-mapping.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Generate source maps", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/trace-mapping": "^0.3.24", "@jridgewell/sourcemap-codec": "1.4.16-beta.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "7.11.2", "tsx": "4.7.1", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/gen-mapping_0.3.6-beta.1_1719316201242_0.4713812959851873", "host": "s3://npm-registry-packages"}}, "0.3.6-beta.2": {"name": "@jridgewell/gen-mapping", "version": "0.3.6-beta.2", "keywords": ["source", "map"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/gen-mapping@0.3.6-beta.2", "homepage": "https://github.com/jridgewell/gen-mapping#readme", "bugs": {"url": "https://github.com/jridgewell/gen-mapping/issues"}, "dist": {"shasum": "91ce86a2c8566073e3a8e559ce6bc13ae3fb9b25", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.6-beta.2.tgz", "fileCount": 10, "integrity": "sha512-n1d8d3LnT5uU665vCUlQpBtVdZ0tMXiTm/8+YaqMLCMQ0tjMgrPoOd38njoAmtYuUVyvR+sd9gfDWpMDTH8k5w==", "signatures": [{"sig": "MEUCIQDAgLtr4Sn8DscFqz/oSQXUm/0i1y8rroyLiD0rtvWx2AIgRbnjnO9OVayt9NIfw2DOZWS7hT8BG4fImZsQML2IfuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105375}, "main": "dist/gen-mapping.umd.js", "types": "dist/types/gen-mapping.d.ts", "module": "dist/gen-mapping.mjs", "engines": {"node": ">=6.0.0"}, "exports": {".": [{"types": "./dist/types/gen-mapping.d.ts", "import": "./dist/gen-mapping.mjs", "browser": "./dist/gen-mapping.umd.js", "require": "./dist/gen-mapping.umd.js"}, "./dist/gen-mapping.umd.js"], "./package.json": "./package.json"}, "gitHead": "c993c593381443829c896f5c6ae3d570e2970530", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:only", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:*", "test:lint": "run-s -n test:lint:*", "test:only": "c8 mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "mocha --watch", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "benchmark:only": "node benchmark/index.mjs", "prepublishOnly": "npm run preversion", "benchmark:install": "cd benchmark && npm install", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/gen-mapping.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Generate source maps", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/trace-mapping": "^0.3.24", "@jridgewell/sourcemap-codec": "1.4.16-beta.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "7.11.2", "tsx": "4.7.1", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/gen-mapping_0.3.6-beta.2_1719316594544_0.0994689048244548", "host": "s3://npm-registry-packages"}}, "0.3.6-beta.3": {"name": "@jridgewell/gen-mapping", "version": "0.3.6-beta.3", "keywords": ["source", "map"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/gen-mapping@0.3.6-beta.3", "homepage": "https://github.com/jridgewell/gen-mapping#readme", "bugs": {"url": "https://github.com/jridgewell/gen-mapping/issues"}, "dist": {"shasum": "7f97480a8d83a7c5f06a9a1a70b4e9b1808d526b", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.6-beta.3.tgz", "fileCount": 10, "integrity": "sha512-3Ok+ILMAeGsxRhVnv6uKgMx/BH36Y+h0D+BzAypGsXPK6+KPU37rd/wU2qj2/Eypo8EYOPHkDF5dbjZIErRtUw==", "signatures": [{"sig": "MEQCIExhqlRPeC5zurJp6C6Lytf5LSchnQlOvLxeELqHrtqVAiBD0yaf+K+gpYtxQhs7opcQm6zKvniUrLUMIp0Iw7Hp1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106289}, "main": "dist/gen-mapping.umd.js", "types": "dist/types/gen-mapping.d.ts", "module": "dist/gen-mapping.mjs", "engines": {"node": ">=6.0.0"}, "exports": {".": [{"types": "./dist/types/gen-mapping.d.ts", "import": "./dist/gen-mapping.mjs", "browser": "./dist/gen-mapping.umd.js", "require": "./dist/gen-mapping.umd.js"}, "./dist/gen-mapping.umd.js"], "./package.json": "./package.json"}, "gitHead": "a51a9aead18dc1e04549805705e429d1fe5e80b9", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:only", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:*", "test:lint": "run-s -n test:lint:*", "test:only": "c8 mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "mocha --watch", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "benchmark:only": "node benchmark/index.mjs", "prepublishOnly": "npm run preversion", "benchmark:install": "cd benchmark && npm install", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/gen-mapping.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Generate source maps", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/trace-mapping": "^0.3.24", "@jridgewell/sourcemap-codec": "1.4.16-beta.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "7.11.2", "tsx": "4.7.1", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/gen-mapping_0.3.6-beta.3_1719317634094_0.45938265899156105", "host": "s3://npm-registry-packages"}}, "0.3.6-beta.4": {"name": "@jridgewell/gen-mapping", "version": "0.3.6-beta.4", "keywords": ["source", "map"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/gen-mapping@0.3.6-beta.4", "homepage": "https://github.com/jridgewell/gen-mapping#readme", "bugs": {"url": "https://github.com/jridgewell/gen-mapping/issues"}, "dist": {"shasum": "b63fec0ead8041a154f157dc20b20809cd55d89f", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.6-beta.4.tgz", "fileCount": 10, "integrity": "sha512-R9Zbvfrltklz5QAgf8sYeiwPnkbLRD4HfS3DfAzeZF4CmM0iY2EckGgNDschXN6aBt8PgeNYCBfp12xuR5GpLg==", "signatures": [{"sig": "MEYCIQCXmPRyf7hqW4eZO/TVlMcuMimXmilsf7JXXB2uIGsDhwIhALSxXXyiVQiplR1L5YJwk0BPQ0QLTKsUH7d/FoykVPah", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106318}, "main": "dist/gen-mapping.umd.js", "types": "dist/types/gen-mapping.d.ts", "module": "dist/gen-mapping.mjs", "engines": {"node": ">=6.0.0"}, "exports": {".": [{"types": "./dist/types/gen-mapping.d.ts", "import": "./dist/gen-mapping.mjs", "browser": "./dist/gen-mapping.umd.js", "require": "./dist/gen-mapping.umd.js"}, "./dist/gen-mapping.umd.js"], "./package.json": "./package.json"}, "gitHead": "6c86691e35e7c75c83a33d689b25459663929322", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:only", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:*", "test:lint": "run-s -n test:lint:*", "test:only": "c8 mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "mocha --watch", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "benchmark:only": "node benchmark/index.mjs", "prepublishOnly": "npm run preversion", "benchmark:install": "cd benchmark && npm install", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/gen-mapping.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Generate source maps", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/trace-mapping": "^0.3.24", "@jridgewell/sourcemap-codec": "1.4.16-beta.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "7.11.2", "tsx": "4.7.1", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/gen-mapping_0.3.6-beta.4_1719317859801_0.1375146075334459", "host": "s3://npm-registry-packages"}}, "0.3.6": {"name": "@jridgewell/gen-mapping", "version": "0.3.6", "keywords": ["source", "map"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/gen-mapping@0.3.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/sourcemaps#readme", "bugs": {"url": "https://github.com/jridgewell/sourcemaps/issues"}, "dist": {"shasum": "9c3ced7fc4ceee01fbd5f8cd257310aa71341a0b", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.6.tgz", "fileCount": 7, "integrity": "sha512-e8fGuQbA+pfsS2fGGRZgciyzstnkNrZCRIuOzgej9WCxPjHW3fn6h9SoJC3MrvfylMegiJPse94+mBJGf/qltQ==", "signatures": [{"sig": "MEYCIQCorULB0DpN2ouxXL8p8KrTTklDDqFjuUWndC0cGJAv3gIhANEsBSVAiJNktY9UFL2T2AEHQJkndtrD9QSvo4cW2OvW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31627}, "main": "dist/gen-mapping.umd.js", "types": "types/gen-mapping.d.ts", "module": "dist/gen-mapping.mjs", "exports": {".": [{"types": "./types/gen-mapping.d.ts", "import": "./dist/gen-mapping.mjs", "browser": "./dist/gen-mapping.umd.js", "require": "./dist/gen-mapping.umd.js"}, "./dist/gen-mapping.umd.js"], "./package.json": "./package.json"}, "gitHead": "7575b63bc3f70388c2cafc33efc8b47254ff4b4c", "scripts": {"lint": "run-s -n lint:types lint:format", "test": "run-s -n test:types test:only test:format", "build": "run-s -n build:code build:types", "benchmark": "run-s build:code benchmark:*", "test:only": "mocha", "build:code": "UMD=sourcemapCodec node ../../esbuild.mjs gen-mapping.ts", "lint:types": "npm run test:types -- --fix", "test:types": "eslint '{src,test}/**/*.ts'", "build:types": "tsc --project tsconfig.build.json", "lint:format": "npm run test:format -- --write", "test:format": "prettier --check '{src,test}/**/*.ts'", "benchmark:only": "node --expose-gc benchmark/index.js", "benchmark:install": "cd benchmark && npm install"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "publish failed to build the dist directory", "repository": {"url": "git+https://github.com/jridgewell/sourcemaps.git", "type": "git", "directory": "packages/gen-mapping"}, "_npmVersion": "9.8.1", "description": "Generate source maps", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"@jridgewell/trace-mapping": "^0.3.24", "@jridgewell/sourcemap-codec": "1.4.16-beta.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/gen-mapping_0.3.6_1733813967026_0.27991118928926717", "host": "s3://npm-registry-packages-npm-production"}}, "0.3.7": {"name": "@jridgewell/gen-mapping", "version": "0.3.7", "keywords": ["source", "map"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/gen-mapping@0.3.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/sourcemaps#readme", "bugs": {"url": "https://github.com/jridgewell/sourcemaps/issues"}, "dist": {"shasum": "724ecf7ce7bb6aca0b4bf8d15ec40fb435162ef3", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.7.tgz", "fileCount": 15, "integrity": "sha512-rNoqXKSm/P1EJw/lrzyaQepC6KfryLFSShA+jl1mbPYqutXlvZWwm0x03W6jMHaYJqIOmc7QarD+GVfG9hO7XQ==", "signatures": [{"sig": "MEUCIBKzkj5J8/qUAjI0EOX/IgFRBMPKyapGVvkWgu1OmiBbAiEAqzr54uEtoYXKAnTzDWA4IPZW6yLJKj0ynsyDBZ27ucY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275633}, "main": "dist/gen-mapping.umd.js", "types": "types/gen-mapping.d.ts", "module": "dist/gen-mapping.mjs", "exports": {".": [{"types": "./types/gen-mapping.d.ts", "import": "./dist/gen-mapping.mjs", "browser": "./dist/gen-mapping.umd.js", "require": "./dist/gen-mapping.umd.js"}, "./dist/gen-mapping.umd.js"], "./package.json": "./package.json"}, "gitHead": "9fd42d6cfa8c80540d5b08621cd23957f128d866", "scripts": {"lint": "run-s -n lint:types lint:format", "test": "run-s -n test:types test:only test:format", "build": "run-s -n build:code build:types", "benchmark": "run-s build:code benchmark:*", "test:only": "mocha", "build:code": "UMD=sourcemapCodec node ../../esbuild.mjs gen-mapping.ts", "lint:types": "npm run test:types -- --fix", "test:types": "eslint '{src,test}/**/*.ts'", "build:types": "tsc --project tsconfig.build.json", "lint:format": "npm run test:format -- --write", "test:format": "prettier --check '{src,test}/**/*.ts'", "benchmark:only": "node --expose-gc benchmark/index.js", "prepublishOnly": "npm run-s -n build test", "benchmark:install": "cd benchmark && npm install"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "newer syntax features were not transpiled", "repository": {"url": "git+https://github.com/jridgewell/sourcemaps.git", "type": "git", "directory": "packages/gen-mapping"}, "_npmVersion": "10.9.2", "description": "Generate source maps", "directories": {}, "_nodeVersion": "20.6.1", "dependencies": {"@jridgewell/trace-mapping": "^0.3.24", "@jridgewell/sourcemap-codec": "1.4.16-beta.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/gen-mapping_0.3.7_1733819098815_0.025692396914250848", "host": "s3://npm-registry-packages-npm-production"}}, "0.3.8": {"name": "@jridgewell/gen-mapping", "version": "0.3.8", "keywords": ["source", "map"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/gen-mapping@0.3.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/gen-mapping#readme", "bugs": {"url": "https://github.com/jridgewell/gen-mapping/issues"}, "dist": {"shasum": "4f0e06362e01362f823d348f1872b08f666d8142", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz", "fileCount": 10, "integrity": "sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==", "signatures": [{"sig": "MEYCIQC4lRjYrY+X+5qmRros2HRnGDP5pe42Kep5Q6wv9SLM2QIhAMwX3MD9c4IwKUY6A0pA9ZWM+Mwt9gks23jCUURBbuzB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81596}, "main": "dist/gen-mapping.umd.js", "types": "dist/types/gen-mapping.d.ts", "module": "dist/gen-mapping.mjs", "engines": {"node": ">=6.0.0"}, "exports": {".": [{"types": "./dist/types/gen-mapping.d.ts", "import": "./dist/gen-mapping.mjs", "browser": "./dist/gen-mapping.umd.js", "require": "./dist/gen-mapping.umd.js"}, "./dist/gen-mapping.umd.js"], "./package.json": "./package.json"}, "gitHead": "0719ff4756b9f1c6c66c244a2ef29e0229e8dcd9", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:only", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:*", "test:lint": "run-s -n test:lint:*", "test:only": "c8 mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "mocha --watch", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "benchmark:only": "node benchmark/index.mjs", "prepublishOnly": "npm run preversion", "benchmark:install": "cd benchmark && npm install", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/gen-mapping.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Generate source maps", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/trace-mapping": "^0.3.24", "@jridgewell/sourcemap-codec": "^1.4.10"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "7.11.2", "tsx": "4.7.1", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/gen-mapping_0.3.8_1733944203485_0.0024117644006400596", "host": "s3://npm-registry-packages-npm-production"}}, "0.3.9": {"name": "@jridgewell/gen-mapping", "version": "0.3.9", "keywords": ["source", "map"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/gen-mapping@0.3.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/sourcemaps#readme", "bugs": {"url": "https://github.com/jridgewell/sourcemaps/issues"}, "dist": {"shasum": "6651c7afca59e19abd00cd40346d13e225026c83", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.9.tgz", "fileCount": 27, "integrity": "sha512-xpz6C/vXOegF9VEtlMBlkNNIjHrLhKaFBsO4lmQGr00x5BHp7p+oliR6i7LwIcM5cZU2VjLSwm2R+/zj5IjPWg==", "signatures": [{"sig": "MEUCIAg0PvWiiGZd5/AqWa2rfbuc/GKorTWg1GpsofhozkcTAiEA97MAEhLbSofTtTxwHlpt6i662Z80HSicfs2+aZXJa3M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 87371}, "main": "dist/gen-mapping.umd.js", "types": "types/gen-mapping.d.cts", "module": "dist/gen-mapping.mjs", "exports": {".": [{"import": {"types": "./types/gen-mapping.d.mts", "default": "./dist/gen-mapping.mjs"}, "browser": {"types": "./types/gen-mapping.d.cts", "default": "./dist/gen-mapping.umd.js"}, "require": {"types": "./types/gen-mapping.d.cts", "default": "./dist/gen-mapping.umd.js"}, "module-sync": {"types": "./types/gen-mapping.d.mts", "default": "./dist/gen-mapping.mjs"}}, "./dist/gen-mapping.umd.js"], "./package.json": "./package.json"}, "gitHead": "eddb11752fcd9cd752a986c5b5c4ae836e3d6662", "scripts": {"lint": "run-s -n lint:types lint:format", "test": "run-s -n test:types test:only test:format", "build": "run-s -n build:code build:types", "clean": "run-s -n clean:code clean:types", "benchmark": "run-s build:code benchmark:*", "test:only": "mocha", "build:code": "node ../../esbuild.mjs gen-mapping.ts", "clean:code": "tsc --build --clean tsconfig.build.json", "lint:types": "npm run test:types -- --fix", "test:types": "eslint '{src,test}/**/*.ts'", "build:types": "run-s build:types:force build:types:emit build:types:mts", "clean:types": "rimraf dist types", "lint:format": "npm run test:format -- --write", "test:format": "prettier --check '{src,test}/**/*.ts'", "benchmark:only": "node --expose-gc benchmark/index.js", "prepublishOnly": "npm run-s -n build test", "build:types:mts": "node ../../mts-types.mjs", "build:types:emit": "tsc --project tsconfig.build.json", "benchmark:install": "cd benchmark && npm install", "build:types:force": "rimraf tsconfig.build.tsbuildinfo"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/sourcemaps.git", "type": "git", "directory": "packages/gen-mapping"}, "_npmVersion": "10.2.3", "description": "Generate source maps", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"@jridgewell/trace-mapping": "^0.3.24", "@jridgewell/sourcemap-codec": "^1.5.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/gen-mapping_0.3.9_1751232492051_0.18922720989367803", "host": "s3://npm-registry-packages-npm-production"}}, "0.3.10": {"name": "@jridgewell/gen-mapping", "version": "0.3.10", "keywords": ["source", "map"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/gen-mapping@0.3.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/sourcemaps/tree/main/packages/gen-mapping", "bugs": {"url": "https://github.com/jridgewell/sourcemaps/issues"}, "dist": {"shasum": "1cad974c8478e644c5cbce2a4b738137bb64bd4f", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.10.tgz", "fileCount": 27, "integrity": "sha512-HM2F4B9N4cA0RH2KQiIZOHAZqtP4xGS4IZ+SFe1SIbO4dyjf9MTY2Bo3vHYnm0hglWfXqBrzUBSa+cJfl3Xvrg==", "signatures": [{"sig": "MEYCIQCVNTkqFckQVvtNwrXTnJa2gvEAu03u2TbXBsv5CZS0BgIhAJcNdr8QMJ5SuqW8ABle4iaiisgFd9/UEYK3sbEHZyKc", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 87331}, "main": "dist/gen-mapping.umd.js", "types": "types/gen-mapping.d.cts", "module": "dist/gen-mapping.mjs", "exports": {".": [{"import": {"types": "./types/gen-mapping.d.mts", "default": "./dist/gen-mapping.mjs"}, "browser": {"types": "./types/gen-mapping.d.cts", "default": "./dist/gen-mapping.umd.js"}, "require": {"types": "./types/gen-mapping.d.cts", "default": "./dist/gen-mapping.umd.js"}}, "./dist/gen-mapping.umd.js"], "./package.json": "./package.json"}, "gitHead": "dad47801cf1793389c88aeea2b2f688e98113eaa", "scripts": {"lint": "run-s -n lint:types lint:format", "test": "run-s -n test:types test:only test:format", "build": "run-s -n build:code build:types", "clean": "run-s -n clean:code clean:types", "benchmark": "run-s build:code benchmark:*", "test:only": "mocha", "build:code": "node ../../esbuild.mjs gen-mapping.ts", "clean:code": "tsc --build --clean tsconfig.build.json", "lint:types": "npm run test:types -- --fix", "test:types": "eslint '{src,test}/**/*.ts'", "build:types": "run-s build:types:force build:types:emit build:types:mts", "clean:types": "rimraf dist types", "lint:format": "npm run test:format -- --write", "test:format": "prettier --check '{src,test}/**/*.ts'", "benchmark:only": "node --expose-gc benchmark/index.js", "prepublishOnly": "npm run-s -n build test", "build:types:mts": "node ../../mts-types.mjs", "build:types:emit": "tsc --project tsconfig.build.json", "benchmark:install": "cd benchmark && npm install", "build:types:force": "rimraf tsconfig.build.tsbuildinfo"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/sourcemaps.git", "type": "git", "directory": "packages/gen-mapping"}, "_npmVersion": "10.2.3", "description": "Generate source maps", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"@jridgewell/trace-mapping": "^0.3.24", "@jridgewell/sourcemap-codec": "^1.5.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/gen-mapping_0.3.10_1751259823080_0.21640380582872254", "host": "s3://npm-registry-packages-npm-production"}}, "0.3.11": {"name": "@jridgewell/gen-mapping", "version": "0.3.11", "keywords": ["source", "map"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/gen-mapping@0.3.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/sourcemaps/tree/main/packages/gen-mapping", "bugs": {"url": "https://github.com/jridgewell/sourcemaps/issues"}, "dist": {"shasum": "02faf35e82eb08a465704d501f60cd073f8d1715", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.11.tgz", "fileCount": 27, "integrity": "sha512-C512c1ytBTio4MrpWKlJpyFHT6+qfFL8SZ58zBzJ1OOzUEjHeF1BtjY2fH7n4x/g2OV/KiiMLAivOp1DXmiMMw==", "signatures": [{"sig": "MEQCIBPv2HOrxasVd+2SsYFWBW/ebS82dQGLPE3mjlyAMIJQAiA6P3pgFfK35QrU1t2ZURNAqUHyEMLuYjvWxupquNs3Ww==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 87699}, "main": "dist/gen-mapping.umd.js", "types": "types/gen-mapping.d.cts", "module": "dist/gen-mapping.mjs", "exports": {".": [{"import": {"types": "./types/gen-mapping.d.mts", "default": "./dist/gen-mapping.mjs"}, "browser": {"types": "./types/gen-mapping.d.cts", "default": "./dist/gen-mapping.umd.js"}, "require": {"types": "./types/gen-mapping.d.cts", "default": "./dist/gen-mapping.umd.js"}}, "./dist/gen-mapping.umd.js"], "./package.json": "./package.json"}, "gitHead": "d2578bd71829135bee7f510e62f898bcde97e80a", "scripts": {"lint": "run-s -n lint:types lint:format", "test": "run-s -n test:types test:only test:format", "build": "run-s -n build:code build:types", "clean": "run-s -n clean:code clean:types", "benchmark": "run-s build:code benchmark:*", "test:only": "mocha", "build:code": "node ../../esbuild.mjs gen-mapping.ts", "clean:code": "tsc --build --clean tsconfig.build.json", "lint:types": "npm run test:types -- --fix", "test:types": "eslint '{src,test}/**/*.ts'", "build:types": "run-s build:types:force build:types:emit build:types:mts", "clean:types": "rimraf dist types", "lint:format": "npm run test:format -- --write", "test:format": "prettier --check '{src,test}/**/*.ts'", "benchmark:only": "node --expose-gc benchmark/index.js", "prepublishOnly": "npm run-s -n build test", "build:types:mts": "node ../../mts-types.mjs", "build:types:emit": "tsc --project tsconfig.build.json", "benchmark:install": "cd benchmark && npm install", "build:types:force": "rimraf tsconfig.build.tsbuildinfo"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/sourcemaps.git", "type": "git", "directory": "packages/gen-mapping"}, "_npmVersion": "10.2.3", "description": "Generate source maps", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"@jridgewell/trace-mapping": "^0.3.24", "@jridgewell/sourcemap-codec": "^1.5.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/gen-mapping_0.3.11_1751306911920_0.30827863124480803", "host": "s3://npm-registry-packages-npm-production"}}, "0.3.12": {"name": "@jridgewell/gen-mapping", "version": "0.3.12", "description": "Generate source maps", "keywords": ["source", "map"], "main": "dist/gen-mapping.umd.js", "module": "dist/gen-mapping.mjs", "types": "types/gen-mapping.d.cts", "exports": {".": [{"import": {"types": "./types/gen-mapping.d.mts", "default": "./dist/gen-mapping.mjs"}, "require": {"types": "./types/gen-mapping.d.cts", "default": "./dist/gen-mapping.umd.js"}, "browser": {"types": "./types/gen-mapping.d.cts", "default": "./dist/gen-mapping.umd.js"}}, "./dist/gen-mapping.umd.js"], "./package.json": "./package.json"}, "scripts": {"benchmark": "run-s build:code benchmark:*", "benchmark:install": "cd benchmark && npm install", "benchmark:only": "node --expose-gc benchmark/index.js", "build": "run-s -n build:code build:types", "build:code": "node ../../esbuild.mjs gen-mapping.ts", "build:types": "run-s build:types:force build:types:emit build:types:mts", "build:types:force": "rimraf tsconfig.build.tsbuildinfo", "build:types:emit": "tsc --project tsconfig.build.json", "build:types:mts": "node ../../mts-types.mjs", "clean": "run-s -n clean:code clean:types", "clean:code": "tsc --build --clean tsconfig.build.json", "clean:types": "rimraf dist types", "test": "run-s -n test:types test:only test:format", "test:format": "prettier --check '{src,test}/**/*.ts'", "test:only": "mocha", "test:types": "eslint '{src,test}/**/*.ts'", "lint": "run-s -n lint:types lint:format", "lint:format": "npm run test:format -- --write", "lint:types": "npm run test:types -- --fix", "prepublishOnly": "npm run-s -n build test"}, "homepage": "https://github.com/jridgewell/sourcemaps/tree/main/packages/gen-mapping", "repository": {"type": "git", "url": "git+https://github.com/jridgewell/sourcemaps.git", "directory": "packages/gen-mapping"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0", "@jridgewell/trace-mapping": "^0.3.24"}, "_id": "@jridgewell/gen-mapping@0.3.12", "gitHead": "3c6286e949d58c2bc6e27334385349c6a83c6d7d", "bugs": {"url": "https://github.com/jridgewell/sourcemaps/issues"}, "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-OuLGC46TjB5BbN1dH8JULVVZY4WTdkF7tV9Ys6wLL1rubZnCMstOhNHueU5bLCrnRuDhKPDM4g6sw4Bel5Gzqg==", "shasum": "2234ce26c62889f03db3d7fea43c1932ab3e927b", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz", "fileCount": 27, "unpackedSize": 87114, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCy5dK3ItZW8ct6gpyEPlbdHKkPz5QaClDasveGRXy4gAIhAMvIxVmjzM2ITDVGdn3nyeHi6vRuLle74ihD00/ehNY4"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/gen-mapping_0.3.12_1751393934111_0.5562253790474305"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-04-27T01:01:12.594Z", "modified": "2025-07-01T18:18:54.456Z", "0.1.0": "2022-04-27T01:01:12.843Z", "0.1.1": "2022-04-27T14:35:08.800Z", "0.2.0": "2022-04-27T18:53:55.146Z", "0.3.0": "2022-04-30T04:26:27.301Z", "0.3.1": "2022-05-05T21:59:31.553Z", "0.3.2": "2022-06-26T15:49:53.823Z", "0.3.3": "2023-04-07T02:42:47.543Z", "0.3.4": "2024-02-24T09:24:36.219Z", "0.3.5": "2024-03-01T07:39:07.918Z", "0.3.6-beta.0": "2024-06-25T11:29:10.917Z", "0.3.6-beta.1": "2024-06-25T11:50:01.420Z", "0.3.6-beta.2": "2024-06-25T11:56:34.815Z", "0.3.6-beta.3": "2024-06-25T12:13:54.254Z", "0.3.6-beta.4": "2024-06-25T12:17:39.973Z", "0.3.6": "2024-12-10T06:59:27.174Z", "0.3.7": "2024-12-10T08:24:59.035Z", "0.3.8": "2024-12-11T19:10:03.689Z", "0.3.9": "2025-06-29T21:28:12.225Z", "0.3.10": "2025-06-30T05:03:43.261Z", "0.3.11": "2025-06-30T18:08:32.100Z", "0.3.12": "2025-07-01T18:18:54.284Z"}, "bugs": {"url": "https://github.com/jridgewell/sourcemaps/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/jridgewell/sourcemaps/tree/main/packages/gen-mapping", "keywords": ["source", "map"], "repository": {"type": "git", "url": "git+https://github.com/jridgewell/sourcemaps.git", "directory": "packages/gen-mapping"}, "description": "Generate source maps", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# @jridgewell/gen-mapping\n\n> Generate source maps\n\n`gen-mapping` allows you to generate a source map during transpilation or minification.\nWith a source map, you're able to trace the original location in the source file, either in Chrome's\nDevTools or using a library like [`@jridgewell/trace-mapping`][trace-mapping].\n\nYou may already be familiar with the [`source-map`][source-map] package's `SourceMapGenerator`. This\nprovides the same `addMapping` and `setSourceContent` API.\n\n## Installation\n\n```sh\nnpm install @jridgewell/gen-mapping\n```\n\n## Usage\n\n```typescript\nimport { GenMapping, addMapping, setSourceContent, toEncodedMap, toDecodedMap } from '@jridgewell/gen-mapping';\n\nconst map = new GenMapping({\n  file: 'output.js',\n  sourceRoot: 'https://example.com/',\n});\n\nsetSourceContent(map, 'input.js', `function foo() {}`);\n\naddMapping(map, {\n  // Lines start at line 1, columns at column 0.\n  generated: { line: 1, column: 0 },\n  source: 'input.js',\n  original: { line: 1, column: 0 },\n});\n\naddMapping(map, {\n  generated: { line: 1, column: 9 },\n  source: 'input.js',\n  original: { line: 1, column: 9 },\n  name: 'foo',\n});\n\nassert.deepEqual(toDecodedMap(map), {\n  version: 3,\n  file: 'output.js',\n  names: ['foo'],\n  sourceRoot: 'https://example.com/',\n  sources: ['input.js'],\n  sourcesContent: ['function foo() {}'],\n  mappings: [\n    [ [0, 0, 0, 0], [9, 0, 0, 9, 0] ]\n  ],\n});\n\nassert.deepEqual(toEncodedMap(map), {\n  version: 3,\n  file: 'output.js',\n  names: ['foo'],\n  sourceRoot: 'https://example.com/',\n  sources: ['input.js'],\n  sourcesContent: ['function foo() {}'],\n  mappings: 'AAAA,SAASA',\n});\n```\n\n### Smaller Sourcemaps\n\nNot everything needs to be added to a sourcemap, and needless markings can cause signficantly\nlarger file sizes. `gen-mapping` exposes `maybeAddSegment`/`maybeAddMapping` APIs that will\nintelligently determine if this marking adds useful information. If not, the marking will be\nskipped.\n\n```typescript\nimport { maybeAddMapping } from '@jridgewell/gen-mapping';\n\nconst map = new GenMapping();\n\n// Adding a sourceless marking at the beginning of a line isn't useful.\nmaybeAddMapping(map, {\n  generated: { line: 1, column: 0 },\n});\n\n// Adding a new source marking is useful.\nmaybeAddMapping(map, {\n  generated: { line: 1, column: 0 },\n  source: 'input.js',\n  original: { line: 1, column: 0 },\n});\n\n// But adding another marking pointing to the exact same original location isn't, even if the\n// generated column changed.\nmaybeAddMapping(map, {\n  generated: { line: 1, column: 9 },\n  source: 'input.js',\n  original: { line: 1, column: 0 },\n});\n\nassert.deepEqual(toEncodedMap(map), {\n  version: 3,\n  names: [],\n  sources: ['input.js'],\n  sourcesContent: [null],\n  mappings: 'AAAA',\n});\n```\n\n## Benchmarks\n\n```\nnode v18.0.0\n\namp.js.map\nMemory Usage:\ngen-mapping: addSegment      5852872 bytes\ngen-mapping: addMapping      7716042 bytes\nsource-map-js                6143250 bytes\nsource-map-0.6.1             6124102 bytes\nsource-map-0.8.0             6121173 bytes\nSmallest memory usage is gen-mapping: addSegment\n\nAdding speed:\ngen-mapping:      addSegment x 441 ops/sec ±2.07% (90 runs sampled)\ngen-mapping:      addMapping x 350 ops/sec ±2.40% (86 runs sampled)\nsource-map-js:    addMapping x 169 ops/sec ±2.42% (80 runs sampled)\nsource-map-0.6.1: addMapping x 167 ops/sec ±2.56% (80 runs sampled)\nsource-map-0.8.0: addMapping x 168 ops/sec ±2.52% (80 runs sampled)\nFastest is gen-mapping:      addSegment\n\nGenerate speed:\ngen-mapping:      decoded output x 150,824,370 ops/sec ±0.07% (102 runs sampled)\ngen-mapping:      encoded output x 663 ops/sec ±0.22% (98 runs sampled)\nsource-map-js:    encoded output x 197 ops/sec ±0.45% (84 runs sampled)\nsource-map-0.6.1: encoded output x 198 ops/sec ±0.33% (85 runs sampled)\nsource-map-0.8.0: encoded output x 197 ops/sec ±0.06% (93 runs sampled)\nFastest is gen-mapping:      decoded output\n\n\n***\n\n\nbabel.min.js.map\nMemory Usage:\ngen-mapping: addSegment     37578063 bytes\ngen-mapping: addMapping     37212897 bytes\nsource-map-js               47638527 bytes\nsource-map-0.6.1            47690503 bytes\nsource-map-0.8.0            47470188 bytes\nSmallest memory usage is gen-mapping: addMapping\n\nAdding speed:\ngen-mapping:      addSegment x 31.05 ops/sec ±8.31% (43 runs sampled)\ngen-mapping:      addMapping x 29.83 ops/sec ±7.36% (51 runs sampled)\nsource-map-js:    addMapping x 20.73 ops/sec ±6.22% (38 runs sampled)\nsource-map-0.6.1: addMapping x 20.03 ops/sec ±10.51% (38 runs sampled)\nsource-map-0.8.0: addMapping x 19.30 ops/sec ±8.27% (37 runs sampled)\nFastest is gen-mapping:      addSegment\n\nGenerate speed:\ngen-mapping:      decoded output x 381,379,234 ops/sec ±0.29% (96 runs sampled)\ngen-mapping:      encoded output x 95.15 ops/sec ±2.98% (72 runs sampled)\nsource-map-js:    encoded output x 15.20 ops/sec ±7.41% (33 runs sampled)\nsource-map-0.6.1: encoded output x 16.36 ops/sec ±10.46% (31 runs sampled)\nsource-map-0.8.0: encoded output x 16.06 ops/sec ±6.45% (31 runs sampled)\nFastest is gen-mapping:      decoded output\n\n\n***\n\n\npreact.js.map\nMemory Usage:\ngen-mapping: addSegment       416247 bytes\ngen-mapping: addMapping       419824 bytes\nsource-map-js                1024619 bytes\nsource-map-0.6.1             1146004 bytes\nsource-map-0.8.0             1113250 bytes\nSmallest memory usage is gen-mapping: addSegment\n\nAdding speed:\ngen-mapping:      addSegment x 13,755 ops/sec ±0.15% (98 runs sampled)\ngen-mapping:      addMapping x 13,013 ops/sec ±0.11% (101 runs sampled)\nsource-map-js:    addMapping x 4,564 ops/sec ±0.21% (98 runs sampled)\nsource-map-0.6.1: addMapping x 4,562 ops/sec ±0.11% (99 runs sampled)\nsource-map-0.8.0: addMapping x 4,593 ops/sec ±0.11% (100 runs sampled)\nFastest is gen-mapping:      addSegment\n\nGenerate speed:\ngen-mapping:      decoded output x 379,864,020 ops/sec ±0.23% (93 runs sampled)\ngen-mapping:      encoded output x 14,368 ops/sec ±4.07% (82 runs sampled)\nsource-map-js:    encoded output x 5,261 ops/sec ±0.21% (99 runs sampled)\nsource-map-0.6.1: encoded output x 5,124 ops/sec ±0.58% (99 runs sampled)\nsource-map-0.8.0: encoded output x 5,434 ops/sec ±0.33% (96 runs sampled)\nFastest is gen-mapping:      decoded output\n\n\n***\n\n\nreact.js.map\nMemory Usage:\ngen-mapping: addSegment       975096 bytes\ngen-mapping: addMapping      1102981 bytes\nsource-map-js                2918836 bytes\nsource-map-0.6.1             2885435 bytes\nsource-map-0.8.0             2874336 bytes\nSmallest memory usage is gen-mapping: addSegment\n\nAdding speed:\ngen-mapping:      addSegment x 4,772 ops/sec ±0.15% (100 runs sampled)\ngen-mapping:      addMapping x 4,456 ops/sec ±0.13% (97 runs sampled)\nsource-map-js:    addMapping x 1,618 ops/sec ±0.24% (97 runs sampled)\nsource-map-0.6.1: addMapping x 1,622 ops/sec ±0.12% (99 runs sampled)\nsource-map-0.8.0: addMapping x 1,631 ops/sec ±0.12% (100 runs sampled)\nFastest is gen-mapping:      addSegment\n\nGenerate speed:\ngen-mapping:      decoded output x 379,107,695 ops/sec ±0.07% (99 runs sampled)\ngen-mapping:      encoded output x 5,421 ops/sec ±1.60% (89 runs sampled)\nsource-map-js:    encoded output x 2,113 ops/sec ±1.81% (98 runs sampled)\nsource-map-0.6.1: encoded output x 2,126 ops/sec ±0.10% (100 runs sampled)\nsource-map-0.8.0: encoded output x 2,176 ops/sec ±0.39% (98 runs sampled)\nFastest is gen-mapping:      decoded output\n```\n\n[source-map]: https://www.npmjs.com/package/source-map\n[trace-mapping]: https://github.com/jridgewell/trace-mapping\n", "readmeFilename": "README.md"}