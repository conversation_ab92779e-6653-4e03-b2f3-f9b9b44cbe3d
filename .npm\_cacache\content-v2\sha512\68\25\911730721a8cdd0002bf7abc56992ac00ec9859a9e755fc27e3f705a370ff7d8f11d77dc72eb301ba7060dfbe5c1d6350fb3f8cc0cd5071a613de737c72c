{"_id": "cjs-module-lexer", "_rev": "50-8f044c1ecbc0bb409703bb34b507bf03", "name": "cjs-module-lexer", "dist-tags": {"latest": "2.1.0"}, "versions": {"0.1.0": {"name": "cjs-module-lexer", "version": "0.1.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "02d3277f96a8e81ea34982a80a71c12a4079b974", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.1.0.tgz", "fileCount": 6, "integrity": "sha512-gWIUFhjJfST2qL2X7g7aUDjL9w1NG3c9DCElEGLTIohU0V0cmfCpGrwXkuFv6ZE6+XNsOZdUNrojmP7RKP9rGw==", "signatures": [{"sig": "MEQCIGspFgtOXrKcCD9Ecr6L+watbYN2AUg15NBWRdFDRDodAiBfXUCxBWvRqd+Y2ajKq14YspKaaLMn3ECx8ABhE87EUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevQbvCRA9TVsSAnZWagAA1mYP/1Bu/HhPQgcqvCFYnI/l\nAS67druJtXx6JktOMLZTMIfkIsgtqCOwJ9qxhBDhdVLqT9EqlqCKCVxe/rz0\nQoj+x8XAaxFgXtlBRgKWRvlynY5VbsTE5vvkQfC9kJUBcjffbEoUf+d8SROP\nbgUDFYyL51eJBJ1mb8+7+0HBJ2DOlWsIoCESGZL/3INbqhq7VJkCKBSRIIPq\ntdx4wqm0X1SlerPElxYja7UWV5xnypC2/nQXKIBVrkYOfeOv8PHcyxHiPmlB\nPRxuH134J0m73Wsg0a1h+5GHqkDXsqCfx0+FWRqHBWX74nXtGBwdqh8okTZW\nYfgQxHkg2JnTA+8PrHCzthVJ8zGrLTblpsMT79FUeT763DAgF1RN4UBkbvJ+\nYblJaFq3sMzxkDh/80DHnL6w/HV0cJr9ZXhjyjMQovrgS1fwcXo7IrP/Cghm\nulO/JR0x+XtwFZrWVJJi03CsprzEkMDydPVEQGaTqQ6k1omNdyuSul+F8T5r\nC17eRTBkbQDNSQi9RcsOTkyPzpZ/n/Y4dKWXbVEMpAgWHg8zy2D6auyNQptr\nl5kwdk9mjUnhqXbqFiEiy7FxyTj/ufghoW9Hys0v4Oqm6HLBa/gRdhhQqpNU\nbAsNUmB3WuqTxLN3Pq52UP549Fn7PzrrXkLXW2LrKsHWd8fOyHclvCOgoYTX\ny1kq\r\n=bpAC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "f97ad8d5e5f06da27c2c674473c9488c7c5b80db", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "12.16.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.1.0_1589446382937_0.035552164550745946", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "cjs-module-lexer", "version": "0.1.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "363c57839c7af7fe91e83c497947eddd4ac7a519", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.1.1.tgz", "fileCount": 6, "integrity": "sha512-GqiWxRVaI0J1qwOtXyLx1QnNwdewP/Shy/tywWQRy8vC1eHfsCURp0QFx3JPJJ5SclXl8HWpDpsypLzmLoPpyg==", "signatures": [{"sig": "MEYCIQDIhma67BlkxO+6LPJ8KbldT6t1rc0tJ38YohfcYPp8awIhANKXIIx4IzJTHuBQBhbqRT9MmuswxkkCITR0MqvvttOf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50754, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevRQLCRA9TVsSAnZWagAAH+cP/3POMoF1LY3/mnO2eJQJ\nHEA54/h0tzm6Wk7KXEHliK/TNjbapodJlySGvwBh9gXQGUDJGC9HI+wRlZoA\naw7VHxMDa0gBCt6c/HbcgKUsmfzGCes/8TXP1Ygm7hS7AEBiYWSHe8n7KNIa\nPg7ziRikE9FoInV1nyCEXfos1tgwCWzKaFVUxgKN+B62UKxTpzy3YEq1WsoB\nthkvHEzOX45F+8z8Lvjd8lfLoUEs2/tJQpxXc9yjv4zzjVxI8/kvW7RHKNXk\n1PuUFdHlA6YEDj4zzmsuGXyaz4F0thMn2/bUqBIKioe1CwsAuEY3KrndIvQl\nLQ0vp9ta7dChFr8QL7MX7hGEn+vHcPXeMUejxnbPiLCfESyaJynOMyjWVrd7\nBxz783EeFRf7AGmhPmZs8g490KbHxejorMMsKWeRpC3Bwq6uoxPpudZd9l2h\n931Ry8Td/pTTSn7Q78VDhC1kyXz2hX/euJGqq1VPLAi3cDdCFgcHGh81koG4\nS//URL9d4s9WTR+X+NnpS746Rfl/tlTE8cGzQtR91nmvbhThTLOMlN57axgF\n6QoVjzwbMG+21VkZooTOwqIxrrPA7ceEyBo31oj/FSn6Djh+GjqelkJGw0kn\nsZ/CSi4TdpCnidYOaDmaOzbzqqQ8l7E+iMg005Zp8ONi9yxSQwP69Gbbw4Fa\n4KVz\r\n=GnC7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "cd2f288972d5a6cf2d3d771a34afed6d2590dbda", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "12.16.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.1.1_1589449738927_0.8735157421125943", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "cjs-module-lexer", "version": "0.1.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "b0d45ab878847d7e88751637164d2c8e6efdd1d2", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.1.2.tgz", "fileCount": 5, "integrity": "sha512-AGRmO1viw1tC/DtwHuRbyFL0hPtfhDgm3cKx4gfmORxZz1Pd47Mc/Om0LjGaWSr1fhNUSRvO68HOTBEAfH/6mg==", "signatures": [{"sig": "MEQCIHiOuHB/JCXC7LrbJ4AsAQiHFg7v5gTqpw1C+9LpMrVwAiBsZTL2F7EJGO0Q9Sx2wKvJHZPx/Maa/mX72gBvse6new==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48362, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevRRwCRA9TVsSAnZWagAAhR4P/ijj0xV5PqRJgaaYvYxc\nvpYM1tn249uIJaHngTPRkqeRDHeQ+q68JcqG5ue9YxfFrTqOjlFnBCoXR1Qf\nhBQdAagiPVs73UvdUZJAeOGcIESVYbHmWdlQ/pTegf51AozhosE4GA4pJoqb\n2DFHKEPubnB/ybz3J0uMnv6VB/hrV7VFI67XVbB9brZV3uMxUFTNmM8+XUAX\nRkvJSeuq5W3gFbr53RKr4IuMcTxtNpo5cIuhPDZ46heKM+GBk002bVt8yUyc\nr93MH7XMNZkwI/fYeF0AeAzozs7IWiBDM97wKWsy4CO7Acga4XnHxj8uBFh/\nlOjpooF8jwqgPJOGN6sE0TRsBk8A2aqCQygiFaRBYmLNbwv78Z3ep0s1a4tt\nDasMyyjGXyxhRQyidDHxer5t4zSnOKZl+7fV1gXWIJ0dIC/ecN8IB2Qy2TPJ\n2n7XItYj6ggf6JDNfQ9C3+D5gtAOVThHM5JsNuH86M3VoUIgZomMlyNcNtst\n904Enz08TfUCL+662KDAjtqeRc1h+KirdX+FqzFZ8ovlBuQyNfFqfcru0R0N\nvXOuTtorIdPZYdRh4KSx8S4ZblFscwtZ751ZK2zEHAttwn76hxGknD16nqyc\n7mLSmwctb3sd3eLoaDlEBxT/LH2bJylHKYTs7PLRWiTok6K+Bo1burzHJXPu\nEVGx\r\n=1YAx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "7672983fdb90274718546e1ea9e9b99935c7b2f1", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "12.16.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.1.2_1589449839932_0.13418472169098905", "host": "s3://npm-registry-packages"}}, "0.1.3": {"name": "cjs-module-lexer", "version": "0.1.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "128e38e57f7397b3b548b8c2015ebb1cba31ee03", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.1.3.tgz", "fileCount": 5, "integrity": "sha512-0qDX3hssI97DLAwD5TygLP6mNJVgyCk/6VgMnztR0m/ylotMF5sE6vx/6rfxnTsbfOz/7HcBuzuczPG/tYtcOw==", "signatures": [{"sig": "MEQCICiOkw4YJjfN6A7ZrDmKO/yZkRWIEYJbVCRz79DgoYa8AiApT0PmvqZ4m3AId0FAxQOM4oW1j/lu5rfD6T5wRs97kA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51040, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevh4qCRA9TVsSAnZWagAAL0oQAI7CWEGawqmcdCPQ+qMD\nVVjiIS90RBVCkLQmUP24s0GNuhL6u+JxuG7OBB99PlawLw/ak5EQIAvuqwTg\nuIJ9pm8p8jxTAiZ1pzJzYvKL+qW2BF/irKxcqOBaV0UEoMrAkwzWx7FcqnLK\ndGYLvWvardPcOb/rtsF3Za9Y1IjbN1kRqV6hHcqBbAMWGWidOm9ybclBaGFr\nRqFIk4BFC15wiqYfMiN4DX5BVkL5SUQX9ytlJqfLT6YB3SIu/YNDIGe4Mkmh\n06bTHVup+9McYayCCQiecqmPsidPDLbUPKCiMY1TT0gfE67xo9kI152Nbb3w\nf5v9cXOjKkiREhR7+afteqB13VPLEGI/jD1to0BVzvhYQFyvMk+M2nP/spjY\nx3IlM4oL1mWU+81BKEN3CoeaX5Ill9bcaDdTkeQWag5fFOw7EN5Pi36g8bNG\nMve4zYq1GbWwRVzzGSKSlh6wW00AXIdGCZF25+dShBqeSWW2A8u87eNqu0H3\nkTslrj0S5ao4uKZWdyw2iltYqCPkwZwTc9qk9duBIqBSY5KKjBaibKKJ8up9\nOGNtKMgYYf+kwjHQBvM4Si6qtJGhe6LcIsfvCNDmj2XKdKN493yl7SXE3PzZ\n77nzPWz8IJgNk31MjFtvhqq0+TmtcePyxQ5ihp4V4wZf5WV5g9ReciT7Wt9r\nxVxh\r\n=XdYv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "e85bb98955bafda1fdb609f702429dd2d4a60e60", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "12.16.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.1.3_1589517866154_0.026601161137137952", "host": "s3://npm-registry-packages"}}, "0.1.4": {"name": "cjs-module-lexer", "version": "0.1.4", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.1.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "36586d7aa40d55dc27fb179abc1cca286b91bc12", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.1.4.tgz", "fileCount": 5, "integrity": "sha512-DGXNvad1Va8xw6uHF7W+rNKtfbVkxLvuUWbOro3Quval2/r2LN4Q+AOPx6KToc5F8Bojpml2BqtgQ0hK4qeMpA==", "signatures": [{"sig": "MEQCIGDGONBGbjnDgzuhgVzEcfW9kyPqu+wxdw7refepckaWAiBWXUcmaajntdEbp49D67wENF/vhGTkhrEVbbX4PwMuHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeviSACRA9TVsSAnZWagAATuUP/R9zZtW6QWcQjtkjBL9V\nWdIjeqBtAIKrhvLSD9gBFBga2GMluEoPMtPIc27yIPE8DanMrNsO9sa4gJhQ\nV3d8BzweNQcMYVoeYoLd0RWlotWaFoXDAy6ktI8lIFkbQNOZSsrpkcoo8VWB\nzc8KuDqkYbQH4w0YtD/UY6fmO+dju8SckTioNkYaq4w+hXHwIdeyStrti1Uy\npFZydoUbLc7fSazyigUHRADb7et+vJaXoxzOrM0AivUkHDrAGAsJjVk92JLB\nt4FwJNYJEgG/IVMPTNM94N23xTygYkWCOL7l+DnY2PMiuwuiF2xgrdLNNl7h\nt88Reja2OAeRmuyYm1aUceyK8JzLoNcJ3TNKBuqVU7a1ZW1YDN8t3wdj7aIh\nX8zT8M3wVRupoh5EhiHonppI7LIkscaNvSQStUz8pBh8LzQvVg/Uz8HAPBKi\nIW8YyqTSdANdlM8qAol8S7fIkMgDHghk8QKWy3WQzagfKoXcyNIY4nUHK/mW\nK2gz6QwzTXPpI16ZaeMWCg1KQjZXz2cpQ8EoUDtgYup50M0sIjsjJo+8J5Nf\npllcM+WKNGqkwtg7m25criPT6GJVt/ILCnK60VrdOP0OzChMjcLhjlsEBNd9\nY2xHF/nDT2sj2sHno3yBZN6UotJ0ukH/oKXObeQkecCpT2nPHyKQ6FPlcmug\nzcKA\r\n=InJ6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.cjs", "type": "module", "module": "dist/lexer.js", "gitHead": "76c8ad99ebdba83fce64445fd247b3810c07d138", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.js | terser -o dist/lexer.cjs", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "12.16.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.1.4_1589519488062_0.6554376800257853", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "cjs-module-lexer", "version": "0.2.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "d288a35c25b17ab51fe6e90ab29829f704f5910f", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.2.0.tgz", "fileCount": 5, "integrity": "sha512-D5NBjO3UUKZ60D9nKI1XW1UlUQNeIheObJ6bD6+zmhXUi0BZz/+Ds6ZU29y8UGfHCw6JVfonn2FlfCnal72kWg==", "signatures": [{"sig": "MEYCIQDmzVKAPhvpWUGSlgxjQ0/2iUDE+Hw1wrv9YXuERtnn4gIhAK/GCTjanM9wO28bsIqi+IS7zWRWolqQthcSRs+UTYGN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevzTgCRA9TVsSAnZWagAAY4QP/0JnvAZmnrRozNyxJzU1\nW1Dt53Al+6KlaUrfvjEcCPln3xRVB4thEMc2ugfmV1SIT1seYfyoOfQHj62R\nOQhn2Rm1+D0Q7Irxsmu5FB0BTJogm0BA350tNzkhD38qjPPXcZ+mZ4Uz3xWd\nmJfIn0KlFU4XEDavvdXrgZiC4NDI2VTQAlkrzOEvlfBDQcGvEChlKlUZsYPZ\nxMRX3fFXRpNcPxFnkbOwk8Yztmv49XaiP7HWxrVJ/zH6NSgSQHLpV3glSHLW\nrcGXoyWlpD9D/aIGPC8bOOW2Lb8gMFTYlp9lc/9d71w3NzlkkV3ab/6IjjEs\nGhY6ONnvQ0OS8nf5Myj6rJNRd/bXOMmLi0WcK+W4hNcOXDzMNZX6i0Q5EsIN\nimv3gPCfYLyYJrZYJ+P3MI74lQRjMPhwNv07N1tKXp8G7w7NDQ46/aNj2EiF\nKRFTfKCB96iBnBvSGPtGUujwwzxZ8HG0gOHS0uP7yIihkUsHaNmTdW3nlHLP\ncI4WKGv6WhjNMzMf39FQh5+blQekKebxOjihSF/iOQcg4YEsPLN/5+RqybQQ\ng5zK78BMLdpw/K2S0AG20K57jhPKw++DCYxx+0RKjTZlcx0RDJnTZtNrEVq6\nKjiDv/6+XuosAzfgzqw/r3KNPkSYxsXRZ+FXKAIGQO9WMYaX2qkdf+lyPs0M\n++xx\r\n=d1A3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.js", "module": "dist/lexer.mjs", "gitHead": "708bdaea20f159f30342d515b545776dd9e1cacb", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "12.16.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.2.0_1589589216022_0.014526623779237191", "host": "s3://npm-registry-packages"}}, "0.2.1": {"name": "cjs-module-lexer", "version": "0.2.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "85d1f3ae526db13f10b4ce82dd0ba994c792a64c", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.2.1.tgz", "fileCount": 5, "integrity": "sha512-5fujADOB1OVQv2MvOf7t4a+/nwc/p6gziGI4n7U0Jc9+WBotrZZE948L/zgkhg63Ch4z+0KlwKW7PcR9nLUG3A==", "signatures": [{"sig": "MEQCIF3bDOfzUWXaGZTWRA5WWsu7yKfaxOUxXrFHvzB5AHplAiBI828EfdBCvsVkLruRGXLdqXHOf//M9MsmIBQIw8jfjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJev0LACRA9TVsSAnZWagAAWFkP/11/NRWj95eQzDNmFmZd\nZ5EKuVDgG+7yvivbK9YZaxjCcWPJfAgd+eWjFXpHAuQnVanvRLwjJudZGplt\nFxuadIoG5qlmOu4O4d0JPOzpnh7MrLRc9+3UKAgwE9K1Bp743QueGiZ7Yf5D\nGIVqbNlB8wWIQXKOSsSxMSOAM8MYUo0KVads6oQOavXRioe6WBuQ/NlMWr7J\n8c9eWFq9yuk5bAkJPqqsoR/RbCgwO6B8TADBMTktTGmzblculArs8bLGD5Df\nLaFPlUdechm3MiMVm8Phx8e5h5EwB9ez6CwawxiJLiUnw33JTD21lovvxFwn\nkhqld1tolZDYZF9v6KvADT1Czm30DKFE9mz9kTEZAPFGATjp/QtZ8G8m8yZK\n5aFuc/VK/GlDBs+jt1butfai78aURXVzCHLfD3tkzsZ7wNjnrwaQE6uPXGbO\nmLQY6iVjRVhsBbutHARAjwrK/36M4+mk7Gm16Rt4eRMKuTKJVpIG8EUdyiJE\nMfsLPeyBiY+66hNTP4hFJKcyTiMxtHNlOb4yVeKzpV/ZYLbvsHZhyWGPWAI5\nyakCrc8cCOidTGvBPNj5jO4QQXB7FHXoYNkiR3W9MXwnAeudql5XdelIYhzR\nHIVFyEMr4JJlXWKkuLiv8Bj0qwCMM1JY+ujCqAZAGssp0GOShutoGgC9miUK\nqQRq\r\n=qHrZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.js", "module": "dist/lexer.mjs", "gitHead": "e836294f548b74a5c0e46193e1d86312cd7c4ea0", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "12.16.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.2.1_1589592767917_0.6330824532916965", "host": "s3://npm-registry-packages"}}, "0.2.2": {"name": "cjs-module-lexer", "version": "0.2.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "72fa13961a037ca1f00ded6d34d56281bc1ff7ba", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.2.2.tgz", "fileCount": 5, "integrity": "sha512-xOPP8xNkt0A0d9DeLisfEdNcTW0ZZGyWJ4+IIyRhvqEj0xrd6FaWNiQCo0ARnev1lanscofekEuS5RvF36I4gQ==", "signatures": [{"sig": "MEUCIQCBEWa1R2l+nY6XBQwcniRcWcRZLUZ6rBX/PRq99l0E9AIgSDBi7VZQuQ1G6VBrdAgsXGqm5LS8rs1DFt0jEObeutA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52134, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJev0M7CRA9TVsSAnZWagAAn+0P/10FMQtch/YiGxmg2q+b\nk7ABuZBVeaLXFChxbpWoAr/y1U3246zGcYcwzKj/y9XLS51zzsZj8x8ASLbJ\n7HCei5eb86SQzzV9YOcpSb+20xEbJ935OwuhyZzqO8CBqNZQAdbnjjyJ5osZ\n/X+/AqrW5VlvmFF2e32C6tJzzO9xZ+InQY7Bta18FoQTPaL/2mf6wr5XSR/B\nyX3RzlogruydBfAKak6QHZFMVsBfy63Iw4toaNTFwHLPoES2cUfACCY2G4GG\nRHWDwZRcHN6j5Nd2OmckFvhvvziS4vopXyImWsMFczthEdBj6CWAl69SLL7W\n0JztK9Dt7Xu6UwUHsvUuIHYw/rgs9Er9laEk3c00TdqpStWpdTUpJCHtu0Cg\nPARbmWXEpGZQgSYshqtb5Y0VRihXv13J9wyFXt1W0gQf0W9pIz142VcjkyzH\nUUeHySQZc1lZ0wbEc5eQ+gt5mu3svjlZkXt4AxzL4eAvL5d96WTyXmKSC+Tc\n1+rouLSLsA9mIk3n1vCfdqY8WpIlkCTiNRG7aNEWgOTEj+IUNZ/lTc71IY5R\nLs/MrkQ88ne72ok1upKc2lQWmK9Ol7kAm6pRYdlVSZym4sbtxb5uWekyJRBX\nize2B5CBIqTymV54QB1rfaAKqed6leaQGM/6kl1clXILQFDV+9JE+pD8eH18\nVMkq\r\n=K1Vk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.js", "module": "dist/lexer.mjs", "gitHead": "7a04e2437c602ce66a023fa7d736dd979e3c5555", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "12.16.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.2.2_1589592891126_0.5802981205425355", "host": "s3://npm-registry-packages"}}, "0.2.3": {"name": "cjs-module-lexer", "version": "0.2.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "998a375ebaa5ffc2fb3dc1a6526ccb68d5fd1322", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.2.3.tgz", "fileCount": 5, "integrity": "sha512-Y0EhOo/99idux8SyX6FWOzjNIf3uJ3zz4Dhr3xBdyuhj68LAxlE4yaL36mq+1NlZ3IwLDZ8aPohhHdHyWXIRnQ==", "signatures": [{"sig": "MEQCIDYwY9VnHtEK5Jv+eAzeZ4VbR+VhEnZ3kZjqdlzzqI2JAiAJYeX3dMHYpjEsha+eIdwV5pEalrDtkCjVlbpCOJQYvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJewPcICRA9TVsSAnZWagAAPmgP/17RHIo9XUxNEphUGlcd\nqiRPLELu16V08KMl0DPc3KzrKlgScl6A5yjBvDcwotLb6Bb8QnOmZJNfMbpa\nc8rmJvbFP8a1coHyn9/kuU/llTGKbUEqkWtOWOGAYyo5SpKy6ftR0JKjpoAn\nEbSTgEXvErjPlA0qM4q2z8MD8UjrdwI1KYV0HOH9zLAjZZH+0nB2sZXgbb5i\nVRshs68ViDJdPN+LTNp3nERy+UcLuN28Q5TmZc+Z/XsWUN2WWsvlsMmxczNW\nxyRygDoJBLEnLCkiMT0T5uDuCIvDzWXL6CEi/6w/PnKCvXYKFLyS0NXvWN7Y\nJi/Z1wutNBVNdT9uRJHOCdB68vUda2O3hTFxB+DA3Gns9KeS3NE1CANYiufX\nUUVD5MmZiqy8986QRWK6v4zkNwcdmp8rIS6ou1gnbOMtoyR+URkv5IIQd70k\nyOEJGOTpAi5m3M1bLHUbGN04/7Qyhzs/wFwdo/6DCwHWrduWHQcKIDIfQSNG\nlOZHjNID+4VZGaTL75bgKaPE+JD5Q9AyYDHEjgvklb0O1N0e61Jf1khDruUE\nIvVgb3TmfdeNUNVKhZGbMjW5ndTwDEK+z0A6mEgvsqGFJiMTh9eUNvrKzz7s\n0+ih8UaJGiK8ILt5dvUqAPQmBfpxAznvxFSlYIcdX+bjvtwyvDB6pw6E1kyU\nvoMZ\r\n=NXZ2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.js", "module": "dist/lexer.mjs", "gitHead": "b890acf6614f0a6256d256eea98abfce6aefdf5e", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "12.16.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.2.3_1589704456340_0.8712411940542812", "host": "s3://npm-registry-packages"}}, "0.2.4": {"name": "cjs-module-lexer", "version": "0.2.4", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.2.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "df16401dd94a9b0e2b1eb0248c0bfa37685c596c", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.2.4.tgz", "fileCount": 5, "integrity": "sha512-UFBf6SIKOi1FFwUPCotrlK8qgo4+zLswS2zrV7ZvilII6+3DJUfXuQtu9bBZONcqWDeRIpnT1kwAMOuEe96Dow==", "signatures": [{"sig": "MEQCIBome9tPwkZBRgA/vYrcDik5xfgXc2Y5emlQMC6Z9FudAiBPPfx2cjPILRpwDWn9tFL2P0DGZ5KzjU4gQLs/TPHMTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJew2GPCRA9TVsSAnZWagAAzlIP/RMh5uvBeIwg2yWG0qnU\n+JEbslmFmKs/7risxkpnRiX7Ol8Gee+w0deQFxuLKK+0SMxTDAQMyAsEW4Ie\nRU5gigbR1HBBedfbegt3VA/hTM+hIrOZ4lyiqIbpOLWxqGuXTky/m3ztSPH9\ntyw8iUci8E63+6mnFG9qrj6tXKY68P0OL6vZdN67ffjfWWamkb4fliecsX7W\nKvDIFqZr9eJds8fmU2ArfX56DPifafLtAn2lVruou/REI9X5fs5XPn8VVCr6\nm+LTdcg3Ag/vPKqfh0LYGVv8x4f6mBBUg/BMkam5VDHMGw8TsFlOhf4R7jgK\nxaQl7i5DsAQSX2do5LRVdRO/V/LV2ap79qy18mlOq0brZ9F2YEctS/pu5J8N\n3oXQaH8vmGnB0oTc9bInoeH6i01NJ28lpOp8fsns68IyLeXYsas7S8zh28Yq\nW35tUD1zK65Xa8rSRdFsBRetoS02i/YQiI+JogiCqyLJcMqBk42pWMyoIlPc\n0Wr5/I1ahYz84vmM6kwy2YsSJWIGOP8Geiu8Ff5XGCeLrRGm18jVztT10Ksr\nSk7Wwc+bYRiB42dj+PiivAoRVAFfjA1QfeJsj+dX3EzOfjOPXTKnly+5juUe\nyzHwa+azzlakWLsqJS0l90bT8bQCsF80YwzM7Tm03XFrabuy+8+DPEwy+t7a\nAKwX\r\n=h5mZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.js", "module": "dist/lexer.mjs", "gitHead": "65d6743ec903d3b411a7d0f2b1ff708ca6ede19e", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "12.16.3", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.2.4_1589862798632_0.2666174156480876", "host": "s3://npm-registry-packages"}}, "0.2.5": {"name": "cjs-module-lexer", "version": "0.2.5", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.2.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "bc20a5a222342aebc9736d811d5b9fd132a82a2a", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.2.5.tgz", "fileCount": 5, "integrity": "sha512-VHRRN/99QIBQpjO/Dmf3VZGT1rz4s/NzmaokVGIuI5bXjAlnsFuy9WuovMAdnifW9i8lzUrO2ADwXjPgmdPLxg==", "signatures": [{"sig": "MEQCIEAbYRvCpHXMmrauNqLhGHoZQzo8vXV4OaSjnPwCVnctAiBpZinLfKMD/kdmzW/NyM+v9GcqDO4fmDSfmqw06dOoIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53711, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe0IKHCRA9TVsSAnZWagAA2/IP/1NYKuORFPuuBsT5bGTd\n/J5QP3cJ5R3d+L4MNPo1j5JHMFzZyfdtest06N3G2ZHAKHeYtyu0R2n3bKKs\nJda0jYtL2zmxq7nXwo53ICyWii+cnXqLw2eUhpE1WXtUXnMTCjtQn6wrx58v\nKSa7F1u6/c969zExeikZZYbJHCpMx4pXxoVGae2TMzFEmKiunGngwm87Kd+Z\ntSgVqhQSi1Ai1hel0Lk0pVCVC1uq+Ubl+MQ8OQoyOykWrRMfATYOFIGvEerG\nFRI8qjQTYSwhxnsHtPTD+G2nJdH9452wJ2Xf52USEVGlMIfm2pv6T4/T9FJ0\nqE0RTz9rQew/ABdIkDmmAqLOFfz3zgRGqd1dldIHWZQvpMPT+HfzOxLs30ey\na9HtHodRVEhLIzgkW+OSI+MQ8tG86Z2BdxpNqsKF7q+H6mrBRNjPvDb34F58\norZAXmzVpuqN6+EmWhtUampuNaChoshRIAbfQ0Qjwq1D13/vA6OTAn1VJ+IM\n7fm8aOoIMF31X3TKp5jZIV3drxQJBcH1wOKeMwFU0xlIMTcclUTHNlIW1o0w\nTUNQtnxOJvyhmZYvA+K1Sy/eRUNb8AanO9ItmztGOp/dzpr4BfZPF6PrQwBq\nIo5zfQhWHkXsWzh9uONmTk0x6EwIhbSuavMIesLgJ069Oap6ma5D5GoK0AlN\nP5KU\r\n=zUSy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.js", "module": "dist/lexer.mjs", "gitHead": "c05d6b0ac9c2505f2b48902cd2512c5d5cf50127", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "12.16.3", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.2.5_1590723207085_0.6384862534812106", "host": "s3://npm-registry-packages"}}, "0.2.6": {"name": "cjs-module-lexer", "version": "0.2.6", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.2.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "105e932024e9df12c5f165c9f09c027e99d1f472", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.2.6.tgz", "fileCount": 5, "integrity": "sha512-rGKjCXHXvNpD0cEliCdV3wdc1P/ETWvIdUImS1i66b3Oq0s0cJHl/zsYSey8P8WWvtRTFtkl+3se8B1uIBCH3A==", "signatures": [{"sig": "MEQCIEoTwFM593FTFiMV1tyE4JgvzBPB4Y31U9AszYnwsJx7AiAh0CD6/LKcU3Rvhc20ACfwgo/cndniHNrbRlIFBifRcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53807, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe0IPhCRA9TVsSAnZWagAAuLwP/3Dfa2TeHuyKnWZDO0iW\nDWofnO+beroRWClfh1BaslP3BXIE/zFMO3EwQ4IvZ1wN0aNiSNOjhZIQ20tI\n/+Y3m/aBGTkXj9POq9PLgazacp2YdnLPSJo1FPPVBezAmhylLH4/crxqKRRa\nORzSjD1xN4onxZ76HklfbhDRKBe3IgSRDHdTDVBufDF5uz4bzE5cg+g8xIyf\ndRrwUlS7+YKiwUwh2XnWd3C9pGFBIrxvRPO3dhMiSBY81pWzjJENB/WC764N\nvda/F4jMkoJ7XIzvqnO7j31/pasyKXnvK06HmaG2vBrAMABa+uHafPHsnLhw\nBFZaoc9oBAS6F5CGgDy7+5qXliPBh9WU5UxW51iJFsdTwaErPntpxjabLL0N\nl8p/ehjzmYJgAaunX8bYu9486wm39cJP3UGJ6DwJrPkJlTfEL4B1qmwNGSOW\ng9oX5Vb2CxOCbAHszOMmIUy7cebxpqneBnzRJmGuhNRXBFiszjE28DCmS240\n1R09lbVak382j1uHUCF9kxFv/qelm82NjoLkpD6G9ZYsgmU43FVzX4NxWaPV\ntRFaHEPN+CfXvbF3DiL5OA7tXqlzGmv8TyEW3SlfHvzorVdCDxqD+OCmK8x6\nqylTw650ZnOzZE3DlPk1Weeyc4Ha3BZGpCQ9H5yKbS00IwGU9FOXDDiTIK4g\nFfGo\r\n=qt0T\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.js", "module": "dist/lexer.mjs", "gitHead": "3904b352e4fdddafcf227bdab4b2bc27c824978b", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "12.16.3", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.2.6_1590723553393_0.17827609033357672", "host": "s3://npm-registry-packages"}}, "0.2.7": {"name": "cjs-module-lexer", "version": "0.2.7", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.2.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "35a4546bd780eb2055a584b4ccae738b74accbdc", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.2.7.tgz", "fileCount": 5, "integrity": "sha512-r7FmHKhkFAI9/N48q9Kwqajpe3m10oxWvczNLmnLZvP5+cAaSnPypM56AvxeKhT7kN52NF/4UFOf4hbkbFtixg==", "signatures": [{"sig": "MEUCIG/NGFkwHTRVM2XxHH3FFTi1MBWc+1wZ2frv7pMFPIj0AiEArG1z/IeVoLhRR+6vDIC0D2IX34LLnTKp0Igja6BQrfs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53943, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe42ryCRA9TVsSAnZWagAAeJ8QAI3jLz2++VceHnuLUV4j\nIVjwpBzEKYfbXa1M6YeMAbSCAdAalq2iAd8z4/HgMW6mMagI2uh3J+JcS0X7\ndjRD4TXVGCus0fWD991v06M434Rup1CJummq3MTjRinJgbkcnhf4Osziew2b\nr0WwQXnOmxNbao95kMJkU9VePAhsNPn5Jo1VYVnkz88T1r8qOGp5G62wKtCl\nnnpr7+lkAUXf+3e7EtJAujiWukrudAGjGrMt9KUm/kJ0Gw8096ZAX20R2E0W\nXOiKZKDaJf0d7Xr0BDZwSWxx+dBPTI/kL4Eo89rFA6Nyc39xnY2AXFbf3fBV\nGiGSyiKCLSjxSp5cpdkhiCu7acCPU+2V7XiHFaZZzH9AbAy1T9IUDY2hlIF7\nBifqAeTkvwyHZZvDPOg6zzyPNMB67EhlXpG+OhUDMz/ZMCu7LWeRF/C1cRhp\nDtNGMgwiRK1p52JJUbg/8FQrtwfpXzmcG4UrAWh7Qi2ctHPaqCDFMQD1fnH1\nHDZiWs2ScSplRUze2zeiNsmDWaHq2bt9/VjK4f7CQW5fpzsmZUwYGu82oN9v\nubzC3CjIo75moPO7z63eHuEHvziPs2ZJcN6sCUiC0xNNeZUzYemruw1huMeS\nstUvLdG15wP11NQYMUbZIjnndYlzJi1skETXud6B5Pxm0Xtorm9izVEIG5AJ\njrfW\r\n=gy3p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.js", "module": "dist/lexer.mjs", "gitHead": "f2acf6031ad3626938f80bf4ee44b66dc8bb44e5", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.2.7_1591962353877_0.2819335636310787", "host": "s3://npm-registry-packages"}}, "0.2.8": {"name": "cjs-module-lexer", "version": "0.2.8", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.2.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "e7ed93563da94ac9bbabf9b22d40d068c0b51ad2", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.2.8.tgz", "fileCount": 5, "integrity": "sha512-A6aHoGMNuSLuAD2032xynnJ8n2hgKi4rUg0P2CrqJvVNwX8klfRw/6sH0JzunF37lpuZtBk5hpO7NEuVmUk7Jg==", "signatures": [{"sig": "MEYCIQCQImYoQxS+pAUZUgyja+KPmcJtbDqP0lyPsbFGGUnONwIhAIOgUL2TVPhWbMCpU02VmTjXbYnW4WcwBM8UNy3Q8Lys", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5JCrCRA9TVsSAnZWagAAXk8P/0cnahA3x0aG7Pa8pbQf\nYuuDirhjd2RA1ma+eX/Vc+ig/7XUvysBPq6xoX9nLQx+z/ABNjDQhAhpQOw6\nWwvbQ6qUjh4wNmrkQH/AoNqW7uvmSXue0v6qlfavq9wtGjLmqMqYZDkd9Xmu\nzNWe1FY6OW6qgPy5Ng2M9ZE2zj1W0X4jy/kvoJGn3yeppGCKJHQV/fd/b5RE\nzrDcTaZt4d8u4Dz6C2dn4VoMEDRogVc/p/xuuZMdKa7x6JPIjs+sV4+WihKt\n8HUQbgfrCZDtdnzuMjatmhYI5B8utPC+hPXwG/sLIuV+X2xYbSedDmqWMsqm\n9EkF2/zx0u0QC/S5eHeomws9cTR4IJVKvUug0ctfpZy5W/yb5kHVrAqldU3F\nnYQiE9K7wesW+QMf01pvXuOlnAs7Zyp1HzFaHe+U8b6p+U2jA8Bj5pMAZNWR\nrN+lTdX228URo3UA1mFfxKy7/z/prRrk4ww4yIkeLpepSTYeOjdjk7f5X7CW\nxdVv+3ECHwZYROpIfPqoKLnAel0XRSPw+VHXFl3nwDBS8BT0toxyE58X28TC\naW1TXsQxBcfXv9YTTP6mf+NVbA+xF1/J5bcDo0FVO2/HWssel4PD33zbgQWd\nVZtGvnAcGfFqMATLTJkpjqe0YeA7z6NshOhGOdRZAgVbR88tQuw7rbnw665+\nXCBP\r\n=xGa4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.js", "module": "dist/lexer.mjs", "gitHead": "06bfdb03e739312209c8ddeeaf423b60e33ad790", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.2.8_1592037547104_0.06489106851381221", "host": "s3://npm-registry-packages"}}, "0.2.9": {"name": "cjs-module-lexer", "version": "0.2.9", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.2.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "6ad13b4ba4c681762389df818c725369816606a6", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.2.9.tgz", "fileCount": 5, "integrity": "sha512-bGKFkStkdIj9J6VlzCYKZ0hA/SUZucHykjxGHoK5s0OISN0pkLcvqFyZwLqKcpqD+RYMfjYJ+/zv5PzAKhVSwA==", "signatures": [{"sig": "MEUCIQDPb1HKtmT+IUKg9wWQpUAxBhVuHW2vo8RDhg/hRHgvYAIgQvInvwmpW9/y79KrPTCiJwj7HDNvage94UEiR7u+Pbo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54223, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5X8dCRA9TVsSAnZWagAAfP4P/1QWIQe/DBlkyUgBEVeN\nvIgPndHzd9zQ0OU/NC4sSvLQy0HxqF5CDr/3mXP6pZHeT0SJWULYh8yPzPp+\nW5mPx75w+heRrNKw41RIXsspMGq6W1xPqaHcxskYlgSiW7V2aUMZpBai6Szv\nHrecvsa9SgqBp17QoA1+aYypoOqADks5SLwZQTi2Vq4CO/RDzAI/O3DEHJEe\nLZRuU6IISsmWm7HxOdltVZzftfLSoueKCAupCuXoUJsZWgNBPHFk7V7Z1goZ\nwuAgfWNLBLFyyEbx3hgXGZmq876fXp3XWLRFXY+b4gzmh340Zv3v7DLLmJ1P\nnHx+uZJ/XQpH8Tfz+/rnImq35ooji4UTxZMa8PFbcusxxQVQ7Ivy/m8GJ0k1\nA7wBmtwXo44As0ZedIeJnd+wWCwa2Mefiw71nBhJDRtu1oYbVHgtUyBgH7ta\nDOPkfmhMOxCSILOuBiI7YeNE8eDApSssE7GuYn9dqCdcJHqixOmOGMru3Pih\nPPWMjfNc2HXFtMJx4UJJ+FwWzghc4wJcQ6zAbqFtKKazM8JGgDKiu2xURbwU\nm5L2dnGA9U0SxNB/afL5Q1ldahrVErcbHiA35u/y8j53hxaANwdDWVHuUnOQ\nUW0KWULI7LnUmqiVdQxPoILF39iqKscBSWll1NqUAfAn2j2BA3KIbLYK+8W+\nbhKm\r\n=PSZ9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.js", "module": "dist/lexer.mjs", "gitHead": "0f16e02fe454eefff55a9e15ac7556840dd6ae0a", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.2.9_1592098589018_0.7031524223903256", "host": "s3://npm-registry-packages"}}, "0.2.10": {"name": "cjs-module-lexer", "version": "0.2.10", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.2.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "1a842d5adb5438468567255102765001f1c64719", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.2.10.tgz", "fileCount": 5, "integrity": "sha512-j8ICkYt5QX5pvgjZbt+BElzqYiGToRO3DIsYmdGSCoKaKn2AJUEEi17Zp8f03kkl6q9L6dl/4p+t4+oznzk0Qw==", "signatures": [{"sig": "MEYCIQCGlL+XGeQ0SQtulmoN++lYa6ZT76h6HGY99oAtQFBSfQIhALytBEMbfioGZsa2Bnznj4TrH8KqN9C33qo6wYbzE2JF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54743, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5sxVCRA9TVsSAnZWagAAifkP/3IBxRZru2XJordHWOh0\n+9DuC3sJLdjorxq6aGt3cXSggvfRGTyLl051p3vs08BQH8/VAqgMznD5Q7bv\nxcoHAWyjbOQXJC5bwM761qRq8vD/5vbyvI9Y7PMV3m0LGKfRYPS9EYg8UIH1\nJqqWtwKWvQto1Wb6oy3T4Wa7MzJ3c4JxfmgWNuM38QXpckxDDvLg9rJc67/1\nCihAB6CAVX9eYjt2mlV+QRGYfcLZgsUr90kYb4euWwxRdgwod+c1duFWICDD\nUmrcJGOem1cg+JZSnRJz/73O03+r0sf1bivrg1fsJ4SBNNVUiOiifQRoyB9D\n3J/CyJzyigzO9ggDDVwXVBY3DQyX5GT46oyo7muAW/zZVyhQm4QQ4gHwsmq6\ne/62lOo7r0h1dSbywhfenwgpV5IjUqx1fSzYx3hJ8eHmGjSdlD96pJ5TxkoX\nhBPMoKqaOOIGchQpzlD78JhMeYqDLoxGge+ERN/eV3Gcr21aoFQhmYd3iCyq\nptTqw3nMY2ASjeEHfPkWgC7d8VjBXfETJLnqjU/ftIRADalPnyCVjh1Bo2uH\n9CbRhe27VGgRnLpO1XRv9vQqIt8hv1SaEt1F9Ffct1LlZdn87cTDiBItkqmG\nYhMhJYlvB0sJyqjSWHNx15T2VB8GTXMVRDyjZYA70zEwPS8V0+NHvLaE9I/Z\nV+OO\r\n=Ghaz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.js", "module": "dist/lexer.mjs", "gitHead": "a6565697a4954494be1ce094912fc81a38b36daf", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.2.10_1592183893209_0.5021664162969084", "host": "s3://npm-registry-packages"}}, "0.2.11": {"name": "cjs-module-lexer", "version": "0.2.11", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.2.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "1994124fc48c1c269202533d9670046f4c6c936f", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.2.11.tgz", "fileCount": 5, "integrity": "sha512-8Z3VtyNfWpDcMvDEDmvFyrVGR+4z2yGT/UMPV1mlcoSP9dnA9TPli1lsonueS5gS7oEQ5lIMLPyhHQQs6hIY9w==", "signatures": [{"sig": "MEQCICVza9yHBHNcuYB9oTz00QDut2V5ZXdmAVCfbBUsV+R9AiAzZiLlx4+t1bc18CMuYswiV64Fr3Zct0UqtdWMz0Ny8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53147, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe9/LYCRA9TVsSAnZWagAAASwQAJHDG6qiwHGuDLYjB7P+\ncmnPXGnWNRMsrOtU6iQDdN5G5aLWBfWaW+menNHr/BLc/mSW0xJiZFfpABZk\ndO5rAbiT5j6SxftnVrMUcC5Qbnx3KffdasEd6/nZPZn0PvzgrFE3yoHMnE0L\ntiwU41L7qzbrdofmtVZgSU9RxnZp/rF2iOa21YqsIswDmyVzz4NW7WAc3ARF\nCuhvtZBrzz8+TltUPwLNXn/OicTeX7n7x1ZsOx7R/G65lMXbtA8EbLhX+nsb\niBycyC7ywYnDt6WCQ9sEvUyJ6BOMKiZgfzV+8nB36jHADDCz/ICIDeY5Rk7I\nbeEa2k/uxemhsONwzchCBPCOys3lxGs9jd1ekK3ehTEO78kzN9yHKl6YDaRC\nIn+iFDLCSRftlyvK9hb2Wg92ksGJLGKZzYxRf/FNP9Pqbg2RzFtUBVq2mihy\nC+tfMZoprgTiG7VJ+rA2QooxApt9vo9PA1ng6TUtagSB4WN2ioJi3qTZt97Q\ndZvK75sqj8vefjG57p/EMgugbDPJDXCxT9cUHxxTP4JJzEncmZ7s423JBiTh\nAISf+IWhLpWyTDxQFB52F8qqQISOCH2vP/XjtytI6Lgog7IhlRdNqRFH32pr\nWzSpZI0GP4NNEuDcYzCkUX58eGPltoin4EIbJakMLzmSoB1jgj3FpDyKj1i0\nNGMj\r\n=XvVe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.js", "module": "dist/lexer.mjs", "gitHead": "480a099ad911190a5675c524afaa6bc422d2ed02", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "12.18.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.2.11_1593307864115_0.4154059813065101", "host": "s3://npm-registry-packages"}}, "0.2.12": {"name": "cjs-module-lexer", "version": "0.2.12", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.2.12", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "f7e8fe5ceed35e92d33e8b28bbf2ed390725f3ab", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.2.12.tgz", "fileCount": 5, "integrity": "sha512-dCHkd6cc45OFYhZS4Oj598SdIpnLfbyR/wesguT0xzwRPKsW10FScy+f+UW4UEFj0kL8VlWSuQwpXezRqwMOnw==", "signatures": [{"sig": "MEUCIQCU+/ceY2cA37GsuqdDk/vf1maMlgajILRFB64yijBEHgIgUPkL8LMZCH/NHNRlC5FDvL7y7VJQZfAieA4uuQrMKDk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTsggCRA9TVsSAnZWagAAA30P/iYqp/diHJegj2+iCSOZ\nxitAU519hrT2m8WXnkSkbii5Uz7fuNKP86nXS/tdAdVdvX7oMyH9SixKjFwB\n11nT14R32F05cR9YHZ+RKYPZo9WSCpqD7t7BWQyEqRkg6sSKH/DPWTYCvIZP\niUHMrzHkgkA6YKkus5oTGNtxIHCG8AdkHIO9ZfA5Lx48/Q/KD1XyViWfAw6S\nsSka0SiAXjm5hCeE/nTEW8W0oZGRLhnNhvPwW9AaxvRO4x5btJ/32Bq4FSCH\nFKgtctWZCzqvxSNG4pc4c8TDVZ/a4pLbM0Q0dnArEX0aWHFv9/JukYcCXjy3\ntLUyD6PgDxR4vI8j/sIDPKJrRtO/J+1edcRIQzPMXq6wFvVCiuEiNq254Dd3\nB0RRjB1I3Jksqq32CSMM05j231zuVd2O+syoNJTqwKaD2opVrrq1fqNj8zOT\naE1vjLQcpt68iNtu1BEKM03fKaNJBlsw5hcrKmZu+mM1vmMiDWCls1F7AqcJ\n28FC0hYKQQTv/oTU2MDyUVXYFQO9mq85nyokaffVioEAJ3EwtlTBP+ZujNMi\n1Bw3y1UXB/Ja58v69+Wmye8q7gbqf9CSacwwQCtQEX8Yv6Uz2aXEx7sQDhaL\nTnuhG1HbIo4/51A8kIKYF4becGynpqsjDynBmgm14NWg5hfSh1ZDLRZQS4YM\n7vcq\r\n=+AXi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lexer.js", "module": "dist/lexer.mjs", "gitHead": "c062e552107c02dd88df0e577fe92669d523590e", "scripts": {"test": "NODE_OPTIONS=\"--experimental-modules\" mocha -b -u tdd test/*.cjs", "bench": "node --experimental-modules --expose-gc bench/index.js", "build": "node --experimental-modules build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.6", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "12.18.3", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.2.12_1598998559556_0.7257892288264285", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "cjs-module-lexer", "version": "0.3.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "9766c5cfe7d46652143310f608ffd0394926c73b", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.3.0.tgz", "fileCount": 6, "integrity": "sha512-Lsmb3kaxnEzUEkM57tAacA9RHXC/1QXzRJhjP098bbN3W3CAahvlO0bykxhC7ZKd3cVowQfSQzY3XIv7nOfkpA==", "signatures": [{"sig": "MEUCIEDn27acFI4JJY3+FtVDAeIqxmEFp9wiHv5cvX24CtZEAiEAorASNC5whLkiFBFincoDUi6nE5rLoF5g17r/U/VfYz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfbaHRCRA9TVsSAnZWagAARLQP/AtMdYYYzf2AWVkKgvHo\nwA26NDo9eQoCqZvfn92/2IN6Ro3SnGAjoGLK6zZPSdh+WPUtSzWNIyFitrpf\nhAErnwCp7eTh87uSG1St5PbcHBFPjvO6l7aeXwRE2OiI2E+6CTsEIo8Vqymy\nkk/z54ECsu6b63MsV+7GEAUQQRy/Rr88ot1ZuN56j7y2YIz6i0BuJVybx8Aa\nBpeIWd9ybrEgAZva4dGktiY51cPEsKirgUSUe0OcHRi6XT/EOJp9oXqGx4ll\nkdeo0ZINnwxNeXeg0D2tjXIAwpLCoxC7j3TTNxyHqyyjjXvkJLU7m848niEA\ng5F8AGZl78PbcsIoCRmSEZ9J+4nES71Z/Imgo8HIaeaAd9DfdPUQQNBtm/eT\neU0Ji9NLZhutDifrq9J+DCIUOjIXwvuYVt2hwy9gaXmaEx6VBJcbPcJYTQPO\nmR7NZZi7PApnLsN9B7xiexXC1GkflZzEnkdKkTBkROkn2+tlLxltDu3GzP87\nHvdL/Tv3GabWju8c5PVgyOwCkAQlPUT7eNiLw/tjJt0XlgyQVV3s4pLQBnX3\naevD2H66r9GOG9p8IbZaQyvuz6My6KgMeRWKhzHlJnMh6tEKIN5dSTxxQ/m2\nCfamfEgSEh0YM10rypNFAhvaHy4Qrqv8U0D+UflFN2RcIaPmVJnn+GFPmtkK\nqvnx\r\n=XgNM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lexer.js", "exports": {"import": "./dist/lexer.mjs", "default": "./lexer.js"}, "gitHead": "200bccd28ced104502af02c4aa10828b2a23c003", "scripts": {"test": "npm run test-wasm && npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "14.10.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.3.0_1601020368646_0.15949722111344355", "host": "s3://npm-registry-packages"}}, "0.3.1": {"name": "cjs-module-lexer", "version": "0.3.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "9dc38f56fe33614d72b5def28d0069752ae7e256", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.3.1.tgz", "fileCount": 6, "integrity": "sha512-2N8xlHz+neau4tRqc+lDKZ+wFFzCNIGxm/b2wuBMQHj/g6kNOrlEg/rmd6gFAB0oOaz8MXqzUMkZCYseyFByJA==", "signatures": [{"sig": "MEUCIGB0AZ8lvQwwwnC2nmO20mnKBxnwIQv6wVcqjstaEEovAiEAmP7q/KX9b6S3wBkNW3/Vw4Id9jPch+zjRS28OdVyPn8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99113, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfcAajCRA9TVsSAnZWagAAxE0P+wVAa5UPPIRsPa5wRNS0\nwQEfm9nLyMGtFB99bVCBvjo0CcYCZHPPUQ0lEGvS49slK1EOrveaCuIvE241\nprGgQX2r+Z6cL77ehB3T5qovizPwxhfNEjv94lDzNJLE7JZoxUYIz3A/KrQn\ncXMweUoHMN8tEJ7xa06rbAF6lNVldVrOzxArWKyIWqMGSJ2C6fYAIeBHJ6C9\nPteMwAAEIwdIh9xTxoP2WlXIafPbkjToxGPrBKiht4HR22FCRWMP1ougC84U\nhYECre8rExvJ9Mx/fCfea+MgxcN8ua3RmQ/q8VgNKda+gbFICs4refxagesL\nXEBJDanVBgA9UcjOEgaeyNOHU15xPaxe7mYhdBD+W7Hi88htgOBsPdCzrLXk\nZcU0OEBCCV1WZGQWqlfKuW0snfURMwyXBkfyQ/QdMkDJ2LADbOfKOX9da9gE\nYfMVQSVLci/+IOUHlLOOJ0cfLIbNmBl4vIjn4FE0yONFqd95mPMW85fbVkXb\nCjqumsb03MWrQ6wNdNz1Tb4mz+YxBFzlR41J5u6CaEtE2MS3EVlQgPlF4OsH\nuNyAQTNxZgJAvt3zvORkEbDNzft40pntb2zTWN26Y/U6DDRiGw0mD7bLI1NN\ndZ7q9+2bYmEFgPLvs2Jmsm9cOmQyAJc2Yrl7XxjwiZJU3AtWq89rwzn3puBm\nz+ER\r\n=Ulli\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lexer.js", "exports": {"import": "./dist/lexer.mjs", "default": "./lexer.js"}, "gitHead": "fa9483c8dbc3366ed276e8d4de600dd3a2e09106", "scripts": {"test": "npm run test-wasm && npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "14.10.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.3.1_1601177251318_0.38793712244523815", "host": "s3://npm-registry-packages"}}, "0.3.2": {"name": "cjs-module-lexer", "version": "0.3.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.3.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "483e53472feffbcc4439fd9461541432893203ba", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.3.2.tgz", "fileCount": 6, "integrity": "sha512-UyUuRMZReLegjQ5JFdQlrD6dutNJhqGALVXjhaEAsR1izUURCtSieuqpD3f5D6c49/A8m7bw4aWCJqSsE7ykIA==", "signatures": [{"sig": "MEYCIQDOT2Z+FSTftidXkx1Rb/5icG5Xd7RqSxym27PsH5jJngIhAIOoXCKCNu1daVsEcDd5cEisaTMnz2Hz+TQUMeOFHBqf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111370, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfcW1+CRA9TVsSAnZWagAAEp8P/1mCd3y1pH2P4fXskGMD\nFu60CKgdzMZW5W08a6PQfVPgXPQVok4NBqWxm1TfY/mYdKNet3aobWtxPrGb\n+t1KDMhhDaXzc2Lx37EFHCOrJxpERa+hQx8hCJL2AitqDBrKaKWa7iHdv1eo\nTk0YtxS4jwHz7890d5fdwqKJLFPI+aupjJG0tkeKYxq9c1AfDNwb8It9uqRB\nljMzcqUIUSrPu4lb4PGm9GcWVl500olO/+FbzH7qp78WsHsKwepI3Yi7HExd\nblB7thaWs7Xe2hrITF+iGknmoc48obnhv2B/iPrXi+iahRjyeWnkxPDC6H3B\n10g0/jX12GvjcrLV/oQUpk1ZcZkd2HN06853gy0RHxeWbMNleDUNg1RejsLw\n3LP+oxH3mGMwCEuUlgjnZuNxLRiWc/cCb05ttaeCHHaFBJ3ct9yFak9miK0C\nfhwCXX5K4KvQbxMwP1TAqr0f8wP5/UUb28GynleuscyeHFAsNqZD+BB/bBDp\npCEAz87EMmjv1HL/Aw238h6Gkhk8tD08GrPBgGhPpfCefIVV1xk5mYLjQ0ON\nqRLJA1wa95lO1XR+3WhdH1gs2zaaxO1dUAYLkeRgecpXIftMD6urbW5wem0L\nryLUBEf7+chBTOgR0i00Sbd/2vU5h66LV/xs954yPB3Yr4c8H+Q7tPd0/ccp\nZBEW\r\n=JduJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lexer.js", "exports": {"import": "./dist/lexer.mjs", "default": "./lexer.js"}, "gitHead": "42ce1cc5126bdbdeda4b9eb6a860760e3eb77685", "scripts": {"test": "npm run test-wasm && npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "14.10.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.3.2_1601269117735_0.26975391817658534", "host": "s3://npm-registry-packages"}}, "0.3.3": {"name": "cjs-module-lexer", "version": "0.3.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.3.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "40b863443a2ad8aa8d70b10afd65b7aba93a3a08", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.3.3.tgz", "fileCount": 6, "integrity": "sha512-gfhBYtBojuFFdenh8qN1IAlfuMVYPT2m1d9oYV27PgQbuNn79ixoBE3a4SkJds6iaIKYmnVT/UAifV13og2DaQ==", "signatures": [{"sig": "MEUCIQDtTMcla6M0aKD2HVEVgpsJbkIXR4sqh+iWbwn/0FfTDgIgKMIwn5Yc8VCR3PypqmyhedQL1BR0QE6TrFWSOZR5ZsQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfcXB/CRA9TVsSAnZWagAA3koQAI3XJ5Mnpv+8D/roWgU1\n7qxSmU3eRBtUwHErxM1yDQCmCjMYwPHkMNXUakKPRWDAVYBpf+MoS3n9qhhC\ntunDPQH3hz+buLo8olngtUc/DgiLYguwjQ0/wsT/DS+epk0fLvYpO8aiAz7k\n8eLMno5jw5EPykDN8UBydvi+X05Hsj+76yIwWIcbofM1p6TqJLX3mn67a/RJ\nE8jTYZXDo8+sJYImS/kemb2CfT3MYOQNsM8ofB1PNoLfXCUwRS/UoP9C48tR\nnZli+kLOwBZUvI3RiiykObxCZ1WPSUTLCI/q+JdicukWd3dVqEKoPJ2ShxTS\naGDOcJWormYCkONkbfaKdMGo1+ATOXZNlMjN5YuMviQaaYr/FtZQuwelsVHY\n2FiiBVOKQzjQAb/kICQDfwM95A4239NOLXOSuXQojebhPC525FMdJdKzcYac\nLKylcBfrAdgxB+wsbSwyFGQkebaHtR4uOl6PaA3kOosXXyhN5NUcUc16S2tO\nXx3Kmnyr5f6BWs6qfWEqHD9lo10I/cvFaC8QLK6X5WF/iaKiD+rqNz3gvMRB\n/jmp8cTX11Ak92KvIWw3AceMTcuNeUlo7GnzWEX1vplUuqZxJ78od6fBCgjq\nB/qI9kmDP6SJtlDuJxq3VLhyvWNblimtkHqCvZIK0RyCCykK32T3ltgyzNUS\n3Xox\r\n=F01H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lexer.js", "exports": {"import": "./dist/lexer.mjs", "default": "./lexer.js"}, "gitHead": "9f32d7a83a435ed6f7716318a5367a82d6480079", "scripts": {"test": "npm run test-wasm && npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "14.10.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.3.3_1601269886938_0.12619854723447044", "host": "s3://npm-registry-packages"}}, "0.4.0": {"name": "cjs-module-lexer", "version": "0.4.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "c0369ade083b167d89fb1ce9bd7222277d879952", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.4.0.tgz", "fileCount": 6, "integrity": "sha512-os3xuSZ0e9YzM+d9KIKh+gE+redb52kWzZuzNqCTc3rtpXA7gdVNRPkDiOnVcuGQMWiABGtWXkl1gCweG0Jmsw==", "signatures": [{"sig": "MEUCIQDvDHnlfMsEB1CDMNzbcBB/s/wh/dQxfsu/Cf/mb2G2dAIgIviEnzUlYnlVl8ueQm1/qLqV9KmUMGvmTOXtr2N/ALU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112086, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfed8qCRA9TVsSAnZWagAAH+kP/3f4YRUcpvW/6A73/a7b\noJOuhuAQf18bXSJLkoJWD1ayC+SOMc/SESD6noWozlonpOnCLwTszUYERVoK\ntB9efalZCoMUYSp4/Jdp7FIDbD+g8jTFlM6V+d3aVhmXSQnX/US5f1rGUYmU\nx7eVpdc5FQ7744pGDfBsIH3ioVNTxDqgByUJxjsm2GXhMtvtlO7bCKASygOp\nVNtIgvuPZ16VbDp6RRvHXlmsB7iF8dJTD1VPXBenUgqXPB/g6PrrVwYF4msv\n3yBLNvDbraW4xsC2Hqe3IAxFfhVIYNMs44izZ+/abBhYT2ezIA2fLsZMLmGO\nroseIckhizeRlIUfVpxnIr4jKy0ZYw891pIpyQm8IG98hNb8lHKzPLzKD/qP\n4LvAraXMEbModCEnezrpobO9mKT0NNBiQMcJNDn+Yhs+50bdjfdF34bFhc3Z\nI94JsK9OeNmmEBU8N5b8lupK+P7XbOSeyoSdmllRg5l3V4jzC8UwqlpndJyQ\nCqoex8ItETXCz74fynZh59Z4cAAu/dTOVJkNGI/BOYSQIaFq2vVtPWg2GdMy\nboFbvFwOJRLGucqCr7tPVzBxQK/QkPnzt2A3/KWIliA0OflYwaswV9bKtrM2\nX7owm/IuIqHDspMdrB54CJqYZ00ibj2SIpR7As+6UF9PZoE8UO05PqVZCXGS\nf5nq\r\n=1gFJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lexer.js", "exports": {"import": "./dist/lexer.mjs", "default": "./lexer.js"}, "gitHead": "10ef34d01300c1a0b56847006f3e2e157abaaf31", "scripts": {"test": "npm run test-wasm && npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "14.10.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.4.0_1601822506117_0.686287049807019", "host": "s3://npm-registry-packages"}}, "0.4.1": {"name": "cjs-module-lexer", "version": "0.4.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "5b3ebc507a0baf5681a7fcdf9fb5509d09c84ae8", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.4.1.tgz", "fileCount": 6, "integrity": "sha512-bUo+wGzpQq163gPOXW/cYdAnGMbHxpo3k8gUdEDDdyDEDfLLuXYjxHmjxBijAjJCH8c9so2JlDcD+Tqr3iF9gg==", "signatures": [{"sig": "MEYCIQCNfpVQttcgu23ztB4vQuh4dICJ39ZhRdZl7NjRKtxChAIhAKJCGdr/etD3fLsQnKF7FmH9uQO048nQtTAwzYrkdd5m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112424, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfhgxhCRA9TVsSAnZWagAA+BcP/1BPNKAlp6MxGtEPkv3y\nrG718/9kpze34vg/RyeatQqYrZKbIT3bIotFyGWVIFvv5IogqcWv00vD7J08\n/b+LDNRbAbdU3XDNht9TCcCOBi7ArOQHpiezYwdWgccIpW3hkM8A/VuJl+5M\nbCdp31lt/kg3vkTQGFQD2qcBd3YKvH7rjYfveYlxhfIG1efOUfjScHOyTmEz\njOidrdoXNHUZWB77HQPJIzciYi7WrnBQTfObqJ6Omran+CEE8DE10KJFvVn0\nAo4R0czi8WPiVnPcSbeIQc91jNH4VfN5lTwPzPXLR7UlumoUooplJpaCphcL\n94Cx+TpnCotYmZxdJIPnflluxaNr9b9JztcvjNggDXT77jLtv8nHpayP8APH\nQ9sGRdO87/W8k05G1UF8Q1S/qBakLWq4+W4cuvVliVU4TuP2sCSyYagMmbce\nAmNdZeMOn8UUqkCixOnkefGk86aK7xH/sLOi660daN1DP63M39NBDvP9mgvZ\n/znMYu3QwYEg5fTkKTql6Wym5gLjnZxWJIlDkhFFVv7jfSqlC4JEQjMmB6Uj\nazT9KNwVRvH0uiVMAEzw8zGeZKlQY52ympwKyYepNF7BdqmCW2ez9H8IHTzV\nO0y0gZiI+4Cej1LZS5YeXu2lM7EWcdkRP1nlrTl/PMs4Yw7OsT7ye3wCfJ7d\n6+uU\r\n=R5ZZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lexer.js", "exports": {"import": "./dist/lexer.mjs", "default": "./lexer.js"}, "gitHead": "664f6d750abeea22ccaf832db1b1fbc3b2e0f3ae", "scripts": {"test": "npm run test-wasm && npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "14.10.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.4.1_1602620513067_0.7764190584856394", "host": "s3://npm-registry-packages"}}, "0.4.2": {"name": "cjs-module-lexer", "version": "0.4.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "0eb6e2dee8f1969d013d12bc0435db9a5a31aadf", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.4.2.tgz", "fileCount": 6, "integrity": "sha512-uV69iZhqztkGwmbLrX3gE/XaH2VgYJ8gZYFXrZ0Xo7iyPy9gCW7/C1ISX6avtt3vatIocYJPbCrZRyssl2e8MQ==", "signatures": [{"sig": "MEYCIQDyp25xqSOW7POmfZCXBzAPnuQX9aHk4ObZWkIbz49lqQIhAODl8JjO2CHZl0KYTYPWAPDkJG6zcVcJGoFQhn2xYltS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112412, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfhvLxCRA9TVsSAnZWagAAq0oP+gJLnzImVVhefa9ZVHyC\n9XEnyMfPa/ory1/v/g79ti8TgOHvEoXGHWzKmgHd00aZ/NTlf25d5vj18AyF\nOIzXQuBGXVvtNc0doP72qy9CQ2ZXAH5sfHLEE7Q++fC5fxEbqE2i+aV2NEp4\nStsYjpa1PDqsObbon0B/B+s5/pGujz2fcibZ/yBsmUo6d9UiACdCqo3bA+LA\nF3B8w21ILdfiPdRFr4YQPNfJ1sw9s2dvJy/YYls857BDh0ONM3eXKqHV6D8c\nleSuErjltKgZIsO9aJAKcuyr7GluaHRzm259ZSrkO64SqcgFGZKGQo6scM8C\nYNtHHCaL/uO5im9j0xn6frrne/1y/djhvQd28RynTGW0Wu48Vt1CTqdDFtoY\n5Zp3VR0tFqWMDslEkzSQZvOhuZ/CNGDRMBRkt2Y+VUnAknt2679A7/Q2UEyy\nH5otFSqb1DPdKWrSU9dQGXCKwD2qWYCVRFLcDiAa7ds4Ykn4dSlloa/oKz8j\nwlB1Ov4T2WEvsN7nXBpU0T3Ogydiu9flaMDZaKR5Ri6+8fMa8XG1ZYrNoDhm\npPcgZaxhkLKKfUiBJuKeBqupckKOKBCFN51y6L/caiXbZiFbUsCIeSde7CKE\nyXNO88eTpddBzOTWdvbrYWEW4UOIo9gY80ArFyiEUCCQ/AtYrW2ZeOATjYS9\nuUUE\r\n=c5YZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lexer.js", "exports": {"import": "./dist/lexer.mjs", "default": "./lexer.js"}, "gitHead": "49a3687bc67131c5f7538f231337773a59abeeb8", "scripts": {"test": "npm run test-wasm && npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "make optimize && npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make optimize && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "14.10.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.4.2_1602679536973_0.9676565202230729", "host": "s3://npm-registry-packages"}}, "0.4.3": {"name": "cjs-module-lexer", "version": "0.4.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "9e31f7fe701f5fcee5793f77ab4e58fa8dcde8bc", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.4.3.tgz", "fileCount": 7, "integrity": "sha512-5RLK0Qfs0PNDpEyBXIr3bIT1Muw3ojSlvpw6dAmkUcO0+uTrsBn7GuEIgx40u+OzbCBLDta7nvmud85P4EmTsQ==", "signatures": [{"sig": "MEQCIAGM5pguzhVQQ6H6TTQZa2gjJljsNjzOoUf1JP609YMIAiBsKTJ6ZKS5oBb8nsYutVcb3/UZ0fj+HvH+hsuOjMKSpA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120428, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkJorCRA9TVsSAnZWagAA8QAQAIMQ9D/iUbK+QIlpKcaE\nq2P/n+B6hg3AF/bVYa0BJYVBHYWdYqOHxrRWiNj+p22xbjsmhPn6lpV4dhRV\nIF0LalucQJn5phBhRjjoTjcrBbD71x0v4YyDKEm9RUD7IbGGoRM/8L1B83NI\nD5e7yL2waHBnA4OrGJ4MG9nhgcf6y3Jp8a2P1WdTRrJs4CdgkMgrdS0pGr/u\nx8YNpp+ffis0+3vinU1Zj9g+/aDXavLD+qNAx45DdA/F5Q3DDsDmog+bY8tn\nb8hNCOqwGHicoiLen5cqSIGXLSF7W8vczW7ZaA7FP6lwGwMV/xkkYgYTt5rX\n8OyaHOPJuqskzG5qLXWAQH3crRoYUZa4Jql+lQG8OSIOVqf2sVVSD4qpVLi4\ns8CBs7hcN2yHldLMB0S1uecfJYOk6A6l9MQ+T+0YpAWhi0n6CVRtl6dB4pmA\nZo9SVnqz3Zk5r/geCIl6/UooJ7AH/Aa45bsa17VVSkRZmnqmcya/6mpV+Awf\nlBSqz863SgOAnvjR88D8w9cTywyq+IPfxt6RkbRCyamN+I9fgfEp33efFyyV\noVzg3kxqE+qJHdhcw9uVjPmF76kHlw3dZphjBmjvST5Wi7ckxUbg63Nkbasq\nGFmsofD3g8SU7EvPKU0MNOz5VvRUu/KOzhFn1aBMzq9xpJQ0utEdcFtpOipf\nqb23\r\n=XU1x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lexer.js", "exports": {"import": "./dist/lexer.mjs", "default": "./lexer.js"}, "gitHead": "91462ded1f144c2d56512af4bd2ccb8ce16a3a17", "scripts": {"test": "npm run test-wasm && npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "14.10.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.4.3_1603312171396_0.5305717631244737", "host": "s3://npm-registry-packages"}}, "0.5.0": {"name": "cjs-module-lexer", "version": "0.5.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "25ca2a6799f5ead21d006293c69a198789b488c6", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.5.0.tgz", "fileCount": 7, "integrity": "sha512-L0yxs8h0rEzHxkqqYbHmCHmS43hO/gqD93vVBC/qKaIpl/xVLovKsmM4RjGnP3YmeQ8HTsO73oDcUxBj/1WmWg==", "signatures": [{"sig": "MEUCICgRZu7aGYiJ95qHHnbBYq6W2utuKp1/sv2dIQzZY9QlAiEAvyBvWi0I9CQI1756sVraS+WbphkXdDKD9Y/C4o974no=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121161, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfmvm6CRA9TVsSAnZWagAAlPoP/3gOKzm3cHxEKw18jysB\nmpWLMyHXQyUd4cKq2TC8nkjK9F52POpCS9brokVCEjlhv1/Er90Be1gOWxdd\niyeiSFQi2WosVieqAmv6qvPvaxb4AAnjY8VggB0+Ql2tGacuSE/BuQyjCQIc\naVJJGlHWozNFcw4uCL7fmqskGZ3mA5FIa7Q48R9uBaF8sd3RRJa/D7KBeh3v\nDvl/HO1zaq498qRR8ieJqg9fMAayeKo01OxQATmgUx7x8PVnPqbC9FbaAZSN\nWTbnhSVSVoe7rrnWJsKOkNHWveBb+2VAErAAuVTUZHSQlHNxdjlHTnhWRT+G\nA1EIoS7heEy+ZR8PHPV4WZaApVIwEwYdJ71Lk2nFoSS5BGIGrwdh9ZYB9OEb\njrT5kHPRAHipz1Bz0048q1u/BagbhAPDKBlSEgr+i7Bqub0yrdOIpf/yzwwG\nq2Bo4Lh1Ys3ZMD3WZFN/Qwr5dluQOToNDhKrc+GDmZFIN3QQ0uYlQ9lsyh/r\ntP651iZJdQGbJlAW+fcbldhaHoAyvhpz4tCU+e92b3uJbFXYurSHUpyvbW7I\n8vNdWSS/8QUNHrHnOY2tSQHsDNztX06t6RKOLBoanINxqpArYy7BJZ3SyfEy\n2E9+sTtLv25jzGNWLyGVCkehG5zUcm3wdVWrcDur/C9rAyLt800x30tKYv7H\nc5PT\r\n=ZtqX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lexer.js", "exports": {"import": "./dist/lexer.mjs", "default": "./lexer.js"}, "gitHead": "638f6d61825859c56c9642bd0bb6d7395cf98d09", "scripts": {"test": "npm run test-wasm && npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.5.0_1603991993701_0.8236149168034901", "host": "s3://npm-registry-packages"}}, "0.5.1": {"name": "cjs-module-lexer", "version": "0.5.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "d974cc192c7235e849a5d866f641fdec599feba9", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.5.1.tgz", "fileCount": 7, "integrity": "sha512-YdRxVW5O3F+PcIcXJYaWdYwWwuTVczZwJbGBQVRzEeZZ4KK5RYBhmSp9bzJ9mUQRZiUtK7nRWloWo51Xalqv5g==", "signatures": [{"sig": "MEQCIQDJdXb/uBxkbJK5QNJzRo8h9xV6fQMGI/JPVp+6iCq88gIfUU3pZvtK02AeA2voKF04Tzr9PGbgGq+rCEcX3P6tlg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126628, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfnYt+CRA9TVsSAnZWagAAwlcP/jry1JHJfnbkPE232sTu\n1R66Ou4Z95Clu/NFHkjRJfOYD+1tQg8DRJwE5e5q8yCb6YFAUnxlhLd8Gebl\nCo6EJAC8xTV8qjiX508OPk6kppo2NMMw0CNW4bM+dpOOi5+LAqxdU7eUluGi\nVpEhJXLrPaXupqEr4TolPgAYnlAITS9lddoHXCs/b0+lUitoZ+o8VBernRPp\nb+Y6qVKjzqLwF1Xez9Gt2Dje5MGaxWdaApl+KTb4RHEM75JKn7+8hYxWSdBt\ncZVFNIi4j95NSlCkhS2LTQTKIOGArFn/E5ZfIP3UXRiZl/bEXyHxl4FxmrBn\nDvjn56IyPWtlJnnjuHgh91j1xW08OeuRNYyUh683WxVbUW9hdKlEL1SNekWp\nZNZXjtjsJsqnGot+JeoIaM6gb9zf7HYSSb2MUi1W0X7iCDWNzkFJI1U/0gHO\nhzMGFAFDVAWfaDu84ebfOoNdHSke8Lk3byqBxfKjMFXiu8pnbBaonpBlmTjr\nyaz+FrEd5OtHQT46/kf82S1Rb73x1J7aSUbRJYst/+ZRt9CNJ9SbkVDi4Lwf\ng0ZliqTnBExG6KWjXqkrQL7xEej8/m+/+mXAxSGeBR5VOwT3FVR3msxGwEw8\n7tbrWrslkLULr3Ipzja4U+uv3Tv5r4BFKAm1UdLpkYyLSE4E1V2vAH9m81xb\njOSS\r\n=1i3D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lexer.js", "exports": {"import": "./dist/lexer.mjs", "default": "./lexer.js"}, "gitHead": "16e6bc63ce26037bec7f7987e2fb4933a66769a8", "scripts": {"test": "npm run test-wasm && npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "7.0.3", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "15.0.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.5.1_1604160381917_0.1367199903103864", "host": "s3://npm-registry-packages"}}, "0.5.2": {"name": "cjs-module-lexer", "version": "0.5.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.5.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "6e979f3065fe30cd531ebfbbd93033e6a14bc4ca", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.5.2.tgz", "fileCount": 7, "integrity": "sha512-GlXp/i+x/WJP5X/qBAfo8ygo97ePkYSTL9um1TZ7nMG9JlFXZADyMhHQ7DFc0xk4G8oCagEKxQaHHD1wd+ZEyA==", "signatures": [{"sig": "MEYCIQCSEWcG8604QmL94N56WlXzN3HhVScuf71HL/P+v5woBAIhAJU0jwUbGQrO+WK3xu0Hflx12UnpeavxhCf+E+aQBoBt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126963, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfnbCCCRA9TVsSAnZWagAARS8QAJnRiAtQP/XPmixzSV5h\nON7/Z161UcJnAQLgqiq7PJBJXgRAxWXTnSYC7vIdeHnU+VFhAuetwtlruZhL\n7qYulNL6aW1kPnWJBWy/XPHNhgLJ5AB/vZYE2+8KdKVTlNREvS2j10j9LRqh\n6PMBL5PUGwG//O/8Edtv0FrUQwCvDdh3EhD5cNYtprxIJ+NL7tMsDJ6qs14O\npNSy2ACNFMVe/57FNzcMI1jUu0ujNI5LFl3q4gZPUnGuWM2Jzvfgb+z3kbs0\nahvHyD87PShorzP823j3aK9sO0hjoBJ1uXqg+IkqArCSqisPMjF8Xn0lqW/P\ndW5zu9lZxV45v5CWPMMMB/UrJnGBbefR7tcgQwsEp0/hH3E9aDo+AmoB1HYx\n6p/lfxe1ZdGhq1tQCPzDeL+HwQjyRtpvPhGinwA0pWoqvZDTk08TtEOINqsQ\nmgkM5uyx8wwEMxkhHmpPz/XwQJUS7j4gVcnHxvet43Wi+3Xm7Pyyc8+8slEB\nYST7aN0cFi9/GS8dxKXYFXjwcNJtICDKZahpQFihNBPDVAYOymPVI0vfXg4f\nocmp1eZUI05BKLhpEWehFQBskeJkNqSXddXjfN3KcHV5kofJeqTiO9w3pPHk\nGbeugWm9h7tRBX9u7n6RISbenVDXjrwd5xSTMzMOnVknmByKWpiB8nqr46Bv\nD/uE\r\n=EBpT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lexer.js", "exports": {"import": "./dist/lexer.mjs", "default": "./lexer.js"}, "gitHead": "9ad85cba094441effcab6b315c96414448cdb178", "scripts": {"test": "npm run test-wasm && npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "7.0.3", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "15.0.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.5.2_1604169858186_0.6370414543619802", "host": "s3://npm-registry-packages"}}, "0.6.0": {"name": "cjs-module-lexer", "version": "0.6.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@0.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "4186fcca0eae175970aee870b9fe2d6cf8d5655f", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-0.6.0.tgz", "fileCount": 8, "integrity": "sha512-uc2Vix1frTfnuzxxu1Hp4ktSvM3QaI4oXl4ZUqL1wjTu/BGki9TrCWoqLTg/drR1KwAEarXuRFCG2Svr1GxPFw==", "signatures": [{"sig": "MEUCIQDoYlslxuj76vBz1EiKuPGwuuYxD319ZtAJmLF0urAqawIgMofjS8sPyDNfh7ulC37zNZsjCCfQ3C9nOkcooDhnvDA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfnzxOCRA9TVsSAnZWagAAMLIP/1WoRSYWTe70Ub7GR1qw\n7Vp7z5JhmqKMuM/B6K/73qsD/qtEyDCh7Srpzpsrq5/VW7AtI/ACgf5uE5RB\nMqXzdECmDYLUWIovH3Vc6x/4MlOG+fFVjx+Juw7JSGK0HU6Jcij2/okgtUyQ\nFzijDp2fe6OmFFhYbxLMjojG6Fk8JFY+VWXQV1llKNQt/bCie5TdBkVAahGs\nsGpJh6Hs+W6HW/4lIC+KGReMm75FwkmQkH/emHXxwV1Wk3i4f0yhYjdqV/CD\nnDc1w2J5WPXZ3WVuOCA319KTCoFTiVhVw5Yy4wprSly7Ljfya1eQjq8rT2nU\nPHocmFnJZL20+VCKsKZKKHwMXzDpF5fLxnUEcyvuFGIEK886UJkuhPEReqBN\nEdwx6bRanXLzgWs7tfrBhqjyxNZryh/+xyhCZWgHxT5J8B/Sn3qpfwJgvf/y\n+2qeqjq5OGA9G5fRseJm0/4bRK6n0Wu3CQa/x8DBiT74eZs1eLF4k/yDZe2/\nLGgihWTqenOkEGsOBN3MLS+YhvcREiaTSMyG8+6x3V3vXivTLUJKg95WNk0T\nu3zyrYW8OBCE87Zr2FpPqQOf5j0idjOuf0OH9dBxdJUQmCPFSV7vSlHU7GG6\nLiGqMXaBv7zythtieM2PngurihbIXCGB8Ecu6VaPAEpSZMrNhBNaVpZPJlgB\nNWPj\r\n=Ni+u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lexer.js", "types": "lexer.d.ts", "exports": {"import": "./dist/lexer.mjs", "default": "./lexer.js"}, "gitHead": "f50b9ef5b8cbb992ef9747032de8d9b268d1fd4f", "scripts": {"test": "npm run test-wasm && npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "7.0.3", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "15.0.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_0.6.0_1604271181808_0.8244588583255592", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "cjs-module-lexer", "version": "1.0.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "c125ff0f4ab2c898dda909352f254d55e2213261", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-bLSEjEwg4knnuXt7LIWegvgTOClk6ZonZY6g4CFGBly1EjRqVjTjI8Dwnb/dsu1PwJjYBKxnguE5bRTdk+bFOA==", "signatures": [{"sig": "MEYCIQCB17P7FvaP4BYC/7erj74zjjuOl+dOnpGI3LRfMmgDGQIhALDf388BZjDyhcy1F9A7m29oixf9JcJZMNv71bFWJLQr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 129193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoC59CRA9TVsSAnZWagAAkPYP/AiYfcP/nxlYM9nhkiz7\nLriUhHj7Vs4UYUl4uezZamxjS8AzBnkqFP8PG/fKACowHGVb+C4GaoIAugi9\ny0h0vq5h1QrBchHnsiaQcwU/i6x9GeQWOtovHEhusrBqSmOuW9aPmPbWrUHW\nXI1Ydh2ggUJcWklcuUqp9c3r6Q0MBLxdOazDgzWGIOWKMhUKBm80MIi8rgws\nEktqIDrutJxJtngyLm6JRt9yWIbcXZbrrjDujp97wxCXWJtRZ6goeznw5P9s\nFrX251lryCFcBy8q1NIjRc0eFAagvB76Drt7hTg2HG2RJZJtPKdAS0dWoovL\nnaomNBMjnugzCrFCfFbc9CUrJyXbIYhEdEATt3tGt2vwiAzETNNX3ieFIf0j\nqVmM2NHTyLMaSV8iLoHIO+mBl+iL/QrFY7JmnXNs8108HP5EuiVj62m8yUE/\nY1UYrlPAE5kaIQm6LgkQJ7jArQ1J/D+p20mloDU3yc3yD+nx5EpLKlbgtmC2\ng5T8PNbMCUYuuTiPW83KkFRrvstUYWRQl/3eQqhO95bIMUpOfUZUGKHUTDEm\nnccXCij8+YCb+BbTKzgQctFCjfirk9J4aALP3egbPI53aPgaGB6gPFRMDaD8\noe+kXOtUrUrCKypzBJzAdvfiWvNB8PM11qXXBO5QeAe2h/l2GeBzI5FmOMr9\nTQ6h\r\n=goa/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lexer.js", "types": "lexer.d.ts", "exports": {"import": "./dist/lexer.mjs", "default": "./lexer.js"}, "gitHead": "e4af39ff7efbbd4e86a3170be69760123c1a44be", "scripts": {"test": "npm run test-wasm && npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "7.0.3", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "15.0.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_1.0.0_1604333181263_0.12089865853576054", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "cjs-module-lexer", "version": "1.1.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "22a566b2c5335fc390de2fab246a0b8eccdd5264", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-zE63fH4Nrb9N7JUc9yQx+PY1kt+5rClC1xAK6qMldoRcMZUv0LtNQdGJ1jgo5XLETEl0EmSCGvTloAt/J4tayA==", "signatures": [{"sig": "MEYCIQDmzjmaXm7NCiPOeg0uP8CxlwqwiRB53sBThvEp4ZWBtQIhALXF5x0Ky/229aOLhvuxQCMx2JSGXoW09xkFSa3BfwbA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136983, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgSg+fCRA9TVsSAnZWagAAWtEP/2MwS/xwozrmVLNbp6Zu\nYZmBnSbbWgZpt8tJ9mEkcZthyeKCz3V/VEsSyoVApXyItH5yFm9OMjfnssX/\noGDioUxDxXQ5VBqOeDLUhDcuY5+SRwEQmxlb/lIPke2FHYawXESoJ/AWdLyR\n4aMZ8mTgM825QwaZGXvbHIbvmsgROTCMAa4ql4aY+/BG60hUt5Yab6VXwfz3\nXNCF5FWZLgMTz58SFEGgpGDyB+WqWJv5gNF/faMUBHhbaiOLw89G9funfTux\nnN8Duak25Z4n2/91yTqmw7hmu/4QAzh+GxCLKYo/0nCzrOaC92AB3D+OC5/Z\n+xdqQ366wAzNVYADIS1m4vA8tpiMn8VSlmfI/Myog0sds0KyDm4u0xuh46wE\nST3aYQzcc4CQphUWYUNMdK6rLoZ2QG3vuF8yKU0uT3HIZ6gWQM6TmywOGDxU\nSe6XMFgqEtr2rcSCs1qQUl2aKwwprRUPcbrHxFm79D50hjOEMBZ3QOCdDNf3\nCaVt8UBdiLNYXL3tkA1f0B0xSIxw6OWTrJnznM9sZu8EVcDy6WV3MjsZomM5\nAEx0n5M7xCDlcLCJcwuDwb1fYwp/qhSPLlXIi928uDYDo0xjQQmSwj9B1z8c\nL6wfnu14BbHE7ydUnmML0DBpF1MtJkjwu04S0SlicJRIYGM6Xd6z8sAqC6SK\nQw+o\r\n=xz30\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lexer.js", "types": "lexer.d.ts", "exports": {"import": "./dist/lexer.mjs", "default": "./lexer.js"}, "gitHead": "6ec8053af179c18bef3ae496935a08a87eaaaecb", "scripts": {"test": "npm run test-wasm && npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "7.3.0", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "15.5.1", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_1.1.0_1615466399344_0.010924048572736877", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "cjs-module-lexer", "version": "1.1.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@1.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "4a62531147dd488d0369c1cf1580b621796a9b26", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-7/a2+QJu5Bt1WYi4vk0NinXin/4pEUBlygSree2N5dSlhc/szO0+HwYKL3llv7myOVingwTgmEHH4WO6Q7Zv7w==", "signatures": [{"sig": "MEUCIQDH5cxsJYp0Rlu7+43korjAp3SwyDRVNy0U/qGdTp1tRwIgPoNF9QScey4zyjDrXypP+kxocRbPWnJoGEqNWXNZo9Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgY2orCRA9TVsSAnZWagAAXGoP+gPQDneWdGs17d4ksvUJ\nFzMEfIEwbp95zIVdv9I2+Imhvh4K/gQeA64xQosyXVprkOcsfY88a/zkJ5zP\nZWBA3xcMyh1bCv+mu4CsouGqQxJeySHqlTbQ5fo8E+qCN5R16Cw+vmBdW84E\np5HsYTnXbzE9smpFexCtMR+0T72TPcXxldTgtmTZ/anG4V83ycyQo6/Iqh6T\nAmIz4Ow3kbF0RTPIycEr00W85zoBa/pT+pkvPqmgF2y2X8o1rZUjj4+97eyC\n/khLwSsUqwT/EVl2QhD7zZCTx32aV+B0SENaCOzoCt3viPlT/ksfmAasqHWs\n5sLPNw1nhQkuvYfe5xQ9eUaqTIOJ7KMNKmtx3Jesldam7nm80oH3BBqcfhhF\nhMb+h0FCHxnErFy497JUdEnUVjj6U5O5VXCO2Zb6SbA6uwnlaNFMV4Dqrd8G\n8+5qVjOHqekGxOE3fsMAENDIZB2sWXtatKPO+LmkMdIJkpp/43/kCX9d+B73\nOindJPk0IkBPkGU9RUJPfshxArpaObswymvV6eNOUZOISz0UC5elx3Jqhf1c\n+l7+ep+qGe7VOr0PuYs7OGFJctH44IZOLZ5AHS4aWSUtkKQ1HKMrfD32GgNy\nQxEXj56JM4+bQ1+aJ5BNd5GZ9l3NJuqNH06Nltu/jvvqrpe6KO4d1SotTxuY\nBq78\r\n=yXk8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lexer.js", "types": "lexer.d.ts", "exports": {"import": "./dist/lexer.mjs", "default": "./lexer.js"}, "gitHead": "d74e77007d1bb632f3607d2afd9ffbdea69d76f2", "scripts": {"test": "npm run test-wasm && npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "14.16.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_1.1.1_1617127978813_0.3481678168519362", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "cjs-module-lexer", "version": "1.2.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@1.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "5b2a542d63f32f69c8735e4447ec26a8a6f21b3e", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.2.0.tgz", "fileCount": 8, "integrity": "sha512-S3lnDW/6bYN8xry9cmjsY+sTo0du8OMce/ux+MTWA1/IvkarL4FrPLCn+RU+v0hpX9Sa4TMBro4idpm51b3fXw==", "signatures": [{"sig": "MEUCIQCuW4Ko29RrDIkRLXXco2LeaI4uoFj475eyLMJI5D7vBAIgN/KXktcjsssUmVKWXGycLStuqvLrOfOu2TpH0fo/4u0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136747, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgiUCXCRA9TVsSAnZWagAA68cP/RXT8dUmMIUVe5XD9iWo\nustzx1mK1lZlkflT/1UYNUHyi9d8ALiVQpz/YnkdI3lUhgflW5BWZS0f7Juf\n8uVfxCToXUQxfH4u8/YhJwPcAyxP9Twhd692P6poBZUh7KDDZbgV0H3+YQCU\ni6Mf7o2wVwgpTupUEqCr2U9U81QuvK3WaSO+verC9hxz8Z1aCP378q4HzabM\n7a3HTiaE2GebZM8x0ilRGBLXKJlg4fUMVXn1yqHeV0ETuHznu0drscKDLAJB\nSHRSNavV4dKyol8pNPZ+9McX7gLPgliOPoP0ThYm+FrrJ9F4AskpEwVvymLO\nfNCbzIKSJ/zJxfZCX/4uLQTiI/eVApKg/bpAcMtDjfgx91aPxV7rpI2eHP+G\n+E0VQMXNYUTK79ac8ddtfakkekEyFZe6+vXfz+7u6SoGZid2lrmxNdfR8WXl\n12EjGeCp2rDArc2UAYPzrPdQp9dWqv6Ef6jCsTRRpBtVUvIM4KoaxteVCFYp\nYgL0t0FsUode1c8qrjQUlOxmBuqoUZHKkKDcIHUU1Gw6DnarVV3e0dUBm7jT\nyf981zQ44xw3X/JvVLtPjFgxhzUzucE1CsEoUPmx6Bq7KaQZ5lpLrUEGp/VA\n9QEYhVoVRs66SC5vI0ILNvaVv/Nso+qLU2DaaHRj8i+fqlftTrNi5Ii83lD4\nQuQC\r\n=YD/V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lexer.js", "types": "lexer.d.ts", "exports": {"import": "./dist/lexer.mjs", "default": "./lexer.js"}, "gitHead": "ee93a605240f103296feb44b3b3d0c32528f2535", "scripts": {"test": "npm run test-wasm && npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "14.15.5", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_1.2.0_1619607703263_0.4576102531532942", "host": "s3://npm-registry-packages"}}, "1.2.1": {"name": "cjs-module-lexer", "version": "1.2.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@1.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "2fd46d9906a126965aa541345c499aaa18e8cd73", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.2.1.tgz", "fileCount": 8, "integrity": "sha512-jVamGdJPDeuQilKhvVn1h3knuMOZzr8QDnpk+M9aMlCaMkTDd6fBWPhiDqFvFZ07pL0liqabAiuy8SY4jGHeaw==", "signatures": [{"sig": "MEUCIQDUiLPqFw8BFs2/2MTA8qxTSkr/DtFl6AuarN5vGfUcQwIgfXEfmAvSgiGl8NAuWc2plT+goOCuvkXAaLk+V014Rnw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgio/qCRA9TVsSAnZWagAA40cP/3TBvivZUEQP2XZNq1nh\n4S/uRPqRknnkDUMzSoKq1YIduGiC7rRchtVNcGcrjMItMKbrzhdAQXSP9LIY\nNOJ6l5Pn10V2UGsj9sjxybTPcsIyE15+fi+w4aoYbBR4CfVmi4XaKK8EunrL\n/9/BURx8FsYgdb8oE3Cjow6UCA9mDGHY0jCv4qde75tifDHbE3IpVeeM3qoJ\nEqF+f/JqfiFuB3gggGIRv4+aMIGc2mMkF5qSb87UdL+ik/SXek+Du2tqAZu7\nUqtygzycqcF/VQ2aT9g36wPB5Acg4RIszkLMq42Ok0bsO88quKnDyoxERIYo\nSUmjW8+fyn1p+lt10Xf89hf4bPy/gtIfrDkxBNEPpBV7/DICXhiMy3+6ys83\nkWCF+T3D6QbpBFhZ0y8lm/j+GID5GaVzxspOo+ZSSopdiIyAm4EWu+1BdvCT\ng1rCaEDW2tPWUh4jEsomhj1w+rXftNvHkBaDFs++G3UPIj6OlV70zkyInhdM\netY4iYTQdsKJoDTVclykq0JSDHqMHEQozLyl6jfznRrmRLDCrxGUSGDZvDM6\nuuyX/Kzso+5UYe/b8akoTZJ9ToFdECYvp7kr2Uvvt6KmoO45Q/urepYGM1zK\nrbROfoWVD93mD1s2Io3ccP+wj5YlmMAdhS2PgbeZud7Q8zJR7xlqmyU7A/AF\n8dak\r\n=02Q+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lexer.js", "types": "lexer.d.ts", "exports": {"import": "./dist/lexer.mjs", "default": "./lexer.js"}, "gitHead": "466b1a2e736d778f4304698569d71efef711a371", "scripts": {"test": "npm run test-wasm && npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "14.15.5", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_1.2.1_1619693545780_0.8657069478346293", "host": "s3://npm-registry-packages"}}, "1.2.2": {"name": "cjs-module-lexer", "version": "1.2.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@1.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}], "homepage": "https://github.com/guybedford/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/guybedford/cjs-module-lexer/issues"}, "dist": {"shasum": "9f84ba3244a512f3a54e5277e8eef4c489864e40", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.2.2.tgz", "fileCount": 8, "integrity": "sha512-cOU9usZw8/dXIXKtwa8pM0OTJQuJkxMN6w30csNRUerHfeQ5R6U3kkU/FtJeIf3M202OHfY2U8ccInBG7/xogA==", "signatures": [{"sig": "MEQCIGXfNTgpT2V/7aFeY+H5N/HJFFCIaK8qe/H3zXjL6QdBAiBSo6We7s/Oxho64Ui+SPw4q2P19Lt1J8+zjkzV0F3euw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2mARCRA9TVsSAnZWagAAqRgP/R5R66vGQ/ODGCT56BKC\neV3K/qK3sYFxlBvxH9LvT62eU2KoFwtufrh71toLRZJH6YyMTSOJR92QXzKy\n23oIhPvHZYjSbezRXycwTTn1qRPJw9oeWB51iZioCCTlXqw3gPBRECeY4dPA\ngJLg4GIaY0L5DNaXuCu5eehQy6ZTiUQ9jCksmBPXuRIy5sOyKa9J98WieYN9\nAR2/517xEvkz3MoHbsOHMNYt9r+yLO+Qx4xinJ0ISpj7tL1rrehALhSTbgvj\nzUULxsImy6pnET7fHOiqAzhEtjFI1Gb7/2MQt99onKL0GxFLPyeZtiAd8ee8\nMM0Jlr5d/IAPOEA0Spad2g4jbJXTyxqe6bqh/VPpO0ADMMkzNsAMATNf8aOn\nRmOS6p92mLQZanLG8XMvVud16J3SolkR2K9jOZdXfEEjNJ4JdHY3qKJPHm2T\nKLZvmbRfxxjPeMy0RXdfZvDo4bOPJMxgAqMaaa7upBpcQ7mCew6t3ylh54OE\nKBA02Phf1mqQ7bGoWp74BFMj36T+3+1ZR5QOL/Wg7oNYqtcw/7slIO8MGXMP\ncqR27AeUt0wdKOW4nKbYgN/IdT4yNe8SpTSM3wxTipkIy0etTFEE8C5ixrfc\nVgDklIVYYkZDRxQiZlUBGad0qHinBj922jDN2l6gwkmfHAIT4484SKzjhEkk\ncI8O\r\n=yJ2H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lexer.js", "types": "lexer.d.ts", "exports": {"import": "./dist/lexer.mjs", "default": "./lexer.js"}, "gitHead": "a96e7b52b99d39fee25d69f356905161d14a15ed", "scripts": {"test": "npm run test-wasm && npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/guybedford/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "14.17.2", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_1.2.2_1626377593719_0.8548195669133953", "host": "s3://npm-registry-packages"}}, "1.2.3": {"name": "cjs-module-lexer", "version": "1.2.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@1.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/nodejs/cjs-module-lexer/issues"}, "dist": {"shasum": "6c370ab19f8a3394e318fe682686ec0ac684d107", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.2.3.tgz", "fileCount": 7, "integrity": "sha512-0TNiGstbQmCFwt4akjjBg5pLRTSyj/PkWQ1ZoO2zntmg9yLqSRxwEa4iCfQLGjqhiqBfOJa7W/E8wfGrTDmlZQ==", "signatures": [{"sig": "MEQCIBSNTKhKZPRphnwzinG5r6r3Mbkvu15T5qTkk/l4CikmAiBVn6Cldic8wNluIfDdJOsfsAausNzy1XXvL80LkRyzeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139393}, "main": "lexer.js", "types": "lexer.d.ts", "exports": {"import": {"types": "./lexer.d.mts", "default": "./dist/lexer.mjs"}, "default": "./lexer.js"}, "gitHead": "4acffa9831ec8425df0d958f0c2e93daf9608802", "scripts": {"test": "npm run test-wasm && npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js && babel dist/lexer.mjs | terser -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm && node build.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "19.3.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^9.1.3", "terser": "^4.1.4", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_1.2.3_1686427762885_0.513183526380617", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "cjs-module-lexer", "version": "1.3.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@1.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/nodejs/cjs-module-lexer/issues"}, "dist": {"shasum": "73de79dec6585a39fb72a4905851423b3f4f420b", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.3.0.tgz", "fileCount": 7, "integrity": "sha512-bVFe+IGccK6kz2sM7Er2CoAcllmIVZFeauiyy/0r7UpBMAFTXD3yduZ7w1+MowSb0htol+zTArLTuzjoMZa14w==", "signatures": [{"sig": "MEQCIBsxCGOufF4wAKh8vhgC400f2OTZ2jGZgnqCYEbK7VzEAiALcYka6cXtfOk40HLT6THbeVuAbJ2wJlOB8JwA3/FE2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139411}, "main": "lexer.js", "types": "lexer.d.ts", "exports": {"import": {"types": "./lexer.d.mts", "default": "./dist/lexer.mjs"}, "default": "./lexer.js"}, "gitHead": "425e14d9b5929c79216824aa54b978cfa7e98d1f", "scripts": {"test": "npm run test-wasm && npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "cross-env WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "9.7.1", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "21.2.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^9.1.3", "esbuild": "^0.19.12", "cross-env": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_1.3.0_1714258918781_0.2271298516897562", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "cjs-module-lexer", "version": "1.3.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@1.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/nodejs/cjs-module-lexer/issues"}, "dist": {"shasum": "c485341ae8fd999ca4ee5af2d7a1c9ae01e0099c", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.3.1.tgz", "fileCount": 7, "integrity": "sha512-a3KdPAANPbNE4ZUv9h6LckSl9zLsYOP4MBmhIPkRaeyybt+r4UghLvq+xw/YwUcC1gqylCkL4rdVs3Lwupjm4Q==", "signatures": [{"sig": "MEUCIFVMAafkPevlaCnJOAPtSALaSuTfMu7jLp6moxd4t1yDAiEAm8+jJXBo3r/wkdbyh/DxoouqVmulJ2hq8IeumiLgKGw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139411}, "main": "lexer.js", "types": "lexer.d.ts", "exports": {"import": {"types": "./lexer.d.mts", "default": "./dist/lexer.mjs"}, "default": "./lexer.js"}, "gitHead": "5f974d80f603681ad8b5febb57b0bfa10b538d9c", "scripts": {"test": "npm run test-wasm && npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "cross-env WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm && node build.js", "prepublishOnly": "make && npm run build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "9.7.1", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "21.2.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^9.1.3", "esbuild": "^0.19.12", "cross-env": "^7.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_1.3.1_1714283087704_0.9737655663430835", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "cjs-module-lexer", "version": "1.4.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@1.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/nodejs/cjs-module-lexer/issues"}, "dist": {"shasum": "677de7ed7efff67cc40c9bf1897fea79d41b5215", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.4.0.tgz", "fileCount": 9, "integrity": "sha512-N1NGmowPlGBLsOZLPvm48StN04V4YvQRL0i6b7ctrVY3epjP/ct7hFLOItz6pDIvRjwpfPxi52a2UWV2ziir8g==", "signatures": [{"sig": "MEYCIQC//vI39nPOlUrdW1a0N/RJQ3Q0pPb4apyqkbG87PoT9QIhAJw8mVkUX2fT5/Wk9TsIGzzeH/pVNpvKz7t2ce0aKeuE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145784}, "main": "lexer.js", "types": "lexer.d.ts", "exports": {"import": {"types": "./lexer.d.mts", "default": "./dist/lexer.mjs"}, "default": "./lexer.js"}, "gitHead": "a8898ea6a0a8ebc3fd6e5af5b011f66399a7c697", "scripts": {"test": "npm run test-wasm ; npm run test-wasm-sync ; npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js ; babel dist/lexer.mjs -o dist/lexer.js ; terser dist/lexer.js -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "cross-env WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm ; node build.js", "prepublishOnly": "make && npm run build", "test-wasm-sync": "cross-env WASM_SYNC=1 mocha -b -u tdd test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "9.7.1", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "21.2.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^9.1.3", "terser": "^4.1.4", "cross-env": "^7.0.3", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_1.4.0_1724621928295_0.7439693104849965", "host": "s3://npm-registry-packages"}}, "1.4.1": {"name": "cjs-module-lexer", "version": "1.4.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@1.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/nodejs/cjs-module-lexer/issues"}, "dist": {"shasum": "707413784dbb3a72aa11c2f2b042a0bef4004170", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.4.1.tgz", "fileCount": 9, "integrity": "sha512-cuSVIHi9/9E/+821Qjdvngor+xpnlwnuwIyZOaLmHBVdXL+gP+I6QQB9VkO7RI77YIcTV+S1W9AreJ5eN63JBA==", "signatures": [{"sig": "MEQCIG7Ka1e6f3+rfyzB/+aTpkIAtJd3fvDPrGJiUQfqSOGyAiAaHJIEbNTcpjGfr1/voGadLNuwFdqBO5R4jv7ylrI0pQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145734}, "main": "lexer.js", "types": "lexer.d.ts", "exports": {"import": {"types": "./lexer.d.mts", "default": "./dist/lexer.mjs"}, "default": "./lexer.js"}, "gitHead": "b9f2c204961b02bbfee603c196f9db05df960d14", "scripts": {"test": "npm run test-wasm ; npm run test-wasm-sync ; npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js ; babel dist/lexer.mjs -o dist/lexer.js ; terser dist/lexer.js -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "cross-env WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm ; node build.js", "prepublishOnly": "make && npm run build", "test-wasm-sync": "cross-env WASM_SYNC=1 mocha -b -u tdd test/*.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "9.7.1", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "21.2.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^9.1.3", "terser": "^4.1.4", "cross-env": "^7.0.3", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_1.4.1_1725742310166_0.8309903985615814", "host": "s3://npm-registry-packages"}}, "1.4.2": {"name": "cjs-module-lexer", "version": "1.4.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@1.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/nodejs/cjs-module-lexer/issues"}, "dist": {"shasum": "22cb6f7701f9948fd47a5c4ae978493b1a03c6b9", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.4.2.tgz", "fileCount": 7, "integrity": "sha512-7gdnIlr/WqvlQaX6yMvhHbiEVZ07qCV22rb/brgyFGKgo76ckIsrtDp4w2NIOitmKDNgiUm+pfVSE4VMwnkXwQ==", "signatures": [{"sig": "MEQCIA16mMY1zVIAilO1sRyyTote0H5BMgl9Qj0XrmwMxa4YAiBOCzgeM6sdql2XVN8PNz10JPSWsWUL8YaKuWe93dVQOw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 133125}, "main": "lexer.js", "types": "lexer.d.ts", "exports": {"import": {"types": "./lexer.d.mts", "default": "./dist/lexer.mjs"}, "default": "./lexer.js"}, "gitHead": "a0b54be6012645971b8f28969c0ff68d788aaddb", "scripts": {"test": "npm run test-wasm ; npm run test-wasm-sync ; npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js ; babel dist/lexer.mjs -o dist/lexer.js ; terser dist/lexer.js -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "cross-env WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm ; node build.js", "prepublishOnly": "make && npm run build", "test-wasm-sync": "cross-env WASM_SYNC=1 mocha -b -u tdd test/*.js"}, "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "22.9.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^9.1.3", "terser": "^4.1.4", "cross-env": "^7.0.3", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_1.4.2_1738257762532_0.5931386883912542", "host": "s3://npm-registry-packages-npm-production"}}, "1.4.3": {"name": "cjs-module-lexer", "version": "1.4.3", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@1.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/nodejs/cjs-module-lexer/issues"}, "dist": {"shasum": "0f79731eb8cfe1ec72acd4066efac9d61991b00d", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.4.3.tgz", "fileCount": 7, "integrity": "sha512-9z8TZaGM1pfswYeXrUpzPrkx8UnWYdhJclsiYMm6x/w5+nN+8Tf/LnAgfLGQCm59qAOxU8WwHEq2vNwF6i4j+Q==", "signatures": [{"sig": "MEYCIQCOJb9azF1+SpPGgA+lhKCBEsdAGvckG6ifseNjmWSbOAIhAMQ1/xeNATN7CkixMQ28Sy09K9+V4PhoQkHivAMw0xxp", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 137667}, "main": "lexer.js", "types": "lexer.d.ts", "exports": {"import": {"types": "./lexer.d.mts", "default": "./dist/lexer.mjs"}, "default": "./lexer.js"}, "gitHead": "b9f2c204961b02bbfee603c196f9db05df960d14", "scripts": {"test": "npm run test-wasm ; npm run test-wasm-sync ; npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js ; babel dist/lexer.mjs -o dist/lexer.js ; terser dist/lexer.js -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "cross-env WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm ; node build.js", "prepublishOnly": "make && npm run build", "test-wasm-sync": "cross-env WASM_SYNC=1 mocha -b -u tdd test/*.js"}, "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "22.9.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^9.1.3", "terser": "^4.1.4", "cross-env": "^7.0.3", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_1.4.3_1738279292666_0.9481655687712329", "host": "s3://npm-registry-packages-npm-production"}}, "2.0.0": {"name": "cjs-module-lexer", "version": "2.0.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "cjs-module-lexer@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "homepage": "https://github.com/nodejs/cjs-module-lexer#readme", "bugs": {"url": "https://github.com/nodejs/cjs-module-lexer/issues"}, "dist": {"shasum": "c3e62caadcb2b964e0c091111d7e82f6eab31213", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-2.0.0.tgz", "fileCount": 7, "integrity": "sha512-Pdn6hu/sAcShUiRaufGI+txfkQl1bYD/DykS9Hg7ufj7J/76PPEOpQCrpiIkQGWao7IHQXbafD91dyBoNIWYNw==", "signatures": [{"sig": "MEYCIQCA9Lh4oZBaRJGrIno+AoGAXfgDD9Q4iReiEZ4CbnlQswIhAPBEDUybyfTBVnsHYc7dokzNdwlHdIIpkb0+SQbaehTD", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 133125}, "main": "lexer.js", "types": "lexer.d.ts", "exports": {"import": {"types": "./lexer.d.mts", "default": "./dist/lexer.mjs"}, "default": "./lexer.js"}, "gitHead": "3c141f8bd4697e5cf64d0047d922cc79a1e0d57a", "scripts": {"test": "npm run test-wasm ; npm run test-wasm-sync ; npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js ; babel dist/lexer.mjs -o dist/lexer.js ; terser dist/lexer.js -o dist/lexer.js", "test-js": "mocha -b -u tdd test/*.js", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c", "test-wasm": "cross-env WASM=1 mocha -b -u tdd test/*.js", "build-wasm": "make lib/lexer.wasm ; node build.js", "prepublishOnly": "make && npm run build", "test-wasm-sync": "cross-env WASM_SYNC=1 mocha -b -u tdd test/*.js"}, "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/nodejs/cjs-module-lexer.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Lexes CommonJS modules, returning their named exports metadata", "directories": {}, "_nodeVersion": "22.9.0", "_hasShrinkwrap": false, "devDependencies": {"kleur": "^2.0.2", "mocha": "^9.1.3", "terser": "^4.1.4", "cross-env": "^7.0.3", "@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/cjs-module-lexer_2.0.0_1738343491561_0.05512595421355737", "host": "s3://npm-registry-packages-npm-production"}}, "2.1.0": {"name": "cjs-module-lexer", "version": "2.1.0", "description": "Lexes CommonJS modules, returning their named exports metadata", "main": "lexer.js", "exports": {"import": {"types": "./lexer.d.mts", "default": "./dist/lexer.mjs"}, "default": "./lexer.js"}, "types": "lexer.d.ts", "scripts": {"test-js": "mocha -b -u tdd test/*.js", "test-wasm": "cross-env WASM=1 mocha -b -u tdd test/*.js", "test-wasm-sync": "cross-env WASM_SYNC=1 mocha -b -u tdd test/*.js", "test": "npm run test-wasm ; npm run test-wasm-sync ; npm run test-js", "bench": "node --expose-gc bench/index.mjs", "build": "node build.js ; babel dist/lexer.mjs -o dist/lexer.js ; terser dist/lexer.js -o dist/lexer.js", "build-wasm": "make lib/lexer.wasm ; node build.js", "prepublishOnly": "make && npm run build", "footprint": "npm run build && cat dist/lexer.js | gzip -9f | wc -c"}, "author": {"name": "<PERSON>"}, "license": "MIT", "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "cross-env": "^7.0.3", "kleur": "^2.0.2", "mocha": "^9.1.3", "terser": "^4.1.4"}, "repository": {"type": "git", "url": "git+https://github.com/nodejs/cjs-module-lexer.git"}, "bugs": {"url": "https://github.com/nodejs/cjs-module-lexer/issues"}, "homepage": "https://github.com/nodejs/cjs-module-lexer#readme", "_id": "cjs-module-lexer@2.1.0", "gitHead": "c79e9db8bb34699fd50ed8aaed1c0844b479d86d", "_nodeVersion": "22.9.0", "_npmVersion": "10.8.3", "dist": {"integrity": "sha512-UX0OwmYRYQQetfrLEZeewIFFI+wSTofC+pMBLNuH3RUuu/xzG1oz84UCEDOSoQlN3fZ4+AzmV50ZYvGqkMh9yA==", "shasum": "586e87d4341cb2661850ece5190232ccdebcff8b", "tarball": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-2.1.0.tgz", "fileCount": 7, "unpackedSize": 133936, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDOyZMspG+jbfHRy3ul0nLu7UZ9jmS53YYXOIwwExj/DwIgD89oWho/CMCtiywM5xDLBOUGxfJl3MUNHR1b0+Bve/8="}]}, "_npmUser": {"name": "mhdawson1", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/cjs-module-lexer_2.1.0_1740076071112_0.6353111015395525"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-05-14T08:53:02.937Z", "modified": "2025-02-20T18:27:51.446Z", "0.1.0": "2020-05-14T08:53:03.067Z", "0.1.1": "2020-05-14T09:48:59.112Z", "0.1.2": "2020-05-14T09:50:40.081Z", "0.1.3": "2020-05-15T04:44:26.334Z", "0.1.4": "2020-05-15T05:11:28.174Z", "0.2.0": "2020-05-16T00:33:36.214Z", "0.2.1": "2020-05-16T01:32:48.092Z", "0.2.2": "2020-05-16T01:34:51.244Z", "0.2.3": "2020-05-17T08:34:16.482Z", "0.2.4": "2020-05-19T04:33:18.759Z", "0.2.5": "2020-05-29T03:33:27.244Z", "0.2.6": "2020-05-29T03:39:13.544Z", "0.2.7": "2020-06-12T11:45:54.072Z", "0.2.8": "2020-06-13T08:39:07.305Z", "0.2.9": "2020-06-14T01:36:29.196Z", "0.2.10": "2020-06-15T01:18:13.326Z", "0.2.11": "2020-06-28T01:31:04.318Z", "0.2.12": "2020-09-01T22:15:59.675Z", "0.3.0": "2020-09-25T07:52:48.807Z", "0.3.1": "2020-09-27T03:27:31.471Z", "0.3.2": "2020-09-28T04:58:37.875Z", "0.3.3": "2020-09-28T05:11:27.110Z", "0.4.0": "2020-10-04T14:41:46.323Z", "0.4.1": "2020-10-13T20:21:53.245Z", "0.4.2": "2020-10-14T12:45:37.399Z", "0.4.3": "2020-10-21T20:29:31.527Z", "0.5.0": "2020-10-29T17:19:53.872Z", "0.5.1": "2020-10-31T16:06:22.116Z", "0.5.2": "2020-10-31T18:44:18.302Z", "0.6.0": "2020-11-01T22:53:01.966Z", "1.0.0": "2020-11-02T16:06:21.448Z", "1.1.0": "2021-03-11T12:39:59.496Z", "1.1.1": "2021-03-30T18:12:58.999Z", "1.2.0": "2021-04-28T11:01:43.424Z", "1.2.1": "2021-04-29T10:52:25.915Z", "1.2.2": "2021-07-15T19:33:13.875Z", "1.2.3": "2023-06-10T20:09:23.187Z", "1.3.0": "2024-04-27T23:01:58.939Z", "1.3.1": "2024-04-28T05:44:47.868Z", "1.4.0": "2024-08-25T21:38:48.468Z", "1.4.1": "2024-09-07T20:51:50.370Z", "1.4.2": "2025-01-30T17:22:42.737Z", "1.4.3": "2025-01-30T23:21:32.873Z", "2.0.0": "2025-01-31T17:11:31.789Z", "2.1.0": "2025-02-20T18:27:51.293Z"}, "bugs": {"url": "https://github.com/nodejs/cjs-module-lexer/issues"}, "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/nodejs/cjs-module-lexer#readme", "repository": {"type": "git", "url": "git+https://github.com/nodejs/cjs-module-lexer.git"}, "description": "Lexes CommonJS modules, returning their named exports metadata", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "guy<PERSON><EMAIL>"}, {"name": "mhdawson1", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}], "readme": "# CJS Module Lexer\n\n[![Build Status][travis-image]][travis-url]\n\nA [very fast](#benchmarks) JS CommonJS module syntax lexer used to detect the most likely list of named exports of a CommonJS module.\n\nOutputs the list of named exports (`exports.name = ...`) and possible module reexports (`module.exports = require('...')`), including the common transpiler variations of these cases.\n\nForked from https://github.com/guybedford/es-module-lexer.\n\n_Comprehensively handles the JS language grammar while remaining small and fast. - ~90ms per MB of JS cold and ~15ms per MB of JS warm, [see benchmarks](#benchmarks) for more info._\n\n### Project Status\n\nThis project is used in Node.js core for detecting the named exports available when importing a CJS module into ESM, and is maintained for this purpose.\n\nPRs will be accepted and upstreamed for parser bugs, performance improvements or new syntax support only.\n\n_Detection patterns for this project are **frozen**_. This is because adding any new export detection patterns would result in fragmented backwards-compatibility. Specifically, it would be very difficult to figure out why an ES module named export for CommonJS might work in newer Node.js versions but not older versions. This problem would only be discovered downstream of module authors, with the fix for module authors being to then have to understand which patterns in this project provide full backwards-compatibily. Rather, by fully freezing the detected patterns, if it works in any Node.js version it will work in any other. Build tools can also reliably treat the supported syntax for this project as a part of their output target for ensuring syntax support.\n\n### Usage\n\n```\nnpm install cjs-module-lexer\n```\n\nFor use in CommonJS:\n\n```js\nconst { parse } = require('cjs-module-lexer');\n\n// `init` return a promise for parity with the ESM API, but you do not have to call it\n\nconst { exports, reexports } = parse(`\n  // named exports detection\n  module.exports.a = 'a';\n  (function () {\n    exports.b = 'b';\n  })();\n  Object.defineProperty(exports, 'c', { value: 'c' });\n  /* exports.d = 'not detected'; */\n\n  // reexports detection\n  if (maybe) module.exports = require('./dep1.js');\n  if (another) module.exports = require('./dep2.js');\n\n  // literal exports assignments\n  module.exports = { a, b: c, d, 'e': f }\n\n  // __esModule detection\n  Object.defineProperty(module.exports, '__esModule', { value: true })\n`);\n\n// exports === ['a', 'b', 'c', '__esModule']\n// reexports === ['./dep1.js', './dep2.js']\n```\n\nWhen using the ESM version, Wasm is supported instead:\n\n```js\nimport { parse, init } from 'cjs-module-lexer';\n// init() needs to be called and waited upon, or use initSync() to compile\n// Wasm blockingly and synchronously.\nawait init();\nconst { exports, reexports } = parse(source);\n```\n\nThe Wasm build is around 1.5x faster and without a cold start.\n\n### Grammar\n\nCommonJS exports matches are run against the source token stream.\n\nThe token grammar is:\n\n```\nIDENTIFIER: As defined by ECMA-262, without support for identifier `\\` escapes, filtered to remove strict reserved words:\n            \"implements\", \"interface\", \"let\", \"package\", \"private\", \"protected\", \"public\", \"static\", \"yield\", \"enum\"\n\nSTRING_LITERAL: A `\"` or `'` bounded ECMA-262 string literal.\n\nMODULE_EXPORTS: `module` `.` `exports`\n\nEXPORTS_IDENTIFIER: MODULE_EXPORTS_IDENTIFIER | `exports`\n\nEXPORTS_DOT_ASSIGN: EXPORTS_IDENTIFIER `.` IDENTIFIER `=`\n\nEXPORTS_LITERAL_COMPUTED_ASSIGN: EXPORTS_IDENTIFIER `[` STRING_LITERAL `]` `=`\n\nEXPORTS_LITERAL_PROP: (IDENTIFIER  (`:` IDENTIFIER)?) | (STRING_LITERAL `:` IDENTIFIER)\n\nEXPORTS_SPREAD: `...` (IDENTIFIER | REQUIRE)\n\nEXPORTS_MEMBER: EXPORTS_DOT_ASSIGN | EXPORTS_LITERAL_COMPUTED_ASSIGN\n\nEXPORTS_DEFINE: `Object` `.` `defineProperty `(` EXPORTS_IDENFITIER `,` STRING_LITERAL\n\nEXPORTS_DEFINE_VALUE: EXPORTS_DEFINE `, {`\n  (`enumerable: true,`)?\n  (\n    `value:` |\n    `get` (`: function` IDENTIFIER? )?  `() {` return IDENTIFIER (`.` IDENTIFIER | `[` STRING_LITERAL `]`)? `;`? `}` `,`?\n  )\n  `})`\n\nEXPORTS_LITERAL: MODULE_EXPORTS `=` `{` (EXPORTS_LITERAL_PROP | EXPORTS_SPREAD) `,`)+ `}`\n\nREQUIRE: `require` `(` STRING_LITERAL `)`\n\nEXPORTS_ASSIGN: (`var` | `const` | `let`) IDENTIFIER `=` (`_interopRequireWildcard (`)? REQUIRE\n\nMODULE_EXPORTS_ASSIGN: MODULE_EXPORTS `=` REQUIRE\n\nEXPORT_STAR: (`__export` | `__exportStar`) `(` REQUIRE\n\nEXPORT_STAR_LIB: `Object.keys(` IDENTIFIER$1 `).forEach(function (` IDENTIFIER$2 `) {`\n  (\n    (\n      `if (` IDENTIFIER$2 `===` ( `'default'` | `\"default\"` ) `||` IDENTIFIER$2 `===` ( '__esModule' | `\"__esModule\"` ) `) return` `;`?\n      (\n        (`if (Object` `.prototype`? `.hasOwnProperty.call(`  IDENTIFIER `, ` IDENTIFIER$2 `)) return` `;`?)?\n        (`if (` IDENTIFIER$2 `in` EXPORTS_IDENTIFIER `&&` EXPORTS_IDENTIFIER `[` IDENTIFIER$2 `] ===` IDENTIFIER$1 `[` IDENTIFIER$2 `]) return` `;`)?\n      )?\n    ) |\n    `if (` IDENTIFIER$2 `!==` ( `'default'` | `\"default\"` ) (`&& !` (`Object` `.prototype`? `.hasOwnProperty.call(`  IDENTIFIER `, ` IDENTIFIER$2 `)` | IDENTIFIER `.hasOwnProperty(` IDENTIFIER$2 `)`))? `)`\n  )\n  (\n    EXPORTS_IDENTIFIER `[` IDENTIFIER$2 `] =` IDENTIFIER$1 `[` IDENTIFIER$2 `]` `;`? |\n    `Object.defineProperty(` EXPORTS_IDENTIFIER `, ` IDENTIFIER$2 `, { enumerable: true, get` (`: function` IDENTIFIER? )?  `() { return ` IDENTIFIER$1 `[` IDENTIFIER$2 `]` `;`? `}` `,`? `})` `;`?\n  )\n  `})`\n```\n\nSpacing between tokens is taken to be any ECMA-262 whitespace, ECMA-262 block comment or ECMA-262 line comment.\n\n* The returned export names are taken to be the combination of:\n  1. All `IDENTIFIER` and `STRING_LITERAL` slots for `EXPORTS_MEMBER` and `EXPORTS_LITERAL` matches.\n  2. The first `STRING_LITERAL` slot for all `EXPORTS_DEFINE_VALUE` matches where that same string is not an `EXPORTS_DEFINE` match that is not also an `EXPORTS_DEFINE_VALUE` match.\n* The reexport specifiers are taken to be the combination of:\n  1. The `REQUIRE` matches of the last matched of either `MODULE_EXPORTS_ASSIGN` or `EXPORTS_LITERAL`.\n  2. All _top-level_ `EXPORT_STAR` `REQUIRE` matches and `EXPORTS_ASSIGN` matches whose `IDENTIFIER` also matches the first `IDENTIFIER` in `EXPORT_STAR_LIB`.\n\n### Parsing Examples\n\n#### Named Exports Parsing\n\nThe basic matching rules for named exports are `exports.name`, `exports['name']` or `Object.defineProperty(exports, 'name', ...)`. This matching is done without scope analysis and regardless of the expression position:\n\n```js\n// DETECTS EXPORTS: a, b\n(function (exports) {\n  exports.a = 'a'; \n  exports['b'] = 'b';\n})(exports);\n```\n\nBecause there is no scope analysis, the above detection may overclassify:\n\n```js\n// DETECTS EXPORTS: a, b, c\n(function (exports, Object) {\n  exports.a = 'a';\n  exports['b'] = 'b';\n  if (false)\n    exports.c = 'c';\n})(NOT_EXPORTS, NOT_OBJECT);\n```\n\nIt will in turn underclassify in cases where the identifiers are renamed:\n\n```js\n// DETECTS: NO EXPORTS\n(function (e) {\n  e.a = 'a';\n  e['b'] = 'b';\n})(exports);\n```\n\n#### Getter Exports Parsing\n\n`Object.defineProperty` is detected for specifically value and getter forms returning an identifier or member expression:\n\n```js\n// DETECTS: a, b, c, d, __esModule\nObject.defineProperty(exports, 'a', {\n  enumerable: true,\n  get: function () {\n    return q.p;\n  }\n});\nObject.defineProperty(exports, 'b', {\n  enumerable: true,\n  get: function () {\n    return q['p'];\n  }\n});\nObject.defineProperty(exports, 'c', {\n  enumerable: true,\n  get () {\n    return b;\n  }\n});\nObject.defineProperty(exports, 'd', { value: 'd' });\nObject.defineProperty(exports, '__esModule', { value: true });\n```\n\nValue properties are also detected specifically:\n\n```js\nObject.defineProperty(exports, 'a', {\n  value: 'no problem'\n});\n```\n\nTo avoid matching getters that have side effects, any getter for an export name that does not support the forms above will\nopt-out of the getter matching:\n\n```js\n// DETECTS: NO EXPORTS\nObject.defineProperty(exports, 'a', {\n  get () {\n    return 'nope';\n  }\n});\n\nif (false) {\n  Object.defineProperty(module.exports, 'a', {\n    get () {\n      return dynamic();\n    }\n  })\n}\n```\n\nAlternative object definition structures or getter function bodies are not detected:\n\n```js\n// DETECTS: NO EXPORTS\nObject.defineProperty(exports, 'a', {\n  enumerable: false,\n  get () {\n    return p;\n  }\n});\nObject.defineProperty(exports, 'b', {\n  configurable: true,\n  get () {\n    return p;\n  }\n});\nObject.defineProperty(exports, 'c', {\n  get: () => p\n});\nObject.defineProperty(exports, 'd', {\n  enumerable: true,\n  get: function () {\n    return dynamic();\n  }\n});\nObject.defineProperty(exports, 'e', {\n  enumerable: true,\n  get () {\n    return 'str';\n  }\n});\n```\n\n`Object.defineProperties` is also not supported.\n\n#### Exports Object Assignment\n\nA best-effort is made to detect `module.exports` object assignments, but because this is not a full parser, arbitrary expressions are not handled in the\nobject parsing process.\n\nSimple object definitions are supported:\n\n```js\n// DETECTS EXPORTS: a, b, c\nmodule.exports = {\n  a,\n  'b': b,\n  c: c,\n  ...d\n};\n```\n\nObject properties that are not identifiers or string expressions will bail out of the object detection, while spreads are ignored:\n\n```js\n// DETECTS EXPORTS: a, b\nmodule.exports = {\n  a,\n  ...d,\n  b: require('c'),\n  c: \"not detected since require('c') above bails the object detection\"\n}\n```\n\n`Object.defineProperties` is not currently supported either.\n\n#### module.exports reexport assignment\n\nAny `module.exports = require('mod')` assignment is detected as a reexport, but only the last one is returned:\n\n```js\n// DETECTS REEXPORTS: c\nmodule.exports = require('a');\n(module => module.exports = require('b'))(NOT_MODULE);\nif (false) module.exports = require('c');\n```\n\nThis is to avoid over-classification in Webpack bundles with externals which include `module.exports = require('external')` in their source for every external dependency.\n\nIn exports object assignment, any spread of `require()` are detected as multiple separate reexports:\n\n```js\n// DETECTS REEXPORTS: a, b\nmodule.exports = require('ignored');\nmodule.exports = {\n  ...require('a'),\n  ...require('b')\n};\n```\n\n#### Transpiler Re-exports\n\nFor named exports, transpiler output works well with the rules described above.\n\nBut for star re-exports, special care is taken to support common patterns of transpiler outputs from Babel and TypeScript as well as bundlers like RollupJS.\nThese reexport and star reexport patterns are restricted to only be detected at the top-level as provided by the direct output of these tools.\n\nFor example, `export * from 'external'` is output by Babel as:\n\n```js\n\"use strict\";\n\nexports.__esModule = true;\n\nvar _external = require(\"external\");\n\nObject.keys(_external).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  exports[key] = _external[key];\n});\n```\n\nWhere the `var _external = require(\"external\")` is specifically detected as well as the `Object.keys(_external)` statement, down to the exact\nfor of that entire expression including minor variations of the output. The `_external` and `key` identifiers are carefully matched in this\ndetection.\n\nSimilarly for TypeScript, `export * from 'external'` is output as:\n\n```js\n\"use strict\";\nfunction __export(m) {\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\n}\nObject.defineProperty(exports, \"__esModule\", { value: true });\n__export(require(\"external\"));\n```\n\nWhere the `__export(require(\"external\"))` statement is explicitly detected as a reexport, including variations `tslib.__export` and `__exportStar`.\n\n### Environment Support\n\nNode.js 10+, and [all browsers with Web Assembly support](https://caniuse.com/#feat=wasm).\n\n### JS Grammar Support\n\n* Token state parses all line comments, block comments, strings, template strings, blocks, parens and punctuators.\n* Division operator / regex token ambiguity is handled via backtracking checks against punctuator prefixes, including closing brace or paren backtracking.\n* Always correctly parses valid JS source, but may parse invalid JS source without errors.\n\n### Benchmarks\n\nBenchmarks can be run with `npm run bench`.\n\nCurrent results:\n\nJS Build:\n\n```\nModule load time\n> 4ms\nCold Run, All Samples\ntest/samples/*.js (3635 KiB)\n> 299ms\n\nWarm Runs (average of 25 runs)\ntest/samples/angular.js (1410 KiB)\n> 13.96ms\ntest/samples/angular.min.js (303 KiB)\n> 4.72ms\ntest/samples/d3.js (553 KiB)\n> 6.76ms\ntest/samples/d3.min.js (250 KiB)\n> 4ms\ntest/samples/magic-string.js (34 KiB)\n> 0.64ms\ntest/samples/magic-string.min.js (20 KiB)\n> 0ms\ntest/samples/rollup.js (698 KiB)\n> 8.48ms\ntest/samples/rollup.min.js (367 KiB)\n> 5.36ms\n\nWarm Runs, All Samples (average of 25 runs)\ntest/samples/*.js (3635 KiB)\n> 40.28ms\n```\n\nWasm Build:\n```\nModule load time\n> 10ms\nCold Run, All Samples\ntest/samples/*.js (3635 KiB)\n> 43ms\n\nWarm Runs (average of 25 runs)\ntest/samples/angular.js (1410 KiB)\n> 9.32ms\ntest/samples/angular.min.js (303 KiB)\n> 3.16ms\ntest/samples/d3.js (553 KiB)\n> 5ms\ntest/samples/d3.min.js (250 KiB)\n> 2.32ms\ntest/samples/magic-string.js (34 KiB)\n> 0.16ms\ntest/samples/magic-string.min.js (20 KiB)\n> 0ms\ntest/samples/rollup.js (698 KiB)\n> 6.28ms\ntest/samples/rollup.min.js (367 KiB)\n> 3.6ms\n\nWarm Runs, All Samples (average of 25 runs)\ntest/samples/*.js (3635 KiB)\n> 27.76ms\n```\n\n### Wasm Build Steps\n\nThe build uses docker and make, they must be installed first.\n\nTo build the lexer wasm run `npm run build-wasm`.\n\nOptimization passes are run with [Binaryen](https://github.com/WebAssembly/binaryen)\nprior to publish to reduce the Web Assembly footprint.\n\nAfter building the lexer wasm, build the final distribution components\n(lexer.js and lexer.mjs) by running `npm run build`.\n\nIf you need to build lib/lexer.wat (optional) you must first install\n[wabt](https://github.com/WebAssembly/wabt) as a sibling folder to this\nproject. The wat file is then build by running `make lib/lexer.wat`\n\n### Creating a Release\nThese are the steps to create and publish a release. You will need docker\ninstalled as well as having installed [wabt](https://github.com/WebAssembly/wabt)\nas outlined above:\n\n- [ ] Figure out if the release should be semver patch, minor or major based on the changes since\n  the last release and determine the new version.\n- [ ] Update the package.json version, and run a full build and test\n  - npm install\n  - npm run build\n  - npm run test\n- [ ] Commit and tag the changes, pushing up to main and the tag\n  - For example\n    - `git tag -a 1.4.2 -m \"1.4.2\"`\n    - `git push origin tag 1.4.2`\n- [ ] Create the GitHub release\n- [ ] Run npm publish from an account with access (asking somebody with access\n      the nodejs-foundation account is an option if you don't have access.\n      \n### License\n\nMIT\n\n[travis-url]: https://travis-ci.org/guybedford/es-module-lexer\n[travis-image]: https://travis-ci.org/guybedford/es-module-lexer.svg?branch=master\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}