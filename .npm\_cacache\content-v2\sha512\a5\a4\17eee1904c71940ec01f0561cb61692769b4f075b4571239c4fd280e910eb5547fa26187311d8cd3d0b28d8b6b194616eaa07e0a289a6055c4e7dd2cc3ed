{"_id": "which", "_rev": "93-e4a44be15f0bd73e44dde6ee44caf0ec", "name": "which", "dist-tags": {"latest": "5.0.0"}, "versions": {"1.0.0": {"name": "which", "version": "1.0.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "which@1.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bin": {"which": "./bin/which"}, "dist": {"shasum": "31c5f9bd9a939d6a08caf65456a9b660138ca5fc", "tarball": "https://registry.npmjs.org/which/-/which-1.0.0.tgz", "integrity": "sha512-7bnY/v/Pg7mrLQOYG9nd25J2OlD64tK7i+rbb1cpQ/fS+Cf+SG7wxL2Cs6PePwbbr/AqMcTZ5HVvv3Lb0ddekg==", "signatures": [{"sig": "MEUCIH+K5yRaE7ShrDq2HUy/afcv0t8rbdNdSmUeSNnf1j9XAiEAz9QIfWj2sgHd4BLzH4Zi+Xlqn9TcrvIpMpSlAVmIQys=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "engines": {"node": "*"}, "scripts": {}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "1.0.22", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "v0.4.11-pre", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/which/1.0.0/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.0.1": {"name": "which", "version": "1.0.1", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "which@1.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bin": {"which": "./bin/which"}, "dist": {"shasum": "863c91cb0de414808e2dfa4e4473909d5f7945f5", "tarball": "https://registry.npmjs.org/which/-/which-1.0.1.tgz", "integrity": "sha512-i6TcWGCzEmSHvSAYpvGQEzvERiOg364uJ8g+cqEiMXQ8c3LD5cx4pEreNoA86KMkZEd2LWUB5kaNNR6hzVJ/6g==", "signatures": [{"sig": "MEUCIQDh14pxDhYQ+ruHep76/8+gbkmhZxu2GBlrBmCtgLWKagIgVaAwqSnyjZpwTJQnPMtsREH4s1a74A42Y8jywmcS9KA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "engines": {"node": "*"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "1.0.27", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/which/1.0.1/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.0.2": {"name": "which", "version": "1.0.2", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "which@1.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bin": {"which": "./bin/which"}, "dist": {"shasum": "d50433d4935ccf0adf0c4f332c174b300a049415", "tarball": "https://registry.npmjs.org/which/-/which-1.0.2.tgz", "integrity": "sha512-5uN41lyYKF2TLhI+1kXHWDmoNfbZUMew9I1kitd+2tvnqP0zTNkdFDWErIzedWc8Eua3FVry2+Hsc87EEL/7BA==", "signatures": [{"sig": "MEYCIQCSiD3LvFBHe0HvkNiMFnXlo4s0HQ2p/WQ8S+JWVV04OwIhAPXw4L18rCZ7ljAoJqGZLxqXZkeIW/YUWtN1el9k7ayH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "engines": {"node": "*"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "1.0.28-pre-DEV-UNSTABLE", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "v0.4.11", "_npmJsonOpts": {"file": "/Users/<USER>/.npm/which/1.0.2/package/package.json", "wscript": false, "serverjs": false, "contributors": false}, "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true}, "1.0.3": {"name": "which", "version": "1.0.3", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "which@1.0.3", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bin": {"which": "./bin/which"}, "dist": {"shasum": "fa8cecb2fab32f95a48a8c001667130787be6e08", "tarball": "https://registry.npmjs.org/which/-/which-1.0.3.tgz", "integrity": "sha512-UbuL6RFuADs/1AGCpBml9jFgoJC4ifaRZJOHMPlTXGmQ60Kp5vUr60OvNMeeuI0bbUXW55m+PR8faUYI7/s2kQ==", "signatures": [{"sig": "MEQCIHS9XTzMXmHPtkT78gggD9ox0W++iyYHtah8Mb52o5HZAiBI1CW46UqHWCe3ljM/LEplnGhgcY4Bu1XDUWzGnBGVMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "engines": {"node": "*"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "1.1.0-3", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "v0.6.9-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "1.0.5": {"name": "which", "version": "1.0.5", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "which@1.0.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "bin": {"which": "./bin/which"}, "dist": {"shasum": "5630d6819dda692f1464462e7956cb42c0842739", "tarball": "https://registry.npmjs.org/which/-/which-1.0.5.tgz", "integrity": "sha512-p82w9e3628Y1sCI41PbzZb5Qbacst2Yt/gE4mtqrNXv65SmwNbAcQvS4VkehtqmclAcOh36tgQiwhg6fKvkyDA==", "signatures": [{"sig": "MEYCIQDSXggA38EoLCFi7HQp0TXzl+tma8LDmwwAzc8+C+wuZQIhAJqhcyxQS21nL74i6TLYo6RDMAj3Dra1a1gFmmNeUS+T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "engines": {"node": "*"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "1.1.2", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "v0.7.6-pre", "dependencies": {}, "_defaultsLoaded": true, "devDependencies": {}, "_engineSupported": true, "optionalDependencies": {}}, "1.0.6": {"name": "which", "version": "1.0.6", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "_id": "which@1.0.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "cb2ea2e23e07ba01b5ea100f52419bf1f30cc5cb", "tarball": "https://registry.npmjs.org/which/-/which-1.0.6.tgz", "integrity": "sha512-rgu0SjIBFFy7ToxSBgFHl6jkOdWH/G60woXv8DkCXuILjEbUmrlcnQbO9aoOBiW3nMhv4nH4Jh0GtVLkt7sGkw==", "signatures": [{"sig": "MEUCIFYzwkcsjIwWNYtkiMFHSUvwn70Rar9Ru4mkUrK526dgAiEA+/CM4Y+OHZRXBmV63D95Aw6rBua3UkOL/x6GyDAwN9A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "_from": ".", "_shasum": "cb2ea2e23e07ba01b5ea100f52419bf1f30cc5cb", "engines": {"node": "*"}, "gitHead": "c6e318bc3122766c62a661abc5c509e9e2f8aa44", "scripts": {}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "2.1.9", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "0.10.16", "dependencies": {}, "devDependencies": {}}, "1.0.7": {"name": "which", "version": "1.0.7", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@1.0.7", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "853133f40e5bd2783e51a06774959455cc8ee865", "tarball": "https://registry.npmjs.org/which/-/which-1.0.7.tgz", "integrity": "sha512-7AU6+gPuRDL32xy/y719KIfFmCpjbhTlRY6j0XIhG1o0TT1GqfvqWS1sgY57krI12tmeOEB+BsXF4X/1eKr6Zw==", "signatures": [{"sig": "MEUCIGkcPMF4geR1v+YGfxMsfCP/9wM8+9z+XCWKRdH4vlm4AiEAs0U5VzJ5vI52/mfSQfdwxQ63K3HVVQegPrUP0g10zAQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "_from": ".", "_shasum": "853133f40e5bd2783e51a06774959455cc8ee865", "gitHead": "989ba91ddf1864696d5048a98a70dc117ae6ba0c", "scripts": {}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "2.1.9", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "0.10.16"}, "1.0.8": {"name": "which", "version": "1.0.8", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@1.0.8", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "c2ff319534ac4a1fa45df2221b56c36279903ded", "tarball": "https://registry.npmjs.org/which/-/which-1.0.8.tgz", "integrity": "sha512-UV29+FQzB2ZdU3JJrisDvGE3xAvXhAmTwLa3r6E9kefO5/l8OGmd0vv9NZ32ORZlxKwjjsj4vQM5liAewKCkNQ==", "signatures": [{"sig": "MEMCHz8/RfkZ/P3LArf+inG1Da/SMcb1UBd2ebu0Xv5XbecCIHDiHOTxXV0cMQ5lBgWS1JQ7COXrxfkL5h9JWVeWpAbZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "_from": ".", "_shasum": "c2ff319534ac4a1fa45df2221b56c36279903ded", "gitHead": "681a9ebbc447cb428232ddf6c0983006d89e7755", "scripts": {}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "2.1.11", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "0.10.16"}, "1.0.9": {"name": "which", "version": "1.0.9", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@1.0.9", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "460c1da0f810103d0321a9b633af9e575e64486f", "tarball": "https://registry.npmjs.org/which/-/which-1.0.9.tgz", "integrity": "sha512-E87fdQ/eRJr9W1X4wTPejNy9zTW3FI2vpCZSJ/HAY+TkjKVC0TUm1jk6vn2Z7qay0DQy0+RBGdXxj+RmmiGZKQ==", "signatures": [{"sig": "MEYCIQDs/Oh+yDd1f7Hlx6rOl/1kSd+LuT4r28Z0DlNgNb6lzgIhALUrRyg+0U9wOHI9o59atK0BWE9golG4Sq9I301Gbtpj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "_from": ".", "_shasum": "460c1da0f810103d0321a9b633af9e575e64486f", "gitHead": "df3d52a0ecd5f366d550e0f14d67ca4d5e621bad", "scripts": {}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "2.6.0", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "1.1.0"}, "1.1.0": {"name": "which", "version": "1.1.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@1.1.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which#readme", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "c93ef173d4d81938991265e5db5a6866a6568baa", "tarball": "https://registry.npmjs.org/which/-/which-1.1.0.tgz", "integrity": "sha512-A5zWtVU+PjYolCowFnUx2P2o1MeC7N1Qy0G4A9rPd0UmO0qv91rAwQtbwUpEp8Z4AGTzudxdkJQ02beF22lhLw==", "signatures": [{"sig": "MEUCIQDuV2+UQq0lWmR03QHs/cyaFPBqiXLFRbtxUcppzXIC1QIgSvg/+iN/F+5T1NIazSegjRQ3JhXSSbqVJxxFUhRBiUE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "_from": ".", "_shasum": "c93ef173d4d81938991265e5db5a6866a6568baa", "gitHead": "ba01de30247549aaa31838b120d8059bf96c6168", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "2.9.1", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "2.0.1", "dependencies": {"is-absolute": "^0.1.7"}, "devDependencies": {"tap": "^1.0.2", "mkdirp": "^0.5.0", "rimraf": "^2.3.3"}}, "1.1.1": {"name": "which", "version": "1.1.1", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@1.1.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which#readme", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "9ce512459946166e12c083f08ec073380fc8cbbb", "tarball": "https://registry.npmjs.org/which/-/which-1.1.1.tgz", "integrity": "sha512-FgjRix4uC5hAbzCmYO/D0C/6riWHLNWqI2k+9s3P9469R3monfE+/mEyFufdAhhp9pJiw4+Gr4TUI9VEWMPcdA==", "signatures": [{"sig": "MEUCIDmfi9kR8T/1Tl2o3P/4wHiEDcyETWr/F0pH61cccdMIAiEAqa9hjv+w77qLxYdTMh3ofKK90n46gXc7cwj563isbbA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "_from": ".", "_shasum": "9ce512459946166e12c083f08ec073380fc8cbbb", "gitHead": "c80a08e9f8cf7a5c0f39c2e2f87f18f153b118a8", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "2.9.1", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "2.0.1", "dependencies": {"is-absolute": "^0.1.7"}, "devDependencies": {"tap": "^1.0.2", "mkdirp": "^0.5.0", "rimraf": "^2.3.3"}}, "1.1.2": {"name": "which", "version": "1.1.2", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@1.1.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which#readme", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "486c48af6dfecc7a7dcf9c655acf108d2dcbdf3d", "tarball": "https://registry.npmjs.org/which/-/which-1.1.2.tgz", "integrity": "sha512-zRdeMPig0qJ6gzMDjF0kwe7o3BkTuXIcM4Ir4DfjxxLr+D5h5eymaVpryA2RAAn8YFcNvryeytE1aLISVKosxw==", "signatures": [{"sig": "MEQCIBuzYN55gCDYwhRdthl0hM8goDSUh662/o0BR3ex7WBCAiAH6JrcLfD6VJJKZ90+NvqvJj2WfoSiIw6JWwf9qlVsQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "_from": ".", "_shasum": "486c48af6dfecc7a7dcf9c655acf108d2dcbdf3d", "gitHead": "e576e42f0c377571884f844eec58b3ca4a331681", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "3.3.1", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "2.5.0", "dependencies": {"is-absolute": "^0.1.7"}, "devDependencies": {"tap": "^1.0.2", "mkdirp": "^0.5.0", "rimraf": "^2.3.3"}}, "1.2.0": {"name": "which", "version": "1.2.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@1.2.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which#readme", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "a5c8df5abc792f6ce9652c8d9ca8f3a91b77e59d", "tarball": "https://registry.npmjs.org/which/-/which-1.2.0.tgz", "integrity": "sha512-/420cWnf57Wg54+KYShzsvKGCGnmG1CWXtl9zLLzwJ5I1NnHtiIDRRSWd3mgQvcSpmh3amTeZbhncji6mWbk8w==", "signatures": [{"sig": "MEUCIQCwwux5JpeMZ/VxXDD7AiwLeyN9f2u0UHGwuQdO65ZWEgIgE0twB06VhfjMA+Vil3JqUqvDvMwtVwWt99nqwzxRiMw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "_from": ".", "_shasum": "a5c8df5abc792f6ce9652c8d9ca8f3a91b77e59d", "gitHead": "98925d6bced9ba820a17fd857e7a53a491958419", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "3.3.2", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "4.0.0", "dependencies": {"is-absolute": "^0.1.7"}, "devDependencies": {"tap": "^2.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.3.3"}}, "1.2.1": {"name": "which", "version": "1.2.1", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@1.2.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which#readme", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "a010c43aade1a798a3e6c1b1e453d45cb497a2bc", "tarball": "https://registry.npmjs.org/which/-/which-1.2.1.tgz", "integrity": "sha512-yW81tHzyiJtg+nszm26OM2TcMRg7aWVIl/9WUPL+LpKdf6rj7ZOMsb+f356sxivnP6ofbpZXDGqZNIkmBBrPLA==", "signatures": [{"sig": "MEUCIQDnRZy6qUecD8D2+k+UcX80/WU3JaAXeZy1HjZDXF5t7AIgU39B6RD78Rn7Y8jrAYRAuQnNHBNfHSXw4WpV21NXpe0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "_from": ".", "_shasum": "a010c43aade1a798a3e6c1b1e453d45cb497a2bc", "gitHead": "c3b472bd2e13a61a880eca44c76025920a4cb1d5", "scripts": {"test": "tap test/*.js"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "3.3.2", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "4.0.0", "dependencies": {"is-absolute": "^0.1.7"}, "devDependencies": {"tap": "^2.0.0", "mkdirp": "^0.5.0", "rimraf": "^2.3.3"}}, "1.2.4": {"name": "which", "version": "1.2.4", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@1.2.4", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which#readme", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "1557f96080604e5b11b3599eb9f45b50a9efd722", "tarball": "https://registry.npmjs.org/which/-/which-1.2.4.tgz", "integrity": "sha512-zDRAqDSBudazdfM9zpiI30Fu9ve47htYXcGi3ln0wfKu2a7SmrT6F3VDoYONu//48V8Vz4TdCRNPjtvyRO3yBA==", "signatures": [{"sig": "MEUCIQCmwcyizCgMuqjH1JZaO9TySvaHuXb7GKQBH9L1l5xJIgIgcvDE2XdSlrZjlz1FdbbPC890i9VVNcPSvXoQxrNcTEw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "_from": ".", "_shasum": "1557f96080604e5b11b3599eb9f45b50a9efd722", "gitHead": "1375684d40af9de2ecc527d1ab9b87b537d7a1cc", "scripts": {"test": "tap test/*.js --cov"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "2.14.15", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "4.0.0", "dependencies": {"isexe": "^1.1.1", "is-absolute": "^0.1.7"}, "devDependencies": {"tap": "^5.1.1", "mkdirp": "^0.5.0", "rimraf": "^2.3.3"}}, "1.2.5": {"name": "which", "version": "1.2.5", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@1.2.5", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which#readme", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "b0a9be4e49c9828874a82ed23d77468bfdba6c76", "tarball": "https://registry.npmjs.org/which/-/which-1.2.5.tgz", "integrity": "sha512-TP2JexFeStVw7aPQFJ0EpAODIXNBXPl4C4WMKu/oRGD4ypUX2YTQT14zANb4KfZfyuO2w3oMVHGGGYX9XwWn0g==", "signatures": [{"sig": "MEUCIEBJNW4QWCB5ZAUSdUqmGBmu6R/y/YIn1B5STeU4WDaKAiEAk/KfovBSldbcfjozgR1dKGffpjGYIX1dut7PwQ0XuJ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "_from": ".", "files": ["which.js", "bin/which"], "_shasum": "b0a9be4e49c9828874a82ed23d77468bfdba6c76", "gitHead": "f4067f92dd0294ecb5f0c2096eb8bc87ea2f06ec", "scripts": {"test": "tap test/*.js --cov", "changelog": "bash changelog.sh", "postversion": "npm run changelog && git add CHANGELOG.md && git commit -m 'update changelog - '${npm_package_version}"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "5.6.0", "dependencies": {"isexe": "^1.1.1", "is-absolute": "^0.1.7"}, "devDependencies": {"tap": "^5.1.1", "mkdirp": "^0.5.0", "rimraf": "^2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/which-1.2.5.tgz_1462409123177_0.37886844645254314", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.6": {"name": "which", "version": "1.2.6", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@1.2.6", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which#readme", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "d3d6910e26103a04d43b6d2fde5c37cf228f3fa3", "tarball": "https://registry.npmjs.org/which/-/which-1.2.6.tgz", "integrity": "sha512-LqYqLuGhRlLSISBjpU0Cs+vsG6GzAcvlk2KmEOG+gq5kNPz6+ZyWnLr3JTjFdpJNo71P7y/0ZipBh25/1FoxKw==", "signatures": [{"sig": "MEQCIFK3sR9GEdeef0CnNuoTAygDWFTY9wZfHdhSbpGhKrqCAiAfyCCGJBMCPIkY7dVdJhHZFiGlrkP5RucyghYQFXcdrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "_from": ".", "files": ["which.js", "bin/which"], "_shasum": "d3d6910e26103a04d43b6d2fde5c37cf228f3fa3", "gitHead": "10001122b47b661d72d44828227d3cd16a4912e7", "scripts": {"test": "tap test/*.js --cov", "changelog": "bash changelog.sh", "postversion": "npm run changelog && git add CHANGELOG.md && git commit -m 'update changelog - '${npm_package_version}"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "5.6.0", "dependencies": {"isexe": "^1.1.1", "is-absolute": "^0.1.7"}, "devDependencies": {"tap": "^5.1.1", "mkdirp": "^0.5.0", "rimraf": "^2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/which-1.2.6.tgz_1462409283265_0.8679879489354789", "host": "packages-16-east.internal.npmjs.com"}}, "1.2.7": {"name": "which", "version": "1.2.7", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@1.2.7", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which#readme", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "e5f694eaea43fdf5cdb6a66bbef9fba802375c6e", "tarball": "https://registry.npmjs.org/which/-/which-1.2.7.tgz", "integrity": "sha512-3q0hZDxVO6Ag8rxkUGBZOZZmdAWprL3BMVfkesfCq/G0DjlTgoSwkiyQbO91gB/n4qf85QQ5qh50SpqOFdsUsA==", "signatures": [{"sig": "MEUCICdUCi8cEgJer+j4YGRfVZe2BNzpQAxObZFFVBw46COdAiEA/102lKvGViCNZpeev4Mf79UnmlTNwxVGxMg9TPP2iAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "_from": ".", "files": ["which.js", "bin/which"], "_shasum": "e5f694eaea43fdf5cdb6a66bbef9fba802375c6e", "gitHead": "187ca6949701675d839f1c7b68b2159a3d3dbc76", "scripts": {"test": "tap test/*.js --cov", "changelog": "bash changelog.sh", "postversion": "npm run changelog && git add CHANGELOG.md && git commit -m 'update changelog - '${npm_package_version}"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "5.6.0", "dependencies": {"isexe": "^1.1.1", "is-absolute": "^0.1.7"}, "devDependencies": {"tap": "^5.1.1", "mkdirp": "^0.5.0", "rimraf": "^2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/which-1.2.7.tgz_1462409354513_0.5792055949568748", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.8": {"name": "which", "version": "1.2.8", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@1.2.8", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which#readme", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "37fa9f6eab30e49b8ef6eea24681c5799d52ebd6", "tarball": "https://registry.npmjs.org/which/-/which-1.2.8.tgz", "integrity": "sha512-XfjfLsyVmhyY/sVKLAa1ftRcRMKPPDtgPysdBORImBO9rv7gSvMXWC7T2x4jVcESYGHQYsIUFBgiFmL0kuEbuw==", "signatures": [{"sig": "MEUCIQCMpyzFXxQe5UsVfGoDIHAU8kplOUApGP8ZATwdAdRulwIgdm9HchY6ODTPpgeqs99KATF7Ap/QAObcCMIpn18k8pA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "_from": ".", "files": ["which.js", "bin/which"], "_shasum": "37fa9f6eab30e49b8ef6eea24681c5799d52ebd6", "gitHead": "e4de2c25e9163b1f55323792f0fc5806e948ffc1", "scripts": {"test": "tap test/*.js --cov", "changelog": "bash changelog.sh", "postversion": "npm run changelog && git add CHANGELOG.md && git commit -m 'update changelog - '${npm_package_version}"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "5.6.0", "dependencies": {"isexe": "^1.1.1", "is-absolute": "^0.1.7"}, "devDependencies": {"tap": "^5.1.1", "mkdirp": "^0.5.0", "rimraf": "^2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/which-1.2.8.tgz_1462472514341_0.746755798580125", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.9": {"name": "which", "version": "1.2.9", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@1.2.9", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which#readme", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "0b3a0e5c073bc10ca7b9ec13534eeef8a71ab61f", "tarball": "https://registry.npmjs.org/which/-/which-1.2.9.tgz", "integrity": "sha512-in9Hb8KabOpB74FlW6nWmxHUQM2s68/alCKLHlisufhiC2c6sopH99skxQn10pW8iL2Az/NfytNUEl0/qHjFdw==", "signatures": [{"sig": "MEYCIQDW0pmZ2DyHJc/ydSuWINecUUl6YcEdV3fSuJ5V+VL4cwIhANf5LLskszUx8bIfSyA5+XZ8RVXvb1l96xoSt6iQTCyh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "_from": ".", "files": ["which.js", "bin/which"], "_shasum": "0b3a0e5c073bc10ca7b9ec13534eeef8a71ab61f", "gitHead": "34aac93a4c4ee9e3c7a49fe09778ca942e636cce", "scripts": {"test": "tap test/*.js --cov", "changelog": "bash changelog.sh", "postversion": "npm run changelog && git add CHANGELOG.md && git commit -m 'update changelog - '${npm_package_version}"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "3.9.1", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "4.4.4", "dependencies": {"isexe": "^1.1.1"}, "devDependencies": {"tap": "^5.1.1", "mkdirp": "^0.5.0", "rimraf": "^2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/which-1.2.9.tgz_1463603459182_0.9633393425028771", "host": "packages-16-east.internal.npmjs.com"}}, "1.2.10": {"name": "which", "version": "1.2.10", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@1.2.10", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which#readme", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "91cd9bd0751322411b659b40f054b21de957ab2d", "tarball": "https://registry.npmjs.org/which/-/which-1.2.10.tgz", "integrity": "sha512-iSO5sARTg7dEc3mgRW/N8RDYaM3xQKGMFT2oxYPpsdgsGC7kc/qHTwgoHT53o8JqFK+QWiXpwQsdcTgneXq8MA==", "signatures": [{"sig": "MEYCIQD/Zs5HphkQDwxr+kX8Il+lGAKV/xmDZgKY93Eh/FhCnQIhAJbHdvw/b1rGVZUurrRon3AN563pcJ6AkCn3KvEHzqTb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "_from": ".", "files": ["which.js", "bin/which"], "_shasum": "91cd9bd0751322411b659b40f054b21de957ab2d", "gitHead": "3f590834623ee940d922e12b1c8b9cbf24bd5012", "scripts": {"test": "tap test/*.js --cov", "changelog": "bash changelog.sh", "postversion": "npm run changelog && git add CHANGELOG.md && git commit -m 'update changelog - '${npm_package_version}"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "3.9.1", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "4.4.4", "dependencies": {"isexe": "^1.1.1"}, "devDependencies": {"tap": "^5.1.1", "mkdirp": "^0.5.0", "rimraf": "^2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/which-1.2.10.tgz_1465116744337_0.4818702598568052", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.11": {"name": "which", "version": "1.2.11", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@1.2.11", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which#readme", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "c8b2eeea6b8c1659fa7c1dd4fdaabe9533dc5e8b", "tarball": "https://registry.npmjs.org/which/-/which-1.2.11.tgz", "integrity": "sha512-8kWuAZrtPpcfj0UM1rpIJopAawk+Uz+KxNSxk0U0j9fmekidFSRDhyrHOEbD5qePWF6YicG1wSUuFA6BfsE3ig==", "signatures": [{"sig": "MEYCIQCQQPp5jls8EKgauy8OV/9FAkWyo38DxdcBBHceVXrQEQIhAJRgmvUwu+5Xq4WUiFF9mzoLYVcpYM0FUycSKL9a1UMr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "_from": ".", "files": ["which.js", "bin/which"], "_shasum": "c8b2eeea6b8c1659fa7c1dd4fdaabe9533dc5e8b", "gitHead": "8a7d0aa1ca10173f9f4d84bf528a3efc1f5d0c6f", "scripts": {"test": "tap test/*.js --cov", "changelog": "bash gen-changelog.sh", "postversion": "npm run changelog && git add CHANGELOG.md && git commit -m 'update changelog - '${npm_package_version}"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "3.10.7", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"isexe": "^1.1.1"}, "devDependencies": {"tap": "^5.1.1", "mkdirp": "^0.5.0", "rimraf": "^2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/which-1.2.11.tgz_1473093398489_0.1032072464004159", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.12": {"name": "which", "version": "1.2.12", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@1.2.12", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which#readme", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "de67b5e450269f194909ef23ece4ebe416fa1192", "tarball": "https://registry.npmjs.org/which/-/which-1.2.12.tgz", "integrity": "sha512-1RH0iipPxZQ2CHSa6cYPnmseyen7+JkeEqvYLWI4PbJc5gA4/fzeeA0nnCwlHxJoxgWixbRXdFnqj70mkewlVA==", "signatures": [{"sig": "MEQCIDJIbopuB+93pvpxNZ5f1Dg4Oi7QYHNolI4M3k7iIglOAiBYcpi5TOLy0e/Tpp2k66A+ruUPP7zgiqHeU2aJvOzfVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "_from": ".", "files": ["which.js", "bin/which"], "_shasum": "de67b5e450269f194909ef23ece4ebe416fa1192", "gitHead": "5db2078bc2ec50d5c5f3d324e1ffcc2348b9cbbd", "scripts": {"test": "tap test/*.js --cov", "changelog": "bash gen-changelog.sh", "postversion": "npm run changelog && git add CHANGELOG.md && git commit -m 'update changelog - '${npm_package_version}"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"isexe": "^1.1.1"}, "devDependencies": {"tap": "^5.1.1", "mkdirp": "^0.5.0", "rimraf": "^2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/which-1.2.12.tgz_1478902859933_0.6313941152766347", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.13": {"name": "which", "version": "1.2.13", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@1.2.13", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which#readme", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "27b013fe308e2a28c35c768fc9249a4bdb0d486b", "tarball": "https://registry.npmjs.org/which/-/which-1.2.13.tgz", "integrity": "sha512-YSzeSD2GtwUfFgKYUvuZscuizi+E9OyhI/SJhAZKchJ+CZydDlpySTERqVbwkQF6RgFyGhHeziPdCKA+CyYxFA==", "signatures": [{"sig": "MEUCIQCWsI3K5lGMnOnLg21Opxkvmnmujyb+i8LwVYuATDI4SgIgQVuga5IFmn4y4fMe2Xf9+ZRnoFdG50yqbO5wSVkh43E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "_from": ".", "files": ["which.js", "bin/which"], "_shasum": "27b013fe308e2a28c35c768fc9249a4bdb0d486b", "gitHead": "c0ba70cfd21274041489149c206116af4c8d2c09", "scripts": {"test": "tap test/*.js --cov", "changelog": "bash gen-changelog.sh", "postversion": "npm run changelog && git add CHANGELOG.md && git commit -m 'update changelog - '${npm_package_version}"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "4.4.2", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "8.0.0-pre", "dependencies": {"isexe": "^2.0.0"}, "devDependencies": {"tap": "^10.3.0", "mkdirp": "^0.5.0", "rimraf": "^2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/which-1.2.13.tgz_1490248201139_0.47650997736491263", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.14": {"name": "which", "version": "1.2.14", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@1.2.14", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which#readme", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "9a87c4378f03e827cecaf1acdf56c736c01c14e5", "tarball": "https://registry.npmjs.org/which/-/which-1.2.14.tgz", "integrity": "sha512-16uPglFkRPzgiUXYMi1Jf8Z5EzN1iB4V0ZtMXcHZnwsBtQhhHeCqoWw7tsUY42hJGNDWtUsVLTjakIa5BgAxCw==", "signatures": [{"sig": "MEUCIQD96gFHFTu+PrTOEnS76mD0YVR/wXfAXCck2U2HPEvPoQIgeDw9ClZSPxWyNDQjtG7v4r+rpvGNlx7lrPp2LTO0Ldo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "_from": ".", "files": ["which.js", "bin/which"], "_shasum": "9a87c4378f03e827cecaf1acdf56c736c01c14e5", "gitHead": "ae4f02dfacb208fbb19beab08e7946c4e3d524dd", "scripts": {"test": "tap test/*.js --cov", "changelog": "bash gen-changelog.sh", "postversion": "npm run changelog && git add CHANGELOG.md && git commit -m 'update changelog - '${npm_package_version}"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "4.4.2", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "8.0.0-pre", "dependencies": {"isexe": "^2.0.0"}, "devDependencies": {"tap": "^10.3.0", "mkdirp": "^0.5.0", "rimraf": "^2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/which-1.2.14.tgz_1490248705131_0.02947138948366046", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.0": {"name": "which", "version": "1.3.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@1.3.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which#readme", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "ff04bdfc010ee547d780bec38e1ac1c2777d253a", "tarball": "https://registry.npmjs.org/which/-/which-1.3.0.tgz", "integrity": "sha512-xcJpopdamTuY5duC/KnTTNBraPK54YwpenP4lzxU8H91GudWpFv38u0CKjclE1Wi2EH2EDz5LRcHcKbCIzqGyg==", "signatures": [{"sig": "MEYCIQDBLGab9oknedAtpUoKG4apRCyiLmoN4ckmo8iIM+U+2AIhAOLSLlPy+sqKhvjMx94YSuvG/rvrD5wza4kbVsQkxaeA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "which.js", "files": ["which.js", "bin/which"], "gitHead": "6b2de9381d6f6484489187faf24d22ac5bf3d668", "scripts": {"test": "tap test/*.js --cov", "changelog": "bash gen-changelog.sh", "postversion": "npm run changelog && git add CHANGELOG.md && git commit -m 'update changelog - '${npm_package_version}"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"isexe": "^2.0.0"}, "devDependencies": {"tap": "^10.7.0", "mkdirp": "^0.5.0", "rimraf": "^2.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/which-1.3.0.tgz_1501548893969_0.39246653905138373", "host": "s3://npm-registry-packages"}}, "1.3.1": {"name": "which", "version": "1.3.1", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@1.3.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which#readme", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "a45043d54f5805316da8d62f9f50918d3da70b0a", "tarball": "https://registry.npmjs.org/which/-/which-1.3.1.tgz", "fileCount": 6, "integrity": "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==", "signatures": [{"sig": "MEYCIQCq7/3cO/RsJNd79MYW0/Vn0ee30oDefIUI82fWFjjgwwIhAOMPu7x7ZA3tzJRDOiU/tfvZDZFJHvlj5AGjTVg4NZfY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9419, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCI3VCRA9TVsSAnZWagAAILoP/RHSoHTTTbIOg0UG8vtG\nEI7F2ueoGwFFiJAB+xPIEE1B8kP8WqJ3zW2StJ6fY5y53lTDoVyNFKYX9HTI\nBmrhEf2+SaHSYfiG1chBs8MIdatKswhZDy4r+Fyzk50UcgdaaJOBRfer9y8R\nGNISIjep1SQWB+KXhvV2SUg4DkOtcUc9XUHEEopPh9GLGM8HE+aMIYMOvdSK\naSbo8Kq4Ec9Kf8PvPSLV5mU7QFXodqVSkeM4tcZPxi0kGn5ZS/UzpHUian0y\n0EFrfb+IjlXmY7EFIB8Y2aVz8wjowNhAchROdNKAGDzCS4k78vwnSUbYVb5L\nazMs3SoujuYzKxdBo4Ifm/PTfWicgwH1L5vku3zsZ+ss3sgaMjBXDVGc/4bh\nmZ2aPx/DTrySjw6TXySHTbKqFuH6g8n6JWugcGJl0Ai15RNqjojA8FeVR1C0\nabteBcL8ly6pRLX3HwU/l229uFKL0jBVVvtaLsvB3mhMq3k75co36fCz7p13\nqlLXaNvmq6Tu6A0xpRzgkPwmb7DqWqvApz6RulJXobSBn9euJh09dWvMdrgx\n2VzqdpnNkMqXlYtrJEe7bdbCsNRZw/TzYMD+GBabGw4qppGSWkPtYCuTJNE1\nF3QxwZp47woCXnBqPbavSUsVqQQMxHb4y959ltS+0OifWWZy6EU4AG0zv5aL\nTbyd\r\n=c6n/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "which.js", "files": ["which.js", "bin/which"], "gitHead": "563406d75b97f97a33e506b9cc7a82b268332b6f", "scripts": {"test": "tap test/*.js --cov", "changelog": "bash gen-changelog.sh", "postversion": "npm run changelog && git add CHANGELOG.md && git commit -m 'update changelog - '${npm_package_version}"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "6.0.1", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"isexe": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^12.0.1", "mkdirp": "^0.5.0", "rimraf": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/which_1.3.1_1527287252351_0.9093966007084426", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "which", "version": "2.0.0", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@2.0.0", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which#readme", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "dist": {"shasum": "a80cdea6ce49ec6e071edabcba2dc28ac5165ec4", "tarball": "https://registry.npmjs.org/which/-/which-2.0.0.tgz", "fileCount": 6, "integrity": "sha512-+/LPKsk2hvjYlLvcMr8jVybf6s19hBEAh0YqFbfO6Wndsb8jTuk1nzqXTRNn/mP3vlNApPHzvljEu2DyOL8tDA==", "signatures": [{"sig": "MEQCIE42kAL5E1AYYrooTgTqrf/whRlXhcDp5UkTTAVjZ3MgAiBLxixip8CVXTIe01lEQjqydnkpjqwSKM/zNnWsD1P0vw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdlnuYCRA9TVsSAnZWagAAqR4P/3ME1aBblFPEjPQ6GpyP\nt+7rcLAZStKAIF9MkOAPuJhaeIvTJq0yBE3xe5p9Kyrq9TczvE+yLt82kHwz\nHHrsQNj0cfr996bUCftztzw9vWhcv87gFghsBbkYvoOg5S4n8APrzOqMZyjy\nf4x3kxy+83zGtonQAxPejtcOi6JUGP+PJfayC1ORTyHceEIF4fs5mXGKgrff\nljuDaPrEFkmnDqqDWMGbWd6+h7ey91f6/ZmZI6NCCxAz1B+CY65CKIA60CHZ\nijsovDbOkr6d5hZIT8XazUNYHjaVQV/QY3rbVo1tQyn67vR7Or/LBsEMtqGo\n6x7+1M8+MPu8FlZxn+xK7ZFpYva1UKqnVJvnqXeK6aOu4HbwvyJXH0zWuQUe\nUig4ZCWPMlj8xpr7kRzoBiIP6Oh/ozYE3He+clZ641qN+qr4FzfJFDxc2f0U\ntUEBAZOR0Ja2bjnATRN46zaB7cjx7iLXu2GHWlB2NCy/4t24ejBn11sm0kah\nVA54HpqmIXKSMlV1KlkAZ1EcOXaeRDfSbyqiUrRIaTFd+Wpf0etHZVKxz4kk\n7heYe6L0zNb803dOXsITxcI8qqW3mZYDW4HMIitehGPb21y7WpAcGMMfzX0x\nRnFXt4eiJ1MNe8374S2Q5osa9qxwTm/XIcrM2GxSUjMOXPp44cEM+tC6zB7N\nsr29\r\n=UdKk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "which.js", "engines": {"node": ">= 8"}, "gitHead": "9f8d3b3162ea74db06521434bfd94999f02efa5c", "scripts": {"test": "tap test/*.js --cov", "changelog": "bash gen-changelog.sh", "postversion": "npm run changelog && git add CHANGELOG.md && git commit -m 'update changelog - '${npm_package_version}"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "6.12.0-next.0", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "12.8.1", "dependencies": {"isexe": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.6.9", "mkdirp": "^0.5.0", "rimraf": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/which_2.0.0_1570143128307_0.34842007349707305", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "which", "version": "2.0.1", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@2.0.1", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which#readme", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"which": "./bin/which"}, "tap": {"check-coverage": true}, "dist": {"shasum": "f1cf94d07a8e571b6ff006aeb91d0300c47ef0a4", "tarball": "https://registry.npmjs.org/which/-/which-2.0.1.tgz", "fileCount": 6, "integrity": "sha512-N7GBZOTswtB9lkQBZA4+zAXrjEIWAUOB93AvzUiudRzRxhUdLURQ7D/gAIMY1gatT/LTbmbcv8SiYazy3eYB7w==", "signatures": [{"sig": "MEUCIHIj9aqLsHzu5qlfDMZWC4zVQpsOW+BjpCt/qFTia4UyAiEAkvFZbb3XHbZ0QhABbdg927dvT8RGoFAq3g/qReYNKyc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdloPBCRA9TVsSAnZWagAA+BgP/2GJw/9PrSO1HI/LWbZg\nYPT2jaP2bvrHSNYPSVlmHyM2KYXgf0ZbhMtCyXie7g0bbXvpUmA7x0iB4cGc\nuJsGmbOgxqZXWQdF/AqV2bp7VPl+8AB9E8GEWyY5b7Yu9V2h7QTtRz5I1rpH\neBGj8930V/KLSzo32qmWlrfwaoZfB/7LxxtLjRZPIHthBAIRV4e1Jxu9SD9Z\nYYMumdNuvheaxPSQ5vjWnycat8bdyshcqn98LtbdKtsvy3i360f03vPGDaiT\nFlJBMQVe5vA3eKhhcgJH+K6G0PwF6ih5x4OERDVYskPbjAJhM4+X+FpN2jJe\n4CHVXNE+accvMgEuLGc5KmkIn8Hlt9WMD440ISh+usvr1MrKorzdSD3L8UQu\nfc4XfHE6bQEbSOI20mgqgq6WqVlUVcUQPBOeiNseXoZwwr03cPUfbkRMfSyT\n/U5DEh4OIlSbRv4k9WeD4paKM1zJnPjOJRLf5b8iTKOEcxzzDUoQLNT9o5iI\nvl+mG815nYEBI57TTZpOnmfBtW4vv+ZaqYj6H8uaOQHfTV+Mka4WsB0lmYbJ\nbUWWCK1oG2AYIeLJQGNxpvoM85e9CRBtyXwW4MR2q6x6DdNjNn0bwgQSn+yV\nmKmQzjS4rTLE84WbDnH9+46fR76DRbt1DsORQ9WaIV2R0Hrh7c8VJ9ztnXA9\niHfR\r\n=90mr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "which.js", "engines": {"node": ">= 8"}, "gitHead": "8ef60698398872699b5446a06fc579d281a94b91", "scripts": {"test": "tap", "changelog": "git add CHANGELOG.md", "prepublish": "npm run changelog", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish", "prechangelog": "bash gen-changelog.sh", "postchangelog": "git commit -m 'update changelog - '${npm_package_version}"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "6.12.0-next.0", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "12.8.1", "dependencies": {"isexe": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.6.9", "mkdirp": "^0.5.0", "rimraf": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/which_2.0.1_1570145217393_0.887774316286817", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "which", "version": "2.0.2", "author": {"url": "http://blog.izs.me", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "which@2.0.2", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/isaacs/node-which#readme", "bugs": {"url": "https://github.com/isaacs/node-which/issues"}, "bin": {"node-which": "./bin/node-which"}, "tap": {"check-coverage": true}, "dist": {"shasum": "7c6a8dd0a636a0327e10b59c9286eee93f3f51b1", "tarball": "https://registry.npmjs.org/which/-/which-2.0.2.tgz", "fileCount": 6, "integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==", "signatures": [{"sig": "MEYCIQDyPP0ND3fboT3W2mBlojUZmkvbY2KjDxGtB8vG07fjWAIhAPYfINxHQt1SI7Ic6dhnGscWdcYwLfGhKRrIcS6tW8Js", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9975, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3BQjCRA9TVsSAnZWagAAROcQAKSZ8CI1Iv2+wYWgfiWF\nhBylRFmu7Deko4CMQd7U0q2ALJhejNZ3Ld+OTGDPTsL08FOfjMlyyBS5UIAa\nCjBJXWJlqGHEEhnJhZrOzYnUioSJg/uZVevKJdLsO46pAYAOZFUdt29o66pT\nruKcq2LuGNeZx/hv4iah5arJ94p96skBe/J8H7HzJ5ka3FHfF22l6Uy3wkJE\n4R78ldIoRTkWjmOYOdHc+MLn+VFcqIQArUh7DF1PIYvhCigQDs//0IxVft5h\nSk9fKmoHH+Dg27cq/apCSdz/S2QjTRCE58sRE9BmluWl3HMCcGz4kicAu6yg\nRkBlB6Qs5jDWtftA2dYpNEQqc50lsiI2l5ZDyp2xdbT/YLr1jrc/vpg1zTSQ\nL+qH2geftMEQazcIYqEHIwaPtZOu0UCr6wDRlKMMd9FQM6Iq4ERUHcdzDSzX\nGXxDqkGCkVRhT9KTz/VQvMmGx9cNCfeF2AafJw/NUvyYA48/CTCovHSzxUpE\nc/n4WmnZZ2OsWnTsJeFeEG7avXFUPYRByObGSeLg59zuzsW4WcDm7TgNMgmJ\nKUlXea2WFQA4nuvGh3E/FJQhTOwN+pvPK4Wh9BCmgUMkT0QDSUAAJUjfFxpF\nE3akdS+Y+NPQkBk8rCwbUlbuvKTqNFgSABmVZ6djvIyiGsol2ze7i708ZX5c\nXfF1\r\n=W89C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "which.js", "engines": {"node": ">= 8"}, "gitHead": "6a822d836de79f92fb3170f685a6e283fbfeff87", "scripts": {"test": "tap", "changelog": "git add CHANGELOG.md", "prepublish": "npm run changelog", "preversion": "npm test", "postpublish": "git push origin --follow-tags", "postversion": "npm publish", "prechangelog": "bash gen-changelog.sh", "postchangelog": "git commit -m 'update changelog - '${npm_package_version}"}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/isaacs/node-which.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"isexe": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^14.6.9", "mkdirp": "^0.5.0", "rimraf": "^2.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/which_2.0.2_1574115975158_0.6350192484736228", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "which", "version": "3.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "which@3.0.0", "maintainers": [{"name": "fritzy", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-which#readme", "bugs": {"url": "https://github.com/npm/node-which/issues"}, "bin": {"node-which": "bin/which.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "check-coverage": true}, "dist": {"shasum": "a9efd016db59728758a390d23f1687b6e8f59f8e", "tarball": "https://registry.npmjs.org/which/-/which-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-nla//68K9NU6yRiwDY/Q8aU6siKlSs64aEC7+IV56QoAuyQT2ovsJcgGYGyqMOmI/CGN1BOR6mM5EN0FBO+zyQ==", "signatures": [{"sig": "MEQCIFVJ5bm9L3Unj+BESxkCj/hkd7UkGkVFVFPfRRtngw3rAiB50R+TRuOBP2tPrVntqti8/PjbPMAxrdsJXqPEy30/4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYXF4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmosAg/8CRLjAXtxcHnjzsOEqglxoRdQ276u0zPgVVD9UBQresgee6Cw\r\nnxRMs0EsOYBH454XrKIHS47JfqjYvRGMutTKOFiLChY+4oNxLf9lpWDzlsA6\r\n/ozkRacTRDbxUot8m396qNVetfkAQrW71n9oy738zTptP7hJuTyYp9F714jY\r\n5osQQT0Q4L3LrHJLv8vmvnt6FyzuUwX899sKQnJMc8ceOQGm3540NHdLEqQh\r\nPQF46IbXFk9Lln4y9UC5WwX0gFUE0ZO3jSS4M8OnvVTc7il32nEtlVfD22TN\r\nGXeXumAqLQWM8qMTgSTYPLrRG4rHnH+BV2I7bdzj7u1cCfXE1PjJxBK/ktRg\r\nD1tkYB6XU4xjGCQHvvOiYVQhqNHtZsiq+a6EOPoELKmfrY2B0vNitd7yvjMX\r\nLy+Y2GSVfs5yJnPl4IJKFpQOjt7J9AgpKhmpW6iI5emTFmL5intJd8G43CoH\r\nzfaqIF2oVhoIkOa2h0EfE0PyLqqoeTZ6sKvbEUMs7bXQkih1PPm/MvZO2rB9\r\nrBw6AG4ilIS4LxBrTIRz9UuMpgw9o0ASKEqu88zDw8udd8mBJ6EgepPILqhD\r\nKwaIWxTY+jhKYi7q20CM03L77s5LG+Z7BCoRZ/InejHYQrM6NoX/cvgAqo/2\r\n0jn+dHRcqMrYyK95DzPZpZ1bUh73BqxiVqE=\r\n=UC0+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "78d52f46f773e677f9348c2efadeabf06b244eef", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-which.git", "type": "git"}, "_npmVersion": "9.0.1", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "templateOSS": {"version": "4.8.0", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.12.0", "dependencies": {"isexe": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "@npmcli/template-oss": "4.8.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/which_3.0.0_1667330424304_0.13881868227482497", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "which", "version": "3.0.1", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "which@3.0.1", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-which#readme", "bugs": {"url": "https://github.com/npm/node-which/issues"}, "bin": {"node-which": "bin/which.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "check-coverage": true}, "dist": {"shasum": "89f1cd0c23f629a8105ffe69b8172791c87b4be1", "tarball": "https://registry.npmjs.org/which/-/which-3.0.1.tgz", "fileCount": 5, "integrity": "sha512-XA1b62dzQzLfaEOSQFTCOd5KFf/1VSzZo7/7TUjnya6u0vGGKzU96UQBZTAThCb2j4/xjBAyii1OhRLJEivHvg==", "signatures": [{"sig": "MEUCIF5YrHptjJ0We7XmwbnTxb5OEVDIUL7xowQs9rxe3XmSAiEAnVztR4rc6WzHFxwSQTEOe/b2vIBDi7grxnKjb6DbTcs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/which@3.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 7472, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkT/gKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTNg/+IDsC0m3rf6gIdl6zjkaZW5nCmvFyEUYivv6WyN1k764WUfTS\r\nbdFxblAgWB/KAwXBjkoP8dB01tXnjb1nRZ6fW5DugSyLqzyqW1M/GBNtcKb4\r\nVNpV3mMA+MIYgLEt5vgr/ib4qlojCP2VY3nG94jz6nC3kUakyIeLV1Y7uoam\r\n8ThhJ851pExgrm+aYGoPQodXTNLmX0AVqqYasiHlqTCoT1MErMTfnmqOMZKw\r\nc0sBGY3FzShn7d1QLrRG+MG6HIaLKCyScVaXgKy3Hi5XQUKqAsflmf+G/rsS\r\nnWHWDN+oEyqBh1xjcQDIoypQhYMpQa5XkyyydS1S/tmHCF+gsnIMCQAKoOxH\r\nV3SxnGcl4MPxI6ZtBG8EWkzdnwoIahaluHCbwC0BdWj5knwtNJaHJnVfxtEg\r\nIPr/lXZuTNO+VoFirtnffnxHLKhI1wXnwERQacPMyhMM2IoBdoNZUOv822dd\r\nPGEExizanItj+SqTkjZeEWNOnT88MYwaCmTsM7KeoV1ZJWDJHI+908lTi4or\r\ny9w+x/vtH9Ht+wXtZEB7i38USm9zI2ufc17InG0+6aXLf87ld8QAZ95hMQ3v\r\n8CCYveXghwPHy+RYag2J8KBbuezdvArRYq2M+ZaT+pGSI9YVe/4HzOenf1B6\r\nAzyooAreoW8tMu9ZH2kHC7A8HQCYHjboipM=\r\n=tL1V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "gitHead": "261e1cfb6792c1ffacd055b21aa4111c8cb3571e", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-which.git", "type": "git"}, "_npmVersion": "9.6.5", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "templateOSS": {"publish": "true", "version": "4.14.1", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.16.0", "dependencies": {"isexe": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "@npmcli/template-oss": "4.14.1", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/which_3.0.1_1682962441867_0.8931723184122589", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "which", "version": "4.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "which@4.0.0", "maintainers": [{"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}, {"name": "luke<PERSON><PERSON>s", "email": "<EMAIL>"}, {"name": "isaacs", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-which#readme", "bugs": {"url": "https://github.com/npm/node-which/issues"}, "bin": {"node-which": "bin/which.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "check-coverage": true}, "dist": {"shasum": "cd60b5e74503a3fbcfbf6cd6b4138a8bae644c1a", "tarball": "https://registry.npmjs.org/which/-/which-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-GlaYyEb07DPxYCKhKzplCWBJtvxZcZMrL+4UkrTSJHHPyZU4mYYTv3qaOe77H7EODLSSopAUFAc6W8U4yqvscg==", "signatures": [{"sig": "MEYCIQDxKC/VdlmpCGbE2hcOXKsSAUD3InV9ZHni303CQiEJxAIhAPugO1gz+W1lYM2gG1nszdjZhbwB6huvWYVVqEu0BRyH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/which@4.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 7505}, "main": "lib/index.js", "engines": {"node": "^16.13.0 || >=18.0.0"}, "gitHead": "3038155822c2e6f38694843ede2ca03ee841a2e8", "scripts": {"lint": "eslint \"**/*.js\"", "snap": "tap", "test": "tap", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-which.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "templateOSS": {"publish": "true", "version": "4.18.0", "ciVersions": ["16.13.0", "16.x", "18.0.0", "18.x"], "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "18.17.1", "dependencies": {"isexe": "^3.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "@npmcli/template-oss": "4.18.0", "@npmcli/eslint-config": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/which_4.0.0_1693334397153_0.7756464020991809", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "which", "version": "5.0.0", "author": {"name": "GitHub Inc."}, "license": "ISC", "_id": "which@5.0.0", "maintainers": [{"name": "hashtagchris", "email": "<EMAIL>"}, {"name": "reggi", "email": "<EMAIL>"}, {"name": "npm-cli-ops", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fritzy", "email": "<EMAIL>"}, {"name": "gar", "email": "<EMAIL>"}], "homepage": "https://github.com/npm/node-which#readme", "bugs": {"url": "https://github.com/npm/node-which/issues"}, "bin": {"node-which": "bin/which.js"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"], "check-coverage": true}, "dist": {"shasum": "d93f2d93f79834d4363c7d0c23e00d07c466c8d6", "tarball": "https://registry.npmjs.org/which/-/which-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-JEdGzHwwkrbWoGOlIHqQ5gtprKGOenpDHpxE9zVR1bWbOtYRyPPHMe9FaP6x61CmNaTThSkb0DAJte5jD+DmzQ==", "signatures": [{"sig": "MEUCIQCS2eteVPSvsL5TUrOB/UoA4c/f04/wnyN/KL/bv1v6QAIgYxhqQX6hbluit1v/hKWZQpEdfSggGwQzlcOnhwbnLbs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/which@5.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 7477}, "main": "lib/index.js", "engines": {"node": "^18.17.0 || >=20.5.0"}, "gitHead": "b8d11c791296a3baf44703352619621215189b0e", "scripts": {"lint": "npm run eslint", "snap": "tap", "test": "tap", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "lintfix": "npm run eslint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "_npmUser": {"name": "npm-cli-ops", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/npm/node-which.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "directories": {}, "templateOSS": {"publish": "true", "version": "4.23.3", "//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten."}, "_nodeVersion": "22.9.0", "dependencies": {"isexe": "^3.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"tap": "^16.3.0", "@npmcli/template-oss": "4.23.3", "@npmcli/eslint-config": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/which_5.0.0_1727805778596_0.1336750694702682", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2011-08-07T18:36:12.410Z", "modified": "2025-05-14T20:04:27.585Z", "1.0.0": "2011-08-07T18:36:17.514Z", "1.0.1": "2011-09-03T00:20:41.576Z", "1.0.2": "2011-09-13T20:01:41.982Z", "1.0.3": "2012-02-05T07:26:29.424Z", "1.0.4": "2012-03-01T16:26:45.298Z", "1.0.5": "2012-03-02T00:07:45.774Z", "1.0.6": "2014-11-25T10:35:10.788Z", "1.0.7": "2014-11-25T10:38:55.262Z", "1.0.8": "2014-12-06T08:14:03.285Z", "1.0.9": "2015-02-25T20:30:56.254Z", "1.1.0": "2015-05-10T04:01:32.694Z", "1.1.1": "2015-05-10T04:24:28.768Z", "1.1.2": "2015-09-08T23:47:44.246Z", "1.2.0": "2015-10-07T19:12:30.993Z", "1.2.1": "2015-12-22T17:43:12.774Z", "1.2.2": "2016-01-22T02:06:39.513Z", "1.2.3": "2016-01-26T02:15:06.654Z", "1.2.4": "2016-01-26T23:39:48.262Z", "1.2.5": "2016-05-05T00:45:23.648Z", "1.2.6": "2016-05-05T00:48:06.024Z", "1.2.7": "2016-05-05T00:49:14.976Z", "1.2.8": "2016-05-05T18:21:54.829Z", "1.2.9": "2016-05-18T20:31:03.508Z", "1.2.10": "2016-06-05T08:52:24.836Z", "1.2.11": "2016-09-05T16:36:38.725Z", "1.2.12": "2016-11-11T22:21:00.166Z", "1.2.13": "2017-03-23T05:50:01.372Z", "1.2.14": "2017-03-23T05:58:25.358Z", "1.3.0": "2017-08-01T00:54:54.042Z", "1.3.1": "2018-05-25T22:27:32.406Z", "2.0.0": "2019-10-03T22:52:08.445Z", "2.0.1": "2019-10-03T23:26:57.574Z", "2.0.2": "2019-11-18T22:26:15.325Z", "3.0.0": "2022-11-01T19:20:24.475Z", "3.0.1": "2023-05-01T17:34:02.081Z", "4.0.0": "2023-08-29T18:39:57.339Z", "5.0.0": "2024-10-01T18:02:58.779Z"}, "bugs": {"url": "https://github.com/npm/node-which/issues"}, "author": {"name": "GitHub Inc."}, "license": "ISC", "homepage": "https://github.com/npm/node-which#readme", "repository": {"url": "git+https://github.com/npm/node-which.git", "type": "git"}, "description": "Like which(1) unix command. Find the first instance of an executable in the PATH.", "maintainers": [{"email": "<EMAIL>", "name": "gar"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "npm-cli-ops"}, {"email": "<EMAIL>", "name": "reggi"}, {"email": "<EMAIL>", "name": "hashtagchris"}, {"email": "<EMAIL>", "name": "owlstronaut"}], "readme": "# which\n\nLike the unix `which` utility.\n\nFinds the first instance of a specified executable in the PATH\nenvironment variable.  Does not cache the results, so `hash -r` is not\nneeded when the PATH changes.\n\n## USAGE\n\n```javascript\nconst which = require('which')\n\n// async usage\n// rejects if not found\nconst resolved = await which('node')\n\n// if nothrow option is used, returns null if not found\nconst resolvedOrNull = await which('node', { nothrow: true })\n\n// sync usage\n// throws if not found\nconst resolved = which.sync('node')\n\n// if nothrow option is used, returns null if not found\nconst resolvedOrNull = which.sync('node', { nothrow: true })\n\n// Pass options to override the PATH and PATHEXT environment vars.\nawait which('node', { path: someOtherPath, pathExt: somePathExt })\n```\n\n## CLI USAGE\n\nJust like the BSD `which(1)` binary but using `node-which`.\n\n```\nusage: node-which [-as] program ...\n```\n\nYou can learn more about why the binary is `node-which` and not `which` \n[here](https://github.com/npm/node-which/pull/67)\n\n## OPTIONS\n\nYou may pass an options object as the second argument.\n\n- `path`: Use instead of the `PATH` environment variable.\n- `pathExt`: Use instead of the `PATHEXT` environment variable.\n- `all`: Return all matches, instead of just the first one.  Note that\n  this means the function returns an array of strings instead of a\n  single string.\n", "readmeFilename": "README.md", "users": {"bcoe": true, "daizch": true, "wh8766": true, "demoive": true, "itonyyo": true, "pasturn": true, "subchen": true, "paraself": true, "qddegtya": true, "strawhat": true, "aquafadas": true, "retorillo": true, "mysticatea": true, "shipengyan": true, "flumpus-dev": true, "evanshortiss": true, "jakeginnivan": true}}