{"_id": "mimic-fn", "_rev": "15-605fe7c26716b1edbb1db480f4aadced", "name": "mimic-fn", "description": "Make a function mimic another one", "dist-tags": {"latest": "5.0.0"}, "versions": {"1.0.0": {"name": "mimic-fn", "version": "1.0.0", "description": "Make a function mimic another one", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mimic-fn.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["function", "mimic", "imitate", "rename", "copy", "inherit", "properties", "name", "func", "fn", "set", "infer", "change"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "aa6ed7d5bd448aacfb68b249362e2997ca969696", "bugs": {"url": "https://github.com/sindresorhus/mimic-fn/issues"}, "homepage": "https://github.com/sindresorhus/mimic-fn#readme", "_id": "mimic-fn@1.0.0", "_shasum": "93c514dcbfb753539ceb8e7a1f55cbf815580da4", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "93c514dcbfb753539ceb8e7a1f55cbf815580da4", "tarball": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-1.0.0.tgz", "integrity": "sha512-LEeiAHbBHYckIvV1OfM26E7F4pH19hVFZuOMyMYhXxF9ztFz12Y8yjRAqoftbLMbKkDhITd0+ND+P+O6J6tetg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCI5nomJgZ+V8wCJkv+0U03fXqTrEA7d3qzIo/oWkiaHgIhAMqT5KsR/EGdpM+nATbMua6Vpo91CiXQSCN3puDZspaT"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/mimic-fn-1.0.0.tgz_1476898052705_0.4593465782236308"}, "directories": {}}, "1.1.0": {"name": "mimic-fn", "version": "1.1.0", "description": "Make a function mimic another one", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mimic-fn.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["function", "mimic", "imitate", "rename", "copy", "inherit", "properties", "name", "func", "fn", "set", "infer", "change"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "3703ef8142ce6b7170297e58fee1a14799b79975", "bugs": {"url": "https://github.com/sindresorhus/mimic-fn/issues"}, "homepage": "https://github.com/sindresorhus/mimic-fn#readme", "_id": "mimic-fn@1.1.0", "_shasum": "e667783d92e89dbd342818b5230b9d62a672ad18", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.6.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "e667783d92e89dbd342818b5230b9d62a672ad18", "tarball": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-1.1.0.tgz", "integrity": "sha512-h1HSEmsL/ggPjnixiDrEdt2YmXDpeSQlT26BMutTYBxJ46L3nOybRl/aNh/i623y6suDlbgF+m1dqX5V1eCBBQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/j5NKL4Km9hXsN+Ao1g1apkDsv2IFHxpmHQea2QWTkgIgBvSewZRiqKnQKbpZffhohipoD0A4df1Ws6Tax4aNnCE="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/mimic-fn-1.1.0.tgz_1477992909009_0.6083487698342651"}, "directories": {}}, "1.2.0": {"name": "mimic-fn", "version": "1.2.0", "description": "Make a function mimic another one", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mimic-fn.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["function", "mimic", "imitate", "rename", "copy", "inherit", "properties", "name", "func", "fn", "set", "infer", "change"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "d762fc495eef1e48718e1f39b82c39ff5e95dfe4", "bugs": {"url": "https://github.com/sindresorhus/mimic-fn/issues"}, "homepage": "https://github.com/sindresorhus/mimic-fn#readme", "_id": "mimic-fn@1.2.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-jf84uxzwiuiIVKiOLpfYk7N46TSy8ubTonmneY9vrpHNAnp0QBt2BxWV9dO3/j+BoVAb+a5G6YDPW3M5HOdMWQ==", "shasum": "820c86a39334640e99516928bd03fca88057d022", "tarball": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-1.2.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGXAvBn91EsNdSaXeBsi4CXYaVZ1TzabXETiWr1gLqxaAiB5xNXsbIni0E0ZkI/nlGzUBdL/oqDAtfByOb+dU/nNyw=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mimic-fn-1.2.0.tgz_1517542098165_0.264689544448629"}, "directories": {}}, "2.0.0": {"name": "mimic-fn", "version": "2.0.0", "description": "Make a function mimic another one", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mimic-fn.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd-check"}, "keywords": ["function", "mimic", "imitate", "rename", "copy", "inherit", "properties", "name", "func", "fn", "set", "infer", "change"], "devDependencies": {"ava": "^1.3.1", "tsd-check": "^0.3.0", "xo": "^0.24.0"}, "gitHead": "aa885ea38acdc9bfb55c3e7fd0ff2bfa75ccda71", "bugs": {"url": "https://github.com/sindresorhus/mimic-fn/issues"}, "homepage": "https://github.com/sindresorhus/mimic-fn#readme", "_id": "mimic-fn@2.0.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.8.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-jbex9Yd/3lmICXwYT6gA/j2mNQGU48wCh/VzRd+/Y/PjYQtlg1gLMdZqvu9s/xH7qKvngxRObl56XZR609IMbA==", "shasum": "0913ff0b121db44ef5848242c38bbb35d44cabde", "tarball": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.0.0.tgz", "fileCount": 5, "unpackedSize": 3582, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgWo1CRA9TVsSAnZWagAAMroP/3gX8/w+muTYgS1KGl5D\nWXswvPjGUlwgiBqSmAs0sWif3ewB/SoKDKhnGIaOQDNV1TsMiNc8kKZOss2p\ny1IbanOW1ITLD6tfSWTtXaHCGKatDJFCxnPDxWD+w5+N4F8UMA98DMvL78p6\nLZih91DLSfe1MCn4oSRvunW++TSfZWOYDQrqyIM6639yqh5zD/Ki/Tk0bLJR\n2sFJSkxTPTBRUq7LicgH+tQaDCdbEF6eS3ioVSIar5qx3I2i4HqhZyVKTekp\na3T0E9dsyOxokJM7o3zGkXOuSf+R3CnHB/J31Y1ociwTaywdVq0N8mEn0xHo\nYKcpgLmfCB64uvVcBOxzAw8LuarzzbL/RtLygVxxRTl/+JsGrDLFt8yDKyiV\nqgyZY/qeenGskuvQNaiBK7BxdGNzPvnynSfWWP/JL01kGtERTOLTlVS/cAi3\njmQtd2kXIcEnrsBu6CtHLe4FOm6wodhRSpyrhB559IoDCGE6RpC38WFvzZp+\n6wRIAVV11ObhYaLqWKn8RadjUZWlAGRZZDOzSdFGP5fj7EnEPnpjOW4UZWKZ\nWyBGkNa5E9T7qKBSrM+xJTVBk+wBMuBjOh4Jd60fmx4yTsgZx3p/5hYOFmsf\nxvqXqyMc0zAx42sanZQGOD41uXRgdjL920k0lBI+EBDIU8shEa2FBVXa/UhA\n7hL+\r\n=BzEr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDblWghlQxfCf5yQGn3tv6yLmV8AwiMV/E/fOsQOAbnJgIgPO5GhuEF9QBc7xcCV/wOtGgBBABtpfZCfh3zZlD3cZI="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mimic-fn_2.0.0_1551985204769_0.7133439914854229"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "mimic-fn", "version": "2.1.0", "description": "Make a function mimic another one", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mimic-fn.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["function", "mimic", "imitate", "rename", "copy", "inherit", "properties", "name", "func", "fn", "set", "infer", "change"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "gitHead": "d29f5387f4c19814a042d03a780ff4481ceac5a3", "bugs": {"url": "https://github.com/sindresorhus/mimic-fn/issues"}, "homepage": "https://github.com/sindresorhus/mimic-fn#readme", "_id": "mimic-fn@2.1.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==", "shasum": "7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b", "tarball": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz", "fileCount": 5, "unpackedSize": 4457, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcoP6eCRA9TVsSAnZWagAABhUP/0r53cJjmLHgRJsHmF5j\nVbo/ZfYjnaYVOtWnhLMqKckmQyZwFrlWnVqgvpAX+9zT4mwWQrs9b5BklbRH\nEW0EnFbfov2GtzngwpLlEFu1I8zCcwtSNgXFLjC2zRBIyoZwfMcmR8AnVXVE\nYeKl7lpF+k2UO6e6qBqaTfOyiHfaT2KqGHgrojvi2807BNL+mAiwt/HOA+A/\nj47kuPGK3gwvfoXqU0FFHtvx8/cF4KxJuL2bOqOfJRFMvDYzszIn9hMsODMN\nivLzdtqU2KN4GVALWdgIgmBOiivCvQWfeveJpN+FCGS+tqhI8pA++jai5Bc9\noplfu08LTp44MxEWNqA/4ZvYNtGqdDhfuCbkYJfWs1JVsdMiy+/+GjOg9GSr\nuMAvgO7xCjg/4bmt5xhGRf458zNHcK85M0N6E4bTZUCnUlpmwVtNYyv5IG0r\nKANhkq2vRyzb6XbNgWgaTuV75EA/qtlF2aM3MJqn0abyfOb/KbsADYNFLF13\nYnWKZLL0zyIzNTa8yMggOa4hIM+qczWoPbxXQYWNUKjheFWw4Z0HXJG9DiN5\nLe0CBrtqUB9RNuFSPpfySoGQZ26iGeLTVMCYSouoednp/5iqD5/uKcRqhmEE\nZ/LhXFoCea95amej1cFLV/4kKp8XnY2aN8cmawYtTTTL8QKpro534KGlPg0h\nYOd+\r\n=2q3H\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDxU+rLv+cNZnG5Ij14sX0PMBPR9oXTreQjipFjWhEWEQIgFdCUrsiBSFkkylqnzlC9y5B65EKgtdbZPvlr/VvFBqo="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mimic-fn_2.1.0_1554054813982_0.46010347442389166"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "mimic-fn", "version": "3.0.0", "description": "Make a function mimic another one", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mimic-fn.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["function", "mimic", "imitate", "rename", "copy", "inherit", "properties", "name", "func", "fn", "set", "infer", "change"], "devDependencies": {"ava": "^2.1.0", "tsd": "^0.7.1", "xo": "^0.24.0"}, "gitHead": "a08e9cdca1b94f0cae6e11d2d805f2205a153878", "bugs": {"url": "https://github.com/sindresorhus/mimic-fn/issues"}, "homepage": "https://github.com/sindresorhus/mimic-fn#readme", "_id": "mimic-fn@3.0.0", "_nodeVersion": "12.4.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-PiVO95TKvhiwgSwg1IdLYlCTdul38yZxZMIcnDSFIBUm4BNZha2qpQ4GpJ++15bHoKDtrW2D69lMfFwdFYtNZQ==", "shasum": "76044cfa8818bbf6999c5c9acadf2d3649b14b4b", "tarball": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-3.0.0.tgz", "fileCount": 5, "unpackedSize": 7974, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdAnS5CRA9TVsSAnZWagAAjLIP/RhJrjpk1/tjsB8+LVq5\nZntLv9nY2kv5incmfrpbM7SZr6UbPaC2Az6GSoYbmZj5Gvp+Byzo5wVg1OWn\nXBr1lahmNUjAyMOEoSdKNv1CBpIzE6ShjelYuPt6Lifn+2kABzG8NvrrALri\nHfKAHYDFxSFb60WdVc+R/8nDgPlac5Tcd5nyyUACXFdseskIDBBOADVi+Uis\nXJRLVanRlrwiYB4RJqJGeEk1YhhnxTqxViRG7zIFUKJS/sdrkBPQosqtqkMt\n1RkgBhefaLj39F231sRrdl0W1lRu96doig7+zoVhj9m4K/HyGseVGVSTnj9+\nOOhk0R21BJ+7N+uJqIucUdVfFXR4GMF4dcTtZJ4wupr0YENNF1kDG+FaklKN\nALI7dqfCQX8+O/wBI481xLNs40+rJb63Ume8VZmhyaAvZkq21CfWLRecyHAh\nCQ4RvGUcuwOWJPJRoj4uoAOAsk9yiNgkq1LtINPMTGkId/npy5xL7q2vxjvz\nqZ7TzCs9E2YVWOUhmskee7SNFB+hOTeKugLyjOKisGkh2CVF7q1OiB7a54d5\n/J54/JdqDDVsujtJVs3L9MPCSF+zLgcKZRdHYHH5uBnOSj/NoCvWxelpgDs3\nnzNYpORi3eFCdcDLDbKkV8RhZIVO3vy+Uk8kpvepoD8O99LpVdNaC+p37hqr\n8zR0\r\n=2irg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDubU18JXuIWqx3q+bHbnRSdRNcpnJy48sgRj56GxrrSAiA76mOcRpXqATOlAoXKm9IRG2ip5CU+tr3RIFv6d26IrQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mimic-fn_3.0.0_1560442040132_0.7815444311909043"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "mimic-fn", "version": "3.1.0", "description": "Make a function mimic another one", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mimic-fn.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["function", "mimic", "imitate", "rename", "copy", "inherit", "properties", "name", "func", "fn", "set", "infer", "change"], "devDependencies": {"ava": "^2.1.0", "tsd": "^0.7.1", "xo": "^0.24.0"}, "gitHead": "2ada36ff62c03d97da9604c8a2f22a5153c87b06", "bugs": {"url": "https://github.com/sindresorhus/mimic-fn/issues"}, "homepage": "https://github.com/sindresorhus/mimic-fn#readme", "_id": "mimic-fn@3.1.0", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.5", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Ysbi9uYW9hFyfrThdDEQuykN4Ey6BuwPD2kpI5ES/nFTDn/98yxYNLZJcgUAKPT/mcrLLKaGzJR9YVxJrIdASQ==", "shasum": "65755145bbf3e36954b949c16450427451d5ca74", "tarball": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-3.1.0.tgz", "fileCount": 5, "unpackedSize": 8244, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGqZBCRA9TVsSAnZWagAA9+EP/jPxwLw6SVZTo9Z19At9\n2Uh3/V+6tLuZHOdiaSSzOhqQWHzhuXmnmLe4jNOZ4LwTN7fGaD/HEkC6NqrR\njYGRL6KHDCaArr3RffVJ5Vqy8F2xBowYPwnOIKBbkBzVvmbFHmf708b9U8ed\nTb7ZLDRdNgyCS67GoX2Bs1SCNZJrCzFcjqcY/EP4vYeTxyEljpbamrsdLvZC\nlv4NA4/zGfABgynozKa3McBplcgOmBqM1TYjsTQ00ClluUbcb8954YNEDqal\nPqBYUocgQCbsdeYIuGlQWOLbB1IgFrb0hgRpr3m7igEHK3vURN4xf5KozpWa\nC24Mc0k8Nwq5D7FJ2Sdv1oJwqJk3kA5Br1alnBO4rgoySNc0kIagznbSBk+v\nfaEsJoVzhHzmIW+27fpFs/1nDim2CnCOijsyoGccuXxd+WWH/snFq7+wBsrs\n/MIrOIu0XiSOjZRIdKQec9/5N4XvwzBCGEtmb9Ti6K9h4cLEpZY65sKfSCP+\nW2jFzVzwbTBz2sihgvxNSKIi/bJZdzb78AxF84jcxd3p2xV6OrGjPBfES8dA\nt/SGJvsc39QQZRZxxEpTXKRphlthds18thJQiE+M0c6IdoJERnXLdVu476Az\nurwci7xIkB8lJHuyRzOpKk/iW3ulzoxL1nsarpioLOK1LBFZwurUuQgj0u1s\n9/1Y\r\n=UMkg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB0rmkv1jTo6iYyuI44ph64qzMI8nS4dmjSJVWlV93DFAiEAlnF5HD7Cp13LhLKEidtHZY3CUu0Sn/bqxvCHTQfidOs="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mimic-fn_3.1.0_1595582016684_0.8596377806235356"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "mimic-fn", "version": "4.0.0", "description": "Make a function mimic another one", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mimic-fn.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["function", "mimic", "imitate", "rename", "copy", "inherit", "properties", "name", "func", "fn", "set", "infer", "change"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "gitHead": "c8994d7c70a5e1e74efe97e12394b9878f943dab", "bugs": {"url": "https://github.com/sindresorhus/mimic-fn/issues"}, "homepage": "https://github.com/sindresorhus/mimic-fn#readme", "_id": "mimic-fn@4.0.0", "_nodeVersion": "12.20.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-vqiC06CuhBTUdZH+RYl8sFrL096vA45Ok5ISO6sE/Mr1jRbGH4Csnhi8f3wKVl7x8mO4Au7Ir9D3Oyv1VYMFJw==", "shasum": "60a90550d5cb0b239cca65d893b1a53b29871ecc", "tarball": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-4.0.0.tgz", "fileCount": 5, "unpackedSize": 8179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbga+CRA9TVsSAnZWagAATlIP/igCx9O2dNaUZhb+guHI\nbYmJX/6G21vPmmte4Qkmj29LRvgRuW3WTbTDpPvC9O1Jms5DXa8bCir2yVpY\n7NxVvkzmTzoXjtwk+uyJEUNMyUDCjAuo+IgntUzHWOrrCd27DcXZ53S43kMY\n109gh4xvik9OBClVomrOLZ878/rf/kXpm/XZ9jYQX4L3vlF+ZvsZXa5gaUxv\nmgStWWj+OBfRl/AcnoK5NDYu3YSUZjRiioV/b/cCe1KbBs8uN1FEwIGaRZh1\nYDnLTHeBwuyi8H53+ee/Gr073vgbqRSvF4f8L7adJlrrcJJvdy0TU1PL7Ri8\n2B0yTqCBpje3lNlgeZ1sCJGXo2QAVkOQvd0ONZ0ninB5SHlyXB/ylwdgUW4P\n2/+uwpA51k1i6h9Ui6bsRmx/Rb8pyw6HaAOgbBu8chCDgHN+fTJ04SQagl+J\nAZkHl1gQ84r+7TyJzzBXMgcb0+mKmkEPtzUN36APePcSBU5DEbAXAUgJ9YJq\n2V24Yo4hvWzWTMqX36X8WeEycqx5NCh/20/svLp9l1yuo7dlKjOaa2nTc7v4\nuSYaopBbWHX34mSQYWAMqx+RD7YMGRHOtAKYFA4GKj4oHxFMB5CrOfmJqam9\nNA38UDFmSwrFpYw6R92ouy/2Jh5zSvSMIkNkHyXPJmHZup0JCaA+dcigChOp\ngm52\r\n=3Hkg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDB23w2ytWRy5TujWc6rY5/HXVBk2pl95p4R/9KlOvoMgIhAOpSN7vhamMldi6U79H5kStFjT8CZi1KF2DC/EKxygu9"}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mimic-fn_4.0.0_1617823422055_0.746416395003386"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "mimic-fn", "version": "5.0.0", "description": "Make a function mimic another one", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mimic-fn.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["function", "mimic", "imitate", "rename", "copy", "inherit", "properties", "name", "func", "fn", "set", "infer", "change"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "types": "./index.d.ts", "gitHead": "c8994d7c70a5e1e74efe97e12394b9878f943dab", "bugs": {"url": "https://github.com/sindresorhus/mimic-fn/issues"}, "homepage": "https://github.com/sindresorhus/mimic-fn#readme", "_id": "mimic-fn@5.0.0", "_nodeVersion": "20.9.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-VqpBkpWXT3jLrdlFSgrx0z9r1kuIqEudeXDGWTTaOhIKEny5XYqLesgGQSa7Nlsde1jrMqGM/zF3w0zhQUc6Ag==", "shasum": "a2bdb66dcd0731d0ad9c9a2235b43582f9d71564", "tarball": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-5.0.0.tgz", "fileCount": 5, "unpackedSize": 8179, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJQkIpE53IdVufWo10bSZgJm61EbrzeRwObi67vdYIpAIgbL3hFYOriV0wwEN3HrIc1BjPPBQX92IcuvyYRd9fzqc="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mimic-fn_5.0.0_1699214099162_0.20470565228903603"}, "_hasShrinkwrap": false, "deprecated": "Renamed to mimic-function"}}, "readme": "<img src=\"media/logo.svg\" alt=\"mimic-fn\" width=\"400\">\n<br>\n\n> Make a function mimic another one\n\nUseful when you wrap a function in another function and like to preserve the original name and other properties.\n\n## Install\n\n```\n$ npm install mimic-fn\n```\n\n## Usage\n\n```js\nimport mimicFunction from 'mimic-fn';\n\nfunction foo() {}\nfoo.unicorn = '🦄';\n\nfunction wrapper() {\n\treturn foo();\n}\n\nconsole.log(wrapper.name);\n//=> 'wrapper'\n\nmimicFunction(wrapper, foo);\n\nconsole.log(wrapper.name);\n//=> 'foo'\n\nconsole.log(wrapper.unicorn);\n//=> '🦄'\n\nconsole.log(String(wrapper));\n//=> '/* Wrapped with wrapper() */\\nfunction foo() {}'\n```\n\n\n## API\n\n### mimicFunction(to, from, options?)\n\nModifies the `to` function to mimic the `from` function. Returns the `to` function.\n\n`name`, `displayName`, and any other properties of `from` are copied. The `length` property is not copied. Prototype, class, and inherited properties are copied.\n\n`to.toString()` will return the same as `from.toString()` but prepended with a `Wrapped with to()` comment.\n\n#### to\n\nType: `Function`\n\nMimicking function.\n\n#### from\n\nType: `Function`\n\nFunction to mimic.\n\n#### options\n\nType: `object`\n\n##### ignoreNonConfigurable\n\nType: `boolean`\\\nDefault: `false`\n\nSkip modifying [non-configurable properties](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/getOwnPropertyDescriptor#Description) instead of throwing an error.\n\n## Related\n\n- [rename-fn](https://github.com/sindresorhus/rename-fn) - Rename a function\n- [keep-func-props](https://github.com/ehmicky/keep-func-props) - Wrap a function without changing its name and other properties\n\n---\n\n<div align=\"center\">\n\t<b>\n\t\t<a href=\"https://tidelift.com/subscription/pkg/npm-mimic-fn?utm_source=npm-mimic-fn&utm_medium=referral&utm_campaign=readme\">Get professional support for this package with a Tidelift subscription</a>\n\t</b>\n\t<br>\n\t<sub>\n\t\tTidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.\n\t</sub>\n</div>\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-11-05T19:55:23.083Z", "created": "2016-10-19T17:27:32.921Z", "1.0.0": "2016-10-19T17:27:32.921Z", "1.1.0": "2016-11-01T09:35:10.608Z", "1.2.0": "2018-02-02T03:28:18.400Z", "2.0.0": "2019-03-07T19:00:04.908Z", "2.1.0": "2019-03-31T17:53:34.125Z", "3.0.0": "2019-06-13T16:07:20.324Z", "3.1.0": "2020-07-24T09:13:36.810Z", "4.0.0": "2021-04-07T19:23:42.197Z", "5.0.0": "2023-11-05T19:54:59.336Z"}, "homepage": "https://github.com/sindresorhus/mimic-fn#readme", "keywords": ["function", "mimic", "imitate", "rename", "copy", "inherit", "properties", "name", "func", "fn", "set", "infer", "change"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/mimic-fn.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/mimic-fn/issues"}, "license": "MIT", "readmeFilename": "readme.md"}