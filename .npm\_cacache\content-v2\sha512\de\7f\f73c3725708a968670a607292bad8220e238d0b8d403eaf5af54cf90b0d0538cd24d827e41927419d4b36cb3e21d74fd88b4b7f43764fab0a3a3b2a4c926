{"_id": "babel-plugin-macros", "_rev": "50-90b6f727b9f19d9cff95c049630e5367", "name": "babel-plugin-macros", "description": "Allows you to build compile-time libraries", "dist-tags": {"latest": "3.1.0"}, "versions": {"0.0.1": {"name": "babel-plugin-macros", "version": "0.0.1", "description": "Macros for JavaScript via a babel plugin.", "main": "lib/index.js", "scripts": {"build": "babel -d ./lib ./src", "prepublish": "npm run build", "pretest": "npm run build", "test": "mocha", "watch": "mocha --watch"}, "repository": {"type": "git", "url": "https://github.com/codemix/babel-plugin-macros"}, "keywords": ["babel", "babel-plugin", "macros", "ast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/codemix/babel-plugin-macros/issues"}, "homepage": "https://github.com/codemix/babel-plugin-macros", "devDependencies": {"mocha": "^2.2.4", "should": "^6.0.1", "babel": "^5.0.0"}, "dependencies": {"lodash": "^3.8.0"}, "_id": "babel-plugin-macros@0.0.1", "_shasum": "b33cc8396254a80622b8a76d1c958b003e5cd70e", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "codemix", "email": "<EMAIL>"}, "maintainers": [{"name": "codemix", "email": "<EMAIL>"}], "dist": {"shasum": "b33cc8396254a80622b8a76d1c958b003e5cd70e", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-0.0.1.tgz", "integrity": "sha512-yZj8QmGXL2d0F13ORV/9yS+n6/oyf0GIE8ezPRM5JF8gkuEIaumKdvzJBN4q0y3pCw4eiYGw+eaLzXnlGd0kaw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBh71rDAwbnot1BcLp99XIv+Grqaf4Jav42f2o3gR9j3AiEA3L30MLp+JgAVK5IMZoN0Su/Mp+VwjbXTFfGKdQfemjk="}]}, "directories": {}}, "1.0.0": {"name": "babel-plugin-macros", "version": "1.0.0", "description": "Macros for JavaScript via a babel plugin.", "main": "lib/index.js", "scripts": {"build": "babel -d ./lib ./src", "prepublish": "npm run build", "pretest": "npm run build", "test": "mocha", "watch": "mocha --watch"}, "repository": {"type": "git", "url": "git+https://github.com/codemix/babel-plugin-macros.git"}, "keywords": ["babel", "babel-plugin", "macros", "ast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/codemix/babel-plugin-macros/issues"}, "homepage": "https://github.com/codemix/babel-plugin-macros", "devDependencies": {"babel-cli": "^6.4.5", "babel-core": "^6.4.5", "babel-plugin-transform-flow-strip-types": "^6.4.0", "babel-preset-es2015": "^6.3.13", "babel-register": "^6.4.3", "mocha": "^2.3.4", "should": "^6.0.3"}, "dependencies": {"lodash": "^4.0.0"}, "gitHead": "01d6668b2bf37df62ec970c4ca64c7e71126ab60", "_id": "babel-plugin-macros@1.0.0", "_shasum": "c2baca9688150815432649ec6e8205dbd0969b27", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.2.0", "_npmUser": {"name": "codemix", "email": "<EMAIL>"}, "maintainers": [{"name": "codemix", "email": "<EMAIL>"}], "dist": {"shasum": "c2baca9688150815432649ec6e8205dbd0969b27", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-1.0.0.tgz", "integrity": "sha512-n2UQ9qVgy1A9TlFBM79lKfsSUR8LyqPVk7pPya45VATqj1c455xhJxCHqwjWn5BV8Uh2NHKZPKX8c6yx8Vs3RQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDUfwgBwzjbQRFSbA/O9B+v1EMCK2TQPfz6H0doMNoL0AIhAPXF81jxPvQFkkJ9t8mNzB1/coT65p2r42iZCrRLsXS1"}]}, "directories": {}}, "1.0.1": {"name": "babel-plugin-macros", "version": "1.0.1", "description": "Macros for JavaScript via a babel plugin.", "main": "lib/index.js", "scripts": {"build": "babel -d ./lib ./src", "prepublish": "npm run build", "pretest": "npm run build", "test": "mocha", "watch": "mocha --watch"}, "repository": {"type": "git", "url": "git+https://github.com/codemix/babel-plugin-macros.git"}, "keywords": ["babel", "babel-plugin", "macros", "ast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/codemix/babel-plugin-macros/issues"}, "homepage": "https://github.com/codemix/babel-plugin-macros", "devDependencies": {"babel-cli": "^6.4.5", "babel-core": "^6.4.5", "babel-plugin-transform-flow-strip-types": "^6.4.0", "babel-preset-es2015": "^6.3.13", "babel-register": "^6.4.3", "mocha": "^2.3.4", "should": "^6.0.3"}, "dependencies": {"lodash": "^4.0.0"}, "gitHead": "088bbbed3a649ecc7685e52bd623df1e57d28d66", "_id": "babel-plugin-macros@1.0.1", "_shasum": "bb92127749d349cc3eb0c91fd1535f4d231a3ffb", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.2.0", "_npmUser": {"name": "codemix", "email": "<EMAIL>"}, "maintainers": [{"name": "codemix", "email": "<EMAIL>"}], "dist": {"shasum": "bb92127749d349cc3eb0c91fd1535f4d231a3ffb", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-1.0.1.tgz", "integrity": "sha512-a/ErthwczKsq5VFtThoQ5BzbV6JusA0zEvnLty4dhe4eaBdnszwGFPRftb6mKwvo/Xku7p6HF7OCZlSliSCRZg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCWQT6SvweQRPtIXksIYFKjvWlWQPMY8hVlHZmfMRG73wIhALocZLmP3ruQQsW/TWbdFkKH3uXfBlBYJhdrVBUgiiZ6"}]}, "directories": {}}, "1.0.4": {"name": "babel-plugin-macros", "version": "1.0.4", "description": "Macros for JavaScript via a babel plugin.", "main": "lib/index.js", "scripts": {"build": "babel -d ./lib ./src", "prepublish": "npm run build", "pretest": "npm run build && npm run lint", "test": "mocha", "watch": "mocha --watch", "lint": "eslint ./src/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/codemix/babel-plugin-macros.git"}, "keywords": ["babel", "babel-plugin", "macros", "ast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/codemix/babel-plugin-macros/issues"}, "homepage": "https://github.com/codemix/babel-plugin-macros", "devDependencies": {"babel-cli": "^6.4.5", "babel-core": "^6.4.5", "babel-eslint": "^4.1.6", "babel-plugin-transform-flow-strip-types": "^6.4.0", "babel-preset-es2015": "^6.3.13", "babel-register": "^6.4.3", "eslint": "^1.10.3", "mocha": "^2.3.4", "should": "^6.0.3"}, "dependencies": {"lodash": "^4.0.0"}, "gitHead": "11ac31ef3d451c315bc324c71473df198ac7f1c6", "_id": "babel-plugin-macros@1.0.4", "_shasum": "53816d0ed0535e99bcbed695083d0a6ddf4e523c", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.0", "_npmUser": {"name": "gvozd", "email": "<EMAIL>"}, "dist": {"shasum": "53816d0ed0535e99bcbed695083d0a6ddf4e523c", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-1.0.4.tgz", "integrity": "sha512-YaeRM8zAMyk2Q64gnQ4ZQAjl7JAgLHrAVCkEQuOcdeh8dhm+P1OxWqAJd76sem/J6VS/AGRrClvD/F1lYnq5Kg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGpXPD7uIVhRQ8NleVgdHPdeY/rRcp15CYErC1AJ0tqwIga+SbtUj7gM8TNO81zg2Qb2bOkuFNtXOaA92vUh+WXDc="}]}, "maintainers": [{"name": "codemix", "email": "<EMAIL>"}, {"name": "gvozd", "email": "<EMAIL>"}], "directories": {}}, "1.0.5": {"name": "babel-plugin-macros", "version": "1.0.5", "description": "Macros for JavaScript via a babel plugin.", "main": "lib/index.js", "scripts": {"build": "babel -d ./lib ./src", "prepublish": "npm run build", "pretest": "npm run build && npm run lint", "test": "mocha", "watch": "mocha --watch", "lint": "eslint ./src/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/codemix/babel-plugin-macros.git"}, "keywords": ["babel", "babel-plugin", "macros", "ast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/codemix/babel-plugin-macros/issues"}, "homepage": "https://github.com/codemix/babel-plugin-macros", "devDependencies": {"babel-cli": "^6.4.5", "babel-core": "^6.4.5", "babel-eslint": "^4.1.6", "babel-plugin-transform-flow-strip-types": "^6.4.0", "babel-preset-es2015": "^6.3.13", "babel-register": "^6.4.3", "eslint": "^1.10.3", "mocha": "^2.3.4", "should": "^6.0.3"}, "dependencies": {"lodash": "^4.0.0"}, "gitHead": "4adab3f949edd7cb776e45e51323782fd240d693", "_id": "babel-plugin-macros@1.0.5", "_shasum": "58431c9b7321a263338c32f3c199d7f71966957b", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.0", "_npmUser": {"name": "gvozd", "email": "<EMAIL>"}, "dist": {"shasum": "58431c9b7321a263338c32f3c199d7f71966957b", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-1.0.5.tgz", "integrity": "sha512-lvJJ7urh5rTS7QaVuzviXW77I9P4jr14AP04mSV1X0VuC50g2jCZofO0rnFdTOag3C/8iXcYqqKaNkQIDmn5+Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHEvb+keJP4OWQEXo7TbSXNjNExqwNYtZTdVt8khchwCAiEAvwcV8JSUtU7W9mNmVzH2TyawEs2z48n/rEZLi6PDTqI="}]}, "maintainers": [{"name": "codemix", "email": "<EMAIL>"}, {"name": "gvozd", "email": "<EMAIL>"}], "directories": {}}, "1.0.6": {"name": "babel-plugin-macros", "version": "1.0.6", "description": "Macros for JavaScript via a babel plugin.", "main": "lib/index.js", "scripts": {"build": "babel -d ./lib ./src", "prepublish": "npm run build", "pretest": "npm run build && npm run lint", "test": "mocha", "watch": "mocha --watch", "lint": "eslint ./src/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/codemix/babel-plugin-macros.git"}, "keywords": ["babel", "babel-plugin", "macros", "ast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/codemix/babel-plugin-macros/issues"}, "homepage": "https://github.com/codemix/babel-plugin-macros", "devDependencies": {"babel-cli": "^6.4.5", "babel-core": "^6.4.5", "babel-eslint": "^4.1.6", "babel-plugin-transform-flow-strip-types": "^6.4.0", "babel-preset-es2015": "^6.3.13", "babel-register": "^6.4.3", "eslint": "^1.10.3", "mocha": "^2.3.4", "should": "^6.0.3"}, "dependencies": {"lodash": "^4.0.0"}, "gitHead": "9fc17842cf438890cd17774a0afe2c8a2cb52838", "_id": "babel-plugin-macros@1.0.6", "_shasum": "af500573260abd9f6435c6247c672e4cbac996be", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.0", "_npmUser": {"name": "gvozd", "email": "<EMAIL>"}, "dist": {"shasum": "af500573260abd9f6435c6247c672e4cbac996be", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-1.0.6.tgz", "integrity": "sha512-1AXDwUzBt+zYZHj7gzy+kyoGO+FBx0XnB16UZXz6Lhjqmo4pDCLMaT+vN5r4BLVpoETKfUPTNJdhPXEMtOtPWw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBkgETuZC1swMk/ATfO9S8xwhsyhVNxJ4HHS4nQNzQpcAiEAoTxQsk89shilzS03Buip9xZGpJy0hGljmCj+FeFsy2U="}]}, "maintainers": [{"name": "codemix", "email": "<EMAIL>"}, {"name": "gvozd", "email": "<EMAIL>"}], "directories": {}}, "1.0.7": {"name": "babel-plugin-macros", "version": "1.0.7", "description": "Macros for JavaScript via a babel plugin.", "main": "lib/index.js", "scripts": {"build": "babel -d ./lib ./src", "prepublish": "npm run test", "pretest": "npm run build && npm run lint", "test": "mocha", "watch": "mocha --watch", "lint": "eslint ./src/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/codemix/babel-plugin-macros.git"}, "keywords": ["babel", "babel-plugin", "macros", "ast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/codemix/babel-plugin-macros/issues"}, "homepage": "https://github.com/codemix/babel-plugin-macros", "devDependencies": {"babel-cli": "^6.4.5", "babel-core": "^6.4.5", "babel-eslint": "^4.1.6", "babel-plugin-transform-flow-strip-types": "^6.4.0", "babel-plugin-typecheck": "^3.6.1", "babel-preset-es2015": "^6.3.13", "babel-register": "^6.4.3", "eslint": "^1.10.3", "mocha": "^2.3.4", "should": "^6.0.3"}, "dependencies": {"lodash": "^4.0.0"}, "gitHead": "a229886db8e08a628d25a9cd4f5054ed7d6518e9", "_id": "babel-plugin-macros@1.0.7", "_shasum": "c93c2eaa3b45a685670720ab2b7d3a033b14161d", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.0", "_npmUser": {"name": "gvozd", "email": "<EMAIL>"}, "dist": {"shasum": "c93c2eaa3b45a685670720ab2b7d3a033b14161d", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-1.0.7.tgz", "integrity": "sha512-gH/PFJH8B0tGXWKxCH0VZp8QY8dgTFhG0WnvS35NFnCz0lQZ0CAZsKwHSu5vwuEghBSHcfIm+5v5gpkBS8SqHg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFi1kgOvgRTFdJzeYM5v6Q6rGwDiCFwOm0DJivgh+CZ5AiB5Mo7ztShnn3jevSIn4apfcxlAa2Oa1S9QZfwzGEx63g=="}]}, "maintainers": [{"name": "codemix", "email": "<EMAIL>"}, {"name": "gvozd", "email": "<EMAIL>"}], "directories": {}}, "1.0.8": {"name": "babel-plugin-macros", "version": "1.0.8", "description": "Macros for JavaScript via a babel plugin.", "main": "lib/index.js", "scripts": {"build": "babel -d ./lib ./src", "prepublish": "npm run test", "pretest": "npm run build && npm run lint", "test": "mocha", "watch": "mocha --watch", "lint": "eslint ./src/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/codemix/babel-plugin-macros.git"}, "keywords": ["babel", "babel-plugin", "macros", "ast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/codemix/babel-plugin-macros/issues"}, "homepage": "https://github.com/codemix/babel-plugin-macros", "devDependencies": {"babel-cli": "^6.4.5", "babel-core": "^6.4.5", "babel-eslint": "^4.1.6", "babel-plugin-transform-flow-strip-types": "^6.4.0", "babel-plugin-typecheck": "^3.6.1", "babel-preset-es2015": "^6.3.13", "babel-register": "^6.4.3", "eslint": "^1.10.3", "mocha": "^2.3.4", "should": "^6.0.3"}, "dependencies": {"lodash": "^4.0.0"}, "gitHead": "e8f3a81ae7867ff7a3bf06594f418f1f0feb6ac7", "_id": "babel-plugin-macros@1.0.8", "_shasum": "bfde1610b168286775a385b22a1d791a295df08a", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.0", "_npmUser": {"name": "gvozd", "email": "<EMAIL>"}, "dist": {"shasum": "bfde1610b168286775a385b22a1d791a295df08a", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-1.0.8.tgz", "integrity": "sha512-2+nZlpQmLe7s1j4TNKuqC9c7pZHQ4gz3ATjyeifdcjGQVEZPSnIiF0cKUwsnXJu2f6emmT+VsshBSmr916c99Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCyDRH5AS6pGAy7PTN6CncLhF0gOhe6Br2Poh2P6lAJDgIgWd/tRppB4FryDn9QBHso6hmfeddJFbHDX+dJ0dy3LnU="}]}, "maintainers": [{"name": "codemix", "email": "<EMAIL>"}, {"name": "gvozd", "email": "<EMAIL>"}], "directories": {}}, "1.0.11": {"name": "babel-plugin-macros", "version": "1.0.11", "description": "Macros for JavaScript via a babel plugin.", "main": "lib/index.js", "scripts": {"build": "babel -d ./lib ./src", "prepublish": "npm run test", "pretest": "npm run build && npm run lint", "test": "mocha", "watch": "mocha --watch", "lint": "eslint ./src/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/codemix/babel-plugin-macros.git"}, "keywords": ["babel", "babel-plugin", "macros", "ast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/codemix/babel-plugin-macros/issues"}, "homepage": "https://github.com/codemix/babel-plugin-macros", "devDependencies": {"babel-cli": "^6.4.5", "babel-core": "^6.4.5", "babel-eslint": "^4.1.6", "babel-plugin-transform-flow-strip-types": "^6.4.0", "babel-plugin-typecheck": "^3.6.1", "babel-preset-es2015": "^6.3.13", "babel-register": "^6.4.3", "eslint": "^1.10.3", "mocha": "^2.3.4", "should": "^6.0.3"}, "dependencies": {}, "gitHead": "b874229a41ebc97bb05f18d49116711d33537d0f", "_id": "babel-plugin-macros@1.0.11", "_shasum": "2b6ff71e5fe89c6c0bed3f0a0e93aab86f366ba1", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.0", "_npmUser": {"name": "gvozd", "email": "<EMAIL>"}, "dist": {"shasum": "2b6ff71e5fe89c6c0bed3f0a0e93aab86f366ba1", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-1.0.11.tgz", "integrity": "sha512-yCyKfluN0Is7Gc//E2JDXVj+le87H6SZZBNtQduiArrDEDd2O0hsmXVyhApKQFtwPTG+efnGGNJzKPni5kgMIg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGiQqeAFcEgaNzD0gDFqz/PpKbLQ4eqs4RxxuPIa1sdhAiEA0XEk4bHXRpsPBHYunRyffdlAciZ2poGo0YVJ+0td6yg="}]}, "maintainers": [{"name": "codemix", "email": "<EMAIL>"}, {"name": "gvozd", "email": "<EMAIL>"}], "directories": {}}, "1.0.12": {"name": "babel-plugin-macros", "version": "1.0.12", "description": "Macros for JavaScript via a babel plugin.", "main": "lib/index.js", "scripts": {"build": "babel -d ./lib ./src", "prepublish": "npm run test", "pretest": "npm run build && npm run lint", "test": "mocha", "watch": "mocha --watch", "lint": "eslint **/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/codemix/babel-plugin-macros.git"}, "keywords": ["babel", "babel-plugin", "macros", "ast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/codemix/babel-plugin-macros/issues"}, "homepage": "https://github.com/codemix/babel-plugin-macros", "devDependencies": {"babel-cli": "^6.4.5", "babel-core": "^6.4.5", "babel-eslint": "^4.1.6", "babel-plugin-transform-flow-strip-types": "^6.4.0", "babel-plugin-typecheck": "^3.6.1", "babel-preset-es2015": "^6.3.13", "babel-register": "^6.4.3", "eslint": "^1.10.3", "mocha": "^2.3.4", "should": "^6.0.3"}, "dependencies": {"babel-types": "^6.4.5", "babel-traverse": "^6.4.5"}, "gitHead": "a1a1dcbffd42e99d2e0946d9386fa4c26c50c1b3", "_id": "babel-plugin-macros@1.0.12", "_shasum": "25b2c586217c83edfb8d6879b5db0521f72e0f95", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.0", "_npmUser": {"name": "gvozd", "email": "<EMAIL>"}, "dist": {"shasum": "25b2c586217c83edfb8d6879b5db0521f72e0f95", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-1.0.12.tgz", "integrity": "sha512-1gwwpFDBxBtXqCJ4cpssEZ3of+IZSH1CzctrPs76vy1Y0S1+Ph0TWwtLRbRkFserxno1rUO/idZORNvc80s0tQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDOzVqB7ELoklvIfB3PPvM1WRDehnhYaJyuBJ/FvdKAUAIgHV1LCqHigEk24b2mL8mdqSE6FfhLDzJbJE9DH/9SH/s="}]}, "maintainers": [{"name": "codemix", "email": "<EMAIL>"}, {"name": "gvozd", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/babel-plugin-macros-1.0.12.tgz_1455134038158_0.5041044312529266"}, "directories": {}}, "1.0.13": {"name": "babel-plugin-macros", "version": "1.0.13", "description": "Macros for JavaScript via a babel plugin.", "main": "lib/index.js", "scripts": {"build": "babel -d ./lib ./src", "prepublish": "npm run test", "pretest": "npm run build && npm run lint", "test": "mocha", "watch": "mocha --watch", "lint": "eslint **/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/codemix/babel-plugin-macros.git"}, "keywords": ["babel", "babel-plugin", "macros", "ast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/codemix/babel-plugin-macros/issues"}, "homepage": "https://github.com/codemix/babel-plugin-macros", "devDependencies": {"babel-cli": "^6.4.5", "babel-core": "^6.4.5", "babel-eslint": "^4.1.6", "babel-plugin-transform-flow-strip-types": "^6.4.0", "babel-plugin-typecheck": "^3.6.1", "babel-preset-es2015": "^6.3.13", "babel-register": "^6.4.3", "eslint": "^1.10.3", "mocha": "^2.3.4", "should": "^6.0.3"}, "dependencies": {"babel-types": "^6.4.5", "babel-traverse": "^6.4.5"}, "gitHead": "09e9ec32704490141f4a4668c386062fa32f2b5f", "_id": "babel-plugin-macros@1.0.13", "_shasum": "421b3eafc475fb8e736d778ff45ccd604f065e45", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.0", "_npmUser": {"name": "gvozd", "email": "<EMAIL>"}, "dist": {"shasum": "421b3eafc475fb8e736d778ff45ccd604f065e45", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-1.0.13.tgz", "integrity": "sha512-QejlgWM2Nutwv02HWnpvxbGckcQDTR/a5+bfEv0WE6cat/eQqCB3yBzHzDU1MgX3V4EvYT8oX332do4TX/ckHA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEdu8Llp776/jtM/KvUPL5Du0cGcqbeCQ5KXLVfNlhaaAiEApyQaU9970/O3Ux4pD/odeZmbzgcAmxWekfjAAs0rCd0="}]}, "maintainers": [{"name": "codemix", "email": "<EMAIL>"}, {"name": "gvozd", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/babel-plugin-macros-1.0.13.tgz_1455147349752_0.7907719360664487"}, "directories": {}}, "1.0.14": {"name": "babel-plugin-macros", "version": "1.0.14", "description": "Macros for JavaScript via a babel plugin.", "main": "lib/index.js", "scripts": {"build": "babel -d ./lib ./src", "prepublish": "npm run test", "pretest": "npm run build && npm run lint", "test": "mocha", "watch": "mocha --watch", "lint": "eslint **/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/codemix/babel-plugin-macros.git"}, "keywords": ["babel", "babel-plugin", "macros", "ast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/codemix/babel-plugin-macros/issues"}, "homepage": "https://github.com/codemix/babel-plugin-macros", "devDependencies": {"babel-cli": "^6.4.5", "babel-core": "^6.4.5", "babel-eslint": "^4.1.6", "babel-plugin-transform-flow-strip-types": "^6.4.0", "babel-plugin-typecheck": "^3.6.1", "babel-preset-es2015": "^6.3.13", "babel-register": "^6.4.3", "eslint": "^1.10.3", "mocha": "^2.3.4", "should": "^6.0.3"}, "dependencies": {"babel-types": "^6.4.5", "babel-traverse": "^6.4.5"}, "gitHead": "7d544cdd82c8fa86247082eea745bcd79965c1cb", "_id": "babel-plugin-macros@1.0.14", "_shasum": "8bb6eaac73aacfd2fd20f367ae22aeb5f1396050", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.0", "_npmUser": {"name": "gvozd", "email": "<EMAIL>"}, "dist": {"shasum": "8bb6eaac73aacfd2fd20f367ae22aeb5f1396050", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-1.0.14.tgz", "integrity": "sha512-3aAew5MZuz0EhZHBuZeODMzqPWgiIoZiTNgYHKX/tinCet+GLDwgrgUAdGGuGqLcMwoXg+JoFrOjwNBNaBYw8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBzffzPfc6kxApM9ul1TAHXhTEI7LwB5lsvYFJHSJDI9AiBPydYP9fZm+huTkIddKMUYveulSCAznqUYmjPFFji+XA=="}]}, "maintainers": [{"name": "codemix", "email": "<EMAIL>"}, {"name": "gvozd", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/babel-plugin-macros-1.0.14.tgz_1455821542840_0.9555563314352185"}, "directories": {}}, "1.0.15": {"name": "babel-plugin-macros", "version": "1.0.15", "description": "Macros for JavaScript via a babel plugin.", "main": "lib/index.js", "scripts": {"build": "babel -d ./lib ./src", "prepublish": "npm run test", "pretest": "npm run build && npm run lint", "test": "mocha", "watch": "mocha --watch", "lint": "eslint **/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/codemix/babel-plugin-macros.git"}, "keywords": ["babel", "babel-plugin", "macros", "ast"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/codemix/babel-plugin-macros/issues"}, "homepage": "https://github.com/codemix/babel-plugin-macros", "devDependencies": {"babel-cli": "^6.5.1", "babel-core": "^6.5.2", "babel-eslint": "^4.1.6", "babel-plugin-transform-flow-strip-types": "^6.5.0", "babel-plugin-typecheck": "^3.6.1", "babel-preset-es2015": "^6.5.0", "babel-register": "^6.5.2", "eslint": "^1.10.3", "mocha": "^2.4.5", "should": "^8.2.2"}, "dependencies": {"babel-traverse": "^6.5.0", "babel-types": "^6.5.2"}, "gitHead": "1416bffe1e0c0ddba80ffdfd8e7084092f72c3da", "_id": "babel-plugin-macros@1.0.15", "_shasum": "cd6234d12b8fd1fb81f9feb371ba4735a21781f6", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.0", "_npmUser": {"name": "gvozd", "email": "<EMAIL>"}, "dist": {"shasum": "cd6234d12b8fd1fb81f9feb371ba4735a21781f6", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-1.0.15.tgz", "integrity": "sha512-RV9oEhxbt0SPz+RrUqKLeLmDP5cohUqmJtfKctPe4dv520q1CpvSdgpLz2yhnM1dxJOEHqFuMphqPKinDrTtzQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZfxtgecj5S6e32FqDABtTCyJDf/hWUhT5k1vZ0CNHZgIhALMWfI9xBVcUUt89syhkJRbveh9WzfqqGx+GvvhTjoaO"}]}, "maintainers": [{"name": "codemix", "email": "<EMAIL>"}, {"name": "gvozd", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/babel-plugin-macros-1.0.15.tgz_1456097685549_0.37345910375006497"}, "directories": {}}, "2.0.0": {"name": "babel-plugin-macros", "version": "2.0.0", "description": "Enables zero-config, importable babel plugins", "main": "dist/index.js", "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot --coverage", "validate": "kcd-scripts validate", "setup": "npm install && npm run validate -s", "precommit": "kcd-scripts precommit"}, "files": ["dist"], "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "http://kentcdodds.com/"}, "license": "MIT", "dependencies": {"cosmiconfig": "3.1.0"}, "devDependencies": {"ast-pretty-print": "^2.0.1", "babel-core": "7.0.0-beta.3", "babel-plugin-tester": "^5.0.0", "babylon": "7.0.0-beta.34", "cpy": "^6.0.0", "kcd-scripts": "^0.30.4"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "gitHead": "9b178f08d82ca900ab46ca9a2ad43d3a0b1efd94", "_id": "babel-plugin-macros@2.0.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-MSsoNjn8yLe3s71m4THcOXn1ZKXVHVpKNoRKCAEUe9mrkECAazW+ON5lJnjMPAuAPllAPh/WaUqoSduMu1//ew==", "shasum": "fd3aee135f7dec0b82898b7c8f1aed6fa75f9af9", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDDIZe0s6v7eEqd8DIZpQFF/+P9rE+gL7q+nne9vBsmMgIhAMiLexlGzoGa2veCPJ3aNXuAvzqILPGExL1SeXjLvfwH"}]}, "maintainers": [{"email": "<EMAIL>", "name": "kentcdodds"}, {"email": "<EMAIL>", "name": "gvozd"}, {"email": "<EMAIL>", "name": "codemix"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros-2.0.0.tgz_1512871243575_0.5018010793719441"}, "directories": {}}, "2.0.1": {"name": "babel-plugin-macros", "version": "2.0.1", "description": "Enables zero-config, importable babel plugins", "main": "dist/index.js", "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot", "validate": "kcd-scripts validate", "setup": "npm install && npm run validate -s", "precommit": "kcd-scripts precommit"}, "files": ["dist"], "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "http://kentcdodds.com/"}, "license": "MIT", "dependencies": {"cosmiconfig": "^4.0.0"}, "devDependencies": {"ast-pretty-print": "^2.0.1", "babel-core": "7.0.0-beta.3", "babel-plugin-tester": "^5.0.0", "babylon": "7.0.0-beta.34", "cpy": "^6.0.0", "kcd-scripts": "^0.32.1"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "gitHead": "ec116e4e08ac7598d7e62046c81a91e51b0317bb", "_id": "babel-plugin-macros@2.0.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ZkaC9lruHwvVDn05+8LK6NB5t2HQf9Q5KCSoS2Bsj6MWrQzA12OLBxbUmmUjXaqiURktw6GUtPLn/crlALditA==", "shasum": "1fe32689edb8796f9ba2967a05c581ee6ca53c2f", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.0.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCcH5aJT3uGomATusByUzrJzV3LSAdz3aFeKvw0m6S1/QIhANoWtgB38AF1pcL9CPVooBsyeeugS+AE7UmZbfL2xS1m"}]}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros-2.0.1.tgz_1517030113445_0.4813914361875504"}, "directories": {}}, "2.1.0": {"name": "babel-plugin-macros", "version": "2.1.0", "description": "Enables zero-config, importable babel plugins", "main": "dist/index.js", "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot", "validate": "kcd-scripts validate", "setup": "npm install && npm run validate -s", "precommit": "kcd-scripts precommit"}, "files": ["dist"], "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "http://kentcdodds.com/"}, "license": "MIT", "dependencies": {"cosmiconfig": "^4.0.0"}, "devDependencies": {"ast-pretty-print": "^2.0.1", "babel-core": "7.0.0-beta.3", "babel-plugin-tester": "^5.0.0", "babylon": "7.0.0-beta.34", "cpy": "^6.0.0", "kcd-scripts": "^0.32.1"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "gitHead": "e2d8973ccaeaf81810dd1ab3037ff9ab20a48b51", "_id": "babel-plugin-macros@2.1.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-oqxZ3Ncj1iFBCEfg0iwxh7oleupqjLonJp9TQunCQITZxkrHo8QaXMQrn5w3ljmM2d2xZVKR8oweyty+hNgxFA==", "shasum": "e978fd4c5ee9cca73a809c176524c2e9f4dcccbf", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.1.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHNWD71IYUPb5KiLdjO0VVfLtIAy2FvOt7rdR+d2hrWPAiEAlTXu3xmo1V9CBeLHTxHVoncfCNsWRLVDMaR3/SpoETY="}]}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros-2.1.0.tgz_1517067546284_0.241248742910102"}, "directories": {}}, "2.2.0": {"name": "babel-plugin-macros", "version": "2.2.0", "description": "Enables zero-config, importable babel plugins", "main": "dist/index.js", "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot", "validate": "kcd-scripts validate", "setup": "npm install && npm run validate -s", "precommit": "kcd-scripts precommit"}, "files": ["dist"], "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "http://kentcdodds.com/"}, "license": "MIT", "dependencies": {"cosmiconfig": "^4.0.0"}, "devDependencies": {"ast-pretty-print": "^2.0.1", "babel-core": "7.0.0-beta.3", "babel-plugin-tester": "^5.0.0", "babylon": "7.0.0-beta.34", "cpy": "^6.0.0", "kcd-scripts": "^0.32.1"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "gitHead": "d7cb81c3cf6e5aac72e03a1eb0b379b4b73e8abf", "_id": "babel-plugin-macros@2.2.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-HGdenPU9+WGhg++P65O+6aIdmXx99p58K7VtAtizC3eUHbO4FXfyfK9SCJubylcyKziEB3nMAUDFHeyDUj38eA==", "shasum": "31fc16748d6480697a517f692dc4421cb7bff0cc", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.2.0.tgz", "fileCount": 5, "unpackedSize": 28698, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCjFK20MSdX/ec4k1iuO6ego/Bh5w4EcvhmWR2vLlkcqQIgIyRnwA+rj3eyqArEN1HmvqrwcZ0gOmtJn/0h9iCACWM="}]}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros_2.2.0_1520033242160_0.45096223300682037"}, "_hasShrinkwrap": false}, "2.2.1": {"name": "babel-plugin-macros", "version": "2.2.1", "description": "Enables zero-config, importable babel plugins", "main": "dist/index.js", "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot", "validate": "kcd-scripts validate", "setup": "npm install && npm run validate -s", "precommit": "kcd-scripts precommit"}, "files": ["dist"], "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "http://kentcdodds.com/"}, "license": "MIT", "dependencies": {"cosmiconfig": "^4.0.0"}, "devDependencies": {"ast-pretty-print": "^2.0.1", "babel-core": "7.0.0-beta.3", "babel-plugin-tester": "^5.0.0", "babel-types": "^6.26.0", "babylon": "7.0.0-beta.34", "cpy": "^6.0.0", "kcd-scripts": "^0.32.1"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "gitHead": "7d8f7a6f69d9c0f952d7f36941ce44a7fff15b9a", "_id": "babel-plugin-macros@2.2.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.1", "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-DSLZpd6/LQFOJUr2pQK6pncxvAL87E6ReWgfaEfgOL1y/YTTIeqVfsrbdDgerdjtVzCIKajD32fxlvhEgvxMEw==", "shasum": "7cc0f84735aa86f776b51860793a98928f43a7fa", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.2.1.tgz", "fileCount": 5, "unpackedSize": 29214, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa838TCRA9TVsSAnZWagAAQYUQAJCjTX94NAJo4fA4g0Wv\nFooPO6u0Oew8RtgOxXwg5nQD+eGrLkF7bxmUfAuau2+9LygMwUUN6V3qIQds\na03epkFczVmxVkbMxegE6Wc6RA4bsytHzKi9EemPm2PRlS7PEPpdcN9m5/Lt\nCr+Y2wZY68QzwLoXuj58IuAo00vuoSrGA7mWZSViyWmHEsjSLZ0PiCx1ffiP\nTSrUNsrAUwRXSoqWtM27RqegAPVT63q2POcd/7sKtJC7ib6egXPpvr1LxEV0\nwoIf5P+k1OPi7oo+a1PH+mB0G3MXh/sxLyoR3iyYTDjAnph5P3rU2A5zrNzc\nE4QhE+DUpihk8twZLh2kOFIDAQVxQq+uFdlc9V62UwQig2RUf7IPxrB92PGS\n5Vfg85Bg1orTdn5dp7LJS+RBLqMZk2khXWx/w/eGw0wvVb71hi4h0TXdi89M\neymGpMR/RrhfInNgzpu9BSxAYffwW+Lgt3gJ0W7JPdmwKYjPH+BpfwbK88NB\ngkuvYoJpg0bs4z3em+anKlkwpljGTjs8lO+DoQAqyy7Fr7Kg/mLJB9yS6OEh\n0XRtN/Ba3J4cItnuTMpitfJ99Wiwjg5eqYEw1tpXW6ojJtofj/5D0nftgYkC\n83CKA3ZrjbsMzAm8ZL4zBMn3tsJ4r8t2fd9IumH06LdRN/TFCFCxwr3qEHkv\nSFJG\r\n=NJLb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDDfxrVZVxGoep5cDm928kTXKqLf/vVZRw5djcU7aRxWAIgXA8ZOweQ+HghNQkK+o7RwVuPt/H4wVfterx0AQ2/LoQ="}]}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros_2.2.1_1525907218511_0.7967537969403222"}, "_hasShrinkwrap": false}, "2.2.2": {"name": "babel-plugin-macros", "version": "2.2.2", "description": "Enables zero-config, importable babel plugins", "main": "dist/index.js", "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot", "validate": "kcd-scripts validate", "setup": "npm install && npm run validate -s", "precommit": "kcd-scripts precommit"}, "files": ["dist"], "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro", "babel-macros", "babel-plugin-macros"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "http://kentcdodds.com/"}, "license": "MIT", "dependencies": {"cosmiconfig": "^4.0.0"}, "devDependencies": {"ast-pretty-print": "^2.0.1", "babel-core": "7.0.0-beta.3", "babel-plugin-tester": "^5.0.0", "babel-types": "^6.26.0", "babylon": "7.0.0-beta.34", "cpy": "^6.0.0", "kcd-scripts": "^0.32.1"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "gitHead": "1d1dbf90963dfbf17b604d71cc5be5caae11c77d", "_id": "babel-plugin-macros@2.2.2", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.2", "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-wq6DYqjNmSPskGyhOeRIbmuvLtsHTfc6ROtGqapTttIGL1RoQmM3V5N8aJiDxPaw3/fveIsVspF51E3V7qTOMQ==", "shasum": "049c93f4b934453688a6ec38bba529c55bf0fa1f", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.2.2.tgz", "fileCount": 5, "unpackedSize": 29251, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbFZBOCRA9TVsSAnZWagAAAbAQAJO2Yr+kBP98XWk3zSmW\nmVf96f93qElnYkiBlkciNSAvY0X8q7rtV5sA5z20ox9Em4eLgLLV6v4FBeKB\nabNQgBf0XGCa+B4hkZLJAHWAVWcriax/bbPTj5xQVnJvW1eqfHFWImFVqF/o\nv1Nq9Wbl3wT6ZnI5xfs9jIaomLEU0VG7h7EIC4rMAwm1YcqZ7Rt6o5N+FS3+\niN8l7unRZwbaPQFunc8Biut5SyMn6nTnPP86Hs4F2LxeUaKbB3tqOgWFM0vS\n0uDiFNLbo3oEiiVFeZo2zodFzhdRK54TZxrYZPLBbJoSzaUaZn/nxN9tEWys\nCaNySS9iFHeMyMRdGCs28IzCWRDh39e5wuLuCDZ7ZjTH36XiW2fL8SHPIKYo\nmjxrBqVP33FHHgi5ahyoQ8hVjlpxQ8FY36Rs3AKyI68sIQ71IEVPx38d8lmZ\ntyAgNK22Tov29EPdbbu5Xz5PSeaDFL4labRM+1eTroHPney5eZ3z30fx261y\nZMJUsrBcla/J7M9IIldrBGwV4AqXgmkACLKqNIWOmbx4Osp4h07weSW3gHRl\n8A9suhIvD2G7swG8/aj5uR6ytUmsx/jlNwc34KWXLECmZUgAygauoBxdMTN3\ndvx5Q+ojZyyuzPmOAK6n0n1KRxjL28KSpaKSKPO6CLbYxFVpmiLOZXchS5ZL\nxDDp\r\n=/kMS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCNGeinDwKeFrHx9luIcxTXMteOgPvuROm9p06d0jmB2QIhAP3d0mqcXWhzoUP/ahxWl7p5tcdPymSttyzMSUhpXWd1"}]}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros_2.2.2_1528139851140_0.7281595162129892"}, "_hasShrinkwrap": false}, "2.3.0": {"name": "babel-plugin-macros", "version": "2.3.0", "description": "Enables zero-config, importable babel plugins", "main": "dist/index.js", "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot", "validate": "kcd-scripts validate", "setup": "npm install && npm run validate -s", "precommit": "kcd-scripts precommit"}, "files": ["dist"], "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro", "babel-macros", "babel-plugin-macros"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "http://kentcdodds.com/"}, "license": "MIT", "dependencies": {"cosmiconfig": "^4.0.0"}, "devDependencies": {"ast-pretty-print": "^2.0.1", "babel-core": "7.0.0-beta.3", "babel-plugin-tester": "^5.0.0", "babel-types": "^6.26.0", "babylon": "7.0.0-beta.34", "cpy": "^6.0.0", "kcd-scripts": "^0.32.1"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "gitHead": "62905650957419bd65caacb5961c301f74659f59", "_id": "babel-plugin-macros@2.3.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Y9h4dQMlzUUKATfNEN+sgiwND/+PGiAkjSW+qwyATIvYMk1y39XmaLHXKI2VojplqtOWQry0y6CumvDw+qETvQ==", "shasum": "1538e6339cbcbf093f334dc2f10f5f53043e3fda", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.3.0.tgz", "fileCount": 5, "unpackedSize": 29457, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbSRz6CRA9TVsSAnZWagAAaRkQAKCIdJawP5zG+WA7pcs8\njranqdrJjljPsBeHcDUSMzPvSwaB5OckpZVNc8G6F5aBV5WsixkpMisl/Ln1\nidbQRcJiciO5hyi/1vI4Ma64RTAF5YqGWUxfxX3wfTw5oHfnUY7+xmSKMeDn\nIrLZaR6NtSqfU36dUCQPyAxQ89bH5IIIemfKFGzgl85hdgYHH1zFvUrAvHjr\nJXTZeIz7h3MI9c6ilFZDFdoFIZ76luqy0CPiFDJb58mDuj1gP9nUWMy4M5cu\nTBP7gHGoWeUqxiLFo45xMvb33K2Bt1XypZ/v/g0KDzQC25rYghGG1E0alupU\n1Bo7vu4hkAgpzBBcf6ZtcccRWlIKI0vQXLZe5fVYREYG3o/2asDjy29h6Nvc\nDth0BdjCVUzy4VboQKOss7ZLWeufWltlGOz/n275u8XQ62WBV3OsbSnvRAda\n5Jad2CYWnsH5N/XfRsbsNYgIYMSdm6aVEZGCFBx1vLsDTruHTTGV/Dm4Klin\nkYzUGFY3IgWgTCl3Clew3fKioZEC7Hf1abfKzEbeienH+4aA80YbIZpLotxI\nzLnuiwMi9YjanLHWZ00JCN55p0O4r0Xpm/7xYfAzjsGmmDISJZkqygb6+wq9\nd/x7bfgyCmoAHCKEVcRcS6zNM/lFlPk+RDFgo5+rBZ6lpSHGKf5EDJ+iKRNz\nA5D7\r\n=ccTb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCGNFPmwbPSCB5VV4j5MBZB1pTvxCzUCbBQC6yd1pDaHgIgKvzhbO93O4OMyVf/Khu7tBpAu0bhlUZEgIy4JClVBys="}]}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros_2.3.0_1531518202803_0.5682024237950345"}, "_hasShrinkwrap": false}, "2.4.0": {"name": "babel-plugin-macros", "version": "2.4.0", "description": "Enables zero-config, importable babel plugins", "main": "dist/index.js", "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot", "validate": "kcd-scripts validate", "setup": "npm install && npm run validate -s", "precommit": "kcd-scripts precommit"}, "files": ["dist"], "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro", "babel-macros", "babel-plugin-macros"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "http://kentcdodds.com/"}, "license": "MIT", "dependencies": {"cosmiconfig": "^5.0.5"}, "devDependencies": {"ast-pretty-print": "^2.0.1", "babel-core": "7.0.0-beta.3", "babel-plugin-tester": "^5.0.0", "babel-types": "^6.26.0", "babylon": "7.0.0-beta.34", "cpy": "^7.0.0", "kcd-scripts": "^0.32.1"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "gitHead": "38f9db3c2bee021b6a511374697b73c6ee631b12", "_id": "babel-plugin-macros@2.4.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-flIBfrqAdHWn+4l2cS/4jZEyl+m5EaBHVzTb0aOF+eu/zR7E41/MoCFHPhDNL8Wzq1nyelnXeT+vcL2byFLSZw==", "shasum": "6c5f9836e1f6c0a9743b3bab4af29f73e437e544", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.4.0.tgz", "fileCount": 5, "unpackedSize": 30447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZx3TCRA9TVsSAnZWagAAfKUQAKEH9O2ggSV+8E6n+c3G\nKTTGpg6YmGaZ9qWxJXtwB7a8LoVAJmS4FoNdAbJRbVKO3zmWEV8/S6cGYv5V\nNVwCLpt0K/lOvMNkcaEIiCp+lTHOlYNK0q9zNzznz/m921uIoz+WmE1muVQ9\n0BnAyPgVH2itPIep1Ehxq08gGPeGFapkDPJQd+g8NiHozLTuejm9ka8FN1Yl\nBKQlTZKAdjFh8lqB0oB7qbbFBLXRt3sQLJxTdOlpYx/xD4yBRzsr5Fst7eZ+\nIy1mJk+IptWjY1UOFoUsx5cXcuEHJk9PN5vZmSWsxJ4uHlcMK7/MjVDZ1oxI\nYnfc0SA1gWnwWzIsOFcpfyDZ0LNRVzZjaGDkXrCrV8mwi5WpEphczq82y9go\ncrtdGuy02EZhQpwicCaPlBLXiW86Yfqi2LjahJl87+WgQKNhlFwVCniM0vmw\nZWvk5juOmPJOYDuwKSbUlWycqDLWVMKD+cASF9NN2WZsCaWL/bzhI4hYZbFC\nxe+Lk+Api5XgUhL+8EcmWlMC4WrAnAU04ePbNAcnEMkNDq0vtCBKucQhyfF2\nyYKO9FqucH7OcoJrYv+AUVN7qWXypbpvRUJDW+RZ+BAAfjqsRnEuEv3XVQ82\nQFlp2GWUOTB0I+XIkMj8CPakHHjTgFcfdrkWwqNkvhc0KPuyxMjHAs9YtnjH\nVXQW\r\n=u335\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE1DiRpKIbFvQpBysow5BfjhFhBtTKgQPzMfqHqhH7zYAiBhYRjnX0/P9Yvg8AiFbSSBNsK0P0X75SLIHBeiE9jGNw=="}]}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros_2.4.0_1533484498928_0.6174502538553257"}, "_hasShrinkwrap": false}, "2.4.1": {"name": "babel-plugin-macros", "version": "2.4.1", "description": "Enables zero-config, importable babel plugins", "main": "dist/index.js", "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot", "validate": "kcd-scripts validate", "setup": "npm install && npm run validate -s", "precommit": "kcd-scripts precommit"}, "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro", "babel-macros", "babel-plugin-macros"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "http://kentcdodds.com/"}, "license": "MIT", "dependencies": {"cosmiconfig": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.1.0", "@babel/parser": "^7.1.0", "@babel/types": "^7.0.0", "ast-pretty-print": "^2.0.1", "babel-plugin-tester": "^5.0.0", "cpy": "^7.0.0", "kcd-scripts": "^0.32.1"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "gitHead": "134f7f6346923b69756a807f6ce72189c9facb76", "_id": "babel-plugin-macros@2.4.1", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-pUdm8I6LAcKIzh6H2JZ1QUJByg7Im5/llcUHD/T9mTtlaBl/4YkHzvd6oUpT0cjIFgkCadVSHuJNVoNkLqvM1w==", "shasum": "f6f7d9551270b2669660ed38f88ca0dc83363734", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.4.1.tgz", "fileCount": 5, "unpackedSize": 30457, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboefmCRA9TVsSAnZWagAAQx8QAIN6qIdF1uSnSau/XSWb\n0dComvsTB8V38J/7cC6vR65j0EzDuYWaYb9gs40bboKU5nUq7y6jv3ExX4ON\nnXeLAN48pYl+XbGFC05jxcqJme8pu+Fl1bsJOnoAIZubiDaiNmBGNjm6Y/LK\nXoyEK31fpcXJNbmbbaG4yE34gucrHLBMyD9dBNgBl1E3SdcbB/T/3gzRGsZi\n3WeCVeQAKJ3kzTPUvResPQ/OQ7+05+dSgYLELm58P9/Z24ivLcKh34n8VFNH\nNQz/iwLeV110S0t/15vQGqlVANR0ctmc392QSVOcjVGYB4E+zSIaS4H1daaQ\nq7XWOKMQQrMSawB2pD7Tu4M3nVheJ7qGaa9Y8+4Vv+cpUA+1sLRnAKhkUbMI\npMo4RCfvJ3fBOX5pBFdsLoYzJNd6SxJUOXn+rDVC8UudcpuCkgC9LKB8F/Jf\nQ26hMV6OpjaSbV2kOrhZELkSVqFRfVtLGeI00WuuXagy8W/lgsOE2nDEmHt0\nZ5EU9MqHb3qJBj9CqBSkg4BImKfo7tgzOmcMWk8s74lKgGO5cB7zoY/z+RW8\nzYbI4kLu52p23b7ngOrULAW6UYEQrgQz3Q4yA1p8aG7ZjPRsLP0D5lhIA149\n32JfsZk1ZiZPIZZq8ETH9l3taz0ymdUQYqA284KZJcQgjzAReg2ZmJhPurRa\n0uBy\r\n=2bOK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC0lDeV/54owQ38go/iQR0aVt+HD+++vd/4tkh80WYPFQIgMSWEJh6y+X/GI/xyesbLvn5drJBWweLiWFvXGASgL5M="}]}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros_2.4.1_1537337317507_0.46424440626751196"}, "_hasShrinkwrap": false}, "2.4.2": {"name": "babel-plugin-macros", "version": "2.4.2", "description": "Enables zero-config, importable babel plugins", "main": "dist/index.js", "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot", "validate": "kcd-scripts validate", "setup": "npm install && npm run validate -s", "precommit": "kcd-scripts precommit"}, "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro", "babel-macros", "babel-plugin-macros"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "http://kentcdodds.com/"}, "license": "MIT", "dependencies": {"cosmiconfig": "^5.0.5", "resolve": "^1.8.1"}, "devDependencies": {"@babel/core": "^7.1.0", "@babel/parser": "^7.1.0", "@babel/types": "^7.0.0", "ast-pretty-print": "^2.0.1", "babel-plugin-tester": "^5.0.0", "cpy": "^7.0.0", "kcd-scripts": "^0.32.1"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "gitHead": "1785a0fab2c374d1505c49cff1bd38fd541819e2", "_id": "babel-plugin-macros@2.4.2", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-NBVpEWN4OQ/bHnu1fyDaAaTPAjnhXCEPqr1RwqxrU7b6tZ2hypp+zX4hlNfmVGfClD5c3Sl6Hfj5TJNF5VG5aA==", "shasum": "21b1a2e82e2130403c5ff785cba6548e9b644b28", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.4.2.tgz", "fileCount": 5, "unpackedSize": 30485, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbpVYkCRA9TVsSAnZWagAAePsQAJuVCnKcwOG5eTzN6x/u\nB01Rwj2zd8WXte07iEdTknLAomyYxbODBk9omXhs21nDjIX7Hw3Zp53mTRuO\nTaiz3SBiiLNTCl1wikXgeoaISAJ5xhhsh0OOvEfboTf8ALOTgBbSmWqI8UOJ\n4JxWo3Dtt4q+Nghc1WiXp+/nAJXlpjdmyaMFKIOWMmbb9zNJ6Xuwwq3tXIee\nh2soilPigEIHR5kQxYl5rmlAj/kQW42zWlX70LLnkuHTHS7p7yYIkPi2COo4\n3uS19LKxuDEbArp0tAlHTqrgTbYybJ/gTh55TBQV9FAIePbE8CGPgYA2d+Li\ndF0i5FfikDjyi2GSkeRGVZpC1zSTLS0lUDbojdWwPalgNbTG3zp5q6EuB3un\nNsQjAvrXgsI5q3v6NxbxpDz0CfRWEHB/3L5t/NT03leb27HgAKUv8t5/cDYl\n4CpoKeIeGLZrb17FrnnwR6hubYXEcESdSAl0tIWXBkrnErkDtS8SlHf6j3Gk\nJvYjBIfDxnemQD/ymiMTwrTfhOxWYg3x9NM6SnTieO7YIAAWWb4/Cj2fjnv7\n85RjGNdBhMaVFfMdX0C/Q5mHRDx0O2DaLj1W8wnmY7lYWB19/sMmtj42SJA+\nUr3XBZhHhFfI7sxlmoYsMqRvjFzr5R/cE/u0OtW6sP1drh7r2q9l0hP+Zk3j\ny8Ae\r\n=z7fV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID0aqmFUY8YhQWdwuuffqlNqNVy0otidzCV9Y2woIyooAiBpv7SJGv5obQzzvnRljmWnAQgoh6saJoxkOvEliSj6QA=="}]}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros_2.4.2_1537562147501_0.7547846447795021"}, "_hasShrinkwrap": false}, "2.4.3": {"name": "babel-plugin-macros", "version": "2.4.3", "description": "Enables zero-config, importable babel plugins", "main": "dist/index.js", "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot", "validate": "kcd-scripts validate", "setup": "npm install && npm run validate -s", "precommit": "kcd-scripts precommit"}, "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro", "babel-macros", "babel-plugin-macros"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "http://kentcdodds.com/"}, "license": "MIT", "dependencies": {"cosmiconfig": "^5.0.5", "resolve": "^1.8.1"}, "devDependencies": {"@babel/core": "^7.1.0", "@babel/parser": "^7.1.0", "@babel/types": "^7.0.0", "ast-pretty-print": "^2.0.1", "babel-plugin-tester": "^5.0.0", "cpy": "^7.0.0", "kcd-scripts": "^0.32.1"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "gitHead": "2e0badbeaf038001068a9fec8a8dec90a94f3078", "_id": "babel-plugin-macros@2.4.3", "_npmVersion": "6.4.1", "_nodeVersion": "10.14.2", "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-M8cE1Rx0zgfKYBWAS+T6ZVCLGuTKdBI5Rn3fu9q6iVdH0UjaXdmF501/VEYn7kLHCgguhGNk5JBzOn64e2xDEA==", "shasum": "870345aa538d85f04b4614fea5922b55c45dd551", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.4.3.tgz", "fileCount": 5, "unpackedSize": 30800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcGBZHCRA9TVsSAnZWagAA8ioP+gIf39ap0+8+PmodSuf1\nrZhfp+ZmcZYFD0QzL954hrzKzyFO9bWaTAVfu6u/EW8jR8+P6XbUsNpjYpr6\nbJpoLeJ4FiBwNvXHrGFIxzkaqNofy/d5kKzZHqnROt7fHtEa2OdCYLu+Y4WU\n2s35FrjZI+d9NKJMAbAYsEMlYx25YIClaAUJS4NCmbcEVFr+dZj17KdF1FgT\nnFI45OskqDlEY9bnQNwMOPCk6QRx1Hpm6IOCZiNUvwdPb2LUbxrRiA+K81MJ\noeeglh452pJng4k2qdVDXHjZkckbkEykJvzT0iKrmj8lxiRAz+RRZ5SgMVml\n1Q5a/YggO6O2mEkF5XaBWEU0frkl8DpQGJ34ajSTK2UtMSEwBK5Ua7tXxwrW\nslXUBbeCQcXuOVFLVqvqOI7D3rcG+xKh+Qxcs96Y+Mlfu1a/XvnfVLYNVGT5\nDz2oZ8G334HtTbjzgPwJlZMG7OJ7Rsbnlx0z0R1XNgJYysQYf/eLhJr8itr0\ngbv7R8hIN2xhOPdce6NdiTMhRhx6CT92l6e/I1EH6xWNUlq+rPVm0AckVEDR\n0ef2Nn/oGFtgmviqsunvXG9aq7EpsBNVY5yvTKjtyJb43iVqlMAei/6+9Vls\nyNL1BPgKUHQXl4hmZ2b0BJNDaXAgKfYrXAsHd16GEQ42x72fFPbOhCwXgviZ\nLU2R\r\n=9jc7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC/SZSMplUsFJfxZO+1utCQRgVVmxU+B7DeXdMDRwBmQgIhAPAD70Z/+srMGxSpjDrXDwxNs3Mkg0xs3pkCzJFxstw/"}]}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros_2.4.3_1545082438303_0.162446124785943"}, "_hasShrinkwrap": false}, "2.4.4": {"name": "babel-plugin-macros", "version": "2.4.4", "description": "Enables zero-config, importable babel plugins", "main": "dist/index.js", "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot", "validate": "kcd-scripts validate", "setup": "npm install && npm run validate -s", "precommit": "kcd-scripts precommit"}, "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro", "babel-macros", "babel-plugin-macros"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "http://kentcdodds.com/"}, "license": "MIT", "dependencies": {"cosmiconfig": "^5.0.5", "resolve": "^1.8.1"}, "devDependencies": {"@babel/core": "^7.1.0", "@babel/parser": "^7.1.0", "@babel/types": "^7.0.0", "ast-pretty-print": "^2.0.1", "babel-plugin-tester": "^5.0.0", "cpy": "^7.0.0", "kcd-scripts": "^0.32.1"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "gitHead": "7ad592c5d8c2190cf4a349f193d30b465b7d64d1", "_id": "babel-plugin-macros@2.4.4", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.0", "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-PODLcF8vhB2eC0zZ5s67sBQPLlT0YdINd4/2erTgtyVG5q5cLfsxtSmP8wleeFDH8SZpeLXAStqqFOyfIqccqg==", "shasum": "d7bb55ba6473094ac0e1087fbceb062ebfd2337f", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.4.4.tgz", "fileCount": 5, "unpackedSize": 30976, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcLvdACRA9TVsSAnZWagAARz0P+wYlYbXPf6vSKDsbS3AY\nU9eCFW4cg411udGNJ1q/s1Io4SAQA1nKdFHd5z0ZkRmJICKJCi/yIHk+JVcL\n1m/EadC0acy3ptfSYT6jf4b2bqQyobjI25aBm7/C8fvzwq0xUDH+e7bljyp2\nwhXTNNCyneb2LXlEIlrpfLno7OTcptmJ+jMM+yFGPvpwWMBo4A7Dz4AeR/Nx\nrrIb0dkpWUWx01oRhl7CNO49SsMfFxfwqUlf8m+GRGDVNlWhg6ja16DFTZeu\n1zy9TNwuJp+NFScRgdPu/gjUyMokOgNvSS2mGDmQXeXIByIdrpNi5TWPOTVg\nQYjPgNhzlvgQDQtNRjVOfjQa2e+H6CUmPvgIkKBaqUSJXv93SBj2o4qrxdFl\ngHfktIWyyRbXYEr5lyUAcGftfuzTrD1+qnbrPmrt5VTXzdGh4ZbKN3MRC6Sf\nw7QoEyzKv1H79EQ2nKsOxIlquzcNJ79qVkZt0LHUfh6+fAjnyo7hbVPI1rD2\noYgQWDTDu355SeAB706qk79SL0z0TmME8lvCIVdoBJhSJ6kimrz22PInGLbU\nquFERELc8iBQon6PelbLulLABwTWPVdm8Fg5P6g6PT/hcJ+CeGUFVnOG46bp\nK27KkudpgIrH1WqfcF4RNOSaeq6jgBhcaxDN2jcGq6+xcE2R8cfpSVkdbzuw\nmdqu\r\n=L2sJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBc0JR9VWEkhdszSq7Q+Hwvbvj/nAgGYfiGhIG7og2plAiEAxh51ewIT/xRLKom+sqAF5DUpTgz1ID08EB++R4Qexv8="}]}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros_2.4.4_1546581823367_0.9422352013082755"}, "_hasShrinkwrap": false}, "2.4.5": {"name": "babel-plugin-macros", "version": "2.4.5", "description": "Enables zero-config, importable babel plugins", "main": "dist/index.js", "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot", "validate": "kcd-scripts validate", "setup": "npm install && npm run validate -s", "precommit": "kcd-scripts precommit"}, "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro", "babel-macros", "babel-plugin-macros"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "http://kentcdodds.com/"}, "license": "MIT", "dependencies": {"cosmiconfig": "^5.0.5", "resolve": "^1.8.1"}, "devDependencies": {"@babel/core": "^7.1.0", "@babel/parser": "^7.1.0", "@babel/types": "^7.0.0", "ast-pretty-print": "^2.0.1", "babel-plugin-tester": "^5.0.0", "cpy": "^7.0.0", "kcd-scripts": "^0.32.1"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "gitHead": "9edac400c6dfb3f87c8e62a5c067a11d356b7679", "_id": "babel-plugin-macros@2.4.5", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.0", "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-+/9yteNQw3yuZ3krQUfjAeoT/f4EAdn3ELwhFfDj0rTMIaoHfIdrcLePOfIaL0qmFLpIcgPIL2Lzm58h+CGWaw==", "shasum": "7000a9b1f72d19ceee19a5804f1d23d6daf38c13", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.4.5.tgz", "fileCount": 5, "unpackedSize": 31322, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcM5NuCRA9TVsSAnZWagAALSsQAI0nrHm089b0vZaOfwg/\nsP8kbpl5mYZi39LsNzUt3w2lQ1WLrfHwrCndIOzieLoDADsnLmvK5QKHn76A\nkrhKKGslDA7wx39i/PBXV0jTyLnihuJGxfxGlxh3wanSL36gKwxG1f8hZvhm\nl2paTgu037AJQtCLdZr4qNgVCSrLYHsjtCZGnEI2so9Ulk6Gb42UAZb0VKI7\nXtHCU3F/n99+Xog5g1sLzp6ptGug30HGDmx7COB8nrwcPZM/tnTDMWzHCk6/\nnksnGlQ+KlfGe9W5J06ds1mC53cIHkVqcHbd/54gWroi0DEkIZR6oEzmIg4m\ncraXQqN4mrg8Uz4piiFMEw3+IzQ1cA0gR5OwiBO7pI0Qe5Zf6r1aXyxBIa5S\nvV33e9Z3HHZRWYMc6cWrJ/gL61Y2fwcFvIuOX7DvuiWhsfePN4yNWspDewV6\nhs0fzUpft330QkfS5yjonEaakbZFYPdiFnG36um+jhP0chUKrwGBibzbpvNf\nUwR1Gx58djjXe6q3SFUDE73gY3ef/tlDukdadHMkjmzeP/QJBa32HcluoJlb\nocqxnBfEAC8FCplfMDzybnh2mmb+u6ltVaGMkVgpVuMKDhQRnXX1tDcT8sIf\niG8W25QeoD+CN0Ar1VBci60tMOdj+t25Yoa92gp4Te1k507iET/QqRMi73vs\npwWr\r\n=oCkT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEh7w58rxizK6HvEci07ip4ZvtoprIP/1ImtCwNXobUiAiA2r3YyiXFxCreE+unDGm50ZqahNeojPu7vJM+hbMlIRw=="}]}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros_2.4.5_1546883949623_0.6018427831755362"}, "_hasShrinkwrap": false}, "2.5.0": {"name": "babel-plugin-macros", "version": "2.5.0", "description": "Allows you to build compile-time libraries", "main": "dist/index.js", "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot", "validate": "kcd-scripts validate", "setup": "npm install && npm run validate -s", "precommit": "kcd-scripts precommit"}, "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro", "babel-macros", "babel-plugin-macros"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "http://kentcdodds.com/"}, "license": "MIT", "dependencies": {"cosmiconfig": "^5.0.5", "resolve": "^1.8.1"}, "devDependencies": {"@babel/core": "^7.1.0", "@babel/parser": "^7.1.0", "@babel/types": "^7.0.0", "ast-pretty-print": "^2.0.1", "babel-plugin-tester": "^5.0.0", "cpy": "^7.0.0", "kcd-scripts": "^0.32.1"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "gitHead": "2f8e446d1e710b29986dac53cc363ba997f64ef1", "_id": "babel-plugin-macros@2.5.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-BWw0lD0kVZAXRD3Od1kMrdmfudqzDzYv2qrN3l2ISR1HVp1EgLKfbOrYV9xmY5k3qx3RIu5uPAUZZZHpo0o5Iw==", "shasum": "01f4d3b50ed567a67b80a30b9da066e94f4097b6", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.5.0.tgz", "fileCount": 5, "unpackedSize": 31618, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcXcP/CRA9TVsSAnZWagAAKc0P/1XU7q8p6RKHBdW9d+rl\n52gBEd7aaEuN7k3DvyWddlUbhrWSVaQ//Aa+tdfKQEP97YHbNxJW4Gmxj7R1\nSD9dFysmJwYxfUG7xGSpcphGOBXH5ii/sY2JP1LuI0uwjN55wq4ALf3Y/3Xh\ni4Ubn3imD34gy4hMIXOCDgywmVrVrS/XfSMAWYyVcWTdFmfEPpjoxKWMgr1y\n55EvfTYJnQYEMagQ82KvZrIebJ6w4bLFWIdXP5vujAKxSaKu8TKd8Dhs5fwL\nVYHtQ0KrXwb3UAGEQLjaFTh/dlpxa4MNfZMzxprMR19Xb6Ud8m0pVzk3My1W\nAzSW9xfahLWTns0sh10RaSgjzNE8s8+z3kDSOvVVXOTd3LWQwGoWa3CeInpQ\ni8bPZRKnkbLr/TQEbEzsRx7QEzk94uQlVf1x57n16TSYALW0yrVyupB9uT7s\nS2sT8BjN4Ggj9Sey+Ie3eMe2FAWABmTfdRv4vcjyS2O9pmBJcBmyoNoZDJvR\nB/ZCXylWV/OvgWAWC6RKREm3boKPyup/3orLeL2SArbSuMaDtlZDlEstve4j\nT969T6lh5MrLLk/Kn55PlXossCul9sNTC+lUzfUUb1u+CS4OEeJaQ8TGsUH1\nZdvgiOC9BkOQJZy9Jrp03oeLcvi8jutQAozDyTscBClfTQ700A9Jy5u7O6t/\nRi2R\r\n=+Wew\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnPcQUpjXvtrJq6R19PHoLjxVKDQV+PnpD/LwmKvrC+wIhAIrwB1g9KLpf1aFMhk+GMr5FHMXZZv8IlpiXTUu4zWp7"}]}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros_2.5.0_1549648894624_0.2525741112690405"}, "_hasShrinkwrap": false}, "2.5.1": {"name": "babel-plugin-macros", "version": "2.5.1", "description": "Allows you to build compile-time libraries", "main": "dist/index.js", "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot", "validate": "kcd-scripts validate", "setup": "npm install && npm run validate -s"}, "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro", "babel-macros", "babel-plugin-macros"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "http://kentcdodds.com/"}, "license": "MIT", "dependencies": {"@babel/runtime": "^7.4.2", "cosmiconfig": "^5.2.0", "resolve": "^1.10.0"}, "devDependencies": {"@babel/core": "^7.4.0", "@babel/parser": "^7.4.2", "@babel/types": "^7.4.0", "ast-pretty-print": "^2.0.1", "babel-plugin-tester": "^6.0.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "cpy": "^7.1.0", "kcd-scripts": "^1.1.3"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "gitHead": "e0ebca780856be76cbe1c5841d09a922fdde533b", "_id": "babel-plugin-macros@2.5.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-xN3KhAxPzsJ6OQTktCanNpIFnnMsCV+t8OloKxIL72D6+SUZYFn9qfklPgef5HyyDtzYZqqb+fs1S12+gQY82Q==", "shasum": "4a119ac2c2e19b458c259b9accd7ee34fd57ec6f", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.5.1.tgz", "fileCount": 22, "unpackedSize": 54096, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcnn9FCRA9TVsSAnZWagAA8McP+wZ/auDlliUbjauvM7fy\nJP5RsArU2aLGkWnDinxjOjy5XvipVQUO81HE2RNLNe92gOiP5UPklZ6dcxpZ\nAU60ZLrd7qsA9iz3I+lxE4TlRKR9o7QFcV1k/TD1CaaQm9+5+/byYy8yX8PY\nJEUQw+oopWvOLYo87hdrlbA2etxsSSgRMZNopurwbRt2Uo/p9eUxNw2GUkgU\nlemIgUvR37nl0dDJzvYAN3KCkAbgxVCVkuxJNTLlaZWUY/Q8raM2G/IQx4kz\nHOtFapokYTP9nPAW9MDkTTX0HwrBOesGVTWlGTr8DYQjpDYneGYQmT3Mry+w\nEZmmt1hC9bqmXUF6WscZO3gRGK020/wjS9jppPiAgYrq48vvRf8CcvAxZMVg\nSCVeqnCve1MrEXXR1ypFRrrYnHrMPq4vVJV0Z/J/3wQ+uyAeaURESMuD99Mj\nsJNNGoYapyXyvacqz6hIWKTV3slMVCdPICGNHWlfzlQrAcf63N2kFQ49oofF\no4mhfa/X8hM52+5mDIfPylqwRcDWXpLKarGsuUcla1pNYRzc89VVzPRoX6oY\nSIWIWqbdySWyllFW9fA/FMJAdxqYYo4MnQ5wiy9E+O+piRvx0GENMsS4EuMc\nLaFL2CCWJWTVnRgOPJk2zkW7W/7EnemRZRRyNumo+P3+i1k+MUb6Wmbw68z5\nxSyx\r\n=Parb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDju5jRgeWfjxpmLeaUOS38gRTzwM/jTUKpHTMyh4LZ4gIhAJ+PniyaR8GltQMl4/W4FaWdAKkKk1wIUgU6sR8TEY72"}]}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros_2.5.1_1553891140997_0.5092947573913089"}, "_hasShrinkwrap": false}, "2.6.0": {"name": "babel-plugin-macros", "version": "2.6.0", "description": "Allows you to build compile-time libraries", "main": "dist/index.js", "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot", "validate": "kcd-scripts validate", "setup": "npm install && npm run validate -s"}, "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro", "babel-macros", "babel-plugin-macros"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "http://kentcdodds.com/"}, "license": "MIT", "dependencies": {"@babel/runtime": "^7.4.2", "cosmiconfig": "^5.2.0", "resolve": "^1.10.0"}, "devDependencies": {"@babel/core": "^7.4.0", "@babel/parser": "^7.4.2", "@babel/types": "^7.4.0", "ast-pretty-print": "^2.0.1", "babel-plugin-tester": "^6.0.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "cpy": "^7.1.0", "kcd-scripts": "^1.1.3"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "gitHead": "97cdfdd69a03dff4a76d84f1bdb0d97cbd576482", "_id": "babel-plugin-macros@2.6.0", "_nodeVersion": "10.16.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-6hrXm6NIoSp+JiqhHZ6tUemhClnu//vjx9fAU5tkRCztTKxgiUpFpMDBX4yZiJIco7qkf0CPX2u4Ax3x6GCiUg==", "shasum": "1840d0778f27698e5ceaf1a831293b2a57fc26ae", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.6.0.tgz", "fileCount": 25, "unpackedSize": 61590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc8B/kCRA9TVsSAnZWagAA7XoP/RmFgsvN/PtPhFGK+S3g\nUmheRmKV7QyvgVrTKc/RnzYPAMskbXi8IiEU7jwZJvsnbhDCDtIH0VANTVQS\nzIXhr6FIICdWR2bEd/Upsb7CTXplWTYrgJu40FSOjZiI5tpRlOGEnhV436oG\nR7i7BYbXFpi3Ecna+Pt8CkA/G+TErlv35N0mf0be+QqAosmVJMdHeOEa1Usl\nd8nVdRsmk20iXsq5kKNu+hjoa/UcGj+mpkt4jhTY0qLibE6g9PPyUdSZsaJw\nsNO8HyF5ARwB2tBaWZb3wOTAAjfFMG9bcW8YFSsA00+EiTzmeY5ujc5V5dpd\ngCMDwfRAtBA39qe6GYmuqILeZ1Zk3UWVwq9fqPeX/FpDnqfDc5Tk+4IJIa1G\n4axN5lgECQeobhvzGMC15EuniXhbWA5iBNDFzpAGAs8nXTYz1Yu7nB+S1Rcn\njI/X41R4GNFdoPl5hYbP7vfgPWrasJMyZJbmHmIo3nukR1SEJZK2Eu5kYoqF\n38I/LzeEFimRDpTNMwujbmB4IgiZDmDMlm/9U1kOqrkyShudnY9YOPQ23Fxg\nGRO/Qgola+sV//7jELuzDhUxMpBbmQSKIkNvwfuMbX/YqUhWIJhYv59ZaFBQ\nMje5GQOHu9kBBpDD5dBdoNG5ZKNtu0JccKqAEY5gVZg/QPANIbdsBlDtaOpX\npStA\r\n=Wpua\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICP+WTM6wsQflukRKdr43MuwphTA4EpV1aiI8W5Xbe0QAiBhBgf9pVVu2gW8yf22+XFUUm9xQvxGx1Ud99nkT9vvkw=="}]}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros_2.6.0_1559240675419_0.2640228028998779"}, "_hasShrinkwrap": false}, "2.6.1": {"name": "babel-plugin-macros", "version": "2.6.1", "description": "Allows you to build compile-time libraries", "main": "dist/index.js", "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot", "validate": "kcd-scripts validate", "setup": "npm install && npm run validate -s"}, "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro", "babel-macros", "babel-plugin-macros"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "http://kentcdodds.com/"}, "license": "MIT", "dependencies": {"@babel/runtime": "^7.4.2", "cosmiconfig": "^5.2.0", "resolve": "^1.10.0"}, "devDependencies": {"@babel/core": "^7.4.0", "@babel/parser": "^7.4.2", "@babel/types": "^7.4.0", "ast-pretty-print": "^2.0.1", "babel-plugin-tester": "^6.0.1", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "cpy": "^7.1.0", "kcd-scripts": "^1.1.3"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "gitHead": "2d57c60adadd8904db9624614faffc34a251c40d", "_id": "babel-plugin-macros@2.6.1", "_nodeVersion": "10.16.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-6W2nwiXme6j1n2erPOnmRiWfObUhWH7Qw1LMi9XZy8cj+KtESu3T6asZvtk5bMQQjX8te35o7CFueiSdL/2NmQ==", "shasum": "41f7ead616fc36f6a93180e89697f69f51671181", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.6.1.tgz", "fileCount": 26, "unpackedSize": 63219, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc9+x/CRA9TVsSAnZWagAAuxMP/jNZLJkIA/ZCsTNSeyeH\nsx/DmeBKgF2rqALwsZC5jjQZy0fcybGAKHFaxrJUL+uSkqtREDWUysPnpYtR\nJwEKopzKfyJIiZ9Wbk+6aLFZ8MuTSv3A7ei/Vw9d5DIewp1pCvQcmX+T209R\ncH1BBT/KudBXxZ95+ywJK0/nYGKXLDk9/i9UOaKJS/TfCfr1US6peEUkXiw0\n9YtYH3xGcZYTaXDBoNm8jbyR1IlAp9Vj1QmwjP1ncn2vFuJrZgB4km5qf0EC\nC933lXaON9HAZ6672OE33311XlazMVCHSp8Gxrxuca11Tdr0PRDswe9UqUdD\nbtyBEjrtzGHlsPAyXwWxjzXBMGKcifAQ1sb3VF20Yd36KtbQv39Izg3gDFP6\nkkeuQtwYmt02CPuDJpp6sRY7LqRq/w1l254kCxsp/ZnH4XiDZUUI2EiTtfQE\nEqxhGIqf/0IkjeO8fSryEhoc9p0TmYR/XaTBCzcgNt7SscEjFoXNyz95ZBRs\n1Xye/2xQrP7KebDULf9rNekohNanj3aFxeOgqNml1ICwCs0esadOrSOQSjDT\nNd0RlvdNWxMNV4gMzf48M3fULnq6RIUnbN8yzbE8t7ITbPt20EO/uu23eiPR\nJMyKOQyC0K4AQKXVHQi4o3gNroiDA3360Ye1OFVU5YCOPJsFtxqSU3Vz7973\nkfHs\r\n=9NiH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDjbazVaUoPIPQB49igGiLv9GTkeEzmwmSNyidwYAERmwIgX/V1bn/xqV3m4En1VLywen+rWp5lih48uTsuidFbdFg="}]}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros_2.6.1_1559751806607_0.3121885010115335"}, "_hasShrinkwrap": false}, "2.6.2": {"name": "babel-plugin-macros", "version": "2.6.2", "description": "Allows you to build compile-time libraries", "main": "dist/index.js", "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot", "validate": "kcd-scripts validate", "setup": "npm install && npm run validate -s"}, "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro", "babel-macros", "babel-plugin-macros"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "http://kentcdodds.com/"}, "license": "MIT", "dependencies": {"@babel/runtime": "^7.7.2", "cosmiconfig": "^6.0.0", "resolve": "^1.12.0"}, "devDependencies": {"@babel/core": "^7.7.2", "@babel/parser": "^7.7.3", "@babel/types": "^7.7.2", "ast-pretty-print": "^2.0.1", "babel-plugin-tester": "^7.0.4", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "cpy": "^7.3.0", "kcd-scripts": "^1.11.0"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "gitHead": "1fcdc4f7c1b6c293b7f6dc7eb1a6c4edc1b8e8ec", "_id": "babel-plugin-macros@2.6.2", "_nodeVersion": "10.17.0", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-Ntviq8paRTkXIxvrJBauib+2KqQbZQuh4593CEZFF8qz3IVP8VituTZmkGe6N7rsuiOIbejxXj6kx3LMlEq0UA==", "shasum": "98ae30a02645dfa8033628fe613854ec9541bbc8", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.6.2.tgz", "fileCount": 5, "unpackedSize": 34501, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdzewsCRA9TVsSAnZWagAA+w0QAJXOvZL4NHlIyBZhG8Eg\nOpka8vvAcukpOu16whD2Paha88n6gfktW7rWcZ59jtUQtFvoiqYB8bqKjGE8\nJggPorl5JtwH9KX0crfv0NwPjHS+fu/7qZ4cSAyoe42CImjAvO3KXYJsMgEW\n0growtb3FAfaD6t/yp/uDo0okiIu8GfwU6VdC9Ji89L1BNtnfvZg2Vmp7lwn\noUnHg36Jkk03+9TdtUe93Oy1ipZacfOlH86tvu8VY0/dQ1SO1RdHiuvmtP03\nz7wZrmu1veyVPZ3RskznQcLokPf43ban5UkJzIRnvqoUsQrTqRys8Q9GF30O\nrhedIgnlXDY1X+qMOmOAiGK2c8Ba/4UHsqFAMZEvfOWqJy7v71RuUAt++Qs0\nLfXYWW0h+7b00VWmr2yJ6PJbvM0tbGLy3l6DfgoLAPKMl0pJuAvtgWEbPIvn\nH4eK+EzJjO4dtMNNGmXMiVwF/UdaJWyjkFnIq9qSlWbC7aT7oAvAUq5aZRFx\n6Pn4BqgtJY2s9h26/yPc6+Ef/LXf8jmd6GFnbwzLjwHm+HwxDMIg3poYnfsG\nd4H571XHKRHVYz/ELDFYVM0earW0RxHMBI+WqNzu2MX4y19JKrCDzLdOeArH\nZGu9nPJR4jbgrzdxrp0QTnjAlLE/MF8ibsL7WshCKiMGhGdZTlEYxJjz+IFv\n8xj8\r\n=WZAg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDiEGwv9bnfMVhIgy44b8kboHdRwmzjeWvJ6gVwylUcQgIhALJ4tHxiS9wJ3V6hZQK47QFq7PYFM8dNCZitjuswA9aA"}]}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros_2.6.2_1573776428213_0.6456732280545106"}, "_hasShrinkwrap": false}, "2.7.0": {"name": "babel-plugin-macros", "version": "2.7.0", "description": "Allows you to build compile-time libraries", "main": "dist/index.js", "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot", "validate": "kcd-scripts validate", "setup": "npm install && npm run validate -s"}, "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro", "babel-macros", "babel-plugin-macros"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "http://kentcdodds.com/"}, "license": "MIT", "dependencies": {"@babel/runtime": "^7.7.2", "cosmiconfig": "^6.0.0", "resolve": "^1.12.0"}, "devDependencies": {"@babel/core": "^7.7.2", "@babel/parser": "^7.7.3", "@babel/types": "^7.7.2", "ast-pretty-print": "^2.0.1", "babel-plugin-tester": "^7.0.4", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "cpy": "^7.3.0", "kcd-scripts": "^1.11.0"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "gitHead": "800a12dfa84bdf8ab845a37d42c9ecf805e75926", "_id": "babel-plugin-macros@2.7.0", "_nodeVersion": "10.17.0", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-eV/9IWjmwT/TDCrNzA9sgO/j+x1dWAdLds7KLTY/emkLimzYbiXugfa0EO9IMkLeBBYvS0OdY+6pkF5VGf0iog==", "shasum": "f409a674cbbe548b60cbdf495ec059a2de429ab7", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.7.0.tgz", "fileCount": 5, "unpackedSize": 35026, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd1IbTCRA9TVsSAnZWagAAo2UP/jrFrsNb6WXJCEXAV18Z\n7/QlmAyBSQhNfP935g0FiLgPgqWTwzLfJ15Ph7l2RFTMZH9veUvmBIDwW2nE\nphxRXY709EM5qFVA7vmjE9+dReubseAJuIOAxsiFgy9Ejrd/xw776vzoDLG7\nBoLea1oxWtMZBlpQ7I0ia5edJlrR1DbujpK7RIOLnIyMd6ax38I809J5D0Hu\neujHIpeofbdQG/GDT3Fv/4Lnymxu0RJ0J2U1t3hKleRMvDrPYgJ0/+FvoKzz\ne5sCyM3HxpbmmdyXl3ci4NmdlZxm7HvMsM995E/WshulKjXmdlcCRvZWnQXs\nVcB1L9EA7h2zHbzwS5qHaATW421gO98uXCR4rns8jmXe9KyyQ8YsIxsYEBPV\noKpFDY2t9aB3Z13eH4mKKyqWx07YdVj/lYV3FQZsPn4IfXIo3cgJV+jzEHnN\ncSx6PYE3qnD9fcnFxdYL8oHAqcfhWScWB7V1nSuXLhVxP4QAl5sxIw6tJouU\nK/By4VGnLr5PCepXm45acqyz9FC33wCd/ZCS0TuZc8vCYovSGwbd1fV5qwJm\n07tg+mB1XIg8mYh+Je5cNmBHJlojKT20R0oyQ7Xg8DI+i1lB2xOVuic0R5f3\nXMlPUQINFNJJc+kQIbFK57PlyOZpIBluon53v9QvA2Avi2akbFEBbesPuxPP\n/kFi\r\n=MhIn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD5gipC9FRkQiF2k0PbMi06qQIxEw9JtxWdW2WkvpmxRQIhAOXzwC27GMQEoihdzg9r8PzivsbrckxgxuWiVozVdfwT"}]}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros_2.7.0_1574209234722_0.4362623615357235"}, "_hasShrinkwrap": false}, "2.7.1": {"name": "babel-plugin-macros", "version": "2.7.1", "description": "Allows you to build compile-time libraries", "main": "dist/index.js", "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot", "validate": "kcd-scripts validate", "setup": "npm install && npm run validate -s"}, "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro", "babel-macros", "babel-plugin-macros"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "http://kentcdodds.com/"}, "license": "MIT", "dependencies": {"@babel/runtime": "^7.7.2", "cosmiconfig": "^6.0.0", "resolve": "^1.12.0"}, "devDependencies": {"@babel/core": "^7.7.2", "@babel/parser": "^7.7.3", "@babel/types": "^7.7.2", "ast-pretty-print": "^2.0.1", "babel-plugin-tester": "^7.0.4", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "cpy": "^7.3.0", "kcd-scripts": "^1.11.0"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "gitHead": "a3c0e236df3f52ae19de39eb3c0894d477c94d88", "_id": "babel-plugin-macros@2.7.1", "_nodeVersion": "10.17.0", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-HNM284amlKSQ6FddI4jLXD+XTqF0cTYOe5uemOIZxHJHnamC+OhFQ57rMF9sgnYhkJQptVl9U1SKVZsV9/GLQQ==", "shasum": "ee294383c1a38f9d6535be3d89734824cb3ed415", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.7.1.tgz", "fileCount": 5, "unpackedSize": 35005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2GMdCRA9TVsSAnZWagAAOMoQAKQ65uireATC01Ku8Eu3\nUedxkOt3I383KI507jqkpehL9Fl0DaT35eGzxAJbHtgACz5zzvt5Xr7aGrNZ\n88+lYnOsA68fLDy+1M/yJ655mlMjfKDenROBZ9QXNLdS7wyc5qxNV2oUlMwi\nw24ZfDMKWAV8wf+8nMNMhULWppac6rGuLwvbS+7eOXhNz1fG94ew45OVvA75\nWQVhtpJ+dZM6QKzK9fJ72mlqrxs+YG/RtIvQ0Jlj/dUT+jpyF+KMdJL+I2p9\nzkJjOBOUnuFVvp0LyGANMMQjAD5LCYYWG+6PTSJb0WG0MLAtsvSsWdwo86RA\nNO1mHXhQAiD/5QyMCYzuIINgIHKm/CjUEXcP345j/ik21mxxT080ADcJXaVE\n2XhvdfpnSf+FyoBkwyS2VuqwCg22FvyoVC1+kr7JT9o5s2m04aK4bC2Rv9Wk\na/abdK80hRjAyt4Pclyy+hkj8lL5VZ2sdOtCT1o/uboGMkGfWk5FPzTFoJET\nhTuwrTC34eR5zhmnUxRAD2II9+YAVLEalzjZt2tI8NNsMl3wzat6JSDCeH5Y\nlpsdaR6gglM3VJ/gfYavFJJAjZ0u911tr2QyY0c/YvnogRIERP+FfT4gXe17\nR1DLZ8ild4vRXkgBotXhIXkKq2IHq/aiRfeMAFAoGf/0fGQ6kWOs9K78KVDe\nxptf\r\n=WF3T\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFinoN0mNfuUk1ykNue1d3vCiZJKPnVtg++/WsDy+COIAiEAkd6jtXfDJQsfGoSQSdEry1kaAVcDtR3ajV/PuD78P00="}]}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros_2.7.1_1574462237031_0.9149253629229155"}, "_hasShrinkwrap": false}, "2.8.0": {"name": "babel-plugin-macros", "version": "2.8.0", "description": "Allows you to build compile-time libraries", "main": "dist/index.js", "scripts": {"add-contributor": "kcd-scripts contributors add", "build": "kcd-scripts build", "lint": "kcd-scripts lint", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot", "validate": "kcd-scripts validate", "setup": "npm install && npm run validate -s"}, "husky": {"hooks": {"pre-commit": "kcd-scripts pre-commit"}}, "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro", "babel-macros", "babel-plugin-macros"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "http://kentcdodds.com/"}, "license": "MIT", "dependencies": {"@babel/runtime": "^7.7.2", "cosmiconfig": "^6.0.0", "resolve": "^1.12.0"}, "devDependencies": {"@babel/core": "^7.7.2", "@babel/parser": "^7.7.3", "@babel/types": "^7.7.2", "ast-pretty-print": "^2.0.1", "babel-plugin-tester": "^7.0.4", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "cpy": "^7.3.0", "kcd-scripts": "^1.11.0"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "gitHead": "6ecddef103c35f171948241561daea731995d0fa", "_id": "babel-plugin-macros@2.8.0", "_nodeVersion": "10.17.0", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-SEP5kJpfGYqYKpBrj5XU3ahw5p5GOHJ0U5ssOSQ/WBVdwkD2Dzlce95exQTs3jOVWPPKLBN2rlEWkCK7dSmLvg==", "shasum": "0f958a7cc6556b1e65344465d99111a1e5e10138", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-2.8.0.tgz", "fileCount": 5, "unpackedSize": 35911, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd69nLCRA9TVsSAnZWagAAnoIP/imN6NK66Zo3KovSd+qo\nQxoixcLsKcbA3Js74Qorm6iGyr6m8JfzvXC6nN/Jaa289ldj6KoKIpDuldLh\nmzXlCM/8HRqX+uH2sEE9WyqWFPZA5R2CrzbhfcFc3iPe7Edudki+F5hkYwyV\nMnou1Rzxw8N0zDaQWiNDREjnfEOFHfT6gTcehyIJMd3zTZJA4P6ON4LTHI65\nset3ijg/c8vtKf9Ltdg4m2eM7u+FposNBVncd7LxZvCDpu5CxsVUHtUJZVQS\nhqWSktph5jhEBLOCVLjpJz8BhwTld1zv67Zhie+KG1zKh9IA5WyOPNfDLArL\nK3gvjocMgIqOHOMUs8Tsh/mqi6nEKdZ8q/TLv1lxpb1DvhJWH5czH93sBzK4\nRUEc0oO5lE+4KS93lyQU2NoTmIUmhcKS3n7Qh2Fhl5HZ9TkGjp+BhaGg0c9p\ntSWCwJJMvHXJuF8zFfspNPL8O4NHfQl6jfOBm6m/BxtG07m0DteW5yHf+Yn+\nAZ3J9N5+D91DlGrr0vVqtfdm5wA/hKwM64veivwbzbkzmGsyEhhKe6Th1yN0\nwmLKu+2xZrw45ddlPgfIYoPY9wgRQ2o5OA5VUsOYxUKwP/ZBoHMu4S8pFf+1\n2y+i/LBiEYizrEesFOA6rwN7uvsJpvSOqU1HMSyTxGxrC00ceequtX3GzPTW\nMrL1\r\n=uu8Z\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICfkAQ9SUT4jTjaRG4SuhE+jm0TWiei0yrWsr2jM8EcTAiEAwrCOYuTUfXBqwQgW8kwIF8kuP7bK1MDO4HbOWiFKCOw="}]}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros_2.8.0_1575737802926_0.7471394403630127"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "babel-plugin-macros", "version": "3.0.0", "description": "Allows you to build compile-time libraries", "main": "dist/index.js", "scripts": {"build": "kcd-scripts build", "lint": "kcd-scripts lint", "setup": "npm install && npm run validate -s", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot --coverage", "validate": "kcd-scripts validate"}, "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro", "babel-macros", "babel-plugin-macros"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "https://kentcdodds.com"}, "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.5", "@babel/traverse": "^7.12.9", "cosmiconfig": "^7.0.0", "resolve": "^1.19.0"}, "devDependencies": {"@babel/core": "^7.12.9", "@babel/parser": "^7.12.7", "@babel/types": "^7.12.7", "ast-pretty-print": "^2.0.1", "babel-plugin-tester": "^10.0.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "cpy": "^8.1.1", "kcd-scripts": "^7.1.0"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "engines": {"node": ">=10", "npm": ">=6"}, "gitHead": "ccc43ec0a0d09248d2b91abcb481037828532de5", "_id": "babel-plugin-macros@3.0.0", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-KtmdOA0b8gsD8cid2iOdPEPSI45UFkf3wczAWN1FHhfI4UoXMLM6Cdhk72VznDMLPnEC88txqG0101e8MOpsaQ==", "shasum": "de9ad21283f8493b2aa70ba0abe82efafb65f5bd", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-3.0.0.tgz", "fileCount": 5, "unpackedSize": 36891, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfv1UOCRA9TVsSAnZWagAAreQP/jzwk4uogqtzYHY6Oovm\nLUb1p4+VGqJxSIQVQvCRjpR8uto9DaWfcaLbjmpEG5rfrH5xzzC6hipr16fn\n/Z6vI3BczmLzv/ndp12TBOhoadKQWEQ/xs7dLgAOUY36RybfN3+Fh9WzWezl\nW35Nc+TV3pvXzkTRroX542xsyQvbMtu5T0H6gJTSm1EZmkMiQAVuWzIHqO7U\ndwvvgOrsv17XSu707FnfXxrN3tNRBP6Ms/9haps5f/GDTkOoqhDck5azkrW0\nTkIBVLQmO0Og49dTYiA6NIhwEkddq45nujRABO6EQgNbL2gjwI4B0EdvQiPJ\nRhOQdyA/b0uFBDdVOdd2WM/G3N90DW79yV6JICMJ6SqLZ3UaSIycAhGE0VgU\n30ZZ9G/hrY9Uq1H6Z/jd4HevjYxX9zPJzaMXaHtDCrWUolGxbd/XZku0ZP0z\nueKDKkzsqbDwEWNgzGKhjQR4ESvaGlliAkLdvNhU9dyBGTrVgVBl5aRulbr2\np0pgRKPGsaYjE1cFkTrISlBFasA+xC8tmSlZa7CPe1vIBZiAHHfsMbsJti1/\nizrbMxoATpwwYK2r9QqrA/1h1wiEQQw1GFLVTGfcG7X4GHC3YvzoZzsrh7WW\nejsVUTcDRxmTBZL4k/Ni1jhyOL98Sco9bkKQQj/LGG4MYOK27IAP1jGEwAcK\nYIWy\r\n=W1NB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDr7xyJOQ0R+E884ycGsxzJhCiBe2JC4oaHMrMoLs0UZAIgQ3f2RciKzYtzsVPP5zB8j2wlpWoxrBBkSb+GnJG0zXw="}]}, "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros_3.0.0_1606374669873_0.44522761972524716"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "babel-plugin-macros", "version": "3.0.1", "description": "Allows you to build compile-time libraries", "main": "dist/index.js", "scripts": {"build": "kcd-scripts build", "lint": "kcd-scripts lint", "setup": "npm install && npm run validate -s", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot --coverage", "validate": "kcd-scripts validate"}, "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro", "babel-macros", "babel-plugin-macros"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "https://kentcdodds.com"}, "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.5", "cosmiconfig": "^7.0.0", "resolve": "^1.19.0"}, "devDependencies": {"@babel/core": "^7.12.9", "@babel/parser": "^7.12.7", "@babel/types": "^7.12.7", "ast-pretty-print": "^2.0.1", "babel-plugin-tester": "^10.0.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "cpy": "^8.1.1", "kcd-scripts": "^7.1.0"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "engines": {"node": ">=10", "npm": ">=6"}, "gitHead": "18d79d3ac3c2975565e7897d2233cf498f15ffb5", "_id": "babel-plugin-macros@3.0.1", "_nodeVersion": "14.15.1", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-CKt4+Oy9k2wiN+hT1uZzOw7d8zb1anbQpf7KLwaaXRCi/4pzKdFKHf7v5mvoPmjkmxshh7eKZQuRop06r5WP4w==", "shasum": "0d412d68f5b3d1b64358f24ab099bd148724e2a9", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-3.0.1.tgz", "fileCount": 5, "unpackedSize": 36809, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf234UCRA9TVsSAnZWagAAGzEP/0GgbfYDyVluairoVaL2\nhcmtcxZnZDKb1JsVmms7/m57OfDhMUWvLEC1EeAOU3UzFOZ1PgFVv4i87tiw\nZ+NUFZDEZ5+xY/6VEFSEnS0+YZ2GORaxzfB/E5nSa1BFcppW3qhTfV5E7kUy\n3JyiTHrsZPfeArR6DXqASUEYNnIGzeBq5PHhac1xhxxGdo35un5AZM6Sl00q\njZeZHFENICFyvZhUgXB3Um1l4WasgSqT+UvDCEP3Ae9Iyiop+quISmd5Ire1\nlq0SCgwZgNt4Y3XSKXfEBmzjS63o6ibUZV2iSxtrYhWdqzwyHJgP3nvFkJ99\nzKKQh3FR2f6+ppenhBRHqlSC6dKGxb6m0Y6wLRYiM3v272HbrcuRvqAuPTlz\n/HeRTGTz7olMGLo9eh7mLjUlDCAK92Fvta/FpNkqz+FMp35hDQa1kmGmKboO\nAWEbG6T9BBD6IJjh0xPJo377ApFnv/k67fuwOnyhicdzsSZpCp0Hanh7siAx\nm6W/2SUwDwOgjMof1NczJafG0IwNtR4k5J5lsjpzEuPwaI8TurZdbrpEFNfB\nGpp5OeVZsYStngrsMCJVTfDPKG7pfm1PTCBZr8ORLs05fggQMpw3yGQm1GHX\ntZDaqB9fs4wUPpcOzQzPxvlgv0KllT9tXzx28aTMoYq87Mo7Tb45PvOpL+Qt\nbyvp\r\n=dgwc\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGyWcSop7kaK40YTEkZeiO/rlbdkqXXxETuyW0g94k/2AiBx42pgp22mkfPJb77NVp3gKWi0WUCMU/1WZB9dODIrgQ=="}]}, "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros_3.0.1_1608220179773_0.839574421505457"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "babel-plugin-macros", "version": "3.1.0", "description": "Allows you to build compile-time libraries", "main": "dist/index.js", "scripts": {"build": "kcd-scripts build", "lint": "kcd-scripts lint", "setup": "npm install && npm run validate -s", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot --coverage", "validate": "kcd-scripts validate"}, "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro", "babel-macros", "babel-plugin-macros"], "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "https://kentcdodds.com"}, "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.5", "cosmiconfig": "^7.0.0", "resolve": "^1.19.0"}, "devDependencies": {"@babel/core": "^7.12.9", "@babel/parser": "^7.12.7", "@babel/types": "^7.12.7", "ast-pretty-print": "^2.0.1", "babel-plugin-tester": "^10.0.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "cpy": "^8.1.1", "kcd-scripts": "^7.1.0"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js"}, "eslintIgnore": ["node_modules", "coverage", "dist"], "babel": {"presets": ["./other/babel-config.js"]}, "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "engines": {"node": ">=10", "npm": ">=6"}, "gitHead": "14810096e822e2c2370f9b1afacb66501137afa8", "_id": "babel-plugin-macros@3.1.0", "_nodeVersion": "14.16.1", "_npmVersion": "6.14.12", "dist": {"integrity": "sha512-Cg7TFGpIr01vOQNODXOOaGz2NpCU5gl8x1qJFbb6hbZxR7XrcE2vtbAsTAbJ7/xwJtUuJEw8K8Zr/AE0LHlesg==", "shasum": "9ef6dc74deb934b4db344dc973ee851d148c50c1", "tarball": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-3.1.0.tgz", "fileCount": 5, "unpackedSize": 37983, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgksyjCRA9TVsSAnZWagAAO8YP/0Vcw6MOEchY72h/Qadj\nzOzhd9EW9ACevP7DfZ7xVi5uGBL2ZlQoiuM9WKZQpHDkiAnyZbm61ERhHeBI\nHW9uUye48gv4NmPSbU+gBCovMBbN56LauIfclvLZJLjHLajvgzb1jH+r8upn\nR1Ib1Vk97LOsTv9p/JK9gZwp01fFx8d0J8PAq8N36L0hHk1Np0TENngA5GNx\nA5Lwc3yjhoeHn2BFStpwz89TmEejC5Byb/yPCmJlcDM03ceRvuaScw8UorDh\n1eHekp7bjGaO9yvx5cqgd6h4ICbdk0youfbvdCO1QvlY6UBgdKNhrGaMhXsV\neiXouRDIjG1nW44kutp6iRbR5pvreWkHvjncObDtASjV01PPTs/uX2gAQ/m9\n5qs3UBNy/16A0OdMXG7nHNi8mQl5J18bQOZJEGEUe8WuL0DbUlsYX3O+rnb5\ndl4PY/7hzpvXwhm+fChXVhXjGavrIiFb2HvSkIDNaWBa15o/Mn8lpaPurdLZ\nlXi5861YH+DtUCmosS6NZSsKLe027tkxPMKlyV7RojEfLrlGgJf9JSbXmA9o\nbcOFfytBP5dUcNPL9nqAZFNBTVLfTNJuX5kcLyWTbxCpBR3xGivs+1W44S2b\nYLDUhqJsUJXiCHj+yVqZu8DxPG03EzghLA7K0nRnf3wsC22xIT/yLErWOyNn\nvwPC\r\n=Nw1D\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCbrC0jnriST05sk6BUaD49Ax6JrcuvyLAQpPjEDCsKvgIhAMKcM7i30BWMYUVWd3vJcRkw60v4iHJbnqfmJ230sqKa"}]}, "_npmUser": {"name": "kentcdodds", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/babel-plugin-macros_3.1.0_1620233379144_0.25857078026343494"}, "_hasShrinkwrap": false}}, "readme": "<div align=\"center\">\n<h1>babel-plugin-macros 🎣</h1>\n\n<p>Allows you to build simple compile-time libraries</p>\n</div>\n\n---\n\n<!-- prettier-ignore-start -->\n[![Build Status][build-badge]][build]\n[![Code Coverage][coverage-badge]][coverage]\n[![version][version-badge]][package]\n[![downloads][downloads-badge]][npmtrends]\n[![MIT License][license-badge]][license]\n[![All Contributors][all-contributors-badge]](#contributors-)\n[![PRs Welcome][prs-badge]][prs]\n[![Code of Conduct][coc-badge]][coc]\n<!-- prettier-ignore-end -->\n\n## The problem\n\nCheck out\n[this guest post](https://babeljs.io/blog/2017/09/11/zero-config-with-babel-macros)\non the Babel.js blog for a complete write up on the problem, motivation, and\nsolution.\n\nCurrently, each babel plugin in the babel ecosystem requires that you configure\nit individually. This is fine for things like language features, but can be\nfrustrating overhead for libraries that allow for compile-time code\ntransformation as an optimization.\n\n## This solution\n\nbabel-plugin-macros defines a standard interface for libraries that want to use\ncompile-time code transformation without requiring the user to add a babel\nplugin to their build system (other than `babel-plugin-macros`, which is ideally\nalready in place).\n\n<details>\n\n<summary>Expand for more details on the motivation</summary>\n\nFor instance, many css-in-js libraries have a css tagged template string\nfunction:\n\n```js\nconst styles = css`\n  .red {\n    color: red;\n  }\n`\n```\n\nThe function compiles your css into (for example) an object with generated class\nnames for each of the classes you defined in your css:\n\n```js\nconsole.log(styles) // { red: \"1f-d34j8rn43y587t\" }\n```\n\nThis class name can be generated at runtime (in the browser), but this has some\ndisadvantages:\n\n- There is cpu usage/time overhead; the client needs to run the code to generate\n  these classes every time the page loads\n- There is code bundle size overhead; the client needs to receive a CSS parser\n  in order to generate these class names, and shipping this makes the amount of\n  js the client needs to parse larger.\n\nTo help solve those issues, many css-in-js libraries write their own babel\nplugin that generates the class names at compile-time instead of runtime:\n\n```js\n// Before running through babel:\nconst styles = css`\n  .red {\n    color: red;\n  }\n`\n// After running through babel, with the library-specific plugin:\nconst styles = {red: '1f-d34j8rn43y587t'}\n```\n\nIf the css-in-js library supported babel-plugin-macros instead, then they\nwouldn't need their own babel plugin to compile these out; they could instead\nrely on babel-plugin-macros to do it for them. So if a user already had\n`babel-plugin-macros` installed and configured with babel, then they wouldn't\nneed to change their babel configuration to get the compile-time benefits of the\nlibrary. This would be most useful if the boilerplate they were using came with\n`babel-plugin-macros` out of the box, which is true for\n[`create-react-app`][cra].\n\nAlthough css-in-js is the most common example, there are lots of other things\nyou could use `babel-plugin-macros` for, like:\n\n- Compiling GraphQL fragments into objects so that the client doesn't need a\n  GraphQL parser\n- Eval-ing out code at compile time that will be baked into the runtime code,\n  for instance to get a list of directories in the filesystem (see\n  [preval][preval])\n\n</details>\n\n## Table of Contents\n\n<!-- START doctoc generated TOC please keep comment here to allow auto update -->\n<!-- DON'T EDIT THIS SECTION, INSTEAD RE-RUN doctoc TO UPDATE -->\n\n- [Installation](#installation)\n- [Usage](#usage)\n  - [User docs](#user-docs)\n  - [Author docs](#author-docs)\n  - [Caveats](#caveats)\n- [FAQ](#faq)\n  - [How do I find available macros?](#how-do-i-find-available-macros)\n  - [What's the difference between babel plugins and macros?](#whats-the-difference-between-babel-plugins-and-macros)\n  - [In what order are macros executed?](#in-what-order-are-macros-executed)\n  - [Does it work with function calls only?](#does-it-work-with-function-calls-only)\n  - [How about implicit optimizations at compile time?](#how-about-implicit-optimizations-at-compile-time)\n- [Inspiration](#inspiration)\n- [Other Solutions](#other-solutions)\n- [Issues](#issues)\n  - [🐛 Bugs](#-bugs)\n  - [💡 Feature Requests](#-feature-requests)\n- [Contributors ✨](#contributors-)\n- [LICENSE](#license)\n\n<!-- END doctoc generated TOC please keep comment here to allow auto update -->\n\n## Installation\n\nThis module is distributed via [npm][npm] which is bundled with [node][node] and\nshould be installed as one of your project's `devDependencies`:\n\n```\nnpm install --save-dev babel-plugin-macros\n```\n\n## Usage\n\n> You may like to watch\n> [this YouTube video](https://www.youtube.com/watch?v=1queadQ0048&list=PLV5CVI1eNcJgCrPH_e6d57KRUTiDZgs0u)\n> to get an idea of what macros is and how it can be used.\n\n### User docs\n\nAre you trying to use `babel-plugin-macros`? Go to\n[`other/docs/user.md`](other/docs/user.md).\n\n### Author docs\n\nAre you trying to make your own macros that works with `babel-plugin-macros`? Go\nto [`other/docs/author.md`](other/docs/author.md). (you should probably read the\nuser docs too).\n\n### Caveats\n\n#### Babel cache problem\n\n> **Note:** This issue is not present when used in Create React App.\n\nMost of the time you'll probably be using this with the babel cache enabled in\nwebpack to rebuild faster. If your macro function is **not pure** which gets\ndifferent output with same code (e.g., IO side effects) it will cause recompile\nmechanism fail. Unfortunately you'll also experience this problem while\ndeveloping your macro as well. If there's not a change to the source code that's\nbeing transpiled, then babel will use the cache rather than running your macro\nagain.\n\nFor now, to force recompile the code you can simply add a cache busting comment\nin the file:\n\n```diff\nimport macro from 'non-pure.macro';\n\n-// Do some changes of your code or\n+// add a cache busting comment to force recompile.\nmacro('parameters');\n```\n\nThis problem is still being worked on and is not unique to\n`babel-plugin-macros`. For more details and workarounds, please check related\nissues below:\n\n- babel-plugin-preval:\n  [How to force recompile? #19](https://github.com/kentcdodds/babel-plugin-preval/issues/19)\n- graphql.macro:\n  [Recompile problem (babel cache) #6](https://github.com/evenchange4/graphql.macro/issues/6)\n- twin.macro: \n  [Can't change taliwind config #37](https://github.com/ben-rogerson/twin.macro/discussions/37)\n\n## FAQ\n\n### How do I find available macros?\n\nYou can write your own without publishing them to `npm`, but if you'd like to\nsee existing macros you can add to your project, then take a look at the\n[Awesome babel macros](https://github.com/jgierer12/awesome-babel-macros)\nrepository.\n\nPlease add any you don't see listed!\n\n### What's the difference between babel plugins and macros?\n\nLet's use\n[`babel-plugin-console`](https://www.npmjs.com/package/babel-plugin-console) as\nan example.\n\nIf we used `babel-plugin-console`, it would look like this:\n\n1.  Add `babel-plugin-console` to `.babelrc`\n2.  Use it in a code:\n\n```js\nfunction add100(a) {\n  const oneHundred = 100\n  console.scope('Add 100 to another number')\n  return add(a, oneHundred)\n}\n\nfunction add(a, b) {\n  return a + b\n}\n```\n\nWhen that code is run, the `scope` function does some pretty nifty things:\n\n**Browser:**\n\n![Browser console scoping add100](https://github.com/mattphillips/babel-plugin-console/raw/53536cba919d5be49d4f66d957769c07ca7a4207/assets/add100-chrome.gif)\n\n**Node:**\n\n<img alt=\"Node console scoping add100\" src=\"https://github.com/mattphillips/babel-plugin-console/raw/53536cba919d5be49d4f66d957769c07ca7a4207/assets/add100-node.png\" width=\"372\" />\n\nInstead, let's use the macro it's shipped with like this:\n\n1.  Add `babel-plugin-macros` to `.babelrc` (only once for all macros)\n2.  Use it in a code:\n\n```js\nimport scope from 'babel-plugin-console/scope.macro'\nfunction add100(a) {\n  const oneHundred = 100\n  scope('Add 100 to another number')\n  return add(a, oneHundred)\n}\n\nfunction add(a, b) {\n  return a + b\n}\n```\n\nThe result is exactly the same, but this approach has a few advantages:\n\n**Advantages:**\n\n- requires only one entry in `.babelrc` for all macros used in project. Add that\n  once and you can use all the macros you want\n- toolkits (like [create-react-app][cra]) may already support\n  `babel-plugin-macros`, so no configuration is needed at all\n- it's explicit. With `console.scope` people may be fooled that it's just a\n  normal `console` API when there's really a babel transpilation going on. When\n  you import `scope`, it's obvious that it's macro and does something with the\n  code at compile time. Some ESLint rules may also have issues with plugins that\n  look for \"global\" variables\n- macros are safer and easier to write, because they receive exactly the AST\n  node to process\n- If you misconfigure `babel-plugin-console` you wont find out until you run the\n  code. If you misconfigure `babel-plugin-macros` you'll get a compile-time\n  error.\n\n**Drawbacks:**\n\n- Cannot (should not) be used for implicit transpilations (like syntax plugins)\n- Explicitness is more verbose. Which some people might consider a drawback...\n\n### In what order are macros executed?\n\nThis is another advantage of `babel-plugin-macros` over regular plugins. The\nuser of the macro is in control of the ordering! The order of execution is the\nsame order as imported. The order of execution is clear, explicit and in full\ncontrol of the user:\n\n```js\nimport preval from 'preval.macro'\nimport idx from 'idx.macro'\n\n// preval macro is evaluated first, then idx\n```\n\nThis differs from the current situation with babel plugins where it's\nprohibitively difficult to control the order plugins run in a particular file.\n\n### Does it work with function calls only?\n\nNo! Any AST node type is supported.\n\nIt can be tagged template literal:\n\n```js\nimport eval from 'eval.macro'\nconst val = eval`7 * 6`\n```\n\nA function:\n\n```js\nimport eval from 'eval.macro'\nconst val = eval('7 * 6')\n```\n\nJSX Element:\n\n```js\nimport Eval from 'eval.macro'\nconst val = <Eval>7 * 6</Eval>\n```\n\nReally, anything...\n\nSee the [testing snapshot](src/__tests__/__snapshots__/index.js.snap) for more\nexamples.\n\n### How about implicit optimizations at compile time?\n\nAll examples above were _explicit_ - a macro was imported and then evaluated\nwith a specific AST node.\n\nCompletely different story are _implicit_ babel plugins, like\n[transform-react-constant-elements](https://babeljs.io/docs/plugins/transform-react-constant-elements/),\nwhich process whole AST tree.\n\nExplicit is often a better pattern than implicit because it requires others to\nunderstand how things are globally configured. This is in this spirit are\n`babel-plugin-macros` designed. However, some things _do_ need to be implicit,\nand those kinds of babel plugins can't be turned into macros.\n\n## Inspiration\n\n- [threepointone/babel-plugin-macros](https://github.com/threepointone/babel-plugin-macros)\n- [facebookincubator/create-react-app#2730][cra-issue]\n\nThank you to [@phpnode](https://github.com/phpnode) for donating the npm package\n`babel-plugin-macros`.\n\n## Other Solutions\n\n- [sweetjs](http://sweetjs.org/)\n\n## Issues\n\n_Looking to contribute? Look for the [Good First Issue][good-first-issue]\nlabel._\n\n### 🐛 Bugs\n\nPlease file an issue for bugs, missing documentation, or unexpected behavior.\n\n[**See Bugs**][bugs]\n\n### 💡 Feature Requests\n\nPlease file an issue to suggest new features. Vote on feature requests by adding\na 👍. This helps maintainers prioritize what to work on.\n\n[**See Feature Requests**][requests]\n\n## Contributors ✨\n\nThanks goes to these people ([emoji key][emojis]):\n\n<!-- ALL-CONTRIBUTORS-LIST:START - Do not remove or modify this section -->\n<!-- prettier-ignore-start -->\n<!-- markdownlint-disable -->\n<table>\n  <tr>\n    <td align=\"center\"><a href=\"https://kentcdodds.com\"><img src=\"https://avatars.githubusercontent.com/u/1500684?v=3?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Kent C. Dodds</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=kentcdodds\" title=\"Code\">💻</a> <a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=kentcdodds\" title=\"Documentation\">📖</a> <a href=\"#infra-kentcdodds\" title=\"Infrastructure (Hosting, Build-Tools, etc)\">🚇</a> <a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=kentcdodds\" title=\"Tests\">⚠️</a></td>\n    <td align=\"center\"><a href=\"https://github.com/threepointone\"><img src=\"https://avatars1.githubusercontent.com/u/18808?v=3?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Sunil Pai</b></sub></a><br /><a href=\"#ideas-threepointone\" title=\"Ideas, Planning, & Feedback\">🤔</a></td>\n    <td align=\"center\"><a href=\"http://suchipi.com\"><img src=\"https://avatars0.githubusercontent.com/u/1341513?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Lily Scott</b></sub></a><br /><a href=\"#question-suchipi\" title=\"Answering Questions\">💬</a> <a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=suchipi\" title=\"Documentation\">📖</a></td>\n    <td align=\"center\"><a href=\"http://twitter.com/dralletje\"><img src=\"https://avatars1.githubusercontent.com/u/767261?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Michiel Dral</b></sub></a><br /><a href=\"#ideas-dralletje\" title=\"Ideas, Planning, & Feedback\">🤔</a></td>\n    <td align=\"center\"><a href=\"https://github.com/tkh44\"><img src=\"https://avatars2.githubusercontent.com/u/662750?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Kye Hohenberger</b></sub></a><br /><a href=\"#ideas-tkh44\" title=\"Ideas, Planning, & Feedback\">🤔</a></td>\n    <td align=\"center\"><a href=\"https://hamil.town\"><img src=\"https://avatars1.githubusercontent.com/u/11481355?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Mitchell Hamilton</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=mitchellhamilton\" title=\"Code\">💻</a> <a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=mitchellhamilton\" title=\"Tests\">⚠️</a></td>\n    <td align=\"center\"><a href=\"https://github.com/wKovacs64\"><img src=\"https://avatars1.githubusercontent.com/u/1288694?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Justin Hall</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=wKovacs64\" title=\"Documentation\">📖</a></td>\n  </tr>\n  <tr>\n    <td align=\"center\"><a href=\"https://github.com/PiereDome\"><img src=\"https://avatars3.githubusercontent.com/u/1903016?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Brian Pedersen</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=PiereDome\" title=\"Code\">💻</a> <a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=PiereDome\" title=\"Documentation\">📖</a></td>\n    <td align=\"center\"><a href=\"https://github.com/apalm\"><img src=\"https://avatars3.githubusercontent.com/u/4495237?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Andrew Palm</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=apalm\" title=\"Code\">💻</a></td>\n    <td align=\"center\"><a href=\"https://michaelhsu.tw/\"><img src=\"https://avatars1.githubusercontent.com/u/1527371?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Michael Hsu</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=evenchange4\" title=\"Documentation\">📖</a> <a href=\"#plugin-evenchange4\" title=\"Plugin/utility libraries\">🔌</a></td>\n    <td align=\"center\"><a href=\"https://github.com/citycide\"><img src=\"https://avatars2.githubusercontent.com/u/16605186?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Bo Lingen</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=citycide\" title=\"Code\">💻</a></td>\n    <td align=\"center\"><a href=\"https://github.com/tylerthehaas\"><img src=\"https://avatars1.githubusercontent.com/u/11150235?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Tyler Haas</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=tylerthehaas\" title=\"Documentation\">📖</a></td>\n    <td align=\"center\"><a href=\"https://github.com/FWeinb\"><img src=\"https://avatars0.githubusercontent.com/u/1250430?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>FWeinb</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=FWeinb\" title=\"Code\">💻</a></td>\n    <td align=\"center\"><a href=\"http://www.tomasehrlich.cz\"><img src=\"https://avatars2.githubusercontent.com/u/827862?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Tomáš Ehrlich</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/issues?q=author%3Atricoder42\" title=\"Bug reports\">🐛</a> <a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=tricoder42\" title=\"Code\">💻</a></td>\n  </tr>\n  <tr>\n    <td align=\"center\"><a href=\"https://github.com/jgierer12\"><img src=\"https://avatars0.githubusercontent.com/u/4331946?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Jonas Gierer</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=jgierer12\" title=\"Documentation\">📖</a></td>\n    <td align=\"center\"><a href=\"http://loicpadier.com\"><img src=\"https://avatars2.githubusercontent.com/u/4009640?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Loïc Padier</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=lPadier\" title=\"Code\">💻</a></td>\n    <td align=\"center\"><a href=\"https://www.pshrmn.com\"><img src=\"https://avatars0.githubusercontent.com/u/1127037?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Paul Sherman</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=pshrmn\" title=\"Code\">💻</a></td>\n    <td align=\"center\"><a href=\"http://burningpotato.com\"><img src=\"https://avatars1.githubusercontent.com/u/540777?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Conrad Buck</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=conartist6\" title=\"Code\">💻</a> <a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=conartist6\" title=\"Tests\">⚠️</a> <a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=conartist6\" title=\"Documentation\">📖</a></td>\n    <td align=\"center\"><a href=\"https://github.com/InvictusMB\"><img src=\"https://avatars3.githubusercontent.com/u/3091209?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>InvictusMB</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=InvictusMB\" title=\"Tests\">⚠️</a></td>\n    <td align=\"center\"><a href=\"https://codefund.io\"><img src=\"https://avatars2.githubusercontent.com/u/12481?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Eric Berry</b></sub></a><br /><a href=\"#fundingFinding-coderberry\" title=\"Funding Finding\">🔍</a></td>\n    <td align=\"center\"><a href=\"http://futagoza.github.io/\"><img src=\"https://avatars1.githubusercontent.com/u/1943570?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Futago-za Ryuu</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=futagoza\" title=\"Code\">💻</a> <a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=futagoza\" title=\"Tests\">⚠️</a></td>\n  </tr>\n  <tr>\n    <td align=\"center\"><a href=\"https://luc.im\"><img src=\"https://avatars3.githubusercontent.com/u/6616955?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Luc</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=lucleray\" title=\"Code\">💻</a></td>\n    <td align=\"center\"><a href=\"http://wintercounter.me\"><img src=\"https://avatars2.githubusercontent.com/u/963776?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Victor Vincent</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=wintercounter\" title=\"Code\">💻</a></td>\n    <td align=\"center\"><a href=\"http://mvasilkov.ovh\"><img src=\"https://avatars3.githubusercontent.com/u/140257?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>я котик пур-пур</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=mvasilkov\" title=\"Documentation\">📖</a></td>\n    <td align=\"center\"><a href=\"http://armandososa.com\"><img src=\"https://avatars0.githubusercontent.com/u/139577?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Armando Sosa</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=soska\" title=\"Documentation\">📖</a></td>\n    <td align=\"center\"><a href=\"https://github.com/matvp91\"><img src=\"https://avatars3.githubusercontent.com/u/12699796?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Matthias</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=matvp91\" title=\"Code\">💻</a></td>\n    <td align=\"center\"><a href=\"https://www.jovidecroock.com/\"><img src=\"https://avatars3.githubusercontent.com/u/17125876?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Jovi De Croock</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=JoviDeCroock\" title=\"Code\">💻</a> <a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=JoviDeCroock\" title=\"Tests\">⚠️</a></td>\n    <td align=\"center\"><a href=\"http://victorarowo.com\"><img src=\"https://avatars0.githubusercontent.com/u/25545108?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Victor Arowo</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=VictorArowo\" title=\"Documentation\">📖</a></td>\n  </tr>\n  <tr>\n    <td align=\"center\"><a href=\"https://twitter.com/alexandermchan\"><img src=\"https://avatars.githubusercontent.com/u/1864372?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Alex Chan</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=alexanderchan\" title=\"Documentation\">📖</a></td>\n    <td align=\"center\"><a href=\"https://probablyup.com\"><img src=\"https://avatars.githubusercontent.com/u/570070?v=4?s=100\" width=\"100px;\" alt=\"\"/><br /><sub><b>Evan Jacobs</b></sub></a><br /><a href=\"https://github.com/kentcdodds/babel-plugin-macros/commits?author=probablyup\" title=\"Code\">💻</a></td>\n  </tr>\n</table>\n\n<!-- markdownlint-restore -->\n<!-- prettier-ignore-end -->\n\n<!-- ALL-CONTRIBUTORS-LIST:END -->\n\nThis project follows the [all-contributors][all-contributors] specification.\nContributions of any kind welcome!\n\n## LICENSE\n\nMIT\n\n<!-- prettier-ignore-start -->\n[npm]: https://www.npmjs.com\n[node]: https://nodejs.org\n[build-badge]: https://img.shields.io/github/workflow/status/kentcdodds/babel-plugin-macros/validate?logo=github&style=flat-square\n[build]: https://github.com/kentcdodds/babel-plugin-macros/actions?query=workflow%3Avalidate\n[coverage-badge]: https://img.shields.io/codecov/c/github/kentcdodds/babel-plugin-macros.svg?style=flat-square\n[coverage]: https://codecov.io/github/kentcdodds/babel-plugin-macros\n[version-badge]: https://img.shields.io/npm/v/babel-plugin-macros.svg?style=flat-square\n[package]: https://www.npmjs.com/package/babel-plugin-macros\n[downloads-badge]: https://img.shields.io/npm/dm/babel-plugin-macros.svg?style=flat-square\n[npmtrends]: http://www.npmtrends.com/babel-plugin-macros\n[license-badge]: https://img.shields.io/npm/l/babel-plugin-macros.svg?style=flat-square\n[license]: https://github.com/kentcdodds/babel-plugin-macros/blob/main/LICENSE\n[prs-badge]: https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square\n[prs]: http://makeapullrequest.com\n[coc-badge]: https://img.shields.io/badge/code%20of-conduct-ff69b4.svg?style=flat-square\n[coc]: https://github.com/kentcdodds/babel-plugin-macros/blob/main/CODE_OF_CONDUCT.md\n[emojis]: https://github.com/all-contributors/all-contributors#emoji-key\n[all-contributors]: https://github.com/all-contributors/all-contributors\n[all-contributors-badge]: https://img.shields.io/github/all-contributors/kentcdodds/babel-plugin-macros?color=orange&style=flat-square\n[bugs]: https://github.com/kentcdodds/babel-plugin-macros/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen+sort%3Acreated-desc+label%3Abug\n[requests]: https://github.com/kentcdodds/babel-plugin-macros/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen+sort%3Areactions-%2B1-desc+label%3Aenhancement\n[good-first-issue]: https://github.com/kentcdodds/babel-plugin-macros/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen+sort%3Areactions-%2B1-desc+label%3Aenhancement+label%3A%22good+first+issue%22\n[preval]: https://github.com/kentcdodds/babel-plugin-preval\n[cra]: https://github.com/facebook/create-react-app\n[cra-issue]: https://github.com/facebook/create-react-app/issues/2730\n<!-- prettier-ignore-end -->\n", "maintainers": [{"name": "kentcdodds", "email": "<EMAIL>"}], "time": {"modified": "2023-04-29T05:24:38.168Z", "created": "2015-05-14T11:47:30.948Z", "0.0.1": "2015-05-14T11:47:30.948Z", "1.0.0": "2016-01-20T23:24:07.804Z", "1.0.1": "2016-01-21T01:39:47.845Z", "1.0.4": "2016-01-24T21:40:11.295Z", "1.0.5": "2016-01-24T21:47:24.332Z", "1.0.6": "2016-01-25T20:56:41.501Z", "1.0.7": "2016-01-25T23:00:23.545Z", "1.0.8": "2016-01-28T18:03:57.497Z", "1.0.11": "2016-01-29T21:21:24.595Z", "1.0.12": "2016-02-10T19:53:59.992Z", "1.0.13": "2016-02-10T23:35:51.906Z", "1.0.14": "2016-02-18T18:52:25.500Z", "1.0.15": "2016-02-21T23:34:50.750Z", "2.0.0": "2017-12-10T02:00:44.583Z", "2.0.1": "2018-01-27T05:15:14.471Z", "2.1.0": "2018-01-27T15:39:07.210Z", "2.2.0": "2018-03-02T23:27:22.261Z", "2.2.1": "2018-05-09T23:06:58.626Z", "2.2.2": "2018-06-04T19:17:32.591Z", "2.3.0": "2018-07-13T21:43:22.924Z", "2.4.0": "2018-08-05T15:54:59.006Z", "2.4.1": "2018-09-19T06:08:37.712Z", "2.4.2": "2018-09-21T20:35:47.729Z", "2.4.3": "2018-12-17T21:33:58.429Z", "2.4.4": "2019-01-04T06:03:43.498Z", "2.4.5": "2019-01-07T17:59:09.776Z", "2.5.0": "2019-02-08T18:01:34.780Z", "2.5.1": "2019-03-29T20:25:41.131Z", "2.6.0": "2019-05-30T18:24:35.625Z", "2.6.1": "2019-06-05T16:23:26.740Z", "2.6.2": "2019-11-15T00:07:08.353Z", "2.7.0": "2019-11-20T00:20:34.869Z", "2.7.1": "2019-11-22T22:37:17.167Z", "2.8.0": "2019-12-07T16:56:43.050Z", "3.0.0": "2020-11-26T07:11:10.041Z", "3.0.1": "2020-12-17T15:49:39.916Z", "3.1.0": "2021-05-05T16:49:39.264Z"}, "homepage": "https://github.com/kentcdodds/babel-plugin-macros#readme", "keywords": ["babel-plugin", "macros", "macro", "babel-macro", "babel-plugin-macro", "babel-macros", "babel-plugin-macros"], "repository": {"type": "git", "url": "git+https://github.com/kentcdodds/babel-plugin-macros.git"}, "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "https://kentcdodds.com"}, "bugs": {"url": "https://github.com/kentcdodds/babel-plugin-macros/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"nelix": true, "timdp": true, "gvozd": true, "isao": true, "flumpus-dev": true}}