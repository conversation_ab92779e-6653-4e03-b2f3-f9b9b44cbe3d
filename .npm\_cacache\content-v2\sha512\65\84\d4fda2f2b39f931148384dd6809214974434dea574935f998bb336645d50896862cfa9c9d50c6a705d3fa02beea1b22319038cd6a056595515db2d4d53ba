{"_id": "escalade", "_rev": "10-cfcc7cbd6a9f2e4c35f4470a24cdc48d", "name": "escalade", "dist-tags": {"latest": "3.2.0"}, "versions": {"0.0.0": {"name": "escalade", "version": "0.0.0", "keywords": [], "author": {"url": "https://lukeed.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "escalade@0.0.0", "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "homepage": "https://github.com/lukeed/escalade#readme", "bugs": {"url": "https://github.com/lukeed/escalade/issues"}, "dist": {"shasum": "6417618198af1c944a48940b06c19a58d6df2dd4", "tarball": "https://registry.npmjs.org/escalade/-/escalade-0.0.0.tgz", "fileCount": 3, "integrity": "sha512-ZD1iWAfRJJRP14vD+OPxdnLgpK7ZY+4qUUx1mtAiE247uhChVGutIGVxMUSCAeIRZagEhj4AOVnM9rOFYSJFcg==", "signatures": [{"sig": "MEUCIDdD6DWz7VPd3giM6J2j+o/2gLks7Vrlsp5LyGVTG0dkAiEA3yzFeyypGssdbrlziKe4KarvNWe2UeKNCXxAnn4iJ/w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1762, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7E4yCRA9TVsSAnZWagAA3+MP/Aqi+0zPtLJ250iavBob\nezKMELaAQ317VdSBi+TB1uYWw0r3fDjVNMWj9IVCsUHygc1ZFZoFmBrdYby2\nmKyF+MUypg4QhVwCLKkEDuhSQnoEtQKg2Fbps/yi+qUqgVYQiBaoB6wdR7QL\nSA53IpvhbZPQIf8JliHH1CXoGUYGHZYOuEKBUpLpcp7Pd2hHtkYVLEc2gztm\nxJVYpYe7cW9huI8v1HRJ310+EYLDbQWWAhPFsZ/6WpeWHNTWU0EPoyk87YcZ\nU/8eNQjhd2LiFVemrY4J5hh37uRU9+9JpQ7W2atFSk4bEyx/3pI+sqinL75b\ndriY1CBGmrMCHs3NcrLBgwDPQ/lJlmEEYaVNW6UHdNZyCE81idEl08L/X/aO\nJXK0O7NyBDMZL96QsHS46GQWOCaDV0VaZO52baiHNicPFxDQ8C+VJzr1bHcl\ncIMTUj8y2DDvw/iXDYsbz9JaDXfS/GMVGHoHfE3nv0yqtevYQtikDoPigPfX\nBqeHC74e85GJc3j959kPQwAlj6OvYMgfWhUfuXcX64y1tU4trTY51bIEP1Ei\nBmFGOvFvLQpguDlw6ds2dOiiqby+UwE8JnoxjEIEP5OnY05TUm+ldpxT6riR\nj4TCXNry2bpwsDaRnC3Zx/YoWTj7sgEVVWGadP3krcXP7dYBtMIds3Ul4Eie\nFuA0\r\n=SMGu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=8"}, "scripts": {"test": "uvu -r esm test -i fixtures", "build": "bundt", "pretest": "npm run build"}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukeed/escalade.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Scale parent directories", "directories": {}, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "3.2.25", "uvu": "0.0.11", "bundt": "1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/escalade_0.0.0_1592544817862_0.7997921389561231", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "escalade", "version": "1.0.0", "keywords": ["find", "parent", "parents", "directory", "search", "walk"], "author": {"url": "https://lukeed.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "escalade@1.0.0", "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "homepage": "https://github.com/lukeed/escalade#readme", "bugs": {"url": "https://github.com/lukeed/escalade/issues"}, "dist": {"shasum": "eba62cba84324dd46dc2ae4fbc15dd5088813e13", "tarball": "https://registry.npmjs.org/escalade/-/escalade-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-WvKmjtpItFC/A7e7XmgTv1bjoJRGirq6fTiV5MK9bmqD0fzWnH1AaVEby0dd3m9XVESNLrnPemSn5G9xcJn1UQ==", "signatures": [{"sig": "MEUCIQDw51zlLepCZ4wIezckJPwc9XYKTjBcocB0KkvW2J2KEQIgW+sQEnrnOwcFf/i+J8Kufx8VeWB6mxwmBKVehaffnh0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10821, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7Q+QCRA9TVsSAnZWagAA2wgP+gNL1UlDf5AsEuWrIv2X\n+7xAId1UBdYLnOTB3EVUUuj3gllZR2DOazcE5e/U8npVJRFvSz+lImKeDoMA\nPAZ/hkbPEqElBUEYR7dt2J4jEFdYdv6H76x050lKyIXzbci4jh0Db72VPaX3\nYFvMXU7tH72pC8HBra6Zv3E3ItK6vSMPjDu28JGk+vL2oUhDk690iOApftUA\n8YQW0wQYrOTWDImAg1bzkcA+DZQ+YI0ftumuUkqMgA7r6j5IerJPAWIWGKNU\nrrsY1sZssR6BNeZlqFBOr84AdqqpAJxx5oAmRpXljP2zRMcx7l9c7FVlPSh4\nDBdQGeFb2ixAWv/FNSlR9R8ecOaWVCXMa9M8ycT0YQIgb1QsNRSYU0pYQx9G\nol74zW1CjjnYMnoMX7QUSA4MxRMLp0kZp6MbwN10B/8JK0B1PvcjjBIH1P5u\nN1l4ahV6wrjOwDMogoLN9PiIpP/4Fx2pDJpZJdP34kr4GS7taXYPUxzvDFb0\nJBMCFwT6NX4XPuqX5SZ3/fuLL5Cblj5WRu5khVfqiA2tvVJVenT38xDN4Me5\ni3Hd3mvno1WCLMY1UTt5VxkiLgov0CJNLGBvXBrPnFiNLD1mvTM9AoAxaOE+\nIx1pOfxgT1kpBEbKQSVckdp7R+IhAhGI0mviJNR3SNE2g+dH1M/dy3o6TFOF\nOQrA\r\n=B+vT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "modes": {"sync": "src/sync.js", "default": "src/async.js"}, "types": "index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=6"}, "exports": {"./": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./sync": {"import": "./sync/index.mjs", "require": "./sync/index.js"}}, "gitHead": "97381e2b163b3ba3617eea146844dc74c491e25d", "scripts": {"test": "uvu -r esm test -i fixtures", "build": "bundt", "pretest": "npm run build"}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukeed/escalade.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A tiny (186B to 214B) and fast utility to ascend parent directories", "directories": {}, "_nodeVersion": "10.13.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "3.2.25", "uvu": "0.0.11", "bundt": "1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/escalade_1.0.0_1592594320131_0.3712813635301244", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "escalade", "version": "2.0.0", "keywords": ["find", "parent", "parents", "directory", "search", "walk"], "author": {"url": "https://lukeed.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "escalade@2.0.0", "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "homepage": "https://github.com/lukeed/escalade#readme", "bugs": {"url": "https://github.com/lukeed/escalade/issues"}, "dist": {"shasum": "0a526670b38a29dd68cf82a019ac0e19599a4bd8", "tarball": "https://registry.npmjs.org/escalade/-/escalade-2.0.0.tgz", "fileCount": 8, "integrity": "sha512-YOlYi0jO9/vqLYFrfyuGP/Hj4UrBPAoj75i0OKrJnbXG2QfIDPCJkJUZQAWE8t6EYiWgN0ZEyBcn/PMbqi8L9A==", "signatures": [{"sig": "MEUCIQDHI+v4gvZ91qlSIG92liUIHhSbzwSkztEemLsiS7bGsgIgPGerDw4JOtEanYi+sZRJxsBTCkxj3tJ/AG+S6lbBzfE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11147, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7WgwCRA9TVsSAnZWagAAr48P/0l6kjO4vaSjUyrwTkR3\nZnBsxPYL41HIlzE1IfLfaUCM+5apg7zHOSEjlTsRzXG0at6OdPHyX/lDlqcj\nKewD+tBMeQT+K7l/gApVX60rvxnsGuCagE8z95WQAwyBwZMvGA/k8oQ0XDtX\nxavVwAoV6qyFp0t2j4eWh84JJKLb31yuAtxN2i1Dex6rhKlcl/wQf096FIjI\nISSKutV3aC91poAiw+mfGhAJYkVWKOEaV817nSlMYHhb7uMFg5NuR8P8WJvx\n+On8vJBEa0Qf+IP15a3EyuBNNh6DJ/nX0YI/7PxaaRPx/0icK89hlH5jdSr3\nJa7PPxQ12E73kgHHJDKV009c/Iscg+6HXNIe7REE00gMOeZKz9xl8VMhl1C4\nkQory8VsT0IeuscXWtIbSZ8ZqUFiIca3wacr1vVSS7MP4ipSNNShS48WjGhb\nSf0ev4Dh6y9d5/N0WEAAWTQ9KhK+A+m3Legd5PeeFP2wlUU31dH5R+50fKLW\nhin+n8L79K5/ZIiUIqJ0MMcT35N5SQV/Tn95SRe47DW+a4n3Vfgi5PmUUWCa\nDDxA8qYZulRIA0oedl3ONa4Ewkb6/1ftNFehtHD2Vddf0e2zkQ4hKakWDDBN\ndpo7LZZTm3YXafSW6LAp2JUzQqr/501MSAriDjI7SqrR9aPCtwPB9IwSlHm7\n9quK\r\n=dkFt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "modes": {"sync": "src/sync.js", "default": "src/async.js"}, "types": "index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=6"}, "exports": {"./": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./sync": {"import": "./sync/index.mjs", "require": "./sync/index.js"}}, "gitHead": "fafa75c0978b608d50c4dec271be4fefeb1be411", "scripts": {"test": "uvu -r esm test -i fixtures", "build": "bundt", "pretest": "npm run build"}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukeed/escalade.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A tiny (196B to 224B) and fast utility to ascend parent directories", "directories": {}, "_nodeVersion": "10.13.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "3.2.25", "uvu": "0.0.11", "bundt": "1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/escalade_2.0.0_1592617007996_0.4323706808823915", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "escalade", "version": "3.0.0", "keywords": ["find", "parent", "parents", "directory", "search", "walk"], "author": {"url": "https://lukeed.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "escalade@3.0.0", "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "homepage": "https://github.com/lukeed/escalade#readme", "bugs": {"url": "https://github.com/lukeed/escalade/issues"}, "dist": {"shasum": "79b81f3c4bebb9c1727f0adba4b0f9d0ff050ca9", "tarball": "https://registry.npmjs.org/escalade/-/escalade-3.0.0.tgz", "fileCount": 8, "integrity": "sha512-mA6h4UpYVBSxD7uq6y9RDtiYd9EMyuzITP1WgPKH2Gd0cDtnGP4LUrIoteH+BWRJfCXl94J/0dNfjUa45Oh+hQ==", "signatures": [{"sig": "MEUCIGu8ON3sJ6uRNLEFkzbCu0FTff2c0CbwIMILXiuh7B6XAiEAva+SVaa68rIA9SaCYHrLA3TVn7QC2oPb1PiqeCQ+c7I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10880, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7Y99CRA9TVsSAnZWagAAeNUP/AuOX7qHAq8jv9AkDypm\n4XOxCa7HmxJmVf6ScJCOJ09duw3rvRhEkTTlE+mo3cQvr48muNtR4swQUkim\nwuY5VYpOkGEe5fufCbjPVfk8ukTrc+a2Rt630hbiEf9tmS2gZB7gV7BugewO\nBA7V9FJGKMlrdlMAl3hocMiYO3AmF0HdwC3OII0elff4f5+CVc1GbsH/LDV8\nlJru6hdmmA8kxEhVsPANnfKxjuNDrFKUu0Hyu565OOgbuIzCo1oSR/J5Yxqc\nM26SLUj8WxMxSAmb8UPDszYrZL8Gim5idTjaGbYwAipC0lRmqGoC47as3BK5\noL83uV3/A/dKhb89+ufIwFoDoJdHP4eCqmTmABwcaQOrQbP2z9wBeJD7ouiN\n+aiTxCdzst1D40siDZUC+W9XS3UGbkKVGP2MIJf2tkefcXtO3LvgwIw1ND6E\nwRiISii3Nzhzmj5gnelZ3VRtSu/YvuS2TH57dvWkrwML+JT5ZVsgY8iH4rhT\njZYiq3cvd6hlBn8AG6fhoFQf1EgpYjeh/0OU2rZbUXKQAc9H1w1K+jICwW7h\n3CYdfbZERMzwff04ZHTdhbD1dpWGJ5PRjV2i8uUQGXZmb9wrdSo5WP31A27e\nkf+VCVIjb4/ws3JVR57AUnhSZ3Bh7P07f8Z6W1CgzKP0j+tI34Vgv02gtdQB\nvu3B\r\n=7Z23\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "modes": {"sync": "src/sync.js", "default": "src/async.js"}, "types": "index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=6"}, "exports": {"./": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./sync": {"import": "./sync/index.mjs", "require": "./sync/index.js"}}, "gitHead": "aac99f4eb8fb8ce98b5143029dc328c83cdfc4d8", "scripts": {"test": "uvu -r esm test -i fixtures", "build": "bundt", "pretest": "npm run build"}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukeed/escalade.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "A tiny (183B to 210B) and fast utility to ascend parent directories", "directories": {}, "_nodeVersion": "12.18.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "3.2.25", "uvu": "0.0.11", "bundt": "1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/escalade_3.0.0_1592627069201_0.6863596103425054", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "escalade", "version": "3.0.1", "keywords": ["find", "parent", "parents", "directory", "search", "walk"], "author": {"url": "https://lukeed.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "escalade@3.0.1", "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "homepage": "https://github.com/lukeed/escalade#readme", "bugs": {"url": "https://github.com/lukeed/escalade/issues"}, "dist": {"shasum": "52568a77443f6927cd0ab9c73129137533c965ed", "tarball": "https://registry.npmjs.org/escalade/-/escalade-3.0.1.tgz", "fileCount": 8, "integrity": "sha512-DR6NO3h9niOT+MZs7bjxlj2a1k+POu5RN8CLTPX2+i78bRi9eLe7+0zXgUHMnGXWybYcL61E9hGhPKqedy8tQA==", "signatures": [{"sig": "MEUCIH2lLSXDu1UzgF907A4foSf4s/xTt3GSzyb6xZ6mvA00AiEA/JzAgOslnVNkd9c1rURlu/dMVU/DpWDKmRBDWXJSyQw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10879, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7wDBCRA9TVsSAnZWagAAr9wP/344IqanV5bptUPqYoD4\nuR9qfQZe2bYRn4RYjhuvhBI6Y9y7SfgKaVZAxnz/QIQU/Pqv12caL3qHmzl5\ntuQewpQ1hRUYQi2D932TRdFSnGQ5Sa/5grjwxMCGhUd1+96iVb75kIlrL+uW\nLfRWWAcUIwrnSDljEDXKBJzk6J7/POVXOVsQ1km3GawgIRvZyguWaFpxyPoD\nGsbSP99ef9VJtRskT9snvqHS8ad4nbkKTqtwcCfk/QkjUI0TDIdNG3TdwoEm\nxGT0ej8C96R9t1b668QbBk41noSgsEOFFGOdssWTdKsvE2aRkqsHNqC9Iez/\nuiXt/FbkKZqlWyOmyZCcKnQZCXz71/Gac3lmqhjntHhl10shLcTn7I9c4AtV\nfcVDmXVtcfN3H5/ELE48WFuW9AUMgH1i25ZbjmXr9NZbgM8zfN5GN81hZ/P4\nebNcogEp+Dq3atDIyop0EPwD9mzJt4C/0CHfXDRieb6a0JQwbSAWBpA+XQdm\n6Tbs+GoHO87qv029d34eRglFh7ZtAM5jFri6OcC0URjz+Scc8gYRjLJ5Xj65\nnCMXfx6Lu/UEESl0zpYQ43VC4UZAwOOmCMPfDhNDdo1lg+TypDPE6iRZ/p+Y\nDWRCCzrtdmU3NStgYK/ts8PdIycLtagERRwDqFgu7iXNKUaVrmUT/MbSs3Ra\n9N8+\r\n=YtWA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "modes": {"sync": "src/sync.js", "default": "src/async.js"}, "types": "index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=6"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./sync": {"import": "./sync/index.mjs", "require": "./sync/index.js"}}, "gitHead": "9a40c76dbb54c4df6eb3468473b3cf6cf3f903de", "scripts": {"test": "uvu -r esm test -i fixtures", "build": "bundt", "pretest": "npm run build"}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukeed/escalade.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "A tiny (183B to 210B) and fast utility to ascend parent directories", "directories": {}, "_nodeVersion": "12.18.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "3.2.25", "uvu": "0.0.11", "bundt": "1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/escalade_3.0.1_1592721601176_0.3074567859393793", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "escalade", "version": "3.0.2", "keywords": ["find", "parent", "parents", "directory", "search", "walk"], "author": {"url": "https://lukeed.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "escalade@3.0.2", "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "homepage": "https://github.com/lukeed/escalade#readme", "bugs": {"url": "https://github.com/lukeed/escalade/issues"}, "dist": {"shasum": "6a580d70edb87880f22b4c91d0d56078df6962c4", "tarball": "https://registry.npmjs.org/escalade/-/escalade-3.0.2.tgz", "fileCount": 8, "integrity": "sha512-gPYAU37hYCUhW5euPeR+Y74F7BL+IBsV93j5cvGriSaD1aG6MGsqsV1yamRdrWrb2j3aiZvb0X+UBOWpx3JWtQ==", "signatures": [{"sig": "MEUCIQCtWT1IHpaeWvv7xItckDsz6nAAv4RpR75eIMZvc8TdMwIgRsoJBtyxsDDOJdGP4i3GIewXAZr9tQAGj4vPvSlkGvo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10879, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDd3dCRA9TVsSAnZWagAAfrMP/185q8AwgoLH9faxGX0m\nfRxZFYqW+piR1BKHAqtQoldi6Wp1c9A9f4yTfyVUz9QfQ6ht5WQdWjW80vaT\nSJZq3zlgjy539RzDbvzL88z6x+Df8yklWwAIPLBuQEn1WWJZUAb5dUuC5F5J\ngvkSgsxOtXTx0mMreCM7nVqlYAxNi28owMwsSLsJouG3/fHnS41yC+MpDyao\nyWrFY4J0vs+eIpeeRd4DXwwjypxQyYZGidAv5IkGIy0ZepABZqapy2NpVaez\n1M70QOBL0CzzI2nDMv+17jhZX4xZKMXayGj1hmPypYni0IERLmr7NgAkednW\nVuFlxPk+fMgKPcwnz/5ajCXW0gouFpET6Rhn8ZH/2WbXqZxcFx463+B/NTzo\neg6TM9gVZED/VY1I/A0SJL5lMpKCoZg1qfoJ9WPD1yyYJ9FLnrJHsmqf1tKp\nvZBITjtVchtaWC6AwlrLQmxmdfh1bCo8wdKjyXgKtOMUj80MQesLtJ4Lxc6B\nf8ls2IcNYe8vTH1q59rQpYPAEtA5XTC+W418lpzSwQSALxE3W2gNVzvFIwV/\nQopzru4Pb9PdoQABckws9DWTbXQQYblFuNqnooIGoaTybVCodWFNJWNwu/Ik\njzLiS+L6svcQvwUKClaU0HICclYC2SucMhlJmhL0Hj0pre1pIdXgX8Qo1y5Y\nHgqg\r\n=LRdZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "modes": {"sync": "src/sync.js", "default": "src/async.js"}, "types": "index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=6"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./sync": {"import": "./sync/index.mjs", "require": "./sync/index.js"}}, "gitHead": "00e5a242da88a09034d7784df686d14f246cd28e", "scripts": {"test": "uvu -r esm test -i fixtures", "build": "bundt", "pretest": "npm run build"}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukeed/escalade.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "A tiny (183B to 210B) and fast utility to ascend parent directories", "directories": {}, "_nodeVersion": "12.18.2", "_hasShrinkwrap": false, "devDependencies": {"esm": "3.2.25", "uvu": "0.0.11", "bundt": "1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/escalade_3.0.2_1594744284416_0.41136776208533954", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "escalade", "version": "3.1.0", "keywords": ["find", "parent", "parents", "directory", "search", "walk"], "author": {"url": "https://lukeed.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "escalade@3.1.0", "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "homepage": "https://github.com/lukeed/escalade#readme", "bugs": {"url": "https://github.com/lukeed/escalade/issues"}, "dist": {"shasum": "e8e2d7c7a8b76f6ee64c2181d6b8151441602d4e", "tarball": "https://registry.npmjs.org/escalade/-/escalade-3.1.0.tgz", "fileCount": 9, "integrity": "sha512-mAk+hPSO8fLDkhV7V0dXazH5pDc6MrjBTPyD3VeKzxnVFjH1MIxbCdqGZB9O8+EwWakZs3ZCbDS4IpRt79V1ig==", "signatures": [{"sig": "MEUCIQCTtdiqEoqE5/KXp+HYg0aG1AWIfjHU3aZ8ef+L7pUyWwIgALt6yeKqOpgGqFlr561kVV1R095PRMeBT41FGX5McCE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11344, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfX6n5CRA9TVsSAnZWagAAvYUP/0BRjGl9OZWIBX/RfOoQ\nVKt6pGtcIZO/ZztTezQxXwX17ZHyodVdbDRjzYunFDHTtHZPCTLPL/77RgOV\nBM+vvrmVIP9E99AsGzMcm9c5pYcWepU+R+V0mPlCI4S2/20oXNxeg17MUw1A\nN645mv3O0+oWCg9ovjO3NZM8hkh4dqG1QX0BbWWrOfzDLsle9vgt68hgQFkY\nP4fe9jNnRYAmVfKmdVNRgum3BVIol1ygIh84YOMuSfNboSDblwzQz8zGBE2b\nhH3TluBeRZ1hlj0RxukjOhN+xpD5fl/+2Zjit/YDxcsxyx+wE6RnXtiBrwCT\nEDNFcHAv1Vcj2GyssS3CIm/pN71ChyD2Ujor7Nmf3zsLUq10hBGZp2NpJ36q\nQaBbBSseJljBt9GjPe3iYFpzH0WxBOmGzqaMLlCAfqOVrHPHHamhXLndTuFv\neiRv1MEyIHLo/Ivnt1l5b7QjmQ3i+/Un8H2h7F7GgNXr1zu5SnJCsbbxg/5j\nNnvZOIAiSDf4qXuRZT0PK1ASjkCf6EHRkK8IvM6h4rCBuaQ8XlFrNOiAg39c\nyv0HOiZ9dnOxeMc4yD3A9ajc1hJPOWy/kc2V+19bElgCsj+rTpulZlHUZHQ3\niUd9p+VSjEa353voOBbFPb6naTBwbRztEXaNKxTQ+FN/XDBn1UuESE7VViXO\nrCAe\r\n=KRx1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "modes": {"sync": "src/sync.js", "default": "src/async.js"}, "types": "index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=6"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./sync": {"import": "./sync/index.mjs", "require": "./sync/index.js"}}, "gitHead": "717487994128bd22bc8bc4318496fadbf5cab746", "scripts": {"test": "uvu -r esm test -i fixtures", "build": "bundt", "pretest": "npm run build"}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukeed/escalade.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "A tiny (183B to 210B) and fast utility to ascend parent directories", "directories": {}, "_nodeVersion": "14.10.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "3.2.25", "uvu": "0.3.3", "bundt": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/escalade_3.1.0_1600104953222_0.47528761525994767", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "escalade", "version": "3.1.1", "keywords": ["find", "parent", "parents", "directory", "search", "walk"], "author": {"url": "https://lukeed.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "escalade@3.1.1", "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "homepage": "https://github.com/lukeed/escalade#readme", "bugs": {"url": "https://github.com/lukeed/escalade/issues"}, "dist": {"shasum": "d8cfdc7000965c5a0174b4a82eaa5c0552742e40", "tarball": "https://registry.npmjs.org/escalade/-/escalade-3.1.1.tgz", "fileCount": 9, "integrity": "sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==", "signatures": [{"sig": "MEQCIBqbSV18IHNszA9TZQJ/C4u+AQ934CWMh5jwYlBr7MeqAiBe/LnFmB8tCqdBdsOhtK9AvRboHFs/Ikpf6yzhxAjbyA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11434, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfhUNACRA9TVsSAnZWagAAxgUP/A8GPqV1Tm+m3shzqfBA\nHUKtBmVn7xCHFAm8sw1aHCPQHoV3cWZUR+zVJyXnVXg5w8Mw93733m7H3DSO\njol+uLlCeVTtgFVqgw6f5puC8+6vPaTwQav612Tk6uBc4RUL00pQf/Td9k0P\nyVpSudXUT2RnyRWOkJ7T9/kNS3tHsyl+AVhH2kO+QUqUe4N5go7D8juWO4H1\n/Jia3Mv7Eh8Ex9onDWavMVqudkWvioWkVQrrCqa+WHzEcp9thnd7iFhkoqM/\nVYDlEk41E+EDhOOvtRwhvQtklQJujBOPpIDsYNEmyc0w+HpNDkYAklWlP/Y6\nNB3jSh44XUvCF4Iq0lNMeNOi+dUFLyMQd8bxXbDMzVispLmmJTr043K9ZB/c\nChy0OV28fWpWA/iz5/5D9Al8tv3VUHSI0apRHkUS7hH18zosLfIOe1lG3XHm\n99pIdlPABgrRek4zSl9SVL3/kr5u8CCMYiqH10OBMm/3agFC4Ia0VU1k7Zm1\n6uvAbbCUhdLpUFbJyKjQJIloSWcyrWCDzQ7yPYB/eQMnBqWF51/nR0RuSBpQ\nRbRkhZd4g9Ndyot41ImqRz9FcOsOzEoJZumsav97JpDV0NXXP3ZtJaLrwHJ8\n2bcGDm6KaxpA9BpLzeYaKyZ1XeYSjwpsPa1RSJp2piZTqSmh2ZzTxvmEQkmL\noD/u\r\n=S3nB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "modes": {"sync": "src/sync.js", "default": "src/async.js"}, "types": "index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=6"}, "exports": {".": [{"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./dist/index.js"], "./sync": [{"import": "./sync/index.mjs", "require": "./sync/index.js"}, "./sync/index.js"]}, "gitHead": "2477005062cdbd8407afc90d3f48f4930354252b", "scripts": {"test": "uvu -r esm test -i fixtures", "build": "bundt", "pretest": "npm run build"}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukeed/escalade.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "A tiny (183B to 210B) and fast utility to ascend parent directories", "directories": {}, "_nodeVersion": "14.10.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "3.2.25", "uvu": "0.3.3", "bundt": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/escalade_3.1.1_1602569023852_0.8272593854757944", "host": "s3://npm-registry-packages"}}, "3.1.2": {"name": "escalade", "version": "3.1.2", "keywords": ["find", "parent", "parents", "directory", "search", "walk"], "author": {"url": "https://lukeed.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "escalade@3.1.2", "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "homepage": "https://github.com/lukeed/escalade#readme", "bugs": {"url": "https://github.com/lukeed/escalade/issues"}, "dist": {"shasum": "54076e9ab29ea5bf3d8f1ed62acffbb88272df27", "tarball": "https://registry.npmjs.org/escalade/-/escalade-3.1.2.tgz", "fileCount": 9, "integrity": "sha512-ErCHMCae19vR8vQGe50xIsVomy19rg6gFu3+r3jkEO46suLMWBksvVyoGgQV+jOfl84ZSOSlmv6Gxa89PmTGmA==", "signatures": [{"sig": "MEQCIHWHoPywlP+zGsrPqQpjVa/hKql6fPWzHWAQFbu2PRQRAiAmcrE8BxbCn62DkCKWwimqRrjYZaMf6hhL68r7VA0vxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11591}, "main": "dist/index.js", "modes": {"sync": "src/sync.js", "default": "src/async.js"}, "types": "index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=6"}, "exports": {".": [{"types": "./index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./dist/index.js"], "./sync": [{"types": "./sync/index.d.ts", "import": "./sync/index.mjs", "require": "./sync/index.js"}, "./sync/index.js"]}, "gitHead": "35bbaa0e19f12cb9515542a25eba5ee9f3900d6f", "scripts": {"test": "uvu -r esm test -i fixtures", "build": "bundt", "pretest": "npm run build"}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukeed/escalade.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "A tiny (183B to 210B) and fast utility to ascend parent directories", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "3.2.25", "uvu": "0.3.3", "bundt": "1.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/escalade_3.1.2_1707150719202_0.9356232642555491", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "escalade", "version": "3.2.0", "repository": {"type": "git", "url": "git+https://github.com/lukeed/escalade.git"}, "description": "A tiny (183B to 210B) and fast utility to ascend parent directories", "module": "dist/index.mjs", "main": "dist/index.js", "types": "index.d.ts", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "exports": {".": [{"import": {"types": "./index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./index.d.ts", "default": "./dist/index.js"}}, "./dist/index.js"], "./sync": [{"import": {"types": "./sync/index.d.mts", "default": "./sync/index.mjs"}, "require": {"types": "./sync/index.d.ts", "default": "./sync/index.js"}}, "./sync/index.js"]}, "modes": {"sync": "src/sync.js", "default": "src/async.js"}, "engines": {"node": ">=6"}, "scripts": {"build": "bundt", "pretest": "npm run build", "test": "uvu -r esm test -i fixtures"}, "keywords": ["find", "parent", "parents", "directory", "search", "walk"], "devDependencies": {"bundt": "1.1.1", "esm": "3.2.25", "uvu": "0.3.3"}, "_id": "escalade@3.2.0", "gitHead": "fa5be167391581849cff04dbc75dc2978744b642", "bugs": {"url": "https://github.com/lukeed/escalade/issues"}, "homepage": "https://github.com/lukeed/escalade#readme", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==", "shasum": "011a3f69856ba189dffa7dc8fcce99d2a87903e5", "tarball": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "fileCount": 11, "unpackedSize": 12385, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGbh1u054ZOcKJ1m/VcM9Ebw9OV2rRD108wfM2VsZPcgIgdaaa62M7U4VIgHfaglQ0eAh0mROuuzjzoyoA+dJOwBE="}]}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escalade_3.2.0_1724972376536_0.0521928522218551"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-06-19T05:33:37.862Z", "modified": "2024-08-29T22:59:36.901Z", "0.0.0": "2020-06-19T05:33:38.006Z", "1.0.0": "2020-06-19T19:18:40.240Z", "2.0.0": "2020-06-20T01:36:48.148Z", "3.0.0": "2020-06-20T04:24:29.337Z", "3.0.1": "2020-06-21T06:40:01.316Z", "3.0.2": "2020-07-14T16:31:24.535Z", "3.1.0": "2020-09-14T17:35:53.351Z", "3.1.1": "2020-10-13T06:03:44.007Z", "3.1.2": "2024-02-05T16:31:59.413Z", "3.2.0": "2024-08-29T22:59:36.690Z"}, "bugs": {"url": "https://github.com/lukeed/escalade/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "license": "MIT", "homepage": "https://github.com/lukeed/escalade#readme", "keywords": ["find", "parent", "parents", "directory", "search", "walk"], "repository": {"type": "git", "url": "git+https://github.com/lukeed/escalade.git"}, "description": "A tiny (183B to 210B) and fast utility to ascend parent directories", "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "readme": "# escalade [![CI](https://github.com/lukeed/escalade/workflows/CI/badge.svg)](https://github.com/lukeed/escalade/actions) [![licenses](https://licenses.dev/b/npm/escalade)](https://licenses.dev/npm/escalade) [![codecov](https://badgen.now.sh/codecov/c/github/lukeed/escalade)](https://codecov.io/gh/lukeed/escalade)\n\n> A tiny (183B to 210B) and [fast](#benchmarks) utility to ascend parent directories\n\nWith [escalade](https://en.wikipedia.org/wiki/Escalade), you can scale parent directories until you've found what you're looking for.<br>Given an input file or directory, `escalade` will continue executing your callback function until either:\n\n1) the callback returns a truthy value\n2) `escalade` has reached the system root directory (eg, `/`)\n\n> **Important:**<br>Please note that `escalade` only deals with direct ancestry – it will not dive into parents' sibling directories.\n\n---\n\n**Notice:** As of v3.1.0, `escalade` now includes [Deno support](http://deno.land/x/escalade)! Please see [Deno Usage](#deno) below.\n\n---\n\n## Install\n\n```\n$ npm install --save escalade\n```\n\n\n## Modes\n\nThere are two \"versions\" of `escalade` available:\n\n#### \"async\"\n> **Node.js:** >= 8.x<br>\n> **Size (gzip):** 210 bytes<br>\n> **Availability:** [CommonJS](https://unpkg.com/escalade/dist/index.js), [ES Module](https://unpkg.com/escalade/dist/index.mjs)\n\nThis is the primary/default mode. It makes use of `async`/`await` and [`util.promisify`](https://nodejs.org/api/util.html#util_util_promisify_original).\n\n#### \"sync\"\n> **Node.js:** >= 6.x<br>\n> **Size (gzip):** 183 bytes<br>\n> **Availability:** [CommonJS](https://unpkg.com/escalade/sync/index.js), [ES Module](https://unpkg.com/escalade/sync/index.mjs)\n\nThis is the opt-in mode, ideal for scenarios where `async` usage cannot be supported.\n\n\n## Usage\n\n***Example Structure***\n\n```\n/Users/<USER>\n  └── oss\n    ├── license\n    └── escalade\n      ├── package.json\n      └── test\n        └── fixtures\n          ├── index.js\n          └── foobar\n            └── demo.js\n```\n\n***Example Usage***\n\n```js\n//~> demo.js\nimport { join } from 'path';\nimport escalade from 'escalade';\n\nconst input = join(__dirname, 'demo.js');\n// or: const input = __dirname;\n\nconst pkg = await escalade(input, (dir, names) => {\n  console.log('~> dir:', dir);\n  console.log('~> names:', names);\n  console.log('---');\n\n  if (names.includes('package.json')) {\n    // will be resolved into absolute\n    return 'package.json';\n  }\n});\n\n//~> dir: /Users/<USER>/oss/escalade/test/fixtures/foobar\n//~> names: ['demo.js']\n//---\n//~> dir: /Users/<USER>/oss/escalade/test/fixtures\n//~> names: ['index.js', 'foobar']\n//---\n//~> dir: /Users/<USER>/oss/escalade/test\n//~> names: ['fixtures']\n//---\n//~> dir: /Users/<USER>/oss/escalade\n//~> names: ['package.json', 'test']\n//---\n\nconsole.log(pkg);\n//=> /Users/<USER>/oss/escalade/package.json\n\n// Now search for \"missing123.txt\"\n// (Assume it doesn't exist anywhere!)\nconst missing = await escalade(input, (dir, names) => {\n  console.log('~> dir:', dir);\n  return names.includes('missing123.txt') && 'missing123.txt';\n});\n\n//~> dir: /Users/<USER>/oss/escalade/test/fixtures/foobar\n//~> dir: /Users/<USER>/oss/escalade/test/fixtures\n//~> dir: /Users/<USER>/oss/escalade/test\n//~> dir: /Users/<USER>/oss/escalade\n//~> dir: /Users/<USER>/oss\n//~> dir: /Users/<USER>\n//~> dir: /Users\n//~> dir: /\n\nconsole.log(missing);\n//=> undefined\n```\n\n> **Note:** To run the above example with \"sync\" mode, import from `escalade/sync` and remove the `await` keyword.\n\n\n## API\n\n### escalade(input, callback)\nReturns: `string|void` or `Promise<string|void>`\n\nWhen your `callback` locates a file, `escalade` will resolve/return with an absolute path.<br>\nIf your `callback` was never satisfied, then `escalade` will resolve/return with nothing (undefined).\n\n> **Important:**<br>The `sync` and `async` versions share the same API.<br>The **only** difference is that `sync` is not Promise-based.\n\n#### input\nType: `string`\n\nThe path from which to start ascending.\n\nThis may be a file or a directory path.<br>However, when `input` is a file, `escalade` will begin with its parent directory.\n\n> **Important:** Unless given an absolute path, `input` will be resolved from `process.cwd()` location.\n\n#### callback\nType: `Function`\n\nThe callback to execute for each ancestry level. It always is given two arguments:\n\n1) `dir` - an absolute path of the current parent directory\n2) `names` - a list (`string[]`) of contents _relative to_ the `dir` parent\n\n> **Note:** The `names` list can contain names of files _and_ directories.\n\nWhen your callback returns a _falsey_ value, then `escalade` will continue with `dir`'s parent directory, re-invoking your callback with new argument values.\n\nWhen your callback returns a string, then `escalade` stops iteration immediately.<br>\nIf the string is an absolute path, then it's left as is. Otherwise, the string is resolved into an absolute path _from_ the `dir` that housed the satisfying condition.\n\n> **Important:** Your `callback` can be a `Promise/AsyncFunction` when using the \"async\" version of `escalade`.\n\n## Benchmarks\n\n> Running on Node.js v10.13.0\n\n```\n# Load Time\n  find-up         3.891ms\n  escalade        0.485ms\n  escalade/sync   0.309ms\n\n# Levels: 6 (target = \"foo.txt\"):\n  find-up          x 24,856 ops/sec ±6.46% (55 runs sampled)\n  escalade         x 73,084 ops/sec ±4.23% (73 runs sampled)\n  find-up.sync     x  3,663 ops/sec ±1.12% (83 runs sampled)\n  escalade/sync    x  9,360 ops/sec ±0.62% (88 runs sampled)\n\n# Levels: 12 (target = \"package.json\"):\n  find-up          x 29,300 ops/sec ±10.68% (70 runs sampled)\n  escalade         x 73,685 ops/sec ± 5.66% (66 runs sampled)\n  find-up.sync     x  1,707 ops/sec ± 0.58% (91 runs sampled)\n  escalade/sync    x  4,667 ops/sec ± 0.68% (94 runs sampled)\n\n# Levels: 18 (target = \"missing123.txt\"):\n  find-up          x 21,818 ops/sec ±17.37% (14 runs sampled)\n  escalade         x 67,101 ops/sec ±21.60% (20 runs sampled)\n  find-up.sync     x  1,037 ops/sec ± 2.86% (88 runs sampled)\n  escalade/sync    x  1,248 ops/sec ± 0.50% (93 runs sampled)\n```\n\n## Deno\n\nAs of v3.1.0, `escalade` is available on the Deno registry.\n\nPlease note that the [API](#api) is identical and that there are still [two modes](#modes) from which to choose:\n\n```ts\n// Choose \"async\" mode\nimport escalade from 'https://deno.land/escalade/async.ts';\n\n// Choose \"sync\" mode\nimport escalade from 'https://deno.land/escalade/sync.ts';\n```\n\n> **Important:** The `allow-read` permission is required!\n\n\n## Related\n\n- [premove](https://github.com/lukeed/premove) - A tiny (247B) utility to remove items recursively\n- [totalist](https://github.com/lukeed/totalist) - A tiny (195B to 224B) utility to recursively list all (total) files in a directory\n- [mk-dirs](https://github.com/lukeed/mk-dirs) - A tiny (420B) utility to make a directory and its parents, recursively\n\n## License\n\nMIT © [Luke Edwards](https://lukeed.com)\n", "readmeFilename": "readme.md"}