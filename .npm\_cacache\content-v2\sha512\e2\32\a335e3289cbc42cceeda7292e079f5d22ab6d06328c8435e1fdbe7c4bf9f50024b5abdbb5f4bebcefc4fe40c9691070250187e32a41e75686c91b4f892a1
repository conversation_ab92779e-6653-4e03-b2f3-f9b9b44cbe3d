{"_id": "@types/mime", "_rev": "645-8c6a304ed23a1f792f7bceb920ed1688", "name": "@types/mime", "description": "Stub TypeScript definitions entry for mime, which provides its own types definitions", "dist-tags": {"latest": "4.0.0", "ts2.0": "2.0.1", "ts2.1": "2.0.1", "ts2.2": "2.0.1", "ts2.3": "2.0.1", "ts2.4": "2.0.1", "ts2.5": "2.0.1", "ts2.6": "2.0.1", "ts2.7": "2.0.1", "ts2.8": "2.0.1", "ts2.9": "2.0.1", "ts3.0": "2.0.3", "ts3.1": "2.0.3", "ts3.2": "2.0.3", "ts3.3": "2.0.3", "ts3.4": "2.0.3", "ts3.5": "2.0.3", "ts3.6": "2.0.3", "ts3.7": "2.0.3", "ts3.8": "2.0.3", "ts3.9": "2.0.3", "ts4.0": "3.0.1", "ts4.1": "3.0.1", "ts4.2": "3.0.1", "ts4.3": "3.0.1", "ts4.4": "3.0.1", "ts4.5": "3.0.4", "ts4.6": "3.0.4", "ts4.7": "3.0.4", "ts4.8": "3.0.4", "ts4.9": "3.0.4", "ts5.0": "3.0.4", "ts5.1": "3.0.4", "ts5.2": "3.0.4", "ts5.3": "3.0.4", "ts5.4": "3.0.4", "ts5.5": "3.0.4"}, "versions": {"0.0.15-alpha": {"name": "@types/mime", "version": "0.0.15-alpha", "description": "Type definitions for mime from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/jedigo"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {}, "_id": "@types/mime@0.0.15-alpha", "_shasum": "45cd31154dc7cbb5539cd465239d7f6d5b03f7c5", "_from": "output\\mime", "_resolved": "file:output\\mime", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "45cd31154dc7cbb5539cd465239d7f6d5b03f7c5", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-0.0.15-alpha.tgz", "integrity": "sha512-8qHn5USTlWCPey1zFH14D/y4ebtH20ls2CeuiB6ZaPR6FUpxZWXrg+XSACtdmt/bEbxtnfTzw64CCv1IZbIcQg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCvJaAkM3+bzwoyxnkowW++w2yS9D0Q7yJCCRIAbIE1fwIgeXtQakt4e79cNRTsLVIyjTVD9S0f7BYgXGkiwV4b5yc="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/mime-0.0.15-alpha.tgz_1463509247611_0.3217241014353931"}, "directories": {}}, "0.0.16-alpha": {"name": "@types/mime", "version": "0.0.16-alpha", "description": "Type definitions for mime from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/jedigo"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {}, "_id": "@types/mime@0.0.16-alpha", "_shasum": "b1c8b4aa1da8a0050b2a72c29ab0d43f37a6a732", "_from": "output\\mime", "_resolved": "file:output\\mime", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "b1c8b4aa1da8a0050b2a72c29ab0d43f37a6a732", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-0.0.16-alpha.tgz", "integrity": "sha512-D2MCrpabj+4O9zxCmsixwDmQyn6z1BdxHokmJbJ63dzL2eW3XpyV54a6mLSLgvY1HJuSLtUwW4AaLSFZLZQdrQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBpoWA5w5CKXAxZW2gkbTxuh4m/wSE9/OHL/s5mKMTfrAiAST7Wv32Ezo+cohOqToRQK6OEbrTIBT7w79c5eN9WMJw=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/mime-0.0.16-alpha.tgz_1463694198690_0.19705815287306905"}, "directories": {}}, "0.0.21-alpha": {"name": "@types/mime", "version": "0.0.21-alpha", "description": "TypeScript definitions for mime", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/jedigo"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {}, "_id": "@types/mime@0.0.21-alpha", "_shasum": "28dde1e93079cd35df78e9e96de30c0b6566cecf", "_from": "output\\mime", "_resolved": "file:output\\mime", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "28dde1e93079cd35df78e9e96de30c0b6566cecf", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-0.0.21-alpha.tgz", "integrity": "sha512-mGaQS6i+XW6479My52SAg3M4RQ+03jt3rbu/tN5BG80A9RyeReg72QzKRK9I/oU1QNBOCa3IEIOx1OROVb4TYQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCyP4Oc1oCl9DwYq1Olzd1exUeN5x4ZQXZboaPK3PwiRQIhAI9TuNKVjhc2HrbfNopcYDb9HW3DGX5s0uX74vHqv8P2"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/mime-0.0.21-alpha.tgz_1463775064199_0.19318236084654927"}, "directories": {}}, "0.0.22-alpha": {"name": "@types/mime", "version": "0.0.22-alpha", "description": "TypeScript definitions for mime", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/jedigo"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {}, "_id": "@types/mime@0.0.22-alpha", "_shasum": "0cbaa4033568a7b59be068f3bc7fcce4e7cb06e2", "_from": "output\\mime", "_resolved": "file:output\\mime", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "0cbaa4033568a7b59be068f3bc7fcce4e7cb06e2", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-0.0.22-alpha.tgz", "integrity": "sha512-Bn937/rqYUJlkbGBx8yQp37vWs4FZZRg5xY6dpm3xiFPKg2HTcxkHBk29Is0JjjKk5uXufP7gWMI8IAg4tskoQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICbbCZ5Cb9Kn5Xmvxx0M4/IsbQVRRcBmxepK479wPdbdAiBc1RB5I1JTr46IIywJy2+LfYQ+jJ7HRIYTNvnWxxH6yQ=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/mime-0.0.22-alpha.tgz_1464154114397_0.585701884701848"}, "directories": {}}, "0.0.23-alpha": {"name": "@types/mime", "version": "0.0.23-alpha", "description": "TypeScript definitions for mime", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/jedigo"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {}, "_id": "@types/mime@0.0.23-alpha", "_shasum": "8d5fbbeae3a900b4ab1675f4f04426751b116596", "_from": "output\\mime", "_resolved": "file:output\\mime", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "8d5fbbeae3a900b4ab1675f4f04426751b116596", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-0.0.23-alpha.tgz", "integrity": "sha512-NXWQIYde84gU3qqJUh6EGNWMSWAsJ4hkz44J+A/LlkcVn9dbN5UvBP1qC71GnJi//HvcU1L/IU3qPqNmQYUjow==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFUmXVlg7Wsk+u0T0IQoOIUYjMGsTXAfavPDCnEZydDWAiEAj+z6ELFfozUDtQ2eKudJlkBGnfibCJt8/PlPvw8Rvvs="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/mime-0.0.23-alpha.tgz_1467403787354_0.6990488793235272"}, "directories": {}}, "0.0.24-alpha": {"name": "@types/mime", "version": "0.0.24-alpha", "description": "TypeScript definitions for mime", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/jedigo"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {}, "_id": "@types/mime@0.0.24-alpha", "_shasum": "9b6ed8658556f694846519c83781592d6accf6be", "_from": "output\\mime", "_resolved": "file:output\\mime", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "9b6ed8658556f694846519c83781592d6accf6be", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-0.0.24-alpha.tgz", "integrity": "sha512-Qr5qaUiDzE43LwcggNXXo3NX8pZii77L+j6W9R9QFPGTgQAVklReI+7IYdSbk/xSkBfF8rbTD0lRjme2TVzfYQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA/6ajfkDFkbSIlHzfVg/5nNGjv/pPvWYG2EfhwwsKTGAiAixaZA8urgaERy2yF7fZkLKdOxSyDVWn+l/3gp0TpBNQ=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/mime-0.0.24-alpha.tgz_1467415799570_0.0833575886208564"}, "directories": {}}, "0.0.25-alpha": {"name": "@types/mime", "version": "0.0.25-alpha", "description": "TypeScript definitions for mime", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/jedigo"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {}, "_id": "@types/mime@0.0.25-alpha", "_shasum": "a6dadecd9e9915ef424567cac2c3e5e2625e9859", "_from": "output\\mime", "_resolved": "file:output\\mime", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "a6dadecd9e9915ef424567cac2c3e5e2625e9859", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-0.0.25-alpha.tgz", "integrity": "sha512-7Ku1fJBWpbsXgpf3mkJC7QzJlRolZbsJ+aKv7qkaVkrtwZi8jCmrgaHWzPh5PgO6Oh5UlbEPJ9t8XJYzHQbPtw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID6tl6q3oLDRn2aPBxtFwh2I+2mBgpJy2FklCdQx8E1kAiBxTEgODU43BLm69KTso3jgC/+B5Bb+HrIkGX+ewXSz3g=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/mime-0.0.25-alpha.tgz_1467428268498_0.6706391565967351"}, "directories": {}}, "0.0.26-alpha": {"name": "@types/mime", "version": "0.0.26-alpha", "description": "TypeScript definitions for mime", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/jedigo"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {}, "_id": "@types/mime@0.0.26-alpha", "_shasum": "da68f4d0deda12f5cad9f07be2ca259d28b4b56e", "_from": "output\\mime", "_resolved": "file:output\\mime", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "da68f4d0deda12f5cad9f07be2ca259d28b4b56e", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-0.0.26-alpha.tgz", "integrity": "sha512-Y4yWZX4V8D0meFOjRgOVOv3eZ4DoLn5XdA2HbQc0K4qRfqLeP2PCOBJZ546Rxa0agm6ArIbgAfUWby4LuXJCog==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZPJWd95Ws2ItZj7UpuHPUsHqh93jtbmjX0PXGuci8uQIgHplXVWMSNLFBn3cWjsMDEfMvRBHyrlP+hkdaBAGW1zg="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/mime-0.0.26-alpha.tgz_1467593593913_0.17793670832179487"}, "directories": {}}, "0.0.27-alpha": {"name": "@types/mime", "version": "0.0.27-alpha", "description": "TypeScript definitions for mime", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/jedigo"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {}, "_id": "@types/mime@0.0.27-alpha", "_shasum": "bf97707169661269014c4a85fec83ea1a0e87962", "_from": "output\\mime", "_resolved": "file:output\\mime", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "bf97707169661269014c4a85fec83ea1a0e87962", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-0.0.27-alpha.tgz", "integrity": "sha512-UF6ME4cxgLGuzqvOQWDkFm3PNhnxd0XVRSe0NvG55dEAY6vesgczosmubd3HfQJoH3m2cFi6rjGXb8z7H6kJiA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDguNn33LwLP+hvexENOrZCEidXSfD2d/GGszxZDOMJkwIgW4OfvjmUaqDL3lO8jtFJ1LalHLCigCLkwJCcD10s31o="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/mime-0.0.27-alpha.tgz_1468011093524_0.3766513706650585"}, "directories": {}}, "0.0.28": {"name": "@types/mime", "version": "0.0.28", "description": "TypeScript definitions for mime", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/jedigo"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {}, "_id": "@types/mime@0.0.28", "_shasum": "57c3a60e773632066c82b0d046bf768c9b401ff6", "_from": "output\\mime", "_resolved": "file:output\\mime", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "57c3a60e773632066c82b0d046bf768c9b401ff6", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-0.0.28.tgz", "integrity": "sha512-Gdba9hgQuat/n8Q8aES0MEiBwqz/zOSBNMrW9J0BUF7lrIYJwznI4xpPQtcrExx9UmESgnS0br48S6mw1w4Z8w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJNRSX4puQKmvzYTZnDaxbJTlkzCXLdQGy9agWGSQi+wIhAI2UHzXF+RDUfxXoORkq77JcGd/i9/pFo+2ngDgLRiSO"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/mime-0.0.28.tgz_1468509846970_0.9029851392842829"}, "directories": {}}, "0.0.29": {"name": "@types/mime", "version": "0.0.29", "description": "TypeScript definitions for mime", "license": "MIT", "author": "<PERSON> <https://github.com/jedigo>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {}, "typings": "index.d.ts", "typesPublisherContentHash": "3ffa4132b090a4f452c7f9ad0263fda5008be71108fe473635886dc8d1437b58", "_id": "@types/mime@0.0.29", "dist": {"shasum": "fbcfd330573b912ef59eeee14602bface630754b", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-0.0.29.tgz", "integrity": "sha512-EqWQSlonwbNgLMq2dMkokuzv/pwevb4q0JrPjfc7zzieG/cpqt+HsCE9dYoQd1snp2zlksl6k3rQ4LLfyQbQdA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBNxfmWA6ci9mM1futzLJONcQWp6npQJE8Iyw1qvvi8jAiEAm7aDE9tDeXPV517VUcj88bMS9AZUoBXjkxlsXoU4e3w="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/mime-0.0.29.tgz_1474307517085_0.034191184444352984"}, "directories": {}}, "1.3.0": {"name": "@types/mime", "version": "1.3.0", "description": "TypeScript definitions for mime", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/jedigo"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "6a2b3d2dd50108712f2c91ef37eb70d024362cfaaf20a46325083ac27ec9a7c6", "typeScriptVersion": "2.0", "_id": "@types/mime@1.3.0", "dist": {"integrity": "sha512-pZ3X4wf0T07Zcp383IJLTrPBDLo/s9DAQQ7QkDMLJpoDNosUkSRSkbXt8OExwWkK8OEPzwqzBz7XvBHofCXcsA==", "shasum": "d24ffac7d1006fe68517202fb2aeba3dbe48284b", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-1.3.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHznpixsRV9PY+Fny2rxCkI1dncrlgngRGFLo8iSw+AYAiEA+zUNMgNeEL7kcZXo28L4J8gV+2fwgFDUW93BR6lPFbs="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-1.3.0.tgz_1497564187439_0.78930863016285"}, "directories": {}}, "1.3.1": {"name": "@types/mime", "version": "1.3.1", "description": "TypeScript definitions for mime", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/jedigo"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "a202774c75ea5bbc14f80c3c0c6eb0041ff65c04079c674e1b7e3657127f4153", "typeScriptVersion": "2.0", "_id": "@types/mime@1.3.1", "dist": {"integrity": "sha512-rek8twk9C58gHYqIrUlJsx8NQMhlxqHzln9Z9ODqiNgv3/s+ZwIrfr+djqzsnVM12xe9hL98iJ20lj2RvCBv6A==", "shasum": "2cf42972d0931c1060c7d5fa6627fce6bd876f2f", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-1.3.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFtd51bV8M50Oz4xgMDg59+yY/x4g4ev85FzFJ86hOqAAiEA6txOn694bkYOfsTPaZCqJXocFBE8KBPOgAPJQbZi33M="}]}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-1.3.1.tgz_1497991697361_0.23746417299844325"}, "directories": {}}, "2.0.0": {"name": "@types/mime", "version": "2.0.0", "description": "TypeScript definitions for mime", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/jedigo", "githubUsername": "jedigo"}, {"name": "<PERSON>", "url": "https://github.com/dhritzkiv", "githubUsername": "<PERSON>hr<PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "719b27892c0327424f3d155be034d74dc26a16ed6022ae6f9a9cb8872f8fa70e", "typeScriptVersion": "2.0", "_id": "@types/mime@2.0.0", "dist": {"integrity": "sha512-A2TAGbTFdBw9azHbpVd+/FkdW2T6msN1uct1O9bH3vTerEHKZhTXJUQXy+hNq1B0RagfU8U+KBdqiZpxjhOUQA==", "shasum": "5a7306e367c539b9f6543499de8dd519fac37a8b", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-2.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHax0ksZP01JBp1mKteaBERfi4o3VvGmzRrEhKNC1tvHAiBkIVN5BZtUAC3sBF7g1NymuXSHoVAvi/Qf3MpXYzu+Aw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime-2.0.0.tgz_1505744101448_0.13140471396036446"}, "directories": {}}, "2.0.1": {"name": "@types/mime", "version": "2.0.1", "description": "TypeScript definitions for mime", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/jedigo", "githubUsername": "jedigo"}, {"name": "<PERSON>", "url": "https://github.com/dhritzkiv", "githubUsername": "<PERSON>hr<PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "44440ff7edf5dabbbd9ed5edb3482c642f8167d582192aa9d93b68daaab883cf", "typeScriptVersion": "2.0", "_id": "@types/mime@2.0.1", "dist": {"integrity": "sha512-FwI9gX75FgVBJ7ywgnq/P7tw+/o1GUbtP0KzbtusLigAOgIgNISRK0ZPl4qertvXSIE8YbsVJueQ90cDt9YYyw==", "shasum": "dc488842312a7f075149312905b5e3c0b054c79d", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-2.0.1.tgz", "fileCount": 5, "unpackedSize": 3357, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWgmyCRA9TVsSAnZWagAAu/oP/iX0mcoWBo/RPM98hmEr\nxWfXfmtEbJ4pBHyCHkwm/+Y7ROBKeBOEJrHyuMDpihnssfIft+Icp5X4t+wG\n/NlYldu0lq28Wmhyn2/BsManlgYgvmx1OAS3BuMYZ//kKmXEutm4Bpbfquv/\nWj9a9M0zeesVA3vHN5iGx0ut0fTKT9QmIc3HZFyhcHMk3JHQbAecZuP/CPYd\nJ7Y5sfikNCGx7GY5QUCGIhz382IUmhR+XoRtTPQZ3RqWQHWvWxxnYeehOvJM\nigC6tThHSUyEI3zPB7lp8cEh5QuTe8k6s6yi4pA++7Ldt89INGVZyoJvNmWp\nf4zMhrA+ygdRUuDDxh8RyF/cGMc1w8GFPbQtojOB4CkM0t+3pxQhisjdzbQz\ncWAZ+/znkw0aS/WBk9cRQY/RxZQWloEIfyBcMdiCfx6k8cn6nrbQWk7cHC9R\nq4KjzBGDpEg+9An7Ct3LUEENbeEj8cc6rV2EElu/m1TuR19rBWMWMd6zSPBn\nB0jdxblGd4j9PL9dxXwtMlZpYk4a+wrpHBUYUmvASQ6Q7Fs9Dp3PdCwX63sz\nZWXESKewHc3W/TabYUAMW/iluU/GWmahRxTDkzKAOaNwn12ZC+LVWGCjmX1e\nSPL6u8QMzn5HFFnyHhbu/061BJdfisW+u0yybtxm7eF8+cgRknLm+4TwzTGx\ntKIJ\r\n=1BoV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDr34xGN6E1AESgl/cB/344PuUt/rNfNzY6+F+PONoiKgIgNseB2Df10AdaUtLiQhxLAj0wJdh5oHGG9HuntSya5Uk="}]}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime_2.0.1_1549404593820_0.5034616426253711"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "@types/mime", "version": "2.0.2", "description": "TypeScript definitions for mime", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/jedigo", "githubUsername": "jedigo"}, {"name": "<PERSON>", "url": "https://github.com/dhritzkiv", "githubUsername": "<PERSON>hr<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mime"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "f9d0462f012053c4c38947d2d0c779fbeec4b457ef17192e5020cf095df9489d", "typeScriptVersion": "3.0", "_id": "@types/mime@2.0.2", "dist": {"integrity": "sha512-4kPlzbljFcsttWEq6aBW0OZe6BDajAmyvr2xknBG92tejQnvdGtT9+kXSZ580DqpxY9qG2xeQVF9Dq0ymUTo5Q==", "shasum": "857a118d8634c84bba7ae14088e4508490cd5da5", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-2.0.2.tgz", "fileCount": 5, "unpackedSize": 3372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevmxTCRA9TVsSAnZWagAAzRQP/0oL6d3y7KieBgnqsyOM\n0eR/q5d6u6L3a1qAzk4Ky2kK7Js5ETb/vgyQg4WtuMbGiklcj/l9irqmxuIn\nTZKurcQvKKKxRsKsYHSftfySPkQ/SCupQfmnM06cWZHqrGcMw5a6e+4vlqd4\nxPVRAQcjF9+IPIPkUHJ8HHRr/JuhigSreJhwxt7/dqm2hykGvQqew6FCcKLf\n6urxN7Drj5BBSzHMohM9bLd5RAe/L/SekFnLMyjut3ioGixlytAsVp4jUjmZ\nic2QwAjimCHC4sKN5UGgU6B6J/5Fgd+wNjNXNw5vm7Y3Wb13aq40y3Ab0nks\nqo+5u4EJ4t5zNMtLU1NpwqBmya5fEKvy5s5JKxe6R/oaiGhILFuFBU/ezcTq\nar6wReswbOPJHq6oxDA1iBgwJbG5ic/gXCkglLvFllf+W6rX8jxxEfcu2ddQ\nBb5wevrsmqGN6cgp3RAX8Dk3HNnJ6bxTOLWtg+UnR2FZ1R5OdcS6trFcCv7X\nE2g4RxsBYDN0bKT7m3yewI0zH1nwyCvgF7qkxpnO3RCYexutP/dwT+gFagMN\nDSiUwv5rvzo1vKVHLGwGvyCDT4WZtAPst5NzDbbTfreCGkiTDStoCgZG7ryk\nPH9xVAjU0ZU/AEzMgu5rWzt9ldFH1NDireWHWTajmFPIvOzp//xqvMAo7YFN\n2ucz\r\n=saOf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDKKgm1ob2mew9aiV3VuqivOOrJ00tJt2sawsK3PvQFYQIhAP7Vxochx58ry33Kw9kY+GnylOZkHMZ91+DM4gz03KjL"}]}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime_2.0.2_1589537874840_0.4862469241594056"}, "_hasShrinkwrap": false}, "2.0.3": {"name": "@types/mime", "version": "2.0.3", "description": "TypeScript definitions for mime", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/jedigo", "githubUsername": "jedigo"}, {"name": "<PERSON>", "url": "https://github.com/dhritzkiv", "githubUsername": "<PERSON>hr<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mime"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "75aed3d4a994aa68fd6cea1ee8943bc2163a4ecb83f5168cd4f956a488fce32f", "typeScriptVersion": "3.0", "_id": "@types/mime@2.0.3", "dist": {"integrity": "sha512-<PERSON>s9s4CDbqwocc5pOAnh8ShfrnMcPHuJYzVcSUU7lrh8Ni5HuIqX3oilL86p3dlTrk0LzHRCgA/GQ7uNCw6l2Q==", "shasum": "c893b73721db73699943bfc3653b1deb7faa4a3a", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-2.0.3.tgz", "fileCount": 6, "unpackedSize": 3508, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGIG1CRA9TVsSAnZWagAA/ggP/ikTiDpucXLvhORcNFhZ\nNVh42fRoqY3KoiOXCH5XRLasx7JJBXgWoQ+DGUw8E/LakaYXTF+9HEtq3d0r\nuZ5NPCqoEK6kyLjGA+x6SZpcFc7DIezDtNDLEcUnItfWKIA+rI9Qk/G71HSf\nS7BlBF0xJpS0BVIfX+VJsaAmgOPbIzrBQuwoz9Pm3H62lXk70RMMRTPM/lJ6\nInF48lL5FbVr3OqWD+sVyt9+SULSOVJieVPQrcvz6akkUTRBluBsxpWb4Fox\nTY/j0NAKW8wCcAUMIVzNvXpUbHTS/MLb3zMRnzsMb4kGXwX+FNDwkmYt3yTU\ntI581FYjmgVE891dTNDVREJL3u69TYTHFKbmgLDuWt7D1i6ACygJyRMMFINg\ngRAJiQGPSMmvnkNKLqZDiTlMjlE5V5DhuoaOxBRfNUBYYmiw6QbkFLGIauMY\n9W575eZjaYTXt4ielqBNFKniswvLLCzlIZDIIC8jfL6uQ5/Yn0TdF1oY87Fe\n+5i6URKAzSLbi4335tpZSG1u7uuY/JxouBeCPumtwvNPPGrlSNsO/3kjJN1O\nUJDxdUJkFuOnf27dgIRJn7fGYxH/hzmZqBk4LWEPi3Hgpu5KO8UJt5QDA9Jm\nY19/wa0v3VXM6IWs0WJhgYqqu4k/SP2KpF98+6jtFusPVpgekVwxbh5Rcsw8\n7zFD\r\n=RfSx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCUa0OchyTRX50gcZyMpr9+z/kya+kqyn4P4HiZ8Db2agIgOPmskWEXaj8ry0aoD/TKonDmhOU0bPM4aoCE0baeSUs="}]}, "maintainers": [{"email": "<EMAIL>", "name": "types"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime_2.0.3_1595441589264_0.7075693689262297"}, "_hasShrinkwrap": false}, "1.3.2": {"name": "@types/mime", "version": "1.3.2", "description": "TypeScript definitions for mime", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/jedigo", "githubUsername": "jedigo"}, {"name": "<PERSON>", "url": "https://github.com/dhritzkiv", "githubUsername": "<PERSON>hr<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mime"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "529a2ee85950b588c079bd053520c5b3c2bbff1886870bc08188284265324348", "typeScriptVersion": "3.4", "_id": "@types/mime@1.3.2", "dist": {"integrity": "sha512-YATxVxgRqNH6nHEIsvg6k2Boc1JHI9ZbH5iWFFv/MTkchz3b1ieGDa5T0a9RznNdI0KhVbdbWSN+KWWrQZRxTw==", "shasum": "93e25bf9ee75fe0fd80b594bc4feb0e862111b5a", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-1.3.2.tgz", "fileCount": 6, "unpackedSize": 3997, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgBZxiCRA9TVsSAnZWagAAoC4P/1woQka20K51uwE6IRqM\nABrSwrYGlbqxK9H4ctIbtsM9VqRy/hRsmh9BtcEDNtokUIXJjKLRvKEnzF37\ndwXEJHGehr0vgdd8QD++3BkyZ81gLKkTVfxcmlgYiD9E+1MaThbYVGCsBSMb\nCwP+1QZmam+nIKXgejekdLqJ7o1c11NVnyX+dzIXDFpWiP5lWZObdvdATFZP\nH+ZL9mQHJiXt8h0lbj2TlsVEnggMfUj4PBwKFAj9Grf2XEIm8CMg02RRx8qY\na/H6hPWh1TXZA84KlL0anFrGk/8Or4Fhc3YoluIMbLqA9WC6Qj9UZaBEZtSQ\nduh//aw3BghbMuJcwqkSNx0wqYdLoqgC2/vPtI8JeOZGuhWN296irhGCwjLN\nCIg70wTTBXkATXQEaxAvIugNPe0pg7chLhub/mhUKRt2swAGQz7PI11AodPr\nwxzNYBzS5CttmxIH/U27B9chf4l0b5BrcDmeqDjYnVPYNjHWmSkh4vZBKUas\nPF47cZNjjJno9iSWFdB4+dzA83VFojSFt92/l9WzalYKjUWff6rxnhBsT20B\n6DshlgzQMrn+7jsKtZwzya2ElZAz5OKdPrRVApXD9maFwwfXAvya7qeiGnyt\nspAyoQ+N1Dgar5sUgEGWHxBgy0/FsMJLzw+rzFhzXKPm8ufMcrIm9lb1eir9\n+CY3\r\n=NhJn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC0dPHkdd9KuM5SpmqOhDB51scMV1DhZEKQi6Y9nybwrQIgOaSZwEkpa0JX0PjeKEaTO7XK4CZv1blvwDLFbsF/HD4="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime_1.3.2_1610980450398_0.8547211716639562"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "@types/mime", "version": "3.0.0", "description": "TypeScript definitions for mime", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mime", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/jedigo", "githubUsername": "jedigo"}, {"name": "<PERSON>", "url": "https://github.com/dhritzkiv", "githubUsername": "<PERSON>hr<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mime"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "b8c6d5cf70eeadbb7f76b1404b924146152d68933aafb90f1f22afd6d9baaf06", "typeScriptVersion": "4.0", "_id": "@types/mime@3.0.0", "dist": {"integrity": "sha512-fccbsHKqFDXClBZTDLA43zl0+TbxyIwyzIzwwhvoJvhNjOErCdeX2xJbURimv2EbSVUGav001PaCJg4mZxMl4w==", "shasum": "e9a9903894405c6a6551f1774df4e64d9804d69c", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-3.0.0.tgz", "fileCount": 7, "unpackedSize": 3601, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHixGHvEJTJhK61AUOT8RoP80q4TgcbI3RSLBlH1uCgnAiEA57DPmpFBZFtEiyRr2XQrzHf9sqIqYNVqhriJua3kl0o="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi5FjuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoEwxAAixjfVkH1LzO7vX/Ca7yRp49uFuSZLwtJPzPW2Ry2XpZvjPgj\r\nqzgbiigjR3BSexMvD89W/uXxiZPVcC6nU3h6HuvftBtX4JyDn1zLM0U2Kso9\r\ncZay/bZRh305cafS8qm9PpPu4NL2JqQA/ZyvS9XyvLpgeix8tsAxnMVmn85Y\r\nAu1wXr/GOYti0baVm0HCWyx2FstYtm+701ZddE856t2ZcyNY0jjzSv+GE6Qd\r\nkanHqpzPdxHHt6k5L8QyLj5ysZ4VNOrvqJyk5iGjk1wbw/PVcW1wiqgpnj5K\r\nW888GY29DM1TDtcckX9aEfVixX2n8yC0LVDF7kI0EHl1VQz/9JLly9lHiH3q\r\n/gzuALepeCFllB542qAd3nO1O/sgKQuQarai0nt9ONkKDKbSOzwPuQJk15e5\r\ntQV8wfm3LAmgx5QMb/oW135o8Y84jdkZ4EuMrCG+qqQYI3TFLTf5MyYJ7ArQ\r\n3tbeU3RTmaz1JGkfyNhwFiONcjxQKCREZxGQDf3eSBEiTU3QiePZhXXpM6qq\r\nVCeWVs8aMD/JQIYj3SHi0VfYIqlewp9l5eFEgQc6eipGh7m5mobtPm4F8DdJ\r\nJHo8oYjIF4vw2lenfhvhh0DfeX6QBwixyOPZd5JKP0Cq7bmLd+xr9GD6y4GD\r\ndWIHD4EFdWXzMeJblF4uut1vxbCAV+zIkHk=\r\n=KSuU\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime_3.0.0_1659132142100_0.2731037655474513"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "@types/mime", "version": "3.0.1", "description": "TypeScript definitions for mime", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mime", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/jedigo", "githubUsername": "jedigo"}, {"name": "<PERSON>", "url": "https://github.com/dhritzkiv", "githubUsername": "<PERSON>hr<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mime"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "594e1e4777af908b69119d71c37be0a70e63c840095dcdb0881343b51b7286f3", "typeScriptVersion": "4.0", "_id": "@types/mime@3.0.1", "dist": {"integrity": "sha512-Y4XFY5VJAuw0FgAqPNd6NNoV44jbq9Bz2L7Rh/J6jLTiHBSBJa9fxqQIvkIld4GsoDOcCbvzOUAbLPsSKKg+uA==", "shasum": "5f8f2bca0a5863cb69bc0b0acd88c96cb1d4ae10", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-3.0.1.tgz", "fileCount": 7, "unpackedSize": 3567, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCLp9ijpS7z2TIVq0KZDHaY8dKyRbNlK7ya9bDMTcKEhQIgOUdMYgR1r2hxwIDpOROYijmxANBIeb3/UndTtl9VZzM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7DPjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmraVRAAnBlpXZ1Veq4R3jVhnV4gEpPh0WgUEfsFo8SD5AjM6KZKd4T2\r\nlBPDo0WPkO5yTQehN5Qcm87FWk2gPP53zUumWCdeeRYIFnPz7pNT9JbFropn\r\n0vuajUVu46Y5ZVcENxzdoaeZxPmIqSiL+6l2Kn4wGkKiSUpwYz1r34AEmbNB\r\nFQ3QOVsBiqhohuM1P+CW7X+Njygq+RN5M1/0V9309wsTeH/JE2Oy3/D+RWAP\r\npoCg/8TCefAKMDsjKQ1QtDJ87wPerSk3+ECNKTQArYGDNJTzDYjLZlIx/+FC\r\nSUesQQTEWuxSygy0s9JNJR67grCI050wUoBqTUYT04Bdon5IYwydNBpoov7w\r\nr3ru3NXNEBYvarlx/ky6pPb79wyG6DFObBZNV11tYae1TcHlXShpDnSHEWMT\r\nSqwJv56/xosFMGNtKponiGUvUFKkc7NtXPh6dw47Nwq6un0Kxm54h+kGOUz0\r\nq73hYpQvFQRrXX8E3RcBpJ47sPnLSSmzoa/L9LyFsQKVjT8uscmH0hsJdHqf\r\nAxBdoBoQDw5WPEz82tKpoEZufJfK6D53gn7bFm/Ij4MIqd6rgoyZzq4VtWqc\r\n2czxEDFGg/J71IU/xIQEPj77oH3DUySwtpcIJDen+BNNk+zBRr/R/G3jFp0M\r\nKo8mrj/dI4BBNJO5xPGB36HmdnWoCoi7ZwY=\r\n=1zHA\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime_3.0.1_1659646947067_0.6536362738155013"}, "_hasShrinkwrap": false}, "3.0.2": {"name": "@types/mime", "version": "3.0.2", "description": "TypeScript definitions for mime", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mime", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/jedigo", "githubUsername": "jedigo"}, {"name": "<PERSON>", "url": "https://github.com/dhritzkiv", "githubUsername": "<PERSON>hr<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mime"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "e8715368dd9ff39a99048df447948f99f47514bf83818f9568798c0aed291767", "typeScriptVersion": "4.5", "_id": "@types/mime@3.0.2", "dist": {"integrity": "sha512-Wj+fqpTLtTbG7c0tH47dkahefpLKEbB+xAZuLq7b4/IDHPl/n6VoXcyUQ2bypFlbSwvCr0y+bD4euTTqTJsPxQ==", "shasum": "c1ae807f13d308ee7511a5b81c74f327028e66e8", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-3.0.2.tgz", "fileCount": 7, "unpackedSize": 3567, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG/vJu7glp4pGs8O4JiSFfvMY16WMZhnPukfeLJuRU/hAiBOtYOUUmbxg2FTQntM7Y3ZIKNuiuYgOIsPVJpnP+Nagw=="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime_3.0.2_1695809409292_0.07602122264176248"}, "_hasShrinkwrap": false}, "1.3.3": {"name": "@types/mime", "version": "1.3.3", "description": "TypeScript definitions for mime", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mime", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/jedigo", "githubUsername": "jedigo"}, {"name": "<PERSON>", "url": "https://github.com/dhritzkiv", "githubUsername": "<PERSON>hr<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mime"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "c7c335f6c685d30c4b5b0ccb38a1bf168ca756cdd46d5ea83ce6d4aaddd3084d", "typeScriptVersion": "4.5", "_id": "@types/mime@1.3.3", "dist": {"integrity": "sha512-Ys+/St+2VF4+xuY6+kDIXGxbNRO0mesVg0bbxEfB97Od1Vjpjx9KD1qxs64Gcb3CWPirk9Xe+PT4YiiHQ9T+eg==", "shasum": "bbe64987e0eb05de150c305005055c7ad784a9ce", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-1.3.3.tgz", "fileCount": 7, "unpackedSize": 4094, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCBlA/jty16vtW6iSwEG/1Gaa59ks3NAG6Y6uFHBzLsMQIhAPHWp8d05GcCUYqCO2rwSNG3Yp2Q4hJcKgoCRQGO6esK"}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime_1.3.3_1695809416338_0.685473655282048"}, "_hasShrinkwrap": false}, "3.0.3": {"name": "@types/mime", "version": "3.0.3", "description": "TypeScript definitions for mime", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mime", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "jedigo", "url": "https://github.com/jedigo"}, {"name": "<PERSON>", "githubUsername": "<PERSON>hr<PERSON><PERSON><PERSON>", "url": "https://github.com/dhritzkiv"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mime"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "2c497e55268040d2962e8ee4ee52b995044e4e96b6ca71c85d5cfebeadbe481b", "typeScriptVersion": "4.5", "_id": "@types/mime@3.0.3", "dist": {"integrity": "sha512-i8MBln35l856k5iOhKk2XJ4SeAWg75mLIpZB4v6imOagKL6twsukBZGDMNhdOVk7yRFTMPpfILocMos59Q1otQ==", "shasum": "886674659ce55fe7c6c06ec5ca7c0eb276a08f91", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-3.0.3.tgz", "fileCount": 7, "unpackedSize": 3254, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCqzMTFI2XkbCkCvCTF/cNcuLR3jDBnPs4Xm98o1dJ5WwIgbaBVDnoPIerCdElzOiwrFAgoCd4Ly6CqZqQZOSUqciQ="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime_3.0.3_1697653698794_0.48725170179299204"}, "_hasShrinkwrap": false}, "1.3.4": {"name": "@types/mime", "version": "1.3.4", "description": "TypeScript definitions for mime", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mime", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "jedigo", "url": "https://github.com/jedigo"}, {"name": "<PERSON>", "githubUsername": "<PERSON>hr<PERSON><PERSON><PERSON>", "url": "https://github.com/dhritzkiv"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mime"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "f06f6e62838d0cbd4aff6460ea48697e4247ef3b461538a9ffd4600ccdffa518", "typeScriptVersion": "4.5", "_id": "@types/mime@1.3.4", "dist": {"integrity": "sha512-1Gjee59G25MrQGk8bsNvC6fxNiRgUlGn2wlhGf95a59DrprnnHk80FIMMFG9XHMdrfsuA119ht06QPDXA1Z7tw==", "shasum": "a4ed836e069491414bab92c31fdea9e557aca0d9", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-1.3.4.tgz", "fileCount": 7, "unpackedSize": 3781, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICD4WMkqOKIfXOEeBzHiv0xTZ3IeFugk+2hmob5+QSeBAiBKWEYh91qFX17y4cB6EYZ5rtIFvkpQRv8o8M5dOJCDvQ=="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime_1.3.4_1697653706237_0.9301621219830631"}, "_hasShrinkwrap": false}, "3.0.4": {"name": "@types/mime", "version": "3.0.4", "description": "TypeScript definitions for mime", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mime", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "jedigo", "url": "https://github.com/jedigo"}, {"name": "<PERSON>", "githubUsername": "<PERSON>hr<PERSON><PERSON><PERSON>", "url": "https://github.com/dhritzkiv"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mime"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "983179e5cf99a98e2effcd9a9b5e0a972eeec00711e4ac14a607c518c4b4d547", "typeScriptVersion": "4.5", "_id": "@types/mime@3.0.4", "dist": {"integrity": "sha512-iJt33IQnVRkqeqC7PzBHPTC6fDlRNRW8vjrgqtScAhrmMwe8c4Eo7+fUGTa+XdWrpEgpyKWMYmi2dIwMAYRzPw==", "shasum": "2198ac274de6017b44d941e00261d5bc6a0e0a45", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-3.0.4.tgz", "fileCount": 7, "unpackedSize": 3254, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICVp3KyUEgTjcVIzX9hlTuev/hD55m5ZE7n3HoVv+ScUAiEA6ZjTe13mPKu9vnoqin4Uq354OO9r7aaGypuQM1PyTm8="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime_3.0.4_1699388762556_0.4410676149167916"}, "_hasShrinkwrap": false}, "1.3.5": {"name": "@types/mime", "version": "1.3.5", "description": "TypeScript definitions for mime", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mime", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "jedigo", "url": "https://github.com/jedigo"}, {"name": "<PERSON>", "githubUsername": "<PERSON>hr<PERSON><PERSON><PERSON>", "url": "https://github.com/dhritzkiv"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mime"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "2ad7ee9a549e6721825e733c6a1a7e8bee0ca7ba93d9ab922c8f4558def52d77", "typeScriptVersion": "4.5", "_id": "@types/mime@1.3.5", "dist": {"integrity": "sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w==", "shasum": "1ef302e01cf7d2b5a0fa526790c9123bf1d06690", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-1.3.5.tgz", "fileCount": 7, "unpackedSize": 3781, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCaIkbDBfP9pgtoPl7ZvWb2UUhw3nSEYpqwYJXkhoWtWQIhALe5OV2+hutZPQTMNNrzam/+I4BZBmby0UmLa8jsaPW9"}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime_1.3.5_1699388774048_0.3715293589013491"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "@types/mime", "version": "4.0.0", "description": "Stub TypeScript definitions entry for mime, which provides its own types definitions", "main": "", "scripts": {}, "license": "MIT", "dependencies": {"mime": "*"}, "deprecated": "This is a stub types definition. mime provides its own type definitions, so you do not need this installed.", "_id": "@types/mime@4.0.0", "dist": {"integrity": "sha512-5eEkJZ/BLvTE3vXGKkWlyTSUVZuzj23Wj8PoyOq2lt5I3CYbiLBOPb3XmCW6QcuOibIUE6emHXHt9E/F/rCa6w==", "shasum": "b5f8a75697ac775ecf1daaea9bfb91cde065b397", "tarball": "https://registry.npmjs.org/@types/mime/-/mime-4.0.0.tgz", "fileCount": 4, "unpackedSize": 1706, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJ8ZKsKq0Jx+lV+c2B31R/9z3plLvIttZXtwMyv531AgIhAOal9payETCUBKZZ3e/y5Plve2zYwNByXNJH7DFakjOA"}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/mime_4.0.0_1711877729602_0.9770973707328015"}, "_hasShrinkwrap": false}}, "readme": "[object Object]", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "time": {"modified": "2024-03-31T09:35:29.893Z", "created": "2016-05-17T18:20:50.605Z", "0.0.15-alpha": "2016-05-17T18:20:50.605Z", "0.0.16-alpha": "2016-05-19T21:43:19.266Z", "0.0.21-alpha": "2016-05-20T20:11:06.443Z", "0.0.22-alpha": "2016-05-25T05:28:36.967Z", "0.0.23-alpha": "2016-07-01T20:09:47.995Z", "0.0.24-alpha": "2016-07-01T23:30:03.023Z", "0.0.25-alpha": "2016-07-02T02:57:51.955Z", "0.0.26-alpha": "2016-07-04T00:53:17.631Z", "0.0.27-alpha": "2016-07-08T20:51:34.929Z", "0.0.28": "2016-07-14T15:24:09.159Z", "0.0.29": "2016-09-19T17:52:00.194Z", "1.3.0": "2017-06-15T22:03:07.591Z", "1.3.1": "2017-06-20T20:48:17.467Z", "2.0.0": "2017-09-18T14:15:02.431Z", "2.0.1": "2019-02-05T22:09:53.900Z", "2.0.2": "2020-05-15T10:17:54.961Z", "2.0.3": "2020-07-22T18:13:09.404Z", "1.3.2": "2021-01-18T14:34:10.529Z", "3.0.0": "2022-07-29T22:02:22.254Z", "3.0.1": "2022-08-04T21:02:27.370Z", "3.0.2": "2023-09-27T10:10:09.506Z", "1.3.3": "2023-09-27T10:10:16.563Z", "3.0.3": "2023-10-18T18:28:19.181Z", "1.3.4": "2023-10-18T18:28:26.540Z", "3.0.4": "2023-11-07T20:26:02.711Z", "1.3.5": "2023-11-07T20:26:14.364Z", "4.0.0": "2024-03-31T09:35:29.749Z"}, "license": "MIT", "readmeFilename": "", "users": {"kontrax": true, "flumpus-dev": true}}