{"_id": "utf-8-validate", "_rev": "47-b839f352a7810d46b7e7960c35b1e9b4", "name": "utf-8-validate", "dist-tags": {"n-api": "4.0.0-napi", "latest": "6.0.5"}, "versions": {"1.0.0": {"name": "utf-8-validate", "version": "1.0.0", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@1.0.0", "maintainers": [{"name": "V1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "a8b5cbb4b14d350a8cbae35c1d15b658d44ec86e", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-1.0.0.tgz", "integrity": "sha512-/TZDwvPz2mlv2VFRoPIhD6JMXPGTiSzsoFjcmaTC9dSsFWWnuWGTsmNdzGiqcs16UThW5ivpX8ciLvOAesx5YQ==", "signatures": [{"sig": "MEUCIH34ZTMGKPqajJ0exMV/dNoQC6diFmEfbOJh8YXuLRliAiEA0J7CMBcxj9+JYvpmtODtWmXt+KJxNUY1AzCR9d0BtLM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "a8b5cbb4b14d350a8cbae35c1d15b658d44ec86e", "gitHead": "e1619c12bee0a4d7cd3197536d7a3a0f211eb4f7", "gypfile": true, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "install": "node-gyp rebuild"}, "_npmUser": {"name": "V1", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/websockets/utf-8-validate", "type": "git"}, "_npmVersion": "2.3.0", "description": "Validate UTF-8 for Web", "directories": {}, "_nodeVersion": "0.10.35", "dependencies": {"nan": "1.6.x", "bindings": "1.2.x"}}, "1.0.1": {"name": "utf-8-validate", "version": "1.0.1", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "d15eb67e28f6bb93c9401eeb7eac7030a183e8d1", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-1.0.1.tgz", "integrity": "sha512-4x5/7xlBqCXvLdfny27SS7iVf7lg7Qc5YLc2dlU5+jaHzjtFXmTIj8R2HWP3beVKzCkaddzK77F4gQJyRmljMw==", "signatures": [{"sig": "MEQCIDCh6FWxSyJkEsn7r4A/A1Vxq9zp/I8A0r8Y54tdME57AiAFjYlx6+frcUK66ScuVQYzD1iwoTSo+Op9PO7LVmefPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "d15eb67e28f6bb93c9401eeb7eac7030a183e8d1", "gitHead": "1c0a74c3f2a8bb9e5b14df235404faa3760abce3", "gypfile": true, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "install": "node-gyp rebuild"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/websockets/utf-8-validate", "type": "git"}, "_npmVersion": "2.3.0", "description": "Validate UTF-8 for Web", "directories": {}, "_nodeVersion": "0.10.35", "dependencies": {"nan": "1.6.x", "bindings": "1.2.x"}}, "1.1.0": {"name": "utf-8-validate", "version": "1.1.0", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "521a6bb2189d0b307ddc5b79c3c95a5fd8085db4", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-1.1.0.tgz", "integrity": "sha512-Qsgu1u2akdyOneurUEVf/tXhkqBQMoE3x0GY5+P7ayPHvVzTqOkkgBhtl26wm6ap10Amhwy0jIhPwMGywx76QQ==", "signatures": [{"sig": "MEUCIFLFDr7a+3R7l9dye7Ku8vm0niqS4qZbJpOLw6NzF32QAiEAzsxEkhsMLlD80D/eU/TXgnl+qnFWMLddfhzdx9YaAsw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "521a6bb2189d0b307ddc5b79c3c95a5fd8085db4", "gitHead": "873544269aa2840df261872c650d89768b4cceab", "gypfile": true, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "install": "node-gyp rebuild"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/websockets/utf-8-validate", "type": "git"}, "_npmVersion": "2.7.5", "description": "Validate UTF-8 for Web", "directories": {}, "_nodeVersion": "0.10.35", "dependencies": {"nan": "1.8.x", "bindings": "1.2.x"}}, "1.2.0": {"name": "utf-8-validate", "version": "1.2.0", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@1.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "7d41d57e60e1b36c01e79b7ac41c00ad1bf933fd", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-1.2.0.tgz", "integrity": "sha512-rslrg/VwP1JJDtfGj/zHP5RlZoo1PbTP8eM++tCQOUd2Osiw5ILTu/TIQgLLGBptIDmoBXt3mBSubd5wdhiSFQ==", "signatures": [{"sig": "MEQCIA50gwBM9cjJ679Do0F6RvynxN099O5giBla3/G+zI2QAiA8BIA9ztPsDc/oWBq5hQ6DyrfUCoRoWbMC/u/0QhmEug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "7d41d57e60e1b36c01e79b7ac41c00ad1bf933fd", "gitHead": "1b6018403eaebf2f4869208dbcd302f3c20944e9", "gypfile": true, "scripts": {"test": "echo \"Only testing builds, test have to be extraced from `ws`\" && exit 0", "install": "node-gyp rebuild"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "2.13.3", "description": "Validate UTF-8 for Web", "directories": {}, "_nodeVersion": "3.0.0", "dependencies": {"nan": "^2.0.5", "bindings": "1.2.x"}}, "1.2.1": {"name": "utf-8-validate", "version": "1.2.1", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@1.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "44cb7c6eead73d6b40448f71f745904357b9f72c", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-1.2.1.tgz", "integrity": "sha512-wI+UhNgPPaXKP8kF3owsF2HiVjWy+UdJDSHEh48fPu8nInfx2t1FOSqbqAh6/uXwm7nDe3uydYy+KxTHS8wR/w==", "signatures": [{"sig": "MEUCIF0+IY+5T3qUcykaoKHdVmrWeo2twKlnW8cnt3nlbwq/AiEAkJQNk7k3ycIFot5GGM1Jd9UT1lJjNVNEUHA/KNY698s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "44cb7c6eead73d6b40448f71f745904357b9f72c", "gitHead": "8067ecff68899b9a1bb31d6906c80e1d5e88bcc7", "gypfile": true, "scripts": {"test": "echo \"Only testing builds, test have to be extraced from `ws`\" && exit 0", "install": "node-gyp rebuild"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "2.9.1", "description": "Validate UTF-8 for Web", "directories": {}, "_nodeVersion": "0.12.3", "dependencies": {"nan": "^2.0.5", "bindings": "1.2.x"}}, "1.2.2": {"name": "utf-8-validate", "version": "1.2.2", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@1.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "8bb871a4741e085c70487ca7acdbd7d6d36029eb", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-1.2.2.tgz", "integrity": "sha512-CcV1z1L/e1wFAZwl8T6o1MmxIsg/ClZ4nmUolyIhb3ZJKbD/ZQTZXstCf6BiRcvaThSJVI8SqWLodWq/hnWDxQ==", "signatures": [{"sig": "MEQCIGASyCFEpHCW2cx8RtwFrfK5K+AC37KVOAjB1Fd+v5l8AiB+QM3NmEqLVVUJM2bNYR3gnREvanYy3Bh5LYw8QwxteQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "8bb871a4741e085c70487ca7acdbd7d6d36029eb", "gitHead": "c06b8e7fb1c310bd5e490072bac3df4a8414bd6c", "gypfile": true, "scripts": {"test": "mocha test/*.test.js", "install": "node-gyp rebuild", "coverage": "istanbul cover _mocha --report html -- test/*.test.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Validate UTF-8 for Web", "directories": {}, "_nodeVersion": "7.2.1", "dependencies": {"nan": "~2.4.0", "bindings": "~1.2.1"}, "devDependencies": {"mocha": "~3.2.0", "istanbul": "~0.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate-1.2.2.tgz_1481128509915_0.5595374992117286", "host": "packages-18-east.internal.npmjs.com"}}, "2.0.0": {"name": "utf-8-validate", "version": "2.0.0", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "38f5636d78c2e4cc20e132470cc4d06b92192e44", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-2.0.0.tgz", "integrity": "sha512-5rBawdrP1+SRz9nRRYqQqNxopDWkF/m04rSS+oR9HEkGIiR1xX5deuu30V3goBJG/DtfYPUtqHwv9NIbR67aDw==", "signatures": [{"sig": "MEYCIQCCPmwT6GnGoXSfaHyr+9X2XJt4JhkbSLnQYySGCuqFNQIhAPRCiF7DZhj3ZrywMo1ALiC0yqTc3WITu8oZ4/42iWbV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "38f5636d78c2e4cc20e132470cc4d06b92192e44", "gitHead": "c5e4bd5ce9e20cda3c9f410ba10b42e544d674e7", "gypfile": true, "scripts": {"test": "mocha test/*.test.js", "install": "node-gyp rebuild", "coverage": "istanbul cover _mocha --report html -- test/*.test.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Validate UTF-8 for Web", "directories": {}, "_nodeVersion": "7.2.1", "dependencies": {"nan": "~2.4.0", "bindings": "~1.2.1"}, "devDependencies": {"mocha": "~3.2.0", "istanbul": "~0.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate-2.0.0.tgz_1481210394453_0.810942031443119", "host": "packages-18-east.internal.npmjs.com"}}, "3.0.0": {"name": "utf-8-validate", "version": "3.0.0", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@3.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "42e54dfbc7cdfbd1d3bbf0a2f5000b4c6aeaa0c9", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-3.0.0.tgz", "integrity": "sha512-H4+1nack9PmqYL8LknEUNg3QJgzq8mmv5qJrPiylmoXNWlN/yEKWcEB+P/Z+VNpxO4rybbl2IHBm33+PrexYiA==", "signatures": [{"sig": "MEQCIGmctYmqOWxyRKWuatmKtqQCLb3+lmuUs0TDjSzk24TwAiBtjnK9s5454Pb/Z15/S4qhuJSpdoeSRgkhkPS3+aVT1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "42e54dfbc7cdfbd1d3bbf0a2f5000b4c6aeaa0c9", "gitHead": "393fee752a680b34b2731d66a0bd6c3c5370f480", "gypfile": true, "scripts": {"test": "mocha test/*.test.js", "install": "node-gyp rebuild", "coverage": "istanbul cover _mocha --report html -- test/*.test.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "7.5.0", "dependencies": {"nan": "~2.5.0", "bindings": "~1.2.1"}, "devDependencies": {"mocha": "~3.2.0", "istanbul": "~0.4.5"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate-3.0.0.tgz_1486144335884_0.9701544980052859", "host": "packages-18-east.internal.npmjs.com"}}, "3.0.1": {"name": "utf-8-validate", "version": "3.0.1", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@3.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "5d2b8656b4ddcfded47217b647a98941b63cf213", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-3.0.1.tgz", "integrity": "sha512-5CtGcZePJCN+QJ46frtxB+dal1P4xw9q04gt3jCyvfCS1OWVzAwLvv5i+lmbpgodBAYRyeTeHEQ0ijFIRPxBbg==", "signatures": [{"sig": "MEQCIEy1XjvFffXfkmOsF5qcSQaCQpa/KKLzAEKaU0/1x1F3AiB+WSdrCBKrGopyVYBq+Wh1PF+aMK2uF0AOQJwjfPpd5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "5d2b8656b4ddcfded47217b647a98941b63cf213", "gitHead": "86a6688afabbe76d3c0019696536f77142c6bdbb", "scripts": {"test": "mocha test/*.test.js && prebuild-ci", "install": "prebuild-install || node-gyp rebuild", "coverage": "istanbul cover _mocha --report html -- test/*.test.js"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "7.5.0", "dependencies": {"nan": "~2.5.0", "bindings": "~1.2.1", "prebuild-install": "~2.1.0"}, "devDependencies": {"mocha": "~3.2.0", "istanbul": "~0.4.5", "prebuild": "~6.0.2", "prebuild-ci": "~2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate-3.0.1.tgz_1486389066577_0.938203826546669", "host": "packages-12-west.internal.npmjs.com"}}, "3.0.2": {"name": "utf-8-validate", "version": "3.0.2", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@3.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "227a15261743e6d08872b8b660a9d09e52db4daf", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-3.0.2.tgz", "integrity": "sha512-7bQSIlcdKNLGnXTnu8b7EZ3p5x1LJcDZwsjxI6P5+wEfM/UHnpEZ41ltn87KIaLSlQk6QJGacILkYkmUHBL6uA==", "signatures": [{"sig": "MEQCIFtS7+YKm7nn/yaqwNNSW1Ne0lGVmb3qDfEAaa2g4F5RAiBG+F8YiEJNohALsPLVtjv3+FzOZvWaHC2tHQA7aCtYmw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "227a15261743e6d08872b8b660a9d09e52db4daf", "gitHead": "2babb5d5d49c9b1569be1722730ccd9eaa1c2dd3", "scripts": {"test": "mocha && prebuild-ci", "install": "prebuild-install || node-gyp rebuild", "coverage": "istanbul cover _mocha --report html"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"nan": "~2.6.0", "bindings": "~1.2.1", "prebuild-install": "~2.1.0"}, "devDependencies": {"mocha": "~3.4.1", "istanbul": "~0.4.5", "prebuild": "~6.1.0", "prebuild-ci": "~2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate-3.0.2.tgz_1496218180739_0.03906935919076204", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "utf-8-validate", "version": "3.0.3", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@3.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "5c053cd92c50cea73c155c965a51805f674e7794", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-3.0.3.tgz", "integrity": "sha512-uwD6vBjyGvvAN6v0rRnhxzKcUhOVASqdu+y79l7E6sDzE5bhwo8+Cc5t7sU8grDWWDOUGv0Uw8oWCchD+FtZ9A==", "signatures": [{"sig": "MEYCIQCOGB9zDH7XAL22sG5x1DSRLKRXO46YQVedr1rDEln2fQIhAL8XX6dSBYOgB9l13v7+q3MkoenN5ikqOxEr06aOjinP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "eb889a9d74c40c876a57249b8eb5f032e9dbdd01", "scripts": {"test": "mocha && prebuild-ci", "install": "prebuild-install || node-gyp rebuild", "coverage": "istanbul cover _mocha --report html"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "8.1.3", "dependencies": {"nan": "~2.6.0", "bindings": "~1.2.1", "prebuild-install": "~2.2.0"}, "devDependencies": {"mocha": "~3.4.1", "istanbul": "~0.4.5", "prebuild": "~6.2.0", "prebuild-ci": "~2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate-3.0.3.tgz_1499423463119_0.7190883683506399", "host": "s3://npm-registry-packages"}}, "3.0.4": {"name": "utf-8-validate", "version": "3.0.4", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@3.0.4", "maintainers": [{"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "a79962cfa4142dda7050234e7e9b37b33907d2af", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-3.0.4.tgz", "integrity": "sha512-L/YpTFQFeE013nD++TQYFwbDdGww9pL1d5j8gena2VN3qUcyIIsSS2POsiTRub0MTuOWbd6+kHFRF6FOlcfkvw==", "signatures": [{"sig": "MEUCIGLTJ1B2xd3DqEl0VIXBGKiSM+0qCie+aTiOTKKntnQRAiEA4uiyL2ATpapdr/BJ241HlDVVdFbbVaksYvlK/9rz/ng=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "493c2f03ed6072fd7d3257caf99d1cf7c49dee7e", "scripts": {"test": "mocha && prebuild-ci", "install": "prebuild-install || node-gyp rebuild", "coverage": "istanbul cover _mocha --report html"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "8.9.0", "dependencies": {"nan": "~2.7.0", "bindings": "~1.3.0", "prebuild-install": "~2.3.0"}, "devDependencies": {"mocha": "~4.0.0", "istanbul": "~0.4.5", "prebuild": "~6.2.0", "prebuild-ci": "~2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate-3.0.4.tgz_1509697622011_0.7822523561771959", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "utf-8-validate", "version": "4.0.0", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@4.0.0", "maintainers": [{"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "93812f447b6fd11a3dad4302d5870830cae8470a", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-4.0.0.tgz", "integrity": "sha512-JS/c6nR/qauqSdvTksgDO1142kYddTXz42y5X/he188B/kgcFLLB4l9CfZd+hGic/ORgsL+pPfwr9lYsL/80Fw==", "signatures": [{"sig": "MEQCIHpRZXhe/KNGqzR0R9s5+yUxCHVzpT6a5KbbJxWF4WSzAiAEAl35wqGojTNW92H4A6lLMMlnAxW1mGKJSt3R8ZB+IA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "ac99ea56a92168168ee6d87f06b5b1334510ac35", "scripts": {"test": "mocha && prebuild-ci", "install": "prebuild-install || node-gyp rebuild", "coverage": "istanbul cover _mocha --report html"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"nan": "~2.8.0", "bindings": "~1.3.0", "prebuild-install": "~2.3.0"}, "devDependencies": {"mocha": "~4.0.0", "istanbul": "~0.4.5", "prebuild": "~6.2.0", "prebuild-ci": "~2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate-4.0.0.tgz_1512301456936_0.759905205341056", "host": "s3://npm-registry-packages"}}, "4.0.0-napi": {"name": "utf-8-validate", "version": "4.0.0-napi", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@4.0.0-napi", "maintainers": [{"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "77fe9f547e684c81f38a761134232be70e86aa36", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-4.0.0-napi.tgz", "integrity": "sha512-krc3gh7LcspQEHprg8ls0vZVerxxA+Oyvfj1NE7dhNLtbK3ByGenAWCy9lK30Dt1MmvySgYMkyahYUquwkwYjw==", "signatures": [{"sig": "MEQCIFuYbkSLcOkBV2DEuU2NYMQv5YjOUPRWl0MyM12GzLSFAiB3nLakbp3PldrQzBwRzCrR1gsGOawXu6uaLvc8lBbiNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "42329dc53a025be88cdeca248611391ef3309e4e", "scripts": {"test": "mocha && prebuild-ci", "install": "prebuild-install || node-gyp rebuild", "coverage": "istanbul cover _mocha --report html"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"bindings": "~1.3.0", "node-addon-api": "~1.1.0", "prebuild-install": "~2.3.0"}, "devDependencies": {"mocha": "~4.0.0", "istanbul": "~0.4.5", "prebuild": "~6.2.0", "prebuild-ci": "~2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate-4.0.0-napi.tgz_1512395739841_0.10674984939396381", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "utf-8-validate", "version": "4.0.1", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@4.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "ec12589a42bbf0d77709baf5c082c610bd5b5fa6", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-4.0.1.tgz", "fileCount": 7, "integrity": "sha512-d+PRK1C8xTqJn+TVdMX2UQ2aY2Q8jtC8wp6eLIsdV5qbx5N7igR6kZg4uLWvwJINJSx2ahF2/OjVpTdC4QpZdw==", "signatures": [{"sig": "MEUCIBDqqMe778nH64h1vXTm3fo1skl49vmM7RWNXAgqaGYMAiEAkaq9/w4y1sRawWqhVzAGKnbkdDrkoP4+kaOPkORYTR0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7433}, "main": "index.js", "gitHead": "e7e007c32f50698a2c5594c7a415744c259ad2b9", "scripts": {"test": "mocha && prebuild-ci", "install": "prebuild-install || node-gyp rebuild", "coverage": "istanbul cover _mocha --report html"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "9.11.1", "dependencies": {"nan": "~2.10.0", "bindings": "~1.3.0", "prebuild-install": "~2.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "~5.0.0", "istanbul": "~0.4.5", "prebuild": "~7.4.0", "prebuild-ci": "~2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate_4.0.1_1523016439084_0.13210260330351553", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "utf-8-validate", "version": "4.0.2", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@4.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "deadcedfbf5ec535e3c72874e9ddc0663c139691", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-4.0.2.tgz", "fileCount": 7, "integrity": "sha512-CS63Ssp6zynBQ4IxVzgjP5Abdo6LuJ87HFIcgIiVUeY96+MTHkqKtrUhphbwQ6jX8aSGZv8zX6l1DCPpfcAnxA==", "signatures": [{"sig": "MEUCIQClZpcmbokvZ9LeTj591u+zZD8b0iPXbIqkfbeDc/GsnAIgWEaSfKivtCgKGFsrN2AnTsxYevYYS/+5VSMTh8JYd1U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7peeCRA9TVsSAnZWagAAhXoP/1EkCGiGMDsF55IlKfFS\n8k9SVaW18FCRPhYFdyJ4G8qI4Th3uPiiKhsU73uZu+lL/LXyzubNZsoMzLfP\n5LzCLbEre5ODhAR+e02TDmJaIMhq5+dp4RmYr8P+nC1OGb0pD8a1PTs7X8jM\n5K4TLG3FXsLW5SqmtT7BOGmCIey7VG3gcwVQUyMoGuA7v5mp1Mcw9u+J1Hq1\nVBuGRsUJjq7JrOcDdGsG7SGiNtI/UX9+2jEhlXKc+oluR0w2yjkriICuEWvx\nS2oRl5br6HklUyeYSSjEQLh3WoYpuJbfOJmGtzKwtEVAAtlhnoE5/s327JJS\ncgPMfTHq3tXFeDc8zgDsXqVfeW66wanqlVDXA1+cXIfrBSVaanm+6h30ZRVp\nh7suF11nrZx1mGM9/JM6kyLxrY0C2tV1GYgpc+P5VKeQRb2j75wPwdPY+n2e\nPq8pX0zS5FZNdc7AQjrqsulcDRsk/XEgSG5hj4IX88UvzGLlbDC7CSCGGB0F\nucjLaMMs3v1QrjqOJYSAnRajuQe+4qek4+YqkoJy78Hef1SvNUd/VVyIAbmi\ni3RQBL6zwz5s1iae3TKKVYz45SgrXBxSWNszx23EF/6V3Bm2v+z14oTasjol\nHMPfIGhDa0e8nK0PqtQW+z8rRC3WJ1RHb7hfJiszDoF2ZQvR1yLlBQaSn3Qw\nLYOU\r\n=7/Wa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "c55597393aff0c5c8cee23b7477713c99c805661", "scripts": {"test": "mocha && prebuild-ci", "install": "prebuild-install || node-gyp rebuild", "coverage": "istanbul cover _mocha --report html"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"nan": "~2.10.0", "bindings": "~1.3.0", "prebuild-install": "~4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "~5.1.0", "istanbul": "~0.4.5", "prebuild": "~7.6.0", "prebuild-ci": "~2.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate_4.0.2_1525585821201_0.1814272586779042", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "utf-8-validate", "version": "5.0.0", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@5.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "31fc8cc465a7564db2f4c8b0077beeb9b804714e", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-5.0.0.tgz", "fileCount": 20, "integrity": "sha512-lpryeKubip/ZgOaWCZQaEJ5hrckhwrdXP+E7LvQEzBml1LXQkLhgUnCMo87ltznuHCr/glTj20FI48/6m4Gehw==", "signatures": [{"sig": "MEQCIAtjuALlbPrKVCpp4bzHsn+8gbQpiT6PvDqXC2y3G0++AiA/epzxelvLfEHO0W39IX0H54PVhWPSkqhe70jjtuwaCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 307480, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbPcQvCRA9TVsSAnZWagAAW+gP/2AHe9ToPYwWqC2Nf6ap\nnH7IJ+9H3Pk4GUad8upAyC3/wc+s6fOzoywZIkzsO8fI1jU36sV9mE97E5ZE\nmVs/P3i5g9sS39nOhtQzf/Pszg984nEXUko5TqrwdSTkYAkK8QYunDPQUNK4\nHDlsS15xNtzd7R6MvDCCwQIqslwM0yp1CQwG7ulFIbn0YPzNkK2lrRzwzCCk\nglalH+vZ1CHfpjGmIUfsHPFgM3X2S0cqBNGXk6FFwQ0b7JfdZc6XhT2BUblf\n3yhNfjVxfQfhkmZCyK5b89TqWqv8ZWJarTao/gY+YrV4IiXo1FuIfmD3W8C6\n2VuscXZRwU/1laBQRg7I6zxtd24nPK3mjbSX1j3hvxExoGbUZFqhQLC8LpMd\nCJ0g6qhU+/OcJnp6xGeF7/H6+atecQtJPITlEpmmbGaDTv2fvv/RDRyfxFf0\nKa+qfo+L1xWP5MsLlcng4kQupAHt4BcxnItiXeCGGX5BodA7TDk+00AUJkfq\njl/Iy75ob27NiGzKvvDmmv5NiXETx258ONf+w5AYh8izq2HrVHL+aI719SIY\nivHvr6etGIIaaGSEmZClYLKz+/LF6xxcMaZOZEeZpv6eBdPXwPX3cQtJwK/m\nLZURo70jLSxL4fOWj7bbthR+8rAolRC6mIG+e7sqHib7M2DBT/qSQsvl2mIC\nLBVX\r\n=ppNZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "3d88f3eba3c749f615d911ffcce82939839c1d4a", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "10.6.0", "dependencies": {"node-gyp-build": "~3.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "~5.2.0", "prebuildify": "~2.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate_5.0.0_1530774575292_0.7196381786171346", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "utf-8-validate", "version": "5.0.1", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@5.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "cef1f9011ba4b216f4d7c6ddf5189d750599ff8b", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-5.0.1.tgz", "fileCount": 11, "integrity": "sha512-Qef1AuiWWxQeZ1Oa4DTV3ArRafpZvsK+CLrlB8khLfsV+9mwhj58hNSGmel0ns5jYP+3yEwav6vxxW7Gz85bVw==", "signatures": [{"sig": "MEUCIFYAGiSyjhBYtM8NjXEYiU/+5AfiMD+C/BDukYmC7yJ4AiEAi5q7PiSKWXgRZcJYI8o8+Ykv5MK7oJxmvsa/7ZZNh7E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 258270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbPcuqCRA9TVsSAnZWagAARCwP/A8u1cI13Z74pTVCwqPw\ni5xGCzrSwuiZE0aspdTIy9eM99AAudQ2vhZIomKIhJfr1rUqnWS41KbdaxU9\nygK275DiEhWvN6uypPbLkOXbeeDgTn/MsLtejR5FC3nN7mBdfTD2tMioSvKY\noOU7LaVb+DRCJAJ+rshIC+JsCzEW9z07BB/iZDRdvNRlCxIFk1kZIeE+51Er\n+ebkj9daEgT3tAOBrqVfzh7JNuirLLAeb0oif9puwxmoSRJfT+pxHbyZDYR0\nCnaEV3yDS+XEeSeEQNP3uLle12NgSo2O1rnriK3p1fwC8nUBm4n9NtX7E/WH\n3McCkKoPyJTgBAqBZ5O+aYs4vSF8aHg2GxzeFKqwnPrtZVqq30pSeHC0SKcz\neIxk94WCDOn4271LI5dD0+YTCdAYo3frNRD2pN7EDLzmGkljhKT88FmnBeS1\nSqwo5e5amZtUmM0EgChSJUhB33v2PC23GZj+vncDm0whrYPjX9DaIV+EBbMy\ntS4o3zRYCGsujR805fH4wY/dnm1ZeOeN4CFTpQdngfrtFzqXR+dCFCUEkUHt\n/Qtch339+FGiJvZnapl8hRYx/+f66mf485+/QoBUDY2DrK+nZL+/7HDeK2DY\ntNBIWFq7IVOg4MOIV4nal9NMRYHCpqMmJMfYz0UPjeTTl016RR9Tw3cR3ifk\neDyW\r\n=v5g6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "2d4da6cad127630664f383c1b404495d67b61cc1", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "10.6.0", "dependencies": {"node-gyp-build": "~3.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "~5.2.0", "prebuildify": "~2.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate_5.0.1_1530776490635_0.9739359934327403", "host": "s3://npm-registry-packages"}}, "5.0.2": {"name": "utf-8-validate", "version": "5.0.2", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@5.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "v1", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "63cfbccd85dc1f2b66cf7a1d0eebc08ed056bfb3", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-5.0.2.tgz", "fileCount": 15, "integrity": "sha512-SwV++i2gTD5qh2XqaPzBnNX88N6HdyhQrNNRykvcS0QKvItV9u3vPEJr+X5Hhfb1JC0r0e1alL0iB09rY8+nmw==", "signatures": [{"sig": "MEUCIEqoZQZOCAf6ZHunufG3BPvSD+ngVjDWkAAqRJyGRGLXAiEA5k7DkyafYnIdBITOVZzxvEdUZKoNb94QfIECIWku/Zw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 509350, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcH0YjCRA9TVsSAnZWagAA6wQP/0Mh0L2SC/GmnWc8cMJX\npzmpYIiMpX0I/UkfJJMDZOyk1mnsXRlTAC/W5PmZNyGfnQmT9ctNfl82GnYC\nv07DnoflCtdB8005FFgrVsSkk22NfQn49ld5wKvmSUymKh8+7/qw0yZuE4hA\niV6RLrC5oV1ZDcgnMIT2qe6hktslWYt65X6Ea0N8TrAjho9Fw5tgJFg+MLZB\nd8d70ju5MKOHSrUV8livsHXhdy3GvrOByvxdvOxqXDh7kc6QTN1l/4pOC1Zr\nS5OU41SqalPo/LFmPw+JFwQI4T0cTgBuZ/feUfk2TPIlmaN5+znrSXChFRCs\nUrBSXUcdtJhNJPxFohzDf+n6FP45rvJqN16uHr4rzYBgR1Dj60N7oVm+xYIi\n6Bd9L7Tgpv6ZWIdMCROkCdUFCz/V78No7z6/zexXEX3xllZECQQwwNZsLGy2\nIbnBFhd+jJ69MqtW6VpAzTCfoRpDR1CXiA0pBPL10TSbpGE3DGj4B/iS3qzA\nTtmLp2zuLz7/MZIe0eu06H/pUXnDf/TxcQcU6tG9ZCf2cJWpkbUq68NhSxCR\nPqoohh7vozErPm8eXqz5uHlZNrIJhwy76CKkGEjn65dbADZv42splHDtVkek\nmpRvmD5qbqSTb39g60ranSLgRlGwN0nBmnTH5oLi7FnPoHPVlR0fj2Pc7aXN\nunt6\r\n=uPp5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "510cc71bb8c0aa81357c216d8fe457877671e776", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "11.5.0", "dependencies": {"node-gyp-build": "~3.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "~5.2.0", "prebuildify": "~2.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate_5.0.2_1545553442607_0.23174118730274285", "host": "s3://npm-registry-packages"}}, "5.0.3": {"name": "utf-8-validate", "version": "5.0.3", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@5.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "3b64e418ad2ff829809025fdfef595eab2f03a27", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-5.0.3.tgz", "fileCount": 11, "integrity": "sha512-jtJM6fpGv8C1SoH4PtG22pGto6x+Y8uPprW0tw3//gGFhDDTiuksgradgFN6yRayDP4SyZZa6ZMGHLIa17+M8A==", "signatures": [{"sig": "MEYCIQDNat/aJb5etPcSgZRWimTnM70JrkimsAVk1by6/vpd9gIhAInHQhRllT3EGUmjFfkoBtkQ74JpsX/V5OnBqftYudYk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 921513, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfnXc3CRA9TVsSAnZWagAAQaYP/AntVWD37GgeHOq16usF\ndTubgwLNkS7LtLasSr9wNNDjDtXz1gVgg60qaKa0/P9bNmOEAkNpnT6mYlhO\nY6/ALcyiURHqr4hyIkwzumW/iIKD28Kq2I+CHXXQZLAvImB2ohU//XYU3lBe\nbslCL9KVroxvX0jX6NpgXsRpWJ+T5PiSiHSwUVOK91zMIUGxLheK3XHGYdtL\npGuIFZa4wPRBdC9IqGAe1sQyIhYpSINFBI5cvlm3TXqCTokofuE/8hxZ3lWx\nBzrLlaOnDb2MfxuaEcIMaoIb8ghp/WJu0eh1CtR8mzYIbHROnV5xfTMMoQiX\nJagwl8nEoZBr4xRqav9mT8aXXQEpnZMiPL1DVws+TOw4xwBEvMIfDfZWTKnO\nFvpkmUoWdj1EufD+/THBU+HPdEONA+qBi3MIRz+urnsAtYOR2qHRN1dMP+AC\nZUqjrxlpDsmv7RDiKawBr0824KHrRw/muRv0WnbFI340SN068hy4sT6lv2f2\nP70lAGiDj9UBV35CmD/NS5WIJenlWCwVvTiPEV3e9Y3BljDfbWYV7EUqpxh+\nVHIr+LaRb9tkB9tJbo+ilJqOEeiBuku5pa8+l5HaWPGDqMBucHyAp/7q0TZC\nFe+UeZBik0c36oUSXV5OpVE2gzApxUT7vPaLfbnsWAmvoox35QIKtSAw7xrl\nQ/tg\r\n=myC5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "de8c3eb15ca904fe38e6d0cb0cefcec84c3640fd", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "7.0.3", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "15.0.1", "dependencies": {"node-gyp-build": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^8.0.1", "prebuildify": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate_5.0.3_1604155191469_0.3156553142769416", "host": "s3://npm-registry-packages"}}, "5.0.4": {"name": "utf-8-validate", "version": "5.0.4", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@5.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "72a1735983ddf7a05a43a9c6b67c5ce1c910f9b8", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-5.0.4.tgz", "fileCount": 14, "integrity": "sha512-MEF05cPSq3AwJ2C7B7sHAA6i53vONoZbMGX8My5auEVm6W+dJ2Jd/TZPyGJ5CH42V2XtbI5FD28HeHeqlPzZ3Q==", "signatures": [{"sig": "MEUCIAMiowksL+/ek8Y4Ee+h6IbNnogbLwVXRcwEXfvz+gmpAiEApBKM1hbXF2M6pxIw8QXABVw86lLAEVD1qC7ly9folZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275898, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf8hlCCRA9TVsSAnZWagAABSQP/1134KxSZTBg3u3GNT4f\nl0YpA9rXyVGUGIGptQPyH2VN95cbULWl42hgMSEXidXa0L/J0SysPGDXkKft\nFRMptAlD/abh+6+fz/lkpIypmgcER7BSLA0qrhalLpMLHYXhJDJm6k+u1mMJ\n7BRH/pIK0AQ5K24KaPUR96GrA1rohVpmrJIyOfy9YiEVfNco6elK/JpM9/bk\nwS6x7zmyteNrAvvqufN4GH39WxAXj01ey+mRMgQUT/gFTfx3xcHtmRVJa7OM\nCjke5t7enySboINDBwms9LDh3+t68iGyrq7xwdkbqbynmO0CcWKmO+HoLGWJ\np9wMnbsh7AVWxy6xyrpbFgH1WHgIvY8iRR4x5pEC49/wG8kMnwX56S/IGJA3\n9a5iXm0iUFiWNcsTzDakm8DIAfVhk5vaxPcbRsx38kGBFuiySJYOspjujpI+\nSU5gXoxa97pi+ho/NbMBAWDBF0l6NzgU207l7A9kmWFkSzO8dcOkn6wFiqSr\nHU/iBuAR+twMvyPHbHTRZ31uGL9jTj76XJOpl9chOBM9hhON8EI4KyRYd2s+\n3VjTjp+SUNMLR20LLdeMxQ3Whc/tE25Z9SlHcNicVKyVjNOqeK/lmP00vwnw\nzNdOhm7S8FnMxaH7NgPiDju1O8AHAifpjvDISIM8DhlfCOv/2TnXhprTdLQ0\nu5sF\r\n=AHA4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "0c831ee16694ac5247086b31d1eb6e29998ada8e", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi --strip", "prebuild-linux-arm": "prebuildify-cross -i linux-armv6 -i linux-armv7 --napi --strip"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "7.3.0", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "15.5.0", "dependencies": {"node-gyp-build": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^8.0.1", "node-gyp": "^7.1.2", "prebuildify": "^4.0.0", "prebuildify-cross": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate_5.0.4_1609701698279_0.29740745418847014", "host": "s3://npm-registry-packages"}}, "5.0.5": {"name": "utf-8-validate", "version": "5.0.5", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@5.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "dd32c2e82c72002dc9f02eb67ba6761f43456ca1", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-5.0.5.tgz", "fileCount": 14, "integrity": "sha512-+pnxRYsS/axEpkrrEpzYfNZGXp0IjC/9RIxwM5gntY4Koi8SHmUGSfxfWqxZdRxrtaoVstuOzUp/rbs3JSPELQ==", "signatures": [{"sig": "MEUCIQChA+Ibo5PLF8PZofKrywWPwoMWD4bj0xs6ET/p+lcbQQIgZnb+s2bS/HmHp+c3/eYUwPmF/wD3GysSfymDBMIou7w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 314805, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkEXjCRA9TVsSAnZWagAA3OEP/3HCms4z1W8VIcC9ShyS\nZjce4p8a2yrWb7I0Wg6B36LgjYOmg6aDh7ghSa2BPblERmAvhWPzG9cIqV3e\nG6MUot8aSpajhbldlE0Al4PnF3aGHjpNRjj8H9aQFBUT49tTwMcqoounkJf9\nYGgCjcA+sL3IAh4CoG8h0rCbNF6TiZhl0Eibbq3Yat6LJHXL7BTz+vObOpdb\nSqBp1fElu6+DlDzDDc8mHQn/Jpe5SmHtsFqdhYWr6XMmKkSyIZq3oQ5WKhda\nE6FortzofIwJogqkWWUyei/LnOSBNoP1x79VGsnV5M53A2UPhRYEhOVIhJwb\nfHXEaCyCsJSzHrrcQ2AQaJEB8m6Uqnj8VRpprDnt4H2ZFjU7JeKU9gM5M7Wg\nBHgDVPQZonGo75cAbF+Wp7zzPNR1LiKfrrQmeL3GkM9J6Z3TqBHu3ZOx4Ngs\nHrur0TRwohjSGRtAk4sJzjodkCs4PEuPcpc7rUFrUS34dWJ3drdtGNKChUke\nlTrJQuyQ1H3Gg/+xi3lIgMHKHeXe+AEs02mhHMLjnyssY+zDGgnuSoZkg+gJ\nItShD00fyN11OnucNv8QGNs/nsqih8ezXnRDlDqSyHFG4hMZcE5iRW7RBWF7\nhFeq878mTXjqHc3kq7otkbbQgtAA3yft/VPcRCECNUu7lPl/YvbU/gLgdRzk\nFG61\r\n=v3u6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "d45089ad7c78cb7df09983c20ac77fbc92b21bcd", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi --strip", "prebuild-ia32": "prebuildify --arch=ia32 --napi --strip", "prebuild-linux-arm": "prebuildify-cross -i linux-armv6 -i linux-armv7 -i linux-arm64 --napi --strip"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "16.0.0", "dependencies": {"node-gyp-build": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^8.0.1", "node-gyp": "^7.1.2", "prebuildify": "^4.0.0", "prebuildify-cross": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate_5.0.5_1620067810790_0.7241741317414967", "host": "s3://npm-registry-packages"}}, "5.0.6": {"name": "utf-8-validate", "version": "5.0.6", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@5.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "e1b3e0a5cc8648a3b44c1799fbb170d1aaaffe80", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-5.0.6.tgz", "fileCount": 14, "integrity": "sha512-hoY0gOf9EkCw+nimK21FVKHUIG1BMqSiRwxB/q3A9yKZOrOI99PP77BxmarDqWz6rG3vVYiBWfhG8z2Tl+7fZA==", "signatures": [{"sig": "MEUCIAHLzM2Kxi2l49Knet6VFk73s4mcThGP/stFi3nMo/zBAiEAzR3wN6koy8SBKJQX5dD8QmsU4BCnHscJE10DHLE9GbQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 417613}, "main": "index.js", "engines": {"node": ">=6.14.2"}, "gitHead": "f74964c529c09164dd90327e80cc754f3b4ade26", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi --strip", "prebuild-linux-arm": "prebuildify-cross -i linux-armv6 -i linux-armv7 -i linux-arm64 --napi --strip", "prebuild-darwin-x64+arm64": "prebuildify --arch x64+arm64 --napi --strip"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "7.24.0", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "16.10.0", "dependencies": {"node-gyp-build": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^9.0.3", "node-gyp": "^7.1.2", "prebuildify": "^4.0.0", "prebuildify-cross": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate_5.0.6_1632639012860_0.8937544336780723", "host": "s3://npm-registry-packages"}}, "5.0.7": {"name": "utf-8-validate", "version": "5.0.7", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@5.0.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "c15a19a6af1f7ad9ec7ddc425747ca28c3644922", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-5.0.7.tgz", "fileCount": 11, "integrity": "sha512-vLt1O5Pp+flcArHGIyKEQq883nBt8nN8tVBcoL0qUXj2XT1n7p70yGIq2VK98I5FdZ1YHc0wk/koOnHjnXWk1Q==", "signatures": [{"sig": "MEYCIQDQuHmuuYuo4upFcZAMHttHoWNpguWVDK3EYIUfjRstwgIhAKLmhk3vBibYNR1vzycm9CFBlFLwPvMEz/HsIwBKqDJn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 400442}, "main": "index.js", "engines": {"node": ">=6.14.2"}, "gitHead": "c3a9e8a6dc9dfd968834a524c76dd9ad43a3bb5c", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi --strip", "prebuild-darwin-x64+arm64": "prebuildify --arch x64+arm64 --napi --strip"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "8.0.0", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "16.11.1", "dependencies": {"node-gyp-build": "^4.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^9.0.3", "node-gyp": "^7.1.2", "prebuildify": "^4.0.0", "prebuildify-cross": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate_5.0.7_1634144759137_0.6582862304505412", "host": "s3://npm-registry-packages"}}, "5.0.8": {"name": "utf-8-validate", "version": "5.0.8", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@5.0.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "4a735a61661dbb1c59a0868c397d2fe263f14e58", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-5.0.8.tgz", "fileCount": 11, "integrity": "sha512-k4dW/Qja1BYDl2qD4tOMB9PFVha/UJtxTc1cXYOe3WwA/2m0Yn4qB7wLMpJyLJ/7DR0XnTut3HsCSzDT4ZvKgA==", "signatures": [{"sig": "MEUCIQCp9GAUvpBuYEdbtf7tFDQg/gDikBk4NIR7dM9HwjJjEwIgY4bHWuVESK4NydQ/27bOJc8bcooG0S6N3v9p2gL5Jpk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 400001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh0V3kCRA9TVsSAnZWagAAmckP/jst5gc7RSb3ZRPLMzlr\nqi8sjo4gKRT7chdNU+IJGx7ACF7CP5ciBt10a78iTFC5e40YjB/gc8XbhY4/\naIx6cMAicPNJz43mUmzt1KqxGY10m9CzOsePdAyv9LkQu61u9yXlXT/pB7cj\nJ4oXk3kj9SDbaJBa7fglSkdZA2D8OSh4NHHLbdjfBGHkHarVIuPLm8NyNB6Z\nuSeQzxx/fBboX82pW5Z4xawUz3nCHnBf3yPEZhtWQD8z2iyNRBCQVpFAh+ib\n1qwbZJ9coJIF3xaB5pW0/sy+zVESyDwHABF1beQ6hmNI+nh0uEiZwvM75GAO\ncSHVgkVctJQNT5KEWUy7e/0VoTBc1gHK4nz2lTdsd0eLrZrIlmPfbeaOVMck\nYSnP2OmBbfJ2Ody37eI4qX5u1IFI4tRUI2QSkqhX3/pnuV0E3XmSc+nRcXUP\n14M4Vdh/JTA3+EpHA34kEZ4iF3H5ysHDbE0GWzF6/uwsaS9PIkGwaHzcM/K7\nZ04f+oxpeJTMEAubHdcLeta74V5gLOgueo0LuKAgRYkEbgs8kD3EgxOs6fiU\naXRCwWf/kJiMlhB/90qD6jRdpUqah7khkBL7VGJrTzRt3pQ5CutjGX51OrT6\nqnF8Ev/bL9eItMN+vfJTOp5yY7e1rf1mI9nhU1m9M2W4p+pUWiy0Js6HKhHT\nkt92\r\n=fF1i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.14.2"}, "gitHead": "c9543426769b60adca60ba9f41e5dd65e561fc97", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi --strip", "prebuild-darwin-x64+arm64": "prebuildify --arch x64+arm64 --napi --strip"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "8.3.0", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "17.3.0", "dependencies": {"node-gyp-build": "^4.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^9.0.3", "node-gyp": "^7.1.2", "prebuildify": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate_5.0.8_1641111011981_0.7772687464485495", "host": "s3://npm-registry-packages"}}, "5.0.9": {"name": "utf-8-validate", "version": "5.0.9", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@5.0.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "ba16a822fbeedff1a58918f2a6a6b36387493ea3", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-5.0.9.tgz", "fileCount": 11, "integrity": "sha512-Yek7dAy0v3Kl0orwMlvi7TPtiCNrdfHNd7Gcc/pLq4BLXqfAmd0J7OWMizUQnTTJsyjKn02mU7anqwfmUP4J8Q==", "signatures": [{"sig": "MEUCIDq8QvqQRTelx3SFyAzIBxWmCbzCZFlgeMmFhRLJ6l9tAiEAih+S5Fdk6zrml5fNyWP657tZDOdSsFsMTWqnyyDnkGU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 400793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJ7KpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQxA/9GUDUcPcRbl5dMU4t8Dq2ruDOpFysyM16cHWcRe7u57xXj4Yh\r\nAgNmzdSeN2/wikU1HwB4ssumka2x1RXBOnC8hJMb7AvKZFDIsH5c+nTH1abQ\r\nZ+l9qlaSlFY3Fj4rYkUMXB6Vb3OKX4BYEuzPuzvniyxWQgLUFi3BJHK01nzt\r\nXb1Lkb1lXdWDO04Y6VLcJDsXeTfPZEk5oBU5oMel8rER/QY/UNwo7Vpuvix7\r\nzIldY+3yJ8q3emIOfX+zYmSS5V8Z+BI1B58S/txfnbjIaqGpJmtkIz1p8a+3\r\nXphWBEzS4+mX9skS/riMmho2Q50w+MK6zcVKfTAvgJEkyQhjNcZBRaPvduXd\r\nBrge6GjAkxQhP40oJ/j/lDerc+CjIho076anYW/2MeTHmmP6CIBnq/Ujuaji\r\n6Lf9knTPVA4Ga/6LZSTQL2zsxwCts3NQYC5i2OtHhxVRw0UVne3Pq6VMCvm5\r\nfEIqxQtPP+BiiHp2QU1DkFRhTMewa43hYLUc6C2cTRPw3XRht1fegQV5uMPE\r\n8YytzgAbIM2uIek217/TEM7tZXQeeknlLtVMSkps5mzUtGtdf27lUwBmRBIt\r\nHxgdPJosKvoKHYzOmsjSXfsg9O46CZMOLD0QTEr5i56mPmptYXuG0vise0TG\r\nu+Bx20/JSRhv1oephmZMMKBPsJZAUBvKsbE=\r\n=+xkv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.14.2"}, "gitHead": "25e81d715e93477b089b7585b41d6bfcf36e279e", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi --strip", "prebuild-darwin-x64+arm64": "prebuildify --arch x64+arm64 --napi --strip"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "8.5.1", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "17.6.0", "dependencies": {"node-gyp-build": "^4.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^9.0.3", "node-gyp": "^8.4.1", "prebuildify": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate_5.0.9_1646768809039_0.5519268628962095", "host": "s3://npm-registry-packages"}}, "5.0.10": {"name": "utf-8-validate", "version": "5.0.10", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@5.0.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "d7d10ea39318171ca982718b6b96a8d2442571a2", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-5.0.10.tgz", "fileCount": 11, "integrity": "sha512-Z6czzLq4u8fPOyx7TU6X3dvUZVvoJmxSQ+IcrlmagKhilxlhZgxPK6C5Jqbkw1IDUmFTM+cz9QDnnLTwDz/2gQ==", "signatures": [{"sig": "MEYCIQCk1Etvkn9znvQZ9QH16RxJ878DYqeRSNCrfYizQ9yAbwIhAJG4JriA/JhFnfxkhIP0qQHmllm8RDn7Kl/q56jYJEbJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 402875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTq/qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrzmBAAlus7NgLqT5DxSvFyrzpChKXwBpjGi0oCX5sFH8dEXDm5khBG\r\nOpXZm/Suyn9o0cyCU9+NaOt3AluhzcKycL5nLQCnV7pHHAYznIC7hlDagpwq\r\n5p8jJbRvwddQO9UabPtao8th9sVW+iDfNEAFjZqk0yc2eQF/cZHwP+IphX1W\r\n3hloElIfNSffdOcOyJhW2J8QO0IQwNEfAoBpAQ6jtt2xvxTpwm290a9qMREP\r\n5TXVN8NeI0dNfA4oBe6m4U439hrYPBN+D4Jz4UAIitHEE2DgnlFvIHBxIRe4\r\nFI686oV3uk4bIVj9HJcBqFJGrrnwhiM4oBPI5NX5ROtEfQreAbFCbaUa6G+y\r\nIcNHQ22Van6oVyT0DIJIg6Mjw0CJQT488IGCg5oCTnhp79b7HF/TNSzxVCCY\r\nLI7rj5ZGBKZUr5IkRopHXaCDGD8VIemjbe9lFrRXenZ3QGs1ANmbOUStPcyC\r\npxVglrkzsl2xnPZPfxWK5UwzBhFq38yC74NOQKAnEmugfRhYc2LmOcX9804G\r\n5xKvackpiLujHHDAhJ59HHnCykdt1m4uilHgj1p9jEoKGchlEVJuAPoMdDmo\r\nWO9KWz0tx/4+qfGmQSBxEuU8YgH6vJajgz0jgXeNN8S83wn8TXnoJU38mtir\r\nhk5AdqdnkTfptRN4OG1HU/V4xknV8ymlJNg=\r\n=V35M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.14.2"}, "gitHead": "f7d38c38022545b1bd12b7b24839927124cde76d", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi --strip --target=14.0.0", "prebuild-darwin-x64+arm64": "prebuildify --arch x64+arm64 --napi --strip --target=14.0.0"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "18.11.0", "dependencies": {"node-gyp-build": "^4.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "node-gyp": "^9.1.0", "prebuildify": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate_5.0.10_1666101226223_0.7363293758305502", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "utf-8-validate", "version": "6.0.0", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@6.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "91a169e91ee5441a2bab5f059f4a39cdd402caf2", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-6.0.0.tgz", "fileCount": 15, "integrity": "sha512-OCJuwxQsnG51swYmNloViggxNOFO/leOZpnb/vVeoastJbrzrZZU7lGsYlUcdkCl9nsBu2nkKLjpljb3Ckvb/Q==", "signatures": [{"sig": "MEYCIQDsOsrs3kTY27hIdEJMBCzpL1GJDsTnvgC4y5ORCne4LwIhANrtkrQcgTSRgZndbQPZ7chhKteOkTxJNegy8yZ21vi/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 705783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjteagACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTVg//U8x6J9RhwgSVnRLFEuJ7M1gyCdOI2c4tSqT9mMa7zoD5B4aR\r\npv6/n9dxZHIvxS0LBFF9tBZxb7E9sfSLmSSovfJSXtqjusrHkp2ZVsZWouaK\r\nGc8oZX/gjKJNPk88hmO9JcAX/Ypkc2Z6ApsE6Q8QdiNXfC40HxK+l/neJ8v1\r\njKi1wzAIYsMzME1ZQ7JldE/vpZP4J+zQ083A0UYin73FRbaJQcKgf6zhLVAR\r\nHRq8ueWrhBe4NBClq/nVcjo6j6tfeFC3sScAfGX0eCa0s5v/LOfXwPGX0Asj\r\nbOVqxnON++pzw2XfTuoiBglRH0RQ252Bneg6zujFDtfVMO958B7mUL66HcLa\r\nKvwfhTtbYoYlg57sOkGTajZq5Czgpx4J9s0pouzGpoczjEWAdEE1ZXjccskb\r\n+Rhrt5ER3MQfdc4g4ThNS69k6dSdbcajB3ODrMqgLMGLgoI/9jEoGkc1Jghi\r\nLOyBboTy4lQede5h2Q0oX395M9sCji8pynKXRPW3pG31eTIn00nsBUgVm5gz\r\nDIgFhG8VHfPMUneJMIIXLsH/bbyT/bB+y5rYPD0aQxReQn7SmrMHQgHGxs/L\r\nlQCbjjN8slPRaoogeWYl8/ysDvkh7/AHdzSfEy6jQRxJ8MmkHriEO+XKo38P\r\nU6geQY5MWtO1lIUctKNZA9wnKNFgx3q+BpQ=\r\n=Hqf6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.14.2"}, "gitHead": "569049a5f1f438296ef35048e9b8efab30485f54", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi --strip --target=14.0.0", "prebuild-darwin-x64+arm64": "prebuildify --arch x64+arm64 --napi --strip --target=14.0.0"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "19.3.0", "dependencies": {"node-gyp-build": "^4.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "node-gyp": "^9.1.0", "prebuildify": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate_6.0.0_1672865439750_0.0601933126338321", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "utf-8-validate", "version": "6.0.1", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@6.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "ca94aac987856c17c1b556b8b692323de98f521e", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-6.0.1.tgz", "fileCount": 15, "integrity": "sha512-gAZEa1DMXeBiHEwxef81kLtZjBrC1hib7UWnsvMVtxY8oJGtDSXt9McWu2D6V/xgrjbfRBsS5UIGEUBg2SrAsg==", "signatures": [{"sig": "MEUCIQD8WT4mbF52/87SaE7SW8OJLAEk9vIK7dw2PM5Vqtj4iwIgTYJzxccWpn7PXRjRmepXq9oubBodcRhBPZx2RUGN7vU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 709616, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzD3IACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYkQ//R3/07gOBEwpkXqugGalDPx/fmvBtnX8aU9d9cf5pRY+wyAP7\r\nNwpFt1hThnT8ghq1M9z40PzSMgoPDd8dl8WX0Fr838B7ZMluT6Yc0vsuLToc\r\nYAL06cefnsxeEn03KPmv+WxB1cPeKncm4uGhkogK3kBrLyu4rr3JftQkJ/lJ\r\nRnYYyy4cm+AetX/uUOVVIHUvBQGz1VDXHtFnpF2KjR5hCnu3M/aIoXWs9O2H\r\nIHJLqi8JMnmZOzCEDgba5B8T9PteAD0kBGAGWXZoBaJyJGwFc9QWDlv+IOM7\r\n6M+OLnjqAi25GUziyA7Yc41Pq31uprWw/VkORhUNAgNdbNf0ZeLIJhbGntas\r\nMkk/YYqfgbQChUZjXop4Sk5IsoJ+5Wh9oBJWxgwgvY9hvKvcAj4kSKMrSESr\r\nLInCDaEF+PzD78qVZgQ7tV4OHPMvwZrhiUJIexZRKXo4wICe2FH92N41No6b\r\n+5V5DuJU3AUHTuAD4qESOCpqo6BAz3BLYRjc7c0Ck1YNBESsaQGGrMv2Z1x9\r\n9hBxXTPPJTkiDMf51I0ZRKfDWsP82SiwlXnQHXsdj+e/yPU0fYsyZXuteN92\r\nW/7gJz8EAbprzyMx9CoAmYKBN5c4unGhhZjiwJa5rcfBWUqmw2Xxw7xL3io0\r\ngr1phXyWtOjy2kRlFBWAFo2DUKNC920MQRI=\r\n=9lyz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.14.2"}, "gitHead": "2f0e494f046d2ef647ef09bf0fc80a655d15d400", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi --strip --target=14.0.0", "prebuild-darwin-x64+arm64": "prebuildify --arch x64+arm64 --napi --strip --target=14.0.0"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "19.4.0", "dependencies": {"node-gyp-build": "^4.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "node-gyp": "^9.1.0", "prebuildify": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate_6.0.1_1674329544082_0.925786721292575", "host": "s3://npm-registry-packages"}}, "6.0.2": {"name": "utf-8-validate", "version": "6.0.2", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@6.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "2d80529963e4cc55ac5a1ca9dafdaa990d5ea16b", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-6.0.2.tgz", "fileCount": 16, "integrity": "sha512-yd7PQEOW+EgecUzSD7XUXTyq/vREGXk7t7fzGfOvwOAr0Z64h5rfGrmkNk8+ddVmf/FrkjPPhVyYBa7fuSPVTg==", "signatures": [{"sig": "MEUCIQDAYNb2E5aQ2f7FGC/3+h6Um6Cwiszo7+ZfxhyLxUfTXwIgWYMhoxodYtlOCiGMTq+HwEBGUDXyDz9oduaCqV35wKA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 744378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0uP/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmryWA//R38mwvwZyxUD7hLBNbJ6K9kwipqy1jSxaUkuKNW8ley/HNH5\r\nbISm+nxN/zd7WrVLonJR0PXtKHqWmQOqIOmbvi/wxCSVol86D3khPFrDVpbW\r\nVvatc0RwovVcsBJe+X3dYUvVPfV6HysfF7jI2vrd6DY1nlKc8o2BUiunJ0c0\r\nv2ON0kV9gMA4XUcQFTQNYAyXJhUGQ6ganlU6HLMuDasjEqoaBgxtjuL7+Qgp\r\n+6IxPWnoaegu6oOfFvliDaQTxB1pS0yc+JbCjwjKXHrQ86DaReM0lhrQQylU\r\nyZ8HFUe8np76P0hPBlJ2suRN8OhnyHhttBwbxDI4z7vIOFM8p9+8z3Hm0Rf2\r\nw6M2ExJOM1z9u+RKIMMBemqokiudP0Iwzq1ETwPNwL1bahnowZr9C/H2EOm2\r\nougIVdtefPwc7f2M2BhVPzdzb7DhGfpcIXcS09N37THe0oYMo+sfzqJAQxQx\r\n8HmSgJAX6s66SipQwVpLY+x4XfcBRgFmYzb3WGMYfiQMuASo13Uu8MvyvRsy\r\nEjWnoAMU2bcInCjFmc3ik0JdWKColBm3MEN8W2MTHp9G5ue9ERqZOisz+Y16\r\nFe2N09TtU4zBDjDgBwpiEjx18kGKBUXcmKFQ36sm8D27b3HqLiPTu+UqkNVK\r\nCYXBHzEtZ+c+vrCrQamZotTkHRi58zmrksI=\r\n=kTYW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.14.2"}, "gitHead": "a1c67cbd88039a3975a5381e83348eea5d199e87", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi --strip --target=8.11.2", "prebuild-linux-musl-x64": "prebuildify-cross --image alpine --napi --strip --target=8.11.2", "prebuild-darwin-x64+arm64": "prebuildify --arch x64+arm64 --napi --strip --target=8.11.2"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "9.3.1", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "19.5.0", "dependencies": {"node-gyp-build": "^4.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "node-gyp": "^9.1.0", "prebuildify": "^5.0.0", "prebuildify-cross": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate_6.0.2_1674765310949_0.28234913645469506", "host": "s3://npm-registry-packages"}}, "6.0.3": {"name": "utf-8-validate", "version": "6.0.3", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@6.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "7d8c936d854e86b24d1d655f138ee27d2636d777", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-6.0.3.tgz", "fileCount": 16, "integrity": "sha512-uIuGf9TWQ/y+0Lp+KGZCMuJWc3N9BHA+l/UmHd/oUHwJJDeysyTRxNQVkbzsIWfGFbRe3OcgML/i0mvVRPOyDA==", "signatures": [{"sig": "MEUCIBFxV4t4OhlKaxNbwxnfd5D6fDxmuKuHttcv//4duT2/AiEA3dzpFlOhPUvYUVMwjoopmnSF6WClUWk5uktNM9KEoi0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 744194, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+PvAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOdw//cZCNg2tN0I1bL/2F54t20tu6OGIbib7i5Rl9B8LcQQxnbbnM\r\nXhFavxeD6tMDboyKaORFDhV7fK21uMePyREXP0SYqlr3l+2YRPdTV2Fzu2MT\r\nKLXo3kOFf4CryuX/+fSv9/nx/YYLKthjrWT6jtlAfn26MeqmvnfeqGp2+FvO\r\n46oKQgSReGBOCR+AP+Oebx9TTdKtA08tWfS968r99P3Y/9ZuV6Do9DVIJVSM\r\nBrjsGn3nwODMRPPkznuJK1fsjO1zm67w7O9IndlobhXwv2/zRvyseCQA77Sc\r\nIMBzQYD6UNlBaPVTmNN1mpDKO0NUVLS5vF8uHG9xpPv21PCqXgjnP4U6EHeo\r\nUVm+w0RjJMkiR0xbzeGy0UsYD2BYNFwhPPSsNPZSN62WlQkNnuQ5CRLmPQwf\r\n+XycRVQQahudKznqGT3VYeGug6gaabdM3Gb85d+W3tOsG0SlcLx4BcxYsWjZ\r\ncSfUwFOxOtyddXDr8Gjhsm6t7FdcSSqZAp6fV+zVXiReUp6xbGd2Mpg8cpth\r\nWUNwOlZB9uyF9QkWf5guyLsS69qWrmjw/Nlllv3OY84uqqyFOW3F1+3hXkc9\r\nMeQD/VqdJjqvtMRwoMWZtIuabkrL9kWoroWnHZYTHmtgL0mJzchiqCilVykW\r\nNxOs1ZzskUDqHALaNeG2C711I31H6RmPUQA=\r\n=W2Zc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6.14.2"}, "gitHead": "ac283149c66cb025987bd1a583ee38751ad38db9", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi --strip --target=8.11.2", "prebuild-linux-musl-x64": "prebuildify-cross --image alpine --napi --strip --target=8.11.2", "prebuild-darwin-x64+arm64": "prebuildify --arch x64+arm64 --napi --strip --target=8.11.2"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "19.7.0", "dependencies": {"node-gyp-build": "^4.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "node-gyp": "^9.1.0", "prebuildify": "^5.0.0", "prebuildify-cross": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate_6.0.3_1677261760318_0.0018567359856622723", "host": "s3://npm-registry-packages"}}, "6.0.4": {"name": "utf-8-validate", "version": "6.0.4", "keywords": ["utf-8-validate"], "author": {"url": "http://2x.io", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "utf-8-validate@6.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/websockets/utf-8-validate", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "dist": {"shasum": "1305a1bfd94cecb5a866e6fc74fd07f3ed7292e5", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-6.0.4.tgz", "fileCount": 15, "integrity": "sha512-xu9GQDeFp+eZ6LnCywXN/zBancWvOpUMzgjLPSjy4BRHSmTelvn2E0DG0o1sTiw5hkCKBHo8rwSKncfRfv2EEQ==", "signatures": [{"sig": "MEYCIQCaPcUbyk93u1MX3GfYg1Yq3SrDzNHZfRgbXq9W3ntoXgIhAI7tIFRexK0MX6s7A/zfSUsOiQk/MPGjbxo3e8EY9zXg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 753246}, "main": "index.js", "engines": {"node": ">=6.14.2"}, "gitHead": "8d159b34a470e2a2e31e9b4cfbdeb683912e2f69", "scripts": {"test": "mocha", "install": "node-gyp-build", "prebuild": "prebuildify --napi --strip --target=8.11.2", "prebuild-linux-musl-x64": "prebuildify-cross --image alpine --napi --strip --target=8.11.2", "prebuild-darwin-x64+arm64": "prebuildify --arch x64+arm64 --napi --strip --target=8.11.2"}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "repository": {"url": "git+https://github.com/websockets/utf-8-validate.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Check if a buffer contains valid UTF-8", "directories": {}, "_nodeVersion": "22.1.0", "dependencies": {"node-gyp-build": "^4.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.0.0", "node-gyp": "^10.0.1", "prebuildify": "^6.0.0", "prebuildify-cross": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/utf-8-validate_6.0.4_1715358416127_0.7886578690145345", "host": "s3://npm-registry-packages"}}, "6.0.5": {"name": "utf-8-validate", "version": "6.0.5", "description": "Check if a buffer contains valid UTF-8", "main": "index.js", "engines": {"node": ">=6.14.2"}, "scripts": {"install": "node-gyp-build", "prebuild": "prebuildify --napi --strip --target=8.11.2", "prebuild-linux-musl-x64": "prebuildify-cross --image alpine --napi --strip --target=8.11.2", "prebuild-darwin-x64+arm64": "prebuildify --arch x64+arm64 --napi --strip --target=8.11.2", "test": "mocha"}, "repository": {"type": "git", "url": "git+https://github.com/websockets/utf-8-validate.git"}, "keywords": ["utf-8-validate"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://2x.io"}, "license": "MIT", "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "homepage": "https://github.com/websockets/utf-8-validate", "dependencies": {"node-gyp-build": "^4.3.0"}, "devDependencies": {"mocha": "^10.0.0", "node-gyp": "^10.0.1", "prebuildify": "^6.0.0", "prebuildify-cross": "^5.0.0"}, "_id": "utf-8-validate@6.0.5", "gitHead": "4a9a05728641674620818c6a78b01fce38e6f222", "_nodeVersion": "22.10.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-EYZR+OpIXp9Y1eG1iueg8KRsY8TuT8VNgnanZ0uA3STqhHQTLwbl+WX76/9X5OY12yQubymBpaBSmMPkSTQcKA==", "shasum": "8087d39902be2cc15bdb21a426697ff256d65aab", "tarball": "https://registry.npmjs.org/utf-8-validate/-/utf-8-validate-6.0.5.tgz", "fileCount": 15, "unpackedSize": 717511, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC7yAFm/qGptFOFlo/+mlBdbwt+ll3S891sr5jqlnITsgIgWXcU2SlnkRVxFbo2dj8s91N2pUVdq/FUcSgJWOY0MUo="}]}, "_npmUser": {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/utf-8-validate_6.0.5_1730483020836_0.04149551631767867"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-01-28T08:38:29.614Z", "modified": "2024-11-01T17:43:41.232Z", "1.0.0": "2015-01-28T08:38:29.614Z", "1.0.1": "2015-01-29T10:05:31.659Z", "1.1.0": "2015-05-05T19:37:11.971Z", "1.2.0": "2015-08-13T12:14:09.977Z", "1.2.1": "2015-08-21T11:38:43.101Z", "1.2.2": "2016-12-07T16:35:10.571Z", "2.0.0": "2016-12-08T15:19:55.026Z", "3.0.0": "2017-02-03T17:52:16.535Z", "3.0.1": "2017-02-06T13:51:08.438Z", "3.0.2": "2017-05-31T08:09:41.671Z", "3.0.3": "2017-07-07T10:31:04.016Z", "3.0.4": "2017-11-03T08:27:02.909Z", "4.0.0": "2017-12-03T11:44:17.845Z", "4.0.0-napi": "2017-12-04T13:55:40.833Z", "4.0.1": "2018-04-06T12:07:19.180Z", "4.0.2": "2018-05-06T05:50:21.437Z", "5.0.0": "2018-07-05T07:09:35.396Z", "5.0.1": "2018-07-05T07:41:30.689Z", "5.0.2": "2018-12-23T08:24:02.814Z", "5.0.3": "2020-10-31T14:39:51.679Z", "5.0.4": "2021-01-03T19:21:38.414Z", "5.0.5": "2021-05-03T18:50:11.167Z", "5.0.6": "2021-09-26T06:50:13.195Z", "5.0.7": "2021-10-13T17:05:59.360Z", "5.0.8": "2022-01-02T08:10:12.184Z", "5.0.9": "2022-03-08T19:46:49.212Z", "5.0.10": "2022-10-18T13:53:46.390Z", "6.0.0": "2023-01-04T20:50:39.970Z", "6.0.1": "2023-01-21T19:32:24.287Z", "6.0.2": "2023-01-26T20:35:11.260Z", "6.0.3": "2023-02-24T18:02:40.504Z", "6.0.4": "2024-05-10T16:26:56.317Z", "6.0.5": "2024-11-01T17:43:41.060Z"}, "bugs": {"url": "https://github.com/websockets/utf-8-validate/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://2x.io"}, "license": "MIT", "homepage": "https://github.com/websockets/utf-8-validate", "keywords": ["utf-8-validate"], "repository": {"type": "git", "url": "git+https://github.com/websockets/utf-8-validate.git"}, "description": "Check if a buffer contains valid UTF-8", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "v1", "email": "<EMAIL>"}, {"name": "lpinca", "email": "luigi<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# utf-8-validate\n\n[![Version npm](https://img.shields.io/npm/v/utf-8-validate.svg?logo=npm)](https://www.npmjs.com/package/utf-8-validate)\n[![Linux/macOS/Windows Build](https://img.shields.io/github/actions/workflow/status/websockets/utf-8-validate/ci.yml?branch=master&label=build&logo=github)](https://github.com/websockets/utf-8-validate/actions?query=workflow%3ACI+branch%3Amaster)\n\nCheck if a buffer contains valid UTF-8 encoded text.\n\n## Installation\n\n```\nnpm install utf-8-validate --save-optional\n```\n\nThe `--save-optional` flag tells npm to save the package in your package.json\nunder the\n[`optionalDependencies`](https://docs.npmjs.com/files/package.json#optionaldependencies)\nkey.\n\n## API\n\nThe module exports a single function that takes one argument. To maximize\nperformance, the argument is not validated. It is the caller's responsibility to\nensure that it is correct.\n\n### `isValidUTF8(buffer)`\n\nChecks whether a buffer contains valid UTF-8.\n\n#### Arguments\n\n- `buffer` - The buffer to check.\n\n#### Return value\n\n`true` if the buffer contains only correct UTF-8, else `false`.\n\n#### Example\n\n```js\n'use strict';\n\nconst isValidUTF8 = require('utf-8-validate');\n\nconst buf = Buffer.from([0xf0, 0x90, 0x80, 0x80]);\n\nconsole.log(isValidUTF8(buf));\n// => true\n```\n\n## License\n\n[MIT](LICENSE)\n", "readmeFilename": "README.md", "users": {"hiwanz": true, "apihlaja": true, "mojaray2k": true}}