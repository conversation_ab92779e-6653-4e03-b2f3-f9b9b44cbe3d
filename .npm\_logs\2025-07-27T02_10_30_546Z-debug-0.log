0 verbose cli /usr/local/bin/node /usr/local/bin/npm
1 info using npm@11.4.2
2 info using node@v23.11.1
3 silly config load:file:/usr/local/lib/node_modules/npm/npmrc
4 silly config load:file:/home/<USER>/.npmrc
5 silly config load:file:/usr/local/etc/npmrc
6 verbose title npm install discord.js
7 verbose argv "install" "discord.js"
8 verbose logfile logs-max:10 dir:/home/<USER>/.npm/_logs/2025-07-27T02_10_30_546Z-
9 verbose logfile /home/<USER>/.npm/_logs/2025-07-27T02_10_30_546Z-debug-0.log
10 silly logfile done cleaning log files
11 silly packumentCache heap:1228144640 maxSize:307036160 maxEntrySize:153518080
12 silly idealTree buildDeps
13 silly fetch manifest @discordjs/builders@^1.7.0
14 silly packumentCache full:https://registry.npmjs.org/@discordjs%2fbuilders cache-miss
15 http fetch GET 200 https://registry.npmjs.org/npm 127ms
16 http fetch GET 200 https://registry.npmjs.org/@discordjs%2fbuilders 138ms (cache miss)
17 silly packumentCache full:https://registry.npmjs.org/@discordjs%2fbuilders set size:undefined disposed:false
18 silly fetch manifest @discordjs/rest@^2.2.0
19 silly packumentCache full:https://registry.npmjs.org/@discordjs%2frest cache-miss
20 http fetch GET 200 https://registry.npmjs.org/@discordjs%2frest 188ms (cache miss)
21 silly packumentCache full:https://registry.npmjs.org/@discordjs%2frest set size:undefined disposed:false
22 silly fetch manifest @discordjs/voice@^0.18.0
23 silly packumentCache full:https://registry.npmjs.org/@discordjs%2fvoice cache-miss
24 http fetch GET 200 https://registry.npmjs.org/@discordjs%2fvoice 356ms (cache miss)
25 silly packumentCache full:https://registry.npmjs.org/@discordjs%2fvoice set size:undefined disposed:false
26 silly fetch manifest better-sqlite3@^12.2.0
27 silly packumentCache full:https://registry.npmjs.org/better-sqlite3 cache-miss
28 http fetch GET 200 https://registry.npmjs.org/better-sqlite3 37ms (cache miss)
29 silly packumentCache full:https://registry.npmjs.org/better-sqlite3 set size:undefined disposed:false
30 silly fetch manifest discord.js@^14.21.0
31 silly packumentCache full:https://registry.npmjs.org/discord.js cache-miss
32 http fetch GET 200 https://registry.npmjs.org/discord.js 138ms (cache miss)
33 silly packumentCache full:https://registry.npmjs.org/discord.js set size:undefined disposed:false
34 silly fetch manifest dotenv@^16.4.7
35 silly packumentCache full:https://registry.npmjs.org/dotenv cache-miss
36 http fetch GET 200 https://registry.npmjs.org/dotenv 30ms (cache miss)
37 silly packumentCache full:https://registry.npmjs.org/dotenv set size:undefined disposed:false
38 silly fetch manifest fs@^0.0.1-security
39 silly packumentCache full:https://registry.npmjs.org/fs cache-miss
40 http fetch GET 200 https://registry.npmjs.org/fs 38ms (cache miss)
41 silly packumentCache full:https://registry.npmjs.org/fs set size:undefined disposed:false
42 silly fetch manifest path@^0.12.7
43 silly packumentCache full:https://registry.npmjs.org/path cache-miss
44 http fetch GET 200 https://registry.npmjs.org/path 23ms (cache miss)
45 silly packumentCache full:https://registry.npmjs.org/path set size:undefined disposed:false
46 silly fetch manifest @types/cors@2.8.17
47 silly packumentCache full:https://registry.npmjs.org/@types%2fcors cache-miss
48 http fetch GET 200 https://registry.npmjs.org/@types%2fcors 38ms (cache miss)
49 silly packumentCache full:https://registry.npmjs.org/@types%2fcors set size:undefined disposed:false
50 silly fetch manifest @types/express@5.0.0
51 silly packumentCache full:https://registry.npmjs.org/@types%2fexpress cache-miss
52 http fetch GET 200 https://registry.npmjs.org/@types%2fexpress 33ms (cache miss)
53 silly packumentCache full:https://registry.npmjs.org/@types%2fexpress set size:undefined disposed:false
54 silly fetch manifest cross-env@7.0.3
55 silly packumentCache full:https://registry.npmjs.org/cross-env cache-miss
56 http fetch GET 200 https://registry.npmjs.org/cross-env 39ms (cache miss)
57 silly packumentCache full:https://registry.npmjs.org/cross-env set size:undefined disposed:false
58 silly fetch manifest jest@29.7.0
59 silly packumentCache full:https://registry.npmjs.org/jest cache-miss
60 http fetch GET 200 https://registry.npmjs.org/jest 66ms (cache miss)
61 silly packumentCache full:https://registry.npmjs.org/jest set size:undefined disposed:false
62 silly fetch manifest node-notifier@^8.0.1 || ^9.0.0 || ^10.0.0
63 silly packumentCache full:https://registry.npmjs.org/node-notifier cache-miss
64 http fetch GET 200 https://registry.npmjs.org/node-notifier 26ms (cache miss)
65 silly packumentCache full:https://registry.npmjs.org/node-notifier set size:undefined disposed:false
66 silly fetch manifest nodemon@3.1.7
67 silly packumentCache full:https://registry.npmjs.org/nodemon cache-miss
68 http fetch GET 200 https://registry.npmjs.org/nodemon 56ms (cache miss)
69 silly packumentCache full:https://registry.npmjs.org/nodemon set size:undefined disposed:false
70 silly fetch manifest typescript@5.7.3
71 silly packumentCache full:https://registry.npmjs.org/typescript cache-miss
72 http fetch GET 200 https://registry.npmjs.org/typescript 232ms (cache miss)
73 silly packumentCache full:https://registry.npmjs.org/typescript set size:undefined disposed:false
74 silly placeDep ROOT @discordjs/builders@1.11.2 OK for: v14bot@1.0.0 want: ^1.7.0
75 silly placeDep ROOT @discordjs/rest@2.5.1 OK for: v14bot@1.0.0 want: ^2.2.0
76 silly placeDep ROOT @discordjs/voice@0.18.0 OK for: v14bot@1.0.0 want: ^0.18.0
77 silly placeDep ROOT @types/cors@2.8.17 OK for: v14bot@1.0.0 want: 2.8.17
78 silly placeDep ROOT @types/express@5.0.0 OK for: v14bot@1.0.0 want: 5.0.0
79 silly placeDep ROOT better-sqlite3@12.2.0 OK for: v14bot@1.0.0 want: ^12.2.0
80 silly placeDep ROOT cross-env@7.0.3 OK for: v14bot@1.0.0 want: 7.0.3
81 silly placeDep ROOT discord.js@14.21.0 OK for: v14bot@1.0.0 want: ^14.21.0
82 silly placeDep ROOT dotenv@16.6.1 OK for: v14bot@1.0.0 want: ^16.4.7
83 silly placeDep ROOT fs@0.0.1-security OK for: v14bot@1.0.0 want: ^0.0.1-security
84 silly placeDep ROOT jest@29.7.0 OK for: v14bot@1.0.0 want: 29.7.0
85 silly placeDep ROOT nodemon@3.1.7 OK for: v14bot@1.0.0 want: 3.1.7
86 silly placeDep ROOT path@0.12.7 OK for: v14bot@1.0.0 want: ^0.12.7
87 silly placeDep ROOT typescript@5.7.3 OK for: v14bot@1.0.0 want: 5.7.3
88 silly fetch manifest tslib@^2.6.3
89 silly packumentCache full:https://registry.npmjs.org/tslib cache-miss
90 http fetch GET 200 https://registry.npmjs.org/tslib 27ms (cache miss)
91 silly packumentCache full:https://registry.npmjs.org/tslib set size:undefined disposed:false
92 silly fetch manifest ts-mixer@^6.0.4
93 silly packumentCache full:https://registry.npmjs.org/ts-mixer cache-miss
94 http fetch GET 200 https://registry.npmjs.org/ts-mixer 24ms (cache miss)
95 silly packumentCache full:https://registry.npmjs.org/ts-mixer set size:undefined disposed:false
96 silly fetch manifest @discordjs/util@^1.1.1
97 silly packumentCache full:https://registry.npmjs.org/@discordjs%2futil cache-miss
98 http fetch GET 200 https://registry.npmjs.org/@discordjs%2futil 231ms (cache miss)
99 silly packumentCache full:https://registry.npmjs.org/@discordjs%2futil set size:undefined disposed:false
100 silly fetch manifest fast-deep-equal@^3.1.3
101 silly packumentCache full:https://registry.npmjs.org/fast-deep-equal cache-miss
102 http fetch GET 200 https://registry.npmjs.org/fast-deep-equal 24ms (cache miss)
103 silly packumentCache full:https://registry.npmjs.org/fast-deep-equal set size:undefined disposed:false
104 silly fetch manifest discord-api-types@^0.38.1
105 silly packumentCache full:https://registry.npmjs.org/discord-api-types cache-miss
106 http fetch GET 200 https://registry.npmjs.org/discord-api-types 150ms (cache miss)
107 silly packumentCache full:https://registry.npmjs.org/discord-api-types set size:undefined disposed:false
108 silly fetch manifest @sapphire/shapeshift@^4.0.0
109 silly packumentCache full:https://registry.npmjs.org/@sapphire%2fshapeshift cache-miss
110 http fetch GET 200 https://registry.npmjs.org/@sapphire%2fshapeshift 64ms (cache miss)
111 silly packumentCache full:https://registry.npmjs.org/@sapphire%2fshapeshift set size:undefined disposed:false
112 silly fetch manifest @discordjs/formatters@^0.6.1
113 silly packumentCache full:https://registry.npmjs.org/@discordjs%2fformatters cache-miss
114 http fetch GET 200 https://registry.npmjs.org/@discordjs%2fformatters 205ms (cache miss)
115 silly packumentCache full:https://registry.npmjs.org/@discordjs%2fformatters set size:undefined disposed:false
116 silly fetch manifest undici@6.21.3
117 silly packumentCache full:https://registry.npmjs.org/undici cache-miss
118 http fetch GET 200 https://registry.npmjs.org/undici 31ms (cache miss)
119 silly packumentCache full:https://registry.npmjs.org/undici set size:undefined disposed:false
120 silly fetch manifest magic-bytes.js@^1.10.0
121 silly packumentCache full:https://registry.npmjs.org/magic-bytes.js cache-miss
122 http fetch GET 200 https://registry.npmjs.org/magic-bytes.js 96ms (cache miss)
123 silly packumentCache full:https://registry.npmjs.org/magic-bytes.js set size:undefined disposed:false
124 silly fetch manifest @sapphire/snowflake@^3.5.3
125 silly packumentCache full:https://registry.npmjs.org/@sapphire%2fsnowflake cache-miss
126 http fetch GET 200 https://registry.npmjs.org/@sapphire%2fsnowflake 186ms (cache miss)
127 silly packumentCache full:https://registry.npmjs.org/@sapphire%2fsnowflake set size:undefined disposed:false
128 silly fetch manifest @discordjs/collection@^2.1.1
129 silly packumentCache full:https://registry.npmjs.org/@discordjs%2fcollection cache-miss
130 http fetch GET 200 https://registry.npmjs.org/@discordjs%2fcollection 104ms (cache miss)
131 silly packumentCache full:https://registry.npmjs.org/@discordjs%2fcollection set size:undefined disposed:false
132 silly fetch manifest @sapphire/async-queue@^1.5.3
133 silly packumentCache full:https://registry.npmjs.org/@sapphire%2fasync-queue cache-miss
134 http fetch GET 200 https://registry.npmjs.org/@sapphire%2fasync-queue 160ms (cache miss)
135 silly packumentCache full:https://registry.npmjs.org/@sapphire%2fasync-queue set size:undefined disposed:false
136 silly fetch manifest @vladfrangu/async_event_emitter@^2.4.6
137 silly packumentCache full:https://registry.npmjs.org/@vladfrangu%2fasync_event_emitter cache-miss
138 http fetch GET 200 https://registry.npmjs.org/@vladfrangu%2fasync_event_emitter 169ms (cache miss)
139 silly packumentCache full:https://registry.npmjs.org/@vladfrangu%2fasync_event_emitter set size:undefined disposed:false
140 silly fetch manifest ws@^8.18.0
141 silly packumentCache full:https://registry.npmjs.org/ws cache-miss
142 http fetch GET 200 https://registry.npmjs.org/ws 37ms (cache miss)
143 silly packumentCache full:https://registry.npmjs.org/ws set size:undefined disposed:false
144 silly fetch manifest @types/ws@^8.5.12
145 silly packumentCache full:https://registry.npmjs.org/@types%2fws cache-miss
146 http fetch GET 200 https://registry.npmjs.org/@types%2fws 423ms (cache miss)
147 silly packumentCache full:https://registry.npmjs.org/@types%2fws set size:undefined disposed:false
148 silly fetch manifest prism-media@^1.3.5
149 silly packumentCache full:https://registry.npmjs.org/prism-media cache-miss
150 http fetch GET 200 https://registry.npmjs.org/prism-media 94ms (cache miss)
151 silly packumentCache full:https://registry.npmjs.org/prism-media set size:undefined disposed:false
152 silly fetch manifest discord-api-types@^0.37.103
153 silly packumentCache full:https://registry.npmjs.org/discord-api-types cache-miss
154 http cache https://registry.npmjs.org/discord-api-types 23ms (cache hit)
155 silly packumentCache full:https://registry.npmjs.org/discord-api-types set size:7191096 disposed:false
156 silly fetch manifest @types/node@*
157 silly packumentCache full:https://registry.npmjs.org/@types%2fnode cache-miss
158 http fetch GET 200 https://registry.npmjs.org/@types%2fnode 181ms (cache miss)
159 silly packumentCache full:https://registry.npmjs.org/@types%2fnode set size:undefined disposed:false
160 silly fetch manifest @types/qs@*
161 silly packumentCache full:https://registry.npmjs.org/@types%2fqs cache-miss
162 http fetch GET 200 https://registry.npmjs.org/@types%2fqs 30ms (cache miss)
163 silly packumentCache full:https://registry.npmjs.org/@types%2fqs set size:undefined disposed:false
164 silly fetch manifest @types/body-parser@*
165 silly packumentCache full:https://registry.npmjs.org/@types%2fbody-parser cache-miss
166 http fetch GET 200 https://registry.npmjs.org/@types%2fbody-parser 55ms (cache miss)
167 silly packumentCache full:https://registry.npmjs.org/@types%2fbody-parser set size:undefined disposed:false
168 silly fetch manifest @types/serve-static@*
169 silly packumentCache full:https://registry.npmjs.org/@types%2fserve-static cache-miss
170 http fetch GET 200 https://registry.npmjs.org/@types%2fserve-static 34ms (cache miss)
171 silly packumentCache full:https://registry.npmjs.org/@types%2fserve-static set size:undefined disposed:false
172 silly fetch manifest @types/express-serve-static-core@^5.0.0
173 silly packumentCache full:https://registry.npmjs.org/@types%2fexpress-serve-static-core cache-miss
174 http fetch GET 200 https://registry.npmjs.org/@types%2fexpress-serve-static-core 62ms (cache miss)
175 silly packumentCache full:https://registry.npmjs.org/@types%2fexpress-serve-static-core set size:undefined disposed:false
176 silly fetch manifest bindings@^1.5.0
177 silly packumentCache full:https://registry.npmjs.org/bindings cache-miss
178 http fetch GET 200 https://registry.npmjs.org/bindings 21ms (cache miss)
179 silly packumentCache full:https://registry.npmjs.org/bindings set size:undefined disposed:false
180 silly fetch manifest prebuild-install@^7.1.1
181 silly packumentCache full:https://registry.npmjs.org/prebuild-install cache-miss
182 http fetch GET 200 https://registry.npmjs.org/prebuild-install 30ms (cache miss)
183 silly packumentCache full:https://registry.npmjs.org/prebuild-install set size:undefined disposed:false
184 silly fetch manifest cross-spawn@^7.0.1
185 silly packumentCache full:https://registry.npmjs.org/cross-spawn cache-miss
186 http fetch GET 200 https://registry.npmjs.org/cross-spawn 25ms (cache miss)
187 silly packumentCache full:https://registry.npmjs.org/cross-spawn set size:undefined disposed:false
188 silly fetch manifest @discordjs/ws@^1.2.3
189 silly packumentCache full:https://registry.npmjs.org/@discordjs%2fws cache-miss
190 http fetch GET 200 https://registry.npmjs.org/@discordjs%2fws 110ms (cache miss)
191 silly packumentCache full:https://registry.npmjs.org/@discordjs%2fws set size:undefined disposed:false
192 silly fetch manifest fast-deep-equal@3.1.3
193 silly packumentCache full:https://registry.npmjs.org/fast-deep-equal cache-miss
194 http cache https://registry.npmjs.org/fast-deep-equal 6ms (cache hit)
195 silly packumentCache full:https://registry.npmjs.org/fast-deep-equal set size:48634 disposed:false
196 silly fetch manifest lodash.snakecase@4.1.1
197 silly packumentCache full:https://registry.npmjs.org/lodash.snakecase cache-miss
198 http fetch GET 200 https://registry.npmjs.org/lodash.snakecase 32ms (cache miss)
199 silly packumentCache full:https://registry.npmjs.org/lodash.snakecase set size:undefined disposed:false
200 silly fetch manifest @sapphire/snowflake@3.5.3
201 silly packumentCache full:https://registry.npmjs.org/@sapphire%2fsnowflake cache-miss
202 http cache https://registry.npmjs.org/@sapphire%2fsnowflake 7ms (cache hit)
203 silly packumentCache full:https://registry.npmjs.org/@sapphire%2fsnowflake set size:2899762 disposed:false
204 silly fetch manifest @discordjs/collection@1.5.3
205 silly packumentCache full:https://registry.npmjs.org/@discordjs%2fcollection cache-miss
206 http cache https://registry.npmjs.org/@discordjs%2fcollection 11ms (cache hit)
207 silly packumentCache full:https://registry.npmjs.org/@discordjs%2fcollection set size:5051076 disposed:false
208 silly fetch manifest jest-cli@^29.7.0
209 silly packumentCache full:https://registry.npmjs.org/jest-cli cache-miss
210 http fetch GET 200 https://registry.npmjs.org/jest-cli 35ms (cache miss)
211 silly packumentCache full:https://registry.npmjs.org/jest-cli set size:undefined disposed:false
212 silly fetch manifest @jest/core@^29.7.0
213 silly packumentCache full:https://registry.npmjs.org/@jest%2fcore cache-miss
214 http fetch GET 200 https://registry.npmjs.org/@jest%2fcore 42ms (cache miss)
215 silly packumentCache full:https://registry.npmjs.org/@jest%2fcore set size:undefined disposed:false
216 silly fetch manifest @jest/types@^29.6.3
217 silly packumentCache full:https://registry.npmjs.org/@jest%2ftypes cache-miss
218 http fetch GET 200 https://registry.npmjs.org/@jest%2ftypes 45ms (cache miss)
219 silly packumentCache full:https://registry.npmjs.org/@jest%2ftypes set size:undefined disposed:false
220 silly fetch manifest import-local@^3.0.2
221 silly packumentCache full:https://registry.npmjs.org/import-local cache-miss
222 http fetch GET 200 https://registry.npmjs.org/import-local 29ms (cache miss)
223 silly packumentCache full:https://registry.npmjs.org/import-local set size:undefined disposed:false
224 silly fetch manifest debug@^4
225 silly packumentCache full:https://registry.npmjs.org/debug cache-miss
226 http fetch GET 200 https://registry.npmjs.org/debug 21ms (cache miss)
227 silly packumentCache full:https://registry.npmjs.org/debug set size:undefined disposed:false
228 silly fetch manifest touch@^3.1.0
229 silly packumentCache full:https://registry.npmjs.org/touch cache-miss
230 http fetch GET 200 https://registry.npmjs.org/touch 33ms (cache miss)
231 silly packumentCache full:https://registry.npmjs.org/touch set size:undefined disposed:false
232 silly fetch manifest semver@^7.5.3
233 silly packumentCache full:https://registry.npmjs.org/semver cache-miss
234 http fetch GET 200 https://registry.npmjs.org/semver 27ms (cache miss)
235 silly packumentCache full:https://registry.npmjs.org/semver set size:undefined disposed:false
236 silly fetch manifest chokidar@^3.5.2
237 silly packumentCache full:https://registry.npmjs.org/chokidar cache-miss
238 http fetch GET 200 https://registry.npmjs.org/chokidar 26ms (cache miss)
239 silly packumentCache full:https://registry.npmjs.org/chokidar set size:undefined disposed:false
240 silly fetch manifest minimatch@^3.1.2
241 silly packumentCache full:https://registry.npmjs.org/minimatch cache-miss
242 http fetch GET 200 https://registry.npmjs.org/minimatch 38ms (cache miss)
243 silly packumentCache full:https://registry.npmjs.org/minimatch set size:undefined disposed:false
244 silly fetch manifest undefsafe@^2.0.5
245 silly packumentCache full:https://registry.npmjs.org/undefsafe cache-miss
246 http fetch GET 200 https://registry.npmjs.org/undefsafe 27ms (cache miss)
247 silly packumentCache full:https://registry.npmjs.org/undefsafe set size:undefined disposed:false
248 silly fetch manifest pstree.remy@^1.1.8
249 silly packumentCache full:https://registry.npmjs.org/pstree.remy cache-miss
250 http fetch GET 200 https://registry.npmjs.org/pstree.remy 37ms (cache miss)
251 silly packumentCache full:https://registry.npmjs.org/pstree.remy set size:undefined disposed:false
252 silly fetch manifest supports-color@^5.5.0
253 silly packumentCache full:https://registry.npmjs.org/supports-color cache-miss
254 http fetch GET 200 https://registry.npmjs.org/supports-color 36ms (cache miss)
255 silly packumentCache full:https://registry.npmjs.org/supports-color set size:undefined disposed:false
256 silly fetch manifest ignore-by-default@^1.0.1
257 silly packumentCache full:https://registry.npmjs.org/ignore-by-default cache-miss
258 http fetch GET 200 https://registry.npmjs.org/ignore-by-default 22ms (cache miss)
259 silly packumentCache full:https://registry.npmjs.org/ignore-by-default set size:undefined disposed:false
260 silly fetch manifest simple-update-notifier@^2.0.0
261 silly packumentCache full:https://registry.npmjs.org/simple-update-notifier cache-miss
262 http fetch GET 200 https://registry.npmjs.org/simple-update-notifier 25ms (cache miss)
263 silly packumentCache full:https://registry.npmjs.org/simple-update-notifier set size:undefined disposed:false
264 silly fetch manifest process@^0.11.1
265 silly packumentCache full:https://registry.npmjs.org/process cache-miss
266 http fetch GET 200 https://registry.npmjs.org/process 23ms (cache miss)
267 silly packumentCache full:https://registry.npmjs.org/process set size:undefined disposed:false
268 silly fetch manifest util@^0.10.3
269 silly packumentCache full:https://registry.npmjs.org/util cache-miss
270 http fetch GET 200 https://registry.npmjs.org/util 27ms (cache miss)
271 silly packumentCache full:https://registry.npmjs.org/util set size:undefined disposed:false
272 silly placeDep ROOT @discordjs/formatters@0.6.1 OK for: @discordjs/builders@1.11.2 want: ^0.6.1
273 silly placeDep ROOT @discordjs/util@1.1.1 OK for: @discordjs/builders@1.11.2 want: ^1.1.1
274 silly placeDep ROOT @sapphire/shapeshift@4.0.0 OK for: @discordjs/builders@1.11.2 want: ^4.0.0
275 silly placeDep ROOT discord-api-types@0.38.17 OK for: @discordjs/builders@1.11.2 want: ^0.38.1
276 silly placeDep ROOT fast-deep-equal@3.1.3 OK for: @discordjs/builders@1.11.2 want: ^3.1.3
277 silly placeDep ROOT ts-mixer@6.0.4 OK for: @discordjs/builders@1.11.2 want: ^6.0.4
278 silly placeDep ROOT tslib@2.8.1 OK for: @discordjs/builders@1.11.2 want: ^2.6.3
279 silly fetch manifest lodash@^4.17.21
280 silly packumentCache full:https://registry.npmjs.org/lodash cache-miss
281 http fetch GET 200 https://registry.npmjs.org/lodash 26ms (cache miss)
282 silly packumentCache full:https://registry.npmjs.org/lodash set size:undefined disposed:false
283 silly placeDep ROOT @discordjs/collection@2.1.1 OK for: @discordjs/rest@2.5.1 want: ^2.1.1
284 silly placeDep ROOT @sapphire/async-queue@1.5.5 OK for: @discordjs/rest@2.5.1 want: ^1.5.3
285 silly placeDep ROOT @sapphire/snowflake@3.5.5 OK for: @discordjs/rest@2.5.1 want: ^3.5.3
286 silly placeDep ROOT @vladfrangu/async_event_emitter@2.4.6 OK for: @discordjs/rest@2.5.1 want: ^2.4.6
287 silly placeDep ROOT magic-bytes.js@1.12.1 OK for: @discordjs/rest@2.5.1 want: ^1.10.0
288 silly placeDep ROOT undici@6.21.3 OK for: @discordjs/rest@2.5.1 want: 6.21.3
289 silly fetch manifest bufferutil@^4.0.1
290 silly packumentCache full:https://registry.npmjs.org/bufferutil cache-miss
291 http fetch GET 200 https://registry.npmjs.org/bufferutil 22ms (cache miss)
292 silly packumentCache full:https://registry.npmjs.org/bufferutil set size:undefined disposed:false
293 silly fetch manifest utf-8-validate@>=5.0.2
294 silly packumentCache full:https://registry.npmjs.org/utf-8-validate cache-miss
295 http fetch GET 200 https://registry.npmjs.org/utf-8-validate 21ms (cache miss)
296 silly packumentCache full:https://registry.npmjs.org/utf-8-validate set size:undefined disposed:false
297 silly fetch manifest @discordjs/opus@>=0.8.0 <1.0.0
298 silly packumentCache full:https://registry.npmjs.org/@discordjs%2fopus cache-miss
299 http fetch GET 200 https://registry.npmjs.org/@discordjs%2fopus 191ms (cache miss)
300 silly packumentCache full:https://registry.npmjs.org/@discordjs%2fopus set size:undefined disposed:false
301 silly fetch manifest ffmpeg-static@^5.0.2 || ^4.2.7 || ^3.0.0 || ^2.4.0
302 silly packumentCache full:https://registry.npmjs.org/ffmpeg-static cache-miss
303 http fetch GET 200 https://registry.npmjs.org/ffmpeg-static 44ms (cache miss)
304 silly packumentCache full:https://registry.npmjs.org/ffmpeg-static set size:undefined disposed:false
305 silly fetch manifest node-opus@^0.3.3
306 silly packumentCache full:https://registry.npmjs.org/node-opus cache-miss
307 http fetch GET 200 https://registry.npmjs.org/node-opus 102ms (cache miss)
308 silly packumentCache full:https://registry.npmjs.org/node-opus set size:undefined disposed:false
309 silly fetch manifest opusscript@^0.0.8
310 silly packumentCache full:https://registry.npmjs.org/opusscript cache-miss
311 http fetch GET 200 https://registry.npmjs.org/opusscript 51ms (cache miss)
312 silly packumentCache full:https://registry.npmjs.org/opusscript set size:undefined disposed:false
313 silly placeDep ROOT @types/ws@8.18.1 OK for: @discordjs/voice@0.18.0 want: ^8.5.12
314 silly placeDep node_modules/@discordjs/voice discord-api-types@0.37.120 OK for: @discordjs/voice@0.18.0 want: ^0.37.103
315 silly placeDep ROOT prism-media@1.3.5 OK for: @discordjs/voice@0.18.0 want: ^1.3.5
316 silly placeDep ROOT ws@8.18.3 OK for: @discordjs/voice@0.18.0 want: ^8.18.0
317 silly placeDep ROOT lodash@4.17.21 OK for: @sapphire/shapeshift@4.0.0 want: ^4.17.21
318 silly placeDep ROOT @types/node@24.1.0 OK for: @types/cors@2.8.17 want: *
319 silly fetch manifest undici-types@~7.8.0
320 silly packumentCache full:https://registry.npmjs.org/undici-types cache-miss
321 http fetch GET 200 https://registry.npmjs.org/undici-types 28ms (cache miss)
322 silly packumentCache full:https://registry.npmjs.org/undici-types set size:undefined disposed:false
323 silly placeDep ROOT @types/body-parser@1.19.6 OK for: @types/express@5.0.0 want: *
324 silly placeDep ROOT @types/express-serve-static-core@5.0.7 OK for: @types/express@5.0.0 want: ^5.0.0
325 silly placeDep ROOT @types/qs@6.14.0 OK for: @types/express@5.0.0 want: *
326 silly placeDep ROOT @types/serve-static@1.15.8 OK for: @types/express@5.0.0 want: *
327 silly fetch manifest @types/connect@*
328 silly packumentCache full:https://registry.npmjs.org/@types%2fconnect cache-miss
329 http fetch GET 200 https://registry.npmjs.org/@types%2fconnect 32ms (cache miss)
330 silly packumentCache full:https://registry.npmjs.org/@types%2fconnect set size:undefined disposed:false
331 silly fetch manifest @types/send@*
332 silly packumentCache full:https://registry.npmjs.org/@types%2fsend cache-miss
333 http fetch GET 200 https://registry.npmjs.org/@types%2fsend 31ms (cache miss)
334 silly packumentCache full:https://registry.npmjs.org/@types%2fsend set size:undefined disposed:false
335 silly fetch manifest @types/range-parser@*
336 silly packumentCache full:https://registry.npmjs.org/@types%2frange-parser cache-miss
337 http fetch GET 200 https://registry.npmjs.org/@types%2frange-parser 34ms (cache miss)
338 silly packumentCache full:https://registry.npmjs.org/@types%2frange-parser set size:undefined disposed:false
339 silly fetch manifest @types/http-errors@*
340 silly packumentCache full:https://registry.npmjs.org/@types%2fhttp-errors cache-miss
341 http fetch GET 200 https://registry.npmjs.org/@types%2fhttp-errors 41ms (cache miss)
342 silly packumentCache full:https://registry.npmjs.org/@types%2fhttp-errors set size:undefined disposed:false
343 silly placeDep ROOT @types/connect@3.4.38 OK for: @types/body-parser@1.19.6 want: *
344 silly placeDep ROOT @types/range-parser@1.2.7 OK for: @types/express-serve-static-core@5.0.7 want: *
345 silly placeDep ROOT @types/send@0.17.5 OK for: @types/express-serve-static-core@5.0.7 want: *
346 silly fetch manifest @types/mime@^1
347 silly packumentCache full:https://registry.npmjs.org/@types%2fmime cache-miss
348 http fetch GET 200 https://registry.npmjs.org/@types%2fmime 34ms (cache miss)
349 silly packumentCache full:https://registry.npmjs.org/@types%2fmime set size:undefined disposed:false
350 silly placeDep ROOT undici-types@7.8.0 OK for: @types/node@24.1.0 want: ~7.8.0
351 silly placeDep ROOT @types/mime@1.3.5 OK for: @types/send@0.17.5 want: ^1
352 silly placeDep ROOT @types/http-errors@2.0.5 OK for: @types/serve-static@1.15.8 want: *
353 silly placeDep ROOT bindings@1.5.0 OK for: better-sqlite3@12.2.0 want: ^1.5.0
354 silly placeDep ROOT prebuild-install@7.1.3 OK for: better-sqlite3@12.2.0 want: ^7.1.1
355 silly fetch manifest file-uri-to-path@1.0.0
356 silly packumentCache full:https://registry.npmjs.org/file-uri-to-path cache-miss
357 http fetch GET 200 https://registry.npmjs.org/file-uri-to-path 23ms (cache miss)
358 silly packumentCache full:https://registry.npmjs.org/file-uri-to-path set size:undefined disposed:false
359 silly fetch manifest detect-libc@^2.0.0
360 silly packumentCache full:https://registry.npmjs.org/detect-libc cache-miss
361 http fetch GET 200 https://registry.npmjs.org/detect-libc 24ms (cache miss)
362 silly packumentCache full:https://registry.npmjs.org/detect-libc set size:undefined disposed:false
363 silly fetch manifest expand-template@^2.0.3
364 silly packumentCache full:https://registry.npmjs.org/expand-template cache-miss
365 http fetch GET 200 https://registry.npmjs.org/expand-template 36ms (cache miss)
366 silly packumentCache full:https://registry.npmjs.org/expand-template set size:undefined disposed:false
367 silly fetch manifest github-from-package@0.0.0
368 silly packumentCache full:https://registry.npmjs.org/github-from-package cache-miss
369 http fetch GET 200 https://registry.npmjs.org/github-from-package 89ms (cache miss)
370 silly packumentCache full:https://registry.npmjs.org/github-from-package set size:undefined disposed:false
371 silly fetch manifest minimist@^1.2.3
372 silly packumentCache full:https://registry.npmjs.org/minimist cache-miss
373 http fetch GET 200 https://registry.npmjs.org/minimist 27ms (cache miss)
374 silly packumentCache full:https://registry.npmjs.org/minimist set size:undefined disposed:false
375 silly fetch manifest mkdirp-classic@^0.5.3
376 silly packumentCache full:https://registry.npmjs.org/mkdirp-classic cache-miss
377 http fetch GET 200 https://registry.npmjs.org/mkdirp-classic 44ms (cache miss)
378 silly packumentCache full:https://registry.npmjs.org/mkdirp-classic set size:undefined disposed:false
379 silly fetch manifest napi-build-utils@^2.0.0
380 silly packumentCache full:https://registry.npmjs.org/napi-build-utils cache-miss
381 http fetch GET 200 https://registry.npmjs.org/napi-build-utils 32ms (cache miss)
382 silly packumentCache full:https://registry.npmjs.org/napi-build-utils set size:undefined disposed:false
383 silly fetch manifest node-abi@^3.3.0
384 silly packumentCache full:https://registry.npmjs.org/node-abi cache-miss
385 http fetch GET 200 https://registry.npmjs.org/node-abi 54ms (cache miss)
386 silly packumentCache full:https://registry.npmjs.org/node-abi set size:undefined disposed:false
387 silly fetch manifest pump@^3.0.0
388 silly packumentCache full:https://registry.npmjs.org/pump cache-miss
389 http fetch GET 200 https://registry.npmjs.org/pump 24ms (cache miss)
390 silly packumentCache full:https://registry.npmjs.org/pump set size:undefined disposed:false
391 silly fetch manifest rc@^1.2.7
392 silly packumentCache full:https://registry.npmjs.org/rc cache-miss
393 http fetch GET 200 https://registry.npmjs.org/rc 25ms (cache miss)
394 silly packumentCache full:https://registry.npmjs.org/rc set size:undefined disposed:false
395 silly fetch manifest simple-get@^4.0.0
396 silly packumentCache full:https://registry.npmjs.org/simple-get cache-miss
397 http fetch GET 200 https://registry.npmjs.org/simple-get 25ms (cache miss)
398 silly packumentCache full:https://registry.npmjs.org/simple-get set size:undefined disposed:false
399 silly fetch manifest tar-fs@^2.0.0
400 silly packumentCache full:https://registry.npmjs.org/tar-fs cache-miss
401 http fetch GET 200 https://registry.npmjs.org/tar-fs 24ms (cache miss)
402 silly packumentCache full:https://registry.npmjs.org/tar-fs set size:undefined disposed:false
403 silly fetch manifest tunnel-agent@^0.6.0
404 silly packumentCache full:https://registry.npmjs.org/tunnel-agent cache-miss
405 http fetch GET 200 https://registry.npmjs.org/tunnel-agent 21ms (cache miss)
406 silly packumentCache full:https://registry.npmjs.org/tunnel-agent set size:undefined disposed:false
407 silly placeDep ROOT file-uri-to-path@1.0.0 OK for: bindings@1.5.0 want: 1.0.0
408 silly placeDep ROOT cross-spawn@7.0.6 OK for: cross-env@7.0.3 want: ^7.0.1
409 silly fetch manifest which@^2.0.1
410 silly packumentCache full:https://registry.npmjs.org/which cache-miss
411 http fetch GET 200 https://registry.npmjs.org/which 21ms (cache miss)
412 silly packumentCache full:https://registry.npmjs.org/which set size:undefined disposed:false
413 silly fetch manifest path-key@^3.1.0
414 silly packumentCache full:https://registry.npmjs.org/path-key cache-miss
415 http fetch GET 200 https://registry.npmjs.org/path-key 29ms (cache miss)
416 silly packumentCache full:https://registry.npmjs.org/path-key set size:undefined disposed:false
417 silly fetch manifest shebang-command@^2.0.0
418 silly packumentCache full:https://registry.npmjs.org/shebang-command cache-miss
419 http fetch GET 200 https://registry.npmjs.org/shebang-command 20ms (cache miss)
420 silly packumentCache full:https://registry.npmjs.org/shebang-command set size:undefined disposed:false
421 silly placeDep ROOT path-key@3.1.1 OK for: cross-spawn@7.0.6 want: ^3.1.0
422 silly placeDep ROOT shebang-command@2.0.0 OK for: cross-spawn@7.0.6 want: ^2.0.0
423 silly placeDep ROOT which@2.0.2 OK for: cross-spawn@7.0.6 want: ^2.0.1
424 silly fetch manifest shebang-regex@^3.0.0
425 silly packumentCache full:https://registry.npmjs.org/shebang-regex cache-miss
426 http fetch GET 200 https://registry.npmjs.org/shebang-regex 23ms (cache miss)
427 silly packumentCache full:https://registry.npmjs.org/shebang-regex set size:undefined disposed:false
428 silly fetch manifest isexe@^2.0.0
429 silly packumentCache full:https://registry.npmjs.org/isexe cache-miss
430 http fetch GET 200 https://registry.npmjs.org/isexe 25ms (cache miss)
431 silly packumentCache full:https://registry.npmjs.org/isexe set size:undefined disposed:false
432 silly placeDep node_modules/discord.js @discordjs/collection@1.5.3 OK for: discord.js@14.21.0 want: 1.5.3
433 silly placeDep ROOT @discordjs/ws@1.2.3 OK for: discord.js@14.21.0 want: ^1.2.3
434 silly placeDep node_modules/discord.js @sapphire/snowflake@3.5.3 OK for: discord.js@14.21.0 want: 3.5.3
435 silly placeDep ROOT lodash.snakecase@4.1.1 OK for: discord.js@14.21.0 want: 4.1.1
436 silly placeDep ROOT @jest/core@29.7.0 OK for: jest@29.7.0 want: ^29.7.0
437 silly placeDep ROOT @jest/types@29.6.3 OK for: jest@29.7.0 want: ^29.6.3
438 silly placeDep ROOT import-local@3.2.0 OK for: jest@29.7.0 want: ^3.0.2
439 silly placeDep ROOT jest-cli@29.7.0 OK for: jest@29.7.0 want: ^29.7.0
440 silly fetch manifest exit@^0.1.2
441 silly packumentCache full:https://registry.npmjs.org/exit cache-miss
442 http fetch GET 200 https://registry.npmjs.org/exit 22ms (cache miss)
443 silly packumentCache full:https://registry.npmjs.org/exit set size:undefined disposed:false
444 silly fetch manifest chalk@^4.0.0
445 silly packumentCache full:https://registry.npmjs.org/chalk cache-miss
446 http fetch GET 200 https://registry.npmjs.org/chalk 24ms (cache miss)
447 silly packumentCache full:https://registry.npmjs.org/chalk set size:undefined disposed:false
448 silly fetch manifest slash@^3.0.0
449 silly packumentCache full:https://registry.npmjs.org/slash cache-miss
450 http fetch GET 200 https://registry.npmjs.org/slash 20ms (cache miss)
451 silly packumentCache full:https://registry.npmjs.org/slash set size:undefined disposed:false
452 silly fetch manifest ci-info@^3.2.0
453 silly packumentCache full:https://registry.npmjs.org/ci-info cache-miss
454 http fetch GET 200 https://registry.npmjs.org/ci-info 19ms (cache miss)
455 silly packumentCache full:https://registry.npmjs.org/ci-info set size:undefined disposed:false
456 silly fetch manifest jest-util@^29.7.0
457 silly packumentCache full:https://registry.npmjs.org/jest-util cache-miss
458 http fetch GET 200 https://registry.npmjs.org/jest-util 39ms (cache miss)
459 silly packumentCache full:https://registry.npmjs.org/jest-util set size:undefined disposed:false
460 silly fetch manifest micromatch@^4.0.4
461 silly packumentCache full:https://registry.npmjs.org/micromatch cache-miss
462 http fetch GET 200 https://registry.npmjs.org/micromatch 22ms (cache miss)
463 silly packumentCache full:https://registry.npmjs.org/micromatch set size:undefined disposed:false
464 silly fetch manifest strip-ansi@^6.0.0
465 silly packumentCache full:https://registry.npmjs.org/strip-ansi cache-miss
466 http fetch GET 200 https://registry.npmjs.org/strip-ansi 27ms (cache miss)
467 silly packumentCache full:https://registry.npmjs.org/strip-ansi set size:undefined disposed:false
468 silly fetch manifest graceful-fs@^4.2.9
469 silly packumentCache full:https://registry.npmjs.org/graceful-fs cache-miss
470 http fetch GET 200 https://registry.npmjs.org/graceful-fs 24ms (cache miss)
471 silly packumentCache full:https://registry.npmjs.org/graceful-fs set size:undefined disposed:false
472 silly fetch manifest jest-config@^29.7.0
473 silly packumentCache full:https://registry.npmjs.org/jest-config cache-miss
474 http fetch GET 200 https://registry.npmjs.org/jest-config 40ms (cache miss)
475 silly packumentCache full:https://registry.npmjs.org/jest-config set size:undefined disposed:false
476 silly fetch manifest jest-runner@^29.7.0
477 silly packumentCache full:https://registry.npmjs.org/jest-runner cache-miss
478 http fetch GET 200 https://registry.npmjs.org/jest-runner 28ms (cache miss)
479 silly packumentCache full:https://registry.npmjs.org/jest-runner set size:undefined disposed:false
480 silly fetch manifest ansi-escapes@^4.2.1
481 silly packumentCache full:https://registry.npmjs.org/ansi-escapes cache-miss
482 http fetch GET 200 https://registry.npmjs.org/ansi-escapes 25ms (cache miss)
483 silly packumentCache full:https://registry.npmjs.org/ansi-escapes set size:undefined disposed:false
484 silly fetch manifest jest-resolve@^29.7.0
485 silly packumentCache full:https://registry.npmjs.org/jest-resolve cache-miss
486 http fetch GET 200 https://registry.npmjs.org/jest-resolve 52ms (cache miss)
487 silly packumentCache full:https://registry.npmjs.org/jest-resolve set size:undefined disposed:false
488 silly fetch manifest jest-runtime@^29.7.0
489 silly packumentCache full:https://registry.npmjs.org/jest-runtime cache-miss
490 http fetch GET 200 https://registry.npmjs.org/jest-runtime 32ms (cache miss)
491 silly packumentCache full:https://registry.npmjs.org/jest-runtime set size:undefined disposed:false
492 silly fetch manifest jest-watcher@^29.7.0
493 silly packumentCache full:https://registry.npmjs.org/jest-watcher cache-miss
494 http fetch GET 200 https://registry.npmjs.org/jest-watcher 48ms (cache miss)
495 silly packumentCache full:https://registry.npmjs.org/jest-watcher set size:undefined disposed:false
496 silly fetch manifest @jest/console@^29.7.0
497 silly packumentCache full:https://registry.npmjs.org/@jest%2fconsole cache-miss
498 http fetch GET 200 https://registry.npmjs.org/@jest%2fconsole 57ms (cache miss)
499 silly packumentCache full:https://registry.npmjs.org/@jest%2fconsole set size:undefined disposed:false
500 silly fetch manifest jest-snapshot@^29.7.0
501 silly packumentCache full:https://registry.npmjs.org/jest-snapshot cache-miss
502 http fetch GET 200 https://registry.npmjs.org/jest-snapshot 31ms (cache miss)
503 silly packumentCache full:https://registry.npmjs.org/jest-snapshot set size:undefined disposed:false
504 silly fetch manifest jest-validate@^29.7.0
505 silly packumentCache full:https://registry.npmjs.org/jest-validate cache-miss
506 http fetch GET 200 https://registry.npmjs.org/jest-validate 38ms (cache miss)
507 silly packumentCache full:https://registry.npmjs.org/jest-validate set size:undefined disposed:false
508 silly fetch manifest pretty-format@^29.7.0
509 silly packumentCache full:https://registry.npmjs.org/pretty-format cache-miss
510 http fetch GET 200 https://registry.npmjs.org/pretty-format 31ms (cache miss)
511 silly packumentCache full:https://registry.npmjs.org/pretty-format set size:undefined disposed:false
512 silly fetch manifest jest-haste-map@^29.7.0
513 silly packumentCache full:https://registry.npmjs.org/jest-haste-map cache-miss
514 http fetch GET 200 https://registry.npmjs.org/jest-haste-map 31ms (cache miss)
515 silly packumentCache full:https://registry.npmjs.org/jest-haste-map set size:undefined disposed:false
516 silly fetch manifest @jest/reporters@^29.7.0
517 silly packumentCache full:https://registry.npmjs.org/@jest%2freporters cache-miss
518 http fetch GET 200 https://registry.npmjs.org/@jest%2freporters 53ms (cache miss)
519 silly packumentCache full:https://registry.npmjs.org/@jest%2freporters set size:undefined disposed:false
520 silly fetch manifest @jest/transform@^29.7.0
521 silly packumentCache full:https://registry.npmjs.org/@jest%2ftransform cache-miss
522 http fetch GET 200 https://registry.npmjs.org/@jest%2ftransform 50ms (cache miss)
523 silly packumentCache full:https://registry.npmjs.org/@jest%2ftransform set size:undefined disposed:false
524 silly fetch manifest jest-regex-util@^29.6.3
525 silly packumentCache full:https://registry.npmjs.org/jest-regex-util cache-miss
526 http fetch GET 200 https://registry.npmjs.org/jest-regex-util 34ms (cache miss)
527 silly packumentCache full:https://registry.npmjs.org/jest-regex-util set size:undefined disposed:false
528 silly fetch manifest @jest/test-result@^29.7.0
529 silly packumentCache full:https://registry.npmjs.org/@jest%2ftest-result cache-miss
530 http fetch GET 200 https://registry.npmjs.org/@jest%2ftest-result 50ms (cache miss)
531 silly packumentCache full:https://registry.npmjs.org/@jest%2ftest-result set size:undefined disposed:false
532 silly fetch manifest jest-message-util@^29.7.0
533 silly packumentCache full:https://registry.npmjs.org/jest-message-util cache-miss
534 http fetch GET 200 https://registry.npmjs.org/jest-message-util 30ms (cache miss)
535 silly packumentCache full:https://registry.npmjs.org/jest-message-util set size:undefined disposed:false
536 silly fetch manifest jest-changed-files@^29.7.0
537 silly packumentCache full:https://registry.npmjs.org/jest-changed-files cache-miss
538 http fetch GET 200 https://registry.npmjs.org/jest-changed-files 43ms (cache miss)
539 silly packumentCache full:https://registry.npmjs.org/jest-changed-files set size:undefined disposed:false
540 silly fetch manifest jest-resolve-dependencies@^29.7.0
541 silly packumentCache full:https://registry.npmjs.org/jest-resolve-dependencies cache-miss
542 http fetch GET 200 https://registry.npmjs.org/jest-resolve-dependencies 46ms (cache miss)
543 silly packumentCache full:https://registry.npmjs.org/jest-resolve-dependencies set size:undefined disposed:false
544 silly fetch manifest @types/yargs@^17.0.8
545 silly packumentCache full:https://registry.npmjs.org/@types%2fyargs cache-miss
546 http fetch GET 200 https://registry.npmjs.org/@types%2fyargs 49ms (cache miss)
547 silly packumentCache full:https://registry.npmjs.org/@types%2fyargs set size:undefined disposed:false
548 silly fetch manifest @jest/schemas@^29.6.3
549 silly packumentCache full:https://registry.npmjs.org/@jest%2fschemas cache-miss
550 http fetch GET 200 https://registry.npmjs.org/@jest%2fschemas 40ms (cache miss)
551 silly packumentCache full:https://registry.npmjs.org/@jest%2fschemas set size:undefined disposed:false
552 silly fetch manifest @types/istanbul-reports@^3.0.0
553 silly packumentCache full:https://registry.npmjs.org/@types%2fistanbul-reports cache-miss
554 http fetch GET 200 https://registry.npmjs.org/@types%2fistanbul-reports 36ms (cache miss)
555 silly packumentCache full:https://registry.npmjs.org/@types%2fistanbul-reports set size:undefined disposed:false
556 silly fetch manifest @types/istanbul-lib-coverage@^2.0.0
557 silly packumentCache full:https://registry.npmjs.org/@types%2fistanbul-lib-coverage cache-miss
558 http fetch GET 200 https://registry.npmjs.org/@types%2fistanbul-lib-coverage 35ms (cache miss)
559 silly packumentCache full:https://registry.npmjs.org/@types%2fistanbul-lib-coverage set size:undefined disposed:false
560 silly fetch manifest pkg-dir@^4.2.0
561 silly packumentCache full:https://registry.npmjs.org/pkg-dir cache-miss
562 http fetch GET 200 https://registry.npmjs.org/pkg-dir 25ms (cache miss)
563 silly packumentCache full:https://registry.npmjs.org/pkg-dir set size:undefined disposed:false
564 silly fetch manifest resolve-cwd@^3.0.0
565 silly packumentCache full:https://registry.npmjs.org/resolve-cwd cache-miss
566 http fetch GET 200 https://registry.npmjs.org/resolve-cwd 33ms (cache miss)
567 silly packumentCache full:https://registry.npmjs.org/resolve-cwd set size:undefined disposed:false
568 silly fetch manifest yargs@^17.3.1
569 silly packumentCache full:https://registry.npmjs.org/yargs cache-miss
570 http fetch GET 200 https://registry.npmjs.org/yargs 37ms (cache miss)
571 silly packumentCache full:https://registry.npmjs.org/yargs set size:undefined disposed:false
572 silly fetch manifest create-jest@^29.7.0
573 silly packumentCache full:https://registry.npmjs.org/create-jest cache-miss
574 http fetch GET 200 https://registry.npmjs.org/create-jest 36ms (cache miss)
575 silly packumentCache full:https://registry.npmjs.org/create-jest set size:undefined disposed:false
576 silly fetch manifest ts-node@>=9.0.0
577 silly packumentCache full:https://registry.npmjs.org/ts-node cache-miss
578 http fetch GET 200 https://registry.npmjs.org/ts-node 40ms (cache miss)
579 silly packumentCache full:https://registry.npmjs.org/ts-node set size:undefined disposed:false
580 silly fetch manifest @swc/core@>=1.2.50
581 silly packumentCache full:https://registry.npmjs.org/@swc%2fcore cache-miss
582 http fetch GET 200 https://registry.npmjs.org/@swc%2fcore 70ms (cache miss)
583 silly packumentCache full:https://registry.npmjs.org/@swc%2fcore set size:undefined disposed:false
584 silly fetch manifest @swc/helpers@>=0.5.17
585 silly packumentCache full:https://registry.npmjs.org/@swc%2fhelpers cache-miss
586 http fetch GET 200 https://registry.npmjs.org/@swc%2fhelpers 96ms (cache miss)
587 silly packumentCache full:https://registry.npmjs.org/@swc%2fhelpers set size:undefined disposed:false
588 silly fetch manifest @swc/wasm@>=1.2.50
589 silly packumentCache full:https://registry.npmjs.org/@swc%2fwasm cache-miss
590 http fetch GET 200 https://registry.npmjs.org/@swc%2fwasm 60ms (cache miss)
591 silly packumentCache full:https://registry.npmjs.org/@swc%2fwasm set size:undefined disposed:false
592 silly fetch manifest typescript@>=2.7
593 silly packumentCache full:https://registry.npmjs.org/typescript cache-miss
594 http cache https://registry.npmjs.org/typescript 33ms (cache hit)
595 silly packumentCache full:https://registry.npmjs.org/typescript set size:14471082 disposed:false
596 silly placeDep ROOT @jest/console@29.7.0 OK for: @jest/core@29.7.0 want: ^29.7.0
597 silly placeDep ROOT @jest/reporters@29.7.0 OK for: @jest/core@29.7.0 want: ^29.7.0
598 silly placeDep ROOT @jest/test-result@29.7.0 OK for: @jest/core@29.7.0 want: ^29.7.0
599 silly placeDep ROOT @jest/transform@29.7.0 OK for: @jest/core@29.7.0 want: ^29.7.0
600 silly placeDep ROOT ansi-escapes@4.3.2 OK for: @jest/core@29.7.0 want: ^4.2.1
601 silly placeDep ROOT chalk@4.1.2 OK for: @jest/core@29.7.0 want: ^4.0.0
602 silly placeDep ROOT ci-info@3.9.0 OK for: @jest/core@29.7.0 want: ^3.2.0
603 silly placeDep ROOT exit@0.1.2 OK for: @jest/core@29.7.0 want: ^0.1.2
604 silly placeDep ROOT graceful-fs@4.2.11 OK for: @jest/core@29.7.0 want: ^4.2.9
605 silly placeDep ROOT jest-changed-files@29.7.0 OK for: @jest/core@29.7.0 want: ^29.7.0
606 silly placeDep ROOT jest-config@29.7.0 OK for: @jest/core@29.7.0 want: ^29.7.0
607 silly placeDep ROOT jest-haste-map@29.7.0 OK for: @jest/core@29.7.0 want: ^29.7.0
608 silly placeDep ROOT jest-message-util@29.7.0 OK for: @jest/core@29.7.0 want: ^29.7.0
609 silly placeDep ROOT jest-regex-util@29.6.3 OK for: @jest/core@29.7.0 want: ^29.6.3
610 silly placeDep ROOT jest-resolve@29.7.0 OK for: @jest/core@29.7.0 want: ^29.7.0
611 silly placeDep ROOT jest-resolve-dependencies@29.7.0 OK for: @jest/core@29.7.0 want: ^29.7.0
612 silly placeDep ROOT jest-runner@29.7.0 OK for: @jest/core@29.7.0 want: ^29.7.0
613 silly placeDep ROOT jest-runtime@29.7.0 OK for: @jest/core@29.7.0 want: ^29.7.0
614 silly placeDep ROOT jest-snapshot@29.7.0 OK for: @jest/core@29.7.0 want: ^29.7.0
615 silly placeDep ROOT jest-util@29.7.0 OK for: @jest/core@29.7.0 want: ^29.7.0
616 silly placeDep ROOT jest-validate@29.7.0 OK for: @jest/core@29.7.0 want: ^29.7.0
617 silly placeDep ROOT jest-watcher@29.7.0 OK for: @jest/core@29.7.0 want: ^29.7.0
618 silly placeDep ROOT micromatch@4.0.8 OK for: @jest/core@29.7.0 want: ^4.0.4
619 silly placeDep ROOT pretty-format@29.7.0 OK for: @jest/core@29.7.0 want: ^29.7.0
620 silly placeDep ROOT slash@3.0.0 OK for: @jest/core@29.7.0 want: ^3.0.0
621 silly placeDep ROOT strip-ansi@6.0.1 OK for: @jest/core@29.7.0 want: ^6.0.0
622 silly fetch manifest glob@^7.1.3
623 silly packumentCache full:https://registry.npmjs.org/glob cache-miss
624 http fetch GET 200 https://registry.npmjs.org/glob 27ms (cache miss)
625 silly packumentCache full:https://registry.npmjs.org/glob set size:undefined disposed:false
626 silly fetch manifest jest-worker@^29.7.0
627 silly packumentCache full:https://registry.npmjs.org/jest-worker cache-miss
628 http fetch GET 200 https://registry.npmjs.org/jest-worker 39ms (cache miss)
629 silly packumentCache full:https://registry.npmjs.org/jest-worker set size:undefined disposed:false
630 silly fetch manifest string-length@^4.0.1
631 silly packumentCache full:https://registry.npmjs.org/string-length cache-miss
632 http fetch GET 200 https://registry.npmjs.org/string-length 24ms (cache miss)
633 silly packumentCache full:https://registry.npmjs.org/string-length set size:undefined disposed:false
634 silly fetch manifest v8-to-istanbul@^9.0.1
635 silly packumentCache full:https://registry.npmjs.org/v8-to-istanbul cache-miss
636 http fetch GET 200 https://registry.npmjs.org/v8-to-istanbul 23ms (cache miss)
637 silly packumentCache full:https://registry.npmjs.org/v8-to-istanbul set size:undefined disposed:false
638 silly fetch manifest istanbul-reports@^3.1.3
639 silly packumentCache full:https://registry.npmjs.org/istanbul-reports cache-miss
640 http fetch GET 200 https://registry.npmjs.org/istanbul-reports 23ms (cache miss)
641 silly packumentCache full:https://registry.npmjs.org/istanbul-reports set size:undefined disposed:false
642 silly fetch manifest @bcoe/v8-coverage@^0.2.3
643 silly packumentCache full:https://registry.npmjs.org/@bcoe%2fv8-coverage cache-miss
644 http fetch GET 200 https://registry.npmjs.org/@bcoe%2fv8-coverage 33ms (cache miss)
645 silly packumentCache full:https://registry.npmjs.org/@bcoe%2fv8-coverage set size:undefined disposed:false
646 silly fetch manifest collect-v8-coverage@^1.0.0
647 silly packumentCache full:https://registry.npmjs.org/collect-v8-coverage cache-miss
648 http fetch GET 200 https://registry.npmjs.org/collect-v8-coverage 33ms (cache miss)
649 silly packumentCache full:https://registry.npmjs.org/collect-v8-coverage set size:undefined disposed:false
650 silly fetch manifest istanbul-lib-report@^3.0.0
651 silly packumentCache full:https://registry.npmjs.org/istanbul-lib-report cache-miss
652 http fetch GET 200 https://registry.npmjs.org/istanbul-lib-report 24ms (cache miss)
653 silly packumentCache full:https://registry.npmjs.org/istanbul-lib-report set size:undefined disposed:false
654 silly fetch manifest istanbul-lib-coverage@^3.0.0
655 silly packumentCache full:https://registry.npmjs.org/istanbul-lib-coverage cache-miss
656 http fetch GET 200 https://registry.npmjs.org/istanbul-lib-coverage 20ms (cache miss)
657 silly packumentCache full:https://registry.npmjs.org/istanbul-lib-coverage set size:undefined disposed:false
658 silly fetch manifest istanbul-lib-instrument@^6.0.0
659 silly packumentCache full:https://registry.npmjs.org/istanbul-lib-instrument cache-miss
660 http fetch GET 200 https://registry.npmjs.org/istanbul-lib-instrument 28ms (cache miss)
661 silly packumentCache full:https://registry.npmjs.org/istanbul-lib-instrument set size:undefined disposed:false
662 silly fetch manifest istanbul-lib-source-maps@^4.0.0
663 silly packumentCache full:https://registry.npmjs.org/istanbul-lib-source-maps cache-miss
664 http fetch GET 200 https://registry.npmjs.org/istanbul-lib-source-maps 42ms (cache miss)
665 silly packumentCache full:https://registry.npmjs.org/istanbul-lib-source-maps set size:undefined disposed:false
666 silly fetch manifest @jridgewell/trace-mapping@^0.3.18
667 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2ftrace-mapping cache-miss
668 http fetch GET 200 https://registry.npmjs.org/@jridgewell%2ftrace-mapping 54ms (cache miss)
669 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2ftrace-mapping set size:undefined disposed:false
670 silly fetch manifest pirates@^4.0.4
671 silly packumentCache full:https://registry.npmjs.org/pirates cache-miss
672 http fetch GET 200 https://registry.npmjs.org/pirates 21ms (cache miss)
673 silly packumentCache full:https://registry.npmjs.org/pirates set size:undefined disposed:false
674 silly fetch manifest @babel/core@^7.11.6
675 silly packumentCache full:https://registry.npmjs.org/@babel%2fcore cache-miss
676 http fetch GET 200 https://registry.npmjs.org/@babel%2fcore 47ms (cache miss)
677 silly packumentCache full:https://registry.npmjs.org/@babel%2fcore set size:undefined disposed:false
678 silly fetch manifest write-file-atomic@^4.0.2
679 silly packumentCache full:https://registry.npmjs.org/write-file-atomic cache-miss
680 http fetch GET 200 https://registry.npmjs.org/write-file-atomic 38ms (cache miss)
681 silly packumentCache full:https://registry.npmjs.org/write-file-atomic set size:undefined disposed:false
682 silly fetch manifest convert-source-map@^2.0.0
683 silly packumentCache full:https://registry.npmjs.org/convert-source-map cache-miss
684 http fetch GET 200 https://registry.npmjs.org/convert-source-map 26ms (cache miss)
685 silly packumentCache full:https://registry.npmjs.org/convert-source-map set size:undefined disposed:false
686 silly fetch manifest babel-plugin-istanbul@^6.1.1
687 silly packumentCache full:https://registry.npmjs.org/babel-plugin-istanbul cache-miss
688 http fetch GET 200 https://registry.npmjs.org/babel-plugin-istanbul 37ms (cache miss)
689 silly packumentCache full:https://registry.npmjs.org/babel-plugin-istanbul set size:undefined disposed:false
690 silly fetch manifest fast-json-stable-stringify@^2.1.0
691 silly packumentCache full:https://registry.npmjs.org/fast-json-stable-stringify cache-miss
692 http fetch GET 200 https://registry.npmjs.org/fast-json-stable-stringify 26ms (cache miss)
693 silly packumentCache full:https://registry.npmjs.org/fast-json-stable-stringify set size:undefined disposed:false
694 silly fetch manifest type-fest@^0.21.3
695 silly packumentCache full:https://registry.npmjs.org/type-fest cache-miss
696 http fetch GET 200 https://registry.npmjs.org/type-fest 33ms (cache miss)
697 silly packumentCache full:https://registry.npmjs.org/type-fest set size:undefined disposed:false
698 silly fetch manifest ansi-styles@^4.1.0
699 silly packumentCache full:https://registry.npmjs.org/ansi-styles cache-miss
700 http fetch GET 200 https://registry.npmjs.org/ansi-styles 31ms (cache miss)
701 silly packumentCache full:https://registry.npmjs.org/ansi-styles set size:undefined disposed:false
702 silly fetch manifest supports-color@^7.1.0
703 silly packumentCache full:https://registry.npmjs.org/supports-color cache-miss
704 http cache https://registry.npmjs.org/supports-color 3ms (cache hit)
705 silly packumentCache full:https://registry.npmjs.org/supports-color set size:115466 disposed:false
706 silly fetch manifest execa@^5.0.0
707 silly packumentCache full:https://registry.npmjs.org/execa cache-miss
708 http fetch GET 200 https://registry.npmjs.org/execa 24ms (cache miss)
709 silly packumentCache full:https://registry.npmjs.org/execa set size:undefined disposed:false
710 silly fetch manifest p-limit@^3.1.0
711 silly packumentCache full:https://registry.npmjs.org/p-limit cache-miss
712 http fetch GET 200 https://registry.npmjs.org/p-limit 24ms (cache miss)
713 silly packumentCache full:https://registry.npmjs.org/p-limit set size:undefined disposed:false
714 silly fetch manifest deepmerge@^4.2.2
715 silly packumentCache full:https://registry.npmjs.org/deepmerge cache-miss
716 http fetch GET 200 https://registry.npmjs.org/deepmerge 33ms (cache miss)
717 silly packumentCache full:https://registry.npmjs.org/deepmerge set size:undefined disposed:false
718 silly fetch manifest babel-jest@^29.7.0
719 silly packumentCache full:https://registry.npmjs.org/babel-jest cache-miss
720 http fetch GET 200 https://registry.npmjs.org/babel-jest 29ms (cache miss)
721 silly packumentCache full:https://registry.npmjs.org/babel-jest set size:undefined disposed:false
722 silly fetch manifest parse-json@^5.2.0
723 silly packumentCache full:https://registry.npmjs.org/parse-json cache-miss
724 http fetch GET 200 https://registry.npmjs.org/parse-json 31ms (cache miss)
725 silly packumentCache full:https://registry.npmjs.org/parse-json set size:undefined disposed:false
726 silly fetch manifest jest-circus@^29.7.0
727 silly packumentCache full:https://registry.npmjs.org/jest-circus cache-miss
728 http fetch GET 200 https://registry.npmjs.org/jest-circus 30ms (cache miss)
729 silly packumentCache full:https://registry.npmjs.org/jest-circus set size:undefined disposed:false
730 silly fetch manifest jest-get-type@^29.6.3
731 silly packumentCache full:https://registry.npmjs.org/jest-get-type cache-miss
732 http fetch GET 200 https://registry.npmjs.org/jest-get-type 26ms (cache miss)
733 silly packumentCache full:https://registry.npmjs.org/jest-get-type set size:undefined disposed:false
734 silly fetch manifest strip-json-comments@^3.1.1
735 silly packumentCache full:https://registry.npmjs.org/strip-json-comments cache-miss
736 http fetch GET 200 https://registry.npmjs.org/strip-json-comments 29ms (cache miss)
737 silly packumentCache full:https://registry.npmjs.org/strip-json-comments set size:undefined disposed:false
738 silly fetch manifest @jest/test-sequencer@^29.7.0
739 silly packumentCache full:https://registry.npmjs.org/@jest%2ftest-sequencer cache-miss
740 http fetch GET 200 https://registry.npmjs.org/@jest%2ftest-sequencer 40ms (cache miss)
741 silly packumentCache full:https://registry.npmjs.org/@jest%2ftest-sequencer set size:undefined disposed:false
742 silly fetch manifest jest-environment-node@^29.7.0
743 silly packumentCache full:https://registry.npmjs.org/jest-environment-node cache-miss
744 http fetch GET 200 https://registry.npmjs.org/jest-environment-node 32ms (cache miss)
745 silly packumentCache full:https://registry.npmjs.org/jest-environment-node set size:undefined disposed:false
746 silly fetch manifest walker@^1.0.8
747 silly packumentCache full:https://registry.npmjs.org/walker cache-miss
748 http fetch GET 200 https://registry.npmjs.org/walker 23ms (cache miss)
749 silly packumentCache full:https://registry.npmjs.org/walker set size:undefined disposed:false
750 silly fetch manifest anymatch@^3.0.3
751 silly packumentCache full:https://registry.npmjs.org/anymatch cache-miss
752 http fetch GET 200 https://registry.npmjs.org/anymatch 27ms (cache miss)
753 silly packumentCache full:https://registry.npmjs.org/anymatch set size:undefined disposed:false
754 silly fetch manifest fb-watchman@^2.0.0
755 silly packumentCache full:https://registry.npmjs.org/fb-watchman cache-miss
756 http fetch GET 200 https://registry.npmjs.org/fb-watchman 24ms (cache miss)
757 silly packumentCache full:https://registry.npmjs.org/fb-watchman set size:undefined disposed:false
758 silly fetch manifest @types/graceful-fs@^4.1.3
759 silly packumentCache full:https://registry.npmjs.org/@types%2fgraceful-fs cache-miss
760 http fetch GET 200 https://registry.npmjs.org/@types%2fgraceful-fs 33ms (cache miss)
761 silly packumentCache full:https://registry.npmjs.org/@types%2fgraceful-fs set size:undefined disposed:false
762 silly fetch manifest fsevents@^2.3.2
763 silly packumentCache full:https://registry.npmjs.org/fsevents cache-miss
764 http fetch GET 200 https://registry.npmjs.org/fsevents 23ms (cache miss)
765 silly packumentCache full:https://registry.npmjs.org/fsevents set size:undefined disposed:false
766 silly fetch manifest stack-utils@^2.0.3
767 silly packumentCache full:https://registry.npmjs.org/stack-utils cache-miss
768 http fetch GET 200 https://registry.npmjs.org/stack-utils 36ms (cache miss)
769 silly packumentCache full:https://registry.npmjs.org/stack-utils set size:undefined disposed:false
770 silly fetch manifest @babel/code-frame@^7.12.13
771 silly packumentCache full:https://registry.npmjs.org/@babel%2fcode-frame cache-miss
772 http fetch GET 200 https://registry.npmjs.org/@babel%2fcode-frame 36ms (cache miss)
773 silly packumentCache full:https://registry.npmjs.org/@babel%2fcode-frame set size:undefined disposed:false
774 silly fetch manifest @types/stack-utils@^2.0.0
775 silly packumentCache full:https://registry.npmjs.org/@types%2fstack-utils cache-miss
776 http fetch GET 200 https://registry.npmjs.org/@types%2fstack-utils 29ms (cache miss)
777 silly packumentCache full:https://registry.npmjs.org/@types%2fstack-utils set size:undefined disposed:false
778 silly fetch manifest resolve@^1.20.0
779 silly packumentCache full:https://registry.npmjs.org/resolve cache-miss
780 http fetch GET 200 https://registry.npmjs.org/resolve 47ms (cache miss)
781 silly packumentCache full:https://registry.npmjs.org/resolve set size:undefined disposed:false
782 silly fetch manifest resolve.exports@^2.0.0
783 silly packumentCache full:https://registry.npmjs.org/resolve.exports cache-miss
784 http fetch GET 200 https://registry.npmjs.org/resolve.exports 20ms (cache miss)
785 silly packumentCache full:https://registry.npmjs.org/resolve.exports set size:undefined disposed:false
786 silly fetch manifest jest-pnp-resolver@^1.2.2
787 silly packumentCache full:https://registry.npmjs.org/jest-pnp-resolver cache-miss
788 http fetch GET 200 https://registry.npmjs.org/jest-pnp-resolver 23ms (cache miss)
789 silly packumentCache full:https://registry.npmjs.org/jest-pnp-resolver set size:undefined disposed:false
790 silly fetch manifest emittery@^0.13.1
791 silly packumentCache full:https://registry.npmjs.org/emittery cache-miss
792 http fetch GET 200 https://registry.npmjs.org/emittery 37ms (cache miss)
793 silly packumentCache full:https://registry.npmjs.org/emittery set size:undefined disposed:false
794 silly fetch manifest jest-docblock@^29.7.0
795 silly packumentCache full:https://registry.npmjs.org/jest-docblock cache-miss
796 http fetch GET 200 https://registry.npmjs.org/jest-docblock 24ms (cache miss)
797 silly packumentCache full:https://registry.npmjs.org/jest-docblock set size:undefined disposed:false
798 silly fetch manifest @jest/environment@^29.7.0
799 silly packumentCache full:https://registry.npmjs.org/@jest%2fenvironment cache-miss
800 http fetch GET 200 https://registry.npmjs.org/@jest%2fenvironment 42ms (cache miss)
801 silly packumentCache full:https://registry.npmjs.org/@jest%2fenvironment set size:undefined disposed:false
802 silly fetch manifest jest-leak-detector@^29.7.0
803 silly packumentCache full:https://registry.npmjs.org/jest-leak-detector cache-miss
804 http fetch GET 200 https://registry.npmjs.org/jest-leak-detector 32ms (cache miss)
805 silly packumentCache full:https://registry.npmjs.org/jest-leak-detector set size:undefined disposed:false
806 silly fetch manifest source-map-support@0.5.13
807 silly packumentCache full:https://registry.npmjs.org/source-map-support cache-miss
808 http fetch GET 200 https://registry.npmjs.org/source-map-support 23ms (cache miss)
809 silly packumentCache full:https://registry.npmjs.org/source-map-support set size:undefined disposed:false
810 silly fetch manifest jest-mock@^29.7.0
811 silly packumentCache full:https://registry.npmjs.org/jest-mock cache-miss
812 http fetch GET 200 https://registry.npmjs.org/jest-mock 36ms (cache miss)
813 silly packumentCache full:https://registry.npmjs.org/jest-mock set size:undefined disposed:false
814 silly fetch manifest strip-bom@^4.0.0
815 silly packumentCache full:https://registry.npmjs.org/strip-bom cache-miss
816 http fetch GET 200 https://registry.npmjs.org/strip-bom 22ms (cache miss)
817 silly packumentCache full:https://registry.npmjs.org/strip-bom set size:undefined disposed:false
818 silly fetch manifest @jest/globals@^29.7.0
819 silly packumentCache full:https://registry.npmjs.org/@jest%2fglobals cache-miss
820 http fetch GET 200 https://registry.npmjs.org/@jest%2fglobals 57ms (cache miss)
821 silly packumentCache full:https://registry.npmjs.org/@jest%2fglobals set size:undefined disposed:false
822 silly fetch manifest @jest/source-map@^29.6.3
823 silly packumentCache full:https://registry.npmjs.org/@jest%2fsource-map cache-miss
824 http fetch GET 200 https://registry.npmjs.org/@jest%2fsource-map 38ms (cache miss)
825 silly packumentCache full:https://registry.npmjs.org/@jest%2fsource-map set size:undefined disposed:false
826 silly fetch manifest cjs-module-lexer@^1.0.0
827 silly packumentCache full:https://registry.npmjs.org/cjs-module-lexer cache-miss
828 http fetch GET 200 https://registry.npmjs.org/cjs-module-lexer 29ms (cache miss)
829 silly packumentCache full:https://registry.npmjs.org/cjs-module-lexer set size:undefined disposed:false
830 silly fetch manifest @jest/fake-timers@^29.7.0
831 silly packumentCache full:https://registry.npmjs.org/@jest%2ffake-timers cache-miss
832 http fetch GET 200 https://registry.npmjs.org/@jest%2ffake-timers 58ms (cache miss)
833 silly packumentCache full:https://registry.npmjs.org/@jest%2ffake-timers set size:undefined disposed:false
834 silly fetch manifest expect@^29.7.0
835 silly packumentCache full:https://registry.npmjs.org/expect cache-miss
836 http fetch GET 200 https://registry.npmjs.org/expect 42ms (cache miss)
837 silly packumentCache full:https://registry.npmjs.org/expect set size:undefined disposed:false
838 silly fetch manifest jest-diff@^29.7.0
839 silly packumentCache full:https://registry.npmjs.org/jest-diff cache-miss
840 http fetch GET 200 https://registry.npmjs.org/jest-diff 34ms (cache miss)
841 silly packumentCache full:https://registry.npmjs.org/jest-diff set size:undefined disposed:false
842 silly fetch manifest @babel/types@^7.3.3
843 silly packumentCache full:https://registry.npmjs.org/@babel%2ftypes cache-miss
844 http fetch GET 200 https://registry.npmjs.org/@babel%2ftypes 60ms (cache miss)
845 silly packumentCache full:https://registry.npmjs.org/@babel%2ftypes set size:undefined disposed:false
846 silly fetch manifest natural-compare@^1.4.0
847 silly packumentCache full:https://registry.npmjs.org/natural-compare cache-miss
848 http fetch GET 200 https://registry.npmjs.org/natural-compare 29ms (cache miss)
849 silly packumentCache full:https://registry.npmjs.org/natural-compare set size:undefined disposed:false
850 silly fetch manifest @babel/generator@^7.7.2
851 silly packumentCache full:https://registry.npmjs.org/@babel%2fgenerator cache-miss
852 http fetch GET 200 https://registry.npmjs.org/@babel%2fgenerator 42ms (cache miss)
853 silly packumentCache full:https://registry.npmjs.org/@babel%2fgenerator set size:undefined disposed:false
854 silly fetch manifest @jest/expect-utils@^29.7.0
855 silly packumentCache full:https://registry.npmjs.org/@jest%2fexpect-utils cache-miss
856 http fetch GET 200 https://registry.npmjs.org/@jest%2fexpect-utils 39ms (cache miss)
857 silly packumentCache full:https://registry.npmjs.org/@jest%2fexpect-utils set size:undefined disposed:false
858 silly fetch manifest jest-matcher-utils@^29.7.0
859 silly packumentCache full:https://registry.npmjs.org/jest-matcher-utils cache-miss
860 http fetch GET 200 https://registry.npmjs.org/jest-matcher-utils 33ms (cache miss)
861 silly packumentCache full:https://registry.npmjs.org/jest-matcher-utils set size:undefined disposed:false
862 silly fetch manifest @babel/plugin-syntax-jsx@^7.7.2
863 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-jsx cache-miss
864 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-jsx 32ms (cache miss)
865 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-jsx set size:undefined disposed:false
866 silly fetch manifest @babel/plugin-syntax-typescript@^7.7.2
867 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-typescript cache-miss
868 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-typescript 51ms (cache miss)
869 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-typescript set size:undefined disposed:false
870 silly fetch manifest babel-preset-current-node-syntax@^1.0.0
871 silly packumentCache full:https://registry.npmjs.org/babel-preset-current-node-syntax cache-miss
872 http fetch GET 200 https://registry.npmjs.org/babel-preset-current-node-syntax 30ms (cache miss)
873 silly packumentCache full:https://registry.npmjs.org/babel-preset-current-node-syntax set size:undefined disposed:false
874 silly fetch manifest picomatch@^2.2.3
875 silly packumentCache full:https://registry.npmjs.org/picomatch cache-miss
876 http fetch GET 200 https://registry.npmjs.org/picomatch 25ms (cache miss)
877 silly packumentCache full:https://registry.npmjs.org/picomatch set size:undefined disposed:false
878 silly fetch manifest leven@^3.1.0
879 silly packumentCache full:https://registry.npmjs.org/leven cache-miss
880 http fetch GET 200 https://registry.npmjs.org/leven 23ms (cache miss)
881 silly packumentCache full:https://registry.npmjs.org/leven set size:undefined disposed:false
882 silly fetch manifest camelcase@^6.2.0
883 silly packumentCache full:https://registry.npmjs.org/camelcase cache-miss
884 http fetch GET 200 https://registry.npmjs.org/camelcase 29ms (cache miss)
885 silly packumentCache full:https://registry.npmjs.org/camelcase set size:undefined disposed:false
886 silly fetch manifest braces@^3.0.3
887 silly packumentCache full:https://registry.npmjs.org/braces cache-miss
888 http fetch GET 200 https://registry.npmjs.org/braces 26ms (cache miss)
889 silly packumentCache full:https://registry.npmjs.org/braces set size:undefined disposed:false
890 silly fetch manifest picomatch@^2.3.1
891 silly packumentCache full:https://registry.npmjs.org/picomatch cache-miss
892 http cache https://registry.npmjs.org/picomatch 4ms (cache hit)
893 silly packumentCache full:https://registry.npmjs.org/picomatch set size:118818 disposed:false
894 silly fetch manifest react-is@^18.0.0
895 silly packumentCache full:https://registry.npmjs.org/react-is cache-miss
896 http fetch GET 200 https://registry.npmjs.org/react-is 95ms (cache miss)
897 silly packumentCache full:https://registry.npmjs.org/react-is set size:undefined disposed:false
898 silly fetch manifest ansi-styles@^5.0.0
899 silly packumentCache full:https://registry.npmjs.org/ansi-styles cache-miss
900 http cache https://registry.npmjs.org/ansi-styles 4ms (cache hit)
901 silly packumentCache full:https://registry.npmjs.org/ansi-styles set size:66356 disposed:false
902 silly fetch manifest ansi-regex@^5.0.1
903 silly packumentCache full:https://registry.npmjs.org/ansi-regex cache-miss
904 http fetch GET 200 https://registry.npmjs.org/ansi-regex 20ms (cache miss)
905 silly packumentCache full:https://registry.npmjs.org/ansi-regex set size:undefined disposed:false
906 silly placeDep ROOT @bcoe/v8-coverage@0.2.3 OK for: @jest/reporters@29.7.0 want: ^0.2.3
907 silly placeDep ROOT @jridgewell/trace-mapping@0.3.29 OK for: @jest/reporters@29.7.0 want: ^0.3.18
908 silly placeDep ROOT collect-v8-coverage@1.0.2 OK for: @jest/reporters@29.7.0 want: ^1.0.0
909 silly placeDep ROOT glob@7.2.3 OK for: @jest/reporters@29.7.0 want: ^7.1.3
910 silly placeDep ROOT istanbul-lib-coverage@3.2.2 OK for: @jest/reporters@29.7.0 want: ^3.0.0
911 silly placeDep ROOT istanbul-lib-instrument@6.0.3 OK for: @jest/reporters@29.7.0 want: ^6.0.0
912 silly placeDep ROOT istanbul-lib-report@3.0.1 OK for: @jest/reporters@29.7.0 want: ^3.0.0
913 silly placeDep ROOT istanbul-lib-source-maps@4.0.1 OK for: @jest/reporters@29.7.0 want: ^4.0.0
914 silly placeDep ROOT istanbul-reports@3.1.7 OK for: @jest/reporters@29.7.0 want: ^3.1.3
915 silly placeDep ROOT jest-worker@29.7.0 OK for: @jest/reporters@29.7.0 want: ^29.7.0
916 silly placeDep ROOT string-length@4.0.2 OK for: @jest/reporters@29.7.0 want: ^4.0.1
917 silly placeDep ROOT v8-to-istanbul@9.3.0 OK for: @jest/reporters@29.7.0 want: ^9.0.1
918 silly fetch manifest @jridgewell/resolve-uri@^3.1.0
919 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2fresolve-uri cache-miss
920 http fetch GET 200 https://registry.npmjs.org/@jridgewell%2fresolve-uri 40ms (cache miss)
921 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2fresolve-uri set size:undefined disposed:false
922 silly fetch manifest @jridgewell/sourcemap-codec@^1.4.14
923 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2fsourcemap-codec cache-miss
924 http fetch GET 200 https://registry.npmjs.org/@jridgewell%2fsourcemap-codec 45ms (cache miss)
925 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2fsourcemap-codec set size:undefined disposed:false
926 silly fetch manifest once@^1.3.0
927 silly packumentCache full:https://registry.npmjs.org/once cache-miss
928 http fetch GET 200 https://registry.npmjs.org/once 105ms (cache miss)
929 silly packumentCache full:https://registry.npmjs.org/once set size:undefined disposed:false
930 silly fetch manifest inflight@^1.0.4
931 silly packumentCache full:https://registry.npmjs.org/inflight cache-miss
932 http fetch GET 200 https://registry.npmjs.org/inflight 26ms (cache miss)
933 silly packumentCache full:https://registry.npmjs.org/inflight set size:undefined disposed:false
934 silly fetch manifest inherits@2
935 silly packumentCache full:https://registry.npmjs.org/inherits cache-miss
936 http fetch GET 200 https://registry.npmjs.org/inherits 22ms (cache miss)
937 silly packumentCache full:https://registry.npmjs.org/inherits set size:undefined disposed:false
938 silly fetch manifest minimatch@^3.1.1
939 silly packumentCache full:https://registry.npmjs.org/minimatch cache-miss
940 http cache https://registry.npmjs.org/minimatch 4ms (cache hit)
941 silly packumentCache full:https://registry.npmjs.org/minimatch set size:298903 disposed:false
942 silly fetch manifest fs.realpath@^1.0.0
943 silly packumentCache full:https://registry.npmjs.org/fs.realpath cache-miss
944 http fetch GET 200 https://registry.npmjs.org/fs.realpath 25ms (cache miss)
945 silly packumentCache full:https://registry.npmjs.org/fs.realpath set size:undefined disposed:false
946 silly fetch manifest path-is-absolute@^1.0.0
947 silly packumentCache full:https://registry.npmjs.org/path-is-absolute cache-miss
948 http fetch GET 200 https://registry.npmjs.org/path-is-absolute 33ms (cache miss)
949 silly packumentCache full:https://registry.npmjs.org/path-is-absolute set size:undefined disposed:false
950 silly fetch manifest @babel/core@^7.23.9
951 silly packumentCache full:https://registry.npmjs.org/@babel%2fcore cache-miss
952 http cache https://registry.npmjs.org/@babel%2fcore 7ms (cache hit)
953 silly packumentCache full:https://registry.npmjs.org/@babel%2fcore set size:721886 disposed:false
954 silly fetch manifest @babel/parser@^7.23.9
955 silly packumentCache full:https://registry.npmjs.org/@babel%2fparser cache-miss
956 http fetch GET 200 https://registry.npmjs.org/@babel%2fparser 66ms (cache miss)
957 silly packumentCache full:https://registry.npmjs.org/@babel%2fparser set size:undefined disposed:false
958 silly fetch manifest @istanbuljs/schema@^0.1.3
959 silly packumentCache full:https://registry.npmjs.org/@istanbuljs%2fschema cache-miss
960 http fetch GET 200 https://registry.npmjs.org/@istanbuljs%2fschema 39ms (cache miss)
961 silly packumentCache full:https://registry.npmjs.org/@istanbuljs%2fschema set size:undefined disposed:false
962 silly fetch manifest semver@^7.5.4
963 silly packumentCache full:https://registry.npmjs.org/semver cache-miss
964 http cache https://registry.npmjs.org/semver 6ms (cache hit)
965 silly packumentCache full:https://registry.npmjs.org/semver set size:224186 disposed:false
966 silly fetch manifest make-dir@^4.0.0
967 silly packumentCache full:https://registry.npmjs.org/make-dir cache-miss
968 http fetch GET 200 https://registry.npmjs.org/make-dir 31ms (cache miss)
969 silly packumentCache full:https://registry.npmjs.org/make-dir set size:undefined disposed:false
970 silly fetch manifest debug@^4.1.1
971 silly packumentCache full:https://registry.npmjs.org/debug cache-miss
972 http cache https://registry.npmjs.org/debug 4ms (cache hit)
973 silly packumentCache full:https://registry.npmjs.org/debug set size:193521 disposed:false
974 silly fetch manifest source-map@^0.6.1
975 silly packumentCache full:https://registry.npmjs.org/source-map cache-miss
976 http fetch GET 200 https://registry.npmjs.org/source-map 27ms (cache miss)
977 silly packumentCache full:https://registry.npmjs.org/source-map set size:undefined disposed:false
978 silly fetch manifest html-escaper@^2.0.0
979 silly packumentCache full:https://registry.npmjs.org/html-escaper cache-miss
980 http fetch GET 200 https://registry.npmjs.org/html-escaper 22ms (cache miss)
981 silly packumentCache full:https://registry.npmjs.org/html-escaper set size:undefined disposed:false
982 silly fetch manifest merge-stream@^2.0.0
983 silly packumentCache full:https://registry.npmjs.org/merge-stream cache-miss
984 http fetch GET 200 https://registry.npmjs.org/merge-stream 24ms (cache miss)
985 silly packumentCache full:https://registry.npmjs.org/merge-stream set size:undefined disposed:false
986 silly fetch manifest supports-color@^8.0.0
987 silly packumentCache full:https://registry.npmjs.org/supports-color cache-hit
988 silly fetch manifest char-regex@^1.0.2
989 silly packumentCache full:https://registry.npmjs.org/char-regex cache-miss
990 http fetch GET 200 https://registry.npmjs.org/char-regex 28ms (cache miss)
991 silly packumentCache full:https://registry.npmjs.org/char-regex set size:undefined disposed:false
992 silly fetch manifest @types/istanbul-lib-coverage@^2.0.1
993 silly packumentCache full:https://registry.npmjs.org/@types%2fistanbul-lib-coverage cache-miss
994 http cache https://registry.npmjs.org/@types%2fistanbul-lib-coverage 6ms (cache hit)
995 silly packumentCache full:https://registry.npmjs.org/@types%2fistanbul-lib-coverage set size:18094 disposed:false
996 silly placeDep ROOT @types/istanbul-lib-coverage@2.0.6 OK for: @jest/test-result@29.7.0 want: ^2.0.0
997 silly placeDep ROOT @babel/core@7.28.0 OK for: @jest/transform@29.7.0 want: ^7.11.6
998 silly placeDep ROOT babel-plugin-istanbul@6.1.1 OK for: @jest/transform@29.7.0 want: ^6.1.1
999 silly placeDep ROOT convert-source-map@2.0.0 OK for: @jest/transform@29.7.0 want: ^2.0.0
1000 silly placeDep ROOT fast-json-stable-stringify@2.1.0 OK for: @jest/transform@29.7.0 want: ^2.1.0
1001 silly placeDep ROOT pirates@4.0.7 OK for: @jest/transform@29.7.0 want: ^4.0.4
1002 silly placeDep ROOT write-file-atomic@4.0.2 OK for: @jest/transform@29.7.0 want: ^4.0.2
1003 silly fetch manifest debug@^4.1.0
1004 silly packumentCache full:https://registry.npmjs.org/debug cache-hit
1005 silly fetch manifest json5@^2.2.3
1006 silly packumentCache full:https://registry.npmjs.org/json5 cache-miss
1007 http fetch GET 200 https://registry.npmjs.org/json5 46ms (cache miss)
1008 silly packumentCache full:https://registry.npmjs.org/json5 set size:undefined disposed:false
1009 silly fetch manifest semver@^6.3.1
1010 silly packumentCache full:https://registry.npmjs.org/semver cache-hit
1011 silly fetch manifest gensync@^1.0.0-beta.2
1012 silly packumentCache full:https://registry.npmjs.org/gensync cache-miss
1013 http fetch GET 200 https://registry.npmjs.org/gensync 44ms (cache miss)
1014 silly packumentCache full:https://registry.npmjs.org/gensync set size:undefined disposed:false
1015 silly fetch manifest @babel/types@^7.28.0
1016 silly packumentCache full:https://registry.npmjs.org/@babel%2ftypes cache-miss
1017 http cache https://registry.npmjs.org/@babel%2ftypes 5ms (cache hit)
1018 silly packumentCache full:https://registry.npmjs.org/@babel%2ftypes set size:467862 disposed:false
1019 silly fetch manifest @babel/parser@^7.28.0
1020 silly packumentCache full:https://registry.npmjs.org/@babel%2fparser cache-miss
1021 http cache https://registry.npmjs.org/@babel%2fparser 4ms (cache hit)
1022 silly packumentCache full:https://registry.npmjs.org/@babel%2fparser set size:691741 disposed:false
1023 silly fetch manifest @babel/helpers@^7.27.6
1024 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelpers cache-miss
1025 http fetch GET 200 https://registry.npmjs.org/@babel%2fhelpers 36ms (cache miss)
1026 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelpers set size:undefined disposed:false
1027 silly fetch manifest @babel/template@^7.27.2
1028 silly packumentCache full:https://registry.npmjs.org/@babel%2ftemplate cache-miss
1029 http fetch GET 200 https://registry.npmjs.org/@babel%2ftemplate 37ms (cache miss)
1030 silly packumentCache full:https://registry.npmjs.org/@babel%2ftemplate set size:undefined disposed:false
1031 silly fetch manifest @babel/traverse@^7.28.0
1032 silly packumentCache full:https://registry.npmjs.org/@babel%2ftraverse cache-miss
1033 http fetch GET 200 https://registry.npmjs.org/@babel%2ftraverse 37ms (cache miss)
1034 silly packumentCache full:https://registry.npmjs.org/@babel%2ftraverse set size:undefined disposed:false
1035 silly fetch manifest @babel/generator@^7.28.0
1036 silly packumentCache full:https://registry.npmjs.org/@babel%2fgenerator cache-miss
1037 http cache https://registry.npmjs.org/@babel%2fgenerator 3ms (cache hit)
1038 silly packumentCache full:https://registry.npmjs.org/@babel%2fgenerator set size:484005 disposed:false
1039 silly fetch manifest @babel/code-frame@^7.27.1
1040 silly packumentCache full:https://registry.npmjs.org/@babel%2fcode-frame cache-miss
1041 http cache https://registry.npmjs.org/@babel%2fcode-frame 3ms (cache hit)
1042 silly packumentCache full:https://registry.npmjs.org/@babel%2fcode-frame set size:180401 disposed:false
1043 silly fetch manifest @ampproject/remapping@^2.2.0
1044 silly packumentCache full:https://registry.npmjs.org/@ampproject%2fremapping cache-miss
1045 http fetch GET 200 https://registry.npmjs.org/@ampproject%2fremapping 42ms (cache miss)
1046 silly packumentCache full:https://registry.npmjs.org/@ampproject%2fremapping set size:undefined disposed:false
1047 silly fetch manifest @babel/helper-module-transforms@^7.27.3
1048 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-module-transforms cache-miss
1049 http fetch GET 200 https://registry.npmjs.org/@babel%2fhelper-module-transforms 36ms (cache miss)
1050 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-module-transforms set size:undefined disposed:false
1051 silly fetch manifest @babel/helper-compilation-targets@^7.27.2
1052 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-compilation-targets cache-miss
1053 http fetch GET 200 https://registry.npmjs.org/@babel%2fhelper-compilation-targets 40ms (cache miss)
1054 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-compilation-targets set size:undefined disposed:false
1055 silly fetch manifest test-exclude@^6.0.0
1056 silly packumentCache full:https://registry.npmjs.org/test-exclude cache-miss
1057 http fetch GET 200 https://registry.npmjs.org/test-exclude 20ms (cache miss)
1058 silly packumentCache full:https://registry.npmjs.org/test-exclude set size:undefined disposed:false
1059 silly fetch manifest @istanbuljs/schema@^0.1.2
1060 silly packumentCache full:https://registry.npmjs.org/@istanbuljs%2fschema cache-miss
1061 http cache https://registry.npmjs.org/@istanbuljs%2fschema 2ms (cache hit)
1062 silly packumentCache full:https://registry.npmjs.org/@istanbuljs%2fschema set size:12737 disposed:false
1063 silly fetch manifest istanbul-lib-instrument@^5.0.4
1064 silly packumentCache full:https://registry.npmjs.org/istanbul-lib-instrument cache-miss
1065 http cache https://registry.npmjs.org/istanbul-lib-instrument 2ms (cache hit)
1066 silly packumentCache full:https://registry.npmjs.org/istanbul-lib-instrument set size:219175 disposed:false
1067 silly fetch manifest @babel/helper-plugin-utils@^7.0.0
1068 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-plugin-utils cache-miss
1069 http fetch GET 200 https://registry.npmjs.org/@babel%2fhelper-plugin-utils 39ms (cache miss)
1070 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-plugin-utils set size:undefined disposed:false
1071 silly fetch manifest @istanbuljs/load-nyc-config@^1.0.0
1072 silly packumentCache full:https://registry.npmjs.org/@istanbuljs%2fload-nyc-config cache-miss
1073 http fetch GET 200 https://registry.npmjs.org/@istanbuljs%2fload-nyc-config 48ms (cache miss)
1074 silly packumentCache full:https://registry.npmjs.org/@istanbuljs%2fload-nyc-config set size:undefined disposed:false
1075 silly fetch manifest imurmurhash@^0.1.4
1076 silly packumentCache full:https://registry.npmjs.org/imurmurhash cache-miss
1077 http fetch GET 200 https://registry.npmjs.org/imurmurhash 23ms (cache miss)
1078 silly packumentCache full:https://registry.npmjs.org/imurmurhash set size:undefined disposed:false
1079 silly fetch manifest signal-exit@^3.0.7
1080 silly packumentCache full:https://registry.npmjs.org/signal-exit cache-miss
1081 http fetch GET 200 https://registry.npmjs.org/signal-exit 20ms (cache miss)
1082 silly packumentCache full:https://registry.npmjs.org/signal-exit set size:undefined disposed:false
1083 silly fetch manifest @babel/core@^7.0.0
1084 silly packumentCache full:https://registry.npmjs.org/@babel%2fcore cache-hit
1085 silly placeDep ROOT @ampproject/remapping@2.3.0 OK for: @babel/core@7.28.0 want: ^2.2.0
1086 silly placeDep ROOT @babel/code-frame@7.27.1 OK for: @babel/core@7.28.0 want: ^7.27.1
1087 silly placeDep ROOT @babel/generator@7.28.0 OK for: @babel/core@7.28.0 want: ^7.28.0
1088 silly placeDep ROOT @babel/helper-compilation-targets@7.27.2 OK for: @babel/core@7.28.0 want: ^7.27.2
1089 silly placeDep ROOT @babel/helper-module-transforms@7.27.3 OK for: @babel/core@7.28.0 want: ^7.27.3
1090 silly placeDep ROOT @babel/helpers@7.28.2 OK for: @babel/core@7.28.0 want: ^7.27.6
1091 silly placeDep ROOT @babel/parser@7.28.0 OK for: @babel/core@7.28.0 want: ^7.28.0
1092 silly placeDep ROOT @babel/template@7.27.2 OK for: @babel/core@7.28.0 want: ^7.27.2
1093 silly placeDep ROOT @babel/traverse@7.28.0 OK for: @babel/core@7.28.0 want: ^7.28.0
1094 silly placeDep ROOT @babel/types@7.28.2 OK for: @babel/core@7.28.0 want: ^7.28.0
1095 silly placeDep ROOT debug@4.4.1 OK for: @babel/core@7.28.0 want: ^4.1.0
1096 silly placeDep ROOT gensync@1.0.0-beta.2 OK for: @babel/core@7.28.0 want: ^1.0.0-beta.2
1097 silly placeDep ROOT json5@2.2.3 OK for: @babel/core@7.28.0 want: ^2.2.3
1098 silly placeDep ROOT semver@6.3.1 OK for: @babel/core@7.28.0 want: ^6.3.1
1099 silly fetch manifest @jridgewell/gen-mapping@^0.3.5
1100 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2fgen-mapping cache-miss
1101 http fetch GET 200 https://registry.npmjs.org/@jridgewell%2fgen-mapping 61ms (cache miss)
1102 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2fgen-mapping set size:undefined disposed:false
1103 silly fetch manifest js-tokens@^4.0.0
1104 silly packumentCache full:https://registry.npmjs.org/js-tokens cache-miss
1105 http fetch GET 200 https://registry.npmjs.org/js-tokens 28ms (cache miss)
1106 silly packumentCache full:https://registry.npmjs.org/js-tokens set size:undefined disposed:false
1107 silly fetch manifest picocolors@^1.1.1
1108 silly packumentCache full:https://registry.npmjs.org/picocolors cache-miss
1109 http fetch GET 200 https://registry.npmjs.org/picocolors 27ms (cache miss)
1110 silly packumentCache full:https://registry.npmjs.org/picocolors set size:undefined disposed:false
1111 silly fetch manifest @babel/helper-validator-identifier@^7.27.1
1112 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-validator-identifier cache-miss
1113 http fetch GET 200 https://registry.npmjs.org/@babel%2fhelper-validator-identifier 31ms (cache miss)
1114 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-validator-identifier set size:undefined disposed:false
1115 silly fetch manifest jsesc@^3.0.2
1116 silly packumentCache full:https://registry.npmjs.org/jsesc cache-miss
1117 http fetch GET 200 https://registry.npmjs.org/jsesc 36ms (cache miss)
1118 silly packumentCache full:https://registry.npmjs.org/jsesc set size:undefined disposed:false
1119 silly fetch manifest @jridgewell/gen-mapping@^0.3.12
1120 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2fgen-mapping cache-miss
1121 http cache https://registry.npmjs.org/@jridgewell%2fgen-mapping 2ms (cache hit)
1122 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2fgen-mapping set size:77827 disposed:false
1123 silly fetch manifest lru-cache@^5.1.1
1124 silly packumentCache full:https://registry.npmjs.org/lru-cache cache-miss
1125 http fetch GET 200 https://registry.npmjs.org/lru-cache 23ms (cache miss)
1126 silly packumentCache full:https://registry.npmjs.org/lru-cache set size:undefined disposed:false
1127 silly fetch manifest browserslist@^4.24.0
1128 silly packumentCache full:https://registry.npmjs.org/browserslist cache-miss
1129 http fetch GET 200 https://registry.npmjs.org/browserslist 43ms (cache miss)
1130 silly packumentCache full:https://registry.npmjs.org/browserslist set size:undefined disposed:false
1131 silly fetch manifest @babel/compat-data@^7.27.2
1132 silly packumentCache full:https://registry.npmjs.org/@babel%2fcompat-data cache-miss
1133 http fetch GET 200 https://registry.npmjs.org/@babel%2fcompat-data 37ms (cache miss)
1134 silly packumentCache full:https://registry.npmjs.org/@babel%2fcompat-data set size:undefined disposed:false
1135 silly fetch manifest @babel/helper-validator-option@^7.27.1
1136 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-validator-option cache-miss
1137 http fetch GET 200 https://registry.npmjs.org/@babel%2fhelper-validator-option 28ms (cache miss)
1138 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-validator-option set size:undefined disposed:false
1139 silly fetch manifest @babel/traverse@^7.27.3
1140 silly packumentCache full:https://registry.npmjs.org/@babel%2ftraverse cache-miss
1141 http cache https://registry.npmjs.org/@babel%2ftraverse 4ms (cache hit)
1142 silly packumentCache full:https://registry.npmjs.org/@babel%2ftraverse set size:525342 disposed:false
1143 silly fetch manifest @babel/helper-module-imports@^7.27.1
1144 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-module-imports cache-miss
1145 http fetch GET 200 https://registry.npmjs.org/@babel%2fhelper-module-imports 33ms (cache miss)
1146 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-module-imports set size:undefined disposed:false
1147 silly fetch manifest @babel/types@^7.28.2
1148 silly packumentCache full:https://registry.npmjs.org/@babel%2ftypes cache-hit
1149 silly fetch manifest @babel/types@^7.27.1
1150 silly packumentCache full:https://registry.npmjs.org/@babel%2ftypes cache-hit
1151 silly fetch manifest debug@^4.3.1
1152 silly packumentCache full:https://registry.npmjs.org/debug cache-hit
1153 silly fetch manifest @babel/helper-globals@^7.28.0
1154 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-globals cache-miss
1155 http fetch GET 200 https://registry.npmjs.org/@babel%2fhelper-globals 33ms (cache miss)
1156 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-globals set size:undefined disposed:false
1157 silly fetch manifest @babel/helper-string-parser@^7.27.1
1158 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-string-parser cache-miss
1159 http fetch GET 200 https://registry.npmjs.org/@babel%2fhelper-string-parser 36ms (cache miss)
1160 silly packumentCache full:https://registry.npmjs.org/@babel%2fhelper-string-parser set size:undefined disposed:false
1161 silly fetch manifest ms@^2.1.3
1162 silly packumentCache full:https://registry.npmjs.org/ms cache-miss
1163 http fetch GET 200 https://registry.npmjs.org/ms 27ms (cache miss)
1164 silly packumentCache full:https://registry.npmjs.org/ms set size:undefined disposed:false
1165 silly placeDep ROOT @jridgewell/gen-mapping@0.3.12 OK for: @ampproject/remapping@2.3.0 want: ^0.3.5
1166 silly fetch manifest @jridgewell/sourcemap-codec@^1.5.0
1167 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2fsourcemap-codec cache-miss
1168 http cache https://registry.npmjs.org/@jridgewell%2fsourcemap-codec 3ms (cache hit)
1169 silly packumentCache full:https://registry.npmjs.org/@jridgewell%2fsourcemap-codec set size:56434 disposed:false
1170 silly placeDep ROOT @babel/helper-validator-identifier@7.27.1 OK for: @babel/code-frame@7.27.1 want: ^7.27.1
1171 silly placeDep ROOT js-tokens@4.0.0 OK for: @babel/code-frame@7.27.1 want: ^4.0.0
1172 silly placeDep ROOT picocolors@1.1.1 OK for: @babel/code-frame@7.27.1 want: ^1.1.1
1173 silly placeDep ROOT jsesc@3.1.0 OK for: @babel/generator@7.28.0 want: ^3.0.2
1174 silly placeDep ROOT @babel/compat-data@7.28.0 OK for: @babel/helper-compilation-targets@7.27.2 want: ^7.27.2
1175 silly placeDep ROOT @babel/helper-validator-option@7.27.1 OK for: @babel/helper-compilation-targets@7.27.2 want: ^7.27.1
1176 silly placeDep ROOT browserslist@4.25.1 OK for: @babel/helper-compilation-targets@7.27.2 want: ^4.24.0
1177 silly placeDep ROOT lru-cache@5.1.1 OK for: @babel/helper-compilation-targets@7.27.2 want: ^5.1.1
1178 silly fetch manifest caniuse-lite@^1.0.30001726
1179 silly packumentCache full:https://registry.npmjs.org/caniuse-lite cache-miss
1180 http fetch GET 200 https://registry.npmjs.org/caniuse-lite 131ms (cache miss)
1181 silly packumentCache full:https://registry.npmjs.org/caniuse-lite set size:undefined disposed:false
1182 silly fetch manifest electron-to-chromium@^1.5.173
1183 silly packumentCache full:https://registry.npmjs.org/electron-to-chromium cache-miss
1184 http fetch GET 200 https://registry.npmjs.org/electron-to-chromium 128ms (cache miss)
1185 silly packumentCache full:https://registry.npmjs.org/electron-to-chromium set size:undefined disposed:false
1186 silly fetch manifest node-releases@^2.0.19
1187 silly packumentCache full:https://registry.npmjs.org/node-releases cache-miss
1188 http fetch GET 200 https://registry.npmjs.org/node-releases 30ms (cache miss)
1189 silly packumentCache full:https://registry.npmjs.org/node-releases set size:undefined disposed:false
1190 silly fetch manifest update-browserslist-db@^1.1.3
1191 silly packumentCache full:https://registry.npmjs.org/update-browserslist-db cache-miss
1192 http fetch GET 200 https://registry.npmjs.org/update-browserslist-db 20ms (cache miss)
1193 silly packumentCache full:https://registry.npmjs.org/update-browserslist-db set size:undefined disposed:false
1194 silly fetch manifest yallist@^3.0.2
1195 silly packumentCache full:https://registry.npmjs.org/yallist cache-miss
1196 http fetch GET 200 https://registry.npmjs.org/yallist 34ms (cache miss)
1197 silly packumentCache full:https://registry.npmjs.org/yallist set size:undefined disposed:false
1198 silly placeDep ROOT @babel/helper-module-imports@7.27.1 OK for: @babel/helper-module-transforms@7.27.3 want: ^7.27.1
1199 silly placeDep ROOT @babel/helper-globals@7.28.0 OK for: @babel/traverse@7.28.0 want: ^7.28.0
1200 silly placeDep ROOT @babel/helper-string-parser@7.27.1 OK for: @babel/types@7.28.2 want: ^7.27.1
1201 silly placeDep ROOT @jest/schemas@29.6.3 OK for: @jest/types@29.6.3 want: ^29.6.3
1202 silly placeDep ROOT @types/istanbul-reports@3.0.4 OK for: @jest/types@29.6.3 want: ^3.0.0
1203 silly placeDep ROOT @types/yargs@17.0.33 OK for: @jest/types@29.6.3 want: ^17.0.8
1204 silly fetch manifest @sinclair/typebox@^0.27.8
1205 silly packumentCache full:https://registry.npmjs.org/@sinclair%2ftypebox cache-miss
1206 http fetch GET 200 https://registry.npmjs.org/@sinclair%2ftypebox 63ms (cache miss)
1207 silly packumentCache full:https://registry.npmjs.org/@sinclair%2ftypebox set size:undefined disposed:false
1208 silly fetch manifest @types/istanbul-lib-report@*
1209 silly packumentCache full:https://registry.npmjs.org/@types%2fistanbul-lib-report cache-miss
1210 http fetch GET 200 https://registry.npmjs.org/@types%2fistanbul-lib-report 35ms (cache miss)
1211 silly packumentCache full:https://registry.npmjs.org/@types%2fistanbul-lib-report set size:undefined disposed:false
1212 silly fetch manifest @types/yargs-parser@*
1213 silly packumentCache full:https://registry.npmjs.org/@types%2fyargs-parser cache-miss
1214 http fetch GET 200 https://registry.npmjs.org/@types%2fyargs-parser 34ms (cache miss)
1215 silly packumentCache full:https://registry.npmjs.org/@types%2fyargs-parser set size:undefined disposed:false
1216 silly placeDep ROOT @sinclair/typebox@0.27.8 OK for: @jest/schemas@29.6.3 want: ^0.27.8
1217 silly placeDep ROOT @jridgewell/sourcemap-codec@1.5.4 OK for: @jridgewell/gen-mapping@0.3.12 want: ^1.5.0
1218 silly placeDep ROOT @jridgewell/resolve-uri@3.1.2 OK for: @jridgewell/trace-mapping@0.3.29 want: ^3.1.0
1219 silly placeDep ROOT @types/istanbul-lib-report@3.0.3 OK for: @types/istanbul-reports@3.0.4 want: *
1220 silly placeDep ROOT @types/yargs-parser@21.0.3 OK for: @types/yargs@17.0.33 want: *
1221 silly placeDep ROOT type-fest@0.21.3 OK for: ansi-escapes@4.3.2 want: ^0.21.3
1222 silly placeDep ROOT @babel/helper-plugin-utils@7.27.1 OK for: babel-plugin-istanbul@6.1.1 want: ^7.0.0
1223 silly placeDep ROOT @istanbuljs/load-nyc-config@1.1.0 OK for: babel-plugin-istanbul@6.1.1 want: ^1.0.0
1224 silly placeDep ROOT @istanbuljs/schema@0.1.3 OK for: babel-plugin-istanbul@6.1.1 want: ^0.1.2
1225 silly placeDep node_modules/babel-plugin-istanbul istanbul-lib-instrument@5.2.1 OK for: babel-plugin-istanbul@6.1.1 want: ^5.0.4
1226 silly placeDep ROOT test-exclude@6.0.0 OK for: babel-plugin-istanbul@6.1.1 want: ^6.0.0
1227 silly fetch manifest camelcase@^5.3.1
1228 silly packumentCache full:https://registry.npmjs.org/camelcase cache-miss
1229 http cache https://registry.npmjs.org/camelcase 2ms (cache hit)
1230 silly packumentCache full:https://registry.npmjs.org/camelcase set size:57059 disposed:false
1231 silly fetch manifest find-up@^4.1.0
1232 silly packumentCache full:https://registry.npmjs.org/find-up cache-miss
1233 http fetch GET 200 https://registry.npmjs.org/find-up 28ms (cache miss)
1234 silly packumentCache full:https://registry.npmjs.org/find-up set size:undefined disposed:false
1235 silly fetch manifest get-package-type@^0.1.0
1236 silly packumentCache full:https://registry.npmjs.org/get-package-type cache-miss
1237 http fetch GET 200 https://registry.npmjs.org/get-package-type 50ms (cache miss)
1238 silly packumentCache full:https://registry.npmjs.org/get-package-type set size:undefined disposed:false
1239 silly fetch manifest js-yaml@^3.13.1
1240 silly packumentCache full:https://registry.npmjs.org/js-yaml cache-miss
1241 http fetch GET 200 https://registry.npmjs.org/js-yaml 29ms (cache miss)
1242 silly packumentCache full:https://registry.npmjs.org/js-yaml set size:undefined disposed:false
1243 silly fetch manifest resolve-from@^5.0.0
1244 silly packumentCache full:https://registry.npmjs.org/resolve-from cache-miss
1245 http fetch GET 200 https://registry.npmjs.org/resolve-from 25ms (cache miss)
1246 silly packumentCache full:https://registry.npmjs.org/resolve-from set size:undefined disposed:false
1247 silly fetch manifest minimatch@^3.0.4
1248 silly packumentCache full:https://registry.npmjs.org/minimatch cache-hit
1249 silly placeDep ROOT camelcase@5.3.1 OK for: @istanbuljs/load-nyc-config@1.1.0 want: ^5.3.1
1250 silly placeDep ROOT find-up@4.1.0 OK for: @istanbuljs/load-nyc-config@1.1.0 want: ^4.1.0
1251 silly placeDep ROOT get-package-type@0.1.0 OK for: @istanbuljs/load-nyc-config@1.1.0 want: ^0.1.0
1252 silly placeDep ROOT js-yaml@3.14.1 OK for: @istanbuljs/load-nyc-config@1.1.0 want: ^3.13.1
1253 silly placeDep ROOT resolve-from@5.0.0 OK for: @istanbuljs/load-nyc-config@1.1.0 want: ^5.0.0
1254 silly fetch manifest locate-path@^5.0.0
1255 silly packumentCache full:https://registry.npmjs.org/locate-path cache-miss
1256 http fetch GET 200 https://registry.npmjs.org/locate-path 29ms (cache miss)
1257 silly packumentCache full:https://registry.npmjs.org/locate-path set size:undefined disposed:false
1258 silly fetch manifest path-exists@^4.0.0
1259 silly packumentCache full:https://registry.npmjs.org/path-exists cache-miss
1260 http fetch GET 200 https://registry.npmjs.org/path-exists 24ms (cache miss)
1261 silly packumentCache full:https://registry.npmjs.org/path-exists set size:undefined disposed:false
1262 silly fetch manifest argparse@^1.0.7
1263 silly packumentCache full:https://registry.npmjs.org/argparse cache-miss
1264 http fetch GET 200 https://registry.npmjs.org/argparse 27ms (cache miss)
1265 silly packumentCache full:https://registry.npmjs.org/argparse set size:undefined disposed:false
1266 silly fetch manifest esprima@^4.0.0
1267 silly packumentCache full:https://registry.npmjs.org/esprima cache-miss
1268 http fetch GET 200 https://registry.npmjs.org/esprima 22ms (cache miss)
1269 silly packumentCache full:https://registry.npmjs.org/esprima set size:undefined disposed:false
1270 silly fetch manifest browserslist@>= 4.21.0
1271 silly packumentCache full:https://registry.npmjs.org/browserslist cache-miss
1272 http cache https://registry.npmjs.org/browserslist 3ms (cache hit)
1273 silly packumentCache full:https://registry.npmjs.org/browserslist set size:482975 disposed:false
1274 silly placeDep ROOT caniuse-lite@1.0.30001727 OK for: browserslist@4.25.1 want: ^1.0.30001726
1275 silly placeDep ROOT electron-to-chromium@1.5.191 OK for: browserslist@4.25.1 want: ^1.5.173
1276 silly placeDep ROOT node-releases@2.0.19 OK for: browserslist@4.25.1 want: ^2.0.19
1277 silly placeDep ROOT update-browserslist-db@1.1.3 OK for: browserslist@4.25.1 want: ^1.1.3
1278 silly fetch manifest escalade@^3.2.0
1279 silly packumentCache full:https://registry.npmjs.org/escalade cache-miss
1280 http fetch GET 200 https://registry.npmjs.org/escalade 24ms (cache miss)
1281 silly packumentCache full:https://registry.npmjs.org/escalade set size:undefined disposed:false
1282 silly placeDep ROOT ansi-styles@4.3.0 OK for: chalk@4.1.2 want: ^4.1.0
1283 silly placeDep ROOT supports-color@7.2.0 OK for: chalk@4.1.2 want: ^7.1.0
1284 silly fetch manifest color-convert@^2.0.1
1285 silly packumentCache full:https://registry.npmjs.org/color-convert cache-miss
1286 http fetch GET 200 https://registry.npmjs.org/color-convert 24ms (cache miss)
1287 silly packumentCache full:https://registry.npmjs.org/color-convert set size:undefined disposed:false
1288 silly fetch manifest has-flag@^4.0.0
1289 silly packumentCache full:https://registry.npmjs.org/has-flag cache-miss
1290 http fetch GET 200 https://registry.npmjs.org/has-flag 22ms (cache miss)
1291 silly packumentCache full:https://registry.npmjs.org/has-flag set size:undefined disposed:false
1292 silly placeDep ROOT color-convert@2.0.1 OK for: ansi-styles@4.3.0 want: ^2.0.1
1293 silly fetch manifest color-name@~1.1.4
1294 silly packumentCache full:https://registry.npmjs.org/color-name cache-miss
1295 http fetch GET 200 https://registry.npmjs.org/color-name 28ms (cache miss)
1296 silly packumentCache full:https://registry.npmjs.org/color-name set size:undefined disposed:false
1297 silly placeDep ROOT color-name@1.1.4 OK for: color-convert@2.0.1 want: ~1.1.4
1298 silly placeDep ROOT ms@2.1.3 OK for: debug@4.4.1 want: ^2.1.3
1299 silly placeDep ROOT locate-path@5.0.0 OK for: find-up@4.1.0 want: ^5.0.0
1300 silly placeDep ROOT path-exists@4.0.0 OK for: find-up@4.1.0 want: ^4.0.0
1301 silly fetch manifest p-locate@^4.1.0
1302 silly packumentCache full:https://registry.npmjs.org/p-locate cache-miss
1303 http fetch GET 200 https://registry.npmjs.org/p-locate 18ms (cache miss)
1304 silly packumentCache full:https://registry.npmjs.org/p-locate set size:undefined disposed:false
1305 silly placeDep ROOT fs.realpath@1.0.0 OK for: glob@7.2.3 want: ^1.0.0
1306 silly placeDep ROOT inflight@1.0.6 OK for: glob@7.2.3 want: ^1.0.4
1307 silly placeDep ROOT inherits@2.0.4 OK for: glob@7.2.3 want: 2
1308 silly placeDep ROOT minimatch@3.1.2 OK for: glob@7.2.3 want: ^3.1.1
1309 silly placeDep ROOT once@1.4.0 OK for: glob@7.2.3 want: ^1.3.0
1310 silly placeDep ROOT path-is-absolute@1.0.1 OK for: glob@7.2.3 want: ^1.0.0
1311 silly fetch manifest wrappy@1
1312 silly packumentCache full:https://registry.npmjs.org/wrappy cache-miss
1313 http fetch GET 200 https://registry.npmjs.org/wrappy 22ms (cache miss)
1314 silly packumentCache full:https://registry.npmjs.org/wrappy set size:undefined disposed:false
1315 silly fetch manifest brace-expansion@^1.1.7
1316 silly packumentCache full:https://registry.npmjs.org/brace-expansion cache-miss
1317 http fetch GET 200 https://registry.npmjs.org/brace-expansion 22ms (cache miss)
1318 silly packumentCache full:https://registry.npmjs.org/brace-expansion set size:undefined disposed:false
1319 silly placeDep ROOT pkg-dir@4.2.0 OK for: import-local@3.2.0 want: ^4.2.0
1320 silly placeDep ROOT resolve-cwd@3.0.0 OK for: import-local@3.2.0 want: ^3.0.0
1321 silly placeDep ROOT wrappy@1.0.2 OK for: inflight@1.0.6 want: 1
1322 silly placeDep node_modules/istanbul-lib-instrument semver@7.7.2 OK for: istanbul-lib-instrument@6.0.3 want: ^7.5.4
1323 silly placeDep ROOT make-dir@4.0.0 OK for: istanbul-lib-report@3.0.1 want: ^4.0.0
1324 silly placeDep ROOT source-map@0.6.1 OK for: istanbul-lib-source-maps@4.0.1 want: ^0.6.1
1325 silly placeDep ROOT html-escaper@2.0.2 OK for: istanbul-reports@3.1.7 want: ^2.0.0
1326 silly placeDep ROOT execa@5.1.1 OK for: jest-changed-files@29.7.0 want: ^5.0.0
1327 silly placeDep ROOT p-limit@3.1.0 OK for: jest-changed-files@29.7.0 want: ^3.1.0
1328 silly fetch manifest onetime@^5.1.2
1329 silly packumentCache full:https://registry.npmjs.org/onetime cache-miss
1330 http fetch GET 200 https://registry.npmjs.org/onetime 21ms (cache miss)
1331 silly packumentCache full:https://registry.npmjs.org/onetime set size:undefined disposed:false
1332 silly fetch manifest is-stream@^2.0.0
1333 silly packumentCache full:https://registry.npmjs.org/is-stream cache-miss
1334 http fetch GET 200 https://registry.npmjs.org/is-stream 21ms (cache miss)
1335 silly packumentCache full:https://registry.npmjs.org/is-stream set size:undefined disposed:false
1336 silly fetch manifest get-stream@^6.0.0
1337 silly packumentCache full:https://registry.npmjs.org/get-stream cache-miss
1338 http fetch GET 200 https://registry.npmjs.org/get-stream 19ms (cache miss)
1339 silly packumentCache full:https://registry.npmjs.org/get-stream set size:undefined disposed:false
1340 silly fetch manifest signal-exit@^3.0.3
1341 silly packumentCache full:https://registry.npmjs.org/signal-exit cache-miss
1342 http cache https://registry.npmjs.org/signal-exit 2ms (cache hit)
1343 silly packumentCache full:https://registry.npmjs.org/signal-exit set size:53489 disposed:false
1344 silly fetch manifest npm-run-path@^4.0.1
1345 silly packumentCache full:https://registry.npmjs.org/npm-run-path cache-miss
1346 http fetch GET 200 https://registry.npmjs.org/npm-run-path 23ms (cache miss)
1347 silly packumentCache full:https://registry.npmjs.org/npm-run-path set size:undefined disposed:false
1348 silly fetch manifest human-signals@^2.1.0
1349 silly packumentCache full:https://registry.npmjs.org/human-signals cache-miss
1350 http fetch GET 200 https://registry.npmjs.org/human-signals 22ms (cache miss)
1351 silly packumentCache full:https://registry.npmjs.org/human-signals set size:undefined disposed:false
1352 silly fetch manifest strip-final-newline@^2.0.0
1353 silly packumentCache full:https://registry.npmjs.org/strip-final-newline cache-miss
1354 http fetch GET 200 https://registry.npmjs.org/strip-final-newline 19ms (cache miss)
1355 silly packumentCache full:https://registry.npmjs.org/strip-final-newline set size:undefined disposed:false
1356 silly fetch manifest yocto-queue@^0.1.0
1357 silly packumentCache full:https://registry.npmjs.org/yocto-queue cache-miss
1358 http fetch GET 200 https://registry.npmjs.org/yocto-queue 30ms (cache miss)
1359 silly packumentCache full:https://registry.npmjs.org/yocto-queue set size:undefined disposed:false
1360 silly placeDep ROOT get-stream@6.0.1 OK for: execa@5.1.1 want: ^6.0.0
1361 silly placeDep ROOT human-signals@2.1.0 OK for: execa@5.1.1 want: ^2.1.0
1362 silly placeDep ROOT is-stream@2.0.1 OK for: execa@5.1.1 want: ^2.0.0
1363 silly placeDep ROOT merge-stream@2.0.0 OK for: execa@5.1.1 want: ^2.0.0
1364 silly placeDep ROOT npm-run-path@4.0.1 OK for: execa@5.1.1 want: ^4.0.1
1365 silly placeDep ROOT onetime@5.1.2 OK for: execa@5.1.1 want: ^5.1.2
1366 silly placeDep ROOT signal-exit@3.0.7 OK for: execa@5.1.1 want: ^3.0.3
1367 silly placeDep ROOT strip-final-newline@2.0.0 OK for: execa@5.1.1 want: ^2.0.0
1368 silly fetch manifest mimic-fn@^2.1.0
1369 silly packumentCache full:https://registry.npmjs.org/mimic-fn cache-miss
1370 http fetch GET 200 https://registry.npmjs.org/mimic-fn 21ms (cache miss)
1371 silly packumentCache full:https://registry.npmjs.org/mimic-fn set size:undefined disposed:false
1372 silly placeDep ROOT create-jest@29.7.0 OK for: jest-cli@29.7.0 want: ^29.7.0
1373 silly placeDep ROOT yargs@17.7.2 OK for: jest-cli@29.7.0 want: ^17.3.1
1374 silly fetch manifest prompts@^2.0.1
1375 silly packumentCache full:https://registry.npmjs.org/prompts cache-miss
1376 http fetch GET 200 https://registry.npmjs.org/prompts 37ms (cache miss)
1377 silly packumentCache full:https://registry.npmjs.org/prompts set size:undefined disposed:false
1378 silly fetch manifest y18n@^5.0.5
1379 silly packumentCache full:https://registry.npmjs.org/y18n cache-miss
1380 http fetch GET 200 https://registry.npmjs.org/y18n 27ms (cache miss)
1381 silly packumentCache full:https://registry.npmjs.org/y18n set size:undefined disposed:false
1382 silly fetch manifest cliui@^8.0.1
1383 silly packumentCache full:https://registry.npmjs.org/cliui cache-miss
1384 http fetch GET 200 https://registry.npmjs.org/cliui 25ms (cache miss)
1385 silly packumentCache full:https://registry.npmjs.org/cliui set size:undefined disposed:false
1386 silly fetch manifest escalade@^3.1.1
1387 silly packumentCache full:https://registry.npmjs.org/escalade cache-miss
1388 http cache https://registry.npmjs.org/escalade 2ms (cache hit)
1389 silly packumentCache full:https://registry.npmjs.org/escalade set size:33724 disposed:false
1390 silly fetch manifest string-width@^4.2.3
1391 silly packumentCache full:https://registry.npmjs.org/string-width cache-miss
1392 http fetch GET 200 https://registry.npmjs.org/string-width 25ms (cache miss)
1393 silly packumentCache full:https://registry.npmjs.org/string-width set size:undefined disposed:false
1394 silly fetch manifest yargs-parser@^21.1.1
1395 silly packumentCache full:https://registry.npmjs.org/yargs-parser cache-miss
1396 http fetch GET 200 https://registry.npmjs.org/yargs-parser 50ms (cache miss)
1397 silly packumentCache full:https://registry.npmjs.org/yargs-parser set size:undefined disposed:false
1398 silly fetch manifest get-caller-file@^2.0.5
1399 silly packumentCache full:https://registry.npmjs.org/get-caller-file cache-miss
1400 http fetch GET 200 https://registry.npmjs.org/get-caller-file 21ms (cache miss)
1401 silly packumentCache full:https://registry.npmjs.org/get-caller-file set size:undefined disposed:false
1402 silly fetch manifest require-directory@^2.1.1
1403 silly packumentCache full:https://registry.npmjs.org/require-directory cache-miss
1404 http fetch GET 200 https://registry.npmjs.org/require-directory 30ms (cache miss)
1405 silly packumentCache full:https://registry.npmjs.org/require-directory set size:undefined disposed:false
1406 silly placeDep ROOT prompts@2.4.2 OK for: create-jest@29.7.0 want: ^2.0.1
1407 silly fetch manifest kleur@^3.0.3
1408 silly packumentCache full:https://registry.npmjs.org/kleur cache-miss
1409 http fetch GET 200 https://registry.npmjs.org/kleur 20ms (cache miss)
1410 silly packumentCache full:https://registry.npmjs.org/kleur set size:undefined disposed:false
1411 silly fetch manifest sisteransi@^1.0.5
1412 silly packumentCache full:https://registry.npmjs.org/sisteransi cache-miss
1413 http fetch GET 200 https://registry.npmjs.org/sisteransi 23ms (cache miss)
1414 silly packumentCache full:https://registry.npmjs.org/sisteransi set size:undefined disposed:false
1415 silly placeDep ROOT @jest/test-sequencer@29.7.0 OK for: jest-config@29.7.0 want: ^29.7.0
1416 silly placeDep ROOT babel-jest@29.7.0 OK for: jest-config@29.7.0 want: ^29.7.0
1417 silly placeDep ROOT deepmerge@4.3.1 OK for: jest-config@29.7.0 want: ^4.2.2
1418 silly placeDep ROOT jest-circus@29.7.0 OK for: jest-config@29.7.0 want: ^29.7.0
1419 silly placeDep ROOT jest-environment-node@29.7.0 OK for: jest-config@29.7.0 want: ^29.7.0
1420 silly placeDep ROOT jest-get-type@29.6.3 OK for: jest-config@29.7.0 want: ^29.6.3
1421 silly placeDep ROOT parse-json@5.2.0 OK for: jest-config@29.7.0 want: ^5.2.0
1422 silly placeDep ROOT strip-json-comments@3.1.1 OK for: jest-config@29.7.0 want: ^3.1.1
1423 silly fetch manifest babel-preset-jest@^29.6.3
1424 silly packumentCache full:https://registry.npmjs.org/babel-preset-jest cache-miss
1425 http fetch GET 200 https://registry.npmjs.org/babel-preset-jest 29ms (cache miss)
1426 silly packumentCache full:https://registry.npmjs.org/babel-preset-jest set size:undefined disposed:false
1427 silly fetch manifest @types/babel__core@^7.1.14
1428 silly packumentCache full:https://registry.npmjs.org/@types%2fbabel__core cache-miss
1429 http fetch GET 200 https://registry.npmjs.org/@types%2fbabel__core 33ms (cache miss)
1430 silly packumentCache full:https://registry.npmjs.org/@types%2fbabel__core set size:undefined disposed:false
1431 silly fetch manifest co@^4.6.0
1432 silly packumentCache full:https://registry.npmjs.org/co cache-miss
1433 http fetch GET 200 https://registry.npmjs.org/co 28ms (cache miss)
1434 silly packumentCache full:https://registry.npmjs.org/co set size:undefined disposed:false
1435 silly fetch manifest dedent@^1.0.0
1436 silly packumentCache full:https://registry.npmjs.org/dedent cache-miss
1437 http fetch GET 200 https://registry.npmjs.org/dedent 25ms (cache miss)
1438 silly packumentCache full:https://registry.npmjs.org/dedent set size:undefined disposed:false
1439 silly fetch manifest jest-each@^29.7.0
1440 silly packumentCache full:https://registry.npmjs.org/jest-each cache-miss
1441 http fetch GET 200 https://registry.npmjs.org/jest-each 37ms (cache miss)
1442 silly packumentCache full:https://registry.npmjs.org/jest-each set size:undefined disposed:false
1443 silly fetch manifest pure-rand@^6.0.0
1444 silly packumentCache full:https://registry.npmjs.org/pure-rand cache-miss
1445 http fetch GET 200 https://registry.npmjs.org/pure-rand 28ms (cache miss)
1446 silly packumentCache full:https://registry.npmjs.org/pure-rand set size:undefined disposed:false
1447 silly fetch manifest @jest/expect@^29.7.0
1448 silly packumentCache full:https://registry.npmjs.org/@jest%2fexpect cache-miss
1449 http fetch GET 200 https://registry.npmjs.org/@jest%2fexpect 48ms (cache miss)
1450 silly packumentCache full:https://registry.npmjs.org/@jest%2fexpect set size:undefined disposed:false
1451 silly fetch manifest is-generator-fn@^2.0.0
1452 silly packumentCache full:https://registry.npmjs.org/is-generator-fn cache-miss
1453 http fetch GET 200 https://registry.npmjs.org/is-generator-fn 27ms (cache miss)
1454 silly packumentCache full:https://registry.npmjs.org/is-generator-fn set size:undefined disposed:false
1455 silly fetch manifest error-ex@^1.3.1
1456 silly packumentCache full:https://registry.npmjs.org/error-ex cache-miss
1457 http fetch GET 200 https://registry.npmjs.org/error-ex 21ms (cache miss)
1458 silly packumentCache full:https://registry.npmjs.org/error-ex set size:undefined disposed:false
1459 silly fetch manifest lines-and-columns@^1.1.6
1460 silly packumentCache full:https://registry.npmjs.org/lines-and-columns cache-miss
1461 http fetch GET 200 https://registry.npmjs.org/lines-and-columns 21ms (cache miss)
1462 silly packumentCache full:https://registry.npmjs.org/lines-and-columns set size:undefined disposed:false
1463 silly fetch manifest json-parse-even-better-errors@^2.3.0
1464 silly packumentCache full:https://registry.npmjs.org/json-parse-even-better-errors cache-miss
1465 http fetch GET 200 https://registry.npmjs.org/json-parse-even-better-errors 38ms (cache miss)
1466 silly packumentCache full:https://registry.npmjs.org/json-parse-even-better-errors set size:undefined disposed:false
1467 silly fetch manifest @babel/core@^7.8.0
1468 silly packumentCache full:https://registry.npmjs.org/@babel%2fcore cache-hit
1469 silly placeDep ROOT @types/babel__core@7.20.5 OK for: babel-jest@29.7.0 want: ^7.1.14
1470 silly placeDep ROOT babel-preset-jest@29.6.3 OK for: babel-jest@29.7.0 want: ^29.6.3
1471 silly fetch manifest @types/babel__template@*
1472 silly packumentCache full:https://registry.npmjs.org/@types%2fbabel__template cache-miss
1473 http fetch GET 200 https://registry.npmjs.org/@types%2fbabel__template 30ms (cache miss)
1474 silly packumentCache full:https://registry.npmjs.org/@types%2fbabel__template set size:undefined disposed:false
1475 silly fetch manifest @types/babel__traverse@*
1476 silly packumentCache full:https://registry.npmjs.org/@types%2fbabel__traverse cache-miss
1477 http fetch GET 200 https://registry.npmjs.org/@types%2fbabel__traverse 36ms (cache miss)
1478 silly packumentCache full:https://registry.npmjs.org/@types%2fbabel__traverse set size:undefined disposed:false
1479 silly fetch manifest @types/babel__generator@*
1480 silly packumentCache full:https://registry.npmjs.org/@types%2fbabel__generator cache-miss
1481 http fetch GET 200 https://registry.npmjs.org/@types%2fbabel__generator 27ms (cache miss)
1482 silly packumentCache full:https://registry.npmjs.org/@types%2fbabel__generator set size:undefined disposed:false
1483 silly fetch manifest babel-plugin-jest-hoist@^29.6.3
1484 silly packumentCache full:https://registry.npmjs.org/babel-plugin-jest-hoist cache-miss
1485 http fetch GET 200 https://registry.npmjs.org/babel-plugin-jest-hoist 28ms (cache miss)
1486 silly packumentCache full:https://registry.npmjs.org/babel-plugin-jest-hoist set size:undefined disposed:false
1487 silly placeDep ROOT @types/babel__generator@7.27.0 OK for: @types/babel__core@7.20.5 want: *
1488 silly placeDep ROOT @types/babel__template@7.4.4 OK for: @types/babel__core@7.20.5 want: *
1489 silly placeDep ROOT @types/babel__traverse@7.20.7 OK for: @types/babel__core@7.20.5 want: *
1490 silly placeDep ROOT babel-plugin-jest-hoist@29.6.3 OK for: babel-preset-jest@29.6.3 want: ^29.6.3
1491 silly placeDep ROOT babel-preset-current-node-syntax@1.1.0 OK for: babel-preset-jest@29.6.3 want: ^1.0.0
1492 silly fetch manifest @babel/plugin-syntax-async-generators@^7.8.4
1493 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-async-generators cache-miss
1494 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-async-generators 30ms (cache miss)
1495 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-async-generators set size:undefined disposed:false
1496 silly fetch manifest @babel/plugin-syntax-bigint@^7.8.3
1497 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-bigint cache-miss
1498 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-bigint 32ms (cache miss)
1499 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-bigint set size:undefined disposed:false
1500 silly fetch manifest @babel/plugin-syntax-class-properties@^7.12.13
1501 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-class-properties cache-miss
1502 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-class-properties 27ms (cache miss)
1503 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-class-properties set size:undefined disposed:false
1504 silly fetch manifest @babel/plugin-syntax-class-static-block@^7.14.5
1505 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-class-static-block cache-miss
1506 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-class-static-block 32ms (cache miss)
1507 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-class-static-block set size:undefined disposed:false
1508 silly fetch manifest @babel/plugin-syntax-import-attributes@^7.24.7
1509 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-import-attributes cache-miss
1510 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-import-attributes 32ms (cache miss)
1511 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-import-attributes set size:undefined disposed:false
1512 silly fetch manifest @babel/plugin-syntax-import-meta@^7.10.4
1513 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-import-meta cache-miss
1514 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-import-meta 37ms (cache miss)
1515 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-import-meta set size:undefined disposed:false
1516 silly fetch manifest @babel/plugin-syntax-json-strings@^7.8.3
1517 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-json-strings cache-miss
1518 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-json-strings 43ms (cache miss)
1519 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-json-strings set size:undefined disposed:false
1520 silly fetch manifest @babel/plugin-syntax-logical-assignment-operators@^7.10.4
1521 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-logical-assignment-operators cache-miss
1522 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-logical-assignment-operators 34ms (cache miss)
1523 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-logical-assignment-operators set size:undefined disposed:false
1524 silly fetch manifest @babel/plugin-syntax-nullish-coalescing-operator@^7.8.3
1525 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-nullish-coalescing-operator cache-miss
1526 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-nullish-coalescing-operator 30ms (cache miss)
1527 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-nullish-coalescing-operator set size:undefined disposed:false
1528 silly fetch manifest @babel/plugin-syntax-numeric-separator@^7.10.4
1529 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-numeric-separator cache-miss
1530 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-numeric-separator 29ms (cache miss)
1531 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-numeric-separator set size:undefined disposed:false
1532 silly fetch manifest @babel/plugin-syntax-object-rest-spread@^7.8.3
1533 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-object-rest-spread cache-miss
1534 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-object-rest-spread 27ms (cache miss)
1535 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-object-rest-spread set size:undefined disposed:false
1536 silly fetch manifest @babel/plugin-syntax-optional-catch-binding@^7.8.3
1537 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-optional-catch-binding cache-miss
1538 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-optional-catch-binding 28ms (cache miss)
1539 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-optional-catch-binding set size:undefined disposed:false
1540 silly fetch manifest @babel/plugin-syntax-optional-chaining@^7.8.3
1541 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-optional-chaining cache-miss
1542 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-optional-chaining 40ms (cache miss)
1543 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-optional-chaining set size:undefined disposed:false
1544 silly fetch manifest @babel/plugin-syntax-private-property-in-object@^7.14.5
1545 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-private-property-in-object cache-miss
1546 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-private-property-in-object 23ms (cache miss)
1547 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-private-property-in-object set size:undefined disposed:false
1548 silly fetch manifest @babel/plugin-syntax-top-level-await@^7.14.5
1549 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-top-level-await cache-miss
1550 http fetch GET 200 https://registry.npmjs.org/@babel%2fplugin-syntax-top-level-await 27ms (cache miss)
1551 silly packumentCache full:https://registry.npmjs.org/@babel%2fplugin-syntax-top-level-await set size:undefined disposed:false
1552 silly placeDep ROOT @babel/plugin-syntax-async-generators@7.8.4 OK for: babel-preset-current-node-syntax@1.1.0 want: ^7.8.4
1553 silly placeDep ROOT @babel/plugin-syntax-bigint@7.8.3 OK for: babel-preset-current-node-syntax@1.1.0 want: ^7.8.3
1554 silly placeDep ROOT @babel/plugin-syntax-class-properties@7.12.13 OK for: babel-preset-current-node-syntax@1.1.0 want: ^7.12.13
1555 silly placeDep ROOT @babel/plugin-syntax-class-static-block@7.14.5 OK for: babel-preset-current-node-syntax@1.1.0 want: ^7.14.5
1556 silly placeDep ROOT @babel/plugin-syntax-import-attributes@7.27.1 OK for: babel-preset-current-node-syntax@1.1.0 want: ^7.24.7
1557 silly placeDep ROOT @babel/plugin-syntax-import-meta@7.10.4 OK for: babel-preset-current-node-syntax@1.1.0 want: ^7.10.4
1558 silly placeDep ROOT @babel/plugin-syntax-json-strings@7.8.3 OK for: babel-preset-current-node-syntax@1.1.0 want: ^7.8.3
1559 silly placeDep ROOT @babel/plugin-syntax-logical-assignment-operators@7.10.4 OK for: babel-preset-current-node-syntax@1.1.0 want: ^7.10.4
1560 silly placeDep ROOT @babel/plugin-syntax-nullish-coalescing-operator@7.8.3 OK for: babel-preset-current-node-syntax@1.1.0 want: ^7.8.3
1561 silly placeDep ROOT @babel/plugin-syntax-numeric-separator@7.10.4 OK for: babel-preset-current-node-syntax@1.1.0 want: ^7.10.4
1562 silly placeDep ROOT @babel/plugin-syntax-object-rest-spread@7.8.3 OK for: babel-preset-current-node-syntax@1.1.0 want: ^7.8.3
1563 silly placeDep ROOT @babel/plugin-syntax-optional-catch-binding@7.8.3 OK for: babel-preset-current-node-syntax@1.1.0 want: ^7.8.3
1564 silly placeDep ROOT @babel/plugin-syntax-optional-chaining@7.8.3 OK for: babel-preset-current-node-syntax@1.1.0 want: ^7.8.3
1565 silly placeDep ROOT @babel/plugin-syntax-private-property-in-object@7.14.5 OK for: babel-preset-current-node-syntax@1.1.0 want: ^7.14.5
1566 silly placeDep ROOT @babel/plugin-syntax-top-level-await@7.14.5 OK for: babel-preset-current-node-syntax@1.1.0 want: ^7.14.5
1567 silly fetch manifest babel-plugin-macros@^3.1.0
1568 silly packumentCache full:https://registry.npmjs.org/babel-plugin-macros cache-miss
1569 http fetch GET 200 https://registry.npmjs.org/babel-plugin-macros 33ms (cache miss)
1570 silly packumentCache full:https://registry.npmjs.org/babel-plugin-macros set size:undefined disposed:false
1571 silly placeDep ROOT @jest/environment@29.7.0 OK for: jest-circus@29.7.0 want: ^29.7.0
1572 silly placeDep ROOT @jest/expect@29.7.0 OK for: jest-circus@29.7.0 want: ^29.7.0
1573 silly placeDep ROOT co@4.6.0 OK for: jest-circus@29.7.0 want: ^4.6.0
1574 silly placeDep ROOT dedent@1.6.0 OK for: jest-circus@29.7.0 want: ^1.0.0
1575 silly placeDep ROOT is-generator-fn@2.1.0 OK for: jest-circus@29.7.0 want: ^2.0.0
1576 silly placeDep ROOT jest-each@29.7.0 OK for: jest-circus@29.7.0 want: ^29.7.0
1577 silly placeDep ROOT jest-matcher-utils@29.7.0 OK for: jest-circus@29.7.0 want: ^29.7.0
1578 silly placeDep ROOT pure-rand@6.1.0 OK for: jest-circus@29.7.0 want: ^6.0.0
1579 silly placeDep ROOT stack-utils@2.0.6 OK for: jest-circus@29.7.0 want: ^2.0.3
1580 silly fetch manifest escape-string-regexp@^2.0.0
1581 silly packumentCache full:https://registry.npmjs.org/escape-string-regexp cache-miss
1582 http fetch GET 200 https://registry.npmjs.org/escape-string-regexp 28ms (cache miss)
1583 silly packumentCache full:https://registry.npmjs.org/escape-string-regexp set size:undefined disposed:false
1584 silly placeDep ROOT @jest/fake-timers@29.7.0 OK for: @jest/environment@29.7.0 want: ^29.7.0
1585 silly placeDep ROOT jest-mock@29.7.0 OK for: @jest/environment@29.7.0 want: ^29.7.0
1586 silly fetch manifest @sinonjs/fake-timers@^10.0.2
1587 silly packumentCache full:https://registry.npmjs.org/@sinonjs%2ffake-timers cache-miss
1588 http fetch GET 200 https://registry.npmjs.org/@sinonjs%2ffake-timers 51ms (cache miss)
1589 silly packumentCache full:https://registry.npmjs.org/@sinonjs%2ffake-timers set size:undefined disposed:false
1590 silly placeDep ROOT expect@29.7.0 OK for: @jest/expect@29.7.0 want: ^29.7.0
1591 silly placeDep ROOT @sinonjs/fake-timers@10.3.0 OK for: @jest/fake-timers@29.7.0 want: ^10.0.2
1592 silly fetch manifest @sinonjs/commons@^3.0.0
1593 silly packumentCache full:https://registry.npmjs.org/@sinonjs%2fcommons cache-miss
1594 http fetch GET 200 https://registry.npmjs.org/@sinonjs%2fcommons 40ms (cache miss)
1595 silly packumentCache full:https://registry.npmjs.org/@sinonjs%2fcommons set size:undefined disposed:false
1596 silly placeDep ROOT @sinonjs/commons@3.0.1 OK for: @sinonjs/fake-timers@10.3.0 want: ^3.0.0
1597 silly fetch manifest type-detect@4.0.8
1598 silly packumentCache full:https://registry.npmjs.org/type-detect cache-miss
1599 http fetch GET 200 https://registry.npmjs.org/type-detect 51ms (cache miss)
1600 silly packumentCache full:https://registry.npmjs.org/type-detect set size:undefined disposed:false
1601 silly placeDep ROOT type-detect@4.0.8 OK for: @sinonjs/commons@3.0.1 want: 4.0.8
1602 silly placeDep ROOT @jest/expect-utils@29.7.0 OK for: expect@29.7.0 want: ^29.7.0
1603 silly placeDep ROOT @types/graceful-fs@4.1.9 OK for: jest-haste-map@29.7.0 want: ^4.1.3
1604 silly placeDep ROOT anymatch@3.1.3 OK for: jest-haste-map@29.7.0 want: ^3.0.3
1605 silly placeDep ROOT fb-watchman@2.0.2 OK for: jest-haste-map@29.7.0 want: ^2.0.0
1606 silly placeDep ROOT fsevents@2.3.3 OK for: jest-haste-map@29.7.0 want: ^2.3.2
1607 silly placeDep ROOT walker@1.0.8 OK for: jest-haste-map@29.7.0 want: ^1.0.8
1608 silly fetch manifest picomatch@^2.0.4
1609 silly packumentCache full:https://registry.npmjs.org/picomatch cache-hit
1610 silly fetch manifest normalize-path@^3.0.0
1611 silly packumentCache full:https://registry.npmjs.org/normalize-path cache-miss
1612 http fetch GET 200 https://registry.npmjs.org/normalize-path 31ms (cache miss)
1613 silly packumentCache full:https://registry.npmjs.org/normalize-path set size:undefined disposed:false
1614 silly fetch manifest bser@2.1.1
1615 silly packumentCache full:https://registry.npmjs.org/bser cache-miss
1616 http fetch GET 200 https://registry.npmjs.org/bser 27ms (cache miss)
1617 silly packumentCache full:https://registry.npmjs.org/bser set size:undefined disposed:false
1618 silly fetch manifest makeerror@1.0.12
1619 silly packumentCache full:https://registry.npmjs.org/makeerror cache-miss
1620 http fetch GET 200 https://registry.npmjs.org/makeerror 23ms (cache miss)
1621 silly packumentCache full:https://registry.npmjs.org/makeerror set size:undefined disposed:false
1622 silly placeDep ROOT normalize-path@3.0.0 OK for: anymatch@3.1.3 want: ^3.0.0
1623 silly placeDep ROOT picomatch@2.3.1 OK for: anymatch@3.1.3 want: ^2.0.4
1624 silly placeDep ROOT bser@2.1.1 OK for: fb-watchman@2.0.2 want: 2.1.1
1625 silly fetch manifest node-int64@^0.4.0
1626 silly packumentCache full:https://registry.npmjs.org/node-int64 cache-miss
1627 http fetch GET 200 https://registry.npmjs.org/node-int64 24ms (cache miss)
1628 silly packumentCache full:https://registry.npmjs.org/node-int64 set size:undefined disposed:false
1629 silly placeDep ROOT node-int64@0.4.0 OK for: bser@2.1.1 want: ^0.4.0
1630 silly placeDep ROOT jest-diff@29.7.0 OK for: jest-matcher-utils@29.7.0 want: ^29.7.0
1631 silly fetch manifest diff-sequences@^29.6.3
1632 silly packumentCache full:https://registry.npmjs.org/diff-sequences cache-miss
1633 http fetch GET 200 https://registry.npmjs.org/diff-sequences 23ms (cache miss)
1634 silly packumentCache full:https://registry.npmjs.org/diff-sequences set size:undefined disposed:false
1635 silly placeDep ROOT diff-sequences@29.6.3 OK for: jest-diff@29.7.0 want: ^29.6.3
1636 silly placeDep ROOT @types/stack-utils@2.0.3 OK for: jest-message-util@29.7.0 want: ^2.0.0
1637 silly fetch manifest jest-resolve@*
1638 silly packumentCache full:https://registry.npmjs.org/jest-resolve cache-miss
1639 http cache https://registry.npmjs.org/jest-resolve 3ms (cache hit)
1640 silly packumentCache full:https://registry.npmjs.org/jest-resolve set size:648243 disposed:false
1641 silly placeDep ROOT jest-pnp-resolver@1.2.3 OK for: jest-resolve@29.7.0 want: ^1.2.2
1642 silly placeDep ROOT resolve@1.22.10 OK for: jest-resolve@29.7.0 want: ^1.20.0
1643 silly placeDep ROOT resolve.exports@2.0.3 OK for: jest-resolve@29.7.0 want: ^2.0.0
1644 silly fetch manifest is-core-module@^2.16.0
1645 silly packumentCache full:https://registry.npmjs.org/is-core-module cache-miss
1646 http fetch GET 200 https://registry.npmjs.org/is-core-module 21ms (cache miss)
1647 silly packumentCache full:https://registry.npmjs.org/is-core-module set size:undefined disposed:false
1648 silly fetch manifest path-parse@^1.0.7
1649 silly packumentCache full:https://registry.npmjs.org/path-parse cache-miss
1650 http fetch GET 200 https://registry.npmjs.org/path-parse 61ms (cache miss)
1651 silly packumentCache full:https://registry.npmjs.org/path-parse set size:undefined disposed:false
1652 silly fetch manifest supports-preserve-symlinks-flag@^1.0.0
1653 silly packumentCache full:https://registry.npmjs.org/supports-preserve-symlinks-flag cache-miss
1654 http fetch GET 200 https://registry.npmjs.org/supports-preserve-symlinks-flag 20ms (cache miss)
1655 silly packumentCache full:https://registry.npmjs.org/supports-preserve-symlinks-flag set size:undefined disposed:false
1656 silly placeDep ROOT emittery@0.13.1 OK for: jest-runner@29.7.0 want: ^0.13.1
1657 silly placeDep ROOT jest-docblock@29.7.0 OK for: jest-runner@29.7.0 want: ^29.7.0
1658 silly placeDep ROOT jest-leak-detector@29.7.0 OK for: jest-runner@29.7.0 want: ^29.7.0
1659 silly placeDep ROOT source-map-support@0.5.13 OK for: jest-runner@29.7.0 want: 0.5.13
1660 silly fetch manifest detect-newline@^3.0.0
1661 silly packumentCache full:https://registry.npmjs.org/detect-newline cache-miss
1662 http fetch GET 200 https://registry.npmjs.org/detect-newline 26ms (cache miss)
1663 silly packumentCache full:https://registry.npmjs.org/detect-newline set size:undefined disposed:false
1664 silly fetch manifest buffer-from@^1.0.0
1665 silly packumentCache full:https://registry.npmjs.org/buffer-from cache-miss
1666 http fetch GET 200 https://registry.npmjs.org/buffer-from 23ms (cache miss)
1667 silly packumentCache full:https://registry.npmjs.org/buffer-from set size:undefined disposed:false
1668 silly placeDep ROOT detect-newline@3.1.0 OK for: jest-docblock@29.7.0 want: ^3.0.0
1669 silly placeDep ROOT @jest/globals@29.7.0 OK for: jest-runtime@29.7.0 want: ^29.7.0
1670 silly placeDep ROOT @jest/source-map@29.6.3 OK for: jest-runtime@29.7.0 want: ^29.6.3
1671 silly placeDep ROOT cjs-module-lexer@1.4.3 OK for: jest-runtime@29.7.0 want: ^1.0.0
1672 silly placeDep ROOT strip-bom@4.0.0 OK for: jest-runtime@29.7.0 want: ^4.0.0
1673 silly fetch manifest callsites@^3.0.0
1674 silly packumentCache full:https://registry.npmjs.org/callsites cache-miss
1675 http fetch GET 200 https://registry.npmjs.org/callsites 34ms (cache miss)
1676 silly packumentCache full:https://registry.npmjs.org/callsites set size:undefined disposed:false
1677 silly placeDep ROOT callsites@3.1.0 OK for: @jest/source-map@29.6.3 want: ^3.0.0
1678 silly placeDep ROOT @babel/plugin-syntax-jsx@7.27.1 OK for: jest-snapshot@29.7.0 want: ^7.7.2
1679 silly placeDep ROOT @babel/plugin-syntax-typescript@7.27.1 OK for: jest-snapshot@29.7.0 want: ^7.7.2
1680 silly placeDep ROOT natural-compare@1.4.0 OK for: jest-snapshot@29.7.0 want: ^1.4.0
1681 silly placeDep node_modules/jest-snapshot semver@7.7.2 OK for: jest-snapshot@29.7.0 want: ^7.5.3
1682 silly placeDep node_modules/jest-validate camelcase@6.3.0 OK for: jest-validate@29.7.0 want: ^6.2.0
1683 silly placeDep ROOT leven@3.1.0 OK for: jest-validate@29.7.0 want: ^3.1.0
1684 silly placeDep node_modules/jest-worker supports-color@8.1.1 OK for: jest-worker@29.7.0 want: ^8.0.0
1685 silly placeDep ROOT argparse@1.0.10 OK for: js-yaml@3.14.1 want: ^1.0.7
1686 silly placeDep ROOT esprima@4.0.1 OK for: js-yaml@3.14.1 want: ^4.0.0
1687 silly fetch manifest sprintf-js@~1.0.2
1688 silly packumentCache full:https://registry.npmjs.org/sprintf-js cache-miss
1689 http fetch GET 200 https://registry.npmjs.org/sprintf-js 25ms (cache miss)
1690 silly packumentCache full:https://registry.npmjs.org/sprintf-js set size:undefined disposed:false
1691 silly placeDep ROOT sprintf-js@1.0.3 OK for: argparse@1.0.10 want: ~1.0.2
1692 silly placeDep ROOT p-locate@4.1.0 OK for: locate-path@5.0.0 want: ^4.1.0
1693 silly fetch manifest p-limit@^2.2.0
1694 silly packumentCache full:https://registry.npmjs.org/p-limit cache-miss
1695 http cache https://registry.npmjs.org/p-limit 3ms (cache hit)
1696 silly packumentCache full:https://registry.npmjs.org/p-limit set size:50060 disposed:false
1697 silly placeDep ROOT yallist@3.1.1 OK for: lru-cache@5.1.1 want: ^3.0.2
1698 silly placeDep node_modules/make-dir semver@7.7.2 OK for: make-dir@4.0.0 want: ^7.5.3
1699 silly placeDep ROOT braces@3.0.3 OK for: micromatch@4.0.8 want: ^3.0.3
1700 silly fetch manifest fill-range@^7.1.1
1701 silly packumentCache full:https://registry.npmjs.org/fill-range cache-miss
1702 http fetch GET 200 https://registry.npmjs.org/fill-range 22ms (cache miss)
1703 silly packumentCache full:https://registry.npmjs.org/fill-range set size:undefined disposed:false
1704 silly placeDep ROOT fill-range@7.1.1 OK for: braces@3.0.3 want: ^7.1.1
1705 silly fetch manifest to-regex-range@^5.0.1
1706 silly packumentCache full:https://registry.npmjs.org/to-regex-range cache-miss
1707 http fetch GET 200 https://registry.npmjs.org/to-regex-range 20ms (cache miss)
1708 silly packumentCache full:https://registry.npmjs.org/to-regex-range set size:undefined disposed:false
1709 silly placeDep ROOT to-regex-range@5.0.1 OK for: fill-range@7.1.1 want: ^5.0.1
1710 silly fetch manifest is-number@^7.0.0
1711 silly packumentCache full:https://registry.npmjs.org/is-number cache-miss
1712 http fetch GET 200 https://registry.npmjs.org/is-number 20ms (cache miss)
1713 silly packumentCache full:https://registry.npmjs.org/is-number set size:undefined disposed:false
1714 silly placeDep ROOT brace-expansion@1.1.12 OK for: minimatch@3.1.2 want: ^1.1.7
1715 silly fetch manifest balanced-match@^1.0.0
1716 silly packumentCache full:https://registry.npmjs.org/balanced-match cache-miss
1717 http fetch GET 200 https://registry.npmjs.org/balanced-match 23ms (cache miss)
1718 silly packumentCache full:https://registry.npmjs.org/balanced-match set size:undefined disposed:false
1719 silly fetch manifest concat-map@0.0.1
1720 silly packumentCache full:https://registry.npmjs.org/concat-map cache-miss
1721 http fetch GET 200 https://registry.npmjs.org/concat-map 31ms (cache miss)
1722 silly packumentCache full:https://registry.npmjs.org/concat-map set size:undefined disposed:false
1723 silly placeDep ROOT balanced-match@1.0.2 OK for: brace-expansion@1.1.12 want: ^1.0.0
1724 silly placeDep ROOT concat-map@0.0.1 OK for: brace-expansion@1.1.12 want: 0.0.1
1725 silly placeDep ROOT chokidar@3.6.0 OK for: nodemon@3.1.7 want: ^3.5.2
1726 silly placeDep ROOT ignore-by-default@1.0.1 OK for: nodemon@3.1.7 want: ^1.0.1
1727 silly placeDep ROOT pstree.remy@1.1.8 OK for: nodemon@3.1.7 want: ^1.1.8
1728 silly placeDep node_modules/nodemon semver@7.7.2 OK for: nodemon@3.1.7 want: ^7.5.3
1729 silly placeDep ROOT simple-update-notifier@2.0.0 OK for: nodemon@3.1.7 want: ^2.0.0
1730 silly placeDep node_modules/nodemon supports-color@5.5.0 OK for: nodemon@3.1.7 want: ^5.5.0
1731 silly placeDep ROOT touch@3.1.1 OK for: nodemon@3.1.7 want: ^3.1.0
1732 silly placeDep ROOT undefsafe@2.0.5 OK for: nodemon@3.1.7 want: ^2.0.5
1733 silly fetch manifest is-glob@~4.0.1
1734 silly packumentCache full:https://registry.npmjs.org/is-glob cache-miss
1735 http fetch GET 200 https://registry.npmjs.org/is-glob 26ms (cache miss)
1736 silly packumentCache full:https://registry.npmjs.org/is-glob set size:undefined disposed:false
1737 silly fetch manifest readdirp@~3.6.0
1738 silly packumentCache full:https://registry.npmjs.org/readdirp cache-miss
1739 http fetch GET 200 https://registry.npmjs.org/readdirp 26ms (cache miss)
1740 silly packumentCache full:https://registry.npmjs.org/readdirp set size:undefined disposed:false
1741 silly fetch manifest glob-parent@~5.1.2
1742 silly packumentCache full:https://registry.npmjs.org/glob-parent cache-miss
1743 http fetch GET 200 https://registry.npmjs.org/glob-parent 23ms (cache miss)
1744 silly packumentCache full:https://registry.npmjs.org/glob-parent set size:undefined disposed:false
1745 silly fetch manifest is-binary-path@~2.1.0
1746 silly packumentCache full:https://registry.npmjs.org/is-binary-path cache-miss
1747 http fetch GET 200 https://registry.npmjs.org/is-binary-path 24ms (cache miss)
1748 silly packumentCache full:https://registry.npmjs.org/is-binary-path set size:undefined disposed:false
1749 silly fetch manifest has-flag@^3.0.0
1750 silly packumentCache full:https://registry.npmjs.org/has-flag cache-miss
1751 http cache https://registry.npmjs.org/has-flag 2ms (cache hit)
1752 silly packumentCache full:https://registry.npmjs.org/has-flag set size:15180 disposed:false
1753 silly placeDep ROOT glob-parent@5.1.2 OK for: chokidar@3.6.0 want: ~5.1.2
1754 silly placeDep ROOT is-binary-path@2.1.0 OK for: chokidar@3.6.0 want: ~2.1.0
1755 silly placeDep ROOT is-glob@4.0.3 OK for: chokidar@3.6.0 want: ~4.0.1
1756 silly placeDep ROOT readdirp@3.6.0 OK for: chokidar@3.6.0 want: ~3.6.0
1757 silly fetch manifest is-glob@^4.0.1
1758 silly packumentCache full:https://registry.npmjs.org/is-glob cache-miss
1759 http cache https://registry.npmjs.org/is-glob 2ms (cache hit)
1760 silly packumentCache full:https://registry.npmjs.org/is-glob set size:40530 disposed:false
1761 silly fetch manifest binary-extensions@^2.0.0
1762 silly packumentCache full:https://registry.npmjs.org/binary-extensions cache-miss
1763 http fetch GET 200 https://registry.npmjs.org/binary-extensions 22ms (cache miss)
1764 silly packumentCache full:https://registry.npmjs.org/binary-extensions set size:undefined disposed:false
1765 silly fetch manifest is-extglob@^2.1.1
1766 silly packumentCache full:https://registry.npmjs.org/is-extglob cache-miss
1767 http fetch GET 200 https://registry.npmjs.org/is-extglob 24ms (cache miss)
1768 silly packumentCache full:https://registry.npmjs.org/is-extglob set size:undefined disposed:false
1769 silly placeDep ROOT binary-extensions@2.3.0 OK for: is-binary-path@2.1.0 want: ^2.0.0
1770 silly placeDep ROOT is-extglob@2.1.1 OK for: is-glob@4.0.3 want: ^2.1.1
1771 silly placeDep ROOT mimic-fn@2.1.0 OK for: onetime@5.1.2 want: ^2.1.0
1772 silly placeDep ROOT yocto-queue@0.1.0 OK for: p-limit@3.1.0 want: ^0.1.0
1773 silly placeDep node_modules/p-locate p-limit@2.3.0 OK for: p-locate@4.1.0 want: ^2.2.0
1774 silly fetch manifest p-try@^2.0.0
1775 silly packumentCache full:https://registry.npmjs.org/p-try cache-miss
1776 http fetch GET 200 https://registry.npmjs.org/p-try 21ms (cache miss)
1777 silly packumentCache full:https://registry.npmjs.org/p-try set size:undefined disposed:false
1778 silly placeDep ROOT error-ex@1.3.2 OK for: parse-json@5.2.0 want: ^1.3.1
1779 silly placeDep ROOT json-parse-even-better-errors@2.3.1 OK for: parse-json@5.2.0 want: ^2.3.0
1780 silly placeDep ROOT lines-and-columns@1.2.4 OK for: parse-json@5.2.0 want: ^1.1.6
1781 silly fetch manifest is-arrayish@^0.2.1
1782 silly packumentCache full:https://registry.npmjs.org/is-arrayish cache-miss
1783 http fetch GET 200 https://registry.npmjs.org/is-arrayish 23ms (cache miss)
1784 silly packumentCache full:https://registry.npmjs.org/is-arrayish set size:undefined disposed:false
1785 silly placeDep ROOT is-arrayish@0.2.1 OK for: error-ex@1.3.2 want: ^0.2.1
1786 silly placeDep ROOT process@0.11.10 OK for: path@0.12.7 want: ^0.11.1
1787 silly placeDep ROOT util@0.10.4 OK for: path@0.12.7 want: ^0.10.3
1788 silly fetch manifest inherits@2.0.3
1789 silly packumentCache full:https://registry.npmjs.org/inherits cache-miss
1790 http cache https://registry.npmjs.org/inherits 3ms (cache hit)
1791 silly packumentCache full:https://registry.npmjs.org/inherits set size:12925 disposed:false
1792 silly placeDep ROOT detect-libc@2.0.4 OK for: prebuild-install@7.1.3 want: ^2.0.0
1793 silly placeDep ROOT expand-template@2.0.3 OK for: prebuild-install@7.1.3 want: ^2.0.3
1794 silly placeDep ROOT github-from-package@0.0.0 OK for: prebuild-install@7.1.3 want: 0.0.0
1795 silly placeDep ROOT minimist@1.2.8 OK for: prebuild-install@7.1.3 want: ^1.2.3
1796 silly placeDep ROOT mkdirp-classic@0.5.3 OK for: prebuild-install@7.1.3 want: ^0.5.3
1797 silly placeDep ROOT napi-build-utils@2.0.0 OK for: prebuild-install@7.1.3 want: ^2.0.0
1798 silly placeDep ROOT node-abi@3.75.0 OK for: prebuild-install@7.1.3 want: ^3.3.0
1799 silly placeDep ROOT pump@3.0.3 OK for: prebuild-install@7.1.3 want: ^3.0.0
1800 silly placeDep ROOT rc@1.2.8 OK for: prebuild-install@7.1.3 want: ^1.2.7
1801 silly placeDep ROOT simple-get@4.0.1 OK for: prebuild-install@7.1.3 want: ^4.0.0
1802 silly placeDep ROOT tar-fs@2.1.3 OK for: prebuild-install@7.1.3 want: ^2.0.0
1803 silly placeDep ROOT tunnel-agent@0.6.0 OK for: prebuild-install@7.1.3 want: ^0.6.0
1804 silly fetch manifest semver@^7.3.5
1805 silly packumentCache full:https://registry.npmjs.org/semver cache-hit
1806 silly fetch manifest end-of-stream@^1.1.0
1807 silly packumentCache full:https://registry.npmjs.org/end-of-stream cache-miss
1808 http fetch GET 200 https://registry.npmjs.org/end-of-stream 20ms (cache miss)
1809 silly packumentCache full:https://registry.npmjs.org/end-of-stream set size:undefined disposed:false
1810 silly fetch manifest ini@~1.3.0
1811 silly packumentCache full:https://registry.npmjs.org/ini cache-miss
1812 http fetch GET 200 https://registry.npmjs.org/ini 29ms (cache miss)
1813 silly packumentCache full:https://registry.npmjs.org/ini set size:undefined disposed:false
1814 silly fetch manifest deep-extend@^0.6.0
1815 silly packumentCache full:https://registry.npmjs.org/deep-extend cache-miss
1816 http fetch GET 200 https://registry.npmjs.org/deep-extend 28ms (cache miss)
1817 silly packumentCache full:https://registry.npmjs.org/deep-extend set size:undefined disposed:false
1818 silly fetch manifest strip-json-comments@~2.0.1
1819 silly packumentCache full:https://registry.npmjs.org/strip-json-comments cache-miss
1820 http cache https://registry.npmjs.org/strip-json-comments 3ms (cache hit)
1821 silly packumentCache full:https://registry.npmjs.org/strip-json-comments set size:41580 disposed:false
1822 silly fetch manifest decompress-response@^6.0.0
1823 silly packumentCache full:https://registry.npmjs.org/decompress-response cache-miss
1824 http fetch GET 200 https://registry.npmjs.org/decompress-response 31ms (cache miss)
1825 silly packumentCache full:https://registry.npmjs.org/decompress-response set size:undefined disposed:false
1826 silly fetch manifest simple-concat@^1.0.0
1827 silly packumentCache full:https://registry.npmjs.org/simple-concat cache-miss
1828 http fetch GET 200 https://registry.npmjs.org/simple-concat 80ms (cache miss)
1829 silly packumentCache full:https://registry.npmjs.org/simple-concat set size:undefined disposed:false
1830 silly fetch manifest chownr@^1.1.1
1831 silly packumentCache full:https://registry.npmjs.org/chownr cache-miss
1832 http fetch GET 200 https://registry.npmjs.org/chownr 26ms (cache miss)
1833 silly packumentCache full:https://registry.npmjs.org/chownr set size:undefined disposed:false
1834 silly fetch manifest tar-stream@^2.1.4
1835 silly packumentCache full:https://registry.npmjs.org/tar-stream cache-miss
1836 http fetch GET 200 https://registry.npmjs.org/tar-stream 58ms (cache miss)
1837 silly packumentCache full:https://registry.npmjs.org/tar-stream set size:undefined disposed:false
1838 silly fetch manifest safe-buffer@^5.0.1
1839 silly packumentCache full:https://registry.npmjs.org/safe-buffer cache-miss
1840 http fetch GET 200 https://registry.npmjs.org/safe-buffer 27ms (cache miss)
1841 silly packumentCache full:https://registry.npmjs.org/safe-buffer set size:undefined disposed:false
1842 silly placeDep node_modules/node-abi semver@7.7.2 OK for: node-abi@3.75.0 want: ^7.3.5
1843 silly placeDep node_modules/pretty-format ansi-styles@5.2.0 OK for: pretty-format@29.7.0 want: ^5.0.0
1844 silly placeDep ROOT react-is@18.3.1 OK for: pretty-format@29.7.0 want: ^18.0.0
1845 silly placeDep ROOT kleur@3.0.3 OK for: prompts@2.4.2 want: ^3.0.3
1846 silly placeDep ROOT sisteransi@1.0.5 OK for: prompts@2.4.2 want: ^1.0.5
1847 silly placeDep ROOT end-of-stream@1.4.5 OK for: pump@3.0.3 want: ^1.1.0
1848 silly placeDep ROOT deep-extend@0.6.0 OK for: rc@1.2.8 want: ^0.6.0
1849 silly placeDep ROOT ini@1.3.8 OK for: rc@1.2.8 want: ~1.3.0
1850 silly placeDep node_modules/rc strip-json-comments@2.0.1 OK for: rc@1.2.8 want: ~2.0.1
1851 silly placeDep ROOT is-core-module@2.16.1 OK for: resolve@1.22.10 want: ^2.16.0
1852 silly placeDep ROOT path-parse@1.0.7 OK for: resolve@1.22.10 want: ^1.0.7
1853 silly placeDep ROOT supports-preserve-symlinks-flag@1.0.0 OK for: resolve@1.22.10 want: ^1.0.0
1854 silly fetch manifest hasown@^2.0.2
1855 silly packumentCache full:https://registry.npmjs.org/hasown cache-miss
1856 http fetch GET 200 https://registry.npmjs.org/hasown 21ms (cache miss)
1857 silly packumentCache full:https://registry.npmjs.org/hasown set size:undefined disposed:false
1858 silly placeDep ROOT hasown@2.0.2 OK for: is-core-module@2.16.1 want: ^2.0.2
1859 silly fetch manifest function-bind@^1.1.2
1860 silly packumentCache full:https://registry.npmjs.org/function-bind cache-miss
1861 http fetch GET 200 https://registry.npmjs.org/function-bind 24ms (cache miss)
1862 silly packumentCache full:https://registry.npmjs.org/function-bind set size:undefined disposed:false
1863 silly placeDep ROOT function-bind@1.1.2 OK for: hasown@2.0.2 want: ^1.1.2
1864 silly placeDep ROOT shebang-regex@3.0.0 OK for: shebang-command@2.0.0 want: ^3.0.0
1865 silly placeDep ROOT decompress-response@6.0.0 OK for: simple-get@4.0.1 want: ^6.0.0
1866 silly placeDep ROOT simple-concat@1.0.1 OK for: simple-get@4.0.1 want: ^1.0.0
1867 silly fetch manifest mimic-response@^3.1.0
1868 silly packumentCache full:https://registry.npmjs.org/mimic-response cache-miss
1869 http fetch GET 200 https://registry.npmjs.org/mimic-response 31ms (cache miss)
1870 silly packumentCache full:https://registry.npmjs.org/mimic-response set size:undefined disposed:false
1871 silly placeDep ROOT mimic-response@3.1.0 OK for: decompress-response@6.0.0 want: ^3.1.0
1872 silly placeDep node_modules/simple-update-notifier semver@7.7.2 OK for: simple-update-notifier@2.0.0 want: ^7.5.3
1873 silly placeDep ROOT buffer-from@1.1.2 OK for: source-map-support@0.5.13 want: ^1.0.0
1874 silly placeDep ROOT escape-string-regexp@2.0.0 OK for: stack-utils@2.0.6 want: ^2.0.0
1875 silly placeDep ROOT char-regex@1.0.2 OK for: string-length@4.0.2 want: ^1.0.2
1876 silly placeDep ROOT ansi-regex@5.0.1 OK for: strip-ansi@6.0.1 want: ^5.0.1
1877 silly placeDep ROOT has-flag@4.0.0 OK for: supports-color@7.2.0 want: ^4.0.0
1878 silly placeDep ROOT chownr@1.1.4 OK for: tar-fs@2.1.3 want: ^1.1.1
1879 silly placeDep ROOT tar-stream@2.2.0 OK for: tar-fs@2.1.3 want: ^2.1.4
1880 silly fetch manifest bl@^4.0.3
1881 silly packumentCache full:https://registry.npmjs.org/bl cache-miss
1882 http fetch GET 200 https://registry.npmjs.org/bl 28ms (cache miss)
1883 silly packumentCache full:https://registry.npmjs.org/bl set size:undefined disposed:false
1884 silly fetch manifest fs-constants@^1.0.0
1885 silly packumentCache full:https://registry.npmjs.org/fs-constants cache-miss
1886 http fetch GET 200 https://registry.npmjs.org/fs-constants 24ms (cache miss)
1887 silly packumentCache full:https://registry.npmjs.org/fs-constants set size:undefined disposed:false
1888 silly fetch manifest readable-stream@^3.1.1
1889 silly packumentCache full:https://registry.npmjs.org/readable-stream cache-miss
1890 http fetch GET 200 https://registry.npmjs.org/readable-stream 25ms (cache miss)
1891 silly packumentCache full:https://registry.npmjs.org/readable-stream set size:undefined disposed:false
1892 silly placeDep ROOT bl@4.1.0 OK for: tar-stream@2.2.0 want: ^4.0.3
1893 silly placeDep ROOT fs-constants@1.0.0 OK for: tar-stream@2.2.0 want: ^1.0.0
1894 silly placeDep ROOT readable-stream@3.6.2 OK for: tar-stream@2.2.0 want: ^3.1.1
1895 silly fetch manifest buffer@^5.5.0
1896 silly packumentCache full:https://registry.npmjs.org/buffer cache-miss
1897 http fetch GET 200 https://registry.npmjs.org/buffer 30ms (cache miss)
1898 silly packumentCache full:https://registry.npmjs.org/buffer set size:undefined disposed:false
1899 silly fetch manifest readable-stream@^3.4.0
1900 silly packumentCache full:https://registry.npmjs.org/readable-stream cache-miss
1901 http cache https://registry.npmjs.org/readable-stream 6ms (cache hit)
1902 silly packumentCache full:https://registry.npmjs.org/readable-stream set size:261096 disposed:false
1903 silly fetch manifest string_decoder@^1.1.1
1904 silly packumentCache full:https://registry.npmjs.org/string_decoder cache-miss
1905 http fetch GET 200 https://registry.npmjs.org/string_decoder 23ms (cache miss)
1906 silly packumentCache full:https://registry.npmjs.org/string_decoder set size:undefined disposed:false
1907 silly fetch manifest util-deprecate@^1.0.1
1908 silly packumentCache full:https://registry.npmjs.org/util-deprecate cache-miss
1909 http fetch GET 200 https://registry.npmjs.org/util-deprecate 35ms (cache miss)
1910 silly packumentCache full:https://registry.npmjs.org/util-deprecate set size:undefined disposed:false
1911 silly placeDep ROOT buffer@5.7.1 OK for: bl@4.1.0 want: ^5.5.0
1912 silly fetch manifest base64-js@^1.3.1
1913 silly packumentCache full:https://registry.npmjs.org/base64-js cache-miss
1914 http fetch GET 200 https://registry.npmjs.org/base64-js 23ms (cache miss)
1915 silly packumentCache full:https://registry.npmjs.org/base64-js set size:undefined disposed:false
1916 silly fetch manifest ieee754@^1.1.13
1917 silly packumentCache full:https://registry.npmjs.org/ieee754 cache-miss
1918 http fetch GET 200 https://registry.npmjs.org/ieee754 30ms (cache miss)
1919 silly packumentCache full:https://registry.npmjs.org/ieee754 set size:undefined disposed:false
1920 silly placeDep ROOT base64-js@1.5.1 OK for: buffer@5.7.1 want: ^1.3.1
1921 silly placeDep ROOT ieee754@1.2.1 OK for: buffer@5.7.1 want: ^1.1.13
1922 silly placeDep ROOT string_decoder@1.3.0 OK for: readable-stream@3.6.2 want: ^1.1.1
1923 silly placeDep ROOT util-deprecate@1.0.2 OK for: readable-stream@3.6.2 want: ^1.0.1
1924 silly fetch manifest safe-buffer@~5.2.0
1925 silly packumentCache full:https://registry.npmjs.org/safe-buffer cache-miss
1926 http cache https://registry.npmjs.org/safe-buffer 3ms (cache hit)
1927 silly packumentCache full:https://registry.npmjs.org/safe-buffer set size:41183 disposed:false
1928 silly placeDep ROOT safe-buffer@5.2.1 OK for: string_decoder@1.3.0 want: ~5.2.0
1929 silly placeDep ROOT is-number@7.0.0 OK for: to-regex-range@5.0.1 want: ^7.0.0
1930 silly placeDep ROOT escalade@3.2.0 OK for: update-browserslist-db@1.1.3 want: ^3.2.0
1931 silly placeDep node_modules/util inherits@2.0.3 OK for: util@0.10.4 want: 2.0.3
1932 silly placeDep ROOT makeerror@1.0.12 OK for: walker@1.0.8 want: 1.0.12
1933 silly fetch manifest tmpl@1.0.5
1934 silly packumentCache full:https://registry.npmjs.org/tmpl cache-miss
1935 http fetch GET 200 https://registry.npmjs.org/tmpl 26ms (cache miss)
1936 silly packumentCache full:https://registry.npmjs.org/tmpl set size:undefined disposed:false
1937 silly placeDep ROOT tmpl@1.0.5 OK for: makeerror@1.0.12 want: 1.0.5
1938 silly placeDep ROOT isexe@2.0.0 OK for: which@2.0.2 want: ^2.0.0
1939 silly placeDep ROOT imurmurhash@0.1.4 OK for: write-file-atomic@4.0.2 want: ^0.1.4
1940 silly placeDep ROOT cliui@8.0.1 OK for: yargs@17.7.2 want: ^8.0.1
1941 silly placeDep ROOT get-caller-file@2.0.5 OK for: yargs@17.7.2 want: ^2.0.5
1942 silly placeDep ROOT require-directory@2.1.1 OK for: yargs@17.7.2 want: ^2.1.1
1943 silly placeDep ROOT string-width@4.2.3 OK for: yargs@17.7.2 want: ^4.2.3
1944 silly placeDep ROOT y18n@5.0.8 OK for: yargs@17.7.2 want: ^5.0.5
1945 silly placeDep ROOT yargs-parser@21.1.1 OK for: yargs@17.7.2 want: ^21.1.1
1946 silly fetch manifest wrap-ansi@^7.0.0
1947 silly packumentCache full:https://registry.npmjs.org/wrap-ansi cache-miss
1948 http fetch GET 200 https://registry.npmjs.org/wrap-ansi 38ms (cache miss)
1949 silly packumentCache full:https://registry.npmjs.org/wrap-ansi set size:undefined disposed:false
1950 silly fetch manifest string-width@^4.2.0
1951 silly packumentCache full:https://registry.npmjs.org/string-width cache-miss
1952 http cache https://registry.npmjs.org/string-width 2ms (cache hit)
1953 silly packumentCache full:https://registry.npmjs.org/string-width set size:61460 disposed:false
1954 silly fetch manifest emoji-regex@^8.0.0
1955 silly packumentCache full:https://registry.npmjs.org/emoji-regex cache-miss
1956 http fetch GET 200 https://registry.npmjs.org/emoji-regex 30ms (cache miss)
1957 silly packumentCache full:https://registry.npmjs.org/emoji-regex set size:undefined disposed:false
1958 silly fetch manifest is-fullwidth-code-point@^3.0.0
1959 silly packumentCache full:https://registry.npmjs.org/is-fullwidth-code-point cache-miss
1960 http fetch GET 200 https://registry.npmjs.org/is-fullwidth-code-point 22ms (cache miss)
1961 silly packumentCache full:https://registry.npmjs.org/is-fullwidth-code-point set size:undefined disposed:false
1962 silly placeDep ROOT wrap-ansi@7.0.0 OK for: cliui@8.0.1 want: ^7.0.0
1963 silly placeDep ROOT emoji-regex@8.0.0 OK for: string-width@4.2.3 want: ^8.0.0
1964 silly placeDep ROOT is-fullwidth-code-point@3.0.0 OK for: string-width@4.2.3 want: ^3.0.0
1965 silly placeDep node_modules/nodemon has-flag@3.0.0 OK for: supports-color@5.5.0 want: ^3.0.0
1966 silly placeDep ROOT p-try@2.2.0 OK for: p-limit@2.3.0 want: ^2.0.0
1967 silly reify moves {}
1968 silly audit bulk request {
1968 silly audit   '@discordjs/builders': [ '1.11.2' ],
1968 silly audit   '@discordjs/rest': [ '2.5.1' ],
1968 silly audit   '@discordjs/voice': [ '0.18.0' ],
1968 silly audit   '@types/cors': [ '2.8.17' ],
1968 silly audit   '@types/express': [ '5.0.0' ],
1968 silly audit   'better-sqlite3': [ '12.2.0' ],
1968 silly audit   'cross-env': [ '7.0.3' ],
1968 silly audit   'discord.js': [ '14.21.0' ],
1968 silly audit   dotenv: [ '16.6.1' ],
1968 silly audit   fs: [ '0.0.1-security' ],
1968 silly audit   jest: [ '29.7.0' ],
1968 silly audit   nodemon: [ '3.1.7' ],
1968 silly audit   path: [ '0.12.7' ],
1968 silly audit   typescript: [ '5.7.3' ],
1968 silly audit   '@discordjs/formatters': [ '0.6.1' ],
1968 silly audit   '@discordjs/util': [ '1.1.1' ],
1968 silly audit   '@sapphire/shapeshift': [ '4.0.0' ],
1968 silly audit   'discord-api-types': [ '0.38.17', '0.37.120' ],
1968 silly audit   'fast-deep-equal': [ '3.1.3' ],
1968 silly audit   'ts-mixer': [ '6.0.4' ],
1968 silly audit   tslib: [ '2.8.1' ],
1968 silly audit   '@discordjs/collection': [ '2.1.1', '1.5.3' ],
1968 silly audit   '@sapphire/async-queue': [ '1.5.5' ],
1968 silly audit   '@sapphire/snowflake': [ '3.5.5', '3.5.3' ],
1968 silly audit   '@vladfrangu/async_event_emitter': [ '2.4.6' ],
1968 silly audit   'magic-bytes.js': [ '1.12.1' ],
1968 silly audit   undici: [ '6.21.3' ],
1968 silly audit   '@types/ws': [ '8.18.1' ],
1968 silly audit   'prism-media': [ '1.3.5' ],
1968 silly audit   ws: [ '8.18.3' ],
1968 silly audit   lodash: [ '4.17.21' ],
1968 silly audit   '@types/node': [ '24.1.0' ],
1968 silly audit   '@types/body-parser': [ '1.19.6' ],
1968 silly audit   '@types/express-serve-static-core': [ '5.0.7' ],
1968 silly audit   '@types/qs': [ '6.14.0' ],
1968 silly audit   '@types/serve-static': [ '1.15.8' ],
1968 silly audit   '@types/connect': [ '3.4.38' ],
1968 silly audit   '@types/range-parser': [ '1.2.7' ],
1968 silly audit   '@types/send': [ '0.17.5' ],
1968 silly audit   'undici-types': [ '7.8.0' ],
1968 silly audit   '@types/mime': [ '1.3.5' ],
1968 silly audit   '@types/http-errors': [ '2.0.5' ],
1968 silly audit   bindings: [ '1.5.0' ],
1968 silly audit   'prebuild-install': [ '7.1.3' ],
1968 silly audit   'file-uri-to-path': [ '1.0.0' ],
1968 silly audit   'cross-spawn': [ '7.0.6' ],
1968 silly audit   'path-key': [ '3.1.1' ],
1968 silly audit   'shebang-command': [ '2.0.0' ],
1968 silly audit   which: [ '2.0.2' ],
1968 silly audit   '@discordjs/ws': [ '1.2.3' ],
1968 silly audit   'lodash.snakecase': [ '4.1.1' ],
1968 silly audit   '@jest/core': [ '29.7.0' ],
1968 silly audit   '@jest/types': [ '29.6.3' ],
1968 silly audit   'import-local': [ '3.2.0' ],
1968 silly audit   'jest-cli': [ '29.7.0' ],
1968 silly audit   '@jest/console': [ '29.7.0' ],
1968 silly audit   '@jest/reporters': [ '29.7.0' ],
1968 silly audit   '@jest/test-result': [ '29.7.0' ],
1968 silly audit   '@jest/transform': [ '29.7.0' ],
1968 silly audit   'ansi-escapes': [ '4.3.2' ],
1968 silly audit   chalk: [ '4.1.2' ],
1968 silly audit   'ci-info': [ '3.9.0' ],
1968 silly audit   exit: [ '0.1.2' ],
1968 silly audit   'graceful-fs': [ '4.2.11' ],
1968 silly audit   'jest-changed-files': [ '29.7.0' ],
1968 silly audit   'jest-config': [ '29.7.0' ],
1968 silly audit   'jest-haste-map': [ '29.7.0' ],
1968 silly audit   'jest-message-util': [ '29.7.0' ],
1968 silly audit   'jest-regex-util': [ '29.6.3' ],
1968 silly audit   'jest-resolve': [ '29.7.0' ],
1968 silly audit   'jest-resolve-dependencies': [ '29.7.0' ],
1968 silly audit   'jest-runner': [ '29.7.0' ],
1968 silly audit   'jest-runtime': [ '29.7.0' ],
1968 silly audit   'jest-snapshot': [ '29.7.0' ],
1968 silly audit   'jest-util': [ '29.7.0' ],
1968 silly audit   'jest-validate': [ '29.7.0' ],
1968 silly audit   'jest-watcher': [ '29.7.0' ],
1968 silly audit   micromatch: [ '4.0.8' ],
1968 silly audit   'pretty-format': [ '29.7.0' ],
1968 silly audit   slash: [ '3.0.0' ],
1968 silly audit   'strip-ansi': [ '6.0.1' ],
1968 silly audit   '@bcoe/v8-coverage': [ '0.2.3' ],
1968 silly audit   '@jridgewell/trace-mapping': [ '0.3.29' ],
1968 silly audit   'collect-v8-coverage': [ '1.0.2' ],
1968 silly audit   glob: [ '7.2.3' ],
1968 silly audit   'istanbul-lib-coverage': [ '3.2.2' ],
1968 silly audit   'istanbul-lib-instrument': [ '6.0.3', '5.2.1' ],
1968 silly audit   'istanbul-lib-report': [ '3.0.1' ],
1968 silly audit   'istanbul-lib-source-maps': [ '4.0.1' ],
1968 silly audit   'istanbul-reports': [ '3.1.7' ],
1968 silly audit   'jest-worker': [ '29.7.0' ],
1968 silly audit   'string-length': [ '4.0.2' ],
1968 silly audit   'v8-to-istanbul': [ '9.3.0' ],
1968 silly audit   '@types/istanbul-lib-coverage': [ '2.0.6' ],
1968 silly audit   '@babel/core': [ '7.28.0' ],
1968 silly audit   'babel-plugin-istanbul': [ '6.1.1' ],
1968 silly audit   'convert-source-map': [ '2.0.0' ],
1968 silly audit   'fast-json-stable-stringify': [ '2.1.0' ],
1968 silly audit   pirates: [ '4.0.7' ],
1968 silly audit   'write-file-atomic': [ '4.0.2' ],
1968 silly audit   '@ampproject/remapping': [ '2.3.0' ],
1968 silly audit   '@babel/code-frame': [ '7.27.1' ],
1968 silly audit   '@babel/generator': [ '7.28.0' ],
1968 silly audit   '@babel/helper-compilation-targets': [ '7.27.2' ],
1968 silly audit   '@babel/helper-module-transforms': [ '7.27.3' ],
1968 silly audit   '@babel/helpers': [ '7.28.2' ],
1968 silly audit   '@babel/parser': [ '7.28.0' ],
1968 silly audit   '@babel/template': [ '7.27.2' ],
1968 silly audit   '@babel/traverse': [ '7.28.0' ],
1968 silly audit   '@babel/types': [ '7.28.2' ],
1968 silly audit   debug: [ '4.4.1' ],
1968 silly audit   gensync: [ '1.0.0-beta.2' ],
1968 silly audit   json5: [ '2.2.3' ],
1968 silly audit   semver: [ '6.3.1', '7.7.2' ],
1968 silly audit   '@jridgewell/gen-mapping': [ '0.3.12' ],
1968 silly audit   '@babel/helper-validator-identifier': [ '7.27.1' ],
1968 silly audit   'js-tokens': [ '4.0.0' ],
1968 silly audit   picocolors: [ '1.1.1' ],
1968 silly audit   jsesc: [ '3.1.0' ],
1968 silly audit   '@babel/compat-data': [ '7.28.0' ],
1968 silly audit   '@babel/helper-validator-option': [ '7.27.1' ],
1968 silly audit   browserslist: [ '4.25.1' ],
1968 silly audit   'lru-cache': [ '5.1.1' ],
1968 silly audit   '@babel/helper-module-imports': [ '7.27.1' ],
1968 silly audit   '@babel/helper-globals': [ '7.28.0' ],
1968 silly audit   '@babel/helper-string-parser': [ '7.27.1' ],
1968 silly audit   '@jest/schemas': [ '29.6.3' ],
1968 silly audit   '@types/istanbul-reports': [ '3.0.4' ],
1968 silly audit   '@types/yargs': [ '17.0.33' ],
1968 silly audit   '@sinclair/typebox': [ '0.27.8' ],
1968 silly audit   '@jridgewell/sourcemap-codec': [ '1.5.4' ],
1968 silly audit   '@jridgewell/resolve-uri': [ '3.1.2' ],
1968 silly audit   '@types/istanbul-lib-report': [ '3.0.3' ],
1968 silly audit   '@types/yargs-parser': [ '21.0.3' ],
1968 silly audit   'type-fest': [ '0.21.3' ],
1968 silly audit   '@babel/helper-plugin-utils': [ '7.27.1' ],
1968 silly audit   '@istanbuljs/load-nyc-config': [ '1.1.0' ],
1968 silly audit   '@istanbuljs/schema': [ '0.1.3' ],
1968 silly audit   'test-exclude': [ '6.0.0' ],
1968 silly audit   camelcase: [ '5.3.1', '6.3.0' ],
1968 silly audit   'find-up': [ '4.1.0' ],
1968 silly audit   'get-package-type': [ '0.1.0' ],
1968 silly audit   'js-yaml': [ '3.14.1' ],
1968 silly audit   'resolve-from': [ '5.0.0' ],
1968 silly audit   'caniuse-lite': [ '1.0.30001727' ],
1968 silly audit   'electron-to-chromium': [ '1.5.191' ],
1968 silly audit   'node-releases': [ '2.0.19' ],
1968 silly audit   'update-browserslist-db': [ '1.1.3' ],
1968 silly audit   'ansi-styles': [ '4.3.0', '5.2.0' ],
1968 silly audit   'supports-color': [ '7.2.0', '8.1.1', '5.5.0' ],
1968 silly audit   'color-convert': [ '2.0.1' ],
1968 silly audit   'color-name': [ '1.1.4' ],
1968 silly audit   ms: [ '2.1.3' ],
1968 silly audit   'locate-path': [ '5.0.0' ],
1968 silly audit   'path-exists': [ '4.0.0' ],
1968 silly audit   'fs.realpath': [ '1.0.0' ],
1968 silly audit   inflight: [ '1.0.6' ],
1968 silly audit   inherits: [ '2.0.4', '2.0.3' ],
1968 silly audit   minimatch: [ '3.1.2' ],
1968 silly audit   once: [ '1.4.0' ],
1968 silly audit   'path-is-absolute': [ '1.0.1' ],
1968 silly audit   'pkg-dir': [ '4.2.0' ],
1968 silly audit   'resolve-cwd': [ '3.0.0' ],
1968 silly audit   wrappy: [ '1.0.2' ],
1968 silly audit   'make-dir': [ '4.0.0' ],
1968 silly audit   'source-map': [ '0.6.1' ],
1968 silly audit   'html-escaper': [ '2.0.2' ],
1968 silly audit   execa: [ '5.1.1' ],
1968 silly audit   'p-limit': [ '3.1.0', '2.3.0' ],
1968 silly audit   'get-stream': [ '6.0.1' ],
1968 silly audit   'human-signals': [ '2.1.0' ],
1968 silly audit   'is-stream': [ '2.0.1' ],
1968 silly audit   'merge-stream': [ '2.0.0' ],
1968 silly audit   'npm-run-path': [ '4.0.1' ],
1968 silly audit   onetime: [ '5.1.2' ],
1968 silly audit   'signal-exit': [ '3.0.7' ],
1968 silly audit   'strip-final-newline': [ '2.0.0' ],
1968 silly audit   'create-jest': [ '29.7.0' ],
1968 silly audit   yargs: [ '17.7.2' ],
1968 silly audit   prompts: [ '2.4.2' ],
1968 silly audit   '@jest/test-sequencer': [ '29.7.0' ],
1968 silly audit   'babel-jest': [ '29.7.0' ],
1968 silly audit   deepmerge: [ '4.3.1' ],
1968 silly audit   'jest-circus': [ '29.7.0' ],
1968 silly audit   'jest-environment-node': [ '29.7.0' ],
1968 silly audit   'jest-get-type': [ '29.6.3' ],
1968 silly audit   'parse-json': [ '5.2.0' ],
1968 silly audit   'strip-json-comments': [ '3.1.1', '2.0.1' ],
1968 silly audit   '@types/babel__core': [ '7.20.5' ],
1968 silly audit   'babel-preset-jest': [ '29.6.3' ],
1968 silly audit   '@types/babel__generator': [ '7.27.0' ],
1968 silly audit   '@types/babel__template': [ '7.4.4' ],
1968 silly audit   '@types/babel__traverse': [ '7.20.7' ],
1968 silly audit   'babel-plugin-jest-hoist': [ '29.6.3' ],
1968 silly audit   'babel-preset-current-node-syntax': [ '1.1.0' ],
1968 silly audit   '@babel/plugin-syntax-async-generators': [ '7.8.4' ],
1968 silly audit   '@babel/plugin-syntax-bigint': [ '7.8.3' ],
1968 silly audit   '@babel/plugin-syntax-class-properties': [ '7.12.13' ],
1968 silly audit   '@babel/plugin-syntax-class-static-block': [ '7.14.5' ],
1968 silly audit   '@babel/plugin-syntax-import-attributes': [ '7.27.1' ],
1968 silly audit   '@babel/plugin-syntax-import-meta': [ '7.10.4' ],
1968 silly audit   '@babel/plugin-syntax-json-strings': [ '7.8.3' ],
1968 silly audit   '@babel/plugin-syntax-logical-assignment-operators': [ '7.10.4' ],
1968 silly audit   '@babel/plugin-syntax-nullish-coalescing-operator': [ '7.8.3' ],
1968 silly audit   '@babel/plugin-syntax-numeric-separator': [ '7.10.4' ],
1968 silly audit   '@babel/plugin-syntax-object-rest-spread': [ '7.8.3' ],
1968 silly audit   '@babel/plugin-syntax-optional-catch-binding': [ '7.8.3' ],
1968 silly audit   '@babel/plugin-syntax-optional-chaining': [ '7.8.3' ],
1968 silly audit   '@babel/plugin-syntax-private-property-in-object': [ '7.14.5' ],
1968 silly audit   '@babel/plugin-syntax-top-level-await': [ '7.14.5' ],
1968 silly audit   '@jest/environment': [ '29.7.0' ],
1968 silly audit   '@jest/expect': [ '29.7.0' ],
1968 silly audit   co: [ '4.6.0' ],
1968 silly audit   dedent: [ '1.6.0' ],
1968 silly audit   'is-generator-fn': [ '2.1.0' ],
1968 silly audit   'jest-each': [ '29.7.0' ],
1968 silly audit   'jest-matcher-utils': [ '29.7.0' ],
1968 silly audit   'pure-rand': [ '6.1.0' ],
1968 silly audit   'stack-utils': [ '2.0.6' ],
1968 silly audit   '@jest/fake-timers': [ '29.7.0' ],
1968 silly audit   'jest-mock': [ '29.7.0' ],
1968 silly audit   expect: [ '29.7.0' ],
1968 silly audit   '@sinonjs/fake-timers': [ '10.3.0' ],
1968 silly audit   '@sinonjs/commons': [ '3.0.1' ],
1968 silly audit   'type-detect': [ '4.0.8' ],
1968 silly audit   '@jest/expect-utils': [ '29.7.0' ],
1968 silly audit   '@types/graceful-fs': [ '4.1.9' ],
1968 silly audit   anymatch: [ '3.1.3' ],
1968 silly audit   'fb-watchman': [ '2.0.2' ],
1968 silly audit   fsevents: [ '2.3.3' ],
1968 silly audit   walker: [ '1.0.8' ],
1968 silly audit   'normalize-path': [ '3.0.0' ],
1968 silly audit   picomatch: [ '2.3.1' ],
1968 silly audit   bser: [ '2.1.1' ],
1968 silly audit   'node-int64': [ '0.4.0' ],
1968 silly audit   'jest-diff': [ '29.7.0' ],
1968 silly audit   'diff-sequences': [ '29.6.3' ],
1968 silly audit   '@types/stack-utils': [ '2.0.3' ],
1968 silly audit   'jest-pnp-resolver': [ '1.2.3' ],
1968 silly audit   resolve: [ '1.22.10' ],
1968 silly audit   'resolve.exports': [ '2.0.3' ],
1968 silly audit   emittery: [ '0.13.1' ],
1968 silly audit   'jest-docblock': [ '29.7.0' ],
1968 silly audit   'jest-leak-detector': [ '29.7.0' ],
1968 silly audit   'source-map-support': [ '0.5.13' ],
1968 silly audit   'detect-newline': [ '3.1.0' ],
1968 silly audit   '@jest/globals': [ '29.7.0' ],
1968 silly audit   '@jest/source-map': [ '29.6.3' ],
1968 silly audit   'cjs-module-lexer': [ '1.4.3' ],
1968 silly audit   'strip-bom': [ '4.0.0' ],
1968 silly audit   callsites: [ '3.1.0' ],
1968 silly audit   '@babel/plugin-syntax-jsx': [ '7.27.1' ],
1968 silly audit   '@babel/plugin-syntax-typescript': [ '7.27.1' ],
1968 silly audit   'natural-compare': [ '1.4.0' ],
1968 silly audit   leven: [ '3.1.0' ],
1968 silly audit   argparse: [ '1.0.10' ],
1968 silly audit   esprima: [ '4.0.1' ],
1968 silly audit   'sprintf-js': [ '1.0.3' ],
1968 silly audit   'p-locate': [ '4.1.0' ],
1968 silly audit   yallist: [ '3.1.1' ],
1968 silly audit   braces: [ '3.0.3' ],
1968 silly audit   'fill-range': [ '7.1.1' ],
1968 silly audit   'to-regex-range': [ '5.0.1' ],
1968 silly audit   'brace-expansion': [ '1.1.12' ],
1968 silly audit   'balanced-match': [ '1.0.2' ],
1968 silly audit   'concat-map': [ '0.0.1' ],
1968 silly audit   chokidar: [ '3.6.0' ],
1968 silly audit   'ignore-by-default': [ '1.0.1' ],
1968 silly audit   'pstree.remy': [ '1.1.8' ],
1968 silly audit   'simple-update-notifier': [ '2.0.0' ],
1968 silly audit   touch: [ '3.1.1' ],
1968 silly audit   undefsafe: [ '2.0.5' ],
1968 silly audit   'glob-parent': [ '5.1.2' ],
1968 silly audit   'is-binary-path': [ '2.1.0' ],
1968 silly audit   'is-glob': [ '4.0.3' ],
1968 silly audit   readdirp: [ '3.6.0' ],
1968 silly audit   'binary-extensions': [ '2.3.0' ],
1968 silly audit   'is-extglob': [ '2.1.1' ],
1968 silly audit   'mimic-fn': [ '2.1.0' ],
1968 silly audit   'yocto-queue': [ '0.1.0' ],
1968 silly audit   'error-ex': [ '1.3.2' ],
1968 silly audit   'json-parse-even-better-errors': [ '2.3.1' ],
1968 silly audit   'lines-and-columns': [ '1.2.4' ],
1968 silly audit   'is-arrayish': [ '0.2.1' ],
1968 silly audit   process: [ '0.11.10' ],
1968 silly audit   util: [ '0.10.4' ],
1968 silly audit   'detect-libc': [ '2.0.4' ],
1968 silly audit   'expand-template': [ '2.0.3' ],
1968 silly audit   'github-from-package': [ '0.0.0' ],
1968 silly audit   minimist: [ '1.2.8' ],
1968 silly audit   'mkdirp-classic': [ '0.5.3' ],
1968 silly audit   'napi-build-utils': [ '2.0.0' ],
1968 silly audit   'node-abi': [ '3.75.0' ],
1968 silly audit   pump: [ '3.0.3' ],
1968 silly audit   rc: [ '1.2.8' ],
1968 silly audit   'simple-get': [ '4.0.1' ],
1968 silly audit   'tar-fs': [ '2.1.3' ],
1968 silly audit   'tunnel-agent': [ '0.6.0' ],
1968 silly audit   'react-is': [ '18.3.1' ],
1968 silly audit   kleur: [ '3.0.3' ],
1968 silly audit   sisteransi: [ '1.0.5' ],
1968 silly audit   'end-of-stream': [ '1.4.5' ],
1968 silly audit   'deep-extend': [ '0.6.0' ],
1968 silly audit   ini: [ '1.3.8' ],
1968 silly audit   'is-core-module': [ '2.16.1' ],
1968 silly audit   'path-parse': [ '1.0.7' ],
1968 silly audit   'supports-preserve-symlinks-flag': [ '1.0.0' ],
1968 silly audit   hasown: [ '2.0.2' ],
1968 silly audit   'function-bind': [ '1.1.2' ],
1968 silly audit   'shebang-regex': [ '3.0.0' ],
1968 silly audit   'decompress-response': [ '6.0.0' ],
1968 silly audit   'simple-concat': [ '1.0.1' ],
1968 silly audit   'mimic-response': [ '3.1.0' ],
1968 silly audit   'buffer-from': [ '1.1.2' ],
1968 silly audit   'escape-string-regexp': [ '2.0.0' ],
1968 silly audit   'char-regex': [ '1.0.2' ],
1968 silly audit   'ansi-regex': [ '5.0.1' ],
1968 silly audit   'has-flag': [ '4.0.0', '3.0.0' ],
1968 silly audit   chownr: [ '1.1.4' ],
1968 silly audit   'tar-stream': [ '2.2.0' ],
1968 silly audit   bl: [ '4.1.0' ],
1968 silly audit   'fs-constants': [ '1.0.0' ],
1968 silly audit   'readable-stream': [ '3.6.2' ],
1968 silly audit   buffer: [ '5.7.1' ],
1968 silly audit   'base64-js': [ '1.5.1' ],
1968 silly audit   ieee754: [ '1.2.1' ],
1968 silly audit   string_decoder: [ '1.3.0' ],
1968 silly audit   'util-deprecate': [ '1.0.2' ],
1968 silly audit   'safe-buffer': [ '5.2.1' ],
1968 silly audit   'is-number': [ '7.0.0' ],
1968 silly audit   escalade: [ '3.2.0' ],
1968 silly audit   makeerror: [ '1.0.12' ],
1968 silly audit   tmpl: [ '1.0.5' ],
1968 silly audit   isexe: [ '2.0.0' ],
1968 silly audit   imurmurhash: [ '0.1.4' ],
1968 silly audit   cliui: [ '8.0.1' ],
1968 silly audit   'get-caller-file': [ '2.0.5' ],
1968 silly audit   'require-directory': [ '2.1.1' ],
1968 silly audit   'string-width': [ '4.2.3' ],
1968 silly audit   y18n: [ '5.0.8' ],
1968 silly audit   'yargs-parser': [ '21.1.1' ],
1968 silly audit   'wrap-ansi': [ '7.0.0' ],
1968 silly audit   'emoji-regex': [ '8.0.0' ],
1968 silly audit   'is-fullwidth-code-point': [ '3.0.0' ],
1968 silly audit   'p-try': [ '2.2.0' ]
1968 silly audit }
1969 verbose reify failed optional dependency /home/<USER>/node_modules/fsevents
1970 silly reify mark deleted [ '/home/<USER>/node_modules/fsevents' ]
1971 http cache p-try@https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz 0ms (cache hit)
1972 http cache emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz 0ms (cache hit)
1973 http cache is-fullwidth-code-point@https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz 0ms (cache hit)
1974 http cache wrap-ansi@https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz 0ms (cache hit)
1975 http cache y18n@https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz 0ms (cache hit)
1976 http cache require-directory@https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz 0ms (cache hit)
1977 http cache get-caller-file@https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz 0ms (cache hit)
1978 http cache imurmurhash@https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz 0ms (cache hit)
1979 http cache isexe@https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz 0ms (cache hit)
1980 http cache string-width@https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz 0ms (cache hit)
1981 http cache cliui@https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz 0ms (cache hit)
1982 http cache tmpl@https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz 0ms (cache hit)
1983 http cache makeerror@https://registry.npmjs.org/makeerror/-/makeerror-1.0.12.tgz 0ms (cache hit)
1984 http cache is-number@https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz 0ms (cache hit)
1985 http cache escalade@https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz 0ms (cache hit)
1986 http cache safe-buffer@https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz 0ms (cache hit)
1987 http cache util-deprecate@https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz 0ms (cache hit)
1988 http cache string_decoder@https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz 0ms (cache hit)
1989 http cache ieee754@https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz 0ms (cache hit)
1990 http cache base64-js@https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz 0ms (cache hit)
1991 http cache buffer@https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz 0ms (cache hit)
1992 http cache readable-stream@https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz 0ms (cache hit)
1993 http cache bl@https://registry.npmjs.org/bl/-/bl-4.1.0.tgz 0ms (cache hit)
1994 http cache tar-stream@https://registry.npmjs.org/tar-stream/-/tar-stream-2.2.0.tgz 0ms (cache hit)
1995 http cache fs-constants@https://registry.npmjs.org/fs-constants/-/fs-constants-1.0.0.tgz 0ms (cache hit)
1996 http cache chownr@https://registry.npmjs.org/chownr/-/chownr-1.1.4.tgz 0ms (cache hit)
1997 http cache has-flag@https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz 0ms (cache hit)
1998 http cache ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz 0ms (cache hit)
1999 http cache buffer-from@https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz 0ms (cache hit)
2000 http cache char-regex@https://registry.npmjs.org/char-regex/-/char-regex-1.0.2.tgz 0ms (cache hit)
2001 http cache escape-string-regexp@https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz 0ms (cache hit)
2002 http cache mimic-response@https://registry.npmjs.org/mimic-response/-/mimic-response-3.1.0.tgz 0ms (cache hit)
2003 http cache simple-concat@https://registry.npmjs.org/simple-concat/-/simple-concat-1.0.1.tgz 0ms (cache hit)
2004 http cache decompress-response@https://registry.npmjs.org/decompress-response/-/decompress-response-6.0.0.tgz 0ms (cache hit)
2005 http cache shebang-regex@https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz 0ms (cache hit)
2006 http cache function-bind@https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz 0ms (cache hit)
2007 http cache yargs-parser@https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz 0ms (cache hit)
2008 http cache hasown@https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz 0ms (cache hit)
2009 http cache supports-preserve-symlinks-flag@https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz 0ms (cache hit)
2010 http cache is-core-module@https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz 0ms (cache hit)
2011 http cache path-parse@https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz 0ms (cache hit)
2012 http cache ini@https://registry.npmjs.org/ini/-/ini-1.3.8.tgz 0ms (cache hit)
2013 http cache deep-extend@https://registry.npmjs.org/deep-extend/-/deep-extend-0.6.0.tgz 0ms (cache hit)
2014 http cache kleur@https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz 0ms (cache hit)
2015 http cache sisteransi@https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz 0ms (cache hit)
2016 http cache end-of-stream@https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.5.tgz 0ms (cache hit)
2017 http cache react-is@https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz 0ms (cache hit)
2018 http cache tunnel-agent@https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz 0ms (cache hit)
2019 http cache simple-get@https://registry.npmjs.org/simple-get/-/simple-get-4.0.1.tgz 0ms (cache hit)
2020 http cache tar-fs@https://registry.npmjs.org/tar-fs/-/tar-fs-2.1.3.tgz 0ms (cache hit)
2021 http cache rc@https://registry.npmjs.org/rc/-/rc-1.2.8.tgz 0ms (cache hit)
2022 http cache pump@https://registry.npmjs.org/pump/-/pump-3.0.3.tgz 0ms (cache hit)
2023 http cache node-abi@https://registry.npmjs.org/node-abi/-/node-abi-3.75.0.tgz 0ms (cache hit)
2024 http cache mkdirp-classic@https://registry.npmjs.org/mkdirp-classic/-/mkdirp-classic-0.5.3.tgz 0ms (cache hit)
2025 http cache napi-build-utils@https://registry.npmjs.org/napi-build-utils/-/napi-build-utils-2.0.0.tgz 0ms (cache hit)
2026 http cache minimist@https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz 0ms (cache hit)
2027 http cache github-from-package@https://registry.npmjs.org/github-from-package/-/github-from-package-0.0.0.tgz 0ms (cache hit)
2028 http cache expand-template@https://registry.npmjs.org/expand-template/-/expand-template-2.0.3.tgz 0ms (cache hit)
2029 http cache util@https://registry.npmjs.org/util/-/util-0.10.4.tgz 0ms (cache hit)
2030 http cache process@https://registry.npmjs.org/process/-/process-0.11.10.tgz 0ms (cache hit)
2031 http cache detect-libc@https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.4.tgz 0ms (cache hit)
2032 http cache is-arrayish@https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz 0ms (cache hit)
2033 http cache lines-and-columns@https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz 1ms (cache hit)
2034 http cache json-parse-even-better-errors@https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz 0ms (cache hit)
2035 http cache error-ex@https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz 0ms (cache hit)
2036 http cache yocto-queue@https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz 0ms (cache hit)
2037 http cache mimic-fn@https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz 0ms (cache hit)
2038 http cache binary-extensions@https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz 0ms (cache hit)
2039 http cache readdirp@https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz 0ms (cache hit)
2040 http cache is-binary-path@https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz 0ms (cache hit)
2041 http cache is-glob@https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz 0ms (cache hit)
2042 http cache glob-parent@https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz 0ms (cache hit)
2043 http cache is-extglob@https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz 0ms (cache hit)
2044 http cache touch@https://registry.npmjs.org/touch/-/touch-3.1.1.tgz 0ms (cache hit)
2045 http cache undefsafe@https://registry.npmjs.org/undefsafe/-/undefsafe-2.0.5.tgz 0ms (cache hit)
2046 http cache pstree.remy@https://registry.npmjs.org/pstree.remy/-/pstree.remy-1.1.8.tgz 0ms (cache hit)
2047 http cache ignore-by-default@https://registry.npmjs.org/ignore-by-default/-/ignore-by-default-1.0.1.tgz 0ms (cache hit)
2048 http cache simple-update-notifier@https://registry.npmjs.org/simple-update-notifier/-/simple-update-notifier-2.0.0.tgz 0ms (cache hit)
2049 http cache chokidar@https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz 0ms (cache hit)
2050 http cache concat-map@https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz 0ms (cache hit)
2051 http cache balanced-match@https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz 0ms (cache hit)
2052 http cache brace-expansion@https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz 0ms (cache hit)
2053 http cache to-regex-range@https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz 0ms (cache hit)
2054 http cache fill-range@https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz 0ms (cache hit)
2055 http cache braces@https://registry.npmjs.org/braces/-/braces-3.0.3.tgz 0ms (cache hit)
2056 http cache p-locate@https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz 0ms (cache hit)
2057 http cache yallist@https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz 0ms (cache hit)
2058 http cache sprintf-js@https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz 0ms (cache hit)
2059 http cache esprima@https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz 0ms (cache hit)
2060 http cache argparse@https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz 0ms (cache hit)
2061 http cache natural-compare@https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz 0ms (cache hit)
2062 http cache leven@https://registry.npmjs.org/leven/-/leven-3.1.0.tgz 0ms (cache hit)
2063 http cache @babel/plugin-syntax-typescript@https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz 0ms (cache hit)
2064 http cache @babel/plugin-syntax-jsx@https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz 0ms (cache hit)
2065 http cache strip-bom@https://registry.npmjs.org/strip-bom/-/strip-bom-4.0.0.tgz 0ms (cache hit)
2066 http cache cjs-module-lexer@https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.4.3.tgz 0ms (cache hit)
2067 http cache callsites@https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz 0ms (cache hit)
2068 http cache @jest/globals@https://registry.npmjs.org/@jest/globals/-/globals-29.7.0.tgz 0ms (cache hit)
2069 http cache @jest/source-map@https://registry.npmjs.org/@jest/source-map/-/source-map-29.6.3.tgz 0ms (cache hit)
2070 http cache detect-newline@https://registry.npmjs.org/detect-newline/-/detect-newline-3.1.0.tgz 0ms (cache hit)
2071 http cache source-map-support@https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.13.tgz 0ms (cache hit)
2072 http cache jest-leak-detector@https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.7.0.tgz 0ms (cache hit)
2073 http cache resolve.exports@https://registry.npmjs.org/resolve.exports/-/resolve.exports-2.0.3.tgz 0ms (cache hit)
2074 http cache jest-docblock@https://registry.npmjs.org/jest-docblock/-/jest-docblock-29.7.0.tgz 0ms (cache hit)
2075 http cache resolve@https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz 0ms (cache hit)
2076 http cache jest-pnp-resolver@https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz 0ms (cache hit)
2077 http cache @types/stack-utils@https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.3.tgz 0ms (cache hit)
2078 http cache diff-sequences@https://registry.npmjs.org/diff-sequences/-/diff-sequences-29.6.3.tgz 0ms (cache hit)
2079 http cache jest-diff@https://registry.npmjs.org/jest-diff/-/jest-diff-29.7.0.tgz 0ms (cache hit)
2080 http cache node-int64@https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz 0ms (cache hit)
2081 http cache bser@https://registry.npmjs.org/bser/-/bser-2.1.1.tgz 0ms (cache hit)
2082 http cache emittery@https://registry.npmjs.org/emittery/-/emittery-0.13.1.tgz 0ms (cache hit)
2083 http cache picomatch@https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz 0ms (cache hit)
2084 http cache normalize-path@https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz 0ms (cache hit)
2085 http cache walker@https://registry.npmjs.org/walker/-/walker-1.0.8.tgz 0ms (cache hit)
2086 http cache anymatch@https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz 0ms (cache hit)
2087 http cache @types/graceful-fs@https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.9.tgz 0ms (cache hit)
2088 http cache fb-watchman@https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.2.tgz 0ms (cache hit)
2089 http cache type-detect@https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz 0ms (cache hit)
2090 http cache @jest/expect-utils@https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.7.0.tgz 0ms (cache hit)
2091 http cache @sinonjs/commons@https://registry.npmjs.org/@sinonjs/commons/-/commons-3.0.1.tgz 0ms (cache hit)
2092 http cache @sinonjs/fake-timers@https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz 0ms (cache hit)
2093 http cache expect@https://registry.npmjs.org/expect/-/expect-29.7.0.tgz 0ms (cache hit)
2094 http cache @jest/fake-timers@https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.7.0.tgz 0ms (cache hit)
2095 http cache jest-mock@https://registry.npmjs.org/jest-mock/-/jest-mock-29.7.0.tgz 0ms (cache hit)
2096 http cache stack-utils@https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.6.tgz 0ms (cache hit)
2097 http cache pure-rand@https://registry.npmjs.org/pure-rand/-/pure-rand-6.1.0.tgz 0ms (cache hit)
2098 http cache jest-each@https://registry.npmjs.org/jest-each/-/jest-each-29.7.0.tgz 0ms (cache hit)
2099 http cache jest-matcher-utils@https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-29.7.0.tgz 0ms (cache hit)
2100 http cache is-generator-fn@https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-2.1.0.tgz 0ms (cache hit)
2101 http cache dedent@https://registry.npmjs.org/dedent/-/dedent-1.6.0.tgz 0ms (cache hit)
2102 http cache @jest/expect@https://registry.npmjs.org/@jest/expect/-/expect-29.7.0.tgz 0ms (cache hit)
2103 http cache co@https://registry.npmjs.org/co/-/co-4.6.0.tgz 0ms (cache hit)
2104 http cache @jest/environment@https://registry.npmjs.org/@jest/environment/-/environment-29.7.0.tgz 0ms (cache hit)
2105 http cache @babel/plugin-syntax-top-level-await@https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz 0ms (cache hit)
2106 http cache @babel/plugin-syntax-private-property-in-object@https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz 0ms (cache hit)
2107 http cache @babel/plugin-syntax-optional-chaining@https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz 0ms (cache hit)
2108 http cache @babel/plugin-syntax-optional-catch-binding@https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz 0ms (cache hit)
2109 http cache @babel/plugin-syntax-nullish-coalescing-operator@https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz 0ms (cache hit)
2110 http cache @babel/plugin-syntax-numeric-separator@https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz 0ms (cache hit)
2111 http cache @babel/plugin-syntax-object-rest-spread@https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz 0ms (cache hit)
2112 http cache @babel/plugin-syntax-logical-assignment-operators@https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz 0ms (cache hit)
2113 http cache @babel/plugin-syntax-json-strings@https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz 0ms (cache hit)
2114 http cache @babel/plugin-syntax-import-meta@https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz 0ms (cache hit)
2115 http cache @babel/plugin-syntax-import-attributes@https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz 0ms (cache hit)
2116 http cache @babel/plugin-syntax-class-static-block@https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz 0ms (cache hit)
2117 http cache @babel/plugin-syntax-bigint@https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz 0ms (cache hit)
2118 http cache @babel/plugin-syntax-class-properties@https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz 0ms (cache hit)
2119 http cache @babel/plugin-syntax-async-generators@https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz 0ms (cache hit)
2120 http cache babel-preset-current-node-syntax@https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.1.0.tgz 0ms (cache hit)
2121 http cache @types/babel__template@https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz 0ms (cache hit)
2122 http cache babel-plugin-jest-hoist@https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.6.3.tgz 0ms (cache hit)
2123 http cache @types/babel__traverse@https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.7.tgz 0ms (cache hit)
2124 http cache @types/babel__generator@https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.27.0.tgz 0ms (cache hit)
2125 http cache @types/babel__core@https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz 0ms (cache hit)
2126 http cache strip-json-comments@https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz 1ms (cache hit)
2127 http cache babel-preset-jest@https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.6.3.tgz 0ms (cache hit)
2128 http cache parse-json@https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz 0ms (cache hit)
2129 http cache jest-get-type@https://registry.npmjs.org/jest-get-type/-/jest-get-type-29.6.3.tgz 0ms (cache hit)
2130 http cache jest-environment-node@https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-29.7.0.tgz 0ms (cache hit)
2131 http cache deepmerge@https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz 0ms (cache hit)
2132 http cache jest-circus@https://registry.npmjs.org/jest-circus/-/jest-circus-29.7.0.tgz 0ms (cache hit)
2133 http cache babel-jest@https://registry.npmjs.org/babel-jest/-/babel-jest-29.7.0.tgz 0ms (cache hit)
2134 http cache @jest/test-sequencer@https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.7.0.tgz 0ms (cache hit)
2135 http cache prompts@https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz 0ms (cache hit)
2136 http cache yargs@https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz 0ms (cache hit)
2137 http cache create-jest@https://registry.npmjs.org/create-jest/-/create-jest-29.7.0.tgz 0ms (cache hit)
2138 http cache strip-final-newline@https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz 0ms (cache hit)
2139 http cache signal-exit@https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz 0ms (cache hit)
2140 http cache onetime@https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz 0ms (cache hit)
2141 http cache npm-run-path@https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz 0ms (cache hit)
2142 http cache merge-stream@https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz 0ms (cache hit)
2143 http cache is-stream@https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz 0ms (cache hit)
2144 http cache human-signals@https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz 0ms (cache hit)
2145 http cache execa@https://registry.npmjs.org/execa/-/execa-5.1.1.tgz 0ms (cache hit)
2146 http cache p-limit@https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz 0ms (cache hit)
2147 http cache get-stream@https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz 0ms (cache hit)
2148 http cache html-escaper@https://registry.npmjs.org/html-escaper/-/html-escaper-2.0.2.tgz 1ms (cache hit)
2149 http cache source-map@https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz 0ms (cache hit)
2150 http cache make-dir@https://registry.npmjs.org/make-dir/-/make-dir-4.0.0.tgz 0ms (cache hit)
2151 http cache resolve-cwd@https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz 0ms (cache hit)
2152 http cache wrappy@https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz 0ms (cache hit)
2153 http cache once@https://registry.npmjs.org/once/-/once-1.4.0.tgz 0ms (cache hit)
2154 http cache path-is-absolute@https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz 0ms (cache hit)
2155 http cache pkg-dir@https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz 0ms (cache hit)
2156 http cache minimatch@https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz 0ms (cache hit)
2157 http cache inherits@https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz 0ms (cache hit)
2158 http cache inflight@https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz 0ms (cache hit)
2159 http cache path-exists@https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz 0ms (cache hit)
2160 http cache fs.realpath@https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz 0ms (cache hit)
2161 http cache locate-path@https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz 0ms (cache hit)
2162 http cache ms@https://registry.npmjs.org/ms/-/ms-2.1.3.tgz 0ms (cache hit)
2163 http cache color-name@https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz 0ms (cache hit)
2164 http cache color-convert@https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz 0ms (cache hit)
2165 http cache ansi-styles@https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz 0ms (cache hit)
2166 http cache supports-color@https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz 0ms (cache hit)
2167 http cache update-browserslist-db@https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz 0ms (cache hit)
2168 http cache node-releases@https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz 0ms (cache hit)
2169 http cache electron-to-chromium@https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.191.tgz 0ms (cache hit)
2170 http cache caniuse-lite@https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001727.tgz 0ms (cache hit)
2171 http cache resolve-from@https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz 0ms (cache hit)
2172 http cache js-yaml@https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz 0ms (cache hit)
2173 http cache find-up@https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz 0ms (cache hit)
2174 http cache get-package-type@https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz 0ms (cache hit)
2175 http cache camelcase@https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz 0ms (cache hit)
2176 http cache test-exclude@https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz 0ms (cache hit)
2177 http cache @istanbuljs/schema@https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz 0ms (cache hit)
2178 http cache @babel/helper-plugin-utils@https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz 0ms (cache hit)
2179 http cache @istanbuljs/load-nyc-config@https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz 0ms (cache hit)
2180 http cache @types/yargs-parser@https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.3.tgz 0ms (cache hit)
2181 http cache type-fest@https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz 0ms (cache hit)
2182 http cache @types/istanbul-lib-report@https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz 0ms (cache hit)
2183 http cache @jridgewell/resolve-uri@https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz 0ms (cache hit)
2184 http cache @jridgewell/sourcemap-codec@https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz 0ms (cache hit)
2185 http cache @sinclair/typebox@https://registry.npmjs.org/@sinclair/typebox/-/typebox-0.27.8.tgz 0ms (cache hit)
2186 http cache @types/yargs@https://registry.npmjs.org/@types/yargs/-/yargs-17.0.33.tgz 0ms (cache hit)
2187 http cache @babel/helper-string-parser@https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz 0ms (cache hit)
2188 http cache @types/istanbul-reports@https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz 0ms (cache hit)
2189 http cache @jest/schemas@https://registry.npmjs.org/@jest/schemas/-/schemas-29.6.3.tgz 0ms (cache hit)
2190 http cache @babel/helper-globals@https://registry.npmjs.org/@babel/helper-globals/-/helper-globals-7.28.0.tgz 0ms (cache hit)
2191 http cache @babel/helper-module-imports@https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz 0ms (cache hit)
2192 http cache browserslist@https://registry.npmjs.org/browserslist/-/browserslist-4.25.1.tgz 0ms (cache hit)
2193 http cache lru-cache@https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz 0ms (cache hit)
2194 http cache @babel/compat-data@https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.28.0.tgz 0ms (cache hit)
2195 http cache jsesc@https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz 0ms (cache hit)
2196 http cache picocolors@https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz 0ms (cache hit)
2197 http cache js-tokens@https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz 0ms (cache hit)
2198 http cache @babel/helper-validator-identifier@https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz 0ms (cache hit)
2199 http cache @jridgewell/gen-mapping@https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz 0ms (cache hit)
2200 http cache semver@https://registry.npmjs.org/semver/-/semver-6.3.1.tgz 0ms (cache hit)
2201 http cache json5@https://registry.npmjs.org/json5/-/json5-2.2.3.tgz 0ms (cache hit)
2202 http cache gensync@https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz 0ms (cache hit)
2203 http cache debug@https://registry.npmjs.org/debug/-/debug-4.4.1.tgz 0ms (cache hit)
2204 http cache @babel/types@https://registry.npmjs.org/@babel/types/-/types-7.28.2.tgz 0ms (cache hit)
2205 http cache @babel/template@https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz 0ms (cache hit)
2206 http cache @babel/traverse@https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.0.tgz 0ms (cache hit)
2207 http cache @babel/helper-validator-option@https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz 0ms (cache hit)
2208 http cache @babel/helpers@https://registry.npmjs.org/@babel/helpers/-/helpers-7.28.2.tgz 0ms (cache hit)
2209 http cache @babel/parser@https://registry.npmjs.org/@babel/parser/-/parser-7.28.0.tgz 0ms (cache hit)
2210 http cache @babel/helper-module-transforms@https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz 0ms (cache hit)
2211 http cache @babel/generator@https://registry.npmjs.org/@babel/generator/-/generator-7.28.0.tgz 0ms (cache hit)
2212 http cache @babel/code-frame@https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz 0ms (cache hit)
2213 http cache @babel/helper-compilation-targets@https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz 0ms (cache hit)
2214 http cache @ampproject/remapping@https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz 0ms (cache hit)
2215 http cache write-file-atomic@https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-4.0.2.tgz 0ms (cache hit)
2216 http cache fast-json-stable-stringify@https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz 0ms (cache hit)
2217 http cache pirates@https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz 0ms (cache hit)
2218 http cache babel-plugin-istanbul@https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz 0ms (cache hit)
2219 http cache convert-source-map@https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz 0ms (cache hit)
2220 http cache @babel/core@https://registry.npmjs.org/@babel/core/-/core-7.28.0.tgz 0ms (cache hit)
2221 http cache @types/istanbul-lib-coverage@https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz 0ms (cache hit)
2222 http cache v8-to-istanbul@https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz 0ms (cache hit)
2223 http cache string-length@https://registry.npmjs.org/string-length/-/string-length-4.0.2.tgz 0ms (cache hit)
2224 http cache jest-worker@https://registry.npmjs.org/jest-worker/-/jest-worker-29.7.0.tgz 0ms (cache hit)
2225 http cache istanbul-reports@https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.7.tgz 0ms (cache hit)
2226 http cache istanbul-lib-report@https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz 0ms (cache hit)
2227 http cache istanbul-lib-source-maps@https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz 0ms (cache hit)
2228 http cache istanbul-lib-coverage@https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz 0ms (cache hit)
2229 http cache istanbul-lib-instrument@https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz 0ms (cache hit)
2230 http cache glob@https://registry.npmjs.org/glob/-/glob-7.2.3.tgz 0ms (cache hit)
2231 http cache collect-v8-coverage@https://registry.npmjs.org/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz 0ms (cache hit)
2232 http cache @jridgewell/trace-mapping@https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz 0ms (cache hit)
2233 http cache strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 0ms (cache hit)
2234 http cache @bcoe/v8-coverage@https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz 0ms (cache hit)
2235 http cache pretty-format@https://registry.npmjs.org/pretty-format/-/pretty-format-29.7.0.tgz 0ms (cache hit)
2236 http cache slash@https://registry.npmjs.org/slash/-/slash-3.0.0.tgz 0ms (cache hit)
2237 http cache micromatch@https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz 0ms (cache hit)
2238 http cache jest-watcher@https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.7.0.tgz 0ms (cache hit)
2239 http cache jest-snapshot@https://registry.npmjs.org/jest-snapshot/-/jest-snapshot-29.7.0.tgz 0ms (cache hit)
2240 http cache jest-util@https://registry.npmjs.org/jest-util/-/jest-util-29.7.0.tgz 0ms (cache hit)
2241 http cache jest-validate@https://registry.npmjs.org/jest-validate/-/jest-validate-29.7.0.tgz 0ms (cache hit)
2242 http cache jest-runtime@https://registry.npmjs.org/jest-runtime/-/jest-runtime-29.7.0.tgz 0ms (cache hit)
2243 http cache jest-runner@https://registry.npmjs.org/jest-runner/-/jest-runner-29.7.0.tgz 0ms (cache hit)
2244 http cache jest-resolve-dependencies@https://registry.npmjs.org/jest-resolve-dependencies/-/jest-resolve-dependencies-29.7.0.tgz 0ms (cache hit)
2245 http cache jest-resolve@https://registry.npmjs.org/jest-resolve/-/jest-resolve-29.7.0.tgz 0ms (cache hit)
2246 http cache jest-regex-util@https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-29.6.3.tgz 0ms (cache hit)
2247 http cache jest-haste-map@https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-29.7.0.tgz 0ms (cache hit)
2248 http cache jest-message-util@https://registry.npmjs.org/jest-message-util/-/jest-message-util-29.7.0.tgz 0ms (cache hit)
2249 http cache jest-config@https://registry.npmjs.org/jest-config/-/jest-config-29.7.0.tgz 0ms (cache hit)
2250 http cache jest-changed-files@https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-29.7.0.tgz 0ms (cache hit)
2251 http cache ci-info@https://registry.npmjs.org/ci-info/-/ci-info-3.9.0.tgz 0ms (cache hit)
2252 http cache exit@https://registry.npmjs.org/exit/-/exit-0.1.2.tgz 0ms (cache hit)
2253 http cache graceful-fs@https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz 0ms (cache hit)
2254 http cache chalk@https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz 0ms (cache hit)
2255 http cache ansi-escapes@https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz 1ms (cache hit)
2256 http cache @jest/reporters@https://registry.npmjs.org/@jest/reporters/-/reporters-29.7.0.tgz 0ms (cache hit)
2257 http cache @jest/test-result@https://registry.npmjs.org/@jest/test-result/-/test-result-29.7.0.tgz 0ms (cache hit)
2258 http cache @jest/transform@https://registry.npmjs.org/@jest/transform/-/transform-29.7.0.tgz 0ms (cache hit)
2259 http cache @jest/console@https://registry.npmjs.org/@jest/console/-/console-29.7.0.tgz 0ms (cache hit)
2260 http cache jest-cli@https://registry.npmjs.org/jest-cli/-/jest-cli-29.7.0.tgz 0ms (cache hit)
2261 http cache @jest/types@https://registry.npmjs.org/@jest/types/-/types-29.6.3.tgz 0ms (cache hit)
2262 http cache @jest/core@https://registry.npmjs.org/@jest/core/-/core-29.7.0.tgz 0ms (cache hit)
2263 http cache import-local@https://registry.npmjs.org/import-local/-/import-local-3.2.0.tgz 0ms (cache hit)
2264 http cache lodash.snakecase@https://registry.npmjs.org/lodash.snakecase/-/lodash.snakecase-4.1.1.tgz 0ms (cache hit)
2265 http cache @discordjs/ws@https://registry.npmjs.org/@discordjs/ws/-/ws-1.2.3.tgz 0ms (cache hit)
2266 http cache shebang-command@https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz 0ms (cache hit)
2267 http cache path-key@https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz 0ms (cache hit)
2268 http cache cross-spawn@https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz 0ms (cache hit)
2269 http cache which@https://registry.npmjs.org/which/-/which-2.0.2.tgz 0ms (cache hit)
2270 http cache file-uri-to-path@https://registry.npmjs.org/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz 0ms (cache hit)
2271 http cache prebuild-install@https://registry.npmjs.org/prebuild-install/-/prebuild-install-7.1.3.tgz 0ms (cache hit)
2272 http cache @types/http-errors@https://registry.npmjs.org/@types/http-errors/-/http-errors-2.0.5.tgz 0ms (cache hit)
2273 http cache @types/mime@https://registry.npmjs.org/@types/mime/-/mime-1.3.5.tgz 0ms (cache hit)
2274 http cache bindings@https://registry.npmjs.org/bindings/-/bindings-1.5.0.tgz 0ms (cache hit)
2275 http cache @types/send@https://registry.npmjs.org/@types/send/-/send-0.17.5.tgz 0ms (cache hit)
2276 http cache undici-types@https://registry.npmjs.org/undici-types/-/undici-types-7.8.0.tgz 0ms (cache hit)
2277 http cache @types/connect@https://registry.npmjs.org/@types/connect/-/connect-3.4.38.tgz 0ms (cache hit)
2278 http cache @types/serve-static@https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.8.tgz 0ms (cache hit)
2279 http cache @types/range-parser@https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.7.tgz 0ms (cache hit)
2280 http cache @types/qs@https://registry.npmjs.org/@types/qs/-/qs-6.14.0.tgz 0ms (cache hit)
2281 http cache @types/express-serve-static-core@https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-5.0.7.tgz 0ms (cache hit)
2282 http cache @types/body-parser@https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.6.tgz 0ms (cache hit)
2283 http cache lodash@https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz 0ms (cache hit)
2284 http cache ws@https://registry.npmjs.org/ws/-/ws-8.18.3.tgz 0ms (cache hit)
2285 http cache @types/node@https://registry.npmjs.org/@types/node/-/node-24.1.0.tgz 0ms (cache hit)
2286 http cache @types/ws@https://registry.npmjs.org/@types/ws/-/ws-8.18.1.tgz 0ms (cache hit)
2287 http cache prism-media@https://registry.npmjs.org/prism-media/-/prism-media-1.3.5.tgz 0ms (cache hit)
2288 http cache magic-bytes.js@https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.12.1.tgz 0ms (cache hit)
2289 http cache undici@https://registry.npmjs.org/undici/-/undici-6.21.3.tgz 0ms (cache hit)
2290 http cache @vladfrangu/async_event_emitter@https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.4.6.tgz 0ms (cache hit)
2291 http cache @sapphire/snowflake@https://registry.npmjs.org/@sapphire/snowflake/-/snowflake-3.5.5.tgz 0ms (cache hit)
2292 http cache @discordjs/collection@https://registry.npmjs.org/@discordjs/collection/-/collection-2.1.1.tgz 0ms (cache hit)
2293 http cache tslib@https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz 0ms (cache hit)
2294 http cache @sapphire/async-queue@https://registry.npmjs.org/@sapphire/async-queue/-/async-queue-1.5.5.tgz 0ms (cache hit)
2295 http cache discord-api-types@https://registry.npmjs.org/discord-api-types/-/discord-api-types-0.38.17.tgz 0ms (cache hit)
2296 http cache ts-mixer@https://registry.npmjs.org/ts-mixer/-/ts-mixer-6.0.4.tgz 0ms (cache hit)
2297 http cache fast-deep-equal@https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz 0ms (cache hit)
2298 http cache @sapphire/shapeshift@https://registry.npmjs.org/@sapphire/shapeshift/-/shapeshift-4.0.0.tgz 0ms (cache hit)
2299 http cache @discordjs/util@https://registry.npmjs.org/@discordjs/util/-/util-1.1.1.tgz 0ms (cache hit)
2300 http cache @discordjs/formatters@https://registry.npmjs.org/@discordjs/formatters/-/formatters-0.6.1.tgz 0ms (cache hit)
2301 http cache typescript@https://registry.npmjs.org/typescript/-/typescript-5.7.3.tgz 0ms (cache hit)
2302 http cache path@https://registry.npmjs.org/path/-/path-0.12.7.tgz 0ms (cache hit)
2303 http cache jest@https://registry.npmjs.org/jest/-/jest-29.7.0.tgz 0ms (cache hit)
2304 http cache nodemon@https://registry.npmjs.org/nodemon/-/nodemon-3.1.7.tgz 0ms (cache hit)
2305 http cache fs@https://registry.npmjs.org/fs/-/fs-0.0.1-security.tgz 0ms (cache hit)
2306 http cache dotenv@https://registry.npmjs.org/dotenv/-/dotenv-16.6.1.tgz 0ms (cache hit)
2307 http cache discord.js@https://registry.npmjs.org/discord.js/-/discord.js-14.21.0.tgz 0ms (cache hit)
2308 http cache cross-env@https://registry.npmjs.org/cross-env/-/cross-env-7.0.3.tgz 0ms (cache hit)
2309 http cache better-sqlite3@https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-12.2.0.tgz 0ms (cache hit)
2310 http cache @types/express@https://registry.npmjs.org/@types/express/-/express-5.0.0.tgz 0ms (cache hit)
2311 http cache @types/cors@https://registry.npmjs.org/@types/cors/-/cors-2.8.17.tgz 0ms (cache hit)
2312 http cache @discordjs/voice@https://registry.npmjs.org/@discordjs/voice/-/voice-0.18.0.tgz 0ms (cache hit)
2313 http fetch POST 200 https://registry.npmjs.org/-/npm/v1/security/advisories/bulk 132ms
2314 silly audit report {}
2315 http cache @discordjs/builders@https://registry.npmjs.org/@discordjs/builders/-/builders-1.11.2.tgz 0ms (cache hit)
2316 http cache @discordjs/rest@https://registry.npmjs.org/@discordjs/rest/-/rest-2.5.1.tgz 0ms (cache hit)
2317 http cache strip-json-comments@https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz 0ms (cache hit)
2318 http cache semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 0ms (cache hit)
2319 http cache semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 0ms (cache hit)
2320 http cache inherits@https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz 0ms (cache hit)
2321 http cache p-limit@https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz 0ms (cache hit)
2322 http cache semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 0ms (cache hit)
2323 http cache semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 0ms (cache hit)
2324 http cache supports-color@https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz 0ms (cache hit)
2325 http cache istanbul-lib-instrument@https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz 0ms (cache hit)
2326 http cache ansi-styles@https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz 0ms (cache hit)
2327 http cache camelcase@https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz 0ms (cache hit)
2328 http cache has-flag@https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz 0ms (cache hit)
2329 http cache supports-color@https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz 0ms (cache hit)
2330 http cache semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 1ms (cache hit)
2331 http cache @sapphire/snowflake@https://registry.npmjs.org/@sapphire/snowflake/-/snowflake-3.5.3.tgz 0ms (cache hit)
2332 http cache semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 0ms (cache hit)
2333 http cache @discordjs/collection@https://registry.npmjs.org/@discordjs/collection/-/collection-1.5.3.tgz 0ms (cache hit)
2334 http cache discord-api-types@https://registry.npmjs.org/discord-api-types/-/discord-api-types-0.37.120.tgz 0ms (cache hit)
2335 silly tarball no local data for p-try@https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz. Extracting by manifest.
2336 silly tarball no local data for emoji-regex@https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz. Extracting by manifest.
2337 silly tarball no local data for is-fullwidth-code-point@https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz. Extracting by manifest.
2338 silly tarball no local data for wrap-ansi@https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz. Extracting by manifest.
2339 silly tarball no local data for y18n@https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz. Extracting by manifest.
2340 silly tarball no local data for require-directory@https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz. Extracting by manifest.
2341 silly tarball no local data for get-caller-file@https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz. Extracting by manifest.
2342 silly tarball no local data for imurmurhash@https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz. Extracting by manifest.
2343 silly tarball no local data for isexe@https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz. Extracting by manifest.
2344 silly tarball no local data for string-width@https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz. Extracting by manifest.
2345 silly tarball no local data for cliui@https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz. Extracting by manifest.
2346 silly tarball no local data for tmpl@https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz. Extracting by manifest.
2347 silly tarball no local data for makeerror@https://registry.npmjs.org/makeerror/-/makeerror-1.0.12.tgz. Extracting by manifest.
2348 silly tarball no local data for is-number@https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz. Extracting by manifest.
2349 silly tarball no local data for escalade@https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz. Extracting by manifest.
2350 silly tarball no local data for safe-buffer@https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz. Extracting by manifest.
2351 silly tarball no local data for util-deprecate@https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz. Extracting by manifest.
2352 silly tarball no local data for string_decoder@https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz. Extracting by manifest.
2353 silly tarball no local data for ieee754@https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz. Extracting by manifest.
2354 silly tarball no local data for base64-js@https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz. Extracting by manifest.
2355 silly tarball no local data for buffer@https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz. Extracting by manifest.
2356 silly tarball no local data for readable-stream@https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz. Extracting by manifest.
2357 silly tarball no local data for bl@https://registry.npmjs.org/bl/-/bl-4.1.0.tgz. Extracting by manifest.
2358 silly tarball no local data for tar-stream@https://registry.npmjs.org/tar-stream/-/tar-stream-2.2.0.tgz. Extracting by manifest.
2359 silly tarball no local data for fs-constants@https://registry.npmjs.org/fs-constants/-/fs-constants-1.0.0.tgz. Extracting by manifest.
2360 silly tarball no local data for chownr@https://registry.npmjs.org/chownr/-/chownr-1.1.4.tgz. Extracting by manifest.
2361 silly tarball no local data for has-flag@https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz. Extracting by manifest.
2362 silly tarball no local data for ansi-regex@https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz. Extracting by manifest.
2363 silly tarball no local data for buffer-from@https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz. Extracting by manifest.
2364 silly tarball no local data for char-regex@https://registry.npmjs.org/char-regex/-/char-regex-1.0.2.tgz. Extracting by manifest.
2365 silly tarball no local data for escape-string-regexp@https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz. Extracting by manifest.
2366 silly tarball no local data for mimic-response@https://registry.npmjs.org/mimic-response/-/mimic-response-3.1.0.tgz. Extracting by manifest.
2367 silly tarball no local data for simple-concat@https://registry.npmjs.org/simple-concat/-/simple-concat-1.0.1.tgz. Extracting by manifest.
2368 silly tarball no local data for decompress-response@https://registry.npmjs.org/decompress-response/-/decompress-response-6.0.0.tgz. Extracting by manifest.
2369 silly tarball no local data for shebang-regex@https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz. Extracting by manifest.
2370 silly tarball no local data for function-bind@https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz. Extracting by manifest.
2371 silly tarball no local data for yargs-parser@https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz. Extracting by manifest.
2372 silly tarball no local data for hasown@https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz. Extracting by manifest.
2373 silly tarball no local data for supports-preserve-symlinks-flag@https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz. Extracting by manifest.
2374 silly tarball no local data for is-core-module@https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz. Extracting by manifest.
2375 silly tarball no local data for path-parse@https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz. Extracting by manifest.
2376 silly tarball no local data for ini@https://registry.npmjs.org/ini/-/ini-1.3.8.tgz. Extracting by manifest.
2377 silly tarball no local data for deep-extend@https://registry.npmjs.org/deep-extend/-/deep-extend-0.6.0.tgz. Extracting by manifest.
2378 silly tarball no local data for kleur@https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz. Extracting by manifest.
2379 silly tarball no local data for sisteransi@https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz. Extracting by manifest.
2380 silly tarball no local data for end-of-stream@https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.5.tgz. Extracting by manifest.
2381 silly tarball no local data for react-is@https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz. Extracting by manifest.
2382 silly tarball no local data for tunnel-agent@https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz. Extracting by manifest.
2383 silly tarball no local data for simple-get@https://registry.npmjs.org/simple-get/-/simple-get-4.0.1.tgz. Extracting by manifest.
2384 silly tarball no local data for tar-fs@https://registry.npmjs.org/tar-fs/-/tar-fs-2.1.3.tgz. Extracting by manifest.
2385 silly tarball no local data for rc@https://registry.npmjs.org/rc/-/rc-1.2.8.tgz. Extracting by manifest.
2386 silly tarball no local data for pump@https://registry.npmjs.org/pump/-/pump-3.0.3.tgz. Extracting by manifest.
2387 silly tarball no local data for node-abi@https://registry.npmjs.org/node-abi/-/node-abi-3.75.0.tgz. Extracting by manifest.
2388 silly tarball no local data for mkdirp-classic@https://registry.npmjs.org/mkdirp-classic/-/mkdirp-classic-0.5.3.tgz. Extracting by manifest.
2389 silly tarball no local data for napi-build-utils@https://registry.npmjs.org/napi-build-utils/-/napi-build-utils-2.0.0.tgz. Extracting by manifest.
2390 silly tarball no local data for minimist@https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz. Extracting by manifest.
2391 silly tarball no local data for github-from-package@https://registry.npmjs.org/github-from-package/-/github-from-package-0.0.0.tgz. Extracting by manifest.
2392 silly tarball no local data for expand-template@https://registry.npmjs.org/expand-template/-/expand-template-2.0.3.tgz. Extracting by manifest.
2393 silly tarball no local data for util@https://registry.npmjs.org/util/-/util-0.10.4.tgz. Extracting by manifest.
2394 silly tarball no local data for process@https://registry.npmjs.org/process/-/process-0.11.10.tgz. Extracting by manifest.
2395 silly tarball no local data for detect-libc@https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.4.tgz. Extracting by manifest.
2396 silly tarball no local data for is-arrayish@https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz. Extracting by manifest.
2397 silly tarball no local data for lines-and-columns@https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz. Extracting by manifest.
2398 silly tarball no local data for json-parse-even-better-errors@https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz. Extracting by manifest.
2399 silly tarball no local data for error-ex@https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz. Extracting by manifest.
2400 silly tarball no local data for yocto-queue@https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz. Extracting by manifest.
2401 silly tarball no local data for mimic-fn@https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz. Extracting by manifest.
2402 silly tarball no local data for binary-extensions@https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz. Extracting by manifest.
2403 silly tarball no local data for readdirp@https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz. Extracting by manifest.
2404 silly tarball no local data for is-binary-path@https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz. Extracting by manifest.
2405 silly tarball no local data for is-glob@https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz. Extracting by manifest.
2406 silly tarball no local data for glob-parent@https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz. Extracting by manifest.
2407 silly tarball no local data for is-extglob@https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz. Extracting by manifest.
2408 silly tarball no local data for touch@https://registry.npmjs.org/touch/-/touch-3.1.1.tgz. Extracting by manifest.
2409 silly tarball no local data for undefsafe@https://registry.npmjs.org/undefsafe/-/undefsafe-2.0.5.tgz. Extracting by manifest.
2410 silly tarball no local data for pstree.remy@https://registry.npmjs.org/pstree.remy/-/pstree.remy-1.1.8.tgz. Extracting by manifest.
2411 silly tarball no local data for ignore-by-default@https://registry.npmjs.org/ignore-by-default/-/ignore-by-default-1.0.1.tgz. Extracting by manifest.
2412 silly tarball no local data for simple-update-notifier@https://registry.npmjs.org/simple-update-notifier/-/simple-update-notifier-2.0.0.tgz. Extracting by manifest.
2413 silly tarball no local data for chokidar@https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz. Extracting by manifest.
2414 silly tarball no local data for concat-map@https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz. Extracting by manifest.
2415 silly tarball no local data for balanced-match@https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz. Extracting by manifest.
2416 silly tarball no local data for brace-expansion@https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz. Extracting by manifest.
2417 silly tarball no local data for to-regex-range@https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz. Extracting by manifest.
2418 silly tarball no local data for fill-range@https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz. Extracting by manifest.
2419 silly tarball no local data for braces@https://registry.npmjs.org/braces/-/braces-3.0.3.tgz. Extracting by manifest.
2420 silly tarball no local data for p-locate@https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz. Extracting by manifest.
2421 silly tarball no local data for yallist@https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz. Extracting by manifest.
2422 silly tarball no local data for sprintf-js@https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz. Extracting by manifest.
2423 silly tarball no local data for esprima@https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz. Extracting by manifest.
2424 silly tarball no local data for argparse@https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz. Extracting by manifest.
2425 silly tarball no local data for natural-compare@https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz. Extracting by manifest.
2426 silly tarball no local data for leven@https://registry.npmjs.org/leven/-/leven-3.1.0.tgz. Extracting by manifest.
2427 silly tarball no local data for @babel/plugin-syntax-typescript@https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz. Extracting by manifest.
2428 silly tarball no local data for @babel/plugin-syntax-jsx@https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz. Extracting by manifest.
2429 silly tarball no local data for strip-bom@https://registry.npmjs.org/strip-bom/-/strip-bom-4.0.0.tgz. Extracting by manifest.
2430 silly tarball no local data for cjs-module-lexer@https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.4.3.tgz. Extracting by manifest.
2431 silly tarball no local data for callsites@https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz. Extracting by manifest.
2432 silly tarball no local data for @jest/globals@https://registry.npmjs.org/@jest/globals/-/globals-29.7.0.tgz. Extracting by manifest.
2433 silly tarball no local data for @jest/source-map@https://registry.npmjs.org/@jest/source-map/-/source-map-29.6.3.tgz. Extracting by manifest.
2434 silly tarball no local data for detect-newline@https://registry.npmjs.org/detect-newline/-/detect-newline-3.1.0.tgz. Extracting by manifest.
2435 silly tarball no local data for source-map-support@https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.13.tgz. Extracting by manifest.
2436 silly tarball no local data for jest-leak-detector@https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.7.0.tgz. Extracting by manifest.
2437 silly tarball no local data for resolve.exports@https://registry.npmjs.org/resolve.exports/-/resolve.exports-2.0.3.tgz. Extracting by manifest.
2438 silly tarball no local data for jest-docblock@https://registry.npmjs.org/jest-docblock/-/jest-docblock-29.7.0.tgz. Extracting by manifest.
2439 silly tarball no local data for resolve@https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz. Extracting by manifest.
2440 silly tarball no local data for jest-pnp-resolver@https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz. Extracting by manifest.
2441 silly tarball no local data for @types/stack-utils@https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.3.tgz. Extracting by manifest.
2442 silly tarball no local data for diff-sequences@https://registry.npmjs.org/diff-sequences/-/diff-sequences-29.6.3.tgz. Extracting by manifest.
2443 silly tarball no local data for jest-diff@https://registry.npmjs.org/jest-diff/-/jest-diff-29.7.0.tgz. Extracting by manifest.
2444 silly tarball no local data for node-int64@https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz. Extracting by manifest.
2445 silly tarball no local data for bser@https://registry.npmjs.org/bser/-/bser-2.1.1.tgz. Extracting by manifest.
2446 silly tarball no local data for emittery@https://registry.npmjs.org/emittery/-/emittery-0.13.1.tgz. Extracting by manifest.
2447 silly tarball no local data for picomatch@https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz. Extracting by manifest.
2448 silly tarball no local data for normalize-path@https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz. Extracting by manifest.
2449 silly tarball no local data for walker@https://registry.npmjs.org/walker/-/walker-1.0.8.tgz. Extracting by manifest.
2450 silly tarball no local data for anymatch@https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz. Extracting by manifest.
2451 silly tarball no local data for @types/graceful-fs@https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.9.tgz. Extracting by manifest.
2452 silly tarball no local data for fb-watchman@https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.2.tgz. Extracting by manifest.
2453 silly tarball no local data for type-detect@https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz. Extracting by manifest.
2454 silly tarball no local data for @jest/expect-utils@https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.7.0.tgz. Extracting by manifest.
2455 silly tarball no local data for @sinonjs/commons@https://registry.npmjs.org/@sinonjs/commons/-/commons-3.0.1.tgz. Extracting by manifest.
2456 silly tarball no local data for @sinonjs/fake-timers@https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz. Extracting by manifest.
2457 silly tarball no local data for expect@https://registry.npmjs.org/expect/-/expect-29.7.0.tgz. Extracting by manifest.
2458 silly tarball no local data for @jest/fake-timers@https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.7.0.tgz. Extracting by manifest.
2459 silly tarball no local data for jest-mock@https://registry.npmjs.org/jest-mock/-/jest-mock-29.7.0.tgz. Extracting by manifest.
2460 silly tarball no local data for stack-utils@https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.6.tgz. Extracting by manifest.
2461 silly tarball no local data for pure-rand@https://registry.npmjs.org/pure-rand/-/pure-rand-6.1.0.tgz. Extracting by manifest.
2462 silly tarball no local data for jest-each@https://registry.npmjs.org/jest-each/-/jest-each-29.7.0.tgz. Extracting by manifest.
2463 silly tarball no local data for jest-matcher-utils@https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-29.7.0.tgz. Extracting by manifest.
2464 silly tarball no local data for is-generator-fn@https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-2.1.0.tgz. Extracting by manifest.
2465 silly tarball no local data for dedent@https://registry.npmjs.org/dedent/-/dedent-1.6.0.tgz. Extracting by manifest.
2466 silly tarball no local data for @jest/expect@https://registry.npmjs.org/@jest/expect/-/expect-29.7.0.tgz. Extracting by manifest.
2467 silly tarball no local data for co@https://registry.npmjs.org/co/-/co-4.6.0.tgz. Extracting by manifest.
2468 silly tarball no local data for @jest/environment@https://registry.npmjs.org/@jest/environment/-/environment-29.7.0.tgz. Extracting by manifest.
2469 silly tarball no local data for @babel/plugin-syntax-top-level-await@https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz. Extracting by manifest.
2470 silly tarball no local data for @babel/plugin-syntax-private-property-in-object@https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz. Extracting by manifest.
2471 silly tarball no local data for @babel/plugin-syntax-optional-chaining@https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz. Extracting by manifest.
2472 silly tarball no local data for @babel/plugin-syntax-optional-catch-binding@https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz. Extracting by manifest.
2473 silly tarball no local data for @babel/plugin-syntax-nullish-coalescing-operator@https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz. Extracting by manifest.
2474 silly tarball no local data for @babel/plugin-syntax-numeric-separator@https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz. Extracting by manifest.
2475 silly tarball no local data for @babel/plugin-syntax-object-rest-spread@https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz. Extracting by manifest.
2476 silly tarball no local data for @babel/plugin-syntax-logical-assignment-operators@https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz. Extracting by manifest.
2477 silly tarball no local data for @babel/plugin-syntax-json-strings@https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz. Extracting by manifest.
2478 silly tarball no local data for @babel/plugin-syntax-import-meta@https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz. Extracting by manifest.
2479 silly tarball no local data for @babel/plugin-syntax-import-attributes@https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz. Extracting by manifest.
2480 silly tarball no local data for @babel/plugin-syntax-class-static-block@https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz. Extracting by manifest.
2481 silly tarball no local data for @babel/plugin-syntax-bigint@https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz. Extracting by manifest.
2482 silly tarball no local data for @babel/plugin-syntax-class-properties@https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz. Extracting by manifest.
2483 silly tarball no local data for @babel/plugin-syntax-async-generators@https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz. Extracting by manifest.
2484 silly tarball no local data for babel-preset-current-node-syntax@https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.1.0.tgz. Extracting by manifest.
2485 silly tarball no local data for @types/babel__template@https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz. Extracting by manifest.
2486 silly tarball no local data for babel-plugin-jest-hoist@https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.6.3.tgz. Extracting by manifest.
2487 silly tarball no local data for @types/babel__traverse@https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.7.tgz. Extracting by manifest.
2488 silly tarball no local data for @types/babel__generator@https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.27.0.tgz. Extracting by manifest.
2489 silly tarball no local data for @types/babel__core@https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz. Extracting by manifest.
2490 silly tarball no local data for strip-json-comments@https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz. Extracting by manifest.
2491 silly tarball no local data for babel-preset-jest@https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.6.3.tgz. Extracting by manifest.
2492 silly tarball no local data for parse-json@https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz. Extracting by manifest.
2493 silly tarball no local data for jest-get-type@https://registry.npmjs.org/jest-get-type/-/jest-get-type-29.6.3.tgz. Extracting by manifest.
2494 silly tarball no local data for jest-environment-node@https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-29.7.0.tgz. Extracting by manifest.
2495 silly tarball no local data for deepmerge@https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz. Extracting by manifest.
2496 silly tarball no local data for jest-circus@https://registry.npmjs.org/jest-circus/-/jest-circus-29.7.0.tgz. Extracting by manifest.
2497 silly tarball no local data for babel-jest@https://registry.npmjs.org/babel-jest/-/babel-jest-29.7.0.tgz. Extracting by manifest.
2498 silly tarball no local data for @jest/test-sequencer@https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.7.0.tgz. Extracting by manifest.
2499 silly tarball no local data for prompts@https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz. Extracting by manifest.
2500 silly tarball no local data for yargs@https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz. Extracting by manifest.
2501 silly tarball no local data for create-jest@https://registry.npmjs.org/create-jest/-/create-jest-29.7.0.tgz. Extracting by manifest.
2502 silly tarball no local data for strip-final-newline@https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz. Extracting by manifest.
2503 silly tarball no local data for signal-exit@https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz. Extracting by manifest.
2504 silly tarball no local data for onetime@https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz. Extracting by manifest.
2505 silly tarball no local data for npm-run-path@https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz. Extracting by manifest.
2506 silly tarball no local data for merge-stream@https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz. Extracting by manifest.
2507 silly tarball no local data for is-stream@https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz. Extracting by manifest.
2508 silly tarball no local data for human-signals@https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz. Extracting by manifest.
2509 silly tarball no local data for execa@https://registry.npmjs.org/execa/-/execa-5.1.1.tgz. Extracting by manifest.
2510 silly tarball no local data for p-limit@https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz. Extracting by manifest.
2511 silly tarball no local data for get-stream@https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz. Extracting by manifest.
2512 silly tarball no local data for html-escaper@https://registry.npmjs.org/html-escaper/-/html-escaper-2.0.2.tgz. Extracting by manifest.
2513 silly tarball no local data for source-map@https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz. Extracting by manifest.
2514 silly tarball no local data for make-dir@https://registry.npmjs.org/make-dir/-/make-dir-4.0.0.tgz. Extracting by manifest.
2515 silly tarball no local data for resolve-cwd@https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz. Extracting by manifest.
2516 silly tarball no local data for wrappy@https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz. Extracting by manifest.
2517 silly tarball no local data for once@https://registry.npmjs.org/once/-/once-1.4.0.tgz. Extracting by manifest.
2518 silly tarball no local data for path-is-absolute@https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz. Extracting by manifest.
2519 silly tarball no local data for pkg-dir@https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz. Extracting by manifest.
2520 silly tarball no local data for minimatch@https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz. Extracting by manifest.
2521 silly tarball no local data for inherits@https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz. Extracting by manifest.
2522 silly tarball no local data for inflight@https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz. Extracting by manifest.
2523 silly tarball no local data for path-exists@https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz. Extracting by manifest.
2524 silly tarball no local data for fs.realpath@https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz. Extracting by manifest.
2525 silly tarball no local data for locate-path@https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz. Extracting by manifest.
2526 silly tarball no local data for ms@https://registry.npmjs.org/ms/-/ms-2.1.3.tgz. Extracting by manifest.
2527 silly tarball no local data for color-name@https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz. Extracting by manifest.
2528 silly tarball no local data for color-convert@https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz. Extracting by manifest.
2529 silly tarball no local data for ansi-styles@https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz. Extracting by manifest.
2530 silly tarball no local data for supports-color@https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz. Extracting by manifest.
2531 silly tarball no local data for update-browserslist-db@https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz. Extracting by manifest.
2532 silly tarball no local data for node-releases@https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz. Extracting by manifest.
2533 silly tarball no local data for electron-to-chromium@https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.191.tgz. Extracting by manifest.
2534 silly tarball no local data for caniuse-lite@https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001727.tgz. Extracting by manifest.
2535 silly tarball no local data for resolve-from@https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz. Extracting by manifest.
2536 silly tarball no local data for js-yaml@https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz. Extracting by manifest.
2537 silly tarball no local data for find-up@https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz. Extracting by manifest.
2538 silly tarball no local data for get-package-type@https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz. Extracting by manifest.
2539 silly tarball no local data for camelcase@https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz. Extracting by manifest.
2540 silly tarball no local data for test-exclude@https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz. Extracting by manifest.
2541 silly tarball no local data for @istanbuljs/schema@https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz. Extracting by manifest.
2542 silly tarball no local data for @babel/helper-plugin-utils@https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz. Extracting by manifest.
2543 silly tarball no local data for @istanbuljs/load-nyc-config@https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz. Extracting by manifest.
2544 silly tarball no local data for @types/yargs-parser@https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.3.tgz. Extracting by manifest.
2545 silly tarball no local data for type-fest@https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz. Extracting by manifest.
2546 silly tarball no local data for @types/istanbul-lib-report@https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz. Extracting by manifest.
2547 silly tarball no local data for @jridgewell/resolve-uri@https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz. Extracting by manifest.
2548 silly tarball no local data for @jridgewell/sourcemap-codec@https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz. Extracting by manifest.
2549 silly tarball no local data for @sinclair/typebox@https://registry.npmjs.org/@sinclair/typebox/-/typebox-0.27.8.tgz. Extracting by manifest.
2550 silly tarball no local data for @types/yargs@https://registry.npmjs.org/@types/yargs/-/yargs-17.0.33.tgz. Extracting by manifest.
2551 silly tarball no local data for @babel/helper-string-parser@https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz. Extracting by manifest.
2552 silly tarball no local data for @types/istanbul-reports@https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz. Extracting by manifest.
2553 silly tarball no local data for @jest/schemas@https://registry.npmjs.org/@jest/schemas/-/schemas-29.6.3.tgz. Extracting by manifest.
2554 silly tarball no local data for @babel/helper-globals@https://registry.npmjs.org/@babel/helper-globals/-/helper-globals-7.28.0.tgz. Extracting by manifest.
2555 silly tarball no local data for @babel/helper-module-imports@https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz. Extracting by manifest.
2556 silly tarball no local data for browserslist@https://registry.npmjs.org/browserslist/-/browserslist-4.25.1.tgz. Extracting by manifest.
2557 silly tarball no local data for lru-cache@https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz. Extracting by manifest.
2558 silly tarball no local data for @babel/compat-data@https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.28.0.tgz. Extracting by manifest.
2559 silly tarball no local data for jsesc@https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz. Extracting by manifest.
2560 silly tarball no local data for picocolors@https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz. Extracting by manifest.
2561 silly tarball no local data for js-tokens@https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz. Extracting by manifest.
2562 silly tarball no local data for @babel/helper-validator-identifier@https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz. Extracting by manifest.
2563 silly tarball no local data for @jridgewell/gen-mapping@https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz. Extracting by manifest.
2564 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-6.3.1.tgz. Extracting by manifest.
2565 silly tarball no local data for json5@https://registry.npmjs.org/json5/-/json5-2.2.3.tgz. Extracting by manifest.
2566 silly tarball no local data for gensync@https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz. Extracting by manifest.
2567 silly tarball no local data for debug@https://registry.npmjs.org/debug/-/debug-4.4.1.tgz. Extracting by manifest.
2568 silly tarball no local data for @babel/types@https://registry.npmjs.org/@babel/types/-/types-7.28.2.tgz. Extracting by manifest.
2569 silly tarball no local data for @babel/template@https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz. Extracting by manifest.
2570 silly tarball no local data for @babel/traverse@https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.0.tgz. Extracting by manifest.
2571 silly tarball no local data for @babel/helper-validator-option@https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz. Extracting by manifest.
2572 silly tarball no local data for @babel/helpers@https://registry.npmjs.org/@babel/helpers/-/helpers-7.28.2.tgz. Extracting by manifest.
2573 silly tarball no local data for @babel/parser@https://registry.npmjs.org/@babel/parser/-/parser-7.28.0.tgz. Extracting by manifest.
2574 silly tarball no local data for @babel/helper-module-transforms@https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz. Extracting by manifest.
2575 silly tarball no local data for @babel/generator@https://registry.npmjs.org/@babel/generator/-/generator-7.28.0.tgz. Extracting by manifest.
2576 silly tarball no local data for @babel/code-frame@https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz. Extracting by manifest.
2577 silly tarball no local data for @babel/helper-compilation-targets@https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz. Extracting by manifest.
2578 silly tarball no local data for @ampproject/remapping@https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz. Extracting by manifest.
2579 silly tarball no local data for write-file-atomic@https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-4.0.2.tgz. Extracting by manifest.
2580 silly tarball no local data for fast-json-stable-stringify@https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz. Extracting by manifest.
2581 silly tarball no local data for pirates@https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz. Extracting by manifest.
2582 silly tarball no local data for babel-plugin-istanbul@https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz. Extracting by manifest.
2583 silly tarball no local data for convert-source-map@https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz. Extracting by manifest.
2584 silly tarball no local data for @babel/core@https://registry.npmjs.org/@babel/core/-/core-7.28.0.tgz. Extracting by manifest.
2585 silly tarball no local data for @types/istanbul-lib-coverage@https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz. Extracting by manifest.
2586 silly tarball no local data for v8-to-istanbul@https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz. Extracting by manifest.
2587 silly tarball no local data for string-length@https://registry.npmjs.org/string-length/-/string-length-4.0.2.tgz. Extracting by manifest.
2588 silly tarball no local data for jest-worker@https://registry.npmjs.org/jest-worker/-/jest-worker-29.7.0.tgz. Extracting by manifest.
2589 silly tarball no local data for istanbul-reports@https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.7.tgz. Extracting by manifest.
2590 silly tarball no local data for istanbul-lib-report@https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz. Extracting by manifest.
2591 silly tarball no local data for istanbul-lib-source-maps@https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz. Extracting by manifest.
2592 silly tarball no local data for istanbul-lib-coverage@https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz. Extracting by manifest.
2593 silly tarball no local data for istanbul-lib-instrument@https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz. Extracting by manifest.
2594 silly tarball no local data for glob@https://registry.npmjs.org/glob/-/glob-7.2.3.tgz. Extracting by manifest.
2595 silly tarball no local data for collect-v8-coverage@https://registry.npmjs.org/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz. Extracting by manifest.
2596 silly tarball no local data for @jridgewell/trace-mapping@https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz. Extracting by manifest.
2597 silly tarball no local data for strip-ansi@https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz. Extracting by manifest.
2598 silly tarball no local data for @bcoe/v8-coverage@https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz. Extracting by manifest.
2599 silly tarball no local data for pretty-format@https://registry.npmjs.org/pretty-format/-/pretty-format-29.7.0.tgz. Extracting by manifest.
2600 silly tarball no local data for slash@https://registry.npmjs.org/slash/-/slash-3.0.0.tgz. Extracting by manifest.
2601 silly tarball no local data for micromatch@https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz. Extracting by manifest.
2602 silly tarball no local data for jest-watcher@https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.7.0.tgz. Extracting by manifest.
2603 silly tarball no local data for jest-snapshot@https://registry.npmjs.org/jest-snapshot/-/jest-snapshot-29.7.0.tgz. Extracting by manifest.
2604 silly tarball no local data for jest-util@https://registry.npmjs.org/jest-util/-/jest-util-29.7.0.tgz. Extracting by manifest.
2605 silly tarball no local data for jest-validate@https://registry.npmjs.org/jest-validate/-/jest-validate-29.7.0.tgz. Extracting by manifest.
2606 silly tarball no local data for jest-runtime@https://registry.npmjs.org/jest-runtime/-/jest-runtime-29.7.0.tgz. Extracting by manifest.
2607 silly tarball no local data for jest-runner@https://registry.npmjs.org/jest-runner/-/jest-runner-29.7.0.tgz. Extracting by manifest.
2608 silly tarball no local data for jest-resolve-dependencies@https://registry.npmjs.org/jest-resolve-dependencies/-/jest-resolve-dependencies-29.7.0.tgz. Extracting by manifest.
2609 silly tarball no local data for jest-resolve@https://registry.npmjs.org/jest-resolve/-/jest-resolve-29.7.0.tgz. Extracting by manifest.
2610 silly tarball no local data for jest-regex-util@https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-29.6.3.tgz. Extracting by manifest.
2611 silly tarball no local data for jest-haste-map@https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-29.7.0.tgz. Extracting by manifest.
2612 silly tarball no local data for jest-message-util@https://registry.npmjs.org/jest-message-util/-/jest-message-util-29.7.0.tgz. Extracting by manifest.
2613 silly tarball no local data for jest-config@https://registry.npmjs.org/jest-config/-/jest-config-29.7.0.tgz. Extracting by manifest.
2614 silly tarball no local data for jest-changed-files@https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-29.7.0.tgz. Extracting by manifest.
2615 silly tarball no local data for ci-info@https://registry.npmjs.org/ci-info/-/ci-info-3.9.0.tgz. Extracting by manifest.
2616 silly tarball no local data for exit@https://registry.npmjs.org/exit/-/exit-0.1.2.tgz. Extracting by manifest.
2617 silly tarball no local data for graceful-fs@https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz. Extracting by manifest.
2618 silly tarball no local data for chalk@https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz. Extracting by manifest.
2619 silly tarball no local data for ansi-escapes@https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz. Extracting by manifest.
2620 silly tarball no local data for @jest/reporters@https://registry.npmjs.org/@jest/reporters/-/reporters-29.7.0.tgz. Extracting by manifest.
2621 silly tarball no local data for @jest/test-result@https://registry.npmjs.org/@jest/test-result/-/test-result-29.7.0.tgz. Extracting by manifest.
2622 silly tarball no local data for @jest/transform@https://registry.npmjs.org/@jest/transform/-/transform-29.7.0.tgz. Extracting by manifest.
2623 silly tarball no local data for @jest/console@https://registry.npmjs.org/@jest/console/-/console-29.7.0.tgz. Extracting by manifest.
2624 silly tarball no local data for jest-cli@https://registry.npmjs.org/jest-cli/-/jest-cli-29.7.0.tgz. Extracting by manifest.
2625 silly tarball no local data for @jest/types@https://registry.npmjs.org/@jest/types/-/types-29.6.3.tgz. Extracting by manifest.
2626 silly tarball no local data for @jest/core@https://registry.npmjs.org/@jest/core/-/core-29.7.0.tgz. Extracting by manifest.
2627 silly tarball no local data for import-local@https://registry.npmjs.org/import-local/-/import-local-3.2.0.tgz. Extracting by manifest.
2628 silly tarball no local data for lodash.snakecase@https://registry.npmjs.org/lodash.snakecase/-/lodash.snakecase-4.1.1.tgz. Extracting by manifest.
2629 silly tarball no local data for @discordjs/ws@https://registry.npmjs.org/@discordjs/ws/-/ws-1.2.3.tgz. Extracting by manifest.
2630 silly tarball no local data for shebang-command@https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz. Extracting by manifest.
2631 silly tarball no local data for path-key@https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz. Extracting by manifest.
2632 silly tarball no local data for cross-spawn@https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz. Extracting by manifest.
2633 silly tarball no local data for which@https://registry.npmjs.org/which/-/which-2.0.2.tgz. Extracting by manifest.
2634 silly tarball no local data for file-uri-to-path@https://registry.npmjs.org/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz. Extracting by manifest.
2635 silly tarball no local data for prebuild-install@https://registry.npmjs.org/prebuild-install/-/prebuild-install-7.1.3.tgz. Extracting by manifest.
2636 silly tarball no local data for @types/http-errors@https://registry.npmjs.org/@types/http-errors/-/http-errors-2.0.5.tgz. Extracting by manifest.
2637 silly tarball no local data for @types/mime@https://registry.npmjs.org/@types/mime/-/mime-1.3.5.tgz. Extracting by manifest.
2638 silly tarball no local data for bindings@https://registry.npmjs.org/bindings/-/bindings-1.5.0.tgz. Extracting by manifest.
2639 silly tarball no local data for @types/send@https://registry.npmjs.org/@types/send/-/send-0.17.5.tgz. Extracting by manifest.
2640 silly tarball no local data for undici-types@https://registry.npmjs.org/undici-types/-/undici-types-7.8.0.tgz. Extracting by manifest.
2641 silly tarball no local data for @types/connect@https://registry.npmjs.org/@types/connect/-/connect-3.4.38.tgz. Extracting by manifest.
2642 silly tarball no local data for @types/serve-static@https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.8.tgz. Extracting by manifest.
2643 silly tarball no local data for @types/range-parser@https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.7.tgz. Extracting by manifest.
2644 silly tarball no local data for @types/qs@https://registry.npmjs.org/@types/qs/-/qs-6.14.0.tgz. Extracting by manifest.
2645 silly tarball no local data for @types/express-serve-static-core@https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-5.0.7.tgz. Extracting by manifest.
2646 silly tarball no local data for @types/body-parser@https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.6.tgz. Extracting by manifest.
2647 silly tarball no local data for lodash@https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz. Extracting by manifest.
2648 silly tarball no local data for ws@https://registry.npmjs.org/ws/-/ws-8.18.3.tgz. Extracting by manifest.
2649 silly tarball no local data for @types/node@https://registry.npmjs.org/@types/node/-/node-24.1.0.tgz. Extracting by manifest.
2650 silly tarball no local data for @types/ws@https://registry.npmjs.org/@types/ws/-/ws-8.18.1.tgz. Extracting by manifest.
2651 silly tarball no local data for prism-media@https://registry.npmjs.org/prism-media/-/prism-media-1.3.5.tgz. Extracting by manifest.
2652 silly tarball no local data for magic-bytes.js@https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.12.1.tgz. Extracting by manifest.
2653 silly tarball no local data for undici@https://registry.npmjs.org/undici/-/undici-6.21.3.tgz. Extracting by manifest.
2654 silly tarball no local data for @vladfrangu/async_event_emitter@https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.4.6.tgz. Extracting by manifest.
2655 silly tarball no local data for @sapphire/snowflake@https://registry.npmjs.org/@sapphire/snowflake/-/snowflake-3.5.5.tgz. Extracting by manifest.
2656 silly tarball no local data for @discordjs/collection@https://registry.npmjs.org/@discordjs/collection/-/collection-2.1.1.tgz. Extracting by manifest.
2657 silly tarball no local data for tslib@https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz. Extracting by manifest.
2658 silly tarball no local data for @sapphire/async-queue@https://registry.npmjs.org/@sapphire/async-queue/-/async-queue-1.5.5.tgz. Extracting by manifest.
2659 silly tarball no local data for discord-api-types@https://registry.npmjs.org/discord-api-types/-/discord-api-types-0.38.17.tgz. Extracting by manifest.
2660 silly tarball no local data for ts-mixer@https://registry.npmjs.org/ts-mixer/-/ts-mixer-6.0.4.tgz. Extracting by manifest.
2661 silly tarball no local data for fast-deep-equal@https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz. Extracting by manifest.
2662 silly tarball no local data for @sapphire/shapeshift@https://registry.npmjs.org/@sapphire/shapeshift/-/shapeshift-4.0.0.tgz. Extracting by manifest.
2663 silly tarball no local data for @discordjs/util@https://registry.npmjs.org/@discordjs/util/-/util-1.1.1.tgz. Extracting by manifest.
2664 silly tarball no local data for @discordjs/formatters@https://registry.npmjs.org/@discordjs/formatters/-/formatters-0.6.1.tgz. Extracting by manifest.
2665 silly tarball no local data for typescript@https://registry.npmjs.org/typescript/-/typescript-5.7.3.tgz. Extracting by manifest.
2666 silly tarball no local data for path@https://registry.npmjs.org/path/-/path-0.12.7.tgz. Extracting by manifest.
2667 silly tarball no local data for jest@https://registry.npmjs.org/jest/-/jest-29.7.0.tgz. Extracting by manifest.
2668 silly tarball no local data for nodemon@https://registry.npmjs.org/nodemon/-/nodemon-3.1.7.tgz. Extracting by manifest.
2669 silly tarball no local data for fs@https://registry.npmjs.org/fs/-/fs-0.0.1-security.tgz. Extracting by manifest.
2670 silly tarball no local data for dotenv@https://registry.npmjs.org/dotenv/-/dotenv-16.6.1.tgz. Extracting by manifest.
2671 silly tarball no local data for discord.js@https://registry.npmjs.org/discord.js/-/discord.js-14.21.0.tgz. Extracting by manifest.
2672 silly tarball no local data for cross-env@https://registry.npmjs.org/cross-env/-/cross-env-7.0.3.tgz. Extracting by manifest.
2673 silly tarball no local data for better-sqlite3@https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-12.2.0.tgz. Extracting by manifest.
2674 silly tarball no local data for @types/express@https://registry.npmjs.org/@types/express/-/express-5.0.0.tgz. Extracting by manifest.
2675 silly tarball no local data for @types/cors@https://registry.npmjs.org/@types/cors/-/cors-2.8.17.tgz. Extracting by manifest.
2676 silly tarball no local data for @discordjs/voice@https://registry.npmjs.org/@discordjs/voice/-/voice-0.18.0.tgz. Extracting by manifest.
2677 silly tarball no local data for @discordjs/builders@https://registry.npmjs.org/@discordjs/builders/-/builders-1.11.2.tgz. Extracting by manifest.
2678 silly tarball no local data for @discordjs/rest@https://registry.npmjs.org/@discordjs/rest/-/rest-2.5.1.tgz. Extracting by manifest.
2679 silly tarball no local data for strip-json-comments@https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz. Extracting by manifest.
2680 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz. Extracting by manifest.
2681 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz. Extracting by manifest.
2682 silly tarball no local data for inherits@https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz. Extracting by manifest.
2683 silly tarball no local data for p-limit@https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz. Extracting by manifest.
2684 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz. Extracting by manifest.
2685 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz. Extracting by manifest.
2686 silly tarball no local data for supports-color@https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz. Extracting by manifest.
2687 silly tarball no local data for istanbul-lib-instrument@https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz. Extracting by manifest.
2688 silly tarball no local data for ansi-styles@https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz. Extracting by manifest.
2689 silly tarball no local data for camelcase@https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz. Extracting by manifest.
2690 silly tarball no local data for has-flag@https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz. Extracting by manifest.
2691 silly tarball no local data for supports-color@https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz. Extracting by manifest.
2692 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz. Extracting by manifest.
2693 silly tarball no local data for @sapphire/snowflake@https://registry.npmjs.org/@sapphire/snowflake/-/snowflake-3.5.3.tgz. Extracting by manifest.
2694 silly tarball no local data for semver@https://registry.npmjs.org/semver/-/semver-7.7.2.tgz. Extracting by manifest.
2695 silly tarball no local data for @discordjs/collection@https://registry.npmjs.org/@discordjs/collection/-/collection-1.5.3.tgz. Extracting by manifest.
2696 silly tarball no local data for discord-api-types@https://registry.npmjs.org/discord-api-types/-/discord-api-types-0.37.120.tgz. Extracting by manifest.
2697 http fetch GET 200 https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz 655ms (cache miss)
2698 http fetch GET 200 https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz 655ms (cache miss)
2699 http fetch GET 200 https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz 692ms (cache miss)
2700 http fetch GET 200 https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz 769ms (cache miss)
2701 http fetch GET 200 https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz 785ms (cache miss)
2702 http fetch GET 200 https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz 853ms (cache miss)
2703 http fetch GET 200 https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz 852ms (cache miss)
2704 http fetch GET 200 https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz 853ms (cache miss)
2705 http fetch GET 200 https://registry.npmjs.org/makeerror/-/makeerror-1.0.12.tgz 851ms (cache miss)
2706 http fetch GET 200 https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz 890ms (cache miss)
2707 http fetch GET 200 https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz 972ms (cache miss)
2708 http fetch GET 200 https://registry.npmjs.org/bl/-/bl-4.1.0.tgz 967ms (cache miss)
2709 http fetch GET 200 https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz 990ms (cache miss)
2710 http fetch GET 200 https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz 993ms (cache miss)
2711 http fetch GET 200 https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz 1050ms (cache miss)
2712 http fetch GET 200 https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz 1048ms (cache miss)
2713 http fetch GET 200 https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz 1046ms (cache miss)
2714 http fetch GET 200 https://registry.npmjs.org/chownr/-/chownr-1.1.4.tgz 1047ms (cache miss)
2715 http fetch GET 200 https://registry.npmjs.org/fs-constants/-/fs-constants-1.0.0.tgz 1048ms (cache miss)
2716 http fetch GET 200 https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz 1046ms (cache miss)
2717 http fetch GET 200 https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz 1079ms (cache miss)
2718 http fetch GET 200 https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz 1082ms (cache miss)
2719 http fetch GET 200 https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz 1078ms (cache miss)
2720 http fetch GET 200 https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz 1082ms (cache miss)
2721 http fetch GET 200 https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz 1074ms (cache miss)
2722 http fetch GET 200 https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz 1139ms (cache miss)
2723 http fetch GET 200 https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz 1135ms (cache miss)
2724 http fetch GET 200 https://registry.npmjs.org/char-regex/-/char-regex-1.0.2.tgz 1134ms (cache miss)
2725 http fetch GET 200 https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz 1167ms (cache miss)
2726 http fetch GET 200 https://registry.npmjs.org/decompress-response/-/decompress-response-6.0.0.tgz 1166ms (cache miss)
2727 http fetch GET 200 https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz 1164ms (cache miss)
2728 http fetch GET 200 https://registry.npmjs.org/simple-concat/-/simple-concat-1.0.1.tgz 1178ms (cache miss)
2729 http fetch GET 200 https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz 1178ms (cache miss)
2730 http fetch GET 200 https://registry.npmjs.org/mimic-response/-/mimic-response-3.1.0.tgz 1179ms (cache miss)
2731 http fetch GET 200 https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz 1176ms (cache miss)
2732 http fetch GET 200 https://registry.npmjs.org/deep-extend/-/deep-extend-0.6.0.tgz 1243ms (cache miss)
2733 http fetch GET 200 https://registry.npmjs.org/mkdirp-classic/-/mkdirp-classic-0.5.3.tgz 1215ms (cache miss)
2734 http fetch GET 200 https://registry.npmjs.org/util/-/util-0.10.4.tgz 1214ms (cache miss)
2735 http fetch GET 200 https://registry.npmjs.org/ini/-/ini-1.3.8.tgz 1261ms (cache miss)
2736 http fetch GET 200 https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.5.tgz 1260ms (cache miss)
2737 http fetch GET 200 https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz 1227ms (cache miss)
2738 http fetch GET 200 https://registry.npmjs.org/tar-stream/-/tar-stream-2.2.0.tgz 1287ms (cache miss)
2739 http fetch GET 200 https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz 1283ms (cache miss)
2740 http fetch GET 200 https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz 1284ms (cache miss)
2741 http fetch GET 200 https://registry.npmjs.org/simple-get/-/simple-get-4.0.1.tgz 1281ms (cache miss)
2742 http fetch GET 200 https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz 1282ms (cache miss)
2743 http fetch GET 200 https://registry.npmjs.org/github-from-package/-/github-from-package-0.0.0.tgz 1254ms (cache miss)
2744 http fetch GET 200 https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz 1246ms (cache miss)
2745 http fetch GET 200 https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz 1359ms (cache miss)
2746 http fetch GET 200 https://registry.npmjs.org/pump/-/pump-3.0.3.tgz 1359ms (cache miss)
2747 http fetch GET 200 https://registry.npmjs.org/napi-build-utils/-/napi-build-utils-2.0.0.tgz 1332ms (cache miss)
2748 http fetch GET 200 https://registry.npmjs.org/process/-/process-0.11.10.tgz 1328ms (cache miss)
2749 http fetch GET 200 https://registry.npmjs.org/touch/-/touch-3.1.1.tgz 1326ms (cache miss)
2750 http fetch GET 200 https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz 1329ms (cache miss)
2751 http fetch GET 200 https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz 1328ms (cache miss)
2752 http fetch GET 200 https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz 1327ms (cache miss)
2753 http fetch GET 200 https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz 1325ms (cache miss)
2754 http fetch GET 200 https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz 1326ms (cache miss)
2755 http fetch GET 200 https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz 1392ms (cache miss)
2756 http fetch GET 200 https://registry.npmjs.org/expand-template/-/expand-template-2.0.3.tgz 1362ms (cache miss)
2757 http fetch GET 200 https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz 1355ms (cache miss)
2758 http fetch GET 200 https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz 1354ms (cache miss)
2759 http fetch GET 200 https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz 1355ms (cache miss)
2760 http fetch GET 200 https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz 1357ms (cache miss)
2761 http fetch GET 200 https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz 1357ms (cache miss)
2762 http fetch GET 200 https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz 1357ms (cache miss)
2763 http fetch GET 200 https://registry.npmjs.org/ignore-by-default/-/ignore-by-default-1.0.1.tgz 1355ms (cache miss)
2764 http fetch GET 200 https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.4.tgz 1378ms (cache miss)
2765 http fetch GET 200 https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz 1370ms (cache miss)
2766 http fetch GET 200 https://registry.npmjs.org/strip-bom/-/strip-bom-4.0.0.tgz 1369ms (cache miss)
2767 http fetch GET 200 https://registry.npmjs.org/detect-newline/-/detect-newline-3.1.0.tgz 1368ms (cache miss)
2768 http fetch GET 200 https://registry.npmjs.org/node-abi/-/node-abi-3.75.0.tgz 1419ms (cache miss)
2769 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz 1404ms (cache miss)
2770 http fetch GET 200 https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz 1406ms (cache miss)
2771 http fetch GET 200 https://registry.npmjs.org/undefsafe/-/undefsafe-2.0.5.tgz 1517ms (cache miss)
2772 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz 1514ms (cache miss)
2773 http fetch GET 200 https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz 1513ms (cache miss)
2774 http fetch GET 200 https://registry.npmjs.org/bser/-/bser-2.1.1.tgz 1510ms (cache miss)
2775 http fetch GET 200 https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz 1511ms (cache miss)
2776 http fetch GET 200 https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz 1612ms (cache miss)
2777 http fetch GET 200 https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz 1608ms (cache miss)
2778 http fetch GET 200 https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz 1609ms (cache miss)
2779 http fetch GET 200 https://registry.npmjs.org/leven/-/leven-3.1.0.tgz 1607ms (cache miss)
2780 http fetch GET 200 https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz 1608ms (cache miss)
2781 http fetch GET 200 https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.7.0.tgz 1605ms (cache miss)
2782 http fetch GET 200 https://registry.npmjs.org/@jest/source-map/-/source-map-29.6.3.tgz 1606ms (cache miss)
2783 http fetch GET 200 https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.3.tgz 1605ms (cache miss)
2784 http fetch GET 200 https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz 1604ms (cache miss)
2785 http fetch GET 200 https://registry.npmjs.org/rc/-/rc-1.2.8.tgz 1670ms (cache miss)
2786 http fetch GET 200 https://registry.npmjs.org/pstree.remy/-/pstree.remy-1.1.8.tgz 1633ms (cache miss)
2787 http fetch GET 200 https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz 1632ms (cache miss)
2788 http fetch GET 200 https://registry.npmjs.org/jest-docblock/-/jest-docblock-29.7.0.tgz 1627ms (cache miss)
2789 http fetch GET 200 https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz 1627ms (cache miss)
2790 http fetch GET 200 https://registry.npmjs.org/@jest/globals/-/globals-29.7.0.tgz 1629ms (cache miss)
2791 http fetch GET 200 https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.9.tgz 1625ms (cache miss)
2792 http fetch GET 200 https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-29.7.0.tgz 1677ms (cache miss)
2793 http fetch GET 200 https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.7.0.tgz 1680ms (cache miss)
2794 http fetch GET 200 https://registry.npmjs.org/@jest/expect/-/expect-29.7.0.tgz 1677ms (cache miss)
2795 http fetch GET 200 https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz 1701ms (cache miss)
2796 http fetch GET 200 https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-2.1.0.tgz 1698ms (cache miss)
2797 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz 1698ms (cache miss)
2798 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz 1697ms (cache miss)
2799 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz 1697ms (cache miss)
2800 http fetch GET 200 https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz 1777ms (cache miss)
2801 http fetch GET 200 https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz 1728ms (cache miss)
2802 http fetch GET 200 https://registry.npmjs.org/walker/-/walker-1.0.8.tgz 1728ms (cache miss)
2803 http fetch GET 200 https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.6.tgz 1725ms (cache miss)
2804 http fetch GET 200 https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz 1719ms (cache miss)
2805 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz 1723ms (cache miss)
2806 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz 1719ms (cache miss)
2807 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz 1723ms (cache miss)
2808 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz 1722ms (cache miss)
2809 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz 1721ms (cache miss)
2810 http fetch GET 200 https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.13.tgz 1789ms (cache miss)
2811 http fetch GET 200 https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.2.tgz 1785ms (cache miss)
2812 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz 1780ms (cache miss)
2813 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz 1780ms (cache miss)
2814 http fetch GET 200 https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.6.3.tgz 1776ms (cache miss)
2815 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz 1779ms (cache miss)
2816 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz 1779ms (cache miss)
2817 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz 1780ms (cache miss)
2818 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz 1781ms (cache miss)
2819 http fetch GET 200 https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz 1810ms (cache miss)
2820 http fetch GET 200 https://registry.npmjs.org/co/-/co-4.6.0.tgz 1806ms (cache miss)
2821 http fetch GET 200 https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.1.0.tgz 1802ms (cache miss)
2822 http fetch GET 200 https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz 1801ms (cache miss)
2823 http fetch GET 200 https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz 1800ms (cache miss)
2824 http fetch GET 200 https://registry.npmjs.org/jest-get-type/-/jest-get-type-29.6.3.tgz 1800ms (cache miss)
2825 http fetch GET 200 https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.6.3.tgz 1801ms (cache miss)
2826 http fetch GET 200 https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz 1892ms (cache miss)
2827 http fetch GET 200 https://registry.npmjs.org/simple-update-notifier/-/simple-update-notifier-2.0.0.tgz 1847ms (cache miss)
2828 http fetch GET 200 https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.27.0.tgz 1827ms (cache miss)
2829 http fetch GET 200 https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz 1830ms (cache miss)
2830 http fetch GET 200 https://registry.npmjs.org/create-jest/-/create-jest-29.7.0.tgz 1824ms (cache miss)
2831 http fetch GET 200 https://registry.npmjs.org/diff-sequences/-/diff-sequences-29.6.3.tgz 1903ms (cache miss)
2832 http fetch GET 200 https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.7.tgz 1891ms (cache miss)
2833 http fetch GET 200 https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-29.7.0.tgz 1889ms (cache miss)
2834 http fetch GET 200 https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz 1890ms (cache miss)
2835 http fetch GET 200 https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz 1889ms (cache miss)
2836 http fetch GET 200 https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.7.0.tgz 1889ms (cache miss)
2837 http fetch GET 200 https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz 1887ms (cache miss)
2838 http fetch GET 200 https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz 1887ms (cache miss)
2839 http fetch GET 200 https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz 1887ms (cache miss)
2840 http fetch GET 200 https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz 1887ms (cache miss)
2841 http fetch GET 200 https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz 1888ms (cache miss)
2842 http fetch GET 200 https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz 1886ms (cache miss)
2843 http fetch GET 200 https://registry.npmjs.org/resolve.exports/-/resolve.exports-2.0.3.tgz 1923ms (cache miss)
2844 http fetch GET 200 https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz 1918ms (cache miss)
2845 http fetch GET 200 https://registry.npmjs.org/@jest/environment/-/environment-29.7.0.tgz 1915ms (cache miss)
2846 http fetch GET 200 https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz 1902ms (cache miss)
2847 http fetch GET 200 https://registry.npmjs.org/once/-/once-1.4.0.tgz 1901ms (cache miss)
2848 http fetch GET 200 https://registry.npmjs.org/dedent/-/dedent-1.6.0.tgz 1934ms (cache miss)
2849 http fetch GET 200 https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz 1924ms (cache miss)
2850 http fetch GET 200 https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz 1921ms (cache miss)
2851 http fetch GET 200 https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz 1921ms (cache miss)
2852 http fetch GET 200 https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz 1920ms (cache miss)
2853 http fetch GET 200 https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz 1921ms (cache miss)
2854 http fetch GET 200 https://registry.npmjs.org/make-dir/-/make-dir-4.0.0.tgz 1922ms (cache miss)
2855 http fetch GET 200 https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz 1919ms (cache miss)
2856 http fetch GET 200 https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz 1920ms (cache miss)
2857 warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
2858 http fetch GET 200 https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz 1970ms (cache miss)
2859 http fetch GET 200 https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz 1969ms (cache miss)
2860 http fetch GET 200 https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz 1969ms (cache miss)
2861 http fetch GET 200 https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz 1969ms (cache miss)
2862 http fetch GET 200 https://registry.npmjs.org/jest-diff/-/jest-diff-29.7.0.tgz 2015ms (cache miss)
2863 http fetch GET 200 https://registry.npmjs.org/html-escaper/-/html-escaper-2.0.2.tgz 1996ms (cache miss)
2864 http fetch GET 200 https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz 1994ms (cache miss)
2865 http fetch GET 200 https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz 1994ms (cache miss)
2866 http fetch GET 200 https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz 1994ms (cache miss)
2867 http fetch GET 200 https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz 2003ms (cache miss)
2868 http fetch GET 200 https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz 2004ms (cache miss)
2869 http fetch GET 200 https://registry.npmjs.org/emittery/-/emittery-0.13.1.tgz 2032ms (cache miss)
2870 http fetch GET 200 https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.7.0.tgz 2030ms (cache miss)
2871 http fetch GET 200 https://registry.npmjs.org/ms/-/ms-2.1.3.tgz 2010ms (cache miss)
2872 http fetch GET 200 https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz 2009ms (cache miss)
2873 http fetch GET 200 https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz 2006ms (cache miss)
2874 http fetch GET 200 https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz 2058ms (cache miss)
2875 http fetch GET 200 https://registry.npmjs.org/babel-jest/-/babel-jest-29.7.0.tgz 2072ms (cache miss)
2876 http fetch GET 200 https://registry.npmjs.org/execa/-/execa-5.1.1.tgz 2070ms (cache miss)
2877 http fetch GET 200 https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz 2063ms (cache miss)
2878 http fetch GET 200 https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz 2061ms (cache miss)
2879 http fetch GET 200 https://registry.npmjs.org/expect/-/expect-29.7.0.tgz 2112ms (cache miss)
2880 http fetch GET 200 https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz 2098ms (cache miss)
2881 http fetch GET 200 https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz 2088ms (cache miss)
2882 http fetch GET 200 https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz 2089ms (cache miss)
2883 http fetch GET 200 https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz 2086ms (cache miss)
2884 http fetch GET 200 https://registry.npmjs.org/@jest/schemas/-/schemas-29.6.3.tgz 2087ms (cache miss)
2885 http fetch GET 200 https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz 2103ms (cache miss)
2886 http fetch GET 200 https://registry.npmjs.org/braces/-/braces-3.0.3.tgz 2139ms (cache miss)
2887 http fetch GET 200 https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz 2103ms (cache miss)
2888 http fetch GET 200 https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz 2102ms (cache miss)
2889 http fetch GET 200 https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz 2103ms (cache miss)
2890 http fetch GET 200 https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz 2101ms (cache miss)
2891 http fetch GET 200 https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz 2102ms (cache miss)
2892 http fetch GET 200 https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz 2188ms (cache miss)
2893 http fetch GET 200 https://registry.npmjs.org/jest-each/-/jest-each-29.7.0.tgz 2176ms (cache miss)
2894 http fetch GET 200 https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz 2158ms (cache miss)
2895 http fetch GET 200 https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.3.tgz 2153ms (cache miss)
2896 http fetch GET 200 https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz 2155ms (cache miss)
2897 http fetch GET 200 https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz 2146ms (cache miss)
2898 http fetch GET 200 https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz 2145ms (cache miss)
2899 http fetch GET 200 https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.4.3.tgz 2214ms (cache miss)
2900 http fetch GET 200 https://registry.npmjs.org/@babel/helper-globals/-/helper-globals-7.28.0.tgz 2178ms (cache miss)
2901 http fetch GET 200 https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz 2178ms (cache miss)
2902 http fetch GET 200 https://registry.npmjs.org/jest-mock/-/jest-mock-29.7.0.tgz 2231ms (cache miss)
2903 http fetch GET 200 https://registry.npmjs.org/@types/yargs/-/yargs-17.0.33.tgz 2205ms (cache miss)
2904 http fetch GET 200 https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz 2203ms (cache miss)
2905 http fetch GET 200 https://registry.npmjs.org/debug/-/debug-4.4.1.tgz 2198ms (cache miss)
2906 http fetch GET 200 https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz 2198ms (cache miss)
2907 http fetch GET 200 https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz 2196ms (cache miss)
2908 http fetch GET 200 https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz 2196ms (cache miss)
2909 http fetch GET 200 https://registry.npmjs.org/browserslist/-/browserslist-4.25.1.tgz 2357ms (cache miss)
2910 http fetch GET 200 https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-4.0.2.tgz 2348ms (cache miss)
2911 http fetch GET 200 https://registry.npmjs.org/string-length/-/string-length-4.0.2.tgz 2355ms (cache miss)
2912 http fetch GET 200 https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz 2353ms (cache miss)
2913 http fetch GET 200 https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz 2542ms (cache miss)
2914 http fetch GET 200 https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz 2448ms (cache miss)
2915 http fetch GET 200 https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz 2531ms (cache miss)
2916 http fetch GET 200 https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz 2473ms (cache miss)
2917 http fetch GET 200 https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz 2471ms (cache miss)
2918 http fetch GET 200 https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz 2470ms (cache miss)
2919 http fetch GET 200 https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz 2471ms (cache miss)
2920 http fetch GET 200 https://registry.npmjs.org/slash/-/slash-3.0.0.tgz 2468ms (cache miss)
2921 http fetch GET 200 https://registry.npmjs.org/jest-util/-/jest-util-29.7.0.tgz 2467ms (cache miss)
2922 http fetch GET 200 https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz 2536ms (cache miss)
2923 http fetch GET 200 https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.28.0.tgz 2496ms (cache miss)
2924 http fetch GET 200 https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz 2486ms (cache miss)
2925 http fetch GET 200 https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz 2484ms (cache miss)
2926 http fetch GET 200 https://registry.npmjs.org/glob/-/glob-7.2.3.tgz 2485ms (cache miss)
2927 warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
2928 http fetch GET 200 https://registry.npmjs.org/jest-runner/-/jest-runner-29.7.0.tgz 2482ms (cache miss)
2929 http fetch GET 200 https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-29.6.3.tgz 2453ms (cache miss)
2930 http fetch GET 200 https://registry.npmjs.org/jest-resolve-dependencies/-/jest-resolve-dependencies-29.7.0.tgz 2483ms (cache miss)
2931 http fetch GET 200 https://registry.npmjs.org/jest-circus/-/jest-circus-29.7.0.tgz 2568ms (cache miss)
2932 http fetch GET 200 https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz 2539ms (cache miss)
2933 http fetch GET 200 https://registry.npmjs.org/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz 2539ms (cache miss)
2934 http fetch GET 200 https://registry.npmjs.org/jest-runtime/-/jest-runtime-29.7.0.tgz 2536ms (cache miss)
2935 http fetch GET 200 https://registry.npmjs.org/exit/-/exit-0.1.2.tgz 2506ms (cache miss)
2936 http fetch GET 200 https://registry.npmjs.org/jest-message-util/-/jest-message-util-29.7.0.tgz 2507ms (cache miss)
2937 http fetch GET 200 https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz 2506ms (cache miss)
2938 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-6.3.1.tgz 2561ms (cache miss)
2939 http fetch GET 200 https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz 2553ms (cache miss)
2940 http fetch GET 200 https://registry.npmjs.org/jest-resolve/-/jest-resolve-29.7.0.tgz 2523ms (cache miss)
2941 http fetch GET 200 https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-29.7.0.tgz 2523ms (cache miss)
2942 http fetch GET 200 https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz 2522ms (cache miss)
2943 http fetch GET 200 https://registry.npmjs.org/jest-cli/-/jest-cli-29.7.0.tgz 2520ms (cache miss)
2944 http fetch GET 200 https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz 2573ms (cache miss)
2945 http fetch GET 200 https://registry.npmjs.org/ci-info/-/ci-info-3.9.0.tgz 2536ms (cache miss)
2946 http fetch GET 200 https://registry.npmjs.org/@jest/test-result/-/test-result-29.7.0.tgz 2534ms (cache miss)
2947 http fetch GET 200 https://registry.npmjs.org/import-local/-/import-local-3.2.0.tgz 2533ms (cache miss)
2948 http fetch GET 200 https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz 2532ms (cache miss)
2949 http fetch GET 200 https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz 2532ms (cache miss)
2950 http fetch GET 200 https://registry.npmjs.org/@jest/transform/-/transform-29.7.0.tgz 2535ms (cache miss)
2951 http fetch GET 200 https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz 2545ms (cache miss)
2952 http fetch GET 200 https://registry.npmjs.org/lodash.snakecase/-/lodash.snakecase-4.1.1.tgz 2542ms (cache miss)
2953 http fetch GET 200 https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz 2541ms (cache miss)
2954 http fetch GET 200 https://registry.npmjs.org/prebuild-install/-/prebuild-install-7.1.3.tgz 2540ms (cache miss)
2955 http fetch GET 200 https://registry.npmjs.org/file-uri-to-path/-/file-uri-to-path-1.0.0.tgz 2541ms (cache miss)
2956 http fetch GET 200 https://registry.npmjs.org/@types/http-errors/-/http-errors-2.0.5.tgz 2540ms (cache miss)
2957 http fetch GET 200 https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz 2590ms (cache miss)
2958 http fetch GET 200 https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz 2591ms (cache miss)
2959 http fetch GET 200 https://registry.npmjs.org/jest-validate/-/jest-validate-29.7.0.tgz 2584ms (cache miss)
2960 http fetch GET 200 https://registry.npmjs.org/@jest/console/-/console-29.7.0.tgz 2551ms (cache miss)
2961 http fetch GET 200 https://registry.npmjs.org/@jest/types/-/types-29.6.3.tgz 2552ms (cache miss)
2962 http fetch GET 200 https://registry.npmjs.org/which/-/which-2.0.2.tgz 2549ms (cache miss)
2963 http fetch GET 200 https://registry.npmjs.org/@types/mime/-/mime-1.3.5.tgz 2548ms (cache miss)
2964 http fetch GET 200 https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.7.0.tgz 2630ms (cache miss)
2965 http fetch GET 200 https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.8.tgz 2591ms (cache miss)
2966 http fetch GET 200 https://registry.npmjs.org/bindings/-/bindings-1.5.0.tgz 2593ms (cache miss)
2967 http fetch GET 200 https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-5.0.7.tgz 2591ms (cache miss)
2968 http fetch GET 200 https://registry.npmjs.org/@types/connect/-/connect-3.4.38.tgz 2592ms (cache miss)
2969 http fetch GET 200 https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.7.tgz 2592ms (cache miss)
2970 http fetch GET 200 https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.6.tgz 2591ms (cache miss)
2971 http fetch GET 200 https://registry.npmjs.org/prism-media/-/prism-media-1.3.5.tgz 2590ms (cache miss)
2972 http fetch GET 200 https://registry.npmjs.org/tar-fs/-/tar-fs-2.1.3.tgz 2734ms (cache miss)
2973 http fetch GET 200 https://registry.npmjs.org/@types/send/-/send-0.17.5.tgz 2601ms (cache miss)
2974 http fetch GET 200 https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.12.1.tgz 2598ms (cache miss)
2975 http fetch GET 200 https://registry.npmjs.org/@types/qs/-/qs-6.14.0.tgz 2609ms (cache miss)
2976 http fetch GET 200 https://registry.npmjs.org/@types/ws/-/ws-8.18.1.tgz 2607ms (cache miss)
2977 http fetch GET 200 https://registry.npmjs.org/fs/-/fs-0.0.1-security.tgz 2608ms (cache miss)
2978 http fetch GET 200 https://registry.npmjs.org/@sapphire/async-queue/-/async-queue-1.5.5.tgz 2612ms (cache miss)
2979 http fetch GET 200 https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz 2612ms (cache miss)
2980 http fetch GET 200 https://registry.npmjs.org/@discordjs/util/-/util-1.1.1.tgz 2611ms (cache miss)
2981 http fetch GET 200 https://registry.npmjs.org/jest-snapshot/-/jest-snapshot-29.7.0.tgz 2662ms (cache miss)
2982 http fetch GET 200 https://registry.npmjs.org/path/-/path-0.12.7.tgz 2617ms (cache miss)
2983 http fetch GET 200 https://registry.npmjs.org/jest/-/jest-29.7.0.tgz 2617ms (cache miss)
2984 http fetch GET 200 https://registry.npmjs.org/cross-env/-/cross-env-7.0.3.tgz 2615ms (cache miss)
2985 http fetch GET 200 https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.191.tgz 2691ms (cache miss)
2986 http fetch GET 200 https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz 2678ms (cache miss)
2987 http fetch GET 200 https://registry.npmjs.org/pretty-format/-/pretty-format-29.7.0.tgz 2670ms (cache miss)
2988 http fetch GET 200 https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz 2620ms (cache miss)
2989 http fetch GET 200 https://registry.npmjs.org/@types/express/-/express-5.0.0.tgz 2622ms (cache miss)
2990 http fetch GET 200 https://registry.npmjs.org/@types/cors/-/cors-2.8.17.tgz 2622ms (cache miss)
2991 http fetch GET 200 https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz 2728ms (cache miss)
2992 http fetch GET 200 https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz 2622ms (cache miss)
2993 http fetch GET 200 https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz 2622ms (cache miss)
2994 http fetch GET 200 https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz 2626ms (cache miss)
2995 http fetch GET 200 https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz 2632ms (cache miss)
2996 http fetch GET 200 https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz 2790ms (cache miss)
2997 http fetch GET 200 https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz 2741ms (cache miss)
2998 http fetch GET 200 https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz 2637ms (cache miss)
2999 http fetch GET 200 https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz 2636ms (cache miss)
3000 http fetch GET 200 https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz 2636ms (cache miss)
3001 http fetch GET 200 https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz 2681ms (cache miss)
3002 http fetch GET 200 https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz 2755ms (cache miss)
3003 http fetch GET 200 https://registry.npmjs.org/dotenv/-/dotenv-16.6.1.tgz 2709ms (cache miss)
3004 http fetch GET 200 https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz 2771ms (cache miss)
3005 http fetch GET 200 https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz 2717ms (cache miss)
3006 http fetch GET 200 https://registry.npmjs.org/json5/-/json5-2.2.3.tgz 2775ms (cache miss)
3007 http fetch GET 200 https://registry.npmjs.org/@discordjs/collection/-/collection-2.1.1.tgz 2734ms (cache miss)
3008 http fetch GET 200 https://registry.npmjs.org/@discordjs/formatters/-/formatters-0.6.1.tgz 2733ms (cache miss)
3009 http fetch GET 200 https://registry.npmjs.org/jest-config/-/jest-config-29.7.0.tgz 2804ms (cache miss)
3010 http fetch GET 200 https://registry.npmjs.org/ws/-/ws-8.18.3.tgz 2795ms (cache miss)
3011 http fetch GET 200 https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz 2849ms (cache miss)
3012 http fetch GET 200 https://registry.npmjs.org/@discordjs/collection/-/collection-1.5.3.tgz 2793ms (cache miss)
3013 http fetch GET 200 https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-29.7.0.tgz 2822ms (cache miss)
3014 http fetch GET 200 https://registry.npmjs.org/@sinonjs/commons/-/commons-3.0.1.tgz 2901ms (cache miss)
3015 http fetch GET 200 https://registry.npmjs.org/jest-worker/-/jest-worker-29.7.0.tgz 2860ms (cache miss)
3016 http fetch GET 200 https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.4.6.tgz 2814ms (cache miss)
3017 http fetch GET 200 https://registry.npmjs.org/@sapphire/snowflake/-/snowflake-3.5.5.tgz 2817ms (cache miss)
3018 http fetch GET 200 https://registry.npmjs.org/@sapphire/snowflake/-/snowflake-3.5.3.tgz 2817ms (cache miss)
3019 http fetch GET 200 https://registry.npmjs.org/@jest/reporters/-/reporters-29.7.0.tgz 2841ms (cache miss)
3020 http fetch GET 200 https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz 2907ms (cache miss)
3021 http fetch GET 200 https://registry.npmjs.org/ts-mixer/-/ts-mixer-6.0.4.tgz 2903ms (cache miss)
3022 http fetch GET 200 https://registry.npmjs.org/undici-types/-/undici-types-7.8.0.tgz 2932ms (cache miss)
3023 http fetch GET 200 https://registry.npmjs.org/@discordjs/ws/-/ws-1.2.3.tgz 3008ms (cache miss)
3024 http fetch GET 200 https://registry.npmjs.org/@jest/core/-/core-29.7.0.tgz 3012ms (cache miss)
3025 http fetch GET 200 https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz 3061ms (cache miss)
3026 http fetch GET 200 https://registry.npmjs.org/@discordjs/voice/-/voice-0.18.0.tgz 3011ms (cache miss)
3027 http fetch GET 200 https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz 3088ms (cache miss)
3028 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 3027ms (cache miss)
3029 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 3027ms (cache miss)
3030 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 3028ms (cache miss)
3031 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 3028ms (cache miss)
3032 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 3029ms (cache miss)
3033 http fetch GET 200 https://registry.npmjs.org/@discordjs/builders/-/builders-1.11.2.tgz 3036ms (cache miss)
3034 http fetch GET 200 https://registry.npmjs.org/semver/-/semver-7.7.2.tgz 3035ms (cache miss)
3035 http fetch GET 200 https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz 3127ms (cache miss)
3036 http fetch GET 200 https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz 3166ms (cache miss)
3037 http fetch GET 200 https://registry.npmjs.org/@sinclair/typebox/-/typebox-0.27.8.tgz 3157ms (cache miss)
3038 http fetch GET 200 https://registry.npmjs.org/nodemon/-/nodemon-3.1.7.tgz 3108ms (cache miss)
3039 http fetch GET 200 https://registry.npmjs.org/@discordjs/rest/-/rest-2.5.1.tgz 3106ms (cache miss)
3040 http fetch GET 200 https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz 3162ms (cache miss)
3041 http fetch GET 200 https://registry.npmjs.org/@babel/generator/-/generator-7.28.0.tgz 3176ms (cache miss)
3042 http fetch GET 200 https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz 3207ms (cache miss)
3043 http fetch GET 200 https://registry.npmjs.org/pure-rand/-/pure-rand-6.1.0.tgz 3231ms (cache miss)
3044 http fetch GET 200 https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.7.tgz 3246ms (cache miss)
3045 http fetch GET 200 https://registry.npmjs.org/@sapphire/shapeshift/-/shapeshift-4.0.0.tgz 3208ms (cache miss)
3046 http fetch GET 200 https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.0.tgz 3283ms (cache miss)
3047 http fetch GET 200 https://registry.npmjs.org/@babel/parser/-/parser-7.28.0.tgz 3299ms (cache miss)
3048 http fetch GET 200 https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz 3489ms (cache miss)
3049 http fetch GET 200 https://registry.npmjs.org/@babel/core/-/core-7.28.0.tgz 3516ms (cache miss)
3050 http fetch GET 200 https://registry.npmjs.org/@types/node/-/node-24.1.0.tgz 3552ms (cache miss)
3051 http fetch GET 200 https://registry.npmjs.org/@babel/helpers/-/helpers-7.28.2.tgz 3665ms (cache miss)
3052 http fetch GET 200 https://registry.npmjs.org/undici/-/undici-6.21.3.tgz 3696ms (cache miss)
3053 http fetch GET 200 https://registry.npmjs.org/@babel/types/-/types-7.28.2.tgz 3842ms (cache miss)
3054 http fetch GET 200 https://registry.npmjs.org/discord.js/-/discord.js-14.21.0.tgz 4102ms (cache miss)
3055 http fetch GET 200 https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-12.2.0.tgz 4412ms (cache miss)
3056 http fetch GET 200 https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001727.tgz 4789ms (cache miss)
3057 http fetch GET 200 https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz 4790ms (cache miss)
3058 http fetch GET 200 https://registry.npmjs.org/discord-api-types/-/discord-api-types-0.37.120.tgz 4832ms (cache miss)
3059 http fetch GET 200 https://registry.npmjs.org/discord-api-types/-/discord-api-types-0.38.17.tgz 4887ms (cache miss)
3060 http fetch GET 200 https://registry.npmjs.org/typescript/-/typescript-5.7.3.tgz 4910ms (cache miss)
3061 info run better-sqlite3@12.2.0 install node_modules/better-sqlite3 prebuild-install || node-gyp rebuild --release
3062 info run better-sqlite3@12.2.0 install { code: 0, signal: null }
3063 silly ADD node_modules/p-try
3064 silly ADD node_modules/is-fullwidth-code-point
3065 silly ADD node_modules/emoji-regex
3066 silly ADD node_modules/wrap-ansi
3067 silly ADD node_modules/yargs-parser
3068 silly ADD node_modules/y18n
3069 silly ADD node_modules/string-width
3070 silly ADD node_modules/require-directory
3071 silly ADD node_modules/get-caller-file
3072 silly ADD node_modules/cliui
3073 silly ADD node_modules/imurmurhash
3074 silly ADD node_modules/isexe
3075 silly ADD node_modules/tmpl
3076 silly ADD node_modules/makeerror
3077 silly ADD node_modules/escalade
3078 silly ADD node_modules/is-number
3079 silly ADD node_modules/safe-buffer
3080 silly ADD node_modules/util-deprecate
3081 silly ADD node_modules/string_decoder
3082 silly ADD node_modules/ieee754
3083 silly ADD node_modules/base64-js
3084 silly ADD node_modules/buffer
3085 silly ADD node_modules/readable-stream
3086 silly ADD node_modules/fs-constants
3087 silly ADD node_modules/bl
3088 silly ADD node_modules/tar-stream
3089 silly ADD node_modules/chownr
3090 silly ADD node_modules/has-flag
3091 silly ADD node_modules/ansi-regex
3092 silly ADD node_modules/char-regex
3093 silly ADD node_modules/escape-string-regexp
3094 silly ADD node_modules/buffer-from
3095 silly ADD node_modules/mimic-response
3096 silly ADD node_modules/simple-concat
3097 silly ADD node_modules/decompress-response
3098 silly ADD node_modules/shebang-regex
3099 silly ADD node_modules/function-bind
3100 silly ADD node_modules/hasown
3101 silly ADD node_modules/supports-preserve-symlinks-flag
3102 silly ADD node_modules/path-parse
3103 silly ADD node_modules/is-core-module
3104 silly ADD node_modules/ini
3105 silly ADD node_modules/deep-extend
3106 silly ADD node_modules/end-of-stream
3107 silly ADD node_modules/sisteransi
3108 silly ADD node_modules/kleur
3109 silly ADD node_modules/react-is
3110 silly ADD node_modules/tunnel-agent
3111 silly ADD node_modules/tar-fs
3112 silly ADD node_modules/simple-get
3113 silly ADD node_modules/rc
3114 silly ADD node_modules/rc/node_modules/strip-json-comments
3115 silly ADD node_modules/pump
3116 silly ADD node_modules/node-abi
3117 silly ADD node_modules/node-abi/node_modules/semver
3118 silly ADD node_modules/napi-build-utils
3119 silly ADD node_modules/mkdirp-classic
3120 silly ADD node_modules/minimist
3121 silly ADD node_modules/github-from-package
3122 silly ADD node_modules/expand-template
3123 silly ADD node_modules/detect-libc
3124 silly ADD node_modules/util
3125 silly ADD node_modules/util/node_modules/inherits
3126 silly ADD node_modules/process
3127 silly ADD node_modules/is-arrayish
3128 silly ADD node_modules/lines-and-columns
3129 silly ADD node_modules/json-parse-even-better-errors
3130 silly ADD node_modules/error-ex
3131 silly ADD node_modules/yocto-queue
3132 silly ADD node_modules/mimic-fn
3133 silly ADD node_modules/is-extglob
3134 silly ADD node_modules/binary-extensions
3135 silly ADD node_modules/readdirp
3136 silly ADD node_modules/is-glob
3137 silly ADD node_modules/is-binary-path
3138 silly ADD node_modules/glob-parent
3139 silly ADD node_modules/undefsafe
3140 silly ADD node_modules/touch
3141 silly ADD node_modules/simple-update-notifier
3142 silly ADD node_modules/simple-update-notifier/node_modules/semver
3143 silly ADD node_modules/pstree.remy
3144 silly ADD node_modules/ignore-by-default
3145 silly ADD node_modules/chokidar
3146 silly ADD node_modules/concat-map
3147 silly ADD node_modules/balanced-match
3148 silly ADD node_modules/brace-expansion
3149 silly ADD node_modules/to-regex-range
3150 silly ADD node_modules/fill-range
3151 silly ADD node_modules/braces
3152 silly ADD node_modules/yallist
3153 silly ADD node_modules/p-locate
3154 silly ADD node_modules/p-locate/node_modules/p-limit
3155 silly ADD node_modules/sprintf-js
3156 silly ADD node_modules/esprima
3157 silly ADD node_modules/argparse
3158 silly ADD node_modules/leven
3159 silly ADD node_modules/natural-compare
3160 silly ADD node_modules/@babel/plugin-syntax-typescript
3161 silly ADD node_modules/@babel/plugin-syntax-jsx
3162 silly ADD node_modules/callsites
3163 silly ADD node_modules/strip-bom
3164 silly ADD node_modules/cjs-module-lexer
3165 silly ADD node_modules/@jest/source-map
3166 silly ADD node_modules/@jest/globals
3167 silly ADD node_modules/detect-newline
3168 silly ADD node_modules/source-map-support
3169 silly ADD node_modules/jest-leak-detector
3170 silly ADD node_modules/jest-docblock
3171 silly ADD node_modules/emittery
3172 silly ADD node_modules/resolve.exports
3173 silly ADD node_modules/resolve
3174 silly ADD node_modules/jest-pnp-resolver
3175 silly ADD node_modules/@types/stack-utils
3176 silly ADD node_modules/diff-sequences
3177 silly ADD node_modules/jest-diff
3178 silly ADD node_modules/node-int64
3179 silly ADD node_modules/bser
3180 silly ADD node_modules/picomatch
3181 silly ADD node_modules/normalize-path
3182 silly ADD node_modules/walker
3183 silly ADD node_modules/fsevents
3184 silly ADD node_modules/fb-watchman
3185 silly ADD node_modules/anymatch
3186 silly ADD node_modules/@types/graceful-fs
3187 silly ADD node_modules/@jest/expect-utils
3188 silly ADD node_modules/type-detect
3189 silly ADD node_modules/@sinonjs/commons
3190 silly ADD node_modules/@sinonjs/fake-timers
3191 silly ADD node_modules/expect
3192 silly ADD node_modules/jest-mock
3193 silly ADD node_modules/@jest/fake-timers
3194 silly ADD node_modules/stack-utils
3195 silly ADD node_modules/pure-rand
3196 silly ADD node_modules/jest-matcher-utils
3197 silly ADD node_modules/jest-each
3198 silly ADD node_modules/is-generator-fn
3199 silly ADD node_modules/dedent
3200 silly ADD node_modules/co
3201 silly ADD node_modules/@jest/expect
3202 silly ADD node_modules/@jest/environment
3203 silly ADD node_modules/@babel/plugin-syntax-top-level-await
3204 silly ADD node_modules/@babel/plugin-syntax-private-property-in-object
3205 silly ADD node_modules/@babel/plugin-syntax-optional-chaining
3206 silly ADD node_modules/@babel/plugin-syntax-optional-catch-binding
3207 silly ADD node_modules/@babel/plugin-syntax-object-rest-spread
3208 silly ADD node_modules/@babel/plugin-syntax-numeric-separator
3209 silly ADD node_modules/@babel/plugin-syntax-nullish-coalescing-operator
3210 silly ADD node_modules/@babel/plugin-syntax-logical-assignment-operators
3211 silly ADD node_modules/@babel/plugin-syntax-json-strings
3212 silly ADD node_modules/@babel/plugin-syntax-import-meta
3213 silly ADD node_modules/@babel/plugin-syntax-import-attributes
3214 silly ADD node_modules/@babel/plugin-syntax-class-static-block
3215 silly ADD node_modules/@babel/plugin-syntax-class-properties
3216 silly ADD node_modules/@babel/plugin-syntax-bigint
3217 silly ADD node_modules/@babel/plugin-syntax-async-generators
3218 silly ADD node_modules/babel-preset-current-node-syntax
3219 silly ADD node_modules/babel-plugin-jest-hoist
3220 silly ADD node_modules/@types/babel__traverse
3221 silly ADD node_modules/@types/babel__template
3222 silly ADD node_modules/@types/babel__generator
3223 silly ADD node_modules/babel-preset-jest
3224 silly ADD node_modules/@types/babel__core
3225 silly ADD node_modules/strip-json-comments
3226 silly ADD node_modules/parse-json
3227 silly ADD node_modules/jest-get-type
3228 silly ADD node_modules/jest-environment-node
3229 silly ADD node_modules/jest-circus
3230 silly ADD node_modules/deepmerge
3231 silly ADD node_modules/babel-jest
3232 silly ADD node_modules/@jest/test-sequencer
3233 silly ADD node_modules/prompts
3234 silly ADD node_modules/yargs
3235 silly ADD node_modules/create-jest
3236 silly ADD node_modules/strip-final-newline
3237 silly ADD node_modules/signal-exit
3238 silly ADD node_modules/onetime
3239 silly ADD node_modules/npm-run-path
3240 silly ADD node_modules/merge-stream
3241 silly ADD node_modules/is-stream
3242 silly ADD node_modules/human-signals
3243 silly ADD node_modules/get-stream
3244 silly ADD node_modules/p-limit
3245 silly ADD node_modules/execa
3246 silly ADD node_modules/html-escaper
3247 silly ADD node_modules/source-map
3248 silly ADD node_modules/make-dir
3249 silly ADD node_modules/make-dir/node_modules/semver
3250 silly ADD node_modules/wrappy
3251 silly ADD node_modules/resolve-cwd
3252 silly ADD node_modules/pkg-dir
3253 silly ADD node_modules/path-is-absolute
3254 silly ADD node_modules/once
3255 silly ADD node_modules/minimatch
3256 silly ADD node_modules/inherits
3257 silly ADD node_modules/inflight
3258 silly ADD node_modules/fs.realpath
3259 silly ADD node_modules/path-exists
3260 silly ADD node_modules/locate-path
3261 silly ADD node_modules/ms
3262 silly ADD node_modules/color-name
3263 silly ADD node_modules/color-convert
3264 silly ADD node_modules/supports-color
3265 silly ADD node_modules/ansi-styles
3266 silly ADD node_modules/update-browserslist-db
3267 silly ADD node_modules/node-releases
3268 silly ADD node_modules/electron-to-chromium
3269 silly ADD node_modules/caniuse-lite
3270 silly ADD node_modules/resolve-from
3271 silly ADD node_modules/js-yaml
3272 silly ADD node_modules/get-package-type
3273 silly ADD node_modules/find-up
3274 silly ADD node_modules/camelcase
3275 silly ADD node_modules/test-exclude
3276 silly ADD node_modules/@istanbuljs/schema
3277 silly ADD node_modules/@istanbuljs/load-nyc-config
3278 silly ADD node_modules/@babel/helper-plugin-utils
3279 silly ADD node_modules/type-fest
3280 silly ADD node_modules/@types/yargs-parser
3281 silly ADD node_modules/@types/istanbul-lib-report
3282 silly ADD node_modules/@jridgewell/resolve-uri
3283 silly ADD node_modules/@jridgewell/sourcemap-codec
3284 silly ADD node_modules/@sinclair/typebox
3285 silly ADD node_modules/@types/yargs
3286 silly ADD node_modules/@types/istanbul-reports
3287 silly ADD node_modules/@jest/schemas
3288 silly ADD node_modules/@babel/helper-string-parser
3289 silly ADD node_modules/@babel/helper-globals
3290 silly ADD node_modules/@babel/helper-module-imports
3291 silly ADD node_modules/lru-cache
3292 silly ADD node_modules/browserslist
3293 silly ADD node_modules/@babel/helper-validator-option
3294 silly ADD node_modules/@babel/compat-data
3295 silly ADD node_modules/jsesc
3296 silly ADD node_modules/picocolors
3297 silly ADD node_modules/js-tokens
3298 silly ADD node_modules/@babel/helper-validator-identifier
3299 silly ADD node_modules/@jridgewell/gen-mapping
3300 silly ADD node_modules/semver
3301 silly ADD node_modules/json5
3302 silly ADD node_modules/gensync
3303 silly ADD node_modules/debug
3304 silly ADD node_modules/@babel/types
3305 silly ADD node_modules/@babel/traverse
3306 silly ADD node_modules/@babel/template
3307 silly ADD node_modules/@babel/parser
3308 silly ADD node_modules/@babel/helpers
3309 silly ADD node_modules/@babel/helper-module-transforms
3310 silly ADD node_modules/@babel/helper-compilation-targets
3311 silly ADD node_modules/@babel/generator
3312 silly ADD node_modules/@babel/code-frame
3313 silly ADD node_modules/@ampproject/remapping
3314 silly ADD node_modules/write-file-atomic
3315 silly ADD node_modules/pirates
3316 silly ADD node_modules/fast-json-stable-stringify
3317 silly ADD node_modules/convert-source-map
3318 silly ADD node_modules/babel-plugin-istanbul
3319 silly ADD node_modules/babel-plugin-istanbul/node_modules/istanbul-lib-instrument
3320 silly ADD node_modules/@babel/core
3321 silly ADD node_modules/@types/istanbul-lib-coverage
3322 silly ADD node_modules/v8-to-istanbul
3323 silly ADD node_modules/string-length
3324 silly ADD node_modules/jest-worker
3325 silly ADD node_modules/jest-worker/node_modules/supports-color
3326 silly ADD node_modules/istanbul-reports
3327 silly ADD node_modules/istanbul-lib-source-maps
3328 silly ADD node_modules/istanbul-lib-report
3329 silly ADD node_modules/istanbul-lib-instrument
3330 silly ADD node_modules/istanbul-lib-instrument/node_modules/semver
3331 silly ADD node_modules/istanbul-lib-coverage
3332 silly ADD node_modules/glob
3333 silly ADD node_modules/collect-v8-coverage
3334 silly ADD node_modules/@jridgewell/trace-mapping
3335 silly ADD node_modules/@bcoe/v8-coverage
3336 silly ADD node_modules/strip-ansi
3337 silly ADD node_modules/slash
3338 silly ADD node_modules/pretty-format
3339 silly ADD node_modules/pretty-format/node_modules/ansi-styles
3340 silly ADD node_modules/micromatch
3341 silly ADD node_modules/jest-watcher
3342 silly ADD node_modules/jest-validate
3343 silly ADD node_modules/jest-validate/node_modules/camelcase
3344 silly ADD node_modules/jest-util
3345 silly ADD node_modules/jest-snapshot
3346 silly ADD node_modules/jest-snapshot/node_modules/semver
3347 silly ADD node_modules/jest-runtime
3348 silly ADD node_modules/jest-runner
3349 silly ADD node_modules/jest-resolve-dependencies
3350 silly ADD node_modules/jest-resolve
3351 silly ADD node_modules/jest-regex-util
3352 silly ADD node_modules/jest-message-util
3353 silly ADD node_modules/jest-haste-map
3354 silly ADD node_modules/jest-config
3355 silly ADD node_modules/jest-changed-files
3356 silly ADD node_modules/graceful-fs
3357 silly ADD node_modules/exit
3358 silly ADD node_modules/ci-info
3359 silly ADD node_modules/chalk
3360 silly ADD node_modules/ansi-escapes
3361 silly ADD node_modules/@jest/transform
3362 silly ADD node_modules/@jest/test-result
3363 silly ADD node_modules/@jest/reporters
3364 silly ADD node_modules/@jest/console
3365 silly ADD node_modules/jest-cli
3366 silly ADD node_modules/import-local
3367 silly ADD node_modules/@jest/types
3368 silly ADD node_modules/@jest/core
3369 silly ADD node_modules/lodash.snakecase
3370 silly ADD node_modules/@discordjs/ws
3371 silly ADD node_modules/which
3372 silly ADD node_modules/shebang-command
3373 silly ADD node_modules/path-key
3374 silly ADD node_modules/cross-spawn
3375 silly ADD node_modules/file-uri-to-path
3376 silly ADD node_modules/prebuild-install
3377 silly ADD node_modules/bindings
3378 silly ADD node_modules/@types/http-errors
3379 silly ADD node_modules/@types/mime
3380 silly ADD node_modules/undici-types
3381 silly ADD node_modules/@types/send
3382 silly ADD node_modules/@types/range-parser
3383 silly ADD node_modules/@types/connect
3384 silly ADD node_modules/@types/serve-static
3385 silly ADD node_modules/@types/qs
3386 silly ADD node_modules/@types/express-serve-static-core
3387 silly ADD node_modules/@types/body-parser
3388 silly ADD node_modules/@types/node
3389 silly ADD node_modules/lodash
3390 silly ADD node_modules/ws
3391 silly ADD node_modules/prism-media
3392 silly ADD node_modules/@types/ws
3393 silly ADD node_modules/undici
3394 silly ADD node_modules/magic-bytes.js
3395 silly ADD node_modules/@vladfrangu/async_event_emitter
3396 silly ADD node_modules/@sapphire/snowflake
3397 silly ADD node_modules/@sapphire/async-queue
3398 silly ADD node_modules/@discordjs/collection
3399 silly ADD node_modules/tslib
3400 silly ADD node_modules/ts-mixer
3401 silly ADD node_modules/fast-deep-equal
3402 silly ADD node_modules/discord-api-types
3403 silly ADD node_modules/@sapphire/shapeshift
3404 silly ADD node_modules/@discordjs/util
3405 silly ADD node_modules/@discordjs/formatters
3406 silly ADD node_modules/typescript
3407 silly ADD node_modules/path
3408 silly ADD node_modules/nodemon
3409 silly ADD node_modules/nodemon/node_modules/has-flag
3410 silly ADD node_modules/nodemon/node_modules/supports-color
3411 silly ADD node_modules/nodemon/node_modules/semver
3412 silly ADD node_modules/jest
3413 silly ADD node_modules/fs
3414 silly ADD node_modules/dotenv
3415 silly ADD node_modules/discord.js
3416 silly ADD node_modules/discord.js/node_modules/@sapphire/snowflake
3417 silly ADD node_modules/discord.js/node_modules/@discordjs/collection
3418 silly ADD node_modules/cross-env
3419 silly ADD node_modules/better-sqlite3
3420 silly ADD node_modules/@types/express
3421 silly ADD node_modules/@types/cors
3422 silly ADD node_modules/@discordjs/voice
3423 silly ADD node_modules/@discordjs/voice/node_modules/discord-api-types
3424 silly ADD node_modules/@discordjs/rest
3425 silly ADD node_modules/@discordjs/builders
3426 verbose cwd /home/<USER>
3427 verbose os Linux 5.15.0-119-generic
3428 verbose node v23.11.1
3429 verbose npm  v11.4.2
3430 notice
3430 notice New [33mminor[39m version of npm available! [33m11.4.2[39m -> [34m11.5.1[39m
3430 notice Changelog: [34mhttps://github.com/npm/cli/releases/tag/v11.5.1[39m
3430 notice To update run: [4mnpm install -g npm@11.5.1[24m
3430 notice  { force: true, [Symbol(proc-log.meta)]: true }
3431 verbose exit 0
3432 info ok
