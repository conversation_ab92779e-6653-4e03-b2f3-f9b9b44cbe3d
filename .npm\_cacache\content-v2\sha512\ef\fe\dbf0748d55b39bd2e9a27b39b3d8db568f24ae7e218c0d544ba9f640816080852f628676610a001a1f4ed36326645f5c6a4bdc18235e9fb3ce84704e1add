{"_id": "@types/yargs", "_rev": "1231-f585231ec6f0e4014aa4ab7f46a1c001", "name": "@types/yargs", "dist-tags": {"ts2.1": "6.6.0", "ts2.0": "6.6.0", "ts2.5": "12.0.1", "ts2.6": "12.0.1", "ts2.7": "12.0.1", "ts2.4": "12.0.1", "ts2.2": "12.0.1", "ts2.3": "12.0.1", "ts2.8": "12.0.2", "ts2.9": "12.0.2", "ts3.1": "15.0.5", "ts3.0": "15.0.5", "ts3.2": "15.0.10", "ts3.3": "15.0.12", "ts3.4": "16.0.0", "ts3.5": "17.0.0", "ts3.6": "17.0.2", "ts3.7": "17.0.7", "ts3.8": "17.0.8", "ts3.9": "17.0.10", "ts4.0": "17.0.12", "ts4.1": "17.0.15", "ts4.2": "17.0.22", "ts4.4": "17.0.24", "ts4.3": "17.0.24", "ts4.5": "17.0.32", "ts4.6": "17.0.32", "ts4.7": "17.0.32", "latest": "17.0.33", "ts5.4": "17.0.33", "ts5.6": "17.0.33", "ts4.9": "17.0.33", "ts5.8": "17.0.33", "ts5.2": "17.0.33", "ts5.7": "17.0.33", "ts5.3": "17.0.33", "ts5.1": "17.0.33", "ts4.8": "17.0.33", "ts5.5": "17.0.33", "ts5.0": "17.0.33", "ts5.9": "17.0.33"}, "versions": {"0.0.14-alpha": {"name": "@types/yargs", "version": "0.0.14-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/poelstra"}, "license": "MIT", "_id": "@types/yargs@0.0.14-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "48547d324b912311e1d51357f43b2b06e43a05e7", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-0.0.14-alpha.tgz", "integrity": "sha512-M+4WFJYs0FCxNUTJtyjKdRzDESu3UIXUBCH5B3e4lE/4E3EMHu0xEr158IfoyEPJarcmH0KhvijrJ5zcGe54Ag==", "signatures": [{"sig": "MEUCIQDuEPYNrohpGgAqBTRZeswHbtIgojbI0xB/HH4qm8roFAIgFGVSqB22DU3+IWwsoYO9Ppzi9b8pWeozlXXOqkzNDaQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\yargs", "_shasum": "48547d324b912311e1d51357f43b2b06e43a05e7", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\yargs", "_npmVersion": "3.8.2", "description": "Type definitions for yargs from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-0.0.14-alpha.tgz_1463512754383_0.5021597156301141", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.15-alpha": {"name": "@types/yargs", "version": "0.0.15-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/poelstra"}, "license": "MIT", "_id": "@types/yargs@0.0.15-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "f0f5fccf391154241c50882991ce1bfbbcd0fade", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-0.0.15-alpha.tgz", "integrity": "sha512-Fitb/LeWvj6ctHIh5eDioYfA+BnTWc+mu57WxY0TRd2EHJk8vIAPymQNMVD9nuctayEYJW62WsP5BRT6WssEQw==", "signatures": [{"sig": "MEUCIE+gIklSNg+Dj6ePR+SU1l2fDn7IjGBRd2GhH19hUSZCAiEAzuFBwXq1zh2EKY7EUVyj3jUGwieRibvCBOiqLRLMSDA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\yargs", "_shasum": "f0f5fccf391154241c50882991ce1bfbbcd0fade", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\yargs", "_npmVersion": "3.8.2", "description": "Type definitions for yargs from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-0.0.15-alpha.tgz_1463698039265_0.3103389753960073", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.20-alpha": {"name": "@types/yargs", "version": "0.0.20-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/poelstra"}, "license": "MIT", "_id": "@types/yargs@0.0.20-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "1437c7e298dc80951aee23e4b813e262abdde097", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-0.0.20-alpha.tgz", "integrity": "sha512-fZ8Arl6HupO0kaDNs0y3Y6YTyG6MImIyR98SI1xu/zSiAgLa88KEplvZLRPeci9dvNPSIdFAnDfBAY5bynnIUA==", "signatures": [{"sig": "MEYCIQD+9FnZCFLhBXy6a5fyfC9lZAKUCv3h6TV3qZZWPjw0mAIhAMU+KrRZ/hi6ueeg/lgCvzNc/JsY2mRrnmRlFxaU1cWc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\yargs", "_shasum": "1437c7e298dc80951aee23e4b813e262abdde097", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\yargs", "_npmVersion": "3.8.2", "description": "TypeScript definitions for yargs", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-0.0.20-alpha.tgz_1463777875490_0.7535920685622841", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.21-alpha": {"name": "@types/yargs", "version": "0.0.21-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/poelstra"}, "license": "MIT", "_id": "@types/yargs@0.0.21-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "7d5d7cda247c3af42b6c0deba1f78bea3e4fa6ed", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-0.0.21-alpha.tgz", "integrity": "sha512-1Q7OSU2KS0dPqoZ7cAHLOxGFVe4A5qjd3fvjysQMrHTyKcEIoIbRLQpHvB2MuLT+8aR1AFggHs44e1JYpurxnQ==", "signatures": [{"sig": "MEUCICztVnH0aaJGunDeopWCPgYzt0jEo6kYnic+ocBaAhOnAiEAySQ374ZrXEceG2CqtFIdOc4e3AhNywDbda/17CXGHHc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\yargs", "_shasum": "7d5d7cda247c3af42b6c0deba1f78bea3e4fa6ed", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\yargs", "_npmVersion": "3.8.2", "description": "TypeScript definitions for yargs", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-0.0.21-alpha.tgz_1464156765645_0.6809182935394347", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.22-alpha": {"name": "@types/yargs", "version": "0.0.22-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/poelstra"}, "license": "MIT", "_id": "@types/yargs@0.0.22-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "68a3713d34101d74ba39e0ed44d368e0b181561e", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-0.0.22-alpha.tgz", "integrity": "sha512-kptrFBuAtCkc86gWehjv5jP4mnCMMAWI6XVBbYfNK3t8mvo7JARq5NUV/WJzCufWydNjK3yk0TJYAoAYz8oGfg==", "signatures": [{"sig": "MEUCIFGBsUhP0XTJWz2fm774BeWoLFxueDxvhyri3/wcgZz/AiEA6CYoUXUJicqfcvDzn4jlTGj3YIaL93E3iTyAV0GHs5k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\yargs", "_shasum": "68a3713d34101d74ba39e0ed44d368e0b181561e", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\yargs", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.9.5", "description": "TypeScript definitions for yargs", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-0.0.22-alpha.tgz_1467406463191_0.46642183093354106", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.23-alpha": {"name": "@types/yargs", "version": "0.0.23-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/poelstra"}, "license": "MIT", "_id": "@types/yargs@0.0.23-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "0ac69d52e57e7a01bfe1ef18be05307097808584", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-0.0.23-alpha.tgz", "integrity": "sha512-ZBQNHUmg4eaxoL45WcWPQiv3hGMRrgDcVanpJcy96CgF7wphJNMs1xTSLVHhlt4ejMX5rvQ5tZaN8vsXkdKD1g==", "signatures": [{"sig": "MEUCIQDczvmBjJDhRiPraa9uLVsU+A60hG2Uko2DfubCamoatQIgeLA4SvwDwAloDS8lZCTuo73YL0gsNWXDfJ4KFIzb7oU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\yargs", "_shasum": "0ac69d52e57e7a01bfe1ef18be05307097808584", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\yargs", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for yargs", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-0.0.23-alpha.tgz_1467418673438_0.8948383273091167", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.24-alpha": {"name": "@types/yargs", "version": "0.0.24-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/poelstra"}, "license": "MIT", "_id": "@types/yargs@0.0.24-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "f66161be13f778504048becd751e118d22a2f3ed", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-0.0.24-alpha.tgz", "integrity": "sha512-NCsw4js4iOn6XCtYOzOxDIFq5uylfISWWz/nBdPxap0mBuxglvMt6XzvBN5KVsHpiyX5VP4kUaZOX7Hb4gXHZg==", "signatures": [{"sig": "MEUCIQC1EqbtO/aP7XjXhXppJ1DWpkd9zz5k+1wkwnBi24A1/AIgcqYwn2HGdNVfaqCSMXMfonDPeEhb4xDCo+SyhooUEig=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\yargs", "_shasum": "f66161be13f778504048becd751e118d22a2f3ed", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\yargs", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for yargs", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-0.0.24-alpha.tgz_1467431010928_0.6505357888527215", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.25-alpha": {"name": "@types/yargs", "version": "0.0.25-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/poelstra"}, "license": "MIT", "_id": "@types/yargs@0.0.25-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "022574e2686f895ec583a6a4b755a7b543c5ea4c", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-0.0.25-alpha.tgz", "integrity": "sha512-sHMF+9gU4J/wS3Udzf3i0fJ14dreWyxppPpOwzCiV+PenDAtoe9nWegi5JveHPhmiB5P7KvykD8fnyfMJee59A==", "signatures": [{"sig": "MEUCIQD5runrpG8d75ioTEjn1MK0NL//rwfQReycOJ91AL14lwIgQBJ2orq9kGN3cGkRbasmhfG6Rd48vl9RO8XG8LCuEuE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\yargs", "_shasum": "022574e2686f895ec583a6a4b755a7b543c5ea4c", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\yargs", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for yargs", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-0.0.25-alpha.tgz_1467596032407_0.17527196579612792", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.26-alpha": {"name": "@types/yargs", "version": "0.0.26-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/poelstra"}, "license": "MIT", "_id": "@types/yargs@0.0.26-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "980dbb9fda51d5b70805027a80e5039c9e4fc54e", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-0.0.26-alpha.tgz", "integrity": "sha512-dlcrqwpd/DgKhSrAlNjWSeCOxLeFBnQzONXxZNLa4cWI4Pyq6dPZBy7J0ModxIGESUlk6iMWSK/zHJjGNHcVZw==", "signatures": [{"sig": "MEQCIFY/oZ1e0lEWaVgrD6aNjBveiMYemr23r/PevLQMAtmoAiBWb1rt/Qp/Yt/lxpOgXG1fjKlNZvDzQTZvGLYSFmy+dg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\yargs", "_shasum": "980dbb9fda51d5b70805027a80e5039c9e4fc54e", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\yargs", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for yargs", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-0.0.26-alpha.tgz_1468014494716_0.11605168972164392", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.27": {"name": "@types/yargs", "version": "0.0.27", "author": {"name": "<PERSON>", "email": "https://github.com/poelstra"}, "license": "MIT", "_id": "@types/yargs@0.0.27", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "73f5cd692fee3557a0ba12eef5f6a89b2c1be6c4", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-0.0.27.tgz", "integrity": "sha512-+6VRdxN/ijYpK9PJ4kxxeXfy/JActsFD2fL1Al0ZW9ekSZsLz5groCanbWnzbgDKeUFGsOhHSTQ8hOoMI0TnVw==", "signatures": [{"sig": "MEQCIBGe1NXgKRBP/Pe+ITlWE7NHEaN1oSCki9HfcNn0QawtAiAOfHTEuwnevHdMtXwqHlxS2Ps8XUzq4t/ciIe0pUFwaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\yargs", "_shasum": "73f5cd692fee3557a0ba12eef5f6a89b2c1be6c4", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\yargs", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for yargs", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-0.0.27.tgz_1468513597694_0.9733309638686478", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.28": {"name": "@types/yargs", "version": "0.0.28", "author": "<PERSON> <https://github.com/poelstra>", "license": "MIT", "_id": "@types/yargs@0.0.28", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "09b85ded3c4c2939ac2b09d82e5862b14a7029ec", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-0.0.28.tgz", "integrity": "sha512-9CsTdD0moWn8cYtBa7CtHCepSP4JN0YsTRVT+6Rb0bGdFfAQy6qBLrHaeIRw6lLugdPBiQgesgeZ0ZHOt8TDYw==", "signatures": [{"sig": "MEUCIA2mfyaQ/JXNpZ5ppWTitUOVAnF9Ci58+qx2CozD4SVwAiEAoWDbIaQBUuFUujsaL3f7ph1u+Za+bjGimf+J8J4J00s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-0.0.28.tgz_1470154553772_0.8299409840255976", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.29": {"name": "@types/yargs", "version": "0.0.29", "author": "<PERSON> <https://github.com/poelstra>", "license": "MIT", "_id": "@types/yargs@0.0.29", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "996e087616b1fddd2d5e221028f7e70165a67df1", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-0.0.29.tgz", "integrity": "sha512-uiCHWpv5HG8aw2392aFQVOq/U1kb2iIaCzz5PbJNDqSmxuorNy8oqNNgRM51gj6mcdHTew6G0L3g94gyQLHn1Q==", "signatures": [{"sig": "MEUCIQC6Fh/4nBP9ZrprtCN5sSNlRU64gWfPxReHOQqsLUN7OwIgDfF6fIaWpYG9ILaBwEm+tDC+M2SfQO6HSG4eUmOiUes=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-0.0.29.tgz_1470923724604_0.7135619251057506", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.30": {"name": "@types/yargs", "version": "0.0.30", "author": "<PERSON> <https://github.com/poelstra>", "license": "MIT", "_id": "@types/yargs@0.0.30", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "5d37290e02ad162ce05688515480dc2a9474e2d3", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-0.0.30.tgz", "integrity": "sha512-wJ3LFbVNyFfqNASh3nwSdGX4OH33Y9Ei3vyRliPzrP54YLNZQiGnPVbNwqjzI5pcgFmK2FHz7rvtAkr70d1kHA==", "signatures": [{"sig": "MEYCIQCb4I+mmmxOtaCq5/hXOOPmxX/2sGIPSnIhPgabFzvGJwIhAKMblVqGThOtbDoNKOxBL4P1zjBDCZz8VYeewL3OvKCH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-0.0.30.tgz_1472151885945_0.03414057660847902", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.31": {"name": "@types/yargs", "version": "0.0.31", "author": "<PERSON> <https://github.com/poelstra>", "license": "MIT", "_id": "@types/yargs@0.0.31", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "d4777f40d25787596c216040b81932ccbde2b56c", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-0.0.31.tgz", "integrity": "sha512-jMBkoKmbGm+zLisAumtzOwlYNJQqtYKMZ8GbFx0hE+Rr4jHFga5SQo4bKwcK1BMd8adoqhz9O09ora1V3JHUvA==", "signatures": [{"sig": "MEQCIHPLVivNWBlP7oo542i7cCdaxkjJo7PglfaEFMvujl6UAiAUvXtN53oNS/ec07Odyb5tZhSs+bbB1icFxOD+UgkMCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-0.0.31.tgz_1474309292144_0.416815155884251", "host": "packages-16-east.internal.npmjs.com"}, "typesPublisherContentHash": "65b1a3dcb1ec0056dd8879b4454312683230f90410e07d9de6e6ee3b231f10dc"}, "0.0.32": {"name": "@types/yargs", "version": "0.0.32", "author": "<PERSON> <https://github.com/poelstra>", "license": "MIT", "_id": "@types/yargs@0.0.32", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "d4c2b99be291bab12fd1c23fc472c4c2bba161cc", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-0.0.32.tgz", "integrity": "sha512-XrXT1ENeQ4fmI4VtHG94yZdcXDU1WpKVjMp+clr7F2Q7jGHZPGizbYRcW4yNqceCkvDEt5B3D/kz1ic5pOaFgg==", "signatures": [{"sig": "MEYCIQCku12Na6OkVG23RsN+jfk/MrDiXAmR2Ueil1QypPwqBAIhAOrDN2vbHYtUNFFtVjmoewUIuoI59KU+VszEXGH65sXM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-0.0.32.tgz_1475773572351_0.9036764723714441", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "ba505357562494cf2f41ed38592b945d239906d932aace1bf0f1131b7d28b0a5"}, "0.0.33": {"name": "@types/yargs", "version": "0.0.33", "author": "<PERSON> <https://github.com/poelstra>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/mizunashi-mana>", "license": "MIT", "_id": "@types/yargs@0.0.33", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "1a5313b7485055f71bab4e3e2f7325981390af51", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-0.0.33.tgz", "integrity": "sha512-s4feibHuiM+Bli/nkm7d/wkTBFGlSa6plZY19kqj799lRj3h+5+G4IOSiFvr0UmB2LNlSOigG/Ev8iTMtdh5IA==", "signatures": [{"sig": "MEUCIFI8SInx4Bf9UTdXtXBySnjrw3TQG+7URKKXkxFeYqmVAiEAxVnNekw5JG92aOOCQZrTleO8OacEsXMoXrc4sKitGfQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-0.0.33.tgz_1476211710012_0.6248610080219805", "host": "packages-16-east.internal.npmjs.com"}, "typesPublisherContentHash": "0a6484ee728a389d05df57ba31ac360727b46d60d728ded5c894ff063827ace6"}, "0.0.34": {"name": "@types/yargs", "version": "0.0.34", "author": "<PERSON> <https://github.com/poelstra>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/mizunashi-mana>", "license": "MIT", "_id": "@types/yargs@0.0.34", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "1560429fc550c43bc41a7b7d3dfa0afbcc914a35", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-0.0.34.tgz", "integrity": "sha512-Rrj9a2bqpcPKGYCIyQGkD24PeCZG3ow58cgaAtI4jwsUMe/9hDaCInMpXZ+PaUK3cVwsFUstpOEkSfMdQpCnYA==", "signatures": [{"sig": "MEQCIBkfrkBagIvPY+lupt7ioql0CkbHXb5eL7sMQjkfzO74AiBf4iQM69V9vTb4KV2IweDH4ACgw3BOAJeb1ZOWPaXa0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-0.0.34.tgz_1477510173155_0.5542305284179747", "host": "packages-18-east.internal.npmjs.com"}, "typesPublisherContentHash": "e610252bcd82e49bcfb5cd652fd868ab04bec4f92861cb04576ffe68a6ae0d63"}, "0.0.35": {"name": "@types/yargs", "version": "0.0.35", "author": "<PERSON> <https://github.com/poelstra>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/mizunashi-mana>", "license": "MIT", "_id": "@types/yargs@0.0.35", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "407733576aced4895fbad67a3f0af633d2dfcbad", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-0.0.35.tgz", "integrity": "sha512-cXgmr2XZj6k+tNved/HHocCqK6ToAW807t8U2esw7bf2h2iIU0i5gyllALi6rX7nd076e9fpV3KsLh39zZIvXA==", "signatures": [{"sig": "MEQCIF7Xbfgbpu26RJZTyiPfuhWkswpRd9z0cCgW1O5BeDWmAiAeBJTWBPYgunDLcGUXS3deAPn/hOO2koOFTAylQzafqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-0.0.35.tgz_1478005089724_0.6062543736770749", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "e68bc04ce382de4fdb1272e172544ed519cd8b096158b3a9818567a72cf03895"}, "0.0.36": {"name": "@types/yargs", "version": "0.0.36", "author": "<PERSON> <https://github.com/poelstra>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/mizunashi-mana>", "license": "MIT", "_id": "@types/yargs@0.0.36", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "5b901a46acda4f376a89245759adb369328eee5b", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-0.0.36.tgz", "integrity": "sha512-I03EEBf/oSJ2Xssh3bVlH7kdJeLFvZEuvaguwOAo0OAZYmahaB8VAHN4XWW5AShTKEoD1F33NRknzuweZAIaEw==", "signatures": [{"sig": "MEYCIQCVMtMMC00MBoArfIvrQvsygdmyArk0gF7hw+Nn6WDtvQIhAP54VL+Gj744Kf5g9iUeOUg9QhSeXNTfdDw9vsB4GLOn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-0.0.36.tgz_1478005322481_0.7942832577973604", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "1929474cdc715fbd90b7ea7d3f01da58e977753f34d2a29e93a666eac9619f7c"}, "6.3.0": {"name": "@types/yargs", "version": "6.3.0", "author": "<PERSON> <https://github.com/poelstra>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/mizunashi-mana>, <PERSON><PERSON> <https://github.com/pushplay>", "license": "MIT", "_id": "@types/yargs@6.3.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "78ed0b6197f0c0dcbb798a12e908362ceed22f71", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-6.3.0.tgz", "integrity": "sha512-ciyQAhytoFZvskkR7qok26d4MblFxFU3bHdiNxakifteNMAV8pxF/rlD+BR2ZTVMatAoDvfk5SabhTkKCVM8SA==", "signatures": [{"sig": "MEUCIQD7ElbNsH2bkmnoylA9h+nBtc6iCBmMjmFpWivvdjdMXwIgGk5CuPB3rbq46jLdBazVy6K//pmm5U4sL6ApUWCeZZ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs 6.3.0", "directories": {}, "dependencies": {}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-6.3.0.tgz_1478195017258_0.4471048575360328", "host": "packages-18-east.internal.npmjs.com"}, "typesPublisherContentHash": "0305dead0231f4085cf78a714b96a4dab0ceb49959ef8ae1a6db1a44123b0b78"}, "6.3.1": {"name": "@types/yargs", "version": "6.3.1", "author": "<PERSON> <https://github.com/poelstra>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/mizunashi-mana>, <PERSON><PERSON> <https://github.com/pushplay>", "license": "MIT", "_id": "@types/yargs@6.3.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "5ca7f6a5ac66d435fedabe6e15bced609f9054f8", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-6.3.1.tgz", "integrity": "sha512-HKMUwhPha8skr4rxwcUh3h++c74KT+BH7aDH3urebK9QqsjmJJ8GhomLnnHG1LAd1xOd3lE16wRqwvRRjttang==", "signatures": [{"sig": "MEUCIF5jY9RIhWMY2stPO96383UrmwL0Q+w8YlJKcOlfct2TAiEA7rYmrmD+VraxTX/YdZg+z7PvTR3kihtwngG3PgQPJIg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs 6.3.0", "directories": {}, "dependencies": {}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-6.3.1.tgz_1478617571821_0.7968124577309936", "host": "packages-18-east.internal.npmjs.com"}, "typesPublisherContentHash": "2ab42ec302d732d3bc0ecc2ee4ae07bdb4831bb8abeac021438d6975d8ce85ab"}, "6.3.2": {"name": "@types/yargs", "version": "6.3.2", "author": "<PERSON> <https://github.com/poelstra>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/mizunashi-mana>, <PERSON><PERSON> <https://github.com/pushplay>", "license": "MIT", "_id": "@types/yargs@6.3.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "cc744e1f0b75727e35b82ae0a0a1c7ff46212577", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-6.3.2.tgz", "integrity": "sha512-DYbIJAf9yhOiT5sx5SR3bGqVkuxQJaEwq4OPmQUZTvEKKwTPIM+fxeQelmH78fGnfs0L/9IxD4gzn+C8YMsYnw==", "signatures": [{"sig": "MEQCIBPCgRnnzx8GjbzM1Pp1s3v339sjXxIE8FyppuSj6eZQAiB6Bhil15Y9GootjusaeIxD79MVnAx8HHjcagMUEWFN1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs 6.3.0", "directories": {}, "dependencies": {}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-6.3.2.tgz_1479152267000_0.33144539571367204", "host": "packages-18-east.internal.npmjs.com"}, "typesPublisherContentHash": "affdaa3cdd82793d448d1977e83f4fb5d2d146c9e0aa3d565191c8cdfe11f096"}, "6.3.3": {"name": "@types/yargs", "version": "6.3.3", "author": "<PERSON> <https://github.com/poelstra>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/mizunashi-mana>, <PERSON><PERSON> <https://github.com/pushplay>", "license": "MIT", "_id": "@types/yargs@6.3.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "b088bf22c126ab3fa3b110e29318b3f47894c04d", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-6.3.3.tgz", "integrity": "sha512-Qo5oYzFBbrYZQgakWahvUeYInmkJNzBgdk0Zr0RZyJMozYM1J/N0X7a06AVJ+PkG512zNH+gMjePT8SXCvZigA==", "signatures": [{"sig": "MEUCIQCFhB34bJstfIP32rtLMq/7KtAUQTrdOEljNu5fipHO0gIgB1eHzfJaaxPHWfFOrmIh3lD/hpSajJVfIj37w37kcAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/yargs-6.3.3.tgz_1480350683129_0.055670807836577296", "host": "packages-18-east.internal.npmjs.com"}, "typesPublisherContentHash": "5b9ebd53cb470e8ffc892ce7228a2cdfc28d2b0cac9e680ffa28646bb57e5edb"}, "6.5.0": {"name": "@types/yargs", "version": "6.5.0", "author": "<PERSON> <https://github.com/poelstra>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/mizunashi-mana>, <PERSON><PERSON> <https://github.com/pushplay>", "license": "MIT", "_id": "@types/yargs@6.5.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "308b17d5ca469f49ef76460baba793cb75657887", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-6.5.0.tgz", "integrity": "sha512-DIdZoVSfRJoHLLX/wYzYHaJU6m3OVDXBl3o22juFZKEcbxBKFB1XsG9m4r6xCGFCud6Z6syyD94Di9T7SvWJJA==", "signatures": [{"sig": "MEQCIHRN0orQQJU7YPLt2UgLBzo8p8kES1fPx0CNtvuVFLkVAiAHI4ISJbf0jAyP/edt9bdPykstuUURbgutHVG8JDcWbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/yargs-6.5.0.tgz_1482502602359_0.7856357144191861", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "53899bf300e2fc8803e417647ff3e4d0c8fbce151c0426128d3724e75ac65354"}, "6.6.0": {"name": "@types/yargs", "version": "6.6.0", "author": "<PERSON> <https://github.com/poelstra>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/mizunashi-mana>, <PERSON><PERSON> <https://github.com/pushplay>, <PERSON> <https://github.com/jeff<PERSON><PERSON>>", "license": "MIT", "_id": "@types/yargs@6.6.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "91f8e2580a8083049f78311c059aa57d6949df6b", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-6.6.0.tgz", "integrity": "sha512-021+XKlD4/hDZkkdgGhgtDGKlcTIXrII1lrCLp/ZNPoU0AHN9HmTNe+i1eKRxcZisFObX3ItTncemegEACgnsw==", "signatures": [{"sig": "MEYCIQCBnBMgNTR89/+2lKvgu5WrntyqEnJ6QJcSa2FrCF1+1gIhANdv4xb1okkxjXgO9Bc/6p30IAVX6eQ+6eRCEQRS0rhr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/yargs-6.6.0.tgz_1486500871969_0.22539031482301652", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "9b79b82e6860eb01d6ac04eb73af8ddb6a1a6dd62caf985caee6f441ec6466ab"}, "8.0.0": {"name": "@types/yargs", "version": "8.0.0", "license": "MIT", "_id": "@types/yargs@8.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>"}], "dist": {"shasum": "654d1e9d1729732a78436bde2b8076ee0d023693", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-8.0.0.tgz", "integrity": "sha512-HR3mkr3OuDvlbT0jtB5mkqOALEXik2mMz0kDNRRXBp7S83rbylqAKcTw+eY0yeTu8Nl7oVLf1nCI7zoJ4j+JMg==", "signatures": [{"sig": "MEUCIQDNjhMMIVbHdNE3Ymkdh/1jQN2+f86H0chBG+XnyENEpgIgb7NdRzrtxz95Ekp4kk9+4I9b0I6AwCkEQLsVxJT7Gk0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "peerDependencies": {}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/yargs-8.0.0.tgz_1498859200487_0.8413674111943692", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "71e333d1890d3635ff8d8fa07a03e2a999b5ddc8983c259e7ca901eddbe025e8"}, "8.0.1": {"name": "@types/yargs", "version": "8.0.1", "license": "MIT", "_id": "@types/yargs@8.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>"}], "dist": {"shasum": "f0e79284b713d9383f315fa38c71d933959637f8", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-8.0.1.tgz", "integrity": "sha512-VW1LZGMLomDEF3yr5FlM38gdoKqCkFXPs0q0J57eehhOmZnFh2Z/IKN89g6Q5uQoFxFYIsR9F87+e/tZ8hwUJg==", "signatures": [{"sig": "MEQCIEwR5rLk/u5fKWLYLZTsgrSLjJvr7mkecjFyEgHxxoOIAiBHnglm88rGW9x/lMU+oQWCErfmJU1y/F+Alm90dB65rQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "peerDependencies": {}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/yargs-8.0.1.tgz_1499716055116_0.41899351798929274", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8e76f45b7acb1ae810d25b1d95b44490c1d17d58edd20ac28f1e8fd2e1e38b94"}, "8.0.2": {"name": "@types/yargs", "version": "8.0.2", "license": "MIT", "_id": "@types/yargs@8.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>"}], "dist": {"shasum": "0f9c7b236e2d78cd8f4b6502de15d0728aa29385", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-8.0.2.tgz", "integrity": "sha512-Upj9YsBZRgjEVPvsaeGru48d2JiyzBNZkmkebHyoaQ+UM9wqj/rp5mkilRjSq/Ga45yfd/zwrNuML9f2gGfVpw==", "signatures": [{"sig": "MEUCIHZ09xYav+NjB7pcH5MUjafT8g5iiHXo157j0Er0hJsfAiEAwP+nqxRKziT+yS/RJUeo+V/+YlcnlnXnxwzYPfY2D+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "peerDependencies": {}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/yargs-8.0.2.tgz_1501622422516_0.2728897363413125", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b037ce9187ffa3e8ad3713f0e68c16c3d5a45863794058d4a279a4a45351648c"}, "10.0.0": {"name": "@types/yargs", "version": "10.0.0", "license": "MIT", "_id": "@types/yargs@10.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}], "dist": {"shasum": "b93aa88155fe5106cddf3f934517411ca2a45939", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-10.0.0.tgz", "integrity": "sha512-c1v82fFxticW/Iy49M8tbYy/4OTxuNAZHBVJTFlTYiW6JZrZxA0Yepj8c8qHgS3ZUxqW8M49awWaaF1DUJf/Ow==", "signatures": [{"sig": "MEQCICjSiD9/cN2EDp9mkF4E9gsrH0aVVQK6Ey/45lp/4rqlAiABD2YpKidKDnMbbp3mK9ljoNUoPtGPGkt62WbTL14YCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/yargs-10.0.0.tgz_1512591067544_0.44821297680027783", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "dba3a0c62a61e2c1e5b43a9164b6e9c04ef1e6204fb9fe5e5c0e886e5b9871c8"}, "8.0.3": {"name": "@types/yargs", "version": "8.0.3", "license": "MIT", "_id": "@types/yargs@8.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "6f0ad77792762fc69d209716dbab3201dcba56fb", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-8.0.3.tgz", "integrity": "sha512-YdxO7zGQf2qJeMgR0fNO8QTlj88L2zCP5GOddovoTyetgLiNDOUXcWzhWKb4EdZZlOjLQUA0JM8lW7VcKQL+9w==", "signatures": [{"sig": "MEYCIQCVKWez584J6fOdPUFkUCNCUSvyN59X66KV/2tWrPJi2AIhAK95Gtbv14HYXaAYtVyga0gOttYvajbjNmYQ5JFM0x2n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/yargs-8.0.3.tgz_1512591069450_0.4907161600422114", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d836b744e9686e8f0d13c5854e7311f5ca53ef83ad562a802c3d71608e10524a"}, "10.0.1": {"name": "@types/yargs", "version": "10.0.1", "license": "MIT", "_id": "@types/yargs@10.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}], "dist": {"shasum": "f986e2b5d37f1fb8c13c0ed15f45d01bcc3fb3d6", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-10.0.1.tgz", "integrity": "sha512-EvK+v8864qaRCjtqcJa7iUKWYTIvbdSZ4MJd99QTcBpq2FbVllwW7ldRBesBYINgj2Mn0yMQ2yZZJPej1DcJFA==", "signatures": [{"sig": "MEYCIQCwJndSkpX0kxwBBvd3tDRk1eat8tpTQBmV+mfflOmMrgIhAKzHt5awaTw0tdHfWMrHLF5NBIXwUQk5EY8RkB+IQY9W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/yargs-10.0.1.tgz_1514969280241_0.4409500495530665", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "22929d6a43057e6232ff6746e3e173e05c5d8d4070ef93b3f273ec279914a680"}, "11.0.0": {"name": "@types/yargs", "version": "11.0.0", "license": "MIT", "_id": "@types/yargs@11.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}], "dist": {"shasum": "124b9ed9c65b7091cc36da59ae12cbd47d8745ea", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-11.0.0.tgz", "fileCount": 4, "integrity": "sha512-vFql3tOxs6clgh+WVoLW3nOkNGCdeKsMU6mQZkOerJpV/CR9Xc1c1lZ+kYU+hNSobrQIOcNovWfPFDJIhcG5Pw==", "signatures": [{"sig": "MEYCIQDkZbEzxDVG3J2OrTTkGkQr+aP3WSBI+oxYaXN0avQbgAIhAN1+b2GZL0eu6nqz5i5yjghI679v0guNDrraGCAHAhI9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15562}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_11.0.0_1518563714745_0.6680582201386478", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "cc206e82380553e77085db67cf9e4cfc2efb9e903078296d0b635072d729cbbe"}, "10.0.2": {"name": "@types/yargs", "version": "10.0.2", "license": "MIT", "_id": "@types/yargs@10.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}], "dist": {"shasum": "cc4ea921877874d1261e2c44b89807bc836e1b12", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-10.0.2.tgz", "fileCount": 4, "integrity": "sha512-VbsIazac1gy20qTjEZVgDUhs8uuVmGbFkSGcdHpcMoXSC4+0vn/PRHz9YBqpgxKwUi8qoxf3eHff07w7aKNBOg==", "signatures": [{"sig": "MEUCIC7JPHL6aqde+RJ+ZSyPdOJ+KuI3Iy5MMuodUvu+FVfcAiEAvcOnGb4tN4xWqq6sRrsj/zTenbG4qm7zIpDMk3KGCoY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15361}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_10.0.2_1518563720162_0.8532182022735189", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "1a6ae9fb05601f8629790c5e6361c306832badcdce339ca6d107648ac47cc7e4"}, "11.1.0": {"name": "@types/yargs", "version": "11.1.0", "license": "MIT", "_id": "@types/yargs@11.1.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}], "dist": {"shasum": "923caa0cca53745d83f139eeff4fa7906c52ebce", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-11.1.0.tgz", "fileCount": 4, "integrity": "sha512-bhNrCSpRNPKlIejjo+WavCrqmL4rUjOWdqGhO57nVB9vEUX6Xw4Dj//Jge7jFSz2r2yzKt74Ce+Xx5W2Ul2xNA==", "signatures": [{"sig": "MEYCIQDTSTFEWxbIWFcXLOll4B+drfwHQG7wS2VX3WR25bP4yAIhAMXiRzooQYJrnJ4tOJvST3KwYhunqlrvNk0QHXR6CzUn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15706, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRVocCRA9TVsSAnZWagAA1LoQAI9bObNhcP8udZICafOb\ntauKBCZ2+rPHLSoP+FRBlKU0OT2m3zK2CldwzyXjvwrcSIc6d5bJ1uyJIZ+p\neA+zChljm2QXV9zjFtyvtWSy5hrv/BLMvkp8HZYP4CX/JyJVGfBtkY+iyVcD\nSqtZ1Fjm1FWltoIENAfCxnp8ryJvxwAx21NrsTymSThJpz1Gu4YcnUTRJc7L\nKHmjRF5RPHL8YbsMLXSBmvi1GXVLOCMuuUqwxDucOKnLkTfdy57qmja6AI4r\nRC+vLmgfJ1pwygBSzYfJ+/SPipt/p1zCQ8XqQA/eNDralw3dgV+3BV6V9yjS\nHMdndmNZ/s/R2UA1xZ2S444+tvUsRDvK6CZgvmqU/cZVO1xD3E3ApVBhvMQ1\nm21w87TIMWDO5a9sgWjQKCKlqew7mz69d0fWYqiBkHAmZLSFGyZXuaFcgd7r\nVm8pJRbt/AAKBz7L9HSZ4fLt4Blva3B8iucPKCl9rykqtssph7rWl+KItthw\noeAvVrf5Cv5asp+SC993u5lvEPsg4WD1zA84JUC86Rm8FybCweAAe+Qr3Zjy\nZYvUnIRKWWLeIuA4HDjpOxclnsfxFqgmM4qWtHJnkmfmSp7tnDnoUPTYQHh0\nr+7pPJc+VtO0+Q+2jvASBnqOCK+0soWyz4nwc3nQNIDrkIIaY5p1BiT913Sj\nYqz/\r\n=fnLG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_11.1.0_1531271708224_0.35790658892248817", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "443dd5122e4e308fca65b3bfd3a88c8d7d0832bf7fd067c6e6f978d5624c91de"}, "11.1.1": {"name": "@types/yargs", "version": "11.1.1", "license": "MIT", "_id": "@types/yargs@11.1.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}], "dist": {"shasum": "2e724257167fd6b615dbe4e54301e65fe597433f", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-11.1.1.tgz", "fileCount": 4, "integrity": "sha512-Awgju4dqD8kHXX3jc/B/LaryJC7MsyNfnbN62lIbFzTi0GewH64zrkh4bxo/YTgVEK6r9V3GNecxMhXTJw0+jA==", "signatures": [{"sig": "MEYCIQDndKQfX4i0ymPwIeVtqFckvqwXL0DJlqzPVKcXztg4VgIhAIr6PO9HEL+f6yXD3E4HZ05SRhW8uEu3X3r7WqxLM4LL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15745, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUpaPCRA9TVsSAnZWagAAvNMP/3tUdJPdJyJGs90wq32w\nlXH0sccfSOWKJJG17/8a1DuCF1HvgNSUJDD6I35Uo/B7vnDcEdgXaz1hF7fI\ncH+jzupvr51nvZawJ6jjySgi2Km9iruujqX2pOk+rv+q1HepOn91SWnvzWg2\nG+NCFHzJR7qsO077+J2ZpOZIVFQ3FJqL5kyIvkIbrAkcDDJmXGDcQ8dDFHe9\nKvIOBQbLZTEoVnspEPz/4kygUMiDT5pi2dU3s7aC+zsP/GGxtwFVy5IugD4n\nhFyGzRnAZhDkDxNCaHC1fpamIFHHrQZLiMuVAB3kcYpAsvF2rfQLKZojvy0f\n18zDKwcwKGf77lf4YONk2Cmtp5A34rbE61vIBGz0SnrwNfl627NrN4ZoxqC8\nMFRiiYWOcRVL1ubiiRiiaBZ0ZuJyQoLfzg2h1bqBbISs6P+r3SYN4RsyMfvV\n8y77lnL7l3cdVY0mXglJZIeJqRZOo1sQ5+0vn/Oeocgqw34+nKQER1lzJs4v\nkpLIpamNGzJ6vtCTTuSzWp4V83rbUDzi5uOSsVh9Eh811zolB8nmcdji9IfM\nx/JRyw/QTSUvt+6+zirfCXKWmXHRnziGBC5Avf/BIr80xj+7kyRL93ZnQ+EU\nwQrUhY13Hp6OUldcwFIxtxES0YdCuNSke5yacXi9z5lIoz9gdM+QjQU5fmPe\npVWz\r\n=y8Kh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_11.1.1_1532139151816_0.3891170725704296", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d3ee457c90ddf019fe342e3044e28e2bf6a79b719952c1df5fa62eb256c596bc"}, "11.1.2": {"name": "@types/yargs", "version": "11.1.2", "license": "MIT", "_id": "@types/yargs@11.1.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}], "dist": {"shasum": "fd4b676846fe731a5de5c6d2e5ef6a377262fc30", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-11.1.2.tgz", "fileCount": 4, "integrity": "sha512-zG61PAp2OcoIBjRV44wftJj6AJgzJrOc32LCYOBqk9bdgcdzK5DCJHV9QZJ60+Fu+fOn79g8Ks3Gixm4CfkZ+w==", "signatures": [{"sig": "MEQCIFl5lb58gM1//SBma0JAFAbdqIG77cgy95ZQH2QgTEKUAiAfI1ZbXKS6fY9NCqkhxDjiSWjG7qNETwbAFn3QQEa5IA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16089, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbn1t4CRA9TVsSAnZWagAAoPkP/0w0t5YURref+WcL24GM\nefy6W+OoVNsLKeMo1jG2gXUMVFpwYt1n46jzLJTqOQiNU8UFJ7Wq3Q9XqOUP\nW8w6gJS9vj90NJ+1kV/4wmcW+eTB7GbwAzYijQhF7Ho5te8tAIwMG8wFHCNT\nOxNxyIYEP4icd9TH8XuQV4+H+Bd0TjqEbkaeuPwT9OUIxE9zdNYuL0Ekcm92\nY58ElLilQ2lB5ZUQUCuV/Q6SsWLzmCvEbQsdguoTF/hgq3SEFeJMYZOh7BVa\noqsqACMpciCD/oB8kWmCmsZyLY2Iv4s/1PRfJXd71cKCnh/IF40yT67VnuQX\nD3xSnqMOQkYo0X3YUNVl/7GNVNc7K8+iy2ks1AEW3xJXEjTaPQfCmQVrdKOf\nYUf8Dp23LoS0nuX0Pk7dUuJXnUJLqR1W2NdobnFJ/6iKO9HDGjQKDNn8ae2M\nkL/h+OPOvDWWpF21irP/pLERQqA8O0QT/R4RKhe6EwosPGu7Jydmv1bu9FZt\n1IMsyqAOYP/bm6vCGsFWt3ZV7zrK06ZLt9LxKCe+uKE2+bmu4X1XDEoyEtrm\ngNQDcyUyzX1kD3yrmjYDa8awqvme4hulm4tAAlp5/KKmDLZChYbCI385FGIb\nq7KiuTDz52CxsRE3hN6F7+Wy7e2LjYkAbnpWokeMa53QKLkZf/ngc42CJZhe\n5Jpe\r\n=/zxz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_11.1.2_1537170296099_0.1224772356203232", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "4e151c7149908a36f5af264a09bc90ef844de409ba28d1e08a74c8e40e33f18b"}, "12.0.0": {"name": "@types/yargs", "version": "12.0.0", "license": "MIT", "_id": "@types/yargs@12.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}], "dist": {"shasum": "cfadf4ca78505a074d9ffcb84f203e97c3ed4347", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-12.0.0.tgz", "fileCount": 4, "integrity": "sha512-GTSFHeTWamRTKaApG47hgWsZWrjsw6PJSfrgntA9WAXKFUvjHi6Evf2JL0NkZNIKIX1Xi5wtV9sXMNJSC/O3Tw==", "signatures": [{"sig": "MEUCIQDfM2FoBZ3PwcZ1SaH2wLUy8ENYiXSJZJw+6It+r1YYYAIgXYJAuHvLsYWd7lSlnsbtZ76R6/sn6g8ebabhaDxjcPc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16226, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbn1t8CRA9TVsSAnZWagAAo18P+QAkEfMOXdNH4zsjUWNL\neo2vSZLBbKBaJgCiNpcktZFkvZmo1E6xjYvi+Stf/fSQAsSGv202avbHUCNO\ntQmhqHvJtNcFU+LjVzp2s3HZr6RaX1+3MlzkTYyRtQs3GT0Oogw56B2UfkKg\nguUF48FdIrya80Vy6LSBowgj6bM+FTk3n4hpOLbnOKgHxogB1lfDA2euPfzh\n3qRKokafGnGu8PQNPEYV18QIYBUkdqqy7aTx7fCUAtWBAoLuaOSDnJf1TOUt\nxgZa59XEh/fd6LL+lfbBLByuOI4ePNA3TqExLW8yjoaMqTnHXECMTSKDeVSV\nwVco9+K8SvkD/lUwKP1xXlfBjE1rrNvKz3hgOw7TX9ZvUccw9pBj0y2JdZbF\nPuqTba5h/AMDsEmKL91r6Mmbih/QR6ocAUjsoxmgkPKUNFHk4UglsITUoO67\nVflgmllUdOUXoPZiN6htPRz2WlccdxFrWO5AGP/bp3ems3xbqrr/vfAEqrQR\nMIaUr21RV2g1m16XMyL7soGqPjiiKXtYpQS/cfNQ7yaSpP5gvT8xA0zjJzh9\nXdCBKI7dAxr4/qODL3SnQyjnzLIxIu6QbntCXy0sHmqx1QipJ1av1j3Io54t\nnPFijyvCMZi+ON+9H0voyD6WJ8zj46DhmqdO5arPBioE59zOisqpGcIXUW82\nAYgY\r\n=On59\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_12.0.0_1537170299764_0.9337781389394944", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7c4387c17b589b29939b569af0a112f98b62245cba8ce42d1e17f8b33570a5cc"}, "12.0.1": {"name": "@types/yargs", "version": "12.0.1", "license": "MIT", "_id": "@types/yargs@12.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/tkazec", "name": "<PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "c5ce4ad64499010ae4dc2acd9b14d49749a44233", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-12.0.1.tgz", "fileCount": 4, "integrity": "sha512-UVjo2oH79aRNcsDlFlnQ/iJ67Jd7j6uSg7jUJP/RZ/nUjAh5ElmnwlD5K/6eGgETJUgCHkiWn91B8JjXQ6ubAw==", "signatures": [{"sig": "MEYCIQD8X0uytai5S/S0EOTuj7o+jaOCbe0EcI9iKK+mP5uNkQIhAMg1XE/l123iATYAmBv5GXMPi0v9gkW35ZYyclXqAFqB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16126, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbrqGICRA9TVsSAnZWagAAFcIQAKHF134Mdew2sjpim6kJ\nfKuOcQI0DXICBfalWzSgikE8VPaEDzXymeVr87SmeR5eAfNM3B4eWQjfGyMj\n2bKVU1nlZ4cCYn2PCnHcljoPvitwbqLZ0CT6N40dZ/drfuwwtY6tr27uBy3K\nLeE7+lSGqKUGsebiPtYxfKrbrbLhG+du7pPKmA9FHFPEG8+v3TWxrAwFYJTJ\n9ZZYxl0lnRxJUz+EJkBfkPxrzpghHcD01WZlV0vQsp+xpibdT4/NiVW93J/3\n+YvGQ9CZcF6eedzerZ/NmbtsOa8bxYigWYcn2Rbm3xYscCOggixQy+0gyZjo\nVZ/NPeDvze/nzq8oTxcIM+c4HpIvJ8U6QmWUWuIHx6eb8+xikYk3mZYV50a0\nK7kJDi5vJAScilkvwm1xp21Y/Cs/GDsmiOIhY4YcbpwJZj+6K5xJeoL8eZGH\nd8Zd66mFYva7OERv0Z91eDy7r1Y8gIqMUF4hj8GsHHBi4AF0FxtyMQm3J2vB\ndm1DPQOTCqXwij+xy68KVy9wD0yj+0oi8MscPESzgQNprWuQR8BdiJVKAckl\nc9qeaXnIsSRxhZOxZp+8ZgOOA5Kyy1p66E5uuAJXvxzhG3aF+r9suWkLSrbN\nqbdGjC2ARcVIex/zFCnjHR9QQCQMNakKzW3UCSvrPfEqPbJAutSHhWB8SYKP\noIsk\r\n=SeYX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_12.0.1_1538171272093_0.6983526786967587", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d89217e465a095bcf84e7a680cb0c11e6aeb194f3979a2cd236bac0371817790"}, "12.0.2": {"name": "@types/yargs", "version": "12.0.2", "license": "MIT", "_id": "@types/yargs@12.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/tkazec", "name": "<PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}], "dist": {"shasum": "7569157b2601d49d4442023c0d22f81d23f62d1e", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-12.0.2.tgz", "fileCount": 4, "integrity": "sha512-NUyxaQW48vjeDHybmhy7CFx/6of1+PoaEGPMI+0PzBVr5s3BTELT7gV4lNaxUsKllNS1YAjkeTEhBtzIDWs2WQ==", "signatures": [{"sig": "MEUCIQDn7yYF2P1XpWj+vCgoYMV+In4smD4FGxpkZQcT2+IHQQIgR1wZRRR4PP1p3REXIb/1y4JAptKIVqV3sn6ku7H20pw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23980, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcGuCqCRA9TVsSAnZWagAAuYUP/1zc6M7OLh4Y/M34BI0D\neOaJ96TI1jjhX6o2Yg1hDBq65npym9ZJsdnTU3q9+ajHG5YhljV1eAr8QwMb\n5Jc9fLWS5NDFVXqcVWkg7jdf0GdAmzH5VfFY5AUFMK9pxT5hU6vnhe2hs/ST\nq+SIDqRPmXLbs7FG2RWn/AjlqkOnRI3I27aHohXfeD3afpd+TLs/0p5bd4UH\n3tWTkFBNRGzmfeSk/mHIz/zJ12SPd34X+aMT5i6IsobeM+p+le3QCep6UbX6\n5Oc2T1GTpafZZI1QcGncQLwKZYQUrff/cHiDlGMbDsSI4GJz87TKh/h6Imyq\nzwjAMK0QV/ueEkHvZSQv1nS6QoBXt3648hNePftv/tq/QpniJHoDSupixd12\nmNpuC9YoB723vQlb+sisswJ2jsKyhcXhrDV6DT3PZxlHB8cNXQZrims5483Y\n7rzMzG2y4kuX5L7kN3AMTF7yL8OHMIrCkIJ7WSv8pQOshdIsf7eKD0d1Ec0r\nUidhZk7HoakKaE87kBTqZ3LH+xWFWVxsr4MmRRiKdxm02Smtp6gGVHNVYArE\nJRoXqnfcF00qUYoR/0mQzEBZvIUydLcMh6YZtzm7cl6/OYrv5HvU7X8kEN2f\nXDiuFxy2TWI+nT8nfF0lxnzDbdJaI1+coc0CupTZzsoATYKETJeo/hy15eMi\n9kAE\r\n=5oat\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/yargs_12.0.2_1545265321699_0.8227783760790486", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "9fec9595952d38ad676bc9b63e0bc3b330ff2a35db1eae7078a2ebc6211397bf"}, "12.0.3": {"name": "@types/yargs", "version": "12.0.3", "license": "MIT", "_id": "@types/yargs@12.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/tkazec", "name": "<PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}], "dist": {"shasum": "9a9fbaf6a81c9ebf5c283386162d1118274c6827", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-12.0.3.tgz", "fileCount": 4, "integrity": "sha512-/3AwpLvieyVFADvpP5MHkBOhHJ7pJYZFUex5noLEn/jSQT1pZInHdB7JproJz0E65ystrap36GMKiatijI8K5g==", "signatures": [{"sig": "MEYCIQDRT/kktfTAlnaXRqlA61qmSUbExyFLRzQDo0V2AFnCMwIhAIzTsRuO/eR7LtFgXsSA71MkAMTmQccuY0WtnD3ARDH1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24075, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcIbsxCRA9TVsSAnZWagAAd0MP/3PkFKvjNvHCYRznHzi7\n7bx1PaUJ1GNrdCr6n8u2s+nz5HMgM+jY+l42tMq4wLHWufzJwsjvHa0qnPat\nBi4zs4iUl9BrkxOrWCez7ZQnKezKxqov6yytf+QaxEFPIGis2JxZ9Sm5gvpq\n5Uzc5W8WNs9reyHOC+Z5b1PMxF9AutXOhGq83DGQeV0SxLkJdUSK3tj5+KVp\ntUCHJxjLRgFGH60n83Z5Y/4V9SY6cIiD/R7J1rhB/o+1KBbx6Ux2qqgtKKai\nZbE+tnAt3fwk6+bJ/jEEkJvHP+mYirShPtQaMDYH68kXQCYkYqwmHx+Dd7tg\nV/mT9Ayw1nNlWiz2zZ7Redj837sk7RT61YNkDpf/j2AXjZKV+xWBDJv/7m7f\nDyp/4GRpDspnQYUFP7l/JWRd9PW7iwio7BijxGESxbwujIjTpcEs4epeVEfT\n/H6G0f3xiggd74M2lm9Ur7m3Q50LQ5a4TxQULLmY2hBQ+8UWKGo+EIBtv9pP\n/gIni2J5dBtSd3EuUWgWBd2uy9qEFnT12fEjmeWUAHcUgMBh8B080PJ5/ea5\n19mS7S/XlDsBl5L1UmHLpHYajpCTtIw1bEP5/GvOUsjiVXW9uxeraZSWEL/T\no8tFIbiPxkTduStMClepTA6+Sf6Vw+ryIKO8CmwPfc5jiRAc5i7/PDF7luNx\nHnVH\r\n=31QM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_12.0.3_1545714481146_0.6563807222624229", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c23264db60b61f4da26432864983979fe2af6103fecbca84f6b6df949199b52d"}, "12.0.4": {"name": "@types/yargs", "version": "12.0.4", "license": "MIT", "_id": "@types/yargs@12.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/tkazec", "name": "<PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}], "dist": {"shasum": "14a37542f01a1ed4d743d5f8bc3f40e63e5835d4", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-12.0.4.tgz", "fileCount": 4, "integrity": "sha512-hBYcOaYPnlAPj2OZbivFFzsW61mN0L3N0Aq4KwRt/ZXlw1c25Srj8413MBu5IhFGOyOdjLi2/qOqtStDTXGs9g==", "signatures": [{"sig": "MEUCIQCaRb2ZgY+LWbdov+07XPsGjU5mYNEUmHac5Rp1YdE/9QIgMw0hK/FmkMSuNh2dNxUrKM7u6lroUxJ5IgAwk5esDCk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24080, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcIyPvCRA9TVsSAnZWagAAOawP/RfWhX9VmN7RhWzy6bCZ\n6uNsBZfycMbPqI2AGGfprMoV3GTwlJkYx81EBkpLvkd36wBRIE04dYrhjCRj\nGj9iFfzSeusJSLrbtT7PhFyp3e0iwXXKx+dccabakNyVoFX7ftZPxAwfxtSl\nhOgVVN6Lw7aaRc82i7aaTLmolg3IrFWFG6+KkixvzvSnePqCmUtOmWdmSZmQ\nGaYptFFhvXWzmE+ct9gsBg7k9BTfn/2Jaz/O6CjRqve+U1te2b16np6thE0g\nTFYVPRbb4mNBP7k+ySz2rINDtceadRMJi5FZPchcKoNdmd/LJ+yuZ30FoFTH\n0fepe9DZDxsYt/dXW/InFc8vP9GwE9d8dDw/wZZTj1+PIbKh0983UqwG4HVV\nWpn6UXDUKKGpZkcrgzYFEF2mXgCngkJX4avhUHUn5xFQ9e9Q4iMQ+5snSv7G\nKWqxImjJKF0iSTtZWWkkZdOoFxNun5bwOCoD0wBdt35q84RY2Cgh0yZK5Fxv\nzFgglWttdOJxuVsGWOXFx4ixvd4sqZlERDmWmJfeTmuHI3hdOd/w351dqEMu\nIwuJ2Pcx/UgZcxX31oM31iTFcYSO8iCDMr9g8i8gf0Mkuz+ev5mwAIf/xsBG\n7CjHmBB6T7+kxHS4SFlC5V4YKqE1oRFJdxFzAu2JOv2+4lqeF75wtoghifqf\nl+w8\r\n=lYXY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_12.0.4_1545806831115_0.7289530279086667", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "661d5822e65194cea3177d7d864f9c1c9bd3d4d18e9188c449bd70bdce43b9df"}, "12.0.5": {"name": "@types/yargs", "version": "12.0.5", "license": "MIT", "_id": "@types/yargs@12.0.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/tkazec", "name": "<PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}], "dist": {"shasum": "f4f85fe198e1e9cedc0bb53a8c2d30fb34299e50", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-12.0.5.tgz", "fileCount": 4, "integrity": "sha512-y51XRWWg1GiW4+mV2zmFQUqJT3bX7nuUaXW4j8a2dFvT9ZlaNLjb7ZxcUSDyklt/UBWrW2+31x+hQpRzxaTFzw==", "signatures": [{"sig": "MEUCIDKXg556hMqtoK9HKUgJ4u21uZBRDBnVHuCH8UbSsslGAiEAhaGZtcnuwT395zGwIfl6JoQP0nqBVj8qSUybSAIYwoE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24113, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcKkiYCRA9TVsSAnZWagAAlrkP+wRMj+OkCPEu1zM44Ff4\nNV63U8AXBu+SUq81ia6SzRXk+XAtDUTj8wiJ0BvrFFFJNG8H63YcHjeypBbw\n1ox2TB0x5H8weFAAPodYkjyQAZeLBl6dbVZjPAaowQDT0kzV0EnDr2agKN3r\nq2/XacdLxm/E5eimrnQ6s+E3MLZslJJ2PnduFQ94kUjTX2VkNM4nGztTLjrS\nFUnIQyoZoZwpgixHsZkA4pKnd4BuE286acClAK2Yw8YuK8sAuzYiAddOZLyi\nNj4BH7An4cT5uFqh4plPmZYFlg5eZk9RzD/CPIqjlHrVv4KByMvNIwgHxytP\nNtzB4nXGIwW6XU+p9pMYPDZ8prk8A5dFAD6RFDRFcKavWu0Oj9u7xQ10i26D\nlmarzxLve5bJ8x1RcW4+Es4U7N7BmlC6UO2lxtkxavRNonXV1MPRH7/bULvB\n8HBv+lbUZ2TrX0hoTsmEZe69srRJHXPd10Tv0qTiIu63Aodkios5bwYKR/4G\n/gnZhTg0SCQmgpn9t4IHhOUvtF1Lan/gRdN+A58epHOI7XIr8WqpXi+VbuYA\nCkYZPLzo7Jd08ePTUN6L5FV6EENLAcl10qdLnU7onZ3q4vEpBP02FoBiVdz9\nonfN3tK5TOzRCvvVvyVoV1QI0SB35t5UgCm9QX1yQ50O1UTxQFybT78FHSV4\nUfwK\r\n=wYmm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_12.0.5_1546274967904_0.4319440414813336", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b78c7590fee0bb28d640e69a0ac4e7259a9b7b50a350c46c73480ab4cb53fad6"}, "12.0.6": {"name": "@types/yargs", "version": "12.0.6", "license": "MIT", "_id": "@types/yargs@12.0.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/tkazec", "name": "<PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}], "dist": {"shasum": "579f95b303d135f0c16928acfbb3550c8a78d4c2", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-12.0.6.tgz", "fileCount": 4, "integrity": "sha512-lppGO3m316B4dY4AcbOVOjyOy3EH1M/Y4gMYNvJxricIJ9B72i5ntGJ8s5DJiSiW4t6J2yEByrGOGDEbBBLlVw==", "signatures": [{"sig": "MEQCIH9YM3kB9DK0fkY55uv37OHCiOH6e/tqmbB09ivjO/+XAiAU24fi0GwAEjaFKRpo7pgYmUqw/F92BmhdQglvaQbs5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcR1TzCRA9TVsSAnZWagAA1kMP/2AA4889rvikpR1cc6Lo\nwvHnocdWhl6ORUxRPAGjPPuCMx/GDqvK3a3VwYtg+G9QjWScemwbl7JD7Jxu\n4OOxBmfSLMvpTAzL8FXgoeJzqfUd9KLYr1Rt6z4wYbyLvSt+7UQFv4WNSrnL\nbBfqTOINfetNeij60iHOyEwRbWY3kSc/bphTNWAZKl0OyfuuollZdZDYdPsF\n6w9pv/eLOTt+YH3x++E66ZxLdm5eCcjpN+QpnTOuAZSaXQqVqd5VSGF3gt+9\n2LoAOtvKnodxauoVTapi+2XDF35q/VtCCGPfC3CEAz4fg1L5ICa9vOf0LlMW\nRjlL2aTXbw2LEMn8OZ3ZhF7iSGdhn263du5BkbYHrzCCH8H5pUOhWtWAdcsh\nJpAbQP+WKFdwHCfBuxNP8cZujRAjZEux4QDbTfXbisjgCAhR7/NVZoLXQbUk\nOV8xZvidAqNXIroGMgcUYVmDXS/rVRUmdDujkbheU5XDThz3u2Nc1da8erGR\nSw7093X+OTllyvWllQxrSMjyAfhKC8rFAokfrDqkhUwZHMbEMQ9tP1MKFTGC\nsHA9/GCc9Y0KG+L7e+XYULfewLZtjITZxNCSNeJA56rKWM8feiwh/LnIpP4i\npMODUdtUxdsR0J6I37AEmOE3zdzw+yQcgGytBmS+x58hz3XHmjU19qF8SeBg\nZaKF\r\n=Q67l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_12.0.6_1548178675132_0.7050634248253829", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d290f199781dd184e8349fd0730f94304519a30f2cbb87178ab9d13319028428"}, "12.0.7": {"name": "@types/yargs", "version": "12.0.7", "license": "MIT", "_id": "@types/yargs@12.0.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}], "dist": {"shasum": "a9dc9b6db5f585396d05c1f07f08020a3a768db1", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-12.0.7.tgz", "fileCount": 4, "integrity": "sha512-lVPWmPTzmjvYKQ1BONzcu+DdunbVWn72/x5RZYeES03/6gLsRHEv3nPIjuLG1lbmeXUqUuQ3iJDMj6oy5MFNfQ==", "signatures": [{"sig": "MEUCIH9rvdjm0/EVcctoggpdKJXnYCGi1Nu+VAMV0tprzmK9AiEA8ef9LCxTrCyhmoqJ86TNdd6YNCQ3rLPvN1rD+FJGY7M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24113, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSSL8CRA9TVsSAnZWagAAaAwQAIv56M/8KjD3T6a/y1rX\nyMvbjCO0D05QGdP8r0DDMcmsm+EWJLJ1V6M44zvyoQue/ViUOEjVT+siM7Et\nTZ/K4qXRQl7TeNYKIFyxGZOHZRn+HguXqL4iBCkFSurW+/nXg/5XJrOYFKMx\nmwsuuSzDoqkS2nvdk9LhEFUGYG1toOOFU7HiMxjQ7mbvqrwOofSgsAcQ1YD4\nAO0mRjKRt9XJbJSHZ6gzacgQW2GvnkPRd6vDzEKGqdcn/KHb7n/aNNLl1h1O\nN5oQBBzyVmTvUBH8pOlZ+1/OJkKN69SYKn8dzzglHg+4VJDI5ZMh9Tsy2Sue\nODdeelyXXl9pWegHUp10NGoDlz371YXoH0Q+kda7TF8zgVysx9jQR/SC6PWT\n2Q44C7HhodcZR8n72JxvKCIODSFnaGzjhNdcW6IwvpFhQDpcrjKq/yMI4LSs\nwOsLmqSAYtTgUrDphdWll1s23obKoZEnrtRIhU6WeXFTvN7U9RSz8nGqJ4KN\nDFRBSCSFQ/Z9g2M9EvrfYLcb/dKCej+3zACWk/2Lqs8Oi2wFnTUPSjHl7nP6\nsW9x3x1OfvTcjs1Ebaj6C4RrgmK+Vk2FA7rLDVzHzGoGMxguSxO7Tfz169MS\nkA9Al753RVZ4rSL2eCNOLVA2r80cFac7hHU7PpeT8lpT6q95IvsEyNeY3t6A\n3Kgc\r\n=ZcRA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_12.0.7_1548296955444_0.9047506234660068", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ed4322650527f599a980e23e571fb803beee9711e3be0424e35e9e743f090d06"}, "12.0.8": {"name": "@types/yargs", "version": "12.0.8", "license": "MIT", "_id": "@types/yargs@12.0.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}], "dist": {"shasum": "0b45bf0607a5509b921335658d87489c12955176", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-12.0.8.tgz", "fileCount": 4, "integrity": "sha512-OMSKUmZ09gbzITzx4nxnJqhprWC7JqsmlrEsVtb+cv3GXHNpv0kktqxhboKX52FnMggkQvT5ezt8pxTWyKpJHA==", "signatures": [{"sig": "MEQCIEPqRmuIs0Y7hSdJpqxvEICHQ3cdi8RwJJsWy67bhaljAiBKEC4VEaoj5lxzpUHHx6APWtA6ypHzg1QTn1xThz9VPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24158, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSkplCRA9TVsSAnZWagAAo0MQAJHt/bpnMizmQ4O2sawB\nZW2boePvhP80aYjk5xrrDwtlynTmKeIY0MLwZ3AyBh+7ALkzZWcy9IODwNB6\n4i0nmk35/+ZBgE579r0Zqb5wWu4mdDEAqeR5G1BB1dwffVPvGqyjHGE1gMqN\nLAUng5gt55MVLael3216eg4jg5ocy+jL3apoJOBT5Ou+MFf1LNyPTdP5KJ/6\nVSCW9Z9vDiqanBbdtGTNfg45PBQVRXnscEREza453PQrGi92d7U/5yvrsfV0\nvEZHhpfS0yBzlL7HK3zOJh6FC0axoVDXBkfnKlvyDCwM5R+OiL0M3N9rnoU6\nmhCY0S9n82KzrbAFYaP3sxQejxtMn2EG/R3rvQKRQrYleaiLeb4cmP7tkmJf\n/8c6gKXTJ4FVfDGTvcRCxKbiuFSP2vTCRhC01vaKoTyovoLQmjNhU3Pr3IZv\n0qW8kA5nOtsqi61Pt6bjnpStR4iPMwaWMSj/FXb86A8iS//Xc1xXyO90s+qs\nD25iVNQLZACEAIVCncwA/Khn/hcVjrPyYm5HaJYcHoXyCOznVPpYAmPd3HbN\n9g6/MaIlRff6akSHWWr+l4LbAQAU0oDsYH2jTiGVl9dwc1Xdnhm22kbvKDL/\n4YaLOAPrajevcUOtxDYHTkPIucr4GKdi0QnbivPmSAqcDQJj3UOkXeWd17Tw\noVNp\r\n=iOIz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_12.0.8_1548372580619_0.24176955480763884", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7a9c81087e186aaa5608dd46c979c8709f28194a21c1754aca5de73ae6f67573"}, "12.0.9": {"name": "@types/yargs", "version": "12.0.9", "license": "MIT", "_id": "@types/yargs@12.0.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}], "dist": {"shasum": "693e76a52f61a2f1e7fb48c0eef167b95ea4ffd0", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-12.0.9.tgz", "fileCount": 4, "integrity": "sha512-sCZy4SxP9rN2w30Hlmg5dtdRwgYQfYRiLo9usw8X9cxlf+H4FqM1xX7+sNH7NNKVdbXMJWqva7iyy+fxh/V7fA==", "signatures": [{"sig": "MEUCIQDInSLCWey/NiDTlgx0Dc/iXloNxoitFbuNfN5llfE1RQIgeCzzsEnOKWLnmQ2qtp4jzlUGUN4Ah1SRo3TjNEGGTjo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24180, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZMSvCRA9TVsSAnZWagAAqtAP/0rrGuaGw2LYGVxGAU5X\n/s9YMVKMylJJbl8dtGsPyAW+Xw5XgqTvOpmWxZOKl0Vbbgp9rZanj5it2HKf\nvH7r1yXgAoy0JtUYuPQc6Vgr3kb4FWXYC0h+NNe50BUZtpl+RW8x4CzfUKEJ\nIIlweL4A8oE5/46xPpc3U0B6JM+BrGpLz2uMu/kI0jcPXVQ62XHY9Kt+PW4M\nmspyZQci2q50/PDGI44czmtrw09AJbpOT0PIomXDvU1XOidCmyl7SvkjLg4A\nH8jMm2pao/UZsNHiWF/niBBfENcm0QEhXtF/OxkB15YYwGJiQ6sA5GJ60rZV\na3teSPhQbuJnCWRkSfuui6oUKsuD0qZY7A3YxpJ6n7ZsO5vBZWZ2z0F2lrmS\nPx4BuuqQRItU4T38BJx458vjIDtD6i1s5/1G16MySUH/ItkYjMtiMT5aPyax\nzevKzV6kMtX7TNTKsq72cZ3trX/9oGoxI2vfwO4fwIDF+RQGOCClVkjYinRi\nZaB306F1eDYFQ/D/sJ8u9hUVMYd2jfjY0b02ojZ3ZTRDlqYa7Jj5H1pI0RQ0\nWO391OdudbWYA9ULl7yE/d9vWT0+EN1mjY1rX3HnL/AMtxZZl8dZGJuk7LNV\nGL9usakZL0zAgpbF7FNvWPz/g8aUpuLdg8GGCTR+BL2n3jGoNzpOyqIshqWv\nMKJl\r\n=NPS0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_12.0.9_1550107822825_0.3368993596164753", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "fda2ac2cda4bb2a18e8f41d72ff8482c7c228b23cec274d1137fe4b1e377b2fa"}, "12.0.10": {"name": "@types/yargs", "version": "12.0.10", "license": "MIT", "_id": "@types/yargs@12.0.10", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}], "dist": {"shasum": "17a8ec65cd8e88f51b418ceb271af18d3137df67", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-12.0.10.tgz", "fileCount": 4, "integrity": "sha512-WsVzTPshvCSbHThUduGGxbmnwcpkgSctHGHTqzWyFg4lYAuV5qXlyFPOsP3OWqCINfmg/8VXP+zJaa4OxEsBQQ==", "signatures": [{"sig": "MEQCIHYyuf3oGY3uNajxZqCvJ2R+yFaRcjDPbvUAhlqPxZ8WAiAwdvoJFjh7Ni9aQz4eqpFm3pk1xwAsCipB8LKNDSlyIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24218, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcj/AcCRA9TVsSAnZWagAAqnEQAIaNHIY/PR80rxzIhwn9\nnnsmEWxjlirzA5Mq2gOX/sc5Mnnzq4z64mq3UtnAoWxWK5enYOJk14OAbZOQ\nQY0yhCUq/MNytqOcx97izfzqfQrt9Ypej5IbeyVeXM27NPF0GU4GqTlKYfPC\nCNh+JmM+ih1a8ph1EV8cRKOZ6RDaysAp3ZTqF+8A6oxVBt6WtbGV9mv6kRlH\n9nQSnYWJPVnzm3p1+jUyOBWsfJ0msvGxF84KXr8UneP6QzQoO0VBTBJhfDuL\nI/VixMejyY6qZnEeMMvRijokXnkfoIRABF1n6KddOqnwzU/tnN1I/7JWGti7\ni7Tz+k0E3XapgWARFLaizr9//uZh8Z2DRrSptKHwPSMRrW9jfCmIMq7KqMXw\n7cYEMxp0v3681ZsqXq7EZPRZp5GIJHoHfvwAj7TSPSea1WweQIuHiHqVaplf\n3aBjFrROMtVTIfh1vHUctP0zRTnn/0JvWghu1P0moPKkI/FSlADQdQeJQQVX\nDFAzfBSyhSB4Vulfutx4YT+UgQeOZExhgoGkJe8YFMVVM1h9hopn7aUQndW/\nMzNroToIJIIN79BnHHWhzGYJaL9AP9tbexjwpxF2VRgVcxGuGWLOhCTalHw6\n99S0cENGP16UUcJtW/bdY0TlxEV615ujnUGWnL+H4ITScIENH3dw0xZ8QgRb\nTk6B\r\n=F89v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_12.0.10_1552936987890_0.8339408608009629", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "16decea9a83fa2463923ceb31c20d21a09cc36cf83d3c1a900269bd3f006d4fe"}, "12.0.11": {"name": "@types/yargs", "version": "12.0.11", "license": "MIT", "_id": "@types/yargs@12.0.11", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}], "dist": {"shasum": "a090d88e1f40a910e6443c95493c1c035c76ebdc", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-12.0.11.tgz", "fileCount": 5, "integrity": "sha512-IsU1TD+8cQCyG76ZqxP0cVFnofvfzT8p/wO8ENT4jbN/KKN3grsHFgHNl/U+08s33ayX4LwI85cEhYXCOlOkMw==", "signatures": [{"sig": "MEUCIQDHlNRnsmommC/hoNPw/p5C6cYpqFubq0dL85OplCeAQAIgVeJS8HTlCbbu2aIfShehGhIJguzmp3314HGh6LITbe8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJconjXCRA9TVsSAnZWagAAdy4P/jxmda3JOkjXGm0NvkIt\nC+0fJV+ycthRkMMqd6utrygogUpAfLphTSpL6RYEP1iReGf3t8Lax4PdRt2q\nWSCC1eQt98xtIrUkrfe8JIUN/zfD32XXB86lawyMe4jXNHAAK68aXypmTYr3\ngoV6hpuwjMHasl0p1reNz/pv62DY9dmV0qBTgg2hY9VAvkFrK+6sTTL1JqGR\nCJKQ/KkQ7VgQ6GySPSZbH9ioyBvOmhxbxV1W9PU8LX7Ve/av7HNvsGmfeJXH\n+Grd2BDoNBz6eL20gbuUMNrQsupNmpv/dvQ0dY50B9Le0+wLHMqWSDeNNSvC\nf3GFSKW91j5d6DuPujYFTBOYHnGpNJ/+ouIUtuiZHFNAXQE4mkzSVd/EZC/A\n+D0jOLbX0bfNTcfaPVlGs7fEj0lZlwCqpdcP/ZS+qtcqjFJK2nMFZRH7SX31\nRIOQWtEEMtwO0008gDKhNNKNB2XJ/Of1FE29fynPvB5+nmdxJOyHnw6Gy9q/\nLg7ekrTyJDKctUGYMF7XLUeTAIiWNLe52xNa0YY0AvLOFpUMajM6c/eYUh/F\nnaLf/bPITLawjIgXWRKyXVlCl50PrMQmRwaCYwkRvMW53J3kRuI5WTLTZQiq\nDLX1Ov4qC20xeZr8TXN8LYJlVzySTCwPTdYKzMAbWzAJC045mfu1PwcFSp2K\n69ZH\r\n=07lK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_12.0.11_1554151639121_0.726011549113099", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "037a31d5454e25bccfefef7d3e330e3f114fcdeef67576cdf8b35ca85c9b1909"}, "13.0.0": {"name": "@types/yargs", "version": "13.0.0", "license": "MIT", "_id": "@types/yargs@13.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}], "dist": {"shasum": "d2acb3bec0047d8f648ebacdab6b928a900c42c4", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-13.0.0.tgz", "fileCount": 5, "integrity": "sha512-hY0o+kcz9M6kH32NUeb6VURghqMuCVkiUx+8Btsqhj4Hhov/hVGUx9DmBJeIkzlp1uAQK4wngQBCjqWdUUkFyA==", "signatures": [{"sig": "MEYCIQCDlPl3Hk0zRxNVWIshzwWPlEJctfGdBWHZQfBXlbCqFQIhALVHfbxvn0u+s8wYafPJBJrc6WCXZ8EsHOw3bTA66GVy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48829, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqqk+CRA9TVsSAnZWagAAGIoQAJCmv/WvQid/kTtUctw6\ndIl8+qYeukRP+TidG3hnG/lF6JfDn3G5c9NeS5R2JVCTyNbVVhqLQs2oZTZF\n1ILiZK7J3dwX/H54EHfvMFEG/YMRsN66UP/XVlt0NIHya8vkBosTejmkQUqz\nQOCTlKAyYRQ2pDC2gi5jhW7QsQP562xC5Z52u37+7rqJ5dKx7CrzuSu43eeB\ngcUIQTGKK1OwgXwE13OcoD0pi4cp1oVgfQkM0AzfS3Eiurob4z8ZfHBB61Vg\nPjwUMXMntA2xvNt9+L4kQbPC+ypFGRRoSHJGKflE4dIJHtZyTuMAWjdnIK7g\nwsghVD6G2c7xoQfN89d/FMuK1e0pVmTQkte18yNuQcJwSp8jq2MwAKBODVDu\nYrEav2vyyeNGdqALwBiUaJQ+ZKCv9PAgAJW++XD5WiUKgsuClFx2TgCwTAL6\nhNcuro8QyA39XNjsKBAaD91zTw+rc9nTaTqLjWx/8cmfgonXZ02XcbX/u+M9\nVr3MKJxrZ52GAkGDrulySPy2R6b60sAIp8R6b+pDMyavDDdMvWLzAxh4KTqQ\nsk58qqP+t4Y4dGxUQ0C4Cxcc4YYENGviTSgyXcSsWT5OjILAzWl5DVfZAU6l\n6aHe2tvwlxJ4o0V0LTazEvb5chh8F+IbC4Fbbm50Q6afhxZ/Nfx2rMLXXlog\n70dc\r\n=0Zyz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_13.0.0_1554688317844_0.6351838901271967", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ba177d8ce098c69fde6038d1da47d06641703bdd8660fe3516a7ea28f3202104"}, "12.0.12": {"name": "@types/yargs", "version": "12.0.12", "license": "MIT", "_id": "@types/yargs@12.0.12", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}], "dist": {"shasum": "45dd1d0638e8c8f153e87d296907659296873916", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-12.0.12.tgz", "fileCount": 5, "integrity": "sha512-SOhuU4wNBxhhTHxYaiG5NY4HBhDIDnJF60GU+2LqHAdKKer86//e4yg69aENCtQ04n0ovz+tq2YPME5t5yp4pw==", "signatures": [{"sig": "MEQCIFVx34m+smTpnujhbwzosj8bX+qvqeAlQOFPm2ng4b4jAiAJ5qGJoh0U+togM+g3RF/NVP4bI6ogM0dnqV6Zs4HizQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25455, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqqlRCRA9TVsSAnZWagAAPEEP/1vO46a7qItVCIHsuAEY\nL0Qt67BGbQi2HZrcACqBnV8W7JW/eEV4UKp5Yyia4hgGKvYZa2Xcy7VrUPMD\nh2y8bE+UIkRtqeNSHBGZXLTCE+aJMxl0ymi3Zb63Z0AAVGGoRYMHU5xdIX7v\nOOV61enHF8nzo+SuUdxWduWxgwKZh1I3WvWLmhrrSsLKji/vRvzKOh6NNNpa\noemRAbAjtWBzxIJi4YduF751/36hTlv+PaQpU/qf9VI16bkR2/yCUDWG+3kT\nl66UCnIN6vpRbvBDz4jm7w6MsaSVgud3fk/Fxdn8kQrwK+LayN7rBtmC8AFn\nx7Xp+sI2Ad2w2znGWJUymMv1elvOq6NumlNnpahfCUfKtXXAYqaU6b05kXCe\ngtRaGAneoBKx8rxKG2kGi7yCmzRyxMGGAT6pqBRAfKSNXlZ+LsSfmEn42vsB\nRXQ+57a8cK5L77NCr2YQTglpmFRO+7ENvPcTKYFx1zQWxVCpvbLI1EVWxUNd\nktMUAdt6YVIMigWlpG7iJfydc1mSgEcmO/HR8TEi/ZkRT7Pzbl+IDYUM/2eY\nNqWKf4Kw+jjVNQE+O0Hayu93gAX5GjbpBl22SKsRoAOvMg9EK7/1f4eakXBu\neVrfDbIO3vBKThLfrazS19nHMbUNg7hr/S+aNGHcsRZgHgOd5DAMe/23XVjU\ngUJE\r\n=svRP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_12.0.12_1554688337268_0.6261583934231194", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "797da61a576678d4a7247c12796a39175a72c67c12a6b2e34a47306ad6c42cdf"}, "13.0.1": {"name": "@types/yargs", "version": "13.0.1", "license": "MIT", "_id": "@types/yargs@13.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}], "dist": {"shasum": "a45b3c9faadd7adfdb40646d14a7f6b70f4e8233", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-13.0.1.tgz", "fileCount": 5, "integrity": "sha512-7X+FaCpRQ1scFQnWUY0TnVuuNaP5ajN45IGmwHVkAbsH1g4txz4hNb6sjJeR/wLaqUdNoInOwLk7oRL5M4zVFA==", "signatures": [{"sig": "MEQCICurZCm9Tsz6qY9leDqVYqJMh6vOTlIduQGhyHbsRF8nAiBxrg0/I9Rm2Z8HlGdIs6/FFuOeJupfj0da5xxqKSpzvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48840, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdP3eMCRA9TVsSAnZWagAAGMsQAJKq3M42YeybkSb0ojcI\niOnDeQBFDW8JnZKZpGyXfyr7a0hLveaFmwvxQSpMSHKy5UU4byntZl9dTY0Q\nNvhObXLXsFUlA4zvW1Tk0zUqn0nuLTPAxm9hob754WYLqpejrKu5XfdFoAcj\ncrgZz/3b07sIU5B4sBDYUEI3Qvxmr5KJ1fuPTRhzZDwydVRUcxl8zZ9O8UYI\nHu+I1S4rPR5R+imPGW+OBP9PnoClFTm9BE37Ou+IaS7mKhIks5tO65wQEMNA\ndji5pRhSCsFzty2F3A4fKF3xV+5nNLKzu/GedIF2ZSow9XHZs36UOQBNsiNn\njwOFfLr1vj4/nAPBAPuxyUi68NUNbUtYJ+41En6wW5rfE960khe6uSWgZky+\nYo0LqA3VZnCKHqPKvXPqoZQgK7s8Wanxzyh+G3Jl6WTopHqXsQLY3egQiuNh\nfSZt/cvmd1uaTVvccp/wXRaQ8zUus+DcEA3l8ZIdY4Gv38fqs1huYmgFaG3d\nUXLSGwXRYhgtFQKS0YUtMvb3+neguAkCYK+jxkGTU2REvQmSBcpX7pZR0jhe\nmaLcjiQJnN1ioKkhC6V/4ZjSTXjv8BykQ2257v5pQFdQue0G2CyLk3Co5CQC\n5yQlCBepHNfzcbF1tW6gu5J06QXh3/lXQLHTd562GQffxTqqJQgBlfmGXC9v\nnCUm\r\n=4RDP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_13.0.1_1564440460010_0.4098936053833053", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "2f6dd0326983ad385bbf2b8a48bcd2b45bca39690a244ce0220c7e4bc491c8e0"}, "13.0.2": {"name": "@types/yargs", "version": "13.0.2", "license": "MIT", "_id": "@types/yargs@13.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}], "dist": {"shasum": "a64674fc0149574ecd90ba746e932b5a5f7b3653", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-13.0.2.tgz", "fileCount": 5, "integrity": "sha512-lwwgizwk/bIIU+3ELORkyuOgDjCh7zuWDFqRtPPhhVgq9N1F7CvLNKg1TX4f2duwtKQ0p044Au9r1PLIXHrIzQ==", "signatures": [{"sig": "MEQCIENBdyngnM20AYVw50AvsX3lxKg/SRquue8FewBeeWgmAiAXoXQfNWZ2uPwNMRL+1WJ2efUbvNlXkjK0tgPf+CJm+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48864, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdQcxnCRA9TVsSAnZWagAAnX0QAIGvyi/QWzV/kuwulg06\nH9RKEGN8N13eMIdfwMJVnSOmNwEkweX5KcoHSLEZwT9LraldFb6NZJjC6Bvt\nN2iWdab1yiqop0T5isFLTOSB29DRzk5cKgCbvTygAzxI53Mi5ORsXl2aq3/V\nd5gaitx3wcoiO9kuUslK/JKX8E+5UwIggDPUFnBdabyYyVwy5YCQmCmtomfK\nnG3bhxOojNXwwLNU4zZ5C1SXHydwnjSdvLxnBeuk322l8XzfL8cw4eDmOB+f\npELcJF+ZUngwikm5R1/1KkGyFXgct9wCS+3Ixqxjnz05JBWHdlMogaDpXTNn\nkLAWbj0ICFFPU6p7sGJHDHQq6G9QT9NbXlEnlYkdAadMdPpbuVOKeEz42pat\nrQFJbOxki+cyNGZiVmJJeSuAKZYnZYoNbUVQBrSWRyUAFudb5atdyuRg/Kjx\nFm/yrFzoVs4BHARosAR3kc55vlqG46YzykL1gL96Q+Zy6Ytvfs5VeDAHnmQs\n1Rkb/F5TDA8H/iqbRGLoiijv7Koe2G9E3yRu+fs0YtsRs5rarlGuKD41wWOF\n07v2rWH27b5ihnXY9l17XA6OQI8pOX7UOABJFfOZM6QPP2Hk/GRtkBrbRSRu\n6RFYHtH3GdcblcGfYEHlm+S+6GqmowrSPFRsPJxXT3ceZGoHMe2YMtEJMWmE\nHXWB\r\n=3nuT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_13.0.2_1564593254438_0.6176418651380535", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "5a8204a9fcea5ff2f6fd239ea4db72637ab00c6e3c82ff1855070c7ecc25394e"}, "13.0.3": {"name": "@types/yargs", "version": "13.0.3", "license": "MIT", "_id": "@types/yargs@13.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "76482af3981d4412d65371a318f992d33464a380", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-13.0.3.tgz", "fileCount": 5, "integrity": "sha512-K8/LfZq2duW33XW/tFwEAfnZlqIfVsoyRB3kfXdPXYhl0nfM8mmh7GS0jg7WrX2Dgq/0Ha/pR1PaR+BvmWwjiQ==", "signatures": [{"sig": "MEYCIQD9EjuQjJXxKvAgrWCJTsyV2LdKldvG6LZc2eHDU/tyfQIhAOzw7KpBZJjKV20F5Um4LNqfwQVLOE1An0biXSfaNLpK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49359, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdi5GECRA9TVsSAnZWagAAeYoP/1Wwf5TyWFmsNUNts5nf\nSeijwgYB+x7rVGogXy3seyAW/F9CsHGjhoZUer/ruX0jbshXm69ifFRBA7+r\nspXN8Ilw+l1lVkn5TkVyaG0p6zL1iMRAlaXmUD2sdnQZO+yMMjbvaiTdufr7\n3JfnrsgScdDJViwbO7ZPZIzYLzpY4iBEN4e07F6ex5aPM4yAq5dGubJq+SxL\nHRQPHX/JnRe7oGHNKBjoaZbKFW90xKc39iG3P2GriqITiAZkIlh7ca9NOt/G\nNfNVjl6fcJRNHP7753JUGCtn4tRnIv/1NwTtO2VXmAnV79ChYzjzMqdZ7+56\nKVmWUWcfl6heOWIo3HcII+0nW5Ep1XStuWUXFwaeL4DQqzjBLclAG5+Bak7j\nCOTh9W/hK6YiLUBR2SEBiDIGGMQjk2HVEBLp2G3IocHaRVPSKN8+G+R14Do9\nalR3x4QDjQKtgY9x9QKpFNWxneMfSbeXctnmsTVehx8ZXYKAFjN2s1U0OEGN\n0Lir4eS7sdvOpjQK+3sctpWIxA64XY24+zcJ14RXrVB7JntltGahrnRbGQBp\nStQGVoy2b4Qscgr6Q6kWF7VcU0xBpGYlwkeufwInDUsqIoYksQq4NdJplrbS\nCXHfavkB4AhXdqm6Xa9yNXRA+vdlZ3/zQ/ahdiZ9oYehp3tySInQmympgNFu\nRv1i\r\n=o3f9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_13.0.3_1569427843456_0.648910804976947", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "500be969ef6ef1e24d5f2d9a3961407976a93ae7e549d23c155cf9d680f1cf8a"}, "12.0.13": {"name": "@types/yargs", "version": "12.0.13", "license": "MIT", "_id": "@types/yargs@12.0.13", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "fcc05fb3188f2daefbf2b7643dea5af4c4883163", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-12.0.13.tgz", "fileCount": 5, "integrity": "sha512-CXlavd8Q7ZQkB7sMpx9QKC/B7gUsjtftxMHNr7qGJaDiZZ+Qmhwe4Zt3aS9aXF7cn6BYQuFlKU1UlrebyKsh9g==", "signatures": [{"sig": "MEUCIEiG/mLQW8ladS1bw2le0vS6dLIy6nwydetQCzH5PWcQAiEA36bU9hZ3O/b+orXiFcSfjHLPa9rNnd9+nBV8Np8V4h4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26786, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdi5GTCRA9TVsSAnZWagAATm4P/0UGo2F4Yh2rOnS9Y7zM\nccUKmr+uSMVJ+Y/x8aY/NQ4CJ0LBA6erY/IMvgfPhJ61lQOSP4pTJIH4tHC7\nzz/rc7B0+WVo3Bxjt9vObPqaoePD3VdSplCrgRvS3J57R9G9MVTB4+4Jceii\nCI9SeS8y6soLCz1LnODy0Z4E1whMJPgAwKjHAriVjuw7OuK8uY835A0tH2aM\nlXeHtPJYZ2nycCijcGdWPpx3iftOBn6NyZsU/ldeML6FBKWEz1po+aWAnXL+\nBv/Xgjt/bWWgKY6VFdZ5HsvrQG8NcMkD1eGpq04s96bUxs63ug/+OLd+oMk4\npNGZWWY0j2HSEwC0oLLM/L5jmjmNv0H42lmC1wTbAZRKRJeTS0Tsc8Cjtvid\nCRXe4NQYaOGPrhy+mWoem40IcdmSwudwg/g5zqv0LVPYXPkoRQQOM9Tc9Iew\nHgTd2cbogWplPkL72eeYv00cdgBb2n/Sn8R4WmX+y2GZBSHxBJ1TlaUpRV04\nijXxFBxNuEAp5NWosKvzwZUbuRNKS+E+89i2oQWgVVZkSPF9MZRn6+wefLN0\nsKtwVSMaO3eIe/UWsIX7RCzTemfeEccYMXFYLF7zNa18VAGTR1+KISDC69tI\nKFOtXrVTXuG3mKzSInSK8xKK+vaBmajvvrT/clSln6qAukcfaBI9Z4yzzj70\nthcC\r\n=G+VR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_12.0.13_1569427859080_0.4176535649214572", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ea9855e5c1ba05db1ffd4d41938348cfcfe0c40fa527d651521a4ca7ff4b0946"}, "11.1.3": {"name": "@types/yargs", "version": "11.1.3", "license": "MIT", "_id": "@types/yargs@11.1.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "33c8ebf05f78f1edeb249c1cde1a42ae57f5664e", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-11.1.3.tgz", "fileCount": 4, "integrity": "sha512-moBUF6X8JsK5MbLZGP3vCfG/TVHZHsaePj3EimlLKp8+ESUjGjpXalxyn90a2L9fTM2ZGtW4swb6Am1DvVRNGA==", "signatures": [{"sig": "MEYCIQDk4qNojv5sJtNYaXlXxZ3c/Q1HxGK+LAYlLHj81qvUVAIhALXUY83jd3R0x/q0xM81HEQUtzrK8NGXdls5AmHtRa27", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16975, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdi5GgCRA9TVsSAnZWagAAEswP+wbp7mikvFWKEAr5lC0q\nrbExC7Shh4ck7kNBngfBGwpSldLHXH1D9y+whbjQtPkydMBudhZTTA8sZngj\nG3YEBdCyfDdjqR/U1s+jg06OZ5xYqrRgENX38OeNnc0o8aat6EJ5jAcH6c5i\nsrv07Y4HBr5JD7ggd5MbJjiShbaZA4mEUrJMN7bLdMrhAe0P9I6eRiDXrTbb\nRUtONAd3lpfdTGUTDHctejojupwnm3fY3GoHCqeh/ptFRUI0DaHHu22VDWwz\ny6alCeNnWP2LwcMhvZW+B7M8nRffVAT5W/N932m4vnXv6N3Q2l4U02WrgoZv\njYmqVMpLUL7c53sIL475gQll2PIsfDFNrIVoqbcyzmAkJ+L5SAPdmnbuYaRV\nqCRTb+2M4MS3QpKr9NB9vEQE8UACcRPtPmSQ+843ZnXye599ACncNbRJAAh6\nVQ0EnE9YlCAd/Z5JvG5p16cDAED3Rw35xYFpkSCPWtu7ZoMFp9+u9BWWux4M\nTbIk7dz3tyypPfalWmyYoIxYinQlYflB07wR2lLMyN50SgZG2HdwI/4/U0lp\n7jrFqcdLSWT4KW/tQNdz69K/fdyJtrffgKdoYaO96w3rsQIHqiGwoWNaWhEL\neBvlSg6zTT75ZhQ6PLsksYIlmQOTVpcNoQAH6SSJOyHlwh924g4Yq5MAX3qL\niID7\r\n=bq4L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_11.1.3_1569427872046_0.4885960923645234", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "21eb788e57195b9950f4a37f78b2f7f304c68626d81e6f650f49092935202ede"}, "10.0.3": {"name": "@types/yargs", "version": "10.0.3", "license": "MIT", "_id": "@types/yargs@10.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "ab96616695c92aa9a085381ba8aa009773a089c2", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-10.0.3.tgz", "fileCount": 4, "integrity": "sha512-pwG/x25SzWPwZz5zWB+WjvM+ffbK8wSCwmUA2KJ4HDI62vdIGpb9/In8Y+HmJpcFBRiFYy0OPxywxRWL6sRqOw==", "signatures": [{"sig": "MEUCIQCY/J0NvvqVYM+q0mmIXONI2cqijLJfsBBBUP4/ivcweAIgJyEdUUfEZb8d5IiPik4+QT1KRskinkBIZRwW+vVdCHk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdi5GvCRA9TVsSAnZWagAAfAEP/RpTlJuIY/YpQ/vyDfU0\naMpofUFW15jDKOXVQE31QmZc1c4nftFWrMP4aR3PanA9sorTaOtfKFX3Kxwc\njPmEHdOzvvmx5SWz5DNeUJVJvyCS9+cKvz1qCOGyQsLtEUp8sb5NZgaG+eTe\nupHpO3XTWvsNZrFFo4VbspbxyO4aEqPkdCYnBl/6kJ9rN7mJFl5VBnxwv0vw\nVi26o+DxDEj/QWqzXcGwqc3OvEuvQgrRBYL0cdBI+k3S010abcy5GmkfVY9M\nE+eLsgLpYb7S/L4hUtkqiaSn+rF6FskiyPjaNTB0I5S5+ktX6ucVGLN+n1jw\nC9v+qXAqjKBeeIry7qim5dU7PhroGwlHlzjj7uOXKlyZfq3VASutdwVBz2Of\ngDlvFK+WLtmJAy5ypowKeZNSMw/dEs4jmiuZXEArJIuWcluZDU2ZVLAmkS5V\n///Q5q+0hmhf3WOfgtjAyWxNTJDsVK0N0/nKIElbVHYKix0zHX8c5juQR7nj\nzCfocwnWlO3ePRMzG7iuPcvPr4q53bafJL3fu7D5Fw1EOAmRJs5e72VZNPK6\noVAKXv7O/oxt8Z1piTC/OYNxlgMRYA4J+KSssNUmtQNt/LU176b/+NhL+gcK\nRckIgG0GlqUw4bfbfsiWkd2f5xtTIjEjmMPMhjANcuBPiutWsnTghYcXgUZE\nm4/F\r\n=G+1r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_10.0.3_1569427886727_0.1298180403348257", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ce6b9b48cdbeffebb15c0f4e6d23f6a5a0593b7c87635c445757fb9335cdacd6"}, "13.0.4": {"name": "@types/yargs", "version": "13.0.4", "license": "MIT", "_id": "@types/yargs@13.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "53d231cebe1a540e7e13727fc1f0d13ad4a9ba3b", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-13.0.4.tgz", "fileCount": 5, "integrity": "sha512-Ke1WmBbIkVM8bpvsNEcGgQM70XcEh/nbpxQhW7FhrsbCsXSY9BmLB1+LHtD7r9zrsOcFlLiF+a/UeJsdfw3C5A==", "signatures": [{"sig": "MEUCIQDKT9WEnzE8utuLj1grJaM57ARK4sOFvsteZTWAOTShmwIgdMg+kFFNIsMBVI2/KB0zhvOm6cyIJNJRfsnlYWXW8nw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49410, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeAPLqCRA9TVsSAnZWagAAB3EP/2608wr+4RqN+uLukCxC\nLCuLLSsarIMSkm1ZUy9mHARmsmYyC0RYvoaHl3XNXu4CyzFA6Z1PhVbBAD7M\nz7iO+ty2EX8Xp44uFMqnAWH1pr4mTZXeOSuJXX42csvsCd5XbDDndtJ92RW1\naRSmjVCbxC7U1bhMDcePmwo3KXkQ32Uith3oUW1fBKYtk8EG52XJ2xCHP7ac\nx7stru05/zht/lt3qrxhj1twbd3WHm0A/D2ApOveqGlJgSGPkokCeSjU5poG\nj+U9wUTwVHQfApZYAhQou2eXghWsv1Ji2bBOvOy+t+BXchMBdtXJ/ThI4wRQ\n35gFTUNkLX8lFRp+aNOc8ikBjaJBiTyHddMGOKzyd+2cpE3JvwMRbfJrRmHq\nIwxg45w9pOQyY5WwXEgfy/bx/6NlYg4PxbFlC2Lo3U+H1qc/XM9t1yQoTj0X\n6ImFw3kqN2hFfNBeRfIa5KVCXFsZ6WG2oE4wCxW4jKdiEKQ67XNKPin+fGUt\nFhudaPLhd4nVQd607jarFdF92dEjmSXte3XubBxjYDr7RU0VgyePHJuQ2lnF\nsNru4OOaeE6m8Q6I/Auv/U30hW5pzso6Y0sAnqD6H+NQ/3y3vu9n6Ow0dqSs\n9mqhcGABuxxLmvIsThlyyZilvAXU++AZYpU0FAjxGKHwJdaZj9vPh/paGH9E\nPzZP\r\n=Izuu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_13.0.4_1577120490115_0.34260418979549856", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "37222ee5a663b0260061ab31ce6e21896839ea44a19eedd06a1d4856a40c5797"}, "12.0.14": {"name": "@types/yargs", "version": "12.0.14", "license": "MIT", "_id": "@types/yargs@12.0.14", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "917b57cb50c7421842a38de24fec10a55673b7f0", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-12.0.14.tgz", "fileCount": 5, "integrity": "sha512-AoVBCxg1PQpEE4HUnHpagMFrGzogywNJElZVg+aFmvOYUiz7y74CKS9/8G9J8hrYM4xu+rJR69ulmoTMKgWSWQ==", "signatures": [{"sig": "MEYCIQCfBGOCbwE6J37oPidQRBkUEE49yTPw6cnCwSIPoeVyPQIhAKS7sH8SmV9k1lq2OXKi8Oys+h0BhPLWU2sypeb8PtAg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26788, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeAPL/CRA9TVsSAnZWagAAU9kP/3N4Yif+m8qFu8ZoNRAs\nMrOBGzSUmpR/oPDSR4eG0qn3eFArzLk2S1eIW31nMnWvJy4x/7zo+IQJc52J\nMl0aoxOlXsjD075yXpvCmf2nmZL240zxwmuAw4T+USpGrj7EsuGny/Ll/DsW\nin/Kb8qqEA1AtcGekzgPFinW+mgIlSDJJM0Om2k10rkVmDJKTDkjaGlRcpeA\ngvcThkKonMZol0qqwgqIbhVkeC8Iwd28bbGD1SIl2GO4dpOBKLvDPsTTPtID\nB2CyBOPBUgyMHS8P71sMTxUAnEsV3JEuaM0VtuiK2LoMDCg8O20MHX0dFIu1\nGpjUHtZLjG3CQATMonrBCzsp1Hx3Fvc+/pMOyIlw8K1Cb0BEY/ce3AdRkbgD\nKh+G2tzUVs/ZNDaLEm/SRB0CJrdXKWeJKpDmBtkxunE2ZHsPNcZwE0LmmQ5y\nv/N1eyEUIYMdNKYt++tGoc4cWPjZ+EWkXnQmv6a2Qlcb1Bj46GtoWnXEv/F1\n59lZsdYTOw3d5q3KTNTTXjd4w9ryuE0K1dTei70yj6iwiTjwu8BmY3p2wd5C\nVNXUA7rDCJ4867D9TboJViuf21G4nQH1oCrdlTRbFQXJ2Wj3L62Ni1omUfVi\nMCJQxCbqoJKIS9RJjuMjADs6flPE0xMaMaOFs5Otfqfjawlzb3Pe4MY6EQkb\ntA9j\r\n=wBxC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_12.0.14_1577120511155_0.9688818356475128", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "bf5d61c9b6478a51a2a5707302f5beea5368ae68197626f0216bc732fd68a1b7"}, "15.0.0": {"name": "@types/yargs", "version": "15.0.0", "license": "MIT", "_id": "@types/yargs@15.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "26aa57277eec9ab9c5fce61bddec20ecc9a4cfb7", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.0.tgz", "fileCount": 5, "integrity": "sha512-TITCsNxRSDVmq3kPGuUdKZTPTfHeswsUGIjxSe8SB4EBKTPA0DO0y4yWI95kZ2hfqJAYxmu+gxzjOwdumB5S0g==", "signatures": [{"sig": "MEUCIC5MWPmiA8lQyccS/5rkDLgLwzWG4K+LCLKEJrKE1C4gAiEAgw/a4a1gpUIy0LJWVhbrCGs9Sydd2Ve0JPtIeswCNf8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49655, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeFjEACRA9TVsSAnZWagAAi6YP/2f2IcbjfGU8p4Mt8QGA\nUWTJpMwiTohlEcrzFwfkCsJeT+g7JOvuqwauzr07VPcdjoK4SiYVZls7xBxs\nde5WmYvQKXC3eifE5+2V4HL6V7+cowqPyvXQhHV5jg8KlpykvP6yERFwNj8q\nd9N/AfcSxKjVSpgN/6hna+qXU5/+U0ssvB6b2osI8sxYG2SG0jkp+++2koxF\nvG0ktGHn3yArjlwftsrv8y3RjhfA9NvcC1lGxDBLDHpLIG9LsG6m0PIeDBqB\nqJxC4UVUCw9I96zWQfZo3wX/83o0wwMYmEfpfAZcJMrcI/xGqzrZLPz8R8WF\nYFGHu24zTj9l0Ymoww5+fxn+84AsGTMGPG0JobYRw1tbz4ez+wZOkaB/FYT+\nfbks8UkDu1IrhA9ABQKHHWbz5kPIS3JJPZuf5hmwBIsZjDAFD1S04xy6HM9h\nZXwjusgHWpry4Yqq0vW+WyfnPPnQqwRYsfpZtyVCqrH/HzxRCVeG7zmal/YU\nX/KxcsYPIvGFTXWvuc96qwBGQP8sx4w43BPjvtGizGMAiy3B1tpRcPFqNgZb\nELihu5fnLTJKNiCMhkPgnwUu3iLoHE+EEInMik/49Hmd7NlPnCzjuZFZUTKt\nVfJadh/UF4C3wodHdxTVzyBKAb0BPsEjJ6RRcJU/8IQz411p+zHewUPD/Zjg\n9w2z\r\n=iNki\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_15.0.0_1578512639972_0.9870982408844897", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "4fb8536f97078e909cdef4dc12a6f8d208218b2c02ada6d2898208d1ee677f94"}, "13.0.5": {"name": "@types/yargs", "version": "13.0.5", "license": "MIT", "_id": "@types/yargs@13.0.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "18121bfd39dc12f280cee58f92c5b21d32041908", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-13.0.5.tgz", "fileCount": 5, "integrity": "sha512-CF/+sxTO7FOwbIRL4wMv0ZYLCRfMid2HQpzDRyViH7kSpfoAFiMdGqKIxb1PxWfjtQXQhnQuD33lvRHNwr809Q==", "signatures": [{"sig": "MEQCIFPYDyBVC0MVoekqk/TODepAUGJRj3mlLmDBV0eqvBfSAiAl/fI6fnh5xDA7+SmKcPYCMhntQQc9gKq1LyZPw3JuGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeFjEVCRA9TVsSAnZWagAA1c0P/jcQdU0vwIxPxAP7tfgB\nbKY3geRuHqId9cLYDXLkej8/tkCP4KnFyArEAmsPtfuy21g9Yj7G6vZpdF4W\nEr2jg70acfc8QfoEPiXlgvjJ+CgUE7b/MvwXIVsM0CVhlgEK5TDpuzgQEEdg\nz9q2sJXk8oAND0mlCWu8doTykfBPlf+6OCuNRMIMtWPqVM19bkarU/WqCFsL\nh9TEQe63CI1JoCr/V1W/pjxY+KVZ8Px063hgD0NOyZknuc2tQzyLulVcYeGi\nxqcP23HCRHpPqXtcReUqGUc3VVKMPpeDiGO2SlE7O/gehnDmMKwKaVFFTclQ\nIeDfjElee9XsHYtWT2HpB6MCUALDfYerueDDoEwnZC6wIrsdLP46pXvTs+0B\n5fAWqeJsQep1XniFMx85qtGtK6bgYSfzVbqcPVlWfzgHy8t7qwtk40Hobft9\nwP3lAR7rBE4tVcT/BBtI10Sxm5Z+wWZjjvWY+L+Ps1dleo6A2wIWjkjFvUiN\nHg6pILVX4lT809RdtRONh1KPHlxeFF0UFhmrRr5zGIBAr4xVUlvarGpyEVb1\nlLucHNrDLZlkkf7ii7AJH7QQ4NVFNf5dqwgAK9v6DSciHCR0oVeRmGc6fMx9\nFZ7QQcy8QXPbYkRjAP2zH1BvMTR0JMU2cXIqgtbB9Ibq+I4ma1hAdx+/8wHT\n1VBc\r\n=z3G6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_13.0.5_1578512660884_0.03554400143619252", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7d64b1281d978136905c144b44bcb97416b072c0f8bf9c5265604ccf51bc6b6f"}, "15.0.1": {"name": "@types/yargs", "version": "15.0.1", "license": "MIT", "_id": "@types/yargs@15.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "9266a9d7be68cfcc982568211085a49a277f7c96", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.1.tgz", "fileCount": 5, "integrity": "sha512-sYlwNU7zYi6eZbMzFvG6eHD7VsEvFdoDtlD7eI1JTg7YNnuguzmiGsc6MPSq5l8n+h21AsNof0je+9sgOe4+dg==", "signatures": [{"sig": "MEUCIQDjTawWpgZDHFjSBryurpe6jhVTfPmr5HPQ+JQGik4g+wIgalKU5ViZ4paef65j6vtjDFJn5oAM7nc/Y8enTnAuzDg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ49XCRA9TVsSAnZWagAAlYcQAJarKq0HhbZ1Q1iklql5\n3ZpRavKE2A4OYcN6MnGCEGR7GaOMsHSNFBAwWwvCFLv4J+vbXVlLQPeEpXUk\nxkEDEdr5f41mlhRptBnCzDadMNmPFxzxVD6X3DfuPF4LfL5VfzRnoDsa9Jm6\niBcMyrPAjAfZFB2yQri66RWmTu0OxOELASjk/5uX5JEsXAiUhHTBRLQPQIJ/\nhOn87UtYmlOByTlrPoNrln8G37igZ5hJe52gHjlj+/oM2yOZuS8kLLlgWUvh\nHYU3g3/DLwYykkQXpFZ9llBt450s6x3+zj+hYRFY+sVnxVuOAfgPR0UmtQD/\n+ujVgsRRncmK6D5KcmHHaWaDrjsWnw6tfmm67/qBOf9zyNXtt44iHQmUeKdQ\nCDyYCj+zRQsE6NXG3SS3oEFY6NcCMSR8S0+9UU804pVIqA51n2vjxdAlakQc\nTyaKfbiPQm4rlbMgSPQ5GGSZMNlVu5Cxx3ZrEgUUWdwcpx4McopruOKWVDp1\nu8VJpjhzmff+0ygJ7raJ9zWZ/Uj69FFaqoz0xRN/WUJwytsm+vBp9WcXmW7e\nCxaWdorl2SeWu9PHQMkXNS2/iEVspifWdeRIOivfpnodnVHCWv14456TH+PS\n/9iKdPRjmVVurqslBDce+CJr+48lZQemODDRxhC22NHRrdCmYS1JC1mDBhdI\nTjS2\r\n=SQtp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_15.0.1_1579650903190_0.16762673508860626", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "47def568d5c4e745a8fc7c19bff9f6398d27fbc7649331667a3029388cf016e1"}, "13.0.6": {"name": "@types/yargs", "version": "13.0.6", "license": "MIT", "_id": "@types/yargs@13.0.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "6aed913a92c262c13b94d4bca8043237de202124", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-13.0.6.tgz", "fileCount": 5, "integrity": "sha512-IkltIncDQWv6fcAvnHtJ6KtkmY/vtR3bViOaCzpj/A3yNhlfZAgxNe6AEQD1cQrkYD+YsKVo08DSxvNKEsD7BA==", "signatures": [{"sig": "MEUCIATN+ORJ0PyWL2R1CWI7H97wzITcUz/15ma6JK30h8xCAiEA9Awy16OyM2zgTaXAz+5L0xn6PCfEM2/xnPkhQtEDlhU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49430, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ49vCRA9TVsSAnZWagAAFlkP/A4xjV2FGpJRLci+UBin\nbeij1UlXWQZkG8QKJfx4r38WsHAvMJqSn2Xu0Za/YCaEQlvKBJjx0khCAwJV\nCtcLCwUrgsLAhbcUhLiBDiEo3/a2BIA8d80Xgw7MwSeqXh1n9Rivb8d5AAz0\nz40YBml8ftRgHLGBlRP9wilNlWG41dy+cwFsDBmvWnlJrsMYoYY8MEhsJEqb\ndPZIPaBShHA1MYEG+GsK+mVSiCCMMS2gICe7+W0nLykY7dHkeQ+MCAAUErpz\nghRVIX5WlEVdoke0/FGaLhs+BwHfcP50RhPDZe9aeyGbc+ZaghTAEiCJ9n4O\nuiqzmhqT0W87d++7yO9USzbINADBdVyHZtsyaFu2IfT4ky3Q/kYIRkraqNde\nbVC+edKtdPjK+upF5K4X/aZPfs4Uibn+9gcOXIr6oNrr7LotUroKkUKcvWlH\n4o2LHDXO/YE92+BhFT10Y+p7lWTTTzQ0NElENVPzzgAN/psY5AommvRcjxEt\n6noDzUOVX3RwUfwrwiJix1uOj6f7K+ndMfsdQfOel6Gc9dexxRqkpIBW+LAy\nX36f69AwkICjap+rNXn9A9qAc1aLAHREbXcIXF/5J4Iwt0QE2JVV2w7ych2V\nV1sJXJl1vjLNxueSkFuzbZl5LmmEkGwHNIPgf1D19r94SlG1mpHbJ1Zfqkf6\nmwKs\r\n=x0ep\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_13.0.6_1579650927281_0.5960471927997268", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "6277e48cf40a9a309249fe48f8ed71f5023b487808711d986f5b581ae561d44d"}, "12.0.15": {"name": "@types/yargs", "version": "12.0.15", "license": "MIT", "_id": "@types/yargs@12.0.15", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "581eaf3731649dde3baae7da3a173217af77a606", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-12.0.15.tgz", "fileCount": 5, "integrity": "sha512-stWBBTD1IX7iMc/L38Azs0P5vHl6AcuiV8fuuXXgQVLmlXJ7igykYuroMHgpGrD1vwrk56yEBD6dIM5or1Robw==", "signatures": [{"sig": "MEQCIECYrlxSZiQinuK+3Eq77DuWzMOr834BfagwWUo428rzAiB2bE1y8QJGG3YUnipYdGh4Pobcx+cUarxU04fqWp8ukA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26804, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ49/CRA9TVsSAnZWagAACcwP/jZL2qDWn8cD3dLm6okK\npIU5REYiE8ZRcXQAL0i/SfNSeiE66LIIAWIRZENCGaSiCOjQ6K21M2Tj0chW\nMnM9a1HP46DBxb7ooJq6a5NZ5AGw5PPSJhcm0k+yKGjxSra298FwVLafNuHd\no5dSl1k5frewZpOvFvnSNjkheIFZ4ujCHWJSjeA7Geelp4RnDUFwm33R8vYa\ngoCN+ftjSlzzdROp5ZYyhKhhIR3V3s3kxFvTqW8Q6DjzthwkAd3bYYQolnsN\nzB/aD8uRbvx7SyzYR5qvt91fhdhV+L1nC3/8LrlJLl/atTWXypE+IEJ48a0K\nyokIK+6UBTYYvWOIo9DiP7Fbf0Qy2wnIwMnFcGG9EorsdbCJEB7GQZP2i3Wl\nHseC8qGRAHPNfA8En6QWeoVbAfvbQyxn3LmDVCJ4xhUfUFU2RAQimb17ArBe\n9zdROnQwWDcVQ7Lkdx2qLvWuKlWCOtKcAoMcir7HI3nKGTo6x+y7YZv9fGJK\nlR6aAyz7nfs/7LeuiAjdjF+6x4HgnuT9E4W2vzTBeN9Jow3yMICQHDJu5j/J\n9LmhVYIVXeocZB0D1Rm0zj25q6ziAnOUytIYy4yas8izTOCStWAsYxjdMxUW\nwbmPKb37BxtOceE8Hr6avrEFc41YflJEd1TWYpYnW/ZP3QyaOhk/4qy0niWX\nQL5P\r\n=kmnr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_12.0.15_1579650942681_0.06335905060501301", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0ba19dd198ad8010ab4fdac4173d169293e3ef7f5c61b012c00115a17758af0a"}, "11.1.4": {"name": "@types/yargs", "version": "11.1.4", "license": "MIT", "_id": "@types/yargs@11.1.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "695d875cfd8010dfd9e292903bc3adf87e2cf514", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-11.1.4.tgz", "fileCount": 4, "integrity": "sha512-dZKaZg9KW6XSTg73rbPR6ClfF623VCTxQ+HFc4IPQ+OJB+AmaPnYR/nObnQ8fXW9oziXlOiX+Z+24u6X/Peq7Q==", "signatures": [{"sig": "MEQCIAL//WS4rQDiMwGOiQWXKFQci7xl/NVrg5Cjw6ESzlkuAiA9GzR4HlD0hcBnqad1CCmERk6MDxPbiLJus7HWopjupA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ4+OCRA9TVsSAnZWagAAboUP/03tlwS8G8PxFHKRA2oZ\nsb+zgIP0oQ5l+/zlbpFtDtx3Wn5ZIdBfxuJ6ybyFOBGACT8ViOYFnWjDFBsf\nPBzpPq9onKgQ3ix+S+55fCl7kCOg2YUt0fB8Wn9eszI2obgHe1+uU14bZyAf\nrDLvMMPYIMz0bqZiGRcTuTS6s+1Fj5LgYoXU8RypuxrpwW0IAfmjhfddKVUY\nEhaVXHTBjr3iyzPbvR3ovLVIgfn3VhpT3/kRZ8bg094VN7vLauySIf6wPqdP\nCCaskIMGm4Nl/4Q0QAYlhF+HpBRaxaMdhjknIJfO1C1tFdazX37DY0yQaQvf\nSazK7aPVcrEcJkspmj2o++q+9dq84X0DgqRZZK0h0INL55MrevkuApFST+dE\noR1Pz53Twi3dlomcjUhUszVBbxKNAowrLrfNGIO45k0ENoVDWm/y/jId1/Pc\nDdhIPwZWSWRYf1STWYJSLB234PzaFKPXpQx6g+lrgTce0bo8eUNDqHhbtRrM\nrqqAGpLEVij0afc6lIrp9U9nmkz38ZcjF2M/iO9o34VxTjKr3Zs0WlwH3KVC\nRa/G1UqkHFJLy3kdlvqmvDVDSXcCAJp0oZZhyoFBR3wOCCuSj3QChFR1Q8k1\nu4ijbP+KeQxpDaXO5JJSr0+ElkwhJdepjbJ2CVe5IS6mpJaUkX1hDNy4+VzF\nnvBm\r\n=h902\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/yargs_11.1.4_1579650957805_0.07234622670803503", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "6b9976d70813a6503538ce2eb47c68ca3269d44c9633a4c19f0c0b64b787a41e"}, "10.0.4": {"name": "@types/yargs", "version": "10.0.4", "license": "MIT", "_id": "@types/yargs@10.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "35e62f3d7a989340e8f5f1341effdef3ca451084", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-10.0.4.tgz", "fileCount": 4, "integrity": "sha512-uOdjOx0Pxr2vSHMdhJDw4aOUXLdbofKIzu0whGjrlQ/2itzdjWBuutXGFLQFXKQHIHWi6bAytl5DUlP5VcbcxA==", "signatures": [{"sig": "MEUCIQC6Y5aQfkxApRo56ZoVJPbcYfYfM0wZQoTSGC7i4uHRIgIgZpwtvloN7oTnx6SDKCmLNuTJqqIjd5ZdFIkVCAZJOiU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ4+eCRA9TVsSAnZWagAAER0P/1RnYZn9S+USATrDpMx8\nwzdYsghWFNb/8ghhdMlB27IuEO4oSx47Pw5WiECaMuFu4URGd4lxy4/AefZN\ntmFPWhlhYrudI1NZlxJJo/oJwzlDTk4QY0XfTZ8ewqsFOmHX7FSpIzhCuCre\nm1EXAQMGUIC2hibxYLCJU7Yv/zIFACfukCrCwOBCsWs99OLgPKDpRvDXyYa8\nsdGWvxM/9i+gFGlPMPXMRBHvUxt/b8MGAr95SyYVOx+e58jqu9ZTfPSBFKe1\nn5tEfA9hLjNl1XchMUq0Je6JJKzIruE9AnjNO4zV8F+0SoQvJy3KmSykAzEZ\nn0vShBLV6jLYI4Dx1Sjrf/ddsSLUcH9tQnYvi8g6S5PdUII3BZ7dqR9fbW61\nVrp1s7MENvu2j9aOtFPxx3+1inW5xUa0lMtG9y7Yd+mbVu762RsqDUODJwDM\nJHu00ys3Yuy89Eom3VY/YuxrCnX98FoTYhlEfoHA4iPsbkuDjW4u9grIbVlk\n6ooU7EXcGFII1fUlMflANHm4gMagwp3eU5uwdAhTs5S3hpkqDm0C9CDIkB0z\nU9NT6VPmwOinWHFe/ymAbBRwfwj1lhVDamCN8EJ8L1wemYGD/u5/uGT76fS2\ntbBRNyptRNsnT5nfczTbBR+34pw9cnd3Dh66SXTxr7KsSOFS9zFVE1CJKVtO\nr0FR\r\n=HoZm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/yargs_10.0.4_1579650974397_0.939549243028128", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "bbf28196acd1af579ab830dccd8883f724dc84ee64519e2756028328a988ae21"}, "8.0.4": {"name": "@types/yargs", "version": "8.0.4", "license": "MIT", "_id": "@types/yargs@8.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "9665504f5bcdb3c37d7babacd038954692717684", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-8.0.4.tgz", "fileCount": 4, "integrity": "sha512-n2UqbleYF93mEGsaYuE66TDiJq18+8YAvITNonNb1q/ZllzqfvCl4CaE2qcXow5g0gai1p7B/FqiVtgNTrbE3A==", "signatures": [{"sig": "MEYCIQDH4Iey6p7utTAIeydLEsHOw3go2rztLQ7LXvN4VJbevwIhAJIfP2VuoMUNleIVPn4wfwB4DssgthfMDSaXaZvscqO9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12988, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ4+uCRA9TVsSAnZWagAA4yMQAICdEh9Mu5E6bfe5Glme\nI4TzznLBAMUPGB2NTbgeatdg5GcSyN1ppi4qmFu2zPSj19Qc7O6iXuC99fuV\nXTK8aDhM8lzBtm5ZofcWHs3o2vMOfFKrz0lgQHa4wXhu4XCloXtO5nmoKvCN\n5yk0jVnFcTK/gF1+Az8rNpKAsz04LQIEoTZgBiwST+JhQZ5mrAxR5T1uQjc6\n5vlSBbBIbYBESnw9fKxaDJqyFb/7lTcIJebkFocOP6mXcDgo4PeztgfYclJY\nC7bZJyDjmlr5QxBC+2BcWZCTXPIkPfCidH+RLebNCxbjHDuPyDZOs3FRNFqM\nC+WTqyj2c8mg1Ea3RVO21lWiGZevhCeVmjweQ0fcWPO96XVr5twveXoBEvUC\nuTWXeFx0xpuonFp+ORI9Rj061rjozKGIZpJo6F5d4APSjsgn42MlE5bWonsD\nx+lDgN2A55HoZLH4KEhfv7Vs8rET+ut87jh3/4GCzG5p8WySy4SV9NQIfsX3\nM4Ny5+uxLphuZHEcp0WM+b0fg/HXCrHCF0T8NYNxSlpGggXvNinYTsqsu1yq\npbSeI8cKvO59T1wjoRVgP4nCxdQxjc8GmvAcr0Q8EZd69Pdn1JkXznPt4CaD\nj+7sQade5XjkmjxM+zJCmUYQT8qNN7UcorgOHivKyojxBAv8Z3HAc6BrB/Al\n6vjE\r\n=f3T/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/yargs_8.0.4_1579650990358_0.44948852401139083", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3665807cdb22891a3fb47188b15e80a02ecac52eb363274cfd637acb9726b60f"}, "15.0.2": {"name": "@types/yargs", "version": "15.0.2", "license": "MIT", "_id": "@types/yargs@15.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "0bf292a0369493cee030e2e4f4ff84f5982b028d", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.2.tgz", "fileCount": 5, "integrity": "sha512-hFkuAp58M2xOc1QgJhkFrLMnqa8KWTFRTnzrI1zlEcOfg3DZ0eH3aPAo/N6QlVVu8E4KS4xD1jtEG3rdQYFmIg==", "signatures": [{"sig": "MEYCIQD301viSmPWlWmbQNMMGUumw7bHTGVzCIjkGc8VOTgcbQIhALiVkBt/d+kzoNG9ClZrCd9qy/chClA0ZTUY2FOj1tht", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeLzraCRA9TVsSAnZWagAAV/EP/2x5b/UEHLJbcxFvto1H\neRlH1JkBWEBgzXHY2rDrMzfg5R+0FcejLiFXroHWxc6kR19u3gKSP1GqMP19\nxdU99e3E60EObJA/Ankx4SXj0YPCXa/EcTr7hjHux3dXillQDjRqsCB0pMvt\ns/zPwWdiGY6kMhqeY9BbUAz93eUdaNG3D1S9iIe+Sj8baLKUlcSowDi81S5H\ny9jmj4o1X+9urILyP+NnSkaB3ziNivlogGFEMcINvjdDgaGhOlXkFC9zQGql\n6HNxhbJSAT+zNgxzQs8fNHCoq0BVpK/ojEzr7JyE8UX64yx7X7Ikw0WhIqlb\nSEmoRzaLWEMZv9FIqHO7sHtFGixAQtTshAdQ5XmCDRy2M80vuvQlQ6n+cuUF\ngW77eAeNBr6mGf5az9cnOad2Tx21lgivlT7oF3xXtcLZf5MoTLxNJs8j6OZ0\ncM8tWT3bva/ISjY21xUz7NYFm+YwwZWwXfydteHFpsCIKYAvJVebqnNyqq0D\nY8PmKIbKr8l4zrPTRVO/EyKJs6VDFHjiap9nOKaiHwRD3aARgl+ZJBIHIP7a\n2yjxJKjF0E3zHkxHI9GZSzyumUnx/OdtpWVwclhLAJMm1fS0sYOtTMlzkBJ1\n39qKLAOI1/IHv2b+Zdek4WWeX9fF/XKdzWXIIZXsLt6WZzbEW96rs1hWS5xy\nC9t+\r\n=dkxZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_15.0.2_1580153562414_0.5039750755443047", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "021b0b3397b0113bd7b139dc012200aa396c815e370bf1fc8ce853cdd3060fae"}, "13.0.7": {"name": "@types/yargs", "version": "13.0.7", "license": "MIT", "_id": "@types/yargs@13.0.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "658d8578a444670a41cc9c338d5e0e0a9910fd9e", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-13.0.7.tgz", "fileCount": 5, "integrity": "sha512-Sg9kNeJz+V+W+0fugcVhHC+mNHnydDR1RJrW5Qn2jVrDQARF8wfPVqIqwEzZp+bneuEBIm2ClsJ1/je42ZBzSg==", "signatures": [{"sig": "MEUCIQCEsbK6JceEF/Xt1Iw5Z065I93rlz+sdi5nmb6dJgImqwIgaA5ke/skLGgS1XCmEwhUWBx/WvS18pb3bdZvmtBCqdE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeLzr0CRA9TVsSAnZWagAATMgQAKL0A2wOUbz0vYqSGlmU\ng66sy060/JRI5euZC+SlDGd33KHFXDzeA98H725GHj4/k2sipNn/hE6h29Wy\nEPXqtPAO/T11JLXpOr0VM3ZaWSdojX3IPR40liXsXHkyoOQaiHGHrVDMDoK3\nsFLhDyCaRQO/Li8bWz0+N14g0ua5/69at1cHq1YLgNMQd1xUGi/6QiDqaCfp\npOE4SBuqKNLHgQJ8tGKNI5Z/6GXIlM8Ve2ZN1INbadT51h3IunA929rq3aV7\nh/S/EIW1a5026JN/qvUJDSr89tR1yoxerr3KbwqkO/H8p0nZORA+RnA5RPiG\nBRq2AD1j5nCnGn/qrq3ofhEPKO9DKNl1KgXotIDkrXmStSAUUp4Mbm4l5oKD\nYejMF0ivKAX9TO97x/O/xOcvlu3jEahvf+FY6n0CqBNM23h1i05szjS4QmDB\n96C5m5oMio8Frfa2T5IoOR1DNPiwBUm8ys7BUd178ZGhFWCgYzLYjXCflO9O\nZwtLdlq8DKY+HL/Yq3X2uayDXfKuxx4cw41YbuiRwyZ0JHbUX1BiwoQF0V90\nXoiaFD/M68sQspTbM8fJ7lEU7O1/5QHluRD5bMZA9w156Xdmz/3g7PtNhHh/\nTPZxxBeTuHS2uoF2gBrYZYF4fEkfD+UDlIULkaLTekV/CAtTYzLLjrc3/mFD\nyLnI\r\n=3VWS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_13.0.7_1580153587741_0.9103381657890242", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "6c911ce327cbccdeef5d3dd2b03e53cb4e431f8b053d826ed844864cccdc9cc1"}, "15.0.3": {"name": "@types/yargs", "version": "15.0.3", "license": "MIT", "_id": "@types/yargs@15.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "41453a0bc7ab393e995d1f5451455638edbd2baf", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.3.tgz", "fileCount": 5, "integrity": "sha512-XCMQRK6kfpNBixHLyHUsGmXrpEmFFxzMrcnSXFMziHd8CoNJo8l16FkHyQq4x+xbM7E2XL83/O78OD8u+iZTdQ==", "signatures": [{"sig": "MEUCIQDJy9JSIrWAx0cuvtfIM/+7HMpVqTHvWMeeaaM2AYHPRwIgWHVs44KWqA11FbXXscq8HIiheFoZpoIptmePe5EFN7E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50092, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNLsUCRA9TVsSAnZWagAA6/wP/i5Wt+WB/nxaxb2aU7fu\n38BkgssFyKD+6vdbFjSGOVw1KxHzLLF6kzAhTBrSsWJxi5q51XcsJ3gGSrZm\nMIK/dSBCONHjNVt81+Bsxq8tTYClT+k9+dVy5iceZjNgDPJjYJ93CF062k4M\nG4G1TY0I3eE4QnVtIO4BpGzWDJcsMw44vjgGIwkD+PZXL/yAZ7CYJwLRxRGQ\ngK/6jC70Fqxz+HwkMMZmRJa+tYRvvb6/jgqzle59naEEy+1fRFZnVxVKF66V\nBuH//xntcqElOJFVzpl5DmiJXktz8rMX3nPIy1pwdbsM5hHzbP4TmIdp89+m\nHnTYlUY3CqvpB9CtPgRpBSXzduw8TYD/p8NhEpyIQg/mpSo+5QDuEC28fn0M\ny9X0AE7DH3gbiRQ73iwBf6KrRNDBD3ZfscBhlpQ0/O8klV8AV7dkAoknrGHJ\nH102Ris4v6fgczwDCpUAdcyS2mKCLOAb4XjGSg9tPAFJJY4stZ0rIJfId+rO\nkmujTOEex/bWcwYLEEg6JBhqHjl5uExilYeRGaLCkMkVNLud5hqNVZaZUGL7\n3otVbggIteYuZFYt/bZi0MNA7wMCv9zzyA+ZadpP4Au2XTD325YECTft5n2e\nl7ggjcrvQzUX8RHdZyh013olaORPx9LmeVbLcClcOxQ/yzpQuPGXkBjwsnaH\n2Ghc\r\n=633z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_15.0.3_1580514068445_0.1935555601722856", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ec77619dcd9e47142c40489fcc9caca6c0fd158e40ee327d9bd20dda2c4edad5"}, "13.0.8": {"name": "@types/yargs", "version": "13.0.8", "license": "MIT", "_id": "@types/yargs@13.0.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "a38c22def2f1c2068f8971acb3ea734eb3c64a99", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-13.0.8.tgz", "fileCount": 5, "integrity": "sha512-XAvHLwG7UQ+8M4caKIH0ZozIOYay5fQkAgyIXegXT9jPtdIGdhga+sUEdAr1CiG46aB+c64xQEYyEzlwWVTNzA==", "signatures": [{"sig": "MEQCIDbk5j5J6jSUClOxgoI9W1tn7BVfgFCXMeDcHjSafMZ/AiBcdD/jju6i8U+yuqkNuplmG3nUbqDDhcb5vJt+fjb7ag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNLsnCRA9TVsSAnZWagAAVU0QAIU4+PV475oKtIQeteZ9\n2tgQd+nPv1ZOaBHEbpiAoZMWG/G+GylToXDAJobtvyUIh1IDptmNUztkcIdC\nGxxhT/jR46d3p1wvcVxVvZJvNWJa8EWP/FZdbui+3TXAX5yqdqz30l6Iqndf\n2CTpjsWNNy6lchfOIXnUqzAmWEs4MYZ3on1bOOjwX2KZrQoZozJ/U/kOdQTB\nQOUMr1BxjAIs4nngr38DnLHUuCtsNCXBHtfUfKWCyfyFqUnHSZmf+E+MGkfD\noDXUlYhBb3zGbf+WUaWtZi03StkbZ2gdA6vd+yb32rD7y0yFS4JGlPKj/mjV\n8xAVMc122/li2ibXEGnZILWOcniLGyt17ZC5TdXd7/4pUmEAPuB1hLVAG+OY\nVPayuVzGkXi2ixOgNZX+cFIaJUbF7Dx3IVOfiEQ2kz5qzgRCLxC6inPE1Kfg\n5ck2znLzC58haFXuR9y33hD4M4On35JlR9hs+3Wtsnn7yEvmAyccRXyqKUCp\ns97gSD/yW+oTTLxvlWzQ+Eh2bOD6OlUl+TyoHeey/Vi5SoGAgUgYDAmvDIOl\nParjOERjdAx6H5+QpLumv5jhHRw96M6uLHtHWzQf5cXci2VlTKzqbFZhxd6J\np6xaHdWuzW9/37kG3hl/90A7o7XDGJb1Gp/Ma7J5SkgBIxpzzheD/8LdNBiv\nsRNy\r\n=yTL7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_13.0.8_1580514086821_0.4681797256307747", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f60c17dfa3d977378df28a46766f943542171a73c8b80e517da0516865963578"}, "12.0.16": {"name": "@types/yargs", "version": "12.0.16", "license": "MIT", "_id": "@types/yargs@12.0.16", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "e3a5c0420bc72917f780d222d933871328d0ab72", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-12.0.16.tgz", "fileCount": 5, "integrity": "sha512-L6ZOCUl5N/7BgBByqGk3hOsqEvv/orgBy7+JOH2GC57J6xyGO+dahQyG6W8XDvQYjxkWqLkq3RBpbSy5WVR1TQ==", "signatures": [{"sig": "MEQCIHxc+DLtLazXGiNTZekbuEGh/0P/65Z4ekSPe/MxpKdPAiBJF5n1IpI553LbbBOF98F4SfZdRMfUu3SjC7GCr0stZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26870, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNLs2CRA9TVsSAnZWagAA3NkP/RtuT8DJmUQHYYE20Oii\nly6nIN+4soCr1gW2M6y/MkKUmowb2C2dPRX6OSnoVivcgteOkpxjv6P6AqaP\nEO7xWthaBXRFiu1s7JNpk0ST9V7QawfGL/q56FaAw9P2PYWNQ7P5uPcRXdAJ\nqIVp2A+G2pqDB3KY+5BI0Zu67ozRdLmSeJW2bwu8k0NTpLpwry4EYL3YGXDN\nYhXkSLd/rWoeN76rCc7cEGzy4v7um4Y5Hqj+YPRRzdEc0XCz3bB3Sz7wMmQ0\nyvxNbHpwwIElfgJLNj0o1AnIehBOcj83+yZLJeXjf2thTctJR78PhxjqrOEX\nvTWURGmku5s7IG5HFQqVHj3UuqwarOgB1FK8jAmoeRVFg4CfkzbON0460yaT\n1n1y9lQvbGJaTO4LF+VyxB+7+NZNRD7SIsg6xjF5onn9ABSi99E8xdQ4L+tH\nNeTuJeCv8p0YlTUbXmYztaSdxi4otfeEpg9KXjIyNx8NboYX2CgU00Jk1trx\nUMkyxj5XdiytUshXwhcUgOp2tf0ZCf6s1XM5PcftpHid2MRyYkRR6IA3ZExf\nQhj0jCSvre/itlfTrVA1YEgLpl9kOUQyHxh4TP+PTkVS4R5oaAVcOLwNHrqj\nSs4L+B6ILJ2FyocWlr/XWofSQSEBm3V2qfD+iPUHHaHVmku8f8PKefU0nrHH\naYz2\r\n=H9Vq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_12.0.16_1580514102100_0.13097571900098903", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "cba1a6dea3113a7b337bff98071c30b4bc0d80d5ff5caecac589f1954d67d030"}, "11.1.5": {"name": "@types/yargs", "version": "11.1.5", "license": "MIT", "_id": "@types/yargs@11.1.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "8d71dfe4848ac5d714b75eca3df9cac75a4f8dac", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-11.1.5.tgz", "fileCount": 4, "integrity": "sha512-1jmXgoIyzxQSm33lYgEXvegtkhloHbed2I0QGlTN66U2F9/ExqJWSCSmaWC0IB/g1tW+IYSp+tDhcZBYB1ZGog==", "signatures": [{"sig": "MEUCIQChtojpB/RoiSPOdqpNoCkEuBBhDlwGPRmEU0ij7GHuOAIgIvF+E/rqkEEVYYicFnPia0t8kM85+AHKD+Jr9ZitcWE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNLtFCRA9TVsSAnZWagAAufgP/j7jx2Gqy5S8BFIDvsmz\nYdGYAP8N57H+EmB5jdEQ8o7JenmKqyZt8dQXpQgyLydbftmTTei2i69W3xqz\n9zr2sy++xLnvrzfCoUCLnJDCoVAZ3ARlL0AYGa1v35gWis0nzkVfXqItMGPG\nAKsYdJ/gtSlgq38p4MO7hSaxezTd9dpbhqY9X7TewhRJ17z9IY/Nt8PlqqJm\n8/kDxOSVF2QOizbd335fJSB+eSjuPPE4CfixszzpjWfX+eFtu+rvpLoTFQU4\naY6FU/RC0gd+uv2680/W+nkQ5OT25m/doxWBVggCrTiiP1HY76REivXnuT8H\nU2xFOwTbB82A8NRV9G0q0OZMgLRlUgom86oQNJTW9LQhdD8UwbcPtuaE9y8b\nXmfxh0yKJMVLducn8KDfYpMcD3LDuJFcHMPhRC5FV8HiZAOVDwK1kIQQ4jW3\n0B3JFMvOqt+vSWs6DtkAj3v2nNk3sm6/YE39DXw7FlVmYmdIPgIE7RnySfea\nZ7HFnWF94nOoG1/MxFKBhUSfhPD9Q92F5RzmJosdfWKzXhDl5LDDwhzws9FD\nYzsU25qwNUGv2yQ41J5XGoLdBhtkoK4dpYgp+FB9+cOmv8WZ6oo+KKg4WMYg\nTXVQcKD13xPco0qkifGbuCRNSbSR8EenZcFTMyDN4rVItN4MArgS3rqnSeEU\nTgLm\r\n=Iwx0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/yargs_11.1.5_1580514117373_0.2164406467520894", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "28bb5643e71ccc0ef340e04936bc7b61fef8635af3c86b868537f533a936afb5"}, "10.0.5": {"name": "@types/yargs", "version": "10.0.5", "license": "MIT", "_id": "@types/yargs@10.0.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "5b450199e35a145fcc86b4edfbb7a8d39ca16a03", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-10.0.5.tgz", "fileCount": 4, "integrity": "sha512-pGrcwQFyd6VLkr1akI/lCpXLDtK5FOHRsL1HK3r/lOq6ifGW9WkHRz0Twgy14vCcOtxbSe7c8Aht3szIa3oWtw==", "signatures": [{"sig": "MEYCIQC/SWb504sdtB0+5SN6qbOf0OW2CFdL83mNP3v9RtsvXQIhAKfjINAJvC9yyRurdyVJP7XedrAXwan0oHb7vGzaf9oB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16627, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNLtUCRA9TVsSAnZWagAAMEwP/iZCKIbyw8bRRUHtH4q7\n3qRssDvXGcrY/n5Y3nAVprCmEnVTUeqvFwZsgyjUdZK1aNaCIBkFVnU6Ctgi\nOcVyUKht/OOGCLUwgIi/Beg/m5QajKglBMZdhETwryKHVGMNzRkYJufUT/R0\nUtrLbTIyxBZa3VUW8MlxfRNiEf+EEKbhYu+0oEBBby9IgrzEVOOoCSLbRTAL\nQ2dTaWElDfeT4TraKi+sRyOlJbrK5JnnvEIVOjKhkPts7ukEYmb7QB2mh7/C\nEyeZXbm4T3PTUgxoL6FjVHGeWvFGGkGmI5B3lHOZz2F9yXCw29xJlZd98K03\nYtZHAu3BDkzhkZg9DwWMObOgTwlCGM+I76gd2hk35NbWODl40PZWo5oleL/d\nijYHYat+CsBkp0DuAyBWxwZFI/6Dv4n24NVusDLUD0vpD7cSnBi8cw5mh6zU\ne8Tn6nUVhiKZU8Fcix1bOKqXnRzXJjqXDjkVfbnY+FxW/XOmWAZxkmYOPo5j\nrCl49yjxrTflUEdHvNzCAke3/f8xwV62iMhVH2pX9MuikvGAlu32IBrNNQVn\nlofN33vs4UJgloRZu6GjJUxjw3HqWKxCV30Quv51LSgcKTFN9V1LLSqQeNon\n3O/9MzOxPaBwJl8cHSU8JgEAJIodCneMaF1ifFYLRHm8VBcfMGUfDfelJVs6\n5x2z\r\n=fJO7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/yargs_10.0.5_1580514132262_0.8253003989276486", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "093ebe0886fbf2b9a4b71fd52b4e676edec3238df82d81a42e1f92f803239e64"}, "15.0.4": {"name": "@types/yargs", "version": "15.0.4", "license": "MIT", "_id": "@types/yargs@15.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "7e5d0f8ca25e9d5849f2ea443cf7c402decd8299", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.4.tgz", "fileCount": 5, "integrity": "sha512-9T1auFmbPZoxHz0enUFlUuKRy3it01R+hlggyVUMtnCTQRunsQYifnSGb8hET4Xo8yiC0o0r1paW3ud5+rbURg==", "signatures": [{"sig": "MEUCIQDIUeFACB3U7FKswcfzDSWK406VhmQTRuoz+SUk5oL/AgIgGCVjbecKohwW+P1Q9F6QLWFWcoA+GQi3aCdYwYOcUOI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50424, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVYTcCRA9TVsSAnZWagAAA2QP/i5Tg1d1luuCTjbqYjRD\n4aRZrzBTnLZCe43+zDybBYMONPYLrsXsNy6f5LA6rqQ7uLXA/a4EIdMQpk8k\nuowjC7h4ItWJX4RJ6KdfloCshyfmXeKWVf3+F9/7jcIowGR+AXnji8mvFOiN\n8gRtZuI5rY1fAN0ucThaegwA0V3Ms40ZrR6leBwHChFNW8iBiFaVtOjjB71c\n5EdzLf0620r2rQ/5vFWDNoJywWNhGkkOhk48IGUpjcfcDK3dti1obwYLHhLi\nqLN9vnpCZAC3GKzvRfOljP1Hcu5JME92dTu8++2QXFYqjg27yHGxiFr8gh8A\nPMr/kRrNZeVi9jGCwWOZHqj2cNFRYU/e6mElwhUDQcExEJ6QzVY6OR2Q3PEV\nVxCsHtUGx895g1n90NdO5LvHmhIxUlJKn9aBMBeRDt6/0CNfbv0SAddaNUWC\nLp+NwItDyWQGrRS+qPxMje3/1EpkSpbmT0ko2gUyQ8lcVA5mEteDmSJGXk9b\nE+MBEPs5iABtIkRLTy4K4pOTGMbyIqyNRHMKZXjEzq+ejLM49LtCjUqgPxKl\nH8qHgehsLR8f6uYLcUQyvy9CGcxL7rOdo3UTIrTfu/eE+yYslK4Kw3FKpxQo\nIFRfztOwT9V+Vn4nDLgzG7ep6Jy3kw6hUcDGgLEdKDaOY3A/eUgQiw7sPUPm\nYH+X\r\n=1S6H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_15.0.4_1582662876372_0.7148680428744663", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "33123d2b50bbbc5d15307f8a14b565cbb797530f625c3087b83b21aceb528536"}, "13.0.9": {"name": "@types/yargs", "version": "13.0.9", "license": "MIT", "_id": "@types/yargs@13.0.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "44028e974343c7afcf3960f1a2b1099c39a7b5e1", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-13.0.9.tgz", "fileCount": 5, "integrity": "sha512-xrvhZ4DZewMDhoH1utLtOAwYQy60eYFoXeje30TzM3VOvQlBwQaEpKFq5m34k1wOw2AKIi2pwtiAjdmhvlBUzg==", "signatures": [{"sig": "MEQCIHlMtm/afx80Fp7ZKXI/QmvdRUQFdoMzVEpg/g04sQdRAiBZR5pRszYdsFq07BgIXxmMcmWyMznBcfzMCE1pj/goUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49817, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeudzLCRA9TVsSAnZWagAA9PUP/iVdaprasj/irC6EnPor\nPIqa8Qrda34gGNEHQEK9ThPPxj5J5YWhx0+SOK/hO16uW1Gx4idrNoRdq5yf\nB09QXb/DHl/2WzbHbk1Ufav5mc910oOD0hynF7EtZi8KQYPjeW3QuvAbj+w4\nBXdRQsBfzMUin7kTjsXT0FmCZk6K/r3myRQ4yFP0Hxrn8V3Vn24bKs9RC1AC\nR434zn0Xjouc8zSBy5y1QpccS86JxPhSn9MiE0HImdUQgnw3RSepxdtGm3qv\nhZ9pnNeie0nMDZA2Fcs0UEHu0u6lMRbQ06ubxTfSF0DhRDgXLVdBfyRFzZ8A\nNDAW/X34B9vzQNCnmQcT0PWDYFNYR2dj+Ismf0khN81VDAIUzPB0Zis5SEww\nwxauGDQ/Esphi1omOeAUzfR+KQyCFXcoubYqzLlytKUf2pZEfeYSQsJqUYAd\n4mIB10+UMq5SrEnN/7R/aL7QIjr4C+VXiwf532xNUPM23NNjSxpW2oHXp27W\nT2k2ZMTAqq/MUgbtuEnjavaw5o/ZeUtbhW9bsxijorIaX6xzBhEztbI6tCjR\nS/70mUczCz9ikUCXeIK+RYEhk+1/sjFSwZFUfRY8mkgeUhuX2FK0FE1o3ko6\nW51TyXPFbLI6OyjC1LK85ylcXxrYQzdiqWLf9S6V3ctAFMYaV2zbaymiWUTy\nJXDw\r\n=6ctu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_13.0.9_1589238987096_0.3029732377633563", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "29e2debc80d07a54e3f10927390d75e9d9dfa9804f2a85a1819acfac60b5d21b"}, "12.0.17": {"name": "@types/yargs", "version": "12.0.17", "license": "MIT", "_id": "@types/yargs@12.0.17", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "631692a0d3c201e4e2063c3742201031bfb2e5b3", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-12.0.17.tgz", "fileCount": 5, "integrity": "sha512-WNDLAvU4Io2ZaML2CaM/9kSgS9r6zdLfgpMWj40WPr24lrCc6RuWla0e23fy79/iD7IiF2Om9xsNISW7V2zZLg==", "signatures": [{"sig": "MEUCIC4tcN0YMrWwtU+6eY8FNvTgOsHh87i/oLwsgV/g7LpfAiEAsEWgLa7hqBODhFkA0m2CAZONhZBCkZLYOeKAOjKcfLg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeudzaCRA9TVsSAnZWagAA5uoP/0wu0xhTjYp7fLfPR73I\nymnXoopBp3G5tcvC3spyfENSU8BqlAT9SLlHTCMA1wzdLMnNIrVvndMSL0vc\nc0IKl2XewywPaMTRC3xypn/Lyj/137s+Jt67+aMGWETuiuD0EMdJ8mWRixwk\nFUmc/sIaEQHHXevCF8IWMoIwe46FKPZocxTxLddiwbsxAE62SJQ6ZpJQZXAv\nAhBz9+Ie9GVkLj8kInR+GMghU32tEP/QipA3BNaSH6U2oNlD/km99olaln74\n1jYAa1tyjLkNyBxMlihVI1teH+HEOeVh23xPvfrDVBn+W8l5AVlvl5UpxDwd\n/ZiCQr2cITse95nE+pdDvhVbfdCtqjd7gM1KOINEC1rAz2tpuYyAn8ZU3L8M\nBR/b4rHvLyOXGimYRsJZJvUFtVIkF7aI5NeItMLOE/mWi/569/Yxc83N7eZ2\nvX+yy+4Smu4re0iukXg/AvvXRdgS9rU9DI9gdP1j/JWTS/alRA/h+/y0KkwK\n82B1LPyquVyDJcimyQSs8jsgcWJbB0JrxbW18KoBYJ4IOzI6NTdEZQyaURdM\nT6roLLzR7k31pgMNzxwKaSzkgwP6JraUsJBQsoywqK9pPblVLL3R6e8GU1dR\nK4tRuq7Y+74HCtUKsdCDzXG4LQrSm0Plm3yKldEp0hSRihczROr1FDJCXYiO\nGZ5O\r\n=W7We\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_12.0.17_1589239002206_0.6578900460373107", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7c6d064c0d6ebf41058e76752b5431cbc590d26fd3876802fa6720d13fed3296"}, "15.0.5": {"name": "@types/yargs", "version": "15.0.5", "license": "MIT", "_id": "@types/yargs@15.0.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "947e9a6561483bdee9adffc983e91a6902af8b79", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.5.tgz", "fileCount": 5, "integrity": "sha512-Dk/IDOPtOgubt/IaevIUbTgV7doaKkoorvOyYM2CMwuDyP89bekI7H4xLIwunNYiK9jhCkmc6pUrJk3cj2AB9w==", "signatures": [{"sig": "MEQCIF8coMtG9Cu4T+HXCe5MPTuLdWtkssBdF6YIlAjOBLOuAiAkRraWtpbfhqn+U56TJ1ZZgkMuopCGY7xf1tu21KGhxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevGtACRA9TVsSAnZWagAAbCkP/1PiN3Kc88NyMNvGH1gq\nT4X2w7K9uh5tyz3tySMIA7E4eM/4ko1vDzoVHvk2xBlyH7LrxibJguLGdRXr\n792YWPJQQmcECwxmubpb1ZYjr8ZudP4iAR2flnZPKSi/fm3ubL1pO578A/GR\n6CH/GqqzUlzn2y13flN9DdStRe+M2aKLLhkeaWJIXgmzLlHcsh9wPQyJTb5z\nsv6UvPIv4VqIKgNQveroa8TBFjq4w27hCF445OB68PYbBOrGmsBsiicgUZeB\nPiAAcejby8uyyNqm1H/pcCQFgQHEjzjnrVVtYwncZHW2icRpR59wXxAbLNOr\nRE4bA7QvEWAdCsZKINfSMwAGtb02QybFXNxApMX/10x6qfRnUlRyGBN2Sta6\nsk/K/tm44ZNSdYUaNtBZg4KjWqI0prhgjKAVqcBVh1CVVQKPkyDeRHS39y69\nxdw2dQbVtO3Sbg54O7jG2WfnsUX5CQVKOXE0TdApEtEJShF5/QHiN+07ImYK\nuPP9SXfWbu5aMoPSxOhwBGvsaIMHtoAl1D8An6jzvtajBN7ySnOTJMzBgmAJ\nT0N51xwq0hDec962V5wFehugMcqNBK6MT86Q0lOuvLhlqTHCEPVZ/sKlRytd\nrQG7vwLuHB+s2M4H6EcwqW62wgOHMbGuattg0yPngRYy1Qx2PFIM22W4Nd2v\nBuoN\r\n=TLYM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_15.0.5_1589406528164_0.34175767569097326", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "2bb8b330a882b24ea5ce38de8f9057b7e686daeead308722b6c2e7b262fe34fb"}, "13.0.10": {"name": "@types/yargs", "version": "13.0.10", "license": "MIT", "_id": "@types/yargs@13.0.10", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "e77bf3fc73c781d48c2eb541f87c453e321e5f4b", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-13.0.10.tgz", "fileCount": 5, "integrity": "sha512-MU10TSgzNABgdzKvQVW1nuuT+sgBMWeXNc3XOs5YXV5SDAK+PPja2eUuBNB9iqElu03xyEDqlnGw0jgl4nbqGQ==", "signatures": [{"sig": "MEUCIBbW563ZMYxXyv+JLEgzvuaiBJUxbQBbU6T423cM9GddAiEAnh3FOyUrUuOoW7ANvzjrSRMV7aMBGi3LzDwf9r2mx/k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49990, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKzBxCRA9TVsSAnZWagAAsJwQAJ6swL5cSKeVT3Jxzay8\nllMJfQDqCFqwcL7MfI/GWHyeGmG3pVdvu92jfNHSE3Y2hfL58gRrF4g4q42D\ne9xhu7W/OuhjONJQnfWj7CjG6jQ+sB5Q7XVF0lofD9M5Hp3f0QuDH+6J7nts\n8j7RjorxBdXjiaq+3NkTXY3tnDhIriUD3JOwYVAYa73L33PAmBbC+k5XfNGq\nAocawweeCW53zULXfdTrl9NDO0CnRrfaKrZZjBs4pd0VgeZXvIhjYJLYVFhO\nGl3+Cxx7FY3aPL0A154OKYIwGjHos/5/t07QGLkae92khLnF50TbtGVz6Huz\n9+PRsjshfRE6iGwArRe6+Mi3Mpfq1WierLG/ktfG11dWgkSlVK6bT8gXRmlg\n4O9jFK6d5n+vVtRHnq9uvy2JvpimDHrbV5TaJsaW6xOWqoqrmmuIBBb/q1lG\nnGntINYinvwyEkmmJycsKHbd9MRzZMhfVk3Qf/RvRz0fbS8p0nOFY822G+RB\nkOkOBxuMsVTHytQ2FRZOAXyTb9nkHE4vkMWcNp1TQsGdFs+26snfRgujq4Wf\nAVyRCWQFL3uD4G27qsQ/p9kS9QeLTW/kmPcsjlkXwTwz0mHgvg2lDD3jse5M\nNClguSsVfwFyC8nHEeCJDvuMk1fB7Q/BRHiyeXYd3gG2otVxTxMa4JXLRmPH\n6/sc\r\n=i0J6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_13.0.10_1596665968857_0.6900929603990371", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a7ee2cfade1340ab79423a143a4528266df904e02d386143a1260c0dfe472edc"}, "12.0.18": {"name": "@types/yargs", "version": "12.0.18", "license": "MIT", "_id": "@types/yargs@12.0.18", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "df12d5f1ab40463d445dd0ee44999b527c1364da", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-12.0.18.tgz", "fileCount": 5, "integrity": "sha512-S5zJIrr2/n0DJpbwfpUc7wt9zQF0Y4GYKecxjPZ/25xPfsCIl+u+wD/HjyNZ6QPwmDPcPJmt9uMXtriRW/FiAw==", "signatures": [{"sig": "MEUCIQDJS9zEfsU0ZT57QArn0PbQrSFySkiTXRwoiS0Slpcf8AIgKuGB2hpzt3jrqHY+Vjpedv/HYBnCBrv+C0WpGDR/rZs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27008, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKzCACRA9TVsSAnZWagAAoQcP/iqi6hIGe/8ES/ulMHm/\n5sJ/hC4XreA9uwz2OGHyCwbVQkvivnLRmzG4YDzF+aS5GmrGUAnDy7KfmlQF\nODR6Fr+h6IT+gtu8jiy4nXG71d6MFr50M2eB4GP0XLmkVBNlFvalXjTvAd+X\nasqjPnDFAGsVMcq1k1hFVKt+/jZkcfQ42jghPh7WgZ/ntvnNKDx0JDX40H/y\nYzoZXLEYqxQa1RWXwyocIS4CkpckPyPLioIcMbfYhpnlQjwa89Hu3Wkuy5cj\nxh7M7mzFVA6cy1mpfhiYbXWBqg0CditvzLWr6FICI13sij9Tdi6eBTf4/Vi5\nOwVfT8vW1DWIlupsiA/o/1puOCW2+4JheCXaea/Th8n9WRSBzeEuWqCaf0u4\nrcJeFjEBxLfN1SzIXCV+Cthtq4whpCu6fNQWqosRlmIxqwed53vkEwPImhu0\n0xBLhBGe0/Oik93VcqO+un/+hsIQFh/dmzbCaNkrvybBKYKShwg08tLWXaQx\nM3W8ZfDWvQZYooSzbnr1W1Bw8thifg68zTg4O124SVoJjOA/YmzaFHT+rZDR\nN5pGsUL8LXrLEGX3RdwCS/AcfRuU4qUci5QKxApYZU9a8MPnKyr1KWCYG2OD\nXXx4yJ544ABYOJtUbLfg+eWdmTeriMYb+7nE+olAvOzUYHCzDh9ndz9fsA6z\noz6R\r\n=o4qh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_12.0.18_1596665984194_0.17797634838614806", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8f399f92f761d33f849a2582a90e86886f8162c8cd190b4d3a8bd8ae35505949"}, "11.1.6": {"name": "@types/yargs", "version": "11.1.6", "license": "MIT", "_id": "@types/yargs@11.1.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "02c2f4740e30c113717e44cd8be0d591b7a286ca", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-11.1.6.tgz", "fileCount": 4, "integrity": "sha512-AknAe0PLtzjcmT6kYIGLSL1hkdU+xrpxZXspM6UOHoFW+F+2FAWiRvQ0qSVpnLwjUc3dsrFyMAHK5A8nzmLlCg==", "signatures": [{"sig": "MEUCIQDQ0uP56MnlPtSOuYCicg3F2B7eMU5xNIEw9pLbOWTskgIgRz5R5uSNIJm3kh494KqNQcWYoG5/r7O452OVlYpsyNc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17128, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKzCPCRA9TVsSAnZWagAAD10P/3u953z/t8A3DHIbfT8g\nMvohBkAbw8DiHr/u2Gm29GoqvprmPU979Y5EIOJJbIcBRZ71jJ6uBFrKAGy3\n8kduXdpt2jZH+Z7MybwQ/MvJkzuWPxfLk8HJJ8dV/OLzPyR/jDrXw6DH7H42\ncYutEvabkWcIMNDEU8697S1xrQkATBTZHn8s6h2MMauMh31IxGMyWntxvkVQ\nHN8ylm6TGsUoiE6j0MjxB4W2+U7TsZYfK9OWT/gb6TAe7q4FFHP3WBrmzh7A\nrug+IOl8iW9cE5GRieXL1VTuo5ytTQyxzf7LTE9erdo+14jbA6SdfUjIsbFL\nXavu8wbTYOYzEPZncdj+3IKcu/JNkQoPl0fw+WfWiTFpLbYfyxwiIWzjNFi0\ngZoMmqalXOOSsyYr0ExfUB19YjgVmbI+GQjA59e5gq29cb11Thh60CVYgRH8\nGAZG+MQiXXHsLnHJBrDN4JE7V7I/BPapE+LM9ZEJJ6Ad8AZUAFTSmMEJqGo3\nRl1Fyv0vCql5nW2Euakp803vSKrgh/E26kL4qoteMbk3oz2D+HOkWXO0v10J\nN1LvC3NZUmFq7QhAUyj1XAXBufztbaFlB/0pNsYFHtO1PdjSLcRYmxlomGiO\nhqbGiZ/uEaOBnJT4B3ybAi5a/21u5yO1ITMKpbQZ/MYCjmFBp1T1fBTmV6hu\npH3Y\r\n=TMfZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_11.1.6_1596665999526_0.4336389585185656", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ae83e2440e6adbb340e4641d86523895cdaeb706a23bd3f4e989110bbdc034e9"}, "10.0.6": {"name": "@types/yargs", "version": "10.0.6", "license": "MIT", "_id": "@types/yargs@10.0.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "f1458bfe70869577e282c6fc9c88d7ac3b1b74ab", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-10.0.6.tgz", "fileCount": 4, "integrity": "sha512-AkXVu6KGKUvBK3sCNX3vs98mpi18+m5fT2AlWddLTRu1Tla92xsIIpSLYCZG5dXXJEeoVcmnoUZ5fDeChda1nA==", "signatures": [{"sig": "MEUCIQDvwxXekq+o6ulImGJFKVHUXh7zJz2Zfz0NbwfpfM8gAwIgARr1NpARV3JkypLp3TApuOL8oRPtSE3ZP5BWETevzg8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16732, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKzCgCRA9TVsSAnZWagAAYeYQAIxFz10E73/654gvlXUL\nPCKT06DBX/PdNMisWJFgi/hMsDVeUbc44deTyrRmQKTCIYFFVAclcZlcHwt3\nvx2r71fmuusDTGQAL+/vKDqlVy7a/zUHrEZfHg23wx9gb91+Dr6Wi/OttRCZ\nSCWI/2iT4n6WUP0cycypbt+DiNXay6mI2z6Er7g/fix0EmSfIQBF7vMEIjHC\nnSG/15LEUpgp1VqwUVpqaQ8YwS2KykqqBfwqWLggH9LSqWGoSPMnjwhkWEnO\n1/yNjLcr3LLW0UHtAYozt4WDLdnvuvH6NnL1QKbLjH2Xl88LRwys2le1NHfH\nzkuNpk1DSO/FJa7jxvJcplTvQG1Kn9V8qv54l7gCtPKUMGbS1jnZSFIz3qnY\npbeJdMFFBBGmaUt3NxwjIADHEUVPdkwWDe6BRWJ+RunbS2cGPCZEAr/6hkQl\npdtYNHvIMPZzgHHbh0sqcgpiOrCJ3jGZMMYQX0vaVRioYLsShASK6AWLXcON\n5a52agCLPE5sjonCMqsyXV/nKmqjTuba9kPUO+5Z+7tihqhkxGCOXVyMNmQd\nSyls8+jZhP6LnnGfD3WHbZmsly7qlPi6zFChC0zrSTPv1n9gIliSlAdsTll3\neVRxeNgMiz2Q3d7SinqEjUROm6QYhwq0fpVDTs4E/NLOBmou5v2SH2y3A7mG\nCFyN\r\n=ziTo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_10.0.6_1596666015948_0.6780062421390212", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3e66990cbefabec9f673bdc15589949b92a72fc8bc817918cc9a937efa231a58"}, "8.0.5": {"name": "@types/yargs", "version": "8.0.5", "license": "MIT", "_id": "@types/yargs@8.0.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "670e913342c909f5a3202c7d3b860abac12877d4", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-8.0.5.tgz", "fileCount": 4, "integrity": "sha512-ODv+V75Dxyy19IngzhWNgH3dfbgUrfSZZgA2pit5djIwaS3hSADvnz2kgw2TkF/duj/akIAPFJTRlqYdGUrVDA==", "signatures": [{"sig": "MEUCIQDUSjyvrUgHfyKOxSXHZ0RZWRBCvbasGiul7oMnjw1zUwIgVCLaiTzUnk4joXUzPY+h8CtPgL3sTkN5DS2zm2qd6UI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13117, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKzCvCRA9TVsSAnZWagAAqikQAIbj9qRtnYNP1Cm9bx8g\nI0EQ9jlxjmIe7W9jkYRjER4jookEzMQtFHGC9DK5D3Gs6Lm5IrlgH8tw/HKA\nAHMcn9ySDnzOeW92HR4BO6F4jQG+8dnVB/0Y5VQq2cyZ7h5DwO5H0AfNQpMo\nXkMy+o03mNwKcgKv5chpQUrn+qtiZQK57qz1FeObMN33w7XPp85Y3wms1DX0\nYU/XGqX3jdJkKbLb1xh52MM2hSNbHcKsHw9qsVp7wWLDN1PeW+v70Cha4QY7\nh9LAZOkvY0/mxmAeYa6KBoqiVC9cu+6E+bOg6gEk2BGvLHRv0XJwGAa6ER/h\nHSyV/Tsvy7p1U10U3klKd3YHUcM6tY/xgHjySp5pUA4FMYZpLn0KUVD4Nqpj\nmgxYcI9jOMP3LrpN1W0WiKIa/iiqGhQ0lr1hFsIi7g57y5evYWX+oSJ9iJRM\nWDd6dZe0kq4DvTzXCI+6HRpXu+hQDsEbLi0rVQh6E3NZMWhl8mbBCNneRylM\nrElge7TSbEEicBWLeUWuLQLxSeEdAAFON0OWCJzv6FuXdim1+SBUSxHLaDtN\nklL74YIlkojWjkuSrtfNHYYCPRRUeekezWjVmrs58I06ZKQeVhv6/POrPlsG\nBBtK4Hx+L+HhmF9W9xHLN37Xfa96/sItPEfwyZCuwJKnVcj398tZeFMDWOz1\nFDKa\r\n=jndU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_8.0.5_1596666030793_0.6811124380717815", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "427f8b2f7658b86b95c2e29eee9add2f37960e7adf411278d628a12f6f2b61d6"}, "15.0.6": {"name": "@types/yargs", "version": "15.0.6", "license": "MIT", "_id": "@types/yargs@15.0.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/jeffkenney", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "822919963e1acc0ed03dbb11570d548258004c62", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.6.tgz", "fileCount": 5, "integrity": "sha512-sw6JBfeZeE7ep9Pmhcysxo4lgaMLlHuQ8ie0srx5VOEnN4RNJgLryCuvxCUotw6Jmd50zBwCJHs3iwjLyIJgbQ==", "signatures": [{"sig": "MEUCIC19MkzB+ao8cODJZAb5ZN15DvVreHaX3eLpi+AK1YRxAiEAzLZDNfY1UK/sHmm1C+azH5fTpC3gzF3s2QV17CsgRB8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51194, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfaqUFCRA9TVsSAnZWagAAZkMQAKMNVOksI8UluXf9gYRn\niTbAnTnyD1knHmtAPEPeXlV/qkPpQVr6orr6Mxlb4FQJYyArfTj/G+9uhMn0\nymCQ1yNhy+aunlfm5mci9vFIG+LWA69eoBM8Ij36jc0Zkrqw53Xs4lYrufv4\nv6QVhTGSfJ69xDYbZn3kxr6dIAC4dYhIMKG1QCyMgUcK1DjDdGNNTN3k8f4R\nYNdB7BOzZZ+gZaXpjKTRShSM02o3I2YrQKdiiWCQsxVZuqa8Mw2xeM1bty73\n6R4t7/eB+Ne7aKCWoMf3eSgzIf9LYWYa18FRISNLONIkGniKKpsDGnfCNdAo\nZ8BUd9iDpcozuiMAOJkgwDUtzN8UTanHxIKZ43TV7q2HLx3x2U+JozQbppOY\nJ9xRNZK5agcJ/MqfNcVqYhLpnqi07SpS9m+698UQZYm+QyfI1ooZ4MVtbvR7\nb25+GNkoLQbuca4VIQTxMuxYuT31dETrli/eRcMJnDsT3/xoK4BpYiKtz7Oh\nAOmBHmxTI20H84agygVgr/PA5drfETYyFodJy+qEgF95IBGxszJLOSmSb9Ex\nJMFdHfbtRmnBpbWDYgqANCI9Mu6EgW8KQbahFu3xcqcWxsMSJIuauhQgRoyb\nIj0411Lg1w7CZtIMd+7FEqFmMtXkUyxug8VrtahDOjZLQBnR3+8o6+c1jDWQ\nH1+s\r\n=ULs8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_15.0.6_1600824581218_0.6882522825509914", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "109a7069aaa7653f636bd989e45b1e2e04265c2e04fbc1f46b102394bed6254f"}, "15.0.7": {"name": "@types/yargs", "version": "15.0.7", "license": "MIT", "_id": "@types/yargs@15.0.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "dad50a7a234a35ef9460737a56024287a3de1d2b", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.7.tgz", "fileCount": 5, "integrity": "sha512-Gf4u3EjaPNcC9cTu4/j2oN14nSVhr8PQ+BvBcBQHAhDZfl0bVIiLgvnRXv/dn58XhTm9UXvBpvJpDlwV65QxOA==", "signatures": [{"sig": "MEQCIAaI3aC5kXKLHufMvx05iGJ5IpHM1RevOWYU9ZweX704AiBJOybvRNsD7j3Cl7QQAfnomd9Ha1owVMFit5k9jlr+Rw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfa5//CRA9TVsSAnZWagAAJU8P/i536FKG1a5gGPqhUXx/\nOkwmVqhJByaL6FQ5IwVx6cEHHcAZjrQH4FXMGStzv34NFHs0A1iWCABXd1oQ\njt3vcpIQEx7pm2vYcwjTAJY3OjDJrpnTlJtI8bRLq+CuVc1F387Bsu3XANoJ\nelfG0qmBdNdFDg5g9a0p5Qyv7JqXQ0+uXWXGpgEHMeknhBccyG4lDf0n8Ylb\n+w2n6AQJRQjwYb1Rlp1DX9VZ7vRKXisFtOX+iqrRrBS5PJeHjfkVYKvhMHtT\nEWy7hhBAf8KsgtoBtsaMNRtxrRYJV+oTZSl3qC29mh753M2YIu/TA1ymOG6i\nlQ7X+KRbdkn9vVh41XFcl06Qw0jTczfCOIK6At7ldLiX6nwfe1iY2oBsOKxB\nkcebCiBC+SJeMHdPCU5oTUyktUYqqAXJt6fRiu5V1CZFMSt8ReupzoloCf6Y\nC4Agybpl8Plfk+4/jkejq/+WRNomVN3RxDHVc6vOxMVSOi+GJc3I1M9Cfw6F\nGNLnlDfP7OQYTLmk/zeg9N3fcWRs4KNu+IJfTr6sY8AWRoyWwgXy4wXAfRpd\nU86R3p7khHw/r0FBy8qIA4O3dDA2r/k2qZictrADIQ4X5sG4BJA6FwpfoakV\npcc4k5dNF79uezZtFM5uaQJSEh5307hWRLysT/Yqo3ljcWUr5k8J/hapsdOL\nKJF9\r\n=En8H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_15.0.7_1600888830879_0.425132577270811", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "4b39ad82ba1c1a2e95f9077e568d6a92124090952e30fe07fedd0a9c0c307ee0"}, "13.0.11": {"name": "@types/yargs", "version": "13.0.11", "license": "MIT", "_id": "@types/yargs@13.0.11", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "def2f0c93e4bdf2c61d7e34899b17e34be28d3b1", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-13.0.11.tgz", "fileCount": 5, "integrity": "sha512-NRqD6T4gktUrDi1o1wLH3EKC1o2caCr7/wR87ODcbVITQF106OM3sFN92ysZ++wqelOd1CTzatnOBRDYYG6wGQ==", "signatures": [{"sig": "MEUCIBOADxNjGtee7lFC5y/nDLZzUEjExo0G9VR881LH3gQsAiEAzSbIaS7iRL3LppCeXqdiAVjwow6h0PnNdkaVOh00T7U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfa6AWCRA9TVsSAnZWagAAKjMP/0FjqlC7u6GiuNC7WcEy\nUV9Z4d5egVEsbYWR8jVqv3CtHYf9Yg2KNANBtBxOlBUe11+UOdnrjcMc/RZT\nVp616iLcakjLlynY1shgM9jrsoqt8pDfFEZFcs+Mts77mPj/7my12445SlgL\nVHjphjfRLVYoRpBKwG+juWFh94WuW0adTX82SM+ZKPSnYdGoPKnqLI6R2+qW\nqHIj1d05UjwXRzyNk6RGwr3M+tZpee2XApiQK1cvVNIMmD5GD4UYswYrnjJ/\nHPdFeL9Clf51+rixpPyVGgTQvMSiRWVjjnQIk1gvqWrnRPtdXqJD9p4mvP84\nrxGnCZstfezrzWUOZajkb94bkrKJMWAf2j4cUyjztoGIbd8DIQ8bzVDUEz6F\natWjZGGoTfVyQXUUadVp48fNKzcgk2xOlPvMsymDQbA0SCuePMBlMagxrcUQ\nUd9CtotOYJWeSSLH4X5I9Y0ORK+kQ/Pr8rr54ZqtRcNTheSl38WFCgJmJKTR\nRNnskNCE7y3P12J4l4kspW2l8EFo8cNuL+QdCg/Vn1SfR2Nsu7zIpzXaxvSs\nKETwn78l5vbuGgvLVGh6NC9tYwNmaWmxc0ODV8RzAl/oMdWLr4inuMwlESYW\nz4GwlCh8pa+c3NmGJDKAE2xkf39drcDtcogMwC34jqCHg8IVqjQ+hhwjrsXs\nUOuJ\r\n=KjnZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_13.0.11_1600888853777_0.03829087139612719", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "064f147d70f08ec7f0379158a1e21bfe3f20ceadcadc729bdf7e2272c3d3fac9"}, "12.0.19": {"name": "@types/yargs", "version": "12.0.19", "license": "MIT", "_id": "@types/yargs@12.0.19", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "04ec85aa12bb0b0f05a77699396d39417f0b837f", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-12.0.19.tgz", "fileCount": 5, "integrity": "sha512-1wT5nWbZ0p2dJtKi9S0CjCKzQoVWby4VjtqOeqhTK8XK3nUHWqKWWuji29PzBC7cCDJatyA1VRzQnbEPLiXRSw==", "signatures": [{"sig": "MEUCIBPkEfuD8spMtPqgjY2aqvlqe04BjXv02LZdnGpQeZxwAiEA7vrrSshqUtutkJsdfzfGwtZNnU4q152eYCbSYWitcDU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfa6AlCRA9TVsSAnZWagAAUpoP/16JQU7F4pDXd6OcK21g\nGbVA764Mma8sJgGGzCoEC+V8YIugydFUY2ChUnL4nDG2qFfGSTyo4R0e3WA0\np5hqR/ImyLk6xb6eP5bVtHgopontFHlJtViMSfB9Zej7eA3LA1s0ksUNqmKE\n3r8qU4DPlwAmqy40Sd3+gx9t9JR9vBLVSZeGxBrkifYEVSHSFucNR2dIQdnU\nRglQknMcft0pJqWeFoXbqCU3Yd6vKY0OYYmdDS7ujYxk+VtP+7ZwcZHCMnIW\n4eVNClopZohdKxAnJ2Qi8RTqVBO3884EWlbiwmxzPrhmZucdqlPeTQYPJj+I\n3iipw+1UdnZ+9N7SWwoNhFeXI2AqRc4ilyaxR1fHBbpB0Pq2OsYbijgah/5W\n11ZpA9ACNSId595a+4jVdl+fHvFna72Ks9el8Zy9k2ynwWevXo67iX+HOXbg\nphjQuc5JGalSues2dEMCBg27zWsEFuyg141MoDzPGBCRG+R2Gd8hAPsBF/Np\nHfO8oOPLJ1P53J8YxujPhKkddNTHHqpasc8rRuo0WKusC0o2k4mxJnbxU9W6\nAIH6iF7k0wMN+7LzhVmNx58z1He4UZhDqTAp7zYd2zF1RE1+J3Y5boW5UtWy\nBgwPsx6sLfUfRlahTd+1Vq3bUUtX9nsQsV2I52UpM2B+nik5Yv/odXiRQigX\ntt4y\r\n=ldP0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_12.0.19_1600888868713_0.5062569222095135", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ff82bae9da0c19f99d8a04e87d174341619e25e5e736be50c8865970c215e6c3"}, "11.1.7": {"name": "@types/yargs", "version": "11.1.7", "license": "MIT", "_id": "@types/yargs@11.1.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "a5be4a5f0f4b77b8accdd476004ce911f4241e8f", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-11.1.7.tgz", "fileCount": 4, "integrity": "sha512-ynyKD3/wNHw9/vwYpiVqsrkDZjezXH7aVkg+Xp73irK0WzYYHkBOogiiM6/+eufAxmZxew3aZhAkrrjsOKbLOA==", "signatures": [{"sig": "MEUCIQDt8QUTAvz0L23t4fadlPa0vJrbNCnE7Y8ad34FZwf9oQIgHzcqXoiaIZcdqojXHifiPsK0sqWF8aHe1oOciDU309w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16868, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfa6A1CRA9TVsSAnZWagAAEBQP/A/bKfpF+ejeOqqhhFX7\n9BjL53jZdfQhbD4cAyktrzeSsEn8LjvyHokgbCfZOuQEX7jQduVRjb+1lW8q\n9S1LTaCn5GFIzUhKFXUc/CfQyptIueHaMTGlkb80ZDIjjkzODot1Hd7hOwlE\nM0F7d30TLcjhXvRGihmrgCaCi4W/GLBWI5hiN6cpmYHM+TYmG9oPJOJekqgj\nX3k7jLPmP6R6Mi9DiQYELI6UonVPYsCrFLzNJ+74hjhGRjrN0r7RAbg3eftC\nR4uE3kHk3ZPyabgNlrYLvZ+VIpG27ClPKulnKu2L3d5Q6UkCCOgA7TaHGZJl\nrlD3HB4L4MTVHO/NnNw67Bov01VAQ7GbrDX5ZQA51ozqQ6feeTL5XksiYJj9\n82dEu8RJASQ2btc3EoU5Zu0hFGzqGgY/hE5eYZJn8ZH9mBmy4Jd8aV9EE1Ag\nOFP2oPgACAU1yIMZLWJv9cnwd8O4iRPX3AIINhJpvPM2z/Bhh0sG7okT0TVq\nPvQBl7FJ5+Lg8YsJJYbhZwTG2EAedWInG3u71qeQNnEMWT8etPp59aDgUP/D\ncCnkbOenaI/R8y1kBDrNj5FtcmauHVguQDczYZRDOe5bkzdfvH9TdYxWTjpP\nfRTVHGB/WXOE1oP4LJvjVtuIlk62F/yokW5jLDOPki+WNbhTRPJ1wEsIDdTt\nJjY9\r\n=YsDb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_11.1.7_1600888885514_0.05294781419300354", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7e1b59ef1e21aa377f77e031184d85e0b2607288f943420326042b00b33e2324"}, "10.0.7": {"name": "@types/yargs", "version": "10.0.7", "license": "MIT", "_id": "@types/yargs@10.0.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "dist": {"shasum": "3bac8b1b482d0ac2b3fa292120be7d1fc485b090", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-10.0.7.tgz", "fileCount": 4, "integrity": "sha512-ruCz0Np0Cf01LaBrHc8i+Mgxazs/MdsV67C0siAAdAWVPuwQ+K/+2csHBh/15IQptQPC29nVrrNivZTGR30ItQ==", "signatures": [{"sig": "MEQCIFz2XhX0n/5rm3WdIftOgHQCaUBAo+9H7V+cXA+/kEyUAiB0wA3NF/bfPnOoUM0twQfaHUbJaYlw91Sv2J4AEqx7kw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16472, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfa6BFCRA9TVsSAnZWagAAKU8P/RMT4q1P5oDckJBq3mgT\nu66P+PkijW0iaCt5UtMVEQbysrcxDmNFFZ8AXSNnyXQMsGTBgmd2GkWCJwpv\nOadnAk1viUio8a9q9DYdJQWlOhJCOiYwYAgQHu2vUzCfsyF124UNQkmhmcgC\n76JfCsN4RQ91uZphMyqpVkkzxCaoVthQI3XK4sNo0IqHtHFal5jU0tiLap6J\ni0M4FHNMNIIEF+sKolcKeeG0oZ8RBT0PHLCuspyqhp946Afajm5T17/KZ6F+\nrqm1bvbwjmjFrQRF5drNmg6T9/BrDpFf9XW/AaWe0wdzByeM4h8JrFGrnDB7\nZGhhPakz1noh4goVNtHW1EUYH8ILRFp6Q7OqUAQtg68qQsagWPHszIsY02tx\nyOSOjAi5Rsu7bNvkm1J5CFPoDuczotjF6vyMw+ebs9F4kvOfLFqCnWktgL4Q\nAXMjOoPgX1xfvDnrVdN2/uBW9dL8frmVQqBROWr4aiAyZnO5KVb4oyFG/LvS\nZBL0aRA2EIk9ApuBCHWnQS3gxXOQCMHKdhmIi6I2/lol5MSmzI1xg+F4PTL1\nu3H1EGL3a1hWpNOkPPdT2UKwxN1y0xS8t4iL7Ve5UxVVj/7WgBfxpQW2/KfZ\nWqlgPSmg9xCSqtIPY4TfcOrUBAvP4XQd9brnoHU/FwC3VPhDo8whMc9/dyKs\nRXQD\r\n=mQug\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_10.0.7_1600888900606_0.3282241052793018", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "338fce431353ea8103d342f40898584ffba5b2c49f938d1d0e31154cdcff853b"}, "8.0.6": {"name": "@types/yargs", "version": "8.0.6", "license": "MIT", "_id": "@types/yargs@8.0.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}], "dist": {"shasum": "b68d57b1f9905adb8cd980ae06f11894d08d3999", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-8.0.6.tgz", "fileCount": 4, "integrity": "sha512-30lmIpn5Rk0AzJg/1bjdpjxQMAfuWB7rhOta9z8sQf7Hs70y08qRuW84D3jHq+9vJt5Hqj7st/JX1Nqx43Lysw==", "signatures": [{"sig": "MEQCIHLZuvIB8i3qD2OwEkj0ZcQ+28xUXyg/9mCuC3sAwMrYAiBBg0BoswuRt2tFzU2L+JqFaGjnrmN5fLru/PMIe7BsRg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12857, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfa6BUCRA9TVsSAnZWagAAElcQAKFv8ZLqXmyma0n0QeJF\n5haPpFdaun6FQUuU4qZsL0OJsXpxiWeTPNwqjXzqdkEBT4IaWcOXWv1nU8rh\n5ELxzxlNxk1XBEIQWqMHDygpexYzBRE+A80D0ToC4XLkmnEHPSvNuhgAGvUJ\n/MESz2p7t1WWJJbkH4FF+JZN+naUjysjGXSStEuqzLvkMHhCHsnNE6yGZBS1\n5k/uR0Pcq/+5PN18EmNsa/VbGKdj1LuG9JzrsctAIObjpZYjkJb64pWUQqbv\n+gWyTnD5syQX3nii7F4dVaqJfSa8h4EmoKPhVL2iW8WrQsIvEkY0VtYWVOaS\nzwPiXpwTgA0JHExNGo/jHSlcm+JJEA6yXFrh1RLhQ+imTmtMNS96OlnUOkI/\nKJlpG8CEWsCIfuHZYg2zfgN0cwTW+Ppc2HQsPNkTXY0mZ4mJCE3TdGYOrIxt\nMpi+4D5Pd5z0JuvGOIQxtf6W4CTnc6fDE7uPdcipf1HvIFzgrWVykFebsZC8\nJdltM6yS3VmfWS8ln6+OnfGkYcn9NV/W2u5tnPkHztgSrShk50iIAF/UPmj5\nvEjzTwj+2e5Pr16LujL0fV6fqjmF30FnPJoA9eZIFzohQ4vhvjprhN8Dsogx\nz0jraoLvFqYg01KfcZtFDS5hbvt+480IHOKhzTfhMzL21AhTW14DwAvKaPWn\ncJi7\r\n=Sfiv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_8.0.6_1600888915866_0.3674074930316553", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "1b8bcacf4e7edb3ce480a0a6dddb581f9f0b25f6139a11f4b8977d2311d9d694"}, "15.0.8": {"name": "@types/yargs", "version": "15.0.8", "license": "MIT", "_id": "@types/yargs@15.0.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "7644904cad7427eb704331ea9bf1ee5499b82e23", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.8.tgz", "fileCount": 5, "integrity": "sha512-b0BYzFUzBpOhPjpl1wtAHU994jBeKF4TKVlT7ssFv44T617XNcPdRoG4AzHLVshLzlrF7i3lTelH7UbuNYV58Q==", "signatures": [{"sig": "MEQCIHEVU/vzUYC7ue8vKagxkhf4MgrHLfFN2D/SzIyV04MAAiAXmlZ4WWTc/WQVykKtZFCwLPS/9qS5UUz4zbzTJgTWaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51413, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJff1h/CRA9TVsSAnZWagAAouEP/RY94oK1TCniIxMbXpne\nRC437W7yH4FWDLz6Ul+QeG8CmD5u9xnSEdiSbBgmdwQxJP3HgluKualr2+LR\nSPh9cBlp8emDEZZied6TQuKB1a4PDcTPIe/BRTRrTfL4TUHAuQAFpMAb+owq\nx2yzANZtBPAg5OCXt1cJ+vX5pdLuZHU/C/TJouRneIcSVBGjYynJtrZhxTBk\nSPnKKsieXnGOagzHunED+ItXDake2eosf0QqFTypOJZ6bFLXqsfLml3x4jRa\nxH/pwLWW8ODS0uXYtIJ8K437goJhpZojrjdNYoEjs2owfAIwEkpcZLuaR63M\nVLVjku0kkfyPOdhihU33yenXKNx6UBzdvDyB2S94W4pYmpJXSMx215azEYnz\nsTcxdDo48gpYUaiRsbNCTFGL/ZloqJVTgMcLeYvvSTFbqyjP1GGunaw191HL\nB4Rhht8rxP3oX2RFQlE/YYgInPKwX4JXklnZG7e/veUZCSpyDk+GuHc+LOx3\niO2RaxIFUHLXWhB5MeVsXhukgKnwJhMp9QA0IZXDV4EtRenh6EAuoDOsAQAq\nAHb8XpsI9vvBUj7DlzqThIsyaunGIctiIFXIHS1TApfijq98Mz7n/ia0gmse\nCG3QmK98JCFYM2ZSfNXjhTIcXHv6d6nYSVQ1nlzs5FULpJtFtuHaoMMAyrm6\ntuzy\r\n=k9K4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_15.0.8_1602181247255_0.511319543655286", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "11d798c3f32cbea8b6410703d6318849808023734e64c476ce543d1d8133db7a"}, "15.0.9": {"name": "@types/yargs", "version": "15.0.9", "license": "MIT", "_id": "@types/yargs@15.0.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "524cd7998fe810cdb02f26101b699cccd156ff19", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.9.tgz", "fileCount": 5, "integrity": "sha512-HmU8SeIRhZCWcnRskCs36Q1Q00KBV6Cqh/ora8WN1+22dY07AZdn6Gel8QZ3t26XYPImtcL8WV/eqjhVmMEw4g==", "signatures": [{"sig": "MEQCICa+bcEJLMNrylHKsiTpTQx0CkDGR44WEz2ggYwlr56zAiBefrDjSmgBZ21LntNX42xK0e2qsYuZNNj3BFjpOFQLWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51728, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfib3fCRA9TVsSAnZWagAA6CMQAKNlNyt5LNG/JI8cE0F6\ngyUbqYEEHJgiIw9JJUVhUp5Eri/UOSzz66DWIRTKTcF1H4z90Ha//A2qyftM\n+WNI00R84BeD/VhiQVbJwMsqxfDqM6Ioa1qKvU1BomJ6mPGOn5R8kn3sIDHW\n9pCEbBGQx9Gbdm5Y53d5UCxbSCZ4WYj//FSSGtONhk0J8hlKhaiClUAGFdK+\n9ilMF16PHqiqyEtgpH9/SCbZ7jhBB03ZjaAVx9xAhfxdU4430gRYdkucXqA4\n2K2k/u1lrLjtgeHQVgihQgB2PdWW9EvvgVDIJBn2KXhNP/HWywV2x5jHTpln\nePIpC3Pz7oqiGrnZIbQNg2VYlQQnCMeaUMrwlUcA8VHa31wwnCOkWAS006sg\n4OIY5qOvlm+bUfShiW1Xs+dompgdkm9BUdlbqVdkyQo36BfxvsrJoO0eY5uH\nrNnq9EeIL+4O7LMsHH3zTgcBkpI3gOLgGmoFgfjXf++Ky4HTSQqAf8uzDwqj\nkLNBZy10u7qLX6afGIQ9RJ9oOPkNpvit6/zWV7tV6mmy1hHjrxSeHhVK8KdV\nd0MZJhnKpPkTIzq3yU7HflrSN12sMTmzfBDFdb0oGZgyXiL/wjTDtj4RrJG4\n+e2KRVMT+sh/evV5oO9D99uUxj1UKf8SG1T7W2ovjTdriJOROtOf8UK6/8pK\naQMH\r\n=m91s\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_15.0.9_1602862559426_0.03512423862978542", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "4594ba115356a9ffac78ebfc7bb6880e4c7401ac75a560a444a935955eb1d274"}, "15.0.10": {"name": "@types/yargs", "version": "15.0.10", "license": "MIT", "_id": "@types/yargs@15.0.10", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "0fe3c8173a0d5c3e780b389050140c3f5ea6ea74", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.10.tgz", "fileCount": 5, "integrity": "sha512-z8PNtlhrj7eJNLmrAivM7rjBESG6JwC5xP3RVk12i/8HVP7Xnx/sEmERnRImyEuUaJfO942X0qMOYsoupaJbZQ==", "signatures": [{"sig": "MEUCIQCHK503qQ1ZJrxvm9Utj0M/rn83IpqRrhRnRWgAaqH+fwIgFWJJ5rBulH/nArd16gIFHoBcCkOejo2Ut6adYHF0wBo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51801, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftGkCCRA9TVsSAnZWagAAgnIQAIBWpPxcpI1xkn/P9Her\nf3HW3qFOEL4GCYTf2zwEWFnWtwrPDHrGgnlFmIfaPqrTPU96A/Mxr1EEGGXO\nrL/pGzMNRi8+67TQTAE8TPdHJGCmh0XPD9L7XJdDamtSTy/WP1yhD7/gMGoV\nvtjaweHPOat068znY2xFQ4UJcx3/mMRhoUv4KHA/5oZAyz7nGPvyKUej3YhI\n95Q2AZmcAcz7SUKNzeBnMCBu0TvK0k2StTKxOFGNgJ7r71boCOx7ltdAvs4y\noKiwcmfSfOa0khG3M71BqdXC8JwwDZr+hktuKVvWMoAqf/ZbA7eCQ1wBD4i8\nMNCsjcgSPW4qUpsVgsfrlpPdB8I1c1GHF7RO8EfgSprHGoH74AGOt4Soxogg\nfu8DYIx/2b/KHbz/JWjkbKrkTfyOMCOmqwfV2fJivajEJS/YEtfXpSo46SOa\n08q7U0vEtbdue8xR94mDQxNg4ZGmTyn3n6Z0/lla9ZhsHMhyeTp0oxJQmuLd\nxlv/hMI8L8dBIjjgNyf27Mcofrz7YBazz5Jd/QHVYp9ytTCCRfVIASCCl+31\nRTspixIJJttazss08cPXd95IDlMKqwnN6ig6Ccf0frGjcXORKQi8K8jn1E+B\nWygizWTOMW9sTlVrQxASIHm0lj5eOfGVHkj7aOjJxBS4irIEeMmc4a7HReev\nulCf\r\n=cWFY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_15.0.10_1605658881366_0.3058801153565718", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "980c1c04770beae335597eac3b4ec59214fae852b8eb6da0d7fcb8684f07afbb"}, "15.0.11": {"name": "@types/yargs", "version": "15.0.11", "license": "MIT", "_id": "@types/yargs@15.0.11", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "361d7579ecdac1527687bcebf9946621c12ab78c", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.11.tgz", "fileCount": 5, "integrity": "sha512-jfcNBxHFYJ4nPIacsi3woz1+kvUO6s1CyeEhtnDHBjHUMNj5UlW2GynmnSgiJJEdNg9yW5C8lfoNRZrHGv5EqA==", "signatures": [{"sig": "MEUCIA4mO2QpYzEdl38i+01//+u6YyUWs8hrqYlw82PEnij2AiEAqz1BkjEaIvNUEclHeURKKRlyxHCCEug91s0yYJ89uaY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51815, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfx+fUCRA9TVsSAnZWagAAbtwP/2rTIN6Y9aPVVAAUGhNJ\nIzYQQ5FMENr0gXq4GR41MsLkZiwFXtSzagV6h0aJv+iUPTCj4kFozXfgqCSJ\nP66023/rcMRC2Jr7dtAICmk4G7dbdVrOpR79OTrgGlR/osQ86hjFMDihFxWE\nATnpHZtofKY9qJbkbydPHP4nj/3bwpPHkLPCIWyvngORwFtBDp0lMHdE4qIJ\n0+RRBTNvIWDfvXL7C6rkukm5fD2h5Q51mBay0FIx44o7dvoc2xkJ+fMiUTMx\n5Txx0LQeVJPFmWK3I2tP28MIun19f3IelTy2F98lkv6RpSAT1mbfIClCGEix\nVd0CED45FTDuYOuTDOMztDG9asycWmnrRY6+ISjji8X1lzOddhmi9OnN1nDC\n/nEEWUhpAeO4m/sFzIs/M+nCwM77hZ83iAkeCnMNr/+FRu3KwGmb59HwJlMt\nQ7jRU4qfPIHn0Gc19jxbSJoq5qSOSBzVQtf8L52qIqgCF9ZCWInEPt1bQ+43\nAiEruqPGSeiFRiO5n/2yxJD37AaK+pyoB1S+OFzKSV63O36rRXCve/fX5IOP\ne6fsMaOoLMGtuVGJU+4X0dPKbPxAp9fsnwKTh1VkhGY+NNh8RjAYcXzvpSJV\nkUhhB/i2VcYxAPceH6u3UJJFg7ZuO2CijRSkXNWwCN/WfwMS/knea+6pa7vL\n/MTf\r\n=i70l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.3", "_npmOperationalInternal": {"tmp": "tmp/yargs_15.0.11_1606936532383_0.010400777328203992", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8d38a17965ca009a87ac8f7b1bf915d6e6c2eb04f153b907f2f8c81657b503e5"}, "15.0.12": {"name": "@types/yargs", "version": "15.0.12", "license": "MIT", "_id": "@types/yargs@15.0.12", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "6234ce3e3e3fa32c5db301a170f96a599c960d74", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.12.tgz", "fileCount": 5, "integrity": "sha512-f+fD/fQAo3BCbCDlrUpznF1A5Zp9rB0noS5vnoormHSIPFKL0Z2DcUJ3Gxp5ytH4uLRNxy7AwYUC9exZzqGMAw==", "signatures": [{"sig": "MEUCIQDsS0W6AlY0Fcw7fHyvXHzuO2/K8ADDjlaB59FK2kOrcAIgPiFonG3PL5dPeixMTNNEsPD+Iel+2rVR/KIi5pvgy9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52132, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2PNCCRA9TVsSAnZWagAALUUP/Ahk8JBh1G9kLBu8RFxp\nMK+9qSKuy/Pb8CWJpwWj1n0bJacyF7/jV+41zxlE1U/XpEeJvU1m3i19gdY8\nzuyqvEwDTVkg3J3MlAKjJqWzr3/xLliDwro1nNhd0sPFaikD8Vvvs+Xo6ovN\ns1UC44glT8ca4CEOIN0E2D7nievMB+Jq6OY83u4AIsgik6Jfl69N7/kaV/l4\nP8Sq3bNiE5PI1Dh1g6GPDUe7mNJQGB7pszJQ/DN4+oQ/BqEhqQrTcxP5PuS1\nNtRA/1G6M8EuRwEt0/l4XNU2j4dvem8WhnAPwIHAGango82KHMf5ph1oMILW\nqnEkMQXNut6BTkTy0DTDSBrwv7Ky6JUfWWUOYM4hM7tZbL+jekkaDQoNqYtI\nRGFdNZiKtV7BptERpZeHqPyTjmVb63LswQ4gYz5pYqfRsNhBLNZvVebSyBqY\ngz3uiCJ2pAVKEjllwj8hEsON54fM4h1GXd3ndo6uICBRmeh0XfJB80wQpKoW\nDHm4vv/UVTXODKQAje2l8uuhtZwCc+vP84M1V5lXM23tdooZvUsaB6OYDlmw\nJkoCoUF/CU5H3cl6KcrVr6cKi5dyAazIUQYOdfXhn8xub114tnoJzsRzF7W0\nRrvuxGo60F4EhPxXNSTmvcmQqsC+9+nWwuK32CpyVfReCRyXDeRHC8H1l5bC\nXWzQ\r\n=0uGa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.3", "_npmOperationalInternal": {"tmp": "tmp/yargs_15.0.12_1608053570166_0.8735122254501249", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "81c600c5850fae5f545c97f33a2a0de90a6890f7ca9ba47eb4146e2a78e7c35f"}, "16.0.0": {"name": "@types/yargs", "version": "16.0.0", "license": "MIT", "_id": "@types/yargs@16.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "0e033b23452da5d61b6c44747612cb80ac528751", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-16.0.0.tgz", "fileCount": 5, "integrity": "sha512-2nN6AGeMwe8+O6nO9ytQfbMQOJy65oi1yK2y/9oReR08DaXSGtMsrLyCM1ooKqfICpCx4oITaR4LkOmdzz41Ww==", "signatures": [{"sig": "MEUCIDSMWsK8+AYugulklxA00KpyM12Sur8u696hGBtLmnqsAiEAom3QKbRezac1he/4NTJbX9DHPhNscYofOTpo3Mpbgf0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52131, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgE2NeCRA9TVsSAnZWagAAYfgP/3jxhOgDBdQaJ4ZKnDMx\nbq5SWIaOdsfSqSkRkaTVRyCaZ0qneGTw+RNstSaC7idgFau3OmBXph4gKtkn\n4RgdKLrVzXCH1QipjIU9oWTOiEqbpX+dZHKGpxiZakpPmviBrcZs1oofbdHK\n78khKe2V5y2o1pW9MURM7PhdTMi1KIqhtiPSsfWbJ7va4Fy7ZVoLbk63DXIa\n3gmYqb6aAV8Aen0oC11dNREi2WU6wjWjMChiqfYGU+M63WJPHLWugSuEvyjv\ndvJiY+dWWKI2sk0+s6bB5HhCmVMY7wfvSg0QzADsYWCl27ZIi1v9MPNkbk3n\necF1xNub3wz2khisSIyr82tJ+fnCp9NCjUxSz4TQdaliIOVspaRMEN/grpzn\nO4eFHRney2+r3b4muUBYZZcXQh7XzX9zYnunPJm+lg8OB4vsvA/Q4EuIHr5h\nM2Qu4YGPvvxCE0RIyzlwnN+c7Vnet7SA4aU8APIplIu2y8Zai/YVhe/TikGC\ncK8XN09mNGccTBrVfr3bD+eDdaB7b+mq/U0/073ccav2WxTLkkGFC+m2XE9l\nCAitU/Dwh/ZHlwoDWgnTug7N9YtZaa/ISdH+mY1XsWtbo32el8kcf/2sLPwv\nyk5B+7ToRiDbtU/4B7VO9a+2c/FZxBQqCodS9r9HiRAxDCqfWobsanZqcUDZ\ngFDi\r\n=ag3Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/yargs_16.0.0_1611883357702_0.09887630260965419", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b840afbd3923da8d5fa153835adc2a59373c402f9379503db0a2eb55aaadb568"}, "15.0.13": {"name": "@types/yargs", "version": "15.0.13", "license": "MIT", "_id": "@types/yargs@15.0.13", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "34f7fec8b389d7f3c1fd08026a5763e072d3c6dc", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.13.tgz", "fileCount": 5, "integrity": "sha512-kQ5JNTrbDv3Rp5X2n/iUu37IJBDU2gsZ5R/g1/KHOOEc5IKfUFjXT6DENPGduh08I/pamwtEq4oul7gUqKTQDQ==", "signatures": [{"sig": "MEQCIHYSt5kdTETXF2VnLHQyhl3zqeXTBtbEj3e7BIe/v9svAiAsutznTL/IQzp5hURUGL6LJpymN7uiAxbrUFJBIRmbnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51821, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgE2NwCRA9TVsSAnZWagAAqCoP/j+QxPPTvjq0k624xvGy\ndqIPndbSsEmCa2QvLj/JGWqpoQ8/gKZlR8Ai0DrApqQ3A3tfMMaFXD5EaJkd\nUGouEon9pc9o3eg27/Z0Uc73oNYL/PKfzqZ+1j4V4AJPUim07mAmUfrkYjSP\nawDI+9h11/TUEKsk9pseEym+hXvfD6ethc8E4aSlb6kSICreV/gGObma+7mg\nDOeR6Ah5LmHSaK+dVCUKDHBA9bOInDErEwowZfqKXiSY9DrRieYi6HXyPDKb\n/PMgpBGmm4p9N4PcSO+efpqMR76PJbu9FQ7BtzFKJ94Hkb+UugAgJIX0jxWy\nPpzWKneFD+4k/sdMUlDXcwVFqsxDf5BTe9MfBEP6k7+qLwU51WjDMO74rK34\nGs4zas+OouSMnRh5HWX3qlSgk1k+yINBAshT8MCUYtRUTfM7jY+/zJSqAcii\n3xJzH185YSqSOZZ9d36BmTowhHwRfeVkJMI1EMKmU8Ro8SLgxxk0UBMV44KE\nFUStixGvrcM3jIpAJTklwQqpoQnF5jA5uG/JomhPnDgiCgC/kLovau1636uU\nlVhTXqXN1XpWfX01n2k+b7VS5Nvw+BE6mc0EWm92SdwyABTJ7abpqAK2jhaQ\nQQ95hHdQQpi9pbMdZkh5wp2jL7Vil/pURERggFUmczUl53GQ+/08Dl1Ok1uI\nJqlv\r\n=/b79\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/yargs_15.0.13_1611883375969_0.06430018832238216", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "9dc5dfe6f64e907144ccb7cbdc31a59a4f0d5408c4fe0c0b1975f64ac7d3b312"}, "16.0.1": {"name": "@types/yargs", "version": "16.0.1", "license": "MIT", "_id": "@types/yargs@16.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "5fc5d41f69762e00fbecbc8d4bf9dea47d8726f4", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-16.0.1.tgz", "fileCount": 6, "integrity": "sha512-x4HABGLyzr5hKUzBC9dvjciOTm11WVH1NWonNjGgxapnTHu5SWUqyqn0zQ6Re0yQU0lsQ6ztLCoMAKDGZflyxA==", "signatures": [{"sig": "MEUCID0/yTMoQ/38i95wj2E9+Aa+zMDnXsuKZoljb21+ZtDrAiEAluEfOLTNNmtKq1CNgJzG1uXbqOtLfj8lL4nr+rUU8go=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52358, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXNceCRA9TVsSAnZWagAAAq0P/ijM/jpSpAagH8vhRZSN\nPcVlfO0dxUorXC3c1rGhk7/hkwvISPAJAHU4q/xJXjOutqn6huMXV7432aKu\nJpaBS9yTaYLMEtwya44kdfCrQ7Fu4r29PJUcmK2V/yk0DgBbwAOxPrdoXNbL\nfiUip8Zr5aJtjEBYRpBUaOjvHI0inmxE8spj+amTelhOBVwf0e7lnQ+aOdP7\nDaIzDPEU48ZVaIzpPR+jhOZu+NHuRyidCdjprpbYsecY6q52LZpEpu43Ui34\nmG3OPptuNAMftiUPc+N6CAaNoPKMdTdbHJPR8DHoe8Zl8/cjIzzMAABTEsIj\n8l540lc939SnhdKBO1MNS19e9KSJDvTyJ45fWfWNmNwRGLCvpcZq0wqaNxwo\n4ZwswgoIgvyGhubcU/yKywUHNLbAX3vdhlIyU3Z2Q7Hssjgn1VCEaV4+A2Tr\nyqHSpJ6IGG7wl7NQlpfI1BBrwTCQdZY9XT4bvgbTB0MT6A1wUqCQu3GOSUB6\nYWmpRApuskAqSh8R8NnW8b9xtJCbwaghXTqmoaaQKFTW/xkvzxnrJN2oQ4CC\n5rHLX1e9MxflSJuVdcDbdpCOaTD9bWORBzhjHPDZJJvqEWybE2zqLYDHciMm\n51uTvuHQPTF6EmHk3X0ZsE+/CbhBT91YSvb5j6Gpv9V8ZDYzQiWFWGSVDf7I\nOSWN\r\n=oOba\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.5", "_npmOperationalInternal": {"tmp": "tmp/yargs_16.0.1_1616697118306_0.5786749337327999", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "239fbbe94d54dc788bb480a4bd0239be4a3e7cb30660013efab359cac0dfabc4"}, "16.0.2": {"name": "@types/yargs", "version": "16.0.2", "license": "MIT", "_id": "@types/yargs@16.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "b860dfa2964425239bba37ab3178b0acd79b8a61", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-16.0.2.tgz", "fileCount": 6, "integrity": "sha512-EkZDJvHXblOE2/iFZb3Ty5c8RHPssmFvrXqj3V/ZLXtOt0f7xJh9Fd7p1pl2ITrNHM7Rl3Db3JJVBT7q/C0wUg==", "signatures": [{"sig": "MEUCIQCS2Trhb8ScIgu+b8moO3A7O1x5DJZYcjGzS+aNDQiR4AIgVrvHS5Ry616hEB6HMydgYZB+hdpDxNWso+7e6OgIBWE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgo+xzCRA9TVsSAnZWagAAjp4P/A0V2JQEGOgOvGGxmXLM\nVmhhwra2UtuJJumARtsaEC/BnvVQJ079zLF8iVc/GGzVg6uEJNG1vk5XqknC\nhwHYH0sFB25S6LAsYjclKyMk+9DKLFp61deLNLfqbiQnYIXr/r3THd8M9KsZ\nQdAfD75xX4mJ5e4VbRdmUlsSCGfTsw0N4/8k2ZAlp7IbiGREVLUPW8XC64pg\nCWg5za3MKC0p4NrZ3+zu24iIxav9un0syvdm6+4z9iJs/XpZhh+VN8pqsWaz\nUG+yJXlfFhetOrkS1DIDwGPgUOTyBoCZg7+LjTQvQoRLSPAap4PJG5xe12l7\niWewap8A2qiMn8oaYLGW1q6PITZgG7WvfREzgrZVn7n+/L/chCFAVbYZB488\n++uNqUrK2uIvhJiQAGLyWvVKH4cWKmqfw2g22klGns7prB9ApToXYpLHKpix\nftu+Fewiy5hNv2Cx//O2JhFS8lyBN7PiUO52pQrs15wITi3DpA8GZ5VAjZJ3\n79IwCuId3U/PYBsn4KQFFDo9ExntO3yqPdeF2lBzIB2+iMiW6lpp36//BY5r\nOsuFVcPzlwHX1PGOewkvyJFmfTI1Mr/FupnK+CmN1fsQNiQPKw2rdpar8xtk\n6UxnkKaAE0Ki/lm3+R8I0BkdJDaYB4z/NjGdc6u4pCVJndkQwjfWsnZ/wYhU\nb3aK\r\n=NpMT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.5", "_npmOperationalInternal": {"tmp": "tmp/yargs_16.0.2_1621355634863_0.7155018067269356", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "76606fd40d2d65e82ce39dc28453bc0e8f09daf4aa40f90030bdd8af8cdf068d"}, "17.0.0": {"name": "@types/yargs", "version": "17.0.0", "license": "MIT", "_id": "@types/yargs@17.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "dist": {"shasum": "32f740934eedf0a5cd19470249f317755c91f1ae", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.0.tgz", "fileCount": 6, "integrity": "sha512-RS7u2X7vdXjVQs160PWY1pjLBw6GJj04utojn0KU8p2rRZR37FSzzK6XOT+KLzT/DVbDYRyezroc0LHIvM5Z2A==", "signatures": [{"sig": "MEYCIQClxlx+m2D8fsjYVD/Naca4WXZP98EoVItsF3YQg4QMTgIhANU7fa1f2+fGY4EvYU22bNuIIsSUvr293POQgbKhBmt2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53313, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgp40LCRA9TVsSAnZWagAAwywP/R8zLOGMtPwmB2lHE6zJ\n7G3Lxo/dWCnsnXMgx1tz0Pd9U8t4kIj2FD7aK0YUW8rbGyRZ01cfWz0WD9OE\nJoN1SNT6QhHvP5RJUwYryNSrOyDPe93BkoFQ6OBQwHhKLIshkZxRJbpT2ZfX\nNwLpKPSHwr77J+B7IuRAruKcKycQjTjv5gezA5CFbmZrKIg92rWknLSe82EY\nN54/lDDobAPwjDtFzj3XBw9JDfSIyRj1Hx8L13kMxtMpUdDidoSLB1KF3AvF\nd3ZTPXxkLEBEX0MsCotfpTbXLzp6z4PsiadS4kAhmtH4kA/0L3tJbrcvP8Bo\nuVhnklx2hcbH6f5eZVLUufSEFOBbem2hLdi1EYNEHVm2LONSv85U2Rg06PnD\nUtHRq1Qw0U+JRkyV0AQS5/y6sW4HX2S3eB+LGJ84PDV3TPy8Hrm8ge1Q6OPk\nwAIwuKjlROe7RS3tele2KAVc6Tw9Ax0n/lIxmjevbit5HdacFuXdKnZ9sObw\nLeuaQ+/EjykkwtSTBmVog2L+i6/efz3ObFzMasBZ0v+1JJNYh9hhjLWYSMX0\nTJOg5vPHnX+MwsbtmgKi3lOm/sR5GGamCSRL0dekbeP1XtY4ttx1dJ+3zjaq\nROfiBia7wwAU8nydvsB3FdCDYnAiTOSzXJETPVUVy7S56QKilZT7gw7kiRNA\nxWyo\r\n=0You\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.5", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.0_1621593354830_0.03422583816631675", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "fe3fe339a2118313d9c0674a32f74702649889c130df337b9b11025cf8402e41"}, "16.0.3": {"name": "@types/yargs", "version": "16.0.3", "license": "MIT", "_id": "@types/yargs@16.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "4b6d35bb8e680510a7dc2308518a80ee1ef27e01", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-16.0.3.tgz", "fileCount": 6, "integrity": "sha512-YlFfTGS+zqCgXuXNV26rOIeETOkXnGQXP/pjjL9P0gO/EP9jTmc7pUBhx+jVEIxpq41RX33GQ7N3DzOSfZoglQ==", "signatures": [{"sig": "MEUCIQCANy3Cm/HPuGTmacy1Om7t4E5oHkXDaD35b3vT3h+1hgIgYegLVFZA4CPTewCXnCIp7vXDZ+pxqOUfAAT3pmHUSzU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52362, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgp40aCRA9TVsSAnZWagAA1ycP/0thdQBKgRduIowzk0U3\ntpQ/MFm6iI6t1jSOg5UU6iDifcPAQdBrInDC465qRSgZZWH5bzmhQnKIubBy\nZZkazWt+nlUmtWr0+mGGGDQkNZzjIKsrJAcYUY5gJDfgb2Yo1mMex9GuZYqD\nceyArxqTzn8GaxaEjZ2HRm1lTe2WRPXcDs77o8x/0NxcjDk/v0Prv8af1rhe\nKghYCS9hDntsWLod2UThFG2uJdHgEPxrZ5/QNvgeSxsy4UUMcIkeKSWeRlRz\nHv7ghz9DlspMgVDfUkBesWJG8v0ZlFhiX+ohUchAgqGiPrySKxvhXsFQaMlp\n60Txx7uk2N/CGyoOq5p6t/rWhXxwDh8zOP+jqjoDRMpXTsvIY1QWABQNropK\nFDUvSOvQNjhgNEHMsqHsMm5/wdTvVBoaKuln6asXYZvy9rWdd/tuIUmreHeR\npqKyVnJ13a4MlNSHZuYPSS5NxdG+hg5vAYICiNaWNS6NjfcwLvAcZzTL/Gog\nVseh83a5cbEh4ibkECf3CFQ9b9c+0jwUavzsRp8GeEZH2pxzkPONbdMZRPXd\nplkZDMOPJdK+f7yHkbOfEOfv3moLnzQw6+kfAqSG8Y4CcFOYmyFh8nj+wNet\nt748Dak2l5EafX2ikQBzy64DwA5fYUbiO6JDjxymWmVAsaAIfMN/Z7j3Cpk0\nBRAi\r\n=6mn4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.5", "_npmOperationalInternal": {"tmp": "tmp/yargs_16.0.3_1621593370279_0.7179258146430301", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "1545099833872af2a3d5c4819aaadc45017e9933152ab9cac3124a572174284e"}, "17.0.1": {"name": "@types/yargs", "version": "17.0.1", "license": "MIT", "_id": "@types/yargs@17.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "7129ed41a450b21d11be5dae30d1a834deaa63ab", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.1.tgz", "fileCount": 6, "integrity": "sha512-bGmavvJsnGi4uPUH++e3kb7G3TzRF/rB8m/OY1eH0Q2QahKgPInk3Y+b7tT7D418N08MjsXhB11nDjuqw9w3EQ==", "signatures": [{"sig": "MEUCIQDcaYLWKJF8mltlw4swci+XUi1gyC3gkT57klIAosWEOwIgPo0wpCgRN9+FRYOZN/LasCOtyxe0Q+hk5QUlxF2rl1M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54039, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg30AkCRA9TVsSAnZWagAAdQ4QAJrelOyxFR1uc2ede8h9\n/cR/27NNgNzr6ePxoxuDZpLSdXlFcCDe+sJrDBKCojtuXSKTpuWUwJWgcfft\ndwEK2WocKOfnqgPW9kYDPlhiGzjIKPpGwX+US1bko2l3KqsuxkvjcCe+Dt7s\nbQqnl7zQbxiTTtbFe236ZIgRV4Xur+M2rrPKKXzj8NRzlqBJ2zPBSKL17rB6\nAANoIBw8ZNcO28HkpvdwtNv8XetQy/Ci9DZCwLXTgioEU4nVRSulmtUq2+Zw\nyhN0uGoz7nMipikPLeFrqPmRlSPdm3GET8VsthuL7heZ80wQpR5x0r/Yts48\nY55k6R3MfDq4WijaGLCaszIPZZt/2WsrnE7XSu0ouggxjZyhIQQ7r6M6CXnA\n/3cLduT3igeHIS/5a8/33q8iHLC85QbjEBnCjr9K4dw2Npu5veB803OXqVPL\nL3aPOZOXaO6wf7fFbAXsgq1O6SY5TikRPNwLcgcxzanyset8zP79LNfLEgdq\no53xfecw5Z5fiOI4Q4nVEfBd2uJHg8C2uqXx/JK4IcwmseACN5zSJDol9OBW\nNdPBMQlC9hP57Bv496167xOZTaYUwkZEkxI7oNMDr4P8PSh/YBRltfkAfaMN\ns/8LEdWnikxcQGHOFranZSoquouLe8AyhxW968R3ceAZiiR+l+StVYyZmtc1\nLVzz\r\n=npgJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.1_1625243683860_0.5301668172699969", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "25df0af795640fba246429c1417f257f3995b49935b34d96db222c2b3bfdfc24"}, "16.0.4": {"name": "@types/yargs", "version": "16.0.4", "license": "MIT", "_id": "@types/yargs@16.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "26aad98dd2c2a38e421086ea9ad42b9e51642977", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-16.0.4.tgz", "fileCount": 6, "integrity": "sha512-T8Yc9wt/5LbJyCaLiHPReJa0kApcIgJ7Bn735GjItUfh08Z1pJvu8QZqb9s+mMvKV6WUQRV7K2R46YbjMXTTJw==", "signatures": [{"sig": "MEQCIFTLVWr3GWKMPXA3OjEsVX7wIKfts1tHo3ZJgCQhOy63AiAkatfT2PD1OpAczl720YWTMorkfwf/vX3PZXbtAQqPkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53088, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg30AvCRA9TVsSAnZWagAA8zoP/Ar/avO222g7hBLZwfg6\naElzB8iEZGIEeFF8F+n+z5II10Gx0x1CGVuodXvtdbry31lVe7oRIO4o1k5o\nGzakHydk+Ww++8hhf4EyEhLreR5NVJGp9QRB9yXEMrwC1p6WgfOz4rzyr8Yz\n/35R2SHE2113McBpQDIpvqHy4pKRsS8KsNFhgAI3BqW5W7Nx0gWZjDQzmhiy\nhSandpjMvJx9QMMxE4E3HFhgKb38JdchS98Bwf0/YlWQA8IGpzKzFZFMGCtY\nf7FMqbJpi1TquZq2PvaiFF+CKb+omV1/CUuyiRramjHDKm1gHPZ5zzP2OAxZ\nTY34CAMcI0t55/8657L0T3Ksr/ojsttvhMj8hi0AjqGID60uSC0eEezWY//z\nDP1t42FTuQNPk2wQ7ngbAHBX4WU45/51nzuKB6txZe10x6xJBYPWaqJLKneH\nsvAAlJijNlKm5oLPy/HW/8JdrVhMLt1eTTLOV6UcQ2XBKyMtCOGedxru3b3y\neqQIg0Elbp6uIQiqhaaEByG7ROdpnxbmc+uG3Bbz51JjcQiX+fcHggJxW9+I\npGDAPvum7BFHb9itM4EHfwnVd9T4+LjxR40LLmFJV9zpnjNeJ9ZjmPe3ELkj\nWt0i7G2xO3SrRV0by9gYPytTA6JoYCDf/nmhts8dPmfkUE33nJWJhyO/Yymg\ndtKG\r\n=RHCg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/yargs_16.0.4_1625243694984_0.46612195706504145", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d46fdfa13e8760d2fb36fecd2e85de1f955504f612121a84f42188b00876dc45"}, "15.0.14": {"name": "@types/yargs", "version": "15.0.14", "license": "MIT", "_id": "@types/yargs@15.0.14", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "26d821ddb89e70492160b66d10a0eb6df8f6fb06", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.14.tgz", "fileCount": 5, "integrity": "sha512-yEJzHoxf6SyQGhBhIYGXQDSCkJjB6HohDShto7m8vaKg9Yp0Yn8+71J9eakh2bnPg6BfsH9PRMhiRTZnd4eXGQ==", "signatures": [{"sig": "MEUCIQC2xuQRpL2vW1BN6srXcEyEAIzplj/24/C6Csiy9YenZgIgAzzC9pCs/L9QhtsPqA3bt96xo4eRMGV1c028i0tjxPk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52547, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg30A1CRA9TVsSAnZWagAAuAQP/3oJF3sx885bwL8SjtaI\nuXHeeCCoGwWSoaoT6rg8Avwe9LFczd+IZ2DOvF9y3JFIsVaW2oPq1+g14T2G\n7lA5nz6u8zqgDmd1PJ0Jo99S0Vpc7FTixwBL+dSQ+ZO6mNWEVdN0aUKcuk4S\nZyrtvNm2UbKCrPZdjylYpXtzYduyR6ACwJY6MRwsYmr5FcF8q3rt8GCxvEcg\nHR/WtQTLpJFJmkG2ig1DDVeSxFy/KLarhHOXz5Uq63aWCe8QcqXrXRVWQRVo\n8TrJ0PewgJzx1ZXYXYiUTX6Xb6GCc15JzsoHtWP+8Mcd1LYFxXB+L4Gv1HQD\nOmnhbN7xx0kMBM7wd2YwlqswSnRQBDMOMGZCtVckn4EPYSz+4OKrvqpDug3A\nsHOr43Pf17L+CIxQXKMigOHpwcAGrhvr1m3uzYFzLxtIEy32e8L30Ha0UD+f\n+RH/02iQNgO+8TbC15ZxnOPzJ8/DE+GdAs1LbsHsFuC4YEPPMb4rjJynA3vg\nP6h4UwvHj/jjCF4mnDMtTxi3/cx4o67WpLL2zKW2mLvRNbg9AdGk3377HyZy\nEEV5X/+dV8zyNIj1e9YUQiLpmxRRbg+pwpbX454AY1hchzZt3lB/u5Bsz2Kp\nJjNvETlp1ga8bbBMW2XWC2q6+RfF8sfOaExB5DRKuG/7Fnu5wF/cgz0zTTdj\n645r\r\n=Dcu7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/yargs_15.0.14_1625243700987_0.3658258559031149", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "08f18db584d12815700691202627482987b3f369093c020e1539deee222b2a09"}, "13.0.12": {"name": "@types/yargs", "version": "13.0.12", "license": "MIT", "_id": "@types/yargs@13.0.12", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "d895a88c703b78af0465a9de88aa92c61430b092", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-13.0.12.tgz", "fileCount": 5, "integrity": "sha512-qCxJE1qgz2y0hA4pIxjBR+PelCH0U5CK1XJXFwCNqfmliatKp47UCXXE9Dyk1OXBDLvsCF57TqQEJaeLfDYEOQ==", "signatures": [{"sig": "MEQCIDhZJ0Xj+fB0l8KpGsW7hLiBZbhc7NdQfF3X9hRZF0i+AiBZXR7hLHQr4iwS8hl1/F6rG6q5n4rQML5DgavN9tACMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg30A7CRA9TVsSAnZWagAAGUgQAKIeMlG1Y+NCrmFcWJJ+\nsQIc5ODMxUr73FvzGPCgym8kZSv0dyRq2zS/mQpEW9PSNXakPoDEcGi1hRDB\nHVMvyyailinZ4yk/R2jZobVRm6w4+okkiVw1BnI7jaB92LtcU81g3NcsQw7f\nwQ7n36LDqVFMDvuK/iXXP2VxCA+vBE18dLOR/NMXLDzEbJE3VLLYziJ9MMFc\nGZwDOVrSSaDWMQuidx33U6mhos1iOSyRWMtTsHfKfmCqgeJezlORHZS7DnLx\nCoGJPPjthSVzmwoeboD1IPAfAGJJ2DaiGICU05HANib3nsG3eQT4rOHLANo6\nreGMvSTRVzmW858G7vjQXXEABUVt8kF9CeEODMl5J7l3Gt9hnkHBy8/fZZBw\nK1GZsTBtRQEvv14G+Xid34TCxdAPuIoCE1hYyBmOGPGFH2kAtYgt0r2oCcVs\niE8tmSonk8Uhyr51Qrjhh6uAz8Yux9pVWTMcK+zx6fhb6BSCmi18UXWbwBZJ\nLTqY0aP3AiIf0LCHFEZbSxGeJDiCXCuuP0fjRP0VOu6Ik1hdQ+BErFZWmMsq\nDoyCVwNCseAdenj44Rb+UIJlrJU0rl5zceaf7zhJdIkU10Hx7hFnrs9O0Rxj\nNCeK88Ju5+q0vULXWI8OhbcrqJShqVLgqWDmLMgU6k97qv7i4ndFPUIib53Z\nUmou\r\n=Goye\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/yargs_13.0.12_1625243707461_0.22787704893337368", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0c832761c9bdd2e9f6188b74466b3908a9fedb41ebf63717f183b4d74a4c0367"}, "12.0.20": {"name": "@types/yargs", "version": "12.0.20", "license": "MIT", "_id": "@types/yargs@12.0.20", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "50c390e95b8dd32105560e08ea0571dde99d2d8a", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-12.0.20.tgz", "fileCount": 5, "integrity": "sha512-MjOKUoDmNattFOBJvAZng7X9KXIKSGy6XHoXY9mASkKwCn35X4Ckh+Ugv1DewXZXrWYXMNtLiXhlCfWlpcAV+Q==", "signatures": [{"sig": "MEYCIQCxz+ejZpC8+wIWNFuqbKJ4AJNfiZ+o75Ntf9KHB5Q52QIhAKPkIOoyLA2UPdLyADDiGjcM4Tacu/FW9kqizuTXSHzW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg30BCCRA9TVsSAnZWagAAaLoQAJOm3GsFIWPaWntRYTIz\ndOe7ervXzjioT+PjQ71UWWTz4RgULx6i7+kPpZOXf7W7hzCtr43RQa7ROYLn\nE9QTi12DLqLrCbxKMFoQPDZyAhs1eYN2GlKCEhwntqfzhIDEp8x6DpN8o6h+\nKdSh6c+m39bCc02Fa8GPyvFk6GZ+qRxIu+q2tz72zDlHN8SLUifw3aYMg62D\nMRg+Bm1E/tX2lVaOYe8gX28z9XgAoCiCgIFtNrwe+2ZDqUNMRPuUk0Y1udV7\nQtiBEie0iQsacKuw8JnEFvMDlTiyu4L8/fJoQrObLVwmYEYdjV0NzlKCwuXO\nZCHVyyfsJipwrm8Uc/srUfndU5p0n2AnfdXPzycvpehebOf7aWCzTHRwyUqL\nvMbp/203ZnwghLJYWoRu8FdousBwsq6tYACor/wDxZGYH/x4YUih7ORjPJQG\nE4SBBCjnuQQVvm/S5YLnMcb/HDUhUcY82r97bN/0VoCIB+RhHfN7jniz5u/5\nluXwyYbFOafpghYLXUI3pnJcLMmuoBk3X1NzYpfGPpyuXBIp91GX8k1fbyV5\njFhrC+GgJN44TabVwa5c2wvDhhBsn2oEplb9k0YIepOfo0H18q2shJuNsTr0\nRD6QUtN24nKmPIAk/JhXtFKDAMn+UkNlxc1R9Y9LlRffHZH9UZUWxp93eLO4\n0SLC\r\n=55zm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/yargs_12.0.20_1625243713825_0.7548508453859284", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0997a8201ea9bd6a87a4d0ed3c1748ba394fc7521b4d9efdb98abac5cf47c93e"}, "11.1.8": {"name": "@types/yargs", "version": "11.1.8", "license": "MIT", "_id": "@types/yargs@11.1.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "b730ecb2bde209d12194cdf8bf9f12c4bd21965a", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-11.1.8.tgz", "fileCount": 4, "integrity": "sha512-49Pmk3GBUOrs/ZKJodGMJeEeiulv2VdfAYpGgkTCSXpNWx7KCX36+PbrkItwzrjTDHO2QoEZDpbhFoMN1lxe9A==", "signatures": [{"sig": "MEYCIQCUERMApiDwuLSplgqbjediI0xAnJorxdk8uMNXwnskgAIhALegCPusRJgZ4t6tpbiab7Ane5+4iPIDjA4QGLxnGeMD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg30BICRA9TVsSAnZWagAA+zQP/1xduqcfS/LttgN+o0sT\nUOmDIFCDqGO2mTOxV6Kx0FBDwbfExWQUae7lWGB2/csPU6NtQiKpLK2j21/H\nffN6AcCcDUdL2GDke6i7FO+pl6radYHVAzo6TTEJueiMdOi8Z8Qm0eKNn4MA\nlSqNGAEHlofwkNqqFfgSaVa3Mqte0xBR3FyERi/V1STeSUrv9iMDvpgFb2gl\nq/DBkMZzjpwUP4EwmuVl3lF8OkT+HrArnUvSnLDn8Dn8fKuIRbPqSX3zK7+Y\no6PRYA227vBYBDTxxd+T0v8OgcA/5pP0DkLqgztcgPv6oTgHD9e7fix4DkUx\nc7u6Q7QIatCvDWujwxqxCRvoofVXcbFqMokOHOWv4j4UaF896IUzIgsWFffg\ny580lo5/OpKL5wPfVdcOhd9eCLgpMtPmIFBhtUGmm5bylVAeYyXN13/+ErdO\nKUb+qkrDOzly/0K2Dtwhk+m+i0Xc5kI1u8PRKH5RpeuZjWZqkPbmNigqpmrK\nvNtZViATs+zEt/u7YRCyXI9tLwqDg+QdisSnNAhoSzt6Vx4T9f8EMcxQZ/ct\n8U+KLz8ISAXTmZsobaFz2gJedKVfWz16Vj9YbF4prmC5u+AxcIVDBHOeOBYQ\nj54mchwwtqpd5moKgl2nJBGQsgR7Na2qIi12tk/ojCTg+IIs9q0qk/kf2a8r\nxqro\r\n=Qmk2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/yargs_11.1.8_1625243720146_0.36310626894975373", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b6cec4d02c07cc1fd538802d3542e1fbf20fa9d1dae2b6fedcb4ad464a6b1941"}, "10.0.8": {"name": "@types/yargs", "version": "10.0.8", "license": "MIT", "_id": "@types/yargs@10.0.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "9c95c303a287cb2b607704d5ac02a320999b0632", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-10.0.8.tgz", "fileCount": 4, "integrity": "sha512-yOFNLWIPWJOqA+0WNtLqhq9Qwg//CPxeetf3GoMi1iSYupeb+t1VUjuO2X4EHNonTX7PNMIOVsocJotaUeHppA==", "signatures": [{"sig": "MEUCIHiafAs7k+1EMFYIghmGLa4JyXSkaj+ycppKJPZ3GqZ4AiEA0pkHlM4bOtD91DOaAq8jA3sNxBxR1KGGmXKT1FILoeo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg30BOCRA9TVsSAnZWagAA7cUP/21r59p59kr8Ym8fCmwo\n3fW/YeZ09XOR67a0IYg3uo9sVUoxl80zLUGMzHlfZlagekmE1ZlegrtAV9V1\nHBryVviSZw5r74APcy6czJBL1xiuCbdEQ6CoO7weTuzbg/R5osehh3b8374p\nwSb2OlaRKDigGktAg986nfuUl1gFZta5BNA8XMFYnn5uaVy/8y/5/FWbDsJx\ns4ZCNSTGyas7bdETKUvwErC0LhK2ciI3P+CcrnIlSHnhjihBUb02sj09BBc3\nO2oIACJwlv7ye5+mCE5G3yx7bJ5uWMhkZatk9lCKimL/6Tw2CgGMiKXW47wi\nRSHcx00eC0ADLPV6r+w5m8Gk1+cqXQ25JsT422bjaXx3fX+5boRBKrbYAqrV\nLIBKDarM+NVkSqSmpFW8K7auZZPSovZtyyvFLm++USXrM+QZ8f/cVlj8EShq\noJw9rNNB6ldoU94CkelEFzq90pnpvqNm4v/EPqnwJpD2mIFCGLyXzzEyBpg+\n8dJnrK9asPSSBkc/fz0fWjU12u5p0L3EjHJaeZrXFenrW4nIKc6Q6Db8rG66\nBS//nL4lDDTRtkWrJRvQE5DflLzlMP9qUaHvrzViwCW1PwzkC78r/3W3Awxp\nyaRQAP6tEFE9LvrtXhj64i3I5C8zp9EcmAIQ5Ze4EzbCjtvxz6XfidLALTTB\ncmVJ\r\n=qNjn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/yargs_10.0.8_1625243726116_0.7429468434738669", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3f5a502866dc0b8fff37fc791726708b553961c82c36c1a9ce33c345f3cd0bea"}, "8.0.7": {"name": "@types/yargs", "version": "8.0.7", "license": "MIT", "_id": "@types/yargs@8.0.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "584309625288ea73203b1a84a162a0fe0a3bc7c5", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-8.0.7.tgz", "fileCount": 4, "integrity": "sha512-rbhOjgHktbR4gZu211AnKXVT0daBHeLrV1PThsZ6ZfYzRkKV7dktx/7rmENqQkCGCtb3iKee6KT1tZvbRVzjwQ==", "signatures": [{"sig": "MEUCIQCdhpgkptyei/+Fdo1/+Bzknb/mc6XzZQFQAbT6di1n9wIgQcF0xYeZ7oJjm3u+Ovh39T2WhaFLZ1MMiJGtySL8bgE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13389, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg30BUCRA9TVsSAnZWagAAHv4P/18W7DoEU9Rzyl8Wke7N\nyu4v7YulD4ozAV+HGhAR0+DQGSqQ0kQh4hVT7oZx9Fnb5GZz/97fopOP918Y\nKooneP2Y95WCZwx+/bVGEBIAUNA2u039WzphKu8t9Go8Zu6hcF1Fg7CsWDjv\nxAFrfn+xX/GAzuobhx62+yKGyOjgfO9FbeFZfdfEaWiB9pPsGj6gbTB7uDUO\n9Av4aPkXRBYODX3NRl31TxVpW3uPQRUZ+aGr76d3jQCadZrS1CSAFxDNFdeF\nPB6Z3RmPPnUGYspmdwzalndN0BbA2JNGDB5PvsMV/YIRozZW1EuvbftEKlxk\nQoKBveHShiUNJAypXmsuteSMuAnVUh0WaPgfOy6XQ/eM/1tbHRoL3brBj3Fq\nkgpr2eQ7C0Mm0X6XFKi3B2DrmaDUaW89uSYU8Ey0P/Yahbr9qCZ0mTN0e4gf\nCznsBsGlwsIqSexHKY9DmUR/l6f/EgjNXpr3rQg87WWg4z+JEMvq4Cvvm3Nv\nbgh6n2/RK1UOe9otsTXbNxTlL4xx9qdFZzLG3zRA1eNbYvlqdeZ52WdpLDC4\n7VTQbZOmSZ6PYY4C7fAkvjSChgYRm4K458Bula5PY9y5WxFXzkAiBJUqbBDA\ntup6MDeeW5j5Yke0CZW3Y1B3rn0lCM/e7ZLpggHvhtnVkb2+m33DqtNgJsjJ\nPVfV\r\n=Bk7G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/yargs_8.0.7_1625243732060_0.8410582109101858", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "2720b461b9d177415c4b457156d16e4e9b42de676e08dddd5aa915da16aa30e9"}, "17.0.2": {"name": "@types/yargs", "version": "17.0.2", "license": "MIT", "_id": "@types/yargs@17.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "8fb2e0f4cdc7ab2a1a570106e56533f31225b584", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.2.tgz", "fileCount": 6, "integrity": "sha512-JhZ+pNdKMfB0rXauaDlrIvm+U7V4m03PPOSVoPS66z8gf+G4Z/UW8UlrVIj2MRQOBzuoEvYtjS0bqYwnpZaS9Q==", "signatures": [{"sig": "MEQCIA1GJQsCNLY/BG6bfxQgumxA9izQMsdkxuZAeGjXINBcAiASNyyJYXja6T6DMB3bpKUx7AvrGMht2ekj8/9HeRrJWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54182, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg43NHCRA9TVsSAnZWagAAJV0P/1wbuWFKqpMm3Np4nP94\nFZxNkB+QXjDCAlpX0SJhE/ImGpDWb3lJmUDPfDlvSMiWH1eqMgDBEMn5v1E4\nzh7YEV8f83zO83HODrfrmlMChmDhfYZGyj/buj7Ug94IIpTHo68aKGl42BO5\nj8i+eVEYxpDDpIzqyYBI6sKDWjA5OClkUnqlX9SXI4FWMYRDhAv6fXM8l6Tt\nyWXoEXhV4ktVBr5u2sLfMMHtOoK6HqDCZ5TG6kWthjCSeZWmBwcVVtjLOv2P\n97QT0KUVKsQC4C3HXXT8sGQGVfhSfcrIECs+BWGz+Zln9cgRE5XANyfZXxRu\ndTUs0GSyNEEV4GU/oPXuKZyqP/Gg83OcDLdhbdL6SEbO8bCas6LowqgLEZE5\n5gFx2kFySFtP+P6kmm/E5+yfN2B7rOWrUqwtgSBRbef8SK357HBIVkOmW52s\n96TqGRCXndPKIJCJXlqyfFTOLw48h1RRgZEUNhdNs9qFmdpiwd76Ex9m2JYb\n9EjcNUUrzfjpg+IA899hPwVnmlDqLeEOwZGdNwV8aOi9eETPhdmtcOa7JrTy\njtLjO7yY7NPMZLRfvQ3dTWU2v7p+fdli5qHy8w19DVBgZgXQzZ/YjoC7zsXN\navwHUe+BTwvR0yHGE43700IQzHTolgGWTBm6SygvBDpnZmnntvfNG00mZdgJ\ncrso\r\n=6ldF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.2_1625518918954_0.8791403781667622", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "bea5322b451a6df32fb3dc97be6004fbf9d25c264c4df7f051355c27c5fece8d"}, "17.0.3": {"name": "@types/yargs", "version": "17.0.3", "license": "MIT", "_id": "@types/yargs@17.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "e6c552aa3277b21a8e802019d03ee5e77894cf27", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.3.tgz", "fileCount": 6, "integrity": "sha512-K7rm3Ke3ag/pAniBe80A6J6fjoqRibvCrl3dRmtXV9eCEt9h/pZwmHX9MzjQVUc/elneQTL4Ky7XKorC71Lmxw==", "signatures": [{"sig": "MEUCIGSuIYHLrC0ZvltjoBM1iS/UjGrJLrAx3oioQhBpsaV8AiEAscTppE40c6cVA8XvRdt28QQR2BwEZ0PYgALEipfxzl4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54534}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.7", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.3_1632251035670_0.10396347076889345", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "caeca6833cb97c4ec9d2b51cd05e5ea2f57c145074231f9c9ae3bfebaa888c77"}, "17.0.4": {"name": "@types/yargs", "version": "17.0.4", "license": "MIT", "_id": "@types/yargs@17.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "d7ad5c311aaca3d7daebba169e1ecf35be97ceee", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.4.tgz", "fileCount": 6, "integrity": "sha512-D/wihO9WFYqwsmJI0e0qS+U09wIQtYRSBJlXWjTFGjouEuOCy0BU4N/ZK5utb00S5lW/9LO7vOpvGDd8M06NvQ==", "signatures": [{"sig": "MEYCIQCpsn+2qTCETldqzbrI1vCq8V+p4gOs1igvW++XSsemjAIhAMRA0bZyw8bKUoKxcO4DkKOjnKU7XdCfjIHS6WGWIVbT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54550}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.7", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.4_1634259689831_0.5144600972538924", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "00804606ef472de36eea162afd43480165f3f6367b9719816b178374a4decef5"}, "17.0.5": {"name": "@types/yargs", "version": "17.0.5", "license": "MIT", "_id": "@types/yargs@17.0.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "1e7e59a88420872875842352b73618f5e77e835f", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.5.tgz", "fileCount": 8, "integrity": "sha512-4HNq144yhaVjJs+ON6A07NEoi9Hh0Rhl/jI9Nt/l/YRjt+T6St/QK3meFARWZ8IgkzoD1LC0PdTdJenlQQi2WQ==", "signatures": [{"sig": "MEUCIEBBxxiLep2UU0zALn4HcAUZcwivevuFNy7B+NWszJmsAiEA35ZV6Bg0Y/MJ6e6cqvqbLqwKD9COkOFbECLqgOI/lQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55346}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.7", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.5_1635552119568_0.03327656087067221", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8d84566da42c63464259dc0b774f4380a1934b31a71ef48878840e2c5a234cf5"}, "17.0.6": {"name": "@types/yargs", "version": "17.0.6", "license": "MIT", "_id": "@types/yargs@17.0.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "675136e8ec0009f4f04aafc1273a9ca6d2a9e368", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.6.tgz", "fileCount": 8, "integrity": "sha512-6jzGs5Kb3Rqe98q8CPKPABvzgnueEzGMLd8aT4FD3HrtO8Ju7Efnb8pyUSL22IoQ5WfUH7eMVQ2AwjO5EOIXFQ==", "signatures": [{"sig": "MEQCIG19FCOrQWr4oePKmAZG6yAZl+uEKIjghZitAtWF1YxtAiAkYly0e+KAiTsUKuk06e+pkWS7XAtREEW9L5hitr6sGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55891, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlWbfCRA9TVsSAnZWagAAfxMQAKO2xc2rIgkEbx6bYNcx\neV9uy6igp9dDYNPhhYv64tYmu/zhZwKMp38lfgwk2torVi80TNexwIRuxZ+U\nh4HGpxqqy+f2rUgEH+kW0UMntmJt08fixeCsP8LQ1/K47I12kRy9+xYM4v3Y\nH+pb77a4zgowiVjJagFTij8NKssR3MI3doMLA6+tSoSH0hsQUGZGk02YQS03\nW0qxcco+n7W7NmuWdekQGsHCYSCzSzrc/JqWQMS+n+zbXEpBLH8H1uJnVLsy\n+Gjcg3/J/XkkmiyRV9LuZOCENyXROdAy5fAk0B+ud5TcaCwaAAYhHRv6OPxu\nCilyjOFUxVjsxJQ7MNY0ecOxpGaRvi0AMngaTDTuExkYtUlLqmjdPtOabED4\nIrNjo8k8hFo02lfs5Nk0nZJ96hzwMgDubponCzUCCBNPimmonuorY9gl19Fj\nv7f7Fh310/jM7Ca8IGpoIhkEbym1ghGJjgq2WhTamHnY3UaQxi6NRK4EuDCL\nEMAppt6/yydUpWQc8diC1gS4zDPD+BCZRY5+AGIlXUaoo0s/2vKZZuRx9BKp\nj2kxj0OWlopFJ3kiGfjQ5aqhMFLEDWpwpTVUZ9y0HSsGI54I7gOYew5mqZgx\nv27t8dE0PirtiHcyWZpybT91O3r6bkbzKjSPVgddaEQZnITkY+SDb6NMJU1V\nimnl\r\n=WgjR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.7", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.6_1637181151195_0.031553928354273175", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e048c7723aeed31444483f2a810bc6850072174fa89f9f750edb247bda6f21e4"}, "17.0.7": {"name": "@types/yargs", "version": "17.0.7", "license": "MIT", "_id": "@types/yargs@17.0.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "44a484c634761da4391477515a98772b82b5060f", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.7.tgz", "fileCount": 8, "integrity": "sha512-OvLKmpKdea1aWtqHv9bxVVcMoT6syAeK+198dfETIFkAevYRGwqh4H+KFxfjUETZuUuE5sQCAFwdOdoHUdo8eg==", "signatures": [{"sig": "MEUCIQCT86Coht4feQ/SPhEdB3OyBymLMA9OeK8/+i24QNVOgAIgAPNKsHrn+4T+6uBBgTbANV1UCT29wuAcQLIZwnN4G90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56136, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhldc7CRA9TVsSAnZWagAAZeYQAJnR2yTpJtN31S1+G+dN\neU2RrObcvKOT9xnu9JHakhBROlolnZPzsRtQKhl1LT9QTuhl7q1tsMPjI9G1\nfs3X/RAXGpHyRLRcugud+Q8RofUb/3brKcR6GMUXWpcqs0nb6GKtHC+2E2Ol\n9iwDTXzfcuFgQsH2QGt2DZFXYA0e2jQ1x+yu6lspNSzkvqsMircK0Hz6JZ3h\n6mCb2R2KSnqLD8PGpescaymj+zD8HRc7HkmA/Bd68DtPiX8Nej4ddQLx1A2U\nBS9EQl2mkYLOcpfe4D5ek5LzDWkIcmsBJ54hLpj3itOrXzA7aG1UzS3t94M8\nM5fvZWs/zieyQ63Li8T8ZfWGfdT9Wt93J+cLM5Jk/9pM7wMlen0CT6h6qiaU\n4/ht7KyCV/HB9pUUKgHRhAGd4weC3FTWwPh2YHfV0kv9HC2AQ4xIbtAVwiR5\nS/FhCR5TJX7CZicCgHGNpmwOyBoRzNoDAJLaGZZ01kULiFOcJJeKIBirSseF\nEFnSE3qCOPRy+KIjHYwO5jPkYr02nYOhkgLhNHbFvJShZg+4OHZw6yh8pTHA\nKfL1jcvpqDkgi+Pe+yJjt4Ok5tJUJ320Fsq0GY0zhI2pXsLHm6GhHosDvDnG\nOW2jpiXyyGzbrsCPrIkaP1VS+lfeFpzQA39CJTA7dCzOV7P+woO2twuMgXLd\ngDQ5\r\n=7ZPg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.7", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.7_1637209914897_0.3960816220448933", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b0ad85989b238fef5b94147fd81cb376b2d26f919fa831203ef522a59ddd95ab"}, "17.0.8": {"name": "@types/yargs", "version": "17.0.8", "license": "MIT", "_id": "@types/yargs@17.0.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "d23a3476fd3da8a0ea44b5494ca7fa677b9dad4c", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.8.tgz", "fileCount": 10, "integrity": "sha512-wDeUwiUmem9FzsyysEwRukaEdDNcwbROvQ9QGRKaLI6t+IltNzbn4/i4asmB10auvZGQCzSQ6t0GSczEThlUXw==", "signatures": [{"sig": "MEYCIQCDrzd6sfkKxKxMK1ISdswKNTi+iaaHBIKHPqX25dJuFwIhAMan2+rZKCAAKRY6Ekrz8R/oYSYMzEHKYAGC43Fxun77", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107140, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhw9GDCRA9TVsSAnZWagAAeAYP/A4HriBi6gnypLQAvjik\nrIolNsnkujHpXEdnpfKKZ52QebbmCnRt500dxn/4dprRrduGHdUEw17r/6lu\nY1VkcrwXD5sdd9mRfXErSZ1s1VjJy0kqhdLh/5/y4PGe7nc2OD3Krcd6hEAY\ne3BaRXyrZGCwnFAgULnqoorMGO3XsAPhAlT6su8u5KgXWlNDcAkDpol93zvB\nrtAQbIevNKpAv5hDifHZFdWDT4a3tlVbXcsxjr1uB5EPW+boZb9bCtKJ6fNx\nktrUgTcxQqfMG/AETDXwDL+wPcW2n/H+NDvuxjcorn9gE8yC3cnX478Y38RW\njju9uM4h2GFiTaMBwQwjntysT5AHspUZ1XILSLxU1mpFoj4kJU1ekPZj2nUo\n85znIdk0YYbpoPm4pYpfpZdk1AoRJlUyaqofhH+C5IZZKB/Bn0UgtNDtM3ik\ncabY3dqAbmvi/Xu2+s0jOQ3zJKbatQqbrf43CgSZgKne/fBZaoMSQn74tuNS\nh6ZdKarYxFR6OSyF3PL5o6FZVFGt+4b8kSzOcqhVeKnmFREwsw9Ok4QlJofP\ncDOU3QepYPiIaxRh4oKowH/+NAhfiQfxGvtqCbDve8S5v7PR59LzVbBif3oR\n5zoB9iC5iOVFvL1OuGl7RUOAef/9yYLDodTsCNeWUOPIOFIKCp5OBf67nYiO\nxs7H\r\n=xrp+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "typesVersions": {"<=4.1": {"*": ["ts4.1/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.8", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.8_1640223107156_0.21239918759231968", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "2ff81d51a7846308736765fd80ef385e4d6901ea5367cdadcc370a58a4cc1bda"}, "17.0.9": {"name": "@types/yargs", "version": "17.0.9", "license": "MIT", "_id": "@types/yargs@17.0.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "f1f931a4e5ae2c0134dea10f501088636a50b46a", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.9.tgz", "fileCount": 10, "integrity": "sha512-Ci8+4/DOtkHRylcisKmVMtmVO5g7weUVCKcsu1sJvF1bn0wExTmbHmhFKj7AnEm0de800iovGhdSKzYnzbaHpg==", "signatures": [{"sig": "MEQCIHmkf7tXoKcj1TxtZR5k6+BCXRoGPXft1c5xoYcviyevAiAy03aSLwprCasqUjb5m9LJhPi807Eq+wtlwUz7LxBWUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107140, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiH6pcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptzhAAgCPc6jKroxm68mRZRgVJiFKbbdh3owaCfYgWHys4CO8nTMtV\r\nmqzi/mIcbrwiHMU2UyR0H1dUhTtX9aiCSMUN+O9qEvGsNmASqGWwHVW65g3K\r\nGVHZ9vllFt9uMmZp+0yCGp6Z5fLbWZeY9KQD80y+jHiRFT2yh1luLfaXWes3\r\nceWlrTR3zNrWkm27TCro5yNyMULIIq/AcCWNOs8030lgHr7PuUhhlvC0ZgY4\r\n0NXR5R8c9yq1b/soQ7AJx7Ujjzk0EZQg47cujM3jgmBWoQGfs20wO6gKnDMl\r\n8YW5iM4vyWTUIIJJGvztu4kATUAvPjdRnqB2EWs/i5c8Dfig9xVygtOkpdOn\r\nZIOinKsWyCgVfUOMDJm4wLjGvKmhbJ8mPY27q4ewDnVtPy1VaYUSeOpZA96F\r\nj1qf7OCa1kPVtqB/tDa+3Ct5nWYfw/opGUPKnVAbbyzacil5oDuO68Ii2kt6\r\nrMe3SoSFYDWQ9KBswsNyKUbRwiKF3iLojg5yqLrOGVNfqreyClP77UG/CXb9\r\nWabul9vtjaPcLg4y9wOLwtrlfy7YbPQsQKlBQ3/hAfevFv+/1UrDJgclJvMw\r\n4eEMctnjvOeuvuV+96tAqM24mpgup+jyBQ2o3OTjNKePIYCZAq4grueOYx12\r\nBkPG/0RTppprPAdQzFTwJByXoEnCozCogPw=\r\n=r+2V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "typesVersions": {"<=4.1": {"*": ["ts4.1/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.9", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.9_1646242396266_0.5720998132526267", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "50db1915bee528c6540428d531efd474ad1f6dfbef8232e90b1867909bb4e0e2"}, "17.0.10": {"name": "@types/yargs", "version": "17.0.10", "license": "MIT", "_id": "@types/yargs@17.0.10", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "591522fce85d8739bca7b8bb90d048e4478d186a", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.10.tgz", "fileCount": 10, "integrity": "sha512-gmEaFwpj/7f/ROdtIlci1R1VYU1J4j95m8T+Tj3iBgiBFKg1foE/PSl93bBd5T9LDXNPo8UlNN6W0qwD8O5OaA==", "signatures": [{"sig": "MEQCIEvYyRqA+naODtfO22cyVX5iU/fzeomMiBzC16MNpjl1AiBJO0SfbhG4MLQQyyU6aRiYFB+AB9UujMqFaVBzUdpQoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107157, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiM7bqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLHhAAozeTEgpOssXrTR+iSs/SHYcqMg0LxRN9Shxq3C+59QbqKp1t\r\nK0YArHWTZr+MqvRppNxG8denUGbmDhS87XNlb5IN/b7ZvgniqIWxKGHe31PY\r\n0/zKRVzehbOdKXTljQwvPH83dhxKkehslB4FgCTIxTcx3pP10PvVRKzHG9kZ\r\ne3mj/OLCdyc2sv7vKnZUnvAarKYK78H3aob8s+248mICQbxTw+t9bOBdrr8q\r\ni0ZSm1mD61NV7Kh3PMuTxXxtqH8ouLa9oO0UYsiYX2b2YLZzrvPPEbKSpq6N\r\nZ+6EdxiklB+nP+IJLs36IxwDeNDFuXyV7u53qYqkFGXbbrla28duGzUA1QU2\r\nfWVpUyMjyHu81GnN21ssKALDjGlNlF+Nj8+HjwmUhe2vNKbo/TBJvTLyAndy\r\nR8jqdNpfNIZz2MmwSPDwNrcQ5nGrA4D5YZfmdgeLTloBHxdCYg9TrDQSJruH\r\nQZG3OFAGTsHyIiNVHEJzRnWwVIlWL7MpZ8qY0e440xa2VyGUVHDTdLunGiLq\r\n6/pDeNeuuA9SHhkH+4pI/dbS4nsrd3MxByOuWoVn+1owoplt+btqhgZsDXmP\r\nLnxLHztrsczfZIdk39FP4i81GApgetEy+wO/HbAtM5Gh12pC33K92ZX6ZHuE\r\nPg7IpGazs16l+kGm1cCwZ/gUulFZG4/DYKc=\r\n=af4Q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "typesVersions": {"<=4.1": {"*": ["ts4.1/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.9", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.10_1647556330535_0.2612207675300302", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3a3301ae1e830cacd8e2ff90d3e54797b81ab3d844e13c4f0c49351248d8d002"}, "17.0.11": {"name": "@types/yargs", "version": "17.0.11", "license": "MIT", "_id": "@types/yargs@17.0.11", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "5e10ca33e219807c0eee0f08b5efcba9b6a42c06", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.11.tgz", "fileCount": 11, "integrity": "sha512-aB4y9UDUXTSMxmM4MH+YnuR0g5Cph3FLQBoWoMB21DSvFVAxRVEHEMx3TLh+zUZYMCQtKiqazz0Q4Rre31f/OA==", "signatures": [{"sig": "MEQCIE1zkddO1DjvVIls7P3oyFtx+WFX1gx4ctOcR/2qXjRzAiAXLkctOBu4nLZnf+BsYUNkcSxQstD+j6b2otUzTK7J5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106912, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7GwYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoEeA//ekmyN6xJZwB8NSXjMw76JhrilPyHX0qj3Kb7Kn5JgN7NtVWo\r\no/3DPkpaiuL6MHTWLWgqmOSCPOwIa0W1hV0peWVjkcF1uG/NqAsC2l5jTSFR\r\nnD8IpXTRotYU8gIhRkvcLGX+gs6ElUU+m3HikKod/HzetP9dI3NnHmoZbcyf\r\nuqrT0fZzKDg1mAulCHmEr3Xk7mocJvR3SIj+qnsoY8ujR8OukdrqFFyzg6NU\r\nOewYSVrCttN8lo0VMNb3rOL09CiuhSbuJUaeyW9ZRufVpc/hiRchWqY8YT3u\r\nE2+h5mk7YgAUFq6fsv/ztBlV7S3d/+kBbio0TJluAMWlV7gcEWPCKqjkbrbJ\r\nrTrsbF+W35/BM3Mtx7LPOj9hc8PZs5uzIWARWT0QmXDXyngv4LU2keY3QWnk\r\n6ZnxIfv3ftu5mdgZp8aY4x7aTEONi7+FJfkbwBE5xRqKJsCOmwICoea08/8s\r\nlaZPnHqgdwLtsCkzExeLZnjFkFzma6AYz88Q3wFJYT8FGFp4jUs1Ammk8DMp\r\n1oUGNlVsdpWTZl3L0a3RsHJfaH1Qqef1y0evlNwqCbghunqxJ8eGjrZUbI0y\r\nf6rSYIDjYh5QvcD4rU7xfEgdiHLhNQ9SH/nBM+nUoTx21GUZN5YR+vl9/8MD\r\n7IuZVERInOsuBH7m1ZRBM4Bi1+aOt4pQW/8=\r\n=jPLF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "typesVersions": {"<=4.1": {"*": ["ts4.1/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "4.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.11_1659661336225_0.18478492969967442", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "536aa258e1bb9b6ca98d3e7249c64cc5b3cd850d6cfbe22cdb76e1ed2c43552a"}, "17.0.12": {"name": "@types/yargs", "version": "17.0.12", "license": "MIT", "_id": "@types/yargs@17.0.12", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "0745ff3e4872b4ace98616d4b7e37ccbd75f9526", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.12.tgz", "fileCount": 11, "integrity": "sha512-Nz4MPhecOFArtm81gFQvQqdV7XYCrWKx5uUt6GNHredFHn1i2mtWqXTON7EPXMtNi1qjtjEM/VCHDhcHsAMLXQ==", "signatures": [{"sig": "MEQCIEo8ORzce1RY4hiReE28a/rjtYkN6UR0tvrlGmkcGwlQAiAipr3nO74Mcs2B2Ur3bkOZyZ2dmfl1bWsjXishaN3cOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107142, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjDU07ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqFDQ/+MeFArThE7E/5r9rOR0bKYtfP6eC9v07zegtuCSmGDf2qJu6i\r\n0Dj1+OIKwwbo1pwvXarLMIqVTZkutZZKrwKIXA1omLQPLRaqbuzjd8qt2nwG\r\nKMSMyaWsvABxiLGHurLwRvmdYkueCs6B9LeUq9cPqzAWIhzeNe5yDxGHsiza\r\nGwLgFQ4kI2NFTL04bL+uFzPyqgGJkZ4YVme8zpo9V1TKIKhbDcix+RlaHZ+7\r\nj/XW3AYBIQ1KHDy62l31qnNXZ40T7xPY1tuhHq2jALi0C37FUiQvdfzlhoqo\r\nVfyrfuW64xK3zqFOnifOoxCO22TAdj97w6t86ku6S3m6p6o9LWcAYj4mz7rk\r\nzj/rpHMneXKWs5hYnGWgP7FHdm0f4ntC41Qxy7nJZhgGiNHjbChs0Ldskxp2\r\nc7ZFYiVZxWKtUrt7zOeTZsjrM4yYeUIDlu2HIqVNggFwaqwK0x/1nUzEwZ3G\r\nmZZhiSEQMSyqcn2rmVAvacazutVjfGtPC9KBmd0aKZIQWd7XjSEWLfJxh3mB\r\nKgd4AjONiGRzDyJROH+o7n2W/CRFeznESp2LQpcr3ERGawPTqZHgelp/ZLft\r\nxcOTnOT1Eb+bpm5PnubjFcuTsuvpqUKaJc4ukUTfyiShnK1LxC+tsEt/dH5D\r\nMZqtc5K6WeTlsfbL+UbfFNNjPOAGF7hMqOw=\r\n=kEqX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "typesVersions": {"<=4.1": {"*": ["ts4.1/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "4.0", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.12_1661816122788_0.742222931142783", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d4df68df8e78a5fbea065f2c70cb5a640ef0af23e51d0c2333d415774c195825"}, "17.0.13": {"name": "@types/yargs", "version": "17.0.13", "license": "MIT", "_id": "@types/yargs@17.0.13", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "34cced675ca1b1d51fcf4d34c3c6f0fa142a5c76", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.13.tgz", "fileCount": 11, "integrity": "sha512-9sWaruZk2JGxIQU+IhI1fhPYRcQ0UuTNuKuCW9bR5fp7qi2Llf7WDzNa17Cy7TKnh3cdxDOiyTu6gaLS0eDatg==", "signatures": [{"sig": "MEUCIQCeNctExyDrHCrht+jZF3wXeZxGAqvJBoKxNrLL0ck5TwIgS6kqfUNe0joH/11kRY/gTz5DURpjy8C/P9PZEQdE7h8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107145, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjLkJ0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmprwA//RlVZnlmJ95CXweyIqwzYH/94KT5L2H6+9PjNuo4Tg30wOOYe\r\n7ayf3L3oSV4YUFNBVhujGcYwq6hfu65uPPQggqvG5LGl0Aehf7LXCUajqqHS\r\nRsx9X8nqhVO2pwoyoiDVQWXprvy3tN0Dt6cJc29c/raks1YXym/xQSpsV/zU\r\nKnx1F/N14OJtrzOvzvx8BG7tvGZ8c6UB4V+DtFmpHYVESOfovBP3GG3yuWqR\r\ngR+d5b0OAhF+kyVVMqDpgDpeeAqK/tBpcUq41i2v9I/kMIwFVvf2OZdrYKSn\r\nvp9S+DRfwYff8l114EEAWnsOLFsjOhtuqbmXTdgH0Xo34iv6J6viMSJCATPS\r\nOcZJoPa7ynVHhWju0FAMI5SAuRhSWI+c6lwVlq3EW9DVg72dd9RtuzslIJhk\r\nnRXNfbH3Kd1MyteCFFrxZNEdXTFAMXStsFAJ2n8sZ/rPqEKKZFTBBKqoeM1q\r\nIfSxriGfBmhEvN2zRMNRWsNBmK4TSMZq25aJRusj5gbnVqNktKQPO39BHhGf\r\nhZCS4zPUatPiiUgXG/xtETaLhxqj3ojreL+ObuDS3OIXbLykSC4938z2DlYX\r\nnnODao9iEafJjiC7VAZQ9camENEWF0mtXsqeHMAONNdb7VjXx/3gq7doHLLs\r\n/vz7YadPTg47LvjJ14d7bMlS+woYFDGs51A=\r\n=JVSi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "typesVersions": {"<4.2.0-0": {"*": ["ts4.1/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "4.1", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.13_1663976052597_0.036661010466743305", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "9016ff10b6e6354147619595a33b71abde9faf7542f46ec8296d7c8d2bd5cc45"}, "17.0.14": {"name": "@types/yargs", "version": "17.0.14", "license": "MIT", "_id": "@types/yargs@17.0.14", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "0943473052c24bd8cf2d1de25f1a710259327237", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.14.tgz", "fileCount": 11, "integrity": "sha512-9Pj7abXoW1RSTcZaL2Hk6G2XyLMlp5ECdVC/Zf2p/KBjC3srijLGgRAXOBjtFrJoIrvxdTKyKDA14bEcbxBaWw==", "signatures": [{"sig": "MEUCIQC580zRYI+zW36/zCB8221P+HWLGLbGTb3EQY/3Bc0bIQIgFSswUyG9nVXkI2Q+JNhW9xUf8Zu0a5dgWvKppQg6BmA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108947, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjfmQpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRpA/9GohbhUV9Y41pxocSEwAlJq3qJuk8BfkQAUzK+5jz7IGG6VOy\r\nH+fxEFPMJ/GfzvR4tOKT4FKuSAEBwYd+pcFUBNoc+OrUmJBtetmZs5jiK3LO\r\nr3KtXIe7qfXh3u0X8RwNvJIrPKo0mm8EucbLwp2jC1aklVrPiQjIIfqoBWCa\r\nF545BvJ2wBtK7LUWJuIjruSKI+A55a16ff7U9If54FmAi87THv9E2N8VNHSG\r\nCk35q3d3ZXmQUP5gbRNlA6XK0qMS1kyc0oP6uo8dn9tHcoHWzzbLQ/I4V+sA\r\nl3DvbcD4CvNj2P8V9nKkV+zo7UFsFUcuLwTj8svbNNaS2B7DKsG5IvAVou9/\r\nJ3Qcj1T4EuOx3v03W6TAd1YxvnuHUu6ERSG0sTKxtljdiZ0y/DhD74TAys/r\r\nZoYwJ49BEX7uaDPyI93Jy2VjDqec2FbxCmauBcPB3KY4dDm5aKlj3qwUq0hk\r\nTBpJWIyt0D9QAASBMsIvF+A+rM4GmlsUvVaSn09H3XkQ8vNDUnbfvNEDNmWC\r\n6AzKb9VXOreBurda3y4mMzgyCmaCsGSab1wSh1YpJNItLF3JrDge3OpsTT7+\r\nB6JCdae8y2f1+Fot0LilbGsOCixHqPgIiXAXqviP1rFcQPE1f+xaDS+YIquj\r\nlU2VhQ840XPAN0HVUMrR35qhh+gcBcjPtq4=\r\n=6FJj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "typesVersions": {"<4.2.0-0": {"*": ["ts4.1/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "4.1", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.14_1669227561561_0.6195432574579431", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "55f48f27baf55e300b0d8d7b86e66b01ef28ff00687cfae2e99157d8286bdd62"}, "17.0.15": {"name": "@types/yargs", "version": "17.0.15", "license": "MIT", "_id": "@types/yargs@17.0.15", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "5b62c89fb049e2fc8378394a2861a593055f0866", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.15.tgz", "fileCount": 9, "integrity": "sha512-ZHc4W2dnEQPfhn06TBEdWaiUHEZAocYaiVMfwOipY5jcJt/251wVrKCBWBetGZWO5CF8tdb7L3DmdxVlZ2BOIg==", "signatures": [{"sig": "MEQCIBiOzrkQaYlcf/ahtcYu1X7B3JLZK8swYZamK6WVNIWtAiB61b1R5RXh+kT3af421yNIAG33CEAOquVGHJU81bsJ8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhOo2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo2BA/+Iq0Kg3ZL9f1PapV8OWc2ylmeEUtWrteP/TvE+5/J32vTjqzw\r\nry7uQn0g4hIYpuBQl35Ar8h311uHZDh5Zzs7DLWenm1rHWrpjTqiNUVVqqK9\r\nA8LMcFNyN403eMwf5MPuomvPJ0HC42LLuf6KJrQ17v2ZauRB3MPMmApLHDbf\r\npB++GZRgehOw1i5p0iupHNm8/iicpl89eFaQVTrJnUeTCGXvb/Nea3DM+gFs\r\nGpysJsMKWDAZUInVJ1BlKcXBSGfR6nqLe7/jOHKWwLLOogb0vwSDPcTPeJLN\r\nXweWl2yqrhZ3ww9JPRQiJhu/XS/IOTb6mCD6bFrOgkCprPIYZiE8JNYoz1xp\r\nOdhryqr4QJVHykRR5s3EL41QI74eP/oKFeyLXH7WmlEl6z2CHl2I3CWxJGPd\r\nJudfMHLzTYGs52xHnYCqBVeSAt5f5rS6VOxhI05XO6kaTQDWwC0apdpXRMih\r\ndG4nve0MhJKSpJJe1pziIQ4O/F9KY0pLzqju8Fq5wJH7P+0e1Ni78gbUs/pU\r\nhCoFn2KgSmekqV3ZRpdgT3zTJnBBaCqJC1c+yoWw5nWprIcBevZx9Qjol6iO\r\ncFOMqVoOCFDhMxvrdRF1KYaAe8gj89vOrDZ5iZZBMQ7486cPtcU3cCeiEWCm\r\n6MVOlrHNbY8yvJ20a18Uw32gHZSpkOxunws=\r\n=uzPf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.1", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.15_1669655094280_0.08886992413622208", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "48c3d2bcd7e025f1a3d9c71c123ea7d37543345d2e4d49099539c413f78fc85b"}, "17.0.16": {"name": "@types/yargs", "version": "17.0.16", "license": "MIT", "_id": "@types/yargs@17.0.16", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "9ad7938c9dbe79a261e17adc1ed491de9999612c", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.16.tgz", "fileCount": 9, "integrity": "sha512-Mh3OP0oh8X7O7F9m5AplC+XHYLBWuPKNkGVD3gIZFLFebBnuFI2Nz5Sf8WLvwGxECJ8YjifQvFdh79ubODkdug==", "signatures": [{"sig": "MEUCIQCx0ZkbBqYlCQ9F7f+pv8GE0B1obPZeMjb0xtHXYoeARAIgVnmvu9jx5h2iGoT30sU3Z9Y7cJ0viynFnZu+hzJPtrk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59126, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjo27ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqqIw//dt//OQ5QZw0ZND96Ya/I49vAIwkmBnBsJbpnxSviCjznpN5U\r\n8Zh3HVa27qXv2iiXVZ3VOeOpItZ7KK/jNmlLSHlymjJNbnVdZdoSyfD7NetK\r\n8LsY8Y0KiXJgbRKRaMLl7elZ4sCTH+4oueGbMLf5sDGV3x1utB3fjrbE11c3\r\nDC9b2mTbrFSWcSNCVoIyC11lpBTfT/iTE7AskjO4mHaCIoykbcyG4aFKsk2N\r\n1KewOYQcaWgghpPVZa9OZvUwIgX0nlKiaT4jSXinmx6pO8A+HuKA21TVfBZU\r\n1FU2sW3M2BKujX51PjI4CadOmA2wrUGK62o6K2o0fCWn7YTZ5b+VXF8IBkUv\r\n3Akb4SL+Y9FzlMLtoBsZpnCKKaJiVWWNeckrCDO/MOajXH/Jd8tPPSvfSh7+\r\naccfl7UBr/P84ODeCjzeb/9CLvFvHK9JI5ypxuyErvllFdXVESiHxh8puvv7\r\n0FNXtt4NQm/A73QxudONDxSLJ8MbL6GUf9b9MItXt/RfClMBL2DpWSsjcEQx\r\ndFXwp2DviF+JggEkMtskyExnZ7PDGK7bZ/rdITDqwyw6FLKSa9rCmUt4axOF\r\nVSx/28SE+X5mXU2XqNENYZzsGJuJTuZLJa7PnIYk3O/mCUGOKC0aMxquH46n\r\nG2b/oMoOCk0chLew8cHv6xIcHdc1Xx6NYDU=\r\n=PQVZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.16_1670286779451_0.8155588385815047", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3b1f28d212947229a918b7f1505f2b421fb28cb90f16a9b59596e1d0aa14be6c"}, "17.0.17": {"name": "@types/yargs", "version": "17.0.17", "license": "MIT", "_id": "@types/yargs@17.0.17", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "5672e5621f8e0fca13f433a8017aae4b7a2a03e7", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.17.tgz", "fileCount": 9, "integrity": "sha512-72bWxFKTK6uwWJAVT+3rF6Jo6RTojiJ27FQo8Rf60AL+VZbzoVPnMFhKsUnbjR8A3BTCYQ7Mv3hnl8T0A+CX9g==", "signatures": [{"sig": "MEYCIQDFn46KT42HOfs589OeVGOV5XpHEetZzZ1vsTQ05jUBTwIhAN2jXAgP6K0exTBDGbhiC9ULFwcX1pkUOqwXrU57+Xp3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59135, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkM5cACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqGkw//XqZ+PCgO2e8k31d3TN/2I1JK6W5uPG8NsQrOwruyqwqnC+uE\r\nF7X3J3IMcToWspkRF8mm4SMONPj6crz88/Fw51I6yLo5Oly8Pie6BO3oAI2w\r\nqHdZ5kjDWyIN7zg/GTwm9vnu771T5DdB2/VuFLSr2nMyiOBuze2pXEHjASgD\r\ntzV4HxHIHPMSci7P/tECLfaQ0kU4lB0VpTO2wQzHY/zc+a8cpuAL0lk5g+3m\r\nxSSqiMwfpHBn4BMjoO2OlqwWF/d3e83Ki7wLVMO3oYimgUrHYcXofTQit1zd\r\ne5F0WJ0wL6Yzfwg07ZYgI4KhoSF64wOo/k8tb4NLPBNhPIYdFupBNs/WVfYf\r\nWuKvORmhN2WTw0GebkhQPTvQ5SNlOCLR16B9CYahcBGs8ojUM4HxneNZWpoh\r\nsWWBdFhgBFdJFpdnwCIS0SwioP9814v/AnxNuKjCe28ttKp7K3zF45vdyY3s\r\nlUT0inknmh1Bo+3am4UhtQP0zUAgc4X/bg+bIeCptlO4okQ8Icyksfpe91rC\r\nDMRwzSYcxJTB1KsocrQ0x+hM7UkL9uCe8oNxLX60ibtUqj+VWaBN183yKKCH\r\nKTvex2krIA8zPFyrAmlv/Zs/W5bNhjtqSoJzthYEDZ7xU3Fmbl6UC7NRyzHw\r\n3CSdWWssWGTeGKdu5963xaj7dBfuzk49Bq8=\r\n=36ea\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.17_1670434396327_0.30010352866017076", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f5ec6b11b7337b53caabe350f8208f9ebc0bc752a72d514fd466266b1862d838"}, "17.0.18": {"name": "@types/yargs", "version": "17.0.18", "license": "MIT", "_id": "@types/yargs@17.0.18", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "466225ab4fbabb9aa711f5b406796daf1374a5b7", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.18.tgz", "fileCount": 9, "integrity": "sha512-eIJR1UER6ur3EpKM3d+2Pgd+ET+k6Kn9B4ZItX0oPjjVI5PrfaRjKyLT5UYendDpLuoiJMNJvovLQbEXqhsPaw==", "signatures": [{"sig": "MEUCIHCF9wbFtC1/DIi59D49gE3S/OF21HFgRz4WgpKpr25gAiEA9N201w/OTuzyoHNZxnLXRwMwFizvDdTvxTcjWKIn/MU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjreu5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq91Q/+NKSkD1XIWjFzJm3uYb5AK5iskk6XYKfbT36w1POmzjYTKhQH\r\nmoN/1y47QeEwmOywa2mFUPvLsw7q6mi+8WxcFJjkNIWVOfhiAguIOjVZoFcl\r\n94rZgP/nYUJdT2WYq+MtkdTYuZtN4CKt19VhJoeXOAEDBMUAr13Fn0/NpYhu\r\n5Krb0+3OwXm9SZcjrENnzLkKOP/yCTIwLJIKYwtLP4ajpigsY5k/FihctUYC\r\nEtKybunNLcbL91KVQmNto61qNzPImGgreeBC6inAuy636znXTpWjP2YTkdvG\r\nmF7bFhhVfUocRCUCf4t0gLK2Z67lkRRUs9Z5jQVYrCSdS8H+/eJB2jVCnjgg\r\nmBa3jKr+faUJxjz4TBI5bJ0yNVk5bSAZuxBXdpfunmjTsXP9SnG9Oei3WIvG\r\nEkPhtMeqeJk3h12LJWJnLRE/t42UaWzdvjsFO6Oi73l6YeXBEBska5fCnRKv\r\nvVXvGIyeJuDSsILRNcOtaWGYRTgepcQhzyPZc42v0ObQHCz1gMkPpZ82w/XV\r\nvvemMzFrYBbRqb5V/6wJIe/ir+eiGZjYbAdYQbRS6t+BPghK1FxQM+2R9HpB\r\nNYvB7O5/x+ogdQYcT9SCGChSIniosly8La3NM30+XGg3TRLB0hJJWYuqIYiF\r\n13VR4BXbdTyC2XYDP9AIRG6feJPU/s8+EAs=\r\n=tyyD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.18_1672342457038_0.5808074934374914", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "53990dcc2537e6b7342410fd71d58b83b8d158407122ffd03082bd60ba944f58"}, "17.0.19": {"name": "@types/yargs", "version": "17.0.19", "license": "MIT", "_id": "@types/yargs@17.0.19", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "8dbecdc9ab48bee0cb74f6e3327de3fa0d0c98ae", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.19.tgz", "fileCount": 9, "integrity": "sha512-cAx3qamwaYX9R0fzOIZAlFpo4A+1uBVCxqpKz9D26uTF4srRXaGTTsikQmaotCtNdbhzyUH7ft6p9ktz9s6UNQ==", "signatures": [{"sig": "MEQCIHx+FwiaMIC5QmNetmPCJa6uP2LUk6bfyd5jn1EOMV+WAiBLP9y2ZIDBwTal3G8cn/SokKfPgA/ddTk7enkbwfFQDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtLQiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2fw//dGU7kk7imncU0FIDkLUgnoQK8Qm3J545/aOQsymBRUwOk4l9\r\nsB8F9KuTg+Ff757Cq9UaGct96+Z2eed5BTEKsDSsi2oLPPn0b8qZe0/lykZh\r\nc3s0AOP9paZLa96N7VzcnqyIIMgcbYyn3XwM2bspVQRFH+rYpDT6RmivKpBf\r\nO3ZLag9jOzTCFPScpEIUp3AvvcbZq9nlum3ay+NtRamvtMaKkucJ6s0YQjuV\r\nHva1uJ+o2Mwie4N2HDsW7AONUuVtWR2Be8ogkbYJfDsu+NfmAl2DZXVA8ca8\r\nRfRDggClL+XbFP2jjw/w493Dc44HDSHJ6mCxHyTcQrYoS5BU/vSw/j6/b9yz\r\neBEVr20Pb944tE1lRGSFWu0hBcqaQuSaFOY8TvqnDdfyfQizYYsHaaUVjWeL\r\n85LxMs5+Al6yM4TeBf1xixaq1ws5ELbbItuGNgGN5Ojn+w7hNAPQfUZvNlOe\r\n1HqURYwCWq/FHCwnZV8popPBa1710XqW6weUEhPJRWxzrRCJ0X/ofjhZixZH\r\nSC26DFuM8VH5iW23ZrGKF9H0pbNl5iiAm/j3frZCCVn0jipBhu0wfyDxzzxm\r\nMeDm6Y8yHX77YAIFueR9v9/tduyCuO1SfMcgTdy2POVt2Wo7LUUT8ZXoaor+\r\nkpQWd9i5ZmDCLIH/WFzsy+sT5HXKLFva0wY=\r\n=lWiD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.19_1672786978077_0.06952060871636312", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "bcc41179e6154fa3d745cf197a12242438d667355715f5d22ffb1321cb0218d4"}, "16.0.5": {"name": "@types/yargs", "version": "16.0.5", "license": "MIT", "_id": "@types/yargs@16.0.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "12cc86393985735a283e387936398c2f9e5f88e3", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-16.0.5.tgz", "fileCount": 7, "integrity": "sha512-AxO/ADJOBFJScHbWhq2xAhlWP24rY4aCEG/NFaMvbT3X2MgRsLjhjQwsn0Zi5zn0LG9jUhCCZMeX9Dkuw6k+vQ==", "signatures": [{"sig": "MEUCIQCao9MEHyQ1xI/nNyych7M5M0QH7bCCBB9gCTCZLgRdjgIgXXIgFAW8C5vFiJncm4l9NF1d8Su1TARw7OfdnGD9KnE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53112, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtLQzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrG3xAAkBKNV79JtFcSU5p1WIwoYn5pZuTeiUaJ1YBGhy3zjoTip1az\r\nPOlHswrZWKYgAZgYUuLdj5wDUqS49xjuU2RLdB2Y3suoS9pl8yAeYYT7VhSe\r\n5y2zT1Meb4YDDaRjRJAp1EtHxF0C4zn9BPQVo+ta2AT8y/qdr/q3EBKucp7+\r\nXrbs+HiD5Of/+H7yJuP0k7vDyBVS0TbRkuL92YiFOLkgA96/Yds0oRJqvBFV\r\nceKW102m8qwEvOCcvAboRsWUKECi6hjmlSgInKfAnG3Pdi/2yCFH66Y1FpzH\r\no51IjYHyUEOSmLlmX4sYJoMoQf/uKpiKQWjQZP0l/7MvfGLDIdx6hhz1pjiN\r\nEMc7IY83v5ia24sJlnSb7w6S/eQ3+4TZ8HpI8YRAIuJWshpUAgMZXjLB+YHd\r\n85cUBG2DXgMztxAg/VYmHmOln07R/GUAVxqS573suM2j7KzHlzT+C9kjisL/\r\nNIg+42NY5SUx8gXXSMpq8YCrfOeblK7AEdnPMaQmu8wflJtdT8mEbb7YuHCw\r\nGNy9LK7MTERM5xaDoyNB+3FqkmEbucgaobob1DHodBLfxQqJ5ojmqAlTKcwN\r\nKhWDXkKkyvQsfe4i0Czu/pIRCsoy6TZf2vlKVI5nvLcoQHbTQYrTNuJjmTG+\r\nOTL9hlrqN47l3CgFLKK552XEz2K718ZlP2M=\r\n=Uxuv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_16.0.5_1672786995021_0.3837022188964212", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "718c015e408b3fe3ff22029038c67d6982bc91e3723390bf38cb0419cb544703"}, "15.0.15": {"name": "@types/yargs", "version": "15.0.15", "license": "MIT", "_id": "@types/yargs@15.0.15", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "e609a2b1ef9e05d90489c2f5f45bbfb2be092158", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.15.tgz", "fileCount": 6, "integrity": "sha512-IziEYMU9XoVj8hWg7k+UJrXALkGFjWJhn5QFEv9q4p+v40oZhSuC135M38st8XPjICL7Ey4TV64ferBGUoJhBg==", "signatures": [{"sig": "MEUCIAsOIoYz7lnDnh+ZahRMH+vhL83gjGISpNVCaLSPqh2bAiEAkB86133Q/NtE6HhV/0gFoP2GnImqssPvJg+Je9b1mq8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtLQ3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo2Xg//drXAvjTiizW+0K5j+Q9gqiXYRSdzetCaNxN9tJMMckHui7QX\r\nLso12gRx9tmKmZcM/FlPWRTRLIwwu1Vej0mPjCYI2bKAjqZKnCFH3SsAAdhq\r\n0lHd3/L8Kj/1FrdXIb1KMosZOa92q2fhibVHwwQBkMu5bsQ3eAvf2E6WFFak\r\ngBKuztPBTzBHotHg33KKktERv3mtIE5pi9Gxvznd9Xkya9Ns9KoqP6Yqk6Tx\r\nxKMV7YyxsMP39XnV7qEosPADo4BSBx0eIfeAS3KrJ9OX/D2p1ixgREXtPh2e\r\nCD/2oc2fjCgpGdcurd7u3QPA+SddhDvVmSxAXtA20GMY0RCbSfaCiWJolUIe\r\n0Y8Su0DWpCl2SUo8Eh35wwlOkJekFn0zL5Xi/Wtm6+ksF50H+cFbQgV1x0wp\r\nAtn9omvKKoDxnLOQ3Pjafdo3B1mRkEYoRI691YmDCuvFJbvvjTPNxrryRBsI\r\na9sP+cripE3AO4mRSWue8ZEzYIFGdZJlMm9AQE4onrA1iJcz2MqTj4FREB+L\r\n/ESQlN8CcMZjsScRCYVM1pqJhDaZJzZCgwUmliADG5aHC+vWtITDAAku2okr\r\npupGYdHoqBqdZPPTNU28n1bLZ7fwU9hOMnHP0RtuDtReIp9s0DHeUCr4gFs6\r\npBsv4gmtfDgP04GHwe+BqmL+6wqcM9G8OeU=\r\n=2Qjr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_15.0.15_1672786999167_0.15177902816636446", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7a706fdf1246d1f36538a21e0fbde4ae87cf370cac03b93854b1120dba730aed"}, "17.0.20": {"name": "@types/yargs", "version": "17.0.20", "license": "MIT", "_id": "@types/yargs@17.0.20", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "107f0fcc13bd4a524e352b41c49fe88aab5c54d5", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.20.tgz", "fileCount": 9, "integrity": "sha512-eknWrTHofQuPk2iuqDm1waA7V6xPlbgBoaaXEgYkClhLOnB0TtbW+srJaOToAgawPxPlHQzwypFA2bhZaUGP5A==", "signatures": [{"sig": "MEYCIQC0uKCd4Xdn89IWzIfn5iTX7I/JOJYIpMFPhIH1HzNz2wIhAKp8k86Leg65mAgWpyDqjLYDxGzGLpbe/IpidVvbvV7D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59317, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyEKnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1tw/5ARUBlOLYtU4RVKmok4VZCb959Vbi6nbKLNFCrG4mxFE7G2ho\r\nELA6QPF4xENGyyrC1DXZ1VtXP8APyxtD/PVrqZcqLP7/qUCGvXEFrNeen6G5\r\nMf3HOhvPHb4mk/n0EQ3w3Gsz8P7IVPipj3N6Hq/WHJfVpTS2w187LGtEepFK\r\n2vb/6Z00yYVJGYt3K2gMZhTNt4xtk583RfIbo0KztpsXoa/TwrNufgkIIWkd\r\nk3NJBOcVB6OsPt7S7BX75ulMJTB/dgOnHFdTsdvkrnxiY1jjR4WoDqoY+Jae\r\nKnI4KjcmavBayiZOaX2A2Guv+dIY+ci8ixikRiXkJT1iDnjKUa2QhAPyrxAe\r\ntLECybP2nli1dmkTUzoUJ4AoyK2SKHP9ru3Fz6W4EiYvsiXOjHNNIh0D4Ltr\r\n7s2BFr8z1BCFERMxketzyIEXhUSqcIFtafSW5QdOyZUDZFR2iS6+xgKyA+b3\r\nOqf4+r1gpTvRPR7egTC0acevq1rPZBwM9DGbmkFRyUnQfcBGz6JHCDQJz4Ud\r\nVqn68Yt3aooE69hqhk14RhpULCq7+IvIoxh9/GOj/UcNZlOaG9B/UGuIZ2tc\r\nKajxofmwo2JycydNaUuAm0HxFAmS5/C2gM8Mvbc4B8Q/m2QGwe2S9TOWMmrX\r\nGA9dD1tEgkkrnNUUPLj41ibET6CxEjCuOFk=\r\n=lSkG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.20_1674068647717_0.22055498726290423", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "127423b8e182147ee7d3281c39f91cb5fa22e985ca500f39569bb7e79a0b7f67"}, "17.0.21": {"name": "@types/yargs", "version": "17.0.21", "license": "MIT", "_id": "@types/yargs@17.0.21", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "1d8d790eb7c5ba31a8eda9fb43557859972cdb94", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.21.tgz", "fileCount": 9, "integrity": "sha512-kQxgIw2qr3/au36DPzK4Kzl5fpB/SehrD7TUBdWQlOLUkgBMhOBQzz1R9Kuukng9ukWxD3lewSMUZWCwNcmRHg==", "signatures": [{"sig": "MEYCIQCPreh8is2mbG9Zm0x4pIIize1bLrthsZ+bbJeH8PNHxgIhAM2aJEbe3Lzi7EO4QoJpacgB80Tx9CvQX25WdaRhJqzm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2FOUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6cQ//aYO/cpPz0vJFoeJrnnZh0Qt1B0J7U4+xswzqi8pNvtryuV/f\r\nD6852JZVY9ekpKJ9XEqB3dceHKb2/90qNYtIpQ78i1rXs64fUeuyYF4BHd6d\r\nVzMGleDjEFJ5SBI7WQ7n6QFZHbKITiwvS45tEN3JQdWmXwWOFCkFvFL6YDf9\r\nEdOi0rxvhZKCOSS0rtqfMJHG0cWQvNlFTqgzSWeF9MpAUVepRrAZx8WGlmHr\r\nlshtGgwyf5/hNwQE0ouBsjG2p4tleuP2zN4/JuNYf7OsRFZF7IBkdsz8XK/E\r\nBDXiMGwR93GdkNuxvPyGnl451jwvCcDVnTB9qs28Bm8sGFk+XnQ66C5BJYjX\r\nkClT26nqXTO1dxemEGaWKpOGTeeZ0PzM6K8RU5ci9hK2dBULia/2uPuYl21E\r\nI31HnPEd8PAJaNE60Ac6oEImIMyaFG0Qk+EkU6cSUZaar43zL7C3JlhvJ0Vj\r\nz1aAqcNo3R94aj9H3wJwCFtMizAS23r0bUcN4sFXPAaqGmEniKYn/2/HCJQ2\r\nD/XsvQsb5ChwP6AgbWUo4fIEveFQIinGcsdtQU02FhrG0KLTNHv+FiwEyJ97\r\nthoNRkYFnCuXsF+fOrbpErSeoxXJ/xVqcSK4NNy+Azl4AmU1qMLOXhs8Ws1J\r\n6v09EhxSRWnFcVQXQkIr//lOlfK89eS/WAU=\r\n=lQ1o\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.21_1675121556104_0.4104614214058673", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3f0a1cdca41b80752fb82331c7ffe522d2b80871be3c470de3e8848cdb026496"}, "17.0.22": {"name": "@types/yargs", "version": "17.0.22", "license": "MIT", "_id": "@types/yargs@17.0.22", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "7dd37697691b5f17d020f3c63e7a45971ff71e9a", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.22.tgz", "fileCount": 9, "integrity": "sha512-pet5WJ9U8yPVRhkwuEIp5ktAeAqRZOq4UdAyWLWzxbtpyXnzbtLdKiXAjJzi/KLmPGS9wk86lUFWZFN6sISo4g==", "signatures": [{"sig": "MEUCIQCjYWf5VOSZoRHfEaGHykns2LGMc9tLV59k0zpmyqDeJgIgNjAdxUgbGhd1Gc5giLDYbpuAGjTEWdruf3Gu7Am2ojA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2O5AACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8IA//eYHXF+wtS7N4rZTEGvwCUKJW5JfwtgBocQTYoGN0zs/gE05q\r\nGwJXXcMha7WjEzZh3pHrZ9JnGLv13eefG50YWCJHUQMnW4aNBYQuJJbatoC4\r\nHFBGep/joUbnuQqUlTwzBbLpJkBEERmxPqR241de+/tCbRBQX6EmFKTJYdIG\r\nSYsiKZy1yG6BZVtakjlGebiWD2M+E2JGy399W3iH7CH6Q+UQl3kMrndfxeEz\r\niC02qKiFZFkq8c/x3fXFZJbO2VCH/5tDMXHeMCwa4PVt9GM0/UrnsgRQpXXQ\r\n7A+ylGFewHlkLxCNafyN8bylLMMmyF5qc6TKgJi6lXP2s8I32Wim+3L1Kf1m\r\nq1rC53hwJwIk7UrCrZAe+PnL2WtNkES4kF7C1zi2lD3fCJkPCguGeAZKYgeK\r\njbk4r38nmkQdBreTWEa8BC4MqM6g/1iMLv1QFmSMOR8baarx+8wG5DsfW43v\r\nBpbOx739Mbncc3/BaXPiMwxz7k4WN34vCJtk21X6XIQnziYUHQxgTY8C2uh+\r\n71avUnGD02Muq22nHMaP+JnNEc0CJFTDvG0TvObS0UWriZalJxwx5OBrgPz0\r\niWXh52IUap/8ucXAgwVGuypzRJNIbsA2fWkx79JS1SWpL96SqXaDjjy15STd\r\nbODXztpqxYAbmjRcM24PjkgSF1OMimNfVx4=\r\n=rU+d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.22_1675161152434_0.5917059987209716", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "03d6c4c41d671cab14d9258777ecc56b1192bd89b7bb45226cddeb391de18616"}, "17.0.23": {"name": "@types/yargs", "version": "17.0.23", "license": "MIT", "_id": "@types/yargs@17.0.23", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "a7db3a2062c95ca1a5e0d5d5ddb6521cbc649e35", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.23.tgz", "fileCount": 9, "integrity": "sha512-yuogunc04OnzGQCrfHx+Kk883Q4X0aSwmYZhKjI21m+SVYzjIbrWl8dOOwSv5hf2Um2pdCOXWo9isteZTNXUZQ==", "signatures": [{"sig": "MEQCIDz6/PDceufX8avGKPDPqOGODuwvR6V8YkDlnyV+VSgKAiARVdbJbDxKrZcDprIVQJ5B6p2cC+jACh/JlbKw6YWLMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGPS7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpaOw/9GtDCpqtX6URbm0zTa/1ZSKZ70Z4/ME5bzgOv1I3URSF4LgSC\r\nvfhTxmr/PK9y5tlq3ids1Yfl9yA01Oo2bTirxoXOnsBQjQZqgp4fvQNqnhSo\r\nXV7mVysnOBa6AVnE1135RDmW4C173UmvIAB7xlLbQlQOyJsPC7Uot6j/869I\r\nF6cG+4OT4bj3INVeequLWwKQFpadm8IbDbSATSbC/t0mP9hm8bo70BZjOznt\r\n2DGcA3P2L96oNYpZXzdwgDDu01dYHp60S5kHCiG+jE3ziWD1VSPN7wjZYlx8\r\nIgvUFmFMIYMjRaLF6MFydlKcOLlo8424EcuH56Hc35+cl57DKjXXH7PhPbMA\r\nAxUtLpHWlLANznWc7kn+HoCFiDAwm6nmrfpp0EoNKNOmqOWyluuOM1Xrtmdv\r\nD5Pst15wEncROOBmCq2H1D02xQ+sI6tLkX9qgmZ7rme8pIHppngSs/p4si1K\r\nhKsrJPjereAfVHfHyUhi3/Ag7O8cDTlEsIVKveqNfe5C1AyxhIOD9vhR7Cps\r\nWw0nwtWW1H4kGnqga8OQjHmsSZq8iq6NEIp5buq/+Z7x90O7JahDUx/T5Xin\r\nFKmnBCnlyRupdgznNdZwcBpJSlqXiVUA1g58H8Wzh/q9jd6kTp+KxSZWJfSo\r\nR5hon15ixzPxEAIzJDLKxzCfdkkDkU5x0lw=\r\n=293X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.23_1679357115378_0.10300562035415495", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b37c113435ecdfb17a56b1a7b1377f511f1f7cbbfb9db6a2df04c92731f50924"}, "17.0.24": {"name": "@types/yargs", "version": "17.0.24", "license": "MIT", "_id": "@types/yargs@17.0.24", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "b3ef8d50ad4aa6aecf6ddc97c580a00f5aa11902", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.24.tgz", "fileCount": 9, "integrity": "sha512-6i0aC7jV6QzQB8ne1joVZ0eSFIstHsCrobmOtghM11yGlH0j43FKL2UhWdELkyps0zuf7qVTUVCCR+tgSlyLLw==", "signatures": [{"sig": "MEUCIQDpvG+D51Sxx7VGKdWiajhoHBOd2jQsTaKoc3xuTHpBiQIgU/j1Ezm70Uf8CSAQuR0X/x5TVx/458GTo//T0YLEQmw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59838, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkIeKIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKnw//bgOn9nyVB3SDFk1OdbSIuX7SaRrJeUHtgy6TDyZxaOj267JN\r\no+tXh5KavSKVyOAkbCq2MBUI6iaeDoiZNIgjPDZ+BsfZLzpsB0uWC/h1dasp\r\nzYglU5TZchPkg4qkeXVLPMhkugKc9iDd80aAdIf1E28MKezxsWb546xo853u\r\nNuGLy7rTAynj+DcGYt8pJjMzGbzCltlOmGcg1KsqTBD766GeICdujH+hJZal\r\n0/fkTVqYEo1CVvAdcTRTfHylsr1HTOssJjZHrG21GW3n0bOdxgdq2OuedJDW\r\nTuPcZ+2fCixkw3Ln16wUUogXtm1rH+eOPJtxHXmOfylv3Nyq1/rodf32f55q\r\njzlMnvAghJzgMkE2Ac858zC22jKVM4ZYqePrSjplEqbGGF4ypCWTutYa/yih\r\nXC8ba28H7Ek+/BDSbFGpoXCUK4AllRWIb4ci7e8V6+qpPZtyJS0QaFXNJS+F\r\nG2is5mPh6rparFaQRbokE+d2fY/soXiVdNTeszh0cqHXCXSxYBEl9qzUkmav\r\nS9uvn+3iJWmGCtHGl/wyXecTns8aU0zbDlbmMw9+yoI/3gVS+zf0Lf+MVweK\r\nC4bGqXt8PbmGOwUHhfrk2joLAiPI7DKA0Hw/DHWQ4XYCA6LlED/p6BcESu3d\r\n/q8FIXNG+K9Iq46QY4wETAaxHhTuBNvbrTs=\r\n=aasm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.24_1679942280646_0.9218298807833356", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8c666a98fd66e7ead842a9f404d65f9210b3c3feb9aada8fbfa3e29c79b43788"}, "17.0.25": {"name": "@types/yargs", "version": "17.0.25", "license": "MIT", "_id": "@types/yargs@17.0.25", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "3edd102803c97356fb4c805b2bbaf7dfc9ab6abc", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.25.tgz", "fileCount": 9, "integrity": "sha512-gy7iPgwnzNvxgAEi2bXOHWCVOG6f7xsprVJH4MjlAWeBmJ7vh/Y1kwMtUrs64ztf24zVIRCpr3n/z6gm9QIkgg==", "signatures": [{"sig": "MEUCIFg2iI0OBqOeevfML5Du3kf27XKOYbgRMc19fvRURePxAiEAibz21LyJ+YvkTBPeneglBKUOOJnc/Do1emxiPUMpNdc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60853}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.25_1695620257323_0.3715721498711675", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d5e4d7261347b44c4193cc32be7bbe2927ea63985676565c5767c6a00f3a57c3"}, "16.0.6": {"name": "@types/yargs", "version": "16.0.6", "license": "MIT", "_id": "@types/yargs@16.0.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "cc0c63684d68d23498cf0b5f32aa4c3fb437c638", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-16.0.6.tgz", "fileCount": 7, "integrity": "sha512-oTP7/Q13GSPrgcwEwdlnkoZSQ1Hg9THe644qq8PG6hhJzjZ3qj1JjEFPIwWV/IXVs5XGIVqtkNOS9kh63WIJ+A==", "signatures": [{"sig": "MEYCIQC+S0cFTD24rUUXcUJIjaCuRdW/JcXveLi1nALTh4ArzQIhAJMGJiNy8vjfvMOVVO6m3GE63deNsdZccS+P00q9NJu3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54084}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs_16.0.6_1695620276760_0.8103747152476806", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "51741c8e5c857d2ceb86e87903d6e93afabdf6c9d0c92b3a50ec51284a70f9da"}, "15.0.16": {"name": "@types/yargs", "version": "15.0.16", "license": "MIT", "_id": "@types/yargs@15.0.16", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "258009dc52907e8f03041eb64ffdac297ba4b208", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.16.tgz", "fileCount": 6, "integrity": "sha512-2FeD5qezW3FvLpZ0JpfuaEWepgNLl9b2gQYiz/ce0NhoB1W/D+VZu98phITXkADYerfr/jb7JcDcVhITsc9bwg==", "signatures": [{"sig": "MEUCIQDkrUM9Jpf0DXv+Gp5gwn9yBJ7NDyGaWO93+nrI2NRUUgIgX/Q0XZOFPu2xrkxFIGvZkhH3uwdjYGsrGMrtQ8xLrvw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53543}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs_15.0.16_1695620281315_0.02407341640182481", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "62cc6c492efb0be6dc2b848f858fa1dc7ef407c175100fa8a184d92019f4245b"}, "17.0.26": {"name": "@types/yargs", "version": "17.0.26", "license": "MIT", "_id": "@types/yargs@17.0.26", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "388e5002a8b284ad7b4599ba89920a6d74d8d79a", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.26.tgz", "fileCount": 9, "integrity": "sha512-Y3vDy2X6zw/ZCumcwLpdhM5L7jmyGpmBCTYMHDLqT2IKVMYRRLdv6ZakA+wxhra6Z/3bwhNbNl9bDGXaFU+6rw==", "signatures": [{"sig": "MEUCIQDic/FlGshw/KYNEC3eZe8l8jURZWCJRDxsPHbMP/Ky+gIgd1c9Wz5GNVaqjKsKwrEgQ2l5rNjpt74r3kJ93M9w7F8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61189}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.26_1695946490631_0.7918110503576881", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ba24f1afefd3edf8c715354912f85c46f957dfded393da97610c3c9e96758940"}, "17.0.27": {"name": "@types/yargs", "version": "17.0.27", "license": "MIT", "_id": "@types/yargs@17.0.27", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "a0508217c6a920ff54f272042ed91753480342d7", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.27.tgz", "fileCount": 9, "integrity": "sha512-J+a6bwjDa0ezk5jBUR/iAHH2xm0gIf4YzsS4QINYTQ42bTiz/Szt5ZSLNLyHospoUAQ6EanoD4NztoJBLACHoQ==", "signatures": [{"sig": "MEQCIBatznfE9FOz3EbhIA3rUyHB8XMR9Du3WVgLGANBR0NsAiAU3+oWzrToXXRgcmx+zeB99ABafycEIxG2Xvk5PDOKcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61283}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.27_1696633892414_0.9604426919063023", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c44959f58c1c4b14033ec43aa7bc070a28fb8a7a366afc26c3a794d2abf057f1"}, "17.0.28": {"name": "@types/yargs", "version": "17.0.28", "license": "MIT", "_id": "@types/yargs@17.0.28", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "d106e4301fbacde3d1796ab27374dd16588ec851", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.28.tgz", "fileCount": 9, "integrity": "sha512-N3e3fkS86hNhtk6BEnc0rj3zcehaxx8QWhCROJkqpl5Zaoi7nAic3jH8q94jVD3zu5LGk+PUB6KAiDmimYOEQw==", "signatures": [{"sig": "MEYCIQD3sMhkc6jTsssrdTLeU/DgD4SvXqm0f27Ii0WQjq65wwIhAPIXMKE4eKU3Lv8KA0mhZLygNLcm1zbJzEenN6P8SCeB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61257}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.28_1696639760743_0.5936043054296172", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "6338e06d114d47ef8e25fcdc5f30d40f5cba54c9d5805e9ea40f84909f411811"}, "17.0.29": {"name": "@types/yargs", "version": "17.0.29", "license": "MIT", "_id": "@types/yargs@17.0.29", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "06aabc72497b798c643c812a8b561537fea760cf", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.29.tgz", "fileCount": 9, "integrity": "sha512-nacjqA3ee9zRF/++a3FUY1suHTFKZeHba2n8WeDw9cCVdmzmHpIxyzOJBcpHvvEmS8E9KqWlSnWHUkOrkhWcvA==", "signatures": [{"sig": "MEYCIQCexPlPc60fXUdyEkytZD1JgG2rN2tsa91pzCrcKh6tYwIhAMNGjQgnO0i+cGqsezO466MDG2uh2CjWNrZk+cXobBjV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60474}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.29_1697657058284_0.7092175570258057", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3a2cba458599ba65d6954ec81ea82f7f8f670bb850e8efae39e3d39697e9a927"}, "16.0.7": {"name": "@types/yargs", "version": "16.0.7", "license": "MIT", "_id": "@types/yargs@16.0.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "b0d0502cb5f6c17994df72a600049f10bbf17203", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-16.0.7.tgz", "fileCount": 7, "integrity": "sha512-lQcYmxWuOfJq4IncK88/nwud9rwr1F04CFc5xzk0k4oKVyz/AI35TfsXmhjf6t8zp8mpCOi17BfvuNWx+zrYkg==", "signatures": [{"sig": "MEUCIBFdMT2uhZSJHY3wsXc8nkhspwjZNw6puhAZ53UwcbXKAiEA8+oTdro318eM2XJoWcgJjLrzWgwA/uSy2HVWfrQxW0E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53327}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs_16.0.7_1697657067131_0.7701776981312611", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3225e59b2dcf0609439bdcfe4202fb5536796da339a4d46d423cd78874d3c8b4"}, "15.0.17": {"name": "@types/yargs", "version": "15.0.17", "license": "MIT", "_id": "@types/yargs@15.0.17", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "bea870ba551b43831bfaa75de2e4a3849c39322b", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.17.tgz", "fileCount": 6, "integrity": "sha512-cj53I8GUcWJIgWVTSVe2L7NJAB5XWGdsoMosVvUgv1jEnMbAcsbaCzt1coUcyi8Sda5PgTWAooG8jNyDTD+CWA==", "signatures": [{"sig": "MEQCIFvQengSpG0h5Nnx+Q6h5m2B8hAE9vSbQsNYx10ubPHmAiBSrvmwcBySOmGLQAvxMpZ+OzhFA+cgpsoBSG+H+3LOaw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52786}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs_15.0.17_1697657069409_0.15627212610246888", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d4fbf6dc67005724f0335727c8017358c08e0fde03f77a49175446d270272f1e"}, "17.0.30": {"name": "@types/yargs", "version": "17.0.30", "license": "MIT", "_id": "@types/yargs@17.0.30", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "921094ec92faffd2cd7e5ddb02f95ba158ab5c1d", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.30.tgz", "fileCount": 9, "integrity": "sha512-3SJLzYk3yz3EgI9I8OLoH06B3PdXIoU2imrBZzaGqUtUXf5iUNDtmAfCGuQrny1bnmyjh/GM/YNts6WK5jR5Rw==", "signatures": [{"sig": "MEYCIQCNqN8fXNe9GfNjZOu7RaCN1fhCKuv7IcaYLHO0HeLyWAIhAMCGI+4Ib4KVWIaeYssvWiv5uvOZhxnbTn5gpds/TRJT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60474}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.30_1699300116283_0.03339823590821833", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "55b5ed21a4ec8bcd8927551ae97eb85492de08871e0b0fdeff9f082d5bdbcbd5"}, "17.0.31": {"name": "@types/yargs", "version": "17.0.31", "license": "MIT", "_id": "@types/yargs@17.0.31", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "8fd0089803fd55d8a285895a18b88cb71a99683c", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.31.tgz", "fileCount": 9, "integrity": "sha512-bocYSx4DI8TmdlvxqGpVNXOgCNR1Jj0gNPhhAY+iz1rgKDAaYrAYdFYnhDV1IFuiuVc9HkOwyDcFxaTElF3/wg==", "signatures": [{"sig": "MEYCIQC7kFO3zy+jgH/Y2Y0MCRVQhIHOxoQ41+Gu6LTQh/1lYgIhALiyyz4JU1lNFnubsVf7IDBxGlx7I+ofYpMGdhJiLhIX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60474}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.31_1699390404464_0.3834914653833712", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8642a52065f5623d73ae3cb486e65a9ab6bc470db4ed0d908060e600e8a56b11"}, "16.0.8": {"name": "@types/yargs", "version": "16.0.8", "license": "MIT", "_id": "@types/yargs@16.0.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "0d57a5a491d85ae75d372a32e657b1779b86c65d", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-16.0.8.tgz", "fileCount": 7, "integrity": "sha512-1GwLEkmFafeb/HbE6pC7tFlgYSQ4Iqh2qlWCq8xN+Qfaiaxr2PcLfuhfRFRYqI6XJyeFoLYyKnhFbNsst9FMtQ==", "signatures": [{"sig": "MEUCIFgN0d1gVmOa1Ca4+ChZrP1rfiDiZ+eGSrSk596icFFOAiEAkBdvOoseOYXlLQ8JXXQWW6o/xgMNNTuncRluvhWsUgo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53327}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs_16.0.8_1699390414038_0.4906390673110983", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8c78f38375dd2c8c1ff03402b501b0c0a949f4092be6a5e95330b96b5cfcde59"}, "15.0.18": {"name": "@types/yargs", "version": "15.0.18", "license": "MIT", "_id": "@types/yargs@15.0.18", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "b7dda4339f4dde367ffe99650e18967108cea321", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.18.tgz", "fileCount": 6, "integrity": "sha512-DDi2KmvAnNsT/EvU8jp1UR7pOJojBtJ3GLZ/uw1MUq4VbbESppPWoHUY4h0OB4BbEbGJiyEsmUcuZDZtoR+ZwQ==", "signatures": [{"sig": "MEYCIQDHIzXAfrBnWQOEmzDzMKv/NlSe831D1XtgsomzLBLU3AIhAOVNelmR4h4j9TDsxrdZrT1e4QH3Fu1gG1Pg8UEGTMio", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52786}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs_15.0.18_1699393013516_0.38377842151892994", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "649d51b17ba5af7e414f5962957530354f7377e0cfe8e1af4d1ff458ae147fb5"}, "17.0.32": {"name": "@types/yargs", "version": "17.0.32", "license": "MIT", "_id": "@types/yargs@17.0.32", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "030774723a2f7faafebf645f4e5a48371dca6229", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.32.tgz", "fileCount": 9, "integrity": "sha512-xQ67Yc/laOG5uMfX/093MRlGGCIBzZMarVa+gfNKJxWAIgykYpVGkBdbqEzGDDfCrVUj6Hiff4mTZ5BA6TmAog==", "signatures": [{"sig": "MEYCIQCK9q3LrTeCz1zVp0DB9AAQiBABwqwe/n4CsBTv7g+frQIhAMvKl96Yh+O6pILQZArhnye47Ql57BT+URZr5wsrrKJs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60158}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.32_1700530769101_0.39333061612622244", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "490ca9a07cb156b66b475f9dc1666bc4b4d19059bcace8f035888670f6952639"}, "16.0.9": {"name": "@types/yargs", "version": "16.0.9", "license": "MIT", "_id": "@types/yargs@16.0.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "ba506215e45f7707e6cbcaf386981155b7ab956e", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-16.0.9.tgz", "fileCount": 7, "integrity": "sha512-tHhzvkFXZQeTECenFoRljLBYPZJ7jAVxqqtEI0qTLOmuultnFp4I9yKE17vTuhf7BkhCu7I4XuemPgikDVuYqA==", "signatures": [{"sig": "MEYCIQCTuX5rMo2Yg4Rpy+QzlqBhJcuD4PqQ+q/XXlrMW0sqxgIhANhZ+/Ma+9eBrCmoaHd46jbvuou65yyfY6z9718PGXzp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53027}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs_16.0.9_1700530779345_0.7062738684533361", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "cb1afff2e12b5d722156de0eabb2bd446fd1eb57638052e13771b041d96e0e52"}, "15.0.19": {"name": "@types/yargs", "version": "15.0.19", "license": "MIT", "_id": "@types/yargs@15.0.19", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "328fb89e46109ecbdb70c295d96ff2f46dfd01b9", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-15.0.19.tgz", "fileCount": 6, "integrity": "sha512-2XUaGVmyQjgyAZldf0D0c14vvo/yv0MhQBSTJcejMMaitsn3nxCB6TmH4G0ZQf+uxROOa9mpanoSm8h6SG/1ZA==", "signatures": [{"sig": "MEQCIGtaKKGtu+awgLYbRtu0EmI6RmVSiGHslhC8EeAHW8GTAiAM6xgtIAR62W7fr6Qzj8bmLAahy3lLrE8k2Csy3u1cbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52486}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/yargs_15.0.19_1700530781346_0.2203583965263396", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "2320f14d0576aeec97e6b7bdfcc018edfc2a73a7d5202feab66105deee3e59f4"}, "17.0.33": {"name": "@types/yargs", "version": "17.0.33", "license": "MIT", "_id": "@types/yargs@17.0.33", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "dist": {"shasum": "8c32303da83eec050a84b3c7ae7b9f922d13e32d", "tarball": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.33.tgz", "fileCount": 9, "integrity": "sha512-WpxBCKWPLr4xSsHgz511rFJAM+wS28w2zEO1QDNY5zM/S8ok70NNfztH0xwhqKyaK0OHCbN98LDAZuy1ctxDkA==", "signatures": [{"sig": "MEQCIF+slJefpW9LFeh4sZJ52C3uShr5DCK6F6xkk+qlHabnAiA2eh9BO70mLlH2HCNz90uxYhplZ+jdGKG9X6loKfeydA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60405}, "main": "", "types": "index.d.ts", "exports": {".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}, "./yargs": {"types": {"default": "./yargs.d.ts"}}, "./helpers": {"types": {"import": "./helpers.d.mts", "default": "./helpers.d.ts"}}, "./package.json": "./package.json"}, "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "directories": {}, "dependencies": {"@types/yargs-parser": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.8", "_npmOperationalInternal": {"tmp": "tmp/yargs_17.0.33_1722974860546_0.6519343665535973", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7b837c3dffe16c68f89717a71a88af716d72acc79790394e14f9cafa6be33a2f"}}, "time": {"created": "2016-05-17T19:19:15.034Z", "modified": "2025-02-23T08:10:03.004Z", "0.0.14-alpha": "2016-05-17T19:19:15.034Z", "0.0.15-alpha": "2016-05-19T22:47:22.556Z", "0.0.20-alpha": "2016-05-20T20:57:57.798Z", "0.0.21-alpha": "2016-05-25T06:12:46.143Z", "0.0.22-alpha": "2016-07-01T20:54:26.514Z", "0.0.23-alpha": "2016-07-02T00:17:55.590Z", "0.0.24-alpha": "2016-07-02T03:43:34.223Z", "0.0.25-alpha": "2016-07-04T01:33:53.014Z", "0.0.26-alpha": "2016-07-08T21:48:17.291Z", "0.0.27": "2016-07-14T16:26:40.190Z", "0.0.28": "2016-08-02T16:15:55.519Z", "0.0.29": "2016-08-11T13:55:26.989Z", "0.0.30": "2016-08-25T19:04:47.871Z", "0.0.31": "2016-09-19T18:21:35.422Z", "0.0.32": "2016-10-06T17:06:12.572Z", "0.0.33": "2016-10-11T18:48:31.700Z", "0.0.34": "2016-10-26T19:29:36.684Z", "0.0.35": "2016-11-01T12:58:09.950Z", "0.0.36": "2016-11-01T13:02:02.712Z", "6.3.0": "2016-11-03T17:43:39.064Z", "6.3.1": "2016-11-08T15:06:13.460Z", "6.3.2": "2016-11-14T19:37:48.790Z", "6.3.3": "2016-11-28T16:31:24.904Z", "6.5.0": "2016-12-23T14:16:42.630Z", "6.6.0": "2017-02-07T20:54:32.193Z", "8.0.0": "2017-06-30T21:46:40.587Z", "8.0.1": "2017-07-10T19:47:35.199Z", "8.0.2": "2017-08-01T21:20:22.585Z", "10.0.0": "2017-12-06T20:11:07.623Z", "8.0.3": "2017-12-06T20:11:09.523Z", "10.0.1": "2018-01-03T08:48:00.333Z", "11.0.0": "2018-02-13T23:15:14.858Z", "10.0.2": "2018-02-13T23:15:20.379Z", "11.1.0": "2018-07-11T01:15:08.345Z", "11.1.1": "2018-07-21T02:12:31.967Z", "11.1.2": "2018-09-17T07:44:56.303Z", "12.0.0": "2018-09-17T07:44:59.856Z", "12.0.1": "2018-09-28T21:47:52.229Z", "12.0.2": "2018-12-20T00:22:01.922Z", "12.0.3": "2018-12-25T05:08:01.231Z", "12.0.4": "2018-12-26T06:47:11.216Z", "12.0.5": "2018-12-31T16:49:28.113Z", "12.0.6": "2019-01-22T17:37:55.354Z", "12.0.7": "2019-01-24T02:29:15.563Z", "12.0.8": "2019-01-24T23:29:40.769Z", "12.0.9": "2019-02-14T01:30:22.948Z", "12.0.10": "2019-03-18T19:23:07.985Z", "12.0.11": "2019-04-01T20:47:19.272Z", "13.0.0": "2019-04-08T01:51:57.967Z", "12.0.12": "2019-04-08T01:52:17.399Z", "13.0.1": "2019-07-29T22:47:40.145Z", "13.0.2": "2019-07-31T17:14:14.588Z", "13.0.3": "2019-09-25T16:10:43.647Z", "12.0.13": "2019-09-25T16:10:59.208Z", "11.1.3": "2019-09-25T16:11:12.216Z", "10.0.3": "2019-09-25T16:11:26.867Z", "13.0.4": "2019-12-23T17:01:30.293Z", "12.0.14": "2019-12-23T17:01:51.416Z", "15.0.0": "2020-01-08T19:44:00.226Z", "13.0.5": "2020-01-08T19:44:21.009Z", "15.0.1": "2020-01-21T23:55:03.316Z", "13.0.6": "2020-01-21T23:55:27.485Z", "12.0.15": "2020-01-21T23:55:42.878Z", "11.1.4": "2020-01-21T23:55:57.922Z", "10.0.4": "2020-01-21T23:56:14.560Z", "8.0.4": "2020-01-21T23:56:30.539Z", "15.0.2": "2020-01-27T19:32:42.560Z", "13.0.7": "2020-01-27T19:33:08.473Z", "15.0.3": "2020-01-31T23:41:08.541Z", "13.0.8": "2020-01-31T23:41:26.999Z", "12.0.16": "2020-01-31T23:41:42.230Z", "11.1.5": "2020-01-31T23:41:57.474Z", "10.0.5": "2020-01-31T23:42:12.388Z", "15.0.4": "2020-02-25T20:34:36.631Z", "13.0.9": "2020-05-11T23:16:27.205Z", "12.0.17": "2020-05-11T23:16:42.298Z", "15.0.5": "2020-05-13T21:48:48.405Z", "13.0.10": "2020-08-05T22:19:28.971Z", "12.0.18": "2020-08-05T22:19:44.317Z", "11.1.6": "2020-08-05T22:19:59.683Z", "10.0.6": "2020-08-05T22:20:16.067Z", "8.0.5": "2020-08-05T22:20:30.923Z", "15.0.6": "2020-09-23T01:29:41.323Z", "15.0.7": "2020-09-23T19:20:30.994Z", "13.0.11": "2020-09-23T19:20:53.944Z", "12.0.19": "2020-09-23T19:21:08.825Z", "11.1.7": "2020-09-23T19:21:25.669Z", "10.0.7": "2020-09-23T19:21:40.739Z", "8.0.6": "2020-09-23T19:21:55.992Z", "15.0.8": "2020-10-08T18:20:47.371Z", "15.0.9": "2020-10-16T15:35:59.604Z", "15.0.10": "2020-11-18T00:21:21.465Z", "15.0.11": "2020-12-02T19:15:32.543Z", "15.0.12": "2020-12-15T17:32:50.327Z", "16.0.0": "2021-01-29T01:22:37.886Z", "15.0.13": "2021-01-29T01:22:56.154Z", "16.0.1": "2021-03-25T18:31:58.477Z", "16.0.2": "2021-05-18T16:33:55.044Z", "17.0.0": "2021-05-21T10:35:54.968Z", "16.0.3": "2021-05-21T10:36:10.531Z", "17.0.1": "2021-07-02T16:34:44.055Z", "16.0.4": "2021-07-02T16:34:55.139Z", "15.0.14": "2021-07-02T16:35:01.174Z", "13.0.12": "2021-07-02T16:35:07.593Z", "12.0.20": "2021-07-02T16:35:14.076Z", "11.1.8": "2021-07-02T16:35:20.318Z", "10.0.8": "2021-07-02T16:35:26.231Z", "8.0.7": "2021-07-02T16:35:32.174Z", "17.0.2": "2021-07-05T21:01:59.315Z", "17.0.3": "2021-09-21T19:03:55.898Z", "17.0.4": "2021-10-15T01:01:30.046Z", "17.0.5": "2021-10-30T00:01:59.739Z", "17.0.6": "2021-11-17T20:32:31.409Z", "17.0.7": "2021-11-18T04:31:55.061Z", "17.0.8": "2021-12-23T01:31:47.299Z", "17.0.9": "2022-03-02T17:33:16.424Z", "17.0.10": "2022-03-17T22:32:10.747Z", "17.0.11": "2022-08-05T01:02:16.494Z", "17.0.12": "2022-08-29T23:35:23.067Z", "17.0.13": "2022-09-23T23:34:12.878Z", "17.0.14": "2022-11-23T18:19:21.757Z", "17.0.15": "2022-11-28T17:04:54.504Z", "17.0.16": "2022-12-06T00:32:59.606Z", "17.0.17": "2022-12-07T17:33:16.498Z", "17.0.18": "2022-12-29T19:34:17.246Z", "17.0.19": "2023-01-03T23:02:58.259Z", "16.0.5": "2023-01-03T23:03:15.242Z", "15.0.15": "2023-01-03T23:03:19.331Z", "17.0.20": "2023-01-18T19:04:07.919Z", "17.0.21": "2023-01-30T23:32:36.313Z", "17.0.22": "2023-01-31T10:32:32.608Z", "17.0.23": "2023-03-21T00:05:15.563Z", "17.0.24": "2023-03-27T18:38:00.815Z", "17.0.25": "2023-09-25T05:37:37.516Z", "16.0.6": "2023-09-25T05:37:56.973Z", "15.0.16": "2023-09-25T05:38:01.569Z", "17.0.26": "2023-09-29T00:14:50.958Z", "17.0.27": "2023-10-06T23:11:32.587Z", "17.0.28": "2023-10-07T00:49:21.058Z", "17.0.29": "2023-10-18T19:24:18.495Z", "16.0.7": "2023-10-18T19:24:27.547Z", "15.0.17": "2023-10-18T19:24:29.657Z", "17.0.30": "2023-11-06T19:48:36.536Z", "17.0.31": "2023-11-07T20:53:24.653Z", "16.0.8": "2023-11-07T20:53:34.255Z", "15.0.18": "2023-11-07T21:36:53.732Z", "17.0.32": "2023-11-21T01:39:29.261Z", "16.0.9": "2023-11-21T01:39:39.528Z", "15.0.19": "2023-11-21T01:39:41.532Z", "17.0.33": "2024-08-06T20:07:40.791Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/yargs", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/yargs"}, "description": "TypeScript definitions for yargs", "contributors": [{"url": "https://github.com/poelstra", "name": "<PERSON>", "githubUsername": "p<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mizunashi-mana", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "mi<PERSON><PERSON><PERSON>-mana"}, {"url": "https://github.com/pushplay", "name": "<PERSON><PERSON>", "githubUsername": "pushplay"}, {"url": "https://github.com/JimiC", "name": "<PERSON><PERSON> (<PERSON><PERSON>) Charalampidis", "githubUsername": "JimiC"}, {"url": "https://github.com/steffenvv", "name": "Steffen Viken Valvåg", "githubUsername": "steffenvv"}, {"url": "https://github.com/forivall", "name": "<PERSON>", "githubUsername": "forivall"}, {"url": "https://github.com/ExE-Boss", "name": "ExE Boss", "githubUsername": "ExE-Boss"}, {"url": "https://github.com/Aankhen", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/bcoe", "name": "<PERSON>", "githubUsername": "bcoe"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": "", "users": {"vzivkovic": true}}