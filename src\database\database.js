const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

class TeamDatabase {
    constructor() {
        // Ensure database directory exists
        const dbDir = path.join(__dirname, '../data');
        if (!fs.existsSync(dbDir)) {
            fs.mkdirSync(dbDir, { recursive: true });
        }

        // Initialize database
        const dbPath = path.join(dbDir, 'teams.db');
        this.db = new Database(dbPath);
        
        // Enable WAL mode for better performance
        this.db.pragma('journal_mode = WAL');
        
        this.initializeTables();
        this.runMigrations();
        console.log('✅ Team database initialized successfully');
    }

    initializeTables() {
        // Create members table
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS members (
                id TEXT PRIMARY KEY,
                team_id TEXT,
                points INTEGER DEFAULT 0,
                level INTEGER DEFAULT 0,
                last_daily INTEGER DEFAULT 0,
                voice_time INTEGER DEFAULT 0,
                message_count INTEGER DEFAULT 0,
                created_at INTEGER DEFAULT (strftime('%s', 'now')),
                updated_at INTEGER DEFAULT (strftime('%s', 'now')),
                FOREIGN KEY (team_id) REFERENCES teams (id) ON DELETE SET NULL
            )
        `);

        // Create teams table
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS teams (
                id TEXT PRIMARY KEY,
                name TEXT UNIQUE NOT NULL,
                role_id TEXT,
                created_at INTEGER DEFAULT (strftime('%s', 'now')),
                total_points INTEGER DEFAULT 0,
                level INTEGER DEFAULT 0
            )
        `);

        // Create indexes for better performance
        this.db.exec(`
            CREATE INDEX IF NOT EXISTS idx_members_team_id ON members(team_id);
            CREATE INDEX IF NOT EXISTS idx_members_points ON members(points DESC);
            CREATE INDEX IF NOT EXISTS idx_teams_total_points ON teams(total_points DESC);
        `);

        // Create triggers to automatically update team totals and levels
        this.db.exec(`
            CREATE TRIGGER IF NOT EXISTS update_team_totals_on_member_update
            AFTER UPDATE OF points ON members
            WHEN NEW.team_id IS NOT NULL
            BEGIN
                UPDATE teams
                SET total_points = (
                    SELECT COALESCE(SUM(points), 0)
                    FROM members
                    WHERE team_id = NEW.team_id
                ),
                level = (
                    SELECT COALESCE(SUM(points), 0) / 500
                    FROM members
                    WHERE team_id = NEW.team_id
                )
                WHERE id = NEW.team_id;
            END;
        `);

        this.db.exec(`
            CREATE TRIGGER IF NOT EXISTS update_team_totals_on_member_insert
            AFTER INSERT ON members
            WHEN NEW.team_id IS NOT NULL
            BEGIN
                UPDATE teams
                SET total_points = (
                    SELECT COALESCE(SUM(points), 0)
                    FROM members
                    WHERE team_id = NEW.team_id
                ),
                level = (
                    SELECT COALESCE(SUM(points), 0) / 500
                    FROM members
                    WHERE team_id = NEW.team_id
                )
                WHERE id = NEW.team_id;
            END;
        `);

        this.db.exec(`
            CREATE TRIGGER IF NOT EXISTS update_team_totals_on_member_team_change
            AFTER UPDATE OF team_id ON members
            BEGIN
                -- Update old team totals
                UPDATE teams
                SET total_points = (
                    SELECT COALESCE(SUM(points), 0)
                    FROM members
                    WHERE team_id = OLD.team_id
                ),
                level = (
                    SELECT COALESCE(SUM(points), 0) / 500
                    FROM members
                    WHERE team_id = OLD.team_id
                )
                WHERE id = OLD.team_id AND OLD.team_id IS NOT NULL;

                -- Update new team totals
                UPDATE teams
                SET total_points = (
                    SELECT COALESCE(SUM(points), 0)
                    FROM members
                    WHERE team_id = NEW.team_id
                ),
                level = (
                    SELECT COALESCE(SUM(points), 0) / 500
                    FROM members
                    WHERE team_id = NEW.team_id
                )
                WHERE id = NEW.team_id AND NEW.team_id IS NOT NULL;
            END;
        `);

        console.log('✅ Database tables and triggers created successfully');
    }

    // Member operations
    getMember(userId) {
        const stmt = this.db.prepare('SELECT * FROM members WHERE id = ?');
        return stmt.get(userId);
    }

    createMember(userId) {
        const stmt = this.db.prepare(`
            INSERT OR IGNORE INTO members (id, points, level, last_daily, voice_time, message_count)
            VALUES (?, 0, 0, 0, 0, 0)
        `);
        return stmt.run(userId);
    }

    updateMemberPoints(userId, points) {
        // Ensure member exists
        this.createMember(userId);
        
        const stmt = this.db.prepare(`
            UPDATE members 
            SET points = points + ?, 
                level = (points + ?) / 100,
                updated_at = strftime('%s', 'now')
            WHERE id = ?
        `);
        return stmt.run(points, points, userId);
    }

    setMemberTeam(userId, teamId) {
        // Ensure member exists
        this.createMember(userId);
        
        const stmt = this.db.prepare(`
            UPDATE members 
            SET team_id = ?, 
                updated_at = strftime('%s', 'now')
            WHERE id = ?
        `);
        return stmt.run(teamId, userId);
    }

    removeMemberFromTeam(userId) {
        const stmt = this.db.prepare(`
            UPDATE members 
            SET team_id = NULL, 
                updated_at = strftime('%s', 'now')
            WHERE id = ?
        `);
        return stmt.run(userId);
    }

    updateLastDaily(userId, timestamp) {
        // Ensure member exists
        this.createMember(userId);
        
        const stmt = this.db.prepare(`
            UPDATE members 
            SET last_daily = ?, 
                updated_at = strftime('%s', 'now')
            WHERE id = ?
        `);
        return stmt.run(timestamp, userId);
    }

    updateVoiceTime(userId, seconds) {
        // Ensure member exists
        this.createMember(userId);
        
        const stmt = this.db.prepare(`
            UPDATE members 
            SET voice_time = voice_time + ?, 
                updated_at = strftime('%s', 'now')
            WHERE id = ?
        `);
        return stmt.run(seconds, userId);
    }

    // Team operations
    createTeam(teamId, teamName, roleId = null) {
        const stmt = this.db.prepare(`
            INSERT INTO teams (id, name, role_id, total_points, level)
            VALUES (?, ?, ?, 0, 0)
        `);
        return stmt.run(teamId, teamName, roleId);
    }

    getTeam(teamId) {
        const stmt = this.db.prepare('SELECT * FROM teams WHERE id = ?');
        return stmt.get(teamId);
    }

    getTeamByName(teamName) {
        const stmt = this.db.prepare('SELECT * FROM teams WHERE name = ?');
        return stmt.get(teamName);
    }

    deleteTeam(teamId) {
        // Get team info before deletion (for role cleanup)
        const team = this.getTeam(teamId);

        // First remove all members from the team
        const removeMembersStmt = this.db.prepare('UPDATE members SET team_id = NULL WHERE team_id = ?');
        removeMembersStmt.run(teamId);

        // Then delete the team
        const deleteTeamStmt = this.db.prepare('DELETE FROM teams WHERE id = ?');
        const result = deleteTeamStmt.run(teamId);

        return { result, team };
    }

    getTeamMembers(teamId) {
        const stmt = this.db.prepare(`
            SELECT * FROM members 
            WHERE team_id = ? 
            ORDER BY points DESC
        `);
        return stmt.all(teamId);
    }

    // Leaderboard operations
    getTopMembers(limit = 10) {
        const stmt = this.db.prepare(`
            SELECT m.*, t.name as team_name 
            FROM members m 
            LEFT JOIN teams t ON m.team_id = t.id 
            WHERE m.team_id IS NOT NULL
            ORDER BY m.points DESC 
            LIMIT ?
        `);
        return stmt.all(limit);
    }

    getTopTeams(limit = 10) {
        const stmt = this.db.prepare(`
            SELECT * FROM teams 
            ORDER BY total_points DESC 
            LIMIT ?
        `);
        return stmt.all(limit);
    }

    // Utility methods
    getAllTeams() {
        const stmt = this.db.prepare('SELECT * FROM teams ORDER BY name');
        return stmt.all();
    }

    getAllMembersInTeams() {
        const stmt = this.db.prepare(`
            SELECT m.*, t.name as team_name, t.role_id as team_role_id
            FROM members m
            LEFT JOIN teams t ON m.team_id = t.id
            WHERE m.team_id IS NOT NULL
            ORDER BY t.name, m.points DESC
        `);
        return stmt.all();
    }

    getMemberCount(teamId) {
        const stmt = this.db.prepare('SELECT COUNT(*) as count FROM members WHERE team_id = ?');
        return stmt.get(teamId).count;
    }

    updateMessageCount(userId, count) {
        // Ensure member exists
        this.createMember(userId);

        const stmt = this.db.prepare(`
            UPDATE members
            SET message_count = ?,
                updated_at = strftime('%s', 'now')
            WHERE id = ?
        `);
        return stmt.run(count, userId);
    }

    getMessageCount(userId) {
        const stmt = this.db.prepare('SELECT message_count FROM members WHERE id = ?');
        const result = stmt.get(userId);
        return result ? result.message_count : 0;
    }

    runMigrations() {
        try {
            // Check if role_id column exists in teams table
            const tableInfo = this.db.prepare("PRAGMA table_info(teams)").all();
            const hasRoleId = tableInfo.some(column => column.name === 'role_id');

            if (!hasRoleId) {
                console.log('🔄 Adding role_id column to teams table...');
                this.db.exec('ALTER TABLE teams ADD COLUMN role_id TEXT');
                console.log('✅ Migration completed: role_id column added');
            }

            // Check if message_count column exists in members table
            const membersTableInfo = this.db.prepare("PRAGMA table_info(members)").all();
            const hasMessageCount = membersTableInfo.some(column => column.name === 'message_count');

            if (!hasMessageCount) {
                console.log('🔄 Adding message_count column to members table...');
                this.db.exec('ALTER TABLE members ADD COLUMN message_count INTEGER DEFAULT 0');
                console.log('✅ Migration completed: message_count column added');
            }

        } catch (error) {
            console.error('❌ Error running database migrations:', error);
        }
    }

    close() {
        this.db.close();
    }
}

module.exports = TeamDatabase;
