{"_id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_rev": "20-497dd666b1387aefe27697c48d45634b", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "An incremental implementation of MurmurHash3", "dist-tags": {"latest": "0.1.4"}, "versions": {"0.0.1": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.0.1", "description": "An incremental implementation of MurmurHash3", "homepage": "https://github.com/jensyt/imurmurhash-js", "main": "imurmurhash.js", "files": ["imurmurhash.js", "imurmurhash.min.js", "package.json", "README.md"], "repository": {"type": "git", "url": "https://github.com/jensyt/imurmurhash-js"}, "bugs": {"url": "https://github.com/jensyt/imurmurhash-js/issues"}, "keywords": ["murmur", "<PERSON><PERSON>h", "murmurhash3", "hash", "incremental"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/homebrewing"}, "license": "MIT", "dependencies": {}, "devDependencies": {}, "engines": {"node": ">=0.8.19"}, "readme": "iMurmurHash.JS\n==============\n\nAn incremental implementation of the MurmurHash3 (32-bit) hashing algorithm for JavaScript based on [<PERSON>'s implementation](https://github.com/garycourt/murmurhash-js).\n\nInstallation\n------------\n\nTo use iMurmurHash in the browser, [download the latest version](https://raw.github.com/jensyt/imurmurhash-js/master/imurmurhash.min.js) and include it as a script on your site.\n\n```html\n<script type=\"text/javascript\" src=\"/scripts/imurmurhash.min.js\"></script>\n<script>\n// Your code here, access iMurmurHash using the global object MurmurHash3\n</script>\n```\n\n---\n\nTo use iMurmurHash in Node.js, install the module using NPM:\n\n```bash\nnpm install imurmurhash\n```\n\nThen simply include it in your scripts:\n\n```javascript\nMurmurHash3 = require('imurmurhash');\n```\n\nQuick Example\n-------------\n\n```javascript\n// Create the initial hash\nvar hashState = MurmurHash3('string');\n\n// Incrementally add text\nhashState.hash('more strings');\nhashState.hash('even more strings');\n\n// All calls can be chained if desired\nhashState.hash('and').hash('some').hash('more');\n\n// Get a result\nhashState.result();\n// returns 0x29d3f1e3\n```\n\nFunctions\n---------\n\n### MurmurHash3 ([string], [seed])\nGet a hash state object, optionally initialized with the given _string_ and _seed_. _Seed_ must be a positive integer if provided. Calling this function without the `new` keyword will return a cached state object that has been reset. This is safe to use as long as the object is only used from a single thread and no other hashes are created while operating on this one. If this constraint cannot be met, you can use `new` to create a new state object. For example:\n\n```javascript\n// Use the cached object, calling the function again will return the same\n// object (but reset, so the current state would be lost)\nhashState = MurmurHash3();\n...\n\n// Create a new object that can be safely used however you wish. Calling the\n// function again will simply return a new state object, and no state loss\n// will occur, at the cost of creating more objects.\nhashState = new MurmurHash3();\n```\n\nBoth methods can be mixed however you like if you have different use cases.\n\n---\n\n### MurmurHash3.prototype.hash (string)\nIncrementally add a _string_ to the hash. This can be called as many times as you want for the hash state object, including after a calls to `result()`. Returns `this` so calls can be chained.\n\n---\n\n### MurmurHash3.prototype.result ()\nGet the result of the hash as a 32-bit positive integer. This performs the tail and finalizer portions of the algorithm, but does not store the result in the state object. This means that it is perfectly safe to get results and then continue adding strings via `hash`.\n\n```javascript\n// Do the whole string at once\nMurmurHash3('this is a test string').result();\n// 0x70529328\n\n// Do part of the string, get a result, then the other part\nvar m = MurmurHash3('this is a');\nm.result();\n// 0xbfc4f834\nm.hash(' test string').result();\n// 0x70529328 (same as above)\n```\n\n---\n\n### MurmurHash3.prototype.reset (seed)\nReset the state object for reuse, optionally using the given _seed_ (defaults to 0 like constructor). Returns `this` so calls can be chained.\n\n---\n\nLicense (MIT)\n-------------\nCopyright (c) 2013 Gary Court, Jens Taylor\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\nCOPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "readmeFilename": "README.md", "_id": "im<PERSON><PERSON><PERSON><PERSON><PERSON>@0.0.1", "dist": {"shasum": "951d3f9ce9393309aa1bf6408f6c7f00f2d9e5c9", "tarball": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.0.1.tgz", "integrity": "sha512-GfrN68aWqzZ7szLpD3R13ZLfPWrsfC/t2PL8pUfIh1V4tMGmccsI91A/Akpbtmo5xPtVeyykAeryRamQLeEW/g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDGmzPBl5GON39eRv5N0bZZdp5AQoHDTN/747sh0bUDnAIhAMKQPSoqQJC43glJ7u8CjE3y5BXDmgIxvC2PnRL32H8w"}]}, "_from": "imurmurhash-js/", "_npmVersion": "1.3.2", "_npmUser": {"name": "j<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.1": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.1", "description": "An incremental implementation of MurmurHash3", "homepage": "https://github.com/jensyt/imurmurhash-js", "main": "imurmurhash.js", "files": ["imurmurhash.js", "imurmurhash.min.js", "package.json", "README.md"], "repository": {"type": "git", "url": "https://github.com/jensyt/imurmurhash-js"}, "bugs": {"url": "https://github.com/jensyt/imurmurhash-js/issues"}, "keywords": ["murmur", "<PERSON><PERSON>h", "murmurhash3", "hash", "incremental"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/homebrewing"}, "license": "MIT", "dependencies": {}, "devDependencies": {}, "engines": {"node": ">=0.8.19"}, "readme": "iMurmurHash.JS\n==============\n\nAn incremental implementation of the MurmurHash3 (32-bit) hashing algorithm for JavaScript based on [<PERSON>'s implementation](https://github.com/garycourt/murmurhash-js) with [ka<PERSON><PERSON><PERSON><PERSON>'s modifications](https://github.com/ka<PERSON><PERSON><PERSON>mura/murmurhash-js).\n\nThis version works significantly faster than the non-incremental version if you need to hash many small strings into a single hash, since string concatenation (to build the single string to pass the non-incremental version) is fairly costly. In one case tested, using the incremental version was about 50% faster than concatenating 5-10 strings and then hashing.\n\nInstallation\n------------\n\nTo use iMurmurHash in the browser, [download the latest version](https://raw.github.com/jensyt/imurmurhash-js/master/imurmurhash.min.js) and include it as a script on your site.\n\n```html\n<script type=\"text/javascript\" src=\"/scripts/imurmurhash.min.js\"></script>\n<script>\n// Your code here, access iMurmurHash using the global object MurmurHash3\n</script>\n```\n\n---\n\nTo use iMurmurHash in Node.js, install the module using NPM:\n\n```bash\nnpm install imurmurhash\n```\n\nThen simply include it in your scripts:\n\n```javascript\nMurmurHash3 = require('imurmurhash');\n```\n\nQuick Example\n-------------\n\n```javascript\n// Create the initial hash\nvar hashState = MurmurHash3('string');\n\n// Incrementally add text\nhashState.hash('more strings');\nhashState.hash('even more strings');\n\n// All calls can be chained if desired\nhashState.hash('and').hash('some').hash('more');\n\n// Get a result\nhashState.result();\n// returns 0x29d3f1e3\n```\n\nFunctions\n---------\n\n### MurmurHash3 ([string], [seed])\nGet a hash state object, optionally initialized with the given _string_ and _seed_. _Seed_ must be a positive integer if provided. Calling this function without the `new` keyword will return a cached state object that has been reset. This is safe to use as long as the object is only used from a single thread and no other hashes are created while operating on this one. If this constraint cannot be met, you can use `new` to create a new state object. For example:\n\n```javascript\n// Use the cached object, calling the function again will return the same\n// object (but reset, so the current state would be lost)\nhashState = MurmurHash3();\n...\n\n// Create a new object that can be safely used however you wish. Calling the\n// function again will simply return a new state object, and no state loss\n// will occur, at the cost of creating more objects.\nhashState = new MurmurHash3();\n```\n\nBoth methods can be mixed however you like if you have different use cases.\n\n---\n\n### MurmurHash3.prototype.hash (string)\nIncrementally add a _string_ to the hash. This can be called as many times as you want for the hash state object, including after a call to `result()`. Returns `this` so calls can be chained.\n\n---\n\n### MurmurHash3.prototype.result ()\nGet the result of the hash as a 32-bit positive integer. This performs the tail and finalizer portions of the algorithm, but does not store the result in the state object. This means that it is perfectly safe to get results and then continue adding strings via `hash`.\n\n```javascript\n// Do the whole string at once\nMurmurHash3('this is a test string').result();\n// 0x70529328\n\n// Do part of the string, get a result, then the other part\nvar m = MurmurHash3('this is a');\nm.result();\n// 0xbfc4f834\nm.hash(' test string').result();\n// 0x70529328 (same as above)\n```\n\n---\n\n### MurmurHash3.prototype.reset (seed)\nReset the state object for reuse, optionally using the given _seed_ (defaults to 0 like the constructor). Returns `this` so calls can be chained.\n\n---\n\nLicense (MIT)\n-------------\nCopyright (c) 2013 Gary Court, Jens Taylor\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\nCOPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "readmeFilename": "README.md", "_id": "im<PERSON><PERSON><PERSON><PERSON><PERSON>@0.1.1", "dist": {"shasum": "a0bf1bd6a1556f2625159230d797ba3d5b622257", "tarball": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.1.tgz", "integrity": "sha512-lMnpsInz+a1oHlk1MTQEVxd3JHPobdYFPA4OnhLJVBuAzrZ5xFUswgTPreSupvySFCtVo8lzMP4Otlj86uJsew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCMf+IFLyZ4vJgcNAxJwB3CfvmyIq7KXFIq9zQALwIUJgIgEw9+25L7uW045/rFOe2x+3GCpfYB1SSjfOBYMjzAzxA="}]}, "_from": ".", "_npmVersion": "1.3.2", "_npmUser": {"name": "j<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.2": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.2", "description": "An incremental implementation of MurmurHash3", "homepage": "https://github.com/jensyt/imurmurhash-js", "main": "imurmurhash.js", "files": ["imurmurhash.js", "imurmurhash.min.js", "package.json", "README.md"], "repository": {"type": "git", "url": "https://github.com/jensyt/imurmurhash-js"}, "bugs": {"url": "https://github.com/jensyt/imurmurhash-js/issues"}, "keywords": ["murmur", "<PERSON><PERSON>h", "murmurhash3", "hash", "incremental"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/homebrewing"}, "license": "MIT", "dependencies": {}, "devDependencies": {}, "engines": {"node": ">=0.8.19"}, "readme": "iMurmurHash.JS\n==============\n\nAn incremental implementation of the MurmurHash3 (32-bit) hashing algorithm for JavaScript based on [<PERSON>'s implementation](https://github.com/garycourt/murmurhash-js) with [ka<PERSON><PERSON><PERSON><PERSON>'s modifications](https://github.com/ka<PERSON><PERSON><PERSON>mura/murmurhash-js).\n\nThis version works significantly faster than the non-incremental version if you need to hash many small strings into a single hash, since string concatenation (to build the single string to pass the non-incremental version) is fairly costly. In one case tested, using the incremental version was about 50% faster than concatenating 5-10 strings and then hashing.\n\nInstallation\n------------\n\nTo use iMurmurHash in the browser, [download the latest version](https://raw.github.com/jensyt/imurmurhash-js/master/imurmurhash.min.js) and include it as a script on your site.\n\n```html\n<script type=\"text/javascript\" src=\"/scripts/imurmurhash.min.js\"></script>\n<script>\n// Your code here, access iMurmurHash using the global object MurmurHash3\n</script>\n```\n\n---\n\nTo use iMurmurHash in Node.js, install the module using NPM:\n\n```bash\nnpm install imurmurhash\n```\n\nThen simply include it in your scripts:\n\n```javascript\nMurmurHash3 = require('imurmurhash');\n```\n\nQuick Example\n-------------\n\n```javascript\n// Create the initial hash\nvar hashState = MurmurHash3('string');\n\n// Incrementally add text\nhashState.hash('more strings');\nhashState.hash('even more strings');\n\n// All calls can be chained if desired\nhashState.hash('and').hash('some').hash('more');\n\n// Get a result\nhashState.result();\n// returns 0x29d3f1e3\n```\n\nFunctions\n---------\n\n### MurmurHash3 ([string], [seed])\nGet a hash state object, optionally initialized with the given _string_ and _seed_. _Seed_ must be a positive integer if provided. Calling this function without the `new` keyword will return a cached state object that has been reset. This is safe to use as long as the object is only used from a single thread and no other hashes are created while operating on this one. If this constraint cannot be met, you can use `new` to create a new state object. For example:\n\n```javascript\n// Use the cached object, calling the function again will return the same\n// object (but reset, so the current state would be lost)\nhashState = MurmurHash3();\n...\n\n// Create a new object that can be safely used however you wish. Calling the\n// function again will simply return a new state object, and no state loss\n// will occur, at the cost of creating more objects.\nhashState = new MurmurHash3();\n```\n\nBoth methods can be mixed however you like if you have different use cases.\n\n---\n\n### MurmurHash3.prototype.hash (string)\nIncrementally add a _string_ to the hash. This can be called as many times as you want for the hash state object, including after a call to `result()`. Returns `this` so calls can be chained.\n\n---\n\n### MurmurHash3.prototype.result ()\nGet the result of the hash as a 32-bit positive integer. This performs the tail and finalizer portions of the algorithm, but does not store the result in the state object. This means that it is perfectly safe to get results and then continue adding strings via `hash`.\n\n```javascript\n// Do the whole string at once\nMurmurHash3('this is a test string').result();\n// 0x70529328\n\n// Do part of the string, get a result, then the other part\nvar m = MurmurHash3('this is a');\nm.result();\n// 0xbfc4f834\nm.hash(' test string').result();\n// 0x70529328 (same as above)\n```\n\n---\n\n### MurmurHash3.prototype.reset (seed)\nReset the state object for reuse, optionally using the given _seed_ (defaults to 0 like the constructor). Returns `this` so calls can be chained.\n\n---\n\nLicense (MIT)\n-------------\nCopyright (c) 2013 Gary Court, Jens Taylor\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\nCOPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "readmeFilename": "README.md", "_id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@0.1.2", "dist": {"shasum": "34d7409511b41c1fbf2026829e27e6b59161618a", "tarball": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.2.tgz", "integrity": "sha512-5S4ayL97Nv+xz8lfLiFk4qBZdPX21Ng5BQlt5ebRya0QVi9FFJ03pI06Yh2r0F1Az/+q3pxcD126n1x6O5mHVA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFn3KISYMk7KGlwYOgazBxFI/s9mLMPH4l/UTSMUXAiZAiEA98Oq5IKKEk5gCu7xPl4OwfH7x3OHeLsg1ESzIUvpSUc="}]}, "_from": ".", "_npmVersion": "1.3.2", "_npmUser": {"name": "j<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.3": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.3", "description": "An incremental implementation of MurmurHash3", "homepage": "https://github.com/jensyt/imurmurhash-js", "main": "imurmurhash.js", "files": ["imurmurhash.js", "imurmurhash.min.js", "package.json", "README.md"], "repository": {"type": "git", "url": "https://github.com/jensyt/imurmurhash-js"}, "bugs": {"url": "https://github.com/jensyt/imurmurhash-js/issues"}, "keywords": ["murmur", "<PERSON><PERSON>h", "murmurhash3", "hash", "incremental"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/homebrewing"}, "license": "MIT", "dependencies": {}, "devDependencies": {}, "engines": {"node": ">=0.8.19"}, "readme": "iMurmurHash.JS\n==============\n\nAn incremental implementation of the MurmurHash3 (32-bit) hashing algorithm for JavaScript based on [<PERSON>'s implementation](https://github.com/garycourt/murmurhash-js) with [ka<PERSON><PERSON><PERSON><PERSON>'s modifications](https://github.com/ka<PERSON><PERSON><PERSON>mura/murmurhash-js).\n\nThis version works significantly faster than the non-incremental version if you need to hash many small strings into a single hash, since string concatenation (to build the single string to pass the non-incremental version) is fairly costly. In one case tested, using the incremental version was about 50% faster than concatenating 5-10 strings and then hashing.\n\nInstallation\n------------\n\nTo use iMurmurHash in the browser, [download the latest version](https://raw.github.com/jensyt/imurmurhash-js/master/imurmurhash.min.js) and include it as a script on your site.\n\n```html\n<script type=\"text/javascript\" src=\"/scripts/imurmurhash.min.js\"></script>\n<script>\n// Your code here, access iMurmurHash using the global object MurmurHash3\n</script>\n```\n\n---\n\nTo use iMurmurHash in Node.js, install the module using NPM:\n\n```bash\nnpm install imurmurhash\n```\n\nThen simply include it in your scripts:\n\n```javascript\nMurmurHash3 = require('imurmurhash');\n```\n\nQuick Example\n-------------\n\n```javascript\n// Create the initial hash\nvar hashState = MurmurHash3('string');\n\n// Incrementally add text\nhashState.hash('more strings');\nhashState.hash('even more strings');\n\n// All calls can be chained if desired\nhashState.hash('and').hash('some').hash('more');\n\n// Get a result\nhashState.result();\n// returns 0xe4ccfe6b\n```\n\nFunctions\n---------\n\n### MurmurHash3 ([string], [seed])\nGet a hash state object, optionally initialized with the given _string_ and _seed_. _Seed_ must be a positive integer if provided. Calling this function without the `new` keyword will return a cached state object that has been reset. This is safe to use as long as the object is only used from a single thread and no other hashes are created while operating on this one. If this constraint cannot be met, you can use `new` to create a new state object. For example:\n\n```javascript\n// Use the cached object, calling the function again will return the same\n// object (but reset, so the current state would be lost)\nhashState = MurmurHash3();\n...\n\n// Create a new object that can be safely used however you wish. Calling the\n// function again will simply return a new state object, and no state loss\n// will occur, at the cost of creating more objects.\nhashState = new MurmurHash3();\n```\n\nBoth methods can be mixed however you like if you have different use cases.\n\n---\n\n### MurmurHash3.prototype.hash (string)\nIncrementally add a _string_ to the hash. This can be called as many times as you want for the hash state object, including after a call to `result()`. Returns `this` so calls can be chained.\n\n---\n\n### MurmurHash3.prototype.result ()\nGet the result of the hash as a 32-bit positive integer. This performs the tail and finalizer portions of the algorithm, but does not store the result in the state object. This means that it is perfectly safe to get results and then continue adding strings via `hash`.\n\n```javascript\n// Do the whole string at once\nMurmurHash3('this is a test string').result();\n// 0x70529328\n\n// Do part of the string, get a result, then the other part\nvar m = MurmurHash3('this is a');\nm.result();\n// 0xbfc4f834\nm.hash(' test string').result();\n// 0x70529328 (same as above)\n```\n\n---\n\n### MurmurHash3.prototype.reset (seed)\nReset the state object for reuse, optionally using the given _seed_ (defaults to 0 like the constructor). Returns `this` so calls can be chained.\n\n---\n\nLicense (MIT)\n-------------\nCopyright (c) 2013 Gary Court, Jens Taylor\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\nCOPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "readmeFilename": "README.md", "_id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@0.1.3", "dist": {"shasum": "306c9e3a1562fadae1761a778b82e815f5d8e6e4", "tarball": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.3.tgz", "integrity": "sha512-5yqpIIjRrfVtSGS/RbbjB3xA0xQeelX5m5TUccfDtbKmJqlY4l8Wa6fb41obsmcNYmHYpVtun3kv4K4r6dJJTQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVDPCF6+o/hc/vfhhTJnNgpk70FCth5b0YFj3fWbdD1AIhAIOXxSNNhHtYM3bT6J7cJEeIabx0NwXHnXF9HxuN3T4u"}]}, "_from": ".", "_npmVersion": "1.3.2", "_npmUser": {"name": "j<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.4": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.4", "description": "An incremental implementation of MurmurHash3", "homepage": "https://github.com/jensyt/imurmurhash-js", "main": "imurmurhash.js", "files": ["imurmurhash.js", "imurmurhash.min.js", "package.json", "README.md"], "repository": {"type": "git", "url": "https://github.com/jensyt/imurmurhash-js"}, "bugs": {"url": "https://github.com/jensyt/imurmurhash-js/issues"}, "keywords": ["murmur", "<PERSON><PERSON>h", "murmurhash3", "hash", "incremental"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/homebrewing"}, "license": "MIT", "dependencies": {}, "devDependencies": {}, "engines": {"node": ">=0.8.19"}, "readme": "iMurmurHash.js\n==============\n\nAn incremental implementation of the MurmurHash3 (32-bit) hashing algorithm for JavaScript based on [<PERSON>'s implementation](https://github.com/garycourt/murmurhash-js) with [ka<PERSON><PERSON><PERSON><PERSON>'s modifications](https://github.com/ka<PERSON><PERSON><PERSON>mura/murmurhash-js).\n\nThis version works significantly faster than the non-incremental version if you need to hash many small strings into a single hash, since string concatenation (to build the single string to pass the non-incremental version) is fairly costly. In one case tested, using the incremental version was about 50% faster than concatenating 5-10 strings and then hashing.\n\nInstallation\n------------\n\nTo use iMurmurHash in the browser, [download the latest version](https://raw.github.com/jensyt/imurmurhash-js/master/imurmurhash.min.js) and include it as a script on your site.\n\n```html\n<script type=\"text/javascript\" src=\"/scripts/imurmurhash.min.js\"></script>\n<script>\n// Your code here, access iMurmurHash using the global object MurmurHash3\n</script>\n```\n\n---\n\nTo use iMurmurHash in Node.js, install the module using NPM:\n\n```bash\nnpm install imurmurhash\n```\n\nThen simply include it in your scripts:\n\n```javascript\nMurmurHash3 = require('imurmurhash');\n```\n\nQuick Example\n-------------\n\n```javascript\n// Create the initial hash\nvar hashState = MurmurHash3('string');\n\n// Incrementally add text\nhashState.hash('more strings');\nhashState.hash('even more strings');\n\n// All calls can be chained if desired\nhashState.hash('and').hash('some').hash('more');\n\n// Get a result\nhashState.result();\n// returns 0xe4ccfe6b\n```\n\nFunctions\n---------\n\n### MurmurHash3 ([string], [seed])\nGet a hash state object, optionally initialized with the given _string_ and _seed_. _Seed_ must be a positive integer if provided. Calling this function without the `new` keyword will return a cached state object that has been reset. This is safe to use as long as the object is only used from a single thread and no other hashes are created while operating on this one. If this constraint cannot be met, you can use `new` to create a new state object. For example:\n\n```javascript\n// Use the cached object, calling the function again will return the same\n// object (but reset, so the current state would be lost)\nhashState = MurmurHash3();\n...\n\n// Create a new object that can be safely used however you wish. Calling the\n// function again will simply return a new state object, and no state loss\n// will occur, at the cost of creating more objects.\nhashState = new MurmurHash3();\n```\n\nBoth methods can be mixed however you like if you have different use cases.\n\n---\n\n### MurmurHash3.prototype.hash (string)\nIncrementally add _string_ to the hash. This can be called as many times as you want for the hash state object, including after a call to `result()`. Returns `this` so calls can be chained.\n\n---\n\n### MurmurHash3.prototype.result ()\nGet the result of the hash as a 32-bit positive integer. This performs the tail and finalizer portions of the algorithm, but does not store the result in the state object. This means that it is perfectly safe to get results and then continue adding strings via `hash`.\n\n```javascript\n// Do the whole string at once\nMurmurHash3('this is a test string').result();\n// 0x70529328\n\n// Do part of the string, get a result, then the other part\nvar m = MurmurHash3('this is a');\nm.result();\n// 0xbfc4f834\nm.hash(' test string').result();\n// 0x70529328 (same as above)\n```\n\n---\n\n### MurmurHash3.prototype.reset ([seed])\nReset the state object for reuse, optionally using the given _seed_ (defaults to 0 like the constructor). Returns `this` so calls can be chained.\n\n---\n\nLicense (MIT)\n-------------\nCopyright (c) 2013 Gary Court, Jens Taylor\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\nCOPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "readmeFilename": "README.md", "_id": "im<PERSON><PERSON><PERSON><PERSON><PERSON>@0.1.4", "dist": {"shasum": "9218b9b2b928a238b13dc4fb6b6d576f231453ea", "tarball": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDBYV53bBFjsfNsa4u6aXAPKZ+9co9516ZzIbyHU6H9GAiBXXkJRFrbyt8Y2KjDxx6KfZb8DhPxQB/BkZd2wnfAMEg=="}]}, "_from": ".", "_npmVersion": "1.3.2", "_npmUser": {"name": "j<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}}, "readme": "iMurmurHash.JS\n==============\n\nAn incremental implementation of the MurmurHash3 (32-bit) hashing algorithm for JavaScript based on [<PERSON>'s implementation](https://github.com/garycourt/murmurhash-js).\n\nInstallation\n------------\n\nTo use iMurmurHash in the browser, [download the latest version](https://raw.github.com/jensyt/imurmurhash-js/master/imurmurhash.min.js) and include it as a script on your site.\n\n```html\n<script type=\"text/javascript\" src=\"/scripts/imurmurhash.min.js\"></script>\n<script>\n// Your code here, access iMurmurHash using the global object MurmurHash3\n</script>\n```\n\n---\n\nTo use iMurmurHash in Node.js, install the module using NPM:\n\n```bash\nnpm install imurmurhash\n```\n\nThen simply include it in your scripts:\n\n```javascript\nMurmurHash3 = require('imurmurhash');\n```\n\nQuick Example\n-------------\n\n```javascript\n// Create the initial hash\nvar hashState = MurmurHash3('string');\n\n// Incrementally add text\nhashState.hash('more strings');\nhashState.hash('even more strings');\n\n// All calls can be chained if desired\nhashState.hash('and').hash('some').hash('more');\n\n// Get a result\nhashState.result();\n// returns 0x29d3f1e3\n```\n\nFunctions\n---------\n\n### MurmurHash3 ([string], [seed])\nGet a hash state object, optionally initialized with the given _string_ and _seed_. _Seed_ must be a positive integer if provided. Calling this function without the `new` keyword will return a cached state object that has been reset. This is safe to use as long as the object is only used from a single thread and no other hashes are created while operating on this one. If this constraint cannot be met, you can use `new` to create a new state object. For example:\n\n```javascript\n// Use the cached object, calling the function again will return the same\n// object (but reset, so the current state would be lost)\nhashState = MurmurHash3();\n...\n\n// Create a new object that can be safely used however you wish. Calling the\n// function again will simply return a new state object, and no state loss\n// will occur, at the cost of creating more objects.\nhashState = new MurmurHash3();\n```\n\nBoth methods can be mixed however you like if you have different use cases.\n\n---\n\n### MurmurHash3.prototype.hash (string)\nIncrementally add a _string_ to the hash. This can be called as many times as you want for the hash state object, including after a calls to `result()`. Returns `this` so calls can be chained.\n\n---\n\n### MurmurHash3.prototype.result ()\nGet the result of the hash as a 32-bit positive integer. This performs the tail and finalizer portions of the algorithm, but does not store the result in the state object. This means that it is perfectly safe to get results and then continue adding strings via `hash`.\n\n```javascript\n// Do the whole string at once\nMurmurHash3('this is a test string').result();\n// 0x70529328\n\n// Do part of the string, get a result, then the other part\nvar m = MurmurHash3('this is a');\nm.result();\n// 0xbfc4f834\nm.hash(' test string').result();\n// 0x70529328 (same as above)\n```\n\n---\n\n### MurmurHash3.prototype.reset (seed)\nReset the state object for reuse, optionally using the given _seed_ (defaults to 0 like constructor). Returns `this` so calls can be chained.\n\n---\n\nLicense (MIT)\n-------------\nCopyright (c) 2013 Gary Court, Jens Taylor\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software is furnished to do so,\nsubject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\nFOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\nCOPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\nIN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\nCONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "maintainers": [{"name": "j<PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-06-22T16:32:20.870Z", "created": "2013-07-31T23:41:36.514Z", "0.0.1": "2013-07-31T23:41:37.908Z", "0.1.1": "2013-08-02T13:33:50.247Z", "0.1.2": "2013-08-02T13:38:41.852Z", "0.1.3": "2013-08-02T14:10:27.116Z", "0.1.4": "2013-08-24T20:45:23.169Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/homebrewing"}, "repository": {"type": "git", "url": "https://github.com/jensyt/imurmurhash-js"}, "users": {"meeh": true, "heartnett": true, "flumpus-dev": true}}