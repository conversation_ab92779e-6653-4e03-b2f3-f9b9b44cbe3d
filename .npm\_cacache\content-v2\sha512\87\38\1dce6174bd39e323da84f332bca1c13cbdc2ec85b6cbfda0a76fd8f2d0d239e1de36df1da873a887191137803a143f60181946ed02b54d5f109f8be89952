{"_id": "babel-plugin-jest-hoist", "_rev": "325-1eede9242d887f7d68c033d1abbe28c8", "name": "babel-plugin-jest-hoist", "dist-tags": {"next": "30.0.0-beta.8", "latest": "30.0.1"}, "versions": {"1.0.0": {"name": "babel-plugin-jest-hoist", "version": "1.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@1.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c71f390248e4fc034057ec2c8b1980db24bb4ff0", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-1.0.0.tgz", "integrity": "sha512-MSC4MPQeE420bSIQ1fionJLyqx5/rqDhh7GmeRna7Pb+g+n5AHMew7rBoWiMUOXfnTzuhowaEFpHeBw+kznuHQ==", "signatures": [{"sig": "MEUCIAhNZxtGlQwwqEdqtch4HjIQJOQaytPCGtJ7JJZQ3iceAiEA+jh0bJBRJh+YjL82Y1YHtIZ1RBmaQ+6AD1b1c8a+RRQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testEnvironment": "<rootDir>/node_modules/jest-cli/src/environments/NodeEnvironment"}, "main": "src/index.js", "_from": ".", "_shasum": "c71f390248e4fc034057ec2c8b1980db24bb4ff0", "scripts": {"test": "jest", "prepublish": "npm test"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock` calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest", "directories": {}, "_nodeVersion": "4.2.1", "devDependencies": {"react": "^0.14.0", "jest-cli": "*", "babel-jest": "*", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-1.0.0.tgz_1458164425604_0.8212160009425133", "host": "packages-12-west.internal.npmjs.com"}}, "9.0.3": {"name": "babel-plugin-jest-hoist", "version": "9.0.3", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@9.0.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b4f07099bb3352a42150d8844615c6dd971b48ab", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-9.0.3.tgz", "integrity": "sha512-Dbt4yPrHDbRNtv6gphDSm0es+RyFV/eVXRUAvFQUelFIXnJem9mQC/QD1UpGZdluC++9vi698byJar8smTl3dA==", "signatures": [{"sig": "MEYCIQCNY+g/N1fxnRMIp86dpAB9P9f/tAEbtHtggJmNw4AUPgIhAO1hXVA197wZ2WbP4MCMId27Kh3YhRNRG0Qy30AxD8/u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testEnvironment": "<rootDir>/node_modules/jest-cli/src/environments/NodeEnvironment"}, "main": "src/index.js", "_from": ".", "_shasum": "b4f07099bb3352a42150d8844615c6dd971b48ab", "scripts": {"test": "jest", "prepublish": "npm test"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock` calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest", "directories": {}, "_nodeVersion": "4.2.1", "devDependencies": {"react": "^0.14.0", "jest-cli": "*", "babel-jest": "*", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-9.0.3.tgz_1458695396924_0.5787106873467565", "host": "packages-12-west.internal.npmjs.com"}}, "10.0.0": {"name": "babel-plugin-jest-hoist", "version": "10.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@10.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "45b6db6730554167c494a72a54f921a06bcafcb9", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-10.0.0.tgz", "integrity": "sha512-TGmoZi/XBwTNP9jT6w7BFao+C9evo2YKz6H1xP7GCv+t2+EUAXnUBiFxI4hkmSQuPhuTw65zsP8gsndDm2ri1w==", "signatures": [{"sig": "MEUCIBRYUV3NlFXazY2c3CHJ+0uJl5mfFP1u5+YZlpeoRuD1AiEAgNzpmFrz/7FOhLWzftZ7PWv1N+cr6EraW0RJ5oFfvaw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testEnvironment": "<rootDir>/../../src/environments/NodeEnvironment"}, "main": "src/index.js", "_from": ".", "_shasum": "45b6db6730554167c494a72a54f921a06bcafcb9", "scripts": {"test": "../../bin/jest.js", "prepublish": "npm test"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock` calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest", "directories": {}, "_nodeVersion": "4.2.1", "devDependencies": {"react": "^0.14.0", "babel-jest": "*", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-10.0.0.tgz_1459378034610_0.9973439981695265", "host": "packages-12-west.internal.npmjs.com"}}, "10.0.1": {"name": "babel-plugin-jest-hoist", "version": "10.0.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@10.0.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "34f4989ef9df56a21b7e67e4e88dbd336e8d3f66", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-10.0.1.tgz", "integrity": "sha512-4unaSW0w6FeOdP7tgirjDb52bQmlGK3BYdmdMLErnuZlaA3tsGna4d0hoamkhevjuJXDKxGsmKUk2W660H9Lxg==", "signatures": [{"sig": "MEUCIQC5+uczSdueqN4v7guyRz63C2IxXhIBYge6qQa/doK/FAIgHJ/5gQF6/hc8Hv0VIdsxyHRpQWQMGbYCnoZwVLfZ3Ww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testEnvironment": "<rootDir>/../../src/environments/NodeEnvironment"}, "main": "src/index.js", "_from": ".", "_shasum": "34f4989ef9df56a21b7e67e4e88dbd336e8d3f66", "scripts": {"test": "../../bin/jest.js", "prepublish": "npm test"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock` calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest", "directories": {}, "_nodeVersion": "4.2.1", "devDependencies": {"react": "^0.14.0", "babel-jest": "*", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-10.0.1.tgz_1459381439899_0.7797783007845283", "host": "packages-12-west.internal.npmjs.com"}}, "10.0.2": {"name": "babel-plugin-jest-hoist", "version": "10.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@10.0.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a1b1dcf8e9abdfa7a8849a15b295e8f6bc60c4b3", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-10.0.2.tgz", "integrity": "sha512-jO4e6e7+9pwxUl+/P7wueppAU0u6YeB5yMn9HjT/w+PIBmbgE0835GqCvzBMCgi3xQS0EGKdUjUyJiocEsCvBQ==", "signatures": [{"sig": "MEUCIQDhx4nYfibI8mQG21hkBIhIHEQXpAcjZ6JlDgdSqxmAqAIgZwDFjKJsdZiq1mtXnAfDfA1gRaAMXAGNV4zePoyiG8U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testEnvironment": "../jest-environment-node", "scriptPreprocessor": "../babel-jest"}, "main": "src/index.js", "_from": ".", "_shasum": "a1b1dcf8e9abdfa7a8849a15b295e8f6bc60c4b3", "scripts": {"test": "../../bin/jest.js", "prepublish": "npm test"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock` calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest", "directories": {}, "_nodeVersion": "4.2.1", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-10.0.2.tgz_1460363052541_0.06863606558181345", "host": "packages-12-west.internal.npmjs.com"}}, "11.0.0": {"name": "babel-plugin-jest-hoist", "version": "11.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@11.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b0b9bc2cdae2b654ca0f18933184165ea49f8f7e", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-11.0.0.tgz", "integrity": "sha512-cJK6rQ6LnD+o0sOhKVQHA9+PWavjHyLIOLdYBGEfFHBnStU09krdN3nceAyliwcNt0DeyIPsfk+VvGKP4e5W2g==", "signatures": [{"sig": "MEQCIHyliM7D2TDwY2NTklY4Fs99la44++SjUUuO3MJ2WELAAiAXeCC0o+PRa/GwKnPo2mJ1UD2wgNiz7YKEFpzj4JbReQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testEnvironment": "../jest-environment-node", "scriptPreprocessor": "../babel-jest"}, "main": "src/index.js", "_from": ".", "_shasum": "b0b9bc2cdae2b654ca0f18933184165ea49f8f7e", "scripts": {"test": "node -e \"const spawn = require('child_process').spawn, path=require('path'); spawn('node', [path.resolve('../../bin/jest.js')], {stdio:'inherit'})\"", "prepublish": "npm test"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock` calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest", "directories": {}, "_nodeVersion": "4.2.1", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-11.0.0.tgz_1460447777427_0.5352613478899002", "host": "packages-16-east.internal.npmjs.com"}}, "11.0.1": {"name": "babel-plugin-jest-hoist", "version": "11.0.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@11.0.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "59451b70a613aaa8d09ca49a998bfd267d592a5a", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-11.0.1.tgz", "integrity": "sha512-G1rjdGzIDuscH2B6CBMaGAwNBsmXfn63X4WZqu+jKA+f+NzHRVd5l6cPiX5V7XbRpzZoWsULTUTKHFICHc+LfQ==", "signatures": [{"sig": "MEUCIHQN13wNJQCuJVy93EUBlz2SjqZhNt9mFEOz7mab5y9fAiEA3jepahOLhvIu3i2ZSMrvxRNeR6T7UQ22MA2equE57yA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testEnvironment": "../jest-environment-node", "scriptPreprocessor": "../babel-jest"}, "main": "src/index.js", "_from": ".", "_shasum": "59451b70a613aaa8d09ca49a998bfd267d592a5a", "scripts": {"test": "node -e \"const spawn = require('child_process').spawn, path=require('path'); spawn('node', [path.resolve('../../bin/jest.js')], {stdio:'inherit'})\"", "prepublish": "npm test"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock` calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest", "directories": {}, "_nodeVersion": "5.10.1", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-11.0.1.tgz_1460692949305_0.34537630854174495", "host": "packages-16-east.internal.npmjs.com"}}, "11.0.2": {"name": "babel-plugin-jest-hoist", "version": "11.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@11.0.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d75ff771d7e22987ba97eb72e3817058567b3808", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-11.0.2.tgz", "integrity": "sha512-kCsZQoc/75Pf5291BOOdYHBb44ioKgmDjbP2SP5GMfgBMeN15e35hMlnbrizDzqOals3UlP07vbm8Pu1awnweg==", "signatures": [{"sig": "MEQCIB8a71Vyq8+fDb4jDn4ME5IizctHSXukeqvagPZyqjgbAiBIIe+NE2ZJIAIocyBu1RAPK9euUdlc7aD42dTfhrROaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testEnvironment": "../jest-environment-node", "scriptPreprocessor": "../babel-jest"}, "main": "src/index.js", "_from": ".", "_shasum": "d75ff771d7e22987ba97eb72e3817058567b3808", "scripts": {"test": "node -e \"const spawn = require('child_process').spawn, path=require('path'); spawn('node', [path.resolve('../../bin/jest.js')], {stdio:'inherit'})\"", "prepublish": "npm test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "2.14.12", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock` calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest", "directories": {}, "_nodeVersion": "4.3.1", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-11.0.2.tgz_1460952201909_0.34950387035496533", "host": "packages-12-west.internal.npmjs.com"}}, "12.0.0": {"name": "babel-plugin-jest-hoist", "version": "12.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@12.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0e5dbf5d23c854e98f876160441c6800c5fab7ef", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-12.0.0.tgz", "integrity": "sha512-<PERSON>bJjkMucROpy2c0kbpvVxsZyVjwJlqTIVK6vpCVnbvm8qHn9DU3kirYl3dYWTflI9Hc+/jGzbyqKq4POyYJSuw==", "signatures": [{"sig": "MEYCIQCjvWlVmn6Nbf/BTynKQuRo98WOxXPH75bgiusih49WyQIhAOAgZ6QWIJZBVB1BGnYbFNqy3aeTIFuJSZVsK16vZVaN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testEnvironment": "node", "scriptPreprocessor": "../babel-jest"}, "main": "src/index.js", "_from": ".", "_shasum": "0e5dbf5d23c854e98f876160441c6800c5fab7ef", "scripts": {"test": "../../bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock` calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest", "directories": {}, "_nodeVersion": "5.11.0", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-12.0.0.tgz_1461734650146_0.260187670821324", "host": "packages-16-east.internal.npmjs.com"}}, "12.0.1": {"name": "babel-plugin-jest-hoist", "version": "12.0.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@12.0.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "07852d5181f5e34e73f220c80094893136800750", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-12.0.1.tgz", "integrity": "sha512-jJGOoJm8QU4HtAr9O8jQP/WDB6ml3GgmiTTKOTiFtVt+eHsPsPbYbAvv/euFAVzo2USWBlPGaHV0rIPUaEsEWw==", "signatures": [{"sig": "MEUCIQDQwf/s5e/MKGjAk7RJzK5/waqzIBm5rv0l7dpBhCJB4QIgBD5tESx9ptVvqJ8RJJWIoP280mhG0+A/VBfG7mAu9YI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testEnvironment": "node", "scriptPreprocessor": "../babel-jest"}, "main": "src/index.js", "_from": ".", "_shasum": "07852d5181f5e34e73f220c80094893136800750", "scripts": {"test": "../../bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock` calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest", "directories": {}, "_nodeVersion": "5.11.0", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-12.0.1.tgz_1461751775786_0.707614035345614", "host": "packages-16-east.internal.npmjs.com"}}, "12.0.2": {"name": "babel-plugin-jest-hoist", "version": "12.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@12.0.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "585b98bf566a6183b15facf7b1c0e42ee299139c", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-12.0.2.tgz", "integrity": "sha512-k4CmqQRyYKQVNF9jSPCAEadZTo8iSxJ4O8Y4C8Uj3wyI/rgMfvOjo2rIFsxBaraUFgd98wnhkmoJMX8PFzDmqg==", "signatures": [{"sig": "MEUCIQDL8Fb6HL4e1Zd4bLZpysIoRKurOOU1bOjqQPkIWc7u7AIgUeh/ht85K5Qn/TpoUALqAYiUKac5rqgh+sltoTzaBvo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testEnvironment": "node", "scriptPreprocessor": "../babel-jest"}, "main": "src/index.js", "_from": ".", "_shasum": "585b98bf566a6183b15facf7b1c0e42ee299139c", "scripts": {"test": "../../bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock` calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest", "directories": {}, "_nodeVersion": "6.0.0", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-12.0.2.tgz_1461817899039_0.7220230461098254", "host": "packages-12-west.internal.npmjs.com"}}, "12.1.0": {"name": "babel-plugin-jest-hoist", "version": "12.1.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@12.1.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4e0d0a2fd723c4f9ab01a1606daa15bf36bd7083", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-12.1.0.tgz", "integrity": "sha512-DDc3dgXd9Bz53W3BppSHIPhDom429Gu2OInL84qwEUxZOT4/PTZ9VKdhrnOyyYnMt3dRXoTqZNb2uE1hOA0YmQ==", "signatures": [{"sig": "MEQCIC/SHtd7iM0MezaUVbU5k7Y3lyZBegaUttW/s/a2eEpbAiAwrFSidrYbWqEGvbSOLG96p81LsF8xWBcs+hCu75ue4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"testEnvironment": "node", "scriptPreprocessor": "../babel-jest"}, "main": "src/index.js", "_from": ".", "_shasum": "4e0d0a2fd723c4f9ab01a1606daa15bf36bd7083", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock` calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jest", "directories": {}, "_nodeVersion": "6.0.0", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-12.1.0.tgz_1463767563271_0.3822157788090408", "host": "packages-12-west.internal.npmjs.com"}}, "12.1.1-alpha.2935e14d": {"name": "babel-plugin-jest-hoist", "version": "12.1.1-alpha.2935e14d", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@12.1.1-alpha.2935e14d", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f5d5f19283d076c9ace436bb70f85c3f761e9319", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-12.1.1-alpha.2935e14d.tgz", "integrity": "sha512-Pv1lcLturYUlc5Tdq0uDtMyHcsTQHQ+V8d8M1lauG4DsZVNDWUHPef/4BBGXAZymD+7VdLejz6ynr0yRuEWFBw==", "signatures": [{"sig": "MEQCIHXCaAiXGGXmc0iMAcAiH4pesHKSMrnzQx+v5IZxkAgXAiBSxAR6dElkU83E4/eyRaV/IdZKBnrhfr6PmBk3dF3DDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./build", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "f5d5f19283d076c9ace436bb70f85c3f761e9319", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.0.0", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-12.1.1-alpha.2935e14d.tgz_1466141592510_0.6303157061338425", "host": "packages-16-east.internal.npmjs.com"}}, "12.1.2-alpha.a482b15c": {"name": "babel-plugin-jest-hoist", "version": "12.1.2-alpha.a482b15c", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@12.1.2-alpha.a482b15c", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "76fcb13f1fb16cadd58dea13eca7436f035bd09e", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-12.1.2-alpha.a482b15c.tgz", "integrity": "sha512-6XyGaK+u0x0i6++6ceIfIxLm/UfBU0FWr0SKP/nv5mi/CX66l4sDNzUowLV68hFoAkIDZv7/Ht2RiSI4NfRwzw==", "signatures": [{"sig": "MEYCIQCnrzD3R+vsOeQqI45kX2Q9+kMOz0CourXVUPTSL1LLPAIhAIhb5yjGhdNicBen5OwEcfQhjFQ4SXWiG2HcNXGJOnaq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./build", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "76fcb13f1fb16cadd58dea13eca7436f035bd09e", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.0.0", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-12.1.2-alpha.a482b15c.tgz_1466150283149_0.03131375857628882", "host": "packages-12-west.internal.npmjs.com"}}, "12.1.2-alpha.6230044c": {"name": "babel-plugin-jest-hoist", "version": "12.1.2-alpha.6230044c", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@12.1.2-alpha.6230044c", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "868ecfc27c47030e13b9ef7cc9294cc650168cab", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-12.1.2-alpha.6230044c.tgz", "integrity": "sha512-AK7qKTLKnODEx3gdh6cXPO4+OMB5B1K6gMqmaLhHwWHnV4a20yCsQRgcKqA0PdAkeaUv40UlUk9pKsdfxvVkEg==", "signatures": [{"sig": "MEQCIB6gjrrv/NISxwjLVzhlO7NOtKTz56LbADk6gmftS7GyAiAj4puvAGuEsMUMhwVUcluUbz/+GsTQKlchLgTeM9eFqw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./build", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "868ecfc27c47030e13b9ef7cc9294cc650168cab", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.0.0", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-12.1.2-alpha.6230044c.tgz_1466536514548_0.3582484549842775", "host": "packages-12-west.internal.npmjs.com"}}, "12.1.3-alpha.6230044c": {"name": "babel-plugin-jest-hoist", "version": "12.1.3-alpha.6230044c", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@12.1.3-alpha.6230044c", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "914ec03c80e85c03d9b0a1044005b2018cb3923c", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-12.1.3-alpha.6230044c.tgz", "integrity": "sha512-SrneyG3Ye9+6RxjPGNv7UL/OSLDWrD3/6D8MX2SFff5VrAY8j4GQcLzU9/MJ3p6pICX/pZ63oU0dGxvXh3Npdg==", "signatures": [{"sig": "MEYCIQDr866PEKua7FFXjodrHzMPXvCWfugzL1+/foPWV5M7FAIhAJlm/Qv9kDSKE74hQUDkxToxDJ9FxhqAXs8lib/fsUtC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./build", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "914ec03c80e85c03d9b0a1044005b2018cb3923c", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.0.0", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-12.1.3-alpha.6230044c.tgz_1466539907338_0.6450229780748487", "host": "packages-16-east.internal.npmjs.com"}}, "12.1.4-alpha.a737c6e5": {"name": "babel-plugin-jest-hoist", "version": "12.1.4-alpha.a737c6e5", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@12.1.4-alpha.a737c6e5", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "30951b5446c0477d2ab7612ad4f5a288477b0df9", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-12.1.4-alpha.a737c6e5.tgz", "integrity": "sha512-ZNYeJswXR07+NwnYQVIn5/YVcjs0xF3kKtGQej++3Lm1NHdXuqUJ+TATHWmV7jZLgANUUZl2z3oMjLfYBuqQkQ==", "signatures": [{"sig": "MEUCIQCoSk0yvfWosvps1R2Fl1BlvvK7mRUVW0s3A+OmPZaJaAIgc8T86a+fRF5jCqk7BEnTH+p377yfVWVOI6teJCFIKnY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./build", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "30951b5446c0477d2ab7612ad4f5a288477b0df9", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.0.0", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-12.1.4-alpha.a737c6e5.tgz_1466566601824_0.5048077614046633", "host": "packages-12-west.internal.npmjs.com"}}, "12.1.5-alpha.b5322422": {"name": "babel-plugin-jest-hoist", "version": "12.1.5-alpha.b5322422", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@12.1.5-alpha.b5322422", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "eb136e1529cba8454f8961bea4d5115179848a7d", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-12.1.5-alpha.b5322422.tgz", "integrity": "sha512-8BrxJj3hs0EO+BKgvosbRI2Ata+Wl3DVMi1KpIQeQGYvooXdQiAz45ThlxCDiyRwS1XAPm8kiITZidUqmo6XoQ==", "signatures": [{"sig": "MEYCIQDiK/WXChOZo3V8bptkFt1jrPamm5EIL5c51wfC22GCYAIhAP2mzrOTpfBqNgv+QR6Gd3qoF1Riqr1wkHEnOd5mHMLW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./build", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "eb136e1529cba8454f8961bea4d5115179848a7d", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.0.0", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-12.1.5-alpha.b5322422.tgz_1466577948200_0.7224099109880626", "host": "packages-12-west.internal.npmjs.com"}}, "13.0.0": {"name": "babel-plugin-jest-hoist", "version": "13.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@13.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fe2980fc780d15ce499b8d8e93e45020db3f24dc", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-13.0.0.tgz", "integrity": "sha512-oDRGy2Qlbdpe/TZJxdXTuQrcKxWcWfcHOMTkxzgdM5Uog4Rzjt+suJoKF6LrODJAgqD53ddagiroqQOU2FuJFw==", "signatures": [{"sig": "MEYCIQCQKn776XLBeG8Mw1G1RBC5Uaw2WQh0PNfYte7OVSmoywIhALt5PIqggSDGQEE6KHaKG7nR4zr4fpyEMMe3NXlWepoX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./build", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "fe2980fc780d15ce499b8d8e93e45020db3f24dc", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.0.0", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-13.0.0.tgz_1466626696179_0.9970591606106609", "host": "packages-12-west.internal.npmjs.com"}}, "13.2.1": {"name": "babel-plugin-jest-hoist", "version": "13.2.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@13.2.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d87f8ef03a85c7fa2dc082d53f6d97516ea75482", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-13.2.1.tgz", "integrity": "sha512-wnb9weth/5ENqGrj1vSGqdWGfeJ7roBfcbtsxaqiZk9+d8LFykKkQ2/kbOmMEA0xnTNpKXAY3skkZvE+wDOo7g==", "signatures": [{"sig": "MEUCIQC/id+8yM0SnrgnXc4Lyue5qqbD0T3m8E7TszkobUwVqwIgM78VKFRfsWxZPgBnYiRFk+D894KVE5Wn5ZoJdp5/WG4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./build", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "d87f8ef03a85c7fa2dc082d53f6d97516ea75482", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-13.2.1.tgz_1467856774824_0.913418407086283", "host": "packages-12-west.internal.npmjs.com"}}, "13.2.2": {"name": "babel-plugin-jest-hoist", "version": "13.2.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@13.2.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cbaf05efd43d1871b38ed24ae642278d86c27f8f", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-13.2.2.tgz", "integrity": "sha512-MlxP0QTLTGrsZONY3uaM4ZY9k+YD3jfhs5+k4IkAeZi4ZSOnI8EHfZU2ox076ErpbO3rVfDVrKuxSckQFpRMxg==", "signatures": [{"sig": "MEQCIE8r9S/SlQLR4cPFAxAlmY4TWEPDRahOUcZPxeMCzM0zAiAeRawGSobXjoAZagocFA/N1/q4rB5a4Gp7Vf5qpW7j1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./build", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "cbaf05efd43d1871b38ed24ae642278d86c27f8f", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-13.2.2.tgz_1467857580408_0.3163878256455064", "host": "packages-12-west.internal.npmjs.com"}}, "13.3.0-alpha.a44f195f": {"name": "babel-plugin-jest-hoist", "version": "13.3.0-alpha.a44f195f", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@13.3.0-alpha.a44f195f", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4f3078eef5e1f876de8734633aaa9117cd840b7c", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-13.3.0-alpha.a44f195f.tgz", "integrity": "sha512-ckqsv3TDjJnHDf1YWTPw1XOWmGeTx8OiyjQwv5jhm3In1xGuUcLO054IM0ABG3gC/qznlVUSP4H8C4MfZ8F7tw==", "signatures": [{"sig": "MEUCIQCN2kykfNkQjyJuf0m09yh067lUJcmnORxLzf9lpkfEdwIgXcuuec4LQpojZxcxTxsN4JOpxs0UnQKwHSrd8fH4nzU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./src", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "4f3078eef5e1f876de8734633aaa9117cd840b7c", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-13.3.0-alpha.a44f195f.tgz_1468231007797_0.8205949659459293", "host": "packages-12-west.internal.npmjs.com"}}, "13.3.0-alpha.4eb0c908": {"name": "babel-plugin-jest-hoist", "version": "13.3.0-alpha.4eb0c908", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@13.3.0-alpha.4eb0c908", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a0157f999ea79a67763e2c1c0012bdee147b6a80", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-13.3.0-alpha.4eb0c908.tgz", "integrity": "sha512-dhdEVSj96R/4JeNuFdQW+1lMqZomHC8IlEi3w2DXTNrU7fv2Wvx5sOW3g6Vlg46cbhptWhV6r+sILLzF9v6y4g==", "signatures": [{"sig": "MEYCIQDalEOi3C6pkROYpboo6C0k6Rm+TqFOKhbI5nIFGZO5OQIhAORKn5AOXj47e9RuT7cNXlDIDtToOdYWLkk5IfomhAk6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./src", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "a0157f999ea79a67763e2c1c0012bdee147b6a80", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-13.3.0-alpha.4eb0c908.tgz_1468231471490_0.24476458155550063", "host": "packages-12-west.internal.npmjs.com"}}, "13.2.3-alpha.ffc7404b": {"name": "babel-plugin-jest-hoist", "version": "13.2.3-alpha.ffc7404b", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@13.2.3-alpha.ffc7404b", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "70020780d229c47b65c7583c609d29cf54a629e8", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-13.2.3-alpha.ffc7404b.tgz", "integrity": "sha512-gG3wBOf+8ETJhwya4ST9buuUrNu9hh22nSuIYRibiv8pHbAj/hGI3tNYt2Qqk6FUvokDGeIP1YzG3Du8GqQr2Q==", "signatures": [{"sig": "MEUCIQDtCQkiOpqPA6r1YDca+e5MFfjg5Tf3fF7vKmzZDzdlEgIgRVAh/3SMt/RKoQ57cCG4ZYik4jXywCTQH8ebUoY6SX8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./src", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "70020780d229c47b65c7583c609d29cf54a629e8", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-13.2.3-alpha.ffc7404b.tgz_1468232399337_0.7159421376418322", "host": "packages-12-west.internal.npmjs.com"}}, "13.3.0-alpha.ffc7404b": {"name": "babel-plugin-jest-hoist", "version": "13.3.0-alpha.ffc7404b", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@13.3.0-alpha.ffc7404b", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d9f11a17a6fa74cc73269919467248b150df038e", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-13.3.0-alpha.ffc7404b.tgz", "integrity": "sha512-3+6gE46Npr3e63qL0FNbAFkAFT4F0UdDHMFXJSD5c0OrgHNEuYsRunP7k+mXJL2T3ukbXfEaHYCQAhvLuDDgzw==", "signatures": [{"sig": "MEUCIQCdXUreZ4UyIYLbha32i6JAjLo2Z9xBBydCGy7GyJw/1wIgQqIDDLhP+oZFEK6A34yr0QpXKrX5HBJGYABgzDuhndc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./src", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "d9f11a17a6fa74cc73269919467248b150df038e", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-13.3.0-alpha.ffc7404b.tgz_1468232410004_0.7830219746101648", "host": "packages-12-west.internal.npmjs.com"}}, "13.3.0-alpha.8b48d59e": {"name": "babel-plugin-jest-hoist", "version": "13.3.0-alpha.8b48d59e", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@13.3.0-alpha.8b48d59e", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "96f4a8724619c774721a8cbd4289129bc2503fce", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-13.3.0-alpha.8b48d59e.tgz", "integrity": "sha512-JLFIqNXpfqCqxbA93cyuE0Q027cnGyOFyZorLTfr1b+zET8xY64gMPYjDgCQxUuGbjhzxdVIckotzxhnnXVdzg==", "signatures": [{"sig": "MEUCIQDpqYdWXw8YqNtaaYa6VECSVdfCJl4kHkskTOcCBm8VeQIgT8gxGDu0MtH8HhbwFIo4eDk2PPQVcK6tJANsCSnUi3Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./src", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "96f4a8724619c774721a8cbd4289129bc2503fce", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-13.3.0-alpha.8b48d59e.tgz_1468390445083_0.9583935709670186", "host": "packages-12-west.internal.npmjs.com"}}, "13.3.0-alpha.g8b48d59": {"name": "babel-plugin-jest-hoist", "version": "13.3.0-alpha.g8b48d59", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@13.3.0-alpha.g8b48d59", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e5aa531c136c7ee68044ebdb86d50b02d1812a29", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-13.3.0-alpha.g8b48d59.tgz", "integrity": "sha512-/r81GZgclHugjyhCCOGAilKTd7hlzQL+KwGFIAFRUEDP+SvYnM9GcyPSMCZTnQt9p/SCQIgIbFqkdiqaJ/aQXQ==", "signatures": [{"sig": "MEQCIHrIDkCGuiFCa5pimWSYw/OeeJDX6nZF43J2meTRsSKaAiAd02X5tdM+fx+hpXewyUIdjHRzfQZQxJcwqKtvgwP/2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./src", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "e5aa531c136c7ee68044ebdb86d50b02d1812a29", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-13.3.0-alpha.g8b48d59.tgz_1468391978193_0.17267933674156666", "host": "packages-12-west.internal.npmjs.com"}}, "13.4.0-alpha.d2632006": {"name": "babel-plugin-jest-hoist", "version": "13.4.0-alpha.d2632006", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@13.4.0-alpha.d2632006", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b7116479e2ce53b3273f6de2e690df9f0b240091", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-13.4.0-alpha.d2632006.tgz", "integrity": "sha512-OONJhdy72Nu5h4wYMGaTAe+OlVEc3w2/w3FC/BDc2GwEFiLS0A+T+Z9Cgh1XB+6+NgEWMhRYCupnczx4Nndq+w==", "signatures": [{"sig": "MEYCIQCGkycTEBCeeJPOmJBGV5OOUn+e5SqEFMNVw1hmYz76ygIhANExnvKS+205wLYn9vwt0BxN8BRx9EOgc0ZISx1+x3K5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./src", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "b7116479e2ce53b3273f6de2e690df9f0b240091", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-13.4.0-alpha.d2632006.tgz_1469609830905_0.45259326323866844", "host": "packages-16-east.internal.npmjs.com"}}, "14.0.0": {"name": "babel-plugin-jest-hoist", "version": "14.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@14.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b19078e3799b180ddd1585932c59df02007bf886", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-14.0.0.tgz", "integrity": "sha512-qRTJmCxBMXLIKZnc+vFTBO7widJCXTWl4jzhgpWvgjFtSYRBSsXFYyPQc12/nJvE4h/jR07A40OxAXWn7YONXw==", "signatures": [{"sig": "MEQCIC38oa+8JYg2PZcNC0lnBtg7rAMhpSV3gil1ysYP8a2pAiBQBZYS8oP8tDDoL4JTR/N6//W7oj33+EtbDl8oJtzU0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./src", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "b19078e3799b180ddd1585932c59df02007bf886", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-14.0.0.tgz_1469610876859_0.19585225963965058", "host": "packages-12-west.internal.npmjs.com"}}, "14.1.0": {"name": "babel-plugin-jest-hoist", "version": "14.1.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@14.1.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8588adcba50409e39563ca45f4eb3ec4fabcbd7d", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-14.1.0.tgz", "integrity": "sha512-OH59UUVzVbielmXfWVrQiv2JjRPlXk8EIUaBnddqE4lamEUG+L9ZmOPabIYFjQEXSzKwmfYELNPImyZuyQ8s6A==", "signatures": [{"sig": "MEYCIQC7grd/k8vuZZvNZjqKvlCdF2/D2caSblrlqOvDEST/pAIhAPawkm7mzXjymf9HCp+vkc6Kjf2LqcOimhyr9dBUS58l", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./src", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "8588adcba50409e39563ca45f4eb3ec4fabcbd7d", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.3.0", "devDependencies": {"react": "^0.14.0", "babel-preset-es2015": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-14.1.0.tgz_1470047189352_0.16077166027389467", "host": "packages-16-east.internal.npmjs.com"}}, "14.1.1": {"name": "babel-plugin-jest-hoist", "version": "14.1.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@14.1.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6d6bbd09ba2441e707308bab32521d3ad5c7536d", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-14.1.1.tgz", "integrity": "sha512-47STv58Xc9aHpqk5VMvNQMnC1kh+WOe1NAILHM2A0zigpRsIwVR1SCV582MRHcYC/3TBuX8/eeKZHoIN7jATXA==", "signatures": [{"sig": "MEYCIQCao9jpsLMsPkjMB9tls464QqVDCvYL2+2FKWtQV2jsOwIhAOmOjfS7dtYVxT8BGlFdWsKhsQua6oDCRe19AlSfa8Jv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"rootDir": "./src", "testEnvironment": "node", "scriptPreprocessor": "../../babel-jest"}, "main": "build/index.js", "_from": ".", "_shasum": "6d6bbd09ba2441e707308bab32521d3ad5c7536d", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.3.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-14.1.1.tgz_1470299747779_0.408423516433686", "host": "packages-16-east.internal.npmjs.com"}}, "14.2.0-alpha.ca8bfb6e": {"name": "babel-plugin-jest-hoist", "version": "14.2.0-alpha.ca8bfb6e", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@14.2.0-alpha.ca8bfb6e", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "914ce197d11ee589968ac418f716c0cd851a568e", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-14.2.0-alpha.ca8bfb6e.tgz", "integrity": "sha512-XV+if7NP2SFSpyPuE3tsZ3o6kPj3jaOLcKREhUbnsak9pJdCCbgWYOPQvOFfHoUCAtMyrFFs4J0S4grHpQJCrQ==", "signatures": [{"sig": "MEUCIFYjuQSigfqBdgFeFmSsH4J3i1oJ/lBP2+Kw/buDPpYpAiEA8MDngPfADTmmNBEzNbzaVOiotvJowJUuSs26Pcn3nTg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "914ce197d11ee589968ac418f716c0cd851a568e", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.3.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-14.2.0-alpha.ca8bfb6e.tgz_1471287273420_0.625320257851854", "host": "packages-12-west.internal.npmjs.com"}}, "14.2.1-alpha.e21d71a4": {"name": "babel-plugin-jest-hoist", "version": "14.2.1-alpha.e21d71a4", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@14.2.1-alpha.e21d71a4", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3144c352ef48454e895bf8f1368b26bc8b34feb3", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-14.2.1-alpha.e21d71a4.tgz", "integrity": "sha512-iZaxLxMdQxvs7AvXb3KcVll2loqRM9CJb8T1BlfwefVthEsMfqcpL3rP751JgFUStxON4/BTHd5VcxwqTERH5A==", "signatures": [{"sig": "MEQCIB5np5wcp6zSkJCkSAcLDSe6tiP6Hj/lW2+MLY2abAidAiBIf3sQ6vLGgTBxGHrfMa9IY5AeF+qSmJhghS8tZwjK+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "3144c352ef48454e895bf8f1368b26bc8b34feb3", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.3.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-14.2.1-alpha.e21d71a4.tgz_1471382313927_0.552253877511248", "host": "packages-12-west.internal.npmjs.com"}}, "14.2.2-alpha.22bd3c33": {"name": "babel-plugin-jest-hoist", "version": "14.2.2-alpha.22bd3c33", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@14.2.2-alpha.22bd3c33", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "68010195868c7004d305ddd793669d0a29cb0af1", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-14.2.2-alpha.22bd3c33.tgz", "integrity": "sha512-9/P2aHTM3tXsebDPbzU+Pd8W7HrHMFJmKY/RxoyvSSdR0+fPrnCX8Ne/4WVGLENKn4Dj/5711UCSOR4Fiv2axA==", "signatures": [{"sig": "MEUCIQDqneCyaXUnmVHcucWzOtKyDDLlYrkkbtCZ7FELXdZF9QIgaoSZzVKKaoFj7Mu1wBom2KaOxfUxkBp1WBxJvsHlFVE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "68010195868c7004d305ddd793669d0a29cb0af1", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.3.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-14.2.2-alpha.22bd3c33.tgz_1471388086085_0.38253147481009364", "host": "packages-16-east.internal.npmjs.com"}}, "14.3.0-alpha.d13c163e": {"name": "babel-plugin-jest-hoist", "version": "14.3.0-alpha.d13c163e", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@14.3.0-alpha.d13c163e", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0bd7d11542f93f43cc99ff46c07a184683c49b3a", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-14.3.0-alpha.d13c163e.tgz", "integrity": "sha512-ls65657ZZHtSoOkXdAsdMisen8pGobnRQJutL8UQcGvJY0mfwFpRM52bZ1D3OmygOg+Sbz4yFJEhdOQfl+k4CQ==", "signatures": [{"sig": "MEQCIGg0bGblKY52477dMewm3BctSvVJY31351B8JWBG7DTgAiAUuW+jpWu8+ADmNONWBVybvPlQgyzsPZRhJ2xX1RodTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "0bd7d11542f93f43cc99ff46c07a184683c49b3a", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.3.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-14.3.0-alpha.d13c163e.tgz_1471552521762_0.027674278244376183", "host": "packages-16-east.internal.npmjs.com"}}, "14.3.1-alpha.410cb91a": {"name": "babel-plugin-jest-hoist", "version": "14.3.1-alpha.410cb91a", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@14.3.1-alpha.410cb91a", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "90f454ea2fb13d43e460247a34740079c9525568", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-14.3.1-alpha.410cb91a.tgz", "integrity": "sha512-TwuaFHsgpnyUZtcY2g1ul2ezBJZdWZNdWAYeOw03/qjVV26OM8TcZnh6nyk1GkIVY+jbrf9fqk35cjSk+T0CSA==", "signatures": [{"sig": "MEUCIQDkeMnZcnFMxjfIfhzN7UzMfYKiCQxBVJdX5ML7PBOcHQIge9wBeFo1bclPkwoQrlKrBEB9ZByb+8xMe3R+Q3jYn4w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "90f454ea2fb13d43e460247a34740079c9525568", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.5.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-14.3.1-alpha.410cb91a.tgz_1472593923280_0.8586636576801538", "host": "packages-16-east.internal.npmjs.com"}}, "14.3.2-alpha.83c25417": {"name": "babel-plugin-jest-hoist", "version": "14.3.2-alpha.83c25417", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@14.3.2-alpha.83c25417", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "837c7ea9b95febcd43a5d8defad5ef57613dd5e8", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-14.3.2-alpha.83c25417.tgz", "integrity": "sha512-/Rv130j382931fJhgCJlsG3/wvaoT0m4dHX9tz7C/MLt8K5lUEd4l1kolGQwpRzALLF+a52An5tWn7pV75ABcA==", "signatures": [{"sig": "MEUCIAp/u2e0Kx0GmLvF+i1WMv3AZt4DZbqRmmdmh+0hl/fHAiEApd+4XYCvwfg91fNtt3wP3n8N0B1k31IoSFHL8m/XA+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "837c7ea9b95febcd43a5d8defad5ef57613dd5e8", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.5.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-14.3.2-alpha.83c25417.tgz_1472669431048_0.11415125639177859", "host": "packages-16-east.internal.npmjs.com"}}, "15.0.0": {"name": "babel-plugin-jest-hoist", "version": "15.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@15.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7b2fdbd0cd12fc36a84d3f5ff001ec504262bb59", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-15.0.0.tgz", "integrity": "sha512-shto3Xj9EiT94KaydxbK1eBWKXidvjmDF5f5Y7/4FefIC5noBSiKHpStVgTCQOK2LNWbATQvvnWcarCHvJkNxA==", "signatures": [{"sig": "MEUCIQCvdsFbatXSKAmaIPXk56Pxewvpt6iT+ZpGT0BKYJIxXAIgFnLSFAHlHRIg8usVlP9hCrkJ4/ialNCBj0WHTYJRJTA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "7b2fdbd0cd12fc36a84d3f5ff001ec504262bb59", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.5.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-15.0.0.tgz_1472669723950_0.19455887656658888", "host": "packages-12-west.internal.npmjs.com"}}, "15.2.0-alpha.c681f819": {"name": "babel-plugin-jest-hoist", "version": "15.2.0-alpha.c681f819", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@15.2.0-alpha.c681f819", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2bd39ad3371f8d96929fbfac05f7409bba89225a", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-15.2.0-alpha.c681f819.tgz", "integrity": "sha512-VjAWg+VztA70rva84krE8rQN3b3cGMSWHeB4IsMRPjS/8cRaYG8NnpfaYLlnMf8LWw41Luuv7ZSx2eEsby3t6A==", "signatures": [{"sig": "MEUCIQC7eeKnvJIjU54cEZ4t+rHQ6I8Z/mXDP2Zfs8P+/S6XXQIgASQ3ro3/gis6rhQCkl53aNqBm87n07S8/0S3RFnZdbM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "2bd39ad3371f8d96929fbfac05f7409bba89225a", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.7", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.7.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-15.2.0-alpha.c681f819.tgz_1475139733783_0.8818315078970045", "host": "packages-12-west.internal.npmjs.com"}}, "16.0.0": {"name": "babel-plugin-jest-hoist", "version": "16.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@16.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b58ca3f770982a7e7c25b5614b2e57e9dafc6e76", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-16.0.0.tgz", "integrity": "sha512-Kcg63kNYotzS1/S1dv8OSEwLq8t2sQqAQ7bvoFMkAWgp4xQ3lmmC745sxv7c5a0BvbJkFTmKKzlnkJSsmNQCHw==", "signatures": [{"sig": "MEQCIFBoOg4zD6AX4SS/j9GOurefFnGf/kdF6dbPNyV7TPxSAiBLTnEXGMo5hVsNfvqrivFpRQ5fmKRomtoxFOghwFlyOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "b58ca3f770982a7e7c25b5614b2e57e9dafc6e76", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.7", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.7.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-16.0.0.tgz_1475483907921_0.12181239225901663", "host": "packages-16-east.internal.npmjs.com"}}, "16.1.0-alpha.691b0e22": {"name": "babel-plugin-jest-hoist", "version": "16.1.0-alpha.691b0e22", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@16.1.0-alpha.691b0e22", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ab3d495af25d2ad53440089e7d4d8ab782b3b664", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-16.1.0-alpha.691b0e22.tgz", "integrity": "sha512-s212oIPL6GQbH9qD2YKwMutQW2z7rv9/yJdLgZAdNddexuKoHLrxxrZXwsE/gJRqMB4p8b4ekFcGOTReiwfVGQ==", "signatures": [{"sig": "MEQCIAad8UKEXqicz+gV2ge/m5EjdHZhjObrdSt1qKb8p9z9AiBtJ6vQS7UJtl44flTsq16bEazggdDIfv2PHLfrdSoscw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "ab3d495af25d2ad53440089e7d4d8ab782b3b664", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "6.9.1", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-16.1.0-alpha.691b0e22.tgz_1477639644690_0.9125848286785185", "host": "packages-12-west.internal.npmjs.com"}}, "17.0.2": {"name": "babel-plugin-jest-hoist", "version": "17.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@17.0.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "213488ce825990acd4c30f887dca09fffeb45235", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-17.0.2.tgz", "integrity": "sha512-Jj4GmqNLj4XUWhLfqAz3Mrd0fxwl0uRSYIDsPVHD+Ta9m/cPM/4C6W2JHGkhvc6eQ+mb00Bs809UkEKV6/sqCw==", "signatures": [{"sig": "MEYCIQDDmVoOXigidULrBq6p533u3lG0DGwH3pks1L5KhXqajgIhAIJlOvsJzYqvMAI4TA3hoKtkWPYK4hn88TWN67/JmBy3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "213488ce825990acd4c30f887dca09fffeb45235", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "7.1.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-17.0.2.tgz_1479170355637_0.4131905680987984", "host": "packages-18-east.internal.npmjs.com"}}, "18.0.0": {"name": "babel-plugin-jest-hoist", "version": "18.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@18.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4150e70ecab560e6e7344adc849498072d34e12a", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-18.0.0.tgz", "integrity": "sha512-Y26YgbdPh84sgTfYGOSnyEJKQle9PtkGQqsMoztgh+pREUe5UumAwB848RxV3CP7y1nzKK2nxdW46sknkibhwg==", "signatures": [{"sig": "MEYCIQCNqbEmEEfX6LPGL17DAvLzMsaHgmLHFDj4Hk7potpb2AIhANkY/odtP9aOjUX1q9ICjSIKTvXXWHYqKpIXUrewb2Gp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "4150e70ecab560e6e7344adc849498072d34e12a", "scripts": {"test": "../../packages/jest-cli/bin/jest.js"}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "7.2.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-18.0.0.tgz_1481801061788_0.2897357577458024", "host": "packages-18-east.internal.npmjs.com"}}, "18.5.0-alpha.7da3df39": {"name": "babel-plugin-jest-hoist", "version": "18.5.0-alpha.7da3df39", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@18.5.0-alpha.7da3df39", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "26dd740263e5eacc50753b072abf11a4257e7ffb", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-18.5.0-alpha.7da3df39.tgz", "integrity": "sha512-EAtCzRS1Q5JaWo/ngHhPWodVpQCNGouuNRYKfUWrC4PO8xS2ii6g0p9Nuy83l/FLrcuFVbuSDoRvIMKS+Mioag==", "signatures": [{"sig": "MEQCIASFPel5N/3wckGOuxSpnFLtlIrvcoVluGwn3rAJk+75AiB0bHcolkQDjWSBy5G+dUiyCG4AWS7us/ETEUDV9Fc5QA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "26dd740263e5eacc50753b072abf11a4257e7ffb", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "7.5.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-18.5.0-alpha.7da3df39.tgz_1487350649173_0.7384724102448672", "host": "packages-18-east.internal.npmjs.com"}}, "19.0.0": {"name": "babel-plugin-jest-hoist", "version": "19.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@19.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "g<PERSON><PERSON>n", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4ae2a04ea612a6e73651f3fde52c178991304bea", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-19.0.0.tgz", "integrity": "sha512-bMlU8Y0lmia0Ari/qSjulDS1H7rPLtT5I4LBhk8oN/u0CzlAiVxcvU8nTgx0915aZx9+eboVBGbm0TJaEWM3NA==", "signatures": [{"sig": "MEQCIGntag0zTxmtLwnXzB5Inw3H0pzCbGSXcfW8+PxMy0k5AiAOv1I16iRwLvOQW3dE9Xad6Yr8pamVPCJXqCvGycRO0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "4ae2a04ea612a6e73651f3fde52c178991304bea", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "7.5.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-19.0.0.tgz_1487669409096_0.6368202443700284", "host": "packages-18-east.internal.npmjs.com"}}, "19.1.0-alpha.eed82034": {"name": "babel-plugin-jest-hoist", "version": "19.1.0-alpha.eed82034", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@19.1.0-alpha.eed82034", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "56d805a069499b2ee804783678f3017c54367fcf", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-19.1.0-alpha.eed82034.tgz", "integrity": "sha512-hHB/+KRjuS5q9Ss4EHxaLBokYBFcuzYxFnRDZrE8kbIuCxB7/Jg41kOZeOsRGN0PYwIaKIcfp18ck/3amhibrA==", "signatures": [{"sig": "MEYCIQCc+5kotR+B3Aee9k2r2tjFSEPiOOm2ywjf8CfOaiIWxgIhALa9fCf0cM1jb4sCSFqOxrYHWYJu1vPL071j591UWM/C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "56d805a069499b2ee804783678f3017c54367fcf", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "7.7.2", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-19.1.0-alpha.eed82034.tgz_1489711276544_0.840749496826902", "host": "packages-12-west.internal.npmjs.com"}}, "19.2.0-alpha.993e64af": {"name": "babel-plugin-jest-hoist", "version": "19.2.0-alpha.993e64af", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@19.2.0-alpha.993e64af", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "848d9bfb29300df48701c08426be8d72e17c0d8d", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-19.2.0-alpha.993e64af.tgz", "integrity": "sha512-VZw6W7BM4BT4yZZByMLiZK+zFmBArpTMSca48RbuTy1TK/huYCoIb3qq2ewIZESYYWZoKdm/GYaUjO0PCAuCyA==", "signatures": [{"sig": "MEQCIHNANtOFgJegjut8ZbrKFfL1Lutf7A54UCHaXFgZxKEeAiBjPToJgdEGs0FTtg5ot3ORJvpMeQHHCeQ9Tyg7A+Dcaw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "848d9bfb29300df48701c08426be8d72e17c0d8d", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "7.9.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-19.2.0-alpha.993e64af.tgz_1493912292566_0.9936230150051415", "host": "packages-18-east.internal.npmjs.com"}}, "19.3.0-alpha.85402254": {"name": "babel-plugin-jest-hoist", "version": "19.3.0-alpha.85402254", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@19.3.0-alpha.85402254", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "45265d730c238c4a2a7e8c345181f516c28a483a", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-19.3.0-alpha.85402254.tgz", "integrity": "sha512-TMZFwpUShkA8XpD1cRzYaZUSAR9+OfdICThTBkIhasarTE7K4zzFvSzN7MQnTuUhHa9FpwV7jKIBYWn3XmvUPA==", "signatures": [{"sig": "MEYCIQDBn7RgfTmRnuyJp5+Y4aJj5B8t9cnWkFr7A9nRFRRBJQIhANnvNwhv6f1XN8yQSX15rr5nr1dn985SGkbNDS28pnsY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "45265d730c238c4a2a7e8c345181f516c28a483a", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "7.9.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-19.3.0-alpha.85402254.tgz_1493984896552_0.08491432527080178", "host": "packages-18-east.internal.npmjs.com"}}, "20.0.0": {"name": "babel-plugin-jest-hoist", "version": "20.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@20.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d2afe94fa6aea3b8bfa5d61d8028f633c898d86d", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-20.0.0.tgz", "integrity": "sha512-c2AnR19YVb7zBgalvs5WJrRTmMuBJQZmRH7PgIsVQ2an7MRAwYFVVenbvNYldWNBPVBj2arEVs1a49guK7o1Rg==", "signatures": [{"sig": "MEUCIFWCtH4k+9ToJWipKed2QQ/XfxwoNz0DmHIrg8HfASrSAiEA7FiUfx2+4X0FEBhPyYe6dzbMRvbKRyB717QX5q+65ZU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "d2afe94fa6aea3b8bfa5d61d8028f633c898d86d", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "7.9.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-20.0.0.tgz_1494073947415_0.613827338675037", "host": "packages-18-east.internal.npmjs.com"}}, "20.0.1": {"name": "babel-plugin-jest-hoist", "version": "20.0.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@20.0.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1b9cc322cff704d3812d1bca8dccd12205eedfd5", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-20.0.1.tgz", "integrity": "sha512-UND0/hs/rG23CztpOYb9k0jZKIu4dJr78uKw+xxPzvCTCsL8wKNJP+S7avFzQor1rKpyZ1x888IxNNK91cRzLQ==", "signatures": [{"sig": "MEUCIGtkIJHhvGHzgs4XjGWIAFvgREe+Zls3bZpifCChkXlgAiEA6nrVjZTTN6/SsGfOM27GG3afVs4fcxe+qhnhYzeZteE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "1b9cc322cff704d3812d1bca8dccd12205eedfd5", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "7.9.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-20.0.1.tgz_1494499800603_0.517991233151406", "host": "packages-12-west.internal.npmjs.com"}}, "20.0.2": {"name": "babel-plugin-jest-hoist", "version": "20.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@20.0.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a2ad78da0bfd52ec8b1d5acce6563471c469c7f9", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-20.0.2.tgz", "integrity": "sha512-SI1BeW+RpZwUZvufJrxLKYDrM3Ji6XNCQyXE9zKMCFHSxz6CZSVq2ZTiVqKdrRZaZ7147JpByRHD3Qja9dwu9Q==", "signatures": [{"sig": "MEQCIC4kbqgvkPkqbOCQydU2qw12wuni3MrE6MkodTiJMolKAiBssN8HlH8VFTFqO4hkRFiS2L+gNbCelhV+1z0Ry29bZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "a2ad78da0bfd52ec8b1d5acce6563471c469c7f9", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "7.10.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-20.0.2.tgz_1495018214119_0.1757009532302618", "host": "packages-18-east.internal.npmjs.com"}}, "20.0.3": {"name": "babel-plugin-jest-hoist", "version": "20.0.3", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@20.0.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "d<PERSON><PERSON><PERSON>@rheia.us"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "afedc853bd3f8dc3548ea671fbe69d03cc2c1767", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-20.0.3.tgz", "integrity": "sha512-rnyXaCLaHX6BjzT9h4UI1xK7oobCwtLfhompk0qxbcQNhw8JEidggjO1KonVrpdd2+q3QAww3DwVdTYs0g373Q==", "signatures": [{"sig": "MEQCIAPMpU0QQgP2paIM3KxAM1TrtMU5K6/UAhn2Da+sxj2hAiBPCNnSLujfdQaV+hOFqXi7TBjtTk0t0+6B+r4iaGYRvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_from": ".", "_shasum": "afedc853bd3f8dc3548ea671fbe69d03cc2c1767", "scripts": {}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "7.10.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-20.0.3.tgz_1495018624145_0.9682767807971686", "host": "packages-18-east.internal.npmjs.com"}}, "20.1.0-alpha.1": {"name": "babel-plugin-jest-hoist", "version": "20.1.0-alpha.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@20.1.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "506fe48033c71867e00c43f7c380d3ebabb6de47", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-20.1.0-alpha.1.tgz", "integrity": "sha512-q6+r1CzVwJbhkhM2hw/xLwwRe29ySUTuLJHJvMlkBM5ty4A8btiobxGsDXgSYxMH9amrLdserLiKRinRVSrrJw==", "signatures": [{"sig": "MEQCIAOzQTBpUxAytmpFYCfZKtQ+paffZFOx0MnNwUCHKEC7AiBBrogTp0cKzNhGGIqr0ppQvCxM0H4pXp6EBKWIsWdSHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.1.2", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-20.1.0-alpha.1.tgz_1498644974016_0.5932602076791227", "host": "s3://npm-registry-packages"}}, "20.1.0-alpha.2": {"name": "babel-plugin-jest-hoist", "version": "20.1.0-alpha.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@20.1.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "777e546ae3871485189d810e81cbbcf7ecaccc0e", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-20.1.0-alpha.2.tgz", "integrity": "sha512-Rw124XBSSDUxFgJG31R2QgF5jVZvUeuwfhzubJZaPF7FeDO2iduKeFpBUhfbgWtgf8K1MN8GW+2pXKMLko3yHw==", "signatures": [{"sig": "MEQCIGuSVnqVo16jV6MVZQiogY+FtwbaujlilkGmK2XvKoweAiBM2xx8dMfz491Q2v5cchUF+nrR/i+Xrw6cerFvgeuRpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.1.2", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-20.1.0-alpha.2.tgz_1498754200620_0.5029912344180048", "host": "s3://npm-registry-packages"}}, "20.1.0-alpha.3": {"name": "babel-plugin-jest-hoist", "version": "20.1.0-alpha.3", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@20.1.0-alpha.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3b5cbcc430dd7fa72339d5cbd0778cc27315d113", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-20.1.0-alpha.3.tgz", "integrity": "sha512-hnGWIU+4ag854/THtl8keE9THH9L+eo1W+0G4Rl6p5xwaSlSMCYB3xrjxyXMe946uAHPslp1i/ny2TTygdx3Og==", "signatures": [{"sig": "MEYCIQD2wXgItIziadmbxO/ThYWpzpJkw016qke02zCf2qMxwgIhALddKR6xYGLmLRHFcpTYtJKMx6sg3ZmQsbcBSVlWOFYs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.1.2", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-20.1.0-alpha.3.tgz_1498832446676_0.14184845564886928", "host": "s3://npm-registry-packages"}}, "20.1.0-beta.1": {"name": "babel-plugin-jest-hoist", "version": "20.1.0-beta.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@20.1.0-beta.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3d1bc3fc05b24fa71d97b5a96c0972281a9c25af", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-20.1.0-beta.1.tgz", "integrity": "sha512-sJSm9fFKZuVbenlYarLLmUKJ8fS6+C926th/Y7rzudNgGsQFGLcxMgyqAtsTdUCA2oDl4xotnRINnA2jVNoiuA==", "signatures": [{"sig": "MEUCIQDCps5DxDqMUUotW3PcNTtThUN/rSpxNfb641RJsloxuwIgRBikpgFAA4FsSzDev/JXXM3l3EozaFxqhCssrBsVC0g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-20.1.0-beta.1.tgz_1499942015732_0.5704501429572701", "host": "s3://npm-registry-packages"}}, "20.1.0-chi.1": {"name": "babel-plugin-jest-hoist", "version": "20.1.0-chi.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@20.1.0-chi.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3283ba911f8d862957d73bbf82922aa549f044ad", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-20.1.0-chi.1.tgz", "integrity": "sha512-xRWySYSNPGV6TmyAwrn31q7VQqpAJ6n8fAb4ryESG3eeft9gQhDZeQ0OQJIyj5tZ0o1PbG47chpDkH5SRpaLKw==", "signatures": [{"sig": "MEQCIGNq5MokPZ4fTu2Xbl7bDjnd3OzVJ3SvOjqCDgOQFl4uAiAZ7AkOj7c1iLweH+iPG4GY9JeoxEtSwrqw4U9phVT0/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-20.1.0-chi.1.tgz_1500027896082_0.461082307389006", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.1": {"name": "babel-plugin-jest-hoist", "version": "20.1.0-delta.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@20.1.0-delta.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f0e6ac6cc7fb894b2584be74674435a4eefa1ed8", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-20.1.0-delta.1.tgz", "integrity": "sha512-d3jmEkhBV+qpccNVzmWO0DMovx7EnjAscens5hdzQHne4hyKHUpkzcsnnA4bXGlSwWdKQCLcIM7mz+v5mrYU+A==", "signatures": [{"sig": "MEYCIQDiXC/zPsaM7FYORdsWtHAGudc4ocT4oRHWgK1pC1nwNwIhAL0Mn62ktsFqCu1b5gwKTabvfaRXSTlbmKMa+YeVJ5nH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-20.1.0-delta.1.tgz_1500367606829_0.16413071122951806", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.2": {"name": "babel-plugin-jest-hoist", "version": "20.1.0-delta.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@20.1.0-delta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c46173317b7fd40f44c15779212c4fc890b203bd", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-20.1.0-delta.2.tgz", "integrity": "sha512-BxYNXXzNalgiCoyq8gmqO4BHz++wfpPgWDXFasuVGYVpYw7j2Sv16+bhaUxmQR5/TXzVnxo3yxqttz3JAs0afg==", "signatures": [{"sig": "MEQCIF8mXrfvepmCVZM9E7+/0Geux5vXJu7NwWaKyyhjQRp8AiB3M5PlU/NJourC/qjohGXooNC7AtYF2GemZQWsZPTjEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-20.1.0-delta.2.tgz_1500468996416_0.7330456560011953", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.3": {"name": "babel-plugin-jest-hoist", "version": "20.1.0-delta.3", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@20.1.0-delta.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "91a6ae3e546f7dbf26322e4241c55cd8a4145ff6", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-20.1.0-delta.3.tgz", "integrity": "sha512-bfGbaNs3pTIEWxWsANHHowjSjmNaFG/idbHYoxgfy1Cw4dGLYIIPKHggYrcI+7goj11tBrJDM7PC1HCrvBIKfA==", "signatures": [{"sig": "MEQCHxoRj66LMPXF5okBf7KFiL49jhMFeQbgbq+A7KB58V4CIQDbTXBASHX5UMuJo0cey2C+bbMO1ewhqBHx6xciVvr6ug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.2.1", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-20.1.0-delta.3.tgz_1501020740856_0.6169727155938745", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.4": {"name": "babel-plugin-jest-hoist", "version": "20.1.0-delta.4", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@20.1.0-delta.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "45f6a69d529e0e9e4c979c8f12f4054338ac85c6", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-20.1.0-delta.4.tgz", "integrity": "sha512-BviA3O2X2kdh8MXnxgkaA/0uoVy2OOPyIo+DoxoA6lHaDYw1oGoTe7IuU8v2LqFAMNQIqDZQd2MqcHlF4aBiCg==", "signatures": [{"sig": "MEUCIQD03tqhhDdiRkoCl4rZ+Lu2im3if1dPFaD+Vk4VzqpQxgIgOZvoEn7cedbFWLmI5aiupCbdMX8Hme5ds4IpMY2SO4c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.2.1", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-20.1.0-delta.4.tgz_1501175944715_0.0567747897002846", "host": "s3://npm-registry-packages"}}, "20.1.0-delta.5": {"name": "babel-plugin-jest-hoist", "version": "20.1.0-delta.5", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@20.1.0-delta.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "56024b38c168cb71dad4bb60ae6ae4b1c20f625c", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-20.1.0-delta.5.tgz", "integrity": "sha512-/8FQHzyP1/lOZkX+lYcr75EgmV383Ts1CTfYNhCmOQINMEy/ceJPhD0eFAiQYJHu4MtIOGYPBZCp6YpoxIqP7w==", "signatures": [{"sig": "MEUCIQCgZtKo9dG+VfBgH5jfgLD7Q00j5ELPYzoXYxdJQoO37wIgPLmxGgB/Ru+axie5SLV7gG71g4xtHwb+GXWBINRJdYQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.0.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-20.1.0-delta.5.tgz_1501605213235_0.8434050544165075", "host": "s3://npm-registry-packages"}}, "20.1.0-echo.1": {"name": "babel-plugin-jest-hoist", "version": "20.1.0-echo.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@20.1.0-echo.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "507eceaeadd1ba025d816efdb5023b3adfaac866", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-20.1.0-echo.1.tgz", "integrity": "sha512-9qSYFbPOX24oYJdn2fg7COiGDkiDpZc9dV0nthH0UuQuF1sII60HasbYOtYuzqGUvBtkG44TdKq/Ydo35+qpnQ==", "signatures": [{"sig": "MEUCIQCV4Uewub8c+ZssRyIVU0DQpmKHKqUaXb5+xY+GBsBVFgIgW6JtjdGdo/Ij7hX/MChvsFR8C6NRNoPYDxJ0BQc30G4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-20.1.0-echo.1.tgz_1502210985443_0.21856674691662192", "host": "s3://npm-registry-packages"}}, "21.0.0-alpha.1": {"name": "babel-plugin-jest-hoist", "version": "21.0.0-alpha.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@21.0.0-alpha.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "70bc8d9b985603dce892c969bbbed772356e2370", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.0.0-alpha.1.tgz", "integrity": "sha512-aZUJ2VuqSNLIMQS/h8nOcVLeeAsAGhGD3/7pKp108ydZyiNbiOIG5S6LJX6Qu2Qic01KvbhvxNCz1/OQ8C6+xg==", "signatures": [{"sig": "MEUCIQDaJQ/3ryyATz5cy8Rc2uqCa5XRwBirnEKbSNV2A3aPxwIgU1J6EhsvAJ4Pbb22/dTvrom6SRHvDThQEgTuJNHplbY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.2.1", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.0.0-alpha.1.tgz_1502446437299_0.3342508019413799", "host": "s3://npm-registry-packages"}}, "21.0.0-alpha.2": {"name": "babel-plugin-jest-hoist", "version": "21.0.0-alpha.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@21.0.0-alpha.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4727a412490b10b482e1fa5a00ece82cac2f73c9", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.0.0-alpha.2.tgz", "integrity": "sha512-iWdYrKRMXAkUJbnr2485YJcaT5Spwf9i0+pmUXscdKPAbvdMtd/S9O1CkKeJR/ZQ4y17uk2e9UkTZonsVQNI/A==", "signatures": [{"sig": "MEYCIQD4by7UfN94MMSKxyG3LOiVjss66c9bc+WnHzkLvEGkdAIhAJkk6wT4ZzRpW7CbEsD6v5PJSk3cQTkHSdyj3qS/i5LV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.0.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.0.0-alpha.2.tgz_1503353206252_0.12329284800216556", "host": "s3://npm-registry-packages"}}, "21.0.0-beta.1": {"name": "babel-plugin-jest-hoist", "version": "21.0.0-beta.1", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@21.0.0-beta.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "693524f381f60a0cac3a2b6296d3b9aee9800d19", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.0.0-beta.1.tgz", "integrity": "sha512-y+T1cjsxO45eI9kYbZvCSdxcmzyq7fjlnMQgU5L1H+T73BIUlLaKDLY1cvMUdzBl2ZA5oxx4ufEyOcT+j0QLOw==", "signatures": [{"sig": "MEUCIQDMtNBELwfSOCsEkJo+uXsdIpCM9ELaFbQEEX2e9ReGOAIgFYNQC6qcncDl7us5JZF7+kKyBOBlw6EhFAPHex028aM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.0.0-beta.1.tgz_1503609998301_0.7993281427770853", "host": "s3://npm-registry-packages"}}, "21.0.0": {"name": "babel-plugin-jest-hoist", "version": "21.0.0", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@21.0.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "aa2dbab7b0d58fa635640efd53aab730be7b3273", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.0.0.tgz", "integrity": "sha512-Vn5qE/9mebjmdLNZkLTNCWsYS2U4dBAHOENQt5JPSVBP/kw26vy/M9H+byo4wHGF6n99dn9k4RO04DEhnjRI3Q==", "signatures": [{"sig": "MEUCIET+Po+/n5sUO8t8CmimJTb1KaaCjyF5FUGrdcSpHLNpAiEA6thaArWUlgZzy1PovqgLGkRnfKbMWvfliMZmLYyvJTg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.0.0.tgz_1504537301642_0.586597143439576", "host": "s3://npm-registry-packages"}}, "21.0.2": {"name": "babel-plugin-jest-hoist", "version": "21.0.2", "license": "BSD-3-<PERSON><PERSON>", "_id": "babel-plugin-jest-hoist@21.0.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cfdce5bca40d772a056cb8528ad159c7bb4bb03d", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.0.2.tgz", "integrity": "sha512-iQeYbiM0lr5TCW42qvGkBBoy0rTx6SPppRFT7NwvdnSwNOGMI8+1Oc27SF5wJbCvAY7x5KScP3f0TKtunl+NRw==", "signatures": [{"sig": "MEQCIE15B8ckj5PtV6zIFkjvIP8eK6dPW+0DzLJ8X0y4YFCfAiAr1YzT8LxbChC6MCZn7XJMlLdNksvJHSYJnSYFgAzIJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.0.2.tgz_1504880342116_0.7964196461252868", "host": "s3://npm-registry-packages"}}, "21.2.0": {"name": "babel-plugin-jest-hoist", "version": "21.2.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@21.2.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2cef637259bd4b628a6cace039de5fcd14dbb006", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.2.0.tgz", "integrity": "sha512-yi5QuiVyyvhBUDLP4ButAnhYzkdrUwWDtvUJv71hjH3fclhnZg4HkDeqaitcR2dZZx/E67kGkRcPVjtVu+SJfQ==", "signatures": [{"sig": "MEYCIQCaB43m2yXX3PmdZA4Ss0kiwos2G7k9DYmJ62cA/UotVgIhAOqY17BdN32XpfaSYAedTY1RswCEYhiqR+VPvDeVciz5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.2.0.tgz_1506457322800_0.8544242626521736", "host": "s3://npm-registry-packages"}}, "21.3.0-alpha.1e3ee68e": {"name": "babel-plugin-jest-hoist", "version": "21.3.0-alpha.1e3ee68e", "license": "MIT", "_id": "babel-plugin-jest-hoist@21.3.0-alpha.1e3ee68e", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ff501c9956a480968933b5ac5ef111edb91108f2", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.3.0-alpha.1e3ee68e.tgz", "integrity": "sha512-1/kg8zfOerKSjSD8Z0qDLlQp7ZQ+9yXS/3uCaEeRyCzncuqwYn5iLC0xNRbPgMAUPWY1YyN/ioHU/5Rs/JDdRQ==", "signatures": [{"sig": "MEUCIGSuYnN2XGiMs8fxXfT+QRz5vmkK0/5zgKD1x8spkdowAiEA0n/YvtjJSUg+yWNZj7HHKdVmeXcGVum92NC5nJrsDBU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.3.0-alpha.1e3ee68e.tgz_1506608425838_0.7215686710551381", "host": "s3://npm-registry-packages"}}, "21.3.0-alpha.eff7a1cf": {"name": "babel-plugin-jest-hoist", "version": "21.3.0-alpha.eff7a1cf", "license": "MIT", "_id": "babel-plugin-jest-hoist@21.3.0-alpha.eff7a1cf", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5183136e04d005686cccf870e5896e89f4114635", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.3.0-alpha.eff7a1cf.tgz", "integrity": "sha512-YtblOG/V1Ep8T8kiGhK5ptd1aXBUX09H4TkZabgB1yaS/hrIgACNjI6aNCT6nAqAnqn3CdCt9GB3WMJJWgPGJg==", "signatures": [{"sig": "MEYCIQC2nworiyOqCFUZXuTL6sRyL9nUnZlkrxN/ukqWmX+tcAIhALkjHxOqJcaLglgmESt/+5f38IMO0IEX8UtDlRG5q60n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.3.0-alpha.eff7a1cf.tgz_1506876399559_0.5168410509359092", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.1": {"name": "babel-plugin-jest-hoist", "version": "21.3.0-beta.1", "license": "MIT", "_id": "babel-plugin-jest-hoist@21.3.0-beta.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "56710bb8955b3d1084db23adfaa94bacc890a53e", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.3.0-beta.1.tgz", "integrity": "sha512-u4SXYWk3inypegfT82IertqEX2bEUsxOLUohdlXt+mT3UHkJ1h50+iAmUuYAycYrXAm/0iMdqJLEDkgSWkhtHA==", "signatures": [{"sig": "MEUCIGFncdIBasF8ooR9J/ZrwlwTLZyUGUXz5+ieJprJKuTQAiEA7GXAxKeWgyIkXaknpX89MvnErhSTvZbgd7wjbTAiKsc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.3.0-beta.1.tgz_1507114114718_0.9810063280165195", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.2": {"name": "babel-plugin-jest-hoist", "version": "21.3.0-beta.2", "license": "MIT", "_id": "babel-plugin-jest-hoist@21.3.0-beta.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bb1ef8bed1e0e9f566900b9512326f322008900c", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.3.0-beta.2.tgz", "integrity": "sha512-6laHQm2ijjJaGKLlU7kzHPlrR3Q0NrsT/SOpCcPn8Bt5o3I9qwotLBCe9tzub2Ot2jpKWRVpxSqwD9TVqaNqqQ==", "signatures": [{"sig": "MEUCIQC9/DtlJCTjM1AjD691BWH0ZPmkuXHbCZeLhHPy1nEwtAIgWW0RVkpJ1lqlnPRhfcIuMQgKVUX78O5m3cc5NH3IceQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.3.0-beta.2.tgz_1507888440343_0.1171637384686619", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.3": {"name": "babel-plugin-jest-hoist", "version": "21.3.0-beta.3", "license": "MIT", "_id": "babel-plugin-jest-hoist@21.3.0-beta.3", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a9c854fa0567f8000d66c687e2ad68727af1e608", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.3.0-beta.3.tgz", "integrity": "sha512-jJ0pXswkgdDbJ7E1jehCNmcXCt4Y0B5x59yLncnqGE0YhAzfSggQxYA4gzH4nCyj87ay7owBhyWUtFaR9QDKJA==", "signatures": [{"sig": "MEUCIQCQgjw4AyqMmPxw/XpQmCdqXYoXr9AagDyj8FIYvUJ2KgIgCLHP07yeCJ5aTrLEIbmG7C7/66TkYhn87g49yXvRq40=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.3.0-beta.3.tgz_1508960036383_0.5802895158994943", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.4": {"name": "babel-plugin-jest-hoist", "version": "21.3.0-beta.4", "license": "MIT", "_id": "babel-plugin-jest-hoist@21.3.0-beta.4", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a1283b7e556c3cf236604c17e0f18aaa9cf3d79a", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.3.0-beta.4.tgz", "integrity": "sha512-jvMfb15IcdXjTfCPP0xyGPoxL09UzNV+TOddT4syDD6FDXMHJ/+3q4ScAKLdshgQA+AGRFCHc582sNsjCiBNww==", "signatures": [{"sig": "MEQCIHuozOUoR+hEswqo+GVyUNv5dau38oQ2OSH4etI3+2+4AiBA1eHNJH7iBQI4uhKE+mmUGi3mBBKHe7ORO/Q7IxGZQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.3.0-beta.4.tgz_1509024406932_0.2507983024697751", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.5": {"name": "babel-plugin-jest-hoist", "version": "21.3.0-beta.5", "license": "MIT", "_id": "babel-plugin-jest-hoist@21.3.0-beta.5", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "936b16abe8dbcda889383b9ee4f4f36345fceee7", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.3.0-beta.5.tgz", "integrity": "sha512-16BbbiEl9R/qibvgOuL46dsNdl5ZMoikGF8Qu5x9Cvwqm+7iexElTkyQXfDb1v0Kd6qLyZozAA4fPMFbeQs/WA==", "signatures": [{"sig": "MEUCIEqD7IDuS0h0TQ9JrhughjEotwVOdwBloP+9sNm5rzfeAiEA6yzycSNTIpSXKfsYxQlnvL7js86W7ptx3L2zoxM0Wug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.3.0-beta.5.tgz_1509628641677_0.2093061024788767", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.6": {"name": "babel-plugin-jest-hoist", "version": "21.3.0-beta.6", "license": "MIT", "_id": "babel-plugin-jest-hoist@21.3.0-beta.6", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "593c922775d3bf59ed996463ce1f38f0762557cb", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.3.0-beta.6.tgz", "integrity": "sha512-PYShAUdIGeuAIU6ivET+/5Qq8IlFmAgpg0Dz4hjx/WsONSWPZrXnK9SvmXjTPatIYHHVNG/+MDWHXi2evJeEFA==", "signatures": [{"sig": "MEUCIQC+jqLFcfQ1OUSShOIbkENqUpwvbbfAxskXyi4lQfydugIgYsVJ4u90qA2zyrcRxKUjFvzv/HbTqAbp2wgUDswr2zI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.3.0-beta.6.tgz_1509726080924_0.02274735807441175", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.7": {"name": "babel-plugin-jest-hoist", "version": "21.3.0-beta.7", "license": "MIT", "_id": "babel-plugin-jest-hoist@21.3.0-beta.7", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2a496f65b61d9d4f04ea88bc293f5d49c8d941c8", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.3.0-beta.7.tgz", "integrity": "sha512-lhi7kdOgN7QkkA3GnzSdeiko67VVw3hllGz2klBVDGoxhDqp71nsH76jpc+KwstIwsq0XJlq9fcwo3BzCSQQMw==", "signatures": [{"sig": "MEUCIQDOQqR9XxvhlFwYZOX8QPmyCp2FjV+cRq9t7JqTjGcavgIgDNZsZmX6gbQNOqPKar0haLEFR52JKSEdkhMD6/GztPU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.3.0-beta.7.tgz_1509961179346_0.2662551652174443", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.8": {"name": "babel-plugin-jest-hoist", "version": "21.3.0-beta.8", "license": "MIT", "_id": "babel-plugin-jest-hoist@21.3.0-beta.8", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "de76e6ec446e7c4ae16817e283aed9f566d285d8", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.3.0-beta.8.tgz", "integrity": "sha512-vdQDf8ORTk5TZ19W1BOlemo8muTEZkoackEp5StTfNjqrIkPAs88mKzToM+Bde+ZshilxefEJNuISOACVgfhyQ==", "signatures": [{"sig": "MEYCIQCnKlm8xACwbkEZ3NKHx4eGWGvDMXSkPrHS2TWSkZ0Y4QIhANRN7rZt/rSmSguAubjAxg6UJ5Ltuw3Wl8x9s7KyhtJA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.3.0-beta.8.tgz_1510076609542_0.19773285067640245", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.9": {"name": "babel-plugin-jest-hoist", "version": "21.3.0-beta.9", "license": "MIT", "_id": "babel-plugin-jest-hoist@21.3.0-beta.9", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9fa3ec95fe79ccca4713808556673fd80c0af4f3", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.3.0-beta.9.tgz", "integrity": "sha512-fCABrfxthY5KZCjzMIAeO3xKvI2PdQDSaGLovj8pzaTA5xyqfsseW8jZVXfbR2mtOKZXtotVnci5V0/C1jdCow==", "signatures": [{"sig": "MEYCIQCvxRWIx0q9hjXKuF+PMD5TYLah6O1yAMPT0WsFKZ9MdwIhAJrGCNBE7NZyj0ncm0CC2r2l0ULLpw+TghXC5QySYc5A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.3.0-beta.9.tgz_1511356640753_0.6130275290925056", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.10": {"name": "babel-plugin-jest-hoist", "version": "21.3.0-beta.10", "license": "MIT", "_id": "babel-plugin-jest-hoist@21.3.0-beta.10", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6711a320892a3986580d553c75683a65f7908fc7", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.3.0-beta.10.tgz", "integrity": "sha512-xS/QyX2gBTxXVE8xdfL3JPBqgfrj1mvAEDFn8jnFf82LOwA5mXYpsfpZS7o0f9cfu2GcqbHd1EyyzMI4NsgKBg==", "signatures": [{"sig": "MEUCIF0AD9RTSnPv5zUhqyW7cf0pcrX32Ezh9inh4IXTYJQUAiEA0TLQIDxDb17YHMpgA3cCaaJzta22tFE2J+tHguFkyVE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.9.1", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.3.0-beta.10.tgz_1511613557942_0.2006564971525222", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.11": {"name": "babel-plugin-jest-hoist", "version": "21.3.0-beta.11", "license": "MIT", "_id": "babel-plugin-jest-hoist@21.3.0-beta.11", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c198fbaafd4d6adb45eb7265a27414e4ae1cc774", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.3.0-beta.11.tgz", "integrity": "sha512-fAynfhmIc365IkJdh4nwG6QlLta+rDU+/0EjR4appGUNAMepFRTLbbpjGF6P1l17L/u3HUovhrmMxGc7xQgRrQ==", "signatures": [{"sig": "MEUCIHF0+6lLDhR/CQft0EjnckEXcqksiUKVk3uS6BVw30eDAiEAkRa7rusGH5GiGOZ++yFrhVGRwOhOzIlG1R0WodrQu00=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.9.1", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.3.0-beta.11.tgz_1511965873779_0.3617103807628155", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.12": {"name": "babel-plugin-jest-hoist", "version": "21.3.0-beta.12", "license": "MIT", "_id": "babel-plugin-jest-hoist@21.3.0-beta.12", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c1144f6fda46ba9cc44c8ea78e33b8af8d8b059b", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.3.0-beta.12.tgz", "integrity": "sha512-FWUGgJFhPsOmHg67sslKp7+k3NZFiA2Sqdc/HO7r9OKMv9i7nsYq1zkEhPxTbeCASSHARRCtrddb+qiHibEk4Q==", "signatures": [{"sig": "MEUCICfJekLBFBNsx4lbC96h9oZ2tvZleBm27zn4EXiZNY5QAiEA0L8vMR8S05/YCrgaNROE+BiXCD2VhR1fpxbi/e4/aWk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.9.1", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.3.0-beta.12.tgz_1512499708412_0.7816572824958712", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.13": {"name": "babel-plugin-jest-hoist", "version": "21.3.0-beta.13", "license": "MIT", "_id": "babel-plugin-jest-hoist@21.3.0-beta.13", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "16db88e7e0073f144c45ffaa8af7ab8e2e349359", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.3.0-beta.13.tgz", "integrity": "sha512-TD7sABmWIYcq5AU6eFA4HIXOnOt6jgfTIlk3t9DXVvfB73fbSpqWtNRhkBFhXuiVoeK2jWRFjPjDJ9cAruNXCw==", "signatures": [{"sig": "MEYCIQC9Sofa9F/VfdEx/UD35CIgvWEN1npsRbQVnQGGoFbSMAIhAJRAIC+QWr/POhikBzZ4QIW6wui8VIpU1qc+tzE0aWtj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.9.1", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.3.0-beta.13.tgz_1512571020979_0.8035021813120693", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.14": {"name": "babel-plugin-jest-hoist", "version": "21.3.0-beta.14", "license": "MIT", "_id": "babel-plugin-jest-hoist@21.3.0-beta.14", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6750586001ad467e7b03e51bc1402d136f09a3d0", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.3.0-beta.14.tgz", "integrity": "sha512-AstfKjgZ6yn6e83jMBwzn1VXPJLjVlLjw0edTCwiKxenAgfOFOnee9U6x8cG4gtE+qeGiI2ZGfWyBM5HghaxhA==", "signatures": [{"sig": "MEQCIFALTNE6csY1iCasnkupx28mv4B3XxdIEwgrx+C/rw1VAiBGiwD825v9T5enif25VNk1i2jmsBwR8ig+6g8inyRgNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "9.2.1", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.3.0-beta.14.tgz_1513075944407_0.05863064178265631", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.15": {"name": "babel-plugin-jest-hoist", "version": "21.3.0-beta.15", "license": "MIT", "_id": "babel-plugin-jest-hoist@21.3.0-beta.15", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d3c08d0a38bf21f9a40f1c38106c127f722a6410", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-21.3.0-beta.15.tgz", "integrity": "sha512-jT7XVP2vTzFx82VL1neN+/ZJnTNtut5axkbdsOuro06u1HtQKUGhLnsEo9FOsYm30pHwROvLKBiehfQZkXc6wg==", "signatures": [{"sig": "MEQCIEITqoCAEYJfFP8GOBmcVeVoWzxq3TwJPdBz+WOk5yFSAiAdzv9Cre1ENlqExe2ovsryGYeLM/p95M8tRAv9Hsqeuw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "9.2.1", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-21.3.0-beta.15.tgz_1513344448932_0.519450131803751", "host": "s3://npm-registry-packages"}}, "22.0.0": {"name": "babel-plugin-jest-hoist", "version": "22.0.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@22.0.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "30859d15453a324aee01264be9c522802f8ba512", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-22.0.0.tgz", "integrity": "sha512-eyge/WyfBs2ztsywfksVC+6ag76aGAf7+dDQzKQLPRqTdlDvHEzO+UCMp/motiGu4MIINy0247bwdmxZtH8C8w==", "signatures": [{"sig": "MEYCIQCMWs+9r1KE46i4sx/+xtGQhcOAnNbIeRCid5e0M8JxXQIhAJpSzabCYEnEGG7F63yrtFTuVoRulINPQtwS5sHymL9q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.9.1", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-22.0.0.tgz_1513594997541_0.4341146538499743", "host": "s3://npm-registry-packages"}}, "22.0.1": {"name": "babel-plugin-jest-hoist", "version": "22.0.1", "license": "MIT", "_id": "babel-plugin-jest-hoist@22.0.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e5a65a213158d6c523686c542124591e29a35d47", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-22.0.1.tgz", "integrity": "sha512-YtFAm9HEg/kMg9qw77QPa81RdHa2b7yJTVTKbQV6TU7UI2gYsO8b2lHJwejJ2lZWXE7rHm7epuPIGGdm1XGGIA==", "signatures": [{"sig": "MEUCIFUS5mqZbgbowQObqXcXGATcykk1plEahetL/NTI3fKzAiEA+x+8FoEc5cLzeTAB//eFh6CYTNPigykbRZzqMJYQrHM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "9.2.1", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-22.0.1.tgz_1513628956624_0.7131505692377687", "host": "s3://npm-registry-packages"}}, "22.0.2": {"name": "babel-plugin-jest-hoist", "version": "22.0.2", "license": "MIT", "_id": "babel-plugin-jest-hoist@22.0.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1067ad72a359e0ba1f752cf8b347a8f8560b5243", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-22.0.2.tgz", "integrity": "sha512-Zshqe6Jxn5xLbeyXeNu8Ov9VRLhcqsslUJfw2iTAmwH2pkJxX5dzCJeARsfKnrAegdxaNW+hUrTdzD3W7VOCbg==", "signatures": [{"sig": "MEQCIGHe0tGJgoIMaNUwcVRLuF7cZi2BLEYMdw0YYiZvOkfRAiArjMrdd4wt7ipoTC5+xbzQD7p4YtygopfwiLbxcYmeqA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "9.2.1", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-22.0.2.tgz_1513691576291_0.5498661492019892", "host": "s3://npm-registry-packages"}}, "22.0.3": {"name": "babel-plugin-jest-hoist", "version": "22.0.3", "license": "MIT", "_id": "babel-plugin-jest-hoist@22.0.3", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "62cde5fe962fd41ae89c119f481ca5cd7dd48bb4", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-22.0.3.tgz", "integrity": "sha512-Z0pOZFs0xDctwF0bPEKrnAzvbbgDi2vDFbQ0EdofnLI2bOa3P1H66gNLb2vMJJaa00VDjfiGhIocsHvBkqtyEQ==", "signatures": [{"sig": "MEUCIQCcLaL4L718dw3Uns0qW35WY72qAEkV+3/87MH0N06IGAIgHCBtl/IbTOvuAlZt7MM6pjBGeekI2Ufq85h6KyifW7A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "9.2.1", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-22.0.3.tgz_1513695524251_0.5472701427061111", "host": "s3://npm-registry-packages"}}, "22.0.6": {"name": "babel-plugin-jest-hoist", "version": "22.0.6", "license": "MIT", "_id": "babel-plugin-jest-hoist@22.0.6", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "551269ded350a15d6585da35d16d449df30d66c4", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-22.0.6.tgz", "integrity": "sha512-KiAmvs8r+lJVzPfnWor4ftrDjvq3CRonbU5lYOqQLi1wXNRoGC21N1Yak6qGXjdpyz3H9plZJPcT7o14RyCs5g==", "signatures": [{"sig": "MEQCIFDGKjiIMGdcrSnCPdJrTomuCZVQIqZxAtpp5u/JsoPgAiBOI2+WY4gwtlrjQ0NNJul0XL3ThsieP7p7//hy8Y6YCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "9.3.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-22.0.6.tgz_1515663996989_0.7484427418094128", "host": "s3://npm-registry-packages"}}, "22.1.0": {"name": "babel-plugin-jest-hoist", "version": "22.1.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@22.1.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c1281dd7887d77a1711dc760468c3b8285dde9ee", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-22.1.0.tgz", "integrity": "sha512-Og5sjbOZc4XUI3njqwYhS6WLTlHQUJ/y5+dOqmst8eHrozYZgT4OMzAaYaxhk75c2fBVYwn7+mNEN97XDO7cOw==", "signatures": [{"sig": "MEUCIQC0hua9f9tVC3f9xzVHyOGMIViEnhULf7dISsu0b6O/dQIgEucO7WDon5ips/Tmu2zWqOcwU5jh+J4CKz7Dj5EmjRs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "9.4.0", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist-22.1.0.tgz_1516017420979_0.854206790914759", "host": "s3://npm-registry-packages"}}, "22.2.0": {"name": "babel-plugin-jest-hoist", "version": "22.2.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@22.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bd34f39d652406669713b8c89e23ef25c890b993", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-22.2.0.tgz", "fileCount": 3, "integrity": "sha512-NwicD5n1YQaj6sM3PVULdPBDk1XdlWvh8xBeUJg3nqZwp79Vofb8Q7GOVeWoZZ/RMlMuJMMrEAgSQl/p392nLA==", "signatures": [{"sig": "MEQCIHBpdvKvfwv9XrDeVtxuRg0nUPmEcGMnhebotOkGSncUAiAvhUKjEMaNfykXgZG3kkNMCYOU2uDCE9IsYlOuCya1GA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5266}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_22.2.0_1517999151809_0.6215924109926483", "host": "s3://npm-registry-packages"}}, "22.4.1": {"name": "babel-plugin-jest-hoist", "version": "22.4.1", "license": "MIT", "_id": "babel-plugin-jest-hoist@22.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d712fe5da8b6965f3191dacddbefdbdf4fb66d63", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-22.4.1.tgz", "fileCount": 3, "integrity": "sha512-gmj5FvFflXSnRapWmF/jDjx5Lof1kX0OwXibCxMOx38V3CFMOnTxLTUrAFfLkhCey3FJvv0ACvv/+h4nzFRxhg==", "signatures": [{"sig": "MEUCIAldMff535Q3Zabn5wxRUlRrnyBf47x5YilwkpTfWYiQAiEA6kARiLqWvxoaZZkxG2IrnaUVW6D9wrAl9xE22TcSjpQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5019}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_22.4.1_1519334935795_0.37945774908559815", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.0": {"name": "babel-plugin-jest-hoist", "version": "23.0.0-alpha.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@23.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9d3e334679db15795de6b7df46b04bdb2d191869", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-h99vly8bIw9K/nvmd0Xs10Jor0HRuAax5wEZK8cfjnHCz3AyrBKcBj2dK6pN4LvvdIU1GaNL3SnN7PbXs02qeQ==", "signatures": [{"sig": "MEYCIQC9YG7oOFGIq+2AWQdrsnga1Q+7iaXbvYVATkUmH+3uNQIhAKOtzaLu7RFJGzhcGrIg5xkGbug6yg96CvT7Ftfy8OWu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5080}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_23.0.0-alpha.0_1521125732907_0.1865549568184226", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.1": {"name": "babel-plugin-jest-hoist", "version": "23.0.0-alpha.1", "license": "MIT", "_id": "babel-plugin-jest-hoist@23.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b22e1008129cac02a63a4a1c274a5835ae61e87e", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-eALbT7iEWdo0YhOYeEYlem7VbDRgxYq8tVpZm3k5x2Hc8uNb4UAu/N8IadoDlzvK6i521rp7VepM0zgS80/C1w==", "signatures": [{"sig": "MEQCIC21lmKhqKlmiudMw9mfBA9v85y5vznZ2+CkKiN/2galAiB81pxX9IfE6Xlw6ayHkZsiw0Lz6Vci3D2WRIKGOFv7IA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5080}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_23.0.0-alpha.1_1521648010520_0.22235045431316713", "host": "s3://npm-registry-packages"}}, "22.4.3": {"name": "babel-plugin-jest-hoist", "version": "22.4.3", "license": "MIT", "_id": "babel-plugin-jest-hoist@22.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7d8bcccadc2667f96a0dcc6afe1891875ee6c14a", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-22.4.3.tgz", "fileCount": 3, "integrity": "sha512-zhvv4f6OTWy2bYevcJftwGCWXMFe7pqoz41IhMi4xna7xNsX5NygdagsrE0y6kkfuXq8UalwvPwKTyAxME2E/g==", "signatures": [{"sig": "MEYCIQCsTEMFhqwopieEQDsJ7aGESlhK/4s733jBAej33Ne3wwIhAMzNzox0ccU6cEBa831J8EtchvRKR2SjBNGS5vIfoSa0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5019}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_22.4.3_1521648478635_0.06067312929043389", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.2": {"name": "babel-plugin-jest-hoist", "version": "23.0.0-alpha.2", "license": "MIT", "_id": "babel-plugin-jest-hoist@23.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f1b9da84ab5f8802c29ae164f566802bf2e3a33f", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-CLjs/3BgMj3svBcXdSRCzU+A6WaxaezoXzoh1o4xumEM52tnTfslxodpymQF0Ot8FBrfIopOb/y8WAejc9GcDQ==", "signatures": [{"sig": "MEQCIDhTxOHKEG7/634Eh3LUrqlpNrFCdJec0XEHflf/UVFjAiAsz6Rr4m1fc08kZzvADJ29Q3mO14W6yoNEMfGf2DBUZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5080}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_23.0.0-alpha.2_1522060843696_0.2828780186052864", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.4": {"name": "babel-plugin-jest-hoist", "version": "23.0.0-alpha.4", "license": "MIT", "_id": "babel-plugin-jest-hoist@23.0.0-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "3f32715d54640e37cc62a84a9d3d7eada6a067fc", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-mka8rh3UlPVlovubfjD7DMF5cO12an8+7oT7ZbzXUK1yC1844YiYl4n6ykSBfaXbo3ZYJhwMvT0NufPETak0aA==", "signatures": [{"sig": "MEYCIQC24xTv5Q5y4Ozgeac9b2maIzk+dwpnCXfa5j4rEqua3wIhAIvsxJIhCuMYTlCxqiVf1qOHAOSUfXrmm6fPeBpTV/zJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5322}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_23.0.0-alpha.4_1522067496394_0.29133482560340784", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.5": {"name": "babel-plugin-jest-hoist", "version": "23.0.0-alpha.5", "license": "MIT", "_id": "babel-plugin-jest-hoist@23.0.0-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "d3ac1b55cfcf78d1418629b5cbd8f82d97f1545e", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-3<PERSON><PERSON>h+Y+2/yo8u1FTA1ZlE/n2u8hmNDe7L15w6SDF/IKrrGidjVEfi3ifoj4Q9j/BWd57yljp/58aE+3t+/m1A==", "signatures": [{"sig": "MEYCIQDJMN4ugEsSTkzxaNyw2Uj1wz3qun0tkM6V2A7/i+ZPjwIhAJnbshJ9g/HbDC1ZPRoLqGaTW27SWpIayPmxpXlw5Zy8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5322}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_23.0.0-alpha.5_1523387897220_0.4942594639483624", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.5r": {"name": "babel-plugin-jest-hoist", "version": "23.0.0-alpha.5r", "license": "MIT", "_id": "babel-plugin-jest-hoist@23.0.0-alpha.5r", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "713df3d8044a0ca18b51a2e09a8a017691b885c5", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.0.0-alpha.5r.tgz", "fileCount": 5, "integrity": "sha512-28kBAMNwOkq0CqbnYuVlXIXypjhMKwGtDuGjEMxZu/RNauP31CCpmCgaqz6zsBREg9QFzANRJr0fy8U5GIj6Gw==", "signatures": [{"sig": "MEUCICSEfZZGPYU65DQE1Sz1y47+ZjwbZjoExefjlDrjIUVFAiEA5QN3xuoVIGlKi4eKDQvXsMrQmugpWUDwW/3JfUIxYpU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5323}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_23.0.0-alpha.5r_1523425967342_0.9298736740821003", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.6r": {"name": "babel-plugin-jest-hoist", "version": "23.0.0-alpha.6r", "license": "MIT", "_id": "babel-plugin-jest-hoist@23.0.0-alpha.6r", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "db75c456722f4ae4e454858810d4bff2ab7916f2", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.0.0-alpha.6r.tgz", "fileCount": 5, "integrity": "sha512-ecq6L9S9uLFw+KXBqqUuh2/HAqUUtBWBuWYSqE63MnueWibBCEGFj/qsaJCCgwV4qm8/gXB15Bd2aZA4QvkW6Q==", "signatures": [{"sig": "MEQCIGp0l1JuZW+h7JnOL4bIeUs8Q7kRfLdKYII67ryz9LGqAiAsWAbQTltp316NLYD7w7AwIswSKEK5JqF2Q/M+Xdni8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5323}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_23.0.0-alpha.6r_1523516491914_0.29732316932787173", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.7": {"name": "babel-plugin-jest-hoist", "version": "23.0.0-alpha.7", "license": "MIT", "_id": "babel-plugin-jest-hoist@23.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "feff5f56d5f5a381d3d08d37a7daaceca4fe062d", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-07vyKh+r6149tQY3ZHMDFW+xROlYyoy0cQshQP2i2iAoj+kGcJYtNpxToYE6GQt7jmzsnTRv8xBK2FZ9eVCuPw==", "signatures": [{"sig": "MEUCIAGy/zNTTbbb8CrjlHKCZ0/PAEHoeyrtmWxLiBQgURMeAiEAgXn45J6zxotF68iDFddK+P1UObJdnOqREeXZTFKuawk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5322, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1kMHCRA9TVsSAnZWagAAJ+8QAIxHVMsQpg5SpApbw0t1\nQD6HDUGTrezDus3TE469vpy3rKHrbTEkYA12mWjOBQy2cU0J0oDESO+3olor\nzre0A6dhmcDg62hO7IX7KV5Nault9zlY9jnpipZzk2UIVbz6LJ6t2lTHmypO\n2Sr9gbJB8jjf0q6Qwye8IVcCOV5+kxjQMfD8fBadpt2xV4/Cxesg+7paBPSP\n4c6hGW7Yg1vwo9o0tEYxKJ5hUZMnSHzSrZ1CQ0asziSPSj+pmLK/19elcsXW\n1LZw4wc7miItIhGKLW77tbqAgz/hantRTLoHdeDc5T4jQbc0F5DFQJMNXKiN\nVGWsNjPpcTKeU9vdF+P7RzU/e0gParnDjcAKGvz7esTgn4LQEP/dZSmZbvYr\ndyb8fgZcqUXp64cAG1O+IHHI7C3f7KAIpLm0mqndoKpYjcIhfXSqgGgmLEcQ\nVzMXOl7x+cwDNeztUj1NPHzfgvDRL6JtqAn8gS8wzZdpaNYQo+21REA7Q7bZ\nCXthQ97TZP6QxGJ8Ueo0MHDP3uDo+Y/dWVLWryz13PI0G5KaCsPJgtawzHdr\n2p9eGNkRA/v1Yh0tbn7EkGzHqUehM3+WcaxUJK0lZn0AIokyzJL/ZFXOHQl2\nx8S0NbWk0bBcRyOcjBi5OtdFZM2iyGNSNZUhrSbuxdfYqKUF8cVjZP2wMQhh\nUz+1\r\n=12DX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_23.0.0-alpha.7_1523991301287_0.18313154334264992", "host": "s3://npm-registry-packages"}}, "23.0.0-beta.0": {"name": "babel-plugin-jest-hoist", "version": "23.0.0-beta.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@23.0.0-beta.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f115e6f8fe201105aebd9c4020b14d5152997d3b", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.0.0-beta.0.tgz", "fileCount": 5, "integrity": "sha512-oDzsqUORGI1WpGY6c49paxJ/0i0ueivnxvnEk4xUKogYSdMWNyi0FmLg80Pp4LsSrCLXBFr0JIspz5NN1HDWsg==", "signatures": [{"sig": "MEQCIGmS/HC3pCRoeupgYh78BHL3LmQbje5Zyi+RvEUhlUrxAiAESI9BYllev1JVfbUErfXq61p/gvgdVi3tKIpXw7nlRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5321, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa2byRCRA9TVsSAnZWagAAPKMP/jqegyutSAWlgch7Uvow\nyFB0f07xt+IcuTyBlh/AWl8vs0y7AEoSje+4T2XiEA21OltxONZeRONXE03P\nBety4Nk4ghxnmPhkgWySOK94Pfu9V2SI/pIm1mpxkwnl50IqlO5cfVOmOdlp\nHDzrikkuyW79kPp2fPRheIGLugScsIAOvvS0Ge1O/dlU33JbGWVevUmXxX6J\ncPwp1bY+HL3eMh0grcQKKtc+20u3esrRyyV9gU9xpPfR8HRhbGBKlXsxJH3Y\n/AzWsKZzGEvimu/jeFME03AGBW8x5zoohtG8/fHU9dHAAF9Ehh+Zfqey08nC\n6Hvws0MT56t540EkFtgKDjbvCAL9IWI1Ni3WT/DzdEhjUXZynL3RfNtzVTeX\nxPmiRbci+xkrd4KraV7BsbQRJqi5iriyr2V2TdA6/MgeEiPRS2XSRtRXGL9N\nSYremOuXuv001vPKXg1y3jw4Dx0q+luwT+f6OqADFM+m7ttEL6DLWlmrjh/5\n9gUTt8ZmI8GkmM0DARF0bOKDaV0/qf+/O5VrYQ8PgF1i1uNZzt9eT7jXgNeZ\nnRy/LuOq0vYd4nKG7ctlcuqi1UEJc57aE9yyOijk5/HXTNBlSasVhf/3f2LH\nr2pWSBtFGm8Bngt9tGesO+KmyRzmCXKqidUUFfmXmTG0OJPEMbwJ/m53Pu6n\n5M6F\r\n=811A\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_23.0.0-beta.0_1524219024669_0.07842954956999915", "host": "s3://npm-registry-packages"}}, "23.0.0-beta.1": {"name": "babel-plugin-jest-hoist", "version": "23.0.0-beta.1", "license": "MIT", "_id": "babel-plugin-jest-hoist@23.0.0-beta.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "d0a96a2b0cc84856b2633aa8741fc79bfe1a7663", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.0.0-beta.1.tgz", "fileCount": 5, "integrity": "sha512-cCjJIkIq6/04XRqZWNU3Tew3VOCKnKRYaAJ54Yt/hVdjQrKC80qoja6YfCPHqPI0IR5157Se7od+mPwSLUZBAA==", "signatures": [{"sig": "MEUCIATGrJ4OWlUMWSzW4GOkDWXV+gz8UGVIll6/DLCWh4XDAiEAmoEcTVHVWSWVedxYu1AxoGNrX5WNEVAMcikuAzxsCMA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5321, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa21xRCRA9TVsSAnZWagAASsEQAJZjVrMkNRfS+D3E/5fw\nTYaEp4zIqbdhc3xDd77SrJUebCw1QAlqvSw1JbqhcCgx72oeOj5cKv26MUa/\nPrD2uPH4rIC9z/yTlgx7bwmi6dlpAU2uZvx6ByUHzOIFGPHMzv9SZS+wpbMh\ncN0qUQoEOAPtdT3ZSkOrZa79+ZPBvzm8rsRgDSydLShLfgzFp8IiQ200kHSN\nemsv+Ycyn5AgLwJWIB6T1Y8W+e182B0HA4QBB6XxvegRuiZSJeY4S4rFoLuT\nYEtj3nyyQtynEwsMVQce0uxKLdT51WBRkMWuMZT/mrhLo+wzm11THS7odXT2\nit7sdhfzF36BQ2ro2sDYeLHwTqxxtJ8xj+jetEiTcEvNrHrhs76pf4Vy1tXT\nkW/Y70lVBkzSERVIPw7NmuTS6GC9ZA47aSToL9/H08vVCPDBJcQssmaKU2OJ\n2XxM+hdMjn35D3wVZEsGpz/ioKKErBsimRKVDRdp714mLLhANGQoKK8JoF43\n/DGoWDPbVZdqky1yj/TRgdz55ReztV0cPcZCkDKr0JD9oYdamBqE4VPdrrWq\nkxGH6CoIrxgTAwVA5y2QOYT79BmeNgQ7cCkEJT5Mnm6OIYIuzcYndHR9P5Xr\n3JA2AqUT7W6rrzKneDvhfKbtNkJ1aQ4/6A9EJBI6wIrI1hN0u4/tsPa/45Gl\ngTZQ\r\n=bsCB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_23.0.0-beta.1_1524325456723_0.896757785902579", "host": "s3://npm-registry-packages"}}, "23.0.0-beta.2": {"name": "babel-plugin-jest-hoist", "version": "23.0.0-beta.2", "license": "MIT", "_id": "babel-plugin-jest-hoist@23.0.0-beta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4a9373dab43d53de42c5da0b1aff95cf24a71b93", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.0.0-beta.2.tgz", "fileCount": 5, "integrity": "sha512-xoqop/++4XGAc4kRtvkTYUflRlhJJ286cSCqNq/aIHAX7oozplt5D5ccdhvtsXv1V714QTcc90LmZJ7d0QMlCw==", "signatures": [{"sig": "MEYCIQDV3fFfxVkqcaiThoxozb1Z9oL3ol2m38eIzOmqhGeTNQIhALm2+GWLwXlsVG9T4XqenwGI9NzQMrVLgN2on4SGk++/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5338, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa4kHqCRA9TVsSAnZWagAAtNEP/ROZ7N2uOmvDatKvZIUI\nsvVlvNtBJESQUbyEbH7YAYOa0sfwjnITgzJfNywrkrTFSuSy/aqCHucAcOUx\nuh3zR9ec2O3Z1rRz1ruRpfX5lQiIOUt+vyLihnAaIYYrHi2hUv1clxw3JkaP\n214S3T57wG2Ff5bYE7DaGcLs6ANNMZNakfTIfR0BhGUonbnUTaUvJGySRuX8\nlphA1SznA5G3RHZagiqcF67kvkNyGHSgxQ8olfpqzpuEB8e2/PgtvIiaXXHI\nQxnH0zzUvOzGWkhdr9+w4pDo3MBoWKO52phkx7PovV8Fhyi3/A49oWNIfIi+\nC5fkqrwS08QpYOq8jmbfDfG9zbq3XbkFi5pn41oAGBqNLvPuCz5uxlWFImx8\nxGOcRA7nmeu/zKnhz0WmRD/K1FELBtvQTnMtaAbGCXfaVpezgTmlFZmlVvWq\nouYTuo+bO4nbjS1brH5V/t25G3TuH7oqw2B0drLqOzHPVnbBq1lqSnsZpqW2\n9XyAoWL4HGQOMH5WaZ0molw5omizwAuCUfT5ONTZOhXKmDWsmXYtzE6YOflS\nn40SwK2He8sooLT/lMoo2bPTUaGKgCS5Mzvz0sLB4zYujLG15AiYAcgEbWUd\n4D7GL3B63oHmoWRx2Prrm1MdzdF/bFI7Lg1yXcyrb75OaOme4cpNpF+xTvZT\nVeDl\r\n=g9UN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_23.0.0-beta.2_1524777450132_0.3236797391733435", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.3r": {"name": "babel-plugin-jest-hoist", "version": "23.0.0-alpha.3r", "license": "MIT", "_id": "babel-plugin-jest-hoist@23.0.0-alpha.3r", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "fb4ff992fff5679f4ba6b892bbc1db83dee5fc0d", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.0.0-alpha.3r.tgz", "fileCount": 5, "integrity": "sha512-/zSMyAzwnDkqgFJwzH5xjM7XIGu/yEzbPhI2aNh6zPm7lj8T6MRMKQSYpVxRAmNkNk7R7sT91PT/H3jbE+zeOg==", "signatures": [{"sig": "MEUCIQD5Srsg2jq7UPoQ3jyEQpkbIN0z0ngKziIRh1AOHsVexwIgKtMJGom5tWTqo/cBaroRGD0BFQ6PyDS5h1Ur6WDunuU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5xWrCRA9TVsSAnZWagAA880P/1X1XvpVmLqYojEpWww5\nOvUNUfNv7GKHLwkzYg6Q0llKOcJzMVjS6M9vtSc1IHByUcOTGS0PrZwa2sTc\nONwJZhIfw4hGmXwg9arxuLtiQ4ric4l96g/UTILbkk3264uny67Brx+qLi0l\nuzLU/WI8NtGbo3wCyvLWjpSpv14528IOWkpIdItHRmXNv9bKBApuEQlcjz1V\nom8fuoZrgFpadITVSRu9uWxxLHbEtNp9GVct1nhsiW+LN+bliGlXUqZVDBhc\ntjfNTpIhPjhrzG1lAxlHYfaoK5hXKxL2XOXGHPbWkc8LYS8Lfyud6ykkc0VC\nYLiKqJpzczTYe1UrOuUIZCerjzr4rm/SY0l9s0shbweX/EEiDR2JSyyT/dat\nWsPbkPAC1zeb4OeE2QwJktDUWZeNHpC/ScJvLyeuEzYLkmhVaUZcFjL1MKoV\niFztam9ATFAb97Y0JNtGDIDNHirV6mxYEV2D7QCAIONSZeFfmqFwRtThkfWi\nk4qVewOEIQATuYps8H+hOM9pTsA+17I9T2AHAGdIBSE+XqUlPWMB9GFBcr9m\nzZr1cWnVCjxwnFzuR8fqTU11r2uYyKgpp7JaNEh1y76yR5+rGzbM+k8w82QC\nZt8VtLztoJPlZIlveXU44YOOB6eWwH63IvZiWtSR/UQE/LIx9jc8glf4TedP\ni7ss\r\n=456E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_23.0.0-alpha.3r_1525093802918_0.1781168859600133", "host": "s3://npm-registry-packages"}}, "23.0.0-beta.3r": {"name": "babel-plugin-jest-hoist", "version": "23.0.0-beta.3r", "license": "MIT", "_id": "babel-plugin-jest-hoist@23.0.0-beta.3r", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "3880a9eaae510f4cb17c589909da727233b5fb69", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.0.0-beta.3r.tgz", "fileCount": 5, "integrity": "sha512-B4iPgl2SwVKf3qQhFgqV5L3l265UsEtLUHxIw9wpP9z5zPjIuQIGCsZY6fHb+P+zCQIO996sN7sp8edXOwckJA==", "signatures": [{"sig": "MEYCIQDxNyQCm8lbOGp9z7Iyf9ClHbc3Qpt2kMycFKodpmwBkgIhAO3qiffLXiuvAB7V5fahBB7J9acehPKVN12Y0+btiMlR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5339, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5xbGCRA9TVsSAnZWagAAY5sP/1gbqMYKRtzaUqr36B1p\n17Rtkp+gSsoPQCzuR47zIC41wrqNkGYPJ4a+B6yUWuqhJGkdRrPjehH6P1Wk\nRLrFgJMMEUq5CSA/rfzJjUJaQp/bj3LKA026DkloHrUhYD+Q/tIOnRkxLl/h\nstoJUXXnsRPSuL+hFAJlPVkHC5vLokUIqVIFVxrHP1nQAUf1IVW0LtnDft1k\nwrkuPF18LDWVCYoDm7+FqOkI+/n3ubN5you+BwUYlAyA8vfSqVnxwylnyaXb\nz/BDQ0seQpyfCsThZOmW17bvgUjIK/cOBP/ZzbufdOBJyCZb3OoYf9PNyGjs\njffZntHIXqVI7LEz1x4CNi4WMuRtjuz+7CwhexTXcWQXgWuxYB1KhzBZmHwC\nVYgsD9Vdb0AG/4Dm0oddz9ExLoyNH8Ns8RrdTCpXP5Drgm/iV5OY7h7BmUIH\ndpDc92hXuPeUsjrf5/XyG95d/kUxEQZKcSNJ8+uqOGlfxXiiU/0pX3mEV/Dx\nv4/y/+SEilQCAd5LTUTK2SHWqLIL3sRNbw3hYOaas9oVy+aZhum1hjo48uZz\nGJspmBQDk80WRW43OvzI7UEDClZIo1s9qFdn/nWTLvnqe0q9sD7IMJPnPKI4\n3ztCciqExrq12ioFoswixQ9lhR44G9HyGZiR4raiwSWVbndZAYnAR8UHmKsw\nLlhw\r\n=MeGB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_23.0.0-beta.3r_1525094085234_0.14029606296774366", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.0": {"name": "babel-plugin-jest-hoist", "version": "23.0.0-charlie.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@23.0.0-charlie.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c92075712b0f6e96ba4c2b5741488a76a377cccd", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.0.0-charlie.0.tgz", "fileCount": 5, "integrity": "sha512-6u562fAUa00XlFOJpTWHkjKNlCZWxd1QkMMF4fWPGV7ld/aa52jCRcmuEVexKIZT61ItQunkYBGEYbpEg/qsMQ==", "signatures": [{"sig": "MEUCIQCroxeZr9BtJvHKJdr3ONfFc+VS0C489GG7r0tBTyJAnwIgco1Ry2GUcq3KwfbknDgL4EcCw/dtgfzwmB2zZIGnBG8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6ZlHCRA9TVsSAnZWagAAYncP/2E+DmOKyW9zhUB9EWZ5\nmjfRPok27T/9niPf4be1Vqt9v85AroXF5y1Ga5OK20JVfJKAmfWl2biZKcyY\nu6ObgMlsgLK+wc/iX/K3EEwqjDWFcMAPK6Vl0/9315n2WPRyFDUcccl8YFNR\nDWIG1GFRaB/yktiuWEzcXECnYYL9NV/LgOmj6R/iV7AO8rah+PKq/INJvs0S\nFc5jQEg5BlmJAxq/+h7FPrsdtpxE0DX/IAyfFtKGPdnw820Tgu/rzO/3T6Hy\nMMauqg6tdssYOI3JIqqywJdol4lNoPtWfO5ALUa89ng2NvSQ94nf8XjdwddR\nsQQNtBVCD1TI5/HdyoRfccdGOOCV0yxwugZ3unbMF07XCB4rInup2cJZ7vPF\nmbT1pybGhaNaEssuvFZ0knV//6pCzVu26vNIQiCGkhDCP8VfSU4+qV8xiK2a\n7udRHvCBvm4AIpegwqexAQHZW9iHuN4iV/Yh6IkIoLJBolcaleJLRF3tkENW\nBdHbKhecUzok3IAO6b+m1OF0DduGVKyA2X8gS7xpFY4gnU+3sw8oAJwonD3L\n6EMQUYgy756Orc8NnVc+HJTN5mZAM9IaA0otFIXCQPp2lzg1KKYCCUTBiJ/s\nxajC6PSG+BZ5mjx4PgmNrGqHGAHdmEjluMZSXZIHC7ebYNQ4SkzBqEmwDNID\nqghQ\r\n=jRkd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_23.0.0-charlie.0_1525258566388_0.8388976402143684", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.1": {"name": "babel-plugin-jest-hoist", "version": "23.0.0-charlie.1", "license": "MIT", "_id": "babel-plugin-jest-hoist@23.0.0-charlie.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2a6d15cb609443158d61eb01ca63ed08edd2c46b", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.0.0-charlie.1.tgz", "fileCount": 5, "integrity": "sha512-M+wjEyHW660B3vNF11BJQpuKiuxu5shSva2NocTb9rmocENTzdzqdDNS29JHlTUDVYNS7I24PXr7i4iatnHSrQ==", "signatures": [{"sig": "MEYCIQDWz7d0oxyhPG91zaLi+irVZfGwRizW2k3arhzK4e6tFgIhAI6MZ1CLS3/9Qf05o08bUrP4hcXhmYOOUTEgEsy1jXNi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6vwdCRA9TVsSAnZWagAATjcP/07e2uGxKwVyzzDOcUCN\n5QjYoC4kKZ0pE1f002CenRVIQSqcDJp4mJwbnc40aiP9EpPkZ9wdTHXrFwr/\nPixjaug1xLAsQwbSqFKLfgefPYJ/CTEA9rWlddNV+dNNFz1CXcy2f1PnZGqh\nnEv5n3Idnk11w+//FbxlElhucIGMGzVVuIZl7manv94XzTPR17Mk0RZ1s4LZ\nJnQ39XXj9fb16pMd02zyAl+NUrxQp2QpfeZ/PmgbsSqQWKEi/0zu1JVQtUdG\nCikb3Rm7X2xWoa05JUX/kw4ydHrNIYIpmunrZFxZgtDe7YySZ4zK0o+4bkJa\nMAVLdFmzvczeICkpAm2wLcDTu5Fjbz7EjxBQuN4pCly7L+doKwHi3xE1F3hF\nhTanTWrC0VN7Cc5RwErlMqqwC+B43dwOz41MrgxxtlA4zEQH+FRBnLwpKMeu\nnlGF3YjdSqUioX1kj0pz9jnyAwqSzMm671Z4EmNb8CGl/a74jSrTa3gmHq7y\nG4g/tQlUT8FxAVmYkr60N/w+xJdUajsSKEd/dtFy6Q7Wnf0kM8ZYiCOhsGc6\nl7TVMex6xta/zsOy3AWJz9LIl/j8ec6hJdh/sKvikdyncqHw3ZgQB/8NS+Aa\nvu5aZae6BcHS//w3He/q1lLPe/ZF8mVJtirpbylL7cQ5GLxxRYPiOjlzaWvV\nxoi7\r\n=1Vze\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_23.0.0-charlie.1_1525349405479_0.5685394593029058", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.2": {"name": "babel-plugin-jest-hoist", "version": "23.0.0-charlie.2", "license": "MIT", "_id": "babel-plugin-jest-hoist@23.0.0-charlie.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "def92a20f64e732be1c82188b93622ef9641ba67", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.0.0-charlie.2.tgz", "fileCount": 5, "integrity": "sha512-Rt/MKXMGji/gxr/p/IxNs9cjQoDADY36IL2ge/b0IpMA7UlVcCubShDK64QbRmccuGrGVvWHR4fqVx+/uPfOgw==", "signatures": [{"sig": "MEUCIQC5NxEYaHhv3Jq9fkXwOuZlWeurvXI6za66vyznNKqbyQIgdT2MZn3PfAji991MtHy2nQW17GalxdfHBDik93T2VgY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+q2QCRA9TVsSAnZWagAAF7MP/jnGzWa3BxCdM8FK7kwY\nWZOHrgdC5ZuGWUX6EEd1UVwaw/JrxVzWVJgMBV1WXFLZohgSMidbqz7IWNGD\nOWU6/pRwPs7YPn7gIIMFzTK2T42qPe3FpzvPrkI+lUUU1aGlYvGUOq9pfR0D\nyIUurbmhpQigkivEC0f52ydu7qUc5CwlBQJzakTWHZrHspcdTd8QXYIOP2FZ\nD8g07XHMEh1DAbLTpc7mZ8q4GcX8RC/n0bxDfvQ72eyZu1FyZ31C/1O/Qju0\nzE7XAfBkFxGudAprp/YndDHgYsJtZqrYrX7aEIFpLpqdlICS8Kv4sRAVHG3m\nEdaWE0CEoRhkUuTDL9ZqfIm59DoMQNGJK+GAV1OAnMCtcK655TSGiIn4U2/P\nCsVrEHsaZXJmof+MkxfDFL4oqFKyp7kcexVy7HBL+AwzOtlPOYjQZ2h58kMa\nor1QaenR+65iNj9VqqV6B6DeAweWr0BRE+XU43e160lWpk2CcQr5MbDOwdr4\n6mFv7Zd4QtZFawebpyNimH2PDUj/+KgDUS4Mg/D8uwLplxStxo7BMBKB/wLS\nDAefSPzZs3vE0vr6bYP54Xl3Gerwd9l3paIpWvDrx0roQfBu2eMF/9g1cj4d\nfdWiL5jHUbTHu4azX/9NKxFpxI022l+EhTXqOvyxflHM/gFz4lueaAbG0FWa\ngVCN\r\n=kQtF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_23.0.0-charlie.2_1526377872177_0.8826375223204297", "host": "s3://npm-registry-packages"}}, "22.4.4": {"name": "babel-plugin-jest-hoist", "version": "22.4.4", "license": "MIT", "_id": "babel-plugin-jest-hoist@22.4.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b9851906eab34c7bf6f8c895a2b08bea1a844c0b", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-22.4.4.tgz", "fileCount": 3, "integrity": "sha512-DUvGfYaAIlkdnygVIEl0O4Av69NtuQWcrjMOv6DODPuhuGLDnbsARz3AwiiI/EkIMMlxQDUcrZ9yoyJvTNjcVQ==", "signatures": [{"sig": "MEYCIQCagsCeoSS3aCEguKhNakh3kNSNd80ISaqCkNEa93kVTwIhAI8jVUZpfLbO3mEbZUKkois0Tx7xHoPFdboMUdwTVaXZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5036, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa/s4FCRA9TVsSAnZWagAAjewP/Ajm7ziUFKVxf6LGoytX\nf4KJjaFGbGfXiHUVLbY/N0F3YqForVm1XUdzjpJ0OkPPh+yTK4HbWwlZTnPV\n9SbMffFT1Msbs0LiXZQMLOQBI45YbCVvaPx6hp4X6AFivx+4RKBJM85YE9Ru\nIOkPlqibco4Z8Nzz7p+pfq5iAObjU3VJ4MyORUxnxPuVjqJ3o0qxV8pkSrj4\nixS5X1NIA0gEHUPjUE1Xpe//0vUIB5IxBalkNtR9OviY0U3Amn8CF07WwIib\nxcgNC0LMEcjhuKNa2Nvf1jrrWCmpVCQJFkvV74TnNqVf9eanXDgAWfqCqHr+\nUvP1jkZRTm+G+XPkwogMrc43N9rL7FO8jgKOCG4jhvOUfPMiJPBbZLy1SBue\nABtoiA71lMe3sI29cJ1OF3YWohrPdj6hEsWHbR1/CPnyU1/cIaAm97OCyKBE\nlmxL1S6b8hntA5DBVHukNrjm3dIp5MjUjijErTLPCHjChaernCJVlPf18CeW\njiKwG2H3TXLrc6QDL2HR1kf6jDng/Na/cKKG6IIOi4q0bmB2wEtFedC25zio\nVPCtYlTgACtRtzTKQgVFPPj61exrBP0dKDQqs4QLmIO40Ty6eWCA84ZODKU6\nEInFUI0ysF6ln0it1qND5KrJGYYiLHHzQxmicefbcQ/3gxseO70MRgNQ/qq7\nqC8W\r\n=WOEJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_22.4.4_1526648324779_0.48861573617028187", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.3": {"name": "babel-plugin-jest-hoist", "version": "23.0.0-charlie.3", "license": "MIT", "_id": "babel-plugin-jest-hoist@23.0.0-charlie.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7b1547bb922d4e283a88063e910b67a73bd03738", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.0.0-charlie.3.tgz", "fileCount": 5, "integrity": "sha512-VAQ46qKbtpSqcKvlXns0u0Q4vKzI4qxxQ+D2dmGY5bwViz5jfvSMa67ZDLuwwyeYBmwkBwn6WqrFD2tug61Glw==", "signatures": [{"sig": "MEUCIDcFTjE8clNVQ4amIzwXWTDngv6nzlF1yMteFACu/pSvAiEA6L+dyKCF8obKeYYzWAXDCWDWeiiAheZSU76w9tFVWgk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBDArCRA9TVsSAnZWagAAY8wP/ijNeLNp6ifXR7TMdUyo\ndH5KYIQF3iNmORTU8DSx9uUWwwrXA0H095yOdHbmxTM1+YLyEsEJ6rZAZWyB\nQ2CbO+EaGVKEdZBMMGbABhvm9GWjEaf5Fpx9+hqJScU7VM3MI/N7Y2mE4MJL\nP9XIDF1fe6Lcg5syr+MlztfVsYOQ3cx2E2U+JsnY5rB4R2HPhuZ33VImXwNG\nn4pp/ksCEtX++7DvA7cUKa4EUXwmuKV3bFUYcLtxc7Ri9uaNLg/40czknhAV\nARkoT5Ti0zcIVCMlMkYavyLZmmWl0bzfUJwwrS6oYMJFAKVYhTWtAAMSaRXG\nKA2G6ttvVugA296YonJIqZ5tPM78JZaNrPBmY2W4+s/ZwmpJAIhK3rhJgW/r\nNBqulBdWdWGOUPFJUjFwNMimDBLEbnJdDjydUM9oDFtJB+CYi4Bhp1s7PyjB\nSMwfNeyH2gbosvioAqYN65BWfzNUvwceE+yRpYxWgmldVadKfwPVS3Zqtloq\nCUDPKHtCZkKgDK6OcOJZ24Rkr2lz7mLpgvlVa+s0TFHQas+aX78OVifAvYRO\nWm78mG82NLWhUmNLO6VIygV+jrVEVCUI6mmZseCZCHOZzj8pMKtg33yfCFGD\n5mbrYbpnLI+b9KZArjTRBK/itrQhJEcQgAuSt42+jsRX01qvlF21FtcHOpss\niQTD\r\n=H9TI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_23.0.0-charlie.3_1527001130860_0.8416549417752817", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.4": {"name": "babel-plugin-jest-hoist", "version": "23.0.0-charlie.4", "license": "MIT", "_id": "babel-plugin-jest-hoist@23.0.0-charlie.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2bc2cc571882f3ca3dec947b63568f91a1bd24bb", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.0.0-charlie.4.tgz", "fileCount": 5, "integrity": "sha512-4LEyadX5EIGyGnb9eItDPB+ebVr0gJQrvfSD9geLOWFJqX6dgfoQ7tzXYoXnkUKZSEddg6B6S3zPImvhUzMAbQ==", "signatures": [{"sig": "MEQCIFXezptj5MnL22F+UxBcVQ0uUP9vSZMa0DGSKoBM4HziAiB42fNmwwQPJxAiXEAOeM5HySZPMSGcH8gTCcunumjlRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBUV+CRA9TVsSAnZWagAAp/EP/A2o8V/0WkbICH1Gj8kK\n/qimfKLVrKTeo0HtbPza4U4rurLpwE0hkX+A0EXvmhtxmcCNPxVjbx8aarHK\nLwhnMP2UNT+ZkjhEA0OYtzMjQAc1McPlTluBlj2OoK2Xbv9wOSEcTrjoikjc\nFxIIqYB06TMfb22kknaU78jdJVoMIGHJMTf07NqQO7DT71ZZte7EXX5cWLCI\nYK62Q+RkEcUj9qJTRUcaZIzwaQ5x+mBKEpxJxtH3whPD54g//A+X05C2YMTz\n45RK35+iAWL8aMpGg/rIxAXfbRowllZxHO1AOTmQghxy7mbfILh1J1Zbvm8p\nareQliOKpei8miWOjbPSQwirR40FCbQJLySnkZeKKfcfYQyTeD3PNeis9cI5\nwWKXAAhos2M0jcGZQQYxcq/jVFFtM6SNx1Uz6I3dMTehNlUqDO6AyjEonRtG\nT42BDKJteq4fYh6GYfWC/+nToCn0KUCRaiUN0PO/tuEMv9HYQxSx6AikkyX7\n5HlX6AFlmZHcrvGqk7SC4PZnWn7wYqjtoHe2/YIFBDrXv3WsgszR1XdCqLj/\n7rU9DiRgc7TOfjhPXI3L64ZuI/n+qLy1vUhBFXCgVEFFUpmPbLRZHpOsanYk\nVoMYpEwENnPOCaX6zX+SEv5Nn0q6xOgDEtKI8mHLwvVCuZoAiDRoX8EycY0/\n+sMM\r\n=011A\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_23.0.0-charlie.4_1527072125984_0.6809279058622673", "host": "s3://npm-registry-packages"}}, "23.0.0": {"name": "babel-plugin-jest-hoist", "version": "23.0.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@23.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e61e68799f743391a1e6306ee270477aacf946c8", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.0.0.tgz", "fileCount": 5, "integrity": "sha512-Lnc98ocwoCv2TCJORObAK4nzuEXmuNnoTj2wEjakqMB/YuewVSFBfL4T8vfCv1MBKGj7sYYhBb99q74gf39UyA==", "signatures": [{"sig": "MEQCIBfOzO9PtJht8oGVmewQpZMpK4//EajzxpPuYAgBkUq1AiBMF54C0YIJrGpyhWqi5YeBIwW/AetpPSSt+6sa4w9Nlw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5331, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBvW2CRA9TVsSAnZWagAArr4P/i5WJo/QUOkEHsWkMdfE\nqertlVBbkwfrMLmw3QbLWE4Mqz6Cse6KRFy7HqrIzyeXy7+uRXEqofzD4q2R\np6+yJ+CL9SYdw2fCuzqx676D+85gLBdyH2PzN9WDXcXl6+lEUPmEym7sSL8p\n1c9AIRRx4zSkp0I70BU6tMO1RuIkl7fOzuQ5B++4X+29ibh5e6zM58eAcSEO\nTDNlO28C3ciJeVST57GbEJiTR1IGnngazHSy1cWPkSuz0MSBdYGe9udPRt0E\nAe/1L0lLaSSTj6fq136/VDOChHI85FhL3n66JnbGgsHYcB0c4DfmLtbbnoMn\n9HBUKB60zr00thv5IRJoPbTNuTV2pxvrN99WFbDDHWCqHJBXPXWqe1fJkXFd\nCK2ED0KaZ+e2wGU0dK/p+IBhTrl531FEteId1BsEUprMBsyqVPAFF/Jq0MMB\nBifXSeOdH5UIkRe9Byo9ytqRogUqFpG9fcKXu3Gu4yfTHeaHflvZfXYx1JOb\n2V9T08ilFkqI0/u5uLAwunTmG5UDCB7iYNp7+TVAE4BvxGeZGdD8b1jPzg8Q\ns6bvZbf7F2sb44+f5xVVelpTyGfZPQeItgwdzPvqP980yApB+j4qJbev2CAt\nVnnZFvcrJu226fWzQUNyMdV4rWTPXmLXOzdtRUPhfg9e+eKMgBNm6BMloUUs\nQlNc\r\n=VuFm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_23.0.0_1527182773888_0.17297563200195065", "host": "s3://npm-registry-packages"}}, "23.0.1": {"name": "babel-plugin-jest-hoist", "version": "23.0.1", "license": "MIT", "_id": "babel-plugin-jest-hoist@23.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "eaa11c964563aea9c21becef2bdf7853f7f3c148", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.0.1.tgz", "fileCount": 5, "integrity": "sha512-f0zE3HnWNqYXwFIiIGAeC4TmA+fcg0dqpdeEcJ7kJWqzhDKFzyM1c2AJ6GrddUw4qIjsFWw3bQg69e6wqJLIxQ==", "signatures": [{"sig": "MEQCIEVBBk0eQLWC+yCx9xCzKDbSd4DzfG55EN8CQFThn0ltAiBuqBliNu40rB2DgHzltWVVr0dFOP9hDaWtDcavkvrpcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5331, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCs8fCRA9TVsSAnZWagAAPTIP/1QqMdLKmLbkN2Fztwa3\nEoGY4nlcwUJlYVmHeH+GFewwiX57ua/ezG9IswEevAf40kOLS30xwfAFrlvJ\nFGINVi7WAwcmzHBYe2ddlC1V+AbvlnJlMEzef3TMu4W+IYRQ/Bl6en2kAih7\nSarCwZq250Bz56mQ8WcYC0FKkIfpTtUIUM1ruqD7KSbA8yyMbUplzu8wGjKU\n21Iu6GRA/tEFDfKeqaIlKFy65jjVnw4aR8yhSfckLO/gSsPOMIiN/ZheztPY\nJLyu1EkXxd6qGaHy+UChlxntl4dILFPgh9tOjqMbZ6Ew7SUE6At9UM9VHngB\n5Ot6TN6cZOa+yxZUIKmTM1/7MCs5mBom243wt79IylbR/zSMSjYEhdS3qpvG\nukGlY+42DuEvDU3kYaXiLDIod4DlqZ3jWuHq0CzNpVukjzIG7CRWxHoP66ol\nscztRCM2/SkAXlQY02zWzVTdzcBIWbypb6T6H5b7u1QLXNRbvrDbzejFbnS/\n5i/OS9WKQfsZxSmarGhWmKqd2F88QB7kkEwC8QFx8Ry9rmWMzCz5R5eJIuNb\nNwCYYIKBv3HO/NXRKl/HLNw1lMqUkz36uAzLr4Kv5EcRKNSd05B9m88J833S\nkz9IOlEH3b9tHN7AVqymtaLirXvullglOideIMs7QQEiUjkTINNQCCIfBJfS\ne4hz\r\n=Z8cY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_23.0.1_1527435037618_0.5180615724418758", "host": "s3://npm-registry-packages"}}, "23.2.0": {"name": "babel-plugin-jest-hoist", "version": "23.2.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@23.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e61fae05a1ca8801aadee57a6d66b8cefaf44167", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-23.2.0.tgz", "fileCount": 5, "integrity": "sha512-N0MlMjZtahXK0yb0K3V9hWPrq5e7tThbghvDr0k3X75UuOOqwsWW6mk8XHD2QvEC0Ca9dLIfTgNU36TeJD6Hnw==", "signatures": [{"sig": "MEUCID7EvlYA7q2Fu2FWwo5HP8eNGhJIhyKSuLxfqdSuFWPlAiEA0JSIDzdltJmDEU/McbKkBXgQ1z/i7HW24gsXulUhVLo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5351}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_23.2.0_1529935507163_0.3677377680759144", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.0": {"name": "babel-plugin-jest-hoist", "version": "24.0.0-alpha.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@24.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "27068af2b114cc38006448eae2688624d2bdbb83", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-24.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-YHuezA4DP3cBNh+Ck6SS5+3Orydz6djNJ1fJaO1013I8tJr37zgyUYtZj3gy0kRLHWcBnBYPs2IIrsLpJzbSFg==", "signatures": [{"sig": "MEUCIBz2jJikx1in6ouTU4z8gXB6dBcix1S4r3guujfGZ65BAiEAyaDAvxClE0d56FL+Bjd/ok9GgCQQN0jCgutZgWEP6OI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6541, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbycooCRA9TVsSAnZWagAAnREP/1QJK8RQJgT8gqXlEskH\nJq234l4KpeBRrIv6L/gRfAo0ROVvPaeWUUjSRsO0qvC/jLATmvO96EXxXU1H\nwEqrbC1E07pbZUrhcrmY030Uf8COyLxlO9vyYfSxP46ZXbanbFHWSn0V+8Fk\n0xeeL4uS3tzPGM3ZzZyIhouMWaO6OmE/WstuuvNovgPjefXiQfYIrQZDGQOW\n6Fz38ipgBNlKfeKHrd27Y2hZy69aumfnT8rIO6Ghw9CDYlXWLEfTmf05dgNX\nIuIawdYzzv72+74QADLUhyVpNPpqLAqq1Bk5ERI2g7OBZlAdFJgbl04Q839o\nn17I+T7joq9v50nTZCwrfTiLa/Z31CxjVWAqjyknrSvmLXaGP0QoPN01+ulT\nboNX1rvU4hQDrEudVx1+F3oK7as/5tLWloohteNg5IsMKXALNushsPE5IdtD\nm0y5OLxwa4zxx3qfi2m/wZOrKjN25FuqbTcegluVwH76fNiRxKioNA3UXWPw\nveio+kDoXGsvEN0eKgvR8pvFDS7WhBHPuyhMnrFMuPLj/25wkHcaAh6g+uTR\nbWJsLlHNhbRXGrEo0eFng7lG/93f/lWPtqLNN7+7CDSp2GGGmDjJ6deYvLwe\n/FepaxeFESE8rH6hxJQYp/iPCUjYIaaVazhkCbOFLtEu56j462+phxDOUgAC\ntmHo\r\n=UH5v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "22f67d49ffcce7a5b6d6891438b837b3b26ba9db", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_24.0.0-alpha.0_1539951143708_0.34354392725281424", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.1": {"name": "babel-plugin-jest-hoist", "version": "24.0.0-alpha.1", "license": "MIT", "_id": "babel-plugin-jest-hoist@24.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f34e318af20b8ff9132b8f123d16c5cd4be4d9b3", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-24.0.0-alpha.1.tgz", "fileCount": 4, "integrity": "sha512-4VCaV7i8vTutkW7dhNrC/Y9ObKoxnXfUvzikZgJuD265I3CHQVZtlSk/Do9VcSHjdyvwKEn/hQUmC3cX6+t3RQ==", "signatures": [{"sig": "MEUCIQDkCZsAKcparJfLXqkCrCBwv0gE4ZELdvVv3qCcjAvpHAIgdKgFW6nX3J45PUrRaaSsPKxIfHLFDByU9cQ3OjdnbpI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6541, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbze5ACRA9TVsSAnZWagAATl0P/AgZCp4TGQzGafg7a+uS\nZomVldVdwxlxkJ/2B/fc7uzJ+bs7HBZHDczvI41hqxeguWFCmlvS8wYd1ZZ6\nBsTdHCRZac8ibr3TY874+HvFO2oozN+2Q3St6PlpadsZtEkvo8QA7SCmTDZk\nDvGttVq6aG9T7bAZyyZQhX6A1whoJX7DTBJR75Dm+xUN4UraCY9UzT0FKNUl\nNFmf37GBeokj6Wfcod4RQCYOSkSZTKE2jrfDqpOoyWScfYqIpQ955gAkQGJ1\n6fovcKaG/FZKtlefmNb8F74DAJPTkfjXyKJGVAGt1uGeS/mBWNRUCouEri0h\nsNWHHg1iUPtstFqUUgVw0RP9wTC4CtfnBF6HehKNo8CmyMZ1BB/fEeSrUgIR\nifi6ycRoqol0n0kUdPMU6EHPSBhNyWhF9NO078fN4QCZSPHDuglI8ZvbBweB\ntXrTib1rtOtMipss1VT9iRYW/Gnw31fMmhTPqIfUDpd6BTNtS4JoGkkvmTUW\nV0fABJxN1233Vt464wGesrDpnzDpKc2qQQLFZZORa8C39qiHMr+XEuXLU2a6\nPp+Iti7nOoU9sfYqj34GTFqvF308WA16JlyMu7lpYO4zJlcBjHpr9qAbUl8g\nRzqsbYBzsKIj/c046MJ9ii4NUI+WW6JQzW1KB4qo6hXjsl7TNorKQplalgGS\nSEfC\r\n=9kvA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "4954f46708415174c48a58f296a605fbe1244a31", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_24.0.0-alpha.1_1540222527889_0.7373207995079336", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.2": {"name": "babel-plugin-jest-hoist", "version": "24.0.0-alpha.2", "license": "MIT", "_id": "babel-plugin-jest-hoist@24.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b2e53c9e09a96a34c23072ea4ca8c498e0428670", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-24.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-lj+zLXu4ixXZhmjQxc4ZTo1Nhhb2Jx3mdUDMFdV8iAlURVAJiiP8bAnbVmOGxn7uMBhsTWtDOfHaAvKRMUQp3g==", "signatures": [{"sig": "MEQCIBmZsOtSQlmN/FxxSaqPRE5oRzbea7JW5KCO7oElYu8UAiBcbATw+iMVdHxdBjg5ktaryh1/IjjC7QdHJUbo0rLgzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0Z+BCRA9TVsSAnZWagAAQTkP/03Yh/opWkLh0Erf8vwW\n7CgG1tsoE39mTy+2qsTWvk1q6EZOhiwpIqf8CnR+qPbCM3SAW8z6K/6OPvg4\nzSSWQN3sEW7AhX4gkn+lfqmGfP5KIDNFtnzYmT3WHjGrNQ9Pfr++vPxxThau\nfeAvPXAz2cgFM/4iDdYdlzn5sTs1GrU+W5j+dEinuR8pSgbbF+QUK7gLn8Qs\nN43TSQGbKj7+8cnA1ppo8zukhc8f3ZrK1rg4LwigBn3o4yk6bhcZtUBrI//K\nu+m5kQbYyBzmKSWY8FHtyfiH+rVxIZGYVIWXJdJT1iJWShmGqEcPP18V4uAT\n+ICsZ0RWBEGhdbpja4uMAhhp1PlP/Wl7eBxf8hrPn9+HhNCJ4yJjsqW8ipFA\nPE6Kp3CMeZFMiOFvw5gsaIaEUNfMB847f1ka8FT1tJtrdE9UbTG/jHqiSJsp\nTypF7JEcJtMw1wq/jhL8Af2vtjq9U8w41eEXgzva8JYhWO1osDBs+qoH0Bu8\n/pxPpqwPSJ9xTeAZvwVJGjIPtrb6f8z6vBxfvdDiwT5xaUERUPXYkNuF8E0M\nkMyX8n8xHxXOp/Nw2MN9vpKrta2D0H1dJMnkKeUKBYQ1iNiFHIaEt2FEDzVn\nHZlWLzVub/1hf1hsXs5Q0wlNUv9/udmMei1cwx079e262vS0OTyYdFxLIwZS\n2IEh\r\n=MMWE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "c5e36835cff4b241327db9cf58c8f6f7227ed1f7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_24.0.0-alpha.2_1540464513017_0.6260717129215272", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.4": {"name": "babel-plugin-jest-hoist", "version": "24.0.0-alpha.4", "license": "MIT", "_id": "babel-plugin-jest-hoist@24.0.0-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4fcb1369b3814f98496153c86946916c22d0a0aa", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-24.0.0-alpha.4.tgz", "fileCount": 4, "integrity": "sha512-7xF5AEQCJXqT4rRvIK/tUnAAa4OCk62yiemep61qNgyTlGJNeS/SC1CecPOkV0KEhRrVZOhwLScHXAUWxaYagg==", "signatures": [{"sig": "MEUCIGxw52Rw/QzA09y6OxE7yXElSAAxz0BbBZv/4g2OwDGhAiEAgeu3GIZDut/JNO3iwVE3hdbAPicSS58k35zbvhyF6ks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6580, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb00G6CRA9TVsSAnZWagAA/JIP/jgUZ3ACCd9/qWHRFtiH\nXvUFMbItKg1LzC6BVIMZ3pSPamyNlPO+TxZNcDWsQT6UYD8glNEXf4B46MBC\nbrp3rvtQD7DeFOLy5AA/yTYlgyWwlKj1iDqVCcMaVDLsNhiiwqNToxcAzomL\niopVwDvDincAeQSlJRjPefM3tfUTs4PcDqpRzBujBXcyNWK49xXYFzORfdcv\na2maZdNRqrEj3S9HfqUu+uJSuarnEpWOCL6IBuNbUfAOw5907qA2a+YycL7X\nYhvBMZx7qCc3SSEHEmewTx5UQChwMSRVbcZ5Vvz4eCBu1/GyCRnz1xBsVjiI\ndcAsrEAXSTTY9f458dqZEnxY8Y6KWJpIupL75LguZwf/GTLQHzyLPrzbNBBg\nrMLbeiKGKYjUTxkonSkyRagje7pj+NKKE9eA/9ALkXHJuOv0OuJsjW/FZ1fy\nqr2zRyjYNbwWZYd6bk4f1/L4sCZUS0GfChKRZyeloM5E8ltWci3W8gtu7Atv\n3vJJ3l+Ous7wYrqYYVaOwUatC5IvD16N7CtFz1vLOfj/Ids8QM/tzIt4UbUk\nHKSS+heFJV1OEcu8X1z/WYPCztsLlrMuCl7JZO/HKJ+1O3+hDlXEeoe/XQ9V\n7XgYFeZ9LWkm0HJydIZuoFzjk4yAXjomdM8XvAeY0ux62MZ7GeJ/Q2yv9o9v\nQFb4\r\n=Zrqy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "e41f0bb257c6652c3100b97a1087f9f812fbea0d", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_24.0.0-alpha.4_1540571577486_0.27292745081808345", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.5": {"name": "babel-plugin-jest-hoist", "version": "24.0.0-alpha.5", "license": "MIT", "_id": "babel-plugin-jest-hoist@24.0.0-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "eea811550b2030edbb75d8de4774a670c745468e", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-24.0.0-alpha.5.tgz", "fileCount": 4, "integrity": "sha512-FlzSFMyRQrsfAHmNKgXQhPnC0vjYa8ZkJgCY/kXrf0pGfemzRGJPFTB2BiX9fq3W2oAlp6pZXbAp0ejIDKGwlQ==", "signatures": [{"sig": "MEYCIQDGogNL1suHRdRvMGsSbta1aUSU26RpVtkw2b92yKThzAIhANp1JTOs49kRjOGpjO77Zmuic5qE5QHzvvHeaYhAnR9O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6580, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5Ye7CRA9TVsSAnZWagAARsMP/RaZhipsvyG41GrJTFCz\nhoz3Zpe2l35O/AcoBNybb5YSFmlTKEJGT5cjYSEeJImM9wSTycrpqa89kdE+\nnHvp5aQHRNYk4QB9hyJFZi0VTLTVrpweHBVphzr8+FDS4mnbVaMOEtSdoqSt\nxPYx3FygiSh6jfB7fuP1jSEOPAgOF1ASx5wJeUlT+xBJp9cpxLxYKDaGoX9g\nVEPODL+/XYdAkhJISnWcH2JDzi5Ikk9OVUZuZsuK43ABw7S5uirz4/FKuhis\n4BdXk9mOH+BfG3peeWtpFDRH8rzeEe+/KPX8WmIygIFdISEcV9wPQI2XCN70\ng8Z3lxx216wEaNjhQ0nM0zE/zZvwXK9y99U4TgzEljKgxB4armZu+aB2cfft\n9nYzzmBEnqhnZWEkVwh6PLczZzncono9Z5ehqk76Z3xyviZMHy7GM2DX0rO8\n0f9F9UBy8kIGY3ydch/vO62i9r3kKKA+NcJVbOucIbnZMK5pLF+iHagGTMm3\n/OBg8oQOlNL+5LCZhPU6sRM7k+Fdi+z0YS2g9GBvi+r+ICqiswvYJyZYgTwl\nrZcqwJP3PlyKUXSvr3pl7ikg7MUQZRFeTKBTDN7PSwTqzDaQ8lGfotgcV0yL\n5ckXMHVVSXCpM0xLSI7dXEMOtH6ImfLKCE8USM8IVihOa3zatOL+ol/Nt7gT\nskLB\r\n=NBSZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "2c18a53e8ff2437bba5fcb8076b754ac5f79f9f8", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_24.0.0-alpha.5_1541769147227_0.45098516203369177", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.6": {"name": "babel-plugin-jest-hoist", "version": "24.0.0-alpha.6", "license": "MIT", "_id": "babel-plugin-jest-hoist@24.0.0-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b1e3e1b8d3d64f1f94dde438b7fbb5f91506b6f7", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-24.0.0-alpha.6.tgz", "fileCount": 4, "integrity": "sha512-Z06kZ8x8EtaBgUUI9Rkg3oZBq3jWhlF2yHFOzxBvjkQGQU/r014RjcIVT5qKeYDGn9Z8eYHy8w//a0qOphSI3Q==", "signatures": [{"sig": "MEUCIQChk5dsoOrWuf5JMqMKVU8WY6p9N9x3hmgSN0FYlveZEgIgM6zTqqvE/UxRrW6rcIQKu5W8t8t3TJln/9I33Yazckk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6580, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5cimCRA9TVsSAnZWagAAILoP/2myIykDa/vtFbp1PFFC\nitAkDEAG5kd1+0Sto+siCjmJ82k0dLMDAphbioJEI+hl8Jiq+hikRx0eDLip\nU7yd3MnOxniSe8RrvmOag7Rzgznm9icREmSZ2Dqzax7n3Y9SJT0b32piIXuK\n6c5zhM1n3Y5Xa7Agl3iFwX4dipm08O+RBhLNdSi5lxirj7h0/0Z5uixongDl\nim7efmGeBi1jUMpr/mwVNSYR7hnEwPh71rapqh6k3+uwYosi0rx6t6Wlvt2R\nCleJmxlzvbg9hEZxGu2CJanQWXO1Lr8eSuEvqO3jJoO1XJJhwnqHCcKPsLQl\n9CyyVslBv23k0gV8ioSN4fuCqLewzrpwX1Nc+AgCsF8MKwc1RVHs/zhkJsBk\nBWTG6SsuQo4zIrsrXQp31Ec7Uprh+gT+q1I0VV/hc08F78+weiOVZqFNKWir\nU5q9MoMgHsm9k7UKelgbVRbgy8Xq0b44GSN0NBafpe3RjlGD3puPm16ZKo/t\nuyC2dQ1KybUDog850xTrjmYaCwbgV9u+BftHM1GwA6wL98d+9RcLN/Svy3QU\nL1cvtSwAFVdbhutcxFEQ+VsxHiUytNI9f91s4FCw/pCO51E672gjVAqwbXsx\nZS1FpMuIt0dRqNXz3cbinwpmVGBqCOi+/e76RchWu+WE//dJ2Ng5Y4l9nonV\nnD7N\r\n=IzQ/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "49d08403a941e596eda1279c07a1eaf4d4a73dad", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_24.0.0-alpha.6_1541785765612_0.458174886875242", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.7": {"name": "babel-plugin-jest-hoist", "version": "24.0.0-alpha.7", "license": "MIT", "_id": "babel-plugin-jest-hoist@24.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6393b8cfa1dbf1f73e497f1b0b548f8de364cf4c", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-24.0.0-alpha.7.tgz", "fileCount": 3, "integrity": "sha512-r4oKmL8Sv24GMKjzxcj4W56seK1sS1SCDk9M42DABdJlMgYtC2ld0hJK2Por+uR9cdHBW0mY2N2/ASx/qNt3Dg==", "signatures": [{"sig": "MEUCIQDuODvLF+ptzrdLwo5bEkemFcSUOdhdD5nA3L/XYr4nnwIgb08/p6O6EOuNB4CVCjtXt4lR4OzvJSsVT+SaWNoAydo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcD+DCCRA9TVsSAnZWagAAB8cP/3iEBS4+o+3M9BWdNpq1\nsSYRoTi7g/cooMrq1dSzDYHvGSjrVbO9gnzhbf8kO+icae/aMV+7+ysrP+51\nqbKDw6PWOes6Yf9bWDz4fSP9Pn9SlikEUDWNsd+aq9FRvTVsDi6/Mm2aWC6h\nE7Z/jdTnPX6Vp1+LKJn5/9B+ZF4utXAp40EoxfLwSdD1rq4B8ZLp27gaS6iE\nIlzBCaYJvPzJ4v4ReW6zLFFLUARdjlb//euR+gcQ9msT6UUwJk2smscGRbRS\nx0hGmDBw3QiTsylr/+XaofIq90lnsKzTrmh3NGemGuVNJPl+F0wh7S2h5rjm\nesDfD/uAy8Bigz8KCXcMtoA+vSwLWdBjqUZqCmns2DhSpuZ4YhW4Fvb/GAAc\nXCX2L0OHfAlYa2uM2E9uXqCG1p3GkJq3yHIeuNntVr13PPQ57TxmbSYdi1Zs\nj3d+VN9MrQ/yQMk0e+Mk7okMw29ThtoBEUo6Y/jY0f1Cxuzo9HNYckbHA6C5\n0YCmLS6IFE1gnQKp/ua6U3683PC/FDXxSgNdkLdsEvaett0GhWwR0ueHOW1h\nkdSISGwxwlfYzhSPU3dGcdwsXXBdzRQBWSBfhNBQLpmzqvBAi3oKUoQjgwbk\ne72/kwpoM5BeNSS5ttYL9O16703vTATdR56UVSObXio/Cp8WidktOrh5I1SR\nwVFO\r\n=mfh2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "4954f46708415174c48a58f296a605fbe1244a31", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.10.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_24.0.0-alpha.7_1544544449478_0.3684909377204906", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.9": {"name": "babel-plugin-jest-hoist", "version": "24.0.0-alpha.9", "license": "MIT", "_id": "babel-plugin-jest-hoist@24.0.0-alpha.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6946864ebfbacf173fc3e5471cd2179613862f64", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-24.0.0-alpha.9.tgz", "fileCount": 4, "integrity": "sha512-cI94H2VKm9cg6kr8cvk3XWyvUMAT7oaLh9DIUl0ydQvGZ54epAIvTT0spALI1JqNsaWynLO0ReidniqEnaFFwg==", "signatures": [{"sig": "MEQCIFqHnkHRT+hywovpzrNEyDTFmCcLIybEFPUZlyzRlHqVAiA8Wy7mm1bTCmW7bKGVRxL9DBnysrtt7+A5zbascEGoZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7268, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcGlQ0CRA9TVsSAnZWagAAcXsP/3kxY7DPm/v7FDt6tJd2\nSPaqOpvjvJp8oCe46Ry+SDPHCMl3NpYkK1zAT7/xKtrRgrpGBuNyzxxiEnOx\n3eVfk+tEiuTtpSbOZHnReQloZAHS5JUfgBwAUNZZPaxkWtaDLYs4ewAIZYZ+\nBG854iAdVDAVXh5IJTxgJOEBJtb5iM4FEe5hXtJm2vHsTmuou2Bef6Bvdqsx\nx2XddYf6yC2hZ4x3RBBBGNxcW38f7qkFKgVUiv+CM3RYSCgyZpegvVUGcodW\nl2EQK4r4ukdyBYdxnJfGjhTQfjJTUkcMwiHcQdCBijHEXIBd9JtcvJ2e/gT0\nTy4L4HZuWTTBMQP7XxNFMZd0pkqPQwe79nghcCt7sQ1AO+9IF7pFccnPXy+W\nMIkbzo2zY7AOn4TNwsoFiS2A7jI07QlYCvPRVi5v/3vKPfREqxac/nQ3sga0\nLg1VpQF90Sf64ayupDxIwhoV6Kk4oVB0e+vTzH5NA5a5f/jIczLWG8dvOHFo\nAzAz6Hx8byQdQI3BT/ME906av5SO4BIbq/UFXuXyTowl8UgcKtvgQ0JMmSvp\ny2HWikL8baBcqbdvC2HYnJoWbc6Pctpsam4RO/W4TL3MNi1XECY7fsEJcPhy\ngH8UDrH1CfHhZ2MYXV+ijw5sqcASO/vGRGh0xt9WYl8ocmhOOG3p83hPwnzV\nGfYu\r\n=Y3Kv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "c7caa7ba5904d0c61e586694cde5f536639e4afc", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_24.0.0-alpha.9_1545229364349_0.4673393198565561", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.10": {"name": "babel-plugin-jest-hoist", "version": "24.0.0-alpha.10", "license": "MIT", "_id": "babel-plugin-jest-hoist@24.0.0-alpha.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f874251453d99ffcf7cd48fb873f2a70d8dbd619", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-24.0.0-alpha.10.tgz", "fileCount": 4, "integrity": "sha512-dxBHeJK7LkAgVxX4IEi/EHTGhTbIjOKrKgUm9oOzF0PoEct17a2RgYvA1UydK1JUfP/sxFodLdpbCj2xB+jW2w==", "signatures": [{"sig": "MEUCIBRiSdjDzUoSHvNXiA7zRztvEWIYxQODhVFoi8mKSelzAiEAo1WJw4yi9GMKSJPAyiVk7XnBFYnyEVLS2qfWzpKR+y0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7394, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNijDCRA9TVsSAnZWagAAG+UP/1mWztFsGNV1JKOobj5e\noK8V3dGBzC2px4pX0z9R3Vwf1qgCP4H3f3YzTGafDSo5Ptqk/hUHNpeBacu2\nPKvJNdLPHWQMvasnzyMLROufWLT+UBETucJ5qR1JobCWKo/QPxx35UET+2sV\nAf3M6pqzQdmpyQqwyE4GEmWfqw71IN+SPZ3jbgTZJaZy2CpBBiOS3tydknKa\nhQ10JN47cVYO5Uu3KPNwayQXlYE3gYm5U2mjxR8V5QVZ+HIJOE9tlF1djovA\njWxIyWNcwQCROO7KQrgyRR7x5tHv0ZyAB8yPw2VfGrKqmK8s/VP56HgJT/iH\neHCwphVbO/NGS5jCVE5pr8+IVGswu0UIyLKJGabj2YbpYhDo7JVOIeJkX44i\npI6WQCfNDYUBgK6BFWAPi234FOSLbEh5iJfwDrNSxRshH1NL4ofoNCxEFMcH\ncSenxJVAXFxU86+xLd9pdN0VrAdMNFw+MzJ4ckhLy9yFO68/2Kt9Wvv2l0jr\n28rRE1QC9I47RmZqxW+N2pBuOSIRvCxSqqlnf7s2y66Mce0rWfbqk1mzd1Yg\nlI6OQcjXYhuTL1K/dhD/VfNcdrkTU+I59Iy0tPgr27Bh3s+fjq5og+0DLVQC\nYfG+8HMmwqvOUgUVN04Va+6ZlHSxHQPWe6Yo6BA+SqRHiwZ1K6taKAeLz2oa\nNhwF\r\n=oPbm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "722049ccd66947d48296dcb666bc99fccab86065", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_24.0.0-alpha.10_1547053250504_0.7534252177290055", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.11": {"name": "babel-plugin-jest-hoist", "version": "24.0.0-alpha.11", "license": "MIT", "_id": "babel-plugin-jest-hoist@24.0.0-alpha.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e58d446d323dbcbb9b01f0d37ac82ab2b9d422c1", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-24.0.0-alpha.11.tgz", "fileCount": 4, "integrity": "sha512-ZZfqt+RiazHIp/Lsl114giQmG7IXhKtq1yWQrTLF5Nwo1DSafefP3fGu/z/ukWzdbscwB9djLzWMXHQstGHeEw==", "signatures": [{"sig": "MEYCIQDqif7s7kVSyguCVdS8BlOfKwbor82dPNzfzUA6Way1QQIhALKBSn4bIaIKGeofSBRneknhT8iv/+0vmJ/+qDsdm3M8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7275, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcN5HMCRA9TVsSAnZWagAAKOcP/1WsADdn90DQOB3TZTQw\nC1r8BfX7DHvl4QP1ygXTmD880TpcqBECizyUiZ0pKpW58TdokUwR12EQNxnX\nX55dO0qL0viZki3k7LDxYSp5VA/0XP0HNrL3n+y+aX/cKr7/JJO1ASntTxJ0\nHyr7904QObVjTIF5ZYuZeanXfjDcqr6vEhNaK1rvALDNwQ7hCa2OJBHmFO+u\nsj9W0IQG4Qy0WFFEit4WEnqFW29TdJFF00iS+/6SrH4XMR2PlJlyegoJKqrk\nXswWoOv6UstNs4eIQea1MiQ3Alr2Bq6wxIqcsIzKTNkS3BkhuVr3qKqUN6JU\na3uf+iG0v2N38fu0mwRJoahC8V00GXxQODXkGTzO4lG1UTNOMfTxbHh6sjDJ\nNu7eccX69t15z5sG46r3QwZx4Acb1r7cJfInlP2L/7JNHwmUpE54AIiwqGAl\nIRLLL6D9Koc00OO9ui8lWBmXRrpaqTLeueeQ89UXrYZet4gyOj8yol1xeVTU\nsUfA2QmpLlrXEkJmPTv82qSkiQ46Ni1XPMFpa+xUmvV5PtHasVGxOjEdYY21\nvr5tg5PdkpCDMr7R310I9OmG6fe6VF2ullOEJLzUWf3fu1mDDWuLtM7PFuMJ\nnVCPs7tPNM21oB149PpEV/TOTlmYd7PIiwWIXCBVlSNQZ6jBpE3ZrjZ5bZ1V\nbBEm\r\n=5rIb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "6a066c6afe2ae08669a27d3b703a6cf0d898e7b7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_24.0.0-alpha.11_1547145675568_0.9443369825259749", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.12": {"name": "babel-plugin-jest-hoist", "version": "24.0.0-alpha.12", "license": "MIT", "_id": "babel-plugin-jest-hoist@24.0.0-alpha.12", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5cdf88aa83e0d740099d43d98165127b37514937", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-24.0.0-alpha.12.tgz", "fileCount": 4, "integrity": "sha512-3f78HgJZ1E+frnbCAVO1seGi3QSwnch+ofHAtG6RxJyHkg37oLgjPrw3G3dzjMtVv9lvALR7e6/dzBeM+6bQ1A==", "signatures": [{"sig": "MEYCIQCNjYLG5BhqmiJLOL+j3kscPDLhf9OqWvRwVfzeUOSzrAIhAKrs7IfkeB3FI1UxtXJtuVmlHfK2e5nNouWO6O/44ql3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7275, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcOK7yCRA9TVsSAnZWagAAeuEP/057eWVb8kP3DCDYMs1c\ndz+9mrU4oOvZ+2EvPhPepJs2p3Z/R3cBef/1dbfqGFoZMPGxArVMeuqWjFE0\nk9JFG/cmIOqTLg6czbSZYRAGjwjol7/DKk1BF2LE2vsmrIBmun4pCqVrizi/\nN/B95xDH++0Np/5HLgEHtkl7FQQ5fdw5s+G6jHFep8gjhwmiuFFubKF2gCtZ\nikbqqVwJmEYios/8PfR7pSmXxi2exvh854NWk91hxqznSOYjZpIvAeT5mKed\n2Aw05+aWnXetKfyQQkMkysR5EITt+wT5ZPu9rKhZ5aaTn5ftaeC8Ijps+MvM\nlFbLSjsCZBg2tssA9x7yIm+RnxFjv/JHH0sILid+eGSfBAVnlL6ZVdvWsSAW\niyoaHUAEPJO322nBfGj9xEBF4gFDsfNhy3nSHKpn7iIpXUdKaiE6Tk4jDhmv\nnB3972/S/GjA6iUrkRZDIRNDLQjiq24hGKXH1jUSr5DTnco/IPr0PrVxMgKz\nQHI96FnZQDx3piYHvLwv9Xsi6Adq6EOFbK0REt53IQC/62vvavhNkU0g8UKT\nGE8BRql5lEIDu8NfxK9gZx7XTk0fh693DvMKAynI7AaraQVp0M8Tss8oIlSs\nyU5zQVEo3yKmTdGs/HTFLRTBOWyK9D1D5c/vWI704c1+4Rl+OCxX5Tuwfxxk\nrZtM\r\n=HcxU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "4f2bcb861d1f0fb150c05970362e52a38c31f67e", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_24.0.0-alpha.12_1547218674309_0.17095835967628736", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.13": {"name": "babel-plugin-jest-hoist", "version": "24.0.0-alpha.13", "license": "MIT", "_id": "babel-plugin-jest-hoist@24.0.0-alpha.13", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "91292d81cc46a356ca2f50c0f54c88f27f54c8d1", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-24.0.0-alpha.13.tgz", "fileCount": 4, "integrity": "sha512-bq2UCTk5HDfBb3xmAjW/HuwT8h28Mjw4UMtpjY2IshAhUE73Y2weBMkhlqt5KD80Pkc0aPUrh8Wyrqgtq6sjyA==", "signatures": [{"sig": "MEUCIHmyJJqhN5ldLEvdi0cFzIXTDOlarXrEUrCEFeeaUDkwAiEAm1yoxYnc0xOJ/AZFwEA5dt1FFjoRDZXWAa7w39mblCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSIUECRA9TVsSAnZWagAAPvgQAIkZPzo4J85TM87cjTOp\nf/VmfxWrfHQ0ygbqYXNiYEghjrPTVkln9Vi7PEYKV8tuXMq58SKakyCHNumB\nBdIYhIfRjFVx71lKJGzYQ9XxprKMtnGxuPzcitBwCFyKSrDVNiL4/ebAjURE\nGpnSiH5niv1Zjs/vA9yFiP/aE+vUXKoJhj+5Z5myzvR4+bKJ5cXs9RFhUWtP\nKFvoXxzhxqUTCEvq8uzSFV/EyWWVNjhFvGtcvcWPjJzncEIqvphcbiN5K0rb\n1OiWvE4ymphFn67jsD2aFxkAJLu2+ejmVECYo2QujxrhlvAfsKk3jvEE3bE9\n1GYQjJhmq4qPhtDMU5t9VASoY0ooIxqyVRHP8OFuFTBEKnpNVTBGDM242Db7\niSafYuSlqLcJMtYeUsV5tWTMSffC7l6uk2SeVVZusdIfDe0mSrZHUHVhZ9xC\n51mS9O4Upczl7gZcKxh+ihNh6xCxxefo1RBGAaB6YX2ak/aXBVqFH/E/IICr\nkcPbcuT9OP3Y4/ZEnfetNk36hsr/3je2eiHwsFs4fw0v/1yEtL86o0+HOBNx\nE7YK6uEIPTyJBW2thqNjEK1mvLjLfCKoTIYn7E5XfiiyZPrxGXvzUhOClRUL\nQC6PGOMqacYMFhv7Zw+PiIKguCIMHnWeQ02A7JNId0hOJvAy8OCbPLF47hbj\nllLQ\r\n=0L7M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "6de22dde9a10f775adc7b6f80080bdd224f6ae31", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_24.0.0-alpha.13_1548256515551_0.0951203181564495", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.15": {"name": "babel-plugin-jest-hoist", "version": "24.0.0-alpha.15", "license": "MIT", "_id": "babel-plugin-jest-hoist@24.0.0-alpha.15", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8e3be636f6e279157ea8e57aebf877a3f88936d9", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-24.0.0-alpha.15.tgz", "fileCount": 4, "integrity": "sha512-7<PERSON><PERSON>Dwinjb1gj9F0L6kTGSucVr4nRkAVYaroLe4bAgP0i5+0k2mJ6YNftUWExNOhq2+RtvHb4cl3T/O0IO2TCw==", "signatures": [{"sig": "MEUCIQCFIJhDIQX6XGtrAdKg0R83IpodEyb/207xUZWh2ll91QIgcaayXUtAA95PW2rriHOyWkxAd2+OrVw8ji+6P49FNzM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSftRCRA9TVsSAnZWagAAFu8P/Rk5Fl8bcRTjzKRvXsVN\n+yxQR5hlHxEWKJy0sZ/PNWyh4t4IwTBCkGqevJ123xk3E6ONuWzxT9SdqgnW\ny/Tft7NgBU4xsMobEbpME0wMXlAO8m3BLQ+gKKzIlxLx6kAibbxZYGSkJ9yI\nQyTsxngzsF730DhyX9IW3dMUVCNGrUc77Keek2nBepIAfFMLxXsCjMnmNITt\nAWACzx5bb/xLKAl0f1WDJlzB2qwuZnDVxZ0S7JyWfrIcVugR2I/9Uq+K8ZFv\n8joIQDWzBQgMmUkYXwa76hpQlYpE6TXH5tR/6ROZE+wsAURStIN2jclLuzRU\npKnRdaKpl8BHL5KKQ8r1Bm2691ZIMs/TLdexgkAOoJtGg6ZOY48BYzcwAk2o\nEUBdfhJQ6CUUzpayGlz0grlJokaOYIZJ9PfRmH4oXuK+ekSpH/+4B1Vnj2E1\nZNgtMnA2tirsxKlzE5PYiPkwvmzX9OcHdrRtKeGoTJUmXXFDmRz91BEgDU7R\nVyrNeI2T616Q2xsO7NV2rg6/3cYC+iNqLSgTaUG7biz/xxzK4hoXUkkMXC0R\nXXFhqMElpPJubJkn4C2OhUBEtYm/dJF940qZkMT9qTt6MbcxNWNKZJ+JZs3r\n2UmyAWep4PhfGnxiphdCyNJlr4cQMxfIPKB4qkuFa7smcbNXwGhSZ/oAMs7X\nkmzD\r\n=xVBc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "28971c5f794330e8acc6861288e6daafcd32238e", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_24.0.0-alpha.15_1548352336748_0.9538701836926087", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.16": {"name": "babel-plugin-jest-hoist", "version": "24.0.0-alpha.16", "license": "MIT", "_id": "babel-plugin-jest-hoist@24.0.0-alpha.16", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f7d9bb4a01ab083fb29abf738d391e0cac80e8cd", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-24.0.0-alpha.16.tgz", "fileCount": 4, "integrity": "sha512-S2E0qgfeVUp+j9037LXYQPJyc4H94C1IcYVYeMEgyqxtCjHde3M9Bc4yPbqA93EL0eyNViQJ6YtCRPTAcxSwCw==", "signatures": [{"sig": "MEQCIF8ATIxiJENbh5L03VeRSRtM2GVH36uSVD5y0+bPRzIYAiBhumalLWNSH+fklmkuywRda8bAukG611aanXIienTVxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSxIcCRA9TVsSAnZWagAAxHgQAIuxa2ndincsi3gaMGGL\nVRKZnDlb1lQIAWo7yLRCvvKb6Nkdw7kd1VZd2aJ70K+cSsFxJIEmZ5bw4o+H\nka/DDdqah/0TsPt+vqqEGdtkxRHzk9aGbxJclYfpmL/Knk/M8H7QMgxawDkf\nI6QtymbW6dXzp10404aZq6NQCxxpw7FHqmj1ognct6N+uTNhtXgaR93HdlQc\ntey/zJsYNtXcNL5MU0/ojf9QMBKgBXE3+q++q5HN8ee68LsaUYyS/Ky+PxI4\nDPtTHQ/xfDfZG4MBn9ZX07d3YuGMICtrmMB4PmiNeauorf/qtvdSLh37s44W\nLWSXYQh7VVOrGbv7evAK8sNbUdlrWdgV4vTF8W4k8KxKXI3R2ZqSHPi0U5MF\ngmKorQ8uC+ESD9MEKbeXLNHJLZkONwdHRXdOQSC9y2zvMoJ8NTY/WhNW8Pq9\nsaA6LG4svum1sMPKhiYOd0uSREoVpmnLdY2ajklKySKqEWaqFctkijUt0ArC\nLK29Ih7mR2LYZJIKQlCQvjYLYVvmAVTSVUvERZMRVyScgye/ICm8zncBs7gj\n0Rq7rYXmoV/7sgJ2RQgN6PslFiP3MrohXtt1y+Tb2vwDIZmTNz3kLBFodxCh\nhEXu531zrmO2SZ7SPC5ouabEllgHby3N+gu6ab4WXJLuEqXO5IrhJU2XdJEB\nrUmI\r\n=yi/y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "634e5a54f46b2a62d1dc81a170562e6f4e55ad60", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_24.0.0-alpha.16_1548423707318_0.8791153038603023", "host": "s3://npm-registry-packages"}}, "24.0.0": {"name": "babel-plugin-jest-hoist", "version": "24.0.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@24.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3adf030b6fd67e4311479a54b24077bdfc226ec9", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-24.0.0.tgz", "fileCount": 4, "integrity": "sha512-ipefE7YWNyRNVaV/MonUb/I5nef53ZRFR74P9meMGmJxqt8s1BJmfhw11YeIMbcjXN4fxtWUaskZZe8yreXE1Q==", "signatures": [{"sig": "MEUCIFPj7M9kbZKSfN81ZlL/G5RP93wNbYWEDEkOC71ApZjmAiEAqjp7V/IUImKlwK375r4IStFIEuX1PnH1jvGZHz1+xfc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7190, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSyWKCRA9TVsSAnZWagAA6wIP/0kCYvMOChUoNgFWZplb\n6Y4kTfwjzPvVSFbPmFvTc8+kKrlREI+zweKh4FJ9MGsfJYoXUcPetKtNB5aM\n5gTTaIj8cuWaYchMNLDY5ouLROXojgtJy3jBBVjdKcPTrh3136Vh/x0oJSNN\njBGVmpauQoNne94yBECWOJ7HOy6IgoGHpuW2UKecfI4hsFxD2/DScNreviBo\nk/srCWquBnqo2ZEpj60SB749QClTwEGWETIChfr/HG7m42fJEOBlydB/DY+a\nEQspCnMFg9Nh0A89vnSCrOIqqQzmBejkMTeo4tfxFe+Smro0O/fglxUxqgV6\nS7cWfsrzwPKIzpv2nHltLLOEWbuIjzZlY2MYjI6HvUPxc2xGgj1GCY6Swl4p\n9HiSUPAlOO1ChMXNKZCMCVh4uzzCR9qSq9A/eXG4Y7f2XgcYdDox1YOnPIBT\n89DQUFlBSNvuz2qi/3gZqsIeAQ3VHuqmFhhhKt9k3YHZwI9rNWMbTXhmN4Ia\nHQoQsyJxBPFOJK5tSBSz+Ad8kmvAR/sbeWf3MuUkvpdcRS8dqua0O4AJjamP\nu1ccTYb8zWiSTEdzBQxSWvf7bBV0qWxhnCphIcSMSNXpVi3QrPvuiedAV+9j\n6Bx8X9Jv/dZhkmmH+ZOflXbOo/HuAsbTr8XCE76KghSQ/9MR9E78zze034Pr\nT/4j\r\n=KSJ/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "634e5a54f46b2a62d1dc81a170562e6f4e55ad60", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_24.0.0_1548428681723_0.1750062568080586", "host": "s3://npm-registry-packages"}}, "24.1.0": {"name": "babel-plugin-jest-hoist", "version": "24.1.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@24.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "dfecc491fb15e2668abbd690a697a8fd1411a7f8", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-24.1.0.tgz", "fileCount": 4, "integrity": "sha512-gljYrZz8w1b6fJzKcsfKsipSru2DU2DmQ39aB6nV3xQ0DDv3zpIzKGortA5gknrhNnPN8DweaEgrnZdmbGmhnw==", "signatures": [{"sig": "MEUCIG+0bKc6y+nkeUFQapXwVUq8b9FDLV5f9geLAxZ90ddzAiEA00D0pGnmGwhodsPSHJ22zJv60k+XkdCl8cFxbkii7v4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7209, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWaaFCRA9TVsSAnZWagAAGB8P/A/LCSpdmcU3hvVew+E8\n9bKoUJq7KwJJM+Xtx7Q1QQNlfkclQKbbKlngB0SujI7JsDgjE4QdT8UBEugP\nleGnqmbutMTvwsf4+XNVpTramzR22ZTfdazdHsnol1A7nxdISFJRne36PTrP\nhDQa6ewHR2yblBseqIieLT9ubsn3tgCUAEYlsS77wgTWvaRjlxHBZqtAx22w\nHFaY1P6Pl9f5C31wUS4MMUr6rRkSyNO/4Xp+bLgxmjS0QI/e06laybg0wYbK\nJyaWGAne52z/EQ459YjyG+JIg2o6UdincxynjYyJqjXM2lBiNn97JlAwRu6d\noXGjMfBc5j4QYGkXyaLIKpIoZC3y8Hauf28ilalY5EwuGiii+U7MRMjCWsBG\nyLoaedhfSHDYjFNXJwQZTMss87FQ5MrPZ114dxSbMR4H8sF8Yh9raOhgDnhs\nQbjc1yB18qZ/YWpJvrf4Ol0KdYPiMJoUlMc9nSTUgRvqqXZMzp7EaC+L4He1\nJC7+Jixi6VG8SVG0dlR3cDqM+m1/tM2Omix/5mmBlzFeLJtbgB3d3/v44aia\nz6W8/3zQXjZphAYf3rR1jbaTN6uXGPbDGGB6zL8Cc+Y1fAkIqkrUrf/xymDs\nu8MsRRJh1vrRgqVTfiGIzHq1OPrXr5xLu3VYhRolWOqt9pQUhCvKeDQOEE2U\nVTR4\r\n=bIm1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "b16789230fd45056a7f2fa199bae06c7a1780deb", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.11.3", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_24.1.0_1549379204632_0.8224633866435329", "host": "s3://npm-registry-packages"}}, "24.2.0": {"name": "babel-plugin-jest-hoist", "version": "24.2.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@24.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2e71559c7fb4e7eac58d521adf0cb50acea3f35d", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-24.2.0.tgz", "fileCount": 7, "integrity": "sha512-U63Kx0ZbB6TFjcmRRZvkQfkBlh8beJ1q8CsO+cl4uAlr7bLZM0isvQP369fUEZeJJr/1yqRplzHj14TAmQ1r0Q==", "signatures": [{"sig": "MEUCIDGBQkR0FvqRlNdxgCMb/pi1V4Ac77QnLoJa/MMaOuKmAiEAkKiCx+YcMJZKVSjnWWVmW5yWN0QkG4UU3Zbgpk2H0pc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8280, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcflwECRA9TVsSAnZWagAAnZkP/jskKhHyNj2IZ0d7j0CO\nWx+vOyO7F+9mT1CGLJ2+iYhuH1deIZ5gHmmZPy0pfoFpSCXD5KCPtUFFNAZD\nb5qbssl+rid+D+nmG84gWO4ck/VJHcEZz6WMxrBKwrfAaKA649oWnPhg0Mpx\ntT8e3r5psPaTvpwrLhii/tZeoT6A11dFObro1ZBFDtEZ586BeuleLErV7lcZ\nMWjQrVQ4YCD4yty5EQl8ih+ER80OIzTW4csn4oSI9fKeh3I//ViqeYGVfHFk\nBGZMJ9l9GQlTTH+Y8396mtmDNP80dgTE2C5PvqibLd3pk+WzBpmqaDZvr0XR\nzf6l+mz+dWgjUAXA9ilR+JvSMwfH/W36OGYGxFTofwxgsm6y4PrmGefqZKDh\nmB+Hm/aMQmdfKD/V2E6KWIHg+GstL5H/y0VG4GUWe/I8mV6IUmUK9q9EdkM9\nllFE4rpSF4wzf7dWgkLLFqG1XtBrchxuiNTRDUeZPEAnmId9fOIJXaSvtSEx\nJ7NqXzIw4cFi4VBFTHgsNAF7s+b88oAXXKB6e0QwmjBqNLm9rHxSecX0p6pZ\n7nlbsYv0GIGU/ESaSjsmO0IUP7Bc1Wep1yJM4owPCAU1xr3OTqmN2wW6cJty\nncKNwckxbYLm3jWsM1agCZkMO9co1aL+emErGcFp5lDTC973bAme8VdeAiAj\nkzx5\r\n=VSuq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "d23f1ef18567763ab0133372e376ae5026a23d4b", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "deprecated": "This version was accidentally published and might contain errors. Please use 24.1.0 or a newer release", "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.13.1/node@v8.10.0+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@types/babel__traverse": "^7.0.6"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/types": "^7.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_24.2.0_1551784963776_0.7399284019879333", "host": "s3://npm-registry-packages"}}, "24.2.0-alpha.0": {"name": "babel-plugin-jest-hoist", "version": "24.2.0-alpha.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@24.2.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ad6d929670bafa2acfa6316e83e792b0d5ecf926", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-24.2.0-alpha.0.tgz", "fileCount": 7, "integrity": "sha512-nnuXnwb/oYgx/boxpd4TSPtyBD3l+NbPRzeVBI29guOc1hKaS2pYDVa06Jdn3iCpYJwPedopmhT8bf7yseoXqA==", "signatures": [{"sig": "MEYCIQDht4yd5svTOK4xBb6ZeqqK4VGRqrMc3yA4s8gbybUE8QIhANR0rMzFk8ayqNbjfqwka2X7Zu5P3onNlzuPq5oBv7vR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfou+CRA9TVsSAnZWagAAKHoP/3IcNo3qhGHws7rElg8W\nHNJGMnd5YjBF7Bitcy9/8697tYopsR2QLkriN6njE7mlMHTiySBX8vdJ1rl/\np3lnlA5PmkFnAtEByrBN1UIXAY/xiwRuyvRJ3oqK6SUiVmAlOAP4pR7u5znt\nNe2wFbIgsN3EWV6wVipR6dxjC8BPCXY6lvbkL26zUnyyphCY3Lx/eBqv2TGG\n0P6s0LhOYq1sn2uf42i5OyJ/gtClB9CgWUDlTxv9oorsUWEooyDUp7VhKgXc\nR2k424bFxSQS37rYVVJzB8O+WkA6bOv5ZPZftVvNRTkXhr75tgACjmYzE/1g\nkIxt8o2F1sXYxdoSxLZFeiGvoLdE2WayKdZrW9Obt+/NOyYCQ5qjb1G1zBvO\nyDXFIrXzg4sFdUQtWNfhsEGxnN7YoaWZSeRArzh0qoB5z4tyRkM69Twwywir\nrXWqXE/JvQqU9/11wD6XZVpU2DLHdaT+HXuz6wYFJlr0YBp1WoTlBCGw0oHd\nOrb2Y/d/x0wduV5ARnhuyfnz7NieKwZAMvqqHdoOL7ULUy12N9B11VUDZPxW\ngVETJyIUhmJRBoou4VWTsFz/NqmxajIK2j2wafClCAMpj+/DXbQY9QRsFHUM\nvULv6+pm+D87aKYhD+Z+MxERW8Gi1mZoQRoD1Uv66Ehua70y0Kz2hZ0IUBDb\nACJa\r\n=aVbr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "800f2f803d01c8ae194d71b251e4965dd70e5bf2", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/types": "^7.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_24.2.0-alpha.0_1551797181985_0.33791453269585614", "host": "s3://npm-registry-packages"}}, "24.3.0": {"name": "babel-plugin-jest-hoist", "version": "24.3.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@24.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f2e82952946f6e40bb0a75d266a3790d854c8b5b", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-24.3.0.tgz", "fileCount": 7, "integrity": "sha512-nWh4N1mVH55Tzhx2isvUN5ebM5CDUvIpXPZYMRazQughie/EqGnbR+czzoQlhUmJG9pPJmYDRhvocotb2THl1w==", "signatures": [{"sig": "MEQCIDlmcnoGe+pDq+N7Dwl2tfw2B6q87b+ybNI6hERho2vnAiBBi9eGJGx6hWCKWUYOajALzQ4r39X4OJdPWaUVI5AAeA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8329, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgRWnCRA9TVsSAnZWagAAkDIP/07r2Q71p895a0Zs2CLq\no8RzOLuajfu6c0tf2GIn63Zrag4opPaR7ChrxcSVSqbqpdELIXhZNLKG52+f\nkJkg7xwK2f12V89nGVay/ggVjMPztSglBVFP7ioQSefhbfnC9OvTAaXw9AfH\nWQPUCkQs6vyxfFMR6Q/QHDBjSsRPtRTKYEhn3bj55IrPTDuKZFMplL5ZUYtZ\n94T5N9H7Lk7JXGpE3qugbL6q6ecrPTwFzQ0EgjKGjm7iC8BeagEM0fHKIaFY\nmHjlCDW4l9ejZ89QqkJOUc+n74Nhwt5YfjRwyrvpqMh7aKpdMTs9HvIqRXJ3\nIjBZFZKVnQEwIhWFKK+1/0tLgnv9mSmbfuDISDV6xy3U5Xp1i4+gXgG6zyeO\nKx3Hi/X0TWnRN2bPJmhIA8Gw/jYt7frcvoZxKUKGRYJWdkHUQuIGZ/LR0ZwE\nx3POR17eTefMcAXXRPwl8vOxcqRLDvt3z0nbbMdzER89v40InQiZqBU+UAh6\nUqbFwgU6q+TJGbYIrY8GeFJchDWsdhtWS1woGf/zFMA/3ikC9VEo0FBehZX/\nP6DTPyZ+1+e3YH7QxpQ4y0F8tNhMZRrvXvhCZ2nYALZm+x9GQkCtcKXdzReD\npYmq1yK0H6or1SNd1qGPjOCojZGzmqjWWS9fUzZymhPmpAqHeCtlwojqNjk0\njITD\r\n=hcnG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "3a7a4f3a3f5489ac8e07dcddf76bb949c482ec87", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/types": "^7.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_24.3.0_1551963558764_0.018004420166414903", "host": "s3://npm-registry-packages"}}, "24.6.0": {"name": "babel-plugin-jest-hoist", "version": "24.6.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@24.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f7f7f7ad150ee96d7a5e8e2c5da8319579e78019", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-24.6.0.tgz", "fileCount": 8, "integrity": "sha512-3pKNH6hMt9SbOv0F3WVmy5CWQ4uogS3k0GY5XLyQHJ9EGpAT9XWkFd2ZiXXtkwFHdAHa5j7w7kfxSP5lAIwu7w==", "signatures": [{"sig": "MEUCIQDGYX+mkMQwSMZMwQYGB9tBcEJQGOY+ojW3lts2zQufYwIgSrU63+N+l/N0tpYR/fNiMG5dMEA7prxlpPO3vbFxHfg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcopAJCRA9TVsSAnZWagAADYsP/2IEjQebWUsGTDcPSkQQ\nh0yVziG5vDEUCMdLr3FcMkDICSNHtVU1XCZoG/BMNspZl1BwS+500vZVD0SO\n4N6Tl8gvERtzArkLL9y+5QQf7DrNi0DBkBmZtZrwjQGSJBEb00cEIuEjW4ef\nYdzW1vAbl4zLtmPn4RDgmRVAq3hHFb6Fg7tGgvfSMW3O/0zLfi0BgpSaAh9H\nVgx+7uE2CvoGArSpnqefyt5gWg+1DWD7RHMv/xQaoWEQBYtOHkcnM1+VNxbn\nhQkLmySAEOoKbMbneyBJY29gMrGW2dSnvhxFt2xPXrMYXfHjPUpqlTaGkmNO\nNSzWloUh8Ja+uFwUzrZumWXwcd4EO6YI023iA/wtMtl/qrxoC+Bq2XVou43M\n/t/R6Yr9UVQr6nk5UjXv8XCdH2c4EUPZkHRpctqXREPb2xVDDVQ98KWuY+mS\nm7vmlWTlFQQooQqZkJHWd33nmaOzI/oKoobC+rdQ0goksbgpO5+mW3aaZpXs\n9Vogdtm7X3qMBMBbEHMNRmKxetz5N6V75L4Kp4o9q0g5GVNxZiJjIAjnlM3I\nRUVm/k032Gda4Trex1uRCEWfUniMJPTGnkDeawHL54v3EfVoDCVZKrRkn6zf\ne2ohPJr3OcfjI2tJ29h/55QBWSrrvepSR+t0Mw+EbDZqotXn548ltvvnA7Hh\nxyc6\r\n=SUD4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "04e6a66d2ba8b18bee080bb28547db74a255d2c7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/types": "^7.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_24.6.0_1554157576682_0.5009437005803175", "host": "s3://npm-registry-packages"}}, "24.9.0": {"name": "babel-plugin-jest-hoist", "version": "24.9.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@24.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4f837091eb407e01447c8843cbec546d0002d756", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-24.9.0.tgz", "fileCount": 6, "integrity": "sha512-2EMA2P8Vp7lG0RAzr4HXqtYwacfMErOuv1U3wrvxHX6rD1sV6xS3WXG3r8TRQ2r6w8OhvSdWt+z41hQNwNm3Xw==", "signatures": [{"sig": "MEUCIDeoPM912CXOkCoTyjx2LBC91vEGVGGPxzO+sTVJsbsaAiEAnzAgAfQXtgyx4n1N6UhEaDvrC0c7ABVfIL3uYDFDHGo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8273, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVkVjCRA9TVsSAnZWagAAcvwP/jLwWbjIUfmHRcSoCED/\nv8mkG+qKAJ3JyHz8QXK+O5SxY+lEAT5iZ9BRtrnKPz3pFD3IRKeCSH2p/dFt\n5MWuiW/wnyzolNFNdbcs3XRDgp7Ow+F4bGpnTmBY8iXXCORiGhzCZYEoJp/q\nFZZ2Vj8mRYk7L1WVw3NR0jfStfJuuXOmyGHhEAD7MVbO5BW9kCCGT2RFhq3R\nez9mxajKbLFtI1MGPZdoSSCFoVyUiDw8RKolk4M69Qm6/2/NtHsiw/1lS3Mj\nJBM6lSyfO6T5OCrsWcXwVcGxmuEezv6fw/tjlX60r+hfiefZXCBzHUOuj0Jo\nY4uzVbUwsjecK2SVAuobv5ngjxek4rJ543rEmwBDdbEnuuOiOU3M4A3iU/TE\nYcL+SeeePqwoQlr8cxh3Tme6iXCkONMYdurDe/acJG2Yksdv5ZvJWns1l/fb\nbJwZDWFjJHUx0injhTBy+GBkbwjIkAkIUXDGI8AFEIN9qay2NA2wYJF5hyW/\nUoJETO2ubNWyKdLAxKYUtQ9vpGVq8Wd3otWl5pGbtGlKjXxAtu2Y5yG/Wqts\nF7wGDyorCPWRHY6vQObDGWXBhUKUjF5QH1wkiyf6u4UZaSa3RHAxyum4TVCN\nk91iz1IRj+Aa2pVIpc5egb+pNafEcbcrKxqzXXAzaYywMvIYIomC+o9Pj8/V\nDyXl\r\n=y6TJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "9ad0f4bc6b8bdd94989804226c28c9960d9da7d1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.15.0/node@v11.12.0+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/types": "^7.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_24.9.0_1565934946560_0.8740652058215748", "host": "s3://npm-registry-packages"}}, "25.0.0": {"name": "babel-plugin-jest-hoist", "version": "25.0.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@25.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "735f3b1a123818250feb5f755e1cdd36f92b862f", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-25.0.0.tgz", "fileCount": 6, "integrity": "sha512-mWl52OS2AlVbv1Cg3WxdvQk3C88UU63Iv0+jBkcpTuyU49yKpG0hVBX0i4PkCWafCefj6PfWkpzF9ztRU+4nyA==", "signatures": [{"sig": "MEUCIBBCVslcqjshrV+VnKJncIV28O9znD0tQCVrMZTDH98TAiEAmR65+zQFW/6VfNUfwc7a0N6x8WOyknGJa51hElNgCu0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXgrACRA9TVsSAnZWagAAVlYP/jg4jDXlEgClZXF06/WD\n61CAhm+Cu783Nawssx3/aqPHY3FkYUaW7NQYJp2otXd+TlPe2lvpp6G53bL4\nVmdDYg/QmLiVZ9BnuFliLxCUFHByDkzZA2mq60J+tBkOvvXpmSAkm99BmS4E\niKRepb+j5aezvEeUwzG2OAltwnFwg2uOu0xLJ76ZZEO8K6pSSO06prea3SyT\nr9cf8DGLctc6P8/GOjGLqKU1npc+xQ8bvnYIlzdRuLDW6trVTy6n3ZvO09GE\nTJqf1pzKq+c8GgyCq5Bxswjs8qL0U4X6SehShB3U/+RAcJgSefVgYSluJsaA\nTuUpTUwnTUzAK4HpA+EoTBWaH+L1YlmsGq/cZzhZe6SwUdx1YVyUulhZmft6\nb+vfzP5BQ3KK8BFknjDZ8dWv29VoN4EfpevTPhYdRO5yHC79TLvJFYuguHkE\nqkBMqcT0m5uz2W/dySAjaxOquF9TnFhuNa8PftW47ps2owzeiigmWkHZDOWk\niGksYJMs0i9ouZHnv5U0yv6+3cIUkWeY2hTuK6WjxPYLE5qdWf+d4PjjfNDF\nV3T9/kLKzpsvoOzR9hGGnMHKLIK7Jn7dAca6VuYC4l4f/VUF4SIBGrSLauuT\ndI2EMFt6S13Png+L9v0EfNFJNg+RFAcR8/KPSo4fpm8mr/RNn/xlVG/fix0X\nhdYw\r\n=HZPh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "ff9269be05fd8316e95232198fce3463bf2f270e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.16.4/node@v11.12.0+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/types": "^7.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_25.0.0_1566444224177_0.5152363284754315", "host": "s3://npm-registry-packages"}}, "25.1.0": {"name": "babel-plugin-jest-hoist", "version": "25.1.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@25.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fb62d7b3b53eb36c97d1bc7fec2072f9bd115981", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-25.1.0.tgz", "fileCount": 6, "integrity": "sha512-oIsopO41vW4YFZ9yNYoLQATnnN46lp+MZ6H4VvPKFkcc2/fkl3CfE/NZZSmnEIEsJRmJAgkVEK0R7Zbl50CpTw==", "signatures": [{"sig": "MEYCIQCHwd2URtQnNMpeXJWT569l7HWlDpisDHiSmgCKjWPa8QIhAOmtk7ijJ11GUgv6E6L62UH9u0JnUZoL62tviOkpsIl+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7890, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ56ACRA9TVsSAnZWagAAv5cQAIipGnmsd4Nx4NOOOAX6\nDVInYNdAIb4PLr39Qu6Ao6a+NHeO/jwgorswZc3AmYaf9xrwCA/1H5Y+zxRa\nVuli0UidmKvRwZUItdmE0h7GZEYabO9ZMHYJV4ucDfS1tcybVzjKDI4Co2ll\n3+k3ojCgV0C8aa0IEGOIDxeiHebWriXqvCq45iJqNeMoW5dNCeITHrvvr0yJ\nus7xNfGdNimYDGW5PNVn9CB0ecLdQhXY+t1TEgTDGOedh9W0qRl7DRzfo1A0\nuXk2fR10chI50tD1S6/9S5vWXMUBgVc7gRipdtyQECpY8ID0aQrbtEglzQ7F\nWbQ6dudk+Zv30oP2NaXLWLZ2Rl32SeErbkqxtlt1CnLujr3eVubvrVXltpJB\n6IbphbJhtKNjE+NmjpHxT5mG8T48CqLNDWiEG/W188JsTSZY6u38uh4ubtS8\nfGB+L6UcVMrQJsjWDQPXVziyswf4h8UvufcdxQFR5AVl8yoz8bI21Mv4uxpe\nWbrNYGahZ8cfwCVtHeD+Abx+d5Yd5jMTKAyoRx5JgqVjnamsylABDXPavvdf\n7WGoTsH16Uuun7EnPQJ4uJT0BSrcZp0peAXi/1gzWSTNWVg9scJbYEntl6hT\nsbTY+YzU5dfjhHOb8tXnYZK4eL70rnYa7U72LMYPziIsR7z4M8X6hyOCvAlY\nDste\r\n=Ymds\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "170eee11d03b0ed5c60077982fdbc3bafd403638", "_npmUser": {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.20.2/node@v10.16.0+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/types": "^7.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_25.1.0_1579654784430_0.6530832371996982", "host": "s3://npm-registry-packages"}}, "25.2.0-alpha.86": {"name": "babel-plugin-jest-hoist", "version": "25.2.0-alpha.86", "license": "MIT", "_id": "babel-plugin-jest-hoist@25.2.0-alpha.86", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7ad8c975b2becb81c4c2ce1c4ddf3b62bf436bee", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-25.2.0-alpha.86.tgz", "fileCount": 6, "integrity": "sha512-kKAYg3YVZvTqtep3VK3c6chrxi9hHZVogBiOGLRYTs+mCrnrVlIa9xnyPXir/jPD79m7LsYWAl0fYBS+QmB+NA==", "signatures": [{"sig": "MEUCIQCrSXow8Y+4blv7K9M+XbBiZZrtVMiKAB7DedLJFlImvgIgWPrSwY72VG+nT3BwR2AwNmG2MzXJGYvwT5OBv+XNvho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7843, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5HhCRA9TVsSAnZWagAASUYP/2C1BjKFG/lVevkR6HnS\nYTXPU/eIqdk/sSSIPdL2cUt+kofhO9cRgn4Ze1/lallGH0R3eEwD2fx9IacC\n58eANahOox4CqRr6IgcOQFppLdvi/CzHxQJXgM/jwnBGbY7DBMDVrxxx2vCv\nOgRiqXgAUbTQrJ9ZYRnUOzokeQpX1CHTO2I9jgqJJPNuS4PFl1q7xKTUJCDA\n2TDnZI77ResM4ubr+dwUsla+jpKucDrIrEPDaT/9238aWBeNemgWZdkqxR0s\nomJ/M1F4Q1yho84LbIPFKBQ13So8mj+Jg6BP19qDBw19hiDBekVi5Iy+UzCy\nqLl3qBvHl8SlEDqVZWeEvCijwAJdnfxLTJ+ZFTgHOHKj4uXzeFg+rk89X/Pm\nbQ9VWvlA3senGd+vhCYtMbB5/lgXEO2Kn0LsMODr97ToI5BBdBuCAC06Qyb1\nzjbkXihX6hnzNnExRIuby2J/xDKS+eXtinyyg/n3tsS6/1LQW9AHVHM5J9t8\n/b+iuHRA397cH2bTC1Jto/N3z0XBLJq3kA3oRmPiJyLN+cwc5pUFGQXTuTLn\n8gqT5qWPd2eI3Ngv1ZTyLp351uNQZyc2UtEy1ozB2V2+zwWEQ2QGwcZbIeiX\nzQ2dTOsXG0MipPhzSkuH9Y1LEZ38s18x1xPrcGLUfkauhdHYY75dHTSDDrpS\n6Nr/\r\n=sqaF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "cd98198c9397d8b69c55155d7b224d62ef117a90", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*", "@babel/types": "^7.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_25.2.0-alpha.86_1585156577275_0.42037014467661593", "host": "s3://npm-registry-packages"}}, "25.2.0": {"name": "babel-plugin-jest-hoist", "version": "25.2.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@25.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "08785be7f40bda12e54a09cc89a44c5560a96f61", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-25.2.0.tgz", "fileCount": 6, "integrity": "sha512-3JlSf80rRq8q8PTrLQ/6Tt1H6w+xCUJ6jiJdHoRzMXGy7ppb9fMBJLzq1iL2K5FIr3wzga6q9E9uRFB7E5aNLQ==", "signatures": [{"sig": "MEUCIHcMQFA3I9AS3TOgg4u/Rk/NtRYGG0l6nuQeBvi8BhEsAiEAozH6vd0G66shYeji9S/8HyQsOcIlgofNbM2+d9ggzkY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5ulCRA9TVsSAnZWagAAco0P/1Z16tRBrGIKNBIfyf9i\nHC/0fLVbGkQtAj2Xn9xme5np8ZApMo5Yje7A5+ByKXxmrp+gdxlmCuME/PAa\noUXZJNPecgoq44iVf5G+50X1Fvi5AqeEFCXWJc9wkHp0WXu/diRKtHzI7HIQ\nibGQJ/8ocabdfGBIqFQU1hUKdmpKdBk6f8pnnIZgf2ek8iTs9pCZhdooyu2z\ngRkKjWGH5M+auEeVnROuTgmzwIir6HCEyJce2FzI34YixLgaah8qSWI936FJ\n4jzt+aOL/CS5XjcX9xQYYzpVs/lmHFKXjAzPLRh6v7c3YrbVANBI3f5UXSgQ\nwurJoJ1Oy3QXobi0x/JIbIPXPXSJzzKuVHMMlIGu1j3XT+IMe4GteyRDNHWJ\nIcFVrs5WZKpokzlOi9WKzuoKQeVNJ62PwjpvHVVXtCriOb9EXAPKnq9leAPC\ndYtJhy8+DaTfrZmntUQjfWrX/CGCkBLxK5X5go8Xw6YvKopy/RoT/33fnR7U\nAqLMIiE2/DwsUBG/v66lVPgxziuDPMs9bvhhAD+AFxJg0oHk4hE6i0prLUP3\nucJ2IS1fUTYFEuafzu2HdaEnl+Ddz7S6BxnwIINaIvrERbWMo2+xreWaNhXZ\nhGZBZOFgfj0CRrwboXU84wlb++M0fBdRhoaAgUhcPzlBRepEnFEJKI/e4E1I\nDCwF\r\n=38dq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "9f0339c1c762e39f869f7df63e88470287728b93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@babel/types": "^7.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_25.2.0_1585159077459_0.10034233938886317", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.1": {"name": "babel-plugin-jest-hoist", "version": "25.2.1-alpha.1", "license": "MIT", "_id": "babel-plugin-jest-hoist@25.2.1-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fda9ee6a90f2ba7512914f44bb385b663acf28e5", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-25.2.1-alpha.1.tgz", "fileCount": 7, "integrity": "sha512-AoVjVVzgMqLWe2Hsj3Pk4rRwSv5mjGdlLdFX1MCxkOlOzTX8jzDbJnQBCgD5sdqu6NK7zM7jCkTIp0/LUXLQ6g==", "signatures": [{"sig": "MEUCIAKIET3VNZfbxGgSrW5r6rpBX0f/xg2en1XZ+TMVs7oRAiEA35QO9R9kPFz2xp9xmTU1Q+2KGlHxqqWB1zPlpV8RaWY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefF+mCRA9TVsSAnZWagAAC1wQAJ3Ot0ATR8XE6aY/EZyn\nddhLUzMgBA+9brJF2txI65+91BikdG1HkoWfsM82zsu8f3ownaN5ermyKOwn\nUepjmvOGYAi+pdb2HyMY11mT8Q4U/lUb9LBvbXGPylkBryXnT9n2L7TYysHD\nkK7D64HwkItb+CNuL+19UaIg16/kndcMh33dumNVi+vE7OrfC05pLOVS8M70\nRMBAS6QP0EehxsJBqNZmzSsPqabL+C6poiOdwibNObBdDLSX9NQr0RMUCyK+\ncEqYyhFdP2j3Ds99FO+SBrvU05wvDfdQlKK5x2QgQBXatZ/yDaiwO9+e79Eh\nOSTfzWBrhfKr6YzX7Fp8WVmgiGVH/NqAdtiU/8Wx8gjPHvOsgKmjt0d93j8g\nu/G2t7wB/3JLf6HDALDW8z/aj9zMOIUnQdJkSGrrq88X0uNOpru8eaCxuzFP\neChSjKtdocK2O/mWdSK87CjGa31FGdINSaWXocp6OLvnwVmC9oWfuPakZZZ8\nTz8JeQnahQQVO4ch4cIjXJR3AqKapTnYyVuY9v6PbSYlAbwLyUhIkoPtFTh6\n6fzni2wZ1pfXuo1vbBbzskoovBbh/NrSna2z+jSbeq54S8gHFnGGp/SmEAOt\nzl8hroFQmteSKHjyW0jbmdvGWSxZJ0Pl+rxL6jE7o8hXslFq+mu8u75OHjaN\nrJu7\r\n=h02x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5cc2ccdacb1b2433581222252e43cb5a1f6861a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"*": ["ts3.4/*"]}}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*", "@babel/types": "^7.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_25.2.1-alpha.1_1585209253486_0.23834837605717074", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.2": {"name": "babel-plugin-jest-hoist", "version": "25.2.1-alpha.2", "license": "MIT", "_id": "babel-plugin-jest-hoist@25.2.1-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ec149f3c9ac7aaef20e0de25744055f9c2a3c83c", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-25.2.1-alpha.2.tgz", "fileCount": 9, "integrity": "sha512-ySmJbxbpEg+INAXxOHwE6NGTESFO0anv8aBj9Sa0KioiazQR2I0O8+KSrcW0rIs6K8S0XtuWJdhK62BmsLnvrQ==", "signatures": [{"sig": "MEYCIQDa5+Ct5dJpdReDuvjxo+Rf82sMmqLEjE651PJMtVhzSgIhAJ7k3gWir0aTN2Rt1IF7Ox/7GHSHa6QYaRL//CLcD18K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefGNuCRA9TVsSAnZWagAA4cMQAJWgdE3KRHk2jxWE/cvz\n2uD1TOfjyQ3MxH/ebVI/6lkmk8e3BJfVBuq3ouroDz8/hW4my4PiXksMr4/o\niNUnU094+r5QeLTBdi0/e2r3QqmJ1DzCWNiMbrmekFW8CP8+ddbCBDCoVfnT\nWfliduhHbUCeF6cDRppW0i5phqD4HYuDN4wPBuDIMZxl+EgxagWTeSOT6VUu\nAivoQyx5ZjRWyoiZ0jTmRDqU5ve1vMhoykSSREGJyVhWLD5rDy1puhohxmXI\nPcDCnRNVnN6VQf8tZ7JpHvo3hJC8bGtkhBbbuO7CRLTljYd3gi3ZJye2BuzB\nUXIxAG5m8/yc0pfipdXZrfT6RoRKaeOD+jg0j4kURC2XJloQtAWauiCuQeYw\ndK5A13D/sNvjuVDXgiQ+SBkATZejrgdSc13FbQ1JDDrIWTkRKK74whEQWml+\nErK1QVLFlOFT8RZZytWr7/J3EaBAAXOWSf1b/HV33yFienaGMVRJ2M8Rp2FP\nPdN1NGex4UD3w8H2CkPv6GnoCSOS7OWvwJus4Ooh2wF8M1K7Mgd3LflUs7Q1\npmx48Sg/Dal2qq8gky2h2/DoyAZGswl0JcNc18jOIGAH/ljR3IIAMXcJ4I1Z\nRh4r1RaHxJisomKgY9k1wLFsqwCwJyN+Na7A6mUUdvYPwHXWu5voD40h3Ld5\nh86n\r\n=yboC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "79b7ab67c63d3708f9689e25fbc0e8b0094bd019", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*", "@babel/types": "^7.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_25.2.1-alpha.2_1585210221635_0.4236541861533487", "host": "s3://npm-registry-packages"}}, "25.2.1": {"name": "babel-plugin-jest-hoist", "version": "25.2.1", "license": "MIT", "_id": "babel-plugin-jest-hoist@25.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d0003a1f3d5caa281e1107fe03bbf16b799f9955", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-25.2.1.tgz", "fileCount": 7, "integrity": "sha512-HysbCQfJhxLlyxDbKcB2ucGYV0LjqK4h6dBoI3RtFuOxTiTWK6XGZMsHb0tGh8iJdV4hC6Z2GCHzVvDeh9i0lQ==", "signatures": [{"sig": "MEUCIDY2EkL4gFhxc2XioLQgJtpEYlhTpHcYxIeTN27z40jGAiEA1UFIKllvhqFtz8d7qQP4Xo5ZdL88LyxdCaImjE9AVQw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8312, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefG9QCRA9TVsSAnZWagAAycQP/3Im0utJdo5DwKP/rIsL\nQNFisRjnVQvKhRR3xJg2rFByCgtbGodijcsfxSQ9MCZzP6bER2FsrzjZn0Nz\nlaGtj8EMyClAoG7Ogo85MIM5XaSaR+FzfDDkuPQWxxcNanVQfgEsgjpBw9Pm\nhubcfKq3gtwGgB++/zPlXVVtDDdziYamLb31ODJifiT7h8hye/Hqth3+8uth\nVLHjam+HjjzSMl9TEPpuuu3vJlH2f4BtnA2QmSRC1v6GoA32R1phDG8ramNF\nL7CU/ZtMRl8xsxgiEmkr/Msl2cZXJf+TwXwACqwSoIscLlQQxRenXNwPOcIU\n2TLRQwNG6tqC/v8oHZFtTobs15lNhlELzQiGnHXOKpyHghjyxauA9rsR8+i3\naLn9+PnMkPBfFK4SetKzk6NqhaMC8OprWWfDHU/cmjIRtwLOGug9vIlapAS8\nbWDyVYDTOhjFI0vi2eTEW8A8NyOGLs/1KC5Pp9jWB0fFRjCVEJqZChvVXxOH\nEinP94DCT3FiDj9TKFcyPKkv50a/Tm/jyFwy2tPRNjCcOKtrt7rTE/cD512v\no+qFyAeM1ujgTqjjr4bmHbmPjSM5M/mQW7k8SI4ARxZ+1MNVtGBYH7IIxwW0\n6FGHYSDES+jp/jsbBuEXZRx8lfFoNyaHhkJg9as3+yCI1lWuarkfag0lHI1I\n61kO\r\n=nHju\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "a679390828b6c30aeaa547d8c4dc9aed6531e357", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@babel/types": "^7.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_25.2.1_1585213264088_0.8743728506903263", "host": "s3://npm-registry-packages"}}, "25.2.6": {"name": "babel-plugin-jest-hoist", "version": "25.2.6", "license": "MIT", "_id": "babel-plugin-jest-hoist@25.2.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2af07632b8ac7aad7d414c1e58425d5fc8e84909", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-25.2.6.tgz", "fileCount": 7, "integrity": "sha512-qE2xjMathybYxjiGFJg0mLFrz0qNp83aNZycWDY/SuHiZNq+vQfRQtuINqyXyue1ELd8Rd+1OhFSLjms8msMbw==", "signatures": [{"sig": "MEUCIQCZQYcFpgxybsCxkRZQaWoTRPk/il+lqAR9WF5Pdab8cwIgTVbtu+/yT1qYZBw7Z/H32zQepdSOE3GcRGfrxOmTMBc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8312, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehb50CRA9TVsSAnZWagAA16wP/28d/UwkOHaTnd5cuNhT\n9ckvnzumHL7FZ41MuI/sd23BohqJXq/LTwEc/uvSXHHK64xBPsNQQyP4Euwb\nP4FuYzok8U+Me8L641aCgWtgm5R7cGQ4e6truWa2m4v71fFCloTB2ru5yE3E\nrGD4bBkwn5saYZSaM5byhTiFSji5RHV3zqRFG5HEhUyrjcdaBkqQa+xtW9jE\n0Q8JxxX3qm0RkYHsmrz513LyNWE87Of8/GsHHoa4NGFEY0guxnmuwSF/blmV\n9TLkoibbhp6N4/iYqEa64tH7KXGj4mW7/lP2l+FlXG6x8AzICn/GwxEWmrfk\nFnPfzj3XQ6Jif3R5dGRfeJAdt1dCXZIhDdYs7/tFA2izfX+YUP2QIEFIHyEm\n3CycfpkJhjQdniQVpvgoH0dD3iC+vNsJtFTHH70uWxife4kHnGCPsHMQYs3P\nAJPT6kVcjp/HoaxMPLNJlmKJ5R0+4T5fU6lRxU4HyMyGbUy+2w08Qx20Ut4g\nTG9AS0Cg56SGnoubU0pOAoOt4Iio2TWE2zDS/hbHOcp5gLKmF1y2f5Cmzxk7\nUZBTDgvGYVhakfB7SEk7q8H28svWzFaw2T6RmmxCefBEDFtU04+IYbkIfw0s\nS7wOl0KRA4vW2yaYkccd39GQSg16zotnZyxpRh83U/cMfRVLMOq2sQbwvhmf\nIDLl\r\n=ZArV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "43207b743df164e9e58bd483dd9167b9084da18b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@babel/types": "^7.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_25.2.6_1585823348060_0.41315472224031136", "host": "s3://npm-registry-packages"}}, "25.4.0": {"name": "babel-plugin-jest-hoist", "version": "25.4.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@25.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0c122c1b93fb76f52d2465be2e8069e798e9d442", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-25.4.0.tgz", "fileCount": 6, "integrity": "sha512-M3a10JCtTyKevb0MjuH6tU+cP/NVQZ82QPADqI1RQYY1OphztsCeIeQmTsHmF/NS6m0E51Zl4QNsI3odXSQF5w==", "signatures": [{"sig": "MEUCIQDhXG5YdsnIFTqL/Bus3haiSg06C29jYc5FCXz6klQNiwIgbqiSS7Li9b3xn2+AFmkH8vhTp8+aH0vHGuurbm5zcyQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenMebCRA9TVsSAnZWagAAge4P/RtiqWn2sWkwtHTNX0xq\nlw+MTiTs6reFjFqVrV+Y72e1lXifcrIs0MSlzdplBYFbrCVxesc9FMDcmWOB\nhpH4eFENSSBGkmVDozMq9833jJT0klHbxOP8S+X7fe7qqtAevgWzCFHzKW83\nPb8BkMxi/R0UDlhYevuIf56na2m5bIu+C0jeX3W2AJrjHBKWcF0LQTuwOEfb\nCFSZZN6pFPw7AHZZZjdGp7I4BMLxoyMhflOyA7gfqPG6/Xg+otrMDfIeoV6R\nwancH6iujXDHhnNBCzRFLHhPcteCaQuvvMzSB7Nh2W5Iy9wka7QujTPLlgN1\n+Io/ymzjejruMKT5QWvqNd8sBS7aZU24/W4olxUm5vwrrVZIewC41tW8a2Bc\n82CQRD8oUWMRtEGKA/pWJLxJTTkIv8fqV1q+BdIX/VNt3b1AKpV0+bUkZ7kv\nKUqvQWKM2SLEsjwxCDI9PcDxckUPw4VaAwUsuyQLkoLhZ5CIs1j5FLhzbubh\nD2+O7p8ZKwaQEpZW3l8zNkz026lwecJiaZU7BBKZ/mVItndSsZeIsBPu88X8\nZprj2QWxdWi+Xw5Mc34dgOLl7YrCJuWJg2nULEu+HuEwRkXkHq5n4NIW2pX0\nepuVLxkB9ZNs7qgQmhgj+o1JXz8MAbtBUkQJCmp1RoJBJDKk4vpXKx97as+v\nfjyx\r\n=8NX8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5b129d714cadb818be28afbe313cbeae8fbb1dde", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@babel/types": "^7.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_25.4.0_1587333019440_0.1917652319744223", "host": "s3://npm-registry-packages"}}, "25.5.0": {"name": "babel-plugin-jest-hoist", "version": "25.5.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@25.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "129c80ba5c7fc75baf3a45b93e2e372d57ca2677", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-25.5.0.tgz", "fileCount": 6, "integrity": "sha512-u+/W+WAjMlvoocYGTwthAiQSxDcJAyHpQ6oWlHdFZaaN+Rlk8Q7iiwDPg2lN/FyJtAYnKjFxbn7xus4HCFkg5g==", "signatures": [{"sig": "MEUCIAE7XHsTHgX5IvdnKYSRnBLb5HSEmSY8aLwxjmYle3pqAiEArnwBBiR6KdqT4cIjwYBTW7McOQmdToUUs2+47qc609c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11791, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqIfICRA9TVsSAnZWagAAPugP/1TBpkzLZdrSJzmH1wSG\nUvuxzn+33nadDk3rQlXVNw5F/Czr+u6jzcDIh0SIMjDWdoO2Xzt99rBb0nYK\n8I/86NhPGjYC8wMhNYAqzDrDPuMxwreZ+YOxDaFrSZr8daHuPUlDSde4w1BJ\n/VwSY9Pr8qRXkBlj6fs3B42gCCZp7TEgrzjsOnAcl8PKJ1SVDgVHQ5cHH+zy\nhwNZWIcYDKjQ3/ZtrG92P+CrxOdfq8jeEIh4b3LoKCHOSIbdodIW4Vum7EAG\nATjhgtXtqJep2cBv7J+TYM0pFa0j9gPbP/ajnsbID5ePSMns4uiGbLklTZVY\n35hgJNq9Q+Ji8rFwU5eemsqUtftcxH0rblV4wUGZU3/DbFXw+SD1sx+NXiuZ\nRFVIcRSFvQFSHOfq1YOKPDeeXIz/7akLUBpmCOcSC05xIXH02MbBdJ21waE5\nXDQCL2Lu+ykh7LO8K5pjAY24yosXbYC7Mx0hgncJruzBdtnV84MB8G+1FtLM\nQWJuDTEwpGHiHC/+5wzS3sFgZ5wVOrIHb/u4BUkXmVYbyXxbMIhGL3pvf8+V\nif21zbteV+a4OY3TlAd56RWovFw9tTiqbLOFZK2QUyxs4gUlRdR+iKaNGK2e\nxlqXFTtGdARYfxe/RasdW0ilOyu8dmr8swDE7HYsraUSJ1ikted4pHRqXRC4\ntvhE\r\n=uKmi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "ddd73d18adfb982b9b0d94bad7d41c9f78567ca7", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_25.5.0_1588103112414_0.6089146416571176", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.0": {"name": "babel-plugin-jest-hoist", "version": "26.0.0-alpha.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@26.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "143cf5bddd5e142edca8f38d3fd75097871bac22", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-26.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-491RxYGQ69cN04EzMp8ESspMAm8HICUW186nxll2ijSu/HgjbfE2CvVlYiamclxt24RG1bhWVCsuDJAFBtwLJA==", "signatures": [{"sig": "MEUCIQCJaTaq+xRv91gkxsWXi0g0GG5AVMCKh5c0w8c3V9qhRgIgbfZjYj0XUo4LnKmuuFF1ocWy8XBqDRDXTmJjHO6EsVY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerWPECRA9TVsSAnZWagAAqn0P/3XSt3g4wyDILhaKaQ/y\nQnSH1pnq82fRgDW196UuGR+XvTxJ8MVeAoIVMRwoKSjXnWzao0V5mGQGbyWo\nAzew6LWIHfxkkxvo+qYe9uJOzP7t82eziC6+xRpAHU+llalz728W5MiDWI/w\n4mhSXDRCzbQS3SbhcNQum84ka1VK3BqXUu3Wj9ya7AR8ymii3LhGQQZ71I+n\nPFDYhUf9gnmHhcV5UFEPVjU8qlCK96JI6VFfvP1lcJvTMhEWB87jYCMzm+Bl\ntqvOx5CeVmUp2FMJ3U+4EFUjTrOocMQHAlKm/BYF7YFAr/Q3FUcHp6oAgDVk\nUXHcsS8MGGJt3gChf5L57OA1T8ZjqRluZ7sMkArRkvUpEIBDGreBjpaTW47m\n0WQjbDzd/B7sRvEikDipz1h/j/eIA+kXBXxtzqb12OBHJQLL+tWfj431hWZ9\n4Cvl9pzTchjCTZ3kpcK54qfpEZRK7hRPSdajfzhQ2I3g3PNzkI0eFj6+KiSL\nHlB2yV8wfhAnPPXbb0neP73YKSfp7dhAu2g1poKhyYKCpbEfRF8eovRe2X+w\n8x4G4D8gFq6g8yY7Kn7rvqmUedE41JNQgSPTt9v6A1Fwf4yXb+t6Jpwr54Dh\nlSMfGiQi59D258stFu8DXd9TFWZRY6VX1OKEVnz+3oLRB1tNkYE0+BiCQN/I\nk7uh\r\n=aSvu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "ba962e7e9669a4a2f723c2536c97462c8ddfff2d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_26.0.0-alpha.0_1588421571883_0.7486670556215065", "host": "s3://npm-registry-packages"}}, "26.0.0": {"name": "babel-plugin-jest-hoist", "version": "26.0.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@26.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fd1d35f95cf8849fc65cb01b5e58aedd710b34a8", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-26.0.0.tgz", "fileCount": 5, "integrity": "sha512-+AuoehOrjt9irZL7DOt2+4ZaTM6dlu1s5TTS46JBa0/qem4dy7VNW3tMb96qeEqcIh20LD73TVNtmVEeymTG7w==", "signatures": [{"sig": "MEUCIHVH3b1bTe5GJpzuXktSkFN+qLCdvGlneDCH2iVqRpb8AiEAmzDclLn3TmmH5O598hAW/SlZrJyzus6YcdVy5Mlg+Cw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11208, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesFZ5CRA9TVsSAnZWagAAKHEP/idupHyHNxpmULz5wCBI\nNCDcOd7IMRyQMVLQVRiZtcUJR3VpMJ51yY3KzdxJkwB6awkBlxCRscYwQbVU\nb+AATn/3MFUSQdhGqFN72egeItLOpa3Nn920zXzY0eOUu+w0KKKB87g8RrPg\nOW+tjdQBpDGQkzL2uO8CzVAlGj9axQrfmJJyXyOe0aStf3nHmHTIiXVGFI6q\nga4jj1AYGtk8FMHpDb/TDUrL6h49J3L7LSMxW0twybmR7XO/o83/lWFjnycT\n5RVQMVD2JMVGHvLEwf3NXDD2lCglPopAOJYc8muYbVBjwbZFdRsYWRuD+h60\nDXEfe0FegQ30skRy9yHvIW44plMvpEdiM43VP1pPbPGDz7/9r4oD1lNI3pzj\nNmxPZEFVxsf6Kea/sWYg0FvJnFpMm3dlLF9ObFwDWRSwTX+MhzhJFs49hrUO\nn/4WnDbBvVoXrowea0DuZ/6zTPhO7AQ342onIbS4UBVf8yCEC4MnV5TKZoPo\nz9iUnyu0lYJFXyPjQYoZX/Z6jCpH4sqKKmKCaYcL5s66x0MLHF47zTv58ctq\nTNlPF58iVjqlJFm0QGMUaW6X3lRXyTZCYagXSSAX9XCE6hIBR3wTUiML8UMu\ncnLPpzcc4aHPTYhO5xm9ItE7G9derGxP1WrfmAyFTGjCx+cmwszHgxdSRmmp\nPTCw\r\n=KiKj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "343532a21f640ac2709c4076eef57e52279542e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_26.0.0_1588614776813_0.5710276948137896", "host": "s3://npm-registry-packages"}}, "26.1.0": {"name": "babel-plugin-jest-hoist", "version": "26.1.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@26.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c6a774da08247a28285620a64dfadbd05dd5233a", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-26.1.0.tgz", "fileCount": 5, "integrity": "sha512-qhqLVkkSlqmC83bdMhM8WW4Z9tB+JkjqAqlbbohS9sJLT5Ha2vfzuKqg5yenXrAjOPG2YC0WiXdH3a9PvB+YYw==", "signatures": [{"sig": "MEUCIHqAkmHc2uvVj17uirKK+WBhJVIk2QBnJ5zyugU4kxwSAiEA0ARUsOYxRH8oYXsU0tdqvHkCJ+Cs5kLDWEyKrj5d0sg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11278, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8hx3CRA9TVsSAnZWagAA4KEQAJ+JCx/dVDcY9YF2Ro4H\nsuyqCnHiDfSvwy6/SxW8zofAPFvt0HhGu8mP5PJAWbjJ3/0G9DqELvhjbVvb\nFKKMYTCavmV7Eeg6OOdM7W9cY5qwWkdEx2amw3Teb+M1X84tc+ZUe94kBNBG\n4GoWYqLwTOS4bKh4fvKk4Ka7fij6Ys+PEBzvWlYb8FtldBHgxlPAr10QGHCa\nMT+h/aoCkY4dD0Pijx4NoqtOzjk9Yncc+fob8spQpjxxkf7Aks2clvqIwc8x\nqx9TKbLUKH/4rSyt1l7DvJ+k2SDELUEQFQxdRjnmN2gWhk9+fsAoL02rem2m\nBJlXV+BfNrp1GlJz9b7MWO/0JHooAs4nhc2ntJdIZP51UHUrfbcplgxH9Jct\n86PQm4a2+F1GfLqNMeSr/oVSE3Y8hccCe1+ZHrQvX7AcRpkE0Lte8AmwymwV\nTEs2GjXnzoRTndh1vVth1U3FlW/j35jpDaV57kh6jvC50VrnCLCEeOCJXFHc\n3WxQF+Cr2kD07XqfZKzzVRKzMllpbu+E7B6qoRMqz7cMrL7K8Gf3eKBf4IkT\n4h94sBgjrbAKsi6n13dfjOWqRPLklkJnie3x4H5NpZd9IXE4pImWkS900b/2\n3lIcpCHaWy8Z7TLieiiLR7QQbmxg6+bcFOVEcN/xJKPWyr41W5iEWQJ4gwWh\nDxXt\r\n=xVtk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "817d8b6aca845dd4fcfd7f8316293e69f3a116c5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.0.0", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_26.1.0_1592925303035_0.19724517410018283", "host": "s3://npm-registry-packages"}}, "26.2.0": {"name": "babel-plugin-jest-hoist", "version": "26.2.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@26.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bdd0011df0d3d513e5e95f76bd53b51147aca2dd", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-26.2.0.tgz", "fileCount": 5, "integrity": "sha512-B/hVMRv8Nh1sQ1a3EY8I0n4Y1Wty3NrR5ebOyVT302op+DOAau+xNEImGMsUWOC3++ZlMooCytKz+NgN8aKGbA==", "signatures": [{"sig": "MEUCIQDDEDJ33dDQCU7Omv6DvsR9pjhRgPtD6pbEW7ZEGJkzfAIgaP+WUcCGUx61q09FBQli1Er4u50xN4cSe1HmWlH7i5g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11316, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIpzZCRA9TVsSAnZWagAA4IMP+wTFlOy9LSMYiVczES4h\njmVr/v1CERR+2Q6KRu+l7qZXoWypMRXdiUDE/EjRZmQc2qU0RDNCFtOU56Ta\n1og2msADfm+XvsrbyCQYKu9ZeI+TPSvGRLNO5vIWvaIDFV60I36YiqcUuyRO\nt/ZNnN0bLIqSkmn1KxAPW7a2F9uCSvef7sZCk8OV/uR+FDuj6WfyfK2pOtbi\nOMD5nGVsE2YzI/ix16xPTxTIipkKoVENy3vBD6d/2nTQ0+bsZ+0bHcji2Rfy\n5ABUIBD+mYDXG1Vt9MemoqddC8THPnFg4KOc41XqW84ZCux3ELKz9cmEHk3R\n5b3dbd6rpSUIerMvmIOW57eqxY3qUCKZt2pvF6FADt4WMYG+zO8tIoj186qs\na6v2zg1BSn0mP5czQ06+f7iDJL2LEApizQrM4ziF189hc5wJxIQl6H0TmFIF\nT+5V71TXPmJb/7J1+d0brc3E2dzakzl5wg4mQXFKIvJnnHV+0g5Kk6eKeAf+\nkhxibGY2jt17jVSGv4lXNIiLwismCH4mDNNVzCvIvRct1MogU1oe50x9fRJZ\n5//5Ivx/gqiJlkAw6RCILOX6qdt0stPflqPl5yaUuuZ5rA5F5UxMQ9RXaMvx\nJSjO/jgLfzJ8qQurAvriztFM639X6k8+XHu5j5oP5zR+//Vjb1Mf8MXkI2aG\nqkOW\r\n=4neQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4a716811a309dae135b780a87dc1647b285800eb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.0.0", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_26.2.0_1596103896735_0.48348352200582845", "host": "s3://npm-registry-packages"}}, "26.5.0": {"name": "babel-plugin-jest-hoist", "version": "26.5.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@26.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3916b3a28129c29528de91e5784a44680db46385", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-26.5.0.tgz", "fileCount": 5, "integrity": "sha512-ck17uZFD3CDfuwCLATWZxkkuGGFhMij8quP8CNhwj8ek1mqFgbFzRJ30xwC04LLscj/aKsVFfRST+b5PT7rSuw==", "signatures": [{"sig": "MEUCIQCvs7BChlPZUYx3pS2DlRXKQZMXfgBPGf3Z402ZF0TGoQIgd2ayquwWGxtG6+nd1+8KKLR98gjy56LGYaDcmnzUdpE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11257, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeucmCRA9TVsSAnZWagAAQyUP/RnljOX3ltQFYOkNri9F\nSOelB2G1rwYaRaeguroO7e/2hWFvFXcUhRApNBrlXdQonodhMf1BajxjuDQX\nBeWN4NnRM4dnnyzof/LiLJZBCXrRoSf6b4QnZ9DueesNunXFoDSkM+8jT8Tf\nuZn5OIeogzGnxH5JepRtD5E7I8rUL8VDQQf8l/klKYcBRXYYfgxlCmcZJ11T\nJWfZqUtWw8e5R8pAmRwivu1vKa8gv/gjEqs2rEgrokgAAmdCRO8ebeNg7E9V\nZx2Lrj9rpE9kkHhWEvbyyXICm8LllsuiwrJeKLVUfFeTkbl1DxJ9nEkJnzPs\ncAzo4gK1m1IzE4KQ6DnySyr45Ws9R/M8k/qAiVop/SsTpLhIseDy967FgLGU\nNiRsvnpB1I5wCRL4BLe1ibBUgBXQ6ibY3vauEsyFpquZ23XcYpzG/FSyR3R3\n3r+a542NYc1eEIN8s2h2rL7cBHlN07SjAkOE8Kxd9SadNKQiEYGc307//aRD\nNCyUFcHAZuKB9w/mICRnOeStGHMx199JSDJ5d5psB2m7K+U+9SsAFOEf/0+Q\ncOz/kl4ZyITMb2nX+rjwA1wTxlQPBfAk4B0J4V9hNhWykbkvkHI5+aXpGR9U\nBXA13Hw7jGelVDoa4780qnhGyqrmQ6tXbUv20diPZy/Blp+6bvf7Kno4nIyV\nzwRl\r\n=+s10\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68d1b1b638bc7464c2794a957c1b894de7da2ee3", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.0.0", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_26.5.0_1601890086317_0.1290540066973489", "host": "s3://npm-registry-packages"}}, "26.6.2": {"name": "babel-plugin-jest-hoist", "version": "26.6.2", "license": "MIT", "_id": "babel-plugin-jest-hoist@26.6.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8185bd030348d254c6d7dd974355e6a28b21e62d", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-26.6.2.tgz", "fileCount": 5, "integrity": "sha512-PO9t0697lNTmcEHH69mdtYiOIkkOlj9fySqfO3K1eCcdISevLAE0xY59VLLUj0SoiPiTX/JU2CYFpILydUa5Lw==", "signatures": [{"sig": "MEQCIG1pTwGWFpUIGcWGrSwRk1qUiitkXDzEuHEQ5/BAbyJcAiAFCQSl3tFFlgsBquxJhGTtj7Sy+QI2tMpDcIHZgcPM1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13335, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoADBCRA9TVsSAnZWagAA7aUP+gPIROP0GgfVniQX0Lrh\nZzVWz6LazGHpYUcv0Xv1nwf9+IDGLa29MrWHqnKolLOrHdObNy7vqlVm+PX7\npCM0rJ2yQlocTAXbWFkC3Llo4Y9fBQcXRLhlxdSHxRrM/LtZfJgrovSDwqLA\nxzuT6YpWXZDTtwUa/aNz0hg4H99623DbsgcoThdZwcA12n9k5GrqCPV6h+c8\ntyQnW8opMmFlR4d3hG6VvKq3v2RhgQ4lQq7Gof3J27/CFiAKmiwNDvAwmW5v\nGAybIaViEFj5EAu+ulH4VeRen4VbB+NZQnETDOoOO931BiV5HusQtN4OxHOc\nqC7uT4lZQO0Y7g/3UKRXj/QxEGW78oEAuo5zHW4/Ec4mNR5VDQOJWFb5Khoh\nneR8C7iH2RNyBeUNmhhWF37+8phNgHUUDKjbUOvVhV0rmSQhtkfEXHhDvhXe\nF880ZacTW7l2Yj2GkRgSVBedsC+zQL/o1C8bsiQAeZsaFB71BOB2i0F7ByED\nqpl4WBL+swAMxFh3O4OrntaQ79aNXZfMm7MXhbPnRj623fgQ2xdkI3lvMbzP\nW3x9ZaxnzzNSXqbqbHL1yDTRmT03vCsWmE8vMi8tMmy0dhrtQtOfvJWvCIvf\n2wklYU2+FYeQGafIz+DRKzk2hhREFyjBE2euA3YSg/l6z6AvuiJIruGsd4m6\nVWOU\r\n=zMkM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4c46930615602cbf983fb7e8e82884c282a624d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.0.0", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.0.0", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_26.6.2_1604321473167_0.9733702804382478", "host": "s3://npm-registry-packages"}}, "27.0.0-next.0": {"name": "babel-plugin-jest-hoist", "version": "27.0.0-next.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@27.0.0-next.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d70462c31b2b0f1f629ffb55e958fde32a85f135", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-27.0.0-next.0.tgz", "fileCount": 5, "integrity": "sha512-H2v48wQCF3yJ+uVKc+VTh/ddyDGlBL1DEPNnA5lVEls6b5SwbWqP4CpZ65N7tlh4VUCVcMjy503W9DT8p94i1Q==", "signatures": [{"sig": "MEYCIQCLbWlFtQQ2+pp/djPid1hqv/LEDE8Ggf1cB79cvFESxwIhAMyaZdUj+XdN7M1xEY92RghdPH9bGBKHd8YTjSUkLevX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy8JzCRA9TVsSAnZWagAAXEYP/jHzL9xgDaKaxJy5rBRb\n6Ot1OMi4qYOrUQftb+U0vNTrHHXQBZ+lkwQKkRtrHzReLaWUhOUZU+D7a/gV\nhRmvACTzCCA37SReCr6qQgqGtD2lMUJuX/EYrgo0A1uZrndtn6AIY970MPAY\nqZFctSF9qvsFiYaj/flw7QDfhYGhTvg1x4n7quXBC9b9pHA9gNAjVG+CXvPl\nZrhBejfXkbbjrte8f8bu59Gpng/qNXixudnX3mleu/dmf3ejVNmCtD3N5cAQ\nZTIirAJT3+zT87crcFLhCA1gbMLQ+PNzVTcFsbUmEk2OXQZxJw6ue376eMhz\nV9F73nTKAJSWuCwn/SYgzLmP2TytFThm3wNe3AtHM/SvNnx1izGJ05YaJ15N\nN3iQRez7D3wGUfasLy/2cNWW8R44iUfLhZvXfyUKQJad0XF6mhD4s3GqJvJk\nbJgth15uyPS8emFZV9HJFeyP3jCkE3TPjJF76iV8qExJ83P9SuGIAH0lFtvy\nbIQMWqeIbJSd4aCwc6ryVlEFCt1XMiwtx2D1auFCFiZVoDccxLA8CPoF3rh0\nVfaj4fU3dwqbk4lvl+CmeKkCq/eqJcTMu+8zq8MnWpZMFPBAeTxsQqO4vPKu\neRzPbdpG/2kU0phnTEtrHWasSeO//Xksg2fPU6eBqnkkHy0zbtr/y/KWlx/+\nUjUC\r\n=+I5q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f77c70602cab8419794f10fa39510f13baafef8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.0.0", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.0.0", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_27.0.0-next.0_1607189107018_0.6057163036967781", "host": "s3://npm-registry-packages"}}, "27.0.0-next.3": {"name": "babel-plugin-jest-hoist", "version": "27.0.0-next.3", "license": "MIT", "_id": "babel-plugin-jest-hoist@27.0.0-next.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5a8a32acda0946bfad7ae25a99168b42e39229f9", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-27.0.0-next.3.tgz", "fileCount": 5, "integrity": "sha512-s6XOBa+TIoy3tu8SLENLGk435ynzc6ojJ6O3ErI7iHyNSPjsQIZqZLbV1nIEdD5e7IrbtWml3umtxJqMnjmcAA==", "signatures": [{"sig": "MEUCIQCQ7SnGJM4KnGyxJ3j8ZBYQWD/c1aaGnw8KJa30iW89GgIgHHG29uMA8b7PuSH8Ovh7CdhcaSRtd1gZFsrn+mxudL0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13469, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLuWmCRA9TVsSAnZWagAAxO0P/2nVj7F9NF6qjaYXiFw7\naPk3npBLmwKb9K1sfQLgqAwudZ6wKmWp0fbulGWUuG+UbdnkXOPniyOnCzhm\n72myV8iZd1y30MD5L09V8SaqXwhBdaE1f7z934vMGUn6SFJT4hpQ+BTrvJ4z\nxdApnHzHstLrEx0E+WNT5obwk2h51eWmus1yGrT7ZlyhIotdUaKu15VJeN4t\nrL6xWGyQwinAVqQn4jLId2rhgla2iELNB84n9swLIZ/rKoRQkkJJYvcUoMDv\nV3348nzSpFAG3qC8pH2y8TAxC5N7hYmoPmRP/8ug5/E8VpRQudNYv37BGFqz\nV93jUBOjaK8FUF3fDGzv18n2lT6OX0/IMvFNmgojUtSD+Io+1aMTdnCTs+aX\nDYqyVbdxEPrnhxjchOEATdP+FGdP9VX93BEsXt40PeB9BIKp0MHMxeF0NH2G\nBr3eqxP2zVO44echCDUas6YYiZNIDiVjPwybXCcOzPTkPrfLocISij+ONf1Q\n+ax2lAJmHSfj3RqesWPl9zErFCKhhjrlpGMLMlnbz0k8hrMYEngWi/PSErpo\nM/cAkMjAtA3WPz5RWcYIOP+xRFc8wxbCgIbbAEJT+crmdvzvYGaL9U4sil/w\nexR77xR5QWWZ8VnNJOEgs0ucKSvv5AhFzMhWiRpV0fBOg0AycM4+iz2dFJfX\nd5kq\r\n=XaxS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2e34f2cfaf9b6864c3ad4bdca05d3097d3108a41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.22.1/node@v14.15.3+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.0.0", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.0.0", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_27.0.0-next.3_1613686182010_0.6957730847157129", "host": "s3://npm-registry-packages"}}, "27.0.0-next.10": {"name": "babel-plugin-jest-hoist", "version": "27.0.0-next.10", "license": "MIT", "_id": "babel-plugin-jest-hoist@27.0.0-next.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3a0fc859686be483b107ee3e820941be6e121fa6", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-27.0.0-next.10.tgz", "fileCount": 5, "integrity": "sha512-aFYm+iATNx905n2vQ3Et8I3SfuiGkZ+8JBC74ijz2X+vsbjYPGZv+ZoAtud8OsxEChd5odVa/aBYloa5Te92Hw==", "signatures": [{"sig": "MEUCIF86337KJ6OtCzNtD4/o6qDVaztE0xkK+odNN9Vinq7+AiEAwi9g8iMo3oHUpZA9NoV4SQBeFMTxaUaRevlItVuqi8E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13462, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpm4ACRA9TVsSAnZWagAAmCEP/2vlTJum1Lm1yniE2AUa\nALzmJACvgJ7TERtgXopIAdZj6HX2Mo+uAq9WBUjNBT28sd5NI/nRH7JikI+Y\nQPiEhfV/fI1feMa6OSaODAIT6E9oDawrNdi3BDn5mqrEYZYRRHjA2T2wmWtb\nMN0T0e77ICqBha8yNHBtcSyzRZSvEJTlsz+vsN68vSGachSc/cbzij+syetE\ni6eAOqarublKu86jUi+s+jCkQq620lXnHBMZ/fkV6TdVK4kAgvALtNP2tEkm\nf/lsbPdVuKOyQ8VO1klgXjWSfdWOagbEOqyt0PO6MRTAc3xentik0A7r3FyH\nzhsnYN0zHCFPEWcZ8cp3+CHFr9uyYMNqZ0F0zaCDumLfrXwkNlMJx+i9+dqf\nOR9uzAkaVrHf8S6d/21AAQhP3RIELHjD4HVitMBHNKvi+obxgSr3r2IcH27P\nxxKdvfMsV0KIuvrzj+ksiVmEskNC3S6au8lEOLrqPpd4iEzyJ6mPI688cp2Y\njWTFZAS4FN18nHH+KZQD3iXWyInyPkAGGDRII68Z2wBbCE2McAqK9JiheXzD\nxY9URV2C+BpsAobz23tXuCgblMxJaFXbWkKXOEQIycuPM0i/P7zcdUSzmwi0\nHpRZwJK4n7Nktz826RXjYQqmU/NlYiDj2zRRCwImss3N+IsqRLT20W4uoAzK\n9070\r\n=T5Ja\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "6f44529270310b7dbdf9a0b72b21b5cd50fda4b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.0.0", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.0.0", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_27.0.0-next.10_1621519872579_0.14554595719810615", "host": "s3://npm-registry-packages"}}, "27.0.1": {"name": "babel-plugin-jest-hoist", "version": "27.0.1", "license": "MIT", "_id": "babel-plugin-jest-hoist@27.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a6d10e484c93abff0f4e95f437dad26e5736ea11", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-27.0.1.tgz", "fileCount": 5, "integrity": "sha512-sqBF0owAcCDBVEDtxqfYr2F36eSHdx7lAVGyYuOBRnKdD6gzcy0I0XrAYCZgOA3CRrLhmR+Uae9nogPzmAtOfQ==", "signatures": [{"sig": "MEUCIQCOI4l1s9X7qEZ1fVNbo6YHCM2kAOWT0Z+phcmzxAPrEwIgLljMPOFGJ3wmeSDgx5MW/YZInZTBk5qnQFHkCmekZ5I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrMwfCRA9TVsSAnZWagAAmaoP/jr0o0iQt2q9zFvLc0iu\nMH4eVsJNr97ITY3F6AK/fPFtOMDwfUEpuDm/roR+48n4MvoGt0ZwFI25ZR4M\n84vPKQPbLtKVwPLmQ2hbzXtmhs8RlBHGcHAa+8l14W9gyi17G+hOj+lMDGlu\n/WWD5yQwrDFal3zLNtd+QhzGRn8S8vFF8pdVapL8vTSY12OZ0u9HqX9lBb/H\n8Gf2AdtGIQDziCrC4Ml9vslxEZ72jmfNkap1aVZBA0Dn7apXwOL5s8zGUoFa\nemyTV7geQ8SJfBw1DHO/6M+e2GE8s3G9HGswVbh3FhxQ4V4XyzstDZAHDCeB\nvQ67MuE3eHdryvwbOf+EBBlRvDLFjCGzJNsjXa33aCGtgUuUgTg6dNWRuYoC\n7jo3UCVqfVz04KXay+tt2EiQQRwKj6NZiNwKw15yQ5/x1kJo2zrxtsmZ8Jgu\nLwSBEeqBuzsSKzb+32mcMgf7CWUhIVxHfbGVtsBE8zbnkfE/jUhQi5PMmGVt\nfz+Vr/8xLBTkm5xMmPciR/nVFoZGrvEsMSywgDZsAagPLmQMqypu7yafXF+Q\nVlXcUgeplovkVjN1kLIhlAL/25GJbwaAMnvY0fMLajY0jGwH0+qE3st1wbT+\nZd5cEuYmx1iN6C5ciumth6T1Xtq2+BweUu52C9IcNcCCMOPnVhbQw7JnTPWn\n4pbg\r\n=mPCn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2cb20e945a26b2c9867b30b787e81f6317e59aa1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.0.0", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.0.0", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_27.0.1_1621937183346_0.48339592200083237", "host": "s3://npm-registry-packages"}}, "27.0.6": {"name": "babel-plugin-jest-hoist", "version": "27.0.6", "license": "MIT", "_id": "babel-plugin-jest-hoist@27.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f7c6b3d764af21cb4a2a1ab6870117dbde15b456", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-27.0.6.tgz", "fileCount": 5, "integrity": "sha512-CewFeM9Vv2gM7Yr9n5eyyLVPRSiBnk6lKZRjgwYnGKSl9M14TMn2vkN02wTF04OGuSDLEzlWiMzvjXuW9mB6Gw==", "signatures": [{"sig": "MEUCIQD5kq9+O19U+O6mtIShBFDXmlLGeACEgSahDNy4JyPwawIgd6KT0oax6zTEnRU6wSlk+oJjTF+BmJaIGVNVBQp/GSQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2gFbCRA9TVsSAnZWagAAGzIQAJNuKJIMOlLE9tRD5eDm\nAbfFnaSzFxj4djCAS6EaAPiLYQI44w4EzdTqEMljPxaOAbH5u/gfgfh1lpkZ\nJgY+TTR9lvoZiuvkZNfoveEcVaicQUAM2SFbSFq5lJSAE9VZZS78cAFtlS6y\nfaSSCksQu/mZTqap9PiyFBvKXE/MmWoNzwoIlthGHRb+LL59rgfZiKhEZ3Ms\n3KmKtwcg+NW8ZEkHeTaDchimYkYOb+497nW2XCofjy/oYHPWmu6P4OKX3spP\njIkYxnxMxgvdigdWlq75KyrkQyBV4mH54EwO1d3i6vZZ2lFnZFbHarOrcvhu\nOm+mjlcygC+RZNbsG/Hd1Ihd1xEt4jlxPIKkqMqjn0i4RPNnh7tfUDks4U9z\nEapkM1/ShaTAO6jhDAiRjDEyMXVYmyR6wseXh6vmxi43qzUBQ1E6Vu2dW2EH\nTknzz78v6y5XuOftVTqTjIcxV2rLRpNkVg5sr43i3HbDVF8/6V+hbs4Vi5pF\nRHTyS79yLYfACqBiJOJkmJeVue2GA9m1p9ygk/fr9ASKr13H1n8b6OIVEAqY\naGZBMH4YaEOyVPY/EE486+5LWfroas4mKeNw62v+7CcIKr5TDObeBJUD+u+2\nk98UTUKALQy5zh6WbBBGzVxA8fgl2pR2azZ3Hi1Gm8WH9JIpIGniXJN9ScbN\nIBGY\r\n=i4zz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d257d1c44ba62079bd4307ae78ba226d47c56ac9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/master/packages/babel-jes", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.0.0", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.0.0", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_27.0.6_1624899930772_0.827669373710646", "host": "s3://npm-registry-packages"}}, "27.2.0": {"name": "babel-plugin-jest-hoist", "version": "27.2.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@27.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "79f37d43f7e5c4fdc4b2ca3e10cc6cf545626277", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-27.2.0.tgz", "fileCount": 5, "integrity": "sha512-TOux9khNKdi64mW+0OIhcmbAn75tTlzKhxmiNXevQaPbrBYK7YKjP1jl6NHTJ6XR5UgUrJbCnWlKVnJn29dfjw==", "signatures": [{"sig": "MEUCIGbxXzR0lWWwkqtVFQIVCcjn5MEwLWSAIaogA0Fs/3qEAiEAq39ehxhcgMrJGh6L8rAItEAeC0dR90L9QtWi99Gb3ZE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13452, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPwaKCRA9TVsSAnZWagAAA+MP/03A4GdvEnbjht2h8Xla\nMD8tRLBYab961Fo7r1yl3Ecx7OMT4kSbBzTmpFSmn4j/nJpkyvnsc6PfdayC\n8YdkC0BrTHRKI7gN7fqczNE2PPfLFPUvI1nDVIoP0yy8b9A8rG5afUx45UmY\nav0aDxloqmsmE1esPHmy2ZlqJdOX4drOnBOeDgBGgGyMf4lCa/33SJhbIUue\nblw3J8uh5/KjqE+y6zgMU3/cQf4I/V+IrOUMhzUD5JLQCs3s9xSgTmLeoG9l\nPmKKpPYVwxbXbXtb0ddqvpmESqzVb/VSpoH0O1I7iwr7j1RhgRSk2mKTSbc0\nhECD2R9yK+OR1bYHecAxI7vDZc8tVdh0u0TwsQJriFMCJEZ329JkFHcVyOCh\nP4jdsTQqFUqSZFtwFxPnG0x7c/PgVbWO76IQ0ZToLckGlBRYcIB8iHI1hgk1\nN6QhXZ2PamefUPjbu0l0eEs+Ch+YTfun8XxTUDjZ6tObPvLM5taOwt5jD3dG\nHI62RRck4HzQGSd8DY7H3tn5tsyBSrWtGYsfKWQoau8HoazC4np34ldFs1y7\neXnsymEOgryT3DFcwc9j1WEucMz+oL7TZIug38yfb/Gi8vFFwm+Rujd16KvW\nxMb7UWnXnlR4FdpBTw0rwCNZm4B5UY7gXWYkboPRycWlxL1iulqXYVejLiuJ\nUYW5\r\n=Ixqh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "b05635c539f8f673dfed5bf05ea727a8d5d7bbe2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.0.0", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.0.0", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_27.2.0_1631520394656_0.6982186755439976", "host": "s3://npm-registry-packages"}}, "27.4.0": {"name": "babel-plugin-jest-hoist", "version": "27.4.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@27.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d7831fc0f93573788d80dee7e682482da4c730d6", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-27.4.0.tgz", "fileCount": 5, "integrity": "sha512-Jcu7qS4OX5kTWBc45Hz7BMmgXuJqRnhatqpUhnzGC3OBYpOmf2tv6jFNwZpwM7wU7MUuv2r9IPS/ZlYOuburVw==", "signatures": [{"sig": "MEUCICFL1Hp9vDBde9FLCR6p5u1H7pAtHunfydXmyQiKSFTeAiEAmic6glUj5zdtmWHSsXV3M+BeAR5KjskPrm3C/axqopk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13514, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpNd2CRA9TVsSAnZWagAAGh0P/jmr4UAHdlPT6YfKqMom\nH478gQQ3W+ZPlR9OcapEhDP3Tjo/La4tY87g38Dr1ZQT/HdTo3u5gL9BK379\n2hFjZ5ROEvM9XBBWUFxGOljni5ZWlitJ99iNzLPNlBNAlR/I4XLu0iSkI+oa\nY+xnjGjCS8DXYwAek48/hG5iAPDwNYRt5Ye2PfbhzAB9ZIP0BFBchRbJ5mvV\nyUTG2mCo/YUEXFruVOMaALGfYhZaCS2TivNcwaG3S//JSKGRxZ9ec+et/6Ai\nRGJIbnc98B7ZpxmQt/9QRSJK6DPqJzwFyvAGDV4o7HP1rPFoJ+sHxZiOVM0I\ngiPH5Rq4O4sDs05yLeL4pPjG/qVmkynTocg7BonB3hntdJgzHUPz1EEPhuWB\nNKfr4OE+OqAp1oeO0jbGMT3dRhoyvMnT8JmB8//FJj3D0xoQwzBGzQ/0MXg+\nh2PCwngcUl6dEhAxQqdyCg40GeeJind54YxcaGl33cycwsgAiiG8KgcUUrlQ\nHd/Kb+g7IrpWBzZJBQZmVoCRGRdklCIPFI72nkqHjut03ZY80+jQmBrPiAMY\nt5tgcN2XohvfNLN8nn5A0Ma8pXq3WgUbYojrgsJwT4dhtqSkbnoUT0Niu2My\nQdxAF2+SmkU5rlgoVmCBMjhcdANSPENr0MamvDoD2lwjWA4EyYLVg4fIMzS0\n1wBT\r\n=Ii+B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0dc6dde296550370ade2574d6665748fed37f9c9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.0.0", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.0.0", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_27.4.0_1638193014696_0.7011917920786648", "host": "s3://npm-registry-packages"}}, "27.5.0": {"name": "babel-plugin-jest-hoist", "version": "27.5.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@27.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8fdf07835f2165a068de3ce95fd7749a89801b51", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-27.5.0.tgz", "fileCount": 5, "integrity": "sha512-ztwNkHl+g1GaoQcb8f2BER4C3LMvSXuF7KVqtUioXQgScSEnkl6lLgCILUYIR+CPTwL8H3F/PNLze64HPWF9JA==", "signatures": [{"sig": "MEUCIEHNzN7EjxvgOI8CPdazQoz5ZWSmYpb/5/krd2kdyGb6AiEA7r0qy4+oEhS9IEmqGuV6o83+X5IBgxWnFfCWBGPtetE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13514, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/kp2CRA9TVsSAnZWagAAbZ0P/2h+tTvs5oPi2XzuN8eP\njuuNcZkxIl6afkEyt+gh1d1U2ew7zsQZk3+Z8o5t6RSwt4sfSBVhjwNG5cLU\nFgzv+CIr/Fg/OitbBKXolfWeFlzpRFUszhhBSYDz6As+07SRE4HzgJ1frAE9\nv+5TvcvjnB4N5afvd1ff/DT+FcEkCC9gA1DdgzIsXbj71kMcTKqC8UxkhEOT\nV0HiYoDXn+XevhV4o3PPIsgYhxMhb/F3V06Nm7IxqB5l5tbGto8Q/evvXxRE\ndTOlK1Zs/F+MjvATMS0ONzp8lNystiO5++N8zRbnRKxEHY9JJA2GGI1Swmak\n9f9NR6/Y5ZnWpjo3Gbijh7cQAON8S1ZhA8AmzUL6dpmgtnVkEB5Fc8OGDlCa\nFPAgr9im4pWQlX7Fryjj+oa1QHICudbGxzhpZmJQ+TS8Oob94stzF2VcTHUS\nnc3C3b2lirdrhtF0Mc6/p9lNq4ZeyEE68YAnbHmDBJRzNuZgJq6eYpTBd0sn\ns+ErLKDs95XPdrbDA9rAx6TRpMK86miYJq00cB0pUs9ju5cS536F5/8Kvxls\nOLBm+IagVCGhwyoKCeeQGJjSuFcJNnvWUNtz53NXrehSkfP3OO9jcu53RiI7\nqKDEFFr9zfNGvFde+Um2M6NsI5wUZbtF6KAtXw/37A4TBqSrsdMq4qYvuAI+\na4qb\r\n=/+pU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "247cbe6026a590deaf0d23edecc7b2779a4aace9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.0.0", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.0.0", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_27.5.0_1644055158280_0.8037731077391237", "host": "s3://npm-registry-packages"}}, "27.5.1": {"name": "babel-plugin-jest-hoist", "version": "27.5.1", "license": "MIT", "_id": "babel-plugin-jest-hoist@27.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9be98ecf28c331eb9f5df9c72d6f89deb8181c2e", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-27.5.1.tgz", "fileCount": 5, "integrity": "sha512-50wCwD5EMNW4aRpOwtqzyZHIewTYNxLA4nhB+09d8BIssfNfzBRhkBIHiaPv1Si226TQSvp8gxAJm2iY2qs2hQ==", "signatures": [{"sig": "MEUCIBPEsikXUc56iqOyutxQ6uwutiz65X9Jh3/vLH0FV2VHAiEA9tRU6vIITOmg5dCDIzv/XEjbJX0KfEwz4X8W1f2/SRQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAktcCRA9TVsSAnZWagAANMMP/0tQJYQ7mKL/ZN+LOAfB\nVHZ5wGl5+4Uuvn68KLGtcTLAd8T18CNzBkYd/nJ+TPV+LLMak+snA0M5t2Cm\niQuqev5wyHMAod+1kZ6KF2ctDD66NNXouLa3OAGbhHVdZ2fuxGP+lBCIWIJM\n89xZ6GVqS8VcKlBa1d4nf5r6X7d4eiK6O0JeEYTJYKVk7hfxT1UziqlDp3Bv\nLEfpwQx3tjaB+SUMFTSUJylJJ2+wnf9T8y9TQPikWMa/XB2CgUtYzRVbMV4K\nAohymgAPnUrwgY+u9JACGTI8Wy9dNMkdYpdHgCERnKi/9QsAaCfJa38qiATY\nGNODR5nzWm6CRsFNxZIjeNq0nNi6UayoW69zWkRZgDkPz08pRRgPJn0+9Zaq\nACdC9qZz0dCpwIdoOet2kxNt8v8vg9YwXtkI1A1+k+iqXc8+5FLvzt4mOyS5\ntsB1TVlAGe/+nixJGi9YjTxQ7udFPp8ffZ4WP17IVerBwaRYaQhCYvAcYXjl\nN/6GCvnQnUJ2E/CcKk0jCS/eCH+ZIjxKKCur5mHzHoUbOvzm5/vMA03o0TFa\niNvJeDGWPzJY/kKd6Ou3kwzT1RTsyxHCvRDRonksXoCP9S+feXAi10ouZw/Y\nmguY9j9pfSAaG/DWKG/AAFLpnHmAijer17w7VZXZ0L8KYo/pzDj/AiUizcTs\nWFFO\r\n=HFxf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.0.0", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.0.0", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_27.5.1_1644317531956_0.5877540317666456", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.0": {"name": "babel-plugin-jest-hoist", "version": "28.0.0-alpha.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@28.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f3fbc996cf5b5b2572ae27b21d9372a52d9aa58f", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-28.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-rvWus95GjXfXQrK7JsFNM6ciKJ+nOcI59eYQyDBgbsJhfmZkojVss3vYECJqPzc/8ybnJoPyfGpPRqTWixTgAQ==", "signatures": [{"sig": "MEUCIQC30RFn5jYiGJFYQ1LOEuFjs5+gGhRTvlDlmetgerP0jwIgFqaIdawhQe8GhX6W2WdvWLZWtvtmZkuWrxpgpJAXUoo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBVa2CRA9TVsSAnZWagAAjsMP/jZcUhHJq7DOShL0ISyS\nc32eYj8XhOu7CxmRXYRWiIQoxrw7k6tBcZ9LqzzR7EzHtaEdTVr7xQPKt/9u\nINfMaYjtG87LWwe1cOBFGhVTyGGz+WqIWCDl8BMN2+m8vcocKTiVdUZbihVl\nE+IwgebFaJikdrO1pgvllRdqQC+z1Aqj8jflRI4VKPzeCOqZHy7elb2g73+D\nsThvp2YA7/6aQqU47M6eibncM21S/U1pTvLBA1kX21Zg+kLupv5kwcfLQSjx\npNSjTeKTw/pVylgiNdjXMgeGYeNIw3ODq3wHESdgBQFodjQSD7eSlG6qf8Zt\nBBs6l2FLDbl3zYlwYRvsekjTO/HX5hHqTjr7VSs0lHCYNWO6JF/tOMWOSI2P\nHi429v3GPAEeu3x+zUKqQDtQjnED6X6+4lhaJyiQTXL5FMU4kHE1X2ZyFZ6b\nsw2QjxC1O71GbiPtP2iMFwtYQ11J/aqenBX9gv9fuRq19a7j92PuzgxDyS7d\nwqfN987lTs9JvHIFQ0mlbiRo8TppL7G0LuUJNJ66c6xwD5s3Ba5JtXVMBi5L\num0sETixa4ZjE1vb9Mn5/iwyppXB+oAso/Y6yZXm63nt9juSp8WbcYiXGzXs\n7Hr7Fo5mzr1o2eSzRbWWqMuo16FV2XJ5umVEGBCYYoR8ZvDY/xoq21ks8c1R\nK29U\r\n=maQ6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "89275b08977065d98e42ad71fcf223f4ad169f09", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.0.0", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.0.0", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_28.0.0-alpha.0_1644517046385_0.050100972458334114", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.3": {"name": "babel-plugin-jest-hoist", "version": "28.0.0-alpha.3", "license": "MIT", "_id": "babel-plugin-jest-hoist@28.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "82ae9006a733a7223d207a10825a27e3ad8b149e", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-28.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-Sz7G5eS8368aIcVPlVv2sj6EBkEomwOUq07DNQV9HBksTgYzBANjPikXvR18nOSXgy294hti5RawIMBUDshHow==", "signatures": [{"sig": "MEYCIQCEKBr+nOg1ysih0Dx4coX+D05FJViZXUw+r6znl2OPpwIhAKhxZzYFFP4S1RmCAKoBgbozyvpYhbdS3Q0sQMyzmqwO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDmzcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMLQ//T0upnhuRI+TbvI0SZoI8gTL3NQmixKQmGstX+16J4B3KJLy9\r\nEmSBqGyZmKgl7ffFRsjd982NOGlPRyB2kncp2moz4SjWrzzw20hOXtOi5cmW\r\nNqwV0CLHc8CZT2lPZZFNWvF6HEYH/USrza7cIuBSjzhyNpma4WeVl1qZOfEv\r\n+sl+0n2Qu35Qrk2TyfDMCKJ9rUERX1XMWMQ6A+DwD74HO6YV47+7scLGXnYu\r\nyr1nfqMphAix9PflB+2xPTChLCP1ej5FI7L6xB0t/oXiewuSmfeLX2gP/xc2\r\nBhqQEbL/DWTpkC21Qal4W+ZuaEhGsRMV14dKtJmxEvAvh4HdPVFKwzQTlvOy\r\njkX/eLW5uwBB8QKIG9RME4z+QnukrvmPpQHR/2xj5TZRZ9rA3pjXjSIT9xQi\r\nAFfOtnnID4JNGOSnM6F3M1BLy4IWJuaXKaaMTcMYULSMNYLa0Q//J5r4/74A\r\nLT52G9F5RZ3hS2ZmMU8eQTVQIMERPMaQCcwT9Qjm7UoMcsP8mPYLL3MwqvlD\r\nDxBBBAkoVc8856NVuCOs8goKW24Fj9PPEXR3zPcSghz4nWWPSFuBJs+byJS2\r\ndUAJoWjgm8TlHbMnNfe/2cEcmQmwyEpQ81RQJgfPca2D7ni/gXIayO51bHEv\r\n5SlfAas0rXp6ShX03XV1w3qrNANPmhKojSI=\r\n=70kk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fc30b27bd94bb7ebeaadc72626ebbdba535150d2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.0.0", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.0.0", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_28.0.0-alpha.3_1645112540007_0.8213313988155555", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.4": {"name": "babel-plugin-jest-hoist", "version": "28.0.0-alpha.4", "license": "MIT", "_id": "babel-plugin-jest-hoist@28.0.0-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5a79073a95757a602c667a9f8a8713473df6ef4f", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-28.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-1Iq6ytoHtS2YnY5RmWdQUXM6pR7+7pJz/RC29F2JeXPXTbHaQPFfeRuasNST5Z2QGLixi9Xwbb+eF1lO6uRWxw==", "signatures": [{"sig": "MEYCIQCDsIqGgSbkLmycrSAMLLTCmUYXULUbldbroQ9x7tF9OQIhALqdXByqfUoYoJT0iUeTS5OVjnUFTFVEik9dZ5wwNtEt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13706, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFNOCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqIhw//YNQPFDNBeB+AA1Xma4tMk8WRDy8ZDF2Hjjxm8dDyxamlJx3O\r\nm4qMDbo3RpCGBcZnaAaRHYkEF+d7MBt1Po3QIK0oAvMhKYoTjKjvX6QUyMg4\r\nSpvvfvgC27zGoa9KuecEFAdfDHZtrR8OT1Qk7RIZdZwXKjkrdxi7wYN/p2qW\r\noc1bUgKXifrE2KbzV3dw0nlNroc2R2glEkzRTGhpuTfIXS07f3/IZO5/M2E0\r\nZKIDvSoZYl07Hz8aAdIMdJ7sjW5N7DpYOa974NJyt92JNkD25r97Ece2XmTb\r\nvh7Qv7nx5PLNsMxaAI69ZhDNFYo9HyecKMKmQ81JrXpAhOvsgSzQ0IQISFNR\r\noFPD4GKX8rKR8NBgPvm8CDxCrmutfqc3buTEW0NSzhdeOBKQWt1Uz9csgM3d\r\nEBuDHPbjZ/ilsDXCQsdr7r6mhXgSQwjC9qFHfIQuJ2Op6pyJHbYB1TIKO21t\r\n/Bgw7+YD/zx4Ado0emOvswXrhnsXJdnVGJCso+wqJYgPHLOq1CbiyCATG/e/\r\n1tovEejXHQbSweE6W3R9ulYINMFK2u4zBkf3pEbSK6F7liFyKSzxSrT94Fkv\r\n+tucvg6VA0G10UvVu7QBMiiMoiXXCbJxCMhMhcFKBgapHrbGCQ5KXCCC5YDa\r\nxH5uLQe+XHQwt4U+yc6mbC8FLeS73TmDkH0=\r\n=dMk4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c13dab19491ba6b57c2d703e7d7c4b20189e1e17", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.0.0", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.0.0", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_28.0.0-alpha.4_1645532034467_0.6592427539753818", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.6": {"name": "babel-plugin-jest-hoist", "version": "28.0.0-alpha.6", "license": "MIT", "_id": "babel-plugin-jest-hoist@28.0.0-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "478ff038f24bb076d97f43628ad5b80f9cf7bb0d", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-28.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-6XBV8vogS5pqxGjIXFX9Vc2CqtBAbKbqdxjL8Iq1vii4YC7867MtKZTkmakj4g0Bx3LVCUXNCJPbbyc1rfL58A==", "signatures": [{"sig": "MEUCIQC5P01edOTmEhm+StV+oPHSjrO/V2Udxqv9qwub+9SIygIgaFuwWyrjTxKKL+P0tZpO/SfnnRM3eLMCKtwssIbCoPM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13672, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHdoWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpiAhAAmnwcNV5BNu1PmLXPCpZ70EdYBWmexOpIM9KJr1ekh8UmllIE\r\nbX82+QJTCTLJaHVBC8TruTq47V7XDgvM8MeAe6A/3S87n2VZ0Q/75H0g8PSN\r\n5fxai3J0rntSO4x59Zl6mmGNrXVeDYNVa/wz4Z/qPNnE3phyGavoIQf6pb4d\r\nq4ALqTdaBYS+PGX7uRWUqxBO+H1S8rsGEftvjueoPBz1ylDmaxbAH0wFKtIA\r\nrgkOyYO8FPi9VYnkjUU+J28Sgcq21SCVYV5FHljkFp47L0dODGhAGIZxfqyi\r\nUweT/qfx6inxj0e24MCnzz30a8iWAQ7bxqBAzaIk+KnsvMQkgMfjHm9SsyFA\r\nrejR6j+wxVEUGk2glUjKMJOFraN4GacMCBsVS6quEOSJRVURVSEIcOJLm7Jk\r\nPrVYtpyNcBZApdO1/mKx/2d7XtfmKbGlY/3mV1loVr4gSthbTSiU4mYyJ43E\r\nSGGTOZFGeiNg5sF4FezJ0crVmRLBlukcBwKvxkq45HEK7dLQ9y4j0582dQO+\r\nhsDfsxIC/gtNKHMErFoVJSuiO8qIJ011CwWXt5NB09Il2MmhIjNnAU3FQOfh\r\n2I/kpte9P702H7LatQHdwfe/Psu0K6maSas/fSnM7cu/DMtHKeiULl2GL8mp\r\nYUOkzOaWZ9k8Nh4qD0JXEqrPOxQGyz0IE2c=\r\n=OKZt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6284ada4adb7008f5f8673b1a7b1c789d2e508fb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.0.0", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.0.0", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_28.0.0-alpha.6_1646123542784_0.36378854106595826", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.9": {"name": "babel-plugin-jest-hoist", "version": "28.0.0-alpha.9", "license": "MIT", "_id": "babel-plugin-jest-hoist@28.0.0-alpha.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ec888965473a27b7489a1e95e05aeb3612f368cb", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-28.0.0-alpha.9.tgz", "fileCount": 5, "integrity": "sha512-6zEDlRjDtbetneMopTYPDEeXybjZeMGlDuWG0HommNTRnHStgV0sny8ztRZ9HjWAfE83iLk8tMgOD7LUC4WiSQ==", "signatures": [{"sig": "MEUCIDXYXViuau7Nj6Zd7CdKMaCfoxAGsMeNkEPUwwsKqJIzAiEA3VCEo5BHPLVOA4MY2O/19FZlSg477A4Qj7jREGEJuoM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXpYBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDOw/+O1dLtI/Yng7u7GK93sbgJHCN4RNvWUOGyVNhWxd4Duypx5QR\r\nijVHYQzFCirWm8/k2O7qGC10+F8LSmHPwbiPw2fQ2bDrpu6EL9C81S4WistA\r\nKHiWcIU/+bHq6g47JdfOZBdQLJpHrpAbimJMjfL2Zk9VgofoNF6VoY+uvk7v\r\nUhKfUlLJRhgWrUgIkni7vWeuegrjsMeLjIoprBXPHopQCP4/WVVl99ElmPLI\r\nsNXIpgdpEA6N5fopMmDInAOR+tJt8RH0IBZS2WNkRHIR1Oc3yx0BPnnxw7Io\r\nMpdWNLbA+UtYHAz8ffBehs/31iQ4dwLUlJAOHrFk3qn9BeT9A4Xs/uQGX+dC\r\nUVgNSft8LNo173q3vravOb659kph+OkYijJf2qpqlmxuq2kJOahDeCuwWIT+\r\nQ6mmm5ftxK/6PbwXPwCtH4XwSsvazHvrWQNBCS8/+H0btp9NGpTZdg4+IYh9\r\nmxeP/HgITbDiLXsW85WcRYM5uDLO7D9/6XjCnrDIsdYgipCjXSrgBUvpzvC2\r\nxHInMR8nn/2LnJc4QBwR+jHVRDTxtrbNsDvrfv5UozsPnvBfqeF17KEfh7+J\r\nmC/Xi8nEwd07dIsolqegIVul9GnQUr6EBJaV0TmMc9in5Iouewbq6KK1LJSV\r\nN1+0jUzUDjypjzeIXadQsNa6lxcsLvDFuBM=\r\n=79Wr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7c63f5981eb20d4b89a4c04f3675e0050d8d7887", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.1.5", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_28.0.0-alpha.9_1650365953791_0.020118833156697047", "host": "s3://npm-registry-packages"}}, "28.0.0": {"name": "babel-plugin-jest-hoist", "version": "28.0.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@28.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7429bb64b97170822dce58ee763555ebc983cbdc", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-28.0.0.tgz", "fileCount": 5, "integrity": "sha512-Eu+TDlmKd2SsnvmlooVeHFryVHHom6ffCLSZuqrN8WpIHE0H6qiIPW5h5rFlzIZQmVqnZR2qHnbm2eQWIP7hZg==", "signatures": [{"sig": "MEUCIQC+oh+BoNVeHgxFVYxS6q+S7yx8A64tLs5eRZg+El2mZAIgJi+oHftyDaqHUo/twsLYCKECtHK8gLPY10gUYmwNVXg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13665, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZo8iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrwBA/+L1guZ1XG7hzl8F4dbAIEwUjun7XdLlBlnG8tr5tk7Lo9ge51\r\n6Ag7nufIZMsb0env1e7z0RyFhClOdaswBEW/Z09hhDAYL/1T6YLYtUqa6Y+E\r\naWeNYghisPf/RmggxMnMGC/+2rnECdCAaHoYmYwOl3s3UA/Av7axMAo1Pn4p\r\nItx5n/MsC9JIesOEFUep/1gjnajUQgyIFOobLSMfraSOVBts+ehUQZoPwUdE\r\nhJcS0nohxsEGHjRrmBfjUEJ7LsH7DNx0IsmBNZH3PXrRm5ikdqqLdqDu/chY\r\nGsrMZkuqNkyob5mgP3NAl61iyLNSGPMARm19lzlpN+WdQSOGX0aLlYBz/NPw\r\nWVIXTclRerstKkUpEO3JDNepGAqNYYeTPeQPZw2CMCWfi4oejiLDxxMzUrXa\r\nlkDNIwqFsmQZ1NyeKZr7grc7az2nbVh2XybtSucLlchJmKz+3ooN5+b5UeW2\r\nqo7NTn7Y5fsRnvugN4y+9htQVz/NizufiPy5rmKlHm4/xmJr14dSgO3D+w4G\r\njAzkBgcx8hGhqJNKEqVTHnK1PgTac3GuxQgvnZzX3pZCgdCFZwxoUrW76hSu\r\nxQM9/e1FRFywQ9GXCV9tKOAg5w5XAuQJvt4xonMPBCXV7UJ1fHCHnwjk/lnt\r\n/6oMbRm2Y9BVzn/Pd7WuUoFovLUk9lBH+gw=\r\n=Hxu9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8f9b812faf8e4d241d560a8574f0c6ed20a89365", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.1.5", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_28.0.0_1650888482373_0.3917369828404367", "host": "s3://npm-registry-packages"}}, "28.0.2": {"name": "babel-plugin-jest-hoist", "version": "28.0.2", "license": "MIT", "_id": "babel-plugin-jest-hoist@28.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9307d03a633be6fc4b1a6bc5c3a87e22bd01dd3b", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-28.0.2.tgz", "fileCount": 5, "integrity": "sha512-Kizhn/ZL+68ZQHxSnHyuvJv8IchXD62KQxV77TBDV/xoBFBOfgRAk97GNs6hXdTTCiVES9nB2I6+7MXXrk5llQ==", "signatures": [{"sig": "MEUCIAFPLjHr+1YnaEU5iHvxVrZeZmwJl4uJ3snDtQ99Lat3AiEA8zJGM7U86Go8rrW7HmK7H5PP+8v/IAeLujrmMD+fpqU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13665, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaPRAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq6oA//TYTXMOT9022D9q+E+BYybZwoj+ho1vM5yBYCZzRAvv8t/zNK\r\n2WG48xbPypSGjbaV1KiHdkbIuBuAHobyhYuYqtB6jogeUOZdtoC9ejfGUu4s\r\nlUem5xSDuleV08u7X3DnQ2RDUMhK2rKOT8QJuZvw0sF8fJPLF8WoIFXuWrmr\r\n0fEaDZBJ7q1fJYteXQs7a4Stmn1eytwRYboCD2MBAupXqOXV8hn1ScgIHn4A\r\n1DwAIQ0xaR5tz5usCTJf6knWQQDYOkWeR5MQlsrSqR7lnzhknBnfrgsiK26/\r\nzGZD0bhHUseu+kBhhlepMvU9Ymux0/0inuByONKNg9BBzxZ5rIoZcJiuuBUM\r\nogHsKuzeKnbVCyhoOV9awm6k2AGN6+EZ4OYCh0/FU3Fjxi9WQRvsTIFCJr2y\r\npJ3mNq00/98NsoNckPEiigVVDWQPhRFzzr5VxQ/dcNS43YFvwLbeaiTlfSSc\r\nOjsAIAZu1cS6SohIziSPh4SRrxi5+1NZMwiSyGAWOSxyaDkGpdFNPq7Vlr09\r\nLdnMXbOsLUPN5UGAm3gXbMp7GvaIanyXPSgqibUUhlha1N76EukTyAZMX4ph\r\nnQ1EcWUzQAaCFsVHDGkMRy0Ru2Ls8x7oirzrq7LqeZVYulNOjL3qSKWGPnI3\r\nGaG/QTKZrHm+z9RhAa0aV+PLpa12VCgUrNQ=\r\n=ohTT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "279ee6658d763f024d51f340fab6a37c17d94502", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.1.5", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_28.0.2_1651045440038_0.35742142987592374", "host": "s3://npm-registry-packages"}}, "28.1.1": {"name": "babel-plugin-jest-hoist", "version": "28.1.1", "license": "MIT", "_id": "babel-plugin-jest-hoist@28.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5e055cdcc47894f28341f87f5e35aad2df680b11", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-28.1.1.tgz", "fileCount": 5, "integrity": "sha512-NovGCy5Hn25uMJSAU8FaHqzs13cFoOI4lhIujiepssjCKRsAo3TA734RDWSGxuFTsUJXerYOqQQodlxgmtqbzw==", "signatures": [{"sig": "MEQCIBTG+rl9+QcxC/9jBrPtw2gDaOKO86coRYJief9ncZfeAiBvHr4kaLspfraTevLgzSyQCxLYsUga1JhqBfKhYEtwXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13732, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinuufACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfzA/6AspFwHSdgRuNafcieiuAOR48TNFOw/BdccWhlZBW+mpttrLm\r\n2YsqVo23aQr3TQepO5SOfJQd4goSTTYynTWpCe4/o/z3R5H6DwXdlQ3TAsrR\r\nHoqYHaG8nawWygF2eP+pcO16O3bHPpzGgxbxMYBkJbE8VxiloFviX0enehvF\r\n1nLAbKVcQtn2PzZch6SMpoKZgG1mVsgvZ8ps8yn6Y7Pz12bVIaAxF6SWTtRB\r\nSyIeqxA2cVsxSB6FSbbzgOCaWJWI8gHOrn1PH0ErN+21YNLDC+e+pypqTiHw\r\naRyveBfk7wZPbwm7nwqFyri7clY5zpcXoHJVLbxn0EXLB88ZdEbC0fTqr5J/\r\nCfSfBQpuF+mt1pV93iw81y4lQgqpq27CE5QKhlAlqH2IXHstImu2Oy/5eEX/\r\nh5nQaguyDLifQIamI3LDQPGrJvAUMZXoWxTdLtMRiFBAMDPNWXJR0KucavXA\r\nQyl+0gEa9NtCZe2RM2AiEs2zhgr9mkGO0AVfbCmmVyUZagqAq7dPirYbvKg6\r\n60G7nqTRGaamrYx+cdG32dYcOKq9hIPwnV/I+ff8R46C5ktrwC4/l3E21NrJ\r\nYuPWaQ8fChAEtycB3wkXO4cC1ADuPklUtpNGRk8lgT1zjzEHEK4jLwq2cLmC\r\n6smkMNfUhlziCEFDV/7ZN53e3gBHvzNA7fg=\r\n=ds0r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "eb954f8874960920ac50a8f976bb333fbb06ada9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.1.5", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_28.1.1_1654582175254_0.24389327976270048", "host": "s3://npm-registry-packages"}}, "28.1.3": {"name": "babel-plugin-jest-hoist", "version": "28.1.3", "license": "MIT", "_id": "babel-plugin-jest-hoist@28.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1952c4d0ea50f2d6d794353762278d1d8cca3fbe", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-28.1.3.tgz", "fileCount": 5, "integrity": "sha512-Ys3tUKAmfnkRUpPdpa98eYrAR0nV+sSFUZZEGuQ2EbFd1y4SOLtD5QDNHAq+bb9a+bbXvYQC4b+ID/THIMcU6Q==", "signatures": [{"sig": "MEYCIQDx4WPkxoi1FF0JFNYRUeiTci7xu8Mf2sXn8RloGLuowQIhAJnyVliPVRRjN9MdGcSkpn9gopyWIOtuREZ5pvW8JSb0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13732, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiztLKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoCtw/+M08zyTWSI4uO90CmyDAaWcLXbSBqT/QmssS68m29W6kDH4O6\r\nF3AxgXTc2KLI5u2K0ouThkGPbO1iTDQUn1RX1wJNwQ99K37dmWiYIuPs79ey\r\nbbdKhuyGg2jMF79rUfXcNQQUIWdbBxhXy8WsVvCBcDL+Ayp5+Xz/m1cPLn1y\r\n3TlqhFtziLLg0Byfz+oUFkX9IfbZjbwNktUN/JS3lEgs6oA6/4HAbA92nwLu\r\nep3R2tAulUk+b+ZFcdU5LjC3liaMuZoXbRCDZ3t1bMapPgVsOfGHe7vToCZR\r\nVg20Bnk5Ruk0Lt0tCC+8hQ1B3N23JmXNBwDCfWhKjEU4gdZbpDvWb1thBD3n\r\nGfOHrSQI/2awqrijJHsAkvmt2wrR+TcjPTsDxyswTWEIbSo2hib5vosJiwF/\r\ncOqjDrdKNZLPzMw9gjwfBvjIk1dUNbByMj8LWAYDbEoyyJazzxdd3GGwG8d0\r\nf7COdbpoahfmA7sqgzQhefL5PgTwnSoOphlgXGesdnkZT6jSG02L9wwlZ+xC\r\nX+94hGyais6zjxo4HlK0wKqL4MXYAf9+wGL7r6XQQn+6cqxuMIDFIcqZyZeH\r\nOrx+vd9Bcj4KbVGC/Lqmacnm+yPttsgDoy9MhS85hTDu9ptt1jIJ+PyuGMgA\r\nbPUSQFOAKjZPMYxk7N/KF7DCcGKw5WWqqKE=\r\n=WTYO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "2cce069800dab3fc8ca7c469b32d2e2b2f7e2bb1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.1.5", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_28.1.3_1657721545894_0.9380886735636778", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.0": {"name": "babel-plugin-jest-hoist", "version": "29.0.0-alpha.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@29.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d6273ae6bd4141851cc7f39805dd4742a60d7a25", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-kT/GOxLr3JNRePWe19Oat/ekHs+VQpn1Oij41Mjg2KAdaN319VDz+LXGxIqS1Kjzzbi/QaEj2FwO6+DrPm8VRQ==", "signatures": [{"sig": "MEYCIQD1DRo1tjKu6HEW4yvE33n2crsnSleZJlmH4PM/k/zFyAIhAII4p752N4i/rOYeY8fC0PBzpFvkjWqqAeIHuveXJqDs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13266, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1IgKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOtA//Sb5RdmosPTmZX1tHUhc82Hd1AQ/1L2D97xmqgPmoJJKAQNov\r\n4lREf6Nty2FeqyS5DXATDV8gbsDc9o3diUHosKnrTj6is70G3xH6m1xWF3NH\r\noysKZmmDbiYaGFdcUYgRTFHVqAh7B/hyF0rASQecqj5j5aTgqTeTVrVXlPfy\r\nSs/yJNDYwha/noP2x3KHBojk1lThbOnGi7opQLoy3clqt21st8HVlnuzpTp/\r\nYcmVNhUlX7UbO0lR2In7BNeQ3Aomo8vBnDLGg9Iw7dksZsjNEqkQ4YvlVI5k\r\noRf2LHkLx7Ck+ONeen8hutg9hxkmDoIYyxuN/q+MIBaXET6pLnzrtvm2p9AK\r\ng+UzXOx3qPY0ReUJ8ZH8H66IBvRhv+zmJBo8Q9mgs1ObwbVKnNmt0JS4aFyA\r\nZ8nelP8Sk8BgXRN2t9PdZ4rHkVPMiWo2MfnlFK5AkNutLX8h85IEoj+eOtDx\r\nOb84hgazU5N6U9oudmlwMc/VqRQFUzmH3Hz46RfcUb3tjJE7yEga7TWz5CIX\r\nIN8z0z9AzlSpY8UE/Jdk0m+njqmT4HtqPlpuNPd5VACp0U6SdTyln7g4aN/R\r\nT6M3wIjJLixcGYq46kRtFAP2y4oX/+4hxjsoAz74SRGvRXv3a1zQuXlzGOpQ\r\no1Df+JAc/wybTUKrC0OmBtlV1Ox8jCy2XtE=\r\n=1u+g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6862afb00307b52f32eedee977a9b3041355f184", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.1.5", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_29.0.0-alpha.0_1658095626323_0.6493312266376978", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.2": {"name": "babel-plugin-jest-hoist", "version": "29.0.0-alpha.2", "license": "MIT", "_id": "babel-plugin-jest-hoist@29.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "78301b1fb23ea1bfe0523fb4e6d17847ca552485", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-Pqjbyud92/Ak1GrCWIv6JvPhQPv2yT1b9aDX34T4lUzsd+DmNKpiQoHUkxuofuLROM2v5OaUTpwwjpGfHtZXnQ==", "signatures": [{"sig": "MEQCIF3Bw95FKkEolU2wlBJaXuvqo+PWrtlQ7mJjYlqA4EovAiALaVN3xx+G3SDHhAOF6jpq1KHAR8J+DF2z8/4Mi8c2mQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13266, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7aiJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpbbhAAoEyuTs4J7dtAx3vGruCACx1A5iQSdMlwU/THwt1iCkWabv6D\r\nvmetrzlxNk/D1nEPrxtOsccVGU8DtrFAxXVTd6TNghA5oidJ6WJ9uCVC6kwG\r\nIVh2+hTfTkK+WlXfGqxYBmaPR0qf81S0MAN4WUr6s/lfM+kPW6gJb/1Y43Vl\r\nHDNR2GLM0iuY4H9EZE6uZQ8hy6L5KTqCw7Ppmd4XeouolSCgRVqNvOQXd6ZA\r\nkoGJ0IaYQr8EK3jiW1i0p/rsmyUh6S3jxAUq6WbAraNDr3NrDwbemec1UJ8s\r\n4D628M6GAM1ZQolvy/SBAkh90b8L6yGUO8hVdOlALqO91airkAjx/Xr82chb\r\nFOfvE2vb5Mk6QbLkR9wlzfDqDZgcP1ZREGCceGA+ZsmyeIE2JxKKEeiVftqO\r\nmmyQ8+fZ+lGkokHGoTz1js9Auvx8I1B6s0V25oGteC3JDKdh9vdK2B5NVTm9\r\neQ4P23NW59xkIXeBlB5dVmsEiGUbgh5mklDLkeMX4BTCVKoxAqIUo4ryH3Oa\r\nuNXNVUhV97gjbfPPsyyI6/T9Yw/bYbtkgH6qN59oppeASB8vSaKVYyeWb0pQ\r\n8uBQ9CGIrAO5LEatVSdYM/g8lLM3ANB+mPWucX4qKFPYuqrTuQNemnNBXDzG\r\nJ4QVUgrHlJZPE2OnlKkT3Bl20G9EWnXk9X8=\r\n=gAgE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "53c11a22213dfde9901678a3fdeb438dc039066a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/1.9.1/node@v16.15.1+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.1.5", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_29.0.0-alpha.2_1659742345190_0.035210611281010884", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.3": {"name": "babel-plugin-jest-hoist", "version": "29.0.0-alpha.3", "license": "MIT", "_id": "babel-plugin-jest-hoist@29.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7544706abeff87b207b3a90d52126706f34f1f35", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-fx9ij7e4Gubr4knij8Fiq/YsqK+Ny0rzEmLGYw+MnXqDr/JT01gBuRVU41qo/RkNiNiTRVbzIfimO4rZK4LIzQ==", "signatures": [{"sig": "MEUCIQCqpazIBU8H8Qz+nHY0z+EEpxWgvQF+PmV0WxXFJURzuQIgV/4q9kV+SPwpXg/N55JwiQkAqxJC0LbMGS+37IDvghY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13266, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi78EMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqTjA//Y8MdO615I0BB3ERqkMSYQyiz8RrUyVoHDbTKGZvkH+6PvfSO\r\nY5tlBXvYkXHePaacg0Www0jqTkipLoCSj5/5oarZOlY6Q1q0OfurAsKzBRQC\r\nq9+qzATZsyyaCb1NZAUgt8Ctj/CnL+fcKTx44/YYDWlO2vb5ZlyKdirBCUeK\r\nwRregUH+KbEOnNwRNRvARR/R/mdCt3pJd7dLW/wYAqHs1WTR7QuFMmo++Hek\r\n2TGC6wNNEl8cpNGEBI0LLCL7n4d2rpMIFygIWX7lc83fP8iizMH26svqeibl\r\nuPl10oniESuh1aYohO9SVsY6/aEFecA6y/4PctlA1Ij8+O/B6JabKgKckaSa\r\n3IEAPEZXnIJ+r9fvs9RThhzI8SJKut0HRsIx6yr3BuvdjcvSlhRQITc4PRpl\r\nIUvV4vyFhk9KJ793dgzJhdVn6TjRwFzOWY8KkgVCCADrNdw84V3v1rQiAdwy\r\nAGsxJpdbKEX+soqUGkJyXv3/B4X83fDrTkTIa+Of3Ja+uKJUbYa3wrfy7rXb\r\n3UmbKzLXyi2jdt/H4bE3l404WQMryvn7jhLNZgs2rCJQgUt0HOtSPxV6L2O4\r\n1ecM8Vuk9JlBJToiigfkBIUAOqi9icpcIgy+0+I1Neumo2ub45QXw2U6zY8G\r\n+M3V/4EHLv5dngGSsqGvqCRZsT42UUe62gU=\r\n=Ii4F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "09981873c55442e5e494d42012f518b7d3d41fbd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.1.5", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_29.0.0-alpha.3_1659879691878_0.8369568943533949", "host": "s3://npm-registry-packages"}}, "29.0.0": {"name": "babel-plugin-jest-hoist", "version": "29.0.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@29.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ae4873399a199ede93697a15919d3d0f614a2eb1", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.0.0.tgz", "fileCount": 5, "integrity": "sha512-B9oaXrlxXHFWeWqhDPg03iqQd2UN/mg/VdZOsLaqAVBkztru3ctTryAI4zisxLEEgmcUnLTKewqx0gGifoXD3A==", "signatures": [{"sig": "MEQCID5C11RkhTWnC5JJNLsF6Ra22LXduJ8MakS+2L4d2w55AiB6gULe0/sjcznqfy9/YOir0T6QI2Rfikfs8tOgkQIXtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13258, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2wUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqptRAAlaISF20gcPbkdhHyaw59ixZohy3y1BIR3rUyNGcKSLfv+EF1\r\nNvahHqnCPv+XmWP2FqjIFPK4BUL3f3NpoVhUU5lo7ppBDBaSIu1dhtsIYtil\r\nj2EyE/yTW6HVvSTxm/tm2qtdH+StH70ei8HQ4wF2v4cbphOOwo/Qo0EpTNvJ\r\nmsw/8KbMzaG5UvskxpkRtuiR4OSVZ/6rdvaXBuaVj8ggiYuCUO+cH9GmUWjI\r\nygWmAVFvDSQEAIIu0yx7NRxI9X1ZHpnkNU7Bx4yjcO5bQMY/5GXCRkpzsIwt\r\nB733aiHIKL+XJBnW6DArhbpJHthNjHdH7FrjNGpQH0/9HrHScDMsjUqrrJ7X\r\nJDppDzM3bAA+htmNp/IKi9MAVkLPn9ZhPuS+4F1vUVkgB4PCUorcIyTMw/3s\r\ng9HFIGlzl5wO2oJfLGFFZORkpATVuiqQPUPo3bNnW1KODDS0djG6pAQzLyo2\r\n6m5HLRxq6dWvbXxF4SKakZUZmtedwLU3n3p9yOTY3sejUDjTmxog8JWfZwTk\r\n3jNaWTWCl7Y+K/ropJhMSoUYqfKOAJ6RkEuGVrcrTmACtvUeCePRQLuUYQb3\r\n6K83b8qzTdJsv0a5rpSt3u43ylgEfPHod08Z6Kipr29WWm70f5dl4vgyMgfI\r\niPc6Z844FxJMrKjYuEh5e1p6nkKk1Ohj+TA=\r\n=CqP4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "75006e46c76f6fda14bbc0548f86edb2ba087cd2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.1.5", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_29.0.0_1661430804484_0.3650217102108797", "host": "s3://npm-registry-packages"}}, "29.0.2": {"name": "babel-plugin-jest-hoist", "version": "29.0.2", "license": "MIT", "_id": "babel-plugin-jest-hoist@29.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ae61483a829a021b146c016c6ad39b8bcc37c2c8", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.0.2.tgz", "fileCount": 5, "integrity": "sha512-eBr2ynAEFjcebVvu8Ktx580BD1QKCrBG1XwEUTXJe285p9HA/4hOhfWCFRQhTKSyBV0VzjhG7H91Eifz9s29hg==", "signatures": [{"sig": "MEQCID+9OEa2FiDQ2kJOo+pHQuXSDdLUb9XqSmsaAea+bfpxAiAHXTGMpgTTuzvD5vRr6pcawkBzKD86JIgcvmqG9HrlHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14304, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEzDxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpf9A//TZibfGGbCSe4W0SorkWbWuTcECxi7mb3W4XNAuSUlgUtwA2G\r\nvzyM9OBFwomsjhLCSM9AFkBQM2qR3+Ta4OxwSzinzM64IVGRz1tfbToOy8CW\r\niXgcSJaoA38WuJrA4sP5pnqoYDyseLLgc9hXQbr5UTGEoiaxThQkG1UsY/0d\r\nyfPTsWryS3nFT/1N677GARciv1q/quONjUfcGWSeOwUj2f22qSpQYyscB54Q\r\nmWaRJHDoMjE4Qz8hvUiWxuRrxKxO0d7y2KDdCVrNgl4j1sXN2hf+aIijPxnl\r\nwOTEDX0uytrmILSnp4KCzkY0ozYvZpkF4UwBDd8vQearlpYer/hIEJGCiX1p\r\n8bKXYf1SjwV2O8WOadFK81dglAIZ9KCMRhdr5r3X5rS77fSskG+3sVeIGuv5\r\nuX/WAkcqeQwQeRbOYTj5WDPJjnxTegOvGko9ngrdbLkB4G+kZjfnzgTk3lBa\r\ng+aE76/9QrIVzDZa0NlsPehE2ooifdL0PRiAXkJJTsggzvywfqRrUMcAux7P\r\nBLhw768sP+8kZqOX1k0MYGIVYugyRoik+X/J1+0WV8OcODw9QOt9OL6y8cHu\r\nNJ2kCQfClTm5Su93NFODF2sKwFv7Ihk7REZnC7ywT56W3hpR1xh1brdcemz2\r\neZR5BnMmOwEhfWMPUWOigrfeazs9yzxz14k=\r\n=T3n3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "616fcf56bb8481d29ba29cc34be32a92b1cf85e5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.1.5", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_29.0.2_1662202096948_0.23205942078234498", "host": "s3://npm-registry-packages"}}, "29.2.0": {"name": "babel-plugin-jest-hoist", "version": "29.2.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@29.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "23ee99c37390a98cfddf3ef4a78674180d823094", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.2.0.tgz", "fileCount": 5, "integrity": "sha512-TnspP2WNiR3GLfCsUNHqeXw0RoQ2f9U5hQ5L3XFpwuO8htQmSrhh8qsB6vi5Yi8+kuynN1yjDjQsPfkebmB6ZA==", "signatures": [{"sig": "MEUCIFPYhDMoLtURcKs3XQSiKsvvc1YX7LHeLG2LMAPAsNA/AiEAtoYhRK5je8jVOLbGtEkjbD00L9psgEPyfomlNBQ0GOI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14380, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSShFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqQNg/8CNhMB7T9ouPmQaYCHInbfp8OFXnLkI6Pzpl5XTZ0WVnHMrdH\r\nH79IJkDnfAsAOrV9B7J7U5p2kFtqpXr14QDTLJYMubwiMW87PSAXe7CzT0gk\r\n67xaF248p7czYCiNMnpVBMbHiAHoWX/CYzDHYM5kffiHOjsYdSuTWcTNneg5\r\nWZrR4+Z7mTxDc8QPZpAjidfDCe7Hc31xnk6479vIdUsQESLQHaEJ8pLBMpCQ\r\ng0AZvosEvW5j0RtCPPHIVaC6oGK487MxiExWZCs3WVzu/eFT7loOqi5T7+3M\r\nV4xrX9ZLMzDJ2aMQufZ8MjEGXFKlYuloFmbJWPjc1yh93viDQKk5xtodwswD\r\nHyDE2+UgfulUU/xLQd7LRx44Qu3Ug0hL/qdAMEKONGIVcZ0S+wwDN3LZLKyI\r\n4wFuMsbjKbWySAjxrztGRPlStQE3xAIh572Uca9neyXGBcEWgYmVIP3y4ZAh\r\nIO8HTfeLkuRtXNGmElpjr1lFslESeE0G+/02Rv1hUME98kmyktpXUdaeydxE\r\nWkZvdVbMeed1EEQPkwEjoZCBYZI3b/YH7yX0ZwH+D5kj3gfqfe7IIujvaHkb\r\ndqqY0Lagud5JTvuDYI4YXVuGCesxbhFW7z/e7QSyEUR9J2jFGNxFCjEER8JF\r\njRPLvteycVowyMPCDJRZDlUB9OD6dM81uIQ=\r\n=A39f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ee5b37a4f4433afcfffb0356cea47739d8092287", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.1.5", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^10.0.0", "@types/babel__template": "^7.0.2", "@babel/preset-typescript": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_29.2.0_1665738821143_0.8227545942284056", "host": "s3://npm-registry-packages"}}, "29.4.0": {"name": "babel-plugin-jest-hoist", "version": "29.4.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@29.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3fd3dfcedf645932df6d0c9fc3d9a704dd860248", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.4.0.tgz", "fileCount": 6, "integrity": "sha512-a/sZRLQJEmsmejQ2rPEUe35nO1+C9dc9O1gplH1SXmJxveQSRUYdBk8yGZG/VOUuZs1u2aHZJusEGoRMbhhwCg==", "signatures": [{"sig": "MEUCIGbGRg6E6LeXayKeK3g8LgK+6ZkGPWa6D0zIPM5HkLwmAiEAoB4Rl94Ti92GjccwMLdU5uhS0r4W+nzO+lpqGpNYWKU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17502, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjz7kvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrWrA/+MSUez0e5Yvk28OV12eIfwSzm3cg85ZKrdCDT3g2PL0HCzrNV\r\nn6r7qu9B+a+2hVW8MoSvHzhygfhnyommsBiPZM8APsndfGlzNNTlzmFdNC8S\r\nChNXbTC+o49pXGawXwZR4lr7+CxirgAb2NhwZXjXoSeQYENiOo7TFqybXJPV\r\nFwPEtd7AgjzOHKpaylnrwPpogBTT+3kqpf512Q9sJkSA8QvWIDLYOAzmmQrc\r\nTcLuKxzRBv7I7sFH6zEe8oyX4JEAM0OHCCvE06Ofee3BDh5gQbXJFAfifi2G\r\nUrFGhH3bKARlSz0/a0SPQqMllutmlwFnCSOaWgAVPQUyR7xoAPUepEqJgn2D\r\neCQP2QXjT02hGqHhwcZRLR2eWy4pZdT4ZaEMvf5/7a13aLae+JchnUVJeMbd\r\nPcYnATRF/7mRh005igDQtymSm1FMoKTn9WSCuoIs3JPpm5CNR2IMZCJFe4cJ\r\nGjIb8crOUGpKzq13w9hsVWWUKUogyilXrEQQwCWxrblXduxorNX5jDM+xPig\r\n4wzzww98ADxj3zbvOsNubQqfyn2JRcc64ZTE+MPCao5lzg/IJRpUBJ+gtEIP\r\nEnKg/QSGPXwu4V6xuX6BgBjA2K1UaMG9IU+O0FyM5gBPcAKZql6KJM2yVe/2\r\nai4FCh+sp7pNdbdpl59sKPLBT/iqffwOyTY=\r\n=hMxy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4bc0e8acaf990e6618a7bed1dca67760c20bb12a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.1.5", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^11.0.2", "@types/babel__template": "^7.0.2", "@babel/preset-typescript": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_29.4.0_1674557743273_0.7239695320428021", "host": "s3://npm-registry-packages"}}, "29.4.2": {"name": "babel-plugin-jest-hoist", "version": "29.4.2", "license": "MIT", "_id": "babel-plugin-jest-hoist@29.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "22aa43e255230f02371ffef1cac7eedef58f60bc", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.4.2.tgz", "fileCount": 5, "integrity": "sha512-5HZRCfMeWypFEonRbEkwWXtNS1sQK159LhRVyRuLzyfVBxDy/34Tr/rg4YVi0SScSJ4fqeaR/OIeceJ/LaQ0pQ==", "signatures": [{"sig": "MEYCIQCjOKwMABapKOvBEGMaMyCXOsS8PzQXWjbSEW9R89tncAIhAPT7cQjwL7NiNGm8aNVe29dskcykI0r2Gl0dwhszrxlM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14380, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4lXxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpxBw/6ApV7iweMpKjA4rIgEoiVUk9HUYbfmtBKWmqQRwA8tCfzPSDT\r\ncTxinZeu4u8eHOcIXwX+YHGQy6h+Y5Szu2HWSG1blvhp+zAMnTM27l/NYAlb\r\nXsgbKTVNquwBmsqj94BnbPojpNPkFe5qQC1QRRKvm2fzc5NgoCW+ZAIiXiqS\r\nKJErGaHutTHVacwOtCvfEz1C1RdXUxz9N/+kxpvie7j2xky8TYJwg5vfR90B\r\naQIgoxIlJL1nRcdDWPZwyoNVj7wUSzMGE/PC0vVsvLNCFbw5gYUfYVspX2l6\r\n3CKeG/gJPO659lH8IbyW42XVUzc0dLAuc/+yhTk0icg2pH8nPsD6IVEia9yP\r\n8oRJDpvqg8tr1npdRei/1+SmTzpApAhEvpWVLnZPmElUby3lIdzW1cyerYN/\r\nF4pRznG99QJUssIBWt5niU6idJUx7RCJYKwas067FckuDTgSlHy/yUncQpGP\r\n14sqhMyqNtZcovU6JEmlsK6BDCXcmhX5rQprTIuWFjThWO31tnwZZOr1qnUL\r\nljvQuQxIxGZt3dMxKAxWJB1DMMRFLJnDxqBs1NflL0ShRON8NdnV0ETWG9lm\r\noveQ8BucCxtce/at/rcAVpOVeCZtobO4Oviw2DjW3sU3ziirIjrAcWfzLzXF\r\nzxVMdG2TMvam+lB7WevRW57XVTdm2OE1rJA=\r\n=kD9i\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f0fc92e8443f09546c7ec0472bf9bce44fe5898f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.1.5", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^11.0.2", "@types/babel__template": "^7.0.2", "@babel/preset-typescript": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_29.4.2_1675777521753_0.5158626994352844", "host": "s3://npm-registry-packages"}}, "29.4.3": {"name": "babel-plugin-jest-hoist", "version": "29.4.3", "license": "MIT", "_id": "babel-plugin-jest-hoist@29.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ad1dfb5d31940957e00410ef7d9b2aa94b216101", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.4.3.tgz", "fileCount": 5, "integrity": "sha512-mB6q2q3oahKphy5V7CpnNqZOCkxxZ9aokf1eh82Dy3jQmg4xvM1tGrh5y6BQUJh4a3Pj9+eLfwvAZ7VNKg7H8Q==", "signatures": [{"sig": "MEUCIEUEywu8dIJlggvVM35kpLz5S0Axhhnor5oFDZbHck1nAiEAkl8QjJu2VHac1sJOMXXBcgP9T20MIuNt8cmy8OYASfQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14344, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7MicACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrmxRAAoNPq7g6zkN2XXAFa7NtrRV+jdlmlzfL+Tm4tZQd/QBF6O5qY\r\ntdZffLY1w56QVg9DT8S0u5bJa5x9ox0hRBLaxO56N2KlCIVRjFjn19FbYg1u\r\nXToPIsoD3e9flX6BW4zwrirWxU8GQmTjNsMteqOvYORWiBFWCPK0LHVZc95U\r\nlWD6X0BLZ8z2o8H0rhvuIAp6iZOwaayPZNlW+QaITuPk3NUKv8UpE9urcmxo\r\nqeGjFxoaw02DT5Ymn+K5wsi9oeFIhfbUzAo+Xm+kv4Sl3EluBIdVZqMVAFqu\r\nQ5QBm26UFMPXQEr+CRSx5KELdAd7FClUhOY0w3ojzo5BfHHT18u1jwB6gsAF\r\naA+frFssyGtfPqFOcVv7bMf/t8ihetZ+4xuECjxZoqqVwYpKG/c6DkUpn41C\r\nKDPUyV94FtotclJdKnYfwCPW6Yjg78EOfxqThWKaLeUnAWvjSI6EHbg1q/S9\r\nk3ma+G5jCYTFK+zGKRVmCxIEmS9C8PkJF7k7c/2XVkLLBfN8nTsQovSDIy8r\r\nW92eChNAG/MV9sBvY1aSJXQj36w5PzqYFVV5wHqayf4awLpWnvjh91LHu1mh\r\ni+NY4RuzYQTPcSg1a2ID4mdzRtH+H6fWBkNb3qTFCZHCamwflbe2iPODl4E+\r\nmHO222KuZG5SyoZqT9hQzytCZHAjNoaKb9o=\r\n=6ZiR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/1.13.0/node@v18.14.0+arm64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.1.5", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^11.0.2", "@types/babel__template": "^7.0.2", "@babel/preset-typescript": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_29.4.3_1676462236363_0.38646419373043894", "host": "s3://npm-registry-packages"}}, "29.5.0": {"name": "babel-plugin-jest-hoist", "version": "29.5.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@29.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a97db437936f441ec196990c9738d4b88538618a", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.5.0.tgz", "fileCount": 5, "integrity": "sha512-zSuuuAlTMT4mzLj2nPnUm6fsE6270vdOfnpbJ+RmruU75UhLFvL0N2NgI7xpeS7NaB6hGqmd5pVpGTDYvi4Q3w==", "signatures": [{"sig": "MEUCIE7iUPP1TW8AxANPU6VlAVzKrFs1faC68sZrAzhGNFv7AiEA6H33Qd3MFG1CCiLg6n+FnxOlEpivaqTvz1IhCRHL6/k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14335, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBeujACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqFHg/9Gu6XlMihKpbR1FFpSZ9ZNApvVJ8QuNM/gwQIFOoOWPOodf/y\r\nDAHUDw619tbpekYvofe+1DzPWkdjoKQOvB3kns6E74704VjTgCbqG6gFa+za\r\nvjDOkShaYZ5l4EJuZiwGEEyhUbf3TqXOWgGm7IvOrHyK0+ID0GpeGM9HaGPy\r\nw73cTbCUc1Nzaizd3FVb00CYg76l+2a+Hykidin7s38AvHlJdiTzKypBHjOU\r\ntAAyQcPCcc4esejEQVBe+uf92xxYPsh3gCfA1+rjQ/N3vDjOkm9tAXWxD1+G\r\nwXNiivEt6+XnNgvyeb1IyPrHclmG2ObLzidSY4cU/OZEKEbqaQMPYLqImr4V\r\nXkLvOakaNgeRfGVIPSC8PWu57WGvW1eZzEmjtYJ2G9dbsF/tjLYPJrNEoEUH\r\nCwcX7hbyDD/7aS3qXRZTE6SnWBzXD7og4t31ks1vRYPX3GVEvyIdtvFjE69R\r\nYc36DoHpHSzlPjVVcJcb7mP3yxcsdAL0cu9P6c0I/+ozESeD3C1IvjDs4UO4\r\nJGCHQx7wpJHVMmR5Rl7RdtiCV8cd/xWNl828j9vJgaBQOxUCezoBhnP37tg5\r\nic8NDThL+MWxssecbnNehBDuauXe5t2mmiTwkH1qa/8HTePOf36d0TmczSSX\r\n93JNzcQZfM3c6vjWqCeMtWhtbMxtlRw2NKk=\r\n=KwBW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "39f3beda6b396665bebffab94e8d7c45be30454c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/1.13.0/node@v18.14.2+arm64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/facebook/jest/tree/main/packages/babel-jest)", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.1.5", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^11.0.2", "@types/babel__template": "^7.0.2", "@babel/preset-typescript": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_29.5.0_1678109603777_0.2466541710272927", "host": "s3://npm-registry-packages"}}, "29.6.3": {"name": "babel-plugin-jest-hoist", "version": "29.6.3", "license": "MIT", "_id": "babel-plugin-jest-hoist@29.6.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "aadbe943464182a8922c3c927c3067ff40d24626", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.6.3.tgz", "fileCount": 5, "integrity": "sha512-ESAc/RJvGTFEzRwOTT4+lNDk/GNHMkKbNzsvT0qKRfDyyYTskxB5rnU2njIDYVxXCBHHEI1c0YwHob3WaYujOg==", "signatures": [{"sig": "MEYCIQCX5KqWECUFYX7uCHQO0QnkHRIvwOM7Mb8HiL6rRwb1LwIhAIFuoWIOdEGWfekQks9f0d/G4tIoVD1xrwFvASKTUMgh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14331}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/jestjs/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^2.1.1", "@babel/core": "^7.11.6", "@types/node": "*", "@types/prettier": "^2.1.5", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^11.0.2", "@types/babel__template": "^7.0.2", "@babel/preset-typescript": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_29.6.3_1692621537600_0.9572279483913795", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.0": {"name": "babel-plugin-jest-hoist", "version": "30.0.0-alpha.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@30.0.0-alpha.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "7317b7d985c6738c531c7f292b3d44e43f04653f", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-30.0.0-alpha.0.tgz", "fileCount": 6, "integrity": "sha512-ocv/uHNujEfttPTzIOUjrgewTeANJWEjkSeMLx4zmx+HoVY9zA67KE8INFkcVd3R6ijTMdk49KuM4pmvDoN1ZA==", "signatures": [{"sig": "MEQCIF9+paGqAsJR//xwH4QxKtfXyCybzF4uvOgkIm4s1DqZAiBYlfv92JeOgTpRZG14vp6hQbJlIqWu6Sd7W1LlVR/Icw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14355}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "780ae28333df4d188b2ef78bd19d4ed5bc53562d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/jestjs/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^3.0.3", "@babel/core": "^7.11.6", "@types/node": "*", "@prettier/sync": "^0.3.0", "@types/prettier": "^2.1.5", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^11.0.2", "@types/babel__template": "^7.0.2", "@babel/preset-typescript": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_30.0.0-alpha.0_1698671619543_0.8164418113404113", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "babel-plugin-jest-hoist", "version": "30.0.0-alpha.1", "license": "MIT", "_id": "babel-plugin-jest-hoist@30.0.0-alpha.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "5a8e15f2aa0a0b46a69c95f320ebcfdbc0432ef9", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-30.0.0-alpha.1.tgz", "fileCount": 6, "integrity": "sha512-iZO7dT9xglQAXmeTd3fw1FVWqBlfELQ1I/nIa6nS3yl8/tl6e4JeEeaHuXm1/7xKt2SlAMoXkr69Ywpt+SBdZQ==", "signatures": [{"sig": "MEUCIQCO3wc6o9i/D/lsKmHeM7Cn1OkFJf5cbdXoT0khF5IFrwIgfIClGHfFG5Qp4E/E6SdaXWjTK5p7d2DPGwja0Bphb6U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14355}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/jestjs/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prettier": "^3.0.3", "@babel/core": "^7.11.6", "@types/node": "*", "@prettier/sync": "^0.3.0", "@types/prettier": "^2.1.5", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^11.0.2", "@types/babel__template": "^7.0.2", "@babel/preset-typescript": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_30.0.0-alpha.1_1698672765949_0.422489108631938", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "babel-plugin-jest-hoist", "version": "30.0.0-alpha.2", "license": "MIT", "_id": "babel-plugin-jest-hoist@30.0.0-alpha.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "fc883e04f0ccebc648e4181c86c5bd354fe939d5", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-30.0.0-alpha.2.tgz", "fileCount": 6, "integrity": "sha512-53zZih7ZKAFp1ZFNQc9T86SyNnD47FosYc2q9wVL0MZSMVL061lRtP7+u8MZJrX33drFP/2dVzHD60j2t0bBQA==", "signatures": [{"sig": "MEUCIAKGgfQgGXYwZ1diyGh58f033KetaGNNKVgNskCDn7YCAiEAkdvVRYWHVW2cH6z9FTFNb3/o5sK3g9hox+IYMiJF0mE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14351}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/jestjs/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prettier": "^3.0.3", "@babel/core": "^7.11.6", "@types/node": "*", "@prettier/sync": "^0.3.0", "@types/prettier": "^2.1.5", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^11.0.2", "@types/babel__template": "^7.0.2", "@babel/preset-typescript": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_30.0.0-alpha.2_1700126894335_0.6170714722449764", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "babel-plugin-jest-hoist", "version": "30.0.0-alpha.3", "license": "MIT", "_id": "babel-plugin-jest-hoist@30.0.0-alpha.3", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "0bd6f3e6777faa13944b236c6a353b5d2e3dba62", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-30.0.0-alpha.3.tgz", "fileCount": 6, "integrity": "sha512-XIy+DV/1gpXSdJcSiNhq10+MKpevarPun90sW4Ch/1wFyma6nXsIk8Nwax7GYwz0mLTand+bY59I+DBfBi8n+Q==", "signatures": [{"sig": "MEQCIEFz27haYfCSRwFB7oeBuRnfFTkwcwBNGCDlOeyj69IFAiAyOEp76YZPZmmrxgTAttYDZdurHDM1798lqe+2Je4tdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14097}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/jestjs/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prettier": "^3.0.3", "@babel/core": "^7.11.6", "@types/node": "*", "@prettier/sync": "^0.3.0", "@types/prettier": "^2.1.5", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^11.0.2", "@types/babel__template": "^7.0.2", "@babel/preset-typescript": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_30.0.0-alpha.3_1708427327866_0.7401342296536153", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "babel-plugin-jest-hoist", "version": "30.0.0-alpha.4", "license": "MIT", "_id": "babel-plugin-jest-hoist@30.0.0-alpha.4", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "7881b3bef3b9ce21d8c7248ef146ab91dfc63964", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-30.0.0-alpha.4.tgz", "fileCount": 6, "integrity": "sha512-VN6UBGa+rFMyLoEpMfhi59ryOQWp+RFCWAr3Vt3TLhPEVxFxP2BruYxyBBa3TtHfHIyz0YIL1FdCvRj5BA7NOQ==", "signatures": [{"sig": "MEQCIF7trM2OI7+ohEDq01wNhga8VEfJ7A0MI25p6moMdTzqAiAcJsGN3yNFh2vFIGeZ2lFlNBke07sowbW28Yow3R2gOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14108}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/jestjs/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prettier": "^3.0.3", "@babel/core": "^7.11.6", "@types/node": "*", "@prettier/sync": "^0.5.0", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^11.0.2", "@types/babel__template": "^7.0.2", "@babel/preset-typescript": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_30.0.0-alpha.4_1715550194279_0.3825145380834123", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "babel-plugin-jest-hoist", "version": "30.0.0-alpha.5", "license": "MIT", "_id": "babel-plugin-jest-hoist@30.0.0-alpha.5", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "acff7ea29bff392632aa7b2efffff8a7b2898559", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-30.0.0-alpha.5.tgz", "fileCount": 6, "integrity": "sha512-Vx32ynouoDRR85S3giKaEfYrZ3Nc73XfCITi4EftNQ16cY/jOPyldrJHIwye79WjTIUj/+bhVgBRZALDYx+Ncw==", "signatures": [{"sig": "MEYCIQCFHIzhZqr8kPnTGIJT5/LLO5R9kgtILIg9+bdKK7uUHwIhAJoeVjf51ffOJANntUNlV3EtFSKk6iFPd1LwEhQ70zzA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14108}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "Babel plugin to hoist `jest.disable<PERSON><PERSON>mock`, `jest.enableAutomock`, `jest.unmock`, `jest.mock`, calls above `import` statements. This plugin is automatically included when using [babel-jest](https://github.com/jestjs/jest/tree/main/packages/babel-jest).", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"prettier": "^3.0.3", "@babel/core": "^7.11.6", "@types/node": "*", "@prettier/sync": "^0.5.0", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^11.0.2", "@types/babel__template": "^7.0.2", "@babel/preset-typescript": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_30.0.0-alpha.5_1717073032906_0.1740603606717619", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "babel-plugin-jest-hoist", "version": "30.0.0-alpha.6", "license": "MIT", "_id": "babel-plugin-jest-hoist@30.0.0-alpha.6", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "296ea19eced1565b15ed41d49745c601330f386c", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-30.0.0-alpha.6.tgz", "fileCount": 6, "integrity": "sha512-e/aPv0pmnvJqXM5SfCBpyMwZFEZrKW1Mb4unwTkxewk6/0TjwBk6l3B3F9H9OKZ3ErhkH4b+Epd3IIM5E53I2g==", "signatures": [{"sig": "MEUCIQD5q1mOfbi/+YN5j0lFbG7jYarWO18bxg6kHDbanu7iYAIgOaD1EWmsm4KT1LBrb4zuFySkYk3X3ZZmRhOsQ1HIlno=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14108}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^3.0.3", "@babel/core": "^7.11.6", "@types/node": "*", "@prettier/sync": "^0.5.0", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^11.0.2", "@types/babel__template": "^7.0.2", "@types/babel__traverse": "^7.0.6", "@babel/preset-typescript": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_30.0.0-alpha.6_1723102976049_0.5568158287060647", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "babel-plugin-jest-hoist", "version": "30.0.0-alpha.7", "license": "MIT", "_id": "babel-plugin-jest-hoist@30.0.0-alpha.7", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "105b5ca28740a7d1c200177ec8b939e7c9dc6eb2", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-30.0.0-alpha.7.tgz", "fileCount": 6, "integrity": "sha512-1EGEprSFBDxHdQvnkkklxdcmb6+WssLMTQU3FyzrBVUuzXOg5+D+Z909gGPZ4bhZ9VkrDGcNg08m6OjL2jh6Bg==", "signatures": [{"sig": "MEUCIQDPMjN+1NSkX7sAgEKdDNthhND9vV5HwrNxKLeUZ6kHWwIgIP+z+Nb0US36EvPjiSBue/E1rFpd1QlX/eec0lDwNqs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13945}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^3.0.3", "@babel/core": "^7.11.6", "@types/node": "*", "@prettier/sync": "^0.5.0", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^11.0.2", "@types/babel__template": "^7.0.2", "@types/babel__traverse": "^7.0.6", "@babel/preset-typescript": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_30.0.0-alpha.7_1738225702387_0.34769881245130607", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.1": {"name": "babel-plugin-jest-hoist", "version": "30.0.0-beta.1", "license": "MIT", "_id": "babel-plugin-jest-hoist@30.0.0-beta.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "d55c5a636653257a5d3eff433a50289d7a74cdf3", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-30.0.0-beta.1.tgz", "fileCount": 6, "integrity": "sha512-Mel9hWZIjOY4BdBefYimcSrIMx9QB1/yqxYZUiHWKadeOdeeRt5Z7yjzBRqe22puQOMHHDX9u63uVyAIiy4L0g==", "signatures": [{"sig": "MEQCIELQqQKDhs6vBoQO4ACkU08K4QZla/GbdOtHF5hkzda0AiARNkbCXc2ztR0AElQ0QYjKGHjQQDA2Ajk7aAZ+KB6wxw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13932}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ca9c8835e3c74ec17450cac43c7cd3e8bb5747b2", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^3.0.3", "@babel/core": "^7.11.6", "@types/node": "*", "@prettier/sync": "^0.5.0", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^11.0.2", "@types/babel__template": "^7.0.2", "@types/babel__traverse": "^7.0.6", "@babel/preset-typescript": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_30.0.0-beta.1_1748306894985_0.8638919887564536", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.3": {"name": "babel-plugin-jest-hoist", "version": "30.0.0-beta.3", "license": "MIT", "_id": "babel-plugin-jest-hoist@30.0.0-beta.3", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "81192a841988e214eb88fbe0f6aff4f4d43501af", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-30.0.0-beta.3.tgz", "fileCount": 6, "integrity": "sha512-phSBX46tzCw+6KB9lUuYzyjq16nCRYntYvsDNOx5ZXSGPBcEGbe1mQI+CgdmKUKDD4+o/NDYlvDaQSB3UaSVSw==", "signatures": [{"sig": "MEUCIHTN6IC4Zii2XH3F5WzN3TYg6CiK9Mw+a9OQZ69ZTdDcAiEAgv09IKM8oR+D/vCc5lYBvCD/OUUNnbqcjfhAqrAdAV8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13932}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"@babel/types": "^7.3.3", "@babel/template": "^7.3.3", "@types/babel__core": "^7.1.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^3.0.3", "@babel/core": "^7.11.6", "@types/node": "*", "@prettier/sync": "^0.5.0", "@babel/preset-react": "^7.12.1", "babel-plugin-tester": "^11.0.2", "@types/babel__template": "^7.0.2", "@types/babel__traverse": "^7.0.6", "@babel/preset-typescript": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_30.0.0-beta.3_1748309255367_0.3278370897106142", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.4": {"name": "babel-plugin-jest-hoist", "version": "30.0.0-beta.4", "license": "MIT", "_id": "babel-plugin-jest-hoist@30.0.0-beta.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "603bd4eb45338a087b809b344a12d0375f6afe8b", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-30.0.0-beta.4.tgz", "fileCount": 6, "integrity": "sha512-f5HoxK7v3dHpKkDu+M7+EsbXEEBpRw+XeekZsJCOhqa21kYZzOEViGQ2spcvqzwnoSpy5ve6ZDSmSJNiqICbpw==", "signatures": [{"sig": "MEQCIFdeqxo9WL2l4BEBrdY3XceljXKkmVAyMNsVZ1qMjmOWAiA5Bx164aW2/H1OKfQ4zksJL4XIN+/1UYaOT/8WsMU1Mg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13935}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "69f0c890c804e6e6b0822adb592cd00372a7c297", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"@babel/types": "^7.27.1", "@babel/template": "^7.27.2", "@types/babel__core": "^7.20.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^3.0.3", "@babel/core": "^7.27.1", "@types/node": "*", "@prettier/sync": "^0.5.0", "@babel/preset-react": "^7.27.1", "babel-plugin-tester": "^11.0.2", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.7", "@babel/preset-typescript": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_30.0.0-beta.4_1748329464951_0.0775919358825421", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.6": {"name": "babel-plugin-jest-hoist", "version": "30.0.0-beta.6", "license": "MIT", "_id": "babel-plugin-jest-hoist@30.0.0-beta.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "4fab67d174f93b9e6fe7ba2150880f488eeb7887", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-30.0.0-beta.6.tgz", "fileCount": 6, "integrity": "sha512-NDAqA44YBiC/E20kFJfkF9OQIsdJ5lUYfQx7vQ/xyLXEDNOHnH/mHwPfKpwwP6K0BPslXETfv1h9NcreqyUmyg==", "signatures": [{"sig": "MEUCIQDUG9iad0CB+c48RlWmRh145BZ/IOFDSdkmF+XF57IiOgIgZY1qMwo70XyLFJKJmum30tHTFaBAhVpVyc+Ro7jioas=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13946}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4f964497dc21c06ce4d54f1349e299a9f6773d52", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/3.12.3/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"@babel/types": "^7.27.1", "@babel/template": "^7.27.2", "@types/babel__core": "^7.20.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^3.0.3", "@babel/core": "^7.27.1", "@types/node": "*", "@prettier/sync": "^0.5.0", "@babel/preset-react": "^7.27.1", "babel-plugin-tester": "^11.0.2", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.7", "@babel/preset-typescript": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_30.0.0-beta.6_1748994636965_0.3648705329480748", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.7": {"name": "babel-plugin-jest-hoist", "version": "30.0.0-beta.7", "license": "MIT", "_id": "babel-plugin-jest-hoist@30.0.0-beta.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "7982d13ca911a2e3551143c66ad057df0280bad6", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-30.0.0-beta.7.tgz", "fileCount": 6, "integrity": "sha512-RlcVKuRSoBWn2cEmaC2qNHG1oliWoPhgZDoRUxQqj41Hu7qEQZvScWelzmcYg8oGM+PnQ2TdzsKi1Ta56AI8qQ==", "signatures": [{"sig": "MEQCIDDLvA6TGwz57i1it1n98Kzk4Q/oiRHqwyegiuGTryx0AiB45rpnXNjb2wWvT1ZW2aSR/qCeQDU3CBUVDBtu3rYZcg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13947}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "48de6a91368727d853d491df16e7d00c1f323676", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"@babel/types": "^7.27.3", "@babel/template": "^7.27.2", "@types/babel__core": "^7.20.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^3.0.3", "@babel/core": "^7.27.4", "@types/node": "*", "@prettier/sync": "^0.5.5", "@babel/preset-react": "^7.27.1", "babel-plugin-tester": "^11.0.4", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.7", "@babel/preset-typescript": "^7.27.1"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_30.0.0-beta.7_1749008134471_0.5614360138593668", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.8": {"name": "babel-plugin-jest-hoist", "version": "30.0.0-beta.8", "license": "MIT", "_id": "babel-plugin-jest-hoist@30.0.0-beta.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "914b3e0b454617b680fcd980e17a20ec45691001", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-30.0.0-beta.8.tgz", "fileCount": 6, "integrity": "sha512-I/cNDZxc2FqVsYS6wwsdHmFHqPQ+6gyGxRijlv41r/fxHsMrFl5rZrNeD7MNYTMO8NLBuBzGwJKWdACol/AQMw==", "signatures": [{"sig": "MEQCIEBEkTFUBW2OCa+GQ5oRBvR6mc1yjz8cEoHutGHcp1amAiB8lhYb/kZml0xXFSzoFiaIYLthOB9bsVJk0dBzfxzgPw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13947}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ac334c0cdf04ead9999f0964567d81672d116d42", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"@babel/types": "^7.27.3", "@babel/template": "^7.27.2", "@types/babel__core": "^7.20.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^3.0.3", "@babel/core": "^7.27.4", "@types/node": "*", "@prettier/sync": "^0.5.5", "@babel/preset-react": "^7.27.1", "babel-plugin-tester": "^11.0.4", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.7", "@babel/preset-typescript": "^7.27.1"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_30.0.0-beta.8_1749023581484_0.13183655670063477", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0": {"name": "babel-plugin-jest-hoist", "version": "30.0.0", "license": "MIT", "_id": "babel-plugin-jest-hoist@30.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "76c9bf58316ebb7026d671d71d26138ae415326b", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-30.0.0.tgz", "fileCount": 6, "integrity": "sha512-DSRm+US/FCB4xPDD6Rnslb6PAF9Bej1DZ+1u4aTiqJnk7ZX12eHsnDiIOqjGvITCq+u6wLqUhgS+faCNbVY8+g==", "signatures": [{"sig": "MEUCIB+oxyjZ+0eHo71x3LjxiV81mObsMFsMjo0DrlelL5pxAiEAihLHyXR6VUE0LzlUXsQtxl0WV+0IjaSzF+bb6ZhSEfM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13940}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/babel-plugin-jest-hoist"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"@babel/types": "^7.27.3", "@babel/template": "^7.27.2", "@types/babel__core": "^7.20.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"prettier": "^3.0.3", "@babel/core": "^7.27.4", "@types/node": "*", "@prettier/sync": "^0.5.5", "@babel/preset-react": "^7.27.1", "babel-plugin-tester": "^11.0.4", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.7", "@babel/preset-typescript": "^7.27.1"}, "_npmOperationalInternal": {"tmp": "tmp/babel-plugin-jest-hoist_30.0.0_1749521739653_0.07544382595723831", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.1": {"name": "babel-plugin-jest-hoist", "version": "30.0.1", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/babel-plugin-jest-hoist"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "@types/babel__core": "^7.20.5"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@prettier/sync": "^0.5.5", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.7", "@types/node": "*", "babel-plugin-tester": "^11.0.4", "prettier": "^3.0.3"}, "publishConfig": {"access": "public"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7", "_nodeVersion": "24.2.0", "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "_id": "babel-plugin-jest-hoist@30.0.1", "dist": {"integrity": "sha512-zTPME3pI50NsFW8ZBaVIOeAxzEY7XHlmWeXXu9srI+9kNfzCUTy8MFan46xOGZY8NZThMqq+e3qZUKsvXbasnQ==", "shasum": "f271b2066d2c1fb26a863adb8e13f85b06247125", "tarball": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-30.0.1.tgz", "fileCount": 6, "unpackedSize": 13940, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHuvnj/h/Am/tEl51CKVJr70rk+hWK/URh9xfYRXc+3yAiEAkKD3Kmqyqw/vErSULWeqCRh25bLCbunrmAPkpkdaH88="}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>", "actor": {"name": "cpojer", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/babel-plugin-jest-hoist_30.0.1_1750285877693_0.004493974029194714"}, "_hasShrinkwrap": false}}, "time": {"created": "2016-03-16T21:40:26.213Z", "modified": "2025-06-18T22:31:18.068Z", "1.0.0": "2016-03-16T21:40:26.213Z", "9.0.3": "2016-03-23T01:09:57.398Z", "10.0.0": "2016-03-30T22:47:15.095Z", "10.0.1": "2016-03-30T23:44:00.411Z", "10.0.2": "2016-04-11T08:24:12.983Z", "11.0.0": "2016-04-12T07:56:20.166Z", "11.0.1": "2016-04-15T04:02:32.827Z", "11.0.2": "2016-04-18T04:03:22.324Z", "12.0.0": "2016-04-27T05:24:12.500Z", "12.0.1": "2016-04-27T10:09:38.073Z", "12.0.2": "2016-04-28T04:31:39.567Z", "12.1.0": "2016-05-20T18:06:03.708Z", "12.1.1-alpha.2935e14d": "2016-06-17T05:33:15.087Z", "12.1.2-alpha.a482b15c": "2016-06-17T07:58:03.623Z", "12.1.2-alpha.6230044c": "2016-06-21T19:15:15.088Z", "12.1.3-alpha.6230044c": "2016-06-21T20:11:51.143Z", "12.1.4-alpha.a737c6e5": "2016-06-22T03:36:42.451Z", "12.1.5-alpha.b5322422": "2016-06-22T06:45:48.896Z", "13.0.0": "2016-06-22T20:18:17.024Z", "13.2.1": "2016-07-07T01:59:36.877Z", "13.2.2": "2016-07-07T02:13:02.792Z", "13.3.0-alpha.a44f195f": "2016-07-11T09:56:48.361Z", "13.3.0-alpha.4eb0c908": "2016-07-11T10:04:32.193Z", "13.2.3-alpha.ffc7404b": "2016-07-11T10:19:59.969Z", "13.3.0-alpha.ffc7404b": "2016-07-11T10:20:10.526Z", "13.3.0-alpha.8b48d59e": "2016-07-13T06:14:05.790Z", "13.3.0-alpha.g8b48d59": "2016-07-13T06:39:38.825Z", "13.4.0-alpha.d2632006": "2016-07-27T08:57:14.662Z", "14.0.0": "2016-07-27T09:14:37.102Z", "14.1.0": "2016-08-01T10:26:31.223Z", "14.1.1": "2016-08-04T08:35:50.321Z", "14.2.0-alpha.ca8bfb6e": "2016-08-15T18:54:35.244Z", "14.2.1-alpha.e21d71a4": "2016-08-16T21:18:35.656Z", "14.2.2-alpha.22bd3c33": "2016-08-16T22:54:51.892Z", "14.3.0-alpha.d13c163e": "2016-08-18T20:35:22.772Z", "14.3.1-alpha.410cb91a": "2016-08-30T21:52:04.899Z", "14.3.2-alpha.83c25417": "2016-08-31T18:50:32.950Z", "15.0.0": "2016-08-31T18:55:25.739Z", "15.2.0-alpha.c681f819": "2016-09-29T09:02:14.019Z", "16.0.0": "2016-10-03T08:38:30.167Z", "16.1.0-alpha.691b0e22": "2016-10-28T07:27:24.972Z", "17.0.2": "2016-11-15T00:39:17.568Z", "18.0.0": "2016-12-15T11:24:22.647Z", "18.5.0-alpha.7da3df39": "2017-02-17T16:57:29.786Z", "19.0.0": "2017-02-21T09:30:09.707Z", "19.1.0-alpha.eed82034": "2017-03-17T00:41:16.847Z", "19.2.0-alpha.993e64af": "2017-05-04T15:38:13.341Z", "19.3.0-alpha.85402254": "2017-05-05T11:48:17.708Z", "20.0.0": "2017-05-06T12:32:28.035Z", "20.0.1": "2017-05-11T10:50:02.340Z", "20.0.2": "2017-05-17T10:50:15.915Z", "20.0.3": "2017-05-17T10:57:06.278Z", "20.1.0-alpha.1": "2017-06-28T10:16:14.927Z", "20.1.0-alpha.2": "2017-06-29T16:36:41.473Z", "20.1.0-alpha.3": "2017-06-30T14:20:47.593Z", "20.1.0-beta.1": "2017-07-13T10:33:36.779Z", "20.1.0-chi.1": "2017-07-14T10:24:57.383Z", "20.1.0-delta.1": "2017-07-18T08:46:47.729Z", "20.1.0-delta.2": "2017-07-19T12:56:37.295Z", "20.1.0-delta.3": "2017-07-25T22:12:20.954Z", "20.1.0-delta.4": "2017-07-27T17:19:04.865Z", "20.1.0-delta.5": "2017-08-01T16:33:33.350Z", "20.1.0-echo.1": "2017-08-08T16:49:46.417Z", "21.0.0-alpha.1": "2017-08-11T10:13:58.786Z", "21.0.0-alpha.2": "2017-08-21T22:06:46.355Z", "21.0.0-beta.1": "2017-08-24T21:26:39.234Z", "21.0.0": "2017-09-04T15:01:42.605Z", "21.0.2": "2017-09-08T14:19:03.122Z", "21.2.0": "2017-09-26T20:22:03.741Z", "21.3.0-alpha.1e3ee68e": "2017-09-28T14:20:26.697Z", "21.3.0-alpha.eff7a1cf": "2017-10-01T16:46:41.219Z", "21.3.0-beta.1": "2017-10-04T10:48:35.619Z", "21.3.0-beta.2": "2017-10-13T09:54:01.235Z", "21.3.0-beta.3": "2017-10-25T19:33:57.266Z", "21.3.0-beta.4": "2017-10-26T13:26:47.803Z", "21.3.0-beta.5": "2017-11-02T13:17:22.620Z", "21.3.0-beta.6": "2017-11-03T16:21:21.051Z", "21.3.0-beta.7": "2017-11-06T09:39:40.242Z", "21.3.0-beta.8": "2017-11-07T17:43:30.713Z", "21.3.0-beta.9": "2017-11-22T13:17:21.698Z", "21.3.0-beta.10": "2017-11-25T12:39:18.877Z", "21.3.0-beta.11": "2017-11-29T14:31:14.768Z", "21.3.0-beta.12": "2017-12-05T18:48:29.296Z", "21.3.0-beta.13": "2017-12-06T14:37:01.892Z", "21.3.0-beta.14": "2017-12-12T10:52:25.279Z", "21.3.0-beta.15": "2017-12-15T13:27:30.979Z", "22.0.0": "2017-12-18T11:03:18.708Z", "22.0.1": "2017-12-18T20:29:17.576Z", "22.0.2": "2017-12-19T13:52:57.182Z", "22.0.3": "2017-12-19T14:58:45.145Z", "22.0.6": "2018-01-11T09:46:38.007Z", "22.1.0": "2018-01-15T11:57:01.904Z", "22.2.0": "2018-02-07T10:25:52.515Z", "22.4.1": "2018-02-22T21:28:55.933Z", "23.0.0-alpha.0": "2018-03-15T14:55:32.976Z", "23.0.0-alpha.1": "2018-03-21T16:00:10.565Z", "22.4.3": "2018-03-21T16:07:59.087Z", "23.0.0-alpha.2": "2018-03-26T10:40:43.787Z", "23.0.0-alpha.4": "2018-03-26T12:31:36.469Z", "23.0.0-alpha.5": "2018-04-10T19:18:17.298Z", "23.0.0-alpha.5r": "2018-04-11T05:52:47.425Z", "23.0.0-alpha.6r": "2018-04-12T07:01:31.976Z", "23.0.0-alpha.7": "2018-04-17T18:55:01.362Z", "23.0.0-beta.0": "2018-04-20T10:10:24.732Z", "23.0.0-beta.1": "2018-04-21T15:44:16.839Z", "23.0.0-beta.2": "2018-04-26T21:17:30.202Z", "23.0.0-alpha.3r": "2018-04-30T13:10:02.971Z", "23.0.0-beta.3r": "2018-04-30T13:14:45.289Z", "23.0.0-charlie.0": "2018-05-02T10:56:06.578Z", "23.0.0-charlie.1": "2018-05-03T12:10:05.572Z", "23.0.0-charlie.2": "2018-05-15T09:51:12.248Z", "22.4.4": "2018-05-18T12:58:44.831Z", "23.0.0-charlie.3": "2018-05-22T14:58:50.909Z", "23.0.0-charlie.4": "2018-05-23T10:42:06.110Z", "23.0.0": "2018-05-24T17:26:13.978Z", "23.0.1": "2018-05-27T15:30:37.683Z", "23.2.0": "2018-06-25T14:05:07.212Z", "24.0.0-alpha.0": "2018-10-19T12:12:23.818Z", "24.0.0-alpha.1": "2018-10-22T15:35:28.063Z", "24.0.0-alpha.2": "2018-10-25T10:48:33.172Z", "24.0.0-alpha.4": "2018-10-26T16:32:57.646Z", "24.0.0-alpha.5": "2018-11-09T13:12:27.299Z", "24.0.0-alpha.6": "2018-11-09T17:49:25.747Z", "24.0.0-alpha.7": "2018-12-11T16:07:29.594Z", "24.0.0-alpha.9": "2018-12-19T14:22:44.501Z", "24.0.0-alpha.10": "2019-01-09T17:00:50.628Z", "24.0.0-alpha.11": "2019-01-10T18:41:15.670Z", "24.0.0-alpha.12": "2019-01-11T14:57:54.437Z", "24.0.0-alpha.13": "2019-01-23T15:15:15.662Z", "24.0.0-alpha.15": "2019-01-24T17:52:16.839Z", "24.0.0-alpha.16": "2019-01-25T13:41:47.524Z", "24.0.0": "2019-01-25T15:04:41.890Z", "24.1.0": "2019-02-05T15:06:44.728Z", "24.2.0": "2019-03-05T11:22:43.930Z", "24.2.0-alpha.0": "2019-03-05T14:46:22.095Z", "24.3.0": "2019-03-07T12:59:18.928Z", "24.6.0": "2019-04-01T22:26:16.818Z", "24.9.0": "2019-08-16T05:55:46.683Z", "25.0.0": "2019-08-22T03:23:44.272Z", "25.1.0": "2020-01-22T00:59:44.561Z", "25.2.0-alpha.86": "2020-03-25T17:16:17.439Z", "25.2.0": "2020-03-25T17:57:57.585Z", "25.2.1-alpha.1": "2020-03-26T07:54:13.751Z", "25.2.1-alpha.2": "2020-03-26T08:10:21.766Z", "25.2.1": "2020-03-26T09:01:04.191Z", "25.2.6": "2020-04-02T10:29:08.240Z", "25.4.0": "2020-04-19T21:50:19.565Z", "25.5.0": "2020-04-28T19:45:12.558Z", "26.0.0-alpha.0": "2020-05-02T12:12:51.994Z", "26.0.0": "2020-05-04T17:52:56.939Z", "26.1.0": "2020-06-23T15:15:03.183Z", "26.2.0": "2020-07-30T10:11:36.825Z", "26.5.0": "2020-10-05T09:28:06.461Z", "26.6.2": "2020-11-02T12:51:13.301Z", "27.0.0-next.0": "2020-12-05T17:25:07.155Z", "27.0.0-next.3": "2021-02-18T22:09:42.145Z", "27.0.0-next.10": "2021-05-20T14:11:12.705Z", "27.0.1": "2021-05-25T10:06:23.472Z", "27.0.6": "2021-06-28T17:05:30.905Z", "27.2.0": "2021-09-13T08:06:34.772Z", "27.4.0": "2021-11-29T13:36:54.816Z", "27.5.0": "2022-02-05T09:59:18.438Z", "27.5.1": "2022-02-08T10:52:12.097Z", "28.0.0-alpha.0": "2022-02-10T18:17:26.513Z", "28.0.0-alpha.3": "2022-02-17T15:42:20.154Z", "28.0.0-alpha.4": "2022-02-22T12:13:54.610Z", "28.0.0-alpha.6": "2022-03-01T08:32:22.898Z", "28.0.0-alpha.9": "2022-04-19T10:59:13.921Z", "28.0.0": "2022-04-25T12:08:02.539Z", "28.0.2": "2022-04-27T07:44:00.277Z", "28.1.1": "2022-06-07T06:09:35.482Z", "28.1.3": "2022-07-13T14:12:26.393Z", "29.0.0-alpha.0": "2022-07-17T22:07:06.498Z", "29.0.0-alpha.2": "2022-08-05T23:32:25.345Z", "29.0.0-alpha.3": "2022-08-07T13:41:32.019Z", "29.0.0": "2022-08-25T12:33:24.615Z", "29.0.2": "2022-09-03T10:48:17.198Z", "29.2.0": "2022-10-14T09:13:41.374Z", "29.4.0": "2023-01-24T10:55:43.501Z", "29.4.2": "2023-02-07T13:45:21.926Z", "29.4.3": "2023-02-15T11:57:16.549Z", "29.5.0": "2023-03-06T13:33:23.933Z", "29.6.3": "2023-08-21T12:38:57.739Z", "30.0.0-alpha.0": "2023-10-30T13:13:39.779Z", "30.0.0-alpha.1": "2023-10-30T13:32:46.199Z", "30.0.0-alpha.2": "2023-11-16T09:28:14.547Z", "30.0.0-alpha.3": "2024-02-20T11:08:48.033Z", "30.0.0-alpha.4": "2024-05-12T21:43:14.422Z", "30.0.0-alpha.5": "2024-05-30T12:43:53.176Z", "30.0.0-alpha.6": "2024-08-08T07:42:56.214Z", "30.0.0-alpha.7": "2025-01-30T08:28:22.560Z", "30.0.0-beta.1": "2025-05-27T00:48:15.197Z", "30.0.0-beta.3": "2025-05-27T01:27:35.569Z", "30.0.0-beta.4": "2025-05-27T07:04:25.124Z", "30.0.0-beta.6": "2025-06-03T23:50:37.147Z", "30.0.0-beta.7": "2025-06-04T03:35:34.697Z", "30.0.0-beta.8": "2025-06-04T07:53:01.648Z", "30.0.0": "2025-06-10T02:15:39.852Z", "30.0.1": "2025-06-18T22:31:17.868Z"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/babel-plugin-jest-hoist"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"fangbot": true, "moimikey": true, "aofleejay": true}}