{"_id": "path-key", "_rev": "13-299facf5a4a16a08cbe2f41820153bd5", "name": "path-key", "description": "Get the PATH environment variable key cross-platform", "dist-tags": {"latest": "4.0.0"}, "versions": {"1.0.0": {"name": "path-key", "version": "1.0.0", "description": "Get the PATH environment variable key cross-platform", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/path-key"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["path", "key", "environment", "env", "variable", "var", "get", "cross-platform", "windows"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "7f9703b3ae62da971bae635d561b1a6b80102cc1", "bugs": {"url": "https://github.com/sindresorhus/path-key/issues"}, "homepage": "https://github.com/sindresorhus/path-key", "_id": "path-key@1.0.0", "_shasum": "5d53d578019646c0d68800db4e146e6bdc2ac7af", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "5d53d578019646c0d68800db4e146e6bdc2ac7af", "tarball": "https://registry.npmjs.org/path-key/-/path-key-1.0.0.tgz", "integrity": "sha512-T3hWy7tyXlk3QvPFnT+o2tmXRzU4GkitkUWLp/WZ0S/FXd7XMx176tRurgTvHTNMJOQzTcesHNpBqetH86mQ9g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFil/aONubdcPkKI8x1nPcfZ4bI/VC1XTYu+D/7to/WdAiEA5TI3NcYdTOewnJeVl0vLUXM9NpV6hEPEVB6eC3g5l94="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "path-key", "version": "2.0.0", "description": "Get the PATH environment variable key cross-platform", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/path-key.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["path", "key", "environment", "env", "variable", "var", "get", "cross-platform", "windows"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "c20998aada28a193bf245fd3be5f066c78690a4a", "bugs": {"url": "https://github.com/sindresorhus/path-key/issues"}, "homepage": "https://github.com/sindresorhus/path-key#readme", "_id": "path-key@2.0.0", "_shasum": "a07e1d3d81ee9a21e4fc70d0fd765f3022e6f70c", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.5.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "a07e1d3d81ee9a21e4fc70d0fd765f3022e6f70c", "tarball": "https://registry.npmjs.org/path-key/-/path-key-2.0.0.tgz", "integrity": "sha512-0ZKacolv78i1s63zunrC+Xdhbav6+B6aHhgCDcXCwGu4KN6jW8wD9SeLKN8G8EEgACsVTjTESQKNK6rGQ4HknA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBHV76WmAV0orqHhSw0tE9fd826mKNdpmgmjaCvMo2mBAiEAtGQdify1uNB+/w+C5po1F16EDs557A/IvvGIok//7Oc="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/path-key-2.0.0.tgz_1474784823704_0.3132385080680251"}, "directories": {}}, "2.0.1": {"name": "path-key", "version": "2.0.1", "description": "Get the PATH environment variable key cross-platform", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/path-key.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["path", "key", "environment", "env", "variable", "var", "get", "cross-platform", "windows"], "devDependencies": {"ava": "*", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "d60207f9ab9dc9e60d49c87faacf415a4946287c", "bugs": {"url": "https://github.com/sindresorhus/path-key/issues"}, "homepage": "https://github.com/sindresorhus/path-key#readme", "_id": "path-key@2.0.1", "_shasum": "411cadb574c5a140d3a4b1910d40d80cc9f40b40", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.5.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "411cadb574c5a140d3a4b1910d40d80cc9f40b40", "tarball": "https://registry.npmjs.org/path-key/-/path-key-2.0.1.tgz", "integrity": "sha512-fEHGKCSmUSDPv4uoj8AlD+joPlq3peND+HRYyxFz4KPw4z926S/b8rIuFs2FYJg3BwsxJf6A9/3eIdLaYC+9Dw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA1QymvHINnQoBSpli7ha3x7VFlfTiXfTS40xXQP15MXAiA/52DZRtxKaGFcNH6pX688Lpdr0QIkJCzU9letBPVwZA=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/path-key-2.0.1.tgz_1474887352898_0.8162120468914509"}, "directories": {}}, "3.0.0": {"name": "path-key", "version": "3.0.0", "description": "Get the PATH environment variable key cross-platform", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/path-key.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd-check"}, "keywords": ["path", "key", "environment", "env", "variable", "var", "get", "cross-platform", "windows"], "devDependencies": {"@types/node": "^11.11.0", "ava": "^1.3.1", "tsd-check": "^0.3.0", "xo": "^0.24.0"}, "gitHead": "5d21e7064d248a7fc928da67d78003a855145641", "bugs": {"url": "https://github.com/sindresorhus/path-key/issues"}, "homepage": "https://github.com/sindresorhus/path-key#readme", "_id": "path-key@3.0.0", "_nodeVersion": "10.15.1", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-zLY/S2T5y8zv1vEpx4VHguUgmtgkewofGKSpa7VmzdU3Jbu8JonUvt5hzjBpJvamSnusynqJiYwkbi22aTo7Rw==", "shasum": "4c459253329ac9abfd22a456a18a49bb3394f271", "tarball": "https://registry.npmjs.org/path-key/-/path-key-3.0.0.tgz", "fileCount": 5, "unpackedSize": 3721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchRuJCRA9TVsSAnZWagAAbW4P/ji9nDoz6ngkY0cPomuI\nQ+wclEXxSsySYPTjvcQYZQYSrJCpVUJIZ+Ut4KkFj6tidHxCzGPxKvGX6Jp/\nbYzelAwtg1X6oKQVhv4dBdV1+sCZWLz53mpIPNJb9rQBNcaPBHtc8iQu0Mww\nMBgGzvlobADDZQfk5pxdLOp92db/iQIOXP7OzPynS6mrLkU2pJpkx1KJjj0R\n+JZdd/4cEE4PlSM7UTwDZa/XlJpj5RXI2zjHn31bLz8ozgGb3i9rjEODlZI1\nc1Kk23QZYl3UFp2x7Ixj8fyZPIAZcaghBKiQt2kmQyDAF11T2vKKE6anSMM9\nh9FoHR9xqjuzFhcBOK+LhMCDrAw4wIxLsfBFwgkT4Ao8HJnMQnSoTHPcXAUB\nnLn6EQjrl7WknMpwzud3m9nlKNxsLmJi73WH/Agh82ltaT7N27msnV9Rr89z\nK5KsqEo1K165LV73O6pB2AnH7xJXslAPiXLZ5pa0R4EvjF+bUNDPZcWm9nMF\nQc7V3o5D+xkcpWFlbocgn60MoqBKcB0yWPwy2OHZQas/aIocRtGDTpdb4HaV\n+C/Zp87U7bCFDL5K5vWpNl5SPkswCXdqi7lt4zP5q6uVDxJtMQtfJId20YDd\nfmi0SpTStu87ak1WouJGrH4AtZG0UVj6scXaPsNnYR7B7qZRZJQcL51G4Aqa\nW4Pl\r\n=QINq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG3AyES+u89nXE4cU6XhdeoDV5tncQitKayomAF/4dnFAiATxi5f0Nnzv3gpy6uD92fc2mSpt/E+fAklFEOAyvGjyw=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/path-key_3.0.0_1552227208700_0.9010846848491241"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "path-key", "version": "3.1.0", "description": "Get the PATH environment variable key cross-platform", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/path-key.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["path", "key", "environment", "env", "variable", "var", "get", "cross-platform", "windows"], "devDependencies": {"@types/node": "^11.13.0", "ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "cc5e5ab75428c639ed64762e182a55fbfb07db1c", "bugs": {"url": "https://github.com/sindresorhus/path-key/issues"}, "homepage": "https://github.com/sindresorhus/path-key#readme", "_id": "path-key@3.1.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-8cChqz0RP6SHJkMt48FW0A7+qUOn+OsnOsVtzI59tZ8m+5bCSk7hzwET0pulwOM2YMn9J1efb07KB9l9f30SGg==", "shasum": "99a10d870a803bdd5ee6f0470e58dfcd2f9a54d3", "tarball": "https://registry.npmjs.org/path-key/-/path-key-3.1.0.tgz", "fileCount": 5, "unpackedSize": 4172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpvs8CRA9TVsSAnZWagAATFUQAKL3ob3wLUEir7iegTpq\nFeruhlVSBc3qwBzdsuQRnkAYsLwRkOtN2H3Hzp3X+qeACGrP+gzEZ1qmar7n\nttq8C0GqAwtHMO8dFM6b/YxZx0b2haLzmjEGSir858dsTE3yfWTsDWsinO66\n3voFik5GijfRNeiIZZ7pYGX3YhW7WnuZe+21Yh9y4Kuvz6BTszJSQPQj4MHi\n2xcBynpP7048E7q+OKHO/UGYvodctGzyXfy37aoq+kDmQ9igp7xjZs6rxTWY\nVGXrG721L3OsCcSZTrpClVjxzKlzgj+C6aNj3IhqjQ/uBd9OwbJqktHqe+Vk\nNE4nvSAoFDOgf2utUOLsjrIgCH4NYgDQNwZ8pfyr1TGjjjwZfn9XmtjswArj\n2uydJO9pkrfemq7LS+LxcGydtaHqR27CTOigd13Aba1pguMAxN2/508Bx9jd\nY6xcwK1lxRDBZMGq8bmZVYlE74DnuieOd+BkX8wcYFQZoT1W0/XH1boDQgs5\nP/3C6kG1BOKAagPrvMJwr+PGXztFDAsxuvKYU+pMzpOmyYnIcXjd/1mn3DNH\njKjBbLxLV2AIpciRHpf2Jl/iI6RTXkaf8uZJrC/pgRQInwtNSKHRZPhFvkps\nqLaQRSKLEg+/KCfo7GAHonve3/5cJv7xtgnJabl9rAv3RSuw2KgZUn+7GSvQ\nZCAq\r\n=GJoB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH7KI5YhHlfYsY94EpCm5+1jOztBdxDCgeOnZGToRhVoAiBsfsXpU8CF5+8Yb1iS6aYUy4bPj0os/p9HOjzUdsivgA=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/path-key_3.1.0_1554447164202_0.31022058392905505"}, "_hasShrinkwrap": false}, "3.1.1": {"name": "path-key", "version": "3.1.1", "description": "Get the PATH environment variable key cross-platform", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/path-key.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["path", "key", "environment", "env", "variable", "var", "get", "cross-platform", "windows"], "devDependencies": {"@types/node": "^11.13.0", "ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "caea269db3a5eb0c09c793f4be5435312971d2fa", "bugs": {"url": "https://github.com/sindresorhus/path-key/issues"}, "homepage": "https://github.com/sindresorhus/path-key#readme", "_id": "path-key@3.1.1", "_nodeVersion": "10.17.0", "_npmVersion": "6.11.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==", "shasum": "581f6ade658cbba65a0d3380de7753295054f375", "tarball": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz", "fileCount": 5, "unpackedSize": 4553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2BEWCRA9TVsSAnZWagAAYX0P/Rz/b6MNfPe+VoHlRz8Q\nJQlrCaHeSa+2Hj4XhEt+DWDfpDMXX5OcgAOiz6JzF4Hnsx+VGpQZsYkp7F3q\noQjcL0pQ2sA25U14C5mP06VMKGMmsDnXMX/iIc7VYyC6/JUGdtxAOs42pAJG\n9fs8fha47T2gFWsAziytzdTODop1UCtbfDTsOrO81KUzMfhmLAxVaIBHztaD\nOgdxOeINVMrqyCplRnOYHXjjd10CbqVWBmqtyIAMJj84cHitF/03ZHhpvt1P\ngzpTkH60nVm3gaMnMz+akGLapyy2I72MjZcuRx3Tkw8Qqwm7lGygoE/UCtfR\noo5MtUSgnPPLZf/BHuRGwd2L8rCxqnaLYO6su05/1WC4MmGacRni2czBybTp\niTUh30Lzhwna47u+VfWTv77kPlV3IqcSSMnKVCwF4Ea4Z+diQ8K0BCzp/N7U\nXxpN1nh1jqR8kMcQr9ybgTsQ5Xv/x/KNvMNpGnBu/VfAuy0q7XG4k0efI4NJ\nGXtl3LzeTgw94KtVeaC5V/+iBijPvO2TGCoOoONaSkGPP8UlLaabCr62O76h\nxe15coJ04Yz8cPTzyOOMkoUJrtJmG6whwI/OqIxKaNKGUzL0u0YZDTepeJD2\nYw+LxPkIJwaw5ohi7nMFh6pR4Fva28KOQvbYUMM80/R1VZDvwMBqpQCJNlV0\nYPc9\r\n=1qcn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAwOqg8TUPAYc0BU4fVgYQw5sg3cZlPq2S90+ei63ZrEAiAsWC5WjxqdP1lbFmNySWF/BHy6UfJwkONg7eFU55uHwA=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/path-key_3.1.1_1574441238024_0.7107545641938868"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "path-key", "version": "4.0.0", "description": "Get the PATH environment variable key cross-platform", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/path-key.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["path", "key", "environment", "env", "variable", "get", "cross-platform", "windows"], "devDependencies": {"@types/node": "^14.14.37", "ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "gitHead": "d5ad08865acad539d2d64655f14987a7ad764451", "bugs": {"url": "https://github.com/sindresorhus/path-key/issues"}, "homepage": "https://github.com/sindresorhus/path-key#readme", "_id": "path-key@4.0.0", "_nodeVersion": "15.12.0", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==", "shasum": "295588dc3aee64154f877adb9d780b81c554bf18", "tarball": "https://registry.npmjs.org/path-key/-/path-key-4.0.0.tgz", "fileCount": 5, "unpackedSize": 4069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgcELkCRA9TVsSAnZWagAAy3gQAJ2VsZWgJZZQxBORfE1W\nYdWz8zTgZBp+ZWFa5frmfzVH4w4TiA1kns8EoSn+OFw4k4StZwkcfverCiXw\nSin2/so9+zIiSriM1di8lGs6h35YfOZLTW6lQEgm73JdfLTtGotPujV8g8l5\njB01BaBNUBxfvLb5fxZV6rauvHPG+tafhomm8m55aCA4q+xG27Ufol+1S6G1\nD47Bg3vFEvs/3UBS3CWYbydRYlkED773c2az4unrOVflYX0hPNaxVR5i23fb\n1JpgLi9h0t8+eMRiAe7CYsaSe+yH4BXl/Ld2fZVWr01BjNhcaIsaCHB+R/Ws\n1EWA222EPFxIQEofS4vQ2r07ikyirJXdPf9iCZaKpBjEvHGeEZR2oKQvadW8\nRS74BdIFTs4L47mWZ4V+nwOb98zJo9dlUIclpNuEkWb3q9TrtQ62jkcvgDX8\nF2msHMkUStD/l0vePQVQgHVM92xSXRNsJrWxiMZClLLZzYAX8gxqpIOxFUAT\nAT1LMfk0xofNZqsOVWrgW606aVlBNhZGr+J9dg58HuZXmnfEx/mBcsB0zwPM\nc21SSnsmUDnjHuX8PfJQzaat9sitxnQvNkjCYxecXgTh992BirzLju0gMc5l\nWIY0vEhBOcMDwyXcKvUNS+pEBWNt5xnalmBkD9CVZsdnRJjz+PYJIN2AwWVZ\n8OJJ\r\n=H3Kp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDweU+QrdjKgSpk78xRNP5Yfffb0RWhBJklnQJqATjPUAIhAOJWyOkUtdp+JMQdY8ZMD6v//I2JnKvIJrQ8s6/puTrJ"}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/path-key_4.0.0_1617969891636_0.***********99604"}, "_hasShrinkwrap": false}}, "readme": "# path-key\n\n> Get the [PATH](https://en.wikipedia.org/wiki/PATH_(variable)) environment variable key cross-platform\n\nIt's usually `PATH` but on Windows it can be any casing like `Path`...\n\n## Install\n\n```\n$ npm install path-key\n```\n\n## Usage\n\n```js\nimport pathKey from 'path-key';\n\nconst key = pathKey();\n//=> 'PATH'\n\nconst PATH = process.env[key];\n//=> '/usr/local/bin:/usr/bin:/bin'\n```\n\n## API\n\n### pathKey(options?)\n\n#### options\n\nType: `object`\n\n##### env\n\nType: `object`\\\nDefault: [`process.env`](https://nodejs.org/api/process.html#process_process_env)\n\nUse a custom environment variables object.\n\n#### platform\n\nType: `string`\\\nDefault: [`process.platform`](https://nodejs.org/api/process.html#process_process_platform)\n\nGet the PATH key for a specific platform.\n\n---\n\n<div align=\"center\">\n\t<b>\n\t\t<a href=\"https://tidelift.com/subscription/pkg/npm-path-key?utm_source=npm-path-key&utm_medium=referral&utm_campaign=readme\">Get professional support for this package with a Tidelift subscription</a>\n\t</b>\n\t<br>\n\t<sub>\n\t\tTidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.\n\t</sub>\n</div>\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-06-17T00:09:27.470Z", "created": "2015-12-28T18:36:51.108Z", "1.0.0": "2015-12-28T18:36:51.108Z", "2.0.0": "2016-09-25T06:27:06.037Z", "2.0.1": "2016-09-26T10:55:53.120Z", "3.0.0": "2019-03-10T14:13:28.802Z", "3.1.0": "2019-04-05T06:52:44.335Z", "3.1.1": "2019-11-22T16:47:18.153Z", "4.0.0": "2021-04-09T12:04:51.822Z"}, "homepage": "https://github.com/sindresorhus/path-key#readme", "keywords": ["path", "key", "environment", "env", "variable", "get", "cross-platform", "windows"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/path-key.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/path-key/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"faraoman": true, "zuojiang": true, "flumpus-dev": true}}