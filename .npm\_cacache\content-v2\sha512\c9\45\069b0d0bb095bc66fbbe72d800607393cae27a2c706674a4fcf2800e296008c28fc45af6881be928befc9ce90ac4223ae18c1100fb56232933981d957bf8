{"_id": "hasown", "_rev": "11-a074973674b38963c608b6224deaaf66", "name": "hasown", "description": "A robust, ES3 compatible, \"has own property\" predicate.", "dist-tags": {"latest": "2.0.2"}, "versions": {"1.0.1": {"name": "hasown", "version": "1.0.1", "description": "JavaScript curried hasOwn helper", "main": "index.js", "scripts": {"test": "make test", "test-w": "make test-w", "test-debug": "mocha --debug-brk"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.4"}, "repository": {"type": "git", "url": "git://github.com/radubrehar/hasown.git"}, "keywords": ["own", "property", "hasOwn", "properties", "object", "key"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/radubrehar/has-own/issues"}, "_id": "hasown@1.0.1", "dist": {"shasum": "b64ff1570673ba06cc5d0183c0a4f0b5b1bd6459", "tarball": "https://registry.npmjs.org/hasown/-/hasown-1.0.1.tgz", "integrity": "sha512-My8IVgPaNw1TPrcOtLxG5N2BQJUr2YYI8a3ei3Njx4QIZ+WzEkvLQ4jySrcy6YNfq1JwHpyimb4p2Rw5IuE/SA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2PWvzNlWQHInYuUclgwlqTxpUbJxAWKeX9B4eBbEVdAIgMiAZHo+4GgV/pDaqJvxagk93ulJYd5IJcFBleF46caM="}]}, "_from": ".", "_npmVersion": "1.3.6", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "hasown", "version": "2.0.0", "description": "A robust, ES3 compatible, \"has own property\" predicate.", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated && npm run emit-types", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "npm run tsc", "preemit-types": "rm -f *.ts *.ts.map test/*.ts test/*.ts.map", "emit-types": "npm run tsc -- --noEmit false --emitDeclarationOnly", "pretest": "npm run lint", "tsc": "tsc -p .", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/hasOwn.git"}, "keywords": ["has", "hasOwnProperty", "hasOwn", "has-own", "own", "has", "property", "in", "javascript", "ecmascript"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/hasOwn/issues"}, "homepage": "https://github.com/inspect-js/hasOwn#readme", "dependencies": {"function-bind": "^1.1.2"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/function-bind": "^1.1.9", "@types/mock-property": "^1.0.1", "@types/tape": "^5.6.3", "aud": "^2.0.3", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "mock-property": "^1.0.2", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.1", "typescript": "^5.3.0-dev.20231019"}, "engines": {"node": ">= 0.4"}, "testling": {"files": "test/index.js"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows", "test", "!*.d.ts", "!*.d.ts.map"]}, "_id": "hasown@2.0.0", "gitHead": "1237980ed6c3915c5c3ba996345289546b<PERSON>bef", "types": "./index.d.ts", "_nodeVersion": "21.0.0", "_npmVersion": "10.2.0", "dist": {"integrity": "sha512-vUptKVTpIJhcczKBbgnS+RtcuYMB8+oNzPK2/Hp3hanz8JmpATdmmgLgSaadVREkDm+e2giHwY3ZRkyjSIDDFA==", "shasum": "f4c513d454a57b7c7e1650778de226b11700546c", "tarball": "https://registry.npmjs.org/hasown/-/hasown-2.0.0.tgz", "fileCount": 11, "unpackedSize": 10837, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHEMS61n5pjHhGQuuWvV31D+pe/OiLmR6TMc3v1yh0kBAiEA8SQoSfqGFGGm6/ZJBXPQm6o8RaFI1/UzrWvqAd3xyAg="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hasown_2.0.0_1697864055249_0.5993234708355475"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "hasown", "version": "2.0.1", "description": "A robust, ES3 compatible, \"has own property\" predicate.", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "types": "index.d.ts", "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "npm run tsc", "pretest": "npm run lint", "tsc": "tsc -p .", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/hasOwn.git"}, "keywords": ["has", "hasOwnProperty", "hasOwn", "has-own", "own", "has", "property", "in", "javascript", "ecmascript"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/hasOwn/issues"}, "homepage": "https://github.com/inspect-js/hasOwn#readme", "dependencies": {"function-bind": "^1.1.2"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/function-bind": "^1.1.10", "@types/mock-property": "^1.0.2", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "mock-property": "^1.0.3", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.4", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "testling": {"files": "test/index.js"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows", "test"]}, "_id": "hasown@2.0.1", "gitHead": "b4dc660cc5ad50be810b378406b67c6247632db0", "_nodeVersion": "21.6.0", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-1/th4MHjnwncwXsIW6QMzlvYL9kG5e/CpVvLRZe4XPa8TOUNbCELqmvhDmnkNsAjwaG4+I8gJJL0JBvTTLO9qA==", "shasum": "26f48f039de2c0f8d3356c223fb8d50253519faa", "tarball": "https://registry.npmjs.org/hasown/-/hasown-2.0.1.tgz", "fileCount": 10, "unpackedSize": 11140, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCkyEeCGFLgef5pXaA5lBJ/2OgNan8SC1oifdZqTq5NkAIhAOm/oEE81UZRadLLQapZh8qy66V7ykHcRI6hjj3ruXvg"}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hasown_2.0.1_1707630722982_0.2386679966825953"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "hasown", "version": "2.0.2", "description": "A robust, ES3 compatible, \"has own property\" predicate.", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "types": "index.d.ts", "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "npm run tsc", "pretest": "npm run lint", "tsc": "tsc -p .", "posttsc": "attw -P", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/hasOwn.git"}, "keywords": ["has", "hasOwnProperty", "hasOwn", "has-own", "own", "has", "property", "in", "javascript", "ecmascript"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/hasOwn/issues"}, "homepage": "https://github.com/inspect-js/hasOwn#readme", "dependencies": {"function-bind": "^1.1.2"}, "devDependencies": {"@arethetypeswrong/cli": "^0.15.1", "@ljharb/eslint-config": "^21.1.0", "@ljharb/tsconfig": "^0.2.0", "@types/function-bind": "^1.1.10", "@types/mock-property": "^1.0.2", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "mock-property": "^1.0.3", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "testling": {"files": "test/index.js"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows", "test"]}, "_id": "hasown@2.0.2", "gitHead": "d00d35005baf16a33d691a13f8ad627f35040742", "_nodeVersion": "21.7.0", "_npmVersion": "10.5.0", "dist": {"integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "shasum": "003eaf91be7adc372e84ec59dc37252cedb80003", "tarball": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz", "fileCount": 10, "unpackedSize": 8765, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDwT1Hb/p8miJ/uXHCku9k+wzFABQD1ohA9jK2Wx7JrgwIgB1GtchHXm+XK4rsE3qomVCL/uhDlhqCYGaYgd7hCY5s="}]}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/hasown_2.0.2_1710092304974_0.23927876187037622"}, "_hasShrinkwrap": false}}, "readme": "# hasown <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nA robust, ES3 compatible, \"has own property\" predicate.\n\n## Example\n\n```js\nconst assert = require('assert');\nconst hasOwn = require('hasown');\n\nassert.equal(hasOwn({}, 'toString'), false);\nassert.equal(hasOwn([], 'length'), true);\nassert.equal(hasOwn({ a: 42 }, 'a'), true);\n```\n\n## Tests\nSimply clone the repo, `npm install`, and run `npm test`\n\n[package-url]: https://npmjs.org/package/hasown\n[npm-version-svg]: https://versionbadg.es/inspect-js/hasown.svg\n[deps-svg]: https://david-dm.org/inspect-js/hasOwn.svg\n[deps-url]: https://david-dm.org/inspect-js/hasOwn\n[dev-deps-svg]: https://david-dm.org/inspect-js/hasOwn/dev-status.svg\n[dev-deps-url]: https://david-dm.org/inspect-js/hasOwn#info=devDependencies\n[npm-badge-png]: https://nodei.co/npm/hasown.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/hasown.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/hasown.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=hasown\n[codecov-image]: https://codecov.io/gh/inspect-js/hasOwn/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/inspect-js/hasOwn/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/hasOwn\n[actions-url]: https://github.com/inspect-js/hasOwn/actions\n", "maintainers": [{"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2024-03-10T17:38:25.538Z", "created": "2014-09-03T06:38:13.950Z", "1.0.1": "2014-09-03T06:38:17.009Z", "2.0.0": "2023-10-21T04:54:15.456Z", "2.0.1": "2024-02-11T05:52:03.147Z", "2.0.2": "2024-03-10T17:38:25.117Z"}, "readmeFilename": "README.md", "keywords": ["has", "hasOwnProperty", "hasOwn", "has-own", "own", "has", "property", "in", "javascript", "ecmascript"], "repository": {"type": "git", "url": "git+https://github.com/inspect-js/hasOwn.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/inspect-js/hasOwn/issues"}, "license": "MIT", "users": {"radubrehar": true}, "homepage": "https://github.com/inspect-js/hasOwn#readme"}