{"_id": "@types/istanbul-lib-report", "_rev": "486-7027f33c216f2b08e0c7a3f342e97161", "name": "@types/istanbul-lib-report", "dist-tags": {"ts2.4": "1.1.1", "ts2.5": "1.1.1", "ts2.6": "1.1.1", "ts2.7": "1.1.1", "ts2.8": "3.0.0", "ts2.9": "3.0.0", "ts3.0": "3.0.0", "ts3.1": "3.0.0", "ts3.2": "3.0.0", "ts3.3": "3.0.0", "ts3.4": "3.0.0", "ts3.5": "3.0.0", "ts3.6": "3.0.0", "ts3.7": "3.0.0", "ts3.8": "3.0.0", "ts3.9": "3.0.0", "ts4.0": "3.0.0", "ts4.1": "3.0.0", "ts4.2": "3.0.0", "ts4.3": "3.0.0", "ts4.4": "3.0.0", "ts5.8": "3.0.3", "ts5.7": "3.0.3", "latest": "3.0.3", "ts4.5": "3.0.3", "ts4.6": "3.0.3", "ts4.7": "3.0.3", "ts4.8": "3.0.3", "ts4.9": "3.0.3", "ts5.0": "3.0.3", "ts5.1": "3.0.3", "ts5.2": "3.0.3", "ts5.3": "3.0.3", "ts5.4": "3.0.3", "ts5.5": "3.0.3", "ts5.6": "3.0.3", "ts5.9": "3.0.3"}, "versions": {"1.1.0": {"name": "@types/istanbul-lib-report", "version": "1.1.0", "license": "MIT", "_id": "@types/istanbul-lib-report@1.1.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}], "dist": {"shasum": "79e9b463f947e98dcc82272da51b908fc93e8aea", "tarball": "https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-1.1.0.tgz", "integrity": "sha512-nW5QuzmMhr7fHPijtaGOemFFI8Ctrxb/dIXgouSlKmWT16RxWlGLEX/nGghIBOReKe9hPFZXoNh338nFQk2xcA==", "signatures": [{"sig": "MEUCIBcqA4HGJSoR7PnmUOh8GP0ltE184YnRUSx50XTMPKUnAiEA2W6S2+omamsIek2REaAjrBwvjD3RG11oobq0SJz4YGY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for istanbul-lib-report", "directories": {}, "dependencies": {"@types/istanbul-lib-coverage": "*"}, "typeScriptVersion": "2.4", "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-report-1.1.0.tgz_1504188371148_0.7109213527292013", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c45248cc43a4189acb35a4744b16de14957f6d78ed8cb005debaa4e80de0e1fb"}, "1.1.1": {"name": "@types/istanbul-lib-report", "version": "1.1.1", "license": "MIT", "_id": "@types/istanbul-lib-report@1.1.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}], "dist": {"shasum": "e5471e7fa33c61358dd38426189c037a58433b8c", "tarball": "https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-1.1.1.tgz", "fileCount": 4, "integrity": "sha512-3BUTyMzbZa2DtDI2BkERNC6jJw2Mr2Y0oGI7mRxYNBPxppbtEK1F66u3bKwU2g+wxwWI7PAoRpJnOY1grJqzHg==", "signatures": [{"sig": "MEUCIQC4XIsaasgevSh97WDjZ74//DirRd4rdzLG9yfaYLastgIgSDvkudH2ghGY7+P7qPN5LhI9co6BCvKZp1qy11YMqHA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcwj4JCRA9TVsSAnZWagAA/FsP/i9vkeWIkFLU9at1i4CP\nxnmd7mmJ71AQo/KvDZeJ3uGrzmkwHKcVPb8KKEJQhkNyNnSEJDriKshxPB/u\nMMpHL2LPm4OgERP/BJApR3x56c5hHPwXYDrzfRHBfxhmmOwKRhQKbaxi7iw8\n7YcMvzb/daG+/NI3WClVr+w9YuR0g62HLJ1XR0fee6NdxMAGgSp6mSoJzake\nv2bAWZ5hWwsTwSnj8wuzldMqytzlAILad9SpZ5F53QiUiLFgNMzY+CGmz/u9\ngvOhzxIkAHFm7eom9zxAmIRuED1HuIx1P/xznKTlfwew9ta8W2xx3pWQRvU8\nOGO0+0aCEoRLwLAWTEobPDzvBad123f36V88F7HxJ/FEZdM/0hRxVvhdZChl\nRTqD+yj90vhuCIjZnVkT+k1UkpkRxYQdJZuo51xw1bRlhXg+xtgmv3r6GpUP\nBRav3qGqYkAEnhUUz5u7Lb0sSRNtckIw3w5sI5lCTd+Trf+diWePLuonxhjV\nqkNhkMTK1esBwNS97w88ZwQi+lnZt7MOAqHomFZzLWjZueTzyGsNH3WZcmGz\nlelkhPP55uIyIwH0honJrG3PNsE4F/iC8L4PCOv36G90z/8QWxgyrFJtsECr\n4T+YduquRWXEz5NBo3UeOkVWs77Inog/hjx4zoCVqYqB+OzJZINZ+pm5j/JR\nCTPf\r\n=CkRO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/istanbul-lib-report"}, "description": "TypeScript definitions for istanbul-lib-report", "directories": {}, "dependencies": {"@types/istanbul-lib-coverage": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.4", "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-report_1.1.1_1556233736658_0.6164418279986457", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "64af305d196bdbb3cc44bc664daf0546df5c55bce234d53c29f97d0883da2f32"}, "3.0.0": {"name": "@types/istanbul-lib-report", "version": "3.0.0", "license": "MIT", "_id": "@types/istanbul-lib-report@3.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}, {"url": "https://github.com/zache", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "zache"}], "dist": {"shasum": "c14c24f18ea8190c118ee7562b7ff99a36552686", "tarball": "https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.0.tgz", "fileCount": 4, "integrity": "sha512-plGgXAPfVKFoYfa9NpYDAkseG+g6Jr294RqeqcqDixSbU34MZVJRi/P+7Y8GDpzkEwLaGZZOpKIEmeVZNtKsrg==", "signatures": [{"sig": "MEYCIQDMXoglG+vJnN38iibrm47ydlNm794uh4f870JCVekTYQIhAKG53RcJcMn3v87aE4F+0SgVSqV/R0jp6K4xDhR0y5XO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8226, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJk09CRA9TVsSAnZWagAAgXMP/jkQKVHlCfds0qcl0odM\n5NuNbkClDMorAkIaq26ddal9eK32P22MnzMntGXb//e0N8azAI+2yrMrGEeH\no4Mt+BsH5zYo2oyi7NLH8MmeI3aB3EN7HYptCFsgBnHtJy6oiXn4063HlOsV\nn1wjzxE1WVgGapIPlv3bFdPU29XN8eMZJL+2fp8WeJUjYyDOGpDmTTdCJoAI\nx2qVNnuI1xcnFITkvegjCmq58HeldIY+nrkZ0rPZiFOM3ah81I/VYGnS2WsE\nVHgiKGT5WYsfxuB+CZTuiQz1bZXsQX06Fq3GiVEeVGF4EwBPhmJJhaSixO/V\nllQQixZuRGh3LPeZ8Rh+u8VGpmqUuv37KFXMwb1FxW+Mc7S9jQf/P+ZvAFk9\nLB5+nK1xt6oJSrIhZrSORf/vMau7qraa5z7rg6+PbjJpGk6EFSGULoqvLAnz\n+731chd/B5bt1PzCytjRqAeDVJ15C6CKFTbMQcbMl1l41WGkxAEsfjnFtfiw\nMa6m9d0A9NjMvoeCEn3O0SGBheuyoGwKPJvdTfHfobQhGPasUlCROru2sZYC\n78dh0vS8eg+wqpC+3tzCM+3ckrPp4M0wLnhwfFArm7Gz+z0uDn/n+nLc419z\nNr05WN00HbkadT7sjG9OfyLX6CfvCvooZcnZAbGbFEeGQOZ+nskh2hSGKod2\nq1Wv\r\n=hTxd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/istanbul-lib-report"}, "description": "TypeScript definitions for istanbul-lib-report", "directories": {}, "dependencies": {"@types/istanbul-lib-coverage": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-report_3.0.0_1579568445192_0.6855300003326508", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f8b2f5e15a24d9f52a96c5cfadb0f582bf6200ce8643e15422c3c8f1a2bb1c63"}, "3.0.1": {"name": "@types/istanbul-lib-report", "version": "3.0.1", "license": "MIT", "_id": "@types/istanbul-lib-report@3.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}, {"url": "https://github.com/zache", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "zache"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/istanbul-lib-report", "dist": {"shasum": "412e0725ef41cde73bfa03e0e833eaff41e0fd63", "tarball": "https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz", "fileCount": 5, "integrity": "sha512-gPQuzaPR5h/djlAv2apEG1HVOyj1IUs7GpfMZixU0/0KXT3pm64ylHuMUI1/Akh+sq/iikxg6Z2j+fcMDXaaTQ==", "signatures": [{"sig": "MEYCIQDJCsjA7gqAtvZ957ECl0yMUJRN1QUbl4PhyhjgTWLsfAIhAI27BYafvzXByYOD5iIoUIXA5OIM2LFabTeRtwyrclZd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8294}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/istanbul-lib-report"}, "description": "TypeScript definitions for istanbul-lib-report", "directories": {}, "dependencies": {"@types/istanbul-lib-coverage": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-report_3.0.1_1695741198334_0.8761415624556292", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "96df52e277954c084779ad7403454ac3cc4562e6adc84f8f7bd31fb37cc6a6b6"}, "3.0.2": {"name": "@types/istanbul-lib-report", "version": "3.0.2", "license": "MIT", "_id": "@types/istanbul-lib-report@3.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}, {"url": "https://github.com/zache", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "zache"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/istanbul-lib-report", "dist": {"shasum": "394798d5f727402eb5ec99eb9618ffcd2b7645a1", "tarball": "https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.2.tgz", "fileCount": 5, "integrity": "sha512-8toY6FgdltSdONav1XtUHl4LN1yTmLza+EuDazb/fEmRNCwjyqNVIQWs2IfC74IqjHkREs/nQ2FWq5kZU9IC0w==", "signatures": [{"sig": "MEUCIHeMkKoyEbfgeu9GsA7hUi9YgfI+0NvO+RnHJUGtrgS5AiEA8D9nk8OQ6ZdMh5mTZP9c8Heia/EGhLtIzb771N1/YzU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7917}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/istanbul-lib-report"}, "description": "TypeScript definitions for istanbul-lib-report", "directories": {}, "dependencies": {"@types/istanbul-lib-coverage": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-report_3.0.2_1697606982217_0.32445720488799945", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "5ad3fea906ff78dd149cad260ab9caad57cbf6df5ab4547d5e9350a5b2175073"}, "3.0.3": {"name": "@types/istanbul-lib-report", "version": "3.0.3", "license": "MIT", "_id": "@types/istanbul-lib-report@3.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}, {"url": "https://github.com/zache", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "zache"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/istanbul-lib-report", "dist": {"shasum": "53047614ae72e19fc0401d872de3ae2b4ce350bf", "tarball": "https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz", "fileCount": 5, "integrity": "sha512-NQn7AHQnk/RSLOxrBbGyJM/aVQ+pjj5HCgasFxc0K/KhoATfQ/47AyUl15I2yBUpihjmas+a+VJBOqecrFH+uA==", "signatures": [{"sig": "MEQCIBflvyw624aj5AGUXWnmz7hCK1pwnshw6GvQiPWwaTZiAiAE/qge1pZp1HL4Z5NiNswpaC1RJBFd+dC/90Bp1d1pKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7917}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/istanbul-lib-report"}, "description": "TypeScript definitions for istanbul-lib-report", "directories": {}, "dependencies": {"@types/istanbul-lib-coverage": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-report_3.0.3_1699344668186_0.9229101953786232", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7036cfd1108c02c3ceec9ffab2cbc424c76e2cafd694c550037d808bf66e3946"}}, "time": {"created": "2017-08-31T14:06:11.229Z", "modified": "2025-02-23T07:03:25.795Z", "1.1.0": "2017-08-31T14:06:11.229Z", "1.1.1": "2019-04-25T23:08:56.835Z", "3.0.0": "2020-01-21T01:00:45.335Z", "3.0.1": "2023-09-26T15:13:18.632Z", "3.0.2": "2023-10-18T05:29:42.390Z", "3.0.3": "2023-11-07T08:11:08.313Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/istanbul-lib-report", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/istanbul-lib-report"}, "description": "TypeScript definitions for istanbul-lib-report", "contributors": [{"url": "https://github.com/jason0x43", "name": "<PERSON>", "githubUsername": "jason0x43"}, {"url": "https://github.com/zache", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "zache"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}