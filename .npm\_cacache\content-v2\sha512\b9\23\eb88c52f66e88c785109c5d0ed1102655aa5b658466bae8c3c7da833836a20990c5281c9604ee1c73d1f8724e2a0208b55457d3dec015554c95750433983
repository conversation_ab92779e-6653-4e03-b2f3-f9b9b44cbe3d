{"_id": "@types/send", "_rev": "530-723c0843fb3e6a42139b65566298c637", "name": "@types/send", "dist-tags": {"ts2.1": "0.14.3", "ts2.0": "0.14.3", "ts2.2": "0.14.4", "ts3.3": "0.14.5", "ts2.7": "0.14.5", "ts2.9": "0.14.5", "ts3.0": "0.14.5", "ts3.1": "0.14.5", "ts3.2": "0.14.5", "ts2.5": "0.14.5", "ts2.6": "0.14.5", "ts2.8": "0.14.5", "ts2.3": "0.14.5", "ts2.4": "0.14.5", "ts3.4": "0.14.6", "ts3.5": "0.14.7", "ts4.1": "0.17.1", "ts4.4": "0.17.1", "ts4.3": "0.17.1", "ts3.9": "0.17.1", "ts3.8": "0.17.1", "ts3.7": "0.17.1", "ts3.6": "0.17.1", "ts4.0": "0.17.1", "ts4.2": "0.17.1", "ts5.0": "0.17.4", "ts5.9": "0.17.5", "ts4.5": "0.17.4", "ts4.6": "0.17.4", "ts4.7": "0.17.4", "ts4.8": "0.17.4", "ts4.9": "0.17.4", "latest": "0.17.5", "ts5.6": "0.17.5", "ts5.8": "0.17.5", "ts5.7": "0.17.5", "ts5.5": "0.17.5", "ts5.4": "0.17.5", "ts5.3": "0.17.5", "ts5.2": "0.17.5", "ts5.1": "0.17.5"}, "versions": {"0.14.1": {"name": "@types/send", "version": "0.14.1", "author": "<PERSON> <https://github.com/MikeJ<PERSON>red>", "license": "MIT", "_id": "@types/send@0.14.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "1cb1c03805f9434916b428c3098b21f9273bee98", "tarball": "https://registry.npmjs.org/@types/send/-/send-0.14.1.tgz", "integrity": "sha512-g5bLDtFwLIngTX1TRbbqPM7oc48ASyK3CSiM3i0+hmuDn96B3ptR/muFEsPHohowA8vZrVwAnXF+ygPVdrEdFQ==", "signatures": [{"sig": "MEYCIQCwaF0H5XM5074SnAYEqY0F79Tb3PrmqtKKMGFhFrhe/wIhAJHTqvTJgSxiSutbh+kYlRssYELaljOK2SNv12jFjgKu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "send.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for send v0.14.1", "directories": {}, "dependencies": {"@types/mime": "*"}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/send-0.14.1.tgz_1477510105244_0.4656780061777681", "host": "packages-18-east.internal.npmjs.com"}, "typesPublisherContentHash": "a1b2985771edfff4744b1f7662d166616485a999b0e5db18aac7910a60fe5fa4"}, "0.14.2": {"name": "@types/send", "version": "0.14.2", "author": "<PERSON> <https://github.com/MikeJ<PERSON>red>", "license": "MIT", "_id": "@types/send@0.14.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "7e7f7d4c08248e9f57ea2133038c34c46e9d5564", "tarball": "https://registry.npmjs.org/@types/send/-/send-0.14.2.tgz", "integrity": "sha512-ljjazKgC4I8FfywdGbORRhQzWIrwdNtilWk122aDZzqvgu4pI9ary+kEbv5ewiZq7wk4qz0n7mINnN2z0iPBXg==", "signatures": [{"sig": "MEUCIQDpedUOvh/VhxljnnJgoZk2W0wLkkvPZLmZeYN6pc9vogIgC3NlbeDeuZFHnYeaih2sNGko5wfhT9B+yNk5LrsUrQQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for send v0.14.1", "directories": {}, "dependencies": {"@types/mime": "*", "@types/node": "*"}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/send-0.14.2.tgz_1478301593461_0.7983790358994156", "host": "packages-18-east.internal.npmjs.com"}, "typesPublisherContentHash": "3383e45092cb7e9dd336efeebe74a5ca3a8869e5409f2d24fd41056b6b395179"}, "0.14.3": {"name": "@types/send", "version": "0.14.3", "license": "MIT", "_id": "@types/send@0.14.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/<PERSON><PERSON><PERSON>red", "name": "<PERSON>"}], "dist": {"shasum": "707a8233c2cc46a6835afe4974032f8c28c9291c", "tarball": "https://registry.npmjs.org/@types/send/-/send-0.14.3.tgz", "integrity": "sha512-le38u4BbfJ+q4kC0Ojc27DVXUdqJbepveFfMGE2+rMipbGAkdA40TNT6ugRNCxQxRKOLWL4nGcHe/wac3N0DDg==", "signatures": [{"sig": "MEUCIEq0vmev3p1XHjmW8MtCPs1/AbDLm1bDDCDp1mw/HCOjAiEA92op6sQcrELrPC40d4KT+SmKiZH7DE3kWyn1u5MJWho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for send", "directories": {}, "dependencies": {"@types/mime": "*", "@types/node": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/send-0.14.3.tgz_1499265107753_0.18156875763088465", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f75f96ec9dfdcff7fc8f9b9f17bb9ba0d257fe979a1e9db18d36a587b1d3429a"}, "0.14.4": {"name": "@types/send", "version": "0.14.4", "license": "MIT", "_id": "@types/send@0.14.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/<PERSON><PERSON><PERSON>red", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "d70458b030305999db619a7b057f7105058bd0ff", "tarball": "https://registry.npmjs.org/@types/send/-/send-0.14.4.tgz", "integrity": "sha512-SCVCRRjSbpwoKgA34wK8cq14OUPu4qrKigO85/ZH6J04NGws37khLtq7YQr17zyOH01p4T5oy8e1TxEzql01Pg==", "signatures": [{"sig": "MEUCIQCCKYE3wCbwSW3c0SYpEGQDl6d3St1VVzbHbTX8wDhPwgIgKE6kE8AJWaf2wN2ciQ8fwm3jwA88wKODF7sJ6nxwsvA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for send", "directories": {}, "dependencies": {"@types/mime": "*", "@types/node": "*"}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/send-0.14.4.tgz_1510240681712_0.869967294158414", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8729699d94c105256ee497230a62b6070ff55bf902f3fa38ff49d295363288f4"}, "0.14.5": {"name": "@types/send", "version": "0.14.5", "license": "MIT", "_id": "@types/send@0.14.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/<PERSON><PERSON><PERSON>red", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "653f7d25b93c3f7f51a8994addaf8a229de022a7", "tarball": "https://registry.npmjs.org/@types/send/-/send-0.14.5.tgz", "fileCount": 4, "integrity": "sha512-0mwoiK3DXXBu0GIfo+jBv4Wo5s1AcsxdpdwNUtflKm99VEMvmBPJ+/NBNRZy2R5JEYfWL/u4nAHuTUTA3wFecQ==", "signatures": [{"sig": "MEUCIF8uTkyKS5IY4Mh5aZGVVM0rOdGgbj2mv7isWWcOQozzAiEAqBlHln0slLGvrV+mo+TzLdrHmvLTRzdhsEDwxn4aj9E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9459, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdWffxCRA9TVsSAnZWagAAcw8P/2xlZh5Bg9aXRpMZQcPv\njPEo+xMqw82I+sEr8YzXZ2cqrpWeoxwT0OPVZVFxXDh8FWeFKdY8EkZEbfgu\nlLkd9vfoLRTJX8YDalSB6TzyrbcYjFMAGei0D53uuEEW0ga5BvVU9VP+zPHP\nZHR1lIRIBAiDTgvErtU9cioBbc/wJaiTiVIJ8cJwu3YWbyaB9LTrYm3ZTf3F\nl84/SXcUplFehuNqixaPwvQP01BCRbE2FskEgkOI+D3s6XI1ZsFPK3jyO1tC\n8wKMcyqaEzPqnoWt6JD8qIlPUE3cb+nWY9x4m4r5umBDrmfRrYbzjaOiorbD\nl2/g32eUkIZKTPGOYYsgFpW+wJzlr1QuqL+YWLoOiUlR1JycJXQV73dORaNj\nfzsFxTsIXlAIcPLpyA09b4Rw7dujl6dq6/1zcTGsSfHpO3y19zqEx+7u2kw9\nNsni25BsFCgzh8LbK9iTjWIjAJjytBr9NXZWeR4tQnyuT8U3D4WoTehuOvRT\nHOzi+Lbpdsucuam0poJjfr5l7l3ZLdjadbf3+OQ5at9eh7SgVeUAX/zagd5b\n8LyDJ8Uj1E+CbjV0IYrdzLj3tFaZO8ukl+q3LtdF7mW4BX5K/r5WYWZzElrQ\nEu6BPJ0Ke9jQ2PEB1eu0RtEW4XfN7VNd/9KaPt7QH1Dz6s3q7KXq7TLMAy53\nxSm9\r\n=W20J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/send"}, "description": "TypeScript definitions for send", "directories": {}, "dependencies": {"@types/mime": "*", "@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/send_0.14.5_1566177265137_0.8479508851072963", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "9fca1e2e6eb38a9f076dd891f1e3e5cda42daf66b911bbb9716d83c43e3eb0cf"}, "0.14.6": {"name": "@types/send", "version": "0.14.6", "license": "MIT", "_id": "@types/send@0.14.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/<PERSON><PERSON><PERSON>red", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "6ca7a80fefde9602a66e8102636c179ec7257459", "tarball": "https://registry.npmjs.org/@types/send/-/send-0.14.6.tgz", "fileCount": 4, "integrity": "sha512-umNRC1v5RoxWqryLeugCPMug4txOqFaeCUJjSuEubMhtR2zFw6ZbuSMZCnd5Y6137g+mxSrOVdB1YGkjAnMzVA==", "signatures": [{"sig": "MEUCIQDF86ZXU6Q06pUnbZ/SalhlTX94BIfeGBmnoIA6CBmF6gIgaeTNrkrqB5lo526RYhH6cvZM3rDKA9/So6IoNcfSQ5A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgBZyoCRA9TVsSAnZWagAA9nUP/3D9lGZnDCvpQAmGAtp6\nyOZlSa+ZSSlymQ17b/UAGYllha506aE6eEWJ7ISyd5poI8NMyAdQR5hrpFhV\nY+u1cYk2dTcnMca9V/47W8/iVaBuWM9GT7ud3LR8hk7woXjDOGdbl9YBD0Mo\nplGdSW/hTt/JJrXg8VnOmF4bPH5qvj4WyPWGHMWg/Zvk34kYXvrhgTDY3Cie\nJPKDjW2MKeWBLGLH6iRHkKuCUb+Nx4pv/9BV+vKGYcZrbOGy0P6KbGytwUQm\nnYj2mroi7Db+asFTc9vYiutR2l2GHHLCfSVlIiUtG1hvlttBj7yxPRKQrZjH\naRADypB1gweMZtJ7Ye2z5M5cGmRZ/1l9iBbrrgKfK3zsdUgKbaX8KJlzwwj2\nKWCue+gACPpJByjXmzFN906QMFq55wiVIrkrJi4OrmeNMxICLK8QHA+PrRwL\n4HQ5q41urZ8c3cZcE/71AgQPqu2VdyCwpnHdEnLYfadUDh+LgFh/32ucqxDw\n8CEWc03h+TNz3c3wM6BmBFeFNGMAepei4ZDPiFXy25UxdAFXFhIivf9Ej01G\n00kI+QNR+31EDIJJSp9znzMUHshLYM37j59GGFD60yFKsaFR12JEDgXGeOUT\n2G5bQLvVxV4mVPiprk4hTbp6WNu+U36yW30X0RRJdFpfPhHZyXArTfVTos5a\n3cRZ\r\n=n+j6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/send"}, "description": "TypeScript definitions for send", "directories": {}, "dependencies": {"@types/mime": "^1", "@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/send_0.14.6_1610980520569_0.9750828599482684", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d062ba39e87ba4b2e163fa7ebfdb551a95f897b821cb72a4586631329e0f7ae6"}, "0.14.7": {"name": "@types/send", "version": "0.14.7", "license": "MIT", "_id": "@types/send@0.14.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/<PERSON><PERSON><PERSON>red", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "ee6224edd5a593d7f553235f350569accb56a4af", "tarball": "https://registry.npmjs.org/@types/send/-/send-0.14.7.tgz", "fileCount": 4, "integrity": "sha512-WCUMbzWW1sTEZX31cRMcBxflUXX/JmAYejhjxXtrLGn+vd/yyFjHh/F9FIigAEjE2LauhfH94BT7NJj9Ru2Wlg==", "signatures": [{"sig": "MEUCIQD5U0zRw+g4sZrFM1NP3Ntoqp0OgjY7rlrBLTVkWEb1zQIgcbWObHFtZIGDETJ8Dp2P6kf6oRhGi67eDTkSKVekKWk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9191, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW/W9CRA9TVsSAnZWagAAA98P/1kDnAIvJJQRzBVHtKF0\nppIRre9ddUiYkgJCaVzTEDP9ngzVQ1zvRAFdm78BRtvnPMcOgddCsLU+Lv0A\ntRIH+I9JxbuGgsWhH/feHamZjDcbEjPhMSIVd2/QpMN7DBqwD6IX0ZtD9X1V\nMreb26itzH/6vx4iNDmBW9gsv+tQJgGFaxyjGcv0vA9+S/wcetEtSTta8s+y\nSyg9JqVb2kHnmpUx303lCU/lLRTgmxEGL9g4U9koexdORnk48ikzW9RPjIEP\nSMb+oWfkbUKhZw2kjAIuDoF8GaAdBIE6EVfsuvfVIn8tbsAzDmhWWZmSXXQS\npGuRXu8GC+HIxJveey90FpNogCT33DMbxh4AhApENbVVGQh40EOlcOCuHld/\ntQeKd9UPKMD7QXEI3d1CrOPm4Z37Lvrx9Y1L4/a4i8OtJAjcS0DfUZ77lMDI\nZE1kZIHWGSP7eawdO52PdS3VJ8sX4maM2wJcKq4vJPF67F63xsC6L0XJHBjT\nIInZ2I5WkqP9SxEkn1SDuGrKBKN3RSbrD2X0iyTO9u9sLxlBupuh5m76LZnk\nodAzUJNMf4sTCnG01cvEzsNXxoHlInvVWj6Du/+7cXb0bkNa+9Zktk23cljS\n0wwL4xRz+ivmwk5DthCOYrqVi0I3aSeHXS4LnLeBGt0u+RF0W7fY0baP+h6W\nF/Y2\r\n=ThEh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/send"}, "description": "TypeScript definitions for send", "directories": {}, "dependencies": {"@types/mime": "^1", "@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.5", "_npmOperationalInternal": {"tmp": "tmp/send_0.14.7_1616639420581_0.7989054740340431", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "2d35bf887fe88ae7cd276e93b75f6be31bd04999dc4fb1923bd5cc0ef1745b07"}, "0.17.0": {"name": "@types/send", "version": "0.17.0", "license": "MIT", "_id": "@types/send@0.17.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/<PERSON><PERSON><PERSON>red", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/send", "dist": {"shasum": "2c8cd6a4a97efd0ae54be3bc16647d864be2f94a", "tarball": "https://registry.npmjs.org/@types/send/-/send-0.17.0.tgz", "fileCount": 4, "integrity": "sha512-Kh5ghRpkAcjAak9sCns9XTu9xL/hzJHMQIEi2xLZ8kJ1JVKPly0E0NA7snmSk9Ah7BndN8jEnltzz2VPjBMQIg==", "signatures": [{"sig": "MEYCIQD0land4a5tyHdsV0qCw/SaTO4dJx65GQgbjmCQ8C8g5QIhANWyoaYTM+r5DdTF0SQ5mFjwd7Q1+9vc2Bzf+mJIcMxw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10016, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwSxJCRA9TVsSAnZWagAAtV4P/jGjL5rJbCZmDys0VoLm\n7RP8DVUVbalSnuqNAc8kUkkAYIJB/IXV7zwU+3H7SEXbQtZZuOyjjdXHSP3z\n3CwXt/xldPjPrIUiH1CeVLwJB3ROPlqzGtSEMCIOWyWFDV7WC/4uOtJPZpKc\nPHDiKG9gKAYvwFwKdKnAxQDOg9oolcqVCbdB3IiebpWgFTa3IakWMDPHHGF9\nKbQ/3JcM7ONMkk2/P06QBELYX0U7ft8hBzIukLxatWd1cA4EGBxkhGQXzDOm\n/AmQ7PcKDJcQYNbovjN6HeNa9eJla5Jhhtmw7UB7kubQDuWiEnsiIHOi4VLw\nxzd/etcLyp6YKks2yqUF2AlxPKh/49W9/OOgwvN8mkAH7B5fSNe3IKgNTSBJ\ntTxdxfscGyfkqQU5NKe0p+QNXFQVeNamk+2ViPogCas67Fk7Vc3Nyg9rjUlF\n5W0YhpNYCn2sWUZctPE9LCWbfPTQgfoLbW93OxvSmxCEr3zmEbq9eZ8S42IF\nBcXQeJDwm0v1PucJbwy4Bp4fHDhEaH8RJQxUEu1iZ0tso9MNKUUQZZ9KXtxb\nmUvk+3PXQBS4/OJk7wFpQzepj36pQ/AGDYzuas1Yo4RokcxaywpWf8F1v+My\nbxtK46Pl0cJWt+kjthWE4hmHUaqj+lg6rAHRG7hjmJZmr4mRyboQIwW6TmRh\nXu1J\r\n=F4U3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/send"}, "description": "TypeScript definitions for send", "directories": {}, "dependencies": {"@types/mime": "^1", "@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/send_0.17.0_1623272521808_0.4190361627927357", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "64e2ab623fd294bc20d3fb7a5bfac6c172deb0cfb95cf695f47005195fe69919"}, "0.17.1": {"name": "@types/send", "version": "0.17.1", "license": "MIT", "_id": "@types/send@0.17.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/<PERSON><PERSON><PERSON>red", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/send", "dist": {"shasum": "ed4932b8a2a805f1fe362a70f4e62d0ac994e301", "tarball": "https://registry.npmjs.org/@types/send/-/send-0.17.1.tgz", "fileCount": 4, "integrity": "sha512-Cwo8LE/0rnvX7kIIa3QHCkcuF21c05Ayb0ZfxPiv0W8VRiZiNW/WuRupHKpqqGVGf7SUA44QSOUKaEd9lIrd/Q==", "signatures": [{"sig": "MEUCIH05Hj4IYMNqrhTEOL2YBZ+PUClD41adsCs4dd5+VoTBAiEAz+28FuhRlSax0s0mnCEf7xiha2yWjUXhkYWaOC11PnE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5IstCRA9TVsSAnZWagAAjCwQAJmTiBLMSB/HCPYn7mfj\nyNy8YspBp8mj5ApCREer/WJKvKfD5IlAYsLqM2S6XwuB4i+Q3TUjRv2qSVrS\nMn3wbtgg1fwy2Fm98CNB3RZdfUZe/Ispsomd9XJaeVSZZiTMbFCLxDEMR7Oo\nzycpwBnC0fiXefJcQfnoadJAgGCvMPEm6tHtSqFwO7QiGvxHi6ybsF5SVIFs\niLhdlW9rQAUzKAQwxAezyDvft+w08VPyOijwgFob6hOdqgwa586EFVkg4Npv\n4NZVkruccGpkylrrX71WpVzE/gGyAeD0PFqWb4kfjnhBl23XZLYBfX2q7qsH\n4dbxXX46zmLWUKHUP+8VaX+5HrfeqWV7UPevC5I9PoM4m+jxSTF8rY2UlFyg\ncCdXEFF6l1rWW02xDWWIknVd20NjFa376R7F5EPUNEcuT7rwNincVGEa2V2H\nDAEj4uq4Q7ZUCReXgT5SoZDoHsXaITPUmSDZPRKxPvVw9H2OQ4cvUMJohvcR\n0UghAdJXKlzCdMafIkknChQFJGrRTTNYGXaPdJqGadeM+lQQkZgVFAWXLqM6\nI9ZCfG8S//+lc10VgsxAD2GDYTFrPz6pgUuB5P8gfDlzOZjfL7/m7TaO+StU\nXZvfsk2vFZOAR9M2xUS7TW5qbIPb/1Tbq8ZvJ+hASaB72MYFqW5E4iYpaPoc\niKER\r\n=QXMa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/send"}, "description": "TypeScript definitions for send", "directories": {}, "dependencies": {"@types/mime": "^1", "@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/send_0.17.1_1625590572634_0.09870693854508517", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "11d86e6b6fa2c78b8659b4ad9d2f295175d5f4bd5fa11649b26a8c945ee3ffb3"}, "0.17.2": {"name": "@types/send", "version": "0.17.2", "license": "MIT", "_id": "@types/send@0.17.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/<PERSON><PERSON><PERSON>red", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/send", "dist": {"shasum": "af78a4495e3c2b79bfbdac3955fdd50e03cc98f2", "tarball": "https://registry.npmjs.org/@types/send/-/send-0.17.2.tgz", "fileCount": 5, "integrity": "sha512-aAG6yRf6r0wQ29bkS+x97BIs64ZLxeE/ARwyS6wrldMm3C1MdKwCcnnEwMC1slI8wuxJOpiUH9MioC0A0i+GJw==", "signatures": [{"sig": "MEQCIE5NZE3QTVXvgYsDbiZAPTXMekxYn81qDzQu+cWBdHE/AiALqDUJpA4yKoh96bQH+to+dRaawmeoWoQxLLNDNXTgrg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10160}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/send"}, "description": "TypeScript definitions for send", "directories": {}, "dependencies": {"@types/mime": "^1", "@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/send_0.17.2_1695651671143_0.6601859790320992", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d502699bacac8ea6454e18081b60116513d3c67414f72720bddb40152b209089"}, "0.17.3": {"name": "@types/send", "version": "0.17.3", "license": "MIT", "_id": "@types/send@0.17.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/<PERSON><PERSON><PERSON>red", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/send", "dist": {"shasum": "81b2ea5a3a18aad357405af2d643ccbe5a09020b", "tarball": "https://registry.npmjs.org/@types/send/-/send-0.17.3.tgz", "fileCount": 5, "integrity": "sha512-/7fKxvKUoETxjFUsuFlPB9YndePpxxRAOfGC/yJdc9kTjTeP5kRCTzfnE8kPUKCeyiyIZu0YQ76s50hCedI1ug==", "signatures": [{"sig": "MEUCIQC8BZC5rsn7dZUi6T3N+E3GyIZJYI/PQVUJcItd4PkMYQIgOF+D/1ZG6foMK09Ud4Fil12lwOuFcu1+teyFrL90fNI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9849}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/send"}, "description": "TypeScript definitions for send", "directories": {}, "dependencies": {"@types/mime": "^1", "@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/send_0.17.3_1697640010297_0.26917547694959", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "30288ea815b270cd0f4399c01e7f2bbb9317a92dad191638348b0c2fa39494d4"}, "0.17.4": {"name": "@types/send", "version": "0.17.4", "license": "MIT", "_id": "@types/send@0.17.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/<PERSON><PERSON><PERSON>red", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/send", "dist": {"shasum": "6619cd24e7270793702e4e6a4b958a9010cfc57a", "tarball": "https://registry.npmjs.org/@types/send/-/send-0.17.4.tgz", "fileCount": 5, "integrity": "sha512-x2EM6TJOybec7c52BX0ZspPodMsQUd5L6PRwOunVyVUhXiBSKf3AezDL8Dgvgt5o0UfKNfuA0eMLr2wLT4AiBA==", "signatures": [{"sig": "MEUCIQDvt9xS2WVYez31+8VIUOO/FsTJnFstR/TtKfqmeeYejAIgFjPqmvUOOhxDgn/ibCwE4F1HIZLWx7aZan3owpw6ZpQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9849}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/send"}, "description": "TypeScript definitions for send", "directories": {}, "dependencies": {"@types/mime": "^1", "@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/send_0.17.4_1699373948331_0.4686270305488913", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "80c2c0bda207c1a802cfc46bf20e4f774429c8ae1e9808128c20468d438b895c"}, "0.17.5": {"name": "@types/send", "version": "0.17.5", "license": "MIT", "_id": "@types/send@0.17.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/<PERSON><PERSON><PERSON>red", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/send", "dist": {"shasum": "d991d4f2b16f2b1ef497131f00a9114290791e74", "tarball": "https://registry.npmjs.org/@types/send/-/send-0.17.5.tgz", "fileCount": 5, "integrity": "sha512-z6F2D3cOStZvuk2SaP6YrwkNO65iTZcwA2ZkSABegdkAh/lf+Aa/YQndZVfmEXT5vgAp6zv06VQ3ejSVjAny4w==", "signatures": [{"sig": "MEYCIQD7GbzzEwWbF8QgrlJEvk6dJKnXPWVpBXsth1w6LPr+dwIhALEE3dEwIAsYa1ZRUygdshS/JXEKvp1AAIb2I3AJeYI+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10089}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/send"}, "description": "TypeScript definitions for send", "directories": {}, "dependencies": {"@types/mime": "^1", "@types/node": "*"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/send_0.17.5_1749262730166_0.9773396312123068", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "8088c014f9e7c4c38cfce1ccf4f348a612ebe7009a733d262a12236edd4e90d7"}}, "time": {"created": "2016-10-26T19:28:28.620Z", "modified": "2025-06-07T02:18:56.934Z", "0.14.1": "2016-10-26T19:28:28.620Z", "0.14.2": "2016-11-04T23:19:55.271Z", "0.14.3": "2017-07-05T14:31:47.938Z", "0.14.4": "2017-11-09T15:18:01.781Z", "0.14.5": "2019-08-19T01:14:25.259Z", "0.14.6": "2021-01-18T14:35:20.668Z", "0.14.7": "2021-03-25T02:30:20.722Z", "0.17.0": "2021-06-09T21:02:01.963Z", "0.17.1": "2021-07-06T16:56:12.800Z", "0.17.2": "2023-09-25T14:21:11.357Z", "0.17.3": "2023-10-18T14:40:10.508Z", "0.17.4": "2023-11-07T16:19:08.727Z", "0.17.5": "2025-06-07T02:18:50.367Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/send", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/send"}, "description": "TypeScript definitions for send", "contributors": [{"url": "https://github.com/<PERSON><PERSON><PERSON>red", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}