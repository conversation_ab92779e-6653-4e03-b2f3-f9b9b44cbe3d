
0ed4c402a51e6299e8d613762b8a856dda9ac1c9	{"key":"make-fetch-happen:request-cache:https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz","integrity":"sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==","time":1753582250760,"size":1528,"metadata":{"time":1753582250243,"url":"https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz","reqHeaders":{},"resHeaders":{"cache-control":"public, must-revalidate, max-age=31557600","content-type":"application/octet-stream","date":"Sun, 27 Jul 2025 02:10:50 GMT","etag":"\"f07933e3b37ddc954a8b88c9add93a0c\"","last-modified":"Tue, 30 Jun 2020 13:11:53 GMT","vary":"Accept-Encoding"},"options":{"compress":true}}}