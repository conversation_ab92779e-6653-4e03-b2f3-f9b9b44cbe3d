{"_id": "ts-mixer", "_rev": "43-7ec4b73a17e9a0cc3c31215ffcbae6c7", "name": "ts-mixer", "dist-tags": {"latest": "6.0.4", "beta": "6.0.0-beta.0"}, "versions": {"1.0.0": {"name": "ts-mixer", "version": "1.0.0", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/mixins.js", "module": "dist/mixins.js", "types": "dist/mixins.d.ts", "scripts": {"build": "tsc", "build-clean": "rm -rf ./dist && npm run build && git add ./dist", "watch": "npm run build -- -w", "lint": "eslint src/**/*.ts", "test": "nyc mocha --recursive -r ts-node/register test/**/*.test.*", "coverage": "nyc report", "coveralls": "cat ./coverage/lcov.info | coveralls", "version": "npm run build-clean"}, "nyc": {"extension": [".ts"], "include": ["./src/**/*.ts"], "reporter": ["html"], "all": true}, "dependencies": {"typescript": "^3.1.1"}, "devDependencies": {"@types/chai": "^4.1.6", "@types/mocha": "^5.2.5", "@types/node": "^10.11.4", "chai": "^4.2.0", "coveralls": "^3.0.2", "eslint": "^5.7.0", "mocha": "^5.2.0", "nyc": "12.x.x", "ts-node": "^7.0.1", "typescript-eslint-parser": "^20.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "gitHead": "2aeedddee37bf26939d36d5a4318eeceb878ae4e", "_id": "ts-mixer@1.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-aTH2HXzvhHmODQ1ybgbejphQ/nFyvrx02ABdUeP2zY9aQoRsXvu8XVhQoKQ79JC0PesmZxAwTT+trj+dlGOSJQ==", "shasum": "08a56c1fcd6933b1d4ece98274c2a6de6b957e2d", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-1.0.0.tgz", "fileCount": 5, "unpackedSize": 12629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzUWNCRA9TVsSAnZWagAACucP/RFHFItv5d9OLAx4I4Om\ntzl/az84Y403RaX6HMylkdC6fbuLa1Go08g2fGnlkAw/lpT3eKvqmx94e6LH\nCFK1gQZEVgCoe6EGuztU1rsinDNmrofnfow4HE25RFg5T6Mi97Q02gTZuA8+\nJ1LG9nerNostO9804/6xw7lp0ETX+ht5+GdyxJ/WrQ+A8h2k4022WJ2ofHDg\nJa6/3b5P2veCYbzslWCSDbOXyODTF8dfVmR/UAO1a7MdiOKGlAz2tMsCph1W\n/3oJ4s9E3yckdTDAEn2egZuBP8aF5xpzUHXV8uXU/1pqJ75Ldl4ZCKZkJChm\nGesZfLvimp25Gq76kv726tNa8wH0ThGz3m23Bc0Mm4O/1kphiRiBpL3g6rxr\nGkCc/D2GQhINKRlLaSaeODw5ogV2WxlG7wX6sEM8gGIZD4W8GFSpeduUXEHV\n445oXSLcIvvSYi2doT+bWH8uxrAbxPn6HBnOFpr/A3MC2+sOdTpoRwRvnuww\nIjdXvxvt1+H2919asoa9DzQ6Eu2HF8CWBGZFMKAvOEeEXKYUTSKWOkss6Nc9\nJphlAvu5VSU1tuD/2IaapxeJUtDrBSVKefrVjyoKp01uMdCwVlHUdN9SLAUW\nzE6inOWBsl3d8G6DQDABv2CZMhQYoqyc5SiFaTiMPomi1jkMBYfzfCgNR6r3\n9ruo\r\n=jkEH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCbGXzpraBVnjzBwAj0BI53oDH3D/tH23SCFWQ+nj8gzAIgEQ7EvaQ7osnRq6u4PHVdiwvreRvHSg6kbr0ONszmzxc="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_1.0.0_1540179340741_0.75589694459296"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "ts-mixer", "version": "1.0.1", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/mixins.js", "module": "dist/mixins.js", "types": "dist/mixins.d.ts", "scripts": {"build": "tsc", "build-clean": "rm -rf ./dist && npm run build", "watch": "npm run build -- -w", "lint": "eslint src/**/*.ts", "test": "nyc mocha --recursive -r ts-node/register test/**/*.test.*", "coverage": "nyc report", "coveralls": "cat ./coverage/lcov.info | coveralls", "version": "npm run build-clean"}, "nyc": {"extension": [".ts"], "include": ["./src/**/*.ts"], "reporter": ["html", "lcov"], "all": true}, "dependencies": {}, "devDependencies": {"@types/chai": "^4.1.6", "@types/mocha": "^5.2.5", "@types/node": "^10.11.4", "chai": "^4.2.0", "coveralls": "^3.0.2", "eslint": "^5.7.0", "mocha": "^5.2.0", "nyc": "12.x.x", "ts-node": "^7.0.1", "typescript": "^3.1.1", "typescript-eslint-parser": "^20.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "gitHead": "3b257a2778093125b0a22b9715508dba7b677051", "_id": "ts-mixer@1.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-OCGufKkQrb0uk/b9qcysT2K4Ldch6TOrqyr7P0Y0vyxhR0lSn+F5qyobC4JSkx4iJlrGRwMhLJx36lnIK10n/w==", "shasum": "7e43f97aea04a56462e99dc80574f226c3189bbc", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-1.0.1.tgz", "fileCount": 3, "unpackedSize": 7886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzlrLCRA9TVsSAnZWagAAfP4QAJHXzbyTepzkjXuMoKrF\nNUQftePIHu6R+2JA9bh1/m9NGoLrf8fBOIEqtTn6ny/RcR5YI2nNl+JdJelF\niyXcs2xMAZ1k/lxhrIOP9RVw9LqPxkk65pgbaM8RsoGqPrv7dp+EL3ArlHEO\na9ZYN2dDaxQKIpT3d9/61+BN2ljjkrkcVimQCU2LnxPRq4MwPt12dhFUIk6p\nEd52gGqSeSF0VumVfnJ+EmjLX13eDA81Vx8g8XvXD45W8auT37ByKfgTLe2R\nKrh5brYgv0pnShMYikkc3E4Fnqx72lwMU44QNlQ1dBhIEpAgkAWK6eTaWvF6\nuL/P1U3W9jp5exRzAaIetqWGCb4svVJTqnVOJiuR9bKFSYSjPnhYfAS39n5V\nA60eHTCVm09hDleEaW9bQwu4lX4RZKGTLzUClOzS4kRZ5J+MfuZEz8ppKay4\nsnPTH041nfoLL9BLkloswnk0BScgVVhDDSbylX2J/nuqRnfoVHTyhYRCYC1F\nWSWGVKJs183Ato9/VHbWXwsONYHpzV31a6gk+8cV8jixKBdPQkXA3p9YXC5b\nvDv9f2B8lQN6wIBYKKHc9JePM1ls8Q87h9imVQQVpHTcYQ/0dgVqMrsI790D\nE0dqGDnWa+RPxlYS9ApqXoYlNDQdlqGoiNLHum8q9y++KgVr5tJ520eS+h6o\nUT6h\r\n=IYjc\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEpVquamhFhWJNakQqkNuHiJjOc8q2K8zboYylnSuq0tAiAQXyE8aC2JEjk9fivvjZsIhzb13h/H6DIRzMKv0R22nQ=="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_1.0.1_1540250314358_0.327887305030923"}, "_hasShrinkwrap": false}, "1.0.2": {"name": "ts-mixer", "version": "1.0.2", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/mixins.js", "module": "dist/mixins.js", "types": "dist/mixins.d.ts", "scripts": {"build": "tsc", "build-clean": "rm -rf ./dist && npm run build && git add ./dist", "watch": "npm run build -- -w", "lint": "eslint src/**/*.ts", "test": "nyc mocha --recursive -r ts-node/register test/**/*.test.*", "coverage": "nyc report", "coveralls": "cat ./coverage/lcov.info | coveralls", "version": "npm run build-clean"}, "nyc": {"extension": [".ts"], "include": ["./src/**/*.ts"], "reporter": ["html", "lcov"], "all": true}, "dependencies": {}, "devDependencies": {"@types/chai": "^4.1.6", "@types/mocha": "^5.2.5", "@types/node": "^10.11.4", "chai": "^4.2.0", "coveralls": "^3.0.2", "eslint": "^5.7.0", "mocha": "^5.2.0", "nyc": "12.x.x", "ts-node": "^7.0.1", "typescript": "^3.1.1", "typescript-eslint-parser": "^20.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "gitHead": "773836d7816444d5d367b88a616a06bbcce82dfb", "_id": "ts-mixer@1.0.2", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-TOY/yg3vfHpJWlGB+ah7q99t5Zox4WQhbAYxoP7mYcZD85/vtTu0Lv4L/WqgIZ5ZAW7p8wrzDb+BHWmHfqs7BA==", "shasum": "cd0546a14a5c896a63b61790ab23cc5c346b6cf0", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-1.0.2.tgz", "fileCount": 5, "unpackedSize": 12819, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzlwcCRA9TVsSAnZWagAAPIQP/3mnTm0KzDIQ2WOvzIZE\n8kcaqcGGc5EWMrDADYwhU8zTxhwhbMcDLQTVz688nRvGXqaoV2IJRuxPFued\nV3/GPKgf7Hwk+BUWt9+r71R9jhcAVN3pl8RVmwuOMS14hb4z11xwo6cou9YW\nxVqu823bm5zKqCI0cBkj/LUluCI1Z12SW/gaUVtp23Gcy0wWSkXNG3+u3UgF\nKPAvJsP+kiKA3ywZuVwJFQXTth5RZ1XzbPGELgyCWo/iqostTrg0psfmunt7\n5vFdjQZrTXwvj072tgjL2965ZdQ4ZNg7puXjvM1oLTDLBRznAGegza1weqa3\nUmnRFmlY35AlZCVpIRUHTXamrpzhY/a7WbpeulRycm/oC3sW911wJ0U1i76Z\nogTXKj5mIXvauq1juKRv18wSR9HMD1bGtaV4oFIWOggI7QNln39HJq2Jj411\njg4yI75v3p3N3NGEoS4oE7h/Z6L4dr9KaDkTE4G37n5dNhqbTvti3a5OYOQq\n1m9t4lIW3xfH6s/CGPwctIkNVnFjvVhwOPUUYwoVGhzR8EiRsNlWzInGmG97\nAl4J+J8fL0O23BYqtpAsaAqP8SW3qAsNeF19HL61RhUxfTZA8JvPZb+W3QrX\n95no4PHm/7xUZ72XmkgbJyhud5f/TE+FQQ9yzMW96QIAeKHPVs9ToHq95YEn\nJyBa\r\n=RIrN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDtezgJYM5U4Qb/zjC1FHFFnhTwv7fqiPDH8TxIbx5abAIhAKyX/1srLfNSJVZkFqhbP/ckK4BqKvqL3FAaNgJdsJ3+"}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_1.0.2_1540250651586_0.20042518943708498"}, "_hasShrinkwrap": false}, "1.0.3": {"name": "ts-mixer", "version": "1.0.3", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/mixins.js", "module": "dist/mixins.js", "types": "dist/mixins.d.ts", "scripts": {"build": "tsc", "build-clean": "rm -rf ./dist && npm run build && git add ./dist", "watch": "npm run build -- -w", "lint": "eslint src/**/*.ts", "test": "nyc mocha --recursive -r ts-node/register test/**/*.test.*", "coverage": "nyc report", "coveralls": "cat ./coverage/lcov.info | coveralls", "version": "npm run build-clean"}, "nyc": {"extension": [".ts"], "include": ["./src/**/*.ts"], "reporter": ["html", "lcov"], "all": true}, "dependencies": {}, "devDependencies": {"@types/chai": "^4.1.6", "@types/mocha": "^5.2.5", "@types/node": "^10.11.4", "chai": "^4.2.0", "coveralls": "^3.0.2", "eslint": "^5.7.0", "mocha": "^5.2.0", "nyc": "12.x.x", "ts-node": "^7.0.1", "typescript": "^3.1.1", "typescript-eslint-parser": "^20.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "gitHead": "8f1435d4c6f4cd6b5b9a9574f8ebe58a143a030c", "_id": "ts-mixer@1.0.3", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Vg4JsG6LCK/6vI1tcI0lNpAxrknekHnyD6Zp292BlbMTLGj9IVY3dccDHxwrJiie6ALA8I1Cdv+dSuqu0uAWEg==", "shasum": "b8c4ff3b221370657e5755d97ec9f05fec54f140", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-1.0.3.tgz", "fileCount": 5, "unpackedSize": 13340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzow0CRA9TVsSAnZWagAAsCAP/0QnYHeznfhEkCxsLxU3\n0WR7zMSTmZ4HHhTh1VnsOk/pyVTH8P3Q8bJuiRvOW7BT5W2aESvu6VrVh5ke\neeyDLA6hI+hfxEGZtCY1ZvqXAJK1/IQNsv0WrRPli5kgc2pU85dN1tKMv50F\nkgJ0Z5v2+NDBNrofSlw54Lt5kpkgsJUYX/XQX4sUnEKiss/2WeZxKD2Eo+ca\noAG6S2R5z1JyctcNby8Ym167M5/ftQig3j+pteH0A5OtZGTP6NPdLlr/r1f5\n5B89y/Z4dWSuBA/eo2x2L6WMc5qfnv56BuNpm/s3oFODx1toPBFRDgMpP+Sh\nO6OPOj4+n0l5zUW0oJZt14qXMPEfGQHOLwMm19kwpOuTWLIi9LgARepxoq/c\nmYs6Be/YyQW9mwWMBkGhjBr6lxJl4K94lF9qLrCqxYpxYSzSUAQ9kNwFeQcM\n9s9gbOLBOTJS9iBWGJUinVXkaevK9hctJt2mqTUfuT8bBEAK+e3jRVmfNnp0\ndyDhWSMMSekEq0dbrjH2xCi0hqrJcDhF/wdDBlQyZe9a6t7xVMKs/Gx0LUJL\nnX2WGwLvWx/1nHoWu2cMu9xBWNp6V4jZItNRZ3dvYBsMLF6EQb9G6vhYxS1B\nErbztvBilb9wMPkdNXFTr/gfhRqbjWJC2Ami224BaBJf6usW6aQSA9GPYN4/\ns+gS\r\n=Rd04\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDyh5LKK1/99PXhr/5ykMTmsSr4mgkoEk5rYspTpmpEmgIgId+PNIldbmI1NbLvowHkvyc7bXPxIbj0n07BADm+maY="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_1.0.3_1540262964221_0.1841659496443342"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "ts-mixer", "version": "1.1.0", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/mixins.js", "module": "dist/mixins.js", "types": "dist/mixins.d.ts", "scripts": {"build": "tsc", "build-clean": "rm -rf ./dist && npm run build && git add ./dist", "watch": "npm run build -- -w", "lint": "eslint src/**/*.ts", "test": "nyc mocha --recursive -r ts-node/register test/**/*.test.*", "coverage": "nyc report", "coveralls": "cat ./coverage/lcov.info | coveralls", "version": "npm run build-clean"}, "nyc": {"extension": [".ts"], "include": ["./src/**/*.ts"], "reporter": ["html", "lcov"], "all": true}, "dependencies": {}, "devDependencies": {"@types/chai": "^4.1.6", "@types/mocha": "^5.2.5", "@types/node": "^10.11.4", "chai": "^4.2.0", "coveralls": "^3.0.2", "eslint": "^5.7.0", "mocha": "^5.2.0", "nyc": "12.x.x", "ts-node": "^7.0.1", "typescript": "^3.1.1", "typescript-eslint-parser": "^20.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "gitHead": "ae889b37153b1ce2ffc5c05092a715057ea25ee0", "_id": "ts-mixer@1.1.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-OhOOLMIUi5Yr8r1y3F081IlQJd1AJ7PadLXfcMjn+PgAMxXJJBpYb0ap0+BOKzD9J+nPxpo3qaptfLPB4q5l8g==", "shasum": "10822f281e70b6a3afb58e73690f2b332c661ffe", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-1.1.0.tgz", "fileCount": 5, "unpackedSize": 14531, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0o4ICRA9TVsSAnZWagAAP+MP/2N04ghif8g6pfNQ1NLz\nDrj/weUAoR1DBrqnsj9iXfpLIM+BqoJPFoIK4ZyG4rgOlNgQFAjYg8suxGkJ\nwdaycdbrNZecFHCyWVitX1upS3SdmrrfM50TWMvCKCILVaWOqQMxcnatbp5z\nuNCXi1mX5AnEd6fH3MOP2BfjouSuRA7v2clOLs3nNnhHZTFAANLN1RcuNdbr\nO811UmeBefgmDrA0ef7ZhrFEnI1l02hE9Vv7KzO2YZbOF4a679Pk5k7D2Wfu\nz4ZR2KQGWFYmLHcSdbs3Fg/ot6RmMl78cqjasuZN+5MYSlKzsSv5uC5Mm/01\natnbLLsEncGl3qT1yWiPbOSTQKFy7RVDUWbrGRUVokl6veIQjiYt0t/1iebL\nWGer4oH6Ef7EIJ8vN0sUFUzfPBfG+uVA36bazBuxOm7i/Qr5kfn4+soAy1YT\n/pO0I9fs7Y0TlcaD4jYpjpirap1JtgOzvvjLs6CnZCIWrgc2UYoZ4ntQeO5v\noLkhPuAsXxaNDOsSLOmKZ8MSMLrp7gCABNUHYCXmhJmECE58GDQdchNnYLYk\n83Q1wAdQj16CXt3hi7Al8SPkKZHsn00oXnqTF6w8v4LEn4fr2bLQJ2KjDR/G\n1YESqoacCSQzS5rcDXrr5dPPhNADlYKuGf3/TWTlIzd+3LBobm4lNn+4eAgC\nPoGt\r\n=Z6F3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD2FcJPEVc095zID1E1sQtIZF1RAg05c+bH+ikUMoFq0AIhAI3DEeH5EFFVRcHxzQneH88qHLRqYcd9Fo6pNXpYo1CW"}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_1.1.0_1540525575028_0.6816907279742408"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "ts-mixer", "version": "2.0.0", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/mixins.js", "module": "dist/mixins.js", "types": "dist/mixins.d.ts", "scripts": {"build": "tsc", "build-clean": "rm -rf ./dist && npm run build && git add ./dist", "watch": "npm run build -- -w", "lint": "eslint src/**/*.ts", "test": "nyc mocha --recursive -r ts-node/register test/**/*.test.*", "coverage": "nyc report", "coveralls": "cat ./coverage/lcov.info | coveralls", "version": "npm run build-clean"}, "nyc": {"extension": [".ts"], "include": ["./src/**/*.ts"], "reporter": ["html", "lcov"], "all": true}, "dependencies": {}, "devDependencies": {"@types/chai": "^4.1.6", "@types/mocha": "^5.2.5", "@types/node": "^10.11.4", "chai": "^4.2.0", "coveralls": "^3.0.2", "eslint": "^5.7.0", "mocha": "^5.2.0", "nyc": "12.x.x", "ts-node": "^7.0.1", "typescript": "^3.1.1", "typescript-eslint-parser": "^20.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "gitHead": "8355005b74afb1132ea9d11b4e6c8bffe73cfce1", "_id": "ts-mixer@2.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-I3s+UsUTRgB/QyZavEpPCNyda8Cm4quNxjT7aEiyFCPZoeecuAWsgopDMX+6QwN5LmGXd+NdTBIR/PB0WGDQbA==", "shasum": "4b1615e11f0b90b291f9540b1476efdd54e5ce1e", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-2.0.0.tgz", "fileCount": 5, "unpackedSize": 18839, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0+sGCRA9TVsSAnZWagAAeq4P/iRbsAPZK3+gmX4OG6M2\nI47hhRMeJQq4xh2DIHMKvognzaOAUCcYi0K1M78yYTPn4PNd91jSx+CAWaT/\ncWRDs8nG7+Mwjqit2EtKKtCkCKhL46iO4ZkysT3M/fiWpH5Wjp5LHK1XeflJ\ncohFfGd2ZKU3JO1D82Es+gcuihJnPsu/DxS91rjhINOB8srPIHF0S1yVzEcS\nH4rGPBxscsm38Z0qw0IhI/CbgjNUbmZ38v2ol8cBxvkXS+LpQLebIiAW2xF+\n+g7euZqZsDSQlPClQM3dkDiXQsP1lLb0scc9LYG4sr3leCFRbO/SNFknasYO\npA6CjAjtDT0H3x62L4D5ifpBBnQtDk6NU4u8D5RoI658AhaVri7Q3K4K2fg3\n73gb68Bi3u9E0suK3euP06C/qGgTAUDXzBDHwhmGpZyzO/Qohm+80NlV2YW+\nTOkUNEJXLsEzJ5eMwGMIVMwaBNdLMjXoRshCJ1mCe4CHNkSYWWLeAytKohxx\naaruehie0U7gjKID2nqN4ZYw113l0bcxYQ6HAFgBz0YkXYuhV4KdPPQ/k25n\nCEwTemPOmcylPu/YO7n0w6ZRfFpN+r6FOJq9eXnM9I5igviAMbew4kVgwFtn\n7ufisMHbAmwhtwdXh7lk04eJ3CI9/7ATs6PqkTAqlDY8jJ0wslQcrt5ISwEg\nGbXU\r\n=VSFb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEUSxm+rI5pwmSz94QzNPCpeiPYJ00FiksIqTDE0mXGlAiAudY47Q58b/lxnzcZfIFi/L2ttMKYvkxmXU9lHrUmd+w=="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_2.0.0_1540614917223_0.6248805714715029"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "ts-mixer", "version": "2.0.1", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/mixins.js", "module": "dist/mixins.js", "types": "dist/mixins.d.ts", "scripts": {"build": "tsc", "build-clean": "rm -rf ./dist && npm run build && git add ./dist", "watch": "npm run build -- -w", "lint": "eslint src/**/*.ts", "test": "nyc mocha --recursive -r ts-node/register test/**/*.test.*", "coverage": "nyc report", "coveralls": "cat ./coverage/lcov.info | coveralls", "version": "npm run build-clean"}, "nyc": {"extension": [".ts"], "include": ["./src/**/*.ts"], "reporter": ["html", "lcov"], "all": true}, "dependencies": {}, "devDependencies": {"@types/chai": "^4.1.6", "@types/mocha": "^5.2.5", "@types/node": "^10.11.4", "chai": "^4.2.0", "coveralls": "^3.0.2", "eslint": "^5.7.0", "mocha": "^5.2.0", "nyc": "12.x.x", "ts-node": "^7.0.1", "typescript": "^3.1.1", "typescript-eslint-parser": "^20.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "gitHead": "ee799c8ffdf35ace2fa6657cd4c64c2a8b31a3e7", "_id": "ts-mixer@2.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-qDFgwmioXLIEuLhb4g+hsSEG0vwkhKAdSdsRanVOYTSbWtgKDJSVweU8204PluaCPVAm46GIiqh1oFjWynGeow==", "shasum": "eb67c59b91382eb0c4c4cde4849a5deb9adbf194", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-2.0.1.tgz", "fileCount": 5, "unpackedSize": 18880, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0+vwCRA9TVsSAnZWagAAoUEP/iMN37zeRAo66HcZneqR\nE7Q7BCprDglpuYYeaMYL3ZPbv+iR2n2v2hnAATaURj1IidBE/z375teU+G65\ntC2CbridfzkqOEdh/YmIw96AfOUEf4S/f6dxVO2o4CfNpc7QJUMwHdx00LeT\nlfB65PeWrIYdqPR8bImLgQJF7AM2kTP0rTcOOGNYzPFJUM7RmFWQOZOUuOO9\noV/bOq+aOV2hC5PhRve75Eo1WWrwZu0NMAYxKxFSW24aj29uzR3osNbKt2Y1\nnK3Dd8K+gcaJOiSTWlSNFE3j8k8z9gsT3ezuY4D9ye1i+/5JZFU5wFDO3UOE\n5lT75jivOG5LV6N+meJVLFwf6/UK2duYiWJ7oC/fkI9RgDtagoEdpmXSW3OD\nGM2qi7x4jP58E8os26/ORTswPRxOB7oYX9TomnmYzk8obTSkrZ4hubs75+Uj\nJajjkb0C7j9wOknd2aT+PPHmg5ZYUvLwlqSZWg/RnsBWPuNxsK7ryafkAjBc\nxy6XSrNeq5brZ0dyc1L1KaRMZeQVVWvQ7eC7VUaevWvVq5TO4Uzj32j2RiRk\nQHdTVJDlJtRkVtc7td88nNX5kCTNrOkm/o4UrJK2wViWabVJp2larc6NQwEs\nuPNzWk+e8ynBkF+PrZJbngtPUnVoQQTOc46VYGGnu+HL0g2YihBaTt0wlCL4\n0olH\r\n=YF1+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDkZchkqbCU+7BFsFsaphO1noCzDGKxYSjSSr4SH/6RcgIhAK0rMpye8FVcM0YaH+hNFjO3pCqtqahgpulu7P2rd8gu"}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_2.0.1_1540615151325_0.9876877397741026"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "ts-mixer", "version": "2.0.2", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "keywords": ["typescript", "mixins"], "main": "dist/mixins.js", "module": "dist/mixins.js", "types": "dist/mixins.d.ts", "scripts": {"build": "tsc", "build-clean": "rm -rf ./dist && npm run build && git add ./dist", "watch": "npm run build -- -w", "lint": "eslint src/**/*.ts", "test": "nyc mocha --recursive -r ts-node/register test/**/*.test.*", "coverage": "nyc report", "coveralls": "cat ./coverage/lcov.info | coveralls", "version": "npm run build-clean"}, "nyc": {"extension": [".ts"], "include": ["./src/**/*.ts"], "reporter": ["html", "lcov"], "all": true}, "dependencies": {}, "devDependencies": {"@types/chai": "^4.1.6", "@types/mocha": "^5.2.5", "@types/node": "^10.11.4", "chai": "^4.2.0", "coveralls": "^3.0.2", "eslint": "^5.7.0", "mocha": "^5.2.0", "nyc": "12.x.x", "ts-node": "^7.0.1", "typescript": "^3.1.1", "typescript-eslint-parser": "^20.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "gitHead": "55c4bee004c220a023f3a6aa0b7201c18dd00007", "_id": "ts-mixer@2.0.2", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-/L0FzYEyKFAfDI0TXfJR03CrnsDOPXS6szq49UirVR2OUTbJUvasnD9P1YnP/d6soJY5K0cO/qJJtzAZJ5DsoA==", "shasum": "fba90b3643e601ff27eaa687502e5d0e91b861ff", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-2.0.2.tgz", "fileCount": 5, "unpackedSize": 19406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb1IbuCRA9TVsSAnZWagAAY1YP/38jSEEBtceQMMoxfNQL\nvkI4ifqeedyrW69+0fuzttuMt/rrLPKrrs1MhGu7GgmF5xg0He4tqvdTf90n\n5gctKfAe0X6obgk+5rVXja7SuMyVcnTcBJ0MvecgUlF/idred0BMK2W0jXmy\niN1HEIkS2W1BS9F9NyqFpodUhdqBwiPZXT4hCbBSgwoztFT7is1MA571rXrX\nzfh2EWPfJv+kTI1yTOe1LrTPvu/bKPWgT/IKHwuubbH5/NwFPdbzyIKgKn8t\nrIIorpUPjUPC1w6CwiY3iPJXtzHTCwW7badJ5cMecAnI14jihDLNvPEyGazu\nyAHtaF4/RVElUc4jUxN4ccJoMMi9ttKdr8FObnivnOt5x3/mLrIrFWK0QmOZ\nfyqCw9ZT2OJfx30Ok9dTSy21rf9KIjy/yPJeV/TiKshpteBrejpl1OPQuidy\noDbvuf/WkrNnXpES7hVdVTC4Pm08X7LSXpGhjr5Git0vYe1/FKmys86N36uA\n2/cNjr5q8c0cU3Ct5+MXIEpZunmm9l4hzbvlfPPrRgSBfd5uWVod4IBNYHCJ\nj6ZPfoSDl+5aZSZJNA+tQj58D5qEMFCpLvhyV2MQHEc7XUJif/YokhpdS1JD\nyoDa99KpLPj30G9z6i39vqSkpr4f3oUQbpWnJ1iJ0ajmZ1H8cG8a2R21dzTs\nFL+g\r\n=BJdl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCAYgcdstgQpiR3BJFglSHjXxpecp5HavZp7i04xqW4pQIhAKFEXD3I+5U86LtWjex8ic2eVcRlEsI4AX5ZZmwUUtEM"}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_2.0.2_1540654830166_0.3403234599974343"}, "_hasShrinkwrap": false}, "2.0.3": {"name": "ts-mixer", "version": "2.0.3", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "keywords": ["typescript", "mixins"], "main": "dist/mixins.js", "module": "dist/mixins.js", "types": "dist/mixins.d.ts", "scripts": {"build": "tsc", "build-clean": "rm -rf ./dist && npm run build && git add ./dist", "docs": "typedoc --out ./docs --mode modules ./src", "docs-clean": "rm -rf ./docs && npm run docs && touch ./docs/.nojekyll && git add ./docs", "lint": "eslint src/**/*.ts", "test": "nyc mocha --recursive -r ts-node/register test/**/*.test.*", "coverage": "nyc report", "coveralls": "cat ./coverage/lcov.info | coveralls", "version": "npm run build-clean && npm run docs-clean"}, "nyc": {"extension": [".ts"], "include": ["./src/**/*.ts"], "reporter": ["html", "lcov"], "all": true}, "dependencies": {}, "devDependencies": {"@types/chai": "^4.1.6", "@types/mocha": "^5.2.5", "@types/node": "^10.11.4", "chai": "^4.2.0", "coveralls": "^3.0.2", "eslint": "^5.7.0", "mocha": "^5.2.0", "nyc": "12.x.x", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.1", "typescript-eslint-parser": "^20.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "gitHead": "d2da6a2656c796579362db82da0997666a02a635", "_id": "ts-mixer@2.0.3", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ShdOjzKdAdlFk6dpWhLa1LawC5MI+ArZZx8MXvOP3gPdx4dlFc8PQStWvoZTQ/iGnXoCiFCPVi0Jt9rhmB3npA==", "shasum": "3ea34b8ee79bcd78d88ed1df4f70180475a2e146", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-2.0.3.tgz", "fileCount": 5, "unpackedSize": 21195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb2IwXCRA9TVsSAnZWagAAifMQAJexaGtf4GA0hsQinZex\noalxWmEsBOmd0Qcx1Qe1JC4dENCTHXs6E6KveLGWdmnqZKjCBp0Kb/VZAb0z\nQHY1FX9nGloBOz9yQ545Kb/Cg6924l9W5X2xALQY1Mc5i3JqsHc5z1Z2FwGo\nOmioPbnRwqesxI+nPzoYZMcKAzIU1mNWfAMg9aacp1Q2YJSxeJT1xwDKFAXX\n6yiRkspaSb5qvk2BQprGz11Ozm/pizBGGp11OsuQfGWJ2qSLLmS60ZkOxRsf\nS2Lj5rDGEH1wFEfSCdOgN8y68WviobHRASLA7udzlkIdozNplHUrV47kaLqt\ngWgm/teND0P2F/yNRB7IJWyHSnWvjdWSy+RULiY7y75wkRJ/dfUDTIYeob8z\n70TOl5iwRlpqFkFYysR4pqgJORgHa2e2HSqPRzQrGG+RK24BrBsMp0Px2s+4\nGMA7KUZs/gEAjrcoB48Zez58GZgIrA8SEX7uEahd2Wbve2FFLngegqKY4jqJ\nGXnNN4edv1Qz6MuErLFuNeToC480CHwJmYWNYgZvUnxEstz8ojjnVCTtGj3z\njtwQC/LoiuhGMe9uZuSWTbANYL2KxTmeOE5K4HW7JZzBxcx1enEThOx0ipbH\nYAoe9QUWp6W/ucelgFiS4/2Qu20bmehow+3Khs4eG4+zbGRn4o8IfzBlu+vr\nDoHk\r\n=CDyd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFaJVjarbtLtXSHhdK79ROzlia9OodPYA/bgdksi6noiAiEA/WlqCQvAXK33Ucz3M5fzIXk+awNkW2mpNzVkDn1zxRo="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_2.0.3_1540918294594_0.28754704417599286"}, "_hasShrinkwrap": false}, "2.0.4": {"name": "ts-mixer", "version": "2.0.4", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "keywords": ["typescript", "mixins"], "main": "dist/mixins.js", "module": "dist/mixins.js", "types": "dist/mixins.d.ts", "scripts": {"build": "tsc", "build-clean": "rm -rf ./dist && npm run build && git add ./dist", "docs": "typedoc --out ./docs --mode modules ./src", "docs-clean": "rm -rf ./docs && npm run docs && touch ./docs/.nojekyll && git add ./docs", "lint": "eslint src/**/*.ts", "test": "nyc mocha --recursive -r ts-node/register test/**/*.test.*", "coverage": "nyc report", "coveralls": "cat ./coverage/lcov.info | coveralls", "version": "npm run build-clean && npm run docs-clean"}, "nyc": {"extension": [".ts"], "include": ["./src/**/*.ts"], "reporter": ["html", "lcov"], "all": true}, "dependencies": {}, "devDependencies": {"@types/chai": "^4.1.7", "@types/mocha": "^5.2.5", "@types/node": "^10.12.2", "chai": "^4.2.0", "coveralls": "^3.0.2", "eslint": "^5.8.0", "mocha": "^5.2.0", "nyc": "12.x.x", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.1.6", "typescript-eslint-parser": "^20.1.1"}, "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "gitHead": "db8e020fd2bfbe0359b95309c7efc4ed7167b3bb", "_id": "ts-mixer@2.0.4", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-xmqZLnifCh0zwEYR4ixbB7gfIBqqFocShZoLnVr7ehFUtTvvc/4jRz17wy1mJ3AyhWy24hOSotIo8w1UUt1JWA==", "shasum": "e2447cf395c911413426ab79c0b81a6c3b163037", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-2.0.4.tgz", "fileCount": 5, "unpackedSize": 21195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb3ka1CRA9TVsSAnZWagAAB0EP/jHMumIfOM9w2ihdbPoo\nWjhI3vpTVljgnYZfIoXO071jkbLUNjqvItjhC7/q34KxaBEUfHimg/a8sem6\nIZuHqjXENaQx+NIfcfjLFaDGiqj2tznAMMDfAAjHC750P2pd0/m45Z7kjMFm\n/LgkPoIj6K4BpgZnAcjWr3fTwl+p7Pyfoq+D+hgXaUax0+RQV7YChfj62tif\nAgc9eOHV8GyCR8AmKwttsx/cq/PHXrMNRxTUr7pNth0qKacdCf3M7uZMPW8i\nRjPut1rpSgCXbCUjMxsCTMAlUzMlRkLnunLJxbc06IhpNDiaA0z5hUVy82CI\nXoEhkamZQh6k4iR8ZO87m5Pp7t2pwXE+KEv4rPjKep70uqEzpP8/0681Y/lj\nhbBRiTN3v5PlwFNg3wBdpMiSzTaRRjb9knJz7MER7LLnuA+XE4puMF7B0J7N\nxA1Xo86OU0OpBuiDtJCWnACPRh+OkbQCqpoBS6CmaJbj1+O2sxsgb0ymtYoE\n+CG3pzzqt6hXidLEbW5CIj84r4FX/NYmtY/OpvL1GXFwu609vtlPqRQeLhnB\nGpwajcIF/hdMDgrmKhS5032/Clz9kFwH6M3xms/t569Kx1k9k80KeiGj8QIP\nbB59PXWI1ofxHnZpUDQ0x9jQmJfvbu4EwBjwSsKedUaW6wHITIlS9TTPQ0W1\n1Nah\r\n=5sAJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDfbl/aAvl0fAfaFmQy1otViVmIIUCtfq+gXdtOPxgKqAIgRDLIraSnE1cH/P51PBZXkh03HhyZcLdwnYizwCC+x3Q="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_2.0.4_1541293748852_0.5464057035843437"}, "_hasShrinkwrap": false}, "2.0.5": {"name": "ts-mixer", "version": "2.0.5", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "keywords": ["typescript", "mixins"], "main": "dist/mixins.js", "module": "dist/mixins.js", "types": "dist/mixins.d.ts", "scripts": {"build": "tsc", "build-clean": "rm -rf ./dist && npm run build && git add ./dist", "docs": "typedoc --out ./docs --mode modules ./src", "docs-clean": "rm -rf ./docs && npm run docs && touch ./docs/.nojekyll && git add ./docs", "lint": "eslint src/**/*.ts", "test": "nyc mocha --recursive -r ts-node/register test/**/*.test.*", "coverage": "nyc report", "coveralls": "cat ./coverage/lcov.info | coveralls", "version": "npm run build-clean && npm run docs-clean"}, "nyc": {"extension": [".ts"], "include": ["./src/**/*.ts"], "reporter": ["html", "lcov"], "all": true}, "dependencies": {}, "devDependencies": {"@types/chai": "^4.1.7", "@types/mocha": "^5.2.5", "@types/node": "^10.12.18", "chai": "^4.2.0", "coveralls": "^3.0.2", "eslint": "^5.12.0", "mocha": "^5.2.0", "nyc": "12.x.x", "ts-node": "^7.0.1", "typedoc": "^0.13.0", "typescript": "^3.2.2", "typescript-eslint-parser": "^20.1.1"}, "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "gitHead": "8efa0b29d1d52348109fff4a27292bc354fa820e", "_id": "ts-mixer@2.0.5", "_npmVersion": "6.4.1", "_nodeVersion": "8.15.0", "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-hvQugLuY1gdVs3b08YD24iY7Xuomd52U0Fuxt7elP+p/LrRXq4Zk/tM7QVFsT18AmyMBJ7etNNCGVn3mZbDlBw==", "shasum": "d4d9c850a2271449fc821ea434321c5504d57486", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-2.0.5.tgz", "fileCount": 5, "unpackedSize": 21633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcPNbUCRA9TVsSAnZWagAAF+gP/1HCvGq+2y/7TD9KBIQD\nrr2DsmSrXQ+SvRU+c6Atfva8qGxckt3GlAHflxj00C7gFlPD0AfP3+i60+zs\n0q70TkQhlX4ddO7P7bjkZx31G6mznC6XrDNphI49CThuevVyjC/J622qfd5d\nVYR86OPSjVgIrJNQpfuACrt3cjlZeuUtNpjMc2AHdKmdInYY3asc0JgfuHlw\nNZ6T4Kc5kl3BPiBEHqZB19GI0os2C+Unbpfl1LmxtT7wOVSf/q5sts96hOGD\nbxDXXrHd5cizXq3J6o0FQpLV/YYkjAc8I8pxePduSTRyyu7p+pIa8CD55zxu\nM3GLCOBd5ch/w6Uf8004Igq2C3Vzy4y3XXn5cUyS7Xabiwg51KSGlAlDgFkk\nZ9Ubsngbi6c8IlJl/WinIssO2ZkGj+SrKSCq1LD7m9BgyGK+vDhhgmE6NJno\nYX/BB2nqy2URPopBkM/9z01GWkkkLMEVv/sHYvW/L5mjaELJEoDccbLx4stR\nza8pdiZ8Puu5cjl2k65nvvUfQHFDX3tsFh//oA1oFpwp6gF6vXOu8SiClPLR\nyqFKCTJYdQv01Rse6CIJwemZGuHl8LLFP/oeG7GCYp9VxlMuGsyFrRSGRUn9\n76wiiwNNTIKf7vYqBCDh19GddxS6zyu3umIo2/zDMsRCU6ML1M4xe1BPAbs/\nTCnI\r\n=AYI1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFcE1BP0HChHUqs6ea0cw7GlRWIq0k5E7EBmiqc30hk9AiEAqZ/qnW3xgMwJXXpTaPVWHA3LSQT9p+c64xe3xoDQznI="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_2.0.5_1547491027943_0.5422410946400242"}, "_hasShrinkwrap": false}, "3.0.0-beta": {"name": "ts-mixer", "version": "3.0.0-beta", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/mixins.js", "module": "dist/mixins.js", "types": "dist/mixins.d.ts", "scripts": {"build": "tsc", "build-clean": "rimraf dist && yarn build && git add dist", "docs": "typedoc --out ./docs --mode modules ./src --ignoreCompilerErrors", "docs-clean": "rimraf docs && yarn docs && touch docs/.nojekyll && git add docs", "lint": "eslint src/**/*.ts", "test": "nyc mocha --recursive -r ts-node/register test/**/*.test.ts", "coverage": "nyc report", "coveralls": "cat coverage/lcov.info | coveralls", "version": "yarn build-clean && yarn docs-clean"}, "dependencies": {}, "devDependencies": {"@types/chai": "^4.1.7", "@types/mocha": "^5.2.6", "@types/node": "^11.13.5", "chai": "^4.2.0", "coveralls": "^3.0.3", "eslint": "^5.16.0", "is-class": "^0.0.6", "mocha": "^6.1.3", "nyc": "12.x.x", "rimraf": "^2.6.3", "ts-node": "^8.1.0", "typedoc": "^0.14.2", "typescript": "^3.4.4", "typescript-eslint-parser": "^22.0.0"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixins"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "4c72f597a862d2c98cbdfb98374ef56855a0ff08", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@3.0.0-beta", "_npmVersion": "6.4.1", "_nodeVersion": "8.16.0", "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-cyJc1+5RpDrZHC/5L95wo7VHsMllCWBuIJxmiSO84/DpfI2iGryPx36QAcUTrdgQO630MrmNEM9ucUNasugtyQ==", "shasum": "884244c8aaefad3b6dfebfc481068c88ca1dd419", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-3.0.0-beta.tgz", "fileCount": 5, "unpackedSize": 17662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcvIeaCRA9TVsSAnZWagAAOngQAIjLU2Pt6pfvBfey3KNe\nHyKg2oSHkVvxPwv8YEv184dNw8VOKsbGnby1VAd46nmyPqJobUAHAzhrl8Bo\ndoJee1BExtBkAmNr/SMCeyASs4b8m4hytYT6U74mcqUB9it378U38Pcs/Yws\nSUUuXFY35Eb+viLKu/5cdK0vdpImMthkDYV6gQfFMuTCR31SZVSCYmuOlaiE\nOFnJ8Gxa9gPtaHf4lziTvnfeb6QaSsBgmO2aTUvYecv3qhmhXbp1LWKaRdr5\nM26VqzESV4vn38yfc4XssWAVbGt1+hfSRnaurPd5/sDp7ooRZ6hjwfgIuS18\nCDv8A1H3xgMu6oCrxCRHjNwlBZOV3E5XN5B5zGFUQDfm9p+RrwfOnACgR1+e\netinFIKQdW/2DOC0aLN5DZGy9fTfv6t7IfJlDcIs9kjpd1lkwyHCcqch6ycJ\nFCqZ9i2Lg6m6Fxusx0L7nxPpqNUEpssUTzUK01KrP8A7tqud4/Dp6FSD4Ign\n/KfVEOW5Tjt3rQrct69uThYqow+0f/MXDUbJus00PVheRBAvncLUq3CQX3w6\n0ESHthSPDnzezm9UHApJoYPbZChpGPYWyswXr+rQMX2Cb25gRL65z1b4A2Yj\nQm2ja7LJJZersvwmNyPtLG4ePoslpWWGrBu8W//88TDyWisNc/EJSxh3R6w7\n/Yz4\r\n=YZaZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDrx5eP/PzFf3n4/uox74xTKfjURpNdXqv2ANvbpfFSzgIhAJzchb4nh+mRq6XtfSFSB1zlLgTtRRkpFOLiAf2Hyrjt"}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_3.0.0-beta_1555859352859_0.6683187595412248"}, "_hasShrinkwrap": false}, "3.0.0-beta.1": {"name": "ts-mixer", "version": "3.0.0-beta.1", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/mixins.js", "module": "dist/mixins.js", "types": "dist/mixins.d.ts", "scripts": {"build": "tsc", "build-clean": "rimraf dist && yarn build && git add dist", "docs": "typedoc --out ./docs --mode modules ./src --ignoreCompilerErrors", "docs-clean": "rimraf docs && yarn docs && touch docs/.nojekyll && git add docs", "lint": "eslint src/**/*.ts", "test": "nyc mocha --recursive -r ts-node/register test/**/*.test.ts", "coverage": "nyc report", "coveralls": "cat coverage/lcov.info | coveralls", "version": "yarn build-clean && yarn docs-clean"}, "dependencies": {}, "devDependencies": {"@types/chai": "^4.1.7", "@types/mocha": "^5.2.6", "@types/node": "^11.13.5", "chai": "^4.2.0", "coveralls": "^3.0.3", "eslint": "^5.16.0", "is-class": "^0.0.6", "mocha": "^6.1.3", "nyc": "12.x.x", "rimraf": "^2.6.3", "ts-node": "^8.1.0", "typedoc": "^0.14.2", "typescript": "^3.4.4", "typescript-eslint-parser": "^22.0.0"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixins"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "491c7c1793cb50521e301c81a2fd11b8c5fea356", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@3.0.0-beta.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-65rwrxwFCxU7Lxd+8NpfIc6BNVIqhqVWktQO668QlZgWVZRqr0Xsf6hjdyTTimGyVt9RknL3XrHt7u94W1S8hQ==", "shasum": "a107d51c0a8db815189caee5326d004ec9a80a8c", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-3.0.0-beta.1.tgz", "fileCount": 5, "unpackedSize": 17664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcvIkNCRA9TVsSAnZWagAANBwP/2GO34szLzgac7kQn66Y\nIKYe1x4kECujFuJfTdE1gYPmOV7KRFAmw1HFY3JMMNfAPjqWDWsmYKq3xq2s\noE2wHawcgg80KNhUHeSYlows1N7rVMAsY+jcVZuq00UuGegS55eSSwmvkaY6\nJhDDbOs2jt37DIFGKKp/4HiKrsXG8YWTNgGoJrv9HXK7SAJBHYtx0e9nC35B\nKroHnznuX66v/AVYYAKd1P+S0d9Tc1CPB6JSJJErmuiI5h68ZgLJ+Xxc6x/3\nAUkRFi2cu1GkqZaYQYxHvArNQXTgPQJdPJvEY0pACCGINo76gF28MW6KAGaY\n8MQZ8OOs7zRK2A1YhykWn6kwsaeZOOooblQxB2yg5zsSgZUeu3i7vab3ve6m\nrv/qtdubU9wXyFBOvq7P5MqZ5MuyjRoYuMsMaK6/sFdHWlVSyjz3k8+Ne9SZ\nNRwp4ebJathIU9TaL+l4IEtlaNQF7KB07U6U0ITHYD4XrMZXJbIuiToNbebZ\n6B/JRnzKegiIaA254NOtNiIUEi9M1FWdAlSSto5R0qvNDDQstx3+GvRKkaiX\nOPrSzmGQ/3dPbwOdrLR8B1x5nQW5kpt2pQOcNs/xoaAGRPUzqkOy1p7BKqao\nGZ1YOIBOs8e6w3BUZy2wkHhlVaO7p+bFz/Csa6IXkXCjGTv05pP0P5ZGQPfp\nQiVA\r\n=0JJd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICbw7J9HsEJHaYCEI4/y61yR6dRTYhCBJusYm5T35FmgAiEA202oEnkRumjUAODHwpXt3QOXEwIQtM/wOnKs+/H6QcI="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_3.0.0-beta.1_1555859724290_0.9957856304426422"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "ts-mixer", "version": "3.0.0", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/mixins.js", "module": "dist/mixins.js", "types": "dist/mixins.d.ts", "scripts": {"build": "tsc", "build-clean": "rimraf dist && yarn build", "docs": "typedoc --out ./docs --mode modules ./src --ignoreCompilerErrors", "docs-clean": "rimraf docs && yarn docs && touch docs/.nojekyll && git add docs", "lint": "eslint src/**/*.ts", "test:base": "nyc mocha --recursive -r ts-node/register test/**/*.test.ts", "test:es5": "cross-env TS_NODE_COMPILER_OPTIONS=\"{\\\"target\\\": \\\"es5\\\"}\" yarn test:base", "test:es6": "cross-env TS_NODE_COMPILER_OPTIONS=\"{\\\"target\\\": \\\"es6\\\"}\" yarn test:base", "test": "yarn test:es5 && yarn test:es6", "coverage": "nyc report", "coveralls": "cat coverage/lcov.info | coveralls", "version": "yarn build-clean && yarn docs-clean"}, "dependencies": {}, "devDependencies": {"@types/chai": "^4.1.7", "@types/mocha": "^5.2.6", "@types/node": "^11.13.5", "chai": "^4.2.0", "coveralls": "^3.0.3", "cross-env": "^5.2.0", "eslint": "^5.16.0", "is-class": "^0.0.6", "mocha": "^6.1.3", "nyc": "12.x.x", "rimraf": "^2.6.3", "ts-node": "^8.1.0", "typedoc": "^0.14.2", "typescript": "^3.4.5", "typescript-eslint-parser": "^22.0.0"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixins"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "096e88904ae78b36244cda6193e204f213b7f461", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@3.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-MG/XIz7ihXy9w73H2//iU3Ao6Qmmk3fViqdh49UGhzbAV+0xubckh2znJKJcjMLt29VdTGiuMc7KaG1/YZU3sg==", "shasum": "a6182ed41515ffeb7399942337cbf940e030eeed", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-3.0.0.tgz", "fileCount": 5, "unpackedSize": 18788, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc2iVlCRA9TVsSAnZWagAAQGkP/R+jU7uxKC6ypjBwSwnL\nvy09/mmNb85HxW7JD1u0L3EE2cWa1xB5/5nPPaN7/J0NEBmkUFUz7kptHvvL\noS7G61h8RG6wM2i0w4zoAlkk6UpS/soNVJuoUYf1nknpjeg1GxWpJXNY5TlR\n3/qjuRYG6LX48sJUnwJxNYC/4NnbxwARtc+7rN8MevCb5ZYyXEppZ35g0d9B\nVNrb4mKg/lTYGC8uR9uJItqLGf+UuWVN+hEBqxrhGwr1yhPOu42bZcYXmoBX\n1qB/McQzOU6IJDAO3kEMooLuBqySFdj/88Y14izirE2WNouJjgZ3nI9UIbsh\nFiFsss6b2VkhdkEprI6CKCNmc9EjbNrP6JN+Qqi9S3kSzPQmxcCjXW4RFVA1\npQ2epxljeZLIvmjrdOOkGhzjigWuvfZE6Tn6w9zJQCJP3U3nIxEEtJahTYOD\nC9cbi/XMShrgRI8xthByfYBtoCxnkAQP/1ItuRJRZKDiVcks7TG75L4MQx3O\nm+opaimGYs+LqO9ojiT0FK75vng3eCO7kzjx9ICTTQ67l3UgHFtZOH0ZGZW/\nqP2VgTTYrOFXmTdPxOIisnfbQxt0+uBBDsrvgndZFXV0kcx3X93EBKekB4yI\neYZPcBcPIMpsUd76c2Yn6ZBCjvtzdK+nz+kasSFE9USwOK0i7QVfag9iKNuv\nWAnZ\r\n=Om5N\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCKxfNQp5Vs9iYHs22SIaS3dEfDHddUOa62FMQ0o32KZQIgHGRFiactXXhW+TwjEx6quznQMy97a030AfkZOfLrLds="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_3.0.0_1557800292123_0.2849062538992857"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "ts-mixer", "version": "3.0.1", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/mixins.js", "module": "dist/mixins.js", "types": "dist/mixins.d.ts", "scripts": {"build": "tsc", "build-clean": "rimraf dist && yarn build", "docs": "typedoc --out ./docs --mode modules ./src --ignoreCompilerErrors", "docs-clean": "rimraf docs && yarn docs && touch docs/.nojekyll && git add docs", "lint": "eslint src/**/*.ts", "test:base": "nyc mocha --recursive -r ts-node/register test/**/*.test.ts", "test:es5": "cross-env TS_NODE_COMPILER_OPTIONS=\"{\\\"target\\\": \\\"es5\\\"}\" yarn test:base", "test:es6": "cross-env TS_NODE_COMPILER_OPTIONS=\"{\\\"target\\\": \\\"es6\\\"}\" yarn test:base", "test": "yarn test:es5 && yarn test:es6", "coverage": "nyc report", "coveralls": "cat coverage/lcov.info | coveralls", "version": "yarn build-clean && yarn docs-clean"}, "dependencies": {"is-class": "^0.0.6"}, "devDependencies": {"@types/chai": "^4.1.7", "@types/mocha": "^5.2.6", "@types/node": "^11.13.5", "chai": "^4.2.0", "coveralls": "^3.0.3", "cross-env": "^5.2.0", "eslint": "^5.16.0", "mocha": "^6.1.3", "nyc": "12.x.x", "rimraf": "^2.6.3", "ts-node": "^8.1.0", "typedoc": "^0.14.2", "typescript": "^3.4.5", "typescript-eslint-parser": "^22.0.0"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixins"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "644f1edd0921fc4df69e219de5b17f8b97c38dc7", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@3.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-SOIYXTpg0XGfyG87FqasiEg5siOqelBH6PCA+CcNd+R/ThkrRNWOEi84TddzyqC4f3wflzjERiZ3vkjQpu3WKw==", "shasum": "36087fdb284a36175b41b5ec396d3affd8678bc8", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-3.0.1.tgz", "fileCount": 5, "unpackedSize": 18790, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc3ZB8CRA9TVsSAnZWagAAoe4P/RPjV+v2r9hKH29h9QPo\nVyQ7Msqs2u032qHt5v08AE7xkFf2cwaiKBbbkaOy4oLj13VvzRQKnHXakOWn\n+u5+oSWP/KxQ621MZABruCL08xx5B1xIaCGtFh+df01DOVER+oBv3Dsjjrio\nVDfoeWEcWqRi2/2Kdaz6XxxxLAptL3N9E2+Ga6RDwgrAkQLGRPnCK782WPBw\ncko5otgsv5UxkTdMeNgpYQXNvFxBgWMEbIxQ8KE7T5H8zQBza76lDBF61kOA\nC9tAwgc0XuhkgSJy+WYrmrrAUmD8cQTFMN4JaSvdgQAPwgZvNIrEF2SRDntn\nC2q04P9YSe5WnP3Oi4h4CevbBU5kfMgH/1tt05kTSi908X9jzAs7wYFblsHk\n5NglnmZpvvfku477qQWdW4ZMnSFaVA2SB8XUi6Z39fbMyvYg6Ty0w/iPNN1i\nLmi5gOgIo4HFWEHF+zlxWMwdvQvlgC+wO5otbTeQBnyE/i4iXL0Fsz5bfTud\npgnpesxUwLnBmMv4h29DCL4oydUj/qJUAgz/2cj4653RKnBI7IDAT5HvWpuJ\nmaJp8jJHEuvm5JPW2UMxr/OzXS9kmuxDF85unghfVdy3E9bHDIXTRKpkwu/o\nhp4sO2q2x6iQ0IAeUWr9kG8doyj0fv3N8MlNzUQQ3klWVSL3ftplSNtYkFvp\nc7Ti\r\n=c/ob\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCXPagXtlrkCg0yNFiEYiMHi+4OOcJDBDorxOx9QkCoMgIgG+zDCIFmkKTuYNsR1fvM9c+jeJdmy+WBEWiIZCpcDSA="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_3.0.1_1558024316031_0.249828499599676"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "ts-mixer", "version": "3.1.0", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/mixins.js", "module": "dist/mixins.js", "types": "dist/mixins.d.ts", "scripts": {"build": "tsc", "build-clean": "rimraf dist && yarn build", "docs": "typedoc --out docs --mode modules src --ignoreCompilerErrors", "docs-clean": "rimraf docs && yarn docs && touch docs/.nojekyll && git add docs", "lint": "eslint src/**/*.ts", "test:es5": "cross-env TS_NODE_COMPILER_OPTIONS=\"{\\\"target\\\": \\\"es5\\\"}\" nyc --silent mocha", "test:es6": "cross-env TS_NODE_COMPILER_OPTIONS=\"{\\\"target\\\": \\\"es6\\\"}\" nyc --silent --no-clean mocha", "test": "yarn test:es5 && yarn test:es6 && nyc report", "coveralls": "cat coverage/lcov.info | coveralls", "preversion": "yarn lint && yarn test"}, "dependencies": {"is-class": "^0.0.7"}, "devDependencies": {"@types/chai": "^4.1.7", "@types/mocha": "^5.2.6", "@types/node": "^12.0.2", "chai": "^4.2.0", "coveralls": "^3.0.3", "cross-env": "^5.2.0", "eslint": "^5.16.0", "mocha": "^6.1.3", "nyc": "^14.1.1", "rimraf": "^2.6.3", "ts-node": "^8.1.0", "typedoc": "^0.14.2", "typescript": "^3.4.5", "typescript-eslint-parser": "^22.0.0"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixins"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "d28c536b47133e608e83eb50e97a177d21052ba0", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@3.1.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Y8D80nF0viWO4EUQz2Q8TO8xVsr/ZRmSkLvgNLqYwKiypA2wAizysa8REGedKvJhdzd86gu+qs9ZNOWMt3xJcQ==", "shasum": "abbf76044f8ac859dc36e965384b8fa7848500db", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-3.1.0.tgz", "fileCount": 5, "unpackedSize": 18449, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc3jGuCRA9TVsSAnZWagAACtwP/0yc6JQiavOdouIK486g\n6V/PguB+wEt/UtWLEWOwG1onEnV1SNZWEIHoZR8YPH2I00fehe5pozcS2Xjm\nN7i+VG0AoE580MueINml/zXP2TXP1pi393OPQIiKN1+Cd0WPjC5bkKerjaU+\nov9ro0IWeNW2pEltcdFgbfGYksjESx6z8/VqUQSlGnNT2KQJGfPoXtkM4ASR\noLzb5M4tD5CROrZOaYLAxBHYnuzjKX3nY4YNs5zw4OBCjn5NBm/DsOeTf/Ss\nPkPwN0008FTy/LIA9Y2vyzH9un7F5A19bo/9qP1NzGKlDxvRC3/cDP3+OJBC\nLeGO2iptu2hmxOBYGy6X6YUQAkEbQ5TghqmVUJdjUCGhInBepTa+P06zu4po\nOPctfcAYJyUU+b2n/+2iFtB+M4uQ3pWhwJkDN4tcoFHBNbMIaEd2rcXqtzjK\nhINfegGs5Mra4IgsfnLDobmSUv8pL3wT6RcvC/9n/nJ/ffdjNQN2bsCUqVP0\n8xSpwhizwiunoSK0iJfwzr3mz+04F7G1xgm9INByYMJTaazAArjh4fYCmpV1\ngoE3qc4MD5vsv4T6Wje44eTHC2U0Ky7j1FVYVjJSxJAiIfpr7I83W7rp10nd\nLIWAD6GSEVbwEbveQBvLf86RTH3oHskye3wW2loZeO5xH5mlPt8J8nzbkdjE\nJ5FT\r\n=m6Nl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCNj1chuZTBvE1bD2GQuQVNKANUXPQ151A+1Uqn8puMKgIhANdA2zpueIKT18MFxq+ZFeMikqbrTWwHWcRZgYt6zeLT"}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_3.1.0_1558065581567_0.8092499388293377"}, "_hasShrinkwrap": false}, "3.1.1": {"name": "ts-mixer", "version": "3.1.1", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/mixins.js", "module": "dist/mixins.js", "types": "dist/mixins.d.ts", "scripts": {"build": "tsc", "build-clean": "rimraf dist && yarn build", "docs": "typedoc --out docs --mode modules src --ignoreCompilerErrors", "docs-clean": "rimraf docs && yarn docs && touch docs/.nojekyll && git add docs", "lint": "eslint src/**/*.ts", "test:es5": "cross-env TS_NODE_COMPILER_OPTIONS=\"{\\\"target\\\": \\\"es5\\\"}\" nyc --silent mocha", "test:es6": "cross-env TS_NODE_COMPILER_OPTIONS=\"{\\\"target\\\": \\\"es6\\\"}\" nyc --silent --no-clean mocha", "test": "yarn test:es5 && yarn test:es6 && nyc report", "coveralls": "cat coverage/lcov.info | coveralls", "preversion": "yarn lint && yarn test"}, "dependencies": {"is-class": "^0.0.7"}, "devDependencies": {"@types/chai": "^4.1.7", "@types/mocha": "^5.2.6", "@types/node": "^12.0.2", "chai": "^4.2.0", "coveralls": "^3.0.3", "cross-env": "^5.2.0", "eslint": "^5.16.0", "mocha": "^6.1.3", "nyc": "^14.1.1", "rimraf": "^2.6.3", "ts-node": "^8.1.0", "typedoc": "^0.14.2", "typescript": "^3.4.5", "typescript-eslint-parser": "^22.0.0"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixins"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "8c6ad6a6af11ae9d663de09e3e985cad715f32df", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@3.1.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-2BeiTZQJ2IDG92xeD3qPNwj7xv9jMpEUPLOiKksrPbUV5qLExxhM8DSCwnfFZfGttuXs2G4jinnPszYOjx2gpQ==", "shasum": "2030d8d15f01e9b37c3f4e492770c2cdc9bd21c2", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-3.1.1.tgz", "fileCount": 5, "unpackedSize": 18466, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc34I4CRA9TVsSAnZWagAAoGsP/RI/bCoWyt4Nq2oiH0xa\n/TRUAv+hBJAbA27RnKPJobN2hJtS4N2Ez9Ic4m/+6tKECI5RSwlOO02aBMpS\nczQSfAwJKaUFs0Gm8yamwN2G0fLnxE6b2Txp3tFbDIdxWZX3XCOM1CGQXeba\nHk9yp2RB/XUvs2PqMIBrkzh0hOBDcOowtKptjVBVDDxS7IgC/jz+cHQ+CmCM\nrLAMxl43rPvFyCQ1OqxO4wU9+ZoQb2IDqS+up9dKMTw270XHkuWlTDHUANCE\nabIvl3TkwqeFgQinB6yXbz9fFpx56CPDsYr7hnX2LierdwvpaKKk8cELfxvX\nHOwq9outA/7gbJWV4GldP67DiSHIr3YT1LAUo9kLKmUxCZBk+t0IT7P6ptH1\nzEgeQ3ssC7/dW7mwB8W+wSt51TkdP4L4DFZRwNeLt/3Tjv6EEIkf/EM50rkM\nnmXQX31dIIbaPmyU1fFrZELmfQFdNwIr7Ezy75qDUrzRzXtlDEnCDHOvYizJ\ntEgkWKGaYYl3l/azQxvtbUSoGnJlfoe1lBThsfL+EZ6KF3OWIQ6fLPaGbERi\n7wKFEuHRlKPwa5qADVroabnVPjrk6zZSFkmxsWavsITdI85SGHdSVW2p8JYA\ncJZl77cOiNTXP1QvEb7lBiGhRmjv3SV9GHwyNEZbpMQfnEJYEjVd2qCREUeF\nxis+\r\n=WY4G\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIByrGUx2aJeBeS7aMl2d/fN8JnQGzMQOfrlJ4tOQSxLhAiEAxdNm0OiVMoZgGeguI/nwoXxc6/WJJzSxcGbE02bQ1EU="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_3.1.1_1558151736161_0.4815602185520962"}, "_hasShrinkwrap": false}, "3.1.2": {"name": "ts-mixer", "version": "3.1.2", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/mixins.js", "module": "dist/mixins.js", "types": "dist/mixins.d.ts", "scripts": {"build": "tsc", "build-clean": "rimraf dist && yarn build", "docs": "typedoc --out docs --mode modules src --ignoreCompilerErrors", "docs-clean": "rimraf docs && yarn docs && touch docs/.nojekyll && git add docs", "lint": "eslint src/**/*.ts", "test:es5": "cross-env TS_NODE_COMPILER_OPTIONS=\"{\\\"target\\\": \\\"es5\\\"}\" nyc --silent mocha", "test:es6": "cross-env TS_NODE_COMPILER_OPTIONS=\"{\\\"target\\\": \\\"es6\\\"}\" nyc --silent --no-clean mocha", "test": "yarn test:es5 && yarn test:es6 && nyc report", "coveralls": "cat coverage/lcov.info | coveralls", "preversion": "yarn lint && yarn test && yarn docs-clean"}, "dependencies": {"is-class": "^0.0.8"}, "devDependencies": {"@types/chai": "^4.1.7", "@types/mocha": "^5.2.6", "@types/node": "^12.0.2", "chai": "^4.2.0", "coveralls": "^3.0.3", "cross-env": "^5.2.0", "eslint": "^5.16.0", "mocha": "^6.1.3", "nyc": "^14.1.1", "rimraf": "^2.6.3", "ts-node": "^8.1.0", "typedoc": "^0.14.2", "typescript": "^3.4.5", "typescript-eslint-parser": "^22.0.0"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixins"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "c615ecd8640ae51cee71890f97d2b0b8d98c53e0", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@3.1.2", "_nodeVersion": "10.16.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-C7HJ0wskZOEXRtznPRSTkOEaMOwA+GqwxLEfUxNCqaRThnTQwbU8ZGjEr1T5NoqqN8CKT0262cNkSf+JWgyaTQ==", "shasum": "2f0bdf00d53d62c171c3d4170a9fd8bc8cce1ee8", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-3.1.2.tgz", "fileCount": 5, "unpackedSize": 18485, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJywGCRA9TVsSAnZWagAAxAQP/jTFzw6dSTd36ivEyX3F\nyec14bZTP8liKgPQVQ1FTcDknN1owKQUo/7rnyMPO9HrlD1o6WAKMtN1hz6p\nUFGOuOyQ7T95C66yocKOM+Jc4a3rbCzANbY2XdFrmdUVW82vOamWniTln2qt\nQdTodDOr7VPuYCtiaHcYNQnAAJx7mCqWDCWxdWqxcywCjX0QC9lLpC6l5KNN\n3Uj0K0SybU2AhixPvLgN3CoBQ30KNI9ldeHXam2iljGNAUL4ni4CnLrN49X4\nPfLkwVOGcG8xsJeDus8IedG/VGBgCEShR7Iki9vo7GZoejbQupL3Mm4pNXNT\n2qxHQqhgLsAFXVE1kOFxEUAgOUUzDC6CrUEwCrND0vjYZI7dYSwTw7nKL2Tu\nFPukXZvO1GL5d6w4eLIkHP+0cwuDRjFK2HuG7Fk8Mi5AravTKURbv5c0elp6\nlH0pxkp+H2UfInsLwrOwAO6a/omNHCU14gOXeg9nHnDAiQtLo9UrWD8+fpDE\nDPwC0qxkBX1KukeEY/E9bv/5VopkveEaxHCthaPXOucIbJwz9E5qgOps2gzl\n7krNN5MRXMEUJqmktGQi9st5A73AUZ0etZ74cKW5XguMd/T/hMmrTxH/IhSa\nNOtQ/B4BczI6hNMYHF8tS8HJhkV7VpxrfdW3+DTQeKYW0ewnktsH6dU+2+gK\nKH7A\r\n=8Gz7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHy+eEg6eeHslbf6qMgdW8oycDxHGOugfwv5uS1bF7bmAiEA14EJMKVdF4TP44ubuh066nmo1jHJkK/V7xOzimq602Y="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_3.1.2_1562848261949_0.9676128802221038"}, "_hasShrinkwrap": false}, "3.1.3": {"name": "ts-mixer", "version": "3.1.3", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/mixins.js", "module": "dist/mixins.js", "types": "dist/mixins.d.ts", "scripts": {"build": "tsc", "build-clean": "rimraf dist && yarn build", "docs": "typedoc --out docs --mode modules src --ignoreCompilerErrors", "docs-clean": "rimraf docs && yarn docs && touch docs/.nojekyll && git add docs", "lint": "eslint src/**/*.ts", "test:es5": "cross-env TS_NODE_COMPILER_OPTIONS=\"{\\\"target\\\": \\\"es5\\\"}\" nyc --silent mocha", "test:es6": "cross-env TS_NODE_COMPILER_OPTIONS=\"{\\\"target\\\": \\\"es6\\\"}\" nyc --silent --no-clean mocha", "test": "yarn test:es5 && yarn test:es6 && nyc report", "coveralls": "cat coverage/lcov.info | coveralls", "preversion": "yarn lint && yarn test && yarn docs-clean"}, "dependencies": {"is-class": "^0.0.8"}, "devDependencies": {"@types/chai": "^4.1.7", "@types/mocha": "^5.2.6", "@types/node": "^12.0.2", "chai": "^4.2.0", "coveralls": "^3.0.3", "cross-env": "^5.2.0", "eslint": "^5.16.0", "mocha": "^6.1.3", "nyc": "^14.1.1", "rimraf": "^2.6.3", "ts-node": "^8.1.0", "typedoc": "^0.14.2", "typescript": "^3.4.5", "typescript-eslint-parser": "^22.0.0"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixins"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "5708c87e58ad7adfabe73a4627bdc4a246969e68", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@3.1.3", "_nodeVersion": "10.16.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-YNoGc0fcc9+AOl1XanVtuvEGMoAhDSXhIxJApHB0i0plv1IkH7rfrLm/U/f+FWLrhLKh54mY8yx0M2ii+cUEtg==", "shasum": "180d8c10820f2bd0d4dcb0a39e0359fcb400ee7f", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-3.1.3.tgz", "fileCount": 5, "unpackedSize": 19283, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdP4rWCRA9TVsSAnZWagAAE0IP+QCuTuJVAoqr9O1Z9cyU\nJCb6HhAr5WgwUBoE6C/3bZsMTkZ5fh8Pls55evMMEpy+3faJ9k0FH7uFs7Xd\nNUZVHgFcfIEBdxTCMkEgaoxoEzfqLBDm33jllLBpVkcUN0JQVmJmYlXvzG+B\nPzH+YX8mNXOA62cqQBXpMYl5CV/zRcLJE6ttXY7rznm0JgwAGgnTlpbJeLcf\nVltTx8yHq/zixQ3Ngtrg/COWzbT3C8SRfHC5DPs/sxvoUcTr1ZP6NBJYeAKz\nmI7fRgkx46COuivVY5UmVaBO5hhV74OK733Ub4dv0n7mPwEGkH7nxAuxocNt\naUctcJGxy9F8oLPBwJYWUlk+VO9BJnKYPgsOfprxLnxrlBewD5XDV4nT/SuG\nlHUyIOaWyzhle+R60/vdRT70P3k+vCJtaJL4R/S9pRl/BJnpCCCvWs3iOL79\nJo6o72nuKvB36EcjYZ60kH3jwqSAKAg2T+YUM0Eo9THLIX5Bx3Xk/VIeP+tm\nSd94ydqZFXnjjHdn04ge9Hu7+SokPGXMB5z3dJjUWr7jaVHmVwoLxNwPi+Jh\neBDlddmqAfZpCLhvpdwuVjACfNlyezuXsQ2Cc+sZsELtcW3P8uVo2xu18cFc\nRRciCsDYY8WR38km9AXeJU1MlioeQoex6Sy3n6qf1aPVUcNCqslEZarP8SBu\nblLL\r\n=mV6o\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD1Hxqj5cVTGkctf0NICNOklNySb3EGp+lE+2mSlEZHgQIgSnSByIQSGvdh+09QI4t4lFYnCKj+a2nPBJJg+LlFSak="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_3.1.3_1564445397734_0.5756492480430402"}, "_hasShrinkwrap": false}, "3.1.4": {"name": "ts-mixer", "version": "3.1.4", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/mixins.js", "module": "dist/mixins.js", "types": "dist/mixins.d.ts", "scripts": {"build": "tsc", "build-clean": "rimraf dist && yarn build", "docs": "typedoc --out docs --mode modules src --ignoreCompilerErrors", "docs-clean": "rimraf docs && yarn docs && touch docs/.nojekyll && git add docs", "lint": "eslint src/**/*.ts", "test:es5": "cross-env TS_NODE_COMPILER_OPTIONS=\"{\\\"target\\\": \\\"es5\\\"}\" nyc --silent mocha", "test:es6": "cross-env TS_NODE_COMPILER_OPTIONS=\"{\\\"target\\\": \\\"es6\\\"}\" nyc --silent --no-clean mocha", "test": "yarn test:es5 && yarn test:es6 && nyc report", "coveralls": "cat coverage/lcov.info | coveralls", "preversion": "yarn lint && yarn test && yarn docs-clean"}, "dependencies": {"is-class": "^0.0.8"}, "devDependencies": {"@types/chai": "^4.1.7", "@types/mocha": "^5.2.6", "@types/node": "^12.0.2", "chai": "^4.2.0", "coveralls": "^3.0.3", "cross-env": "^5.2.0", "eslint": "^5.16.0", "mocha": "^6.1.3", "nyc": "^14.1.1", "rimraf": "^2.6.3", "ts-node": "^8.1.0", "typedoc": "^0.14.2", "typescript": "^3.4.5", "typescript-eslint-parser": "^22.0.0"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixins"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "1d8027d687c649df1ad5fbe4222ba4de6319bfdb", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@3.1.4", "_nodeVersion": "10.16.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-6Ai79m/MbBKgf1AoiREEhNEbBC7ToPj4KyUnpBLHvFl96Pf/d1xZ1MKPaTjuTpEcXhGJJCn7QATtB031LW6/6A==", "shasum": "d5bde18d9a0bc185f8308c0f7b68d9bc917710a7", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-3.1.4.tgz", "fileCount": 5, "unpackedSize": 19721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdQN+kCRA9TVsSAnZWagAAj5gQAIVFuFyaHtH1VjrI3tuq\n3Pr7jQmH801ugyGfJgnVEflVOcIWa9L1M4TuAYOZBKc32XaJRbN+BA/AOYkQ\nre+kQ/pKWBKI2bjrT4CNL4+tYPZiZXi4K5vWoSpr9vCBZ078Z8s2jzCy2JGQ\npQ7xEGK++og2oPz5D8qcfJVHTXfZKG0VrhN2SwQKmbjYw9zR0NU3TMds/mCH\nJZz/4SMrMsKv7tUuSy7P78NB1IFrHM+1DgEfizeidRRRPW2MaFIitX4XCCnx\nI93CQytB4Lw9qpEo1lrTL32Cd5xqCaTJfB6twts++sb31CUGJI/XgHOzo1uY\nVlpst/CxRp2uzpHWvRdd2PLEaX5t0j52WUgYdFY1dAzLo7vMmqXGDYjZe3bb\nEFHTgKO5JfMN/AvdDFj2NYOXWq1pgVOKSzTlbVm3rmk6VTheL+viKq4zBeuv\netGOevMBW1YTzCuXxfq1hUeFAosSewc7+9/I3/d6Paipc5Mzf9oGh+NfNmIr\nPyzUibOXsbR4FXfVOS9kzQzIX5CBEX+tvqA0yG30Phalk6TCMPu4y0DY9euj\nBwk+8o6a6GzL6nbzUeE12v4WTA1VSJtLFeRYMMOStkRFhkXPehixcwJ4FNf+\nTLCj7vtRQ+dOb7uVtJ7NoUFIr/8bfiBHRY7wtsOaeUw9jAVEe40BlF7Umbdq\nK9Dh\r\n=s8yt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDVbwVSsGzuTcchpyUiK77hJPJKL6OMpnG5MUOAllHejAiEAsRllW5y6So1bgL+MsMvuBS1zRpYassMRIuk6uP08I0w="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_3.1.4_1564532644336_0.11444340886465709"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "ts-mixer", "version": "4.0.0", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/mixins.js", "module": "dist/mixins.js", "types": "dist/mixins.d.ts", "scripts": {"build": "tsc", "build-clean": "rimraf dist && yarn build", "docs": "typedoc --out docs --mode modules src --ignoreCompilerErrors", "docs-clean": "rimraf docs && yarn docs && touch docs/.nojekyll && git add docs", "lint": "eslint src/**/*.ts", "test:es5": "cross-env TS_NODE_COMPILER_OPTIONS=\"{\\\"target\\\": \\\"es5\\\"}\" nyc --silent mocha", "test:es6": "cross-env TS_NODE_COMPILER_OPTIONS=\"{\\\"target\\\": \\\"es6\\\"}\" nyc --silent --no-clean mocha", "test": "yarn test:es5 && yarn test:es6 && nyc report", "coveralls": "cat coverage/lcov.info | coveralls", "preversion": "yarn lint && yarn test && yarn docs-clean"}, "dependencies": {"is-class": "^0.0.8"}, "devDependencies": {"@types/chai": "^4.1.7", "@types/mocha": "^5.2.6", "@types/node": "^12.0.2", "chai": "^4.2.0", "coveralls": "^3.0.3", "cross-env": "^5.2.0", "eslint": "^5.16.0", "mocha": "^6.1.3", "nyc": "14.1.1", "rimraf": "^2.6.3", "ts-node": "^8.1.0", "typedoc": "^0.14.2", "typescript": "^3.4.5", "typescript-eslint-parser": "^22.0.0"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixins"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "cf7baef338756440cc90151ee413b1157bf788fe", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@4.0.0", "_nodeVersion": "10.16.1", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-OLgrSuUa5idkJw6v8Qki6uspCCeS/Ycb+r92CQRsNYMd44sBKkrwD9WS/j1PtlhUcgYqznGbEsYlGer9BtIO9g==", "shasum": "b9491c4dc36fa5385283957713f236739af05313", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-4.0.0.tgz", "fileCount": 5, "unpackedSize": 18381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdQkVFCRA9TVsSAnZWagAAxUMP/1esyWxCFlFmmB8M0By9\nB1QrWoR1/OwrisKAwlgcRFr6M826PTAR2zcXmzURr5uaDv0ORPnc3kgqXmjz\nRf82XcEPW3nF1vS1DJ6f21APgDebb4pJ6GVu68j5CiUfnIVDSf9haTceWN9e\nKWDQ4kFUTMENx2YOaGSKnqdJa5HTW+ToPF/cnebuYZoJobM8MtT+gqgSlq5m\nOg1+4sOLbMmvfs8uviReggKvv+4wkG/e4kyUdo/mCDZ5OTu+6Umv1TvO9RZJ\nIVCdeXOD0mTlfeCiK7DCts6Iq0HiM+9yaEQT8ck6Bxyb5ALWv5DjO+70b9v4\nxYcZ4uLQu2DLCgiNJf42AjCOTMpXNPPa3tdKpiQJRbqECskutj/FWb3NMml7\nkAUzPB1k07vbcmoiNiU7+pvasyna4qphAyXGrKZQjbdpoWH9bk5Kgl6WZn3C\n+vcFmfmgEnuR+14Wy26NS/OklTKRBKLoD6cvQN6rG7m4b+eoOxICcePCitay\nnJO3JX9i50c5wp4hWvVKH43TfOQkGKYuXIBoIIziTCkvVQU+wVqEcCz5shuq\nY5T8wlPu0EkODrmM+kF+78Is/sAbMNVTsGYM5oiBdsQbhhZiL+k8PSDyWSTj\nXEcTTdvDKCnORZS7CML6Te6FjyxflHKKuyfMNZjFhV6O6h8QMC4FmorDNDh6\nm6m7\r\n=fZ2O\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHfRExxjXKeX42D6LLL2o9F3/0yndAAm9iMRwQKPAc/VAiEA99bZaKRijIQoiWbTcOk+51H10JmAJh8htcQ6hfqI2ak="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_4.0.0_1564624196674_0.8944792019602177"}, "_hasShrinkwrap": false}, "5.0.0-beta.0": {"name": "ts-mixer", "version": "5.0.0-beta.0", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "build-clean": "rimraf dist && yarn build", "lint": "eslint src/**/*.ts", "test": "nyc mocha", "coveralls": "cat coverage/lcov.info | coveralls", "release": "standard-version"}, "devDependencies": {"@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@types/chai": "^4.2.8", "@types/mocha": "^7.0.1", "@types/node": "^13.7.0", "@typescript-eslint/parser": "^2.18.0", "chai": "^4.2.0", "class-validator": "^0.11.0", "coveralls": "^3.0.9", "eslint": "^6.8.0", "husky": "^4.2.1", "mocha": "^7.0.1", "nyc": "14.1.1", "rimraf": "^3.0.1", "standard-version": "^7.1.0", "ts-node": "^8.6.2", "typescript": "^3.7.5"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixin", "mixins", "multiple inheritance", "mixin classes"], "author": {"name": "<PERSON>"}, "license": "MIT", "readme": "# ts-mixer\r\n[![npm version](https://badgen.net/npm/v/ts-mixer)](https://npmjs.com/package/ts-mixer)\r\n[![Build Status](https://travis-ci.org/tannerntannern/ts-mixer.svg?branch=master)](https://travis-ci.org/tannerntannern/ts-mixer)\r\n[![Coverage Status](https://coveralls.io/repos/github/tannerntannern/ts-mixer/badge.svg?branch=master)](https://coveralls.io/github/tannerntannern/ts-mixer?branch=master)\r\n[![Minified Size](https://badgen.net/bundlephobia/min/ts-mixer)](https://bundlephobia.com/result?p=ts-mixer)\r\n[![Conventional Commits](https://badgen.net/badge/conventional%20commits/1.0.0/yellow)](https://conventionalcommits.org)\r\n\r\n### What is it?\r\n`ts-mixer` is a lightweight package that brings mixins to TypeScript.  Mixins in JavaScript are easy, but TypeScript introduces complications.  `ts-mixer` deals with these complications for you and infers all of the intelligent typing you'd expect, including instance properties, methods, static properties, **generics**, and more.\r\n\r\n[Quick start guide](#quick-start)\r\n\r\n### Why another Mixin implementation? \r\nIt seems that no one has been able to implement TypeScript mixins gracefully.  Mixins as described by the [TypeScript docs](https://www.typescriptlang.org/docs/handbook/mixins.html) are far less than ideal.  Countless online threads feature half-working snippets, each one interesting but lacking in its own way.\r\n\r\nMy fruitless search has led me to believe that there is no perfect solution with the current state of TypeScript.  Instead, I present a \"tolerable\" solution that attempts to take the best from the many different implementations while mitigating their flaws as much as possible.\r\n\r\n## Features\r\n* can mix plain classes\r\n* can mix classes that extend other classes\r\n* can mix abstract classes (with caveats)\r\n* can mix generic classes (with caveats)\r\n* proper constructor argument typing (with caveats)\r\n* proper handling of protected/private properties\r\n* proper handling of static properties\r\n* [multiple options](#settings) for mixing (ES6 proxies vs copying properties)\r\n\r\n#### Caveats\r\n* Mixing abstract classes requires a bit of a hack that may break in future versions of TypeScript.  See [dealing with abstract classes](#dealing-with-abstract-classes) below.\r\n* Mixing generic classes requires a more cumbersome notation, but it's still possible.  See [dealing with generics](#dealing-with-generics) below.\r\n* ES6 made it impossible to use `.apply(...)` on class constructors, which means the only way to mix instance properties is to instantiate all the base classes, then copy the properties over to a new object.  This means that (beyond initializing properties on `this`), constructors cannot have [side-effects](https://en.wikipedia.org/wiki/Side_effect_%28computer_science%29) involving `this`, or you will get unexpected results.  Note that constructors need not be _completey_ side-effect free; just when dealing with `this`.\r\n\r\n## Non-features\r\n* `instanceof` support.  Difficult to implement, and not hard to work around (if even needed at all).\r\n\r\n# Quick Start\r\n## Installation\r\n```\r\n$ npm install ts-mixer\r\n```\r\n\r\nor if you prefer [Yarn](https://yarnpkg.com):\r\n\r\n```\r\n$ yarn add ts-mixer\r\n```\r\n\r\n## Examples\r\n### Minimal Example\r\n```typescript\r\nimport { Mixin } from 'ts-mixer';\r\n\r\nclass Foo {\r\n    protected makeFoo() {\r\n        return 'foo';\r\n    }\r\n}\r\n\r\nclass Bar {\r\n    protected makeBar() {\r\n        return 'bar';\r\n    }\r\n}\r\n\r\nclass FooBar extends Mixin(Foo, Bar) {\r\n    public makeFooBar() {\r\n        return this.makeFoo() + this.makeBar();\r\n    }\r\n}\r\n\r\nconst fooBar = new FooBar();\r\n\r\nconsole.log(fooBar.makeFooBar());  // \"foobar\"\r\n```\r\n\r\n[Play with this example](https://www.typescriptlang.org/play/index.html?ssl=1&ssc=1&pln=4&pc=2#code/JYWwDg9gTgLgBAbzgWWAD2AOzgXzgMyghDgHIYBnAWhHQFMpSBuAKBYGMAbAQworgBiECIhZxxcMERh12MgCZwQ3ANZ0hEABQBKURP1wodGAFco2UvmHMxEnC3scefOACFuUPRKkQZcuorKau5QOl4GhsZmFgBGHjb69o5cvPwaIXB0aDKY8vyoGJiaGgA0bh66CLbiYCYxnMDsSqrqwiFhVRHiRqbmcDAAFsAUAHRBrVq6ANT9Q6Pj7dqsiQ5s7BCYFPBWEBkAvHCYdADugm0eOqwcGxQQnHQjnBAA5po7IWMt6RfaS+IA9P84AAiHZxKDAoA)\r\n\r\n### Mixing Abstract Classes\r\nAbstract classes, by definition, cannot be constructed, which means they cannot take on the type, `new(...args) => any`, and by extension, are incompatible with `ts-mixer`.  BUT, you can \"trick\" TypeScript into giving you all the benefits of an abstract class without making it technically abstract.  The trick is just some strategic `// @ts-ignore`'s:\r\n\r\n```typescript\r\nimport { Mixin } from 'ts-mixer';\r\n\r\n// note that Foo is not marked as an abstract class\r\nclass Foo {\r\n    // @ts-ignore: \"Abstract methods can only appear within an abstract class\"\r\n    public abstract makeFoo(): string;\r\n}\r\n\r\nclass Bar {\r\n    public makeBar() {\r\n        return 'bar';\r\n    }\r\n}\r\n\r\nclass FooBar extends Mixin(Foo, Bar) {\r\n    // we still get all the benefits of abstract classes here, because TypeScript\r\n    // will still complain if this method isn't implemented\r\n    public makeFoo() {\r\n        return 'foo';\r\n    }\r\n}\r\n```\r\n\r\n[Play with this example](https://www.typescriptlang.org/play/index.html#code/JYWwDg9gTgLgBAbzgWWAD2AOzgXzgMyghDgHIYBnAWhHQFMpSBuAKBYHp25MIY64YACwCG8AGIQIcYBW684IYVADWdACZxhs4dmEAjCjCjCAxvBMAbLRRaXrcCVIQs4ruJzgABSlWABzHig6AC44ACIAQQMjU3gQOiEINVkTHTgITAsAT00wMDolOAB3YCEsTV1o4zM4OwoKMJc3MABXPQtgE00q2IVhVUcACgBKUMMoLD9WHDY62QAhQuc3OFb2zr7VRagRxCaV1yCYFqhsUj0lZn3cFhnbK3qHSW24OjQ+TGSUdCxBxwAaODbYZ7FYeIr8QzACwWOB+BKaGECQT8PR0TB0fClWQQfDdca9OZ0WQooKAtGpFoUfgAFSy+QAyiYJmAYNdwdDYVCkSZiGArOVgHiyrJ4okNDJMORpOALHR4pg+GprmsOl1FANJLtlgdDgkTmd8JIrisZjMgA)\r\n\r\nDo note that while this does work quite well, it is a bit of a hack and I can't promise that it will continue to work in future TypeScript versions.\r\n\r\n### Mixing Generic Classes\r\nFrustratingly, it is _impossible_ for generic parameters to be referenced in base class expressions.  No matter how you try to slice it, you will eventually run into `Base class expressions cannot reference class type parameters.`\r\n\r\nThe way to get around this is to leverage [declaration merging](https://www.typescriptlang.org/docs/handbook/declaration-merging.html), and a slightly different mixing function from ts-mixer: `mix`.  It works exactly like `Mixin`, except it's a decorator, which means it doesn't affect the type information of the class being decorated.  See it in action below:\r\n\r\n```typescript\r\nimport { mix } from 'ts-mixer';\r\n\r\nclass Foo<T> {\r\n    public fooMethod(input: T): T {\r\n        return input;\r\n    }\r\n}\r\n\r\nclass Bar<T> {\r\n    public barMethod(input: T): T {\r\n        return input;\r\n    }\r\n}\r\n\r\ninterface FooBar<T1, T2> extends Foo<T1>, Bar<T2> { }\r\n@mix(Foo, Bar)\r\nclass FooBar<T1, T2> {\r\n    public fooBarMethod(input1: T1, input2: T2) {\r\n        return [this.fooMethod(input1), this.barMethod(input2)];\r\n    }\r\n}\r\n```\r\n\r\n[Play with this example](https://www.typescriptlang.org/play/index.html?experimentalDecorators=true&ssl=1&ssc=1&pln=22&pc=1#code/JYWwDg9gTgLgBAbziYAPOBfOAzKERwDkMAzgLQqoCmUhA3AFAMDGANgIYklwBiEEAHgAqAPkQM4kuGACuAI1bBmOfgFkqMABYQAJgApgAO1kwAXHCEBKc0PFT7cKBplRDcIycb2MDHyw5ccABC7FDCYggSUrIKSnByoepaugbGMmYW1hZ2DpJOMC5uHuleUj5+RjA02OzMVLz8IWFCAIwANBYATGJUqFWGOtx8gq0iHU3C3YiYDAAClHrD46GW-pxDjaHC7V0RUZIxisrYm1BJ2vrFMC02O1edNp2WObn5hXAA2lrAJAB0JxBzikri1LB1vn8EmcNBdUiYngBdUqScoMIA)\r\n\r\nKey takeaways from this example:\r\n* `interface FooBar<T1, T2> extends Foo<T1>, Bar<T2> { }` makes sure `FooBar` has the typing we want, thanks to declaration merging\r\n* `@mix(Foo, Bar)` wires things up \"on the JavaScript side\", since the interface declaration has nothing to do with runtime behavior.\r\n* The reason we have to use the `mix` decorator is that the typing produced by `Mixin(Foo, Bar)` would conflict with the typing of the interface.  `mix` has no effect \"on the TypeScript side,\" thus avoiding type conflicts.\r\n\r\n## Settings\r\nts-mixer has multiple strategies for mixing classes which can be configured by modifying `Settings` from ts-mixer.  For example:\r\n\r\n```typescript\r\nimport { Settings, Mixin } from 'ts-mixer';\r\n\r\nSettings.prototypeStrategy = 'proxy';\r\n\r\n// then use `Mixin` as normal...\r\n```\r\n\r\n### `Settings.prototypeStrategy`\r\n* Determines how ts-mixer will mix class prototypes together\r\n* Possible values:\r\n    - `'copy'` (default) - Copies all methods from the classes being mixed into a new prototype object.  (This will include all methods up the prototype chains as well.)  This is the default for ES5 compatibility, but it has the downside of stale references.  For example, if you mix `Foo` and `Bar` to make `FooBar`, then redefine a method on `Foo`, `FooBar` will not have the latest methods from `Foo`.  If this is not a concern for you, `'copy'` is the best value for this setting.\r\n    - `'proxy'` - Uses an ES6 Proxy to \"soft mix\" prototypes.  Unlike `'copy'`, updates to the base classes _will_ be reflected in the mixed class, which may be desirable.  The downside is that method access is not as performant, nor is it ES5 compatible.\r\n\r\n### `Settings.staticsStrategy`\r\n* Determines how static properties are inherited\r\n* Possible values:\r\n    - `'copy'` (default) - Simply copies all properties (minus `prototype`) from the base classes/constructor functions onto the mixed class.  Like `Settings.prototypeStrategy = 'copy'`, this strategy also suffers from stale references, but shouldn't be a concern if you don't redefine static methods after mixing.\r\n    - `'proxy'` - Similar to `Settings.prototypeStrategy`, proxy's static method access to base classes.  Has the same benefits/downsides.\r\n\r\n# Author\r\nTanner Nielsen <<EMAIL>>\r\n* Website - [tannernielsen.com](http://tannernielsen.com)\r\n* Github - [tannerntannern](https://github.com/tannerntannern)\r\n", "readmeFilename": "README.md", "gitHead": "1ff40359a6295aa9e43d703cbcf3429caa340e88", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@5.0.0-beta.0", "_nodeVersion": "10.17.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-gFWq4T78NxOd2QymAtAmLx1T1Gx4wikIqkrzBvm8yDjAbWh1uekcgfrBbcJqHUVEpAh/vvdktRe0608Cb3JWng==", "shasum": "bd0911dde0ac2174dbe4d23c26a0b4444494186f", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-5.0.0-beta.0.tgz", "fileCount": 16, "unpackedSize": 30225, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNluICRA9TVsSAnZWagAAs5YP/iVdgdISy+/N4G7mCuGy\nD8MBzFFmgVuz5OLVztPM2/8bgm1MB4kjl7ygWOIoGo6PTipUD/dXhiM48pQV\nBW0SCVleDzVA5KshCv9+TZd7x3HK95KKw8OJzeO4uqKTbYQA8MqrBMM81VXt\nD/qMllys+nm+IALVewWCK74DyNh5UsZY/40CgwmUj/5oP7ZGv4GqVUWhCAeN\nCdN868FbPO0T98b5f7+wMvgK/balms+mydVr5aHDaDmkcG/p6b40hW/1IEo2\nXS2DlD+aoi4bKg4qABGnmANLbumB7AMsp1eB8Br47+XXz/BBL9BWWF8kRhbK\nZQ2x62KS2A7DSvkWjOUYs0AJauwW2B0MRoqcoS4zD8sR99y5aHbghoN9y8Ts\n0dSMq9QnJO/7taTi/YDmNw3f5e6votPE15X1fu6A8w5p0+710ic0TaDZvYlI\nHFzCiJsFlgpY31YZs5ylKyGISIAbWjiQ8of666psFvL4ZoOc/tTZE6kUDfi6\nKprYmCmRiNCbAiQQwkdpEkqwnhgq89A5OnnetanB1KS+n6K992oQmR79AY+H\nfiGBgj3SC8M+D9SeSpdFyGjXWipzp29POgY976b8jm3Qto9AsnI0DtA+GWUZ\nLR/zyVKMAv4QiC1BmBrTSmIHBnvF4GCTLDT4w31uZ76x9PfH8SWQOOHlqqsC\nBJyO\r\n=wuL5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICZptS2KfEPF8j23dc6C+5evT927kf4vqahexzQYXmv/AiBVY6tmLMa97RMQd0K3DrOT+nrLzyvir0xcbKTqLAtCjQ=="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_5.0.0-beta.0_1580620679548_0.7052561823371701"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "ts-mixer", "version": "5.0.0", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "build-clean": "rimraf dist && yarn build", "lint": "eslint src/**/*.ts", "test": "nyc mocha", "coveralls": "cat coverage/lcov.info | coveralls", "release": "standard-version"}, "devDependencies": {"@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@types/chai": "^4.2.9", "@types/mocha": "^7.0.1", "@types/node": "^13.7.7", "@typescript-eslint/parser": "^2.21.0", "chai": "^4.2.0", "class-validator": "^0.11.0", "coveralls": "^3.0.9", "eslint": "^6.8.0", "husky": "^4.2.3", "mocha": "^7.1.0", "nyc": "14.1.1", "rimraf": "^3.0.2", "standard-version": "^7.1.0", "ts-node": "^8.6.2", "typescript": "^3.8.3"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixin", "mixins", "multiple inheritance", "mixin classes"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "40c30e5ea185953739e14996c58522a4e3bbad3c", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@5.0.0", "_nodeVersion": "12.16.1", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-xknwJQJgjpAu8QT4WjMuPyLy3cTkhUbk5QBs/s/xvQ115e5aBR5gU1/66zeYXdxU4bc2F5bexzqkfAeVfI4J/Q==", "shasum": "e0077a624048b2d51d3152ab5fe42d0fb82b7d96", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-5.0.0.tgz", "fileCount": 16, "unpackedSize": 29801, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeWyZDCRA9TVsSAnZWagAAaiEP/0mpCJgjf9hhjhvtphpG\nSVJOHj4AMOSMIuxNTYiV7Q+4zD0wV6Crl3TN9Ow42OQJN6g2AoSD5Ro3Jjsa\neZh6YvQn3uGBPm+8Fc6sC/GXvbPVwgTAtXeBnk0oUGqpKoTYA3Pfj1PlPldq\nBHTbxHF8ipUdQ/WSd8MpNrpIpUwigFzYLMioTj2p39uyaCTrEjLU/ZsEfyKF\ni8v7f/RL4izXTuyLU5M/6TrjWCy3xNuHxch+UMG+dpa4QK2BYR20SKosK17W\nXy6kDIOlkzgrwrABQNLUANeTX46pMBdcZVsdGox6eMYuFLQrg98zFsNSB2KM\nNmlW2/JoWFnnZmeVgR3caywP03h48uuGAnVbWVaYeO3mvxw0h2XYQKZXtLww\n/8l//wlT2APqKdhwVM3XIBOEYFnI869fMhoLoCAXfT0opm3M2gfyQ9HhJuuW\nPyh0l1AThzLDAo7Icrw2UkddeaPwuuCCt8VOf4P6okFx1LMs/hnmyJMSZR6n\nkh9ZGTQg3blvCKqAsGUU7xLHJF0zkNH1TuwWQbyhUfpSDWDUIy/0ZDZd924H\n0HBy+Pgj+zycpd0s+OaMb9a3vFC0B223bPno6qJ4J3CroztIwvvWiSeLwHc6\nBUfpmkSVPQHRCQ6G+NDvC5Kjt0q+iu2CQGcDzTwhKohlD/efi1KzBSsrM5Cn\nBG3c\r\n=btPG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA/Azvei4WPwiToSP7GIH/3xhMEBv/NNA0jGjDR1hwW4AiEA9/XNRt3AQuHe/zuRrLdC+d249s7XH4UIOT7+59T0vVk="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_5.0.0_1583031875172_0.9696296617086255"}, "_hasShrinkwrap": false}, "5.1.0-beta.0": {"name": "ts-mixer", "version": "5.1.0-beta.0", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "build-clean": "rimraf dist && yarn build", "lint": "eslint src/**/*.ts", "test": "nyc mocha", "coveralls": "cat coverage/lcov.info | coveralls", "release": "standard-version"}, "devDependencies": {"@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@types/chai": "^4.2.11", "@types/mocha": "^7.0.2", "@types/node": "^13.9.3", "@types/sinon": "^7.5.2", "@typescript-eslint/parser": "^2.25.0", "chai": "^4.2.0", "class-validator": "^0.11.0", "coveralls": "^3.0.11", "eslint": "^6.8.0", "husky": "^4.2.3", "mocha": "^7.1.1", "nyc": "14.1.1", "rimraf": "^3.0.2", "sinon": "^9.0.1", "standard-version": "^7.1.0", "ts-node": "^8.8.1", "typescript": "^3.8.3"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixin", "mixins", "multiple inheritance", "mixin classes"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "3a8adc22c80e3f2c97c7c7af77ca4663da67a51f", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@5.1.0-beta.0", "_nodeVersion": "12.16.1", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-cpBXe+iGANNtBskTJFgGMZ/A8mfhKQGbon3X630SMRSg64oIpuCFARFYgRltst/lfkkX8Ge8bP9xgkZdEnWdYw==", "shasum": "3d7a1ccebebe421b31d87a294f51a07921667e38", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-5.1.0-beta.0.tgz", "fileCount": 18, "unpackedSize": 35397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefEF/CRA9TVsSAnZWagAAfXEP/07t/MeOXvZsiw6aDi85\nUzIqX7aa5UWl3hOSfzvtLGAeuXAqv0rEbKwQJb11fv5PBgg+yPZKEWkcvMzh\nnIY1OWStQARYcXmhe0065VRbIdtkyQ+qXc60InL6LgNeIvho9X6Xwb62W6pl\nmsrdWEgQ+ML8Z57nB2yTcvQOoaF4SuCZhkbcEYwd25NYFJ3wU0kEMDBlzd8o\nHaMFf7s2gE0Dn5OB7Ji5VvLassMklioON9lAjoYenszJ2zBm7EHI5sfaZz8J\nN+VTFDveu9L2qig8LlngY59e/vexHJ6AhdpCgC6/gxe3m7s1vUAXv6bRkFGp\nrQp/hfjUpyM9pEYZSMCgXT3MLyvZURdiGrzsmSC8G2NSHg571acaYjiHzzf2\nR736jqZTM84k0DSaBWFUZZByTfWbqzTJwi0EzEMuEf5lMgZAGuMUH2LvuUbM\nWyFomuJjFfo3DjI6g8YZ12VQbIaC3g4igWuk8U0LqO3TyIBH5qw1KvL1OX7n\nsf5RmcapuMw6HVkIdVdeKnI35GdjFH2UxPPDgVbeu+XE0Kd4ivPJX3HfG4Uk\n1+FK6q/69YIBL4DCoYIKAqxUYVgwhNfp88WxJ03wa8SnU9lVetL5ScvdothU\nsv9k3Xiog5w3fviTN87B8IQ9ceaKFhjckc0q2VtLxEAn5r/KGW87JQXjo96f\nTNrq\r\n=PnVI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDWo284aEa8rwaUPE+CA1eOX/4r6ZzSjKuFfI8B4YcWAwIgIAvcc2lcqW2ZLLkYIqD4FImQDVBnYk0fXw89lCx0lyk="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_5.1.0-beta.0_1585201534812_0.48760993861053725"}, "_hasShrinkwrap": false}, "5.1.0": {"name": "ts-mixer", "version": "5.1.0", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "build-clean": "rimraf dist && yarn build", "lint": "eslint src/**/*.ts", "test": "nyc mocha", "coveralls": "cat coverage/lcov.info | coveralls", "release": "standard-version"}, "devDependencies": {"@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@types/chai": "^4.2.11", "@types/mocha": "^7.0.2", "@types/node": "^13.9.3", "@types/sinon": "^7.5.2", "@typescript-eslint/parser": "^2.25.0", "chai": "^4.2.0", "class-validator": "^0.11.0", "coveralls": "^3.0.11", "eslint": "^6.8.0", "husky": "^4.2.3", "mocha": "^7.1.1", "nyc": "14.1.1", "rimraf": "^3.0.2", "sinon": "^9.0.1", "standard-version": "^7.1.0", "ts-node": "^8.8.1", "typescript": "^3.8.3"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixin", "mixins", "multiple inheritance", "mixin classes"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "dd2c04b9c868f743988bb0774fc01917acc77f4a", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@5.1.0", "_nodeVersion": "12.16.1", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-u6caArZIK/XXKq80fWyE8JOnWSOod7Q7lGdJKZ/d2xP0g8cBj5+rHpJzCz3mvy5sUwnfN42bH9BWHuek/pSH5g==", "shasum": "4edc3792d30e706db9bdd2e9442758146a3a2189", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-5.1.0.tgz", "fileCount": 18, "unpackedSize": 35713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefXbpCRA9TVsSAnZWagAAnosP/1p8W7D/Q54YsPVC3YRu\nbOxDjBjqKT4fIrX4A27IHDu+cDytbm8wVYCRxwxdaOWNfqQyaBjvKuZ0Tuez\nI8kr3ZoroZWWvVGo2k61W9hR68zqaD+Mjy2xFL8dbJsXdKs7GUPcjbOLcaRd\n4uqUitPDaaTUw0n4Oa0OXoespwQezp15ZBe9boDdUIxMcpTw5YNIwZ1+7+e+\nCllqEv6Wz7qPJ/dnPbKKXYelX+uMB0GAkVndtHZJV50ulb7N58Vzq1l2uFVK\nXkY/3BaLAUnD0oDZCXIw/VFIYokMsHRnO4eLI/CNeMW35TheFYuzxszyn4V6\ncc8KjWDqFt7SulIAhfpAi7/o6RTy+Pm+w9IRiWJdFm5M+EJZyB5UjJwyhFnb\ne9ikkp8uTzKQVGNAaELwjzBHRUYWLzkOhSdSxOQQasK51g6yv/V64TaglLNv\nUuoBLJblMa+ay+r/AqkY5FeFK3HgAiQDZgbpg89U827zfoQ72xmCGIP6mLyL\nXDZxKlzAvXh+3RvM55Uaqw+6DlnNValsEKycUdQ3jHD8wuOdSOSN+ezztzds\n6Xyj7Uh/tsmj4oRwe2buCmFIi0fqD2aqCPL8fmA1Y6+kqXEJTkDrfH0bUVnI\n6hi39VQ+cHFxDzbOuBQlj2rdIDuvYqm1qajOe/C6Iha9VAT11Xrl377lZ2qs\nXsL+\r\n=gCW0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDU6PzA9xi7ONOz7aWESk7K27VZn9li7pwZ74GLQmAtwwIhAKoge3KxGOn75f4eQOei8AzSxLEDymwDYkByb4yojRPS"}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_5.1.0_1585280745400_0.9475693556586788"}, "_hasShrinkwrap": false}, "5.2.0-beta.0": {"name": "ts-mixer", "version": "5.2.0-beta.0", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "build-clean": "rimraf dist && yarn build", "lint": "eslint src/**/*.ts", "test": "nyc mocha", "coveralls": "cat coverage/lcov.info | coveralls", "release": "standard-version"}, "devDependencies": {"@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@types/chai": "^4.2.11", "@types/mocha": "^7.0.2", "@types/node": "^13.11.1", "@types/sinon": "^9.0.0", "@typescript-eslint/parser": "^2.27.0", "chai": "^4.2.0", "class-validator": "^0.11.1", "coveralls": "^3.0.11", "eslint": "^6.8.0", "husky": "^4.2.5", "mocha": "^7.1.1", "nyc": "14.1.1", "rimraf": "^3.0.2", "sinon": "^9.0.2", "standard-version": "^7.1.0", "ts-node": "^8.8.2", "typescript": "^3.8.3"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixin", "mixins", "multiple inheritance", "mixin classes"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "2cbddccb980a1127e9b70e9b85fbd3094316554a", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@5.2.0-beta.0", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-Nbb0F25ugCSBstzzMUn078jKg01RjLtOdUYsbaHP9hxJINPYU5atJDzIqxH1CvxPsT1lOTBdk4UjvRYgQ653eQ==", "shasum": "ea8da678a217c6faf38ba8d3710741dca22e1ff7", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-5.2.0-beta.0.tgz", "fileCount": 18, "unpackedSize": 37273, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJek/1JCRA9TVsSAnZWagAAP0AP/1SaqGzaBNxIWyRellMB\npm+kqhvVXTvIt62Q3wv5qj7jf2tyv2cb1XCbgYbOv5yyVQVSisHWunvsNGBz\nA5q/j8B8ApX7HHfbftyd/AKL0d0ThEUjbn+Ez+9gGf6eU3Ktc4zEvMmxk3yb\nIWVpi/+ZSIQbFbV6Pgc7EDY4+10C8YC4vB1xju3Ufdn1iTLZQqOHLQp08PZS\nVusC7eXCDcK9fqllCP4COXTrNe6XSy9qcZsm0n9bQdT7IgJVcZEv0ROkHBVK\nOxdMjEoTXhSG25osQauGHvr2GDz8K/6pKF6/nBV5qnhIYbKQPP4DYRZY29EX\nfAbWk6wB9VaE6blcM4Md51gEn5jTUMz+p8jDiKdFPmrL4HMM3/ZSc8t241fX\nnPPYmI4FZm7GZVSDajWYxPZOiiKJ/+LKwLMko7mmLJKTzP2x3rjK/sh+3osw\ncn4T+DjW2ojZNLfUPpd4+/ORGVpWDYaRKUu+3JpViok+lDv/JaNHt2o/uuap\nk8IAmh7D5NciJRDA8rv+2+VmqH2I/rPuMSwgN3+bmk52LXFSn2ae8MxzPZmL\nGjDu8zVNOYwLZ1AyfkP0CkukSncoxUApV1+obSBpzvTy/WtE4VfvvULlH7so\n++3QOBUxBB9vmQDk8Ah9cglG/FM+yJ4I3/g3V5nYFUkTO0d1N4q/FYtt4Gtb\nGJ50\r\n=mBUC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDXG/uqU38eF5mZ3pydntEDrtByyG1Cfhu8n1NssKvDIQIgSXCeO9+HAKxNeNgmbiFW75LpHci3lH+2CdUyoNVV4wA="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_5.2.0-beta.0_1586756936656_0.6940213324453655"}, "_hasShrinkwrap": false}, "5.2.0-beta.1": {"name": "ts-mixer", "version": "5.2.0-beta.1", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "build-clean": "rimraf dist && yarn build", "lint": "eslint src/**/*.ts", "test": "nyc mocha", "coveralls": "cat coverage/lcov.info | coveralls", "release": "standard-version"}, "devDependencies": {"@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@types/chai": "^4.2.11", "@types/mocha": "^7.0.2", "@types/node": "^13.11.1", "@types/sinon": "^9.0.0", "@typescript-eslint/parser": "^2.27.0", "chai": "^4.2.0", "class-validator": "^0.11.1", "coveralls": "^3.0.11", "eslint": "^6.8.0", "husky": "^4.2.5", "mocha": "^7.1.1", "nyc": "14.1.1", "rimraf": "^3.0.2", "sinon": "^9.0.2", "standard-version": "^7.1.0", "ts-node": "^8.8.2", "typescript": "^3.8.3"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixin", "mixins", "multiple inheritance", "mixin classes"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "dafc33fd02915f7354b5c87e303c879981e76d68", "readme": "# ts-mixer\n[![npm version](https://badgen.net/npm/v/ts-mixer)](https://npmjs.com/package/ts-mixer)\n[![Build Status](https://travis-ci.org/tannerntannern/ts-mixer.svg?branch=master)](https://travis-ci.org/tannerntannern/ts-mixer)\n[![Coverage Status](https://coveralls.io/repos/github/tannerntannern/ts-mixer/badge.svg?branch=master)](https://coveralls.io/github/tannerntannern/ts-mixer?branch=master)\n[![Minified Size](https://badgen.net/bundlephobia/min/ts-mixer)](https://bundlephobia.com/result?p=ts-mixer)\n[![Conventional Commits](https://badgen.net/badge/conventional%20commits/1.0.0/yellow)](https://conventionalcommits.org)\n\n## Overview\n`ts-mixer` brings mixins to TypeScript.  \"Mixins\" to `ts-mixer` are just classes, so you already know how to write them, and you can probably mix classes from your favorite library without trouble.\n\nThe mixin problem is more nuanced than it appears.  I've seen countless code snippets that work for certain situations, but fail in others.  `ts-mixer` tries to take the best from all these solutions while accounting for the situations you might not have considered.\n\n[Quick start guide](#quick-start)\n\n### Features\n* mixes plain classes\n* mixes classes that extend other classes\n* supports static properties\n* supports protected/private properties (the popular function-that-returns-a-class solution does not)\n* mixes abstract classes (with caveats [[1](#caveats)])\n* mixes generic classes (with caveats [[2](#caveats)])\n* supports class, method, and property decorators (with caveats [[3](#caveats)])\n* mostly supports the complexity presented by constructor functions (with caveats [[4](#caveats)])\n* [multiple mixing strategies](#settings) (ES6 proxies vs hard copy)\n\n### Caveats\n1. Mixing abstract classes requires a bit of a hack that may break in future versions of TypeScript.  See [mixing abstract classes](#mixing-abstract-classes) below.\n2. Mixing generic classes requires a more cumbersome notation, but it's still possible.  See [mixing generic classes](#mixing-generic-classes) below.\n3. Using decorators in mixed classes also requires a more cumbersome notation.  See [mixing with decorators](#mixing-with-decorators) below.\n4. ES6 made it impossible to use `.apply(...)` on class constructors (or any means of calling them without `new`), which makes it impossible for `ts-mixer` to pass the proper `this` to your constructors.  This may or may not be an issue for your code, but there are options to work around it.  See [dealing with constructors](#dealing-with-constructors) below.\n\n### Non-features\n* `instanceof` support.  Difficult to implement, and not hard to work around (if even needed at all).\n\n## Quick Start\n### Installation\n```\n$ npm install ts-mixer\n```\n\nor if you prefer [Yarn](https://yarnpkg.com):\n\n```\n$ yarn add ts-mixer\n```\n\n### Basic Example\n```typescript\nimport { Mixin } from 'ts-mixer';\n\nclass Foo {\n    protected makeFoo() {\n        return 'foo';\n    }\n}\n\nclass Bar {\n    protected makeBar() {\n        return 'bar';\n    }\n}\n\nclass FooBar extends Mixin(Foo, Bar) {\n    public makeFooBar() {\n        return this.makeFoo() + this.makeBar();\n    }\n}\n\nconst fooBar = new FooBar();\n\nconsole.log(fooBar.makeFooBar());  // \"foobar\"\n```\n\n## Special Cases\n### Mixing Abstract Classes\nAbstract classes, by definition, cannot be constructed, which means they cannot take on the type, `new(...args) => any`, and by extension, are incompatible with `ts-mixer`.  BUT, you can \"trick\" TypeScript into giving you all the benefits of an abstract class without making it technically abstract.  The trick is just some strategic `// @ts-ignore`'s:\n\n```typescript\nimport { Mixin } from 'ts-mixer';\n\n// note that Foo is not marked as an abstract class\nclass Foo {\n    // @ts-ignore: \"Abstract methods can only appear within an abstract class\"\n    public abstract makeFoo(): string;\n}\n\nclass Bar {\n    public makeBar() {\n        return 'bar';\n    }\n}\n\nclass FooBar extends Mixin(Foo, Bar) {\n    // we still get all the benefits of abstract classes here, because TypeScript\n    // will still complain if this method isn't implemented\n    public makeFoo() {\n        return 'foo';\n    }\n}\n```\n\nDo note that while this does work quite well, it is a bit of a hack and I can't promise that it will continue to work in future TypeScript versions.\n\n### Mixing Generic Classes\nFrustratingly, it is _impossible_ for generic parameters to be referenced in base class expressions.  No matter what, you will eventually run into `Base class expressions cannot reference class type parameters.`\n\nThe way to get around this is to leverage [declaration merging](https://www.typescriptlang.org/docs/handbook/declaration-merging.html), and a slightly different mixing function from ts-mixer: `mix`.  It works exactly like `Mixin`, except it's a decorator, which means it doesn't affect the type information of the class being decorated.  See it in action below:\n\n```typescript\nimport { mix } from 'ts-mixer';\n\nclass Foo<T> {\n    public fooMethod(input: T): T {\n        return input;\n    }\n}\n\nclass Bar<T> {\n    public barMethod(input: T): T {\n        return input;\n    }\n}\n\ninterface FooBar<T1, T2> extends Foo<T1>, Bar<T2> { }\n@mix(Foo, Bar)\nclass FooBar<T1, T2> {\n    public fooBarMethod(input1: T1, input2: T2) {\n        return [this.fooMethod(input1), this.barMethod(input2)];\n    }\n}\n```\n\nKey takeaways from this example:\n* `interface FooBar<T1, T2> extends Foo<T1>, Bar<T2> { }` makes sure `FooBar` has the typing we want, thanks to declaration merging\n* `@mix(Foo, Bar)` wires things up \"on the JavaScript side\", since the interface declaration has nothing to do with runtime behavior.\n* The reason we have to use the `mix` decorator is that the typing produced by `Mixin(Foo, Bar)` would conflict with the typing of the interface.  `mix` has no effect \"on the TypeScript side,\" thus avoiding type conflicts.\n\n### Mixing with Decorators\nPopular libraries such as [class-validator](https://github.com/typestack/class-validator) and [TypeORM](https://github.com/typeorm/typeorm) use decorators to add functionality.  Unfortunately, `ts-mixer` has no way of knowing what these libraries do with the decorators behind the scenes.  So if you want these decorators to be \"inherited\" with classes you plan to mix, you first have to wrap them with a special `decorate` function exported by `ts-mixer`.  Here's an example using `class-validator`:\n\n```typescript\nimport { IsBoolean, IsIn, validate } from 'class-validator';\nimport { Mixin, decorate } from 'ts-mixer';\n\nclass Disposable {\n    @decorate(IsBoolean())  // instead of @IsBoolean()\n    isDisposed: boolean = false;\n}\n\nclass Statusable {\n    @decorate(IsIn(['red', 'green']))  // instead of @IsIn(['red', 'green'])\n    status: string = 'green';\n}\n\nclass ExtendedObject extends Mixin(Disposable, Statusable) {}\n\nconst extendedObject = new ExtendedObject();\nextendedObject.status = 'blue';\n\nvalidate(extendedObject).then(errors => {\n    console.log(errors);\n});\n```\n\n### Dealing with Constructors\nAs mentioned in the [caveats section](#caveats), ES6 disallowed calling constructor functions without `new`.  This means that the only way for `ts-mixer` to mix instance properties is to instantiate each base class separately, then copy the instance properties into a common object.  The consequence of this is that constructors mixed by `ts-mixer` will _not_ receive the proper `this`.\n\n**This very well may not be an issue for you!**  It only means that your constructors need to be \"mostly pure\" in terms of how they handle `this`.  Specifically, your constructors cannot produce [side effects](https://en.wikipedia.org/wiki/Side_effect_%28computer_science%29) involving `this`, _other than adding properties to `this`_ (the most common side effect in JavaScript constructors).\n\nIf you simply cannot eliminate `this` side effects from your constructor, there is a workaround available:  `ts-mixer` will automatically forward constructor parameters to a predesignated init function (`settings.initFunction`) if it's present on the class.  Unlike constructors, functions can be called with an arbitrary `this`, so this predesignated init function _will_ have the proper `this`.  Here's a basic example:\n\n```typescript\nimport { Mixin, settings } from 'ts-mixer';\n\nsettings.initFunction = 'init';\n\nclass Person {\n    public static allPeople: Set<Person> = new Set();\n    \n    protected init() {\n        Person.allPeople.add(this);\n    }\n}\n\ntype PartyAffiliation = 'democrat' | 'republican';\n\nclass PoliticalParticipant {\n    public static democrats: Set<PoliticalParticipant> = new Set();\n    public static republicans: Set<PoliticalParticipant> = new Set();\n    \n    public party: PartyAffiliation;\n    \n    // note that these same args will also be passed to init function\n    public constructor(party: PartyAffiliation) {\n        this.party = party;\n    }\n    \n    protected init(party: PartyAffiliation) {\n        if (party === 'democrat')\n            PoliticalParticipant.democrats.add(this);\n        else\n            PoliticalParticipant.republicans.add(this);\n    }\n}\n\nclass Voter extends Mixin(Person, PoliticalParticipant) {}\n\nconst v1 = new Voter('democrat');\nconst v2 = new Voter('democrat');\nconst v3 = new Voter('republican');\nconst v4 = new Voter('republican');\n```\n\nNote the above `.add(this)` statements.  These would not work as expected if they were placed in the constructor instead, since `this` is not the same between the constructor and `init`, as explained above.\n\n## Settings\nts-mixer has multiple strategies for mixing classes which can be configured by modifying `settings` from ts-mixer.  For example:\n\n```typescript\nimport { settings, Mixin } from 'ts-mixer';\n\nsettings.prototypeStrategy = 'proxy';\n\n// then use `Mixin` as normal...\n```\n\n### `settings.prototypeStrategy`\n* Determines how ts-mixer will mix class prototypes together\n* Possible values:\n    - `'copy'` (default) - Copies all methods from the classes being mixed into a new prototype object.  (This will include all methods up the prototype chains as well.)  This is the default for ES5 compatibility, but it has the downside of stale references.  For example, if you mix `Foo` and `Bar` to make `FooBar`, then redefine a method on `Foo`, `FooBar` will not have the latest methods from `Foo`.  If this is not a concern for you, `'copy'` is the best value for this setting.\n    - `'proxy'` - Uses an ES6 Proxy to \"soft mix\" prototypes.  Unlike `'copy'`, updates to the base classes _will_ be reflected in the mixed class, which may be desirable.  The downside is that method access is not as performant, nor is it ES5 compatible.\n\n### `settings.staticsStrategy`\n* Determines how static properties are inherited\n* Possible values:\n    - `'copy'` (default) - Simply copies all properties (minus `prototype`) from the base classes/constructor functions onto the mixed class.  Like `settings.prototypeStrategy = 'copy'`, this strategy also suffers from stale references, but shouldn't be a concern if you don't redefine static methods after mixing.\n    - `'proxy'` - Similar to `settings.prototypeStrategy`, proxy's static method access to base classes.  Has the same benefits/downsides.\n\n### `settings.initFunction`\n* If set, `ts-mixer` will automatically call the function with this name upon construction\n* Possible values:\n    - `null` (default) - disables the behavior\n    - a string - function name to call upon construction\n* Read more about why you would want this in [dealing with constructors](#dealing-with-constructors)\n\n# Author\nTanner Nielsen <<EMAIL>>\n* Website - [tannernielsen.com](http://tannernielsen.com)\n* Github - [tannerntannern](https://github.com/tannerntannern)\n", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@5.2.0-beta.1", "_nodeVersion": "12.16.2", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-ip72cMYh5W8iRqjUTadbzczxhsMlBv7lkdFuxfJ2vCEi0pOxTEBwdBnGjUx5G2jaiySWc22nndsNCxgNj9us8g==", "shasum": "6e7f549db50a162f9d88e1eece3da08e01aae46d", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-5.2.0-beta.1.tgz", "fileCount": 18, "unpackedSize": 38021, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeoRi5CRA9TVsSAnZWagAAI4cP/2VtLV0MXYsNobTekI1L\nKE75qehWLJ9J3Lsua3IbxvkYIK5J2VjaariNbTB5vSRl7Jv6q/knSW2W/dx/\nZEnMtX7/76U7XA2WBdLqRWL03Cwm7C+6TGrIrhmLPkFk2xUDxRzOiOiuifjW\nQkkNIZfwAlbOJmsjIEtNZXj37ExXfSq6CA0zSnTxw32kfiAeser4vsBa9Eqp\nqc0KwaXs54HreCJGAnh+Gi/2Pxjz5MHvZqzv2kHnDgTLs8Rl3hgCEvKRyoGX\n21FHrnX/OfSE4430p6N8I73AX1hJO3X7TCsAU7O5qh5JYOCFH3IjXtNjiOrW\nwWhIJn1Ms3au4PKbTpJR3wCz3JrEGZR49JvfWHoTgfkr2wMBejw0jPz6qBA6\nLCupsWM+TCAJ4W3RSsIVDuL95sREFH29KBePceIGlmh60OxX61GINeCWzzfS\nl9UVbaOdHEKVD6ZbC7dSD1px1uGkiUP4ppJwrOZQ8PBmbEe/mPPodHfsHuim\nWwy7DrVmHz4SjK9AKksPyJMkSTfCj0EAaDIg/vvDuPTHsX6jwple/VbLlXCY\nrkVdrcjHDXSfPMZXggp5FqK0WV/YORQSXgSdA4XVrkxn/bcfOpJHMwl0UbFT\nZRGVCUxi8FJyo3TCvPvVDWFgJR/hH0bpEcJYThOxf9r9N9g7UWGTguYeSzpe\n7q5/\r\n=+SUg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFVypyfCFvuerr5bG0gekiSa+w6wZJdDE6C9S6yor6EWAiB4A7qrnBZkBH8R25/uWPDFu+v4ItiOmL8xss18C2hiww=="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_5.2.0-beta.1_1587615929257_0.0177975348032966"}, "_hasShrinkwrap": false}, "5.2.0": {"name": "ts-mixer", "version": "5.2.0", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "build-clean": "rimraf dist && yarn build", "lint": "eslint src/**/*.ts", "test": "nyc mocha", "coveralls": "cat coverage/lcov.info | coveralls", "release": "standard-version"}, "devDependencies": {"@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@types/chai": "^4.2.11", "@types/mocha": "^7.0.2", "@types/node": "^13.11.1", "@types/sinon": "^9.0.0", "@typescript-eslint/parser": "^2.27.0", "chai": "^4.2.0", "class-validator": "^0.11.1", "coveralls": "^3.0.11", "eslint": "^6.8.0", "husky": "^4.2.5", "mocha": "^7.1.1", "nyc": "14.1.1", "rimraf": "^3.0.2", "sinon": "^9.0.2", "standard-version": "^7.1.0", "ts-node": "^8.8.2", "typescript": "^3.8.3"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixin", "mixins", "multiple inheritance", "mixin classes"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "b36ad538673019fecfdc321cfee4c6dc78f4963c", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@5.2.0", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-pp+R/Fva/GkZZGpP/2fMkKIUB5C0DdAK5RT+8riCWtFXlFQmgWKc/PI++8ZN6J8uTEryFTM9nREoqJEQ0AvMzg==", "shasum": "77b5ce44383fde7ba9f0d7741571a79aa5506e74", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-5.2.0.tgz", "fileCount": 18, "unpackedSize": 38427, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqPfkCRA9TVsSAnZWagAA5KoQAINgogPYQoSi84fGMxRg\nPtdR6lM9em7zYwpN0VWvT2BOi+zzsLEFL5Mq5rDllj2w/XYfbQq+FwlawXJa\n06ofTae/+VLsZKwSuBEi1ctaCu8ahy9O7Nt7afyIlQcAkQC3kjKNKEagjvcT\nVRt3tThOlePqrJhnOPe/r9I1FZSUE9aHx+Ks4lG91gf9MN79H5d+9Zqw0eCz\nLfaHEq6NrTmJRhygMKtDK/0xZo8TdfOQ6INXtMp7a1zGWzvg8kQ8bDq1Foo5\nqVBw/tV5OOOOxbrIlqWVlCVSrer18YpKX0ckgtEBqbmUL/6bzTxXex9EBuDc\nmHAtwfaaR2OIWM1Q2pwfU4fTA30aVC3GyxaWDiewTtKZlbG6LfTmKxUiuEMN\nDYqnfKQ8fH4hWKCLJcdOEr7URUeUvVXfJ1n/l3Vxv3+j+rPTlryUxbF0X09V\nRW13w84Aud6AbfwFXd37u7r6gQLq3OVv2SH+CzzTe9VdxLRkhF71qXQI3WzE\nUEJCOS8PxUxUKhNEEux2p/OAI3uus7s4LIB1RrljuhCed4rFIqdA/w9tn619\ng5VNar/vQ7B34+XxgQiTlGCx357/v2CXoWwLQ3rKDW3TeRU1j1Ujgv/GK2M4\nqZRLzEiv08EckZNcLhXUBywKhnpdntkH9scwNs0OzsbGTnfJ38eWZFX8rZpl\nPXE7\r\n=FOHi\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDy2GTT3QhYJ+rNO+r7BIWLT+Qdshuk+H6bqKNwiMJsRAIgbYGAwP5jILGe63fDhSJZXL+5DEc5vpYG3guhbgaFtnc="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_5.2.0_1588131811749_0.4096584356415911"}, "_hasShrinkwrap": false}, "5.2.1": {"name": "ts-mixer", "version": "5.2.1", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "build-clean": "rimraf dist && yarn build", "lint": "eslint src/**/*.ts", "test": "nyc mocha", "coveralls": "cat coverage/lcov.info | coveralls", "release": "standard-version"}, "devDependencies": {"@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@types/chai": "^4.2.11", "@types/mocha": "^7.0.2", "@types/node": "^13.13.5", "@types/sinon": "^9.0.0", "@typescript-eslint/parser": "^2.31.0", "chai": "^4.2.0", "class-validator": "^0.12.2", "coveralls": "^3.1.0", "eslint": "^6.8.0", "husky": "^4.2.5", "mocha": "^7.1.2", "nyc": "14.1.1", "rimraf": "^3.0.2", "sinon": "^9.0.2", "standard-version": "^8.0.0", "ts-node": "^8.10.1", "typescript": "^3.8.3"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixin", "mixins", "multiple inheritance", "mixin classes"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "b3a72d30d3d3a85d041cdc82f1a84ca532e5ca7c", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@5.2.1", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-JD0OH+4J4tsBLzcjv/oPEfRNOULYJYgJ6CiYqUlSKv5+bncVVxFSu1cxLd8+d9am91XqVoB8IMx7MXntf/PuJg==", "shasum": "59bf13bb3aa5a332b1ec8d042cbe3af9a860f500", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-5.2.1.tgz", "fileCount": 18, "unpackedSize": 38859, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetVcFCRA9TVsSAnZWagAAZTIP/3mKIiFxEfpgF3hfsn0U\nqenOMy6/DIFPfB52ng94wiTrLvJFI3wIMYF6kwkv4w4dNWgyFv05Jwn9n1By\ncmxgjjCVBIxUKxkWCzzA/m6g4wGqIs45wmCGE2zb2WqMwa6uPhNyuZoSpqMi\n2KosANe9aUY7+5R8pMnCQ0/lQV4EAVKO7vBGcWLZcualJZBmNtmm8agyYySo\nlOApAoEQ8n9SXLv91Hd2IclYHlf5ukY0McQNHGzsZKVCRNiIrlwEjnc5REgI\nw/Yi+i6v3SjX8CqmAcjyn9wYTnogIgY4UfdLAtlvKw0e3IPo2bOiXK8b4wnC\n1o/Bod0GdEUalSIQ8woBlADosB+9ir08LubMDXI8AfKO+uT3jaZproG3Zm+d\nYCykpMUwRn7ej232jZDk012dCU9w9AzoRQPw9m6ZPk2ZE3hRFavhjTJSDVkA\niGNIcgEfYboiKhKr6mw0xrySgBGm74tSwhgqXDaAlB0JYLXX5aCJ+6V29KBx\nrWp7dEFmUBmTkMmp3p2ZSrsBBNJ1iGcl4R7uHy8BU3+6ybsFU1onDATwAocm\nQIXJXHZnNEbUQd3jhVX2lY7heK9FZLIT8x6CMVyWjxUxf0VVQockv0djKQer\niEBQALUnxNF+e/pGOoqq9i6mN4EiguvwGeXB55h5TtLnsbKC1cOZFUjOkDxv\n5Cgl\r\n=74u2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAjdBZHvdclVL55BvP/5J9svPPxl6D0cFws5EK+DMQMxAiB1I9tg6vUBFZ4W9JyUERHqo8dzJuqArCb0WML0pgvyPQ=="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_5.2.1_1588942597052_0.8168752094398322"}, "_hasShrinkwrap": false}, "5.3.0-beta.0": {"name": "ts-mixer", "version": "5.3.0-beta.0", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "build-clean": "rimraf dist && yarn build", "lint": "eslint src/**/*.ts", "test": "nyc mocha", "coveralls": "cat coverage/lcov.info | coveralls", "release": "standard-version"}, "devDependencies": {"@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@types/chai": "^4.2.11", "@types/mocha": "^7.0.2", "@types/node": "^13.13.5", "@types/sinon": "^9.0.0", "@typescript-eslint/parser": "^2.31.0", "chai": "^4.2.0", "class-validator": "^0.12.2", "coveralls": "^3.1.0", "eslint": "^6.8.0", "husky": "^4.2.5", "mocha": "^7.1.2", "nyc": "14.1.1", "rimraf": "^3.0.2", "sinon": "^9.0.2", "standard-version": "^8.0.0", "ts-node": "^8.10.1", "typescript": "^3.8.3"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixin", "mixins", "multiple inheritance", "mixin classes"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "78a938cf2e418101dd2472ae4edf1fbec3049dbd", "readme": "# ts-mixer\n[![npm version](https://badgen.net/npm/v/ts-mixer)](https://npmjs.com/package/ts-mixer)\n[![Build Status](https://travis-ci.org/tannerntannern/ts-mixer.svg?branch=master)](https://travis-ci.org/tannerntannern/ts-mixer)\n[![Coverage Status](https://coveralls.io/repos/github/tannerntannern/ts-mixer/badge.svg?branch=master)](https://coveralls.io/github/tannerntannern/ts-mixer?branch=master)\n[![Minified Size](https://badgen.net/bundlephobia/min/ts-mixer)](https://bundlephobia.com/result?p=ts-mixer)\n[![Conventional Commits](https://badgen.net/badge/conventional%20commits/1.0.0/yellow)](https://conventionalcommits.org)\n\n## Overview\n`ts-mixer` brings mixins to TypeScript.  \"Mixins\" to `ts-mixer` are just classes, so you already know how to write them, and you can probably mix classes from your favorite library without trouble.\n\nThe mixin problem is more nuanced than it appears.  I've seen countless code snippets that work for certain situations, but fail in others.  `ts-mixer` tries to take the best from all these solutions while accounting for the situations you might not have considered.\n\n[Quick start guide](#quick-start)\n\n### Features\n* mixes plain classes\n* mixes classes that extend other classes\n* mixes classes that were mixed with `ts-mixer`\n* supports static properties\n* supports protected/private properties (the popular function-that-returns-a-class solution does not)\n* mixes abstract classes (with caveats [[1](#caveats)])\n* mixes generic classes (with caveats [[2](#caveats)])\n* supports class, method, and property decorators (with caveats [[3, 6](#caveats)])\n* mostly supports the complexity presented by constructor functions (with caveats [[4](#caveats)])\n* comes with an `instanceof`-like replacement (with caveats [[5, 6](#caveats)])\n* [multiple mixing strategies](#settings) (ES6 proxies vs hard copy)\n\n### Caveats\n1. Mixing abstract classes requires a bit of a hack that may break in future versions of TypeScript.  See [mixing abstract classes](#mixing-abstract-classes) below.\n2. Mixing generic classes requires a more cumbersome notation, but it's still possible.  See [mixing generic classes](#mixing-generic-classes) below.\n3. Using decorators in mixed classes also requires a more cumbersome notation.  See [mixing with decorators](#mixing-with-decorators) below.\n4. ES6 made it impossible to use `.apply(...)` on class constructors (or any means of calling them without `new`), which makes it impossible for `ts-mixer` to pass the proper `this` to your constructors.  This may or may not be an issue for your code, but there are options to work around it.  See [dealing with constructors](#dealing-with-constructors) below.\n5. `ts-mixer` does not support `instanceof` for mixins, but it does offer a replacement.  See the [hasMixin function](#hasmixin) for more details.\n6. Certain features (specifically, `@dectorator` and `hasMixin`) make use of ES6 `Map`s, which means you must either use ES6+ or polyfill `Map` to use them.  If you don't need these features, you should be fine without.\n\n## Quick Start\n### Installation\n```\n$ npm install ts-mixer\n```\n\nor if you prefer [Yarn](https://yarnpkg.com):\n\n```\n$ yarn add ts-mixer\n```\n\n### Basic Example\n```typescript\nimport { Mixin } from 'ts-mixer';\n\nclass Foo {\n    protected makeFoo() {\n        return 'foo';\n    }\n}\n\nclass Bar {\n    protected makeBar() {\n        return 'bar';\n    }\n}\n\nclass FooBar extends Mixin(Foo, Bar) {\n    public makeFooBar() {\n        return this.makeFoo() + this.makeBar();\n    }\n}\n\nconst fooBar = new FooBar();\n\nconsole.log(fooBar.makeFooBar());  // \"foobar\"\n```\n\n## Special Cases\n### Mixing Abstract Classes\nAbstract classes, by definition, cannot be constructed, which means they cannot take on the type, `new(...args) => any`, and by extension, are incompatible with `ts-mixer`.  BUT, you can \"trick\" TypeScript into giving you all the benefits of an abstract class without making it technically abstract.  The trick is just some strategic `// @ts-ignore`'s:\n\n```typescript\nimport { Mixin } from 'ts-mixer';\n\n// note that Foo is not marked as an abstract class\nclass Foo {\n    // @ts-ignore: \"Abstract methods can only appear within an abstract class\"\n    public abstract makeFoo(): string;\n}\n\nclass Bar {\n    public makeBar() {\n        return 'bar';\n    }\n}\n\nclass FooBar extends Mixin(Foo, Bar) {\n    // we still get all the benefits of abstract classes here, because TypeScript\n    // will still complain if this method isn't implemented\n    public makeFoo() {\n        return 'foo';\n    }\n}\n```\n\nDo note that while this does work quite well, it is a bit of a hack and I can't promise that it will continue to work in future TypeScript versions.\n\n### Mixing Generic Classes\nFrustratingly, it is _impossible_ for generic parameters to be referenced in base class expressions.  No matter what, you will eventually run into `Base class expressions cannot reference class type parameters.`\n\nThe way to get around this is to leverage [declaration merging](https://www.typescriptlang.org/docs/handbook/declaration-merging.html), and a slightly different mixing function from ts-mixer: `mix`.  It works exactly like `Mixin`, except it's a decorator, which means it doesn't affect the type information of the class being decorated.  See it in action below:\n\n```typescript\nimport { mix } from 'ts-mixer';\n\nclass Foo<T> {\n    public fooMethod(input: T): T {\n        return input;\n    }\n}\n\nclass Bar<T> {\n    public barMethod(input: T): T {\n        return input;\n    }\n}\n\ninterface FooBar<T1, T2> extends Foo<T1>, Bar<T2> { }\n@mix(Foo, Bar)\nclass FooBar<T1, T2> {\n    public fooBarMethod(input1: T1, input2: T2) {\n        return [this.fooMethod(input1), this.barMethod(input2)];\n    }\n}\n```\n\nKey takeaways from this example:\n* `interface FooBar<T1, T2> extends Foo<T1>, Bar<T2> { }` makes sure `FooBar` has the typing we want, thanks to declaration merging\n* `@mix(Foo, Bar)` wires things up \"on the JavaScript side\", since the interface declaration has nothing to do with runtime behavior.\n* The reason we have to use the `mix` decorator is that the typing produced by `Mixin(Foo, Bar)` would conflict with the typing of the interface.  `mix` has no effect \"on the TypeScript side,\" thus avoiding type conflicts.\n\n### Mixing with Decorators\nPopular libraries such as [class-validator](https://github.com/typestack/class-validator) and [TypeORM](https://github.com/typeorm/typeorm) use decorators to add functionality.  Unfortunately, `ts-mixer` has no way of knowing what these libraries do with the decorators behind the scenes.  So if you want these decorators to be \"inherited\" with classes you plan to mix, you first have to wrap them with a special `decorate` function exported by `ts-mixer`.  Here's an example using `class-validator`:\n\n```typescript\nimport { IsBoolean, IsIn, validate } from 'class-validator';\nimport { Mixin, decorate } from 'ts-mixer';\n\nclass Disposable {\n    @decorate(IsBoolean())  // instead of @IsBoolean()\n    isDisposed: boolean = false;\n}\n\nclass Statusable {\n    @decorate(IsIn(['red', 'green']))  // instead of @IsIn(['red', 'green'])\n    status: string = 'green';\n}\n\nclass ExtendedObject extends Mixin(Disposable, Statusable) {}\n\nconst extendedObject = new ExtendedObject();\nextendedObject.status = 'blue';\n\nvalidate(extendedObject).then(errors => {\n    console.log(errors);\n});\n```\n\n### Dealing with Constructors\nAs mentioned in the [caveats section](#caveats), ES6 disallowed calling constructor functions without `new`.  This means that the only way for `ts-mixer` to mix instance properties is to instantiate each base class separately, then copy the instance properties into a common object.  The consequence of this is that constructors mixed by `ts-mixer` will _not_ receive the proper `this`.\n\n**This very well may not be an issue for you!**  It only means that your constructors need to be \"mostly pure\" in terms of how they handle `this`.  Specifically, your constructors cannot produce [side effects](https://en.wikipedia.org/wiki/Side_effect_%28computer_science%29) involving `this`, _other than adding properties to `this`_ (the most common side effect in JavaScript constructors).\n\nIf you simply cannot eliminate `this` side effects from your constructor, there is a workaround available:  `ts-mixer` will automatically forward constructor parameters to a predesignated init function (`settings.initFunction`) if it's present on the class.  Unlike constructors, functions can be called with an arbitrary `this`, so this predesignated init function _will_ have the proper `this`.  Here's a basic example:\n\n```typescript\nimport { Mixin, settings } from 'ts-mixer';\n\nsettings.initFunction = 'init';\n\nclass Person {\n    public static allPeople: Set<Person> = new Set();\n    \n    protected init() {\n        Person.allPeople.add(this);\n    }\n}\n\ntype PartyAffiliation = 'democrat' | 'republican';\n\nclass PoliticalParticipant {\n    public static democrats: Set<PoliticalParticipant> = new Set();\n    public static republicans: Set<PoliticalParticipant> = new Set();\n    \n    public party: PartyAffiliation;\n    \n    // note that these same args will also be passed to init function\n    public constructor(party: PartyAffiliation) {\n        this.party = party;\n    }\n    \n    protected init(party: PartyAffiliation) {\n        if (party === 'democrat')\n            PoliticalParticipant.democrats.add(this);\n        else\n            PoliticalParticipant.republicans.add(this);\n    }\n}\n\nclass Voter extends Mixin(Person, PoliticalParticipant) {}\n\nconst v1 = new Voter('democrat');\nconst v2 = new Voter('democrat');\nconst v3 = new Voter('republican');\nconst v4 = new Voter('republican');\n```\n\nNote the above `.add(this)` statements.  These would not work as expected if they were placed in the constructor instead, since `this` is not the same between the constructor and `init`, as explained above.\n\n## Other Features\n### hasMixin\nAs mentioned above, `ts-mixer` does not support `instanceof` for mixins.  While it is possible to implement [custom `instanceof` behavior](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Symbol/hasInstance), this library does not do so because it would require modifying the source classes, which is deliberately avoided.\n\nYou can fill this missing functionality with `hasMixin(instance, mixinClass)` instead.  See the below example:\n\n```typescript\nimport { Mixin, hasMixin } from 'ts-mixer';\n\nclass Foo {}\nclass Bar {}\nclass FooBar extends Mixin(Foo, Bar) {}\n\nconst instance = new FooBar();\n\n// doesn't work with instanceof...\nconsole.log(instance instanceof FooBar)  // true\nconsole.log(instance instanceof Foo)     // false\nconsole.log(instance instanceof Bar)     // false\n\n// but everything works nicely with hasMixin!\nconsole.log(hasMixin(instance, FooBar))  // true\nconsole.log(hasMixin(instance, Foo))     // true\nconsole.log(hasMixin(instance, Bar))     // true\n```\n\n`hasMixin(instance, mixinClass)` will work anywhere that `instance instanceof mixinClass` works.  Additionally, like `instanceof`, you get the same [type narrowing benefits](https://www.typescriptlang.org/docs/handbook/advanced-types.html#instanceof-type-guards):\n\n```typescript\nif (hasMixin(instance, Foo)) {\n    // inferred type of instance is \"Foo\"\n}\n\nif (hasMixin(instance, Bar)) {\n    // inferred type of instance of \"Bar\"\n}\n```\n\n## Settings\nts-mixer has multiple strategies for mixing classes which can be configured by modifying `settings` from ts-mixer.  For example:\n\n```typescript\nimport { settings, Mixin } from 'ts-mixer';\n\nsettings.prototypeStrategy = 'proxy';\n\n// then use `Mixin` as normal...\n```\n\n### `settings.prototypeStrategy`\n* Determines how ts-mixer will mix class prototypes together\n* Possible values:\n    - `'copy'` (default) - Copies all methods from the classes being mixed into a new prototype object.  (This will include all methods up the prototype chains as well.)  This is the default for ES5 compatibility, but it has the downside of stale references.  For example, if you mix `Foo` and `Bar` to make `FooBar`, then redefine a method on `Foo`, `FooBar` will not have the latest methods from `Foo`.  If this is not a concern for you, `'copy'` is the best value for this setting.\n    - `'proxy'` - Uses an ES6 Proxy to \"soft mix\" prototypes.  Unlike `'copy'`, updates to the base classes _will_ be reflected in the mixed class, which may be desirable.  The downside is that method access is not as performant, nor is it ES5 compatible.\n\n### `settings.staticsStrategy`\n* Determines how static properties are inherited\n* Possible values:\n    - `'copy'` (default) - Simply copies all properties (minus `prototype`) from the base classes/constructor functions onto the mixed class.  Like `settings.prototypeStrategy = 'copy'`, this strategy also suffers from stale references, but shouldn't be a concern if you don't redefine static methods after mixing.\n    - `'proxy'` - Similar to `settings.prototypeStrategy`, proxy's static method access to base classes.  Has the same benefits/downsides.\n\n### `settings.initFunction`\n* If set, `ts-mixer` will automatically call the function with this name upon construction\n* Possible values:\n    - `null` (default) - disables the behavior\n    - a string - function name to call upon construction\n* Read more about why you would want this in [dealing with constructors](#dealing-with-constructors)\n\n# Author\nTanner Nielsen <<EMAIL>>\n* Website - [tannernielsen.com](http://tannernielsen.com)\n* Github - [tannerntannern](https://github.com/tannerntannern)\n", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@5.3.0-beta.0", "_nodeVersion": "12.17.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-08zGhG8/MLfOea6L02QcO1z50SEARhx+YTSYcfRmYiY5F3sINNUKMGiOLP2X1pdfLtbLuUgvpG3E6UT5hKgOkg==", "shasum": "73a2ee1405f9d984eb155b70999970eb2176483b", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-5.3.0-beta.0.tgz", "fileCount": 20, "unpackedSize": 43209, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe0/izCRA9TVsSAnZWagAAhyMP/ioK0Z2v9aeemcPpfRQo\nA6txGyBZP8IrlrNA6cz6Ni/V0uAaFuZjniTjwicwO2CKyTfylk+DNFW/R+nR\nwFPGg0aFfXVsCUAp8zqURgzK+U1CkPsm6GF6Hs6t9iyLCS9bxlc2zn+lygfA\np59rQVFSTEvcuZsgEueTHuxy9y5XXc+/QG6WIBxg9o0wrA3wBNoPM4GNcBtm\nUGMw6RzlOxLvOaS3jt0QlOJnC1DaBhGD7l5xBsrcA2xaQsHu4rPIRI4h4qVz\nJ1QiUWl878UGLYlrJ7ezvVy20Zc/UqPv3ruo89dTg7e8mQQZ2wfavc6ZGaYy\n4x2x0rxFElWKVP2I6Qa3Xhe8zZNcixGuXZ/64XzUqi0KQGkyVVt6WwNquImN\nwkJbcm7JNhwZdGgIRsRaZx5ydwDU6JbgxHpQq/FJyYeggJfFJGcrFnpC99so\n0Xk7jyyu1goawZcmQmp6MHIxpHc1c1XqT52g/+Oj2UKuOBPOt3HzAPvE/7Q/\nhyG1v7E5SYaV60L5hYVb9npaHieqI6/cxy3xiG6aB0iB9QAmHnNlKPE7wyxf\nc6BgbBDbGQ4lPoSsp2xKE34kEi6OiTVmzGKcI9y8iSfwhDzuF/p6zRXM2wCO\nFHO4MuObqsAvNoPvBohxE235dF3WHODS3OqWa27KGWHnpqypX+u5MhwGaCls\niTFr\r\n=EqzP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/jPtyWFw56GKwcyxz6Qp29eq+7sI4zCBjPuANHj577QIgFTxUgLlqONvAUDBZvSUOxaYVb/h0BZDqqz4YZxZEmcY="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_5.3.0-beta.0_1590950066644_0.6231276917692556"}, "_hasShrinkwrap": false}, "5.3.0": {"name": "ts-mixer", "version": "5.3.0", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "build-clean": "rimraf dist && yarn build", "lint": "eslint src/**/*.ts", "test": "nyc mocha", "coveralls": "cat coverage/lcov.info | coveralls", "release": "standard-version"}, "devDependencies": {"@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@types/chai": "^4.2.11", "@types/mocha": "^7.0.2", "@types/node": "^14.0.6", "@types/sinon": "^9.0.4", "@typescript-eslint/parser": "^3.0.2", "chai": "^4.2.0", "class-validator": "^0.12.2", "coveralls": "^3.1.0", "eslint": "^7.1.0", "husky": "^4.2.5", "mocha": "^7.2.0", "nyc": "14.1.1", "rimraf": "^3.0.2", "sinon": "^9.0.2", "standard-version": "^8.0.0", "ts-node": "^8.10.2", "typescript": "^3.9.3"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixin", "mixins", "multiple inheritance", "mixin classes"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "007b2df3291943a61fe4361cbc78b73ea950ec76", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@5.3.0", "_nodeVersion": "12.17.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-K627CEtJduskqdClMZQ21fKDWy7EZN+1FkhFa1aTpsqdxKtqJXJ1kC2vge84c/L5I3Ed/F7Jsz5wGnhgKt2rDA==", "shasum": "aaf73a7bd75340cdbabdc255f0eb3757ee2e5acf", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-5.3.0.tgz", "fileCount": 20, "unpackedSize": 44015, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1F0jCRA9TVsSAnZWagAA8m0P/iUNhtSfSZQqNOqYdkG8\n+XeQbsxj5ZabRau1hjK6a9njhjcQpxUPFzKaIyEnKn3U/H4vwRMHPYHMTfT4\n8YvTFlhFuc/kMzjH2KWWolXsghwwSSrpoRuIYjaOk3GZS861qF3C1OgLdcrk\n4c51GutKwcH9YFxmmDkR3nPUMqRgfy/Eu7zhi01ZdKopNkjDSSPXPXFJYZcr\ng2DerUfBjLqN2LkgZpTwNp7sQSaQZ1py4atcTFgfe9BBfMwFhNFMYd5OGmdi\ncsN/e6niORvfTSu+xs5t+3Qpr2p4t1vXF001OFffMhvmvN1CDw4NcO+7QN1X\n0tl6ElkpJr1HvO+I3KJGmzw62UTFBA/K8CczuXFtqk0uIJyUp7aTFiWq8Qyi\nUM5DBB2eaZuBFqROqu+JSZ0oE0Fs0x/JR+Dy7nGRRrxN5nA/7RDuDrCX4eCY\nwOYIjHCPTR00wrlQw2TQ/i0E2GDuGpXsUKYqLCfyyaOoNSVUPZZGfstpvXbG\nSoEgMiXpCguEb1loyxLG3lz7sIHOFqtrckCL+HKHZiEveY1SxyVASwBtBNVr\nSqX/SfqORNpwj2AmxU0UmI96HdYSi0MDJs4f5UDFPuXutNKMr244GdbNwo3W\nu/dHCXDUJ4VKX+eie6ZPcJPN+3cXbPQumBttWnzChEopGTTYt63pClnZ6ufL\nmuOC\r\n=9akf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDI6s9F7QFaPJ+DxQj1+psmFvg5nBL34fJCrC3c+kuA9QIgbhx/pzeY9anKQHxaYlvhQHrklTS1n8RM/ZA/Ya1B0WA="}]}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_5.3.0_1590975778881_0.26907520981788724"}, "_hasShrinkwrap": false}, "5.4.0": {"name": "ts-mixer", "version": "5.4.0", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "build-clean": "rimraf dist && yarn build", "lint": "eslint src/**/*.ts", "test": "nyc mocha", "codegen": "node ./codegen.js", "release": "standard-version"}, "devDependencies": {"@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@types/chai": "^4.2.11", "@types/mocha": "^7.0.2", "@types/node": "^14.0.6", "@types/sinon": "^9.0.4", "@typescript-eslint/parser": "^3.0.2", "chai": "^4.2.0", "class-validator": "^0.12.2", "coveralls": "^3.1.0", "eslint": "^7.1.0", "husky": "^4.2.5", "js-yaml": "^3.14.0", "mocha": "^7.2.0", "nyc": "14.1.1", "rimraf": "^3.0.2", "sinon": "^9.0.2", "standard-version": "^8.0.0", "ts-node": "^8.10.2", "typescript": "^3.9.3", "yarn-add-no-save": "^1.0.3"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixin", "mixins", "multiple inheritance", "mixin classes"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "fccd7f9f86a576d395ba9eed3662efb3f709e696", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@5.4.0", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-Z5u/hpTkP4VWTIQT5vzxyeFAe8siPkatOC0Q49Uf56ccMGSHosI/IEo+t8BFvJX9yiF111YEOKfKY3m72V7F/w==", "shasum": "f98583e6acd72bf4c7a04481352ebfd203171297", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-5.4.0.tgz", "fileCount": 20, "unpackedSize": 49795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftMi4CRA9TVsSAnZWagAACWkP/i9kBywLs/ZFxzOoQN2m\nYk3KuM8jcPjDnsm/zXc+tMAeMdVXFixBpknwBQFy1F4JCnIANSN518jjGgxk\noigl4em5ySDYo8dsSMp0c4etvOVqo8kzvqFfPXw/cTIGAwQ2K1JSCOSvakMc\nUDlKOnBLGutDCtitd4dhOFhM21P4R5J+joFzAzjsgdJdpn1ZyyIM2PZnyV9v\nQtfLFl2wSehlko8wqPmQCuzQU+RtatJToWbRR2EQpxsc6Tb84XrzA4vNv4WZ\nHJamqsvF/LavNlczu5e7GI0j178B1N/6cmDcIFY4rEfadM6/2RKDBH5iCHZL\nTrBRLqlSO2ST++Ayl2HrqdwGl0gQKUL8HrKBfyQHjYU3RoJLKWzl1/qQPifJ\nldA5bCSs1BBVEyyzuCTgMeIjohy2gbtjeC6rgz5+WKbKuhzjU5LfUjv4aveU\n+IrRCqY2zW+p0wE9dqFCEqzhIZtVPuJ6Mro3dav7ijtGsyei61QIQwV5PCRp\nTSQPMf32JdkCuCVcff8JmJr94OtPRz2F6zQomU2J42IFqFWt3N+3USrP/tOX\nMj+gz8oiVUII5pWMykEDrDA19VXTQp7/TZUQn01b2hsCzXBoJIDZ3aD98Tdf\nW55IL6Q6m5EXL388CeU0Qn5BcsOw7qYckQ55tQcfqkaJWJmMCFDdCdJPN765\nZdYX\r\n=HTkW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH+tD7p9u5o9siyKBSL0tqVGVVU8K4CPNCLM7Xp/A6d2AiEA2pRBOzyXN34J+v7MBNVFnfPvJVlF3Jzl1Ihs6/hrPzc="}]}, "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_5.4.0_1605683384123_0.47270084889590436"}, "_hasShrinkwrap": false}, "5.4.1": {"name": "ts-mixer", "version": "5.4.1", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "browser": "dist/esm/index.js", "unpkg": "dist/esm/index.min.js", "types": "dist/types/index.d.ts", "scripts": {"prebuild": "yarn clean", "build": "rollup -c && tsc", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src/**/*.ts", "test": "nyc mocha", "codegen": "node ./codegen.js", "release": "standard-version"}, "devDependencies": {"@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@rollup/plugin-typescript": "^8.2.1", "@types/chai": "^4.2.15", "@types/mocha": "^8.2.0", "@types/node": "^14.14.28", "@types/sinon": "^9.0.10", "@typescript-eslint/parser": "^4.15.1", "chai": "^4.3.0", "class-validator": "^0.13.1", "coveralls": "^3.1.0", "eslint": "^7.20.0", "husky": "^4.2.5", "js-yaml": "^4.0.0", "mocha": "^8.3.0", "nyc": "14.1.1", "rimraf": "^3.0.2", "rollup": "^2.45.2", "rollup-plugin-terser": "^7.0.2", "sinon": "^9.2.4", "standard-version": "^9.1.0", "ts-node": "^9.1.1", "tslib": "^2.2.0", "typescript": "^4.2.4", "yarn-add-no-save": "^1.0.3"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixin", "mixins", "multiple inheritance", "mixin classes"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "83aca820f9017163227cf191abcad2c4006ceb81", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@5.4.1", "_nodeVersion": "14.16.1", "_npmVersion": "6.14.12", "dist": {"integrity": "sha512-Zo9HgPCtNouDgJ+LGtrzVOjSg8+7WGQktIKLwAfaNrlOK1mWGlz1ejsAF/YqUEqAGjUTeB5fEg8gH9Aui6w9xA==", "shasum": "b90db9ced48531aa17ce9184a2890d1e3c99b1e5", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-5.4.1.tgz", "fileCount": 22, "unpackedSize": 73950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgi0k1CRA9TVsSAnZWagAATe4P/1v/XguZnBe7NJaO4y+Z\nwMPLXwnprtSuL1R6uPH7gVcn5QTKQ+pJsmerZUzsLt1RUgslKb46ApNg2Ajx\nHNxrOnDrMOEfQ+jYdJJfYfEsPVXP+sxR2g74QaUSzGSHZe0Fp2I4ePows1bQ\ndNV/5S6E3h7T67SgGVlXlbjFaCvUtuNTxYkfeVKvgcRUgWzQgXMgT1Xz+JGZ\n59qRsrHBWR/9laVENGvJk58vsaJQyhdDRfO7kr57TPyOWhWijbPbMdshB2Tz\nqzz4036abZI1swmSRI9PeT2901/Heh9Ud1wQ0tswmNeK8HV/WGUYp1jxzFM/\njIpqt4M9IuRlTbCAK/vyYEcut3HGuS7hPux9I/+dbtFw9Go5IMUk9TlGApRH\no3Hooh22UCpYTR2vS7J1YGjRc6UvMHvAAKrUYJHD07Nf+8zVUO28NM4tvAw2\nDhf2Zxyd/Dv9cjLh5M6gN+bqIe6wJwJkK+4LCeN/8byeHez7jqOGSYMIOT1U\nZpU/jM33cXPdkc9gpTOTqKKb2I32of+kDefcZOrkLOCnjFwHJG5TqYqF0e5q\nLlYmGM0r0x05hYrPirreeT9fqKsjHofcFQhWMc6uURthqnz4HL/StINNSTov\n8Nab12q3K4SbBHYkRMozLhPGiH2/xVgg5ZtTkIeBfxnJEJId3p4q2Nnypk2Y\nL6te\r\n=Q0vt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIByy+evcE1Qxw0z+79+QUMuOCPk+3W6ofm3tiW56YDV/AiEAz293f3TqVempwas9VDkCHL8GbhnphZw7zSTcxQGZaV8="}]}, "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_5.4.1_1619740980636_0.07551411186624102"}, "_hasShrinkwrap": false}, "6.0.0-beta.0": {"name": "ts-mixer", "version": "6.0.0-beta.0", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "browser": "dist/esm/index.js", "unpkg": "dist/esm/index.min.js", "types": "dist/types/index.d.ts", "scripts": {"prebuild": "yarn clean", "build": "rollup -c && tsc", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src/**/*.ts", "test": "nyc mocha", "codegen": "node ./codegen.js", "release": "standard-version"}, "devDependencies": {"@commitlint/cli": "^12.1.4", "@commitlint/config-conventional": "^12.1.4", "@rollup/plugin-typescript": "^8.2.1", "@types/chai": "^4.2.19", "@types/mocha": "^8.2.2", "@types/node": "^15.12.4", "@types/sinon": "^10.0.2", "@typescript-eslint/parser": "^4.27.0", "chai": "^4.3.4", "class-validator": "^0.13.1", "coveralls": "^3.1.0", "eslint": "^7.29.0", "husky": "^4.2.5", "js-yaml": "^4.1.0", "mocha": "^9.0.1", "nyc": "14.1.1", "rimraf": "^3.0.2", "rollup": "^2.52.1", "rollup-plugin-terser": "^7.0.2", "sinon": "^11.1.1", "standard-version": "^9.3.0", "ts-node": "^10.0.0", "tslib": "^2.3.0", "typescript": "^4.3.4", "yarn-add-no-save": "^1.0.3"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixin", "mixins", "multiple inheritance", "mixin classes"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "8884811a2e16294bb21b87d84617cd4da129359f", "readme": "# ts-mixer\n[version-badge]: https://badgen.net/npm/v/ts-mixer\n[version-link]: https://npmjs.com/package/ts-mixer\n[build-badge]: https://img.shields.io/github/workflow/status/tannerntannern/ts-mixer/ts-mixer%20CI\n[build-link]: https://github.com/tannerntannern/ts-mixer/actions\n[ts-versions]: https://badgen.net/badge/icon/4.2,4.3?icon=typescript&label&list=|\n[node-versions]: https://badgen.net/badge/node/10%2C12%2C14/blue/?list=|\n[![npm version][version-badge]][version-link]\n[![github actions][build-badge]][build-link]\n[![TS Versions][ts-versions]][build-link]\n[![Node.js Versions][node-versions]][build-link]\n[![Minified Size](https://badgen.net/bundlephobia/min/ts-mixer)](https://bundlephobia.com/result?p=ts-mixer)\n[![Conventional Commits](https://badgen.net/badge/conventional%20commits/1.0.0/yellow)](https://conventionalcommits.org)\n\n## Overview\n`ts-mixer` brings mixins to TypeScript.  \"Mixins\" to `ts-mixer` are just classes, so you already know how to write them, and you can probably mix classes from your favorite library without trouble.\n\nThe mixin problem is more nuanced than it appears.  I've seen countless code snippets that work for certain situations, but fail in others.  `ts-mixer` tries to take the best from all these solutions while accounting for the situations you might not have considered.\n\n[Quick start guide](#quick-start)\n\n### Features\n* mixes plain classes\n* mixes classes that extend other classes\n* mixes classes that were mixed with `ts-mixer`\n* supports static properties\n* supports protected/private properties (the popular function-that-returns-a-class solution does not)\n* mixes abstract classes (requires TypeScript >= 4.2)\n* mixes generic classes (with caveats [[1](#caveats)])\n* supports class, method, and property decorators (with caveats [[2, 5](#caveats)])\n* mostly supports the complexity presented by constructor functions (with caveats [[3](#caveats)])\n* comes with an `instanceof`-like replacement (with caveats [[4, 5](#caveats)])\n* [multiple mixing strategies](#settings) (ES6 proxies vs hard copy)\n\n### Caveats\n1. Mixing generic classes requires a more cumbersome notation, but it's still possible.  See [mixing generic classes](#mixing-generic-classes) below.\n2. Using decorators in mixed classes also requires a more cumbersome notation.  See [mixing with decorators](#mixing-with-decorators) below.\n3. ES6 made it impossible to use `.apply(...)` on class constructors (or any means of calling them without `new`), which makes it impossible for `ts-mixer` to pass the proper `this` to your constructors.  This may or may not be an issue for your code, but there are options to work around it.  See [dealing with constructors](#dealing-with-constructors) below.\n4. `ts-mixer` does not support `instanceof` for mixins, but it does offer a replacement.  See the [hasMixin function](#hasmixin) for more details.\n5. Certain features (specifically, `@decorator` and `hasMixin`) make use of ES6 `Map`s, which means you must either use ES6+ or polyfill `Map` to use them.  If you don't need these features, you should be fine without.\n\n## Quick Start\n### Installation\n```\n$ npm install ts-mixer\n```\n\nor if you prefer [Yarn](https://yarnpkg.com):\n\n```\n$ yarn add ts-mixer\n```\n\n### Basic Example\n```typescript\nimport { Mixin } from 'ts-mixer';\n\nclass Foo {\n    protected makeFoo() {\n        return 'foo';\n    }\n}\n\nclass Bar {\n    protected makeBar() {\n        return 'bar';\n    }\n}\n\nclass FooBar extends Mixin(Foo, Bar) {\n    public makeFooBar() {\n        return this.makeFoo() + this.makeBar();\n    }\n}\n\nconst fooBar = new FooBar();\n\nconsole.log(fooBar.makeFooBar());  // \"foobar\"\n```\n\n## Special Cases\n### Mixing Generic Classes\nFrustratingly, it is _impossible_ for generic parameters to be referenced in base class expressions.  No matter what, you will eventually run into `Base class expressions cannot reference class type parameters.`\n\nThe way to get around this is to leverage [declaration merging](https://www.typescriptlang.org/docs/handbook/declaration-merging.html), and a slightly different mixing function from ts-mixer: `mix`.  It works exactly like `Mixin`, except it's a decorator, which means it doesn't affect the type information of the class being decorated.  See it in action below:\n\n```typescript\nimport { mix } from 'ts-mixer';\n\nclass Foo<T> {\n    public fooMethod(input: T): T {\n        return input;\n    }\n}\n\nclass Bar<T> {\n    public barMethod(input: T): T {\n        return input;\n    }\n}\n\ninterface FooBar<T1, T2> extends Foo<T1>, Bar<T2> { }\n@mix(Foo, Bar)\nclass FooBar<T1, T2> {\n    public fooBarMethod(input1: T1, input2: T2) {\n        return [this.fooMethod(input1), this.barMethod(input2)];\n    }\n}\n```\n\nKey takeaways from this example:\n* `interface FooBar<T1, T2> extends Foo<T1>, Bar<T2> { }` makes sure `FooBar` has the typing we want, thanks to declaration merging\n* `@mix(Foo, Bar)` wires things up \"on the JavaScript side\", since the interface declaration has nothing to do with runtime behavior.\n* The reason we have to use the `mix` decorator is that the typing produced by `Mixin(Foo, Bar)` would conflict with the typing of the interface.  `mix` has no effect \"on the TypeScript side,\" thus avoiding type conflicts.\n\n### Mixing with Decorators\nPopular libraries such as [class-validator](https://github.com/typestack/class-validator) and [TypeORM](https://github.com/typeorm/typeorm) use decorators to add functionality.  Unfortunately, `ts-mixer` has no way of knowing what these libraries do with the decorators behind the scenes.  So if you want these decorators to be \"inherited\" with classes you plan to mix, you first have to wrap them with a special `decorate` function exported by `ts-mixer`.  Here's an example using `class-validator`:\n\n```typescript\nimport { IsBoolean, IsIn, validate } from 'class-validator';\nimport { Mixin, decorate } from 'ts-mixer';\n\nclass Disposable {\n    @decorate(IsBoolean())  // instead of @IsBoolean()\n    isDisposed: boolean = false;\n}\n\nclass Statusable {\n    @decorate(IsIn(['red', 'green']))  // instead of @IsIn(['red', 'green'])\n    status: string = 'green';\n}\n\nclass ExtendedObject extends Mixin(Disposable, Statusable) {}\n\nconst extendedObject = new ExtendedObject();\nextendedObject.status = 'blue';\n\nvalidate(extendedObject).then(errors => {\n    console.log(errors);\n});\n```\n\n### Dealing with Constructors\nAs mentioned in the [caveats section](#caveats), ES6 disallowed calling constructor functions without `new`.  This means that the only way for `ts-mixer` to mix instance properties is to instantiate each base class separately, then copy the instance properties into a common object.  The consequence of this is that constructors mixed by `ts-mixer` will _not_ receive the proper `this`.\n\n**This very well may not be an issue for you!**  It only means that your constructors need to be \"mostly pure\" in terms of how they handle `this`.  Specifically, your constructors cannot produce [side effects](https://en.wikipedia.org/wiki/Side_effect_%28computer_science%29) involving `this`, _other than adding properties to `this`_ (the most common side effect in JavaScript constructors).\n\nIf you simply cannot eliminate `this` side effects from your constructor, there is a workaround available:  `ts-mixer` will automatically forward constructor parameters to a predesignated init function (`settings.initFunction`) if it's present on the class.  Unlike constructors, functions can be called with an arbitrary `this`, so this predesignated init function _will_ have the proper `this`.  Here's a basic example:\n\n```typescript\nimport { Mixin, settings } from 'ts-mixer';\n\nsettings.initFunction = 'init';\n\nclass Person {\n    public static allPeople: Set<Person> = new Set();\n    \n    protected init() {\n        Person.allPeople.add(this);\n    }\n}\n\ntype PartyAffiliation = 'democrat' | 'republican';\n\nclass PoliticalParticipant {\n    public static democrats: Set<PoliticalParticipant> = new Set();\n    public static republicans: Set<PoliticalParticipant> = new Set();\n    \n    public party: PartyAffiliation;\n    \n    // note that these same args will also be passed to init function\n    public constructor(party: PartyAffiliation) {\n        this.party = party;\n    }\n    \n    protected init(party: PartyAffiliation) {\n        if (party === 'democrat')\n            PoliticalParticipant.democrats.add(this);\n        else\n            PoliticalParticipant.republicans.add(this);\n    }\n}\n\nclass Voter extends Mixin(Person, PoliticalParticipant) {}\n\nconst v1 = new Voter('democrat');\nconst v2 = new Voter('democrat');\nconst v3 = new Voter('republican');\nconst v4 = new Voter('republican');\n```\n\nNote the above `.add(this)` statements.  These would not work as expected if they were placed in the constructor instead, since `this` is not the same between the constructor and `init`, as explained above.\n\n## Other Features\n### hasMixin\nAs mentioned above, `ts-mixer` does not support `instanceof` for mixins.  While it is possible to implement [custom `instanceof` behavior](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Symbol/hasInstance), this library does not do so because it would require modifying the source classes, which is deliberately avoided.\n\nYou can fill this missing functionality with `hasMixin(instance, mixinClass)` instead.  See the below example:\n\n```typescript\nimport { Mixin, hasMixin } from 'ts-mixer';\n\nclass Foo {}\nclass Bar {}\nclass FooBar extends Mixin(Foo, Bar) {}\n\nconst instance = new FooBar();\n\n// doesn't work with instanceof...\nconsole.log(instance instanceof FooBar)  // true\nconsole.log(instance instanceof Foo)     // false\nconsole.log(instance instanceof Bar)     // false\n\n// but everything works nicely with hasMixin!\nconsole.log(hasMixin(instance, FooBar))  // true\nconsole.log(hasMixin(instance, Foo))     // true\nconsole.log(hasMixin(instance, Bar))     // true\n```\n\n`hasMixin(instance, mixinClass)` will work anywhere that `instance instanceof mixinClass` works.  Additionally, like `instanceof`, you get the same [type narrowing benefits](https://www.typescriptlang.org/docs/handbook/advanced-types.html#instanceof-type-guards):\n\n```typescript\nif (hasMixin(instance, Foo)) {\n    // inferred type of instance is \"Foo\"\n}\n\nif (hasMixin(instance, Bar)) {\n    // inferred type of instance of \"Bar\"\n}\n```\n\n## Settings\nts-mixer has multiple strategies for mixing classes which can be configured by modifying `settings` from ts-mixer.  For example:\n\n```typescript\nimport { settings, Mixin } from 'ts-mixer';\n\nsettings.prototypeStrategy = 'proxy';\n\n// then use `Mixin` as normal...\n```\n\n### `settings.prototypeStrategy`\n* Determines how ts-mixer will mix class prototypes together\n* Possible values:\n    - `'copy'` (default) - Copies all methods from the classes being mixed into a new prototype object.  (This will include all methods up the prototype chains as well.)  This is the default for ES5 compatibility, but it has the downside of stale references.  For example, if you mix `Foo` and `Bar` to make `FooBar`, then redefine a method on `Foo`, `FooBar` will not have the latest methods from `Foo`.  If this is not a concern for you, `'copy'` is the best value for this setting.\n    - `'proxy'` - Uses an ES6 Proxy to \"soft mix\" prototypes.  Unlike `'copy'`, updates to the base classes _will_ be reflected in the mixed class, which may be desirable.  The downside is that method access is not as performant, nor is it ES5 compatible.\n\n### `settings.staticsStrategy`\n* Determines how static properties are inherited\n* Possible values:\n    - `'copy'` (default) - Simply copies all properties (minus `prototype`) from the base classes/constructor functions onto the mixed class.  Like `settings.prototypeStrategy = 'copy'`, this strategy also suffers from stale references, but shouldn't be a concern if you don't redefine static methods after mixing.\n    - `'proxy'` - Similar to `settings.prototypeStrategy`, proxy's static method access to base classes.  Has the same benefits/downsides.\n\n### `settings.initFunction`\n* If set, `ts-mixer` will automatically call the function with this name upon construction\n* Possible values:\n    - `null` (default) - disables the behavior\n    - a string - function name to call upon construction\n* Read more about why you would want this in [dealing with constructors](#dealing-with-constructors)\n\n### `settings.decoratorInheritance`\n* Determines how decorators are inherited from classes passed to `Mixin(...)`\n* Possible values:\n    - `'deep'` (default) - Deeply inherits decorators from all given classes and their ancestors\n    - `'direct'` - Only inherits decorators defined directly on the given classes\n    - `'none'` - Skips decorator inheritance\n\n# Author\nTanner Nielsen <<EMAIL>>\n* Website - [tannernielsen.com](http://tannernielsen.com)\n* Github - [tannerntannern](https://github.com/tannerntannern)\n", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@6.0.0-beta.0", "_nodeVersion": "14.17.0", "_npmVersion": "6.14.13", "dist": {"integrity": "sha512-TjXInziwjtttIQRy3+UNYZaSA8opO6nw9kvaimQwwJOLga9kwEy7+VxHDifcVo0mIMu76H9B+BMV9xGrmn0+2A==", "shasum": "93f94fcf3425e64704499a95e59af7493c26b3bd", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-6.0.0-beta.0.tgz", "fileCount": 22, "unpackedSize": 73107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1AzgCRA9TVsSAnZWagAAdCEP/1UjEP7j4qZH4LGJCl3g\nmSOdIA/TfI9CLv+qEWiz4cIJQOrxvzZB0FP4SbUdcgSQtIH6FVdlsJeGEBDs\n4pTL9GzTBi/PXNqphTjAl5e6/32z0w/sFEVCsJ2cySxTdH390DyuqpzBU7rR\n8XszACClavvn62jdr/s1V6qkQsP+xyE5HrwinsA1fw+fJrxjzSJqk3mtD4pR\nn/yha47LmtlvlvRlijbuVlwLuY4kD4W1+3sqn7tuo370EbqjC3en4ueLOEuP\n6WqY2CVGINiZGWPDkg3fRV5jzyoitZHb110zIxTOJ4QmmDdIuqX95XAd9kYb\nOZA4c9lyfJ9+HkadSzxBnCRe7CfxF4loS7r6FdV+0gIHgsJ4pL/FTqp7cDyH\nb46rpGddqUncy1uk5SLPrA/1ebXZdzHqDvEftrdH/WFopGrgKrmnOfcX+XtT\nnh+/1+C0Z0X/4TsKgRtfykSFHN3ypSoL4CrYow0G1z/kkQfc41PI5H5/M/4u\nvsaAJEYQ6R1M/p1eXf3s1NB1OHJYHd2qnv0HaC9w6s5W2n8QIY2r0HyQk6s+\nvansfklZ8vxPJUks3yFkEEyU71ZCg3VJUQPDEbPSx2daBFFZgTBRNF7Bc3X6\nSCxEeAlG8coUN+W+h20PgUgl1m+Vq8T3Yucfl+X36PayG1APGvgTFsmSf9iV\ngLoh\r\n=gLHr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZSrbbAk4KNMrzruxWDsNwu7iZH7d/D78Z302zsj8jZgIgQJtRzUDba0W7rFctCAymobj4wdWrnNEXbnWVkMVXox8="}]}, "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_6.0.0-beta.0_1624509663484_0.1615964642044878"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "ts-mixer", "version": "6.0.0", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "browser": "dist/esm/index.js", "unpkg": "dist/esm/index.min.js", "types": "dist/types/index.d.ts", "scripts": {"prebuild": "yarn clean", "build": "rollup -c && tsc", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src/**/*.ts", "test": "nyc mocha", "codegen": "node ./codegen.js", "release": "standard-version"}, "devDependencies": {"@commitlint/cli": "^12.1.4", "@commitlint/config-conventional": "^12.1.4", "@rollup/plugin-typescript": "^8.2.1", "@types/chai": "^4.2.19", "@types/mocha": "^8.2.2", "@types/node": "^15.12.4", "@types/sinon": "^10.0.2", "@typescript-eslint/parser": "^4.27.0", "chai": "^4.3.4", "class-validator": "^0.13.1", "coveralls": "^3.1.0", "eslint": "^7.29.0", "husky": "^4.2.5", "js-yaml": "^4.1.0", "mocha": "^9.0.1", "nyc": "14.1.1", "rimraf": "^3.0.2", "rollup": "^2.52.1", "rollup-plugin-terser": "^7.0.2", "sinon": "^11.1.1", "standard-version": "^9.3.0", "ts-node": "^10.0.0", "tslib": "^2.3.0", "typescript": "^4.3.4", "yarn-add-no-save": "^1.0.3"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixin", "mixins", "multiple inheritance", "mixin classes"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "2e433b43d8c332d35222a656fcc78e08b0a83716", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@6.0.0", "_nodeVersion": "14.17.1", "_npmVersion": "6.14.13", "dist": {"integrity": "sha512-nXIb1fvdY5CBSrDIblLn73NW0qRDk5yJ0Sk1qPBF560OdJfQp9jhl+0tzcY09OZ9U+6GpeoI9RjwoIKFIoB9MQ==", "shasum": "4e631d3a36e3fa9521b973b132e8353bc7267f9f", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-6.0.0.tgz", "fileCount": 22, "unpackedSize": 73200, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5jLkCRA9TVsSAnZWagAAq+IP/1SBpUNsDrnlZIyBhaLJ\nUk6Pq8+SFi5kB5en5GeOm/u3dyKy9mgWDHARB72PBg7e6xVa7+9/176bepeF\n+WQspWn7Zz4sSAhrNZcpDxaIzB5Vp0p1kByK9hv5CnXiadOJuMpi2wnfpsra\nt2PGmCN8o7IgV205vIzGeHXVT6yhB3hiBX700TpHLEhf/aFYcT5AnfVOdvPZ\n2DylyzMDUpcbL7BKwAhbeYn3xsYX3nM/1+xzGjkJn5tXHgZ05peGFYGJjMpZ\nvMxU1GtZERCkiB5JGwj4achmIf2Mlc8U45sbYlTD9vS3uYO+qldNlIxNHwIW\n7GQpn/kryZT3PMbeK84xErXmtORUa8uEg5yI0ECzgr2bkd8f5AZNQbMkuMsL\nW9fAVg8ghB9ZwwZhuDL0GluBSQZYMyW70LpMsXzYh5FlQG+aVXPqAs6mykPH\nBDrHXVOJFI69/xxYgU8HlLZEAa4n8ndHGjtkEf5uJb9ggUOeaFamgBMl4P6y\n2MRvuKZQfoDmgG7Cta0pNZ+mAlWo1mH8aBYYLyjTaGDwnuaW7EvppjeQ9WGg\nRMABlPhZu5R2pBW22V4/i7fxytp/lvauvs8YzLuzNEfgyoCxjny2BUbWUy2U\nTdEsllyuQyvbETVbY+h0XqoUHeg0xDn8r+KSK9C9XZzPc0fvsaE49r3P07BB\nsEbF\r\n=ujA6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEdWPM10FuZLRnn90JaTnMLELffTepuy6bSBtwZ5r1jMAiBIYpDQVjNJ0mPUn3p68od9lsCbu16oCqOSG7KB+SYpCg=="}]}, "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_6.0.0_1625699044015_0.30178416569270006"}, "_hasShrinkwrap": false}, "6.0.1": {"name": "ts-mixer", "version": "6.0.1", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "browser": "dist/esm/index.js", "unpkg": "dist/esm/index.min.js", "types": "dist/types/index.d.ts", "scripts": {"prebuild": "yarn clean", "build": "rollup -c && tsc", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src/**/*.ts", "test": "nyc mocha", "codegen": "node ./codegen.js", "release": "standard-version"}, "devDependencies": {"@commitlint/cli": "^12.1.4", "@commitlint/config-conventional": "^12.1.4", "@rollup/plugin-typescript": "^8.2.1", "@types/chai": "^4.2.19", "@types/mocha": "^8.2.2", "@types/node": "^15.12.4", "@types/sinon": "^10.0.2", "@typescript-eslint/parser": "^4.27.0", "chai": "^4.3.4", "class-validator": "^0.13.1", "coveralls": "^3.1.0", "eslint": "^7.29.0", "husky": "^4.2.5", "js-yaml": "^4.1.0", "mocha": "^9.0.1", "nyc": "14.1.1", "rimraf": "^3.0.2", "rollup": "^2.52.1", "rollup-plugin-terser": "^7.0.2", "sinon": "^11.1.1", "standard-version": "^9.3.0", "ts-node": "^10.0.0", "tslib": "^2.3.0", "typescript": "^4.3.4", "yarn-add-no-save": "^1.0.3"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixin", "mixins", "multiple inheritance", "mixin classes"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "85f4fea6d72a08928e6c031f5ef77f2dd9f84390", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@6.0.1", "_nodeVersion": "14.19.0", "_npmVersion": "6.14.16", "dist": {"integrity": "sha512-hvE+ZYXuINrx6Ei6D6hz+PTim0Uf++dYbK9FFifLNwQj+RwKquhQpn868yZsCtJYiclZF1u8l6WZxxKi+vv7Rg==", "shasum": "7c2627fb98047eb5f3c7f2fee39d1521d18fe87a", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-6.0.1.tgz", "fileCount": 22, "unpackedSize": 73501, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiLVXpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpPuA/9FL2UmbL51Ury9wjVjlXBJSS7Mo9Sas49cDUCRMbGLDyJF31f\r\nwxE8k59vVIGiXyJsTPnFJRpKdkPz38jVSaWOdewMd4JTmzkaR8HwijwxUdys\r\n4/wtD6POHEM3q7TG2B+egEytJPsLsZsstK09+CClIdJbz8A2xInYlz0TuJ5b\r\nVpEosDzFFACVksKCxqjtCtay+oIeHe7cR86lNOFTgeLaqx9e3oMfDqLK68gz\r\ndOziTP7K/BIDJRjkqvslTudVgoVYPcY/rbcoOFB776wtzZscXhxUoplWyej9\r\nqf07SAMSu7oxUytsj4UR8ni8Snd7eEytc+f4QZXJDj4VgJuymjPgGwau/KOF\r\nqRfYNNV2/cxBm0rBjDi8EiVFgeN8sIQ/iutdRBOGj3/FabFFtbXHiT9cJSue\r\nY+qLyNheubKHhx6pMM7dhvLhCT7G7YM5U3I8oWHVLcGeNLDZwk1NnypQf6+8\r\nij+zeJTKiH1YkxIsxNDwSBeTU0u8GThAE5wWwgjXlHXn41w2z+HVfERfmFL+\r\nmWcwoWPLnekLnej17RC/LnNn9rfNtUOa/rTLWvWEb9uF6avUhj65wYafjnLI\r\ndxqCvZ0zYT+1ZaY1jEBcZ/xX+hPAbpUDWwRp0l9rk3cI13OxHJvwOk4YXyvZ\r\nNqD0gsaLnHSjcTAuiyOMGkzxI1eLwGtLDpg=\r\n=1oOB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDujWvEV1IdN6V28PQGeemwgxLcxTxqBHS+2CLI05G5qwIhAOlcXuDwgjlVoiyM56R84R6XEPDGrBZKkTKSsK48g1rE"}]}, "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_6.0.1_1647138281538_0.06280475520478723"}, "_hasShrinkwrap": false}, "6.0.2": {"name": "ts-mixer", "version": "6.0.2", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "browser": "dist/esm/index.js", "unpkg": "dist/esm/index.min.js", "types": "dist/types/index.d.ts", "scripts": {"prebuild": "yarn clean", "build": "rollup -c && tsc", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src/**/*.ts", "test": "nyc mocha", "codegen": "node ./codegen.js", "release": "standard-version"}, "devDependencies": {"@commitlint/cli": "^12.1.4", "@commitlint/config-conventional": "^12.1.4", "@rollup/plugin-typescript": "^8.2.1", "@types/chai": "^4.2.19", "@types/mocha": "^8.2.2", "@types/node": "^15.12.4", "@types/sinon": "^10.0.2", "@typescript-eslint/parser": "^4.27.0", "chai": "^4.3.4", "class-validator": "^0.13.1", "coveralls": "^3.1.0", "eslint": "^7.29.0", "husky": "^4.2.5", "js-yaml": "^4.1.0", "mocha": "^9.0.1", "nyc": "14.1.1", "rimraf": "^3.0.2", "rollup": "^2.52.1", "rollup-plugin-terser": "^7.0.2", "sinon": "^11.1.1", "standard-version": "^9.3.0", "ts-node": "^10.0.0", "tslib": "^2.3.0", "typescript": "^4.3.4", "yarn-add-no-save": "^1.0.3"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixin", "mixins", "multiple inheritance", "mixin classes"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "a9534061fa18cb976dadcfaefb17c04a466ebc48", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@6.0.2", "_nodeVersion": "14.20.1", "_npmVersion": "6.14.17", "dist": {"integrity": "sha512-zvHx3VM83m2WYCE8XL99uaM7mFwYSkjR2OZti98fabHrwkjsCvgwChda5xctein3xGOyaQhtTeDq/1H/GNvF3A==", "shasum": "3e4e4bb8daffb24435f6980b15204cb5b287e016", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-6.0.2.tgz", "fileCount": 22, "unpackedSize": 73984, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICQfwxUDRV6Br5ApeS6bYIbDdkySC5LXzJXpAC2+WpkuAiAgSh1b36xAZ+zoMypqRoI7pFPA+4qeqRBnbZHtOw7iZw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjbHfDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmofag//caQefoVFBWDCB8733c9eiSO67wFLCGmMj0ycucvciYlvDcCe\r\nkopQBanjBfMozdNPJoIhYzg2IaQjVlxuzirVTuJuoJYt6+1CFzejGN43HN/Z\r\nSCCZ5Tkvq6H5AMS/HlgQkEaYYLLaR8SBOFkRrYWB5Fp7fJR2dDaoVR9H1QIS\r\nHMUJqqjtjM3gowskAHhGMsuykaAbbcHTQcQ2ho2T3Umh+dkRyCg2v/Ds2Mbz\r\nhS0AHDZ+yPJf+nESmzmjfM1Ccmaxxah/WwGdnELBA30xkwDvlQ5YgDa+R2AP\r\nGfuPHFi400c/Oa1dSZBwQzLX0aQFTr5tpu5/1aAcCQRjxmr29liAJfEVaCXN\r\nWE/LiYB1XR4nI+ZImA2NhOxbJ0FmEq2W23QFR2nfjmCe38/zj5iAoxA82ybx\r\ncHuyW/zqPwcjK8/YtPvDXvWuGlNhqcPX/eCf5Rsi1KFSbhzOXfW5XOrOGJii\r\nNITXyzpKa/HblELL18S7yTfRvJGklJkoKsAcFzKbcPGpgv0ZqO5lZs+KSm7J\r\n1XlDTcgEa5B4SeSIdwA7GaFgwOjwY/rKFY8R4+/3/PsoGRLI3gXQsUMUlEoC\r\ncFDIMkMpS9BrUpeBTR4inEIEcLQaNCWoDpSGTmt1ew7chEbjeVoG61qu61ee\r\nR/NU443XW/DVWKeInzr78Kl0c1zn62p14w8=\r\n=5wDf\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_6.0.2_1668052930955_0.15907099113297063"}, "_hasShrinkwrap": false}, "6.0.3": {"name": "ts-mixer", "version": "6.0.3", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "browser": "dist/esm/index.js", "unpkg": "dist/esm/index.min.js", "types": "dist/types/index.d.ts", "scripts": {"prebuild": "yarn clean", "build": "rollup -c && tsc", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src/**/*.ts", "test": "nyc mocha", "codegen": "node ./codegen.js", "release": "standard-version"}, "devDependencies": {"@commitlint/cli": "^12.1.4", "@commitlint/config-conventional": "^12.1.4", "@rollup/plugin-typescript": "^8.2.1", "@types/chai": "^4.2.19", "@types/mocha": "^8.2.2", "@types/node": "^15.12.4", "@types/sinon": "^10.0.2", "@typescript-eslint/parser": "^4.27.0", "chai": "^4.3.4", "class-validator": "^0.13.1", "coveralls": "^3.1.0", "eslint": "^7.29.0", "husky": "^4.2.5", "js-yaml": "^4.1.0", "mocha": "^9.0.1", "nyc": "14.1.1", "rimraf": "^3.0.2", "rollup": "^2.52.1", "rollup-plugin-terser": "^7.0.2", "sinon": "^11.1.1", "standard-version": "^9.3.0", "ts-node": "^10.0.0", "tslib": "^2.3.0", "typescript": "^4.3.4", "yarn-add-no-save": "^1.0.3"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixin", "mixins", "multiple inheritance", "mixin classes"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "d7d43717c386776d92e1e60fc82322e60c638eab", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@6.0.3", "_nodeVersion": "14.21.2", "_npmVersion": "6.14.17", "dist": {"integrity": "sha512-k43M7uCG1AkTyxgnmI5MPwKoUvS/bRvLvUb7+Pgpdlmok8AoqmUaZxUUw8zKM5B1lqZrt41GjYgnvAi0fppqgQ==", "shasum": "69bd50f406ff39daa369885b16c77a6194c7cae6", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-6.0.3.tgz", "fileCount": 30, "unpackedSize": 83966, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG9Y8qj7G7oDsvF/2eKP6G/MpXMO2MMugXOu8zSGKN9NAiEA1i2YfcVD+OfEtXfA4/+pKL0CrXVcO8WbHOCCc7DEuVI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5GAzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpLMxAAgkKgm7+tBKomAAFMBeGEO3f1XMsso+GMJTIOYEC7yVdLEgY0\r\nxVWPj0K/CplUeZLNUwv0/vSKG3/0CoU+mzP0va1zQL4Fr+9kUGiWOIP/EBth\r\ne+0KNJe75Qeqdm2QSBzaZ1uHI9LJKT63rXD1HrhmA1r8Pflzb5C+8fYSg2nx\r\nnAXYR5EpDQ9IGqTH8WArDQZyEkVp4HjLw1OUSkxrtmZjwM5p0lUlD9YwFFJT\r\ngEx+EieHSRnGbc2j3hP2Nk8bS8+0hLmHh+sYaBr67vcz7g/kr6Pyjpou0Nwk\r\nmWjXnshznqhGlX00cFR3axGXU1iZL7/tSJja5F/OZro0dCyluo8k5SaMU2nn\r\nAgTqimsf4MrB2WUa7ZscQjC6bhMfyr/LxaQ+93/O9A7YdywpVHd6f5CVfhOS\r\nPsaSJwVVig+EthVWtGJ3/m5u7N6pOoE2+9GQoxzVi98Hrh+8dsRg5ajUY56x\r\nqvngeTe+n/gUuBXkyE3RKdxmXRdkojvxXxr2PQ2qheSPnGGIR8zvHFEwoukS\r\ndETWEryDXq+R0t+AHjLUef8FwD4yx3TTYOAiRveJWL0Fgq7Ja4gQM9wHz4Mz\r\nIu3rkxjLPER3IWOl7kPZgFaF21MPVGZhAuAI+2ZWO+12Bp5GScRyIMurfPII\r\nzzZZA9quVdLN2/fFo+aVvYv6D/gSMeiSD1E=\r\n=BgsM\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_6.0.3_1675911219115_0.21138159983457605"}, "_hasShrinkwrap": false}, "6.0.4": {"name": "ts-mixer", "version": "6.0.4", "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "browser": "dist/esm/index.js", "unpkg": "dist/esm/index.min.js", "types": "dist/types/index.d.ts", "scripts": {"prebuild": "yarn clean", "build": "rollup -c && tsc", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src/**/*.ts", "test": "nyc mocha", "codegen": "node ./codegen.js", "release": "standard-version"}, "devDependencies": {"@commitlint/cli": "^12.1.4", "@commitlint/config-conventional": "^12.1.4", "@rollup/plugin-typescript": "^8.2.1", "@types/chai": "^4.2.19", "@types/mocha": "^8.2.2", "@types/node": "^15.12.4", "@types/sinon": "^10.0.2", "@typescript-eslint/parser": "^4.27.0", "chai": "^4.3.4", "class-validator": "^0.13.1", "coveralls": "^3.1.0", "eslint": "^7.29.0", "husky": "^4.2.5", "js-yaml": "^4.1.0", "mocha": "^9.0.1", "nyc": "14.1.1", "rimraf": "^3.0.2", "rollup": "^2.52.1", "rollup-plugin-terser": "^7.0.2", "sinon": "^11.1.1", "standard-version": "^9.3.0", "ts-node": "^10.0.0", "tslib": "^2.3.0", "typescript": "^4.3.4", "yarn-add-no-save": "^1.0.3"}, "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "keywords": ["typescript", "mixin", "mixins", "multiple inheritance", "mixin classes"], "author": {"name": "<PERSON>"}, "license": "MIT", "gitHead": "6798034f99b3017c4ef54c69ce2864d6e620ab40", "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "_id": "ts-mixer@6.0.4", "_nodeVersion": "14.21.3", "_npmVersion": "6.14.18", "dist": {"integrity": "sha512-ufKpbmrugz5Aou4wcr5Wc1UUFWOLhq+Fm6qa6P0w0K5Qw2yhaUoiWszhCVuNQyNwrlGiscHOmqYoAox1PtvgjA==", "shasum": "1da39ceabc09d947a82140d9f09db0f84919ca28", "tarball": "https://registry.npmjs.org/ts-mixer/-/ts-mixer-6.0.4.tgz", "fileCount": 30, "unpackedSize": 84313, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDNU/vtKqIqWivUWtbnsNg8Mx80aUHHty1Sfh/hPWBA2AiEA6Uz4Y+Mp9IhBkg2X3uLRNoddM/U/wVMA8d9GYLli1XQ="}]}, "_npmUser": {"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ts-mixer_6.0.4_1708402811027_0.39799632077080105"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-10-22T03:35:40.741Z", "1.0.0": "2018-10-22T03:35:40.896Z", "modified": "2024-02-20T04:20:11.589Z", "1.0.1": "2018-10-22T23:18:34.558Z", "1.0.2": "2018-10-22T23:24:11.765Z", "1.0.3": "2018-10-23T02:49:24.352Z", "1.1.0": "2018-10-26T03:46:15.231Z", "2.0.0": "2018-10-27T04:35:17.379Z", "2.0.1": "2018-10-27T04:39:11.518Z", "2.0.2": "2018-10-27T15:40:30.344Z", "2.0.3": "2018-10-30T16:51:34.779Z", "2.0.4": "2018-11-04T01:09:08.983Z", "2.0.5": "2019-01-14T18:37:08.142Z", "3.0.0-beta": "2019-04-21T15:09:13.036Z", "3.0.0-beta.1": "2019-04-21T15:15:24.402Z", "3.0.0": "2019-05-14T02:18:12.276Z", "3.0.1": "2019-05-16T16:31:56.178Z", "3.1.0": "2019-05-17T03:59:41.786Z", "3.1.1": "2019-05-18T03:55:36.329Z", "3.1.2": "2019-07-11T12:31:02.095Z", "3.1.3": "2019-07-30T00:09:57.883Z", "3.1.4": "2019-07-31T00:24:04.441Z", "4.0.0": "2019-08-01T01:49:56.846Z", "5.0.0-beta.0": "2020-02-02T05:17:59.709Z", "5.0.0": "2020-03-01T03:04:35.278Z", "5.1.0-beta.0": "2020-03-26T05:45:34.933Z", "5.1.0": "2020-03-27T03:45:45.544Z", "5.2.0-beta.0": "2020-04-13T05:48:56.786Z", "5.2.0-beta.1": "2020-04-23T04:25:29.374Z", "5.2.0": "2020-04-29T03:43:31.880Z", "5.2.1": "2020-05-08T12:56:37.172Z", "5.3.0-beta.0": "2020-05-31T18:34:26.820Z", "5.3.0": "2020-06-01T01:42:58.988Z", "5.4.0": "2020-11-18T07:09:44.335Z", "5.4.1": "2021-04-30T00:03:00.779Z", "6.0.0-beta.0": "2021-06-24T04:41:03.691Z", "6.0.0": "2021-07-07T23:04:04.161Z", "6.0.1": "2022-03-13T02:24:41.673Z", "6.0.2": "2022-11-10T04:02:11.145Z", "6.0.3": "2023-02-09T02:53:39.271Z", "6.0.4": "2024-02-20T04:20:11.179Z"}, "maintainers": [{"name": "tanner<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A very small TypeScript library that provides tolerable Mixin functionality.", "homepage": "https://github.com/tannerntannern/ts-mixer#readme", "repository": {"type": "git", "url": "git+https://github.com/tannerntannern/ts-mixer.git"}, "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/tannerntannern/ts-mixer/issues"}, "license": "MIT", "readme": "# ts-mixer\n[version-badge]: https://badgen.net/npm/v/ts-mixer\n[version-link]: https://npmjs.com/package/ts-mixer\n[build-link]: https://github.com/tannerntannern/ts-mixer/actions\n[ts-versions]: https://badgen.net/badge/icon/4.2,4.6,5.0,5.2?icon=typescript&label&list=|\n[node-versions]: https://badgen.net/badge/node/16%2C18%2C20/blue/?list=|\n[![npm version][version-badge]][version-link]\n[![TS Versions][ts-versions]][build-link]\n[![Node.js Versions][node-versions]][build-link]\n[![Minified Size](https://badgen.net/bundlephobia/min/ts-mixer)](https://bundlephobia.com/result?p=ts-mixer)\n[![Conventional Commits](https://badgen.net/badge/conventional%20commits/1.0.0/yellow)](https://conventionalcommits.org)\n\n## Overview\n`ts-mixer` brings mixins to TypeScript.  \"Mixins\" to `ts-mixer` are just classes, so you already know how to write them, and you can probably mix classes from your favorite library without trouble.\n\nThe mixin problem is more nuanced than it appears.  I've seen countless code snippets that work for certain situations, but fail in others.  `ts-mixer` tries to take the best from all these solutions while accounting for the situations you might not have considered.\n\n[Quick start guide](#quick-start)\n\n### Features\n* mixes plain classes\n* mixes classes that extend other classes\n* mixes classes that were mixed with `ts-mixer`\n* supports static properties\n* supports protected/private properties (the popular function-that-returns-a-class solution does not)\n* mixes abstract classes (requires TypeScript >= 4.2)\n* mixes generic classes (with caveats [[1](#caveats)])\n* supports class, method, and property decorators (with caveats [[2, 5](#caveats)])\n* mostly supports the complexity presented by constructor functions (with caveats [[3](#caveats)])\n* comes with an `instanceof`-like replacement (with caveats [[4, 5](#caveats)])\n* [multiple mixing strategies](#settings) (ES6 proxies vs hard copy)\n\n### Caveats\n1. Mixing generic classes requires a more cumbersome notation, but it's still possible.  See [mixing generic classes](#mixing-generic-classes) below.\n2. Using decorators in mixed classes also requires a more cumbersome notation.  See [mixing with decorators](#mixing-with-decorators) below.\n3. ES6 made it impossible to use `.apply(...)` on class constructors (or any means of calling them without `new`), which makes it impossible for `ts-mixer` to pass the proper `this` to your constructors.  This may or may not be an issue for your code, but there are options to work around it.  See [dealing with constructors](#dealing-with-constructors) below.\n4. `ts-mixer` does not support `instanceof` for mixins, but it does offer a replacement.  See the [hasMixin function](#hasmixin) for more details.\n5. Certain features (specifically, `@decorator` and `hasMixin`) make use of ES6 `Map`s, which means you must either use ES6+ or polyfill `Map` to use them.  If you don't need these features, you should be fine without.\n\n## Quick Start\n### Installation\n```\n$ npm install ts-mixer\n```\n\nor if you prefer [Yarn](https://yarnpkg.com):\n\n```\n$ yarn add ts-mixer\n```\n\n### Basic Example\n```typescript\nimport { Mixin } from 'ts-mixer';\n\nclass Foo {\n    protected makeFoo() {\n        return 'foo';\n    }\n}\n\nclass Bar {\n    protected makeBar() {\n        return 'bar';\n    }\n}\n\nclass FooBar extends Mixin(Foo, Bar) {\n    public makeFooBar() {\n        return this.makeFoo() + this.makeBar();\n    }\n}\n\nconst fooBar = new FooBar();\n\nconsole.log(fooBar.makeFooBar());  // \"foobar\"\n```\n\n## Special Cases\n### Mixing Generic Classes\nFrustratingly, it is _impossible_ for generic parameters to be referenced in base class expressions.  No matter what, you will eventually run into `Base class expressions cannot reference class type parameters.`\n\nThe way to get around this is to leverage [declaration merging](https://www.typescriptlang.org/docs/handbook/declaration-merging.html), and a slightly different mixing function from ts-mixer: `mix`.  It works exactly like `Mixin`, except it's a decorator, which means it doesn't affect the type information of the class being decorated.  See it in action below:\n\n```typescript\nimport { mix } from 'ts-mixer';\n\nclass Foo<T> {\n    public fooMethod(input: T): T {\n        return input;\n    }\n}\n\nclass Bar<T> {\n    public barMethod(input: T): T {\n        return input;\n    }\n}\n\ninterface FooBar<T1, T2> extends Foo<T1>, Bar<T2> { }\n@mix(Foo, Bar)\nclass FooBar<T1, T2> {\n    public fooBarMethod(input1: T1, input2: T2) {\n        return [this.fooMethod(input1), this.barMethod(input2)];\n    }\n}\n```\n\nKey takeaways from this example:\n* `interface FooBar<T1, T2> extends Foo<T1>, Bar<T2> { }` makes sure `FooBar` has the typing we want, thanks to declaration merging\n* `@mix(Foo, Bar)` wires things up \"on the JavaScript side\", since the interface declaration has nothing to do with runtime behavior.\n* The reason we have to use the `mix` decorator is that the typing produced by `Mixin(Foo, Bar)` would conflict with the typing of the interface.  `mix` has no effect \"on the TypeScript side,\" thus avoiding type conflicts.\n\n### Mixing with Decorators\nPopular libraries such as [class-validator](https://github.com/typestack/class-validator) and [TypeORM](https://github.com/typeorm/typeorm) use decorators to add functionality.  Unfortunately, `ts-mixer` has no way of knowing what these libraries do with the decorators behind the scenes.  So if you want these decorators to be \"inherited\" with classes you plan to mix, you first have to wrap them with a special `decorate` function exported by `ts-mixer`.  Here's an example using `class-validator`:\n\n```typescript\nimport { IsBoolean, IsIn, validate } from 'class-validator';\nimport { Mixin, decorate } from 'ts-mixer';\n\nclass Disposable {\n    @decorate(IsBoolean())  // instead of @IsBoolean()\n    isDisposed: boolean = false;\n}\n\nclass Statusable {\n    @decorate(IsIn(['red', 'green']))  // instead of @IsIn(['red', 'green'])\n    status: string = 'green';\n}\n\nclass ExtendedObject extends Mixin(Disposable, Statusable) {}\n\nconst extendedObject = new ExtendedObject();\nextendedObject.status = 'blue';\n\nvalidate(extendedObject).then(errors => {\n    console.log(errors);\n});\n```\n\n### Dealing with Constructors\nAs mentioned in the [caveats section](#caveats), ES6 disallowed calling constructor functions without `new`.  This means that the only way for `ts-mixer` to mix instance properties is to instantiate each base class separately, then copy the instance properties into a common object.  The consequence of this is that constructors mixed by `ts-mixer` will _not_ receive the proper `this`.\n\n**This very well may not be an issue for you!**  It only means that your constructors need to be \"mostly pure\" in terms of how they handle `this`.  Specifically, your constructors cannot produce [side effects](https://en.wikipedia.org/wiki/Side_effect_%28computer_science%29) involving `this`, _other than adding properties to `this`_ (the most common side effect in JavaScript constructors).\n\nIf you simply cannot eliminate `this` side effects from your constructor, there is a workaround available:  `ts-mixer` will automatically forward constructor parameters to a predesignated init function (`settings.initFunction`) if it's present on the class.  Unlike constructors, functions can be called with an arbitrary `this`, so this predesignated init function _will_ have the proper `this`.  Here's a basic example:\n\n```typescript\nimport { Mixin, settings } from 'ts-mixer';\n\nsettings.initFunction = 'init';\n\nclass Person {\n    public static allPeople: Set<Person> = new Set();\n    \n    protected init() {\n        Person.allPeople.add(this);\n    }\n}\n\ntype PartyAffiliation = 'democrat' | 'republican';\n\nclass PoliticalParticipant {\n    public static democrats: Set<PoliticalParticipant> = new Set();\n    public static republicans: Set<PoliticalParticipant> = new Set();\n    \n    public party: PartyAffiliation;\n    \n    // note that these same args will also be passed to init function\n    public constructor(party: PartyAffiliation) {\n        this.party = party;\n    }\n    \n    protected init(party: PartyAffiliation) {\n        if (party === 'democrat')\n            PoliticalParticipant.democrats.add(this);\n        else\n            PoliticalParticipant.republicans.add(this);\n    }\n}\n\nclass Voter extends Mixin(Person, PoliticalParticipant) {}\n\nconst v1 = new Voter('democrat');\nconst v2 = new Voter('democrat');\nconst v3 = new Voter('republican');\nconst v4 = new Voter('republican');\n```\n\nNote the above `.add(this)` statements.  These would not work as expected if they were placed in the constructor instead, since `this` is not the same between the constructor and `init`, as explained above.\n\n## Other Features\n### hasMixin\nAs mentioned above, `ts-mixer` does not support `instanceof` for mixins.  While it is possible to implement [custom `instanceof` behavior](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Symbol/hasInstance), this library does not do so because it would require modifying the source classes, which is deliberately avoided.\n\nYou can fill this missing functionality with `hasMixin(instance, mixinClass)` instead.  See the below example:\n\n```typescript\nimport { Mixin, hasMixin } from 'ts-mixer';\n\nclass Foo {}\nclass Bar {}\nclass FooBar extends Mixin(Foo, Bar) {}\n\nconst instance = new FooBar();\n\n// doesn't work with instanceof...\nconsole.log(instance instanceof FooBar)  // true\nconsole.log(instance instanceof Foo)     // false\nconsole.log(instance instanceof Bar)     // false\n\n// but everything works nicely with hasMixin!\nconsole.log(hasMixin(instance, FooBar))  // true\nconsole.log(hasMixin(instance, Foo))     // true\nconsole.log(hasMixin(instance, Bar))     // true\n```\n\n`hasMixin(instance, mixinClass)` will work anywhere that `instance instanceof mixinClass` works.  Additionally, like `instanceof`, you get the same [type narrowing benefits](https://www.typescriptlang.org/docs/handbook/advanced-types.html#instanceof-type-guards):\n\n```typescript\nif (hasMixin(instance, Foo)) {\n    // inferred type of instance is \"Foo\"\n}\n\nif (hasMixin(instance, Bar)) {\n    // inferred type of instance of \"Bar\"\n}\n```\n\n## Settings\nts-mixer has multiple strategies for mixing classes which can be configured by modifying `settings` from ts-mixer.  For example:\n\n```typescript\nimport { settings, Mixin } from 'ts-mixer';\n\nsettings.prototypeStrategy = 'proxy';\n\n// then use `Mixin` as normal...\n```\n\n### `settings.prototypeStrategy`\n* Determines how ts-mixer will mix class prototypes together\n* Possible values:\n    - `'copy'` (default) - Copies all methods from the classes being mixed into a new prototype object.  (This will include all methods up the prototype chains as well.)  This is the default for ES5 compatibility, but it has the downside of stale references.  For example, if you mix `Foo` and `Bar` to make `FooBar`, then redefine a method on `Foo`, `FooBar` will not have the latest methods from `Foo`.  If this is not a concern for you, `'copy'` is the best value for this setting.\n    - `'proxy'` - Uses an ES6 Proxy to \"soft mix\" prototypes.  Unlike `'copy'`, updates to the base classes _will_ be reflected in the mixed class, which may be desirable.  The downside is that method access is not as performant, nor is it ES5 compatible.\n\n### `settings.staticsStrategy`\n* Determines how static properties are inherited\n* Possible values:\n    - `'copy'` (default) - Simply copies all properties (minus `prototype`) from the base classes/constructor functions onto the mixed class.  Like `settings.prototypeStrategy = 'copy'`, this strategy also suffers from stale references, but shouldn't be a concern if you don't redefine static methods after mixing.\n    - `'proxy'` - Similar to `settings.prototypeStrategy`, proxy's static method access to base classes.  Has the same benefits/downsides.\n\n### `settings.initFunction`\n* If set, `ts-mixer` will automatically call the function with this name upon construction\n* Possible values:\n    - `null` (default) - disables the behavior\n    - a string - function name to call upon construction\n* Read more about why you would want this in [dealing with constructors](#dealing-with-constructors)\n\n### `settings.decoratorInheritance`\n* Determines how decorators are inherited from classes passed to `Mixin(...)`\n* Possible values:\n    - `'deep'` (default) - Deeply inherits decorators from all given classes and their ancestors\n    - `'direct'` - Only inherits decorators defined directly on the given classes\n    - `'none'` - Skips decorator inheritance\n\n# Author\nTanner Nielsen <<EMAIL>>\n* Website - [tannernielsen.com](http://tannernielsen.com)\n* Github - [tannerntannern](https://github.com/tannerntannern)\n", "readmeFilename": "README.md", "keywords": ["typescript", "mixin", "mixins", "multiple inheritance", "mixin classes"]}