{"_id": "resolve-from", "_rev": "19-6441605e158d39c45054d1bc737f30cd", "name": "resolve-from", "description": "Resolve the path of a module like `require.resolve()` but from a given path", "dist-tags": {"latest": "5.0.0"}, "versions": {"1.0.0": {"name": "resolve-from", "version": "1.0.0", "description": "Resolve the path of a module like require.resolve() but from a given path", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/resolve-from"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["require", "resolve", "path", "module", "from", "like", "path"], "devDependencies": {"ava": "0.0.3"}, "bugs": {"url": "https://github.com/sindresorhus/resolve-from/issues"}, "homepage": "https://github.com/sindresorhus/resolve-from", "_id": "resolve-from@1.0.0", "_shasum": "dedda6f6f6346f0211ce4bc25aca0ca7826bb367", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "dedda6f6f6346f0211ce4bc25aca0ca7826bb367", "tarball": "https://registry.npmjs.org/resolve-from/-/resolve-from-1.0.0.tgz", "integrity": "sha512-8oho8oGBe8Z3toBV9TeT0AdSx6TNTrWQRvwmkJlj6grJg2IiSt0Ci8r2fe+fhJfYdqLm0zyDYsGYSdjReaf4qA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFZlHVmcKY9yeUwPN+te7LJh112FYcpxZU4IGvzYer7yAiAE9PfMgzOCiBSo+hc9zrf8KF7/3osiadvojlu2sFKfZw=="}]}, "directories": {}}, "1.0.1": {"name": "resolve-from", "version": "1.0.1", "description": "Resolve the path of a module like require.resolve() but from a given path", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/resolve-from"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["require", "resolve", "path", "module", "from", "like", "path"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "bae2cf1d66c616ad2eb27e0fe85a10ff0f2dfc92", "bugs": {"url": "https://github.com/sindresorhus/resolve-from/issues"}, "homepage": "https://github.com/sindresorhus/resolve-from", "_id": "resolve-from@1.0.1", "_shasum": "26cbfe935d1aeeeabb29bc3fe5aeb01e93d44226", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "26cbfe935d1aeeeabb29bc3fe5aeb01e93d44226", "tarball": "https://registry.npmjs.org/resolve-from/-/resolve-from-1.0.1.tgz", "integrity": "sha512-kT10v4dhrlLNcnO084hEjvXCI1wUG9qZLoz2RogxqDQQYy7IxjI/iMUkOtQTNEh6rzHxvdQWHsJyel1pKOVCxg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCiFPYNd6HmGXjSSFtvLIEQ3EZZ9NUGNiFzVww/I1fX5AIgW66unL+VEagO0sNEi0sctaHY1dItR7O3hp13vzbQw50="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "resolve-from", "version": "2.0.0", "description": "Resolve the path of a module like require.resolve() but from a given path", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/resolve-from"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["require", "resolve", "path", "module", "from", "like", "path"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "583e0f8df06e1bc4d1c96d8d4f2484c745f522c3", "bugs": {"url": "https://github.com/sindresorhus/resolve-from/issues"}, "homepage": "https://github.com/sindresorhus/resolve-from", "_id": "resolve-from@2.0.0", "_shasum": "9480ab20e94ffa1d9e80a804c7ea147611966b57", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "9480ab20e94ffa1d9e80a804c7ea147611966b57", "tarball": "https://registry.npmjs.org/resolve-from/-/resolve-from-2.0.0.tgz", "integrity": "sha512-qpFcKaXsq8+oRoLilkwyc7zHGF5i9Q2/25NIgLQQ/+VVv9rU4qvr6nXVAw1DsnXJyQkZsR4Ytfbtg5ehfcUssQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBOMts2OuBLdp4jRd+KOvP2IvZY+jvvmCS4w+wN+3saHAiEA+fcSIU6Tu7/TIqHtVL2uvIeNosDu3Sc2YwjEakXhL+M="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "3.0.0": {"name": "resolve-from", "version": "3.0.0", "description": "Resolve the path of a module like `require.resolve()` but from a given path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/resolve-from.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["require", "resolve", "path", "module", "from", "like", "import", "path"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "c3a318d7144b9cc22488f5d2f3884b6c8c05a05a", "bugs": {"url": "https://github.com/sindresorhus/resolve-from/issues"}, "homepage": "https://github.com/sindresorhus/resolve-from#readme", "_id": "resolve-from@3.0.0", "_shasum": "b22c7af7d9d6881bc8b6e653335eebcb0a188748", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.7.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b22c7af7d9d6881bc8b6e653335eebcb0a188748", "tarball": "https://registry.npmjs.org/resolve-from/-/resolve-from-3.0.0.tgz", "integrity": "sha512-GnlH6vxLymXJNMBo7XP1fJIzBFbdYt49CuTwmB/6N53t+kMPRMFKz783LlQ4tv28XoQfMWinAJX6WCGf2IlaIw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCacMANR7ov42DWDpqlZIJ9j9mm5q42KZbRbXfbdtJ7JgIgTLmInrYvnv7wm5qLr6ZzYIilhyuU4eudr+Ua9OQlHwg="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/resolve-from-3.0.0.tgz_1493366293559_0.5914990743622184"}, "directories": {}}, "4.0.0": {"name": "resolve-from", "version": "4.0.0", "description": "Resolve the path of a module like `require.resolve()` but from a given path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/resolve-from.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["require", "resolve", "path", "module", "from", "like", "import"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "60cd04e69135b96b98b848fff719b1276a5610c0", "bugs": {"url": "https://github.com/sindresorhus/resolve-from/issues"}, "homepage": "https://github.com/sindresorhus/resolve-from#readme", "_id": "resolve-from@4.0.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.5.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==", "shasum": "4abcd852ad32dd7baabfe9b40e00a36db5f392e6", "tarball": "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFgqnTs3q1vjji4AAId3bd1CWGThpg9nbR4wgeiSLK4WAiAuJT1LNSXHw3OqLopFsWOw/A/XqpTvH553EzlWG4fXRw=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/resolve-from-4.0.0.tgz_1506160096801_0.3549803947098553"}, "directories": {}}, "5.0.0": {"name": "resolve-from", "version": "5.0.0", "description": "Resolve the path of a module like `require.resolve()` but from a given path", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/resolve-from.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["require", "resolve", "path", "module", "from", "like", "import"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "53f4a1b40f972dbfc4dda9627d24068000eae897", "bugs": {"url": "https://github.com/sindresorhus/resolve-from/issues"}, "homepage": "https://github.com/sindresorhus/resolve-from#readme", "_id": "resolve-from@5.0.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==", "shasum": "c35225843df8f776df21c57557bc087e9dfdfc69", "tarball": "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz", "fileCount": 5, "unpackedSize": 5824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJctANCCRA9TVsSAnZWagAAk7AQAIcOATtZd/Q0bDCROwwr\ne0iFyIuXPepEyjRgUuflYglfxBa4zg8z6LEKV2PIlVBryh+nw3bv8HIb0I1+\n9CLHFLwRrmjoDAttsm5EU8wY9wqTGzxoY/n72jnq5Nekpw0ENKkXsElZHaiG\ny3abQy+PAI1jtNhlZwxYMPm5v43JnIscxLKIe2+D4Glfy05779edS7BrrPYw\nKLn8bWgijuc27FIXKwoRKvysyH7Rp+7A45UE2MoWemLuPIPoGv0HI9/qUgrW\n3M1wWqLU3jwPvfa8lshrJ4SESHRxZlOCqOStDJqRP8xV80Us4hTfiPDg9MHx\n2MKoAtB7X4dhaqWXg489O3pUzxNazl9euxFRFybq30O/S8git4b2v0ETHpEt\nPLJZBO4p6Qw8x26CjspGbHTJMbMgQsdvR18FiTvq5Pg74ZkXCDEAH5mQcLUr\nPofEpd/QwQGxfM+Y+/q4y4/Z4fKp7M+1fpbCLHwn5c5TIUNc253CvbXhy+Oc\nZNhafcMsudm2NRYQlMLnWnShztMes2wFk+XRFO1S8E2QWktBHh4s1FIoSuYY\nilRxr3BHtf4g8j5gFBorgUx0RyDMrStlNJQG89HQTtEewrOyboUeUfoJl1Un\nAscCCCLsiSjZNyHT/KZmgfMAJP1go8taMEzmfFDllsYy1rg6GJxQpftyA02V\nCkJD\r\n=95CN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFdfeKkK7ahAmOwRJw9uMCzGkfH4+mLjxb+TuaoM5sWiAiEAy9YosWMTFN9wZVLj8bg13Xa0U9lnl6Hb5RvjkjeE5Ok="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/resolve-from_5.0.0_1555301185445_0.996189064165252"}, "_hasShrinkwrap": false}}, "readme": "# resolve-from [![Build Status](https://travis-ci.org/sindresorhus/resolve-from.svg?branch=master)](https://travis-ci.org/sindresorhus/resolve-from)\n\n> Resolve the path of a module like [`require.resolve()`](https://nodejs.org/api/globals.html#globals_require_resolve) but from a given path\n\n\n## Install\n\n```\n$ npm install resolve-from\n```\n\n\n## Usage\n\n```js\nconst resolveFrom = require('resolve-from');\n\n// There is a file at `./foo/bar.js`\n\nresolveFrom('foo', './bar');\n//=> '/Users/<USER>/dev/test/foo/bar.js'\n```\n\n\n## API\n\n### resolveFrom(fromDirectory, moduleId)\n\nLike `require()`, throws when the module can't be found.\n\n### resolveFrom.silent(fromDirectory, moduleId)\n\nReturns `undefined` instead of throwing when the module can't be found.\n\n#### fromDirectory\n\nType: `string`\n\nDirectory to resolve from.\n\n#### moduleId\n\nType: `string`\n\nWhat you would use in `require()`.\n\n\n## Tip\n\nCreate a partial using a bound function if you want to resolve from the same `fromDirectory` multiple times:\n\n```js\nconst resolveFromFoo = resolveFrom.bind(null, 'foo');\n\nresolveFromFoo('./bar');\nresolveFromFoo('./baz');\n```\n\n\n## Related\n\n- [resolve-cwd](https://github.com/sindresorhus/resolve-cwd) - Resolve the path of a module from the current working directory\n- [import-from](https://github.com/sindresorhus/import-from) - Import a module from a given path\n- [import-cwd](https://github.com/sindresorhus/import-cwd) - Import a module from the current working directory\n- [resolve-pkg](https://github.com/sindresorhus/resolve-pkg) - Resolve the path of a package regardless of it having an entry point\n- [import-lazy](https://github.com/sindresorhus/import-lazy) - Import a module lazily\n- [resolve-global](https://github.com/sindresorhus/resolve-global) - Resolve the path of a globally installed module\n\n\n## License\n\nMIT © [Sindre Sorhus](https://sindresorhus.com)\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-06-22T16:33:46.245Z", "created": "2014-07-20T00:34:24.210Z", "1.0.0": "2014-07-20T00:34:24.210Z", "1.0.1": "2015-10-05T14:33:18.756Z", "2.0.0": "2015-11-29T05:09:13.029Z", "3.0.0": "2017-04-28T07:58:15.268Z", "4.0.0": "2017-09-23T09:48:16.876Z", "5.0.0": "2019-04-15T04:06:25.601Z"}, "homepage": "https://github.com/sindresorhus/resolve-from#readme", "keywords": ["require", "resolve", "path", "module", "from", "like", "import"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/resolve-from.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/resolve-from/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"stringparser": true, "max_devjs": true, "gggauravgandhi": true, "jlsjonas": true, "robmcguinness": true, "arcticicestudio": true, "flumpus-dev": true}}