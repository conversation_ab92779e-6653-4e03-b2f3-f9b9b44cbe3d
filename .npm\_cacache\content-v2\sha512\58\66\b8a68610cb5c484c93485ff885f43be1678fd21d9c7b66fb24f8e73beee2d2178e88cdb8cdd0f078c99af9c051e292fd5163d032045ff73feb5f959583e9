{"_id": "minimist", "_rev": "585-27c42896fc3ecc9935c435edcd5b37b7", "name": "minimist", "description": "parse argument options", "dist-tags": {"latest": "1.2.8", "0.2-backport": "0.2.4"}, "versions": {"0.0.0": {"name": "minimist", "version": "0.0.0", "description": "parse argument options", "main": "index.js", "devDependencies": {"tape": "~1.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/3.6", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@0.0.0", "dist": {"shasum": "0f62459b3333ea881e554e400243e130ef123568", "tarball": "https://registry.npmjs.org/minimist/-/minimist-0.0.0.tgz", "integrity": "sha512-E67oNBArv4Jfy0Hg9ja9Z0tZJJq049HDEmcWnb8HTrAufkT4zvygjyDSFs/KVMPra2+MxU+XLHOSoW/zO2bSCw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDHGPvadEN20Zuvu4aJW2wfeaAgSwyc68ik9oJLlyV3FAiAR7MS9qM0ijFouVMybimonN034DfKZQjVcU9768O41Aw=="}]}, "_from": ".", "_npmVersion": "1.3.0", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.0.1": {"name": "minimist", "version": "0.0.1", "description": "parse argument options", "main": "index.js", "devDependencies": {"tape": "~1.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/3.6", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@0.0.1", "dist": {"shasum": "fa2439fbf7da8525c51b2a74e2815b380abc8ab6", "tarball": "https://registry.npmjs.org/minimist/-/minimist-0.0.1.tgz", "integrity": "sha512-E1CWA04YJ5H7ULMOjYTxh2tTi5CmvdmwO50dXQGmeeZ7JQ+8MUtLzVIvRDK0+nkVQ334mEr1lR0CdwlP5eb4IA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCqwMA2eeyku2vV1NOUILjGS+neXK6x+Dgr/hdffUOqXAIgXT6cSX4ooLRIc220sCDoKE3JyjYr9qnke6i/eYJhH4s="}]}, "_from": ".", "_npmVersion": "1.3.0", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.0.2": {"name": "minimist", "version": "0.0.2", "description": "parse argument options", "main": "index.js", "devDependencies": {"tape": "~1.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@0.0.2", "dist": {"shasum": "3297e0500be195b8fcb56668c45b925bc9bca7ab", "tarball": "https://registry.npmjs.org/minimist/-/minimist-0.0.2.tgz", "integrity": "sha512-KmiZwKNIbCQOwzrJVARdoJDv4xIR/YC5x/4s8cG1lT0GgoaP/86JbmJCiNw8XioLEe+Lva+XHTu7s+chKP31+Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHhaO2hRw/rsZXtmTzv7qgLdN3ssdn+uypYIP14iZcMjAiAXshAGzbYmTAGxcDjFRyPNt7mmYgn3QBFl3oFlBOC0LA=="}]}, "_from": ".", "_npmVersion": "1.3.7", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.0.3": {"name": "minimist", "version": "0.0.3", "description": "parse argument options", "main": "index.js", "devDependencies": {"tape": "~1.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@0.0.3", "dist": {"shasum": "a7a2ef8fbafecbae6c1baa4e56ad81e77acacb94", "tarball": "https://registry.npmjs.org/minimist/-/minimist-0.0.3.tgz", "integrity": "sha512-8PBeG2T4HJHs9Jm8Rpj/jkcYSS+O/S1UtRl640/kjJL1lrHBNef7VTuEOTPRIBbWIu4LTDWcHvIMf91F+U4Qgg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC6jlGzq/zje3qhFwnuIg0tj66iX6GEXt9NcurzPDQNAAiEA2r7VremWrUoVaP3sH2gwZEjZxsrUdcEABLWBdMDmbVM="}]}, "_from": ".", "_npmVersion": "1.3.7", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.0.4": {"name": "minimist", "version": "0.0.4", "description": "parse argument options", "main": "index.js", "devDependencies": {"tape": "~1.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@0.0.4", "dist": {"shasum": "db41b1028484927a9425765b954075f5082f5048", "tarball": "https://registry.npmjs.org/minimist/-/minimist-0.0.4.tgz", "integrity": "sha512-TyyxQAv56Turf5abLNriutoXxTdKIczShBppJFv+Qs9N1gKSznI0hzWEGqQ0mIloaizbwaJvS0gYeoXOnr5Srw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCS0MQq3MOG5rkz9IKwGJgvB3I6fiDCgotyfss/q6qyJgIhANpuWJUDI74H+NBuajXk2fNOXGP9Xo2J7IhLGbcxJ/N7"}]}, "_from": ".", "_npmVersion": "1.3.7", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.0.5": {"name": "minimist", "version": "0.0.5", "description": "parse argument options", "main": "index.js", "devDependencies": {"tape": "~1.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@0.0.5", "dist": {"shasum": "d7aa327bcecf518f9106ac6b8f003fa3bcea8566", "tarball": "https://registry.npmjs.org/minimist/-/minimist-0.0.5.tgz", "integrity": "sha512-rSJ0cdmCj3qmKdObcnMcWgPVOyaOWlazLhZAJW0s6G6lx1ZEuFkraWmEH5LTvX90btkfHPclQBjvjU7A/kYRFg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD+IRrZedbAVTqv1+0zJ2GpYDKd3FGf94tGwepA7czdCQIgAwelbWZRm2cHd884iYs7ZM2ZUGJJBRxS5JF1diILsIc="}]}, "_from": ".", "_npmVersion": "1.3.7", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.0.6": {"name": "minimist", "version": "0.0.6", "description": "parse argument options", "main": "index.js", "devDependencies": {"tape": "~1.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@0.0.6", "dist": {"shasum": "a515054dd7de651410a5511ef6b2d7fdcb596394", "tarball": "https://registry.npmjs.org/minimist/-/minimist-0.0.6.tgz", "integrity": "sha512-OInSo7C1gq9RQuB9nEe45kkDS8GjOd5JClKbhtzbsUbgP2fu4CeNJT8C2BdW2N/KCyV7/CxqNXTmX7P71zqAJQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCH9PPbr5tEb/Gif5HpBI6mhv0IL/2vs3OWM9JzxqoGvQIgUtFUU0rlUBEbQFLgccrjBMOVBEsIJJ9KdGjI6wf2tvw="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.0.7": {"name": "minimist", "version": "0.0.7", "description": "parse argument options", "main": "index.js", "devDependencies": {"tape": "~1.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@0.0.7", "dist": {"shasum": "dc4c620253c542eda0d2eb91c3c6a971a11e63e7", "tarball": "https://registry.npmjs.org/minimist/-/minimist-0.0.7.tgz", "integrity": "sha512-CKamsrP6RrNQOs7fuDkeMgdxThH9nh0CwRZCj6QO11AKmoa1sUlM0/KvvCyike3V04JpNw2vFLyal1LPl1ikEw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAt7zS5bMP8tVTSpric7pUdB7JRQpbxfW7jI0BbQlKnxAiBonA7X37UQE2yBbBaJXxRALBLJA1eLqdg162Z3Q7Nt9Q=="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.0.8": {"name": "minimist", "version": "0.0.8", "description": "parse argument options", "main": "index.js", "devDependencies": {"tape": "~1.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@0.0.8", "dist": {"shasum": "857fcabfc3397d2625b8228262e86aa7a011b05d", "tarball": "https://registry.npmjs.org/minimist/-/minimist-0.0.8.tgz", "integrity": "sha512-miQKw5Hv4NS1Psg2517mV4e4dYNaO3++hjAvLOAzKqZ61rH8NS1SK+vbfBWZ5PY/Me/bEWhUwqMghEW5Fb9T7Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDW7J/Us43W8sxeysKUFk4mJj4B8ZpWxTsPHSc3ma5YHQIhAK2j6GqOYWD1UcAb6yxa85VuFB1DhhfYHwW0pDeAcBcS"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.0.9": {"name": "minimist", "version": "0.0.9", "description": "parse argument options", "main": "index.js", "devDependencies": {"tape": "~1.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@0.0.9", "dist": {"shasum": "04e6034ffbf572be2fe42cf1da2c696be0901917", "tarball": "https://registry.npmjs.org/minimist/-/minimist-0.0.9.tgz", "integrity": "sha512-Zoa3x7tinSmKcQGt/xc7YRpA/ynhZ8KtIqO4bvwRQSGLDN3IpkRf9JNr78H+JTOs7ff1D7s90s46giEAvkAidA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEK94nt3jEJG1gk0og9jue8nvp3dG+iXoNSSgkJ1OnaSAiB4d/BaCQFwjYSncRvrA5HdD4igT3WpTyC/lgcc0VoAyA=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.0.10": {"name": "minimist", "version": "0.0.10", "description": "parse argument options", "main": "index.js", "devDependencies": {"tape": "~1.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@0.0.10", "dist": {"shasum": "de3f98543dbf96082be48ad1a0c7cda836301dcf", "tarball": "https://registry.npmjs.org/minimist/-/minimist-0.0.10.tgz", "integrity": "sha512-iotkTvxc+TwOm5Ieim8VnSNvCDjCK9S8G3scJ50ZthspSxa7jx50jkhYduuAtAjvfDUwSgOwf8+If99AlOEhyw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG05Q+A/9hVxtsMY9//9ZktB+0QzHxcXzc+SuVzv1UqvAiEAxDJhsSVDfcU4UZi620h/Bs2/E3FcTA+RSKtWVObYoe4="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.1.0": {"name": "minimist", "version": "0.1.0", "description": "parse argument options", "main": "index.js", "devDependencies": {"tape": "~1.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@0.1.0", "dist": {"shasum": "99df657a52574c21c9057497df742790b2b4c0de", "tarball": "https://registry.npmjs.org/minimist/-/minimist-0.1.0.tgz", "integrity": "sha512-wR5Ipl99t0mTGwLjQJnBjrP/O7zBbLZqvA3aw32DmLx+nXHfWctUjzDjnDx09pX1Po86WFQazF9xUzfMea3Cnw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEuhRK1Hug8YxJtvg5HhjpvQ4BrqKZeF40mJ3Jna10ogAiEAiDHAlkRzAW34fe+EHnGC+KjcqjNKc9/pJ9yosGl7EAM="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "minimist", "version": "0.2.0", "description": "parse argument options", "main": "index.js", "devDependencies": {"tape": "~1.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "gitHead": "f904dcc3c28f10eb11840091e9f0bab9d9519b5c", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@0.2.0", "_shasum": "4dffe525dae2b864c66c2e23c6271d7afdecefce", "_from": ".", "_npmVersion": "1.4.15", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "4dffe525dae2b864c66c2e23c6271d7afdecefce", "tarball": "https://registry.npmjs.org/minimist/-/minimist-0.2.0.tgz", "integrity": "sha512-BESHrmJFCIiBW/PR08JD7bdtBheVQMd9GcvLEq3ACsZU6X3IrvurZwO0xg7uy/U3nt3My6HzDjNWcT6iOygqXA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB6wR7gMqunfKWDp4CRmuIdCEobIn/EUvgSwzh8kW4j5AiASvq61yGtlcahVWeb2NmSaYhwLa56hYv7NGiATIRbuiA=="}]}, "directories": {}}, "1.0.0": {"name": "minimist", "version": "1.0.0", "description": "parse argument options", "main": "index.js", "devDependencies": {"tape": "~1.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "gitHead": "13077b369b092797bb176fd18131d07b0d6c2d1c", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@1.0.0", "_shasum": "9429c4f83e0d22590a76aeb610006dcc3f89c70f", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "9429c4f83e0d22590a76aeb610006dcc3f89c70f", "tarball": "https://registry.npmjs.org/minimist/-/minimist-1.0.0.tgz", "integrity": "sha512-13Y8cPHNMRHq8ZCgBIghxclG1jfu+pJ3UfksvUPJ20WvORiaIvwkLUX+XaM+1+ZtTgPrONs6AoG3iuC6MSAOnw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCMay1ncslmcM9DCvWca9We/M7ytahVYMVI1E/rZxjvXwIgZ5FexSBirbK1Tu+1D7yuqfALC/D8VsKKG5QXqBoVDuI="}]}, "directories": {}}, "1.1.0": {"name": "minimist", "version": "1.1.0", "description": "parse argument options", "main": "index.js", "devDependencies": {"tape": "~1.0.4", "tap": "~0.4.0", "covert": "^1.0.0"}, "scripts": {"test": "tap test/*.js", "coverage": "covert test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "gitHead": "e2563e462be40c344c6c65b7cde85091fe261976", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@1.1.0", "_shasum": "cdf225e8898f840a258ded44fc91776770afdc93", "_from": ".", "_npmVersion": "1.4.21", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "cdf225e8898f840a258ded44fc91776770afdc93", "tarball": "https://registry.npmjs.org/minimist/-/minimist-1.1.0.tgz", "integrity": "sha512-ozllOyYiayzEgHCQKMPXKkOn9QRdeVe0TrIxLp+SJXMA0XNCL+yf4OtyPkB2JthzzYePYOTRnipBi3oOOa82sw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD/3yU9AGO/nPdAK9t8brmU+KjAe5YRlW+GicOyjY6iFwIgasP9Pb1dCFeXuMD77mcGgq1Mk5RN9Vk2LB647XTuZ1Y="}]}, "directories": {}}, "1.1.1": {"name": "minimist", "version": "1.1.1", "description": "parse argument options", "main": "index.js", "devDependencies": {"covert": "^1.0.0", "tap": "~0.4.0", "tape": "^3.5.0"}, "scripts": {"test": "tap test/*.js", "coverage": "covert test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "gitHead": "bc9d1c466541eb52ae85e6682a4b809f4c32fe1f", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@1.1.1", "_shasum": "1bc2bc71658cdca5712475684363615b0b4f695b", "_from": ".", "_npmVersion": "2.3.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "dist": {"shasum": "1bc2bc71658cdca5712475684363615b0b4f695b", "tarball": "https://registry.npmjs.org/minimist/-/minimist-1.1.1.tgz", "integrity": "sha512-FzcUe2HULkO6NxOnADCRJos39lkw3Uy+i8hpVfHDrBK0fdbTLkeo6LveAY6dEJwoSxwB3z6MyQSOJDRZ6w9kvA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEaGmEXbStCIU28jllSyebi/5C0AoGfKSfyjqG1Ql5+TAiEAuP/LgJj2xn5dmVP5ZPn55HNZzDW2D4wfHTjowL/6IeM="}]}, "directories": {}}, "1.1.2": {"name": "minimist", "version": "1.1.2", "description": "parse argument options", "main": "index.js", "devDependencies": {"covert": "^1.0.0", "tap": "~0.4.0", "tape": "^3.5.0"}, "scripts": {"test": "tap test/*.js", "coverage": "covert test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "gitHead": "a8e2fe153a22dad7a0da67fd6465fab4cfa63e37", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@1.1.2", "_shasum": "af960b80caf71b38236352af7fef10a8efceeae3", "_from": ".", "_npmVersion": "3.1.2", "_nodeVersion": "2.0.0", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "dist": {"shasum": "af960b80caf71b38236352af7fef10a8efceeae3", "tarball": "https://registry.npmjs.org/minimist/-/minimist-1.1.2.tgz", "integrity": "sha512-bpx70wDUCusw0HkTxa8RWd54KPesD+n2kPy73T2828imCtDQEFa/4oNNKhxO0faRm5KxI+15uqX9teWUyl613Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD1QunRAI6ktbBgzj2wXq0/F0Yc17RErkCpFMi3YgfxtgIgA+9yG07yoGgQj7sRF2M3MVngKsi+GiM1SUu6+5GN1Yc="}]}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "1.1.3": {"name": "minimist", "version": "1.1.3", "description": "parse argument options", "main": "index.js", "devDependencies": {"covert": "^1.0.0", "tap": "~0.4.0", "tape": "^3.5.0"}, "scripts": {"test": "tap test/*.js", "coverage": "covert test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "gitHead": "8a5d94cf17fb8b126cf6c8fbf2a7713df76d16cd", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@1.1.3", "_shasum": "3bedfd91a92d39016fcfaa1c681e8faa1a1efda8", "_from": ".", "_npmVersion": "3.2.0", "_nodeVersion": "2.4.0", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "dist": {"shasum": "3bedfd91a92d39016fcfaa1c681e8faa1a1efda8", "tarball": "https://registry.npmjs.org/minimist/-/minimist-1.1.3.tgz", "integrity": "sha512-2RbeLaM/Hbo9vJ1+iRrxzfDnX9108qb2m923U+s+Ot2eMey0IYGdSjzHmvtg2XsxoCuMnzOMw7qc573RvnLgwg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDvTgFGcEU4OwZTxVcMXSOjBtd+uOLDpZidEU1VJoBKYAiBeN2MZKgItHryN4eif+T+QhKWbgNvAqh3uqsNSJg38qQ=="}]}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "1.2.0": {"name": "minimist", "version": "1.2.0", "description": "parse argument options", "main": "index.js", "devDependencies": {"covert": "^1.0.0", "tap": "~0.4.0", "tape": "^3.5.0"}, "scripts": {"test": "tap test/*.js", "coverage": "covert test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "gitHead": "dc624482fcfec5bc669c68cdb861f00573ed4e64", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@1.2.0", "_shasum": "a35008b20f41383eec1fb914f4cd5df79a264284", "_from": ".", "_npmVersion": "3.2.2", "_nodeVersion": "2.4.0", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "dist": {"shasum": "a35008b20f41383eec1fb914f4cd5df79a264284", "tarball": "https://registry.npmjs.org/minimist/-/minimist-1.2.0.tgz", "integrity": "sha512-7Wl+Jz+IGWuSdgsQEJ4JunV0si/iMhg42MnQQG6h1R6TNeVenp4U9x5CC5v/gYqz/fENLQITAWXidNtVL0NNbw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDlc+G9Ld9mHF1lQ6TZFhLSKJq55ztlnWLVWRA79BWI7QIhAKVKkW/bxg2Lq35pvORK11LqBI47CcjdqBxvvl69qGNi"}]}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "1.2.1": {"name": "minimist", "version": "1.2.1", "description": "parse argument options", "main": "index.js", "devDependencies": {"covert": "^1.0.0", "tap": "~0.4.0", "tape": "^3.5.0"}, "scripts": {"test": "tap test/*.js", "coverage": "covert test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "gitHead": "29783cdf94cc9a0663bb31f5eb9a4eff9c515bf6", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@1.2.1", "_nodeVersion": "12.16.1", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-MxgrjJPBtptee4aL2EExBK3tS4KUc1RtuRluaHZCmxKII8SBkvUP6z7XDmxEaUxdvpp++waKrqvSzuFXTRntVg==", "shasum": "f41818114b8d4be3c0a4febdf3c432b956680ece", "tarball": "https://registry.npmjs.org/minimist/-/minimist-1.2.1.tgz", "fileCount": 20, "unpackedSize": 30683, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeZ9cNCRA9TVsSAnZWagAAtWMP+wR5OEzH8TkYeKqLbQZn\nvDcc3Dppv/TiPzbcEkHj5Cg72TVv4o3zX5seBR8eKOTHA3l/hwbet+MemOtn\n8h81N/3iFmeV+qd0w1r9PxMRsLUbK1x83CV/QvYUiOTVuDiIBD4KUnaBhzZH\nQgjsyAWLAX2tDN0RHpMXQSG31Ya1Bu5mWJ/AQU//X8Fxy1HgiuI1PfN+6+Sz\nuMrFx7uxzLX6dF/BKZ/24OHzuMTz29qKdvv/l4PLECtQ+MYh4WTtFW8zKEzT\nXN7DW5stdThhbZnRfLae3eozVZZhCDogPNJ19HKGNinu6f7E6YdFAKtUEQf/\n/kP67e5NhZHYZnLkY6c3ATQa8QuRCs8c/rJftbmxYcze+8HnyYTu5JmkVgAn\nNcoW70e0S+DHXrMJ4DILPMmtuCyUmbaSMVTeeykjPGyouM85a0/pVP1oRVmJ\npTNBpWY/8H2XJiOPJjIY+86tYUmwyD9eL34xYbTWuB1qGm1q9lzcb862thnl\nTgy/HyE2kaLke12J0PH0NU9djftMyxVSnebttUOjRneTZ3U/Xf+U5BJH6oPA\nDLVPWCa3Jxq98E8KBlMdPK/xMBD8HLdTLoNUkNJamPiZxvUxJwkM++jph3G2\n1/Iw7MvrckGKhKU16c7qNocQXXk73vBHOq8kyefX7WUmDOEJcbcMlesmhF0u\nNOr6\r\n=99eC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCvcna22jj9EMPA5RN3J8UzmQvhBYu98S+/lsPmTmn/1gIgCfu/sPZPiyCseKv7414yqmpiy3kgCOi48vwSL6tTcwM="}]}, "maintainers": [{"email": "<EMAIL>", "name": "emilbayes"}, {"email": "<EMAIL>", "name": "substack"}], "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minimist_1.2.1_1583863564815_0.12688201316956627"}, "_hasShrinkwrap": false}, "1.2.2": {"name": "minimist", "version": "1.2.2", "description": "parse argument options", "main": "index.js", "devDependencies": {"covert": "^1.0.0", "tap": "~0.4.0", "tape": "^3.5.0"}, "scripts": {"test": "tap test/*.js", "coverage": "covert test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "gitHead": "f34df077a6b2bee1344188849a95e66777109e89", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@1.2.2", "_nodeVersion": "12.16.1", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-rIqbOrKb8GJmx/5bc2M0QchhUouMXSpd1RTclXsB41JdL+VtnojfaJR+h7F9k18/4kHUsBFgk80Uk+q569vjPA==", "shasum": "b00a00230a1108c48c169e69a291aafda3aacd63", "tarball": "https://registry.npmjs.org/minimist/-/minimist-1.2.2.tgz", "fileCount": 21, "unpackedSize": 30741, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeZ95cCRA9TVsSAnZWagAA56QP+gK14eqYLhwCvOl2CS/v\nJlCozvb1bOJROoCBu4gZRro830Z5Sx1L5NuMetqLyKd4hbeotDsrymXCgebJ\nS+gAQmtLXV3EGSwAgK0bHrI2+GrfOc70kx5lBTYwXXNVn6oZU6ONM+ERWYNs\ncf6I/ZZX424u8uE0086Q8Zp9fPp/qcf76p0P5zWJuaY+t7gUV2l3NDGXITaz\n7s9hfKKIX92++w44RAXDwoyXi7gb57mIKatSYplQCtQuIjISA0Lo7KIU7mhH\nvLHbzHIusgm6KQ4wdtpHNbcClF8kZIqdzJ4rCrWAT6/wKeDnrx+4kLzN+Xvq\nRA2qvb2xsN8/1g7qbu5elxNPuHwPjmjcf1ic2x0/mzmQEV/wqPHSDB7isT8U\ns4G2UPC9SztuTXf80Zd8K6SI0Qu8GzomhdV+9QMB5PbBvkGkVTjTOY3PRmdF\nxG74/RLGft9+iKKPIitmkDU8XXiKi2WJnB2pShtHDDTXQVlrDMxvD85uDzXL\nkqiohLXMp1brNCENQFKUBR7Cg405b/O5NBgNFxnPdHpFE7makXHhjiKAo3nX\nntDBKN3Px7+iKu4zvb9L9k9v3mj6YxGWRhMYemRv4ci2K6fGTj3Oz2zxNosp\nDKLU9RHkV+HCtRJloF3vj6EeIOLD7yPqk/KTJC4TtlwjoUznuejAPxi4ju6J\niyOA\r\n=cJCk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGfK0vSXEKFPqZZ5KeRNZEwVsmuJR975Kqi9zxZNZUkJAiEAuGE+r7SxO7XBBH3CTVkaEfuTKrCkOyptLlLpFEQ2/PI="}]}, "maintainers": [{"email": "<EMAIL>", "name": "emilbayes"}, {"email": "<EMAIL>", "name": "substack"}], "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minimist_1.2.2_1583865435605_0.11525894784863966"}, "_hasShrinkwrap": false}, "1.2.3": {"name": "minimist", "version": "1.2.3", "description": "parse argument options", "main": "index.js", "devDependencies": {"covert": "^1.0.0", "tap": "~0.4.0", "tape": "^3.5.0"}, "scripts": {"test": "tap test/*.js", "coverage": "covert test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "gitHead": "6457d7440a47f329c12c4a5abfbce211c4235b93", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@1.2.3", "_nodeVersion": "12.16.1", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-+bMdgqjMN/Z77a6NlY/I3U5LlRDbnmaAk6lDveAPKwSpcPM4tKAuYsvYF8xjhOPXhOYGe/73vVLVez5PW+jqhw==", "shasum": "3db5c0765545ab8637be71f333a104a965a9ca3f", "tarball": "https://registry.npmjs.org/minimist/-/minimist-1.2.3.tgz", "fileCount": 21, "unpackedSize": 31954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeZ+WoCRA9TVsSAnZWagAA4+EP/iTCVS055EBuEuRFMplv\n+3IvmNilogYEWtZpz6HF5mVJA1eksI3LrbcXEzJjd+C0sZ+eZwONxPN7AbEX\nAJ8kIlTQ3n/1qGCtpwnLtBd158tzyLBprxvtab3BMDO49hx8uSuedeOa8Fh/\nxGghAeLZZfPYqQQ9RTcgpFzPbvmN2CWNPeNT90a3E4mHlkcYRO/lWnz5IroI\n+5igofAC0foyr/lQqcXLO4jTreEt/6Lo153a0h8Tl6rW5sq1P7BB/4mGA21l\nZpvonhpMXrLL4eHCbVMPlS6GNISI9xvctp7J+CPNIy8S2FMZemYKb3EBkbBE\n4jUElnxyxdhNA6sZQTT1wryiqvD82qkCwPyDQOjT38tBaFf93+JaegsKR2gX\nr0X7t1w9IgEVuJJ6rYKh41w5R4f/H7wV9I87xW1IlhsFLSq4+F0bHb5xYk2P\naWqEl5n09yQ4a5VLYOwWn15QHVIJ8x5tB2puNrm1kzq5GHNw8tMKoRAmExCP\nvlgi1S4ymbWvZc8HVjQDkWNVkWDZZKPsd6hsTwLURnbicoXJbEAo0jPAc+/M\nQBah70YQ7bqsEk9E9iDPPVibB8TiM4ZLr5lvk2wAG9sjROjYSZMCmtJZN7tv\nq5tXp2BgYQIyCA4TqAn0nk+IaXgzRjRKeDOOwhbrdcEVwp2YfjWe1n4Uq/5G\nqsro\r\n=WPB0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDFA4AL8i4HTPwCeftoQ5Zc3COcQuhnT/oFRUnZnyNehQIhAK68JAIEf7vL8QaGjQGvYJk8bH7ZaG4udg+hYptPZP2A"}]}, "maintainers": [{"email": "<EMAIL>", "name": "emilbayes"}, {"email": "<EMAIL>", "name": "substack"}], "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minimist_1.2.3_1583867304504_0.*****************"}, "_hasShrinkwrap": false}, "1.2.4": {"name": "minimist", "version": "1.2.4", "description": "parse argument options", "main": "index.js", "devDependencies": {"covert": "^1.0.0", "tap": "~0.4.0", "tape": "^3.5.0"}, "scripts": {"test": "tap test/*.js", "coverage": "covert test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "gitHead": "278677b171d956b46613a158c6c486c3ef979b20", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@1.2.4", "_nodeVersion": "12.16.1", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-wTiNDqe4D2rbTJGZk1qcdZgFtY0/r+iuE6GDT7V0/+Gu5MLpIDm4+CssDECR79OJs/OxLPXMzdxy153b5Qy3hg==", "shasum": "40357ef9582f8cd42ba8eaa274ddcf27f0c979b7", "tarball": "https://registry.npmjs.org/minimist/-/minimist-1.2.4.tgz", "fileCount": 21, "unpackedSize": 32384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeaTsaCRA9TVsSAnZWagAAOMgQAJylxIJH+awQLgWzP9Ik\nmbQvKz5Bk7E+keg0vGA/ecxMspZGZiTsXnO72JL0+DFpXxbdcs04uXm822MS\nizWri8HZrdJwQqb/E6ZnqSdKij+C6YXM3yUutKdeN56h5dAZNiZotsyVdHcu\nmhpAf0AHyYhG9cS2Gz+wNCQDTpz6HZ3lqvxonLsotTHsp6i1JyQaBygq+kj9\nnCs/rvxpBnACjHJmtLmlrMcRZL8FiwhOYJp5vUA8TNcRFF/FHCsXRo2UGyzr\nbFDXysU9tQIe5Omt47ZAwLw1m3xu37S6DK8ABewWA8JE6twmsk65HXQq7ea1\nHRiQs6fdMieUeOy4083sgsejsWSsB1d4tlkdeOjW0nFfkVlDJoocU90AIcD9\nZac17pFUsVmmhwCeEtoR9SQ7sYgTTFtdE/YOeoN7z0AhjJ+OW54qPoEC2neM\nQvd+w7KioIHNwivH5JkMgE43WJFwxnNlFqRMMtrQzCxYTk9QqSRYqzblv5lG\na2kHMaW82dMZzp8d7wL1JadtA+lVwz+P2fspMCsgiFc8jnci4r9z+A76fj4W\nW94AmC8mZ6IVaaLgRVZnRmWRoEbjuv8whQf3hneQ+XwnMakBRzkMJUfs8XEI\ndSD4GTqgoikn4qrOTh0bNp6pqZfSJQspHnfDQFIa/LWfqlI2en+RC+AXcDKC\nIgE+\r\n=oj4i\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICAeuYkRod1FNzIbRELsZG+EcxSD/+T2zAftUJ4r+3JRAiA+WtRKzpXvkxTANxFL0k8EA3/7iwtrABd67LzM3b0DNg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "emilbayes"}, {"email": "<EMAIL>", "name": "substack"}], "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minimist_1.2.4_1583954714023_0.45862116307064804"}, "_hasShrinkwrap": false}, "0.2.1": {"name": "minimist", "version": "0.2.1", "description": "parse argument options", "main": "index.js", "devDependencies": {"tape": "~1.0.4", "tap": "~0.4.0"}, "scripts": {"test": "tap test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "gitHead": "a0489cb0120308a235ea8b8a8ee1d7635a065c6a", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@0.2.1", "_nodeVersion": "12.16.1", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-GY8fANSrTMfBVfInqJAY41QkOM+upUTytK1jZ0c8+3HdHrJxBJ3rF5i9moClXTE8uUSnUo8cAsCoxDXvSY4DHg==", "shasum": "827ba4e7593464e7c221e8c5bed930904ee2c455", "tarball": "https://registry.npmjs.org/minimist/-/minimist-0.2.1.tgz", "fileCount": 18, "unpackedSize": 25782, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJearRoCRA9TVsSAnZWagAA3DgP/A7mSWMTpBKTc8MQ9jt6\n1Q1DL0Eoi16FJrFuxwhVPQyVjSvsq1mMdb34hWkS8ZTuFFo6HrL9SFzjLoBa\nkYk+V6eZD8qqCKdQz2HK/q+EFBVysZz/7S1WwNgrx947fEXhfu4A0khcq4AA\nvur3ZSNkfg0P7Dn+CbKX7sUMdkjLL949tH9R2Bs60JOj1ywsXFB3+yz6yMy2\ndtgjpjIh3qK86vWHQ626RpWcnvOrU8hiTvZpTEeKIx5xdwCaSfiQDaGmGRj0\nnwDd9i9y6bIZXav01/L+gIYhlfg/xCK3jBbLETozYRQv7pNaA7Qh15c1PTHW\n1QGS5/VChuwp6AGcFkM9XDnOBEK/xn+1gqGYYnEnGr8iRdBmNjCyQaUSy31J\n1FA3nb91fW6JvUsmKYRlBP/O38mnx4m40gP8SLUnSllJkOrZ0+aq5DG+c+/X\nl5j257jCUFtdnvGn7LEKU5qO9m8jehzLP4fUmn7kD7d2BfNPzIwY6jnfCxvh\ntT5nEEl8WZaQo5sADKu48jJbluZSj+bD8aGc4Qf7l+qWXQKlrRj8ZSO/bFyY\nVhB3MzR0P6UO9Wu7rWbf6Fu5+kdRBWZ0KjmJSBAW2BMw54s2TZ7MvQ0tJJFi\nuC8OEDzaNSwB0dcOmVRasgW8IIplWbj+JveQ+XW2sfyJ92yFfOoreZY6TQJY\n67eN\r\n=fsdQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCbbl5TXQ3aZIc4rfiebndw4ua98gJ5Ax332V3tB9r1bwIhAPw/gDuupAonHc317wlUsTqiQgiZtUKT++hFHOSTMrxb"}]}, "maintainers": [{"email": "<EMAIL>", "name": "emilbayes"}, {"email": "<EMAIL>", "name": "substack"}], "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minimist_0.2.1_1584051303528_0.5927484925906972"}, "_hasShrinkwrap": false}, "1.2.5": {"name": "minimist", "version": "1.2.5", "description": "parse argument options", "main": "index.js", "devDependencies": {"covert": "^1.0.0", "tap": "~0.4.0", "tape": "^3.5.0"}, "scripts": {"test": "tap test/*.js", "coverage": "covert test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "gitHead": "aeb3e27dae0412de5c0494e9563a5f10c82cc7a9", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@1.2.5", "_nodeVersion": "12.16.1", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-FM9nNUYrRBAELZQT3xeZQ7fmMOBg6nWNmJKTcgsJeaLstP/UODVpGsr5OhXhhXg6f+qtJ8uiZ+PUxkDWcgIXLw==", "shasum": "67d66014b66a6a8aaa0c083c5fd58df4e4e97602", "tarball": "https://registry.npmjs.org/minimist/-/minimist-1.2.5.tgz", "fileCount": 21, "unpackedSize": 32384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJearSzCRA9TVsSAnZWagAAdXQP/37gxveGMGnYPLHEtnLE\nxGoFFRWU82yPya40/uMK45UZl5S48tCIJF+QgifQl4dnRJ0pvPBobZtovjQ3\n9kD9E9my/JJEvytLoM3rIhyaMpfpr68zABVYA8MUVUHsDOIjn6HVr1UR6B7L\nj8AfsLdtmO6Sy6nb8t5TfBuDprsLbGCKqXyUXWOMOJc/9fQN5PEKMJ9bZa2d\nT5gcJ3wxWKJYRt4Wiv6ddQ5xN+4Tnc7pA9yqxz73bn6aaRgLq3c2vam0leGE\nxjVC9OX8riWiPVMJNUqGwC7qa8u59QFos1TZQ8HpBiJLP9L33UdHcYbDNAoZ\nRSSBw3gM3H+eRWIN369POOO8AFrdbAGovSmWcS9RFLDjSm8rWmB+Jx22Z1Na\nc54umOQDXuuAS8TbWsVyLvjKd+UwxHRc0h8e/gMQO2AGFQzTlJAgarCanTrz\nZBAcjJNsE6MKANRqWHobiE38viQOOGqVnCgQ5L4CwcMI7BMH7MFc4ntMLVIO\nxZPbBXHzYi5dfWucd8C11ZTN5Id4D+mdRZbUe8jePZ0WsD/xjABokZuhZziW\n90inv759CQn4JjnDKPQrZlBt+/6L3a+QQHhmNf+WWFKKPdDIpSZ395Kxmqov\nVLunRv2RigRd0r7GQGthStV+sxjCmy/ZgV56PpeFj1j1d/GvUu6/jYwG+J8X\nMbJT\r\n=myam\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHwhJJC96df5g2uJ/Z2O3bWpeoTVR+vobC7mpr670NCDAiAHG/cChsUv606ls0GVUuWTa2OfWnRtykvXmtBPRCe3Cg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "emilbayes"}, {"email": "<EMAIL>", "name": "substack"}], "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minimist_1.2.5_1584051379289_0.11145357496686259"}, "_hasShrinkwrap": false}, "1.2.6": {"name": "minimist", "version": "1.2.6", "description": "parse argument options", "main": "index.js", "devDependencies": {"covert": "^1.0.0", "tap": "~0.4.0", "tape": "^3.5.0"}, "scripts": {"test": "tap test/*.js", "coverage": "covert test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/substack/minimist.git"}, "homepage": "https://github.com/substack/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "gitHead": "7efb22a518b53b06f5b02a1038a88bd6290c2846", "bugs": {"url": "https://github.com/substack/minimist/issues"}, "_id": "minimist@1.2.6", "_nodeVersion": "16.14.0", "_npmVersion": "8.3.1", "dist": {"integrity": "sha512-Jsjnk4bw3YJqYzbdyBiNsPWHPfO++UGG749Cxs6peCu5Xg4nrena6OVxOYxrQTqww0Jmwt+Ref8rggumkTLz9Q==", "shasum": "8637a5b759ea0d6e98702cfb3a9283323c93af44", "tarball": "https://registry.npmjs.org/minimist/-/minimist-1.2.6.tgz", "fileCount": 21, "unpackedSize": 33202, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiOTl2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHWQ/+M1DlrfJO6t4u34gsO6W25Ya5eyBICPBYhHGX7XHY6WAfMK54\r\nmXp06/beXLmXEExDbyYsJVeC5TX5pLQyOpi0sAJq377SR8DR2V2biVOLDiFD\r\n5lcbE04h3ccz0/J5m3dLuEzbVwi0sTJeDTAVYkBheCWhotA2RKK/4x/3Cm2v\r\n8fLvX9i9l/mAlJl2V2E9u+0oQnUBqWOtP0zbUBNe2rR/cGld6OkxxETBbsti\r\nmSEJXI36XBQ1rRQaJ5AKlnCs2ooCf8nSDWWcV4QiH0GukQRC1d7QwzCUPAFI\r\nEjMGqbIxYQbks1t9KLLheGKLXJM763NWPNU8456WJjmAbogMK75mlyC8CMSn\r\nW2EPNhEhWbfhJ7G2Kz1J0i5HPUD1REX5mean3hm6w1CJFi30lmAya9JAMvvv\r\n0ce+Xzk3CUzXn/knh+faKGIrAZELT8CnnGo0bvvSNv/UL9Z+MrtigEX1AJRS\r\nBavdDxjttMcGgdYcsCpchbcgp5sVXxT20tzakGJphkdDypQJ0QiueZKUVkIs\r\njOCfeHBIUV8Pft0LiJv5r2Xa4KbGgRcd8eUdGAAXBT++6o5cUtnnjbAlqPNP\r\nuf8DyBD/NJFd/C2Wk7m5PJkG5IjP7zErmJLbBaifeYcXTQsH2NNf+AzJh6Lx\r\n12omUQn4FF6Wk8D6cdBbhK2iPyobfxWy80o=\r\n=hKft\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC41Vp3EYBOR5hTVEP3OyA25LtgG+XqzaT1qv9TEC4BrQIgejxoLAjHu8BP1g3bwVpoFNw1lrht20mygUgDWMHpr04="}]}, "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minimist_1.2.6_1647917430186_0.9031899943518615"}, "_hasShrinkwrap": false}, "1.2.7": {"name": "minimist", "version": "1.2.7", "description": "parse argument options", "main": "index.js", "devDependencies": {"@ljharb/eslint-config": "^21.0.0", "aud": "^2.0.1", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.6.1"}, "scripts": {"prepack": "npmignore --auto --commentLines=auto", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "nyc tape test/*.js", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/minimistjs/minimist.git"}, "homepage": "https://github.com/minimistjs/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "gitHead": "c590d75b741a12b5423e2b299f38a7f7c7d25a18", "bugs": {"url": "https://github.com/minimistjs/minimist/issues"}, "_id": "minimist@1.2.7", "_nodeVersion": "18.10.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-bzfL1YUZsP41gmu/qjrEk0Q6i2ix/cVeAhbCbqH9u3zYutS1cLg00qhrD0M2MVdCcx4Sc0UpP2eBWo9rotpq6g==", "shasum": "daa1c4d91f507390437c6a8bc01078e7000c4d18", "tarball": "https://registry.npmjs.org/minimist/-/minimist-1.2.7.tgz", "fileCount": 24, "unpackedSize": 50743, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCoHzpirTifq05bkv27zGMkGUy8+zGLhMPwraAsWuimaQIgRjOJLaMTmmUET0gIrNKY3OsGTznXTSpR7XUokIoDKaU="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRMUwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqVmw//crUOeiwaoCV6oQdvP0iwXRXu2tig8B18RThVx8tq7RMFDXxQ\r\n18JbLz/puqiaAqpVj5NoNNqwSphBsKT1blred5B7BTvjA6PZ0oNoAt0mX75K\r\nZc61T9F1ORDUksHn/Oz5ZJAj6mKSxWtbwX/FvGRarOJaU0CVGNs+0rwtzaD0\r\nr+0Nh66lNh9HJ8btepvp5WQW0qRAN96bN91wXPkxg3y38Y1hsC0tepm8EghD\r\nJq8AIe3oLMu1+TsHhGXTYxrNihfyRFYi1TGibewYpqlDoT8gcYmJtVQ3ophr\r\ng5srTKt9rA3lNENAPModCpGp1sHr/JaEKuHlcJlnJjJuSUK6ASCptY/4oO+Q\r\neyZNfeCI0+gaHWjTqqFr8VMlFO99MhHfC1+swqcZwi0b0izJWm0M91NgonJP\r\nPUbovszUbYKLth7RP6/UMGuf/a/GGXqpchVuVfIsP72DcUpihb2ItkSEj9oY\r\n+dhY59Zj7+e4hUG5aow05UBOJ717lIq44kv3xTymFQPZRTHek+R41gIdX0d0\r\ngaZVKVoIb9Qq3QFcMOpll8E5tDfqnU0Zhuo5l0aO4F7KguSNNfybPnmjjrYy\r\nqryciJMJcczqim+4xL0SG6PCelcj71sHZCSkyMCZWenhX2rtBavGVhue0CGC\r\nuH2wqiD4Vr5UlHGSTpLbv4KAPzQxZ7M5Nbw=\r\n=uJA+\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minimist_1.2.7_1665451312125_0.2459601464024852"}, "_hasShrinkwrap": false}, "0.2.2": {"name": "minimist", "version": "0.2.2", "description": "parse argument options", "main": "index.js", "devDependencies": {"@ljharb/eslint-config": "^21.0.0", "aud": "^2.0.1", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "in-publish": "^2.0.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.6.1"}, "scripts": {"prepack": "npmignore --auto --commentLines=auto", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "nyc tape test/*.js", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/minimistjs/minimist.git"}, "homepage": "https://github.com/minimistjs/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "gitHead": "42635cd848481bdb3adca5fbc705a686d6e071af", "readme": "# minimist <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nparse argument options\n\nThis module is the guts of optimist's argument parser without all the\nfanciful decoration.\n\n[![browser support](https://ci.testling.com/substack/minimist.png)](http://ci.testling.com/substack/minimist)\n\n[![build status](https://secure.travis-ci.org/substack/minimist.png)](http://travis-ci.org/substack/minimist)\n\n# example\n\n``` js\nvar argv = require('minimist')(process.argv.slice(2));\nconsole.dir(argv);\n```\n\n```\n$ node example/parse.js -a beep -b boop\n{ _: [], a: 'beep', b: 'boop' }\n```\n\n```\n$ node example/parse.js -x 3 -y 4 -n5 -abc --beep=boop foo bar baz\n{ _: [ 'foo', 'bar', 'baz' ],\n  x: 3,\n  y: 4,\n  n: 5,\n  a: true,\n  b: true,\n  c: true,\n  beep: 'boop' }\n```\n\n# methods\n\n``` js\nvar parseArgs = require('minimist')\n```\n\n## var argv = parseArgs(args, opts={})\n\nReturn an argument object `argv` populated with the array arguments from `args`.\n\n`argv._` contains all the arguments that didn't have an option associated with\nthem.\n\nNumeric-looking arguments will be returned as numbers unless `opts.string` or\n`opts.boolean` is set for that argument name.\n\nAny arguments after `'--'` will not be parsed and will end up in `argv._`.\n\noptions can be:\n\n* `opts.string` - a string or array of strings argument names to always treat as\nstrings\n* `opts.boolean` - a boolean, string or array of strings to always treat as\nbooleans. if `true` will treat all double hyphenated arguments without equal signs\nas boolean (e.g. affects `--foo`, not `-f` or `--foo=bar`)\n* `opts.alias` - an object mapping string names to strings or arrays of string\nargument names to use as aliases\n* `opts.default` - an object mapping string argument names to default values\n* `opts['--']` - when true, populate `argv._` with everything before the `--`\nand `argv['--']` with everything after the `--`. Here's an example:\n\n```\n> require('./')('one two three -- four five --six'.split(' '), { '--': true })\n{ _: [ 'one', 'two', 'three' ],\n  '--': [ 'four', 'five', '--six' ] }\n```\n\nNote that with `opts['--']` set, parsing for arguments still stops after the\n`--`.\n\n# install\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm install minimist\n```\n\n# license\n\nMIT\n\n[package-url]: https://npmjs.org/package/minimist\n[npm-version-svg]: https://versionbadg.es/minimistjs/minimist.svg\n[npm-badge-png]: https://nodei.co/npm/minimist.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/minimist.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/minimist.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=minimist\n[codecov-image]: https://codecov.io/gh/minimistjs/minimist/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/minimistjs/minimist/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/minimistjs/minimist\n[actions-url]: https://github.com/minimistjs/minimist/actions\n", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/minimistjs/minimist/issues"}, "_id": "minimist@0.2.2", "_nodeVersion": "18.10.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-g92kDfAOAszDRtHNagjZPPI/9lfOFaRBL/Ud6Z0RKZua/x+49awTydZLh5Gkhb80Xy5hmcvZNLGzscW5n5yd0g==", "shasum": "2592640f29a3e60863175b95b48c9e51f5050afe", "tarball": "https://registry.npmjs.org/minimist/-/minimist-0.2.2.tgz", "fileCount": 21, "unpackedSize": 45380, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDQoRB8r7QZ1MNySZpoLRayV3E8Ybm1zX0L+/JLtZVR3gIhAPraLiOon5Zj9oXOHtT3bneZKhGFrw24R/GDqQTQD/Ua"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRdT5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+aA//ZhFxXbpWOBjhpzNGgUUgqQplsZVz4WVKuTOhtnE1zVuxTx9g\r\nVRVfNOk79kn5ne+mIt2w/CzClEw8FQqkS2TwL2bTGhnEtFUTj3fD5bpnWl8t\r\n5m6iyo3zfiGf92GfrHbIRqZuVxax9p35AHjbsmG+0hxFegXAL9cy4zEdd9qL\r\nqn30TNEq+AJMuOQ/13HLN5sku9yzXYmVTXClxFZV/4r+PjvmJOxvfzzYc4+n\r\nA+2DosaK/vu9d1496q7Yatk1mtWjdU4DcGDHXujf2pKe6ABVk18Vt/cLokXL\r\nFOiU245lYgbxZyZvb1N1Ct1rSpjpglSInpJBbQ7ycQ41mlC00Y29FVmLTEbQ\r\ncFgZrodJqXswC25+1RTqe55KW/UG6bMGeRxe9kwMheGvgLJdBQ6YIKYqnn2j\r\nojNr1x8ftLceEBjKVCS9cMkBktgUuJK4vLGJMG1BNTFg/hlnMUlKJBFRgXEW\r\np/aMvn7VWRo/jss3IwL61NifPE2+GCBc3K9RovymfWbZRn04wtVqSJjMS8Sb\r\nMcoXN0j5Vd+TyXDQ0g9IQd54UmcBYG2vIKx6Dhfw+Rzl66FfcSTUCvgJUU/j\r\npPYtuJUnkc9y1fuR8s+8Vode9KmZYcQXMsUPq0DHB5dDM8g2VZiN8ZuEUJx9\r\nKJuaDqu04Ubk5RomkImbBeLHrP8n+jRvETk=\r\n=H36/\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minimist_0.2.2_1665520889256_0.9270846351780562"}, "_hasShrinkwrap": false}, "0.2.3": {"name": "minimist", "version": "0.2.3", "description": "parse argument options", "main": "index.js", "devDependencies": {"@ljharb/eslint-config": "^21.0.1", "aud": "^2.0.2", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.6.1"}, "scripts": {"prepack": "npmignore --auto --commentLines=auto", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/minimistjs/minimist.git"}, "homepage": "https://github.com/minimistjs/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "gitHead": "c0b26618322e94adea26c68e613ef0be482c6c63", "bugs": {"url": "https://github.com/minimistjs/minimist/issues"}, "_id": "minimist@0.2.3", "_nodeVersion": "19.6.0", "_npmVersion": "9.4.0", "dist": {"integrity": "sha512-cdOrRjzm/cI4sG1c1Kzgo5kpFQm61wrgADF89L2ONgCqlwWNCJ3L4DoOLamFIagKhdnRuC+4eWgdRB4OoibyuQ==", "shasum": "1a6e35403dd242d48ce38791aa910dee140f3e1c", "tarball": "https://registry.npmjs.org/minimist/-/minimist-0.2.3.tgz", "fileCount": 21, "unpackedSize": 43335, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHWGKs+tinElzwecsaAQpIECUL7wiiYLz/eEQ7DNJxOcAiEA9m0qyjc7u8m098ojFm+aXqnz/7I6ZN5TtVCPnziNJf4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5VNlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrIxg/+JAgposqq0wjRXyKOjjo5BiQaurIXCVltH4hTJ7OKko0HjjUo\r\nRxRlnI1fPtUvQaDCY0hA7kvcTs96uJ38UL925o2cUkSWvbY2BaWYS7eoqIBX\r\nLqfLErdA/ga5ept+NU6zs8o6tSXaw+MRROlhDgi4ikcu1SyA2H1F4Ct71sbj\r\nwLhxj6L0z5KC1wb2HS0CiWvwx3PtoMULuUmP80YZ1Fxpc43rxADLHGM1zIhh\r\njtzLIQkWMjKQ4KhLhi96YPWB3aIfPWwz6MYRYmRL+1VPrP/Wwww6dAkL2cR1\r\nllVE2Y1kmYGbfFCWPSC0FYVQJwP2Duwum/Qkv6Y+4k+ggadmWIuecs27ROxl\r\nNrYimiPRcI5j8b06j8bazCPDa0hTdHqIU3JKNQxV4ee4dvWFqH2aeeMqKxwn\r\nge4SYdQyx8Z2iEXKS3lOcVuac/2yH3bDNLUCMM5XwNSEg0W+Y9YtikSAoj5L\r\nQZE+vRqZGFQLkOj9l8wBY+iVQx0cZccGNudR5Cuet0SmqILeb5zrCryIhUsw\r\n6ri8ACJPy6cTJtnysZtBu0Q9Uhs6VIliCPlE2ZTWevWWaRfxcAFWCj6G63rP\r\nN8YNPfd4nwQ3lb4dx/e3KAFUqCY2eYbuGZNjdtms/4NUDqN/g9R09HuxjsyG\r\nSz3zEGNA9YSu2r9kDbNaPla8EkFidlMtD6E=\r\n=ccqV\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minimist_0.2.3_1675973477423_0.20868381219389942"}, "_hasShrinkwrap": false}, "1.2.8": {"name": "minimist", "version": "1.2.8", "description": "parse argument options", "main": "index.js", "devDependencies": {"@ljharb/eslint-config": "^21.0.1", "aud": "^2.0.2", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.6.3"}, "scripts": {"prepack": "npmignore --auto --commentLines=auto", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/minimistjs/minimist.git"}, "homepage": "https://github.com/minimistjs/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "gitHead": "6901ee286bc4c16da6830b48b46ce1574703cea1", "bugs": {"url": "https://github.com/minimistjs/minimist/issues"}, "_id": "minimist@1.2.8", "_nodeVersion": "19.6.0", "_npmVersion": "9.4.0", "dist": {"integrity": "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==", "shasum": "c1a464e7693302e082a075cee0c057741ac4772c", "tarball": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "fileCount": 24, "unpackedSize": 54477, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE/uB8pUkpXsJIGjGPJIRRIM/9D62XpwJl6Yg3btMkeXAiA/cv8nZVixZjGdNXEgMX11f/N/2VXu+o5woeX5b9tn2A=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5V7FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrRgQ//UOnz6KMAn81WnyW7HHaDJod7K1B/7esKtuUjltRxB2l8ddE/\r\nY0hTaRokSzDACo9p+x1OK/1d6OuCsoOlIn2ypAdGbftaTdDSWLJ0empTGXeq\r\n4Eh6+ND9/JF4H6293+zV0feEJ29EiBgI2x9izYeo/1qGufQwszGL6JNNl0OV\r\nvtJz1xJWnYyWKDSUoQhsf3C5GAqCUW4GDS8/XI8ZsaV6UTRJl/kawFGX0glC\r\nc2NZtzvoKWduVyBa5XKAGyq4x1lGCDDHGwi8pt0Dt32Vm8eci9nDBc8yvRUv\r\nTBAjRR8FkjGKNSAyR+3WU54CLyFMWCZcCaMtFBLrstR1YDP+S1ICDku6yy8I\r\nt4Ir+HAswSkimWnMh/ehvzKQIDOw2po5QEIlS6ZZyavWFZaHCAGbJb+RbCbV\r\nw6v1lQL8edEumEM5oJSJ5rYR+YfkWbfWRf+x3GAaqv1lSgMb1Ujen704KcNy\r\nXMSPsYh7yuO+Vmr9Zyith0XLsBpd/aHmrQoTW9tdic/xS0RTC1g7sk5BrpNd\r\n9ywPe0Va5F2nC22dBalXzI6qgdLBOdOC4A5IdpUoH0IivdBirifW8ncOAIy9\r\nIJaF5ZmslIf2kLuq2+mhc3X6Z7cjBqug58whq2AEYeCRop1POtTZ4ihmGkI0\r\nkaVMSKOyvBcFQzpShsO8Ajsn9O45p5TJ42A=\r\n=h76k\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minimist_1.2.8_1675976389053_0.6943044069255115"}, "_hasShrinkwrap": false}, "0.2.4": {"name": "minimist", "version": "0.2.4", "description": "parse argument options", "main": "index.js", "devDependencies": {"@ljharb/eslint-config": "^21.0.1", "aud": "^2.0.2", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.6.3"}, "scripts": {"prepack": "npmignore --auto --commentLines=auto", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "ff/5", "firefox/latest", "chrome/10", "chrome/latest", "safari/5.1", "safari/latest", "opera/12"]}, "repository": {"type": "git", "url": "git://github.com/minimistjs/minimist.git"}, "homepage": "https://github.com/minimistjs/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "readme": "# minimist <sup>[![Version Badge][npm-version-svg]][package-url]</sup>\n\n[![github actions][actions-image]][actions-url]\n[![coverage][codecov-image]][codecov-url]\n[![License][license-image]][license-url]\n[![Downloads][downloads-image]][downloads-url]\n\n[![npm badge][npm-badge-png]][package-url]\n\nparse argument options\n\nThis module is the guts of optimist's argument parser without all the\nfanciful decoration.\n\n[![browser support](https://ci.testling.com/substack/minimist.png)](http://ci.testling.com/substack/minimist)\n\n[![build status](https://secure.travis-ci.org/substack/minimist.png)](http://travis-ci.org/substack/minimist)\n\n# example\n\n``` js\nvar argv = require('minimist')(process.argv.slice(2));\nconsole.dir(argv);\n```\n\n```\n$ node example/parse.js -a beep -b boop\n{ _: [], a: 'beep', b: 'boop' }\n```\n\n```\n$ node example/parse.js -x 3 -y 4 -n5 -abc --beep=boop foo bar baz\n{\n\t_: ['foo', 'bar', 'baz'],\n\tx: 3,\n\ty: 4,\n\tn: 5,\n\ta: true,\n\tb: true,\n\tc: true,\n\tbeep: 'boop'\n}\n```\n\n# methods\n\n``` js\nvar parseArgs = require('minimist')\n```\n\n## var argv = parseArgs(args, opts={})\n\nReturn an argument object `argv` populated with the array arguments from `args`.\n\n`argv._` contains all the arguments that didn't have an option associated with\nthem.\n\nNumeric-looking arguments will be returned as numbers unless `opts.string` or\n`opts.boolean` is set for that argument name.\n\nAny arguments after `'--'` will not be parsed and will end up in `argv._`.\n\noptions can be:\n\n* `opts.string` - a string or array of strings argument names to always treat as\nstrings\n* `opts.boolean` - a boolean, string or array of strings to always treat as\nbooleans. if `true` will treat all double hyphenated arguments without equal signs\nas boolean (e.g. affects `--foo`, not `-f` or `--foo=bar`)\n* `opts.alias` - an object mapping string names to strings or arrays of string\nargument names to use as aliases\n* `opts.default` - an object mapping string argument names to default values\n* `opts['--']` - when true, populate `argv._` with everything before the `--`\nand `argv['--']` with everything after the `--`. Here's an example:\n\n```sh\n> require('./')('one two three -- four five --six'.split(' '), { '--': true })\n{\n\t_: ['one', 'two', 'three'],\n\t'--': ['four', 'five', '--six'],\n}\n```\n\nNote that with `opts['--']` set, parsing for arguments still stops after the\n`--`.\n\n# install\n\nWith [npm](https://npmjs.org) do:\n\n```\nnpm install minimist\n```\n\n# license\n\nMIT\n\n[package-url]: https://npmjs.org/package/minimist\n[npm-version-svg]: https://versionbadg.es/minimistjs/minimist.svg\n[npm-badge-png]: https://nodei.co/npm/minimist.png?downloads=true&stars=true\n[license-image]: https://img.shields.io/npm/l/minimist.svg\n[license-url]: LICENSE\n[downloads-image]: https://img.shields.io/npm/dm/minimist.svg\n[downloads-url]: https://npm-stat.com/charts.html?package=minimist\n[codecov-image]: https://codecov.io/gh/minimistjs/minimist/branch/main/graphs/badge.svg\n[codecov-url]: https://app.codecov.io/gh/minimistjs/minimist/\n[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/minimistjs/minimist\n[actions-url]: https://github.com/minimistjs/minimist/actions\n", "readmeFilename": "README.md", "gitHead": "8c6be4872b7f49318337223f7099497c63d808d8", "bugs": {"url": "https://github.com/minimistjs/minimist/issues"}, "_id": "minimist@0.2.4", "_nodeVersion": "19.7.0", "_npmVersion": "9.5.0", "dist": {"integrity": "sha512-Pkrrm8NjyQ8yVt8Am9M+yUt74zE3iokhzbG1bFVNjLB92vwM71hf40RkEsryg98BujhVOncKm/C1xROxZ030LQ==", "shasum": "0085d5501e29033748a2f2a4da0180142697a475", "tarball": "https://registry.npmjs.org/minimist/-/minimist-0.2.4.tgz", "fileCount": 21, "unpackedSize": 48940, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDC8bKV5QnJnci7N9gRT4Dxzt7loH1NysMhDbpNpiR3/gIhALl3ggxiPPWDaxPjy2CopwK3uiqjKAWgBOflYTVGulCX"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+ageACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpoZw/9Hi0sCVeQtVDQatxiwswU3fAhx1z6n02WfQVy0snRCY35d7Jp\r\nWMNkesM5exIUtm3g+v8pSk13UKEqw1SFyBBwVi26DJhQVeNUnaEX/q4nPrHF\r\nb0axV/FGCKhNrkiJpb0a1K1/vqFybnoq3X2QY4P7ALmgGTX8Dm/64KLpMZEP\r\nk2UDNuTnNRnjeWdO6mO6CKzhhvYfeeT+Wl5UAfCWZQVHBlzwVXh6zTQN8g3L\r\nSl0e1+kE35mBzXnP6K9SVof8LY1/3Ggamr0xGI81KAMGUo7QrCIBzgzYS8uH\r\nnFHLG/+sIaAHRV7c/EyCoJoDdsXw/2LF/Ty/81Vg0YOVYb5PBh71+TUOrPrD\r\ndhO4x3/xOUxeJgdFErJESKhmUy+oXlyiudgyv44gGSsKF8W2PuK4KUTTJGsO\r\nFZZMJDtMKne3o5K7z11tZreA9lPDsVThDiPtlRJypllm26iG4YgYirQ2nVs6\r\nky4HI4qnlhC8Qs5L06atYJMvHY0HVoUGMRKTdLDVDywdaA1D54wGg2pOpTYW\r\nMPVffCgsi92JnNCKkn9o7HVCN3u/ZugZRqND0JL1BSI/AVlCWtK2Jjlaiw0F\r\nIhJnMdiKTak9QafJEzbqgRYMXFVP2CP2hFbK/mV8duCCkAoJLME3744PoLhO\r\na2AKcIGxP4FdQJirELoSeDzVEKXjck1HzTk=\r\n=WBqU\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "lj<PERSON>b", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/minimist_0.2.4_1677305886182_0.9322173185260776"}, "_hasShrinkwrap": false}}, "readme": "", "maintainers": [{"name": "shadowspawn", "email": "<EMAIL>"}, {"name": "lj<PERSON>b", "email": "<EMAIL>"}, {"name": "emilbayes", "email": "<EMAIL>"}], "time": {"modified": "2023-06-09T21:33:07.258Z", "created": "2013-06-25T08:17:16.343Z", "0.0.0": "2013-06-25T08:17:18.123Z", "0.0.1": "2013-06-25T08:22:05.384Z", "0.0.2": "2013-08-28T23:00:17.595Z", "0.0.3": "2013-09-12T16:27:07.340Z", "0.0.4": "2013-09-17T15:13:28.184Z", "0.0.5": "2013-09-19T06:45:40.016Z", "0.0.6": "2014-02-09T03:49:41.327Z", "0.0.7": "2014-02-09T04:03:57.665Z", "0.0.8": "2014-02-21T04:46:49.997Z", "0.0.9": "2014-05-08T14:56:24.311Z", "0.0.10": "2014-05-11T21:43:10.663Z", "0.1.0": "2014-05-12T01:23:35.188Z", "0.2.0": "2014-06-19T14:57:41.126Z", "1.0.0": "2014-08-11T01:08:53.376Z", "1.1.0": "2014-08-11T03:19:02.076Z", "1.1.1": "2015-03-11T02:27:22.649Z", "1.1.2": "2015-07-22T19:52:35.381Z", "1.1.3": "2015-08-06T23:08:45.090Z", "1.2.0": "2015-08-24T13:56:27.640Z", "1.2.1": "2020-03-10T18:06:04.936Z", "1.2.2": "2020-03-10T18:37:15.747Z", "1.2.3": "2020-03-10T19:08:24.657Z", "1.2.4": "2020-03-11T19:25:14.170Z", "0.2.1": "2020-03-12T22:15:03.676Z", "1.2.5": "2020-03-12T22:16:19.463Z", "1.2.6": "2022-03-22T02:50:30.352Z", "1.2.7": "2022-10-11T01:21:52.310Z", "0.2.2": "2022-10-11T20:41:29.569Z", "0.2.3": "2023-02-09T20:11:17.634Z", "1.2.8": "2023-02-09T20:59:49.233Z", "0.2.4": "2023-02-25T06:18:06.349Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "repository": {"type": "git", "url": "git://github.com/minimistjs/minimist.git"}, "users": {"326060588": true, "chrisdickinson": true, "phette23": true, "jonkemp": true, "bannerbschafer": true, "jenwachter": true, "womjoy": true, "line-o": true, "amio": true, "dpig": true, "alpigc": true, "edull24": true, "digitalsadhu": true, "stefanpearson": true, "qiuzuhui": true, "tunnckocore": true, "seektheerror": true, "axelav": true, "kwpeters": true, "coderaiser": true, "beth_rogers465": true, "kahboom": true, "davidrlee": true, "brentonhouse": true, "janez89": true, "sposmen": true, "maschs": true, "mhaidarh": true, "apasella": true, "bmpvieira": true, "evanlovely": true, "pid": true, "mtscout6": true, "dpobel": true, "mitica": true, "tehdb": true, "thealphanerd": true, "repejota": true, "orion-": true, "dotslash.lu": true, "h-wei": true, "carlosmarte": true, "c1freitas": true, "wenbing": true, "deide": true, "ramjoshi": true, "chetharrison": true, "mimmo1": true, "ryanj": true, "techplex": true, "shahzaib": true, "r3nya": true, "jbdelhommeau": true, "beatak": true, "diversoft": true, "jprempeh": true, "lukebrooker": true, "sophuslie": true, "psstoev": true, "kgryte": true, "jdantonio": true, "akiva": true, "wangnan0610": true, "hagb4rd": true, "hugolantaume": true, "ljharb": true, "abg": true, "koslun": true, "pprice": true, "chiefy": true, "manishrc": true, "infinitycbs": true, "pressla": true, "owanturist": true, "haeck": true, "bausmeier": true, "markthethomas": true, "dylanf": true, "herreraemanuel": true, "chihhaoli": true, "guumaster": true, "moimikey": true, "jordanskole": true, "jakeginnivan": true, "seanjh": true, "mostafazh": true, "srl": true, "pnevares": true, "kinday": true, "borjes": true, "yanndinendal": true, "joakin": true, "simplyianm": true, "jedcn": true, "nickleefly": true, "yashprit": true, "erikvold": true, "itonyyo": true, "mysticatea": true, "ruffle1986": true, "j3kz": true, "lcustodio": true, "jmm23": true, "alexkval": true, "ragnarokkr": true, "strikingloud": true, "jonatasnona": true, "emiljohansson": true, "sedmonds": true, "isyara": true, "joemdavis": true, "yoshuawuyts": true, "jswartwood": true, "jabbrwcky": true, "program247365": true, "getify": true, "glebec": true, "lupomontero": true, "nex": true, "shriek": true, "djensen47": true, "zouloux": true, "dahjelle": true, "danpolitte": true, "adiachenko": true, "adamkdean": true, "eagleeye": true, "rmarques": true, "saru95": true, "wuwenbin": true, "f124275809": true, "jian263994241": true, "axelstudios": true, "bsara": true, "h0ward": true, "tianyk": true, "nice_body": true, "gztomas": true, "drossman": true, "kevbaker": true, "lewisbrown": true, "wfalkwallace": true, "tommyzzm": true, "mikend": true, "its2uraps": true, "andr": true, "sammyteahan": true, "majgis": true, "evan2x": true, "kparkov": true, "!!!": true, "parkerproject": true, "onelaview": true, "zoser": true, "draganhr": true, "martinkuba": true, "incendiary": true, "robertpenner": true, "iliyat": true, "vwal": true, "liushoukai": true, "sobear": true, "rivy": true, "javascript": true, "galenandrew": true, "crazyjingling": true, "arnold-almeida": true, "vio": true, "xieranmaya": true, "restuta": true, "knoja4": true, "superpaintman": true, "nalindak": true, "panlw": true, "tstringer": true, "crazy4groovy": true, "antanst": true, "ferrari": true, "pixlab": true, "jgrl": true, "tatumcreative": true, "nickeljew": true, "rsp": true, "jesusgoku": true, "crysp": true, "ziflex": true, "cable023": true, "yuxin": true, "hacksalot": true, "integrity": true, "danshapir": true, "gurunate": true, "joelwallis": true, "scull7": true, "tobiasnickel": true, "wkaifang": true, "dvdwlsh": true, "groovybytes": true, "kekdude": true, "alejcerro": true, "ryanlee": true, "nketchum": true, "gauravmak": true, "gregorynicholas": true, "a3.ivanenko": true, "amenadiel": true, "nfischer": true, "pauljmartinez": true, "nickeltobias": true, "hypo9eal": true, "pruettti": true, "shan": true, "jedateach": true, "carlosvillademor": true, "fioli": true, "sebastian1118": true, "spanser": true, "52u": true, "rochejul": true, "nocomment17": true, "grimtech": true, "preco21": true, "jasonwang1888": true, "shushanfx": true, "nahuelhds": true, "antixrist": true, "ifeature": true, "huina.gu": true, "itskdk": true, "moonavw": true, "leizongmin": true, "heyimeugene": true, "dresende": true, "dkblay": true, "robert.isaev": true, "ellyo": true, "ackhub": true, "mugifly": true, "mrmartineau": true, "morrelinko": true, "mkwr": true, "enil": true, "anticz": true, "qqcome110": true, "farskipper": true, "lcdss": true, "hingsir": true, "dennisli87": true, "diegoscosta": true, "amir-arad": true, "mevlutsahin": true, "enna": true, "tmshv": true, "mattms": true, "fm-96": true, "rbecheras": true, "leshik": true, "razr9": true, "dvl": true, "abuelwafa": true, "alafazam": true, "nuer": true, "i-erokhin": true, "tmurngon": true, "kenju": true, "jacks": true, "rocket0191": true, "troygizzi": true, "mikestaub": true, "cognivator": true, "landy2014": true, "ragingsmurf": true, "ash": true, "scottfreecode": true, "retorillo": true, "manikantag": true, "soenkekluth": true, "hugojosefson": true, "nikcorg": true, "blam": true, "eijs": true, "cedx": true, "aquafadas": true, "mr-smiley": true, "mojaray2k": true, "fahadjadoon": true, "juangotama": true, "ctesniere": true, "dzhou777": true, "thonatos": true, "langri-sha": true, "terkelg": true, "federico-garcia": true, "quafoo": true, "jedaviata": true, "rylan_yan": true, "petersun": true, "guiqide": true, "ultimatik": true, "krabello": true, "noncreature0714": true, "cyyyu": true, "timwzou": true, "coolhanddev": true, "vmleon": true, "glukki": true, "pbx": true, "seangenabe": true, "jrobinsonc": true, "rajikaimal": true, "alexchao": true, "wheelo": true, "zguillez": true, "i3fox": true, "yuch4n": true, "largepuma": true, "leodutra": true, "giussa_dan": true, "scott.m.sarsfield": true, "hecto932": true, "hain": true, "maoizm": true, "pmbenjamin": true, "alopatindev": true, "sudhirkumark": true, "cygik": true, "rogerta": true, "danielknaust": true, "ognjen.jevremovic": true, "junos": true, "chrisakakay": true, "papasavva": true, "leonardorb": true, "shaomingquan": true, "ninozhang": true, "chinawolf_wyp": true, "iatsiuk": true, "about_hiroppy": true, "marinru": true, "stefano_magrassi": true, "guioconnor": true, "aamirvohra": true, "monjer": true, "djviolin": true, "max_devjs": true, "lourenzo": true, "yikuo": true, "sprying": true, "sibawite": true, "danielpavelic": true, "akinjide": true, "eseath": true, "alexxnica": true, "kakaman": true, "iseif": true, "heartnett": true, "darkowlzz": true, "oleg_tsyba": true, "drdoof": true, "wangfeia": true, "partsunknown": true, "lgh06": true, "usex": true, "dburdese": true, "grabantot": true, "d-band": true, "geekish": true, "dm7": true, "dwayneford": true, "zhenguo.zhao": true, "suryasaripalli": true, "arichiardi": true, "sinahwz": true, "g120hbq": true, "cliff": true, "bryan.ygf": true, "janet-lu": true, "luckyluke": true, "jedchang": true, "highgravity": true, "hoanganh25991": true, "yousoff92": true, "icoon.li": true, "vickykoblinski": true, "tedyhy": true, "liximomo": true, "xuu": true, "shiva127": true, "tztz": true, "justjavac": true, "jk0": true, "phntm": true, "raycharles": true, "wuuashen": true, "yinfxs": true, "seaniap": true, "tpkn": true, "arcticicestudio": true, "laoshaw": true, "irj": true, "undre4m": true, "faraoman": true, "moonrailgun": true, "geeksunny": true, "losymear": true, "imaginegenesis": true, "dyakovk": true, "washan": true, "adelriosantiago": true, "treble.snake": true, "dr2009": true, "reyronald": true, "joshdoescode": true, "morewry": true, "hearsid": true, "xtx1130": true, "rubiadias": true, "sixpetrov": true, "naokikimura": true, "anypossible.w": true, "sepiropht": true, "davidbwaters": true, "vv314": true, "xiaobing": true, "xiechao06": true, "daizch": true, "yanghcc": true, "ungurys": true, "tjorange": true, "nilz3ro": true, "yang.shao": true, "flumpus-dev": true}, "readmeFilename": "", "homepage": "https://github.com/minimistjs/minimist", "keywords": ["argv", "getopt", "parser", "optimist"], "bugs": {"url": "https://github.com/minimistjs/minimist/issues"}, "license": "MIT"}