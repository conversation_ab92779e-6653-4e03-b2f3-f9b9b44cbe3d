{"_id": "@babel/plugin-syntax-bigint", "_rev": "35-ecdbc16aea285ec2a52367a2cc14214e", "name": "@babel/plugin-syntax-bigint", "dist-tags": {"latest": "7.8.3"}, "versions": {"7.0.0-beta.48": {"name": "@babel/plugin-syntax-bigint", "version": "7.0.0-beta.48", "description": "Allow parsing of BigInt literals", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-bigint"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "devDependencies": {"@babel/core": "7.0.0-beta.48"}, "_id": "@babel/plugin-syntax-bigint@7.0.0-beta.48", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-sZRyT7bfeqB6zCxpQ+3uZYLeAzxHvA9xwl6w0zrecvUzY4LY3PiIqLTUfhMWuaLvX/ghkt93E+cSw22ZHIQ0Vg==", "shasum": "47a1bfbbf3860fc177aab395fe6bb0e635161242", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.0.0-beta.48.tgz", "fileCount": 3, "unpackedSize": 1469, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxCtCRA9TVsSAnZWagAAUlgP/0jhNNY8UumvQ+CA0qF8\n3DMhXV6GmaAPjQiDtXmZi8/tfz+2TmOq+JdP9w0A3IOQrTFENkjkOP+lQVA9\nc2jEpM4OwEmaTLWr61K/h8EuB2kkKTRnskvtXYAnFG7NBKFKScLHw5/OYjeM\n5bQGvT+pnIwP6qtBtzqHSjWM6O5C5pzcY//CtxDiKzL6DrJAN2KyRcx9okzg\nRMvSpquz5ukqtUrJM5aSHg+mJpdfKfQfuwUC4piKaUlPD2x6fmYlBPxL6WHJ\nH2f8TkcraL8+YdPtCB+iywLYtY3hu0HKAOeCJKAQS1TjsYMwTnf8YeEpeKd4\n+JTxU4+pAjoa/6r1ts4gY455oR+YjvCXxAilSOBzmw9Yf5ZvlBA6rgjLIqQC\n0A7riMbn7qUdHiC7/ynEo4LVqtYlQU0T9/vtFgLKZ4au88W//FkObCJOPLvs\nwnh88dlmG/jdBzQrS6RqzvylrmIZg6Y4asZfWQdMu9wyOeluqwTGITfVRMz0\nAZXRZCQqlofEfU1TK36VxMrHiSS/yo9ZE77TDyZcA+WSNEJ3hcjknqabX64K\n2sM5AjKf4+s2EyQ7ZECi5e7uNeBlO8Wz+kF/JmkYbqfGO5HFXoCz9uz+jl1g\na/gLCQwQ0zycKIuMwxKpWkvDs2hI8iGMY0fW7UmFXIl+xd8CqnYhcQQ7KLZZ\n/rDL\r\n=Mgmv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCIksRHzYQuZEtetqAa2wpoaoz1kJz4SyDe9SXO2HX88AIhAPsD/5EZrAmBMK1T9TTM5ALYy/IVxu95MazD9Cb6bJbJ"}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-bigint_7.0.0-beta.48_1527189677391_0.672738791910803"}, "_hasShrinkwrap": false}, "7.0.0-beta.49": {"name": "@babel/plugin-syntax-bigint", "version": "7.0.0-beta.49", "description": "Allow parsing of BigInt literals", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-bigint"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.49"}, "_id": "@babel/plugin-syntax-bigint@7.0.0-beta.49", "scripts": {}, "_shasum": "d545acad1700de5d5ffe998674831f1a72aad02e", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.12.3", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d545acad1700de5d5ffe998674831f1a72aad02e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.0.0-beta.49.tgz", "fileCount": 4, "unpackedSize": 1492, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDM5CRA9TVsSAnZWagAA/H0QAJO2uOr0tDBFUYitaRxb\nt1Sq6Atb569hAa1RtcNQBz/eSZuVUbH5bZM6NJB58bdUesCuC1IVD2aWOARh\ntxCTu+8GrrkRDt55O7YnMCZnMlb39aN4fp8qT95lSTqWP2AMdksgfYHGMmD1\n0yJu2UTt7xSqXvoK59WM27AT2Cs+k1/wtgf1KdzATVtWVi+F6JVgY212mq5D\nmcuA5axrUwF+5NluAZCsfEyerxBn7qG2uzUPhRLcLHORSF5QaLGOmV50O0PP\nYpOpfzCPjAPlxwsQBfHiyxl8l3gBmblNqA4srNtExDBT9iwtpKbLepougdGv\nGS9Y9nXLkHm48zxjUEAiBanAf+NKKdr1MymQBXZdMGfq45+RUB7gE6V5ng2h\nx5X4CO9kUod8QKwPRkDZeBDHvbqm5Q182xa+gYJ3NB8OelyOUOB9ekN9BaQ0\nUWSyjuRwbGPLtTA6O1ikeBaQuYFRGa9OdXHJE3cmmT66HIaX0mKU2ZFTf2f7\nevchFsR/UKNWsoNwNEIOva7dNCBRl5MaxVL++e7B4MqV+NlIdFyZ/AtX+zuB\nOS2NByxkWdtso7A2jJlhSEWgdcqrw8251NNxjbSRA60NZ5i5KkETab9VExVR\nkgJOeMCxD4rDv6PEjqNzpc2t7DGf9KGzgxF13h4CNEJPwzvWw9um3WNRQQTd\ni91p\r\n=52z2\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-4f+DWxji7qkha9l1S32yoiqHlDSqa1AmZcThFP6TaQclsrwXnqjQv3+c6iAaqCpFuA9F+ZRJ2i6DrOGucNMOPQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDw5JM60ZS7H2QBiUtdWTFzw1SiWKytvyiiBTNs/35YsQIhAIbNuqi/RVJX00ExmjTPl2G6nkZwvfDYxuzG4A+JRzOZ"}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-bigint_7.0.0-beta.49_1527264057338_0.877877799460486"}, "_hasShrinkwrap": false}, "7.0.0-beta.50": {"name": "@babel/plugin-syntax-bigint", "version": "7.0.0-beta.50", "description": "Allow parsing of BigInt literals", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-bigint"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.50"}, "_id": "@babel/plugin-syntax-bigint@7.0.0-beta.50", "dist": {"shasum": "4a0781915d19738a8983f0d314ae8e9cbc0e949b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.0.0-beta.50.tgz", "fileCount": 5, "unpackedSize": 1355, "integrity": "sha512-2G4FiTGJNeitxfWxZYwgRAvjOKyfWWwnKOZjCxAfyTG0sdcJkNX6eG5hWVY1CK4Vpw/zp3ITnNnzd/B8mHKAbg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDfSplL9JsiMQ+ca0HAb7GPrfXMWMwVUyj36wSFYb0QLAIgKEmMFVOwXIA4cABdlfffT/CtDETAYf33LVPjocr6xdw="}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-bigint_7.0.0-beta.50_1528832817696_0.7015382029082571"}, "_hasShrinkwrap": false}, "7.0.0-beta.51": {"name": "@babel/plugin-syntax-bigint", "version": "7.0.0-beta.51", "description": "Allow parsing of BigInt literals", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-bigint"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.51"}, "_id": "@babel/plugin-syntax-bigint@7.0.0-beta.51", "dist": {"shasum": "442ff28aaac60910d54074af0dafd6f1f588cca3", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.0.0-beta.51.tgz", "fileCount": 5, "unpackedSize": 1369, "integrity": "sha512-xIJ3JoUzf+yhXe/TR5i0nOHSTg5K/LhVmfPR+00kW1UFvLyG+h1jR6QyD6s7AgqRS1tPHkP8jfSxcJ1oxEb3Wg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDRtzt/tPiVhpYoJMv0wVeqbxMCoIHS6d0YU1Qw2Qz/YQIhAOLHnaiQE3BMBOUkkTEsq5pZgmUGm8YtOggiW0BwWAz4"}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-bigint_7.0.0-beta.51_1528838366959_0.08187825369415691"}, "_hasShrinkwrap": false}, "7.0.0-beta.52": {"name": "@babel/plugin-syntax-bigint", "version": "7.0.0-beta.52", "description": "Allow parsing of BigInt literals", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-bigint"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.52"}, "_id": "@babel/plugin-syntax-bigint@7.0.0-beta.52", "dist": {"shasum": "e9422ac963eef2739910b9b18485c6656260b3ea", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.0.0-beta.52.tgz", "fileCount": 5, "unpackedSize": 1368, "integrity": "sha512-FsE6Qv2SvnNgQoU749NKFJ3xQrW4VMc7X5og39NKYTGge9Fl+7a4A2JQI5w7X3xM/c4uuSBelQ/n9UVZBJSoGA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCdUlGrDfikyaRPSm4PLsBsEulVxab8PJHrYpv5oKhQwgIgafBmtUv3cR9GlF3Z8d+NuXTW8O/l0FTUjBG43nyMYzc="}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-bigint_7.0.0-beta.52_1530838755653_0.5400984314148396"}, "_hasShrinkwrap": false}, "7.0.0-beta.53": {"name": "@babel/plugin-syntax-bigint", "version": "7.0.0-beta.53", "description": "Allow parsing of BigInt literals", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-bigint"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.53"}, "_id": "@babel/plugin-syntax-bigint@7.0.0-beta.53", "dist": {"shasum": "8598a6e7983eae75e4b8da46de31c7bb4a1f206c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.0.0-beta.53.tgz", "fileCount": 5, "unpackedSize": 1368, "integrity": "sha512-Sqf7meH/KvrWqyeZ5ZBhYRiPNfDox+eZcx5e3o9KSrww/XFk3RkBaLQQ+Yg9UKJo5gd+6zNhIdEELjNjjiaZ3Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCUiBO1sg+kJS4/1NEbNJ6bA/r8mmvTCQye3ydyFNoA1AIhAOjntTZYcnURp/HI56JghlVKLoQtEmUZEgrtvMQQjVxy"}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-bigint_7.0.0-beta.53_1531316405500_0.04318611433671604"}, "_hasShrinkwrap": false}, "7.0.0-beta.54": {"name": "@babel/plugin-syntax-bigint", "version": "7.0.0-beta.54", "description": "Allow parsing of BigInt literals", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-bigint"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.54"}, "_id": "@babel/plugin-syntax-bigint@7.0.0-beta.54", "dist": {"shasum": "c212a41c26ca9ce305dc85550babd86ef1572841", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.0.0-beta.54.tgz", "fileCount": 5, "unpackedSize": 1368, "integrity": "sha512-eYj2sMKnR65FSm0XEg2MDbUISXAPWRCaszMTG73OPYHV42Ymzy2Py8OAVZQx2zZbruAExd8JnZVt3P87+OmgnA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDMQ8gxDo2RjUVu+NdCYYssXMEiMksPR30xxrlXHP+PeAIgfIOpZ5v+MZKJvTXinjWqbdEoj/pY9mvNxzoXq9EP8fs="}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-bigint_7.0.0-beta.54_1531763996595_0.1670585524192194"}, "_hasShrinkwrap": false}, "7.0.0-beta.55": {"name": "@babel/plugin-syntax-bigint", "version": "7.0.0-beta.55", "description": "Allow parsing of BigInt literals", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-bigint"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.55"}, "_id": "@babel/plugin-syntax-bigint@7.0.0-beta.55", "dist": {"shasum": "078a9df03d0ef3e4bbfd1b51feed0ee8ea4c0757", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.0.0-beta.55.tgz", "fileCount": 5, "unpackedSize": 1368, "integrity": "sha512-c6RUpXxNk4TzD76W9G9v+zwpS6JuSiUcvcYTZTa6vDL82flVpJcWuZBFz/PKlVj3fb2HTpZvMpg3IjFw4v+C0w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDoWsSnTH3uc7ZZYCLBsOBa/5iaR34pxAcdpHq0UOLhdAIhAP+MZ/LFuI4NHl5+J7sYlqJrB77uin4xEqUMhQBjyfJP"}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-bigint_7.0.0-beta.55_1532815619642_0.31487414217377085"}, "_hasShrinkwrap": false}, "7.0.0-beta.56": {"name": "@babel/plugin-syntax-bigint", "version": "7.0.0-beta.56", "description": "Allow parsing of BigInt literals", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-bigint"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.56"}, "_id": "@babel/plugin-syntax-bigint@7.0.0-beta.56", "dist": {"shasum": "867ef28bdec4df24dda1859f0d8b9631f717becb", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.0.0-beta.56.tgz", "integrity": "sha512-luXlTJ0+AcSve95f25oQfpvTvjkWU4FKWxFyS8yu5aNKMJrrmwH+a/ijSJQrmIRe0VIeixysedYsGYtd15QTpA==", "fileCount": 5, "unpackedSize": 1368, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPuBCRA9TVsSAnZWagAAt9YP/3t/jr2ddu4vmkW6kEqO\nsd3wOWpXuAQSBYYopVtO7zQFvBkk8v+/j5abnun8pLJzfLc1B/AS2xhAf+Uy\nWXA/FUDKlW9ot5w4RAXzgy/85SM0m44qSNK3YOTV8CfdD89oGCo3/HgZ7REn\nCGVIAW9IVdLKppKDimHHJJUsKQlkNdwcBQHjhvxgJQPTBGark0+YUA+wRs2x\nMRIUcMfejKWGXbdMPvXQQ//I70LLkDlyQrHxNz3FkAhPaeXqBS2w37iYNdV4\nJK8fW5la3v39OfezIoTXAyIWtFthnmIA2tzqYF/AnH957dpljWF2RmnwCs9W\n/lKdhTloJaH/vQFg2PONkqFAQV992jZxp+14RlXTf7Q2Rdt3plWY7pWaGCND\no45AJz7uVSfECraJuT4C5xZ0PqzjSl2TobUgnapI54i/lYkKcHBK++eOGs5z\nbrwbv3c6UbrpNXlZunVW9LJgpMaqhxwG5fzsccMHC62tZ0uBpmGp0fABm5Kq\nz6Zz6Z2SSN64ScRZbuHaREKBWH9EjDvLKawR9dAY8JIufDi10ku90ND97u21\nMasFqhd4OD+Ly9aIuXaw1cO7WBQRPTKZHBwamtzdIQT+VomoHHOltXVOgmlA\nfCqRYE5Rkzje6cmtzfmzfrRXJ5aeVsLt3zOv+Yv9kVysHJd/lV+mJvA1a/E6\nTD1R\r\n=wIvU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCt04q3bPOKNGM4O8tmySnj+t4f46kFabAeE8xxcDa8KQIhAIsDm6hAakCDZ90GgD/xTr6Se74zvBGp1+gUJ8FN53tu"}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-bigint_7.0.0-beta.56_1533344641137_0.21039277012053081"}, "_hasShrinkwrap": false}, "7.0.0-rc.0": {"name": "@babel/plugin-syntax-bigint", "version": "7.0.0-rc.0", "description": "Allow parsing of BigInt literals", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-bigint"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-rc.0"}, "_id": "@babel/plugin-syntax-bigint@7.0.0-rc.0", "dist": {"shasum": "536912f16ac64e5ead4c7bd40de19fcdf1da7589", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.0.0-rc.0.tgz", "integrity": "sha512-Wk8OIWntPH7cBMJDzM653lEdicUScInQaU7b4yE62isWs1ir+es7puekIB6kUK9HadS5RE0Wsvlxc1BFlbo92g==", "fileCount": 5, "unpackedSize": 1359, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGRfCRA9TVsSAnZWagAAxgIQAIxD4iJe9UnAuceLGJJ4\n1JIAyZaHgiSu5ndGub3AwrwBO9gTpfvhSyniswnrYxgiwEGueyXQKLKxWoBl\nxK7GVmdfFDGXF4zDjN5wwMVvHKcc0XomAS18H/gH2v05SnhrVHHQySjGfuAh\nT6qBfKIBkbGJHQvAXRRHX2X/e41roQh684VWL5pE269/ak8twNKIajuhraUd\nVKIa3dE6G09hPspWOgrt0ZxxezEp67CpySEbSjum3TfGbzMHUJPWyfyFFdBE\n9ZarjozcT9bWuugq5zixDWAFOri1Gt2GVCLrXYhsdmsNt7IKQX60gKkE1I8R\nZXkh0dKkOtdqbNJp+Ahkmg7+qxdAinRS4yh2OSYFG7F6PIQATS5r4ZdCC0Ei\nnU7+OcUv4oUTxxpFi4P9V7HsX3iqGkAtkCI9BvIvjGylVpoaig+pBL04SPJd\n7qJn8e3nJs+N1TlQppwhZ/a7O25Lzl0I6zvtmKfeII0O19/ejsjF9oa+qAuP\nHhExSM4cCH4U/LzmHUogaPDG8j/9HHLCXavTdexh5wjksnyKZ2sfQIkNG8h6\n/YPNSENUOROx1Zpz7S/ZGn6oqst7GhNQ14qwCwcvnBW4Zix61Ky9YlNHC46D\nZYmliEKPdLDMyJgbTWN+qpUVN9Pe5Mkc4zr4TQf+0sF0l5Tt9jlk2xBIp3DX\nuMCN\r\n=QVBJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDnVaOrnkLnleLtsKKBaBu+urQt9Wp0ymEalXBvgpBn9AIhAMh4wHbzxXkX+bM5Wlm6utQ/lpZokN/PYraBb1B1aaBg"}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-bigint_7.0.0-rc.0_1533830239192_0.23683063611531363"}, "_hasShrinkwrap": false}, "7.0.0-rc.1": {"name": "@babel/plugin-syntax-bigint", "version": "7.0.0-rc.1", "description": "Allow parsing of BigInt literals", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-bigint"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.1"}, "_id": "@babel/plugin-syntax-bigint@7.0.0-rc.1", "dist": {"shasum": "ba56c6eeeb9a106872753118c4e93395c31e1699", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.0.0-rc.1.tgz", "integrity": "sha512-qC6c2DvNaAzEU7TV2F2HdQCe0PE3HVjAhM9VDhvGoeHwWOOf5VoknoEk3xuCLXQ39hv7B/Z9er3HyTIftFBFag==", "fileCount": 5, "unpackedSize": 1340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ7lCRA9TVsSAnZWagAAnnIP/RgSp2Gbtay3Y6v2aq+J\nSgtdZ1HyeZmeGltykgC/R/jrMDrYS+TgVZziB41yXtEQrngWs0RJ3MwlD67V\nutXbn+kqXoZor+ToOcVF1y0oEd31Rsy7k20oXMYmQbQq9Eeu5PErSm+f3RxG\n8XGZcx2oPEnXyFt8QHNyX3bYST0PKFsz2nJotYI3I4OWUKqzRrjjsjITujkX\nu8KjpMpEdY6qUDTrF2XSYplReF+9f5TaxheuYZUhxJF1QSVXcW4r2Yzoz2eJ\nH7LGvfvhCe97iiPA6m34S3BcEa5IUw/kKrOAQ+xXgZI8t0VjXR5nBw3TR8pe\ng8gM5xC+Q+pBY+7qcB3BZv6eG84NtU6VvuDF/j3RcQAA5BH4l6o3krsC815X\n39c2wjDAiTfL9NVkMAKlZCR8qIIzfahTQuGzQRjkqp+j0HmLJoVVoA6RaVpv\nSqNLZ5YlsV/I/MinQNgSasobPvvHUug6mWZ2xPuWoSNKMXLed3MNpt3kJJFb\nzoq5jI+Ot94SJMvxvKeRwdGYlJZjZWcPsSKOVMSBowrxyrI4JyxW96TExgAF\nPiNR596vlyE5wgtpFbwbhXYy2rlDwN7B3Dn6cs3kiFxAqkH1MiABLqtSUp23\nIz+H39TaCOk/tNtlnZLnL8I/E6UerhUvMJw5nCO/ouugwRcuuJQkM9aCd/pn\n3jj7\r\n=25Rv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF0qNEhQWAblPN7BDvAeGEH+CjIOsFpxN+v265fOB9xeAiEAsztB70c6l1X5VbqHhcXqcbr+yAR4Xd4HEl7Eeg7Mhhk="}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-bigint_7.0.0-rc.1_1533845220776_0.46423203797584445"}, "_hasShrinkwrap": false}, "7.0.0-rc.2": {"name": "@babel/plugin-syntax-bigint", "version": "7.0.0-rc.2", "description": "Allow parsing of BigInt literals", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-bigint"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.2"}, "_id": "@babel/plugin-syntax-bigint@7.0.0-rc.2", "dist": {"shasum": "417335aedc320ca5600177f5a5e7cfe1d6e35d9f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.0.0-rc.2.tgz", "integrity": "sha512-gEraw3ZWxqRtF14Es87S0ynCzDwn/NipAB5FixSCAfFtgewY5mILL2TX96YD+IVxFUCfwAqMU3nbzcptmVyqHA==", "fileCount": 5, "unpackedSize": 1340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGaVCRA9TVsSAnZWagAA+H4P/jsCv/o7dSIEoC6PWN+c\nc3lil4LsK4t3/Ov++JtIo1VGxDK1TOJXpXTELW6sTGr2lfdjUVGY8xu+ndw7\nQWk0ORAtpZby9iOHbpG/Vs5nGDR57vesitE9g67wZERqPg1nf2x/Cw6Fen2H\n/b4iSwBcyC03rB78aFpr8fQWrz/Pp6P2GChc9/JFFbgfdDWQa1fQTee67ifz\niZWZ/JRydjbkpn84qERqDmYZaJDaVp9RhJbtE/6JrE5+NfScx902InoWwSH/\nUWSeZ/bFpyLK/mqWhXmBORDOGUYwzgzYYaxOkLycsi0xtPboQ2TFDxx0DrNP\nd4MnfDYhu0o/iEmZ2JvYET7+k32ROyOX/UBSgvUwLS+jeYWDmOE7U/YAnP8p\nWAo9WtGh804AwcLMdWV89AwOpIqy04ducQbf68Z5Sx5JN3rajfdekuBqRsYr\n2xrt8dALAdkRD6n9YS2LPHeNrzJcGO08SU6JNAqyoYyxKth91Yjgx44qM1yO\nbv8B/98AqPYaqA9AGLqpRm30ZN89a5SePsKt3igGS4sOcxQpMGMsp6U63VnD\nCIUp6fkLqgPSpuq5tOMngngnQ8UirNcLFNoFZl80fKAVQNmI4pon3MBJxp7j\nPXO79oJar9GGLvsB40KZB2f62SIHoAEXDIgGVtKez2ZLaxa9q6jDEwAgiETp\nNS50\r\n=X+Os\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCPwbEMis8pcoE5EjrMZb5FlVLlTDb87yhD7JlWrwHsKAIhAJkSnFeimiva5G254h3QKor1VYkKeQic8tNqlxE9ZqYH"}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-bigint_7.0.0-rc.2_1534879380771_0.7266850219290732"}, "_hasShrinkwrap": false}, "7.0.0-rc.3": {"name": "@babel/plugin-syntax-bigint", "version": "7.0.0-rc.3", "description": "Allow parsing of BigInt literals", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-bigint"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.3"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-bigint@7.0.0-rc.3", "dist": {"shasum": "e852c63d4fb2e3e62f75ba5fb9cd8e6f567ff305", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.0.0-rc.3.tgz", "integrity": "sha512-Jjm8E5sCbEUvwzFhdR1vBvQtqf7dnz6AS7hhYApZMWdDaQTBnC0kU9B4y0sCyl4El23XVfWzXxBWghVtR9fP0w==", "fileCount": 6, "unpackedSize": 2439, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgElCCRA9TVsSAnZWagAAvwoP/Rrlx8C7jRmy7bLe/Wy3\ncfbZnjPp74q2Vi7JROBMICNQ2eUNeHVhLBwklN9GzJH5jMMss+c0sUAmndiU\nHPZxu499PSGLkweNXfuzSKukVQV7yWGBzHNSDLrcTE4Gno3sePCKaHoY1lGT\nq1Mp52C/eEXtWLnWECp5CCSzjqc0hGEo8J4zWT2MVfXOX1o9TklFDQ8Cr1pk\nlhZFaHispUrFctGm0DkR0rgrQJS/h2qaOVKUc2f/+1MK5sU3jgSFWAvI1PTJ\nNdMjhFo1+FL0aiDj8VJTPSMoqca6QJqfJpANVWf3CFwruuiM0p1Kn4Rl1dl5\ngAj6g5zAAmVIrGqyLb6vIyi1B7+ook+unfpT1uvMvU7jV/ridvOpqS4lBekY\ndMbPUTJuauP4mXgjc9VorM//WcM7uPGRMGFtXHsKHHEXYuihcIPM5OSqFsBn\nNUcaCI28DNvv1lDKc6SFhI323Cfb28/D9aulUfENGExHoRbSMQcUDjrI+RZZ\nj3e5FoUh1kPXZe6fpr4M+L3Qf1EbPWFPgpXWW0ys0voZbCYa2/G1gxuKDVDj\n3xoXR4ReA/k9n4CXM705eeqZDI1aNg1p+ZKYvNJoj81VNtc9L6QPRiK88lM6\nnhwRHKSdcvavGDR3MLJHlGbyy3tNJhfOJoI28QNJ2Ce1wYuzlQEWauzYrKK+\n3N7G\r\n=mJK9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH5eCIdv1KbK4IV3SsQsob70pnLmt2gTW4eKbUEdojW4AiEA5Sp0Lbn95eKC9DlLnEpRfol1aqxXQaYG1lBD/UR1cx8="}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-bigint_7.0.0-rc.3_1535134018082_0.37226751479571907"}, "_hasShrinkwrap": false}, "7.0.0-rc.4": {"name": "@babel/plugin-syntax-bigint", "version": "7.0.0-rc.4", "description": "Allow parsing of BigInt literals", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-bigint"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0-rc.4"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-bigint@7.0.0-rc.4", "dist": {"shasum": "e89d018bedfe12d45b3bfeca3e56278efcef3a9a", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.0.0-rc.4.tgz", "integrity": "sha512-l+FmiG3iABFKs62q4xzL/MEHV0sH73vtq3vadu5qsFYo9Oy4lTsD1iOFLinV7vJez6NYNh1yKRM6NIvGoRDEpw==", "fileCount": 6, "unpackedSize": 2441, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCofCRA9TVsSAnZWagAAB1MQAJTEcBrx3S3EvfILLmHA\nXLo+X6knG41Fe/G9TuosxRKKv3/qxUpb3gJ7ztSkf8zUa72ofqZeSMnoLGb3\ndpfdd11rKUGQZgeNlDdccIPmlvHfHhQ8td83vR4096x2YcBwvUaU1AVmZZ/8\nAk6KPqed71784Br10fg0MKjAtainoELdzB+1iBiJE7t5O9Ay+IkmjB6z20Nv\noixmsMqsyYOE/zLPS6+JegRjxXS1Y+2yWZRPq+oVPGi2OxpW7sfEfgDHOEJx\nCvqiM0+Q3JIYc5bo450Uu6vLttQqAHK2Vcm9glJnTAV2SIcOhzjJVjR2ipX5\ngFIcNBShuOoxz2ZMdTigS5eZPmHhks+VhNoYy5yBZxIMJYD+7dLRFvXnIs6w\nCZhixtxZba2twCmdL6VJvPaudVV85e4CZK+A0nEYS/2xmX54BnWrH9AEyNTJ\nPjoix9jaOH0sOmN4wCWJV41cteTgeDznBC4OU+9bLuIAPWzyaIVOFivUpt+K\nrkUrfdPfV5GStZEcDxTs7RVjAMcLofs3CkcwdsVRcw8PbZBKxTXZzyUWcjVW\ndGGZA4i+IcNpTZMwetzEDs2e7UTOF4j5OkO5fag30naCBMz5kbK+1ZrPMrtK\navL2eOymzhDOJekfjMuezjdqbjAea5VKYWFOUb2Ip1GcHzD19EJXDsiVBudG\n50qa\r\n=OhlV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDnm8oQR+4z0k3f+xpfA6Eie2uhYEDKfAKJsdB1ncTjKgIgQTU101dr+A69VE6mNh0hdKEBTrFxeY3dQc9eGsgx4To="}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-bigint_7.0.0-rc.4_1535388190610_0.5059390527904335"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "@babel/plugin-syntax-bigint", "version": "7.0.0", "description": "Allow parsing of BigInt literals", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-bigint"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0"}, "readmeFilename": "README.md", "readme": "# @babel/plugin-syntax-bigint\n\n> Allow parsing of BigInt literals\n\nSee our website [@babel/plugin-syntax-bigint](https://babeljs.io/docs/en/next/babel-plugin-syntax-bigint.html) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-syntax-bigint\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-syntax-bigint --dev\n```\n", "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-bigint@7.0.0", "dist": {"shasum": "eeafc11df663f2bd78eb0b5880a8abd5fc370776", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.0.0.tgz", "integrity": "sha512-eX2s1Q79SVllGsJ0Rl267MHtBhD6872qbMmE0hxk5RLzC/DHE0tGJ0ADbOQXhw3G+chEJZlYBKriddvAE/hDJQ==", "fileCount": 6, "unpackedSize": 2426, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHA9CRA9TVsSAnZWagAATy0P/30eg+hrX0S5ed6MColf\nppq8dry+2IAfAxhp14CHUUUav8yi5ogHDlWLf8UqqQBrmWsT8OoWCzrbyQ3l\ndDZNBhRTZbN48taQy59kAcDOasM+pn3c8BYqVxwqbUiB3LDUwH8VGPrLh0Om\n+u7qb2EMt5r1tj9Tgx8vfphZBdF1DVMm3U3Pa6MpHTdGIgDpZAoICyM062Zs\nPbbM7rZ5msTUfvP4p+kaY5zufN3gNaGea2DH3tQHmnfslGSKHg8qS1Rvl3Jy\nE4H9zp4ODLWLHfq2XE4iFIaNx9H9DZp2LPUmnXG7hNE9mwHgoGGcd0sojdCR\neceShCk7iG4CFcXQSJUW2BFcAOIklSRY0rPyi/30r4VoNdtTKbzMJMGCx6hy\nQ9A4C1vjAT9VYlEw5jIVemZ9Gy3jtNuUdJ0KrLWMxLjX9M3B/CKxfZ3cNLWf\n5+2z1otL+Zax8v/PGRX6cESM4y+DJxrCUf6b/4SbPEd79lgF+Mx6SydzfBMB\n/ShysR0aQpU38K89IGRgGKubQl5rTMlcG1DxdFkseyB96xGB9KVuPdrtg0Oa\nLzR3fShSSVeiO9hX6G69eK0qOvT7cexb+GQxxYrPRrgjmzguZvXYrTERrI69\notk/FaKXEVkkFc3ly4IEyQHglVtfEzRJqZDI82izGZP6ncWIqvx0GGoQo8qD\nEsLk\r\n=XOF5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCGxBHWqWD7yj2WYsH8vBcBNBvXdnnO4Mbk/mJAjOVXYwIhANeHxhngfB9+291EAXJtHcXgy/DNgMxhYfF2E7b7tjt1"}]}, "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-bigint_7.0.0_1535406140875_0.7374124437181382"}, "_hasShrinkwrap": false}, "7.2.0": {"name": "@babel/plugin-syntax-bigint", "version": "7.2.0", "description": "Allow parsing of BigInt literals", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-bigint"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.2.0"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-bigint@7.2.0", "dist": {"shasum": "d757b9df850b4a797ad2391263d8e19fc1e387f9", "integrity": "sha512-roh2Cgu9nz4+XppHFzKyYwrqFceDyV/1diCJ28yhVLdEyxDirj3Sx3UyjPD8t2aY3lnKeqiwACYOvfehJoWxSg==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.2.0.tgz", "fileCount": 6, "unpackedSize": 2507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX0SCRA9TVsSAnZWagAAnC4P/iZZoreqtH2mfXSFwZTK\nriWi+QKk46pxK8Kovy1++29z9tR0oqPoTpmSfDFWF1Z06OzP9LE5kXvopn0q\nKwDEkOewQQ17mtHjHEPP6LOkn4/J6/hd0RtXrBU4xBW8LPWe+bjDGYyIm3vM\nqC+oLIqYA9rhkf4CifgMsxcEMjuJrbfNacDO+K3HHdQkUmrlqtn8O5MD+FdK\nPlqKec0URERIobx+8ysVe2MOmwKT52w7AUF9UsPjQuGC95ol69V1V5kjKDgm\nIv+kQT4Sk0v3M9UJu9od+yGiUuA4kVRZyfkeB2r2n7oQJa3X0W3jcCFzWXmM\nkVmCXvam19rnX+1gDNaPbg9/Yz2IpeuxiNTFUiDWK2ABLtoH35T6O6S5vm1O\nvqgQbMTzOzjM7qjV9w4o6WKUqNmw+UNOGPfUv7SSKt1jqWeqFVd/2YUBmvyZ\nakTxn+dUNFo+0Zh7fVIXgqbqSrHgbn919AfNfXrccaa+Yn8EsQHJov5cCnCQ\naGRNqC5Qv5kXyu2Z7GJafX1UAD06YoOO8bzFdhviH0vT5EV0+kKfWegvyDlN\n97P3CTPbEUl3iMSy71jLZi1tt9BIc6KulhecANbGxUkvbsixliBO8jqtM7Vg\nvaH1jqE74HCnzfrsrJKLr1rAXtEqRe8pZmRcWNZ5v6Aq/njQeFSkB9eSSETt\ngBUz\r\n=5lDq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCkHbSFCHw8tLDVxptKUXnZ876wQOevYkEYZRqdUV+kQAIgWd+cak5wKvEjIaljNd3DQTN8Dv3LdgtCEBeudFQQohs="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-bigint_7.2.0_1543863569704_0.6759184854359479"}, "_hasShrinkwrap": false}, "7.4.3": {"name": "@babel/plugin-syntax-bigint", "version": "7.4.3", "description": "Allow parsing of BigInt literals", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-bigint"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.4.3"}, "gitHead": "508fde4009f31883f318b9e6546459ac1b086a91", "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-bigint@7.4.3", "dist": {"shasum": "be47962307fe1b12f7bcf6763ee1f856f3e4dd42", "integrity": "sha512-OeWz3nYN4ir1m6slDaJp128HYUa4ZMGY0wT8/ZIFxjrAHQ13ONR/7vsUsoS0I+U5JkPtGsyPjkYylXNFgvwtDQ==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.4.3.tgz", "fileCount": 4, "unpackedSize": 2574, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJco74/CRA9TVsSAnZWagAAgpYQAI1X99HkqJnVSh5ob3ZP\nMUxBkz4AtPE0ubAbPSRFlYCEGRdU1QlWGbgXnGBqbEFRhFtpNw5mvwTTvi6T\nQOnOLNJP0igvO2e3epGoMpaVOPxGNTM1K/i04unkB8heBdB+OeUllG2thGli\nCZLxOzEB3rpyKqe45ZvAICJXj65X/vEsIILG+vu0O9oA9Im9c18fqFrnti0R\neUY+uudSvYYRLfr9pNDaQfHyyIO6f1CsVNRf7BOltQ259RABMw/O+eF/kRU8\n7DXw04kcTzUZBo73hQ6WjAE0S/04yq3K08xlL1vu4Q5x+Uhat2VYLL1i4Lql\njtmE1tq5p5WNc0Nm+0uZrVIRjOEn04hpoqBE83RxLKQNehnEzxWSjHgug4AP\nzRkb9I8VxEf5LvQmNneGxkvFGT02zxBJvtdXpWghPlH4wD/v8KMEISPCRLk3\nVObNo20n8uJZLmFUwcTbZDQ74cpyRKwVTkiZDQoOVmuTNHMjia/yjugntD2v\nR9uBou6++vpPY5f6oNEVwnPiZ+a2b6/mIZsdDjU4D5Wr7VkPRw0jkKWQunh0\nww7xa4MokcM9QQ33lw584OkDMLXezHi3PsJntLy7rcCH9UEn+k/+mLYJGvDO\nJCergdO0vB6AVT7klWRdmvPcKeyZgSwoqyayBmy+jUbkJem6ngUVvXxu0BNF\nOTTk\r\n=iCjG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAts42ZqkTvPe1r9cfN2TK5gG7V43jX+6/Je65OLZS+xAiAEqG8VIsm8nijPzkxd7khInNsdBtDa/W04vmdkeW+ndQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-bigint_7.4.3_1554234942612_0.02015052520350258"}, "_hasShrinkwrap": false}, "7.4.4": {"name": "@babel/plugin-syntax-bigint", "version": "7.4.4", "description": "Allow parsing of BigInt literals", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-bigint"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.4.4"}, "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-bigint@7.4.4", "dist": {"shasum": "3c6f77ba5f4aefbabfd5183117278c9695971093", "integrity": "sha512-tXyL/mlth1QBl5OwpsABCmAWm4u9xvUIk5nsLd2wWKrC6C4Qm9JvmN18IAGlIXkMvWZTRJVuGRXSLCuz+1hG9Q==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.4.4.tgz", "fileCount": 4, "unpackedSize": 2574, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3I1CRA9TVsSAnZWagAAjbYP/0HPf8PjAefx2VC7mvyX\nElbsxfOONuuE7noNTZMXTA9b/1eSEOe/LJtk8A4CywkRiBDgavwDzSCdb4AI\ncJW7lKU9uuvPbtmmQuWh7t2Vh4Unwh1yESdjx816yTd9RKtTPOBS7chLZgyG\nRtyH5wtzFU+smTRPKUoQB2tvJZkVtxxDzToZEM+GlvBc7UZUPRTRnJlG46jx\nJvgSvHYYWKYc2UJl+AM67N6xG3hCBXFq4f2o9cbNg3mKtt4IjESZd1/CUGJJ\nZZIevMaOzA6RorDbLCgsQNLQ10Nhh10yEkvczHomFj9l3bZFxIEnCSHemchZ\nbQIu+sup+AOuR+8q45Uwo2tH53K10tcVe3MbvRRW+w2JuqaXOVvzfmZNeRq4\ncabBbKUXts0QfU87Rlf5W23YaJF6EDAXsDcU/w0I+ATgu6Fp1kG9CTD+2taa\n5IVNgkJqRK3pZQvJiY0PwKNzGVNbNSiNBetIAyjQgK1mP8XA/dlGfXADD8Er\nxPqG51zluOPVzY8UOdnLFd5umLB65EKgr4xAzvDMQxnQ4wKV1w0IAlWOEYiO\nJFwHdbC1029YE7l0FS0+ZOVusgboX2dE2KxjEw5RVnHW6brmVYD32Js6DNMp\ny6WGDaRtJn+9u2UggQVZIkHm7j5ReugvobvPBcGFngvhvxk8DCiE0w8VkcuO\n+MzR\r\n=PvGf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHNi18PI93qqtv3iyWGQ7T8mmH/8BmtYhSnV2DOg3WthAiAsrQBSXwlR5HUoy/eNxP0ZvQyFpylqTC9/IzfRny5mCQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-bigint_7.4.4_1556312628497_0.9523134400869273"}, "_hasShrinkwrap": false}, "7.7.4": {"name": "@babel/plugin-syntax-bigint", "version": "7.7.4", "description": "Allow parsing of BigInt literals", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-bigint"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_id": "@babel/plugin-syntax-bigint@7.7.4", "_nodeVersion": "13.1.0", "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "dist": {"integrity": "sha512-tJrMYYtGWhvCpXbpCQ98MGzAHG4jeMo9d7eGjrVtGLetyDahD3hCJ/Kpw1IAoR7STXmWVR420MsmtBGw2+DJIw==", "shasum": "b5bde16d826f977eddb2319e5bb54edefb53554d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.7.4.tgz", "fileCount": 4, "unpackedSize": 2472, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/jCRA9TVsSAnZWagAASWIP/1kmVinKUX5Hcc/0Y3cA\nlXr/TUh7NzIYJxsq4EKNOrrYqBQW6r0n1eD2dQIOfMeEUmHUvI2FoYFJqckc\nXNAZ0j4tC+XzJeBWWbP9kx/bm5VQ8SAxmugwbM+8otp/deQALkHgg0H6tp75\naKOeY3wkwBMCvI8WxBzGm6tuNqam1KhZSUXuxowFGx2ltTMH61mdlAvNdSR7\nXHO27V1veXJoHZpvu/NokM/u5XANS5iPHYiEUmp8YK3FLgN00U/t63l43Hnq\n2sp99NRgkvI3oC6T+v+kyamDVa7jaTeHsB+sQH6E0zJzF2Q/1kMlegVT07XI\nBmeC81r1SHjalRowh0cX70LMMyeXXAsTKiq73f495sL8y5TpdXloGuIrb76X\nkXQa1mh00D0kp1H7s4rggAj9wYKOXEht+rCZRQ/N8aXYoDmeEZSkCTKkLSX2\nZmgMjNGB3J9RCjBZOBMU9LYEwOg2lz6hMdLKVgQOah3rtaiLKEqar8LXWmnH\n6qUg//zZ0Q3GZc9vSFWil0e+Tf0Bq0uHfGuZ/TjAjPm6NLHfCUnnsLSVxVmA\nfCnPjOibH4VDbOecaQ8MGHm6q188uU6/q8u1UHJk3VklZN6fm3HweJ97PmfU\n1WUnqvtPNoCmpbIWL5IYZYP/hIL2FA72qlOiZ2ODr8OA+jlEE8qDum4+virm\nZJRA\r\n=ADo6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC0p3zWzPBcn9GrBp+YsMlm29O8Sinoqbm5LRu3VtL1aAIhAIdWottcO2dBDyGoS+FGdXqgrXOUOAhnO6vgi0Zb5y05"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-bigint_7.7.4_1574465506877_0.9910051351392279"}, "_hasShrinkwrap": false}, "7.8.0": {"name": "@babel/plugin-syntax-bigint", "version": "7.8.0", "description": "Allow parsing of BigInt literals", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-bigint"}, "license": "MIT", "type": "commonjs", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0"}, "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_id": "@babel/plugin-syntax-bigint@7.8.0", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"integrity": "sha512-eIZROuH12pAfVQDLwCZQq7tE/Kq1amLKlgJ687CdRGdzbwtGG6QOvws23TjH1MI8BXGP6glGjl5ZnEn+WXZyvQ==", "shasum": "84dae63f5baccfc476cdd39d07891a962bb7685f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.0.tgz", "fileCount": 4, "unpackedSize": 2494, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVJCRA9TVsSAnZWagAA5x0QAJaTMwDBXzWfYrteRiCV\nbAZlXAe9nzDXZZXvTfyLEXUcx/RaNFmTRZ7rPvXUlbtkF+gn2wSAjA2xrY7Q\nWYCmIX58AJD2oCdAGIGHRw74vDwaQ8b2ucZBF0B2Qnt9lAwa334yvVwbKqvD\nRFhLwAr1HU5LkOa5jLWTMhgIfUk+8kfJwSvVPytAnTvGWSaWLgebP0C9BUIf\nGDYGJ7ZZUdhVEfVHvrU7CBzsBtNIpRsIEtxn7CPo5PhqeCZxhcBHJm+RSbp8\nBz9KkIyQPoKMD1a+UCF1YMb9UAnDvlQYNncTgJ9yvT9rTT7kuqFuRLUe7y/4\nr959ueDOINIPjopzr71viEaOxdWmc3VHCaCPMJX9O0dTl1cVJIZEt5I682PJ\nfkM+L/Biv46EH8rDagf1919eOr4JgipCMB4IHfTWXIhGjVzjVibMEjXXQB9N\nlAQmWsTJpzGuy/ZRWkVrPH9U6E/6EYVHA+ZQVsk4Vt50CY44MTGSyscWijHU\nG5oK18T1kzuwBJ23lRG2QScWPLv3Fz5B2t64Y2hxcrnDVK1ZLVjiaVqomtOr\nebgzNbaaqFcvExpdtoiv6IjY17CyjfdYsWbYHvlOjEMRSRN/IZHZJzBNHwF/\nmsI4h1t7QuVuruVVyq0sUH/lVoLZ5wNzO3xi3X1gVG7slcgBgEnZKrZRcCpf\nxu4y\r\n=aG3I\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGj03ENQVGPqQ1nspS9g8K4w7cfKi5VyVEE/iSq+Od7IAiEA6LzlH2BpK5+YMAOgap7FvbVReKNZmZrW1hjhfOzOfIM="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-bigint_7.8.0_1578788169193_0.6841021966729834"}, "_hasShrinkwrap": false}, "7.8.3": {"name": "@babel/plugin-syntax-bigint", "version": "7.8.3", "description": "Allow parsing of BigInt literals", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-bigint"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0"}, "_id": "@babel/plugin-syntax-bigint@7.8.3", "_nodeVersion": "13.6.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-wnTnFlG+YxQm3vDxpGE57Pj0srRU4sHE/mDkt1qv2YJJSeUAec2ma4WLUnUPeKjyrfntVwe/N6dCXpU+zL3Npg==", "shasum": "4c9a6f669f5d0cdf1b90a1671e9a146be5300cea", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz", "fileCount": 4, "unpackedSize": 2415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHN0zCRA9TVsSAnZWagAAWVYP/0TeBLadQNrQaqsNo9M0\nwulUz5Fmq/9NlXc7RX2cG+c7QifXbKuzJqY6UMaMM+HJZnJKY+4OIuKXc/f0\nsrtg2ZqArnI4x1MG7xYPhJOCMKDLdPy5qP/h9PHCyD+t1zjpOnffNHSq5IU1\ncDQoFESMfSKuU1LK+++PFeWY/hSmqVwU4uyPMTbwukk4p+B60ipDpdu2jHIX\neumDLmAsBUOBXihSmJJ0rrxnWRNhfWHTu8HDcSLumqsfB4Vm4hRz8+XfLcuX\nhgQBKOwcjqn8O+Y2GItXb+9ZZ85H+i7w4z/LGK88ZikRFnWMoZmlDUDN3lcs\nz9K72xE42B9DDF9H9v0KPwnkUMJGZltoP4msuhee2Ls5gTI8m5iTGwswS5pr\nw1yGSQpUM/32NQ987vOhMzfZHtdpbh1YxoskBc9kraq7mFTSpAK+O/Yfyk3r\nA2QANQ8B/k6e3aucBjDzUYAvGDFhINVSEifApgD+hDoHkE0lTqb9DiOgu8CZ\nol5n+Al/1FzgwgSmwiAhd3pVv4Fr3kfG5wa1kYPHTOUuQXnuCw2oACCL9qDd\nuAXV7dEke61J0UJ0srPob3KqCvRR2ud9fGmvAKxC4dQEdQfSIHHiPbKuTSe0\nT3+VTAfkZIPDf4sVIMJ3bu6oRZBD1plExC6zg+SjeCGNf87B4J/WLMti20z3\nQ+vO\r\n=sukl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJodBL1Eaofo74Jk3MvEHsc/ZYN5fBKWF47Hc8FTS3KAIhAKzSJCiI5sCjZObyR8XtgGBBJ/Qg8GSuuQGHgzm5vpUE"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-bigint_7.8.3_1578949939421_0.9511207783876041"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-05-24T19:21:17.124Z", "7.0.0-beta.48": "2018-05-24T19:21:17.457Z", "modified": "2022-06-12T15:00:58.704Z", "7.0.0-beta.49": "2018-05-25T16:00:57.399Z", "7.0.0-beta.50": "2018-06-12T19:46:57.747Z", "7.0.0-beta.51": "2018-06-12T21:19:27.059Z", "7.0.0-beta.52": "2018-07-06T00:59:15.735Z", "7.0.0-beta.53": "2018-07-11T13:40:05.585Z", "7.0.0-beta.54": "2018-07-16T17:59:56.665Z", "7.0.0-beta.55": "2018-07-28T22:06:59.720Z", "7.0.0-beta.56": "2018-08-04T01:04:01.194Z", "7.0.0-rc.0": "2018-08-09T15:57:19.276Z", "7.0.0-rc.1": "2018-08-09T20:07:00.822Z", "7.0.0-rc.2": "2018-08-21T19:23:00.836Z", "7.0.0-rc.3": "2018-08-24T18:06:58.200Z", "7.0.0-rc.4": "2018-08-27T16:43:10.695Z", "7.0.0": "2018-08-27T21:42:20.988Z", "7.2.0": "2018-12-03T18:59:29.827Z", "7.4.3": "2019-04-02T19:55:42.724Z", "7.4.4": "2019-04-26T21:03:48.655Z", "7.7.4": "2019-11-22T23:31:46.991Z", "7.8.0": "2020-01-12T00:16:09.322Z", "7.8.3": "2020-01-13T21:12:19.572Z"}, "maintainers": [{"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}, {"email": "<EMAIL>", "name": "jlhwung"}], "description": "Allow parsing of BigInt literals", "keywords": ["babel-plugin"], "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-bigint"}, "license": "MIT", "readme": "# @babel/plugin-syntax-bigint\n\n> Allow parsing of BigInt literals\n\nSee our website [@babel/plugin-syntax-bigint](https://babeljs.io/docs/en/next/babel-plugin-syntax-bigint.html) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-syntax-bigint\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-syntax-bigint --dev\n```\n", "readmeFilename": "README.md"}