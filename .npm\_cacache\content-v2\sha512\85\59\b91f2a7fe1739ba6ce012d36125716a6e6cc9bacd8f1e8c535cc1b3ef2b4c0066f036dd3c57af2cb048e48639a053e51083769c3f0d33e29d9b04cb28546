{"_id": "pump", "_rev": "134-f0ea8ea67e373c3980adc04a92042e34", "name": "pump", "dist-tags": {"latest": "3.0.3"}, "versions": {"0.1.0": {"name": "pump", "version": "0.1.0", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@0.1.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "666aa286bbee73fa38799dee9ab577102d1ba50d", "tarball": "https://registry.npmjs.org/pump/-/pump-0.1.0.tgz", "integrity": "sha512-810pzskGlba+RVbzkdBr2Q8Mi9irPWiSuKgolD4l5jttRBjOZlDdro7r3phuf08UCHO2CZbb487Sy3z5UXRvIQ==", "signatures": [{"sig": "MEQCIGJD6u4oHfw6/k/fSwW3EXA35byEVZq+JGG5rFPGG1sfAiAgTHbiYSGWtXWPQZLTMypaNx+hOOeD/oiuXGLa1S1szQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "1.3.5", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "dependencies": {"once": "~1.2.0"}}, "0.1.1": {"name": "pump", "version": "0.1.1", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@0.1.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "9e97f8c8de790cbba09b1433b8e08a0695c6f6d6", "tarball": "https://registry.npmjs.org/pump/-/pump-0.1.1.tgz", "integrity": "sha512-lN7XPnR8e2kspMgDvPSHPnekaLAZEABJt5ODM9P4kgdA2EmgACHUqCttYeHUaZDRpavh/gxkg+jYOcvLvGL2Mw==", "signatures": [{"sig": "MEUCIFO2FZDOGz/VGMu0u5j/Ya5aAVXS3OrJSh8ECJS6HdHzAiEAq7zIF7nokKOzrB/B2JqSmUHVsWa8rwoOPkKopfZ9Gsc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "1.3.5", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "dependencies": {"once": "~1.2.0"}}, "0.1.2": {"name": "pump", "version": "0.1.2", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@0.1.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "fa9cd64e4ae7e75cfe79c39021f4bb7742a48654", "tarball": "https://registry.npmjs.org/pump/-/pump-0.1.2.tgz", "integrity": "sha512-8tZ3EZUpwPEjXstz1Y6XabYEnAstrDrDN7/+AikIDensJz91TDl9yLBopOwBt5W0e0wjKDEne1UjA65wuPnkSQ==", "signatures": [{"sig": "MEUCIQDzS63ODyC4PjDm/ypO9mhv3g/P/tqBnxaq4WVmNE9m3gIgK6ReXwUyN1rU3QULK+DZU5MeJes3JbOLeCnYqyjhWEc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "1.3.5", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "dependencies": {"once": "~1.2.0"}}, "0.1.3": {"name": "pump", "version": "0.1.3", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@0.1.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "9df70f0b18e3f54c0ebcde6ba10db546caf76a01", "tarball": "https://registry.npmjs.org/pump/-/pump-0.1.3.tgz", "integrity": "sha512-TLQAOaQFHB/9CH2uro32JJLM/Whv7tmbHUP+uoDEKiJuFWS81oluxibdIfWIlnuB/TLHtf4M901bbCVmpgTIUA==", "signatures": [{"sig": "MEUCIQDgp45CzQ9EegfMXCzRMHth04jWQ2IMFvQgJgqaJxL+QAIgaqIc8FxDoWEiEAN7NXXM1/w+V6rlc2M+kV9LV7HI5ow=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "dependencies": {"once": "~1.2.0"}}, "0.1.4": {"name": "pump", "version": "0.1.4", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@0.1.4", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "827811afcfa66abac3b97e0b115b87fe490f1d65", "tarball": "https://registry.npmjs.org/pump/-/pump-0.1.4.tgz", "integrity": "sha512-HnWdsFfrLYXP7OiKeO9aC3VxapUAfmxxDv8ghWHkJas3R9Q7837h1x95unCB1BcwSAD/e98aXxh3NrRXZBl9Mw==", "signatures": [{"sig": "MEUCIQCAhstmHEmb45Oa8ZRuM6TzFt6LtkP4XoCveaO43zy0rgIgOd1iN1ds89k8xuW1CWZwA8A/OAt3U6jDo/QKA8TeKps=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "dependencies": {"once": "~1.2.0"}}, "0.1.5": {"name": "pump", "version": "0.1.5", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@0.1.5", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "f207211a1023311076bb325e5dd0801238ccbd69", "tarball": "https://registry.npmjs.org/pump/-/pump-0.1.5.tgz", "integrity": "sha512-Efg+Wfu1GaWQlVmJo83N1/9CrXEjWrhQ+ptdsTvVVGRPtQWdVJLLUUl/Av/sLuP0hscjfUftKv3RZR4yNQpACQ==", "signatures": [{"sig": "MEQCIEEghM/9t5LClEo69X/kfJmOLCB2pT+t6I64hhCawje5AiAFPFsNn3Jcn0H5zEbhz+sjYvWK1UvkLO4krLa3PaIKAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "dependencies": {"once": "~1.2.0"}}, "0.1.6": {"name": "pump", "version": "0.1.6", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@0.1.6", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/pump", "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "f0032bffd4f08cbca45f6b05c96d7fac85c71769", "tarball": "https://registry.npmjs.org/pump/-/pump-0.1.6.tgz", "integrity": "sha512-QDjuAiZenBGXe4MU3uT5nDsL1C9VJjlFPy+KevH4tqAgwvcZar8rsaCW4pfws5BkoWdGr5eS4lv6UX37Gt7y6A==", "signatures": [{"sig": "MEQCIDZIkzMViUdc3dRkmGf03HV38kXzUObO+Vk9IYeDUytpAiAl6HH4a20ZN2V5LeXwzPbHdDZ24Dd/9mM8isAoUOntIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "dependencies": {"once": "~1.2.0"}}, "0.1.7": {"name": "pump", "version": "0.1.7", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@0.1.7", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/pump", "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "ce24cf21c27ddb70509131e231f896adce11c170", "tarball": "https://registry.npmjs.org/pump/-/pump-0.1.7.tgz", "integrity": "sha512-5/+vxTGQksj7oTAHLDXjrfRZT9ZLet7XPsvUy+1M81rhtjXb8T0dedn42M/rgk1NNvZ8XG/zxaIxjTBlKPqchw==", "signatures": [{"sig": "MEUCIQDNgzE9YerviY6Sr3bKeeI4u5ikNTehSIAlmS4IUiJtwAIgG9AS9uOdFn4J/xsS9vqn4Eqo9nwL3rc50Fxw2A0vRcY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "dependencies": {"once": "~1.2.0"}}, "0.2.0": {"name": "pump", "version": "0.2.0", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@0.2.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/pump", "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "41c2806a5df966f95130023173f37e8ae09a7247", "tarball": "https://registry.npmjs.org/pump/-/pump-0.2.0.tgz", "integrity": "sha512-bOy3aQ+1GR9P38z7HsHFAbnHKxVXyzpbvFAOUi+uD05sbIjsJmCUY9U4t81VrubHs90tuf4tDkjlG5A5zu+jqw==", "signatures": [{"sig": "MEUCIEU90NQgh0kxYf9jb9UEzsZ9NhTN1VIaaUXsFUAuniysAiEA8hEoW6va3yrhwqzMBFhtYvyGijW5z7JX934YE5sTEuk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "dependencies": {"once": "~1.2.0", "end-of-stream": "~0.1.1"}}, "0.2.1": {"name": "pump", "version": "0.2.1", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@0.2.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/pump", "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "ba51e60ae23543600d81cc83950f2ff6d6e8ef82", "tarball": "https://registry.npmjs.org/pump/-/pump-0.2.1.tgz", "integrity": "sha512-7ey6LCRh/7Qvg18ooC1b2KqHTykjzNKZ0iBAokIuvHMKmoU98LQaXKHohA5sqZcKOIUCbxeuqym8heNnXlfJaQ==", "signatures": [{"sig": "MEQCIG11tfBADhUnrss2vrW0FNYBJKwNjghbwNoOddB+akGdAiA0Mz3O+FDz/x4EfJNaAF1rONw+eoubxjsn9tFOohBJvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "dependencies": {"once": "~1.2.0", "end-of-stream": "~0.1.2"}}, "0.2.2": {"name": "pump", "version": "0.2.2", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@0.2.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/pump", "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "43b392b336f5e3a077345e4178b33db9b3c5251f", "tarball": "https://registry.npmjs.org/pump/-/pump-0.2.2.tgz", "integrity": "sha512-XL5UOqdSrE1uAH4XXjxWN3VOnI5vyM/HIcpILfZbHt60C7+WjA4uA0hrhOUUXqTj+AaaxCMxOdPvIi7chTCPPw==", "signatures": [{"sig": "MEUCIAD2R79xjxuIJLLGqQKTyydv6DIr7SAv5i/r7finf1hLAiEArSID7kEkNtEzg+QStRil/6q/tff7wIc0n4Xwj8QSf1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "dependencies": {"once": "~1.2.0", "end-of-stream": "~0.1.2"}}, "0.2.3": {"name": "pump", "version": "0.2.3", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@0.2.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/pump", "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "8629de0a7f15631e29cb49c513b4e7b44775d8c1", "tarball": "https://registry.npmjs.org/pump/-/pump-0.2.3.tgz", "integrity": "sha512-9+TEL1bFIbbFjRvC113RR35vbRCQcwY1Fpiz6MmVcEJSoVXDiSl+Awzt7dOEoMlwwtZNFn5uZ4P466ltrAedRA==", "signatures": [{"sig": "MEUCIHhMHhly4Kax6X9Est1p+qF+1pcSfIUqbScpgi3YwuUSAiEAwqVY1qJ4V90sMhPHnYmQiDnnKN++Sg/h1F8iFT64k8M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "1.3.17", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "dependencies": {"once": "~1.2.0", "end-of-stream": "~0.1.2"}}, "0.3.0": {"name": "pump", "version": "0.3.0", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@0.3.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/pump", "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "2c51031c988cf6ae3ebe65962dd06bfdac7a85c7", "tarball": "https://registry.npmjs.org/pump/-/pump-0.3.0.tgz", "integrity": "sha512-<PERSON><PERSON>42r41dVdxtmfttIQcY2MZJr1jSZCKNixM9bernSDJ8SJzL4iGJBGoBSTSwhh/OMhgACAAMoHEjfn6CqVcg==", "signatures": [{"sig": "MEQCIG9vk4BXeL9UN6ph37owjUe8pk34s7S5IePYinLD/cCkAiAlqRn0q0op1cU03iII6lgEfHak3/0rc21fYtBtiuFUXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "dependencies": {"once": "~1.2.0", "end-of-stream": "~0.1.2"}}, "0.3.1": {"name": "pump", "version": "0.3.1", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@0.3.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/pump", "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "8ea0878556d8991809bf277bd977abd0825075d3", "tarball": "https://registry.npmjs.org/pump/-/pump-0.3.1.tgz", "integrity": "sha512-PME5eLY4iIacmyJRnWe+gE9yGzniz04XMS3CWR+7gnakf4+AAFxAAx099minzhAlxQJkAYKFrARdOet87HJSxg==", "signatures": [{"sig": "MEUCIE1GJaEws/RFayiE38kB3D6TZUEB5IAOy9nWyCA0IFDGAiEAp7xAkXvEjfEAF+dd1MOqTYBQW9TD3jRr3JB8kJEB0+4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "1.3.24", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "dependencies": {"once": "~1.2.0", "end-of-stream": "~0.1.4"}}, "0.3.2": {"name": "pump", "version": "0.3.2", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@0.3.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/pump", "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "b264874b905408b6048a995fdcc424a27034d76f", "tarball": "https://registry.npmjs.org/pump/-/pump-0.3.2.tgz", "integrity": "sha512-DaQzizxqWPD3/Sxi+wKSOfeF8Tvym93zWPIzX0GdkQEY8CqdKgrlnz0cf8oRYu+67EXtUKmWdKK6W+JcfFvNWA==", "signatures": [{"sig": "MEQCIFdAG/WqW3h9J5rFCiOaL2qF9f6J+fDymkH2fKjgLCEFAiBvsRZgpTXR4yqkmVL+kWO5HUzzgHajWxbvM2ADNwLRWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "b264874b905408b6048a995fdcc424a27034d76f", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "dependencies": {"once": "~1.2.0", "end-of-stream": "~0.1.4"}}, "0.3.3": {"name": "pump", "version": "0.3.3", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@0.3.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/pump", "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "bda1980336f397443674335b6743b3e237f0a9ee", "tarball": "https://registry.npmjs.org/pump/-/pump-0.3.3.tgz", "integrity": "sha512-C9wr2BOQcEaSeEiQ1oHpREgz8+Fn9MjttYLKk+wM3wJu8XRKHg4wQN0HxXJoj3LKhtMf7OM5kAbQGZwIgtAV3Q==", "signatures": [{"sig": "MEUCIQD4rng/sJtp/yb5p5q7WXv15bY86CtxVAKttZSskp8oVAIgPxME1/oYFH9zbDCQ9KCB8BtnFcjGz2IzwDBo+9xrDWQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "bda1980336f397443674335b6743b3e237f0a9ee", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "dependencies": {"once": "~1.2.0", "end-of-stream": "~0.1.4"}}, "0.3.4": {"name": "pump", "version": "0.3.4", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@0.3.4", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/pump", "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "2e025d62199c3372e2a03dc283b583dd4c0c8ef3", "tarball": "https://registry.npmjs.org/pump/-/pump-0.3.4.tgz", "integrity": "sha512-gmvblEO+OHqbLS9r0ntA7gJqJ5UQOP6+ykANfbXMTmhI4XjQAkt6mRytM3WqBIw3+eXKg7HAglHcc6qV23WM1Q==", "signatures": [{"sig": "MEUCIHSJkJoLdF1tINvAJ0fGsk1vcHTcnFK5d2B8xEFl4e4BAiEA86eElSVaYAtstE7gspal7XRibyu76kaJKDQKiaI1gmg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "2e025d62199c3372e2a03dc283b583dd4c0c8ef3", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "dependencies": {"once": "~1.2.0", "end-of-stream": "~0.1.4"}}, "0.3.5": {"name": "pump", "version": "0.3.5", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@0.3.5", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/pump", "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "ae5ff8c1f93ed87adc6530a97565b126f585454b", "tarball": "https://registry.npmjs.org/pump/-/pump-0.3.5.tgz", "integrity": "sha512-U0S7cdRuBLfk1ylK32jc8No8ex3Xj2ok3XOPAYfXXXfVd3zMA2BT5Vl7XxfqXoIioNSxczI0/OGENv68XN7Oyg==", "signatures": [{"sig": "MEQCIBTwBRQhL5QhJyLSZ06sMZddcd44tzGPtU2njpZzV0WNAiBy0Ac0GfH78hnaTnbBPVv2QuP6kXWCmh9PBYJ2zqkSug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "ae5ff8c1f93ed87adc6530a97565b126f585454b", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "dependencies": {"once": "~1.2.0", "end-of-stream": "~1.0.0"}}, "1.0.0": {"name": "pump", "version": "1.0.0", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@1.0.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/pump", "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "f0250fe282742492e4dea170e5ed3f7bc8a5e32c", "tarball": "https://registry.npmjs.org/pump/-/pump-1.0.0.tgz", "integrity": "sha512-YAHUrUBGCFAxPLfsnpVCDzU4MtVMyhnNCBhQ34fhrhETceLULfMM27Rc7Mj6kT8hnk/V02QskZUcMB4enpMMjA==", "signatures": [{"sig": "MEUCIQDxEz0fSRi6ALGp0NaYZ23BTmjPZW9vfaDECCBlxNUtCwIgP0+QErXg+OeXmIQ3bTQW89VBic+8DGKS6evEY4hlx6E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "f0250fe282742492e4dea170e5ed3f7bc8a5e32c", "gitHead": "dc0a3c33ac51a37f2ac3551d1a292620fdc5ad91", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "2.0.0", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "dependencies": {"once": "^1.3.1", "end-of-stream": "^1.1.0"}}, "1.0.1": {"name": "pump", "version": "1.0.1", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@1.0.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/pump", "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "f1f1409fb9bd1085bbdb576b43b84ec4b5eadc1a", "tarball": "https://registry.npmjs.org/pump/-/pump-1.0.1.tgz", "integrity": "sha512-AQkT8yuCm6LmumsJ/Ery8afinLeXPvfdya3ODy8rsyYqvKRnHvDaKyAzrIX5V3VV+dOB+yQcjWUbEjWxg4l0WQ==", "signatures": [{"sig": "MEUCIQCRn6hBdqppQjZ97Lq2NqIhrtb8nHVRJYPdEV45v9RJSwIga1Heh2oh3rVRZHd0mVTDvaWMsTDAUQ0wAlNgdJX442U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "f1f1409fb9bd1085bbdb576b43b84ec4b5eadc1a", "gitHead": "6abb030191e1ccb12c5f735a4f39162307f93b90", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "2.14.4", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "_nodeVersion": "4.1.1", "dependencies": {"once": "^1.3.1", "end-of-stream": "^1.1.0"}}, "1.0.2": {"name": "pump", "version": "1.0.2", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@1.0.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/pump", "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "3b3ee6512f94f0e575538c17995f9f16990a5d51", "tarball": "https://registry.npmjs.org/pump/-/pump-1.0.2.tgz", "integrity": "sha512-0l9Rf87wCGXiNCxHxjixpBTPa0iLYFp6an+fwXp7Yz6Fxyhdo7YiBsV76yqzwajT/2+XjKdiCaCDVIcvyEHqCA==", "signatures": [{"sig": "MEUCIQDvJt8nm59U+2Bgd2MbWAH0BXz8l4GfrxGkscXeq/OzdQIgReUZrLKeAcPddl+ejnBbVy/7JEXuJgU2T+2DsoxA4wA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "3b3ee6512f94f0e575538c17995f9f16990a5d51", "browser": {"fs": false}, "gitHead": "90ed7ae8923ade7c7589e3db28c29fbc5c2d42ca", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "_nodeVersion": "4.6.2", "dependencies": {"once": "^1.3.1", "end-of-stream": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/pump-1.0.2.tgz_1482243286673_0.09530888125300407", "host": "packages-18-east.internal.npmjs.com"}}, "1.0.3": {"name": "pump", "version": "1.0.3", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@1.0.3", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/pump#readme", "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "5dfe8311c33bbf6fc18261f9f34702c47c08a954", "tarball": "https://registry.npmjs.org/pump/-/pump-1.0.3.tgz", "integrity": "sha512-8k0JupWme55+9tCVE+FS5ULT3K6AbgqrGa58lTT49RpyfwwcGedHqaC5LlQNdEAumn/wFsu6aPwkuPMioy8kqw==", "signatures": [{"sig": "MEYCIQD8nI8G0rt/EvpvLt8KDR14yLWbpLLmVrhxPu2Ixo3tcQIhAK1FOdPs2oiMD0nw7MJsfgtC/n+ra22h62ShJxHXB9XM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "browser": {"fs": false}, "gitHead": "28557d8349d8e3b6878f9955d28b4a52c6a44de3", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"once": "^1.3.1", "end-of-stream": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/pump-1.0.3.tgz_1510865055632_0.71864059031941", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "pump", "version": "2.0.0", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@2.0.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/pump#readme", "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "7946da1c8d622b098e2ceb2d3476582470829c9d", "tarball": "https://registry.npmjs.org/pump/-/pump-2.0.0.tgz", "integrity": "sha512-6MYypjOvtiXhBSTOD0Zs5eNjCGfnqi5mPsCsW+dgKTxrZzQMZQNpBo3XRkLx7id753f3EeyHLBqzqqUymIolgw==", "signatures": [{"sig": "MEUCIGKxxsRuD6XLTe5rXVXsnna+maKfAWQMsI5o/KcCFpnRAiEArcbr6968ebSJF2lAxJPrgRgKVi4LIJj/Y86P7+bGi0c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "browser": {"fs": false}, "gitHead": "e3d0c444ceef59ff694c753303a35052e60b5186", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"once": "^1.3.1", "end-of-stream": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/pump-2.0.0.tgz_1512085986230_0.5930282876361161", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "pump", "version": "2.0.1", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@2.0.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/pump#readme", "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "12399add6e4cf7526d973cbc8b5ce2e2908b3909", "tarball": "https://registry.npmjs.org/pump/-/pump-2.0.1.tgz", "integrity": "sha512-ruPMNRkN3MHP1cWJc9OWr+T/xDP0jhXYCLfJcBuX54hhfIBnaQmAUMfDcG4DM5UMWByBbJY69QSphm3jtDKIkA==", "signatures": [{"sig": "MEYCIQDtIrXFBS0/jHJy8Ekj3ttjnJGGgkVNo2bJfgLrK4MIJQIhAIxmZYDGWV9kBO57ukRUh+kW4iud0EWwDjkrsV7DRnW/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "browser": {"fs": false}, "gitHead": "4c93a460459fd970dd963fd9600c75b0e90404c2", "scripts": {"test": "node test-browser.js && node test-node.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"once": "^1.3.1", "end-of-stream": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/pump-2.0.1.tgz_1516638146695_0.7931882732082158", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "pump", "version": "3.0.0", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@3.0.0", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/pump#readme", "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "b4a2116815bde2f4e1ea602354e8c75565107a64", "tarball": "https://registry.npmjs.org/pump/-/pump-3.0.0.tgz", "integrity": "sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==", "signatures": [{"sig": "MEUCIQCnqwEjimrryOA0b/S3hj6MZt/VsYtHGq92eiD9Y8fetAIgRSz5+zCGno6tMfrWUZbMfzQ7fgz9gkAvkj1W0O7kWMI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "browser": {"fs": false}, "gitHead": "b05d1838cc3be47f7c3cd9020c73f6ebbafd3aa0", "scripts": {"test": "node test-browser.js && node test-node.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"once": "^1.3.1", "end-of-stream": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/pump-3.0.0.tgz_1517391995455_0.9241023743525147", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "pump", "version": "3.0.1", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@3.0.1", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/pump#readme", "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "cf711a61348b26a05ad8ce598e50d7e0ea03d398", "tarball": "https://registry.npmjs.org/pump/-/pump-3.0.1.tgz", "fileCount": 9, "integrity": "sha512-2ynnAmUu45oUSq51AQbeugLkMSKaz8FqVpZ6ykTqzOVkzXe8u/ezkGsYrFJqKZx+D9cVxoDrSbR7CeAwxFa5cQ==", "signatures": [{"sig": "MEQCICF+dsyYMdCDLDRueYRpnJFI3+X6hZw3pnDI8ZRf4/aUAiAxDhzcKQav0vxTFk6pK02uUx68zHyDmg3VF9rwpZBTGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8758}, "browser": {"fs": false}, "gitHead": "1b0ac2d4dc5f4a0e9bc28308dc386e26cdc34f44", "scripts": {"test": "node test-browser.js && node test-node.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"once": "^1.3.1", "end-of-stream": "^1.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/pump_3.0.1_1725987597739_0.08848482567192217", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "pump", "version": "3.0.2", "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "pump@3.0.2", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/mafintosh/pump#readme", "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "dist": {"shasum": "836f3edd6bc2ee599256c924ffe0d88573ddcbf8", "tarball": "https://registry.npmjs.org/pump/-/pump-3.0.2.tgz", "fileCount": 9, "integrity": "sha512-tUPXtzlGM8FE3P0ZL6DVs/3P58k9nk8/jZeQCurTJylQA8qFYzHFfhBJkuqyE0FifOsQ0uKWekiZ5g8wtr28cw==", "signatures": [{"sig": "MEQCIAK+IE4tnHrlXzTmP1pNKtUG3ISSorWI9KZFhSKcVJDhAiA5BZrmsmLnacIndJGU8Cun+ngAghpKQggBLF9E/q3e1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8762}, "browser": {"fs": false}, "gitHead": "16e858ed6594ef22f8daade05613d3a9b0e3a139", "scripts": {"test": "node test-browser.js && node test-node.js"}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/mafintosh/pump.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "pipe streams together and close all of them if one of them closes", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"once": "^1.3.1", "end-of-stream": "^1.1.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/pump_3.0.2_1726041513856_0.31858505274469184", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "pump", "version": "3.0.3", "repository": {"type": "git", "url": "git://github.com/mafintosh/pump.git"}, "license": "MIT", "description": "pipe streams together and close all of them if one of them closes", "browser": {"fs": false}, "keywords": ["streams", "pipe", "destroy", "callback"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}, "scripts": {"test": "node test-browser.js && node test-node.js"}, "_id": "pump@3.0.3", "gitHead": "723ecd008c5b7c3021573b864402f01b7005f297", "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "homepage": "https://github.com/mafintosh/pump#readme", "_nodeVersion": "22.16.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-todwxLMY7/heScKmntwQG8CXVkWUOdYxIvY2s0VWAAMh/nd8SoYiRaKjlr7+iCs984f2P8zvrfWcDDYVb73NfA==", "shasum": "151d979f1a29668dc0025ec589a455b53282268d", "tarball": "https://registry.npmjs.org/pump/-/pump-3.0.3.tgz", "fileCount": 9, "unpackedSize": 8803, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDvYZvcaaCUQSIZxEU3fXCV48swbK7MhtUH4Y9TqXSNJwIhALKIkSXy07c55SN+lT4vhF3FVtih831Tvr0aWhydHyAT"}]}, "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/pump_3.0.3_1750065359389_0.6902651510302507"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-08-14T20:03:55.441Z", "modified": "2025-06-16T09:15:59.764Z", "0.1.0": "2013-08-14T20:03:59.214Z", "0.1.1": "2013-08-14T22:41:45.452Z", "0.1.2": "2013-08-15T05:58:55.737Z", "0.1.3": "2013-08-22T16:58:38.299Z", "0.1.4": "2013-08-24T11:46:46.802Z", "0.1.5": "2013-08-24T14:56:48.720Z", "0.1.6": "2013-11-24T10:25:07.649Z", "0.1.7": "2013-11-24T11:06:56.909Z", "0.2.0": "2013-11-27T01:13:58.376Z", "0.2.1": "2013-11-27T01:19:27.732Z", "0.2.2": "2013-12-01T16:47:22.667Z", "0.2.3": "2013-12-26T09:09:54.429Z", "0.3.0": "2014-02-12T09:56:15.952Z", "0.3.1": "2014-02-18T10:19:54.895Z", "0.3.2": "2014-05-07T15:02:54.986Z", "0.3.3": "2014-07-12T12:12:30.589Z", "0.3.4": "2014-07-12T12:15:23.088Z", "0.3.5": "2014-07-21T11:09:43.572Z", "1.0.0": "2014-09-24T04:16:11.221Z", "1.0.1": "2015-10-23T14:31:11.866Z", "1.0.2": "2016-12-20T14:14:47.280Z", "1.0.3": "2017-11-16T20:44:16.536Z", "2.0.0": "2017-11-30T23:53:07.172Z", "2.0.1": "2018-01-22T16:22:27.684Z", "3.0.0": "2018-01-31T09:46:35.636Z", "3.0.1": "2024-09-10T16:59:57.894Z", "3.0.2": "2024-09-11T07:58:34.009Z", "3.0.3": "2025-06-16T09:15:59.585Z"}, "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/mafintosh/pump#readme", "keywords": ["streams", "pipe", "destroy", "callback"], "repository": {"type": "git", "url": "git://github.com/mafintosh/pump.git"}, "description": "pipe streams together and close all of them if one of them closes", "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# pump\n\npump is a small node module that pipes streams together and destroys all of them if one of them closes.\n\n```\nnpm install pump\n```\n\n[![build status](http://img.shields.io/travis/mafintosh/pump.svg?style=flat)](http://travis-ci.org/mafintosh/pump)\n\n## What problem does it solve?\n\nWhen using standard `source.pipe(dest)` source will _not_ be destroyed if dest emits close or an error.\nYou are also not able to provide a callback to tell when then pipe has finished.\n\npump does these two things for you\n\n## Usage\n\nSimply pass the streams you want to pipe together to pump and add an optional callback\n\n``` js\nvar pump = require('pump')\nvar fs = require('fs')\n\nvar source = fs.createReadStream('/dev/random')\nvar dest = fs.createWriteStream('/dev/null')\n\npump(source, dest, function(err) {\n  console.log('pipe finished', err)\n})\n\nsetTimeout(function() {\n  dest.destroy() // when dest is closed pump will destroy source\n}, 1000)\n```\n\nYou can use pump to pipe more than two streams together as well\n\n``` js\nvar transform = someTransformStream()\n\npump(source, transform, anotherTransform, dest, function(err) {\n  console.log('pipe finished', err)\n})\n```\n\nIf `source`, `transform`, `anotherTransform` or `dest` closes all of them will be destroyed.\n\nSimilarly to `stream.pipe()`, `pump()` returns the last stream passed in, so you can do:\n\n```\nreturn pump(s1, s2) // returns s2\n```\n\nNote that `pump` attaches error handlers to the streams to do internal error handling, so if `s2` emits an\nerror in the above scenario, it will not trigger a `proccess.on('uncaughtException')` if you do not listen for it.\n\nIf you want to return a stream that combines *both* s1 and s2 to a single stream use\n[pumpify](https://github.com/mafintosh/pumpify) instead.\n\n## License\n\nMIT\n\n## Related\n\n`pump` is part of the [mississippi stream utility collection](https://github.com/maxogden/mississippi) which includes more useful stream modules similar to this one.\n\n## For enterprise\n\nAvailable as part of the Tidelift Subscription.\n\nThe maintainers of pump and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-pump?utm_source=npm-pump&utm_medium=referral&utm_campaign=enterprise)\n", "readmeFilename": "README.md", "users": {"avvs": true, "bura": true, "mrxf": true, "akiva": true, "cshum": true, "mskjp": true, "agplan": true, "ajduke": true, "daizch": true, "iseric": true, "klaemo": true, "knoja4": true, "monjer": true, "mradko": true, "mucbuc": true, "rajiff": true, "rexpan": true, "zewish": true, "alectic": true, "ceejbot": true, "drewigg": true, "itonyyo": true, "sejoker": true, "ungurys": true, "coalesce": true, "eagleeye": true, "ifeature": true, "lmangani": true, "maxogden": true, "moimikey": true, "nicknaso": true, "flockonus": true, "jjonathan": true, "jmiziolek": true, "maoxiaoke": true, "peunzhang": true, "raschdiaz": true, "roccomuso": true, "stone-jin": true, "timhudson": true, "axelrindle": true, "davidchase": true, "farskipper": true, "kuzmicheff": true, "nwservices": true, "oleg_tsyba": true, "quocnguyen": true, "rain-again": true, "rocket0191": true, "seangenabe": true, "shiningray": true, "charlesread": true, "icognivator": true, "jonatasnona": true, "krampstudio": true, "onlyutkarsh": true, "alexjeffcott": true, "dpjayasekara": true, "mobeicaoyuan": true, "norbertparti": true, "stringparser": true, "zhenguo.zhao": true, "diegorbaquero": true, "robmcguinness": true, "matteo.collina": true}}