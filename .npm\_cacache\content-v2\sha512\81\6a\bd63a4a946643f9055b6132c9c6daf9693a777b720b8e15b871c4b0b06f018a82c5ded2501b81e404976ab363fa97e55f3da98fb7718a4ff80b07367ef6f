{"_id": "ffmpeg-static", "_rev": "63-7413634737eda12ede1a167aed42523f", "name": "ffmpeg-static", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "dist-tags": {"latest": "5.2.0", "next": "5.2.0"}, "versions": {"1.0.0": {"name": "ffmpeg-static", "version": "1.0.0", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"test": "tape test/*.js"}, "repository": {"type": "git", "url": "https://github.com/eugeneware/ffmpeg-static"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "dependencies": {}, "devDependencies": {"tape": "~2.3.2"}, "gitHead": "b44ab375edfdf7faa75d7ebb5035d85219a7cb7d", "homepage": "https://github.com/eugeneware/ffmpeg-static", "_id": "ffmpeg-static@1.0.0", "_shasum": "c18e5d9d13c38d78f6e88ad7c3adb794c135247b", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c18e5d9d13c38d78f6e88ad7c3adb794c135247b", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-1.0.0.tgz", "integrity": "sha512-cXhSnCFoV/VFD9HmMZIXsl2KhPSTVBxGB3PJflz9quFedSTh7FSvPZwXFU9JSZfjjRxkvEstr+T9b/UiBwjBBA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG7tszGvxAiyDNMI6uW5G0kjVzAS6Zjj/rMQG6qB1iFfAiEA9Ukgj7mu+vw1D2JGmeGCSOLApIsMVxnBlwaxN/0Zo8Y="}]}, "directories": {}, "deprecated": "ffmpeg-static@1 won't be updated with new ffmpeg binaries anymore. Use ffmpeg-static@latest."}, "2.0.0": {"name": "ffmpeg-static", "version": "2.0.0", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"test": "tape test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "dependencies": {}, "devDependencies": {"tape": "~2.3.2"}, "gitHead": "cdedd02ef5b44c11709425a5ff021157546b413f", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@2.0.0", "_shasum": "b060989a6c04d567ba2ad3d4027e3877b6dc72fb", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "0.12.15", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "noblesamurai", "email": "<EMAIL>"}], "dist": {"shasum": "b060989a6c04d567ba2ad3d4027e3877b6dc72fb", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-2.0.0.tgz", "integrity": "sha512-UO9/GINE0yiYd8gKpzi5fm6Iy71elvDDu4xNCR3jp+4Nd1EtVx91OPsIdaTjuMT4SF0NpxD+8ZfyyhBI/1y31w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDMb6ZsZ+fEja+4V2Q+kzdklBrF2UvXwZjqtAUIoWKTCgIgLgeJANk6gIKQ2E2DhNMfElaqZ4kdFWprUazJq8xQMrw="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/ffmpeg-static-2.0.0.tgz_1467268181965_0.1547241739463061"}, "directories": {}, "deprecated": "ffmpeg-static@2 won't be updated with new ffmpeg binaries anymore. Use ffmpeg-static@latest."}, "2.1.0": {"name": "ffmpeg-static", "version": "2.1.0", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "files": ["index.js", "bin"], "scripts": {"build": "./build/index.sh", "test": "tape test/*.js", "prepublishOnly": "npm run build && npm test"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "dependencies": {}, "devDependencies": {"tape": "~2.3.2"}, "gitHead": "a1ae1c18e135c13d6bb26ecd2d6f0b85c1bcc2c0", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@2.1.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.1.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-tSX2l1iuy6BxSMTNCPheERVmYGQf4NvkQ9feNG8dQ9rRsAIOFMfDc80L78IWPh3WhITa+SSf+wmiIeCR7mHTtQ==", "shasum": "a5562cf5452758c7ba456a11d156c3f8fd35526d", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-2.1.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCPuWNfF6r8YuDYR/Q+Twsdf39mZNsi73DkJH2JeLVUpQIhAMwYCOUMGm6M+r/yQ1p4rGvioxLHP42sdKgs3ZN7QalX"}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "noblesamurai"}, {"email": "<EMAIL>", "name": "tomns"}, {"email": "<EMAIL>", "name": "wayno"}, {"email": "<EMAIL>", "name": "jeffroh81"}, {"email": "<EMAIL>", "name": "aeh"}, {"email": "<EMAIL>", "name": "tim<PERSON><PERSON><PERSON>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static-2.1.0.tgz_1511931612120_0.5332535908091813"}, "directories": {}, "deprecated": "ffmpeg-static@2 won't be updated with new ffmpeg binaries anymore. Use ffmpeg-static@latest."}, "2.2.0": {"name": "ffmpeg-static", "version": "2.2.0", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "files": ["index.js", "bin"], "scripts": {"build": "./build/index.sh", "test": "tape test/*.js", "prepublishOnly": "npm run build && npm test"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "dependencies": {}, "devDependencies": {"tape": "~2.3.2"}, "gitHead": "742288592bc851591d00912529a9bf85949a8bee", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@2.2.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.7.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-BbbRnQrIRMATWgSxQlP0uRy14RC2kykh5U7+d0Wn558poosVeN2wtp1JF9vASmBny2UMSnb5o/91JfVyj8Ka0A==", "shasum": "68f7fe56bb0248050d2d2902a3049f2fb42099b9", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-2.2.0.tgz", "fileCount": 14, "unpackedSize": 207268303, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFeYRfJdNRazhEYXnJgdLKPE9PsRDFPBWq9bcZ54SbRGAiEA8cwQohNZ7jDMPBpgWpeyrEP3MSz9Pdm865HssccPEyg="}]}, "maintainers": [{"email": "<EMAIL>", "name": "aeh"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jeffroh81"}, {"email": "<EMAIL>", "name": "noblesamurai"}, {"email": "<EMAIL>", "name": "tim<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "tomns"}, {"email": "<EMAIL>", "name": "wayno"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_2.2.0_1521138065296_0.70235788386837"}, "_hasShrinkwrap": false, "deprecated": "ffmpeg-static@2 won't be updated with new ffmpeg binaries anymore. Use ffmpeg-static@latest."}, "2.2.1": {"name": "ffmpeg-static", "version": "2.2.1", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "files": ["index.js", "bin"], "scripts": {"build": "./build/index.sh", "test": "tape test/*.js", "prepublishOnly": "npm run build && npm test"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "dependencies": {}, "devDependencies": {"tape": "~2.3.2"}, "gitHead": "ba16b848148f1e668fb3fe435d4f8e13eb47a777", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@2.2.1", "_npmVersion": "5.6.0", "_nodeVersion": "9.10.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6gg18ZD4IsvrpvTYMaxNOH9L8oELGjV/HDcT9X/45EAQKDfNVdZEC4x4vhXWxp6YAMg9VFeRA+r0cGQCRQqRtA==", "shasum": "c2599c01cd43928a38f68af59eae3b4cf1c13172", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-2.2.1.tgz", "fileCount": 14, "unpackedSize": 233262303, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDINj5fK7DaKddfv86XKBN6Vsrojex89N6wWZG3idhyDQIgB1uFroARVwoQ+neC4x3DYVVFgk0TCwE22Omb7mDaPJU="}]}, "maintainers": [{"email": "<EMAIL>", "name": "aeh"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jeffroh81"}, {"email": "<EMAIL>", "name": "noblesamurai"}, {"email": "<EMAIL>", "name": "tim<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "tomns"}, {"email": "<EMAIL>", "name": "wayno"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_2.2.1_1523524079071_0.30060519502646277"}, "_hasShrinkwrap": false, "deprecated": "ffmpeg-static@2 won't be updated with new ffmpeg binaries anymore. Use ffmpeg-static@latest."}, "2.3.0": {"name": "ffmpeg-static", "version": "2.3.0", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "files": ["index.js", "bin"], "scripts": {"build": "./build/index.sh", "test": "tape test/*.js", "prepublishOnly": "npm run build && npm test"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "dependencies": {}, "devDependencies": {"tape": "~2.3.2"}, "_resolved": "", "_integrity": "", "_from": "file:ffmpeg-static-2.3.0.tgz", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@2.3.0", "_npmVersion": "5.8.0", "_nodeVersion": "9.10.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Cb6AjBnybjlxrqBEgrnwRMeQFiyQYUd1C8SsRPabJ4Glc7SD//QHYnDuZlNX5E6sdMitnINkADFGREUtY8lhuQ==", "shasum": "36976dc432a8df269c62aea705256bbd9fb66a94", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-2.3.0.tgz", "fileCount": 14, "unpackedSize": 264653618, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6Z8UCRA9TVsSAnZWagAAOCQP/2HrybMs7KfZCXBjlWJz\n98QXm9WKNYCw/RrREPrRzHEL8q7CYoeVKumZVnXNeIBclMombeCQZ1iqvoKx\n7tqOwywULjHwXRKz2x8X3FFdTn6E1rE7MVZy+JVe81IAshgu6Ocoi0IPEzqJ\nNY/pXkUjOWtaRGEJOF0TIUNBPyFZ3mcA9A8cR0kgxRWrtGISHxT+01vCG7Uq\ngrH6GYJ/0GOMmqQUkzBj4niblLk+FkEnt6b0eG9PHt3V31ajUBDtmVCFwNIA\n8wLV1dPO6dRbTjCVRq8eM2G8zs1wmJWLDovhv2n/wvpIBLrapZ+fOEuKYuFv\nT7MrpK1AZb4gJYd2CL+bVqnOtLtAS+OU2F4UB3Kf188LpV/BR4KQTMbV9h/N\n96aQ0WRCfW9i0AjgGpInzbXm5pRaLvP9rUC5Vo6vWcm3jWdyM5BQu+i41Rmk\nzDV1rEB10AvTaU5d+JZHQoHUvtxerLdN5XFogMyKHWehO8kBseM+Gbqa4bUY\nX0Gfpm2MXOYGUb7ncd0Vl+97PQAH2KJrHpeBQwYfsjP1ebDz3YKpoQC0oiR3\n+q6PQzp1sQQ2bttt2GhBaxW6X++jw1t7Fbsf+0NZsARiRk70yqH6tr5wXiSt\nWySJD7RkzUa3W/QiVYY1XRm0uTjT355YFr2ULHRxvhUkk6BwdtpjA9WpUDGW\nUiZP\r\n=ZbTM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICvIM0TdfTjG02fPlfC58ZzK8klatz+OMwMSR8EPlhZyAiEAlpEoZc6OxYcH6YSP5UixXEggMVvi/5Jl7P7YF/naqVc="}]}, "maintainers": [{"email": "<EMAIL>", "name": "aeh"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jeffroh81"}, {"email": "<EMAIL>", "name": "noblesamurai"}, {"email": "<EMAIL>", "name": "tim<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "tomns"}, {"email": "<EMAIL>", "name": "wayno"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_2.3.0_1525260048661_0.050122348422140695"}, "_hasShrinkwrap": false, "deprecated": "ffmpeg-static@2 won't be updated with new ffmpeg binaries anymore. Use ffmpeg-static@latest."}, "2.4.0": {"name": "ffmpeg-static", "version": "2.4.0", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"build": "./build/index.sh", "test": "tape test/*.js", "prepublishOnly": "npm run build && npm test"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "dependencies": {}, "devDependencies": {"tape": "^4.9.1"}, "gitHead": "a37605c92a5b9f7921153b492364376a77f2291f", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@2.4.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-OcYTewd3we/Zi4W+VJtEqrfDzRSMwLs0NyAa61qw/FqtbttidGrm+BFsq44prtT3s6/LV6+aoXNpyNBOshXBuA==", "shasum": "eb3a248849943d493c2a311c0e5e45a06259103a", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-2.4.0.tgz", "fileCount": 14, "unpackedSize": 280664598, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb8yO/CRA9TVsSAnZWagAA9NIQAJfOML6fstyiw//jhEkc\nmDNlwjFYyZ1iyGkcjrBxsL82/qynnZH531NEohXa5d16RCkOMYV9hMZFyqAY\naBHt7gP5X/aMWTArkU9iyy56ESvDRtKgFLh0KaxuqdCB3CgG62rXVwiMLX/z\n4kXuvRHj19NvUeb2r4sijidOsHmX8TDXeDqG35I5rpn0DRXULltJhCRbC6Qh\no1a74HLiseOWVvEvog9HXpPWjcbVlT72b0SO/GNLPhhlU5lx146PCOWx8cNq\n6mDjx8ia/YnR6I3+WYYrf67aHEqVV9j9A+IuezBgcZVIXaEBKZ//HycLdgQd\nuRizvV9v7S1AGdY3exG5IEcfbygjMtg1rT7CiFAfrZPVDFR5AsiJdGSSmGcU\n15yOHyzEUs17WsQg1Mib6U3gmjVTMo/au/25bL7OA5P9Tn92NUwqvhqNfJ1c\nw3yln7WydGxH8mViJNFzN3cts99n/GzWis5lASzk+0+lCRkEx/wewEa7PbNg\nU/TojWxopKIns0CqNAub3Q8qgimJ/4eOKuD8qoKNu7xO1wFmx0WSF70L/emn\nIRfexy33Mb6KTY8fCUPLc39hatcElhnjO7KlDcao6X9GzBvSjqFEvxckNhIA\nCdK3zOfHdbprSYrC20xpKUvfmff8kw+bRh35iwO/l0CnO+7nGQzRjEHftC4u\nfCHk\r\n=n8/3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA2Pv2xRB2Rcs6FpmjV/ciSoWLNnnkaY6STVAr9/KKAFAiAqRHCVcT1g2glWP6YKIyzJeBAX2qcvyHg0Wp8hG5kLgQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "aeh"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jeffroh81"}, {"email": "<EMAIL>", "name": "noblesamurai"}, {"email": "<EMAIL>", "name": "tim<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "tomns"}, {"email": "<EMAIL>", "name": "wayno"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_2.4.0_1542661053199_0.7298234985161764"}, "_hasShrinkwrap": false, "deprecated": "ffmpeg-static@2 won't be updated with new ffmpeg binaries anymore. Use ffmpeg-static@latest."}, "2.5.0": {"name": "ffmpeg-static", "version": "2.5.0", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"build": "./build/index.sh", "test": "tape test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "contributors"], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "dependencies": {}, "devDependencies": {"tape": "^4.9.1"}, "gitHead": "b5a9af6153cb4ad38bfd54b3f4cb424bb3daf843", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@2.5.0", "_nodeVersion": "12.4.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-4HI5aUFq9As3vNOm/0p4Pj47D8AxUqg0qnzaOuXwFMQ6uYhaLXGneUX/8b3C6gbU4ljASI+Ne7WKHpYFnLtK3A==", "shasum": "310fbd7f55bcb55d6dec04eb96dc0953851f5076", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-2.5.0.tgz", "fileCount": 18, "unpackedSize": 356203347, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIkDhCRA9TVsSAnZWagAA3oAP/2EIEzovGCFDGxQtjFVe\ny8ATLc/wMmSS1THWfBdWZ56xMiPEfIxa08AzDjH9V0lu1ow96P9JkpA3hIvD\n2fHViGakla1G0B39MpjUzy+U6rPT+4gGKql3EGuprUj3GqmxQm2nE6pGsshi\nI1D6cdUdD0cztDPQWhNHT77gEqYOETQ49vFmsMwBl0uhlPfnX4j5cHVLNQw2\nOBxPGU6qp9py/OOAskeDJ/JTyWFAuZdbyVw/XBps/2uMIdQ1X169jjX/7Jqb\ne8RJCwvZkv7Vz/rW8CMCMaUiwYLeIHrvb285A/0ocHUhMIBlgj8Ma/xKloSB\nzlT6xZIIsoCovmnUUJBLPCLKAh59wzEtrGCahgGXIZC3JGs1++9bDqQSV2s2\nrwJdOoL5HHumiSaI7hMQLQNnYAcmNBKDA+NOaP5iYoD6L0zk+82+Ly/dEIM6\nZUoq1zVBQOnxHpfved+ByFoLhWlva1qtYU5VCmwhB59R84Nvakds845ePLgL\nDB8BVru8RXNcLAHo+FuOaCjzI62QzKcxuDfAuiutx193JuFIa4d33bp99W7A\nOZM7hq70+hc5dDnW9J0v7/7IGcLbeCTIPXgDIZ7dHQfu3fLSiOnoLBjCRLkD\nD17v4ULz4LGbvyWhT9f+NgIRq3FG2c917xajYk+fwkeByOjAFpQO6yjn9N3x\n82sW\r\n=wl0N\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCyz8/oKe/9cKdisE3e7kQm1APKZtmPWv0SUG5SxsGNwgIgO6mySvn2esFW+1xUPeMe9Z0v75BTi9qtay0DxJ/YRZM="}]}, "maintainers": [{"email": "<EMAIL>", "name": "aeh"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jeffroh81"}, {"email": "<EMAIL>", "name": "noblesamurai"}, {"email": "<EMAIL>", "name": "tim<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "tomns"}, {"email": "<EMAIL>", "name": "wayno"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_2.5.0_1562525918215_0.5878330686940285"}, "_hasShrinkwrap": false, "deprecated": "ffmpeg-static@2 won't be updated with new ffmpeg binaries anymore. Use ffmpeg-static@latest."}, "2.6.0": {"name": "ffmpeg-static", "version": "2.6.0", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"build": "./build/index.sh", "test": "tape test/*.js", "prepublishOnly": "npm run build && npm test"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "contributors"], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "dependencies": {}, "devDependencies": {"tape": "^4.11.0"}, "_resolved": "", "_integrity": "", "_from": "file:ffmpeg-static-2.6.0.tgz", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@2.6.0", "_nodeVersion": "12.9.0", "_npmVersion": "6.11.2", "dist": {"integrity": "sha512-Dt5rQGlqamPKEXiTh4XktpySKt8WZgVVq2fsT+NQVJHPqUL1hEsbNRq2SBFtOmeh9LkT9qiwAF3SgbC5vEPVqA==", "shasum": "6815df9c0220a8ade6636839a5444a5dbaaa3251", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-2.6.0.tgz", "fileCount": 18, "unpackedSize": 387354607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdaloUCRA9TVsSAnZWagAA0nkP/istnrOeg06SlFyHrh6w\nuvIc5n40SsEBbrm4s0+m7DGzplUlzj1DigGl/oQoBQVgBgLAHsvmgO29EOpJ\n+20Q3HGhUeqT8Vo/yAm3KuVChXNJ+Ia9QKV7blZycfhUhQ+pRmdiowVXg47X\ne2xNu6h8kXLKVSBeu89PvWVEu2s5JQVBuMRwkT72aCDwoOxtQcTeOq+EeU8B\nJQyUYP9u/KgQqohPBC5oFwlbBUwlumxxaav8qeHypCNeeQuGDpi+bHrmF+6C\nz5Y4MfQh3K/6DK3Hsmq8kINFyGEAfSR/2Ry84lebHVFI8qGyDjUIl97BrRQC\nsM10FzGgyt1VroSjKFAEQoTYEDld4ofGd6hsbvmhrSNTt3jN5riSa9yMNpyr\nQsh0iXzYJZWslksD4FCTJSkpZOw+v+6yAI6cV2cdDrPz8bKyJsnLVLYtAJvw\nbfWqUZMIDiwcMaxQCYdcHiMiOXOQ9hbxAX+2uFDCikSvVL6WRsGxX9ybeHCq\n8P7f1ALW9sSWSaUI8qThFoShWnh9Z/drJdXRn91/Q/AiiPicGSiGca1yKoVO\nUjK7L3rO6wOSSOo14PuAmfU9EHuqZ8rIvcyCA7gEo2jIaZNzPPjDuKh63gZb\nPMSVTMcPYryz2GGmiusHtW+MpPCysiokG+yue57WV5x1lQu6q+XtpXQ+EbUW\nH7no\r\n=qSn+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHlGP5hMu8tvjMjfOGiXgte8dAs8E6Q0s6qgE/cLPRlYAiEAqT79fM0j4oDrecPwQURhXXXkFQsbxNQ2RBZQ0x9UOY0="}]}, "maintainers": [{"email": "<EMAIL>", "name": "aeh"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jeffroh81"}, {"email": "<EMAIL>", "name": "noblesamurai"}, {"email": "<EMAIL>", "name": "tim<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "tomns"}, {"email": "<EMAIL>", "name": "wayno"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_2.6.0_1567250960464_0.1448840503642126"}, "_hasShrinkwrap": false, "deprecated": "ffmpeg-static@2 won't be updated with new ffmpeg binaries anymore. Use ffmpeg-static@latest."}, "2.7.0": {"name": "ffmpeg-static", "version": "2.7.0", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"build": "./build/index.sh", "test": "tape test/*.js", "prepublishOnly": "npm run build && npm test"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "contributors"], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "dependencies": {}, "devDependencies": {"tape": "^4.11.0"}, "gitHead": "74b46a5d089f7024a3527622ce68c0c82c47252f", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@2.7.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.14.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Khjg/4tCBen58ixhXlkRNqs3hUKpTOlGOUrw859M09tdjeMkXyXRQ+YuJjGczRhGO7Y8fHPJZcQ37V/OzvZvjQ==", "shasum": "e1b050f7c0457595fceccec7d8bb04cdb8f223dc", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-2.7.0.tgz", "fileCount": 18, "unpackedSize": 383722444, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnYULCRA9TVsSAnZWagAA3pEP/jXarPWqIJH9zyRI4Kbu\nfgZJa4iWm5IxT4DmY5/N6qO+7LvVcfzV1rW/whnVhltwL6nA0ZgZ74fLOpcN\nVt3hQPVzDeMPHno+xrlPmCAWzKg5rSX035i1LzHjSG+BPg7BAEZSJ5h3eF0e\nFAtnsNYcrvHuPINxbt/+x+VhNTdlIw9RFbwTTB3QjQ0xsl290MbYZtyAPK9Q\nobmTwoRdjNKZwVpi2HJUriHgfGlNYPIcH8CfVN8iccJdE3D8RfCSz8SPyEAm\n4WDFKe4D0VoXpHrFRDjnQfWF+Lz08axB7zUz3ffX20Lv8DpDdERoy+5TOJIF\nCNP/UUy8YLIFhbglaGBQG37o6RJDtOTciLzUUkxsSoaoLfDCx3W4lVFSWYYH\ntaO7c07jpKb/qEJysdFOuE0J7j82KZi15qDa/j8mNLNIyp4V2PTlYDPb3C8c\nCZkbEk6Z17KlRHzSp0ynIon9DR9tCtGc048AjpVC5RSYVD/mn25hRk65jBRh\naZ7IQ5xNYXamuTGgP2ENwO5XJkyANQueGE2qvRnnLbmHOZe0JRpy9qdjY5Td\nIAQkC1VAb0kCi8Tv0ctQjhz1jbFCO/BMzwP3VxAazf9QBXm4umwTh6ZpQwIJ\n3yEdzUiZvNvCsOo8rLuJJTrnKnj7IJRdqW57ARe1qAs6Luz/XL4smkjFPsJu\nTjZ8\r\n=Vjq8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHDo97lURHRqGSgfps7mYMHuZFQJFQQl+Tv7NbAFDtsgIgJIwC0h1CLJYL1MS1CTGD4a5E4Yv8BfM4a1dxOt/g3A0="}]}, "maintainers": [{"email": "<EMAIL>", "name": "aeh"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jeffroh81"}, {"email": "<EMAIL>", "name": "noblesamurai"}, {"email": "<EMAIL>", "name": "tim<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "tomns"}, {"email": "<EMAIL>", "name": "wayno"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_2.7.0_1570604295537_0.5082519751500725"}, "_hasShrinkwrap": false, "deprecated": "ffmpeg-static@2 won't be updated with new ffmpeg binaries anymore. Use ffmpeg-static@latest."}, "3.0.0": {"name": "ffmpeg-static", "version": "3.0.0", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"build": "./build/index.sh", "test": "tape test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "contributors"], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "engines": {"node": ">=10"}, "dependencies": {}, "devDependencies": {"tape": "^4.11.0"}, "gitHead": "7eb0c10992d505d4106967a4f6e4b8ccafc1919f", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@3.0.0", "_nodeVersion": "12.13.1", "_npmVersion": "6.12.1", "dist": {"integrity": "sha512-LCBAB1J10ku7+SWbFTLJ4zjDtrniwYDMIyRmg1Vxvzst5TyCrsrfKHg1YPrvvsUhrzknTXxL6NoUG6s2xftbcg==", "shasum": "e5d259023682f118ea370eab937385c8da4c664f", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-3.0.0.tgz", "fileCount": 18, "unpackedSize": 384798354, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeB+UoCRA9TVsSAnZWagAABJEP/2ax5cU2s97XjlZbGvCp\nscGAkNt9f5lBaHOr8s1w2xuxf0NHG2DoMY3wOv4UljUTcmnKZ1jZAcHKojnV\nfdknJYQW3loJu6+LUz3dvSHGKvmAi61qJIXGJ2u1fw6BgzIdJ7NOoy1HRkwo\nmluTxcoHmJAS7uJp6cSWPGKHV5kE0vVoWcwUU/cm4+CQf/a/c76m7/bjsLTF\ngu3ZoLOLMkYGYMzz6BfB2B9ZzVAWdXlGcetUkq9J13+mWSzf+sfvw7jJYFFd\njE576q3qndqn/90sRnSZ6PmF3GRiwoy5aq52fsmkl8J7hL1go21elRqsK/Bj\nntfGORdskMa3LqY3xr8SyCEL8PMBfcxiGhKiOOVUDHz81Ej/b5yto+5ApbEA\n0QPhKYPtpwNtriBZAx9Bed3HZyNWSZXpILx/U3ROBgxW84n9K4h+LjwcZ64H\n4vT4mS2LgMukfHZHPwBnjMImezWP1SJMxr6bfR78Izn9b4MAUBxbKRUs8WrR\n/fgsiEFViWlHbxXPUgXXLL4KZEpVFGroVmhDkhV72jSzgRJYTcgcpF2K12rq\ncZ4VnDAjQ7q6r/t4r/lx6gYKvt5PanyPq7ebrg7iv4Kcho9FGbZaF5wtRfvB\no+rr6jpXJzIM29aV5NtaDM1qiSEEvkPhp5nYA3s9+gs9b4kXox4EhiMaetXH\nDUsB\r\n=a8Zx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEnqBmATG56NuzsPGgEVz5d94tl0r3+9SjDFV0Srdo6+AiB1cIbtEaTBxK/7DFy6TaiobhUBOuvPMCqQpqBWLvtUAg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "aeh"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jeffroh81"}, {"email": "<EMAIL>", "name": "noblesamurai"}, {"email": "<EMAIL>", "name": "tim<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "tomns"}, {"email": "<EMAIL>", "name": "wayno"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_3.0.0_1577575717207_0.17363566537131492"}, "_hasShrinkwrap": false, "deprecated": "ffmpeg-static@3 won't be updated with new ffmpeg binaries anymore. Use ffmpeg-static@latest."}, "4.0.0": {"name": "ffmpeg-static", "version": "4.0.0", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"install": "node install.js", "test": "tape test/*.js", "prepublishOnly": "npm test"}, "ffmpeg-static": {"binary_release": "b4.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "contributors"], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "engines": {"node": ">=10"}, "dependencies": {"progress": "^2.0.3", "simple-get": "^3.1.0"}, "devDependencies": {"any-shell-escape": "^0.1.1", "tape": "^4.11.0"}, "gitHead": "192413704dda43a9bfb64bb4d16317b849fd4961", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@4.0.0", "_nodeVersion": "12.14.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-9GELg6SKrijgf5iw073PiHonAty9Cg3cxeh8h4a2HHcOv+r0ZHRg3QGTAU/jvLhDFP6pu1UhnWqSwkIb3ZpvgQ==", "shasum": "fb90e912170800cc642d70feaa3237fb5da24faf", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-4.0.0.tgz", "fileCount": 6, "unpackedSize": 8308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMNowCRA9TVsSAnZWagAAYo8P+QDSkxjRdjeOMUgJRS2S\ndVnlOHlXWWUVcJ1dtIL1LlRImMIevBfrCaQxdG8Hx0MH20JIXojl00dgdcfN\n5EDIHCrL1c5g3Bk5fvIeqz9EPVm3PHXCtaLSsl6KcDghqiKPSPdVyNGhlB9w\nhiAI/ACY6GAesTgAEKC4C4dFxNt5prBwnrf8A73rRRI47kook/TlfWbtPNMA\nUwonGwGJ1zt1siP8T9ZO2+bUD1oGEquqoGMC1/N+oaB7l9g+SzATKJ5CUaDR\n7KVf5tTKF33Tk6hfLghv7ofYf5oED9W5kHkBVcekhEE2UF30feDGcRVtF4HJ\npv0RcwKD76ybCMfOdeOdLz8zbXIOGLfnsquZvZrgyuyDhJui9A9cu1XJoIdR\nLaTi0cFjzI+/f574pTy46nnhS5TvGyIaARRwqFCOA8q/e6mutLe0XEDenmFH\nbGFzcIYN//+PHGtccpAmF3pDgcZtZQlYsRC2WZ4s0crJetzhJpOpqzUXG9zU\nn2+9w0+/SqtTcNswGCuaN6l8mlei6MvGxJvG//ZOzHPsaRYUYW5qf1KYeh4E\nVH36/WRXBEN8yzrqdPSywEmAjVgg1WeTFSlc5ITJ46sBYMMbZ8jdOILqiiKi\nQup1N17ODsDTF+4p73txOIQXBEK1u8PH14F7v7opivIzw1+YajNDIQnDL/lE\nu2xZ\r\n=0h3i\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICz5uFetg2pbw57ZDZSxnB4wU7A65/UBGn4O1B5nw1M2AiARmo9qAcDl+thsVORUsX8kmw8kqiOBNx2CBI82SwUarQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "aeh"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jeffroh81"}, {"email": "<EMAIL>", "name": "noblesamurai"}, {"email": "<EMAIL>", "name": "tim<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "tomns"}, {"email": "<EMAIL>", "name": "wayno"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_4.0.0_1580259887715_0.9281503510727478"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "ffmpeg-static", "version": "4.0.1", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"install": "node install.js", "test": "tape test/*.js", "prepublishOnly": "npm test"}, "ffmpeg-static": {"binary_release": "b4.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "contributors"], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "GPL-3.0-or-later", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "engines": {"node": ">=10"}, "dependencies": {"progress": "^2.0.3", "simple-get": "^3.1.0"}, "devDependencies": {"any-shell-escape": "^0.1.1", "tape": "^4.11.0"}, "gitHead": "ccef05b2adb6bd8c06e9f6b011ea2156633e4b0b", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@4.0.1", "_nodeVersion": "13.7.0", "_npmVersion": "6.13.6", "dist": {"integrity": "sha512-tUGBiessDvslK6jfVpfrhm6NvXPrIOclSVsqI8FGx4uDPN6xvdH8mGghcZDOjXky65FjaRPcTu3y/8Ne6VXRtQ==", "shasum": "0833e010ef96251e1d4c1ca67ebcf0d036be9662", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-4.0.1.tgz", "fileCount": 6, "unpackedSize": 9189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeN2YjCRA9TVsSAnZWagAA7WUP/3HZTK4YcdOi21hKLDd9\nZo0d1c13xGcGIwTzbaX+Vvb0HVkY+Aupf3o/w9d38ruWQSGpLjTbdIyCbQvO\nXvpT6XtijvZOgnMX2/TlGV+Z9Fn6XcR1W37dDCzpV09654BXkL41X2KfORmg\nJ0KPiKiy6k5tYJHD3ipmHhJwF3g1hvulZQwsYMcMiNPecBPrib/p86O1a/lA\nPfiXsHLwLv6Yl5Sxxu8dP5pkGc5sjxQ0CGEMl8NMiQV0u8ukjFaz6yDZnhl9\nkJU/ItGukyEmMn+QJm/LRPEaFMkwqpc5BrJ4vApIURI3T74ocb5F8JhqKTWB\nL8wLVUkQ2OLlBrq0EBt4TNIcyjFPokXvJ0MNeo3ZajjfirfdOSPCFDeFFHjw\nBu5JkBUYQL0QaSjP1NvCdmS1P+CWyhS5fgdvY/iuI24noFvRmB37fWog6fWD\njajkZO974hz632crMYu4Jm2N0+fxqEXDjOLe/xZPQ0qUUN3kuv8fdqfFuYtm\nYYudJ8ADE3OzruDpmRSH83FRw/cLeYIVW+hiJ+jE25A6hiJTM1JGsSTY1IZL\nypxlHqE/4XV+u2JRbWtwFq74N4ylwbFDge5H7lyPBJSETG3jqfWx0hngJBAS\nNAelkCaWX58oeb+/dFfNui5NG/P8qMSzjBW2R2G2lx6J51GzywIpQB0lecAa\nqUCB\r\n=2XEm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCUBtcFPnoMm9AbtxtaSSMjo68a+aLNbnejYDWmu07RSAIhANJD6Zk3ibI1MZSdFMYeRko48z4sB46vAUq83yvTNuQ+"}]}, "maintainers": [{"email": "<EMAIL>", "name": "aeh"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jeffroh81"}, {"email": "<EMAIL>", "name": "noblesamurai"}, {"email": "<EMAIL>", "name": "tim<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "tomns"}, {"email": "<EMAIL>", "name": "wayno"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_4.0.1_1580688931070_0.6417365007598774"}, "_hasShrinkwrap": false}, "4.1.0": {"name": "ffmpeg-static", "version": "4.1.0", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"install": "node install.js", "test": "tape test/*.js", "prepublishOnly": "npm test"}, "ffmpeg-static": {"binary_release": "b4.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "contributors"], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "GPL-3.0-or-later", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "engines": {"node": ">=10"}, "dependencies": {"@derhuerst/http-basic": "^8.2.0", "env-paths": "^2.2.0", "progress": "^2.0.3"}, "devDependencies": {"any-shell-escape": "^0.1.1", "tape": "^4.11.0"}, "gitHead": "d0a5776e07a87bb11aa18fd39ba611585a346799", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@4.1.0", "_nodeVersion": "12.16.1", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-MbLvomoRFmm/EswLoM5frg1fnDAHdMxeaYzfOlH5ujSnd47FAzDnjjLxzmsvVZFwJaOj2DYVt9Pstmttj6eEoQ==", "shasum": "29f9884435a5fc8f9f2bc2b719c24976fe7365a3", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-4.1.0.tgz", "fileCount": 6, "unpackedSize": 11259, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeimnlCRA9TVsSAnZWagAAlm4QAINQZczN2cVlnD1SrhAT\nHSkAhDFhNV0D8L96SjBZ/sCJLsLh/iMeLv7IJQ2K2IZzChoqUFMLzMIqQIfG\nO4oEWyl5Mp6ZJBE8JVIQV/SAUvfmqzXmPleT/iAnKAHXRgU2KZwYy5rw070R\n6St6kim/oJnpb1H2zm5Ejsn12d767rmqdx5Ep33MVjLbUIILmwEtb4WmS0qI\n6GDB5N0eHaEiv8hbHDxchzwbl6/mj+MCoCGhSYM2X5Lif2NJSReebCFQyvlc\n+lk+vtv6V2kc5zQ8yODq7YUloDFcjajRcPGDpHuGIaPmYBojYS/yHOrG0gtU\nMP9ED8g9z8UZ48j6UJvMtgScLZ2ifPFVYFw+PerrrZDaDF3J/NO5cQ+XBXRT\nDpsPDVnkwrBWIj54JrgkDBNozhEnAAzLSHTAFuLQvO2MByb39b+Tv1YhnBwQ\nEdmd2XXo5Ieh9nFT31BCfFsohZi4hSfFxVGWcKoOSUBVdm1mx1yMFS6CNLju\niQKXLKf+6msl/XBtvOBvR8oNKh+iyfsygfr6dJojBW+VTtMj0kuHlPpFnE66\nsPBpc83zqa9VaXNUshv65MgAS2f4uzaDZfS7T+xnnFUKA245HGb36W+kV8dS\nNEPNobhBLYo6+Pb+lqUrD8jZZwUajpHKEya1QUKVITUNJnUMrwq0/Tlf69bV\nX4wH\r\n=yxev\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGPoaU7hVGGwy1JFBFcrBDJ9xkhdgKuLklOU5NMxB+oqAiEAhhNNyzZXOz4k2Ok2+dTmyV7mR5vdHIARNyBEUNFd/ng="}]}, "maintainers": [{"email": "<EMAIL>", "name": "aeh"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jeffroh81"}, {"email": "<EMAIL>", "name": "noblesamurai"}, {"email": "<EMAIL>", "name": "tim<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "tomns"}, {"email": "<EMAIL>", "name": "wayno"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_4.1.0_1586129381270_0.029823055110524077"}, "_hasShrinkwrap": false}, "4.1.1": {"name": "ffmpeg-static", "version": "4.1.1", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"install": "node install.js", "test": "tape test/*.js", "lint": "eslint .", "prepublishOnly": "npm run lint && npm test"}, "ffmpeg-static": {"binary_release": "b4.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "contributors"], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "GPL-3.0-or-later", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "engines": {"node": ">=10"}, "dependencies": {"@derhuerst/http-basic": "^8.2.0", "env-paths": "^2.2.0", "progress": "^2.0.3"}, "devDependencies": {"any-shell-escape": "^0.1.1", "eslint": "^6.8.0", "tape": "^4.11.0"}, "gitHead": "32066b8610168407e7b4c995f732ce2cb0d19c6f", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@4.1.1", "_nodeVersion": "12.16.1", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-+kKq+nbnydsQPIpH0RkGNlaGGKCLnprNwSlgXlwoTjL/ULgqRVvgTvjxIGw/OoWCXQLxLhRxeXkpIIgzj0UCJg==", "shasum": "9a7d0f826c77c19a52ce5de6ae6e71809dc3cd5a", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-4.1.1.tgz", "fileCount": 6, "unpackedSize": 11335, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJekcn2CRA9TVsSAnZWagAArhgP/jpoRqae28COzcZZxDss\nNLaLfAlUMYqig4jevlJayCl+i/+bZz9zptKJdhLM/DuUbzuiVhLrbtftoelX\nd6MMtN7hgSKWnikAzDXAjQcuCzRNz/sgMEd4tbdDjO/lzcIyzmKJoY68LiNb\nhoAyew9D8GIZneUm5bsgcPYjM0xU/V22wtGnruaFlWcHgkGDAWxC+Mvn7BdW\nCTzTHj7yfmwbZ7g1eizXUGdj6sSYTAlEwvror0Cslj5TE9SmeiH2L7kJB4BM\nR5FwPCnAS77LIZpIFYGoHjNF6xraksGIRGg5WVEhwty0TLFiEm4DBb92eM4w\njJ4txJQYQaTh0PkoPY3ZrGNazqYe+WFx+/1EMcmpq/nWXOHaDJ6wP0thoG3O\ngEFbJvY7SDpCZ9N0GoJR7NAal2WAKOL6Nba3E9XfnaxhzkJRUouz9vuBK5Qn\nfpjhTWwcDdQs/8KGcvVK4MIpWnCnWOulI9BjX7VqkrDTH0MyF0lfYXdkBuJ7\nokSBIm/cddciJDZrVYj/IYoDGxwBIjGxg3DzcuOrqILUT0ILR2FvYUa6P4eC\npw72C7+ltlNIWUSrdXJF0ZfBsq/RbN3WN4nbikLrwlPT9OrKXzLsAgQ47Keb\niR6vB5eo5QBDWYntnbazzZx+/AUSUDlPVK5e4qVlDdzGlrgX3/CbeqV4NA2V\nh2j4\r\n=5NoN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDAeVQTUAtnRSCwQd0G4nImGUVHqv1WZt75y34fO5+IpgIgeqFLLSnoKwkaEr12NOOvd+pWS1B3kzIKidQxwWns3d4="}]}, "maintainers": [{"email": "<EMAIL>", "name": "aeh"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jeffroh81"}, {"email": "<EMAIL>", "name": "noblesamurai"}, {"email": "<EMAIL>", "name": "tim<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "tomns"}, {"email": "<EMAIL>", "name": "wayno"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_4.1.1_1586612725742_0.15185731260599677"}, "_hasShrinkwrap": false}, "4.2.0": {"name": "ffmpeg-static", "version": "4.2.0", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"install": "node install.js", "test": "tape test/*.js", "lint": "eslint .", "prepublishOnly": "npm run lint && npm test"}, "ffmpeg-static": {"binary_release": "b4.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "contributors"], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "GPL-3.0-or-later", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "engines": {"node": ">=10"}, "dependencies": {"@derhuerst/http-basic": "^8.2.0", "env-paths": "^2.2.0", "https-proxy-agent": "^5.0.0", "progress": "^2.0.3"}, "devDependencies": {"any-shell-escape": "^0.1.1", "eslint": "^6.8.0", "tape": "^4.11.0"}, "gitHead": "ae18b7393322ea0b52f4bb1bc828a921be2f8bb2", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@4.2.0", "_nodeVersion": "13.11.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-LHQcdAb59eOEJfa5FcjiZ1cBTkm7+ZwndnAhINgAo/1fqKnZOchr2wXEY/45IBMkF0bmTO4rY9YPeztj9iEdNQ==", "shasum": "b6a37002ee0e2d0e4cefa083ca042b9ad8bec302", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-4.2.0.tgz", "fileCount": 6, "unpackedSize": 11833, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqDPsCRA9TVsSAnZWagAAX9EP/RAUUXiZdoqCtd9VAIg5\nTnzNujYo9CegpJp6QtKaCoFiRqz38WGbCQixy0Ye9yg503s0KcQJwdfVfob0\n5oOhCAKd2eA1O+pXtevg3oC1mMryUuOWZ4UCg0cgxALFpxDiKCtm5EYsdYaI\nBQeEhGxLoMbu6qj/RfVBXZ14uhfsUWZnnXB5iwNyN/yS69HtrqjMjdkR9gua\nmEHcpgqolnU2+6pm1Ifcw52pOwMGGxlfceT/hSGnvzS+NfBgIk6C6amGKIzF\nijfejEOp17qRHY7GDzEhujd0mZ0I/hkTRAi75AEGLx9jfjOEN60N4QVAMKPN\nv1tzBdL4BfOGxSnwH7D8yzVhjjImvr8c7kMhHHHqs+3SZwzkdzQC1PuWvU9L\nL6XzVNJ6UxPXrkNA5J5wrG4rGlpt/hdBUSip/wupVZjrBcxe6QVTxvhDZ95q\nB29ZlGVnv2tdr4Vx1wrP1ITd+wIzaDhdbblHfMXy28SUnFAJ7pDekuqm1ABv\nTKmAksn8N+0l3qcAN3H1OzOnRepNawOpmkyaYTuvfwbKHUnVuSLvAeulVC7z\nkqI/IZ0ysCK63cQsti9ApFeWklohkY/M2sBKIk4bfW+iWeyq3gYEdyoV64D/\n7yxcZIbMJMPkKwF9nFicwtSnHW9FZkzI+lTEVJR63pvLvn4T5G6mv7WSq61E\n0BqJ\r\n=BuYt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDG+j620lHEsm8KkA7KbF3MmuJ3hVRP/sAEnADrEQc89wIgOvZYZJ3/OV56EpZkfh5Yv5SouvFffUL3vSwSw5+4HHE="}]}, "maintainers": [{"email": "<EMAIL>", "name": "aeh"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jeffroh81"}, {"email": "<EMAIL>", "name": "noblesamurai"}, {"email": "<EMAIL>", "name": "tim<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "tomns"}, {"email": "<EMAIL>", "name": "wayno"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_4.2.0_1588081643836_0.2013299957145709"}, "_hasShrinkwrap": false}, "4.2.1": {"name": "ffmpeg-static", "version": "4.2.1", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"install": "node install.js", "test": "tape test/*.js", "lint": "eslint .", "prepublishOnly": "npm run lint && npm test"}, "ffmpeg-static": {"binary_release": "b4.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "contributors"], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "GPL-3.0-or-later", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "engines": {"node": ">=10"}, "dependencies": {"@derhuerst/http-basic": "^8.2.0", "env-paths": "^2.2.0", "https-proxy-agent": "^5.0.0", "progress": "^2.0.3"}, "devDependencies": {"any-shell-escape": "^0.1.1", "eslint": "^6.8.0", "tape": "^4.11.0"}, "gitHead": "34a91e70ab0686854b47f3e561a4c4d738f0d9c1", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@4.2.1", "_nodeVersion": "12.16.3", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-vLhKBNqMwtoZe3pPQd8QUGJwlS0A5dTu0EVJQ9MvjsIauDrUHzFHo2XJ3B1SJbKvFnQVd5kZXIK+zqZAUcT0qQ==", "shasum": "10819f35f0f4bae775293be3fdffa74b87d65848", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-4.2.1.tgz", "fileCount": 6, "unpackedSize": 12026, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesTx0CRA9TVsSAnZWagAA9fAP/i7YDUWsuoihhZ0UyERp\nw98pKj78LHCZsGQU2mM5PiwrtEqkesVqjneVOh8M3JKu3Xbq29pKDAqPX3hm\nxsjbTfdbUPoJ7bUpTtp3Ge3PblcOAgI0ERnQKa3+cNKZ5SeRjj827YayD8FG\nKXs1EAXy83GBKuBBAUM0lW/EDc5w3vpPawbz3k7fADhDDXXiysR8IssS/sZF\nWfyZtzLegZ/cLQLyanYJvBqYnRYxNmkrHTCZHFAZpWoHMzBtJakTfGIkeX7g\nr0993skP/xppltim7LAfA5L6stg/7WcpPDOojoHmTAIL7Z7m+VnZVOgstBG/\n+1j5ZOYnhNrrFj4MCbJQuSAsw4ebLKJ7LPNPiCjQ55vIklmljLHOgEsHNly1\nft7WE9wsQo1f0R2eyTbHWPMq4VLbHwvaamC1XdwnXkTd2/zGJvloHdE8jxrR\nfaRwaqNbI2T27xnkTSKoTTPn9aLzVj8WHqpnElsDygwaeop21SAGIYwQRlvp\n/vc2Wy2Phx847Zp7Q7EOKRMv329g2pIh/fZV42P2zoajrN+JkQdD6kWZybUt\nmU2zODEglqifDRMG/TVwYRxN6/PjR6rVGC/rAbwbIsNOMo+2vN5NgHAERVuv\nK83SKfyoHhI/NWkjpq47YLW3l+bicC2oU5GLtpuNQ9NDm4X5yeSVx2yc19X7\nwG4W\r\n=95Ea\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCa12m+P1CUJKk/vfhE8EggszSSJmNthmWvUdahSYFo6gIgbqsksXOfO/wvS7qQy0SHKPNRrFKaitkdkiVqzbvvFiE="}]}, "maintainers": [{"email": "<EMAIL>", "name": "aeh"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jeffroh81"}, {"email": "<EMAIL>", "name": "noblesamurai"}, {"email": "<EMAIL>", "name": "tim<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "tomns"}, {"email": "<EMAIL>", "name": "wayno"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_4.2.1_1588673652157_0.2706639279083254"}, "_hasShrinkwrap": false}, "4.2.2": {"name": "ffmpeg-static", "version": "4.2.2", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"install": "node install.js", "test": "tape test/*.js", "lint": "eslint .", "prepublishOnly": "npm run lint && npm test"}, "ffmpeg-static": {"binary_release": "b4.2.2"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "contributors"], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "GPL-3.0-or-later", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "engines": {"node": ">=10"}, "dependencies": {"@derhuerst/http-basic": "^8.2.0", "env-paths": "^2.2.0", "https-proxy-agent": "^5.0.0", "progress": "^2.0.3"}, "devDependencies": {"any-shell-escape": "^0.1.1", "eslint": "^6.8.0", "tape": "^4.11.0"}, "gitHead": "525755d9d23deddaa392aa98fcbd27269f96df94", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@4.2.2", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-0m07Lb7Wz91xw2g3vBzcrwhcm9D9kWWA/ZheWt6dOhIJrxanFVw1XUe9ewxF9RGBkcdt43UksrpJkL2gvl00Ew==", "shasum": "29f23ac94d691d3f6a8d4229f8d3787a077209d9", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-4.2.2.tgz", "fileCount": 6, "unpackedSize": 12524, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJexCRvCRA9TVsSAnZWagAAgDwP/3zo3ho23DBQbAnpL1bz\nThoFp1VS7IXAcr0MQT9QAuvMIS/NCYv6Zaa5JIcmXietayQKl0TJrpEIrAwo\n/U4/xalD0wk4vJ3kkEuANGbfpfZCOqTRFeJVMdJPm2lbba2CCjEgT+hVtjTE\nYkflrYZnBhPQCm1GnmvMmvkxOoS+ZOsKazQ93VVCmy6lyzWXezk37rOx5yCL\nWk3FsCdDfVJ0P9ltZreutOH7Nv99GRJAQdf2vLxX4xx9bTEIkYmTsQIhZHRp\nNXPPx+or8oa+GAwRlx7oLTaDM6n9Cdhd+N0y00CKMBVQUJT6D90mNsJAL9or\nnVjE9JJgPiLazBcw5beSsOlX5Xs+Vp4u1Z1KRSWy2z2+4r1Ukond9Lu7fuSC\nxxU7ACefsQqkjez+J8cDJ1Nd56Sy4mXwUtQVj3+akQNYJSl9k0KhU41uPk4b\nmzPYaW2AejL8GrGsHE0sPVkW5ejpiI/+NYF7yElTfnVEiZ6yhnY2lPQ8eXS3\nn5j60Idaki0IVa3pPuAwYOIw+Tdf8vfmCh+pV0yBb24Xqb932B/hieBXqffP\n14K+Hx5FZWjg49XnrDRITHCladQkDZupZZD8GpI8ojZyjh0g+rd84nFzgdsh\no9IWZ6dL4RFGMp9qhu/BWnIE7tA2Q2sYNUqS+93lMbTB5YCzY/4JVO0kktJg\n3rnV\r\n=jj/7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEEYkdhp0RRoHpGu4TLm/Ojg7LEQd8x6R8XQ8tGnXF/xAiEAuguJRX+8P9Vd7bXkx3bu5Va7mHL6FVQi0+48P8FZXw4="}]}, "maintainers": [{"email": "<EMAIL>", "name": "aeh"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jeffroh81"}, {"email": "<EMAIL>", "name": "noblesamurai"}, {"email": "<EMAIL>", "name": "tim<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "tomns"}, {"email": "<EMAIL>", "name": "wayno"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_4.2.2_1589912687404_0.08363520065595575"}, "_hasShrinkwrap": false}, "4.2.3": {"name": "ffmpeg-static", "version": "4.2.3", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"install": "node install.js", "test": "tape test/*.js", "lint": "eslint .", "prepublishOnly": "npm run lint && npm test"}, "ffmpeg-static": {"binary_release": "b4.2.3"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "contributors"], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "GPL-3.0-or-later", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "engines": {"node": ">=10"}, "dependencies": {"@derhuerst/http-basic": "^8.2.0", "env-paths": "^2.2.0", "https-proxy-agent": "^5.0.0", "progress": "^2.0.3"}, "devDependencies": {"any-shell-escape": "^0.1.1", "eslint": "^6.8.0", "tape": "^4.11.0"}, "gitHead": "accde847252839a90a41b4e5d8b3bd229fffbf85", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@4.2.3", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-zL2YkWdRFsPZvtmEM6t1Dw7VMIE+9Moy+WlbdoLbqB+Y52BPwANJIGMJ+/kdyxWULPa4UxFPBNJW5SSxi/ex/A==", "shasum": "762dc4da2122d0ea4f3cc1ac86f9c017eae1eca1", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-4.2.3.tgz", "fileCount": 6, "unpackedSize": 12524, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe436ECRA9TVsSAnZWagAAeygQAKUegr/bFCNn3cOyd/50\n+uWaj63pdrfatQoBAjGg8ezn4gskx7MiF5rLxLQz36/8ZpvhGA6ACjeEWcU5\ns06119To1tD9bB2RmBUesE0B3XTUsiF8kHVVloDSg7s/hy2RyIObsbzaAjwu\nRFz26Sz3htvAOQ7jHcJCh83kyiqgE5KJySXBOE4Ex02x+eCFzsxasgRxBZiv\nODpcrw0UBRrGBC3hRfrSwoiuP0kY0YK1bBr+A1mCWiY1qYmErQBI0wB2pv2P\n+8ltG<PERSON>hn+pglfT6/lsJHQo86VmAwlvWoBHI8iHhRJ27Kno5tROHQuc6QlrE\nhaPzbkCJfCXYMfleMWoH0zlVkn1zj7BqdyAqCxaK00UXOOPbkt6/tXQ/kF9d\niCuILE/VIo0/k9glU6f3O4JZ4gsgon4sJ2Bpqj8uYoZ1AFkjuu5R0DRomZHk\nv6ejtAZOPTTGMFN1oHpV2KoVtovU3ozC+vvViKx+EdANYw/FTkzzC1Eny5Zs\nFzlG/P+vhcNUaSvy2mr6yfqt4zqKvMvf78cTSE2TsEirUBKst1ufynKqcxda\nxcPInXO3AgQ+TR2D5wi2uzVIks+lyIIhKUXqk6MZCTMzBINlFbKuCAJkEqJZ\njlHDhsflg6kcGPh+44tDMKYwMQgnmz+S1dG/xS1o5C4xuBYcCAl5YqXqHmOF\n2EU9\r\n=uRrU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGQyzoYSKgpTpXoo9Q0tSGoUyZB0X9daGKYCl3tBNcdcAiB9WuTz8wyTIUkH3GLy1YNsctPTPglO1cna8lbsOczBNA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "aeh"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jeffroh81"}, {"email": "<EMAIL>", "name": "noblesamurai"}, {"email": "<EMAIL>", "name": "tim<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "tomns"}, {"email": "<EMAIL>", "name": "wayno"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_4.2.3_1591967363923_0.3876961749604846"}, "_hasShrinkwrap": false}, "4.2.4": {"name": "ffmpeg-static", "version": "4.2.4", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"install": "node install.js", "test": "node test.js", "lint": "eslint .", "prepublishOnly": "npm run lint && npm test"}, "ffmpeg-static": {"binary_release": "b4.2.3"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "contributors"], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "GPL-3.0-or-later", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "engines": {"node": ">=10"}, "dependencies": {"env-paths": "^2.2.0", "http-basic": "github:derhuerst/http-basic#8.2.0", "https-proxy-agent": "^5.0.0", "progress": "^2.0.3"}, "devDependencies": {"any-shell-escape": "^0.1.1", "eslint": "^7.2.0"}, "gitHead": "72ffc9bb2b3327ee96a5441cff08226a97b371e9", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@4.2.4", "_nodeVersion": "14.2.0", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-uz5cYRuErUclST70fwN4wxni+CsrLWV5/Tbp4nysc50cjcayOm+x7wc/o8pFcl1p3ix+Tb+ZVOJwrYQoGA0bcw==", "shasum": "31326d6eb781ca48264824ec40c6bc026fa321bf", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-4.2.4.tgz", "fileCount": 6, "unpackedSize": 12461, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6A1vCRA9TVsSAnZWagAAbIcP/R3+mB1Jrvq5nLAOhIPG\n/AM0cgLKJzDG87ZVqrwNZwxqXNrQAY8ujfDXwp8HePw0jeM1pSBPhCacJ5uR\nflkZ2cm5vdmnZlUeZAhjAhc3PvOMeM+2hxU3I1PFvraTz4swt9+MC9LBbQEW\ngnikcuZPt0/0Yn/kpGUS71QtJ4A4GXfe1zh0TvX+J4QFOy7CYVaisZfCcPqp\nI429NAY0fG+I2Kd9kBKZaN9O0EXiG8bzeBp9x9amDKgNRbOA0IOPRP5DmcfL\niw0qqBQL9sMsas6CZWaLMeTWsUpafAyo+cFvsTJXsJVDUc6pdQKkNBiLUsMK\nGR72Hp/2+zWliaD44gNPPVjF8REMEjI/XJZjY78jzb6LLJjVqIEdVn+D/jnm\n2MXu+5d5sClzzafb378q0nT5sv9mERRC1X1NWO5p1HDbIMpwOLBaYXXOsTxq\nyNGDsnWeLAAhp9OLOWOP28PQ0Vv/YA8rs0U1ppDkuliofbVN6MDZVFk1zzgr\nqKPnV0L9MNHtDeWBZq+BVUYUgiyFFOX3i+n3TH7Cv2ocAtRmiEjVX6MFIrM9\n3u9xXL4jPF9OJYGIFYvptYNbIlcn2ZQfQrgODmdrkFz+nYvH2b1wfarQSWhB\nflQjXoxxwCM4N6tqvL7tZL4oawZr7jd8pVYFDhR6U9fK1uX/RtfvUVP22d++\nTFbv\r\n=A5NT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEEhBnM/B6WapNFGhBaskxkr0iTfq5An570c3C4LImpwAiEAgoy6iPn2/v4TsVgbJD3TlghhOlxzuzDHh6W0wq6Rc9w="}]}, "maintainers": [{"email": "<EMAIL>", "name": "aeh"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jeffroh81"}, {"email": "<EMAIL>", "name": "noblesamurai"}, {"email": "<EMAIL>", "name": "tim<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "tomns"}, {"email": "<EMAIL>", "name": "wayno"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_4.2.4_1592266094665_0.10910965774903736"}, "_hasShrinkwrap": false}, "4.2.5": {"name": "ffmpeg-static", "version": "4.2.5", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"install": "node install.js", "test": "node test.js", "lint": "eslint .", "prepublishOnly": "npm run lint && npm test"}, "ffmpeg-static": {"binary_release": "b4.3"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "contributors"], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "GPL-3.0-or-later", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "engines": {"node": ">=10"}, "dependencies": {"env-paths": "^2.2.0", "http-basic": "github:derhuerst/http-basic#8.2.0", "https-proxy-agent": "^5.0.0", "progress": "^2.0.3"}, "devDependencies": {"any-shell-escape": "^0.1.1", "eslint": "^7.2.0"}, "gitHead": "7d330bdbec4684e650c4951754327b0b1f809337", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@4.2.5", "_nodeVersion": "14.4.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-4JykaxRgU5z/GfL0YLkBetJyXgaQXh67etMJlJMMTTcoA33voU6y/KFMg/RUehBj3DGDIs9HYhf7tozwabibaQ==", "shasum": "37c2cca00bc971951daa1bd243976fbb18a2d074", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-4.2.5.tgz", "fileCount": 6, "unpackedSize": 12451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe89EICRA9TVsSAnZWagAAYgkP/RTk760AwCynGticEpXS\naR4wr3hL8Zi8cOKNNnITAFf+JYyRF92v2mPvueFLan5zE9PR4odg8pZkb/Hy\n8Xn6M2kjLASQDr2Bb59nIgE/aC4B5Gawaz1U5Vz111pHYiniAerw+SL9SXQp\nJoCIuqPuU3anCXuE699LnDMSBSvVbXgRfEPeazZrxYDQ7FOZjQgvbBYeDpep\nFplYBfyZfJxsqnaAEbckPNAImLRuVUuO/FBrd+lSrCCvaCkDyfodK/KKnLt0\nE7x+fOVa+BEux9Yort6UX38kji2H0B0pPOyYPDqlXWqeuf9HYUeHyrQhtR6L\n/cDQzy7wZNq70mDtm8RPJlE/NPkdCJ8ffNdvQjpBpPHZgGQrdvliHoxwKBm1\nPWgWpHeaJXqsL0mr71+uq1Jr6w62nxt78G+9GJ17G9EtIYhxJcwhBM+sPy5a\n8DJMV0kUIS0FoCi+5BDc6rYFg0TOdLJ6I50+u57shs00Z/G3n/IEcmRK69oG\nxtcXOYp0MRdw0kVtapRi/sfFC895b3AV7UNWOFB47fI9l24hVS9+r4IPo4Nb\nkYnq2ZKYj/aJtl62XXk0YTvGnCTBpDKnRTLBqasWKmElY5Mn51Z5pP8ma/Lw\nIBGJqhbBvW5yKyorba5j/OjRbDO5Qe7TsdVaLaoJXjWK1/dHHojkuYSeG7wG\nS/V1\r\n=d6An\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEQdXauMj5kkTFcQtiLYe7z/dS/WAtXaO1//bMu7IS25AiA3zgwgZDJ5KiHr5qw7bI+RE/TMgm6o889lYS3XOsDqUQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "aeh"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jeffroh81"}, {"email": "<EMAIL>", "name": "noblesamurai"}, {"email": "<EMAIL>", "name": "tim<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "tomns"}, {"email": "<EMAIL>", "name": "wayno"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_4.2.5_1593037063793_0.5499976704709708"}, "_hasShrinkwrap": false}, "4.2.6": {"name": "ffmpeg-static", "version": "4.2.6", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"install": "node install.js", "test": "node test.js", "lint": "eslint .", "prepublishOnly": "npm run lint && npm test"}, "ffmpeg-static": {"binary-release-tag": "b4.3", "binary-release-name": "4.3"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "contributors"], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "GPL-3.0-or-later", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "engines": {"node": ">=10"}, "dependencies": {"@derhuerst/http-basic": "^8.2.0", "env-paths": "^2.2.0", "https-proxy-agent": "^5.0.0", "progress": "^2.0.3"}, "devDependencies": {"any-shell-escape": "^0.1.1", "eslint": "^7.2.0"}, "gitHead": "b2f9e5f751c218e4e1a91ba2da6a60e3a417c519", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@4.2.6", "_nodeVersion": "14.4.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-86I0hm7BqfcNdlJqWw+BoKFyA39xzroa5JQq7GMEBNifzzSRXULVyI8AxoXyVoqDVnclptPhZ37UkmbqVQvO0w==", "shasum": "3930b34b8c9012864d99cb027cf4e8f755832d91", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-4.2.6.tgz", "fileCount": 6, "unpackedSize": 12600, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFAN/CRA9TVsSAnZWagAAioAQAI3fATf9C25ftAopWE09\nuXi5FADw3qC7AxZoqjnGXJtDRtLxAIP96tt0naGT9Ff6qMqqw7N1OXkCnP+V\nVGz2TfDWI6iPTVbmqkL4Bq/awfYiupO3FE/JwFAthgvQlOv19RYIJSiIkcgT\nmtVyBzrUgjmWlGFkOrbfZK0fjCwADecr72X3btmBiPN8DTB+l1L/ckiNWzzH\nyGgpEDRdg0hMdFeFtQYWNQnq1oCstVJ+thCyouD8sA+QXAsn1vdS1nXoeL7w\ntJoVESUA5TrDEwl8a/EQcQkH8LT4cce2xt4/tnguP9bCsah90K435K3YFLs8\naBq256Jl86IJ32IxMswtnpeNoZ7bmZj3tbzoXwk0VCTsM9bZ2PHq07PeUtHi\nCNtgo2JkHLb3IV1RuCvZckvhHSx/CKpz1F4kIH/S8axnSlv51iECaee3Fbwb\nny7biUa/6b1gjIPAzrhnOB4LUgJZAc1u/9i2qQpd7kFlW0XX4vTTa7fjxhrp\nvu4EBRbb60MOtOEEuA2PaJ8popevCsZgYPQ6NuGtbAJzbndCTHbqJEb9udyG\nJfBpxxem0zscZFRudbndje+2XcTBc7/qRX9lPCJOAQhzS3NvzG/r3Ql85YYa\nCCs86MB4xXr6Qqw+V5FlsTZgykZW5NlhA/+YiAAUumYiDfoF4pCEyJK9kWEE\n9c8+\r\n=FBHl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCyEjPU6RBYEXp62+2/PZnt38Ljol9OeDVHaboYRwy4ugIhAJn5LJz9LxBc8XcUKa801WMMrYKSggnKXG5K1620g6TT"}]}, "maintainers": [{"email": "<EMAIL>", "name": "aeh"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jeffroh81"}, {"email": "<EMAIL>", "name": "noblesamurai"}, {"email": "<EMAIL>", "name": "tim<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "tomns"}, {"email": "<EMAIL>", "name": "wayno"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_4.2.6_1595147134670_0.7228934841488273"}, "_hasShrinkwrap": false}, "4.2.7": {"name": "ffmpeg-static", "version": "4.2.7", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"install": "node install.js", "test": "node test.js", "lint": "eslint .", "prepublishOnly": "npm run lint && npm test"}, "ffmpeg-static": {"binary-release-tag": "b4.3.1", "binary-release-name": "4.3.1"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "contributors"], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "GPL-3.0-or-later", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "engines": {"node": ">=10"}, "dependencies": {"@derhuerst/http-basic": "^8.2.0", "env-paths": "^2.2.0", "https-proxy-agent": "^5.0.0", "progress": "^2.0.3"}, "devDependencies": {"any-shell-escape": "^0.1.1", "eslint": "^7.2.0"}, "gitHead": "2fce51e2d97c880befb12c5b1b481dbac457569e", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@4.2.7", "_nodeVersion": "14.7.0", "_npmVersion": "6.14.7", "dist": {"integrity": "sha512-SGnOr2d+k0/9toRIv9t5/hN/DMYbm5XMtG0wVwGM1tEyXJAD6dbcWOEvfHq4LOySm9uykKL6LMC4eVPeteUnbQ==", "shasum": "92ae531ca87748357adf1f05a218a7fadc51fd44", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-4.2.7.tgz", "fileCount": 6, "unpackedSize": 12612, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfOYQcCRA9TVsSAnZWagAAcgsP/RqeoXAVA7mVcbYiI4TX\nBJCZY+HZmOn6mBtqz3bhmqJ/L+fBHbQ1+y76+Xe8o+3NcMH4AOe7HceA1e8+\n0igCSpzAAY98G3V7WhHL6shv/SVUuJy3w6sbshSS0bm7lIfgFGBWlv9TVkVf\nk2Ii4hsX2eNToxqUutJlKcpC5LHBrBJ502DZ6XEyXUxCG/mpoPQoqRBZcWsz\nuXz68CyKej7T4MX0wPRcJwe6SQ1sIqv3aDJcDU4+Si1o1W51df0lgXteGd23\ner3fZAKbwfPzCNlmIdAgKq1zR48RcAR6rYT5W1R8eOOramxLGJgb8xznh+sL\nZikb97aPbyXplOx4JAqNZy2viJW8IzjfCvHciVUcKRMFiUAdYBTLptglPex6\n0NUdmrKB0D69PYwQJqrJ7O8fCojPQyzhKLxGnMo4X2Eb1nZ7n6dfFJX2Mnxr\nLZ6AA6irgevZC0OotSNyih1m003sEa9WVZk1JW1iP879W9F24DU69tCmfxQ4\nLcYzMJq1W8x9CbjVcHcw2BnWLDcj5P12i5JdrBScsqn1ZegbTqfGNKA93mtT\no/EORwduSJv/mAHwCUr0aO6PbGcK3lLKjoBdaB6Fl9B05DD2cCeqRRCYEhaa\nXafG2ElWKhLqVO6wjZpJDFEfBpQfWl6pnEFaUZMYD7HjnetcgpR/GtOjrfci\nrPJG\r\n=zdm/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDSfraqSYOdsrgOdRykiSF0T7Gipdsqz7p5VpMAHJAP3wIhAI2IoUJ3NIT51wwAivgqm/RtyiiVEs7T5C04dRMg8h76"}]}, "maintainers": [{"email": "<EMAIL>", "name": "aeh"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "jeffroh81"}, {"email": "<EMAIL>", "name": "noblesamurai"}, {"email": "<EMAIL>", "name": "tim<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "tomns"}, {"email": "<EMAIL>", "name": "wayno"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_4.2.7_1597604891414_0.8209142517991461"}, "_hasShrinkwrap": false}, "4.2.8": {"name": "ffmpeg-static", "version": "4.2.8", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"install": "node install.js", "test": "node test.js", "lint": "eslint .", "prepublishOnly": "npm run lint && npm run install && npm test"}, "ffmpeg-static": {"binary-release-tag": "b4.3.2", "binary-release-name": "4.3.2"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "contributors"], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "GPL-3.0-or-later", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "engines": {"node": ">=10"}, "dependencies": {"@derhuerst/http-basic": "^8.2.0", "env-paths": "^2.2.0", "https-proxy-agent": "^5.0.0", "progress": "^2.0.3"}, "devDependencies": {"any-shell-escape": "^0.1.1", "eslint": "^7.2.0"}, "gitHead": "1794103d9b2713a8c9da23207e970cda0a12db61", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@4.2.8", "_nodeVersion": "15.10.0", "_npmVersion": "7.5.3", "dist": {"integrity": "sha512-7zdrHt+STR/vzKfF7aO79zyyBknxxEO0SjJnZvOoA2i0RUiAzNo/jv5QLa05poIFAdnuvQSb3ExV4rzkXzhhFQ==", "shasum": "ad5fd9c1279a81ad0bc114b6ac71dc08480cb06a", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-4.2.8.tgz", "fileCount": 6, "unpackedSize": 13150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgUf5QCRA9TVsSAnZWagAAfkYQAJ2SKZGOksttUd2aUWlM\nxo76Np2ME49DDjMbmWiAffomtFH/1SijMauVYPrMIFIq8jJuJ9Li3usodwHO\nwu12ha1Nsg5lxCNjGvhjsngJYPcrw3IUZhkzZaRdvk6akR5GcGhFc66UOyqR\nb6FxARMRq6ddYhmrRA28D74yRpYgvrVm48ooyiDjDyt3p6rXRwH7HTDAC5xz\nHlSUt4ysjysJJN+it13No89OMY6ri4tF920LO3D0dUY/qzT3amQlnp+oASA7\nHrW9Va0KuNLmg7R1pfdqoe2YenHxc0yUaLt5Hs0xcmDfXyy7ndckvxsPZEj0\nzRbfFo6/RrutJfMp4erb+O9tTkQRn7p89GN753pfK2f5se6eygymMH5XQNiB\nzyIrP8EJ374ARB/j97DpJsT5H+Mgohlo4jcZhq3kiVdYlK9RwQAXi59d9MvS\nHy2b5Udbip/1Mkw/dBL1gGz55uMHikfxn8I3oz7lbliu3un4ecy7Sx3k33CQ\nDRJH7NkahAGTjkkTcjfzmyRpEqoU1hsrwgJtLrVQKTDvnkEcBwYAV/8LFvJD\n5r/stvlv5WJB5h4/1Knwz6Jhjib0tBoLYqAkuoJ9prpj0QVnhVLZjRWota0b\nGP6aF0Um001Qcrexd+Y669qupAVaXKtDGVpRd6NxXIM+8kM5DoE+B4ABYJ9R\nLYLB\r\n=SQpi\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDf8t6JcSphh6gfCx9xPsbvD4bty8VMA/3GuQxKVPhpEQIgVig96sacjKR/B7IfiDFtCrzJ437/8BAveokl6o5Y+6E="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jacob-samurai", "email": "<EMAIL>"}, {"name": "watermelonpizza", "email": "<EMAIL>"}, {"name": "noblesamurai", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aeh", "email": "<EMAIL>"}, {"name": "jeffroh81", "email": "<EMAIL>"}, {"name": "wayno", "email": "<EMAIL>"}, {"name": "tomns", "email": "<EMAIL>"}, {"name": "graven", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_4.2.8_1615986256451_0.011779182220077677"}, "_hasShrinkwrap": false}, "4.3.0": {"name": "ffmpeg-static", "version": "4.3.0", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"install": "node install.js", "test": "node test.js", "lint": "eslint .", "prepublishOnly": "npm run lint && npm run install && npm test"}, "ffmpeg-static": {"binary-release-tag": "b4.3.2", "binary-release-name": "4.3.2"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "contributors"], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "GPL-3.0-or-later", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "engines": {"node": ">=10"}, "dependencies": {"@derhuerst/http-basic": "^8.2.0", "env-paths": "^2.2.0", "https-proxy-agent": "^5.0.0", "progress": "^2.0.3"}, "devDependencies": {"any-shell-escape": "^0.1.1", "eslint": "^7.2.0"}, "gitHead": "251282ad5020ea79de5a07fd7aeaa16e5c419679", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@4.3.0", "_nodeVersion": "15.12.0", "_npmVersion": "7.6.3", "dist": {"integrity": "sha512-w/tXYGlOSeAkPHjypjzylaChLrG5wRzHFyB47KFRDsGyBxUJJWiq9I/39/e6r9Y4aY1gzpejTLg5Aa0aqb0XXA==", "shasum": "fa5f689eacb0b3f7ed3eaa52760d132d9db818ec", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-4.3.0.tgz", "fileCount": 6, "unpackedSize": 13172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZaOoCRA9TVsSAnZWagAAr5YP/2kAn7c7/AVyJQaOY2JS\nU8LhqQjXw+F9iWC2zITr6bhYoebTMcNSeuAwqOdtjGqzPJAUsoThdVyq1G4W\n9PTZiCByqG8ktjoYNJ1GoP1ZouYrlzaRLy24i21g5/H6qJ0YHt1brhE+XCJj\npsT9SVOvTSnBCyxc6g8siza7atyLfAJbd3/Tk0Gb8N2vU/9ddmzto4kS5E3d\npdS4QS+I0pm/AgEH1tne6PtRoPQh58LKB35OBMzvt1wIvHeArn3ShNr1vB1/\naDEF1ghl0SzKesSeBnv94DrtPHGBFXxrjcBmFZ8zDd9HeZ22rgbtq2xPY/ws\nCndUn32otuP+n/1AabRigym++ODrEG7s2iYPt2+bVwIEuSRM9uicAC3PwbEa\nEqQLxW6Wt6JuiD0G3HGZB5Pwpnob3dfJyzpCiV0L8yoaEawhFL0LCHIEtBwF\nX2vJu7NAcxnT/TInYSlQThN6bMN4f1cU824w0A+9UtJ3tk7YsKA4fFptK8uN\nzSvXtCh8DUcXZayy2mikuTc0w4ysh35DB60lhQsI9d+fQs9V0LHZmI9mZqMX\nqkxQysh749j/g73SbOLwgtMplyZpY9mCCNEPeTPOr1mK6uY+NWs2fRnFUtw2\nWyEtwBwoZrOIQ1nWGCntN6/58Rxkg2Ap5OlOz3zWJ080muL0CUBpwYT7WBlO\nFcY7\r\n=t0xI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDd32EelFQXvjKlIkqXETUHsTOzcZv5rAOQ+9sYeHRviAiAguFtZ44efuwP3Y4ug6C+vyy0vBb3uvbe5xlzweFHKWQ=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jacob-samurai", "email": "<EMAIL>"}, {"name": "watermelonpizza", "email": "<EMAIL>"}, {"name": "noblesamurai", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aeh", "email": "<EMAIL>"}, {"name": "jeffroh81", "email": "<EMAIL>"}, {"name": "wayno", "email": "<EMAIL>"}, {"name": "tomns", "email": "<EMAIL>"}, {"name": "graven", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_4.3.0_1617273768226_0.3916783600540539"}, "_hasShrinkwrap": false}, "4.4.0": {"name": "ffmpeg-static", "version": "4.4.0", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"install": "node install.js", "test": "node test.js", "lint": "eslint .", "prepublishOnly": "npm run lint && npm run install && npm test"}, "ffmpeg-static": {"binary-release-tag": "b4.4", "binary-release-name": "4.4"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Thefrank"}], "license": "GPL-3.0-or-later", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "engines": {"node": ">=10"}, "dependencies": {"@derhuerst/http-basic": "^8.2.0", "env-paths": "^2.2.0", "https-proxy-agent": "^5.0.0", "progress": "^2.0.3"}, "devDependencies": {"any-shell-escape": "^0.1.1", "eslint": "^7.2.0"}, "gitHead": "b52c7755d251709ed94d8954077c3f67009d9d01", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@4.4.0", "_nodeVersion": "16.3.0", "_npmVersion": "7.15.1", "dist": {"integrity": "sha512-NIJHVPXlSsIK9pYvsTPh4ZlppauorpPLLeOaIG7VOXWQck4Fx4Qi7Ahe+j8mj8KZXhWwCg3Hx46JdWAIOWLcpg==", "shasum": "56f1fbff5dbfd33e368ce309333c8c12ffdafb77", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-4.4.0.tgz", "fileCount": 6, "unpackedSize": 13453, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgzcuWCRA9TVsSAnZWagAAe2QQAJ42Ngjyic330ZgNlQoQ\nGIxQFfkaiS7naxVhm/woiIKtgcaEowMhmlXeXErcTnxdZqKKtnveg5a27/cG\nl0u9etUMv+K1FQfKdjU6kJEFzZV2bqPKlneScsr74kkRZMTzATzNAr96SOQ6\nKNCzkKsYCpIpGPiM2FTB6/HTLuRX6wRv/Y2RpmlYO+qToas8qNloNvrW6IN+\noT6YrG3NPbubTE3n6of+RDn8ebM0YeKoZ5w2YoFcg3duo2GG3ZThL53KkC6l\nmLeWWhE1v6LCJKOeDeAaomYPgkjjCCKsJpvZ8iTIJPBL8JDWy582m7YgFymC\nfQQEynn69lI/Lm1wQGfFWxK2jArXZebFFz4v1ff72iWQFpzpHQOgfcbDktgq\nQqdwQLWJAFpmSgFx+k0022Nt78ZjrX36rS+32J7PY8xR78Ek1yHtIjI2duG5\n6IH2lZJXVa9j/bg5cy0chSOpMFpyQCu02EZlwJqOWRmjVGKmq+Bq67mMQ0xR\nB0X/vu0er23f5vlkdSqDN9CFDcSw63IdmWMW7OqHeaDOR7WCOW57J+zyZsMo\naNUQ88ke7lFOwmuKFwd3hO6CitXucYHh8LBIYDv36uL0pAsqNHARf9jZNj5d\nJZUoPnBYk5VF6qbYojr4ABLToensMEFVmTNaJOjq494sQzPH5r2KvIGK/CoH\nsiPq\r\n=eM6u\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDsf0DQLQWq2CwEc8deGm7zR38VLw7/AKwS0ZljVt30zAIgDYtgX/YCX4a/ex5fpXT0aklcmAWS+BrMq/ckOe0s3m8="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "jacob-samurai", "email": "<EMAIL>"}, {"name": "watermelonpizza", "email": "<EMAIL>"}, {"name": "noblesamurai", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "aeh", "email": "<EMAIL>"}, {"name": "jeffroh81", "email": "<EMAIL>"}, {"name": "wayno", "email": "<EMAIL>"}, {"name": "tomns", "email": "<EMAIL>"}, {"name": "graven", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_4.4.0_1624099734074_0.018155708410802163"}, "_hasShrinkwrap": false}, "4.4.1": {"name": "ffmpeg-static", "version": "4.4.1", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"install": "node install.js", "test": "node test.js", "lint": "eslint .", "prepublishOnly": "npm run lint && npm run install && npm test"}, "ffmpeg-static": {"binary-release-tag": "b4.4.1", "binary-release-name": "4.4.1"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Thefrank"}], "license": "GPL-3.0-or-later", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "engines": {"node": ">=10"}, "dependencies": {"@derhuerst/http-basic": "^8.2.0", "env-paths": "^2.2.0", "https-proxy-agent": "^5.0.0", "progress": "^2.0.3"}, "devDependencies": {"any-shell-escape": "^0.1.1", "eslint": "^7.30.0"}, "gitHead": "474742336b75588c415e92f968e95fa1dee088dd", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@4.4.1", "_nodeVersion": "17.2.0", "_npmVersion": "8.3.0", "dist": {"integrity": "sha512-gyGTIf5kgmLDmH7Rwj8vMmNK46bjXKKofHS2gY+LUqoTe/iybVuTuvnhJQR2tZHlKlDG7A/BIH7cRa2jWDKgWw==", "shasum": "ec465f61f9c4457aa9c1b2b0761a918021a46a77", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-4.4.1.tgz", "fileCount": 6, "unpackedSize": 13466, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh0fH9CRA9TVsSAnZWagAA49MP/2+gnCv2ZZ3t5Ya7CkIK\nTBP6+2O4/FLe431+b4Oomp0xdslMjHB6orNzycNFbY+NgamRcanwE9Tz1O7K\nVBRA5LEYGPWOLQ4C3P1Ugmj5r4t0RJ1lE1Ao8L0jKf6nG84s+iVgOPS2pqrJ\nCyM+dacJ/0mm72K4tYlmhZPSMBIpQUP32huUVACBk9fPc0b4xjlH3lkOPBbQ\nHpjsce3S1eM+zCwJkiMtmch9vdg2kjNPsJgguqCOwtuwUDT0a7YlBiSAtu21\n6gOdDvqpBnvz191gu+WyIxofXhVwJE+odVc4INRIvjmTbyhJMaR8mX5/ZlN/\nSlZCoUKENPRPHanAhcKkGs1a+ALxkBDILEiyd5LwSLRsbHp1sbY7CpuR+yJS\nEvQLn1sX3o6dxoV5GGggecAhVXYQvQg+FdPrxcQIF/RunAnrWyMXogEBzAnr\nZdYg9BcYgs2I5nQ7rwxTLo0VeGrrF0d/M3D8xN6La1NL8e2xN/k1xEBTvOKe\niLpzMf87cuDOkuvqvTbL6WNUeIqIKbiQV6qUQeATD2WlO/y1RMovBjZRI1cO\n2sinCLHI0lXbxYbmRr1/BIL0/Sa35vj/AE0kIyIbUkO5bBPGCowOGgcE8ePV\nruleXam50t+imk+ZqKcoDv0qYuc66HdcJzDRRGITuvXF2lh32tNMlFbDjTs9\nAg2W\r\n=zBKI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMAjoohPSgN7gMyrnABpXPg3xPCup4XMDJsEPLR2FbVAIhAPmuUZ0clFbZgMiVbWZRPv12+piEfWyW+6x6rZoT21oS"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "noblesamurai", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jacob-samurai", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_4.4.1_1641148925169_0.5113467671383174"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "ffmpeg-static", "version": "5.0.0", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"install": "node install.js", "test": "node test.js", "lint": "eslint .", "prepublishOnly": "npm run lint && npm run install && npm test"}, "ffmpeg-static": {"binary-release-tag": "b5.0", "binary-release-name": "5.0"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Thefrank"}], "license": "GPL-3.0-or-later", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "engines": {"node": ">=16"}, "dependencies": {"@derhuerst/http-basic": "^8.2.0", "env-paths": "^2.2.0", "https-proxy-agent": "^5.0.0", "progress": "^2.0.3"}, "devDependencies": {"any-shell-escape": "^0.1.1", "eslint": "^8.6.0"}, "gitHead": "e546e43fe45fe1d4d2949a15c47bcca6e1eecbc1", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@5.0.0", "_nodeVersion": "17.5.0", "_npmVersion": "8.4.1", "dist": {"integrity": "sha512-z96jGVa8uCVMnyrTvbDZe0d5zbvIVQnfBN+vKDZoVK+ssYpfUJGEmo2DkuY43CA9Ytlsk+5ZVTFTqzvKw1gSaA==", "shasum": "adb2d083c5160e034c1053164d214953e061e368", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-5.0.0.tgz", "fileCount": 6, "unpackedSize": 13612, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiH1asACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQIA//UXkV6QExm/AXTpoy0drJtS1nAxpesJy0nz90NWIjlOl/v7qy\r\ntJPOz9CAzF6xB0UPUeJJU03KiJun8ZOtwxgLMNSjrutJnTWdr6qaC/zBWXje\r\n9yOCiNPB9Xa2l5HF+yGyaLSt+spmkLpksvOBl6UXz4qIxyx+3c087nLUM2VU\r\na/pw4GFXOexnhdUBdjBWU8H6l+UWcFD2fQZcJ8ljG33Yyln2/BGiKqU89JWH\r\nvlEjrFw8Z4SbU7A5jOUTblLWeWBYyb0ykB8rLl+hDakTVLDD34hUKKoF/YVU\r\n2ie02uKi8HDjZse/LyFPTTdnPiak67o0B2bIq6bKYZwZis1ai7R93nCNAYvy\r\nRL2mZbjQPwFgZUuIV4+TgKAbP51pihqMfVYh4EkLRUof3zGBcczGQy/LvPJ7\r\nFST7I5D0OGMOR3fMO2FU3tkdd2uh59mWeUKVx6yWOYO/BqMxcjwWAdqwqr16\r\nKs9zTIn16a5YvEf7yqtoSXxHrNPC/jmSbr03HLEvDd3SMb3yTvqWHySIJC77\r\nDVR20gybChf+SymByBpcTF22d3zYLJymrwLi4sru7UxRESyTtmUbQ0QfQQ/o\r\nBGeu1+TYwau4NMED7Sa1z2DlBsUW/1ZFjsxQyK9Y5/18qyNwyDCH4tBsHps1\r\nf3UnpscL3EboMA7MQBSJl3TptPftawZmPUU=\r\n=FLSD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID8ZUr/I380Ei5wbTgVggSISUnyIMBp9Vdto8G0477sVAiEAoSZ7uzPmap1zRzybPh/K1KzwPssB7couoYbOvsFeGHs="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "noblesamurai", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jacob-samurai", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_5.0.0_1646220972417_0.11863719860485356"}, "_hasShrinkwrap": false}, "5.0.1": {"name": "ffmpeg-static", "version": "5.0.1", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"install": "node install.js", "test": "node test.js", "lint": "eslint .", "prepublishOnly": "npm run lint && npm run install && npm test"}, "ffmpeg-static": {"binary-release-tag": "b5.0", "binary-release-name": "5.0"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Thefrank"}], "license": "GPL-3.0-or-later", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "engines": {"node": ">=16"}, "dependencies": {"@derhuerst/http-basic": "^8.2.0", "env-paths": "^2.2.0", "https-proxy-agent": "^5.0.0", "progress": "^2.0.3"}, "devDependencies": {"any-shell-escape": "^0.1.1", "eslint": "^8.6.0"}, "gitHead": "9de18647d5d571e28e80098a5886967a21a00a33", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@5.0.1", "_nodeVersion": "18.4.0", "_npmVersion": "8.12.1", "dist": {"integrity": "sha512-Q3iG0qDBfkGnsQcuK6cZZyFWO2lXwc1FgyiPoWOqjuB6JGM15ShUWUmccDBDzXa4TYfdTgikNUtqc2pAa8iNYA==", "shasum": "5e1dbeed2a71161cde8efddead65a5e4ac626314", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-5.0.1.tgz", "fileCount": 6, "unpackedSize": 13617, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDf9zR2wDUXg6MLSEdie1Eryf3Vm81fxq+6FFSIkM77xwIgPovIZyEL2hJ1twD+2ZA+RvgKLpiIz9KLptaI3+xFBXQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivA+xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrlwg//UTnRo7g0ce8XlIxujv7/U6034e9FmONms1PcP3csKzrHpn4D\r\nTUeXFEc9wJoXhpCCzuoX70UFt0T02epzbyxC3PTTbCyyaoS3CHMFIFqRMeR0\r\nTWZJDDLiuZ6J1Gu740Q5a3Rm5oCvGckoCxeIGzSvWX1C6zeEfvoctB048wig\r\nhZwt4aXvrbQx43fE855/QUW380LSGwYRId7fQh69flNOwvUpy0lnEwnQlWpx\r\nu1qVsQ2GcnNsfe6ryRbhkqUODEkgYps++Gt8z1ed2N0fKdgZ/dzbiwwuWNYH\r\nZO5xqBg18MyGYjijkYyYRzyVNn8YoZcsF+fMTHPMyLErUMKtqWgT0fImIwWf\r\n9M+q4u0XWdoUvCjTyA5quYmxfb//hBYJG3akyT9EUGd9E52BsUGerkSRnHGq\r\nbM+Fzf38VbyH9/o75UCUAufVl2vKs1khoLA8Tkzlpzq8NBsO+qaXRz9N3lrC\r\nZg+l2a+glCtUqPmV2x1u80FRDq+Gjfw0zuGb82JtFfIZRH7UE2glDzwvHjh0\r\ncGYvTlwYZBZL1vYtaqtUXU9oz7QiP/yVfpRgO3pBtuLME5ncyXRmMhReEwqf\r\nSXYHzoRGGaf4ibAVyWslKJCTfIvJffzdBhUokvESEJaOfR9SqwMu+x7u3FOQ\r\nAbO1MBeqcJd3W7227nk/amGkrwEINBQutwU=\r\n=Iogs\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "noblesamurai", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jacob-samurai", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_5.0.1_1656491953143_0.8796595372582094"}, "_hasShrinkwrap": false}, "5.0.2": {"name": "ffmpeg-static", "version": "5.0.2", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "scripts": {"install": "node install.js", "test": "node test.js", "lint": "eslint .", "prepublishOnly": "npm run lint"}, "ffmpeg-static": {"binary-release-tag": "b5.0.1", "binary-release-name": "5.0.1"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Thefrank"}], "license": "GPL-3.0-or-later", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "engines": {"node": ">=16"}, "dependencies": {"@derhuerst/http-basic": "^8.2.0", "env-paths": "^2.2.0", "https-proxy-agent": "^5.0.0", "progress": "^2.0.3"}, "devDependencies": {"any-shell-escape": "^0.1.1", "eslint": "^8.6.0"}, "gitHead": "4024c7879f4fad01456f3767b29a9a517e2edd96", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@5.0.2", "_nodeVersion": "18.4.0", "_npmVersion": "8.12.1", "dist": {"integrity": "sha512-rYeA04AGbvxbUxov6Cn/KDKIzw2rmzwPlgHoqn837NZt0xPdOVA9mJMILz9IX27R45hhSlXS6nThk85XxDivLg==", "shasum": "7e5bae2bf78728b79b85c43979a14e2a8458cf72", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-5.0.2.tgz", "fileCount": 6, "unpackedSize": 13628, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF/iQahGpMPeWXXnKwGS/tKsGeS+7yl8JFURn/vYnJbJAiEAu36YDiCRHq87fZOS72fOiyQ95BMwYnS1eyoQQ43oEMM="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivGKUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoW5g//VSuPCfT/kWMv/o6ZcGa/mWiymVgKbZOFwPb4uF70tW978FYs\r\nMqUUf8jYVFK6cvB+esADnHpN9DmsuUwOS+jUffvS3dAkRPp2Jsejfs/70TKk\r\nb03jSwZrPEa1QmnboWWW2D7FqRphvvc2RXSTkh0QY+O10cFRokWuo7YrGXit\r\nH59i3j65LIPbS93MZrJ9SXqUFdS5kSh9HrJRBf/4RYfwyaxDlFZ1/eYpXRjj\r\n2zLOp/j1eS1eTv02C+Hs2xLS0jSucQ5+Go1cOXa13oKbTmxR3lpgor2XukoW\r\nchYD2ceUwhMg+wuX5Lw+hk1zC8WVPVBxIp6RF8hvNE/5aPboCeO/s166/PZt\r\n6cQh3raTCH4lG4CV264bu/trg9pUGUtpCwPUOs+3OeAcNzGAdddb7r+immKq\r\nAySGNp9rBLgxtslfxfCM3dVfl1EZIktiII7GWUbQxyPBvCSwTgRYamtu1Q/Q\r\ngk5lVgXWteD7J0uFADWXpijAGV/DqK22BAQ/ycjM69U0TDAQtxcfOUjvH00P\r\n6Tr9v+DBKelmzAdcNgUPvcdmvXeF7U7Wh+Ta8YgnFCpjqKyGibRpe3Y6Xjij\r\nXaYcpoyBGMej7rEKiZPEn0S6WHsUD7OlXlSiWfuLBfQY1iGyyySmNdQO2yCn\r\nkyLtBQPd1U8r49LioHJ8l//+smkHxfkJX0w=\r\n=QYiT\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "noblesamurai", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jacob-samurai", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_5.0.2_1656513171908_0.07524253462319352"}, "_hasShrinkwrap": false}, "5.1.0": {"name": "ffmpeg-static", "version": "5.1.0", "description": "ffmpeg static binaries for Mac OSX and Linux and Windows", "main": "index.js", "types": "types/index.d.ts", "scripts": {"install": "node install.js", "test": "node test.js", "lint": "eslint .", "prepublishOnly": "npm run lint && npm run install && npm test"}, "ffmpeg-static": {"binary-release-tag": "b5.0.1", "binary-release-name": "5.0.1"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Thefrank"}], "license": "GPL-3.0-or-later", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "engines": {"node": ">=16"}, "dependencies": {"@derhuerst/http-basic": "^8.2.0", "env-paths": "^2.2.0", "https-proxy-agent": "^5.0.0", "progress": "^2.0.3"}, "devDependencies": {"any-shell-escape": "^0.1.1", "eslint": "^8.6.0"}, "gitHead": "dce6d42ba772a5769df8181e704772db4456ef16", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@5.1.0", "_nodeVersion": "18.6.0", "_npmVersion": "8.15.1", "dist": {"integrity": "sha512-eEWOiGdbf7HKPeJI5PoJ0oCwkL0hckL2JdS4JOuB/gUETppwkEpq8nF0+e6VEQnDCo/iuoipbTUsn9QJmtpNkg==", "shasum": "133500f4566570c5a0e96795152b0526d8c936ad", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-5.1.0.tgz", "fileCount": 7, "unpackedSize": 47895, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCmMntHe4L5JDjAeScw+yxUWVVoJ/cSlStCLkjTIGuW0wIgfZDEwxhkYJ/4id7ZjGruL9CDKT4msuwfxQLbe7zorQg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjC3TJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgwA//Z1vAWwnYPH9r04vt2W+/uVfvY/RPaGazKyCFT6Rm1aMJ2StS\r\nWLfaFjJrLn7KDJwHcR31OnrCb9caH7y+J2MDpDSPlxjfT7dfYJOd6AiXqqLj\r\nw6j7BjOw3dpeTbR3w7RNYRgF52UgkstcpDdUrt5EqayodvE2zkhYd2S2rU8s\r\nWxEsb+atGtwbchT+Me059CynQ5I3fv4OTrjZP0EBQ4JkHYyCk/qRDD7jb6qx\r\n3snEiMyrKM6pz+SN5hRQtOG2b97g97TwDiKKffWqLy1J9UINt82In7hk7PlI\r\noLqprqZjpBBgODY7PUSbj2s71kNGPhSKg4UVpTaExGJkN9201xS/quX/dRLr\r\nLiKWmt04iYk1ISJ/5h6r1IJbWkoksKgPzXdI9XmPMaQSGFZAVNtL95gUglCg\r\nJpWK6g8hd/+F2dCDEQUHqRcyBoZlflTFA51Da6yvTZEea02i/vvwcGCtc/dX\r\nBf28R7XtWhGk9vivx4Je1yWYrwRiHOmvObnCHZa7WrHsUhCxs5qW9JZqNFwR\r\nAjLjntEReDwO9sDBRp4/KLYY0nEQdcMkFybVcdfrhgPwMOMfKchzIOiTW/oA\r\nvtMp78cnIKx/289jmKiDcMVuwtDvwH3UNr+n8i7QoWZg3LdsoZ5ldkm4ryFS\r\nl+eBZ0QL9b4qUumY60Jd+Mg+UfgYfDj5Dro=\r\n=+wzv\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "noblesamurai", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jacob-samurai", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_5.1.0_1661695177660_0.7035939719023747"}, "_hasShrinkwrap": false}, "5.2.0": {"name": "ffmpeg-static", "version": "5.2.0", "description": "ffmpeg binaries for macOS, Linux and Windows", "scripts": {"install": "node install.js", "prepublishOnly": "npm run install"}, "ffmpeg-static": {"binary-path-env-var": "FFMPEG_BIN", "binary-release-tag-env-var": "FFMPEG_BINARY_RELEASE", "binary-release-tag": "b6.0", "binaries-url-env-var": "FFMPEG_BINARIES_URL", "executable-base-name": "ffmpeg"}, "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "keywords": ["ffmpeg", "static", "binary", "binaries", "mac", "linux", "windows"], "authors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Thefrank"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "GPL-3.0-or-later", "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "engines": {"node": ">=16"}, "dependencies": {"@derhuerst/http-basic": "^8.2.0", "env-paths": "^2.2.0", "https-proxy-agent": "^5.0.0", "progress": "^2.0.3"}, "devDependencies": {"any-shell-escape": "^0.1.1"}, "main": "index.js", "types": "types/index.d.ts", "readme": "# ffmpeg-static\n\nStatic **[ffmpeg](https://ffmpeg.org) binaries for macOS, Linux, Windows.**\n\nSupports macOS (64-bit and arm64), Linux (32 and 64-bit, armhf, arm64), Windows (32 and 64-bit). [The ffmpeg version currently used is `6.0`.](https://github.com/eugeneware/ffmpeg-static/releases/tag/b6.0)\n\n[![npm version](https://img.shields.io/npm/v/ffmpeg-static.svg)](https://www.npmjs.com/package/ffmpeg-static)\n![minimum Node.js version](https://img.shields.io/node/v/ffmpeg-static.svg)\n\n*Note:* The version of `ffmpeg-static` follows [SemVer](http://semver.org). When releasing new versions, **we do *not* consider breaking changes in `ffmpeg` itself**, but only the JS interface (see below). For example, `ffmpeg-static@4.5.0` might download ffmpeg `5.0`. To prevent an `ffmpeg-static` upgrade downloading backwards-incompatible ffmpeg versions, [use a strict version range](https://docs.npmjs.com/files/package.json#dependencies) for it or use a [lockfile](https://docs.npmjs.com/files/package-lock.json).\n\nAlso check out [`node-ffmpeg-installer`](https://github.com/kribblo/node-ffmpeg-installer)!\n\n## Installation\n\n``` bash\n$ npm install ffmpeg-static\n```\n\n*Note:* During installation, it will download the appropriate `ffmpeg` binary from the [`b6.0` GitHub release](https://github.com/eugeneware/ffmpeg-static/releases/tag/b6.0). Use and distribution of the binary releases of `ffmpeg` are covered by their respective license.\n\n### Custom binaries url\n\nBy default, the `ffmpeg` binary will get downloaded from `https://github.com/eugeneware/ffmpeg-static/releases/download`. To customise this, e.g. when using a mirror, set the `FFMPEG_BINARIES_URL` environment variable.\n\n```shell\nexport FFMPEG_BINARIES_URL=https://cdn.npmmirror.com/binaries/ffmpeg-static\nnpm install ffmpeg-static\n```\n\n### Electron & other cross-platform packaging tools\n\nBecause `ffmpeg-static` will download a binary specific to the OS/platform, you need to purge `node_modules` before (re-)packaging your app *for a different OS/platform* ([read more in #35](https://github.com/eugeneware/ffmpeg-static/issues/35#issuecomment-630225392)).\n\n## Example Usage\n\nReturns the path of a statically linked ffmpeg binary on the local filesystem.\n\n``` js\nconst pathToFfmpeg = require('ffmpeg-static')\nconsole.log(pathToFfmpeg)\n// /Users/<USER>/playground/node_modules/ffmpeg-static/ffmpeg\n```\n\nCheck the [example script](example.js) for a more thorough example.\n\n## Sources of the binaries\n\nThe binaries downloaded by `ffmpeg-static` are from these locations:\n\n- [Windows x64 builds](https://www.gyan.dev/ffmpeg/builds/)\n- [Windows x86 builds](https://github.com/sudo-nautilus/FFmpeg-Builds-Win32/)\n- [Linux x64/x86/ARM/ARM64 builds](https://johnvansickle.com/ffmpeg/)\n- macOS [x64 (Intel)](https://evermeet.cx/pub/ffmpeg/) & [ARM64 (Apple Silicon)](https://osxexperts.net/) builds\n\n## Show your support\n\nThis npm package includes statically linked binaries that are produced by the following individuals. Please consider supporting and donating to them who have been providing quality binary builds for many years:\n\n- **Linux builds**: [John Van Sickle](https://www.johnvansickle.com/ffmpeg/)\n- **macOS builds**: [Helmut K. C. Tessarek](https://evermeet.cx/ffmpeg/#donations)\n", "readmeFilename": "README.md", "gitHead": "bfa9ffa1d375b2aeea1194f0129e5c404bece471", "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "_id": "ffmpeg-static@5.2.0", "_nodeVersion": "20.0.0", "_npmVersion": "9.6.4", "dist": {"integrity": "sha512-WrM7kLW+do9HLr+H6tk7LzQ7kPqbAgLjdzNE32+u3Ff11gXt9Kkkd2nusGFrlWMIe+XaA97t+I8JS7sZIrvRgA==", "shasum": "6ca64a5ed6e69ec4896d175c1f69dd575db7c5ef", "tarball": "https://registry.npmjs.org/ffmpeg-static/-/ffmpeg-static-5.2.0.tgz", "fileCount": 7, "unpackedSize": 48185, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDF2fTwuiPJk+Q3B2gXloL1ljoo9LQJT9apwUZ37zSP/AIgAX1CY4InzlEQHAFC+dZuFttwlToMeMlwLRcdw+fWhpg="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "noblesamurai", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jacob-samurai", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ffmpeg-static_5.2.0_1688743588388_0.5602314747952941"}, "_hasShrinkwrap": false}}, "readme": "", "maintainers": [{"name": "noblesamurai", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "jacob-samurai", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-08-17T14:45:48.672Z", "created": "2015-01-19T07:42:59.029Z", "1.0.0": "2015-01-19T07:42:59.029Z", "2.0.0": "2016-06-30T06:30:01.702Z", "2.1.0": "2017-11-29T05:00:15.658Z", "2.2.0": "2018-03-15T18:21:07.773Z", "2.2.1": "2018-04-12T09:08:03.559Z", "2.3.0": "2018-05-02T11:20:51.627Z", "2.4.0": "2018-11-19T20:57:35.482Z", "2.5.0": "2019-07-07T18:58:41.008Z", "2.6.0": "2019-08-31T11:29:23.558Z", "2.7.0": "2019-10-09T06:58:18.627Z", "3.0.0": "2019-12-28T23:28:40.230Z", "4.0.0": "2020-01-29T01:04:47.848Z", "4.0.1": "2020-02-03T00:15:31.222Z", "4.1.0": "2020-04-05T23:29:41.382Z", "4.1.1": "2020-04-11T13:45:25.892Z", "4.2.0": "2020-04-28T13:47:23.956Z", "4.2.1": "2020-05-05T10:14:12.265Z", "4.2.2": "2020-05-19T18:24:47.514Z", "4.2.3": "2020-06-12T13:09:24.055Z", "4.2.4": "2020-06-16T00:08:14.752Z", "4.2.5": "2020-06-24T22:17:43.977Z", "4.2.6": "2020-07-19T08:25:34.799Z", "4.2.7": "2020-08-16T19:08:11.531Z", "4.2.8": "2021-03-17T13:04:16.675Z", "4.3.0": "2021-04-01T10:42:48.338Z", "4.4.0": "2021-06-19T10:48:54.246Z", "4.4.1": "2022-01-02T18:42:05.311Z", "5.0.0": "2022-03-02T11:36:12.547Z", "5.0.1": "2022-06-29T08:39:13.334Z", "5.0.2": "2022-06-29T14:32:52.047Z", "5.1.0": "2022-08-28T13:59:37.789Z", "5.2.0": "2023-07-07T15:26:28.586Z"}, "homepage": "https://github.com/eugeneware/ffmpeg-static#readme", "keywords": ["ffmpeg", "static", "library", "binary", "binaries", "mac", "linux", "windows"], "repository": {"type": "git", "url": "git+https://github.com/eugeneware/ffmpeg-static.git"}, "bugs": {"url": "https://github.com/eugeneware/ffmpeg-static/issues"}, "license": "GPL-3.0-or-later", "readmeFilename": "", "users": {"erincinci": true, "zousandian": true, "rifdhan": true, "stephenhuh": true, "temasm": true}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Thefrank"}]}