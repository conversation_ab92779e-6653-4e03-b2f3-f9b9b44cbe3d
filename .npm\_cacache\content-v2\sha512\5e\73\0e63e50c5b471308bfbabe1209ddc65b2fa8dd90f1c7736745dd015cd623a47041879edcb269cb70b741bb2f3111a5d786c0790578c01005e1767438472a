{"_id": "js-tokens", "_rev": "40-48143faa50ea9d19e1e36b9077a44d81", "name": "js-tokens", "dist-tags": {"latest": "9.0.1"}, "versions": {"0.1.0": {"name": "js-tokens", "version": "0.1.0", "keywords": ["JavaScript", "js", "token", "tokenize", "regex"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "78a0bdec6799c48128c005e1b885c05ab3567bb9", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-0.1.0.tgz", "integrity": "sha512-MRf85a3DxOQxJJ07jPxQSJaun/UqD5Uqs3e74tGX+PEoTQdgWqNqNQyuk0XrNewFvjwb9b1/lXzjb710LozGhQ==", "signatures": [{"sig": "MEUCIB9HeimuVr/Tg3Eyem07ggVfu8Ci1HbFiybZD8drWoffAiEA1aW70xS8TX7SDd7UgxGlaocI68ss6qk8QU33vDgkHgs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/lydell/js-tokens", "type": "git"}, "_npmVersion": "1.4.4", "description": "A regex that tokenizes JavaScript.", "directories": {}, "devDependencies": {"mocha": "^1.17.1", "coffee-script": "~1.7.1"}}, "0.2.0": {"name": "js-tokens", "version": "0.2.0", "keywords": ["JavaScript", "js", "token", "tokenize", "regex"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@0.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "e80d3e39faad48fb88d3b81c33cb1a4e8f3d6d97", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-0.2.0.tgz", "integrity": "sha512-ntxjvsZ8y+BinUHppSMwl6McHx0Yw7OcRJXUz7OEf7RPH9KRfDqjWyg0U+esDhUb0wwSD1viCfYe/lkkjvSe4Q==", "signatures": [{"sig": "MEMCHzp3TBprNiPryqmhrrwpCuSf1fK94k0m0eAvtsv51+YCIDpScdsNGnm+4Hi4K+vpn5yjlbwyUcwtDq6+4TisbBKK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "e80d3e39faad48fb88d3b81c33cb1a4e8f3d6d97", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/lydell/js-tokens", "type": "git"}, "_npmVersion": "1.4.9", "description": "A regex that tokenizes JavaScript.", "directories": {}, "devDependencies": {"mocha": "^1.17.1", "coffee-script": "~1.7.1"}}, "0.3.0": {"name": "js-tokens", "version": "0.3.0", "keywords": ["JavaScript", "js", "token", "tokenize", "regex"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@0.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "f81c100024b8f51b9c883ea5ce61582f4feb11a3", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-0.3.0.tgz", "integrity": "sha512-naE6ErJliJ2aFGlBX5VfNwMStrWOECDKR2p+h8aXqDBeiooYFb/cIUWaO22x6zQiwli0vI4lxciMVxRsR6uNSA==", "signatures": [{"sig": "MEYCIQCK7UvefRMTIiIyP+ZmJ7QUVXEpIQqLKC2AtJ+fgYBwVwIhAImt5Nu1XTVIgiwCrhAJU7vUUmEQa0wmrAVxZ0B6zjcX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "f81c100024b8f51b9c883ea5ce61582f4feb11a3", "gitHead": "ecac68aa369003730dc2f964bdb656088c609c93", "scripts": {"dev": "npm run build && npm test", "test": "mocha --ui tdd", "build": "node generate-index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lydell/js-tokens", "type": "git"}, "_npmVersion": "2.1.7", "description": "A regex that tokenizes JavaScript.", "directories": {}, "_nodeVersion": "0.10.33", "devDependencies": {"mocha": "^2.0.1", "coffee-script": "^1.8.0"}}, "0.3.1": {"name": "js-tokens", "version": "0.3.1", "keywords": ["JavaScript", "js", "token", "tokenize", "regex"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@0.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "ae3600436c9a79358a45dc100cf8ceaf02f386c6", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-0.3.1.tgz", "integrity": "sha512-mwNTGrIAxGAgmtuYQr7xdmGERDJgCNwo/503Ch8jWQHG+UW/E2xe4CgWFi2K4XuzkgQh2LnUAutOL8TGJ6CWZw==", "signatures": [{"sig": "MEUCIQCX+6AKg2ykSnggMV8oT7cA3NaKyOt1ypN4P56e2BQalAIgbtREo9GzdfSJHVLPxpo2KOP8bRfwO46yAN0GP3/d0A8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "ae3600436c9a79358a45dc100cf8ceaf02f386c6", "gitHead": "08a7225043538e06b01450297b793e6b55f003c5", "scripts": {"dev": "npm run build && npm test", "test": "mocha --ui tdd", "build": "node generate-index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lydell/js-tokens", "type": "git"}, "_npmVersion": "2.1.16", "description": "A regex that tokenizes JavaScript.", "directories": {}, "_nodeVersion": "0.10.35", "devDependencies": {"mocha": "^2.0.1", "coffee-script": "^1.8.0"}}, "0.4.0": {"name": "js-tokens", "version": "0.4.0", "keywords": ["JavaScript", "js", "token", "tokenize", "regex"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@0.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "3ee9173eb6b4dcbc89ea2c79def34c883bb6a637", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-0.4.0.tgz", "integrity": "sha512-xD3MjA5mFNao42PN2qEfPXXfER8k5DQKs0Oa7c5d9HKUlULPcgst22tn+GchGa1F7/+8olym43+Qo+iUTbNgwg==", "signatures": [{"sig": "MEUCIGzLYY+dCeoOfGbYVCshAPB2FtRTPY83Mk4AdjEseYKYAiEA8PNFLcR8MoFtsufmOuDmGvCCvJYpjTjcMFdR84xyAjI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "3ee9173eb6b4dcbc89ea2c79def34c883bb6a637", "gitHead": "06f5a30a3ef2957b5169f4f4c13fdf57ea028f79", "scripts": {"dev": "npm run build && npm test", "test": "mocha --ui tdd", "build": "node generate-index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lydell/js-tokens", "type": "git"}, "_npmVersion": "1.4.28", "description": "A regex that tokenizes JavaScript.", "directories": {}, "devDependencies": {"mocha": "^2.0.1", "coffee-script": "^1.8.0"}}, "0.4.1": {"name": "js-tokens", "version": "0.4.1", "keywords": ["JavaScript", "js", "token", "tokenize", "regex"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@0.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "b2170ed9dfdea05e7305039b034ef4d2c442a003", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-0.4.1.tgz", "integrity": "sha512-o54XvBq1GF/osCZSqspwBA+ujd8ErXzw2hqR9QKsGdKmFr8kLJkYgLJ95oXa/LzF7GrqKfeB3Kc9xhzrBgjuKw==", "signatures": [{"sig": "MEYCIQD1R7YUU//ILxJArpgQcfOUXHxtXbF5eQaaqwJY0yvuuQIhALXEeAkpmI6DCMgPjKtADmEkFl5oOWCkxANyWRspm6Ia", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "b2170ed9dfdea05e7305039b034ef4d2c442a003", "gitHead": "f52e451b4b7f46dc8d434853908f9b4af4bf98cd", "scripts": {"dev": "npm run build && npm test", "test": "mocha --ui tdd", "build": "node generate-index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lydell/js-tokens", "type": "git"}, "_npmVersion": "1.4.28", "description": "A regex that tokenizes JavaScript.", "directories": {}, "devDependencies": {"mocha": "^2.0.1", "coffee-script": "^1.8.0"}}, "1.0.0": {"name": "js-tokens", "version": "1.0.0", "keywords": ["JavaScript", "js", "token", "tokenize", "regex"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "278b2e6b68dfa4c8416af11370a55ea401bf4cde", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-1.0.0.tgz", "integrity": "sha512-5CoKISU6nrMoXKNWUumMLSdO4N6GctX7Vfjlja801H14CxTeozlq0OC1tTJLCi6Nqjd3qXj7UAUzkgwH0+aezA==", "signatures": [{"sig": "MEUCIArcdk7fs1+w7OwA8Gye368w5ub9NM2fkUZjtsFReYXHAiEAs+EtwqjOF447mlOvjdUaveekHWY/I692QAzQUZ0Gs10=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "278b2e6b68dfa4c8416af11370a55ea401bf4cde", "gitHead": "b465cedf5cfb366c67e1f354ff000fddb6fb5c94", "scripts": {"dev": "npm run build && npm test", "test": "mocha --ui tdd", "build": "node generate-index.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/lydell/js-tokens", "type": "git"}, "_npmVersion": "2.6.0", "description": "A regex that tokenizes JavaScript.", "directories": {}, "_nodeVersion": "0.10.36", "devDependencies": {"mocha": "^2.0.1", "coffee-script": "^1.8.0"}}, "1.0.1": {"name": "js-tokens", "version": "1.0.1", "keywords": ["JavaScript", "js", "token", "tokenize", "regex"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens#readme", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "cc435a5c8b94ad15acb7983140fc80182c89aeae", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-1.0.1.tgz", "integrity": "sha512-WKqed1YxjsT7sGqM2IdbkJHnA3rXHqFqN+4xUy973UeYNjSXZCKM3G/zUmPNYut/6D9QCUbqegDmUCQRdm0lnQ==", "signatures": [{"sig": "MEQCIDzB3FBxdMl8TQdEgerp4TYZEWC4UFuBcw7xNWDNatJIAiAMxI7cBxr2K8bRRi4megkS+D/zrNSz7/y3Ns4FbObrVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "cc435a5c8b94ad15acb7983140fc80182c89aeae", "gitHead": "94fab527b51da636ac996aa99be281746f19adae", "scripts": {"dev": "npm run build && npm test", "test": "mocha --ui tdd", "build": "node generate-index.js", "esprima-compare": "node esprima-compare ./index.js everything.js/es5.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lydell/js-tokens.git", "type": "git"}, "_npmVersion": "2.8.3", "description": "A regex that tokenizes JavaScript.", "directories": {}, "_nodeVersion": "1.8.1", "devDependencies": {"mocha": "^2.2.5", "esprima": "^2.3.0", "coffee-script": "~1.9.3", "everything.js": "^1.0.3"}}, "1.0.2": {"name": "js-tokens", "version": "1.0.2", "keywords": ["JavaScript", "js", "token", "tokenize", "regex"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@1.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens#readme", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "8647f7e13f64ac15d9357a59a346c804d53b3efe", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-1.0.2.tgz", "integrity": "sha512-MG14FeS7TU0K7xdvw6ZJFDJFKMmqB+lJshWgbH+hxRocE3Q7wY6JxTLR6x7j1KYk2x955TYNdtgQ99K45zPuAA==", "signatures": [{"sig": "MEQCIFZim7PdUpAuXJz5VE8vQ1buA0jk8KEjOLLwWh3qtSdTAiABUDR5JQQM/x/JKVfAHi6S477MmRv+ccbHXV4QHbyfFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "8647f7e13f64ac15d9357a59a346c804d53b3efe", "gitHead": "90f0f0a217984625180414763234c923aeee4af5", "scripts": {"dev": "npm run build && npm test", "test": "mocha --ui tdd", "build": "node generate-index.js", "esprima-compare": "node esprima-compare ./index.js everything.js/es5.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lydell/js-tokens.git", "type": "git"}, "_npmVersion": "2.14.7", "description": "A regex that tokenizes JavaScript.", "directories": {}, "_nodeVersion": "4.2.1", "devDependencies": {"mocha": "^2.2.5", "esprima": "^2.3.0", "coffee-script": "~1.9.3", "everything.js": "^1.0.3"}}, "1.0.3": {"name": "js-tokens", "version": "1.0.3", "keywords": ["JavaScript", "js", "token", "tokenize", "regex"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@1.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens#readme", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "14e56eb68c8f1a92c43d59f5014ec29dc20f2ae1", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-1.0.3.tgz", "integrity": "sha512-SfeDkKyjCWzOfBEyjRcmtt4GUejT68lG5DL3+AkWFsyB5sJLcVs/Ucxk5vNjeg0qmQ/Js4jPJTzeqpaDB/6ZVg==", "signatures": [{"sig": "MEQCIGifsba4Wyn/DlYIi9KhUa7fkXfUCwJJbuCokq2OWzLhAiAQrzpIiJ4dVVEhAOqj/38L1XSXsEWd1ePswqG8sHIrBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "14e56eb68c8f1a92c43d59f5014ec29dc20f2ae1", "gitHead": "8fef3fe3b80636bc885bbfc9fa0012a4a2956352", "scripts": {"dev": "npm run build && npm test", "test": "mocha --ui tdd", "build": "node generate-index.js", "esprima-compare": "node esprima-compare ./index.js everything.js/es5.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lydell/js-tokens.git", "type": "git"}, "_npmVersion": "3.7.3", "description": "A regex that tokenizes JavaScript.", "directories": {}, "_nodeVersion": "5.9.0", "devDependencies": {"mocha": "^2.2.5", "esprima": "^2.3.0", "coffee-script": "~1.10.0", "everything.js": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/js-tokens-1.0.3.tgz_1459075945273_0.8152586803771555", "host": "packages-16-east.internal.npmjs.com"}}, "2.0.0": {"name": "js-tokens", "version": "2.0.0", "keywords": ["JavaScript", "js", "token", "tokenize", "regex"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens#readme", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "79903f5563ee778cc1162e6dcf1a0027c97f9cb5", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-2.0.0.tgz", "integrity": "sha512-kk4RjoztHFeAlCNPdTr/uF04oN5z53L18LJfADBQ7YHkO3iR2rP2ZwOH0n33r2FRw0psRmjTDEkOWPE//zmujg==", "signatures": [{"sig": "MEQCIE/gLO5O3Cy6p7dklzPR34IE29pJU3r8a+bsLPRmy439AiACB7iNSuhVb+uT0/xJz7Je1FN4Tyr/SyNdHbeo8IZnNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "79903f5563ee778cc1162e6dcf1a0027c97f9cb5", "gitHead": "23fcbe4639fb4baee5dc53616958cc04c8b94026", "scripts": {"dev": "npm run build && npm test", "test": "mocha --ui tdd", "build": "node generate-index.js", "esprima-compare": "node esprima-compare ./index.js everything.js/es5.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lydell/js-tokens.git", "type": "git"}, "_npmVersion": "3.8.6", "description": "A regex that tokenizes JavaScript.", "directories": {}, "_nodeVersion": "5.11.1", "devDependencies": {"mocha": "^2.5.3", "esprima": "^2.7.2", "coffee-script": "~1.10.0", "everything.js": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/js-tokens-2.0.0.tgz_1466321890449_0.1510669116396457", "host": "packages-16-east.internal.npmjs.com"}}, "3.0.0": {"name": "js-tokens", "version": "3.0.0", "keywords": ["JavaScript", "js", "token", "tokenize", "regex"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@3.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens#readme", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "a2f2a969caae142fb3cd56228358c89366957bd1", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-3.0.0.tgz", "integrity": "sha512-poXEQHPMmTrYZuJgNRll2sbc3kJsSU1m/g1Q93IE6txNj3p6xOOOmdj1G/zCVGawYSPzTkSoWGg1otqbeqKJeg==", "signatures": [{"sig": "MEUCIQDLCwSRFeNGEg61G9ji0uZNH4Xq3rHygDbkSrHR14geswIgCpIibyjOH/z55Tm9Ensyi6/ByR+aAvq2oVtccFk5h/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "a2f2a969caae142fb3cd56228358c89366957bd1", "gitHead": "7e3ca86c8f2260b442e477073cceb9c349fc9b5c", "scripts": {"dev": "npm run build && npm test", "test": "mocha --ui tdd", "build": "node generate-index.js", "esprima-compare": "node esprima-compare ./index.js everything.js/es5.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lydell/js-tokens.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "A regex that tokenizes JavaScript.", "directories": {}, "_nodeVersion": "7.2.0", "devDependencies": {"mocha": "^3.2.0", "esprima": "^3.1.3", "coffee-script": "~1.12.2", "everything.js": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/js-tokens-3.0.0.tgz_1484167093471_0.018422157736495137", "host": "packages-12-west.internal.npmjs.com"}}, "3.0.1": {"name": "js-tokens", "version": "3.0.1", "keywords": ["JavaScript", "js", "token", "tokenize", "regex"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@3.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens#readme", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "08e9f132484a2c45a30907e9dc4d5567b7f114d7", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-3.0.1.tgz", "integrity": "sha512-uGx5mrUJTDuSk2T4NendihsPTPR4Pgu06OYD5bEvFSXX4MZfGSy7tL6nlYWyJUAqQYo/3xkKLyIQzIqDx4UCDg==", "signatures": [{"sig": "MEYCIQDqXcj1WS060XL5lQE/lnMJ7pyeiMETm8WLPD/sdsR5fQIhAM1K2i/JVp4bhqF0j4DUt3F6ClJPKdjmhTL8R+Oqs/Fo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "08e9f132484a2c45a30907e9dc4d5567b7f114d7", "gitHead": "54549dd979142c78cf629b51f9f06e8133c529f9", "scripts": {"dev": "npm run build && npm test", "test": "mocha --ui tdd", "build": "node generate-index.js", "esprima-compare": "node esprima-compare ./index.js everything.js/es5.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lydell/js-tokens.git", "type": "git"}, "_npmVersion": "3.10.9", "description": "A regex that tokenizes JavaScript.", "directories": {}, "_nodeVersion": "7.2.0", "devDependencies": {"mocha": "^3.2.0", "esprima": "^3.1.3", "coffee-script": "~1.12.2", "everything.js": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/js-tokens-3.0.1.tgz_1485800902865_0.11822547880001366", "host": "packages-18-east.internal.npmjs.com"}}, "3.0.2": {"name": "js-tokens", "version": "3.0.2", "keywords": ["JavaScript", "js", "token", "tokenize", "regex"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@3.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens#readme", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "9866df395102130e38f7f996bceb65443209c25b", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-3.0.2.tgz", "integrity": "sha512-RjTcuD4xjtthQkaWH7dFlH85L+QaVtSoOyGdZ3g6HFhS9dFNDfLyqgm2NFe2X6cQpeFmt0452FJjFG5UameExg==", "signatures": [{"sig": "MEYCIQC4fADkRX9utX23aQgDtmj9jkK5rAsIjuv1ZLY3iWXPTwIhALBzax/YwtzAiVTbJMivERsRpQwawLYTorp3Pil0/S7A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "9866df395102130e38f7f996bceb65443209c25b", "gitHead": "8315904c840b14d28de1b0a4968194555f61bea3", "scripts": {"dev": "npm run build && npm test", "test": "mocha --ui tdd", "build": "node generate-index.js", "esprima-compare": "node esprima-compare ./index.js everything.js/es5.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lydell/js-tokens.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "A regex that tokenizes JavaScript.", "directories": {}, "_nodeVersion": "7.10.0", "devDependencies": {"mocha": "^3.4.2", "esprima": "^4.0.0", "coffee-script": "~1.12.6", "everything.js": "^1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/js-tokens-3.0.2.tgz_1498683256536_0.7391157897654921", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "js-tokens", "version": "4.0.0", "keywords": ["JavaScript", "js", "token", "tokenize", "regex"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@4.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens#readme", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "19203fb59991df98e3a287050d4647cdeaf32499", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==", "signatures": [{"sig": "MEUCIHTFYsC9RMPKFuD2Ff2bRCSWNb36hIt4dZfUv4vusrrOAiEAp3qTBJDVdCmqIySFXqJpqfON89dldxEoM1XZQXYkKkg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js"], "gitHead": "0eb6e9daee32160ab0fca979b6dc91a1991b720c", "scripts": {"dev": "npm run build && npm test", "test": "mocha --ui tdd", "build": "node generate-index.js", "esprima-compare": "node esprima-compare ./index.js everything.js/es5.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lydell/js-tokens.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A regex that tokenizes JavaScript.", "directories": {}, "_nodeVersion": "8.9.4", "devDependencies": {"mocha": "5.0.0", "esprima": "4.0.0", "coffeescript": "2.1.1", "everything.js": "1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/js-tokens-4.0.0.tgz_1517140737182_0.3507722308859229", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "js-tokens", "version": "5.0.0", "keywords": ["JavaScript", "js", "token", "tokenize", "regex"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@5.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens#readme", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "ca264bd7bd855edcba8e2737b379570240e43a00", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-RLaS2kBGnYOxeo8MGAoclloj/OtiS0As5mCWNXT97d/nGi62GelcqwxjLriEF2ERwLafVcZnWKqD5l6TaWk0Og==", "signatures": [{"sig": "MEYCIQCe6+rHT6L9GyWFoqAVbA2v0SNy4E5x4E08O1PhV+1KjwIhAJ7fEeKnhY31Y1pTq2P744EjaKIi6VvMfssLKCqiUKfq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15244, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdBQahCRA9TVsSAnZWagAAo5IQAI8DCTJS6qNc94bqKe/c\nWN7/CSFMgxztsVghqKOCOJzUV8jgjcxxf2lDRmWJj2cTq+8AZDPuI4uMqqAr\nErEDGRyR86jqulj/MS8yVwrDC/FqMW9NpeTlvnyAe+yHZKmNTypCxzXU5i08\nghr0OSawzgqRllJz6Z5ZVwhJLatJIzxNCEhJDEkdafUKx+9/PrVX7Kn5uMY9\nEtaid0xl4aBOFXITfzEQnP1CRe3zgdUwfIlB4tNFxzYMHqPTiGJBaKHmFODU\nbiUKS7GZlXwiLBFSU/mY0X0n1DBvrFQB8kpKXaEkn6RfXOKGWGC3OrAHA02D\nIh5jhxzISfKoKab2owYxZ28252sItoV4i0iqWpZThN+ONeGosYLG411UfvFn\nLcf4MZG7Rc6LkyFy2PQiyTEcF+brCRbYQIp9aZtesXmlQZrwzNuXhwiiOe/z\n83l9oYdgHnjYhOk1OQy+CnE7dng4C/d74kC0vGam9ZkIk6jEVSfq6cxQKhTH\n3CaBIg7T5yUXQuF13QmME0XpCdEh9vzeuOg3mE6gPYm9IAcJNXSr5Y7ievhV\nZ1XLjJtQ2iPwACZK2iXOvIOXHt6NfC5Ud0QP4X4VFwfNSGcQICOHB6QtaPGt\nn+t5KQIC86c+8u6yBRBSGSvq+C9OUt711E2I85+ZkTJ85HUZDhjMea9iI9Rn\n62eu\r\n=lNZp\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "8368242deeb6c5b57294769a2e2497b809b58c3a", "scripts": {"dev": "npm run build && npm test", "test": "mocha --ui tdd", "build": "node generate-index.js", "esprima-compare": "node esprima-compare ./index.js everything.js/es5.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lydell/js-tokens.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "A regex that tokenizes JavaScript.", "directories": {}, "_nodeVersion": "10.16.0", "_hasShrinkwrap": false, "devDependencies": {"mocha": "6.1.4", "esprima": "4.0.1", "coffeescript": "2.4.1", "everything.js": "1.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/js-tokens_5.0.0_1560610465352_0.9681630780789874", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "js-tokens", "version": "6.0.0", "keywords": ["JavaScript", "js", "ECMAScript", "es", "token", "tokens", "tokenize", "tokenizer", "regex", "regexp"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@6.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens#readme", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "3c29d3a851d4bd92f86415e65997b4b46b3665cc", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-6.0.0.tgz", "fileCount": 5, "integrity": "sha512-QJUTLeNZNFl/w4MmhHztb1I1UzV42KV/L/zNwNVGlCNc+zl8g01Nve0hPDGU+j2N7ctFZdLot49lUSi78L7WGg==", "signatures": [{"sig": "MEQCIFmCUAfRU51Jp9NpubuLK2XjBxLgi+tkH7jIcQ8wagM1AiBW05FRPuYdtySKGkZbZTgnbAIHqZSHEmbcesYoQhpJYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15524, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJelJbVCRA9TVsSAnZWagAA5VQP/12XL9HznRSp3u/uWjl6\noBpvYZrU9Uf63beXbYY19RPWtOgfxAbIqD3UdYImfGXkiOK2yINTRQS+eIdN\n03j4Sl9LUiNrcK3aykni2XJ2tLOOc6hh2tG+DMsCfGDbOZnYhnFCbc0+s3V6\nYJgi7KOLE2gkcpoQOdBAK0UQr0t0NgiXtf4ocQIWa2yOFF7HjgHqNMLrargC\nK2nv6jgE4R9AoAbZHF99j3/I4He5nbygR3an2/LloLFqqFR77PYaxKGwyaDQ\nJIPGj+liipB7ZXLm5sBpMZVY8+1FJoupP8UTLQvgRzw+qRQiwxqzY8UyhrJr\ngu0DvponBttKSsW+VuyawIHUi+WgNFDVcXURl48dhE3kiSUf4IZv6TkMMf1+\nK68QohEHbHH3Z9PnHd1O9B51ZKvPqggKPXo3dvj7qcweS7dAqBi1bNq/xr+j\nO7OjfHuc1KNCRcpAczm78gn7Mt922gd4LTSZuLlEGR4+kCRQMjJELY2SDJPk\n8X8izkS/k0Bl9Lg0NMxi3VYhq/Lpjx8HgyLnvQKbLJYvNkwN5vG7dEuJr2nr\n7nvMW0xMas6RBLvqZ9wPeTrrro2Sjh3AoF4yixNEjHmXAixbp5fBxGNOyxWo\nahsZPL6qFnl5BPi2pTfzsk3d2bJSU060ctfACOomf504h0vqDtVxPls6Wwu7\nSlui\r\n=glyj\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "exports": "./index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lydell/js-tokens.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Tiny JavaScript tokenizer.", "directories": {}, "_nodeVersion": "13.12.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/js-tokens_6.0.0_1586796244674_0.8455417748962879", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "js-tokens", "version": "7.0.0", "keywords": ["JavaScript", "js", "ECMAScript", "es", "token", "tokens", "tokenize", "tokenizer", "regex", "regexp"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@7.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens#readme", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "5b847c7f3e16c89dbc90fac34a8be947907a5a72", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-7.0.0.tgz", "fileCount": 5, "integrity": "sha512-mstB8Fdp1t7raP2i4IsewoD8l572hutCSwqE9OU9WjKsYxlSdkyX1bgzU60dXrVvhCoZtrcz0DQlW0/T7y57uA==", "signatures": [{"sig": "MEYCIQCoU/iqj50POtnLeJj0XHPGqDWvQTOQWYc8vFDGeUbzgAIhAJ1i+f/fAKroeHb6iGtClc5/Z5PfCby/Zk9nTg3x9Oou", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJghT5oCRA9TVsSAnZWagAAmJUP/ii5ewGtlf9RUHogcyQO\nNiml8UeG7a/RduUZQ7N1hXwWcXAMTcdMipxfr+76mYm491anJTds1aco0Eda\no1kWWpX7lwR+aGduNDWOsbx/Jskf0lLleNd0UOqUvjwcATuoqyKx/09BktPV\ni1gyE0MFKXBWryP0P5NN++kZ+f7BXAWCGg+QTKmI2AoPmtPgZBWJIiDRYiLI\nx1Z7lFRUHGW1flfpKHuzi+x7fc3cvsmpTrkEZhEP7cLezCmJSK0eyn0gfd3R\njh17/2CDIgceuwNiXNc23ZNDVovesNa0Q+F52bAyfT3lg8+NiB7wV8IO+q/t\nxLPj48Mx2L5LRZ8ynCfp2fD44W3MZ+5BdBUu+FiKlHHCZngoEo3uJ0P+CvEG\nhke6SM2FV0yEb1sIPKaCkPyzfAnDA2n344tKVNrKh0OFRTxcCccRL+vCb6QG\n8NbCWGrrOszLzI/7pS3gBN2h752NPBUMJcyoi4YPKp4Lb6uJRWIqDTdocxrQ\np2LZ82AFdejnD/U+awioldCbGsN/m8I0uFCUMMKRq//R5k/4Eqzau+FS7L3v\n5+P+Y3buKNUwFxTfHD2orN1sO6sGWYuv6yNMGDClx28XNyQ7wnoW2X9qI0eK\nfJR29QRYDYv5xcQQmbOQriRC+oGiLFaFwAMe1zMPNw59lnp0r1DP5Kayps2+\n/N7H\r\n=wdTO\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "exports": "./index.js", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lydell/js-tokens.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Tiny JavaScript tokenizer.", "directories": {}, "_nodeVersion": "12.19.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/js-tokens_7.0.0_1619344999944_0.5646989769120967", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "js-tokens", "version": "8.0.0", "keywords": ["JavaScript", "js", "ECMAScript", "es", "token", "tokens", "tokenize", "tokenizer", "regex", "regexp"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@8.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens#readme", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "5dbe2cdfa9afc93251d3a77bf18c3ad6fa8a4de4", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-8.0.0.tgz", "fileCount": 5, "integrity": "sha512-PC7MzqInq9OqKyTXfIvQNcjMkODJYC8A17kAaQgeW79yfhqTWSOfjHYQ2mDDcwJ96Iibtwkfh0C7R/OvqPlgVA==", "signatures": [{"sig": "MEYCIQD/1QKdUzQVkN8PQO3hwxDeK19qFpUNw4op7HfHXRyHegIhAMSjm2icEHOmaYxVBCAcC9pnqk+uMS5QuW1qBkewh5BE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15690, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigM0JACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQXQ//ZIG1CaYhbyY4PFkLDN6dc7o8ov2PuZmu9vLd3POcBFsUaRgk\r\nf+2WlUaUK3KxbzRZoUWEe5KHp6fNMfObJ8Z/nV2Xe42dCueVuL3cZmZOCQcX\r\nMnVh6mtW9/IfwW1AQLhKmF/XcWCLparRPSmRW9ppklujbB+jnWhw8MaOLyam\r\nPKxZ9y2MXmpT0LJPTYppVy2nPiHLFirs2YkT/DHdWsahUl5Qpvx2e+16H6rt\r\n0SAauXUGPMncqjReNpiVkD+tJdTIQyDqpqOQBUYWozeUxtmbrRPXy4b/RtIR\r\n0ikOeFv0oLsF+3A7FQr9NeUC27mObOY8kNIAPJFfgRVU67ZlgmQUyEw3hl9z\r\nPLTS5bMRaLnkVpcWGyQ6Mt8IWbCeGSk7FQfnWhxCbncHxNVaaQsh3mr1k7u5\r\nA9PlHpzAMJqlqVU1gh+Y6JWwlwxlVbGzi2Y8iG8DSGvvGKJjVshTGAt6Hgs6\r\nqCICAkhw+JbrV5U60U/X9Y564iJPhwBhonj44uxYCB8dTc7TZsqc5WTPMhkN\r\nwdc9bq48lL4iLriRumrZc/4FhMAJJE1w6XU87k+h7PSu+Fmeu5tHDYl3EdYX\r\njGjllwonzS37PG3nkra8GQCjl3+KgFT87J5cfWmHwvSejjaUPv8gvw6GV17x\r\nK6cUiI6jRV/AGgINmwVQtZA/Eh0KG9IN0Xs=\r\n=4j6E\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "types": "./index.d.ts", "exports": "./index.js", "gitHead": "16912368ce93332dc7417b95c38b5da4779890b6", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lydell/js-tokens.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Tiny JavaScript tokenizer.", "directories": {}, "_nodeVersion": "16.14.2", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/js-tokens_8.0.0_1652608265243_0.007141159824718857", "host": "s3://npm-registry-packages"}}, "8.0.1": {"name": "js-tokens", "version": "8.0.1", "keywords": ["JavaScript", "js", "ECMAScript", "es", "token", "tokens", "tokenize", "tokenizer", "regex", "regexp"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@8.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens#readme", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "f068fde9bd2f9f4a24ad78f3b4fa787216b433e3", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-8.0.1.tgz", "fileCount": 5, "integrity": "sha512-3<PERSON>rZT6tuMm1ZWWn9mLXh7XMfi2YtiLNPALCVxBCiUVq0LD1OQMxV/AdS/s7rLJU5o9i/jBZw/N4vXXL5dm29A==", "signatures": [{"sig": "MEYCIQCa7JARIMZt7gujObG/Wph+XUPnXAhNc0goLFu9KsCQwAIhAMZxrb4RcZD6qlL3AyrRBiB+Mr3OuQZcksogalSb4mqT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15712, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxb5NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrrPBAAlLryQ+yl5rBiZLKwAeG8cyv/Cok7iDIu1uua2YtnUBysBq5z\r\nAUZzhst5O8rl6MxVJxI2hC5cKEU9pHHk4M3ilrGUlw6M1JRoD71xOq1+wcMm\r\nkGgwroQgXFnTTdLWlJN20tR1efNQaJY4NvOHcsdnPxkkP8H0RX/SdDbnCTKu\r\nZwuBeq2X2KnPB6t9ubgby0ZZNp45CJb6uo8q1vYcxREVTAeX4yT9KvsmRRNW\r\nVVK8qIwFAndu5pQaMKf0dobO5/PwY5Ftkgekpi4OkmcYTuUu4pY/iZ0XohwH\r\nIp+ef6sbpdRj3xCcinixqnBJbIvcNMz6ChqAUE6MOXYf412yaDa7hfbc3wXe\r\nDzW9qUgHuWzKOrI+S75dNZIVEJCtXcHJrBhscxVCFT3P0gStsmQ1UK7tx0UV\r\nk/RE+ef56KWtTCqrVnI0z0K6hovBkqSLeb7ebRvkFl+kV2TEfPlZiOcAnm4+\r\nPzkWIlfW5mRUA74GePQC87Vge8XdZH0POkqqMJDEaTzRwVqWfr1FgINEWkLB\r\nHhSqblv6/ivxYJUsBjCWgFxi35Jt0ZY4RP0Zn8nncEI9WSG2saMvbMmO0P77\r\n43xRpr4RfE41YYBeeGNVEOwo+FRrbzlTfLpnfCDRPLcyd+7aozzzOmC6vlVv\r\nt2e4SySSCRsli1D3wdBM2xxYiOGNaIUJ6ks=\r\n=kPUO\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "commonjs", "types": "./index.d.ts", "exports": "./index.js", "gitHead": "ae542161b0595b08eabee259eb4b9de92233df96", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lydell/js-tokens.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Tiny JavaScript tokenizer.", "directories": {}, "_nodeVersion": "18.13.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/js-tokens_8.0.1_1673903692852_0.6691159649398726", "host": "s3://npm-registry-packages"}}, "8.0.2": {"name": "js-tokens", "version": "8.0.2", "keywords": ["JavaScript", "js", "ECMAScript", "es", "token", "tokens", "tokenize", "tokenizer", "regex", "regexp"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@8.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens#readme", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "86a19e09d81c64f1f4a3af489b8c1b67d0c7c588", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-8.0.2.tgz", "fileCount": 5, "integrity": "sha512-Olnt+V7xYdvGze9YTbGFZIfQXuGV4R3nQwwl8BrtgaPE/wq8UFpUHWuTNc05saowhSr1ZO6tx+V6RjE9D5YQog==", "signatures": [{"sig": "MEQCIG30qe2sELo8YqbtAiA5tHTm47jdPWapUgg9+I86x6CKAiBmsUx5240FfJsuukFNPiZGNMsFzb/owQIp0xN5gAvpNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15783}, "type": "commonjs", "types": "./index.d.ts", "exports": "./index.js", "gitHead": "93a64af449586241b0ba5ec121511291243e4133", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lydell/js-tokens.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "Tiny JavaScript tokenizer.", "directories": {}, "_nodeVersion": "20.5.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/js-tokens_8.0.2_1694264603392_0.3976669609582535", "host": "s3://npm-registry-packages"}}, "8.0.3": {"name": "js-tokens", "version": "8.0.3", "keywords": ["JavaScript", "js", "ECMAScript", "es", "token", "tokens", "tokenize", "tokenizer", "regex", "regexp"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@8.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens#readme", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "1c407ec905643603b38b6be6977300406ec48775", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-8.0.3.tgz", "fileCount": 5, "integrity": "sha512-UfJMcSJc+SEXEl9lH/VLHSZbThQyLpw1vLO1Lb+j4RWDvG3N2f7yj3PVQA3cmkTBNldJ9eFnM+xEXxHIXrYiJw==", "signatures": [{"sig": "MEQCIE6ptUxzI8ESztScoKO6DyIKCCLckiMYAWsx+ybXu1T2AiA3j8maBK6scmFxZYCMYmCN3AlJ4I4Etimyx89+QXcaYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15834}, "type": "commonjs", "types": "./index.d.ts", "exports": "./index.js", "gitHead": "8125aae4df8ab7eb262b29eebf0cdc65d162bf1a", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lydell/js-tokens.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Tiny JavaScript tokenizer.", "directories": {}, "_nodeVersion": "21.6.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/js-tokens_8.0.3_1706955145948_0.06043368363691837", "host": "s3://npm-registry-packages"}}, "9.0.0": {"name": "js-tokens", "version": "9.0.0", "keywords": ["JavaScript", "js", "ECMAScript", "es", "token", "tokens", "tokenize", "tokenizer", "regex", "regexp"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "js-tokens@9.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lydell/js-tokens#readme", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "dist": {"shasum": "0f893996d6f3ed46df7f0a3b12a03f5fd84223c1", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-9.0.0.tgz", "fileCount": 5, "integrity": "sha512-WriZw1luRMlmV3LGJaR6QOJjWwgLUTf89OwT2lUOyjX2dJGBwgmIkbcz+7WFZjrZM635JOIR517++e/67CP9dQ==", "signatures": [{"sig": "MEQCIG162ehE3//1u/AygtFEVWxP+I2OEGoNiYCZBT5uYJsSAiAEDbBRTArqTSqTKSZCVkSkB+//NkeWdQrVEz+ydzyITQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16067}, "type": "commonjs", "types": "./index.d.ts", "exports": "./index.js", "gitHead": "43b47c87b1a1077b0dea733d9798fab559c0c8e3", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lydell/js-tokens.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Tiny JavaScript tokenizer.", "directories": {}, "_nodeVersion": "21.6.1", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/js-tokens_9.0.0_1707433147242_0.013600752887567014", "host": "s3://npm-registry-packages"}}, "9.0.1": {"name": "js-tokens", "version": "9.0.1", "author": {"name": "<PERSON>"}, "license": "MIT", "description": "Tiny JavaScript tokenizer.", "repository": {"type": "git", "url": "git+https://github.com/lydell/js-tokens.git"}, "type": "commonjs", "exports": "./index.js", "keywords": ["JavaScript", "js", "ECMAScript", "es", "token", "tokens", "tokenize", "tokenizer", "regex", "regexp"], "_id": "js-tokens@9.0.1", "gitHead": "1470e1efafcbca46709c73ba98811a4e5bcc1e86", "types": "./index.d.ts", "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "homepage": "https://github.com/lydell/js-tokens#readme", "_nodeVersion": "22.8.0", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-mxa9E9ITFOt0ban3j6L5MpjwegGz6lBQmM1IJkWeBZGcMxto50+eWdjC/52xDbS2vy0k7vIMK0Fe2wfL9OQSpQ==", "shasum": "2ec43964658435296f6761b34e10671c2d9527f4", "tarball": "https://registry.npmjs.org/js-tokens/-/js-tokens-9.0.1.tgz", "fileCount": 5, "unpackedSize": 16071, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDiutdhKJOBegogqXY0gJHJhbWWJD2VivlwmXFySIbAuAIgQucbgpQChfOgrAheSfyZHTCAZvKjvKIWb9ZPVKpZNS0="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/js-tokens_9.0.1_1732296070291_0.6281904360708463"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-03-08T22:30:50.672Z", "modified": "2024-11-22T17:21:10.707Z", "0.1.0": "2014-03-08T22:30:50.672Z", "0.2.0": "2014-06-19T20:20:22.938Z", "0.3.0": "2014-12-19T17:50:28.104Z", "0.3.1": "2015-01-06T21:17:50.696Z", "0.4.0": "2015-02-21T19:00:41.297Z", "0.4.1": "2015-02-21T20:20:20.154Z", "1.0.0": "2015-02-26T09:34:56.843Z", "1.0.1": "2015-06-20T06:38:03.183Z", "1.0.2": "2015-10-18T15:00:47.347Z", "1.0.3": "2016-03-27T10:52:26.157Z", "2.0.0": "2016-06-19T07:38:11.688Z", "3.0.0": "2017-01-11T20:38:15.672Z", "3.0.1": "2017-01-30T18:28:23.511Z", "3.0.2": "2017-06-28T20:54:17.623Z", "4.0.0": "2018-01-28T11:58:58.170Z", "5.0.0": "2019-06-15T14:54:25.462Z", "6.0.0": "2020-04-13T16:44:04.779Z", "7.0.0": "2021-04-25T10:03:20.082Z", "8.0.0": "2022-05-15T09:51:05.420Z", "8.0.1": "2023-01-16T21:14:53.000Z", "8.0.2": "2023-09-09T13:03:23.579Z", "8.0.3": "2024-02-03T10:12:26.165Z", "9.0.0": "2024-02-08T22:59:07.471Z", "9.0.1": "2024-11-22T17:21:10.496Z"}, "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/lydell/js-tokens#readme", "keywords": ["JavaScript", "js", "ECMAScript", "es", "token", "tokens", "tokenize", "tokenizer", "regex", "regexp"], "repository": {"type": "git", "url": "git+https://github.com/lydell/js-tokens.git"}, "description": "Tiny JavaScript tokenizer.", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# js-tokens\n\nThe tiny, regex powered, lenient, _almost_ spec-compliant JavaScript tokenizer that never fails.\n\n```js\nconst jsTokens = require(\"js-tokens\");\n\nconst jsString = 'JSON.stringify({k:3.14**2}, null /*replacer*/, \"\\\\t\")';\n\nArray.from(jsTokens(jsString), (token) => token.value).join(\"|\");\n// JSON|.|stringify|(|{|k|:|3.14|**|2|}|,| |null| |/*replacer*/|,| |\"\\t\"|)\n```\n\n**[➡️ Full readme](https://github.com/lydell/js-tokens/)**", "readmeFilename": "README.md", "users": {"nraibaud": true, "geofftech": true, "qingleili": true, "shuoshubao": true, "flumpus-dev": true, "roboterhund87": true}}