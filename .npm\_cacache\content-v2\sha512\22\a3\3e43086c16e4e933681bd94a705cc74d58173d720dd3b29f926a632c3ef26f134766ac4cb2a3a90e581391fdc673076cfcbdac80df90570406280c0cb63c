{"_id": "esprima", "_rev": "179-6ae5e532faf4275871c9b1f363520b77", "name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "dist-tags": {"latest": "4.0.1", "1.1.0": "1.1.0"}, "versions": {"0.7.0": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis tool", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js"}, "version": "0.7.0", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/ariya/esprima.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "_id": "esprima@0.7.0", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.4.13-pre", "_defaultsLoaded": true, "dist": {"shasum": "73e71270859ab68eb3f806ea5244536670ba7e72", "tarball": "https://registry.npmjs.org/esprima/-/esprima-0.7.0.tgz", "integrity": "sha512-gk+jPNIgfS8Ec08JqtTiZ2ga/m7ILdLF4mmMfWF2bK9wTvOUnSlu2a/LJ+GNFA62Sr5qooRwf04iBClF8K/TYQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGs4iLOZnUCMRyEQ9kMc98vzgzWvyrVpWtBtEAaay5AYAiEAtqHoaRGwOtnm0rBTnr7+CMf8pevjZxEv478tgW/PKrI="}]}, "directories": {}}, "0.8.0": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis tool", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js"}, "version": "0.8.0", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/ariya/esprima.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "_id": "esprima@0.8.0", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.4.13-pre", "_defaultsLoaded": true, "dist": {"shasum": "c3a4d9eb2bab14f050b443296f9acbc83952c9d6", "tarball": "https://registry.npmjs.org/esprima/-/esprima-0.8.0.tgz", "integrity": "sha512-D2I5T/a6TiAfHafGir00YhdKGObI5OtqrycEzqMW9JGUBxx4wImMU/aCCs1wnJ1kR+wPiXjK0Yj7eC6GzenPeA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH3N9ZXDg/r0FQZD3rVvC3SMqHO8ExIf68qDyAUVkZzlAiEAxWjt7gSgrLnGufWuk79q5tBzL9yrKW8O6nZQdKeEw7g="}]}, "directories": {}}, "0.8.1": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js"}, "version": "0.8.1", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/ariya/esprima.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "_id": "esprima@0.8.1", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.4.13-pre", "_defaultsLoaded": true, "dist": {"shasum": "248b67200011337474e42ddd2afe2c7c7a62756d", "tarball": "https://registry.npmjs.org/esprima/-/esprima-0.8.1.tgz", "integrity": "sha512-+1TxCUXKBc/yiWpQvtNO/OhAGoIRvWPp/O7MwU9GRRtrvYfnL7OLBY8wEPhozDOOH3QcVKNT1Nj/u6psLavs+A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGNLhkli/gsMW4mqX/9vLOa6cMT/vNXAdYITbfJx0b/wAiBAg20CHydit9QwQ25bANr3Owb8zsQNsnlY491IMkOx+g=="}]}, "directories": {}}, "0.8.2": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js"}, "version": "0.8.2", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/ariya/esprima.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "_id": "esprima@0.8.2", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.4.13-pre", "_defaultsLoaded": true, "dist": {"shasum": "069a33aaacbb45a60b8071b4cfc6fb6929ebcb50", "tarball": "https://registry.npmjs.org/esprima/-/esprima-0.8.2.tgz", "integrity": "sha512-F8lgMWPHjImHCvvqy2IochFAuKT1tWDui481a/PrfoX6WslOSP7BYSK0cqL4Sf5PA5L7cho4x7PxZz2Er1wCjg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJJCwHw2VNA4PkcDPcPqdMoGWzjz1RhYFp/6lemHS9uQIhAKjCw1MNCTUnsbPHmFZq+a72fjZXaGATY5KyoSB7csUM"}]}, "directories": {}}, "0.9.0": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js"}, "version": "0.9.0", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/ariya/esprima.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "_id": "esprima@0.9.0", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.4.13-pre", "_defaultsLoaded": true, "dist": {"shasum": "9b35d752fb826a53f38661bfc1106723ba583ddf", "tarball": "https://registry.npmjs.org/esprima/-/esprima-0.9.0.tgz", "integrity": "sha512-mdv+XVY99kCjBAFy6LuVo68tLDFrR+ZkR5LdFbna8icjhv3iMQDovLhCyNdBTOJJaRCJKukCOxsPShS8AOVvmw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDdBG6r/1C1SBvsWfKia6ylHMh0nIA5C83yFvntp0xQbAIhALy5/igKydh/Eu0hsUVEovdkZsKRnjjTW8XRI3tRbPHt"}]}, "directories": {}}, "0.9.1": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js"}, "version": "0.9.1", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/ariya/esprima.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "_id": "esprima@0.9.1", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.4.13-pre", "_defaultsLoaded": true, "dist": {"shasum": "7f728fda0ee40873d00511eb9267566e6ccc8ba0", "tarball": "https://registry.npmjs.org/esprima/-/esprima-0.9.1.tgz", "integrity": "sha512-xgkwLHRJqSYXB5UpYYgOY1/YK+rzzdl1ce67DqfJSKm1ULcCrR9w+RVt/x312hQHGdA+sZaRAgXRBJtFjcK++w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDDQZv/tdrIZ1ClxPq7O/OrKa4WwI6YsoOUJyOTdIDEqgIgRRm15YkV1hlLxoiDlwwzifTP1xFH3Fh1h641wD/+H3w="}]}, "directories": {}}, "0.9.2": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js"}, "version": "0.9.2", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/ariya/esprima.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "_id": "esprima@0.9.2", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.4.13-pre", "_defaultsLoaded": true, "dist": {"shasum": "8c6829495376d53580b97b3b6ba88af58e919d17", "tarball": "https://registry.npmjs.org/esprima/-/esprima-0.9.2.tgz", "integrity": "sha512-5mYp6YZp1AaV2E+za1aQawSwFDoxFgIy3gFhVOWgRI1/Dv7tGfSPMFRNOg2Iiuigc8x2uHfoXvuo6Vbih05WoQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHbX0D6w9s3NYLnWlOrMmzIrlAGTA9xkjDpMz509E8uEAiEAylGSR0IaZDW8GZVwHeFCU2qOcPaPiI/7t/ZCwthdH7w="}]}, "directories": {}}, "0.9.3": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js"}, "version": "0.9.3", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/ariya/esprima.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "_id": "esprima@0.9.3", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.4.13-pre", "_defaultsLoaded": true, "dist": {"shasum": "e8bba20b413e91a2175441303599a5123b146eb7", "tarball": "https://registry.npmjs.org/esprima/-/esprima-0.9.3.tgz", "integrity": "sha512-/w7BSrNY2vhdAX8Vvlr2uihk9M6QkUFiqcvZXFKktt6Ju79sdyoieHymddes0us33qiVZjZBdaj6AAodk49VJQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAh+C9ThOi9iLTnR0cQcbSF+zjMyAmfQcGmhZi9jwLMxAiEA+/sdX8uykdl5tcY5ifva7hUNJMf8k/KXzL/AhOc8M4Y="}]}, "directories": {}}, "0.9.4": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js"}, "version": "0.9.4", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/ariya/esprima.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "_id": "esprima@0.9.4", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.4.13-pre", "_defaultsLoaded": true, "dist": {"shasum": "49a0581c2de45aa17d44792540267bddc7574ee1", "tarball": "https://registry.npmjs.org/esprima/-/esprima-0.9.4.tgz", "integrity": "sha512-R82KZjGpQSqA/b7EzxAWLCUEXix4xnQ3mU+lDIISFO1zwnKXE2YjHbGwvOX19dYpNj+iGyeWJWMwhOQGEKeKoA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDt4fzzYPslXQWofpS6kNlTmghB59MMG3qpHomSBLESJgIgLKMJTL3LR/QuoI4DzdqokOxL6R03AVIKgceKMMlCpTE="}]}, "directories": {}}, "0.9.5": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js"}, "version": "0.9.5", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/ariya/esprima.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "_id": "esprima@0.9.5", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.4.13-pre", "_defaultsLoaded": true, "dist": {"shasum": "3403826505ab2496c9b3eb50131b4ff124972ea3", "tarball": "https://registry.npmjs.org/esprima/-/esprima-0.9.5.tgz", "integrity": "sha512-wIUDQ8uzwyphXWxIJmospjGwufBjrRiZCOvNNu5TOU8DIsgWiOw2ahJJbOKgaxjnNMNN+HGlVYZu07y8kL6i4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDcBDbZGNLqkWXKkoBDqgGnT4C89BsIoDxIUuRmJK6YKAIgWCkqF9pNimsLZvzNuR8u8QiQn582ybL+OEw0dwER8fg="}]}, "directories": {}}, "0.9.6": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js"}, "version": "0.9.6", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/ariya/esprima.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "_id": "esprima@0.9.6", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.4.13-pre", "_defaultsLoaded": true, "dist": {"shasum": "e4198843636cf1755781621890712b838cf055cf", "tarball": "https://registry.npmjs.org/esprima/-/esprima-0.9.6.tgz", "integrity": "sha512-5GGeYYHsriG+TBXQh/UcxDLHB6P8BX6qkL3ddL8aL49JYqZh1m/cz4XWK7bbJdf/WoQiBvn3xpyk6DVoAtxo9w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDO4nlnXORnlIvb90+NAA0XmSo2XgcCuH7ddEB8M/KW7QIhAMK+8EGgu9CVN0SB+0yx162/JCH6BR4s75RuzRCsjvR+"}]}, "directories": {}}, "0.9.7": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js"}, "version": "0.9.7", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/ariya/esprima.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/test.js", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}, "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "_id": "esprima@0.9.7", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.4.13-pre", "_defaultsLoaded": true, "dist": {"shasum": "7876246c98b3ce491981386805566beac4e9a45b", "tarball": "https://registry.npmjs.org/esprima/-/esprima-0.9.7.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON>ud9fsAHxyEyspjv6Er1beGH0eX9BHdL9mww7TobanOnV1/1dP5rva1FK8on1ca5861sfw7IowL5KKbXTiw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCVrcx/oHGB9yE7ItdjbhrXCX7vc+8/Vn4OaS36iQ6BHQIgVNZd7BX8gybHZpVk506AnQiX6iQD0t/FB84EUHTsESM="}]}, "directories": {}}, "0.9.8": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js"}, "version": "0.9.8", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/ariya/esprima.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}, "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "_id": "esprima@0.9.8", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.105", "_nodeVersion": "v0.4.13-pre", "_defaultsLoaded": true, "dist": {"shasum": "b800f30036c24335afde886dc5dc89eaba3fc136", "tarball": "https://registry.npmjs.org/esprima/-/esprima-0.9.8.tgz", "integrity": "sha512-2E+baqrFdAszMd1SfgRbUG1ndUAFQI6nzIKYz5f26bzoFRPqfEoTPXVvw7u9ZjpMysENE8nxyiiOLGp1tvojNQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC0ZVTLVFm7/Kf+qtkw7jMsJ43wDZlPSFFN+aoAB3i7jgIgYZyStjvwZcT/fiTmlj3yl3nyjtvPSHLn7t2ySWkwXx8="}]}, "directories": {}}, "0.9.9": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js"}, "version": "0.9.9", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git://github.com/ariya/esprima.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}, "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "_id": "esprima@0.9.9", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.4", "_nodeVersion": "v0.6.12", "_defaultsLoaded": true, "dist": {"shasum": "1b90925c975d632d7282939c3bb9c3a423c30490", "tarball": "https://registry.npmjs.org/esprima/-/esprima-0.9.9.tgz", "integrity": "sha512-uTFHqyoMus4csxVp8FSqPajg59VwNt0PshVERqiIjPed6L9IG0pYz/zbhZ2HFFvn8AKzduipZP6mFxr3dr18ag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDJcV0klxgi1S3aF6f7GJWV7vhZnTv5U/0ODKu287+W8QIhALRXftubhtVSf/OPQ7veGW5hwFPFCRFkaliP5htTGKY+"}]}, "directories": {}}, "1.0.0": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "1.0.0", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/ariya/esprima.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}, "_id": "esprima@1.0.0", "dist": {"shasum": "5f0571b94a87d5199b1f302a7e27eb185e466850", "tarball": "https://registry.npmjs.org/esprima/-/esprima-1.0.0.tgz", "integrity": "sha512-k344tuMul8eif80+WDaTCcS5g4xDSO6FRHmCkvlF6t/QSBYQDrvph2VqP23ry9y9oMVEJ/sdJEh8JXT0smlcQQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBoXmAoVEvDWUq1TtYipU/ApB4zr6uH5AoixB4i19dH5AiA76ApsNFGnvbLFSIY4Tu/Tl0wkRbh+YgAdtsA1jsZhEQ=="}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "directories": {}}, "1.0.1": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "1.0.1", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/ariya/esprima.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}, "_id": "esprima@1.0.1", "dist": {"shasum": "2c8a85447f8bfbe8d9f401da808809cb5397ad0a", "tarball": "https://registry.npmjs.org/esprima/-/esprima-1.0.1.tgz", "integrity": "sha512-+oUOkfRfh/rpFOBrn2Q23goMZGkKhXXDjCyPsk6SKc+XmL4qLScwUH9hMSFUE0DvhgLecugpMAacM61tCk9+jw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA8iV3yhaTiQ125YezlVV5jEJTMnsv37DX1V4b5XOpRdAiEApn40A7/RVHTsVaRPHUGBMCFEU+X0bWEJ4YZT+g8XxEs="}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "directories": {}}, "1.0.2": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "1.0.2", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/ariya/esprima.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}, "_id": "esprima@1.0.2", "dist": {"shasum": "8039bf9ceac4d9d2c15f623264fb292b5502ceaf", "tarball": "https://registry.npmjs.org/esprima/-/esprima-1.0.2.tgz", "integrity": "sha512-j2ZAN1Cm/rgsIEHgNa6eqvZjEtqFh8WEUdVswEpxj03AyzR1R6CHfunOpd9NOmLg0U18aAO2FunAjs49f5w0Yg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGvi2KVm+UHyJmWC0QDRj/hzfy9jYq+1tH0EDtUPvGoSAiEA/x3Y8bjAWh3MROu0/VzPLkihIdiRC/GsH82ndksmD58="}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "directories": {}}, "1.0.3": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "1.0.3", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/ariya/esprima.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "scripts": {"test": "node test/run.js", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}, "_id": "esprima@1.0.3", "dist": {"shasum": "7bdb544f95526d424808654d3b8fbe928650c0fe", "tarball": "https://registry.npmjs.org/esprima/-/esprima-1.0.3.tgz", "integrity": "sha512-Cc9SOu665lwATZT2tzVgeiZlqpnN6wKs2BvWrJ5nQSzGaivjL1MBYxyy951anmdsgMZNTssPXBRJq8W5vgRNcQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC9mS0oDMPypyKSjT/iwIncrscK2Qm1QH7gomQAYczdFQIgCIvsidFk90f9r3XNTxA8yu/tZkRv9E5DiQwapD+JBDE="}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "directories": {}}, "1.0.4": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "files": ["bin", "test/run.js", "test/runner.js", "test/test.js", "test/compat.js", "test/reflect.js", "esprima.js"], "version": "1.0.4", "engines": {"node": ">=0.4.0"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/ariya/esprima.git"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"test": "node test/run.js", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}, "_id": "esprima@1.0.4", "dist": {"shasum": "9f557e08fc3b4d26ece9dd34f8fbf476b62585ad", "tarball": "https://registry.npmjs.org/esprima/-/esprima-1.0.4.tgz", "integrity": "sha512-rp5dMKN8zEs9dfi9g0X1ClLmV//WRyk/R15mppFNICIFRG5P92VP7Z04p8pk++gABo9W2tY+kHyu6P1mEHgmTA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDUant61mRTBoHzKhuSk02c0duDCHtEK2ljN+dO3BvcXwIhALaeHR1fwGjNOrLhqwOuRiLV3vz623+BiymflbjZWbY6"}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "directories": {}}, "1.1.0": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "1.1.0", "files": ["bin", "test/run.js", "test/runner.js", "test/test.js", "test/compat.js", "test/reflect.js", "esprima.js"], "engines": {"node": ">=0.4.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/ariya/esprima.git"}, "bugs": {"url": "http://issues.esprima.org"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "devDependencies": {"jslint": "~0.1.9", "eslint": "~0.4.3", "jscs": "~1.2.4", "istanbul": "~0.1.27", "complexity-report": "~0.6.1", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "json-diff": "~0.3.1", "optimist": "~0.6.0"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"generate-regex": "node tools/generate-identifier-regex.js", "test": "npm run-script lint && node test/run.js && npm run-script coverage && npm run-script complexity", "lint": "npm run-script check-version && npm run-script eslint && npm run-script jscs && npm run-script jslint", "check-version": "node tools/check-version.js", "eslint": "node node_modules/eslint/bin/eslint.js esprima.js", "jscs": "node node_modules/.bin/jscs esprima.js", "jslint": "node node_modules/jslint/bin/jslint.js esprima.js", "coverage": "npm run-script analyze-coverage && npm run-script check-coverage", "analyze-coverage": "node node_modules/istanbul/lib/cli.js cover test/runner.js", "check-coverage": "node node_modules/istanbul/lib/cli.js check-coverage --statement -8 --branch -19 --function 100", "complexity": "npm run-script analyze-complexity && npm run-script check-complexity", "analyze-complexity": "node tools/list-complexity.js", "check-complexity": "node node_modules/complexity-report/src/cli.js --maxcc 17 --silent -l -w esprima.js", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}, "_id": "esprima@1.1.0", "dist": {"shasum": "3efe4810741491bb1c8f67df526d6b2e0dd57790", "tarball": "https://registry.npmjs.org/esprima/-/esprima-1.1.0.tgz", "integrity": "sha512-gGF+0Iz4e28iVuWg7PRd9yuiIxa8ju3Qaf2pyfm2UUMubNwGpqTfvvpfwPPdjpwODkxGvUOSSGHjdybqW6fV+A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICfx372x5bK5kRIIMpFwxOsp7yuohX6ti7m4YO5TEEdPAiAlhoyMGvDgdNhl3VhKSAGJ9mG6ayqoFmYWlVyAe9AznQ=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "directories": {}}, "1.1.1": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "1.1.1", "files": ["bin", "test/run.js", "test/runner.js", "test/test.js", "test/compat.js", "test/reflect.js", "esprima.js"], "engines": {"node": ">=0.4.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/ariya/esprima.git"}, "bugs": {"url": "http://issues.esprima.org"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "devDependencies": {"jslint": "~0.1.9", "eslint": "~0.4.3", "jscs": "~1.2.4", "istanbul": "~0.1.27", "complexity-report": "~0.6.1", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "json-diff": "~0.3.1", "optimist": "~0.6.0"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"generate-regex": "node tools/generate-identifier-regex.js", "test": "npm run-script lint && node test/run.js && npm run-script coverage && npm run-script complexity", "lint": "npm run-script check-version && npm run-script eslint && npm run-script jscs && npm run-script jslint", "check-version": "node tools/check-version.js", "eslint": "node node_modules/eslint/bin/eslint.js esprima.js", "jscs": "node node_modules/.bin/jscs esprima.js", "jslint": "node node_modules/jslint/bin/jslint.js esprima.js", "coverage": "npm run-script analyze-coverage && npm run-script check-coverage", "analyze-coverage": "node node_modules/istanbul/lib/cli.js cover test/runner.js", "check-coverage": "node node_modules/istanbul/lib/cli.js check-coverage --statement -8 --branch -19 --function 100", "complexity": "npm run-script analyze-complexity && npm run-script check-complexity", "analyze-complexity": "node tools/list-complexity.js", "check-complexity": "node node_modules/complexity-report/src/cli.js --maxcc 17 --silent -l -w esprima.js", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}, "_id": "esprima@1.1.1", "dist": {"shasum": "5b6f1547f4d102e670e140c509be6771d6aeb549", "tarball": "https://registry.npmjs.org/esprima/-/esprima-1.1.1.tgz", "integrity": "sha512-qxxB994/7NtERxgXdFgLHIs9M6bhLXc6qtUmWZ3L8+gTQ9qaoyki2887P2IqAYsoENyr8SUbTutStDniOHSDHg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHpqIkAwIeYT8SJ1bmYqwCYjJv1HhCObx9CxGSXhGsIoAiA6xhHVQvrJ11BexnjeF1PTQJrWjyZ03KkVKh9fAhVlEQ=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "directories": {}}, "1.2.0": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "1.2.0", "files": ["bin", "test/run.js", "test/runner.js", "test/test.js", "test/compat.js", "test/reflect.js", "esprima.js"], "engines": {"node": ">=0.4.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/ariya/esprima.git"}, "bugs": {"url": "http://issues.esprima.org"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "devDependencies": {"jslint": "~0.1.9", "eslint": "~0.4.3", "jscs": "~1.2.4", "istanbul": "~0.2.6", "complexity-report": "~0.6.1", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "json-diff": "~0.3.1", "optimist": "~0.6.0"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"generate-regex": "node tools/generate-identifier-regex.js", "test": "npm run-script lint && node test/run.js && npm run-script coverage && npm run-script complexity", "lint": "npm run-script check-version && npm run-script eslint && npm run-script jscs && npm run-script jslint", "check-version": "node tools/check-version.js", "eslint": "node node_modules/eslint/bin/eslint.js esprima.js", "jscs": "node node_modules/.bin/jscs esprima.js", "jslint": "node node_modules/jslint/bin/jslint.js esprima.js", "coverage": "npm run-script analyze-coverage && npm run-script check-coverage", "analyze-coverage": "node node_modules/istanbul/lib/cli.js cover test/runner.js", "check-coverage": "node node_modules/istanbul/lib/cli.js check-coverage --statement 100 --branch 100 --function 100", "complexity": "npm run-script analyze-complexity && npm run-script check-complexity", "analyze-complexity": "node tools/list-complexity.js", "check-complexity": "node node_modules/complexity-report/src/cli.js --maxcc 14 --silent -l -w esprima.js", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}, "_id": "esprima@1.2.0", "dist": {"shasum": "5091beca413223988a7111f418da9aa8a3492abd", "tarball": "https://registry.npmjs.org/esprima/-/esprima-1.2.0.tgz", "integrity": "sha512-G5<PERSON>f2l6TGNLZ6/hmOSkE6Bz0KpxGI2rq1fpcu+opNxaWyJ/F0M0KIyIST1z+fjcCUFhzF+aK2GTqWkFjwJbIg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD1DtH8JwhoVt2IVuIUE4Y2ITjrFfSVLjd7Le12jsOmgAIhAKd+uJV+mNM4oEUWPogwrge7VY6wiW/CgSXhsxBGikiM"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "directories": {}}, "1.2.1": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "1.2.1", "files": ["bin", "test/run.js", "test/runner.js", "test/test.js", "test/compat.js", "test/reflect.js", "esprima.js"], "engines": {"node": ">=0.4.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/ariya/esprima.git"}, "bugs": {"url": "http://issues.esprima.org"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "devDependencies": {"jslint": "~0.1.9", "eslint": "~0.4.3", "jscs": "~1.2.4", "istanbul": "~0.2.6", "complexity-report": "~0.6.1", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "json-diff": "~0.3.1", "optimist": "~0.6.0"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"generate-regex": "node tools/generate-identifier-regex.js", "test": "npm run-script lint && node test/run.js && npm run-script coverage && npm run-script complexity", "lint": "npm run-script check-version && npm run-script eslint && npm run-script jscs && npm run-script jslint", "check-version": "node tools/check-version.js", "eslint": "node node_modules/eslint/bin/eslint.js esprima.js", "jscs": "node node_modules/.bin/jscs esprima.js", "jslint": "node node_modules/jslint/bin/jslint.js esprima.js", "coverage": "npm run-script analyze-coverage && npm run-script check-coverage", "analyze-coverage": "node node_modules/istanbul/lib/cli.js cover test/runner.js", "check-coverage": "node node_modules/istanbul/lib/cli.js check-coverage --statement 100 --branch 100 --function 100", "complexity": "npm run-script analyze-complexity && npm run-script check-complexity", "analyze-complexity": "node tools/list-complexity.js", "check-complexity": "node node_modules/complexity-report/src/cli.js --maxcc 14 --silent -l -w esprima.js", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}, "_id": "esprima@1.2.1", "dist": {"shasum": "201bdf4dfa8595f72f43936c7634674b54f4734f", "tarball": "https://registry.npmjs.org/esprima/-/esprima-1.2.1.tgz", "integrity": "sha512-jrECgEr3fEQcbfTco8Pqzs/JH7n7nnqENJDc/FIGEkg56aYNCMf1CJvD8XdISzHUSRi19mvqmDuU5sXm5R6mZg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzb83uMSdFLujDaySCW5keoN0kW4pO5auyr68Y09FqFAIga0pi0gXsMExaajpsk3G3EmqFEaZFjwmvr+GUj3QN5EQ="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "directories": {}}, "1.2.2": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "1.2.2", "files": ["bin", "test/run.js", "test/runner.js", "test/test.js", "test/compat.js", "test/reflect.js", "esprima.js"], "engines": {"node": ">=0.4.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/ariya/esprima.git"}, "bugs": {"url": "http://issues.esprima.org"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "devDependencies": {"jslint": "~0.1.9", "eslint": "~0.4.3", "jscs": "~1.2.4", "istanbul": "~0.2.6", "complexity-report": "~0.6.1", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "json-diff": "~0.3.1", "optimist": "~0.6.0"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"generate-regex": "node tools/generate-identifier-regex.js", "test": "npm run-script lint && node test/run.js && npm run-script coverage && npm run-script complexity", "lint": "npm run-script check-version && npm run-script eslint && npm run-script jscs && npm run-script jslint", "check-version": "node tools/check-version.js", "eslint": "node node_modules/eslint/bin/eslint.js esprima.js", "jscs": "node node_modules/.bin/jscs esprima.js", "jslint": "node node_modules/jslint/bin/jslint.js esprima.js", "coverage": "npm run-script analyze-coverage && npm run-script check-coverage", "analyze-coverage": "node node_modules/istanbul/lib/cli.js cover test/runner.js", "check-coverage": "node node_modules/istanbul/lib/cli.js check-coverage --statement 100 --branch 100 --function 100", "complexity": "npm run-script analyze-complexity && npm run-script check-complexity", "analyze-complexity": "node tools/list-complexity.js", "check-complexity": "node node_modules/complexity-report/src/cli.js --maxcc 14 --silent -l -w esprima.js", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}, "_id": "esprima@1.2.2", "dist": {"shasum": "76a0fd66fcfe154fd292667dc264019750b1657b", "tarball": "https://registry.npmjs.org/esprima/-/esprima-1.2.2.tgz", "integrity": "sha512-+JpPZam9w5DuJ3Q67SqsMGtiHKENSMRVoxvArfJZK01/BfLEObtZ6orJa/MtoGNR/rfMgp5837T41PAmTwAv/A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDxIEtidXec2N9J6SCMmEl+fpLHNxT9hFhFyHACZvdqPAiBCrU8cAtu+UV6wAI6ECDUfaYibDwDHwVLOz0qxujKvMw=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "directories": {}}, "1.2.3": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "1.2.3", "files": ["bin", "test/run.js", "test/runner.js", "test/test.js", "test/compat.js", "test/reflect.js", "esprima.js"], "engines": {"node": ">=0.4.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/ariya/esprima.git"}, "bugs": {"url": "http://issues.esprima.org"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "devDependencies": {"jslint": "~0.1.9", "eslint": "~0.4.3", "jscs": "~1.2.4", "istanbul": "~0.2.6", "complexity-report": "~0.6.1", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "json-diff": "~0.3.1", "optimist": "~0.6.0"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"generate-regex": "node tools/generate-identifier-regex.js", "test": "npm run-script lint && node test/run.js && npm run-script coverage && npm run-script complexity", "lint": "npm run-script check-version && npm run-script eslint && npm run-script jscs && npm run-script jslint", "check-version": "node tools/check-version.js", "eslint": "node node_modules/eslint/bin/eslint.js esprima.js", "jscs": "node node_modules/jscs/bin/jscs esprima.js", "jslint": "node node_modules/jslint/bin/jslint.js esprima.js", "coverage": "npm run-script analyze-coverage && npm run-script check-coverage", "analyze-coverage": "node node_modules/istanbul/lib/cli.js cover test/runner.js", "check-coverage": "node node_modules/istanbul/lib/cli.js check-coverage --statement 100 --branch 100 --function 100", "complexity": "npm run-script analyze-complexity && npm run-script check-complexity", "analyze-complexity": "node tools/list-complexity.js", "check-complexity": "node node_modules/complexity-report/src/cli.js --maxcc 14 --silent -l -w esprima.js", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}, "_id": "esprima@1.2.3", "dist": {"shasum": "173c9c28d0ec7753afb977b6b21b3017367aac1b", "tarball": "https://registry.npmjs.org/esprima/-/esprima-1.2.3.tgz", "integrity": "sha512-D6gbBd4XBQfLbCzjLeFv1YQ4rlLw54UZTwS6ykxt9CH9IAWO3O6RZdrVipnwabSiSnsD1boR/RcwkxW5A7pgqA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBGWHT/LBC2jBPCq1pg7Gw1c93I1LeUVQiNPEsjvplkwAiEAmeSEX3lsQdQlMocuLR1LLRHRa6FgqjG2atrCiSh31c4="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "directories": {}}, "1.2.4": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "1.2.4", "files": ["bin", "test/run.js", "test/runner.js", "test/test.js", "test/compat.js", "test/reflect.js", "esprima.js"], "engines": {"node": ">=0.4.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/ariya/esprima.git"}, "bugs": {"url": "http://issues.esprima.org"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "devDependencies": {"jslint": "~0.1.9", "eslint": "~0.4.3", "jscs": "~1.2.4", "istanbul": "~0.2.6", "complexity-report": "~0.6.1", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "json-diff": "~0.3.1", "optimist": "~0.6.0"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"generate-regex": "node tools/generate-identifier-regex.js", "test": "npm run-script lint && node test/run.js && npm run-script coverage && npm run-script complexity", "lint": "npm run-script check-version && npm run-script eslint && npm run-script jscs && npm run-script jslint", "check-version": "node tools/check-version.js", "eslint": "node node_modules/eslint/bin/eslint.js esprima.js", "jscs": "node node_modules/jscs/bin/jscs esprima.js", "jslint": "node node_modules/jslint/bin/jslint.js esprima.js", "coverage": "npm run-script analyze-coverage && npm run-script check-coverage", "analyze-coverage": "node node_modules/istanbul/lib/cli.js cover test/runner.js", "check-coverage": "node node_modules/istanbul/lib/cli.js check-coverage --statement 100 --branch 100 --function 100", "complexity": "npm run-script analyze-complexity && npm run-script check-complexity", "analyze-complexity": "node tools/list-complexity.js", "check-complexity": "node node_modules/complexity-report/src/cli.js --maxcc 14 --silent -l -w esprima.js", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}, "_id": "esprima@1.2.4", "dist": {"shasum": "835a0cfc8a628a7117da654bfaced8408a91dba7", "tarball": "https://registry.npmjs.org/esprima/-/esprima-1.2.4.tgz", "integrity": "sha512-iB6GNCXnLu1aizDC2gd8iLz7N6Ab+QhbEA3ihmZVrXf+Mg1THpe5YBLly6PXwk6JSBhP0JlitYuqueSEuZVNtQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCoDuPfvwH3E2P8cvk6ZgAeMWmBxNUL1dmjrpv3NGTpYwIhAKqtpmNmFm6DqJ+f/JqNYoNIeqVkNoM7IMqGiE8C6hq0"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "directories": {}}, "2.0.0": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "2.0.0", "files": ["bin", "test/run.js", "test/runner.js", "test/test.js", "test/compat.js", "test/reflect.js", "esprima.js"], "engines": {"node": ">=0.4.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/jquery/esprima.git"}, "bugs": {"url": "http://issues.esprima.org"}, "licenses": [{"type": "BSD", "url": "https://github.com/jquery/esprima/raw/master/LICENSE.BSD"}], "devDependencies": {"eslint": "~0.12.0", "jscs": "~1.10.0", "istanbul": "~0.2.6", "escomplex-js": "1.0.0", "complexity-report": "~1.1.1", "regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5", "json-diff": "~0.3.1", "optimist": "~0.6.0"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"generate-regex": "node tools/generate-identifier-regex.js", "test": "node test/run.js && npm run lint && npm run coverage", "lint": "npm run check-version && npm run eslint && npm run jscs && npm run complexity", "check-version": "node tools/check-version.js", "jscs": "jscs esprima.js test/*test.js", "eslint": "node node_modules/eslint/bin/eslint.js esprima.js", "complexity": "node tools/list-complexity.js && cr -s -l -w --maxcyc 16 esprima.js", "coverage": "npm run analyze-coverage && npm run check-coverage", "analyze-coverage": "node node_modules/istanbul/lib/cli.js cover test/runner.js", "check-coverage": "node node_modules/istanbul/lib/cli.js check-coverage --statement 100 --branch 100 --function 100", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}, "_id": "esprima@2.0.0", "dist": {"shasum": "609ac5c2667eae5433b41eb9ecece2331b41498f", "tarball": "https://registry.npmjs.org/esprima/-/esprima-2.0.0.tgz", "integrity": "sha512-Nn4dBM9pwAzo5GSAbfKWiT66gqBh3elZgrC242j42cbKpkmnkgX9UINXeC++e013U0qXq5xowcB0Bgkwg09s0g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQClv49CGtTFvxxt3Z/YcWiWRUEmpAkajFU7YeY6NaLh0QIgdgW4GEp7o8W+gys2ApZ3kCrQdEc/HgHWvtbSW/63FLI="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "directories": {}}, "1.2.5": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "1.2.5", "files": ["bin", "test/run.js", "test/runner.js", "test/test.js", "test/compat.js", "test/reflect.js", "esprima.js"], "engines": {"node": ">=0.4.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/ariya/esprima.git"}, "bugs": {"url": "http://issues.esprima.org"}, "licenses": [{"type": "BSD", "url": "http://github.com/ariya/esprima/raw/master/LICENSE.BSD"}], "devDependencies": {"jslint": "~0.1.9", "eslint": "~0.4.3", "jscs": "~1.2.4", "istanbul": "~0.2.6", "complexity-report": "~0.6.1", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "json-diff": "~0.3.1", "optimist": "~0.6.0"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"generate-regex": "node tools/generate-identifier-regex.js", "test": "npm run-script lint && node test/run.js && npm run-script coverage && npm run-script complexity", "lint": "npm run-script check-version && npm run-script eslint && npm run-script jscs && npm run-script jslint", "check-version": "node tools/check-version.js", "eslint": "node node_modules/eslint/bin/eslint.js esprima.js", "jscs": "node node_modules/jscs/bin/jscs esprima.js", "jslint": "echo Skipping JSLint", "coverage": "npm run-script analyze-coverage && npm run-script check-coverage", "analyze-coverage": "node node_modules/istanbul/lib/cli.js cover test/runner.js", "check-coverage": "node node_modules/istanbul/lib/cli.js check-coverage --statement 100 --branch 100 --function 100", "complexity": "npm run-script analyze-complexity && npm run-script check-complexity", "analyze-complexity": "node tools/list-complexity.js", "check-complexity": "node node_modules/complexity-report/src/cli.js --maxcc 14 --silent -l -w esprima.js", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}, "gitHead": "d3e376f04f096cb07f6b9fad0b44e9b6054f0582", "_id": "esprima@1.2.5", "_shasum": "0993502feaf668138325756f30f9a51feeec11e9", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "dist": {"shasum": "0993502feaf668138325756f30f9a51feeec11e9", "tarball": "https://registry.npmjs.org/esprima/-/esprima-1.2.5.tgz", "integrity": "sha512-S9VbPDU0adFErpDai3qDkjq8+G05ONtKzcyNrPKg/ZKa+tf879nX2KexNU95b31UoTJjRLInNBHHHjFPoCd7lQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG2ZUNs1utzv0hPi66m1AKOgOYvlz2jv4bLymlaU7fxKAiBQTuwoxZNZZZeBrICy0lHAl+IlBM2JRV2TzWBIq786og=="}]}, "directories": {}}, "2.1.0": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "2.1.0", "files": ["bin", "test/run.js", "test/runner.js", "test/test.js", "test/compat.js", "test/reflect.js", "esprima.js"], "engines": {"node": ">=0.4.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/jquery/esprima.git"}, "bugs": {"url": "http://issues.esprima.org"}, "licenses": [{"type": "BSD", "url": "https://github.com/jquery/esprima/raw/master/LICENSE.BSD"}], "devDependencies": {"eslint": "~0.15.1", "jscs": "~1.11.3", "istanbul": "~0.3.7", "escomplex-js": "1.2.0", "complexity-report": "~1.4.0", "regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5", "json-diff": "~0.3.1", "optimist": "~0.6.0"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"generate-regex": "node tools/generate-identifier-regex.js", "test": "node test/run.js && npm run lint && npm run coverage", "lint": "npm run check-version && npm run eslint && npm run jscs && npm run complexity", "check-version": "node tools/check-version.js", "jscs": "jscs esprima.js", "eslint": "node node_modules/eslint/bin/eslint.js esprima.js", "complexity": "node tools/list-complexity.js && cr -s -l -w --maxcyc 16 esprima.js", "coverage": "npm run analyze-coverage && npm run check-coverage", "analyze-coverage": "istanbul cover test/runner.js", "check-coverage": "istanbul check-coverage --statement 100 --branch 100 --function 100", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}, "gitHead": "fb6563827bd26357fb39086b2b1d5df4e5858cce", "_id": "esprima@2.1.0", "_shasum": "c1c9fb94975dfcc3fc71c60f074f3c5156a28ef5", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "dist": {"shasum": "c1c9fb94975dfcc3fc71c60f074f3c5156a28ef5", "tarball": "https://registry.npmjs.org/esprima/-/esprima-2.1.0.tgz", "integrity": "sha512-PLBj2ZuACVaiLdINs1PmtKaZEGffumHTZPGtPxCikFo30JnzuKgB9six+H6KbOi3HrI/UKHrBhms2R8k3viwIg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGNVg1PNpPy7LzVWcC+j/I2kEsyMKDjfFBC8pAiHbOHIAiEA2tfznNvDAFeAbxztElmrrQs8G2Ff+e6k7EuScIn22eE="}]}, "directories": {}}, "2.2.0": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "2.2.0", "files": ["bin", "test/run.js", "test/runner.js", "test/test.js", "esprima.js"], "engines": {"node": ">=0.4.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/jquery/esprima.git"}, "bugs": {"url": "http://issues.esprima.org"}, "licenses": [{"type": "BSD", "url": "https://github.com/jquery/esprima/raw/master/LICENSE.BSD"}], "devDependencies": {"eslint": "~0.19.0", "jscs": "~1.12.0", "istanbul": "~0.3.7", "escomplex-js": "1.2.0", "complexity-report": "~1.4.0", "regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5", "json-diff": "~0.3.1", "optimist": "~0.6.0"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"generate-regex": "node tools/generate-identifier-regex.js", "test": "node test/run.js && npm run lint && npm run coverage", "lint": "npm run check-version && npm run eslint && npm run jscs && npm run complexity", "check-version": "node tools/check-version.js", "jscs": "jscs esprima.js", "eslint": "node node_modules/eslint/bin/eslint.js esprima.js", "complexity": "node tools/list-complexity.js && cr -s -l -w --maxcyc 17 esprima.js", "coverage": "npm run analyze-coverage && npm run check-coverage", "analyze-coverage": "istanbul cover test/runner.js", "check-coverage": "istanbul check-coverage --statement 100 --branch 100 --function 100", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}, "gitHead": "deef03ca006b03912d9f74b041f9239a9045181f", "_id": "esprima@2.2.0", "_shasum": "4292c1d68e4173d815fa2290dc7afc96d81fcd83", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "dist": {"shasum": "4292c1d68e4173d815fa2290dc7afc96d81fcd83", "tarball": "https://registry.npmjs.org/esprima/-/esprima-2.2.0.tgz", "integrity": "sha512-YvuZpccAi+WHZq+jLfF1wKzfOxmw1QxebJNufw3a9bXWpEB+XTeaflxE1CLQTB2JADZbxiwsODBslLKUvsYaZw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC/2MWbhO69HKA/n/eKsswGGI0EKTPCM/j1CZdiGEX7igIhAPis1HGDsv9ktn8NDYcSdKZT9BgPBzqq5ALCeoBBJ0Bu"}]}, "directories": {}}, "2.3.0": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "2.3.0", "files": ["bin", "test/run.js", "test/runner.js", "test/test.js", "esprima.js"], "engines": {"node": ">=0.4.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/jquery/esprima.git"}, "bugs": {"url": "https://github.com/jquery/esprima/issues"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"eslint": "~0.23.0", "jscs": "~1.13.1", "istanbul": "~0.3.15", "escomplex-js": "1.2.0", "complexity-report": "~1.4.0", "regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5", "json-diff": "~0.3.1", "optimist": "~0.6.0"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"generate-regex": "node tools/generate-identifier-regex.js", "test": "node test/run.js && npm run lint && npm run coverage", "lint": "npm run check-version && npm run eslint && npm run jscs && npm run complexity", "check-version": "node tools/check-version.js", "jscs": "jscs esprima.js", "eslint": "node node_modules/eslint/bin/eslint.js esprima.js", "complexity": "node tools/list-complexity.js && cr -s -l -w --maxcyc 17 esprima.js", "coverage": "npm run analyze-coverage && npm run check-coverage", "analyze-coverage": "istanbul cover test/runner.js", "check-coverage": "istanbul check-coverage --statement 100 --branch 100 --function 100", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick"}, "gitHead": "df4323f81c5e94516d53887f1c0abbe2fdb8076d", "_id": "esprima@2.3.0", "_shasum": "d26debd7545fcf8916a80668236310e14da6b345", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "dist": {"shasum": "d26debd7545fcf8916a80668236310e14da6b345", "tarball": "https://registry.npmjs.org/esprima/-/esprima-2.3.0.tgz", "integrity": "sha512-9PXQCAM18+RrOHTSaoNpVK1YxwCQxG7CgwfCP/wPgHYKHfQiwebEu5ADkJH6SMsGFTvjMLABETAshP5G80wtXw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCaNQxLgXTu//9Cpo5vHsrMBTUAoJ3vcyHSma98t4FZ5wIhALKMwO4gfVtGtN6O0T+2ADbAi3ihFobsoNx0zjQYoZSA"}]}, "directories": {}}, "2.4.0": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "2.4.0", "files": ["bin", "unit-tests.js", "esprima.js"], "engines": {"node": ">=0.4.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/jquery/esprima.git"}, "bugs": {"url": "https://github.com/jquery/esprima/issues"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"eslint": "~0.23.0", "jscs": "~1.13.1", "istanbul": "~0.3.16", "escomplex-js": "1.2.0", "complexity-report": "~1.4.0", "regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5", "json-diff": "~0.3.1", "optimist": "~0.6.0", "coveralls": "~2.11.2"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"generate-regex": "node tools/generate-identifier-regex.js", "test": "node unit-tests.js && npm run lint && npm run coverage", "lint": "npm run check-version && npm run eslint && npm run jscs && npm run complexity", "check-version": "node tools/check-version.js", "jscs": "jscs esprima.js", "eslint": "node node_modules/eslint/bin/eslint.js esprima.js", "complexity": "node tools/list-complexity.js && cr -s -l -w --maxcyc 19 esprima.js", "coverage": "npm run analyze-coverage && npm run check-coverage", "analyze-coverage": "istanbul cover test/runner.js", "check-coverage": "istanbul check-coverage --statement 100 --branch 100 --function 100", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick", "travis": "npm test && coveralls < ./coverage/lcov.info"}, "gitHead": "5d90fe73c021ab96a0660a7ed8b5e3034174c5c9", "_id": "esprima@2.4.0", "_shasum": "8f1852ea605d36f81e4b6ec831a53ecb4dc501c6", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "dist": {"shasum": "8f1852ea605d36f81e4b6ec831a53ecb4dc501c6", "tarball": "https://registry.npmjs.org/esprima/-/esprima-2.4.0.tgz", "integrity": "sha512-NSVs619inaj9yk4nmrkkZDs4rghNri5fgCyZgfTYBukcJ24A+dPlamMsuaBHqrLgIGYR3DeEBIBygt3/uSbq7g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFASoA+ZuPTRHBeMC1QPBtvcLlMbvWxL3nZnKk3KcCpmAiBUesfg8F7hT9SZ0jda5wvj0a0YzOvF68ExbDXy84quZA=="}]}, "directories": {}}, "2.4.1": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "2.4.1", "files": ["bin", "unit-tests.js", "esprima.js"], "engines": {"node": ">=0.4.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/jquery/esprima.git"}, "bugs": {"url": "https://github.com/jquery/esprima/issues"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"eslint": "~0.23.0", "jscs": "~1.13.1", "istanbul": "~0.3.16", "escomplex-js": "1.2.0", "complexity-report": "~1.4.0", "regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5", "json-diff": "~0.3.1", "optimist": "~0.6.0", "coveralls": "~2.11.2"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"generate-regex": "node tools/generate-identifier-regex.js", "test": "node unit-tests.js && npm run lint && npm run coverage", "lint": "npm run check-version && npm run eslint && npm run jscs && npm run complexity", "check-version": "node tools/check-version.js", "jscs": "jscs esprima.js", "eslint": "node node_modules/eslint/bin/eslint.js esprima.js", "complexity": "node tools/list-complexity.js && cr -s -l -w --maxcyc 19 esprima.js", "coverage": "npm run analyze-coverage && npm run check-coverage", "analyze-coverage": "istanbul cover test/runner.js", "check-coverage": "istanbul check-coverage --statement 100 --branch 100 --function 100", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick", "travis": "npm test && coveralls < ./coverage/lcov.info"}, "gitHead": "fdaf9f6775cb06593816d6b6ab95a15d34a65d09", "_id": "esprima@2.4.1", "_shasum": "83059c751e9e9c41d228a41aaa1eef0ccce384ba", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "dist": {"shasum": "83059c751e9e9c41d228a41aaa1eef0ccce384ba", "tarball": "https://registry.npmjs.org/esprima/-/esprima-2.4.1.tgz", "integrity": "sha512-oQ5niex1XEkpjZhmW1zsozCG515481U0s+A1n6xU9usjkLSy7ZDvfuaAR+CKAKujczvEy7sOPIiX/GO+MZPk8Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBMtguZdbNYgzPD3A8Fz1VuU8oAP5z2QqB2m3gn2322WAiEA5yYgSQihUZ4iwsYvs16+MWmJ3zXcroxmNG9I7VbjtLA="}]}, "directories": {}}, "2.5.0": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "2.5.0", "files": ["bin", "unit-tests.js", "esprima.js"], "engines": {"node": ">=0.10.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/jquery/esprima.git"}, "bugs": {"url": "https://github.com/jquery/esprima/issues"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"coveralls": "~2.11.2", "escomplex-js": "1.2.0", "eslint": "~0.23.0", "everything.js": "~1.0.3", "glob": "^5.0.14", "istanbul": "~0.3.16", "jscs": "~2.0.0", "json-diff": "~0.3.1", "karma": "^0.13.3", "karma-chrome-launcher": "^0.2.0", "karma-mocha": "^0.2.0", "lodash": "^3.10.0", "mocha": "^2.2.5", "node-tick-processor": "~0.0.2", "regenerate": "~0.6.2", "unicode-7.0.0": "~0.1.5"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"check-version": "node test/check-version.js", "jscs": "jscs esprima.js && jscs test/*.js", "eslint": "node node_modules/eslint/bin/eslint.js -c .lintrc esprima.js", "complexity": "node test/check-complexity.js", "static-analysis": "npm run check-version && npm run jscs && npm run eslint && npm run complexity", "unit-tests": "node test/unit-tests.js", "grammar-tests": "node test/grammar-tests.js", "regression-tests": "node test/regression-tests.js", "tests": "npm run unit-tests && npm run grammar-tests && npm run regression-tests", "generate-fixtures": "node tools/generate-fixtures.js", "browser-tests": "npm run generate-fixtures && karma start --single-run", "analyze-coverage": "istanbul cover test/unit-tests.js", "check-coverage": "istanbul check-coverage --statement 100 --branch 100 --function 100", "dynamic-analysis": "npm run analyze-coverage && npm run check-coverage", "test": "npm run tests && npm run static-analysis && npm run dynamic-analysis", "profile": "node --prof test/profile.js && mv isolate*.log v8.log && node-tick-processor", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick", "coveralls": "coveralls < ./coverage/lcov.info", "downstream": "node test/downstream.js", "travis": "npm test && npm run coveralls && npm run downstream", "generate-regex": "node tools/generate-identifier-regex.js"}, "gitHead": "0e3104fb835129d6727868c865a9ba4f80df3ab8", "_id": "esprima@2.5.0", "_shasum": "f387a46fd344c1b1a39baf8c20bfb43b6d0058cc", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "dist": {"shasum": "f387a46fd344c1b1a39baf8c20bfb43b6d0058cc", "tarball": "https://registry.npmjs.org/esprima/-/esprima-2.5.0.tgz", "integrity": "sha512-uM6hfS0/8ybNIj8SGRMdidPJy5uhWqWN/GIkyqnMAbCSL44yfFGLuBpRRCgOpBXBZt2OymQuM+IfahkqJq3DWw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQ22fiRCy3p4RuiCej2d3mvAAVCMnbKYsYa8KYoqfmcAIgJDUPfLoBl0Dncc2w6IDi51mbU7nkss6SKOuYiQVwAig="}]}, "directories": {}}, "2.6.0": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "2.6.0", "files": ["bin", "unit-tests.js", "esprima.js"], "engines": {"node": ">=0.10.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/jquery/esprima.git"}, "bugs": {"url": "https://github.com/jquery/esprima/issues"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"codecov.io": "~0.1.6", "escomplex-js": "1.2.0", "eslint": "~1.3.1", "everything.js": "~1.0.3", "glob": "^5.0.14", "istanbul": "~0.3.19", "jscs": "~2.1.1", "json-diff": "~0.3.1", "karma": "^0.13.3", "karma-chrome-launcher": "^0.2.0", "karma-detect-browsers": "^2.0.1", "karma-firefox-launcher": "^0.1.6", "karma-ie-launcher": "^0.2.0", "karma-mocha": "^0.2.0", "karma-safari-launcher": "^0.1.1", "lodash": "^3.10.0", "mocha": "^2.2.5", "node-tick-processor": "~0.0.2", "regenerate": "~0.6.2", "temp": "~0.8.3", "unicode-7.0.0": "~0.1.5"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"check-version": "node test/check-version.js", "jscs": "jscs -p crockford esprima.js && jscs -p crockford test/*.js", "eslint": "node node_modules/eslint/bin/eslint.js -c .lintrc esprima.js", "complexity": "node test/check-complexity.js", "static-analysis": "npm run check-version && npm run jscs && npm run eslint && npm run complexity", "unit-tests": "node test/unit-tests.js", "grammar-tests": "node test/grammar-tests.js", "regression-tests": "node test/regression-tests.js", "tests": "npm run generate-fixtures && npm run unit-tests && npm run grammar-tests && npm run regression-tests", "generate-fixtures": "node tools/generate-fixtures.js", "browser-tests": "npm run generate-fixtures && cd test && karma start --single-run", "analyze-coverage": "istanbul cover test/unit-tests.js", "check-coverage": "istanbul check-coverage --statement 100 --branch 100 --function 100", "dynamic-analysis": "npm run analyze-coverage && npm run check-coverage", "test": "npm run tests && npm run static-analysis && npm run dynamic-analysis", "profile": "node --prof test/profile.js && mv isolate*.log v8.log && node-tick-processor", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick", "codecov": "istanbul report cobertura && codecov < ./coverage/cobertura-coverage.xml", "downstream": "node test/downstream.js", "travis": "npm test", "circleci": "npm test && npm run codecov && npm run downstream", "appveyor": "npm run tests && npm run browser-tests && npm run dynamic-analysis", "generate-regex": "node tools/generate-identifier-regex.js"}, "gitHead": "72bb2191ffd9d4f2053cab511da0b27b882a5a48", "_id": "esprima@2.6.0", "_shasum": "eddae7cccd7c4d6f3058b7f3823718aaaef7527f", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "dist": {"shasum": "eddae7cccd7c4d6f3058b7f3823718aaaef7527f", "tarball": "https://registry.npmjs.org/esprima/-/esprima-2.6.0.tgz", "integrity": "sha512-NcgdLYV9j3liTi92+VJp1dGVZnHpFXh8QD/0qWzgy85fO3vKB3m3G2eZa2Ks6b3YzT9iCODMkdYLlVgbVJEj3A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD+/8rCkuR4EIKntDQ9+eAx7gifsnGH0zBWaVG6qp2q4QIgJHh83Y46ySnqnANn2rWeyPYAIMaiisR+sCIwMKlfpro="}]}, "directories": {}}, "2.7.0": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "2.7.0", "files": ["bin", "unit-tests.js", "esprima.js"], "engines": {"node": ">=0.10.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/jquery/esprima.git"}, "bugs": {"url": "https://github.com/jquery/esprima/issues"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"codecov.io": "~0.1.6", "escomplex-js": "1.2.0", "eslint": "~1.7.2", "everything.js": "~1.0.3", "glob": "^5.0.15", "istanbul": "~0.4.0", "jscs": "~2.3.5", "json-diff": "~0.3.1", "karma": "^0.13.11", "karma-chrome-launcher": "^0.2.1", "karma-detect-browsers": "^2.0.2", "karma-firefox-launcher": "^0.1.6", "karma-ie-launcher": "^0.2.0", "karma-mocha": "^0.2.0", "karma-safari-launcher": "^0.1.1", "karma-sauce-launcher": "^0.2.14", "lodash": "^3.10.0", "mocha": "^2.3.3", "node-tick-processor": "~0.0.2", "regenerate": "~1.2.1", "temp": "~0.8.3", "unicode-7.0.0": "~0.1.5"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"check-version": "node test/check-version.js", "jscs": "jscs -p crockford esprima.js && jscs -p crockford test/*.js", "eslint": "node node_modules/eslint/bin/eslint.js -c .lintrc esprima.js", "complexity": "node test/check-complexity.js", "static-analysis": "npm run check-version && npm run jscs && npm run eslint && npm run complexity", "unit-tests": "node test/unit-tests.js", "grammar-tests": "node test/grammar-tests.js", "regression-tests": "node test/regression-tests.js", "all-tests": "npm run generate-fixtures && npm run unit-tests && npm run grammar-tests && npm run regression-tests", "generate-fixtures": "node tools/generate-fixtures.js", "browser-tests": "npm run generate-fixtures && cd test && karma start --single-run", "saucelabs-evergreen": "cd test && karma start saucelabs-evergreen.conf.js", "saucelabs-safari": "cd test && karma start saucelabs-safari.conf.js", "saucelabs-ie": "cd test && karma start saucelabs-ie.conf.js", "analyze-coverage": "istanbul cover test/unit-tests.js", "check-coverage": "istanbul check-coverage --statement 100 --branch 100 --function 100", "dynamic-analysis": "npm run analyze-coverage && npm run check-coverage", "test": "npm run all-tests && npm run static-analysis && npm run dynamic-analysis", "profile": "node --prof test/profile.js && mv isolate*.log v8.log && node-tick-processor", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick", "codecov": "istanbul report cobertura && codecov < ./coverage/cobertura-coverage.xml", "downstream": "node test/downstream.js", "travis": "npm test", "circleci": "npm test && npm run codecov && npm run downstream", "appveyor": "npm run all-tests && npm run browser-tests && npm run dynamic-analysis", "droneio": "npm test && npm run saucelabs-evergreen && npm run saucelabs-ie && npm run saucelabs-safari", "generate-regex": "node tools/generate-identifier-regex.js"}, "gitHead": "67289bf6b962d003c0e8a64c77b953b0559b52e5", "_id": "esprima@2.7.0", "_shasum": "74cfb0e4ae43f0b81541dcc30050f9dacb1f707e", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "dist": {"shasum": "74cfb0e4ae43f0b81541dcc30050f9dacb1f707e", "tarball": "https://registry.npmjs.org/esprima/-/esprima-2.7.0.tgz", "integrity": "sha512-ws76NaU7H4sIg0qy5C7TvRFaextntw/T8yb/1XLdO0DqipR4g2lsC6HV8iE4iPT2xe5pa/A1klpm3iP8X2q18g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHeHkyqkE5+wsP3unJpEdi+SbM2rX5rzmFT3YAY8/xagAiEAzIre+K+tcF6ZnpSbL3juKq2BsIWmxZslCbTn1AfyTZQ="}]}, "directories": {}}, "2.7.1": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "2.7.1", "files": ["bin", "unit-tests.js", "esprima.js"], "engines": {"node": ">=0.10.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/jquery/esprima.git"}, "bugs": {"url": "https://github.com/jquery/esprima/issues"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"codecov.io": "~0.1.6", "escomplex-js": "1.2.0", "eslint": "~1.7.2", "everything.js": "~1.0.3", "glob": "^5.0.15", "istanbul": "~0.4.0", "jscs": "~2.3.5", "json-diff": "~0.3.1", "karma": "^0.13.11", "karma-chrome-launcher": "^0.2.1", "karma-detect-browsers": "^2.0.2", "karma-firefox-launcher": "^0.1.6", "karma-ie-launcher": "^0.2.0", "karma-mocha": "^0.2.0", "karma-safari-launcher": "^0.1.1", "karma-sauce-launcher": "^0.2.14", "lodash": "^3.10.0", "mocha": "^2.3.3", "node-tick-processor": "~0.0.2", "regenerate": "~1.2.1", "temp": "~0.8.3", "unicode-7.0.0": "~0.1.5"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"check-version": "node test/check-version.js", "jscs": "jscs -p crockford esprima.js && jscs -p crockford test/*.js", "eslint": "node node_modules/eslint/bin/eslint.js -c .lintrc esprima.js", "complexity": "node test/check-complexity.js", "static-analysis": "npm run check-version && npm run jscs && npm run eslint && npm run complexity", "unit-tests": "node test/unit-tests.js", "grammar-tests": "node test/grammar-tests.js", "regression-tests": "node test/regression-tests.js", "all-tests": "npm run generate-fixtures && npm run unit-tests && npm run grammar-tests && npm run regression-tests", "generate-fixtures": "node tools/generate-fixtures.js", "browser-tests": "npm run generate-fixtures && cd test && karma start --single-run", "saucelabs-evergreen": "cd test && karma start saucelabs-evergreen.conf.js", "saucelabs-safari": "cd test && karma start saucelabs-safari.conf.js", "saucelabs-ie": "cd test && karma start saucelabs-ie.conf.js", "analyze-coverage": "istanbul cover test/unit-tests.js", "check-coverage": "istanbul check-coverage --statement 100 --branch 100 --function 100", "dynamic-analysis": "npm run analyze-coverage && npm run check-coverage", "test": "npm run all-tests && npm run static-analysis && npm run dynamic-analysis", "profile": "node --prof test/profile.js && mv isolate*.log v8.log && node-tick-processor", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick", "codecov": "istanbul report cobertura && codecov < ./coverage/cobertura-coverage.xml", "downstream": "node test/downstream.js", "travis": "npm test", "circleci": "npm test && npm run codecov && npm run downstream", "appveyor": "npm run all-tests && npm run browser-tests && npm run dynamic-analysis", "droneio": "npm test && npm run saucelabs-evergreen && npm run saucelabs-ie && npm run saucelabs-safari", "generate-regex": "node tools/generate-identifier-regex.js"}, "gitHead": "8bf04c923b7eb7dbe1ba2dc119f9ded31b45dcd2", "_id": "esprima@2.7.1", "_shasum": "2ab7d1549edd06d14d69a6c1a1754aca02e9657e", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.2", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "dist": {"shasum": "2ab7d1549edd06d14d69a6c1a1754aca02e9657e", "tarball": "https://registry.npmjs.org/esprima/-/esprima-2.7.1.tgz", "integrity": "sha512-hqecf5XLJSqSSCUlVYnz8EAuD5DIfdGbORUyZmA+93aqVzVaOZgqkhTumf0N65HdDDtDHmiieDGh8iERIGsaug==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD5FGy3q/qitJbUIBaGfQwC8yjdiv1W7g2DaINbN2LUjAIgMLkqQKvbBziW7HlhAoSl40xJH2T77rKq+3iMlWP6XPw="}]}, "directories": {}}, "2.7.2": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "2.7.2", "files": ["bin", "unit-tests.js", "esprima.js"], "engines": {"node": ">=0.10.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/jquery/esprima.git"}, "bugs": {"url": "https://github.com/jquery/esprima/issues"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"codecov.io": "~0.1.6", "escomplex-js": "1.2.0", "eslint": "~1.7.2", "everything.js": "~1.0.3", "glob": "^5.0.15", "istanbul": "~0.4.0", "jscs": "~2.3.5", "json-diff": "~0.3.1", "karma": "^0.13.11", "karma-chrome-launcher": "^0.2.1", "karma-detect-browsers": "^2.0.2", "karma-firefox-launcher": "^0.1.6", "karma-ie-launcher": "^0.2.0", "karma-mocha": "^0.2.0", "karma-safari-launcher": "^0.1.1", "karma-sauce-launcher": "^0.2.14", "lodash": "^3.10.0", "mocha": "^2.3.3", "node-tick-processor": "~0.0.2", "regenerate": "~1.2.1", "temp": "~0.8.3", "unicode-7.0.0": "~0.1.5"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"check-version": "node test/check-version.js", "jscs": "jscs -p crockford esprima.js && jscs -p crockford test/*.js", "eslint": "node node_modules/eslint/bin/eslint.js -c .lintrc esprima.js", "complexity": "node test/check-complexity.js", "static-analysis": "npm run check-version && npm run jscs && npm run eslint && npm run complexity", "unit-tests": "node test/unit-tests.js", "grammar-tests": "node test/grammar-tests.js", "regression-tests": "node test/regression-tests.js", "all-tests": "npm run generate-fixtures && npm run unit-tests && npm run grammar-tests && npm run regression-tests", "generate-fixtures": "node tools/generate-fixtures.js", "browser-tests": "npm run generate-fixtures && cd test && karma start --single-run", "saucelabs-evergreen": "cd test && karma start saucelabs-evergreen.conf.js", "saucelabs-safari": "cd test && karma start saucelabs-safari.conf.js", "saucelabs-ie": "cd test && karma start saucelabs-ie.conf.js", "analyze-coverage": "istanbul cover test/unit-tests.js", "check-coverage": "istanbul check-coverage --statement 100 --branch 100 --function 100", "dynamic-analysis": "npm run analyze-coverage && npm run check-coverage", "test": "npm run all-tests && npm run static-analysis && npm run dynamic-analysis", "profile": "node --prof test/profile.js && mv isolate*.log v8.log && node-tick-processor", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick", "codecov": "istanbul report cobertura && codecov < ./coverage/cobertura-coverage.xml", "downstream": "node test/downstream.js", "travis": "npm test", "circleci": "npm test && npm run codecov && npm run downstream", "appveyor": "npm run all-tests && npm run browser-tests && npm run dynamic-analysis", "droneio": "npm test && npm run saucelabs-evergreen && npm run saucelabs-ie && npm run saucelabs-safari", "generate-regex": "node tools/generate-identifier-regex.js"}, "gitHead": "eb05a03b18b8433ab1ebeabea635a949219cd75e", "_id": "esprima@2.7.2", "_shasum": "f43be543609984eae44c933ac63352a6af35f339", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.2", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "dist": {"shasum": "f43be543609984eae44c933ac63352a6af35f339", "tarball": "https://registry.npmjs.org/esprima/-/esprima-2.7.2.tgz", "integrity": "sha512-Cn1Ys1HqRx8yNnjjL4/l5JWf/C8cC5sKqHWoIPjiwZ0QFPhc7QllIqTlvB7vERpRKUJigdHHOc6+1EgVDMRc4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCVMZsBQWsPQkUy/5SGHpymzWnoimOBCY7rfDeLDomi7gIhAK48gcOPg+0XDMSFFzBExBrSNfmcAAST8hPVPtrkke+8"}]}, "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/esprima-2.7.2.tgz_1454477276067_0.014412595424801111"}, "directories": {}}, "2.7.3": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "2.7.3", "files": ["bin", "unit-tests.js", "esprima.js"], "engines": {"node": ">=0.10.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/jquery/esprima.git"}, "bugs": {"url": "https://github.com/jquery/esprima/issues"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"codecov.io": "~0.1.6", "escomplex-js": "1.2.0", "eslint": "~1.7.2", "everything.js": "~1.0.3", "glob": "^5.0.15", "istanbul": "~0.4.0", "jscs": "~2.3.5", "json-diff": "~0.3.1", "karma": "^0.13.11", "karma-chrome-launcher": "^0.2.1", "karma-detect-browsers": "^2.0.2", "karma-firefox-launcher": "^0.1.6", "karma-ie-launcher": "^0.2.0", "karma-mocha": "^0.2.0", "karma-safari-launcher": "^0.1.1", "karma-sauce-launcher": "^0.2.14", "lodash": "^3.10.0", "mocha": "^2.3.3", "node-tick-processor": "~0.0.2", "regenerate": "~1.2.1", "temp": "~0.8.3", "unicode-7.0.0": "~0.1.5"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax"], "scripts": {"check-version": "node test/check-version.js", "jscs": "jscs -p crockford esprima.js && jscs -p crockford test/*.js", "eslint": "node node_modules/eslint/bin/eslint.js -c .lintrc esprima.js", "complexity": "node test/check-complexity.js", "static-analysis": "npm run check-version && npm run jscs && npm run eslint && npm run complexity", "unit-tests": "node test/unit-tests.js", "grammar-tests": "node test/grammar-tests.js", "regression-tests": "node test/regression-tests.js", "all-tests": "npm run generate-fixtures && npm run unit-tests && npm run grammar-tests && npm run regression-tests", "generate-fixtures": "node tools/generate-fixtures.js", "browser-tests": "npm run generate-fixtures && cd test && karma start --single-run", "saucelabs-evergreen": "cd test && karma start saucelabs-evergreen.conf.js", "saucelabs-safari": "cd test && karma start saucelabs-safari.conf.js", "saucelabs-ie": "cd test && karma start saucelabs-ie.conf.js", "analyze-coverage": "istanbul cover test/unit-tests.js", "check-coverage": "istanbul check-coverage --statement 100 --branch 100 --function 100", "dynamic-analysis": "npm run analyze-coverage && npm run check-coverage", "test": "npm run all-tests && npm run static-analysis && npm run dynamic-analysis", "profile": "node --prof test/profile.js && mv isolate*.log v8.log && node-tick-processor", "benchmark": "node test/benchmarks.js", "benchmark-quick": "node test/benchmarks.js quick", "codecov": "istanbul report cobertura && codecov < ./coverage/cobertura-coverage.xml", "downstream": "node test/downstream.js", "travis": "npm test", "circleci": "npm test && npm run codecov && npm run downstream", "appveyor": "npm run all-tests && npm run browser-tests && npm run dynamic-analysis", "droneio": "npm test && npm run saucelabs-evergreen && npm run saucelabs-ie && npm run saucelabs-safari", "generate-regex": "node tools/generate-identifier-regex.js"}, "gitHead": "abaaf7f12040f0b31fac6fee342ffec8feab15d0", "_id": "esprima@2.7.3", "_shasum": "96e3b70d5779f6ad49cd032673d1c312767ba581", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.1.0", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "dist": {"shasum": "96e3b70d5779f6ad49cd032673d1c312767ba581", "tarball": "https://registry.npmjs.org/esprima/-/esprima-2.7.3.tgz", "integrity": "sha512-OarPfz0lFCiW4/AV2Oy1Rp9qu0iusTKqykwTspGCZtPxmF81JR4MmIebvF1F9+UOKth2ZubLQ4XGGaU+hSn99A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC3aNF9+gmrGbOm5QKIkS6z8Jnmyu/qR6sfkwSoT8MGkwIgHalMsEW2bSU0io4pgcQDdR80U/Twkvn3E5EeABao0SY="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/esprima-2.7.3.tgz_1472013602345_0.010668299393728375"}, "directories": {}}, "3.0.0": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "dist/esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "3.0.0", "files": ["bin", "dist/esprima.js"], "engines": {"node": ">=0.10.0"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/jquery/esprima.git"}, "bugs": {"url": "https://github.com/jquery/esprima/issues"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"codecov.io": "~0.1.6", "escomplex-js": "1.2.0", "everything.js": "~1.0.3", "glob": "~7.0.0", "istanbul": "~0.4.0", "jscs": "~3.0.3", "json-diff": "~0.3.1", "karma": "~1.2.0", "karma-chrome-launcher": "~2.0.0", "karma-detect-browsers": "~2.1.0", "karma-firefox-launcher": "~1.0.0", "karma-ie-launcher": "~1.0.0", "karma-mocha": "~1.1.1", "karma-safari-launcher": "~1.0.0", "karma-sauce-launcher": "~1.0.0", "lodash": "~3.10.1", "mocha": "~3.0.2", "node-tick-processor": "~0.0.2", "regenerate": "~1.3.1", "temp": "~0.8.3", "tslint": "~3.15.1", "typescript": "~1.8.10", "typescript-formatter": "~1.2.0", "unicode-8.0.0": "~0.7.0", "webpack": "~1.13.2"}, "keywords": ["ast", "ecmascript", "esprima", "javascript", "parser", "syntax"], "scripts": {"check-version": "node test/check-version.js", "tslint": "tslint src/*.ts", "code-style": "tsfmt --verify src/*.ts && jscs -p crockford test/*.js", "format-code": "tsfmt -r src/*.ts", "complexity": "node test/check-complexity.js", "static-analysis": "npm run check-version && npm run tslint && npm run code-style && npm run complexity", "hostile-env-tests": "node test/hostile-environment-tests.js", "unit-tests": "node test/unit-tests.js", "api-tests": "mocha -R dot test/api-tests.js", "grammar-tests": "node test/grammar-tests.js", "regression-tests": "node test/regression-tests.js", "all-tests": "npm run generate-fixtures && npm run unit-tests && npm run api-tests && npm run grammar-tests && npm run regression-tests && npm run hostile-env-tests", "generate-fixtures": "node tools/generate-fixtures.js", "browser-tests": "npm run compile && npm run generate-fixtures && cd test && karma start --single-run", "saucelabs-evergreen": "cd test && karma start saucelabs-evergreen.conf.js", "saucelabs-safari": "cd test && karma start saucelabs-safari.conf.js", "saucelabs-ie": "cd test && karma start saucelabs-ie.conf.js", "analyze-coverage": "istanbul cover test/unit-tests.js", "check-coverage": "istanbul check-coverage --statement 100 --branch 100 --function 100", "dynamic-analysis": "npm run analyze-coverage && npm run check-coverage", "compile": "tsc -p src/ && webpack && node tools/fixupbundle.js", "test": "npm run compile && npm run all-tests && npm run static-analysis && npm run dynamic-analysis", "prepublish": "npm run compile", "profile": "node --prof test/profile.js && mv isolate*.log v8.log && node-tick-processor", "benchmark-parser": "node -expose_gc test/benchmark-parser.js", "benchmark-tokenizer": "node --expose_gc test/benchmark-tokenizer.js", "benchmark": "npm run benchmark-parser && npm run benchmark-tokenizer", "codecov": "istanbul report cobertura && codecov < ./coverage/cobertura-coverage.xml", "downstream": "node test/downstream.js", "travis": "npm test", "circleci": "npm test && npm run codecov && npm run downstream", "appveyor": "npm run compile && npm run all-tests && npm run browser-tests", "droneio": "npm test && npm run saucelabs-evergreen && npm run saucelabs-ie && npm run saucelabs-safari", "generate-regex": "node tools/generate-identifier-regex.js", "generate-xhtml-entities": "node tools/generate-xhtml-entities.js"}, "gitHead": "dea024fc158259ed513d78c1bb910ce847fd556c", "_id": "esprima@3.0.0", "_shasum": "53cf247acda77313e551c3aa2e73342d3fb4f7d9", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.1", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "dist": {"shasum": "53cf247acda77313e551c3aa2e73342d3fb4f7d9", "tarball": "https://registry.npmjs.org/esprima/-/esprima-3.0.0.tgz", "integrity": "sha512-xoBq/MIShSydNZOkjkoCEjqod963yHNXTLC40ypBhop6yPqflPz/vTinmCfSrGcywVLnSftRf6a0kJLdFdzemw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCRgamvcwPdLC09RQ00p00FFwiHSII3YtES151yYwGg+QIhANX9aR7z4wfVjqZbpDjhYYrf5E8847NwibT0ZYR2OjvU"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/esprima-3.0.0.tgz_1472911974371_0.6553201307542622"}, "directories": {}}, "3.1.0": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "dist/esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "3.1.0", "files": ["bin", "dist/esprima.js"], "engines": {"node": ">=4"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/jquery/esprima.git"}, "bugs": {"url": "https://github.com/jquery/esprima/issues"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"codecov.io": "~0.1.6", "escomplex-js": "1.2.0", "everything.js": "~1.0.3", "glob": "~7.1.0", "istanbul": "~0.4.0", "json-diff": "~0.3.1", "karma": "~1.3.0", "karma-chrome-launcher": "~2.0.0", "karma-detect-browsers": "~2.1.0", "karma-firefox-launcher": "~1.0.0", "karma-ie-launcher": "~1.0.0", "karma-mocha": "~1.2.0", "karma-safari-launcher": "~1.0.0", "karma-sauce-launcher": "~1.0.0", "lodash": "~3.10.1", "mocha": "~3.1.0", "node-tick-processor": "~0.0.2", "regenerate": "~1.3.1", "temp": "~0.8.3", "tslint": "~3.15.1", "typescript": "~1.8.10", "typescript-formatter": "~2.3.0", "unicode-8.0.0": "~0.7.0", "webpack": "~1.13.2"}, "keywords": ["ast", "ecmascript", "esprima", "javascript", "parser", "syntax"], "scripts": {"check-version": "node test/check-version.js", "tslint": "tslint src/*.ts", "code-style": "tsfmt --verify src/*.ts && tsfmt --verify test/*.js", "format-code": "tsfmt -r src/*.ts && tsfmt -r test/*.js", "complexity": "node test/check-complexity.js", "static-analysis": "npm run check-version && npm run tslint && npm run code-style && npm run complexity", "hostile-env-tests": "node test/hostile-environment-tests.js", "unit-tests": "node test/unit-tests.js", "api-tests": "mocha -R dot test/api-tests.js", "grammar-tests": "node test/grammar-tests.js", "regression-tests": "node test/regression-tests.js", "all-tests": "npm run generate-fixtures && npm run unit-tests && npm run api-tests && npm run grammar-tests && npm run regression-tests && npm run hostile-env-tests", "generate-fixtures": "node tools/generate-fixtures.js", "browser-tests": "npm run compile && npm run generate-fixtures && cd test && karma start --single-run", "saucelabs-evergreen": "cd test && karma start saucelabs-evergreen.conf.js", "saucelabs-safari": "cd test && karma start saucelabs-safari.conf.js", "saucelabs-ie": "cd test && karma start saucelabs-ie.conf.js", "saucelabs": "npm run saucelabs-evergreen && npm run saucelabs-ie && npm run saucelabs-safari", "analyze-coverage": "istanbul cover test/unit-tests.js", "check-coverage": "istanbul check-coverage --statement 100 --branch 100 --function 100", "dynamic-analysis": "npm run analyze-coverage && npm run check-coverage", "compile": "tsc -p src/ && webpack && node tools/fixupbundle.js", "test": "npm run compile && npm run all-tests && npm run static-analysis && npm run dynamic-analysis", "prepublish": "npm run compile", "profile": "node --prof test/profile.js && mv isolate*.log v8.log && node-tick-processor", "benchmark-parser": "node -expose_gc test/benchmark-parser.js", "benchmark-tokenizer": "node --expose_gc test/benchmark-tokenizer.js", "benchmark": "npm run benchmark-parser && npm run benchmark-tokenizer", "codecov": "istanbul report cobertura && codecov < ./coverage/cobertura-coverage.xml", "downstream": "node test/downstream.js", "travis": "npm test", "circleci": "npm test && npm run codecov && npm run downstream", "appveyor": "npm run compile && npm run all-tests && npm run browser-tests", "droneio": "npm run compile && npm run all-tests && npm run saucelabs", "generate-regex": "node tools/generate-identifier-regex.js", "generate-xhtml-entities": "node tools/generate-xhtml-entities.js"}, "gitHead": "8411f93b57d868a26eea3430960978bacd0a61c6", "_id": "esprima@3.1.0", "_shasum": "ea6aec30615034b0e8097ab2297ed2d5c887e3c3", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.7.0", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "dist": {"shasum": "ea6aec30615034b0e8097ab2297ed2d5c887e3c3", "tarball": "https://registry.npmjs.org/esprima/-/esprima-3.1.0.tgz", "integrity": "sha512-RGM0d9EzvfTN0+myEMpRCZxklcYpQ9ZChbA7JnH7zk1XUFYxPhBQSIwAPbLIjdIjin7dsIByUGABzb4A7At7fA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEDLh4dU3wKlLp1/kegFU4P7MP3gj8ZtW6O1gfAIGYYQAiBN6XPX70a6hzQP6xgNBzfya+7HmsuP+ugJ7AJ1bdnGAA=="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/esprima-3.1.0.tgz_1476034508777_0.3441668862942606"}, "directories": {}}, "3.1.1": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "dist/esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "3.1.1", "files": ["bin", "dist/esprima.js"], "engines": {"node": ">=4"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/jquery/esprima.git"}, "bugs": {"url": "https://github.com/jquery/esprima/issues"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"codecov.io": "~0.1.6", "escomplex-js": "1.2.0", "everything.js": "~1.0.3", "glob": "~7.1.0", "istanbul": "~0.4.0", "json-diff": "~0.3.1", "karma": "~1.3.0", "karma-chrome-launcher": "~2.0.0", "karma-detect-browsers": "~2.1.0", "karma-firefox-launcher": "~1.0.0", "karma-ie-launcher": "~1.0.0", "karma-mocha": "~1.2.0", "karma-safari-launcher": "~1.0.0", "karma-sauce-launcher": "~1.0.0", "lodash": "~3.10.1", "mocha": "~3.1.0", "node-tick-processor": "~0.0.2", "regenerate": "~1.3.1", "temp": "~0.8.3", "tslint": "~3.15.1", "typescript": "~1.8.10", "typescript-formatter": "~2.3.0", "unicode-8.0.0": "~0.7.0", "webpack": "~1.13.2"}, "keywords": ["ast", "ecmascript", "esprima", "javascript", "parser", "syntax"], "scripts": {"check-version": "node test/check-version.js", "tslint": "tslint src/*.ts", "code-style": "tsfmt --verify src/*.ts && tsfmt --verify test/*.js", "format-code": "tsfmt -r src/*.ts && tsfmt -r test/*.js", "complexity": "node test/check-complexity.js", "static-analysis": "npm run check-version && npm run tslint && npm run code-style && npm run complexity", "hostile-env-tests": "node test/hostile-environment-tests.js", "unit-tests": "node test/unit-tests.js", "api-tests": "mocha -R dot test/api-tests.js", "grammar-tests": "node test/grammar-tests.js", "regression-tests": "node test/regression-tests.js", "all-tests": "npm run generate-fixtures && npm run unit-tests && npm run api-tests && npm run grammar-tests && npm run regression-tests && npm run hostile-env-tests", "generate-fixtures": "node tools/generate-fixtures.js", "browser-tests": "npm run compile && npm run generate-fixtures && cd test && karma start --single-run", "saucelabs-evergreen": "cd test && karma start saucelabs-evergreen.conf.js", "saucelabs-safari": "cd test && karma start saucelabs-safari.conf.js", "saucelabs-ie": "cd test && karma start saucelabs-ie.conf.js", "saucelabs": "npm run saucelabs-evergreen && npm run saucelabs-ie && npm run saucelabs-safari", "analyze-coverage": "istanbul cover test/unit-tests.js", "check-coverage": "istanbul check-coverage --statement 100 --branch 100 --function 100", "dynamic-analysis": "npm run analyze-coverage && npm run check-coverage", "compile": "tsc -p src/ && webpack && node tools/fixupbundle.js", "test": "npm run compile && npm run all-tests && npm run static-analysis && npm run dynamic-analysis", "prepublish": "npm run compile", "profile": "node --prof test/profile.js && mv isolate*.log v8.log && node-tick-processor", "benchmark-parser": "node -expose_gc test/benchmark-parser.js", "benchmark-tokenizer": "node --expose_gc test/benchmark-tokenizer.js", "benchmark": "npm run benchmark-parser && npm run benchmark-tokenizer", "codecov": "istanbul report cobertura && codecov < ./coverage/cobertura-coverage.xml", "downstream": "node test/downstream.js", "travis": "npm test", "circleci": "npm test && npm run codecov && npm run downstream", "appveyor": "npm run compile && npm run all-tests && npm run browser-tests", "droneio": "npm run compile && npm run all-tests && npm run saucelabs", "generate-regex": "node tools/generate-identifier-regex.js", "generate-xhtml-entities": "node tools/generate-xhtml-entities.js"}, "gitHead": "f404c555cd4b15b828538a2cac303d538f639dd2", "_id": "esprima@3.1.1", "_shasum": "02dbcc5ac3ece81070377f99158ec742ab5dda06", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.8.0", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "dist": {"shasum": "02dbcc5ac3ece81070377f99158ec742ab5dda06", "tarball": "https://registry.npmjs.org/esprima/-/esprima-3.1.1.tgz", "integrity": "sha512-0/2iRkE9/8JkFspCZBtK9wD39BgHAsuuQyXdxqwZ6rSBYiloXojbR6HsIF13g2OjAxD7Y/opnmxtNpwGK02p3w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCSonDGGd/QROAqzt5auf3RE8imf4Y/b+/qWlOtwkKpvgIhAOPo1Bwl52coV6oIGcYF/raJDBIjxvi4QGNKZV45YMHv"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/esprima-3.1.1.tgz_1477964071572_0.006474000168964267"}, "directories": {}}, "3.1.2": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "dist/esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "3.1.2", "files": ["bin", "dist/esprima.js"], "engines": {"node": ">=4"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/jquery/esprima.git"}, "bugs": {"url": "https://github.com/jquery/esprima/issues"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"codecov.io": "~0.1.6", "escomplex-js": "1.2.0", "everything.js": "~1.0.3", "glob": "~7.1.0", "istanbul": "~0.4.0", "json-diff": "~0.3.1", "karma": "~1.3.0", "karma-chrome-launcher": "~2.0.0", "karma-detect-browsers": "~2.1.0", "karma-firefox-launcher": "~1.0.0", "karma-ie-launcher": "~1.0.0", "karma-mocha": "~1.2.0", "karma-safari-launcher": "~1.0.0", "karma-sauce-launcher": "~1.0.0", "lodash": "~3.10.1", "mocha": "~3.1.0", "node-tick-processor": "~0.0.2", "regenerate": "~1.3.1", "temp": "~0.8.3", "tslint": "~3.15.1", "typescript": "~1.8.10", "typescript-formatter": "~2.3.0", "unicode-8.0.0": "~0.7.0", "webpack": "~1.13.2"}, "keywords": ["ast", "ecmascript", "esprima", "javascript", "parser", "syntax"], "scripts": {"check-version": "node test/check-version.js", "tslint": "tslint src/*.ts", "code-style": "tsfmt --verify src/*.ts && tsfmt --verify test/*.js", "format-code": "tsfmt -r src/*.ts && tsfmt -r test/*.js", "complexity": "node test/check-complexity.js", "static-analysis": "npm run check-version && npm run tslint && npm run code-style && npm run complexity", "hostile-env-tests": "node test/hostile-environment-tests.js", "unit-tests": "node test/unit-tests.js", "api-tests": "mocha -R dot test/api-tests.js", "grammar-tests": "node test/grammar-tests.js", "regression-tests": "node test/regression-tests.js", "all-tests": "npm run generate-fixtures && npm run unit-tests && npm run api-tests && npm run grammar-tests && npm run regression-tests && npm run hostile-env-tests", "generate-fixtures": "node tools/generate-fixtures.js", "browser-tests": "npm run compile && npm run generate-fixtures && cd test && karma start --single-run", "saucelabs-evergreen": "cd test && karma start saucelabs-evergreen.conf.js", "saucelabs-safari": "cd test && karma start saucelabs-safari.conf.js", "saucelabs-ie": "cd test && karma start saucelabs-ie.conf.js", "saucelabs": "npm run saucelabs-evergreen && npm run saucelabs-ie && npm run saucelabs-safari", "analyze-coverage": "istanbul cover test/unit-tests.js", "check-coverage": "istanbul check-coverage --statement 100 --branch 100 --function 100", "dynamic-analysis": "npm run analyze-coverage && npm run check-coverage", "compile": "tsc -p src/ && webpack && node tools/fixupbundle.js", "test": "npm run compile && npm run all-tests && npm run static-analysis && npm run dynamic-analysis", "prepublish": "npm run compile", "profile": "node --prof test/profile.js && mv isolate*.log v8.log && node-tick-processor", "benchmark-parser": "node -expose_gc test/benchmark-parser.js", "benchmark-tokenizer": "node --expose_gc test/benchmark-tokenizer.js", "benchmark": "npm run benchmark-parser && npm run benchmark-tokenizer", "codecov": "istanbul report cobertura && codecov < ./coverage/cobertura-coverage.xml", "downstream": "node test/downstream.js", "travis": "npm test", "circleci": "npm test && npm run codecov && npm run downstream", "appveyor": "npm run compile && npm run all-tests && npm run browser-tests", "droneio": "npm run compile && npm run all-tests && npm run saucelabs", "generate-regex": "node tools/generate-identifier-regex.js", "generate-xhtml-entities": "node tools/generate-xhtml-entities.js"}, "gitHead": "23501af8af5721fe1f113e54dcf029cbbcb75bae", "_id": "esprima@3.1.2", "_shasum": "954b5d19321ca436092fa90f06d6798531fe8184", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "7.0.0", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "dist": {"shasum": "954b5d19321ca436092fa90f06d6798531fe8184", "tarball": "https://registry.npmjs.org/esprima/-/esprima-3.1.2.tgz", "integrity": "sha512-X05fhUa1fuYLRHbXsVcYLbY1jF3kRUj8wI2H2rBQazcndYmg2C1sBQgoHlQ2hfrp2GTnauBegP7WN4FHqzmUbw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC7zaypnISgkK/Kp8uIfD/oeULpaA41XfPPgYcGQFeZAAIgDdVIFz0vg5TXFHetorunRd2A3ShKHIsz2D8cpmDhLTM="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/esprima-3.1.2.tgz_1480001572241_0.9349541799165308"}, "directories": {}}, "3.1.3": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "dist/esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "3.1.3", "files": ["bin", "dist/esprima.js"], "engines": {"node": ">=4"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/jquery/esprima.git"}, "bugs": {"url": "https://github.com/jquery/esprima/issues"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"codecov.io": "~0.1.6", "escomplex-js": "1.2.0", "everything.js": "~1.0.3", "glob": "~7.1.0", "istanbul": "~0.4.0", "json-diff": "~0.3.1", "karma": "~1.3.0", "karma-chrome-launcher": "~2.0.0", "karma-detect-browsers": "~2.1.0", "karma-firefox-launcher": "~1.0.0", "karma-ie-launcher": "~1.0.0", "karma-mocha": "~1.2.0", "karma-safari-launcher": "~1.0.0", "karma-sauce-launcher": "~1.0.0", "lodash": "~3.10.1", "mocha": "~3.1.0", "node-tick-processor": "~0.0.2", "regenerate": "~1.3.1", "temp": "~0.8.3", "tslint": "~3.15.1", "typescript": "~1.8.10", "typescript-formatter": "~2.3.0", "unicode-8.0.0": "~0.7.0", "webpack": "~1.13.2"}, "keywords": ["ast", "ecmascript", "esprima", "javascript", "parser", "syntax"], "scripts": {"check-version": "node test/check-version.js", "tslint": "tslint src/*.ts", "code-style": "tsfmt --verify src/*.ts && tsfmt --verify test/*.js", "format-code": "tsfmt -r src/*.ts && tsfmt -r test/*.js", "complexity": "node test/check-complexity.js", "static-analysis": "npm run check-version && npm run tslint && npm run code-style && npm run complexity", "hostile-env-tests": "node test/hostile-environment-tests.js", "unit-tests": "node test/unit-tests.js", "api-tests": "mocha -R dot test/api-tests.js", "grammar-tests": "node test/grammar-tests.js", "regression-tests": "node test/regression-tests.js", "all-tests": "npm run generate-fixtures && npm run unit-tests && npm run api-tests && npm run grammar-tests && npm run regression-tests && npm run hostile-env-tests", "generate-fixtures": "node tools/generate-fixtures.js", "browser-tests": "npm run compile && npm run generate-fixtures && cd test && karma start --single-run", "saucelabs-evergreen": "cd test && karma start saucelabs-evergreen.conf.js", "saucelabs-safari": "cd test && karma start saucelabs-safari.conf.js", "saucelabs-ie": "cd test && karma start saucelabs-ie.conf.js", "saucelabs": "npm run saucelabs-evergreen && npm run saucelabs-ie && npm run saucelabs-safari", "analyze-coverage": "istanbul cover test/unit-tests.js", "check-coverage": "istanbul check-coverage --statement 100 --branch 100 --function 100", "dynamic-analysis": "npm run analyze-coverage && npm run check-coverage", "compile": "tsc -p src/ && webpack && node tools/fixupbundle.js", "test": "npm run compile && npm run all-tests && npm run static-analysis && npm run dynamic-analysis", "prepublish": "npm run compile", "profile": "node --prof test/profile.js && mv isolate*.log v8.log && node-tick-processor", "benchmark-parser": "node -expose_gc test/benchmark-parser.js", "benchmark-tokenizer": "node --expose_gc test/benchmark-tokenizer.js", "benchmark": "npm run benchmark-parser && npm run benchmark-tokenizer", "codecov": "istanbul report cobertura && codecov < ./coverage/cobertura-coverage.xml", "downstream": "node test/downstream.js", "travis": "npm test", "circleci": "npm test && npm run codecov && npm run downstream", "appveyor": "npm run compile && npm run all-tests && npm run browser-tests", "droneio": "npm run compile && npm run all-tests && npm run saucelabs", "generate-regex": "node tools/generate-identifier-regex.js", "generate-xhtml-entities": "node tools/generate-xhtml-entities.js"}, "gitHead": "cd5909280f363d503142cb79077ec532132d9749", "_id": "esprima@3.1.3", "_shasum": "fdca51cee6133895e3c88d535ce49dbff62a4633", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "7.1.0", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "dist": {"shasum": "fdca51cee6133895e3c88d535ce49dbff62a4633", "tarball": "https://registry.npmjs.org/esprima/-/esprima-3.1.3.tgz", "integrity": "sha512-AWwVMNxwhN8+NIPQzAQZCm7RkLC4RbM3B1OobMuyp3i+w73X57KCKaVIxaRZb+DYCojq7rspo+fmuQfAboyhFg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCB9RdZ3ys8Frwurii7ORAmU9EEcII/YnFKtE5GpqZ0kwIgd8QxNmaac9a1+yubRVxaRa67AG6cjwnQcqzeKjA5IY4="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/esprima-3.1.3.tgz_1482463104044_0.19027737597934902"}, "directories": {}}, "4.0.0": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "dist/esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "4.0.0", "files": ["bin", "dist/esprima.js"], "engines": {"node": ">=4"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/jquery/esprima.git"}, "bugs": {"url": "https://github.com/jquery/esprima/issues"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"codecov.io": "~0.1.6", "escomplex-js": "1.2.0", "everything.js": "~1.0.3", "glob": "~7.1.0", "istanbul": "~0.4.0", "json-diff": "~0.3.1", "karma": "~1.3.0", "karma-chrome-launcher": "~2.0.0", "karma-detect-browsers": "~2.2.3", "karma-edge-launcher": "~0.2.0", "karma-firefox-launcher": "~1.0.0", "karma-ie-launcher": "~1.0.0", "karma-mocha": "~1.3.0", "karma-safari-launcher": "~1.0.0", "karma-safaritechpreview-launcher": "~0.0.4", "karma-sauce-launcher": "~1.1.0", "lodash": "~3.10.1", "mocha": "~3.2.0", "node-tick-processor": "~0.0.2", "regenerate": "~1.3.2", "temp": "~0.8.3", "tslint": "~5.1.0", "typescript": "~2.3.2", "typescript-formatter": "~5.1.3", "unicode-8.0.0": "~0.7.0", "webpack": "~1.14.0"}, "keywords": ["ast", "ecmascript", "esprima", "javascript", "parser", "syntax"], "scripts": {"check-version": "node test/check-version.js", "tslint": "tslint src/*.ts", "code-style": "tsfmt --verify src/*.ts && tsfmt --verify test/*.js", "format-code": "tsfmt -r src/*.ts && tsfmt -r test/*.js", "complexity": "node test/check-complexity.js", "static-analysis": "npm run check-version && npm run tslint && npm run code-style && npm run complexity", "hostile-env-tests": "node test/hostile-environment-tests.js", "unit-tests": "node test/unit-tests.js", "api-tests": "mocha -R dot test/api-tests.js", "grammar-tests": "node test/grammar-tests.js", "regression-tests": "node test/regression-tests.js", "all-tests": "npm run verify-line-ending && npm run generate-fixtures && npm run unit-tests && npm run api-tests && npm run grammar-tests && npm run regression-tests && npm run hostile-env-tests", "verify-line-ending": "node test/verify-line-ending.js", "generate-fixtures": "node tools/generate-fixtures.js", "browser-tests": "npm run compile && npm run generate-fixtures && cd test && karma start --single-run", "saucelabs-evergreen": "cd test && karma start saucelabs-evergreen.conf.js", "saucelabs-safari": "cd test && karma start saucelabs-safari.conf.js", "saucelabs-ie": "cd test && karma start saucelabs-ie.conf.js", "saucelabs": "npm run saucelabs-evergreen && npm run saucelabs-ie && npm run saucelabs-safari", "analyze-coverage": "istanbul cover test/unit-tests.js", "check-coverage": "istanbul check-coverage --statement 100 --branch 100 --function 100", "dynamic-analysis": "npm run analyze-coverage && npm run check-coverage", "compile": "tsc -p src/ && webpack && node tools/fixupbundle.js", "test": "npm run compile && npm run all-tests && npm run static-analysis && npm run dynamic-analysis", "prepublish": "npm run compile", "profile": "node --prof test/profile.js && mv isolate*.log v8.log && node-tick-processor", "benchmark-parser": "node -expose_gc test/benchmark-parser.js", "benchmark-tokenizer": "node --expose_gc test/benchmark-tokenizer.js", "benchmark": "npm run benchmark-parser && npm run benchmark-tokenizer", "codecov": "istanbul report cobertura && codecov < ./coverage/cobertura-coverage.xml", "downstream": "node test/downstream.js", "travis": "npm test", "circleci": "npm test && npm run codecov && npm run downstream", "appveyor": "npm run compile && npm run all-tests && npm run browser-tests", "droneio": "npm run compile && npm run all-tests && npm run saucelabs", "generate-regex": "node tools/generate-identifier-regex.js", "generate-xhtml-entities": "node tools/generate-xhtml-entities.js"}, "gitHead": "56c0f0f7248c8611cb55cb97ad089cb86cf8ddb3", "_id": "esprima@4.0.0", "_npmVersion": "5.0.0", "_nodeVersion": "8.0.0", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-oftTcaMu/EGrEIu904mWteKIv8vMuOgGYo7EhVJJN00R/EED9DCua/xxHRdYnKtcECzVg7xOWhflvJMnqcFZjw==", "shasum": "4499eddcd1110e0b218bacf2fa7f7f59f55ca804", "tarball": "https://registry.npmjs.org/esprima/-/esprima-4.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB6iMUaJIWqJ2xEHNw9c5qo1XaAWLZk6rOp2SpHn4YqgAiEAn3kSkQ3POzo473y2i5XuruqhdlWploI1YmPKI5p0mzU="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/esprima-4.0.0.tgz_1497095776705_0.45635089301504195"}, "directories": {}}, "4.0.1": {"name": "esprima", "description": "ECMAScript parsing infrastructure for multipurpose analysis", "homepage": "http://esprima.org", "main": "dist/esprima.js", "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "version": "4.0.1", "files": ["bin", "dist/esprima.js"], "engines": {"node": ">=4"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+https://github.com/jquery/esprima.git"}, "bugs": {"url": "https://github.com/jquery/esprima/issues"}, "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"codecov.io": "~0.1.6", "escomplex-js": "1.2.0", "everything.js": "~1.0.3", "glob": "~7.1.0", "istanbul": "~0.4.0", "json-diff": "~0.3.1", "karma": "~1.3.0", "karma-chrome-launcher": "~2.0.0", "karma-detect-browsers": "~2.2.3", "karma-edge-launcher": "~0.2.0", "karma-firefox-launcher": "~1.0.0", "karma-ie-launcher": "~1.0.0", "karma-mocha": "~1.3.0", "karma-safari-launcher": "~1.0.0", "karma-safaritechpreview-launcher": "~0.0.4", "karma-sauce-launcher": "~1.1.0", "lodash": "~3.10.1", "mocha": "~3.2.0", "node-tick-processor": "~0.0.2", "regenerate": "~1.3.2", "temp": "~0.8.3", "tslint": "~5.1.0", "typescript": "~2.3.2", "typescript-formatter": "~5.1.3", "unicode-8.0.0": "~0.7.0", "webpack": "~1.14.0"}, "keywords": ["ast", "ecmascript", "esprima", "javascript", "parser", "syntax"], "scripts": {"check-version": "node test/check-version.js", "tslint": "tslint src/*.ts", "code-style": "tsfmt --verify src/*.ts && tsfmt --verify test/*.js", "format-code": "tsfmt -r src/*.ts && tsfmt -r test/*.js", "complexity": "node test/check-complexity.js", "static-analysis": "npm run check-version && npm run tslint && npm run code-style && npm run complexity", "hostile-env-tests": "node test/hostile-environment-tests.js", "unit-tests": "node test/unit-tests.js", "api-tests": "mocha -R dot test/api-tests.js", "grammar-tests": "node test/grammar-tests.js", "regression-tests": "node test/regression-tests.js", "all-tests": "npm run verify-line-ending && npm run generate-fixtures && npm run unit-tests && npm run api-tests && npm run grammar-tests && npm run regression-tests && npm run hostile-env-tests", "verify-line-ending": "node test/verify-line-ending.js", "generate-fixtures": "node tools/generate-fixtures.js", "browser-tests": "npm run compile && npm run generate-fixtures && cd test && karma start --single-run", "saucelabs-evergreen": "cd test && karma start saucelabs-evergreen.conf.js", "saucelabs-safari": "cd test && karma start saucelabs-safari.conf.js", "saucelabs-ie": "cd test && karma start saucelabs-ie.conf.js", "saucelabs": "npm run saucelabs-evergreen && npm run saucelabs-ie && npm run saucelabs-safari", "analyze-coverage": "istanbul cover test/unit-tests.js", "check-coverage": "istanbul check-coverage --statement 100 --branch 100 --function 100", "dynamic-analysis": "npm run analyze-coverage && npm run check-coverage", "compile": "tsc -p src/ && webpack && node tools/fixupbundle.js", "test": "npm run compile && npm run all-tests && npm run static-analysis && npm run dynamic-analysis", "prepublish": "npm run compile", "profile": "node --prof test/profile.js && mv isolate*.log v8.log && node-tick-processor", "benchmark-parser": "node -expose_gc test/benchmark-parser.js", "benchmark-tokenizer": "node --expose_gc test/benchmark-tokenizer.js", "benchmark": "npm run benchmark-parser && npm run benchmark-tokenizer", "codecov": "istanbul report cobertura && codecov < ./coverage/cobertura-coverage.xml", "downstream": "node test/downstream.js", "travis": "npm test", "circleci": "npm test && npm run codecov && npm run downstream", "appveyor": "npm run compile && npm run all-tests && npm run browser-tests", "droneio": "npm run compile && npm run all-tests && npm run saucelabs", "generate-regex": "node tools/generate-identifier-regex.js", "generate-xhtml-entities": "node tools/generate-xhtml-entities.js"}, "gitHead": "0ed4b8afdf2abd416f738977672c6232836f410a", "_id": "esprima@4.0.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "ariya", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==", "shasum": "13b04cdb3e6c5d19df91ab6987a8695619b0aa71", "tarball": "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz", "fileCount": 7, "unpackedSize": 314361, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbSGUyCRA9TVsSAnZWagAAEWcP/2DbPmjJx0Z5u0OIDmkU\nw2GTt6y67+KtMSLt2ot5G1acmLJQbRq88pU95swl4rjoOELqJgamPzhaNYal\nA1wvGi3lwUNYqM2T5vY15Ye6K/+OkMAVxSQ6/L5qvg1U4atoj6uwapwIuoeK\nmvPPuYZX4FvaDTciVUPpFYqaRi5JWBImA0qfXlhhCnhdAd7DkeaLd+z18E7h\n9q0PPm2gtmDGJUDXwcJJzrhKSfQIw4ofcWwheaGJnlRzy2CEV2jUe90jdAH+\nv/jXFxCG1tWg4OZDSfqQfSx8uD7eSVeunsIz6AcNmh7dUHZI6S6AT47itPKm\nYeZoTwhPbJJxG7yZ6EVrItONOeuyghYekQpkDq3leEUl1FZbh3We7/yZSnbv\nrpHonlSaFLniH2RB1NBhP2qXe235bLXxcDt45PgBpqHnBJXoZLWKopH1+7z7\nyc2+DwxmhBm6WC9IjqknRi12JceLMJqThHUkXi2TST5PPUAwpiqgzCzJRvbt\nV/KOI8/Ex6QAbjkqLIfEHBVa7nrdGAagVmFoqV3ZUxtRsb0qAUAsS/XGOVwv\nrjR3iqJsZZmNXzt2Bz+bwwXIdBXnPsZFc8b+FCoRk+1RG/WYA4NDcB1PYaJn\nbMaOD+wr8Mym5PSAlg23ujBquISQlVILppKemTi9m091GUR+d/C1SYa1uAJX\nZk8+\r\n=ke5k\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGs4CVmbB0r5JS31tgOUzl/LIt8Skau/M7QFRDWyKfDyAiAye0DDJfNYhHUTMEQY9/NNLczNGOgSWRX/+0r7WuDeJQ=="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/esprima_4.0.1_1531471154575_0.7747545493259598"}, "_hasShrinkwrap": false}}, "readme": "[![NPM version](https://img.shields.io/npm/v/esprima.svg)](https://www.npmjs.com/package/esprima)\n[![npm download](https://img.shields.io/npm/dm/esprima.svg)](https://www.npmjs.com/package/esprima)\n[![Build Status](https://img.shields.io/travis/jquery/esprima/master.svg)](https://travis-ci.org/jquery/esprima)\n[![Coverage Status](https://img.shields.io/codecov/c/github/jquery/esprima/master.svg)](https://codecov.io/github/jquery/esprima)\n\n**Esprima** ([esprima.org](http://esprima.org), BSD license) is a high performance,\nstandard-compliant [ECMAScript](http://www.ecma-international.org/publications/standards/Ecma-262.htm)\nparser written in ECMAScript (also popularly known as\n[JavaScript](https://en.wikipedia.org/wiki/JavaScript)).\nEsprima is created and maintained by [<PERSON><PERSON>](https://twitter.com/ariyahidayat),\nwith the help of [many contributors](https://github.com/jquery/esprima/contributors).\n\n### Features\n\n- Full support for ECMAScript 2017 ([ECMA-262 8th Edition](http://www.ecma-international.org/publications/standards/Ecma-262.htm))\n- Sensible [syntax tree format](https://github.com/estree/estree/blob/master/es5.md) as standardized by [ESTree project](https://github.com/estree/estree)\n- Experimental support for [JSX](https://facebook.github.io/jsx/), a syntax extension for [React](https://facebook.github.io/react/)\n- Optional tracking of syntax node location (index-based and line-column)\n- [Heavily tested](http://esprima.org/test/ci.html) (~1500 [unit tests](https://github.com/jquery/esprima/tree/master/test/fixtures) with [full code coverage](https://codecov.io/github/jquery/esprima))\n\n### API\n\nEsprima can be used to perform [lexical analysis](https://en.wikipedia.org/wiki/Lexical_analysis) (tokenization) or [syntactic analysis](https://en.wikipedia.org/wiki/Parsing) (parsing) of a JavaScript program.\n\nA simple example on Node.js REPL:\n\n```javascript\n> var esprima = require('esprima');\n> var program = 'const answer = 42';\n\n> esprima.tokenize(program);\n[ { type: 'Keyword', value: 'const' },\n  { type: 'Identifier', value: 'answer' },\n  { type: 'Punctuator', value: '=' },\n  { type: 'Numeric', value: '42' } ]\n  \n> esprima.parseScript(program);\n{ type: 'Program',\n  body:\n   [ { type: 'VariableDeclaration',\n       declarations: [Object],\n       kind: 'const' } ],\n  sourceType: 'script' }\n```\n\nFor more information, please read the [complete documentation](http://esprima.org/doc).", "maintainers": [{"name": "ariya", "email": "<EMAIL>"}], "time": {"modified": "2023-04-30T00:33:23.236Z", "created": "2011-11-25T05:45:39.271Z", "0.7.0": "2011-11-25T05:45:41.118Z", "0.8.0": "2011-11-27T19:59:06.359Z", "0.8.1": "2011-11-30T15:43:01.939Z", "0.8.2": "2011-12-01T16:40:18.907Z", "0.9.0": "2011-12-02T16:10:54.419Z", "0.9.1": "2011-12-03T18:34:53.006Z", "0.9.2": "2011-12-05T15:24:51.970Z", "0.9.3": "2011-12-09T06:36:24.578Z", "0.9.4": "2011-12-13T16:10:32.001Z", "0.9.5": "2011-12-22T02:42:29.051Z", "0.9.6": "2012-01-01T19:45:58.344Z", "0.9.7": "2012-01-23T04:01:09.485Z", "0.9.8": "2012-02-16T15:20:36.451Z", "0.9.9": "2012-03-29T05:55:52.820Z", "1.0.0": "2012-10-22T09:20:29.359Z", "1.0.1": "2012-10-29T02:57:27.792Z", "1.0.2": "2012-11-06T06:06:52.547Z", "1.0.3": "2013-05-17T14:51:52.964Z", "1.0.4": "2013-08-29T05:24:39.624Z", "1.1.0": "2014-03-24T15:10:54.011Z", "1.1.1": "2014-03-26T15:21:42.488Z", "1.2.0": "2014-04-30T05:11:57.910Z", "1.2.1": "2014-05-04T18:39:50.836Z", "1.2.2": "2014-05-19T06:35:59.891Z", "1.2.3": "2015-01-18T18:19:56.686Z", "1.2.4": "2015-02-05T16:58:05.039Z", "2.0.0": "2015-02-06T08:28:39.786Z", "1.2.5": "2015-03-03T16:32:43.830Z", "2.1.0": "2015-03-09T16:31:55.960Z", "2.2.0": "2015-04-17T13:44:52.729Z", "2.3.0": "2015-06-17T06:06:13.513Z", "2.4.0": "2015-06-26T08:50:02.645Z", "2.4.1": "2015-07-01T07:37:50.207Z", "2.5.0": "2015-08-01T07:37:47.437Z", "2.6.0": "2015-09-01T13:39:17.351Z", "2.7.0": "2015-10-22T13:37:16.314Z", "2.7.1": "2015-12-10T15:56:54.946Z", "2.7.2": "2016-02-03T05:27:56.817Z", "2.7.3": "2016-08-24T04:40:05.981Z", "3.0.0": "2016-09-03T14:12:54.637Z", "3.1.0": "2016-10-09T17:35:10.436Z", "3.1.1": "2016-11-01T01:34:31.812Z", "3.1.2": "2016-11-24T15:32:54.356Z", "3.1.3": "2016-12-23T03:18:26.259Z", "4.0.0": "2017-06-10T11:56:16.911Z", "4.0.1": "2018-07-13T08:39:14.711Z"}, "repository": {"type": "git", "url": "git+https://github.com/jquery/esprima.git"}, "users": {"6174": true, "fgribreau": true, "isao": true, "freethenation": true, "medikoo": true, "millermedeiros": true, "charmander": true, "hij1nx": true, "kastor": true, "kriskowal": true, "mathias": true, "mk": true, "blakeembrey": true, "delapouite": true, "apk": true, "elmasse": true, "tristen": true, "davepoon": true, "fanchangyong": true, "kareemamin": true, "oceanswave": true, "themiddleman": true, "mastayoda": true, "yashprit": true, "noyobo": true, "alexbaumgertner": true, "fentas": true, "decoded": true, "nice_body": true, "panlw": true, "subchen": true, "roryrjb": true, "djamseed": true, "wdk": true, "tobiasnickel": true, "derflatulator": true, "maninbucket": true, "eserozvataf": true, "nickeltobias": true, "cognivator": true, "grreenzz": true, "suryagh": true, "liu946": true, "suissa": true, "arleytriana": true, "kkuehl": true, "eklem": true, "isenricho": true, "marseilledev": true, "rsp": true, "suddi": true, "shuoshubao": true, "thewhiterabbit": true, "grandsong": true, "tsxuehu": true, "tedyhy": true, "brentlintner": true, "rakeshmakam": true, "krickray": true, "raysix900202": true, "aidenzou": true, "chengxiao": true, "diegorbaquero": true, "grabantot": true, "heartnett": true, "omkar.sheral.1989": true, "tuanvv": true, "zhangaz1": true, "gkodes": true, "brainmurder": true, "xiaobing": true, "xinwangwang": true, "flumpus-dev": true}, "readmeFilename": "README.md", "homepage": "http://esprima.org", "keywords": ["ast", "ecmascript", "esprima", "javascript", "parser", "syntax"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jquery/esprima/issues"}, "license": "BSD-2-<PERSON><PERSON>"}