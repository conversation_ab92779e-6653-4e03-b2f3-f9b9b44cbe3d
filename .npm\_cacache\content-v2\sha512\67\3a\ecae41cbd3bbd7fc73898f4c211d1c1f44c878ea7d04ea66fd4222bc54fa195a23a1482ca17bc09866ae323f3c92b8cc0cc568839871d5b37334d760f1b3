{"_id": "require-directory", "_rev": "63-b8090da430c3acf202ecea2df489b2da", "name": "require-directory", "description": "Recursively iterates over specified directory, require()'ing each file, and returning a nested hash structure containing those modules.", "dist-tags": {"latest": "2.1.1"}, "versions": {"0.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}, "name": "require-directory", "version": "0.0.1", "description": "Recursively iterates over specified directory, requiring each file, and returning a nested hash structure containing those libraries.", "keywords": ["require", "directory", "library", "recursive"], "homepage": "https://github.com/troygoode/node-require-directory/", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/troygoode/node-require-directory.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}], "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "bugs": {"url": "http://github.com/troygoode/node-require-directory/issues/"}, "engines": {"node": ">=0.6.0"}, "dependencies": {}, "devDependencies": {}, "scripts": {}, "bin": {}, "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "_id": "require-directory@0.0.1", "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.6", "_defaultsLoaded": true, "dist": {"shasum": "db2dd671d72cfbc13d99e33cf2cc7de942e4cb4b", "tarball": "https://registry.npmjs.org/require-directory/-/require-directory-0.0.1.tgz", "integrity": "sha512-UB8K8laVN1FTUlf2eZtoFd7k3yrFcyv0LYgbXfCipHryGH/oMyxyT8oMDl3YSS3kBI/LWayU7Wg6mNNuRuTE4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCCOPh1FfVymiA8lF4e+lWYgVMx6nS9q3jRup+pMjkb+QIgOSijNN/m+rxDsuddA7l0d7bihWA8EfaE63HwIlHnWnA="}]}, "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "directories": {}}, "0.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}, "name": "require-directory", "version": "0.0.2", "description": "Recursively iterates over specified directory, requiring each file, and returning a nested hash structure containing those libraries.", "keywords": ["require", "directory", "library", "recursive"], "homepage": "https://github.com/troygoode/node-require-directory/", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/troygoode/node-require-directory.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}], "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "bugs": {"url": "http://github.com/troygoode/node-require-directory/issues/"}, "engines": {"node": ">=0.6.0"}, "dependencies": {}, "devDependencies": {}, "scripts": {}, "bin": {}, "_id": "require-directory@0.0.2", "dist": {"shasum": "86f04926d03b50a5bf3143ea23701888d9e8b3e4", "tarball": "https://registry.npmjs.org/require-directory/-/require-directory-0.0.2.tgz", "integrity": "sha512-mJNRkbf8IXrsLAC1WKxpp+uk84zPy8VYckxiVuEE1ozNem5KmGMwsj0UhSJl624a/FXNIJNGGVsYJySXNy8PTA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDa2WQZG8XEWgH3dl87She/TANcgbFYJpvnoAL5dVFywAIhAJaSK6RXwfX4NTK8aouukJP1lXxBD+J5b585r6fd6ND5"}]}, "_npmVersion": "1.1.66", "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "directories": {}}, "0.0.3": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}, "name": "require-directory", "version": "0.0.3", "description": "Recursively iterates over specified directory, requiring each file, and returning a nested hash structure containing those libraries.", "keywords": ["require", "directory", "library", "recursive"], "homepage": "https://github.com/troygoode/node-require-directory/", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/troygoode/node-require-directory.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}], "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "bugs": {"url": "http://github.com/troygoode/node-require-directory/issues/"}, "engines": {"node": ">=0.6.0"}, "dependencies": {}, "devDependencies": {"mocha": "latest"}, "scripts": {"test": "./node_modules/mocha/bin/mocha"}, "bin": {}, "_id": "require-directory@0.0.3", "dist": {"shasum": "d36fcc775f7595e79d7f904ff01b112b2d9c2b69", "tarball": "https://registry.npmjs.org/require-directory/-/require-directory-0.0.3.tgz", "integrity": "sha512-rBv5vK1byNJTsjQ1B66CLvgjjQrczHV6qxecRLhLa2BK91zDcNIit7D8kv4etwoyqV1xM/th8ZJj1o/tqFiWSg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCjMdrRy+45+Q7hoibOyWuOpEd/aschwBKAmTIgcayh7QIhAJPf1Id3mGzOiZUkjNOy+lwOcu5maEw5V5EN2o2Bcf5D"}]}, "_npmVersion": "1.1.66", "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}, "name": "require-directory", "version": "1.0.0", "description": "Recursively iterates over specified directory, requiring each file, and returning a nested hash structure containing those libraries.", "keywords": ["require", "directory", "library", "recursive"], "homepage": "https://github.com/troygoode/node-require-directory/", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/troygoode/node-require-directory.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}], "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "bugs": {"url": "http://github.com/troygoode/node-require-directory/issues/"}, "engines": {"node": ">=0.6.0"}, "dependencies": {}, "devDependencies": {"mocha": "latest"}, "scripts": {"test": "./node_modules/mocha/bin/mocha"}, "bin": {}, "_id": "require-directory@1.0.0", "dist": {"shasum": "3b53b180a92ed52869a50fecddf5d962bb04e951", "tarball": "https://registry.npmjs.org/require-directory/-/require-directory-1.0.0.tgz", "integrity": "sha512-kCwxwugsl4QBGtc8eRIblDQunAkyrA5fNRBiAFG/fAHfJ9dkfnEJCOFuqD+jruGZKx0nqyNFBuf3dShKFZngFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGUsYJpJjVHYh6OjQ6++Q+/MCYIEdkamXvXLvm/JcUC7AiEA227cTTbaimFi/zdSrjE6l66L++U5LsMAD1wiUKfAAYM="}]}, "_npmVersion": "1.1.59", "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "directories": {}}, "1.0.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}, "name": "require-directory", "version": "1.0.1", "description": "Recursively iterates over specified directory, requiring each file, and returning a nested hash structure containing those libraries.", "keywords": ["require", "directory", "library", "recursive"], "homepage": "https://github.com/troygoode/node-require-directory/", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/troygoode/node-require-directory.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}], "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "bugs": {"url": "http://github.com/troygoode/node-require-directory/issues/"}, "engines": {"node": ">=0.6.0"}, "dependencies": {}, "devDependencies": {"mocha": "latest"}, "scripts": {"test": "./node_modules/mocha/bin/mocha"}, "bin": {}, "_id": "require-directory@1.0.1", "dist": {"shasum": "19c41dac1aeacdc274801ba24166aadde7af4823", "tarball": "https://registry.npmjs.org/require-directory/-/require-directory-1.0.1.tgz", "integrity": "sha512-l5/EK51+3RXxWjsHcGfh+GfqLMIaPbjsnRF4xSWcnMY2uz1lyRAVWOXadjga4g4yOiGxx1ZoDktCl6oFamh64g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQClSh8dKsO5y5MoNQv7VhbzLx/3UF5wg7FB3S/wCEI5NQIhAIhPHpKjXprxTkL7klT4c4L5ITrnlwvtMSf5O1mHM+Kf"}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "directories": {}}, "1.0.2": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}, "name": "require-directory", "version": "1.0.2", "description": "Recursively iterates over specified directory, requiring each file, and returning a nested hash structure containing those libraries.", "keywords": ["require", "directory", "library", "recursive"], "homepage": "https://github.com/troygoode/node-require-directory/", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/troygoode/node-require-directory.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}], "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "bugs": {"url": "http://github.com/troygoode/node-require-directory/issues/"}, "engines": {"node": ">=0.6.0"}, "dependencies": {}, "devDependencies": {"mocha": "latest"}, "scripts": {"test": "./node_modules/mocha/bin/mocha"}, "bin": {}, "_id": "require-directory@1.0.2", "dist": {"shasum": "3c150e9fbbc6b6d634b65a0cecd89b6f8a9bd5d8", "tarball": "https://registry.npmjs.org/require-directory/-/require-directory-1.0.2.tgz", "integrity": "sha512-AplYOAC4jqb5HCqU/2Kn2B31vub2Kqdj2rpIeK102FBViZr+gcMK5JCG9y5RPGeG57cPgdfa1EeQhp56eG5mwQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDYTWAsKpZhgixA5rXkSRAvclaErKcrpG3tDWPRmz2ocAiEApYUI06ni+VAYqYmvoGzWqJsQhy4oq6ope2rwosyP6Y0="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "directories": {}}, "1.1.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}, "name": "require-directory", "version": "1.1.0", "description": "Recursively iterates over specified directory, requiring each file, and returning a nested hash structure containing those libraries.", "keywords": ["require", "directory", "library", "recursive"], "homepage": "https://github.com/troygoode/node-require-directory/", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/troygoode/node-require-directory.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}], "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "bugs": {"url": "http://github.com/troygoode/node-require-directory/issues/"}, "engines": {"node": ">=0.6.0"}, "dependencies": {}, "devDependencies": {"mocha": "latest"}, "scripts": {"test": "./node_modules/mocha/bin/mocha"}, "bin": {}, "_id": "require-directory@1.1.0", "dist": {"shasum": "70bb3fe783c44b24a49eae3ef23420d54ea3484c", "tarball": "https://registry.npmjs.org/require-directory/-/require-directory-1.1.0.tgz", "integrity": "sha512-J2IgynmriRYOlTjCSO7akEXkQK0qyluFd/arKEkA0DiFG2e3H9CL8NORImTxksoiK7h9ZH59om7ugkZkWx1NWg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBVInQlJo5ZfyBp3byA92gOqk5TJdYrxBrfBiyYIcUTTAiBuecXMguOfEroMWo9uyFMBIAXwAk2bVEVkocdVQsOA8w=="}]}, "_from": ".", "_npmVersion": "1.2.18", "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "directories": {}}, "1.1.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}, "name": "require-directory", "version": "1.1.1", "description": "Recursively iterates over specified directory, requiring each file, and returning a nested hash structure containing those libraries.", "keywords": ["require", "directory", "library", "recursive"], "homepage": "https://github.com/troygoode/node-require-directory/", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/troygoode/node-require-directory.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}], "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "bugs": {"url": "http://github.com/troygoode/node-require-directory/issues/"}, "engines": {"node": ">=0.6.0"}, "dependencies": {}, "devDependencies": {"mocha": "latest"}, "scripts": {"test": "./node_modules/mocha/bin/mocha"}, "bin": {}, "_id": "require-directory@1.1.1", "dist": {"shasum": "43b23b76d00ce37ed1787af33dd36d78103efdcd", "tarball": "https://registry.npmjs.org/require-directory/-/require-directory-1.1.1.tgz", "integrity": "sha512-G99S4zD/A4+ZpicEQrYihQwzcGvHISMd9RGUu2vEzniz8DnBjL95zPa34Mk/60JQ3M0E5EW36vTPa0gPVPInjA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBT7LmwS3HPpnCGpKqCk1pQz6iliPMMB1BNiAk2hHVVPAiBiN7UJ1DDPQlXJGZv09WMtAQMHX17lTd1lzb2mekhnow=="}]}, "_from": ".", "_npmVersion": "1.3.8", "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "directories": {}}, "1.2.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}, "name": "require-directory", "version": "1.2.0", "description": "Recursively iterates over specified directory, requiring each file, and returning a nested hash structure containing those libraries.", "keywords": ["require", "directory", "library", "recursive"], "homepage": "https://github.com/troygoode/node-require-directory/", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/troygoode/node-require-directory.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}], "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "bugs": {"url": "http://github.com/troygoode/node-require-directory/issues/"}, "engines": {"node": ">=0.6.0"}, "dependencies": {}, "devDependencies": {"mocha": "latest"}, "scripts": {"test": "./node_modules/mocha/bin/mocha"}, "bin": {}, "_id": "require-directory@1.2.0", "dist": {"shasum": "35ff45a82ab73ca6ca35c746c0a17014371e1afd", "tarball": "https://registry.npmjs.org/require-directory/-/require-directory-1.2.0.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON>cqQlJMSHE4rhxv992WQsnzUe6S30m98xr5nn1SUV5xOWPUgFaM5+xdP+5FD5cfVLmOIFz4AMgC9pu/mQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG9h2QfOEfD9thvG6ic6ojaGU3UipVxM0JzmWYfKo9SeAiEA5B8IE+vpojiTtAbtdqynnhm5QP2OjZzqsBY7GVfnZk0="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}, "name": "require-directory", "version": "2.0.0", "description": "Recursively iterates over specified directory, require()'ing each file, and returning a nested hash structure containing those modules.", "keywords": ["require", "directory", "library", "recursive"], "homepage": "https://github.com/troygoode/node-require-directory/", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/troygoode/node-require-directory.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}], "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "bugs": {"url": "http://github.com/troygoode/node-require-directory/issues/"}, "engines": {"node": ">=0.10.0"}, "dependencies": {"underscore": "^1.6.0"}, "devDependencies": {"jshint": "^2.5.2", "mocha": "^1.21.3"}, "scripts": {"test": "mocha", "lint": "jshint index.js test/test.js"}, "gitHead": "ee51f6f6b1e75d7d16ff5195561b5b0f30027a38", "_id": "require-directory@2.0.0", "_shasum": "22442c7c5a675cf70402cddb29518e5edd3427d9", "_from": ".", "_npmVersion": "1.5.0-alpha-1", "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "dist": {"shasum": "22442c7c5a675cf70402cddb29518e5edd3427d9", "tarball": "https://registry.npmjs.org/require-directory/-/require-directory-2.0.0.tgz", "integrity": "sha512-OMgdBPDGOelmeVH6dTb14PbZEUQ32joxgSQBW1/7WCwONbE/p5AvUZGmBXXAoknyymrOiaNDXv6d6Utb3u9Pdg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICKwLmoeMxOJ5072scvO6hj99V1CpDekF1GILHLjCIAxAiEAntxdAqscAwGRZuRRcICnpRGnsbNij/CSuE3wsz4jizY="}]}, "directories": {}}, "2.1.0": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}, "name": "require-directory", "version": "2.1.0", "description": "Recursively iterates over specified directory, require()'ing each file, and returning a nested hash structure containing those modules.", "keywords": ["require", "directory", "library", "recursive"], "homepage": "https://github.com/troygoode/node-require-directory/", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/troygoode/node-require-directory.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}], "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}], "bugs": {"url": "http://github.com/troygoode/node-require-directory/issues/"}, "engines": {"node": ">=0.10.0"}, "devDependencies": {"jshint": "^2.6.0", "mocha": "^2.1.0"}, "scripts": {"test": "mocha", "lint": "jshint index.js test/test.js"}, "gitHead": "08f75bb9e14aa33834519c4788c5618e318925db", "_id": "require-directory@2.1.0", "_shasum": "707ab5d99b3e819ccf3f2bc77195bdcea0f0e61b", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "dist": {"shasum": "707ab5d99b3e819ccf3f2bc77195bdcea0f0e61b", "tarball": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.0.tgz", "integrity": "sha512-A3IwzwgZ56B26Vt+IDXZXhbqB5VYRHpjvR0Os8ymprlLgBB6WOzcVitsuBa0UThXkXVbkyhRATXbucjs0XmqfQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC2qXVJvwLnchNW/3Za3Cdn2S7FkFYVBlhYXZpYnvQAMAiEAlMHG3o/qioN5Qpssy34j0QnUC0kFXJO5vddpej9AxQw="}]}, "directories": {}}, "2.1.1": {"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}, "name": "require-directory", "version": "2.1.1", "description": "Recursively iterates over specified directory, require()'ing each file, and returning a nested hash structure containing those modules.", "keywords": ["require", "directory", "library", "recursive"], "homepage": "https://github.com/troygoode/node-require-directory/", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/troygoode/node-require-directory.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}], "license": "MIT", "bugs": {"url": "http://github.com/troygoode/node-require-directory/issues/"}, "engines": {"node": ">=0.10.0"}, "devDependencies": {"jshint": "^2.6.0", "mocha": "^2.1.0"}, "scripts": {"test": "mocha", "lint": "jshint index.js test/test.js"}, "gitHead": "cc71c23dd0c16cefd26855303c16ca1b9b50a36d", "_id": "require-directory@2.1.1", "_shasum": "8c64ad5fd30dab1c976e2344ffe7f792a6a6df42", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "troygoode", "email": "<EMAIL>"}, "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "dist": {"shasum": "8c64ad5fd30dab1c976e2344ffe7f792a6a6df42", "tarball": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGClBqRURzZkkK060ZZVZeHq85UyQvNt6ZF9KOQfN12PAiABryiFbUkYd99blPQeu55J/QCLQFAzUicy/mm6OR5I9g=="}]}, "directories": {}}}, "readme": "# require-directory\n\nRecursively iterates over specified directory, `require()`'ing each file, and returning a nested hash structure containing those modules.\n\n**[Follow me (@troygoode) on Twitter!](https://twitter.com/intent/user?screen_name=troygoode)**\n\n[![NPM](https://nodei.co/npm/require-directory.png?downloads=true&stars=true)](https://nodei.co/npm/require-directory/)\n\n[![build status](https://secure.travis-ci.org/troygoode/node-require-directory.png)](http://travis-ci.org/troygoode/node-require-directory)\n\n## How To Use\n\n### Installation (via [npm](https://npmjs.org/package/require-directory))\n\n```bash\n$ npm install require-directory\n```\n\n### Usage\n\nA common pattern in node.js is to include an index file which creates a hash of the files in its current directory. Given a directory structure like so:\n\n* app.js\n* routes/\n  * index.js\n  * home.js\n  * auth/\n    * login.js\n    * logout.js\n    * register.js\n\n`routes/index.js` uses `require-directory` to build the hash (rather than doing so manually) like so:\n\n```javascript\nvar requireDirectory = require('require-directory');\nmodule.exports = requireDirectory(module);\n```\n\n`app.js` references `routes/index.js` like any other module, but it now has a hash/tree of the exports from the `./routes/` directory:\n\n```javascript\nvar routes = require('./routes');\n\n// snip\n\napp.get('/', routes.home);\napp.get('/register', routes.auth.register);\napp.get('/login', routes.auth.login);\napp.get('/logout', routes.auth.logout);\n```\n\nThe `routes` variable above is the equivalent of this:\n\n```javascript\nvar routes = {\n  home: require('routes/home.js'),\n  auth: {\n    login: require('routes/auth/login.js'),\n    logout: require('routes/auth/logout.js'),\n    register: require('routes/auth/register.js')\n  }\n};\n```\n\n*Note that `routes.index` will be `undefined` as you would hope.*\n\n### Specifying Another Directory\n\nYou can specify which directory you want to build a tree of (if it isn't the current directory for whatever reason) by passing it as the second parameter. Not specifying the path (`requireDirectory(module)`) is the equivelant of `requireDirectory(module, __dirname)`:\n\n```javascript\nvar requireDirectory = require('require-directory');\nmodule.exports = requireDirectory(module, './some/subdirectory');\n```\n\nFor example, in the [example in the Usage section](#usage) we could have avoided creating `routes/index.js` and instead changed the first lines of `app.js` to:\n\n```javascript\nvar requireDirectory = require('require-directory');\nvar routes = requireDirectory(module, './routes');\n```\n\n## Options\n\nYou can pass an options hash to `require-directory` as the 2nd parameter (or 3rd if you're passing the path to another directory as the 2nd parameter already). Here are the available options:\n\n### Whitelisting\n\nWhitelisting (either via RegExp or function) allows you to specify that only certain files be loaded.\n\n```javascript\nvar requireDirectory = require('require-directory'),\n  whitelist = /onlyinclude.js$/,\n  hash = requireDirectory(module, {include: whitelist});\n```\n\n```javascript\nvar requireDirectory = require('require-directory'),\n  check = function(path){\n    if(/onlyinclude.js$/.test(path)){\n      return true; // don't include\n    }else{\n      return false; // go ahead and include\n    }\n  },\n  hash = requireDirectory(module, {include: check});\n```\n\n### Blacklisting\n\nBlacklisting (either via RegExp or function) allows you to specify that all but certain files should be loaded.\n\n```javascript\nvar requireDirectory = require('require-directory'),\n  blacklist = /dontinclude\\.js$/,\n  hash = requireDirectory(module, {exclude: blacklist});\n```\n\n```javascript\nvar requireDirectory = require('require-directory'),\n  check = function(path){\n    if(/dontinclude\\.js$/.test(path)){\n      return false; // don't include\n    }else{\n      return true; // go ahead and include\n    }\n  },\n  hash = requireDirectory(module, {exclude: check});\n```\n\n### Visiting Objects As They're Loaded\n\n`require-directory` takes a function as the `visit` option that will be called for each module that is added to module.exports.\n\n```javascript\nvar requireDirectory = require('require-directory'),\n  visitor = function(obj) {\n    console.log(obj); // will be called for every module that is loaded\n  },\n  hash = requireDirectory(module, {visit: visitor});\n```\n\nThe visitor can also transform the objects by returning a value:\n\n```javascript\nvar requireDirectory = require('require-directory'),\n  visitor = function(obj) {\n    return obj(new Date());\n  },\n  hash = requireDirectory(module, {visit: visitor});\n```\n\n### Renaming Keys\n\n```javascript\nvar requireDirectory = require('require-directory'),\n  renamer = function(name) {\n    return name.toUpperCase();\n  },\n  hash = requireDirectory(module, {rename: renamer});\n```\n\n### No Recursion\n\n```javascript\nvar requireDirectory = require('require-directory'),\n  hash = requireDirectory(module, {recurse: false});\n```\n\n## Run Unit Tests\n\n```bash\n$ npm run lint\n$ npm test\n```\n\n## License\n\n[MIT License](http://www.opensource.org/licenses/mit-license.php)\n\n## Author\n\n[Troy Goode](https://github.com/TroyGoode) ([<EMAIL>](mailto:<EMAIL>))\n\n", "maintainers": [{"name": "troygoode", "email": "<EMAIL>"}], "time": {"modified": "2022-06-26T11:36:41.323Z", "created": "2011-12-27T00:06:30.661Z", "0.0.1": "2011-12-27T00:06:32.027Z", "0.0.2": "2012-12-12T15:17:08.227Z", "0.0.3": "2012-12-12T21:23:28.294Z", "1.0.0": "2012-12-16T16:30:54.518Z", "1.0.1": "2013-06-23T20:54:08.357Z", "1.0.2": "2013-06-23T21:15:20.814Z", "1.1.0": "2013-08-28T20:32:19.471Z", "1.1.1": "2013-09-01T15:10:33.473Z", "1.2.0": "2013-11-14T13:59:26.636Z", "2.0.0": "2014-07-29T02:17:35.323Z", "2.1.0": "2015-02-19T05:02:34.997Z", "2.1.1": "2015-05-28T08:31:04.702Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}, "repository": {"type": "git", "url": "git://github.com/troygoode/node-require-directory.git"}, "users": {"troygoode": true, "dgarlitt": true, "filipedeschamps": true, "symblst": true, "metinweb": true, "highflying": true, "nukisman": true, "chaseadamsio": true, "skrdv": true, "mgenev": true, "koulmomo": true, "wangnan0610": true, "ufukomer": true, "nelix": true, "mode54": true, "nmccready": true, "icerainnuaa": true, "sopepos": true, "tmurngon": true, "mojaray2k": true, "paulnordlund": true, "shanemileham": true, "fengbeijing": true, "suddi": true, "mariusc23": true, "bvaccc": true, "eonio": true, "tomgao365": true, "xanderlewis": true, "gamersdelight": true, "shlomi": true, "akamaozu": true, "losymear": true, "likkli": true, "dwqs": true}, "homepage": "https://github.com/troygoode/node-require-directory/", "keywords": ["require", "directory", "library", "recursive"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}], "bugs": {"url": "http://github.com/troygoode/node-require-directory/issues/"}, "readmeFilename": "README.markdown", "license": "MIT"}