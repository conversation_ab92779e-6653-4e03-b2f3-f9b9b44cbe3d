{"_id": "resolve.exports", "_rev": "14-dcdbafba8b16d8ea7a3633ea46c419a1", "name": "resolve.exports", "dist-tags": {"next": "2.0.0-next.0", "latest": "2.0.3"}, "versions": {"0.0.0": {"name": "resolve.exports", "version": "0.0.0", "keywords": [], "author": {"url": "https://lukeed.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve.exports@0.0.0", "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "homepage": "https://github.com/lukeed/resolve.exports#readme", "bugs": {"url": "https://github.com/lukeed/resolve.exports/issues"}, "dist": {"shasum": "b27cafe187230c06a262ef0c1bc5c367df208e55", "tarball": "https://registry.npmjs.org/resolve.exports/-/resolve.exports-0.0.0.tgz", "fileCount": 6, "integrity": "sha512-bV3nLzisp6KwcedybgXjYpi958eJwv7s5kcL62QO4LEM9+MAmS0xlQyqWLanwyv7/843Mv1YpdHyzq/tRAbA7A==", "signatures": [{"sig": "MEUCIQCkKEPyZPlsAJWPEQhwpA6tcYfCEn2HVM9BUkc+/7FOTQIgCFHaP6EZje17VgRH4gLHgoBJZViS7L5IAcRbx5hCRMg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf+RTlCRA9TVsSAnZWagAANcsP/0ONUwthsvd5BmLXpkVr\nRk/uY3oq9c5XNm/nLjSWwssCkHA8LEqcqDy1xQarA7C6aA0VjQUVDnlMGyJ/\nbAT99O0iX9JIhsui3W+qfDi9XsZwaKCsOVFdqD52aTNzxc0O156Ya+ydB03x\nzzU7Tn2xTS1rYlATmEqekSru8M4FWJkSDN/b5SYfInedeUwPTmNv3ndwwUsV\nzh1bgRkI9QaXNz9hlFRxFBedZJW/VyI/uwM4nBJ/KDlycrBZrCcIlb2S/oiK\npEUqkDXB02YLSDMgK/AshZxlEOTpAkSyqK1r5FzoBkuOgyfm/B+GjP09oNM9\nvw0SZqY0OCdg14ORT9WWEepUWFVu28CswbJ1AuXwMIxjQnyWRvl2/nicz+bV\neDghkYdaBZFfMZHd49Toihel+qJHI/rd6k2ul0KrdBivoy9SVum9uL3GDM7V\nh8W5AeGgpoAL79eO57SKgb1Pwf/D2uimZBJIbh89KVue7A5/+GUSaKe4kRd3\nhNYLViEpWwKeRlPFbgy19D+ljUV/LXBkXgnw3A5q8X/7VgcKGljnvL3cdcGJ\nm+Jiywb2BxfsjTs5GvNu36r2aWJ7ftLgiyarOpMuzWMMHnDV4i3D7U2uQbqP\nmqnbK6C8o10xY//Suzp9NJEcg8alpWRntFEaYd9zWL8nj4URrzUQlU5nSxi+\nhC4E\r\n=YqMZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=10"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "gitHead": "32dc6ab123ef15ed096441644b8faa66b6198f86", "scripts": {"test": "uvu -r esm test", "build": "bundt", "pretest": "npm run build"}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukeed/resolve.exports.git", "type": "git"}, "_npmVersion": "6.14.9", "description": "WIP", "directories": {}, "_nodeVersion": "14.15.3", "_hasShrinkwrap": false, "devDependencies": {"esm": "3.2.25", "uvu": "0.5.1", "bundt": "1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/resolve.exports_0.0.0_1610159333296_0.8788517243813143", "host": "s3://npm-registry-packages"}}, "0.0.1": {"name": "resolve.exports", "version": "0.0.1", "keywords": [], "author": {"url": "https://lukeed.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve.exports@0.0.1", "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "homepage": "https://github.com/lukeed/resolve.exports#readme", "bugs": {"url": "https://github.com/lukeed/resolve.exports/issues"}, "dist": {"shasum": "75ed520cbe5e4507eebe5f788d63d112745382aa", "tarball": "https://registry.npmjs.org/resolve.exports/-/resolve.exports-0.0.1.tgz", "fileCount": 6, "integrity": "sha512-nqO559g+G+aU8RWJCXUIQY4WGKEAvZPv89cHPDZJWdmCf2B/TO0xgVOhGaVpHidGyV/Rtc4cDaBlmK9oNEoElA==", "signatures": [{"sig": "MEUCIFG4L+xDK+TekMcz8zEdKegmb1hsFYctQjAJBw7aRVauAiEA4SKFv9pfxzTzs3S8WqoGXbeGYNMUimAmt3arW3sFVpI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8426, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf+Tr/CRA9TVsSAnZWagAAtWEP/i74X0kneLK0PAdYHMIj\nmaDrJoVXqHt0/xzYSAeOZBPSbowioUNMOwUAJRlWa9vF4Ga0o7AP1xDmpMfk\nyeZTMaLjIIcID/6v38YJzZ2ooyL35gVmjCYfrlUTb+ztOcXAVNEUnCZXFs/U\nt4vOaovP5yyyWlcy4Qc/4Q/4lIpxC+IJ56MceX/QiMDIojjBAdcQcad6/ckY\nyekYISue6aI5Qtlj082hAHxa8kEUXcida+UHqJtebuJ73ZUQqcmb0hPszcJ6\nQF9GC0pTZixByDPnnC6SCvv4eQU3xhASmO6Eeur4NMIukAPtT3rrCzSNk7Uk\n+BAdejVFKAuwZWP2At8neOTX2kLZm77peV6bMhq+pSy3GnAutXMJpLUfUSEb\nr5cNNUL/ERbdN0exQZ0wMbq6++4Ok2eSYD9M79SMj4vGjCY/aB9+Z/LsD0Oj\nGwV+EiX/h2LoDXgRnKeGlAoge22tnP6qSAb0ne53igKxqJaHYtDg4p079IwX\nug5Rgn7bPWBUQQuPfIoGYRgeYZo0eG8bSmZdWpY97MK56UVMRBsxRkF3rvbS\nIE0tC98sRPDnlxSBMxUR/YCqcDsNSKDovWY9AygFraoAafcNAyMqdRiM0gYF\n/VY9CXTXC/FA8BRItzE60EvwHzJzgWUBHVQXDYguohtdILCGLCeEfnv3y5jz\nVmKX\r\n=mqeP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=10"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "gitHead": "f490b473abe6937ef582f9c620f77fe2ac69d9e1", "scripts": {"test": "uvu -r esm test", "build": "bundt", "pretest": "npm run build"}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukeed/resolve.exports.git", "type": "git"}, "_npmVersion": "6.14.9", "description": "WIP", "directories": {}, "_nodeVersion": "14.15.3", "_hasShrinkwrap": false, "devDependencies": {"esm": "3.2.25", "uvu": "0.5.1", "bundt": "1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/resolve.exports_0.0.1_1610169087255_0.6282237017307519", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "resolve.exports", "version": "1.0.0", "keywords": ["esm", "exports", "<PERSON><PERSON><PERSON><PERSON>", "fields", "modules", "resolution", "resolve"], "author": {"url": "https://lukeed.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve.exports@1.0.0", "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "homepage": "https://github.com/lukeed/resolve.exports#readme", "bugs": {"url": "https://github.com/lukeed/resolve.exports/issues"}, "dist": {"shasum": "47cea79065a93169e1efa02310c92736dcdc849d", "tarball": "https://registry.npmjs.org/resolve.exports/-/resolve.exports-1.0.0.tgz", "fileCount": 6, "integrity": "sha512-Si4IjiInAK+Q7ff6vkegc7mBttSa5b3mzf9nyMubUURZw+ixwROwlD7EpOvs3EX2h/CvF3ag4zIz1DJ6rGyv0Q==", "signatures": [{"sig": "MEUCIG+xssSaigNCzdOG4TNDUIs9UUBYephdxmQT1PzKhv8kAiEA2xMONDjx5Czb71nlfB1AGbtgVP2arpmdUEeICQr9aUo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf++q5CRA9TVsSAnZWagAAsloP/1WNphPDGfDLziGe7L+O\nnrZ1UPNHQj1Zu43LpC+dyXWUQr0wBLVrwR3M3LbmhtvRTIMOpejgKXrM5ocf\nWtmUb1lYqQ+IVWA6BjgyhYZB+u69b4KqKjO0Vfr0H/hNLt7E84wAKPO9YEzD\nPvRzUX8Hp4m+EsVVWBgj1k1ZYMuRPUOcLrVR7Q6aWvR+TZS/ZSnJaPIURR+l\nftUEskOP2XO7Z4jmDLyjDj3y/1pd19wVyesSShhsg9+HulcNfVOKbLF+AuV4\nZFrx1hpbsUAnGq7jS2lFDbL4QUWuFVHr2ItakECm6GQmAn8r4X5n/tuYdWo3\nyU/2DtGH2guYrheKI+9+bIRXlqPYT5qorDBbwfK5k+HxJzMW9AycdKmhh7ly\ngxdxidnc4dBl2Hy2gAKDztamqEES/AwDzg6/oY+U0OoCCVH2FmRcviQTzb2y\nOzM39M1w3bEiUCfX9XBLuiQViEC+1GTlxc27/9GFqhvVGFzUO4iYqF5xYyzc\nyga5bfUsBNJGVq23vKwm+lIVbUDyN37ByoPJgS5XweFFbujP6UwvvDqR7eWy\n1o8eRSFLicOPs5lsVIKW4Ouq2WtKpp1sPr2VvdEFx6dSRmbxhf7Q5Jo5Klwh\nQtqPgt0KiHhuLIUHXRLl4K1jJwmtYJzCwvxoUf+wbhOfSIvT4xYfiMdy3aPA\nn5MI\r\n=X8gG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=10"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "gitHead": "c81235c81ad786fa5d71cebbab099ae4609b3265", "scripts": {"test": "uvu -r esm test", "build": "bundt"}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukeed/resolve.exports.git", "type": "git"}, "_npmVersion": "6.14.9", "description": "A tiny (710b), correct, general-purpose, and configurable \"exports\" resolver without file-system reliance", "directories": {}, "_nodeVersion": "14.15.3", "_hasShrinkwrap": false, "devDependencies": {"esm": "3.2.25", "uvu": "0.5.1", "bundt": "1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/resolve.exports_1.0.0_1610345144538_0.23640678369926849", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "resolve.exports", "version": "1.0.1", "keywords": ["esm", "exports", "<PERSON><PERSON><PERSON><PERSON>", "fields", "modules", "resolution", "resolve"], "author": {"url": "https://lukeed.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve.exports@1.0.1", "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "homepage": "https://github.com/lukeed/resolve.exports#readme", "bugs": {"url": "https://github.com/lukeed/resolve.exports/issues"}, "dist": {"shasum": "9aff14e2ed43ea40bbdc2f83b9a2465b1f093325", "tarball": "https://registry.npmjs.org/resolve.exports/-/resolve.exports-1.0.1.tgz", "fileCount": 6, "integrity": "sha512-rV7NC8yDnV2wSg3nB7Faoje+oQk7/7cuxEq4cgOR81mULtAWvHGODowzxojq4FIftNA0FRtnn5v5a+F4aTiSzA==", "signatures": [{"sig": "MEYCIQDEbM8wVckQLAVRsQhCrNyGouhd65WDfvqASsb5JKpX0QIhAP+r/R4u6Tjh3at5h9Uel0CI4E0SjfMnCRpadTHGBTbv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18872, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/KGXCRA9TVsSAnZWagAAeYQP/RKbvbBqaoUp6/GmmdxH\nVGmloCOm5WBpv0MeNOaQ/EOT1RBxWMMeUBK7NgIXauTqvHbrmiJK3Ezv1XYW\n4o7pAg4DYjzSqPthHPEqsqEnbLwfDnqVSonYKkHdPbraIvboeZdDE9/H2pPe\nNXbTrzvPEocq00xuqtIynesmFCag9LOEASMugPazCSZrrZH4L7GBL+wkbySH\nm3h13ZTLMK9PHviyVnLSAh95djCa9eyqruNqVXCjiaWl8ZUVGIb4VfiVzuM6\nSurFG8sanEhYvy2TpyngBJPJaxnpSTjP3O7lXhflI/JGSJe4Eu7PBUnPn8Ih\nRT38vYW2bK7u7s7Fq1QzInQejnZWcRKPseWu6cJNJLH9SD0TCjrtqbP2VFbK\nh4PA2UIrBCO4t6sMVeGNH43D7zWoM0kfCc22D3Q9RiB9cDqPv2JWXTzjAyMw\nIeOF6eibi98xZ39+/S1snu/XoEBnKuiFK8dYuaEdSdrDtliTC/N7MEPzkIGZ\nLHJ3ZpKXu1oGRr1a/rEq3Il4kkeLmugQut9jZqglI8diskkbqD1jigtCb/Tx\nVFU1L3tTJbpNqBSavQ3x090Xj0LIHGft/aNRcSf6pUCyMwyh9LElVD7oIV80\ntWyJLTYuo8fWBGa2IMsif4RgnAwPITlrJ4MzGmprWY6IBpldIEa5d2fsva+u\nkTlH\r\n=zJIF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=10"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "gitHead": "bd8eacfbf1144ad7334d8414ce9f5573c91a07d0", "scripts": {"test": "uvu -r esm test", "build": "bundt"}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukeed/resolve.exports.git", "type": "git"}, "_npmVersion": "6.14.9", "description": "A tiny (727b), correct, general-purpose, and configurable \"exports\" resolver without file-system reliance", "directories": {}, "_nodeVersion": "14.15.3", "_hasShrinkwrap": false, "devDependencies": {"esm": "3.2.25", "uvu": "0.5.1", "bundt": "1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/resolve.exports_1.0.1_1610391958457_0.13014374403190643", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "resolve.exports", "version": "1.0.2", "keywords": ["esm", "exports", "<PERSON><PERSON><PERSON><PERSON>", "fields", "modules", "resolution", "resolve"], "author": {"url": "https://lukeed.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve.exports@1.0.2", "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "homepage": "https://github.com/lukeed/resolve.exports#readme", "bugs": {"url": "https://github.com/lukeed/resolve.exports/issues"}, "dist": {"shasum": "98c82ba8a4d3f9fcc32cbfa6f950b803d77b6c21", "tarball": "https://registry.npmjs.org/resolve.exports/-/resolve.exports-1.0.2.tgz", "fileCount": 6, "integrity": "sha512-1+PDdTR3xrGWB/NzXLkzS1+PQlJ+BOR2baBGJSVat4HasiY1mnkyAQws3FUTmBDB79oK54QFaDM8Ig9nUtJwvQ==", "signatures": [{"sig": "MEQCICE6GsjHbSxcNTS3Dy/9gXi/GOr7X+Cs7Kdub3TSlkGLAiBNMCFq3X75eNDI1AAIhvhityIgHn4+mbbdPh4k/WJr5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18898, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/gusCRA9TVsSAnZWagAAt20QAJjwGZafnvyhXQ5cK4XS\naxqGBqMIsm9rsryfcBU8GcsZ76IY2G9FF5ZA9ANo7sfzsktuBhPj/yjKLQYG\nAeJl19qniWXpJS3YjD8uFrys0iVaousrHk6Gq3Q+j0dAPrCuVGWzBTDZ3p0g\nJQav0EwDel0Y9pZAz2syRNmzltIVjSl1ewUfL2HTBrnH0OtNhOcEWZfoKRvu\nQjmvd7nGtTLANHxrJbTWwpJe5Cfc5V1fBEMY05gTLWD8FiNzClcx9X9TSmuD\nJX+IlgP6vHLtOJxVG1/v/OGhOUfekjZniAR/GwSlJ+7myBk6IJ4kBSC9d4Kx\naQVHd8zk5dRLwuQ35Eue0KLRQIA6Ou/Q8vM6hs8EP6k9nWpGnuSohji7Dt1E\nSZgfV/zW7ZPZ4aUKfb0R3M+4ALJC5A8HdlZ4UibWZX/Lvu/p0ITMup/gWOrS\nGPg3Hn8CQPYie/js2wXR6YAjuew5yUvpxtKWoQ8ds+qqmF0FrX8KWHBi3NNj\nWLjhG16wZf3JuLpw8jLxcUx0MInNruRD0WHP2N4qyWOCcsFc/eyIb0CcOgR8\ngE9IEMKqZNB0K5ZoZuoo1kWLPZW9VUAnF11OwObH1d2TN3D0sv5uwTIdLzH/\n/5r9uG5l6XQ48mAl2otdVkRBQenyQAc4I8k+4+eoxyUvLiSdYf0/KxSebDz+\ncRKQ\r\n=gA2f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=10"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "gitHead": "c6814c43cb27ee6092f649be3fdf6c1fb55c51e4", "scripts": {"test": "uvu -r esm test", "build": "bundt"}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukeed/resolve.exports.git", "type": "git"}, "_npmVersion": "6.14.9", "description": "A tiny (727b), correct, general-purpose, and configurable \"exports\" resolver without file-system reliance", "directories": {}, "_nodeVersion": "14.15.3", "_hasShrinkwrap": false, "devDependencies": {"esm": "3.2.25", "uvu": "0.5.1", "bundt": "1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/resolve.exports_1.0.2_1610484652362_0.17950109833312755", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "resolve.exports", "version": "1.1.0", "keywords": ["esm", "exports", "<PERSON><PERSON><PERSON><PERSON>", "fields", "modules", "resolution", "resolve"], "author": {"url": "https://lukeed.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve.exports@1.1.0", "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "homepage": "https://github.com/lukeed/resolve.exports#readme", "bugs": {"url": "https://github.com/lukeed/resolve.exports/issues"}, "dist": {"shasum": "5ce842b94b05146c0e03076985d1d0e7e48c90c9", "tarball": "https://registry.npmjs.org/resolve.exports/-/resolve.exports-1.1.0.tgz", "fileCount": 6, "integrity": "sha512-J1l+Zxxp4XK3LUDZ9m60LRJF/mAe4z6a4xyabPHk7pvK5t35dACV32iIjJDFeWZFfZlO29w6SZ67knR0tHzJtQ==", "signatures": [{"sig": "MEYCIQCggoszIYD+9z9u3/UeZht4W5+B/iTAUDNXTVhHWBDfzAIhANcjRhBJnSzA81mq1nBQL4gIWLo9B3LdvFxIWb5FnzVy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20266, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh25wHCRA9TVsSAnZWagAAtcoQAJ/v53qgWmJ7teQMcwzO\nvIsNJblEbnRH4XvnGNLPwt7x+CYwRAEybAglzcWrxDi4lV2AFEQKpAV6enF3\nK5KZedlqmZULwdOExNF1P1mHZianm/TtqqaKfRsC8pP1/I14vY7Er0diU5M9\nP5ywbaOqY/2zu1TKSX3Lehz1kyQ0kIKvOYyJGnZToyz8J1P1pZIQzwQXBWcW\n6e9FTYewYGxcqJy55iBNXKM/UolbryTC2sYoxVWCeathhdLjGnytxMHBQzcO\nl2LRITwGxYGDwmGXNYIY1gTou22rfPJUA5jtPSW7OrlXqP23xmZMNRw+Btk3\nBj4++tMt9ktYKnM7kbBIcUC9K42FDle5u/Yg56yvxi4dKxDiqm1edK9nO5HL\nAd/HA7Ta95pAD8DWR8dFlIGig+JT3oUyvuXU6zUI0UI59wXHphyBydj1nrzq\nguBpdYK1521KUVLhR3Oc70tJtgUvbRubKJH/jEK7CWfKJDvnWnjePze81LPm\nqa3yvYmqOBhF84f5kpmXd5VcExq6fFYTmyAHbtEjzrRWTHDE2giDEvcyotFW\nKZh5Dqf4vGJVXRBreQJWqWrDkdW2Mo/r8mvyKKRHBrwT5zf4S3OsCjof+wVC\nQbORKX9nij6rWDyBvjGNplcKwCrWljFCPiv9Kc1w+xdHXl23cCvWEMIBVq2B\nTJM9\r\n=bGX9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=10"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "gitHead": "6d29cfbabfea13a41cbb75b57bdf46f6b0bb5115", "scripts": {"test": "uvu -r esm test", "build": "bundt"}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukeed/resolve.exports.git", "type": "git"}, "_npmVersion": "7.21.1", "description": "A tiny (737b), correct, general-purpose, and configurable \"exports\" resolver without file-system reliance", "directories": {}, "_nodeVersion": "16.9.1", "_hasShrinkwrap": false, "devDependencies": {"esm": "3.2.25", "uvu": "0.5.1", "bundt": "1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/resolve.exports_1.1.0_1634259705622_0.15236758173837206", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "resolve.exports", "version": "1.1.1", "keywords": ["esm", "exports", "<PERSON><PERSON><PERSON><PERSON>", "fields", "modules", "resolution", "resolve"], "author": {"url": "https://lukeed.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve.exports@1.1.1", "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "homepage": "https://github.com/lukeed/resolve.exports#readme", "bugs": {"url": "https://github.com/lukeed/resolve.exports/issues"}, "dist": {"shasum": "05cfd5b3edf641571fd46fa608b610dda9ead999", "tarball": "https://registry.npmjs.org/resolve.exports/-/resolve.exports-1.1.1.tgz", "fileCount": 6, "integrity": "sha512-/NtpHNDN7jWhAaQ9BvBUYZ6YTXsRBgfqWFWP7BZBaoMJO/I3G5OFzvTuWNlZC3aPjins1F+TNrLKsGbH4rfsRQ==", "signatures": [{"sig": "MEUCIQCJryg7NS571tgCN9OO0L5S65bNKUMIPLU90nzSJjR/0wIgfkQFf/PBmhKpjhkwY24rOqluNOgvpkX0iVRwX6r9Ps0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20920, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvzEpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmozgQ//SsCJlPjcCUTvJ7x6AVY+J7kjIH+vpXKm00VN+Me5UquqRQ+k\r\nmuZQhVzH9GQE7WqEBwSkiKYwdVZ0iR4d8uXiWxVZTVbj/GODziRTBBFRtwnr\r\nxHK+3HtHYD+OJ/WH3+b4Qo3Po7WOHX/vZWVW6112broaj91jQpWASPeR0PYB\r\nYa/bGt66CLV0fGrBJQ3k9Uc1eLMloIM6ExiuqigqNz0IE12BzM/PAaFOmwtu\r\nmo6ioDH2+TOdLn2zeluSb/usUf8Q0JZrro0HPUnmDiJXK7pvsUp1LBdoCJbU\r\nfu7HerLbim8lwZ5aFXgqtzM4DMSq9vUpclLWdc205Kv29FDEIAW8lTYBIKui\r\ngo2e6IYRwok55e+sJxDAUZ9tjNOLhgr+B3QlF4WFUj5eLR4Vror94z6NB9MT\r\nnOCUHkDybgVzx4gxlxLXaGganZFDFOwJBCZYyb044OrvPTzZ0zyIHENdMihb\r\nmWJIT5aeaTRChEIXWEkQ2GCDoj+kOB2aP85LtObpd7Y4qRfPxW8SvTUpamP4\r\nZFjujHs7tJJMaGS0uPw4NxcqSJbkiqM69cJMdH3PpaDeydC31buQbiSqOTWd\r\nbC/bScmjaz8z4O5cMNSHA0PZjqR5z5hlqme4s3X2USVf9tFGTZrmCvUcqeXY\r\nN+bjFc9l1psJzF/dH6tuKDj+2dVhE9DUj54=\r\n=eGM5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=10"}, "exports": {".": {"types": "./index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "gitHead": "dc499dcd5ad6b237913a4c7e26f5f68707f0a9b6", "scripts": {"test": "uvu -r esm test", "build": "bundt"}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukeed/resolve.exports.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "A tiny (813b), correct, general-purpose, and configurable \"exports\" resolver without file-system reliance", "directories": {}, "_nodeVersion": "19.4.0", "_hasShrinkwrap": false, "devDependencies": {"esm": "3.2.25", "uvu": "0.5.1", "bundt": "1.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/resolve.exports_1.1.1_1673474345729_0.15163516585965664", "host": "s3://npm-registry-packages"}}, "2.0.0-next.0": {"name": "resolve.exports", "version": "2.0.0-next.0", "keywords": ["esm", "exports", "<PERSON><PERSON><PERSON><PERSON>", "fields", "modules", "resolution", "resolve"], "author": {"url": "https://lukeed.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve.exports@2.0.0-next.0", "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "homepage": "https://github.com/lukeed/resolve.exports#readme", "bugs": {"url": "https://github.com/lukeed/resolve.exports/issues"}, "dist": {"shasum": "4f1b450e61bea15351ee2982bbc6967092ad8334", "tarball": "https://registry.npmjs.org/resolve.exports/-/resolve.exports-2.0.0-next.0.tgz", "fileCount": 6, "integrity": "sha512-FXxVFtQJY8IwCBDzVdeOH76R8IqA/0rdHSpfZZDdxh9BCQl2WDw90rbBDF5R9ctiqPPR+T0OQ0VAnp2mhvUUHQ==", "signatures": [{"sig": "MEYCIQC5zgVN0BZ1aGHQq1Pl308r2IQU84D+xHe4kM9SJY5sigIhAMIseEcx6SLau8B2sfdHxfR4cGO88yF1KB2O5pfkGVUO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxCUhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr2QBAAooHFhXXIsHNFfmKhEIpNlsyu03cjP3fVSDA9DW8hx/sZdAu0\r\nQFtgHFFNzAVx9aYI2MMOPWm5J6QtH1XOOO4W+eBdnx/t2aS0CrD2e8L2UGyl\r\n74aWcT4wWqUqbWq8JSStF+hobfnDOs0Vbf2aNRcZYWwxJp9Pi1GQmZ6rmTQ/\r\ncKwHpeiGvuINQxBc1Ws7dLqTLQ0QwWSRrEo1uGpeRdUZtMTBtSC4qSDyFOHw\r\nt/VIFaGm1nveR079r8YqyBUhVB/5V/jFr2HGd6aOgEuNNlcJHLdGUVLRYSuG\r\ne0p9HeiARNSV4NOGKhr3a1J47qpkNvykkeHHD6cz5HyzZc+OUQ3nI2O88XUb\r\nQL1k0uhTHGXNMRzwYi2yHyR7OHL83f6ieIOK+QNGe05EeLZiWMsOcHbR1QSc\r\nsO92ABnoP4rgR06nimo8MZwBuFqP/zgut3OpxTbcr8oHh+ifHW2m8eirJ2ls\r\ni1JADSjt+5RmIMTDZ6vbK7uPNsf3q0BzpPXHV1lzVphcZ36ZWalIJImNzZAY\r\nLhqYtl6OvHY9D0MNyuDr8u9Y0QovPKXcEZ4uUs3nWy3ao6sj2yo1TTGn6Wsz\r\nd7llHNzdGOw580a47m57Sb7a8AH8H7PkrbRE6iqvrsyKWsqWVyrhvSPMASsW\r\nKespaIA+PejP+wGaobC5W17/tbWkJy9/E48=\r\n=2a71\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=10"}, "exports": {".": {"types": "./index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "gitHead": "168ba52c4b0fa267cbae3e98fe0036f3d7b47a5c", "scripts": {"test": "uvu -r tsm test", "build": "bundt -m", "types": "tsc --noEmit"}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukeed/resolve.exports.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "A tiny (987b), correct, general-purpose, and configurable \"exports\" and \"imports\" resolver without file-system reliance", "directories": {}, "_nodeVersion": "19.4.0", "_hasShrinkwrap": false, "readmeFilename": "readme.md", "devDependencies": {"tsm": "2.3.0", "uvu": "0.5.4", "bundt": "next", "typescript": "4.9.4"}, "_npmOperationalInternal": {"tmp": "tmp/resolve.exports_2.0.0-next.0_1673798944798_0.8447938544221767", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "resolve.exports", "version": "2.0.0", "keywords": ["esm", "exports", "<PERSON><PERSON><PERSON><PERSON>", "fields", "modules", "resolution", "resolve"], "author": {"url": "https://lukeed.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve.exports@2.0.0", "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "homepage": "https://github.com/lukeed/resolve.exports#readme", "bugs": {"url": "https://github.com/lukeed/resolve.exports/issues"}, "dist": {"shasum": "c1a0028c2d166ec2fbf7d0644584927e76e7400e", "tarball": "https://registry.npmjs.org/resolve.exports/-/resolve.exports-2.0.0.tgz", "fileCount": 6, "integrity": "sha512-6K/gDlqgQscOlg9fSRpWstA8sYe8rbELsSTNpx+3kTrsVCzvSl0zIvRErM7fdl9ERWDsKnrLnwB+Ne89918XOg==", "signatures": [{"sig": "MEUCIAaHMomfGAB4dA+O3Ncj1acYegi5nrwQv7gG0mTE87jkAiEA19wiE8L6NdQxJGh6nCLmJLkaOgdUEuFDJsrLoaV89+I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxcoZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpOyQ/9H0QzURDvkxHBMDw8t0slI5KnID2h1r9muJSCVVFFrOcmvTov\r\nXq/O6Kak1OHVclw+rcWrnjZB96Yd6WT7vzUunm0YTHz3wUxj74Iv45ezdtv3\r\n9TOjrwsUNLNFbV9r5lyCZUoO1fvKMeqLkxo6Q0T1MDhUKScOLILa9QsBrVnW\r\nZ49wmnI22NqnHSy4bycICTacJahab4WJHd5Mb82D5o2lzVGiuPFkeDg4NcUJ\r\nwGGrYpNHxwWVX6QeRh5oURTXkyTB9qp9TBUmlQ6H02UISObF2XyJ0/GWOyc8\r\nc6TM2jQbHU/wMUIqKvbkPdtDVDytjsWMnubgP96LYPJsWvBeNkwJPUfxPoMy\r\nVRkoeQzCSzcRlxLR/FALX1zZMtGcZaJW7Lwc2KfFsJ7TOaDAS0TivywLLJOl\r\nwc9TDmnZB0BbR7nCf+7rOwcNtNR0Hr3VNuqX4lvHLNTFfO0a5RmDZwCd49Q1\r\nZMom1k01A+ewDZB3iFvFOZ2dUbc48jqNxhDdSjnNKhL0Pfn2zeTI9s7vO4PT\r\nDtjUDeo3Nz/bHEEkYzEupml+bIvYBJPw7GspkQYZIIKArAa1dKDzN5W0rb62\r\nW/dVh0JOBvV0k38aEuJxr6CDrfdBvmx5ltlyQDLleRzAo9MSltWXIrMKABvn\r\nFOYTW7H2GrcopZfA721f9kPYnHUpdtiRnxA=\r\n=Xb0O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=10"}, "exports": {".": {"types": "./index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "gitHead": "d1b3468f9313decb3f025c7d884e0e8629aa31ba", "scripts": {"test": "uvu -r tsm test", "build": "bundt -m", "types": "tsc --noEmit"}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukeed/resolve.exports.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "A tiny (952b), correct, general-purpose, and configurable \"exports\" and \"imports\" resolver without file-system reliance", "directories": {}, "_nodeVersion": "19.4.0", "_hasShrinkwrap": false, "devDependencies": {"tsm": "2.3.0", "uvu": "0.5.4", "bundt": "next", "typescript": "4.9.4"}, "_npmOperationalInternal": {"tmp": "tmp/resolve.exports_2.0.0_1673906713573_0.5393012792194112", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "resolve.exports", "version": "2.0.1", "keywords": ["esm", "exports", "<PERSON><PERSON><PERSON><PERSON>", "fields", "modules", "resolution", "resolve"], "author": {"url": "https://lukeed.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve.exports@2.0.1", "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "homepage": "https://github.com/lukeed/resolve.exports#readme", "bugs": {"url": "https://github.com/lukeed/resolve.exports/issues"}, "dist": {"shasum": "cee884cd4e3f355660e501fa3276b27d7ffe5a20", "tarball": "https://registry.npmjs.org/resolve.exports/-/resolve.exports-2.0.1.tgz", "fileCount": 6, "integrity": "sha512-OEJWVeimw8mgQuj3HfkNl4KqRevH7lzeQNaWRPfx0PPse7Jk6ozcsG4FKVgtzDsC1KUF+YlTHh17NcgHOPykLw==", "signatures": [{"sig": "MEUCIHY5+EvcSY9+wySzQK2J00NgEMd621ALDwFEzTSlCTzlAiEAuLC68TCggb8j5/6BeyDuc0ZEbJnCIs5Z9nHm2hbQIYA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23942, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBji1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCNQ/+NZUi+EKPCrO+/OONMbYloZ9UHkKWr1vz07JoqWKA0Cc5taKQ\r\nYHOym2eSWGlSTVv2G467U9/w0B8WQX8vfrfH6LGv5N1V2AIrR1Gy7Nnk3T8N\r\nSS16ZUI3hYJ3tuWlEqOITB4TQi1DjL9ZNn+G/StwrRjmuUkb3KsHWX0f9/Dp\r\nDlFy7UHpmyIkjV9pbNEg36yIPe0wZuxW7lD8j5DTpyEGomig6BwgPGzbZYRU\r\npuOfJ69F8nipJye2o+CdoPiApzeLx5/QCm0+wRJq5onpgPKTSGOxrufzPhwg\r\nTy24HefYX2SIMI4yn68iErMqMHA7RIeiikLC/YQjYivsh/6snmAK97RbliQG\r\njBzsELPyA/efyv9Ghme3iG6C/8nhEQdtVO7lmJrm7MuJ8t23cJVoIIlVMegS\r\nLCoHhpgfSOh8UIpdKAxUXQ1g4OdZsDVN6XtuwjlOgcE0GF1hY/v2/LcoeqyA\r\naNvkloloFR3SRETH2FGgiGVaTKj5+vuWpGBEg2nk+pWbJqRLSSyipIL3c3+2\r\nE4dev1Uu++viekcJD36EkIsM1TxbVtEgFvHsuL0OfHZODSxpxlgS/IzZ18OZ\r\nD8kzns0r0t/FUXUmXPJ4jUwSrQQtMI5WrFlNoP3skXPE0B5mv5Yd9N/l8h90\r\ndKoANXoR8Luh3hNAl7I2GOzC82F5V8bGPGo=\r\n=DkWi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=10"}, "exports": {".": {"types": "./index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "gitHead": "8f3fe0d48ed349520cdbc23352d20962a73f7b1b", "scripts": {"test": "uvu -r tsm test", "build": "bundt -m", "types": "tsc --noEmit"}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukeed/resolve.exports.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A tiny (952b), correct, general-purpose, and configurable \"exports\" and \"imports\" resolver without file-system reliance", "directories": {}, "_nodeVersion": "18.12.1", "_hasShrinkwrap": false, "devDependencies": {"tsm": "2.3.0", "uvu": "0.5.4", "bundt": "next", "typescript": "4.9.4"}, "_npmOperationalInternal": {"tmp": "tmp/resolve.exports_2.0.1_1678129333393_0.3439290224473044", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "resolve.exports", "version": "2.0.2", "keywords": ["esm", "exports", "<PERSON><PERSON><PERSON><PERSON>", "fields", "modules", "resolution", "resolve"], "author": {"url": "https://lukeed.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "resolve.exports@2.0.2", "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "homepage": "https://github.com/lukeed/resolve.exports#readme", "bugs": {"url": "https://github.com/lukeed/resolve.exports/issues"}, "dist": {"shasum": "f8c934b8e6a13f539e38b7098e2e36134f01e800", "tarball": "https://registry.npmjs.org/resolve.exports/-/resolve.exports-2.0.2.tgz", "fileCount": 6, "integrity": "sha512-X2UW6Nw3n/aMgDVy+0rSqgHlv39WZAlZrXCdnbyEiKm17DSqHX4MmQMaST3FbeWR5FTuRcUwYAziZajji0Y7mg==", "signatures": [{"sig": "MEUCIBPLhVY1jQhkuZoLW/AKT/Mh1r7SDX5KU60X19DdORssAiEAmq5a+5OXxnOo0RtuNPewmfIt+89T98o1zOFp53Ygm9E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23942, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHKHUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqdag//T0xrIGB/BN8aM0nTtBC/yfthkHinSUbIb5yMVY2D27rXLBdx\r\ntP6yY+gYWz3FtXQ8Diy6zJo86624wg3xL4bF972HGzcVqqna+0xavjo8hXgq\r\n6CX9rkuTw894e07CLUCmLDJkBYWYxID56kaV0ksPCiQ7XqaIkoyyf04uja6D\r\nJOaFeVUYCd/9YJWGPa7wWI+RTwxoRqsacSKYmrt57eJYL5KhKwR7GoIciqfE\r\nfVK9C/Fnn0YV7G7QCwuLuoFYjgxXhN0lc3X9ECIHpq/h6AskmHqBancBwgDE\r\nGgijAjnZF0QA9OCByc2D/RoLyqWZvBhgJRw63Cff4ahHHRtB6MXKUnz/nCZc\r\nzzN5ocJOSHDA5Zr+R+VYvQnQj8CnerWczcd4mCPFd2Wzl70+VZJ/gPsjD4Bn\r\nS6epkYuMx75KuJsyObn+aiVuiUf0/znnKkT0NkQrTxmdWA+dXMvWUZmp6OrC\r\nVw1klq+jU50Vf9TA9MpCxmbq/d/TtrwCTU7qHeGJnhfteKcx1Pv/SvvBFvpA\r\nttrnb3NZppjjspXwSd4GZVI5AlcsFC5XCNgIdRLfk/UTOqEobJd5UR3k4s2O\r\njFe7TsCRvf2IzqvUO6U8TcmPs1hbgjJR+UhVA34I+XI78w/zNnwKXVl6rgJf\r\nuXlBiGYaZUlDIOnjDf1OQITpS6ra6YbpBRI=\r\n=b2nc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/index.js", "types": "index.d.ts", "module": "dist/index.mjs", "engines": {"node": ">=10"}, "exports": {".": {"types": "./index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "gitHead": "ac545e5b1b0845c0cae9d5e728531db85becc3fa", "scripts": {"test": "uvu -r tsm test", "build": "bundt -m", "types": "tsc --noEmit"}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lukeed/resolve.exports.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "A tiny (952b), correct, general-purpose, and configurable \"exports\" and \"imports\" resolver without file-system reliance", "directories": {}, "_nodeVersion": "18.12.1", "_hasShrinkwrap": false, "devDependencies": {"tsm": "2.3.0", "uvu": "0.5.4", "bundt": "next", "typescript": "4.9.4"}, "_npmOperationalInternal": {"tmp": "tmp/resolve.exports_2.0.2_1679598036753_0.9606690151455957", "host": "s3://npm-registry-packages"}}, "2.0.3": {"version": "2.0.3", "name": "resolve.exports", "repository": {"type": "git", "url": "git+https://github.com/lukeed/resolve.exports.git"}, "description": "A tiny (952b), correct, general-purpose, and configurable \"exports\" and \"imports\" resolver without file-system reliance", "module": "dist/index.mjs", "main": "dist/index.js", "types": "index.d.ts", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "engines": {"node": ">=10"}, "scripts": {"build": "bundt -m", "types": "tsc --noEmit", "test": "uvu -r tsm test"}, "exports": {".": {"types": "./index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "keywords": ["esm", "exports", "<PERSON><PERSON><PERSON><PERSON>", "fields", "modules", "resolution", "resolve"], "devDependencies": {"bundt": "next", "tsm": "2.3.0", "typescript": "4.9.4", "uvu": "0.5.4"}, "_id": "resolve.exports@2.0.3", "gitHead": "6613167c0b4fc1511fc52171f4f52cb030c928c3", "bugs": {"url": "https://github.com/lukeed/resolve.exports/issues"}, "homepage": "https://github.com/lukeed/resolve.exports#readme", "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-OcXjMsGdhL4XnbShKpAcSqPMzQoYkYyhbEaeSko47MjRP9NfEQMhZkXL1DoFlt9LWQn4YttrdnV6X2OiyzBi+A==", "shasum": "41955e6f1b4013b7586f873749a635dea07ebe3f", "tarball": "https://registry.npmjs.org/resolve.exports/-/resolve.exports-2.0.3.tgz", "fileCount": 6, "unpackedSize": 24050, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCNmiKKxu+lfTpkAvqDR68pvUROCF1ZWANUde7oedWFvQIgSR1qoktjptfP19lh8lCO+Rfi8i5efpUNHly1Grn8c5E="}]}, "_npmUser": {"name": "lukeed", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/resolve.exports_2.0.3_1733157078992_0.7238616381641907"}, "_hasShrinkwrap": false}}, "time": {"created": "2021-01-09T02:28:53.296Z", "modified": "2024-12-02T16:31:19.348Z", "0.0.0": "2021-01-09T02:28:53.413Z", "0.0.1": "2021-01-09T05:11:27.419Z", "1.0.0": "2021-01-11T06:05:44.729Z", "1.0.1": "2021-01-11T19:05:58.595Z", "1.0.2": "2021-01-12T20:50:52.533Z", "1.1.0": "2021-10-15T01:01:45.819Z", "1.1.1": "2023-01-11T21:59:05.921Z", "2.0.0-next.0": "2023-01-15T16:09:04.984Z", "2.0.0": "2023-01-16T22:05:13.742Z", "2.0.1": "2023-03-06T19:02:13.593Z", "2.0.2": "2023-03-23T19:00:36.907Z", "2.0.3": "2024-12-02T16:31:19.165Z"}, "bugs": {"url": "https://github.com/lukeed/resolve.exports/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "license": "MIT", "homepage": "https://github.com/lukeed/resolve.exports#readme", "keywords": ["esm", "exports", "<PERSON><PERSON><PERSON><PERSON>", "fields", "modules", "resolution", "resolve"], "repository": {"type": "git", "url": "git+https://github.com/lukeed/resolve.exports.git"}, "description": "A tiny (952b), correct, general-purpose, and configurable \"exports\" and \"imports\" resolver without file-system reliance", "maintainers": [{"name": "lukeed", "email": "<EMAIL>"}], "readme": "# resolve.exports [![CI](https://github.com/lukeed/resolve.exports/workflows/CI/badge.svg)](https://github.com/lukeed/resolve.exports/actions) [![licenses](https://licenses.dev/b/npm/resolve.exports)](https://licenses.dev/npm/resolve.exports) [![codecov](https://codecov.io/gh/lukeed/resolve.exports/branch/master/graph/badge.svg?token=4P7d4Omw2h)](https://codecov.io/gh/lukeed/resolve.exports)\n\n> A tiny (952b), correct, general-purpose, and configurable `\"exports\"` and `\"imports\"` resolver without file-system reliance\n\n***Why?***\n\nHopefully, this module may serve as a reference point (and/or be used directly) so that the varying tools and bundlers within the ecosystem can share a common approach with one another **as well as** with the native Node.js implementation.\n\nWith the push for ESM, we must be _very_ careful and avoid fragmentation. If we, as a community, begin propagating different _dialects_ of the resolution algorithm, then we're headed for deep trouble. It will make supporting (and using) `\"exports\"` nearly impossible, which may force its abandonment and along with it, its benefits.\n\nLet's have nice things.\n\n## Install\n\n```sh\n$ npm install resolve.exports\n```\n\n## Usage\n\n> Please see [`/test/`](/test) for examples.\n\n```js\nimport * as resolve from 'resolve.exports';\n\n// package.json contents\nconst pkg = {\n  \"name\": \"foobar\",\n  \"module\": \"dist/module.mjs\",\n  \"main\": \"dist/require.js\",\n  \"imports\": {\n    \"#hash\": {\n      \"import\": {\n        \"browser\": \"./hash/web.mjs\",\n        \"node\": \"./hash/node.mjs\",\n      },\n      \"default\": \"./hash/detect.js\"\n    }\n  },\n  \"exports\": {\n    \".\": {\n      \"import\": \"./dist/module.mjs\",\n      \"require\": \"./dist/require.js\"\n    },\n    \"./lite\": {\n      \"worker\": {\n        \"browser\": \"./lite/worker.browser.js\",\n        \"node\": \"./lite/worker.node.js\"\n      },\n      \"import\": \"./lite/module.mjs\",\n      \"require\": \"./lite/require.js\"\n    }\n  }\n};\n\n// ---\n// Exports\n// ---\n\n// entry: \"foobar\" === \".\" === default\n// conditions: [\"default\", \"import\", \"node\"]\nresolve.exports(pkg);\nresolve.exports(pkg, '.');\nresolve.exports(pkg, 'foobar');\n//=> [\"./dist/module.mjs\"]\n\n// entry: \"foobar/lite\" === \"./lite\"\n// conditions: [\"default\", \"import\", \"node\"]\nresolve.exports(pkg, 'foobar/lite');\nresolve.exports(pkg, './lite');\n//=> [\"./lite/module.mjs\"]\n\n// Enable `require` condition\n// conditions: [\"default\", \"require\", \"node\"]\nresolve.exports(pkg, 'foobar', { require: true }); //=> [\"./dist/require.js\"]\nresolve.exports(pkg, './lite', { require: true }); //=> [\"./lite/require.js\"]\n\n// Throws \"Missing <entry> specifier in <name> package\" Error\nresolve.exports(pkg, 'foobar/hello');\nresolve.exports(pkg, './hello/world');\n\n// Add custom condition(s)\n// conditions: [\"default\", \"worker\", \"import\", \"node\"]\nresolve.exports(pkg, 'foobar/lite', {\n  conditions: ['worker']\n}); //=> [\"./lite/worker.node.js\"]\n\n// Toggle \"browser\" condition\n// conditions: [\"default\", \"worker\", \"import\", \"browser\"]\nresolve.exports(pkg, 'foobar/lite', {\n  conditions: ['worker'],\n  browser: true\n}); //=> [\"./lite/worker.browser.js\"]\n\n// Disable non-\"default\" condition activate\n// NOTE: breaks from Node.js default behavior\n// conditions: [\"default\", \"custom\"]\nresolve.exports(pkg, 'foobar/lite', {\n  conditions: ['custom'],\n  unsafe: true,\n});\n//=> Error: No known conditions for \"./lite\" specifier in \"foobar\" package\n\n// ---\n// Imports\n// ---\n\n// conditions: [\"default\", \"import\", \"node\"]\nresolve.imports(pkg, '#hash');\nresolve.imports(pkg, 'foobar/#hash');\n//=> [\"./hash/node.mjs\"]\n\n// conditions: [\"default\", \"import\", \"browser\"]\nresolve.imports(pkg, '#hash', { browser: true });\nresolve.imports(pkg, 'foobar/#hash');\n//=> [\"./hash/web.mjs\"]\n\n// conditions: [\"default\"]\nresolve.imports(pkg, '#hash', { unsafe: true });\nresolve.imports(pkg, 'foobar/#hash');\n//=> [\"./hash/detect.mjs\"]\n\nresolve.imports(pkg, '#hello/world');\nresolve.imports(pkg, 'foobar/#hello/world');\n//=> Error: Missing \"#hello/world\" specifier in \"foobar\" package\n\n// ---\n// Legacy\n// ---\n\n// prefer \"module\" > \"main\" (default)\nresolve.legacy(pkg); //=> \"dist/module.mjs\"\n\n// customize fields order\nresolve.legacy(pkg, {\n  fields: ['main', 'module']\n}); //=> \"dist/require.js\"\n```\n\n## API\n\nThe [`resolve()`](#resolvepkg-entry-options), [`exports()`](#exportspkg-entry-options), and [`imports()`](#importspkg-target-options) functions share similar API signatures:\n\n```ts\nexport function resolve(pkg: Package, entry?: string, options?: Options): string[] | undefined;\nexport function exports(pkg: Package, entry?: string, options?: Options): string[] | undefined;\nexport function imports(pkg: Package, target: string, options?: Options): string[] | undefined;\n//                                         ^ not optional!\n```\n\nAll three:\n* accept a `package.json` file's contents as a JSON object\n* accept a target/entry identifier\n* may accept an [Options](#options) object\n* return `string[]`, `string`, or `undefined`\n\nThe only difference is that `imports()` must accept a target identifier as there can be no inferred default.\n\nSee below for further API descriptions.\n\n> **Note:** There is also a [Legacy Resolver API](#legacy-resolver)\n\n---\n\n### resolve(pkg, entry?, options?)\nReturns: `string[]` or `undefined`\n\nA convenience helper which automatically reroutes to [`exports()`](#exportspkg-entry-options) or [`imports()`](#importspkg-target-options) depending on the `entry` value.\n\nWhen unspecified, `entry` defaults to the `\".\"` identifier, which means that `exports()` will be invoked.\n\n```js\nimport * as r from 'resolve.exports';\n\nlet pkg = {\n  name: 'foobar',\n  // ...\n};\n\nr.resolve(pkg);\n//~> r.exports(pkg, '.');\n\nr.resolve(pkg, 'foobar');\n//~> r.exports(pkg, '.');\n\nr.resolve(pkg, 'foobar/subpath');\n//~> r.exports(pkg, './subpath');\n\nr.resolve(pkg, '#hash/md5');\n//~> r.imports(pkg, '#hash/md5');\n\nr.resolve(pkg, 'foobar/#hash/md5');\n//~> r.imports(pkg, '#hash/md5');\n```\n\n### exports(pkg, entry?, options?)\nReturns: `string[]` or `undefined`\n\nTraverse the `\"exports\"` within the contents of a `package.json` file. <br>\nIf the contents _does not_ contain an `\"exports\"` map, then `undefined` will be returned.\n\nSuccessful resolutions will always result in a `string` or `string[]` value. This will be the value of the resolved mapping itself – which means that the output is a relative file path.\n\nThis function may throw an Error if:\n\n* the requested `entry` cannot be resolved (aka, not defined in the `\"exports\"` map)\n* an `entry` _is_ defined but no known conditions were matched (see [`options.conditions`](#optionsconditions))\n\n#### pkg\nType: `object` <br>\nRequired: `true`\n\nThe `package.json` contents.\n\n#### entry\nType: `string` <br>\nRequired: `false` <br>\nDefault: `.` (aka, root)\n\nThe desired target entry, or the original `import` path.\n\nWhen `entry` _is not_ a relative path (aka, does not start with `'.'`), then `entry` is given the `'./'` prefix.\n\nWhen `entry` begins with the package name (determined via the `pkg.name` value), then `entry` is truncated and made relative.\n\nWhen `entry` is already relative, it is accepted as is.\n\n***Examples***\n\nAssume we have a module named \"foobar\" and whose `pkg` contains `\"name\": \"foobar\"`.\n\n| `entry` value | treated as | reason |\n|-|-|-|\n| `null` / `undefined` | `'.'` | default |\n| `'.'` | `'.'` | value was relative |\n| `'foobar'` | `'.'` | value was `pkg.name` |\n| `'foobar/lite'` | `'./lite'` | value had `pkg.name` prefix |\n| `'./lite'` | `'./lite'` | value was relative |\n| `'lite'` | `'./lite'` | value was not relative & did not have `pkg.name` prefix |\n\n\n### imports(pkg, target, options?)\nReturns: `string[]` or `undefined`\n\nTraverse the `\"imports\"` within the contents of a `package.json` file. <br>\nIf the contents _does not_ contain an `\"imports\"` map, then `undefined` will be returned.\n\nSuccessful resolutions will always result in a `string` or `string[]` value. This will be the value of the resolved mapping itself – which means that the output is a relative file path.\n\nThis function may throw an Error if:\n\n* the requested `target` cannot be resolved (aka, not defined in the `\"imports\"` map)\n* an `target` _is_ defined but no known conditions were matched (see [`options.conditions`](#optionsconditions))\n\n#### pkg\nType: `object` <br>\nRequired: `true`\n\nThe `package.json` contents.\n\n#### target\nType: `string` <br>\nRequired: `true`\n\nThe target import identifier; for example, `#hash` or `#hash/md5`.\n\nImport specifiers _must_ begin with the `#` character, as required by the resolution specification. However, if `target` begins with the package name (determined by the `pkg.name` value), then `resolve.exports` will trim it from the `target` identifier. For example, `\"foobar/#hash/md5\"` will be treated as `\"#hash/md5\"` for the `\"foobar\"` package.\n\n## Options\n\nThe [`resolve()`](#resolvepkg-entry-options), [`imports()`](#importspkg-target-options), and [`exports()`](#exportspkg-entry-options) functions share these options. All properties are optional and you are not required to pass an `options` argument.\n\nCollectively, the `options` are used to assemble a list of [conditions](https://nodejs.org/docs/latest-v18.x/api/packages.html#conditional-exports) that should be activated while resolving your target(s).\n\n> **Note:** Although the Node.js documentation primarily showcases conditions alongside `\"exports\"` usage, they also apply to `\"imports\"` maps too. _([example](https://nodejs.org/docs/latest-v18.x/api/packages.html#subpath-imports))_\n\n#### options.require\nType: `boolean` <br>\nDefault: `false`\n\nWhen truthy, the `\"require\"` field is added to the list of allowed/known conditions. <br>\nOtherwise the `\"import\"` field is added instead.\n\n#### options.browser\nType: `boolean` <br>\nDefault: `false`\n\nWhen truthy, the `\"browser\"` field is added to the list of allowed/known conditions. <br>\nOtherwise the `\"node\"` field is added instead.\n\n#### options.conditions\nType: `string[]` <br>\nDefault: `[]`\n\nA list of additional/custom conditions that should be accepted when seen.\n\n> **Important:** The order specified within `options.conditions` does not matter. <br>The matching order/priority is **always** determined by the `\"exports\"` map's key order.\n\nFor example, you may choose to accept a `\"production\"` condition in certain environments. Given the following `pkg` content:\n\n```js\nconst pkg = {\n  // package.json ...\n  \"exports\": {\n    \"worker\": \"./$worker.js\",\n    \"require\": \"./$require.js\",\n    \"production\": \"./$production.js\",\n    \"import\": \"./$import.mjs\",\n  }\n};\n\nresolve.exports(pkg, '.');\n// Conditions: [\"default\", \"import\", \"node\"]\n//=> [\"./$import.mjs\"]\n\nresolve.exports(pkg, '.', {\n  conditions: ['production']\n});\n// Conditions: [\"default\", \"production\", \"import\", \"node\"]\n//=> [\"./$production.js\"]\n\nresolve.exports(pkg, '.', {\n  conditions: ['production'],\n  require: true,\n});\n// Conditions: [\"default\", \"production\", \"require\", \"node\"]\n//=> [\"./$require.js\"]\n\nresolve.exports(pkg, '.', {\n  conditions: ['production', 'worker'],\n  require: true,\n});\n// Conditions: [\"default\", \"production\", \"worker\", \"require\", \"node\"]\n//=> [\"./$worker.js\"]\n\nresolve.exports(pkg, '.', {\n  conditions: ['production', 'worker']\n});\n// Conditions: [\"default\", \"production\", \"worker\", \"import\", \"node\"]\n//=> [\"./$worker.js\"]\n```\n\n#### options.unsafe\nType: `boolean` <br>\nDefault: `false`\n\n> **Important:** You probably do not want this option! <br>It will break out of Node's default resolution conditions.\n\nWhen enabled, this option will ignore **all other options** except [`options.conditions`](#optionsconditions). This is because, when enabled, `options.unsafe` **does not** assume or provide any default conditions except the `\"default\"` condition.\n\n```js\nresolve.exports(pkg, '.');\n//=> Conditions: [\"default\", \"import\", \"node\"]\n\nresolve.exports(pkg, '.', { unsafe: true });\n//=> Conditions: [\"default\"]\n\nresolve.exports(pkg, '.', { unsafe: true, require: true, browser: true });\n//=> Conditions: [\"default\"]\n```\n\nIn other words, this means that trying to use `options.require` or `options.browser` alongside `options.unsafe` will have no effect. In order to enable these conditions, you must provide them manually into the `options.conditions` list:\n\n```js\nresolve.exports(pkg, '.', {\n  unsafe: true,\n  conditions: [\"require\"]\n});\n//=> Conditions: [\"default\", \"require\"]\n\nresolve.exports(pkg, '.', {\n  unsafe: true,\n  conditions: [\"browser\", \"require\", \"custom123\"]\n});\n//=> Conditions: [\"default\", \"browser\", \"require\", \"custom123\"]\n```\n\n## Legacy Resolver\n\nAlso included is a \"legacy\" method for resolving non-`\"exports\"` package fields. This may be used as a fallback method when for when no `\"exports\"` mapping is defined. In other words, it's completely optional (and tree-shakeable).\n\n### legacy(pkg, options?)\nReturns: `string` or `undefined`\n\nYou may customize the field priority via [`options.fields`](#optionsfields).\n\nWhen a field is found, its value is returned _as written_. <br>\nWhen no fields were found, `undefined` is returned. If you wish to mimic Node.js behavior, you can assume this means `'index.js'` – but this module does not make that assumption for you.\n\n#### options.browser\nType: `boolean` or `string` <br>\nDefault: `false`\n\nWhen truthy, ensures that the `'browser'` field is part of the acceptable `fields` list.\n\n> **Important:** If your custom [`options.fields`](#optionsfields) value includes `'browser'`, then _your_ order is respected. <br>Otherwise, when truthy, `options.browser` will move `'browser'` to the front of the list, making it the top priority.\n\nWhen `true` and `\"browser\"` is an object, then `legacy()` will return the the entire `\"browser\"` object.\n\nYou may also pass a string value, which will be treated as an import/file path. When this is the case and `\"browser\"` is an object, then `legacy()` may return:\n\n* `false` – if the package author decided a file should be ignored; or\n* your `options.browser` string value – but made relative, if not already\n\n> See the [`\"browser\" field specification](https://github.com/defunctzombie/package-browser-field-spec) for more information.\n\n#### options.fields\nType: `string[]` <br>\nDefault: `['module', 'main']`\n\nA list of fields to accept. The order of the array determines the priority/importance of each field, with the most important fields at the beginning of the list.\n\nBy default, the `legacy()` method will accept any `\"module\"` and/or \"main\" fields if they are defined. However, if both fields are defined, then \"module\" will be returned.\n\n```js\nimport { legacy } from 'resolve.exports';\n\n// package.json\nconst pkg = {\n  \"name\": \"...\",\n  \"worker\": \"worker.js\",\n  \"module\": \"module.mjs\",\n  \"browser\": \"browser.js\",\n  \"main\": \"main.js\",\n};\n\nlegacy(pkg);\n// fields = [module, main]\n//=> \"module.mjs\"\n\nlegacy(pkg, { browser: true });\n// fields = [browser, module, main]\n//=> \"browser.mjs\"\n\nlegacy(pkg, {\n  fields: ['missing', 'worker', 'module', 'main']\n});\n// fields = [missing, worker, module, main]\n//=> \"worker.js\"\n\nlegacy(pkg, {\n  fields: ['missing', 'worker', 'module', 'main'],\n  browser: true,\n});\n// fields = [browser, missing, worker, module, main]\n//=> \"browser.js\"\n\nlegacy(pkg, {\n  fields: ['module', 'browser', 'main'],\n  browser: true,\n});\n// fields = [module, browser, main]\n//=> \"module.mjs\"\n```\n\n## License\n\nMIT © [Luke Edwards](https://lukeed.com)\n", "readmeFilename": "readme.md", "users": {"flumpus-dev": true}}