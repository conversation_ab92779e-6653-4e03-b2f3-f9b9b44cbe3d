{"_id": "human-signals", "_rev": "18-8eab02447440d00fe1126e9ea8d3c264", "name": "human-signals", "dist-tags": {"latest": "8.0.1"}, "versions": {"1.1.0": {"name": "human-signals", "version": "1.1.0", "keywords": ["signal", "signals", "handlers", "error-handling", "errors", "interrupts", "sigterm", "sigint", "irq", "process", "exit", "exit-code", "status", "operating-system", "es6", "javascript", "linux", "macos", "windows", "nodejs"], "author": {"url": "https://github.com/ehmicky", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "human-signals@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://git.io/JeluP", "bugs": {"url": "https://github.com/ehmicky/human-signals/issues"}, "dist": {"shasum": "6f5878f93f94776ff667b6d8666d5d9cad6b524c", "tarball": "https://registry.npmjs.org/human-signals/-/human-signals-1.1.0.tgz", "fileCount": 12, "integrity": "sha512-Gvq61o07QrVxxSaRTAJZnzYaoE31l8snVpwZgTgcojYwuQqkYWVRlRfamtxJV3PdV+4Nmp2wWD6htI9tHkFJkA==", "signatures": [{"sig": "MEYCIQDthVrXF6DDJc9OYbZdpclKa6fVAqsMlUV7K9v+2I+R6QIhAM486mEO6SBcEL8zpOwZyLJLl4GDT+iaHAiMrUG8L6zy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42695, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdoiEKCRA9TVsSAnZWagAA5qgP+QGBrKiA+LP5fEhBmoN7\nzYRaW0la1AwlzclSbUphbm05UdxG1LHIOBPxsHx8gqHj+FTuWaV8yZJ+im0I\nc0yUTTlkRRuOCv/Nti6Bxf0XdC2FDC1lVnfpP/aQhUEJkocGdqfinYOvu6sK\nqPWnJgEdOub6FZBL2TnhV0+W5eN2Nn57xBqyLaZmgOvcH+zGnIhvO07hltxd\nzUu8mkOG23Gd/nr8Q8tzvt82wGx/S0u7BWWZdmnQEhvjXx9skQzSEe/GZjeb\nKPPKrnbmWAKsj2r6KDgBQKIlPDPv9kayrEQjRy+aOEeWGIAJlD1BWAi0W9LT\nyEv+nNQAQAimMcE2mkgefNyZVJuzHN+29ZzGZ/jKiJHQ4dxSekVQDiIEKCWe\n6N0bxZoCs81Ps4Kq2AmuEk1uBm/DwrE9g72FgznCSutHxvTUFA+hxa74r0Gm\nWTwH+2Eeti31HPv5urMN0dDVH8gezdtkqo9HGhhNsL8rF1FGoFRjmcMLA7rY\nFzU6XfH4g+9cBmABNlZtGpjXxD7KXCiO63htpvrq+P4rxDb36gAOS58r9OwC\nph55OlXHkFfqAm0gOl2+4hIFIT5LRZK6oeKkmK4mfWW/E675zQzPbL2i6vU1\n9JxHBSddeQaMtnyjxpd8Y0hi71ibjvaLcP5rgYN22FFJpHebWnGO/AK2wmGn\nHBNm\r\n=3k+d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/src/main.js", "husky": {"hooks": {"pre-push": "gulp check --full"}}, "engines": {"node": ">=8.12.0"}, "gitHead": "a75bbce358f69bbf50c526f91baa19faf8451cf0", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ehmicky/human-signals.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Human-friendly process signals", "directories": {"lib": "src", "test": "test"}, "_nodeVersion": "12.12.0", "dependencies": {"core-js": "^3.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"ajv": "^6.10.2", "ava": "^2.4.0", "gulp": "^4.0.2", "husky": "^3.0.8", "test-each": "^1.7.2", "fast-deep-equal": "^2.0.1", "@ehmicky/dev-tasks": "^0.30.46"}, "_npmOperationalInternal": {"tmp": "tmp/human-signals_1.1.0_1570906378356_0.5954442608497985", "host": "s3://npm-registry-packages"}}, "1.1.1": {"name": "human-signals", "version": "1.1.1", "keywords": ["signal", "signals", "handlers", "error-handling", "errors", "interrupts", "sigterm", "sigint", "irq", "process", "exit", "exit-code", "status", "operating-system", "es6", "javascript", "linux", "macos", "windows", "nodejs"], "author": {"url": "https://github.com/ehmicky", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "human-signals@1.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://git.io/JeluP", "bugs": {"url": "https://github.com/ehmicky/human-signals/issues"}, "dist": {"shasum": "c5b1cd14f50aeae09ab6c59fe63ba3395fe4dfa3", "tarball": "https://registry.npmjs.org/human-signals/-/human-signals-1.1.1.tgz", "fileCount": 12, "integrity": "sha512-SEQu7vl8KjNL2eoGBLF3+wAjpsNfA9XMlXAYj/3EdaNfAlxKthD1xjEQfGOUhllCGGJVNY34bRr6lPINhNjyZw==", "signatures": [{"sig": "MEQCIANn3IJwQzrANbriamJnIp5+yGBatpbAt2YDJYNEqg1YAiAzj1d432NlkVBHDFmSL0iai0PMOaRfanVlyd5Gdl7Mrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42370, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdpu1CCRA9TVsSAnZWagAA1CgP/2qzo9Wi/ZxPyNsa/Jub\n7uGv6TLBjWDAHuX+0j3Gw+TgFDLG+fojYbR/TwgSSDl3ao+p+jSu6pA5ZJ9Y\nrlu8wpxDvDd9T4OqizhLlr2j41aJevuOQIKQoxVcZcBggNgnZIstYeytk5u3\n3gWosf9U4NXjvb+sYNXhC9XC9gVnhyX4PmcReWijpp7KSqWSGvvsbE2YFxWJ\n+sfzfOGk7q30AHvisaYg/eEv3frzQS/QBL4zA0Vzk5LiK8hX7ZUWMxqosNp2\nhNuvujD95Hz24NKWzWMd2PixarKAcA72BUZkMPbMmjU+stuvKA6um+uV6lNv\n+KZMzWkzbqJ2KhZXg8o7CnMLQnOHxT9kb23IFCMhBAahHqFsR5+EaaRdSfmC\nTMr0KaFT3oB8rdiztREp3s/VJOFdfSTZkkCXKKxSQhzjSRurY5Kq0nrV9tCS\nyQYCCgn52RaMoaEzZeGwKCdmvT8Q5YMYNtAkm6+6LqQNA4XNF1/nJEK/tHk3\nF9wFBnZM1BhX+pg/BA3a+elI6rnXDk92Rfgtybt69CgaSaxWrwGgRaczXKxC\n9HpxtyLSMcIc93IGnGYvOyw8TOPHlUXIOz8TBLzHFVytRIJFGItI/H/TXInY\nHpO4ktePMvtdTEi0bNNsJjKTNQZfCEwXmkyR/OUCVwA5R9S7Da6p5ZPBplqg\n9YWq\r\n=WI4h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/src/main.js", "husky": {"hooks": {"pre-push": "gulp check --full"}}, "engines": {"node": ">=8.12.0"}, "gitHead": "10ad2027d34ce8d93f72ff1458d5c0e536d89b28", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ehmicky/human-signals.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Human-friendly process signals", "directories": {"lib": "src", "test": "test"}, "_nodeVersion": "12.12.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"ajv": "^6.10.2", "ava": "^2.4.0", "gulp": "^4.0.2", "husky": "^3.0.9", "test-each": "^1.7.2", "fast-deep-equal": "^2.0.1", "@ehmicky/dev-tasks": "^0.30.48"}, "_npmOperationalInternal": {"tmp": "tmp/human-signals_1.1.1_1571220802278_0.05624079018996775", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "human-signals", "version": "2.0.0", "keywords": ["signal", "signals", "handlers", "error-handling", "errors", "interrupts", "sigterm", "sigint", "irq", "process", "exit", "exit-code", "status", "operating-system", "es6", "javascript", "linux", "macos", "windows", "nodejs"], "author": {"url": "https://github.com/ehmicky", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "human-signals@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://git.io/JeluP", "bugs": {"url": "https://github.com/ehmicky/human-signals/issues"}, "dist": {"shasum": "e5840924f83b9b9146dad6252e31f63855b6d568", "tarball": "https://registry.npmjs.org/human-signals/-/human-signals-2.0.0.tgz", "fileCount": 12, "integrity": "sha512-61H5jEHa2GyZm2EgzVpewkhtcZsCqq0qOjHmFpX6N692CXx4TyQgDN4QmP8DJAR5Yu4NafbZav8zxM9r8Lee/Q==", "signatures": [{"sig": "MEUCIQCtw9MototlcwCHFWW1hrG2Ej7hCVshGbXgfSL2lBZAMwIgWKQFyx/hLnNPoUH1/VEZMY82M9fNGewKS3ITsKuqvcg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42416, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeA4oyCRA9TVsSAnZWagAACJ0P/0Mv4jL1x/307ZCG4tqY\n5bvHuucIYmXiMmBsFqjih8lP1UmHIM7/RNpjlZFjIadKw/zNyDxnJCuucHH5\nImM95rib0vE3cG7MNxqf9gux3oQAq8g+/GbdO5qRLtsXl1zOYgjmeRLIjrVk\n7EsV3a3V9mltlvkWujM3VOsqB4mso2R8j88l3B7zvyd2H8fft6m/S7ruvytk\n+ZcZrvpHJ+AF/74/74EvExuGFoYXEbFIz53KGjqe0A0VeScnH4s8gHeupEoH\njuUHu5X37H+peXKHJsnr1gokY5yrVLkli6G0e8kKpdybvHzoQ3ArMV0YDhmE\njlvius8eBt1CHgOKhOczBTY19le6vRf7HehUy6h331KoJMsonkiDDpIlMjPO\nOiU9O4ZiwWLk8/EO/mCqN8u9wbf0snC0n+JDlnObjTm9eftD/OzWVtaLSVi/\nuPgb6YvLuwWKSkdXUkAuCu3/L6Yz0j/0XqlEeVmaNK2TUzAhK2pEl29cl4RJ\n48tgs2VyfZg5zjtyq/Mo4KhO9Zi4c8l0bjZan+hDbI6tFEQQTU662s76IneL\nbFpBugJfk9ji0ufY56Ji8dNYJD3R64tJDoNfDw/UnNJWVDv1A2YSB7FYy4Ss\nErgbmyl2lnjunKWfuo6qC3KjxGrHpA9yH3OCETwZ+fBdrVw9/rbTKvxvWzcB\nnp6O\r\n=by17\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/src/main.js", "husky": {"hooks": {"pre-push": "gulp check --full"}}, "engines": {"node": ">=10.17.0"}, "gitHead": "b9d9225669a1f3b4c6b39343bbac18e9cdc7bf95", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ehmicky/human-signals.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Human-friendly process signals", "directories": {"lib": "src", "test": "test"}, "_nodeVersion": "13.5.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"ajv": "^6.10.2", "ava": "^2.4.0", "gulp": "^4.0.2", "husky": "^3.1.0", "test-each": "^2.0.0", "@ehmicky/dev-tasks": "^0.31.0"}, "_npmOperationalInternal": {"tmp": "tmp/human-signals_2.0.0_1577290289915_0.459201984412124", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "human-signals", "version": "2.1.0", "keywords": ["signal", "signals", "handlers", "error-handling", "errors", "interrupts", "sigterm", "sigint", "irq", "process", "exit", "exit-code", "status", "operating-system", "es6", "javascript", "linux", "macos", "windows", "nodejs"], "author": {"url": "https://github.com/ehmicky", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "human-signals@2.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://git.io/JeluP", "bugs": {"url": "https://github.com/ehmicky/human-signals/issues"}, "dist": {"shasum": "dc91fcba42e4d06e4abaed33b3e7a3c02f514ea0", "tarball": "https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz", "fileCount": 13, "integrity": "sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==", "signatures": [{"sig": "MEYCIQCvErluUHL0KZ6YFZLL7HMNxQEGmTb6DDkdLEwSxRQHYAIhALxMdTYH3cXcZwFlAJPYkC21+qSXpuBdHPnMo10JWwHD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44307, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJebPPtCRA9TVsSAnZWagAAnIMP/imfQdtvcXuwUgUSAZ3m\nT3G3yggoHDY1xQF8yaFa7Zpcs0uf972sNkdkDzrx/r4z1hmQm8j7K/YaHtht\n651ZuUj3LEwhnJk/Wgr9OwWD5zT773jYQxY4AsfheUt4RLHHdKR47B3d4fTU\n8/Xm/R4sNI0Aen1V2B8sX3GdwbxZICeSqiiJ/+pmEWDLa7Vf6sZ1ZK2M5kOL\nDJVwO59BdfgXmk5ipoO5ViOa0Tbu/HwfJ23NzMj/Ibz8cpG2npvjF2sqA35Z\nfNI/nmFsZIUuTRBzs96qpiMCjsdsr4FIBeP4AvUa5V3o3R85B/wzHyFiZhjQ\n+d392hF27+JC8RRwCHTYaQsKkhA78NNzYWr/ddWua4UsLB8O3iYMf+JkbsH+\ncfBrib+XOMHj7z7u3kaHth+DB8k5c5zHR1k6yVSuzI/0tCB7oXW6FrMURc3d\nKQscVoY/iWrISmVRP41AuW+qowQmoYQnIoioz1h+PheON18J6yBi3AYyi1a6\n+clCjbzCFSFEQQxqU16ztaJWx0CjKGALugG3V41R8u+ww+ze4/JG55X2cES4\nhQHLgwwkSDHGqpU+Kzd9bQcg2BVqIfGKV8c6Z/9aZnT0sbX/VTfpoEy8wvHH\n81g1u0cQzqBQmKg2FP2Tg0UlpOvlE8FAwMagOtBM+rCNm4wWWJAyRvErRrIK\nV+OX\r\n=oN4G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/src/main.js", "husky": {"hooks": {"pre-push": "gulp check --full"}}, "types": "build/src/main.d.ts", "engines": {"node": ">=10.17.0"}, "gitHead": "55a61f4716b23a817de686773ed0f7ec32901650", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ehmicky/human-signals.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "Human-friendly process signals", "directories": {"lib": "src", "test": "test"}, "_nodeVersion": "13.11.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"ajv": "^6.12.0", "ava": "^3.5.0", "gulp": "^4.0.2", "husky": "^4.2.3", "test-each": "^2.0.0", "@ehmicky/dev-tasks": "^0.31.9"}, "_npmOperationalInternal": {"tmp": "tmp/human-signals_2.1.0_1584198636813_0.43863526583602575", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "human-signals", "version": "3.0.0", "keywords": ["signal", "signals", "handlers", "error-handling", "errors", "interrupts", "sigterm", "sigint", "irq", "process", "exit", "exit-code", "status", "operating-system", "es6", "javascript", "linux", "macos", "windows", "nodejs"], "author": {"url": "https://github.com/ehmicky", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "human-signals@3.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://git.io/JeluP", "bugs": {"url": "https://github.com/ehmicky/human-signals/issues"}, "dist": {"shasum": "1fb08b80a56970caf6b46dec5c3490873ccdee65", "tarball": "https://registry.npmjs.org/human-signals/-/human-signals-3.0.0.tgz", "fileCount": 12, "integrity": "sha512-LAb3cP5qixXejgqmdlY98KEQeZoHzVyEFsVqJshHToqO02bIf9stSxCf99xG/K3Pin1QC8TmSZoL9XRCKlhEzQ==", "signatures": [{"sig": "MEQCICFQ8vp3gaZyIeZhwH68uO+zmGAXc9lC4X/7S3euR4vAAiAPgr8L50vcPbVIfIFrwRoEV67Uj4NhPfxNr17RzLHoKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg0cw6CRA9TVsSAnZWagAAQAQQAJmdol59XLIW3Dvsl+aM\naeCr+GRgXVM6TyQ4wRkOlhnlCIZtsuuSAwS5M7yVGrMsOgihPDIxEX/0G2eP\nvuSOHuxVSsBtigosSoBGURrVCkaAqov5VPSOBiNwcX1npI72QY2A9Dkual8d\nOp4uDNFdR1wch+pDSYMqxIYer1Y8kKuktUQPQI5BFaUyNP8FC4ejCvQlvENY\ngFfXj+Vscwb+AKZ5SSKqSOPCwf+y1JiVznQ2F0ue6xtM0sTXDyDcLesFtlix\n8UvSpLnlWCb4V6M6LiJlQkLQSAb83HJOyPK19iPCchTpBFtAv7XavkOQKRJW\nLdtkBo10f6vmQAv5ClEVlZdwpxgKURdxkGGz68/9dYCMVvQtXTFOdK+56dKD\nAxHRnq0p7xkowv81oRDSWVRxo47jU1WXYrqPesZGH2kB8L8d47bRZttuPv+w\nv6CUmV+6q4VvUAYcnVTeH/DYNWPCtiRh2wH/e/ttyxmfOQT8YXyMnKDPldNg\nuhx97svktm4JOOskB7i0z5mFqScmNcEuvZmXcg+uOGluNTqJ/eibxPC6xgMC\nIr/jKVZuVg3ehdNyVgzC68LOtVyNV+zdYYmjgyqNuxjJ5xWEBiGWvYlZ7oca\nTE5tTDYshffPpIOhOmCtAX+ai/PTE+AhG3ZObnmrxf+4+XOGdZd2jb3kK6Ep\nScuC\r\n=ox6p\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "build/src/main.d.ts", "engines": {"node": ">=12.20.0"}, "exports": "./build/src/main.js", "gitHead": "07be36f5f22e5cf897040e0ad9e68c2f568129d5", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ehmicky/human-signals.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Human-friendly process signals", "directories": {"lib": "src", "test": "test"}, "_nodeVersion": "16.3.0", "_hasShrinkwrap": false, "devDependencies": {"ajv": "^6.12.6", "test-each": "^3.0.0", "@ehmicky/dev-tasks": "^1.0.33"}, "_npmOperationalInternal": {"tmp": "tmp/human-signals_3.0.0_1624362042440_0.08806899572815463", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "human-signals", "version": "3.0.1", "keywords": ["signal", "signals", "handlers", "error-handling", "errors", "interrupts", "sigterm", "sigint", "irq", "process", "exit", "exit-code", "status", "operating-system", "es6", "javascript", "linux", "macos", "windows", "nodejs"], "author": {"url": "https://github.com/ehmicky", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "human-signals@3.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://git.io/JeluP", "bugs": {"url": "https://github.com/ehmicky/human-signals/issues"}, "dist": {"shasum": "c740920859dafa50e5a3222da9d3bf4bb0e5eef5", "tarball": "https://registry.npmjs.org/human-signals/-/human-signals-3.0.1.tgz", "fileCount": 12, "integrity": "sha512-rQLskxnM/5OCldHo+wNXbpVgDn5A17CUoKX+7Sokwaknlq7CdSnphy0W39GU8dw59XiCXmFXDg4fRuckQRKewQ==", "signatures": [{"sig": "MEQCIHjIgUFqrjsJeeT3FLwcBVd2sKO9WV2Uw4bsi9dbSjpgAiA8vWFu60yy42/PgvNUwgcOU4my7RQ3aJOHixgIYg+RiA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg0dYnCRA9TVsSAnZWagAAmcoP/0/gklEn8DlT19SyidFP\nKpW7qnfncaPCottVlKQc6Z5J/aGfQX0i3j62nuH93+mX+uD8S8ZKn7neH603\nd+6YF6ka7+ucebFOWcyVn3ZUCjF38EJXW5EHE9Xcu/vksaisGPHmE6xKJnBN\ne3FxWWDTlw0IHoC/o7AxXyi4X4O5Xg0OLIiA0dcF5mqh4E6GVOekBqeJjd6C\nOaM+9f0bnjjXmjtvuAPe976YtmWwIqrsTmExsHzTGSql+sqfOXBqdmVPz9+P\nDVvq6IYvGprq1rAaSv3QHHn2dRmNnpbMLbCTx4U14OWddPmIPOxcTMm2r6CN\nkQncjxH8ooc/7ZJ5UYxXBDy+SDOWQ+0FCLUr7nxFOuH62i7QQF79yth5BihW\n1Z+hjisqwNW1LXhGrpmLHLxTkSB9mSGoefPgBLduGRdGkRbZACzlqZ468EBv\n9sjx/0fNF89MfNxTbg3nniE5z9kF4O0sUPsZnLsnp+vpEjY6Yi6eSjo+6AZd\nnfdFDhCvO4JvCT81XUWogkGawHyQfQ5fzlKsBFpCQVv9+cboK9kCjJAohANW\nIEI0MKRlyOv+Gx/IcqZopTJELk9/8sxXVQlM5pGx7kG2l2+VfCgrRFwpDj7B\nS5GjAgOAmDWTsxYrXE/uxvsO1O03o0k+CyUPghfA5EqG0roy/r7S8TYiFCz7\nJ0pq\r\n=RMFe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/src/main.js", "type": "module", "types": "build/src/main.d.ts", "engines": {"node": ">=12.20.0"}, "exports": "./build/src/main.js", "gitHead": "74484255bc7c9e2189b938c45d2dd308fdc3774d", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ehmicky/human-signals.git", "type": "git"}, "_npmVersion": "7.18.1", "description": "Human-friendly process signals", "directories": {"lib": "src", "test": "test"}, "_nodeVersion": "16.3.0", "_hasShrinkwrap": false, "devDependencies": {"ajv": "^6.12.6", "test-each": "^3.0.1", "@ehmicky/dev-tasks": "^1.0.34"}, "_npmOperationalInternal": {"tmp": "tmp/human-signals_3.0.1_1624364583358_0.3334378072500639", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "human-signals", "version": "4.0.0", "keywords": ["signal", "signals", "handlers", "error-handling", "errors", "interrupts", "sigterm", "sigint", "irq", "process", "exit", "exit-code", "status", "operating-system", "es6", "javascript", "linux", "macos", "windows", "nodejs"], "author": {"url": "https://github.com/ehmicky", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "human-signals@4.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://git.io/JeluP", "bugs": {"url": "https://github.com/ehmicky/human-signals/issues"}, "dist": {"shasum": "1ec7830796cce7b399a7d06df6e52901c0f76956", "tarball": "https://registry.npmjs.org/human-signals/-/human-signals-4.0.0.tgz", "fileCount": 12, "integrity": "sha512-TuH7OIjWLLYnKPDLD3ANaVZM+HzpozwBr6G9Yd//fWBnIwF+9nKhscmcfhZCxFHjFzGoGBHe/eXcg73DPZsekA==", "signatures": [{"sig": "MEQCICP/S03hLMkDw2srxDEaQLA4YxMd8PeT8X0UdUdBWfFFAiAY7gA8QAeb7nhKrYssVqHASNeSsY0nSW3Xh61LucBxdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43549, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieXliACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrWzg//XwVwyIYvYsh0xsmWnvbHtr7GNkYWWAvlvjSF3EiWvr2HXrxD\r\nG98LqiK/hBtpUGjBgLdaNiY6s/PTCGbxBCGBjsOx+tAaBDmt13OM2bfJvfd3\r\nN2zV+OiIXMzRK+eGSPzkIRs6aLMPreUwl3VJgDhOHCeIy5rPdk7ilMsYrk22\r\n9dsL/mWtrq5HlICzhE8be3aaB4Mko8oxy2oBemuGZFy4UrmqjILjWypYJyAB\r\nqEvjl8lb9zWpFExdAHLAQkEgnYxhM91oTIQ4tMcdm1MXnxuCWgZMGn7td+1Z\r\nU42hL1hapB2LTBkLHzSb2Cwoen9AdnhaELLr5p3YCIHMt+1FWyUK3/+qcJWJ\r\nIyQR56oOKD/fWiWVegPvv/Mm+dSsTU9X4H5YFX/3flGX0ABIVlaXHWXaoMUL\r\nYVF0VpZcUSf75Tmt+jHzqX0qz4f79vIRLZeJupfiK2ZlGuxn5lIYLWjDZUeW\r\nDDRGndoaYqhnwQT1aEC8PRGEmj5ItZb98/G/Ks8dQzbX8Us0HhwrO+EZ0WAo\r\nU57eGT+/zAJJdKUYJ8aS/MseF+rfBzza5Rbob7GbMZdPhZk78eoYMBfc6faM\r\nISJjkSbPp/8s4eF1XGyTJHnpHYZNQBaDj4pzRJ5g/o5A3i0YrTlxDP7ZFiNq\r\nD/PuQhOog6M64YZaZxChKU3YErREhf/e9oo=\r\n=Gxja\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/src/main.js", "type": "module", "types": "build/src/main.d.ts", "engines": {"node": ">=14.18.0"}, "exports": "./build/src/main.js", "gitHead": "c479b1f336ce81a13afd8cf819b896be6a315fe3", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ehmicky/human-signals.git", "type": "git"}, "_npmVersion": "8.9.0", "description": "Human-friendly process signals", "directories": {"lib": "src", "test": "test"}, "_nodeVersion": "18.1.0", "_hasShrinkwrap": false, "devDependencies": {"ajv": "^6.12.6", "test-each": "^5.0.0", "@ehmicky/dev-tasks": "^1.0.72"}, "_npmOperationalInternal": {"tmp": "tmp/human-signals_4.0.0_1652128098621_0.7803997579014939", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "human-signals", "version": "4.1.0", "keywords": ["signal", "signals", "handlers", "error-handling", "interrupts", "sigterm", "sigint", "irq", "process", "exit", "exit-code", "status", "operating-system", "es6", "javascript", "typescript", "linux", "macos", "windows", "nodejs"], "author": {"url": "https://github.com/ehmicky", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "human-signals@4.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://git.io/JeluP", "bugs": {"url": "https://github.com/ehmicky/human-signals/issues"}, "dist": {"shasum": "38a39ac93efa07379f1d661c36fbfeefe0e04cc0", "tarball": "https://registry.npmjs.org/human-signals/-/human-signals-4.1.0.tgz", "fileCount": 12, "integrity": "sha512-CScwiWvVzh2MwVkXeJVUYRxI2tW+vU8926kZX6IT100ICFu9yh12mxJ0uEP4ovHfzP0TzxiKfByCy+kWCCTW4A==", "signatures": [{"sig": "MEQCIFjjGlp5KcOdYFfA7z8xDyubhSQMNa/t3HhMbjKBOqDjAiAfNgMM3BOJBfUSugUlV6nRBj0q+Uiq8kWENpFp1FNmhQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43797, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuLPfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpj9g//bMfuE59LOiELQ5IrBhp1CXWuEX92PDf3FuhQ1FkhNKf8Nste\r\nvZaI9MRSw4mbWwEvUjQcxsA174TVMBwDiQzM1d4CSitDeUJq0aGoyqimg4Sp\r\nJ/F0Pq2PqBjUgv2gFWe0ajVJHZFdfCpu3/4wl9jGlDH720CSIx/nd0salY9w\r\nuJzcVkXIVbJ95O321bQVDK10S9dvCbSAahtfTIlNb2XfYsbIptdG1IGaGzpZ\r\nw14W/l7VPiqu5SyeCUf7sv4C2hBkQrQ9nkm/BSk9sJ9NuV9OO1T47NH3A9k7\r\nOwvC62axDPkXYs0Ha/muZCSPadvTF0WWeeXqxMDNMo1iB8G2H/w3oQBSE/oK\r\nY1spCwZUxtsQcq5ipMovZJEovgs+7wCYqk7rLsy9X8wAea3hPZ19dQH32sIM\r\niOcIF5uTkzeOQ0OIBaSBu2D4IxeFq0zFH85jvpFl1IIeoQpYCqswEfg0o06d\r\no5WMhnJBPMvfcM1P6FCVxv2SVtlkF4oJ/0hZOiyRM7qHbn1dqMuxmK2s2UcY\r\nfJUbC9PQ+pDfbgmLsWEx7ywgpCIQjXh9dMSlWNKzk3aRy06gE8ENL0xMDkeF\r\niB+MFNvwIoIALOCUTAoJd57lwKjDMx6RTS6MtpmtQ6MF3PcrP5GL7hnSdGGk\r\n+MMeockJT+fYAn9uP22M3J78vniIK9LHueo=\r\n=f2zK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/src/main.js", "type": "module", "types": "./build/src/main.d.ts", "engines": {"node": ">=14.18.0"}, "exports": "./build/src/main.js", "gitHead": "1570dd1e651c3fa56e8daafa7b5bf70e24d7a705", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ehmicky/human-signals.git", "type": "git"}, "_npmVersion": "8.13.1", "description": "Human-friendly process signals", "directories": {"lib": "src", "test": "test"}, "_nodeVersion": "18.4.0", "_hasShrinkwrap": false, "devDependencies": {"ajv": "^8.11.0", "tsd": "^0.21.0", "test-each": "^5.0.0", "@ehmicky/dev-tasks": "^1.0.77"}, "_npmOperationalInternal": {"tmp": "tmp/human-signals_4.1.0_1656271839659_0.5569450327286201", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "human-signals", "version": "4.2.0", "keywords": ["signal", "signals", "handlers", "error-handling", "interrupts", "sigterm", "sigint", "irq", "process", "exit", "exit-code", "status", "operating-system", "es6", "javascript", "typescript", "linux", "macos", "windows", "nodejs"], "author": {"url": "https://github.com/ehmicky", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "human-signals@4.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://www.github.com/ehmicky/human-signals", "bugs": {"url": "https://github.com/ehmicky/human-signals/issues"}, "dist": {"shasum": "f29102d4a753d96c86e4ebeed7bc0960793ba7dc", "tarball": "https://registry.npmjs.org/human-signals/-/human-signals-4.2.0.tgz", "fileCount": 8, "integrity": "sha512-KdFG7tcDGNVR/qR6LUOiAlF5zNf99UEVmoRT+UnIaO1+tj3Cc4qozyuUmYvA6OtoDyb7HPo2A+SwFmNKAOgw2A==", "signatures": [{"sig": "MEQCIE2nUMxs3366FJojRRJeqdx0R01fYqffvcjteD3ibeTqAiBB3yYQwgP+b+yA4jZoHYNQIUkfrGDZy78dKsFc5A+vkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25838, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi++0+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpl5RAAodbC3ffmV/fGZl+hyJ1V0Mkb85RipLk5x1dq5geQvAHupDHi\r\npfzGaC+IoFPvOkIFaNXkIgHlS4X9CTKVw896d/HEXjxk3ITT9bvpZUzlyuUY\r\n7NPqtJ4DS1qEXGgRbmvnktT3cHtn3yrzTJ094r4Iu7thD0bxR9epusZA6K6y\r\nM2btRb0LNNijISdv7koLDcMUWI53pz8cg1SUt2dgr4LkzeSGOGCCKW0oYuWW\r\nnHzDuwJXCalG48rhqAvVNt6YZrq9mWGkN5gjvv5lCoVkpjvVKX2H+WipHV0R\r\nnvUmn8zP+bo8TSiIvpxmwSt8BVK84/OUTTkOeeO+i5Ug6yZk8diZF92bGtX1\r\nVY0s6FWcQBnWaZcg3DJ3ZLL2QfaxHvX4s0JKQ/fk7zuF16fmL3ap6yK10BeJ\r\n67v5c4d2HomlL/BkVCKvEC5GqibGgC9++JnSqZRqjVLEoruBmGPY04T183lu\r\nJsmBshor821cv/aX+A5XKyW0JCrDVM7h75GRkZS07VCpLlGRqiMckT1KDiul\r\nQCt6atz+3X9POCH7/GOtTh1lHqnyeajclmW66BdDI91SDSPFvbX6+mn3OhhD\r\nB9JPmmP8KVkDcokzQm1OK+SAbxRgLvaipvXk192x79xolNrXljgZMeD9eaS9\r\nq+4eJDFE4vi1vmzVcePQGdWUub8bpGVaBvU=\r\n=MWFB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/src/main.js", "type": "module", "types": "./build/src/main.d.ts", "engines": {"node": ">=14.18.0"}, "exports": "./build/src/main.js", "gitHead": "056bfaee1c5e4eae11c168795d75c8c18498548a", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ehmicky/human-signals.git", "type": "git"}, "_npmVersion": "8.17.0", "description": "Human-friendly process signals", "directories": {"lib": "src", "test": "test"}, "_nodeVersion": "18.7.0", "_hasShrinkwrap": false, "devDependencies": {"ajv": "^8.11.0", "test-each": "^5.2.0", "@ehmicky/dev-tasks": "^1.0.84"}, "_npmOperationalInternal": {"tmp": "tmp/human-signals_4.2.0_1660677438115_0.8180927028538647", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "human-signals", "version": "4.3.0", "keywords": ["signal", "signals", "handlers", "error-handling", "interrupts", "sigterm", "sigint", "irq", "process", "exit", "exit-code", "status", "operating-system", "es6", "javascript", "typescript", "linux", "macos", "windows", "nodejs"], "author": {"url": "https://github.com/ehmicky", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "human-signals@4.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://www.github.com/ehmicky/human-signals", "bugs": {"url": "https://github.com/ehmicky/human-signals/issues"}, "dist": {"shasum": "2095c3cd5afae40049403d4b811235b03879db50", "tarball": "https://registry.npmjs.org/human-signals/-/human-signals-4.3.0.tgz", "fileCount": 8, "integrity": "sha512-zyzVyMjpGBX2+6cDVZeFPCdtOtdsxOeseRhB9tkQ6xXmGUNrcnBzdEKPy3VPNYz+4gy1oukVOXcrJCunSyc6QQ==", "signatures": [{"sig": "MEUCIBaWx5UCWcZGSQ6UtWlXghQeJI7AHr9VKU1svaQyLRy7AiEAuT755Cgbrqzgw9j52mrstl9tQuB1ZhOmSjaTvP6BcjI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjbpuqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpiRg//eqgGyZbPlywQOM4adBIWgyiRtxxNK8pezsIZ1NiLpbJ8zMOc\r\nuqqML+/QfgrwO+NAb0Jkx6CiC6JI6R+ri2nicjGEx6TYiHiySw0w6NX2XLNN\r\nES8/rwSj1+rG6Zi3Fl5GFHAMY0VwRQnxFxWhBxswLgcJQoC46Ojk8HnNjL0p\r\nQk42otQmzOY60+faLoaw9fgARM/63p64+IcM0U9ItAKA25GlArogwE8na04t\r\nOrEHYweV0hillVCM/3I2ba2wWF/pXYBbR+4mEFnRHoVUGXNa9Wf0yy+1mj/h\r\nGc+C0XxzYP2C8DamxKLFlEhmePhSWWDgm4Lb5cwxRYc/rj0Mzx/mwIdghqKk\r\nF0DmHjPCEUQg6fU00F+ONJFPgdjTQ8wydxqIEXqgdEIkk2CUp/mFtzVDAizl\r\nAPXbGBqgxpyDOzaf8cTv3J/bxsED2C4xYikPck1eCG3R6KDaGzYn4TlIB/Qs\r\njAr2ghcD7Ny3DY/rXzKjTjtlVEg7kq9Z1uXMIQwy8PHgTPhGCsDq1w8EOwiz\r\n0poEKFW8B+PW27OAHRxgu1PyjtMvLgxubnGW+OwxaM5F3SmtGy8UAaRGN28D\r\nhm0nnpoR0dUAjUyrWxLQ7rz7/6yqCuzv35t26KDWBUJQhO4t7V2nTmYdLSMd\r\nDWr+16ZtbqdiC3oOKIWwzI6P3kJciV755ro=\r\n=o8bU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/src/main.js", "type": "module", "types": "./build/types/main.d.ts", "engines": {"node": ">=14.18.0"}, "exports": {"types": "./build/types/main.d.ts", "default": "./build/src/main.js"}, "gitHead": "8a4455606d7fa5f791135fe282aae3c4f48d2341", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ehmicky/human-signals.git", "type": "git"}, "_npmVersion": "9.1.1", "description": "Human-friendly process signals", "directories": {"lib": "src", "test": "test"}, "sideEffects": false, "_nodeVersion": "19.0.1", "_hasShrinkwrap": false, "devDependencies": {"ajv": "^8.11.0", "test-each": "^5.5.0", "@ehmicky/dev-tasks": "^1.0.102"}, "_npmOperationalInternal": {"tmp": "tmp/human-signals_4.3.0_1668193193957_0.9592529310858631", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "human-signals", "version": "4.3.1", "keywords": ["signal", "signals", "handlers", "error-handling", "interrupts", "sigterm", "sigint", "irq", "process", "exit", "exit-code", "status", "operating-system", "es6", "javascript", "typescript", "linux", "macos", "windows", "nodejs"], "author": {"url": "https://github.com/ehmicky", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "human-signals@4.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://www.github.com/ehmicky/human-signals", "bugs": {"url": "https://github.com/ehmicky/human-signals/issues"}, "dist": {"shasum": "ab7f811e851fca97ffbd2c1fe9a958964de321b2", "tarball": "https://registry.npmjs.org/human-signals/-/human-signals-4.3.1.tgz", "fileCount": 8, "integrity": "sha512-nZXjEF2nbo7lIw3mgYjItAfgQXog3OjJogSbKa2CQIIvSGWcKgeJnQlNXip6NglNzYH45nSRiEVimMvYL8DDqQ==", "signatures": [{"sig": "MEUCIQCqPPO+Nf2sKSJwqC04cMIfL+4shuT7SPvvrsj6QP9ROQIgXXwj+f+funPENYNExUEDswNxrCjDJySChyLv3ZQazBU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFnFlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmol/A//aBZ0O56Oiiidmta8PgsK9BRVKUG4OFAbYO/q9wclMT+31A+2\r\npP0Q//DT6Y2tW37RJIHp9Qk22uyn/4efa4iozmoWGvfnI983dedCcHFl42z2\r\n3oBvRRiBJTu/272EDmJ/qDXbuQszryLUOz2cqqXDkUnzRp3smlorL8MsBWw4\r\nFySxJIZHjGWEDB2F8Vgc2q4TOdC6EXWsF+TNV/ypEThKPw0OpUY1qb5MbswH\r\nnWgGkWYJa9LGeocJgEeDF/wVH1pzmEtTxuxNTIsfoUmGSRiXVGn3NupHtllo\r\noxlcPBCzp8qzhNNsi//ZmJW0raG4tNc3sZngltoLvYKwDJovBjm+lwalpqj8\r\npktLS5W4YjtRZbOXzUJebJl6JeWu/7NWxYX3tnJZSko6R8vj2GBZnLSbZcB7\r\n849feoQ1i2yuSuqcqPsaCUHdklwV9YEIHlTudOgC4N5nwezEb5xG65RUKJ/f\r\nW4WDx6JlXXRnEBwJsngmIQQ74xXgECceonZSBJR9aYG2TsOHHOS3G9eh61wf\r\nQrOgmJGKESC9j+ib86wYuoQSwidG4XMH2VyMtLeDmyACsGfhK5uLOgpCo0fJ\r\nJF0IG75llOz1KUmu0oDvLyHLt/zkSkMf+L/wMwo8i8Z3a5aVW/zjDWnuMK4X\r\nTNY0CqUk3qqCrP+DV5OlIK+gpF3xiDhvWQ4=\r\n=SKDD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/src/main.js", "type": "module", "types": "./build/src/main.d.ts", "engines": {"node": ">=14.18.0"}, "exports": {"types": "./build/src/main.d.ts", "default": "./build/src/main.js"}, "gitHead": "ae29b0acd2da4f5e06446a821fc83347f0b519a8", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ehmicky/human-signals.git", "type": "git"}, "_npmVersion": "9.6.2", "description": "Human-friendly process signals", "directories": {"lib": "src"}, "sideEffects": false, "_nodeVersion": "19.8.1", "_hasShrinkwrap": false, "devDependencies": {"ajv": "^8.12.0", "test-each": "^5.7.1", "@ehmicky/dev-tasks": "^2.0.71"}, "_npmOperationalInternal": {"tmp": "tmp/human-signals_4.3.1_1679192421545_0.9122751202337451", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "human-signals", "version": "5.0.0", "keywords": ["signal", "signals", "handlers", "error-handling", "interrupts", "sigterm", "sigint", "irq", "process", "exit", "exit-code", "status", "operating-system", "es6", "javascript", "typescript", "linux", "macos", "windows", "nodejs"], "author": {"url": "https://github.com/ehmicky", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "human-signals@5.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://www.github.com/ehmicky/human-signals", "bugs": {"url": "https://github.com/ehmicky/human-signals/issues"}, "dist": {"shasum": "42665a284f9ae0dade3ba41ebc37eb4b852f3a28", "tarball": "https://registry.npmjs.org/human-signals/-/human-signals-5.0.0.tgz", "fileCount": 8, "integrity": "sha512-AXcZb6vzzrFAUE61HnN4mpLqd/cSIwNQjtNWR0euPm6y0iqx3G4gOXaIDdtdDwZmhwe82LA6+zinmW4UBWVePQ==", "signatures": [{"sig": "MEUCIQC/gWQAE5N3o6h2RCWipfsQImiVrYPyoqvYJaplWNzzFgIgDmQ0lxflCqCm/Jh64nXIvK5D4/KmKtZGEsfQoThbEbU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25965}, "main": "./build/src/main.js", "type": "module", "types": "./build/src/main.d.ts", "engines": {"node": ">=16.17.0"}, "exports": {"types": "./build/src/main.d.ts", "default": "./build/src/main.js"}, "gitHead": "6e4306da4edc2a6ede9cb3e2b4e30ba542676a88", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ehmicky/human-signals.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "Human-friendly process signals", "directories": {"lib": "src"}, "sideEffects": false, "_nodeVersion": "20.1.0", "_hasShrinkwrap": false, "devDependencies": {"ajv": "^8.12.0", "test-each": "^6.0.0", "@ehmicky/dev-tasks": "^2.0.80"}, "_npmOperationalInternal": {"tmp": "tmp/human-signals_5.0.0_1684021792778_0.9805355474936588", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "human-signals", "version": "6.0.0", "keywords": ["signal", "signals", "handlers", "error-handling", "interrupts", "sigterm", "sigint", "irq", "process", "exit", "exit-code", "status", "operating-system", "es6", "javascript", "typescript", "linux", "macos", "windows", "nodejs"], "author": {"url": "https://github.com/ehmicky", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "human-signals@6.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://www.github.com/ehmicky/human-signals", "bugs": {"url": "https://github.com/ehmicky/human-signals/issues"}, "dist": {"shasum": "f74f863f87ecb61c05facaa5df289d0ba578d8dd", "tarball": "https://registry.npmjs.org/human-signals/-/human-signals-6.0.0.tgz", "fileCount": 8, "integrity": "sha512-E8FEGCQpCoCHSPDn+iWUL2t+XIp1cUGFCiIWNmLuRszJunXrnXbbH34UeLlDQmLVZhyLmRZS53fwVD1Htvn1QA==", "signatures": [{"sig": "MEYCIQC0Oeyn6BMZrdwgm47fJ3yesauZLXlkt3S7fzHxCu7azwIhANJn1g+JX2cbh96jJKaOgxZl0ZCfsG+lUnQbEsQ4+28C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26440}, "main": "./build/src/main.js", "type": "module", "types": "./build/src/main.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"types": "./build/src/main.d.ts", "default": "./build/src/main.js"}, "gitHead": "55d85fc7d7b55524c9b21bf38bfa26ae3c403772", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ehmicky/human-signals.git", "type": "git"}, "_npmVersion": "10.2.1", "description": "Human-friendly process signals", "directories": {"lib": "src"}, "sideEffects": false, "_nodeVersion": "21.1.0", "_hasShrinkwrap": false, "devDependencies": {"ajv": "^8.12.0", "test-each": "^7.0.0", "@ehmicky/dev-tasks": "^2.0.102"}, "_npmOperationalInternal": {"tmp": "tmp/human-signals_6.0.0_1698531546661_0.7846085710632584", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "human-signals", "version": "7.0.0", "keywords": ["signal", "signals", "handlers", "error-handling", "interrupts", "sigterm", "sigint", "irq", "process", "exit", "exit-code", "status", "operating-system", "es6", "javascript", "typescript", "linux", "macos", "windows", "nodejs"], "author": {"url": "https://github.com/ehmicky", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "human-signals@7.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://www.github.com/ehmicky/human-signals", "bugs": {"url": "https://github.com/ehmicky/human-signals/issues"}, "dist": {"shasum": "93e58e0c19cfec1dded4af10cd4969f5ab75f6c8", "tarball": "https://registry.npmjs.org/human-signals/-/human-signals-7.0.0.tgz", "fileCount": 8, "integrity": "sha512-74kytxOUSvNbjrT9KisAbaTZ/eJwD/LrbM/kh5j0IhPuJzwuA19dWvniFGwBzN9rVjg+O/e+F310PjObDXS+9Q==", "signatures": [{"sig": "MEQCID4DxU7ELqYE42o8SveJrsR8P13clWCUkl+wt0u+R6e3AiAVkwOYndN+sh5Ds3wWxZ04HATONfq8wxKDcHfw17BWWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27384}, "main": "./build/src/main.js", "type": "module", "types": "./build/src/main.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"types": "./build/src/main.d.ts", "default": "./build/src/main.js"}, "gitHead": "edec17165774610d33b5fec517260d36020616e5", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ehmicky/human-signals.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Human-friendly process signals", "directories": {"lib": "src"}, "sideEffects": false, "_nodeVersion": "22.1.0", "_hasShrinkwrap": false, "devDependencies": {"ajv": "^8.12.0", "test-each": "^7.0.0", "@ehmicky/dev-tasks": "^2.0.108"}, "_npmOperationalInternal": {"tmp": "tmp/human-signals_7.0.0_1714789902076_0.6335389924058359", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "human-signals", "version": "8.0.0", "keywords": ["signal", "signals", "handlers", "error-handling", "interrupts", "sigterm", "sigint", "irq", "process", "exit", "exit-code", "status", "operating-system", "es6", "javascript", "typescript", "linux", "macos", "windows", "nodejs"], "author": {"url": "https://github.com/ehmicky", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "_id": "human-signals@8.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://www.github.com/ehmicky/human-signals", "bugs": {"url": "https://github.com/ehmicky/human-signals/issues"}, "dist": {"shasum": "2d3d63481c7c2319f0373428b01ffe30da6df852", "tarball": "https://registry.npmjs.org/human-signals/-/human-signals-8.0.0.tgz", "fileCount": 8, "integrity": "sha512-/1/GPCpDUCCYwlERiYjxoczfP0zfvZMU/OWgQPMya9AbAE24vseigFdhAMObpc8Q4lc/kjutPfUddDYyAmejnA==", "signatures": [{"sig": "MEYCIQDNMT12UJl0QIGWTS7uDUYpoj3UajvXIButhy2cTVRVIAIhAJXZMMETZ1nE/K+8kKONe98HntbeHnv3Rx4FjjEPMtDg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28366}, "main": "./build/src/main.js", "type": "module", "types": "./build/src/main.d.ts", "engines": {"node": ">=18.18.0"}, "exports": {"types": "./build/src/main.d.ts", "default": "./build/src/main.js"}, "gitHead": "fd570e08a64df6d6023b8199737ca4e36f420d27", "scripts": {"test": "gulp test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/ehmicky/human-signals.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Human-friendly process signals", "directories": {"lib": "src"}, "sideEffects": false, "_nodeVersion": "22.5.1", "_hasShrinkwrap": false, "devDependencies": {"ajv": "^8.17.1", "test-each": "^7.0.0", "@ehmicky/dev-tasks": "^3.0.5"}, "_npmOperationalInternal": {"tmp": "tmp/human-signals_8.0.0_1722715084502_0.8050872762111017", "host": "s3://npm-registry-packages"}}, "8.0.1": {"name": "human-signals", "version": "8.0.1", "type": "module", "exports": {"types": "./build/src/main.d.ts", "default": "./build/src/main.js"}, "main": "./build/src/main.js", "types": "./build/src/main.d.ts", "sideEffects": false, "scripts": {"test": "gulp test"}, "description": "Human-friendly process signals", "keywords": ["signal", "signals", "handlers", "error-handling", "interrupts", "sigterm", "sigint", "irq", "process", "exit", "exit-code", "status", "operating-system", "es6", "javascript", "typescript", "linux", "macos", "windows", "nodejs"], "license": "Apache-2.0", "homepage": "https://www.github.com/ehmicky/human-signals", "repository": {"type": "git", "url": "git+https://github.com/ehmicky/human-signals.git"}, "bugs": {"url": "https://github.com/ehmicky/human-signals/issues"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/ehmicky"}, "directories": {"lib": "src"}, "devDependencies": {"@ehmicky/dev-tasks": "^3.0.33", "@ehmicky/eslint-config": "^20.0.31", "@ehmicky/prettier-config": "^1.0.6", "ajv": "^8.17.1", "test-each": "^7.0.1"}, "engines": {"node": ">=18.18.0"}, "_id": "human-signals@8.0.1", "gitHead": "3eee85df2ca85d49dd26e37351c6913219a8fa31", "_nodeVersion": "23.10.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-eKCa6bwnJhvxj14kZk5NCPc6Hb6BdsU9DZcOnmQKSnO1VKrfV0zCvtttPZUsBvjmNDn8rpcJfpwSYnHBjc95MQ==", "shasum": "f08bb593b6d1db353933d06156cedec90abe51fb", "tarball": "https://registry.npmjs.org/human-signals/-/human-signals-8.0.1.tgz", "fileCount": 8, "unpackedSize": 28040, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCJcVD6Wuy1uszSEWJ9HSFp5SsJc2a5WAFVUn4y0+Id4AIgISeSgRyQvLMBwVt01VtPb+0yXGRfw2EO7/eGITO4JIY="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/human-signals_8.0.1_1743216226193_0.5049216160123091"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-10-12T18:52:58.137Z", "modified": "2025-03-29T02:43:46.565Z", "1.1.0": "2019-10-12T18:52:58.513Z", "1.1.1": "2019-10-16T10:13:22.409Z", "2.0.0": "2019-12-25T16:11:30.019Z", "2.1.0": "2020-03-14T15:10:37.036Z", "3.0.0": "2021-06-22T11:40:42.578Z", "3.0.1": "2021-06-22T12:23:03.541Z", "4.0.0": "2022-05-09T20:28:18.790Z", "4.1.0": "2022-06-26T19:30:39.800Z", "4.2.0": "2022-08-16T19:17:18.281Z", "4.3.0": "2022-11-11T18:59:54.125Z", "4.3.1": "2023-03-19T02:20:21.732Z", "5.0.0": "2023-05-13T23:49:52.964Z", "6.0.0": "2023-10-28T22:19:06.843Z", "7.0.0": "2024-05-04T02:31:42.294Z", "8.0.0": "2024-08-03T19:58:04.676Z", "8.0.1": "2025-03-29T02:43:46.398Z"}, "bugs": {"url": "https://github.com/ehmicky/human-signals/issues"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/ehmicky"}, "license": "Apache-2.0", "homepage": "https://www.github.com/ehmicky/human-signals", "keywords": ["signal", "signals", "handlers", "error-handling", "interrupts", "sigterm", "sigint", "irq", "process", "exit", "exit-code", "status", "operating-system", "es6", "javascript", "typescript", "linux", "macos", "windows", "nodejs"], "repository": {"type": "git", "url": "git+https://github.com/ehmicky/human-signals.git"}, "description": "Human-friendly process signals", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "[![Node](https://img.shields.io/badge/-Node.js-808080?logo=node.js&colorA=404040&logoColor=66cc33)](https://www.npmjs.com/package/human-signals)\n[![TypeScript](https://img.shields.io/badge/-Typed-808080?logo=typescript&colorA=404040&logoColor=0096ff)](/src/main.d.ts)\n[![Codecov](https://img.shields.io/badge/-Tested%20100%25-808080?logo=codecov&colorA=404040)](https://codecov.io/gh/ehmicky/human-signals)\n[![Mastodon](https://img.shields.io/badge/-Mastodon-808080.svg?logo=mastodon&colorA=404040&logoColor=9590F9)](https://fosstodon.org/@ehmicky)\n[![Medium](https://img.shields.io/badge/-Medium-808080.svg?logo=medium&colorA=404040)](https://medium.com/@ehmicky)\n\nHuman-friendly process signals.\n\nThis is a map of known process signals with some information about each signal.\n\nUnlike\n[`os.constants.signals`](https://nodejs.org/api/os.html#os_signal_constants)\nthis includes:\n\n- human-friendly [descriptions](#description)\n- [default actions](#action), including whether they [can be prevented](#forced)\n- whether the signal is [supported](#supported) by the current OS\n\n# Example\n\n```js\nimport { signalsByName, signalsByNumber } from 'human-signals'\n\nconsole.log(signalsByName.SIGINT)\n// {\n//   name: 'SIGINT',\n//   number: 2,\n//   description: 'User interruption with CTRL-C',\n//   supported: true,\n//   action: 'terminate',\n//   forced: false,\n//   standard: 'ansi'\n// }\n\nconsole.log(signalsByNumber[8])\n// {\n//   name: 'SIGFPE',\n//   number: 8,\n//   description: 'Floating point arithmetic error',\n//   supported: true,\n//   action: 'core',\n//   forced: false,\n//   standard: 'ansi'\n// }\n```\n\n# Install\n\n```bash\nnpm install human-signals\n```\n\nThis package works in Node.js >=18.18.0.\n\nThis is an ES module. It must be loaded using\n[an `import` or `import()` statement](https://gist.github.com/sindresorhus/a39789f98801d908bbc7ff3ecc99d99c),\nnot `require()`. If TypeScript is used, it must be configured to\n[output ES modules](https://www.typescriptlang.org/docs/handbook/esm-node.html),\nnot CommonJS.\n\n# Usage\n\n## signalsByName\n\n_Type_: `object`\n\nObject whose keys are signal [names](#name) and values are\n[signal objects](#signal).\n\n## signalsByNumber\n\n_Type_: `object`\n\nObject whose keys are signal [numbers](#number) and values are\n[signal objects](#signal).\n\n## signal\n\n_Type_: `object`\n\nSignal object with the following properties.\n\n### name\n\n_Type_: `string`\n\nStandard name of the signal, for example `'SIGINT'`.\n\n### number\n\n_Type_: `number`\n\nCode number of the signal, for example `2`. While most `number` are\ncross-platform, some are different between different OS.\n\n### description\n\n_Type_: `string`\n\nHuman-friendly description for the signal, for example\n`'User interruption with CTRL-C'`.\n\n### supported\n\n_Type_: `boolean`\n\nWhether the current OS can handle this signal in Node.js using\n[`process.on(name, handler)`](https://nodejs.org/api/process.html#process_signal_events).\n\nThe list of supported signals\n[is OS-specific](https://github.com/ehmicky/cross-platform-node-guide/blob/main/docs/6_networking_ipc/signals.md#cross-platform-signals).\n\n### action\n\n_Type_: `string`\\\n_Enum_: `'terminate'`, `'core'`, `'ignore'`, `'pause'`, `'unpause'`\n\nWhat is the default action for this signal when it is not handled.\n\n### forced\n\n_Type_: `boolean`\n\nWhether the signal's default action cannot be prevented. This is `true` for\n`SIGTERM`, `SIGKILL` and `SIGSTOP`.\n\n### standard\n\n_Type_: `string`\\\n_Enum_: `'ansi'`, `'posix'`, `'bsd'`, `'systemv'`, `'other'`\n\nWhich standard defined that signal.\n\n# Support\n\nFor any question, _don't hesitate_ to [submit an issue on GitHub](../../issues).\n\nEveryone is welcome regardless of personal background. We enforce a\n[Code of conduct](CODE_OF_CONDUCT.md) in order to promote a positive and\ninclusive environment.\n\n# Contributing\n\nThis project was made with ❤️. The simplest way to give back is by starring and\nsharing it online.\n\nIf the documentation is unclear or has a typo, please click on the page's `Edit`\nbutton (pencil icon) and suggest a correction.\n\nIf you would like to help us fix a bug or add a new feature, please check our\n[guidelines](CONTRIBUTING.md). Pull requests are welcome!\n\nThanks go to our wonderful contributors:\n\n<!-- ALL-CONTRIBUTORS-LIST:START -->\n<!-- prettier-ignore-start -->\n<!-- markdownlint-disable -->\n<table>\n  <tbody>\n    <tr>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://fosstodon.org/@ehmicky\"><img src=\"https://avatars2.githubusercontent.com/u/8136211?v=4?s=100\" width=\"100px;\" alt=\"ehmicky\"/><br /><sub><b>ehmicky</b></sub></a><br /><a href=\"https://github.com/ehmicky/human-signals/commits?author=ehmicky\" title=\"Code\">💻</a> <a href=\"#design-ehmicky\" title=\"Design\">🎨</a> <a href=\"#ideas-ehmicky\" title=\"Ideas, Planning, & Feedback\">🤔</a> <a href=\"https://github.com/ehmicky/human-signals/commits?author=ehmicky\" title=\"Documentation\">📖</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"http://www.electrovir.com\"><img src=\"https://avatars0.githubusercontent.com/u/1205860?v=4?s=100\" width=\"100px;\" alt=\"electrovir\"/><br /><sub><b>electrovir</b></sub></a><br /><a href=\"https://github.com/ehmicky/human-signals/commits?author=electrovir\" title=\"Code\">💻</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://fzy.se\"><img src=\"https://avatars.githubusercontent.com/u/2656517?v=4?s=100\" width=\"100px;\" alt=\"Felix Zedén Yverås\"/><br /><sub><b>Felix Zedén Yverås</b></sub></a><br /><a href=\"https://github.com/ehmicky/human-signals/commits?author=FelixZY\" title=\"Code\">💻</a> <a href=\"https://github.com/ehmicky/human-signals/commits?author=FelixZY\" title=\"Tests\">⚠️</a></td>\n    </tr>\n  </tbody>\n</table>\n\n<!-- markdownlint-restore -->\n<!-- prettier-ignore-end -->\n\n<!-- ALL-CONTRIBUTORS-LIST:END -->\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}