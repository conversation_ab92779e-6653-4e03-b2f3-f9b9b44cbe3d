{"_id": "sprintf-js", "_rev": "86-3983cff8cb7937ac3b705fca3ce4b45c", "name": "sprintf-js", "dist-tags": {"latest": "1.1.3"}, "versions": {"0.0.7": {"name": "sprintf-js", "version": "0.0.7", "author": {"url": "http://alexei.ro/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD", "_id": "sprintf-js@0.0.7", "maintainers": [{"name": "ale<PERSON>i", "email": "<EMAIL>"}], "dist": {"shasum": "f00d78fd160130809b4ab340c0310faa71253dbd", "tarball": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-0.0.7.tgz", "integrity": "sha512-KJ+dRU+F2fCeQ5g5YDnybY5G7pkLryQMzPdufddLi5GtdFt/PkbcwrE2BiSczxmpUvsjUCFB4CeWbNfK/w6W9w==", "signatures": [{"sig": "MEUCIGR6itT7M1VXP5CksSKYv/cRA6HX1ijbV8qc+DeBkwsFAiEA4AdiLGzXa7bHa+Z5UVYviLaMDR30WCxdtEpxvNBND2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/sprintf.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "_npmUser": {"name": "ale<PERSON>i", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/alexei/sprintf.js.git", "type": "git"}, "_npmVersion": "1.1.65", "description": "JavaScript sprintf implementation", "directories": {"test": "test"}}, "1.0.1": {"name": "sprintf-js", "version": "1.0.1", "author": {"url": "http://alexei.ro/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "sprintf-js@1.0.1", "maintainers": [{"name": "ale<PERSON>i", "email": "<EMAIL>"}], "homepage": "https://github.com/alexei/sprintf.js", "bugs": {"url": "https://github.com/alexei/sprintf.js/issues"}, "dist": {"shasum": "57ed6d7fe6240c1b2e7638431b57704f86a7f0ef", "tarball": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.1.tgz", "integrity": "sha512-MjYdFr100X0Ax2XU9L0iPqpR7SLuedw5fJx/JdeW7XEdGAboHddbrQfJ7IKfqw+T8AMxuNNKDv4rKVO6kRPGjA==", "signatures": [{"sig": "MEQCIB1XBkmVWfLFwt6X7m0zkYgTHAIJEiP5FxxROIF2b/hmAiBNg9jkeUjfUdCg47EOZCcAtlAYOP3OCNeBdFr9j35vXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/sprintf.js", "_from": ".", "_shasum": "57ed6d7fe6240c1b2e7638431b57704f86a7f0ef", "gitHead": "6d742809698ee0caff18c71251cba3ed8b03de5d", "scripts": {"test": "mocha test/test.js"}, "_npmUser": {"name": "ale<PERSON>i", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/alexei/sprintf.js.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "JavaScript sprintf implementation", "directories": {}, "devDependencies": {"grunt": "*", "mocha": "*", "grunt-contrib-watch": "*", "grunt-contrib-uglify": "*"}}, "1.0.2": {"name": "sprintf-js", "version": "1.0.2", "author": {"url": "http://alexei.ro/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "sprintf-js@1.0.2", "maintainers": [{"name": "ale<PERSON>i", "email": "<EMAIL>"}], "homepage": "https://github.com/alexei/sprintf.js", "bugs": {"url": "https://github.com/alexei/sprintf.js/issues"}, "dist": {"shasum": "11e4d84ff32144e35b0bf3a66f8587f38d8f9978", "tarball": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.2.tgz", "integrity": "sha512-PLfl5haQqcprzBZvDxQD6PzrQqaCmHSx0U/OERxTAU9lLtWbpPpxTPrDKdvbHfPW/BHhQPb0o5ktMNo0ejRD9Q==", "signatures": [{"sig": "MEYCIQCnAR6ztBhxcq/Rz4TvfyLXlzfgFQAZd6erOjRVTy456AIhAJOUp25da9Mbdq7afCsPiMwRRvkBUKDOLwJXPJxQxSjO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/sprintf.js", "_from": ".", "_shasum": "11e4d84ff32144e35b0bf3a66f8587f38d8f9978", "gitHead": "e8c73065cd1a79a32c697806a4e85f1fe7917592", "scripts": {"test": "mocha test/test.js"}, "_npmUser": {"name": "ale<PERSON>i", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/alexei/sprintf.js.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "JavaScript sprintf implementation", "directories": {}, "devDependencies": {"grunt": "*", "mocha": "*", "grunt-contrib-watch": "*", "grunt-contrib-uglify": "*"}}, "1.0.3": {"name": "sprintf-js", "version": "1.0.3", "author": {"url": "http://alexei.ro/", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "sprintf-js@1.0.3", "maintainers": [{"name": "ale<PERSON>i", "email": "<EMAIL>"}], "homepage": "https://github.com/alexei/sprintf.js#readme", "bugs": {"url": "https://github.com/alexei/sprintf.js/issues"}, "dist": {"shasum": "04e6926f662895354f3dd015203633b857297e2c", "tarball": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz", "integrity": "sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==", "signatures": [{"sig": "MEQCIDSB/raarzaOUXXEaSDKssErvTM4Z3cvCQiIPvLi68d6AiAB2PIbdMo0LWWF/Adn+tb00uQT/t7+9335DWW5SSkG4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/sprintf.js", "_from": ".", "_shasum": "04e6926f662895354f3dd015203633b857297e2c", "gitHead": "747b806c2dab5b64d5c9958c42884946a187c3b1", "scripts": {"test": "mocha test/test.js"}, "_npmUser": {"name": "ale<PERSON>i", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/alexei/sprintf.js.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "JavaScript sprintf implementation", "directories": {}, "_nodeVersion": "0.12.4", "devDependencies": {"grunt": "*", "mocha": "*", "grunt-contrib-watch": "*", "grunt-contrib-uglify": "*"}}, "1.1.0": {"name": "sprintf-js", "version": "1.1.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "sprintf-js@1.1.0", "maintainers": [{"name": "ale<PERSON>i", "email": "<EMAIL>"}], "homepage": "https://github.com/alexei/sprintf.js#readme", "bugs": {"url": "https://github.com/alexei/sprintf.js/issues"}, "dist": {"shasum": "cffcaf702daf65ea39bb4e0fa2b299cec1a1be46", "tarball": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.1.0.tgz", "integrity": "sha512-F5Eiffg9i6Jcq0H3iSr/2HQXTw3/BlrWqxDXS45szJfgR8EBTfQogrPXMxmJ6ylyvwE2XC4MTxPKO5/ivODOnQ==", "signatures": [{"sig": "MEUCIQD3793MG2qmpLka0zwJxtm3HOXpbsfbYNpP8SrE5/uEmgIgMKhRZuma+R/krrJc12P0H0U1+9TUMvdGttjTGHW4Hsk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/sprintf.js", "_from": ".", "_shasum": "cffcaf702daf65ea39bb4e0fa2b299cec1a1be46", "gitHead": "2e19f9a9b2c358749eb53d63be60ff7d3465711b", "scripts": {"test": "mocha test/test.js"}, "_npmUser": {"name": "ale<PERSON>i", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/alexei/sprintf.js.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "JavaScript sprintf implementation", "directories": {}, "_nodeVersion": "7.8.0", "devDependencies": {"gulp": "^3.9.0", "mocha": "^3.3.0", "jshint": "^2.9.1", "benchmark": "^2.1.4", "gulp-mocha": "^4.3.1", "gulp-header": "^1.7.1", "gulp-jshint": "^2.0.0", "gulp-rename": "^1.2.2", "gulp-uglify": "^2.1.2", "gulp-benchmark": "^1.1.1", "gulp-sourcemaps": "^2.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/sprintf-js-1.1.0.tgz_1494081358347_0.5414827775675803", "host": "packages-18-east.internal.npmjs.com"}}, "1.1.1": {"name": "sprintf-js", "version": "1.1.1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "sprintf-js@1.1.1", "maintainers": [{"name": "ale<PERSON>i", "email": "<EMAIL>"}], "homepage": "https://github.com/alexei/sprintf.js#readme", "bugs": {"url": "https://github.com/alexei/sprintf.js/issues"}, "dist": {"shasum": "36be78320afe5801f6cea3ee78b6e5aab940ea0c", "tarball": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.1.1.tgz", "integrity": "sha512-h/U+VScR2Ft+aXDjGTLtguUEIrYuOjTj79BAOElUvdahYMaaa7SNLjJpOIn+Uzt0hsgHfYvlbcno3e9yXOSo8Q==", "signatures": [{"sig": "MEYCIQDwbIkyxheP8rgp++juukMf6BPlTzM/mlrbKYypQYKXLQIhAIz/Fe/Uq4oiZ/a54IEZT3bUKU4109XR4HsXeRPx61oc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "src/sprintf.js", "_from": ".", "_shasum": "36be78320afe5801f6cea3ee78b6e5aab940ea0c", "gitHead": "6bfe81840e560d675a9de3d79c06354a47ac9236", "scripts": {"lint": "eslint .", "test": "mocha test/test.js", "lint:fix": "eslint --fix .", "posttest": "npm run lint"}, "_npmUser": {"name": "ale<PERSON>i", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/alexei/sprintf.js.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "JavaScript sprintf implementation", "directories": {}, "_nodeVersion": "7.8.0", "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.4.2", "eslint": "3.19.0", "benchmark": "^2.1.4", "gulp-mocha": "^4.3.1", "gulp-eslint": "^3.0.1", "gulp-header": "^1.8.8", "gulp-rename": "^1.2.2", "gulp-uglify": "^3.0.0", "gulp-benchmark": "^1.1.1", "gulp-sourcemaps": "^2.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/sprintf-js-1.1.1.tgz_1496074852444_0.6851142619270831", "host": "s3://npm-registry-packages"}}, "1.1.2": {"name": "sprintf-js", "version": "1.1.2", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "sprintf-js@1.1.2", "maintainers": [{"name": "ale<PERSON>i", "email": "<EMAIL>"}], "homepage": "https://github.com/alexei/sprintf.js#readme", "bugs": {"url": "https://github.com/alexei/sprintf.js/issues"}, "dist": {"shasum": "da1765262bf8c0f571749f2ad6c26300207ae673", "tarball": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.1.2.tgz", "fileCount": 12, "integrity": "sha512-VE0SOVEHCk7Qc8ulkWw3ntAzXuqf7S2lvwQaDLRnUeIEaKNQJzV6BwmLKhOqT61aGhfUMrXeaBk+oDGCzvhcug==", "signatures": [{"sig": "MEUCIQDLrYox3B7TVrVwp6JhCgq4JHze+AHVBzABb2D14BIlYgIgDObujuR953IJNu3Pku6Cg1c4oUiKzF9Z45tQGWnczAs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcDSE4CRA9TVsSAnZWagAAaNwP/1h7BqeK4PRzJRpu7PsQ\nEohuxkAhwGYnx6iTt+S5kB55BiVot9WSzOxDCvTfdcD9msg2ejdtgOLPq9kN\ntTBQOtvoXy6Xc5RwYiH+o3Kla6qir+69XBXC9CkwdOAEcPSkDvB2QFUmiUo/\ni8Mkg9r/JGtsjwSxAYcryLUUIOZkDuoMu/YCVeUWRcxPi7WiKgumQsRGW8VY\nQnJiplaydvcYREw5efMvKo3iiK2Puts0Y6KohrBvLTxFK1gnAIX/Ycq/PfYa\npDLFMwLiuvZ799Hbs44yb+52M7SX1H0Y3VULOeFWY5vYJT1I5Xqnr6U8QsQ2\nFIVMeOL2jn8gq+LcP+nUT41V1ij/L3MvMISo4YBqr32dji3+JgC0tqHxqjdi\nph36pRpEgz8cU4Nu0d0ssH9Ubx+MsbLfQ2+cWAR5BJfs79Yhu/6eo1Te0XUi\njQMstGdcQi2jyKCiBMUVaatrqiMeXhvBdnnGd7QRR0ooRZatrCpYATvnwPGl\nXfRG7MjLiFSbrYmMsnpZW09IT3FG88SwoVVnk8VhMR5glUMTS+XUJ5QT7h8F\nqhz9gjQNSUJceKMsSsrjiiRUj93/diqVVGAp7juCHQESEKHe8BCKWDguCAGg\nOsb4YsSCnj5YnCLjuvTF4dYZln9OQ5/kq3PIUbfrBp6KBg4UwQKwems9C+sp\nWCGx\r\n=4TfB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "src/sprintf.js", "gitHead": "ceb9b805e6d594a9c24cdde02890d3c00c6643b7", "scripts": {"lint": "eslint .", "test": "mocha test/*.js", "pretest": "npm run lint", "lint:fix": "eslint --fix ."}, "_npmUser": {"name": "ale<PERSON>i", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/alexei/sprintf.js.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "JavaScript sprintf implementation", "directories": {}, "_nodeVersion": "9.0.0", "_hasShrinkwrap": false, "devDependencies": {"gulp": "^3.9.1", "mocha": "^5.2.0", "eslint": "^5.10.0", "benchmark": "^2.1.4", "gulp-mocha": "^6.0.0", "gulp-eslint": "^5.0.0", "gulp-header": "^2.0.5", "gulp-rename": "^1.4.0", "gulp-uglify": "^3.0.1", "gulp-benchmark": "^1.1.1", "gulp-sourcemaps": "^2.6.4"}, "_npmOperationalInternal": {"tmp": "tmp/sprintf-js_1.1.2_1544364344346_0.16399656635245852", "host": "s3://npm-registry-packages"}}, "1.1.3": {"name": "sprintf-js", "version": "1.1.3", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "sprintf-js@1.1.3", "maintainers": [{"name": "ale<PERSON>i", "email": "<EMAIL>"}], "homepage": "https://github.com/alexei/sprintf.js#readme", "bugs": {"url": "https://github.com/alexei/sprintf.js/issues"}, "dist": {"shasum": "4914b903a2f8b685d17fdf78a70e917e872e444a", "tarball": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.1.3.tgz", "fileCount": 11, "integrity": "sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA==", "signatures": [{"sig": "MEQCIFqEdBzxc5TO4qHn2Vaj+rUbeXXAIrldXZrm+Oh3ctpGAiA9wmSjDRjIGR5h/m5RnndPawduJOh1VPtIhc1D08pEew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39879}, "main": "src/sprintf.js", "gitHead": "3a0d8c26d291b5bd9f1974877ecc50739921d6f5", "scripts": {"lint": "eslint .", "test": "mocha test/*.js", "pretest": "npm run lint", "lint:fix": "eslint --fix ."}, "_npmUser": {"name": "ale<PERSON>i", "email": "<EMAIL>"}, "overrides": {"graceful-fs": "^4.2.11"}, "repository": {"url": "git+https://github.com/alexei/sprintf.js.git", "type": "git"}, "_npmVersion": "9.5.1", "description": "JavaScript sprintf implementation", "directories": {}, "_nodeVersion": "18.16.0", "_hasShrinkwrap": false, "devDependencies": {"gulp": "^3.9.1", "mocha": "^5.2.0", "eslint": "^5.10.0", "benchmark": "^2.1.4", "gulp-mocha": "^6.0.0", "gulp-eslint": "^5.0.0", "gulp-header": "^2.0.5", "gulp-rename": "^1.4.0", "gulp-uglify": "^3.0.1", "gulp-benchmark": "^1.1.1", "gulp-sourcemaps": "^2.6.4"}, "_npmOperationalInternal": {"tmp": "tmp/sprintf-js_1.1.3_1694437928849_0.6056137367139747", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2013-04-03T13:24:36.516Z", "modified": "2024-10-22T17:26:19.033Z", "0.0.7": "2013-04-03T13:24:39.808Z", "1.0.1": "2014-10-25T09:32:31.819Z", "1.0.2": "2014-10-25T09:37:23.896Z", "1.0.3": "2015-07-10T13:41:29.308Z", "1.1.0": "2017-05-06T14:35:59.835Z", "1.1.1": "2017-05-29T16:20:53.315Z", "1.1.2": "2018-12-09T14:05:44.561Z", "1.1.3": "2023-09-11T13:12:09.207Z"}, "bugs": {"url": "https://github.com/alexei/sprintf.js/issues"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://github.com/alexei/sprintf.js#readme", "repository": {"url": "git+https://github.com/alexei/sprintf.js.git", "type": "git"}, "description": "JavaScript sprintf implementation", "maintainers": [{"name": "ale<PERSON>i", "email": "<EMAIL>"}], "readme": "# sprintf-js\n\n[![Build Status][travisci-image]][travisci-url] [![NPM Version][npm-image]][npm-url] [![Dependency Status][dependencies-image]][dependencies-url] [![devDependency Status][dev-dependencies-image]][dev-dependencies-url]\n\n[travisci-image]: https://travis-ci.org/alexei/sprintf.js.svg?branch=master\n[travisci-url]: https://travis-ci.org/alexei/sprintf.js\n\n[npm-image]: https://badge.fury.io/js/sprintf-js.svg\n[npm-url]: https://badge.fury.io/js/sprintf-js\n\n[dependencies-image]: https://david-dm.org/alexei/sprintf.js.svg\n[dependencies-url]: https://david-dm.org/alexei/sprintf.js\n\n[dev-dependencies-image]: https://david-dm.org/alexei/sprintf.js/dev-status.svg\n[dev-dependencies-url]: https://david-dm.org/alexei/sprintf.js#info=devDependencies\n\n**sprintf-js** is a complete open source JavaScript `sprintf` implementation for the **browser** and **Node.js**.\n\n**Note: as of v1.1.1 you might need some polyfills for older environments. See [Support](#support) section below.**\n\n## Usage\n\n    var sprintf = require('sprintf-js').sprintf,\n        vsprintf = require('sprintf-js').vsprintf\n\n    sprintf('%2$s %3$s a %1$s', 'cracker', 'Polly', 'wants')\n    vsprintf('The first 4 letters of the english alphabet are: %s, %s, %s and %s', ['a', 'b', 'c', 'd'])\n\n## Installation\n\n### NPM\n\n    npm install sprintf-js\n\n### Bower\n\n    bower install sprintf\n\n## API\n\n### `sprintf`\n\nReturns a formatted string:\n\n    string sprintf(string format, mixed arg1?, mixed arg2?, ...)\n\n### `vsprintf`\n\nSame as `sprintf` except it takes an array of arguments, rather than a variable number of arguments:\n\n    string vsprintf(string format, array arguments?)\n\n## Format specification\n\nThe placeholders in the format string are marked by `%` and are followed by one or more of these elements, in this order:\n\n* An optional number followed by a `$` sign that selects which argument index to use for the value. If not specified, arguments will be placed in the same order as the placeholders in the input string.\n* An optional `+` sign that forces to precede the result with a plus or minus sign on numeric values. By default, only the `-` sign is used on negative numbers.\n* An optional padding specifier that says what character to use for padding (if specified). Possible values are `0` or any other character preceded by a `'` (single quote). The default is to pad with *spaces*.\n* An optional `-` sign, that causes `sprintf` to left-align the result of this placeholder. The default is to right-align the result.\n* An optional number, that says how many characters the result should have. If the value to be returned is shorter than this number, the result will be padded. When used with the `j` (JSON) type specifier, the padding length specifies the tab size used for indentation.\n* An optional precision modifier, consisting of a `.` (dot) followed by a number, that says how many digits should be displayed for floating point numbers. When used with the `g` type specifier, it specifies the number of significant digits. When used on a string, it causes the result to be truncated.\n* A type specifier that can be any of:\n    * `%` — yields a literal `%` character\n    * `b` — yields an integer as a binary number\n    * `c` — yields an integer as the character with that ASCII value\n    * `d` or `i` — yields an integer as a signed decimal number\n    * `e` — yields a float using scientific notation\n    * `u` — yields an integer as an unsigned decimal number\n    * `f` — yields a float as is; see notes on precision above\n    * `g` — yields a float as is; see notes on precision above\n    * `o` — yields an integer as an octal number\n    * `s` — yields a string as is\n    * `t` — yields `true` or `false`\n    * `T` — yields the type of the argument<sup><a href=\"#fn-1\" name=\"fn-ref-1\">1</a></sup>\n    * `v` — yields the primitive value of the specified argument\n    * `x` — yields an integer as a hexadecimal number (lower-case)\n    * `X` — yields an integer as a hexadecimal number (upper-case)\n    * `j` — yields a JavaScript object or array as a JSON encoded string\n\n## Features\n\n### Argument swapping\n\nYou can also swap the arguments. That is, the order of the placeholders doesn't have to match the order of the arguments. You can do that by simply indicating in the format string which arguments the placeholders refer to:\n\n    sprintf('%2$s %3$s a %1$s', 'cracker', 'Polly', 'wants')\n\nAnd, of course, you can repeat the placeholders without having to increase the number of arguments.\n\n### Named arguments\n\nFormat strings may contain replacement fields rather than positional placeholders. Instead of referring to a certain argument, you can now refer to a certain key within an object. Replacement fields are surrounded by rounded parentheses - `(` and `)` - and begin with a keyword that refers to a key:\n\n    var user = {\n        name: 'Dolly',\n    }\n    sprintf('Hello %(name)s', user) // Hello Dolly\n\nKeywords in replacement fields can be optionally followed by any number of keywords or indexes:\n\n    var users = [\n        {name: 'Dolly'},\n        {name: 'Molly'},\n        {name: 'Polly'},\n    ]\n    sprintf('Hello %(users[0].name)s, %(users[1].name)s and %(users[2].name)s', {users: users}) // Hello Dolly, Molly and Polly\n\nNote: mixing positional and named placeholders is not (yet) supported\n\n### Computed values\n\nYou can pass in a function as a dynamic value and it will be invoked (with no arguments) in order to compute the value on the fly.\n\n    sprintf('Current date and time: %s', function() { return new Date().toString() })\n\n### AngularJS\n\nYou can use `sprintf` and `vsprintf` (also aliased as `fmt` and `vfmt` respectively) in your AngularJS projects. See `demo/`.\n\n## Support\n\n### Node.js\n\n`sprintf-js` runs in all active Node versions (4.x+).\n\n### Browser\n\n`sprintf-js` should work in all modern browsers. As of v1.1.1, you might need polyfills for the following:\n\n - `String.prototype.repeat()` (any IE)\n - `Array.isArray()` (IE < 9)\n - `Object.create()` (IE < 9)\n\nYMMV\n\n## License\n\n**sprintf-js** is licensed under the terms of the BSD 3-Clause License.\n\n## Notes\n\n<small><sup><a href=\"#fn-ref-1\" name=\"fn-1\">1</a></sup> `sprintf` doesn't use the `typeof` operator. As such, the value `null` is a `null`, an array is an `array` (not an `object`), a date value is a `date` etc.</small>\n", "readmeFilename": "README.md", "users": {"rsp": true, "dbck": true, "keyn": true, "flozz": true, "limit": true, "panlw": true, "blalor": true, "jimnox": true, "yuch4n": true, "sopepos": true, "touskar": true, "evandrix": true, "rreusser": true, "guumaster": true, "justjavac": true, "nbuchanan": true, "agamlarage": true, "axelrindle": true, "brightchen": true, "claudiopro": true, "create3000": true, "mattonfoot": true, "roberkules": true, "tnagengast": true, "acollins-ts": true, "m80126colin": true, "monsterkodi": true, "battlemidget": true, "chocolateboy": true, "jahnestacado": true, "kabirbaidhya": true, "soerenskoett": true, "armantaherian": true, "peter.forgacs": true, "shanewholloway": true, "hyokosdeveloper": true, "ys_sidson_aidson": true}}