{"_id": "node-abi", "_rev": "144-68bce2a90cab8b41b974392d667b39ae", "name": "node-abi", "dist-tags": {"release-3.x": "3.75.0", "latest": "4.12.0"}, "versions": {"1.0.0": {"name": "node-abi", "version": "1.0.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@1.0.0", "maintainers": [{"name": "lgeiger", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "9120c531c4b1ae08039bab1ceeace865920a1287", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-1.0.0.tgz", "integrity": "sha512-WYOXQrqlClMYIxkniJxHA6dOG1ODM/2rFM2l32NbOOpCEfYytutpadceY9uDoUQd/A1mrgn7mCnJ8z1GmEoq3A==", "signatures": [{"sig": "MEQCIBv5VlkSfi2DOJVh2MW1DabTonkMrtRyMgsJppC14D5ZAiA0e7venYy3lOaK0ol8oL8OMonvndtTtzR8EkKqz0gJmg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "9120c531c4b1ae08039bab1ceeace865920a1287", "gitHead": "64ff846df1f6406334f4b8a8a83edb6bc47ffa33", "scripts": {"test": "tape test"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "4.0.3", "description": "Get the Node ABI for a given target and runtime", "directories": {}, "_nodeVersion": "7.2.0", "devDependencies": {"tape": "^4.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi-1.0.0.tgz_1480724411546_0.5770387335214764", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.1": {"name": "node-abi", "version": "1.0.1", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@1.0.1", "maintainers": [{"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "a21af9f0892bb4f25a8ac8914d351b74f96f7e96", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-1.0.1.tgz", "integrity": "sha512-nJhWiQaXlJYn/hDch97HBCrwxNMugUExU5HUOqf50ReVrp4Md4dufpdi1mw2x0+giNwYGxwsJoCESYUi10D2OQ==", "signatures": [{"sig": "MEQCICcXtvTJzqcTXziUeuTSf8Al7ILSVVVqHuwWL8C8lXhdAiB2Uixoz6FTCuftx/9d/5/EOAlkFM9wuHNDJ4MnG1vacA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "a21af9f0892bb4f25a8ac8914d351b74f96f7e96", "gitHead": "d1bbe1d507d48e3e4f2116c2f587a22246bb4eda", "scripts": {"test": "tape test"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "4.0.3", "description": "Get the Node ABI for a given target and runtime", "directories": {}, "_nodeVersion": "7.2.0", "devDependencies": {"tape": "^4.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi-1.0.1.tgz_1480728029093_0.2006724860984832", "host": "packages-18-east.internal.npmjs.com"}}, "1.0.2": {"name": "node-abi", "version": "1.0.2", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@1.0.2", "maintainers": [{"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "fb5278963597c1b8eeafa21e148a10a0f1d6150c", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-1.0.2.tgz", "integrity": "sha512-s9rLHYCd/2wDtj/r7fn1RhIDcDxDFES+InV7MeEGATRgQVYOtRlLSXNexQT/0n7golHAZqEQo0JIxgmsu/llvw==", "signatures": [{"sig": "MEYCIQDdIhXdkEzvs7KkoZ6EHoIdU8DCO6MdWpBwx/lbaYcUsgIhALzfwd5xRA6vCfniMYInrRofe1JNGrNQ5AXUfyW99eh9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "fb5278963597c1b8eeafa21e148a10a0f1d6150c", "gitHead": "11c9b6e48d4ae7cc87b864e78e9046b484d448f4", "scripts": {"test": "tape test"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "4.0.3", "description": "Get the Node ABI for a given target and runtime", "directories": {}, "_nodeVersion": "7.2.0", "devDependencies": {"tape": "^4.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi-1.0.2.tgz_1480766234025_0.08750625350512564", "host": "packages-18-east.internal.npmjs.com"}}, "1.0.3": {"name": "node-abi", "version": "1.0.3", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@1.0.3", "maintainers": [{"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "0768a3abc28d260e747ca166c484a34ca42b8659", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-1.0.3.tgz", "integrity": "sha512-Z+cX9Z8G4nsEX1oF4Uw2lkLLqQfF85AJYW85vNn2mxjO+99p+y3kPhoDLNCV67h5yUrTt0jpEpEgVy/HZKEcdQ==", "signatures": [{"sig": "MEUCIQDf1veRKxJuVgArVWQ59uJRjz14ztdkDulDpj6WQQrKtQIgLGcDPXzj1C9R+YADEuS1zV71gsqRsumscADWRyMunO8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0768a3abc28d260e747ca166c484a34ca42b8659", "gitHead": "5e3ce0a0b2b4cb24b643e0b37787be5dbe0868d9", "scripts": {"test": "tape test"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "4.0.3", "description": "Get the Node ABI for a given target and runtime", "directories": {}, "_nodeVersion": "7.2.0", "devDependencies": {"tape": "^4.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi-1.0.3.tgz_1480874888407_0.8221668065525591", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.0": {"name": "node-abi", "version": "1.1.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@1.1.0", "maintainers": [{"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "ec17cc17204dbbd30aa7916ae36515e928ae93bf", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-1.1.0.tgz", "integrity": "sha512-c3nrLVHC6sXFAY6G56EQlWE+SpaADEnYKpqzFWbiQkkBpw/tEE0rFYytwyq4/9QstrN7234RkSY7qXOm1VvjJQ==", "signatures": [{"sig": "MEYCIQDGQUS0cEVPANoIfaYJ7pVvZYtn6oXb7C7ohCHvyimJagIhALuTpU+ENyNNBIRyIT+7yn9qcWDO8YKnJNeDaoVljJP1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "ec17cc17204dbbd30aa7916ae36515e928ae93bf", "gitHead": "ae3fd2f673f1adbe095e59081f325ace5a055772", "scripts": {"test": "tape test"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "4.1.1", "description": "Get the Node ABI for a given target and runtime", "directories": {}, "_nodeVersion": "7.4.0", "devDependencies": {"tape": "^4.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi-1.1.0.tgz_1485423761019_0.10705263866111636", "host": "packages-18-east.internal.npmjs.com"}}, "1.2.0": {"name": "node-abi", "version": "1.2.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@1.2.0", "maintainers": [{"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "63d9b09c1219d54ba129d7bd0579219501d37a8d", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-1.2.0.tgz", "integrity": "sha512-AYmFHioxy9zgp9/s2pYFuDmNaAk29tNPEqNdMZ6JAeFABLr+CS+eClnp70pwI16zlT/tbRrSqYq/gareC6HmUw==", "signatures": [{"sig": "MEUCIF7HSk5m3AufDRc8VAcrNsxSi84AQz5DhLJ0RvIFBYJnAiEA9bYchC8cKZJo8LTUoUpcih8aovK3OchR3G2yGZhkBZc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "63d9b09c1219d54ba129d7bd0579219501d37a8d", "gitHead": "113b54e3f3e189a89c277649fcfd9e0d72db5051", "scripts": {"test": "tape test"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "4.1.1", "description": "Get the Node ABI for a given target and runtime", "directories": {}, "_nodeVersion": "7.4.0", "devDependencies": {"tape": "^4.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi-1.2.0.tgz_1485894491658_0.3468257854692638", "host": "packages-18-east.internal.npmjs.com"}}, "1.2.1": {"name": "node-abi", "version": "1.2.1", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@1.2.1", "maintainers": [{"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "4b37303ee67d8f06ea6b594953c09dad05eabcd6", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-1.2.1.tgz", "integrity": "sha512-+iq0aBpJ2SjrwIvIF0KM6MM1sgtVdV0rDIrLIxQr65okQ2KwG9cGllgdkxw/4Yc8jiV/LxnTT0inc023sXlP4g==", "signatures": [{"sig": "MEQCICW2oXOsSaihh2yNKQKARo3AqSIxY1ncDKaWtB64oXuOAiBostn4agXNJWUl08ihbdu+9mwda8r59UrvifZ/Ywt/LQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "4b37303ee67d8f06ea6b594953c09dad05eabcd6", "gitHead": "490f147aea4fe1493edf4f98e2f8f7ef747f5004", "scripts": {"test": "tape test"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "4.1.1", "description": "Get the Node ABI for a given target and runtime", "directories": {}, "_nodeVersion": "7.4.0", "devDependencies": {"tape": "^4.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi-1.2.1.tgz_1485896757271_0.7254576091654599", "host": "packages-18-east.internal.npmjs.com"}}, "1.3.0": {"name": "node-abi", "version": "1.3.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@1.3.0", "maintainers": [{"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "9b3f4283092960086ad613fdd4fd4af87d98d488", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-1.3.0.tgz", "integrity": "sha512-BY47yCzJwiai7NATRcrLKp3ezbJw1yjAcH9kkP1RHYjBtLjcZg0LFq7vZvrtMS5lWlMkxRjuWZEtHlOJRCyXAA==", "signatures": [{"sig": "MEUCIG/wTMqOYxqkni3Kwq17QB+c+12q3IkRHg2hcsXKJgFzAiEA1XyS26ys+Oah7zKuZmzen2komKpXAU7UKfKdJWeN0+k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "9b3f4283092960086ad613fdd4fd4af87d98d488", "gitHead": "a92656ef96582b9ec4796f7d53fadf86424a4525", "scripts": {"test": "tape test"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "7.5.0", "devDependencies": {"tape": "^4.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi-1.3.0.tgz_1486042607663_0.5966747801285237", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.1": {"name": "node-abi", "version": "1.3.1", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@1.3.1", "maintainers": [{"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "9f8c5fc76b40d4a2af90a6f4d24ea102c3ff554a", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-1.3.1.tgz", "integrity": "sha512-vaPympnpp9FJ2+U8Z+WtJCWoPvKzv7Is6x4s1VN2voP4Oyf3zVCEe0vtzzbcnOU4XPEtc5zIBVo+vtHphKZzeQ==", "signatures": [{"sig": "MEYCIQCKSeQNk8PHph5B+Cf92I0ULeGVQ18b4Vc7BuU3hVTepAIhALegYyHWnnPK1upQ0slq4LGZxdpx18aUQZjX510D/qM6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "9f8c5fc76b40d4a2af90a6f4d24ea102c3ff554a", "gitHead": "22347595c422b847085bc9251d5a1c8fb06d973d", "scripts": {"test": "tape test"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "7.5.0", "devDependencies": {"tape": "^4.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi-1.3.1.tgz_1486046628541_0.43554249964654446", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.2": {"name": "node-abi", "version": "1.3.2", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@1.3.2", "maintainers": [{"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "b0469183e101b5a0105c017a02599712b0663273", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-1.3.2.tgz", "integrity": "sha512-THHHEhlOilr2e+DvMeCPxrrfe5mebHcH8gOnMLsrruJiL9RA30tWWog3SmXZM0gPs1zDp6bIh4eYzN+D14QAEg==", "signatures": [{"sig": "MEUCIQC/BZ1jb7q58nyKvoA3hcroxoi1iAc23lT7B0SghsgZVgIgaWBSjCEvBU8ECz6Owvh0NLh7KXxVIud+4/6eOBG3HMc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "b0469183e101b5a0105c017a02599712b0663273", "gitHead": "ab1ad5ad88bef8097b4da536bfd16958813b4f1e", "scripts": {"test": "tape test"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "7.5.0", "devDependencies": {"tape": "^4.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi-1.3.2.tgz_1486459398137_0.5797008338849992", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.3": {"name": "node-abi", "version": "1.3.3", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@1.3.3", "maintainers": [{"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "0f06f2815deba26107959d2213b36ce97437e6e2", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-1.3.3.tgz", "integrity": "sha512-n5oG92VePQl8sSh4jG+3oGK+VcLHMcLnNZMwlD9p+wr1JWzKM0PrrPI8a47KHhQATwPF/lMWSoNog8A7UhymOg==", "signatures": [{"sig": "MEUCIGSuGZoWsAC3V3sY1H5ILvGFihjlXBsrHcvh8xJHPp7SAiEAz/GaNBvDwZQxKrhQWG4mGYzCrOCSAPqbe65dg4uev1w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0f06f2815deba26107959d2213b36ce97437e6e2", "gitHead": "8dcd3ad5f92598ef55f6e253a32334169fe18064", "scripts": {"test": "tape test"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "7.5.0", "devDependencies": {"tape": "^4.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi-1.3.3.tgz_1486650356342_0.6337803886272013", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.0": {"name": "node-abi", "version": "2.0.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.0.0", "maintainers": [{"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "443bfd151b599231028ae425e592e76cd31cb537", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.0.0.tgz", "integrity": "sha512-SuKeHzMnweZ5hUuYJdxhDYA3thrKJCc+Iq9CxrFfSElO/wiWWHFhAk+50CIqmM7wc8rqZVnHYSlmH/134oU1pg==", "signatures": [{"sig": "MEUCIFDAvMEaZ+KNvj3j3WPBSNLFRvYdqJ390hivXdn9GjyZAiEArl5gxWiyapW2REZrYvAWXRJTdkq9LjJAqEMSUiUOmJg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "443bfd151b599231028ae425e592e76cd31cb537", "gitHead": "e1df71adfdd6596e6dd77e0a34884d23075bfce6", "scripts": {"test": "tape test"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "7.5.0", "devDependencies": {"tape": "^4.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi-2.0.0.tgz_1486746887912_0.0831943484954536", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.1": {"name": "node-abi", "version": "2.0.1", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.0.1", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "5767e5e29b3684c96434417f0797d36a09b1f6ad", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.0.1.tgz", "integrity": "sha512-x4HMPwNtIdgLy9fgsb7B/rb7AQbSYMr2wFLBSL6tojNuw7AEvr0fcBDyPTY8dBT4E6Imwmwvx68XBSfpJt5u1g==", "signatures": [{"sig": "MEUCIQDM+UtnYfvmEQYoWwKs0wPSeSf7ItWvnuOKrKL1iTqctQIgX17cwsNv+gXZu/RS2ryb4jwh2+jbhiXWR+nPiHiPJXM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "5767e5e29b3684c96434417f0797d36a09b1f6ad", "gitHead": "0fd4c0a5fcf19440c900aa8559f24e8fa6b5171f", "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/index.js"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "7.10.0", "devDependencies": {"tape": "^4.6.3", "standard": "^10.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi-2.0.1.tgz_1494634306637_0.5617431760765612", "host": "packages-18-east.internal.npmjs.com"}}, "2.0.2": {"name": "node-abi", "version": "2.0.2", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.0.2", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "00f3e0a58100eb480133b48c99a32cc1f9e6c93e", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.0.2.tgz", "integrity": "sha512-jUHfkWrBZBHs8OkQxCotNwKJxIxJwX1XJczhGfF255WcZsPhNaFGHrGs3uyEyxRYfe3qdABXtK2SUFs5F18Y4g==", "signatures": [{"sig": "MEUCIQDbAOML5rOm7T1BxQ6F60Gv/unHA4BEUUtMDlvPGkIe8gIgfzrIIUH/X5rHGcCItUlQLwTnb1JvUOsoPbGTCB8y1yE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "00f3e0a58100eb480133b48c99a32cc1f9e6c93e", "gitHead": "d2511fd9255942b8e73f25267fa001fbfe662591", "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/index.js"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "7.10.0", "devDependencies": {"tape": "^4.6.3", "standard": "^10.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi-2.0.2.tgz_1494635573657_0.32915789098478854", "host": "packages-18-east.internal.npmjs.com"}}, "2.0.3": {"name": "node-abi", "version": "2.0.3", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.0.3", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "0ca67e5e667b8e1343549ca17153a815d0bbfdaa", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.0.3.tgz", "integrity": "sha512-EJEE2zWTdAhrRM89QK3MsxpcMy5YX78+jFT7q4ka5Nut87Vi5DCUyb8okRYfZaGIUzBz6Zqlx4A4kiEyCoY3vg==", "signatures": [{"sig": "MEUCIQCN6ScYY3oh/R5OcAWmUocnE3gye+uXACVJKn+yDL0JbgIgTfyn1CtjrYGmn3UV8lpmOrOiZd0xJ1zkvB0+XxukYkQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0ca67e5e667b8e1343549ca17153a815d0bbfdaa", "gitHead": "7d1b903eaf2337b342fb9fd6511f625f50bd8a35", "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/index.js"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "7.10.0", "devDependencies": {"tape": "^4.6.3", "standard": "^10.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi-2.0.3.tgz_1496190468343_0.5912974460516125", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "node-abi", "version": "2.1.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.1.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "50ad834affcf17440e12bfc5f9ba0946f572d10c", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.1.0.tgz", "integrity": "sha512-AbW35CPRE4vdieOse46V+16dKispLNv3PQwgqlcfg7GQeQHcLu3gvp3fbU2gTh7d8NfGjp5CJh+j4Hpyb0XzaA==", "signatures": [{"sig": "MEUCIQCQAxJpuBACzCVA65/7H80zJPn1ixrA+y8Wi4BeUycOKwIgTyfI5srC12Nn8+KjzAigqjYVDwtaGaBIAv74Umreg0M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "775458444b2a2fc4d0b22f24ae82f8c34b2b66df", "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/index.js"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "5.0.3", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"tape": "^4.6.3", "standard": "^10.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi-2.1.0.tgz_1500028847286_0.33704616082832217", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "node-abi", "version": "2.1.1", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.1.1", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "c9cda256ec8aa99bcab2f6446db38af143338b2a", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.1.1.tgz", "integrity": "sha512-6oxV13poCOv7TfGvhsSz6XZWpXeKkdGVh72++cs33OfMh3KAX8lN84dCvmqSETyDXAFcUHtV7eJrgFBoOqZbNQ==", "signatures": [{"sig": "MEQCIDNDEPbQnwtGpm4mqKFLuRAoZRiLdMTaQ/68LLa8/EM8AiBLxfB6ql37KbnLynFWV19xOmu3WnCrunSYs0f+TeuTYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "3b92dcc6c42e74a060c962d9e3903541c326b542", "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/index.js"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "5.4.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "8.4.0", "devDependencies": {"tape": "^4.6.3", "standard": "^10.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi-2.1.1.tgz_1504263916926_0.43231524946168065", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "node-abi", "version": "2.1.2", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.1.2", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "4da6caceb6685fcd31e7dd1994ef6bb7d0a9c0b2", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.1.2.tgz", "integrity": "sha512-hmUtb8m75RSi7N+zZLYqe75XDvZB+6LyTBPkj2DConvNgQet2e3BIqEwe1LLvqMrfyjabuT5ZOrTioLCH1HTdA==", "signatures": [{"sig": "MEQCIC5WaEG3TGgjkx0255jsLwCTOieuAPSaA2OXZBybVSxpAiBbmDBTNyV2P8X4md5X4k0ZP1hCFL7+r9k2obO4eWanfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "47eca66d41a501eca3ce5b22d9a88b500b9884c1", "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/index.js", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "8.9.0", "dependencies": {"semver": "^5.4.1"}, "devDependencies": {"tape": "^4.6.3", "standard": "^10.0.0", "semantic-release": "^8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi-2.1.2.tgz_1509537142965_0.7097391991410404", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "node-abi", "version": "2.2.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.2.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "e802ac7a2408e2c0593fb3176ffdf8a99a9b4dec", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.2.0.tgz", "integrity": "sha512-FqVC0WNNL8fQWQK3GYTESfwZXZKDbSIiEEIvufq7HV6Lj0IDDZRVa4CU/KTA0JVlqY9eTDSuPiC8FS9UfGVuzA==", "signatures": [{"sig": "MEUCIQDnBoGVY3/L84UsMCfovBGsQXBpvYT+ESaDFaA8Y4eBAgIgOmlZ9ISl9oKo/FmiSZgWdJTkg5NqdWX6cfe8nVGeaok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "gitHead": "1e324fb48e523d9e2327cd1ac622879a6cf1286b", "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/index.js", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"semver": "^5.4.1"}, "devDependencies": {"tape": "^4.6.3", "standard": "^10.0.0", "semantic-release": "^8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi-2.2.0.tgz_1517158835922_0.18998108385130763", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "node-abi", "version": "2.3.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.3.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "f3d554d6ac72a9ee16f0f4dc9548db7c08de4986", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.3.0.tgz", "fileCount": 8, "integrity": "sha512-zwm6vU3SsVgw3e9fu48JBaRBCJGIvAgysDsqtf5+vEexFE71bEOtaMWb5zr/zODZNzTPtQlqUUpC79k68Hspow==", "signatures": [{"sig": "MEUCIALsAQpX2oNPcANeJ58OfW25pZhKcQilFCdWTYsI/4XWAiEA+SpMiPQ2fkF7ZDoqGAhYKPPo25LNBNdGbcWDOPekyoo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18557}, "main": "index.js", "gitHead": "86000be7bb62987381bf852b2d76f3ce2e67f710", "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/index.js", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "standard": "^11.0.0", "semantic-release": "^8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.3.0_1519598303831_0.236560607284928", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "node-abi", "version": "2.4.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.4.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "3c27515cb842f5bbc132a31254f9f1e1c55c7b83", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.4.0.tgz", "fileCount": 8, "integrity": "sha512-hRUz0vG+eJfSqwU6rOgW6wNyX85ec8OEE9n4A+u+eoiE8oTePhCkUFTNmwQ+86Kyu429PCLNNyI2P2jL9qKXhw==", "signatures": [{"sig": "MEUCIQDbEzxfRlyBIKp/ROUWYQh/+WiLvv5224mJTLuB0oCBzwIgZhiiwAcTErEimEVosBksxsDEaOk1zMDNlnYT06vjOlk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa4FCNCRA9TVsSAnZWagAAATsP/3Wq36oyn89i3rGwgrMl\nuv2sjktkxTyR/kmgmzB1ILk4uEQxcn7Px9zW6GaHvBlwGFuw6+jcljML3H3o\n8xoQFtJKJTWiXXSONV779te8zGpzMjLTlOAnggOiE9+qGB6mmPy6yIGc6rmj\nzJGIkkiAwu37YMqEEgMzQKZnHF5Mtb+NYDRZhpXJkMIfZbevddTO3Q0DEcWY\nq3MxJEit3Mpvt/s1T+DbW1WIlBCS31WNuPybd5PVjFNEyHY11ahIwPU+MugQ\nSAmUiO1aZkcZ2zmMbUbirYjpLSQw9oqudmmoFttEKg+WGKo8gwrP247x2Jtx\nojOMvAP/Tuy1W45rREJ9rFjaUgRpBBS+uCfHUQkjoBUAtZkCC4dTP1HZecPa\nEZ1xCOmEVA+O4IorcxBmlw7RGMtgjkoUXotR72KU+YTXtCKSXBB+yPyXNTXM\nnQ3DcK/ttwy+fDwbdOCLlB58ZO4QyCQtF6Tny5S34MX15GtjEhlD9i/2kaw6\nar7MiqUNA5cnVR/1CUGPlpHrIOlrENmwv6FZtG9DNI31IshxpTmjlusTVw6w\nN2xFxDI0MJpUocdoqgci7evMzEt0zb0h5syIeEnxmsqWdoQ2PJRBgxLc0AzS\naCYcFcCKo0OCIaZcVchdTyKGtZAEHBw6EAa7PugoaBvIrkmVapOkCDj43mOO\nT70R\r\n=g9tj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "09e7676d079ed0ae30c898477ec22b9a943b53c5", "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/index.js", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "8.11.1", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "standard": "^11.0.0", "semantic-release": "^8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.4.0_1524650125279_0.6000165219249347", "host": "s3://npm-registry-packages"}}, "2.4.1": {"name": "node-abi", "version": "2.4.1", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.4.1", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "7628c4d4ec4e9cd3764ceb3652f36b2e7f8d4923", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.4.1.tgz", "fileCount": 8, "integrity": "sha512-pUlswqpHQ7zGPI9lGjZ4XDNIEUDbHxsltfIRb7dTnYdhgHWHOcB0MLZKLoCz6UMcGzSPG5wGl1HODZVQAUsH6w==", "signatures": [{"sig": "MEQCIFZ63Gv5QAALul86Xbz8G7iCBxBn0ZTgxDUE6Z+WaSe8AiA6EPuFApIykSbd07iXKttnbbqYgE2OncpUPTczRu754w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7HUsCRA9TVsSAnZWagAAKosP/0Fdxf9jKoGZgMop1j+O\nKm5kuhZSbY9U5eIB7MffdhmkpAyZe9pLPKGjbI792iQae/RZVZc0ei7wU/XM\nhkcHrwui2k+q25UZR7pN8TZiugVol0sUMibBrKwBTp7I6F+OFrMyM4ey2fJB\nb8eAjJrjDAS0ROYJNZ0AtMjT7UqMbHZfyGCtft4ttyuYC/lONl2vwZOTI1Iv\ns2BdieBT6s1BMFWT8m+ygeFRDsNS16zwY24Nh+8AC+6HCqgJrElS3EgSaTzD\nOeFPq80Fg3KS/YJ6Pjvm3Wov+Q351MZv3xew3KQU5+uFUo/OgwbT+QfImx+v\n9dxt+aQhPHp6JEIHb3JfakLXUuOsp0n/sbVD+seHP12dLZoxtUv1jo9dh/4K\ns34x27RrWGiytzkjqKy5HU1zUwVrqTKSUSldigchI2Sf2v9Zwbnq3IkrdzLg\n21zCjcl4D0c2nG/C1EpXg3YM2eK6ZuCS+H3gU2b0fF7/Y3/32HpoqcS4DYxl\noPdkfvEB3FXCq/6QszA0zLzxCODKrb1h++dFuK+ddL5JovFwIZKjE6Ka5giB\nmABKavZ65GVnGID/3U7xiqhpsLrSFKzdgwDeE/PDuZssO/PmtliJH94pRCR/\nEEsPdq21UEURcY6tf7/aT8OLVRN9jbn3TECuPVQ6sEKtSfWB+pKA6id+ZiVh\nwYnI\r\n=gs6y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "396db7f9a375772726b6502e388fc920cdab9c7a", "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/index.js", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "standard": "^11.0.0", "semantic-release": "^8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.4.1_1525445931805_0.44485092152228134", "host": "s3://npm-registry-packages"}}, "2.4.2": {"name": "node-abi", "version": "2.4.2", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.4.2", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "8a77e348bf59c13e13ed4c22c5d73fce7ce4686a", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.4.2.tgz", "fileCount": 8, "integrity": "sha512-Gitd5dp03nIwR5Npo2SYvnKaaukdSDh8Y3GeRLbDy89brlG3lR5PEYdE3zsR5mJ/9L2X+1LNoE+BPjbRpHN2OQ==", "signatures": [{"sig": "MEUCIQCM6nPk8QJYJdyGwmGtGTiss8MaHwtAov8jHzRpnblBnAIgd1ZXKDWj8U3l6xRlgQwAZsd7OLRkBCeo+Vp+JVuki6A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18694, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbK8SyCRA9TVsSAnZWagAAN1MQAKMEIwGZVriv7UKmMuoG\n3bzGs7Z8a9dqOaJ8wFIQ7abRu/9HKy0NmX9CmscQYkH4Kjj53mA376Q9Z1u0\nV7gfpGxvvyfzvatcADOtcPrUYGkmq1WZFRmNQLm2Yt2fqyy6qZjB6qe9TzSm\na9t27NyVXppPODNSOGEuY6ygf8xeHjTJP3Ek3WpliNnrdU0sFxH6JegKiY0p\nuACh/CQL4ib10f/OEbexHSfB+OD8cZ4fbZa8xUtFyW1/ShNgiarRsZpJxAGM\nBM77de2iJpcKPYZIvWOPpWyY5rLjnDBftuUamTbR2Fiv4sVlLzKb5d6g8J1N\n1lx0Yu/NaU8Te5dzrZGnoQtY/nCoHtIYInPiyj1qNNz2ERuPgXyWtgJ3k8nt\nf4vowEclNQ2W6MfIeTpsbSfZxSRnHIyU6fZojvd81YnOacGSNta+8b55btyj\nY56WYAQ4aC7eRDbTDA2VkxVQ9kqpGPVz8CkOmavDWG7KOvIzidcEJzc827CP\ngO+PAjYw2+JcigGXWP6caM6AD31p+3Q/oPE5zIzGKl1URYZ+MLfTPD/IFFdJ\n9ONlWsiX65SwrzLL/kNu3wgoADgtF5tFM0qVQmC5cUthoJgvp75FV7DXTSrc\nDDUxNgQC7lCm4psO1T8DEl/erBiqPZV7cPC0JQMbb+EvRgr2R/XLHj3yNcuQ\n1QJA\r\n=Ngae\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "4f7b577dfe72bfc32966fddb0f068706652b3a1d", "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/index.js", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.5.0", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "standard": "^11.0.0", "semantic-release": "^8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.4.2_1529595058815_0.28562887587340846", "host": "s3://npm-registry-packages"}}, "2.4.3": {"name": "node-abi", "version": "2.4.3", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.4.3", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "43666b7b17e57863e572409edbb82115ac7af28b", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.4.3.tgz", "fileCount": 8, "integrity": "sha512-b656V5C0628gOOA2kwcpNA/bxdlqYF9FvxJ+qqVX0ctdXNVZpS8J6xEUYir3WAKc7U0BH/NRlSpNbGsy+azjeg==", "signatures": [{"sig": "MEYCIQCLcN+Ry8gQfV6wOyNeuYlmAUqN1g7S/FmEikedyut7EwIhALGpQCc2E1RlvSMZ1965VEEmRVOW88QwokWqV6Czk8LT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18700, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbK9ElCRA9TVsSAnZWagAAzxsP/14QJt2IQx9DCtwtZGJl\n7y5ZehJu+TE/pGGIQHxvLMAM/cyg7MiKwx358oQJN7ZGOrvHQy7LiFufzXAb\nQMKkH1JgWQEYYyYzXAbpft/yEwDuWqO3itjEV+JR5EenFDqWSvUbC7JCjSsI\nF5z+53YzZi8lrzm3rk2o66OlWS1wnV1hPvB0GuiYvTDdbc+BMcgmlafFaRDG\n+JVrVfehP5zHY3+Gf8GweZOit2g3CDrMITLoBUzHtNEwGIenjiX9oHc9uXwN\n52B+KyASsSBqA0IMI7GYBSlFfCxz8trjWSsHY3QPIpM/3kGwWwmd7DD8xLL8\nf3TNpABSEmg4PWbY98GPDxgX8MDgCRG/u4wwO5AxmTztIXMfXVTpyZQdny2e\nsl6XzVgdYdsZ3HZea/zKrQ69NCwHsHTUZSdYvWLBX94WkCLPgR5I0bm0C29c\n1MlPyMYxk+0wHNNaKkzeF8Pf3dnyWT0ncV1cGdPLpbfExT0TOxR5zA4qqkSy\nugjixNc/ZNcoKTFq6FAiR1d2dZ4nxnW3BdYeDBn/mJIqEN2NU404Buf2e97/\nKCm0NIt2UCNtEuRDh97JrNt3DFGN85kJvxyKd6hlj3uedt59DjeSkd9LmqaC\nyspzl3a/XeASbv/eolJFYtNnn8U7rRVgUDBrwEfHHj9GGA9qEHM15eGREGbt\nDNZw\r\n=UbJI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "95b1a112be387073f353cf1a219591d51cadbb69", "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/index.js", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.5.0", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "standard": "^11.0.0", "semantic-release": "^8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.4.3_1529598244942_0.9466380300402024", "host": "s3://npm-registry-packages"}}, "2.4.4": {"name": "node-abi", "version": "2.4.4", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.4.4", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "410d8968809fe616dc078a181c44a370912f12fd", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.4.4.tgz", "fileCount": 8, "integrity": "sha512-DQ9Mo2mf/XectC+s6+grPPRQ1Z9gI3ZbrGv6nyXRkjwT3HrE0xvtvrfnH7YHYBLgC/KLadg+h3XHnhZw1sv88A==", "signatures": [{"sig": "MEYCIQCSJxbniaL+sBMMdvCcJraBp+TKACC7dadeiYe8flk4fgIhAP5GWjzbQD18dNkvEcx42lrvURPGcIzpz1zV0lSfz3Oa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18717, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbodEcCRA9TVsSAnZWagAAwgsP/0ioX2Rvn9129P4N65S8\nOIgrEYexSGI/wpYkuurvrnr0VMS+2U9XyFV27OMEuRteWSc99Osl/Rr5ihbN\nSY9Gz+5LUJVB6wGkw3wbTFsoz6e0Lx4hMea9LJY3LHf+5SEd92M9WxINeP5o\nxeBj2OK3zOUN2qr0s5JH4DOyjtofmqnWclGWRtzU5rSZEuby/44C+sXZZ4yJ\n1fuqIhP0hzksYHQaWV+Xv7g8HmmYJMhQVsJ5iJ0J6V2Kj1F6ALDn1FTGG7NQ\nDxlJzHV2seePM+1DZSTSOiWChoHNvlilBjhnxYUHMLoHtqnDy8VPrxob5Z3X\n4Ie585zbMvgJBv0URIMf0jLyzyJ/VaFguQLntqtpmKvvvhjGnClYiQYX7PzN\n7dF8UwmRzX+8Ul/vd+BqctUmAvqSaxP5DaNi4tw74bx28+Gu+Z7nWjYWqwIW\ncqMIETSPPclocNq8CHm1ZL9NyE5MuVsXFdpuFqQthPKNOh0ShwbUSeCJgQhR\nTRV2tfZ+eLYB8S6GDQc1OO6MZSbomJn/pHrQD/54bdF7sHyKne8zy6uwHC53\nkXyO7cvdhHECOJ2DijtG88iGBfq7vP38nAvHG2UZ+AMKKqPn32LP0FMMHq2L\nJ1gnc4bMeRMAvn0rpS63fVQkjCcGN9T97PxBl2V9CFQWyvWJHcHeaTY6fN0M\nMwQT\r\n=Go3f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "9a3f348d2d9edc3957400af80bfaf658a7685ec6", "scripts": {"lint": "standard", "test": "npm run lint && npm run unit", "unit": "tape test/index.js", "semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.10.0", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "standard": "^11.0.0", "semantic-release": "^8.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.4.4_1537331484348_0.48815369606094916", "host": "s3://npm-registry-packages"}}, "2.4.5": {"name": "node-abi", "version": "2.4.5", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.4.5", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "1fd1fb66641bf3c4dcf55a5490ba10c467ead80c", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.4.5.tgz", "fileCount": 8, "integrity": "sha512-aa/UC6Nr3+tqhHGRsAuw/edz7/q9nnetBrKWxj6rpTtm+0X9T1qU7lIEHMS3yN9JwAbRiKUbRRFy1PLz/y3aaA==", "signatures": [{"sig": "MEUCICcc4ZUtEHYTlKhERVP6Nc6xK+kSxmGf598fgeEYchPKAiEA4oVYVd7rJaOPmW47TUMvmYjsvD4Izbwbw8sk5g0aPY8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18810, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbskAMCRA9TVsSAnZWagAAyTkP/2nvY7c9kGc2pXO3Uxcj\niABpuz74BQJJO2tLFhycjqp26XKuRaPHjgenQ7Dk5pwmNf2VVehZ1RlfuUgq\nhsba26u45k45lqhHksnl2Pc8+it0B5YUVBkWvfpw7MCQESU+slkA5DKnylH6\n/rCn9tSVPKl9ReDvB820rPzm0dToLlLcjgneaAvo8bTdhWs5FMwvuMBkGcqk\nVvU7kCzxMDGQ8K97y3DJW1pylKRU+4Hr8FSEQ4NFYEp9hIIEjYrS2MSWej4C\n7CViwKPvZjFS+JFuzLYUcUC/tJRnQnmj+O1iQ8Rt6hpjlBZQNCC3KmnWys+y\nPjkaGUIxGXHDgwmkcCTHErupvmhqFktThxQyhXw8knV226RgZWKVCFy4+ury\nTDO31h/DBaKK/zppurr+hLv+jlXXPN4Yjj7mRLJ+527ScxOzwng8Btv6EC3x\neTBw+p7m/R2KX8eRn5S9Vym0IDnRLC94G0SmLBNhKrgzSlnkRDmSk8YCy8Bn\nza3y1mNPh9LYs18dA9m/yucBzY/cHo+mqfEwMi7i7WbbeP0Bk6T+Wtk0a6w/\nErficuIY0X7OnDk5ewggb6rQljSpze471XXOr7ScbVHROj6A5tLcyofrewDs\nUecojde8/exs73cJME44RB6mIb7nwDEgRfe7P5/ATMBXP1654+JoUJ+xDjAo\nVKLj\r\n=lYnA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "d11b1aad31f715135b871a21423fe465f3929537", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.4.5_1538408459743_0.758087005640742", "host": "s3://npm-registry-packages"}}, "2.5.0": {"name": "node-abi", "version": "2.5.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.5.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "942e1a78bce764bc0c1672d5821e492b9d032052", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.5.0.tgz", "fileCount": 8, "integrity": "sha512-9g2twBGSP6wIR5PW7tXvAWnEWKJDH/VskdXp168xsw9VVxpEGov8K4jsP4/VeoC7b2ZAyzckvMCuQuQlw44lXg==", "signatures": [{"sig": "MEQCIDRbbhxiRzNf4c7zbLwQyn61gxxBaTscRVj8tWaUSyRiAiAtEdZrZJc0J/TGf144XOpqQqfCBtGNBkYacZJLPU82oQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18872, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb25C8CRA9TVsSAnZWagAAFnMP/3ypMYws2BqDfeX4EHKp\nDBlzGfManLMr3rCktNEdS46khXI6yMC4YCA/DQkuZi2/2SHkuga30y2GolDM\nuJ9PU5bb7en0oPVSlDHFlKsS972R5i0yirLiVod18p4TDhfwTXuiBUb7FFMW\neTTgwtz9nhu7ubvaXrxFv0+lzayDySSTeP1asJA+4Ky5sdPvXDTG0w34BjeW\n7f/+h5vxbpV6lM4vAd62jLI+CxNm9kL37FelS3YOzWxZ9FHolGfWFUkiMbxN\nrbJlrYj0E0kd16NycfBtpNQaXfJBNGj12h4qMe0WFYDaY3HOr+0VKVgsslCf\nwNUZS5h/ASjVdDhvpRIl7Qk1IL2PRkM5qW2LtPtrFFOe4hAy47qZjw+YfrJa\nvM5orqXbq60FpKT9qQNox6rfk0FsPAIMLAzrvDq5uv3o8QB3QVNEyc5OamT6\n+cc3Fn/BzP1UhSjoRdOpGRJx9JkBJjJzztOphoe0lo5G5JoOFGYeVhjjoSLE\n8G/LTb9NXhEfkP1xxeQEpLtxpru/azbgXizNtxUVChkZce+0Ipw7sIM/xXNT\nzfXKPQeIRtiO3A0NSE/V7ZuwHKcBPRl/yGUpJjAGw7fth1Ug3RxXQin4gWN5\noSkPEte/aU1zxZE/k3eEs0rDRvG8qt/fZabF3CSWgDY3dQDDiOMAhg5qa/+R\nShFu\r\n=gKe8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "9db6bd1be92fd7b1059ad468b662f99fc632e9a9", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.5.0_1541116091448_0.11665582885516446", "host": "s3://npm-registry-packages"}}, "2.5.1": {"name": "node-abi", "version": "2.5.1", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.5.1", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "bb17288fc3b2f68fea0ed9897c66979fd754ed47", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.5.1.tgz", "fileCount": 8, "integrity": "sha512-oDbFc7vCFx0RWWCweTer3hFm1u+e60N5FtGnmRV6QqvgATGFH/XRR6vqWIeBVosCYCqt6YdIr2L0exLZuEdVcQ==", "signatures": [{"sig": "MEQCIFXipo7On/fWom9cReslgjJqxelD3OQw6gXYn5RonvjiAiAQZazXzNLY1F4SYSFWDNBKi5RKUmnCoARvyLVuACJjsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18866, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcHaxSCRA9TVsSAnZWagAA86YP/jpuoCxNuAy0Snp1QPDW\nrPceLfX/sCS2JZMQsfENZs/E1ylRSrv5C6DBMW6SYuvPWs3d6bKhkqSUO55s\nre1/Fq/GpVqgK3e0TpgfWKjkZGVnNBVGjwvZJV97Yb2BDmktu5i6sRx07fDP\nC24WJB4etabifGcGEzNcgANhma8HeFfulCT+uR8WWt1TfH/mUf4cBees5st7\n1Xv0JnkoUaCX5ommy9nb61ajeW+gGvusB9tUxjvWyRhR4JyVhV0B3udQEQK2\nQxSoTKkiMrY/KxUx/fxXoP0z2ayYpIiRBR+L80IGa/vyXYAv4eFajHa7ZD6z\nTpSjOLz09ei8kQMQPL2ydDOjMDRsMIZcmRXS67hPx9rN08B2+p2+nRWoCMRp\nuYs4XypJgG0ed4YP9wILgu2YKLVGOLWiV+GHSYyc+aKceOSRPn3Suo4X9spd\nlqTGA/JR3pURKVlHxwZRdmzjA8nAH3KDdhgDKj6I7KdThoQ/zzkJ/lxDd/Fi\nPr39fDIH2rIY8Sqd7+WAGKq9RPMwapWHGMdMU1hzb22CFYi0POpA5/cgQ1IU\nnYqQ2HskFgzYrYy3mibqy3IY7C1GnHmkJXY0IkJJErmwWC+qXVVg3+8U8a3i\n/Ktwz2riuDHr6CgBeH5ZS1hBReiKwCWsTvKzj2X49SrXOoYYVuhxloZVyFYH\nfdc/\r\n=Mupm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "7bc96648bcf9f3e6a1f2a07370d3ecfb6dee465d", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.14.2", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.5.1_1545448530441_0.9271784398622274", "host": "s3://npm-registry-packages"}}, "2.6.0": {"name": "node-abi", "version": "2.6.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.6.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "3adc69b28b334a0556fbadca378c7d18b8c82397", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.6.0.tgz", "fileCount": 8, "integrity": "sha512-kCnEh6af6Z6DB7RFI/7LHNwqRjvJW7rgrv3lhIFoQ/+XhLPI/lJYwsk5vzvkldPWWgqnAMcuPF5S8/jj56kVOA==", "signatures": [{"sig": "MEUCIQD70lG5+oCOapn6j59Ktuz1L+hpkH7iUmGdVztUoZt2xAIgG6noEBN706d0uQLeS/cFoDNrT/pUhmCP0ORRpEaVGSg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18937, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcTMRDCRA9TVsSAnZWagAAoUEP/2+48LEFSGlOd0pvGfOs\nX8cZ4+m87szdqUtG86RAvrw4RjS7b2epOb987Iwxdm7diUxnNnt8xXFhkYBr\n+IYt7AGILi/XiM1C7tbbxIV44AHrVrB2ysj+kEiBXquvTh04B0/J3Ottc11x\nhsGgHti5nA7vESh7i31Lb3uCjJ5rDNFtRP/IJGvIzS2Bt4l64Wjw9EtN3JN0\n/mZFBTc0g7AgD2ZZm2SgQ+GMhb46qHBy0mqjcpK+dEX5ZjmHvFlDbzL52OKD\nZdIt9QCzNpEqHblRZ82vy8TfKGs/lreWknsZU0tBEyUUNVAK/pscUYB3fE1k\nODkqoRP+QhCkeGCZUbRVg15hXQDyZw8nIzsQ2gOeNPnGfZhdXzW+TFZ1zMua\n4C2COkRm2UU/uE6fFZky7cMoLZaqhUBktiialVOPIz6/BpylDaXESpUzVhuj\n7b8hAOI3SFXoFkH2v8320E9zx8vBT2QpxMT8UlI9ZEUbLA0tDhaZ7+WX50s7\nuhswJx54zpRa3jLImveSj16P1LNl/fUCQBSjZi5BS5sCQn4IyMzscrlE7/Qf\nXBthJow1trn4I9bma+ilhkLgH9HQp/skQKixoufSYKAu8h8JzTXaj5OaPfL1\nzuah/hsjR7dcbqd+gemug1XV6AiQokExfu3EvskAoPeIBNSPzXSDw5LE2JF4\n9hmC\r\n=dVPm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "458f2eb39c3cdbbbeb060d88c48acc987e569f74", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.15.0", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.6.0_1548534851242_0.25608379012202476", "host": "s3://npm-registry-packages"}}, "2.7.0": {"name": "node-abi", "version": "2.7.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.7.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "e2f814088ab97c85504ae2bacb8f93d5d77cbc2b", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.7.0.tgz", "fileCount": 8, "integrity": "sha512-egTtvNoZLMjwxkL/5iiJKYKZgn2im0zP+G+PncMxICYGiD3aZtXUvEsDmu0pF8gpASvLZyD8v53qi1/ELaRZpg==", "signatures": [{"sig": "MEYCIQCUisDNL8eTTIOXj0j1v/hLbgX8+3vUtdJR+lpmr5ezrQIhAJdi1AqHwiN/N5LuDtN1hMX+tnMVQUDF/SQeubgpulSq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19002, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcV1JnCRA9TVsSAnZWagAAzGgP/1o5PVDcu83Rtmz70h8s\nMfZn+przhqijyMVu8Q2R55qgQmKnamZoUW8dpIJT2f0yZXdDaNMUGn+kmAh8\ncXYgCBq6ve5jvc283S110EkTa250lhX22Q5q5ctqS/wfQHDE7sYTn3ghOGm4\nD3Vrr6JmheiN1z/4gufIUekXviFepLnG9RSGE8qQiLBGGKa+CaH4iMGuRu7F\nP2LbGvtfzYq4AJMtA7i2ucJC+8K/DXEco5zqFOxABEgflClfdIEs9BG5Kdb/\ndqfsAPX/hxhFCiye8zh986cs6NU/87i6nw2wLtzeWMTgFuSD8BHn3MA8SYtE\nyzpBJVO0URyskHCEH0i4UuOQ+qGV2SqsLWKI5Q7xr0ORPH97vB3PUYAJiUyk\nSCHlD+SyWNrPrBx571plPfsToiQ72s0O3DZNN0fjxejh4DUcV6l/t5DRMI8w\n5dwAC2o5I65zqxwqXlaeYJ+9JGRqPkw+20SuErsgEEcgXlXUzUiiLCO+UHGw\n8/Z4vI5WhJKO4MF3LDQhW+Ck55VNjPYfBRP7bMrADEBCxPGidHCcEK7pcT6w\ns2qrQDvONGlcOs7tUZOnS8NUwq0BKY+YOgWF+z+wlLYVcSVVCQdi8Qm+8Gvz\nG+R4XSoDRVmF6k/vPPHJrkT7euQp0x6DPW6MWKyIZAhYduNtmCQf5sHrzXJI\nlJsO\r\n=1zSL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "9e4c1a63e63f8994cc2f5e3efee43785c9eb1c8f", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.15.1", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.7.0_1549226599108_0.6825815727593161", "host": "s3://npm-registry-packages"}}, "2.7.1": {"name": "node-abi", "version": "2.7.1", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.7.1", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "a8997ae91176a5fbaa455b194976e32683cda643", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.7.1.tgz", "fileCount": 8, "integrity": "sha512-OV8Bq1OrPh6z+Y4dqwo05HqrRL9YNF7QVMRfq1/pguwKLG+q9UB/Lk0x5qXjO23JjJg+/jqCHSTaG1P3tfKfuw==", "signatures": [{"sig": "MEQCIEoxeGhun1Nj0GykfUANFFYGCn7vkSWGchw1Vp1UE4HzAiBeyTJKnl9AhQbaiwQHJIvEgTQpJOtM0wPI/iER90Vbkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19016, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcYeKhCRA9TVsSAnZWagAA4jUQAKChAlQvJB9qN93BJwHI\n0Bp0IWVHNUcOLJtNt37tdBcz4NFVjweuLDR67xu8h65LJ0COFhYTI11hujCG\nrGbzQF71VmAB6Cq6SlVNRKVZX5FxKJmObXvE3RU81C2VoE4zAWMkVCuD/zJZ\n4Ci9syApKZtwfNJ/GAYT/1POYw9i90q9R/hl/vVZpYisbMi0Vg+C2LBv3Wyh\nUBRM8SBr7YuCLuAl5KVuiw+CzbUbKtJviki42N9QKC+2v+AGzs37I5g1h9i9\nQ+3W6QUsV/h61TK+iqfrovDMhXSZqRw99T0SrZoz56WwUD7mWVr6PqvxVnpV\nPl5ehsbHAEqzo2ttE/CLYLeClvCVdFGPjba2J8HEViDhw2CTi55meJltw2CG\nanhbDlE3e3oxILGR2d4G1ge/aMgqfn/pcLcbNhyvX4eibiStM+yV44TRrxTE\nDoyzXd1GGMPrOlmBtlBEBl7tZtXQ+5rQSbLbeSF9V2lCkNiYu8M5QL3L9csf\nfFbmRT9ZJ/bWf7Gmh6kXDs5xI3Jg6tPxL6P7aYv7WEzxvtSLlGlFTYr0LFmL\nsQJIXAn6VElDeBI3rUcp8d5Xa/f0D/2O4PcMyT0KuTUtM9/L1I8HGBLRqtDR\naofzEL8XfREroeyBTV1eZoefMpFnHETUfPWH6pfKxrHoIP+X1RW+SuvNFlva\n9KGN\r\n=mPsv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "9c5acd11ae297c4eabb29c531334dec4b09ebdf3", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.15.1", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.7.1_1549918880479_0.8845012284341398", "host": "s3://npm-registry-packages"}}, "2.8.0": {"name": "node-abi", "version": "2.8.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.8.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "bd2e88dbe6a6871e6dd08553e0605779325737ec", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.8.0.tgz", "fileCount": 8, "integrity": "sha512-1/aa2clS0pue0HjckL62CsbhWWU35HARvBDXcJtYKbYR7LnIutmpxmXbuDMV9kEviD2lP/wACOgWmmwljghHyQ==", "signatures": [{"sig": "MEUCIQDUsNFkg3JpkIqnyxp//1Xr3+R40HZ8qtxl8a82belDSQIgYO0ckfbm2iRuHMo5t107pylt8pVakp1IMQqGhgwk5wo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19343, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcwfrgCRA9TVsSAnZWagAADVUP/233DktDXJFcy7t7krPa\nDpwDJ32ilcsVP51Jl3jEV7aJNTc/8IBO6czZ9LRnbQ/bJC6kVRqNpxwKJAkS\n+7tkCacOQgYozbF7s8XKWImpGY99lfgzhLQycXMFRBO1qPoE0gHFcnpP/7WC\n1KOyZmmSaRQ7m0TCnall3BV8gkJ5zX5hHt88mcIucS9Za01mQU9HdFRHwjhX\nBhtmFqBGldBKq26Rw0thi7IzoI9j6ZDf7dmjER5d5tGYdq+WWSr9W9Bp2Yhd\nQfgv/v4eXHunXKeI1jTOQ3l15YkZJRUQPkqyaQvCawWfG5BiL1GTsFhnQFcT\nIUfjGsuV71RNmaAepYpM6xqsnirHQcFScgEvw3P9Sye9+yGYqxB5soA3ahAW\nbcpJ2FJRMFJQm1by0k8Bgf0at9zVhKXvQ/KXNwUN6KWtctQsysZ0IzwJlh3g\nT0H1fph02DQwHrMMs8cuVxPbtvlcTbNysK50NSnWk7oAnQZm0BgEfgwev/QY\nPW0FUhP0ZreVRyyA9UsQUBSt/TQIAtzp+Fv2eRgnCbTWHzq5CsShhe4yfAX1\n87v9qCdgdWveIkEQJidFaYR4yAFEsAP/mzGHmCHDTzQhD1XgReD9LKueJzoZ\nkXtRXPpljEytxAJYFJLMwROULhPwltWIdxAshZs+DkOQ1kbF/zuIRPi9PUaH\nmUmo\r\n=k+YD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "79d73c3b676add0bd63899f021dabfa7e240be44", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.8.0_1556216543318_0.6049558457479269", "host": "s3://npm-registry-packages"}}, "2.9.0": {"name": "node-abi", "version": "2.9.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.9.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "ae4075b298dab2d92dd1e22c48ccc7ffd7f06200", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.9.0.tgz", "fileCount": 8, "integrity": "sha512-jmEOvv0eanWjhX8dX1pmjb7oJl1U1oR4FOh0b2GnvALwSYoOdU7sj+kLDSAyjo4pfC9aj/IxkloxdLJQhSSQBA==", "signatures": [{"sig": "MEUCID1ZYTJlkNKmKe3qlgyCOsGDzRqECGcWLGeEOfwu/Q1dAiEA/CiityyquabUlQmIaEYB8JyQe0iqZV0vdIJNWihMQbI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19413, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCnaaCRA9TVsSAnZWagAATLYP/RyQRwy12eH8cxbytzYT\nymhGd84WRNaYDtuKjD0nBeNgoodcueVjTngRgbvEBSyKRURFIgq21uxylrYI\nZQxu8GPgYWd+scsK+FvfwvBuFW8+NppW8/mkGTG2MOc0awRKaaUKkwfu5Igz\nZgBCjMQC99Mi0bVSn/nABgJbLLM7E6HvCZemdHnZM46BtkMMJNcwoXMOwMXn\n7Xxxb44CsfX1Hyt29u+b292ICRCv3Mm26Zz6k0f/t3ZuRnICnEmjrK57r95S\ngyXPAKCin5DqDDW2OdjyOwlsOsx2I7dwEeAZAxSLUgnGq7Ua9U0YfDL9hwR6\nTQFDaQrD+Rs93e8HQl/7dIOLgDppcd9xUUis5MYTeB0ELdDYDEckDlXBfyiv\nqn8XYHLD3fmR6kOdB5NRhzGUpX9+GvUtsaOGGZATWFId2kkHmmyONiodLApy\nMIPBnsUvF7bETGL8LTyJ5t8STWmmwSUOuah5jJeAPjsnyc+8mZYMNFpOvcmg\n45EG1ZhIM3G/Acy6omrVTVfFEjMgPhduza/TVHAV34R83TodwhBtSOr+2tkY\neVm4blRymZdLVn32kobWvzbW8hc2dEHv2a81Xtg2l9fclx2FIqdTXFQfQOH/\nBbUgNxUDlXU8qwWkJ281yWUviWkLgbKwtFvTjBfaXGk/UGNwFEaNrYPV3Xfp\nNmqc\r\n=AviM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "165f8849de4bb4b04b265e9eacb9c5ac2a4d3f8a", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.9.0_1560966810011_0.1037879959007082", "host": "s3://npm-registry-packages"}}, "2.10.0": {"name": "node-abi", "version": "2.10.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.10.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "894bc6625ee042627ed9b5e9270d80bb63ef5045", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.10.0.tgz", "fileCount": 8, "integrity": "sha512-OT0WepUvYHXdki6DU8LWhEkuo3M44i2paWBYtH9qXtPb9YiKlYEKa5WUII20XEcOv7UJPzfB0kZfPZdW46zdkw==", "signatures": [{"sig": "MEUCIFf+tt0+42VFLlPAQG1Jwu2PPAi8cZCCXRpeOwRBCPKLAiEA2ouZcnrBy05qw6vmcK0sBFDps8Vqj5ZJxAKSarmY9s4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19409, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdQHLZCRA9TVsSAnZWagAARIoP/0KUkQWkgUxAVguaDZGM\nBcquNA1KM6S+jAbCN5ibvGxXf8pRD8RdisYGeoCu4+IicQCJGPq6MpcTt66Q\nnDKLm2glbJLKNFOkAYX4ojclvEosBAx1E4lfwD7LCR1+5CkIQvm1NrT77T2l\nHA+U4rbgBt/YpA0H4Yzc0f1LPhxmzScQa/x1qzc/9cGlcYZHbvkTHlffkMrm\nXe9ztKek4OAKmi2PUWCzqS14koiSWBINlPWp3TsxJ4Dpd+a3hqbDHbchcHj+\nP6oWqhHPvkB7CFiTN+dWxBSajLNzYI+DBfok5sSG/oNOWBZwcszEXPG0ltiD\n2dHlkQxNhC9DdLVdKkZcAFo0YkPqW92PxaxuQB586fPjyyuk/HCTgSAyOCu+\nqzrgWg08uuGOl9C2imTLibvSeLuwJz1V10h4VmI02J6wFZJBPJKCOAGsw5fO\n7937jeERyl8MWRuNkYBxWHjzuqriVWzETJHSz4PFyZX/uaa48AXZxr23f11/\nzFR3YfZd+oMdXPt3ObAExJxBdjTMyzA/lO23+NlPVKN1sDzA86Zgk3qBiIcF\nu5Domyn98THtCLo386Lfpfck42Rq9bWAGOt+UYtx3PsZ3hJ4IKJdJZFXz0Ya\nXYLnfLThTVD0TTs23+Bz+kBXUkbg7et0ax2oGv/80A68oS9QwTkc+lxj18t3\nVFqk\r\n=P6i7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "1ad02ee49e5ae637e4a46630fb64343ede8dab40", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.10.0_1564504792999_0.35531190427242487", "host": "s3://npm-registry-packages"}}, "2.11.0": {"name": "node-abi", "version": "2.11.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.11.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "b7dce18815057544a049be5ae75cd1fdc2e9ea59", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.11.0.tgz", "fileCount": 8, "integrity": "sha512-kuy/aEg75u40v378WRllQ4ZexaXJiCvB68D2scDXclp/I4cRq6togpbOoKhmN07tns9Zldu51NNERo0wehfX9g==", "signatures": [{"sig": "MEYCIQCktoUDIG9K4uCsAEXY3uxE1wtO3sBgPCveY+nF1FDzawIhALdG4mZdBhHCMaVWDTSIbxyFj3eSRsUQimS0tduQKptv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19480, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdUdVxCRA9TVsSAnZWagAAVbUQAInvCQMTVXpQt3qK6O/k\nO7Ia/n8rpp2ImPWugfHL7hfu/sM6UbKoQtsegLCJy1wv7kA7DrpONN+kDFp0\nvU04YaNdSYwvsaorPyutkfmPixN7ftpiTOJ65/JtaYXhC30NNwp5hwCgmY7Z\nvo0vXx+beL+SEB8Ykm8zhXltxt5k4leoRBDrKLfEvoRliYaMLc5/NLn47UCa\ncZd7gSbyw3Gg9hy6pIRgQ47+BF+hxdkqooIR/a77PUDnzq2kzQF2vtetOZZ+\n37Wflq6namMOrLKn8DUKI9wtmSuADDkJQy0mRCiNEh6AkPD4i2t9eOOIfQjw\ntHYKHpFiEX53AcGU6lJIyw5/IFDSJbUgSSDSnXWLvIf6evpsTZqGqaebJupY\n38q2hrBkA5d69FvBQzvRhIjpQwnAqYvaJ8oPFc779aaisIQDWvaboiWdahMI\n+h5Q0WAOkOwcM3493ynx6SZoEc9HnxqADaTaPIExoVEKleYrfVSAXOgZACPa\nhmcfekkAi8lTA0GMFM4RmsfBm/s7xr0DZCTs5s59SqujQB6wiLoSbRb5wxV4\n2wK1lgs5rfcYoYhrdgoKWHVJp7xfew1T3brMQvODW7SYIMap/QjVTUU2QIBW\nf+Z7vRIe1iK8CBkwK0OF4/HQyxOBlb03lj7j8vYhpEa1NEzYKXWpH1nYHMEd\n6fr/\r\n=PJ7M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "3651a264d7ba540689ae469ed88c075dc340c188", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.16.2", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.11.0_1565644144944_0.8904855376323362", "host": "s3://npm-registry-packages"}}, "2.12.0": {"name": "node-abi", "version": "2.12.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.12.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "40e9cfabdda1837863fa825e7dfa0b15686adf6f", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.12.0.tgz", "fileCount": 8, "integrity": "sha512-VhPBXCIcvmo/5K8HPmnWJyyhvgKxnHTUMXR/XwGHV68+wrgkzST4UmQrY/XszSWA5dtnXpNp528zkcyJ/pzVcw==", "signatures": [{"sig": "MEQCIASJtDVXON68obNhhm84QPH6vbdPqBjkRN+xQQxNuLiwAiBYCurTVQ/qwkBbcNe5mgsNNiGBiuTw+C3ZFx+X676Fbw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19545, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdrf+jCRA9TVsSAnZWagAAccYQAIRlVlzHqBVuq9zJK4Tn\npIUk5wXKpOJEYIMRlDAQHnSe98/cJffTF/RQQGzyrezNybMEQSFZz4Yo184J\nU5ZuIwr6TRQRUbPlrHmKnMwdCUn8n/BVOjkXqo4KO0+TNypI4xKxKo7lhHBN\n77e1vfvg1uMxBdAS4oBqizFq5t8ZFpHd9LythAra/T+OawvBCfJ5cGbb94zI\nbepFjZFudLrgHcECiAiS4sn/kS+9jSmjZXqXrA10PLeOHpZEUyBIckmIhdpl\n33FoGgH4lx8lonhC/sM9zcxLLjldXhoHMPPGC8u0PRjCE7MVvavikwk2lGbw\n3c4AzGPB5J9FwDs9kEDEL9/53OGbADubenN9KwJl2C7oxJoLm9OxrTMCFX+K\nIsuuh6uPXusXawHuDWr6ZsokrIxGMeOL7s4+7qJ+ixWyGUhF3YnH/OHvO+vg\nWRJIIXP9d60ECk6IFCTHA8Vxq9imIh/RaBzYnfTFVnwqzS4Tr8+0BHTqa6fh\nTYu1i4rNypdimW1yzscBYGh5GJMAmVjvKcyoPw80dXlMPRH5IfK7RxncNfWb\nvcH6XaY4t0Nt0hp6NRawz6SkAgdF1J4Oo2WFS6ZxR0ZRceA0FhdfPrfLfvB/\nJQtpcLPHsfOdUUpAtDQCjkgZdlOFshnq34nil45LeZxUKKAIEk7nK+3LtFBF\n2avr\r\n=1JFH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "efa7539788036c039132ce0385068a49c084ea51", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.16.3", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.12.0_1571684258799_0.8924227995335277", "host": "s3://npm-registry-packages"}}, "2.13.0": {"name": "node-abi", "version": "2.13.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.13.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "e2f2ec444d0aca3ea1b3874b6de41d1665828f63", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.13.0.tgz", "fileCount": 8, "integrity": "sha512-9HrZGFVTR5SOu3PZAnAY2hLO36aW1wmA+FDsVkr85BTST32TLCA1H/AEcatVRAsWLyXS3bqUDYCAjq5/QGuSTA==", "signatures": [{"sig": "MEUCIQDGiE7KMwZkrDNorReXFF7sRU2Aqau90htodTash9HvJQIga1I1JKuq1bvb8EIUDzx4XO3DEFpkUBJBAPigcQQUXss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3juGCRA9TVsSAnZWagAAukEP/i4Q9MO3fR2nop5O2w7G\nWeCL8MND9s7b7/EyZMQU5/ayq1odhRRdeI7znFzJPAbJe8JkbaT3dqrladfK\njYkGnoHN3zdKuuwz7v2n8yVMitaEj1KJGNoRvbl0my538uy6KKTqiy0g05gu\nbjtAXO6uvE4XK3OYHL/LIH72N6IiU0X0wAqimt9LUmRZrOnGFi17l3mzKFfr\nKOpC9UdrSrpu0I8mHvI8qoOiBN11LAGqxXJZv0ajOYFGuinF6fZbnKmLpjQN\n51U4cr+ze5bd5Ooa3IFO0dZVb+gWVO+gxioAWBWxDkhahsnHk647eMJWCnLg\nSKQM6NZ/Va3yG9FmuKTLq9Dk/JGinLcff4bp99qvCtuC736za5fPWFYRnzT5\nN4eXzKyz9iMyJKFq5jmRPn593GheT0xMGVBdDu0mZI1fb9EAEA8EbTQlAWYj\nwxNWGp5HEEX9Bkhdy4XLvXfJfTshBKlCA/lIQsIwNQ33rwe9v2OmAXs5OFf7\nDz90GoiphPEBZk9Hj7NbxbB4obYNEbfE9C5MPiBAw6d5uHL2HERAw40FtkH6\nSNyhyodTeizGMRnoqdq8UDY8vbk29FlVG90JzQp3IpTp4Ha2FJUlPMEAlyml\nHct++fPDjEkgTOyuHafyrjiLirBxqWDawcvgb5kZlGsTYDVqT1CySMBUcF0E\n9WTT\r\n=CYIG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "9413d366a68d5c4fbdcdf7b039050804c8eae44e", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.17.0", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.13.0_1574845317842_0.048730622141492264", "host": "s3://npm-registry-packages"}}, "2.14.0": {"name": "node-abi", "version": "2.14.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.14.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "24650e24e8ffad2b61352519263f0cf4e2ddbfe9", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.14.0.tgz", "fileCount": 8, "integrity": "sha512-y54KGgEOHnRHlGQi7E5UiryRkH8bmksmQLj/9iLAjoje743YS+KaKB/sDYXgqtT0J16JT3c3AYJZNI98aU/kYg==", "signatures": [{"sig": "MEQCIExc6q9wA3FezjVRzvPdFPZiNfWMW1EiAvPqCW42gPWuAiBFY1tvx7F4FwZyGG0bMI+I85MnAPF5AFT1wb6tUFs9Cw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19639, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeOcSuCRA9TVsSAnZWagAAlxAQAIDj41XDOo1RMuwEyhow\nhFtezvP9gfW7Ud+hB8ZeoFvWmrMbey7oYwkDAyZY17QMW+8/ojDsJUitWhLN\nbH9VGeWg8zeODBoVAAmULWe0vjs/BWMs20I5i46HvN5wxr5/EtwG5y3lmU0S\njQMCUy+YsHXq5xjlSOvDjJIcuIHcu6jOsDgQSI5/tbDyqAze8Gg4TuhB6D/X\nQj/J0lwzuZarNu6ZkS54aG+jm66LJwaZ/voUnYOM3BuW4dfPM/PllqtqWUdy\nnvwgaBi8+yuqeOSCqgjdDi1vX2GmvMBOGPjdF8AN1LXcQTOsm98UfWOe5QZ8\n3hwoh0zGIpJAKgGnZDb24/dQxTYHtnaYdsfAFhj7ZsXlcP+Wa3lsBhWyxIu4\nvKknPiGCEBlOog0fXQ+cEJf8v0UKvBszmx5Lw03DWTapDA6tTMXkig9siEwl\n+/5sShccdZaqFU9V906Wr/F6oBCS3ClYhce4PGLtK6F/V+Hyu1Iyi/tJarnG\nBJErgg1fcmeP1+WdQnao64NqAY15f9JEL9h2lMdGNTX06VkyTiUcxBrnINFG\ndQZJNL6zns6z0RCuYWZRiJFq5JKXs8usnf6K+CZkkIBDJ1ln+z4A2HFYeUs+\n/xnrovMgYjaF03XTby7YHFc5opiyFsYnsXl1nBZ1crPcvfpdTCXVf21EiZr3\nNh7o\r\n=JXBa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "556a6ed3df618eabf309e4e5d45222506d3e08e7", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.18.1", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.14.0_1580844205833_0.6196442334345946", "host": "s3://npm-registry-packages"}}, "2.15.0": {"name": "node-abi", "version": "2.15.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.15.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "51d55cc711bd9e4a24a572ace13b9231945ccb10", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.15.0.tgz", "fileCount": 8, "integrity": "sha512-FeLpTS0F39U7hHZU1srAK4Vx+5AHNVOTP+hxBNQknR/54laTHSFIJkDWDqiquY1LeLUgTfPN7sLPhMubx0PLAg==", "signatures": [{"sig": "MEYCIQDnJQMz1rgH7bgonfKxgQ22VL30pY/xjNhI/82mZ9ozJAIhANWoVG95Mohj48yMWMrs3hA7ILSWasQ2yMZUEKyFtOSW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeRbDLCRA9TVsSAnZWagAAS64P/ihedOb5fYhkVlzXk/Ef\nHSgf+HIHuglpdmt2wYDRgWdVd+PS6Sucmdbt6Tc8N8ZpkrIX3mabs/u71uKX\niXZedflfG7sF0dX0yt/XdxAqkeSeFUHfkufhtHd9D6j2zEMhmVD5qEOq/ZQB\nkqYwSVAe28j/3mJVT0YYDwZoUDyIq+GDIj0YlogKPfipMM/YOvUd3xLU/zux\nEWt/pyeu/M/R7bw4NnmjM7SCGbXb8kR4MfscIGQ2LbiSVeOLN3RdNj7ZqM5g\nXPlLyV2IBeR2Qt7wmJaP96cV9FLksEfzEuT9TBYAMmh+4NbbBRP11DffZTJP\nfGF+Ib+zRD6eoDMsEDXkwG7tdRFZgid8HC4orlfxWo+2NUGxkVilA+yQ8hr7\nz7O8dc3/yt1IoB1+ljDeTz5i+AlYDHTc2q8+uzw0Oq/kVWp53Fqxs5JOw1Js\nhBLrErBiQo/q9jYNb7Ee7fgBq9hWoftTzpdVrK+tZfnT4ceCcn2hJv3XmIhn\nc5uAXJpp96TOUJ7AoPr11EG7QkML2mqb6Ef/9x2b1bsAxXn6LOnNdffoBP9E\nldrDv40tURDdzwNUI39QMzMRXSDwMwef8r2BWMKNq1QFbVPOwpSANPZMwaff\n5AEOCrpOt8qrzu2jqFb22VlZkAIzhTBn39C5R0MWpk4Ydalm8ipv0ujIBPy+\nk7a6\r\n=G9/8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "14ee3eb65cdd51cc0dd0b7fa3220b07451a02ef9", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.19.0", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.15.0_1581625546341_0.8243358804748091", "host": "s3://npm-registry-packages"}}, "2.16.0": {"name": "node-abi", "version": "2.16.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.16.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "7df94e9c0a7a189f4197ab84bac8089ef5894992", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.16.0.tgz", "fileCount": 8, "integrity": "sha512-+sa0XNlWDA6T+bDLmkCUYn6W5k5W6BPRL6mqzSCs6H/xUgtl4D5x2fORKDzopKiU6wsyn/+wXlRXwXeSp+mtoA==", "signatures": [{"sig": "MEUCIQD5MQqQJYdE50MMJwmPHYx49AANS8KyDb62ir+hLGXRRwIgJ7Ke3CRttTKydn/KixVy1KfO1zc7gDtSeJrb6ofYjA0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19772, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeoBZKCRA9TVsSAnZWagAAGLgP/RXBmbp9dFWX+DefCW+C\nocgTkABq+Rjth6Q7OeOBag/wyTUmlX0sIsa0c8F0ysHxodcFPZFzLeN7SwOj\n7Jt99FL3k+b4IxoIj/SHA8OR05UZaD+6ALiQ2nOfC4BbNDyqhXTQrIL76BXH\niOQ7gyc4NYSmQNflSW3QfNnTjGgic3mGFFyYwdUGpXkEfqPSqcAuLPjr+6Wx\nnYOSQ5dRafxUGzhXFw+7fhIcYZ7j73t8DUaTepdAwCf7/oZkG+eOd01NyciL\nNW2CmuqWnPUUYZBmFetqJWjnGs3n8wDvY9Wpx7+q8Ts++XRBaGzA2tPzbUdq\nC0lMcX+r0m3trhFM3QOYTQ70vz5eCj65g3m9GhBfvWFAkrKJ9v/YVvb9+0R/\nZQVF5oLI1cIURbIlRLh/0aeFwcT/8Ndqwkiw2LnTLquUNbDqQ4MQqj+R7tNC\n4n0mTzAk9O0WBeU7XbgF+ywLL/ra2IVoo6B6jkJ/ihpCsE5NvlOorlD+n1fv\nTJNvZZxoEe+50OSUFbR/lVB+7YF1M2IIriKJ5JWb9m0jhXnQXaJbZ7diFjAb\nCRYbv4LdITAyaZdU9fypk6MSJ/n1+zFFj42mGzlJRzshmK1STQIRs9TPrqLe\nU5YUvJ4llr/s59cBuCwMc4m5WYOJOevSBtBf00iLTuoWeszmaWsOy8Y+jdQ+\nK73m\r\n=VrNN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "02da402275ed262f7d2d6b38d45e79a5fb01b8ce", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.20.1", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.16.0_1587549770370_0.8978484253425916", "host": "s3://npm-registry-packages"}}, "2.17.0": {"name": "node-abi", "version": "2.17.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.17.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "f167c92780497ff01eeaf473fcf8138e0fcc87fa", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.17.0.tgz", "fileCount": 8, "integrity": "sha512-dFRAA0ACk/aBo0TIXQMEWMLUTyWYYT8OBYIzLmEUrQTElGRjxDCvyBZIsDL0QA7QCaj9PrawhOmTEdsuLY4uOQ==", "signatures": [{"sig": "MEUCIQDJg0bB8u15nKRMUa5hyj1ngHDnhpjGOhKSIaTY/zVyNQIgcJDnQjmoGAZUWRcNdZMNihERjMzNKhjP4I+5LMs6zCc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19945, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJew7EQCRA9TVsSAnZWagAAaPEP/0RLnOiuVJkjxPLDATyh\nJviBBdkMHjz0EMwenHFfBZnx4miflvUoNmaHbYsJPtrNhoQdwemLluvwF2Of\nSMWtYpetlM40RlA0nJSl0lDml0i0Cuwg+E6vraEdedYmGRXvM7NUTkc3KqAs\nUw2gRZaCUVhQlEtqAzn33wJEqsM5xK8b7OtMr9Hl14Me0d0grScYKqLdrTT1\ncnpRoJ0E67ofA1y1W9NCcgDiBzCeZwFJ0/mYg6w6WuIRDbt0XjNEvV8bbi+d\n0jkuBilxMPb6Xi5Bvo+IYjjo/mTeyDSW/WBKcUiroCPMAdfho8oUUaBq5H8G\nUOWRLZ51X7DfMiQmtgXKwE/sINmBznb51Cle/ZPeXcM3daGYQ81QA/tc+B9c\nHcRpB4fnKhzQTVmXVx74YrY6JehaRZI+R2mfhQCbwCcBTKsIrARVU1Jx5j9p\nw7QwZ4lzc8ds1IaBRzjeVD9tlNMk/ruPqAKmGx4bqLaSYxT6aYFjeMg+Z4ZH\nMykJccmLYRkUC0zt/cSXS0tQxubPfcktCPEaeCWccg8P+l9HePf/EECNPWdb\nARIzehAMiBfEpll4D9PsTaNKQzus+9xwDZ/N4lub/gU8VhBTDQYs4fV3RYvX\na1MuiJChwHWEpOgbdLMgdqM0Y4WzrxF2oQTPzZD6pMit/g2DFxc8pz9k6nX7\n+7eJ\r\n=Di3F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "8934a47f029c0f6fafc3ef1eeea26adb028f5c6b", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.20.1", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.17.0_1589883152457_0.4325929450484842", "host": "s3://npm-registry-packages"}}, "2.18.0": {"name": "node-abi", "version": "2.18.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.18.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "1f5486cfd7d38bd4f5392fa44a4ad4d9a0dffbf4", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.18.0.tgz", "fileCount": 8, "integrity": "sha512-yi05ZoiuNNEbyT/xXfSySZE+yVnQW6fxPZuFbLyS1s6b5Kw3HzV2PHOM4XR+nsjzkHxByK+2Wg+yCQbe35l8dw==", "signatures": [{"sig": "MEYCIQDAzTGwGy+eZ54fKTWrBeYFyuvdx4qnq7EsCDyF/ll3EAIhAPPlgC2sDDsi8dFwATgFqI7HpbnKQVmjIo5zzOFDNCs+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20071, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe0Q5wCRA9TVsSAnZWagAAXr8P/0uMAwB7syU8xxTKWzDw\nEWEIvXAMAcquaacLOuwzDqqzonvGZVTPq1mAgdAOlklLHIYc7u61wXJMuMYQ\nqrtytK49ac92wrkJIWlcOQ5ogbulcM2fBZHEvOfrLP5+PMFaL1NzoApZVsK0\nRo+AoGl0/dOiOGmWxoLQIfEoSPhwR/ThwPUZrK1m7VsyOy9Lm6bhDGYTdXzO\nHMiIoG8kjpkVPOOVM+wOGBmc3tdzWODHkgfvejRRc61rcSane2k61wTEoIHW\nnchbwzle4B0thEHjgV6k6YfNVdSnGABCWu8YL1fjkYz5Lb5SAvTXajA3tHJH\nLCnTu/+TN6TK795g3j7nHVQaDDTU4/6zZZLjfU5RiwBjcFbgDMPLENW5RN40\nbYKflRBgCCNyLxnnLHlfQ2J6r6BCCmEXAOCcdFRKNlfimZ3QGBimWHLrfCFf\nL235SpMtnkfw6B3ol7ipnLksk7/6q8UpG3YNabsFEZrgzl0DB6EQHzWyB902\nGWrT7vDE7vdE+NF8oILBTzeS/ifVl8ZdFXtP8Epvc62Iodf5jyTqHMs70wH8\nyOJm1V0GKaJs01b4tHquJQGrfwZJ45qHTNb47l8GMsqR3W5wjN94cCZ7G0od\nw5bF6Q4K92oOD45K/XJGvHFJQJnjRcYkVMQU+Ipmkr2sUdPW8/u5TJy9bB67\nVpEI\r\n=nIqb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "75cc2ddc7f4d978a8d44a64877f2edb17b555189", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.20.1", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.18.0_1590759024062_0.36283286900991185", "host": "s3://npm-registry-packages"}}, "2.19.0": {"name": "node-abi", "version": "2.19.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.19.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "11614ff22dd64dad3501074bf656e6923539e17a", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.19.0.tgz", "fileCount": 11, "integrity": "sha512-rpKqVe24p9GvMTgtqUXdLR1WQJBGVlkYPU10qHKv9/1i9V/k04MmFLVK2WcHBf1WKKY+ZsdvARPi8F4tfJ4opA==", "signatures": [{"sig": "MEUCIQCzDntljz0J0q7SSaIru9aTgkn9t7obYdF7hdDqYOwaZwIgaNsxrVhHVX7kismZsRnNeLaaoEbRu9CG69ruCWjoorw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26191, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPwAZCRA9TVsSAnZWagAAptkP/jMqnoFJDDdtKeg92lke\nPKgTW4wPUHMokWBVs8CkLRpqkBa28Ol+ayWjPN4SIM8AprWoCpwAWPSFnWcn\n99jO+Wig7c/MXTFWIzkQUa/08J4enagkQDUuu4oheRSAnma5F5v218KwMACr\nioFaU9pl//RAmsnumZIng1Hz09t8vetQ9sM3ZrOt6c0q3xd1IwqklqKGVoVP\nrAvzDGItlRalbyBhha4fbrsUowvbHH9UBFr63d1rZMprCqePK4a+Q6gUxLhq\n/6AcnI5p/0GAsTIBN3xzea67VE9r+NEwfJf2ZtSA6h8acM3T1DMbi3D0HJ+/\nVC4RItOjGd2reUqzv1fXlr3zxH/iONRaO18OD0Zi5y9sDT4rig9g1Mk4e9he\n/shtS1qoXhCbXuVWjKYdjrjs2YvdU4xx0HVVVNxyyAp0TKxzshi8gzrZ+G0t\nTwW5g6Gq3YpvQxW9buPxbArINoURJMKXS/cT2vlRj/bf8DS0IvCa4AzBcHFW\nE7rreV6oihnO+0+MFmDGrDgbMR246XaKS+/uNmLOnfH1G/YlBrE06Lb7J1TW\nDpvKhMb1I2n5lq1fRL8suJS7PBnuC0Bv41O3hgTE6yqBdVZgoqjsIxyUmsc/\nbqk4DzBJ5ugjhfIu529J4Tai2Rvi8kHBeWBN36C1w/YGHKLRRRY14YiMb7BC\ngj/h\r\n=ARHu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "3f6f27a464c522e9d12f48c685c3524ce6a066a9", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.22.0", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^10.6.0", "tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.19.0_1597964313336_0.9523844031298265", "host": "s3://npm-registry-packages"}}, "2.19.1": {"name": "node-abi", "version": "2.19.1", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.19.1", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "6aa32561d0a5e2fdb6810d8c25641b657a8cea85", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.19.1.tgz", "fileCount": 11, "integrity": "sha512-HbtmIuByq44yhAzK7b9j/FelKlHYISKQn0mtvcBrU5QBkhoCMp5bu8Hv5AI34DcKfOAcJBcOEMwLlwO62FFu9A==", "signatures": [{"sig": "MEUCIQDsV1RPWm5lheT+inXv0cs097r0dzBpwraNnh/v6PFcHQIgMl4Kilv6c0tWV1JVbIKFb1CdE2GtZTb8ZGwhWHMUI4I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26426, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfRCZ7CRA9TVsSAnZWagAAsbwP/3D4PakgbWzbe3SvsbcJ\nU2tSX2WLDJO/KZ8g/RmHAHCfICA6nZXk4NOJQ0Ffv2ib1lSFj+CIKZ1tIqtP\neya4EzuMvAtJZYzww2jl48gax49cT9iChF5NVC7M2bE/Hv7A5mIk1/YDkugt\ntxk/xSgsORamqGZgTrKcOKj+cHp0j1WMHVXPJXO+hPZU+wc22R8JToeNCcHI\nPqTKUOtBBYh/TyMhppQw7b/7OCUBp802XcgEMD+/aOvXYo5cIjXXl2JzHPc7\nQ2C9g2kXnucyqPWyIIG6EKHGIvaFMU0zkgQv2m7TeiNvgzuGEoOv2oEPduYW\nwEued/87hy2F9c4q9+8zJD4IcLGimNFUaiOvhehSojlX5f9g3Z/6/6FTTT6H\nRy9aVQd0cF3lMJdYEgTXYDX98m/CLx59ihBLPAF3eMiWPQW2zz49DXdBSxc8\nasAV9lNLploA8lPUsAZc3F1gFWdKEi/F1rD6YaNGoEa4IxAWvVi8RJz6Noc+\n134BaRcqscKMdDhyy8hgpW8pvdvceROvxjedaF/TuERpk/IYBQxbrZ5WseKR\nxgvHYN+t0rR2iym5Cv5rKnZB1SroRGuz7hI1OuncSUM9UZyQ2EQautqG2lae\nwuQVeU7MW1a4Z0MuK+sNXfDKtru44v+N0HJa0d4E3PvuYl4J9ayuTRpKmj3/\nHI0/\r\n=LnU3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "ef65dcc46776a6b9dda7a38bbb501263453f1631", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.22.0", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^10.6.0", "tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.19.1_1598301818690_0.5715340902858908", "host": "s3://npm-registry-packages"}}, "2.19.2": {"name": "node-abi", "version": "2.19.2", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.19.2", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "e691a3b3bbbe27207841a1bb08684ec294e6e338", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.19.2.tgz", "fileCount": 11, "integrity": "sha512-XwSrRvFWd8iGM+XfBjCvtp3aFJON89Cb0YLxej0/aecSSrBEGXX69iJ0Oz+mc0i66M1o/s7ju4K+T1Esfp90pQ==", "signatures": [{"sig": "MEUCIDjei0Lu6q79BApF4nFtZf8M4UqXQZ/vTgqm0sG8XzRoAiEA+WjnrBH+FgBTeFnKFo138JuBpA+le4kz7+c0J1KD9u0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftG7DCRA9TVsSAnZWagAAuSUP/2atigNBpZrhx5M7rOaq\n5Ql7OjCFWBhYun7eUZQZcBmWYh2KUi3AybQ/kjKU8fZEQQbQ9wcf16NkvEan\nCBcx7Kk9PSY6hi02Ud+cYXVk5Juej89o/mh1AGT03HYGbKzZKx5wi+ZahH+S\nEGnyymC8SLFvgB7d4ZtADvfR3ZVmG8SMr1JH4Tx7LAwbWCp7Mz+P4oalp/0U\nw9tbMcbIaMSv4t21BcPzlsozQiP5Zuuf76JsU8Y7ELAbxFN8BAeTqLwLaO06\nx67ciFbj0kd2ApbkU253BR1okf62lShdbhuqCYBYDhG112vKd994yTRFb9jt\nlldQubfYqNPV5Ry9V+Y+YpyiGHRo4YISLkd2fvRbYgOvVMp/nGpWp8RHHE/R\n8xWAXLNGRh9yo4RVDE6BNJs6FEuHtYiCz8Oc+K0Z2lwdxrWpyBUerUtf2i+b\nB1pPlrCCFJgDNbYThybxhqdXUHr47UY9a73nqu4yiZ5ds8vEMDyDo+/2eNI0\n3I6Ghgibeb2ZWBK4BvsjoRn4WH3TLuTup0Mxz25pWkeAzfTu370sEQo3zycP\n2V/ISPRAj5u0QucYeuTnAjo+ZaPsGCD2bVlHVSWuwUQj+fNlVFeuQBeZqPpj\n2L0MB3hOkxAs1GzA+1IQ1ZQUuAEPUGRls+fX9qd9m6kNqlrZ/KC4IMYBRXML\n+5xI\r\n=1v3G\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "7b787b46f5ec41e05c98c16438b1efaba805dccd", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "marshallofsound", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^10.6.0", "tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.19.2_1605660355380_0.5958038946361928", "host": "s3://npm-registry-packages"}}, "2.19.3": {"name": "node-abi", "version": "2.19.3", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.19.3", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "252f5dcab12dad1b5503b2d27eddd4733930282d", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.19.3.tgz", "fileCount": 11, "integrity": "sha512-9xZrlyfvKhWme2EXFKQhZRp1yNWT/uI1luYPr3sFl+H4keYY4xR+1jO7mvTTijIsHf1M+QDe9uWuKeEpLInIlg==", "signatures": [{"sig": "MEYCIQCdgc3U+okIbDovc7wnXtqiQZixcS5T0OSkdTSUsBbODwIhALsRaRsk6nvCy2jOYsLYikio6vtga+4VMQZsiqJyJzH/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27555, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftM8pCRA9TVsSAnZWagAAIrIP/1QWGAqAdnAdXW2veljb\n+yWmvFsnrstbgqKK+l4ik01P4ACY/ThjA/x+G6MzKNqvBbme+Nc1V7b7CyX8\nMvgFYia+ECAtBb4OEcRwR0ZY6J0h9Cs+lvH31ZOuK4SLKf6YK0288bd1YHhG\nl/mtrjtLXwP3VFH4JtCo46imWo/vu1IQ80G2ym0iusbcImatRw8BCapcJ17I\nARR1gv3nnRjYDtd+cGWvi8rgtRpze56oKSV9BvGhFNq8jI3ymMHAvJgdvyBk\ndc4GzZ9z+FPABr2dQ7moM2QpWcWSUz9oUlj8vYMVkVPUX/fDXeY//nmXYh9e\n4jBYvvwWrNDuyuXzzGBRXJ7rqlH1VIoPx1hqYsz6HiVdsSZTKx70qVCG6RPz\ntYuzv/7uxR5Zr8L4EVZ1cAIgp998XJmQRdhq03v08yQrNHS7cGs6G4399YWz\nl9w5/8mh70B/uNmoJ6Iwkq3Hr33ZStbu3JYRLuPubJ1K9B80+V1kXIq081TK\n02QsBw3o4r63/4+mm6ztG1ff5moqsxIREU6j7IKJLyi6sLMLfpJraiDLq/MO\npPWZVkghokeCTg22kapWN+XzfNIUQGSLhtJxWFo1z+NoLoXyAW5SeiLXLcbU\nv5pRQEkIQcERgQJ4HLH9anT++VfoxRwYeoT69g0XJrBKYgC2YUcF4f8GjeVs\nS/V+\r\n=E7ca\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "42932775e72f8886f87b518fadb25d08b5f8c3da", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.23.0", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^10.6.0", "tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.19.3_1605685031890_0.16345005301003845", "host": "s3://npm-registry-packages"}}, "2.20.0": {"name": "node-abi", "version": "2.20.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.20.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "0659ee1a4a04dacabd3ac4429fac6297ed58e92e", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.20.0.tgz", "fileCount": 11, "integrity": "sha512-6ldtfVR5l3RS8D0aT+lj/uM2Vv/PGEkeWzt2tl8DFBsGY/IuVnAIHl+dG6C14NlWClVv7Rn2+ZDvox+35Hx2Kg==", "signatures": [{"sig": "MEYCIQCk2RnEb4yX/jpzF6IFDR3DJImZhWYexZ8MS9+BAkmm4AIhAL2TS1nRo9Kjsjm8bEwVJuAg25HIEmmm0Cwbx5eLIYGY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPr7VCRA9TVsSAnZWagAAOwwQAIt567DJAglZAgnF9HeC\n4Qjeci/MPXffz8d/NtSkPgt+AyyeBxnuJrma7TbPUeSjUvT/S2IWh3BBGdt0\nnyEHI/0fJmIf5l9KjF/Q/NKwmCr3M3D7LkjEUf45w+iynAAOJAhd02TApLWp\n/0STvNsan9fYVdbfUuyBM4Jj+0kMJTCDd+IhNHI6ETlFbmKeAw31gfJgJfqQ\nhdZ7SfbV4LFzlTuEDxviWKMJ3c2rJSleWIWqYzWUPaTQ74fuCSOezGJes5gx\n7voqy/FG0gA55TI83PWBuqnC3r3ihAf/iCKJ0LQ+d+K1NFqK8zVeIHB7pza4\n2QK/M4c1m0tSu+5b3kZf8i7Ci1j4mkE4TMA3OqM4gW0qJxUvwQAGfWDiNwAu\n9f49ANAEMc3fJNvC4bcj8zT/Lnuz4IAPrcu5J8z1GwpYatK6SZns6Tne3Aq7\nieFuqkuTeJikSgA3aCexbIoOCtkx4ks/PFVzCmVIr4eL+k67ZXDDlTSjh01Q\nYqtMN8dGh8ts4d91wwaF01wQo0Bt/Z7xOVBrSjPnFhX73jQotWWHm+mvZej+\nr9LGGO8QLFd1s06TYmWTuX5RDcN0hUDhrMDX5bdPgw49f3Pa8X1unxx0rpup\nnunFghJJ3ouNGzwqLsKv0z7OeqNtdQ393Ugft5PpJmpZoz6dO/4e15t6zZH7\nneWD\r\n=1Wj4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "0b69668dce944af2fe8e94aebf473eb0431a39a6", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.24.0", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^10.6.0", "tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.20.0_1614724820608_0.5926222779058343", "host": "s3://npm-registry-packages"}}, "2.21.0": {"name": "node-abi", "version": "2.21.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.21.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "c2dc9ebad6f4f53d6ea9b531e7b8faad81041d48", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.21.0.tgz", "fileCount": 11, "integrity": "sha512-smhrivuPqEM3H5LmnY3KU6HfYv0u4QklgAxfFyRNujKUzbUcYZ+Jc2EhukB9SRcD2VpqhxM7n/MIcp1Ua1/JMg==", "signatures": [{"sig": "MEQCIEbuGsFUwiVVspyy4+qzvBtba+tfyLvJVKQ+V6vujeMVAiB4bgoCAcPGRGFU+4jpnFdmWi95neLQuRIecfpSi/MDeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27667, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgQuquCRA9TVsSAnZWagAACwUP/jMB9jCnY+Ae/giYSphZ\ngHyOPinnB+I1GuMnP8UTzPt/GRWF6Xi3oyPaHB8ONvUU9fDpNMlZ/23OVjEa\n4MbiM4+NOkI/iPwy9LI1DMIYhK3iRsaOYIFOl9teGOViR9XSsouNrjFD10HW\n2wS9cWXrdLytqi4g0fXDk+eYroQzItbn7TlzB56/OpOPiXDkMEJpz98b6RMC\nfWfiA8pVXKl/1OYqY7kjt0k3yMyDx1Bk8ROAoGojXs2VjQ9qrtq/GcdGpDT5\nX9+XoioxIRo8sEy0i/t6VtibLtYITNqsYYh2iYuoDYUNUtjxlTitjkYMiOi/\ngbni/9LI9m4CRx9TapVFZPXFmXcSJhN9L91ryHaIipEF4bUrD5qz4i/NahXb\nVvG7eQYwnEnyzBVUEJaso45wKj0Rckrjw/D2juQccAPLmU9SDzT9aviLo8BS\npeRqsnnOd5Ipp7CD/sSv0XE1eeFlzhnkevdzBvJH/jg35m18sp1HARv/gD5Y\nBlm8cDvLAP+0Bp56jXxPIbTxtYTQP2/cWK+rPJLj5mcqMFbOTEmMSkQiejqF\npun/DBbdDciCuQDvY3+b0Od3a4lQcF8/mX37NuWBBZ0cyLtShwBh/1F/So4G\n6jS0q2BCWvPJhj6OMRXv7+FF6a8STv1PQSvyRXuQRWh0mo07uhObscIIlBol\nIB8B\r\n=B2Pu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "9a6e988f2ec052b172a440fdb27e3627e17f0fa6", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.24.0", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^10.6.0", "tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.21.0_1614998189830_0.6615809420331158", "host": "s3://npm-registry-packages"}}, "2.22.0": {"name": "node-abi", "version": "2.22.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.22.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "0b5e4fe042d06bbf4775ab21b95d6092fad2edf3", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.22.0.tgz", "fileCount": 11, "integrity": "sha512-G/+QOrEijgTTb+bloKbxAdaCbSAVo3qHn4n+ebYdzRhne6AjyIjQgZwp19+14bwVv5egfwJZcZadNAcPu5VizQ==", "signatures": [{"sig": "MEUCIQCN6C1Zmt6LILLGgnk49fSL9PKTbK9aAscFvUvdbsk8TwIgSR7O30ZlEtDZWE5NMuVdiSGVxQURQdfujsE9OHKPSss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27819, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgfykOCRA9TVsSAnZWagAAZ7gP/ROAwBx05iuEFnberVtu\nPG+XQqQbieT1FmQGtTRh6xheM5EnZCM6cUV8zHCkDzfxU3qA/93ZghKq6Nic\nVfHcHRAlGc4KZ2vfbJbkBF6WpY1tY2UtM+wqeEi1aNKujtRy0b5NKSbn7iDI\novz6wcpvC8GFYKL+ZqXRSEhaqCPt3IJk27r1Y4Z8GSFH30SpeG3/fp8iv9M5\nFP2+Ax9kOo6CQmfDnw6nnXf0Zp3USqncwB5iFs5QWaFinz+HmoT0c9W8lXa/\n6Kjl8Yh9x1sCo8XDanEV9/fOC4QS8P9GzAWj0JDzYnroo5ftx4UJPp0rLY45\nkfr+e7Ey53HdI14I9GY4BTMAG16/+dMuioUYJLYF1HqtmbNQ7JGzJr/vAbAW\n5H5RFKvFDW1IhvvB8ZFVYovii1OJCfi7ra7A98F3JwvWH7a/GjoiILDs2UBa\nPd5q2FxUuFHoalrB8OP1oFQ8cOYAOv20faKssaa2agoj3NbZL5wWLQAxLePn\n1v5fhNuwcHjB6pNlpzPjadLIo5l6YruUg96OoO1OdjKQ7cYHPBg1Vk2zo48p\nwbO6Bq4FVAn267V6FUF27ddG7HMKjTAtZy8sRDc4KDGkWMkJRqstj68/FFYL\n8gR8573Wbb8sx2GpAD/ZH8tuSpdla45xbhyvHkINVw1eywGuSFkNm7WYyYKI\nbTJK\r\n=ZtWH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "e641b1175afa1912a62d4e4d681e3b0395f6d53a", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.24.1", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^10.6.0", "tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.22.0_1618946318109_0.12400412002893813", "host": "s3://npm-registry-packages"}}, "2.23.0": {"name": "node-abi", "version": "2.23.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.23.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "8ab8782cdb54be2f23972f6fc8659a9d2f709490", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.23.0.tgz", "fileCount": 11, "integrity": "sha512-bNOB2ldBwZCmZXhBKXEp4DkStisI1G+yycm7Z4zlZmioqL55+IuNwS35ZzIPf31hITRtHIRw1J/uFr+aXwToqQ==", "signatures": [{"sig": "MEYCIQCilRh7cr1Cegtv2/trY8MUohLv++TPupjFbPMlSa4HcAIhALiMpxxbJPzcp/6mCkwMzfPi5bAv988OYJLKeYXcOuSk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27667, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgfzTrCRA9TVsSAnZWagAAT+kP/0V1l+I3J1ki2I9X4aix\npHMFwl2kky/qKWAzbEzjPjVt+H2KZNghwHsw9271I/5Hcm/RGM9BKI7sJ9hI\ny8LZtAOwcGFNbaIhXO/v9TuWW12He3YyGFWfDsNsm+mHGpcqak9t+TXLKB1l\ngMJlGsP7N5Q3bBRvyGHyeoVJp+EdvszcMUeg83HngZwOzj72j2K5d07Qktwl\nVZiOx/fF9/R0+rrmlqrsHanVq/zgQP1bfDMLNCKrsdKKB0+i/GNCPoGh8R3D\nn8MO/c7IZ9QdTpDrhlqSXOU+H/s91bVX3T6B6YXcgbYip9SCHGacdc3aC4DZ\nDsmdPnh7T2SK0DBjscWVPRsPblhZNlW9cPFRyG+yjJULJHslLlc/oiZEYZRU\nAKvoWdNKg+2QF9hRQiJjETiIhtf41Yx/peF0/5pA09/XZLRvOm9nKSUkDOoo\nRc0MZMOjVCG59RQGvYBqrTPfapFJzGIm/ZvL8ndVM+2kuWI+zHsjg19WJq+F\no6MRK8AZetDZoNMNJ6enX54lb5Thadct0a88phiEsLp4ULxA+WsbUQxor8Rt\ncEpf2vBMFyXON3KTFj9ndC88H/Zb+fXMCfVJnYXIYgA5J1lhVlF3B2yjOLi/\nhkLos/dF+O5p9y3sotii7vUcLppfCZJNIVp0Y6eTQfGEqv8buCXJSTT1Not4\n5dXu\r\n=lGF8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "0a758a3c289bddcc6ebf1cec34c784ade3e7f5b7", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.24.1", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^10.6.0", "tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.23.0_1618949355413_0.9164395106354435", "host": "s3://npm-registry-packages"}}, "2.24.0": {"name": "node-abi", "version": "2.24.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.24.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "6919fb9091565f753f16ac678395f609f50390ba", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.24.0.tgz", "fileCount": 11, "integrity": "sha512-/zAmJ3r+2IcpGhdYk55edkwv0VEpoJ4zvk58mXeoXV+6Ca5dervF902SGvhNdcX+vQwFHNXfkyCQXBG8n/Eu6A==", "signatures": [{"sig": "MEQCIAluY5FHlIVsGI1dfK9E32q3dzBQkshFQMRq0DWIiEc8AiBmMNX0+Byz0ixIAzWCLcQiXppjfqhoOuwg9msoW84nwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27819, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgf0LnCRA9TVsSAnZWagAAXf0P/jMEvjT/6+r6PFaefO62\n71dH/4yGPAOIW3T4PhOuA58FHr2gVzWoDWOM0XoODlxtDagOXBUgKUX6UzOM\nCeIWoKK/VwMqsmN+1Twe7yh2g4dwymXz8EqXW/NoQfHEWY9J4a00lvsDGBco\ngjxM5r9fdbeDGZGPI/tR0ufbHP3kdW9JpaRo3abNI0g226FMByveQfzr5SoK\nXrQMn8SYa3twwv9KDoi0WLDn/M8Ol4jCleKDmxVS0gafn2mrUmUsyPKXWjeI\nZNQTBRPX6MpWPg3Nt9p2f+lidZxlVPRI9CVfS2jjJOywYdjZxHRnsTKu3hy2\nyZCwu0ttJ1qM+/XbP7OutqltRDPoI3CrWVooGs+nci2ChhtWRMg/39Mi31e9\nFmOZYuIuooj5AJST9tXk/4EF5O1ZVlH03TtEMPoy94R2TgfHTa8/Rd41tjb9\ndFoku4LacY+IsR2RdguvBqS2FUh9k3V1Tm9CS6dHHQeHSipSkMVAaGIqMpzZ\ny10OY7PxH81FCYdjuLfHbKPzmHEKrSoBhYH485Ypd+QDtEOJ5J3/DvWvTe7n\nUGQZrVq+6g8Ggw/ilXr8ppKy34MFD/SWyHv/pMsz+lB7KMcmcKMmTvTr0B+u\nphz7R0XhDuRtnYORY+X7s3g7DFWNp5BSf/OgmivfQA5L28oelYfl8pgrJxXe\nIXYs\r\n=UJxm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "5ad787cc48cbbc4a164fb7fa3b05ef721a61e886", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.24.1", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^10.6.0", "tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.24.0_1618952934909_0.27600401594219903", "host": "s3://npm-registry-packages"}}, "2.25.0": {"name": "node-abi", "version": "2.25.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.25.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "d235b9ef3a8251e2091c3fea96da4df9417632ab", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.25.0.tgz", "fileCount": 11, "integrity": "sha512-7HxMZNwK18N/VJ3jKzbl03Px/ZXLEWs5UyQmAbjCnBOj6IDKM5J7pM/t9Au4Rna1Tpw+meMk10lZSJ6KkgrVcA==", "signatures": [{"sig": "MEQCIDaVKMiJhl1R9Hs9D80/9suCQjgsnMzT2LVIBS6jdYdbAiBJYFXgDrUxbEJ5Cee/bmRfpVFciQQkaJeFne+HzBYI3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27667, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgf3CSCRA9TVsSAnZWagAACKUP/iN2swhghe59sptU8bBv\nFgc1wcDkB5y/klZnPq2CJpdtagxXnjQJfYHzNurWQxhxOjttI6jfqHZSl9JE\nOirayWoS01tr9z+03oUZrF/kHxFMx9SvPr8nQn0/Vbc1LLjlfET5z36nMZQG\nhh5XRbt73yIzg8F1/GrDzQLVZl/WudFLt5VB0p2TDlBAMYxjSvF5GnCcEjab\nEwtTR3qmOs7DA6wq3CR6Vh2adUShhEdRCRD8t965hynWnVu0594huH2rOPil\nFui1/Srqv9NehfuUwyrYrZrOChCZJoxWrZ2eQ4tC1UJVDNVEmj5TGMnblaaJ\nrejZ5eHLABkZ+rXcvJG2lMe/gL/H3CTm+dyIDj2HvBc/fK72VpN8hviLe5x7\nuxmV+KPTq9vh+UatpqE4ozfLzgRVXvwK2VY+t6OHPk457KuC40Ty4d+SYgR2\nu4ECK+PgWLKUcbZxCLCEYZ73esL1hgCbhD/RQCrnIgJ95B01fV4gX0Bbd+2p\nng+dZi4qXTxV9lTciIGZdlGakCaIHC+nuqRb8553G2NcigO3vq5MJ+nRQed3\nPwQE8S2iSgXgg9wo3SH6odJognekp4Plywt1Su9NKCwiaWyfjgWjT0oA9B2+\nYzTfx3/nSnIRG4bl2yGKUrT4GT06GKzYNvjpdz6pevcvkGsZpD9+ENhdVgKw\nOp2u\r\n=+Vnx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "4d4faf73c1eb4cd67aef51b5c76fb850163255a1", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.24.1", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^10.6.0", "tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.25.0_1618964625662_0.9858306546331033", "host": "s3://npm-registry-packages"}}, "2.26.0": {"name": "node-abi", "version": "2.26.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.26.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "355d5d4bc603e856f74197adbf3f5117a396ba40", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.26.0.tgz", "fileCount": 11, "integrity": "sha512-ag/Vos/mXXpWLLAYWsAoQdgS+gW7IwvgMLOgqopm/DbzAjazLltzgzpVMsFlgmo9TzG5hGXeaBZx2AI731RIsQ==", "signatures": [{"sig": "MEUCIQCw/E70RxK/ZWiLYLkH/33PwTv9EkTQd173mSWOHo0UbwIgT/LRA1VfTrDfmZr74RIV3PRWyymTGomVk/9Yyv3RYZ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27819, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgf4DVCRA9TVsSAnZWagAAbhIP/3YSXNq7KnbMzZBkoTlK\nW1XeH6o12h9FG9ff5EYXeOoZLZnRp4MTFT7taOwP8W2OwkH81TZ/qHm3qNvd\n60yjAXRC6MJgRkuJuVwqHAEC5oKXdmxJZquxxkxlZTpNHbc80MoeFLG+pLyP\n5OS8/PW3enpCZw7najcK9spA7cTBiGjm+ZmOaMvP5d0AMH9iDd8JfrGEaF93\nuvqzovm4IGsXCKelrxW/oZ8Z7ZRcw7WrQet+clKT1k9rqnfuxF6hloii/A7h\ne0kOurruQ6DgcKsCJi1Hw/1eNltUkd1++UYhHm9HWSy4eTZssMY1or7+9/lN\nc3XAIXdhJwUMn5JIQoGpFnCu3eUWsad+1/6FgmrvTH7accG/xTRdCO6pWBUU\n/erIdgP4MBGrk/f+je0/A9vMjXTxD0/Py9uW5DjQJRzFR7yjTc5oOYIvuY4Q\n+HKvgzDTkkcc/ERsCcaDjY7YMf8gf9Ay4GXg0vKO1qMr3/kxS+Zwdc/54SxV\nJxeKW68UwPOnp0GegIWFbdJ47kOZ63FjiYJHV1VrF7P58n9QXrFZxPWrLdQV\ndY4INmVAqQltUeOdgG/Tm7epcIv/o/bh075+4zzp5euk1lmfqX0gmUTlnoa5\nPawfCoJ5344RmUHrKfPEECPONecnS7Eoxukg4zLOpYSmF+Z5FXe2nD2CNNKJ\neDrU\r\n=GyVd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "8e696075aff39f29a5cccf8235608dd3c070daa1", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.24.1", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^10.6.0", "tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.26.0_1618968789245_0.5077451667673007", "host": "s3://npm-registry-packages"}}, "2.27.0": {"name": "node-abi", "version": "2.27.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.27.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "495e1009dcfc7ccfed88bd3323a003ef312ad254", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.27.0.tgz", "fileCount": 11, "integrity": "sha512-JH2oiq6FY57bacOnTH1/iL/8+g8OUpY7WyXYMCwSVLsE4bU+WvJbTiCHLCwCRlTcT0VgqnWTXdic+hK5hL8cOQ==", "signatures": [{"sig": "MEQCIGgfrD4/BByutOXghit4MUYwpFqcfDE35cbo/JC9aJk3AiAmTi1zmxktXCbH/VyQJK3fgnXd9lE+6rNh4np2dvBY3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrPEFCRA9TVsSAnZWagAABv0P/19WaxhGObF3Foz+tWf9\nx/mf3+Fx/z/4e3JelSS+ioML437Ym+MMya/iea05JL1vPJBu9vplO0gmwMmR\nqOCmQyEyKyfdwz02tu8cvcacZxIwAMvLagVSx0CEg8bxsxrkVvqj3JZx0eCE\nbRdLjHVECDaLxEmrVuHjMk9r7F0QaxqSEkXXNsp/VhXaiC34Hjcx5fGX9smD\n4PLv1ObqVgRhsEkeh/nwqbK4E/+qkla5qQB0ITpaLMvyDmdP5Cd0CGU2Df/H\nzkUiapRn4L2ANN7JZ8C5ofg6mMzcijsw827WnkWhGS2LRgiCJXNgbdOcAnvF\nbDYeyMzonfvKggM2LdAf2aSw+9Wn6rir669n1AmVGbamGF58kzyEzjKIgJHP\nrpARNokIpLz1vLbI9uO4etrOMBDccYSrbqu/0IU+JfWBXT7icYKT9vLcEfkb\nvtr9e5l3v7/qKTk7c5C7ki/dW5I5R0TrAV7iC7X68nOciShIr+m/gW891jlE\nhvNIl8SOVDUlK0gF+KIXyNW+oEcu4ksHJOGo6xzRgMuQFaqzFnZJfx4fjNzr\ntMKYQk32EGbt1qUGdUMAdm5iKathc1uWbp6+reDcatuUR4RK5bJcOq3I+thV\n1lw4iETRxL+uUeyl6pZDxnB13ccZfLMl2Pt1gCmx+tb/SnZDwz1c48Ty4x+3\n0JbS\r\n=1s79\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "173652f9090a3f28d4cf78dc640e36753891c5ae", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.24.1", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^10.6.0", "tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.27.0_1621946629295_0.564754408885642", "host": "s3://npm-registry-packages"}}, "2.28.0": {"name": "node-abi", "version": "2.28.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.28.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "bd7995b5d06efd247c86229f2c2eb0ca56af8ec8", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.28.0.tgz", "fileCount": 11, "integrity": "sha512-81mGpZh1sdNcp2mRNYOuOx9AZ8iTtaWptMETxHVCo/bDXKFZmjhJ67nmwG6OmKHWrjILOaGqJScIvBrzcjwYeg==", "signatures": [{"sig": "MEUCIQDNAzZ2tsH8IJf/fGyHhBa0xnuuoTKWDhYfdk/9yelt2QIgBcfP/AoMEY0TV4i1xldBtpniXcGEY1MO4ARoKr3F66Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27819, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrQtlCRA9TVsSAnZWagAAJW4P/12V+EiaY5Dxy4xMlGVW\n+nKsKrg96pLp5GFItR0n/64VXxXTmjrtVuiqvBwvV1pkDC6Py/4GGOroZvCO\n2I44QbRUbWFaJGrnuqG2N5wsaHoblTAyNoASdzEQikcFcZFKFJXOPvoTfYXq\nxY9/hQebr7PLX5bs97hhASb/wRsU29xzb/r5n9b5MVrc+iWPls6u3wGG1Mzc\naHcSWdoYZm/v5CgTVr9m9cdPchqdXbJnkJiXjnSDmHIycqT96o2/P+pyqr1M\n9DWRuCX5QuEKnPPN5FIpBtZMHCBItmnOu3f7EAufJGQqB8DdW/Cl6nIdkP8k\nUyQ2GaocQy0mGpm77zuP6GQPJkyu4PG6sn0QBK5jIk0hmBDX5dkVO61VFA2w\nceMA3utzJzpmRk9dauzpjFX4g7aXPR0S+Ohsp7Xlu5fOJaMmy+BEaPA4ynKb\nw+DETJxBrxkPSfYMXIT0tckKCy3m/HVSZ+P2XdHPUQRuGn/73as2aQG+PIyD\nEbUM0F9GQt5/bAtOpd58BJEJI8z/k4W53shbry2S9+zcBKmh4hl0BhdrkBYB\nTSVDNxDy2UXcnANHOqiFB7wn/OeXPltYjB8GhVP0dnfwBjNxxDNPYTJ5NNbL\nH3VW/7Vm4r7SqyMrAyOM9rTPqtQ2PxireCKrDExHRS7vLhW1+NrlgvFd5uvg\nmChr\r\n=qC32\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "f42e18178d5bd97ac44b8da00ca9d6e336900fe5", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.24.1", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^10.6.0", "tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.28.0_1621953381131_0.9242306045744608", "host": "s3://npm-registry-packages"}}, "2.29.0": {"name": "node-abi", "version": "2.29.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.29.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "308fa925060e54795789c904edb68e282cbccd51", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.29.0.tgz", "fileCount": 11, "integrity": "sha512-+xu2xkzOkdVS0YAVpiF+xsYM+hM1ylmeGUaG1OC3To6+pUIL4yoCqU6ggWpZeeiq0GFPE2uUOCf36Y4iq3JnOA==", "signatures": [{"sig": "MEYCIQDU87ViDFVE5LSOdGVyQsCa5R0hHSguuJTeZ/J5GbYQoQIhAPcQXRcbc6FWOeee9P6j1vAmfQ0zay+CWK49aQDq5A/V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrTFTCRA9TVsSAnZWagAAMZAP/RFPWFrNvwaby9NAVCmg\nPlHdY0L2QM672ldROjaQA4dWpzXucQXPwSwBT6QVz9jzBXTt7EyEJPpe6IAv\nSPEG6oP6xMsqd6DQA0E2hX78sixVERz2wAH52cWzUGlMOig5T6pKJ8tAjRMT\npsFJY2l+K2naPcA6L1yHXqB7R7fhhK21VDA1OROX8MXjJtQpbxoi3HY13cD+\nMrdyRO1WGbZoY0FseNc7JWHWDg1kvh4DV0Asm4klJy0Q0UNCakhtBLioortC\nwBPz/udAGuizIFTw+BKgABLz+RQU0LtkaAtvtN0zcHJeztEt2D139FwAOR7A\ne+s9m/LND+xVIJl+O+1JnZS1zzcG/ZFqCAg04+FCayLh48MqoU/xXmJ9Erj0\nAfpZwn54C39iIfpiydUqKxGA6Q2vXy1Co2fNsmrEEkUau92VlRMohWjxGLP0\npooCK7iDGxcfSJ67J2KYOo5tVv6yCezRZ3eucc/I/tEsDsEzCCjg5N3mETUi\nvm0NIAG0NB7Gj3kPQO66SSbjG8FdHHZ2eWBlKbWX5TuZgMTqeR26T3jD8O7Y\nqD82y3ZdQHxCt+19VxmHlj4onqgZaZPSNONMt7ehDU3eqAIYOXOo/9ibrikb\n2FWA906464xO96pX5DMCg0refLoqqGTslRdCe1FrIUefmVnRTEiGzez658PG\ni+Iv\r\n=TYZy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "e7638f32fd0a7751b8e8cccd5cf9d664e7f31f32", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "10.24.1", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^10.6.0", "tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.29.0_1621963091046_0.3865901061608259", "host": "s3://npm-registry-packages"}}, "2.30.0": {"name": "node-abi", "version": "2.30.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.30.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "8be53bf3e7945a34eea10e0fc9a5982776cf550b", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.30.0.tgz", "fileCount": 11, "integrity": "sha512-g6bZh3YCKQRdwuO/tSZZYJAw622SjsRfJ2X0Iy4sSOHZ34/sPPdVBn8fev2tj7njzLwuqPw9uMtGsGkO5kIQvg==", "signatures": [{"sig": "MEUCIQC5rEtc/xjk3QK8B72mwK50abFXhEkhkj26JgSe/rAuhwIgPEiJHIuEA/Qy6+PHRtj1u7Ty46/B47KjtIc1BpVvNXw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28092, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrWuqCRA9TVsSAnZWagAAkDwP/iAn/+1S1SW/C9CPV5dR\nlhH5uBRlOqOSC7raWipDpoDW1Pct354tKc89x14UZcdI+0QJ0IzAgWK9kFmN\nIn8eTvNv7HDiStDy38f/VJk8PkHr0wgJvvWbiMCI7iMcZ2lt+jPNUSyh6Vf1\nF39KZWi0bgLgkQGKQ8NJQ7GxxR3FhBmm3AVw7nHPO1LChYFVQUk1BXbgVP4I\nX/xVhcSrhk0UOz2nCBwu6EI/nZEWyhgqV6wMhhmmiDXB4VJG94tftD+GVycM\nyk8UorueF2DQWhMGw7ou3zRDmq8ejYholSwRKfyrKbMG/t4jiorjVmFvp1Q1\ngPP+LxXIOSJSJcKGKYgMR23HBbbfUPq5wmrVZ234QagCA2DLR5gJq7tKMf48\n0ZoJpclaPgnQFW0U1t+SblXPKAHwrDXMHfRMiNt5W3GOIS2E2mNMpzO+lokP\nCGWQNXhIyDEw3O0D0JQVSB6z9SWDfqtkIUOre+Md7jEXnz1USt0bc43bF+U1\n4Nz1UB0wC7O9ESPOjDrc/o0aTA60xhmKnbN5Hi+Vu3zat5/DY0JWvkqJkc4J\nDUNbMS4v/l2HFJmi5KEdEdpzP2P8iOIx7Wg7+LXaavr7UtyoH0KOLEOpQz6C\n16deEU80yqqjSIrny3dBIhfj9uFsJy4R3a6lGLrSL40ZuGJZac8MrybXervk\nmCSw\r\n=NdPm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "a790e7a5362a45388fb74f3156186f57d04fb4a2", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "lgeiger", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^10.6.0", "tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.30.0_1621978025559_0.7573239636977569", "host": "s3://npm-registry-packages"}}, "2.30.1": {"name": "node-abi", "version": "2.30.1", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@2.30.1", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "c437d4b1fe0e285aaf290d45b45d4d7afedac4cf", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-2.30.1.tgz", "fileCount": 11, "integrity": "sha512-/2D0wOQPgaUWzVSVgRMx+trKJRC2UG4SUc4oCJoXx9Uxjtp0Vy3/kt7zcbxHF8+Z/pK3UloLWzBISg72brfy1w==", "signatures": [{"sig": "MEYCIQDKmDE12on+bcumXlxyFru8nTV1gWm7POl8/cg1UcOQlQIhAMlr3Zq5CXDsyXfsO/vx62fVU94CBSxJ+oBDNQ3JVeA7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28796, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhLocPCRA9TVsSAnZWagAAuuYQAJAPSR6OZ+PHiJ5v7yXm\n3gjihYzDqhR6iVEyCOL/2or3iWk8BwP84PJKmoEDMCFSLASA52K0AIHBuVOv\nkD7v0Hq5D5a2DDK/WgK5XBMB2GUlcxR/oMknVePoO3M1H/MqYXozgEXDZnYm\nd1zDvdrr61KaILf4F6h8ZxARn38U3fG9G092cso38fEWZ+7rCJUtdKa5/BCU\n0yEUF93a+TzIFAABMAqmZdAgwZTmYZZHftk6+eWR0wJHz/jH/lIC3o6Lkpwd\niHE0XT5ylBQdIs5wHftd3ApB5chyCJSRnMjjwBDIY26z2xE/yWIE9LR8EIFw\n/UbE+/+ilvJKtTpmxl4WWGXMzKWrql5stS1vyJ5HGPeHO7m+kOMVJBzm5Snz\nNKAp3ZRaROSJlnGqWR+XD7FIdhhpEEdEh00xzyL01OV9TK3uko5+WOB3hsdw\n8CTkORAhT9x+0RCROJipClbCrWMrFZ0IINamJNO29ZAAhXz5g/vmN6gv6idm\n5BjNujCHarsKMYG79RXPtD1uGG9OYgiXbtrKcpb6daba4E55Xd2i93po7Duf\nePe2ioUtyDKS9AA1ZjmemkFYiWNA0T7JDOS48R99fysRQUK4F0JzGPjDKME0\n8WkAKQNBC/H8+tOwC+CIcUFCKbYoTs96FKVhEWHkuvQG04N1hpWdC0vFzLe2\nLtv1\r\n=1Fd4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "2003a08058a0bfdbd7c7782a82cb2b17b59a5ea3", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "travis-deploy-once": "travis-deploy-once", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "marshallofsound", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"semver": "^5.4.1"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^10.6.0", "tape": "^4.6.3", "semantic-release": "^15.8.0", "travis-deploy-once": "^5.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_2.30.1_1630439182976_0.2584612176668457", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "node-abi", "version": "3.0.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.0.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "aaeec41ffa8dd436de7a97345ff6f5c99eeafeec", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.0.0.tgz", "fileCount": 12, "integrity": "sha512-bAfE5Pp+qqHiz4GkpH64HqHUgK2DippKB3QuYbsOp8QoR8c7S646jJMfsOj+WHZO5dPssO3j+54LwG3w3HeYWg==", "signatures": [{"sig": "MEUCIGZqWYKiRkoaibQ69pf9QSzFj6waZVJJXq63pSoB5a0FAiEAgnLoRQLxoxD5tAoPSxMFxkvfKyiGyRofoXXXX9pNF7c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30027, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMBCrCRA9TVsSAnZWagAAugAP/2yo4Ae94Vlsacep3tEh\nIEYYt0ObrNZ0JU+cof8v+MVsQ2gt+HDcmjAY9S58ezg0TsLmK6ysilZNVNez\nUfGlcisB88gVpW0UFegePd1IM83tjrGjcBs91Bn9uqSOaEdAl/GXC2lLhEon\nvYjImcmL1k5wZrSqMq4RDS8DppfpbJL5tkg1uiXXfAerQ2LglY0l2UBDwq4W\nRx2TkSPKxnST87+VmjzsDuCEXpwvT/H6I4XFqZ7WicedvUeY7p7eJghjllCq\nW9cyXDoumgUk+rHOeTlTaDb7T1uoHM69G9e8mR+gd3hmWvLikjErUmP4Aq/2\nAwNbis9P1swsDxdvC4A8YLW0nFt+rgIQII2Hh6ESNrAj17Ch5Sn9X2DLG3Of\nQofEq+6ofy6J3R6VseIXohoHp2R9M1ZSy49CLiAkyObiaPiPTvOL671LQZS3\nnLAO1one+utN5WCtkNNMY2hOnnoEba6gsoO1JiiVLM57f85wHUNyVaGWGCCu\n0xw8a0yFa2FjWZLgVaHjnHhEv6hicdo9LQ6dDJq06SNg8G94qXFpplEEH2VT\n22BRwFzh8H9qNw0FMFnAenka+4wtb3kEmX3Mb6DK2TqzZ10FItmS+/IExZsE\n6L5TSeau9Gxo9ez8huUWPlXTmwzmJ7aEaH1h9ZspRCbt9/SedOkelDkR4NQV\nqIbN\r\n=sNRf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "ebd52cd8d2dbf2c56b6c71106c888ab630a09499", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "14.15.5", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.0.0_1630539947140_0.45620223603116417", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "node-abi", "version": "3.1.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.1.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "9560f38fbe2309e87c61f4f103c9d95e0f961c32", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.1.0.tgz", "fileCount": 12, "integrity": "sha512-kVF+eIDzPPwPcJdoVhWboJvdCe+YF1+rkd7+LeYmA95179T0sVB5kKG8VADM/3sBnGWeAUBR8FzH+whlksJTrQ==", "signatures": [{"sig": "MEQCIEwqqm6VkdRT5aoVoIk5B8EGxPVwk1x1DSS+dJ+1/5AuAiApK4tpqIq9e1jsxJ9MLZmf0qx4b70O31AS6KGlwgdBLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30272}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "4f67ef8f6d9bbf5c1ff3797fe971ed868ba1e6c2", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "14.15.5", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.1.0_1632383160713_0.7906697345104989", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "node-abi", "version": "3.2.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.2.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "c8ec6874f808b4da5fbd56e9506390ce65b152a2", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.2.0.tgz", "fileCount": 12, "integrity": "sha512-/qb92JAb2uiwEQ4aXpVphXfGJU77qdCieXACDaIofcMz+YMPBmnCo8v0OlzJBuXh5QHmMiiI/GKyiCzbjOMn2g==", "signatures": [{"sig": "MEQCIDpf1sNWDfiwnBaAdKrtXSM8UBQ91VpG49NkcK5hZCa2AiAceh5k4oDu9EpeBSAIOHp7RXV1AWy6qq3VUISKlJwLAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30272}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "3bc452ebde63cc802cf8343170fe8e56770bb8c7", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "14.15.5", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.2.0_1632784047497_0.7751903611869055", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "node-abi", "version": "3.3.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.3.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "62cc0d9c7976bd415889aba622ab8f5562082028", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.3.0.tgz", "fileCount": 12, "integrity": "sha512-/+2sCVPXmj07GY/l0ggRr7+trqzX7F9d4QVfSArqIVYmRzc/LkXKr5FlRO6U8uZ/gVVclDDaBxBNITj1z1Z/Zw==", "signatures": [{"sig": "MEYCIQDFr7c76odXD/98FPIjisr2R3ITS8OnrTZbO8zjiENE4wIhAL6OKZRM/Lgw8YPc225uGGh6GJUtuKPaVYznJhZThYBp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30050}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "b39a65cfa08d1db93f69b7b17c1dbb47a8173753", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "14.15.5", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.3.0_1634846020852_0.2638921968572656", "host": "s3://npm-registry-packages"}}, "3.4.0": {"name": "node-abi", "version": "3.4.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.4.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "fc02e56b3d4056c277223c7ceffc99e268d496ee", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.4.0.tgz", "fileCount": 12, "integrity": "sha512-RR+RK7EBVtnIUNVc2NsKZER85UwUlr8Rb3VYDqsPIunpuro4eoUjqtxZ34bUJsjjMBnappslZ/j6ihWQLiPjbg==", "signatures": [{"sig": "MEQCIAI8VlPVDJg6hNRodmkaGCfWlcAnT6wEJPwH+Q+9EMuHAiBT7QmNnl/HpG/xQG/3xWj6vaXYsdGKM1NwCpigYMvZXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30365, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhkwnCCRA9TVsSAnZWagAAomMP/2ManjFhmIFecYpxHIjP\nuYj6EZRwNEf6f8m13B4O5HvzABvbm/9Mk13+43EBktl8XKOCu7nlS18Xf3Nj\nFiM/IPL+nb3nUSv4uzOvQ3FWc7kkyJqO4jgT2tIeMMvZ+SV+KHop+RX3Whiv\ntMG4eGPjBP41K3vQumJ6TRiHxuXFQmyFK4J2iKRgGpgd7ezeiVEtVx9mtRWx\nlumiAAb1/zAmM+3jVI1z8TAt5fwqVP32RdqZYcGkPipBsdXyMKmjyuUGabeX\nJnTuSYpfKGzmnCG4Pc/c+Ra4wZKTWG07OTSyATlcp/0f0nUB7QvNLwOxGGtb\nD8P7F9+oQX0wZnD6dRo6PZG5gXj1ZTzVKymw592hy2vCRKrFZOAPwhE0EYEL\na/zuc1QEXu3PxEI/eGmeVuhl9aLUO9p7FpwP/76SYzmHAcpbRppPJmqh1+nh\ndZGzqMswm2Q4QBo+dGUCQFFI3nUFTYxH7i1+ye8SFhm72H2remf7twlJqjA7\ngPi3wS7iuFuuyg5hseLz9PtnZIIoicP6CzHjnNknjGVTCNqHIMS5yCAQQgaX\nQuve9ZPJIC21dJ/fnzQsfg7nctmlaiRINyl0R5ws66kw6XpnAP4dj9jcVWcH\ndzmifMFsWCUwHs/XSuL93h4pbY/Vze+YbNTEvTKj1uh/3wGlBhPRHQ2dT5fV\nsFu8\r\n=/8X2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "b1bb804e82b3ee3b343942404e4b0b6fdeed0893", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "14.15.5", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.4.0_1637026242605_0.5704542446825327", "host": "s3://npm-registry-packages"}}, "3.4.1": {"name": "node-abi", "version": "3.4.1", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.4.1", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "c75ac8ef77c5655604eecdaad0e0b1961a61bf2c", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.4.1.tgz", "fileCount": 12, "integrity": "sha512-b9aCldl2F0YgwwjN8QCWkuHW8jae371FpIOJ8b0LNDEBoYShBostAcKe8u9+tbVvLu8dCR4sDCfDBNVRu6yNvg==", "signatures": [{"sig": "MEUCIFcDb5FqC+QpvyCLbiD6TIEhVSl1YMnR12YETH3ZS4cJAiEAkw7IafaadgpVPOf2CDIdsH498MMvVgnpD3SS1XKbOcM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30366, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhkxAzCRA9TVsSAnZWagAAXloQAJPqIL4a2zxV8GOcy/GI\nlexnUnWJ1IQGKeicksv+a0DSWxIDOHTsbKSZ1u/8Uq6c2PCGYeWU9kF9OB0J\nNlWxxfno3VMVWOlDGGit10V0IRAeRs+h4ZeLLGmiiwYFEOucSh53q+Q29gr2\nFdgl0ig9DahsIfTnmykXaI6FS9OVD0/I3aYOkxngyE8IGyUvHSeq5Ezud5Yx\nNzf70UT7KEFtkH11B9mkmq5N8T2M2ZPLWC1hUAW7+SGlBeId3TLbF86RKZUx\nQ5BpjxwZHwX0YkdW/2/5+rRBpzyRxVujmCnUMKL9ZkI9Nt/I6BubaCVBLdnP\nqfxt6x29vfDJie1mWF68e/D4NJEHKs1NCE4C2H5x9UbGyFiNwHvNd3iBoNh8\n04oSGQRV7AWWLUDtjHxdNISJGb3pU6DbJdn0OBEnq5E46EZFYgUrnknpx/zB\nH18U6AGBHpY6dRRGOJIQxnfx+ZcH9zDTidnR41DTzgEfc2Yw6ilZLVmd6GZG\nwDfTNFUCyaZF2qT68dax2NsSFAv2ee8F0DAX77Kh5CSYKcRXuYoexTYrLl9E\naZ/K6uL/v/pHAUVjj+oh74Cl7/9EFm/NwCAbbUYhAjY/c0Z6G1MG2S83Umwo\nxXYXe8QTtJKnisqoHWOoAhHs4V2rzvsbiKLqroLJSwV/oYLwV1QCHiy0wKsl\n6Rkk\r\n=WuHP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "a58b0d281dd8eb7ca284a4940f7d067cf23107d7", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "14.15.5", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.4.1_1637027891701_0.22472300403409573", "host": "s3://npm-registry-packages"}}, "3.5.0": {"name": "node-abi", "version": "3.5.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.5.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "26e8b7b251c3260a5ac5ba5aef3b4345a0229248", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.5.0.tgz", "fileCount": 12, "integrity": "sha512-LtHvNIBgOy5mO8mPEUtkCW/YCRWYEKshIvqhe1GHHyXEHEB5mgICyYnAcl4qan3uFeRROErKGzatFHPf6kDxWw==", "signatures": [{"sig": "MEYCIQDU/mca5y8Xd3JkVr27DBsFmCYtGiq/j1ec70Kf1zzgCwIhAJotKI4hbkz6pBbNw1mJtnKxVb4sjE5FbuVbXHMjDIQj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30365, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhkxVJCRA9TVsSAnZWagAAoFsP/1HdoSJvyfaw3i90+2Ik\nwapy0Nn2OeGOTjORo264otlEZsS8db8f0ahqtc/aY2p5V0Vjn7CReYZyyBMX\nSbZJ/KBa5neWKr5XxBzaShw/7C4ZLu92mw1N5VQIa/14fhVAoqeX8J/2YqsN\nqoJNJX7HFEYfYxooQcV3ts0nlcr2OjkO9mFfcd/rTp7mGcAoCTX3c19/BrYd\nZfOXQFNWqxArdlIpUkp8gZDK5txUR0x5j1cOnWOzcqD+UKtyjIwK2QjwykAu\naPVV+Yaq6IWvsoH2lbehie5SSd2vHvjvAwOe5BpJtMh/U8JXAqF21h/ClZJy\nAtIy/fsQ4twTZUTIUBqc8qGTifUpHFi/rjEwUoFljWvo9OwAzTHoMLSxYb/j\nYJHQrEub0ZEyLjKtKMm2YEm0bliP43Z8RzCgmU5wRtawKooajh6zZv5UQGBi\nY3DGFh22EhHDwWQ5ACEEmDu2vP2bT6NdgbC0zosxI1EdPKP0t9H3gMezZN5t\nWgRL/wECF9vpigqaqS1lkx0onXGBpkxLUPbbIprhqHDTi0EubNMl6fgX6+2e\nNFiGJfgACYdz8cD9x5uIKwy4BMC0t9yU8aLk759IjRAPuQaSDOaT4R0xFzPO\nLa/hIw6YFe2L7dajhDLMVuqnTJsprjrty9Zk56kz2KJSfCUSswvjAo3iU2fP\nhlrO\r\n=j/z6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "ec77e1069e59ed598ce11ea47690695999c9d57b", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "14.15.5", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.5.0_1637029193787_0.26867093989850144", "host": "s3://npm-registry-packages"}}, "3.7.0": {"name": "node-abi", "version": "3.7.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.7.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "ed980f6dbb6db9ff3b31aeb27d43cd9b096f6e9e", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.7.0.tgz", "fileCount": 12, "integrity": "sha512-3J+U4CvxVNEk9+lGdJkmYbN8cIN0HMTDT9R0ezX7pmp7aD6BaKsfAHwVn3IvVg6pYIRUuQ+gHW1eawrvywnSQQ==", "signatures": [{"sig": "MEUCIQCW+fcCZg7tRALUzyVYXTNBVAcODSVRfGS/IQrC19DYPgIgVo3JFzHD9JZasUGvii+DfdYFit/9NHCJGIVAVnlMoHc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30489, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+Jq+CRA9TVsSAnZWagAA+8YP/15z4waKrqniiIqVmfuC\nV3hzBxe5nrTMUHsEF5jlfnfwrHZ/l9kPJY3DojgUhfTlYfg0Osl1U6dScNfo\n1L+Vd9+lwoPHCg9X/zXur8a7gCJA1ALA9CQUzsH7vhvT9QLrW3Eb6LB5rYdj\n7qZaq+XTfB2cplCZkIbF4yutE6sacWDbZOmDdn7bk2PL78moUELITlPbLWkk\nYcGWZRtgqXGfa94BxaC1BNivQzTKd9wXI/6AwJgj8jqM6F9lWvtQwG9PUXHk\nKGJTn8zaiTHk69J/iGAqs84mrGbLoApfTzn0vs2AaXqqrXn+2tublqi5Emwb\nps8v75ueX0OJCsbc9cKG6WMBUQobAWno1TKknn8dUP8EdAsorW7His+w4DMI\nrI5ugNIlEK7T4+LHUg4jci4VI6xPQpkuUuSlNEDMsMJksRnJo13EIsQJTGPr\npUWcJiCAMfFKtLGhAPb/vPnF498G1Ngepw5cGP/e+E/fJhTKT6obvjbv7hsQ\nBjqG82MeVIQG6kCwi+KccF6AV/ed2GZOCdOF370y7QOXRRRCX3x9jULZ5spt\nWeFGHWxMnZpbYFXCNe0qBqtyuCtvB0/iie9+r3HCqKDy/hjblhWjAYoA7xJJ\nCpI5fUjShd2TLHDE6G1baLAS5me1Si/o0g5jlRbK7TQf8mDmEyZv4xlg4xQM\nGF1m\r\n=Vu12\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "8046e81a00600c377a0151f1c603f5d97ea183d5", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "14.15.5", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.7.0_1643682493797_0.7114507438888897", "host": "s3://npm-registry-packages"}}, "3.8.0": {"name": "node-abi", "version": "3.8.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.8.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "679957dc8e7aa47b0a02589dbfde4f77b29ccb32", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.8.0.tgz", "fileCount": 12, "integrity": "sha512-tzua9qWWi7iW4I42vUPKM+SfaF0vQSLAm4yO5J83mSwB7GeoWrDKC/K+8YCnYNwqP5duwazbw2X9l4m8SC2cUw==", "signatures": [{"sig": "MEUCIQDYPtbqIIj4AJvwVjuz3TbPdk7O5tWNvrpjwmBN4yVIvgIgBFPgfSFPOgyWYx5L6JR9xVoDIai4ppwP+exObFpjo0E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30612, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+uxICRA9TVsSAnZWagAAETMP/1iKXWqp6hH0sxmmRkCE\nCB9E08O2uDNKZbNm0gMHtPG517cRjusFWddcD5eVJxOM8oJRCgBhLe/ywugZ\nIInf7mmpN3sSuePZ8xx9HFVYBTzWj1LWh8sG8dvIMuEOz4hYxnTPVideSJpE\n8LYmon3WHCQNCpz/xqN0jgOL646fG2DOvHEJJxytYZ47cF69EDXp5fn3zkbL\n0STNg3qm6jWk8tDZTyWxiIBo0z1drPMz+Os+MHI+kq6gqTY7M/Kt5QdMabFo\ngxuNTt9J8gbEsMBeHQHVdg6apzIxcoXt9aMZMUqLI9kPBcOSONK6mYLgf/q6\nOt/NP24YIUOchUbLEYyn7F3Tn3I4425wN9y/xqxuL/X8PdkMuS9A4ctXavLC\npdEMxEbMiUy46FwV89IpodExb21n9CV7dhY+Aid0S/n6aBt1hm7xg2/3U9DG\nMPY9UeNVx95MNVcX/Hc+u9lG4zuMfsFpbrEF2bIgN0MEMaOXf1jGc3Cz78nL\nf9zKRZw2aj7j8Az6anDHAHSHUeqYjIso7dqW81S7UVLnnz4bc9T1dRzdaECo\nCGEF4YIP5yPtNIU7Iwfx/iHdqnn23SHTWtlM9s4EkrvpgXgUWOPmtqtbLwYz\n9aWQPpfe9mzUl1hkDajuP2G+k+16STUDhh/IThm7QrlWDloTYkF64tHwnLOW\nqo73\r\n=POvQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "b19d97b80fea78bf9d6284cb489a434a698b1686", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "14.15.5", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.8.0_1643834440664_0.5679151569164578", "host": "s3://npm-registry-packages"}}, "3.15.0": {"name": "node-abi", "version": "3.15.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.15.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "cd9ac8c58328129b49998cc6fa16aa5506152716", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.15.0.tgz", "fileCount": 12, "integrity": "sha512-Ic6z/j6I9RLm4ov7npo1I48UQr2BEyFCqh6p7S1dhEx9jPO0GPGq/e2Rb7x7DroQrmiVMz/Bw1vJm9sPAl2nxA==", "signatures": [{"sig": "MEYCIQCP3CDA+HJyk3FJQ3wL9AwbMb+w+U60JRY2uzQgU6SWjQIhAM3H+LHdI3zXUxPD2FU8OQhBtuTh+bwVdpIS4YA20gz6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30890, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXvzmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo6ng/+PM54PDWurjEtf9+HFR+7YmWflrfzGukZi6Qc94JAugW4IHX9\r\n8o1euNdSIpsaEfnzIanov46Wq4DJNjtmaH4czh9c3IDZ4R8XXx3r2Z7qPuFl\r\nm7fe3gYfuY711ruAWH1sGjBCBOzNqFsfthY0/gbZqohOfafXmDyRp+8STCpU\r\na6vN1WH+fbf9uTsNlvbBXZrV4Nf0b09UfP+SlctFFLMYxnaQexU3G5nA5zcX\r\nudf8MEnJP7CxuB03fLh1BNuD8p9augFY4R+9Ws5O+EHKzfIMyZ+uiGgEQLtu\r\ndrsBILVsuDUFyEKUEdubcZEyIOiSp2ligN4cmPUKax/GFqgl+5C58t7d2h7G\r\nXWUvblHcfWeQj0bYSBfAOKR9GzmWXxkV+00a8TXjihHV/+m6i3GKEh3/Lpb5\r\npO7p/Tv9CERuCNN4pgHt7Xp1c9EJPCYkBx0fLlBrsAG3yFxo+YSG9az437o1\r\n9PoPu/THRSACzLS6FIXkaaOCBg+sqh9Jg4DkU4RaiX28JgtywnkIRYYgqZJe\r\npxc9NdVa/3QrerlFzeKOrwcfXwrknxrmb2r0Vk8+6oQKl77hrrfXAVtxmpDT\r\nSDlkVRgH3dcW5OYP+aBY2MQRb496mClAMTw98KtNoSGrvzDDb4lzmFfs6RLz\r\nAJgn66GCbPhKCPWeM8N39cs4zoI5oPW0Xz0=\r\n=AVoR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "e2de0714dd05e95bb233a8b0cfa65aa925e94b88", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "14.15.5", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.15.0_1650392294619_0.12252304958920113", "host": "s3://npm-registry-packages"}}, "3.21.0": {"name": "node-abi", "version": "3.21.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.21.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "cd3c6bfca94fb08b3ae6f2bb1e09da628c97540c", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.21.0.tgz", "fileCount": 12, "integrity": "sha512-0ChvtQmmNYzXju0fjG0Vfg72q2D8FxUhluvV9uqivtXsKblSekJE2juxfg+9HoSgqPMqCmVEC/GHHtGzi4xYTg==", "signatures": [{"sig": "MEYCIQCyodyHXpOfQV9Dsa/tnejV4ntLtDr6nOEuoMOTeg1PvwIhAKMWtsvfLTvHpQ9GNiRk/A0x0KIkNipHbhNN0E4AFT2X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijSx2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpeQw//bK8Ifo69oRC9Yn1jgiSmSgSySk1e1Zewdj80ftIdgN7lqHMB\r\nUbfpbdXKumBRTg1Qd3WgDiPEkhiltHambR6GY5atZ+HtowCbtz82YInFXAge\r\nx93ZHkmUsg5Jp5PVWfxuSX4VWVyeNvbAhJQW51sXt8tiGJ7s6xE3ighmjG+W\r\ns6h1XvtQKyELz/K2TxvSO38rolThTKMYyAdJHrLEaqRwC5rreHA3PnkORImZ\r\ns91gtC8EK9znOuT03J6lflsrJUX7UM8aDe+qQW+MtX9Bcww9wFDtYn7m3b3C\r\nUCHV1WLwUm4XGv2mKxXWHkGjSmaCwN5HVZNg5EmzfQjhChDx5KXyjCciqWtl\r\n8BQwdHEVVDBFAqq4DCSF2ONynBI0/2x35NyMqPmcte+qQiMVf7qUDrhKxbhd\r\nG3oix0alL1nL4QAE5QNt81ZmWP1Cgy1M9w8QHbjNrQDooPd3FNTWRJrJ5Udb\r\n9waERI6uxWlUjaB5PHqKq3T5FobtDiYtZrHmfoQ9lafhbVkLJMUwzmUYWyOV\r\noVnT9RmngXdGJCHejar8IQzHp4scCQHbSM5iCpVzlXZM/LVz+pNnl5J/MYKZ\r\nGbZeBpsbtbihOjcEaDB575yl5Ve4iiJM+8NuX4Cl6HG8SW6QWaMWssHppEou\r\n3GtAqFujJHms/Sjqtxh+5quy+3VpcWmoMro=\r\n=8Sr+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "a45368e43e104dfc6ec3773f57760db2161123c8", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "14.15.5", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.21.0_1653419126007_0.024469078838450287", "host": "s3://npm-registry-packages"}}, "3.22.0": {"name": "node-abi", "version": "3.22.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.22.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "00b8250e86a0816576258227edbce7bbe0039362", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.22.0.tgz", "fileCount": 12, "integrity": "sha512-u4uAs/4Zzmp/jjsD9cyFYDXeISfUWaAVWshPmDZOFOv4Xl4SbzTXm53I04C2uRueYJ+0t5PEtLH/owbn2Npf/w==", "signatures": [{"sig": "MEQCIEPRlhrVo9rOhVl6JBZVoNjch8BqDuzyT6qzsehXLHN5AiB8EHjPUdVz/jBsVduupdxOCmj2kbHa4ANMoUYKsakTMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31888, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijoy2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWXQ//W6yOOlS87zn/3m0BhWgk/q79uS26iuEGFdl6KRtIEiCIWcbR\r\nYVrvsoUPGXeWnMPojkAf518dg9XiIUgkG5Zo4RmHew2ejS0z1Rwv60Dwqbvi\r\n4p43eI89+3gOuPhl5sKXOrmTm/AzHh7zQdruVPSp1Ea3qRsgJMur0Bic3oGI\r\n5GhN/ihtcYPwW8rzB/4xLuVuACsL41W3BtS8XeDKAR1U5tgO4wlnXurfjuo6\r\nmuwGLN5DFgzkgjZW2VJomXg3dQE1PC3qyPtlTrkqjkTizdIYwMrRGdThybGy\r\nX+sEK+0HTOEUAwm2fsxPbyeM4O/vbk+B0Hbw+5RVcFYvCm3iNksSI/QEige5\r\nvwLe+EBUnOo/amvSCXwnZYpG5vHytcK7LpVKkueOreNqNWoSfJNmDbN2cGCs\r\nrr28gg+HqxTJM10nHeSgDymCbR0WB1hZjcSwfp229GU6HuSc0dJu2omeYJ7n\r\nCoqobgwz9S9Acq+HQUUgGjA4N2e8sT30wNhNywdv3zOWD6VCGgUT6eOihBsY\r\nR0/zgJAH5zW2B6u3FyYR3csWaRiL5C5zYELjyUsYNkMzGZFhSiJN0902oA+8\r\ndseLEBS3GP3q6nfFr2JTiKIElqs0VJGdZQX7p51BKWxD6rYZ/nIXfhkRk+Nt\r\nVjD+4FzXk2Zi8CS9bJTk7ySU9VVJcWbodS4=\r\n=2t5D\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "a34d4c130887046cfe2070c40aaf2f54d774c5b7", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "14.15.5", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.22.0_1653509302021_0.889212523425728", "host": "s3://npm-registry-packages"}}, "3.23.0": {"name": "node-abi", "version": "3.23.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.23.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "8e4945c35f125f6c4f8b919b14771c7b1f76a064", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.23.0.tgz", "fileCount": 12, "integrity": "sha512-XWte/uvq7hmgY27WesfxLUAPejKUlkEbikhBFaIhxe+XkHa57rXBwYqGjsIyfVXaU8kC0Wp2p/qQroauDKs1XA==", "signatures": [{"sig": "MEUCIGzx5mX3tDx0rjctrNi6Js8Z2zTtjsGof7msJZfMaOf3AiEA3zCwSPN+2g2V0pO2vhG0N9K3vNp3qyq5HU4+Gw2xDp8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31889, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6FzbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqW3A//bltmI7L2eq71jfoo7K7WdyCeiO7jvdI2VbwX7zMhGmBtBMok\r\nq4JMwisJcXiXvpGe20m6AJh29UgltMNXtabeQK7U8eFbPSpIn0rhLNJzOzw9\r\nbnwfKgoWWiUB+tzWcV3oFTj3WroWWdxzpfVKXpXzWtT/xlz/ApZvk01CicX6\r\nHk/zw/5K+KBma9a+q/Ll9oOVX7RLylgpcfygn9iMl9Tv0H3ItE9CNbIEYedv\r\nZqG6fiTUJ7Dn6xvq87cfp1mXJRCyuwFebx6iGhVV3Gh7SSsg1/1pM6WJPlZB\r\nGZbZSY/T78Y94BEt5z3qWxwleViSRJQfYiu/uAaga8IxPFFkdE9mPrxaQ1O4\r\ntl1rsr+JYHbUdqK8vGLA2bvCHn6tf8Ohc4qdiitJg5LFVlUtSeinSZX/0UxX\r\nno/5agtcmklfnW5bqiJF0TWgOYbmWB8iYUt6iiA8rK6Rx0pKz713fqxU2UU/\r\nGy81/w67YBkmIDiD5k8nOCFHtB0GEgcxz/u/cZQzYNcYaBxOVI4bKx+L7M+1\r\nEjILlhw+4IOr7dbunJU6XXdq6zhpCaiFFO3kStcWfNwHMoK6m7FhEk/gyKAP\r\nYFlNzp2LmQMuP4hqYxrrzl6gyYUbB6mZgtwOFYsjxhpAbNS4cYz43Ht2KY7I\r\nD+zpm9i9d3R1G/WPO8rUiIf/klk98cEyXEI=\r\n=7rRD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "2536e98fddd055d3013111e1998c16647a805ba1", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "14.15.5", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.23.0_1659395291629_0.30776422446633056", "host": "s3://npm-registry-packages"}}, "3.24.0": {"name": "node-abi", "version": "3.24.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.24.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "b9d03393a49f2c7e147d0c99f180e680c27c1599", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.24.0.tgz", "fileCount": 12, "integrity": "sha512-YPG3Co0luSu6GwOBsmIdGW6Wx0NyNDLg/hriIyDllVsNwnI6UeqaWShxC3lbH4LtEQUgoLP3XR1ndXiDAWvmRw==", "signatures": [{"sig": "MEYCIQDHQoInHhZsCnrDJdEVrbrGZQgflKBfF4Thod0EoKhDKAIhALxo9A3vtU6nAgii97TM6dHJy1wFsGeacN/ChunCrfU9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6tWKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmofeBAAn6Eonq/3DkMBdlFC/HH5ehzlhUZiRu0leR5OlKUm8zhOJ+Gz\r\ntTR/vkWHqfk8FORqfbyq7XADdKnlzf6cPO5B1FWLGpv8Xu2ssDCPmReGfU3y\r\nxisNBusIv0Qun0TZ404pHCvVRT+PLfr1kNGmCsG0I+B7SYH7B2mW1nrlHvyl\r\nyjOiGIyvKfp0WMZdbM8RFZWFOTB+Kx+bC6IjobPkbVNB9uTSg2hOEPiZQd5m\r\nQvpiLz43E9m2JSA5+qOhOc1rJ08DxhxDhGqZYc3Yb5H2/5DRUchCrq6RGqm+\r\nuNwIudDmU+mYKUtwrjNDtaNanSZOXGiHVuf986+J8uXUQ84zCK959Kc6EaDb\r\n2mJNp25EEIl76PaHDjfADZ/eRv6Y6krmyvmddYImJxCTJVTz62p8escqzpNv\r\nVGoTAYDTY7zhxv5lEoEskn9Uph/6Aw8zxcNUNXPq8ZURGqhk3d7I7EToamZT\r\nTmJ0MzN26pJot24PrnDIV9pRe8wH2FtvCRuAg+jRifFcmDSVZGUq1Z2oIN3U\r\nfeiE6bqV8NeykSctzZZxkKIlZrFO85OrK5S/Etxlu3m5+b/AdI3j02+yzvJm\r\nXZBa7PFK4+LV/iHDm2MnMNdbTzXIyWb+YG9NKUJ4mCYZPpIrvrJgK05Jr+sv\r\nKQYe/aC1nmks3x9/bh+lvG6rXxc8lPGb0lk=\r\n=fagX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "d32a5008c7d040fe25abee5f77b509d5845b8729", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "14.15.5", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.24.0_1659557258660_0.047298079392762205", "host": "s3://npm-registry-packages"}}, "3.25.0": {"name": "node-abi", "version": "3.25.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.25.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "ca57dd23ae67679ce152b6c45cae2c57ed04faff", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.25.0.tgz", "fileCount": 12, "integrity": "sha512-p+0xx5ruIQ+8X57CRIMxbTZRT7tU0Tjn2C/aAK68AEMrbGsCo6IjnDdPNhEyyjWCT4bRtzomXchYd3sSgk3BJQ==", "signatures": [{"sig": "MEQCIDR+eVM2PSUC9RWCBHR4wxTcR40rpbv+tOERrLlklm9YAiBJgvu0NsnwW1rwHWOfBgvS/2sdw8PBKE7lwmqlaxhhGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32013, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMiMzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXIw//f64/beSmPcy9ts9xZTBLQu9wCxCLTrFVWR0d320wxSXzO1ex\r\n/r38ITOPx6uB8LNvV9rplZ7wwNbd/ImhyYfXFhtByBnZYZkGoZdg8FTXH0pj\r\nmxLJUGOz3Nuw/HmhRWIZC9Ifiky9zJ5hj3lOz5hE3zIaJE7CVZHr3/wOrYi0\r\n8hjo9Mu4QF4pGkWZ1v/+kMH9S49qdz7relf1KzmYBWsQRZqoYDQczVO/Cr2j\r\nv0l8yLh8F6DjYP6apgW5YevkokTLs/CZvf/vo1mAGrCYPHXAiJ3vBeXl7Qak\r\nDJ4ByQcF0XXRpSLZdqtVPzkqrFNZBbHhreoNM0zcv1/LmUEjnl60BnRk3XI9\r\nbUiC95CFYfgQTR7LKf+jdrKTbVU0pPRaInU8IIhhe2lSSnyIB3YY815tTSA6\r\nj+gBCM701d9TUvpWzghz6r+gTLyEGNyAA0j8/dmvoFUnc12rpx/vrHN4Y510\r\nnlUdFaT8ZRZnDHxNF3Y5zZlFxGAJ0h40ONmexAStHM6Z1WJaBxODNe5fwoiu\r\ny56lhELmFpVI12wJ/9hHcQfjDjptrwIoOYd5E+tZcxzqEYw0mM0R0Ge+woQZ\r\namJrnYkjISS9bx7pmyvtVTT1CLZRtXHincNkFDoKwiTstNbUVEJXPuezr2m7\r\n9yjQLC7vrv3wXkA7avDJol/AQZ+3GXaxXqk=\r\n=cjEq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "9a10bbe5fc55b7d07263d005f14f4eef6370be5c", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "14.15.5", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.25.0_1664230194984_0.3519965811452368", "host": "s3://npm-registry-packages"}}, "3.26.0": {"name": "node-abi", "version": "3.26.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.26.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "fa5dfb912b821f29f058cb04b87f397ce107dc4b", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.26.0.tgz", "fileCount": 12, "integrity": "sha512-jRVtMFTChbi2i/jqo/i2iP9634KMe+7K1v35mIdj3Mn59i5q27ZYhn+sW6npISM/PQg7HrP2kwtRBMmh5Uvzdg==", "signatures": [{"sig": "MEUCIGGzPoLacFUN1y/7xeveEurREmtoili82lYgvat8tdspAiEAgzHPra4gPnb6iRDRAjY3ouFe3Ld5iohYDOsouLZhZnc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32136, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPcEGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpO7Q/9HNW9c7OJB1f+Q2sG8sLVPZOi1BKoqpt13ejZMIeneZYYrRh6\r\n9zK9kl7KW9NYMUniMgmNLd+RTM/muHJkRlT/YC7WlbVZqeLNN+nIgRfMPdM4\r\nD6RSiaMnILJ2EDrgYq8XsNKJe6bAosbQ2HKNEhgHbiDquVDRoKcxTDfL6+4z\r\nBA1NjDp+9hH4xiKmM1uwcyvLDBCip+s3jC7+mLrow3UiAf690VOSQPIvuP77\r\nH6+ufs1BP8h9MUIlnUOqFLXy5AeWslVVsV0tTSDaaY9WQ3wCZVcvR7q8HhQ5\r\nSp6B+Zx7prTcgdlV+/44TuiGjTH93TRskiehJscneXhe2V2fL4XWVgl6mUVJ\r\nXabgfn3b/drA5LrNM3dNVRcJAviTqe3A+cTdZ/jPGmsYY5ZybOcCDsIuXwFp\r\nz5EGVCiVKfnLPTaUT66Yx2FEBPKOsNsdzi81CeWzJuRnwlMUj6K5xdnFk0U0\r\nFevkevXlD2PwfUWg/I3xi6KUrVaMcJBvsdXKPOedjje1xwaRK4P3xJ9sBifd\r\n5SX3V8qR1/WdC8+b4RmHRDDnNsKq0LnhUo82Rg4YvPl4/875S4QHqL43XK6/\r\n5odrWYxBxHBiD68wQJFKAfC5dkmmgl3I535R/36LkEm9uJ1TZB37TTjxgu2x\r\nx7NZixMCgpj9V9W4D3yZ/lBu4I2xc42rF4c=\r\n=GI3+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "ba3913a9778009067d3b50eb7a4b2548c6a129dc", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "14.15.5", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.26.0_1664991493927_0.6445451372424076", "host": "s3://npm-registry-packages"}}, "3.27.0": {"name": "node-abi", "version": "3.27.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.27.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "f2fd97e236cd3f1577a14c45c8c2efc7974a947c", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.27.0.tgz", "fileCount": 12, "integrity": "sha512-TRPbfoT+caUU93hegmzajk1SS23O4LHyQHLv/ZpJudc6uL/rtTXFYusQf8ovElBVfhvZeFW/iVr6OJQImllNfQ==", "signatures": [{"sig": "MEYCIQCYvYhpSHe4GiyPfbBe5jnhN86U4Z9Ype8kSX6VUQhSwQIhAImjwiFQgWEIMj/uhT17P1ioMDbHcoLD1ZNj9ebroyrm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjT2lTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVGRAAhMGdwQfGrhHuuclfqoNSU+k/plPh6b8yrtO4YneLYZR4U3oe\r\nKkAHG81DPzNSihTpY+XqQUptJe4qmnOLwZkyynPT10wLTB7UGhCSxJlHJi3X\r\nktczmnS1j00Xa6nqIkzIUq99qBk0pkISS9hDuSimzffXQwYJ4Xf0MR1aTDPb\r\nhrlsvIpLMe6tMNcMvmp6W3bx+BzO6tNcVZEsNR5Ubpunm26ySeD6h5jWlK+N\r\nQVu+MTqoPxMpKxSip10gAcyiA7o1Lc7DEyB2hzId4NotcfSbBalNR7lP16VW\r\nQ2psfP6upd74vBcgNZDXUoHX1gE9kzQqVO2artvupkmNcmmCymUuFYHKBAyD\r\na3CKZjm1QBEJLvavZx7seY2TUeFdGNMQY+Ou7gvx4k7lSy7LTXzhukjjCngu\r\nrjRO6jMUPSzVO60D+ytgHrw/xZms3o/xNgPwMy7ca6zFJoKz14IF2CVzcDwU\r\nYKYzvwcfBi7iZq6XGD1JmkbEBalIkYvbNp7RSXQyg7bHJ6GRzmpkAAW1X6p7\r\nqTs/xfID/jZDIGXk91wipA0lqS+JBpXHyEAmTq+KuDxMo6zCWsMsdJc3NP9a\r\nnr/YByvFmmpzjg2tVpnpnawuUw6fTIU+hjm2eKHwyZb5JeZC4oEsSHc9fdJc\r\nPeMAWgznLUMarpLVVNQJ+iL5ses9RrGkNXw=\r\n=QYyq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "8764edcd983bc43c7d71093fa3f67eb69080dbdf", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "14.15.5", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.27.0_1666148691518_0.9956398162645663", "host": "s3://npm-registry-packages"}}, "3.28.0": {"name": "node-abi", "version": "3.28.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.28.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "b0df8b317e1c4f2f323756c5fc8ffccc5bca4718", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.28.0.tgz", "fileCount": 12, "integrity": "sha512-fRlDb4I0eLcQeUvGq7IY3xHrSb0c9ummdvDSYWfT9+LKP+3jCKw/tKoqaM7r1BAoiAC6GtwyjaGnOz6B3OtF+A==", "signatures": [{"sig": "MEUCIQCodTF+OEz8+sCQ4DisV1syS0IqBA187u22Ft2d016CvAIgK030k8jyorMHyndI4K3NNyWOh8nSg8lrkgARXu7sYqs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32136, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjT4FdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoeaw/+PJohzad0r7EudO4M28ojfgaGGAbeAOou1VIIcnFVigNciNy2\r\nkxRXB9EEfijkAmStIk5F+6upo5GOmvLp6g6b2BwMoVxM9/+fw8rgVG/gHF7e\r\nRATsTUZ/gpqYHbe4zzgRtndVjENZUh3gElGRDcbsdwDZ+TycyjVVf9RgWINX\r\nCx84aCqDSdGSp3HlQRO6bRkeXugilqz4YcwiC6h8Z21swgS1mIlmmJ5cNQed\r\nZuZzbg6ekSoVbljcoVjNjHByFbIBCCOEHdxfSgWi98qygOEwmYkXihZXthiR\r\nlsRtOjh7TOysA7P2gWzgA/KoETLBHJ6IzTYuVB23zGuPCgd6/qhshxAxmQgZ\r\numuY3fLwiHVBYQq6eOv9MYWqKKGKccudq3TZyU6PR7sQb2x1AxfqniJKFRse\r\n1vCMLLtdWW8H99G6SehWxG0l15qFpEQzLuoqF2cAFV+H3iQYV4T4l0OFainn\r\nmzCc9crPoqfz+XkKzkivrERGhJeXfFD2sNQIh27Z96RPVpSnnygSHlCFRFcc\r\n9B+zJCdS6WLYjITKUYPFHZqLDVIEiGQIGcO9mruoR8uXG8TJrYj7BZURi9d9\r\nrHNMHH8fFKkz2aF6DgdCGowdowcjfzWkRQt/vI2fva3BrEsnwiwZZEiQe/kD\r\n3WOO/D1VhSSwYelK4U7o8eS53aEh1EHt/GQ=\r\n=VBhc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "cdc8a9341d6791d72745c7aa6143226233598560", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "14.15.5", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.28.0_1666154845143_0.5713576813794761", "host": "s3://npm-registry-packages"}}, "3.30.0": {"name": "node-abi", "version": "3.30.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.30.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "d84687ad5d24ca81cdfa912a36f2c5c19b137359", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.30.0.tgz", "fileCount": 12, "integrity": "sha512-qWO5l3SCqbwQavymOmtTVuCWZE23++S+rxyoHjXqUmPyzRcaoI4lA2gO55/drddGnedAyjA7sk76SfQ5lfUMnw==", "signatures": [{"sig": "MEUCICG0zHquh0RTlSaaVAXKnF3n6BfmC6/d5chIdvWHuStbAiEAmy4cU5T4PoRYHMfw7FJYrnuCBdess/QQcnRCFZZfAn0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhs3sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpeCA//Sc5qInMACHXQD1vNfoHEbffV6g/Z31hztVJG3Rk7WM2lu6/R\r\nXsMZA2bRPI8foUWBc8/LEgWYj/MUSpR4gp8w6QGQd/xD/t9R3hTxP88B84Us\r\npt3YyRGHDJIasst6iryAK6/gjkuyDPlDecTr47KwXYJ40nO+mUtLzODLHuZr\r\nLml+XovC7Lt2VAYtzmHJVI2klolW5TzP3Bew3+rAyX2oWnNRXL+gTRx5nYqW\r\nSsJ2fjsnexa1V653QAQbNKkbwX8SzcEdnkba3g7MS44VXo6i/6MFC1GCe/GO\r\n5eXsNRWH+zB7q4K498NsIZM34Xca7z8Y2TdMIBaaadg/2vmKhxtHi/NXXc6P\r\nCEMlTI9jiEz+T19CaoOPPAm5eDOg4BpL3Q3YkXkfDcZWJ9cMiCjh1WKNnfV7\r\nPMpPF1qxMYJoNY3R3wkFlSPe9neF+D70RXvUaKrGzMyKdInoAwCbuLOKAdp+\r\noBLyDcp83ttm4TVqI/dND9fpJUYk/xBqLWqlpqWNBU2dgiGbeo1e7gonfE7H\r\n/j5KFfkpciTC9mdKePDTWPPKCe+hHnSbRseUg77Wp7BLh1JtoIDC6+rXCfkv\r\nOwnuRUpW+YkZNvwr99MXeUPTeosS2Pb3YpUuxCvvjajQOfsY41Tg8uAX7qGa\r\n+tIHSYFCLdS6DxLLtn7ykHZmP+n/iYFgheQ=\r\n=dn5k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "b4061c452ccc6af3f117383772a3665827613884", "scripts": {"test": "tape test/index.js", "semantic-release": "semantic-release", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "14.15.5", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.30.0_1669778924413_0.3492696980626311", "host": "s3://npm-registry-packages"}}, "3.31.0": {"name": "node-abi", "version": "3.31.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.31.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/lgeiger/node-abi#readme", "bugs": {"url": "https://github.com/lgeiger/node-abi/issues"}, "dist": {"shasum": "dfb2ea3d01188eb80859f69bb4a4354090c1b355", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.31.0.tgz", "fileCount": 12, "integrity": "sha512-eSKV6s+APenqVh8ubJyiu/YhZgxQpGP66ntzUb3lY1xB9ukSRaGnx0AIxI+IM+1+IVYC1oWobgG5L3Lt9ARykQ==", "signatures": [{"sig": "MEUCIQDFjVhkifN7qUZFPKX5hYYU6K9oK0zEnrL4CIb7LLgwqAIgMtpa39wqKx1JHnwBIAishFmnASgvRXw7gdFvnq1Nrxw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31976, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvH5fACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZfg//dcvoHD/MZBBMUKbZDyckHPNHVo+zgKqjdv4DMZ1gB6WAGeEC\r\n1vmtAfNssR84iKbg2E/anUazeWxqC/3ffO2BTJeZ1JfKr0XkyGmaMIfPxJIX\r\nNzH7dUKiAf6MM0qakUfcsV+TgMovN3fKnyuh2fGpEyf/tnZXosvOl9wgdUTT\r\nrRvg6ncpUGv3Ck+DEiXFL7P1FnI4EXcIG9MQXVEt0pJW3WkQW/j7Oy96VCTz\r\nt/jZyFjF9tO75l6K5v3LnfHLUXUW835fU4OMWjtkXfp0WZArWtHeAvvKzXj8\r\nJNjOc+55tg0+PcVpUs5dBQZ+asxqALo2DLYoz+DLSifxyEwuTO9BFAAqDWbB\r\nYnntBjE1g+ViifE6lbv8o6TZZw3sUpTBW2FVctA8kzsndRjk8UXEvN6pv3DH\r\nFOfVshsEfBGPY3zx6ow16/o5Rvr2LetwyXWFbAvf4T7Z0aw+By1aX9BW41cj\r\nGeeyPGjSXYZ0y5EF/G+1376X3thJZxJ9rQxm1fAxrAHeRjZB87YwvZJZJZQQ\r\nJY3RyDXRdtvWNo3AV7+m/XovMWyVP/Bhmtv5qJOtqyd3HzYvMvUrJpfvieMd\r\nhk81moEMJqOdZbRgY8exk964WGThpLo0grXzHpbCh/ECEk1bumB8ms1IBSYh\r\nz59CXz1DHOqyuJmGm6Wal3ICG48F5OzU1EY=\r\n=qzia\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "c10d14043746efc404e377853c762fd71fa38392", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/lgeiger/node-abi.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "18.12.1", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.31.0_1673297503348_0.1646695327590808", "host": "s3://npm-registry-packages"}}, "3.32.0": {"name": "node-abi", "version": "3.32.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.32.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "a22e038efab01d9a35c6b19808b430616ed80539", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.32.0.tgz", "fileCount": 13, "integrity": "sha512-HkwdiLzE/LeuOMIQq/dJq70oNyRc88+wt5CH/RXYseE00LkA/c4PkS6Ti1vE4OHYUiKjkwuxjWq9pItgrz8UJw==", "signatures": [{"sig": "MEQCIHJa6r0F1e3IAaPI0Z+2MGYUsZoZYfaIInw6mOI6x8dlAiAAx+lXVx4rJaOJ1JuW1rKFPpZ4jJvc2br6Z2BBJExRCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4Z3bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+AA//bPcjKv8cTcVoX2CtEP70m0fPmWI52elJXYrCP/Zv/jFvb5RD\r\ndBrfapTFEoOe//AP3iifaHi0KwYkKv1KxgHh+gqmJ1uAiOncuXuVAGa5TcpA\r\nWGQud234LG/46SQRAiqEBSmQlC+QIpqIFerNl5vZHkZiPpHxrfRYVTuoHIY7\r\nC3QUrPRCQz3GkS9FnR6tIqbI5m5MNQ/4GNufphLnBj9IjA+3oF8YIOrXikMO\r\nhtiqykE9yaFupBnZShRK4XMi7HvXXK9mYWzDzQm5g/EAYmwLINeperaAayXw\r\nF4K86exqLfXfsmgBJqhGQLSrc6OgAIwQ3QaRMAvNGL1kYlGDs+npJEVzmlin\r\nIkQScueiEFeo2GuUN5I8Rypvyr11Oa2C30tz6JEMjezEwxrutYMH0mHgJYOG\r\ne2Yw+KrpjqhGF4av8NGdD+m+W6dNvalL/8v5OqHxZaNVXOfWzQrhsOC6BUNl\r\nnP6mJ15L2JM1E+aFzua0YOh6G3D2DAoAZs3LzNWwZps+eqG9XPJWEPzPZKFc\r\nxx5rfboa/G8yunwsUQpuegIluXthc5h73++CnKl+SNMG91OP+NfkpAUKzelm\r\nRszabCXbTvZtiLMaw+t8N8w3IrylgGQjRcYzYn2jywXxx1dwRgYrWgSU1pJt\r\nc8KlMmEukDmPlJTeMHKSrmvXI10ty+mA0gI=\r\n=lrZ/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "f2809d9de8cc84faae013d7f1a24de8b1852ca49", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "18.13.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.32.0_1675730394909_0.7074601095521391", "host": "s3://npm-registry-packages"}}, "3.33.0": {"name": "node-abi", "version": "3.33.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.33.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "8b23a0cec84e1c5f5411836de6a9b84bccf26e7f", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.33.0.tgz", "fileCount": 13, "integrity": "sha512-7GGVawqyHF4pfd0YFybhv/eM9JwTtPqx0mAanQ146O3FlSh3pA24zf9IRQTOsfTSqXTNzPSP5iagAJ94jjuVog==", "signatures": [{"sig": "MEUCIQDIdieeMxHedImuJsgaYPGKdXRwolzG6eLbaunau7vL9AIgE6xjkC/vrmMt74sVR8I8wKBYwOyo3Z/25p8Aw5ap/HY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5dEfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqrAw//VgWXx7YwVyKbxx7mju/TIhFba8OAzSMYwbquvTvtWiz0WOKU\r\n6ALZRCLwxCZ2f7Bm7voC0bBbb1Gx0vDftM+5Sz35zd0chiLxE0uLsOq65oFy\r\nB2/sC4GHoXPQ/gI18fELIhMKtNMmJJqFJKWiZ+Xj3jj8/ihTBPC4ll1Pes3q\r\nGIpXyqbLD5RlrcAcaxwmQxqpxngqy8TXeCmoLRpDmQUvjMVQHaHU5roMN52k\r\nCk6Oa9vxqQwQEFMJGSuKE29LQKSsVbZRG01zVXtvFMewGyykYb/S88QSiNR6\r\nk6x0teZqAXKLdmeEktw0lE5WMYsTOhubhNIV3IDPJfRyh2n06lHRddkkH59I\r\nD7ycw9XpwVNwnZHFZk6ZGkxTAuVU8BmffnYry6ohfIe1jXRZMikWdITDH8bu\r\nsifMRo79WS2IUGzh75rfHWEgc2tja8jKRoNlVNx5TAErymOjLT+OHPg1Nscr\r\n4yxBeU39FTV7a0H0v63PfilfJl5JTsXFckKxQqM1wTaGZY4pd2XDorHbDE+v\r\nL0THOQJQ//bTpyQPBuoFgzIG9fAGb1/3cFxC38d4E1szmAK+bjxviIvrLhcF\r\nlYwxOTIjxFuLxDmcx29pzOT626aCo4DaCG4XgEKM8WZ/ziTO1X+NhzT+MNgZ\r\nK/1eUqyJYnfdhHg1c36uQfFjJ0z10b2p9ec=\r\n=HuWN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "f313b1836c3eeab96c2db00607e36af28954e1c8", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.33.0_1676005663582_0.31935652990665964", "host": "s3://npm-registry-packages"}}, "3.34.0": {"name": "node-abi", "version": "3.34.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.34.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "3bac9f69fda94ece1752515ed996ed409da4fa26", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.34.0.tgz", "fileCount": 13, "integrity": "sha512-O5sNsdgxptez/bSXk2CfpTcVu4yTiFW1YcMHIVn2uAY8MksXWQeReMx63krFrj/QSyjRJ5/jIBkWvJ3/ZimdcA==", "signatures": [{"sig": "MEYCIQCcxqqGKpA9K0nu62nZLwKyP8unJCbO0A3AB0S5ityWsAIhALj0ulbwes+ueS1xF3QBHtnoGDEkoCQjDtChPlgc46xw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkK5SWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpV0w/+JZlApE9hlyM4LebPXafvp84QGOTgKUTB9CiKTBumHatU+itW\r\n4pnIWniw6P+EaFa0V1KjsTT28UYIFsZEudu67QYWj9kvlymbhHotqW0cgYy7\r\nsvMwdQQiRdLz2wLNseUC6eFPBcgYGZxdhe6j4ZWb2TLSLh1QG1wEcS2BVnY3\r\nTGp4uW3Nltfpp3q39u/UuqETY7HIg6IORzm/QrBzUYaf13D80s6xovgEJgS2\r\nT82ei4jBN9ZA44VNi7MP8r4BZxAm1N1K++XBfEzQjzC08MRkKilofa/8sBCn\r\nAX4jmSTqSISuWKsVGXv/06atcMaw4WOUOzuWVow0F7o9hGQYIR+dvAX99iP3\r\n1rhzBnHN0zFepeGz7GccE4tGi6vD0yUthArZMebRp0qaDpNGX2lAtSfmsael\r\nFUm7ipCZUeoA2P3SvB2yha8t+7UewRHLwBOuBFxzxaQXddVlFuGktAMBK3VO\r\nIf4J2dpt6iR0KOpmF9EGRHLCqUXSJn1PfWn9MgCWmkhjnhTsQEMowhd4P1PK\r\nvrQ88SGsMbWZ2qnfI/6sHBfDea9K5NuX7uKhaW150PFcENcy/gJH4KRwGcVN\r\nCJ4NTxkN1b1HDIvZ8H+A9jRLTWRKf/I2zxL518maUn+L5GXpU6WTwlt9blgt\r\n9IJft1vRXn4qlT5yZitoIRjxOPSlqIKhX6Y=\r\n=R3Ch\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "1b5a91f84e0528c35672caea9a6c526dead8625d", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "18.15.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.34.0_1680577686414_0.1731643305439372", "host": "s3://npm-registry-packages"}}, "3.35.0": {"name": "node-abi", "version": "3.35.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.35.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "c7f171af995cc5286fa95612c4ca97b3910eaa79", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.35.0.tgz", "fileCount": 13, "integrity": "sha512-jAlSOFR1Bls963NmFwxeQkNTzqjUF0NThm8Le7eRIRGzFUVJuMOFZDLv5Y30W/Oaw+KEebEJLAigwO9gQHoEmw==", "signatures": [{"sig": "MEQCIAEA51h7i3r/1yN5DrfU5dcYkBauQLtPMaEDgHEZKimbAiBpPeoKLe36Uep88bhCs+6J2prTzJmcxG8Eu+JDiFyHkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32826, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLwrpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq/nw/9GBwA+6qjNlO7uw7wvI3rV6A03UDg09NvjFNb9efSnucC9GEW\r\n/klxQUJcSt8mOXYweAsUhge6D3TnrsQMx8u6N3ytRwmsaHUJv91/3oTGWCIL\r\n+HXsxODMaz5GZoLuq1crN9PheCt2HKI2daPp7k0H3YjIiPxmQ/zG7t64iNPH\r\nid09/30kKMcr3iifmeO4ODKy1ZDwjtNvye5r47XXh4Uh2NKZ6ZTy9wjfrZGj\r\nSFNZCg0gzTz187piBu22OMsfjEcPFgpZQBHh2MhOLyREDPQmU5PM0yk3qEyw\r\n2zHELhluDNiYHKuCHZaQPdr92eEgaG5zUK1o1xgAaUmOiageh5GuMbRMps2e\r\nz15A1JKr4HGMnuz2IEYjYkKNjIGreH6Or2kG6csweVFpnBwjcZ1SuQ7TzFPq\r\nTjmZQGxQf/5suNT9LaOCXTcJV+jEBLQdD9nDPXdEG0RQ9SXbtaJwquwtdtMG\r\n+wTLI5hnMFzrBuvPtkSNXd8LE0wST2m4s5b12gkpsstk1fp8bdETUuUYPebF\r\nMJAMieFs6aga0f3K78jsYXSHFWdKeiQcFwuVZ25Gab/c1jaEyk4uBt3DB5vd\r\nRgfSLQi5GJl+9IzRgeViIV3tcePa0tVjKDbyzm+qyic2TZjReocMnUWCanrv\r\nl0ZyNkG2LdbllvDEmyQW9OnVVsuwaY6uIGs=\r\n=2Mx9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "d3ab32a9c39b482e45d4159888d887c3982eac91", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "18.15.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.35.0_1680804585600_0.8011328686495189", "host": "s3://npm-registry-packages"}}, "3.36.0": {"name": "node-abi", "version": "3.36.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.36.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "acb40df75317af73e40d43fcd20d7e3496460502", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.36.0.tgz", "fileCount": 13, "integrity": "sha512-Hdv/DrxITY7aqRXxe9U+rdV+UqA4HHxLv89Eum/nuwIgRCf6Hixqx8hkPEy6Y9SzGjViZ4bAyNhfiRELPhI0cw==", "signatures": [{"sig": "MEQCIBlrh2oR0/GCJbITgk+6s0xEjvYSdzDc5fJ/LWrBmzkBAiAe5b9Xmn1mvsg+v2PWd4oTJ3jScYEIB8Mwe3Ua0PRMYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32979, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPs64ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNoQ/9HnLDOvisciGESdHJhEyJdQ0R2f3xnq6+K4Kmt0vskmQCjGOB\r\neZPVAcPxvkjrJOI5hXPiYOjiyR9XEMTqxYUEwcOzTpKVPDOaPXAALVXKuOHk\r\n/RPirJjLBMzF2yvM+dhLLpr9xWUKq9I9VFtSqYqVbAwv/PCm4MUau/SHdxWZ\r\nZCLSbk0lrP1pIjKfuwakBxNDwpi8+naIfCdrhPMRgdtoJ95t+Tlp+Vbw4iHi\r\nGYCkarc9PJITcQvHTI35MN6hqM3M4xHi/24KMb6JNYyICADzWl79Q0w4MuWO\r\nyRUDxmJ5UkaUVpPMsTgnprMrE6kcYyQMf1L+UguZ86Hrep2QCbkWVWWHyKF4\r\nP4rqbPDkHZI3OS+wlkj92Z+jvHSMdcJONFTiO4DFrO2urUo1VUI7Znt/SeI+\r\nYU4z+kMAVJbdHQtTqkmCpquHtCOgwmJeWYq9QZ+HMbNWzel6vnS2cI7VjrQw\r\nN1Z6eOjhlrpX5K4iqeBr6aEirV6VKyvqD2EN8wAkrpXv0ccx7pVtzjH7E6i2\r\nYitanAr0mtuRrUqF1o90fSvoAPcT/aDnXPPWjHys323U2Qfi9M2p2gopPlNW\r\n2N2WazxS0tb7cSLAtrgnzUd0BrLKr+PsWZ5/tjjTQj32UuV4Mgza6oGu2E2m\r\nGdeP3uzvU/zy7G+3MF4kVzEmCR9w0L57s5Q=\r\n=O1Tu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "6c8e458d92ff85bc275c494a34d3bf14ce9faf64", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.36.0_1681837752591_0.9026991120346357", "host": "s3://npm-registry-packages"}}, "3.37.0": {"name": "node-abi", "version": "3.37.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.37.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "188fc3dabd79d5b374108f0cbf528e14b1b14a56", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.37.0.tgz", "fileCount": 13, "integrity": "sha512-Wplt4KQCwXfzbzhoDf4n7nOkc7jrY6pKj/m9gb4uLxSGx0ApDxVu2UCbfoiKRj3cSgvxlvnN8jhTgH/p529Ydg==", "signatures": [{"sig": "MEUCIQC6LRot47ilLPIwSvD0e0rJTqETg2jGUXP0Ws9cpgKahgIgUOWJC1sWAv5J75aa3oKY2qipJD16w1GIfGbFxouEb2s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32826, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPtxwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+NA/8DALeDy6vzyQ0TjO3Iuc7UAofO8yDMOPTUoG34OkYY1xu9VTZ\r\ntKwdAf/pgR1bHVGjiT+YPC1VCcDL1EDTNL7FkzeiRUPdREbaPIl+jptM/q/V\r\nNHvjfO774WWROzHP+A9+1ggRZxuTuzNf7BEdPQmX4+FraSPkVXRTDtDHWa3U\r\nEpdSGoFY8ppWHM3FS1gO4AvUas7d6SkHF5CgfKZCKJjNYryfkGreslftuNP+\r\nrTIrmQ0KoBPVgbGBtF+3XyT/Vn8MfUoQFYDT+oxAIyfZpRDSE/cnuvli0Qcp\r\nAHPkQutJT34cIoJ8nnZE+FAYIcZiBE9n9wopqyu5BrR+sCSS6DIS3UWo9VRe\r\ne8HrYAYbEZJS89k2g2nTqOsTMGs7pNZlvI/D51Xkzv7/yK7fQvKELuypG9k9\r\nMTbXltgAdNJm+xwkQE6br7AC8WMI/9oeK9rOhvHjQaNkR06jbOVDyjc2+4xh\r\nxhpZxD53Lk+VRLcFqsUbWmkZ6l09AHZYzeYWnmb8gcgvhULYAqaFoUXL1S4J\r\nV2JrZZNjzmkyewq13lMPCJUfRnIRlK33n4C+0jxsd0H4OSGPEaMn3vq/MaqM\r\nFrKdUMPrbYIbe9cz6FXMJ6PLGmLkvHAZZPqFsRF3AoPTxYme+7gwF9Z/qkDh\r\nvGeOyMEsMqIkFy6Ap9mlf7kBuTuvDRd7Pkg=\r\n=GwC9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "8bbabb1d6d3b82ca02423d023fc78f6052aa97f7", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.37.0_1681841264396_0.09696996308939343", "host": "s3://npm-registry-packages"}}, "3.40.0": {"name": "node-abi", "version": "3.40.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.40.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "51d8ed44534f70ff1357dfbc3a89717b1ceac1b4", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.40.0.tgz", "fileCount": 13, "integrity": "sha512-zNy02qivjjRosswoYmPi8hIKJRr8MpQyeKT6qlcq/OnOgA3Rhoae+IYOqsM9V5+JnHWmxKnWOT2GxvtqdtOCXA==", "signatures": [{"sig": "MEUCIGCuw7i6WrBcZoK01UXqp6yqp6m1jWKRylQVytBE3my0AiEAhL0peWKZqaFqXoO/VddF/Tapg9eB8XyCECKoHb+ZnwA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32979, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPyKNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoyog/7BjsUIPRzeGfHURlKya3sIT2BKEkz7jyUeMluCawt2pNN+VrF\r\nf20NotewEGAYj7eniLQz2yJz8IWMIukwkPO6sFz6d/nydtQUI0LrL4ntBRPO\r\nPb5ygNLqbjm6ayOBQYD7Isfa/869aera/LGeU3Vd0xdge4J1H8LKdIxsPLYN\r\n3nQ+qno5c6Xwbrszx416c04K0meHwQlTFiD9L1WsXAOlLgyulrWdW1MPVirX\r\n00mso72zpQzIbUOaZlTnumcXHJyfYyYZszrfQHfABibn9ccX6/sZRhrXKmG8\r\nlPniMu02EjdKwj5yQQSQ/bt/+RLDODIyoUqJkD67q3qzW4xqGBAMJRBcuecV\r\nvItOuZah+v9VWYp1zAMK2+42tv8K8K65fOqzo8GLGdvzvls2BpEM6Qlo70bc\r\nU469kbrTku6qLFDJcbGoOXnCJG8jpfJ1XRjZnuC2zrR3J71nCVT1lVpftQ7L\r\nky0KTyYLoAcZPZxIzWkaPHrjJNIbDipu0noS95Ab7SWdEHxNLlUejPzs3YhC\r\n5Q5xyy0BLOnmPqxWhxigK3XtPzvBDQkyyLTOGwTB9tnxLujqW4dYhU32L5Kg\r\neGc1cmBziWix0dZ6JSr0X7wYJexmiEGPefK3f5EoRDXzEZdgn53OXPOWVYc9\r\nj8CsBSDC2bzQvDtZm9Lw/a2Hyp92HF2AJw8=\r\n=kPfV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "680bd972c96ebf4f4dc5229d3a6c6441d7a268c2", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.40.0_1681859213772_0.8500466832972022", "host": "s3://npm-registry-packages"}}, "3.42.0": {"name": "node-abi", "version": "3.42.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.42.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "e86af3dd347ae0bbe40e9a0eef635e5ec6e5bb45", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.42.0.tgz", "fileCount": 14, "integrity": "sha512-eVNy8tM0/RgInLV3R2xF5nGOAfa85lx0D+o4gL1bkR82sPhr6RDAPajtkigExPtUg3SpcHRvLFNxpr3m0Fjz2w==", "signatures": [{"sig": "MEUCIEsKWrLflafhW/xCZIrUrL4aBrrTVFt5QeHTK5ZbI82hAiEAh5bSvXUCkbWglLr53+oiYyAHbb7xk3OWoxzftLIFQb8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33663}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "3310837a5f9592ae86620b0c3b8c73abba730dcb", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.42.0_1685557161157_0.1746262065063673", "host": "s3://npm-registry-packages"}}, "3.43.0": {"name": "node-abi", "version": "3.43.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.43.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "468dc09af3c262ef2fb3a0d2ff34cf8fba61952a", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.43.0.tgz", "fileCount": 14, "integrity": "sha512-QB0MMv+tn9Ur2DtJrc8y09n0n6sw88CyDniWSX2cHW10goQXYPK9ZpFJOktDS4ron501edPX6h9i7Pg+RnH5nQ==", "signatures": [{"sig": "MEQCIA/6cAUeuub4XHa5euXZ+h5k/pEm5tgg8/3AtokJbQUmAiAQ5xXeWZel2Xk208m2vpBZb5OQ1UuHV1yALqObeNUO4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33786}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "6c7fc6097f6fd64a7cfaa880a6c8940d045d4d1e", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.43.0_1685570870790_0.14979567408645922", "host": "s3://npm-registry-packages"}}, "3.44.0": {"name": "node-abi", "version": "3.44.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.44.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "4a3f066636454eb3ceed15a3842199ff97adff3b", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.44.0.tgz", "fileCount": 14, "integrity": "sha512-MYjZTiAETGG28/7fBH1RjuY7vzDwYC5q5U4whCgM4jNEQcC0gAvN339LxXukmL2T2tGpzYTfp+LZ5RN7E5DwEg==", "signatures": [{"sig": "MEQCIBqsdQ1eGQYFtDgt4qt09lackgIcmwf27tJEcSNCCDfvAiAX+FM5LXWZ0Zez8/0Z7WniQjJJp4V8Q9xjMA8Thj0Upg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33786}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "680c90843f81905cea0a99f116c8fa0a3efb17b0", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.44.0_1686183996634_0.6069031885925089", "host": "s3://npm-registry-packages"}}, "3.45.0": {"name": "node-abi", "version": "3.45.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.45.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "f568f163a3bfca5aacfce1fbeee1fa2cc98441f5", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.45.0.tgz", "fileCount": 14, "integrity": "sha512-iwXuFrMAcFVi/ZoZiqq8BzAdsLw9kxDfTC0HMyjXfSL/6CSDAGD5UmR7azrAgWV1zKYq7dUUMj4owusBWKLsiQ==", "signatures": [{"sig": "MEUCIQCui88Fw2SrqEbH9Nhc517o95eudH+MibgO0L4lo2IyNQIgUJdR+Ll84MywianBMLPnArMwUtL1WPEONyiBDO8o5Jk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33786}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "b811739dad1c3103525a390deb678569477dea7b", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.45.0_1686341248545_0.09832215230103714", "host": "s3://npm-registry-packages"}}, "3.46.0": {"name": "node-abi", "version": "3.46.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.46.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "491adaa8d227ca9702f30b27b2b6b248caa1348f", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.46.0.tgz", "fileCount": 5, "integrity": "sha512-LXvP3AqTIrtvH/jllXjkNVbYifpRbt9ThTtymSMSuHmhugQLAWr99QQFTm+ZRht9ziUvdGOgB+esme1C6iE6Lg==", "signatures": [{"sig": "MEQCIDJpr6nZkCWenA2XXBvD1yDbS4+guPAIJMe0ffIlFABJAiAShSRLZCer9IBzyDVCp9kyj3Ie0oepKHhwUimFXZcAJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14875}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "d5b7ee7147a623ad85c2fea5a208dee4ec8b4284", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.46.0_1692053209686_0.6116526620612788", "host": "s3://npm-registry-packages"}}, "3.47.0": {"name": "node-abi", "version": "3.47.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.47.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "6cbfa2916805ae25c2b7156ca640131632eb05e8", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.47.0.tgz", "fileCount": 5, "integrity": "sha512-2s6B2CWZM//kPgwnuI0KrYwNjfdByE25zvAaEpq9IH4zcNsarH8Ihu/UuX6XMPEogDAxkuUFeZn60pXNHAqn3A==", "signatures": [{"sig": "MEQCID5Cv7LG5vqj6IsaaDxf0WyO1En4smhLdT1gLJwEUrEpAiAgiSfS/0rVD64CusDvKbYe64xAo6HzJ1RJZBCZYXrPNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14998}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "f3efb50137fada3613298cf0b6055e9c4fe8ba6a", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.47.0_1692227781206_0.9718356091704021", "host": "s3://npm-registry-packages"}}, "3.48.0": {"name": "node-abi", "version": "3.48.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.48.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "122d132ae1ac097b0d711144560b17922de026ab", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.48.0.tgz", "fileCount": 5, "integrity": "sha512-uWR/uwQyVV2iN5+Wkf1/oQxOR9YjU7gBclJLg2qK7GDvVohcnY6LaBXKV89N79EQFyN4/e43O32yQYE5QdFYTA==", "signatures": [{"sig": "MEYCIQCjJFTx477W7/ULVwXzmk0Y4Sk4QYANB79QcRx6fD7m7gIhAO+6VTBw6p+3RLyvmz5njEcTyXW81Cfux2VP1wWilXtV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14999}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "55a9cc9754a8203a64958f036dd929f185dcc506", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "18.18.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.48.0_1696915189036_0.7124855324524522", "host": "s3://npm-registry-packages"}}, "3.49.0": {"name": "node-abi", "version": "3.49.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.49.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "b0267a615f58939886ddb8324bfbe21bfc0482a9", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.49.0.tgz", "fileCount": 5, "integrity": "sha512-ji8IK8VT2zAQv9BeOqwnpuvJnCivxPCe2HNiPe8P1z1SDhqEFpm7GqctqTWkujb8mLfZ1PWDrjMeiq6l9TN7fA==", "signatures": [{"sig": "MEUCIEob0ZrvrcdxTlqsScdtwmd1FLTHhdkdxw9CDp77BLfoAiEAubFR1uUkltp/J9w8pJHUFZZ2DOjnfYfmB9pd+m+5tC8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15110}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "85418439eef4e47cb53e180447caa69da2f00e97", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "18.18.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.49.0_1696979274760_0.6289939211370159", "host": "s3://npm-registry-packages"}}, "3.50.0": {"name": "node-abi", "version": "3.50.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.50.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "bbee6943c8812d20e241539854d7b8003404d917", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.50.0.tgz", "fileCount": 5, "integrity": "sha512-2Gxu7Eq7vnBIRfYSmqPruEllMM14FjOQFJSoqdGWthVn+tmwEXzmdPpya6cvvwf0uZA3F5N1fMFr9mijZBplFA==", "signatures": [{"sig": "MEYCIQCoX3PBaUu0/F1Cl04MahYMsHvZUeY8yXcv/wXWwh+yDgIhAJndPdrwsLR4m3KIH2BD87ubrX9y5pKPJn0nppzdtCND", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15233}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "bed8a9dd6e92ee2ba6f74aa3c2dd8bd93f76bc37", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "18.18.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.50.0_1697058759406_0.06216798604459117", "host": "s3://npm-registry-packages"}}, "3.51.0": {"name": "node-abi", "version": "3.51.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.51.0", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "marshallofsound", "email": "<EMAIL>"}, {"name": "lgeiger", "email": "<EMAIL>"}, {"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "970bf595ef5a26a271307f8a4befa02823d4e87d", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.51.0.tgz", "fileCount": 5, "integrity": "sha512-SQkEP4hmNWjlniS5zdnfIXTk1x7Ome85RDzHlTbBtzE97Gfwz/Ipw4v/Ryk20DWIy3yCNVLVlGKApCnmvYoJbA==", "signatures": [{"sig": "MEUCIQD6j7ayh9FLmOGTvcD/AN2OZ1L6oJt9YtcdXDPs/25rzgIgfm2+4McT2wGB5V+FeH4T3XLgl8qGaRY9OETvh63duv4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15234}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "d41d6e1d3856f3b2ad6e19d39ab3c0b63b4749c6", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "18.18.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1", "@continuous-auth/semantic-release-npm": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.51.0_1697502186385_0.38139002053953397", "host": "s3://npm-registry-packages"}}, "3.52.0": {"name": "node-abi", "version": "3.52.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.52.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "ffba0a85f54e552547e5849015f40f9514d5ba7c", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.52.0.tgz", "fileCount": 5, "integrity": "sha512-JJ98b02z16ILv7859irtXn4oUaFWADtvkzy2c0IAatNVX2Mc9Yoh8z6hZInn3QwvMEYhHuQloYi+TTQy67SIdQ==", "signatures": [{"sig": "MEQCIFynv7R3novHk13CY52G+y/pKiNjUW7Y15G18xXWpZ/HAiBNenJy2OveRlZhiK6cBjzt8YdxmWSFzmbT8m3cPOvatA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15180}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "091e8e0c16ddb89cc8f2ed17b28997ef98712705", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.52.0_1701731202080_0.14258732997578583", "host": "s3://npm-registry-packages"}}, "3.54.0": {"name": "node-abi", "version": "3.54.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.54.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "f6386f7548817acac6434c6cba02999c9aebcc69", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.54.0.tgz", "fileCount": 5, "integrity": "sha512-p7eGEiQil0YUV3ItH4/tBb781L5impVmmx2E9FRKF7d18XXzp4PGT2tdYMFY6wQqgxD0IwNZOiSJ0/K0fSi/OA==", "signatures": [{"sig": "MEUCIHeVElJhjyedxI6eisjctZYsoknWGg07a/pP3XaV5dxmAiEA4YPU4JdvWjKNkw8AWI95sQtMCtEg0qAn1Cmmre/2J+M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15455}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "126ec6149bdc39f5d3c8426e9d4e55834d934e70", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.10.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.54.0_1704380850682_0.6373568929097728", "host": "s3://npm-registry-packages"}}, "3.55.0": {"name": "node-abi", "version": "3.55.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.55.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "57bc713082a48d4c7c7d3104d3d0cbb1a0e12ed9", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.55.0.tgz", "fileCount": 5, "integrity": "sha512-uPEjtyh2tFEvWYt4Jw7McOD5FPcHkcxm/tHZc5PWaDB3JYq0rGFUbgaAK+CT5pYpQddBfsZVWI08OwoRfdfbcQ==", "signatures": [{"sig": "MEUCIQDGApYPi/UPPXU+GHaD4Qx2BXkZD1v/nCHbvUGhLq2eqgIgYXsF4wlDMqtFEDcCsUsqDcfWza+HZpsXoLVcvwq5WzU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15456}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "6788e4be5e6195c18b7a9824b08f5c809cf58085", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.55.0_1708453977352_0.3169958752056541", "host": "s3://npm-registry-packages"}}, "3.56.0": {"name": "node-abi", "version": "3.56.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.56.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "ca807d5ff735ac6bbbd684ae3ff2debc1c2a40a7", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.56.0.tgz", "fileCount": 5, "integrity": "sha512-fZjdhDOeRcaS+rcpve7XuwHBmktS1nS1gzgghwKUQQ8nTy2FdSDr6ZT8k6YhvlJeHmmQMYiT/IH9hfco5zeW2Q==", "signatures": [{"sig": "MEQCIEA28xRq6kfnFEHEa0oCeTh3RKMSnGmvNz+pgnDYIsgZAiB0stV8MGpPPomXkBFpkfQtbe0semgSUKz/paUluFShzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15579}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "a175be63016d6f8debd642888d69a58b1e1d4d29", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.56.0_1708736027508_0.2351597761057458", "host": "s3://npm-registry-packages"}}, "3.57.0": {"name": "node-abi", "version": "3.57.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.57.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "d772cb899236c0aa46778d0d25256917cf15eb15", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.57.0.tgz", "fileCount": 5, "integrity": "sha512-Dp+A9JWxRaKuHP35H77I4kCKesDy5HUDEmScia2FyncMTOXASMyg251F5PhFoDA5uqBrDDffiLpbqnrZmNXW+g==", "signatures": [{"sig": "MEUCIQCy1hUG7o02HJzfcVCFuA6h1YilosbtrfB7EjPnLKg6UgIgCIzU5kF7TBKFpBjsCoRnguMb2RaBzuf16vfgjNidsjs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15579}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "3acbaba422cf4c29ebbfa32d2495e0228298ed0a", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.12.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.57.0_1711908733180_0.7038136216044115", "host": "s3://npm-registry-packages"}}, "3.58.0": {"name": "node-abi", "version": "3.58.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.58.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "3df24fb742e27c3d2a56375ade5dcc68e9aa9710", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.58.0.tgz", "fileCount": 5, "integrity": "sha512-pXY1jnGf5T7b8UNzWzIqf0EkX4bx/w8N2AvwlGnk2SYYA/kzDVPaH0Dh0UG4EwxBB5eKOIZKPr8VAHSHL1DPGg==", "signatures": [{"sig": "MEQCIBIKkjY50/dJ95knuZnWPXYeUwjfaFkSoBsl2DJUxx23AiBe/zl9QhlrP2UBlUmHB4gIyb+RcQFGv5Z4q/n4EB/5mg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15580}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "baf9339d110f3ca3528f9301121cbab7b7c12073", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.58.0_1713227114037_0.9231500736227778", "host": "s3://npm-registry-packages"}}, "3.59.0": {"name": "node-abi", "version": "3.59.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.59.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "9822e323f8239f603f319103b2d8bcb7a6a01ef6", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.59.0.tgz", "fileCount": 5, "integrity": "sha512-HyyfzvTLCE8b1SX2nWimlra8cibEsypcSu/Az4SXMhWhtuctkwAX7qsEYNjUOIoYtPV884oN3wtYTN+iZKBtvw==", "signatures": [{"sig": "MEUCIQCMJvq0dyRTGPVvQtU7fS2oiw1IzypCwiW3Z+TWvN6g5AIgL0cQf/kQYcs15/YumgA1tdJZwjG9D8ydIMFeegZjrxI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15703}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "72a580dffebeaa2babd7de53132b1f23146fbd38", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.59.0_1713395610900_0.2686053806886932", "host": "s3://npm-registry-packages"}}, "3.60.0": {"name": "node-abi", "version": "3.60.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.60.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "a325b13b3c401c2230202897559fbf0b5f9a90ac", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.60.0.tgz", "fileCount": 5, "integrity": "sha512-zcGgwoXbzw9NczqbGzAWL/ToDYAxv1V8gL1D67ClbdkIfeeDBbY0GelZtC25ayLvVjr2q2cloHeQV1R0QAWqRQ==", "signatures": [{"sig": "MEQCIDfuEnpUyyfMquJw78sg7EngaeQIJhaJp58ab1crlkcQAiBHCIZDa+Qxym4Dl8/8gPBiXgv0TBppCJQLhT2wYJwyKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15703}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "04ca01f7444b6acf646addaa39de372f854ab32c", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.60.0_1713539296983_0.7418891174998528", "host": "s3://npm-registry-packages"}}, "3.61.0": {"name": "node-abi", "version": "3.61.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.61.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "9248f8b8e35dbae2fafeecd6240c5a017ea23f3f", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.61.0.tgz", "fileCount": 5, "integrity": "sha512-dYDO1rxzvMXjEMi37PBeFuYgwh3QZpsw/jt+qOmnRSwiV4z4c+OLoRlTa3V8ID4TrkSQpzCVc9OI2sstFaINfQ==", "signatures": [{"sig": "MEUCIACk5YRsr392kcOsK3iQ39LaMo0M9wZlssp0eZ8F7XhpAiEA1EZQd93kyivUXz82dIVt0L1Ihd53G2t8W1L6u1OBL8Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15704}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "bd11f2da07c5da324e735e2de5bf6716ce97b11c", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.61.0_1713831914337_0.9646485698374831", "host": "s3://npm-registry-packages"}}, "3.62.0": {"name": "node-abi", "version": "3.62.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.62.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "017958ed120f89a3a14a7253da810f5d724e3f36", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.62.0.tgz", "fileCount": 5, "integrity": "sha512-CPMcGa+y33xuL1E0TcNIu4YyaZCxnnvkVaEXrsosR3FxN+fV8xvb7Mzpb7IgKler10qeMkE6+Dp8qJhpzdq35g==", "signatures": [{"sig": "MEUCIQDa+TYgwDFgBlIiSdPL1aHsgd3AhR9DjM3LBxd2Fxw9CQIgBnuQbXQzcGmry3eZ0eVdMkIhDTOBqNmtqit2eFEFqD4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15704}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "6413d5eadb5f8ce43c04924c176b8a2d33ad0949", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.12.2", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.62.0_1713886353626_0.8971222313287919", "host": "s3://npm-registry-packages"}}, "3.63.0": {"name": "node-abi", "version": "3.63.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.63.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "9bfbe68b87357f8b508554608b323e9b1052d045", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.63.0.tgz", "fileCount": 5, "integrity": "sha512-vAszCsOUrUxjGAmdnM/pq7gUgie0IRteCQMX6d4A534fQCR93EJU5qgzBvU6EkFfK27s0T3HEV3BOyJIr7OMYw==", "signatures": [{"sig": "MEYCIQDRqjjQEslB/t9n0vgaUay6uC4kqyrSljB21pIXBoe+oQIhAPGePF8wLhBEP7ml5IpJvrsuUwCAIhh8DfUd7vGLGzm3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15826}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "419d59e76e6370c667469265b9f9d86c353113f0", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.63.0_1717081758557_0.1593342810030518", "host": "s3://npm-registry-packages"}}, "3.64.0": {"name": "node-abi", "version": "3.64.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.64.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "cb24a16eb939ba23d6a5a06a7835ab670e2f3027", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.64.0.tgz", "fileCount": 5, "integrity": "sha512-lxowHVCx3o1zfKJthjWh6WI8Eyi4gdTaK9bUc3oTjYv9j8sp5gSiufkOvoYZ1LgmZKngWUkS5a8G1RSuLWtPgg==", "signatures": [{"sig": "MEYCIQDc19pUIkfV9JWrEmGylbTgvUDXLMK+wUz8g6UZADM5AQIhAPYuSAUDwR1HjfF/Lv1GWKPztaRjBkVyKaba1hY1d/w0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15828}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "556095b1582114196a2617521043b425d4510cb5", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.64.0_1718053756563_0.19151339464963324", "host": "s3://npm-registry-packages"}}, "3.65.0": {"name": "node-abi", "version": "3.65.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.65.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "ca92d559388e1e9cab1680a18c1a18757cdac9d3", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.65.0.tgz", "fileCount": 5, "integrity": "sha512-ThjYBfoDNr08AWx6hGaRbfPwxKV9kVzAzOzlLKbk2CuqXE2xnCh+cbAGnwM3t8Lq4v9rUB7VfondlkBckcJrVA==", "signatures": [{"sig": "MEUCIQDRGQh1c5tq9YY1Vd8DJnz0FdFqTvufO9MYLCJpmqOVtAIgYoeaE4bvSx0ULj+97LKDzm8FjTi/v7aIC+V1WrLrqOw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15951}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "68a8c5269d44b70d86515a805f7cdb2ff6a79ad5", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.65.0_1718243336765_0.46424920070417275", "host": "s3://npm-registry-packages"}}, "3.66.0": {"name": "node-abi", "version": "3.66.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.66.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "bf1101543af24abd9dce47b13047ee49b1358ca0", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.66.0.tgz", "fileCount": 5, "integrity": "sha512-J3ygyG8cTcn5TVzpHYEBE6zHN4uVoj6wKMS946b19VYGhVpGgR5mB3OK2oBTe0l3Bpsj5/h+TAblq0K8Ec9cRQ==", "signatures": [{"sig": "MEUCIQCNs5lp/kmtoGcnYGVZbB8eCQssApAKxduP8h9LoT76uAIgGE3CLAlRL+9jmyh3L5uJo753dVW+wGKsrAQJKGXxiWI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16063}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "d247c3c3238610d61affc48083c3fd51813ba6e8", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.16.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.66.0_1724167759085_0.40761895061200804", "host": "s3://npm-registry-packages"}}, "3.67.0": {"name": "node-abi", "version": "3.67.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.67.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "1d159907f18d18e18809dbbb5df47ed2426a08df", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.67.0.tgz", "fileCount": 5, "integrity": "sha512-bLn/fU/ALVBE9wj+p4Y21ZJWYFjUXLXPi/IewyLZkx3ApxKDNBWCKdReeKOtD8dWpOdDCeMyLh6ZewzcLsG2Nw==", "signatures": [{"sig": "MEUCIQDP5lLRzRH0sErSPWZgtHZBAt/clWwJd2SuOdbNzASZxQIgGb5wQFXAmFMRXm81WlSBp2gqla7i1lh7ZQoij19VduE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16186}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "55fed6805e841f224f12872bb46e0caf1355b375", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.16.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.67.0_1724206855462_0.8080843434100031", "host": "s3://npm-registry-packages"}}, "3.68.0": {"name": "node-abi", "version": "3.68.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.68.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "8f37fb02ecf4f43ebe694090dcb52e0c4cc4ba25", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.68.0.tgz", "fileCount": 5, "integrity": "sha512-7vbj10trelExNjFSBm5kTvZXXa7pZyKWx9RCKIyqe6I9Ev3IzGpQoqBP3a+cOdxY+pWj6VkP28n/2wWysBHD/A==", "signatures": [{"sig": "MEUCIBzjXj1U4ausioOMIwG0g4uaAwxpBt+octH81vH7Xur4AiEA5o+JQzQYxh/jdzBgyJjGWfH5Xohq+oJSkp2KXTnB+/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16190}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "16f0249a42ec27a54b85e164e0250207eecdb7d9", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.68.0_1726758407906_0.9203429333834365", "host": "s3://npm-registry-packages"}}, "3.69.0": {"name": "node-abi", "version": "3.69.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.69.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "00111eb16210ca96233468f4f574e544b82da79f", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.69.0.tgz", "fileCount": 5, "integrity": "sha512-H/k5/+HXto3xXTcqTIl3DAWaelvNVYSoZ2IJVDFJEoYyZYcoRhcRy+1WMMhsKAG+UU7wSCI3DRurJ0DxFMXvyg==", "signatures": [{"sig": "MEUCIQD8FbuxA+wc9EAI6RXFyuRRc34E5Rpu9qpE8YMFwPCHfwIgMGjKS/6DPATyuuudEJC7KMCnmKsmDr3/+6m7fV2BfhE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16191}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "dcd6d045e2f46244cba4aa624e9b92754cab78e9", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.69.0_1728951976015_0.2989766144076067", "host": "s3://npm-registry-packages"}}, "3.70.0": {"name": "node-abi", "version": "3.70.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.70.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "f45cdea658838fe43d43eb78524cbe3aad73f8ac", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.70.0.tgz", "fileCount": 5, "integrity": "sha512-xMTIZdvAyzGyxwOwxXv/8V/f/KAqKWNCeNIIFu2doEtQp9wvMUTam036At/iVtJqum6n5ljbAhUmXAUOhyivSA==", "signatures": [{"sig": "MEQCIFDTUc7iV2aiagMHt9KJHXDqBm2e9mQoQ9Zw7EqW53ekAiBHqyLIf5D8r7qE+PYNAc+eP/oCkQTsd4DIX/UVcm38kA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16192}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "0efb91009265bbea60990010fdbc65fad177196e", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.70.0_1728992617114_0.10227051107509877", "host": "s3://npm-registry-packages"}}, "3.71.0": {"name": "node-abi", "version": "3.71.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.71.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "52d84bbcd8575efb71468fbaa1f9a49b2c242038", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.71.0.tgz", "fileCount": 5, "integrity": "sha512-SZ40vRiy/+wRTf21hxkkEjPJZpARzUMVcJoQse2EF8qkUWbbO2z7vd5oA/H6bVH6SZQ5STGcu0KRDS7biNRfxw==", "signatures": [{"sig": "MEQCIBVBTo2y8EhP24/Oau5clF71ByXmyngBcCONxEWzw8CpAiBewfJ7jVHswMHQCyQY4L4WiWZBW8HP/fmb8m2xXrW3/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16315}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "336ac7e4ad79c6c254701e6c215d5c4645a77df9", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"semver": "^7.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"got": "^11.8.2", "tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.71.0_1729038408192_0.3331157159325526", "host": "s3://npm-registry-packages"}}, "3.72.0": {"name": "node-abi", "version": "3.72.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.72.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "5b86a5aa53873a67c4c502a4a77e5a014f8f19f8", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.72.0.tgz", "fileCount": 5, "integrity": "sha512-a28z9xAQXvDh40lVCknWCP5zYTZt6Av8HZqZ63U5OWxTcP20e3oOIy8yHkYfctQM2adR8ru1GxWCkS0gS+WYKA==", "signatures": [{"sig": "MEUCIAHPJkuk+FpMvB/0RLz1u+iz0vJnt1xY7xYSiLcfa7IoAiEA7APxvqcHZkLrTxTUTTMwGat+exsuCBwRe09+OhWOZ6E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-abi@3.72.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 16367}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "73dbe00538708fbae2bfbcbe251f7dd6838a71b3", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.18.1", "dependencies": {"semver": "^7.3.5"}, "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.72.0_1736870684264_0.5464659646967025", "host": "s3://npm-registry-packages-npm-production"}}, "3.73.0": {"name": "node-abi", "version": "3.73.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.73.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "4459ea77e71969edba8588387eecb05e2c2cff3b", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.73.0.tgz", "fileCount": 5, "integrity": "sha512-z8iYzQGBu35ZkTQ9mtR8RqugJZ9RCLn8fv3d7LsgDBzOijGQP3RdKTX4LA7LXw03ZhU5z0l4xfhIMgSES31+cg==", "signatures": [{"sig": "MEYCIQC4j3N8QNIsjqoi+hsMbPd0J4rLvSWDQVqab9cIeD9iSAIhAKCm4v5otKIfkMgsjm0zeRDRxjUK8UMjCZJAzECWsjJe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-abi@3.73.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 16490}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "117e620962c3320f17b3873173f9856bbdf9c01c", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.18.1", "dependencies": {"semver": "^7.3.5"}, "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.73.0_1736982977558_0.4477936341449791", "host": "s3://npm-registry-packages-npm-production"}}, "3.74.0": {"name": "node-abi", "version": "3.74.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.74.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "5bfb4424264eaeb91432d2adb9da23c63a301ed0", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.74.0.tgz", "fileCount": 5, "integrity": "sha512-c5XK0MjkGBrQPGYG24GBADZud0NCbznxNx0ZkS+ebUTrmV1qTDxPxSL8zEAPURXSbLRWVexxmP4986BziahL5w==", "signatures": [{"sig": "MEUCIAZhBzRY3hYT+7/HP+U04AEyYzus+OrdkMdzsliVdIFvAiEAlSfb1RP2nUtvd9PYOAGO26dt5y1d5WDYHV0BuwM/wYg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-abi@3.74.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 16642}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "152881742dfdfa46277d506a19d07cd5e8249cf6", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.18.2", "dependencies": {"semver": "^7.3.5"}, "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.74.0_1738352993442_0.48885503151174614", "host": "s3://npm-registry-packages-npm-production"}}, "4.0.0": {"name": "node-abi", "version": "4.0.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@4.0.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "a13a5aa4ccbe00b96148ca528f8068c5fd4dfca1", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-4.0.0.tgz", "fileCount": 6, "integrity": "sha512-HPPGQCD3cdAGGqQ9NKQ5RUwBxmOikvut4npzylYU9jt+xR83Gt3PRC44XSzTDqwNFRj7gOdYO9jzOqduIF+XGw==", "signatures": [{"sig": "MEUCIQDDKxDRdUi7NyWX431FHdsaioPe7CyyjNMwFlJT/yVgvQIgZGrUWj6+vvToK4XxBhhR6WGlCkXgtnMchh+f2MFKIsE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-abi@4.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 16624}, "type": "module", "engines": {"node": ">=22.12.0"}, "exports": "./index.js", "gitHead": "0e2679db08fdc456be20015615cd72826e5d556f", "scripts": {"test": "node --test test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"semver": "^7.6.3"}, "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "devDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_4.0.0_1741037537215_0.11116361356942472", "host": "s3://npm-registry-packages-npm-production"}}, "4.1.0": {"name": "node-abi", "version": "4.1.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@4.1.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "2eff4a904e7c0de722ac031d8d17846abd5f4bf8", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-4.1.0.tgz", "fileCount": 6, "integrity": "sha512-I2Gwbj96c5Rx2/0GmvzNVC4N1J5z2YFyFlx6bUNJJC6cC65B+kfCfYxDc+78tPEz5waDd83xe1ho+GuGtXrRjA==", "signatures": [{"sig": "MEYCIQCguRb1Y2MeH+lNwVokbhVl6oNF9n0r8hNF5Aml9TFA6gIhAPRDruBp0PnRyqbijRfFLFp7QE3n308KzZTMXf2vOdLv", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-abi@4.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 16625}, "type": "module", "engines": {"node": ">=22.12.0"}, "exports": "./index.js", "gitHead": "dbdb5297ffa3ec2942457a94b2349c6900f04a68", "scripts": {"test": "node --test test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"semver": "^7.6.3"}, "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "devDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_4.1.0_1741101108699_0.8622272279756924", "host": "s3://npm-registry-packages-npm-production"}}, "4.2.0": {"name": "node-abi", "version": "4.2.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@4.2.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "cf74fb14584a420091723bbb3bb59b6bc4ea639c", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-4.2.0.tgz", "fileCount": 6, "integrity": "sha512-admQxilhDcmFJbUl4LQzGu+QyEijW9rctKRH2P7LNavAvln1bdK9OcujM3yi2KysKI41dxTrDtp6QfGEZeCbkg==", "signatures": [{"sig": "MEQCIFQNvLELPcR0eO7jztm3qfsI/rBp4ulVTxBDBeAhUu5TAiA0/YEDym/qs0IsbTok+HFotIV0BABaXGyAL+YbVZDAcA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-abi@4.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 16748}, "type": "module", "engines": {"node": ">=22.12.0"}, "exports": "./index.js", "gitHead": "fce7362410b545eb9df7a2e032400940acac6d65", "scripts": {"test": "node --test test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"semver": "^7.6.3"}, "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "devDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_4.2.0_1741622519083_0.8430993479726818", "host": "s3://npm-registry-packages-npm-production"}}, "4.3.0": {"name": "node-abi", "version": "4.3.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@4.3.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "b82f203d0b758c4127a69cf4c6df4bc1eeb9133b", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-4.3.0.tgz", "fileCount": 6, "integrity": "sha512-N85L35K9jkdDip0fwVPH7fcSW63UiM9VcCEzgxRbKi6qpHOgyQ2Zv2KbPkGpmI6AIJ2IdFjlqgb/X5ZVjeIdvg==", "signatures": [{"sig": "MEUCIQDnBttOfAQoh0n1m5dnFxzHPkrclsGilVJ1HMlPGgpSQQIgXdUff/vjDOcSOXX0H7eo8ke0lqlImYxrYNGthUCt/vo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-abi@4.3.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 16749}, "type": "module", "engines": {"node": ">=22.12.0"}, "exports": "./index.js", "gitHead": "ce6f7ebcc8023816ccec0c5173141e060b6f731a", "scripts": {"test": "node --test test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"semver": "^7.6.3"}, "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "devDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_4.3.0_1745281485830_0.3135555600134665", "host": "s3://npm-registry-packages-npm-production"}}, "4.4.0": {"name": "node-abi", "version": "4.4.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@4.4.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "f17a2880a556337030a02b7f92e308946cdbbfc9", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-4.4.0.tgz", "fileCount": 6, "integrity": "sha512-+sBEWs/HZ3ZDBtPSPKfYndkTF9ebr1BJm/z2TBDJj/upiOx9J6BeGXRtFyOXz1r6vUqzsCRM5pUr+K83i64agg==", "signatures": [{"sig": "MEUCIQCCL9QA4HDC0Gg7/ehZ9vTZESlTmzvTb4meaiD8lbYQfQIgHbR6BfHyhChw2toKvylN1SpnzLXLpWQQ0Jjp99YhYpc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-abi@4.4.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 16750}, "type": "module", "engines": {"node": ">=22.12.0"}, "exports": "./index.js", "gitHead": "33f59aa0e6f518c8f8117d156f92d6316bf7206d", "scripts": {"test": "node --test test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"semver": "^7.6.3"}, "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "devDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_4.4.0_1745886317316_0.725074469615814", "host": "s3://npm-registry-packages-npm-production"}}, "4.5.0": {"name": "node-abi", "version": "4.5.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@4.5.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "ac6dece59e5b3558979d3cd67557a5ca99b09245", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-4.5.0.tgz", "fileCount": 6, "integrity": "sha512-bjO1R1ylXYB1oC0RTm5yy+8yBjlIfhYK1Un44tyMQkVETrrg68TIw4yKP4wzpi9UxIZ2pXzp2pixo/kqoXfjGw==", "signatures": [{"sig": "MEQCIGmZ1cHF+XIt41lBQdxmz7t9xakbyeMTab++n9SrGbSvAiBdBSlsy2EXHJGpSxP4zZpUOqnYZ74mOaDsk3z3cBot1g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-abi@4.5.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 16873}, "type": "module", "engines": {"node": ">=22.12.0"}, "exports": "./index.js", "gitHead": "dc27d0ad8ab8c68840632cecce614a6f7e396567", "scripts": {"test": "node --test test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"semver": "^7.6.3"}, "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "devDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_4.5.0_1745972980417_0.6521876948362617", "host": "s3://npm-registry-packages-npm-production"}}, "3.75.0": {"name": "node-abi", "version": "3.75.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@3.75.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "2f929a91a90a0d02b325c43731314802357ed764", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-3.75.0.tgz", "fileCount": 5, "integrity": "sha512-OhYaY5sDsIka7H7AtijtI9jwGYLyl29eQn/W623DiN/MIv5sUqc4g7BIDThX+gb7di9f6xK02nkp8sdfFWZLTg==", "signatures": [{"sig": "MEYCIQDmklj7s+TXly9cphN+dVhwiJt9s/iBFEPSts67RlB8VwIhAItASV4U6LfWdZTojWmBUga3Cunpbs7F+K6z+izHxul8", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-abi@3.75.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 16891}, "main": "index.js", "engines": {"node": ">=10"}, "gitHead": "b943f6d1076c262fbf5c8ca6649e42deb5faa347", "scripts": {"test": "tape test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "20.19.1", "dependencies": {"semver": "^7.3.5"}, "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tape": "^5.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_3.75.0_1746055430012_0.13257887556515646", "host": "s3://npm-registry-packages-npm-production"}}, "4.6.0": {"name": "node-abi", "version": "4.6.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@4.6.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "7ac0856bc94557fef74e7b7a25411e73326badf1", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-4.6.0.tgz", "fileCount": 6, "integrity": "sha512-AActsRaxKmWuE/0u28/1u4PlvEcY/mh83HC0j+p3/4nQHgcHTgGR8tkDeDiyVboR+4J6YVl+er/C7NmQBhMP6A==", "signatures": [{"sig": "MEYCIQC+uNL1SkdE3eR9Vsn9o0C2ogd1YD/WGmNZq+ou9llwjAIhAMwXQQAZ0eJkQn07hHkUZMJDjHgrgv4pW68UL/LTauk+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-abi@4.6.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 16873}, "type": "module", "engines": {"node": ">=22.12.0"}, "exports": "./index.js", "gitHead": "cc7c42e7580e05c5bec161c874ab81ff39b83e9c", "scripts": {"test": "node --test test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"semver": "^7.6.3"}, "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "devDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_4.6.0_1746201520540_0.4233773262995062", "host": "s3://npm-registry-packages-npm-production"}}, "4.8.0": {"name": "node-abi", "version": "4.8.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@4.8.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "6e284ed89eab8eb679601da0800d388eaf9dc6fd", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-4.8.0.tgz", "fileCount": 6, "integrity": "sha512-+P1Mf1XNURT+wN9UK8cjtHSwmmG02iAsX0JaYDDdXgMUe5eybde3AKpGGxOrkDmPkuonYJ6WjIRrNVQe0kjmFg==", "signatures": [{"sig": "MEQCIDJzQ6gxvFgn9eStjE4VW6jXwAD877PwvOf26yX0m43uAiAQr+0O5sqZRigAQsMenVzGlbddQpGMhizrHMSHoXGs1Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-abi@4.8.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 16873}, "type": "module", "engines": {"node": ">=22.12.0"}, "exports": "./index.js", "gitHead": "f68e336d5afa2e436c770f8ff10a37eae0a6bca3", "scripts": {"test": "node --test test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"semver": "^7.6.3"}, "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "devDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_4.8.0_1746491443118_0.35839461907785375", "host": "s3://npm-registry-packages-npm-production"}}, "4.9.0": {"name": "node-abi", "version": "4.9.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@4.9.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "ca6dabf7991e54bf3ba6d8d32641e1b84f305263", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-4.9.0.tgz", "fileCount": 6, "integrity": "sha512-0isb3h+AXUblx5Iv0mnYy2WsErH+dk2e9iXJXdKAtS076Q5hP+scQhp6P4tvDeVlOBlG3ROKvkpQHtbORllq2A==", "signatures": [{"sig": "MEUCIQDBE6wiSTe9Ifd1kcxQLqOj08ofuDeugCZ2LO8gSxk7nwIgMpZLi/v4ZAXeowoua+3wodSykEQmZB05dG8DlKF8t+M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-abi@4.9.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 16899}, "type": "module", "engines": {"node": ">=22.12.0"}, "exports": "./index.js", "gitHead": "63bae363574b4349f3cce08554a07d93a6249be2", "scripts": {"test": "node --test test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"semver": "^7.6.3"}, "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "devDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_4.9.0_1747806148069_0.7959570982262383", "host": "s3://npm-registry-packages-npm-production"}}, "4.10.0": {"name": "node-abi", "version": "4.10.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@4.10.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "dccdf1777777a525499ca5205605276e13f37996", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-4.10.0.tgz", "fileCount": 6, "integrity": "sha512-99+BHGmmk969uU3Q4DM9t2tBvDKmqZ3prz/yumY7n+fV2zA+DAOBXuodsp9ZD/QSXK8aSKgWIpkg8tbrAe9JBg==", "signatures": [{"sig": "MEUCIF+zRbfXl1amx5Voyx4L6F6U7vaSNiildcStC+sNUHHDAiEAsA8ih8pJspaWADRTP9yrW1mEV64JFn/KW4XRb4Fr0IA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-abi@4.10.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 16901}, "type": "module", "engines": {"node": ">=22.12.0"}, "exports": "./index.js", "gitHead": "34a63fabd9c05be5e751b19a21e69f09ea79fb48", "scripts": {"test": "node --test test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "actor": {"name": "electron-cfa", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"semver": "^7.6.3"}, "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "devDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_4.10.0_1750777941051_0.04785535193949997", "host": "s3://npm-registry-packages-npm-production"}}, "4.11.0": {"name": "node-abi", "version": "4.11.0", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "_id": "node-abi@4.11.0", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "homepage": "https://github.com/electron/node-abi#readme", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "dist": {"shasum": "5db36496ec9a46171892196865378d5782881786", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-4.11.0.tgz", "fileCount": 6, "integrity": "sha512-QSiqRULRCbxU9nEUBu1oJCwKKjsEdcVEB9+fuoiYMEWJGMJrLMv5xwbKxe5h2en0xCNikTB+f6IIijO+TJc6JQ==", "signatures": [{"sig": "MEUCIQCfYE9t40N0JYty2IkjHQ+t0NvZRpmmo6Kh3bWZ6+69uQIgfL+8S2g64VYh733m/jC5ONVHr6e6Fr+F8YXu5vEgoy0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-abi@4.11.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 17024}, "type": "module", "engines": {"node": ">=22.12.0"}, "exports": "./index.js", "gitHead": "dc87bac4e3d4ad540cbad5642ce9db937a07019b", "scripts": {"test": "node --test test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "_npmUser": {"name": "electron-cfa", "actor": {"name": "electron-cfa", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/electron/node-abi.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"semver": "^7.6.3"}, "publishConfig": {"provenance": true}, "_hasShrinkwrap": false, "devDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/node-abi_4.11.0_1750867843232_0.8009378156615741", "host": "s3://npm-registry-packages-npm-production"}}, "4.12.0": {"name": "node-abi", "version": "4.12.0", "description": "Get the Node ABI for a given target and runtime, and vice versa.", "type": "module", "exports": "./index.js", "scripts": {"test": "node --test test/index.js", "update-abi-registry": "node --unhandled-rejections=strict scripts/update-abi-registry.js"}, "repository": {"type": "git", "url": "git+https://github.com/electron/node-abi.git"}, "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "homepage": "https://github.com/electron/node-abi#readme", "devDependencies": {}, "dependencies": {"semver": "^7.6.3"}, "engines": {"node": ">=22.12.0"}, "publishConfig": {"provenance": true}, "_id": "node-abi@4.12.0", "gitHead": "c4babcba0fd1a808019327cbda0c544eed50f76b", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-bPSN9a/qIEiURzVVO/I7P/8oPeYTSl+vnvVZBXM/8XerKOgA3dMAIUjl+a+lz9VwTowwSKS3EMsgz/vWDXOkuQ==", "shasum": "2e24da338f07a09beeb2d7ad840aa83d94345c3e", "tarball": "https://registry.npmjs.org/node-abi/-/node-abi-4.12.0.tgz", "fileCount": 6, "unpackedSize": 17109, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/node-abi@4.12.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIGhkvKaHtnE1rTi/LEMsYlp7qJq3vt4EDVo4jTWFnfjVAiEA0psJTDiKJjKvHRLlzJOUtaWL0C9lS1w3fMsJvnRvQyg="}]}, "_npmUser": {"name": "electron-cfa", "email": "<EMAIL>", "actor": {"name": "electron-cfa", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/node-abi_4.12.0_1751305889430_0.6701816072599991"}, "_hasShrinkwrap": false}}, "time": {"created": "2016-12-03T00:20:14.024Z", "modified": "2025-06-30T17:51:30.096Z", "1.0.0": "2016-12-03T00:20:14.024Z", "1.0.1": "2016-12-03T01:20:29.717Z", "1.0.2": "2016-12-03T11:57:14.572Z", "1.0.3": "2016-12-04T18:08:10.387Z", "1.1.0": "2017-01-26T09:42:41.879Z", "1.2.0": "2017-01-31T20:28:12.191Z", "1.2.1": "2017-01-31T21:05:57.824Z", "1.3.0": "2017-02-02T13:36:49.476Z", "1.3.1": "2017-02-02T14:43:50.283Z", "1.3.2": "2017-02-07T09:23:20.026Z", "1.3.3": "2017-02-09T14:25:58.269Z", "2.0.0": "2017-02-10T17:14:49.945Z", "2.0.1": "2017-05-13T00:11:47.942Z", "2.0.2": "2017-05-13T00:32:55.453Z", "2.0.3": "2017-05-31T00:27:49.377Z", "2.1.0": "2017-07-14T10:40:48.259Z", "2.1.1": "2017-09-01T11:05:17.823Z", "2.1.2": "2017-11-01T11:52:24.211Z", "2.2.0": "2018-01-28T17:00:36.760Z", "2.3.0": "2018-02-25T22:38:23.896Z", "2.4.0": "2018-04-25T09:55:25.336Z", "2.4.1": "2018-05-04T14:58:51.854Z", "2.4.2": "2018-06-21T15:30:58.882Z", "2.4.3": "2018-06-21T16:24:05.012Z", "2.4.4": "2018-09-19T04:31:24.485Z", "2.4.5": "2018-10-01T15:40:59.877Z", "2.5.0": "2018-11-01T23:48:11.594Z", "2.5.1": "2018-12-22T03:15:30.540Z", "2.6.0": "2019-01-26T20:34:11.388Z", "2.7.0": "2019-02-03T20:43:19.301Z", "2.7.1": "2019-02-11T21:01:20.562Z", "2.8.0": "2019-04-25T18:22:23.431Z", "2.9.0": "2019-06-19T17:53:30.117Z", "2.10.0": "2019-07-30T16:39:53.142Z", "2.11.0": "2019-08-12T21:09:05.062Z", "2.12.0": "2019-10-21T18:57:38.996Z", "2.13.0": "2019-11-27T09:01:57.934Z", "2.14.0": "2020-02-04T19:23:25.959Z", "2.15.0": "2020-02-13T20:25:46.445Z", "2.16.0": "2020-04-22T10:02:50.468Z", "2.17.0": "2020-05-19T10:12:32.575Z", "2.18.0": "2020-05-29T13:30:24.199Z", "2.19.0": "2020-08-20T22:58:33.435Z", "2.19.1": "2020-08-24T20:43:38.857Z", "2.19.2": "2020-11-18T00:45:55.482Z", "2.19.3": "2020-11-18T07:37:12.065Z", "2.20.0": "2021-03-02T22:40:20.810Z", "2.21.0": "2021-03-06T02:36:29.967Z", "2.22.0": "2021-04-20T19:18:38.277Z", "2.23.0": "2021-04-20T20:09:15.532Z", "2.24.0": "2021-04-20T21:08:55.036Z", "2.25.0": "2021-04-21T00:23:45.779Z", "2.26.0": "2021-04-21T01:33:09.413Z", "2.27.0": "2021-05-25T12:43:49.420Z", "2.28.0": "2021-05-25T14:36:21.266Z", "2.29.0": "2021-05-25T17:18:11.201Z", "2.30.0": "2021-05-25T21:27:05.710Z", "2.30.1": "2021-08-31T19:46:23.147Z", "3.0.0": "2021-09-01T23:45:47.303Z", "3.1.0": "2021-09-23T07:46:00.904Z", "3.2.0": "2021-09-27T23:07:27.637Z", "3.3.0": "2021-10-21T19:53:40.996Z", "3.4.0": "2021-11-16T01:30:42.781Z", "3.4.1": "2021-11-16T01:58:11.877Z", "3.5.0": "2021-11-16T02:19:53.924Z", "3.7.0": "2022-02-01T02:28:14.020Z", "3.8.0": "2022-02-02T20:40:40.860Z", "3.15.0": "2022-04-19T18:18:14.786Z", "3.21.0": "2022-05-24T19:05:26.235Z", "3.22.0": "2022-05-25T20:08:22.198Z", "3.23.0": "2022-08-01T23:08:11.844Z", "3.24.0": "2022-08-03T20:07:38.864Z", "3.25.0": "2022-09-26T22:09:55.166Z", "3.26.0": "2022-10-05T17:38:14.217Z", "3.27.0": "2022-10-19T03:04:51.702Z", "3.28.0": "2022-10-19T04:47:25.331Z", "3.30.0": "2022-11-30T03:28:44.621Z", "3.31.0": "2023-01-09T20:51:43.623Z", "3.32.0": "2023-02-07T00:39:55.073Z", "3.33.0": "2023-02-10T05:07:43.850Z", "3.34.0": "2023-04-04T03:08:06.695Z", "3.35.0": "2023-04-06T18:09:45.757Z", "3.36.0": "2023-04-18T17:09:12.793Z", "3.37.0": "2023-04-18T18:07:44.575Z", "3.40.0": "2023-04-18T23:06:53.955Z", "3.42.0": "2023-05-31T18:19:21.342Z", "3.43.0": "2023-05-31T22:07:51.003Z", "3.44.0": "2023-06-08T00:26:36.911Z", "3.45.0": "2023-06-09T20:07:28.737Z", "3.46.0": "2023-08-14T22:46:49.866Z", "3.47.0": "2023-08-16T23:16:21.366Z", "3.48.0": "2023-10-10T05:19:49.237Z", "3.49.0": "2023-10-10T23:07:54.953Z", "3.50.0": "2023-10-11T21:12:39.602Z", "3.51.0": "2023-10-17T00:23:06.639Z", "3.52.0": "2023-12-04T23:06:42.265Z", "3.54.0": "2024-01-04T15:07:30.851Z", "3.55.0": "2024-02-20T18:32:57.509Z", "3.56.0": "2024-02-24T00:53:47.728Z", "3.57.0": "2024-03-31T18:12:13.380Z", "3.58.0": "2024-04-16T00:25:14.262Z", "3.59.0": "2024-04-17T23:13:31.057Z", "3.60.0": "2024-04-19T15:08:17.205Z", "3.61.0": "2024-04-23T00:25:14.487Z", "3.62.0": "2024-04-23T15:32:33.755Z", "3.63.0": "2024-05-30T15:09:18.715Z", "3.64.0": "2024-06-10T21:09:16.746Z", "3.65.0": "2024-06-13T01:48:56.948Z", "3.66.0": "2024-08-20T15:29:19.254Z", "3.67.0": "2024-08-21T02:20:55.592Z", "3.68.0": "2024-09-19T15:06:48.055Z", "3.69.0": "2024-10-15T00:26:16.283Z", "3.70.0": "2024-10-15T11:43:37.303Z", "3.71.0": "2024-10-16T00:26:48.403Z", "3.72.0": "2025-01-14T16:04:44.452Z", "3.73.0": "2025-01-15T23:16:17.758Z", "3.74.0": "2025-01-31T19:49:53.638Z", "4.0.0": "2025-03-03T21:32:17.400Z", "4.1.0": "2025-03-04T15:11:48.852Z", "4.2.0": "2025-03-10T16:01:59.260Z", "4.3.0": "2025-04-22T00:24:46.061Z", "4.4.0": "2025-04-29T00:25:17.473Z", "4.5.0": "2025-04-30T00:29:40.593Z", "3.75.0": "2025-04-30T23:23:50.192Z", "4.6.0": "2025-05-02T15:58:40.738Z", "4.8.0": "2025-05-06T00:30:43.325Z", "4.9.0": "2025-05-21T05:42:28.228Z", "4.10.0": "2025-06-24T15:12:21.217Z", "4.11.0": "2025-06-25T16:10:43.405Z", "4.12.0": "2025-06-30T17:51:29.627Z"}, "bugs": {"url": "https://github.com/electron/node-abi/issues"}, "author": {"name": "<PERSON><PERSON>"}, "license": "MIT", "homepage": "https://github.com/electron/node-abi#readme", "keywords": ["node", "electron", "node_module_version", "abi", "v8"], "repository": {"type": "git", "url": "git+https://github.com/electron/node-abi.git"}, "description": "Get the Node ABI for a given target and runtime, and vice versa.", "maintainers": [{"name": "electron-cfa", "email": "<EMAIL>"}], "readme": "# Node.js ABI\n\n[![Build Status](https://github.com/electron/node-abi/actions/workflows/test.yml/badge.svg)](https://github.com/electron/node-abi/actions/workflows/test.yml)\n[![Auto-update ABI JSON file](https://github.com/electron/node-abi/actions/workflows/update-abi.yml/badge.svg)](https://github.com/electron/node-abi/actions/workflows/update-abi.yml)\n[![Snyk badge](https://snyk.io/test/github/electron/node-abi/badge.svg)](https://snyk.io/test/github/electron/node-abi)\n[![npm version](http://img.shields.io/npm/v/node-abi.svg)](https://npmjs.org/package/node-abi)\n\nGet the Node ABI (application binary interface) for a given target and runtime, and vice versa.\n\n## Installation\n\n```shell\nnpm install node-abi\n```\n\n## Usage\n\n```javascript\nconst nodeAbi = require('node-abi')\n\nnodeAbi.getAbi('7.2.0', 'node')\n// '51'\nnodeAbi.getAbi('1.4.10', 'electron')\n// '50'\nnodeAbi.getTarget('51', 'node')\n// '7.2.0'\nnodeAbi.getTarget('50', 'electron')\n// '1.4.15'\n\nnodeAbi.allTargets\n// [\n//  { runtime: 'node', target: '0.10.48', abi: '11', lts: false },\n//  { runtime: 'node', target: '0.12.17', abi: '14', lts: false },\n//  { runtime: 'node', target: '4.6.1', abi: '46', lts: true },\n//  { runtime: 'node', target: '5.12.0', abi: '47', lts: false },\n//  { runtime: 'node', target: '6.9.4', abi: '48', lts: true },\n//  { runtime: 'node', target: '7.4.0', abi: '51', lts: false },\n//  { runtime: 'electron', target: '1.0.2', abi: '47', lts: false },\n//  { runtime: 'electron', target: '1.2.8', abi: '48', lts: false },\n//  { runtime: 'electron', target: '1.3.13', abi: '49', lts: false },\n//  { runtime: 'electron', target: '1.4.15', abi: '50', lts: false }\n// ]\nnodeAbi.deprecatedTargets\nnodeAbi.supportedTargets\nnodeAbi.additionalTargets\nnodeAbi.futureTargets\n// ...\n```\n\n## References\n\n- https://github.com/lgeiger/electron-abi\n- https://nodejs.org/en/download/releases/\n- https://github.com/nodejs/Release\n", "readmeFilename": "README.md", "users": {"chinawolf_wyp": true}}