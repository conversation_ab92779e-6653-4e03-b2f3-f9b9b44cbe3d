{"_id": "@types/body-parser", "_rev": "687-5c1ab7696a7cfc1c5ef6862e18b09d41", "name": "@types/body-parser", "dist-tags": {"ts2.0": "1.16.5", "ts2.1": "1.16.7", "ts2.2": "1.17.0", "ts2.3": "1.17.1", "ts2.5": "1.17.1", "ts2.6": "1.17.1", "ts2.4": "1.17.1", "ts2.7": "1.17.1", "ts3.5": "1.19.0", "ts2.8": "1.19.0", "ts2.9": "1.19.0", "ts3.0": "1.19.0", "ts3.1": "1.19.0", "ts3.2": "1.19.0", "ts3.3": "1.19.0", "ts3.4": "1.19.0", "ts3.6": "1.19.1", "ts3.7": "1.19.2", "ts3.8": "1.19.2", "ts3.9": "1.19.2", "ts4.0": "1.19.2", "ts4.1": "1.19.2", "ts4.2": "1.19.2", "ts4.4": "1.19.2", "ts4.3": "1.19.2", "ts5.9": "1.19.6", "ts4.5": "1.19.5", "ts4.6": "1.19.5", "ts4.7": "1.19.5", "ts4.8": "1.19.5", "ts4.9": "1.19.5", "ts5.0": "1.19.5", "ts5.5": "1.19.6", "ts5.8": "1.19.6", "ts5.1": "1.19.6", "ts5.3": "1.19.6", "ts5.4": "1.19.6", "latest": "1.19.6", "ts5.2": "1.19.6", "ts5.6": "1.19.6", "ts5.7": "1.19.6"}, "versions": {"0.0.16-alpha": {"name": "@types/body-parser", "version": "0.0.16-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/santialbo/"}, "license": "MIT", "_id": "@types/body-parser@0.0.16-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "19a646b8b35214adfc8e891ecf24e4c56cfd5eae", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-0.0.16-alpha.tgz", "integrity": "sha512-S0/Hqm6QKGcYUHynadLFJI7MFrEGwQqxpfMWutT0p/ZXtRanBwphuezySqflGJ9Pvlgs+NQsttVtO+BnndiU5A==", "signatures": [{"sig": "MEYCIQCit50R9IvOAJydofgEznV5zqaMZHn3xXyH5KOd0rhfTgIhAJGtlt599+Pe2ZLIUEuw50MRPvbvrjkSIxQaJsF+DJPJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\body-parser", "_shasum": "19a646b8b35214adfc8e891ecf24e4c56cfd5eae", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\body-parser", "_npmVersion": "3.8.2", "description": "Type definitions for body-parser from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"express": "*"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-0.0.16-alpha.tgz_1463459562533_0.12686472991481423", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.17-alpha": {"name": "@types/body-parser", "version": "0.0.17-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/santialbo/"}, "license": "MIT", "_id": "@types/body-parser@0.0.17-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "b91f37ec229167f636eef63771782bb500937f83", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-0.0.17-alpha.tgz", "integrity": "sha512-tuP5/VDjtnpFX9WjS5T0cAgfUrLSUV5Y/O11L5ZvJzeR6kVVKg4Fc6kJK9nauryJR6YFPK0dbVJbz8gC2rEu5w==", "signatures": [{"sig": "MEUCICeyGaDUBVChyCpjPnZNO8vI84NifezUHQCtI9oMmJWlAiEAkbo25zdTVgfDGLxolsQ2m411rQMsNEGCPD3CI7eX1GE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\body-parser", "_shasum": "b91f37ec229167f636eef63771782bb500937f83", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\body-parser", "_npmVersion": "3.8.2", "description": "Type definitions for body-parser from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"express": "*"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-0.0.17-alpha.tgz_1463689372840_0.7001565264072269", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.22-alpha": {"name": "@types/body-parser", "version": "0.0.22-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/santialbo/"}, "license": "MIT", "_id": "@types/body-parser@0.0.22-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "d0fc239c5d289335eacb890b660ce047170a56b8", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-0.0.22-alpha.tgz", "integrity": "sha512-Dw8sv994+/BUCTHUE2SbSwO/oI79lSOdzaDXBOTn6Xp18LH7t5eYmlIeL438bdnJ4fLpVOZ70ysZLRdRnDlkFA==", "signatures": [{"sig": "MEUCIEPPJXzNFNqW79JueC/TsYk5w0sEHHfEneWE8Yy2oF9KAiEAs+xhKjddPQfvQsR8KirU5mvjFds4UL+V6hSDNtIlLVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\body-parser", "_shasum": "d0fc239c5d289335eacb890b660ce047170a56b8", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\body-parser", "_npmVersion": "3.8.2", "description": "TypeScript definitions for body-parser", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"@types/express": "*"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-0.0.22-alpha.tgz_1463771687292_0.8772794110700488", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.23-alpha": {"name": "@types/body-parser", "version": "0.0.23-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/santialbo/"}, "license": "MIT", "_id": "@types/body-parser@0.0.23-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "6fd99eb40d9f4c121609caae6d47795ddb2d1d0d", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-0.0.23-alpha.tgz", "integrity": "sha512-QdTasc+jtAgmIOGPywXcozHLwtRN+zVvF3UY6BPx+ZBb3s/SoWUMy0MPVtKdT37yFXLN6yvfZQEyabH5a4FFKQ==", "signatures": [{"sig": "MEUCIBCsJWWJHm28geCrnz3GVImCcB7euHaEMpcxoQy5mbBHAiEA93XOntejd99b3C1aJXDsXyF6SR4gJwpsF4InffSNfu8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\body-parser", "_shasum": "6fd99eb40d9f4c121609caae6d47795ddb2d1d0d", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\body-parser", "_npmVersion": "3.8.2", "description": "TypeScript definitions for body-parser", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"@types/express": "*"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-0.0.23-alpha.tgz_1464150801944_0.7024018482770771", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.24-alpha": {"name": "@types/body-parser", "version": "0.0.24-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/santialbo/"}, "license": "MIT", "_id": "@types/body-parser@0.0.24-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "9dca05c4dd8f5c4111cc419f4b3db064e292bea1", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-0.0.24-alpha.tgz", "integrity": "sha512-WVze3+OTKD485ZT0DiJsZ3c/u6fl3gAbHMSzkisIaUfLCQLuG4rx+z/ucUJLt1WGpcjXFIKy4JfeMV1sCT858A==", "signatures": [{"sig": "MEUCIQDTt+EKzYnTY01eY3JLorCEbYjcLgt7x/U06+NA6Cn4GQIgQecm4lKUOnsALVG6qClWdZGYOaFZ/s8Hl3GAd2ZuMkI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\body-parser", "_shasum": "9dca05c4dd8f5c4111cc419f4b3db064e292bea1", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\body-parser", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.9.5", "description": "TypeScript definitions for body-parser", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {"@types/express": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-0.0.24-alpha.tgz_1467399629978_0.39359091059304774", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.25-alpha": {"name": "@types/body-parser", "version": "0.0.25-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/santialbo/"}, "license": "MIT", "_id": "@types/body-parser@0.0.25-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "427c0f3f9b820e84247c73e44f1d9e1271bc543a", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-0.0.25-alpha.tgz", "integrity": "sha512-Db2lRasosV/vdpz2vk869pSYH/KOVj7PFRE6s8czKMXuJgRW0XFqbirgB+h59qnV7kPg41UsFiyLMQCak+EQkw==", "signatures": [{"sig": "MEUCIA0PvBFvTYkyG9+cQxf++mML4gBNzURYdmy4NTNAVfQ5AiEA6MW4b4iMTiNA64Ls20K1B42ib9V6PpVbDrCbxa/33OM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\body-parser", "_shasum": "427c0f3f9b820e84247c73e44f1d9e1271bc543a", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\body-parser", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for body-parser", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/express": "4.0.24-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-0.0.25-alpha.tgz_1467411338216_0.4542629336938262", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.26-alpha": {"name": "@types/body-parser", "version": "0.0.26-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/santialbo/"}, "license": "MIT", "_id": "@types/body-parser@0.0.26-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "d87d67431c30d96fc900b7c91a7b0389da6b4ff4", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-0.0.26-alpha.tgz", "integrity": "sha512-gunwEdTZEvG8cVpx7jEt076eVhwZ5qvQxtuJF3tRUlAnYNfqSoroqe9dKwBSqwlPEI+aSZ6H/pEbb/Xu8wfAXg==", "signatures": [{"sig": "MEUCIEgxDFgZaA0k7jdKjqE63b3FeXd2Mno85SVN1BL7EpJVAiEAobZKbVk2yidPJ43YSNbiWipwhNiLuA/Gh+aLYcV4U4Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\body-parser", "_shasum": "d87d67431c30d96fc900b7c91a7b0389da6b4ff4", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\body-parser", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for body-parser", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/express": "4.0.25-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-0.0.26-alpha.tgz_1467424896042_0.8576473984867334", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.27-alpha": {"name": "@types/body-parser", "version": "0.0.27-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/santialbo/"}, "license": "MIT", "_id": "@types/body-parser@0.0.27-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "b255f2e2419a4205b4fa2f33178260312639f61e", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-0.0.27-alpha.tgz", "integrity": "sha512-IqkpHbhLg1zf0VlDo4r1xEEyUckbF4AQ0Ib8xRBpm8WM/qH7PP6eeB/VdUZPj1blK16KUKBBIKzhnxJq4/Mmmg==", "signatures": [{"sig": "MEYCIQC4DD0/6AmYN9bumBGM7LU4DczZi87gDsWqH0rUztOdqwIhAIkca8vmQmNifG0LRXxADAPs5rvM7trXz/t0KSk9kT11", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\body-parser", "_shasum": "b255f2e2419a4205b4fa2f33178260312639f61e", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\body-parser", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for body-parser", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/express": "4.0.27-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-0.0.27-alpha.tgz_1467588258777_0.8369862057734281", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.28-alpha": {"name": "@types/body-parser", "version": "0.0.28-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/santialbo/"}, "license": "MIT", "_id": "@types/body-parser@0.0.28-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "6433aec4618e9ec660c318a4e11d9f241eda77f1", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-0.0.28-alpha.tgz", "integrity": "sha512-FMj44c0WseaQHaK/bAV8yGaKPo2XAKCToB+dGR56XvlJJ+H10OMG0SqTj7kbwIkgYUbb8FnicIyo3aBBRCZbPQ==", "signatures": [{"sig": "MEUCIQCKC6Cos/IynCVmjha8RWfcSuI+1a+DU+jDyFonCdQnAQIgDT/+v3t4nwv5iWUccJDiBQXtWdmAFUlyv7N4tDE6sGo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\body-parser", "_shasum": "6433aec4618e9ec660c318a4e11d9f241eda77f1", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\body-parser", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for body-parser", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/express": "4.0.28-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-0.0.28-alpha.tgz_1467917909383_0.6797586085740477", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.29": {"name": "@types/body-parser", "version": "0.0.29", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/santialbo/"}, "license": "MIT", "_id": "@types/body-parser@0.0.29", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "6476c6a2cff20426554a4ea1c1c189fb543d07dc", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-0.0.29.tgz", "integrity": "sha512-o7VrSM5VDupv6NJ5eFPjNBFxIlO6DFjWI+YyaqiRRtsEKEhxZzTsIfgMhoWYgJxtfZFOANlyFyUo65Q6bFVDkg==", "signatures": [{"sig": "MEUCIQD6XcJB0rTYU0OwiU5fVMbq56B3f4MFYBdHIzRD8yiZ4wIgD19vD2hw4vGkZjae2RIBpLGpB0gr6NaKZKR8YFka3eg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\body-parser", "_shasum": "6476c6a2cff20426554a4ea1c1c189fb543d07dc", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\body-parser", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for body-parser", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"@types/express": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-0.0.29.tgz_1468505403896_0.6348783425055444", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.30": {"name": "@types/body-parser", "version": "0.0.30", "author": "<PERSON><PERSON> <https://github.com/santialbo/>, VILIC VANE <https://vilic.info>, <PERSON> <https://github.com/dreampulse/>", "license": "MIT", "_id": "@types/body-parser@0.0.30", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "c1def8ee37d81584a03f70a84a3e5300153e8dd2", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-0.0.30.tgz", "integrity": "sha512-wt6OYa19B8AzxoLVGCqMH+pcjJ5glz7JVUaVDybyEs/Br2ump28YyIQT6RsAxUhhQGv0VkKbdF9P8EUB9EWquQ==", "signatures": [{"sig": "MEQCIB2o25NYT6MYwIdIJbL2z7CIHa9cMqX/5JieiBHIHKpMAiAmeE5Wo0rva3BSU3Z6n/i5Efmi8vSaARVhU9HSvpwyJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/express": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-0.0.30.tgz_1470152868540_0.9353581846226007", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.31": {"name": "@types/body-parser", "version": "0.0.31", "author": "<PERSON><PERSON> <https://github.com/santialbo/>, VILIC VANE <https://vilic.info>, <PERSON> <https://github.com/dreampulse/>", "license": "MIT", "_id": "@types/body-parser@0.0.31", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "1e1187c98bb77ea5fea88027ba720c8c94577ae0", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-0.0.31.tgz", "integrity": "sha512-Nogov78givDz3MtM8yajEi87V/9gK2fTdpnAO3BRskV+1wR6vpbCRNgdx+IykuPNztwkPfSICwmQ74+ybmLEqg==", "signatures": [{"sig": "MEUCIQDLpxCr73+3BAf2COmXeOMlLz2nBxPTCZnX++2jrHvY2wIgAnJbqLz1VKZkfzOEvHNR2+hFOTaWMvEBLd6Xjj5If8o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/express": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-0.0.31.tgz_1471620074581_0.881157050607726", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.32": {"name": "@types/body-parser", "version": "0.0.32", "author": "<PERSON><PERSON> <https://github.com/santialbo/>, VILIC VANE <https://vilic.info>, <PERSON> <https://github.com/dreampulse/>", "license": "MIT", "_id": "@types/body-parser@0.0.32", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "605ec5861338e0e34da764f5b7f622eb7c1fdb1c", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-0.0.32.tgz", "integrity": "sha512-ZhDxTrqDOI+pAB7Tt0JbiFYP7fksfpsLYeXh9NONHae7ziUH2lqqU1iZR0C0PzvHxJCZFdCeWR6z06jWZFM/mw==", "signatures": [{"sig": "MEUCIQCrHX7ax6sVie+Ds/5bXSzdmzN4zak0viR4ityXpyyh8QIgf3D0X4Q7y89UBAUrCOX4fODap3+DENOUqYqsHpcxxbw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/express": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-0.0.32.tgz_1472150138315_0.4636320748832077", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.33": {"name": "@types/body-parser", "version": "0.0.33", "author": "<PERSON><PERSON> <https://github.com/santialbo/>, VILIC VANE <https://vilic.info>, <PERSON> <https://github.com/dreampulse/>", "license": "MIT", "_id": "@types/body-parser@0.0.33", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "33ca1498fc37e51c5df0c81cae34569e7041e025", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-0.0.33.tgz", "integrity": "sha512-QRW4/ZqUqSXwm3QN+EGXvdlTvsRhw//bzfot23QnwSovBFuR3XTGKfXzas1bpmMKWH+BnkLtn7l7OTlrlCCvaQ==", "signatures": [{"sig": "MEYCIQCszTyjhhcsSygDfM1Fqn2NknZASYnK/KxqTPl7PMVAYwIhAPED0fRFFuqab3jqCHZA25IUmtRh2hyMaccVdgzVND4Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/express": "*"}, "_npmOperationalInternal": {"tmp": "tmp/body-parser-0.0.33.tgz_1474302198383_0.8077569084707648", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "3dd3e60390ba243da416a19c8c853368a46f2cc894003a59c5d80c2b6e89966d"}, "0.0.34": {"name": "@types/body-parser", "version": "0.0.34", "license": "MIT", "_id": "@types/body-parser@0.0.34", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/santialbo/", "name": "<PERSON><PERSON>"}, {"url": "https://vilic.info", "name": "VILIC VANE"}, {"url": "https://github.com/dreampulse/", "name": "<PERSON>"}, {"url": "https://github.com/blendsdk/", "name": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "2cb766935f67416bbec80fc770b79e6eeeda80b5", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-0.0.34.tgz", "integrity": "sha512-weifQkE/pw7aYLh7EI4pKRSfvHvyrNl+XthouTtsIdr4KxBfAwh1kTl6ujcOFov9qEML/Ty9sPxTtW2PQHw8SQ==", "signatures": [{"sig": "MEYCIQDYHRMPLCpp/SJilRki+UYErFWxXGvShHq0CeUdNOO2cwIhAOz/5ux5FZZmhz0VCzvW6xF9u5u6mbmNxanXpmLVLmaC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/express": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/body-parser-0.0.34.tgz_1487362952707_0.9786648128647357", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "3d1f7d32fb3b4b91ce714ee48e5a55c01a189b2391321c37bc88451f28c59b69"}, "1.16.0": {"name": "@types/body-parser", "version": "1.16.0", "license": "MIT", "_id": "@types/body-parser@1.16.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/santialbo", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/vilic", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dreampulse", "name": "<PERSON>"}, {"url": "https://github.com/blendsdk/", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>"}], "dist": {"shasum": "98e9dd9664a7fa814fec2c50c95bbf9edacf5f05", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.16.0.tgz", "integrity": "sha512-5j87YEMxNggx5zyhO2yw5pSfRt1iBx41DvyVwsZdHtpY/NsneeZ2PvyTmg24rc0Ni9qoKaCdDBkYgtqtUphAnA==", "signatures": [{"sig": "MEQCIAkE+lljVGeIR13KmZqXTvpZVVAHR4kqjwgL+scWjfrnAiA28VnWk8inVMQ58veM6hrQMH0N0exMAdW8ahbc8M59vw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/node": "*", "@types/express": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/body-parser-1.16.0.tgz_1489018828448_0.154564353171736", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "65457807a3dee76eb4bad879e9f37b6c8fd0f0d69aecfe765fd2959cb94a27c8"}, "1.16.1": {"name": "@types/body-parser", "version": "1.16.1", "license": "MIT", "_id": "@types/body-parser@1.16.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/santialbo", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/vilic", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dreampulse", "name": "<PERSON>"}, {"url": "https://github.com/blendsdk/", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>"}], "dist": {"shasum": "4c1dc2d6ace3b8f74447020493aa19c5d8287d9a", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.16.1.tgz", "integrity": "sha512-8Wsmu3pJ2RDvA0em7EkWg8wPhBPIzkiCBDa2KbQw4O4H6qbuCtrFdQ7wcRrXCbie6yj5KtT5yHn8jfllvR1glQ==", "signatures": [{"sig": "MEUCIFm8Z10kRMTXcYb4BxS2KBWDSruDNEBlHUPoe2w1Z8xgAiEAozvudvv2WCXNNqh3zdeMtLVprsARUOd4HmHIrc9WOCw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/node": "*", "@types/express": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/body-parser-1.16.1.tgz_1490127592109_0.1476863173302263", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "a1ea01d3b87c15f41bde921878526b5c4ea88496d4b0426a1d2bc2fcb70fbdbd"}, "1.16.2": {"name": "@types/body-parser", "version": "1.16.2", "license": "MIT", "_id": "@types/body-parser@1.16.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/santialbo", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/vilic", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dreampulse", "name": "<PERSON>"}, {"url": "https://github.com/blendsdk/", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>"}], "dist": {"shasum": "4407d9924ffb91198103b9ece69bc1a86a3a0969", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.16.2.tgz", "integrity": "sha512-w6qAel0HQb3m3t+5MX1dWSqdGQJbiKzAGpYBupOt5+usSUnUo1za4ZLB8lWIkbtgpRxxyU4Kc5r5G3E5CWTZEw==", "signatures": [{"sig": "MEUCIQDx39pFinZYCAu1rY2rfvDQKpRFdVtug49hZX1Ax6DzTgIgRs/q5P1gdY+TAxjjyPIr77oeK+pJrm3m5QP/jh2tepE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/node": "*", "@types/express": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/body-parser-1.16.2.tgz_1490647458865_0.9632469709031284", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "75757040ce619586a5813699a20df5cf3b2806639300bd7127d1e7058499c338"}, "1.16.3": {"name": "@types/body-parser", "version": "1.16.3", "license": "MIT", "_id": "@types/body-parser@1.16.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/santialbo", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/vilic", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dreampulse", "name": "<PERSON>"}, {"url": "https://github.com/blendsdk/", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>"}], "dist": {"shasum": "bc2b9a181f2fa85c80f1ecacd8a05cf1414b85a3", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.16.3.tgz", "integrity": "sha512-oGcv1n/yXPGWpK939vn0cjQLX3HNytZKEw7Col5b+Oked5aC4TwbcFeZho/5yi70P7riDfTaN6mWrF47SU6Crg==", "signatures": [{"sig": "MEUCIQDja/1gRVzFrRhkmKR0LsNFL7yopbxnT3S25+kMvcFLjwIgWPjjU7dcrM2Bz16Np6KWJUBLdD/6ciRhMukycC/6b+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/node": "*", "@types/express": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/body-parser-1.16.3.tgz_1490983550854_0.5218458729796112", "host": "packages-18-east.internal.npmjs.com"}, "typesPublisherContentHash": "0e2409e65fa444d842ada496308241f92e613550dd08dc901ae91b79abb9b775"}, "1.16.4": {"name": "@types/body-parser", "version": "1.16.4", "license": "MIT", "_id": "@types/body-parser@1.16.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/santialbo", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/vilic", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dreampulse", "name": "<PERSON>"}, {"url": "https://github.com/blendsdk/", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>"}], "dist": {"shasum": "96f3660e6f88a677fee7250f5a5e6d6bda3c76bb", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.16.4.tgz", "integrity": "sha512-y8GxleWZ4ep0GG9IFMg+HpZWqLPjAjqc65cAopXPAWONWGCWGT0FCPVlXbUEBOPWpYtFrvlp2D7EJJnrqLUnEQ==", "signatures": [{"sig": "MEYCIQDOhdjkoCDcvoL3iTldMfBYpJ8Wda9e/pUgH//a2G8IGAIhAOngds9Z4rqT+ntQTSO/L7UqdNo0Z9/Z/SLi2uto/ck/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/node": "*", "@types/express": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/body-parser-1.16.4.tgz_1498226611300_0.12099443422630429", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "755db570229e5e665ddcb42c97841ade8112051e1a2770b4a59f70423f357270"}, "1.16.5": {"name": "@types/body-parser", "version": "1.16.5", "license": "MIT", "_id": "@types/body-parser@1.16.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/santialbo", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/vilic", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dreampulse", "name": "<PERSON>"}, {"url": "https://github.com/blendsdk", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>"}], "dist": {"shasum": "d2b7daefab84e0afa9d3fae0935bc7355b6320af", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.16.5.tgz", "integrity": "sha512-iGYVwwFznpBzqwS3QOoaop3mEbW8vAuqnPN2/pi2/HTREHeFTT+xX4hp5OImpQ7q4bE3a96JsDgBUccsx0fL/A==", "signatures": [{"sig": "MEYCIQDUhW4BmfExtjuo7+IpQ9l2qPpSpix5W0HvxgY/KOs//AIhAKYGIJzxI/RWOGWrHvc9LgPLT5ljJSzSgdjIc1PyqJC8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/node": "*", "@types/express": "*"}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/body-parser-1.16.5.tgz_1503352101826_0.5432878108695149", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "034f77df9661e0a62b84d87d05ac4b049391f8d1594824e9c72600a9278e7f07"}, "1.16.6": {"name": "@types/body-parser", "version": "1.16.6", "license": "MIT", "_id": "@types/body-parser@1.16.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/santialbo", "name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>"}, {"url": "https://github.com/vilic", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "vilic"}, {"url": "https://github.com/dreampulse", "name": "<PERSON>", "githubUsername": "dreampulse"}, {"url": "https://github.com/blendsdk", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "blendsdk"}, {"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "ccba8895337444e07f1134dab696edaf969b09a9", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.16.6.tgz", "integrity": "sha512-B7sY963pBsP46QG/yTX8ei2R8zIUDQUOhPZjTBWBGQXcBMLGFKHc0R8DWkAmouGmval5sDSuQA3AB2C9VmHw6A==", "signatures": [{"sig": "MEQCIGyXp7yuPpGkblWtPyYBs8xf7b2zjbTmbO9HteVIsC2IAiAn3DDUYJ/zPCsONlHg6veX3ZKOQoClRTK5e1wg6XG6Kg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/node": "*", "@types/express": "*"}, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/body-parser-1.16.6.tgz_1508890827196_0.24838930764235556", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "79f4fdb1d76ff40c5000da731ed292c038824b7476d8024f9b6675cc1c801efe"}, "1.16.7": {"name": "@types/body-parser", "version": "1.16.7", "license": "MIT", "_id": "@types/body-parser@1.16.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/santialbo", "name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>"}, {"url": "https://github.com/vilic", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "vilic"}, {"url": "https://github.com/dreampulse", "name": "<PERSON>", "githubUsername": "dreampulse"}, {"url": "https://github.com/blendsdk", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "blendsdk"}, {"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "455fc23fd0ddaaeda6cd6cbb653558276e5920fa", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.16.7.tgz", "integrity": "sha512-Obn1/GG0sYsnlAlhhSR1hvYRGBpQT+fzSi2IlGN8emCE4iu6f6xIjaq499B1sa7N9iBLzxyOUBo5bzgJd16BvA==", "signatures": [{"sig": "MEUCIQCk+tA/Rrk2dTmMEh7sfSy3Cxkm++5WtxUY9dzymGZ0nwIgLbsqmBYreSLKEzt7p8L1a/TOkEHQqmZes7RqBRyZ8yM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/node": "*", "@types/express": "*"}, "typeScriptVersion": "2.1", "_npmOperationalInternal": {"tmp": "tmp/body-parser-1.16.7.tgz_1509046327120_0.83074064925313", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "685be3affb0971241f67685efb98fee2d726b870651b4608590c8fa4a89d7d40"}, "1.16.8": {"name": "@types/body-parser", "version": "1.16.8", "license": "MIT", "_id": "@types/body-parser@1.16.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/santialbo", "name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>"}, {"url": "https://github.com/vilic", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "vilic"}, {"url": "https://github.com/dreampulse", "name": "<PERSON>", "githubUsername": "dreampulse"}, {"url": "https://github.com/blendsdk", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "blendsdk"}, {"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "687ec34140624a3bec2b1a8ea9268478ae8f3be3", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.16.8.tgz", "integrity": "sha512-BdN2PXxOFnTXFcyONPW6t0fHjz2fvRZHVMFpaS0wYr+Y8fWEaNOs4V8LEu/fpzQlMx+ahdndgTaGTwPC+J/EeA==", "signatures": [{"sig": "MEYCIQDzRckbNXY924n8RcR02uedfehwTAU6i+DrLzmfoTdnrAIhALibcD7EDTBTKpYuQy7qYaYEcZYQgl4PbVdEM45VSB4G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/node": "*", "@types/express": "*"}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/body-parser-1.16.8.tgz_1510179404744_0.4493043173570186", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e6976d18d37cd52405268dffcc70ad439e26bfb564395f871130687a24d073fa"}, "1.17.0": {"name": "@types/body-parser", "version": "1.17.0", "license": "MIT", "_id": "@types/body-parser@1.17.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/santialbo", "name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>"}, {"url": "https://github.com/vilic", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "vilic"}, {"url": "https://github.com/dreampulse", "name": "<PERSON>", "githubUsername": "dreampulse"}, {"url": "https://github.com/blendsdk", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "blendsdk"}, {"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jwalton", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "9f5c9d9bd04bb54be32d5eb9fc0d8c974e6cf58c", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.17.0.tgz", "fileCount": 4, "integrity": "sha512-a2+YeUjPkztKJu5aIF2yArYFQQp8d51wZ7DavSHjFuY1mqVgidGyzEQ41JIVNy82fXj8yPgy2vJmfIywgESW6w==", "signatures": [{"sig": "MEQCIHzITmmfovhZoI7zrA7gSVnWPblH+3rE124bVoVU1kfhAiBWy1Gs3zsKltP39Ljy3q078Qf8KtYvSLT4HFik68mH+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa38sHCRA9TVsSAnZWagAAGEwP/j7HiA2irSy4FT3tPCNi\n3kwu9AWIYcpWl7lsd082L+EWqZSh06abckC3NRoVd3PAPQwnsHBEd6+BfZfA\nxYMNoGRm7gp0u4dROSLUG31HbWjFMU97CxbgeATnLAgWGOQKsSyWfXnmc5eZ\nOitJYJlQAdKMaqo2LPgZ7GWGst3m19qSv6i8fuVW/A8CSGmMcds86WHHfx/F\nzF4fmlcj27FpjuF3PfMLMtmGB+kXWMp3rZ7LtRvcCkQRSrqeVXFRzbXth/UH\nBG0CqIiqgnujU2wdgaMZgLEXY2CXjx2rElN9oliJtBGLpsx4Dgr6t5qtPoNs\nyYd1zhcFwWxu+HDUgOdCgn0o6lDmrbA5b/cFvepqTtm07k9Ob7ejk87lCL1/\ngXb+tGtCLniMgsMOItPp3iCm+O6FyGLU3+0kxr6+Hx82H2k4lNnYbI03xdur\nOEPBsH3FFno5efS/PNUCgdCSL3OMlWCsKDj1EHzNxBVH/E76qhHFJoaZMVQK\nEG/7VJ72cGLh2Z3FlCEDTHfcq8QJ++dJvug4OAukCLqaBqGa0px9uBeMnadx\nvqD5A/UyywflkE1TgEoovMxFrwtoj55JNP3RYO2fsPOqlW+yIN3Z8GYQ3h2b\nmFMVf4hTFiLufR0mT3Y6smuZTn0SHJbYH0D+4PVfpXwU1BHNKiFmJzC4BExO\n2RKb\r\n=sBES\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/node": "*", "@types/connect": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/body-parser_1.17.0_1524615942402_0.813020497406129", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d50d69303022e9f76f6d905e480a7dc98120bbcedb696a9722a4a2e9f08473e6"}, "1.17.1": {"name": "@types/body-parser", "version": "1.17.1", "license": "MIT", "_id": "@types/body-parser@1.17.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/santialbo", "name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>"}, {"url": "https://github.com/vilic", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "vilic"}, {"url": "https://github.com/dreampulse", "name": "<PERSON>", "githubUsername": "dreampulse"}, {"url": "https://github.com/blendsdk", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "blendsdk"}, {"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jwalton", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "18fcf61768fb5c30ccc508c21d6fd2e8b3bf7897", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.17.1.tgz", "fileCount": 4, "integrity": "sha512-RoX2EZjMiFMjZh9lmYrwgoP9RTpAjSHiJxdp4oidAQVO02T7HER3xj9UKue5534ULWeqVEkujhWcyvUce+d68w==", "signatures": [{"sig": "MEUCIQCf88dxzqAMCGwMqGhVKUbMl5vqv327g/Yd5pH+RMReawIgTOI3Vq1+e37qQlGuOHTjrmfSA/gH/IXzat3id+OFNDc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdWfK9CRA9TVsSAnZWagAAIjkP/j1Wm5ILdCYW663zPueI\n4jAR+mS/HjBo3+6H/MIIJQwenhNUxoaYQctret5RyhKrTpH8nCaEXiwR5l1e\njb7FJNVzqlG0Z+Wdas1Giym7kg651CZHQrWX0RNkjDe7aU3iHZWvstssC6HU\nh/lENWO7Q6HQL3bFSFjppAK6Bkk5iqMz6PjTYksIzO9cDdorwjDwBFMi4CFM\nvTeY4aveJ5axXL2EXXQP/dVmLrFdHZ1oLpAHmO9wzpy1MrN98S8I794nAHg1\n8siNMgE5iaJ75ciG97V/6FvgNLKDvQv4ryRkGSPrqUZQ+GLzLOswsbKhjsP0\n4uUfodkNwH0NZ8aOCI3oPjHkEDXZ64eCTqwNlaqmm3KZFXVoro237dBgmLBi\nLuqoUnCI+JZ72CMK7zAN0WWnceT0d6HPStalXGPyBJhLqnicCk45DOYctDSp\ndBdTYaNVC/jCprLvK8yPfv4eeUI9VyxeBNvF8Cirum4k2MkS+vnLLcQ9YkFe\nhyhrJ2eXtAbFd7czoGrVLpSIEYOHBw1OahrpFGbDpUi+/2BZ7ZS9biruu0TX\nVC+L1+VC+dbFfjFLF/xNpNYzAiTJfxmAK84CKyGidIw1/aAhJnxBBtylYAwn\n0Q+L+OSQa+KfB2/cAybTNL+oUF4Sdsc7/sdRoFEP+D5fIXdvZCy5jmyTFbn8\nKzgZ\r\n=bwlX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/body-parser"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/node": "*", "@types/connect": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/body-parser_1.17.1_1566175932959_0.7340959426827929", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ada1b55777df6de5327f420d23285a5c476895faa88cacf6b80a1a791eef0f67"}, "1.19.0": {"name": "@types/body-parser", "version": "1.19.0", "license": "MIT", "_id": "@types/body-parser@1.19.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/santialbo", "name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>"}, {"url": "https://github.com/vilic", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "vilic"}, {"url": "https://github.com/dreampulse", "name": "<PERSON>", "githubUsername": "dreampulse"}, {"url": "https://github.com/blendsdk", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "blendsdk"}, {"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jwalton", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "0685b3c47eb3006ffed117cdd55164b61f80538f", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.0.tgz", "fileCount": 4, "integrity": "sha512-W98JrE0j2K78swW4ukqMleo8R7h/pFETjM2DQ90MF6XK2i4LO4W3gQ71Lt4w3bfm2EvVSyWHplECvB5sK22yFQ==", "signatures": [{"sig": "MEUCIQCC3XB8vgje9+4+5LN4cgEvUv3hWLxjuJO47qa2iXhSUAIgeHTtEEmxcvpk5exaHMY687kNcBHSFulUDH3leVkklM8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8010, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQcjaCRA9TVsSAnZWagAA134P+QHWya3kwBw+m/zvAdiH\nQLLK49yEK0tnLL2L9MZFWU5V9GyMp6XJctU3836AHvYg2U8E+OSvubrNDTHf\nMfaq77NUJczrFBiK1pIR1RIEZHzriyvbHcf+9wP5IR1uCmuBZXtOzW8Ebj7t\nbYpXFkzDEaSqJ+irQIAPS1sD1usdCX4l1f/yCGi4XCKYpzWvgQP85ZnQA2rJ\nH7xCSFBhA8a/74V1dhaRM80l4EqeT9lbAmsDiJLSLhSdZbyGk10jC7F4bh7A\n4KKiyRrLhjYWjd+XCMNfoXu92UMXGIN9h+hgAxG2U2jxll0gikO2IgOyDLfZ\nKBGoLHznsu+K4wZJSZ+lZRNKW2OYl/F0W1zMn36WCw03WK8eqY4KnWU7+qt0\nM/NbYnKYbf3ibiusRFz5kpk9aWE0U4i+D0FsFRKlt7FazPW8BGShK6dyDB1h\ncXMOQtCcOR8A5qm82lLzxcT6C7S9MUuP2g3ZEwIFlEuNg4Ieu7DAOJ5dS+5w\nMNrcsMMacEvzaZkdw7NkfVp+aoAVSSVjnI7PxjisMnEG74SMu+A/3ehDCP4/\n5sq7jMUb83pkwZIsV802O5NIuYNpIUnt1tKl3npwhHeOUVUACf8kCUQwGNHp\n0YxydbgTj3HI9CqEmQdQUe2VntLllOcpy6ovv1Ee+96ZBnw32PxzJEgGeTBw\ns2Tz\r\n=efYM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/body-parser"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/node": "*", "@types/connect": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/body-parser_1.19.0_1581369561921_0.5814052373335026", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "4257cff3580f6064eb283c690c28aa3a5347cd3cae2a2e208b8f23c61705724a"}, "1.19.1": {"name": "@types/body-parser", "version": "1.19.1", "license": "MIT", "_id": "@types/body-parser@1.19.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/santialbo", "name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>"}, {"url": "https://github.com/vilic", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "vilic"}, {"url": "https://github.com/dreampulse", "name": "<PERSON>", "githubUsername": "dreampulse"}, {"url": "https://github.com/blendsdk", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "blendsdk"}, {"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jwalton", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/body-parser", "dist": {"shasum": "0c0174c42a7d017b818303d4b5d969cb0b75929c", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.1.tgz", "fileCount": 4, "integrity": "sha512-a6bTJ21vFOGIkwM0kzh9Yr89ziVxq4vYH2fQ6N8AeipEzai/cFK6aGMArIkUeIdRIgpwQa+2bXiLuUJCpSf2Cg==", "signatures": [{"sig": "MEYCIQDundEa/Y+lTTOjxHxa3v3mwzYPe3IMjiL2RBJi9GVqtgIhALPr6ieGi9O3+w5KY9nrVVuUcUWqDRtldv/pRAK/STS1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8159, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5J56CRA9TVsSAnZWagAA4KQQAJ9sVhem1z9QdDoQWvGJ\ntS3Z2BaJ/ico00dgHtEojayoRuF5gL18zIT9z7/O0OTK9kL8nWGgayNKFiL5\nJx3qGtyguxh5R7Xo2xftpe7pxMdP3b4o8pWBuxozrVYtevXdavfTCOxfw/D3\nCH9bVBbymyIxqBvqCk1ojdoqVdTm4LKygOEw7J0R5W1T8NALwDvJzBKyUils\nnF0/9ga6Upxdr6pmBbQ+OoU0GaJVDHHAOOY6gAtL5v3sWMS7/Y3z43zilVLp\nrcJrgBQMtVyQpJgW5/Lm/kGzh7byAdvMwRJ1zZwGWPrPt3TsrOEbwU+7iWmB\nOA40Q795w6v3jp7Sw2YD8GFoXbcXg+eb7QoGTQF2FDgYxqHf+Xnsgpz5jzPi\nkuA8xdZlRV+bkcHkcMa69qHMjOqZptsaK19Xw0Jpqg7cqtXDvs/9iL9oOiuz\nA2woy41QnEu0oa9oAH49q4PAcKDPb4h2ohCpiGSqgkZp766m0CZDLV2rBzOV\nw4WoyIGREMkM7bryBKzt5bLNFvgb7IUJoDQV2tH8M700m+0aMl4OS6/DVy8A\nnNOFfH0HKu7QotM+/x16qiZ3WqS9bSbtl0XXmEyr1dP0aMZ9JoB14O0W8g6l\nqR3UH6Rai8F7Kah+0Yu/dy7UW+cncW07gD9M6rrQaJeha8jXo0vjLvpP5F6t\n004B\r\n=GaK0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/body-parser"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/node": "*", "@types/connect": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/body-parser_1.19.1_1625595513693_0.649623834421087", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "390273f9d955dbe97cc87b88bf69634aeb1b9f82e6eafa68118b286638113100"}, "1.19.2": {"name": "@types/body-parser", "version": "1.19.2", "license": "MIT", "_id": "@types/body-parser@1.19.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/santialbo", "name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>"}, {"url": "https://github.com/vilic", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "vilic"}, {"url": "https://github.com/dreampulse", "name": "<PERSON>", "githubUsername": "dreampulse"}, {"url": "https://github.com/blendsdk", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "blendsdk"}, {"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jwalton", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/body-parser", "dist": {"shasum": "aea2059e28b7658639081347ac4fab3de166e6f0", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.2.tgz", "fileCount": 4, "integrity": "sha512-<PERSON><PERSON>one6pm6QmwZoAgeyNksccT9Q4AWZQ6PvfwR37GT6r6FWUPguq6sUmNGSMV2Wr761oQoBxwGGa6DR5o1DC9g==", "signatures": [{"sig": "MEQCIG8OX14hGsrCIZ/VpOBbO9MbvdtwaL/INVZWgfjwwHvhAiASBSjqpgVETJM6KAZIDE9EVKo5cLM2ZiKvJMyT0Qwwag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhk/kjCRA9TVsSAnZWagAAoc4P/iEZ+r6N6rt6svwKeXFa\n81cH7ueb6hFsCVzJYujZcgmlE1dQP7wPw8VJSTHKmKgwXjr9NQGDg60UhLAu\nifgjlvWExG/CIrr/XxvE5oOdhNNsN75B0znVQkDO5rKlfz1lF2sPAYLnT/xq\nCd2oBcFmqzouYSSUkmPJOZTIWu+jUXJKdTxsA3REEWrqul3KFnu2eRjoN3zF\nV8Ou0Ztbf1M7qah7WBUGCQj/dsRWZiQYO4kgiKOoUl1IauVqd4h4KVmIQLqX\nSTNOUz6as4htUMsS2+CYLP5CpUVkct/Owd/6f+49iUzhSk6fjWPj6cJEWz6h\nt3BGr8X+ZecoHFVSJu47KUlQjDD86FKWgN40Xq1v8q7U96/hxQJlNhNSmOgy\nSktDkn0dMlAWJExHBg46uV2AQdxpVUbyTrLY57Jq0OFwEiWiucQ+7eGvYDCD\nx63u4BcNaur2614XkXtTTRje0BBWXIjrKCK0tY3GNmngIuKm2jVo+OAf/3q4\nndAmjxImvbYC5Bn26eqUNSPsBPfXDEXEznOq85ozsJWt8exNeAzj3qJmP+I4\n2Bvsp5s1leLMdo7ISGREh4rvRLyOte5Bii2mIyVEKdBpzuHerLQ9u7wxT2TZ\nC96Z1PtqgXEwvAWSPsJl+q791cunX3tmN1LsVCPjE5/AbbMBF6tqEbqFcnJs\niHTM\r\n=T+h9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/body-parser"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/node": "*", "@types/connect": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.7", "_npmOperationalInternal": {"tmp": "tmp/body-parser_1.19.2_1637087522894_0.5463877190721698", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ad069aa8b9e8a95f66df025de11975c773540e4071000abdb7db565579b013ee"}, "1.19.3": {"name": "@types/body-parser", "version": "1.19.3", "license": "MIT", "_id": "@types/body-parser@1.19.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/santialbo", "name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>"}, {"url": "https://github.com/vilic", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "vilic"}, {"url": "https://github.com/dreampulse", "name": "<PERSON>", "githubUsername": "dreampulse"}, {"url": "https://github.com/blendsdk", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "blendsdk"}, {"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jwalton", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/body-parser", "dist": {"shasum": "fb558014374f7d9e56c8f34bab2042a3a07d25cd", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.3.tgz", "fileCount": 5, "integrity": "sha512-oyl4jvAfTGX9Bt6Or4H9ni1Z447/tQuxnZsytsCaExKlmJiU8sFgnIBRzJUpKwB5eWn9HuBYlUlVA74q/yN0eQ==", "signatures": [{"sig": "MEYCIQDGf89xK3sFctFQcJ2TuCDQKrEcUKt+9lkOvf4P1iIXRAIhAMubk4mzT94bmus2NWPgV3eXjWzRTPhb4kaHmA6SIGG0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8287}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/body-parser"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/node": "*", "@types/connect": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/body-parser_1.19.3_1694805983061_0.06014666590405793", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "cedd53aab61f577f8f08f95fe8c5383d15a86b804a57641c40f76cc286c94abd"}, "1.19.4": {"name": "@types/body-parser", "version": "1.19.4", "license": "MIT", "_id": "@types/body-parser@1.19.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/santialbo", "name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>"}, {"url": "https://github.com/vilic", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "vilic"}, {"url": "https://github.com/dreampulse", "name": "<PERSON>", "githubUsername": "dreampulse"}, {"url": "https://github.com/blendsdk", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "blendsdk"}, {"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jwalton", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/body-parser", "dist": {"shasum": "78ad68f1f79eb851aa3634db0c7f57f6f601b462", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.4.tgz", "fileCount": 5, "integrity": "sha512-N7UDG0/xiPQa2D/XrVJXjkWbpqHCd2sBaB32ggRF2l83RhPfamgKGF8gwwqyksS95qUS5ZYF9aF+lLPRlwI2UA==", "signatures": [{"sig": "MEUCIC14zq5iHi2Mf52cA0UUcO7jgTDwwurNF0hewLgzcT/+AiEA8bFc7Fxfv5/nixuHbGSe7lpcnvyuXoRDVzdOgYTHIlA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7648}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/body-parser"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/node": "*", "@types/connect": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/body-parser_1.19.4_1697584513186_0.31323221842798565", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0395d4670e350788389bc2df16daa1fe4622daad11863ee7b0af4e4eea120c33"}, "1.19.5": {"name": "@types/body-parser", "version": "1.19.5", "license": "MIT", "_id": "@types/body-parser@1.19.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/santialbo", "name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>"}, {"url": "https://github.com/vilic", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "vilic"}, {"url": "https://github.com/dreampulse", "name": "<PERSON>", "githubUsername": "dreampulse"}, {"url": "https://github.com/blendsdk", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "blendsdk"}, {"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jwalton", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/body-parser", "dist": {"shasum": "04ce9a3b677dc8bd681a17da1ab9835dc9d3ede4", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.5.tgz", "fileCount": 5, "integrity": "sha512-fB3Zu92ucau0iQ0JMCFQE7b/dv8Ot07NI3KaZIkIUNXq82k4eBAqUaneXfleGY9JWskeS9y+u0nXMyspcuQrCg==", "signatures": [{"sig": "MEYCIQD0kl57j3IP702YxT4gCELkprSQLjMYd4GpZrgorT8fTQIhAPa00TB+i2mI6J0nHGgaBruLbwktcwYkYlhN5qrwkxyi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7648}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/body-parser"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/node": "*", "@types/connect": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/body-parser_1.19.5_1699315194519_0.8355482891149741", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7be737b78c8aabd5436be840558b283182b44c3cf9da24fb1f2ff8f414db5802"}, "1.19.6": {"name": "@types/body-parser", "version": "1.19.6", "license": "MIT", "_id": "@types/body-parser@1.19.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/santialbo", "name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>"}, {"url": "https://github.com/vilic", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "vilic"}, {"url": "https://github.com/dreampulse", "name": "<PERSON>", "githubUsername": "dreampulse"}, {"url": "https://github.com/blendsdk", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "blendsdk"}, {"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jwalton", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/body-parser", "dist": {"shasum": "1859bebb8fd7dac9918a45d54c1971ab8b5af474", "tarball": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.6.tgz", "fileCount": 5, "integrity": "sha512-HLFeCYgz89uk22N5Qg3dvGvsv46B8GLvKKo1zKG4NybA8U2DiEO3w9lqGg29t/tfLRJpJ6iQxnVw4OnB7MoM9g==", "signatures": [{"sig": "MEQCIFomBJIRNmJtSRk0VltC0p7f97tK5280BRg2KenjxDg0AiBEvrjZze4PC0l569/oh4EvtuXgLIuaDCVzFYLdG3nGWg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7888}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/body-parser"}, "description": "TypeScript definitions for body-parser", "directories": {}, "dependencies": {"@types/node": "*", "@types/connect": "*"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/body-parser_1.19.6_1749262526187_0.589670325646108", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "d788c843f427d6ca19640ee90eb433324a18f23aed05402a82c4e47e6d60b29d"}}, "time": {"created": "2016-05-17T04:32:46.011Z", "modified": "2025-06-07T02:15:33.731Z", "0.0.16-alpha": "2016-05-17T04:32:46.011Z", "0.0.17-alpha": "2016-05-19T20:22:56.235Z", "0.0.22-alpha": "2016-05-20T19:14:49.578Z", "0.0.23-alpha": "2016-05-25T04:33:22.442Z", "0.0.24-alpha": "2016-07-01T19:00:32.613Z", "0.0.25-alpha": "2016-07-01T22:15:40.751Z", "0.0.26-alpha": "2016-07-02T02:01:39.254Z", "0.0.27-alpha": "2016-07-03T23:24:21.998Z", "0.0.28-alpha": "2016-07-07T18:58:29.811Z", "0.0.29": "2016-07-14T14:10:06.004Z", "0.0.30": "2016-08-02T15:47:49.497Z", "0.0.31": "2016-08-19T15:21:16.206Z", "0.0.32": "2016-08-25T18:35:40.116Z", "0.0.33": "2016-09-19T16:23:19.936Z", "0.0.34": "2017-02-17T20:22:32.941Z", "1.16.0": "2017-03-09T00:20:28.666Z", "1.16.1": "2017-03-21T20:19:52.344Z", "1.16.2": "2017-03-27T20:44:19.086Z", "1.16.3": "2017-03-31T18:05:52.475Z", "1.16.4": "2017-06-23T14:03:31.416Z", "1.16.5": "2017-08-21T21:48:21.904Z", "1.16.6": "2017-10-25T00:20:27.259Z", "1.16.7": "2017-10-26T19:32:07.183Z", "1.16.8": "2017-11-08T22:16:44.827Z", "1.17.0": "2018-04-25T00:25:42.476Z", "1.17.1": "2019-08-19T00:52:13.054Z", "1.19.0": "2020-02-10T21:19:22.046Z", "1.19.1": "2021-07-06T18:18:33.825Z", "1.19.2": "2021-11-16T18:32:03.015Z", "1.19.3": "2023-09-15T19:26:23.273Z", "1.19.4": "2023-10-17T23:15:13.478Z", "1.19.5": "2023-11-06T23:59:54.691Z", "1.19.6": "2025-06-07T02:15:26.399Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/body-parser", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/body-parser"}, "description": "TypeScript definitions for body-parser", "contributors": [{"url": "https://github.com/santialbo", "name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>"}, {"url": "https://github.com/vilic", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "vilic"}, {"url": "https://github.com/dreampulse", "name": "<PERSON>", "githubUsername": "dreampulse"}, {"url": "https://github.com/blendsdk", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "blendsdk"}, {"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/jwalton", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": "", "users": {"crismvp3200": true}}