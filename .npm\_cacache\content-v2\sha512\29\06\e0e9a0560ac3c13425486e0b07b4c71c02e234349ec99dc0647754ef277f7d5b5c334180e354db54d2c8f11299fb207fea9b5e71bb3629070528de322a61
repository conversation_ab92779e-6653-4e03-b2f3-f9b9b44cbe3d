{"_id": "dedent", "_rev": "47-8efbf37d3c59c092cc9d2b1db52b6dd8", "name": "dedent", "dist-tags": {"beta": "1.0.0", "latest": "1.6.0"}, "versions": {"0.1.0": {"name": "dedent", "version": "0.1.0", "keywords": ["dedent", "tag", "multi-line string", "es6"], "author": {"url": "http://desmondbrand.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "dedent@0.1.0", "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "dist": {"shasum": "ec05bd5b7b3e2f3ef80f9f5ea70d316127f480e5", "tarball": "https://registry.npmjs.org/dedent/-/dedent-0.1.0.tgz", "integrity": "sha512-TbYwoa9cLKTl5dSgy21pLwnpvwyS3n7/DPSOj6F0Xi0+s7oxGGRALnVfNdbX7AEHZzJA+e2Cja5NOUZ3EScMGA==", "signatures": [{"sig": "MEUCIQCVecEQpWmneLRXcLKHBzJ9HRLUlmWzYZKecl/BN2RzXQIgXejnXeYd9958p3L10UTboG7txzbtpI8kFW4aXSHq3PM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"scriptPreprocessor": "<rootDir>/node_modules/6to5-jest", "testFileExtensions": ["js"], "moduleFileExtensions": ["js", "json"]}, "main": "dedent.js", "_from": ".", "files": ["dedent.js", "LICENSE"], "_shasum": "ec05bd5b7b3e2f3ef80f9f5ea70d316127f480e5", "gitHead": "fdc08488c39f3e492272dbaea34b8efd9cf31a50", "scripts": {"test": "jest"}, "_npmUser": {"name": "dmnd", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/dmnd/dedent.git", "type": "git"}, "_npmVersion": "2.1.11", "description": "An ES6 string tag that strips indentation from multi-line strings", "directories": {}, "_nodeVersion": "0.10.33", "devDependencies": {"6to5": "^2.11.3", "jest-cli": "^0.2.1", "6to5-jest": "^1.0.0"}}, "0.1.1": {"name": "dedent", "version": "0.1.1", "keywords": ["dedent", "tag", "multi-line string", "es6"], "author": {"url": "http://desmondbrand.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "dedent@0.1.1", "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "dist": {"shasum": "6c0e87ae6f438b2f451dc303a0f10c7a7855bfd4", "tarball": "https://registry.npmjs.org/dedent/-/dedent-0.1.1.tgz", "integrity": "sha512-g1bsSyzqlbO8MR9WzbSZaVjnjvOVvvIr0Sv+7JIegI4eHyMJU3XhR5BZCxY6fbEGa+ixAc2VoPVvAenR2wT/sg==", "signatures": [{"sig": "MEUCIDBshBRhA0SiB0PRo3eGz9RkyjYfbeAgIc473KrXzQ6jAiEAy0PIfsbta1Wx3uaKOfLEtLdvLILA3j93J7ctUwgE2qo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"scriptPreprocessor": "<rootDir>/node_modules/6to5-jest", "testFileExtensions": ["js"], "moduleFileExtensions": ["js", "json"]}, "main": "dedent.js", "_from": ".", "files": ["dedent.js", "LICENSE"], "_shasum": "6c0e87ae6f438b2f451dc303a0f10c7a7855bfd4", "gitHead": "a900d60af05c223961d6feceba9d5a7cce5915da", "scripts": {"test": "jest"}, "_npmUser": {"name": "dmnd", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/dmnd/dedent.git", "type": "git"}, "_npmVersion": "2.1.11", "description": "An ES6 string tag that strips indentation from multi-line strings", "directories": {}, "_nodeVersion": "0.10.33", "devDependencies": {"6to5": "^2.11.3", "jest-cli": "^0.2.1", "6to5-jest": "^1.0.0"}}, "0.2.0": {"name": "dedent", "version": "0.2.0", "keywords": ["dedent", "tag", "multi-line string", "es6"], "author": {"url": "http://desmondbrand.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "dedent@0.2.0", "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "dist": {"shasum": "6fbe07286af2df5cb8852b4372aab6a405e4b0ab", "tarball": "https://registry.npmjs.org/dedent/-/dedent-0.2.0.tgz", "integrity": "sha512-dhyKKY815eNIk34IatV0edwv6OMef8jfuQJ7Y25zsHxfMQFKXeoG2saFmzzjkFjDrTcYCwdB1MYAUa2tBDKLyw==", "signatures": [{"sig": "MEYCIQC2wsHc/9SkF0ZxnW7Sua5c7oqpSudVkj4conlZlaMfQAIhAMyFrBe0/59cs4OB/TVu7FbSOOFLTp6acVgqVGobrQVA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"scriptPreprocessor": "<rootDir>/node_modules/6to5-jest", "testFileExtensions": ["js"], "moduleFileExtensions": ["js", "json"]}, "main": "dedent.js", "_from": ".", "files": ["dedent.js", "LICENSE"], "_shasum": "6fbe07286af2df5cb8852b4372aab6a405e4b0ab", "gitHead": "dce8eb87c1c81340d288ff6816afc62d83e1f276", "scripts": {"test": "jest"}, "_npmUser": {"name": "dmnd", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/dmnd/dedent.git", "type": "git"}, "_npmVersion": "2.1.11", "description": "An ES6 string tag that strips indentation from multi-line strings", "directories": {}, "_nodeVersion": "0.10.33", "devDependencies": {"6to5": "^2.11.3", "jest-cli": "^0.2.1", "6to5-jest": "^1.0.0"}}, "0.2.1": {"name": "dedent", "version": "0.2.1", "keywords": ["dedent", "tag", "multi-line string", "es6"], "author": {"url": "http://desmondbrand.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "dedent@0.2.1", "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "dist": {"shasum": "72460b8511b2f3c74f55c385a5c8d4a94bf467a3", "tarball": "https://registry.npmjs.org/dedent/-/dedent-0.2.1.tgz", "integrity": "sha512-lViHSWXs5EacFeYoCqjGYsKrYxHToO+gAgpPC/L3AwI5B7ayRCLwPCnZ5b8Tww7tx1uCVgQNYx5xSBDKSM7hcg==", "signatures": [{"sig": "MEQCIENhyVWib1TnnonYkX382bmJ1jKl4kEdJo9TanwWW1ZfAiBaosmKVLh2MP4fhdiM3xESiqs0yyR3/xQGo5Yj6y59Zw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"scriptPreprocessor": "<rootDir>/node_modules/6to5-jest", "testFileExtensions": ["js"], "moduleFileExtensions": ["js", "json"]}, "main": "dedent.js", "_from": ".", "files": ["dedent.js", "LICENSE"], "_shasum": "72460b8511b2f3c74f55c385a5c8d4a94bf467a3", "gitHead": "ba2990cc8ab5e8e9e971e5cdf08aa8a96b33666f", "scripts": {"test": "jest"}, "_npmUser": {"name": "dmnd", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/dmnd/dedent.git", "type": "git"}, "_npmVersion": "2.1.11", "description": "An ES6 string tag that strips indentation from multi-line strings", "directories": {}, "_nodeVersion": "0.10.33", "devDependencies": {"6to5": "^2.11.3", "jest-cli": "^0.2.1", "6to5-jest": "^1.0.0"}}, "0.2.2": {"name": "dedent", "version": "0.2.2", "keywords": ["dedent", "tag", "multi-line string", "es6"], "author": {"url": "http://desmondbrand.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "dedent@0.2.2", "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "dist": {"shasum": "3a43cb1b87a96b52cc54dc94670b6d4456bdd1c2", "tarball": "https://registry.npmjs.org/dedent/-/dedent-0.2.2.tgz", "integrity": "sha512-77f8vGDJkKn941uIm1pcxPLj7ldfXX5v/yIgw3j52n8WIsMzVcKyP376WzBQer86lYvVl4kw/yOOnYNiEPeFLQ==", "signatures": [{"sig": "MEUCIFuObwmU0mvumkarT9q/IUN7w0kyvI22b6q0qnJhExaEAiEA5DjU5hY3VnTyOifNDTKakfVdhEL+2FvrYYlqSHxDU28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"scriptPreprocessor": "<rootDir>/node_modules/6to5-jest", "testFileExtensions": ["js"], "moduleFileExtensions": ["js", "json"]}, "main": "dedent.js", "_from": ".", "files": ["dedent.js", "LICENSE"], "_shasum": "3a43cb1b87a96b52cc54dc94670b6d4456bdd1c2", "gitHead": "95a4e07329aac9ccf39161fc292b4533b6fb8e73", "scripts": {"test": "jest"}, "_npmUser": {"name": "dmnd", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/dmnd/dedent.git", "type": "git"}, "_npmVersion": "2.1.11", "description": "An ES6 string tag that strips indentation from multi-line strings", "directories": {}, "_nodeVersion": "0.10.33", "devDependencies": {"6to5": "^2.11.3", "jest-cli": "^0.2.1", "6to5-jest": "^1.0.0"}}, "0.3.0": {"name": "dedent", "version": "0.3.0", "keywords": ["dedent", "tag", "multi-line string", "es6"], "author": {"url": "http://desmondbrand.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "dedent@0.3.0", "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "dist": {"shasum": "98269203d45671a61dcc9e2d016a14255514f737", "tarball": "https://registry.npmjs.org/dedent/-/dedent-0.3.0.tgz", "integrity": "sha512-xTY3ZJHn2xYXC+YhAow4JApIApDORwIyMXDTp9GyJjoyUJ6QN06g1SKcCL1ZNKC03WIQusw2+ho9+F7TgtOPew==", "signatures": [{"sig": "MEUCIQDDF0D943shQ+c+24Z5vDKmcZEUzQ0LX9jCcoCeFDdqkgIgG/CKBR4KDPrSV+/FnXH4f19yOYfsoE3YcIe3YIxOR1Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"scriptPreprocessor": "<rootDir>/node_modules/6to5-jest", "testFileExtensions": ["js"], "moduleFileExtensions": ["js", "json"]}, "main": "dist/dedent.js", "_from": ".", "files": ["dist/dedent.js", "LICENSE"], "_shasum": "98269203d45671a61dcc9e2d016a14255514f737", "gitHead": "081e6108c4786bbbcb0107676f44ba8ce3679b1b", "scripts": {"test": "jest"}, "_npmUser": {"name": "dmnd", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/dmnd/dedent.git", "type": "git"}, "_npmVersion": "2.1.11", "description": "An ES6 string tag that strips indentation from multi-line strings", "directories": {}, "_nodeVersion": "0.10.33", "devDependencies": {"6to5": "^2.11.3", "gulp": "^3.8.10", "jest-cli": "^0.2.1", "6to5-jest": "^1.0.0", "gulp-6to5": "^2.0.2"}}, "0.4.0": {"name": "dedent", "version": "0.4.0", "keywords": ["dedent", "tag", "multi-line string", "es6"], "author": {"url": "http://desmondbrand.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "dedent@0.4.0", "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "dist": {"shasum": "87defd040bd4c1595d963282ec57f3c2a8525642", "tarball": "https://registry.npmjs.org/dedent/-/dedent-0.4.0.tgz", "integrity": "sha512-25DJIXD6mCqYHIqI3/aBfAvFgJSY9jIx397eUQSofXbWVR4lcB21a17qQ5Bswj0Zv+3Nf06zNCyfkGyvo0AqqQ==", "signatures": [{"sig": "MEQCIGXn4SOmoTaHsSI2BK2GpfV7XsHiSOry/tGexiu8gIGcAiBb9PjxnAYamBX14OmAPN10lpUlcEP8ppJZVQu5y+XdWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"scriptPreprocessor": "<rootDir>/node_modules/babel-jest", "testFileExtensions": ["js"], "moduleFileExtensions": ["js", "json"]}, "main": "dist/dedent.js", "_from": ".", "files": ["dist/dedent.js", "LICENSE"], "_shasum": "87defd040bd4c1595d963282ec57f3c2a8525642", "gitHead": "17cca4ab89a80985cacf5c264ff4a7804ff013b7", "scripts": {"lint": "eslint dedent.js __tests__", "test": "jest"}, "_npmUser": {"name": "dmnd", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/dmnd/dedent.git", "type": "git"}, "_npmVersion": "2.3.0", "description": "An ES6 string tag that strips indentation from multi-line strings", "directories": {}, "_nodeVersion": "0.10.36", "devDependencies": {"gulp": "^3.8.10", "eslint": "^0.18.0", "jest-cli": "^0.2.1", "babel-jest": "^5.0.1", "gulp-babel": "^5.1.0", "babel-eslint": "^2.0.2"}}, "0.6.0": {"name": "dedent", "version": "0.6.0", "keywords": ["dedent", "tag", "multi-line string", "es6"], "author": {"url": "http://desmondbrand.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "dedent@0.6.0", "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "dist": {"shasum": "0e6da8f0ce52838ef5cec5c8f9396b0c1b64a3cb", "tarball": "https://registry.npmjs.org/dedent/-/dedent-0.6.0.tgz", "integrity": "sha512-cSfRWjXJtZQeRuZGVvDrJroCR5V2UvBNUMHsPCdNYzuAG8b9V8aAy3KUcdQrGQPXs17Y+ojbPh1aOCplg9YR9g==", "signatures": [{"sig": "MEUCIE4piDdoe+QWsM2D8XWJwecvsgra68fEvDDIAEsdHn75AiEA/DgiuZ5+gcPRs/1I6/eR9mpYmtb8BuIcVMGqpVHcEr0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "jest": {"scriptPreprocessor": "<rootDir>/node_modules/babel-jest", "testFileExtensions": ["js"], "moduleFileExtensions": ["js", "json"]}, "main": "dist/dedent.js", "_from": ".", "files": ["dist/dedent.js", "LICENSE"], "_shasum": "0e6da8f0ce52838ef5cec5c8f9396b0c1b64a3cb", "gitHead": "e7d0bcea425ff647791a30cd9db0fb486c26078a", "scripts": {"lint": "eslint dedent.js __tests__", "test": "jest"}, "_npmUser": {"name": "dmnd", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/dmnd/dedent.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "An ES6 string tag that strips indentation from multi-line strings", "directories": {}, "_nodeVersion": "5.0.0", "devDependencies": {"gulp": "^3.8.10", "eslint": "^1.9.0", "jest-cli": "^0.7.1", "babel-jest": "^5.0.1", "gulp-babel": "^5.1.0"}}, "0.7.0": {"name": "dedent", "version": "0.7.0", "keywords": ["dedent", "tag", "multi-line string", "es6"], "author": {"url": "http://desmondbrand.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "dedent@0.7.0", "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "dist": {"shasum": "2495ddbaf6eb874abb0e1be9df22d2e5a544326c", "tarball": "https://registry.npmjs.org/dedent/-/dedent-0.7.0.tgz", "integrity": "sha512-Q6fKUPqnAHAyhiUgFU7BUzLiv0kd8saH9al7tnu5Q/okj6dnupxyTgFIBjVzJATdfIAm9NAsvXNzjaKa+bxVyA==", "signatures": [{"sig": "MEQCIEqYvcwuRk++bQG119EIYJLa2PVIZUdOuR0EQ3zk/EBVAiB/vGsuvme3qASOmbswdqNGfkrohwuTokOAf0vfCCm1OA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "dist/dedent.js", "_from": ".", "files": ["dist/dedent.js", "LICENSE"], "_shasum": "2495ddbaf6eb874abb0e1be9df22d2e5a544326c", "gitHead": "1cae3207449edba5e6d8588f3670bb03e2ad92a0", "scripts": {"lint": "eslint dedent.js __tests__", "test": "jest", "build": "babel dedent.js --out-file dist/dedent.js"}, "_npmUser": {"name": "dmnd", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/dmnd/dedent.git", "type": "git"}, "_npmVersion": "3.10.7", "description": "An ES6 string tag that strips indentation from multi-line strings", "directories": {}, "_nodeVersion": "6.3.1", "devDependencies": {"jest": "^18.1.0", "eslint": "^3.14.1", "babel-cli": "^6.22.2", "babel-preset-es2015": "^6.22.0", "babel-preset-es2016": "^6.22.0", "babel-preset-es2017": "^6.22.0"}, "_npmOperationalInternal": {"tmp": "tmp/dedent-0.7.0.tgz_1485642779003_0.26584634045138955", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.0": {"name": "dedent", "version": "1.0.0", "keywords": ["dedent", "tag", "multi-line string", "es6"], "author": {"url": "http://desmondbrand.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "dedent@1.0.0", "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}, {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "dist": {"shasum": "ee68bb13914f374727bd44e8dee28460dbaa0acb", "tarball": "https://registry.npmjs.org/dedent/-/dedent-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-Hl6C/SsX4m8Tmn+bskSe3ZoNiNylxjtwhCRAVX9+JBHnOqRglOdeAzgl5lKZXK9xfRtaF5kpLO1ItFvwGlr9rA==", "signatures": [{"sig": "MEUCIBw/R/6ClgNce9LZ4uoM3jZknB855DAaZWD5mI+936FRAiEAovIcbSCKfCl+Fs07MrEpwzwvTTZ+zcOdGuGA5Rtwplo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8151}, "main": "dist/dedent.js", "module": "./dist/dedent.mjs", "gitHead": "ea84081793384edc60fac71fc85874866bbb5781", "scripts": {"lint": "eslint dedent.js __tests__", "test": "jest", "build": "yarn build:legacy && yarn build:modern", "prepack": "yarn build", "build:legacy": "BABEL_ENV=legacy babel dedent.js --out-file dist/dedent.js", "build:modern": "BABEL_ENV=modern babel dedent.js --out-file dist/dedent.mjs"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/dmnd/dedent.git", "type": "git"}, "_npmVersion": "9.6.4", "description": "An ES6 string tag that strips indentation from multi-line strings", "directories": {}, "_nodeVersion": "20.0.0", "dependencies": {"babel-plugin-macros": "^3.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.41.0", "flow-bin": "^0.206.0", "@babel/cli": "^7.21.5", "@babel/core": "^7.21.8", "hermes-eslint": "^0.11.1", "@babel/preset-env": "^7.21.5", "babel-plugin-tester": "^11.0.4", "eslint-plugin-ft-flow": "^2.0.3", "babel-plugin-add-module-exports": "^1.0.4", "@babel/plugin-transform-flow-comments": "^7.22.5"}, "_npmOperationalInternal": {"tmp": "tmp/dedent_1.0.0_1688318139706_0.1491089331107318", "host": "s3://npm-registry-packages"}}, "0.8.0-beta": {"name": "dedent", "version": "0.8.0-beta", "keywords": ["dedent", "tag", "multi-line string", "es6"], "author": {"url": "http://desmondbrand.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "dedent@0.8.0-beta", "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}, {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "dist": {"shasum": "c946ff5de040b25f2daf5003e238d5105f76ffd6", "tarball": "https://registry.npmjs.org/dedent/-/dedent-0.8.0-beta.tgz", "fileCount": 8, "integrity": "sha512-fUWotkeX4wbqSA5VkbmBGHobvemynX0thkLdsE5y3m/f7vGLzsH8vNkn0iaKbnwuG1wvf4yisgMfq7GkiAWZdA==", "signatures": [{"sig": "MEYCIQCzFq98jAgP2V+8Y6sM9rUDQOEqexSwjNk6S9zux72OnwIhAMTee8hoRmiAn2Nfe6ciDI6fY8fSAM9o0ajBbqV7ZtI3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8156}, "main": "dist/dedent.js", "module": "./dist/dedent.mjs", "gitHead": "517af348523e1d09e461dea8b4d3f59791b124eb", "scripts": {"lint": "eslint dedent.js __tests__", "test": "jest", "build": "yarn build:legacy && yarn build:modern", "prepack": "yarn build", "build:legacy": "BABEL_ENV=legacy babel dedent.js --out-file dist/dedent.js", "build:modern": "BABEL_ENV=modern babel dedent.js --out-file dist/dedent.mjs"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/dmnd/dedent.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "An ES6 string tag that strips indentation from multi-line strings", "directories": {}, "_nodeVersion": "20.2.0", "dependencies": {"babel-plugin-macros": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.41.0", "flow-bin": "^0.206.0", "@babel/cli": "^7.21.5", "@babel/core": "^7.21.8", "hermes-eslint": "^0.11.1", "@babel/preset-env": "^7.21.5", "babel-plugin-tester": "^11.0.4", "eslint-plugin-ft-flow": "^2.0.3", "babel-plugin-add-module-exports": "^1.0.4", "@babel/plugin-transform-flow-comments": "^7.22.5"}, "_npmOperationalInternal": {"tmp": "tmp/dedent_0.8.0-beta_1688992570274_0.03896345554719205", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "dedent", "version": "1.0.1", "keywords": ["dedent", "tag", "multi-line string", "es6"], "author": {"url": "http://desmondbrand.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "dedent@1.0.1", "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}, {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "dist": {"shasum": "7fd6c7655e9c5396b65a422fd6834682d09809ea", "tarball": "https://registry.npmjs.org/dedent/-/dedent-1.0.1.tgz", "fileCount": 8, "integrity": "sha512-ISfLyH40XVmphoIMyGwOpN4w9kB4Jjz5r62myZDJMbjJSwur7r/EZ0xg64yyZl50qoDVIvCT9UMLmQAjepmjiA==", "signatures": [{"sig": "MEQCIHht7RMtMA6uNiTdR9Lc8hq1gCyMLx8/EwMHj4Gb3K4uAiBZfEH0xU1LdraTD4z71LxK20+8BLi1DJv/NkaDL1cfYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8151}, "main": "dist/dedent.js", "module": "./dist/dedent.mjs", "gitHead": "e85dfdd8a371d80aa757c6e9b42ea72b11caf23c", "scripts": {"lint": "eslint dedent.js __tests__", "test": "jest", "build": "yarn build:legacy && yarn build:modern", "prepack": "yarn build", "build:legacy": "BABEL_ENV=legacy babel dedent.js --out-file dist/dedent.js", "build:modern": "BABEL_ENV=modern babel dedent.js --out-file dist/dedent.mjs"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/dmnd/dedent.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "An ES6 string tag that strips indentation from multi-line strings", "directories": {}, "_nodeVersion": "20.2.0", "dependencies": {"babel-plugin-macros": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.41.0", "flow-bin": "^0.206.0", "@babel/cli": "^7.21.5", "@babel/core": "^7.21.8", "hermes-eslint": "^0.11.1", "@babel/preset-env": "^7.21.5", "babel-plugin-tester": "^11.0.4", "eslint-plugin-ft-flow": "^2.0.3", "babel-plugin-add-module-exports": "^1.0.4", "@babel/plugin-transform-flow-comments": "^7.22.5"}, "_npmOperationalInternal": {"tmp": "tmp/dedent_1.0.1_1688992629638_0.3208455382814277", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "dedent", "version": "1.0.2", "keywords": ["dedent", "tag", "multi-line string", "es6"], "author": {"url": "http://desmondbrand.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "dedent@1.0.2", "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}, {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "dist": {"shasum": "7888480aeb938c9c56d9b81bb5b63a69aa84e565", "tarball": "https://registry.npmjs.org/dedent/-/dedent-1.0.2.tgz", "fileCount": 8, "integrity": "sha512-jjGxaNbvFRbKwJMrS7Vbtp5VIUet5nmrUUAymCbir4/xXX3xvsBtMqtohNeSQ5ztUTR7lKLO5Qq1vf5cCe+Hcg==", "signatures": [{"sig": "MEUCIGLwyqb7Fp0wB9yyCpYfOKo3XKFE22RTld0XCuKtA1GNAiEAkfaTitsOwIotNfOy6MmIOz9lNKTgZEjueDakljT6VG4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8206}, "main": "dist/dedent.js", "module": "./dist/dedent.mjs", "gitHead": "31b09261906f4d134a7a4b07290a87c4a80cd01a", "scripts": {"lint": "eslint dedent.js __tests__", "test": "jest", "build": "yarn build:legacy && yarn build:modern", "prepack": "yarn build", "build:legacy": "BABEL_ENV=legacy babel dedent.js --out-file dist/dedent.js", "build:modern": "BABEL_ENV=modern babel dedent.js --out-file dist/dedent.mjs"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/dmnd/dedent.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "An ES6 string tag that strips indentation from multi-line strings", "directories": {}, "_nodeVersion": "20.2.0", "dependencies": {"babel-plugin-macros": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.41.0", "flow-bin": "^0.206.0", "@babel/cli": "^7.21.5", "@babel/core": "^7.21.8", "hermes-eslint": "^0.11.1", "@babel/preset-env": "^7.21.5", "babel-plugin-tester": "^11.0.4", "eslint-plugin-ft-flow": "^2.0.3", "babel-plugin-add-module-exports": "^1.0.4", "@babel/plugin-transform-flow-comments": "^7.22.5"}, "_npmOperationalInternal": {"tmp": "tmp/dedent_1.0.2_1689349615103_0.9403699229860598", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "dedent", "version": "1.1.0", "keywords": ["dedent", "tag", "multi-line string", "es6"], "author": {"url": "http://desmondbrand.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "dedent@1.1.0", "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}, {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "dist": {"shasum": "c93224ad1470ac8644818249bf90eeb5896155cf", "tarball": "https://registry.npmjs.org/dedent/-/dedent-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-H2H1MEpxX7qLT/KxcLkqWhtFNNyMDIB6pIKf7dJHNFP70JOOX0/FUUODBw1T7/OG9qnTxxbUiXcQsFbiyd2FSg==", "signatures": [{"sig": "MEYCIQCaExKKBqyjd4ZGO9mATeR7+jmE8/Fi/mN/RVUmFD+x1AIhALIcYofRquhn0dNlSFg8HO7QjGfRBlS/O8+zuI/qHNvi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8578}, "main": "dist/dedent.js", "types": "./dist/dedent.d.ts", "module": "./dist/dedent.mjs", "gitHead": "b1315254fcd1ddd02857e23f01edfb33fd6c5c28", "scripts": {"tsc": "tsc", "lint": "eslint .", "test": "jest", "build": "yarn build:legacy && yarn build:modern && yarn build:types", "prepack": "yarn build", "build:types": "tsup dedent.ts --dts-only", "build:legacy": "BABEL_ENV=legacy babel dedent.ts --out-file dist/dedent.js", "build:modern": "BABEL_ENV=modern babel dedent.ts --out-file dist/dedent.mjs"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/dmnd/dedent.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "An ES6 string tag that strips indentation from multi-line strings", "directories": {}, "_nodeVersion": "20.2.0", "dependencies": {"babel-plugin-macros": "^3.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "tsup": "^7.1.0", "eslint": "^8.41.0", "@babel/cli": "^7.21.5", "typescript": "^5.1.6", "@babel/core": "^7.21.8", "@types/jest": "^29.5.3", "hermes-eslint": "^0.11.1", "@babel/preset-env": "^7.21.5", "babel-plugin-tester": "^11.0.4", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.0.0", "@types/babel-plugin-macros": "^3.1.0", "babel-plugin-add-module-exports": "^1.0.4", "@typescript-eslint/eslint-plugin": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/dedent_1.1.0_1689606997917_0.818453577701735", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "dedent", "version": "1.2.0", "keywords": ["dedent", "tag", "multi-line string", "es6"], "author": {"url": "http://desmondbrand.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "dedent@1.2.0", "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}, {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "dist": {"shasum": "32039cd75c035f684e01c4a07cb88c0ecbeb57be", "tarball": "https://registry.npmjs.org/dedent/-/dedent-1.2.0.tgz", "fileCount": 8, "integrity": "sha512-i4tcg0ClgvMUSxwHpt+NHQ01ZJmAkl6eBvDNrSZG9e+oLRTCSHv0wpr/Bzjpf6CwKeIHGevE1M34Y1Axdms5VQ==", "signatures": [{"sig": "MEQCICnwHuxNdze7fToqmFzp/NJCn28Fho1BzvnXfTVuUQqrAiAuA7laReI9vPGeFrwZHXeZJS6T3IhyCrC+1g32wY5pmw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8673}, "main": "dist/dedent.js", "types": "./dist/dedent.d.ts", "module": "./dist/dedent.mjs", "gitHead": "b6cdb9d53120c18b42b5ef0d92ee0f00871091d4", "scripts": {"tsc": "tsc", "lint": "eslint .", "test": "jest", "build": "yarn build:legacy && yarn build:modern && yarn build:types", "prepack": "yarn build", "build:types": "tsup dedent.ts --dts-only", "build:legacy": "BABEL_ENV=legacy babel dedent.ts --out-file dist/dedent.js", "build:modern": "BABEL_ENV=modern babel dedent.ts --out-file dist/dedent.mjs"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/dmnd/dedent.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "An ES6 string tag that strips indentation from multi-line strings", "directories": {}, "_nodeVersion": "20.2.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "tsup": "^7.1.0", "eslint": "^8.41.0", "@babel/cli": "^7.21.5", "typescript": "^5.1.6", "@babel/core": "^7.21.8", "@types/jest": "^29.5.3", "hermes-eslint": "^0.11.1", "@babel/preset-env": "^7.21.5", "babel-plugin-tester": "^11.0.4", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.0.0", "@types/babel-plugin-macros": "^3.1.0", "babel-plugin-add-module-exports": "^1.0.4", "@typescript-eslint/eslint-plugin": "^6.0.0"}, "peerDependencies": {"babel-plugin-macros": "^3.1.0"}, "peerDependenciesMeta": {"babel-plugin-macros": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/dedent_1.2.0_1689686759829_0.8075113558640181", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "dedent", "version": "1.3.0", "keywords": ["dedent", "tag", "multi-line string", "es6"], "author": {"url": "http://desmondbrand.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "dedent@1.3.0", "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}, {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "dist": {"shasum": "15d6809eb15b581d5587a2dc208f34118e35bee3", "tarball": "https://registry.npmjs.org/dedent/-/dedent-1.3.0.tgz", "fileCount": 8, "integrity": "sha512-7glNLfvdsMzZm3FpRY1CHuI2lbYDR+71YmrhmTZjYFD5pfT0ACgnGRdrrC9Mk2uICnzkcdelCx5at787UDGOvg==", "signatures": [{"sig": "MEUCIHamISKOkc12kroaOUSX56WgRE0+u1oCGiR6QYR0xu5eAiEAqMX69/8OGFgbYgIe/yGoYDbn2rY64Ealy16drFNJmis=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8857}, "main": "dist/dedent.js", "types": "./dist/dedent.d.ts", "module": "./dist/dedent.mjs", "exports": {".": {"types": "./dist/dedent.d.ts", "import": "./dist/dedent.mjs", "default": "./dist/dedent.js"}}, "gitHead": "c966051f70520231fba80adfc7b5f176141a1949", "scripts": {"tsc": "tsc", "lint": "eslint .", "test": "jest", "build": "yarn build:legacy && yarn build:modern && yarn build:types", "prepack": "yarn build", "build:types": "tsup dedent.ts --dts-only", "build:legacy": "BABEL_ENV=legacy babel dedent.ts --out-file dist/dedent.js", "build:modern": "BABEL_ENV=modern babel dedent.ts --out-file dist/dedent.mjs"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/dmnd/dedent.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "An ES6 string tag that strips indentation from multi-line strings", "directories": {}, "_nodeVersion": "16.17.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "tsup": "^7.1.0", "eslint": "^8.41.0", "@babel/cli": "^7.21.5", "typescript": "^5.1.6", "@babel/core": "^7.21.8", "@types/jest": "^29.5.3", "hermes-eslint": "^0.11.1", "@babel/preset-env": "^7.21.5", "babel-plugin-macros": "^3.1.0", "babel-plugin-tester": "^11.0.4", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.0.0", "@types/babel-plugin-macros": "^3.1.0", "babel-plugin-add-module-exports": "^1.0.4", "@typescript-eslint/eslint-plugin": "^6.0.0"}, "peerDependencies": {"babel-plugin-macros": "^3.1.0"}, "peerDependenciesMeta": {"babel-plugin-macros": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/dedent_1.3.0_1690468899511_0.5703643173983524", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "dedent", "version": "1.4.0", "keywords": ["dedent", "tag", "multi-line string", "es6"], "author": {"url": "http://desmondbrand.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "dedent@1.4.0", "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}, {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "dist": {"shasum": "92405d2d81f5b0c9b53dc90cacfe88c619eb5481", "tarball": "https://registry.npmjs.org/dedent/-/dedent-1.4.0.tgz", "fileCount": 8, "integrity": "sha512-gS0y6EDI3wMYzORcuX3HA2n7RciVDnG6Ls5CQjPHEd8KCD4XaFjx1YmH63SLOJVStw6wXfCIO+7JS0urMOavuA==", "signatures": [{"sig": "MEQCIGmEWfAckzlgXDKjdt7d8wgpwVax9UAVEc/AsYrP8LXcAiBxeKrans6PZextK4utBM2iuJsteOuAdrBjkkx2T60j1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9024}, "main": "dist/dedent.js", "types": "./dist/dedent.d.ts", "module": "./dist/dedent.mjs", "exports": {".": {"import": {"types": "./dist/dedent.d.mts", "default": "./dist/dedent.mjs"}, "require": {"types": "./dist/dedent.d.ts", "default": "./dist/dedent.js"}}}, "gitHead": "97d6cc0b950a2057782253f59008ede4dca3ca5e", "scripts": {"tsc": "tsc", "lint": "eslint .", "test": "jest", "build": "yarn build:legacy && yarn build:modern && yarn build:types", "prepack": "yarn build", "build:types": "tsup dedent.ts --dts-only --format cjs,esm", "build:legacy": "BABEL_ENV=legacy babel dedent.ts --out-file dist/dedent.js", "build:modern": "BABEL_ENV=modern babel dedent.ts --out-file dist/dedent.mjs"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/dmnd/dedent.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "An ES6 string tag that strips indentation from multi-line strings", "directories": {}, "_nodeVersion": "20.2.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "tsup": "^7.1.0", "eslint": "^8.41.0", "@babel/cli": "^7.21.5", "typescript": "^5.1.6", "@babel/core": "^7.21.8", "@types/jest": "^29.5.3", "hermes-eslint": "^0.11.1", "@babel/preset-env": "^7.21.5", "babel-plugin-macros": "^3.1.0", "babel-plugin-tester": "^11.0.4", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.0.0", "@types/babel-plugin-macros": "^3.1.0", "babel-plugin-add-module-exports": "^1.0.4", "@typescript-eslint/eslint-plugin": "^6.0.0"}, "peerDependencies": {"babel-plugin-macros": "^3.1.0"}, "peerDependenciesMeta": {"babel-plugin-macros": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/dedent_1.4.0_1690757214824_0.8728884843215454", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "dedent", "version": "1.5.0", "keywords": ["dedent", "tag", "multi-line string", "es6"], "author": {"url": "http://desmondbrand.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "dedent@1.5.0", "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}, {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "dist": {"shasum": "6e0fb8016002deba2d56927ebd7e3caf7e84e22a", "tarball": "https://registry.npmjs.org/dedent/-/dedent-1.5.0.tgz", "fileCount": 8, "integrity": "sha512-3sSQTYoWKGcRHmHl6Y6opLpRJH55bxeGQ0Y1LCI5pZzUXvokVkj0FC4bi7uEwazxA9FQZ0Nv067Zt5kSUvXxEA==", "signatures": [{"sig": "MEQCIFGjRZvV5g+I0i3Kd3abZydhAv66rykDrpzh2G3q1oscAiAzQ+cqAV+iWVcqckQFo6sBM82Ieq1+WpgaumHQoDrj9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13365}, "main": "dist/dedent.js", "types": "./dist/dedent.d.ts", "module": "./dist/dedent.mjs", "exports": {".": {"import": {"types": "./dist/dedent.d.mts", "default": "./dist/dedent.mjs"}, "require": {"types": "./dist/dedent.d.ts", "default": "./dist/dedent.js"}}}, "gitHead": "4d0ea6dfee09312649a17601ae8cc25a34b78723", "scripts": {"tsc": "tsc", "lint": "eslint .", "test": "jest", "build": "yarn build:legacy && yarn build:modern && yarn build:types", "prepack": "yarn build", "build:types": "tsup dedent.ts --dts-only --format cjs,esm", "build:legacy": "BABEL_ENV=legacy babel dedent.ts --out-file dist/dedent.js", "build:modern": "BABEL_ENV=modern babel dedent.ts --out-file dist/dedent.mjs"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/dmnd/dedent.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "An ES6 string tag that strips indentation from multi-line strings", "directories": {}, "_nodeVersion": "20.2.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "tsup": "^7.1.0", "eslint": "^8.41.0", "@babel/cli": "^7.21.5", "typescript": "^5.1.6", "@babel/core": "^7.21.8", "@types/jest": "^29.5.3", "hermes-eslint": "^0.11.1", "@babel/preset-env": "^7.21.5", "babel-plugin-macros": "^3.1.0", "babel-plugin-tester": "^11.0.4", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.0.0", "@types/babel-plugin-macros": "^3.1.0", "babel-plugin-add-module-exports": "^1.0.4", "@typescript-eslint/eslint-plugin": "^6.0.0"}, "peerDependencies": {"babel-plugin-macros": "^3.1.0"}, "peerDependenciesMeta": {"babel-plugin-macros": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/dedent_1.5.0_1690757575279_0.259935855491487", "host": "s3://npm-registry-packages"}}, "1.5.1": {"name": "dedent", "version": "1.5.1", "keywords": ["dedent", "tag", "multi-line string", "es6"], "author": {"url": "http://desmondbrand.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "dedent@1.5.1", "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}, {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "dist": {"shasum": "4f3fc94c8b711e9bb2800d185cd6ad20f2a90aff", "tarball": "https://registry.npmjs.org/dedent/-/dedent-1.5.1.tgz", "fileCount": 9, "integrity": "sha512-+LxW+KLWxu3HW3M2w2ympwtqPrqYRzU8fqi6Fhd18fBALe15blJPI/I4+UHveMVG6lJqB4JNd4UG0S5cnVHwIg==", "signatures": [{"sig": "MEYCIQCEKcC+M/WngBwwc+FwbAFJ9xdtu/XzHGQEUIKigpivygIhAISFxRvR3u5ln50EMHTBvKRqfnudzx7Hp2KjrXXhW0Eh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13912}, "main": "dist/dedent.js", "types": "./dist/dedent.d.ts", "module": "./dist/dedent.mjs", "exports": {".": {"import": {"types": "./dist/dedent.d.mts", "default": "./dist/dedent.mjs"}, "require": {"types": "./dist/dedent.d.ts", "default": "./dist/dedent.js"}}}, "gitHead": "d435ce3ae4b9adfd268786c780558a550e4546e0", "scripts": {"tsc": "tsc", "lint": "eslint .", "test": "jest", "build": "yarn build:legacy && yarn build:modern && yarn build:types", "prepack": "yarn build", "build:types": "tsup dedent.ts --dts-only --format cjs,esm", "build:legacy": "BABEL_ENV=legacy babel dedent.ts --out-file dist/dedent.js", "build:modern": "BABEL_ENV=modern babel dedent.ts --out-file dist/dedent.mjs"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/dmnd/dedent.git", "type": "git"}, "_npmVersion": "9.6.6", "description": "An ES6 string tag that strips indentation from multi-line strings", "directories": {}, "_nodeVersion": "20.2.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.5.0", "tsup": "^7.1.0", "eslint": "^8.41.0", "@babel/cli": "^7.21.5", "typescript": "^5.1.6", "@babel/core": "^7.21.8", "@types/jest": "^29.5.3", "hermes-eslint": "^0.11.1", "@babel/preset-env": "^7.21.5", "babel-plugin-macros": "^3.1.0", "babel-plugin-tester": "^11.0.4", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.0.0", "@types/babel-plugin-macros": "^3.1.0", "babel-plugin-add-module-exports": "^1.0.4", "@typescript-eslint/eslint-plugin": "^6.0.0"}, "peerDependencies": {"babel-plugin-macros": "^3.1.0"}, "peerDependenciesMeta": {"babel-plugin-macros": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/dedent_1.5.1_1690816857661_0.1088098670694917", "host": "s3://npm-registry-packages"}}, "1.5.2": {"name": "dedent", "version": "1.5.2", "keywords": ["dedent", "tag", "multi-line string", "es6"], "author": {"url": "http://desmondbrand.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "dedent@1.5.2", "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}, {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "dist": {"shasum": "838f09196f6d1fafd464904132dea270c5fe96b0", "tarball": "https://registry.npmjs.org/dedent/-/dedent-1.5.2.tgz", "fileCount": 8, "integrity": "sha512-O4ncVvQ2ZI0KH+of+Evf956RssiyxvMgU0hE8QyG+rwmUjcxEYX4DoF9FQxIaHye3WQjPXrv3uRhlDrRzlq2CQ==", "signatures": [{"sig": "MEQCIFjggO6ALeswTtruj6qL1fc+icOF47/c+MXiXb0ujdfYAiBnn5UfQ5u5wSbkc5djsb490JVAS8POIglKYw3Neax/hw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23944}, "main": "./dist/dedent.js", "type": "commonjs", "types": "./dist/dedent.d.mts", "module": "./dist/dedent.mjs", "engines": {"node": ">=18"}, "exports": {".": {"types": {"import": "./dist/dedent.d.mts", "require": "./dist/dedent.d.ts"}, "import": "./dist/dedent.mjs", "require": "./dist/dedent.js"}}, "gitHead": "816f35782ea982446ae75acf9001ec2b13e7f5a1", "scripts": {"tsc": "tsc", "lint": "eslint . .*js --max-warnings 0 --report-unused-disable-directives", "test": "jest", "build": "pnpm build:legacy && pnpm build:modern && pnpm build:types", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\"", "prepare": "husky install", "lint:knip": "knip", "build:types": "tsup src/dedent.ts --dts-only", "build:legacy": "BABEL_ENV=legacy babel src/dedent.ts --out-file dist/dedent.js", "build:modern": "BABEL_ENV=modern babel src/dedent.ts --out-file dist/dedent.mjs", "lint:packages": "pnpm dedupe --check", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:package-json": "npmPkgJsonLint .", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dmnd/dedent.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A string tag that strips indentation from multi-line strings. ⬅️", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "21.5.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.7.0", "devDependencies": {"jest": "^29.7.0", "knip": "^2.41.0", "tsup": "^7.2.0", "husky": "^8.0.3", "cspell": "^8.0.0", "eslint": "^8.53.0", "prettier": "^3.0.3", "@babel/cli": "^7.21.5", "release-it": "^17.0.0", "typescript": "^5.2.2", "@types/jest": "^29.5.3", "lint-staged": "^15.1.0", "markdownlint": "^0.31.1", "@types/eslint": "^8.44.7", "eslint-plugin-n": "^16.3.1", "markdownlint-cli": "^0.37.0", "@babel/preset-env": "^7.23.3", "console-fail-test": "^0.2.3", "eslint-plugin-yml": "^1.10.0", "eslint-plugin-jest": "^27.6.0", "yaml-eslint-parser": "^1.2.2", "babel-plugin-tester": "^11.0.4", "eslint-plugin-jsdoc": "^46.9.0", "eslint-plugin-jsonc": "^2.10.0", "jsonc-eslint-parser": "^2.4.0", "eslint-plugin-regexp": "^2.1.1", "npm-package-json-lint": "^7.1.0", "prettier-plugin-curly": "^0.1.3", "eslint-plugin-markdown": "^3.0.1", "should-semantic-release": "^0.2.1", "@babel/preset-typescript": "^7.23.3", "@typescript-eslint/parser": "^6.10.0", "eslint-plugin-deprecation": "^2.0.0", "@types/babel-plugin-macros": "^3.1.0", "eslint-plugin-no-only-tests": "^3.1.0", "eslint-plugin-perfectionist": "^2.3.0", "prettier-plugin-packagejson": "^2.4.6", "eslint-plugin-eslint-comments": "^3.2.0", "babel-plugin-add-module-exports": "^1.0.4", "@typescript-eslint/eslint-plugin": "^6.10.0", "@release-it/conventional-changelog": "^8.0.1", "npm-package-json-lint-config-default": "^6.0.0"}, "peerDependencies": {"babel-plugin-macros": "^3.1.0"}, "peerDependenciesMeta": {"babel-plugin-macros": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/dedent_1.5.2_1712776718151_0.7091100974088214", "host": "s3://npm-registry-packages"}}, "1.5.3": {"name": "dedent", "version": "1.5.3", "keywords": ["dedent", "tag", "multi-line string", "es6"], "author": {"url": "http://desmondbrand.com", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "dedent@1.5.3", "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}, {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "dist": {"shasum": "99aee19eb9bae55a67327717b6e848d0bf777e5a", "tarball": "https://registry.npmjs.org/dedent/-/dedent-1.5.3.tgz", "fileCount": 8, "integrity": "sha512-NHQtfOOW68WD8lgypbLA5oT+Bt0xXJhiYvoR6SmmNXZfpzOGXwdKWmcwG8N7PwVVWV3eF/68nmD9BaJSsTBhyQ==", "signatures": [{"sig": "MEQCIH8L+qs0dOQE5Qmyawan6p+P0WZB6sVWlBI9Vlcvsw1sAiBbGPlVP9kabFWw29okPSccA0i/eO7MUhJ+1g1JRG9NFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23909}, "main": "./dist/dedent.js", "type": "commonjs", "types": "./dist/dedent.d.mts", "module": "./dist/dedent.mjs", "exports": {".": {"types": {"import": "./dist/dedent.d.mts", "require": "./dist/dedent.d.ts"}, "import": "./dist/dedent.mjs", "require": "./dist/dedent.js"}}, "gitHead": "ec46a29c17d4299a771421c5e2648fbfcb20e668", "scripts": {"tsc": "tsc", "lint": "eslint . .*js --max-warnings 0 --report-unused-disable-directives", "test": "jest", "build": "pnpm build:legacy && pnpm build:modern && pnpm build:types", "format": "prettier \"**/*\" --ignore-unknown", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\"", "prepare": "husky install", "lint:knip": "knip", "build:types": "tsup src/dedent.ts --dts-only", "build:legacy": "BABEL_ENV=legacy babel src/dedent.ts --out-file dist/dedent.js", "build:modern": "BABEL_ENV=modern babel src/dedent.ts --out-file dist/dedent.mjs", "lint:packages": "pnpm dedupe --check", "lint:spelling": "cspell \"**\" \".github/**/*\"", "lint:package-json": "npmPkgJsonLint .", "should-semantic-release": "should-semantic-release --verbose"}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/dmnd/dedent.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "A string tag that strips indentation from multi-line strings. ⬅️", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "_nodeVersion": "21.5.0", "_hasShrinkwrap": false, "packageManager": "pnpm@8.7.0", "devDependencies": {"jest": "^29.7.0", "knip": "^2.41.0", "tsup": "^7.2.0", "husky": "^8.0.3", "cspell": "^8.0.0", "eslint": "^8.53.0", "prettier": "^3.0.3", "@babel/cli": "^7.21.5", "release-it": "^17.0.0", "typescript": "^5.2.2", "@types/jest": "^29.5.3", "lint-staged": "^15.1.0", "markdownlint": "^0.31.1", "@types/eslint": "^8.44.7", "eslint-plugin-n": "^16.3.1", "markdownlint-cli": "^0.37.0", "@babel/preset-env": "^7.23.3", "console-fail-test": "^0.2.3", "eslint-plugin-yml": "^1.10.0", "eslint-plugin-jest": "^27.6.0", "yaml-eslint-parser": "^1.2.2", "babel-plugin-tester": "^11.0.4", "eslint-plugin-jsdoc": "^46.9.0", "eslint-plugin-jsonc": "^2.10.0", "jsonc-eslint-parser": "^2.4.0", "eslint-plugin-regexp": "^2.1.1", "npm-package-json-lint": "^7.1.0", "prettier-plugin-curly": "^0.1.3", "eslint-plugin-markdown": "^3.0.1", "should-semantic-release": "^0.2.1", "@babel/preset-typescript": "^7.23.3", "@typescript-eslint/parser": "^6.10.0", "eslint-plugin-deprecation": "^2.0.0", "@types/babel-plugin-macros": "^3.1.0", "eslint-plugin-no-only-tests": "^3.1.0", "eslint-plugin-perfectionist": "^2.3.0", "prettier-plugin-packagejson": "^2.4.6", "eslint-plugin-eslint-comments": "^3.2.0", "babel-plugin-add-module-exports": "^1.0.4", "@typescript-eslint/eslint-plugin": "^6.10.0", "@release-it/conventional-changelog": "^8.0.1", "npm-package-json-lint-config-default": "^6.0.0"}, "peerDependencies": {"babel-plugin-macros": "^3.1.0"}, "peerDependenciesMeta": {"babel-plugin-macros": {"optional": true}}, "_npmOperationalInternal": {"tmp": "tmp/dedent_1.5.3_1712794459208_0.5767360591330959", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "dedent", "version": "1.6.0", "description": "A string tag that strips indentation from multi-line strings. ⬅️", "keywords": ["dedent", "tag", "multi-line string", "es6"], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "repository": {"type": "git", "url": "git+https://github.com/dmnd/dedent.git"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://desmondbrand.com"}, "type": "commonjs", "exports": {".": {"types": {"import": "./dist/dedent.d.mts", "require": "./dist/dedent.d.ts"}, "import": "./dist/dedent.mjs", "require": "./dist/dedent.js"}}, "main": "./dist/dedent.js", "module": "./dist/dedent.mjs", "types": "./dist/dedent.d.mts", "scripts": {"build": "pnpm build:legacy && pnpm build:modern && pnpm build:types", "build:legacy": "BABEL_ENV=legacy babel src/dedent.ts --out-file dist/dedent.js", "build:modern": "BABEL_ENV=modern babel src/dedent.ts --out-file dist/dedent.mjs", "build:types": "tsup src/dedent.ts --dts-only", "format": "prettier \"**/*\" --ignore-unknown", "lint": "eslint . .*js --max-warnings 0 --report-unused-disable-directives", "lint:knip": "knip", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\"", "lint:package-json": "npmPkgJsonLint .", "lint:packages": "pnpm dedupe --check", "lint:spelling": "cspell \"**\" \".github/**/*\"", "prepare": "husky install", "should-semantic-release": "should-semantic-release --verbose", "test": "jest", "tsc": "tsc"}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "devDependencies": {"@babel/cli": "^7.21.5", "@babel/preset-env": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@release-it/conventional-changelog": "^8.0.1", "@types/babel-plugin-macros": "^3.1.0", "@types/eslint": "^8.44.7", "@types/jest": "^29.5.3", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-tester": "^11.0.4", "console-fail-test": "^0.2.3", "cspell": "^8.0.0", "eslint": "^8.53.0", "eslint-plugin-deprecation": "^2.0.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-jsdoc": "^46.9.0", "eslint-plugin-jsonc": "^2.10.0", "eslint-plugin-markdown": "^3.0.1", "eslint-plugin-n": "^16.3.1", "eslint-plugin-no-only-tests": "^3.1.0", "eslint-plugin-perfectionist": "^2.3.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-yml": "^1.10.0", "husky": "^8.0.3", "jest": "^29.7.0", "jsonc-eslint-parser": "^2.4.0", "knip": "^2.41.0", "lint-staged": "^15.1.0", "markdownlint": "^0.31.1", "markdownlint-cli": "^0.37.0", "npm-package-json-lint": "^7.1.0", "npm-package-json-lint-config-default": "^6.0.0", "prettier": "^3.0.3", "prettier-plugin-curly": "^0.1.3", "prettier-plugin-packagejson": "^2.4.6", "release-it": "^17.0.0", "should-semantic-release": "^0.2.1", "tsup": "^7.2.0", "typescript": "^5.2.2", "yaml-eslint-parser": "^1.2.2"}, "peerDependencies": {"babel-plugin-macros": "^3.1.0"}, "peerDependenciesMeta": {"babel-plugin-macros": {"optional": true}}, "packageManager": "pnpm@8.7.0", "_id": "dedent@1.6.0", "gitHead": "95cdd13874a7948800cfd65cb94814de687e3db7", "_nodeVersion": "22.12.0", "_npmVersion": "10.9.0", "dist": {"integrity": "sha512-F1Z+5UCFpmQUzJa11agbyPVMbpgT/qA3/SKyJ1jyBgm7dUcUEa8v9JwDkerSQXfakBwFljIxhOJqGkjUwZ9FSA==", "shasum": "79d52d6389b1ffa67d2bcef59ba51847a9d503b2", "tarball": "https://registry.npmjs.org/dedent/-/dedent-1.6.0.tgz", "fileCount": 8, "unpackedSize": 24484, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCEfYS5YfjIVhyZZsc5pbvuoKYI/HDQYua6Gc5usCK1LwIhAP3tCbwewnQUD/dN7jDx+2r5vIsaiLS0SeistnJshLEY"}]}, "_npmUser": {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}, {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/dedent_1.6.0_1746102963159_0.****************"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-01-13T10:11:05.527Z", "modified": "2025-05-01T12:36:03.513Z", "0.1.0": "2015-01-13T10:11:05.527Z", "0.1.1": "2015-01-13T10:12:55.759Z", "0.2.0": "2015-01-15T08:36:18.760Z", "0.2.1": "2015-01-15T08:38:40.913Z", "0.2.2": "2015-01-16T07:39:02.600Z", "0.3.0": "2015-01-25T20:33:05.797Z", "0.4.0": "2015-04-13T02:21:01.036Z", "0.5.0": "2015-11-08T05:37:45.046Z", "0.6.0": "2015-11-08T05:55:48.699Z", "0.7.0": "2017-01-28T22:32:59.233Z", "1.0.0": "2023-07-02T17:15:39.893Z", "0.8.0-beta": "2023-07-10T12:36:10.443Z", "1.0.1": "2023-07-10T12:37:09.844Z", "1.0.2": "2023-07-14T15:46:55.308Z", "1.1.0": "2023-07-17T15:16:38.109Z", "1.2.0": "2023-07-18T13:26:00.011Z", "1.3.0": "2023-07-27T14:41:39.654Z", "1.4.0": "2023-07-30T22:46:54.986Z", "1.5.0": "2023-07-30T22:52:55.414Z", "1.5.1": "2023-07-31T15:20:57.855Z", "1.5.2": "2024-04-10T19:18:38.291Z", "1.5.3": "2024-04-11T00:14:19.406Z", "1.6.0": "2025-05-01T12:36:03.341Z"}, "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://desmondbrand.com"}, "license": "MIT", "homepage": "https://github.com/dmnd/dedent", "keywords": ["dedent", "tag", "multi-line string", "es6"], "repository": {"type": "git", "url": "git+https://github.com/dmnd/dedent.git"}, "description": "A string tag that strips indentation from multi-line strings. ⬅️", "maintainers": [{"name": "dmnd", "email": "<EMAIL>"}, {"name": "joshua<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "<h1 align=\"center\">dedent</h1>\n\n<p align=\"center\">A string tag that strips indentation from multi-line strings. ⬅️</p>\n\n<p align=\"center\">\n\t<a href=\"#contributors\" target=\"_blank\">\n<!-- prettier-ignore-start -->\n<!-- ALL-CONTRIBUTORS-BADGE:START - Do not remove or modify this section -->\n<img alt=\"All Contributors: 18 👪\" src=\"https://img.shields.io/badge/all_contributors-18_👪-21bb42.svg\" />\n<!-- ALL-CONTRIBUTORS-BADGE:END -->\n<!-- prettier-ignore-end -->\n</a>\n\t<a href=\"https://codecov.io/gh/dmnd/dedent\" target=\"_blank\">\n\t\t<img alt=\"Codecov Test Coverage\" src=\"https://codecov.io/gh/dmnd/dedent/branch/main/graph/badge.svg\"/>\n\t</a>\n\t<a href=\"https://github.com/dmnd/dedent/blob/main/.github/CODE_OF_CONDUCT.md\" target=\"_blank\">\n\t\t<img alt=\"Contributor Covenant\" src=\"https://img.shields.io/badge/code_of_conduct-enforced-21bb42\" />\n\t</a>\n\t<a href=\"https://github.com/dmnd/dedent/blob/main/LICENSE.md\" target=\"_blank\">\n\t\t<img alt=\"License: MIT\" src=\"https://img.shields.io/github/license/dmnd/dedent?color=21bb42\">\n\t</a>\n\t<img alt=\"Style: Prettier\" src=\"https://img.shields.io/badge/style-prettier-21bb42.svg\" />\n\t<img alt=\"TypeScript: Strict\" src=\"https://img.shields.io/badge/typescript-strict-21bb42.svg\" />\n\t<img alt=\"npm package version\" src=\"https://img.shields.io/npm/v/dedent?color=21bb42\" />\n\t<img alt=\"Contributor Covenant\" src=\"https://img.shields.io/badge/code_of_conduct-enforced-21bb42\" />\n</p>\n\n## Usage\n\n```shell\nnpm i dedent\n```\n\n```js\nimport dedent from \"dedent\";\n\nfunction usageExample() {\n\tconst first = dedent`A string that gets so long you need to break it over\n                       multiple lines. Luckily dedent is here to keep it\n                       readable without lots of spaces ending up in the string\n                       itself.`;\n\n\tconst second = dedent`\n    Leading and trailing lines will be trimmed, so you can write something like\n    this and have it work as you expect:\n\n      * how convenient it is\n      * that I can use an indented list\n         - and still have it do the right thing\n\n    That's all.\n  `;\n\n\tconst third = dedent(`\n    Wait! I lied. Dedent can also be used as a function.\n  `);\n\n\treturn first + \"\\n\\n\" + second + \"\\n\\n\" + third;\n}\n\nconsole.log(usageExample());\n```\n\n```plaintext\nA string that gets so long you need to break it over\nmultiple lines. Luckily dedent is here to keep it\nreadable without lots of spaces ending up in the string\nitself.\n\nLeading and trailing lines will be trimmed, so you can write something like\nthis and have it work as you expect:\n\n  * how convenient it is\n  * that I can use an indented list\n    - and still have it do the right thing\n\nThat's all.\n\nWait! I lied. Dedent can also be used as a function.\n```\n\n## Options\n\nYou can customize the options `dedent` runs with by calling its `withOptions` method with an object:\n\n<!-- prettier-ignore -->\n```js\nimport dedent from 'dedent';\n\ndedent.withOptions({ /* ... */ })`input`;\ndedent.withOptions({ /* ... */ })(`input`);\n```\n\n`options` returns a new `dedent` function, so if you'd like to reuse the same options, you can create a dedicated `dedent` function:\n\n<!-- prettier-ignore -->\n```js\nimport dedent from 'dedent';\n\nconst dedenter = dedent.withOptions({ /* ... */ });\n\ndedenter`input`;\ndedenter(`input`);\n```\n\n### `escapeSpecialCharacters`\n\nJavaScript string tags by default add an extra `\\` escape in front of some special characters such as `$` dollar signs.\n`dedent` will escape those special characters when called as a string tag.\n\nIf you'd like to change the behavior, an `escapeSpecialCharacters` option is available.\nIt defaults to:\n\n- `false`: when `dedent` is called as a function\n- `true`: when `dedent` is called as a string tag\n\n```js\nimport dedent from \"dedent\";\n\n// \"$hello!\"\ndedent`\n  $hello!\n`;\n\n// \"\\$hello!\"\ndedent.withOptions({ escapeSpecialCharacters: false })`\n  $hello!\n`;\n\n// \"$hello!\"\ndedent.withOptions({ escapeSpecialCharacters: true })`\n  $hello!\n`;\n```\n\nFor more context, see [🚀 Feature: Add an option to disable special character escaping](https://github.com/dmnd/dedent/issues/63).\n\n### `trimWhitespace`\n\nBy default, dedent will trim leading and trailing whitespace from the overall string.\n\nThis can be disabled by setting `trimWhitespace: false`.\n\n```js\nimport dedent from \"dedent\";\n\n// \"hello!\"\ndedent`\n  hello! \n`;\n\n// \"\\nhello! \\n\"\ndedent.withOptions({ trimWhitespace: false })`\n  hello! \n`;\n\n// \"hello!\"\ndedent.withOptions({ trimWhitespace: true })`\n  hello! \n`;\n```\n\n## License\n\nMIT\n\n## Contributors\n\n<!-- spellchecker: disable -->\n<!-- ALL-CONTRIBUTORS-LIST:START - Do not remove or modify this section -->\n<!-- prettier-ignore-start -->\n<!-- markdownlint-disable -->\n<table>\n  <tbody>\n    <tr>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://adrianjost.dev/\"><img src=\"https://avatars.githubusercontent.com/u/22987140?v=4?s=100\" width=\"100px;\" alt=\"Adrian Jost\"/><br /><sub><b>Adrian Jost</b></sub></a><br /><a href=\"https://github.com/dmnd/dedent/commits?author=adrianjost\" title=\"Code\">💻</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://m811.com/\"><img src=\"https://avatars.githubusercontent.com/u/156837?v=4?s=100\" width=\"100px;\" alt=\"Andri Möll\"/><br /><sub><b>Andri Möll</b></sub></a><br /><a href=\"https://github.com/dmnd/dedent/issues?q=author%3Amoll\" title=\"Bug reports\">🐛</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://bennypowers.dev/\"><img src=\"https://avatars.githubusercontent.com/u/1466420?v=4?s=100\" width=\"100px;\" alt=\"Benny Powers - עם ישראל חי!\"/><br /><sub><b>Benny Powers - עם ישראל חי!</b></sub></a><br /><a href=\"#tool-bennypowers\" title=\"Tools\">🔧</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/phenomnomnominal\"><img src=\"https://avatars.githubusercontent.com/u/1086286?v=4?s=100\" width=\"100px;\" alt=\"Craig Spence\"/><br /><sub><b>Craig Spence</b></sub></a><br /><a href=\"https://github.com/dmnd/dedent/commits?author=phenomnomnominal\" title=\"Code\">💻</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://synthesis.com/\"><img src=\"https://avatars.githubusercontent.com/u/4427?v=4?s=100\" width=\"100px;\" alt=\"Desmond Brand\"/><br /><sub><b>Desmond Brand</b></sub></a><br /><a href=\"https://github.com/dmnd/dedent/issues?q=author%3Admnd\" title=\"Bug reports\">🐛</a> <a href=\"https://github.com/dmnd/dedent/commits?author=dmnd\" title=\"Code\">💻</a> <a href=\"https://github.com/dmnd/dedent/commits?author=dmnd\" title=\"Documentation\">📖</a> <a href=\"#ideas-dmnd\" title=\"Ideas, Planning, & Feedback\">🤔</a> <a href=\"#infra-dmnd\" title=\"Infrastructure (Hosting, Build-Tools, etc)\">🚇</a> <a href=\"#maintenance-dmnd\" title=\"Maintenance\">🚧</a> <a href=\"#projectManagement-dmnd\" title=\"Project Management\">📆</a> <a href=\"#tool-dmnd\" title=\"Tools\">🔧</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/G-Rath\"><img src=\"https://avatars.githubusercontent.com/u/3151613?v=4?s=100\" width=\"100px;\" alt=\"Gareth Jones\"/><br /><sub><b>Gareth Jones</b></sub></a><br /><a href=\"https://github.com/dmnd/dedent/commits?author=G-Rath\" title=\"Code\">💻</a> <a href=\"https://github.com/dmnd/dedent/issues?q=author%3AG-Rath\" title=\"Bug reports\">🐛</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/otakustay\"><img src=\"https://avatars.githubusercontent.com/u/639549?v=4?s=100\" width=\"100px;\" alt=\"Gray Zhang\"/><br /><sub><b>Gray Zhang</b></sub></a><br /><a href=\"https://github.com/dmnd/dedent/issues?q=author%3Aotakustay\" title=\"Bug reports\">🐛</a></td>\n    </tr>\n    <tr>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://haroen.me/\"><img src=\"https://avatars.githubusercontent.com/u/6270048?v=4?s=100\" width=\"100px;\" alt=\"Haroen Viaene\"/><br /><sub><b>Haroen Viaene</b></sub></a><br /><a href=\"https://github.com/dmnd/dedent/commits?author=Haroenv\" title=\"Code\">💻</a> <a href=\"#maintenance-Haroenv\" title=\"Maintenance\">🚧</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://blog.cometkim.kr/\"><img src=\"https://avatars.githubusercontent.com/u/9696352?v=4?s=100\" width=\"100px;\" alt=\"Hyeseong Kim\"/><br /><sub><b>Hyeseong Kim</b></sub></a><br /><a href=\"#tool-cometkim\" title=\"Tools\">🔧</a> <a href=\"#infra-cometkim\" title=\"Infrastructure (Hosting, Build-Tools, etc)\">🚇</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/jlarmstrongiv\"><img src=\"https://avatars.githubusercontent.com/u/20903247?v=4?s=100\" width=\"100px;\" alt=\"John L. Armstrong IV\"/><br /><sub><b>John L. Armstrong IV</b></sub></a><br /><a href=\"https://github.com/dmnd/dedent/issues?q=author%3Ajlarmstrongiv\" title=\"Bug reports\">🐛</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"http://www.joshuakgoldberg.com/\"><img src=\"https://avatars.githubusercontent.com/u/3335181?v=4?s=100\" width=\"100px;\" alt=\"Josh Goldberg ✨\"/><br /><sub><b>Josh Goldberg ✨</b></sub></a><br /><a href=\"https://github.com/dmnd/dedent/issues?q=author%3AJoshuaKGoldberg\" title=\"Bug reports\">🐛</a> <a href=\"https://github.com/dmnd/dedent/commits?author=JoshuaKGoldberg\" title=\"Code\">💻</a> <a href=\"https://github.com/dmnd/dedent/commits?author=JoshuaKGoldberg\" title=\"Documentation\">📖</a> <a href=\"#ideas-JoshuaKGoldberg\" title=\"Ideas, Planning, & Feedback\">🤔</a> <a href=\"#infra-JoshuaKGoldberg\" title=\"Infrastructure (Hosting, Build-Tools, etc)\">🚇</a> <a href=\"#maintenance-JoshuaKGoldberg\" title=\"Maintenance\">🚧</a> <a href=\"#projectManagement-JoshuaKGoldberg\" title=\"Project Management\">📆</a> <a href=\"#tool-JoshuaKGoldberg\" title=\"Tools\">🔧</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://pratapvardhan.com/\"><img src=\"https://avatars.githubusercontent.com/u/3757165?v=4?s=100\" width=\"100px;\" alt=\"Pratap Vardhan\"/><br /><sub><b>Pratap Vardhan</b></sub></a><br /><a href=\"https://github.com/dmnd/dedent/commits?author=pratapvardhan\" title=\"Code\">💻</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/lydell\"><img src=\"https://avatars.githubusercontent.com/u/2142817?v=4?s=100\" width=\"100px;\" alt=\"Simon Lydell\"/><br /><sub><b>Simon Lydell</b></sub></a><br /><a href=\"https://github.com/dmnd/dedent/issues?q=author%3Alydell\" title=\"Bug reports\">🐛</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/yinm\"><img src=\"https://avatars.githubusercontent.com/u/13295106?v=4?s=100\" width=\"100px;\" alt=\"Yusuke Iinuma\"/><br /><sub><b>Yusuke Iinuma</b></sub></a><br /><a href=\"https://github.com/dmnd/dedent/commits?author=yinm\" title=\"Code\">💻</a></td>\n    </tr>\n    <tr>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/yvele\"><img src=\"https://avatars.githubusercontent.com/u/4225430?v=4?s=100\" width=\"100px;\" alt=\"Yves M.\"/><br /><sub><b>Yves M.</b></sub></a><br /><a href=\"#tool-yvele\" title=\"Tools\">🔧</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/d07RiV\"><img src=\"https://avatars.githubusercontent.com/u/3448203?v=4?s=100\" width=\"100px;\" alt=\"d07riv\"/><br /><sub><b>d07riv</b></sub></a><br /><a href=\"https://github.com/dmnd/dedent/issues?q=author%3Ad07RiV\" title=\"Bug reports\">🐛</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://mizdra.net/\"><img src=\"https://avatars.githubusercontent.com/u/9639995?v=4?s=100\" width=\"100px;\" alt=\"mizdra\"/><br /><sub><b>mizdra</b></sub></a><br /><a href=\"https://github.com/dmnd/dedent/commits?author=mizdra\" title=\"Code\">💻</a></td>\n      <td align=\"center\" valign=\"top\" width=\"14.28%\"><a href=\"https://github.com/sirian\"><img src=\"https://avatars.githubusercontent.com/u/897643?v=4?s=100\" width=\"100px;\" alt=\"sirian\"/><br /><sub><b>sirian</b></sub></a><br /><a href=\"https://github.com/dmnd/dedent/issues?q=author%3Asirian\" title=\"Bug reports\">🐛</a></td>\n    </tr>\n  </tbody>\n</table>\n\n<!-- markdownlint-restore -->\n<!-- prettier-ignore-end -->\n\n<!-- ALL-CONTRIBUTORS-LIST:END -->\n<!-- spellchecker: enable -->\n\n> 💙 This package was templated with [create-typescript-app](https://github.com/JoshuaKGoldberg/create-typescript-app).\n", "readmeFilename": "README.md", "users": {"d3ck": true, "zeke": true, "abhisekp": true, "seldszar": true, "amurchick": true, "spences10": true, "jackboberg": true, "seangenabe": true, "flumpus-dev": true, "migueloller": true, "wesleybliss": true, "chocolateboy": true}}