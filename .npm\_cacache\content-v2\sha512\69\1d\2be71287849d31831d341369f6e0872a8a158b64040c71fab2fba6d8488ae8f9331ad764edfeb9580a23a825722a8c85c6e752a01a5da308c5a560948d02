{"_id": "fs-constants", "_rev": "1-19ed7e4ced0558104b3664b86391dc04", "name": "fs-constants", "dist-tags": {"latest": "1.0.0"}, "versions": {"1.0.0": {"name": "fs-constants", "version": "1.0.0", "description": "Require constants across node and the browser", "main": "index.js", "browser": "browser.js", "dependencies": {}, "devDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/fs-constants.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/fs-constants/issues"}, "homepage": "https://github.com/mafintosh/fs-constants", "gitHead": "3636fd76cf948ebfc143c1913af6d3c4450b1c46", "_id": "fs-constants@1.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.11.1", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow==", "shasum": "6be0de9be998ce16af8afc24497b9ee9b7ccd9ad", "tarball": "https://registry.npmjs.org/fs-constants/-/fs-constants-1.0.0.tgz", "fileCount": 5, "unpackedSize": 2223, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa4eVZCRA9TVsSAnZWagAAwToP/1iImDmdSp3+yQXUS7r5\nAiuHdDIJFdm4uZbS5R+C7LhNCfFHYTOiI1Q0+0yO6zRr+SHAFJJsVZwXcfp2\nDyRm3TY0X9QdjAJs9SifNpIkdWgOwQBrE5QZEUOM8EJ2hNB7zCskk3SUc5FR\noGbkGZEov6pFBn3OpV0IBm2PaiAIyZ+0Xt+TiLlRR5lR0NR+X6LwKtEuNuYC\nfQNCr2Z7uRnJtrvfPOYH6Jycvumt86CgqUfjeWek7CDUgapw2/zA/u+voEoY\nMDbQeaHq+Dzd9F9fVn7/aCh7bKxWwkVHxPB7wEoA8r+u4lJOPLX2sLEjq/QV\nKwGlHKZMFVU6D3kBkg/8deRWuFJpaMkUxqtSKfURbEwWaChq8X7+UNQ+Jx2a\nRdC1gO7od7Jf0srQbPgEGRLXeg19r8NTJOrLStOzZHqrH0oS3iIekGeibNEu\nwKbLJDdJgIVhp1kzzlPtiiv2vV6YVfp/8a3Ny7iysDlP7p/rit5BQeQ9hKaK\nFMeSToWnJfxrSVOYwKZro6vueSxczDgHW6ocxeWXxkZaTTgCidqwqEfzRZ3G\njptJQcVXbbg+TJIPCsOPpRMPGweDjPDsIbx4tIdURMCzzwSv2UHOpy1a9uAn\nq2xKRxLYo6IT8uHlhrfoGFemJk9xkio/gXREaT8DaNVrRoIn3/iYRqOH7BsE\nrITK\r\n=XQxt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDPuKl2Fv3L8SufsUAhIw9o/ViRxxKfHyC/Jw0i4owK7AIhAP5KTZPOj6hDzU7uAysZ8HqPHQbikGvGJv6Py7p2GQa+"}]}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/fs-constants_1.0.0_1524753752667_0.578647269374104"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-04-26T14:42:32.667Z", "1.0.0": "2018-04-26T14:42:32.817Z", "modified": "2022-05-02T23:23:22.507Z"}, "maintainers": [{"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Require constants across node and the browser", "homepage": "https://github.com/mafintosh/fs-constants", "repository": {"type": "git", "url": "git+https://github.com/mafintosh/fs-constants.git"}, "author": {"name": "<PERSON>", "url": "@mafintosh"}, "bugs": {"url": "https://github.com/mafintosh/fs-constants/issues"}, "license": "MIT", "readme": "# fs-constants\n\nSmall module that allows you to get the fs constants across\nNode and the browser. \n\n```\nnpm install fs-constants\n```\n\nPreviously you would use `require('constants')` for this in node but that has been\ndeprecated and changed to `require('fs').constants` which does not browserify.\n\nThis module uses `require('constants')` in the browser and `require('fs').constants` in node to work around this\n\n\n## Usage\n\n``` js\nvar constants = require('fs-constants')\n\nconsole.log('constants:', constants)\n```\n\n## License\n\nMIT\n", "readmeFilename": "README.md"}