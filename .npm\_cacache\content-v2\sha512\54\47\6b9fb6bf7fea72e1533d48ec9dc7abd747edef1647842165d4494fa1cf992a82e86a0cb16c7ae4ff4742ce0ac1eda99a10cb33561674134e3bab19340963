{"_id": "@types/babel__core", "_rev": "594-1485a52b0a05e7f1526003889f7c5eef", "name": "@types/babel__core", "dist-tags": {"ts2.8": "7.0.1", "ts2.9": "7.1.4", "ts3.0": "7.1.4", "ts3.1": "7.1.4", "ts3.2": "7.1.4", "ts3.3": "7.1.4", "ts3.4": "7.1.12", "ts3.5": "7.1.14", "ts3.6": "7.1.15", "ts3.7": "7.1.16", "ts3.8": "7.1.18", "ts3.9": "7.1.19", "ts4.0": "7.1.19", "ts4.1": "7.1.20", "ts4.2": "7.20.0", "ts4.3": "7.20.1", "ts4.4": "7.20.1", "ts5.8": "7.20.5", "ts5.7": "7.20.5", "latest": "7.20.5", "ts4.5": "7.20.5", "ts4.6": "7.20.5", "ts4.7": "7.20.5", "ts4.8": "7.20.5", "ts4.9": "7.20.5", "ts5.0": "7.20.5", "ts5.1": "7.20.5", "ts5.2": "7.20.5", "ts5.3": "7.20.5", "ts5.4": "7.20.5", "ts5.5": "7.20.5", "ts5.6": "7.20.5", "ts5.9": "7.20.5"}, "versions": {"7.0.0": {"name": "@types/babel__core", "version": "7.0.0", "license": "MIT", "_id": "@types/babel__core@7.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "d5a2efb36b07a29d457492938ab8c4570d359ea0", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.0.0.tgz", "fileCount": 4, "integrity": "sha512-UcWHm8tL1/vUFDgjhmb5I/vihNNt5HqB+O+s9lMc4p6TpMtahic7uyjSPEzz3bULpL+DnGf/vagcl1KxnCVTDQ==", "signatures": [{"sig": "MEUCIQCkwaGvSeusOCYyx/HuDp9dYoqrPTgN1dWU4tkJXghB2QIgDjNNSSDNZzmYTTB01Haf5T1F9rpZmAXPV84Ga7YhtFk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20675, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbWPnrCRA9TVsSAnZWagAAcdEP/3mlx7YjnSg1TWpgTMfD\nQG9cPAJMqi6AxbAqqOs8S3kv+tbxMOZloRwv/RHFFkaLHU9OZaFiRoTs7IeZ\nyHVTNdQL+4dlFW+eJyjX15xxGytCvCaqtGXAkJL7krOOzolsb3I6rZeLfSVK\ne6LFbD6zGucTQ2Qm1NkYXNr+skNzVN3gVcTYVQ944nRDMzqz6Oau9Sw+7OWu\nivogT6qscBwPmjpt2EQxIp+C3WnooDg1HNHCeCjVc+QiP6YKcX7FkLh3XAch\n5TWAVaVOOeos918rjOR/QpeZcUkaSM95R/LcVLtggPXU3lTZxgPEMP29PvJc\n5//9fmYD71bxi165peacRDcpcoKNDoZHCvXkrDjvGHoKch6mr4dNXaUH1N5V\nIfOadhD68rMSJ8U9QxTVKcTFzWR/6CgOPbrw5QiGl/V+yO/MUo01fCkYsh5q\n9U87zhGnQpS1WWOZpssVoc66Eyv9sg8TKSdWCzuRb2La1Tydex09q6gsbUQx\nMMNHlmsZtM0SFppw8xDEma1qxSIybTe3hPaMBfK7Y1FWGvh3UT8OLQwMkwdo\nlEEC0NzhjRW49n+b+62qMbk+Ef/PtgVzt85MDqbsVtt8WSJGO2R5ojFKXBUF\nOBIfa8xLAiN+1ebuwXcqgWbUamEF8ks4Tbp9LajEKpXoXSkPEbkdIEw01JtL\nqaoA\r\n=vp3s\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0-beta.54", "@types/babylon": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.0.0_1532557803654_0.7001159003596242", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a434b31778749e74283557b4c940d00d452f0464c57f949f8bfcb7fbd2c7aad8"}, "7.0.1": {"name": "@types/babel__core", "version": "7.0.1", "license": "MIT", "_id": "@types/babel__core@7.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "8b608a1c55cb4c752352cc6c78f7beb61a3fd51f", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.0.1.tgz", "fileCount": 4, "integrity": "sha512-98MQqzXIqRCg/RHd6WHE4iURltTjEwa7b6f+IhI5Gwd9b9QIojTugvXYw9piZgmhi08zHAWwu8BAXO0E5omElg==", "signatures": [{"sig": "MEQCIAFklysUuypYNEhDVFpHz0Y622tlhoM0sJl3XJxH5PLpAiAuCYxH+KgCN+JMGOZDJ7SLFC8VbpGHraLkBeOqR06xYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20650, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbX5x2CRA9TVsSAnZWagAAXZkP/19rUwMw78w8dgoAvBic\nrFWWk1JpTARnMbGRQjS07xpxg2lL+KFMr+MpG2tH2C55DTN6Xsay9fOk3oWz\niYTBb5QjZUzWlXR3Bcvb7XeW4aZEOD2T1l3mILWxrMpKIByC3U1gGPekOX9m\nPyCWyXeQsH7a0EABvXBaYPzseYvjDQiukgGXMJhhPlPv42UfehFfEJA1l+1v\nguADT59PvW4Ew8qaSQa84vKvpvaARB78u8JVgayml4HkTraucFJhxUg3IC2K\nlNzFWDlNGqgYulQMA2Ek3y7yVIEzYzL38a3ULplKulnWjNT3I4q2TDenRoGR\nWuLQiawejCdhYKZ5RJ/2QMHwvLZ6Id7fEniMg/3q14Qx/3akeL+ej7xCawdI\n4InpACvAepsjlbbQ5L6mbhg6Qabz0gQN8E59n99ipo6G4rFWkm8FXRyyjZts\nquOqy1RFd/C06bTYC8kHky8knsvJ52M6hoGM7hoTBMZPVO2DZj6TpiuwLr/l\npOylcU97PgORwXSXgtASa3PvKE21u1O0quT1Piww5MHvocZQgDEweOOtv83S\n13zetG7iLOCqeqTrinkImEFWGvsmrSh8AmFelojnCnE3t2ZG/qiM8R6frgnb\nsrcoVKqi7eKNMrraQo1JpqPJ/3O+UX6Tome2P3jQAN5Wbtw696p0f9X19RzB\nvasU\r\n=Xk2x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0-beta.54"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.0.1_1532992630115_0.25213917440343425", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "886e1ff3cac391b07be98c6efd4416e7400264339b5ff0b217fb59ff14a2facb"}, "7.0.2": {"name": "@types/babel__core", "version": "7.0.2", "license": "MIT", "_id": "@types/babel__core@7.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "393c8853ce3c8445ae8d9536964177b4b1a115bf", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.0.2.tgz", "fileCount": 4, "integrity": "sha512-+m30DjG2n2c1+B9UV0MkaGWZcChiGEtVa42+7B6Pv0oXmgneQwu6IVRbltxF6B2xuh7xg9l1aqT07l9CnLlPhA==", "signatures": [{"sig": "MEQCIGV2rCuk/cuQQNF0FYgvEXDiiamAsgfqZcWFIss6NRdaAiAmoUF7/QdUDoTtm3ZhGvlLTME7ACBGuDgoYJ7HrW23tA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb7fouCRA9TVsSAnZWagAAbu0P/R7YYBtir9TyjV0m1pw9\naiyTAVOebb6tCnTBxnrsibjqzXPlQfhK1dQgP1K0KNJBStP5d3rSj03s5pmp\n7DrRw4W2Dc5cGcdyXinWXGUk21AmOpaJrV6/Afe52acaltvSHUvnxctwe02A\nXtsmKH98EMGCn63p1t+7fLa6xjFQOPZjiYF0Ue1PqlbImf1wffyR4BnTKEHS\ngRflDERV1c9tzhcwTWb4Zy9pZ+UubGsK9DD1gTgAkMpODhnW3KpKUdsobVbd\nc6nibnKQG4ChyVU140zKZ0fXUPUj3GcI+Q96u0sQJ9N6uiLadddbieTUmxMm\nWpkdylRSol5fE5sCxLxXxQyeqYa8DBMc7yKdRqOyWaVDjLw/Dzc8iSoIEtdR\nQGuDbowzCEk1yc6OPOBamlPle910KIblCq/a4jbG4QttkqkUhFDX257fSvE+\neYqLXLH6V+w2FLJd0sGzRgvf+YoD95mPFTHxHsSSJI0hxj8K+RuXYFlcqkkQ\nptvx9DNOTpQIvXmAHxHA+DVIvM9p85mhl5cC55+YVCUNN28QxQrsJxNErB1i\nDFqo+PJxLnItUfQdGbL+GNoD1bHjh9vYXWHM1ik5VOEv5rPc8wf5P+tMqrj6\nHeXp4QExTIvv/Yqi+9USrIHojPI9PYXklF9HQXCVypGxGmht8ag7lhwJUavl\nWMub\r\n=VnbL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.9", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.0.2_1542322733230_0.7259505779186723", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7637d5e1d474b6b0fb1d3d24984841a3375c06cd75d0a9faaca8c75f3f2f5c16"}, "7.0.3": {"name": "@types/babel__core", "version": "7.0.3", "license": "MIT", "_id": "@types/babel__core@7.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "4c8fb2afd4dbea9b0aab56f828a8adce592ef567", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.0.3.tgz", "fileCount": 4, "integrity": "sha512-F8E0lUeQ1uaprb5dEHLdOMElH5z+hk+L/DlQykXYOvhUyPQuH+Sj4Tm0sV3W0Za2sx1YkpdNyug7P2TNetWxKQ==", "signatures": [{"sig": "MEUCIG8aqYt8DuO3aNjI5FCtp9Gz7VWXB+04xX6yaBIut1K3AiEA+WEnpV5i9AiFnS/tSnHQECxJic18kA9TTa3uowMEQQs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20724, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/rpiCRA9TVsSAnZWagAA5uAP/RSOqnd1kwkZjwF8Wbj/\nc2n5tHDucrMesPP0+uPjv/+pLy71ktizvmqj89kAbIoF/74NHPBVtRQwSfCk\nWeWQAo13mjqlV/kXxEZce4J5MAZd8YUEbkx09QHeRogfbOmCuf3+0b7fQobw\ndaz3828Aa7QQKdv42q5OKLNG1VhdQ1jWcrdP0M+ZfYZ/kGOyeRf12aBIaJRL\ng8IfHDhW5DIIQBMGF9hWLMTwokc0of0ysgkLcNamuOLT0s/g+DkV3kk0Umo3\n3OSFjqEK2LVKTI8/Bartont8MmkrT8nzmeugflPbNIibSCB9bkDj0FcOdlnT\nOv4uSII+8KAaDFNeE0OdEBrvPa7VyaYynQh66F14Uf8oNh8yVqgwBvdl2yu4\nFojMvB482y3E2fa3jIpw5xoiI8W39A8uzPmpkt332dHRWQSkRghtDr2CzIbj\njxZpo8tHFSdstsOyo3CwVZtJTPWjQ7o1wg44m5iyzBpI8rtKUtyzpIgtRd2T\nymu8yQ3fjWiQNYutDi769s0MtbPMA34SbUh+keHy35iE5O2OoWdXkpbPAvVz\n2rHGjrXFfpWr/ONsXU/Wn+Tenhj2FUJkXihc0kMKPbl/8sspn8RvvY+WBWP4\noFEsgfLp8fGjfW1VZfsg5+MmUI8LxqjYpNUALmL2Z79ynT6fd4i0bisuY1TK\njuR6\r\n=w0Fj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.9", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.0.3_1543420513678_0.7620786493835316", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "74a85e84fbe8695cb9dc53d87ed2919b17cacce76023f0f9e66d21836f4d1507"}, "7.0.4": {"name": "@types/babel__core", "version": "7.0.4", "license": "MIT", "_id": "@types/babel__core@7.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "14b30c11113bad353cabfaea73e327b48edb0f0e", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.0.4.tgz", "fileCount": 4, "integrity": "sha512-2Y2RK1BN5BRFfhneGfQA8mmFmTANbzGgS5uQPluoRqGNWb6uAcefqxzNbqgxPpmPkLqKapQfmYcyyl5iAQV+fA==", "signatures": [{"sig": "MEQCIAoMAjNEHKRxvB/lMtGSNoY76utL0wZp0BfQbNCup3ZsAiAcxfiVeGnHn4lWw+LIJFL6erN8QX56DZMp+CsksdVD1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcACWmCRA9TVsSAnZWagAAja4QAJCMmyYeJ0rpM/eEo1s7\nWHxFEHOdK3cfrxvob2nbcr0zS0rVdxNegy/GjAdTQQlkRmrM/g+ez65g5I4E\nknOs7JQl+pOzEDHsyLYXTtVqMjypCdtwO6hLFlv1plzpBuXbId4B3giye6JH\nX84SkfYR/P8gSdlpQPFzRkjsuD4l74/92q/fyUdR5Zu0Poy5jYcbKeMi6zWD\ncBxZSRNSxP5JHUqg7koIKCklda1xPbavJoWJlwvZAe3mC0R0ChB8VKaFTpEH\nwHxRdGQXfpIH8FrGBxBjRACFOBbei2lSUNEq6PvbRbU7rkjhoipRwcES1pfr\ns3LgDntycpyB07i47i7k/QmGXRvGMq2DzIbrPLMWu6ebiXEACIireutywpHY\n4fPCiaBznYU7nc85iQmyplWvclKjvTI+lhQ6/2phUyNwBiJ/EeIkKgHevrLb\nYZqf0I+9Bam+aGeVmR2ubq2To1gT+LyhpP8n5VWuOxJbFlXTjZbPvc2Msv+H\njJ9ZC6CaslzebCTIpoIhMeEHcGhXOe7yDjkQJMw3UauZT0D34KM4CjmTTnXg\nIvXny4hZZcOi3ivCBVYR9J+nSZK53Cbu4CQth5pMBLZgQnvefyG+ocDTcN9/\nLc77m4GQxRYJH5Ya8x0CjJb/buoX2GdS32CrsY+q2THlpbi6hjXfBbw25c+l\nsP7b\r\n=Wv1K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.9", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.0.4_1543513509443_0.8603616725716869", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "47925a590ec85d8565d63c4f56ca6bf88bc5bf99c698763c2d60a13467e68cb1"}, "7.0.5": {"name": "@types/babel__core", "version": "7.0.5", "license": "MIT", "_id": "@types/babel__core@7.0.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "34c55fecf643727b85024fb86c056356815040dd", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.0.5.tgz", "fileCount": 4, "integrity": "sha512-VWQHIzaKiXtZGFkoIWBZBb2vW4tv0opWfPDM+DiNmHx0APxMlSVGy9W4DGTvASZ87KmiyLkNs7eE+0eOt/aEjQ==", "signatures": [{"sig": "MEQCIA9xVZNEDr90K8k76czz7zENFbT3oESnt88veouWKUsVAiAWq65qU0BzBcgXTKuuBi2s1FvsYHVR6PYyMNEvH2DaTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZIcpCRA9TVsSAnZWagAAk0IP/RKStUQN+vwoifk6F2eb\nI4CRc4j7G0ndPq07XsYjCv8jMvUxi8nMBnUDtlOIvet0zuoaYWoaP6zfiGwN\nMCz3SmIBZLnQVEGmYQGVVKLu68dNJX00XtWMY62d9qbFakyN4ZkaaFOv2gZo\nJ76fAT+p+6uptLhNYBZ3cvk15C6i80XmO3N0IT0t23e6D21/2UGujBRE/6Gg\nRmhu3UQXwcQSnZB0I4lEv638ALYOgbHxrJ9ExYEqNZ7hq4wXidUk4X/pQgD7\nM58PkMRL8Vkwrw1MKqJm8dPJpzcJCKhTIOzrAawiaknSjtWO+56v81rJvV9e\n3n8woGziKRvAbf657g7j4stnB+/ii0Uqt6QrBOVSuEnKMKd/BuksJMcwJwda\nzqrTEk76RXs+nUKuHqEsYDJfxVgwOVZ+0OjL8+B7zkaOq0IbQ03SxqQh0RWQ\nj4ToK0uhA/tK7GIOC7EmX5Cg7GNcb8eoUtovuk6q/sYe/+f+bgLbP/aAFFZh\nHVKxD4AhXci0lEDFO56n0rsCTYNF32rXFo/VqG8L1PHqGIOMvcTkFXi5lla2\nR5oBIx2/MOc9Q+b0NLmMSEm7oi2o5rEFIdNRmhBCqEdXvSc2pHxvGq4hSeTC\nGAy7I7qD3Hxe7JT/616i4v45be4AxflC3LDr6YnJbCqQM/wL1TbYtDhJzi4j\nsHG7\r\n=/4iH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.9", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.0.5_1550092072847_0.977217451870195", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "62f7226ef5b3ac4aba8734701c164b8426a65bb05e13568942e121345aba096d"}, "7.1.0": {"name": "@types/babel__core", "version": "7.1.0", "license": "MIT", "_id": "@types/babel__core@7.1.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}], "dist": {"shasum": "710f2487dda4dcfd010ca6abb2b4dc7394365c51", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.0.tgz", "fileCount": 4, "integrity": "sha512-wJTeJRt7BToFx3USrCDs2BhEi4ijBInTQjOIukj6a/5tEkwpFMVZ+1ppgmE+Q/FQyc5P/VWUbx7I9NELrKruHA==", "signatures": [{"sig": "MEYCIQC6eyOpa+u5FfKyLsvQyxrJzghKFE0q+uDbx2AZjY51gAIhAMB6xSU7ZVNGZCdYd6d+zMDht4djP29pZY0Lwfir6uIW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27214, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbuHSCRA9TVsSAnZWagAA0ggP/AkJBd58Jp8Vlutp2+u3\nSFg6u1clrFIUX3GjMtQdOmvFYInkbfPGb/oZtOejwF2Ti/kxmh+z/QPjsSWG\nbcSM0leDwkA97UfFBthXf6aTa1E2CnJbKFooSj7ZNxDDJoXSCaIRPGYPqxUL\nBeub6rd8s9LVJxqPwxFw+Eg2tdYx2WPE4ZUwYKiZ/Jijbi7zeP17jK2NvqXR\nBksXaZNjb0f2N17Jg2qKyeQICuzztHaB2bmv9VGLfVS8VkWTJSn9Vsn40+IF\niruvEBnBUU34TBMcF0CELxzHpVrCL8AbMHM4Va1ufJlU9jFIq/rjzJzfuVRT\nOVR0zmSPVYQPTXMxZDZvxK478nd5quZwFDoPKQSM2h/231cIuE8XyRtc1byb\nfMlsNfcyKQnI/1TdXMFCj+2a0MLDdoCpuX4w01S+Lx9gUnQuMT9Q5NMqlAWa\nRqOvEJXog2zx87WDLwBRM0Xzjp+832AwbQfWXr48ZGlvKOAIY3Qn3KYYGb8Y\nY9cYKIJdHRXx+6VcRo/k8hMoiJRW5EzDxvJUYaz1u6LK9IMwcxRhZrX6OGpC\nB7P76A8yNWOi84G3kRwpM9f6McZeIF/dD07XDKhroWo+mzvUyDLpRV0+bVh0\nDT3i38XehPRhpa5VTKUH88ENsL4U1hD+7pj7lu2LxWvs4KCqAqlIBGbd+2tL\nGdPJ\r\n=nRaR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.9", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.1.0_1550770641962_0.29762338340372674", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8efd0310b6950b7d4c77bc5a14bcaafc734c3f77b4d5aea0bd99435aee21c08c"}, "7.1.1": {"name": "@types/babel__core", "version": "7.1.1", "license": "MIT", "_id": "@types/babel__core@7.1.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}], "dist": {"shasum": "ce9a9e5d92b7031421e1d0d74ae59f572ba48be6", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.1.tgz", "fileCount": 4, "integrity": "sha512-+hjBtgcFPYyCTo0A15+nxrCVJL7aC6Acg87TXd5OW3QhHswdrOLoles+ldL2Uk8q++7yIfl4tURtztccdeeyOw==", "signatures": [{"sig": "MEYCIQCqhpA0OHN06q0kaho3ryxoq9ZTFwM17ReaVisQHdSIsgIhAJTAaWN2OpAeRUKYcwzLdN9yJNL7QJsu29fSjhZ3rjJy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrieICRA9TVsSAnZWagAAxi4P/jHW1tjE2c/WSti3mzsn\nFhgbzpgx4ndvRrbD/q7mHBzrz6g2jHpBMPYNk7JXKxobw9gWhy6X12m6PRkt\nnuiI5pb9flAOjPZTwrDez/cePqqVjMqTr6Skkzor26B+WRzylADDUtGcKrKE\nHRs+w+e/rJv5ABh09EXeJOexKXIpZjJDREruMTrnfeToOiuBQUk5qRYeX0KW\n8yccGlhEh3+66Knfg0pvN001aazG4OLpRKf5g70aPzNs/1jljPW5UMAjKMAz\nNVtGL9UJkq9EQibn4hhi2qDmFrvvWCNao45iuY2EycP4ahg//nPPWSpONzHa\nY+Tay939hSI/GsKgXT0ZNY2tUBjqzj9bCS0JcIzbkHqHLWVb4jN3Dg9F7A9R\np4jTVfIwZF06MZ/AVfz5wPpsQRdP+hUWmmbaGppmfpue1XGeGhJ5Wt3bt7ZK\nCLRVuHibuBgWvJcbtLYMBLvpgyH7fK3ZNjeP92e367VBpwAyW1mOybKhLsAj\nTnVIGDBV3DHcyded/0VwaYEsWYFfd+42I0Cjhwl3Vps9IhJx+88hRM5o9Xe9\nKxNEr1u4ePDcboGU+OGEbkx0Ym2Sh2hClQbw7yM99cnG/8QmVN4CMDJXH4qG\nGyzFxfzreEFbrBQko+Q+6+GvfbcDjahEcj7aCS3gesbpiNdZwBXuqegjHgMm\nBiux\r\n=ySMG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.9", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.1.1_1554917255373_0.9750233609426873", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8225141e6aa116d8b063e5d7db974ba22195874281f9bfbe0bfab9a68bcb5933"}, "7.1.2": {"name": "@types/babel__core", "version": "7.1.2", "license": "MIT", "_id": "@types/babel__core@7.1.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}], "dist": {"shasum": "608c74f55928033fce18b99b213c16be4b3d114f", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.2.tgz", "fileCount": 4, "integrity": "sha512-cfCCrFmiGY/yq0NuKNxIQvZFy9kY/1immpSpTngOnyIbD4+eJOG5mxphhHDv3CHL9GltO4GcKr54kGBg3RNdbg==", "signatures": [{"sig": "MEYCIQDbAhMvdzciFzOvJsQo1MoJIPYRhrr9VPRtr+/z4bUK5AIhAK6TF5wAS3d+Qmvu1BaUXkojeDTJBrKKcZoPTrytalzj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27343, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc3D2gCRA9TVsSAnZWagAA7zYP/iuooi+uZfpvTESoSraC\nctAtgAAqFjf1JveEIkQcUopl6c7tbkh2nDAosGqp+wfRI2yIdDgyKNwC8V0Z\n0d8DATDEn3plM4bHEhSRbpACFcNXxVFDcZZW8+/Z1wrRFEbXu+Z5BroFEWRG\nxj71RihDaangN2IflnvsEv3pf1MwwKiWj8ek+v9aZdVnswnKt4TMZDpLPyP9\nF+4Vzf3gwFARTD0IjuFRiu58EmX3AGZethZ37PAD7DtbsoHtdH/e/M/cJ+He\ncpk37FFdxIl/WhO5ItyxOWvOWp1by2ssnUNCusu6SVI/Zg2HYVSosVq65sWd\nx8oFzqx5N5NH2DIwQZVg8ldBvYKG0DLFH58iiDNUmkCVMsXQDHEYt0bNeHwe\nX3qJOCLRfiGlvzW8lnozltkn/D/OFJ0L+jYaW2sjm48XdUR47/TFvNFgWeNd\njIqqr4jENJGIvW8sa/IsnGRWHuc11ViqCo7s5afP+P0Gx+FJd2e6wU3PiQqX\n0GIoRhreauXyxquyNc6rjvwrJ1r4AaXKuRbZEzZazge/sOZwmv0gSdV8SgVT\n0tF6qZI9WY+oI8L00TbVngIGvsm6H25aGddQFlSX6Zn4aIQValCoBP/X8e9K\ne4dnpw1hlmaEzk+n6SdaTLapG4C5Ub3tKc2wWUtiYpBbXvmYKaOaTYB2+Ol5\nlki2\r\n=Qb+C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.9", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.1.2_1557937568157_0.729868803494492", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8ddbc9ecfefbb1a61ece46d6e48876a63d101c6c5291bb173a929cead248d6a2"}, "7.1.3": {"name": "@types/babel__core", "version": "7.1.3", "license": "MIT", "_id": "@types/babel__core@7.1.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}], "dist": {"shasum": "e441ea7df63cd080dfcd02ab199e6d16a735fc30", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.3.tgz", "fileCount": 4, "integrity": "sha512-8fBo0UR2CcwWxeX7WIIgJ7lXjasFxoYgRnFHUj+hRvKkpiBJbxhdAPTCY6/ZKM0uxANFVzt4yObSLuTiTnazDA==", "signatures": [{"sig": "MEQCIDIJNRsHV/9VQuwOsboZQXmsJt8ud/sa1EySTdBLKrZ+AiAMWDJPTD+kg1wl2Kf7egBlVGLViUWIOv3bDKWh5rWuCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27500, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdb/cFCRA9TVsSAnZWagAAPDkP/1Y2CDObhZ+dFs4u+pSl\nthu5CT0R/3Ikz09Wuq0zLTcUwXyynCPCBbbXkZuFCcjz/zMRPZtwtfAYqF1K\nXpYKcL1/wplql606wAfpD+fpqK4qiljbthGnUTICCdqFJzqoNvrDY085lXOq\ntZRC6K62XyxPtraOxIm0goHz6EVTzLDNsBFNNfI8unfzdUYp+g8gSU+f9nVJ\nkG1GK3F2Zq/4XSiEEJ0tEuF6EuezNujNQz9NbHX1FkGCsxn9/DRC77Ot3Ug8\naWWWRRzlQ5C36uORf1A4T07BxJo4x8ylPU2ww8lu8KYw4lS4bVGqmgKg5hdN\nF99M+j5TVx9whP9I/OUPGRg8Zsd5OTy6Oys/vpGAVcBbr6n8TabYKXYmJyi6\nIJW6AD+Ft5zqjPlAoDTVQgXH+ko27ltCoN8otKVUNrd7rkNs2EiROARDR6tT\njXkEEZWkVlrWAOooFrbau56f32juvbY10qMqM4niV/n43G60U/UbtsOwu6lj\nvvpR3gq5T7Zts3KZ1sG67jCSIFSTnJgxzoaWyIllr+aZMsCXu8oiBv6QLt5E\nyFeqQEpTETTancUNdruIKkiFRB4OZgqh+tAOtSUk/0V8GHEkppnKpDnIL6Vu\ncREW2iF+qZhe9IRjYz7DXCNOepjvJzSJcwjHp8vE38W3Yi4tDfgwNE6IFwkQ\nyrRB\r\n=Ul0Q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.9", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.1.3_1567618821161_0.047531780792570366", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "478fc636b3b91d2d8148bbc98176f96231386926f4af91893dbe95d3812775a5"}, "7.1.4": {"name": "@types/babel__core", "version": "7.1.4", "license": "MIT", "_id": "@types/babel__core@7.1.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}], "dist": {"shasum": "5c5569cc40e5f2737dfc00692f5444e871e4a234", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.4.tgz", "fileCount": 4, "integrity": "sha512-c/5MuRz5HM4aizqL5ViYfW4iEnmfPcfbH4Xa6GgLT21dMc1NGeNnuS6egHheOmP+kCJ9CAzC4pv4SDCWTnRkbg==", "signatures": [{"sig": "MEQCIDF7lbEh+z0JVlhx1ZSpHcPCTDau6uB0DdUqO0yomMt9AiAOy9rpj0VXGHKa/qXABb8u1I9wEE1ImlrN9b8dXU1+/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28817, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeRt0bCRA9TVsSAnZWagAApB0P/0p2DVYH730AEWrhU9rY\niTUcM2V3W5Yqzxl7/ZHyF/K53KDg6Do08iRMhZHd8UwnGA83CUS6l+duxcYZ\nHNrOg8dcv8GKlQ5de9KjE+lpXgF/gVT6fwEH9W5erkCemJhplTyRNFJRjuge\nexYo8iqJPel2sz9+gETDw19Tz2Is/T2U7lpLLaLyBJNmwFuUzhjnNhauFKsu\nb/0+OrquxaPtqgw6vH/Q2rJHEam0ZnTvxpSo8b9ge4hGZCQlC4uSFx6AbYVq\n6GAaNzwLATKEyBWGQidZ2+oI51IxaubCsv/MmDkZLUl6fLmHTotxPk7w9gQD\nUrJfpCgcRIUlYT2+MBdcaD4h/rILbCS6eBVAtL3rD0PKnGTbG7rbHM8o0O27\nNfL/g0kv1fXBN6oYZwtb+rGRzEswr7j4Is0/YxiSJvecFoXrX4VszR7S6PBX\n6xDdj7Zcut6dGbOkEbixVRaI5fenm4IsKGgwh2R1m8SzpFVW4uL1rXIBPdPl\nSD9r33qFisKCruRNG/RCcW9yqA2CHdEPYSDPMC3beb/goiEM1Ayxc/rQRpbf\nw7xX1IOuBjdXH2rFncLVH+VEHmekz7aZ1ek0pGE5FqnfTM5JjPuxT/+Orzol\nNSDLR6ofmmVNdS1IqM7TjM1/jfDNPHveY2IqW4kCRQ3q1rmz87usGqH5x6oM\nROYn\r\n=8kVM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.9", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.1.4_1581702426689_0.23327030172295404", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "571574423c1dc97a75fb9a37066b907800173dbdc95d0c84b77b092651890943"}, "7.1.5": {"name": "@types/babel__core", "version": "7.1.5", "license": "MIT", "_id": "@types/babel__core@7.1.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}], "dist": {"shasum": "e4d84704b4df868b3ad538365a13da2fa6dbc023", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.5.tgz", "fileCount": 4, "integrity": "sha512-+ckxwNj892FWgvwrUWLOghQ2JDgOgeqTPwrcl+0t1pG59CP8qMJ6S/efmEd999vCFSJKOpyMakvU+w380rduUQ==", "signatures": [{"sig": "MEQCIC3kcDHoZd8s0UbRjQ8f2/d8gVlNc4euHA/PKG9BAMaEAiBPnCX5vnmsof7/eSqtdBynccZXiRB25ysFdPXY8QvnnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeTtAoCRA9TVsSAnZWagAA9LMP/0YRYYrz40yesQSqHd0F\n2AB5ZrrRoLxBU3l/v1y1MSBAHdHmZFzBCDuwUQSKWj6YV+ETUjANoPAfMAW8\noOpnS2WMxUltr2QoGfi5kE1Zfq5AjeEn8SSWnLHCOs70y7QE1vfAfW840LVA\ntWpDQbOMRLuHmUd/gWpxcw/WEVHoASpTAUoS9aziBV+5HvhPqiEK4h8uVgq4\nfDspl+5tePK3OBnMhUt0S+MjK474vIzbn3QlEngYOuhxlhcQCmXBVM+xcViC\nucP9qu56ibnxS8l3ONAEFCdz4D56qyU23JIz66vfBTy8NYJEzp8wlg2rK1px\n1ETA1b1dT3YvwhLVESMrNFj1EAZ23XxK/w5VDdhRrT26eXkCl1tR1hLKvnkS\nQyJg9+Is/7wqaG4yYTG8e3FiQ3U01IIPX13UUy7qOre2ZgEVIlyMVIBBUv/m\nEgDPEzJrmKE7whFnbwbVbbqw1NCLOW3T6vlsj6/O4bmeSxa6GHpv/WVPRzq3\nDotRJUBlFhPVn/NyPDkbHd+3OCi6MZKMKQrGtDiPdOFXUAEDXbdBQuem1hQW\nhFed8pAHxvgWGvq/uS86EpeWOjxG2F2g1SrfjorDAPCmd3fNG6IECxXtggdC\nUM7QHhOR+m+nY6aB2CMEIzGvdMiIILEJVywqTCLvKylJ4WEUdfLxw9CYYZTV\nIG5L\r\n=992v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.1.5_1582223399765_0.8712350396133828", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a18580ec20de853eff5a602b70e64d3dbdc2b1cfea48906c2bb1a3b0ee34ab18"}, "7.1.6": {"name": "@types/babel__core", "version": "7.1.6", "license": "MIT", "_id": "@types/babel__core@7.1.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}], "dist": {"shasum": "16ff42a5ae203c9af1c6e190ed1f30f83207b610", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.6.tgz", "fileCount": 4, "integrity": "sha512-tTnhWszAqvXnhW7m5jQU9PomXSiKXk2sFxpahXvI20SZKu9ylPi8WtIxueZ6ehDWikPT0jeFujMj3X4ZHuf3Tg==", "signatures": [{"sig": "MEUCIQCyQOAEVxjWNGFy/QE0d7xRoKr7L6R14LCMNn6cfQ3jgAIgAlj+u1Gu/OffKFR0otM9qseC9FagGzblXizBVCdbYVU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28862, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVXD1CRA9TVsSAnZWagAAYWwP/01MDwGNne7PjQlkYjg/\n4fBfosZbbepW3pmMnzmLEQMdcAm4LUivjkrq3kQTvrZEU/fV9winnsC/FukW\nC3hx5Lp0CIytsUZhm9+a8RDtXaHarZxrtXNbY8aC/eUGuHR1g6BOtm1omF1p\nHHP9Zp5YyOC1jJcqRN+bWP1WT6fXmxI0dg+MZPjgVFsznCWq5LKLI4xLHZaG\naxk+N7yWy72xURxKV1SkbLs9etOZGeYl0UnmURiraB92ct9sm7pX9SWQw47f\nB/K06oDgWxxImHU5h7HCAGJwUzEUfJzYFbbPFqkwAc6I4BhOvOmI8EkGXq3W\na/BTHWENP7HmMuy6bBbHhIyVDxpW+ymqfrVHq+d18A2ztpGbuSc+G3CbZcKQ\ncbwRdkbihRmRXp1OvooO2KK3wLV9s/iRBUMeHm1Flr8NFR+7fYJTdcoxzKSL\nkMOH6xOcs/6j20fsxWteZHW+5JQIgKCbcU4KcL2SIGbZrTgtuG38abEFRt6c\nCiS5FH1tXPOYG/L/j/uh7ViORwxpObpazvawSRL5LdDlVNKAd3qlJLMdc+3t\nxwcuBPEntvtg+Z6yTDiUlsXF5CcXhEGcEnfIENO/APeWpnbz0aQtobvITOxl\nxAe1MIRH+ACTakhseLzS+xkzqaN8rCr8+UMBpKf0ZXmkD2RxBDO5VjNb5CkZ\nezTC\r\n=6r16\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.1.6_1582657781354_0.8513271038586718", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "894ec56123a8d17360888ae06a5b3aef80419218f588ec4750f4690f67de012d"}, "7.1.7": {"name": "@types/babel__core", "version": "7.1.7", "license": "MIT", "_id": "@types/babel__core@7.1.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}], "dist": {"shasum": "1dacad8840364a57c98d0dd4855c6dd3752c6b89", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.7.tgz", "fileCount": 4, "integrity": "sha512-RL62NqSFPCDK2FM1pSDH0scHpJvsXtZNiYlMB73DgPBaG1E38ZYVL+ei5EkWRbr+KC4YNiAUNBnRj+bgwpgjMw==", "signatures": [{"sig": "MEUCIQCeg6bXJmMfVGDbleayOSkj8CqNkcN6tHEZyv04HFdRIAIgWR/DJQ9rvRNLHd6q9izOJDMxGIE7nIgsWQK5IuTGMe4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28859, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeg8wSCRA9TVsSAnZWagAAMzEP/jA1QBSyr26cv96AWarl\nMQZNASsLzmey/FlsYPppWiS+lmpHml4pKoYWwi5Drw0/iUzLka3N5UmDd+Y1\ne54QxyQn69HF49kkrh+3oVO+DAkGW7ykq2q8q0lvgKXsyM0m1JD9n1KLo+SF\nhsXmSCYhDvHB/sqhd/TsSm2uW53vdWEexy9wixBc4rGBMESTEsyjMeU3knjN\ne8ZgpT9LlIlLvhiVkvLTawno1hWbXgGF3v8tpAG2jCWuAeCaxF66mp98dzMS\n4ozj7TlVhKdpTg0Slc7RdWq9ewFZplBFzLhdGHT3UO04lOH78y9q2FHU6hVU\nEzwiSmvdLvxpDLDcb7FuyttwrPujVzeQTT/CG7M18KVAy/jdv6WHXLpknalt\nacPbFbE3fiZ99WLp0BahtdYqEyu1dji/ohsZQM3l+MFCl+sFjZT9s906tmPS\nISTfmW+zqpBRKGocVF5UuG26r1QnXPLyfCM12Orzj5KGXACJmBm7fPT7hyej\nNVxFZHCbKbhp1+qxDHomAuS5VDwE9XAaKl68xIWew8Rob3+tYdoespN1MdV7\n60kWvEYOawdAdTxTf3N8iHbrBtmUuVGtRXmI/RzH2hV2ADmVzU5hNtoJbxg+\nepG51rbyKGF8UAz9EEXe9xlj1ivxci4yzNwFT9Ai1a2RJSfSOrwy19rKU85k\nGkLF\r\n=YE/2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.1.7_1585695761609_0.4562745408691944", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e93a4f11a93e3ba496227db0c1bc869c27b668ca27d7bb8224401ef44c4b3d1c"}, "7.1.8": {"name": "@types/babel__core", "version": "7.1.8", "license": "MIT", "_id": "@types/babel__core@7.1.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}], "dist": {"shasum": "057f725aca3641f49fc11c7a87a9de5ec588a5d7", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.8.tgz", "fileCount": 4, "integrity": "sha512-KXBiQG2OXvaPWFPDS1rD8yV9vO0OuWIqAEqLsbfX0oU2REN5KuoMnZ1gClWcBhO5I3n6oTVAmrMufOvRqdmFTQ==", "signatures": [{"sig": "MEUCIG2+ZqeDAz9dfbOdtVmGI/8g0pAeqH4llW/CRHnwJKW3AiEAzJBQWXAExxVKcGCrC5bo22gNIJrGR3KsdW/KLyCoP4A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29547, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1ZVJCRA9TVsSAnZWagAAUY8P/2JZR5lB1/mqNq/cpPa+\nF1iIWmOlHTFOUEr6N3lccS3W5kggShr8NRPgzY0td3xt4zDn9WpF/JeQrffc\nfisfbo+6ULs6vrpOdsULPtSShy4oSLBqOZU47jKWFgs4VT/YgeztWkV+xzh4\nxNJmef02phSYEwl5dZeDfPieEmA+5v3GynvXg3FzU6D0xfP/F6rmvod6OOO/\nWKyk+WIiMg27tAb0n6lUptPNsyBfbtwIVygOFb3bZmpfYWG0qx4FvxINBvoq\ndnpOuQIGsaVMKb3icMlvhTUg+8kY4Vr3GiNkFT94SEYj3VQrNo7wDnBG73Xf\nVcTnnAe+GFvwha87U4bxuO28EDlcb9nac0Xveq7M7o6eeab/rUNC2oO/rz2B\nRDXEmA+yGcIBLKqSGl5wxUcVWLrA9EZyrVoyGaR4w3is2mcMDzJUHZIYQ9tI\nTYYDyLIJc1s/10pP1fkyTeYGhAB8XGGd8bPcQklO0OQ13/D+6MX65a1aZgr0\nLCvmQjaT8NVMKV5FJfOtXREERNa3pCaxRIOlf8lXgQszaU3vlhafwFhZNJI5\npLflx4Z2xqLjmt3XkDjhREC8WAqwn5x+OuKCJKMdS/+rdDrxAGLJFu3KpcTo\natW8bqNMlsG8gQ5QXnWVY7oPX+B1mc63nGf+Ng6eRR3RHL72k1q8pGS8bDas\nYPZF\r\n=RFFt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.1.8_1591055689306_0.19321315819218787", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0560b3e9f5acd92f5d9ba44c76b796af25f4196de9355a27718400bbc62b7c89"}, "7.1.9": {"name": "@types/babel__core", "version": "7.1.9", "license": "MIT", "_id": "@types/babel__core@7.1.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}], "dist": {"shasum": "77e59d438522a6fb898fa43dc3455c6e72f3963d", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.9.tgz", "fileCount": 4, "integrity": "sha512-sY2RsIJ5rpER1u3/aQ8OFSI7qGIy8o1NEEbgb2UaJcvOtXOMpd39ko723NBpjQFg9SIX7TXtjejZVGeIMLhoOw==", "signatures": [{"sig": "MEUCIFJPkyAhcmhtk1pehE7juhruYMuNmshygKeMWXnMC6lbAiEAo850ICQnhKtHsRaXF6qGGHdjMtJqBFYyn0eHi3Xqlw8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29587, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7xkUCRA9TVsSAnZWagAAUGQP/0a2Ij6HICMQrzUS/TdN\nUzVlYBbyGbQdBWEsVLeFlW2Z1p9khewdO2FTLknIMff4VHjiTtk0WPbKa+d2\nOkPrtF1AOB9Z1tKD//T5GEV30EqnNOAGQNiAPJEGkG3xNC3MGqst+SAD/yEM\n9Eo6qD3KB6r9aCj11KxzYwGDomcSe+bpw4BeC9U1tlX40C2/ipyYEc6ytwCT\nLCWb3QNnd/5VaaMoxxKcVsqVBOytKhG7iPiS8fnHYxthQ1lEGJ9gswdR9lBV\nY57M/xAZ6RybfP/j7MnOvYJKhpml880E8X7D+i0b8w8FyjIPqOyAEXsgX+gi\np11XQG7RLlL+7b0rqqGBJ2bQygWmG6ENL7eouj4MOi0dkz0Cy39cIWyhoKE5\nQNCHbifME2hHSjBIUh3jxZR9FH1Dx8+XWNL3nmr8xtMObNwXlUSOpBoTCWiE\n2Ku4OWaWYpEhoXYE1+KidBoRt3V+V0eevCSCuY5K/E085Qpm4e0vHpkJaWNw\nq9H5Ds9uDlYssvdTEVsmmYa/r1xQH0hlGcv/Vs0bR7IFANoEvqnGd9EDIqZM\nus+nN9g3ltVN2BM5pvbtlbC19Nd0me5B94T6LEomQFQWMZMlSOnaeY4wvfGD\njr5vQS5uJdTCVhBcRpD+J8m9Jbhob2/Ts7HFyz7NxJfwuw0dJVHGczzMeA87\npRZ/\r\n=CIe4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.1.9_1592727827750_0.5221203253257896", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "26a4dc5658200c6408c23432c1f75108e2a178e8bc48479ee219337f00413d08"}, "7.1.10": {"name": "@types/babel__core", "version": "7.1.10", "license": "MIT", "_id": "@types/babel__core@7.1.10", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}], "dist": {"shasum": "ca58fc195dd9734e77e57c6f2df565623636ab40", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.10.tgz", "fileCount": 4, "integrity": "sha512-x8OM8XzITIMyiwl5Vmo2B1cR1S1Ipkyv4mdlbJjMa1lmuKvKY9FrBbEANIaMlnWn5Rf7uO+rC/VgYabNkE17Hw==", "signatures": [{"sig": "MEQCIEC/Elst/OJVzH5VteR5UDTyax/HZr/de8BUnRsDyqhVAiA8eOdlGFoPiCUxM36tJB58/stUAFyp/YxHBH/iskY/LA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfbQYwCRA9TVsSAnZWagAA2BUP/RfetXLQiu8cZzANCVL2\ny4zqt9LiPfPuutODvskwnV/K+r5bEmH9wCx1W+NF3BStLjyE7irDpxDcgnm3\nSycJqXmYDK8GZmmFyKLEZ7iyDN6u4hQoBZkNqDzxCjPZCtHPBm07mmX2hBpU\nb6xtfTplrsHINMnqN2nKTaoooNzQWTUnyg6RgBrGd1Egks9GlL0/GjEI2QaG\n2hLtr6F12WTQXhLm2PEZ/An3Ju0yarSbL5VrSjEnrdAc1olER3ECK/l4rXrS\n1IfJFkc8lq/EMq7n7+yz8go4zJxPOvpVCjR0c38+WsEnMKUBHdw/B0PiI177\n0EuG1+0uWVJbIJtt+aeI9i2/dfkIG1MflHjNTFYl7PSDf3U+RT3CcKJEhzps\ntWWY3ZddKG/lilSSQgfJ5Cw7S/cCPVPEeV96GAp0c2G3mq5gfnNmcVmLjxGr\nnisHAVQT5k+jJBhLSgbCyAhdPufTs5O6lluMuwY9ODLfVlx0XNLKCX9UrrU0\nSnm5upFV6ljEUyz4/VS5/wq1AfVGyev6ddgfBrI+YCwZyKy4OI9ET6CUW8XF\nL/wJIGEHBMjDN+viCRg5cTQjiVxOl/fD2xgRnKiAL0yn8Ixbd3x8lwxJI/uy\nfE31hohAe6o0WOgaVFjmfQ0R41B4AaltilFiy7sTDNN1FtT2ItrwA8c9rxH9\noRc9\r\n=nf4K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.1.10_1600980527392_0.8118508456850371", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "1820a9b81a531283d2b2f0b6f3ff7be47c310f20aaab901401b1aa2e47c9ca65"}, "7.1.11": {"name": "@types/babel__core", "version": "7.1.11", "license": "MIT", "_id": "@types/babel__core@7.1.11", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}], "dist": {"shasum": "7fae4660a009a4031e293f25b213f142d823b3c4", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.11.tgz", "fileCount": 4, "integrity": "sha512-E5nSOzrjnvhURYnbOR2dClTqcyhPbPvtEwLHf7JJADKedPbcZsoJVfP+I2vBNfBjz4bnZIuhL/tNmRi5nJ7Jlw==", "signatures": [{"sig": "MEUCIQDKev3Rsq1a9eIfhyWqwDb1t3+B5jA8UhpRWkFBh9YUvQIgLH1HrY964qRsf4/RDXIA9bCpxm/CAKxiNlKT5KsRHhk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfm/XACRA9TVsSAnZWagAAYtoP/ibxA3hGNEgKxe4RWN50\nakSN4jJfFFafOIncjWBMyAMPoMCS798BmwO6/Dk724PCwALfVN6EG/9gTafo\ny8OG78RyDYNI7JDMwEk+LFRgh7sEFhrC1LWoAJLV0Q3NNAoic9hg8zuEeAED\nXbhrq/qpedfZDHv84qC+bAgOLM5/2/bJRQKHKKRmq8utTmTBzJRqWj46kdBz\nMvdYzyoys8zNL8a9dgB8AL3h5MThFl2m6LzeOD3cEvYgGEmgbHJ6fQZYf0cI\nZrFdeZv0ZyEkKJMzKoqAjqoDseuJOfk+/0xUV1E9NcXftG2SMtGi0H2JdPll\nmVY8ITYJRxtMglkmcQpnW+hTjEC/t9Gq+I7bE3h/jUOXDqYTIFcC8xpMc3IW\nP4lHLpIyLk+4oJhzM9lSkc96pdmauJjcQTG/Zf9VEnbRaJ1K/ZlMT4X/Nnko\n6NRH3Is9HOHzhe4uDNLcnabKoAO76PdU2vsR9tFeKuwOSF62buAO+ep5PmBY\nF67Kt3d41RwpNJ97XsroKZxVG5pfWpXxpxzyZ6r8DShxHB1PMu/w+BjMkNGA\naIORZyJP5MBrZWKJeyPBFcZUkcF+JNq/D1MlrnqqCU9Zu5hyiIioyPcboCLC\nFI+kHQ2J91EBqArqTrPBvdpWm0POmGidGaygIfqNqV8Akj4T9oyi0SukSVCY\n4ue+\r\n=pM4m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.1.11_1604056511859_0.4624563588670658", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "90897236eb0b3ed86d67bda2a4976592c99e53a51b2b9b2244a0a527f7ce6c81"}, "7.1.12": {"name": "@types/babel__core", "version": "7.1.12", "license": "MIT", "_id": "@types/babel__core@7.1.12", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}], "dist": {"shasum": "4d8e9e51eb265552a7e4f1ff2219ab6133bdfb2d", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.12.tgz", "fileCount": 4, "integrity": "sha512-wMTHiiTiBAAPebqaPiPDLFA4LYPKr6Ph0Xq/6rq1Ur3v66HXyG+clfR9CNETkD7MQS8ZHvpQOtA53DLws5WAEQ==", "signatures": [{"sig": "MEUCIQCN4AU/6EySVIlt5LlNO7jF80BIvifAZtMFrAE5RQ5uvwIgY3drrAOo1VP4E1qx3NpxxZQjhbq98Fs4ZR84PYC0Z6k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoJ8GCRA9TVsSAnZWagAAzB4P/R+ST+N80vAfPhCNqDVq\nWevN9usycqEIRe9dxd/hGOs5BywyqubNiZ51kWDwo8VMmCb1UsDU8uVmUlFb\nqld6lrQaXbigUOO+FFly6ndcsx9gzqK9gnWuGOss6zCp64WcW9XMDn6Y0qrx\nk2u6IRvBBcAypgKTxrjtj/F3aZYMEIUDFPjvzloRpN3rS+L19VFlGKXy1TWI\nwWxjnTJIRrSP4sD+R6H7Y5wLwkTFuE0StmXzcJ1d9rxAx24FRcaJ3ZKupZns\n7OeG7fWpasygGTmLQMZbWo/Fq4D1DAI3vzTLJi0u64QcH0n3LHDv5BTT956x\nQ8AlaU/fUncfBoemCGUENazia4b6wajKUcgJi44qbQDlHK/RQeICKEQdyohm\n2gNfpmQh7nU2lpshNaFeGEjRj6VR6fBZPHbhd751qNKDzhclTsqfKOqz9Jov\nxIFmgNj1yeWsKN/eVEG3eyIoZExGcXgaKl5BMd4ew2ChsE9Ysi7NvbVrVUFm\nntQTRKnBfy16LeABMGlC3TZwc/55NBkupleB6VdTsx/bpvR/vRaWqcG3Cq7J\nHP4KnnpwoMt8OfRfkusX8yF571riaKL6AmYKJ8bpgDDbYMutYoydMuuE+OAY\nQzvx3deoFncbZo2sg0HmNo6UsquURLJtEfurIXIMstf2ixEQwAbSZb/XQRoO\n63rB\r\n=Jd9v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.1.12_1604361989569_0.9948017093556467", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8ec535b0786ce52da85cea1c495d52fc61648456b3c62b2b996580033cb0f196"}, "7.1.13": {"name": "@types/babel__core", "version": "7.1.13", "license": "MIT", "_id": "@types/babel__core@7.1.13", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}], "dist": {"shasum": "bc6eea53975fdf163aff66c086522c6f293ae4cf", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.13.tgz", "fileCount": 4, "integrity": "sha512-CC6amBNND16pTk4K3ZqKIaba6VGKAQs3gMjEY17FVd56oI/ZWt9OhS6riYiWv9s8ENbYUi7p8lgqb0QHQvUKQQ==", "signatures": [{"sig": "MEUCIAiSUG/E/jvAlG5IDgWQjbFZbb5TeX38TTm0ivDmmNpSAiEAymq+++Iy09vcDnyWK0srZDjeQCsBevO8CJPyfK2F9OU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29802, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgT62qCRA9TVsSAnZWagAA3mkP/0pml9sx1fJ+kRV+mDlR\nqw/j4G/ZtuGL+1sAMmmkm4LbbAqWrfmOB4qmdWj/3SSJnjQEo6j2JUVPTb9w\naIB9IqCU+y7rDpuZlOuLrdM9NjmK57WP6aK0ybAbjJMopsAwMTglCgaG33Bz\nMePPIpCrOrb6gHJUhwJ9XYnesHrl7pdEWmlpK1zssxAN3YXzqWepROokzXTK\nwOCOMsRVho6dn/nzESlOvupz1jg6IOyzCiDNZx2hkLfDcUj5VvCIaT9/gS1Z\n1Is21zOkfpH172hF2oXxVEVPN5K3M1Wj6Tpu/+LjvPb+ZhoM4qtn6MxT0Ad3\nn0aA9BL++dxqE8owY8JfZit1dU9AbJPi0XAIfO1tMMLDKQQwv6lzp7Q+S8o+\nqVOfeaqlfrYECUUR19+FFJqzqBuKbB2WfxE222qG+5ZkkwCZaH7AAKcXAggY\nY4qK0e8Dor+Wf1voQGKN0Mk8almU2MhEIE10QyQOnUESB/KTwZGQDAmvoPGP\n7ku1QruWOTtZxNFJOGr5fpBN/kJWIt4sni3ajixx6jJbtdAtEnfp4x1lg0xY\nAHHfiK6ZKSuDIrsjOETORAn+JgeDC4vrpIpTJoAVm+zY0iyAEfzh1PDkAkDb\nJIs87okylB3hitMLxUXfAk8tcCMa03FTpPuCuunWHREvwfKsa8SYU0C9/DHZ\n6AMw\r\n=gh+k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.5", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.1.13_1615834537812_0.01407079401309308", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "34b56516a90cc9dd735c7d4091410205f3745420bf5a00c8d27e216f9c06db78"}, "7.1.14": {"name": "@types/babel__core", "version": "7.1.14", "license": "MIT", "_id": "@types/babel__core@7.1.14", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}], "dist": {"shasum": "faaeefc4185ec71c389f4501ee5ec84b170cc402", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.14.tgz", "fileCount": 4, "integrity": "sha512-zGZJzzBUVDo/eV6KgbE0f0ZI7dInEYvo12Rb70uNQDshC3SkRMb67ja0GgRHZgAX3Za6rhaWlvbDO8rrGyAb1g==", "signatures": [{"sig": "MEQCIDb6hYe3hVR1jzlcqv2yhSeNw9yVUNkCFzEL8tn1DiskAiAHSyILXjbIUYAA3XOzLYGDlQIfNpSkxrzXtt0V6eowjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30186, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgVUmICRA9TVsSAnZWagAAC/wP/2HukynFtj7S+oHfgY2t\nRgxDtUdGn0DMiBCUX+mWZ/dNp+FKmKoyGNz9quSwPGTl1ZUSKGBVOAIHyGAQ\ngPi/JwbW88nIwex7Xirh9rY2Cl/uJdW+ekdwLyZY7s5utmy++yyNJ/nC252Y\n0PDMaywrDz7iG6mwVtOadNE8kEjndIBp+PfENo8Ycz6CkGW0DRlWmag9BqIf\nne6NKoSP35dRHuRi8+868QAjHeux84KnjFlS+xGQRB3Gw8Ozb98oTLC9XjmG\nNYlluFiVxE0XxBMKVeM2AqaKSRY3rZARNFlgDhsh4SZ+kNXTKIfO2SJXBZry\nhjg06lW5nWOXYtW+b+PG62kNXAfjqOuzmbJslh1C1Fr3+mu2/igZZPtm1NzR\nsJWU8ToDLkNmyteQ54s+Xzl+G610U4DsHaA8r2nFf0PcqKqQxq9pRjJi0W6r\nGIZ3YeerTRk0wOIQOV8PS1Os1J2Xbce8z15FTgO4LDoTUa3OhQUmxZ94k5SD\nvuCQEMIWf8OX61byGIk7IVrW4i8gCW0uR49zZG9LkZlpEXm/pOQAtN5iEOdm\nYP4bjopXn8n0F4OfL5XdVsWKn24+S4L01llOneJny62zQAS8I/RNoWijdtli\nbaBAYPGY5M/Py9TwUjiV5Yon1S9DtketGSi18Vutu/ggmBq1u33hyBKFR9U9\nTfh4\r\n=bPKn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.5", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.1.14_1616202120125_0.06739980098401599", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e6c8d52c30228c1da1d79e9c593356f1ca1eea6ce606faab9738082c81a3c567"}, "7.1.15": {"name": "@types/babel__core", "version": "7.1.15", "license": "MIT", "_id": "@types/babel__core@7.1.15", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__core", "dist": {"shasum": "2ccfb1ad55a02c83f8e0ad327cbc332f55eb1024", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.15.tgz", "fileCount": 4, "integrity": "sha512-bxlMKPDbY8x5h6HBwVzEOk2C8fb6SLfYQ5Jw3uBYuYF1lfWk/kbLd81la82vrIkBb0l+JdmrZaDikPrNxpS/Ew==", "signatures": [{"sig": "MEUCIQCB36O0o/ydK/XfxZgDNcIDrONaMJKntE1HPWKwWNn+kQIgFzZNMGPRO7/76FzPjUrazvaCKnV3CDgERH3CD6ZcTCg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5J3ZCRA9TVsSAnZWagAASocP/jvkfJP5JpDtIJ8N+dwu\nmwp9Jk2YhMhzrqreCrsdcWWkAAphKbwME2GJSHwO2xlK3ptzL42K86vrQ+NV\ngynBc5bt/uDl+bu8gUq2eqlduJ2T9bLGH+CV/zg4Bz2hVDT0tJp8Y5iFh0dr\nlZ/42xEDazW+YwH8esXkQa/7ThOmiUNREbdZvta1syOTWEgV5YBzlpmWVLGW\nw951HjFPh5m8jLb0Sq6juqyEpzsWFRjG0RSUHxbaLD6A4TeUBrGvhOiexP/u\nfr1l+jEobBvTlV0LOuMy24fodwkEjNGReKy7BUTTEYzT6XnekbyIfPCtViY1\nJsTYnzOjRjVDcVPH+wmNdGy3IbP2EE8is1HkhcJPoxqY4Eoa2aPoOM2N3PIo\nhd9/xnZChqCAO6OalxWCMW3hMFVhAuqmRFGFlr4jXFm5snkEh4dNHTgIPEnQ\nlgWhQwcufn2nzo/WDLoyLba0JRqhgYLDYCsbd95PwRDmQAa6QLBUN4UDctGL\naOhgGcXTQz2f/zLgATbj8JKjaMKUfwF5nQOOXpSzntUwpVQJhXc6QiWLgWjO\nqhtU7yVK2j6edss60ich9OWlY4NB0WO4bPrgdhNMU+h/gbfqsTxS4GVPyrVw\nWVwYE4pNjKb63T4vYJVTKBcNLUo/7qx5ZMXqlbk4jvK7r5SkV0tbuJkvLicg\n7bu5\r\n=k0nK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.1.15_1625595353168_0.040623915680160216", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a5aa1b55e58271777246d61dff312f75c35f961b1aa41a4c88986b4c73271542"}, "7.1.16": {"name": "@types/babel__core", "version": "7.1.16", "license": "MIT", "_id": "@types/babel__core@7.1.16", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__core", "dist": {"shasum": "bc12c74b7d65e82d29876b5d0baf5c625ac58702", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.16.tgz", "fileCount": 4, "integrity": "sha512-EAEHtisTMM+KaKwfWdC3oyllIqswlznXCIVCt7/oRNrh+DhgT4UEBNC/jlADNjvw7UnfbcdkGQcPVZ1xYiLcrQ==", "signatures": [{"sig": "MEUCIQC23z0PDnBwR7YjqDRUwvCZV+dnRgSYGhbSskYzVqHCnwIgQeChTUWFI6ErAHRgYBWyi3aEHxPfDsWUCKOmNHhNLVo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31066, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNnrxCRA9TVsSAnZWagAAoJUP/A5WocQbaH+OCtVtVxJS\nY1ImRdbRgUzVp0z7JCNT6nO3WGIgkVVBegVNtkpUCZ/zIZ/JOGrpmo2bALMT\n7ne08njL4xEYGF39HdVtaThxoeRbjNWXjbRPyte72VIg/2FxpTBLwcsPPYPa\nK0IetyyUoUbNMuqvp+DY7nhHfvjh/L6nNuxRPSW1IBVQbt6hteUYYmEcr5Yc\n8dTzh9o0WUwrl2hodI7XFa/d90Hz6Z+tWYsT1pjyNQcU4Q6YoUd3mrOaCsLk\n/99YMHBGZJiS1wPsTQACFnI/zTSFScQ6fVzWXWzobeS4UV1E7QuGbzggwodD\nccqeQALgTc/ocnV1KHGfB+WLl8LwlPWikVmuBoDL6HFl5vfxV066BvhsjAhz\ngy6ST4gb2SESj8HpzgSMLP/NX/3H/ZQlnkoKdTV5qSTzcnNdH1YlkD5jPYiy\nSkoi9VyioefUVs6USXFUGIg2+CGhGiCq+lB6QRaLOlcqh4aEDUk+YXF/f5hc\nSY6k5MJv/RESVr54corZNSoXqErrWlCTeN9B41sFOus1PgKmtsXJ1Lh8lgMC\n2npNf0CaoV1Iu83f7u9W0FlAqRe7uka7ORkUMVGR0qxYhEK4nFit0Rkt3teR\np2yZ+xALMv5/14+s9XETYP5v0dcfrkBQNITvFZXvPVHZMKZrG9yQhmbkNbyH\n6QlA\r\n=Bdcq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.7", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.1.16_1630960369247_0.4887570318916896", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "49cc8e879470cfa14ca4c8ca74b5187f116c1ddf97c19f4afe5b34114de28e92"}, "7.1.17": {"name": "@types/babel__core", "version": "7.1.17", "license": "MIT", "_id": "@types/babel__core@7.1.17", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__core", "dist": {"shasum": "f50ac9d20d64153b510578d84f9643f9a3afbe64", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.17.tgz", "fileCount": 4, "integrity": "sha512-6zzkezS9QEIL8yCBvXWxPTJPNuMeECJVxSOhxNY/jfq9LxOTHivaYTqr37n9LknWWRTIkzqH2UilS5QFvfa90A==", "signatures": [{"sig": "MEUCIQD1bw2LxytOLfIJAFHlJHKMOASqxhPQX9FxuPqDV1u+xQIgHtSdiga3O2i8kVj9jqK8ruNVQWCDX17Kyixuk/riiDg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31132, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrpZPCRA9TVsSAnZWagAA30sQAIL49jz6JjunOFQTy41k\nbab9kG5MNMCvsmgvCnFlwHHXUO1fDUsQvmF1Q/B7gwSCssOFfmzWIdbUicei\nNb/AWfj8VmYmdOBYDTiMxL+UZq5K6nmedLoASWoPRkcCR/zovWw619XbkBlA\ns6OkN4OZBxNMAQmtAln/KfidipxMqWqFJCFErU0mVd3Jr35rAX1cLntq92nR\n+Dr2V99fys6djuq89fEcMgIAJDeyljMCP0tPj73I3NSyiKrH5IVr6NKH5Rk/\nSYrzWUprnaRI8CKC/IzbnAHahYxDpUl6TJ6L4EUot8/mCXVLLDq+UTDZuQnr\nc1xPYZb26Eskpz3zMKuObYKd0oAr6SkTHiiFALVTR01y3SgVoyBGKYWIfYzV\nG+20Q+MJG/MCqdabLvDSQOugE/cAUHov01HuPjobJkJOQ9rFP1JMS3848vG6\nDnXUrvoA8rROiDELZoszXRYGV3nvEk5J2WBmn5rYnvEwl/jeSvkWTeV3iN1S\n4FH5+ga8B1/U7n7yPleevPJO6wdm/v3x6ofViCA/gym8coRB466mH7RFKP/9\nVIkjxLXQPTqQxYkbrQkXN21vsXHuPM+NmRIExXNqHR/83qZw8tDGxn8DdEcU\ngzdnJBDLaXCAIfjqtbpjeQf/Ih8VmJqAdSr87k/6llGs+2+DeXuWhUCm6s9p\nl8xc\r\n=SG5L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.8", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.1.17_1638831695531_0.45181259435171883", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "503ee27ea45ac43a667cde36107fe4242d077ac2a3cd9c1cbb3b8bbf58fce397"}, "7.1.18": {"name": "@types/babel__core", "version": "7.1.18", "license": "MIT", "_id": "@types/babel__core@7.1.18", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__core", "dist": {"shasum": "1a29abcc411a9c05e2094c98f9a1b7da6cdf49f8", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.18.tgz", "fileCount": 4, "integrity": "sha512-S7unDjm/C7z2A2R9NzfKCK1I+BAALDtxEmsJBwlB3EzNfb929ykjL++1CK9LO++EIp2fQrC8O+BwjKvz6UeDyQ==", "signatures": [{"sig": "MEUCIQCekZkjg8r19LKJHy17pGeWViNxrwSTvYWpDsp7s/zZIwIgd4RUqEJeudbVrY3UaxKwqIZmnt3gvzQePyAAqYUJD0c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhz33+CRA9TVsSAnZWagAAqnEP/2CbuFpUvDAL0ZSGqSog\nv7dQKyp4xXkZq4oPgowpCG86TSxMtGGzmetGdRgjXufSX2kjGtnldpeoRIFM\nqiySQ8lTxUHdg//OJwMRSbagUhPWNBqUqQddwKQESnWKwU0sau19FrlrtbCO\niJbdVyF5dBnf0kRWa+qMiepp2LlQZ1cBmLqJIE4CgwAl3SFzHXwpFY81JW18\nMRxrMiFcDkFHSR6gpUWZ3QBOhQ9dYfdFXNkcbl+Dljj0LCfV2gParnFUaOb5\nA2A5oarXd/E/0r8c0rkb8IlpCaABvH4ULxs9OUJtG9j2FU7Ov69ofg+fR3pa\nrNVh25cex5puH4ye9p/yOfNqs0oLVRTRKkKPSUTZNVdo3DjuoV5XwZzztJGd\nxXLc3A/IdnkpJgvK005ampGIYLxXlTAKmik1bLNYKcxpXHmTk3jXEl6ZH+Vc\nBAmNGK5jPkwaXk7KC3eaQzdLpw2rhn3itiWaFPwyLdCU77mFe9eN4rnrJ3w5\nAz6UtYpAO4Rq7+LG37GcoVT5qkd/LiWcbuo2yOjDAddSwZPsXOnjwcm34xPv\npgzEaXSofuCtjAaEqPRYxJlRkGcUizJGW6DEEN/OYyvBn4T1G0fRmZZeYBg2\nnBZ+CNul+WdS3JlFmojsZq3HOIlhzRh8S1eQ8zMWQ+kjjSFP5QkA6Ac2mtMN\n/qxN\r\n=NreN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.8", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.1.18_1640988158842_0.8689607126559926", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3310d05f56f6ab3fd792adc1e156191125acfb51b18820e9869ceea2c59972f9"}, "7.1.19": {"name": "@types/babel__core", "version": "7.1.19", "license": "MIT", "_id": "@types/babel__core@7.1.19", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__core", "dist": {"shasum": "7b497495b7d1b4812bdb9d02804d0576f43ee460", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.19.tgz", "fileCount": 4, "integrity": "sha512-WEOTgRsbYkvA/KCsDwVEGkd7WAr1e3g31VHQ8zy5gul/V1qKullU/BU5I68X5v7V3GnB9eotmom4v5a5gjxorw==", "signatures": [{"sig": "MEUCIQCHurs6z62BqJVPMCN9OmvFSKftZrvIC/XUQKyCQWKB/wIgANHfMHd4L96XJ6BFQzWkbm7Ek0yEEALLIRNSyxQdSS8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32265, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiNtFdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZmg/8CNNtwkGHsnJ3Sl5q/UT4kae1zDqb1A+vdYz1/r8N6ERRP00e\r\nXAzYnWDlhCZn1u4pDkcQDF1+5+doP1tCIvMIG9Xz5M+LxPrwWQt/ooZ+NKkz\r\n9jHxp5UBcihHBbo9SAQb2t0zp/Kbzn4CWei9OOF2oGxOOownx9PARlbm1E3w\r\nd+WvnypLe0QfRcuprDlSdgkTf81JaZbHKm4gxUZr+iHl77b/b0+yq4zliaQX\r\nDqbY63trnB1P5UrXEigvC6ORu68Kq4UGm3SORf6oi7ezU7bj3XDKMhY50FJx\r\nnFHaNp8xNtVVGMz+O5jEzd1Ca/+a00uZFTfp9DmKGh5AT/mmW1ixikjsBI2C\r\n9hDjijksMPsXjcQqXN7l9OMg1YFQ4OiamKZkew/JLfeLDFGlJ+nRkmGXSKey\r\nT3hTgkiLB0b7ytY4vnW4P/Tdlg4/0QrmNbwXVZK9U7CzadZKsuviZwU7G7Lb\r\nXn+mnmzwiB9Tf4mvS4K5PQsdOoSIMEHsOhXFpeVZ8ZzRoEfn27l1ErFUQOZM\r\nYcQFNMSo7W8zfmn1+OlElu5dtPg6gswKsVZyymDF13rcT2E42WzUxlM6KHlX\r\nbUvt+kH7fQ+Ae+NEGgbUy34wEB+Xaq+uQeOgbIc3/Dt8VxOomUgRaMOgMUwQ\r\ng/5761O4e4w5iVbEo31c4U7s31hMAD6VrvY=\r\n=DBlZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.9", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.1.19_1647759709781_0.568628611351667", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "5358ce595fd2768093c1ef80a9b4e731532c9c96f303bea08913d5e19a166a90"}, "7.1.20": {"name": "@types/babel__core", "version": "7.1.20", "license": "MIT", "_id": "@types/babel__core@7.1.20", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__core", "dist": {"shasum": "e168cdd612c92a2d335029ed62ac94c95b362359", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.1.20.tgz", "fileCount": 5, "integrity": "sha512-PVb6Bg2QuscZ30FvOU7z4guG6c926D9YRvOxEaelzndpMsvP+YM74Q/dAFASpg2l6+XLalxSGxcq/lrgYWZtyQ==", "signatures": [{"sig": "MEUCIQCblW7vBL8lfsUVZBjuMzblsdxVWi2JBwV1QlWxog9kGAIgIxR1nftTOXohUs/ryJQASns23fXWSmxOcTyESx9S0m8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32646, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaWvBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpOMw//d1T/q/4cL0g/He4Kea35QBPsvC6vKXJYnX5PVp3KKP+0Ua//\r\nDDOg6i8NMVTOIr+vI1OKijja30KKa89ageUb21DhChi049GKeqGo3hZ/D1Mh\r\n7iWVNOgwoaCDGmzj6wrggzkCt5BY559xfDh9odybRhMsNmEPmg8iqLlRv1z5\r\nFi4miyk+ZmkHz9FgVqvkKV/d5Da8f/jBa9kAL3eRIxY4aP3ydsvwumrOJB5u\r\nPHf5phiQORWsBBja3BFMwBs36YQcE2iCWQyToO+kJXxLVMPDBLzvGlfE+wvM\r\n3a0YwMntCGHAOzf9s8yw33C8VKXVxfRTXslNYJ8qS+z7Ecunn/4MO++A0a/t\r\ni0NFbxYWlOxgEyU8ZwzAh/gteGS4+bMRA3MGT/KhdIAEW6Leas9LQ3pFG9DM\r\nhF+kf/9Zq7SODtQijlCA9DVDH6StZfPJW6jjGiftyZ1Dus+iV0uuThKEwhDk\r\nVRlM1idmG4vkI9vk0nnEE8xe9N1bP1OdMJgm2o7Mv51ktbUTHI1wE7L58oic\r\nPt6YM2XgR34Ucg/Bz63Z1die6HsceRwDWcZKAQCRtD6L6f0D6uk44+rnj3+e\r\nVyJD347OxjCeFNUtuEyqkD8dRdhKR0+iqIppXWk3mBRBEPfKCwVLWxhx97QK\r\nl+M8nfbKQbdn2woq6Jl3mXvRjE9tpmj3RzM=\r\n=PxYz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.0.0", "@babel/parser": "^7.1.0", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.1", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.1.20_1667853249470_0.6397914536110139", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8c4006e1cbff5b6a112a0358becc84759f1b9a79116faf90a41e68831e1064eb"}, "7.20.0": {"name": "@types/babel__core", "version": "7.20.0", "license": "MIT", "_id": "@types/babel__core@7.20.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__core", "dist": {"shasum": "61bc5a4cae505ce98e1e36c5445e4bee060d8891", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.0.tgz", "fileCount": 5, "integrity": "sha512-+n8dL/9GWblDO0iU6eZAwEIJVr5DWigtle+Q6HLOrh/pdbXOhOtqzq8VPPE2zvNJzSKY4vH/z3iT3tn0A3ypiQ==", "signatures": [{"sig": "MEUCIEN0uaUjcdizxedFo3qb3BFX1bA3r1P9GDl9YXGS04+UAiEA9pQnrhx9WV4EAXE4b+kZDtHgqlKCHGGoAegUBjufNh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33720, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyDtRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqo4A//aIdB+OwP4uzEX5IZiKPY8e21BYEdIFPPRzr1Wn83CZStqrol\r\nxb67p5FTNxJxI6csp8L8msfrTsPHSXMWGbYmz7hUz2zBGx1fUF5mxsVLm+Og\r\nlRACKuY5V+GUJXpUFibDpG5ByPgc2FBFzSLK3lpTo+Y8tcAnCOI0yZDPEplv\r\nPmeJuT+/U6Sk6QwbHo+tLCa5934rJgyXlLOEOgHALY1EVT6Vnlfu1/fTMaCj\r\nVy/ARHC824gncOliIMhaVtG8HRVIrSXuzECD9zUbbEKM9B9SIwX+qKIDDd84\r\nLRc+KhMkpB36zis6e8zCFHl4AWwmvHJAyUGkkB/8Bf0o4kafGSBDdlZlmYH6\r\n2TFc9yxjAoxGjwMs1Jzbiw1fn+CaqQfoNd2Gd0jHm5J2jSORzUeZ1x/QR3p/\r\nzCFRFVtq76Q+EQHo1CtwrbaVaPm3F+ugX8hxzmqSb0nZ63zdHfz+E5wywUwQ\r\nl1noDEjSAcwX5pN/SXPD1UZVpG2RZjKzw610QhIhJ+zyp86ExkCLwii0rHJq\r\nb79SC5PBYlsfcFfiPyXmS6GqIqtlq7JHLszxO0/KFEgrB+U0YEiGvvF9851s\r\nCFJwnaz1kI3su9K63e4e1C6IciXmtLn6nGX6KYaOvuzU6uov742uqIxFN4Q6\r\nillRW++8rfkngz43tdjxqe05e0bWA7TT61g=\r\n=VNPd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.20.7", "@babel/parser": "^7.20.7", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.20.0_1674066769099_0.08706665931362223", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "5f7ac35ea0366e8fe844535c52b8a802863f6047af2f052830c5b75a78d9b55c"}, "7.20.1": {"name": "@types/babel__core", "version": "7.20.1", "license": "MIT", "_id": "@types/babel__core@7.20.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__core", "dist": {"shasum": "916ecea274b0c776fec721e333e55762d3a9614b", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.1.tgz", "fileCount": 5, "integrity": "sha512-aACu/U/omhdk15O4Nfb+fHgH/z3QsfQzpnvRZhYhThms83ZnAOZz7zZAWO7mn2yyNQaA4xTO8GLK3uqFU4bYYw==", "signatures": [{"sig": "MEUCIQDNwRgzuogR2/OFFYAMrQ8DXh1D/ZBHNW8/V2Nfo+g7aQIgDgYkPygH32/uapyJeCTK8XlFAwuv6wf7L6ABXgxkosw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33685}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.20.7", "@babel/parser": "^7.20.7", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.20.1_1685037780488_0.30440179642751186", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d0a9a3e3a20a635188042ebfb19fae75d7ef511226640bc77ac7f60e882e36a3"}, "7.20.2": {"name": "@types/babel__core", "version": "7.20.2", "license": "MIT", "_id": "@types/babel__core@7.20.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__core", "dist": {"shasum": "215db4f4a35d710256579784a548907237728756", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.2.tgz", "fileCount": 5, "integrity": "sha512-pNpr1T1xLUc2l3xJKuPtsEky3ybxN3m4fJkknfIpTCTfIZCDW57oAg+EfCgIIp2rvCe0Wn++/FfodDS4YXxBwA==", "signatures": [{"sig": "MEYCIQDWUywIJbeKlkKvdkjwLbeZnHMv/meaGC9uM4pKu82p2gIhAKbQiQc5yPI6rigZ4d1cOZLEhAvzE8253vrGn6asYw7R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33611}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.20.7", "@babel/parser": "^7.20.7", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.20.2_1694805015164_0.8362110523258668", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "2fe22f2b6af7e82d9f2d7774b005980a3071f549be055d84b466bea9baf414b0"}, "7.20.3": {"name": "@types/babel__core", "version": "7.20.3", "license": "MIT", "_id": "@types/babel__core@7.20.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__core", "dist": {"shasum": "d5625a50b6f18244425a1359a858c73d70340778", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.3.tgz", "fileCount": 5, "integrity": "sha512-54fjTSeSHwfan8AyHWrKbfBWiEUrNTZsUwPTDSNaaP1QDQIZbeNUg3a59E9D+375MzUw/x1vx2/0F5LBz+AeYA==", "signatures": [{"sig": "MEUCIQDOycOqL4q/TVsaBW5Lzx/pdnZepyMFezxbq4V2QnetYgIgdqokoTUgp/pHFlVXNmJkL0tXGtyCxpatez+FzFJFVgE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33024}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.20.7", "@babel/parser": "^7.20.7", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.20.3_1697583324576_0.4671408318282011", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "aa7a8166019138cdc96a6a6d8b723dc333acd01b2164e92152211b5eea7d13d4"}, "7.20.4": {"name": "@types/babel__core", "version": "7.20.4", "license": "MIT", "_id": "@types/babel__core@7.20.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__core", "dist": {"shasum": "26a87347e6c6f753b3668398e34496d6d9ac6ac0", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.4.tgz", "fileCount": 5, "integrity": "sha512-mLnSC22IC4vcWiuObSRjrLd9XcBTGf59vUSoq2jkQDJ/QQ8PMI9rSuzE+aEV8karUMbskw07bKYoUJCKTUaygg==", "signatures": [{"sig": "MEUCIQCmHN8GezYITK1GkyPj1B2hSBHeBZM+Hv4LnBGSbBAlhwIgIt7qUS4hcWis6dq4Jd+RpeJ3u61tKoILqozDp09ceRc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33024}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.20.7", "@babel/parser": "^7.20.7", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.20.4_1699313948546_0.26807780451651997", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f21b63eb15464709084cacbba2ebedf3c8c9993d675693726f473d510c23ee27"}, "7.20.5": {"name": "@types/babel__core", "version": "7.20.5", "license": "MIT", "_id": "@types/babel__core@7.20.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__core", "dist": {"shasum": "3df15f27ba85319caa07ba08d0721889bb39c017", "tarball": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz", "fileCount": 5, "integrity": "sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==", "signatures": [{"sig": "MEQCICZunIbpZ83F4tI8FM0UoHLJFGt9tqJVps+2rfErtJGrAiAPqBBeCFFv2ZyYkw5fVK3gUixvRaroWHc+012gr912yg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33020}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "directories": {}, "dependencies": {"@babel/types": "^7.20.7", "@babel/parser": "^7.20.7", "@types/babel__template": "*", "@types/babel__traverse": "*", "@types/babel__generator": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/babel__core_7.20.5_1700523850564_0.7919218994006494", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3ece429b02ff9f70503a5644f2b303b04d10e6da7940c91a9eff5e52f2c76b91"}}, "time": {"created": "2018-07-25T22:30:03.389Z", "modified": "2025-02-23T06:25:45.190Z", "7.0.0": "2018-07-25T22:30:03.730Z", "7.0.1": "2018-07-30T23:17:10.181Z", "7.0.2": "2018-11-15T22:58:53.617Z", "7.0.3": "2018-11-28T15:55:13.915Z", "7.0.4": "2018-11-29T17:45:09.557Z", "7.0.5": "2019-02-13T21:07:52.963Z", "7.1.0": "2019-02-21T17:37:22.138Z", "7.1.1": "2019-04-10T17:27:35.500Z", "7.1.2": "2019-05-15T16:26:08.353Z", "7.1.3": "2019-09-04T17:40:21.309Z", "7.1.4": "2020-02-14T17:47:06.852Z", "7.1.5": "2020-02-20T18:29:59.875Z", "7.1.6": "2020-02-25T19:09:41.530Z", "7.1.7": "2020-03-31T23:02:41.736Z", "7.1.8": "2020-06-01T23:54:49.417Z", "7.1.9": "2020-06-21T08:23:47.966Z", "7.1.10": "2020-09-24T20:48:47.537Z", "7.1.11": "2020-10-30T11:15:12.017Z", "7.1.12": "2020-11-03T00:06:29.705Z", "7.1.13": "2021-03-15T18:55:37.986Z", "7.1.14": "2021-03-20T01:02:00.321Z", "7.1.15": "2021-07-06T18:15:53.284Z", "7.1.16": "2021-09-06T20:32:49.407Z", "7.1.17": "2021-12-06T23:01:35.711Z", "7.1.18": "2021-12-31T22:02:38.993Z", "7.1.19": "2022-03-20T07:01:49.931Z", "7.1.20": "2022-11-07T20:34:09.612Z", "7.20.0": "2023-01-18T18:32:49.307Z", "7.20.1": "2023-05-25T18:03:00.696Z", "7.20.2": "2023-09-15T19:10:15.507Z", "7.20.3": "2023-10-17T22:55:24.788Z", "7.20.4": "2023-11-06T23:39:08.818Z", "7.20.5": "2023-11-20T23:44:10.721Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/babel__core", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/babel__core"}, "description": "TypeScript definitions for @babel/core", "contributors": [{"url": "https://github.com/yortus", "name": "<PERSON>", "githubUsername": "yortus"}, {"url": "https://github.com/marvinhagemeister", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/mgroenhoff", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/Jessidhia", "name": "<PERSON>", "githubUsername": "Jessidhia"}, {"url": "https://github.com/ifiokjr", "name": "<PERSON><PERSON><PERSON> Jr.", "githubUsername": "ifiokjr"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}