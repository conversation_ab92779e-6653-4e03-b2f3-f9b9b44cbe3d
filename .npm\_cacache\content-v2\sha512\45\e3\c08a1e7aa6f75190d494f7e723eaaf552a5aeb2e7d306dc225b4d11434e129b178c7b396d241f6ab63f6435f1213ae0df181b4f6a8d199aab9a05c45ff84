{"_id": "ieee754", "_rev": "30-85c89c424e1085b5b5ee60837504ed15", "name": "ieee754", "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "dist-tags": {"latest": "1.2.1"}, "versions": {"1.0.0": {"name": "ieee754", "version": "1.0.0", "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "main": "index.js", "dependencies": {}, "devDependencies": {"browserify": "*", "tape": "*"}, "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "keywords": ["ieee754", "IEEE 754", "floating point", "buffer", "convert"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org/"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "tape test/*.js"}, "license": "MIT", "bugs": {"url": "https://github.com/feross/ieee754/issues"}, "homepage": "https://github.com/feross/ieee754", "_id": "ieee754@1.0.0", "dist": {"shasum": "202735c93842db2c3593ac58ab961373b2f0315e", "tarball": "https://registry.npmjs.org/ieee754/-/ieee754-1.0.0.tgz", "integrity": "sha512-xjT2hH+OnbBRHEvtOQ4d12ZnDKQpSS5eFyASybRj5ZmGjOMASjQsHxZZRMoUIuAuyFDFogdpT/m/wQG30JLWhw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCmuRyJovpVDzI57duIyrkaESDRZBjOdKtirgF231c7AgIhANL2h99hCno2TdH+OPQlnaWbHa6Pspbal19EqZSh/Ies"}]}, "_from": ".", "_npmVersion": "1.3.17", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}}, "1.1.0": {"name": "ieee754", "version": "1.1.0", "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "main": "index.js", "dependencies": {}, "devDependencies": {"tape": "*"}, "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "keywords": ["ieee754", "IEEE 754", "floating point", "buffer", "convert"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org/"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "tape test/*.js"}, "license": "MIT", "bugs": {"url": "https://github.com/feross/ieee754/issues"}, "homepage": "https://github.com/feross/ieee754", "_id": "ieee754@1.1.0", "dist": {"shasum": "af1017cc1f6c88d087361ebd4d028ebb013f40ad", "tarball": "https://registry.npmjs.org/ieee754/-/ieee754-1.1.0.tgz", "integrity": "sha512-X/d5ctgfcLZdy8qgU/6dzANJXhQKP37hrbo3e8QPVM6/Rr4ZBxltFIdkM+6MZXA0qdusZmQ4FR7LLEapBzi15A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8oYuRjzBowz5S8/bN3ejJcwo4bXD37ncVlt5a/yxCPwIgAoMP6Asn45KIHJzr45ULdQHtrULhVSNYgHGcyOKXMsc="}]}, "_from": ".", "_npmVersion": "1.3.17", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}}, "1.1.1": {"name": "ieee754", "version": "1.1.1", "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "main": "index.js", "dependencies": {}, "devDependencies": {"tape": "*"}, "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "keywords": ["ieee754", "IEEE 754", "floating point", "buffer", "convert"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org/"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "tape test/*.js"}, "license": "MIT", "bugs": {"url": "https://github.com/feross/ieee754/issues"}, "homepage": "https://github.com/feross/ieee754", "_id": "ieee754@1.1.1", "dist": {"shasum": "eee2eae514617e22de232a08f299aaf5a2c3676c", "tarball": "https://registry.npmjs.org/ieee754/-/ieee754-1.1.1.tgz", "integrity": "sha512-nW7Ug9CoNe259iaVHtOPhNGd6lzyAJ/UogIGOXUIHq+/a/3E5LzhvoUzDT8Ahcy9mVdgnvU0nBm26NESS2SP0Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD/Vwb7vM1/V4tdIJ+8/vkHPIBCR81kAebPBAa8Q8PLlQIgRshcU/MMn+dg1Uc6Uzaeq1iogK/GNqNseLQIJfAKr/0="}]}, "_from": ".", "_npmVersion": "1.3.17", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}}, "1.1.2": {"name": "ieee754", "version": "1.1.2", "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "main": "index.js", "dependencies": {}, "devDependencies": {"tape": "*"}, "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "keywords": ["ieee754", "IEEE 754", "floating point", "buffer", "convert"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org/"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "tape test/*.js"}, "license": "MIT", "bugs": {"url": "https://github.com/feross/ieee754/issues"}, "homepage": "https://github.com/feross/ieee754", "_id": "ieee754@1.1.2", "dist": {"shasum": "23f6091c2a76ce46f79d77303e5328cec903ee4f", "tarball": "https://registry.npmjs.org/ieee754/-/ieee754-1.1.2.tgz", "integrity": "sha512-NrJ15xRKB7pkrytVTxtDrWmzg9XsTXC+mVLHLmuR+Ra5IBtf4RdN0LMMaB1FZNdPcdihKC5y6qm0hV3inZ0beA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBVmlSs/LL5LEFKHjW9XH0S7obNh1Gev4e5vB1a1yRzOAiEAuEJSPOUaqFRCz5r351eguMVsFSyEBZ/wOmnnFzdcgwA="}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}}, "1.1.3": {"name": "ieee754", "version": "1.1.3", "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "main": "index.js", "dependencies": {}, "devDependencies": {"tape": "*"}, "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "keywords": ["ieee754", "IEEE 754", "floating point", "buffer", "convert"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org/"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "tape test/*.js"}, "license": "MIT", "bugs": {"url": "https://github.com/feross/ieee754/issues"}, "homepage": "https://github.com/feross/ieee754", "_id": "ieee754@1.1.3", "dist": {"shasum": "1d4baae872e15ba69f6ab7588a965e09d485ec50", "tarball": "https://registry.npmjs.org/ieee754/-/ieee754-1.1.3.tgz", "integrity": "sha512-EQ7R5rT32ipl2nRAafQfNun7FMiX6HoW6hjrPTcoHwk43liOaiiq+rxpV6DQBYE2t9GUf6543LgUuDKrQ3t5nQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCYrHFuqT1iN9k7cBV5IywVCsLwWVxNwVw/WDNGtrK61gIhAKxbXHh9LdaIZRUbm+BuF9dScDEQdb4oHhDSzcvMrhBT"}]}, "_from": ".", "_npmVersion": "1.3.25", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}}, "1.1.4": {"name": "ieee754", "version": "1.1.4", "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "main": "index.js", "dependencies": {}, "devDependencies": {"tape": "*"}, "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "keywords": ["ieee754", "IEEE 754", "floating point", "buffer", "convert"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org/"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "tape test/*.js"}, "license": "MIT", "gitHead": "01fcb668d406696c24398da9cc7f95f0633dfa09", "bugs": {"url": "https://github.com/feross/ieee754/issues"}, "homepage": "https://github.com/feross/ieee754", "_id": "ieee754@1.1.4", "_shasum": "e3ec65200d4ad531d359aabdb6d3ec812699a30b", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "e3ec65200d4ad531d359aabdb6d3ec812699a30b", "tarball": "https://registry.npmjs.org/ieee754/-/ieee754-1.1.4.tgz", "integrity": "sha512-riCnQr7G6vUvHHrqI9jxE2XDvHwelWpUhx4hB91tViEkeV0cERYOvaq+tMSwmeG2CLgqaaI29vpTQ9NVgArueA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHA9tbicKI9tOFS9Aqssw7CrJSCplTlKxXS6lh+6womDAiAtKgfNw6/gJ0pj4ZDmQwx8lpspAY8ZtvvCHzhi/HTRUw=="}]}, "directories": {}}, "1.1.5": {"name": "ieee754", "version": "1.1.5", "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "main": "index.js", "dependencies": {}, "devDependencies": {"standard": "^3.7.3", "tape": "^4.0.0", "zuul": "^3.0.0"}, "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "keywords": ["ieee754", "IEEE 754", "floating point", "buffer", "convert"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org/"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-browser": "zuul -- test/*.js", "test-browser-local": "zuul --local -- test/*.js", "test-node": "tape test/*.js"}, "license": "MIT", "gitHead": "a92e12057e5005f2b8903872a181f020ac5ba01c", "bugs": {"url": "https://github.com/feross/ieee754/issues"}, "homepage": "https://github.com/feross/ieee754", "_id": "ieee754@1.1.5", "_shasum": "2ddd7b4e3e48bcc67a32eed6abe9eeb18c5159e8", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "2ddd7b4e3e48bcc67a32eed6abe9eeb18c5159e8", "tarball": "https://registry.npmjs.org/ieee754/-/ieee754-1.1.5.tgz", "integrity": "sha512-wO+TwjtnsxSg7yl+2o5OBDmA7tBcD/Hb8BtQg0wqETFIX5rawx/IM5qy9l68m2iawxyeNXPMhKFMST2hjgJpGg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDl/HpJbrClr0iTmBTxwjpolzgj9wn5Pq789mFFjqNqUQIgaizTPYKk1nTWANpxXWVEdhV1ax0Hh2VduOQw4pm9Kos="}]}, "directories": {}}, "1.1.6": {"name": "ieee754", "version": "1.1.6", "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "main": "index.js", "dependencies": {}, "devDependencies": {"standard": "^4.1.1", "tape": "^4.0.0", "zuul": "^3.0.0"}, "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "keywords": ["ieee754", "IEEE 754", "floating point", "buffer", "convert"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org/"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-browser": "zuul -- test/*.js", "test-browser-local": "zuul --local -- test/*.js", "test-node": "tape test/*.js"}, "license": "MIT", "gitHead": "d3b4a18991a20dd7977e32fe2e6cec1b73efe53d", "bugs": {"url": "https://github.com/feross/ieee754/issues"}, "homepage": "https://github.com/feross/ieee754#readme", "_id": "ieee754@1.1.6", "_shasum": "2e1013219c6d6712973ec54d981ec19e5579de97", "_from": ".", "_npmVersion": "2.9.1", "_nodeVersion": "0.12.3", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "2e1013219c6d6712973ec54d981ec19e5579de97", "tarball": "https://registry.npmjs.org/ieee754/-/ieee754-1.1.6.tgz", "integrity": "sha512-B5svZW2QyELoy5LG4oZ81ycUfOLAvP1rmbWjodPW8CV4K83h6ufpbLyzY2MclbLBDCHUlz5E1NF0SqzDtYHcsw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCXSoTQYmWPrkGJxRvK1rlfdpxTBrBw4x/FRADM0DGZSgIhAIuxEppiEQLM+vjST4Z4aUHu5nbT+Ao/CAWT23Ln7rxY"}]}, "directories": {}}, "1.1.7": {"name": "ieee754", "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "version": "1.1.7", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "devDependencies": {"standard": "*", "tape": "^4.0.0", "zuul": "^3.0.0"}, "keywords": ["IEEE 754", "buffer", "convert", "floating point", "ieee754"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-browser": "zuul -- test/*.js", "test-browser-local": "zuul --local -- test/*.js", "test-node": "tape test/*.js"}, "gitHead": "25981754d8be54edf8846d3a5aebf43d3ae759c5", "bugs": {"url": "https://github.com/feross/ieee754/issues"}, "homepage": "https://github.com/feross/ieee754#readme", "_id": "ieee754@1.1.7", "_shasum": "cc7a6ffd6142dc5bed0a76d5f30df0dcf660291d", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.7.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "cc7a6ffd6142dc5bed0a76d5f30df0dcf660291d", "tarball": "https://registry.npmjs.org/ieee754/-/ieee754-1.1.7.tgz", "integrity": "sha512-84nussyv8Ji/IREiqiPon28OhH/O7B1jWnQfWd1Ntt5t0Dck3bcaiqpGyqw5jq05TDp8M14OFk/bArx4EcFf8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEL+4OKoqf1eE10O9vRPuIu2fxVABXpEtDJXep0cFIvCAiAl7rBomgYGKBL8JXT37Jz+2Kc6CjxY7Qo9tBYECtJ0aw=="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/ieee754-1.1.7.tgz_1475478818995_0.6850021036807448"}, "directories": {}}, "1.1.8": {"name": "ieee754", "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "version": "1.1.8", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "devDependencies": {"standard": "*", "tape": "^4.0.0", "zuul": "^3.0.0"}, "keywords": ["IEEE 754", "buffer", "convert", "floating point", "ieee754"], "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-browser": "zuul -- test/*.js", "test-browser-local": "zuul --local -- test/*.js", "test-node": "tape test/*.js"}, "gitHead": "53d3f869cc527852156b8307353c55addc3e03ae", "bugs": {"url": "https://github.com/feross/ieee754/issues"}, "homepage": "https://github.com/feross/ieee754#readme", "_id": "ieee754@1.1.8", "_shasum": "be33d40ac10ef1926701f6f08a2d86fbfd1ad3e4", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.7.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "be33d40ac10ef1926701f6f08a2d86fbfd1ad3e4", "tarball": "https://registry.npmjs.org/ieee754/-/ieee754-1.1.8.tgz", "integrity": "sha512-/aoyv2Nt7mGLnCAWzE0C1WH9Xd8ZsqR0f4Pjwxputi1JNm01+InyAYQotF4N+ulEIjbEsJo22NOHr+U/XEZ1Pw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnEBlrTX4+5d8QtejGmgewI1U9XJvXEIiuxnksVfVM+QIhAPcZ26l5siC4yzbGuKrYQIPeTKQRS2D4qqQg6fPkKezY"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ieee754-1.1.8.tgz_1475481601035_0.6688473029062152"}, "directories": {}}, "1.1.9": {"name": "ieee754", "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "version": "1.1.9", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "devDependencies": {"airtap": "0.0.4", "standard": "*", "tape": "^4.0.0"}, "keywords": ["IEEE 754", "buffer", "convert", "floating point", "ieee754"], "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-browser": "airtap -- test/*.js", "test-browser-local": "airtap --local -- test/*.js", "test-node": "tape test/*.js"}, "gitHead": "c0b57da7e631c7b3b7f256c0c3640a804bb332bd", "bugs": {"url": "https://github.com/feross/ieee754/issues"}, "homepage": "https://github.com/feross/ieee754#readme", "_id": "ieee754@1.1.9", "_npmVersion": "5.7.1", "_nodeVersion": "8.10.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-yWDsidMaZHbeTa0a1iSFpK8QhzicsFxo8zKxH0YU2g47rNUZql5+2o3DSc5Z070kjGPLP292BWiF4bd8Q+G87g==", "shasum": "13acbc76462de80959be14b2d4ac93b96761b195", "tarball": "https://registry.npmjs.org/ieee754/-/ieee754-1.1.9.tgz", "fileCount": 5, "unpackedSize": 7060, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA4BHtwZ+CqIAndLCa5YUqmWq0fi7MWJapwlJZhM/DB7AiB63Ik1gBFF64+4iqr1RmT0WYw0kIw4m8V2MfYHKqNvOg=="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ieee754_1.1.9_1521287296812_0.6477363359191368"}, "_hasShrinkwrap": false}, "1.1.10": {"name": "ieee754", "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "version": "1.1.10", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "devDependencies": {"airtap": "0.0.4", "standard": "*", "tape": "^4.0.0"}, "keywords": ["IEEE 754", "buffer", "convert", "floating point", "ieee754"], "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-browser": "airtap -- test/*.js", "test-browser-local": "airtap --local -- test/*.js", "test-node": "tape test/*.js"}, "gitHead": "0295917be9d9bdc773697af0187cc39b338d7760", "bugs": {"url": "https://github.com/feross/ieee754/issues"}, "homepage": "https://github.com/feross/ieee754#readme", "_id": "ieee754@1.1.10", "_npmVersion": "5.7.1", "_nodeVersion": "8.10.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-byWFX8OyW/qeVxcY21r6Ncxl0ZYHgnf0cPup2h34eHXrCJbOp7IuqnJ4Q0omfyWl6Z++BTI6bByf31pZt7iRLg==", "shasum": "719a6f7b026831e64bdb838b0de1bb0029bbf716", "tarball": "https://registry.npmjs.org/ieee754/-/ieee754-1.1.10.tgz", "fileCount": 5, "unpackedSize": 7061, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD7rqFkB5WY5yOuEJ3HfgwJUBCYlX4D7Msd55n1BCsYZwIhAOi8X/Uca31savdP4IogM05rceKPMdcMGrv6eENKc/Zv"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ieee754_1.1.10_1521352939340_0.8187348933114915"}, "_hasShrinkwrap": false}, "1.1.11": {"name": "ieee754", "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "version": "1.1.11", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "devDependencies": {"airtap": "0.0.4", "standard": "*", "tape": "^4.0.0"}, "keywords": ["IEEE 754", "buffer", "convert", "floating point", "ieee754"], "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-browser": "airtap -- test/*.js", "test-browser-local": "airtap --local -- test/*.js", "test-node": "tape test/*.js"}, "gitHead": "1b454b58560efd08e47ff53827808bcee5df096b", "bugs": {"url": "https://github.com/feross/ieee754/issues"}, "homepage": "https://github.com/feross/ieee754#readme", "_id": "ieee754@1.1.11", "_npmVersion": "5.8.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-VhDzCKN7K8ufStx/CLj5/PDTMgph+qwN5Pkd5i0sGnVwk56zJ0lkT8Qzi1xqWLS0Wp29DgDtNeS7v8/wMoZeHg==", "shasum": "c16384ffe00f5b7835824e67b6f2bd44a5229455", "tarball": "https://registry.npmjs.org/ieee754/-/ieee754-1.1.11.tgz", "fileCount": 5, "unpackedSize": 7061, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCmZrGLvohzt5/pW6RTG2SlQLFGEQ+oJUBZPXjL0uS6lgIgCKjWTCz2jqHntr+YXCxv8gjK3nuHTFPH3SEhehKQxg4="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ieee754_1.1.11_1521876394621_0.8917228152846983"}, "_hasShrinkwrap": false}, "1.1.12": {"name": "ieee754", "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "version": "1.1.12", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "devDependencies": {"airtap": "0.0.7", "standard": "*", "tape": "^4.0.0"}, "keywords": ["IEEE 754", "buffer", "convert", "floating point", "ieee754"], "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-browser": "airtap -- test/*.js", "test-browser-local": "airtap --local -- test/*.js", "test-node": "tape test/*.js"}, "gitHead": "1e2646ce533930c16040cfb54ae54165575a4c19", "bugs": {"url": "https://github.com/feross/ieee754/issues"}, "homepage": "https://github.com/feross/ieee754#readme", "_id": "ieee754@1.1.12", "_npmVersion": "6.1.0", "_nodeVersion": "8.11.2", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-GguP+DRY+pJ3soyIiGPTvdiVXjZ+DbXOxGpXn3eMvNW4x4irjqXm4wHKscC+TfxSJ0yw/S1F24tqdMNsMZTiLA==", "shasum": "50bf24e5b9c8bb98af4964c941cdb0918da7b60b", "tarball": "https://registry.npmjs.org/ieee754/-/ieee754-1.1.12.tgz", "fileCount": 4, "unpackedSize": 6116, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbHGGxCRA9TVsSAnZWagAAfu4QAId2qg9gpZvsVX1yXsAB\ndknmK313WeIFcyo5qDd17H6wLM8fyDy7h2LxG1yyQmoI+txBZEXml0hTTC+k\nBzH4aA20sAUUKb8c/TbH7ELNOa84MKclQo+lquZSVYqrvax1UE/hCDXxHBWw\nbYHNFOuqfyM16UTKP+bCqXwi61CC2oA4IQHdeKCdBIL2jIQ/u1OYSIdxK/H6\nYCmPoar7dF1OOBHGGmkDx1mSWDIep/rbad4apUfy2kIjJsQtGy4aKI/4cLc8\nPDfqIFDJ0N8lYmuczqgVsGPhP9OCMN2cX8xg31dnmyNDsRggPS1StkjU5gUE\nrTd921R9rl+rwRrPv6rZde6dWC26mmaYZ2NBOEtjAfHFy0O1yvniQrBmwxlC\nUeSx+rKv3cFEhHMm0y6jZgZqqN2OE8Is8ASIwTXVp8tCBWrcfcyc8eSQre0x\nulABWGsS029QRtR5U4F++l4pS0mBImL3zULNwvKB7IT0NrA9sBfAyzKomMdD\nej9QpbFduVedSvtLI9XqA5pdfU9CSCB2UiWmhiJjH3n3eXjPCYhN20LaJVYx\nxe0xMlS758n9C1ocr4dKbVIGlxluN/D4stgTh3lrtY8FRWOvV1F2t6pzXM0l\n28aM0olB+iyhHjkHgll+aofh85u/LFRrXAulBQNhF6u3serAcWKVeqFkaIYP\nlpAO\r\n=uhbC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCLryNtIIW/SGoAXzZ10CbW+YGS9qZUxcnC/Vm5bQVURAIhAI6Hj+s24Jvof1msMckIrckc3i6VgqGCuKlZYjm2+kru"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ieee754_1.1.12_1528586672368_0.8737436726749925"}, "_hasShrinkwrap": false}, "1.1.13": {"name": "ieee754", "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "version": "1.1.13", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "devDependencies": {"airtap": "0.1.0", "standard": "*", "tape": "^4.0.0"}, "keywords": ["IEEE 754", "buffer", "convert", "floating point", "ieee754"], "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-browser": "airtap -- test/*.js", "test-browser-local": "airtap --local -- test/*.js", "test-node": "tape test/*.js"}, "gitHead": "4971f0e802c2cbdfad7791bd223c53e37b4ebb85", "bugs": {"url": "https://github.com/feross/ieee754/issues"}, "homepage": "https://github.com/feross/ieee754#readme", "_id": "ieee754@1.1.13", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-4vf7I2LYV/HaWerSo3XmlMkp5eZ83i+/CDluXi/IGTs/O1sejBNhTtnxzmRZfvOUqj7lZjqHkeTvpgSFDlWZTg==", "shasum": "ec168558e95aa181fd87d37f55c32bbcb6708b84", "tarball": "https://registry.npmjs.org/ieee754/-/ieee754-1.1.13.tgz", "fileCount": 4, "unpackedSize": 6245, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcmbnHCRA9TVsSAnZWagAAtYYP/22y6qTBTrSGYfV/TRaR\nK22j5TLZBkcv2v3yUEOhtlYOXcPyFFUwaAccm/dE1B5YEINqc/BLtonjHYNY\nITt47l/0glWUoG4X3hSgezZSYPS2z0OZXd+ACnHoMHryxXkhldIqHSisaAsQ\nakgx8oIAGOPA/eMg473zoqAzDyr6Jz7auTKXGhmxHCzd6u4xOmRB8db7JTZr\nSuE0sDN8iE2Dr2XPtfm4FV2E9I9qHHRC/kLigUAVaROUpHJilOmdTuz1eDYi\nQeShVfEVydkVnr0Cetx/d8RF+D777XKf/jomJJ+oHptcgF3bZqwhs8A75URJ\nEBA1AhbbWrQuOF+ESuaXhF0Mf4XlwZ9MA847GiF0j/JX6bGtyWSNI/1V/NEL\n9+DdvFaNvsUNfggwROJawmLwFyCusWsKzjXFnRO1hjHafLFOeXlE+vF9v3H5\nimZfnNkRxTq3+Vut2vwLF+yjz6hVVSRtuCnHb84geL1dmHDBRXctpfAcW24v\nfUTvZH0+KZT/Na6HooZgAUWcxGIiBT3vhAE1BQAjTTJukGie6mJAD1jfZb6l\nGMLt2WTF03UgkcgaosOAdudyTyHTW0w/ai23m10jP2xaFRhjErZkSv+oc/73\nyw+co0BsxN9T4fsXHSXcai+sI1r31KnO5arPUQmtYrRiAfH8LBGBggTn/Zl1\nsTwW\r\n=rYe2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVsFspo4QtFhhmSOupy0LyaR10CHVOFPE3lfVL7uxmAgIhAO3b6DqULna9fagVH6IaVE87k1mdS9ttMXX30GvNL4VP"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ieee754_1.1.13_1553578438992_0.7571224818616284"}, "_hasShrinkwrap": false}, "1.2.0": {"name": "ieee754", "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "version": "1.2.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "devDependencies": {"airtap": "^3.0.0", "standard": "*", "tape": "^5.0.1"}, "keywords": ["IEEE 754", "buffer", "convert", "floating point", "ieee754"], "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-browser": "airtap -- test/*.js", "test-browser-local": "airtap --local -- test/*.js", "test-node": "tape test/*.js"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "gitHead": "c02ee853c508bbd6af0861f010f36b95250e79b8", "bugs": {"url": "https://github.com/feross/ieee754/issues"}, "homepage": "https://github.com/feross/ieee754#readme", "_id": "ieee754@1.2.0", "_nodeVersion": "14.14.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-EWa7B4Ik9ncTIIojzGoKzpUdswDKO6v1BQ0pcKajYCrZckY9gNjiEparSGlyz5C2HcpV62qiVkmLBCPx77Hq2g==", "shasum": "199187d07b31d4aa68ed02311914049795868ea6", "tarball": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.0.tgz", "fileCount": 4, "unpackedSize": 6464, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfmKWNCRA9TVsSAnZWagAALBkQAJ40lK3TDpxWds/6W1eN\nsIBkFh7+ync+RgetKoNIk9g5E6zyGRPxGDr80+eBhNfg5XRANT7B6CuiDnk2\nkVb12bbZJPkBpKDuuygUW5sDJcUVQ/Kr0O+jF7NBc6X6+z8uaLD+spvDl7I5\n8sv/hLG6PF4I/dOQVZkI/7gopB0Jjr91Gj8oi2bfNBuRGAemO5GfcDcBtEAJ\nAMXXp7UbpxCp7bJdd9F8csnHUM+U1/I4wx+oj+qA1QT6dgScK0S6n95hqTTX\nIGGqik8W2JKWzU2bkHgGG4ZUVMqpBogqhONDikUEHnRtrOl2CiVHkJEcmKOD\ntpC7zem4fpXEJ0bEsnvg44Rb1ZhwCz/bKm+eSFR5ZkqEyW2FtQkJcwMV3wcm\n4LsjJlLcqdd82OPByY3YnRYtsqycwZCYRpMVjt/aVWwbBgltOpXpJYRylW4N\nvcCGbmK+LvaofxwHUZqH3kxyQuugQKnShIfFR56Lv2+xTp6y/2wf7lHK6U+1\naKI7kNrLNvqt8jgemR1ScPNjYWdvTjbxtol1ilrf8JMcEonkOn9CGH+AZX/S\nOTOpYKWnHecP5jqksxDojAxay3pCqqvYNQDdNGHwhT63E2D2gjklezd7hQ25\n9/Xz8TYztVjWzIelY7vZLWw0jgYt4Jg5dQvCh7VtdM+AhyDhz9UDywkvC5rg\nbUDj\r\n=acPc\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJU3uN5nAhdzPMzGBjRwwBBQ46adBgh/LHyd3R1HX6owIhAP33z+Hwz9ICYyUT7Xind4/2zS9xj51UUf1kamU6f1wZ"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ieee754_1.2.0_1603839373204_0.12442439275073669"}, "_hasShrinkwrap": false}, "1.2.1": {"name": "ieee754", "description": "Read/write IEEE754 floating point numbers from/to a Buffer or array-like object", "version": "1.2.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "devDependencies": {"airtap": "^3.0.0", "standard": "*", "tape": "^5.0.1"}, "keywords": ["IEEE 754", "buffer", "convert", "floating point", "ieee754"], "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "scripts": {"test": "standard && npm run test-node && npm run test-browser", "test-browser": "airtap -- test/*.js", "test-browser-local": "airtap --local -- test/*.js", "test-node": "tape test/*.js"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "gitHead": "b60d148be9cad718f9ff007c211c2427cdc180a4", "bugs": {"url": "https://github.com/feross/ieee754/issues"}, "homepage": "https://github.com/feross/ieee754#readme", "_id": "ieee754@1.2.1", "_nodeVersion": "14.14.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==", "shasum": "8eb7a10a63fff25d15a57b001586d177d1b0d352", "tarball": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "fileCount": 5, "unpackedSize": 6796, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfmKffCRA9TVsSAnZWagAADakP/0mqXfjH4OS4rtzd7bst\niGth3Ohks66Tx1Ev+bmlhQ2QdnVKfn0+IifXRYtzDIJocgcSHFJHghc2i8Ij\nfuQn6Evx/wsEwiOAQ7Ulf9pbW6OegmkFNvljNYsej5zBG/6Z3wASbEuHrGY1\nUO+lJ0HjNtQnMcj9QMdn5TX6UKS7r0/YAkXntjstKW9bajPP26XrbqDxuoCj\n8miijE4FU/dTZogiLxH09ny51fTBav0qJIIf2veFZYJ2JeUVHzGCvuVr5Uyc\nv1z78ehTz/ZmF8L1kN3yv+60UaqgbR9+CA06oGsOO7dJiwA2KTrSZKbBF7ai\nHDKuDveIoUDrSG0ly03A+PwCPOjQXTsuBQg9ah46SyQsWVgoPIq6cIDoxwU6\n628Pl3yYv4SG9RK/MMF8PVZKDhJ0OuDGakHuJPQgoZwo9T37sm+f2u9VfXnI\n0KJW8RIWJwAR4flY1tW82efS3SWng2ALT3cYc5zYA5WCuuQadxSi664Dk/ac\nzsHlt2DYqdYznm5/G9mfcaPW0ZMWfPOnnxTSOmsc0SjPwCqa1Uihg92yE+CZ\n2bpw8JD69zG73SN7NuQ5adlvM/CR3UIqnXUgGIfHN9vxJigOY2UM7d++guUB\nqormxvM32BOea5CT/9ruJeCq8c0oaEVn5h4V603ivlSFnoweJDo9N+/7Z71X\nAB70\r\n=9yaZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIERTumjBhGJ4OT9tplNZGAYln/n40hO6KDzQkjyJr1yBAiB3e3ccmoGvY+nVyoz3TajE0t/MvUHO5pUWewAkCtF8fQ=="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/ieee754_1.2.1_1603839967317_0.34663956255577455"}, "_hasShrinkwrap": false}}, "readme": "# ieee754 [![travis][travis-image]][travis-url] [![npm][npm-image]][npm-url] [![downloads][downloads-image]][downloads-url] [![javascript style guide][standard-image]][standard-url]\n\n[travis-image]: https://img.shields.io/travis/feross/ieee754/master.svg\n[travis-url]: https://travis-ci.org/feross/ieee754\n[npm-image]: https://img.shields.io/npm/v/ieee754.svg\n[npm-url]: https://npmjs.org/package/ieee754\n[downloads-image]: https://img.shields.io/npm/dm/ieee754.svg\n[downloads-url]: https://npmjs.org/package/ieee754\n[standard-image]: https://img.shields.io/badge/code_style-standard-brightgreen.svg\n[standard-url]: https://standardjs.com\n\n[![saucelabs][saucelabs-image]][saucelabs-url]\n\n[saucelabs-image]: https://saucelabs.com/browser-matrix/ieee754.svg\n[saucelabs-url]: https://saucelabs.com/u/ieee754\n\n### Read/write IEEE754 floating point numbers from/to a Buffer or array-like object.\n\n## install\n\n```\nnpm install ieee754\n```\n\n## methods\n\n`var ieee754 = require('ieee754')`\n\nThe `ieee754` object has the following functions:\n\n```\nieee754.read = function (buffer, offset, isLE, mLen, nBytes)\nieee754.write = function (buffer, value, offset, isLE, mLen, nBytes)\n```\n\nThe arguments mean the following:\n\n- buffer = the buffer\n- offset = offset into the buffer\n- value = value to set (only for `write`)\n- isLe = is little endian?\n- mLen = mantissa length\n- nBytes = number of bytes\n\n## what is ieee754?\n\nThe IEEE Standard for Floating-Point Arithmetic (IEEE 754) is a technical standard for floating-point computation. [Read more](http://en.wikipedia.org/wiki/IEEE_floating_point).\n\n## license\n\nBSD 3 Clause. Copyright (c) 2008, Fair Oaks Labs, Inc.\n", "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "time": {"modified": "2023-07-22T20:56:46.634Z", "created": "2013-12-28T08:36:49.436Z", "1.0.0": "2013-12-28T08:36:50.333Z", "1.1.0": "2013-12-28T09:00:44.292Z", "1.1.1": "2013-12-28T09:10:38.056Z", "1.1.2": "2014-01-29T11:10:35.835Z", "1.1.3": "2014-02-16T03:15:52.627Z", "1.1.4": "2014-09-03T09:19:52.313Z", "1.1.5": "2015-05-05T01:33:13.811Z", "1.1.6": "2015-06-11T21:25:05.773Z", "1.1.7": "2016-10-03T07:13:41.604Z", "1.1.8": "2016-10-03T08:00:01.268Z", "1.1.9": "2018-03-17T11:48:16.870Z", "1.1.10": "2018-03-18T06:02:19.444Z", "1.1.11": "2018-03-24T07:26:34.705Z", "1.1.12": "2018-06-09T23:24:32.433Z", "1.1.13": "2019-03-26T05:33:59.129Z", "1.2.0": "2020-10-27T22:56:13.398Z", "1.2.1": "2020-10-27T23:06:07.424Z"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "repository": {"type": "git", "url": "git://github.com/feross/ieee754.git"}, "readmeFilename": "README.md", "users": {"thomasfoster96": true, "flumpus-dev": true}, "homepage": "https://github.com/feross/ieee754#readme", "keywords": ["IEEE 754", "buffer", "convert", "floating point", "ieee754"], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/feross/ieee754/issues"}, "license": "BSD-3-<PERSON><PERSON>"}