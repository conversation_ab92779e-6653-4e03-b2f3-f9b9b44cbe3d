{"_id": "@types/http-errors", "_rev": "628-0f827ae3feca4c9447eb8d2aec7c48e9", "name": "@types/http-errors", "dist-tags": {"ts2.1": "1.5.34", "ts2.0": "1.5.34", "ts2.2": "1.6.1", "ts2.5": "1.6.2", "ts2.6": "1.6.2", "ts2.3": "1.6.2", "ts2.7": "1.6.2", "ts2.4": "1.6.2", "ts2.9": "1.6.3", "ts2.8": "1.6.3", "ts3.2": "1.8.0", "ts3.0": "1.8.0", "ts3.1": "1.8.0", "ts3.3": "1.8.0", "ts3.4": "1.8.0", "ts3.5": "1.8.0", "ts3.7": "1.8.1", "ts3.6": "1.8.1", "ts3.9": "1.8.2", "ts4.0": "1.8.2", "ts3.8": "1.8.2", "ts4.2": "2.0.1", "ts4.4": "2.0.1", "ts4.3": "2.0.1", "ts4.1": "2.0.1", "ts5.9": "2.0.5", "ts4.5": "2.0.4", "ts4.6": "2.0.4", "ts4.7": "2.0.4", "ts4.8": "2.0.4", "ts4.9": "2.0.4", "ts5.0": "2.0.4", "ts5.7": "2.0.5", "ts5.5": "2.0.5", "ts5.8": "2.0.5", "ts5.4": "2.0.5", "ts5.3": "2.0.5", "ts5.2": "2.0.5", "ts5.1": "2.0.5", "latest": "2.0.5", "ts5.6": "2.0.5"}, "versions": {"1.3.15-alpha": {"name": "@types/http-errors", "version": "1.3.15-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/tkrotoff"}, "license": "MIT", "_id": "@types/http-errors@1.3.15-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "011602c6cb7b5afa99d7f8d9cb65c43c5e27dcff", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.3.15-alpha.tgz", "integrity": "sha512-eFcwdYQKtNKWL3Eg1BQSuXGkqHft+qS0WDgbC/MH/lF2QGXKjNfnsfNr/WupU2bSjRCbLiFaA1m03fDA4FQ8mQ==", "signatures": [{"sig": "MEQCIBdbPHWo9pg4NxEnL3wZXDS13mN+aXnFiPMFT/XSjydSAiAg8rt9PAN8kpoxUH4K+NHwuXBLggRZ7xbffMijjmPAFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\http-errors", "_shasum": "011602c6cb7b5afa99d7f8d9cb65c43c5e27dcff", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\http-errors", "_npmVersion": "3.8.2", "description": "Type definitions for http-errors v1.3.1 from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.3.15-alpha.tgz_1463461834416_0.10853233374655247", "host": "packages-16-east.internal.npmjs.com"}}, "1.3.16-alpha": {"name": "@types/http-errors", "version": "1.3.16-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/tkrotoff"}, "license": "MIT", "_id": "@types/http-errors@1.3.16-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "52a3486f68cc338455e65811709bd3c6730b652f", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.3.16-alpha.tgz", "integrity": "sha512-FEiof0nwUv/a5U0BheK/q6+MtXOnrIk96lsFeSa+gOw2p5qqsgkKynCakVVRniqKIb6HYtfBUVQF7KtxNIrHzg==", "signatures": [{"sig": "MEQCIAfoPMB1ed7I/n0uIs/r3+/0yuhIL4kz70g6Ls2ZzCXBAiA9JCexeT2FJZGenUizqW9OHmiFXWHbF+5t1Xy1j8c1tw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\http-errors", "_shasum": "52a3486f68cc338455e65811709bd3c6730b652f", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\http-errors", "_npmVersion": "3.8.2", "description": "Type definitions for http-errors v1.3.1 from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.3.16-alpha.tgz_1463692206720_0.8896998048294336", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.21-alpha": {"name": "@types/http-errors", "version": "1.3.21-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/tkrotoff"}, "license": "MIT", "_id": "@types/http-errors@1.3.21-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "01c9df3ce81ea6106f732a26b85700c18c2baf3c", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.3.21-alpha.tgz", "integrity": "sha512-QMvSrwrlxAo2rkole46Yx6VbPo4uK2oe7yjNJvSiC/0teOs7pRK0bqrBaY4fmQe4vnEkfGYCpyXLrAlyYIgo0Q==", "signatures": [{"sig": "MEUCIQCsOT53K7X1sJGF27CK2RsMuTxBOkcF5w/wuMrF3qfj6AIgbdLZbfvhggkLqiw/fcViNaRmIJyrJpKsQ/5QOwVTQWU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\http-errors", "_shasum": "01c9df3ce81ea6106f732a26b85700c18c2baf3c", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\http-errors", "_npmVersion": "3.8.2", "description": "TypeScript definitions for http-errors v1.3.1", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.3.21-alpha.tgz_1463773635892_0.913444145116955", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.22-alpha": {"name": "@types/http-errors", "version": "1.3.22-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/tkrotoff"}, "license": "MIT", "_id": "@types/http-errors@1.3.22-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "5e536518a80fb064d56c4e6f3d9289c9ab92463a", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.3.22-alpha.tgz", "integrity": "sha512-X2ZPMSFE7JYDVS95rurqy7CpfWnq5ZqLf2ER98TsJgQ4cOnnGLkbqPNY3x39vF2LmPRzSS5Oae8Fwkvv6W1CgQ==", "signatures": [{"sig": "MEYCIQD1W86Jwz7ce4ItZ2Div4pTuMRoHcEmQJM2PBsokmLGBQIhAIv/K2kIsBFb5P1NyHaz61HvUj79YfMx2K7+/wWOzwrr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\http-errors", "_shasum": "5e536518a80fb064d56c4e6f3d9289c9ab92463a", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\http-errors", "_npmVersion": "3.8.2", "description": "TypeScript definitions for http-errors v1.3.1", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.3.22-alpha.tgz_1464152741228_0.1186867372598499", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.23-alpha": {"name": "@types/http-errors", "version": "1.3.23-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/tkrotoff"}, "license": "MIT", "_id": "@types/http-errors@1.3.23-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "b3b161c8463d785a0c53b5e09a2aa33b9cadac93", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.3.23-alpha.tgz", "integrity": "sha512-ZSGc2+aAltm3HK8DBXedKRJLdiWXBTil1uj2+ZhwWjI5gnvlAXfCvEWRrlJfp5UK8PSWC+Faom7CDAJOObEX2w==", "signatures": [{"sig": "MEMCIF8uWvy37+jVXCEruKMXZx3cJ1ZDgsA4q80npDQ4LQ6tAh9dg/ZgEXMeqwot7KlDqMnCjg3og52LqkPY9SLCameF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\http-errors", "_shasum": "b3b161c8463d785a0c53b5e09a2aa33b9cadac93", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\http-errors", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.9.5", "description": "TypeScript definitions for http-errors v1.3.1", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.3.23-alpha.tgz_1467401545995_0.6464285291731358", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.24-alpha": {"name": "@types/http-errors", "version": "1.3.24-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/tkrotoff"}, "license": "MIT", "_id": "@types/http-errors@1.3.24-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "0c451582cb4b296b9f822a0ea13eded6cedf87f8", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.3.24-alpha.tgz", "integrity": "sha512-kRxEaldPSX3LDfik2uJLMT/pmOng7DMWPunEvRjbr9Rh5QQyhCeuiH8xs4Yebbm1em3u/IY0CgJ/SWnH0yA+rQ==", "signatures": [{"sig": "MEUCIQCzUCEaxV0IV+T7HTvQl9ChjAseU8PNputqzH8hdQhq5gIgLxQoG9BuqxHY1D862VAG2IM2SBhCWDEDclJ2rcUOVZ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\http-errors", "_shasum": "0c451582cb4b296b9f822a0ea13eded6cedf87f8", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\http-errors", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for http-errors v1.3.1", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.3.24-alpha.tgz_1467413924390_0.8496618415229023", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.25-alpha": {"name": "@types/http-errors", "version": "1.3.25-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/tkrotoff"}, "license": "MIT", "_id": "@types/http-errors@1.3.25-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "ae637a9cf55c7bf094a3eaeab80e956e41448383", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.3.25-alpha.tgz", "integrity": "sha512-wpGvJVN3tkvobbFyWtKHHOZ2blB9sabIxFjjZNDEhjhIVuCsxPOsuNDtw41mMTQk/MC+bRvfYGoiT2nUB+Mgpg==", "signatures": [{"sig": "MEUCIQCZmSJvfsUapYbRfHwihB92nvr72EN4SsbVAzv1nhXe4AIgBFT7QNwMJ7DNZbLzIHEHcatmeL9EpIlwwhAxIGg9T0I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\http-errors", "_shasum": "ae637a9cf55c7bf094a3eaeab80e956e41448383", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\http-errors", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for http-errors v1.3.1", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.3.25-alpha.tgz_1467426853622_0.581492206081748", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.26-alpha": {"name": "@types/http-errors", "version": "1.3.26-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/tkrotoff"}, "license": "MIT", "_id": "@types/http-errors@1.3.26-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "758c40e1656ab30fda06902530fbfe1a4450ca3f", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.3.26-alpha.tgz", "integrity": "sha512-gVd5jO2bKJPSGk7odVmMdR0qOFG0ZoaFcn5NcpJm0RVx16vLRtgD9Pu+6lXN4x/6q71T4v2p0OhcwR7BOLcqZQ==", "signatures": [{"sig": "MEQCIAMUK0ZmJC2vA6+7r6ynOKPN3OaeuGWSak/UaYOROncjAiBphkGQz1lccWZ7o70bkcz7LX/SHQrlRNSzSHiMo56O2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\http-errors", "_shasum": "758c40e1656ab30fda06902530fbfe1a4450ca3f", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\http-errors", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for http-errors v1.3.1", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.3.26-alpha.tgz_1467592026146_0.985611475771293", "host": "packages-16-east.internal.npmjs.com"}}, "1.3.27-alpha": {"name": "@types/http-errors", "version": "1.3.27-alpha", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/tkrotoff"}, "license": "MIT", "_id": "@types/http-errors@1.3.27-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "c1961790642287d6549bd0ea883b2c3b8b218b11", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.3.27-alpha.tgz", "integrity": "sha512-s+u9A2nWQNwSU/40wFEx83enMVy0dKAEDR0QZIeQD65NXGp2azWMBL278eV0L9X0QEyI4F1156AClftFKGUqHA==", "signatures": [{"sig": "MEQCIHPt4aGE3ElsTEz+I6z/pqGgWB+iAKFm8cpe54WOJV9+AiA5wvt64zLG5w17/aiBuLHFV9Z6sHrPcTSFpoVW03tlDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\http-errors", "_shasum": "c1961790642287d6549bd0ea883b2c3b8b218b11", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\http-errors", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for http-errors v1.3.1", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.3.27-alpha.tgz_1468009543120_0.43040690408088267", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.28": {"name": "@types/http-errors", "version": "1.3.28", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/tkrotoff"}, "license": "MIT", "_id": "@types/http-errors@1.3.28", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "ecc1c6d245adaddcbb0fe5c2c7a77428ac8d9be7", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.3.28.tgz", "integrity": "sha512-FtHq6ZVQA2V4/10WwlzQg6iB35BSqbqAm/t5WmrUy0iwyf1L9FVmwC8+BP9q9Hl1EnfK0gPEU6Od+gMkKXUcLA==", "signatures": [{"sig": "MEUCIQC6Uv+4Anyyu/ZYFkXVPtzcrdONMe4hnzKzIkDlf0MosQIgAIS1Z8guW05GMVnc/lBoTDOaLHPnAE9MPO3ZFwUrMN4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\http-errors", "_shasum": "ecc1c6d245adaddcbb0fe5c2c7a77428ac8d9be7", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\http-errors", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for http-errors v1.3.1", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.3.28.tgz_1468507955891_0.707784435711801", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.29": {"name": "@types/http-errors", "version": "1.3.29", "author": "<PERSON><PERSON> <https://github.com/tkrotoff>", "license": "MIT", "_id": "@types/http-errors@1.3.29", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "1f63c18c631fb07ca185e27eb2a738051665562d", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.3.29.tgz", "integrity": "sha512-051JDhGah5s80YfNgEfG+B6DIdIZFFqiJ+MFc96ZurH8KOWMKtiqnLalNF9sR9gpgrCsZhRbSWT9Ezlm4PwQqw==", "signatures": [{"sig": "MEUCID8EtoJfT6unazumzwBhld7dJRtMwSC64WPpw2fTxG9gAiEA6Y/gbyGq23SlwmVo0ZK+5NMXO7D5ddlI5pP6FrJVGlU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for http-errors v1.3.1", "directories": {}, "dependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.3.29.tgz_1474306712229_0.8794106119312346", "host": "packages-16-east.internal.npmjs.com"}, "typesPublisherContentHash": "d31e15b3834c2af1b29fa66df2814dccb3ef67ef24f589108311063267ebdbfa"}, "1.5.30": {"name": "@types/http-errors", "version": "1.5.30", "author": "<PERSON><PERSON> <https://github.com/tkrotoff>", "license": "MIT", "_id": "@types/http-errors@1.5.30", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "69067a5255bf1217eeeab715b942d1ab51e4c619", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.5.30.tgz", "integrity": "sha512-peTb3wtFzoo//+fCoAM1Ijx+hNVDBMMSoiXEik4M1EEdvKWE0oHQDgT2uCmn7Y9PHCb4en4Vy9uJnqxitSz8mQ==", "signatures": [{"sig": "MEUCIQDtk5Kdhmg0eZq7xD/BLik0WYzY69D39pt+TLiloYPqKgIgCVFr90itaIwNW3Q57BXPngLbgARCrcOh6DyZlRznHW0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for http-errors v1.5.0", "directories": {}, "dependencies": {}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.5.30.tgz_1477509975747_0.48789541237056255", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "3e3e3b9e4d14d4e68f989d466ce8729a6a495e39d86fa03a1b1cd267ab1969fd"}, "1.5.31": {"name": "@types/http-errors", "version": "1.5.31", "author": "<PERSON><PERSON> <https://github.com/tkrotoff>", "license": "MIT", "_id": "@types/http-errors@1.5.31", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "e319409ec35d592397030a8da7864677705a9b0a", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.5.31.tgz", "integrity": "sha512-TjEXWm6KkKly3uyyG29RD849VDMrSVtkNqUe3iOQ8lA/FF99bkcBRKTrMTDZEo0UNQ3wFwRVvI2QazSevfrsSQ==", "signatures": [{"sig": "MEYCIQDDxf/hhV4/B0DWPn0fCgRTt6ThjjGULFjWw6kqvlLATQIhANFEz2Ywwu6qQkoVK2rVpj9ZH1vfTwrBvSduLBHSSO9D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for http-errors v1.5.0", "directories": {}, "dependencies": {}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.5.31.tgz_1479848423540_0.9457758301869035", "host": "packages-18-east.internal.npmjs.com"}, "typesPublisherContentHash": "1e13fb6297235645a2fb2d5fab4660db60f67f20ba057fa0325eeaff41212040"}, "1.5.32": {"name": "@types/http-errors", "version": "1.5.32", "author": "<PERSON><PERSON> <https://github.com/tkrotoff>", "license": "MIT", "_id": "@types/http-errors@1.5.32", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "b7a2a5a7e31e2b822517b56e32b1c6c1a5d787b4", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.5.32.tgz", "integrity": "sha512-q12dVqhm2J4TfoykaZa7/I1SK20q7xj27wuEchODSgaSvdQ5rkftuB3J2vRSPiHzVPukml8y5YmtZiVAKl9dgg==", "signatures": [{"sig": "MEUCIDJ0irUYtv8lUJC+PBiWjrC9S85HQQkXDR25wQwNPUB7AiEAxNg1Uqw2JKA1m/gZdLlYgj7q8wZDUtfQA4+09aKbiTE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for http-errors", "directories": {}, "dependencies": {}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.5.32.tgz_1482884335058_0.4293472319841385", "host": "packages-18-east.internal.npmjs.com"}, "typesPublisherContentHash": "fc691eb0d330476ded52b520d4f0cb7133de7e429e4edb5b0238ac40c888e180"}, "1.5.33": {"name": "@types/http-errors", "version": "1.5.33", "author": "<PERSON><PERSON> <https://github.com/tkrotoff>", "license": "MIT", "_id": "@types/http-errors@1.5.33", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "ae2d1dfc33745ea548f1ae06fc338cdf66406770", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.5.33.tgz", "integrity": "sha512-W6TnKxKxvZQnlc7Yc/n2he3E79S2iS+QIjprcviQ/65Sha72kboS57kI7/ix/Vc78hNR+srZCHQZBqLqFIiuMw==", "signatures": [{"sig": "MEUCIH6fjHU82a52WVkd3+Ok5JCcfPd61qXmRHcpwPjz20HEAiEA2ysA+E6osQJ8RgxRXrkjIj91bv/2vqKRBUChbXvcSXA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for http-errors", "directories": {}, "dependencies": {}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.5.33.tgz_1485375398233_0.4786070603877306", "host": "packages-18-east.internal.npmjs.com"}, "typesPublisherContentHash": "73b449a7b1bb3393d4f609a8c9af62968ea73ca0d13f188b0b33c58a44779193"}, "1.5.34": {"name": "@types/http-errors", "version": "1.5.34", "license": "MIT", "_id": "@types/http-errors@1.5.34", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>"}], "dist": {"shasum": "d6a56f25d7b95dd07047680bf825632e29796815", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.5.34.tgz", "integrity": "sha512-RT2qsQsIY4qfuDPShB1u5dvGh+vsOrW1IVErrs72jyBQO4qtkygL9uKerD4+em3JudWV8hSco4k+QLUSWrJt1w==", "signatures": [{"sig": "MEQCIHtXIgWcKPoFUBhS+xmYzu70Ggg58YIp9Pe2RI9zI+wsAiAUq8IkaFAnoZPI28jofkhW/naYPq/T7+NATywE2hewRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for http-errors", "directories": {}, "dependencies": {}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.5.34.tgz_1489551080965_0.24180646310560405", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "c1d1ed1b04f07a92373d156a59dc34609fc849c23d22ff68433a44c7a950f0e8"}, "1.6.0": {"name": "@types/http-errors", "version": "1.6.0", "license": "MIT", "_id": "@types/http-errors@1.6.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "91fb247acbde11632a5a532b2713b4dea03ccfdd", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.6.0.tgz", "integrity": "sha512-bKKGJGIdOUYYeL/DWgBfS9b6UjGFMLx1B8bW20f+ir54qDH+DGUftDrWR7j206DsImgrgefjcWX2EfRIO1Taeg==", "signatures": [{"sig": "MEUCIEVX4dSUgAOgYJFrXbfbJ7naAkzHr1Bqgnlu0mIhJATjAiEArQnGFVO0+CjwYDy6CNmd6wfQX0TPd17GDcPpwRuyYxU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for http-errors", "directories": {}, "dependencies": {}, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.6.0.tgz_1502921242464_0.6647931605111808", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "512e071ba3faa2c95cae65b2f9766a821489ab8810dc2cfcc51f2953a22e6743"}, "1.6.1": {"name": "@types/http-errors", "version": "1.6.1", "license": "MIT", "_id": "@types/http-errors@1.6.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "367744a6c04b833383f497f647cc3690b0cd4055", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.6.1.tgz", "integrity": "sha512-s+RHKSGc3r0m3YEE2UXomJYrpQaY9cDmNDLU2XvG1/LAZsQ7y8emYkTLfcw/ByDtcsTyRQKwr76Bj4PkN2hfWg==", "signatures": [{"sig": "MEYCIQDfkPCLQQUPce71tKh0PoTLk97rNCIb+M2dQrkGJZZItgIhAPwlFQd/N/FvUyzb17/6apTY/pGGBGvl/EZUigFJQUTx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for http-errors", "directories": {}, "dependencies": {}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/http-errors-1.6.1.tgz_1504216337728_0.0392232951708138", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "722274c0bfcd49e50993dbe2085bb78bf13d615a3dd3187cd99075c67fea439c"}, "1.6.2": {"name": "@types/http-errors", "version": "1.6.2", "license": "MIT", "_id": "@types/http-errors@1.6.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "8d613d31be6871d093ef4640ae3272507156ba59", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.6.2.tgz", "fileCount": 4, "integrity": "sha512-mzJX3tIbtadNZQIDbfA9eW+mAjww7GY7WYcfKDGB5SSXMAzI8KD+5fvyX5FqcKtns346+WGwAJJ8cPsDxMz0lw==", "signatures": [{"sig": "MEUCIQDikc/rI/tSaRCgyIG/N/XepgS3Ta6z5PFPCYh4c5Z0YgIgJY6M55sU/IcQZRUzs6n+qjcVWDD6mzIdeWeB3DvhlyE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5457, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdWfZHCRA9TVsSAnZWagAAhekP/1dC6D+Qlk9hL9oVooGy\nKAEkgOkyoXBxOX7MQIO/6v5W/edsIrRkDmEG4wqOmGUt1ClaYVFwCva8+ays\nVeht58vEyBIKnbNLXig8rpgZgEoqZ7FsM+GJnexQhn5J1OstUikoQW2N7Nty\nSNfSgl7oEUAwjJ08tDJXlk2kruDeG8U4Hnc+W4jEGljgBgejukLrU7sishKM\naStybX2aBUDV5GRxS8z62ZvwF18xVxM0emz7EzD0Mr4xSx0WuK74u7NZ0ZDr\nx2Y2dPdjtSZ9Mr2aYptU+xVllcB32vhD1Aix16JcNlFLcshmGK/AMLEC4hSa\nsYMOhZ+VEnjV+3NrGnOia0V5PxJuT1zMzKRtZ3mKen7/g/cUqOiPwDVUtxjN\nzm3JlOqo3UIIOHyawcCXm6Q4+maXMKEg/2IHpNJvxqw9x+GaAj8LiiMY6K8j\nEEdHo1F6rQGfPkOtDN17J4WToN24lOj26eLh8ho6mgwqIOLKGbIh9FwM8IkC\nYb0CXKe2+SudVHDJMwkoGb7th5n7VkGNs3Dm+hqJJoRjJhJRHJUiCG3cz4Ax\nJNyPdB/5wt4siKncdtMCszTiPGotapt3At0OjDHiwkhUsA3b0BgLhEAMFSeU\ni150JnhozdysNnL6y78xq2ISRlPnmOALr7cUhflO2nX+wkulgF4/z2EgJv6n\nn2hy\r\n=+PoO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/http-errors"}, "description": "TypeScript definitions for http-errors", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/http-errors_1.6.2_1566176839295_0.7373935630530102", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "aa878c13f27a0196d6a5dc6ba7413608867244cab353100f84ac3ecbb4fb740a"}, "1.6.3": {"name": "@types/http-errors", "version": "1.6.3", "license": "MIT", "_id": "@types/http-errors@1.6.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "619a55768eab98299e8f76747339f3373f134e69", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.6.3.tgz", "fileCount": 4, "integrity": "sha512-4KCE/agIcoQ9bIfa4sBxbZdnORzRjIw8JNQPLfqoNv7wQl/8f8mRbW68Q8wBsQFoJkPUHGlQYZ9sqi5WpfGSEQ==", "signatures": [{"sig": "MEYCIQD5F3K6Nz28cc4fxND+mtrkHFffHqlkPEGzD7q4ufG15gIhAKba4xEqDcKhx+233BH3fOZH6JpB7+afG8ewOzaQkV1P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5501, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd1EaUCRA9TVsSAnZWagAAMigP+wYWrBcT9mcR7IJGMQ25\nCgJBOJlifwE9xGw2Z8IQpyNAPKfTbaqVrS5bVrqn2pMRV0kGuTJHLYEJG5Jf\nw6Bf2scNZnye7kWrWeH+ObVwv9LRxrmBMnR85mcvKBGH+46U07xVkHi08YTI\nkfUnY3kBKE1gZDu5lxToWnK6VEpvTIWUCcPVvZDTasXgty/+iUUmcvOVmkv9\n+7qhOkq3yKLFjin8sYUVLJYg2m9KsRnEpVITa0+phvm8r5ZUC/TNjKzwGTl0\nF99UFPKcQDK9NV89/gcj1BTydgqoSExxm+JPXhRO+xUfTn+Sy+AWcOeuzu7U\nKzkz4I7Pvb/tY5pycsSNeppazeZAFI7YhHonjKtMB56kJNC+9iEEjYtTXRDe\nadgpOZNBM36g42sY/yJS091Yq0mpFZSungg/TP4CxqlbvJ3RZ+GQlRSHP43t\n2ADDEuM5aYLwv6PeyOCC88bJ7V1jg2ZCRSOITz80t9bwryls2RKnGO2NQiZw\n90Iyl6eTM5SsZPKQ33BotTBeeDhgfAEDl7qkgbWGLjDUDbPRVExfXYas7o1U\nna0Gsds0Kdj21cML8ciQXx1RKnaszOoBIPmWNOW16ZdJRSUA61OjRwjH57Gb\nWCkeTOP49tsfk9FYARY+VQ7lJ2byOjxEWpeddO++z8EBCLe5Q/hjZNp2YfD9\nOo1y\r\n=V08U\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/http-errors"}, "description": "TypeScript definitions for http-errors", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/http-errors_1.6.3_1574192787962_0.897382711923536", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "2c47d02e158fb74058d2f54804728805fae067cafa130447d99a4495534fd759"}, "1.8.0": {"name": "@types/http-errors", "version": "1.8.0", "license": "MIT", "_id": "@types/http-errors@1.8.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "682477dbbbd07cd032731cb3b0e7eaee3d026b69", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.8.0.tgz", "fileCount": 4, "integrity": "sha512-2aoSC4UUbHDj2uCsCxcG/vRMXey/m17bC7UwitVm5hn22nI8O8Y9iDpA76Orc+DWkQ4zZrOKEshCqR/jSuXAHA==", "signatures": [{"sig": "MEYCIQC5c0K5Ult0mwNJF7eQnkSCK2KhEyWWAxb3nn9QeaVbpwIhAJUImtu6xZerzziPt4Z5hyZDJf8bVl3be0Dkz3jSINfI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCHhWCRA9TVsSAnZWagAAZKUP/jUzWmexCYQ0OCTKIkmY\nntFf8Fu9Kz0mGsMShucLUlrTd8+hvS8ldXGrU2IDhp4bMPqQQKWzdP0c/+83\n7PgtUgKIYJZ5xvAub13+K8K89Aq2Eq9lD9X48VaPUSxYFwOVNbWD6snO0JEs\n/3qWdnibXGooYksrAZnqPrMJdCSlf1hcGWUGOLrF7f3rShOY7sDr/p5p9+yb\nJcfxqT4rau4uGBm4L4zX3tWz4DjeQ/BjMOnaf/9EHEuSubtq3mhoKkQ90+Lp\nutj9TEmIhp1cjiom+G6o4wRY3S58xGd0kx64h5yeAgQrjYghK0YVFat85M/p\nd1ndrmhu724IqRPzDd26htivXf03mnqeHAtwxaPZ+gnjyTEGJ3AI26dJPaOc\ng8txcQUkboLMmy73QZmHSrGzXYMTv7dowzEvOmxXZOsvEoGygRiFjZGCwvgZ\neC+ME2lWUZDKKSvE65Dr1FkHusj8Klp2jYWGY9M5YICUMlgMekS1VOX3t1ze\nQ9M8/PqlDqDKhtnGTpmfbWv/uL+Aw6j6687hVSIfFS+HQOzjypDCTg5rmDzw\nr2cqKJvjJsgaXYzkwRV0efT7uV/x0lvhyKa+56K8b6YQ3j4wREUS8q51IBuD\n1d8fOcCkmQ4+gcFZTkIkSJpzbCQkv9M11uau9UkHyOBiraialGolYO9oq4aV\nEBwz\r\n=+hGS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/http-errors"}, "description": "TypeScript definitions for http-errors", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/http-errors_1.8.0_1594390614290_0.7408102596160266", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "61c68cc213bf1a21d07bfac01c18754adea53b40a66a043c89fb6a35957ac0c9"}, "1.8.1": {"name": "@types/http-errors", "version": "1.8.1", "license": "MIT", "_id": "@types/http-errors@1.8.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-errors", "dist": {"shasum": "e81ad28a60bee0328c6d2384e029aec626f1ae67", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.8.1.tgz", "fileCount": 4, "integrity": "sha512-e+2rjEwK6KDaNOm5Aa9wNGgyS9oSZU/4pfSMMPYNOfjvFI0WVXm29+ITRFr6aKDvvKo7uU1jV68MW4ScsfDi7Q==", "signatures": [{"sig": "MEUCIQDwgh71gJgwsQjpQiCRKdJvDF6ECLV538AD5qoLFY3jTgIgChJWUW6lsxPJb4+nBa9AanYBJDfKDF5P0Kw7boWaCFA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5688, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5M0RCRA9TVsSAnZWagAAOSIP/3q1bJgHObRTGza5E/vl\n6lSeT795QuEG6zdXhbCHCqVCgir59r/QXIAwnPFJzfPnJ6mIHKZnsrBSqzYc\nRu/aEuzbl3f2dqnyXsbt0fw7CztSTRjUcTxqysW5/HOz+layfkqv9x5hXXTK\nxkOvZ7NW/eYC1z2xcKqT2SKNY0eGiR5mM+rcfSGsbPhbZPwSogNDk/mjlFis\nZnQUH7LlSdUlKGg/KvGHYZgl7hobGvAW+dhXKWGljscFOiOp28Gz+DGySNjE\nE90neXCTOkcbzS5J2pWBvajNMZo1pFLspwt9DRLvMbHGET7nFOb+3+pf1dTa\nIVVa6wDWZ7DNko9+pWT04ZEZwWiE0LT0eK9Hh3V+NBZ8dzI2NmUYI+QcaVpK\nWFXWLC33lPwo64e4uKXy15DQ056zA/ltY4g/E2Id11XuiW9uL4pML+cB0Cly\nfymrYaB+wvoRVFxmGddbLePFALw/n+osn0xITSgWqS9LKbrIGGBIwEcL7A37\nAWWMcutOSG6qCJCfmjwiK/Q+IWn71yothqrZqoEWZcOdpwmUAKVK+A+NS13R\nrAkb8LCpM4+cSIRdWK+XKRwgFBXshRWaBYQgswQ5rPctxxoYRVfij8iTkk9I\n+CC28D2BZoavEGm2Q72ywKy+fTe5eusyIQYkCgkHCXQNsJeMdaLlPcw1wol0\nWOkk\r\n=99F5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/http-errors"}, "description": "TypeScript definitions for http-errors", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/http-errors_1.8.1_1625607440922_0.1517662474167898", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b19a9052d35058a24569cde965e62a59db1ef8d456381598978c2d65370c634b"}, "1.8.2": {"name": "@types/http-errors", "version": "1.8.2", "license": "MIT", "_id": "@types/http-errors@1.8.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-errors", "dist": {"shasum": "7315b4c4c54f82d13fa61c228ec5c2ea5cc9e0e1", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-1.8.2.tgz", "fileCount": 4, "integrity": "sha512-EqX+YQxINb+MeXaIqYDASb6U6FCHbWjkj4a1CKDBks3d/QiB2+PqBLyO72vLDgAO1wUI4O+9gweRcQK11bTL/w==", "signatures": [{"sig": "MEUCIQD3w4LV4FjkW9NUy49v4EYc2BclyTRAwkI1CaSHpOJr8AIgB6UF/TXmJ0gJOeAX2kAlTqpaYunBxbdtCJvXiLOjI3w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6724, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4IVZCRA9TVsSAnZWagAAYlUP/jn91LeXxnpC812NZN/b\nM6wcvV0uELX/JVQSz6KRsXKCOhhO5P16teTQBDmk7VcW/9Xr4bHpY25o5O5G\nRq0sDy3v7cA4iVk1iA48ZhQ8QhfbDZOGc5ZLhwlIVMsLroiCQjXsvHJ8BU8A\nbGleMLHghnfMrPb6xRwpRyxlNdrXyMEyMFyFaHsKqPyrbK4nfZFb/xBFw/Ll\niuyxL5JmmjntOsbQvoDuOAruzWVp+r6qh7TtBdinfrDlQF7mOGdkCrCGrTBY\nERVJx442trBVVx79E99crtP6ObHFsVFhFBacAq01rGAfzKvshcIqknbADuvG\naUwNT1phxO++8Xj2Tj48gzbz5T3fpL//I7v9ZTN4EyrEutiqNQNZlcoqH6dO\nQPhBnCoNwtAAvvY198KzVHqbIjt8tVNuKlvIMFmQABu4NdARh2qQdnlCy6TZ\n16D/KmK/6OtfUc4T1fam2gGR0lqA5GIK2qS0a+9j3VoLqS6pIt3RNWUDLqBZ\nwWhFfpnA8Umr8enrKIHRJpVuxbzg41eBHQhG7dcVnApi/+iY7eVn1ewyJERj\n4gbGU4YFurzNa3s5lKhyNG1+Lt6QxZDHUuJ4W6/kc1fcTrx88rRjRFlKZJ2X\nzP5RoCGDweuCUxw6gJpX8DM7sPa899giRi13ZuWPq/lQNJC2IPQF67IOGGlZ\nA8rA\r\n=SGOP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/http-errors"}, "description": "TypeScript definitions for http-errors", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.8", "_npmOperationalInternal": {"tmp": "tmp/http-errors_1.8.2_1642104153001_0.34246781704495266", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c2479b8b9d3c81b8d5e05a4e9af2847027b1274dadcdd92a5468f98c98449978"}, "2.0.0": {"name": "@types/http-errors", "version": "2.0.0", "license": "MIT", "_id": "@types/http-errors@2.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-errors", "dist": {"shasum": "c1f34eee7f9fd5ddee366d15e2fc39b93c31532a", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-6mkB8Gx7Of1yjqqh1nLH22ATNol5+mIXVFaQCOQY+R3JxB6/soiqKAxBgdSE6R/1IWu69rR4cJ/027TxLEs/Vg==", "signatures": [{"sig": "MEYCIQCHDHtCOyJ1WzSF0pAUEWYKcJIAFi2SCYe738NLjoSdSAIhAIGKC/r69VvIJKJXYo0acIL1cu2L/kLi8/QHAYTqNaZz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6724, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWhgAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqdBg//ZjV6iH2izVp8gbMamedaDc8Rxua0ao5NihtnEURAWTZ/bL1R\r\njE5I2fNWvOiTju3qLy/HPldotVrjhBljbgmD5JlBnFtBZAi04XKFHNyPfuNf\r\nWNPlhGtffBWTdEsUJOiXrWF34wOdr62bwSD8aLAOu+Ll1OQYbYASO/PjTG3A\r\n8xfvQdjsQc2nkbe0C33FfZ5O9yBe7BDKaJPv+T4y6p9fX/Fg0Rv9xCaInDoI\r\nBhi6QPA3jIE4D8Ng+UAwALNjOY2HdGj+y23cz5HhGXjA6zuhFsR+hs2c2pl/\r\nkaEPsxLFPRFpC+SrnS2J1gmOwMtq2xKzZmr1gZ96RjVIem/qdv2L6IUY/4do\r\nK6+nyoQ6zRXjxY6Zcav7sb0C7rGRID6kBk5xqfkF1VzNKiFPQSX5ZI1U5cDG\r\nbepmh0GIcm/QNgKmASRtYMmi+SXnXn3p0qhTU2xu8P3wT1PBbCsAUE7oOCnS\r\ncYmeqTUwYY2Ne4dS5gHru98X9ZghJ4XLjooue4NneJg6vNmOZg1MTMpgZUB/\r\ndHt14RHLX5uZ9AjduGIAv+4W7LLjtrQd1cEhMQbjeUsIzXKD+bHIsbi6MSvp\r\nD3PT/RVLp9J+GaHHDw6a9l1zlhhLj+IP3EZPYHbc0gX7UY2cSeMt/1KgVUfN\r\nqp+cDmHgLd9+vbkb5KafCrE2WKg/qqocNFA=\r\n=H5GC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/http-errors"}, "description": "TypeScript definitions for http-errors", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.1", "_npmOperationalInternal": {"tmp": "tmp/http-errors_2.0.0_1666848768039_0.8593808908930674", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "2a471cba5e01f865223de296b7656093776797c20354f1d13c0d6ee5f98b31a4"}, "2.0.1": {"name": "@types/http-errors", "version": "2.0.1", "license": "MIT", "_id": "@types/http-errors@2.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-errors", "dist": {"shasum": "20172f9578b225f6c7da63446f56d4ce108d5a65", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-2.0.1.tgz", "fileCount": 5, "integrity": "sha512-/K3ds8TRAfBvi5vfjuz8y6+GiAYBZ0x4tXv1Av6CWBWn0IlADc+ZX9pMq7oU0fNQPnBwIZl3rmeLp6SBApbxSQ==", "signatures": [{"sig": "MEQCIGo86uGrwaLs+Fu49uojneTQz8MkwO8+R10skzfzlnIIAiBQoW2CIS7WTug6hrbrTuvILoWUhKY1YTPe8KwkjcH1KQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6724, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYGInACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZzRAApNjNI2fsiIH92P1vAYFd3VVDUGeV7wJ+bbkCuJmdIJyPU+Sc\r\nxUJYsb/KE8Blg24r5KdM7l4zvLd1j+Mn/ghMwheLWdCF5obY0spLOq9Gxawe\r\n6mlqHZd5Yy0TYBef1R5oBRWTTUZtx9aDqv/h92IbJpCcBxuz2CfcnvoEI8K5\r\nO/j2snj0rrft52TlTmjdPttvM+tEI7/KD82J4a0TqSqVnleU4O64nMBZ3eMK\r\nmzfhMPXNlbAkibNrGdwK2teTJ/TQ19bQL4iIcegNdVyy7B1odoUTFO26Bzd0\r\nmm9l5qwuzrX7w+OnaXgZDZKTvJelxUA6BtGsnEPK4hAYpd2JTjmFdEPGwvts\r\nYmUTVGoX9OOgWFAwFjN44x8n9AlgkfPYVntgPQZqyb1LeLP+1u5Sb7DxQl3e\r\nNXrErGIXxZJwtU0fdi6g3qyDJ6QK6vnwXO7KHyu/wWNMhGukpPISRoSGUdfU\r\n4ywVVbblOwxjBHHvVgkH+svN3bRhBwEqbkx4kvzZIcb2QUCZwkjT5SEUgRE/\r\nUAwpg97Cx/5+Ec9Dap+lvHO96at7Znwejw5VYowKi3xNjgIJYDRaMGIoQmf4\r\nI4nxUYuPlX645sLrWnjhRroEONLPh+xa3fmGVjQ9d+ve6uFT9aXFgz+9pQkz\r\nHReZeRqB0tlWvd6XisY6lFdYcOaIY603rjY=\r\n=ylTW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/http-errors"}, "description": "TypeScript definitions for http-errors", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.1", "_npmOperationalInternal": {"tmp": "tmp/http-errors_2.0.1_1667260967363_0.528200268044827", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "1dfc69d7710a0e2de926b0320f119575b2626e7d4b615157ad092d057e039373"}, "2.0.2": {"name": "@types/http-errors", "version": "2.0.2", "license": "MIT", "_id": "@types/http-errors@2.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-errors", "dist": {"shasum": "a86e00bbde8950364f8e7846687259ffcd96e8c2", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-2.0.2.tgz", "fileCount": 5, "integrity": "sha512-lPG6KlZs88gef6aD85z3HNkztpj7w2R7HmR3gygjfXCQmsLloWNARFkMuzKiiY8FGdh1XDpgBdrSf4aKDiA7Kg==", "signatures": [{"sig": "MEUCIQC0hb7c8r8/6e76PIE077GJ9tVXuPG2Lvpe5+xk1tPAAgIgJJPyY3B7PnUMEcdI74KQwh06d/tWYAIV1liRZan+ZBE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6901}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/http-errors"}, "description": "TypeScript definitions for http-errors", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/http-errors_2.0.2_1694852430702_0.1582161712654031", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "27b9b10313e931475831cb9345e8556d2255a79e8f74c65d03d53196e734e182"}, "2.0.3": {"name": "@types/http-errors", "version": "2.0.3", "license": "MIT", "_id": "@types/http-errors@2.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-errors", "dist": {"shasum": "c54e61f79b3947d040f150abd58f71efb422ff62", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-2.0.3.tgz", "fileCount": 5, "integrity": "sha512-pP0P/9BnCj1OVvQR2lF41EkDG/lWWnDyA203b/4Fmi2eTyORnBtcDoKDwjWQthELrBvWkMOrvSOnZ8OVlW6tXA==", "signatures": [{"sig": "MEUCIQDwJ56sY7RwqYCfV+N7qmpX6sczuJcgVtXy9PgGynqcRwIgIjyIExFCbvtifiwUB3/xOXwS1Gj2C/hknd8Ji1ekjcc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6587}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/http-errors"}, "description": "TypeScript definitions for http-errors", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/http-errors_2.0.3_1697604830192_0.03886495072947227", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "52dc708613837b4946f0a0733e2f2ae3e8f3b4213d5ee67452039f1d95475e7b"}, "2.0.4": {"name": "@types/http-errors", "version": "2.0.4", "license": "MIT", "_id": "@types/http-errors@2.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-errors", "dist": {"shasum": "7eb47726c391b7345a6ec35ad7f4de469cf5ba4f", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-2.0.4.tgz", "fileCount": 5, "integrity": "sha512-D0CFMMtydbJAegzOyHjtiKPLlvnm3iTZyZRSZoLq2mRhDdmLfIWOCYPfQJ4cu2erKghU++QvjcUjp/5h7hESpA==", "signatures": [{"sig": "MEQCIGEs3KvuCqdWtrr+IgnLx2NFKhb2prfoo/Fg1RirBswXAiBsDwNsuDoIOBE2KOX6f2RUvLnkKzRGw8huKeJwj1sacg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6587}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/http-errors"}, "description": "TypeScript definitions for http-errors", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/http-errors_2.0.4_1699342500666_0.6483105055895684", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "06e33723b60f818facd3b7dd2025f043142fb7c56ab4832babafeb9470f2086f"}, "2.0.5": {"name": "@types/http-errors", "version": "2.0.5", "license": "MIT", "_id": "@types/http-errors@2.0.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-errors", "dist": {"shasum": "5b749ab2b16ba113423feb1a64a95dcd30398472", "tarball": "https://registry.npmjs.org/@types/http-errors/-/http-errors-2.0.5.tgz", "fileCount": 5, "integrity": "sha512-r8Tayk8HJnX0FztbZN7oVqGccWgw98T/0neJphO91KkmOzug1KkofZURD4UaD5uH8AqcFLfdPErnBod0u71/qg==", "signatures": [{"sig": "MEYCIQCjcw2MCuWYNrTXJ5k2ZSs+GBL5uaI0l+XazQL14SvIIgIhAMp1xCsxIx/Q7Ho94AOCcYcZaDYlfcgDXClLzc6cXIZC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6827}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/http-errors"}, "description": "TypeScript definitions for http-errors", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/http-errors_2.0.5_1749262653244_0.2992151691745788", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "621b9125a6493a2fa928b9150e335cb57429fb00e3bc0257426f1173903f7a4a"}}, "time": {"created": "2016-05-17T05:10:37.874Z", "modified": "2025-06-07T02:17:41.197Z", "1.3.15-alpha": "2016-05-17T05:10:37.874Z", "1.3.16-alpha": "2016-05-19T21:10:07.442Z", "1.3.21-alpha": "2016-05-20T19:47:16.282Z", "1.3.22-alpha": "2016-05-25T05:05:41.680Z", "1.3.23-alpha": "2016-07-01T19:32:26.479Z", "1.3.24-alpha": "2016-07-01T22:58:44.895Z", "1.3.25-alpha": "2016-07-02T02:34:14.151Z", "1.3.26-alpha": "2016-07-04T00:27:09.407Z", "1.3.27-alpha": "2016-07-08T20:25:45.648Z", "1.3.28": "2016-07-14T14:52:38.180Z", "1.3.29": "2016-09-19T17:38:35.296Z", "1.5.30": "2016-10-26T19:26:15.996Z", "1.5.31": "2016-11-22T21:00:25.514Z", "1.5.32": "2016-12-28T00:18:57.030Z", "1.5.33": "2017-01-25T20:16:40.078Z", "1.5.34": "2017-03-15T04:11:21.222Z", "1.6.0": "2017-08-16T22:07:22.533Z", "1.6.1": "2017-08-31T21:52:17.884Z", "1.6.2": "2019-08-19T01:07:19.412Z", "1.6.3": "2019-11-19T19:46:28.092Z", "1.8.0": "2020-07-10T14:16:54.419Z", "1.8.1": "2021-07-06T21:37:21.067Z", "1.8.2": "2022-01-13T20:02:33.228Z", "2.0.0": "2022-10-27T05:32:48.184Z", "2.0.1": "2022-11-01T00:02:47.524Z", "2.0.2": "2023-09-16T08:20:30.972Z", "2.0.3": "2023-10-18T04:53:50.411Z", "2.0.4": "2023-11-07T07:35:00.958Z", "2.0.5": "2025-06-07T02:17:33.428Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/http-errors", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/http-errors"}, "description": "TypeScript definitions for http-errors", "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}