{"_id": "@babel/helper-string-parser", "_rev": "38-b00398363f230c06d762ff85e1ba4ac2", "name": "@babel/helper-string-parser", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.18.10": {"name": "@babel/helper-string-parser", "version": "7.18.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@7.18.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "181f22d28ebe1b3857fa575f5c290b1aaf659b56", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.18.10.tgz", "fileCount": 4, "integrity": "sha512-XtIfWmeNY3i4t7t4D2t02q50HvqHybPqW2ki1kosnvWCwuCMeo81Jf0gwr85jy/neUdg5XDdeFE/80DXiO+njw==", "signatures": [{"sig": "MEQCIBXwBJ0ZjugkRs1Ms5RExWQTWNOVM1WvwcUYYBEyXY6lAiAIQVqYrxa/c8CNVljXRbVNGT/gGpwtLybSmrT0bbBPpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9628, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6B+QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrK1hAAlhZVobkUASCqWf7hpeZwRpVf9SXcERbSihyGgWk0q6nzcnxS\r\n4cNU9niw75QXCsNRSpE7MuUxzKPkxZn7ibRdvltn5gQ9Fh/8VLYaBHOCNAaG\r\nECLJ6GFctdfp4knZ2hs7tA/nictM5siqXohrl/Ujns56p9pZgg0P6nPAyBXb\r\ngpIPg0smCClrEUfadjF6IIdr/lS1eRpywLgA/DdKCcy8o8duvAq2lGgYqIZG\r\nzMud3mPemxjeuK2mNw3nB66uzOlhXbYwDHWglzYHWIqNFnekpoFmqoDUn8hn\r\nDlad7WOrt6/8uxuGP4VOTBMhG14/aVMsSnalJlUH27LQjGYxyLhTf7AWkYkT\r\n5D9R/33raZz6+Bu4l2WCGyhheh6EkOYTyTmnx6BqjtFbukOujtcbVpFnTkr/\r\nqK8fhpZ6GFJVJDRN7VxMsiytDKoUOuwZLxoj8lczyTjyJ2LhMZaPaWZtfDkc\r\no8xznvCAUNkCkPU6A1+kTqMRWlodktDy01dPh1jpqv0Eoj8B+2oBePDjslmR\r\nux7SFIFkf3bYy3YmloSCR/tO/a5UrHHGa1v22zpmpRk3ID9/E/Tz5D2XaqW2\r\nhBV59Ko/l0el6zodJhtOQBgbzqxCLZlhDIZrnjsI4PndzdxYHlE2e+dj0WRP\r\niHgQYIqpLYKsaBMjPy0Z1fICo6LkRrPhjI8=\r\n=HBiQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_7.18.10_1659379599990_0.6858281308708796", "host": "s3://npm-registry-packages"}}, "7.19.4": {"name": "@babel/helper-string-parser", "version": "7.19.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@7.19.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "38d3acb654b4701a9b77fb0615a96f775c3a9e63", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.19.4.tgz", "fileCount": 5, "integrity": "sha512-nHtDoQcuqFmwYNYPz3Rah5ph2p8PFeFCsZk9A/48dPc/rGocJ5J3hAAZ7pb76VWX3fZKu+uEr/FhH5jLx7umrw==", "signatures": [{"sig": "MEUCIQC6Hgn8MzrgnbVu6GCPVXOwpiG+4eoMYkH705j+/uicQQIgMcZA00WEuFaI2cXYgRlbup3XbxLzaqi+J6xX+2e8odE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/g3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqxvw//aCfGGkx0IWmWc/L7Da4IGn3prokcF0Q5YKHEg/71o4D3RgF5\r\nvFjCisj1OTOIHSckK+pmbL++rEzcfStZNGUH84mXiZH61GdLRvDHwGhW0DNo\r\n3FfUCwUD7SgsEcKZwQds/8ZGtgYIQFrrSmNvZjXFqlXOau/cQ/5RVFEuf1vT\r\nTuzQV2hEDYZiS80T8lkPHL1v3/mJ+3U/lw422Aa2r5oYcMOV4emTbiKtSsRb\r\nCz7PCkI/L0o2daNE0o/n6Hmqe/BzmVr7kYUhn9GF+sQgOc4g4s9aQ66GYYya\r\njCs4WWm5VdBtru74mIAf9yXNsUexCoey53ZCQiKrjxnoypqIz8z9TkdT+Pa1\r\njv1kS11Z/D4QopsScO8VoajWzWCKiJb3cwCn79/FUponQvQZG5IMJdZei5Ih\r\n7NYE3QO3PkYf/oCWNU/qfcz5awU9d7YQGxuP464Dmxmp2tYbgMzgMC03IxPK\r\nkGDrT4Q+1xni1P9YcCXBGeWcr06xezY8hvXVqYCrKRT/NERiALRb/N6SJ5aU\r\nhyDlmcCKe+m4CVyA4Ov0Pac2fzHkDIQIVVPRM3aPx7WQT3/h74PjDYr2+mr/\r\n3967bXL0mpEVWszuft1pWZA0Df6kmu/I1BA/jw7IfTvFFD+92hD6UQsIs3ca\r\nmGcnu2bb6tNMAFV1f50FgCqSxLJogbNEigo=\r\n=NW0s\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_7.19.4_1665398839181_0.801877268694329", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/helper-string-parser", "version": "7.21.4-esm", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "43450ca97c577dff8327e0214a1c8be56aba1d3d", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-L0l9Ch0SGh5PCRSZ6AwH0CjkqWanAxwZymMrJGFufhIQKK2dHOeiHuLBEiSOe0RrSbxPZCfpzlXClz5MTUd5UA==", "signatures": [{"sig": "MEYCIQCioB6FoLQJNz5xcAJE3+E8GoDxloMs7XwboMAcsajhlwIhANjqmbvHZbqRIFoofHdxp37lmCpNyr3nnCmXDhCvKE1x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31632, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC98ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoL+g/6A+0wPFSkI3/m07vCtiHyqfyTK1A+j9ViZVNArDq3QKDo5wbu\r\n+JfWSx+IzOsvWWMZl8m/SEAwzhuRQ3NQ6ja6/a562KQdppVVXkfR6vTitJoH\r\n3sBosNL7Gugz1hHlBfZ/MVMA0MdOyS/1bFBD3JKIdMPsU8pHGkRATHWp6lLu\r\nJWyx8ov6KS64IWrwgxiVKQ/C5pDCx7YjgO0FwTtvW1SHJ7S5CnpZ104gaZCx\r\nfZCqW8W4ZhCtFmmeOoZ8lqqC+tRZU2kju3/YVtCzLqNBRnvaXk3isqKaD3Sk\r\naUo9rOEsbx6uNB2z4VzYXxmGlW7qyYaKZ4u8V5vLZfttAVzO1Sm2ctx67k1P\r\nM3oB2NEg9r/9YI8WOn7nH8ImPSWY+JDnDsCvBjcJCO92IGxGTrI3VkrIA3r6\r\n7p97Ab5gfXmbJnZHFyxQ3R/X2le3+MIjyB+IQ2nuesHVwRiI+JCW6+wQ8xjm\r\ntBwWu2b3SnqQwcfPOOgEbg3IEP7eFN8ANnt+QIJKZxOnIPHmJms5gqIzddd8\r\nfuRvhAAijJEi1AHC7U/eDdXYe8LqzEdBk6lDpZIzdMgsaSO2mto4HNehUizo\r\n7/W+0yqIjtBOWTM0gqam1XM2pzk1tVo9Y00rR5fv35j3ipBy55z1vNzxhsMn\r\n6hFKNBk4dhJWiDTH5J+BruBVT6wu5z+MMnw=\r\n=Vq+9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_7.21.4-esm_1680617340545_0.05958139356944647", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/helper-string-parser", "version": "7.21.4-esm.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "37c2ac921e93d36d54b384a751c7897f10746e7a", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-zbtzyc1H1shbXco9dpIDUU5CpRsp/Dntr3NGUY5TFsbSYCWHAcEALOedux0ClVTyHrdzDBWpglTJ+rzHMXpsRw==", "signatures": [{"sig": "MEUCIE7EfMk1Yo7uq3Fziuwq3WtnwD8FkBxlbJhToqOjY8jdAiEA//VkXtOymusVxyE3hrtz5p0IGXNNFSnfXQp0iwBMhls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZTA/8CEFbBdrH2YkkbJVwsqT7EiO8cV4WfCL15ldhv80Z42rag+I+\r\ne1E6w4rYo0UDhYiC/30YAB62MN1tSdbknjUHAq9b99LchQsP93rHqBStLkN7\r\nyFuT1QdkPcVaTlqgYVfV28KMIfbUvV+1FJ+pN5FLcHv99guiLZzY1xn+nBbE\r\n4wnv8er6K8HWRUcS+EPRa6w3yutXayQTKjUOgHKCaEnQ5FrMaGzZ/K7P/elD\r\nWHIe4PuNiR2eIm8AhV23KrDrvIZFzUh/qWUGsiKu2Sl7qof+aGDfWg3F8MU+\r\nQyWs51SngEnQ1bz+jJt3pjX4U4/tHT6cBVO6ScMc8b9tcTKwj+FqYaFRZrp8\r\n3RWTszXWaRC/mKaHbq5n3JlSneh1q1kALWqW0VmBUm1ioOHjvaBC5CIm33BU\r\nFvp0Oe6dt1oiNpgh90nbOFFfF/N5Bo0QpxPkAW5NNpkNsEYcc1VG7CU9iKHN\r\n4VLQgzjV+HlEx3yjg7P/9xPultIxMBgcvBBOyY5fbvqex1Nr4FjpbOgLcmWW\r\n3/9M4bgEa5htqoicjBcvzbYtvMQxoG4WaQXnaApZh9NFb0AiG1n86/wvHduu\r\nemWVimCaTipZ5fvql4Je2woT/1iX5gxu8itI1/qFslkGz2SnC8eqVqS2nF+D\r\n9Q9x1rZsib2NnJLLmYuQzdQmKbtExVrAW2I=\r\n=Jufs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_7.21.4-esm.1_1680618052418_0.8237560109666802", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/helper-string-parser", "version": "7.21.4-esm.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "0ec7d0fc667ee6c387898bcb4d508e4da36d0b31", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-LlbMlzmZNF7HZw9nSabRgqgy64wwL9VYrynsCiCY1X7bI8go2R/cUxaDDLQGgAJ1bTwu6kM0h1obVO+LOGfqKg==", "signatures": [{"sig": "MEUCID+N1kbjiXB636FEYjxVPBcppPiDa6rCCFpcg8BOwR1zAiEAxHtsN9rtQAGLzwnQNZ+gsJ9iwzkFHNjxZ8qc8JVHr7E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31445, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDZqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoKThAApFC25BlgKxHGWJy/ScIEEl//SWZh4GSsnN9um+WimgSLgkEy\r\ncK1BRoQIRBEJXe6+3x1rPF8WP2AW/YQ2oqIlaO2gdzshlmwrouJFSrkAk71k\r\nV9IM8qEDSwmRpYAQG22Yi9PLLyQevP2slm1axIznt11ngLXoDzXdoV32KPJV\r\nZTafE+YEpU5BhTAAAHd93rYDOeMBcMPyRjqFd71hldSxwgKFNddM6Bv/pCNM\r\nRO2qXlKRnry5/kVRo9d9cx3KsOEsNSB4g+I37MV8Y+yyZtC1m3v85fC1TxAe\r\nNRt7OmyglsAWfNoIP8OYhWv3iGyEQfJtPnw1V0BzfXvAIZ34Qtg9DiXXGXbd\r\n2Xh0VEjbn6dG13RvDMdU+kU4yxr2d6PPFEqFhGbx24IV6IeXOcNIcsGpl2yl\r\nhbGAW7b0PVcKTsaMbm8yPoqVwIQKcC1eBH/lwGZxEr3uWAz5EITkVnaDfiC7\r\n1NtIdXJAORr6pRn33wMjdV6x3Cgmgnf90ByJ8gi5tZWzAG3bCbM9PvItH0j8\r\nLqjHdBAuaQty/FJBnsytPGB4CgpHA2F0yVCT4s2yO86wJTA5AmuupBa3jBgb\r\nhzTLqqyMnc0ZBKLL2GmAzzCZfPioh9n8QrjbibELty39iqM4SUBn1kkzeE99\r\ne+AXsr3KaemmiHtEKi9KL7umXdcxfAC+3u0=\r\n=UunF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_7.21.4-esm.2_1680619114629_0.08082890561026979", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/helper-string-parser", "version": "7.21.4-esm.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "2a2010e2fd4a706ecbec54639eed633eb0320d6a", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-snGP+FstdjfCvQNxaorIeSET7lTNnR0aitl251H3c+8xDZJvhUtDJ5wZtxCJw12UBZo1/TUZOObq/p34YQgCug==", "signatures": [{"sig": "MEQCIGf1lollaaNj10qleN6KlpdfmqpzFoahnR6Ntm0h+wF+AiA9E7Ftg0gpL1lehiFmTt7Uf9ssEQKNRjzNOXEPB+w3pg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31612, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDpzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPaQ/+JMRXLeFMVAqBUA0sRGic3fF2SO4QH+Q/nQP0upWRliMynKUT\r\nRrW1ndU2GO8Y7W0iyqAZdhGCWjC1JA4F1MBMhvHX4vrQfNS7MiExpY+lYAIO\r\nJ9Sy1NRDuBnJs40uYLEKiEjkh1AZxu3DUAz5we/7fyUEW9sQToGei8RgMiRc\r\neJN6B3kffjOPPMx2llThguFzuUY12xfg1SGDGzMSXYZ8hlncFgZblX6/Qe9R\r\nmnckSqG6Ht2gVFoEWGAqeoUdG2HcV7bzcPIhR7gNr84OudAP5QPIdgtVh7LW\r\nv0eD8ePABeNtvWsqjtALOEtP8mSsQuhVJqKSzKDMTPqUCLS20nIOjv2VDdMq\r\n3V54qRU0nNVVhpoiZbYLH/XGpX6QkeAVjB1YODpLUt6kXTe8NfgrXogWGp1Q\r\ncA8RfIH1VYKNzCARrAHR6OdtT7vIUEXeNmqyrHcLDkVaohAnvatOUEqlVzdd\r\nAUkKZ84uftdPHRJnWuMKEvYV0JCChvdUYcx12qw8DX/DPaKGEsRBJXvh9iVd\r\nlRifkSFkkiHK2gIui0356f8skEX7giAxf0Ddmcwx6Ef1AldPu6fgUgMtq1rr\r\nbKv4Afz4UpeYsF6b9auZzPnxP5VXpQiAAob4dsYQL83DyVyITPI7Ich/K5Wy\r\n1/nOwpXyLq0VbJrhf9Wh2OZiiFjwpbdy3Go=\r\n=wvnm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_7.21.4-esm.3_1680620146930_0.7078159548981517", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/helper-string-parser", "version": "7.21.4-esm.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "71c3acbd8d4e58be51f5cce62550a330dfeb2b2f", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-yqJZYyP/oxiZAuHqHKIKUVyIfEbGTg2BDBKqafuvQGX8d04qGHJ7Sc7ZchKfJhziXCIq4gQPe+WoOb98zIALmw==", "signatures": [{"sig": "MEQCIDvcwQmUG7DMwce+AoGpzR5yzXFzvyV2WE+vFk9axjaUAiA/j2kqKlImg7/z+5+pPfq32IkYwgR1PjOEGb2zeCiGAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD59ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoiZg//e1KxL8BVJ/0VEDTYE2xzYKc8HwWzxeNGixZ6DSDGpopHhMaN\r\nMD1VdvnWHQUYOrWgvlryM9I+crqxzwbKtVFGW8wpFKXgMB036E6/vrDTbDbK\r\nEo0Xfo4aIeOLD7rO48W/QzCz+ZItJNeLZXcz0XKq7+lkn23lMVBSc/q2Ni/C\r\naZ+i3vwCd/M9FNL3Wc2wlZmUmj3JL3gajNw6pCQJVejOIRmW6Vd5UErD1QMA\r\nUFYHKF13RL8d38a2qREDyWUJG/BNp/Y1S0zEzDnYgAmhftb8gRvE3b8rTgRm\r\naRBdaq9olRACrtdtteyp9bPnPeCMWftHFqyrZok+jT1q2Z3ebn9FhmCjifWy\r\nluJnzT1mEd22XUjlzLALlLmQ/34haKse0LKlkP/VA081N/1BfTxgzdtEF8hI\r\nb+HS221ace+vGb6T3Q0TarZ2d+mbDVbgJ3b4P41EtB4L3FEXpOo9GLfJOIeR\r\nB07D+F+qfGNS9Sr+d0GqlsqsgCG0tIoUZMJUwj/tYzBO2zFhk9uGoIsyjLTu\r\n38JeNg9HCemxZos24gSsmxHR3TA6vGbqAQ+hJfhiHtvkir2yCoW3rJ+DlG78\r\nVCyLbYaN/c0mpU/6Hu/TXAh3oIVvHFMFKJf0WBCpxya/5LfooCDVMqL6cUkA\r\nzapbhJ0auVJG0vCxYy6OG33MhNLDkdNnLNo=\r\n=Mb4R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_7.21.4-esm.4_1680621181427_0.3069852712641239", "host": "s3://npm-registry-packages"}}, "7.21.5": {"name": "@babel/helper-string-parser", "version": "7.21.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@7.21.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "2b3eea65443c6bdc31c22d037c65f6d323b6b2bd", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.21.5.tgz", "fileCount": 5, "integrity": "sha512-5pTUx3hAJaZIdW99sJ6ZUUgWq/Y+Hja7TowEnLNMm1VivRgZQL3vpBY3qUACVsvw+yQU6+YgfBVmcbLaZtrA1w==", "signatures": [{"sig": "MEQCIAdvhGiWt+UitBGXV9BxBe8A4ufjAuXhycFUN79OqORKAiB1q5qH1YaFxrq2dMSor6+Q3aJ6gLIIFZU+x8Ax/pg3jw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTCN3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptDhAAiKeOzKBvGkFT+p11cJhxFsafH1dOMX+9dXz1FP5nvaTDtEn0\r\n1BIbUlGIUzylrT0Ujdl0ijs08Qhl14ZYNBdrnH/YLssvqHBG5RaaZoIojwv5\r\nVcdg0uoYUeF3Wxx4ABcthA0/LrBWsSpohuPoFSNVHsdDlQlvqyYfQuiihIPS\r\n47T1D3H3nZwLR/6ukwyvB3Z97J3yx8wN1NbyANKwRa7oy3AdHntA/jCIbp+X\r\np1GXAUYYtXfAitmnEJp6/o+a/+djd/89oJq55Drjl9nq8HDNgfkOoi52U6vR\r\ndkX0yE0sT5CVAcSZfDBaKN4Nl3UhPbSrajWQIwUjV1ot24nk1nEwkEVJ/HNF\r\nNSXs0eh4YtQb+g86BUht3WGRcGIqty323Gg4nJrCf/hG9DwyGTOSgYT6bXnw\r\nEJyfAAj7UKT7HfxMxuDmxCF098IN77k2twJfP8O2FpGSt7H7VGrOTPJpNdCn\r\nD1AxzyclCZhiXP0/deA9XljIYl304XJ+C9SOV4dMjSgFD0PDW0qnTLOjg7GJ\r\nT6txOcscGlUrpfPltosq5TzHNEc0nCl/7w8hek1NXIFSCEqmPmJFwMQ117Ll\r\nS2+LiofxGGhrq2laiB/hG4t94aLqrlmejcK8YffGT4/t7RmZE5E8Il1jzUxb\r\nKcQrydpm+2dR3lX9QADAeN6RWyyqHCWrLpA=\r\n=oZn8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_7.21.5_1682711415508_0.7431285826705882", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/helper-string-parser", "version": "7.22.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "533f36457a25814cf1df6488523ad547d784a99f", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-mM4COjgZox8U+JcXQwPijIZLElkgEpO5rsERVDJTc2qfCDfERyob6k5WegS14SX18IIjv+XD+GrqNumY5JRCDw==", "signatures": [{"sig": "MEYCIQCKMD82hPHMcJWfjkiZJeiqJLjmdTa3qpXKoMOZRjW7fgIhAMZgS4m9VEEVO/qDThn5ieOd2UziWZmNT5vod4yc57nz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31634}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_7.22.5_1686248461711_0.07696094748124493", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/helper-string-parser", "version": "8.0.0-alpha.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "9e66db8bad6dae3584e5481407827e685d122edf", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-OQpq8gtpSMnTZttiJ/wzL6nJOIRjVId69UGT+SThSkzMqF4eXXrIVXyqhtgOFy1BUH0KQ6wg4gufeX8dFZH/Wg==", "signatures": [{"sig": "MEUCIA20DwBO01bGjtqaVL6/PxFGu/MmACn/qd/JRRFe8iJhAiEAi7Z7OXUwEtDFLaT/kFZAbe+aUkopdH99ystSs8copMU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33149}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_8.0.0-alpha.0_1689861573747_0.39956783556563824", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/helper-string-parser", "version": "8.0.0-alpha.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "dbe7830bb8e718bd48e9d24fb60b1ec1a8f3eeee", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-gSRHw4k60JZdndZY2zQyXdPLujyUjBCq13DR3InVBfJefkh4E5xUODAwLsmYm8W6Ub0Gyx+FGBL3B0F97QVlPQ==", "signatures": [{"sig": "MEQCIGuGzTkRnAQ035Cxx19dse6K+y9gNZl66v/FG0dkvzNeAiAuFfTNua8l1MIBswDQ7pt7wSB61XdOW+mO+8CK6SA0EQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33149}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_8.0.0-alpha.1_1690221055201_0.03888512190734694", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/helper-string-parser", "version": "8.0.0-alpha.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "eba62765ec6e7fb0adfb5f135b908e1139e06da4", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-zdLAD8CTSuAARYmEwYzeQe42vhPXbzONxDjoR+MKPQ1wLCUJpd9HQC+VJb0wbeDdVZKThZXtyYgO+SPCEXtxtA==", "signatures": [{"sig": "MEUCIQDLCTyF6jAamJ/RDtF+ttEDGKdZIsRKzYZB7cwLFyfU6QIgSVLsUaXtmXjEjlnfjqlfvneXiawpEhWqQ8V/R+mXyOo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33149}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_8.0.0-alpha.2_1691594070298_0.23930701364739893", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/helper-string-parser", "version": "8.0.0-alpha.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "276fed5819f7d337879df28ab885e76b30608c6b", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-D2UzlO8k8xgxm9/ryNdM+SB4Cz5DLDLsqt1eSg3xFGutw2L1VfCCSlA2X4cJ0tqd+DEQurQLx9rfbNrULX7B/Q==", "signatures": [{"sig": "MEQCIBn4IeRwRNk1YL4P8qKpSstzWb67jp+1CXxMkOseGjNtAiAVltEUzcdKx1JBNQTTybGST7UrIIandwoOAUoaSGwcvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33149}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_8.0.0-alpha.3_1695740184357_0.9805347525566515", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/helper-string-parser", "version": "8.0.0-alpha.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "159e4136802e7448f53546dcb724564305ddb7bc", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-5NoxqwNq2BAj/aAtqOQA3qOUgCjn4IIyhh9OuH/davLO9Rj2JRfUYzIqJVz24NNgRfuClWF5+3dAixQKCeo1Ng==", "signatures": [{"sig": "MEYCIQCU9CHjNaqnkcGnnQ96ADyhwbH1fFVBb8/gQewcyB5ZDwIhAPVz6vIOg7Q6a7y/1nN90G+OBwNaMOKcjSFJUF7ILnkr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33149}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_8.0.0-alpha.4_1697076352468_0.5845820343086949", "host": "s3://npm-registry-packages"}}, "7.23.4": {"name": "@babel/helper-string-parser", "version": "7.23.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@7.23.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "9478c707febcbbe1ddb38a3d91a2e054ae622d83", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.23.4.tgz", "fileCount": 5, "integrity": "sha512-803gmbQdqwdf4olxrX4AJyFBV/RTr3rSmOj0rKwesmzlfhYNDEs+/iOcznzpNWlJlIlTJC2QfPFcHB6DlzdVLQ==", "signatures": [{"sig": "MEUCIQCz3kVVRCLyYrLkZebQQrv59vwfR0/Vhq8efllyg4vnhwIgQRCzdNNRvoCuFYf6HCpYH3fdH9G1iNng4rD9q/5IQX0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31643}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_7.23.4_1700490124194_0.8964375809717471", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/helper-string-parser", "version": "8.0.0-alpha.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "103cb4e5f27336d2836f685117cdb58c0db43bf4", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-bXAcJ+6jTn/GpiSt7PLr+h5W8mq85xSqJ3sGe0vEws/Jljn+oQH1x56e/6W5QY0f3deXetKZcgAABGgoh7ZOVA==", "signatures": [{"sig": "MEQCIAk6/oXvQr96D20QPoPWCEhH0TnOgUM922HDtGxK72fVAiAdsLgjdz19YzfCLitJYxyEDRot3xU5K6kyfYMLf4T9FQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33161}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_8.0.0-alpha.5_1702307895174_0.17655165475629597", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/helper-string-parser", "version": "8.0.0-alpha.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "92f09a33448c8ee7e5636f8728a4db68b0149d07", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-v5PJQdsB76ivdtM+1iwKjKiWyyq62VGB7UoiG/1mE7XO2nxO9+RxRdRmaUkZ/BnoGR530fYrJ8HcKGaBQ6qkQA==", "signatures": [{"sig": "MEYCIQCRrMdSe71jUfsT0ToYKWwyzoyireA7iItUJl4/yiU2ogIhANDBJJDUvvFp9gQIHLXM4A9VhuIO6d4rVDKN1M9AwJU0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33161}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_8.0.0-alpha.6_1706285620785_0.44669853203978205", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/helper-string-parser", "version": "8.0.0-alpha.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "632b1a8862172789e00048190e5687fb1a6d3c86", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-y+o8He5q96u47oOD5J5qGl+7y90w64SDY6QD7QWXXSqjH50sGc7t3dvjbKYLy+/OGIOYaFlPT/tzGzrX3+lNBw==", "signatures": [{"sig": "MEQCIAlPyiSFt8gJT/anc4C/OZWkE0Il5gVTDJb15tjnaq+pAiA+uK1dEXfLdqQTkKz9HvotC/l8ZSEZQkYGnuRHDCRhWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33161}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_8.0.0-alpha.7_1709129045807_0.2511004373024355", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/helper-string-parser", "version": "7.24.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "f99c36d3593db9540705d0739a1f10b5e20c696e", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-2ofRCjnnA9y+wk8b9IAREroeUP02KHp431N2mhKniy2yKIDKpbrHv9eXwm8cBeWQYcJmzv5qKCu65P47eCF7CQ==", "signatures": [{"sig": "MEYCIQDKCsGazN5dVt4cETdIlfN7To8uGRJ7pFDzhLMo1iGmowIhAK/GyrHNZgGP/0v0fEk+ZBX9BK2CnHg3yxzoZ+iy+g2r", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31658}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_7.24.1_1710841656548_0.6823253757980454", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/helper-string-parser", "version": "8.0.0-alpha.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "6fd5976b2544f1ac7965a6a1eeab0b18dab0c8a0", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-xx4gZbGiSaAHSDbXPxhyO3Gw6duq5hvmqHXCO9/xUksIoiZTOnqJg4bIHuMLOuZVRC9o/LP7+6MQsEZbx99m6g==", "signatures": [{"sig": "MEYCIQD+2lgLEirlj0fJ7WZzfwd+OIjx7UYsWB9BwZGUiPQvZQIhANklAi7Wsdo9ob72j67xkDMdzX5hzzVogS9qhdqLqjSj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33160}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_8.0.0-alpha.8_1712236763743_0.5057664809183964", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/helper-string-parser", "version": "7.24.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "28583c28b15f2a3339cfafafeaad42f9a0e828df", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.24.6.tgz", "fileCount": 5, "integrity": "sha512-WdJjwMEkmBicq5T9fm/cHND3+UlFa2Yj8ALLgmoSQAJZysYbBjw+azChSGPN4DSPLXOcooGRvDwZWMcF/mLO2Q==", "signatures": [{"sig": "MEUCIQCmnW31m61XvSv18ope8KPBrjQG5OUH2kyzUWdonbe2zQIgSL250IizoLrrnXr7erUntImrVgV2dKVyHFCRm2Jcids=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31718}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_7.24.6_1716553449244_0.19547238669354772", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/helper-string-parser", "version": "8.0.0-alpha.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "4a550719b61454904b650efcb147b3b1cb76054d", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-8.0.0-alpha.9.tgz", "fileCount": 6, "integrity": "sha512-9tJl5Ymih/LgagTdQz051x70JDOPpgLY+qmvTMb7bD4XuYEvwPICeoBm29iW1VpOLnQHfQWKQkCQNDwYT3qclA==", "signatures": [{"sig": "MEUCIEtwBJZ+beWfulmBE4qHuDnX1204Ye79EHI0KpUDhljgAiEAu2qco1xvLif4lM4B4hrag7gj2MGhuJAKCYPo3EF0EsM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35425}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_8.0.0-alpha.9_1717423425311_0.11785285608248453", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/helper-string-parser", "version": "8.0.0-alpha.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "1ed110ec29f93970a3829447dc025f30f8bb9623", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-8.0.0-alpha.10.tgz", "fileCount": 6, "integrity": "sha512-+lP24Obmep+JHE+s4EPWxshr8Y6dQ9ZWNb/CDAZ6kkLIXv5i8sT7jZ1RFlJSn6OWFL6U82bIErUaMZKSl7SyWA==", "signatures": [{"sig": "MEYCIQCuKPiZpciS3Tw9YbJZd/nhVSErAxKSQ0kmlxlSN8OjJgIhAMOV0UZ3D5rqgc8gxA2VAPLHNShBKeTDOx1L18tJFvHS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35426}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_8.0.0-alpha.10_1717499979030_0.8733222648876693", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/helper-string-parser", "version": "7.24.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "4d2d0f14820ede3b9807ea5fc36dfc8cd7da07f2", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.24.7.tgz", "fileCount": 5, "integrity": "sha512-7MbVt6xrwFQbunH2DNQsAP5sTGxfqQtErvBIvIMi6EQnbgUOuVYanvREcmFrOPhoXBrTtjhhP+lW+o5UfK+tDg==", "signatures": [{"sig": "MEQCIFp8ty3o8TC4Mc2mO7mXK0yBjDtwe4OVEQDSRxyU0BebAiBYjFWj3cvxgH4PxdDKpWbb73v1R9mFQsT41Hs4xnj5vw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31808}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_7.24.7_1717593293881_0.15311361468560158", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/helper-string-parser", "version": "8.0.0-alpha.11", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "411b082b1ea4ae55b7d925d700d981c2dc8b3a67", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-8.0.0-alpha.11.tgz", "fileCount": 6, "integrity": "sha512-vtSqK4kNEUkFpCiU5mToaM5+OoT8Ur0tqtr7lax74YKe0wKssQXKlzPyesdo5p81UZQD8IWLv0pHBZflvpojAw==", "signatures": [{"sig": "MEUCIQDx4kWGpoyynObpwrBmSNFEaCDSzejSs345+U5vdChgNAIgFP/cZkv/ViDVPQCb5xs5zm6hkYb4NVAjGAm7X7R0pTw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35426}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_8.0.0-alpha.11_1717751710438_0.9731656774441191", "host": "s3://npm-registry-packages"}}, "7.24.8": {"name": "@babel/helper-string-parser", "version": "7.24.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@7.24.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "5b3329c9a58803d5df425e5785865881a81ca48d", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.24.8.tgz", "fileCount": 5, "integrity": "sha512-pO9KhhRcuUyGnJWwyEgnRJTSIZHiT+vMD0kPeD+so0l7mxkMT19g3pjY9GTnHySck/hDzq+dtW/4VgnMkippsQ==", "signatures": [{"sig": "MEUCIAx0ljzRRXJEfSRxNSeDHteqlN8w42ZcI78uu/UVHlQUAiEA3UE39DeotTS9uwe9hm8KlmkgV/a7BE7JAt/cgzyap88=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31816}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_7.24.8_1720709683190_0.39330265695791833", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/helper-string-parser", "version": "8.0.0-alpha.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "3c26b703a8e5b5ee5d3ec329e6c0d74bde3a6a8e", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-8.0.0-alpha.12.tgz", "fileCount": 6, "integrity": "sha512-GIYFzEsWh7lNJYfxM/3ASK0uAp9G2a3IVI/u8XE2NroT3SoMOJOLj+Ve67uHHI81UTZEzU2k9778tBR/agPXkA==", "signatures": [{"sig": "MEQCIGEFfLLkBHrHxr5dU+igJyazXDXFnXoKSLrrjO0bEiXoAiBwnQFo7sxlrrt7v45ztscZFjIx+fLbosoBHfNNI5AgIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35433}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_8.0.0-alpha.12_1722015185469_0.4512075487185039", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/helper-string-parser", "version": "7.25.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "d50e8d37b1176207b4fe9acedec386c565a44a54", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-CbkjYdsJNHFk8uqpEkpCvRs3YRp9tY6FmFY7wLMSYuGYkrdUi7r2lc4/wqsvlHoMznX3WJ9IP8giGPq68T/Y6g==", "signatures": [{"sig": "MEUCIQDYT2LRNgVyEhEfSzFxSoVEsjKtiSuZ/VP3ZxTKwDtRMgIgB8JgsLz2XQEZ6kD0C9UWrbxqKFYBF5m+tRKt/2Ru7P8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74421}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_7.25.7_1727882062458_0.6487874981394777", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/helper-string-parser", "version": "7.25.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "1aabb72ee72ed35789b4bbcad3ca2862ce614e8c", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==", "signatures": [{"sig": "MEUCIQC6Vxa4QGsVLAooKKQSXwE5E00ROGrk5PMOSmAiFTA0XgIgS1/qI+hI6IY8An1iWy+F3QAEMYjvlD8kFDv5E+B3IzI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31816}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_7.25.9_1729610436094_0.10802570480826423", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/helper-string-parser", "version": "8.0.0-alpha.13", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "69edf50ba78de8738efdf8dd06e34daf70ddfdab", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-YHapkEwMu3nqoikRQhUqPZXUjqRepotzlYww49FWObNaU+9CKYtOJkJBwfY9RmcFDsFqGeLNk+IUSWRPgwhujA==", "signatures": [{"sig": "MEYCIQDTOOX08K0hxz/8I1cLf7YYCOp2/3zAndff70l2FHOAQwIhAKGyiU/jV1bpBEN4u8EOZRaUqnZiBmavWwMkN5W5MU++", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35433}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_8.0.0-alpha.13_1729864422562_0.0477688950600319", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/helper-string-parser", "version": "8.0.0-alpha.14", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "89fe4b09619eccea980d99c2d1dd6ad907631bb2", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-QX4FzSOsvMEEUnaYwV4Y89tWmaJQoqKD9xy6immGeGePb7xO7xPJyXopbh/z3/E8bFiXkptV3ojihcGvrE0VPA==", "signatures": [{"sig": "MEUCIQDBNFHhY5gJ6EdRW+cmtGaGt98zknSBcHqShIsekdRIwgIgca36UkanM97cLAdggjTWoeqx/Zse+0hogygYagBGgjI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35433}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_8.0.0-alpha.14_1733504014308_0.5400386669047115", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/helper-string-parser", "version": "8.0.0-alpha.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "7c4887d3db175c2dbf46265f3301eabf92c77fa0", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-ALNr91ssV6G+KlLCRSsSQeaxX6XvT5orWOLyIpMW+LB/+2suo54dvuXwwHWlZjCDOg05yFrJb47rTNzEsY4gTg==", "signatures": [{"sig": "MEUCIQDYYoNJJccgVN4OrXQHu6aOLe63fM9sePW5cQhu9qibQAIgahCwd8k6FhG6wFNMxJfTZhoT/ZivK6DexIlFa22nLUg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35433}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_8.0.0-alpha.15_1736529838055_0.614186305452268", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/helper-string-parser", "version": "8.0.0-alpha.16", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "65a37317ba75493f387eebeb0e99ca6ce91e68d7", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-XG5j07huijEyqkTvO603g4AVAbhxrnKGVaIpOP1TOgACE6F6Ox9y5NzUlPawAf0mlM0UksOe8OVf6dyFrl9TTw==", "signatures": [{"sig": "MEUCIQCm9TbJB634AZsh1qE9Cwr2dBiB+mdWXBuo5lA7/GPPNwIgGAt+6cGtionGY0sBEuZ9jzA80oMyRCikuyug06EBN3U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 35433}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_8.0.0-alpha.16_1739534316196_0.5490463824444056", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/helper-string-parser", "version": "8.0.0-alpha.17", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "f062609c1686c0c501b837caabf847d38306739f", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-0EDJ8CxGs/lMKkfu1SVouk2jD7cO2258TfwDt+PLEG+BH+y2334TB/gP1y89QHc3SYO4osnGsd33hgkyHQ9YUw==", "signatures": [{"sig": "MEQCIG2Rxuzcf+LCycZ+pz2pD4Ic9yP0WVVB3uyiV2O82PvOAiAw3C574h9t4OTZPh8111+mLvEQG4DmklTm9TE6l5QXRw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 35433}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_8.0.0-alpha.17_1741717466341_0.4876507513617492", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/helper-string-parser", "version": "7.27.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "54da796097ab19ce67ed9f88b47bb2ec49367687", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==", "signatures": [{"sig": "MEUCIH77/ejFzx7mcaJmf3M0Y3HJFEe6/W6/zBBM/qq3pG2yAiEA6vTRV6PztYoPvYUFt/h0vPnbA0/QqrEFEpPB915OxGM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31816}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_7.27.1_1746025706305_0.3426444705888576", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/helper-string-parser", "version": "8.0.0-beta.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-string-parser@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "dist": {"shasum": "d74eef7dfecab578c24c15f4458be458c50c0cf4", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-JQSmsLB9YavJZiDNYJIPwCVMdmQMLiJiVTiRFD9izmL1J+Dz4mld5ztikogjPN6SeQ9BvGTRA9K3CExzigZddA==", "signatures": [{"sig": "MEQCIDA3iPOTgW21YI9a1iJO9teWIdHKJ4Z3wwWo2AMzze2/AiB2G2O1ZeZNDi/KM1KLnhkTJvI0TuzNo+SJzv5+360qKg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 35420}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/helper-string-parser_8.0.0-beta.0_1748620238945_0.7910076181510206", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/helper-string-parser", "version": "8.0.0-beta.1", "description": "A utility package to parse strings", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-string-parser"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "devDependencies": {"charcodes": "^0.2.0"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/helper-string-parser@8.0.0-beta.1", "dist": {"shasum": "e63ac0e4171e6c62f86e320ec87b89a7b639dc97", "integrity": "sha512-aJynfGjC06POr7fapv2zl8AI3ELDW0g2mMMwKu/ray9C7z4kd/Ioe8ApbNq/KrSx5iVxKRO+qqkZcJuq3QZLZQ==", "tarball": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 35420, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIHahyeorEOP+CJ5a88wpFpMz0YE3AOKae949CzQcAUzHAiEAw1SbVgMXvjkyr9FvlHM2kqdXh3vI/WtTUO4GjR1DBRo="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/helper-string-parser_8.0.0-beta.1_1751447034233_0.49128271729955797"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-08-01T18:46:39.945Z", "modified": "2025-07-02T09:03:54.659Z", "7.18.10": "2022-08-01T18:46:40.216Z", "7.19.4": "2022-10-10T10:47:19.355Z", "7.21.4-esm": "2023-04-04T14:09:00.713Z", "7.21.4-esm.1": "2023-04-04T14:20:52.561Z", "7.21.4-esm.2": "2023-04-04T14:38:34.794Z", "7.21.4-esm.3": "2023-04-04T14:55:47.089Z", "7.21.4-esm.4": "2023-04-04T15:13:01.621Z", "7.21.5": "2023-04-28T19:50:15.669Z", "7.22.5": "2023-06-08T18:21:01.985Z", "8.0.0-alpha.0": "2023-07-20T13:59:33.973Z", "8.0.0-alpha.1": "2023-07-24T17:50:55.415Z", "8.0.0-alpha.2": "2023-08-09T15:14:30.532Z", "8.0.0-alpha.3": "2023-09-26T14:56:24.750Z", "8.0.0-alpha.4": "2023-10-12T02:05:52.705Z", "7.23.4": "2023-11-20T14:22:04.431Z", "8.0.0-alpha.5": "2023-12-11T15:18:15.394Z", "8.0.0-alpha.6": "2024-01-26T16:13:40.980Z", "8.0.0-alpha.7": "2024-02-28T14:04:06.035Z", "7.24.1": "2024-03-19T09:47:36.696Z", "8.0.0-alpha.8": "2024-04-04T13:19:23.902Z", "7.24.6": "2024-05-24T12:24:09.434Z", "8.0.0-alpha.9": "2024-06-03T14:03:45.460Z", "8.0.0-alpha.10": "2024-06-04T11:19:39.279Z", "7.24.7": "2024-06-05T13:14:54.052Z", "8.0.0-alpha.11": "2024-06-07T09:15:10.582Z", "7.24.8": "2024-07-11T14:54:43.345Z", "8.0.0-alpha.12": "2024-07-26T17:33:05.637Z", "7.25.7": "2024-10-02T15:14:22.678Z", "7.25.9": "2024-10-22T15:20:36.317Z", "8.0.0-alpha.13": "2024-10-25T13:53:42.803Z", "8.0.0-alpha.14": "2024-12-06T16:53:34.508Z", "8.0.0-alpha.15": "2025-01-10T17:23:58.270Z", "8.0.0-alpha.16": "2025-02-14T11:58:36.399Z", "8.0.0-alpha.17": "2025-03-11T18:24:26.542Z", "7.27.1": "2025-04-30T15:08:26.501Z", "8.0.0-beta.0": "2025-05-30T15:50:39.117Z", "8.0.0-beta.1": "2025-07-02T09:03:54.395Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-helper-string-parser", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-string-parser"}, "description": "A utility package to parse strings", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"flumpus-dev": true}}