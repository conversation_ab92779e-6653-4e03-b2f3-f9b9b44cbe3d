{"_id": "test-exclude", "_rev": "59-ee1ba1d080afd2c65458975b4ee2f49c", "name": "test-exclude", "description": "test for inclusion or exclusion of paths using globs", "dist-tags": {"latest": "7.0.1"}, "versions": {"1.0.0": {"name": "test-exclude", "version": "1.0.0", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@1.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/test-exclude#readme", "bugs": {"url": "https://github.com/bcoe/test-exclude/issues"}, "dist": {"shasum": "ced63b8463bf5a8df501d6f5ab9baaf9a7a2dd50", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-1.0.0.tgz", "integrity": "sha512-IXeXyyF+98OiWQtiBTrbXXxl7X+ASPDJv3XX4e62xzJYBgdY9ltfLdPmmhylacs8AF3ry5FCdURoGtuiutGdyA==", "signatures": [{"sig": "MEUCIQCVEu3wSdhhjNfij/2G2tRwgfv3kJrObIceKEE7yY4GzwIgEgq0TT4IqhI4enmSevCEBLS7i4jZ9CdnrEU486xNA/4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "ced63b8463bf5a8df501d6f5ab9baaf9a7a2dd50", "gitHead": "2a559630140a1ceb93487c29f6808d8cf4a1da3a", "scripts": {"test": "nyc mocha ./test/*.js", "pretest": "standard", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/bcoe/test-exclude.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"arrify": "^1.0.1", "micromatch": "^2.3.8", "read-pkg-up": "^1.0.1", "lodash.assign": "^4.0.9", "require-main-filename": "^1.0.1"}, "devDependencies": {"nyc": "^6.4.4", "chai": "^3.5.0", "mocha": "^2.5.3", "standard": "^7.1.2", "coveralls": "^2.11.9", "standard-version": "^2.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude-1.0.0.tgz_1465192578006_0.9678394263610244", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.0": {"name": "test-exclude", "version": "1.1.0", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@1.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/bcoe/test-exclude#readme", "bugs": {"url": "https://github.com/bcoe/test-exclude/issues"}, "dist": {"shasum": "f5ddd718927b12fd02f270a0aa939ceb6eea4151", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-1.1.0.tgz", "integrity": "sha512-09ypgpt5xAWZ9pjLlv3/pswwGGEa+W+b/c8pfP5pqQVoAQA8n/+XDf8vDZDaUweAtVAWUnWQn+sGyqoU4Bo0AA==", "signatures": [{"sig": "MEUCIDgvvJQ/+Z2f+xXVic1wX2dsHTydzeH29TJDGItIKj4xAiEA+yD5y8d6Auq3TJUka0wZ6c+7mg/bbjIEBp+Be2G0GxU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f5ddd718927b12fd02f270a0aa939ceb6eea4151", "gitHead": "d7406b0d00c7a651065e0be51ca83971e8282dfa", "scripts": {"test": "nyc mocha ./test/*.js", "pretest": "standard", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/bcoe/test-exclude.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"arrify": "^1.0.1", "micromatch": "^2.3.8", "read-pkg-up": "^1.0.1", "lodash.assign": "^4.0.9", "require-main-filename": "^1.0.1"}, "devDependencies": {"nyc": "^6.4.4", "chai": "^3.5.0", "mocha": "^2.5.3", "standard": "^7.1.2", "coveralls": "^2.11.9", "standard-version": "^2.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude-1.1.0.tgz_1465366832939_0.557329352479428", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.0": {"name": "test-exclude", "version": "2.0.0", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@2.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/test-exclude#readme", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "nyc": {"lines": 100, "branches": 96, "reporter": ["lcov", "text"], "functions": 100, "statements": 100, "check-coverage": true}, "dist": {"shasum": "bacd36d3d37663ece201eb1fac115e45c79c782a", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-2.0.0.tgz", "integrity": "sha512-uUjeL0dykJxIBT6G+o29TWxJosg864d78Peg0wq8elbHJB3dnEkdZhwdG1QkrSsULSrb9g+Z4nhXWDOj7JrT7Q==", "signatures": [{"sig": "MEQCIGrwbTuORmBOHe+ZGSQZ/rEo1TB1I2tPl75zx5K1ueuPAiBNuidjeo4HIgl5Tah0NvcIYCcN40AQ5m3F+moOyL8JcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "bacd36d3d37663ece201eb1fac115e45c79c782a", "gitHead": "99d58797d332b81cfbab9d58df5d97db5eadb84a", "scripts": {"test": "nyc mocha ./test/*.js", "pretest": "standard", "release": "standard-version"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/test-exclude.git", "type": "git"}, "_npmVersion": "3.10.6", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"arrify": "^1.0.1", "micromatch": "^2.3.11", "read-pkg-up": "^1.0.1", "lodash.assign": "^4.1.0", "require-main-filename": "^1.0.1"}, "devDependencies": {"nyc": "^7.1.0", "chai": "^3.5.0", "mocha": "^2.5.3", "standard": "^7.1.2", "coveralls": "^2.11.12", "standard-version": "^2.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude-2.0.0.tgz_1470975551394_0.7755239922553301", "host": "packages-16-east.internal.npmjs.com"}}, "2.1.0": {"name": "test-exclude", "version": "2.1.0", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@2.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/test-exclude#readme", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "nyc": {"lines": 100, "branches": 96, "reporter": ["lcov", "text"], "functions": 100, "statements": 100, "check-coverage": true}, "dist": {"shasum": "0c7c1b8de529ceaee717e545e03f1bf248521d82", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-2.1.0.tgz", "integrity": "sha512-KEQf8HME55QUrA7Z2yzkYDRduGaYxIzd8M8O5EknTw/0OA+yPxUfFbjn1ZMOkf/oigAwFAuYmR45mZqBQZBF5Q==", "signatures": [{"sig": "MEUCIQDmJczc0mtzXyv0NPDbexvtFUD3mANsSWwi3xU+m9ww/AIgNrh2u9242fdcoPoO2/36+NTs95x2HC9clm+JQjqbwhg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0c7c1b8de529ceaee717e545e03f1bf248521d82", "gitHead": "cce8826fe7e443ae441804f1c8e6760138a8f14e", "scripts": {"test": "nyc mocha ./test/*.js", "pretest": "standard", "release": "standard-version"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/test-exclude.git", "type": "git"}, "_npmVersion": "3.10.6", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"arrify": "^1.0.1", "micromatch": "^2.3.11", "read-pkg-up": "^1.0.1", "lodash.assign": "^4.1.0", "require-main-filename": "^1.0.1"}, "devDependencies": {"nyc": "^7.1.0", "chai": "^3.5.0", "mocha": "^2.5.3", "standard": "^7.1.2", "coveralls": "^2.11.12", "standard-version": "^2.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude-2.1.0.tgz_1470976679249_0.14984446042217314", "host": "packages-12-west.internal.npmjs.com"}}, "2.1.1": {"name": "test-exclude", "version": "2.1.1", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@2.1.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/test-exclude#readme", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "nyc": {"lines": 100, "branches": 96, "reporter": ["lcov", "text"], "functions": 100, "statements": 100, "check-coverage": true}, "dist": {"shasum": "4f5b7199a0e7da28212a3204f1e7b588278268b7", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-2.1.1.tgz", "integrity": "sha512-Lz53KFIoJMqBDEpn6dBgBXAiaVl1LHj1mIEbgnmB7szmIUB6ZYG7Zydc/poCd7rfK9XOEL6g2tA5DB/bY4m0TA==", "signatures": [{"sig": "MEYCIQDMP4H6SXPq3Co7aZ/GIU5de3k9hOwhYpkOeuFQV7fFzgIhANlU5P5p0euJi7jNP7541++Ot7zK14ZzygszAT0Nzlfv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "4f5b7199a0e7da28212a3204f1e7b588278268b7", "gitHead": "1b559af79cc1ac1504f9aca52cad911f95290451", "scripts": {"test": "nyc mocha ./test/*.js", "pretest": "standard", "release": "standard-version"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/test-exclude.git", "type": "git"}, "_npmVersion": "3.10.6", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"arrify": "^1.0.1", "micromatch": "^2.3.11", "read-pkg-up": "^1.0.1", "lodash.assign": "^4.1.0", "require-main-filename": "^1.0.1"}, "devDependencies": {"nyc": "^7.1.0", "chai": "^3.5.0", "mocha": "^2.5.3", "standard": "^7.1.2", "coveralls": "^2.11.12", "standard-version": "^2.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude-2.1.1.tgz_1470978128695_0.8322175100911409", "host": "packages-12-west.internal.npmjs.com"}}, "2.1.2": {"name": "test-exclude", "version": "2.1.2", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@2.1.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/test-exclude#readme", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "nyc": {"lines": 100, "branches": 96, "reporter": ["lcov", "text"], "functions": 100, "statements": 100, "check-coverage": true}, "dist": {"shasum": "186d69b8d8f01e99868ef04f7332a5a1b7c1ae73", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-2.1.2.tgz", "integrity": "sha512-3vDF2lOWdqi8kSZC9Vrh3gApjzOG2vAIC7k4L+s1t+PU220cRpDK9DTvN/dncv40zOlRIwsz3ZGjpHOwOr6oRA==", "signatures": [{"sig": "MEUCIAjxZMbGibUb8G4SJ622hAKYKKXe1UqQ+gaQMryC1syaAiEAh9J1yIwEG9wWPoW23q/q3NtPyfXoZT1h6dkeq3GF6VU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "186d69b8d8f01e99868ef04f7332a5a1b7c1ae73", "gitHead": "dae5ceb1d33e7f4555286013f197b865ae746a28", "scripts": {"test": "nyc mocha ./test/*.js", "pretest": "standard", "release": "standard-version"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/test-exclude.git", "type": "git"}, "_npmVersion": "3.10.6", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"arrify": "^1.0.1", "micromatch": "^2.3.11", "read-pkg-up": "^1.0.1", "lodash.assign": "^4.1.0", "require-main-filename": "^1.0.1"}, "devDependencies": {"nyc": "^7.1.0", "chai": "^3.5.0", "mocha": "^2.5.3", "standard": "^7.1.2", "coveralls": "^2.11.12", "standard-version": "^2.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude-2.1.2.tgz_1472660287897_0.10232024267315865", "host": "packages-16-east.internal.npmjs.com"}}, "2.1.3": {"name": "test-exclude", "version": "2.1.3", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@2.1.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/test-exclude#readme", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "nyc": {"lines": 100, "branches": 96, "reporter": ["lcov", "text"], "functions": 100, "statements": 100, "check-coverage": true}, "dist": {"shasum": "a8d8968e1da83266f9864f2852c55e220f06434a", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-2.1.3.tgz", "integrity": "sha512-KeBNu9ZFrS3fIVWQuvZ5qKoMVxiX14sEFcKYOfUhhxUZHmPSBthPcjbQJYPUTQSl0oBPF9ckmPKDjPAvctjL6g==", "signatures": [{"sig": "MEYCIQDk+lIhzM0IchQkXH1ySCQUcyjNwaY+n5Iv18WIrIcGzwIhAOFnvY80AY7xPia0qAHKu4DTaH6LSBTZLvP3JvAp3jJO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "a8d8968e1da83266f9864f2852c55e220f06434a", "gitHead": "74533000658fe836a104c1d24e7bbd46129355f2", "scripts": {"test": "nyc mocha ./test/*.js", "pretest": "standard", "release": "standard-version"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/test-exclude.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"arrify": "^1.0.1", "micromatch": "^2.3.11", "read-pkg-up": "^1.0.1", "object-assign": "^4.1.0", "require-main-filename": "^1.0.1"}, "devDependencies": {"nyc": "^7.1.0", "chai": "^3.5.0", "mocha": "^2.5.3", "standard": "^7.1.2", "coveralls": "^2.11.12", "standard-version": "^2.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude-2.1.3.tgz_1475270861719_0.9202085055876523", "host": "packages-16-east.internal.npmjs.com"}}, "3.0.0": {"name": "test-exclude", "version": "3.0.0", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@3.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/test-exclude#readme", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "nyc": {"lines": 100, "branches": 96, "reporter": ["lcov", "text"], "functions": 100, "statements": 100, "check-coverage": true}, "dist": {"shasum": "5168720da79677ea1f592dbe283d73d9d753671b", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-3.0.0.tgz", "integrity": "sha512-z8+xBXOjsw/iyKlQf3/ShoiOjXqr1BCR5t0GVLF20/0XJseiLENK8YfAqyUyjaPxslcjryOyB9+G4/coq9X2kg==", "signatures": [{"sig": "MEYCIQCTw2NcAs0FYr2ECY7HMH0q7gMUe/SbAiD7gcrsdqNfzQIhAMdEEFZH+x9sKKvXKyM3WpjzvVVZwZQa4WtCxfUkU9A9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "5168720da79677ea1f592dbe283d73d9d753671b", "gitHead": "6fd9dc1637ef2798b8123755238297ab122c743a", "scripts": {"test": "nyc mocha ./test/*.js", "pretest": "standard", "release": "standard-version"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/test-exclude.git", "type": "git"}, "_npmVersion": "4.0.0", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"arrify": "^1.0.1", "micromatch": "^2.3.11", "read-pkg-up": "^1.0.1", "object-assign": "^4.1.0", "require-main-filename": "^1.0.1"}, "devDependencies": {"nyc": "^8.4.0", "chai": "^3.5.0", "mocha": "^3.1.2", "standard": "^8.5.0", "coveralls": "^2.11.12", "standard-version": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude-3.0.0.tgz_1478996277129_0.8085760453250259", "host": "packages-18-east.internal.npmjs.com"}}, "3.1.0": {"name": "test-exclude", "version": "3.1.0", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@3.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/test-exclude#readme", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "nyc": {"lines": 100, "branches": 96, "reporter": ["lcov", "text"], "functions": 100, "statements": 100, "check-coverage": true}, "dist": {"shasum": "cc518622d89df646add69b062a494339da580d27", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-3.1.0.tgz", "integrity": "sha512-MLtgmzcjkgAIgmRRrXThynuk40zGPbI2I3dlGi+Qc0vvFjHGGThHBax+RH1gQG+zw6/gH9L79lqr497WPMj5Gw==", "signatures": [{"sig": "MEUCIQCrHnh/0Mt7IvSa7XY5cUYrqSJuODLZyCpzYFwR1BC8oAIgeMmWttlvXeqk1AzWszQxUpDOH54w0qGHcZYZ4vgAhes=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "cc518622d89df646add69b062a494339da580d27", "gitHead": "346fb19f4876692b41156c9d10cc445779b16168", "scripts": {"test": "nyc mocha ./test/*.js", "pretest": "standard", "release": "standard-version"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/test-exclude.git", "type": "git"}, "_npmVersion": "4.0.0", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"arrify": "^1.0.1", "micromatch": "^2.3.11", "read-pkg-up": "^1.0.1", "object-assign": "^4.1.0", "require-main-filename": "^1.0.1"}, "devDependencies": {"nyc": "^8.4.0", "chai": "^3.5.0", "mocha": "^3.1.2", "standard": "^8.5.0", "coveralls": "^2.11.12", "standard-version": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude-3.1.0.tgz_1479155656722_0.1710410218220204", "host": "packages-12-west.internal.npmjs.com"}}, "3.2.0": {"name": "test-exclude", "version": "3.2.0", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@3.2.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/test-exclude#readme", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "nyc": {"lines": 100, "branches": 96, "reporter": ["lcov", "text"], "functions": 100, "statements": 100, "check-coverage": true}, "dist": {"shasum": "40c4cf81e604a1869f8c2b161dd82e78e54aca58", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-3.2.0.tgz", "integrity": "sha512-Ef8xWp/xN0qEV6zs83WR4VaI9CcbYrAoZegXJheM4/J9Ww4fgP7mdDc1JavVSXin9FPTYUsH+1JeNTPNEJIUiA==", "signatures": [{"sig": "MEUCIQDZdxcqhz9yo5BcB3fHEQPvWM3gdeZLOd/C3lDiXELxdgIgO5KubKFa+j0joV5rNi5AKaXeQXygtU55vGd+2AM91mQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "40c4cf81e604a1869f8c2b161dd82e78e54aca58", "gitHead": "3d0bf786bdeb241b60b47b5cda9f743a93320698", "scripts": {"test": "nyc mocha ./test/*.js", "pretest": "standard", "release": "standard-version"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/test-exclude.git", "type": "git"}, "_npmVersion": "4.0.0", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"arrify": "^1.0.1", "micromatch": "^2.3.11", "read-pkg-up": "^1.0.1", "object-assign": "^4.1.0", "require-main-filename": "^1.0.1"}, "devDependencies": {"nyc": "^8.4.0", "chai": "^3.5.0", "mocha": "^3.1.2", "standard": "^8.5.0", "coveralls": "^2.11.12", "standard-version": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude-3.2.0.tgz_1479158287285_0.6737476517446339", "host": "packages-12-west.internal.npmjs.com"}}, "3.2.1": {"name": "test-exclude", "version": "3.2.1", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@3.2.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/test-exclude#readme", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "nyc": {"lines": 100, "branches": 96, "reporter": ["lcov", "text"], "functions": 100, "statements": 100, "check-coverage": true}, "dist": {"shasum": "498020bb62274e36cc995182757bef42ac7137ba", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-3.2.1.tgz", "integrity": "sha512-5Bid4J3d8NZHguDt+U0TEbBmZzYmv2yhzlLlKEoc+3JF6FKvOSaUcXdL9qM2IslAH3qXlXHmqjEsyTh9GNrK1A==", "signatures": [{"sig": "MEQCIFLumRGmsL4/Pd1VbNPnJzGfUxYoNvVcTxUR0HNvDluiAiBcMfz2fczJzw7ZwMWALwbLDOIrOfOuyxD/PHeHFY/ZoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "498020bb62274e36cc995182757bef42ac7137ba", "gitHead": "e5f853a0e59c41ff9f8b1392c445b4dbec07572f", "scripts": {"test": "nyc mocha ./test/*.js", "pretest": "standard", "release": "standard-version"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/test-exclude.git", "type": "git"}, "_npmVersion": "4.0.0", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"arrify": "^1.0.1", "micromatch": "^2.3.11", "read-pkg-up": "^1.0.1", "object-assign": "^4.1.0", "require-main-filename": "^1.0.1"}, "devDependencies": {"nyc": "^8.4.0", "chai": "^3.5.0", "mocha": "^3.1.2", "standard": "^8.5.0", "coveralls": "^2.11.12", "standard-version": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude-3.2.1.tgz_1479163027542_0.6193591663613915", "host": "packages-18-east.internal.npmjs.com"}}, "3.2.2": {"name": "test-exclude", "version": "3.2.2", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@3.2.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/test-exclude#readme", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "nyc": {"lines": 100, "branches": 96, "reporter": ["lcov", "text"], "functions": 100, "statements": 100, "check-coverage": true}, "dist": {"shasum": "bce1e739b668c00d71f6cd2c0ef292b8d23c22ae", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-3.2.2.tgz", "integrity": "sha512-J86WvjIcGAuWnSW9lTvS62O1PjpEoxC1z7iBsP6IRXpxADbjs4FCcDnJwknpOns1ySuhHVCGIQObMCXpPVGByA==", "signatures": [{"sig": "MEUCIAjxuL1X98LHSZw9baQ1eKWVLNH/Nz8I1WRP2ScPafL7AiEA3YPLZ5Y1WiS7N1X1WwsAai+CW1bsboOoERI5pH1AF9g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "bce1e739b668c00d71f6cd2c0ef292b8d23c22ae", "gitHead": "5205235b8ff08a08c5dd9d826e9c1fd46c6aad99", "scripts": {"test": "nyc mocha ./test/*.js", "pretest": "standard", "release": "standard-version"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/test-exclude.git", "type": "git"}, "_npmVersion": "4.0.0", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"arrify": "^1.0.1", "micromatch": "^2.3.11", "read-pkg-up": "^1.0.1", "object-assign": "^4.1.0", "require-main-filename": "^1.0.1"}, "devDependencies": {"nyc": "^8.4.0", "chai": "^3.5.0", "mocha": "^3.1.2", "standard": "^8.5.0", "coveralls": "^2.11.12", "standard-version": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude-3.2.2.tgz_1479163955732_0.539127781521529", "host": "packages-12-west.internal.npmjs.com"}}, "3.3.0": {"name": "test-exclude", "version": "3.3.0", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@3.3.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/test-exclude#readme", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "nyc": {"lines": 100, "branches": 96, "reporter": ["lcov", "text"], "functions": 100, "statements": 100, "check-coverage": true}, "dist": {"shasum": "7a17ca1239988c98367b0621456dbb7d4bc38977", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-3.3.0.tgz", "integrity": "sha512-qjn0HJC3EY0oPbV4v3n5rNSHM3uuW7xAUZjt71h3a1HphS891VSaB82xYLT7rgbcr0o3c8DIzhtNcY7Fb0Ccvw==", "signatures": [{"sig": "MEUCIA5iGUKaU1am2BO8QyQr6gOQQ626f7yoEurToVSBGLjlAiEAgbZ4ypWZVkAUYQyO77SHDPmdM8O/Gfrv2CH55xqgLKQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "7a17ca1239988c98367b0621456dbb7d4bc38977", "gitHead": "cf979c7c259fa547755fdb0fbc6b4d8c8ad7d371", "scripts": {"test": "nyc mocha ./test/*.js", "pretest": "standard", "release": "standard-version"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/test-exclude.git", "type": "git"}, "_npmVersion": "4.0.0", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"arrify": "^1.0.1", "micromatch": "^2.3.11", "read-pkg-up": "^1.0.1", "object-assign": "^4.1.0", "require-main-filename": "^1.0.1"}, "devDependencies": {"nyc": "^8.4.0", "chai": "^3.5.0", "mocha": "^3.1.2", "standard": "^8.5.0", "coveralls": "^2.11.12", "standard-version": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude-3.3.0.tgz_1479797602694_0.012697829864919186", "host": "packages-18-east.internal.npmjs.com"}}, "4.0.0": {"name": "test-exclude", "version": "4.0.0", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@4.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "homepage": "https://github.com/istanbuljs/test-exclude#readme", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "nyc": {"lines": 100, "branches": 97, "reporter": ["lcov", "text"], "functions": 100, "statements": 100, "check-coverage": true}, "dist": {"shasum": "0ddc0100b8ae7e88b34eb4fd98a907e961991900", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-4.0.0.tgz", "integrity": "sha512-edXWres68s95HIxqkD59tKxX7AIDU8BgH7OWIgvn4c+xuDn08oE47IHpFeikkybAhlfpqNZKiLsa65zzYkMQpQ==", "signatures": [{"sig": "MEUCIQCMYLX1Ocg1Xzy3asU5s2dFK5MmM8PqsRuDSn8Bf48xjwIgaS3kSwf2tPb5leeI8W0iRI80ZIgSaLntcwMPizIu5Gk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0ddc0100b8ae7e88b34eb4fd98a907e961991900", "gitHead": "d07955b265f079726d75590848e430399c8a3a9c", "scripts": {"test": "nyc mocha", "pretest": "standard", "release": "standard-version"}, "_npmUser": {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/test-exclude.git", "type": "git"}, "_npmVersion": "4.0.5", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "greenkeeper": {"ignore": ["read-pkg-up"]}, "_nodeVersion": "7.4.0", "dependencies": {"arrify": "^1.0.1", "micromatch": "^2.3.11", "read-pkg-up": "^1.0.1", "object-assign": "^4.1.0", "require-main-filename": "^1.0.1"}, "devDependencies": {"nyc": "^10.1.2", "chai": "^3.5.0", "mocha": "^3.1.2", "standard": "^8.5.0", "coveralls": "^2.11.12", "standard-version": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude-4.0.0.tgz_1485078868609_0.3595972142647952", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.1": {"name": "test-exclude", "version": "4.0.1", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@4.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "homepage": "https://github.com/istanbuljs/test-exclude#readme", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "dist": {"shasum": "a8ea2f51ed15c7c458c279bd61391ca0a09b0a24", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-4.0.1.tgz", "integrity": "sha512-BOPMCYh8g76qCTzQOD6c4XCbC/ju7Z0JZ+3vuUNSbDsS/tNxd0aPUlfNdC4HLpSnZmx41/aWEpYYU/Oa3lJ9Cg==", "signatures": [{"sig": "MEYCIQCEJSxr/ftG/6FD91Wg3UmR0VkKQE5daZ8RFLmkE7X2zgIhAIQPJFRbbHZyym4XvZJU2nHZqPEYKQOFexgDBWNQH4VH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "a8ea2f51ed15c7c458c279bd61391ca0a09b0a24", "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/test-exclude.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "greenkeeper": {"ignore": ["read-pkg-up"]}, "_nodeVersion": "6.9.5", "dependencies": {"arrify": "^1.0.1", "micromatch": "^2.3.11", "read-pkg-up": "^1.0.1", "object-assign": "^4.1.0", "require-main-filename": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.1.2", "standard": "^9.0.0", "coveralls": "^2.11.12"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude-4.0.1.tgz_1489982003359_0.8693729802034795", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.2": {"name": "test-exclude", "version": "4.0.2", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@4.0.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "homepage": "https://github.com/istanbuljs/test-exclude#readme", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "dist": {"shasum": "a01b846705fbcbef1700618d2f3e37df36f85779", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-4.0.2.tgz", "integrity": "sha512-LIixHb0HuXS3UgbubI38yw4Qwx7wzzks54BIchBG8bd0NQxQ6FEbYKxB3sph1ukXWKGt4fKo+rwNDKEha281Vg==", "signatures": [{"sig": "MEUCIE8jw+QN6EHms9a3K8pQGiQRDtmmdixszWFBOy2pDHT9AiEAv8pEOMnr87lCNQ6FMKynWBE1S9L4/qaNTQgaf5PNr38=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "a01b846705fbcbef1700618d2f3e37df36f85779", "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/test-exclude.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "greenkeeper": {"ignore": ["read-pkg-up"]}, "_nodeVersion": "7.1.0", "dependencies": {"arrify": "^1.0.1", "micromatch": "^2.3.11", "read-pkg-up": "^1.0.1", "object-assign": "^4.1.0", "require-main-filename": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.1.2", "standard": "^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude-4.0.2.tgz_1490076915686_0.7433674158528447", "host": "packages-18-east.internal.npmjs.com"}}, "4.0.3": {"name": "test-exclude", "version": "4.0.3", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@4.0.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "homepage": "https://github.com/istanbuljs/test-exclude#readme", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "dist": {"shasum": "86a13ce3effcc60e6c90403cf31a27a60ac6c4e7", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-4.0.3.tgz", "integrity": "sha512-UMfGCaEFjVHUoFwi6t7y/f6N69SRnXnOHdu4xRUes30sg6eNrNanVgUv6W9aLKbhADPe3EGHvx+Eh4SYwfGY2Q==", "signatures": [{"sig": "MEYCIQD1zgw9xj/lBaWDooivtFjUAbbRdClatIT21t1WZ7SK/AIhAMX4QoLYYDevt3aEWMQicH45CG5si77+SFIolntudmL6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "86a13ce3effcc60e6c90403cf31a27a60ac6c4e7", "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/test-exclude.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "greenkeeper": {"ignore": ["read-pkg-up"]}, "_nodeVersion": "7.1.0", "dependencies": {"arrify": "^1.0.1", "micromatch": "^2.3.11", "read-pkg-up": "^1.0.1", "object-assign": "^4.1.0", "require-main-filename": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.1.2", "standard": "^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude-4.0.3.tgz_1490077602616_0.8311135193798691", "host": "packages-12-west.internal.npmjs.com"}}, "4.1.0": {"name": "test-exclude", "version": "4.1.0", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@4.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "homepage": "https://github.com/istanbuljs/test-exclude#readme", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "dist": {"shasum": "04ca70b7390dd38c98d4a003a173806ca7991c91", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-4.1.0.tgz", "integrity": "sha512-lh3bsPodJEsqxyKnRYep7V8BOzQgxvOHYAfuVuP1PJ5oEgk6aBh2W37mCRNu0Ueo8oPF9kVPxIEmzj7JSRd8bw==", "signatures": [{"sig": "MEUCIHwpTDvIxQy4i0Djucm1oX/vx0rOSn5F1IchN/WIRobxAiEA+gl8CkrWrohLgCpG8rrl7/Nmu+Cf29/kCWUqv8wWBck=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js"], "_shasum": "04ca70b7390dd38c98d4a003a173806ca7991c91", "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/test-exclude.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "greenkeeper": {"ignore": ["read-pkg-up"]}, "_nodeVersion": "7.1.0", "dependencies": {"arrify": "^1.0.1", "micromatch": "^2.3.11", "read-pkg-up": "^1.0.1", "object-assign": "^4.1.0", "require-main-filename": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.1.2", "standard": "^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude-4.1.0.tgz_1493442004804_0.6866360483691096", "host": "packages-12-west.internal.npmjs.com"}}, "4.1.1": {"name": "test-exclude", "version": "4.1.1", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@4.1.1", "maintainers": [{"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "4d84964b0966b0087ecc334a2ce002d3d9341e26", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-4.1.1.tgz", "integrity": "sha512-35+Asrsk3XHJDBgf/VRFexPgh3UyETv8IAn/LRTiZjVy6rjPVqdEk8dJcJYBzl1w0XCJM48lvTy8SfEsCWS4nA==", "signatures": [{"sig": "MEQCIAap18+chpAxJaZ6HqRDVMVrobscIl0YYWHXpuSrJIxzAiAelTjZoWTnHqFxnrFM30/kZnjlKGBlhqfRzKVgVbLyEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["index.js"], "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "greenkeeper": {"ignore": ["read-pkg-up"]}, "_nodeVersion": "7.1.0", "dependencies": {"arrify": "^1.0.1", "micromatch": "^2.3.11", "read-pkg-up": "^1.0.1", "object-assign": "^4.1.0", "require-main-filename": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.1.2", "standard": "^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude-4.1.1.tgz_1495919579354_0.9240932059474289", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "test-exclude", "version": "4.2.0", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@4.2.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "07e3613609a362c74516a717515e13322ab45b3c", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-4.2.0.tgz", "fileCount": 5, "integrity": "sha512-8hMFzjxbPv6xSlwGhXSvOMJ/vTy3bkng+2pxmf6E1z6VF7I9nIyNfvHtaw+NBPgvz647gADBbMSbwLfZYppT/w==", "signatures": [{"sig": "MEYCIQCFdtFrGaeJUn36e/gDhvmeiKC9uS7QIzmbyMSKKHbk+AIhAIFF+3nSJs30xt4PqQjA82Z1VtU67msfq8VCpOqqGDcA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13463}, "main": "index.js", "files": ["index.js"], "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "greenkeeper": {"ignore": ["read-pkg-up"]}, "_nodeVersion": "8.8.1", "dependencies": {"arrify": "^1.0.1", "micromatch": "^2.3.11", "read-pkg-up": "^1.0.1", "object-assign": "^4.1.0", "require-main-filename": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.1.2", "standard": "^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude_4.2.0_1518500918831_0.8735647603050614", "host": "s3://npm-registry-packages"}}, "4.2.1": {"name": "test-exclude", "version": "4.2.1", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@4.2.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "dfa222f03480bca69207ca728b37d74b45f724fa", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-4.2.1.tgz", "fileCount": 5, "integrity": "sha512-qpqlP/8Zl+sosLxBcVKl9vYy26T9NPalxSzzCP/OY6K7j938ui2oKgo+kRZYfxAeIpLqpbVnsHq1tyV70E4lWQ==", "signatures": [{"sig": "MEQCIBIP1JTCswv8xp3KVbADKU/cTcdLWcgN1EyJPnYoC8b3AiA+rm9iFfJ1aB/ecEsUzHALG635Nryv3PfmEvev4dbQAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13755}, "main": "index.js", "files": ["index.js"], "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "greenkeeper": {"ignore": ["read-pkg-up"]}, "_nodeVersion": "8.8.1", "dependencies": {"arrify": "^1.0.1", "micromatch": "^3.1.8", "read-pkg-up": "^1.0.1", "object-assign": "^4.1.0", "require-main-filename": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.1.2", "standard": "^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude_4.2.1_1520188976002_0.9348203840842662", "host": "s3://npm-registry-packages"}}, "4.2.2": {"name": "test-exclude", "version": "4.2.2", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@4.2.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "8b67aa8408f84afc225b06669e25c510f8582820", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-4.2.2.tgz", "fileCount": 5, "integrity": "sha512-2kTGf+3tykCfrWVREgyTR0bmVO0afE6i7zVXi/m+bZZ8ujV89Aulxdcdv32yH+unVFg3Y5o6GA8IzsHnGQuFgQ==", "signatures": [{"sig": "MEQCIAtabC1pTMx1Mr0q/oOfbgt2b6/hiT4BNmYpNl3mJnIVAiAFbg8fhkmdx+Rn1zH8++x5sjOGz9Ki+BVPdBHD5XV5gA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13883, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbFy/ACRA9TVsSAnZWagAAfJAP/1/RgxGVYUWnaBKcN8VA\nGKdTyv6Zh3uGsVpoJa+wqTeYoKh2EB8v37m5dXKt6UBH+dPTMO5l8DbySmlM\nlxfweSOjqz9ToqY9voYBRC3wDDE2K1dgZnkyDhsDlfdnQWUaU05u99mYXgI/\nwokHAGa+axrFml3wf74WrAkRw8Vz7kw7DdDxnCCF6fFDnpGebRsApnoWnd2h\n+uch8hgQlkGA646WySBLjUK4kb5jqK6L4ESA83hK8nBt8IODBbbnB0fuGlGy\nRG8NjZkQDzyGgMUpA3iCu557ZyDPVkLWKY71BkvCVDh7eFVw9x+w15uFox/c\nk+PcifBYRx3+hIFwSsS17Zu7vPGdZdc0inJsphc3PY7Qar14ROghLMg3oSmK\nwPs6mhtM/YFH6eBbHCgaVrdcQ3zPKTSBhTh/r/HSsdbjdtSoVq9ERG7iniVc\nrDsVxC//EGG6umC4amAwElSg36DcXMYpzKpq3Pl3lutDHw8bDuewVe7c2YnS\naJHGg3E5Tgy3/yMNgytfST34Uv/VnocyeAuVarJw6Ieayp+b2Wo85Oa+Sm1U\nQ3IKgCv3bK/fKFvIAvdSjWK+jFtFmx9O03323QbQwjlWezEU7n2ZuoBYE/Jl\nOCNHXU2i3MVmZQ6Knp924w3tSRVCHUbATkfVuUmIojDdEuum/YDfiZ8l0OYh\nmv46\r\n=cznF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js"], "readme": "# test-exclude\n\nThe file include/exclude logic used by [nyc](https://github.com/istanbuljs/nyc).\n\n[![Build Status](https://travis-ci.org/istanbuljs/test-exclude.svg)](https://travis-ci.org/istanbuljs/test-exclude)\n[![Coverage Status](https://coveralls.io/repos/github/istanbuljs/test-exclude/badge.svg?branch=master)](https://coveralls.io/github/istanbuljs/test-exclude?branch=master)\n[![Standard Version](https://img.shields.io/badge/release-standard%20version-brightgreen.svg)](https://github.com/conventional-changelog/standard-version)\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/test-exclude.svg)](https://greenkeeper.io/)\n\n## Usage\n\n```js\nconst exclude = require('test-exclude')\nif (exclude().shouldInstrument('./foo.js')) {\n  // let's instrument this file for test coverage!\n}\n```\n\n_you can load configuration from a key in package.json:_\n\n_package.json_\n\n```json\n{\n  \"name\": \"awesome-module\",\n  \"test\": {\n    \"include\": [\"**/index.js\"]\n  }\n}\n```\n\n_app.js_\n\n```js\nconst exclude = require('test-exclude')\nif (exclude({configKey: 'test'}).shouldInstrument('./index.js')) {\n  // let's instrument this file for test coverage!\n}\n```\n\n## Including node_modules folder\n\nby default the `node_modules` folder is added to all groups of\nexclude rules. In the rare case that you wish to instrument files\nstored in `node_modules`, a negative glob can be used:\n\n```js\nconst exclude = require('test-exclude')\nconst e = exclude({\n  exclude: ['!**/node_modules/**']\n})\n```\n\n## License\n\nISC\n", "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "_nodeVersion": "10.2.1", "dependencies": {"arrify": "^1.0.1", "minimatch": "^3.0.4", "read-pkg-up": "^3.0.0", "require-main-filename": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"chai": "^4.1.2", "mocha": "^5.2.0", "standard": "^11.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude_4.2.2_1528246207469_0.08114960718704212", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "test-exclude", "version": "5.0.0", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@5.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "cdce7cece785e0e829cd5c2b27baf18bc583cfb7", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-bO3Lj5+qFa9YLfYW2ZcXMOV1pmQvw+KS/DpjqhyX6Y6UZ8zstpZJ+mA2ERkXfpOqhxsJlQiLeVXD3Smsrs6oLw==", "signatures": [{"sig": "MEUCIQDgYD/9DgLfstE8AnHPX6Z+y+gUngWlj3HyYq7HZ327kgIgHImeQ4ZgoLf6+SG2H9B6UKTzQv4r+E2iWUESpELyg48=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14311, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbMdCiCRA9TVsSAnZWagAAZ0sP/RL8PXbfZQ2G88RwKE/i\n/2n2c58Q42dXJ547e50bB12mDIXDtOt4SbYmhlkf+bbBLhJx50NXYc2hNPj4\nFDoTGBxEyXZT2fUPG7HF/gdNU69XB8W2PsvAQGpz/QMDFHuqrXovgEvaKT7b\nKOBZ7qGTo2IqHNXbEX9qT2suPY/CtfNOyW36kr+V99nz4EL8wd0ogNTUqGrG\nMblJGNoXbyHBF+YOpKC4CVgZhVYkzr5ovxF0NSYMk+46SW77J2HudbiP8n49\n6SK/YCml4U8Z3nv2CjB3hgWHIpMYKSmA3qiEBKVSm6PoDnXnYGdVSJoniKN8\nzeuTH0RsiCCE15f2ymYcQTOvvbrgjGCDUpypQRVfKtbJcuAVgHZMj+9FjEMQ\nsw4ylCCdQCOCx/vv5EW7KN4IHNUZNoulehZ0GDvumIzepX0UgcBWW74AvqgG\nIipDH2vUo1gLcGFwIhQxcBgKdqaPzyYXV0qzWXogMYhQrJuiXlBFzhWhFGl1\n2o+ctaf39Yk+CoTDe9TiJN7M8y3W5FtTkxIM3XM2p7YJNTkT/5CeEedfRqzL\n5YDJtel6+lmrAZkZhgYBnJxoy2xnBzlaqEXcgFLx7Dl8/fcyDeqZNcqfSYaZ\nSAydq1uheHvjFqVTTx+46YicTkqHc2u1Jblg1OLXV7JKHgG6Uxyja2YGz+FZ\nHuI0\r\n=LYtc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["index.js"], "engines": {"node": ">=6"}, "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "_nodeVersion": "10.2.1", "dependencies": {"arrify": "^1.0.1", "minimatch": "^3.0.4", "read-pkg-up": "^4.0.0", "require-main-filename": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.2", "mocha": "^5.2.0", "standard": "^11.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude_5.0.0_1529991330007_0.34832577511004525", "host": "s3://npm-registry-packages"}}, "4.2.3": {"name": "test-exclude", "version": "4.2.3", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@4.2.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "a9a5e64474e4398339245a0a769ad7c2f4a97c20", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-4.2.3.tgz", "fileCount": 5, "integrity": "sha512-SYbXgY64PT+4GAL2ocI3HwPa4Q4TBKm0cwAVeKOt/Aoc0gSpNRjJX8w0pA1LMKZ3LBmd8pYBqApFNQLII9kavA==", "signatures": [{"sig": "MEYCIQDrfEWNlck5jR+aXtxFe8X48UsXV6aUa2CIWZ99xjTgKgIhALh6x44D5dwgQQTcrCdauddB4q3zvy7NjntAv/83rKCb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14307, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbkFiJCRA9TVsSAnZWagAAhVQQAJ3eJIc5ZW78Geg9L46Y\nmKpU3RyhuFh48gwA34/4/5QsQTLLOi5mx0HgJ2FVEzN4f7c/0/sZVkSRv8sN\nwlFgWwKA0BL6qNT+X/tGsBvrsSoE/j1U7IBOyI5vhsCGd656nrkLR0nRnmwp\nQ4xK2VQryzeymNigWQBSOkD+BiSI+rK1v4X7UKTEfDtBZ4t8XKiqd2Tcegz8\neiRanhN4tBvk81uYRXRUTPOraczJ778mTr4B5tvgKKwi+OQdw8n9iG1NbMlp\nHUh8se4OcPkt0uxw9osNjplxSRQLS4NA0vj1yGcq4fQ9aE9PV2qzyQPXeO49\nJPvwlTnoa5uhvP42iyBwnWLEKJc3NQJELnyxbc9RIN8KRIAKarnljyKF6ACC\nLcE2LPDSMlyI5BAUkQdO4AKnhxpU1CyqWVazwwOawz9u9RlCXvl4x3lGyWqS\nTbhrCd0NdRbx67ZcdYFxBlWKkAX5yiCUfTVr6nofE8VCpPkKbxHasHCwW4MZ\nUEQtZsAlnRWjmpiQoGmFdYyCxoLULo9MNW4w9RbDA0N9Fc7PXlRhe7u0FmiA\nIm0A3iRcQTzGU2HnKc435HKgHajPB8R2wXj8DNVTJurb+hPv+lLWtrUaZJ5t\njm2eYmY/lpHYs6Qpa6po/mdy3wZIWt3r6GVsobuNTIg8JrzRhc8MiC2+eNs6\nZ8JV\r\n=UJ7b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "scripts": {"test": "mocha", "pretest": "standard"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+https://github.com/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "greenkeeper": {"ignore": ["read-pkg-up"]}, "_nodeVersion": "10.9.0", "dependencies": {"arrify": "^1.0.1", "micromatch": "^2.3.11", "read-pkg-up": "^1.0.1", "object-assign": "^4.1.0", "require-main-filename": "^1.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.1.2", "standard": "^9.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude_4.2.3_1536186504402_0.01974045492065346", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "test-exclude", "version": "5.0.1", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@5.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "714cd156984c04c5f368fb871c0aa225fc395241", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-5.0.1.tgz", "fileCount": 5, "integrity": "sha512-/MLYvfg/KfmR8VKyDI0frzJUdAxBn1QXJHi0ik4IEVY84WcmskRKpx+peZPtnDf+If7HflGhLz0uH1Uh/phFJw==", "signatures": [{"sig": "MEUCIEg8zjshYo2vL+m5OWHho3Ij+71B1xvwMvE94A8AyO2iAiEAs3U5/Ns8NUWPGVnUbt/qhzBFyuXM+nYwPaXpp1RFnMQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14979, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcIXvcCRA9TVsSAnZWagAA9YEP/iAQIaalHL9Wrw8ZDZRX\nDUgC0DyhNTRbJSJXdNe3U3OIV9Og08oC0f2HkUPvCXIDA9cH6kJHhJro/wj8\nCZeOisRSP5aaX4rFbBdWyKnPUsPqQyRsIBI2dnlBe3pX0QzE6aSYWCOvwsTb\nKgRNnQmTvsS6ZIVlLB+gjSqqnrwUXHeaBhnACePI20dokV0ljCmFJkrksJjI\nzDlLFuFfHnh9Y0uiay4sJfZ2G9sscIz38OjD3Wk/YapE9hVnIWQqSoHtvCa7\nFzGmsagbo61ZZEmdYlvaECk8zU3IVD4/q/yYQiYY9GDZ8t7T0MDEYsNO3jaN\nERmaGD46XLo4G3hLx199XwDsaR4EKszpWG2mh9+JAy4kVK6FL8pgv4+TyS8B\nLJdxCuvewDet2Rz2Lb8u9T8/aYDs2vte79bYTp+V8zhngDxpYK2UnRPq/UoY\n8uvver5Fgc86Ad+oZrC2VSGYeD22nzQgLDej/BiM5+DcQDZsrOrDSDz40mTP\nUj2TRjQ3f7yt2P6ewJD6fJnSM0BasdR6tpflptiRvEMzavCmgDepqL5CCwGP\neAzzdsbAOR1nYbilhVhz52Vh9gDpfKdIO8gXBsvXs4BBVYVlbrjgIWAPcp+m\nRtA3nKBjSKVpWkK0u+I2gWlG17KuRsPelPI5fiOZBZ3iSFkFPdLdc5MT4Oyc\n92bA\r\n=Vi7S\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "readme": "# test-exclude\n\nThe file include/exclude logic used by [nyc](https://github.com/istanbuljs/nyc).\n\n[![Build Status](https://travis-ci.org/istanbuljs/test-exclude.svg)](https://travis-ci.org/istanbuljs/test-exclude)\n[![Coverage Status](https://coveralls.io/repos/github/istanbuljs/test-exclude/badge.svg?branch=master)](https://coveralls.io/github/istanbuljs/test-exclude?branch=master)\n[![Standard Version](https://img.shields.io/badge/release-standard%20version-brightgreen.svg)](https://github.com/conventional-changelog/standard-version)\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/test-exclude.svg)](https://greenkeeper.io/)\n\n## Usage\n\n```js\nconst exclude = require('test-exclude')\nif (exclude().shouldInstrument('./foo.js')) {\n  // let's instrument this file for test coverage!\n}\n```\n\n_you can load configuration from a key in package.json:_\n\n_package.json_\n\n```json\n{\n  \"name\": \"awesome-module\",\n  \"test\": {\n    \"include\": [\"**/index.js\"]\n  }\n}\n```\n\n_app.js_\n\n```js\nconst exclude = require('test-exclude')\nif (exclude({configKey: 'test'}).shouldInstrument('./index.js')) {\n  // let's instrument this file for test coverage!\n}\n```\n\n## Including node_modules folder\n\nby default the `node_modules` folder is added to all groups of\nexclude rules. In the rare case that you wish to instrument files\nstored in `node_modules`, a negative glob can be used:\n\n```js\nconst exclude = require('test-exclude')\nconst e = exclude({\n  exclude: ['!**/node_modules/**']\n})\n```\n\n## License\n\nISC\n", "engines": {"node": ">=6"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "_nodeVersion": "11.5.0", "dependencies": {"arrify": "^1.0.1", "minimatch": "^3.0.4", "read-pkg-up": "^4.0.0", "require-main-filename": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"chai": "^4.1.2", "mocha": "^5.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude_5.0.1_1545698267259_0.5143346260025117", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "test-exclude", "version": "5.1.0", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@5.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "6ba6b25179d2d38724824661323b73e03c0c1de1", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-5.1.0.tgz", "fileCount": 5, "integrity": "sha512-gwf0S2fFsANC55fSeSqpb8BYk6w3FDvwZxfNjeF6FRgvFa43r+7wRiA/Q0IxoRU37wB/LE8IQ4221BsNucTaCA==", "signatures": [{"sig": "MEYCIQDpfNMYhIOt5gXI04E2m8ug7vaD+oi7JQZbQbHvXK+WwAIhAN+UhJLydl5LBVTwTjNdACLAR1sFY8G2b7iolX+JQ6sZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15281, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcS8VXCRA9TVsSAnZWagAALjwQAJFJf7wCLACAVmhCj6tq\nUwJt3p0MxGbEU+M2vneYxcanzwyYbV6wKUoIeH5lJD58f3c6SXw+6AsvixPl\nKGnjvG8LQRg9oqvvDuGFisCTX/P71a1VMnc4ObdGDVZZ+Rtfercf/n4tNA9s\nJAHo255uy5gkAVkrf6WfvBLy8cmlK2UQeEf78H1YNTqG68BrJN/5TmqJikyP\nfDMKyMGgmkdlXPO7zkrJAyz6zqRTBkMstwFpGLioYOWOVvelcbU84pCnQHFO\nV1gRymn7Fp16LbWvbcX/DbgX7X2tMKyaqwhrg0r5P3UcovsQMavgfHBDUTKl\nU1XApEt6GEzQ3OVP2NsMCxkeW7e0iu7/SrBYfeFHZk4eizM9MWK/WIqurOr0\n9h8yurPViwZovkjB6bDmUuiejD5w3XnvwcDjQz/HVd9TQGHfAUucxqTjs2Ox\nlJa94EjlM/zPd0cGy3geRqW0qjZCP3ZXN8ttJuX7T362A7pnq9kUACi6dsyJ\no6q4BhaYhiKzjVSzpA+9fd1cBtCXQ5aID+bOlKryx03AWIo9Flnd9RuGQFko\nw1BSb+lXhkezUq+hLjrjz9oD9spqZcHWVhGiNUj/8SURnoX59EJIRT/1i4ic\n6GoeM1CEZpELOLAwksDGMhpGJ+VsUlbKrPZmYqVQrMI4LkLHsKR6NXEzOtZx\ntrDI\r\n=JZtf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6"}, "gitHead": "7875defdc3c3640787ac5d83700246de119e8b83", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+https://github.com/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "lerna/3.10.5/node@v10.14.2+x64 (linux)", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "_nodeVersion": "10.14.2", "dependencies": {"arrify": "^1.0.1", "minimatch": "^3.0.4", "read-pkg-up": "^4.0.0", "require-main-filename": "^1.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-exclude_5.1.0_1548469590851_0.9829596765660666", "host": "s3://npm-registry-packages"}}, "5.2.0": {"name": "test-exclude", "version": "5.2.0", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@5.2.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "68dd36ca35ccc41e82c2d370eb47cf751f533015", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-5.2.0.tgz", "fileCount": 5, "integrity": "sha512-9j9a0PoALUBofbh1Xv/8nP7AGjvzUDy7QYjtuE89JjJTdsjQaju2/2gIZ3AduZ3tpKsK2FnP05c5TD4YMDlUXw==", "signatures": [{"sig": "MEUCIQDe4HNjGFL95pmfc3O1KB74gvYKOIWBuDFL6fQtvKq7ZwIgevOehQODecPNEpKfhk+jL0uOotaq4n/+FqyNI23caU0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17126, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchwfrCRA9TVsSAnZWagAAo6cP/0ZDL87hRtuzTmm/8Ixe\nVVC53nDwFrVBUL7hpa1N51joblM0KPBSKHwWwAxaEu4cxTH3tqzylQLnXdvv\na61z2J5fDoXdANWXe4WpeL4bjylRvDdzXshvAvUpvpjDjL5PSfcoZG2mIETZ\nxpVaSBG/GZnVFJMqzQ5M7nwfIxMgIlzdnMV9YksQfFyl+O22Kz6UMD+TRCXV\n7PdOExTag5upudIsjU4ZSHK4p5HUiACjsfsr3qMqLcMZIRuh93TujeNxRq/E\nfz1DQMHR+RZPD9WAGHR/EEC3RcF6Z2FFXCEFnUUJrwqDSnP5WI1Zzy82q7oO\nbnV/ILJRMCIokrvc6n6AuIiOHMCqNuENRoRQ/uQuqwfGtZYOS0tDDiKSNYh1\nuam4oTvdbsHrynaYpY+CeSflitiP82F1K/v5wcWpX/46SVuOaynjkpAAbSTe\nrhE9Bw1RIxY+6dWwimy6MXox7h0qIgXqbID1J5u7MOTK8Mu8EgWfUKU1ZhHr\nmt4/UfTsKntz5PAhCCXWE0WM1YpeNUm40XMpzZu5fB3J/E/nQVKH8aVxLwXm\nLYZCF9OOMNVrwmW3V2QmV3+VPJbvhDGDJkErLmmy3+3SjReNugLkhEDQAY0q\nqtQeG7mrC1FvVOsO7hw0t5TQvcGl9c4zU4UoI/jPmn2MWtqoUX0kppVZW5he\n0YtF\r\n=zc3e\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "readme": "# test-exclude\n\nThe file include/exclude logic used by [nyc](https://github.com/istanbuljs/nyc).\n\n[![Build Status](https://travis-ci.org/istanbuljs/test-exclude.svg)](https://travis-ci.org/istanbuljs/test-exclude)\n[![Coverage Status](https://coveralls.io/repos/github/istanbuljs/test-exclude/badge.svg?branch=master)](https://coveralls.io/github/istanbuljs/test-exclude?branch=master)\n[![Standard Version](https://img.shields.io/badge/release-standard%20version-brightgreen.svg)](https://github.com/conventional-changelog/standard-version)\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/test-exclude.svg)](https://greenkeeper.io/)\n\n## Usage\n\n```js\nconst exclude = require('test-exclude')\nif (exclude().shouldInstrument('./foo.js')) {\n  // let's instrument this file for test coverage!\n}\n```\n\n_you can load configuration from a key in package.json:_\n\n_package.json_\n\n```json\n{\n  \"name\": \"awesome-module\",\n  \"test\": {\n    \"include\": [\"**/index.js\"]\n  }\n}\n```\n\n_app.js_\n\n```js\nconst exclude = require('test-exclude')\nif (exclude({configKey: 'test'}).shouldInstrument('./index.js')) {\n  // let's instrument this file for test coverage!\n}\n```\n\n## Including node_modules folder\n\nby default the `node_modules` folder is added to all groups of\nexclude rules. In the rare case that you wish to instrument files\nstored in `node_modules`, a negative glob can be used:\n\n```js\nconst exclude = require('test-exclude')\nconst e = exclude({\n  exclude: ['!**/node_modules/**']\n})\n```\n\n## License\n\nISC\n", "engines": {"node": ">=6"}, "gitHead": "c81b051d83217947dfd97d8d06532bd5013e98c3", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+https://github.com/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "lerna/3.13.0/node@v10.15.3+x64 (linux)", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"glob": "^7.1.3", "arrify": "^1.0.1", "minimatch": "^3.0.4", "read-pkg-up": "^4.0.0", "require-main-filename": "^2.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/test-exclude_5.2.0_1552353258638_0.5024353114887681", "host": "s3://npm-registry-packages"}}, "5.2.1": {"name": "test-exclude", "version": "5.2.1", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@5.2.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "08b0515119d6a3d854635db8b283933df52014b1", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-5.2.1.tgz", "fileCount": 5, "integrity": "sha512-q1nstjoN8GfMReH/WpYoFIC+RvZ8BhhUOnaFouG/pXm/gU083Pw3S8QRJm9CNZF/XnZkTRyw0/0b6CNdKaLAHA==", "signatures": [{"sig": "MEQCIENKQcx27bI2SD2QDNHXqMg7aTrwGtLf9t7KwvL7eiyHAiBBnDowuPxXAQaATOVMi8SYELs6ZurxugK12WuQZPIMhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17503, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpPXCCRA9TVsSAnZWagAAPKEP/100KZhTokvgleqVD0//\nHIozu7onBM9nkUWssPtf+hvdKDEqYMga1nxIH0tvyJBGob2PbsXwDXqG7h3P\nWftUVXt6veRbI5rsTVaYrePcF0Ww0OmFwW5DsjPz9kyfugnjP+vCnzeKp/Q/\nGDLBipwFI965sRBpblrnHm4FV2g+hMZCoCA5wVfzI7+gxjLbwC0V4b5zDZcH\nKxnfZ+fPTx1Ql6G2ykEwfSaZlYe4yMPR8L9HAhRGl/KGSkjaBMFbcuWJ2eXA\nY9wjOVi0LTK5ZH/jxhT2mUShmmGhDvUxXXcLQp2n5R+5USOsiXGyVRoS/aL2\nfSKyMZvKuGlQmt9JuQW2dvMpdvG7BbTh4Cy2k+/Evk+XeXvFld2e8X0MBLKn\n43P0NFMsK7MB5pfkiJalMeTPJCJ+4ImoP8lz/Sg4lN/Pc4eupJx5Ta8eoszz\ngbnBGBA3H3uxd/iZThrySmEYI9QfZwOhJP5jEU4diGh1riSlmjJUx6h9mOCI\nx0w74Rgkr0gKYVgRzd6RpES8dGhRNypQu6xA/vfgm+jc1+M9JNWvQBVJD0cN\na7fPs1wx/2eeY+wVhao2c6FimJ0LLkk/VpTBLTLs9wdT2T8RyXwzkS+BAeTX\nUt7BjalewFeTmh0xoeYd5ToZP5KKB8F5e77KkZwWbdPCBoeC+afBK8Nn7TIX\ndGZ1\r\n=TBHr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "readme": "# test-exclude\n\nThe file include/exclude logic used by [nyc](https://github.com/istanbuljs/nyc).\n\n[![Build Status](https://travis-ci.org/istanbuljs/test-exclude.svg)](https://travis-ci.org/istanbuljs/test-exclude)\n[![Coverage Status](https://coveralls.io/repos/github/istanbuljs/test-exclude/badge.svg?branch=master)](https://coveralls.io/github/istanbuljs/test-exclude?branch=master)\n[![Standard Version](https://img.shields.io/badge/release-standard%20version-brightgreen.svg)](https://github.com/conventional-changelog/standard-version)\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/test-exclude.svg)](https://greenkeeper.io/)\n\n## Usage\n\n```js\nconst exclude = require('test-exclude')\nif (exclude().shouldInstrument('./foo.js')) {\n  // let's instrument this file for test coverage!\n}\n```\n\n_you can load configuration from a key in package.json:_\n\n_package.json_\n\n```json\n{\n  \"name\": \"awesome-module\",\n  \"test\": {\n    \"include\": [\"**/index.js\"]\n  }\n}\n```\n\n_app.js_\n\n```js\nconst exclude = require('test-exclude')\nif (exclude({configKey: 'test'}).shouldInstrument('./index.js')) {\n  // let's instrument this file for test coverage!\n}\n```\n\n## Including node_modules folder\n\nby default the `node_modules` folder is added to all groups of\nexclude rules. In the rare case that you wish to instrument files\nstored in `node_modules`, a negative glob can be used:\n\n```js\nconst exclude = require('test-exclude')\nconst e = exclude({\n  exclude: ['!**/node_modules/**']\n})\n```\n\n## License\n\nISC\n", "engines": {"node": ">=6"}, "gitHead": "e8063c799d0854341cb3daaf91c58acd06bd501c", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+https://github.com/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "lerna/3.13.1/node@v10.15.3+x64 (linux)", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"glob": "^7.1.3", "arrify": "^1.0.1", "minimatch": "^3.0.4", "read-pkg-up": "^4.0.0", "require-main-filename": "^2.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/test-exclude_5.2.1_1554314689675_0.5497279288381476", "host": "s3://npm-registry-packages"}}, "5.2.2": {"name": "test-exclude", "version": "5.2.2", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@5.2.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "7322f8ab037b0b93ad2aab35fe9068baf997a4c4", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-5.2.2.tgz", "fileCount": 5, "integrity": "sha512-N2pvaLpT8guUpb5Fe1GJlmvmzH3x+DAKmmyEQmFP792QcLYoGE1syxztSvPD1V8yPe6VrcCt6YGQVjSRjCASsA==", "signatures": [{"sig": "MEQCIE4VSd+zoI/agv5S+2TZ1BccYBKgLkuRzW6V2wpVWCK3AiAv51jKl86TJknWXLTbsJbdhS+La5ZUSqvuATMYxb/12w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrSjtCRA9TVsSAnZWagAA9bgP/1xFlmerrNL8i/YTlRoD\ntGd1+YXjB0N/260l6wGYNIWBmhbIipIIZtIauGQYks87O67Dbw4CkFSx9PuD\nRsPbbT77ExDogo638uDsknK3u40mgId1lxNP4wXgf031qlL10eBnJB2mYMzu\nMF/DJkvUY5AYcORged0HxXN+CKjIogFNffAC21ukx6p3PKMi/1+cLbDB7Wdh\nr87LCj6AifQ5pgOobLc4i8S3AIPbV2B3wbtMiWD0YimfXqQ5zOustwJWDHht\nEP0/HnyEZ2g55VlRrIqT4wW3GB4um6zcnaQG61KkSYgkBf0UTVdOCeu4pFVh\nSa5q+biihBZPHKrL1Ji92SlFSoHc5EFBHFvJjTEWG+q4Tr5m73YfvCFQr+fq\ne70eHppFEt7cc0j6aWSx2WQZAycZEDmZwpomEleR5VbYXG9inoMKy+hD5o2J\nqAhx9U/sfZ/d5WSHIHy/zzOnFkVRyTqfHbEeMBn4dcPLzhoBIiYoogSY6joP\nIMAEgJ6mju/mtbSK2Y3Lq3iB5S81KcF0m2prwCVTITNZUsvvev08f0azsULf\n3up3YREsxQx5g2Y+lZzK7GLEvw1wZNZ/wQ3mq1b7IGR1Ryx6AW/IScL8KhXb\nN5e/n9vYwg6Wx0eBwcCUzuYzD1Garc8QlfTOYPAKrlt9jFbPnpACYebUU2Q0\nrcDf\r\n=AZAU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6"}, "gitHead": "9f8aebf1f08159df20358d77fe98c809d2027c5f", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+https://github.com/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "lerna/3.13.2/node@v10.15.3+x64 (linux)", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"glob": "^7.1.3", "minimatch": "^3.0.4", "read-pkg-up": "^4.0.0", "require-main-filename": "^2.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-exclude_5.2.2_1554852076366_0.19688912548049275", "host": "s3://npm-registry-packages"}}, "5.2.3": {"name": "test-exclude", "version": "5.2.3", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@5.2.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "c3d3e1e311eb7ee405e092dac10aefd09091eac0", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-5.2.3.tgz", "fileCount": 5, "integrity": "sha512-M+oxtseCFO3EDtAaGH7iiej3CBkzXqFMbzqYAACdzKui4eZA+pq3tZEwChvOdNfa7xxy8BfbmgJSIr43cC/+2g==", "signatures": [{"sig": "MEUCIQDsXdEmkyLpRs8eu+a2tOXgI1gDZM/V/7KQHbpH3ROi2QIgEl7HuvBUTX/svSKsEp6dX/qScLsJn+dYtZ7hsbqNYa8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17808, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcwMq7CRA9TVsSAnZWagAAF8cP/0sE8vMdckLMKccxJi2C\nzhwLsr2Yrw8lI6Hnjl9OTqCT0XiyOqYZ5sgvgvy+MZmTDgclvc+cIo0Oq95G\nklQ43n+quUPrv6GjMMB+fUzrm6f1q4Y01qWEgbbdyw2QKi5gEykRPCRCrnvD\nwXOAQ3ueoWKuhhnfP6/gCrZGI1PSnKBVznxjZF47R2fVQZPllXhl6fAAWe9x\ndIF/4Q8Uu37gsXmRQFHEVTtEA7vdxmnJ8jXz4QXHtHpFNrfSvBFX2UH19UJy\nSyy6V8ZDBN5/EWlYMpmzHcqVdbmhNcZnJPKIbrKqR6/OFAfXhpGVztnE5BZM\nZJQQyniEesQhwDPtVIWEktrQ1CSsbNFi/oC083/j9W2l0+jzJCLVwHUik2DT\nr7MN4HXMgPaHoyGXrFOcqER3K/Ma9BQNMcPL23rWtrbKTppdHNFLISxlkPRK\nVoMpNQdBQ+WqPgdvOK1crJM5msenMkltRfMQO+H8hzGlSThp0S9YNoCCJ4UC\nT1K+MZ9F5ttMUWa8LT7ehLeHly1nPMa0sfPZ/PQKmt0sdHTU3NlJH3cBNi6t\n4DTpcGioD4wXZRDFEpJHA6fjOhHn+DJnlQk/J5G4b/Ztb+7N/5ut/RoS+kDp\nPtikKoRdb1+NCn2ncY5FeAmIEbqmBjDXqyqH7aDBB7vGIMswCr6xZ26EWR+8\nydWy\r\n=lP0W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6"}, "gitHead": "90e60cc47833bb780680f916488ca24f0be36ca2", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+https://github.com/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "lerna/3.13.3/node@v12.0.0+x64 (linux)", "description": "test for inclusion or exclusion of paths using pkg-conf and globs", "directories": {}, "_nodeVersion": "12.0.0", "dependencies": {"glob": "^7.1.3", "minimatch": "^3.0.4", "read-pkg-up": "^4.0.0", "require-main-filename": "^2.0.0"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/test-exclude_5.2.3_1556138683061_0.027914894408826996", "host": "s3://npm-registry-packages"}}, "6.0.0-alpha.0": {"name": "test-exclude", "version": "6.0.0-alpha.0", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@6.0.0-alpha.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "0778e2b51de212cd0b58fbb34a3e6f357855dde4", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0-alpha.0.tgz", "fileCount": 9, "integrity": "sha512-FE5W/k1pGBck6UrvxLFAb4/BCqCDaegPnVfzXNf2dfqZcSI9nqRmakF9JXJSLYnvKtJrETL0lygLAlbjOcEzPQ==", "signatures": [{"sig": "MEQCIAZDyBhLlSQJQhbM//eSXzY/CQg70rvthQBOW2IODSahAiA+PvyKmbqHZ9tYlwcD69zGRPoBEpTyk1U/QPYwQVqRHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCifCCRA9TVsSAnZWagAAGxIP/1ZRjI/TeAwvtJ6mHAxk\nM9sEb/5DOUTyfoGVrbTTcPHu6VQl/dlYsXR4Mhy/oUweQRUpPel6jqag5c7h\n1GuEdrs1kQYY8XocXJSkhx27K0nqYYrssL6g97upTmud4zl2nUiOpk3aYHXc\nqpRY/7crxlcZQjvRorLh3boJ/NwOa47YGXZnXo+pTooR6VoxJ3z0AatKNrou\nPqLip3crGyV521maRnWsGqj4N6SO/zmtai5V2DAp6SYzsbxZQeo9xIc71Ll0\no9Dr9tjh10ppOBRrOHpFK5aUoN0yvi+6hXfGyN7aFgQYO8o6fI0g5On/Igah\nWBrjB2ECxTPx9GoAiCi/H7IFoqOz8NclVO0RNP76T5MjoC8YEjOx2KwnI7no\npayx8vvcKY2X28URfsanI8qauQY79ckOagF1XawuXkpbVQcYxIIS5y4BlJa2\nPgNutSilPjsSDCiap5TxHgMfJbeKphom0UcKT6zK/v4/IKKQjzMXv3Ku+09A\nGhoccEG9Wn1TaeLPzvv5rmZ1IHuo2B+r7w2FkyQqKDe0Fh4HKMwT9LubZPVG\nyQYiu1cI+5bB1Tku85XIdV/jLukBqUhyHq1+D4oL1lAaE2ziPBQWBOiwUgjq\n3nWP5ywbNZ998ocJkLGgb3+DSt4fe9dz0XXrNNMEDv8xQ/JhsA/WT/841+7j\n1+Xk\r\n=3B2C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "readme": "# test-exclude\n\nThe file include/exclude logic used by [nyc](https://github.com/istanbuljs/nyc).\n\n[![Build Status](https://travis-ci.org/istanbuljs/test-exclude.svg)](https://travis-ci.org/istanbuljs/test-exclude)\n[![Coverage Status](https://coveralls.io/repos/github/istanbuljs/test-exclude/badge.svg?branch=master)](https://coveralls.io/github/istanbuljs/test-exclude?branch=master)\n[![Standard Version](https://img.shields.io/badge/release-standard%20version-brightgreen.svg)](https://github.com/conventional-changelog/standard-version)\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/test-exclude.svg)](https://greenkeeper.io/)\n\n## Usage\n\n```js\nconst exclude = require('test-exclude');\nif (exclude().shouldInstrument('./foo.js')) {\n    // let's instrument this file for test coverage!\n}\n```\n\n## Including node_modules folder\n\nby default the `node_modules` folder is added to all groups of\nexclude rules. In the rare case that you wish to instrument files\nstored in `node_modules`, a negative glob can be used:\n\n```js\nconst exclude = require('test-exclude');\nconst e = exclude({\n    exclude: ['!**/node_modules/**']\n});\n```\n\n## License\n\nISC\n", "engines": {"node": ">=8"}, "gitHead": "2e885073a9398806c9b8763dd39418398182ca34", "scripts": {"test": "nyc mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+https://github.com/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "lerna/3.15.0/node@v12.3.1+x64 (linux)", "description": "test for inclusion or exclusion of paths using globs", "directories": {}, "_nodeVersion": "12.3.1", "dependencies": {"glob": "^7.1.4", "minimatch": "^3.0.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.1.4"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude_6.0.0-alpha.0_1560946625078_0.08908765035905719", "host": "s3://npm-registry-packages"}}, "6.0.0-alpha.1": {"name": "test-exclude", "version": "6.0.0-alpha.1", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@6.0.0-alpha.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "dist": {"shasum": "56b7123436f693539fde73f23d0b543ec2e21a9a", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0-alpha.1.tgz", "fileCount": 9, "integrity": "sha512-5mYhi<PERSON>+PfxG37MdIfvsj2mbpnC8uwYSHbgJgQE/7OOsUM2QwB0U7Z05SZzQGknevs0Fp+UGJtKayrYLYwC4vMw==", "signatures": [{"sig": "MEUCIBXfwcZ/I9C0wCUj7igKGV+xJlYx06UHzyfGAYAE685TAiEAirWzUaettUk5DLynG20UmUm5O6PwRCzM/K1mblOSInc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdimTJCRA9TVsSAnZWagAAwdUQAI5ZQDzvHAXg6qNfg5c5\niaQc8KaLSOtEVeHbYBHbyykFf69/BBbIHLXYUY4ve4J9wMGjJtoo4yZw7ae0\nXumCQibsW/GslitBvkpkLql/iycJYo9mLaOvj9s5qyKwF3hgINddpqumi+bO\nGnvZM2XfQDh8IiZ91BzEI2L1qEIyI3l2S1fzj2DdJTpR/o/g2Jaj42I/0y6O\nbGWRwr6Uk8AYPKjZK+nYai9x+TwK5/zT26YEmAFWD4DmFTuj3uoh0gD+sQx5\nfr3F/UKeiuyaAdrunrhOhGitYJw1gBSgOEFNtFx1fgmT7Jcn374Vxq0nsa+H\n7kHpqNloois7lni4P19YxWB6LLTDZHGlUG8gKjWmG/tefgty362AaxYCIG4a\nEKpfS2fTWTxrvaJamVDxn4pPU4ERjcNl1ISFBhHHRcU8jV6R/3ZrsGn15/WW\nyvsRacInFQErWPEFRY0mvMQyrc7Kn4X8qym7aUMLtQm5Z9dfJFU95DjUM4Rs\nYRrXQpLIaQJCQK4kCZmgG0moTDcJeoN9nGYFRl62X+0g7h8rDDSEDiIYwD1f\nMYvX6r26TPwCstxtm752WGx6zGoGAQazrn2jbC5faeKjzLUCWGj1R/PR2n39\nP1ZjHoDYNwycAvN6AnmcHOyMq+wP/wU7RAG3XCl98vDKa5WXs8NrZPf902Fy\nsjDF\r\n=tBL0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "readme": "# test-exclude\n\nThe file include/exclude logic used by [nyc](https://github.com/istanbuljs/nyc).\n\n[![Build Status](https://travis-ci.org/istanbuljs/test-exclude.svg)](https://travis-ci.org/istanbuljs/test-exclude)\n[![Coverage Status](https://coveralls.io/repos/github/istanbuljs/test-exclude/badge.svg?branch=master)](https://coveralls.io/github/istanbuljs/test-exclude?branch=master)\n[![Standard Version](https://img.shields.io/badge/release-standard%20version-brightgreen.svg)](https://github.com/conventional-changelog/standard-version)\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/test-exclude.svg)](https://greenkeeper.io/)\n\n## Usage\n\n```js\nconst exclude = require('test-exclude');\nif (exclude().shouldInstrument('./foo.js')) {\n    // let's instrument this file for test coverage!\n}\n```\n\n## Including node_modules folder\n\nby default the `node_modules` folder is added to all groups of\nexclude rules. In the rare case that you wish to instrument files\nstored in `node_modules`, a negative glob can be used:\n\n```js\nconst exclude = require('test-exclude');\nconst e = exclude({\n    exclude: ['!**/node_modules/**']\n});\n```\n\n## License\n\nISC\n", "engines": {"node": ">=8"}, "gitHead": "9d48c0bcb7af4755691c49374fc847d1527ed5c4", "scripts": {"test": "tap", "release": "standard-version"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+https://github.com/istanbuljs/test-exclude.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "test for inclusion or exclusion of paths using globs", "directories": {}, "_nodeVersion": "12.10.0", "dependencies": {"glob": "^7.1.4", "minimatch": "^3.0.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tap": "^14.4.2", "standard-version": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude_6.0.0-alpha.1_1569350856606_0.5205496324749679", "host": "s3://npm-registry-packages"}}, "6.0.0-alpha.2": {"name": "test-exclude", "version": "6.0.0-alpha.2", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@6.0.0-alpha.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "dist": {"shasum": "572b25ac405a0e7ec10de1cbc401db2bb5321039", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0-alpha.2.tgz", "fileCount": 8, "integrity": "sha512-qgV7K2AUuS6IxUBMFoYd+pa1Aq99jMM25SDmPeSfAGbi2DinDlByanxAiYv4ISc/mhtS3/IiI7bYj5DTm9ewVg==", "signatures": [{"sig": "MEUCIQCrRb04JPWnjL2EbyLrIDKOdoiPVpSagN6tzcscLwCzJQIgWU4yJMULGJ8v7HxaYvj9+WPAgnXJHITXz1Cs7GDlQsE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22688, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6wDECRA9TVsSAnZWagAAfcwP/2CYos/IOCk27OAwqRGn\n+yxRDdMQuywnrzzV2g4sUauK+hrWKuC1oN96P+gpKAI2DcP3FGHWgQSMlqgJ\nurfMvgg2a6CfgGNu6OemEIjGTs/gm48DYKfkmIj9fSswGhFsCOEIAWQGmpkY\ne5J7oQcjrDS4v9W0CbxCAzzveDIdP6ItcE6aBahA9QE3G704ouw/wEFQlcpZ\nLmpnviC64w/hsz3A7hdGqcgAJO3mO5A4ixRu8BUwLdjjmb1ITKIZycPji1gi\nKvLiL6XmIP3ebVxIZvUr9q24wmX5VjaAhwSpwA+02Qy8OY5mG6sou8y1ceRE\nln3P3pevGWGDRIQWG86L8tnsG1ZRBBx+I5izfVjKxBtI0V1m139r4mbI23HG\n9IoY+7OCNl2vwXJlsRIuWD84+0NC8ILUnvSMtB+iqyzL5a+sLFuT36KpjGC+\naLAi6NfGwiN2JOGjFN4rvlOycEY02tcQeKGDY7Ye/US6SuujTEqmldBVHMZ/\neFV47KIT3oDSqr7mj42xPf9niGIigUHqQIISFveVEBN//Hx8RgAZBSqUGCLx\n1Ouu9coNg3YP4ytvuotbPSabV3JtX86SlOnHbz5NHkCtM985crF6De/7hHaL\npM9qpWbEOOFdQD81q8cF2sR66WllBVuk9ez85gmcxgv27PheXk5O6aSAnWGA\nXt0+\r\n=G8Kd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "readme": "# test-exclude\n\nThe file include/exclude logic used by [nyc] and [babel-plugin-istanbul].\n\n[![Build Status](https://travis-ci.org/istanbuljs/test-exclude.svg)](https://travis-ci.org/istanbuljs/test-exclude)\n[![Coverage Status](https://coveralls.io/repos/github/istanbuljs/test-exclude/badge.svg?branch=master)](https://coveralls.io/github/istanbuljs/test-exclude?branch=master)\n[![Standard Version](https://img.shields.io/badge/release-standard%20version-brightgreen.svg)](https://github.com/conventional-changelog/standard-version)\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/test-exclude.svg)](https://greenkeeper.io/)\n\n## Usage\n\n```js\nconst TestExclude = require('test-exclude');\nconst exclude = new TestExclude();\nif (exclude().shouldInstrument('./foo.js')) {\n    // let's instrument this file for test coverage!\n}\n```\n\n### TestExclude(options)\n\nThe test-exclude constructor accepts an options object.  The defaults are taken from\n[@istanbuljs/schema].\n\n#### options.cwd\n\nThis is the base directory by which all comparisons are performed.  Files outside `cwd`\nare not included.\n\nDefault: `process.cwd()`\n\n#### options.exclude\n\nArray of path globs to be ignored.  Note this list does not include `node_modules` which\nis added separately.  See [@istanbuljs/schema/default-excludes.js] for default list.\n\n#### options.excludeNodeModules\n\nBy default `node_modules` is excluded.  Setting this option `true` allows `node_modules`\nto be included.\n\n#### options.include\n\nArray of path globs that can be included.  By default this is unrestricted giving a result\nsimilar to `['**']` but more optimized.\n\n#### options.extension\n\nArray of extensions that can be included.  This ensures that nyc only attempts to process\nfiles which it might understand.  Note use of some formats may require adding parser\nplugins to your nyc or babel configuration.\n\nDefault: `['.js', '.cjs', '.mjs', '.ts', '.tsx', '.jsx']`\n\n### TestExclude#shouldInstrument(filename): boolean\n\nTest if `filename` matches the rules of this test-exclude instance.\n\n```js\nconst exclude = new TestExclude();\nexclude.shouldInstrument('index.js'); // true\nexclude.shouldInstrument('test.js'); // false\nexclude.shouldInstrument('README.md'); // false\nexclude.shouldInstrument('node_modules/test-exclude/index.js'); // false\n```\n\nIn this example code:\n* `index.js` is true because it matches the default `options.extension` list\n  and is not part of the default `options.exclude` list.\n* `test.js` is excluded because it matches the default `options.exclude` list.\n* `README.md` is not matched by the default `options.extension`\n* `node_modules/test-exclude/index.js` is excluded because `options.excludeNodeModules`\n  is true by default.\n\n### TestExculde#globSync(cwd = options.cwd): Array[string]\n\nThis synchronously retrieves a list of files within `cwd` which should be instrumented.\nNote that setting `cwd` to a parent of `options.cwd` is ineffective, this argument can\nonly be used to further restrict the result.\n\n### TestExclude#glob(cwd = options.cwd): Promise<Array[string]>\n\nThis function does the same as `TestExclude#globSync` but does so asynchronously.  The\nPromise resolves to an Array of strings.\n\n\n## `test-exclude` for enterprise\n\nAvailable as part of the Tidelift Subscription.\n\nThe maintainers of `test-exclude` and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-test-exclude?utm_source=npm-test-exclude&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n\n[nyc]: https://github.com/istanbuljs/nyc\n[babel-plugin-istanbul]: https://github.com/istanbuljs/babel-plugin-istanbul\n[@istanbuljs/schema]: https://github.com/istanbuljs/schema\n[@istanbuljs/schema/default-excludes.js]: https://github.com/istanbuljs/schema/blob/master/default-exclude.js\n", "engines": {"node": ">=8"}, "gitHead": "131c3b0c80fe98a245392ceb64eb800fd535045c", "scripts": {"snap": "npm test -- --snapshot", "test": "nyc tap", "release": "standard-version"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+https://github.com/istanbuljs/test-exclude.git", "type": "git"}, "_npmVersion": "6.13.2", "description": "test for inclusion or exclusion of paths using globs", "directories": {}, "_nodeVersion": "13.3.0", "dependencies": {"glob": "^7.1.4", "minimatch": "^3.0.4", "@istanbuljs/schema": "^0.1.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^14.1.1", "tap": "=14.10.2-unbundled", "standard-version": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude_6.0.0-alpha.2_1575682243656_0.28036974001722426", "host": "s3://npm-registry-packages"}}, "6.0.0-alhpa.3": {"name": "test-exclude", "version": "6.0.0-alhpa.3", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@6.0.0-alhpa.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "dist": {"shasum": "df1ec8c60dbad094d810020f63f4cea956d40c5e", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0-alhpa.3.tgz", "fileCount": 8, "integrity": "sha512-ICMBlciZxSrgudrLD9f6Z3yYr4Qc/rSUIbcquN0NB5Aw1P0mx6PAbzhiDkrz9D09PX8tmZb162DIJlG5VhA5RQ==", "signatures": [{"sig": "MEUCIGvWtBLuxVDhXD+72Ha1beAW5e1Sw0AfOh3A6XlMs0CqAiEAgvhYXKK671WgnX7yRBkpfp++DhUGVltkB75CjtQPtxk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd7XoECRA9TVsSAnZWagAAj2cQAIA+XYO3L7qKq76zxqkR\nkMd2IUN2kOp+onfyI6m5egdhLiyesCcLS6+3Xv+hq7etptUHDuZKVvPiwpXp\nANM6b9ulSFFZgHv9QQMVf4GfFcS0QEPxkc4z7KaxCnC/JDEU1H48BlM3mW2q\npubV6dYUDKbx9su2HMoK3hKBSm0I0r6hKLlPGYL4e+zPyvD7UboGpZP4Lhti\nAL7zxFMhghQ+jaWrtxLj1nL3zYBNiP1iJJD5aC9Mxuikl4xL6WsDQr8ZLdSW\nrxVnwwwt+Suv998QSBnRsQyrM727L/ZpQNSfBZVrZDgoDMNV+BDKKrvJzT0g\nrp9guqBVT/lW79GwhJvFoqiFGRJ9CvyN5gtYBxHpsGVVnQOSbdzYcehgVOBp\ncMWdP6XeE8M4YfE6EgFzxQGFz4B5Gkn+EN9ZCXbPJJ+BuIX+1mTdRLsCvc+B\n5Ax6Hxk43pvGeLrXYCLNV++mn8YIOA/PpDrRgrSijzHauMyNKw0hXWiiwN33\naT374Pv3umcTSF0VYXE+LacXyzzwy3Sa7cvMf3SxkDQP2BUrVQUN5nJafU1Y\nqt528gdCA5ARkBWN5b7JbdBk4YJFm2nA/+gbIc1iuCa5AdX1RCzXJH9LCryl\nBa6ZKPjirXll1e/Q3i2LhDaiNlGLB3N86uEeC3SxvVbPaO8NZsIlHkZ8GJQC\nezJN\r\n=NI4S\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "readme": "# test-exclude\n\nThe file include/exclude logic used by [nyc] and [babel-plugin-istanbul].\n\n[![Build Status](https://travis-ci.org/istanbuljs/test-exclude.svg)](https://travis-ci.org/istanbuljs/test-exclude)\n[![Coverage Status](https://coveralls.io/repos/github/istanbuljs/test-exclude/badge.svg?branch=master)](https://coveralls.io/github/istanbuljs/test-exclude?branch=master)\n[![Standard Version](https://img.shields.io/badge/release-standard%20version-brightgreen.svg)](https://github.com/conventional-changelog/standard-version)\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/test-exclude.svg)](https://greenkeeper.io/)\n\n## Usage\n\n```js\nconst TestExclude = require('test-exclude');\nconst exclude = new TestExclude();\nif (exclude().shouldInstrument('./foo.js')) {\n    // let's instrument this file for test coverage!\n}\n```\n\n### TestExclude(options)\n\nThe test-exclude constructor accepts an options object.  The defaults are taken from\n[@istanbuljs/schema].\n\n#### options.cwd\n\nThis is the base directory by which all comparisons are performed.  Files outside `cwd`\nare not included.\n\nDefault: `process.cwd()`\n\n#### options.exclude\n\nArray of path globs to be ignored.  Note this list does not include `node_modules` which\nis added separately.  See [@istanbuljs/schema/default-excludes.js] for default list.\n\n#### options.excludeNodeModules\n\nBy default `node_modules` is excluded.  Setting this option `true` allows `node_modules`\nto be included.\n\n#### options.include\n\nArray of path globs that can be included.  By default this is unrestricted giving a result\nsimilar to `['**']` but more optimized.\n\n#### options.extension\n\nArray of extensions that can be included.  This ensures that nyc only attempts to process\nfiles which it might understand.  Note use of some formats may require adding parser\nplugins to your nyc or babel configuration.\n\nDefault: `['.js', '.cjs', '.mjs', '.ts', '.tsx', '.jsx']`\n\n### TestExclude#shouldInstrument(filename): boolean\n\nTest if `filename` matches the rules of this test-exclude instance.\n\n```js\nconst exclude = new TestExclude();\nexclude.shouldInstrument('index.js'); // true\nexclude.shouldInstrument('test.js'); // false\nexclude.shouldInstrument('README.md'); // false\nexclude.shouldInstrument('node_modules/test-exclude/index.js'); // false\n```\n\nIn this example code:\n* `index.js` is true because it matches the default `options.extension` list\n  and is not part of the default `options.exclude` list.\n* `test.js` is excluded because it matches the default `options.exclude` list.\n* `README.md` is not matched by the default `options.extension`\n* `node_modules/test-exclude/index.js` is excluded because `options.excludeNodeModules`\n  is true by default.\n\n### TestExculde#globSync(cwd = options.cwd): Array[string]\n\nThis synchronously retrieves a list of files within `cwd` which should be instrumented.\nNote that setting `cwd` to a parent of `options.cwd` is ineffective, this argument can\nonly be used to further restrict the result.\n\n### TestExclude#glob(cwd = options.cwd): Promise<Array[string]>\n\nThis function does the same as `TestExclude#globSync` but does so asynchronously.  The\nPromise resolves to an Array of strings.\n\n\n## `test-exclude` for enterprise\n\nAvailable as part of the Tidelift Subscription.\n\nThe maintainers of `test-exclude` and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-test-exclude?utm_source=npm-test-exclude&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n\n[nyc]: https://github.com/istanbuljs/nyc\n[babel-plugin-istanbul]: https://github.com/istanbuljs/babel-plugin-istanbul\n[@istanbuljs/schema]: https://github.com/istanbuljs/schema\n[@istanbuljs/schema/default-excludes.js]: https://github.com/istanbuljs/schema/blob/master/default-exclude.js\n", "engines": {"node": ">=8"}, "gitHead": "332d7cc57f431676a310c6352b34c7ed3079bb74", "scripts": {"snap": "npm test -- --snapshot", "test": "nyc tap", "release": "standard-version"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+https://github.com/istanbuljs/test-exclude.git", "type": "git"}, "_npmVersion": "6.13.2", "description": "test for inclusion or exclusion of paths using globs", "directories": {}, "_nodeVersion": "13.3.0", "dependencies": {"glob": "^7.1.4", "minimatch": "^3.0.4", "@istanbuljs/schema": "^0.1.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^14.1.1", "tap": "=14.10.2-unbundled", "standard-version": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude_6.0.0-alhpa.3_1575844356297_0.14689960653693523", "host": "s3://npm-registry-packages"}}, "6.0.0-alpha.3": {"name": "test-exclude", "version": "6.0.0-alpha.3", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@6.0.0-alpha.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "dist": {"shasum": "eb43b20c428f876f548df01e18b704cec5b56545", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0-alpha.3.tgz", "fileCount": 8, "integrity": "sha512-Nh+r+FxQJ7QZ0CoNFkhD10SI740GIg54NcBF4/4PdGJht12wqFWbSWCOynvM3aTyQjzkWCqhQ1XT1HJe1t8LUQ==", "signatures": [{"sig": "MEYCIQCBHS7YGNtosOZPovp6IypT4liZHqqWhEIA4DMYWBxgSwIhAM2J+/rhG9PmcpQx1fwr1a3PUleDG44xWpEKQbC4O59I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd7Zd2CRA9TVsSAnZWagAAtBMQAImlYFksDIHA6r0M+Vqo\nfXyS5OWQn+wZu6EfPiG6SdBB2eeCF+Jvougwuh7Wxrv4Ct2jpPa49Q6HQGq+\nFAVZJqqke0MsJ5Tw3zwzlDl4kleiKmKseIQDob6Z3bL1v6rX1tNSgGE6x7YY\nX4BWGyWBad44CZ+RBOwtLZMvQL17r2lB0WCCjYcPhz0krAiKP20mlK4OOdhB\nxEzeijeVTZZ+/TEJDAvB5pn34DYz8UkNKy/MxdXKGd6HTog2w3yFP3h/VwsF\n+MEuVNmlU/dRtYcqmxwyGfU0d7Fh1EVFAjCB5LeqvzGSDE7ix8ez4XFsR9AV\nNkLNOCdXi2aQEydIMqM7a2Q3ms8neVVeYhhJHxYtDOOZzVtlQOvMsJLjKteo\nxoZ2nqU0s4OHjS7Nwf0Gs8UC0Ru6+3661VvJnVFmXWN7NY6u1jC/Z/rMp2HR\nBaGYAYWIIezyjLB3tKkhXNsI4X7MLNQaeDHQDsae8ubz99U8Sba9vphInbqg\nqc66r9vnmSF/6rTbbbBA5W4wZm8sk98zSQI009Bsc1hVdksQqgwOfrOBf0Ya\nWe/sbZu1MTHQpxcA7ynFMP8t0lQU7XWhLO+zItV2tExPbsmwcj35baoG4Pzv\ntUwEbwtizqBer9DwtvJBNo0pRZ6eg64Fl8YgtV2i/1+G+rKaA0mWWCvnnwk2\nR7jC\r\n=w81L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "readme": "# test-exclude\n\nThe file include/exclude logic used by [nyc] and [babel-plugin-istanbul].\n\n[![Build Status](https://travis-ci.org/istanbuljs/test-exclude.svg)](https://travis-ci.org/istanbuljs/test-exclude)\n[![Coverage Status](https://coveralls.io/repos/github/istanbuljs/test-exclude/badge.svg?branch=master)](https://coveralls.io/github/istanbuljs/test-exclude?branch=master)\n[![Standard Version](https://img.shields.io/badge/release-standard%20version-brightgreen.svg)](https://github.com/conventional-changelog/standard-version)\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/test-exclude.svg)](https://greenkeeper.io/)\n\n## Usage\n\n```js\nconst TestExclude = require('test-exclude');\nconst exclude = new TestExclude();\nif (exclude().shouldInstrument('./foo.js')) {\n    // let's instrument this file for test coverage!\n}\n```\n\n### TestExclude(options)\n\nThe test-exclude constructor accepts an options object.  The defaults are taken from\n[@istanbuljs/schema].\n\n#### options.cwd\n\nThis is the base directory by which all comparisons are performed.  Files outside `cwd`\nare not included.\n\nDefault: `process.cwd()`\n\n#### options.exclude\n\nArray of path globs to be ignored.  Note this list does not include `node_modules` which\nis added separately.  See [@istanbuljs/schema/default-excludes.js] for default list.\n\n#### options.excludeNodeModules\n\nBy default `node_modules` is excluded.  Setting this option `true` allows `node_modules`\nto be included.\n\n#### options.include\n\nArray of path globs that can be included.  By default this is unrestricted giving a result\nsimilar to `['**']` but more optimized.\n\n#### options.extension\n\nArray of extensions that can be included.  This ensures that nyc only attempts to process\nfiles which it might understand.  Note use of some formats may require adding parser\nplugins to your nyc or babel configuration.\n\nDefault: `['.js', '.cjs', '.mjs', '.ts', '.tsx', '.jsx']`\n\n### TestExclude#shouldInstrument(filename): boolean\n\nTest if `filename` matches the rules of this test-exclude instance.\n\n```js\nconst exclude = new TestExclude();\nexclude.shouldInstrument('index.js'); // true\nexclude.shouldInstrument('test.js'); // false\nexclude.shouldInstrument('README.md'); // false\nexclude.shouldInstrument('node_modules/test-exclude/index.js'); // false\n```\n\nIn this example code:\n* `index.js` is true because it matches the default `options.extension` list\n  and is not part of the default `options.exclude` list.\n* `test.js` is excluded because it matches the default `options.exclude` list.\n* `README.md` is not matched by the default `options.extension`\n* `node_modules/test-exclude/index.js` is excluded because `options.excludeNodeModules`\n  is true by default.\n\n### TestExculde#globSync(cwd = options.cwd): Array[string]\n\nThis synchronously retrieves a list of files within `cwd` which should be instrumented.\nNote that setting `cwd` to a parent of `options.cwd` is ineffective, this argument can\nonly be used to further restrict the result.\n\n### TestExclude#glob(cwd = options.cwd): Promise<Array[string]>\n\nThis function does the same as `TestExclude#globSync` but does so asynchronously.  The\nPromise resolves to an Array of strings.\n\n\n## `test-exclude` for enterprise\n\nAvailable as part of the Tidelift Subscription.\n\nThe maintainers of `test-exclude` and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-test-exclude?utm_source=npm-test-exclude&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n\n[nyc]: https://github.com/istanbuljs/nyc\n[babel-plugin-istanbul]: https://github.com/istanbuljs/babel-plugin-istanbul\n[@istanbuljs/schema]: https://github.com/istanbuljs/schema\n[@istanbuljs/schema/default-excludes.js]: https://github.com/istanbuljs/schema/blob/master/default-exclude.js\n", "engines": {"node": ">=8"}, "gitHead": "16047de7871a01cfddd07682d67abbc94c4b4aa5", "scripts": {"snap": "npm test -- --snapshot", "test": "nyc tap", "release": "standard-version"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+https://github.com/istanbuljs/test-exclude.git", "type": "git"}, "_npmVersion": "6.13.2", "description": "test for inclusion or exclusion of paths using globs", "directories": {}, "_nodeVersion": "13.3.0", "dependencies": {"glob": "^7.1.4", "minimatch": "^3.0.4", "@istanbuljs/schema": "^0.1.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^14.1.1", "tap": "=14.10.2-unbundled", "standard-version": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude_6.0.0-alpha.3_1575851894396_0.7229464759769202", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "test-exclude", "version": "6.0.0", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@6.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "dist": {"shasum": "04a8698661d805ea6fa293b6cb9e63ac044ef15e", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz", "fileCount": 8, "integrity": "sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w==", "signatures": [{"sig": "MEUCIB0LVakQpTMqa9NH6Z2xvUUdcM0X3CsYQhOn+U1iWhqtAiEAm74Zm9tVY9oo9y4IjiVhZ4VjQTEPEhXMCVjnBS5bhzQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23612, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd/TI9CRA9TVsSAnZWagAAn5UP/3cmrOZNy1L4F3+mm8AL\nvXq3hdOKcfheF9Tv6fgRc+/GEUNPewDn4Lqoo6NfGjKcxwj+pnuXeiVliYbE\nTWqAr8SD37Kck38/Lli9ml03Pzf4emzbSJt8pcljBchrDGI6+UQvW5avxjCX\nmRLVumcB0mnOO3kgrLAShfHHkTfLCqfAici76YZNMbFVaDqy92IntFDwNFYu\nAiG5+AOAB6x+yNPgwojqZZj4C8ixgVC5KKR4cEQJh/Bs/rbVZ5YOON6rvN6c\nuQzC3NA0hzbrq6plqmi3Lq/NVmwjb4dQVnnlaGxWYfy1Lxim2KsV+MC0a3k5\nLkvz50K5+KK5bSJ0T3hzj/3qQfgNygobK+uM2YYmhhUmUrzmJY6eWpT2+Qbh\nb/3pCujfmmwZzZfwYhdD7fwqPIPuU8XtGNXqV2136nNoaoXJQa+TWGw6klNX\nUqA+Tyq9KQZuWIS9DMnTHBjKBja5fjwJJNBa5412MidYYw45LYZm4QaV0jm0\nCMXFD8rWkmGB6STYCIP49fS1Oik5MVaq0LQW8pNk6P4Wf7C0ZZG0arP0Teuz\nu5SGUir0X2VXPW7qHerC1jGnaxuxxjUGseB1r1LckzaEcK5MjzTo9vO9dCEC\nfGT5iptC+AUha+/IPLFmWc9ffSdmehObb24eS7CgJdsB4PeD+sVvKp6oNfcK\ndbqy\r\n=CT7g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=8"}, "gitHead": "b788e7bd7cfdec8b471ad7b79d0d4a44975e2e99", "scripts": {"snap": "npm test -- --snapshot", "test": "nyc tap", "release": "standard-version"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+https://github.com/istanbuljs/test-exclude.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "test for inclusion or exclusion of paths using globs", "directories": {}, "_nodeVersion": "13.4.0", "dependencies": {"glob": "^7.1.4", "minimatch": "^3.0.4", "@istanbuljs/schema": "^0.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0-beta.3", "tap": "^14.10.5", "standard-version": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude_6.0.0_1576874556848_0.7655720481021979", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "test-exclude", "version": "7.0.0", "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "test-exclude@7.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "dist": {"shasum": "c7beb21bc460f76dc8d6c6ac5da8ff56bbc57fbd", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-7.0.0.tgz", "fileCount": 7, "integrity": "sha512-1oYdK3NmIfAXiCAgOaFrwOucQDCgkCriHMpihvaCDTf65UK3w5jja72JxyJ9AMO+jPsxXxO7ZBXVoyHzpanKMQ==", "signatures": [{"sig": "MEUCIQCu/Ta2WFSx+NqTFsOeeTNvPlWYfQLkvreDvr01YuXy/gIgaQXToCLGNswpM5O1k7VQi3HRR/jLL2gWkg/qXhmo58Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11406}, "main": "index.js", "engines": {"node": ">=18"}, "gitHead": "a1955c5456d7fbe956f5765a4a6908fd1287119f", "scripts": {"snap": "npm test -- --snapshot", "test": "c8 tap", "release": "standard-version"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/istanbuljs/test-exclude.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "test for inclusion or exclusion of paths using globs", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"glob": "^10.4.1", "minimatch": "^9.0.4", "@istanbuljs/schema": "^0.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "tap": "^19.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/test-exclude_7.0.0_1717977714009_0.19568930544964824", "host": "s3://npm-registry-packages"}}, "7.0.1": {"name": "test-exclude", "version": "7.0.1", "description": "test for inclusion or exclusion of paths using globs", "main": "index.js", "scripts": {"release": "standard-version", "test": "c8 tap", "snap": "npm test -- --snapshot"}, "repository": {"type": "git", "url": "git+https://github.com/istanbuljs/test-exclude.git"}, "keywords": ["exclude", "include", "glob", "package", "config"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "homepage": "https://istanbul.js.org/", "dependencies": {"@istanbuljs/schema": "^0.1.2", "glob": "^10.4.1", "minimatch": "^9.0.4"}, "devDependencies": {"c8": "^9.1.0", "tap": "^19.2.2"}, "engines": {"node": ">=18"}, "_id": "test-exclude@7.0.1", "gitHead": "4f7f0a27e4adf43d325c98608331fdec441640d2", "_nodeVersion": "20.11.1", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-pFYqmTw68LXVjeWJMST4+borgQP2AyMNbg1BpZh9LbyhUeNkeaPF9gzfPGUAnSMV3qPYdWUwDIjjCLiSDOl7vg==", "shasum": "20b3ba4906ac20994e275bbcafd68d510264c2a2", "tarball": "https://registry.npmjs.org/test-exclude/-/test-exclude-7.0.1.tgz", "fileCount": 7, "unpackedSize": 11460, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDD6mOzePw/9ZGhNRb5qMx8WfH4ksIW/QZkDaJhuumPzAiAdKrBgChNJmFpPpgxaaD2L/NnxF2u18P9ulBwcnnEJsQ=="}]}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/test-exclude_7.0.1_1718031407365_0.4740338883339412"}, "_hasShrinkwrap": false}}, "time": {"created": "2016-06-06T05:56:20.333Z", "modified": "2024-06-10T14:56:47.679Z", "1.0.0": "2016-06-06T05:56:20.333Z", "1.1.0": "2016-06-08T06:20:33.455Z", "2.0.0": "2016-08-12T04:19:13.720Z", "2.1.0": "2016-08-12T04:37:59.503Z", "2.1.1": "2016-08-12T05:02:10.625Z", "2.1.2": "2016-08-31T16:18:09.793Z", "2.1.3": "2016-09-30T21:27:43.998Z", "3.0.0": "2016-11-13T00:17:57.690Z", "3.1.0": "2016-11-14T20:34:16.949Z", "3.2.0": "2016-11-14T21:18:09.014Z", "3.2.1": "2016-11-14T22:37:08.099Z", "3.2.2": "2016-11-14T22:52:37.504Z", "3.3.0": "2016-11-22T06:53:23.257Z", "4.0.0": "2017-01-22T09:54:28.842Z", "4.0.1": "2017-03-20T03:53:23.625Z", "4.0.2": "2017-03-21T06:15:17.619Z", "4.0.3": "2017-03-21T06:26:44.668Z", "4.1.0": "2017-04-29T05:00:06.995Z", "4.1.1": "2017-05-27T21:12:59.415Z", "4.2.0": "2018-02-13T05:48:38.879Z", "4.2.1": "2018-03-04T18:42:56.056Z", "4.2.2": "2018-06-06T00:50:07.588Z", "5.0.0": "2018-06-26T05:35:30.064Z", "4.2.3": "2018-09-05T22:28:24.487Z", "5.0.1": "2018-12-25T00:37:47.493Z", "5.1.0": "2019-01-26T02:26:30.976Z", "5.2.0": "2019-03-12T01:14:18.890Z", "5.2.1": "2019-04-03T18:04:49.820Z", "5.2.2": "2019-04-09T23:21:16.553Z", "5.2.3": "2019-04-24T20:44:43.179Z", "6.0.0-alpha.0": "2019-06-19T12:17:05.189Z", "6.0.0-alpha.1": "2019-09-24T18:47:36.721Z", "6.0.0-alpha.2": "2019-12-07T01:30:43.916Z", "6.0.0-alhpa.3": "2019-12-08T22:32:36.412Z", "6.0.0-alpha.3": "2019-12-09T00:38:14.720Z", "6.0.0": "2019-12-20T20:42:37.006Z", "7.0.0": "2024-06-10T00:01:54.173Z", "7.0.1": "2024-06-10T14:56:47.504Z"}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/istanbuljs/test-exclude.git"}, "keywords": ["exclude", "include", "glob", "package", "config"], "license": "ISC", "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/test-exclude/issues"}, "readme": "# test-exclude\n\nThe file include/exclude logic used by [nyc] and [babel-plugin-istanbul].\n\n[![Build Status](https://travis-ci.org/istanbuljs/test-exclude.svg)](https://travis-ci.org/istanbuljs/test-exclude)\n[![Coverage Status](https://coveralls.io/repos/github/istanbuljs/test-exclude/badge.svg?branch=master)](https://coveralls.io/github/istanbuljs/test-exclude?branch=master)\n[![Standard Version](https://img.shields.io/badge/release-standard%20version-brightgreen.svg)](https://github.com/conventional-changelog/standard-version)\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/test-exclude.svg)](https://greenkeeper.io/)\n\n## Usage\n\n```js\nconst TestExclude = require('test-exclude');\nconst exclude = new TestExclude();\nif (exclude().shouldInstrument('./foo.js')) {\n    // let's instrument this file for test coverage!\n}\n```\n\n### TestExclude(options)\n\nThe test-exclude constructor accepts an options object.  The defaults are taken from\n[@istanbuljs/schema].\n\n#### options.cwd\n\nThis is the base directory by which all comparisons are performed.  Files outside `cwd`\nare not included.\n\nDefault: `process.cwd()`\n\n#### options.exclude\n\nArray of path globs to be ignored.  Note this list does not include `node_modules` which\nis added separately.  See [@istanbuljs/schema/default-excludes.js] for default list.\n\n#### options.excludeNodeModules\n\nBy default `node_modules` is excluded.  Setting this option `true` allows `node_modules`\nto be included.\n\n#### options.include\n\nArray of path globs that can be included.  By default this is unrestricted giving a result\nsimilar to `['**']` but more optimized.\n\n#### options.extension\n\nArray of extensions that can be included.  This ensures that nyc only attempts to process\nfiles which it might understand.  Note use of some formats may require adding parser\nplugins to your nyc or babel configuration.\n\nDefault: `['.js', '.cjs', '.mjs', '.ts', '.tsx', '.jsx']`\n\n### TestExclude#shouldInstrument(filename): boolean\n\nTest if `filename` matches the rules of this test-exclude instance.\n\n```js\nconst exclude = new TestExclude();\nexclude.shouldInstrument('index.js'); // true\nexclude.shouldInstrument('test.js'); // false\nexclude.shouldInstrument('README.md'); // false\nexclude.shouldInstrument('node_modules/test-exclude/index.js'); // false\n```\n\nIn this example code:\n* `index.js` is true because it matches the default `options.extension` list\n  and is not part of the default `options.exclude` list.\n* `test.js` is excluded because it matches the default `options.exclude` list.\n* `README.md` is not matched by the default `options.extension`\n* `node_modules/test-exclude/index.js` is excluded because `options.excludeNodeModules`\n  is true by default.\n\n### TestExculde#globSync(cwd = options.cwd): Array[string]\n\nThis synchronously retrieves a list of files within `cwd` which should be instrumented.\nNote that setting `cwd` to a parent of `options.cwd` is ineffective, this argument can\nonly be used to further restrict the result.\n\n### TestExclude#glob(cwd = options.cwd): Promise<Array[string]>\n\nThis function does the same as `TestExclude#globSync` but does so asynchronously.  The\nPromise resolves to an Array of strings.\n\n\n## `test-exclude` for enterprise\n\nAvailable as part of the Tidelift Subscription.\n\nThe maintainers of `test-exclude` and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-test-exclude?utm_source=npm-test-exclude&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n\n[nyc]: https://github.com/istanbuljs/nyc\n[babel-plugin-istanbul]: https://github.com/istanbuljs/babel-plugin-istanbul\n[@istanbuljs/schema]: https://github.com/istanbuljs/schema\n[@istanbuljs/schema/default-excludes.js]: https://github.com/istanbuljs/schema/blob/master/default-exclude.js\n", "readmeFilename": "README.md", "users": {"daizch": true, "gurunate": true}}