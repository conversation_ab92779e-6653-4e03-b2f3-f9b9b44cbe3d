{"_id": "@babel/plugin-syntax-top-level-await", "_rev": "15-5a50319adf457cf128b8c7f8f78a39e8", "name": "@babel/plugin-syntax-top-level-await", "dist-tags": {"latest": "7.14.5"}, "versions": {"7.7.0": {"name": "@babel/plugin-syntax-top-level-await", "version": "7.7.0", "description": "Allow parsing of top-level await in modules", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-top-level-await"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.0"}, "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_id": "@babel/plugin-syntax-top-level-await@7.7.0", "_nodeVersion": "13.0.1", "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "dist": {"integrity": "sha512-hi8FUNiFIY1fnUI2n1ViB1DR0R4QeK4iHcTlW6aJkrPoTdb8Rf1EMQ6GT3f67DDkYyWgew9DFoOZ6gOoEsdzTA==", "shasum": "f5699549f50bbe8d12b1843a4e82f0a37bb65f4d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.7.0.tgz", "fileCount": 4, "unpackedSize": 2573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVSZCRA9TVsSAnZWagAA5RAP/3iBEQ67Ac8bDtVhAKOI\nvu2cr+9W5asXADZ1AvN57iG6XTwMEtYBQnCe6BBvllmY5zJF81mrgwAPSbBj\nnauH/eCkpHqXVnqX0L/sV0qX7pRkgM70u8BlB/oXPcS65Duu4Let1rjQEOp/\nWcq4ZTdPwm9vuA8cajeNIhIQANhyZKxdTgWYFKjhQqusUJwm5bNUsKvFfBJ/\nSl0bf0zTr4mHFhoqphk5aPrtDcGEYrylNMe6uZqMFg/VvYLZA5DAEJ8k2xCv\nMb/n76LazU9m7sZ11b8yUFEfHWdkxOeqxyEe9AfpAYURlqb5tmG9CuJ8VAQb\nD6BxRPKFuPds19vZgUJgXvAAecxuA9T9j3E9txN2bDNhrebTFnxlceK7Yo3G\nUi1ZOSarEqAyETwdePpQS4KHqHMI736E+3JN6/4nCUAiSRH2pQw4iei8xO6t\njjkxUrHLbrJwWknZ4rsm2JrkUMSlCSLCOhXjWFEXlyfkjE8o90NLHBU4QZFW\nCQkiqLdq844fxBB6ggL/rNPeCOKfhlAYIjw4q4STnKhe3HTjhOKUWk4SJ1La\npxmkVFuem5bn3jnSYkyGy+q15i/sshXdMJgsUqZCnDmm3dtW0OXDgv/QCnjl\nULfRTZOoWhRgAO/w3peUxifaNsuzq10BIKwbiD+m2F7e+gy9lRrgRmJbMEIC\nViqe\r\n=SGEY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBb8UNrkmLraqE/oI/9CY3M8Qz1izIUCZZv6/KzR4g2iAiEA5NBcJrS1t2hu8dGjMpDCKFLGqPHan5Hi8KilrirBUTw="}]}, "maintainers": [{"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-top-level-await_7.7.0_1572951192640_0.7573864120197906"}, "_hasShrinkwrap": false}, "7.7.4": {"name": "@babel/plugin-syntax-top-level-await", "version": "7.7.4", "description": "Allow parsing of top-level await in modules", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-top-level-await"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_id": "@babel/plugin-syntax-top-level-await@7.7.4", "_nodeVersion": "13.1.0", "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "dist": {"integrity": "sha512-wdsOw0MvkL1UIgiQ/IFr3ETcfv1xb8RMM0H9wbiDyLaJFyiDg5oZvDLCXosIXmFeIlweML5iOBXAkqddkYNizg==", "shasum": "bd7d8fa7b9fee793a36e4027fd6dd1aa32f946da", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.7.4.tgz", "fileCount": 4, "unpackedSize": 2573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/0CRA9TVsSAnZWagAAnyMQAJS3CNRpGe9LbAczoNv4\nXOtsG9ATf+BlkpGvnFr1YH2T9ZMmHJL+6DJ8BPq6+rmkQo3QPL/uPk99KWpC\n4tLsXe/Lq3xRTvsxdlHXIyFXHp48pJEVi2BpLY2i2XfxbyqH8M1b4UdgaUML\nGcPZac7nxusTJXZV6SLMfnmfIFaAVBZvSE84sK1V0/CUVtazWRP/eyEV5RXX\nrkcmg9O27gOrg0jY4oaKDwKMxfYosByTkd92oY8bvnXnIwBkvyrGF/l7WHnP\nF9aoXu7hBXwRido21HGKJP8zNf63qScc4EgU+CStNFAFcuu8E0t4Z7W0LxBp\npe8TTVng1OFEoxajPWIwxD5z8ppj4RRZh4J/8jxFgr15UZ1i0fx+RGkdLRpb\nKwOwcRFaouPRlonanmkPUx7hjLQJWPZiu1z+GK2ya/uqit0SbNFSyVVpMPzd\nI+7TQFF8eChj+czh0jBNgi6jGINqWn7THhZVVL9PWyWT3jsoyhM56eOpxc10\ng0GNRS92k4B2X6kC25ffF1+8vDHszBAI/RwRjGjWZXPBc+G3w+WHJskPRL7g\n7Q/np2vBO50ZXPyZubE6e1H1n6ZvVnmPtEBrB4cojBnHn5/Au8UNCiKkBZXW\nZBx71ioGCTe7B5XZ4zC8f8IxWf4OhjeBHvbefZJtkBm1AaPFPpNY3PDjNpuA\nVAHJ\r\n=z4j9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDE/r23st2exMhY1pIYCBr/IfwMgSs3cT9YBIw4WcEkIAiEArc7rlo5Hr1djikxU2tyHGNKcFodwX2efR3qxhICiuYw="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-top-level-await_7.7.4_1574465524475_0.009674089701156907"}, "_hasShrinkwrap": false}, "7.8.0": {"name": "@babel/plugin-syntax-top-level-await", "version": "7.8.0", "description": "Allow parsing of top-level await in modules", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-top-level-await"}, "license": "MIT", "type": "commonjs", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0"}, "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_id": "@babel/plugin-syntax-top-level-await@7.8.0", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"integrity": "sha512-iXR/Cw32fMfWlD1sK2zD/nXtuLStkalRv+xee6VrX84CFrn2LKwb/EOs/4UaDNUpUsws8YZYKeQjPagacFquug==", "shasum": "8d45e3d68a1e26bce79c51b08dd9126290686207", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.8.0.tgz", "fileCount": 4, "unpackedSize": 2595, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVYCRA9TVsSAnZWagAAK+wQAJayQnFfrDALWeFRG7vp\nPoVlHh2ByET3CqKKJUzfVGClfOt6Id+xQE+nEFDlKBbH4mS/2Zs5aFD/MOAA\nu9oKpS+uGIfwqZ1OKZmM2rWDcDyk/8xKrJq7x2/daMR3ULgOdwHfaAYORvq/\n6TqbeCdkalZwLk0qmLVngAvb2M1FMwXUYgUzMDoIdXNxmyfZM+y7hv2kpbDv\nmoz33gEQ0jlUZLGK9ucfKI5vAXQqy23Jm/4PLPGuwuYIZsB/N2g1PuFWR7KX\nNSaDT8GCSi2EgUIWhOkw/db+SaMDpOeZzMXf6coQ6XWMOfFSBvt6wL6MVjRj\nAdO+FnHJHob4FmfgJRZUL194LDMp+wEeNbVKYeTz+C4EZGnoNMf8U4spPMNV\nRObse7b8/MtQT6zOgmcsvLxYjrHQpePA9qcqidufORmbGIgTIRDFrSZsf92U\nZnc56y9jkcLwasb2CQDONIDMegW730jWCoKVNzBlYrll6GWGgMcL3695eqNZ\nqbiyxQ5xy4uKuYcu1jrOjGda5jOT+ZSQi31dsXVVeJsxuCScHnLAOpFrDOuw\nEzerjmtX0zJ5NJvI7+hpZNfUWrQvkNxuHcURvFVcblKuFmB2pFpqTMNkd08Q\n/dd5dXDSCcBV47TG1snIVNeyVZRHT58YdBZtqfxMCnYnTG3jVkvMW9y2n83p\nxcMK\r\n=j90p\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBDIRtn/Lc6aG9SBJbfvcuW6yLep9SUsWpLPHuYercuWAiAe0PPePhIHeAgBCgv3iEHtjEFPblSQeOVw48Lw5ZRV/g=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-top-level-await_7.8.0_1578788183856_0.8978083585955379"}, "_hasShrinkwrap": false}, "7.8.3": {"name": "@babel/plugin-syntax-top-level-await", "version": "7.8.3", "description": "Allow parsing of top-level await in modules", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-top-level-await"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_id": "@babel/plugin-syntax-top-level-await@7.8.3", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"integrity": "sha512-kwj1j9lL/6Wd0hROD3b/OZZ7MSrZLqqn9RAZ5+cYYsflQ9HZBIKCUkr3+uL1MEJ1NePiUbf98jjiMQSv0NMR9g==", "shasum": "3acdece695e6b13aaf57fc291d1a800950c71391", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.8.3.tgz", "fileCount": 4, "unpackedSize": 2573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQLCRA9TVsSAnZWagAAWW8P+wYrd+02yugcnsqp6UdY\ncZVVcPycv3HHLXlmZReDLUHIK1zuIbK6MbQXGCBDSVhu+fWCtiQz8shafdal\n0juWTkB+wcwcQ+hJMz06q3FvbXxZAiZ6FkcMKEzv9QNNLJAhdKqcRJXFr2J6\nmAP2xF+Rq3ZnrTm9yENFTOQBbkaqAIiWz7Lt41RT35nKEegladvcEj8hwQO+\nnyGViHWPes47T9woXuQW07bzJBF4P0d3tVuGh2RaJFsGSXwZYj71iTq822gS\nRRk0caLVNlSb6UKFqVlvWGslQpwEGoosuQBfs5uq0vx/i0NPeYhxLcfeoiBC\nQ62soDtaWAiNU+v6QZACiPfJgKdtTRI/nxh3APJjTtLOWAM6ot+uvoowjftC\nq+WmsIZBrwoXVyFGRaSDcW6iMo0wZPh84yB4UVzCJJuc/ahEVICYjAWlWmEb\nMT3Qcr6fujvLXtY2JzKZjvm/hOrHYK7tWHT5nPnGgylDpf3OSTtWOmxalivY\norf6xsQRRk6Ueui5655fg6J6v2BHgLURPqGR/ygv+lg1p110TYNgB6VxLw2P\nWXtiiToWIf6azcVc4YbSdqOafK831kZwKXW2DS7FiXrCB3mcEQpl3XIEzts5\nobefwFCFgLA6bWV0FQimm7cC2DQfDiim2pNUSBjR0e6W1VSL1rgWOSGFUUp0\nIfFf\r\n=OwgZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAzfjMU8LtGf+JlUlquFllnemjC8zZIGXFgDVYjca2sxAiEAyodqZNePEAY/PeRIPZtSLgKVtO5iH+Gy2lnAOsOm/5M="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-top-level-await_7.8.3_1578951691109_0.4390098536956253"}, "_hasShrinkwrap": false}, "7.10.1": {"name": "@babel/plugin-syntax-top-level-await", "version": "7.10.1", "description": "Allow parsing of top-level await in modules", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-top-level-await"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.1"}, "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/plugin-syntax-top-level-await@7.10.1", "_nodeVersion": "12.16.3", "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "dist": {"integrity": "sha512-hgA5RYkmZm8FTFT3yu2N9Bx7yVVOKYT6yEdXXo6j2JTm0wNxgqaGeQVaSHRjhfnQbX91DtjFB6McRFSlcJH3xQ==", "shasum": "8b8733f8c57397b3eaa47ddba8841586dcaef362", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.10.1.tgz", "fileCount": 4, "unpackedSize": 2624, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSXCRA9TVsSAnZWagAAfiMP/RvsJh6CJEmxyR3PNJp6\nqKMF0APdzOxdcKZizR2j2lreWhLZ3lhnnaixZvIFn9+TnJR7BOcx0pBY+yAO\nEEwKGYlRtK/wllRDoks1nsg++630omd/GCOlgaIKOPqTWHi3glpBOpeVUmyZ\nbwFtvZ0zEG4bsp7T0R+hu7dxaD0HgvgAY1NDxAhFCZ03SG6TIdv9b8gAWLUI\nNvH0PvYOe+Nx6/euma+N65w1XXLdJnikLvcGGnNtjNtD/xSLc9bfs2dAhMNL\nbHRH6G4qIyg/JRyI1WbIMSmpWSYZEtFf6A06ObXSfdvmvLSEvREJGX8V3p4o\n/RsB2l480jlp+0JL6bIfGenqYzyBRmTXffS8ieOd3Ka1ghrWGs0yl38vaAHL\n50cLxml9VmWnULCKfBkaZ/e946sCUVdmd0o1AIBLqYRVcdJ/bcqRDihYJSJc\nDUtLfllzTv0A0nswGGQOwCzor69opA1yuuk6gtFWVPN4VoXOh44IyIxLzeHo\nT0/LnVoJxVkskrZJb2vRNGwvNncRQDU+0bMQSXYzDyEaT5FF2pKHEFCZLBpQ\n0S4zr6bXY7VKU1hXhUfF7K4WxPUlGoXDyu8R0Sc0bmaNJddiPUDsYNXEOtRH\nGR3XFyso9AfMFoWSLzDpokY/1sQd0Pfn/9jdv93OHlxouRdtK5twtpnTJPAt\njVUd\r\n=e3jZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCTLpBhZvwmI4vUoUDEMqhWSj1OIYQZTcUmLQREFqUwXAIhAP4nbBmxeugruc2duP4BrIYLasBsjlBZcwMIprRtfavv"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-top-level-await_7.10.1_1590617238963_0.6952949724660364"}, "_hasShrinkwrap": false}, "7.10.4": {"name": "@babel/plugin-syntax-top-level-await", "version": "7.10.4", "description": "Allow parsing of top-level await in modules", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-top-level-await"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/plugin-syntax-top-level-await@7.10.4", "_nodeVersion": "14.4.0", "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "dist": {"integrity": "sha512-ni1brg4lXEmWyafKr0ccFWkJG0CeMt4WV1oyeBW6EFObF4oOHclbkj5cARxAPQyAQ2UTuplJyK4nfkXIMMFvsQ==", "shasum": "4bbeb8917b54fcf768364e0a81f560e33a3ef57d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.10.4.tgz", "fileCount": 4, "unpackedSize": 2624, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zoZCRA9TVsSAnZWagAABD4P/1SiSytnZFZFjXFAFyYA\nNpj5liwyv1ce+FCv2stBTXh/1aEjeM0Gg4gMGj6eqfjwvYwMEmdJ0t21fiWQ\n7KIQPaiKJxs0GW8U/6r3M6CL/FWNrutlBVzy0Uc39DcHKZFUPy6Dxe+L0UFQ\nLTZkLiwFdwNVJ+SguSMHZiisa0JJvdqpsrOrT2495rgCD4mei7B/r6oQRiqB\nvKR8cjTSAiplb5wNazamWM3Yv/rqTYzEXj/fGuxcY8MOKJVufQax7RHq05lq\nCiuKVDsKXjQrQttY2FYNZ4bvovRz9ia7l/8huV4f9VCMHdi2ZrRJx2iT1vzV\nLJXbh57IFRE7iJqnHkws0P8Qi0oLmuSjyj0xwut81Fg5dFtvgpECf5PRaaMJ\ntbH8zRTA+qtY1Be52hPekOzUW79z5x9Efta5XWhlKAzG2wwmZOspYENHb/vz\nfCx/QtRufplCsLVlpm3NUBQw/kJHZx0gjOlUQYK+bBKpZRvAW4p9XafTNgAF\n9x/qqMGECWWels2GmerpzNRDrjQQGyjzu0Vi8k1pp6HLuxSmX/WbZhTrHvJw\n40wJOr7VEWCRA8GnnXDadtpXCiGD6yM5RqmwoLpZuJOhtUajlfmHggZEtmsK\nWVGxk+tYy2YFxzCsZDW6caxQm7w9IBTjvyWSe7xs2yFs+Js+R8D3PUH8TjmQ\nwh+n\r\n=Qhk7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEqwWS9vcpIDa+whuNQcFZkNj4iFd+RoBBLQNZLZnywkAiEA+uMfCSKHrXboPwLWJdgZsl6sikfESbhedmMx+MJqvLw="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "jlhwung"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-top-level-await_7.10.4_1593522713457_0.48597385939477755"}, "_hasShrinkwrap": false}, "7.12.1": {"name": "@babel/plugin-syntax-top-level-await", "version": "7.12.1", "description": "Allow parsing of top-level await in modules", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-top-level-await"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.12.1"}, "_id": "@babel/plugin-syntax-top-level-await@7.12.1", "dist": {"shasum": "dd6c0b357ac1bb142d98537450a319625d13d2a0", "integrity": "sha512-i7ooMZFS+a/Om0crxZodrTzNEPJHZrlMVGMTEpFAj6rYY/bKCddB0Dk/YxfPuYXOopuhKk/e1jV6h+WUU9XN3A==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.12.1.tgz", "fileCount": 4, "unpackedSize": 2566, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM+5CRA9TVsSAnZWagAAzMQQAIbVJkK00+YMtDqoF7Aa\n0lXPJlPm0ExEWhX1WSIVhG5Kew3C2rA0BWox+SGemSwUUchiSuXFhxjwGO/h\nnsRrhiZD2GDDZOk9dElEqEknHKZSAZheladQpuFxZNFraLJwuj68mteJ6ve6\ndv6/NNKvLquhM3/lj5ektFL6b4FR80F4vn2Ll2fAgC3FK+wA7DzVP85a8V3e\n5cKNM2rl14Jm8B07H7NV1lBpZhjwm7xQ4tj3u5t2+2T2Hh4OYX27rD2OXRA1\nS+abCve1aOj924ysz2FkIuUeSFAcDQ60k0sc3Ciq1Ba5UYPrX9hlOkAuZ4Ah\n6FSfDNfh27Nm81hf5RRYKQsV7Gc3Hk8aSnYGqGDq1cWxWMmcoYvxSLkmt/aW\nzkiIxX39CM6MhO5AieiZfjeBBUnbSjbWGg3dfsvYRYeiBFH9mdA0El4hASsT\nqcK3QZBl148/U0GYnJ3eTXpwcq6Uy3f8MA9KdiVDCfDWcnkXyOj86EJF7gA8\njichk81uV89PKJg2PdZH90aCadwDr08z+ESG2Vwhg70SRMbiYq+8yD0tte4e\n3Th+hBxIymAN7y7H1GwscbJvYaM3pjTfNLb3YsWWl3CKqgRFO/0vYXgrW4fj\nHUNtTPHLxj7cNblUv5rhMmapYlPp/Y9I/CJCU7Bl7weR3xVZ7J+xRJkBIzMo\nQjJ3\r\n=4yrU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG2ft27zTK4OrWcVVGp1dJk9zLaKn3gYzK36Zooki9/qAiEApkYBYp2vVynJ9Ont2oiGjMI8YPLcJHAUp+HDTw/VVww="}]}, "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-top-level-await_7.12.1_1602801593066_0.5552562767762339"}, "_hasShrinkwrap": false}, "7.12.13": {"name": "@babel/plugin-syntax-top-level-await", "version": "7.12.13", "description": "Allow parsing of top-level await in modules", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-top-level-await"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-top-level-await", "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.12.13"}, "_id": "@babel/plugin-syntax-top-level-await@7.12.13", "dist": {"shasum": "c5f0fa6e249f5b739727f923540cf7a806130178", "integrity": "sha512-A81F9pDwyS7yM//KwbCSDqy3Uj4NMIurtplxphWxoYtNPov7cJsDkAFNNyVlIZ3jwGycVsurZ+LtOA8gZ376iQ==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.12.13.tgz", "fileCount": 4, "unpackedSize": 2642, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfgTCRA9TVsSAnZWagAA/JoP/A3U3X1MM9opuiP2aNfS\niJ2fXVeci7XZkS39eeC41Zp3HFfI8YXaIUEQtbTxlmBZJGfxjmqKzLdS5wYf\nxBLPJR0vfhbh3KYSU9EFAgWgdOeHnKh0dE5mm3CDaCYGibi7eg1Iq0qp6rjL\n67GA67G2m3WA16NfbE/vcSRvt0qmcvLeVT8pIqrcrjwXO3rgZ0HfFO4yyqeE\nyAa6oG7pmyYMGH2qu6E3YqNX15bTX7+rzMiq1UuxTlDUvkhGII3NdiHXhKJg\nKF5FOqCNgIWx6ZHBKL5QbhkFOodFNLC1RhK9MlO4lC2BfVEjR23Ce5RHrlHT\n8nvo1sYJCuLjsbgWrMX+JNT5O+UMyu5xPi9zkB/pFm4Ra7FqlPWX6Ftnz0o3\nhJPwtDAdRaH7KdtIc3w9Urcd+ri3khZBi7ax+In5dqywvmfQ15YU3hnpVu1C\np04L2aoODtFDBItoV1uJZLBtxXWgBqlsm9TzIOkLRDcNGCnB6VGJetKFuQUO\nxNNXjUkeM6XGYDkiev+6fyujXDEX+LNb6736xsH62WcFRWcy3I8X/4nJH6i0\nE4JMROQu2x6n5SQAuswgTGmGpjhc7JK9CSXbucxDNKl+OfIecrGhDe8g0otc\nIMA4BUvXyD3NKhN9LfJfQwAinax8VCPtuLsgzR4xEAJfNzM02MVSR+rmV0f5\noZHG\r\n=uhsw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCl5wot+PtQDskPX8LiQjjvrQyQTZGWaVwRp4noSxA/kgIgGUapLTych9K/wSPnjkW5PqYJAeVDM8/ln2WzAagbBTE="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-top-level-await_7.12.13_1612314643448_0.38920937143386336"}, "_hasShrinkwrap": false}, "7.14.5": {"name": "@babel/plugin-syntax-top-level-await", "version": "7.14.5", "description": "Allow parsing of top-level await in modules", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-top-level-await"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-top-level-await", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.14.5"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-syntax-top-level-await@7.14.5", "dist": {"shasum": "c1cfdadc35a646240001f06138247b741c34d94c", "integrity": "sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz", "fileCount": 4, "unpackedSize": 2738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUq7CRA9TVsSAnZWagAAYKwP/irqUY4At14Xzgv2EGCX\nzzwVkJj5Qy35XuP5ondeHNpsnPbygyrndCftnFzzQPSK2afPUDXtBjNHT6AR\nni1e0jPRo+045SGK+sFjpxYWhUZNsDHPb1ZaURREsRU/uZ5o4/NKg56Tn6og\n6O2c371benb37PYIbI6JKI3AYgdzAcv/YmtB1aCEpzJb3j21ZELzWUexXL9t\nuB6WrslMQ0fjtbDZaTF2uR2V0dBfnk6oUhF1mzcUxQkbpGfUmf24gMpSSNpk\nlYI5i0mqDJlg9H8XB3zV81FOqHTp6P2ZiX6gOo2rkoIRqih6Jbti2bjlfO5B\n8NHbZw9k8er9ChTdp8PGIjVU1yGXGEcQ3yaPAdlQxoXs2JY6m6hIh3+RjpOx\n+gDlf6/YF86PfOx0oalZJ1m0CH2lXxib2OuE0xVQTkEzKeojxncpxXJOgreS\nq0B0s87lra94VJtASRxqhcgOrjZRYVbfu+tv1RrKhMDyPpl2qJdD4xneNy+g\nGjLtyFWzS376JJ9tvfyR4M1pjsIYV7D7GehZ3lYKHdi1d4RiIbmo8rQWTpt0\ntQZ14KzO7b8XeJnvLBcMVLHrUNV3NwyyJKiY/6XEKS6Ky9zAkMjJKl7x8JTq\n0OSW7YxSB14M/I+XqI2BtWKPC/2D5XLF13mSOO0rbNF+WRMXOKHfDTl8N2KW\nItxv\r\n=EbaG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDz1XcksmQvZD1J0HrqE+3DvJA3tnhNur/HvYyPlyj+DQIgC5z4xyo3XQKuWKpo8UqFDPnZEEB5kMFhIovy5igcW68="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-top-level-await_7.14.5_1623280315680_0.2548133668516932"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-11-05T10:53:12.178Z", "7.7.0": "2019-11-05T10:53:12.798Z", "modified": "2022-04-04T17:25:32.341Z", "7.7.4": "2019-11-22T23:32:04.616Z", "7.8.0": "2020-01-12T00:16:23.974Z", "7.8.3": "2020-01-13T21:41:31.257Z", "7.10.1": "2020-05-27T22:07:19.068Z", "7.10.4": "2020-06-30T13:11:53.636Z", "7.12.1": "2020-10-15T22:39:53.169Z", "7.12.13": "2021-02-03T01:10:43.626Z", "7.14.5": "2021-06-09T23:11:55.826Z"}, "maintainers": [{"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}, {"email": "<EMAIL>", "name": "jlhwung"}], "description": "Allow parsing of top-level await in modules", "keywords": ["babel-plugin"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-top-level-await"}, "license": "MIT", "readme": "", "readmeFilename": "", "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-top-level-await", "author": "The Babel Team (https://babel.dev/team)"}