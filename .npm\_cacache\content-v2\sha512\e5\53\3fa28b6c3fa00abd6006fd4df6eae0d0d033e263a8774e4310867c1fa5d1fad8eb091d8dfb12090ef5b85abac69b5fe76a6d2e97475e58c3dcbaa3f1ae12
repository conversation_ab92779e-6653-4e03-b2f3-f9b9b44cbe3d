{"_id": "magic-bytes.js", "_rev": "51-39a733d10ef3eb16a50c1bf35b72022c", "name": "magic-bytes.js", "dist-tags": {"latest": "1.12.1"}, "versions": {"0.1.0": {"name": "magic-bytes.js", "version": "0.1.0", "keywords": ["magic-bytes", "mime", "filetype", "file"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@0.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "da7196154715d4fdaaadd0bdf531bacf5972e030", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-0.1.0.tgz", "fileCount": 17, "integrity": "sha512-Uny7M/1LZKd7GANkhKfDjq9ny3s0KG6iCAIsJ+LtrUMAJKP7vxYggTxrNJLU5HHDhhCjVLSHc8Ja8Cm9Yy8rog==", "signatures": [{"sig": "MEQCIEc+hBRxKipH44OlPVfqjUSiM7R1DiiTb9jmaywEfCjPAiAq4C/9s2aPxZ90YcujxCAb7p8hbXR2F2ZZ7MDVap82lw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31059}, "jest": {"testRegex": "(/__tests__/.*|(\\.|/)(spec))\\.(js)$", "moduleFileExtensions": ["js", "jsx", "json", "node"]}, "main": "index.js", "_from": ".", "_shasum": "da7196154715d4fdaaadd0bdf531bacf5972e030", "gitHead": "58a4efa2af67415e5efd011f5a7a92f24ec035c0", "scripts": {"test": "jest", "start": "babel-node example/node.js", "gen-tree": "babel-node ./src/create-snapshot.js", "example:node": "babel-node ./example/node/node.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "4.6.1", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "8.9.3", "dependencies": {"monet": "^0.8.10", "ramda": "^0.25.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^22.4.3", "babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-jest": "^22.4.3", "babel-preset-env": "^1.6.1", "babel-preset-flow": "^6.23.0", "regenerator-runtime": "^0.11.1", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_0.1.0_1522349530516_0.8211885907557217", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "magic-bytes.js", "version": "0.1.1", "keywords": ["magic-bytes", "mime", "filetype", "file"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@0.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "d3d493bab163f66d68fc09c270f056937e1ed53d", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-0.1.1.tgz", "fileCount": 11, "integrity": "sha512-dlkE7CUtauJwKYPiM13nt772Kt88yrD3a8rVF/2x41w6022ytDx9MvI20aeeNTgJTiWFVrnK5Nn7JW35eZmlDw==", "signatures": [{"sig": "MEUCIDcqYbYWRYtLx8/0XnGhrG4cSlA+j138WoGCJIkWVXPfAiEA4Iq1w/4TRHIoiv4sT1hhpefkm0LgbJqPzyNbLD+UFcw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36116}, "jest": {"testRegex": "(/__tests__/.*|(\\.|/)(spec))\\.(js)$", "moduleFileExtensions": ["js", "jsx", "json", "node"]}, "main": "./dist/bundle.js", "_from": ".", "_shasum": "d3d493bab163f66d68fc09c270f056937e1ed53d", "gitHead": "209f7e25f4f17ff8b2f2b4221cc5a93c91a20e76", "scripts": {"test": "jest", "build": "webpack -p", "start": "babel-node example/node.js", "publish": "npm publish --access=public", "gen-tree": "babel-node ./src/create-snapshot.js", "prepublish": "webpack -p", "example:node": "babel-node ./example/node/node.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "4.6.1", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "8.9.3", "dependencies": {"ramda": "^0.25.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^22.4.3", "webpack": "^4.4.1", "babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-jest": "^22.4.3", "webpack-cli": "^2.0.13", "babel-loader": "^7.1.4", "babel-preset-env": "^1.6.1", "babel-preset-flow": "^6.23.0", "regenerator-runtime": "^0.11.1", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_0.1.1_1522350720375_0.3792809455894788", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "magic-bytes.js", "version": "0.1.2", "keywords": ["magic-bytes", "mime", "filetype", "file"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@0.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "d964d882d03c0dd44c6f0dba83b1ff00a245ea73", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-0.1.2.tgz", "fileCount": 5, "integrity": "sha512-N5q4N62tGVA/cJoPfr/CV5bafCbgfhCfwTeyA2Uaa+ett5cK+fL0z3dXuSqNaivkywmrQ3shqMP7fYpWlH0lOQ==", "signatures": [{"sig": "MEUCIQCPxC61av920ZYL35mv6jjw8pqwMc5zOCtKbVQHeWFU5wIgFewww+vYeEDidKMp/Yp6h+ZDUVSEkGLyLSHc2TB9UOY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9750}, "jest": {"testRegex": "(/__tests__/.*|(\\.|/)(spec))\\.(js)$", "moduleFileExtensions": ["js", "jsx", "json", "node"]}, "main": "./dist/bundle.js", "_from": ".", "_shasum": "d964d882d03c0dd44c6f0dba83b1ff00a245ea73", "gitHead": "4e62286983eb00cd153fb376e1e847e97bea600e", "scripts": {"test": "jest", "build": "webpack -p", "start": "babel-node example/node.js", "gen-tree": "babel-node ./src/create-snapshot.js", "prepublish": "webpack -p", "example:node": "babel-node ./example/node/node.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "4.6.1", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "8.9.3", "dependencies": {"ramda": "^0.25.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^22.4.3", "webpack": "^4.4.1", "babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-jest": "^22.4.3", "webpack-cli": "^2.0.13", "babel-loader": "^7.1.4", "babel-preset-env": "^1.6.1", "babel-preset-flow": "^6.23.0", "regenerator-runtime": "^0.11.1", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_0.1.2_1522351773617_0.5280546705371036", "host": "s3://npm-registry-packages"}}, "0.1.2-testing": {"name": "magic-bytes.js", "version": "0.1.2-testing", "keywords": ["magic-bytes", "mime", "filetype", "file"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@0.1.2-testing", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "4b885c48aa433589f6c23babc9b5d858cd038491", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-0.1.2-testing.tgz", "fileCount": 10, "integrity": "sha512-0lU8ebgWu9Rm8zbworFE4cZcblmi4rhiuzVU9oasTr8ii4deHgdOHpx4W99rXPKg/KW07w9Wlp9zEMgiM/X6uA==", "signatures": [{"sig": "MEYCIQCx8naFlHWeu2S46qdRPblSEtcplCb/5J6Xrl/tlDk0cgIhANR3tl0+jX9BxdrXthy+9YZ2v6evn6tOP+lb5Ng7XAZ1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28368}, "jest": {"testRegex": "(/__tests__/.*|(\\.|/)(spec))\\.(js)$", "moduleFileExtensions": ["js", "jsx", "json", "node"]}, "main": "./src/index.js", "_from": ".", "_shasum": "4b885c48aa433589f6c23babc9b5d858cd038491", "gitHead": "4e62286983eb00cd153fb376e1e847e97bea600e", "scripts": {"test": "jest", "build": "webpack -p", "start": "babel-node example/node.js", "gen-tree": "babel-node ./src/create-snapshot.js", "prepublish": "webpack -p", "example:node": "babel-node ./example/node/node.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "4.6.1", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "8.9.3", "dependencies": {"ramda": "^0.25.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^22.4.3", "webpack": "^4.4.1", "babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-jest": "^22.4.3", "webpack-cli": "^2.0.13", "babel-loader": "^7.1.4", "babel-preset-env": "^1.6.1", "babel-preset-flow": "^6.23.0", "regenerator-runtime": "^0.11.1", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_0.1.2-testing_1522352265391_0.6133148596704496", "host": "s3://npm-registry-packages"}}, "0.1.2-testing-1": {"name": "magic-bytes.js", "version": "0.1.2-testing-1", "keywords": ["magic-bytes", "mime", "filetype", "file"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@0.1.2-testing-1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "021a8585adf4c117055e117243e2bfbd2d91354f", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-0.1.2-testing-1.tgz", "fileCount": 5, "integrity": "sha512-YQh11245bDSfH7mBcR9/f2hcgc0RpPT/r940uX6Dnc8+KlcGDjMWw1Y812LtFquWLlYoW39rFcwptGaMUTuGYw==", "signatures": [{"sig": "MEQCIB0nss8+9JNje7x9h45tWY0/cFpdInf4rEOtFoXB6VPzAiBBz/vbvBXS2y8hJVRX2E2mI2Hjmx9fe8C+bC+zlS5nRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10078}, "jest": {"testRegex": "(/__tests__/.*|(\\.|/)(spec))\\.(js)$", "moduleFileExtensions": ["js", "jsx", "json", "node"]}, "main": "./dist/bundle.js", "_from": ".", "_shasum": "021a8585adf4c117055e117243e2bfbd2d91354f", "gitHead": "4e62286983eb00cd153fb376e1e847e97bea600e", "scripts": {"test": "jest", "build": "webpack -p", "start": "babel-node example/node.js", "gen-tree": "babel-node ./src/create-snapshot.js", "prepublish": "webpack -p", "example:node": "babel-node ./example/node/node.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "4.6.1", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "8.9.3", "dependencies": {"ramda": "^0.25.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^22.4.3", "webpack": "^4.4.1", "babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-jest": "^22.4.3", "webpack-cli": "^2.0.13", "babel-loader": "^7.1.4", "babel-preset-env": "^1.6.1", "babel-preset-flow": "^6.23.0", "regenerator-runtime": "^0.11.1", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_0.1.2-testing-1_1522353282741_0.9085137373701992", "host": "s3://npm-registry-packages"}}, "0.1.2-testing-2": {"name": "magic-bytes.js", "version": "0.1.2-testing-2", "keywords": ["magic-bytes", "mime", "filetype", "file"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@0.1.2-testing-2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "280d202d19d6605564c83d233f333d544e0148ba", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-0.1.2-testing-2.tgz", "fileCount": 5, "integrity": "sha512-M4qGgbO9yctuZLLKZ6U+pbG3tnX1Yty0Tbed/mgye0dNYeAzjUL0Rt9urQ2SvrXDL1EoVyQ/m6DUXmizfFbOuQ==", "signatures": [{"sig": "MEUCID3unSkzDI6LaS+9DzmD+vwRZqCRIfVGZ2lD1NPwTgUeAiEA9hq8u/7hDVYVOOe92ORw6BRcWoAJvy2B87W69JXjtWk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9868}, "jest": {"testRegex": "(/__tests__/.*|(\\.|/)(spec))\\.(js)$", "moduleFileExtensions": ["js", "jsx", "json", "node"]}, "main": "./dist/bundle.js", "_from": ".", "_shasum": "280d202d19d6605564c83d233f333d544e0148ba", "gitHead": "4e62286983eb00cd153fb376e1e847e97bea600e", "scripts": {"test": "jest", "build": "webpack -p", "start": "babel-node example/node.js", "gen-tree": "babel-node ./src/create-snapshot.js", "prepublish": "webpack -p", "example:node": "babel-node ./example/node/node.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "4.6.1", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "8.9.3", "dependencies": {"ramda": "^0.25.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^22.4.3", "webpack": "^4.4.1", "babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-jest": "^22.4.3", "webpack-cli": "^2.0.13", "babel-loader": "^7.1.4", "babel-preset-env": "^1.6.1", "babel-preset-flow": "^6.23.0", "regenerator-runtime": "^0.11.1", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_0.1.2-testing-2_1522353459559_0.4836954421439841", "host": "s3://npm-registry-packages"}}, "0.1.2-testing-3": {"name": "magic-bytes.js", "version": "0.1.2-testing-3", "keywords": ["magic-bytes", "mime", "filetype", "file"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@0.1.2-testing-3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9614864a4261ea26a7ad4f1b072644bfcd892746", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-0.1.2-testing-3.tgz", "fileCount": 5, "integrity": "sha512-nEE12FSbZikR2UVHtxYlX+c5D9is/AHkDZJFQjpMGCPJyV17TgcXawbP6gn+hWlc39Q6O8urfHzYJuveN2Gi+A==", "signatures": [{"sig": "MEUCIQCZ2u4cjxRuFMRIj/VkZ9z6+gjnEO8lbW3lRS3En8tDJgIgV79hAGA2UUtZRPo1A+pj1HIV4CnHB5Z/W7+oMAXKop0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9894}, "jest": {"testRegex": "(/__tests__/.*|(\\.|/)(spec))\\.(js)$", "moduleFileExtensions": ["js", "jsx", "json", "node"]}, "main": "./dist/bundle.js", "_from": ".", "module": "./src/index.js", "_shasum": "9614864a4261ea26a7ad4f1b072644bfcd892746", "gitHead": "4e62286983eb00cd153fb376e1e847e97bea600e", "scripts": {"test": "jest", "build": "webpack -p", "start": "babel-node example/node.js", "gen-tree": "babel-node ./src/create-snapshot.js", "prepublish": "webpack -p", "example:node": "babel-node ./example/node/node.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "4.6.1", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "8.9.3", "dependencies": {"ramda": "^0.25.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^22.4.3", "webpack": "^4.4.1", "babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-jest": "^22.4.3", "webpack-cli": "^2.0.13", "babel-loader": "^7.1.4", "babel-preset-env": "^1.6.1", "babel-preset-flow": "^6.23.0", "regenerator-runtime": "^0.11.1", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_0.1.2-testing-3_1522353732846_0.46403550240186164", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "magic-bytes.js", "version": "0.2.0", "keywords": ["magic-bytes", "mime", "filetype", "file"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@0.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "d8b1cb2a139b193dc5f2f630c25e8bcd26e29752", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-0.2.0.tgz", "fileCount": 5, "integrity": "sha512-6D/wuk9MzKyjG8ZfUr7OlyWbRJuJEHYRFcYW0K7bl21313Gs2fCKKt/Kx7Gmru0ox9yyBJx8BiUzwTe7CHH1tQ==", "signatures": [{"sig": "MEYCIQDHZO9/sfENAS5M8D+XSzlcFf5/PsJ4EXdRn1lt2VTQ1gIhALMKH2MOB8ypCOVN84wte7WOInW9hDgA7iED7orwo5LQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70061}, "jest": {"testRegex": "(/__tests__/.*|(\\.|/)(spec))\\.(js)$", "moduleFileExtensions": ["js", "jsx", "json", "node"]}, "main": "./dist/bundle.js", "_from": ".", "module": "./src/index.js", "_shasum": "d8b1cb2a139b193dc5f2f630c25e8bcd26e29752", "gitHead": "9220aa2f9d7cc274ac850fb49b797a18d6adba7d", "scripts": {"test": "jest", "build": "webpack -p", "start": "babel-node example/node.js", "format": "prettier --single-quote --trailing-comma es5 --write \"{src,__{tests,mocks}__}/**/*.js\"", "gen-tree": "babel-node ./src/create-snapshot.js", "prepublish": "npm run test && webpack -p", "example:node": "babel-node ./example/node/node.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "4.6.1", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "8.9.3", "dependencies": {"ramda": "^0.25.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^22.4.3", "webpack": "^4.4.1", "prettier": "^1.11.1", "babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-jest": "^22.4.3", "webpack-cli": "^2.0.13", "babel-loader": "^7.1.4", "babel-preset-env": "^1.6.1", "babel-preset-flow": "^6.23.0", "regenerator-runtime": "^0.11.1", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_0.2.0_1522357390173_0.1830609790586124", "host": "s3://npm-registry-packages"}}, "0.2.1": {"name": "magic-bytes.js", "version": "0.2.1", "keywords": ["magic-bytes", "mime", "filetype", "file"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@0.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "da999ba8c76506b33bbb8e969ee4b8f000e0fd5d", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-0.2.1.tgz", "fileCount": 5, "integrity": "sha512-ECcyIKdu5YQBd/+ciS8Fzj/fiOlEdaXsMjNilHEM93oHvqS8JoXrEP3sbzss6jMrrHa8rTGAT8mgRrfr0urqJA==", "signatures": [{"sig": "MEUCIDUBgue7/9Qu4Zjvtb4aBM+HDum36qpFnLKVL53L416dAiEAnzFYXAzBK4urpUDPLrkQtad79lVX3irfF60jwNqeYWs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66112}, "jest": {"testRegex": "(/__tests__/.*|(\\.|/)(spec))\\.(js)$", "moduleFileExtensions": ["js", "jsx", "json", "node"]}, "main": "./dist/bundle.js", "_from": ".", "module": "./src/index.js", "_shasum": "da999ba8c76506b33bbb8e969ee4b8f000e0fd5d", "gitHead": "3ac19221a1b3050a1aadfb672a9c2180fb6658e9", "scripts": {"test": "jest", "build": "webpack -p", "start": "babel-node example/node.js", "format": "prettier --single-quote --trailing-comma es5 --write \"{src,__{tests,mocks}__}/**/*.js\"", "gen-tree": "babel-node ./src/create-snapshot.js", "prepublish": "npm run test && webpack -p", "example:node": "babel-node ./example/node/node.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "8.9.3", "dependencies": {"ramda": "^0.25.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^22.4.3", "webpack": "^4.4.1", "prettier": "^1.11.1", "babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-jest": "^22.4.3", "webpack-cli": "^2.0.13", "babel-loader": "^7.1.4", "babel-preset-env": "^1.6.1", "babel-preset-flow": "^6.23.0", "regenerator-runtime": "^0.11.1", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_0.2.1_1522402157808_0.7345497549404736", "host": "s3://npm-registry-packages"}}, "0.2.2": {"name": "magic-bytes.js", "version": "0.2.2", "keywords": ["magic-bytes", "mime", "filetype", "file"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@0.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "66373e951e9c077e612d6f75488b9a36cda999ad", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-0.2.2.tgz", "fileCount": 12, "integrity": "sha512-XOxN86jH1D90gpINvhFuTzmBBpgcWdtnOEQUsCfYg63tfHQbhyXwzNtQPO1LpwDOFWPMbZ63tdO6y+v3SQmTbQ==", "signatures": [{"sig": "MEUCIQDl8cJkDOfh80dLF8njRogc/TVBEQhyLthkLBXUP2WD8QIgY/KvKDOS2YOcxaRkCEqaOD3cRMzeK6Vgq/B/D7HB4zg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151152}, "jest": {"testRegex": "(/__tests__/.*|(\\.|/)(spec))\\.(js)$", "moduleFileExtensions": ["js", "jsx", "json", "node"]}, "main": "./dist/bundle.js", "_from": ".", "module": "./src/index.js", "_shasum": "66373e951e9c077e612d6f75488b9a36cda999ad", "gitHead": "43f7701420d22abc6d1f25409a4d745b41f3a18b", "scripts": {"test": "jest", "build": "webpack -p && webpack -p --config webpack.config.browser.js", "start": "babel-node example/node.js", "format": "prettier --single-quote --trailing-comma es5 --write \"{src,__{tests,mocks}__}/**/*.js\"", "gen-tree": "babel-node ./src/create-snapshot.js", "prepublish": "npm run test && webpack -p", "example:node": "babel-node ./example/node/node.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "8.9.3", "dependencies": {"ramda": "^0.25.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^22.4.3", "webpack": "^4.4.1", "prettier": "^1.11.1", "babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-jest": "^22.4.3", "webpack-cli": "^2.0.13", "babel-loader": "^7.1.4", "babel-preset-env": "^1.6.1", "babel-preset-flow": "^6.23.0", "regenerator-runtime": "^0.11.1", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_0.2.2_1522403187846_0.3376251996578321", "host": "s3://npm-registry-packages"}}, "0.2.3": {"name": "magic-bytes.js", "version": "0.2.3", "keywords": ["magic-bytes", "mime", "filetype", "file"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@0.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "1d32c48c9e17eb6c28fcaff2391c38d59aafb13b", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-0.2.3.tgz", "fileCount": 13, "integrity": "sha512-V52qNvMAgSxbSgJchw6Cmf4KQ5aRP8Qn4YNsO87+k3X5w0yb8ZxVQmKX/mpH7CXSZSVNlA4pGnD5gOk/Jnyh6w==", "signatures": [{"sig": "MEUCIQCf5EHjRSnPJETTc2KHHbalIFNhEImPv0oq68OarC1HvgIgWWghzINhTlj1+yXtitNK7VYgpwvKXu8F/5UBsmurX0g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100130}, "jest": {"testRegex": "(/__tests__/.*|(\\.|/)(spec))\\.(js)$", "moduleFileExtensions": ["js", "jsx", "json", "node"]}, "main": "./dist/bundle.js", "_from": ".", "module": "./src/index.js", "_shasum": "1d32c48c9e17eb6c28fcaff2391c38d59aafb13b", "gitHead": "0a607528f8f2c76db13182bc81f6f60d92ba5631", "scripts": {"test": "jest", "build": "webpack -p && webpack -p --config webpack.config.browser.js", "start": "babel-node example/node.js", "format": "prettier --single-quote --trailing-comma es5 --write \"{src,__{tests,mocks}__}/**/*.js\"", "gen-tree": "babel-node ./src/create-snapshot.js", "prepublish": "npm run test && webpack -p", "example:node": "babel-node ./example/node/node.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "8.9.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^22.4.3", "webpack": "^4.4.1", "prettier": "^1.11.1", "babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-jest": "^22.4.3", "webpack-cli": "^2.0.13", "babel-loader": "^7.1.4", "babel-preset-env": "^1.6.1", "babel-preset-flow": "^6.23.0", "regenerator-runtime": "^0.11.1", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_0.2.3_1522424481485_0.6453960808133763", "host": "s3://npm-registry-packages"}}, "0.2.4": {"name": "magic-bytes.js", "version": "0.2.4", "keywords": ["magic-bytes", "mime", "filetype", "file"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@0.2.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "90206ac64c96d490fd5d1d2001b93a2a613f2b6c", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-0.2.4.tgz", "fileCount": 13, "integrity": "sha512-BhiQeT+FHr2XuLjwlOg/2pwx3mWFF8d5mrsDEta/qAznPu/zpJErvVdVp373L6CBOU/ZnE91zrdSPhm+p6vLdw==", "signatures": [{"sig": "MEYCIQCCThjT+Iu0BWLjGz9ysF+tRbZaxoWROBtJE6Qg+oJ/GQIhAKsm+u9MRVpaa7T2QrGQ29R3ONg9ugjiUqgjdOQfdkT/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100157}, "jest": {"testRegex": "(/__tests__/.*|(\\.|/)(spec))\\.(js)$", "moduleFileExtensions": ["js", "jsx", "json", "node"]}, "main": "./dist/bundle.js", "_from": ".", "types": "./index.d.ts", "module": "./src/index.js", "_shasum": "90206ac64c96d490fd5d1d2001b93a2a613f2b6c", "gitHead": "0a607528f8f2c76db13182bc81f6f60d92ba5631", "scripts": {"test": "jest", "build": "webpack -p && webpack -p --config webpack.config.browser.js", "start": "babel-node example/node.js", "format": "prettier --single-quote --trailing-comma es5 --write \"{src,__{tests,mocks}__}/**/*.js\"", "gen-tree": "babel-node ./src/create-snapshot.js", "prepublish": "npm run test && webpack -p", "example:node": "babel-node ./example/node/node.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "8.9.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^22.4.3", "webpack": "^4.4.1", "prettier": "^1.11.1", "babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-jest": "^22.4.3", "webpack-cli": "^2.0.13", "babel-loader": "^7.1.4", "babel-preset-env": "^1.6.1", "babel-preset-flow": "^6.23.0", "regenerator-runtime": "^0.11.1", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_0.2.4_1522424565445_0.5406191675281238", "host": "s3://npm-registry-packages"}}, "0.2.5": {"name": "magic-bytes.js", "version": "0.2.5", "keywords": ["magic-bytes", "mime", "filetype", "file"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@0.2.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "bd7edf32908975082fdcbc8f6c22191a66b4ec79", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-0.2.5.tgz", "fileCount": 8, "integrity": "sha512-AnZ3ZAMuX/uUO4+xrk429/4FRkco3Av+Wukghooi8+3sJVkZny7qgZU08Do83OuLghGkf9w0sFPehMVQ+UpT+g==", "signatures": [{"sig": "MEYCIQD1hU5Q2QHI7PWqXrDahusO6KMhvCwe686i3wk4hswbAgIhAIP/bGSW023wB/zLWvKs4Cs48innRuNjaLBNoaCDqK7G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77715}, "jest": {"testRegex": "(/__tests__/.*|(\\.|/)(spec))\\.(js)$", "moduleFileExtensions": ["js", "jsx", "json", "node"]}, "main": "./dist/bundle.js", "_from": ".", "types": "./index.d.ts", "module": "./dist/bundle.js", "_shasum": "bd7edf32908975082fdcbc8f6c22191a66b4ec79", "gitHead": "5a306203083491d704a0bb209287371f660152c6", "scripts": {"test": "jest", "build": "webpack -p && webpack -p --config webpack.config.browser.js", "start": "babel-node example/node.js", "format": "prettier --single-quote --trailing-comma es5 --write \"{src,__{tests,mocks}__}/**/*.js\"", "gen-tree": "babel-node ./src/create-snapshot.js", "prepublish": "npm run test && webpack -p", "example:node": "babel-node ./example/node/node.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "8.9.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^22.4.3", "webpack": "^4.4.1", "prettier": "^1.11.1", "babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-jest": "^22.4.3", "webpack-cli": "^2.0.13", "babel-loader": "^7.1.4", "babel-preset-env": "^1.6.1", "babel-preset-flow": "^6.23.0", "regenerator-runtime": "^0.11.1", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_0.2.5_1522425097139_0.8381148169299117", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "magic-bytes.js", "version": "0.3.0", "keywords": ["magic-bytes", "mime", "filetype", "file"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@0.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "61d8bfa6ee946160d40e40bd7dbfffbecd4caf33", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-0.3.0.tgz", "fileCount": 8, "integrity": "sha512-5q32TXm5u/SUP86y86j4vJqWKQX4s4Dt1l5VK2N5h8+UZS3GEnbGxpZCp/xiDs5ZT2kF27mTJ/60VmZBEytN8A==", "signatures": [{"sig": "MEQCICvxGbuEwEvPP8meyvn8Gikog/ZpTvnqbyVJHt6ahwlwAiA3bTYf/Zj+fYowU9+PBK//slEZPZoIejJP9xkj6neZaw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78721}, "jest": {"testRegex": "(/__tests__/.*|(\\.|/)(spec))\\.(js)$", "moduleFileExtensions": ["js", "jsx", "json", "node"]}, "main": "./dist/bundle.js", "_from": ".", "types": "./index.d.ts", "module": "./dist/bundle.js", "_shasum": "61d8bfa6ee946160d40e40bd7dbfffbecd4caf33", "gitHead": "e462bbf517ab254a80d1c1cafe546af9a9ebd461", "scripts": {"test": "jest", "build": "webpack -p && webpack -p --config webpack.config.browser.js", "start": "babel-node example/node.js", "format": "prettier --single-quote --trailing-comma es5 --write \"{src,__{tests,mocks}__}/**/*.js\"", "gen-tree": "babel-node ./src/create-snapshot.js", "prepublish": "npm run test && webpack -p", "example:node": "babel-node ./example/node/node.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "8.9.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^22.4.3", "webpack": "^4.4.1", "prettier": "^1.11.1", "babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-jest": "^22.4.3", "webpack-cli": "^2.0.13", "babel-loader": "^7.1.4", "babel-preset-env": "^1.6.1", "babel-preset-flow": "^6.23.0", "regenerator-runtime": "^0.11.1", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_0.3.0_1522432572498_0.5594202641794785", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "magic-bytes.js", "version": "1.0.0", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "b851a3130e46846346156db487620dca23d43847", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.0.0.tgz", "fileCount": 7, "integrity": "sha512-ghWi2dxGiwvBDCIbDY/ZZBBRIqe20fd1LCS6xH7nNHKO80Dyw6UJqLFRQQelEX86p+psuhUO81UqFzbwnGMZ2g==", "signatures": [{"sig": "MEQCIESBI1Q89Y1Co4NcT7WiqsxtFHYLdj/LOxwNxzrptKu5AiBfvVDdgMgGtZoDjY/eodFFtCSZjq+v61gye0841dn6kg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29191, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe20cpCRA9TVsSAnZWagAANIEP/A/+/VIB5aBZbvqOQ+42\ntz3DbZsb67nftAuXeEupFYDxdzjoh+oUFKsumVuzEIxOOgqAFsYuM8PvVtRI\ndHaK8Ads/IZlKWpB17exiqjqhebvzjXz7oQ7qnr8VDyJlAzOW9zMMIx9Q60F\nCn1GUo80sYTGe4VVpo2S6HdgJw8ihb2keln4cQtZSfZvVmNEBOdRcpSevPxf\nRXQCEozdvhyAfd7F/7dgCYmNUpnzLU0lAmpeWHLIW/qXxQPokMDlbMdgL215\nd8qP592IQ3WhH4TDYwKkmqZzo94NYsGz9vXC4Ve/85QZDyxWgbFNHvgmxj+z\nazHJmb/o6I9QuRoJJ5qxsrTh/9d0VaPPVoDI7UMuuxmCu38q0+YTh2rTOfjm\njzd28YN5iTWU5xpe8+2JygtiI/9FfMGrxqN71PG/qA565+SYfBpUPWPjb4GM\nUQlK6A/1NEcOCZkDpSYQvT76wfJ+XiZresod28W1yN06qN83Ar6U4scpBR10\n6XsEOHo1ipX97tyhtRUeAdxb833B9wo9cPI/YIbLROn1Aw8louCc0FFCye2K\nftDO6FN1Ofd5dyPPSCeFUNBSc3eoBjJruZCd4EwiRWjWWt3GNCreYMQAM56C\nU7Z9IvDWIOvCWlUzmf2uk/d/Q4jSs/HcpMXuVU16RDiYQyJkzyReiaSNRdNN\nfA1c\r\n=wpCH\r\n-----END PGP SIGNATURE-----\r\n"}, "jest": {"testURL": "http://localhost/", "testRegex": "(/__tests__/.*|(\\.|/)(spec))\\.(js)$", "moduleFileExtensions": ["js", "jsx", "json", "node"]}, "main": "./dist/bundle.js", "types": "./index.d.ts", "module": "./dist/bundle.js", "gitHead": "23b24345b16c06b61419433796a6df8765079f4b", "scripts": {"test": "jest", "build": "webpack -p && webpack -p --config webpack.config.browser.js", "start": "babel-node example/node.js", "format": "prettier --single-quote --trailing-comma es5 --write \"{src,__{tests,mocks}__}/**/*.js\"", "gen-tree": "babel-node ./src/create-snapshot.js", "prepublish": "npm run test && webpack -p", "example:node": "babel-node ./example/node/node.js"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^22.4.4", "webpack": "^4.43.0", "prettier": "^1.11.1", "babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-jest": "^22.4.3", "webpack-cli": "^3.3.11", "babel-loader": "^7.1.4", "babel-preset-env": "^1.6.1", "babel-preset-flow": "^6.23.0", "regenerator-runtime": "^0.11.1", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.0.0_1591428905445_0.2507995529402067", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "magic-bytes.js", "version": "1.0.1", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "34954c0375c6017e998a6b27501ac2f4b9852475", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.0.1.tgz", "fileCount": 6, "integrity": "sha512-NbN0ZfcyEVOSS6ejIDzEGTbJuydciB7D2TWZ/S/Q6Iq7vkGKVRk6deNM/88jUaYnYTDRdAj6bacb68HGGJuOXg==", "signatures": [{"sig": "MEUCIA0lW6Hpa3rqlPMzACd0Q755zpBCY0tRpfHJHZ5tH/cdAiEA/h0zPEYguX/01KuDAnalWOXsEhckm/mmjZ0cb6Lf5Hw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11519, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhH5T1CRA9TVsSAnZWagAAt4QP/3q83DuqSrKIS5CajdZv\nl0hhUxagt8AAmLqITGstmrS9AL7oFpqE+4307Y1xlW1xJ9Ln2xDrTv5wpcYW\nrFLB0Joev2PYgjQSK7jcjdCCwmoQ3jBF7EfKfFhBhpZ5DfmsC283Zb19diA0\npCUqqsdJotGgYd5HQXZoOeoy03tUtNv3YAVWmLScuKoUAOe//K0bMUdGd2Oi\n4XANP80mYIVsf4oQWhpTv13JdlyxKcROnKLkZtjLeVCxvcQUe+mjM3VU3Xbb\njnlib+pbTEOTQVeXCkDm8cF23wuJK0MDm3TwYFojCJsP0BqfRZiZkdhcaFXd\nMGUUdGlUEY+Y8SykosocH+nzA+0eQFmQXBW7bSDtO4mHw8u26c+qr4q2apXT\nwRFO5GC4/CMSIaBkFCDhqZi56Ly4h64C5QM5owu9eSp/dW5xeUsiOqj6bjGr\n3hCHR8LavwtR3ui8tGl09o1xny5ONWZlUj481k+K8jyDld0zFgIRbA45r4mW\nO5tdW9DUyxiUh2HInsni8O0BOF4f10VYuxn+eESsLaKWdJKUrxN7T5y+5R8A\nVAQOpEpgDke/yoMQgOZGs3ZnTsGT75mcBPZ1gqIN1wjJTB+Sg8jrsB4BX6MR\n3nHGF6l/CrYSuzrhYUdRxkN9QLYfwu4O+5q9mitXX2ILll68jOKXPbSy6s2A\ncVlO\r\n=Nmss\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./index.d.ts", "module": "./dist/index.js", "gitHead": "d32e6aa4f4e663327372af25f020eb37a5cbf536", "scripts": {"test": "jest", "build": "tsc", "gen-tree": "ts-node ./src/create-snapshot.ts", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"ts-node": "^10.2.1", "@changesets/cli": "^2.16.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "ts-jest": "^27.0.5", "prettier": "^1.19.1", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "regenerator-runtime": "^0.11.1", "@babel/preset-typescript": "^7.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.0.1_1629459701412_0.44626094614262746", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "magic-bytes.js", "version": "1.0.2", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "87c9fd8478b38663b0b08123fdee23a0936af7a1", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.0.2.tgz", "fileCount": 6, "integrity": "sha512-pom6zMIa1cQ33eT9QYBSCwez+P8W9uh1/ma+RClCogCJhWU/+zAO0IxMmOEJPK3w2yZbEvFxS2IRaeR24GCPFw==", "signatures": [{"sig": "MEQCICl7RjzX50QtNf6nFOna6F6BxdLFyjR65NRGsRS70/C7AiBmdhBmER3AF/LC5Hg3XJUbMw/Hasr9tgdIEd+BWVIkKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11517, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhH5mXCRA9TVsSAnZWagAAxzsQAIOIxoYGhXld4J5a6A9L\nUF6SYzmMGS6JKZT3M+t8qmfeFe6FERVYu8KKrz4MasOaK2PFGnftzh9sG+jl\nM3f/KJ3sLnfTJ9iIRrF97xnS9MKOc2ur53mHxrsHoZlYwvjty8Q3a9tbWvwd\nWCeQ5stgCouVaZCIImfEBvlKrqqxxPXsHVMT2I0rmcvBPyWEZSkelwmGxVNO\n0W4ZGCsgNtPz5Oe1GFKEJPf5ILbKnFVuA3pr8ZQFs+JxO3rAtUP4Qb1zXqJ9\nK/yk5FAC7AJrOUaZmjYhNi2msCfByXrPYXh+LY4IouSBpyzwEfonoW/v8Zon\nR0oGb3L9joZNfIpT1VSp0/YARZIDwvmByiWI7zlK++WsDjpU+Q4kqDd8VBpT\nFg4zC6uVx4dLtMrWX2OY7TjqQsPnAxvr1vgokn3VpBUUu8cv9827GyOIS7eX\n8wfWg2vIebV4g50fqmc0ET0AcqlfpYTf9grxWEiEuvNj9C8ypEOksHxRpLGY\nKuz0CMVj6bBtB01y14fF7nZ/o9tY9EMJE4AvLvnsR2RNYM8Tt8hhzP/6vKzE\n52yF3utdy78VzFTqjLtvgUjxSxM9H+ZyECz7t9lDIKzKbpYxzEwD+IVtb8oU\nZlXcKvPqwf0hkOqZux6RcldNAVTL5WDXqowKjZvb7+RHno7hIF9E8ew9pyuW\nDclU\r\n=CcmX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./index.d.ts", "module": "./dist/index.js", "gitHead": "01f808eb37e3888ac01c2ae4449462f1cf7854e3", "scripts": {"test": "jest", "build": "tsc", "gen-tree": "ts-node ./src/create-snapshot.ts", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^1.19.1", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "@changesets/cli": "^2.16.0", "regenerator-runtime": "^0.11.1", "@babel/preset-typescript": "^7.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.0.2_1629460886919_0.1760244344170534", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "magic-bytes.js", "version": "1.0.3", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "ab3ecad226da68c8ff7b0918508bc8151641fe9c", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.0.3.tgz", "fileCount": 30, "integrity": "sha512-OWDRGUhQHmTUNZ9uSKPviNLhQ8fu0QnNVqWJsoncACdVB1PrTZo9IyJQv6/qbxjG3VHitqy8OplfLpXgW9/TMw==", "signatures": [{"sig": "MEUCIQC7XxFBZ+ujKIFzQlUCzB1EjFzzdoeVO9GcTRaav9mRqAIgUUnPAeyLQ1aV1nHfPsL1iFVvT44n8LYiHzXD26MJObM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192383, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhIMcxCRA9TVsSAnZWagAADwYP/1IlzFgCh4Q8cVWycGDa\nxDWRPIiT2TMeqvgWnN2K1xz0UNWNrE1r8VI/A5WSueQFNZWhW86whRGgxmid\nrnEL5uTG8lHq8UCTLpDXLrjQQl9sKwkTjGn2/stXPa/zR79glPHER3rlumWV\neS7TETfNBUT6eOQu+7P4eQ4+OyMNgu/DpGnUHgyHseZVca+RmuK6zoAg1Mg0\nWYatbdzYr1PrJR8Yj8l7XgooGv3Wt2p0BivsuXqqycJ8icuOE/9zb7k/ypGb\nuzRMVLJW8l0xC7q9RCeb0GXdWayXrUboSCkKJiaBHEkE9B3R3F3+8+XXAE4+\nPKKkfBiHZxvDGGODrL9mRHhbiHoeYUYcN4XOfjBINjkQ0EQOhvqcj9UJOASw\n3DZTQi8B/jBdkV0ONLLOfq7exZb9Uy0yFlfrSGwBtn7EDGkRy4m43oKCYDiU\nxpHpMaTK5/8qcWy6xbgUqHXbuxgx7ldZBtu54v0micThdx+Nlt6olySGLcsP\nn1u4b61BW5gaEuE0bBxyIB3pb/pGKedHx3d6Xy1R7i7HE75lKcHnWUipmipr\nXpwb0M9hVPFMV+rSYRAqmFHR9lvDKsExGYs6u5Fr8IViUCrmS1NgNezobOUG\n2+rayuC74r4D76LrWkYDwfU1DfTAVFTN4RL4pX6N03GKGXWFYaUPZ1arEbcU\np++8\r\n=L3AJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./index.d.ts", "module": "./dist/index.js", "gitHead": "6f5fc7f2430fd0bf3347bb705b4557941519a498", "scripts": {"test": "jest", "build": "tsc", "gen-tree": "ts-node ./src/create-snapshot.ts", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^1.19.1", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "@changesets/cli": "^2.16.0", "regenerator-runtime": "^0.11.1", "@babel/preset-typescript": "^7.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.0.3_1629538097004_0.7693586466458082", "host": "s3://npm-registry-packages"}}, "1.0.4": {"name": "magic-bytes.js", "version": "1.0.4", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "a1e6401b13dd4d1a0bd0044b2c29054e26c49a14", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.0.4.tgz", "fileCount": 30, "integrity": "sha512-WYjAZFR5KrHPJKIwGXreT7ylbtZTLrtYkBMc9L5AfDM3yqLMPjUNWGOw/f5d90KtzbcYKK1F9vbwU5dco0JjrA==", "signatures": [{"sig": "MEUCICdXAgIMHuv3wWLjzPXE6myG5RQTk/6jGY1afWbwrzzaAiEAve4pPYilAjgHlHQiW5TapR67x2y/G7RPx9QKhvcACFI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrhRDCRA9TVsSAnZWagAAeBEP/A2ykf2pA3K6/ljQxdkR\nJH0HaUPN9GPVpwvDxjNzfINVYnstXnHsi+OvuKF5lsXze260Q0m3GhsnTwJd\nW8fQRDz/jv3wCIWvUiMZ60yIsT5N4Jo8WV2EzhbeNjVa38JaX70fB8olUOg0\nui/8r8/leTDl6bJ9MPUQqqn2rB0bObFNoQ8ZGt1IY0G+faOHEDdOfcq0S5DZ\n5NJLDpftn0gI//qi5Gr09HurOWzsXpzDbjYgHo/HNxAI5UN2Me4WzpoX4wIo\n2BPt2AJ0NLIGCgJ7BhqOGKTkAat9Vngk5MgVnrjXLESk0fWmjKOgwIMNdzry\npzSOWoa0k1XAy7S0B4eAr/rq2CE0sK3VEeBpZTw+aZ/1OSjg2DGtBYGDyfSJ\nxtDjNNBuuaIxTbyy2V+ockISt9DKieHffUutKtehn06PJXPqSL7+hzCp3QWh\nInZYX2a2PbgNs1bunXgbv/YkGNmEctUNjhM0VoQoxTd+GvQXm6jwhdDRVwnx\npcA7N6jh0sbQ45StYs6KDPMSY3UJ4FDnOgqj5rXjAFYKTMmGMkLNi9hLSWaX\nPZ4FvxZjiu5HGNN9SP9UEctrbNw6GVxU1Epe8Es7yixWVWwH8fQ0pVe1zBJJ\njTTA60vcJQiP6IbtYvYzzHZKFpsJHxs3i1KjFryn3ILQY3jyyra3sKEbmh4Z\nABj4\r\n=AryY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "b0bd51279b6735d7cba23305421f515eaa317397", "scripts": {"test": "jest", "build": "tsc", "gen-tree": "ts-node ./src/create-snapshot.ts", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "14.18.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^1.19.1", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "@changesets/cli": "^2.16.0", "regenerator-runtime": "^0.11.1", "@babel/preset-typescript": "^7.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.0.4_1638798403267_0.7346261824251703", "host": "s3://npm-registry-packages"}}, "1.0.5": {"name": "magic-bytes.js", "version": "1.0.5", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "4173bee9f8a1cf104349273b0699a4ab09dfa1fe", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.0.5.tgz", "fileCount": 30, "integrity": "sha512-STCusEEv6phvhDXbKinz34ywvCR/sTTb4AzR6vibNHdTTAf2oc6g3gR8Fx78qKlPdagxcdrJ2wNhZm4cqa7E/w==", "signatures": [{"sig": "MEQCIEwXOglVvM8iwWyBIflrU6BDZsjTvaomaKPFBY+xcEIsAiABIgfWyfVNBHB0daZ3qu+/t1KZ//wzf3ZmH3sckWrFWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr55OCRA9TVsSAnZWagAA76wP/jUR60Q7sfsVz5P6jokZ\nogc8/hn0DjlX8/MQUnyEaCx7qCVIIVkyHOtABvJz/HvDAIpmHacKlETrLHYd\ntmk7leKWG5R18XH+yfdq/pj7LoKJFDO8n1enYBZIetze5t6pw+3JNfWXK3hh\nGoatweHTouce5S4ZsaCeLyC1eRxvjcp3g6le7F1vJ0FvWMXMsAlzuxEVX6Tv\nlsVs3vUmgWFnP4I1t4jLVnOiSmreFzLnsgvgU5DghZxvHO0VQ4gOnQyn2iwM\nG41Zlo7VWBffH9AxRzqMcsqcGrVnpoYegrzfRPGi5NPaLOJkU/2iAtYzO2/C\nLFT2xtGac8n5a4bZ8H48hBgOZgROnX1cWu1gh8wJWzKXiXeEtx5orEUWihko\nSx7k8kwAvKg3x1GIWmJ55w9yfxAzB84bWuiowMc+T1xax9a1VFuHciLE6AQW\n/PUClNpAtn+PYTv0P5GK8+SnblbxyytJ+iKHxF9dDXxnRRpsXeAp8lWe1gaF\nJlKTGgH02T4kg4H+w9oqskQQM3DSy9ZPrr5lotOZUnFnZts1nygobwZdKkgY\n45CtzYAK5fwPE6lCXt94AmYMZepmbU6dpS+i2xR4p7kVcD5u7qEwjegezbwj\nVgWE4zFwROSrrk7gcdGqDt/fGhxM2B99rWd6FXaqA8ltk8YsmS8EZ4UNmgDg\nnSzV\r\n=WqxU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "3f95f648f79ca5dc8efe7d692dfb958501265495", "scripts": {"test": "jest", "build": "tsc", "gen-tree": "ts-node ./src/create-snapshot.ts", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "14.18.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^1.19.1", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "@changesets/cli": "^2.16.0", "regenerator-runtime": "^0.11.1", "@babel/preset-typescript": "^7.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.0.5_1638899277916_0.2569367475172297", "host": "s3://npm-registry-packages"}}, "1.0.6": {"name": "magic-bytes.js", "version": "1.0.6", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "7fd97dcb8850dec23fe65ff0731fe843f2ef680c", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.0.6.tgz", "fileCount": 30, "integrity": "sha512-AT3BiB++0pDDlTAaDb1RxchyPLfWxb9uTqdsXmHMa/cDcduPKMO9LW8lPtqNOICfP2ohrVFGPYuGM7jenlyoQQ==", "signatures": [{"sig": "MEUCIQDGA1G9gtTKl7GxcphBKjFwhC4Shp3+2QlLm9CuuamEUwIgTeT8rfND1xxGQdVCI/x1/t+zHdJYFt/B7oZgRMt1IS0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhsvU7CRA9TVsSAnZWagAAztwP+QAqYcQnG8Qvq4jSXer/\nUSVH0owylOUj5S0LwJcvpfo144XqSsm2nWXk2sDfC1vxG0eXT/VMTbA3V4s0\nApH9uwtw2AiaWIzt4IVzRuctnK5jzM9DkAUZSZcEBJz+ymGsGlr8J0it8muF\nNiIHO7G/cV/0qkNqhSbxqpIWJmvmY+IKbi3KRZo1T+Hfze140IshMhVPuImv\nY+XbMW9YDKOkF0uFEe3LD6NmpSYJyLeXmbHgQYgRPhL80u9yCQu9zNC4v1je\n8/KYhni+Ucg4Mk+DoOIsyVlHWaYCaFfG2vTT1fSHAJ1dZVFNAvzZ6AU4QU1l\nFZOYbk4wzAPsZYAilqBK5P2Vfov5P4f4pFGO8aWsbLBk8TWyf+DZgYREZIXB\nGkiKGcd0dkOXUhf1pfi7MziISZOdpC/vLwPFsMKM+UFzYv3Knu6qL4rHxe5w\noxiUpu4Cz1JJknUVJl7O8qA7ayZQ5H8aUdyKeoRCpbXlDQyD7wCgwOz6k+Yg\nxBZDsW2MTp9Na8MRdk6k7R9ForQss8/NVSpxURqqrwET5er26CVqdnnK6Fot\nRgUdNJS0mwDD9TH5NK+CTS/hRlhhIzpZR54RWCDI8D87BlHgz/hr34vP93SF\nEAmN5GHXUNleqVKekuIBMEeSQXMT6CTN79pyOuJSKCII+ow9xuFd3XG0IbOo\nQu0G\r\n=fBfa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "0d756565dd6155019b3fb214dd5d038069a93ea2", "scripts": {"test": "jest", "build": "tsc", "gen-tree": "ts-node ./src/create-snapshot.ts", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "14.18.1", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^1.19.1", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "@changesets/cli": "^2.16.0", "regenerator-runtime": "^0.11.1", "@babel/preset-typescript": "^7.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.0.6_1639118139355_0.7741361845317543", "host": "s3://npm-registry-packages"}}, "1.0.7": {"name": "magic-bytes.js", "version": "1.0.7", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.0.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "1b773b19aea7b85aaa5289e6931941b32e04d865", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.0.7.tgz", "fileCount": 30, "integrity": "sha512-/8xDOqjndAJ07DE+uo0CDBcEjV5aRpW33zKIElgVVu1+osC0OBzMjjMu5eJYXJwtL6Q+DanY7hRinJI3uyYNww==", "signatures": [{"sig": "MEQCIBX1eyJaF3tDSju/5g+jhxhvq1lXpD3ULea6D5ufg5JUAiBJ5KYeUmRr1S6ZjGBhM0mS3vZ8aVZPoFiYdrptrNCE3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 209850, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHphMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrlAg/7Bg1zUPOaqRAEzCo+D8bjmkGg7qIDNeYIkQNTWWeOG/07YRCI\r\nqHgcm3/wsglNAgo8wfAzV4Bzk0FmV3nraAFEBL4c1ZSKLrVdKFLvD/I4q57n\r\n5WAZUgmZP+Zmy6cXZub7rgZt9w31QHMtDHbwiWvtvNArF+heBlU6PU1UpjBr\r\nSNzWxGUldoLkwUOqBDuDo15GrPVkGLpsbXRSDkrpxwOvPmlbLf5CP8MiNafv\r\nfCy2oJs/NqlFSQttZW3wHz6ps1kGs9/vCcRJ/YpJUdQITto2tK9xJQpuDrlq\r\nM9Ub/XBeb1EWgp1lx8LdxaGq7yCo+XmHBzvCAqXhG2HDaiwTkEAvr+7WmGmp\r\nT/94Ke1Ds4F3Vr4QYj/1djNphDHYVcRYgf9GC8GulOp0kxiW/WSQJX9ISXw0\r\n0zYblQ3GVFtx+1Qef+cnTgR1DNJj1VjWC3mI0lRytzHhnnJ7KJugfs66uKUY\r\ne8h6+Id7qMarxtMIMMnBW0hqxyYgZIAlp5TB85ebdg4R+HWEDrJqOXxLyupe\r\n3/FLTqYModpVdPUfEhIoRAt17rYJPq50I8Y4qL1P9FkJ75HbWklH63YPhBuQ\r\nZdRVBokW/FECbHeWcqcIt6rnd8SCnUGh+aQlYBRak0O0SBX1827DvJzNnk13\r\nIGd7fVIcWT8H93qPUtWFYnQmS0mg9IR0s6g=\r\n=LVqz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "9423ba204942fb10def56ceaefe9ad31f75e64d8", "scripts": {"test": "jest", "build": "tsc", "gen-tree": "ts-node ./src/create-snapshot.ts", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "14.19.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^1.19.1", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "@changesets/cli": "^2.16.0", "regenerator-runtime": "^0.11.1", "@babel/preset-typescript": "^7.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.0.7_1646172236716_0.3418719267485122", "host": "s3://npm-registry-packages"}}, "1.0.8": {"name": "magic-bytes.js", "version": "1.0.8", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.0.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "e93c4aabe7533f9a29bb04a53f89b5c67c6ac79e", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.0.8.tgz", "fileCount": 31, "integrity": "sha512-bXD6GYCFcdjug+laKhT5twA/ob9s/OOUWz5W5K3IL57zKRjP47GsS5nTM4eAMPoY0qY/i3tdG9/szUIgPh+jsA==", "signatures": [{"sig": "MEYCIQCu2l/O+1cI1BMIZLNNWcVfUhY/ShA/4SG35IKp95ChzgIhAKGXO1ORL9fd4Xt1e0+Pc+Z/twyEHpp2lLOPocEU5heD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210919, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMwygACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNsw//Qct6aIh1szLF6JxAV13EwBkR0egHDlU6MdM838iGsmDqq6FX\r\nqnWHIkJ0p5HJlZasvZ109NRZxpBozNdabdi7GiEuA7XGeOgwOnEq9lbl/8F1\r\nkuPCHWWCa9lRif5JrzXWLqygNXhSGVKcS3NbzBvb+CxBzf5/WvDuWtkd1Qv3\r\nWrNB31zGrQ4H4e5AeV7O35NUnJrBG3dPOw1Ttpc1p/ltlLibkHf/0AStdN1W\r\njfTIet/94e4k/BZ8+zJ3PsmGgjqs6LV22XOuediy36U8AmSU4/Xdg5FIM9Wp\r\np5njB8Tf+WDR5HD4yaM4Oa4+PeWsQItGx9hdoIl3x5/Mgc4YUF077Aq/1tEU\r\nkuN29AQbkh2jQgu03YHGKGLgDkWDLKNykfS/IRyxpgVUiXTO2+FA6f1ohjNV\r\nMseAI1UiIYriOKGDXy/EF29Q73iWZX+2hR50oxlIAXKah9zbjcbTDkcUDyYr\r\nVZ7akNylswIEf0wNWVKZs2ZvD8DYld0BDhoLx4d5xgzadMhPN+Zxb+abmIjW\r\ny/Z5p13S/Nr0+8M7wzxLg0qkdRREjnsdF6T+WApE8m4UAh2c24pXibShyWh8\r\njOEHUMMbL3Qazev2CHDJRL2ibXdZVxYr9gMugNj5Y8A3OU2+7phltXu5kzWc\r\n/4g+x/dynhVepB0UquAV019NUeIdq/oDc7Y=\r\n=2pp9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "402a2abf27850d3f33e95ab324a4bf2d290b8853", "scripts": {"test": "jest", "build": "tsc", "gen-tree": "ts-node ./src/create-snapshot.ts", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "14.19.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^1.19.1", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "@changesets/cli": "^2.16.0", "regenerator-runtime": "^0.11.1", "@babel/preset-typescript": "^7.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.0.8_1647512736112_0.11223195407848352", "host": "s3://npm-registry-packages"}}, "1.0.9": {"name": "magic-bytes.js", "version": "1.0.9", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.0.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "62b590eb6aad773369a07d9c691482909cbf9a15", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.0.9.tgz", "fileCount": 26, "integrity": "sha512-V9W4eWft2tF8KNpD3cfpfpHEBEgzDSVIFJEjgalHTB3RYDKF71XdWns+naFByzEMgmHCG7BCFaMsaJ8qseXJrQ==", "signatures": [{"sig": "MEQCIDpY0AmLy5FjbTHHGi1Fs5yJ51xfKkcKVnYTchkBGPbTAiA/9zcdlWGiaLUpSvzbDxsxOTAXvkC9aYcWeOYz5tyN1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39021, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiQem3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmotug//ZoO9XLdXn/xUcmCWnhWUUazfbeYj/h5kaHOjpNCAMpMfsyBP\r\nZinZJGnKP9RR2lo6FXaPzpefdXLlnNHiWQW4MEFEtEbS2aUkwrnWS8wVW4M8\r\nL3s4HivUc5n18DucvBPULPum/YgKa1CH6tby8cbKTLhSk3ADxo9c0Xhlw1Ag\r\nDPypU29aB6EUAMMqDPuXBOt8GOUy1tzb6mtq/I4mQ8Db5KdXTNTXHqpd0NYR\r\nXY0+9oNuSjxnzjpOOyJE/trvErw31xeeVBSZbB1wa2AF+EKqk5ABFIWX95J0\r\nrCZBZojbtEhLIBwrkwJwMfuN25fonZkmRPmRhHxH4c+4QN2G75kNvuWlByfF\r\nbS04IJwhYpCy/Ny3NTx9WYpytJfrFkCC/zbTsDZsHaZ84RWIA80pDiOQQlER\r\nyNWPYEjQTcEazkPi+dKlVAALX6IHzBFCVAy/ZNdPe6dl1MQQrd6W/vgpD3I0\r\nZ04/rFuuSGYUEndXdPLqO1oCK8wXeZnchMC8v11De0DiPwfry+Gp4Zlx+cVv\r\nw4JY/qp5HYAVoszjIm4Hk9p7BGNXMSfN9YSPHBo4WYOobUtJXLG9tG57txoe\r\n0HZlg7yeMNLHfVHlOSL8h7K6JZvgsmxIgOv24oc2pq5MIwId7nt0RCEX3J8r\r\ncmR/VN1TyQec+uSFxtR/cWCHYqGLV+WruGg=\r\n=mSW5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "3ed22fb7daac20623d5da63284d1d20f99f72879", "scripts": {"test": "jest", "build": "tsc", "gen-tree": "ts-node ./src/create-snapshot.ts", "pre-test": "jest --clear-cache", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "14.19.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "@changesets/cli": "^2.16.0", "regenerator-runtime": "^0.11.1", "@babel/preset-typescript": "^7.15.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.0.9_1648486839395_0.6936713316226408", "host": "s3://npm-registry-packages"}}, "1.0.10": {"name": "magic-bytes.js", "version": "1.0.10", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.0.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "82cde55f70f131388e8fe4d872386cd403391b73", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.0.10.tgz", "fileCount": 26, "integrity": "sha512-ts0m01nUEvO+bfREPB8eDvkz49iM1nIzFsZOgsDDcYaadjIbL2U/N/pC/kVRXeJJb4jV21tjS/iWdXesXJqVOw==", "signatures": [{"sig": "MEUCIQDlaVnIwIvn42HrFXujD1++jIPgly8UwhgE7Ne31h4OPQIgCCAbc/HbTGvshKaVQr6DCVaw0NQPhL3I+MmYfNFkJ1U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39007, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiQerHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/eQ/8DyOU+NFKWdVvUWqxvicUhUX0sc4I1hqBo8aK1DIsxRj4Vidp\r\nxQOyPZWqzQGY3g4ZaOKFRjTlUJ7PCw7wFLllV/wNLKJKIa9Ge62Y5X0lfumZ\r\nVT9+TgqIUrVj/SsYINFg+VenkjKpyiO0KYUfC2vO284Wl7ZmZUQnq86SD2/q\r\nZMiMDFIiba3UDNsmespBpVR792+8XxKl+7gWzUt/n1ZN1kGRHBYHlSoZzeGb\r\nclGVdUDwPXQNma7FW9z0uIoGks6kw/GhYJIwgfKYU25+EWsuRIetTppa3mi2\r\nUHFRitXrdnSwCSoT/l/NcMZgL9MTvjIAte6kccfjhkRtgCw1xLnfBX5fYMpH\r\nDlBdIBTE06nYCZXobR9+q4jDg1nWdPIATNA5MrSbmGKkdn2JxGVbVi6yqQHJ\r\nzFpxbHzG2qV89HzHwJCLhLeZHOeege+a/mpkyALwRdr/PpfkUGalMw3bK/jG\r\nRJgjLUY5v/vrIiYE3kUy4G+mXZyfQAN61mdvf9zX5pOaTirXfgWH3HN1u2eJ\r\npWENz2Fpv86sNmkVwcW7Jwi5J8q71y1WnL+f4K2xOuxxGhi69PlCCfNVZ9jV\r\nQ+Ox9D9/ZBHI/gwMe+zr4ndpNc7BPjGSotJxN++SGS+dkgWl5RFjD8ruMMiF\r\n3jHZGzNdGYh7953VJpl025zxiN4RkyaULqg=\r\n=qZUY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "9d21c9552036919ff7ecbb7a73d5d1d33c827403", "scripts": {"test": "jest", "build": "tsc", "gen-tree": "ts-node ./src/create-snapshot.ts", "pre-test": "jest --clear-cache", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "14.19.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "@changesets/cli": "^2.16.0", "regenerator-runtime": "^0.11.1", "@babel/preset-typescript": "^7.15.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.0.10_1648487111006_0.9462059682119193", "host": "s3://npm-registry-packages"}}, "1.0.11": {"name": "magic-bytes.js", "version": "1.0.11", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.0.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "e3b392a9efd5b2028eba6ce8a3fab13de2b69567", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.0.11.tgz", "fileCount": 26, "integrity": "sha512-kWGdCAX9C3I7zZOqeJi8omLtZBhPcB3SZ5/76j3sCdH/GH4p/YMIcZFFlPhgXYpMvP2jAkcD+YS9loAbYXaifQ==", "signatures": [{"sig": "MEUCIAdytvx5xb8nQnvLq6+eSaWVr8HEeAwVl1Aks7iQCmH2AiEAmoVa9l1KVQWCRfGiJRluDX9MiskmGN0jEQ/rNveXvQY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39146, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaAFyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr1FQ//YvGx327VCHbwEnLFbl28n0FRV1CxLdXzUD0Wrf8AOPsFonpR\r\nWmbiT9CaS5lQxitSjM5jkYgSSeBhjwbkjjFMzTqqrxDY32zCX9xiG+SRKytr\r\nEzKOGygAS78cVfdxqZwvZQCrEI0usJWqdLfSIyyWkRYmxbOgaBPQxPls/VHz\r\nErWt+0gx2UI/b1W3Zy5eDkQmLNMNx/TlOVp0LYEzuPNSZ0d7V2jj59stc/pw\r\nd3yB+Mb0syAkCHfhrjv6DRtRjksDtpli8XLCo7sMzh/PN92cp/qpB5lnyZu3\r\nhNqgOZZzhtKhJxNV9GPLi+9qUCZn6jg+R2B3lm+pD+CD80eUD5vPCkWgZ0vW\r\naqmlMe/Gyz/RLaHCZfp8fpB0GI/+x4pZ6YRP3OWRbhMHLTuIWB+SMIvTNBBb\r\nQEKdeXSU2raCpBjwqbn+e2A/qiearq3mpIFqnYq9AiuArniG3vRD2XTvuI4X\r\nhDyF7aguHtE7YixfbrRPrKcnt2hNzuVFFVZ8Hx9zThU3kNqlAEZCdSMBAB5Q\r\nbpBSAieqgv+md0ZkE0WoSRgNQGx4XdJ4RnDJdKeAyD2j+HfR9DPW2gkLDkCI\r\nPgmpGuAT9Y6+SdQDdIiQ0lYsHAAUN5WAtSMkNkAW1stdbB1Q+4RdU8YdUL+c\r\nI2LA6Z55/lDDNqbzfQPSlkz6qqmZ6faQS/8=\r\n=SSf6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "05c87a78db150101e53778b143cc297866c4bb69", "scripts": {"test": "jest", "build": "tsc", "gen-tree": "ts-node ./src/create-snapshot.ts", "pre-test": "jest --clear-cache", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "14.19.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "@changesets/cli": "^2.16.0", "regenerator-runtime": "^0.11.1", "@babel/preset-typescript": "^7.15.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.0.11_1650983281866_0.9458171695874968", "host": "s3://npm-registry-packages"}}, "1.0.12": {"name": "magic-bytes.js", "version": "1.0.12", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.0.12", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "6287de1070f4c33da4a6e8bffb2752d513a2d287", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.0.12.tgz", "fileCount": 26, "integrity": "sha512-R<PERSON><PERSON>a32FnY3dF5nJeAS4Qcry76tXeOe2sXIQGAt969sV04QLormWHXmHn4o/yIbb4wRlnCqWu8fpVg3TU7sFg==", "signatures": [{"sig": "MEYCIQCwbe7mpOHDVsbCNeXFlWxVjewsdVaEaOHTvVWm8e7dvwIhAOl4i7AreJ2iWsreYLUEEXpipbw4S7dvRWTIkulv7i4y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifRYTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo35A//dLWJQpWRgeX3bFbv/MuGoIaSaXacmJD+1e4w5W9AA9+GwqgE\r\ngsu+Ecbzac1bwAAsuNDLTfTNnGLJNa/v4ukEuQnQi4JdUysAkVlJ6zhFjNCQ\r\nLq38E55wwAYKY+dMEbXNdSS+26veT3c6pGuCuihccrNCETaNUVaGnoqAwo4V\r\noqpZ0t3VoYk5S0eXv2gKK3EsBDkxjkt0Qp436RBJzA93A3PtC4QmZVUCNIe7\r\nLjHKYXgo66lZObJcBonPHjgN6qdVRICa4Fvs3GDxTlMUQLYUYeKBL13XvXWh\r\nFmxvsh2uOzoM1xg9BVwjYKVU21m2Js7QZMLTrWLW358S7STPAPN/UBuVMgwr\r\nIWHSggwpZNAxRUZ6aXCoXsLikYXD22PozeJ/0KDVl9ShyL7sA9D2aQlizOlA\r\nnm0yIPdiCM9+TWP6TuU0VRERsiLZt7Iw34rMt0sVdBk1ROckZi1b1I+DgqqO\r\ncRVqZ4oiC97NuSepVTvMWE5nXaIJ4z1WJRB/A1ygVs92mimDjaldwWF6J47F\r\nw3cG92tqaPUTKcfbuBPNYaMn3ek9tuXCF71ufYMWj1VI0zCAOhXKC5VKsQW/\r\nRYyORQ4HSVUVE3/3CSs0X7AajFcQE84/05F4PYs1PEegBqpuBBgQY4NUhvma\r\n7lfFA0WZZiar3zzNzeQFzpSSz4i/YJ5jETM=\r\n=LcCl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "c4fb684f941b7306081cc44beef849426ead288b", "scripts": {"test": "jest", "build": "tsc", "gen-tree": "ts-node ./src/create-snapshot.ts", "pre-test": "jest --clear-cache", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "14.19.2", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "@changesets/cli": "^2.16.0", "regenerator-runtime": "^0.11.1", "@babel/preset-typescript": "^7.15.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.0.12_1652364818870_0.5929369178373156", "host": "s3://npm-registry-packages"}}, "1.0.13": {"name": "magic-bytes.js", "version": "1.0.13", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.0.13", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "b8e72e7c78900bf7be8647bb71098d4371b3c818", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.0.13.tgz", "fileCount": 26, "integrity": "sha512-DiI8gdaLxnO4+H0WglSlDSCIQRff1L42ZhOVTuAbb0uwCFIVLM3J28oQ4XMX6LJmFTiqMV/ejYV8bc/eewwdmQ==", "signatures": [{"sig": "MEUCIQD1mNK0mCXeDCAFoozg5PV/CTTsnfC3rRj8PkWkPfP7EwIgAv/9R8ylX83lDMzWkCY0qg2+ci3K/mhVtPqRIFFit4I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUOqdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrwDQ/9FwosCzcze8b9+U0jAYvOmvTIlIPeWw9DjfmsWv+0GNDWiBs7\r\nyKm1/7ewRAEQRc6/yd4Gvv38exNxnJh6wxp815JpGvcOb76b5rmLU2XEWIZ7\r\nDwFP2kBf7X0ZZFpHJr1t//CD+JyCWBAq3Hsvrp6uhpvB9iPe1M7mQJXgQYBP\r\n5jSqMs/5nozTAqNMyUsRDfmvVQ4uuQ6tMIgEqaNG+Jx/MAe/f+PD/Fw2WThK\r\nrBVaeIdsS7EaVY6KFHQQ7ycFwnL5WGDbmpunAsEwVnPblN47Gt7YDIW3smVb\r\nG146PCwiTJQxDj1/18tKHimXY6mVfVlaOLuXmhLtWDWvVMpCIkNlJHyJCtZS\r\n9c0y5WJBvKNeXBKkKIqO2n2T4lbjeQtV7JsPrdbTuT46g4vqCOqGZ/e7gurK\r\n3NEicu6HSWMQHSMQ+oiUeFv/VlBZ4dFI+5s+stNXCuQrk1DJXjzKaxR8ubQo\r\nDaV4pTfdWH37Jnp0vaOzuZn5JKs58nD94iijyttzHuoHThjwI3LaSbFHeCwe\r\nfXQwE+KAFGpz1bXU4ohXHu3ykhJXHzB+gOQw1yblTN/6oVVE/oqN3VhQhNjL\r\nvEHdDoLngLCkWZMUS/0w67CgtC4jFamNXpA+uk9wVyhDnHluWMubc1Xh9v5B\r\n5QwhxHTjw3fK8breXMuraANCLDc/v76eQQ8=\r\n=YHiF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "d99e6c2f0203a1b10343ba40352a9a083767a880", "scripts": {"test": "jest", "build": "tsc", "gen-tree": "ts-node ./src/create-snapshot.ts", "pre-test": "jest --clear-cache", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "14.20.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "@changesets/cli": "^2.16.0", "regenerator-runtime": "^0.11.1", "@babel/preset-typescript": "^7.15.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.0.13_1666247325723_0.4225631797645082", "host": "s3://npm-registry-packages"}}, "1.0.14": {"name": "magic-bytes.js", "version": "1.0.14", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.0.14", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "4f878175d095182c086e83e76e7f900d2deeb83e", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.0.14.tgz", "fileCount": 26, "integrity": "sha512-Xz644ideT892A7l9JtYLlVp3KjQc8gHJP9rN6H2Mq8OOld6BMU7xiS9rFGYAXeY7Z/yHOYtFSLyFzL8/NaodPw==", "signatures": [{"sig": "MEYCIQDjZI+7/KcruyUteklth0iVTr8Cyazbo4uXACH/E2LFswIhANj3T1Y5JhRgcL54thOgBlmqPhFOAppsdfYJQ2KFtiXe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj42kgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrszxAAjrciGqG+5CRJ5L0LVPPXboL8YSWhByu1gS47UV/NsNaeqE0P\r\ny2AlkQ3jnfY9lVnlLpEcCkQvERfqT6M4l4+Qa77QNYMhZYnl7JXFNIvmSJzV\r\nc0dqdwI9Be88p3vTLe0kc0lsFLwScYelIr1+wkjLA3vVClwXlR0h8qzkNXXi\r\nmYj1ZhPk2iKDMCJPXmB6NlPLu7PQNAfKZ/LM304VjcJ3XeUgA52rJQkZCfdD\r\nXlazz2o+zeBV4ljJ7kiDqagXsamRZ0rborBPMX2VYSrY7CReymN19qhKg4FA\r\n5nUVBPMl0heQ/AZ/3uvwaXSr1glXBhNAlpyWBWx3vxPlTKSB/dbojHkwWOtP\r\nvh5kj0B4+BlYWS90wOPkUIR9DFoukDChFeO7cNLiTmzJQ3NzLV7TzdSRmTBt\r\nSkpcSRmQOwRCNbGnP9VXRmZWj/fOSTMXm+A2+/xz3iWIbxUAArGDf7NEYmyr\r\nN4XeiXcRJjm4snfWD7uqSiGYO2eAykW9Rsll2oKbdggHT0k2otpW7tMntcrr\r\nB/NXlLiWbud2vQj28nOiGaTbz3IIp6NpysrbRhEmxy7YXXeT7bIRh5pxMBlz\r\n6lqVEnL4vm/5JZ7/c8xzyJqeHNgy1t1xpDda4RmM0LH93Lu6U7FE7RUoOGtu\r\nyJhvzDRp5EQiLJuw7jz2RfA19q3ZRJg2MbI=\r\n=RiH6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "01c9ff6905f4c3d83cad8a44c60af59675fd1428", "scripts": {"test": "jest", "build": "tsc", "gen-tree": "ts-node ./src/create-snapshot.ts", "pre-test": "jest --clear-cache", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "14.21.2", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "@changesets/cli": "^2.16.0", "regenerator-runtime": "^0.11.1", "@babel/preset-typescript": "^7.15.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.0.14_1675847968154_0.4574146476791445", "host": "s3://npm-registry-packages"}}, "1.0.15": {"name": "magic-bytes.js", "version": "1.0.15", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.0.15", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "3c9d2b7d45bb8432482646b5f74bbf6725274616", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.0.15.tgz", "fileCount": 26, "integrity": "sha512-bpRmwbRHqongRhA+mXzbLWjVy7ylqmfMBYaQkSs6pac0z6hBTvsgrH0r4FBYd/UYVJBmS6Rp/O+oCCQVLzKV1g==", "signatures": [{"sig": "MEQCIEbQtda/DOOqIvL0b2GvYoUSm7yyLvVD4vaZxzTK3gGJAiB8lEH7/VHMqJBXQo8wXyD/gmWcogp/Swoj1fh4MG+kjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39553}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "dc7e346c8d1a6f2b8950e433b2f38264055266f7", "scripts": {"test": "jest", "build": "tsc", "gen-tree": "ts-node ./src/create-snapshot.ts", "pre-test": "jest --clear-cache", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "14.21.3", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "@changesets/cli": "^2.16.0", "regenerator-runtime": "^0.11.1", "@babel/preset-typescript": "^7.15.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.0.15_1689673890224_0.430691939299064", "host": "s3://npm-registry-packages"}}, "1.0.17": {"name": "magic-bytes.js", "version": "1.0.17", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.0.17", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "6265d568df98c02b523c0defdd18b86cb2cae883", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.0.17.tgz", "fileCount": 26, "integrity": "sha512-PEDpPzHpKe5AxkVmQrNPHFRvPN2ELkkj3eIg4IZO9JdhBiAY3aU53lgYXs9j8B7lpza+QiW0UA4QHCH7EskSeg==", "signatures": [{"sig": "MEQCICnK95eexcksHSG777eard77cYs5AKNPFbwWZV7GtuLDAiBjqiJ7LOD87dJOrSoV8mgHW5yDMDfsM9bGV4Uo4dnLaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42249}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "2d635af995bc90cc6d436fca02ff1cde191a5faa", "scripts": {"test": "jest", "build": "tsc", "gen-tree": "ts-node ./src/create-snapshot.ts", "pre-test": "jest --clear-cache", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "14.21.3", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "@changesets/cli": "^2.16.0", "regenerator-runtime": "^0.11.1", "@babel/preset-typescript": "^7.15.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.0.17_1694670764979_0.8972210584252835", "host": "s3://npm-registry-packages"}}, "1.0.18": {"name": "magic-bytes.js", "version": "1.0.18", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.0.18", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "d09aedc5cdbafc1d2495de40196df931d94bcd8d", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.0.18.tgz", "fileCount": 26, "integrity": "sha512-ydhkdNEaswgnSsyS2fhoJHBhLxe+u8c24a8qHaIYeAxW0KikwvZWslxEO80M0uoGkruQ4ZwzGEoUQvJ8EgNi+w==", "signatures": [{"sig": "MEYCIQDCwZvw7CZMT5bpVfVuifDjMJhhqRX5ajjs75ExjbdRjwIhAOA1OR7dYtLcNCGjNXwvKmEKdEzWM2CkKLjMvW39NKlN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42448}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "8b0d7a8d1ceca3d67b98d39b97566bcbd8c44890", "scripts": {"test": "jest", "build": "tsc", "gen-tree": "ts-node ./src/create-snapshot.ts", "pre-test": "jest --clear-cache", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "14.21.3", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "@changesets/cli": "^2.16.0", "regenerator-runtime": "^0.11.1", "@babel/preset-typescript": "^7.15.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.0.18_1695663657981_0.7723890699340834", "host": "s3://npm-registry-packages"}}, "1.0.19": {"name": "magic-bytes.js", "version": "1.0.19", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.0.19", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "0b46991c5f9553eff45655109e935c3e21b12a4c", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.0.19.tgz", "fileCount": 26, "integrity": "sha512-/088hrf0svd5aDFLXuWStA447/ZXv21sS/1uaaPELohYL8+QzcO3mD8JkEFe0DSAsU5ex+O2+12Zmiz7wIdA1Q==", "signatures": [{"sig": "MEUCIQCNytUb0mJKKOn0x8bCLuVJPvlN8Bb8ZqXCD4u8XmODJwIgStHvsecFt+GqRhgXGr7FKuVTFEoaNjMW4kV/gEObdHk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42619}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "48d6dfa62be6cba1c2f121fd1b1d4e876e567e44", "scripts": {"test": "jest", "build": "tsc", "gen-tree": "ts-node ./src/create-snapshot.ts", "pre-test": "jest --clear-cache", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\""}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "6.14.18", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "14.21.3", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "@changesets/cli": "^2.16.0", "regenerator-runtime": "^0.11.1", "@babel/preset-typescript": "^7.15.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.0.19_1695664296739_0.02505767259676417", "host": "s3://npm-registry-packages"}}, "1.0.20": {"name": "magic-bytes.js", "version": "1.0.20", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.0.20", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "797e92c0e6214f3476067f13c94d09120eaed0fc", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.0.20.tgz", "fileCount": 27, "integrity": "sha512-JUIOSflEhJLwuNuO1yy+MYY66TdefC+CtJeWgE9pCLCubxz56UxFjgcW3r/w2NopMAOS3hVa+W6MByO3M8UNLA==", "signatures": [{"sig": "MEUCIQD+yEjvDiQ1/1yFYOKgfEiQg1dpbZJehVKQK6c8ZjZ0lQIgFTR0Z6BM634+ROTjz0mlyI/1dOIRbk7L7ZI/XmzH5eo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44961}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "7529899594d6fa5d70acd20ebe0206ac2bfee089", "release": {"branches": ["master"]}, "scripts": {"dev": "vite", "test": "jest", "build": "tsc", "example": "webpack; open example/html/index.html", "gen-tree": "ts-node ./src/create-snapshot.ts", "pre-test": "jest --clear-cache", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\"", "semantic-release": "semantic-release"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "open": "^9.1.0", "vite": "^4.4.9", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "ts-loader": "^9.4.4", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "webpack-cli": "^5.1.4", "@changesets/cli": "^2.16.0", "semantic-release": "^22.0.5", "regenerator-runtime": "^0.11.1", "@babel/preset-typescript": "^7.15.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.0.20_1695758149156_0.4783216714328078", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "magic-bytes.js", "version": "1.1.0", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "a8d9206e765117828b904bdf631bf92e58356ac5", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.1.0.tgz", "fileCount": 25, "integrity": "sha512-xeocNQkU6Qi+ID4LoogvmyLP79wjf5NaYQfHn2z+T+oYdA1fw52PogA7IUEVgmEcpyLQVNiWn5gq7dUmXQ8tEg==", "signatures": [{"sig": "MEQCIGUfxZReVkjbPzvQlr8nJC6Z5s14dOZ/gQKphQWVApgTAiBI2L6Ss013PuE+r1swtkHBmM3YxVaIXIvDJSzd983UDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44956}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "78222e257a758061d9321047828e40b0db2794b1", "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"test": "jest", "build": "tsc", "pre-test": "jest --clear-cache", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\"", "example:html": "webpack --config example/html/webpack.config.js; open example/html/index.html", "example:webapp": "vite --config example/webapp/vite.config.js example/webapp", "semantic-release": "semantic-release", "build:example:webapp": "vite --config example/webapp/vite.config.js build example/webapp"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "open": "^9.1.0", "vite": "^4.4.9", "husky": "^8.0.3", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "ts-loader": "^9.4.4", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "webpack-cli": "^5.1.4", "@changesets/cli": "^2.16.0", "@commitlint/cli": "^17.7.1", "semantic-release": "^22.0.5", "regenerator-runtime": "^0.11.1", "@semantic-release/git": "^10.0.1", "@babel/preset-typescript": "^7.15.0", "@commitlint/config-conventional": "^17.7.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.1.0_1695759990399_0.919777032394292", "host": "s3://npm-registry-packages"}}, "1.2.0": {"name": "magic-bytes.js", "version": "1.2.0", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "265d094d6cf4775906c6dde66892058f4e39d1b3", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.2.0.tgz", "fileCount": 25, "integrity": "sha512-NFrX8tqiYYrIiMQ3f0UkqG8INKzF8Lz7Jo2c5Ut6b5/Bzp/sr2z6dQoXPSLVDaioM2N6/k+3sDnD/y4Xpx6lSQ==", "signatures": [{"sig": "MEYCIQCNcXwO+0v/gsZUuYACEOzAFMDI0gP7mIJMaRW3aF+hPQIhAMmjNoqKfhnaE0JanboY8NlpFoeUqy0yeGmCy6oGoqRF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45576}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "8a82af13fb1a053c57e031ed59be1088d32104bd", "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"test": "jest", "build": "tsc", "pre-test": "jest --clear-cache", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\"", "example:html": "webpack --config example/html/webpack.config.js; open example/html/index.html", "example:webapp": "vite --config example/webapp/vite.config.js example/webapp", "semantic-release": "semantic-release", "build:example:webapp": "vite --config example/webapp/vite.config.js build example/webapp"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "18.17.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "open": "^9.1.0", "vite": "^4.4.9", "husky": "^8.0.3", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "ts-loader": "^9.4.4", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "webpack-cli": "^5.1.4", "@changesets/cli": "^2.16.0", "@commitlint/cli": "^17.7.1", "semantic-release": "^22.0.5", "regenerator-runtime": "^0.11.1", "@semantic-release/git": "^10.0.1", "@babel/preset-typescript": "^7.15.0", "@commitlint/config-conventional": "^17.7.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.2.0_1695761727766_0.2839746166823671", "host": "s3://npm-registry-packages"}}, "1.3.0": {"name": "magic-bytes.js", "version": "1.3.0", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "65cc2faffadf88eb9a30e79d77707ddd01e04707", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.3.0.tgz", "fileCount": 25, "integrity": "sha512-NobauZB5uA4l7IqgjeXx0gn0qBIikI5iFwy/4zUG9+OKLy69/uzn+su5UZPoDfJtVu3ywV4HYAOjPYVPPQqAAA==", "signatures": [{"sig": "MEQCIBXJC/W24kLbkLtsLMoAQNUPkb0QxpVUFHdGgvBAft8mAiBJxiINViRARpjliuct7WKDYzSfgrr9/D8xqeB/pbDmfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46204}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "8b4fbdc8c0fd53f4f319de7f52031ade5c3a5286", "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"test": "jest", "build": "tsc", "pre-test": "jest --clear-cache", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\"", "example:html": "webpack --config example/html/webpack.config.js; open example/html/index.html", "example:webapp": "vite --config example/webapp/vite.config.js example/webapp", "semantic-release": "semantic-release", "build:example:webapp": "vite --config example/webapp/vite.config.js build example/webapp"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "open": "^9.1.0", "vite": "^4.4.9", "husky": "^8.0.3", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "ts-loader": "^9.4.4", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "webpack-cli": "^5.1.4", "@changesets/cli": "^2.16.0", "@commitlint/cli": "^17.7.1", "semantic-release": "^22.0.5", "regenerator-runtime": "^0.11.1", "@semantic-release/git": "^10.0.1", "@babel/preset-typescript": "^7.15.0", "@commitlint/config-conventional": "^17.7.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.3.0_1696013410787_0.8716648018329336", "host": "s3://npm-registry-packages"}}, "1.4.0": {"name": "magic-bytes.js", "version": "1.4.0", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "3ea9dac5157ef3825fa2d020fdf7c6bb761adb9e", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.4.0.tgz", "fileCount": 25, "integrity": "sha512-T1X7IeR1OHuZIN4JAkIg/F8sAs7w2VdRQ7mFW+J8DrNylCTnBJdTItBCkbNVp/6ZpR7EIVCs6O6/qsYQzyId0Q==", "signatures": [{"sig": "MEYCIQCDiMDy61Zi0p4moerdx18wm481kYYC+S7YmBwS/vsqjQIhAInEFfUb3Q82JKAj1MTlcLVRXooYOc3c0z3E1ndgg91k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48066}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "643549b42def964f86b404854289966ac0ed7961", "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"test": "jest", "build": "tsc", "pre-test": "jest --clear-cache", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\"", "example:html": "webpack --config example/html/webpack.config.js; open example/html/index.html", "example:webapp": "vite --config example/webapp/vite.config.js example/webapp", "semantic-release": "semantic-release", "build:example:webapp": "vite --config example/webapp/vite.config.js build example/webapp"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "open": "^9.1.0", "vite": "^4.4.9", "husky": "^8.0.3", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "ts-loader": "^9.4.4", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "webpack-cli": "^5.1.4", "@changesets/cli": "^2.16.0", "@commitlint/cli": "^17.7.1", "semantic-release": "^22.0.5", "regenerator-runtime": "^0.11.1", "@semantic-release/git": "^10.0.1", "@babel/preset-typescript": "^7.15.0", "@commitlint/config-conventional": "^17.7.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.4.0_1696272113854_0.5821870925224724", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "magic-bytes.js", "version": "1.5.0", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "f5531ca53e1c8dab5692b8dcfb360f7ca6c6b6bc", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.5.0.tgz", "fileCount": 25, "integrity": "sha512-wJkXvutRbNWcc37tt5j1HyOK1nosspdh3dj6LUYYAvF6JYNqs53IfRvK9oEpcwiDA1NdoIi64yAMfdivPeVAyw==", "signatures": [{"sig": "MEQCIAi0JHcrITPO0fufjzYaF1c84+Z0HGTT94ITYC57wL37AiAIgz1v+hwZj08K8y9E3p11hg3R/rx7ZZFObFjOY9ftFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48239}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "b471da70374beef0ad36aed8d2c6bca2cb589b92", "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"test": "jest", "build": "tsc", "pre-test": "jest --clear-cache", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\"", "example:html": "webpack --config example/html/webpack.config.js; open example/html/index.html", "example:webapp": "vite --config example/webapp/vite.config.js example/webapp", "semantic-release": "semantic-release", "build:example:webapp": "vite --config example/webapp/vite.config.js build example/webapp"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "18.18.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "open": "^9.1.0", "vite": "^4.4.9", "husky": "^8.0.3", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "ts-loader": "^9.4.4", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "webpack-cli": "^5.1.4", "@changesets/cli": "^2.16.0", "@commitlint/cli": "^17.7.1", "semantic-release": "^22.0.5", "regenerator-runtime": "^0.11.1", "@semantic-release/git": "^10.0.1", "@babel/preset-typescript": "^7.15.0", "@commitlint/config-conventional": "^17.7.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.5.0_1696272837881_0.701095503058847", "host": "s3://npm-registry-packages"}}, "1.6.0": {"name": "magic-bytes.js", "version": "1.6.0", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "ee4354ac41a9b86f410e2690bc25dc4c264fb55d", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.6.0.tgz", "fileCount": 26, "integrity": "sha512-eOGBE+NSCwU9dKKox93BPHjX4KSxIuiRY1/H1lkfxIagT0Llhs6bkRk8iqoP/0aeDl7FEZPa+ln5lay5mcNY4w==", "signatures": [{"sig": "MEYCIQCb2GllyRCGvCqEdgrrecqrwhTJBWOZ2EGr4UBFtgnQHAIhAMcT7D+A//RaTy+WD7XYTMggZi3WiVEYGlrL+QXyo3x+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48557}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "0e15f22c7bf127accd851a2011a4c203b2174119", "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"test": "jest", "build": "tsc", "pre-test": "jest --clear-cache", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\"", "example:html": "webpack --config example/html/webpack.config.js; open example/html/index.html", "example:webapp": "vite --config example/webapp/vite.config.js example/webapp", "semantic-release": "semantic-release", "build:example:webapp": "vite --config example/webapp/vite.config.js build example/webapp"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "18.18.2", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "open": "^9.1.0", "vite": "^4.4.9", "husky": "^8.0.3", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "ts-loader": "^9.4.4", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "webpack-cli": "^5.1.4", "@changesets/cli": "^2.16.0", "@commitlint/cli": "^17.7.1", "semantic-release": "^22.0.5", "regenerator-runtime": "^0.11.1", "@semantic-release/git": "^10.0.1", "@babel/preset-typescript": "^7.15.0", "@commitlint/config-conventional": "^17.7.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.6.0_1701624473506_0.027840682840682973", "host": "s3://npm-registry-packages"}}, "1.7.0": {"name": "magic-bytes.js", "version": "1.7.0", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "29ca1a137b508fa656ca35fe79683bf10246e0d5", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.7.0.tgz", "fileCount": 26, "integrity": "sha512-YzVU2+/hrjwx8xcgAw+ffNq3jkactpj+f1iSL4LonrFKhvnwDzHSqtFdk/MMRP53y9ScouJ7cKEnqYsJwsHoYA==", "signatures": [{"sig": "MEUCIBNzdV687TABt5iFE/0G2NGDYNUsjiH/CsfoGpQQgNNqAiEAm6uxy4Xt/kxHQmcc6Mw/BUSHlzvWI2PuVyE6OOk800w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49038}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "6b137faf0d374a544a8986fd62642a293d14950b", "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"test": "jest", "build": "tsc", "pre-test": "jest --clear-cache", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\"", "example:html": "webpack --config example/html/webpack.config.js; open example/html/index.html", "example:webapp": "vite --config example/webapp/vite.config.js example/webapp", "semantic-release": "semantic-release", "build:example:webapp": "vite --config example/webapp/vite.config.js build example/webapp"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "open": "^9.1.0", "vite": "^4.4.9", "husky": "^8.0.3", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "ts-loader": "^9.4.4", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "webpack-cli": "^5.1.4", "@changesets/cli": "^2.16.0", "@commitlint/cli": "^17.7.1", "semantic-release": "^22.0.5", "regenerator-runtime": "^0.11.1", "@semantic-release/git": "^10.0.1", "@babel/preset-typescript": "^7.15.0", "@commitlint/config-conventional": "^17.7.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.7.0_1702318838312_0.2040458490778383", "host": "s3://npm-registry-packages"}}, "1.8.0": {"name": "magic-bytes.js", "version": "1.8.0", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "8362793c60cd77c2dd77db6420be727192df68e2", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.8.0.tgz", "fileCount": 26, "integrity": "sha512-lyWpfvNGVb5lu8YUAbER0+UMBTdR63w2mcSUlhhBTyVbxJvjgqwyAf3AZD6MprgK0uHuBoWXSDAMWLupX83o3Q==", "signatures": [{"sig": "MEUCIQDXkFylCc151eziD1APqRSEHMp6BHzswUcWD+2rJG/SkwIgUZDDamXmL/lBIS/y2tLSVUTll1G07qvKyuEjVt7pLxU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49128}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "4beef5efca03e54bbe5b86640085347b4be62341", "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"test": "jest", "build": "tsc", "pre-test": "jest --clear-cache", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\"", "example:html": "webpack --config example/html/webpack.config.js; open example/html/index.html", "example:webapp": "vite --config example/webapp/vite.config.js example/webapp", "semantic-release": "semantic-release", "build:example:webapp": "vite --config example/webapp/vite.config.js build example/webapp"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "18.19.0", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "open": "^9.1.0", "vite": "^4.4.9", "husky": "^8.0.3", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "ts-loader": "^9.4.4", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "webpack-cli": "^5.1.4", "@changesets/cli": "^2.16.0", "@commitlint/cli": "^17.7.1", "semantic-release": "^22.0.5", "regenerator-runtime": "^0.11.1", "@semantic-release/git": "^10.0.1", "@babel/preset-typescript": "^7.15.0", "@commitlint/config-conventional": "^17.7.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.8.0_1704966280418_0.9800742511259939", "host": "s3://npm-registry-packages"}}, "1.9.0": {"name": "magic-bytes.js", "version": "1.9.0", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "2361623b20f0adcf768ed6a4c7ee6d07012225cb", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.9.0.tgz", "fileCount": 26, "integrity": "sha512-lPJRxy7rr/RLgImrYd0ifEXLrxr2fCgCCzvkgAaoEw9oBOSb0yVwAAHGxUgCcy8lzo9JaJ0qDuk6Q4pxUEYJoA==", "signatures": [{"sig": "MEUCIFV9DOcYd65s+QXBMA7pnBfnsOWO/BdddnlalZgAsd/HAiEAvPKaNhcxN0ac2AtHHqnbQKAuvfI2M5wP0w2rl0XuhHA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49059}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "614161c5b8e2fa8efe80fc51bff36020ebf2db0e", "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"test": "jest", "build": "tsc", "pre-test": "jest --clear-cache", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\"", "example:html": "webpack --config example/html/webpack.config.js; open example/html/index.html", "example:webapp": "vite --config example/webapp/vite.config.js example/webapp", "semantic-release": "semantic-release", "build:example:webapp": "vite --config example/webapp/vite.config.js build example/webapp"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "open": "^9.1.0", "vite": "^4.4.9", "husky": "^8.0.3", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "ts-loader": "^9.4.4", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "webpack-cli": "^5.1.4", "@changesets/cli": "^2.16.0", "@commitlint/cli": "^17.7.1", "semantic-release": "^22.0.5", "regenerator-runtime": "^0.11.1", "@semantic-release/git": "^10.0.1", "@babel/preset-typescript": "^7.15.0", "@commitlint/config-conventional": "^17.7.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.9.0_1709215783408_0.8015429844000099", "host": "s3://npm-registry-packages"}}, "1.10.0": {"name": "magic-bytes.js", "version": "1.10.0", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.10.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "c41cf4bc2f802992b05e64962411c9dd44fdef92", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.10.0.tgz", "fileCount": 26, "integrity": "sha512-/k20Lg2q8LE5xiaaSkMXk4sfvI+9EGEykFS4b0CHHGWqDYU0bGUFSwchNOMA56D7TCs9GwVTkqe9als1/ns8UQ==", "signatures": [{"sig": "MEUCIQCTJyDUwZHMtjx2f4Y8s22N7Mbm6dB2B1Lps02xfTWqwgIgQ3+RSRScPMnLh4XrN8Wl/4OfgfGhAMdVF6Kdd196QuM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49122}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "8a46b2a6dfbdd7717b6b40b6cb90cbfe158cf56c", "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"test": "jest", "build": "tsc", "pre-test": "jest --clear-cache", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\"", "example:html": "webpack --config example/html/webpack.config.js; open example/html/index.html", "example:webapp": "vite --config example/webapp/vite.config.js example/webapp", "semantic-release": "semantic-release", "build:example:webapp": "vite --config example/webapp/vite.config.js build example/webapp"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "18.19.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "open": "^9.1.0", "vite": "^4.4.9", "husky": "^8.0.3", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "ts-loader": "^9.4.4", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "webpack-cli": "^5.1.4", "@changesets/cli": "^2.16.0", "@commitlint/cli": "^17.7.1", "semantic-release": "^22.0.5", "regenerator-runtime": "^0.11.1", "@semantic-release/git": "^10.0.1", "@babel/preset-typescript": "^7.15.0", "@commitlint/config-conventional": "^17.7.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.10.0_1709223922580_0.31369663502696254", "host": "s3://npm-registry-packages"}}, "1.11.0": {"name": "magic-bytes.js", "version": "1.11.0", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.11.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "b16cd1b412771ae4c9f624111b661b510fb32e52", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.11.0.tgz", "fileCount": 21, "integrity": "sha512-nVmadqN9gam80tdnn74qjFCKgldwzv1+96XmeCvR3bY7wNn2PjHMnRakOWC6+32g133vgZOjUiYgswIxohffzA==", "signatures": [{"sig": "MEUCIDv/WEO7a5EUuQB784tvoHrRtRtnAG4yeGR4HmSiTFOBAiEAzJmkHrgd+ViIfgNJION35dHuDb5so0D8Qu/PxsC5Lf8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 52501}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "7bf7f39b2d50f9c6913d086cdffdf4688f534a30", "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"test": "jest", "build": "tsc", "prepare": "npm run build", "pre-test": "jest --clear-cache", "prebuild": "<PERSON><PERSON><PERSON> dist", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\"", "example:html": "webpack --config example/html/webpack.config.js; open example/html/index.html", "example:webapp": "vite --config example/webapp/vite.config.js example/webapp", "semantic-release": "semantic-release", "build:example:webapp": "vite --config example/webapp/vite.config.js build example/webapp"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "18.20.8", "dependencies": {"rimraf": "^6.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "open": "^9.1.0", "vite": "^4.4.9", "husky": "^8.0.3", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "ts-loader": "^9.4.4", "typescript": "^4.3.5", "@types/jest": "^27.0.1", "@types/node": "^16.6.2", "webpack-cli": "^5.1.4", "@commitlint/cli": "^17.7.1", "semantic-release": "^22.0.5", "regenerator-runtime": "^0.11.1", "@semantic-release/git": "^10.0.1", "@babel/preset-typescript": "^7.15.0", "@commitlint/config-conventional": "^17.7.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.11.0_1744873706123_0.3468388126259323", "host": "s3://npm-registry-packages-npm-production"}}, "1.12.0": {"name": "magic-bytes.js", "version": "1.12.0", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "magic-bytes.js@1.12.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "dist": {"shasum": "b0d8064f49bfef266da73bd316bb1a327a57c925", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.12.0.tgz", "fileCount": 21, "integrity": "sha512-UVA53KPuZEbM9aumq0iQpNOJFXMEBHKLavHgtrVROonuxAw/kXF0uj50AhUM0yxYsCocXt4IOEHSDEA4RZNJKg==", "signatures": [{"sig": "MEUCIQCQWDjo8fdpHmxS3+wM8Qq/py8Jlvy2ooNDUdYzmgQT+AIgfRgB/5X1gsZzIFAfa1WVEqldWF24SH6e/0oNMXjACVE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 56432}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.js", "gitHead": "4b771687d1baf0aa38e409b55fd2f2746c1d14ae", "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"test": "jest", "build": "tsc", "prepare": "npm run build", "pre-test": "jest --clear-cache", "prebuild": "<PERSON><PERSON><PERSON> dist", "prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\"", "example:html": "webpack --config example/html/webpack.config.js; open example/html/index.html", "example:webapp": "vite --config example/webapp/vite.config.js example/webapp", "semantic-release": "semantic-release", "build:example:webapp": "vite --config example/webapp/vite.config.js build example/webapp"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "_npmVersion": "10.1.0", "description": "Detect Filetype by bytes", "directories": {}, "_nodeVersion": "18.20.8", "dependencies": {"rimraf": "^6.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "^27.0.6", "open": "^9.1.0", "vite": "^4.4.9", "husky": "^8.0.3", "ts-jest": "^27.0.5", "ts-node": "^10.2.1", "prettier": "^2.6.1", "ts-loader": "^9.4.4", "typescript": "^4.3.5", "@types/jest": "^27.5.2", "@types/node": "^16.6.2", "webpack-cli": "^5.1.4", "@commitlint/cli": "^17.7.1", "semantic-release": "^22.0.5", "regenerator-runtime": "^0.11.1", "@semantic-release/git": "^10.0.1", "@babel/preset-typescript": "^7.15.0", "@commitlint/config-conventional": "^17.7.0", "prettier-plugin-organize-imports": "^2.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/magic-bytes.js_1.12.0_1745616849396_0.8416336606251653", "host": "s3://npm-registry-packages-npm-production"}}, "1.12.1": {"name": "magic-bytes.js", "version": "1.12.1", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "release": {"branches": ["master"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"]}, "scripts": {"prettier": "prettier --write \"{src,__{tests,mocks}__}/**/*.{tsx,ts}\"", "pre-test": "jest --clear-cache", "prebuild": "<PERSON><PERSON><PERSON> dist", "test": "jest", "build": "tsc", "example:html": "webpack --config example/html/webpack.config.js; open example/html/index.html", "example:webapp": "vite --config example/webapp/vite.config.js example/webapp", "build:example:webapp": "vite --config example/webapp/vite.config.js build example/webapp", "semantic-release": "semantic-release", "prepare": "npm run build"}, "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "author": {"name": "<PERSON>"}, "license": "MIT", "description": "Detect Filetype by bytes", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "devDependencies": {"@babel/preset-typescript": "^7.15.0", "@commitlint/cli": "^17.7.1", "@commitlint/config-conventional": "^17.7.0", "@semantic-release/git": "^10.0.1", "@types/jest": "^27.5.2", "@types/node": "^16.6.2", "husky": "^8.0.3", "jest": "^27.0.6", "open": "^9.1.0", "prettier": "^2.6.1", "prettier-plugin-organize-imports": "^2.3.4", "regenerator-runtime": "^0.11.1", "semantic-release": "^22.0.5", "ts-jest": "^27.0.5", "ts-loader": "^9.4.4", "ts-node": "^10.2.1", "typescript": "^4.3.5", "vite": "^4.4.9", "webpack-cli": "^5.1.4", "rimraf": "^6.0.1"}, "_id": "magic-bytes.js@1.12.1", "gitHead": "359f5ef16134fafb39180799392b2075b0df222a", "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "_nodeVersion": "18.20.8", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-ThQLOhN86ZkJ7qemtVRGYM+gRgR8GEXNli9H/PMvpnZsE44Xfh3wx9kGJaldg314v85m+bFW6WBMaVHJc/c3zA==", "shasum": "031fedceb1fc652c1ccd917c6b45a6e8d6554245", "tarball": "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.12.1.tgz", "fileCount": 21, "unpackedSize": 56479, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCFWAqCa84yEAjX6n8IG4RwBxpZooGIskau/mGZaJdu/QIgLwVdl2jPb+7nTQz9+h1CDdgX1NJqVM/cJJfXbgS0P+I="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/magic-bytes.js_1.12.1_1745650576196_0.1258582365305918"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-03-29T18:52:10.473Z", "modified": "2025-04-26T06:56:16.533Z", "0.1.0": "2018-03-29T18:52:10.643Z", "0.1.1": "2018-03-29T19:12:00.459Z", "0.1.2": "2018-03-29T19:29:33.766Z", "0.1.2-testing": "2018-03-29T19:37:45.497Z", "0.1.2-testing-1": "2018-03-29T19:54:42.806Z", "0.1.2-testing-2": "2018-03-29T19:57:39.800Z", "0.1.2-testing-3": "2018-03-29T20:02:12.951Z", "0.2.0": "2018-03-29T21:03:10.224Z", "0.2.1": "2018-03-30T09:29:17.881Z", "0.2.2": "2018-03-30T09:46:27.976Z", "0.2.3": "2018-03-30T15:41:21.574Z", "0.2.4": "2018-03-30T15:42:45.534Z", "0.2.5": "2018-03-30T15:51:37.227Z", "0.3.0": "2018-03-30T17:56:12.592Z", "1.0.0": "2020-06-06T07:35:05.560Z", "1.0.1": "2021-08-20T11:41:41.506Z", "1.0.2": "2021-08-20T12:01:27.133Z", "1.0.3": "2021-08-21T09:28:17.134Z", "1.0.4": "2021-12-06T13:46:43.419Z", "1.0.5": "2021-12-07T17:47:58.101Z", "1.0.6": "2021-12-10T06:35:39.571Z", "1.0.7": "2022-03-01T22:03:56.848Z", "1.0.8": "2022-03-17T10:25:36.441Z", "1.0.9": "2022-03-28T17:00:39.584Z", "1.0.10": "2022-03-28T17:05:11.198Z", "1.0.11": "2022-04-26T14:28:02.236Z", "1.0.12": "2022-05-12T14:13:39.051Z", "1.0.13": "2022-10-20T06:28:45.910Z", "1.0.14": "2023-02-08T09:19:28.307Z", "1.0.15": "2023-07-18T09:51:30.404Z", "1.0.17": "2023-09-14T05:52:45.171Z", "1.0.18": "2023-09-25T17:40:58.262Z", "1.0.19": "2023-09-25T17:51:37.047Z", "1.0.20": "2023-09-26T19:55:49.339Z", "1.1.0": "2023-09-26T20:26:30.664Z", "1.2.0": "2023-09-26T20:55:27.922Z", "1.3.0": "2023-09-29T18:50:10.994Z", "1.4.0": "2023-10-02T18:41:54.248Z", "1.5.0": "2023-10-02T18:53:58.167Z", "1.6.0": "2023-12-03T17:27:53.739Z", "1.7.0": "2023-12-11T18:20:38.560Z", "1.8.0": "2024-01-11T09:44:40.560Z", "1.9.0": "2024-02-29T14:09:43.607Z", "1.10.0": "2024-02-29T16:25:22.756Z", "1.11.0": "2025-04-17T07:08:26.298Z", "1.12.0": "2025-04-25T21:34:09.590Z", "1.12.1": "2025-04-26T06:56:16.369Z"}, "bugs": {"url": "https://github.com/LarsKoelpin/magic-bytes/issues"}, "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/LarsKoelpin/magic-bytes#readme", "keywords": ["magic-bytes", "mime", "filetype", "file", "extension", "magic byte", "magic number", "mime", "mimetype", "validation", "javascript", "upload"], "repository": {"url": "git+https://github.com/LarsKoelpin/magic-bytes.git", "type": "git"}, "description": "Detect Filetype by bytes", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# Magic bytes\n\n[![Build and test](https://github.com/LarsKoelpin/magic-bytes/actions/workflows/build-and-test.yml/badge.svg)](https://github.com/LarsKoelpin/magic-bytes/actions/workflows/build-and-test.yml)\n\nMagic Bytes is a javascript library analyzing the first bytes of a file to tell you its type. \nUse it inside your browser or serversided using nodejs.\n\nThe procedure is based on https://en.wikipedia.org/wiki/List_of_file_signatures.\n\n\n> [!NOTE]  \n> A small note on versioning.\n> Strictly speaking, each new filetype which is supported by this library can break someones' API.\n> Please note that this library adds new filetypes with minor release.\n> This means files, which validate to \"null\" in some versions, may find a result in a new version.\n> \n> Or in some cases the library will find more results, than before. So don't depend on the found-array size in\n> any shape or form.\n> Filetypes will not be removed though.\n\n# Installation\nRun `npm install magic-bytes.js`\n\n\n# Interactive example\nThere is an interactive example present at https://larskoelpin.github.io/magic-bytes/.\n\n# Usage\n\nThe following functions are available:\n* `filetypeinfo(bytes: number[])` Contains typeinformation like name, extension and mime type: `[{typename: \"zip\"}, {typename: \"jar\"}]`\n* `filetypename(bytes: number[])` : Contains type names only: `[\"zip\", \"jar\"]`\n* `filetypemime(bytes: number[])` : Contains type mime types only: `[\"application/zip\", \"application/jar\"]`\n* `filetypeextension(bytes: number[])` : Contains type extensions only: `[\"zip\", \"jar\"]`\n* `register(fileType: string, string[])`: registers a custom signature\n\nBoth function return an empty array `[]` otherwise, which means it could not detect the file signature. Keep in mind that\ntxt files for example fall in this category.\n\nYou don't have to load the whole file in memory. For validating a file uploaded to S3 using Lambda for example, it may be  \nenough to load the files first 100 bytes and validate against them.  This is especially useful for big files.\n\nsee examples for practical usage.\n\nOn server:\n```javascript\nimport filetype from 'magic-bytes.js'\n\nfiletype(fs.readFileSync(\"myimage.png\")) // [\"png\"]\n```\n\n\nTo run an HTML-Example checkout the project and run\n\n```\nnpm install; npm run example\n```\n\nThis opens an HTML example using magic bytes as a window variable. It kinda looks like that.\n\n```html\n<input type=\"file\" id=\"file\" />\n\n <script src=\"node_modules/magic-bytes.js/dist/browser.js\" type=\"application/javascript\"></script>\n<script>\n    document.getElementById(\"file\").addEventListener('change', (event, x) => {\n      const fileReader = new FileReader();\n      fileReader.onloadend = (f) => {\n        const bytes = new Uint8Array(f.target.result);\n        console.log(\"Possible filetypes: \" + filetypeinfo(bytes))\n      }\n      fileReader.readAsArrayBuffer(event.target.files[0])\n    })\n</script>\n```\n\n\n# Tests\nRun  `npm test`\n\n# Example\nSee examples/\n\n# How does it work\nThe `create-snapshot.js` creates a new tree. The tree has a similar shape to the following \n```json\n{\n  \"0x47\": {\n    \"0x49\": {\n      \"0x46\": {\n        \"0x38\": {\n          \"0x37\": {\n            \"0x61\": {\n              \"matches\": [\n                {\n                  \"typename\": \"gif\",\n                  \"mime\": \"image/gif\",\n                  \"extension\": \"gif\"\n                }\n              ]\n            }\n          },\n        }\n      }\n    }\n  }\n}\n```\n\nIt acts as a giant lookup map for the given byte signatures.\n", "readmeFilename": "README.md"}