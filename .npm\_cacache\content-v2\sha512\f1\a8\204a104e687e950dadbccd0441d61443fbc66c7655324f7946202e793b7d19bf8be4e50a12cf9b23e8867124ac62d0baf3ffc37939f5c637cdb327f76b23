{"_id": "get-stream", "_rev": "39-a7ced5169df832ce17d0c36d6c29cd30", "name": "get-stream", "description": "Get a stream as a string, Buffer, ArrayBuffer or array", "dist-tags": {"latest": "9.0.1"}, "versions": {"1.0.0": {"name": "get-stream", "version": "1.0.0", "description": "Get a stream as a string or buffer", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/get-stream"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.12.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["get", "stream", "concat", "string", "str", "text", "buffer", "process", "read", "data", "readable"], "devDependencies": {"ava": "*", "buffer-equals": "^1.0.3", "xo": "*"}, "xo": {"ignores": ["test.js"]}, "gitHead": "56bf66ad5a924be824a6603d000c68ca394f71f5", "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "homepage": "https://github.com/sindresorhus/get-stream", "_id": "get-stream@1.0.0", "_shasum": "556ef2adce7c20faf0d60aa627ccfd9d03406d81", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "556ef2adce7c20faf0d60aa627ccfd9d03406d81", "tarball": "https://registry.npmjs.org/get-stream/-/get-stream-1.0.0.tgz", "integrity": "sha512-qaHCUz8pjO7Hjq10WuCWq5HobSYsTQvS6UEEfhFcveC84DJp/xR1I/kBHHqK20IBzn8SSJ7WUmXm/KXbxqZg6Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDcEYWStK1T3wSW+Bhyc4HNT+nW5MbP6m0phgZyrvv9/AIgL8uy0V2pLbuYg1/DsOvSCyAmmVBqIB+QlV4lXpXmci4="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "1.1.0": {"name": "get-stream", "version": "1.1.0", "description": "Get a stream as a string or buffer", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/get-stream"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.12.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["get", "stream", "concat", "string", "str", "text", "buffer", "process", "read", "data", "readable"], "devDependencies": {"ava": "*", "buffer-equals": "^1.0.3", "xo": "*"}, "xo": {"ignores": ["test.js"]}, "gitHead": "05c19972d3556efa6e44eb8182118c9410422caf", "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "homepage": "https://github.com/sindresorhus/get-stream", "_id": "get-stream@1.1.0", "_shasum": "554659093606c1b5284218be65f68385c9f0c18b", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "554659093606c1b5284218be65f68385c9f0c18b", "tarball": "https://registry.npmjs.org/get-stream/-/get-stream-1.1.0.tgz", "integrity": "sha512-H3I7qgHPZvoAZsNZ6In79TmbuDKA9PESQ0lgJHs9qZjEXXS+IoWin6PvIF5Q0RHGkvcC8A6oRBoVin1H4ScTlw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFXeJwQfb8b489fdgBFWPu4D/RO+aqcvTELydyIT+Ba+AiBSb32r87wv8RSIZ/8ry8pLkPQ/y+zQSBTBRFJ79w41sw=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "get-stream", "version": "2.0.0", "description": "Get a stream as a string or buffer", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/get-stream"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["get", "stream", "concat", "string", "str", "text", "buffer", "process", "read", "data", "readable"], "dependencies": {"pinkie-promise": "^2.0.0"}, "devDependencies": {"ava": "*", "buffer-equals": "^1.0.3", "xo": "*"}, "gitHead": "5477c3887b7af31f49c276e5c82b733fd1f6c5cf", "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "homepage": "https://github.com/sindresorhus/get-stream", "_id": "get-stream@2.0.0", "_shasum": "c6457200c1b5d3291cf45711217d9f2f87d43af5", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.3.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c6457200c1b5d3291cf45711217d9f2f87d43af5", "tarball": "https://registry.npmjs.org/get-stream/-/get-stream-2.0.0.tgz", "integrity": "sha512-i407lScPC1OfcVkjCkDY61jXEpLCdqju6XIiE2X9SoOWZOHsR6iPdk1eQykO+jppplZAkyN6UQuU9i/zvY90Kg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQtAXnderXj3Urny9uCz3yPFoQwxvZrswnxlBJGpc/dwIhAI04vKaj6AzTdxIkvQ3z1JWFv0XnMmA+3Y1jcrwpapj4"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/get-stream-2.0.0.tgz_1456982714460_0.6460716719739139"}, "directories": {}}, "2.1.0": {"name": "get-stream", "version": "2.1.0", "description": "Get a stream as a string, buffer, or array", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stream.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["get", "stream", "concat", "string", "str", "text", "buffer", "read", "data", "readable", "readablestream", "array", "object", "obj"], "dependencies": {"pinkie-promise": "^2.0.0"}, "devDependencies": {"ava": "*", "buffer-equals": "^1.0.3", "into-stream": "^2.0.1", "xo": "*"}, "gitHead": "52f695aa78add2c6e6731e5f292dc82ce2e02b8a", "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "homepage": "https://github.com/sindresorhus/get-stream#readme", "_id": "get-stream@2.1.0", "_shasum": "5a39f4167aae4f30433f95cf211e315baefc62c9", "_from": ".", "_npmVersion": "2.15.0", "_nodeVersion": "4.4.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "5a39f4167aae4f30433f95cf211e315baefc62c9", "tarball": "https://registry.npmjs.org/get-stream/-/get-stream-2.1.0.tgz", "integrity": "sha512-RakEVbpvjcjnJbftoYnUVq3rdJIa0WYZhadQPJn7UjzhEPbDojLddGA4DI3T3sn73NGCUG0dYcFEO71gAMx2LQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDrhbVLKp611gndV5I7nG17kwPO2Yd7FVH+xWnshIPeEAIgNm4VLTiURJZlDnIESp57Zg5BgndWSDREXqq40kZ4F6Q="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/get-stream-2.1.0.tgz_1461857619841_0.05684150545857847"}, "directories": {}}, "2.2.0": {"name": "get-stream", "version": "2.2.0", "description": "Get a stream as a string, buffer, or array", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stream.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["get", "stream", "concat", "string", "str", "text", "buffer", "read", "data", "readable", "readablestream", "array", "object", "obj"], "dependencies": {"object-assign": "^4.0.1", "pinkie-promise": "^2.0.0"}, "devDependencies": {"ava": "*", "buffer-equals": "^1.0.3", "into-stream": "^2.0.1", "xo": "*"}, "gitHead": "162fa9978a4bfaa9c04581495e0303132af95035", "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "homepage": "https://github.com/sindresorhus/get-stream#readme", "_id": "get-stream@2.2.0", "_shasum": "9073a42b9fbebb2411ed7f1c04b2f5c3d5be289d", "_from": ".", "_npmVersion": "3.8.9", "_nodeVersion": "4.4.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "9073a42b9fbebb2411ed7f1c04b2f5c3d5be289d", "tarball": "https://registry.npmjs.org/get-stream/-/get-stream-2.2.0.tgz", "integrity": "sha512-LfJNy/ffh6GDJO9DG8hzQPEkDj/vpH/QqfnOuA8TKmRaZ8tK/zr75V0E0AodMXbHvxpQRzZWGAwwTBb+mxquWQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCOAuIK/ecr57d5IdiYJb1k3qyHphRaspMhmPf/oRBA7wIhAN2gJQoFekEWnitF6aBSVxBDAx8O2clj2BL3/5OAFOiQ"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/get-stream-2.2.0.tgz_1462049442368_0.29556937725283206"}, "directories": {}}, "2.2.1": {"name": "get-stream", "version": "2.2.1", "description": "Get a stream as a string, buffer, or array", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stream.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js", "buffer-stream.js"], "keywords": ["get", "stream", "promise", "concat", "string", "str", "text", "buffer", "read", "data", "readable", "readablestream", "array", "object", "obj"], "dependencies": {"object-assign": "^4.0.1", "pinkie-promise": "^2.0.0"}, "devDependencies": {"ava": "*", "buffer-equals": "^1.0.3", "into-stream": "^2.0.1", "xo": "*"}, "gitHead": "4e75377d963f6ee4fb9022baf2ffe46cba350110", "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "homepage": "https://github.com/sindresorhus/get-stream#readme", "_id": "get-stream@2.2.1", "_shasum": "ed7e336824f09ab00e37f0296ba42274590ad1c6", "_from": ".", "_npmVersion": "2.15.0", "_nodeVersion": "4.4.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ed7e336824f09ab00e37f0296ba42274590ad1c6", "tarball": "https://registry.npmjs.org/get-stream/-/get-stream-2.2.1.tgz", "integrity": "sha512-bbgISKsWGJ2dYbIb22FucSYdMPZ56AzwRy8wwV5fpxolPobL14/pjoSUVvrraPfLv5Gf80toZezCtEeksraW9g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFDManm/TN+a4DoXYwKiuaQiK4F2ZAdID35onxtdHrJyAiEAweelpfcwoHuR/bJbbPxQ+GEFk6suQQQzKf7yh+nVS50="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/get-stream-2.2.1.tgz_1465235691565_0.8080171663314104"}, "directories": {}}, "2.3.0": {"name": "get-stream", "version": "2.3.0", "description": "Get a stream as a string, buffer, or array", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stream.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js", "buffer-stream.js"], "keywords": ["get", "stream", "promise", "concat", "string", "str", "text", "buffer", "read", "data", "readable", "readablestream", "array", "object", "obj"], "dependencies": {"object-assign": "^4.0.1", "pinkie-promise": "^2.0.0"}, "devDependencies": {"ava": "*", "buffer-equals": "^1.0.3", "into-stream": "^2.0.1", "xo": "*"}, "gitHead": "80081b6fe8bcacdef062ac54e0de4a3b8ec6e61c", "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "homepage": "https://github.com/sindresorhus/get-stream#readme", "_id": "get-stream@2.3.0", "_shasum": "3790b4977e742dd3ad9d61e9fe711865f690c010", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "5.10.1", "_npmUser": {"name": "jamestalmage", "email": "<EMAIL>"}, "dist": {"shasum": "3790b4977e742dd3ad9d61e9fe711865f690c010", "tarball": "https://registry.npmjs.org/get-stream/-/get-stream-2.3.0.tgz", "integrity": "sha512-5NutBTwqwXT1raSO6t73tulXZhwIPMkMmchoX3DE/Hou8RHKFBTuN3rf2/6yHBUZ/h0Sle1IiyJc2blnGhCvgg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH8BDgLwlDChLteAQux1w/gWZUrbERPgofdufoCPohovAiEAsahSPtp5IatbQ9ActPly1hB16ZKqzPwKfdxf9sFQ80U="}]}, "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/get-stream-2.3.0.tgz_1465263104906_0.18662143824622035"}, "directories": {}}, "2.3.1": {"name": "get-stream", "version": "2.3.1", "description": "Get a stream as a string, buffer, or array", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stream.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js", "buffer-stream.js"], "keywords": ["get", "stream", "promise", "concat", "string", "str", "text", "buffer", "read", "data", "readable", "readablestream", "array", "object", "obj"], "dependencies": {"object-assign": "^4.0.1", "pinkie-promise": "^2.0.0"}, "devDependencies": {"ava": "*", "buffer-equals": "^1.0.3", "into-stream": "^2.0.1", "xo": "*"}, "gitHead": "1607196593ead1d000caae8aec37ea4bed5f0797", "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "homepage": "https://github.com/sindresorhus/get-stream#readme", "_id": "get-stream@2.3.1", "_shasum": "5f38f93f346009666ee0150a054167f91bdd95de", "_from": ".", "_npmVersion": "2.15.9", "_nodeVersion": "4.5.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "5f38f93f346009666ee0150a054167f91bdd95de", "tarball": "https://registry.npmjs.org/get-stream/-/get-stream-2.3.1.tgz", "integrity": "sha512-AUGhbbemXxrZJRD5cDvKtQxLuYaIbNtDTK8YqupCI393Q2KSTreEsLUN3ZxAWFGiKTzL6nKuzfcIvieflUX9qA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHMUUz/mfpX6Lc4nPIii9A1HBwKWuYagbZ/Y2ExFcHJQAiEAgEXjdtcyKcFYxkPtmAe8GPttTaY6x4J1LUkinEI9N+o="}]}, "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/get-stream-2.3.1.tgz_1473873226777_0.8189526884816587"}, "directories": {}}, "3.0.0": {"name": "get-stream", "version": "3.0.0", "description": "Get a stream as a string, buffer, or array", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stream.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js", "buffer-stream.js"], "keywords": ["get", "stream", "promise", "concat", "string", "str", "text", "buffer", "read", "data", "consume", "readable", "readablestream", "array", "object", "obj"], "devDependencies": {"ava": "*", "into-stream": "^3.0.0", "xo": "*"}, "xo": {"esnext": true}, "gitHead": "3023bc31dec6680dda4f935a2b320b3a4f18c815", "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "homepage": "https://github.com/sindresorhus/get-stream#readme", "_id": "get-stream@3.0.0", "_shasum": "8e943d1358dc37555054ecbe2edb05aa174ede14", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.6.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "8e943d1358dc37555054ecbe2edb05aa174ede14", "tarball": "https://registry.npmjs.org/get-stream/-/get-stream-3.0.0.tgz", "integrity": "sha512-GlhdIUuVakc8SJ6kK0zAFbiGzRFzNnY4jUuEbV9UROo4Y+0Ny4fjvcZFVTeDA4odpFyOQzaw6hXukJSq/f28sQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCA8FxIANytHUEpLYocdb1uIHpAU7lWK7pRdSrDjjBqHgIhAN/uOcdpGPqjVHlWoga7dbjYjv9CxHoHKYdHE9j5qSxf"}]}, "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/get-stream-3.0.0.tgz_1479869385406_0.47692562686279416"}, "directories": {}}, "4.0.0": {"name": "get-stream", "version": "4.0.0", "description": "Get a stream as a string, buffer, or array", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stream.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "files": ["index.js", "buffer-stream.js"], "keywords": ["get", "stream", "promise", "concat", "string", "text", "buffer", "read", "data", "consume", "readable", "readablestream", "array", "object"], "dependencies": {"pump": "^3.0.0"}, "devDependencies": {"ava": "*", "into-stream": "^3.0.0", "xo": "*"}, "gitHead": "4d1b52a0bac62c0f53f18a7f4bf996689fdf85b6", "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "homepage": "https://github.com/sindresorhus/get-stream#readme", "_id": "get-stream@4.0.0", "_npmVersion": "6.3.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-FneLKMENeOR7wOK0/ZXCh+lwqtnPwkeunJjRN28LPqzGvNAhYvrTAhXv6xDm4vsJ0M7lcRbIYHQudKsSy2RtSQ==", "shasum": "9e074cb898bd2b9ebabb445a1766d7f43576d977", "tarball": "https://registry.npmjs.org/get-stream/-/get-stream-4.0.0.tgz", "fileCount": 5, "unpackedSize": 7677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbUXMCRA9TVsSAnZWagAAr3QQAJasZeqMhzqffOw+HBF9\nEfxhjkOVvkr4RKjz4bvmY/C8SBjBtMrHbzaTo0p8+If3ooEywLj94Z52gQ7q\n6YeWm7bav864fIBsLdjGcw/vK3TgLDxgjODvhPrG9Gby5FUHB0Yv+Zgr58E1\nEl7o3qCfsh2UHB4LcY4e2xwhSMPQIhyjIKz8yUuNiokWP7GmdNRbhMqTvB+6\nq4T6ijngj+kSwY0nZguGIQ/3az4N1wtmlrSVfrNL6h7flGAlzaiZnK9uo0Br\ncQ4t3DeYe/pMOQJ//rI+N4t0OOACg9IoCRm9Wmcaw7PB7QtJmsj0I36EYL5r\nBRUi9BI378Dc5IoRz2//mpUfNXtxsiFJvKvDFOxz6pmcgCNEvq27PRyZV3R5\n7xapCf0uhHiGLWSfAGn2bXPKOMjUvbF6lQYAaBwVc2vDsF2cfOX2adbyx21y\nlqmKsT+tTQbrkQE2pcmYfpVjEWufeWTj9EJyS8MY5TmOIpANRTa+kJD5/poz\nrd5faLEaodCbs/zB1NNL8mnGIsbkj30/HFy1c5Q7TE8DBe/5ePSQAT9Mo9qr\nxO6K1fyAViubWvuY/msrcK5JKwd9A19GmWG/8/ItZOmGR8hE52i4fZRjiaTp\nppyFd9Lc17voTfpYRXzirmMTZZJUSltV3qYwWH7DqHUl3tjeFrqu0YZl6yNa\nYbez\r\n=dW5O\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEiD0II+F45J/nwKz3eakQVyDy1B7woGaVzkPtQzdVHDAiBDUajFdrLBCH3r6r80KzEXRPI9GJdrvyGqCxmMkE+97Q=="}]}, "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/get-stream_4.0.0_1533887947733_0.9848270807807806"}, "_hasShrinkwrap": false}, "4.1.0": {"name": "get-stream", "version": "4.1.0", "description": "Get a stream as a string, buffer, or array", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stream.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "keywords": ["get", "stream", "promise", "concat", "string", "text", "buffer", "read", "data", "consume", "readable", "readablestream", "array", "object"], "dependencies": {"pump": "^3.0.0"}, "devDependencies": {"ava": "*", "into-stream": "^3.0.0", "xo": "*"}, "gitHead": "9ddd965a50132bd70ad42befbcce08a9005c78f7", "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "homepage": "https://github.com/sindresorhus/get-stream#readme", "_id": "get-stream@4.1.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.11.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w==", "shasum": "c1b255575f3dc21d59bfc79cd3d2b46b1c3a54b5", "tarball": "https://registry.npmjs.org/get-stream/-/get-stream-4.1.0.tgz", "fileCount": 5, "unpackedSize": 7877, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbszZQCRA9TVsSAnZWagAAIHsP/3QXvMkXi+6qIzjHY2xs\n4iRqSd7jFgg/5Ceonz87k+Siu9ogewx7aHE5umhfG+4P9WyTjZObby+nYjFq\n622wXR/4JKBZvUfEs2jcLNHwJ6pIRVohXhjbHrvJ5yoA2ZBhSRtw5NR8ZMCD\nTitujVKdx8dunoDWbAtJ6sIF409mO6jC4Ff+U0NuaUmt4eIalzXbBJBu/FX+\nrJeuiisWBjzXVY1E5k5LRmJoLQdNd0rL4s8/sbyPQpeYnJQ6TW1Z5AlwW9NI\nx8WzDfkS9V550tbJmTJBI1yUJgWk1kZ6cpKCOmtiFGVpp96j5tdZ7e9QeYq3\nFvlvSvIbrFLo2NjMoS3vrCKd9naYIK9o4gg1dOto1CPIz08VwkD+6BMenKgr\nAfcGLYaMov7C10dg2agqL0M2Z4gUeTiU0jydxBLObFFDSy/h/fXLu2ObXvQ/\nA6jRvKBOQFj5kwz2qvIxFesTTTm5tuL2tZs6NvxPy46LlTcc+ORYR5o4fDJi\nxqCV8aUc4QqwbInCr1lU8m86WuC/Knu05MVN4GlSIMecgsFzhox7bc5DmzZF\n6feXxXjBBj8+ZqtbvXjw9njYZ4cj4cnk7FDsIj+qYpF5wyzTaryynGyU9kij\nHAK3IcLNHmkjeK2ojUs33Pr1rzZJVg/UPL3Y8nCgf8pR1OK86KWPT4K2We8C\nVzZd\r\n=xvDa\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCK2aHeRSSIxXsOpuyaBbsKqos/NFz2N2Eyf5XHAXez7gIgYV6FdtC5ZZ6UvBTXFV1coug4mnLC51p84a2YCQAkIrM="}]}, "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/get-stream_4.1.0_1538471503599_0.7289635831880876"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "get-stream", "version": "5.0.0", "description": "Get a stream as a string, buffer, or array", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stream.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd-check"}, "keywords": ["get", "stream", "promise", "concat", "string", "text", "buffer", "read", "data", "consume", "readable", "readablestream", "array", "object"], "dependencies": {"pump": "^3.0.0"}, "devDependencies": {"@types/node": "^11.10.5", "ava": "^1.3.1", "into-stream": "^4.0.0", "tsd-check": "^0.3.0", "xo": "^0.24.0"}, "gitHead": "e53a3f46bdb8917910b46f6f826f7935fdebf932", "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "homepage": "https://github.com/sindresorhus/get-stream#readme", "_id": "get-stream@5.0.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-frc9F97ehylll1YI31eJRw5M21M86GCItj5U3S3hOEUa6JG6LtcvRKYDAtV/9E5lVasY8QJSPVsrMg/mbCSP5w==", "shasum": "7d1f137576da207fa83d843f44824a70ef999b5f", "tarball": "https://registry.npmjs.org/get-stream/-/get-stream-5.0.0.tgz", "fileCount": 6, "unpackedSize": 10319, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcheuJCRA9TVsSAnZWagAAcIEQAIKXFozPUiCx1Gdk2H4G\nPqQZpAorFn3rjKiGvpgXPrOcxdh7cdABpUsWDTtGobxMBkq3KnJvBm33FCjc\nGJ3kUf+AzDlUlo+6OS0iWq2qD6F9V6oLFv+Xv7rOqGjbjhOUMhJDIlw95Gp8\nlPixHlE3XlodwYFV12374BscQGJyxOrZCWsyIiAbCXkc8VRB9M7dW+MrvLag\na2N3b7MX6vzbzA42ahbet0gj3I+8714j3yckYc7UbBAlYTURgZ06lEnxok4N\nOG29/ZPCbHJ9lA2TKtVyei2mH8Yq9RCHogr1q6xn4IdL4TTSxT+nYpCGHLte\n92nWjNhWhApaXOJw0/KkSqzxHNJ8WYICNpTY2J4w8Q8a25o+N6m30dQDsRj3\nc3PwSUYxibMRhsxlbL6cwlKOJFMspzT5LzPDhPWg5Gpsw2oDsPACJwmlOWfD\nC3SromAngHOVnriv6EHQcPizGVeDocuQoZqmtNYUQsiMVG0dVEAvlz4u9nar\nS73/hnnxkH0u7qaKjDDwx8b+2N8neBpNoyAVpnW5dDE4zM4jLtGAi21SQNlE\nK7VOVqvdaBcK+oHr117NDITXv0l267XYK/TvhuqE+tjD7ynOMBNcNaVb0ea0\nvu3G/VEmfQY+G6TUo6W+ZXssiP5bS6OqF0JPjyq88mDceVmW8a+qsGjS3W/R\n0jf+\r\n=LWRs\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDUQCcm7gPm1NIfNboqdA37uuItZOBW48gzJLgbLPkIKQIgTgR8UqOl5baSL4l+rAMHNWJ7ZPBWO0Us+ouxBUveW+w="}]}, "maintainers": [{"name": "jamestalmage", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/get-stream_5.0.0_1552280457094_0.683927478244923"}, "_hasShrinkwrap": false}, "5.1.0": {"name": "get-stream", "version": "5.1.0", "description": "Get a stream as a string, buffer, or array", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stream.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["get", "stream", "promise", "concat", "string", "text", "buffer", "read", "data", "consume", "readable", "readablestream", "array", "object"], "dependencies": {"pump": "^3.0.0"}, "devDependencies": {"@types/node": "^11.13.0", "ava": "^1.4.1", "into-stream": "^5.0.0", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "7ce032d306373500e1f4557b3c7a31390a005da6", "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "homepage": "https://github.com/sindresorhus/get-stream#readme", "_id": "get-stream@5.1.0", "_nodeVersion": "8.15.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-EXr1FOzrzTfGeL0gQdeFEvOMm2mzMOglyiOXSTpPC+iAjAKftbr3jpCMWynogwYnM+eSj9sHGc6wjIcDvYiygw==", "shasum": "01203cdc92597f9b909067c3e656cc1f4d3c4dc9", "tarball": "https://registry.npmjs.org/get-stream/-/get-stream-5.1.0.tgz", "fileCount": 6, "unpackedSize": 11825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpvwwCRA9TVsSAnZWagAAtEQP/2VH3Kis67GQUXZ63DqV\nnJj0xObsaInebSdGskLhML4IrPdhLEPew3udOugkRzDdT18Mq7uK2ynBQDCD\ncIWTDKbyk0x1cipGRosgkYAyNepC7toB9Jwfd7Nynkp+g4ca1NUtMr+FqzYz\nIfzpmJ1sW0lydV0hQWf9wxpHPDaZeQvmGzYX6VIXD748uQC3W5ix2VWF/kAv\nmctm2BtEsxRJBPOtcnGcFzeYrJy9C2zhxl21s0dJvxTbOoVRr+UlWVmTPeYG\ncQf4DC2vmlSBoSXvEIfo7Pw9MWWA2F2DGTuLrciGoswFCDYrJsFbfTLYKMxW\naGNNZjgWM1lrU0nxW/2RCE8kVDysc6GY9yxEXHSs2ZTzHNnqhnW9ZI+LMYpB\nLHBQGzsgzNc3pPomIi/Iu+A/2Ui3dgXhOtuFwOJPQz9+tdcWcxgh1++bKltV\nROnXIGMTJ89MH/G6QVrEAvJ9xulMFYwuz39vgI/0HbHeu0PNDKWq/fMxaaWV\nCZS6VuHUP4pzyht5cORDLchDXbSlcnbNSHvQfWYohjjaRMwbICGVlQXzaa8c\nmEVoWwKYZsiidHJmzTfov6EDTaBf0b1hkgF509FaL5pVjKwgKjVZqjuyLcAY\nJHkADXXuPQtFVtRXVjpD8Cm2txKr7WdsSHJyQncK9kwrjSqVu/Qb5KTDknV4\nmGb1\r\n=OslP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG6FOEBujpqJv/aPvhcokRcjHyp0S5o7/5oqyotdKX6BAiAyiwnccMMf0quQ3nZPKwsQxyK0/T4sY1MHSsP9GHuSjg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/get-stream_5.1.0_1554447407803_0.8709433168609912"}, "_hasShrinkwrap": false}, "5.2.0": {"name": "get-stream", "version": "5.2.0", "description": "Get a stream as a string, buffer, or array", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stream.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["get", "stream", "promise", "concat", "string", "text", "buffer", "read", "data", "consume", "readable", "readablestream", "array", "object"], "dependencies": {"pump": "^3.0.0"}, "devDependencies": {"@types/node": "^12.0.7", "ava": "^2.0.0", "into-stream": "^5.0.0", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "010d8deaa2c966f2d230cac5cacee20e29d028c6", "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "homepage": "https://github.com/sindresorhus/get-stream#readme", "_id": "get-stream@5.2.0", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.7", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA==", "shasum": "4966a1795ee5ace65e706c4b7beb71257d6e22d3", "tarball": "https://registry.npmjs.org/get-stream/-/get-stream-5.2.0.tgz", "fileCount": 6, "unpackedSize": 12397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMGVdCRA9TVsSAnZWagAAjswQAJbRMkEomo1FYWd0s9QD\nBJ9yCq2Xw0q8L/9L23Gr19E3pU6cSx3QQvJUYQyiX8v0M6fUt57rF8B+C5Jq\nawcn7AposeDLVv8G1HD1gvcTX514W3qSTZAPDCK6cmVxMM1LvpwDXL35pJy9\nPJjvZefqf/WdCgWtWaugF0x9o+jz2OnXEUz4nbEwDbkcEan3AdqMNcYnUxEy\natGpbvxhEVDKkp6J0YAWK1jo0/iumu/SueCT+U+/HfA+m5zKK/x4J71OrApF\nHLt8S6I9czx7Z3Z8SQlRnJvGgdULTnX5tdCqfxiVtvRXXjwGVil51ZBqHUax\npbbCAqCmhL6QIXV5JlG2utfZMvOiKkAwc3VF4Ko1sYOANrTOIEDrEj18IuhF\niabWrw58fsxqJdwfkeLqcPpx2gDciHNBJ+/PdMSVEfUUmEySummVyvA/Cdjh\nGVvPuH0qyFRtQeft1fylRR7GibAbNEm6Ji2ASdmH4FZqJdNqX/8sKwQVB0/A\njYI3+RaSODL2EGvsRpMRfcTu/LSGETnnNrDJx/rteCOjoqJvcdsP3Y0zs82O\nemWmboF4U1FPjtFNqghN1U8AxE7MjRw+xOLzhdyztW6T4iJKCKOEQmeayrqS\nWqbsTuZ0O1H7g7hz2mt8WWFKL54IIGIzSRC1uQa18s2EB3NMAPQKJMyKOvUq\nRz7k\r\n=hjjZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2Ah8Hyc22v/tuQ454XU+KpJIOeO0qj5iUy1zyoWjfoQIgU8dETKrwXGOkPCXv+9R95XDCd36MbF+Op9z+Ga60ewQ="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/get-stream_5.2.0_1597007197450_0.5569560050452633"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "get-stream", "version": "6.0.0", "description": "Get a stream as a string, buffer, or array", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stream.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["get", "stream", "promise", "concat", "string", "text", "buffer", "read", "data", "consume", "readable", "readablestream", "array", "object"], "devDependencies": {"@types/node": "^14.0.27", "ava": "^2.4.0", "into-stream": "^5.0.0", "tsd": "^0.13.1", "xo": "^0.24.0"}, "gitHead": "4ff974f5025e928c183e9bf2f111b107e7b5bd53", "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "homepage": "https://github.com/sindresorhus/get-stream#readme", "_id": "get-stream@6.0.0", "_nodeVersion": "14.5.0", "_npmVersion": "6.14.7", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-A1B3Bh1UmL0bidM/YX2NsCOTnGJePL9rO/M+Mw3m9f2gUpfokS0hi5Eah0WSUEWZdZhIZtMjkIYS7mDfOqNHbg==", "shasum": "3e0012cb6827319da2706e601a1583e8629a6718", "tarball": "https://registry.npmjs.org/get-stream/-/get-stream-6.0.0.tgz", "fileCount": 6, "unpackedSize": 12325, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMSyACRA9TVsSAnZWagAAT2kP/2J7YG740tovoA4SaQjn\nkL5oXBh1Nbb7V6UOCOhiooYhcvXC0ueJw+8N+iByjcxAJ510UnEHapM50Su9\nH3rZ8Afgk32f7bynWM0FTg4NU3KbQERw6Qk1iQrbNi6tYrvoGy/L9PJMQUtn\nE2aoo/7KzqP0r4Z5IlYc3PaK3AJOKjuFJKV0ZxGtcAEdoZEGNx3nLmO0eAnh\n4JplFPc24BVyLh+n0X5mOa1ZDWkZEv9mVcuYjCB6jSiClmd8SKk+ho0gQIBu\nldtjzbtd8Hbxu8ZEq4L7b/s3Sb71KCXTUZOxEpqX8nUhZIemJVVTWL3RUYXI\nMnpWgSQi9R1y9/UPKaz4bdKMwBl7ZG26+XRaXkh5zavB+MkPePtJI3n4jnSU\n3+0MszIypy4G/iRCCx9vlpsSJVpA1xXYM6YF98OCzu4kdnNz9BRPXvVaJ+uJ\ncQ1zaA8MFO/W/U8bkSCZH3qX/NuatTdK16NV+wzRLQxR+qMoKCg+lETiC6YN\nfStERz7M/p8dNv3suIQ98c2SnY5Rsbp5n/KszQhaBw8EbqUa9ugOCebUfDd4\ntNzBNw8Rk9CkWWbWt5aDrVbaJ/9TCAoHrgUCS38G+tujDcXsPGLw9gmpK3R/\nqoi535+Hf/tr+lbWXLbqmEn7MirZNYSGt1Dqj9zb6NrF3oJ7L7dAtQF8+pCq\nPXS9\r\n=0bP1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCoPw7MeuFg+lEyHdACzQ/4f3SNuRA0I4VWrtCGJUB9HgIgXrooqTxRxYCQ89RU+fdn60mlebFZ5IrFKi2ppC0L3no="}]}, "maintainers": [{"email": "<EMAIL>", "name": "sindresor<PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/get-stream_6.0.0_1597058176245_0.15129601311649044"}, "_hasShrinkwrap": false}, "6.0.1": {"name": "get-stream", "version": "6.0.1", "description": "Get a stream as a string, buffer, or array", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stream.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["get", "stream", "promise", "concat", "string", "text", "buffer", "read", "data", "consume", "readable", "readablestream", "array", "object"], "devDependencies": {"@types/node": "^14.0.27", "ava": "^2.4.0", "into-stream": "^5.0.0", "tsd": "^0.13.1", "xo": "^0.24.0"}, "gitHead": "c17901233590aa49675a7fd0f42d70b9bed1c580", "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "homepage": "https://github.com/sindresorhus/get-stream#readme", "_id": "get-stream@6.0.1", "_nodeVersion": "15.12.0", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==", "shasum": "a262d8eef67aced57c2852ad6167526a43cbf7b7", "tarball": "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz", "fileCount": 6, "unpackedSize": 12176, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgd8eLCRA9TVsSAnZWagAAVYMP/iaRyarQAqXFfFJUeAP+\nIep5ptnfvaRzQ+ULWvFUaNj6SZmEvueCPKdHK7nKky8crXepZ6JEewi9ZBPC\nIGhHes0Vi8Jo2YWkeayCW0rXvxiiP5LqfgKawEzhHRnycII9N3zHaxKyQP+C\ntVFvuS8US6qGMy9AXv3XhVdMB4cymSrL/crtT0qx01JQyayruUh9k38wWRWI\nzUwQstAq7NZhQHlHcu488FmJtssYBpZM7BbKI//4kG0zzgifdAecxajqcndi\nIbLzgsutoZIy5SehtBfRcFtUPMhdeRlcePhs1+dE88W8wMVG0aLtGDlb49Yl\nxALl7ExsywmuE04gdXYO8XfhwAj+a5ontxO9VtufVs2oslxbdrzMMpUuJaKl\nDr6lS4593VncAuH5wRJmEQoTXvcEtQYlbbdC7zyFDh0bOqHKF0LUyYDJOymM\ndBmrMdL2QZHC0LaqQHtuyODW5pSDwJ6+gMhbAY6ZjrvGC7xQvFz7AwDMZU05\nzePqXJymMv6hjMSXa7qPfhveyA1CFW12Xrdkepugjol7UHPccXTlLNAkqIcR\npA3ABmRu9fN85UQyYupBq1duajBxmdfKnvKIrqNCUkt2YgO/lZKnsvx1cHEy\ns5VvXwt58TcIL4jwyuTs6GeTPL5hbS7zyRLqHq32+XbppnehGsJJvAlGgEDZ\nevwB\r\n=Z+Gd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBju41WXsZEG80BNmYd4cmS9l3xyg4A0NDX/WU5rg8/PAiEAyYjoBLMOJT4zyP+XE4ARATW/ypF1mQOLQJztdUvGPEU="}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/get-stream_6.0.1_1618462602795_0.21548152605594484"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "get-stream", "version": "7.0.0", "description": "Get a stream as a string or buffer", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stream.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=16"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["get", "stream", "promise", "concat", "string", "text", "buffer", "read", "data", "consume", "readable", "readablestream", "object", "concat"], "devDependencies": {"@types/node": "^20.2.4", "ava": "^5.3.0", "into-stream": "^8.0.0", "tsd": "^0.28.1", "xo": "^0.54.2"}, "types": "./index.d.ts", "gitHead": "0a818546d2874568ec9df3869aab2bc430eb1f1d", "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "homepage": "https://github.com/sindresorhus/get-stream#readme", "_id": "get-stream@7.0.0", "_nodeVersion": "16.20.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-ql6FW5b8tgMYvI4UaoxG3EQN3VyZ6VeQpxNBGg5BZ4xD4u+HJeprzhMMA4OCBEGQgSR+m87pstWMpiVW64W8Fw==", "shasum": "f5695721f481f5581beb9d11cb90c41ddfceea18", "tarball": "https://registry.npmjs.org/get-stream/-/get-stream-7.0.0.tgz", "fileCount": 5, "unpackedSize": 9328, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB8He8Cf33s2JSQ0CJNy+ah8mo35W7xiLvV6GjVq40SBAiArQLSbPPOAqaUg3lRIPh8RoSn8Kr6dIyYIs7jHYFbO6g=="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/get-stream_7.0.0_1685128969952_0.5234487583591594"}, "_hasShrinkwrap": false}, "7.0.1": {"name": "get-stream", "version": "7.0.1", "description": "Get a stream as a string or buffer", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stream.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=16"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["get", "stream", "promise", "concat", "string", "text", "buffer", "read", "data", "consume", "readable", "readablestream", "object", "concat"], "devDependencies": {"@types/node": "^20.2.4", "ava": "^5.3.0", "into-stream": "^8.0.0", "tsd": "^0.28.1", "xo": "^0.54.2"}, "types": "./index.d.ts", "gitHead": "7b2d2b4c53630fa2bc94459038efaf1ad6c3fc67", "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "homepage": "https://github.com/sindresorhus/get-stream#readme", "_id": "get-stream@7.0.1", "_nodeVersion": "16.20.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-3M8C1EOFN6r8AMUhwUAACIoXZJEOufDU5+0gFFN5uNs6XYOralD2Pqkl7m046va6x77FwposWXbAhPPIOus7mQ==", "shasum": "1664dfe7d1678540ea6a4da3ae7cd59bf4e4a91e", "tarball": "https://registry.npmjs.org/get-stream/-/get-stream-7.0.1.tgz", "fileCount": 5, "unpackedSize": 9402, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICp4i1nXx/1Tc2iy1da9AnPkNUNunKuGB7LUCi6IhJn3AiEAtrlF3UJCWVzKmctqEzoZ6o7lAW2DAYGTyp574Cfnwcc="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/get-stream_7.0.1_1688237987231_0.7605889482378709"}, "_hasShrinkwrap": false}, "8.0.0": {"name": "get-stream", "version": "8.0.0", "description": "Get a stream as a string, Buffer, ArrayBuffer or array", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stream.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./source/index.d.ts", "default": "./source/index.js"}, "engines": {"node": ">=16"}, "scripts": {"benchmark": "node benchmarks/index.js", "test": "xo && ava && tsd --typings=source/index.d.ts --files=source/index.test-d.ts"}, "keywords": ["get", "stream", "promise", "concat", "string", "text", "buffer", "read", "data", "consume", "readable", "readablestream", "object", "concat"], "devDependencies": {"@types/node": "^20.5.0", "ava": "^5.3.1", "precise-now": "^2.0.0", "stream-json": "^1.8.0", "tsd": "^0.28.1", "xo": "^0.56.0"}, "gitHead": "215b672b5b12b95471e380a9543c75336df029b5", "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "homepage": "https://github.com/sindresorhus/get-stream#readme", "_id": "get-stream@8.0.0", "_nodeVersion": "16.20.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-tdTnEIxF4oDc9Gd+PAApCAQWTf5t8N0/QDf4nH7ahIgc2YUvRL7259xiFlkLEpehTXc7QugSflnLLJap5Gd+HA==", "shasum": "6e07aa5b9b2a6e85e757c18a763e01e8a0722075", "tarball": "https://registry.npmjs.org/get-stream/-/get-stream-8.0.0.tgz", "fileCount": 11, "unpackedSize": 23680, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCvwCRVedcvlP1SMpRvWVNRWJRz0833U6s2so8EKg7XtAIgDHiQCJGXHsBoUlVCRkQtkOLGOwJOKc51NhAAX1ZOxvY="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/get-stream_8.0.0_1692183093911_0.5015245611476336"}, "_hasShrinkwrap": false}, "8.0.1": {"name": "get-stream", "version": "8.0.1", "description": "Get a stream as a string, Buffer, ArrayBuffer or array", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stream.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./source/index.d.ts", "default": "./source/index.js"}, "engines": {"node": ">=16"}, "scripts": {"benchmark": "node benchmarks/index.js", "test": "xo && ava && tsd --typings=source/index.d.ts --files=source/index.test-d.ts"}, "keywords": ["get", "stream", "promise", "concat", "string", "text", "buffer", "read", "data", "consume", "readable", "readablestream", "object", "concat"], "devDependencies": {"@types/node": "^20.5.0", "ava": "^5.3.1", "precise-now": "^2.0.0", "stream-json": "^1.8.0", "tsd": "^0.28.1", "xo": "^0.56.0"}, "_id": "get-stream@8.0.1", "gitHead": "d4c9f72fe03e2d3baca352c0e505bbea4ce873a8", "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "homepage": "https://github.com/sindresorhus/get-stream#readme", "_nodeVersion": "20.5.0", "_npmVersion": "9.8.1", "dist": {"integrity": "sha512-VaUJspBffn/LMCJVoMvSAdmscJyS1auj5Zulnn5UoYcY531UWmdwhRWkcGKnGU93m5HSXP9LP2usOryrBtQowA==", "shasum": "def9dfd71742cd7754a7761ed43749a27d02eca2", "tarball": "https://registry.npmjs.org/get-stream/-/get-stream-8.0.1.tgz", "fileCount": 11, "unpackedSize": 25151, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBxx75byCiRJzNUebnnt4eOPAR6O7wU5Mx8RC+ybCvz+AiB9H0j5VN/ZA2YsZddHU7tGX2Su2Q+0//RxRuGKO1OpnQ=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/get-stream_8.0.1_1692286426619_0.5773592338470748"}, "_hasShrinkwrap": false}, "9.0.0": {"name": "get-stream", "version": "9.0.0", "description": "Get a stream as a string, Buffer, ArrayBuffer or array", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stream.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./source/index.d.ts", "browser": "./source/exports.js", "default": "./source/index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"benchmark": "node benchmarks/index.js", "test": "xo && ava && tsd --typings=source/index.d.ts --files=source/index.test-d.ts"}, "keywords": ["get", "stream", "promise", "concat", "string", "text", "buffer", "read", "data", "consume", "readable", "readablestream", "object", "concat"], "dependencies": {"@sec-ant/readable-stream": "^0.3.2", "is-stream": "^4.0.1"}, "devDependencies": {"@types/node": "^20.8.9", "ava": "^5.3.1", "onetime": "^7.0.0", "precise-now": "^3.0.0", "stream-json": "^1.8.0", "tsd": "^0.29.0", "xo": "^0.56.0"}, "gitHead": "d2fafe7755c603872af8621ca1d2a74066d08377", "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "homepage": "https://github.com/sindresorhus/get-stream#readme", "_id": "get-stream@9.0.0", "_nodeVersion": "20.11.1", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-B<PERSON>12iotKJo0U7IgsPuwmr7/62/Twl3Im6vRg6Np2TiAUCTEhQdJAIOMikjVqbJBowQYoC+WTcwgke1hNWnLrrQ==", "shasum": "4f15399aebe8a3409c5b15a6355f65de2f4f0414", "tarball": "https://registry.npmjs.org/get-stream/-/get-stream-9.0.0.tgz", "fileCount": 14, "unpackedSize": 28696, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC7cz3tUKnpI7ADuq2tf2Hlst3T4Q6LpixNRZFdFgWdhgIhAMsTMzOKmoI0DBOM8/boyQLDxqI1rofLnIxJT4smnDOA"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/get-stream_9.0.0_1710488165175_0.7385196822704756"}, "_hasShrinkwrap": false}, "9.0.1": {"name": "get-stream", "version": "9.0.1", "description": "Get a stream as a string, Buffer, ArrayBuffer or array", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stream.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./source/index.d.ts", "browser": "./source/exports.js", "default": "./source/index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"benchmark": "node benchmarks/index.js", "test": "xo && ava && tsd --typings=source/index.d.ts --files=source/index.test-d.ts"}, "keywords": ["get", "stream", "promise", "concat", "string", "text", "buffer", "read", "data", "consume", "readable", "readablestream", "object", "concat"], "dependencies": {"@sec-ant/readable-stream": "^0.4.1", "is-stream": "^4.0.1"}, "devDependencies": {"@types/node": "^20.8.9", "ava": "^6.1.2", "onetime": "^7.0.0", "precise-now": "^3.0.0", "stream-json": "^1.8.0", "tsd": "^0.29.0", "xo": "^0.58.0"}, "gitHead": "83a78c0e4ff54e9b92ab73fc7008e99237a3e9d5", "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "homepage": "https://github.com/sindresorhus/get-stream#readme", "_id": "get-stream@9.0.1", "_nodeVersion": "20.11.1", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-kVCxPF3vQM/N0B1PmoqVUqgHP+EeVjmZSQn+1oCRPxd2P21P2F19lIgbR3HBosbB1PUhOAoctJnfEn2GbN2eZA==", "shasum": "95157d21df8eb90d1647102b63039b1df60ebd27", "tarball": "https://registry.npmjs.org/get-stream/-/get-stream-9.0.1.tgz", "fileCount": 13, "unpackedSize": 28353, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICtmALRCV8VTps6MN1oc/E64V8w1O7+HDihZAh8/DESzAiEAuUlHGoFJKO6eQ4NtZ9ISVYIMsA/wIoo7BnmMauNp1oE="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/get-stream_9.0.1_1710612350113_0.6267644962668235"}, "_hasShrinkwrap": false}}, "readme": "# get-stream\n\n> Get a stream as a string, Buffer, A<PERSON>yBuffer or array\n\n## Features\n\n- Works in any JavaScript environment ([Node.js](#nodejs-streams), [browsers](#browser-support), etc.).\n- Supports [text streams](#getstreamstream-options), [binary streams](#getstreamasbufferstream-options) and [object streams](#getstreamasarraystream-options).\n- Supports [async iterables](#async-iterables).\n- Can set a [maximum stream size](#maxbuffer).\n- Returns [partially read data](#errors) when the stream errors.\n- [Fast](#benchmarks).\n\n## Install\n\n```sh\nnpm install get-stream\n```\n\n## Usage\n\n### Node.js streams\n\n```js\nimport fs from 'node:fs';\nimport getStream from 'get-stream';\n\nconst stream = fs.createReadStream('unicorn.txt');\n\nconsole.log(await getStream(stream));\n/*\n              ,,))))))));,\n           __)))))))))))))),\n\\|/       -\\(((((''''((((((((.\n-*-==//////((''  .     `)))))),\n/|\\      ))| o    ;-.    '(((((                                  ,(,\n         ( `|    /  )    ;))))'                               ,_))^;(~\n            |   |   |   ,))((((_     _____------~~~-.        %,;(;(>';'~\n            o_);   ;    )))(((` ~---~  `::           \\      %%~~)(v;(`('~\n                  ;    ''''````         `:       `:::|\\,__,%%    );`'; ~\n                 |   _                )     /      `:|`----'     `-'\n           ______/\\/~    |                 /        /\n         /~;;.____/;;'  /          ___--,-(   `;;;/\n        / //  _;______;'------~~~~~    /;;/\\    /\n       //  | |                        / ;   \\;;,\\\n      (<_  | ;                      /',/-----'  _>\n       \\_| ||_                     //~;~~~~~~~~~\n           `\\_|                   (,~~\n                                   \\~\\\n                                    ~~\n*/\n```\n\n### Web streams\n\n```js\nimport getStream from 'get-stream';\n\nconst {body: readableStream} = await fetch('https://example.com');\nconsole.log(await getStream(readableStream));\n```\n\nThis works in any browser, even [the ones](https://developer.mozilla.org/en-US/docs/Web/API/ReadableStream#browser_compatibility) not supporting `ReadableStream.values()` yet.\n\n### Async iterables\n\n```js\nimport {opendir} from 'node:fs/promises';\nimport {getStreamAsArray} from 'get-stream';\n\nconst asyncIterable = await opendir(directory);\nconsole.log(await getStreamAsArray(asyncIterable));\n```\n\n## API\n\nThe following methods read the stream's contents and return it as a promise.\n\n### getStream(stream, options?)\n\n`stream`: [`stream.Readable`](https://nodejs.org/api/stream.html#class-streamreadable), [`ReadableStream`](https://developer.mozilla.org/en-US/docs/Web/API/ReadableStream), or [`AsyncIterable<string | Buffer | ArrayBuffer | DataView | TypedArray>`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols#the_async_iterator_and_async_iterable_protocols)\\\n`options`: [`Options`](#options)\n\nGet the given `stream` as a string.\n\n### getStreamAsBuffer(stream, options?)\n\nGet the given `stream` as a Node.js [`Buffer`](https://nodejs.org/api/buffer.html#class-buffer).\n\n```js\nimport {getStreamAsBuffer} from 'get-stream';\n\nconst stream = fs.createReadStream('unicorn.png');\nconsole.log(await getStreamAsBuffer(stream));\n```\n\n### getStreamAsArrayBuffer(stream, options?)\n\nGet the given `stream` as an [`ArrayBuffer`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/ArrayBuffer).\n\n```js\nimport {getStreamAsArrayBuffer} from 'get-stream';\n\nconst {body: readableStream} = await fetch('https://example.com');\nconsole.log(await getStreamAsArrayBuffer(readableStream));\n```\n\n### getStreamAsArray(stream, options?)\n\nGet the given `stream` as an array. Unlike [other methods](#api), this supports [streams of objects](https://nodejs.org/api/stream.html#object-mode).\n\n```js\nimport {getStreamAsArray} from 'get-stream';\n\nconst {body: readableStream} = await fetch('https://example.com');\nconsole.log(await getStreamAsArray(readableStream));\n```\n\n#### options\n\nType: `object`\n\n##### maxBuffer\n\nType: `number`\\\nDefault: `Infinity`\n\nMaximum length of the stream. If exceeded, the promise will be rejected with a `MaxBufferError`.\n\nDepending on the [method](#api), the length is measured with [`string.length`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/length), [`buffer.length`](https://nodejs.org/api/buffer.html#buflength), [`arrayBuffer.byteLength`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/ArrayBuffer/byteLength) or [`array.length`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/length).\n\n## Errors\n\nIf the stream errors, the returned promise will be rejected with the `error`. Any contents already read from the stream will be set to `error.bufferedData`, which is a `string`, a `Buffer`, an `ArrayBuffer` or an array depending on the [method used](#api).\n\n```js\nimport getStream from 'get-stream';\n\ntry {\n\tawait getStream(streamThatErrorsAtTheEnd('unicorn'));\n} catch (error) {\n\tconsole.log(error.bufferedData);\n\t//=> 'unicorn'\n}\n```\n\n## Browser support\n\nFor this module to work in browsers, a bundler must be used that either:\n- Supports the [`exports.browser`](https://nodejs.org/api/packages.html#community-conditions-definitions) field in `package.json`\n- Strips or ignores `node:*` imports\n\nMost bundlers (such as [Webpack](https://webpack.js.org/guides/package-exports/#target-environment)) support either of these.\n\nAdditionally, browsers support [web streams](#web-streams) and [async iterables](#async-iterables), but not [Node.js streams](#nodejs-streams).\n\n## Tips\n\n### Alternatives\n\nIf you do not need the [`maxBuffer`](#maxbuffer) option, [`error.bufferedData`](#errors), nor browser support, you can use the following methods instead of this package.\n\n#### [`streamConsumers.text()`](https://nodejs.org/api/webstreams.html#streamconsumerstextstream)\n\n```js\nimport fs from 'node:fs';\nimport {text} from 'node:stream/consumers';\n\nconst stream = fs.createReadStream('unicorn.txt', {encoding: 'utf8'});\nconsole.log(await text(stream))\n```\n\n#### [`streamConsumers.buffer()`](https://nodejs.org/api/webstreams.html#streamconsumersbufferstream)\n\n```js\nimport {buffer} from 'node:stream/consumers';\n\nconsole.log(await buffer(stream))\n```\n\n#### [`streamConsumers.arrayBuffer()`](https://nodejs.org/api/webstreams.html#streamconsumersarraybufferstream)\n\n```js\nimport {arrayBuffer} from 'node:stream/consumers';\n\nconsole.log(await arrayBuffer(stream))\n```\n\n#### [`readable.toArray()`](https://nodejs.org/api/stream.html#readabletoarrayoptions)\n\n```js\nconsole.log(await stream.toArray())\n```\n\n#### [`Array.fromAsync()`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/fromAsync)\n\nIf your [environment supports it](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/fromAsync#browser_compatibility):\n\n```js\nconsole.log(await Array.fromAsync(stream))\n```\n\n### Non-UTF-8 encoding\n\nWhen all of the following conditions apply:\n  - [`getStream()`](#getstreamstream-options) is used (as opposed to [`getStreamAsBuffer()`](#getstreamasbufferstream-options) or [`getStreamAsArrayBuffer()`](#getstreamasarraybufferstream-options))\n  - The stream is binary (not text)\n  - The stream's encoding is not UTF-8 (for example, it is UTF-16, hexadecimal, or Base64)\n\nThen the stream must be decoded using a transform stream like [`TextDecoderStream`](https://developer.mozilla.org/en-US/docs/Web/API/TextDecoderStream) or [`b64`](https://github.com/hapijs/b64).\n\n```js\nimport getStream from 'get-stream';\n\nconst textDecoderStream = new TextDecoderStream('utf-16le');\nconst {body: readableStream} = await fetch('https://example.com');\nconsole.log(await getStream(readableStream.pipeThrough(textDecoderStream)));\n```\n\n### Blobs\n\n[`getStreamAsArrayBuffer()`](#getstreamasarraybufferstream-options) can be used to create [Blobs](https://developer.mozilla.org/en-US/docs/Web/API/Blob).\n\n```js\nimport {getStreamAsArrayBuffer} from 'get-stream';\n\nconst stream = fs.createReadStream('unicorn.txt');\nconsole.log(new Blob([await getStreamAsArrayBuffer(stream)]));\n```\n\n### JSON streaming\n\n[`getStreamAsArray()`](#getstreamasarraystream-options) can be combined with JSON streaming utilities to parse JSON incrementally.\n\n```js\nimport fs from 'node:fs';\nimport {compose as composeStreams} from 'node:stream';\nimport {getStreamAsArray} from 'get-stream';\nimport streamJson from 'stream-json';\nimport streamJsonArray from 'stream-json/streamers/StreamArray.js';\n\nconst stream = fs.createReadStream('big-array-of-objects.json');\nconsole.log(await getStreamAsArray(\n\tcomposeStreams(stream, streamJson.parser(), streamJsonArray.streamArray()),\n));\n```\n\n## Benchmarks\n\n### Node.js stream (100 MB, binary)\n\n- `getStream()`: 142ms\n- `text()`: 139ms\n- `getStreamAsBuffer()`: 106ms\n- `buffer()`: 83ms\n- `getStreamAsArrayBuffer()`: 105ms\n- `arrayBuffer()`: 81ms\n- `getStreamAsArray()`: 24ms\n- `stream.toArray()`: 21ms\n\n### Node.js stream (100 MB, text)\n\n- `getStream()`: 90ms\n- `text()`: 89ms\n- `getStreamAsBuffer()`: 127ms\n- `buffer()`: 192ms\n- `getStreamAsArrayBuffer()`: 129ms\n- `arrayBuffer()`: 195ms\n- `getStreamAsArray()`: 89ms\n- `stream.toArray()`: 90ms\n\n### Web ReadableStream (100 MB, binary)\n\n- `getStream()`: 223ms\n- `text()`: 221ms\n- `getStreamAsBuffer()`: 182ms\n- `buffer()`: 153ms\n- `getStreamAsArrayBuffer()`: 171ms\n- `arrayBuffer()`: 155ms\n- `getStreamAsArray()`: 83ms\n\n### Web ReadableStream (100 MB, text)\n\n- `getStream()`: 141ms\n- `text()`: 139ms\n- `getStreamAsBuffer()`: 91ms\n- `buffer()`: 80ms\n- `getStreamAsArrayBuffer()`: 89ms\n- `arrayBuffer()`: 81ms\n- `getStreamAsArray()`: 21ms\n\n[Benchmarks' source file](benchmarks/index.js).\n\n## FAQ\n\n### How is this different from [`concat-stream`](https://github.com/maxogden/concat-stream)?\n\nThis module accepts a stream instead of being one and returns a promise instead of using a callback. The API is simpler and it only supports returning a string, `Buffer`, an `ArrayBuffer` or an array. It doesn't have a fragile type inference. You explicitly choose what you want. And it doesn't depend on the huge `readable-stream` package.\n\n## Related\n\n- [get-stdin](https://github.com/sindresorhus/get-stdin) - Get stdin as a string or buffer\n- [into-stream](https://github.com/sindresorhus/into-stream) - The opposite of this package\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2024-03-16T18:05:50.719Z", "created": "2015-10-14T17:28:21.024Z", "1.0.0": "2015-10-14T17:28:21.024Z", "1.1.0": "2015-11-20T18:41:32.705Z", "2.0.0": "2016-03-03T05:25:15.334Z", "2.1.0": "2016-04-28T15:33:42.239Z", "2.2.0": "2016-04-30T20:50:42.753Z", "2.2.1": "2016-06-06T17:54:54.123Z", "2.3.0": "2016-06-07T01:31:47.069Z", "2.3.1": "2016-09-14T17:13:47.014Z", "3.0.0": "2016-11-23T02:49:47.220Z", "4.0.0": "2018-08-10T07:59:07.810Z", "4.1.0": "2018-10-02T09:11:43.738Z", "5.0.0": "2019-03-11T05:00:57.346Z", "5.1.0": "2019-04-05T06:56:47.933Z", "5.2.0": "2020-08-09T21:06:37.591Z", "6.0.0": "2020-08-10T11:16:16.361Z", "6.0.1": "2021-04-15T04:56:42.936Z", "7.0.0": "2023-05-26T19:22:50.154Z", "7.0.1": "2023-07-01T18:59:47.418Z", "8.0.0": "2023-08-16T10:51:34.109Z", "8.0.1": "2023-08-17T15:33:46.835Z", "9.0.0": "2024-03-15T07:36:05.344Z", "9.0.1": "2024-03-16T18:05:50.308Z"}, "homepage": "https://github.com/sindresorhus/get-stream#readme", "keywords": ["get", "stream", "promise", "concat", "string", "text", "buffer", "read", "data", "consume", "readable", "readablestream", "object", "concat"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/get-stream.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/get-stream/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"rubiadias": true, "rocket0191": true, "j3kz": true, "oleg_tsyba": true, "roccomuso": true, "seangenabe": true, "paraself": true, "zhenguo.zhao": true, "bluelovers": true, "pftom": true, "flumpus-dev": true}}