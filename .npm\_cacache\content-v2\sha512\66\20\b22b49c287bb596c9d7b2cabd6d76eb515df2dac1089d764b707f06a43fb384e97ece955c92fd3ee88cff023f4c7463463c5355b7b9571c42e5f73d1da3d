{"_id": "simple-update-notifier", "_rev": "11-1f5502ed059f2b0994ce5c4dcf06af89", "name": "simple-update-notifier", "dist-tags": {"latest": "2.0.0"}, "versions": {"1.0.0": {"name": "simple-update-notifier", "version": "1.0.0", "description": "Simple update notifier to check for npm updates for cli applications", "main": "build/index.js", "types": "build/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/alexbrazier/simple-update-notifier.git"}, "homepage": "https://github.com/alexbrazier/simple-update-notifier.git", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "scripts": {"test": "jest src", "build": "tsc", "prettier:check": "prettier --check src/**/*.ts", "prettier": "prettier --write src/**/*.ts", "eslint": "eslint src/**/*.ts", "lint": "yarn prettier:check && yarn eslint", "prepare": "yarn lint && yarn build", "release": "release-it"}, "devDependencies": {"@babel/preset-env": "^7.18.2", "@babel/preset-typescript": "^7.17.12", "@release-it/conventional-changelog": "^5.0.0", "@types/jest": "^28.1.3", "@types/node": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.29.0", "@typescript-eslint/parser": "^5.29.0", "eslint": "^8.18.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^28.1.1", "prettier": "^2.7.1", "release-it": "^15.1.0", "typescript": "^4.7.4"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular", "infile": "CHANGELOG.md"}}}, "eslintConfig": {"plugins": ["@typescript-eslint", "prettier"], "extends": ["prettier", "eslint:recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "gitHead": "ef1f045a660af303e91c85c88a1157f405595bac", "bugs": {"url": "https://github.com/alexbrazier/simple-update-notifier/issues"}, "_id": "simple-update-notifier@1.0.0", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-pRqOJ78A4xoJVM+Pfit23h9uW4JmYTDj6N1v9uPoeKhIxlBIWz4pbSiVENYjWAymoRxNsUqtRoXlLFCssJghag==", "shasum": "dc1080df11fe6fd1fdfbe9f9b0ad07076675eb06", "tarball": "https://registry.npmjs.org/simple-update-notifier/-/simple-update-notifier-1.0.0.tgz", "fileCount": 20, "unpackedSize": 23537, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGIgQ2KxciJmLCVKoykf/t5EWqplIwoaS3MQK5I2w27CAiEAx/WJFh3Ep80/o7vNyYqIBLu4JQ47Fu0zLdoHzj7nJ4M="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitPoAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrM6w/7Bv70qvQLYDcwuFRN9g26wYcC6OG+D7fjDADYkyewTd7NXiJf\r\nlcHqASwcdCM/ut7vRjNQxgB79BQZIjvLlDBXv4QbmSe52hbVImBKPgzhT0Pz\r\nwtNpD6sy+WWNdGLtBJf48j19btLuwRrlMV8+d8kh1HmaG2Q/AcRBJM4MsvzC\r\nMUSP3Pncbx1Lv7eEU9xUtD3rAFkIM5PD231lCrr8La7++ZixEUFcJYFoaGVh\r\nsK6RIZdI8PZbQsXxavEeDseaiQrx5TtBOBkjv7eo+STthv8MoKBcY+FaUDxA\r\niyWoYTXbbcSIvVcRQHdXRklkVylU10DOK7b+Imyqo6vi0LrAvssDUwM2yDUr\r\nzbHilC96/7ziz2vy50UwwtWmtdIV7Y2LuMtv2BNJPouro+2UZ6sqRZ/ntRIc\r\nMivIGbZpMr92eSon97rbS2bN+tzb3ee8kz3Nn0txdaWFAOodEw8jfjkYzTfI\r\nUyZo/NY6otUhk4yvj/wAI4tltaQ+42VkTOC94AwKxJ1+AkR2v32S4wiAiWyv\r\niBUdaQe92hQRLSivJqa0YrfDneJGjcnTTzq8In0YowHw2fS6RmlPRT4eH3lA\r\nSk4uoNtW2scyDMO94rLIo+YHJVDOyA7XcF0msJdJCwWt9LHrt4FqvZUOskzW\r\nH5UJyPobtaSONkSD7mRnKvxhJHc7JWMEKtE=\r\n=zqdB\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/simple-update-notifier_1.0.0_1656027648651_0.04956855535672067"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "simple-update-notifier", "version": "1.0.1", "description": "Simple update notifier to check for npm updates for cli applications", "main": "build/index.js", "types": "build/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/alexbrazier/simple-update-notifier.git"}, "homepage": "https://github.com/alexbrazier/simple-update-notifier.git", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "scripts": {"test": "jest src", "build": "rollup -c rollup.config.js", "prettier:check": "prettier --check src/**/*.ts", "prettier": "prettier --write src/**/*.ts", "eslint": "eslint src/**/*.ts", "lint": "yarn prettier:check && yarn eslint", "prepare": "yarn lint && yarn build", "release": "release-it"}, "devDependencies": {"@babel/preset-env": "^7.18.2", "@babel/preset-typescript": "^7.17.12", "@release-it/conventional-changelog": "^5.0.0", "@types/jest": "^28.1.3", "@types/node": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.29.0", "@typescript-eslint/parser": "^5.29.0", "eslint": "^8.18.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^28.1.1", "prettier": "^2.7.1", "release-it": "^15.1.0", "rollup": "^2.75.7", "rollup-plugin-ts": "^3.0.2", "typescript": "^4.7.4"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular", "infile": "CHANGELOG.md"}}}, "eslintConfig": {"plugins": ["@typescript-eslint", "prettier"], "extends": ["prettier", "eslint:recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "gitHead": "d84b5657277856e061be54b81907d373bb5ad630", "bugs": {"url": "https://github.com/alexbrazier/simple-update-notifier/issues"}, "_id": "simple-update-notifier@1.0.1", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-5ro3fkMmPcZmTD93DkVIhBt7CQ5G3KaiS4sP/9RYW4JG7duYwf24nxs4FSZgfKdKTUOhBzuhX+f+r/i+Zux1HQ==", "shasum": "a32b0099d8f1534ddbfaa296cf0d91c40e1f03f5", "tarball": "https://registry.npmjs.org/simple-update-notifier/-/simple-update-notifier-1.0.1.tgz", "fileCount": 12, "unpackedSize": 18545, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC6glRcCXiSnIU3QND/1YLQJVlo0jTcrW0PApRLmXvWhAIgOfSoQYa0Qvg5K058JlmxiX4B4Jv5ntpYS4Guug7ku6A="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitQgEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoV+A//XqZUzbNqD/WfGhVEqT6R6oCGX1cM1TM/1Pb3qH1rhT5Mm/8s\r\necEuR5jBjRBSQNmcHaAwHGOCi3d462E4WaT1tdKxxPQsW+VzHV7gHDQTSrIm\r\nA43mElOf9f4On09yH/yL6wi8UUQaApVCMkTdoL799/2MLcEJHwpkLiAf+e59\r\ncwnBY1UqZtk/EoXUngWjZKBxA4aS2lV6mpjkA4MuAjzio3fQBHC35BvPE2Co\r\niP2RjfROlexpovTsMhCRE8QUIKbe6uCSHsTYAVKV8GhcMtzedu+AhnprAR59\r\nNw6IjjX8eAL3CU7Dx7rFXsMUppLsj9pu87bCBasnH8+tSqzgMm4mxLJWT7R8\r\ndVMZHMZB6o9PeKWvOCxHujcmmKSp+0YcgRvt5IAkqxUvjLkkKbre/HktubNt\r\nGRSEHdEURLH20LHrhCV+R7dzTm+Q92xKadFaDiJDsYcz6WyQ+z5XDBsQMtZH\r\nutWIZbGYiZP9RgEcIqJaI7RK07haT0EuDdWET3KQsFBWhmAQDeFilG9RAHa1\r\niESfoJCWNVEbsu98s4aMRz9n1ATg4X+Sf/J/StE44HEkkKFCDx1BEZURdqYo\r\nhVRJZcNqDGnjTUcAMZIq7PNuEN94n6/FRUf8dD+cCLaAQm4z9cWtTjiJ/1Fk\r\n6hiCKLw0ZLMRrZpVFF/CN4U6lLlyEdkT6bk=\r\n=KGek\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/simple-update-notifier_1.0.1_1656031236060_0.28406775958739905"}, "_hasShrinkwrap": false}, "1.0.2": {"name": "simple-update-notifier", "version": "1.0.2", "description": "Simple update notifier to check for npm updates for cli applications", "main": "build/index.js", "types": "build/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/alexbrazier/simple-update-notifier.git"}, "homepage": "https://github.com/alexbrazier/simple-update-notifier.git", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "scripts": {"test": "jest src", "build": "rollup -c rollup.config.js", "prettier:check": "prettier --check src/**/*.ts", "prettier": "prettier --write src/**/*.ts", "eslint": "eslint src/**/*.ts", "lint": "yarn prettier:check && yarn eslint", "prepare": "yarn lint && yarn build", "release": "release-it"}, "devDependencies": {"@babel/preset-env": "^7.18.2", "@babel/preset-typescript": "^7.17.12", "@release-it/conventional-changelog": "^5.0.0", "@types/jest": "^28.1.3", "@types/node": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.29.0", "@typescript-eslint/parser": "^5.29.0", "eslint": "^8.18.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^28.1.1", "prettier": "^2.7.1", "release-it": "^15.1.0", "rollup": "^2.75.7", "rollup-plugin-ts": "^3.0.2", "typescript": "^4.7.4"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular", "infile": "CHANGELOG.md"}}}, "eslintConfig": {"plugins": ["@typescript-eslint", "prettier"], "extends": ["prettier", "eslint:recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "gitHead": "cbae8e8ac27b38469ff32ddc355d7acb86312ea6", "bugs": {"url": "https://github.com/alexbrazier/simple-update-notifier/issues"}, "_id": "simple-update-notifier@1.0.2", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-2oFo7OSx2n2IWP5WF848e77YfJwIdVDEUYzPwqCve7F5cFlU21382j9XSmH/kPFzVZOuuXxD0nDq0PxoDzb+NA==", "shasum": "3a74b918ea383d69673adb03d6facb98fc80cb4d", "tarball": "https://registry.npmjs.org/simple-update-notifier/-/simple-update-notifier-1.0.2.tgz", "fileCount": 15, "unpackedSize": 21159, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDClltE9auJCrkBm+sMn8jvhx9/55lmICs7UqCcYgUfYgIgPa1wlX/V1l1TSXEonJZuuzO+xPb3PM5PK6b2brJoFJA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitXwRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmozQw/9F8n1ouTS99yM9hNYD90j9cCztiWxoINTCPJuegRwoVV3Dz8p\r\n6ZQ33cLjgykn2RdVJFm7UE/IyWr4L0kRN7s513AxpTg5RKzvge7P6Iww4OQL\r\n8/TIPdyiXxTO9TxWwAiajw7ZDa39SAGO6KFPCMmnd6IlW81QIMAFtQussGKK\r\n0hWMuaYlaQEpyTBK+53yR+Cvnifs585cl1OVR/S7jZzvFLgFSN3JvDNxNzt8\r\nOGrT30vFcYQkHtyrg2Xd1ZkUaY3IB36v9QgOMTI5FyV3i86QJG80mXvIvDTE\r\nw8zMu/nVKDas60B4AlZWjC7DJ4LCF8evbiksz2wwKhvrjaX3QUFnMmmaE5nr\r\ngu3/LreNr6UEhNe10URtn1a1c53DU5ubmRqLwiL7catDCl4yhyCBdw+VpLGg\r\neWtaxek/4km5mIqx06VC9ZprxzO1hqHmYEr9A04NGlDOhJ6OamVDiDX+HN3+\r\ndOhWQ/UgZBcXpArLq4UY1Rqzf9RK8vmbFiFz3B1pCVmrTZMIfnJpsNeM/dfy\r\nlWGsyXgX893aKDtq66JFZ8LDFZeQ5N/cTLp6gaX673H/wNkSyNNzhx9lBpDS\r\nVu9s5tdtaMxW4HlEymOOjWJ8SNnSc4Phpf2O7lvGOfr/v37AS/byhpXTNx2p\r\n7eH/a+B8ku0LYCSTXjtd2cVeQyrreAV8C38=\r\n=R4+X\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/simple-update-notifier_1.0.2_1656060944924_0.6481839339501387"}, "_hasShrinkwrap": false}, "1.0.3": {"name": "simple-update-notifier", "version": "1.0.3", "description": "Simple update notifier to check for npm updates for cli applications", "main": "build/index.js", "types": "build/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/alexbrazier/simple-update-notifier.git"}, "homepage": "https://github.com/alexbrazier/simple-update-notifier.git", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "engines": {"node": ">=8.10.0"}, "scripts": {"test": "jest src", "build": "rollup -c rollup.config.js", "prettier:check": "prettier --check src/**/*.ts", "prettier": "prettier --write src/**/*.ts", "eslint": "eslint src/**/*.ts", "lint": "yarn prettier:check && yarn eslint", "prepare": "yarn lint && yarn build", "release": "release-it"}, "devDependencies": {"@babel/preset-env": "^7.18.2", "@babel/preset-typescript": "^7.17.12", "@release-it/conventional-changelog": "^5.0.0", "@types/jest": "^28.1.3", "@types/node": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.29.0", "@typescript-eslint/parser": "^5.29.0", "eslint": "^8.18.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^28.1.1", "prettier": "^2.7.1", "release-it": "^15.1.0", "rollup": "^2.75.7", "rollup-plugin-ts": "^3.0.2", "typescript": "^4.7.4"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular", "infile": "CHANGELOG.md"}}}, "eslintConfig": {"plugins": ["@typescript-eslint", "prettier"], "extends": ["prettier", "eslint:recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "gitHead": "2e7e1e8384198e5105c7142dadf1a47a8a3199a8", "bugs": {"url": "https://github.com/alexbrazier/simple-update-notifier/issues"}, "_id": "simple-update-notifier@1.0.3", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-trl5gWdAG39k+WTM0fbfTHOmjsrPLmuXagmnMGFIQyC0e5cF7pRXcFSna8KP7h9YWehG1l9ACGYa59++mrqQIQ==", "shasum": "90513c29dbbbbef4ea859dcc4641e21a94f76d24", "tarball": "https://registry.npmjs.org/simple-update-notifier/-/simple-update-notifier-1.0.3.tgz", "fileCount": 15, "unpackedSize": 23013, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICsmGWPrBLYXm9bkDZ3CVUKZQLeT04HjQQEM/j3xOqtbAiEA+lr/NCuYMrt+Prik9wIj2zuYkBu/w52TX1pFbOsuxBg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuCgfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmreaQ/5AI7/wr1PXKjy5n649OzhcOLePF+jhAJzvsukVRwEtOERTAPS\r\nf0MuM7xExImGhNGKF9QN2wRmkupnP7nezUlt/KrUonBiTgnlmaObMddN5bnx\r\n85KqGbh2f23l13uBtr86dtx8/NdyjHxTuCUZq4GyphXc/KEmhEgh4Xm+ZtvQ\r\nEU+DfjXkWLXEwYAFT6MEZUT/MVtpvLkrPueCNYKb218q4wiCtm+hZdTOoXrG\r\n/lUerPuWCDUieRTuKTa/rn6qQt2D4ERhL6rrXGAhuptcuAOFbqWiG8Rlu8Vz\r\nHCH+BvZuRTe3o2/0HLAk7/+lyBWOaSfWwxgMS39aKj1PAsTYg6iZ0N1nhS/S\r\necUyDzBF0oF7sgqUBrTrTOuw5haRuQht3Qw0xK2SvATeZV1mYTKDoEtJTzwz\r\n0lpBPTqXg+imUFnomUSPWjMrB08Ung7/2Vbuf2CPeFDKlVeYrjzb2uHuRy2Z\r\nmnAA43EBKDwQM07kj1BXgknV6/QNVltkYCnQ0x10EQiqGyhOwJ8eGb1aZu0c\r\nqlV+C2PTmewSSRPyCRP1DDEbK/YKVxx3Z8nyLnAcQPIOiZON3CQ12g7Fc8T9\r\nvQrDghAcUGRu45v+K8hqjwDKbWOqTGujzxhAMWhVOjYnl+YQozY1x72z3azR\r\n4gmj/pWram8sEx+Ni7fJHrFH8vtdP57Hk1Y=\r\n=noFL\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/simple-update-notifier_1.0.3_1656236063159_0.2910867770340688"}, "_hasShrinkwrap": false}, "1.0.4": {"name": "simple-update-notifier", "version": "1.0.4", "description": "Simple update notifier to check for npm updates for cli applications", "main": "build/index.js", "types": "build/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/alexbrazier/simple-update-notifier.git"}, "homepage": "https://github.com/alexbrazier/simple-update-notifier.git", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "engines": {"node": ">=8.10.0"}, "scripts": {"test": "jest src", "build": "rollup -c rollup.config.js", "prettier:check": "prettier --check src/**/*.ts", "prettier": "prettier --write src/**/*.ts", "eslint": "eslint src/**/*.ts", "lint": "yarn prettier:check && yarn eslint", "prepare": "yarn lint && yarn build", "release": "release-it"}, "devDependencies": {"@babel/preset-env": "^7.18.2", "@babel/preset-typescript": "^7.17.12", "@release-it/conventional-changelog": "^5.0.0", "@types/jest": "^28.1.3", "@types/node": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.29.0", "@typescript-eslint/parser": "^5.29.0", "eslint": "^8.18.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^28.1.1", "prettier": "^2.7.1", "release-it": "^15.1.0", "rollup": "^2.75.7", "rollup-plugin-ts": "^3.0.2", "typescript": "^4.7.4"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular", "infile": "CHANGELOG.md"}}}, "eslintConfig": {"plugins": ["@typescript-eslint", "prettier"], "extends": ["prettier", "eslint:recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "gitHead": "f05c6ddd78c028c80918516035fd3d747593556a", "bugs": {"url": "https://github.com/alexbrazier/simple-update-notifier/issues"}, "_id": "simple-update-notifier@1.0.4", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-kzEJvmhPAzPBDe0TUZ7iRWv2UCcNasbxx2i21ffcCj/bQnqfR4DyBb7llOfQv7Ul3WRluYWmPZhkZa7lSwdnYw==", "shasum": "b6fe7d1f20e1814bfc8bbba8faff320e9d5ba88b", "tarball": "https://registry.npmjs.org/simple-update-notifier/-/simple-update-notifier-1.0.4.tgz", "fileCount": 16, "unpackedSize": 25300, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEIStFf+KSi5R6y+py10+5<PERSON><PERSON><PERSON>44bK3yxX/2E460EGVAiBMHd6nnrY3HOFis5/0mJiBqILp8PnU57w/H+QFbUjT8Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuGC6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZyhAAjTAoaTRCdwBVpqH5eay4mtYuwpBSIslIpVibGnmdymDUH2kb\r\nszvo+MkveU/OT66HRasbLX6Hmz7KvF1wbx8U8iKLqegD6XvV3aaTgiEs/oGZ\r\niItTA9gCE8WH0wRRV8PgGSbBeS6tLOVrZp7q9DX5ke5Mrv254ysxiv0o+ztE\r\nFal7B0Rf8mONL6r3l61Or6cdn+ln99BQDeFBVpCTCaOFKlA1W18ukYEGcNFh\r\n10zlBX6LWSdD01gxc2jEA9Fqi5o3IZDMjqMb1p/YJHHyq5vpkpoNGRFUPV/a\r\nHIL8nRgwohVZvSbj+NHlySFjM/9flWEE2b8q7BIae60xnfE7e9r+haZx2KxS\r\nwpXhpIQAS32VBk18UnmtRDYNaB9x1U4aHRs5d6a7qdJp6CXHPCagAWZ4PN+4\r\n8cTjgBOGh4GZhpiA0QOtTHE6R6yCuX3fK4MxnFelB6qLyjkRzcft+LZwtdmt\r\nWwDeHRdvidF4rc6AZV311Yk85VBNhymxshpM+DfROtCgwV81Cx++FZsnritQ\r\nRcqBjn/KlFhu7njCdL00NE6/3cV9qRYyUXgoBaioj1QmWt+3tQCKFkOMLDzf\r\nWpzrC/Iten0hjhiDv9lPI3AWN3XPSUw1ByhOL3acIZW/XtC3DIpIxyucWIba\r\nDewnrsMbySCL2zXGjtKA5hlaJbG45PLLvDI=\r\n=/O6A\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/simple-update-notifier_1.0.4_1656250553987_0.28841790794607625"}, "_hasShrinkwrap": false}, "1.0.5": {"name": "simple-update-notifier", "version": "1.0.5", "description": "Simple update notifier to check for npm updates for cli applications", "main": "build/index.js", "types": "build/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/alexbrazier/simple-update-notifier.git"}, "homepage": "https://github.com/alexbrazier/simple-update-notifier.git", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "engines": {"node": ">=8.10.0"}, "scripts": {"test": "jest src", "build": "rollup -c rollup.config.js", "prettier:check": "prettier --check src/**/*.ts", "prettier": "prettier --write src/**/*.ts", "eslint": "eslint src/**/*.ts", "lint": "yarn prettier:check && yarn eslint", "prepare": "yarn lint && yarn build", "release": "release-it"}, "devDependencies": {"@babel/preset-env": "^7.18.2", "@babel/preset-typescript": "^7.17.12", "@release-it/conventional-changelog": "^5.0.0", "@types/jest": "^28.1.3", "@types/node": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.29.0", "@typescript-eslint/parser": "^5.29.0", "eslint": "^8.18.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^28.1.1", "prettier": "^2.7.1", "release-it": "^15.1.0", "rollup": "^2.75.7", "rollup-plugin-ts": "^3.0.2", "typescript": "^4.7.4"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular", "infile": "CHANGELOG.md"}}}, "eslintConfig": {"plugins": ["@typescript-eslint", "prettier"], "extends": ["prettier", "eslint:recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "gitHead": "86b6a10f387b8b4aa40ee6c27b64b58ff4974046", "bugs": {"url": "https://github.com/alexbrazier/simple-update-notifier/issues"}, "_id": "simple-update-notifier@1.0.5", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-9GoPRAQGEIls/owIH7uZFA34+dqJeB3QEdMKvWK7MLCXhIVqwj4xE8dHMUfBAgUHIqid3WKZ0hT3tzUWUNXStA==", "shasum": "3dd747f4bf0440aaaf606ea286a82a3f3a7fed87", "tarball": "https://registry.npmjs.org/simple-update-notifier/-/simple-update-notifier-1.0.5.tgz", "fileCount": 16, "unpackedSize": 26388, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHdCR9U/89FES32NNaRXbYgo+b+bDfFjJ8do8bq596OzAiEAvd3wI9nis732i3kk5Bo3dA+5vDSSSjIz9mpivvcoYQY="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuXLUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmobnw//cDAXby1AakqFcICWs35wbtXmPI0XTDrgqiFlwlM+V1YZbfgV\r\n8Ro8T5CdTLa+Mb101aJNJZijyjNmF82Fv6jmixCxM370CvVhDcUAXyF/OOCY\r\nfRX22Sbh2Y90t/2vlc4P8CWnBEZffeFHCzxHWbcR0oH4vc55RskRTgih1VZA\r\nd84SpYZ7nRn2VfkqVYP+F4OTRVhFeBS34FlebuM1DRBn+5IsVGSLHXsnmwnU\r\nysbITVuThE+cJVqJRNM5ofHp+PvxzGkPRh9kytDhyPRwgFf2EY+jU6E6dKvU\r\n/q/AIrFTMYQH3jntXrNKhUXsRP/qa9hzjq5tJaibYHJ6HwqVXAzTbaxDiIi+\r\no4qIw+9oWP8RtuOqAPgUf6IIk5KRH9cpFnjRHk4DhNxvlbHZwRNu2PUyFsR3\r\ny4fZUruvlZKCOgECt7cMI+L3bCIKwlSWHBFnoQG9T6J5a9Fp0u+dtjs2K+vh\r\ngKjV29E+yWim+M8k4oCsex+G3muHHtp0fhf4HYhsfV7LbYLzPxiCLZCMpFTQ\r\nCzrnZUUDio784kb10D/8qPPDCV4RYXFj37MlM5ZBf9B8cpjkgkEzIVNY1wtW\r\ntYe2S1dwmDn6ONdKmqS6IwzIX75Fp+PGN4coj8WoxTCsx7NINHZGBPabDxRa\r\nFqif4Upv0LfuFF8Bj3x5Mz0wr9TN7Rhh4bc=\r\n=b4CT\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/simple-update-notifier_1.0.5_1656320724196_0.7622434281000081"}, "_hasShrinkwrap": false}, "1.0.6": {"name": "simple-update-notifier", "version": "1.0.6", "description": "Simple update notifier to check for npm updates for cli applications", "main": "build/index.js", "types": "build/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/alexbrazier/simple-update-notifier.git"}, "homepage": "https://github.com/alexbrazier/simple-update-notifier.git", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "engines": {"node": ">=8.10.0"}, "scripts": {"test": "jest src", "build": "rollup -c rollup.config.js", "prettier:check": "prettier --check src/**/*.ts", "prettier": "prettier --write src/**/*.ts", "eslint": "eslint src/**/*.ts", "lint": "yarn prettier:check && yarn eslint", "prepare": "yarn lint && yarn build", "release": "release-it"}, "dependencies": {"semver": "^7.3.7"}, "devDependencies": {"@babel/preset-env": "^7.18.2", "@babel/preset-typescript": "^7.17.12", "@release-it/conventional-changelog": "^5.0.0", "@types/jest": "^28.1.3", "@types/node": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.29.0", "@typescript-eslint/parser": "^5.29.0", "eslint": "^8.18.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^28.1.1", "prettier": "^2.7.1", "release-it": "^15.1.0", "rollup": "^2.75.7", "rollup-plugin-ts": "^3.0.2", "typescript": "^4.7.4"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular", "infile": "CHANGELOG.md"}}}, "eslintConfig": {"plugins": ["@typescript-eslint", "prettier"], "extends": ["prettier", "eslint:recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "gitHead": "9698450ec9da85e32f074e7cec069199577be836", "bugs": {"url": "https://github.com/alexbrazier/simple-update-notifier/issues"}, "_id": "simple-update-notifier@1.0.6", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-eQ/VTY0k9PvJhMw8PRskO8AWdBdoo9HZrWSTjsy5BCaao9BwLRljDKNbUkJQMJCj1r/cmy/5SVEpr+X6QY8lKQ==", "shasum": "4fc217a1bdee6e6c7a0e43a17f9ba625af9e6cf2", "tarball": "https://registry.npmjs.org/simple-update-notifier/-/simple-update-notifier-1.0.6.tgz", "fileCount": 16, "unpackedSize": 24129, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDw7dTYnm5tFSwiYj3Iwa7Kqi+jJadUPzUikP7xqpyqAQIgFABj1ZcQsS1Fi0Xbs1Fc5y3EiyxpZJSZtBCdDHW6lSs="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugdhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoL/RAAgwrp9I2Sul9F034CDj2xfcyzpe0868E0Lv6FT0mROY9g1PuX\r\nj+X2zqWgOy/PNPRS5ArbJ/Cn/40h/YwHP/dopDQ93DWzmLn4FztVzF71A6eK\r\nb3rQHfGOihdbX0GxJcm0O1OowbfY0jMcqj1VwJKDS3oeG8tPRDGmnUGtB7wF\r\nlNvJfcy/Y7n5JQg68UQ+k5Z847qPRhO34o9lW76ruBnLtfG9Yq+q4uunmE1/\r\nJtYrmz8dy/BCmPJjCl+Elp+uG+k6f8PIc5NkXEW3aHLDdP7P8sTwnc4VIkAj\r\nlV2eSQZUc0Stt8uUZJ60nBZBu2AXuhiwbzX4vcCQFbLnCXH787dVjb2UVfF+\r\n6FMZFT4vd+epH650SxTdzzFVf3adWQ6hRaRoOSPN3h7Pq6mHniwfQnWr7rJf\r\nuotAp4jp612y8ea1rDLYVClvEGbSvQ7SghUVlshiSA43qxcRZLa5YasB2GKl\r\ngV1AQqi5qVfhRbmib9WeSpG3omkP2kBGU+TapLMA784d0z/eOgUE+Bo+zaJW\r\nkztPwGAp2uyluk6QyHF8OvMEm9uR+trs+xzzCwxmaOlyB2SzBSer/EfJVCMq\r\nsDbWDxP2VTkL95kOgo3uuKmJyfIwRfMZdV/KI9X68jta89nAWexpet8RHRmm\r\nnchUE4wLwL/jH2ab1aBNdwjo0tL56/gRo4w=\r\n=ggHz\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/simple-update-notifier_1.0.6_1656358753654_0.41541423715195136"}, "_hasShrinkwrap": false}, "1.0.7": {"name": "simple-update-notifier", "version": "1.0.7", "description": "Simple update notifier to check for npm updates for cli applications", "main": "build/index.js", "types": "build/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/alexbrazier/simple-update-notifier.git"}, "homepage": "https://github.com/alexbrazier/simple-update-notifier.git", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "engines": {"node": ">=8.10.0"}, "scripts": {"test": "jest src", "build": "rollup -c rollup.config.js", "prettier:check": "prettier --check src/**/*.ts", "prettier": "prettier --write src/**/*.ts", "eslint": "eslint src/**/*.ts", "lint": "yarn prettier:check && yarn eslint", "prepare": "yarn lint && yarn build", "release": "release-it"}, "dependencies": {"semver": "~7.0.0"}, "devDependencies": {"@babel/preset-env": "^7.18.2", "@babel/preset-typescript": "^7.17.12", "@release-it/conventional-changelog": "^5.0.0", "@types/jest": "^28.1.3", "@types/node": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.29.0", "@typescript-eslint/parser": "^5.29.0", "eslint": "^8.18.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^28.1.1", "prettier": "^2.7.1", "release-it": "^15.1.1", "rollup": "^2.75.7", "rollup-plugin-ts": "^3.0.2", "typescript": "^4.7.4"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular", "infile": "CHANGELOG.md"}}}, "eslintConfig": {"plugins": ["@typescript-eslint", "prettier"], "extends": ["prettier", "eslint:recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "gitHead": "2ede8077803666c5531b7c2c51900b3c0ce6a226", "bugs": {"url": "https://github.com/alexbrazier/simple-update-notifier/issues"}, "_id": "simple-update-notifier@1.0.7", "_nodeVersion": "16.13.0", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-BBKgR84BJQJm6WjWFMHgLVuo61FBDSj1z/xSFUIozqO6wO7ii0JxCqlIud7Enr/+LhlbNI0whErq96P2qHNWew==", "shasum": "7edf75c5bdd04f88828d632f762b2bc32996a9cc", "tarball": "https://registry.npmjs.org/simple-update-notifier/-/simple-update-notifier-1.0.7.tgz", "fileCount": 16, "unpackedSize": 24129, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCLx+HuQ8y5cdnREnSHdopyKcd+nfcrEzHA9LQyw3egLQIhAIJ9Zzk5kTBfrOtXWwEoKRaXQnbaC6UX+qa000Ivs7mE"}], "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuvqwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp32w/+P6BmAW/uZshhf9bR2EmbK2AEZRMgWK6izE4irVWqK2wWDam2\r\na5YX4/qRWgKgiAQKykduR1KopM+gvjp8S6t5lylG1VK9s/vIqLNrjxETOQdJ\r\nqCHy0S7nTLef47IHNcAaDtzsfpLfgAoCQtD3kqanDwqnRWLJXzG+1TcTyWuU\r\nhSgiLHD2/cpHHIdw3CarKc49jxTUba2KlHcBkD3AIfVus2DltVMSvhjTO/QZ\r\nDGrTKIxNCB7kQOehgP8QToX2P/4LX+rzljyzbsLIw7mgFR9BWKe7LxRCwn8P\r\nmeelhQ6c0Fw+L499FMJoB6+c2muBDU9vRRNLovjoBD7UMt+t9jNCfGWl0brk\r\nk5OF5bF5HevsRRGLaHplEFtQAlYhrV3GPL/p1rZDXBxwGn18F9JTtaSZ5aYH\r\nXwsElECevh38wm1y6yLFk2esvLaJU4uiAjbP5nKawV9LqZJBQLKaZtYYeC9L\r\n4j4lELnfqBgagDebhatMpt90StcgOwGfpJfztxwhkrwenkg+/nLUaEIQ+YP2\r\n2zJVK4pjIrNBJhZqi0Qwbp+ye4IOp3eChUUfR4DC8BDQw21WKmw8J86W+9gw\r\nZUwof9g66H6LQCBIRq59UoIcmGXjFFAk8Mi2MWnGgWDMk+IVIL/O/IrYqieH\r\naCiwwtSpEuUEj/R1jhEesFl91yHgfW5Bpzc=\r\n=293r\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/simple-update-notifier_1.0.7_1656421040326_0.42901890670830367"}, "_hasShrinkwrap": false}, "1.0.8": {"name": "simple-update-notifier", "version": "1.0.8", "description": "Simple update notifier to check for npm updates for cli applications", "main": "build/index.js", "types": "build/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/alexbrazier/simple-update-notifier.git"}, "homepage": "https://github.com/alexbrazier/simple-update-notifier.git", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "engines": {"node": ">=8.10.0"}, "scripts": {"test": "jest src", "build": "rollup -c rollup.config.js", "prettier:check": "prettier --check src/**/*.ts", "prettier": "prettier --write src/**/*.ts", "eslint": "eslint src/**/*.ts", "lint": "yarn prettier:check && yarn eslint", "prepare": "yarn lint && yarn build", "release": "release-it"}, "dependencies": {"semver": "~7.0.0"}, "devDependencies": {"@babel/preset-env": "^7.19.1", "@babel/preset-typescript": "^7.17.12", "@release-it/conventional-changelog": "^5.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@typescript-eslint/eslint-plugin": "^5.37.0", "@typescript-eslint/parser": "^5.37.0", "eslint": "^8.23.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.0.3", "prettier": "^2.7.1", "release-it": "^15.4.2", "rollup": "^2.79.0", "rollup-plugin-ts": "^3.0.2", "typescript": "^4.8.3"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular", "infile": "CHANGELOG.md"}}}, "eslintConfig": {"plugins": ["@typescript-eslint", "prettier"], "extends": ["prettier", "eslint:recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "gitHead": "ac3ea6e781b4bbe52c872ef2fda4187f8d3633db", "bugs": {"url": "https://github.com/alexbrazier/simple-update-notifier/issues"}, "_id": "simple-update-notifier@1.0.8", "_nodeVersion": "16.18.1", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-KPXw773wEyrIIhjZh83kmHGrmiAWTxwhAFOhN09IIN1I6ZAmh1luQnS9KPmFaz3X53dcIJ4II80iYV7z7LsUjQ==", "shasum": "d78fd1857da58e115127bf363bcd9c1973585082", "tarball": "https://registry.npmjs.org/simple-update-notifier/-/simple-update-notifier-1.0.8.tgz", "fileCount": 16, "unpackedSize": 24356, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBuAQNTi9GQVuWHErmgMQU5TelI/ncO9iiWFt1LKMjXqAiEA0zlB1TpBs17g6n5NcNjGmzwDl7FU8GwVjwiGzLpk+04="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjfnDMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmquJg//TZ4dl7mG7OXLqksoqYEmg5N14lzYkrGap3CCAlJ3keJ/c35Q\r\nPUwpg+F8g5yu34GfJf4VSnvxr7q4WTc+hYm7Oi3dtvYXsp+TrUu87M15PrJ1\r\nmnVS/IjQyAvc2H+h4XzgJlS8q6OjwNjrURE+uw5z7crsC1lh6/YdJKszpHJP\r\njnmymWnC+lo6KUaIt5TLI74K7DpVevy4lpQ3N/maCodsl681/3sysLoygM+g\r\nekW1l8vlDGpRrJgQLMCM0rHrUgnZ1mp3nFOoAVVs3WfT+kkZbHN6DUIqcq5H\r\nXhMrfPknXZ2Zzzf/w14spYVLyLZca/3vEPq3NFVJd5EmTBu57rCUBm4f3Wyy\r\naeUVvLz9cYg1kvEJILgrnwx9DJ1lHZIHhr1d6+dInrzGmt4iULd8953O3qa0\r\nFsCHwvnAU/4VVswh0ZFdwU3WNJaO1UUEjs4hFSjZJE90l47LwUO8Q6YoXyHv\r\no6owt3yxemF13X7qVvlSDvhCQh5cOWi2wDtCllKkC/iTbGGh1yVbLttt1vl3\r\nN+HfnIKNoFQSCTgwU87UctcLxmUz7n1k9jpa3pvIywJNToMvMiiJ0PfWQm7r\r\na2ZoLtKwvLPTmulLOtaEEiQo8YCtIUGL8LHx98ALE4fnSsiepxvAY4ZaGlof\r\nRCHAnhkb2hEcpUUO8+oXsavg+JcPhF3i+Oc=\r\n=x/BN\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/simple-update-notifier_1.0.8_1669230796686_0.3410486719553063"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "simple-update-notifier", "version": "1.1.0", "description": "Simple update notifier to check for npm updates for cli applications", "main": "build/index.js", "types": "build/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/alexbrazier/simple-update-notifier.git"}, "homepage": "https://github.com/alexbrazier/simple-update-notifier.git", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "engines": {"node": ">=8.10.0"}, "scripts": {"test": "jest src --noStackTrace", "build": "rollup -c rollup.config.js", "prettier:check": "prettier --check src/**/*.ts", "prettier": "prettier --write src/**/*.ts", "eslint": "eslint src/**/*.ts", "lint": "yarn prettier:check && yarn eslint", "prepare": "yarn lint && yarn build", "release": "release-it"}, "dependencies": {"semver": "~7.0.0"}, "devDependencies": {"@babel/preset-env": "^7.19.1", "@babel/preset-typescript": "^7.17.12", "@release-it/conventional-changelog": "^5.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@typescript-eslint/eslint-plugin": "^5.37.0", "@typescript-eslint/parser": "^5.37.0", "eslint": "^8.23.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.0.3", "prettier": "^2.7.1", "release-it": "^15.4.2", "rollup": "^2.79.0", "rollup-plugin-ts": "^3.0.2", "typescript": "^4.8.3"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular", "infile": "CHANGELOG.md"}}}, "eslintConfig": {"plugins": ["@typescript-eslint", "prettier"], "extends": ["prettier", "eslint:recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "gitHead": "2daeb9f6436eea23c18583ca566f5d5bbb0fa1b7", "bugs": {"url": "https://github.com/alexbrazier/simple-update-notifier/issues"}, "_id": "simple-update-notifier@1.1.0", "_nodeVersion": "16.18.1", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-VpsrsJSUcJEseSbMHkrsrAVSdvVS5I96Qo1QAQ4FxQ9wXFcB+pjj7FB7/us9+GcgfW4ziHtYMc1J0PLczb55mg==", "shasum": "67694c121de354af592b347cdba798463ed49c82", "tarball": "https://registry.npmjs.org/simple-update-notifier/-/simple-update-notifier-1.1.0.tgz", "fileCount": 16, "unpackedSize": 26389, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFOtvazZNmKYB+qIpo9L8gbM8FqPyYo1m/tm9B1HbwRgIgEdrRJGtbrM/SPzyi/DLf7bvF2k5sa5uTo8HxHxfMMg8="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjf6ntACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrwpQ//QMrWqbFl8h+epBf0UN71W93la0c7yBQC1KrShAOQacDowc7G\r\n8N5qNf7FWVsqCixjD76GsAWdQ8FOmyLUSA37/r9JqN28+Ze1gVnJ1RvQG+/B\r\neMKpts1F9d8MBZFxLEwulIIcCgZekSww9LUKggCULGvRSpTcG8vf7jeh2z6J\r\nqazbXPfaeEuFvNtI/xJpyoQmKlpl0cFNUcLphuIhjw98NqspI9vAdqOiniEI\r\nvPHdB5gGw52HMPc+QMYOGHk53gwaSripCmOPtWxmAG6Ufn0om3ntcKOoLeej\r\nvGZTejbAJb3T0U39MYcqD5R7qwbDzOVt40+GNENw3NR2DJbhfb47xv3U6sVX\r\nc5BEJRvK9T6ig8Pf6ehTuOSbg/N2xVySHenls/ZoQXeUAL5+hMsTvgU50Ogf\r\no11XwuMDpBdk9RpCzvyJKClObapMcCMrqzKbPP0qYt/Dtml8H1RC7SJMM+2w\r\nbgiBZ5w+nLf4QJ30HEtJRc01Dgtt9LUjII7QwefyikA8jWah1VCEO71us+RK\r\nk8UkCp5hvmfSS5LzhhvFkoHMRodlAx1Diwx+zBG1FXYxCTWpLm2lLt+ZqThp\r\nDymGfL46zJ15hkH/qK7C0umwhL+fiAuk9S9UbpHfaCrnksUR1ZBYxBfqAk1T\r\n3AvN0G4P8b3m/Mwvi1CuQb893cZ6DxW4ZDo=\r\n=P8/Z\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/simple-update-notifier_1.1.0_1669310957656_0.7410552809919508"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "simple-update-notifier", "version": "2.0.0", "description": "Simple update notifier to check for npm updates for cli applications", "main": "build/index.js", "types": "build/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/alexbrazier/simple-update-notifier.git"}, "homepage": "https://github.com/alexbrazier/simple-update-notifier.git", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "engines": {"node": ">=10"}, "scripts": {"test": "jest src --noStackTrace", "build": "rollup -c rollup.config.js --bundleConfigAsCjs", "prettier:check": "prettier --check src/**/*.ts", "prettier": "prettier --write src/**/*.ts", "eslint": "eslint src/**/*.ts", "lint": "yarn prettier:check && yarn eslint", "prepare": "yarn lint && yarn build", "release": "release-it"}, "dependencies": {"semver": "^7.5.3"}, "devDependencies": {"@babel/preset-env": "^7.22.5", "@babel/preset-typescript": "^7.22.5", "@release-it/conventional-changelog": "^5.1.1", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@typescript-eslint/eslint-plugin": "^5.60.0", "@typescript-eslint/parser": "^5.60.0", "eslint": "^8.43.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.5.0", "prettier": "^2.8.8", "release-it": "^15.11.0", "rollup": "^3.25.2", "rollup-plugin-ts": "^3.2.0", "typescript": "^5.1.3"}, "resolutions": {"semver": "^7.5.3"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular", "infile": "CHANGELOG.md"}}}, "eslintConfig": {"plugins": ["@typescript-eslint", "prettier"], "extends": ["prettier", "eslint:recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "gitHead": "24a1dd0c5bd360f2ebe5182d631e8dad7c9cdecb", "bugs": {"url": "https://github.com/alexbrazier/simple-update-notifier/issues"}, "_id": "simple-update-notifier@2.0.0", "_nodeVersion": "18.14.0", "_npmVersion": "9.3.1", "dist": {"integrity": "sha512-a2B9Y0KlNXl9u/vsW6sTIu9vGEpfKu2wRV6l1H3XEas/0gUIzGzBoP/IouTcUQbm9JWZLH3COxyn03TYlFax6w==", "shasum": "d70b92bdab7d6d90dfd73931195a30b6e3d7cebb", "tarball": "https://registry.npmjs.org/simple-update-notifier/-/simple-update-notifier-2.0.0.tgz", "fileCount": 16, "unpackedSize": 25785, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHi439HIw7vmd/PzYG9GO/j7vahBxL5r2WvvIuqZaLT3AiEAokZ9h2FokHMJxoIwqQvUAJ9Nlsj6Rb7S28ySxHn1ktE="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/simple-update-notifier_2.0.0_1687780547782_0.7826928410028098"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-06-23T23:40:48.650Z", "1.0.0": "2022-06-23T23:40:48.802Z", "modified": "2023-09-10T18:41:40.826Z", "1.0.1": "2022-06-24T00:40:36.201Z", "1.0.2": "2022-06-24T08:55:45.108Z", "1.0.3": "2022-06-26T09:34:23.327Z", "1.0.4": "2022-06-26T13:35:54.123Z", "1.0.5": "2022-06-27T09:05:24.344Z", "1.0.6": "2022-06-27T19:39:13.860Z", "1.0.7": "2022-06-28T12:57:20.486Z", "1.0.8": "2022-11-23T19:13:16.883Z", "1.1.0": "2022-11-24T17:29:17.826Z", "2.0.0": "2023-06-26T11:55:47.974Z"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Simple update notifier to check for npm updates for cli applications", "homepage": "https://github.com/alexbrazier/simple-update-notifier.git", "repository": {"type": "git", "url": "git+https://github.com/alexbrazier/simple-update-notifier.git"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/alexbrazier/simple-update-notifier/issues"}, "license": "MIT", "readme": "# simple-update-notifier [![GitHub stars](https://img.shields.io/github/stars/alexbrazier/simple-update-notifier?label=Star%20Project&style=social)](https://github.com/alexbrazier/simple-update-notifier/stargazers)\n\n[![CI](https://github.com/alexbrazier/simple-update-notifier/workflows/Build%20and%20Deploy/badge.svg)](https://github.com/alexbrazier/simple-update-notifier/actions)\n[![Dependencies](https://img.shields.io/librariesio/release/npm/simple-update-notifier)](https://www.npmjs.com/package/simple-update-notifier?activeTab=dependencies)\n[![npm](https://img.shields.io/npm/v/simple-update-notifier)](https://www.npmjs.com/package/simple-update-notifier)\n[![npm bundle size](https://img.shields.io/bundlephobia/min/simple-update-notifier)](https://bundlephobia.com/result?p=simple-update-notifier)\n[![npm downloads](https://img.shields.io/npm/dw/simple-update-notifier)](https://www.npmjs.com/package/simple-update-notifier)\n[![License](https://img.shields.io/npm/l/simple-update-notifier)](./LICENSE)\n\nSimple update notifier to check for npm updates for cli applications.\n\n<img src=\"./.github/demo.png\" alt=\"Demo in terminal showing an update is required\">\n\nChecks for updates for an npm module and outputs to the command line if there is one available. The result is cached for the specified time so it doesn't check every time the app runs.\n\n## Install\n\n```bash\nnpm install simple-update-notifier\nOR\nyarn add simple-update-notifier\n```\n\n## Usage\n\n```js\nimport updateNotifier from 'simple-update-notifier';\nimport packageJson from './package.json' assert { type: 'json' };\n\nupdateNotifier({ pkg: packageJson });\n```\n\n### Options\n\n#### pkg\n\nType: `object`\n\n##### name\n\n_Required_\\\nType: `string`\n\n##### version\n\n_Required_\\\nType: `string`\n\n#### updateCheckInterval\n\nType: `number`\\\nDefault: `1000 * 60 * 60 * 24` _(1 day)_\n\nHow often to check for updates.\n\n#### shouldNotifyInNpmScript\n\nType: `boolean`\\\nDefault: `false`\n\nAllows notification to be shown when running as an npm script.\n\n#### distTag\n\nType: `string`\\\nDefault: `'latest'`\n\nWhich [dist-tag](https://docs.npmjs.com/adding-dist-tags-to-packages) to use to find the latest version.\n\n#### alwaysRun\n\nType: `boolean`\\\nDefault: `false`\n\nWhen set, `updateCheckInterval` will not be respected and a check for an update will always be performed.\n\n#### debug\n\nType: `boolean`\\\nDefault: `false`\n\nWhen set, logs explaining the decision will be output to `stderr` whenever the module opts to not print an update notification\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}