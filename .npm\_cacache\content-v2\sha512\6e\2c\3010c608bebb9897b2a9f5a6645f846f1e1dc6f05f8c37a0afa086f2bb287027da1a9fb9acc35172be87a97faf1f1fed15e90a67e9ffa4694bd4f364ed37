{"_id": "@babel/plugin-syntax-jsx", "_rev": "114-c4845d1a67fcd20e0c470b46fdf8c6ff", "name": "@babel/plugin-syntax-jsx", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "e25c1733215f7db77f8461fa8d7499e7798862ce", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.4.tgz", "integrity": "sha512-pDxNWOLJ1ItLHQfvKby/85sqoPPCbUP3mtV0Q5I+oe9yUYVP+NnUOcmuOGDpRF9kBe+BwHmdNGfdRdx4hdbljg==", "signatures": [{"sig": "MEQCIGy5uGYSLwwg/mYmOuahey2Zfy5+LIIjsemMZvtntSJeAiBQxU091pgimlUXLhQsgNIB1eif0J4U7PUm8zN1vShUWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "5.5.1", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx-7.0.0-beta.4.tgz_1509388460653_0.38778818189166486", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.5", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "7157e2107388b4ec2ba1ba4d8a11597ee1b6d61a", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.5.tgz", "integrity": "sha512-It8AGX4Xnv/HeQbgViLaWmQX7oZpcCKVw5HT+aLNNlOm+uDk7SQEjR+JT97FSm0EMFSFTjyuWBqDrMZ1QWOwYw==", "signatures": [{"sig": "MEUCIEfBdpq/4hzs0vhNRCqBwdHoo+j+T3AjynHYe6rp0JApAiEA7bbdSbmV5VK1ygPGZyPqxuSwVPBTY2EaDFuiWlLKZ20=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "5.5.1", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "8.1.4", "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx-7.0.0-beta.5.tgz_1509396963643_0.5167733312118798", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.31", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "91bd862539a738d1db5b7f8839f5a7259d0981c0", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.31.tgz", "integrity": "sha512-mp2velbcUjwMsYjwfKsE/OZJsa/6hV4zBJRe72reWhZIMuF3vs7QoWR4UTivQ4VZxQDpm4LoUW2LSXbxl6xILQ==", "signatures": [{"sig": "MEUCIGfvZiQhhG9gkXjAwbvohURVrip8bp5uImP5LJKYt3iPAiEA9i8UUNzIj98QuYbGcvbfWmhkLBwFaqa7inZSCL+v7so=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "5.5.1", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "8.1.4", "devDependencies": {"@babel/core": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx-7.0.0-beta.31.tgz_1509739395004_0.9551925798878074", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.32", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "ef395991c8fc7ec1bd4678c386635f754b9792e9", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.32.tgz", "integrity": "sha512-uzI9Qm+udgFo2QL2ozULUhhsWJy3Bv7gAGYwhPe29EZshy1EhBmiqXVwc3FZoPCckliBeoQdDWjGQgWcU2Taog==", "signatures": [{"sig": "MEQCIAc5D4rc+QHWdpo4ynk6GP4dyjXmqOR2SXOdmvQ+PPOqAiB0vesmPY7LdDxwemyBjwYj4x9tkdkttPVJSd/sioCTIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "5.5.1", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx-7.0.0-beta.32.tgz_1510493583165_0.5108483650255948", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.33", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "72c4b59f4654c47e58751a384e41ba0b2daf0ad1", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.33.tgz", "integrity": "sha512-IZ+Kl0fSAOK6LMn+3NZItbJmyaBnbNp5ty8sNEzHdVAeg8Ah17PyNPTMFhGtn7LHB99SkTW4guPPIxtfWMjg1A==", "signatures": [{"sig": "MEUCIQCzQ4fMpyLwe9DV+N+h2jlCb2TWzymBT8XF0FGWZSVcjAIgHWNa7xYwQIcnjTwrNNX0LAXENiDJvZJeUPcJytPTwe8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "5.5.1", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx-7.0.0-beta.33.tgz_1512137832911_0.34203107305802405", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.34", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "15763aaa2ef2df00fb9ff6edb7b4295622e52514", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.34.tgz", "integrity": "sha512-ul0JfSnAyhhsaW+sUlLQTXNpFVrClUZrRNhS8UKgOtvT89X/1qyB6yINEGRULrFn8Ip7lsVFreib129b76UHQA==", "signatures": [{"sig": "MEUCIEm7AlfBbD/SoRxxYoDqdT1+5eV/6wiywj0mkyY5UCshAiEAvb5VMH48XCwI+QwKu7676o6qT7QzgDyCHVFBtYgQpLE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "5.5.1", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx-7.0.0-beta.34.tgz_1512225545935_0.7619688352569938", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.35", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "fd802ab4d7aabdfdb8467d9541987afc051e6f99", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.35.tgz", "integrity": "sha512-Oc97eUcDPxAnye/+qq7wGGabMBLwLrntxYzPt3Zr1YnYecKK+BjyUK4+PTX5tKRC4XlToWFijsHet0mj/ZKKBw==", "signatures": [{"sig": "MEQCIHKuLWcK2N1l7bq0Fh2nigvdGK+tJWwWgKdfRQXaXxigAiBDfpUa9fCkKTyTURYK+m+i7LUHjd6Tyv9x6f0hd/kIsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "5.5.1", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx-7.0.0-beta.35.tgz_1513288055527_0.9178325613029301", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.36", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "ed12dafe2cb9d3533050d3935d703ef168143371", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.36.tgz", "integrity": "sha512-j0dwdk3frPLYxGWLn4113/Z27YBS2qCBRSdMOMhUN8fldbQEW5n27IEcGaQ+cvyBQdVqog9Hhot+BNCn6srXbA==", "signatures": [{"sig": "MEQCIBQud9LJm4crAuBEyG7+RuEAB1Z0dlosNaUzTKYdYB4iAiBMWWPU10MjvwyzE2nBXBGArR4uQbiaVkW85OZ/Y4CruQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "5.5.1", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx-7.0.0-beta.36.tgz_1514228663724_0.040064160944893956", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.37", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "df278e1b27f0247e55bf0f8ef4b7b7301a623ef6", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.37.tgz", "integrity": "sha512-5ZER7tkR6E1V/lUOTLqxrBDm1KWbEsMppdHsvKdtHxY5WXlW1TCtvKwhr9dradrl8HA+9tIekL2k724f6kMw6Q==", "signatures": [{"sig": "MEUCIQD9oB1momvppAgnexooqtU4LPA2sWBCMFT6Pf+eOzP4GgIgbMhsmFc6MmzFdwrKQS2+mOzW17AgSmJUrKUqjFH8wqc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "5.5.1", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx-7.0.0-beta.37.tgz_1515427343184_0.6907487413845956", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.38", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "1644f05630105cdbf0d6b4285f62af79fd8dd37f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.38.tgz", "integrity": "sha512-UmowkKfyyVNUdQ8HSLII4jC9wZuLsTrynsjwp53jaIzZSoGjKAsHYT6HF8JQkG01DQTN9l9trSv0LClTeAZgbg==", "signatures": [{"sig": "MEUCIQD78LfY4AW8SggsZlFd2AMWciY+L+xGSu/O/wCDljyziwIgOqNoMbdeYvhnDPat2kmPCyBEHo1ecevZkKz+ylYq8qU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "5.5.1", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx-7.0.0-beta.38.tgz_1516206703384_0.22926845191977918", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.39", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "dist": {"shasum": "c7f0d1a7e6bbe503a814a265d7d7322f2b230bf6", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.39.tgz", "integrity": "sha512-Ir49hnpnyDTz/RlTStks6FTihjNarSOp8jdutwU2yFgBTWFM1OnRgIZV8gVhZjinZDGc6UGdVTr03QtgLwW4bA==", "signatures": [{"sig": "MEUCIQD/GPXDnIlCIxLWb7jMGVCgDnDnUsDnwQbW735M87zfqAIgWT1uTuwjlk4aQxvnRA/T51G9Zh+jQvad+veRNG5H7bw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "5.6.0", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "8.9.1", "devDependencies": {"@babel/core": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx-7.0.0-beta.39.tgz_1517344047050_0.24711652006953955", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.40", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "db44d52ff06f784be22f2659e694cc2cf97f99f9", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-SLicyImotduRkkRtB9hKtEbwmtpLbMNFxWEwkJINTCOHO9slloGAT7GIEztr6Asud7OurxuSo/ORuOdRyfG4LQ==", "signatures": [{"sig": "MEQCICuF8ASXRXEF5g8C9NOjrA+jp1lF4j/kHXvsQfjb2UsOAiBymobVk6fb0HDpqNyrF8Xhouosew1gGv30/z5dqLu8jw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1087}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "5.6.0", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "8.9.1", "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0-beta.40_1518453683892_0.9718305630288104", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.41", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b52ebe8cdf19973f63099b09bb6c9468ff8062d3", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-k9VpxQ54oZdwT4mRGPXN7GV5EvLOQFdz1KOrVeMbhB7hUKY/Seo5ET1zVEuAyY2+akF/eZcf8oen+Uly8bAruQ==", "signatures": [{"sig": "MEQCIGxc8ebZgD9JvCrE2tUpFvPNDR0QXQRoSePHI3GfZsfsAiBxIsNzAMwnJgv4FQBxmsuxnQbc2PB5Z4eM/c0EImxw3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1322}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "5.6.0", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0-beta.41_1521044755470_0.6410935320940798", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.42", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "92ef7807bbec0e12a49815a409822262cbaa7ddd", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-NWlawLcYkxMpH2QK4CbYg1ctTuFaKYRVBZGiGkgcO7xD59bNUu2HTG/BXynuaheQiGX96ItoK1igV6vuS9BK/w==", "signatures": [{"sig": "MEUCIQDTxtGR2KdRfdVbffvLH35hFqAHrfmaCb9q/qGd4pVkDgIgbrXkwTV9V4pC7LLr9XRTs7UHyHZrfI7ahKQoaxzBobI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1322}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "5.6.0", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0-beta.42_1521147030789_0.13332132034294486", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.43", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "abd89ce875df4e9037ca20dba00d57054af2ec59", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-sXrt7md46lMzdmdljlh6S6NXQi6OJMdOZPTcKae/hkBYx1SURJL19G+UGfAZKTWESeT+Pw9DS2nlGMuSIdi6/A==", "signatures": [{"sig": "MEUCIQDdekH367GIndTnV/iFvjY58vajGhMTmlFEIECpJ89PHwIgcuadIw1CvueEbhVpHwz1R9EIseH2qHz8qYKktNobTP8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1427}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "5.6.0", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0-beta.43_1522687696357_0.7446871645412301", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.44", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b3475f0e6ea797634f0ba823273d76e93727e52f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-q0AqflJKM+IcyUTblGnVckka160/tUYkDF77o4fpkk3uCb7TxQIXlnBM8V/mx96Tf+JRyE0AHFwt2mHomd0mWg==", "signatures": [{"sig": "MEQCIB6eBWg3hdSrYXUp8CcwA8LO/GuGXCuV+v5VaIxaghxGAiABcY0VrDxAtyFGKsTqUc4k9KcN+dpFmREf+9yvBZJmrg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1478}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "5.6.0", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0-beta.44_1522707598480_0.8329493059729978", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.45", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "0b0dc8368db9e948528bd3732c4bbe56e057dace", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-31z8UocRh9sNBR3GxUP9Qrat88sOdC5DTp00RCFvfW+Y6QRgWv8D+RB34xyYGKXJyUL+QpwPLepSqDowmEUHWQ==", "signatures": [{"sig": "MEUCIDgX17S1Rd4z0aN4izVtmasSzMtSYMpIt/O6OIUBPWzKAiEA8nZeoVosZ5Rqtkl7YcsnC7ENmjCPSfJ1QfcgjB92DHk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1478, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1DCRA9TVsSAnZWagAAO24P/jd0wmaPEkwIJkRgV7EB\n7KUCx8QzYrCKvV+UNWsyvA0dVl8LkVS23sw03o141VkRwUu7qdJRgUo+zoaR\nXsD62ogWyVbOyOWZc8KQNh/WJLv91W+ZUf2WvAUAR6pWgTglKHAtsgBVXjny\nF/gIXngH8pZOcW2vp3UHQqdBUbVcHmo9H5eNTwtH9HgHg1yt0IOdrCldCqGF\nLU9oql/HRda8aIrVWY7TgZ1mnRohTu5pEpzGH9KTbK00xW9xzgFOR3h5d9nA\nRY9tXJI0BOpjqIsMYlG2rZvOJsjFL4ReqjWvI/R09CUath3Ol8lWK7KItunS\nfxvqA/IR+Eq1kwCxquPxMgtC9mdIL03ppA5c0DxIsMor08gVyzF59HFLkkco\n1Vl+zYWA1rbkJfEZTHkhYv0XWcZNONZnrc7p0+l0mTrssUxKok2H49B5gJnj\nYqN38N7BzwEgPDsesNDHyx7meRByQeiA0C7AynJJ/hjBO3efbivNLthMNlsv\nN0ELTQpFxDdOdMexpUUZU0UG12j1De7v6xw2+plMxwz5b34RwaHzvs7HVFR6\ngX8PCspD+rqRvj01wPJI/vOklfU2aMwjJErTfaEAbLOU1dYx174KF5t24+sY\nBk6c2DiaAVty5IEmyImtsJ4vJn8+pPDel7BKFjV3zrGzj28Gg0D6Uw4ayhzf\niSjm\r\n=H+Lt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "5.6.0", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0-beta.45_1524448579268_0.24324076192047817", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.46", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ed2e8a43716e7904ae33dca71d5f2b436f0f25e8", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-aYN4vmO4nMux1W36F6/YP2ugNQ0cilrs1eU4jClLrlIouxqd9hqBloWtlGmGlyDxIRV5kzr+UWwridLDb+cN5g==", "signatures": [{"sig": "MEQCIGD8Lp6Izv/dQ8PksO1/YXda5p7oMKN9RVbIVw3Yas1WAiAzpMw0SGatu5UYUCguIlsggB/E9rOWvKmh7HC6fsxAcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1478, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WFzCRA9TVsSAnZWagAARk4P/A+v8oEbh23ndh2PkfOB\nG1THgtRf+EUe3z8TF8qQ7IVN4n7/InBlSCK7kWQuNTK+VnsV/4zNFX9y5WrS\nH+mzSoJvWTIS6Fvo/HwcA6uFHULVIP98kaq6yiA6+TqD69LJWmq38TYzL2wY\ntfm5+a+tch/ZCRyuo10U2kvMZa2Mr1n0/vpk6GEYzoml9JKSBtwg9xX701dk\n/HW96I/rTRrN1HxtDz4c+l4OKrE7rXDbze8FRJQA01eydHZPvTtyJ2lCZI5u\nnhIe3hzO/cfqhmPCKMpvAlXg58vybmgaRiZLoeYT4t8k8p9eU5WrAV3Se7V1\nXCCpayilQ/nlSB7r0sHNLsmcS+u3xaJDCp1bBkoTxvgrndU3xSr4/YPS744s\nUXCEh0TcPLNbmQ1rjbvlEg7xqlO41CJKT/z2FUk2JG4Flj9JmCMOOg6TTAy8\nRkLEzNSpiI1nHdZj1IIXiNqEJk7nN3gL0lEgwnUnf5SwwGPemH/OrlJ6vGQE\nKtw1X04sByVH5NHFP/a0QMp+e94YB0cVGdM6ms7mVle26hopfAOihfOrkkh8\n39ZIzdmcpAqvfuo8hFZeDDFn5LgrP2IsUHD23UftF7kaZ6ErINU27vnVVbj2\n73Vt2B1aBgR/H+lQAp2uC6PFPYCfn16lANP/IaMcQ//+f95es3cErNrt97HW\n6SHT\r\n=fOMQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "5.6.0", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0-beta.46_1524457843150_0.14916640038209006", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.47", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f3849d94288695d724bd205b4f6c3c99e4ec24a4", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-5scuJzIYZY8M+A1ra8mcKANIwB5TtsRD6Aw94xZxfvnjhhVMFR5RYE9HshVlBrZVY+r3cJDNIQLJMC/fGJHImA==", "signatures": [{"sig": "MEQCIFj3dq7f9mQKqoSG+DkKc5ikn+kugPiSJJdzl4Km9Dq1AiBAc2VsH8Ofez8kC2knai+baBA9jJeEZMeyk9zW7pK8mw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1445, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iT4CRA9TVsSAnZWagAAQaQQAKPhciKQ2ebvls1evQxr\nvc1lFkGjQB617SxuCFjR6G6q1UlBdg3E/E+S1KLByf1gHLwy8r1i/3uR0b1Y\noJOO2w3fktMYDRFXFfe5UjNZ9YhyW6dNxqrFnFx0CWTLbDf0LMSNZi3ETcff\neVwT+Caji5VA9BQF34He7++XRqL39vI5WQ7t3vaFJyrm3cW5HGNCTfTJ9fV6\nn0LyzI3QSTh87rkkUxtMEU44vaMQhZrF6wrMYH0yJAUP/u9TEnL+hOUUACnl\nw1D8PjKqnvfTmSKoy7LEO9ilSP2jAm+vaog6+8k3aclpdKumYLNMt/TnDDNL\nBpLjXqqanRL5ARF9ZiF/OddzTMXZMZ07al+w4nxJxV6tqG3UbKg/zEqWqvLf\nSBKhnC4drO8GoDLbGGz3UxDFCDUA8nDAAXjWpH/SaySbwSkB8csaj9PiQuAv\nTrRGKwL34Mar41FGd24xO9uLxVGDRj/LCmZSuUQcM/DynIYGvu9jwMartWVE\nqQ56RTOv45cLta2sdCAVRgYU9QCTJStDV1EvPkUYtOoz5YmhWw5fIvWLeFQy\nzYifcWswES43Bq70SsIlJfsnHheT+6mNrzmTeuacw8k/5JEeWfhn/uJ+yosx\nCIczUsAZjXlp7h28Vh8z8HK1KdsYyL07pqXvNbc4QmmPQHFcD6mcouJ9T0ec\nr4d9\r\n=XE2X\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "5.6.0", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0-beta.47_1526342903545_0.22064759306638027", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.48", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c8bf06aa238009fd0ae01d5d490b7b455a5e6295", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-cXdKK+zz6VrRUZJhq0oo7bfLjgME7y4N3igl15VcOS3XORbyOaWwHK2Nwsa8AalOcGEjvkmAmqb40Ht18/XiXA==", "signatures": [{"sig": "MEYCIQD6fqXBEzeFu6T3tMtxB3BWgTMlETp+fP5aa5u82/SdywIhAKU3teT2fRfRoJaa7L1LZvsPlnpdgU7XYBqKAAE8YBPa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxDJCRA9TVsSAnZWagAAhqQP/0HTtM+f5dV6z6eJvVz2\ni5B/4loAcXJlL3VMxGUv7aCYpwVMps4bCQ/qv3hcZ1Hb71HEoouCm1m23OdL\nfgAD4T0sAjBF6X7uLLS7Sk9NDl+dnnL2fx34sZSn+o3pBPWJy/v5YT4Hw0MQ\nvGZorYWtl2fZigZYM03m21nIAOhy27OwJdla3Sz02cT3+GVw/kTAXhP0Yi6f\nkBiAZ6Kl/n90AgxQEJP8YrFm2MdVJOCtLPDJddVxz6TLOjt0jquX7Os46cdE\nI4ccmvoH+A9b9+kwrOOVDpm3UzD8ZANWmwjsaGi6xkFKxbxhtsvo9CqKhHxt\nNAUYGSrGavseeRfTJQlvME3p5oejUQkpMySrl+0YNqfaymrakHH9SdoSySG7\n/vH2m2dheplUBgiaT5GF7CgQNlc4f4hW5c2oB4mmwvSEgY/TUC62z+5Wx+wL\nTL2oPaZZKbzU+GW447+tQ10oG4nTBkX/HGYZOy2GYHcUCakDkmqjtwEH0H5R\nI/vvBSsBX4FjgRPxQFXNdlVmYZU1j+TgLBytI/6MxgiJ7dGAm9hRSDUriHeW\nWPI/g/GxoAolUP4rZ/zxfHjrmQWhMjKEvUGzNUf/NXB9cC1DLD4FtFNcdtaH\n2BhWmVGRTCFk1l5dzm2q6n5EN1YBFfJyWLJQzNJyDgDV1PkVdVokA6vbs+FR\nS1wc\r\n=pfeF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "5.6.0", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0-beta.48_1527189704374_0.45095972356696", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.49", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "15b832504b49f116f9c484e8e40a5e17c542ed13", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-qYXNIMDJF5h7Tn/GodM7k1iP8HlXt/8glJ/A3D9fv0ASblDeTVkLhZtwL443/WnzXM18N6fIGyoj3I5vuGd9ug==", "signatures": [{"sig": "MEYCIQDo+RA1c0rb9DwdxEOpKzsnsdm9lKULHG65AX2ogGzp2wIhAK3j9/LiXlVn1exdPKZW79rpO+a3JOO5LbquUJAEyOo5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1557, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDNTCRA9TVsSAnZWagAA9xgP/jWSKFyBX7Ki4woZT96A\n7KKVpcNjLYbWLTxS1xtfumrj/vY4/Lu5o1yxaZBkKF6fN7qTWJli+bpkEKAN\nYoiv245zkzFVpt6Aal0sBDhC7dHTkV2D5QbbDnDWR0a8ZDV6LYBVWIqaWZxQ\n494CVRB3GQ67eK8Uo2a8g9Z6bX1OdM+HNHRNccjcCShyBWw6BakZGn/ys9A6\nJoVagcR19F6f1FaaXlBhWNNyoLqzV2KcoPFsh3E9lH1awB2BPTjGUwfx6Z1H\n4rGlGpds3PIqTQxYWdj04mw+9Y9YLMYARP+2oFlKp6YKbYA1apf4XnqVhq7o\nLLPz88i3YFYii8iBVoY6MhgFVXEIlLoMYGaXlSrKw+U8Vz/d1MN7ILn2E5kc\nmGRRfzF07cYpYimXXd68Ze9RggX6HnbZV9y4a0Fa72wVOCKjwlKSCuT5Or/g\nuOr9mujQMLWdmS3CZNI/9Y5n/2CHc7h7v1Vh73/lmkPWXcJ0JvhzEY3kuL6c\nh3YXjUSqr6rfraNNtAtNfueMUvvo3doQ3VT4R2ynkB++45d/sVx3BK94SrED\nLh2jUr9RX0Q5ILf0AmDojWyir8Lj7xhwvRwSmCVSrok33HW3skx5pnqPhSqu\n8uxhLK3UflNnVWYFNVeBlSq0jeH86toOOaNy0vTdMkcwMX3vo9ul/U3qmSKl\nwm6i\r\n=1mIO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "_shasum": "15b832504b49f116f9c484e8e40a5e17c542ed13", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "3.10.10", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0-beta.49_1527264083632_0.726754058072345", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.50", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b4c2df7ccad6cea6940b28d59d208d5df5f13720", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-xCJt1lnX4YVkUEfXrke7FYrRhPO9GebGoUxCtFWc8rEzdCgW9KbO6cvJLTpmQRceIhpuAzAzyEuv1II/STmEQg==", "signatures": [{"sig": "MEQCIEgpwHFK8VMHDjbuwjWjuQBurAZPRKNkiMNItalJGbgLAiBi/pMGx0EWw6ohq0sGlYsegqBWjuscbLnylZB+QGa6Gw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1429}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0-beta.50_1528832826246_0.4335919461022828", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.51", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f672a3371c6ba3fe53bffd2e8ab5dc40495382cf", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-HSfwoM/DdUcd6wjZZLepPL6ZpyYGJ6nwsRpGMbg1ALiulN68usDYDj5RCBVGAMqjLRhX2Uh8YmDc7g1MW3RMFw==", "signatures": [{"sig": "MEUCIQCz8rEiTZniIQEHi46gxbJEsy3+I3DH01hJxg/QxRf/2QIgBhdaTjGo9YRquwzCUkYYFXU4jXmpW/tjoLOsUuQIryE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1443}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0-beta.51_1528838373710_0.09764703724609869", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.52", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "ed57b2ca5ab5bcf931c419b111e8df0318f8f65e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-LsX+amDsF3WLE2neK9QW5XV4/1dssZLm1j3olxMzomiSDX/eogT80ZENzfaaLgKp+NuqJOfncwObH+ssbSDV/A==", "signatures": [{"sig": "MEQCICsAXRTQ2zTidboB39SO1L5zB651Z1hReuPaJqyyda5lAiB9RKP103zAbhOCtQbjUUYLN4gH3HDKih+VMHx/VmBokA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1442}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0-beta.52_1530838759292_0.7207141759147897", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.53", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "223cc897723339cb220aa8216c655086feb45398", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-witK5fT2X5p83spu6SHwG51J5IHPXAuY+UtYrS54dVnhG7qYANB/F+bO/NvissiSK48VHUWEk8fH/5HiqDdNqw==", "signatures": [{"sig": "MEYCIQDogUQV0ot4OXr0kike7LHFllM4mhtKFuwdxtSwrz17gQIhAP6zPjtVpwO+wa2Kj8W4eTCgzXOyJYRj6WT0JWj+AIKR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1442}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0-beta.53_1531316409396_0.591631558678563", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.54", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "71171aee902b94ccf2e22a810d1426b5165b8d84", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-P9Ee65tB/g9g24SXZ9iKq0CU0NBUMjP0ofC9zGYDa9NnX4UsjozmvPsYXQc9xXTKCqa+F15rkuAe17p7mSu52A==", "signatures": [{"sig": "MEUCIQDXnBJl/KS0LdVb443V+zEofzCMm/4KKXiPT3jmhJpd3gIgd/wmhGB7EyUSozJd0VIfAT3T/AfNwREBLtkMwWWLC9E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1442}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0-beta.54_1531763999887_0.7071664145908165", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.55", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "3c16cc972b31c27d4c2e6388e834f146463263a4", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-ms+t8LBacIZij1ODH30dyzcrTZleHese4CgZGv23RXhGNpBmveKl4olWDLIPTbuONwyQE74hkWnuNzu0FA78Zg==", "signatures": [{"sig": "MEYCIQDHENbl/D2E91fY1g+sjvQm+8izCU0PL6q0uE3qnhJ8nwIhALacCU8Td0oZV/MnMCemRv1ISA+8uud3PSHSvMR/majo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1442}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0-beta.55_1532815624794_0.867291975277185", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-beta.56", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "d018279f6fc3cae489f72022fa449c568d913681", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-QsZbghj9DemNxr6ZX7V1ULukXrb+d3kRAU9ErikMnSCx60tKW5MQCIuSnHjr1l5wU4XlAZT2qclb+RYTTz0rAw==", "signatures": [{"sig": "MEQCIElOqXZx7Y88gk8Cuv33IBxtn89N0JnTw1iQHqj0XgmuAiAfiiYKwzZxuu0mEzaR1aZA6JpKDOMVGlrmQpHO7mJMXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1442, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPugCRA9TVsSAnZWagAA8iUP/iwub2VaaFj/uzVGekWq\n5DdJnw+oufwmHevnnmdbqN7nY2zv9M6t0flnGv+bOT72aDuP54QtvudBWhUv\nEXM4OP3Wf/a6PhZz1xXUSZP3MiYf1fKvOR4EHbtWwjTs5UMwdGpxFFoQH/Ra\nVciM7m7WjtNTY3O5g3BCtXWwvK8PTiy5TcwTkj0nVFrQsTZpgEVffFwh/lLE\na869C9qvJbEzHHhzZHZkikPC0/UyctoBhjseaMhiA4weakGiWWyiIKkn8gg/\n1UaTIzQ8/PcPy4nYZmlRPgCltdUSVGGa5556Cb8syHeYpYUx8c+tKp6v4nBU\n1Ai1e5YiaAn7kVZ9GbDNLwIDicHhxLQlUsq+fa96S/m/CKkAf6ABdr9dLL1J\nts0cUj0wCDxeqtIu4e4Gho52NyOWOJBHQjp7+WIcSy99/FGswVZZsJ4khW/W\nArYgO8T6tuTq7AzOYlQH3AXe6W+yIne46g9Byf2HIGuXrHc7XosIfRNEPv6G\n8Uauq7ph1KgSyOi+YIrOGepVQXfn3IDhlVIx1Tqcm5Mx2EZuQP5bSZWdO6RE\nRDBHIwYXKDSwYsoopkKQ2yC14TtuJNnBEzvQ/D8rEcxxTVDq4vEY5bgmN1SB\nYPnqX5EULcuJxbEFTlr6GhJkMzsGv1+RNvTvy+hfdl9AXy/3QFwNJMVUONKY\ncbG6\r\n=/4D0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0-beta.56_1533344671522_0.37415261870154826", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-rc.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b93dccc95fd5a2d0279fd5c3783bf2631ca6b9d8", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-j4HW2Uk5FXJYMklPabUzxH7uHJPKgTvc24aSeHmPtisn73Gkcjgg0Hi630GnAIzbDT5tgR9iSiCYOK7E8M308w==", "signatures": [{"sig": "MEYCIQDQ0AFGJXqAgDkQmwur1+BVXpr3H3yFJehBrlWu0U6wpgIhAMNR2D+R/m+Ykq03AmcgEX3cs4+3PcjhIOc4tisBYuWw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGR5CRA9TVsSAnZWagAA3rwP/1afga0KBy3zbi2y/J/f\nPF0WgJ/+aCQMcsfHc9xTHRY008i0PepGOT0d2oF6oa/4tRgLVt1653KYmtKc\nJgPHfj2gnBpW8UBKl6QkjUI9VFfvtLiTALe4mtLaj2ZH6WiUuDG/5OJGjxB3\nWpbvyQ2trH0fj1FCnaWVBMuKQB4q7jz4r8WGOEAJESs8VhZw6AtjZWg2BUI+\n7kJ/TpbIwjRUbIo7ZYsSONWThXb5APDLjvhIFZqBSzAMfxW6bK3s4p5M/diN\nh3lcSZcKa2DiqIHMxzAUWs56ZymWGOIouiXWpd64UICudud2A/RCHMtUX5BT\n/tdWcxliRUnSHhQR2hdseVyaPP/ZUl828MHEO0/4it/0G/ac7xKjpp1lf09P\nAIW6Hf2fFRYrcblYDD8ScSWAULJ3iPJ2WHZ0YjcRt/2EYR5UbFRj57u6blPP\n/b72J7iwXWBeriC4d3TnJdFb8BP4Slu9rDhRtEosckSKUrWmzUzXuhHMjiR5\nkrkSErjhVkc2qqv732Fr8XW+42i7jCiAYZMYJOZ5eYMjMR7PfBN2pnXNqG/I\nueS5B0pSfcEmD0Y6tD/JPss7mfObTmVKPCaOwFXiIClXO1bOtT9eweXVHDmE\n/vsyTZSZQfAnTzoVaHPVJSl52Q/p2hBZqO4xRbHw0hopjwzKmOGT6rqDdAQt\nvARi\r\n=H/ph\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0-rc.0_1533830264959_0.17905158770962393", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-rc.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "f7d19fa482f6bf42225c4b3d8f14e825e3fa325a", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-n0BcD2LmCrQkDKRhUd7lSiXiRpbo6Z7x77v3FSuevH5oWTFChjX34vHCCOszgVP37NLAxhuf4Jz0KwiPgXnexg==", "signatures": [{"sig": "MEQCIAods3fo9a1uGZFBN0mSXN2O/jOzJxzNM8f1JF/Z1g90AiAlGTAtahQJPnXdsTzxpi7do9+UGSDcmD14sIEkC/8wjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ7/CRA9TVsSAnZWagAAJ9MP/jjrxWjI9C2lWMfbk47F\npgg8h11Jm0N6MikrRk12z7dqghOjcqaCycbXfV4VlWClu6pqWATVH82yKH+Y\njDW+rEiu6QtsRr3Dpw4mWwVO6XcI+kf7xXx6wlvIDc1gmleK7QFMd1qfymqb\nqoB1QoQljvBdMIp82+frQLjp98JnyB1QgwJ8tSuD0CqxTJ4t5Up1VlGwKPAu\nhZNop3Eq4A1SsjDCiVhEJral2SJbQNOE0Hkp6HXJYDOCxyFpKlc54aCcpkoC\n3Mxjqba0LALsGp8h++BKPPLxC9NLyXyUD+XMKredTRG2CZIAKbsGHPQf8N4e\n08VhErcwUUSUtYmimgtNUxE6GEyWTdkE0k3arXm52JraERV0RHQ3fdkfgwcj\nDsgpPgsS8aZXzGxPl1Hw2sjJ3Vz0nuo2MXv0trlKS3WtAUi+4CBq/AbuVvum\nDoLmddqbBeStZ0x8zB+Zf6VCHIrPo0+aI18VX5zGqoYTxJTTQqy7eyGByyIa\nLQ6SiBD9LOU3n9/gyeuiSL29Rp3EGiG+0MlXe8FiynX2pccKoq+o2VFiNoHn\nEnWnVPhFauQJMRuhIls3eyjYz/fvdZhtPNZdK5L7WZcQFQqeutXc/9oPofCs\nmZMOUJJwVECWRkw7fcftyY65s9rHtPbdd14VVQKhMApDhYOjzbaVV/Mbgqid\np77g\r\n=y1fy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0-rc.1_1533845246515_0.9233217049104605", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-rc.2", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "c070fd6057ad85c43ba4e7819723e28e760824ff", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-iReV06m3hBc/RyD85vtHNNcYz4wqf0jPUFzUR+yAkzbhupYclHf6Eecse3GMWSNS3sVNMDA/+hCFcgMni41Dwg==", "signatures": [{"sig": "MEUCIHweTMlOp9BVHVLkPxHdQkdlxXanXIkTm0dzovzno1YPAiEAhtxq7SMssWpfGHqJCK28eIr9Kvr2aOoZswkTSwVJa88=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGanCRA9TVsSAnZWagAAAmIP/2qXUpD3TMX9RrkaYC30\nSuHW8JrVvmfR47o2c7bm4znkaYtAEUlUxodaShwbNCTO9NTIRtED3ld0dLhL\nkQ0pq+4rCEiByo2Ylm3vuw/MZbhhyFjLA7uETPb+v7A6CLGAZZ26uYKPYZgJ\n+vWHHBmibQm0x1Vyvul9eJnoqKWDIxBZ/Fytdfoz3gjj1toHU+qodsPifbXG\nRqOrdCj02fjreG6m9Ir75wRVqVH1TWNUQunb8+zBmmWFvko46p+EnN3MX8jh\n8ZYCuSprriW8Jak4TJ++IIhiy8PJuePjibQnGNEl8/JYz/El/rY/XYYJ1AUk\ngE1IpcWXl/0rJDfbcl+o9aBIWezkCJZ+ZldgsYLXiQaD/BoSOcLXTBXK6tjh\nzywTMGI4blAHfg4dg37mqyVI3r+1Y/gRL29vjO9NRrTfs9oarhznwoTvdfQy\nVBRtvgwR8U9m/7BOpg81ouXUuZamTRbOVV+/a7Uoi1qyRisHgjd6umn03nOP\n/egxXFcfyc2VuU7BPQNPhquSIrsjTJXco6hDzeDXn0J8coVVDxME/4ijobjA\nlUxCAlmdI8yHoGeUmrhwb6asgeY0SYJrm3/dE31drzQOXvnURRGVxpit5ZIK\nB3BuI5PNbVqt0EUYU6rSkNl2ISjOFwNUXijUakqz54JXhKkLnIpElAqhLWer\naNCG\r\n=k1yK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0-rc.2_1534879398570_0.5388724990604588", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-rc.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "729759abb046444b64df3b03a84d3e536bf68685", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-t72DUsTHGhmY76AqzCpj1jy0O/DM0VB4BYVIOsUv5ASByT1+UGS7IZKgw+iPKb9iihdw449USchilfmJhkdCrw==", "signatures": [{"sig": "MEUCIFN3YJvG00iuoyrrMT09LZjdAgybZF3B7+elMEAGlenuAiEAoX5wJGx7Lwd/SgGIHkzH2tBuWUYx4eRbR+V8FeYf9G4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2513, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgElXCRA9TVsSAnZWagAA8TMP/035ZfOCd1IZIUWOTfY3\nx/LA6bT7SKjgfQV/36pDLIAkmhP7NwJu61R5jZutJ6WbfxUrUrdlfIs0AJP2\n88j5jtYJMJHY3mNByvUX6g2tbGSLDTAOIdjezwo4yS6fPPW4Sa67y0KkR6jO\nVog/CWiOw2SCBgh+qkJbcDBPjcd+wpwwQfMHNwyu/pGFGSVKG1G7ArCEvNeH\nSNASyO+aN3kVP7F3jzc8ux0XeUv1MNpKDMcrykrhzcVFl2vTTFhoDiP583+A\nlwevbXr2Rr4g8oh3dUggbs5r/t3oze6taWZmNSMX6+nIQxCrLY1J6VmjAMth\nwyBLeS6aUVHAnqW/oZDfoPko7B1sJvi63RmOOBDzOAc3SvbJ7gQ75cFLcThc\nfM9OGFbKXCHh6c1xbmKWlD3/5PqimeYVeoZRR7fpKRT0xyFuAlMbQsK1wmrO\nF7eWAr4oKfVcCpqu6qE40wOOaYfst90q1/n93Iklcxve0Xfr7GpDhOL/KKPl\nBYIUrNrcwdHB9Tru4fD68pwmjqE5C2WEZAuW4m1btMBK+CNqLWOh2lFkRzUJ\nrlA5urkpFwHOcHYsaKTdhiIIoxZbb4qzZz7a0XoKukshOlx3UFcgo4qWgXZ9\nI97ZO7RFWF2Uji2mVKrk+fhHtMcr6sOvn/oSW4lqaAl7eOZ90G1zA3j13Vpy\nfUHG\r\n=d16k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "description": "Allow parsing of jsx", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0-rc.3_1535134039234_0.0042691903374108", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0-rc.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "b749301cd183d7538044e3cc104828c5577367fd", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-LMjLUqpu/RXG8qflBk0TulGXK69nYQD/zog7nIfb04zunusEbFpIg/zRVXh4cPR5HAyZCDJndhbonD1Ceo3Bew==", "signatures": [{"sig": "MEUCIQDsBgQ1F6/yc5GHecotrfqoJ3sYPmhzmbP14rzBj9hdqwIgcInmJeSikPhtatC/chFOy5l/+EvukL/CV1SuumQ/JFM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2515, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCo3CRA9TVsSAnZWagAApYoP/2z+5IiAOfNprzy1aSW2\nSdF/kIDK+d71Nt5ZsGUSK5myGFQA/3iF+i6OT4NQwTypdJ4HclXMQ0JhYmJC\n9JnkJW6cgxnHwkyQjCrm/0XhhNQBmZlB020zOXObTftNsWK+ZWyOUC7pQQgw\nVV/9AplUWXtELYtOPJinO/4NEGHympVwoghszm2Pkwkh6r6qB7rjZzh8FA+W\n9ZhtWih3Vixo+wlCf6hMUKKmX6g6iRXOEkwmdmyQKVg9fqsdYro4TyqqK3Dq\ni14LVR5+TzGoRnNkewWU6+N7wG/M2q6MLv9g6bpxeHaNd1EgxFz3rbg56w8M\n8aWnYnZVGQi/smBbG+TN52p6LO3erC3vNVfPcgRvtLCYfDoakGG6mM9JKMAO\nenD6Zzbo5BuBdSWSyk59d6YVlw4fOuAi2muZdtwo3Nf5G9Fb0Oi44FticX11\ncspp1HeKe0bq67Sy3OuXtL4v3gpb3XLUrTXKu4ryQremuUK7AgoJmA3AJhqS\nx1/q6taAsBYjK6OQ+8yaY4co42U8/PeHhV9/7w4XcofpCw9FhjKOPywgJ/l1\n5yhV292P22tdPfBKb7kbhdxLpw5qi4QJWu7WwZ4SsHCDsT2jFz01v1LpvUBe\nXTKquP0FixSOSv8nW0cp8QIXuKjJicfPPnyjH8OFFK/D2VdPRzTTnuOdu5kK\nq+AT\r\n=aecw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "description": "Allow parsing of jsx", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0-rc.4_1535388214214_0.347714998340628", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/plugin-syntax-jsx", "version": "7.0.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "dist": {"shasum": "034d5e2b4e14ccaea2e4c137af7e4afb39375ffd", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-PdmL2AoPsCLWxhIr3kG2+F9v4WH06Q3z+NoGVpQgnUNGcagXHq5sB3OXxkSahKq9TLdNMN/AJzFYSOo8UKDMHg==", "signatures": [{"sig": "MEYCIQDng5E6+Ua9PYJ8Vll3I9o39nti7R27wFHgRsVpc4jNTQIhAJBY0EO+z9dBV1QwiyWV9+H/+nG72A5VXepVOkv4cbI9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2500, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHBSCRA9TVsSAnZWagAA0JEP/1abBwOj8AKLX9KteBwv\nwGoqVidkV0Sueqt2c20vN3XA6eAsb3VMU0zByk5CUX7KVFQ5KDzb/04YPCXP\npw6mbJ4ogWsUgmmZ4haSy8CpnbVJYKqeSOamPS7mi4D7kzEGeuK5wQUR9qZ8\ncibjyRhznQxb39VzZ8kgfc1Oa44XIEiiYsIFkRfmb7LfuO2iRkxSinJClIJY\nvca+4kcbqcm7nag5Cecl2hu1Nxffuzm3fyUYkgZJ/XqOMd+X2G49rM1uHbBM\nXiI5qWa8xoaG+n1usbapGBUpedn4gi9FFXXcqdal8dVKK5NTwtcs30xvFb55\nPxAgLA8LiE4n/uCADZLUyAfsxs3KCCQG8U/XONagvQUjsq0TZ5CxLEcqo29R\nyLYeI1enAtjCaCSSOjaAbUSACFG/PwNb8LU/nxCE+AnNuHk9E3D8imcdxPnd\ndL/CTL2Z1q3mJ9adST6Y87YG+Tzd3RP8vC6UR5ETIvUZTDt1FHOMVch6XzWr\nTUJNLj90PRm4q4iwL+G4r6jMLR6FXzDvuWvBdCV7egXws+ZsC1S0AoN/vnRs\nw+EQ/sMMSUCyu0TVLLVH2kmXdlIBlhEYN3f7rCXeLYEKv5Pa2uNo/Tya+Vw0\nuQ826+sSjkFNCRGwggFYrsHyi2IUewhnRM/w+LrykZLynAkvamh4mg32/vAY\n2fKl\r\n=85Lz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "description": "Allow parsing of jsx", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/core": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.0.0_1535406162105_0.9676523062775739", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/plugin-syntax-jsx", "version": "7.2.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "0b85a3b4bc7cdf4cc4b8bf236335b907ca22e7c7", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-VyN4QANJkRW6lDBmENzRszvZf3/4AXaj9YR7GwrWeeN9tEBPuXbmDYVU9bYBN0D70zCWVwUy0HWq2553VCb6Hw==", "signatures": [{"sig": "MEUCIEwEcGn9Au8MHbbwv2lq7+DRx2kOh6wLRd28vzovoTbxAiEA0A5CAtu1SQ/aNKrp2i0KZpc42JbKrQYFAhIIXZAicpU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX1nCRA9TVsSAnZWagAA1YYQAJg5pipK3PJYCA6QwSL+\nYnC5Cst3BzRxpHCZwgLDmMQPj+rZAqPF4u5xhvrB7tTbNDR0LT6OMegEy1Aa\nOU3YU27Y/qGqQNlJ9uWL46M8z7lpnygdpukfeu/LcykD+XDFOLy/cN+5Ocfs\nRjBbnBkJlGaUwXVTUtY5w1BDlsvJ2CGIPZ+TERCpls3bp4WQsTtR4oBM+wK1\ncYSSNtel164lSMcKgmEvLEyBP598jdqkq+FoezUYAO1eSnVd+K7I4eIoT1rR\n4VWfchHdzrx9KEelsDYbK/cfRGFDoKPL4r1gfc0wxn6OEoiq/mqG2W8iRqd+\nz3vXPzlbIQi/GgDyHS9NGiWii6/2ohFTQqOm/zWqM+/6ctNWO4MH14npPQr6\nvsN9hkbmcm1YGmqa7DGuCMJRiNwgrrl1y5xxSJtMJMmB5wruN8h3MOp1R6Fp\nlVMRKogzpwqHNhxV8yz79jjDa43c6JvQwvzwedWH33rWLKerhoVyF1TLteaH\nAdS+iuK+A5r5vrUk4DZqJbjU1KWDdJEqomjDYsDG5qbshvNz919kWM3vnZDY\nH6sTB22DpBtU1+oRQ6Kmt5RgU/yY/TsaMEzwW1+EoS6Vs0A0euXb1KIyQPcU\nmqwwUt7/sR72V7D9Ub6H/Uyt1wKiaMqiAnv4pwdP++tRtLDMmfIUMDQSLfIY\njMd2\r\n=XIqJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "description": "Allow parsing of jsx", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.2.0_1543863654342_0.7565371567820187", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/plugin-syntax-jsx", "version": "7.7.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "dab2b56a36fb6c3c222a1fbc71f7bf97f327a9ec", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-wuy6fiMe9y7HeZBWXYCGt2RGxZOj0BImZ9EyXJVnVGBKO/Br592rbR3rtIQn0eQhAk9vqaKP5n8tVqEFBQMfLg==", "signatures": [{"sig": "MEYCIQDysjOLIoP1sbOqHedNm/nBe0gTz0IZXZFzEwS4hU1+aAIhAPss8U2ZHia9YZavmxru0QUuoWX6pdowVyJKtQEaglK6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/sCRA9TVsSAnZWagAAruoP/iDTdGWvpVPadvRA0lu/\nKdHxdnlXPLVunBp5gz/T1dWsyJZO5uUOoh4ozueKYLBceTruwKApdyxqJ2yO\nGQoNIgK10cXriiBfvQDULI9JtKo4/PVZrB1/ALP26p5GPBc01A2WnGPGsPQW\nAxZL/xxq5oAPW3GPpoxwvGN8Uen8Jr9FCmrUj9RM8bblK9NVTlOn0tsZSaUJ\nDFyt54dcN6zbnb/kIhAyB7xHiX31xSx5eEusCB08iVgM8TaGl6ZcseYbF1gD\nCk275i4Qbd1H8yQZ/LB5fFkdtHSuLIlZSHsmE/0m4b4Aa/JuGJvBLble/WI9\nSZ8ZnpGsAm52gt8f05n4/ndF+EDPio0DRiBRjfzIY789X/HGagAQe94nUExR\naALaZJwf67cMakYBcBlPATz7eOADSnMjkG1wqUnUUOwUymPhYWv4c45XKnd+\nSRqc0XrEegRaYBj8TPL7RkSS0+XOf6cYbq9x2js2mTFuym2Hf9fV4Zs/2wh+\nBHPk34J8+C8TRSA605mjJ5LyJEHCb474nxr52DkEl+VAjmkza5lC8CGUI3gQ\nMAQulHqTzvi2KWYXy79x4pJJ396Hjh6c9oUyvMaARXkYU8SvdwKF7nXZiWVv\nTYfZiyD8nksSXrvmPgQJcEDn8W+ie9ZFM6TC3rSj9DqIzTTGGrTL4ne+Y7Wy\niHCb\r\n=uB5b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.7.4_1574465516279_0.21516161001069944", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/plugin-syntax-jsx", "version": "7.8.0", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "657a0306e2c74de84e0dcf8b6cb024ed990224fc", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-zLDUckAuKeOtxJhfNE0TlR7iEApb2u7EYRlh5cxKzq6A5VzUbYEdyJGJlug41jDbjRbHTtsLKZUnUcy/8V3xZw==", "signatures": [{"sig": "MEUCIQDVmFtCpsncZTLyd+DGtx+qDSGpqFRcc7XM65J1TPXeDQIgAuoR+NTb/Ex9DAyC8TNi6gSfrZVUNcY5KvCocGiN9OY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2558, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVRCRA9TVsSAnZWagAAICcP/jRWAHnWt43tPOjxCW47\n46byOjDWMI5Re45PouF7PJRhBF1bwWArbEX4NY9f4/DPuL+gCt1m6Vg6bS7b\nhVBbesWNIB74Pd3/YmpUfOxBf0kUi0V65jT3/5SSNRhyZMAhaMLmgTYs3z+D\nBSt7D5GB2oVyaGXe/CtDUTdB208dE6BvVOxpdtrmfoQrZKDP+6OuutptbjIo\ngKzltw79XCmnlA3BnmGfE8ZmMNRfCEhsP+qJ4Sta4Y8pFsYk1U31cgKti2l8\nU5qjWQOQTSTuVQDElkPgBUBeelVFS3fwwlDpB/A0DFXUQgJZqiKtqishQFRf\nB79gn5NlKJa1b48jen0iFAZbl3JwR1sNWLEouzxMWYy0hXMYC1jbM1b7dtN9\nQ/4+M+d6SqqhN67BvulwFun2Anqi42bCtpGZ8fmCloZ6H2vXQ4ewVhyRS8O9\n+/MAcL2vDH6s50DUdRTDdIy8TUAujzCUcb5WFWv/8QMXP3tdQbJXnUc6NkxI\nd34rhlzS/V0pa8R93+dUSpJCHFG8X6y7drUIQv5ivpqyyNv9GnklHt96n8Ut\n9fiFiRTQ2oyRLQKcbGpWIb5tD4cGZzYS78WNkdgBg1PIz4XdXLRXQ9G9db+Y\nF57madbkV1n3gQKVuKyIfU9a3aMhjARMpYrobHNaj0Oeu5AHqBZsQDyeL+uq\nT33S\r\n=RCxm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.8.0_1578788177438_0.25217235305021757", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/plugin-syntax-jsx", "version": "7.8.3", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "dist": {"shasum": "521b06c83c40480f1e58b4fd33b92eceb1d6ea94", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-WxdW9xyLgBdefoo0Ynn3MRSkhe5tFVxxKNVdnZSh318WrG2e2jH+E9wd/++JsqcLJZPfz87njQJ8j2Upjm0M0A==", "signatures": [{"sig": "MEUCIQDq/NwolDJWG/6ZOt0OG9YHQ9BmXWE8InQhfS3WHC9QTAIgLmA4XoE1AyBz6SKW2ur3Tl4IHgRPE9SWcU9tM46z+EE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOP/CRA9TVsSAnZWagAAlQwP/jmHnd+BpAmKbmHWziSV\nOtmrzMnX0DlB5gmzlDw0RRKVdx+WphYPyzTvU6K5OgLiNMhlCBvoS1+owtxw\n9sKf/mx3z/g77IBa0mhIyYfUvn82MnEok3iq/FpyTeRhx3YuLuQe6LsxStTv\nxTyMQn/ApFG4k15rf3x4vWOjKySCctTR8VxsbfbmWvykWwLCuLfuJ1DZ38S9\n2efTb5Ia2AsOp8bkSexmxD27p7vgX7wuv29vhHZcftfOMxG5gfxfryPmq52O\na9sB5EMrP9TzQr5vpNAle5jgbX/B+nlU7gOqUNuz0+ZDxh5nDXOGRacAeOV0\nErfEi64t5JRwFPXJDTnUbFd+/Sprsi/APGGvGqg/BT2zHvVSUwdn9L8f1atA\nJuRT45f7qGC62GYoQLOYpDPvUl2R95B0CYNS+b0Rq6xWrIssV8Sx6G40rNkB\nsITT7O3KA73grNEyzaSBAUbjdOOqJohQZbt1jG/Dsgz3BW8Nuz4pw+YbMHYv\n4dlzBNIFoF2qGeg9WQi8+I1juvbjc/e+DOo4iDgyrqdotL9f/FOS4aC5WGbC\nn8PcDfIaT/W6P+TJBsMA5W8lyXFgnq6njLu8gzqCgRNOH3jw/3CGYqUsQ3fr\nKBMpmAwNkoT7sZsUoMxLnF4uaiQMpGkTuQN0A8VtFAD6w3Rdj5nTl7xz6d1O\nZIcr\r\n=IjOP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-jsx", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.8.3_1578951679357_0.07980701298842785", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/plugin-syntax-jsx", "version": "7.10.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "0ae371134a42b91d5418feb3c8c8d43e1565d2da", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-+OxyOArpVFXQeXKLO9o+r2I4dIoVoy6+Uu0vKELrlweDM3QJADZj+Z+5ERansZqIZBcLj42vHnDI8Rz9BnRIuQ==", "signatures": [{"sig": "MEUCIA/eW5RrBMNlWMVdhuSfPr7JbzuyOYqfSvlIi5kogmBAAiEAlvltVmD6UbSiRUvVqN3JqzFGANuKKDDPOFVcfhSWyg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2587, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSTCRA9TVsSAnZWagAAXSoP+wQWfZmhO96rWCvhydZ+\nd0oV6tglwaiEqbJTG/FdFiYsdLTMpSvI1gfa/0jwXq3fxARYHmHy5OAzLW8U\nD3x0x0sT22Y90W4LP3dDTRw1nqers5FyUfIIy35t3hIOStBdEbnqZgrzjm7n\nH9SBhFqJjvWojZ9284zN7t+WUuWxuVP5535Vvm06ziOBwkDdncSGfOLiP5j+\nFLUC0Ws6vBTXZhw5rzPiUCzM9ZP97cLWZrOBylKg55fPbDyHskE7WZGR83v7\nckx24hMpni1Mvx7MEmBlLOyTrBE1h+BTh/c3lUBt6nmiMrUQjWO6sBUdS4Lj\ncZXdSzw5mfsgTuyP3c24uRHRpJCh4dV4YzPqeNdRzq09JVzVWO2mpTGiMI9V\nGU+MEh1P4hx70DwHpOGAV26ku2uOxs43hWImC0Uk18wNvwPen/oHA+M2LEkZ\nGuYY2IvGnFzEBcUW10Zk8xz/N96s5uLameYkQPW3CQrTVcJSMDlgk+5rhK4h\nqnl+zb1+0nF/FeJFleqE9GyMyZAuBnhm8ufHTtd1v0V/XLlpz7wPKXjFd1HK\nxRMQOCL+HxzAGZDqz7rYoKD6FE8JJgZn6lDv1Lvy0ohtqSoKoPchbdITyDL+\nWYIpxon0h4SwrzixVCB/6dUTBxkO462ILncdZaoYkfjpa5MYe4obtUwzHoVK\n4KnU\r\n=VC8t\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"@babel/helper-plugin-utils": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.10.1_1590617228513_0.6690040035718903", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/plugin-syntax-jsx", "version": "7.10.4", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "39abaae3cbf710c4373d8429484e6ba21340166c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-KCg9mio9jwiARCB7WAcQ7Y1q+qicILjoK8LP/VkPkEKaf5dkaZZK1EcTe91a3JJlZ3qy6L5s9X52boEYi8DM9g==", "signatures": [{"sig": "MEUCIHd70i8Vz6I+Xh3JPY9XFkcBQFM9g+ARGK5Qlycp+DXlAiEAoG61wCzTp/atR/i0EEQX67rWdGnjfNosRm9t8gYgEKw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2587, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zoSCRA9TVsSAnZWagAAjUoP/R1FNK/wFK/Yh0zeTerI\nbMvqDnBmjpcSfymb0PDR4areDiKOnRWLkrt326JNov83lrpZu31VUZNww0JC\nF+7fsPJ1gK09PJkMyFxqqqsYQsbZ7UUbRC9EcpC836/5R+G81gI4T5doEpPG\ngy62kCXduwnzno7O9CmlqzYGNZY6CV8qai9/e09dWEPP+U8JXMsrEzS5mEvz\n9YFnW+WgzKgykHrY9XrX8/PIpcnQ1zYsL11HEEFZXyLPLi+2v4zdqYDaYT6q\nfA55hGVHhTTS+aWuvwltqEWwtVwIZ4YxizkDeVxvyESrbTLkFIjeVGeC2j0C\nEQ/GOFStV20dbMzEF7qkJAnK8INV4clEJGR6GRT4bQ2XWu3FjbURAsvzJtUd\nyaqJvxYpS/KMjEjZA3jJcijXqi1Pkq4PLav1dTiY6Pg9Moa331lmW2+FGntj\nuqO5ZsYRbLi2aJ6KP15swFlEHeuy09sXA45kCHducbdMQ1jRuWQbwhbu80NB\nLAXN+xGLNuTHuUgUUtLejYmE40PyA7M8yD3m2niBxU9Kqd9KmyPGyu8ObAh0\ncHLXg30KMWeWfOd5V/6SqbNXiayDpiTD35e010NMYTqJtx2AZW2UL0EojZWS\nUXMIS/vjkekJVNiAM03VJP9ZXUAmnv0NaosYfXQtavTviyIBU6ISn7MrOTHa\ncfeP\r\n=r+Ky\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Allow parsing of jsx", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.10.4_1593522706605_0.36105834697799066", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/plugin-syntax-jsx", "version": "7.12.1", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "9d9d357cc818aa7ae7935917c1257f67677a0926", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-1yRi7yAtB0ETgxdY9ti/p2TivUxJkTdhu/ZbF9MshVGqOx1TdB3b7xCXs49Fupgg50N45KcAsRP/ZqWjs9SRjg==", "signatures": [{"sig": "MEUCIDS5mXlN9Ly6mt8jnkyM6B/YoPjIMiF4l3H5351C0p5vAiEAgK8R5Sbswb4NPgCIofSz8rzyvnEAVp53fD1IIX/4tsw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2529, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM+vCRA9TVsSAnZWagAAkh8P/jokNL1mKU7D8WU9l55Y\nWtFqdt0pdVFQP536TIWWhHDMH+eKARMaXvwC22lh/F7pXrNCK3mC9ktelWjc\n6siK+FDFtuMRNNZXp4EuI+/cFtuc6iuSvyTpzQ6hBL5sC9+1qARfhG2rJmzK\nRg42J2WHs/jmTJ6h2ZkD6oxbkkrL9fTjJCAS2XkOkcwf1oZ1vKRzICN1S636\n+sN/vwFkSd0j2p9IfyCs2R3gDVpPqthPQF4XMfitPG0KAbEpdMjPKwXUKlfz\nOhDubhjfSjfmC60gb9nnnPCJWQ4wxLb8fOWcp2+yEIHlJAp1gKtPh2cAQVOS\n7B1cy3mBCeKs30394lrrrH9WWFPh/wHNVoJZSiditmLQXBbKU67/pdzoGoFJ\n7d61CipoN/Ye3lkh4K80eSeybT2Y3l+6ljFiRzY8e/2sMuWMZ8vYbEiXXbfu\n+6XzRrgKtLjVst53ljOUu2iCqUt3D6fDDghHSDGKWrRpA15tAYfdmQP4d8DG\nW1+QDxi7H86doI7m3ese6vATt7J00pf5LNL4kCDMLQ0MUQ87GEClbuqB1BHF\nWAYIeFS8GOax8dLyctnxZecodIx+d9jShledMXJcrWjJpYWuPlLo5MKBMvY4\n7ItxDXP9KiDc8LoI1cRHxy6Ukbd+VQfx4A1XOYG3dOmeNmazvyI9GyXyAb1k\ncRlx\r\n=ZtAk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.12.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.12.1_1602801583138_0.041791233816114826", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/plugin-syntax-jsx", "version": "7.12.13", "keywords": ["babel-plugin"], "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "044fb81ebad6698fe62c478875575bcbb9b70f15", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-d4HM23Q1K7oq/SLNmG6mRt85l2csmQ0cHRaxRXjKW0YFdEXqlZ5kzFQKH5Uc3rDJECgu+yCRgPkG04Mm98R/1g==", "signatures": [{"sig": "MEQCICvUkuKrYDCea4/Uxnw/CPwKLoc/qixLXfnSog2+9ppXAiBZJHiSjqxzZsBYt76Z0WKPlaxAKBMSxErTFSor0dZJSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2593, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfgPCRA9TVsSAnZWagAAj+EP/Rc4QfrKHG0bOOE57rkV\ntkP+MlWCo+nZPbOBH69XhGoDFnZzKMMa5zrMOmPmt3gZIWd5a40E37SNu7ii\nplxfxMePgLbcOpkk/uu7rx8c1hrKtXxUBtH58fdxNDEXGYvkQnioTG1KI5Vk\n0PRkCVecNOzAn6TrH/QBuSQ88qiMiYh9h3DFMsdaNjm56x6kCXwwHXpxM+gr\nRy9GABQgX7JxcIw0GsEPCpdxzR/byagEtn1PNRqKedQZfUie6NqJbjC0C26r\nySXMjgIYwdtoKxPLuqEkSiZkhszx55EBDti6gW7tIYJJ/dpdzXxR0/nXvG3S\nVSszWSO2fooGFSN5G3It3q4hfKdHULmZCihEKE+vLaxfvqOJWm40V3Dx/uGU\nfFxbfaE3SPkDJR2ONMi87FWss9Vk6sqoz3cpHINFSV4l9fXLrhMfTt3Tf65y\nNNu3wrVHky/20qaRf4sGrzdhUrQ9PbhUIVsI8A8I/zVEVNVVke3R/NDVwO+5\nb2BEEGofXzVdE3kn3b22V5WGY3x3ygrAapmvgXPFkooiGZ3QUaq+L7SIRHc4\njHfnaEbP9Jy239v6TdWlD/x+WgfuYFiim213FOc4gzVovXFD3RuFDO27sJV3\nWoe7JyB9EqMHX567iN1KyhLF3gNDUmsjjOcigOjpoFyfR7CzFq7TroL9DIrQ\nXqVT\r\n=sBcE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.12.13_1612314639157_0.002070076990204228", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/plugin-syntax-jsx", "version": "7.14.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "000e2e25d8673cce49300517a3eda44c263e4201", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-ohuFIsOMXJnbOMRfX7/w7LocdR6R7whhuRD4ax8IipLcLPlZGJKkBxgHp++U4N/vKyU16/YDQr2f5seajD3jIw==", "signatures": [{"sig": "MEQCIAtEAVv6YdiTfjXTUmWBYfqWkcMZeuDljL4mhmRjqnsIAiA7gL6s17iDhCkgTbRwu1W/DrCayaPrJHRBuJyAAN3MtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2689, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUq1CRA9TVsSAnZWagAAVOEP+wZY/rGYowDXT7ixwSSR\nzQ5Xe8hbk5jCqTUze0nshCEDWhWgWjUR9hNQn9gPIe68EicEK2TOf5bMivC7\noOxE0iHKAbCxtcaP0WvP+oprRQTBBQEUvNsgTkyOIplLC3JXkkjyGWxckIpY\nFtQJZzzolroMaX93RYssELhzRyvi3Y5RMv5hrm5Oy57duBoXi/ZdgWCtRCGD\n+EGQe19kzkNqhSkus5EItTDl6UpwMs2tlpLqxjzkTKQo01o42rWPnIwj5dUD\n5fpMxhtlln7yfNYAM/zBpgAtFA1TT95Xy+fqljdo+/6N55+r340mMg7VTxPw\nT3duZlXlFK/jVRgLCVqtJl5HQtbYNIVq1c2mUnDVf8moTtJ5Wg5mOF6svt3g\nX9d9CHPJVDunWLceE/0XrJcMRypoK6dnyxIW6FPwlhnWYaGScZPUwmq0kDoi\n2yTfHmYXs8epkdDvxzXvq1tQ6q8stft1MNNP1oDUA1ZSXsQAaFAnGHvlv30Z\n/29P/C8G4LfmZjDl4CFGDkOPgMyzk9jVFPGC3Bg44Yb6kZeBY/NUwqVdMZTc\nOMrJ5ApI88paBwRa0AQ2/5qMFl1DVHRRlQ89GPLEId0Giipy6eT+FcBcKLlT\nkvE9M/bWV9S5o0XM5X2mhYdLObtX/DbCConY2rUT9izhgOad6CRVCJouYfyd\nDTZf\r\n=KeDd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.14.5_1623280309408_0.9112216311273635", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/plugin-syntax-jsx", "version": "7.16.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "f9624394317365a9a88c82358d3f8471154698f1", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-8zv2+xiPHwly31RK4RmnEYY5zziuF3O7W2kIDW+07ewWDh6Oi0dRq8kwvulRkFgt6DB97RlKs5c1y068iPlCUg==", "signatures": [{"sig": "MEUCIHSwepjr8tZZ7LdplzQCQD1htS5fnaXSNQQdg5vnaKgBAiEAm0+s7XuCBkjzE6M83BO4Wm1ykPeL7DpgabfoNUGEWxc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2690}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.16.0_1635551247314_0.5752610572626276", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/plugin-syntax-jsx", "version": "7.16.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "bf255d252f78bc8b77a17cadc37d1aa5b8ed4394", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-42OGssv9NPk4QHKVgIHlzeLgPOW5rGgfV5jzG90AhcXXIv6hu/eqj63w4VgvRxdvZY3AlYeDgPiSJ3BqAd1Y6Q==", "signatures": [{"sig": "MEUCIQC4ryK5w0p02ZSL0rcC4rqZdRAA1wnknjnR/m4C3hJoHgIgLrOGvFatXqwudvKojuhYVeyopAj55/nOCWURtqXOHj0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2690, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8jyCRA9TVsSAnZWagAAOf8P/RKm6f2BEGitIZLIFp1q\n1GwCvP82TxwTfqfT9hNOwZncDRZ046wv8ne/1JSHMVhvzdpxDtnQgvEozsv/\nahMZnnZTwip5OiS+0tjMeKHHrQR4vduBFHIjkI68cEahrsRd90sViPCt8F8n\nSOpqbRVLW1QPecuCz0KIkDwxMfU+lNM7lH1ZCn/ENPkj8S/dcB6Fiasq1DDW\nqVSC4I40Wdo/mIuJrxaLCrHqdczYSlru5/ASt8x3y4g7kBNuvNNATHgm2KkE\nhB0tsq5kh8rgkvAopY2rZ0guv3BwRzjCNvYACA0RETByNu9cLqYwmSWt8dlU\nwAwuS2P4i9SRv2df0LtNjfUDpnVY9tfAQh7U4Et5Ff68C+E7fbTqb9ay4RQc\nbF2pYjI1BoH7E+9r21I0I8wwtkFtJi1ezFnv7fOYqBlJ9afcbK/B7fOBCYAt\nD9OKcWE8lvAd24TaTLsUdAMiGR02LuZm0hwbA8rRWlVBBQjXpDYAWyw1D5+h\ng+mvdjdmQQ+YTakGmNC4j3ODlZl9aGxMJxqrWYz2Dnrb0u6xlKNd/7bb2ZXZ\nwaEbLEgzLhGh9/srrynSJHGTxucVXmVKv+G4EP/W4EaA7Bxo1LNNzqN5N82v\npdN2ShFT8Ck8aqxvPsjzlRmUKsePIQMRsnZwD1/a01xHXjZ532w22aqm0A8M\n8Viy\r\n=DUeO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.16.5_1639434482229_0.12258060976647189", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/plugin-syntax-jsx", "version": "7.16.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "50b6571d13f764266a113d77c82b4a6508bbe665", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-Esxmk7YjA8QysKeT3VhTXvF6y77f/a91SIs4pWb4H2eWGQkCKFgQaG6hdoEVZtGsrAcb2K5BW66XsOErD4WU3Q==", "signatures": [{"sig": "MEYCIQCpqgN14L/zFVxzMXnJvYRu5O56aJlnCLmyDz8Xzrm/rQIhAKMRAYwy/SGOHXIjV8KGiKqC+B4gRapd82QgTNcGfSH7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2690, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0aCRA9TVsSAnZWagAA5REP/3ZK4i6L13GKvssuTK+n\nVaUfy9Wn+zhkqyJG2ahuEUhwshsDBpIzvSwff4IrfICOMrofVYqraFt1a3dA\nONpuWltfbwKS/+Mdx7ghDQxbsBrPpGQPsggg4zmvpMJ54P/fCQeZaSkk63IT\nTg9+YA2vVDKvZIjgon9uMzsQcaWySOqMfM2N7saaxX+pwCMgFKY5gHMdx0eK\nNUhjskKaHR1rDebMFsrOZjqBGhk9w50jS/qsDOXzUtiMV4RkDUJ+7GiNHVR5\nNEtQ0ElYFVd5mqcKlgx9bhQ1m5owhvp28OejZby87NI72MuXQ3GqUVEM2LZR\nVXJ0mPOSxoJe07OvZvUcWUHPOoTDEU4tUOq5JQZxo1ukomh3JFs4ZCIqMLXP\nrauAzAkkWGdobeiwddeAWo6ym1hDi71pwea2GOEVH7iQXuowyTy0qUvrCDPN\nPwoBMRCBeQ+Gw4ZqlOyH0sQggUoFSbInR07tcglQ0g8PlmGLIi5Z4CObXfob\nsHBsjMKUu81iaHmCG7xo5BVeiRr+hXBuQFNwKmRLTBjkhr+b/3rpcauqrk04\n5H5WFUjQhlaTZPGcxOQBxG7iskm0SiRQBojTU4R3HFd1wJ+BNVcSZvn716C/\nSD5NWVNLQzURI8ABxuNijiOhXBpVXAwLIm7jwP0Fmz1F84BxWpEunzvDs1Zh\nJZ24\r\n=+kaq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.16.7_1640910105889_0.03618318296377465", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/plugin-syntax-jsx", "version": "7.17.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "834035b45061983a491f60096f61a2e7c5674a47", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.17.12.tgz", "fileCount": 4, "integrity": "sha512-spyY3E3AURfxh/RHtjx5j6hs8am5NbUBGfcZ2vB3uShSpZdQyXSf5rR5Mk76vbtlAZOelyVQ71Fg0x9SG4fsog==", "signatures": [{"sig": "MEQCIA2arjqOgO3QBXHeFPTXQ0768dmO1+bYwqw9b50D8/DGAiAgEJfOpQY9fn2TNuTKaeKwqj8Bfu7nTkwt8h93bZwMYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2724, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqbZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7NRAAoRC3zNvP70gENRIUSqYMbcvC8S0o0z3TXHrBfpFaafpSSleT\r\nxkK/Fs+i7f8WdB3bE0ZJ2Fjw0khkvzQ2IwlaDWovQV+AvmgqPKmuuEF40/bF\r\nLd5VaCrNIYsCZtpCo/R1nri7CCtEm4s+OoD1UrKRn3S+tPrk+vnvBp7U6+xj\r\ndpp54bp1YLGC0npDUyDJ8twBKAU6/rCZikG9GKSK/ubFy54h6ThmaDEBrar6\r\nWZ0/zpl2zSp8w5D9uc6XJ7eVG3rzZk2D+3s+rdOvlyc2fgxlxZqGfNQYWWHi\r\nIEA4SQOTADk/h9iD64d131h7r3sXiXqcSLyahAlbgnH+iCZ8UVzvMwcKtNxT\r\ng853OPixte8bwd3V9cCMmEeC1O2mnJcwEIgU73UZ28/glw/Aj/YhmVBKTY6O\r\nUfEcDmLy0YvAAtIvBcyhaMdThrVMttWBI7PKNPektPG+cGgYO3VCh2gX1Aeh\r\n0i+CdxhF+y5pollVesTjZ61CR0p135qNXhvAx/HbH89SPH8LLyQPj6muIBN8\r\nj0n8Az+umBVPAZrwcO5Ajr7OGkcN6TcqZ33OIn11VBNB7x+PKPbiPXLgEv48\r\nDM9KDvU0JzQVmp3mdq9MF7vg65DujDefFnWgBl+hI69cgwFEM9SDhV2WNiFx\r\naVlzLlIaoTCr3+4HXHYqDV4gTRkwv3G82m4=\r\n=k/JS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.17.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.17.12"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.17.12_1652729561060_0.7150805560895366", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/plugin-syntax-jsx", "version": "7.18.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "a8feef63b010150abd97f1649ec296e849943ca0", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-6mmljtAedFGTWu2p/8WIORGwy+61PLgOMPOdazc7YoJ9ZCWUyFy3A6CpPkRKLKD1ToAesxX8KGEViAiLo9N+7Q==", "signatures": [{"sig": "MEUCIGoihjDLNUY9TgkTcvv1g0GZxDojTXJ+hjAmK/Jqd4LBAiEArPaKiiiVqpPri1+sPPZT9uuZSImoRHcpkA2YTOs0IHc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2743, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqc6BAAgwAtc+Ke5+yeCWMB2eJvmBOJjCoRt8BBs3RUVa5HFUj6xXuE\r\nAUOYF1+GxlJqHs9yEHY8impGoy8g+Su5Z/OxVitGCY7Ba8zUi+FdFDAzB8Oe\r\n+1GrX4Ajk6IfukFJpC659ud8FsUoNmfbkYMpoCVPdgNES8GDm0rB+BRUOAsa\r\nCjO+WElFdP3cphRgafICnWDdJS1c8NwVmsMY1ocZFw+YUjLWpaYoXLljUf1D\r\nHMcExalDFs9wxAbQKwZ0miswlicEnj0JCgHa2WdyI66JzLtgGq+97qhRjxve\r\nSs+yRAfwJ4FElfqgCZ6NXLQ2af/517IWrAFQreV8cJ1DueJTz3dNaWy/YVqE\r\naV244lpuPyTGvu9RJbVSq6x5lgkVYCNA2OLtqit0u9aluAECcUHP5WACmBYD\r\ne76f5bvZ2kOuVy7GRDoPd2GrS1N2kOOL7i12mcyjU2ZeBjhHLVNh1eXsSucc\r\nThqJKA+EsGp4BWnYhc0RSkSsQRfq9dVxFDqzl7tjTGLWkaCxU52fHpMiuIJy\r\nQZaVGXSX9AyrWsEdwh284PjUiYcc1UdCu5GAqiUnk2baeBUtSWyoaxChw3uU\r\nq6TxABCl9JD/QHhl5BG34g7djJlKCRAgMXO5RdfQVdt/MosgkzWiZR2KTQHX\r\nZXpdgWNFG9/3YdMVJYxTuPfZ6z7+4hBXfu8=\r\n=Iwtn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.18.6_1656359395851_0.2577129591703704", "host": "s3://npm-registry-packages"}}, "7.21.4": {"name": "@babel/plugin-syntax-jsx", "version": "7.21.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.21.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "f264ed7bf40ffc9ec239edabc17a50c4f5b6fea2", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.21.4.tgz", "fileCount": 5, "integrity": "sha512-5hewiLct5OKyh6PLKEYaFclcqtIgCb6bmELouxjF6up5q3Sov7rOayW4RwhbaBL0dit8rA80GNfY+UuDp2mBbQ==", "signatures": [{"sig": "MEUCIH7UEHRIR8ePrSoJ6z9f6Ea3Z3xg24Pz27wjQZyDCf3HAiEA3czzWQZLeqQHY8mAeZ8ngSsUrcuqsoE6V8lablvBNJw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJqGAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrR/g/+J3jpSVrdBg3xBJL02jH2ApY33jfXOR9i8xfkgSz5SlPsFS9P\r\ng/Rtw1/X3ZvG76Qb2Fpgx0mtzpneir52jqLoLew5og/x3vzip4MAlB5yDvEY\r\n0WBgIzceCgteRzBL14JRzspOLQn5DaBzbqWfrOVoiCXwDUmggSUZFpyjUv62\r\nVdaRBiNb9B0F2cqKJ3iWPZI0+uopcD7TMvwp4IrawkuQI3o/3rFhDUVmpc77\r\n4E0ipdNgvTb/IaJVOC1KoAZGmwYk9NfeL9TWW5Y3DdCp5lxYYNP5E61J/wVB\r\nQItVifItuu7BixoIIO4REFzAgaEjIC4vljJYlYxejmNd5N1JWJV4LNHCc9jz\r\noUkr/Y58p+9VzldK2HOZXY9Fj2U5zylJN03t4Kd+ml/SR0K7/Amsfg+TsQDn\r\nc9mxUki+xJrz/lpfdU79SB+0Kk85X4EVm2ad2Eo7hjYLfPF4YK13Lq/jm0nx\r\ncyqLrrnaZaMu2DK4/8chJW1FwcOfhoQYHnytUjEBhTOsZiIExpRaDHCjG52l\r\n90rSZSiz0+vgX6FXClNJBxDvHSRom5u2DfDdHnsIMgtNhn81b24EYzwyR87D\r\n2nyqO1ItuVA3FPDWegIZlGm9eOQ0M7iNTjGa91Mw77kvOWAhB/iVuWxpX3CB\r\nvz9ljKaKDcmBYj7WCB9CkZYUPphAd88c2Dg=\r\n=QXQV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.21.4_1680253312189_0.36028992369400537", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/plugin-syntax-jsx", "version": "7.21.4-esm", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "e6fb3df57622384516b7d12983dfb574a46a803a", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-t6WcNhidszYOYMfvaHMklenyu+ePyWk2bwNtaleme6aOsuJ1ZSOEZJuDV+70lA5AqmTeGD4MO8UwyGKtUrtLUA==", "signatures": [{"sig": "MEQCICIgTqtSwt766p8vzeyXVIr5C7ai2MkeYDw3vVWGtNdiAiBxqJXOGnZ50drov97y4E1QvOqYFrBJKGzWXvglWuPAVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4176, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp8QQ/6AxoTPm06gwl3ymhFeV4EPzj85sMHTaXM/2J0uW5NH2pLZB0/\r\n0X4ZRMimh+1tEr4xgmOk9D1cZXOXkWH5DVnCwi6U7aDVNusmPmgGA1DUeiBf\r\nAZSOPLzc+p5RbiREYoVpfJJtFIlMt2wMpoMjMZgb3A5bQ9mocyjPnaOPvzc3\r\nnHefxqg1cWq1Zzm/AJ2VjKJULQMk62Af2k2b1u8XDAmKAsBYc8GeN3CfMusX\r\nPRO40hRq1t6JREmq+0QqqX/mJT6gDgxUcuO7hGLR8itqmLFy63fGdBR+vWP2\r\n024/31tHr/akaGe4GqXCWGLzFR8gxPaJ2sGhrusrZ0Zv4Z9y/avD9/GllyGt\r\nG0XevKy4yOPcs/vRdFMJB/CsC4N6O+iLl14D9yFig+P8TBVKWsdsZnc2F8wC\r\nIEYmqpTg3QbJALRhOUz4VvsQs+a1QcpYDxQWyqdIzUONn3Ugm4QBt0BYfWYg\r\n5HjydE5oz8lJf0p7YhqR6/sIDI/67axuVOERzzWh2O34LBBTaagMr9398a14\r\n4zyUiU0sVh+tXXHp1Myxo067bWYkeSMWJ+d9awbvyL+BsSPDPXmUzFotov3Z\r\nDoXOKgndIuoY+atA0JncQR/uBjKAimLLB8ULXfNzC6CjNnkfPXZUKw6/zr65\r\nu7PGMRBPvk3Kt025Y3QuHqVXMmLLL4KEceI=\r\n=kkM5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.21.4-esm_1680617357304_0.8127933533086353", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/plugin-syntax-jsx", "version": "7.21.4-esm.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "15fb122dea882ac7750b588ef4ffc54bc94208a9", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-xlCIbuEigTRRJnLLbKh8sVN5ePRfNqlOXUxh2ZhAAInrocUJql/ikE1asZwnKj3EfEA6X7JdFqw3eWI0IKnFeQ==", "signatures": [{"sig": "MEUCIH5iqm8gDWBjzaj/mDJ4wuotfv+6H9C2//n3SjX1ROEEAiEAtpER2G1EF0glZKcUcvG/pSC3bxL/AEJbYubqv/hVmVU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3895, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAvBAAlCjm6yRYLtzMq4UbTyE3phLpCUqYxm0wJd4c+yRwF7fTOiYJ\r\nII1XZ3aQRf4M+PGu4yIpiwpG+EFrMia4N8ezd3zyMbVfTL8lNLfs8hUDfLAv\r\n1FwUdZOlVbYJTlN6LT8VxUcqOMIlcojXuJJtKQwqBEPJBVeiamy8gw/u/qyH\r\nMuLQj8wwprfvDMUhcQKDYq7JAf9N1g96TJ3HwKmZ7uAjMlkq+SMEKgbagf4T\r\ntZwRtnAYWfh3R+tbYQmU4shKMDV3zHTVMd+SDAbzXrOZT+JFVNI97jkQ2FaC\r\nqXngqvJtJWD69b5xjkzTzT1i+42Iocq6NnBzqIAoH8Y4nAnYwpYuo8qhEvRP\r\nSxcf8RSjYjizJzo8GpmkDpnbUJJ6udZ9GzGF1QFMWuED2QnmzfTrSQtHJKcK\r\nVJan8T8EGiBOOI3zXuSLA5bhfTL6Z7sp1qwpXZ0NpYxaMMh5iplp0xYLFQCR\r\nZ+axmyAurnE5IfNEM3/BiOVbKbgjEn3njO9RRSxLT463DnYW0sobrxwsF0sz\r\nZYVlU0QfVF8ZwSvWQQTfqul46udWfIH/kstXaowwdvL3f3Ny/PoCkkv8qb+z\r\nTEDpio764oc9N5Ebh2y6RDQ3z74hkilLLUtMgiluiC5wiElRV23qw+IIgiXx\r\nOGZWf7iH/pBrvgK/Ch/Ejyqu0sv7QKzhNBA=\r\n=AOWn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.21.4-esm.1_1680618067502_0.12321038464655665", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/plugin-syntax-jsx", "version": "7.21.4-esm.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "d2ca857b37e02a4c035cea69ca931d2c20947478", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-DuzrT8TZXPEEqOt/EjBd/J4chdCJCXJctKYsO8S3KOuICfrMg9KfKaG+q3Q15r6JUJ1cq7HiogpqlHiMFaiJEw==", "signatures": [{"sig": "MEYCIQC5QKlty95Tld+huc0ConCGO40X/Hapxb1BY/kCULiUkwIhAPlw6eF4HAqwL0rUGqUSrl1FLYzJcxsaxg/fArM7zEJU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDaKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4wBAAl7ZIgxbgB5IWKes7U00HHzpTWh2LDx85GQPuFPbIXrwBXiLU\r\nke6+uVaGXViUy8gfseGVh4DDPnTwZM+pIs0dTI7Y3NlLGOpWDFOYWwXmzhMS\r\nLjU+oqhsloCNlXAqIXfVgXDz0xxJeGCRf+SOsAOFIO7K8E++zwFkuHRCWKOS\r\niucVP6etztPO2CmubU7sbqeuT1bge7Ozrx7xb22bVqpaNLpsDP2sSVWIYPSl\r\n0+1WSmA+uk6iS3eJZZlHclPbl2Gu2VMfyVrkiBx5k514M5vlMgre24U+WKoB\r\n8dL0jS9LDlNhMDF0tQX2nknjHwL8DT3q8y3jnmEKdwEgW1xsyt0tXWTqqiHd\r\nWjB3O3lbeFOCyT+wkr58x9xsRmyrQNWBirVw5OSXWdt+iud3AQsH4ydjLUJG\r\n2fNo8rQ7gunAgfvLocNIUWklB7iayorTBvBqJOcxSmOlXPkjwrAzznD1iqfj\r\n/5E0vlXHaupb6lL5LDICRO+I29i5GBLIckvyXdkbmYdfd99IQIsayq/RYfkj\r\nwH8e1M5bsnmd3kOsWsMiO5ZIpRe0KM4oFu0Z5xUpFm3riUDlLYtI9Ysqe7g+\r\nsQXYZ6e12jVlmCGuQ0jT6olR8Hn7m0kUwS6lcwf0I/BTPMPmp/TeKNX0cp2F\r\nspUaP3O2t0qJ+DO5LKaSZwKuJ4GJQcWQVuM=\r\n=6bAN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.21.4-esm.2_1680619145849_0.4614251164783212", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/plugin-syntax-jsx", "version": "7.21.4-esm.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "1191d9410218d77aedb253eb3755d13785346485", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-KQ1EqRkO9f7WbhJOUO/XeGOls1+6BrgAKKMTrqP6MMVBjRCP32/0DknIDpU3p1NLhiZVY0poomai+zyt/eUrAw==", "signatures": [{"sig": "MEUCIQDrJVwO94H+K0TaD/9k0xRX7p/UI9U+jlGAmipRlD6opQIgaTqWrpl5lOBS+H6xQr20Beq79o8fQo9p3U/eut2/J3o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4161, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmogcg//czgHGVEya+1ZaXJXaHrCfp2/khXdlUi3PTqDfOmDUIjubrQL\r\nOrY/6G3nlda40tcu9/TCXBeHnvbk6tQ8REVSfWuKp/ojmNkBwe4NxGGM2djS\r\nou1i81I61KL/BfDrIbA3hk+vpW2BlKgQQ81DCMAJFOKPRCu17kLvCQLyF13y\r\nrX1lvtiBuuOadB5zJbLu9fB1QoI/6u7czfl8KJkc5Njrod1an95VgN7n9G41\r\nvV4W5X9hlKN2nlfxNhn8cx1q5wTK5q91g6wZYxuvoEN00LQ8toYgW3kbPNoI\r\nWdyN7LiAxstikkiTY3LO68JBKVxe8LqD4zlA0KInbMRRez/SWF7C6rhCyupU\r\nYFmSqTUpgJQ+5XiHRh/eg7gip64NnYqSiOQYCt8LZ8otoySYl4rWtSQ9kYe8\r\n0v9TtcbhlYgvKYJZAT2RGKcvRZWr8EHRZqnIUTVmozENlGCplSXXezRgiYsI\r\nxWSAUBGRyLds3sPAfzqLCFvIFaRU7NOnoDqqX5a6oS0A3i0giq0ZV4XZS2AZ\r\nQ5Wxr3JOZVDGBItDUg0GlGQcO3sG0sQAa2D50qozcNISRYAS5cAQY9wTixPL\r\nom1UCYaSPkYWsXGiYg3ovCOmrRrX1e1inmR0LKiJF1s8qwrXzM6gWCrwIUmY\r\nqKVj/e3LwPTVLbx66Ub5mkFyqc6Nx/Oi+YM=\r\n=RlzV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.21.4-esm.3_1680620162022_0.07004509653940594", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/plugin-syntax-jsx", "version": "7.21.4-esm.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "4781ef7a8a43534938b382c9574409675aaca786", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-pESU0lCxdm2EnC2dVYHVfSvDaYCLSSKYocNDVPZQN4QWKyF9jHABiTU8ZDUWnjx/o3nuEuScAgQIwtSnP9ThBQ==", "signatures": [{"sig": "MEYCIQD+MFQu1tEQuV/Nevbb7fhQNranVxRGUM1/cVYiQBKRLAIhAK0Hj6KhDnwXiikk21Y2bLzosWOFSxw0Mjr1RuBcosew", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3894, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6LACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2vA/9FjPuI40mwWdpFd7Ewc44Ep4ImELaKJUhVPKp1HUWd4Ey+RYg\r\nbPxivskGWUmzg/POnlxwZaLX7ooJg2fNPizK/NJB6Z0gx9NRnwqBy/n15IDZ\r\nEW0VXWPkEUVnlvJjoy876fTHznmo8uo0AOLz0nxSKX9PY2npbLYMK6IwqtUz\r\nyXnTgJExnPyr+LiVJBWoz3Kd7W9Vo1OvUrz5DreloTCS2Y5/4S6nDRbewrn1\r\n8h13enZ6QUUqQnOJMt5BsCBJqAesNyiGTKen/3xle602sCPIo2UuJ2yCk/Qp\r\ne6nkOq1ljAVOpBrdnTMCQF7YWmkr8jSGKv0hYxHr12gH0hblV/fsr5etxOfH\r\nztXryZgLgiK3TrzXofNtga95wYpb7EE5kM8Lzl7It5aAkPh0Ve3TJz+EU9xt\r\nSneFxOv/dAe4ESmsB+yayZ+oPdTqm7XLf5pgPDf8zRIovTE9BlIqNJGmikYP\r\nCk0lpMtGDK4735/JvbejzQVLfBycKdwEIvaAcsRzipS/iVKd8KPVxgZPQKpL\r\n5f2IYV7SD19gq+iPHGbzqqPYEf34aXMA5GXMOb+Ulw/wu8b2z4wHqv52ABAZ\r\nn4eOdwPUaAwlCRPuuadCYtjHOYmQaPdL9Dim1tFne75m+KJ9MfbiPSK03zh7\r\nAFRucn5lAKmb8j1PMAL2pONnQS8uPBWUFB4=\r\n=iQ9g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.21.4-esm.4_1680621194864_0.5791821486763302", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/plugin-syntax-jsx", "version": "7.22.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "a6b68e84fb76e759fc3b93e901876ffabbe1d918", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-gvyP4hZrgrs/wWMaocvxZ44Hw0b3W8Pe+cMxc8V1ULQ07oh8VNbIRaoD1LRZVTvD+0nieDKjfgKg89sD7rrKrg==", "signatures": [{"sig": "MEUCIEJejJy1S9CRNi9N0w5qVbMCIqWgSDV+qJvX/zdIvxjMAiEAxIQj40tyLIEBf/5JArIECxh4cVVW4Py/YoNRH6tqKuM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4130}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.22.5_1686248471589_0.18647106892871146", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-syntax-jsx", "version": "8.0.0-alpha.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "c1774d455399bbca0464a580967c9f9c393da3de", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-IUZiJmNMOf+kDsb4+xpTzYtQKUcl1OJtunrCMcf0/7FylZ0+1CCLaBXJ88YkAjsY0q2bEE4qArYT7M+UAyoQUQ==", "signatures": [{"sig": "MEUCIQD+BC8uLmxglNcwXLnc19pQ33jcNc5eUDiWy8PULN6w/gIgAzoSf5Ovs2a5/qZXddu6508t8wYw6N3wmzEUM+/Mz/w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3701}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_8.0.0-alpha.0_1689861585402_0.8739032866406837", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-syntax-jsx", "version": "8.0.0-alpha.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "ed0034bcaed60ed31b2693b257364f0c30de8bda", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-uuazir36SMbUMPkoLvYl4akYTLE4GeC838Fp+HXblJfJioRaLEZMaSsVRQ8GNKha1YpLZvihXa/dhgwxB6o5FA==", "signatures": [{"sig": "MEUCIC1zEZ9a4Nsh4eYRCH91Rzzh6L4bTgzBZaTqLRWq3bdmAiEAzTsbrxahfKIIvFS2rCABlWubyS75ZWlM8+nFyAwkaSU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3701}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_8.0.0-alpha.1_1690221081428_0.6398431808187761", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-syntax-jsx", "version": "8.0.0-alpha.2", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "6b5d5e6a630d930782179b045e6079624ba8a7d5", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-DVpGyCAib9TppnfBxj5z53dQjZvsXROVaFYniKSIOEuXBF7WxXbqK6yAo6Q01TWzi1EKyiNWqn4A59dVEAzTew==", "signatures": [{"sig": "MEUCIQC5geBR/mQb2D0F4yQ1FWOwFxP6ok7gUJ6roLgD7G9PsAIgTsg5WpXfxoo/NJqG+Wa1JDINy6udzLN7LU7V6/0bWSg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3701}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_8.0.0-alpha.2_1691594084583_0.9306339918603237", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-syntax-jsx", "version": "8.0.0-alpha.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "36e49a45ed8b350f109d104ddedb1e96046bb73d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-FFOnbqfGdLsLyBExNfMkmCL9HqsiFwNIxhk5mMe2Z46TkoB0bTohVNr2SsxJFpoy7Vd5hjO7SAfz3FZPLINa9g==", "signatures": [{"sig": "MEYCIQDhjhLqCawK2pBs5NOYOy//2R+gaoK3F3TxsOELCTRZoQIhAKGE1sirRRDjM1d/s4FHAiZpG/TIjcsPwWSJ24xL/nSv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3701}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_8.0.0-alpha.3_1695740200091_0.8186627772968611", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-syntax-jsx", "version": "8.0.0-alpha.4", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "3381a7a0053481361cae1854926e1dab88bdf79c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-u4pN0ITZXMGbsNNf/uMFyzVm2X7YYfOpo0JFOo0TTDSFlz5sjrLZbKG/psjP9kozbCQoHheesZXqHE4EFkT2kA==", "signatures": [{"sig": "MEUCIGk8Y/fw1u/qpV+EV7ZsjBtgqmbTXB3qsuqzWOfTcZHyAiEAzYcDMLneLpjQXQaV4vjWgNzJ1XqPVVNg8gNDrxi/PME=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3701}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_8.0.0-alpha.4_1697076366155_0.09276949707515092", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/plugin-syntax-jsx", "version": "7.23.3", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "8f2e4f8a9b5f9aa16067e142c1ac9cd9f810f473", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-EB2MELswq55OHUoRZLGg/zC7QWUKfNLpE57m/S2yr1uEneIgsTgrSzXP3NXEsMkVn76OlaVVnzN+ugObuYGwhg==", "signatures": [{"sig": "MEUCIAXJR69vxn61gl4kXHeoZjvKPmrJFwrNDuuvN9xx33mwAiEAofkDRIHDjm/mKV67uJ7Zo0l/S+AeR+sIrTUPvxdk1wA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4210}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.23.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.23.3_1699513422721_0.30747440970832485", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-syntax-jsx", "version": "8.0.0-alpha.5", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "1973fe41dcc330b0a2aa9b12516c661a4ecefcb1", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-sZ7AlINdnYrrs3XJLWEwB8YR1GJlWEuiGu2w5YUiZ21Nw2yQZeSOUVovGovhuz1vDivreWPjGKaYSdJc1dAaZw==", "signatures": [{"sig": "MEQCIE9577nksctUbiycApQgTPLzGI2t7ToQcuX2EogRVYDZAiAInGuC39NH0p8JPfUEkfhTMQbSF5cfpePS4V9XhsFPbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3814}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_8.0.0-alpha.5_1702307908884_0.9048045059197654", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-syntax-jsx", "version": "8.0.0-alpha.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "0b6e2c5b72bbe79b1e57c62ae96f9a9b662ce36b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-fPVi7UvLL2VHT3hOkBhQrw3ttWK9WYCsOh+pLrAOJ4BY01HApE68aTctDzQRYCZEAzqpS0A5FQ0IXyvoBs5Zog==", "signatures": [{"sig": "MEUCICDFybQxWj7XdVK0TqBgFkvdx75alOjovtEvE/Ald21dAiEA2Caxal4rdUG/j67kM93ZK3UIlLIB+SkdfVibu1os9Xs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3814}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_8.0.0-alpha.6_1706285631387_0.15223988978320713", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-syntax-jsx", "version": "8.0.0-alpha.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "6d7aae42b4f89c0ba6c59689800a1ff037041375", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-oxB3V2y3mBn3mUhNrBPdC2Hr5+F9hoblMugiqfQdZkFVom8VPv/5XbzyczAPJdYNaTRd3kA/0Vfb11v991tvFw==", "signatures": [{"sig": "MEYCIQDX9Srh/1YWBfNaMqvt8lrGdK8A+QHVef6VgWRvc/g5GgIhAKeqBVl8LORK+YIRv8yuenyJH3ltnCpvGf1P60/F/ek5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3814}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_8.0.0-alpha.7_1709129078518_0.7987629655833435", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/plugin-syntax-jsx", "version": "7.24.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "3f6ca04b8c841811dbc3c5c5f837934e0d626c10", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-2eCtxZXf+kbkMIsXS4poTvT4Yu5rXiRa+9xGVT56raghjmBTKMpFNc9R4IDiB4emao9eO22Ox7CxuJG7BgExqA==", "signatures": [{"sig": "MEUCIQD8b96z37htSoY34dp5LvV8HeyTJuv+Psw/HAq8z74uFAIgR7+gfB+MCMKL6flEpQUgSCvWRTx5dQpkowsoZ+L5Zcw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4141}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.24.1_1710841701541_0.19015485092899542", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-syntax-jsx", "version": "8.0.0-alpha.8", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "9a1a3066d43202be6a0679147d7c69273893a257", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-7vfgCfWCmk6o8nZlLovJVdMui1bAwIexAtUEjlsYPFm+DxcfoJkONg2E/BihHeRU1NOFSgtbarXE0nhtsHWTWQ==", "signatures": [{"sig": "MEUCIDamIofFtxBxMbHo+scCV7tuFKJOTJGm4yC9hGypKHiMAiEA1eT0oS0YBDNUwKr4/Yd9JsC5snfGg/LCHR11lPDEfJo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3728}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_8.0.0-alpha.8_1712236780824_0.0636285581374425", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/plugin-syntax-jsx", "version": "7.24.6", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "bcca2964150437f88f65e3679e3d68762287b9c8", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-lWfvAIFNWMlCsU0DRUun2GpFwZdGTukLaHJqRh1JRb80NdAP5Sb1HDHB5X9P9OtgZHQl089UzQkpYlBq2VTPRw==", "signatures": [{"sig": "MEQCIE7sIAfv0WAIk0NE3XJaNXJC0ne2R0Ew1KkmhGxEw9+MAiAI2/+DfFcPttxxABiomzWm7+UtQd+7nmezJJOCnc1cgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70034}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.24.6_1716553462024_0.1794001127807805", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-syntax-jsx", "version": "8.0.0-alpha.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "bc6f3610ca1a5fa7f586873786494b6a2113b495", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-ecYlJfBF1n3+afZUcR+CRCM07qlLmO6xeSfNlTmuIjxgEJBq1TpPUc32IqXyV/jTgb4M/W7f1PsSHLcBQQJGCA==", "signatures": [{"sig": "MEYCIQDAKRwaoIAzLEwQOOYNjVeXk0Hi4vO8yFtx+F07tjMHiAIhAO19sV+BvdBUMU2MZQm8DwNdC7iB2xbjeBa7Jiy6ElMh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69931}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_8.0.0-alpha.9_1717423440042_0.8060247735978676", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-syntax-jsx", "version": "8.0.0-alpha.10", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "49f99f730245ba7a15636fa935c1171733c53984", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-O8Mrz66I2AhsuCT5nkcmkjiFpxie/llQ/BpqbDsMTtrIA0qzhGJER6JzheyI0dIPeh+jKd+jLLddU88H60FVkg==", "signatures": [{"sig": "MEYCIQCqhVon8MNA6xGkyECcMoxC2V8A5MSsdmJb4nv5WuEJrwIhAJDT5ljCRNrvitzWiO5BGf1ZcSU/cRQfuqTCW4oGlRFJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69937}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_8.0.0-alpha.10_1717499992577_0.05311736852100202", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/plugin-syntax-jsx", "version": "7.24.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "39a1fa4a7e3d3d7f34e2acc6be585b718d30e02d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-6ddciUPe/mpMnOKv/U+RSd2vvVy+Yw/JfBB0ZHYjEZt9NLHmCUylNYlsbqCCS1Bffjlb0fCwC9Vqz+sBz6PsiQ==", "signatures": [{"sig": "MEUCIQD4wlzeBjQu/nZOjX8CR2OeWKf1BfJV3wNWHTXvdw1oIgIgYz5hUypdP4bldN4W6PFdQKmWzGG2ro0U3tCvR4trzTU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70030}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.24.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.24.7_1717593312191_0.6851079642559665", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-syntax-jsx", "version": "8.0.0-alpha.11", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "0c9f29b027a8ef8a3ea9f561f7795a452df2b093", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-vQbxXDI5G0R3fwEF7XN4VDaH4riK4+75cGuAHIaLeP7AJCiYOEjpziCuQatf35u5dAtI3kKzieTc9kl/CVKgGw==", "signatures": [{"sig": "MEUCIGZCvHIVMzS13f1ku8jqMEkpUVjkCaE++rbD/rQ5NErkAiEAzxtPi1MjIJq6Es86EiQsEKkpyxAzclCBkayEKA54ylw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69826}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_8.0.0-alpha.11_1717751722330_0.9133437154727735", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-syntax-jsx", "version": "8.0.0-alpha.12", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "46b5b67b37b8d86851ff01cb71ddddb9072acded", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-Hcu2Ez1fFd4ilifMrnIwUVfQUkjUB+ANAYzHBJB1uG/HtJsKXxzkWqljb6qYpxzuYMUaAJaLzJOyU9+Xvlsthg==", "signatures": [{"sig": "MEUCIQCbHwo++4KpbQOWo/sygMtf7+1DrrJeMGIA8yW1nMfYpgIgTXDRJNBricAI3nVx2Eqj3VLAOle0Iyl9DL3gcJigkss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66622}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_8.0.0-alpha.12_1722015198545_0.48726065292968035", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/plugin-syntax-jsx", "version": "7.25.7", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "5352d398d11ea5e7ef330c854dea1dae0bf18165", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-ruZOnKO+ajVL/MVx+PwNBPOkrnXTXoWMtte1MBpegfCArhqOe3Bj52avVj1huLLxNKYKXYaSxZ2F+woK1ekXfw==", "signatures": [{"sig": "MEYCIQDe6x7oulU0cVs7DfRj33sCHqil+2FM3rAIFbWnZJaT7wIhAPGy7vImzKIzSpuqo+k+kbirXcCwUHMR0CPJJQWCualP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74568}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.25.7_1727882075900_0.27307081877263695", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/plugin-syntax-jsx", "version": "7.25.9", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "a34313a178ea56f1951599b929c1ceacee719290", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-ld6oezHQMZsZfp6pWtbjaNDF2tiiCYYDqQszHt5VV437lewP9aSi2Of99CK0D0XB21k7FLgnLcmQKyKzynfeAA==", "signatures": [{"sig": "MEQCIGffVxqhCFHgxiqyOwqZ+ZSTk9MqGu2GZcita+nRBbchAiB0BmlND7yOJQ50kSRcuP4Jo2Pd/JdSxtOlHEtCX5flRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4141}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.25.9_1729610453863_0.11964675321614271", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-syntax-jsx", "version": "8.0.0-alpha.13", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "4140dc0be5de753fe7445895b194dd919ceb2085", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-64QqpoRULZFhxP8Tt17L2plZMRw6Ccep1QnkKr6aNhKKK1n+La1M4ZOuiJJXlrPeF+dGeqIdETiFYPsR3EwHKA==", "signatures": [{"sig": "MEQCICiBfe98rf+qBUQcG3EjrU0YcSHIsB0ZnInyk+M2m9AoAiAhMjrTkO7bwHi+l3VRO93o8KaqUGiqb6sU9ixRecUPjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4065}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_8.0.0-alpha.13_1729864437734_0.4303426395913643", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-syntax-jsx", "version": "8.0.0-alpha.14", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "1ac18761996100f6e8c21ba482a8a94a54da4f99", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-374x8KNiQAJf/yXB9q4d3E5oSFRbHnN/0m0+IVku4lkxIJKBmJ8B4kJ/fw5Z/96W+CKR7SoTGrx3nLpHMmActg==", "signatures": [{"sig": "MEYCIQDBM3ttE7Omz88KwBlWm+yqYkBgnkhx+fY1QBBLOYt8ZwIhALc62cot9G3uKxPn+nkQzcH58m+uxv2XS2+e6Fwpb7ru", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4065}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_8.0.0-alpha.14_1733504030044_0.7089587522778773", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-syntax-jsx", "version": "8.0.0-alpha.15", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "42b5e762523c83f6a6ee4b58e37333d18ea383cf", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-gO7XBBeD0G1reZcN86QDmAPZ0gvhbvolRO0SmjFA0MRfte/T+elPcTZejr5VqI8xsgB2zW4RvlFdw/zqCcp8WQ==", "signatures": [{"sig": "MEYCIQCqfjXKF9sjATjgZP4S3ro6MTYhN5b5KvJ864/E9D94CwIhAJdXUk9Vfu/Cp129dM0oH1MwvgHVLySYuephM6mvatbp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4065}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_8.0.0-alpha.15_1736529854343_0.4433610874724172", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-syntax-jsx", "version": "8.0.0-alpha.16", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "2b0f59f324ad7b643090530d0e221cedee1783fe", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-7+zcsfMTJLwANzi/u6eZ3vcoF7iXkpg9PpYm+/9yGZRM3Tf/Yz4ULomBuz8TJN8ds3T2yPFLiwGWfRqLEi7LqQ==", "signatures": [{"sig": "MEUCIA7GlatmLd2jLUh8FWb2CfrquCOFz/tXQQfnzgNkTxO1AiEA+mmd/UKgXHpXa30KMt70a8EbfJArRODYADAIm+MGZ1w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4065}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_8.0.0-alpha.16_1739534331187_0.8785332882009473", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-syntax-jsx", "version": "8.0.0-alpha.17", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "3aa201f31a60f2137327cdfb90097b0d0f364fec", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-n3Vu5TUVpjiPd9C58sVzgVc+5k119kxDeh7WkWkdh5UOnxKPX05r9ScLWWxjqnVdb1Mxm6lk9nkrJO89GW96Eg==", "signatures": [{"sig": "MEQCIHLLLttgpIa1wgWi02kErfPVUZt6O5CF7Pn+2j1TWJJrAiASPOma8RC5LA75nZ5SoI0qWyd3mcGoDbt7iGAblA6qjQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4065}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_8.0.0-alpha.17_1741717482816_0.2713824357637793", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/plugin-syntax-jsx", "version": "7.27.1", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "2f9beb5eff30fa507c5532d107daac7b888fa34c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz", "fileCount": 5, "integrity": "sha512-y8YTNIeKoyhGd9O0Jiyzyyqk8gdjnumGTQPsz0xOZOQ2RmkVJeZ1vmmfIvFEKqucBG6axJGBZDE/7iI5suUI/w==", "signatures": [{"sig": "MEUCIEVKv1ghSYtmpDlIkVhloOjoUJCa+HWtjUkiR+p812vfAiEAhPsLiGRGOgJVjLSmhr605zVRoqxxhvi6y94zw/p5kTo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4141}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_7.27.1_1746025721240_0.3178634137039116", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/plugin-syntax-jsx", "version": "8.0.0-beta.0", "keywords": ["babel-plugin"], "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/plugin-syntax-jsx@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "dist": {"shasum": "7cdac7982e691568d0ff007ddb0dc31cc0af0822", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-b1irywOBBFuN7fFTuvKetMeDDLxO4JzLCRaC3PpUYWS59oiVAZuu/NHjKkgchu+vJbRJrXYSwX8lztFC2+01ig==", "signatures": [{"sig": "MEUCIQDtqodXV2DAWphkwgTem9RFZ7i69zLudt/CSDoerlvQhAIgZUKxKK1/bR9FQbyMhXMCVEaV7TGq/7FxYDexarJVDQs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 4043}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "directories": {}, "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/core": "^8.0.0-beta.0"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/plugin-syntax-jsx_8.0.0-beta.0_1748620253229_0.6509158755173192", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/plugin-syntax-jsx", "version": "8.0.0-beta.1", "description": "Allow parsing of jsx", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-jsx"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-beta.1"}, "peerDependencies": {"@babel/core": "^8.0.0-beta.1"}, "devDependencies": {"@babel/core": "^8.0.0-beta.1"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "type": "module", "_id": "@babel/plugin-syntax-jsx@8.0.0-beta.1", "dist": {"shasum": "312bc3b50b54a5059db5eb3818d98cc96f5a60b3", "integrity": "sha512-e6ust8JSllUk/nnkpv6InTXmdEyvfdaPYplaqjzwEzj1zqC0ZdHjtb8JMIE2SpYFlcQieOu/x/6NBk2qO9KZBg==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 4043, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQD7Yxx3FWRmePFm0vUhnDsB6o/hwkjJJVYLSpzAnvMP8wIhAO7vVX0zI2EvqatqT5bmKnE5KbUxEAedsLHvXe5Wo1hy"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/plugin-syntax-jsx_8.0.0-beta.1_1751447047475_0.4711809241486955"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:34:20.769Z", "modified": "2025-07-02T09:04:07.872Z", "7.0.0-beta.4": "2017-10-30T18:34:20.769Z", "7.0.0-beta.5": "2017-10-30T20:56:03.707Z", "7.0.0-beta.31": "2017-11-03T20:03:15.066Z", "7.0.0-beta.32": "2017-11-12T13:33:03.254Z", "7.0.0-beta.33": "2017-12-01T14:17:13.863Z", "7.0.0-beta.34": "2017-12-02T14:39:06.889Z", "7.0.0-beta.35": "2017-12-14T21:47:35.674Z", "7.0.0-beta.36": "2017-12-25T19:04:24.750Z", "7.0.0-beta.37": "2018-01-08T16:02:23.247Z", "7.0.0-beta.38": "2018-01-17T16:31:43.468Z", "7.0.0-beta.39": "2018-01-30T20:27:27.114Z", "7.0.0-beta.40": "2018-02-12T16:41:23.956Z", "7.0.0-beta.41": "2018-03-14T16:25:55.504Z", "7.0.0-beta.42": "2018-03-15T20:50:30.838Z", "7.0.0-beta.43": "2018-04-02T16:48:16.474Z", "7.0.0-beta.44": "2018-04-02T22:19:58.529Z", "7.0.0-beta.45": "2018-04-23T01:56:19.332Z", "7.0.0-beta.46": "2018-04-23T04:30:43.206Z", "7.0.0-beta.47": "2018-05-15T00:08:23.633Z", "7.0.0-beta.48": "2018-05-24T19:21:44.782Z", "7.0.0-beta.49": "2018-05-25T16:01:23.715Z", "7.0.0-beta.50": "2018-06-12T19:47:06.339Z", "7.0.0-beta.51": "2018-06-12T21:19:33.754Z", "7.0.0-beta.52": "2018-07-06T00:59:19.374Z", "7.0.0-beta.53": "2018-07-11T13:40:09.450Z", "7.0.0-beta.54": "2018-07-16T18:00:00.635Z", "7.0.0-beta.55": "2018-07-28T22:07:04.836Z", "7.0.0-beta.56": "2018-08-04T01:04:31.597Z", "7.0.0-rc.0": "2018-08-09T15:57:45.023Z", "7.0.0-rc.1": "2018-08-09T20:07:26.628Z", "7.0.0-rc.2": "2018-08-21T19:23:18.659Z", "7.0.0-rc.3": "2018-08-24T18:07:19.307Z", "7.0.0-rc.4": "2018-08-27T16:43:34.331Z", "7.0.0": "2018-08-27T21:42:42.222Z", "7.2.0": "2018-12-03T19:00:54.462Z", "7.7.4": "2019-11-22T23:31:56.413Z", "7.8.0": "2020-01-12T00:16:17.557Z", "7.8.3": "2020-01-13T21:41:19.470Z", "7.10.1": "2020-05-27T22:07:08.652Z", "7.10.4": "2020-06-30T13:11:46.747Z", "7.12.1": "2020-10-15T22:39:43.266Z", "7.12.13": "2021-02-03T01:10:39.269Z", "7.14.5": "2021-06-09T23:11:49.561Z", "7.16.0": "2021-10-29T23:47:27.462Z", "7.16.5": "2021-12-13T22:28:02.353Z", "7.16.7": "2021-12-31T00:21:46.013Z", "7.17.12": "2022-05-16T19:32:41.223Z", "7.18.6": "2022-06-27T19:49:56.063Z", "7.21.4": "2023-03-31T09:01:52.420Z", "7.21.4-esm": "2023-04-04T14:09:17.465Z", "7.21.4-esm.1": "2023-04-04T14:21:07.699Z", "7.21.4-esm.2": "2023-04-04T14:39:06.060Z", "7.21.4-esm.3": "2023-04-04T14:56:02.228Z", "7.21.4-esm.4": "2023-04-04T15:13:15.084Z", "7.22.5": "2023-06-08T18:21:11.751Z", "8.0.0-alpha.0": "2023-07-20T13:59:45.548Z", "8.0.0-alpha.1": "2023-07-24T17:51:21.576Z", "8.0.0-alpha.2": "2023-08-09T15:14:44.902Z", "8.0.0-alpha.3": "2023-09-26T14:56:40.279Z", "8.0.0-alpha.4": "2023-10-12T02:06:06.386Z", "7.23.3": "2023-11-09T07:03:42.891Z", "8.0.0-alpha.5": "2023-12-11T15:18:29.148Z", "8.0.0-alpha.6": "2024-01-26T16:13:51.568Z", "8.0.0-alpha.7": "2024-02-28T14:04:38.657Z", "7.24.1": "2024-03-19T09:48:21.734Z", "8.0.0-alpha.8": "2024-04-04T13:19:40.971Z", "7.24.6": "2024-05-24T12:24:22.170Z", "8.0.0-alpha.9": "2024-06-03T14:04:00.230Z", "8.0.0-alpha.10": "2024-06-04T11:19:52.701Z", "7.24.7": "2024-06-05T13:15:12.358Z", "8.0.0-alpha.11": "2024-06-07T09:15:22.544Z", "8.0.0-alpha.12": "2024-07-26T17:33:18.791Z", "7.25.7": "2024-10-02T15:14:36.089Z", "7.25.9": "2024-10-22T15:20:54.067Z", "8.0.0-alpha.13": "2024-10-25T13:53:57.952Z", "8.0.0-alpha.14": "2024-12-06T16:53:50.210Z", "8.0.0-alpha.15": "2025-01-10T17:24:14.512Z", "8.0.0-alpha.16": "2025-02-14T11:58:51.354Z", "8.0.0-alpha.17": "2025-03-11T18:24:42.976Z", "7.27.1": "2025-04-30T15:08:41.413Z", "8.0.0-beta.0": "2025-05-30T15:50:53.424Z", "8.0.0-beta.1": "2025-07-02T09:04:07.649Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-jsx", "keywords": ["babel-plugin"], "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-plugin-syntax-jsx"}, "description": "Allow parsing of jsx", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}