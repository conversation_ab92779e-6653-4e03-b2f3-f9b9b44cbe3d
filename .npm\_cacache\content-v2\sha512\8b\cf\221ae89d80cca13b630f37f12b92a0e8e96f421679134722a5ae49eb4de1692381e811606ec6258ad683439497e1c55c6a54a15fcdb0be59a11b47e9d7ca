{"_id": "@babel/plugin-syntax-class-static-block", "_rev": "5-f7a0c5f352c3acf2afbf795716e5d43d", "name": "@babel/plugin-syntax-class-static-block", "dist-tags": {"latest": "7.14.5"}, "versions": {"7.12.0": {"name": "@babel/plugin-syntax-class-static-block", "version": "7.12.0", "description": "Allow parsing of class static blocks", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-class-static-block"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": {".": "./lib/index.js"}, "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_id": "@babel/plugin-syntax-class-static-block@7.12.0", "dist": {"shasum": "dd9e60e237c0246cd5cd9581d7bbc5a3d29b9bbf", "integrity": "sha512-ylFXe7XyXlBboTc8MG61A98qdIdPoRfpGk4ssRZeuxSrBX+9w8S/MikL3hets27O2+9ZAb8g+td0K3UuNsVZPg==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.12.0.tgz", "fileCount": 4, "unpackedSize": 2570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfh1luCRA9TVsSAnZWagAAR3EQAKRY9og5mi2ZhusVWIZ1\nr9VcC34JJpRxVbVN6FEnPlY55vpjLbovN08tDhHiGDFcEEevuPU6QTH4Asmj\nUYVfa0u9J5cKnqN+8/vZnOB2/DaY3hqNqu/fraxq5t2soki50rwK8zd/5Ghr\nAmCUCsXfVFkvJporibBmPbnbXc/K3alU5WRBEwgqwihkKV1onOdFil9queCj\n5CMCZ55z58qCTNcfH439pEtwASRZiQNVjnxRvMauqghGbk/JwEdV4blzfn20\n/87fAs7W/xzsp7dItEDDECUFaj1wN6al5CTPOaJ2mBuRaScIAyCrTAq7rZoX\npYvrJyqNEMVf6NailLfsx51KqwIK6ibN85bxssPZvb8mfSfxAS/jutbB7+vY\ngdjQqKg2Q5RnCf4uNp4hHwEC4UZ62L64HogiIigUdg2oKjhh+0XRVlTTDkA7\nBK9a3haWRxYgVrpdabhsEXsGsNRHU+QFp1JkuaFs5rL3xO/yGrgVGJom2C2W\niuUwwiQJsdTgFpCq31UaqXneCmp8bdoQnhBlUAh35wLyEJypa6MbDO4PmUMA\nbftmtv2H50HCR0i0djVVNmNhcP6lCGEQUOLv6t7r3gzFv/z6J74LIG7S6Qw/\n2G3QRkVgrNtSMAkwdKuGEPJoZZpAblm+DuOir0hZ8uwh1fIybboPOXb9qCol\ndlTT\r\n=wtAj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBaN+J/2043z5giSq8m6EKY0dwIryILNh0wiabX7+LmtAiBXPu05xtfvloQa5MpWydD/w+Dp1h5dpLv3Wt8lpOqsrg=="}]}, "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-static-block_7.12.0_1602705773615_0.10031466592777516"}, "_hasShrinkwrap": false}, "7.12.1": {"name": "@babel/plugin-syntax-class-static-block", "version": "7.12.1", "description": "Allow parsing of class static blocks", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-class-static-block"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": {".": "./lib/index.js"}, "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_id": "@babel/plugin-syntax-class-static-block@7.12.1", "dist": {"shasum": "cf72706e328ed1cec5eb3798a149e0e8ac8beedd", "integrity": "sha512-wPCgnoCni8jw4rrn2LlbuRaLFMBhLyDF7HqvH0QehKhMqD0IFzfn5sFEcB0Qw7IuzLzyBVoO/Oa3NxyGXnLb+w==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.12.1.tgz", "fileCount": 4, "unpackedSize": 2570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM+kCRA9TVsSAnZWagAAoSMQAJcNGO+VLmDiIquuXgJe\n4oRcO8We20yioBlDnEdn8WGJrpJlnBUwri7yBBSlBVpR3DIrvSGzNLv9Qro9\n4dFUzY0E8rH6H+LtU1dmCugNeSSQwjCIGbKoG8z5ms2WNJD7QzDj42/Z+4eH\n5n22zSJX2krjg7M2txyoDNU4e/W/0peP3R+5p8quskvasAC6fmjFOXgJyyK6\nr1oSuPzwADmdvjtd8JepM/ouEi0wjiVmsNx3LB+ZsEk76geG8Jw5gSTEcRMm\nVbxqYsG6bjZDt0t/RokhTAUXQY70cuAA+ggqT3Sm3WtiBugn+wER4bQ6xoWf\nkjMb07fj2ql9L3Tviobzk6APan9zDSBSUA2cQfthQk2qCJdyZRYWbrt4f+N2\nyuna/EJD2Tjexuqb0pfRSfwXaL+QIuVxKrIOlOCqV+sOyXRbvzibj2vgptKu\nQtskRGKqiNdJjEWsRJm43tlwmkNKGVdFctgzB5hfYX5pFSdb4VaxpMmcqPz8\nuhvB8x4MrENtD6BZh1eHYA8iEZ6qwmCHiLAW1+nKgvsnZcyG6zfEJLZ7OTfM\n2NWP9ki7YuIL1V8O0CJoggOkwQ9ZOcINQ564jwW4Rs/k2kXgy1DZP7Hp7sBP\nkFWNVf1NVy7kfhQWxnBiU9j+YU4OguJLAPSijdgBGhvk7xA1UOqwOAkN6ie5\nSZmY\r\n=Iywp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID1SO5zEUfInQJWUluix0AHEdHGLiJIS44hNNPV11yMtAiAzz6g3IzySjRPRSHwR11xI3N+h9ybk3h5PqWQWEWceqw=="}]}, "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-static-block_7.12.1_1602801572048_0.700752575377986"}, "_hasShrinkwrap": false}, "7.12.13": {"name": "@babel/plugin-syntax-class-static-block", "version": "7.12.13", "description": "Allow parsing of class static blocks", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-class-static-block"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-class-static-block", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": {".": "./lib/index.js"}, "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "_id": "@babel/plugin-syntax-class-static-block@7.12.13", "dist": {"shasum": "8e3d674b0613e67975ceac2776c97b60cafc5c9c", "integrity": "sha512-ZmKQ0ZXR0nYpHZIIuj9zE7oIqCx2hw9TKi+lIo73NNrMPAZGHfS92/VRV0ZmPj6H2ffBgyFHXvJ5NYsNeEaP2A==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.12.13.tgz", "fileCount": 4, "unpackedSize": 2649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfgGCRA9TVsSAnZWagAAuEoQAJ3bWmk3Z5Y2RMFRnbb+\nerjNLMKuec7YfOX+v7tyJmVsFO/Rpch5ZieNIZ8Avl/YaX6LijEvfStrpjxr\n87dl24W4lPcCvjK8oY3593VcBnQ0okxCGoFAFAg3LbU+YylRr5gkNX4jP3Xy\nwm7M3Og5ZAqRwNaJ2dXNSBjIO0DNvsa9TPEhptuYcGQwOWNVVNmeXospEBJK\nVgfe1Ux71hxRjbAZBZxpEN6LDV/6eybZF95tFnhcc8Q6eIeitIgMogycPb5c\nuNcY7BvwvMoJ6I2tFOkNz2XD6pIFNoZ32gF+Y7LLtgmY4LodDSvgVrggTwyA\nN6e/oivc7bs5HoTfvzw4/UZM6JDps47Av0jxBu6kl3tO0Q3GuGrFonAvI+OD\n/pvtJJEjRopHSD4MMa8i0drXONiuPT2xHXJffJtbNhDL+4TpHSYjOfPa0Ow6\n9pvqBM2lTE3W6sMs5TRUJwnjgsU1SnY5ZiYVlxwialA7BHcM1wTVeTg1GHub\nxooRH46fBCHbVAT+eFU482t56tTgKjZaeU5cUXQiuFmrcfe2/QeBj7fyxQKV\nyh/zPktCAjjOuyf8OveRGpcZHHV7/ATYMq/XnlTUEZ9+JQHaVCyKt/3V0/P0\nMbRFFdHJXuvzDat8UwwXzmVjvSoItWWjXawwxjfIKJQlKvnt8Jfkh509xDKV\nARxD\r\n=vi0x\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAXMSxVC7zpcGwQFBKsWC57mOWtoClpIu+n3K41nchxLAiEApTBRP/zQjLmRI1grYDAz1mJIc/okRQ21XASfk8enr9M="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-static-block_7.12.13_1612314629837_0.8704686215933897"}, "_hasShrinkwrap": false}, "7.14.5": {"name": "@babel/plugin-syntax-class-static-block", "version": "7.14.5", "description": "Allow parsing of class static blocks", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-class-static-block"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-class-static-block", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": {".": "./lib/index.js"}, "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "_id": "@babel/plugin-syntax-class-static-block@7.14.5", "dist": {"shasum": "195df89b146b4b78b3bf897fd7a257c84659d406", "integrity": "sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz", "fileCount": 4, "unpackedSize": 2744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUquCRA9TVsSAnZWagAAsvAP/3BV+J67hEi8Ef3OVmFH\nYh2MUTtnPKqFWjE5TjNCQ2AUlO8j5/3t7G+4d7YY+mKmPlTFQNrL01vftl+9\nneFlVVkEy9+D8V8aAoD0C4oBcB2aqq3/cXJZMrXXXo5Xqw3da3nYqNc5ToM2\nZIQ2+Nyn8YyCVBa7TdrhtawzG0qGoLYlw5USHUxZPKbLOly9wMbhMhXnv7Hr\nnaccBHSY6cMsNApEEdkG+vGg0E5agOzb2+wMQktBLAeT1hyXBZWavYT11g9n\nV/B8GZTX++knTQaNRlH7FWs+L9w0hpaMYgSl1zBy4+1mv3V363zSWIvP0TXi\niq5LICIIyRqLhLDy28SZd/ndFkgN4hSSa/SFBxSPloHlCtc6Sgz6uREqGinh\np4yRuew9L0PljxeqoC89anB4kcF0OWg+azSJDAeseunDW9pYjnB0OdvIOasq\nyL8WDIkQCptPZgqzFu4kmSiBdIyjBNVsVFp5gs9FyWX8oTuEcAbE4XBRqQE3\ncu5mnRRB3oyEwlflu41CpeloU24O1Uew0IltF6ipBZNDB7ucyHQDOifJMM8z\n5hhwySoYOHvz6cnXG2ouduhdrvoa76i2PJNWNSGklkYtnaoHYjkNOUBB8A6u\n/OpG8oPFwAA5ycfNNKCZLSSxTo4WeDGdCRMLXTw0Sb/NHtwIHZ6IJSpEaQlw\nB4YN\r\n=BATr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBm2tKKFOVOQ4rxlNfanrvMiPK9sZTcLHviDtQWzpflwAiAuQePRNyhA85VxoVGXiC7vVx2EALktkJuITe7skdWkyw=="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-class-static-block_7.14.5_1623280302045_0.14854421704979215"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-10-14T20:02:53.371Z", "7.12.0": "2020-10-14T20:02:53.727Z", "modified": "2022-04-04T17:25:23.775Z", "7.12.1": "2020-10-15T22:39:32.215Z", "7.12.13": "2021-02-03T01:10:30.083Z", "7.14.5": "2021-06-09T23:11:42.208Z"}, "maintainers": [{"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}, {"email": "<EMAIL>", "name": "jlhwung"}], "description": "Allow parsing of class static blocks", "keywords": ["babel-plugin"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-class-static-block"}, "license": "MIT", "readme": "", "readmeFilename": "", "homepage": "https://babel.dev/docs/en/next/babel-plugin-syntax-class-static-block", "author": "The Babel Team (https://babel.dev/team)"}