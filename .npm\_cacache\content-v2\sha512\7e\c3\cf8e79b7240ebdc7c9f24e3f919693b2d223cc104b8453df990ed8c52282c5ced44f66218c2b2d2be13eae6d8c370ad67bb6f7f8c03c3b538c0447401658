{"_id": "js-yaml", "_rev": "267-2360bb7f142ba760e3028c482ef99bf1", "name": "js-yaml", "description": "YAML 1.2 parser and serializer", "dist-tags": {"latest": "4.1.0"}, "versions": {"0.2.0": {"name": "js-yaml", "version": "0.2.0", "description": "YAML 1.1 Parser", "keywords": ["yaml", "parser", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net"}, "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "dependencies": {"jsclass": "3.0.4"}, "engines": {"node": "> 0.4.11 < 0.7.0"}, "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "_id": "js-yaml@0.2.0", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.103", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "a2480a4db3c6896e4a5db16b99b504cb2768cc32", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-0.2.0.tgz", "integrity": "sha512-o6S45ie8keyNDekNzq2l4UCBsJ/UjOosg/RI3on4xRgLLltrEsRX3ffHKaF9tQ+ypZb0nEDSz0kucExz+CEQYg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDUGKVXko2GhU5/UZyDOJZAP6i/L+JNznMRKa5/hI4I/wIhAN4X2wyITUXqD4syJahjzZqEc6Z3UIgB/w5IJeqmiqWX"}]}, "scripts": {}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.2.1": {"name": "js-yaml", "version": "0.2.1", "description": "YAML 1.1 Parser", "keywords": ["yaml", "parser", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net"}, "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "dependencies": {"jsclass": "3.0.4"}, "engines": {"node": "> 0.4.11 < 0.7.0"}, "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "_id": "js-yaml@0.2.1", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.103", "_nodeVersion": "v0.5.9", "_defaultsLoaded": true, "dist": {"shasum": "f42ad812a3c3a72740d571f4fb016e31f4bb9344", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-0.2.1.tgz", "integrity": "sha512-bk4SuEnoEaNSafutG68/WUF3GsQsL2aEzFlw7VHUPtrKZfQiqea1gjNzSq6ZPJpxlmBGzz0QPWa8Pcuf0ECo/w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFFXd7R7HFXgjf0tX8zRDzxq5+9788+EYZwNzUuSirm+AiBkHHhhzSgcmzfvs5l3QIIJoiMn7APrHpVhzt7/cW2O/A=="}]}, "scripts": {}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.2.2": {"name": "js-yaml", "version": "0.2.2", "description": "YAML 1.1 Parser", "keywords": ["yaml", "parser", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "dependencies": {"jsclass": "3.0.4"}, "engines": {"node": "> 0.4.11 < 0.7.0"}, "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "_id": "js-yaml@0.2.2", "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.103", "_nodeVersion": "v0.5.9", "_defaultsLoaded": true, "dist": {"shasum": "79650583b962457ef4eba143fcbd8b57f0972c07", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-0.2.2.tgz", "integrity": "sha512-uDs326I93PjMegbbccrhCnCxWvmHa2/e8qi2JxmoDdtsLxEPURCfi1eex9PCnFkO3BY2uzTIPojKcqB4HKgs9g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDXGLTEWWIyf2WJO2/2yyqetwGWpQJMsCB5aORUs0DDcAiEApA1iSgu/EqQ4nE8C0eZ5saf1YwjEmc56mR92pSkcnpE="}]}, "scripts": {}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.3.0": {"name": "js-yaml", "version": "0.3.0", "description": "YAML 1.1 Parser", "keywords": ["yaml", "parser", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "engines": {"node": "> 0.4.11 < 0.7.0"}, "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "_id": "js-yaml@0.3.0", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.103", "_nodeVersion": "v0.5.9", "_defaultsLoaded": true, "dist": {"shasum": "ed2aecd85e9f474c6c766bde89b3e27a88754f97", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-0.3.0.tgz", "integrity": "sha512-xBAB4HTxXKBHRrLFbh5zN9A47zCVdykVXxlVFoiLzDeytid0KkUiixZSw9usWVhtITsNPv/8oRBTL7xxP9/Vtg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBfcascXiKhALgjeQGuDw09Ms1oiEXxQXGt8/+kBAzWLAiARMnHb3vL3v1MlUxZpiZZOP2Bmy1gBv7pyOyrgwxAA/w=="}]}, "scripts": {}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.3.1": {"name": "js-yaml", "version": "0.3.1", "description": "YAML 1.1 Parser", "keywords": ["yaml", "parser", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "engines": {"node": "> 0.4.11 < 0.7.0"}, "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "_id": "js-yaml@0.3.1", "dependencies": {}, "devDependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.103", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "221a8b84dcddd5ee463311b10366574a091c42dd", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-0.3.1.tgz", "integrity": "sha512-GPV56GQMXZtczFLbe6LzItRqLo5azARE2q7jL9L7+bVexc/0fMWrtB/j1CN15HetEPdnhQrHrOO1SdfApyRtYA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCHKbvUhZsfPMvsRS5dorV7zF//TLhW4cGM89U1UqP2yAIgFpmZUnFLE6DH6ZWsasyy3oRlS5m99vtvQ1qEw1ECc50="}]}, "scripts": {}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.3.2": {"name": "js-yaml", "version": "0.3.2", "description": "YAML 1.1 Parser", "keywords": ["yaml", "parser", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "bin": {"jsyaml": "bin/js-yaml.js"}, "main": "./index.js", "scripts": {"test": "make test"}, "devDependencies": {"jslint": "https://github.com/reid/node-jslint/tarball/6131ebf5713274871b89735105e3286131804771"}, "engines": {"node": "> 0.4.11"}, "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "_id": "js-yaml@0.3.2", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.103", "_nodeVersion": "v0.4.12", "_defaultsLoaded": true, "dist": {"shasum": "7b007593429b52d0e91825648c9d8e9e44fe1c8e", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-0.3.2.tgz", "integrity": "sha512-+z9PiZWg3GMMvrbHoiJ/G32KlA5lA89qeS2SThwe5OrTuYGGxL4OtGVhm3+UfOlRQzI09C5Sh5v29gNxNmfxKQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDsUowPE6Z8TBumFDRrjZFAELEY5fX5/R11iRuIrgBNdgIgLVogdbJ224CpNFkeFbPoH+wSyA5Y6I6jmF/+p4APYLk="}]}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.3.3": {"name": "js-yaml", "version": "0.3.3", "description": "YAML 1.1 Parser", "keywords": ["yaml", "parser", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "devDependencies": {"vows": "~ 0.6.0", "jslint": "https://github.com/reid/node-jslint/tarball/6131ebf5713274871b89735105e3286131804771"}, "engines": {"node": "> 0.4.11"}, "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "_id": "js-yaml@0.3.3", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.5", "_defaultsLoaded": true, "dist": {"shasum": "258a966b44a92dfbde4750c049a0fb5ff16d49ce", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-0.3.3.tgz", "integrity": "sha512-sBA52KqfC4EAZH6UNaz7DTF0dtg0ygF2P5wCFecANk6T98DdmKwadAK4+CuQFJuh4KczzUyy1LAyG0W0nsdU8Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBngbwQQZExwlTCz1/ndz+HHnLXbIH2z8uVhDzqzqMs6AiEArAqiwU2PgUgJwjz1Adky80ZO7lw1GbQQBzTlx77SvRw="}]}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.3.4": {"name": "js-yaml", "version": "0.3.4", "description": "YAML 1.1 Parser", "keywords": ["yaml", "parser", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "devDependencies": {"vows": "~ 0.6.0", "jslint": "https://github.com/reid/node-jslint/tarball/6131ebf5713274871b89735105e3286131804771"}, "engines": {"node": "> 0.4.11"}, "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "_id": "js-yaml@0.3.4", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.5", "_defaultsLoaded": true, "dist": {"shasum": "361a88e93e6a2cb5fe6222ed917906f0402fe4d3", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-0.3.4.tgz", "integrity": "sha512-V/SKGGIc7YNvQKxjEGbQoEeUSBWefuf0XNXu8H1RURTVeK1lF3AdIiir0J+UG0OyMKnNZzFGXXYIyKFl3dp6cw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBnRIc2Ii+HUg/Cv8C8Kk0mtIgvPoTk183w1WLlNGCssAiA4gCLX6aFn1pIyxavqk2zWOhgPoli4woZWcATztyDi1w=="}]}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.3.5": {"name": "js-yaml", "version": "0.3.5", "description": "YAML 1.1 Parser", "keywords": ["yaml", "parser", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "devDependencies": {"vows": "~ 0.6.0", "jslint": "https://github.com/reid/node-jslint/tarball/6131ebf5713274871b89735105e3286131804771"}, "engines": {"node": "> 0.4.11"}, "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "_id": "js-yaml@0.3.5", "dependencies": {}, "_engineSupported": true, "_npmVersion": "1.0.106", "_nodeVersion": "v0.6.5", "_defaultsLoaded": true, "dist": {"shasum": "c41bac6f5332e22ae5962323e65cfc38487f2487", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-0.3.5.tgz", "integrity": "sha512-wMGLri91GftZT4yFx6R15wYnzdokbLTKR8J7y1l6M4Fv8p176aBaPlfLKwr0UR6r1XUN6UezZzQPlCk3vvam2g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE5Svx6RjxBduRylBCOmIEadL8oQ6PQYdoA6L2fRuWBGAiAoANsPexmaHuZAYH1AXZXOlQqhnN7zglyql58s+3nPDw=="}]}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.3.6": {"name": "js-yaml", "version": "0.3.6", "description": "YAML 1.1 Parser", "keywords": ["yaml", "parser", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "devDependencies": {"vows": "~ 0.6.0", "jslint": "https://github.com/reid/node-jslint/tarball/6131ebf5713274871b89735105e3286131804771"}, "engines": {"node": "> 0.4.11"}, "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "_id": "js-yaml@0.3.6", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.1", "_nodeVersion": "v0.6.10", "_defaultsLoaded": true, "dist": {"shasum": "6134b2ffde54ad070beae9f342750f0f7133d7a9", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-0.3.6.tgz", "integrity": "sha512-2lKFr6Ezy25yEhSw28Vi+rgAdbAaxFoLA1tsUsY0dB1IXsJ9vJmfEjfbYUpZmLzE8tEUnrVhBvVZsXYUpwwnTQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHvBbi/E39yccgVgfSDQsmfm/i1CXJF3ro8CjnRxXITdAiEAmbLzOKvdut7G+w2SkTohGQuBDnG4A3pkhB/XFxm2Vp4="}]}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "0.3.7": {"name": "js-yaml", "version": "0.3.7", "description": "YAML 1.1 Parser", "keywords": ["yaml", "parser", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "devDependencies": {"vows": "~ 0.6.0", "jslint": "https://github.com/reid/node-jslint/tarball/6131ebf5713274871b89735105e3286131804771"}, "engines": {"node": "> 0.4.11"}, "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "_id": "js-yaml@0.3.7", "dependencies": {}, "optionalDependencies": {}, "_engineSupported": true, "_npmVersion": "1.1.1", "_nodeVersion": "v0.6.10", "_defaultsLoaded": true, "dist": {"shasum": "d739d8ee86461e54b354d6a7d7d1f2ad9a167f62", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-0.3.7.tgz", "integrity": "sha512-/7PsVDNP2tVe2Z1cF9kTEkjamIwz4aooDpRKmN1+g/9eePCgcxsv4QDvEbxO0EH+gdDD7MLyDoR6BASo3hH51g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA209XpZXTEwplWdvsivm1BCp6OHu50PsoiORWCVI8sVAiEA+9opTLgrMsrUidvrkYCb55a7HVy/+eav6Ib9LblARW4="}]}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "js-yaml", "version": "1.0.0", "description": "YAML 1.1 Parser", "keywords": ["yaml", "parser", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "scripts": {"test": "make test"}, "dependencies": {"argparse": "~ 0.1.3"}, "devDependencies": {"vows": "~ 0.6.0"}, "engines": {"node": ">= 0.6.0"}, "_id": "js-yaml@1.0.0", "dist": {"shasum": "3e5b18955f343d042c1f7506810697ceadded873", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-1.0.0.tgz", "integrity": "sha512-sTLRjjwBZTCU1aQWytG2LvMm2MjoGGsBAqrC8CdzYRh2AOzQvMsYRstLr3cPismwy5hYhAY3bX/VEN4QZqSJCg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFuGfPDb6TF4keFRFfuRfZTg3nSrHGxiNjmILQAAgIqwIgAP3WRWGCMAQQMRTanDtQN1uWFi4dVTujn1a2qOY0jqo="}]}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "1.0.1": {"name": "js-yaml", "version": "1.0.1", "description": "YAML 1.1 Parser", "keywords": ["yaml", "parser", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "scripts": {"test": "make test"}, "dependencies": {"argparse": "~ 0.1.3"}, "devDependencies": {"vows": "~ 0.6.0"}, "engines": {"node": ">= 0.6.0"}, "_id": "js-yaml@1.0.1", "dist": {"shasum": "189fdf5ddf523f4f25d4f9914712e30776246201", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-1.0.1.tgz", "integrity": "sha512-GWwH1zBVNwcJE9rmaZh2GY13hdDRoVwLLrAs+1ie72IqLzsmiL7Nau2Eva9kwSEznLiD0d8hAt87LvXeHkmPSg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHuUlowa31YM9bwou+6ReMS5rU78dbPhlcK7dku57CzzAiB4t4LDwVrKOhXYuwF8+cbRegOduqHwIgiIT7oDneoXhg=="}]}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "1.0.2": {"name": "js-yaml", "version": "1.0.2", "description": "YAML 1.1 Parser", "keywords": ["yaml", "parser", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "scripts": {"test": "make test"}, "dependencies": {"argparse": "~ 0.1.3"}, "devDependencies": {"vows": "~ 0.6.0"}, "engines": {"node": ">= 0.6.0"}, "_id": "js-yaml@1.0.2", "dist": {"shasum": "ee3f5cebdf1b62d721e573480879d12e919c80d0", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-1.0.2.tgz", "integrity": "sha512-vudqoSo7VMq4ufUf1nhwX/sOz0BgHc0NqhJI8JT3bMJd158R5iXJk0/gBqeIMrui5dnGN3lJZOIlG3fxh4LIeQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDePwinVVat/q1Y1qABwzspqoqNtezRzaJaVhrXhiuY6AiEA+FGPx4j/qSKPX5+1JGmNM6I7P97gKZvfN8Vkh5Y7guM="}]}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "1.0.3": {"name": "js-yaml", "version": "1.0.3", "description": "YAML 1.1 Parser", "keywords": ["yaml", "parser", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "scripts": {"test": "make test"}, "dependencies": {"argparse": "~ 0.1.3"}, "devDependencies": {"vows": "~ 0.6.0"}, "engines": {"node": ">= 0.6.0"}, "_id": "js-yaml@1.0.3", "dist": {"shasum": "ec619760ffc8ae501c3d62673d874e2b9f07422a", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-1.0.3.tgz", "integrity": "sha512-UqzDrK8526iLQnHaXZDBiAwn+ubNakeOViDTn/jK/PvoV8Zbib1ldAgvEopGIH4JqolR3zKPIMmxi9c5RpZ+EA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD7HTFuWEIv0r+uF1sumZ1zRBRMTcl5Vs4/+qT0BBNHWQIhAJ2AjxyQwEXoHnC5o9fSVq9zPdekbcZ42jP6a615CF+D"}]}, "_npmVersion": "1.1.61", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "js-yaml", "version": "2.0.0", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "scripts": {"test": "make test"}, "dependencies": {"argparse": "~ 0.1.11"}, "devDependencies": {"mocha": "*"}, "engines": {"node": ">= 0.6.0"}, "_id": "js-yaml@2.0.0", "dist": {"shasum": "49f50ea0d2264f68c2e16061e4c71bbebe1c5f93", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-2.0.0.tgz", "integrity": "sha512-CXw0GMCOjLamiV3rPjMx4aqebrdY1x9K30uttErduYrD7cu/8mgiDYEz6oTSMrqlScLAfAqZRxJscTQv7rDucQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD7Lpaie6GC4hLzmt7MkoSU6TEAfz30n/K6y3oZzshc3gIhAL1AojmBIvIefhadFCAHp0+PbRWFY3+OlfEal1KgJuyJ"}]}, "_npmVersion": "1.1.66", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "2.0.1": {"name": "js-yaml", "version": "2.0.1", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "scripts": {"test": "make test"}, "dependencies": {"argparse": "~ 0.1.11"}, "devDependencies": {"mocha": "*"}, "engines": {"node": ">= 0.6.0"}, "_id": "js-yaml@2.0.1", "dist": {"shasum": "f686747a6be294acadaf48ca68e7c0a647f05109", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-2.0.1.tgz", "integrity": "sha512-u2Y4pilLqc0pfqM0LAEj+yPbYs5F4Yje7QeBi4Jrr0iIpMr9UoOul21pGZ8k4WE7H+eu70NVNtGJXNfclgGmBQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDkST4ei+WuPsCWf8daJ3jGkX2/8Pwr0846j6ZrqYE7swIgREAcO5LRccaGB5GLDiuDq2UsxwCHF5XREPyKAlBoVRc="}]}, "_npmVersion": "1.1.66", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "2.0.2": {"name": "js-yaml", "version": "2.0.2", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "scripts": {"test": "make test"}, "dependencies": {"argparse": "~ 0.1.11"}, "devDependencies": {"mocha": "*"}, "engines": {"node": ">= 0.6.0"}, "_id": "js-yaml@2.0.2", "dist": {"shasum": "cbaad5a596cdebdb3c3b5e1a32a0662962961bd8", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-2.0.2.tgz", "integrity": "sha512-T6nASIzKRAW0ZsnyoSB2d0MRxYn/u01jR6EkSvunf7zHQpmpVbKd7pVNvtDlqatwIWyMmU0U48dW8VMWlE8bNA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAn8iVlRPM/q7RlIJ+3ey+SA1I24R3suC6kp7cnT3UidAiAYro4uN7JScg8NdjW+n1koktpP5O3R8uG8D1Nvtl8esw=="}]}, "_npmVersion": "1.2.0", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "2.0.3": {"name": "js-yaml", "version": "2.0.3", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "scripts": {"test": "make test"}, "dependencies": {"argparse": "~ 0.1.11"}, "devDependencies": {"mocha": "*"}, "engines": {"node": ">= 0.6.0"}, "_id": "js-yaml@2.0.3", "dist": {"shasum": "cf3d3372fd7bcc6d01b442d3dab408abf28c82e7", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-2.0.3.tgz", "integrity": "sha512-n8ql53VmyDlI8fcF4CPojScB270inBj9FQyrtOGgVUb+5seOyN7ukZRJxC3x2diE+yPJ8rKZfdMNDoQWullFSw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFIgx9fJ4JLbbMUPugySH048D3KEX1vVrFigoEXUr6lAIhANsoddTgaIrXjiEz0T9U2D6yKWw9ce1O8E7gl/Wx0b8R"}]}, "_from": "https://github.com/nodeca/js-yaml/tarball/2.0.3", "_resolved": "https://github.com/nodeca/js-yaml/tarball/2.0.3", "_npmVersion": "1.2.11", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "2.0.4": {"name": "js-yaml", "version": "2.0.4", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "scripts": {"test": "make test"}, "dependencies": {"argparse": "~ 0.1.11"}, "devDependencies": {"mocha": "*"}, "engines": {"node": ">= 0.6.0"}, "_id": "js-yaml@2.0.4", "dist": {"shasum": "e6b25fceaa2fe9ce062a4d6150079d110786153c", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-2.0.4.tgz", "integrity": "sha512-HykeVw4J+XvQLv3l6GL3pd0CkIOLStUd+lj07orMJ2Fp8VI3FqfdPeeKBjcP976mGNiefFNIWwj26jfnrWS5iw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH+uQD4WpXaNCwKRvnQwzOYkv8ToYrodNwDGLeMQ0oF3AiEA61a9vgfEOmIqO57oDQSGWvcfNBx+7vKOtF5kuW50w2k="}]}, "_from": "https://github.com/nodeca/js-yaml/tarball/2.0.4", "_resolved": "https://github.com/nodeca/js-yaml/tarball/2.0.4", "_npmVersion": "1.2.14", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "2.0.5": {"name": "js-yaml", "version": "2.0.5", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "scripts": {"test": "make test"}, "dependencies": {"argparse": "~ 0.1.11", "esprima": "~ 1.0.2"}, "devDependencies": {"mocha": "*"}, "engines": {"node": ">= 0.6.0"}, "_id": "js-yaml@2.0.5", "dist": {"shasum": "a25ae6509999e97df278c6719da11bd0687743a8", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-2.0.5.tgz", "integrity": "sha512-<PERSON><PERSON>KcIksckDBUhg2JS874xVouiPkywVUh4yyUmLCDe1Zg3bCd6M+F1eGPenPeHLc2XC8pp9G8bsuofK0NeEqRkA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICH92GYWEtYIP4pQ7ZwXFwF2Xm5GVSwtoXi6Do1LuB0JAiEA3Vuhhs6R6sdhDUBVGLXUT0Ap1rvK5kzUoqnB2Mb/O9g="}]}, "_from": "https://github.com/nodeca/js-yaml/tarball/2.0.5", "_resolved": "https://github.com/nodeca/js-yaml/tarball/2.0.5", "_npmVersion": "1.2.14", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "2.1.0": {"name": "js-yaml", "version": "2.1.0", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "scripts": {"test": "make test"}, "dependencies": {"argparse": "~ 0.1.11", "esprima": "~ 1.0.2"}, "devDependencies": {"mocha": "*"}, "engines": {"node": ">= 0.6.0"}, "_id": "js-yaml@2.1.0", "dist": {"shasum": "a55a6e4706b01d06326259a6f4bfc42e6ae38b1f", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-2.1.0.tgz", "integrity": "sha512-VEU3RaxK0f+wtexNOhfV0Kn9l7XnGb7Vwxdpx9j7fpbGqFUOms0g5mw/R8Z1PkYGXSFM18Bi4qdQLzEMV+jhVw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCMlQ1j/t1J2LeAGqy5m0UEveKpOh5ZR6JGCq31gDe8JQIgUlPhWGVIL1HCdzcsVf1v9UxYXKDrH8mv5/Js4yaUS6c="}]}, "_from": "https://github.com/nodeca/js-yaml/tarball/2.1.0", "_resolved": "https://github.com/nodeca/js-yaml/tarball/2.1.0", "_npmVersion": "1.2.18", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "2.1.1": {"name": "js-yaml", "version": "2.1.1", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "scripts": {"test": "make test"}, "dependencies": {"argparse": "~ 0.1.11", "esprima": "~ 1.0.2"}, "devDependencies": {"mocha": "*"}, "engines": {"node": ">= 0.6.0"}, "_id": "js-yaml@2.1.1", "dist": {"shasum": "574095ef2253694313a6c2b261c7b6929a9603b7", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-2.1.1.tgz", "integrity": "sha512-jAGKTTXiFazvAn5tJsMzSW299gN/nGpSGBOnoaKNcLewnqr/aRZqKuXY5gzkATPJYAGG4kvlLZh53wDUTfm51Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD/KCY9QGHPqWKcmXC/lZiIAZiZothblpazERnPSHSIugIgeOEQP8EH4XCXvSz0LKkTTs5C5A7AV/lnxywQwcpTBn0="}]}, "_from": "https://github.com/nodeca/js-yaml/tarball/2.1.1", "_resolved": "https://github.com/nodeca/js-yaml/tarball/2.1.1", "_npmVersion": "1.2.32", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "2.1.2": {"name": "js-yaml", "version": "2.1.2", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "scripts": {"test": "make test"}, "dependencies": {"argparse": "~ 0.1.11", "esprima": "~ 1.0.2"}, "devDependencies": {"mocha": "*"}, "engines": {"node": ">= 0.6.0"}, "_id": "js-yaml@2.1.2", "dist": {"shasum": "5404d58972f70112763e0b7e97ced20c39138bbd", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-2.1.2.tgz", "integrity": "sha512-ZXyAmSyjTIFBN5m4be2aPESWG2wZCqAKCRT+hMr7D3qeEKE42ALyIBBsL9vxb5AoQlIo4/XBp9xf4L9QAWj9Iw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDNXD8sxSrS/IWFyGrkEMTpkURM2HMwShn1g0meR4XJXQIhAOHCdGYcRFxh4KSXXuNvV3GhpP0sG8+6Lsn9TJ6KcLel"}]}, "_from": "https://github.com/nodeca/js-yaml/tarball/2.1.2", "_resolved": "https://github.com/nodeca/js-yaml/tarball/2.1.2", "_npmVersion": "1.3.8", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "2.1.3": {"name": "js-yaml", "version": "2.1.3", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "scripts": {"test": "make test"}, "dependencies": {"argparse": "~ 0.1.11", "esprima": "~ 1.0.2"}, "devDependencies": {"mocha": "*"}, "engines": {"node": ">= 0.6.0"}, "_id": "js-yaml@2.1.3", "dist": {"shasum": "0ffb5617be55525878063d7a16aee7fdd282e84c", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-2.1.3.tgz", "integrity": "sha512-2ElQ5tUBsI5GIjddfYGdudelD5+9JM9FfJXlrn+Mj3k72t4XrqBr3vf3+1sky0WKC3dSVhF0ZqIUpX9QFBmmfQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGl3s6GRo8ID69mp620Kxc/zyFKyvzejg9NpfG78Hx9IAiEAsUKtGVBMRMO4I4CzjO4/8nFwpA5ZUpc7xIxAmwLqiLE="}]}, "_from": "https://github.com/nodeca/js-yaml/tarball/2.1.3", "_resolved": "https://github.com/nodeca/js-yaml/tarball/2.1.3", "_npmVersion": "1.3.8", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "3.0.0": {"name": "js-yaml", "version": "3.0.0", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "scripts": {"test": "make test"}, "dependencies": {"argparse": "~ 0.1.11", "esprima": "~ 1.0.2"}, "devDependencies": {"mocha": "*"}, "engines": {"node": ">= 0.6.0"}, "_id": "js-yaml@3.0.0", "dist": {"shasum": "46a8ac74fcc6e35f2d42e08c36e2961adc76bf2d", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.0.0.tgz", "integrity": "sha512-DGd94kunMKjQaI5DbYd6lCSt5lK4ofdURChSDDWetItkeayUnrk2skiQIkDKSaKY+R06t2/MmsIMcFOFkJnxMw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCX2KE1Dp1aPvBgXKw/KK7Qoa3WKUpUxoVDTTY69rctOwIhAPrgzfHqDP3nhxE7Nm7K+pGDkmQ0He0tvkQb5vHpDrHj"}]}, "_from": "https://github.com/nodeca/js-yaml/tarball/3.0.0", "_resolved": "https://github.com/nodeca/js-yaml/tarball/3.0.0", "_npmVersion": "1.3.17", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "3.0.1": {"name": "js-yaml", "version": "3.0.1", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "scripts": {"test": "make test"}, "dependencies": {"argparse": "~ 0.1.11", "esprima": "~ 1.0.2"}, "devDependencies": {"mocha": "*"}, "engines": {"node": ">= 0.6.0"}, "_id": "js-yaml@3.0.1", "dist": {"shasum": "76405fea5bce30fc8f405d48c6dca7f0a32c6afe", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.0.1.tgz", "integrity": "sha512-dgxgLzZS9qLWAQd/wrVG8tOiRSBJq1Ss9gbd7+fdBhcia3efHjLLpPkcUhKOyVACxTEAgnao3RT1kj1otiW42g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICAIVX7zeq696YKUqu4/XLod0ehAgbmt4bzxComGD7WgAiEAwokYkoBJD13ohMAsUJF0yBvxDVaEH3WwXIljXx/wlH4="}]}, "_from": "https://github.com/nodeca/js-yaml/tarball/3.0.1", "_resolved": "https://github.com/nodeca/js-yaml/tarball/3.0.1", "_npmVersion": "1.3.14", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "3.0.2": {"name": "js-yaml", "version": "3.0.2", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "~ 0.1.11", "esprima": "~ 1.0.2"}, "devDependencies": {"mocha": "*"}, "browser": {"./index.js": "./index_browser.js", "buffer": false}, "_id": "js-yaml@3.0.2", "dist": {"shasum": "9937865f8e897a5e894e73c2c5cf2e89b32eb771", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.0.2.tgz", "integrity": "sha512-8PVwV1480dnAPMj8FCW3I/mZZ+gvtOoMAZFWJKagkFq2eWv5kcyzWTR+6MloV0SY57t6jSIaKc7DIz86K9ZCfA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDD7A1TW6fXhSndTzQQA0o1j+noNmdnUyexJngIbKMLNwIhAO+OyXgLwolR+Y27YGQj2XvMe8cmnbWmwSoTEPOHFZXa"}]}, "_from": "https://github.com/nodeca/js-yaml/tarball/3.0.2", "_resolved": "https://github.com/nodeca/js-yaml/tarball/3.0.2", "scripts": {}, "_npmVersion": "1.4.3", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}}, "3.1.0": {"name": "js-yaml", "version": "3.1.0", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "~ 0.1.11", "esprima": "~ 1.0.2"}, "devDependencies": {"ansi": "*", "benchmark": "*", "mocha": "*"}, "browser": {"buffer": false}, "_id": "js-yaml@3.1.0", "_shasum": "36ba02e618c50748e772dd352428904cbbadcf44", "_from": "https://github.com/nodeca/js-yaml/tarball/3.1.0", "_resolved": "https://github.com/nodeca/js-yaml/tarball/3.1.0", "scripts": {}, "_npmVersion": "1.4.9", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "36ba02e618c50748e772dd352428904cbbadcf44", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.1.0.tgz", "integrity": "sha512-AgPExLjC+sVh3GLVZdoTprN9oKoF911GFq0CMs8XVXPyfOfelpH9+NKGAn0NKqt9g38/5nPHqBOD8ObqhoSQ6Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF4rPIBPEYXBngYZ8EUJhW6eyYp5nciPceRsb9Sd53oEAiEAtXbDziL7RzdccTwP+s/FcsR/mnj5YOgbrlAs6E65zhA="}]}, "directories": {}}, "3.2.1": {"name": "js-yaml", "version": "3.2.1", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "~ 0.1.11", "esprima": "~ 1.0.2"}, "devDependencies": {"ansi": "*", "benchmark": "*", "mocha": "*"}, "browser": {"buffer": false}, "_id": "js-yaml@3.2.1", "_shasum": "bcec9a6c5c7f4e4e195f3cefc867eec3f83ce79d", "_from": "https://github.com/nodeca/js-yaml/tarball/3.2.1", "_resolved": "https://github.com/nodeca/js-yaml/tarball/3.2.1", "scripts": {}, "_npmVersion": "1.4.23", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "bcec9a6c5c7f4e4e195f3cefc867eec3f83ce79d", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.2.1.tgz", "integrity": "sha512-p+9m9qAJVU9QxB3JuushxB2F+Bm3YNg+qyMQpZLsP6Qa5DuMd8DcUtAs15av7vq8e0c8kkir2uFXeTXMYMT8NA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBO50FZfVAC0gfUXmiBey5pkxmyNahXX4rmqaaZAkUugAiEAiwO750qW7MYBoSqDArxCtYCShUmXyAj7xNVK1IQHMrM="}]}, "directories": {}}, "3.2.2": {"name": "js-yaml", "version": "3.2.2", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "~ 0.1.11", "esprima": "~ 1.0.2"}, "devDependencies": {"ansi": "*", "benchmark": "*", "mocha": "*"}, "browser": {"buffer": false}, "_id": "js-yaml@3.2.2", "_shasum": "a34e77fe8d5e10270e225d21d07790fa17fd2927", "_from": "https://github.com/nodeca/js-yaml/tarball/3.2.2", "_resolved": "https://github.com/nodeca/js-yaml/tarball/3.2.2", "scripts": {}, "_npmVersion": "1.4.9", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "a34e77fe8d5e10270e225d21d07790fa17fd2927", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.2.2.tgz", "integrity": "sha512-LRbGv9GHzsD+GrVBXMIVODIN96VErcjot1MltAIhDqJ/OgDn3/Kl9EIc1M91o4AtApwPqc5Whm2CPf8I4XrnVw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC8h+d70oqcvi1G3fzrqQjM48kL8yO+xgsi3rAc/mkCzAIhAJYN5URyQmiswqhl0EXXzusOr7A2WHiREu/YZOeTIf+L"}]}, "directories": {}}, "3.2.3": {"name": "js-yaml", "version": "3.2.3", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "~ 0.1.11", "esprima": "~ 1.0.2"}, "devDependencies": {"ansi": "*", "benchmark": "*", "mocha": "*"}, "browser": {"buffer": false}, "gitHead": "a6cd5657603496c7128d3b9fa621ce9274283194", "_id": "js-yaml@3.2.3", "scripts": {}, "_shasum": "a3af632d13df5bfa95f3b8f3c4b61efe212cd750", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "a3af632d13df5bfa95f3b8f3c4b61efe212cd750", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.2.3.tgz", "integrity": "sha512-G9ihATodIS5+k6bLErXh4GoN39evxvV9/0EC+zdbFr5Qh5oQrY8isLecur40OCUJf82G+pIthO8BgHDWQzazhQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCBjnl4yKU5Cq5IZi2m7iVhycrYTfQpmjxzTZmh6uZ0fQIhANa2HkHy2NQ2v1ZPZbPfMP/GV3I/isjW1CF36QLlFppy"}]}, "directories": {}}, "3.2.4": {"name": "js-yaml", "version": "3.2.4", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "~ 0.1.11", "esprima": "~ 1.0.2"}, "devDependencies": {"ansi": "*", "benchmark": "*", "mocha": "*"}, "browser": {"buffer": false}, "gitHead": "19bac7893be01d1f9c3e1263f43bd6c8a47abf90", "_id": "js-yaml@3.2.4", "scripts": {}, "_shasum": "f2cfb5f5e1d251ff438f41d63139750001021083", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "f2cfb5f5e1d251ff438f41d63139750001021083", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.2.4.tgz", "integrity": "sha512-Q0QJS1aaefIR0fqvXzwgjUkZHrsI+suzJSoITwuDFumN0zWTIw3KEx7XC1SZeXi/H/PRaiWf40nWWDwIuzArbw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFzpR3OHMbUANwQluoh3V9Lk6jqckGI2eL7UX6hg/5/MAiAjfMHG+mn+kZ1P72YBn6dBJbncUQT24XdTR2zP4lI5ug=="}]}, "directories": {}}, "3.2.5": {"name": "js-yaml", "version": "3.2.5", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "~ 0.1.11", "esprima": "~ 1.0.2"}, "devDependencies": {"ansi": "*", "benchmark": "*", "mocha": "*"}, "browser": {"buffer": false}, "gitHead": "8a73181030e075b208c8f4065a4888a786365ae2", "_id": "js-yaml@3.2.5", "scripts": {}, "_shasum": "c29ee9a9e503e7ab83f071ccacdd0dac25ff9e22", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "c29ee9a9e503e7ab83f071ccacdd0dac25ff9e22", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.2.5.tgz", "integrity": "sha512-IncH16MRjTl3hbDHNJSXajdxcalBjlxHfWONqPlUv99Do2HkNa5VcD1JsVSdm7OPbhANfsE4AXpSi95uKlrdWQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFRO5VRAlC2jXvQW25j0Azy7TIhzWqx+hRgMoc+q3SOhAiAxq2PKPT5BqMEuvZ6RuVTj8VWTE3kvVyTBGnp7c7RQ7g=="}]}, "directories": {}}, "3.2.6": {"name": "js-yaml", "version": "3.2.6", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "~ 0.1.11", "esprima": "~ 1.0.2"}, "devDependencies": {"ansi": "*", "benchmark": "*", "mocha": "*"}, "browser": {"buffer": false}, "gitHead": "9a7ae34da906df48e07e9eeb956f53d6aaab0b0b", "_id": "js-yaml@3.2.6", "scripts": {}, "_shasum": "dde1ffbe2726e3fff97efb65fd02dbd6647b8309", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "dde1ffbe2726e3fff97efb65fd02dbd6647b8309", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.2.6.tgz", "integrity": "sha512-Q9wQqWydmmMKixbYdB2a6Oc7zgQI0lIdqTXGSzZ06ixl6DaITVoKKtYKrXU5z9MSJFFaDLVCI8PShJ+xAA4pTQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICIXz2G7ev/tHJzv3R9KM4AH6kdvwhQmTzYzflinxcX3AiEAhJ1rq9js8it1F8GiSwf/b8TeS2G+KOHjsIfeNaTZCXU="}]}, "directories": {}}, "3.2.7": {"name": "js-yaml", "version": "3.2.7", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "~ 1.0.0", "esprima": "~ 2.0.0"}, "devDependencies": {"ansi": "*", "benchmark": "*", "mocha": "*"}, "browser": {"buffer": false}, "gitHead": "2321c0b9d1e25f9b8e6202744bc601a4998fe27f", "_id": "js-yaml@3.2.7", "scripts": {}, "_shasum": "102790f265d986fe95a4d0f2a792e7a7bd886eec", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "102790f265d986fe95a4d0f2a792e7a7bd886eec", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.2.7.tgz", "integrity": "sha512-mV4Mf9iZluwNTw5dU93TJoQFTes57+KAQi+70G9XKChV7uBi3u+fd7+JSa2fQRGfii+YVSMunxgFtD29PLpj6g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAfYINzJVJMxyQgm7Pp7inDtWvWrxsWe0cHVtGjYNag4AiBnjubTqh9u2ZIHsKqe9y1WnDns0Pe4FG/iL+Cfp6ZJvg=="}]}, "directories": {}}, "3.3.0": {"name": "js-yaml", "version": "3.3.0", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "~1.0.2", "esprima": "~2.2.0"}, "devDependencies": {"ansi": "*", "benchmark": "*", "eslint": "0.18.0", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "*", "mocha": "*"}, "browser": {"buffer": false}, "scripts": {"test": "make test"}, "gitHead": "9cc570a50a767b08c3b0fcd0e61efec880aa7c11", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.3.0", "_shasum": "cb6422d39168dbde419fecb7fd06fbe27ad56210", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "cb6422d39168dbde419fecb7fd06fbe27ad56210", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.3.0.tgz", "integrity": "sha512-9gVU+tDOxypkaoPtktV/OESSH1kG0JLtUTAxSezghF/25ut1ofZL+oZFM/Mk2UiBpbOZ4UmFttWWt2Okb11lOQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEhsUzdfLKuN049LnPXy7rbj8DVBUK3BVeJg+di53ASWAiEA6NxSOLGAiquxWMv9ICKkogeWtJt/5qoTmMQPMgrRlMU="}]}, "directories": {}}, "3.3.1": {"name": "js-yaml", "version": "3.3.1", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "~1.0.2", "esprima": "~2.2.0"}, "devDependencies": {"ansi": "*", "benchmark": "*", "eslint": "0.18.0", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "*", "mocha": "*"}, "browser": {"buffer": false}, "scripts": {"test": "make test"}, "gitHead": "c50f9936bd1e99d64a54d30400e377f4fda401c5", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.3.1", "_shasum": "ca1acd3423ec275d12140a7bab51db015ba0b3c0", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "ca1acd3423ec275d12140a7bab51db015ba0b3c0", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.3.1.tgz", "integrity": "sha512-fjgnhG5HijmXtlBCMcU0JyCq/VA8YtdBwFNIBDiu3hpJ5Zsvs7993VC0XNNSiKoM8g399vY+21yqzT/TRS3KMQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBovbnJDoFbrR3al5y7yo0wcmQnbRZTAaJy54j5cdhPoAiEApLL51eFT2O0rp4knm5rLY5gpMYfBkSKYp9VJXryNmZw="}]}, "directories": {}}, "3.4.0": {"name": "js-yaml", "version": "3.4.0", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "~1.0.2", "esprima": "~2.2.0"}, "devDependencies": {"ansi": "*", "benchmark": "*", "eslint": "0.24.1", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "*", "mocha": "*"}, "browser": {"buffer": false}, "scripts": {"test": "make test"}, "gitHead": "da3d1fa05a1e8087ecad25d53f91a8419cf041d1", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.4.0", "_shasum": "63498041a26425d31c3d662ec6f5d0a25c1c7d9b", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "63498041a26425d31c3d662ec6f5d0a25c1c7d9b", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.4.0.tgz", "integrity": "sha512-w6c+CGyW0pEzKGtphho+XfLaNutY1V/KLFuuN6VEld5rXzQgTqtTy0YNXO4gkZYSrLPTv/U02WNwR7OINJSs4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDmCpebTYW1RWnTMUfGCQwgClvx/1WjrB5VZQrmW/muHAIgYq/ajwAuWvOPSSI0OfRcssbGMN4pBqJkzgFhGK3rSE4="}]}, "directories": {}}, "3.4.1": {"name": "js-yaml", "version": "3.4.1", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "~1.0.2", "esprima": "~2.2.0"}, "devDependencies": {"ansi": "*", "benchmark": "*", "eslint": "0.24.1", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "*", "mocha": "*"}, "browser": {"buffer": false}, "scripts": {"test": "make test"}, "gitHead": "0215138ec880c1e0ebc93f5a8c94bb753f5a5456", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.4.1", "_shasum": "7183990c62f646369eaa04675b2d5f1e71d62b8b", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "7183990c62f646369eaa04675b2d5f1e71d62b8b", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.4.1.tgz", "integrity": "sha512-jJ2AOydAfFTPVj7L9Hz1cZl+tg5LNoz4JXrsjH5Gl4e0lr1HShxG0U59IH7CccvbHDgqE9WY0IbTYs/qIrEKVA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICJdLT1alqC7UqxtVm9DyQXz08gpBZuQlgUWuiG1rxfAAiALuTA+Zk/P7tiJ3KDavkGs1n+raDaOjXLNdVMKrJ48LQ=="}]}, "directories": {}}, "3.4.2": {"name": "js-yaml", "version": "3.4.2", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "~1.0.2", "esprima": "~2.2.0"}, "devDependencies": {"ansi": "*", "benchmark": "*", "eslint": "0.24.1", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "*", "mocha": "*"}, "browser": {"buffer": false}, "scripts": {"test": "make test"}, "gitHead": "cab39bb1065959e44f8fd48ad9ff637d1baf72c1", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.4.2", "_shasum": "10942383ee4b9c2c20ede2388c1b0f3878a2b0ce", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "10942383ee4b9c2c20ede2388c1b0f3878a2b0ce", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.4.2.tgz", "integrity": "sha512-JoYL6fywI4XwrGnnbbIZR+UFSRxHMK+EpiX2lHAdq/RcEML0uX0Rfu+XdMrG7Q/XQ084OjR65qPfzQ4x3ubo9A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC4QRw0RcRcYld8zToAS/G14OGOwdePfVwAPJBPRCDIswIhAJCTuFc+LRmSN4bGTUhqaDUPSkfJOtzUlRQDkLzw5UaU"}]}, "directories": {}}, "3.4.3": {"name": "js-yaml", "version": "3.4.3", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.2", "esprima": "^2.6.0"}, "devDependencies": {"ansi": "*", "benchmark": "*", "eslint": "0.24.1", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "*", "mocha": "*"}, "browser": {"buffer": false}, "scripts": {"test": "make test"}, "gitHead": "13e619cb93e100b9edd1d87f9a2cb1e805a3b2c7", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.4.3", "_shasum": "98c3a3f06bdac9dfc270fd91cec9d943e364cecd", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "0.12.4", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "98c3a3f06bdac9dfc270fd91cec9d943e364cecd", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.4.3.tgz", "integrity": "sha512-x4x00j/71mI2/g/dm6DSs3tnPwkJBNae8DAKAjHZ+ywVwsgElxWzXS66ltRSYORwCeTgiicxflaaL2MrALjH7Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDphImL4LXX80baGVguB6Q8tuOewQhkHKaRliBueJ3otAiB3YpNCKE1AuExoluprz08sEQhQ8/57FVLu6eKF4/x5zA=="}]}, "directories": {}}, "3.4.4": {"name": "js-yaml", "version": "3.4.4", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.2", "esprima": "^2.6.0"}, "devDependencies": {"ansi": "*", "benchmark": "*", "eslint": "0.24.1", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "*", "mocha": "*"}, "browser": {"buffer": false}, "scripts": {"test": "make test"}, "gitHead": "54e4dc30cc5fbfc21f291cace630a2f32d3114d9", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.4.4", "_shasum": "33ac457edeb78040fc1490f57eb3e7e9fe57cee5", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.2", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "33ac457edeb78040fc1490f57eb3e7e9fe57cee5", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.4.4.tgz", "integrity": "sha512-siO1i88y1mp+k7+upa+hYXNw0PWejujxG2qxoCIGxjMSbIVZrw1UA0NYVyJ05qKr46ia9zL6Oi82AVPHb5koKQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGjL8PyxipXkiPPBQBav68serHtztjhHnDeXBk31hWVqAiAX4e3GEJRlhDBQsvC0p1idEqd12ZUqEGtI9PG+LLhexw=="}]}, "directories": {}}, "3.4.5": {"name": "js-yaml", "version": "3.4.5", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.2", "esprima": "^2.6.0"}, "devDependencies": {"ansi": "*", "benchmark": "*", "eslint": "0.24.1", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "*", "mocha": "*"}, "browser": {"buffer": false}, "scripts": {"test": "make test"}, "gitHead": "66035322ee0906f0bcb24700bd7332ce66726c32", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.4.5", "_shasum": "c3403797df12b91866574f2de23646fe8cafb44d", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.2", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "c3403797df12b91866574f2de23646fe8cafb44d", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.4.5.tgz", "integrity": "sha512-dGw10UlZWZ7GlJzjvnS754Z9JPePU2ZciIOTcidpSdvWsU4Ct5RcFlUwRSI4JVqIpd/eGow9puxv2onTtUeA5Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHc4XrW/9QULEVy62Vv691L7wgy0B5y00bk+kYEL+dmAAiEA+x0eeUz82IYxqm7UBCRtxKtwSyTfPhNqdBPuttcWsLA="}]}, "directories": {}}, "3.4.6": {"name": "js-yaml", "version": "3.4.6", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.2", "esprima": "^2.6.0", "inherit": "^2.2.2"}, "devDependencies": {"ansi": "*", "benchmark": "*", "eslint": "0.24.1", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "*", "mocha": "*"}, "browser": {"buffer": false}, "scripts": {"test": "make test"}, "gitHead": "38f9b2e82e80a9a540a264ab8920344165a2018b", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.4.6", "_shasum": "6be1b23f6249f53d293370fd4d1aaa63ce1b4eb0", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.2", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "6be1b23f6249f53d293370fd4d1aaa63ce1b4eb0", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.4.6.tgz", "integrity": "sha512-k9JCHDD9Hefc/S1BhsZTHO93mgZ6Z6VrGrZnGLf1pm1KAFhKjERum6v/4Exk8q9MkQyTRKIqlF+IM0BAPxLduw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHuUECwO/CU07s08xmtXTIGlpa8Zo+2SldmsaOoEfsBqAiBmtfCc2IQ+99k+b9Hu307ie4hULvhWPU+9l20zBJlyZQ=="}]}, "directories": {}}, "3.5.0": {"name": "js-yaml", "version": "3.5.0", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.2", "esprima": "^2.6.0", "inherit": "^2.2.2"}, "devDependencies": {"ansi": "*", "benchmark": "*", "browserify": "^13.0.0", "eslint": "0.24.1", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "*", "mocha": "*", "uglify-js": "^2.6.1"}, "browser": {"buffer": false, "esprima": false}, "scripts": {"test": "make test"}, "gitHead": "4ad1e777f354e108d320afe85aea554bf3309e1e", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.5.0", "_shasum": "617f504af336e98c219a8a6eb7d67ab1b7072fcc", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "617f504af336e98c219a8a6eb7d67ab1b7072fcc", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.5.0.tgz", "integrity": "sha512-zGmrRla6y/pMr3H2oU1EFi8MyYCXNUQ9PjKVA/RFql3KEsCMJlNnJXKzLJqyUNjmkyb694c6QhA8sgka30188w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEBe4lurEGQYC9UueW3Z74G63p1xMveHyNRcF6uwsRzaAiBijgUSvBlab6u6DaLrSaADubhPEm5raaZFa+fjQdBaNw=="}]}, "directories": {}}, "3.5.1": {"name": "js-yaml", "version": "3.5.1", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.2", "esprima": "^2.6.0"}, "devDependencies": {"ansi": "*", "benchmark": "*", "browserify": "^13.0.0", "codemirror": "^5.10.0", "eslint": "0.24.1", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "*", "mocha": "*", "uglify-js": "^2.6.1"}, "browser": {"buffer": false}, "scripts": {"test": "make test"}, "gitHead": "8f787fac082198450db5f79e4e92714423cc8478", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.5.1", "_shasum": "dadd0939be1956726051085d259973f301fcdb31", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "dadd0939be1956726051085d259973f301fcdb31", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.5.1.tgz", "integrity": "sha512-zEhizyMh84M98+DCY8DmHzf/PwMNBeO9dyxv35WozPjKNdn4igwvGqYVvWcz+LaoUOsJmoDyUa1VxqUMxbTzYQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC5CunZzxoO5sRodaUcdp/onOKVm2yNsm2X4YD+Q67F/wIhAIFnssk8d5ep8nbT3vkLWQREtldgjEeYMzpyIJu54x32"}]}, "directories": {}}, "3.5.2": {"name": "js-yaml", "version": "3.5.2", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.2", "esprima": "^2.6.0"}, "devDependencies": {"ansi": "*", "benchmark": "*", "browserify": "^13.0.0", "codemirror": "^5.10.0", "eslint": "0.24.1", "eslint-plugin-nodeca": "^1.0.3", "istanbul": "*", "mocha": "*", "uglify-js": "^2.6.1"}, "browser": {"buffer": false}, "scripts": {"test": "make test"}, "gitHead": "4e7b17612d3dd9e20880a8e959b4ae7c2d68209a", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.5.2", "_shasum": "46a76fefeec9ec66cb4e71faef07118c6bf246e9", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "46a76fefeec9ec66cb4e71faef07118c6bf246e9", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.5.2.tgz", "integrity": "sha512-kzsWNFWgM4at+cOVrr3VubR/FtFHUy0SBmNaYhS/FspPL0XYAAxVsAYAGhOjMbo+F2R96SLTpaNu1I61oDBbLw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICWagiMIf6bc61HyWPv0kRFUxK8dcwutus5Gk1KnE4d2AiEAlMHFJ8ullsmk0Eg9J7Al+xogtjFcrywjQaMKGDlwKP4="}]}, "directories": {}}, "3.5.3": {"name": "js-yaml", "version": "3.5.3", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.2", "esprima": "^2.6.0"}, "devDependencies": {"ansi": "*", "benchmark": "*", "browserify": "^13.0.0", "codemirror": "^5.10.0", "eslint": "^2.0.0-rc.1", "istanbul": "*", "mocha": "*", "uglify-js": "^2.6.1"}, "browser": {"buffer": false}, "scripts": {"test": "make test"}, "gitHead": "4a9e0a258586cff292cab63e0b76044fc1f4cb63", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.5.3", "_shasum": "e9ee6082b0657770e4346dfaf2a58c5992251f76", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.3.0", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "e9ee6082b0657770e4346dfaf2a58c5992251f76", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.5.3.tgz", "integrity": "sha512-rkxjJUwevxyYOdr45k3IOyZjyhRbEMqUvcMyXXeBXTf8kdFaFKUIvMkdHgIcFIjNIoctM6l/emA7OjXYVabYSw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG/ohNXHnqkVgOQoCrSgP2KCB+rx3vSQ0lm7u4d4F8oCAiBE6iZ5LgFM8avfHwlzdVbq0bd1f0Az4ELsiYTeLI+lwA=="}]}, "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/js-yaml-3.5.3.tgz_1455175645225_0.033302413299679756"}, "directories": {}}, "3.5.4": {"name": "js-yaml", "version": "3.5.4", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.2", "esprima": "^2.6.0"}, "devDependencies": {"ansi": "*", "benchmark": "*", "browserify": "^13.0.0", "codemirror": "^5.10.0", "eslint": "^2.0.0-rc.1", "istanbul": "*", "mocha": "*", "uglify-js": "^2.6.1"}, "browser": {"buffer": false}, "scripts": {"test": "make test"}, "gitHead": "5f058c3a78fd1a1bd36fd2cdc2c3a8c45ab53fd1", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.5.4", "_shasum": "f64f16dcd78beb9ce8361068e733ebe47b079179", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.3.0", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "f64f16dcd78beb9ce8361068e733ebe47b079179", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.5.4.tgz", "integrity": "sha512-XBGnYi4i/XkEeSclkcRmBKOR9t7SLViumIhaIqwHSkO7FU0F3xW/sqAQ1/y+AcYX2EcnwdKFY1nXWzjJHqBRoA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBKJPDJSfqKVS8UNK9RmtVv/0bOJBCK+7VY0d3nB4mDkAiAYJYndjbiWMjSILfholwOnIsmXkiC3GGrJX8oGX9H89g=="}]}, "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/js-yaml-3.5.4.tgz_1457547930676_0.2546668383292854"}, "directories": {}}, "3.5.5": {"name": "js-yaml", "version": "3.5.5", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.2", "esprima": "^2.6.0"}, "devDependencies": {"ansi": "*", "benchmark": "*", "browserify": "^13.0.0", "codemirror": "^5.10.0", "eslint": "^2.0.0-rc.1", "istanbul": "*", "mocha": "*", "uglify-js": "^2.6.1"}, "browser": {"buffer": false}, "scripts": {"test": "make test"}, "gitHead": "fdd205e5a1175a62868c0efb28a77493e5e34e3f", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.5.5", "_shasum": "0377c38017cabc7322b0d1fbcd25a491641f2fbe", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.3.0", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "0377c38017cabc7322b0d1fbcd25a491641f2fbe", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.5.5.tgz", "integrity": "sha512-epxjSConucMdujAtGaMmQ5PG+9562qhRQHmbHy29hoq06f5Ah9K5ez/b5nkYUyVrlGzGkxHma0NLIBKW0Zr9hw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCW+ZbC2YYdl3P/UlRJ1KlQEtvuGOWjKkzmukogThNIOgIgGjrKafq40UU1YSVYNdFx8e4qV7pAg+Srz8muKmDV0OY="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/js-yaml-3.5.5.tgz_1458226126775_0.5821511475369334"}, "directories": {}}, "3.6.0": {"name": "js-yaml", "version": "3.6.0", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.7", "esprima": "^2.6.0"}, "devDependencies": {"ansi": "*", "benchmark": "*", "browserify": "^13.0.0", "codemirror": "^5.13.4", "eslint": "^2.8.0", "istanbul": "*", "mocha": "*", "uglify-js": "^2.6.1"}, "scripts": {"test": "make test"}, "gitHead": "87e4cdc21deec3dda9b811366fb3a3af16c08f0d", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.6.0", "_shasum": "3b7bf3256dd598f60f8b6f8ea75514a585a24dc6", "_from": ".", "_npmVersion": "2.14.20", "_nodeVersion": "4.4.1", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "3b7bf3256dd598f60f8b6f8ea75514a585a24dc6", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.6.0.tgz", "integrity": "sha512-xDGO1N1cjMchRguXu75T9SdMt+e9ADVFdbFKbPbRRNnc+IslxCucwR+7u+UhC11UI3N/MoAtaKdFoWhBwnFpSw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+zraLpxOzzs2dDiL09ZoXfEgxe/KwhX9jbTAQY/X2fAIhAOm7mwbG6t3grLEn1tv1cCIcdlbs12CcAScKUrhVdj1D"}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/js-yaml-3.6.0.tgz_1460755132000_0.1514641239773482"}, "directories": {}}, "3.6.1": {"name": "js-yaml", "version": "3.6.1", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.7", "esprima": "^2.6.0"}, "devDependencies": {"ansi": "*", "benchmark": "*", "browserify": "^13.0.0", "codemirror": "^5.13.4", "eslint": "^2.8.0", "istanbul": "*", "mocha": "*", "uglify-js": "^2.6.1"}, "scripts": {"test": "make test"}, "gitHead": "c76b837cacc69de6b86a0781db31a9bb7a193875", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.6.1", "_shasum": "6e5fe67d8b205ce4d22fad05b7781e8dadcc4b30", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "6e5fe67d8b205ce4d22fad05b7781e8dadcc4b30", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.6.1.tgz", "integrity": "sha512-BLv3oxhfET+w5fjPwq3PsAsxzi9i3qzU//HMpWVz0A6KplF86HdR9x2TGnv9DXhSUrO7LO8czUiTd3yb3mLSvg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEg69cc1zTSxTABar1nw2KivgXWfnjxNObJQJH0jMlrvAiBym2JNc5Ug7thCGStq9C3SobAI41o8fMqt8NY8xULe3A=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/js-yaml-3.6.1.tgz_1462995636349_0.2209061929024756"}, "directories": {}}, "3.7.0": {"name": "js-yaml", "version": "3.7.0", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.7", "esprima": "^2.6.0"}, "devDependencies": {"ansi": "*", "benchmark": "*", "browserify": "^13.0.0", "codemirror": "^5.13.4", "eslint": "^2.8.0", "istanbul": "*", "mocha": "*", "uglify-js": "^2.6.1"}, "scripts": {"test": "make test"}, "gitHead": "279655fa5ad46d17117f60680fa3b46a0b5391c5", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.7.0", "_shasum": "5c967ddd837a9bfdca5f2de84253abe8a1c03b80", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.8.1", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "5c967ddd837a9bfdca5f2de84253abe8a1c03b80", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.7.0.tgz", "integrity": "sha512-eIlkGty7HGmntbV6P/ZlAsoncFLGsNoM27lkTzS+oneY/EiNhj+geqD9ezg/ip+SW6Var0BJU2JtV0vEUZpWVQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAw1qhPvOoSBULUpFrGFwLEYBx+zzy9E5csMtQIAmfk6AiAWhfXfTMlN2zKXix2xTbQJcrytcvm+LgeCPlXLYq7OvQ=="}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/js-yaml-3.7.0.tgz_1478914323559_0.38230896391905844"}, "directories": {}}, "3.8.0": {"name": "js-yaml", "version": "3.8.0", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.7", "esprima": "^3.1.1"}, "devDependencies": {"ansi": "*", "benchmark": "*", "browserify": "^13.0.0", "codemirror": "^5.13.4", "eslint": "^3.10.0", "istanbul": "*", "mocha": "*", "uglify-js": "^2.6.1"}, "scripts": {"test": "make test"}, "gitHead": "dddcb5f15fa77a8f9a4d50ccbeb969297e980aa4", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.8.0", "_shasum": "f63620d2ee6763dcf95078bf7bdbaa01d8db4e73", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.4", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "f63620d2ee6763dcf95078bf7bdbaa01d8db4e73", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.8.0.tgz", "integrity": "sha512-YWtrNUztpR1dafMJNk+iFX3ay/Ygs6nDbaPov05wfo6IWnY9M5Ma4kyZd99Ga5qmh6hHB0jKeKmYcxL1y4YniQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHgXAh/JFmBkIs9kHHDboz4vdW+MjwwKxUXLj9zgRIaHAiB1QergKFnZnm9HJM/wNgQygU3DXaHDPYPOoBFYE5TcJg=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/js-yaml-3.8.0.tgz_1486427493008_0.31876423419453204"}, "directories": {}}, "3.8.1": {"name": "js-yaml", "version": "3.8.1", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.7", "esprima": "^3.1.1"}, "devDependencies": {"ansi": "*", "benchmark": "*", "browserify": "^13.0.0", "codemirror": "^5.13.4", "eslint": "^3.10.0", "istanbul": "*", "mocha": "*", "uglify-js": "^2.6.1"}, "scripts": {"test": "make test"}, "gitHead": "9c1894e2c4e2a14efaa0ba4afe71804bebfb77d6", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.8.1", "_shasum": "782ba50200be7b9e5a8537001b7804db3ad02628", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.4", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "782ba50200be7b9e5a8537001b7804db3ad02628", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.8.1.tgz", "integrity": "sha512-oSFg1lsOgbJH+3LAZixNuWmPQnq/lLMQndRyM+mMixlSKGqOPH02fhICErNmdt/EMjs5E7QHpZI4BJ5PpSLfug==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDu+Ds1h+aEmDKDFRQcqrGnBNpPMJ15QDUSwVhNhgsLcAiBk13gbm+LlDNXE6Htbf5xDq4iIA/ukcWskVK3XZ8IHlw=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/js-yaml-3.8.1.tgz_1486427973137_0.02093760436400771"}, "directories": {}}, "3.8.2": {"name": "js-yaml", "version": "3.8.2", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.7", "esprima": "^3.1.1"}, "devDependencies": {"ansi": "*", "benchmark": "*", "browserify": "^13.0.0", "codemirror": "^5.13.4", "eslint": "^3.10.0", "istanbul": "*", "mocha": "*", "uglify-js": "^2.6.1"}, "scripts": {"test": "make test"}, "gitHead": "7dd7254139804a0cd15683fd9e61ad949bd5ce14", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.8.2", "_shasum": "02d3e2c0f6beab20248d412c352203827d786721", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.9.4", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "02d3e2c0f6beab20248d412c352203827d786721", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.8.2.tgz", "integrity": "sha512-9I4FTrWjv8PQbXPB9VfD2Ls3rrZS1JT0+ItFbyl0DNZV5nFBduBtugZr497pWmu5BgYrugScShqcQySGAipNag==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHm8OuzDA/ihw/3dsBkyrTcIA4I9quiwvsuYzrhAKJ8/AiBYk4GcG6JSExT9+k+TrFvsBx/By91fDt1wh9k2arGkXA=="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/js-yaml-3.8.2.tgz_1488473380123_0.05922025511972606"}, "directories": {}}, "3.8.3": {"name": "js-yaml", "version": "3.8.3", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.7", "esprima": "^3.1.1"}, "devDependencies": {"ansi": "*", "benchmark": "*", "browserify": "^13.0.0", "codemirror": "^5.13.4", "eslint": "^3.10.0", "istanbul": "*", "mocha": "*", "uglify-js": "^2.6.1"}, "scripts": {"test": "make test"}, "gitHead": "db20313c9c432766d0606aa1d9659b4f96b9bf5b", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.8.3", "_shasum": "33a05ec481c850c8875929166fe1beb61c728766", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.8.0", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "33a05ec481c850c8875929166fe1beb61c728766", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.8.3.tgz", "integrity": "sha512-Bo9Ovd9uC8ETeDKULNfUX69iKYxj0hysFx0iagESUuPovPSWjdfcaLBQ9cFZANMhH3Tenc0tAVZ43j2Sg/qUhQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC1sPfCAsmthx2jEMpatWriSeMMoRCsJHItfvStTQF2ewIhAK2F2q9woI+LIKLxfxBRkZZHtY6owtqmtlRTmGBMkskb"}]}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/js-yaml-3.8.3.tgz_1491396461695_0.8139190883375704"}, "directories": {}}, "3.8.4": {"name": "js-yaml", "version": "3.8.4", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.7", "esprima": "^3.1.1"}, "devDependencies": {"ansi": "^0.3.1", "benchmark": "^2.1.4", "browserify": "^14.3.0", "codemirror": "^5.13.4", "eslint": "^3.10.0", "istanbul": "^0.4.5", "mocha": "^3.3.0", "uglify-js": "^3.0.1"}, "scripts": {"test": "make test"}, "gitHead": "3de650d4d6fd9e24052ffa7ae53439e36eb70cbb", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.8.4", "_shasum": "520b4564f86573ba96662af85a8cafa7b4b5a6f6", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.8.0", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"shasum": "520b4564f86573ba96662af85a8cafa7b4b5a6f6", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.8.4.tgz", "integrity": "sha512-bgjcVwQFrFX7lpj97N1cLRCEUrXKdRqLWwvoKVFep3Qg5RAuYw78NeThxDekWvmuE1tg+0Ke49RshU1ZcXCHmA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHtURj3jY6MXClzANRpd+HuMGKg35XuJgsctHElIoFSgIgREtYem8RU5EGOXmV1M9MNmkDVNkIUf5WTrtFENc8lrw="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/js-yaml-3.8.4.tgz_1494256586834_0.47078769421204925"}, "directories": {}}, "3.9.0": {"name": "js-yaml", "version": "3.9.0", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "devDependencies": {"ansi": "^0.3.1", "benchmark": "^2.1.4", "browserify": "^14.3.0", "codemirror": "^5.13.4", "eslint": "^4.1.1", "istanbul": "^0.4.5", "mocha": "^3.3.0", "uglify-js": "^3.0.1"}, "scripts": {"test": "make test"}, "gitHead": "d411eddbb7a340c89dd0557e277ce31463515c04", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.9.0", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.3", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-0LoUNELX4S+iofCT8f4uEHIiRBR+c2AINyC8qRWfC6QNruLtxVZRJaPcu/xwMgFIgDxF25tGHaDjvxzJCNE9yw==", "shasum": "4ffbbf25c2ac963b8299dc74da7e3740de1c18ce", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.9.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJ06zG1ntSzKZOow8oNiYKr/wYoyPrYQCa2eYnLc092wIgMEZE1w3hxq1bzEodnr97eAhfQRmYNjN2b1Wo4zSv1Mw="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/js-yaml-3.9.0.tgz_1499503855491_0.8773747310042381"}, "directories": {}}, "3.9.1": {"name": "js-yaml", "version": "3.9.1", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "devDependencies": {"ansi": "^0.3.1", "benchmark": "^2.1.4", "browserify": "^14.3.0", "codemirror": "^5.13.4", "eslint": "^4.1.1", "istanbul": "^0.4.5", "mocha": "^3.3.0", "uglify-js": "^3.0.1"}, "scripts": {"test": "make test"}, "gitHead": "82945a758526197ba023ed299d5b537823231002", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.9.1", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.4", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-CbcG379L1e+mWBnLvHWWeLs8GyV/EMw862uLI3c+GxVyDHWZcjZinwuBd3iW2pgxgIlksW/1vNJa4to+RvDOww==", "shasum": "08775cebdfdd359209f0d2acd383c8f86a6904a0", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.9.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDRHk0BeEYjmrkTzLUtdkjZC176zgQ2NT0Kl4itX8afcgIhAKCJG6Ku4ruxu2tnGtdR8BcSLYb6FuEFs90RAbX8GhL/"}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/js-yaml-3.9.1.tgz_1501400037979_0.9373135957866907"}, "directories": {}}, "3.10.0": {"name": "js-yaml", "version": "3.10.0", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "devDependencies": {"ansi": "^0.3.1", "benchmark": "^2.1.4", "browserify": "^14.3.0", "codemirror": "^5.13.4", "eslint": "^4.1.1", "istanbul": "^0.4.5", "mocha": "^3.3.0", "uglify-js": "^3.0.1"}, "scripts": {"test": "make test"}, "gitHead": "42e7fcccf10c74fe157b1f8eb9d73c1c25c3c259", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.10.0", "_npmVersion": "5.3.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-O2v52ffjLa9VeM43J4XocZE//WT9N0IiwDa3KSHH7Tu8CtH+1qM8SIZvnsTh6v+4yFy5KUY3BHUVwjpfAWsjIA==", "shasum": "2e78441646bd4682e963f22b6e92823c309c62dc", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.10.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAPvLYrnioCEHee+7eUmo0CIZHgqD/Wrn/Cnbhca9UAkAiB8Y0HAFhJWX5RaNvhyMh0tUUS0fg9dVk6iJVXyAOh+Jg=="}]}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/js-yaml-3.10.0.tgz_1505117951321_0.05147904600016773"}, "directories": {}}, "3.11.0": {"name": "js-yaml", "version": "3.11.0", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "devDependencies": {"ansi": "^0.3.1", "benchmark": "^2.1.4", "browserify": "^14.3.0", "codemirror": "^5.13.4", "eslint": "^4.1.1", "istanbul": "^0.4.5", "mocha": "^3.3.0", "uglify-js": "^3.0.1"}, "scripts": {"test": "make test"}, "gitHead": "f2bb20759284e1af69aeea0fe11daf5f96a33e03", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.11.0", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-saJstZWv7oNeOyBh3+Dx1qWzhW0+e6/8eDzo7p5rDFqxntSztloLtuKu+Ejhtq82jsilwOIZYsCz+lIjthg1Hw==", "shasum": "597c1a8bd57152f26d622ce4117851a51f5ebaef", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.11.0.tgz", "fileCount": 37, "unpackedSize": 278411, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGZnLTPGPHOrXuncaqt90aQzLylKEjbpzCqeaUCv5luIAiEA1kjUKXZ8w7XfG29HbwY/0ieRRsDmPsBn22wVyCLcCQ8="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/js-yaml_3.11.0_1520265172253_0.8233823763210271"}, "_hasShrinkwrap": false}, "3.12.0": {"name": "js-yaml", "version": "3.12.0", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "files": ["index.js", "lib/", "bin/", "dist/"], "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "devDependencies": {"ansi": "^0.3.1", "benchmark": "^2.1.4", "browserify": "^16.2.2", "codemirror": "^5.13.4", "eslint": "^4.1.1", "fast-check": "1.1.3", "istanbul": "^0.4.5", "mocha": "^5.2.0", "uglify-js": "^3.0.1"}, "scripts": {"test": "make test"}, "gitHead": "5cdad9bd27ad97627b21f0111ca3f125fe618acd", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.12.0", "_npmVersion": "6.0.1", "_nodeVersion": "10.1.0", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-PIt2cnwmPfL4hKNwqeiuz4bKfnzHTBv6HyVgjahA6mPLwPDzjDWrplJBMjHUFxku/N3FlmrbyPclad+I+4mJ3A==", "shasum": "eaed656ec8344f10f527c6bfa1b6e2244de167d1", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.12.0.tgz", "fileCount": 37, "unpackedSize": 279585, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEc07CRA9TVsSAnZWagAA9KYP/jJct6c+A8un8iYGF5tA\nDYdcLVxFtOLUKY9e07zgJH1aqOyGc2IK2FRPDlcrGWk28GhIP57Qu/8j4Eo1\nS1IhBIK8LotiFn0xceoXn0oQc+YLZ5k/tCzdl8FFdm5zZoPZqn6YtawdWE7s\ndP1hdPAYMr8tJq2RFSBYIAeYWe0nTNEeL/dfmxjoyvjcR3n2XB999MLV84b9\nU0iJtl3ddk7VaweQLRnt/dpjnNnGwIgeZSw+BtAtXnB8vWy6jtN2TxUsPN3B\n4fnKiRJ5zSgm3wPdyRvLNfmt6x6koJ7Bm2zky+Bmb4lt+qRNZ9+S7d9vvFKG\n0JMZT6TIsc8ch56zyrnPcQUBwLsdZJhaJMln2afNcLmYSGcNhmCZNRjcqIrD\nOeWJ+xG1rSp1kcKgxXw3Wl8Av8gsk4cG+A/MpJMzGl4qYLWAcLdmdYtG+PLd\nuxiM0YbcVXB/iFMjSTN1CJb4EnUO57Ekb6QxAVwFcRFALEzTBwNggIJWH5JR\nB/B3RVkontPizUWo7G1fFz03ANyqXhL3H5iA3EP+bQ22tsACq7e26uVWSHv4\nyf26bM3Ks8+yAFIcC3YK/C26Ju+3WfLbEQwsDlxAzzJVXpdhfvU6jOs0Snqc\n8Tv0LRuCDq3sYNX336VvvltOfpHBTsNELliAShjlu3E+bnQJ+K72RRVZ8AbP\n/DgW\r\n=U+f/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDxlxQpL7SR3tW+x8MoVX0QkOB+xPh8t+qOGhDL4GtPZAiEAzUorJ308nsH2c2TyqRabeGpKOC9/epC/VTMJI78/+TU="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/js-yaml_3.12.0_1527893306172_0.836872535976291"}, "_hasShrinkwrap": false}, "3.12.1": {"name": "js-yaml", "version": "3.12.1", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "devDependencies": {"ansi": "^0.3.1", "benchmark": "^2.1.4", "browserify": "^16.2.2", "codemirror": "^5.13.4", "eslint": "^4.1.1", "fast-check": "1.1.3", "istanbul": "^0.4.5", "mocha": "^5.2.0", "uglify-js": "^3.0.1"}, "scripts": {"test": "make test"}, "gitHead": "b6d260927b875404eac8b5e610f41d2f8fbe2425", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.12.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.11.0", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-um46hB9wNOKlwkHgiuyEVAybXBjwFUV0Z/RaHJblRd9DXltue9FTYvzCr9ErQrK9Adz5MU4gHWVaNUfdmrC8qA==", "shasum": "295c8632a18a23e054cf5c9d3cecafe678167600", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.12.1.tgz", "fileCount": 37, "unpackedSize": 280143, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcMHc8CRA9TVsSAnZWagAA5CkP/1hzlcBbl9W4wnc73iOL\ni4fK6fISIQTiHkqUensPzoqKPPUGQpUCJiyXi+cRxhsEYuepy1qs8brxfqKf\nej+dk6Gt2GegjKVg94oh9v3NeGus1Nihe/a+KAAoP1YLgBqJx+IxbEuF9X2U\nFsdKRlnTMkDWsGMxJmzj2US9L1U/6UXSrRYykhIbthmrlUZwgUb/U0Clritr\nOdRXJC0uWHHHHutw6Rtn0IkTFACb4jNjwulhSxpA1xKzIbaKEkIE0OxbKklE\nIjnY2KRoibeG9yHCwkAlg8YHnKGutJSuybiHmtjWm+UKsQ8D7+D2PcH2LaFE\nn8MeJTNFQwCEhqlHld1oMopci1pcTmCZKWjHehYWEDNMUr2ymKmk/TzwWtgl\ndUAgXKKK7dBEDRKvIg8xnh/9boxQp70X3m6Kv2ZnTIDtduVUCfDT3B9p1nvG\ntlwS4bkNjsEsCUHNxbs7knw4x545IpIpBNZezy21yR8gRaCFYugSoHVUjt37\nYiEFez9nWS9xiWsx0N0JXcxCQV+GVxuv1UZLkwRI7Dv6TgLbtuY6iTuGypaq\nMvEUzv5dBb2goD+IM06kt4B/Uv+0ZfeaQahqh7Tlbg8iMxbsq9Mndej1Ap57\nHZwpQOVDoqLxTS9aR9DGfcipbjjMj0SHyD2bmc61fN1XyCvgycCaLZyqT3o/\nD288\r\n=AVLk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDiJX9DYN6mddV53I/7M9DleYDFGlLwlcNdQyv36JbriAIhAKFnMTBSQnO9Ho/tG8t9uNkPQC/46ZWRocgLTcwnOfuf"}]}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/js-yaml_3.12.1_1546680124230_0.014144717090798231"}, "_hasShrinkwrap": false}, "3.12.2": {"name": "js-yaml", "version": "3.12.2", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "devDependencies": {"ansi": "^0.3.1", "benchmark": "^2.1.4", "browserify": "^16.2.2", "codemirror": "^5.13.4", "eslint": "^4.1.1", "fast-check": "1.1.3", "istanbul": "^0.4.5", "mocha": "^5.2.0", "uglify-js": "^3.0.1"}, "scripts": {"test": "make test"}, "gitHead": "e4267fc733452d074a3e494fb5cab2a07c7e6b87", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.12.2", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-QHn/Lh/7HhZ/Twc7vJYQTkjuCa0kaCcDcjK5Zlk2rvnUpy7DxMJ23+Jc2dcyvltwQVg1nygAVlB2oRDFHoRS5Q==", "shasum": "ef1d067c5a9d9cb65bd72f285b5d8105c77f14fc", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.12.2.tgz", "fileCount": 37, "unpackedSize": 280272, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcdVUsCRA9TVsSAnZWagAACE0P/26RU6Mym43hFfwbohB8\na4eOjS41sqz6EE7ueT+c1xsTcvVP2GA0dLSX9NtA+DNIWUxzVjuXnmmMRmU4\nujvxsECRe2ESunxwEc5NY32Z0wX3i38oG0TXZQaOKGSGBj404ahFHommLXQ4\ndLUZcMz1PfB/nk04tJGIElUM8GNf5ZfqjUUKlrTffHpekz6vnDW/xx2C7iOg\nyPehdgDUC+myc/Eqb9YQVFScVncZOS91sSOtbKEwxfIigkr7qGITKenXp7nj\nnKmTn28u3+Daoq/ucUElo9zDClwDk/39Vlpz2745KlLiST0alWoYZt44R8S8\nVQC4dbFhVCXpNE2twIc/ouzc2R8uUX6OsaWIqAw4wZLqdAr9U0QV2YW7G4j5\nYczsVkn1eO80nCB2PWIR9hmggEOpvQomni310DVrFGSZo2bi9LvutRovOwAX\n9TUm+Sm6VC5Gb5valgyIm2rUChBdc1DKURTaasLtIqv6YEKK1CtTOKfVNCCy\nle7k487lPYNwpnYBe2YO2hoYeZoU+9FjXqI9pRdzoInR/4MkxHPNw/+Klurj\n1QIlsJnV3gQBj29rskGpVME2fkO7fFhDSLj40Kg1VlqHXCxc6xicFob/vmmD\nyTkxa22Nl3+aRmAPiHivjVW5ZwelHWF+UDNkdrFuOYemRItFtV91mLG9vK8P\n941w\r\n=UEWf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEiVhlj/IGfBnUTqqsLt/1eSpanmA/lYbQ1h+34d7iWOAiEA6vGvrYeLFHkIJO6UOdCOs7OGEhVGpDRxg7MIGPs5X6Q="}]}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/js-yaml_3.12.2_1551193387893_0.4735706534836437"}, "_hasShrinkwrap": false}, "3.13.0": {"name": "js-yaml", "version": "3.13.0", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "devDependencies": {"ansi": "^0.3.1", "benchmark": "^2.1.4", "browserify": "^16.2.2", "codemirror": "^5.13.4", "eslint": "^4.1.1", "fast-check": "1.1.3", "istanbul": "^0.4.5", "mocha": "^5.2.0", "uglify-js": "^3.0.1"}, "scripts": {"test": "make test"}, "gitHead": "9d4ce5e2895365c943d2bdf7e7c8ac1be3ec51a3", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.13.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-pZZoSxcCYco+DIKBTimr67J6Hy+EYGZDY/HCWC+iAEA9h1ByhMXAIVUXMcMFpOCxQ/xjXmPI2MkDL5HRm5eFrQ==", "shasum": "38ee7178ac0eea2c97ff6d96fff4b18c7d8cf98e", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.13.0.tgz", "fileCount": 37, "unpackedSize": 281728, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckpGQCRA9TVsSAnZWagAAoagP/RJ4I/9K0ngegFqs59lY\nhrpqbW9xiZNbsqHm3i6RqiW5hYs90lmeIao5u2zXYUws0CVSu/A7E4mUJlzi\nCQKHfxfzY5/WH4xn0ct8qzUSJ6higEVvryUhZ93rtiKMW/HoQfqjiInkowLc\n1jXJplWpt7cz1xF9S7F9BoFTG2k32nIVK1vqjbUs6/ger0+TPeS/dP8t60mB\nHzwXISXvZ8psSqZ32tx6wIvURNs3jr2yoSr0/2Az2gknT8XoBHyS95STxRgm\nBpwlhxZH2H7DFe2UwsuaqL3hXex4mRrYcyOUXVAAcGwpSbC4W6s+15Dcmaxa\nK4dRJ0wAihknM1H46jD967ZmE69tKfNBoQ5cYELC++EXNvoluKHaf379Ufbk\nkySt9UYwjt/nRqp4gs0E1GJIgtDLDCFXgEVsmxVXfMNwfCJosDsznmztczpw\nOnHHn7E8qqv+zo+2ezP0Kk0mnUZWmzlv9gEg1v4fOOAHYlslXmOxYPD9BLDH\nKinIfd2a7CUikvWXSTDMyb3qWrTOS+RfCplMujGoPx6Qn+RsoqfSjQiV1DPj\nvgzNUqkuLmZAb6EQU1Y5XZqAFfnomFT2mqkn0V23v8pePCPMTo//8LpBkhA0\nD76udOOLSYh7++RUNVE3jYk6iAAs3QU5msTMAVweT6hmAECHe558mqFghgje\nAWJ4\r\n=oITJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF6im706mv3PmwyN7BXJ99eumu1+Z3rgmKlFZn3IGbKRAiEA1gfnas+OD8U/Vpv5+pI84tP5mnnKwhxg9tF0biQu5Q8="}]}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/js-yaml_3.13.0_1553109391761_0.11819358401977476"}, "_hasShrinkwrap": false}, "3.13.1": {"name": "js-yaml", "version": "3.13.1", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "devDependencies": {"ansi": "^0.3.1", "benchmark": "^2.1.4", "browserify": "^16.2.2", "codemirror": "^5.13.4", "eslint": "^4.1.1", "fast-check": "1.1.3", "istanbul": "^0.4.5", "mocha": "^5.2.0", "uglify-js": "^3.0.1"}, "scripts": {"test": "make test"}, "gitHead": "665aadda42349dcae869f12040d9b10ef18d12da", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.13.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.3", "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-YfbcO7jXDdyj0DGxYVSlSeQNHbD7XPWvrVWeVUujrQEoZzWJIRrCPoyk6kL6IAjAG2IolMK4T0hNUe0HOUs5Jw==", "shasum": "aff151b30bfdfa8e49e05da22e7415e9dfa37847", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.13.1.tgz", "fileCount": 37, "unpackedSize": 283005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcp38bCRA9TVsSAnZWagAAhE0P/0hyn9bXVGMTE7DJSmeU\nxZMBBwOP6Ofyr6597JzDE/mpqUDnqRTbArd4Pf1FoM38YDIdoFcvSayCtyaA\nqTodzwt6UIw0ljoI7VnpiSq4S8Gbo6vKeEpbv4Gh6Jc9YHmNIJLfR/iNJ/Ti\np4oAfBogNdWhP6Oqb6Sp3OyF2IOvr7+q90IlkvfDk+pm3ZVF744CjZ0/6rZO\nX5UOWWQsF7bpZDsBV9MAQ+9ifHmHTSbDKsBFxOhnxSvZMSWgJnY8JHHbusk0\nWxCE+DJ9C9C7w+Li22inlNd7y+VNRzK2K3H7pJ0A+fSZp0sWePUuj+ObUarT\nJJU7HbkQT/VyyZdlaprl1GTSWEQdyxvT82fXgqXD3H6X1v5o9kSuKyJaNo8k\nDOFAYQhHBVfY2zMaT+S34uf1hKC9iQJq7kvQ65h/9DYlpcLkSCYhlaANx3V+\nuXdQHLlSRnZHgOaS8HvwVWsA5ZeFjS8xA58d671Li3LsXStZV0x0fgxDJYR0\nUU32uvth2/nCwLaxzPWDFA98FEpOU8/C5ywRk4JIJ1nGbP1qz7XtBwFnL6h4\nmewcAO1JgavPjkMe6qPmiw5jlZd0ebGCRdiaKGoPq1GXRnl+ctOeRSvyfUPf\nx2UNiVHRjzDb7PFpkW7KogNOkmN2TEIrJa3GKAT+GlLn8W0+lkUk/qCUeR5N\nIDd6\r\n=IAWU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICcyMFOpLvWhCJ6rfrqOhJurrDbHHY44FcmJBW04GRpUAiBVXUy35hdps/0FXT9SX3X6rsqOGLU6EbAm2HmGr4PRsw=="}]}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/js-yaml_3.13.1_1554480922316_0.08046878232230115"}, "_hasShrinkwrap": false}, "3.14.0": {"name": "js-yaml", "version": "3.14.0", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "bin": {"js-yaml": "bin/js-yaml.js"}, "unpkg": "dist/js-yaml.min.js", "jsdelivr": "dist/js-yaml.min.js", "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "devDependencies": {"ansi": "^0.3.1", "benchmark": "^2.1.4", "browserify": "^16.2.2", "codemirror": "^5.13.4", "eslint": "^7.0.0", "fast-check": "^1.24.2", "istanbul": "^0.4.5", "mocha": "^7.1.2", "uglify-js": "^3.0.1"}, "scripts": {"test": "make test"}, "gitHead": "34e5072f43fd36b08aaaad433da73c10d47c41e5", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.14.0", "_nodeVersion": "12.14.1", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-/4IbIeHcD9VMHFqDR/gQ7EdZdLimOvW2DdcxFjdyyZ9NsbS+ccrXqVWDtab/lRl5AlUqmpBx8EhPaWR+OtY17A==", "shasum": "a7a34170f26a21bb162424d8adacb4113a69e482", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.0.tgz", "fileCount": 37, "unpackedSize": 291195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeyBpJCRA9TVsSAnZWagAAbLkP/jkhjUJZum1rgaYzZS4p\nj2to4STx/kot62ctT2K65yW6xCU0BG+5BmUYiBqj9ACCUDZeUzqlQSwtbYBf\neX0ca/BKMWN/h1AkjNzVRQm+ms9r1Q0paZ5JhdbGdkXtMq9JgPT3UxzbXKcf\nRfOQwk5/aJliA7QF56a+ZiN2SnMc5G/JDKByT8Xx2cqXshC2l8FgS2tJibj1\nIt+P+03nY6Ysilrnoz5nTyYStADNadkGVLuSb5g5ri/bY5jYsfibBtyXpimJ\nH/oDfx8hbYNYid8nxbS15TB4FNnFHkghaJztBdY9ECOKKnhKwDD5V04ASNNc\nxXDvMKtKNF3Ue7m/HuloNO2WQcKuTFJRTeJau6bv4x+YG7jC0TyuaD3kBtVw\nCLs1UCqDLlua8RrOIB1ckw5Ee5DMjEhtqxCVi92/jgRkqSXAs/7s74RliSNI\n5CQfkuCF0lssVutqAB5aDzbY5FHjMUEYLhGqeP3oEAIfxC+2u/SSSP/BFA5Q\nsAkzxeEU6g7Thx85UqpTMZy/CM88hbSFqoMRRnSnVBou6L/zRy+d5UAP2xhC\nl7fB2qB+fOqgu/SOaY0W5RCz5bYTnzKfLmCtkZI/S7bKCmlXnX2zyp9jhVId\npxjt6daEgD9bXdU91tOIuhuZ4nq4umr8cnKcAhn54hvjADpYRc4egecqA0cQ\ncQj0\r\n=yTe9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFXyfxJ325z4ysBm1yLdttA3IgQwSay7dz0XrAe0KmsnAiEAwlvJiprxRsZFjRsAogT9X4HNaiYtKJxohW800imCm0M="}]}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/js-yaml_3.14.0_1590172232500_0.06002287301971676"}, "_hasShrinkwrap": false}, "3.14.1": {"name": "js-yaml", "version": "3.14.1", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "bin": {"js-yaml": "bin/js-yaml.js"}, "unpkg": "dist/js-yaml.min.js", "jsdelivr": "dist/js-yaml.min.js", "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "devDependencies": {"ansi": "^0.3.1", "benchmark": "^2.1.4", "browserify": "^16.2.2", "codemirror": "^5.13.4", "eslint": "^7.0.0", "fast-check": "^1.24.2", "istanbul": "^0.4.5", "mocha": "^7.1.2", "uglify-js": "^3.0.1"}, "scripts": {"test": "make test"}, "gitHead": "37caaad57dc37d350d9a4577a5da53f482bb2983", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "_id": "js-yaml@3.14.1", "_nodeVersion": "14.15.1", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==", "shasum": "dae812fdb3825fa306609a8717383c50c36a0537", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz", "fileCount": 37, "unpackedSize": 291327, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfzoZHCRA9TVsSAnZWagAA2WUP+QC8N2VlT4KkqVPv7HSA\nPLvXA+iqKDnOpUhbRJmYv0r3jgYWRV4rBKcdTlhoIvrWZx0704h4LtFuz9JL\nFD/hgEoQoBLADdyFEvgSr6jSIn0pk4wukb2c29mw2wRtmAUfizaXC9QB0Ees\nWHntloVe7isDfXgCfu4C7X0lhw3lWSxUCIb55YWxUzxZ10JY/t4pRU6I8qig\nKfiipinJJvlXafr93LzTaYExWUPh0qCb7btOe02eMNoF7njzsihvgZWSb5DW\nk1/N4L8ZcbNZfzE82HySwxuqnCo5sRvZ/YoCfPAFi2lvozILjcdSYDh9DWJO\nrzcBi1XgWjrPc4z/2adXjLMI3TWRTKmNNxBRDCPwedcC1u8u/WrMAHs1+ejo\nnIOuQcucAnWt49SfaH84apiUWrjaKpG3tzExiowr/JfWLe+KxrsgXy07jQID\ns81c4fVPij4xabH6EHemBMARC77rIHKTTys1WcfNQs+Ngu7+m82I1puYvLWm\n1awrglUOUViC2PH1L2hbcIOGo3q16l2CYl06Plbf+8XH6Bh7ETqMAG+yXcDD\nJ2yPdy55AEk73e1hvP2rNySnr2sHWLBGtognczPZxNxODfqDYS+GYrCmmg/e\n9NhXxeA/xraQTyS/QoIkdtGPFoJO7Kr2CClK3NdripSchpsHPLYC/Z6NvzjD\nd4Av\r\n=UtHA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDIE74sLmrdW2HVRqjufLe3yui04Cp/I9VJnr4SSiKp3QIhAOHHlsLpH0rULushbNATK9/YZIc8BYViHmL+v0Pr6C/H"}]}, "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/js-yaml_3.14.1_1607370310569_0.017986086639752985"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "js-yaml", "version": "4.0.0", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "bin": {"js-yaml": "bin/js-yaml.js"}, "module": "./dist/js-yaml.mjs", "exports": {".": {"import": "./dist/js-yaml.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "coverage": "npm run lint && nyc mocha && nyc report --reporter html", "demo": "npm run lint && node support/build_demo.js", "gh-demo": "npm run demo && gh-pages -d demo -f", "browserify": "rollup -c support/rollup.config.js", "prepublishOnly": "npm run gh-demo"}, "unpkg": "dist/js-yaml.min.js", "jsdelivr": "dist/js-yaml.min.js", "dependencies": {"argparse": "^2.0.1"}, "devDependencies": {"@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "ansi": "^0.3.1", "benchmark": "^2.1.4", "codemirror": "^5.13.4", "eslint": "^7.0.0", "fast-check": "^2.8.0", "gh-pages": "^3.1.0", "mocha": "^8.2.1", "nyc": "^15.1.0", "rollup": "^2.34.1", "rollup-plugin-node-polyfills": "^0.2.1", "rollup-plugin-terser": "^7.0.2", "shelljs": "^0.8.4"}, "gitHead": "ee74ce4b4800282b2f23b776be7dc95dfe34db1c", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "homepage": "https://github.com/nodeca/js-yaml#readme", "_id": "js-yaml@4.0.0", "_nodeVersion": "14.15.2", "_npmVersion": "6.14.9", "dist": {"integrity": "sha512-pqon0s+4ScYUvX30wxQi3PogGFAlUyH0awepWvwkj4jD4v+ova3RiYw8bmA6x2rDrEaj8i/oWKoRxpVNW+Re8Q==", "shasum": "f426bc0ff4b4051926cd588c71113183409a121f", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.0.0.tgz", "fileCount": 33, "unpackedSize": 402683, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf8cvXCRA9TVsSAnZWagAA08wP/Rcl8hsNIAhYyWvFVzeQ\nQQun5t1H3i7nPK+S3sPo19qyPSj/DlW806vzyW1UUaXxVJ9+donBp4JOKLum\nLUvKuDovglqiHiW9gPJ9nkJcwDJDp7c4N5G9t2KTfs49lQW21oiVAEe/gdiR\nFom+B3xuQoIIXJV7Uyjw4LG5jGm3NSrZzMzdqf0x/KdARavUnRag7jxzQ4fT\nTJq85dUvAuaipWOmdyPX4+HmgsSD2mNRTEaAGMJcHpPDeOamcrLAKRJWaFAh\nvhHpsl0dX7Es7Ke7PVxBP6ssBZ/CWUXZEr+MlzhTlOqGbAOye427FKXUOrMC\nvft52ROEM0syi2Lmztcn8JDhH5j54wyzH/HDDdj2LLtZCM9MJefrJ6PODtUV\n41PNRJdS//YXoTJzU4WODanHP0VZEfzSZ6lOVIXyftW3Yb+OlD+d0ubabPMd\nf6Yk418PQR9MW145V8eNHdWVAkZlQulkilOnSVBH4v3jqdtIyA04FEAt2hXF\nfoTRbX9H9qTh6nRo8qOmKpTgREtfn0Vo+HzN5z0nCe2B1qc5oQovvjs0MUq7\nnBeUCMGS8zgrvNX4mdbrEwQk/nIXrsY/wHKxt9CDqftpTS3zh12rdzNzdVyb\nFTNBEV5DzRfU/lFNQAVkuwncYDW/P7+BPvhGy/upt0kzON4Qd7T+F9PCw4Hn\nUpoS\r\n=2Y8M\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDMBQeJg2t6hCXguPj0W0nxs2a2OlneZeInFhu1YpWoJwIgFv+vkND4akFFwCCPYG1cTeHmEM4wJOfARRAXNfLK57g="}]}, "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/js-yaml_4.0.0_1609681878541_0.15239597588136355"}, "_hasShrinkwrap": false}, "4.1.0": {"name": "js-yaml", "version": "4.1.0", "description": "YAML 1.2 parser and serializer", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "bin": {"js-yaml": "bin/js-yaml.js"}, "module": "./dist/js-yaml.mjs", "exports": {".": {"import": "./dist/js-yaml.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "scripts": {"lint": "eslint .", "test": "npm run lint && mocha", "coverage": "npm run lint && nyc mocha && nyc report --reporter html", "demo": "npm run lint && node support/build_demo.js", "gh-demo": "npm run demo && gh-pages -d demo -f", "browserify": "rollup -c support/rollup.config.js", "prepublishOnly": "npm run gh-demo"}, "unpkg": "dist/js-yaml.min.js", "jsdelivr": "dist/js-yaml.min.js", "dependencies": {"argparse": "^2.0.1"}, "devDependencies": {"@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "ansi": "^0.3.1", "benchmark": "^2.1.4", "codemirror": "^5.13.4", "eslint": "^7.0.0", "fast-check": "^2.8.0", "gh-pages": "^3.1.0", "mocha": "^8.2.1", "nyc": "^15.1.0", "rollup": "^2.34.1", "rollup-plugin-node-polyfills": "^0.2.1", "rollup-plugin-terser": "^7.0.2", "shelljs": "^0.8.4"}, "gitHead": "2cef47bebf60da141b78b085f3dea3b5733dcc12", "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "homepage": "https://github.com/nodeca/js-yaml#readme", "_id": "js-yaml@4.1.0", "_nodeVersion": "14.16.0", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==", "shasum": "c1fb65f8f5017901cdd2c951864ba18458a10602", "tarball": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz", "fileCount": 33, "unpackedSize": 404738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgd1l9CRA9TVsSAnZWagAAF9IP/2c294d40B2nns6UDAZ4\nylTFVx0ODEZ056pBsj5y2doSpU4mJmBdtwk0Ts5JPRyomu9rbqwmXccZqkio\nMIpWs4ZBxvZZLtmBT7KgFxAHUZ/veI6zylIzM8l5WXNaTR0x7EoupTsw3ErJ\nfcW9vpePbd2Dk1bApZCd3XANCF5DAyqqKzPHFJQwAfDib5j91T7q1Z+H0Ppj\nfPNRKyd4MQPjTK9pWmWeKju02wG3jOqy05pBjHrFMUiVuCpdX98gfbw5+gNj\nrUXT13EVKiNiQkE78wGENVhfUM/tfUDn7hU6Fovg7pVeh6sJdxUqu8aqChSZ\nGPG6vzCDJrn524Tn7HlpnIeK3CFBrKAmgvlcH4nvEWHU2ZB6nUUjfJU/Ooke\ncAdiQ8SDpcecRa0+7y15zxxIVLcbdhgyuBcel5AfMbeEPrLFshwtq1d9Dqc2\nAdAPU3ZcLJ+pin1ppEdYavYBq/C+p3deW0tRf8sIgWCi22uP8xNrZrL2MyE7\n+S7S7FDnyngk2hn7cdKZF7l2VSXHtPMkg4Cv4E2vWdb/JPR/QkgTOHs5Yavo\niAzX2Kgtp02XgEQcYtYQGejdm/DT+hdwBQVlNol7gh7C0PExo8Q5aQucGBxU\ns5N/5cK8afcSe+jpL4OpzFUTorve2TQPC/+7ZLXLmrKwEQ9aQnlfMXP2ZCTQ\n7iqF\r\n=1gZB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBvX4hsjuqb1DyfkWO1/8KcQCQtmU1KzhaRjH82M6uiQAiBOgwE1WLb9jRSUJ6SfeYHVC+ITGVlcebeh3vWXEExS2w=="}]}, "_npmUser": {"name": "vitaly", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/js-yaml_4.1.0_1618434428897_0.2929353399520087"}, "_hasShrinkwrap": false}}, "maintainers": [{"name": "vitaly", "email": "<EMAIL>"}], "time": {"modified": "2023-06-09T21:33:18.974Z", "created": "2011-11-02T01:56:02.870Z", "0.2.0": "2011-11-02T01:56:04.988Z", "0.2.1": "2011-11-02T15:44:59.476Z", "0.2.2": "2011-11-06T19:36:46.376Z", "0.3.0": "2011-11-09T11:50:52.572Z", "0.3.1": "2011-11-18T04:40:26.297Z", "0.3.2": "2011-12-16T03:11:23.796Z", "0.3.3": "2011-12-20T00:33:34.021Z", "0.3.4": "2011-12-24T12:11:57.618Z", "0.3.5": "2012-01-10T12:03:28.241Z", "0.3.6": "2012-02-22T04:58:30.034Z", "0.3.7": "2012-02-28T16:06:34.722Z", "1.0.0": "2012-07-01T15:02:00.004Z", "1.0.1": "2012-07-07T09:42:56.820Z", "1.0.2": "2012-08-02T08:44:43.484Z", "1.0.3": "2012-11-05T01:57:23.010Z", "2.0.0": "2013-02-09T07:34:25.954Z", "2.0.1": "2013-02-09T15:41:35.905Z", "2.0.2": "2013-02-15T11:22:57.482Z", "2.0.3": "2013-02-26T03:14:59.871Z", "2.0.4": "2013-04-08T11:45:36.838Z", "2.0.5": "2013-04-26T14:40:59.621Z", "2.1.0": "2013-06-05T14:44:09.636Z", "2.1.1": "2013-10-01T20:08:34.063Z", "2.1.2": "2013-10-06T21:35:32.358Z", "2.1.3": "2013-10-15T21:30:17.137Z", "3.0.0": "2013-12-16T08:35:24.383Z", "3.0.1": "2013-12-22T18:44:21.114Z", "3.0.2": "2014-02-27T03:20:28.502Z", "3.1.0": "2014-07-07T09:31:00.559Z", "3.2.0": "2014-08-24T07:50:32.612Z", "3.2.1": "2014-08-24T08:05:01.740Z", "3.2.2": "2014-09-06T20:33:29.831Z", "3.2.3": "2014-11-08T05:12:03.094Z", "3.2.4": "2014-12-19T13:13:57.457Z", "3.2.5": "2014-12-27T22:20:55.637Z", "3.2.6": "2015-02-07T03:02:33.322Z", "3.2.7": "2015-02-19T10:09:14.968Z", "3.3.0": "2015-04-26T05:56:35.340Z", "3.3.1": "2015-05-13T13:37:54.734Z", "3.4.0": "2015-08-22T22:35:19.729Z", "3.4.1": "2015-09-04T23:36:30.962Z", "3.4.2": "2015-09-09T10:06:07.503Z", "3.4.3": "2015-10-10T19:41:24.397Z", "3.4.4": "2015-11-21T20:20:51.107Z", "3.4.5": "2015-11-23T04:26:14.849Z", "3.4.6": "2015-11-26T13:53:40.452Z", "3.5.0": "2016-01-10T19:09:01.309Z", "3.5.1": "2016-01-11T01:28:37.047Z", "3.5.2": "2016-01-11T11:03:18.527Z", "3.5.3": "2016-02-11T07:27:27.075Z", "3.5.4": "2016-03-09T18:25:33.192Z", "3.5.5": "2016-03-17T14:48:49.376Z", "3.6.0": "2016-04-15T21:18:53.001Z", "3.6.1": "2016-05-11T19:40:39.230Z", "3.7.0": "2016-11-12T01:32:04.258Z", "3.8.0": "2017-02-07T00:31:34.951Z", "3.8.1": "2017-02-07T00:39:35.390Z", "3.8.2": "2017-03-02T16:49:42.243Z", "3.8.3": "2017-04-05T12:47:42.580Z", "3.8.4": "2017-05-08T15:16:29.219Z", "3.9.0": "2017-07-08T08:50:56.677Z", "3.9.1": "2017-07-30T07:33:59.643Z", "3.10.0": "2017-09-11T08:19:12.765Z", "3.11.0": "2018-03-05T15:52:52.361Z", "3.12.0": "2018-06-01T22:48:26.432Z", "3.12.1": "2019-01-05T09:22:04.437Z", "3.12.2": "2019-02-26T15:03:08.027Z", "3.13.0": "2019-03-20T19:16:31.897Z", "3.13.1": "2019-04-05T16:15:22.531Z", "3.14.0": "2020-05-22T18:30:32.680Z", "3.14.1": "2020-12-07T19:45:10.828Z", "4.0.0": "2021-01-03T13:51:18.695Z", "4.1.0": "2021-04-14T21:07:09.057Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "users": {"pvorb": true, "matthiasg": true, "fgribreau": true, "sbruchmann": true, "adamrenny": true, "brianloveswords": true, "asilvas": true, "gabeio": true, "goliatone": true, "lewisbrown": true, "japh": true, "alxe.master": true, "dewang-mistry": true, "alvint": true, "brentonhouse": true, "jelhaouchi": true, "jdhartley": true, "itonyyo": true, "buzuli": true, "gerst20051": true, "rdcl": true, "brentlintner": true, "denji": true, "rdesoky": true, "willpracht": true, "paroczi": true, "eviratec": true, "lvivier": true, "smartcoordination": true, "fdeneux": true, "monsterkodi": true, "evan2x": true, "octetstream": true, "mfellner": true, "bjmin": true, "leodutra": true, "ambdxtrch": true, "ajmckee": true, "coalesce": true, "pstoev": true, "illuminator": true, "shanewholloway": true, "wouter_vdb": true, "nigel0913": true, "fleischer": true, "monjer": true, "nukosuke": true, "captison": true, "cknowles": true, "talltotal": true, "leizongmin": true, "akarem": true, "mikestaub": true, "jakedetels": true, "hifaraz": true, "bensonho": true, "ajduke": true, "tteogi": true, "tmurngon": true, "gabriellopes": true, "penglu": true, "troygizzi": true, "gbas": true, "prometheas": true, "iori20091101": true, "skellertor": true, "rickyrattlesnake": true, "program247365": true, "leonzhao": true, "janez89": true, "tcrowe": true, "bsara": true, "kael": true, "holmes89": true, "quafoo": true, "lacodda": true, "crimeminister": true, "stone_breaker": true, "nikolay": true, "kontrax": true, "stanlous": true, "axelrindle": true, "bphanikumar": true, "lvpeng101": true, "yitzchak": true, "tsxuehu": true, "lholmquist": true, "felarof99": true, "elevenlui": true, "kakaman": true, "lgh06": true, "kekdude": true, "edwardxyt": true, "keenwon": true, "d7game": true, "geofftech": true, "hehaiyang": true, "rogeruiz": true, "muwum": true, "bstevenson": true, "zhenguo.zhao": true, "omar84": true, "nickeltobias": true, "nilz3ro": true, "jream": true, "chrisyipw": true, "nuwaio": true, "sammade": true, "tamikot": true, "chronosis": true, "eterna2": true, "maxkoryukov": true, "astraloverflow": true, "nraibaud": true, "onlyutkarsh": true, "ierhyna": true, "xgheaven": true, "xiaobing": true, "arcticicestudio": true, "daizch": true, "flumpus-dev": true}, "readme": "JS-YAML - YAML 1.2 parser / writer for JavaScript\n=================================================\n\n[![CI](https://github.com/nodeca/js-yaml/workflows/CI/badge.svg?branch=master)](https://github.com/nodeca/js-yaml/actions)\n[![NPM version](https://img.shields.io/npm/v/js-yaml.svg)](https://www.npmjs.org/package/js-yaml)\n\n__[Online Demo](http://nodeca.github.com/js-yaml/)__\n\n\nThis is an implementation of [YAML](http://yaml.org/), a human-friendly data\nserialization language. Started as [PyYAML](http://pyyaml.org/) port, it was\ncompletely rewritten from scratch. Now it's very fast, and supports 1.2 spec.\n\n\nInstallation\n------------\n\n### YAML module for node.js\n\n```\nnpm install js-yaml\n```\n\n\n### CLI executable\n\nIf you want to inspect your YAML files from CLI, install js-yaml globally:\n\n```\nnpm install -g js-yaml\n```\n\n#### Usage\n\n```\nusage: js-yaml [-h] [-v] [-c] [-t] file\n\nPositional arguments:\n  file           File with YAML document(s)\n\nOptional arguments:\n  -h, --help     Show this help message and exit.\n  -v, --version  Show program's version number and exit.\n  -c, --compact  Display errors in compact mode\n  -t, --trace    Show stack trace on error\n```\n\n\nAPI\n---\n\nHere we cover the most 'useful' methods. If you need advanced details (creating\nyour own tags), see [examples](https://github.com/nodeca/js-yaml/tree/master/examples)\nfor more info.\n\n``` javascript\nconst yaml = require('js-yaml');\nconst fs   = require('fs');\n\n// Get document, or throw exception on error\ntry {\n  const doc = yaml.load(fs.readFileSync('/home/<USER>/example.yml', 'utf8'));\n  console.log(doc);\n} catch (e) {\n  console.log(e);\n}\n```\n\n\n### load (string [ , options ])\n\nParses `string` as single YAML document. Returns either a\nplain object, a string, a number, `null` or `undefined`, or throws `YAMLException` on error. By default, does\nnot support regexps, functions and undefined.\n\noptions:\n\n- `filename` _(default: null)_ - string to be used as a file path in\n  error/warning messages.\n- `onWarning` _(default: null)_ - function to call on warning messages.\n  Loader will call this function with an instance of `YAMLException` for each warning.\n- `schema` _(default: `DEFAULT_SCHEMA`)_ - specifies a schema to use.\n  - `FAILSAFE_SCHEMA` - only strings, arrays and plain objects:\n    http://www.yaml.org/spec/1.2/spec.html#id2802346\n  - `JSON_SCHEMA` - all JSON-supported types:\n    http://www.yaml.org/spec/1.2/spec.html#id2803231\n  - `CORE_SCHEMA` - same as `JSON_SCHEMA`:\n    http://www.yaml.org/spec/1.2/spec.html#id2804923\n  - `DEFAULT_SCHEMA` - all supported YAML types.\n- `json` _(default: false)_ - compatibility with JSON.parse behaviour. If true, then duplicate keys in a mapping will override values rather than throwing an error.\n\nNOTE: This function **does not** understand multi-document sources, it throws\nexception on those.\n\nNOTE: JS-YAML **does not** support schema-specific tag resolution restrictions.\nSo, the JSON schema is not as strictly defined in the YAML specification.\nIt allows numbers in any notation, use `Null` and `NULL` as `null`, etc.\nThe core schema also has no such restrictions. It allows binary notation for integers.\n\n\n### loadAll (string [, iterator] [, options ])\n\nSame as `load()`, but understands multi-document sources. Applies\n`iterator` to each document if specified, or returns array of documents.\n\n``` javascript\nconst yaml = require('js-yaml');\n\nyaml.loadAll(data, function (doc) {\n  console.log(doc);\n});\n```\n\n\n### dump (object [ , options ])\n\nSerializes `object` as a YAML document. Uses `DEFAULT_SCHEMA`, so it will\nthrow an exception if you try to dump regexps or functions. However, you can\ndisable exceptions by setting the `skipInvalid` option to `true`.\n\noptions:\n\n- `indent` _(default: 2)_ - indentation width to use (in spaces).\n- `noArrayIndent` _(default: false)_ - when true, will not add an indentation level to array elements\n- `skipInvalid` _(default: false)_ - do not throw on invalid types (like function\n  in the safe schema) and skip pairs and single values with such types.\n- `flowLevel` _(default: -1)_ - specifies level of nesting, when to switch from\n  block to flow style for collections. -1 means block style everwhere\n- `styles` - \"tag\" => \"style\" map. Each tag may have own set of styles.\n- `schema` _(default: `DEFAULT_SCHEMA`)_ specifies a schema to use.\n- `sortKeys` _(default: `false`)_ - if `true`, sort keys when dumping YAML. If a\n  function, use the function to sort the keys.\n- `lineWidth` _(default: `80`)_ - set max line width. Set `-1` for unlimited width.\n- `noRefs` _(default: `false`)_ - if `true`, don't convert duplicate objects into references\n- `noCompatMode` _(default: `false`)_ - if `true` don't try to be compatible with older\n  yaml versions. Currently: don't quote \"yes\", \"no\" and so on, as required for YAML 1.1\n- `condenseFlow` _(default: `false`)_ - if `true` flow sequences will be condensed, omitting the space between `a, b`. Eg. `'[a,b]'`, and omitting the space between `key: value` and quoting the key. Eg. `'{\"a\":b}'` Can be useful when using yaml for pretty URL query params as spaces are %-encoded.\n- `quotingType` _(`'` or `\"`, default: `'`)_ - strings will be quoted using this quoting style. If you specify single quotes, double quotes will still be used for non-printable characters.\n- `forceQuotes` _(default: `false`)_ - if `true`, all non-key strings will be quoted even if they normally don't need to.\n- `replacer` - callback `function (key, value)` called recursively on each key/value in source object (see `replacer` docs for `JSON.stringify`).\n\nThe following table show availlable styles (e.g. \"canonical\",\n\"binary\"...) available for each tag (.e.g. !!null, !!int ...). Yaml\noutput is shown on the right side after `=>` (default setting) or `->`:\n\n``` none\n!!null\n  \"canonical\"   -> \"~\"\n  \"lowercase\"   => \"null\"\n  \"uppercase\"   -> \"NULL\"\n  \"camelcase\"   -> \"Null\"\n\n!!int\n  \"binary\"      -> \"0b1\", \"0b101010\", \"0b1110001111010\"\n  \"octal\"       -> \"0o1\", \"0o52\", \"0o16172\"\n  \"decimal\"     => \"1\", \"42\", \"7290\"\n  \"hexadecimal\" -> \"0x1\", \"0x2A\", \"0x1C7A\"\n\n!!bool\n  \"lowercase\"   => \"true\", \"false\"\n  \"uppercase\"   -> \"TRUE\", \"FALSE\"\n  \"camelcase\"   -> \"True\", \"False\"\n\n!!float\n  \"lowercase\"   => \".nan\", '.inf'\n  \"uppercase\"   -> \".NAN\", '.INF'\n  \"camelcase\"   -> \".NaN\", '.Inf'\n```\n\nExample:\n\n``` javascript\ndump(object, {\n  'styles': {\n    '!!null': 'canonical' // dump null as ~\n  },\n  'sortKeys': true        // sort object keys\n});\n```\n\nSupported YAML types\n--------------------\n\nThe list of standard YAML tags and corresponding JavaScript types. See also\n[YAML tag discussion](http://pyyaml.org/wiki/YAMLTagDiscussion) and\n[YAML types repository](http://yaml.org/type/).\n\n```\n!!null ''                   # null\n!!bool 'yes'                # bool\n!!int '3...'                # number\n!!float '3.14...'           # number\n!!binary '...base64...'     # buffer\n!!timestamp 'YYYY-...'      # date\n!!omap [ ... ]              # array of key-value pairs\n!!pairs [ ... ]             # array or array pairs\n!!set { ... }               # array of objects with given keys and null values\n!!str '...'                 # string\n!!seq [ ... ]               # array\n!!map { ... }               # object\n```\n\n**JavaScript-specific tags**\n\nSee [js-yaml-js-types](https://github.com/nodeca/js-yaml-js-types) for\nextra types.\n\n\nCaveats\n-------\n\nNote, that you use arrays or objects as key in JS-YAML. JS does not allow objects\nor arrays as keys, and stringifies (by calling `toString()` method) them at the\nmoment of adding them.\n\n``` yaml\n---\n? [ foo, bar ]\n: - baz\n? { foo: bar }\n: - baz\n  - baz\n```\n\n``` javascript\n{ \"foo,bar\": [\"baz\"], \"[object Object]\": [\"baz\", \"baz\"] }\n```\n\nAlso, reading of properties on implicit block mapping keys is not supported yet.\nSo, the following YAML document cannot be loaded.\n\n``` yaml\n&anchor foo:\n  foo: bar\n  *anchor: duplicate key\n  baz: bat\n  *anchor: duplicate key\n```\n\n\njs-yaml for enterprise\n----------------------\n\nAvailable as part of the Tidelift Subscription\n\nThe maintainers of js-yaml and thousands of other packages are working with Tidelift to deliver commercial support and maintenance for the open source dependencies you use to build your applications. Save time, reduce risk, and improve code health, while paying the maintainers of the exact dependencies you use. [Learn more.](https://tidelift.com/subscription/pkg/npm-js-yaml?utm_source=npm-js-yaml&utm_medium=referral&utm_campaign=enterprise&utm_term=repo)\n", "homepage": "https://github.com/nodeca/js-yaml#readme", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": "MIT", "readmeFilename": "README.md"}