{"_id": "istanbul-lib-source-maps", "_rev": "61-dca3157d63781017ff62c55a5a56503d", "name": "istanbul-lib-source-maps", "description": "Source maps support for istanbul", "dist-tags": {"latest": "5.0.6"}, "versions": {"1.0.0-alpha.1": {"name": "istanbul-lib-source-maps", "version": "1.0.0-alpha.1", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@1.0.0-alpha.1", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-source-maps#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-source-maps/issues"}, "dist": {"shasum": "3175f380832f91e3e9bd95df23c229095f656fdc", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.0.0-alpha.1.tgz", "integrity": "sha512-v4fcvN6tzewQZM/keLEo5JRa1uFWc+2Rm+Ph1JCPb3KSnq7e7hd0p6DjHLIg7i/Qn75SHveRc2QGjIKe4QCZsQ==", "signatures": [{"sig": "MEUCIQCHLaPuyE5IY1USmETRdb3NEXJniXgsFztUtLo5zv8xyAIgOebnftKh41b8tc3At2fQILPeF2YscgbXAQBYR2yQS6E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "3175f380832f91e3e9bd95df23c229095f656fdc", "gitHead": "a5d378ca9c2d5bda3e3c476fa62c4bc236728d6d", "scripts": {"fast": "mocha test/", "test": "istanbul cover --include-all-sources --print=both _mocha --  test/", "pretest": "jshint index.js lib/ test/", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-source-maps.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Source maps support for istanbul", "directories": {}, "dependencies": {"source-map": "^0.5.3", "convert-source-map": "^1.1.2", "istanbul-lib-coverage": "^1.0.0-alpha.0"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^2.3.4", "jshint": "^2.8.0", "ts-node": "^0.5.3", "istanbul": "^0.4.0", "babel-core": "^6.2.1"}}, "1.0.0-alpha.2": {"name": "istanbul-lib-source-maps", "version": "1.0.0-alpha.2", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@1.0.0-alpha.2", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-source-maps#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-source-maps/issues"}, "dist": {"shasum": "45503d5277b3e88667fb109a4cf5134079baaa1f", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.0.0-alpha.2.tgz", "integrity": "sha512-SB+1Gvr7/DE6wCKMC9dXh/O097R3xHrVNKxNuHjYqJGo+ELE6lxvipJavo0J/olaAqS/IZ+vKAPRY/jQQRTggg==", "signatures": [{"sig": "MEYCIQC1FH7Wi0s18rdrFcHlHR1rE9r4wNSIZ3HO/QbdmPtvwQIhAIKqho+cKGzuNf4ilzXIJzeQB1aNliKv6NcOx7b38Qc5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "45503d5277b3e88667fb109a4cf5134079baaa1f", "gitHead": "f992dc55966a23a60417370f4a9a22d381fa52df", "scripts": {"fast": "mocha test/", "test": "istanbul cover --include-all-sources --print=both _mocha --  test/", "pretest": "jshint index.js lib/ test/", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-source-maps.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Source maps support for istanbul", "directories": {}, "dependencies": {"source-map": "^0.5.3", "convert-source-map": "^1.1.2", "istanbul-lib-coverage": "^1.0.0-alpha.0"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^2.3.4", "jshint": "^2.8.0", "ts-node": "^0.5.3", "istanbul": "^0.4.0", "babel-core": "^6.2.1"}}, "1.0.0-alpha.3": {"name": "istanbul-lib-source-maps", "version": "1.0.0-alpha.3", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@1.0.0-alpha.3", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-source-maps#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-source-maps/issues"}, "dist": {"shasum": "ecd0cf1f6c946b60ca6961b3085498186a084e50", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.0.0-alpha.3.tgz", "integrity": "sha512-HSj4RaYM47MbocS/cYZNcclaQjLkteMHteGtp3Wd/LWzEcgVVlb0dgYhfTuL8oCA0G8J4kMSUBLbGVCPiQ6diA==", "signatures": [{"sig": "MEUCIQDiGW+oXGQsUfDxjny81y2wfXc4Tx6suyvMfYeHVPWJkwIgf4x85QeGtu2y7+Iqqq3ivEAFRvOviziaz+fa44SMY4w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "ecd0cf1f6c946b60ca6961b3085498186a084e50", "gitHead": "d0c27c8dce942f39463def4b72798a5196a009fe", "scripts": {"fast": "mocha test/", "test": "istanbul cover --include-all-sources --print=both _mocha --  test/", "pretest": "jshint index.js lib/ test/", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-source-maps.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Source maps support for istanbul", "directories": {}, "dependencies": {"source-map": "^0.5.3", "convert-source-map": "^1.1.2", "istanbul-lib-coverage": "^1.0.0-alpha.0"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^2.3.4", "jshint": "^2.8.0", "ts-node": "^0.5.3", "istanbul": "^0.4.0", "babel-core": "^6.2.1"}}, "1.0.0-alpha.4": {"name": "istanbul-lib-source-maps", "version": "1.0.0-alpha.4", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@1.0.0-alpha.4", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-source-maps#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-source-maps/issues"}, "dist": {"shasum": "46e9831e474164a03998eeaaf06fa3be61b64813", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.0.0-alpha.4.tgz", "integrity": "sha512-uBXSjgSRjOfoN2puCBsshlut+IjzAc+qeqmqOMAKwCz7tbpCes0VxScp1TfPxP1yIbdWoPqxr6xIoQymnVQPJg==", "signatures": [{"sig": "MEYCIQCa25XVPv8PX8Xq5hpC7xIJNeHNa51jDMoB6VQnrpeF1gIhAIDnC6J19Kcp6m7Upvnnl545uFh7whb9SPqEqhspnyj1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "46e9831e474164a03998eeaaf06fa3be61b64813", "gitHead": "b5e120db861b2f46b2d9cc2452d2b0f1d9beb128", "scripts": {"fast": "mocha test/", "test": "istanbul cover --include-all-sources --print=both _mocha --  test/", "pretest": "jshint index.js lib/ test/", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-source-maps.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"source-map": "^0.5.3", "istanbul-lib-coverage": "^1.0.0-alpha.0"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^2.3.4", "jshint": "^2.8.0", "ts-node": "^0.5.3", "istanbul": "^0.4.0", "babel-core": "^6.2.1"}}, "1.0.0-alpha.5": {"name": "istanbul-lib-source-maps", "version": "1.0.0-alpha.5", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@1.0.0-alpha.5", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-source-maps#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-source-maps/issues"}, "dist": {"shasum": "b32d9bcf16f669441b4940cd6d53442e2500c24e", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.0.0-alpha.5.tgz", "integrity": "sha512-tbD6QpJYfSK1C53TfaIU6wsp7jZ4ViCCShsxGOcDRxLO8Vw6DvP9B17D2yUYLAxYuSLVpVok5rbJQBVHFKg44g==", "signatures": [{"sig": "MEUCIAvE6B9VdL010QmPDF0dJBPUcCMW3OoItjKDAYAkct3gAiEAl/xFp6QFDEYFjR+aFIfrhJz1VeD2SV1taEBHH8k9czo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "b32d9bcf16f669441b4940cd6d53442e2500c24e", "gitHead": "110746e8dea16a22cb92c46eaa5405286740f109", "scripts": {"fast": "mocha test/", "test": "istanbul cover --include-all-sources --print=both _mocha --  test/", "pretest": "jshint index.js lib/ test/", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-source-maps.git", "type": "git"}, "_npmVersion": "2.12.0", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "0.10.26", "dependencies": {"mkdirp": "^0.5.1", "rimraf": "^2.4.4", "source-map": "^0.5.3", "istanbul-lib-coverage": "^1.0.0-alpha.0"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^2.3.4", "jshint": "^2.8.0", "ts-node": "^0.5.3", "istanbul": "^0.4.0", "babel-core": "^6.2.1"}}, "1.0.0-alpha.7": {"name": "istanbul-lib-source-maps", "version": "1.0.0-alpha.7", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@1.0.0-alpha.7", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-source-maps#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-source-maps/issues"}, "dist": {"shasum": "240f9ac3fdf4e957609da24f0cda13e16625063c", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.0.0-alpha.7.tgz", "integrity": "sha512-rl097dosvFsnPTAHT0WKKg3jDtuVJpcoe2U17XtOkRTT0BvQKtryrvkpeoPeCW5XUvCf4Y2WDXIF9M/oMJ7EfQ==", "signatures": [{"sig": "MEYCIQCjy48yfpiq4SEdtIopbUfWL/rQxVIdQ+M9DW+o60O4OAIhAO8PbTYyf3eCeuFMHuW2rX56IFxVqwLIY9JGGI/BTvc0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "240f9ac3fdf4e957609da24f0cda13e16625063c", "gitHead": "d3790c5252305f63582ff5c89217655416e054b6", "scripts": {"fast": "mocha test/", "test": "istanbul cover --include-all-sources --print=both _mocha --  test/", "pretest": "jshint index.js lib/ test/", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-source-maps.git", "type": "git"}, "_npmVersion": "2.12.0", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "0.10.26", "dependencies": {"mkdirp": "^0.5.1", "rimraf": "^2.4.4", "source-map": "^0.5.3", "istanbul-lib-coverage": "^1.0.0-alpha.0"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^2.3.4", "jshint": "^2.8.0", "ts-node": "^0.5.3", "istanbul": "^0.4.0", "babel-core": "^6.2.1"}}, "1.0.0-alpha.8": {"name": "istanbul-lib-source-maps", "version": "1.0.0-alpha.8", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@1.0.0-alpha.8", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-source-maps#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-source-maps/issues"}, "dist": {"shasum": "a91e64d2d42fc9767e142a001732d241aaaeeb68", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.0.0-alpha.8.tgz", "integrity": "sha512-P23XkpjTxsCfBW3QqK/BZVP0DDnnjYpESxRS7lWqxdEftixFIPLqPB6hXRDSOJ43rTYOB1HZ0mPUQANHv7VbEA==", "signatures": [{"sig": "MEUCIQCwdjIOf49+IpIv34lPdESWzdQIINpzrsdVpl5SCHWeAQIgQKMkJ5FjQMBGg2ylO3f+HwuTe0vlVdGoYKUA0EANK8s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "a91e64d2d42fc9767e142a001732d241aaaeeb68", "gitHead": "cb70e2925b25a5d39ae62019d0330bf3e52fae8e", "scripts": {"fast": "mocha test/", "test": "istanbul cover --include-all-sources --print=both _mocha --  test/", "pretest": "jshint index.js lib/ test/", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-source-maps.git", "type": "git"}, "_npmVersion": "2.12.0", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "0.10.26", "dependencies": {"mkdirp": "^0.5.1", "rimraf": "^2.4.4", "source-map": "^0.5.3", "istanbul-lib-coverage": "^1.0.0-alpha.0"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^2.3.4", "jshint": "^2.8.0", "ts-node": "^0.5.3", "istanbul": "^0.4.0", "babel-core": "^6.2.1"}}, "1.0.0-alpha.9": {"name": "istanbul-lib-source-maps", "version": "1.0.0-alpha.9", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@1.0.0-alpha.9", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-source-maps#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-source-maps/issues"}, "dist": {"shasum": "cc581079e2ff407e1a463188c24a1a191850b259", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.0.0-alpha.9.tgz", "integrity": "sha512-CEDUcDH3nezUjhBE2Mn3oBJmkUjoRaFuSCk/DBKF+FPd7rGrJTxr0oqaEiYjf94mhq5e1SlctAvfzHWnHAeTdg==", "signatures": [{"sig": "MEUCIQCMkvXXWo+XJeihvy3zlxzcWKNSEVE1DQluTaprae9AmAIgPwFIw8xRexYOVURHwRiBRHDH7pkDRgwL8IGa4VmZzeg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "cc581079e2ff407e1a463188c24a1a191850b259", "gitHead": "8c43f14e48baeae6bb461639b4c91fe73c693ce0", "scripts": {"fast": "mocha test/", "test": "istanbul cover --include-all-sources --print=both _mocha --  test/", "pretest": "jshint index.js lib/ test/", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-source-maps.git", "type": "git"}, "_npmVersion": "3.5.0", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"mkdirp": "^0.5.1", "rimraf": "^2.4.4", "source-map": "^0.5.3", "istanbul-lib-coverage": "^1.0.0-alpha.0"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^2.3.4", "jshint": "^2.8.0", "ts-node": "^0.5.3", "istanbul": "^0.4.0", "babel-core": "^6.2.1"}}, "1.0.0-alpha.10": {"name": "istanbul-lib-source-maps", "version": "1.0.0-alpha.10", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@1.0.0-alpha.10", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-source-maps#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-source-maps/issues"}, "dist": {"shasum": "9b15a5c8b59d1b9101be2cb7dd54c703d86adef1", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.0.0-alpha.10.tgz", "integrity": "sha512-LALGJndy4bMCLWgKjln/nYrKhKXe6ciEGbLG6+JL1q3vKzQ6sQw/gazT+zMns+nAUyU5NHzK3LLjtf1IMxHieg==", "signatures": [{"sig": "MEUCIQCUYwHfVfy8E2vWTjuM6LMsF81CsYMzjwtPU4w502pPagIga8efEbFhDJmXNgos8m/urIvUCWzbKlPbHRbnIZlnadw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "9b15a5c8b59d1b9101be2cb7dd54c703d86adef1", "gitHead": "167a7dc13eacc60484e15bc516191506835d609e", "scripts": {"fast": "mocha test/", "test": "istanbul cover --include-all-sources --print=both _mocha --  test/", "pretest": "jshint index.js lib/ test/", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-source-maps.git", "type": "git"}, "_npmVersion": "2.11.3", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "0.12.7", "dependencies": {"mkdirp": "^0.5.1", "rimraf": "^2.4.4", "source-map": "^0.5.3", "istanbul-lib-coverage": "^1.0.0-alpha.0"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^2.3.4", "jshint": "^2.8.0", "ts-node": "^0.5.3", "istanbul": "^0.4.0", "babel-core": "^6.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps-1.0.0-alpha.10.tgz_1459971711845_0.5325277280062437", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.0": {"name": "istanbul-lib-source-maps", "version": "1.0.0", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@1.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-source-maps#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-source-maps/issues"}, "dist": {"shasum": "a0ce45f9ba6a9790512ca3d037e2b858f94d33b3", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.0.0.tgz", "integrity": "sha512-G3Paclx+bs+MqyujPT/rwz/A7myFXoLtQyOjXLIhzcSP8+Ep1rAZW23oWr9QK3T9EjCBcU0xGRIYqK5cLQb5Xg==", "signatures": [{"sig": "MEYCIQCZ7TDwD7sGaZwDDMWBHgrmDjKHEDiU+WxuGNQNblOYsgIhAJJFAQctaniZnIta0lbblADaucn7JqVts39pJPmrDRWE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "a0ce45f9ba6a9790512ca3d037e2b858f94d33b3", "gitHead": "5de589f215d6274c4fce228c208294a4e1cc9881", "scripts": {"fast": "mocha test/", "test": "istanbul cover --include-all-sources --print=both _mocha --  test/", "pretest": "jshint index.js lib/ test/", "release": "standard-version", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-source-maps.git", "type": "git"}, "_npmVersion": "3.10.6", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"mkdirp": "^0.5.1", "rimraf": "^2.4.4", "source-map": "^0.5.3", "istanbul-lib-coverage": "^1.0.0-alpha.0"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^2.3.4", "jshint": "^2.8.0", "ts-node": "^0.5.3", "istanbul": "^0.4.0", "babel-core": "^6.2.1", "standard-version": "^2.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps-1.0.0.tgz_1472656251332_0.1985593643039465", "host": "packages-16-east.internal.npmjs.com"}}, "1.0.1": {"name": "istanbul-lib-source-maps", "version": "1.0.1", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@1.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-source-maps#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-source-maps/issues"}, "dist": {"shasum": "92393f1b1f11b5916beea382c1901398a81b7d4c", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.0.1.tgz", "integrity": "sha512-KSNmdiYaD+L9Lq6Zw8or1dtAnjF/ps8MSmR0jFTMUSVZIgJNxCySKqoC/QuYo8kyDigol37z6YTCWDFGicNGrw==", "signatures": [{"sig": "MEYCIQCqk1WsFEoIykNfulbCcqcYVRCgFIB13AfxvLl4GZXDygIhANB4cfTWZPSaoJ709XVtLWsDtPWhOByEdypqfGSlSL+a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "92393f1b1f11b5916beea382c1901398a81b7d4c", "gitHead": "73383cf76a0e197d3c98798ff7fe99cf0679312a", "scripts": {"fast": "mocha test/", "test": "istanbul cover --include-all-sources --print=both _mocha --  test/", "pretest": "jshint index.js lib/ test/", "release": "standard-version", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-source-maps.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"mkdirp": "^0.5.1", "rimraf": "^2.4.4", "source-map": "^0.5.3", "istanbul-lib-coverage": "^1.0.0-alpha.0"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^2.3.4", "jshint": "^2.8.0", "ts-node": "^0.5.3", "istanbul": "^0.4.0", "babel-core": "^6.2.1", "standard-version": "^2.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps-1.0.1.tgz_1473744276908_0.7366695574019104", "host": "packages-16-east.internal.npmjs.com"}}, "1.0.2": {"name": "istanbul-lib-source-maps", "version": "1.0.2", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@1.0.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-source-maps#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-source-maps/issues"}, "dist": {"shasum": "9e91b0e5ae6ed203f67c69a34e6e98b10bb69a49", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.0.2.tgz", "integrity": "sha512-vrLZfT6p+l7gaKPY/bmK1uhIRKtK15g92LCnykarhsfYQTwuHRKxt8pXffBk5RXOtENRo3yOAex5yVp9TJZZVw==", "signatures": [{"sig": "MEUCIEh5X722hwN9Ju7UoIXvl1ZJi5LS9eth5o3pSLW1PZ7yAiEAsRDK3rCtBqsq1o18In4U8zFpUI9k9/nsSl3QKgrOpZs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "9e91b0e5ae6ed203f67c69a34e6e98b10bb69a49", "gitHead": "2746ef2c2a40c35078d26adbdd4685ba7a8e99a6", "scripts": {"fast": "mocha test/", "test": "istanbul cover --include-all-sources --print=both _mocha --  test/", "pretest": "jshint index.js lib/ test/", "release": "standard-version", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-source-maps.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"mkdirp": "^0.5.1", "rimraf": "^2.4.4", "source-map": "^0.5.3", "istanbul-lib-coverage": "^1.0.0-alpha.0"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^2.3.4", "jshint": "^2.8.0", "ts-node": "^0.5.3", "istanbul": "^0.4.0", "babel-core": "^6.2.1", "standard-version": "^2.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps-1.0.2.tgz_1475536757330_0.3448555942159146", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.0": {"name": "istanbul-lib-source-maps", "version": "1.1.0", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@1.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-source-maps#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-source-maps/issues"}, "dist": {"shasum": "9d429218f35b823560ea300a96ff0c3bbdab785f", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.1.0.tgz", "integrity": "sha512-OKB/+4l4zpTQ/v7ZhLzHlUzQhoExcXRuKdICMCBTHb1TB7n0LiCJ9CBU69lJQOJZV5SVe0/P8FsyF3Je0laqOw==", "signatures": [{"sig": "MEYCIQDKxzCmKNf1zTw1DsVdKhwKpKvEQscl7lFn4lx9D9mHvQIhAKC6+ibIuVconfq+817Ca/11/oObKIWu6/a/GHgRR1cv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "9d429218f35b823560ea300a96ff0c3bbdab785f", "gitHead": "7c077484285b422185aeeaf7f90c0c97e986c79b", "scripts": {"fast": "mocha test/", "test": "istanbul cover --include-all-sources --print=both _mocha --  test/", "pretest": "jshint index.js lib/ test/", "release": "standard-version", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-source-maps.git", "type": "git"}, "_npmVersion": "4.0.0", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"mkdirp": "^0.5.1", "rimraf": "^2.4.4", "source-map": "^0.5.3", "istanbul-lib-coverage": "^1.0.0-alpha.0"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^2.3.4", "jshint": "^2.8.0", "ts-node": "^0.5.3", "istanbul": "^0.4.0", "babel-core": "^6.2.1", "standard-version": "^2.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps-1.1.0.tgz_1478758278578_0.8678429392166436", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.1": {"name": "istanbul-lib-source-maps", "version": "1.1.1", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@1.1.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-source-maps#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-source-maps/issues"}, "dist": {"shasum": "f8c8c2e8f2160d1d91526d97e5bd63b2079af71c", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.1.1.tgz", "integrity": "sha512-f9qeb/b3Tt7peVaxEZWhyf8n2UAPUYXWnVur5KdR9Fx9P7ODUXtiZtOVLx7MJLKLU5j91OA2anWMkBeEo1vCcQ==", "signatures": [{"sig": "MEUCIQD1wn2Exc93UIsf6twYjy9onG8qnp2L334buIH0ePRvtQIgX9XsCXpDg2CNkVjYvXnZwLC5U/cIjGzrWHHwQ0NcN4A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f8c8c2e8f2160d1d91526d97e5bd63b2079af71c", "scripts": {"test": "mocha", "pretest": "jshint index.js lib/ test/"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-source-maps.git", "type": "git"}, "_npmVersion": "4.4.1", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "6.9.5", "dependencies": {"mkdirp": "^0.5.1", "rimraf": "^2.4.4", "source-map": "^0.5.3", "istanbul-lib-coverage": "^1.0.2"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^3.2.0", "jshint": "^2.8.0", "ts-node": "^2.0.0", "babel-core": "^6.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps-1.1.1.tgz_1490593886836_0.8505288932938129", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.0": {"name": "istanbul-lib-source-maps", "version": "1.2.0", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@1.2.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbul-lib-source-maps#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbul-lib-source-maps/issues"}, "dist": {"shasum": "8c7706d497e26feeb6af3e0c28fd5b0669598d0e", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.2.0.tgz", "integrity": "sha512-jr8wtWkHbTSCdWbs0qekBwp3pKm78h/iCdX6GssKXkOYpeSiInm1N0JgllTZuUOlGAtx6d1kJe9oguazg7snVQ==", "signatures": [{"sig": "MEUCIQDktqLR7+waMVJSDl3Pldo/0wdgl9IVoOC4JOY6AlZoLwIgUY5K5QcnDgORPsC3aqk7zqqIrLFzhJIX8FzmP29duBg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib", "index.js"], "_shasum": "8c7706d497e26feeb6af3e0c28fd5b0669598d0e", "scripts": {"test": "mocha", "pretest": "jshint index.js lib/ test/"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbul-lib-source-maps.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"debug": "^2.6.3", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "source-map": "^0.5.3", "istanbul-lib-coverage": "^1.1.0"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^3.2.0", "jshint": "^2.8.0", "ts-node": "^2.0.0", "babel-core": "^6.2.1", "is-windows": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps-1.2.0.tgz_1493442009706_0.9414261810015887", "host": "packages-18-east.internal.npmjs.com"}}, "1.2.1": {"name": "istanbul-lib-source-maps", "version": "1.2.1", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@1.2.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "a6fe1acba8ce08eebc638e572e294d267008aa0c", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.2.1.tgz", "integrity": "sha512-mukVvSXCn9JQvdJl8wP/iPhqig0MRtuWuD4ZNKo6vB2Ik//AmhAKe3QnPN02dmkRe3lTudFk3rzoHhwU4hb94w==", "signatures": [{"sig": "MEUCIQDTySyh8/sOnA6w2SDxp13ap6u2156Kjxu5hbXDbW2UJgIgGrjFwKcKaEnbtgd5DfoDlzkerBHapoBCLJ0iuLTOv6o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["lib", "index.js"], "scripts": {"test": "mocha", "pretest": "jshint index.js lib/ test/"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"debug": "^2.6.3", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "source-map": "^0.5.3", "istanbul-lib-coverage": "^1.1.1"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^3.2.0", "jshint": "^2.8.0", "ts-node": "^2.0.0", "babel-core": "^6.2.1", "is-windows": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps-1.2.1.tgz_1495919581363_0.23226014152169228", "host": "s3://npm-registry-packages"}}, "1.2.2": {"name": "istanbul-lib-source-maps", "version": "1.2.2", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@1.2.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "750578602435f28a0c04ee6d7d9e0f2960e62c1c", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.2.2.tgz", "integrity": "sha512-8BfdqSfEdtip7/wo1RnrvLpHVEd8zMZEDmOFEnpC6dg0vXflHt9nvoAyQUzig2uMSXfF2OBEYBV3CVjIL9JvaQ==", "signatures": [{"sig": "MEUCIQDf2rtx7scebxZYhxRbfi+3LTV8yc850XvrfUecdYiw1wIgTobgw4Dwc1kbR3vvhuk08cA2IPShMUz5y6deNkIZgqs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["lib", "index.js"], "scripts": {"test": "mocha", "pretest": "jshint index.js lib/ test/"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "8.7.0", "dependencies": {"debug": "^3.1.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "source-map": "^0.5.3", "istanbul-lib-coverage": "^1.1.1"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^3.2.0", "jshint": "^2.8.0", "ts-node": "^2.0.0", "babel-core": "^6.2.1", "is-windows": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps-1.2.2.tgz_1508612368677_0.8676236979663372", "host": "s3://npm-registry-packages"}}, "1.2.3": {"name": "istanbul-lib-source-maps", "version": "1.2.3", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@1.2.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "20fb54b14e14b3fb6edb6aca3571fd2143db44e6", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.2.3.tgz", "fileCount": 10, "integrity": "sha512-fDa0hwU/5sDXwAklXgAoCJCOsFsBplVQ6WBldz5UwaqOzmDhUK4nfuR7/G//G2lERlblUNJB8P6e8cXq3a7MlA==", "signatures": [{"sig": "MEYCIQD7v265LKhTg5BPFjkvHV3R4cg8Fu7aAqLltYJfDvDy5AIhAOFz3O+4cFpNH1u7WVFsBGVelaGcWQHhuTg1cINt0248", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22349}, "main": "index.js", "files": ["lib", "index.js"], "scripts": {"test": "mocha", "pretest": "jshint index.js lib/ test/"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "8.8.1", "dependencies": {"debug": "^3.1.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "source-map": "^0.5.3", "istanbul-lib-coverage": "^1.1.2"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.4.1", "mocha": "^3.2.0", "jshint": "^2.8.0", "ts-node": "^2.0.0", "babel-core": "^6.2.1", "is-windows": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_1.2.3_1518500921426_0.7841611936503754", "host": "s3://npm-registry-packages"}}, "1.2.4": {"name": "istanbul-lib-source-maps", "version": "1.2.4", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@1.2.4", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "cc7ccad61629f4efff8e2f78adb8c522c9976ec7", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.2.4.tgz", "fileCount": 10, "integrity": "sha512-UzuK0g1wyQijiaYQxj/CdNycFhAd2TLtO2obKQMTZrZ1jzEMRY3rvpASEKkaxbRR6brvdovfA03znPa/pXcejg==", "signatures": [{"sig": "MEQCIC6s/lQepWANkMph+PnzUlcSFYVdbI0mx/Y1EViX0zwXAiAQQcMh+5u2o4zGWt/zqsjgbIJi3zUgfTX4ZOeaf4PTlg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22578}, "main": "index.js", "files": ["lib", "index.js"], "readme": "istanbul-lib-source-maps\n========================\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbuljs.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbuljs)\n\nSource map support for istanbuljs.\n\n## Debugging\n\n_istanbul-lib-source-maps_ uses the [debug](https://www.npmjs.com/package/debug) module.\nRun your application with the environment variable `DEBUG=istanbuljs`, to receive debug\noutput.\n", "scripts": {"test": "mocha", "pretest": "jshint index.js lib/ test/"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "8.8.1", "dependencies": {"debug": "^3.1.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "source-map": "^0.5.3", "istanbul-lib-coverage": "^1.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"chai": "^3.4.1", "mocha": "^3.2.0", "jshint": "^2.8.0", "ts-node": "^2.0.0", "babel-core": "^6.2.1", "is-windows": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_1.2.4_1520188978623_0.03146353404904567", "host": "s3://npm-registry-packages"}}, "1.2.5": {"name": "istanbul-lib-source-maps", "version": "1.2.5", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@1.2.5", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "ffe6be4e7ab86d3603e4290d54990b14506fc9b1", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.2.5.tgz", "fileCount": 10, "integrity": "sha512-8O2T/3VhrQHn0XcJbP1/GN7kXMiRAlPi+fj3uEHrjBD8Oz7Py0prSC25C09NuAZS6bgW1NNKAvCSHZXB0irSGA==", "signatures": [{"sig": "MEUCIQCWPd0yaTwhdu19oRrVzgxys1g5TDOU7hvtfW4qCDqf5wIgXtBlNYTfAUfKnsjvjUAJgO9LFrFL3Ff4X6O4aXjAdi8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22995, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbD0PaCRA9TVsSAnZWagAA4iUQAIwwteyuw316umqhkum8\nGsEpugxla+WIIVsJeQd8SBw9z5mi+15WP+LYcz/wnF8wVl81PCfTvhxjlS4R\n1le6uvfgLXAuqI/XMvdh8Gxu8f84nCQ1BGiA7329i0/2FVjqaKdPAPOazDFs\nzIv4O/BmIWuXirpBuFraDZHZ5XCS628VdKnAOx65nMSG3iNlqz8qnzVPihIb\nYsS+FTnJ86rSugQUDb6F3gOVBg1c/UNtdEiyGahNvsJYiS8nYB3OfAfrA962\nte5km5Y4YrF7Tiki5R/8QDwfpTzwCJq70PJMR8SVC0bPUc2FMm3/L+4AxwX5\np62WsnE30lheFEG+yT4uAELI0+gaGdMCr41r+O0QcBdgf5/3SY8Yq7klma0m\nDSHIHkjUD9PNdCu7ijroMj4JrBOqH1+m54atfWC/l5qnOE5OdKVBvlejWto2\nCGRo5Oh7lfxnMVZB5RW1xRXKNL+mmKFXoJZPo+XsxMoM5XvEAHaaVhgDiiHu\nWbDSA/Y2eXb7wpofp6G64cVIlqhmNnryDxKXgi0tEpE3Pz5kokurbUBpcu0Y\n6L8lAa4CF9x4Sp5O4Lt0fk8bOX+hivSi+FSiQs3Jmmg8GriTTfwncyP9LQ87\nT3Q67KbdTDPo7Q/DTngJ3c75dUjmnApALuWHYiRq7t/my5JByPTPjGGp3R1L\n4lQ4\r\n=WxwY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["lib", "index.js"], "readme": "istanbul-lib-source-maps\n========================\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbuljs.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbuljs)\n\nSource map support for istanbuljs.\n\n## Debugging\n\n_istanbul-lib-source-maps_ uses the [debug](https://www.npmjs.com/package/debug) module.\nRun your application with the environment variable `DEBUG=istanbuljs`, to receive debug\noutput.\n", "scripts": {"test": "mocha", "pretest": "jshint index.js lib/ test/"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "6.0.0", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "10.0.0", "dependencies": {"debug": "^3.1.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "source-map": "^0.5.3", "istanbul-lib-coverage": "^1.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"chai": "^3.4.1", "mocha": "^3.2.0", "jshint": "^2.8.0", "ts-node": "^2.0.0", "babel-core": "^6.2.1", "is-windows": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_1.2.5_1527727066170_0.975916517099676", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "istanbul-lib-source-maps", "version": "2.0.0", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@2.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "4f66403bdbf9f468ccfadd4557878077c26335ba", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-2.0.0.tgz", "fileCount": 10, "integrity": "sha512-jenUeC0gMSSMGkvqD9xuNfs3nD7XWeXLhqaIkqHsNZ3DJBWPdlKEydE7Ya5aTgdWjrEQhrCYTv+J606cGC2vuQ==", "signatures": [{"sig": "MEUCIHmyEktPlIMwOJ9oPKPqM530+ObSmJzesQZE4tUG24LjAiEA4ryPGP4ecqDhwFeBZNr2Zuxl5luHUZjj8v3qcnZ9Xjs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23478, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbFy/KCRA9TVsSAnZWagAAAoMP/jbYqXuq43cOovRb24qz\nzglCeZTJ5wlmUFeVUBWXmqVzgEgaLrbtQaxd1HzJRrZyBegC4MjLgaYb6h53\nQb+jfKTWJfqtt//v5wyO9V8a4hN5vULE0VWMlBytCr3hV7VafRjRKOkkM9wO\n6N6KkoC/HUAek4U00Zq0anBe98ixQRsBMIm64J6685ZqimJBFtjm8Kn+C3VL\noBRpkdJqGwG82IIQO20kCLlpVTYpuz0UIk3Per14HA+P4UdJVAwwCEi4QgZ2\n+RPHQCB4uw7sfhFmfen65a5Ebgc4OsXQaGB9SaSOAT7cESP1DbxSHpvF9QfJ\nQ4qx/e8S8y4yYqJUrNTp+n2dnz2eU2pzHf/ciJPzv/HmZP944nRelmdY6GLG\n1YnW3wF2maZifSvcoiB41pGKbf9UEJX99ecGPJi3EU4CUs0gnK+DSszTsICw\nEnQOVlyLjlC4pnOfipV07F6DzZ+Jrnw4IXySbod9kmIHlS81Xv2YfzNdhRW9\nEhQlwbVKfZGSVrsA1nHVZxiRDcC0vlVa/TOgkmylxG69KKjGDrbAJblO8T00\nDMJrCmhgKKTY6XuTSyfKkpUPVRXDrjLdFATISSMPcMSAye9TNIq736GnEUw/\neTsOMcVo7DF/TgV4hUYbzEceqg0WYquTfiX04k8f+smEIbmPdGvSTyIBUtAC\nveOe\r\n=uypx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["lib", "index.js"], "readme": "istanbul-lib-source-maps\n========================\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbuljs.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbuljs)\n\nSource map support for istanbuljs.\n\n## Debugging\n\n_istanbul-lib-source-maps_ uses the [debug](https://www.npmjs.com/package/debug) module.\nRun your application with the environment variable `DEBUG=istanbuljs`, to receive debug\noutput.\n", "scripts": {"test": "mocha", "pretest": "jshint index.js lib/ test/"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "10.2.1", "dependencies": {"debug": "^3.1.0", "rimraf": "^2.6.2", "make-dir": "^1.3.0", "source-map": "^0.6.1", "istanbul-lib-coverage": "^2.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"chai": "^4.1.2", "mocha": "^5.2.0", "jshint": "^2.9.5", "ts-node": "^6.1.0", "babel-core": "^6.26.3", "is-windows": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_2.0.0_1528246217547_0.6117702019109958", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "istanbul-lib-source-maps", "version": "2.0.1", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@2.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "ce8b45131d8293fdeaa732f4faf1852d13d0a97e", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-2.0.1.tgz", "fileCount": 10, "integrity": "sha512-30l40ySg+gvBLcxTrLzR4Z2XTRj3HgRCA/p2rnbs/3OiTaoj054gAbuP5DcLOtwqmy4XW8qXBHzrmP2/bQ9i3A==", "signatures": [{"sig": "MEUCIQCT6z4M7sFesxnnYjJmiF4AXSzMu3R2OcX6Mwd5rNd7lAIgA3JkkGMiEpQCocJxSHWm/OLAjakhzWhNdSkLg4n+2Hk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23745, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbQQ3ECRA9TVsSAnZWagAAX1QP+gOYvNxsIs+0hh1WlvPo\n9m94b6I9IEd19CKwYpP7Zbg++n3nPuNPJSjVV/axbKbQb3q0e09Fdk25uFQz\nWt8/AEid7sjpiaPLuA/3gGt8h0AejHTK8B2Zrxvfu19gSFRbAis5EP1oqMOk\nCUGz29jR3nbASZDDhT2dOBSSvn/GH6gkC5ZqS3gIKtoy2rlJVFpFhlJcJs07\nctjOKpRmuudhGOBT/rNsiVA2c3S+R4W+3ZhUChUTQovbJkPwWpBFMnfQylCF\nMcCH6celxW37lygUFisYe+5jtQOF+ycB1JT9KmKMIXGTkaWP5UYIqRUycEPw\nxeyqzxglGR7xTWdw4I4BUm3tGIt1i4aKzYEkkNdCpKeGk9VpmSE9RDB4s6UP\nIxScHL4MR4Ppib+P7gxkrVP+C1oOnk4+OlHd5C9uT/qCiU1PZWDVieAFaGGg\n9yWswZCs/nGBWF/w/iPMR+0iogiPJ9HzMUo7DA/Zn9DYNsPAMu9fixn861WR\ncNPciviYvAORXnui0fxrMJZmbcIvC+iNHyfHOPlK6XY0TOVzvv0IYEkee2O+\nNGPoiQ4VbO/jqNmNuyQ+VudijtSCFAAmJnowmfNHkKoJwL77p+/2S4eLucw5\nqBBlXgymi72f/Mg99pLPI/WBSZdeWSvAiz1UAE9rWFlvD4QROjxi9oV9zN2v\nKMdF\r\n=zmQS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["lib", "index.js"], "engines": {"node": ">=6"}, "scripts": {"test": "mocha", "pretest": "jshint index.js lib/ test/"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "10.2.1", "dependencies": {"debug": "^3.1.0", "rimraf": "^2.6.2", "make-dir": "^1.3.0", "source-map": "^0.6.1", "istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.2", "mocha": "^5.2.0", "jshint": "^2.9.5", "ts-node": "^6.1.0", "babel-core": "^6.26.3", "is-windows": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_2.0.1_1530990019922_0.38443265314270003", "host": "s3://npm-registry-packages"}}, "1.2.6": {"name": "istanbul-lib-source-maps", "version": "1.2.6", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@1.2.6", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "37b9ff661580f8fca11232752ee42e08c6675d8f", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-1.2.6.tgz", "fileCount": 10, "integrity": "sha512-TtbsY5GIHgbMsMiRw35YBHGpZ1DVFEO19vxxeiDMYaeOFOCzfnYVxvl6pOUIZR4dtPhAGpSMup8OyF8ubsaqEg==", "signatures": [{"sig": "MEYCIQCRX29cdT3Zh7DqmpdTtVLIUuy5YMslM8eduC2Z8PmDQwIhAKHUH8cO9NAI9a5jHRYQyxfmcYW+7K1xMYNpo+fe5bu4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23217, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbkFiSCRA9TVsSAnZWagAAocAQAKFtZRIlWjFVh8vi+UjL\nMR9nWMTM62sTAO9VYkchR2BJkG/x27AnSRN/chCuAxUnhixE81mf3lfhyCMB\nOoBhykjUvHY2JnZbJz/2YusvoLwG+V7PUCLJjKkO4ygfHoG/DnEru0o0lNk1\nn7R9qy6aY2j8BblfAeedFn/OmNPfB/pi/sL0o7XwGDzpRndC7La86tceOStO\n2NXgg7DALfZPlbYVLQBbZuy1r/pfgYFozTbIfl6mCze3z0hh3Z9zKHQjiQkc\nPaZmkQtNCln7hBb2OLZt6vhOOo/JUqE78dCPPDttyF5suItLK9U2+Dg8+LAc\nKHcNNC2/hZ5eNhj3+GWoYF1z+Vc+WQ1hizr5/lVuthp7LygXwXVERNXZgwQr\nAcHiKPNaMZAbJ7/jstU9d9qhumfTUAwqpF/qU3ycE+amUZpLnTGGx0d/OiMt\nbJ5nHiG5IpaYoh90dsy1GD9mQpvA9XjAX8wklOO5uN+OcKzpwCrovpE1eEAI\nHYsTRBTy9a1rEbDM9AWneBp5d9aHMUsjt0ag5Ubsf59lxCvANRURejMfJ6Yc\nmllHgmoJGigYBdxx5qoeunXs91vhJ85ZPlXQhTKzeyOADlikAhZX6x+7xT2Z\n0gCF5sJltngwAY9QB1i5odsaxNzeFHESACh0v/s1ZEvrRQG2ypSS+2fxEUaX\n20Ey\r\n=7tcC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "scripts": {"test": "mocha", "pretest": "jshint index.js lib/ test/"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {"debug": "^3.1.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "source-map": "^0.5.3", "istanbul-lib-coverage": "^1.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^3.4.1", "mocha": "^3.2.0", "jshint": "^2.8.0", "ts-node": "^2.0.0", "babel-core": "^6.2.1", "is-windows": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_1.2.6_1536186513596_0.10045853966433849", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "istanbul-lib-source-maps", "version": "3.0.0", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@3.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "f01f7388b7e9079832f7a9d54980413dff232659", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-3.0.0.tgz", "fileCount": 10, "integrity": "sha512-8mTTW1xhk3s21KX3awhWMB+GUx9z0MPtW9Z0dpchok5NAn6IaVjxws2dV98ZvLvL+Kc2HLRxIV8XRptchKN8VQ==", "signatures": [{"sig": "MEUCIQCkUfdT63J3P4urRbIUS7o3l1PgpaWLVVptlBrRPK3kPwIgAoiMBtx9VTXUNDS0ORSPHZ9+CUg5dregF93SUEhY4K4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcGcTpCRA9TVsSAnZWagAArPAP/ikXc/ghi7HAX4F2octu\nuNGLvz3ar2W52c7vLiB7/aPrIXWMpdlREU7opRtnjFypqJJv2pNtFCF0IP/M\nkl/Yj0XaS3KE20dXozs0yYNxcpx3NnDewX5b5M2m4jzXglS3oDsgiAPkb8Oo\nxOv0i2C2niC5JI6UVm0F+csrxIbrtFghMlpgmkccs3kzbr+ML/eh/jTzNmlb\naWt4CJlMQ/m6KiDT6+drosczj5h0n7G3R3PR2kE3kAxQEv0fJTdko8ptU5v/\naOrhEA5k93rwspqiSD2+YTOZ5ORqyLSEuFTyLizlFPwcK0bM9onFG6OUzGHq\nY2eY8jHSdKG3ua7wOXjxdF0aSytXmPbnuXtBdOX+sTLnEi/NxzpA+UMaAWmM\nnpAvqAGAk1BiLPSB0w4wMrUZ6roLwJNji8RGq20heutvy+QIyq8nOafvB0r1\nEWNU2E768/j+0sp8PSf4RHbMHZI/cQocheTYPjtfG18ySDJ6fPtCYdenjtWL\nKCjt3zk2pNYMBeeVMrM+wSNw/7jZ73dZRGAU38gWAoNMlbo0JKDlopD6fekA\nRtpj4mLgLakVgCNW68hYM4HUZMCH7TtuSVH4ip44xxaa206I9/N/e4remB0P\njfSHHgr6FX9Ro09IGnnpMj5z7lhBkH99v/evO+DDzlZXtljBsu+63k0OvRRH\n0AjI\r\n=WgVB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "readme": "istanbul-lib-source-maps\n========================\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbuljs.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbuljs)\n\nSource map support for istanbuljs.\n\n## Debugging\n\n_istanbul-lib-source-maps_ uses the [debug](https://www.npmjs.com/package/debug) module.\nRun your application with the environment variable `DEBUG=istanbuljs`, to receive debug\noutput.\n", "engines": {"node": ">=6"}, "scripts": {"test": "mocha", "pretest": "jshint index.js lib/ test/"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {"debug": "^3.1.0", "rimraf": "^2.6.2", "make-dir": "^1.3.0", "source-map": "^0.6.1", "istanbul-lib-coverage": "^2.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"chai": "^4.1.2", "mocha": "^5.2.0", "jshint": "^2.9.5", "ts-node": "^6.1.0", "babel-core": "^6.26.3", "is-windows": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_3.0.0_1545192681090_0.8319806712490689", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "istanbul-lib-source-maps", "version": "3.0.1", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@3.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "002936e1106c4fa49714a946e6c63c1098b52e11", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-3.0.1.tgz", "fileCount": 10, "integrity": "sha512-DBsZMpCwCPewRCmyd0FETHtzarQK/kKejQkDPBqKPwLYQmhs2p6a7yytfVDqib7PgXGSJZNTc1b6B3jl9G8FqA==", "signatures": [{"sig": "MEQCIE9rkC8QtEGjvfYNJpiUQ/Fdakd7zn2ftxHpJKwT9DhVAiAEizSXFToO2ibSKWrHg6RHtCv2uaH0DAXfN3BX70w3Pw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27866, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcIXvpCRA9TVsSAnZWagAAdKEP/3dwBCpfQSXKG/e2ku0V\ntefmC1NJnw7v/FD6w0lrBWgKzmlwjo9euUIKtKixvoTTXdFFUi2OE4FnZkpJ\n6z6r5EbE+bqx6zbQbUD3C7JdxbC8Ii0hwHhIuuOuGIfLIGc6C7qSE+8rBcnS\n9VwTDWSkPnpMYflWTyFlCP/GhkCcjAF1XRPPAa+faoz1xUce4vYpVpz5UZUA\nqkbW0hpzOZk6atEINu9zVaNeSkCRnbak2oBIL1VmMj9UkJfVC6g1S69ycAhB\nNyKo4qSf/WwJ3ShrxJPfT76HaxjIq23MNu8lX+izM7ms1m6GghUIJfQF6hZk\nIgfBYdVuxJKwgnBA7zH7VEhhC5yb7UMjcuNT8Cz3SFW5ctswuZLR77jBLuFO\nEB46L55unaHzttYlNE5BawNftiWdcAjtSaHWBRm92IBueSMxiv5gJa3itj+K\nk0i37j229j0CuWgFal3oTCsvh6gaUe3HIlDCQJgrgBdcRmsFTZ+CwlMjyO4C\nMoATnxUh2bozgEgjcO5f9joFX2+dgSrul7VTkPvfBQ/GGycGKBbZ8B3nhwIJ\nrQvQ6gwVDpcm+fZfaWOhXT8bRj7WtJm2my4v0JMivwvVPeZIe0lFCcYBWTWO\n7yVnZcXOfgZbIFEr+fAi+/xFvk98do2l+iEb+HWyHEViHqHvU4XOaC3DS+at\neVtF\r\n=GHBE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "readme": "istanbul-lib-source-maps\n========================\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbuljs.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbuljs)\n\nSource map support for istanbuljs.\n\n## Debugging\n\n_istanbul-lib-source-maps_ uses the [debug](https://www.npmjs.com/package/debug) module.\nRun your application with the environment variable `DEBUG=istanbuljs`, to receive debug\noutput.\n", "engines": {"node": ">=6"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "11.5.0", "dependencies": {"debug": "^3.1.0", "rimraf": "^2.6.2", "make-dir": "^1.3.0", "source-map": "^0.6.1", "istanbul-lib-coverage": "^2.0.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"chai": "^4.1.2", "mocha": "^5.2.0", "ts-node": "^6.1.0", "babel-core": "^6.26.3", "is-windows": "^1.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_3.0.1_1545698280625_0.14880020826490425", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "istanbul-lib-source-maps", "version": "3.0.2", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@3.0.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://github.com/istanbuljs/istanbuljs#readme", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "f1e817229a9146e8424a28e5d69ba220fda34156", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-3.0.2.tgz", "fileCount": 10, "integrity": "sha512-JX4v0CiKTGp9fZPmoxpu9YEkPbEqCqBbO3403VabKjH+NRXo72HafD5UgnjTEqHL2SAjaZK1XDuDOkn6I5QVfQ==", "signatures": [{"sig": "MEQCIGW2mCEJP8HX26Vzo/byVQ5lM8a0BPgYr0QQMsYaib0ZAiBF+u0vFIcEHjkJm9RLflcXdREyCEvG6U/6hSX1puazMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28059, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcS8iUCRA9TVsSAnZWagAA7CMP/3tp3sqAXuvOd7z1DHDA\nCDPBUix7vDiZ3ICe+9n8b8+6rBRPnfWFSPEtEvcni8lQCJVLJKEdkxNBEdrp\nwR/zA0LcV4xKr5rXxVlJk9Ak50t4sOfM6hII9iok6BHCMJ3HTpLxxHP+OSfL\nPus5rJToXePNsaYuwCGjSMwUUSFhxz11XvXedCgerI7TF1+oXol5qpnYNzjj\ncN/jU5d8uaBcu0JKc4VC/9lef/cegdnaYuciKA6R+86vV0s7+b7eyONFzTgP\nTHxtlG8L+CysJ0oe204lmTLH+RVBb6RYOQLePIAqVhsBGcdBgAd7VOHV9IaK\nL07VDckqX2u29tTvBNphuurLX3zUvzaEO/hjcygx7FuiuGcc2Bjpu4C2ZfQK\nRE364fZzSMBBfsMKAXQGr/AiArW2EN2GTtQRE5cKmdMkfbL5Cojaql+rNQ8L\nLwXlHT9z2qodwemNrUnPRbyVZjpeBzblhLodKWCdNBLc0wKaPZj5mX4d7Prq\nSmB1pKODS098JDTr++UwXM612Z6YlxZJxETRTYOjcyqu2GCL77plIRYzXBev\nSrBHTEh6JlKVX2pmfO2qkwKIb00TVUT4QTLeQdXwAP2ZwBG2LElWb7yBAEV4\nlmwTUHpi6niCAC/46IRDTJ2Y0W17K1nhX6d6WRkbp79v1FV/LEs++otvILM8\nO9tm\r\n=V9pH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6"}, "gitHead": "7875defdc3c3640787ac5d83700246de119e8b83", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "lerna/3.10.5/node@v10.14.2+x64 (linux)", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "10.14.2", "dependencies": {"debug": "^4.1.1", "rimraf": "^2.6.2", "make-dir": "^1.3.0", "source-map": "^0.6.1", "istanbul-lib-coverage": "^2.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"ts-node": "^7.0.1", "babel-core": "^6.26.3"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_3.0.2_1548470419643_0.2874692489175008", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "istanbul-lib-source-maps", "version": "3.0.3", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@3.0.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "1fb77d6475f5a69e2f02d1dfdea579f6551cae2e", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-3.0.3.tgz", "fileCount": 12, "integrity": "sha512-s5+EmMuryN7oKML+z2pQgUi1ZkGwBwMEkOZ2dW1Jg3ySeImHgO25SycsibMcjBWYAXZ4V1M23rkYEko/jctX+w==", "signatures": [{"sig": "MEYCIQCyxhJdHutUb43gDp0AcbcCsgr7pNVvYjNNkYx4ZmWV2wIhAMfsDc92TFlIESwRbz4D4GCZ2QOHrEXhEN/EauOXzRQL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29751, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchwfxCRA9TVsSAnZWagAApiYP/iqwnkbDMNQRD5gj4042\nSHE4vgqji1cWxFO5juCZp53ez8MVRx36yBbPeGSeWB5pVYYyHJ5187yEemsA\narYLkGQgWFqoU8n1t71WbBpJtWrb4cDwNHwEniEL0zUTfdsrDAlBMoEEWty/\npE6Z6K7h1mGiPanUUKV3DXSuK/iuaGqP8lR6Lk+eJe4w3zi3dOuvC6f2w6dE\njva0jFLPDRNJX1uyS9ez9JKwULmPy8oYvKr5hkMPSNtOzpcFFNyZjwmSxd1x\nDtoY4ZqsfNe7oqaADApsDw1U9d+AJsWxY7BWi0Lcp6qQCbuHXk+NmewAyZfZ\nI6/Jgmh0uwdT0JHmF6sQNFEDKzLjnu7CwHL4rP6TGtD7Zjws/4wr5mDFFpC/\nUMBVE88vv6xvpdSudnE95/sKG/uFji5oda1vcMvFth9wL+EQ6vD2x+kEKpEY\ndDXkj773zK93gq48INtrrCASvADlROMMIw3N0ySzN3V5V31y0deWBBTCQkjY\nmFV3+SB9dZ+b6ewkhYzN+TYHg49+Ia8ivonKzuEJvUU3n2De22ZmX2W/fB4H\nFfPtUifTCSRLzfeFHGxqAJ3jVsjyPYsdMbUmXv33I/IikVxv2T0RgxWN7vRD\n+VD2XcUL6zWfEccbDanfGlo3ms9k8aCcGN6DGQagZPQ1OwVDswTY7eNbgUZe\nGVjg\r\n=ORQs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "readme": "istanbul-lib-source-maps\n========================\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbuljs.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbuljs)\n\nSource map support for istanbuljs.\n\n## Debugging\n\n_istanbul-lib-source-maps_ uses the [debug](https://www.npmjs.com/package/debug) module.\nRun your application with the environment variable `DEBUG=istanbuljs`, to receive debug\noutput.\n", "engines": {"node": ">=6"}, "gitHead": "c81b051d83217947dfd97d8d06532bd5013e98c3", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "lerna/3.13.0/node@v10.15.3+x64 (linux)", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"debug": "^4.1.1", "rimraf": "^2.6.2", "make-dir": "^2.1.0", "source-map": "^0.6.1", "istanbul-lib-coverage": "^2.0.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"ts-node": "^8.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_3.0.3_1552353264541_0.9268422858048806", "host": "s3://npm-registry-packages"}}, "3.0.4": {"name": "istanbul-lib-source-maps", "version": "3.0.4", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@3.0.4", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "b892b7b09819b690018d33adf5e5891965c38238", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-3.0.4.tgz", "fileCount": 12, "integrity": "sha512-3hW4ZQzngD/yXNGiBuiLlNkFP0EjOGPozAUcdi/tNOmNe9Os1lhVf6OzaHCRGIsi/64yTuEyfOJfCde5vYpbYQ==", "signatures": [{"sig": "MEUCIFqJedU33FCgYsHCKayebFwULcFQq2paelx6zXoUi8X7AiEA6X0UV3ekvtwyVEBp/2aiIuEfS29M4DaT24SN30/mH6o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29960, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpPXCCRA9TVsSAnZWagAA5t4P/jV7yUuRfUuA+5+8b0DS\ntWfA2QWUO/D6fWOQJPeUzn3otXaX5MD2eKtP0uSlJoGWjutDrWaOAoMHcRSC\npJaZGTc69ypuk5FxdbAO1MPaZVVNl7Mt77W3Ri22Tl8r3vVqxd5TU6HYDz6Y\n7MoSx+ri7l6iMas51GMqLg6EHYgkmU4Fe37qwuo1BA/lMJkx2m4cdk37YRAP\nLOSudRrGiwkosVzXnTPl2V4Ww1GQmFTnPT0EmuYYas6GhBWw/1CuV1TplG8k\ngwMFKVAwGTOBixZauolxtgswEw8yoVKdQEBV63bbhLwAtUVNnIf/nQCoXp0Z\n5d+IvB9VUB1cIx0qfrUB2Rn942SzjhmCBn6noHb9X7Sjzv9eHHFBW46bI/Ia\n0DL8OxgGKjEhMKtOlGJ6uMyWHHlpa3SAbN36/8z2EWash+O44l7DgsUFTd7y\nJ9s5z+YSyjJolc1KcXsXfipYYPhhvcsNRU8nhVjETRVlDcwghThhdNxExeXJ\nLD8uXbWA0OPDPWmEskFfORZo0LP8EMnLHBMUNlMtxcRu0FSihuNIeydbIQfg\nPpS++ir7YQDd8Qcvcsr/Gtx5T5guCLXC2HQQQ+BpZ0G7dpa73VAJXwZnGejN\nlPwTyZtE/S5CEnxMaHEw1gmZjdkqFy+vbgMgvO8SdNYJFCF6uQZjcU3XHUud\nPfAg\r\n=YFiS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "readme": "istanbul-lib-source-maps\n========================\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbuljs.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbuljs)\n\nSource map support for istanbuljs.\n\n## Debugging\n\n_istanbul-lib-source-maps_ uses the [debug](https://www.npmjs.com/package/debug) module.\nRun your application with the environment variable `DEBUG=istanbuljs`, to receive debug\noutput.\n", "engines": {"node": ">=6"}, "gitHead": "e8063c799d0854341cb3daaf91c58acd06bd501c", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "lerna/3.13.1/node@v10.15.3+x64 (linux)", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"debug": "^4.1.1", "rimraf": "^2.6.2", "make-dir": "^2.1.0", "source-map": "^0.6.1", "istanbul-lib-coverage": "^2.0.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"ts-node": "^8.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_3.0.4_1554314689989_0.6642923168964576", "host": "s3://npm-registry-packages"}}, "3.0.5": {"name": "istanbul-lib-source-maps", "version": "3.0.5", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@3.0.5", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "1d9ee9d94d2633f15611ee7aae28f9cac6d1aeb9", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-3.0.5.tgz", "fileCount": 12, "integrity": "sha512-eDhZ7r6r1d1zQPVZehLc3D0K14vRba/eBYkz3rw16DLOrrTzve9RmnkcwrrkWVgO1FL3EK5knujVe5S8QHE9xw==", "signatures": [{"sig": "MEUCIF269Ex3qPqrRuRpG/auhGklYSXPLXgAdZwqs+h1s71TAiEA3l/pKCR3XXaSUGYjOMyDfn3E49CYJYOeH6WaNRdvv8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30169, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrSjtCRA9TVsSAnZWagAAcpYQAIfNwSBTMAsh5LShvFs7\ncK4KFSq0SoxSZ4Hak6eMPh+CimHznIGdLAOdeyUMDKWXTXGiDD5papV0gzLS\ng5HbyZQLIFhxaiH5vsIT4t61sZi7wbZyMt2QmJRpS1104DNqv/p5uj2ANrDu\no0eqJwQyZqnq7WAIkt0qxALoTM9ZefJNh8Zs6P4vCnrRcVO9f/vmJRcxZ83G\n74i1433yOrcxOtVmdJl/UdRDjRWLZVr2Ts88RU/1j7V1yyOlQkjMvb0QRC8p\nVHKgJ+Q5ADnn2xNolkwJaWFQywMC6dri6owxHXdNdKluUDZz+/uyoDGIATzR\nVDafOg/XrhQ9T5EfCkZj1Ba+2varr2MSCs+mooSoZ5L4AHiWw50odJ4WapWD\nQQUP0FvaK33GNHW9UNK3C80Mqa1UvVoMm1Kk1vkcinUGTHfOLa9URBnTwQL5\nvAUhbom7LX6Ctqndg9UxP6e4y60DjFdl4+9Wp/e9MyUExAFi5EPEIopEiHpO\n+avYOEihwm1EdpIihGv8LfilkdAL03B+00NFEkKoYg3m6bJL/kQfEUjCXjZV\nx36RyJ4UHpMgcFOVJoFIr3VBM/JAI6lsHKsSZPO3hzntuZH1GxaXBckyrOhf\nkilQp92p/CPDtLCGrciEi8N1V2Gp32pU7J994SiqrPEGa+1Ih7kaSW/jIbv/\nINR2\r\n=UMFE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6"}, "gitHead": "9f8aebf1f08159df20358d77fe98c809d2027c5f", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "lerna/3.13.2/node@v10.15.3+x64 (linux)", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "10.15.3", "dependencies": {"debug": "^4.1.1", "rimraf": "^2.6.2", "make-dir": "^2.1.0", "source-map": "^0.6.1", "istanbul-lib-coverage": "^2.0.4"}, "_hasShrinkwrap": false, "devDependencies": {"ts-node": "^8.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_3.0.5_1554852076242_0.1093442043901347", "host": "s3://npm-registry-packages"}}, "3.0.6": {"name": "istanbul-lib-source-maps", "version": "3.0.6", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@3.0.6", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "284997c48211752ec486253da97e3879defba8c8", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-3.0.6.tgz", "fileCount": 12, "integrity": "sha512-R47KzMtDJH6X4/YW9XTx+jrLnZnscW4VpNN+1PViSYTejLVPWv7oov+Duf8YQSPyVRUvueQqz1TcsC6mooZTXw==", "signatures": [{"sig": "MEYCIQC8IAgktt/DtzyOSlu9ldqxD+mpbv/vjMdL1oNocNJdXAIhAL+hWSxumUn+uYGBURL8ZjnsbK4bh/OKMzwgpBOgutyI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31314, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcwMrFCRA9TVsSAnZWagAATTwP/RqsddKokQUiQPsZ5xdn\np14T640alCt7cmEW09dMk2YdATs7/o5TGP5T4DyJFqv/8z7WkzTY39+WPKqW\nXPZJNwDt0gozBpNMgnWHwxwJ3jU9E0UNY6PBSKuvXfNlADsb+vmVKu8IwpOW\n5ayrtCnGuucpM2rtzvfQEeUeBdk3L0vao7J1+rbujCot9242XXgidYt+rHZ1\n2kQvhGPsygz80uJDKdan1PaZqJ98RtrRsb1FkwJuX+1JwTjiXBOpOUwcbxJx\nVwfsuwuTvsHCRbVeaV6+ai5S8IqKaIpS6WkuGjPGX0/YZdPZ9F2CMJFx/Yra\nhLeWaTRwQkacaWS3T+6Hvmc3gTk8SqsvqU6KGkuynczagvxkUgut+A8g/IsH\nmxNm3azR7kqho5wbxqDxkMDGp+29rR4648Tez6SY17B9DmXgTkc2DHWaxHY+\nM1fiLfaHeTpXYs2sjEXc7Q1IICRzMlRz+2X4DREyxGb69vuVE2S+uCTLMdy8\nV3M1XruBmpCSX3xn3wnWJZ2W3laJhDDCOt+gV85kz+kR9dYBluMP2uLZFaWA\nlQ44qWHbBBQEaJRd7/NSSJ8pVMK1Bcoll4M/YvL8LiPyfMTIDN+v3CdMK8Da\nwXO8t2/3U41GgcGouifGX42twyOCochTBtANtQtwqa7Dq9Su3gXYvHKBFb34\nMxr0\r\n=AWx8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6"}, "gitHead": "90e60cc47833bb780680f916488ca24f0be36ca2", "scripts": {"test": "mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "lerna/3.13.3/node@v12.0.0+x64 (linux)", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "12.0.0", "dependencies": {"debug": "^4.1.1", "rimraf": "^2.6.3", "make-dir": "^2.1.0", "source-map": "^0.6.1", "istanbul-lib-coverage": "^2.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"ts-node": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_3.0.6_1556138692413_0.3570204658116396", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.0": {"name": "istanbul-lib-source-maps", "version": "4.0.0-alpha.0", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@4.0.0-alpha.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "972615376842859314da0c66fc19a85cfa21f079", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.0-alpha.0.tgz", "fileCount": 12, "integrity": "sha512-kSdJnr8fxwAKOf9awN/SB0o5rKc/RI+gdLahWzub0AOu/ScWEgLQ9wFg1PnmlsmoTCZdwkI20V3zD4eRDfUSaA==", "signatures": [{"sig": "MEUCIB43WWQJU9T8B276/dY07aIyneezNiTc6AMhV0fwZt2aAiEAp9+WnQb1olU3ZazPYLyA/iz3/oZRmTXicYm2UKXVBqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCifKCRA9TVsSAnZWagAAp/oP/3Ram5yiVhnCNtRnU7nn\nDfReWsJ09qyocMjVNdzZIqxJVUCt0awbvRFwTYtWnWJbdJT0l5rHbsw2Ezyo\n9gFtX/kCY6CsCyOT3SrCULSpb7Tx6eZ8RiPBzXeQjQVK9JU7jgxNeVYlZYJJ\n3DcUMDp9G7NfQ647EQngoq6pEoVxiKgvu2Z0N8r72HXHhG0gz2EY2SO0W3dl\n81ymGQGkqVwkqSGAkgNpF6yD5zJQY9AfR7VB0ygeK+mXSioonaiwqiRxceWL\naX/Zjv6mwRPK3TmJ50Mhf/4A6HshEDL4LSAUB7cOIVAMPO9LNd4C1vVIY8zp\n82YCpzVFcYKtAqFbZzrnikFjqQ2VwQLCFSYsQFdv8MvcTK888Dt/ZE3fag3O\njy7lyuEgltxE/zdRvVSnLwscCyyoi5MUeN0d9HsEi97Q2wr3FYbAko8dMfn9\ngXOmZgjNcopX1DI8jRrYqjo0sq8hnPnnPc33pvjCQx3b0Aqf/5+cMB6K7a6n\ndRcjt/MrZcAel3qK632wKB3CSEKNM+AfiJSjvItnR4K/De4B4czDsP7UFGQL\nfsVPPHOH+UF737Shagilo8KSYM9rMe6QHNRiUh4Q7OT78fZQXkkYCHVwpU0Q\nMZXI+id+82eA3ZTZLZXACM2wM2yR3Q22hEAh4fF3a9XItXkLb40lQE8Sbx0B\nRPPj\r\n=AhRx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "readme": "# istanbul-lib-source-maps\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbuljs.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbuljs)\n\nSource map support for istanbuljs.\n\n## Debugging\n\n_istanbul-lib-source-maps_ uses the [debug](https://www.npmjs.com/package/debug) module.\nRun your application with the environment variable `DEBUG=istanbuljs`, to receive debug\noutput.\n", "engines": {"node": ">=8"}, "gitHead": "2e885073a9398806c9b8763dd39418398182ca34", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git"}, "_npmVersion": "lerna/3.15.0/node@v12.3.1+x64 (linux)", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "12.3.1", "dependencies": {"debug": "^4.1.1", "rimraf": "^2.6.3", "make-dir": "^3.0.0", "source-map": "^0.6.1", "istanbul-lib-coverage": "^3.0.0-alpha.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.1.4", "ts-node": "^8.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_4.0.0-alpha.0_1560946633854_0.24869260560341866", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.1": {"name": "istanbul-lib-source-maps", "version": "4.0.0-alpha.1", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@4.0.0-alpha.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "fcfd6965562d3fbed97bc231f7444e5305ff6272", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.0-alpha.1.tgz", "fileCount": 11, "integrity": "sha512-MSc2eM/ecOs7ojRNkMFB1Nj0DnapAXhcXD7fyjV55qQShgpysezVQuTFnzawHP6dJMNiYzwjymsBsTWbra0SjQ==", "signatures": [{"sig": "MEQCID47Z+lHGzxSlcKUqbiWJQ+gOpAZ6Zl3Xz7Nf8cZsLgDAiAdPIiohkjk0sOYZQzZAnVSoltvVkNVzTn/TLV6uWuKlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31772, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmT4DCRA9TVsSAnZWagAA91sP/02sMpt8CSc67Fnd3uAB\n1+GFuQitK+OrKJYcYDnv4vrn29ywES2xr67DA0a9tYHjtV0rIoKoLRNS6LKw\nziK8VZspM38DTMhPGArsKzH1UqQWiv/IHxutNpJgRslvEFU1COnv3jIvGlcp\nt2yE6lZozUmmhoFpuDkmlyrko1ZiSbiATEs3q+jp3vK2PhuhsGMMpL6YFqaU\nHTNQdk/ceFTbfBq/azx/aEjmdp5wCZS/SPSMQ8Cvltyz7EJeJKEuqiME8fVh\nD05EZTrxCuU9uttkHM0ApWgTXx2PN1s4vi9/oxqHFwYjbU4C3XlL9UZIbuNE\nSCF8nbwWI9HCzB/oMS/ZGNnbChaf+LZ40UW0LunzvALjb6zNCL7kwj8PCJr2\nB8nLregdh7ZP5KfIWZqGQ603KoF7aNOEHm2PJN0404nDHCCg79c7TUp6d9I8\nejx6TpFJlU6Kb+DWFKNAK+XDZQXSz7B2yFRZPDwp7iPLO0vqHmBm0F1N8OrH\niEoILdfwfPMFTF4YKjMD5BI2VMTV0SbGfJi85Pq6l+0Wei0wlkTQVQ48OHdN\nn4C6ITH/w00ipLn6OyqJBfAW0ByxdTQiAMjHnIRr3fBEul8Tbik3ipzNZoQ+\nrmUj1f/JXasH24wKhQ8ay1douLMs3Koc1R9tprLCEfPct7e3QVI02MDCRYwg\ngvT4\r\n=0tLl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "readme": "# istanbul-lib-source-maps\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbuljs.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbuljs)\n\nSource map support for istanbuljs.\n\n## Debugging\n\n_istanbul-lib-source-maps_ uses the [debug](https://www.npmjs.com/package/debug) module.\nRun your application with the environment variable `DEBUG=istanbuljs`, to receive debug\noutput.\n", "engines": {"node": ">=8"}, "gitHead": "4d5e777a9bc4847d178ad31f379307124cdd1e4f", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-source-maps"}, "_npmVersion": "lerna/3.16.4/node@v12.11.0+x64 (linux)", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "12.11.0", "dependencies": {"debug": "^4.1.1", "source-map": "^0.6.1", "istanbul-lib-coverage": "^3.0.0-alpha.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.2.1", "ts-node": "^8.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_4.0.0-alpha.1_1570323970837_0.21402481044207633", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.2": {"name": "istanbul-lib-source-maps", "version": "4.0.0-alpha.2", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@4.0.0-alpha.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "9de983a6596e5d4acd8fcc13e97106436d335f85", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.0-alpha.2.tgz", "fileCount": 11, "integrity": "sha512-taJF9b9Ddd2rQeajwCdQ+byO5li7MBFnebTo7BIIUSS4dVZHafHP5bVh5wPQ92FAjom9Rn0q3ymd3LykmAjYrQ==", "signatures": [{"sig": "MEUCIQDJ8rC/0ggWuJuqx4a432+BG/JPS8gFx+3UgsXC9bFewwIgENgUlxXMoDt88hbBe0dWcLwGQuGRPyuZ+oAKy6uTKPM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32192, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnTY+CRA9TVsSAnZWagAAXK8P/j9TmvCTWDnJG59h3EBn\n5rH1YPBfg/mhZk117ltgYpoEIElWqIILIIaqh6f2kqZwypl+gZD3dV8WnyBv\nQKOCMASlKOzm2yxuMiYdFlLvLTzeAurgZh4dlJ7xDsUrsJPBoxwlRrJD9bfb\npDQgCgIw+BnDErZlmXUGVJy2d5okGRc2TUbmzrDSC575Yv8E658ehuW+pzes\ngf8ISxnn0eO7LSr0XS4xMwPQVvcJjuZvThxH2gSUiVLfFYiTWlrtOfP+wYjg\nUrKK2kf9Z1GNFKSG115lvky6Bu303gspIG8zNBN4DOynAMI5rzaz0IFNdrmi\nN0DRd28QmNHqroNk/BMnoM+kYWfn6sH8J5oJKlQB65azYfJj1jMqh2qknerd\nky7YxV/DP4MEH9e6H0F4J9W1qppAAPmMNnjiS/qvDC8Ae3MCTD5ZtPA+3BiK\ntSiClLAaUGHUP3PjLeJKHFDpyb/K7UX4CAtza9W1gaIq66MQpKCVUYbK1n8a\nBRS2ZfzU1efvkyNe5Tqrisv2irTQ2ciZckTlJnHusSsjHTAil0bPIHLWVrUu\nc+CT+tYrx9EpTWRIDiOQLq511Wcccikf97P/rvXxzW5YZw9+s/cfCRhDH+ag\nVgUnsbfDFSLu15+2Aa5yT3R3Ewttf+XgITXHJb2ZkBNDnxUl18U21r7cdBwP\niaVM\r\n=nOkU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "readme": "# istanbul-lib-source-maps\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbuljs.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbuljs)\n\nSource map support for istanbuljs.\n\n## Debugging\n\n_istanbul-lib-source-maps_ uses the [debug](https://www.npmjs.com/package/debug) module.\nRun your application with the environment variable `DEBUG=istanbuljs`, to receive debug\noutput.\n", "engines": {"node": ">=8"}, "gitHead": "a8b355732367f7c4a740677553b9785f5e9eac61", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-source-maps"}, "_npmVersion": "lerna/3.16.5/node@v12.11.0+x64 (linux)", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "12.11.0", "dependencies": {"debug": "^4.1.1", "source-map": "^0.6.1", "istanbul-lib-coverage": "^3.0.0-alpha.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.2.1", "ts-node": "^8.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_4.0.0-alpha.2_1570584125812_0.6380128459414138", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.3": {"name": "istanbul-lib-source-maps", "version": "4.0.0-alpha.3", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@4.0.0-alpha.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "36e7000996c544e4f26b60f5b98fd4d0b3a7ef5b", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.0-alpha.3.tgz", "fileCount": 11, "integrity": "sha512-eH6jCOzGppm+Ive/Z0Q2S1oBPEblIV6oCcjemMC9dRxlERE7qhgh68mSaptj6VoKtV7N9Entr4CF279q2iZrJw==", "signatures": [{"sig": "MEUCIQCazgK/Ztfml6v1WJZQZkujXoavvEwtol0XNHYL06XhZwIgLMUOkOjgKcWfcoJit6YiNMuB9N8lw/6qU8MpV0HFRn0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32682, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdzuIqCRA9TVsSAnZWagAAL4MP/iqdvj+5b6bFeXuUKv9P\nOcm+U4zKjOht/SXdKL/eDhbRfYlgYqMGFhGrBVEX+NDA8f4OYrWi7fwKoMZy\n1tzRrBeyqVKmV7An11KxIUw2oLWeSjTKsObifXyN332x0L0NqJ4j3KA3QRon\nZ5yQ1xktyw5yqYO/SM+beTnje8LQPS/TvLy8pUOaB9PEZ9VXLNNuOjfKrTTM\n6xJrPwdIVjjEVp3Ae/qFljspranM1MmWTst8C6mhh2dawnJkcIEV43NWrHoZ\nyOqK6I3vrbSGaG7CDsyzDyi3R7UYK7OUPtfG9YMPnOXm4RCVzm/z1tex8GD5\numHx3n1HZf0uaqlElCPaH90/x9HmZIkDC8LXQyzaOw1I41AsOjUswidAVqLm\noJms9qerg4fpdfyHPnNZf9SlQG8cvFMxDtg+pM+e4G0XFpfdjq4LSBfLd56P\nWqOODYdLF/1krx7/XNfsRHgceQnywzhRkm7QDj8iyT8EUYD7+10+N/7k6/qV\nGCwBtljuFfG/4OwbMrPOtRAepP9py6NDDLYVHf+6lmvZ3JqRIZAYOibS5Up/\nxlHoeHx0KgmzTEnU2A2eKW22iLaow9KtDzYDEanR2BCa1/PgL1J4Mv9BlR+b\n++Ip0WbfDAJP/BUOCVm3DH4tPJCxpixMFdPJ8ZllStQGAUnEiPBAR+vwQxsc\n3TFj\r\n=lBKC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "readme": "# istanbul-lib-source-maps\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbuljs.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbuljs)\n\nSource map support for istanbuljs.\n\n## Debugging\n\n_istanbul-lib-source-maps_ uses the [debug](https://www.npmjs.com/package/debug) module.\nRun your application with the environment variable `DEBUG=istanbuljs`, to receive debug\noutput.\n", "engines": {"node": ">=8"}, "gitHead": "886e19c078f26ab0bb3662bea8265f61a8089c2e", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-source-maps"}, "_npmVersion": "lerna/3.18.4/node@v12.11.0+x64 (linux)", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "12.11.0", "dependencies": {"debug": "^4.1.1", "source-map": "^0.6.1", "istanbul-lib-coverage": "^3.0.0-alpha.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.2.1", "ts-node": "^8.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_4.0.0-alpha.3_1573839402486_0.12748147262358733", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.4": {"name": "istanbul-lib-source-maps", "version": "4.0.0-alpha.4", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@4.0.0-alpha.4", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "cd0e5fdcc57c0541ba8369c2132f01526de3cb0c", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.0-alpha.4.tgz", "fileCount": 11, "integrity": "sha512-T6SCz977OqDyT30dxME6QsrosclFxO2vMcFyVWxzhMECbBtOvErgpIpSIeFrxi6s9wEW2P5HYVDG2gmlM2P3Zg==", "signatures": [{"sig": "MEYCIQDaKCVRCQZtXCQVAqZM63LrDmCC/lXK4Q1AsEhOp/XC2wIhAJW0EdrdqkVD9lIgLlRQ0wZ0WNMPdtJsJpdmbCrrSaID", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd0A2iCRA9TVsSAnZWagAAFmYQAJddbXeNYcWRDTEK5uxP\ntLFEEHL8EWOYGqdzjrYtpvCUpoXPn+84Jm3F6E5/UMiMMPZoYKfGWzt6eH5V\ng2V8HRtt/V7LS9bnNacIxSPQmyCrJfNVa4x6r2HdOnU5GoHHzW7msmO6d9j2\nO7g6OpWiYGLUgO8J3rGQ4kFo07h7obtTl9MyGMWWoIGAETGtHZfrk8DlLb0r\noLFRTvD6KSUMqWvP4Ja+2GtPBfP+zQE6fW6m34z0SHUQX9i0NLeQm0YmaBkC\nar9LXa74qsAOaps4Yun5T6V9uEFKf0sPyCb7InMfWtdMDZYOaj/YMBd/oEji\nFQFSFsYWF8a7cMidjdV22Uv+9W9AkrS9dUFZqUKdGVd2sS9gQ3eTS1SiPSHW\nqdI/Xt9gaz+o/S87jHHJj5HzvOaiHE86PsW7Pp6tFMzZtbhO+4zdgPRWwRbO\nLvU2GZxAWmqW9vTDCleT5FgH5BI0hqf+6zP8uR7qd6DfeYXxskzwIBZnjRqy\naU6D7nZdnHiFsTkOuViHnDQp7IMI5wgBJcEUkRmvqcb0+fUgIUJKgJFb9Zvl\n+yknWEuFLYa4jz6jSy+FTZJpaEiUm1guOT90fh5qINKZakyoLcGtvAb8BhNB\ntlVxGpkptYcKrCrNj1N82Unz3AzaYfBLelhC1A+MNf3Dp0fIzDCI8Ua076bm\nuHEA\r\n=V5AD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "readme": "# istanbul-lib-source-maps\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbuljs.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbuljs)\n\nSource map support for istanbuljs.\n\n## Debugging\n\n_istanbul-lib-source-maps_ uses the [debug](https://www.npmjs.com/package/debug) module.\nRun your application with the environment variable `DEBUG=istanbuljs`, to receive debug\noutput.\n", "engines": {"node": ">=8"}, "gitHead": "6d0404072c692f6b5dfd79884ccf1896d44c342b", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-source-maps"}, "_npmVersion": "lerna/3.18.4/node@v12.11.0+x64 (linux)", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "12.11.0", "dependencies": {"debug": "^4.1.1", "source-map": "^0.6.1", "istanbul-lib-coverage": "^3.0.0-alpha.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.2.1", "ts-node": "^8.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_4.0.0-alpha.4_1573916065657_0.4119249102667064", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.5": {"name": "istanbul-lib-source-maps", "version": "4.0.0-alpha.5", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@4.0.0-alpha.5", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "d7e36f031893d8bad31909f50f3fe1832fc98146", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.0-alpha.5.tgz", "fileCount": 11, "integrity": "sha512-w/7MowpHJnhqCepvDd2kIy7HhsXXTAJcPhk+32VMwgoZ655Y2TxIHKRoESbVi3Z9AFTY54ajEpt09NOXE7cw4Q==", "signatures": [{"sig": "MEQCIF0kgtoGHDg1sw43P1pW3G5T5ami0em2oCj+twJZilTiAiAXSGVYvM5+M0eBcQgnR/2JkfdZEOB64l6E4OfrpHFssA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33273, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd69ZVCRA9TVsSAnZWagAAir8P/3AqYVcFki59ZLcBPeJh\nl98sAqrm93W+ZYvTXlQIXXbI3b4jtFpuR6TAeZ5k9uSpOrTYRfwjeURgzU2W\nhVIyQrOqBNj6x/0UyeCEwSQqA3BJEP7+jOC1LGnt2mNi51RkEpPJsl9hfDbT\nIRKWdVUO5l0hA+FZ3m7vMHVP9qBbt3BivkUw71lZwDiYvF/83lCTR70SdutI\nKBz6y9bn/hfdyEc1vrzYbD3rpcmfGuHayAII/ODWl05lET1pXqJu62Z+x66F\nhzMyqYzQbe0HtimLfQd5Mkx1sS1poHAaCv0Z0u1PtJTLpq7LNpNGjhPay2/T\n3zRLnMlS5UyvhX8ugkahwlSoU/Su+u4OzASTLW883iv1DPPqWlVdpWCIM+AP\nw47qbcnIsKmYu1qggfv6DVvnwKuPjo7G+F9znlu76mYKDl9iY0uEuPcZYa+I\nKVUvTPSHtbP/dvsb8ZZGOHmi4wfDMiNDeFZcPwIzORsF38/Bd9FyVwgflP3e\nLxt1Nd9tlTHymZCFND7QQgEUT2JThiwKw43HAeN1hJwj0FoMFvjSDgcEicfn\n6Tu8xAP4n7wZNrFd6s2rLvF0eUlwC+naBjohN/bTzltvPoLlayTQHbSTo9yq\nmAk8m41YJ6CiPQTWak1aBJpQ8R0WYwtHqTduC4xvVkVlb7+WdypRsKIOQaaC\ngrym\r\n=CrRf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "readme": "# istanbul-lib-source-maps\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbuljs.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbuljs)\n\nSource map support for istanbuljs.\n\n## Debugging\n\n_istanbul-lib-source-maps_ uses the [debug](https://www.npmjs.com/package/debug) module.\nRun your application with the environment variable `DEBUG=istanbuljs`, to receive debug\noutput.\n", "engines": {"node": ">=8"}, "gitHead": "9546946f0e4bc80714a5b318c59e459781f05550", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-source-maps"}, "_npmVersion": "lerna/3.19.0/node@v13.3.0+x64 (linux)", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "13.3.0", "dependencies": {"debug": "^4.1.1", "source-map": "^0.6.1", "istanbul-lib-coverage": "^3.0.0-alpha.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^15.0.0-beta.2", "chai": "^4.2.0", "mocha": "^6.2.2", "ts-node": "^8.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_4.0.0-alpha.5_1575736917467_0.7304544583941419", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "istanbul-lib-source-maps", "version": "4.0.0", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@4.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "75743ce6d96bb86dc7ee4352cf6366a23f0b1ad9", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.0.tgz", "fileCount": 11, "integrity": "sha512-c16LpFRkR8vQXyHZ5nLpY35JZtzj1PQY1iZmesUbf1FZHbIupcWfjgOXBY9YHkLEQ6puz1u4Dgj6qmU/DisrZg==", "signatures": [{"sig": "MEUCIQCqfuYyUNPpABB9JSTtaSXegLvtuNqa6h7n3PS9sIWCGAIgd4K/gpetnrjUr1BUHAUuU309YmB3c3Z8bLzrD7ALEpU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33473, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd/TUPCRA9TVsSAnZWagAAW5EP/3qTsFZec8kqWoEEiXqU\nh/DveecL4Jbz5bjm0H91g/m6wWRhnLtFU+Hr+GaiXs7yt37n76W6d3SMgfNh\n8FXGma0/rQoPfbDDhYv3jjg6+Z1wcKjgvfPTAWnlJHvYN2VGpOKMTtcZdcO8\nvPKORH3Lbt6dMp3msRdq32lzweFeBbYHR7ujYLiDbQY+l4h1lEYYWJPj2iTM\nnxT5cPjAIUDJ7AUMPrlLqGfRmcXW6wDR5D/sVJuY46/s6wWu9MfPny7wNDDX\nkVsmC8A/l9Dc28UXoRqP+XXOTg2rk1EJ9F11st2NYH+GWKjNCeC0SZrCCCsk\n+Pt3aq9xqucL/vUkp9DtQQK/tWoEREeq7MXFw9vUY11PM2hHPndL4R220wcH\nrLX7/NWuVuaATf5v6PJWhUYR9NBNLZ/tuuJu/gefvYciz1cd1R6WKm90TlLU\npJTxhrqXT5bC2GozqQ6od1xOfCCMyBN+9EG1NtBNMIrpgZ0wi0wDcI/DppgV\nDwgAP9yMR5eB8bmZMk5XXMbKErsBCeiIb6ytoD5JoKcp8c9XPBNlzATdy4+3\ng1liRqHNrrNudUboxIFE+6iYYNUSxNVCvs8+hu/uDo3D/a202ltJ3mvisbBm\nB1oCx6gKoa/LGVOPr0S4Pe/q1WX6dxTr4g/SCOA9BT4a1FFEgU6jbJU9brlq\nGdAY\r\n=WfBe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "readme": "# istanbul-lib-source-maps\n\n[![Build Status](https://travis-ci.org/istanbuljs/istanbuljs.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbuljs)\n\nSource map support for istanbuljs.\n\n## Debugging\n\n_istanbul-lib-source-maps_ uses the [debug](https://www.npmjs.com/package/debug) module.\nRun your application with the environment variable `DEBUG=istanbuljs`, to receive debug\noutput.\n", "engines": {"node": ">=8"}, "gitHead": "5319df684b508ff6fb19fe8b9a6147a3c5924e4b", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json mocha"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-source-maps"}, "_npmVersion": "lerna/3.19.0/node@v13.3.0+x64 (linux)", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "13.3.0", "dependencies": {"debug": "^4.1.1", "source-map": "^0.6.1", "istanbul-lib-coverage": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^15.0.0-beta.2", "chai": "^4.2.0", "mocha": "^6.2.2", "ts-node": "^8.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_4.0.0_1576875278838_0.11396942130962406", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "istanbul-lib-source-maps", "version": "4.0.1", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@4.0.1", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "895f3a709fcfba34c6de5a42939022f3e4358551", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz", "fileCount": 11, "integrity": "sha512-n3s8EwkdFIJCG3BPKBYvskgXGoy88ARzvegkitk60NxRdwltLOTaH7CUiMRXvwYorl0Q712iEjcWB+fK/MrWVw==", "signatures": [{"sig": "MEYCIQCvRW9b3lrYTu0ywDbexPjOUBqJKSJa1ZqcDfrIhzJc/QIhAOPf8JrUq+Co634gdAvVfZw+w94Kl5Xr3pO4VaDZiPzt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34127, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2wJECRA9TVsSAnZWagAAbbUQAJn1yhOPe1gueegXZsjU\nYEcB3ltt3jQlcyUuTMnIRwgb/aw9nIjA26sKe+Bw5jlISoBLPCUCu8P08gCZ\nmJGOK9bon5rfCQ8nStPYorub1k+ud95jcQ68zR+ILe2crPU0n1bX+hMh7vsM\nXIa4hdMMIuwD+3RhMUpdvMa5Wg6weh0ZrpvFdDzYwyjj7A+/89EBNYVDS60L\nGDnGi2JpvrHjXWb5xNeV7EDfflnwBzTEi0Fn6HThvS3bgfUdm9+ZRr4cV0ic\nPLG7sho+NEYpMBQWUtfuwv6jzyfvQ6tZf/ZrPcVoUi70F8LWA0D+3cmMZRaC\nH9YTG3ajM5GWrF6ZHeKvtqEfya4FbrIuEyGTyCobxoqB+pYpx8pYYJPP7pUO\nVYvkV0xDyrYBO+edk4VI7nt5NcerpJom60rvuLtXhbIH/Ydx7fIYfOmdW0n3\nKpc3LxS2PpbQVwC9gsUlo7fb3nW2vntpaHc1MFHiWjwrnwu+E1rHy5TMQZ5X\n3sAjODrOAKl4LHaq92VheBzt7hplcpUAjx4ro/eUdQKlSmjyY8/VJiqSg1X3\nPZkGE18giSGYtPwUGHi05PUPpIPoJJ9BVmQCqo5X9j3X6uos1YS+QYKk/kcW\nWN4Fv5954rfHEHXqVpgKwJ75E9CQ6DOhAJL2cQNAraJJLv17cfGcoKDWrW1z\nR+zv\r\n=RvpE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=10"}, "scripts": {"test": "nyc mocha"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-source-maps"}, "_npmVersion": "6.14.15", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "14.18.0", "dependencies": {"debug": "^4.1.1", "source-map": "^0.6.1", "istanbul-lib-coverage": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0-beta.2", "chai": "^4.2.0", "mocha": "^6.2.2", "ts-node": "^8.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_4.0.1_1634004069561_0.22442514520252366", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "istanbul-lib-source-maps", "version": "5.0.0", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@5.0.0", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "4b12631fd4e47852f224fb06a661d3d72f1ec738", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-5.0.0.tgz", "fileCount": 11, "integrity": "sha512-b5dJGsX6qObZmJPYc0pG9FaHCDvCKbEr0hAH8ENw7ZzBmOm4k+26sZnMiZFrEm9HSvXVi3JD3ctNAdT2wYmdxQ==", "signatures": [{"sig": "MEQCIC+vmwYVG+PIYjPbvTj3qxuHQlQK41aoqa+RLP+QIZ92AiBpPfl2DSjRmpo5xSYsTGatm2B6bFudtB3moNr0KUzYbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34987}, "main": "index.js", "engines": {"node": ">=10"}, "scripts": {"test": "nyc mocha"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-source-maps"}, "_npmVersion": "6.14.18", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"debug": "^4.1.1", "istanbul-lib-coverage": "^3.0.0", "@jridgewell/trace-mapping": "^0.3.23"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0-beta.2", "chai": "^4.2.0", "mocha": "^6.2.2", "ts-node": "^8.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_5.0.0_1708939888103_0.6030135257076517", "host": "s3://npm-registry-packages"}}, "5.0.4": {"name": "istanbul-lib-source-maps", "version": "5.0.4", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@5.0.4", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "1947003c72a91b6310efeb92d2a91be8804d92c2", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-5.0.4.tgz", "fileCount": 11, "integrity": "sha512-wHOoEsNJTVltaJp8eVkm8w+GVkVNHT2YDYo53YdzQEL2gWm1hBX5cGFR9hQJtuGLebidVX7et3+dmDZrmclduw==", "signatures": [{"sig": "MEUCIQCl+2B78Dz04EnBm6WHCw+rSaEHYFooi6Sopbsl3Ei4gwIgOxM1rJChl5qwPo4OKdKlghb1Vo+PLdtIOVZNm78yaq0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36433}, "main": "index.js", "engines": {"node": ">=10"}, "scripts": {"test": "nyc mocha"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-source-maps"}, "_npmVersion": "6.14.18", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"debug": "^4.1.1", "istanbul-lib-coverage": "^3.0.0", "@jridgewell/trace-mapping": "^0.3.23"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0-beta.2", "chai": "^4.2.0", "mocha": "^6.2.2", "ts-node": "^8.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_5.0.4_1708981083667_0.45053698036139966", "host": "s3://npm-registry-packages"}}, "5.0.5": {"name": "istanbul-lib-source-maps", "version": "5.0.5", "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "istanbul-lib-source-maps@5.0.5", "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "dist": {"shasum": "2bed84db66687448736dba91954f1080f4441ced", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-5.0.5.tgz", "fileCount": 11, "integrity": "sha512-gKf4eJ8bHmSX/ljiOCpnd8vtmHTwG71uugm0kXYd5aqFCl6z8cj8k7QduXSwU6QOst6LCdSXTlaoc8W4554crQ==", "signatures": [{"sig": "MEUCIE05suS/qR2QHYI7K4SsU1mOrGCuxzqKgtE1g2EQbtPHAiEAmu1DlghhCtVw86sO9zDMNsVnuskBhshOgRGoThF5Ty4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37312}, "main": "index.js", "engines": {"node": ">=10"}, "scripts": {"test": "nyc mocha"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/istanbuljs/istanbuljs.git", "type": "git", "directory": "packages/istanbul-lib-source-maps"}, "_npmVersion": "6.14.18", "description": "Source maps support for istanbul", "directories": {}, "_nodeVersion": "14.21.3", "dependencies": {"debug": "^4.1.1", "istanbul-lib-coverage": "^3.0.0", "@jridgewell/trace-mapping": "^0.3.23"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^15.0.0-beta.2", "chai": "^4.2.0", "mocha": "^6.2.2", "ts-node": "^8.5.4"}, "_npmOperationalInternal": {"tmp": "tmp/istanbul-lib-source-maps_5.0.5_1719818845497_0.5810661573669407", "host": "s3://npm-registry-packages"}}, "5.0.6": {"name": "istanbul-lib-source-maps", "version": "5.0.6", "description": "Source maps support for istanbul", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc mocha"}, "dependencies": {"@jridgewell/trace-mapping": "^0.3.23", "debug": "^4.1.1", "istanbul-lib-coverage": "^3.0.0"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^6.2.2", "nyc": "^15.0.0-beta.2", "ts-node": "^8.5.4"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-lib-source-maps"}, "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "engines": {"node": ">=10"}, "_id": "istanbul-lib-source-maps@5.0.6", "_nodeVersion": "14.21.3", "_npmVersion": "6.14.18", "dist": {"integrity": "sha512-yg2d+Em4KizZC5niWhQaIomgf5WlL4vOOjZ5xGCmF8SnPE/mDWWXgvRExdcpCgh9lLRRa1/fSYp2ymmbJ1pI+A==", "shasum": "acaef948df7747c8eb5fbf1265cb980f6353a441", "tarball": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-5.0.6.tgz", "fileCount": 11, "unpackedSize": 37725, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHrSvSFq0TVO9EuM7sTz71gutNMxnvJLOdwE/919WU9CAiA7ubA0TscQEmHJWwDaqfpfgKEwMFYHs+yMeJ7YHx30qA=="}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-lib-source-maps_5.0.6_1719911975550_0.09621476823617714"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-11-24T19:03:56.937Z", "modified": "2024-07-02T09:19:35.853Z", "1.0.0-alpha.1": "2015-11-24T19:03:56.937Z", "1.0.0-alpha.2": "2015-11-24T23:03:18.549Z", "1.0.0-alpha.3": "2015-11-24T23:47:47.556Z", "1.0.0-alpha.4": "2015-11-25T01:58:03.276Z", "1.0.0-alpha.5": "2015-11-26T08:18:16.421Z", "1.0.0-alpha.7": "2015-11-27T00:18:20.205Z", "1.0.0-alpha.8": "2015-11-30T05:34:30.073Z", "1.0.0-alpha.9": "2016-01-09T06:41:36.678Z", "1.0.0-alpha.10": "2016-04-06T19:41:52.263Z", "1.0.0": "2016-08-31T15:10:53.156Z", "1.0.1": "2016-09-13T05:24:39.792Z", "1.0.2": "2016-10-03T23:19:19.776Z", "1.1.0": "2016-11-10T06:11:20.482Z", "1.1.1": "2017-03-27T05:51:28.737Z", "1.2.0": "2017-04-29T05:00:10.266Z", "1.2.1": "2017-05-27T21:13:01.536Z", "1.2.2": "2017-10-21T18:59:28.765Z", "1.2.3": "2018-02-13T05:48:41.477Z", "1.2.4": "2018-03-04T18:42:58.692Z", "1.2.5": "2018-05-31T00:37:46.260Z", "2.0.0": "2018-06-06T00:50:17.595Z", "2.0.1": "2018-07-07T19:00:20.015Z", "1.2.6": "2018-09-05T22:28:33.712Z", "3.0.0": "2018-12-19T04:11:21.268Z", "3.0.1": "2018-12-25T00:38:00.746Z", "3.0.2": "2019-01-26T02:40:19.860Z", "3.0.3": "2019-03-12T01:14:24.672Z", "3.0.4": "2019-04-03T18:04:50.097Z", "3.0.5": "2019-04-09T23:21:16.415Z", "3.0.6": "2019-04-24T20:44:52.588Z", "4.0.0-alpha.0": "2019-06-19T12:17:13.965Z", "4.0.0-alpha.1": "2019-10-06T01:06:10.956Z", "4.0.0-alpha.2": "2019-10-09T01:22:05.984Z", "4.0.0-alpha.3": "2019-11-15T17:36:42.616Z", "4.0.0-alpha.4": "2019-11-16T14:54:25.758Z", "4.0.0-alpha.5": "2019-12-07T16:41:57.596Z", "4.0.0": "2019-12-20T20:54:38.938Z", "4.0.1": "2021-10-12T02:01:09.715Z", "5.0.0": "2024-02-26T09:31:28.259Z", "5.0.4": "2024-02-26T20:58:03.798Z", "5.0.5": "2024-07-01T07:27:25.634Z", "5.0.6": "2024-07-02T09:19:35.697Z"}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-lib-source-maps"}, "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://istanbul.js.org/", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "readme": "# istanbul-lib-source-maps\n\n[![Build Status](https://img.shields.io/github/actions/workflow/status/istanbuljs/istanbuljs/ci.yml?label=CI&logo=GitHub)](https://github.com/istanbuljs/istanbuljs/actions/workflows/ci.yml)\n\nSource map support for istanbuljs.\n\n## Debugging\n\n`istanbul-lib-source-maps` uses the [debug](https://www.npmjs.com/package/debug) module. Run your application with the environment variable `DEBUG=istanbuljs`, to receive debug\noutput.\n", "readmeFilename": "README.md"}