{"_id": "istanbul-reports", "_rev": "85-2d76259736d03dd4180da4618f437836", "name": "istanbul-reports", "description": "istanbul reports", "dist-tags": {"latest": "3.1.7"}, "versions": {"1.0.0-alpha.0": {"name": "istanbul-reports", "version": "1.0.0-alpha.0", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"fast": "mocha --recursive test/", "pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "istanbul cover -x '**/assets/**/*.js' --include-all-sources --print=both _mocha --  --recursive test/", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "dependencies": {"handlebars": "^4.0.3"}, "devDependencies": {"chai": "^3.0.0", "coveralls": "^2.11.4", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^2.3.3"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "**************:istanbuljs/istanbul-reports"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbul-reports/issues"}, "homepage": "https://github.com/istanbuljs/istanbul-reports", "gitHead": "0728ca179c4ec21a75d9b92a5910efb33992555e", "_id": "istanbul-reports@1.0.0-alpha.0", "_shasum": "ac1e3bf7ce0928741372b136095bc8be82418680", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "dist": {"shasum": "ac1e3bf7ce0928741372b136095bc8be82418680", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.0.0-alpha.0.tgz", "integrity": "sha512-2/oeO7M6WJwV8sC6ekGfw11tBXDNl16ste7WWNQIPOIvjZ4XoPi350Osq7TygOUQsxrrwt9nNxlQE4Rut1zn5A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDKz5drnL7CNBD4KJSxNnb/DuPjcFjsN6TjhzJlfeIA1wIgIPpHvTYwT2q9W4T8YhvKLtFMWOEx/e7SCmYRaerYGtY="}]}, "directories": {}}, "1.0.0-alpha.1": {"name": "istanbul-reports", "version": "1.0.0-alpha.1", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"fast": "mocha --recursive test/", "pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "istanbul cover -x '**/assets/**/*.js' --include-all-sources --print=both _mocha --  --recursive test/", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "dependencies": {"handlebars": "^4.0.3"}, "devDependencies": {"chai": "^3.0.0", "coveralls": "^2.11.4", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^2.3.3"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "**************:istanbuljs/istanbul-reports"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbul-reports/issues"}, "homepage": "https://github.com/istanbuljs/istanbul-reports", "gitHead": "398e54c0b55a83376e0cb2bdb05e5993b22ff125", "_id": "istanbul-reports@1.0.0-alpha.1", "_shasum": "98ddfe4e780798c4cd7b6701c854e4d009d1f6bc", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "dist": {"shasum": "98ddfe4e780798c4cd7b6701c854e4d009d1f6bc", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.0.0-alpha.1.tgz", "integrity": "sha512-ffmZOwShKdksK2RPuzOjDD4JzuV36Qmu0cUW8GAjg6Fa+ElqMZoEcWRuEyNa5+ESDf6yTO6YZ38MZomaNUWI3g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDjBp7ViwgvYiVOBa9h/nvgBf574maTYJPjpeR5u89tkwIhAOlnl+k+HiswuUZjp9xTtMb5NRx+L7PxRQ7xDEjblRz6"}]}, "directories": {}}, "1.0.0-alpha.2": {"name": "istanbul-reports", "version": "1.0.0-alpha.2", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"fast": "mocha --recursive test/", "pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "istanbul cover -x 'docs/**' -x '**/assets/**' --include-all-sources --print=both _mocha --  --recursive test/", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "dependencies": {"handlebars": "^4.0.3"}, "devDependencies": {"chai": "^3.0.0", "coveralls": "^2.11.4", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^2.3.3"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbul-reports.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbul-reports/issues"}, "homepage": "https://github.com/istanbuljs/istanbul-reports", "gitHead": "2565b24cb6dd4b1f26f68574c8b8e7668bf509b3", "_id": "istanbul-reports@1.0.0-alpha.2", "_shasum": "e374c036285feb0e9da1eda69f76f3e5e3a25cfa", "_from": ".", "_npmVersion": "2.12.0", "_nodeVersion": "0.10.26", "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "dist": {"shasum": "e374c036285feb0e9da1eda69f76f3e5e3a25cfa", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.0.0-alpha.2.tgz", "integrity": "sha512-Wdsr5uLaN6W/vIuttZe6J3p45XzMGpNZdmNsKCpo6KECOxo4vCoIS05QqRVx5kNvFjJN2pR0xm723bDHFr5ZJA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCuuH+Xx56dR9wBDiQCvfN7Q2gR/PqutHdUSYlDkH39dwIhAIH8TjV1RSx+8RT2+ti54Q5o096Vs0/FNmoxcZpdiM92"}]}, "directories": {}}, "1.0.0-alpha.3": {"name": "istanbul-reports", "version": "1.0.0-alpha.3", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"fast": "mocha --recursive test/", "pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "istanbul cover -x 'docs/**' -x '**/assets/**' --include-all-sources --print=both _mocha --  --recursive test/", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "dependencies": {"handlebars": "^4.0.3"}, "devDependencies": {"chai": "^3.0.0", "coveralls": "^2.11.4", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^2.3.3"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbul-reports.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbul-reports/issues"}, "homepage": "https://github.com/istanbuljs/istanbul-reports", "gitHead": "01e2581dfe3568dd8f88000ea2a462686aaac124", "_id": "istanbul-reports@1.0.0-alpha.3", "_shasum": "3cb7049c786f64935ceeda44cc9d8a4eeb491bdf", "_from": ".", "_npmVersion": "2.12.0", "_nodeVersion": "0.10.26", "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "dist": {"shasum": "3cb7049c786f64935ceeda44cc9d8a4eeb491bdf", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.0.0-alpha.3.tgz", "integrity": "sha512-tgI3WRnNPhexDjLY0osqjDHHYatHKOqMuorpEbf7Jn1JDn1A8j62XTxOzoOoK+y9qSfG8iD+VXnFeXSg5gyELA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDS5uZhvv4+2jwSyxCJw1WYJbtNdMgbiCuB9FCfPaDqZQIhAKfa2rVp0o27ySpq4H6IUlenv0psG0zO+d0kv7LD8yIZ"}]}, "directories": {}}, "1.0.0-alpha.4": {"name": "istanbul-reports", "version": "1.0.0-alpha.4", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"fast": "mocha --recursive test/", "pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "istanbul cover -x 'docs/**' -x '**/assets/**' --include-all-sources --print=both _mocha --  --recursive test/", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "dependencies": {"handlebars": "^4.0.3"}, "devDependencies": {"chai": "^3.0.0", "coveralls": "^2.11.4", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^2.3.3"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbul-reports.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbul-reports/issues"}, "homepage": "https://github.com/istanbuljs/istanbul-reports", "gitHead": "39ab1301fe42ed63dca12bf09074be0d27718f2c", "_id": "istanbul-reports@1.0.0-alpha.4", "_shasum": "5086d417b4bb97bf6870f3fb5cb55b7e703361b8", "_from": ".", "_npmVersion": "2.12.0", "_nodeVersion": "0.10.26", "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "dist": {"shasum": "5086d417b4bb97bf6870f3fb5cb55b7e703361b8", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.0.0-alpha.4.tgz", "integrity": "sha512-YMXP2knBOc8ZGcumBlf9eqn9pNiweoyG1JhMIgz9L7Gv6s16PN2dKTyY6xDykOVav04GvtMeRqjRw2BGze2slg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFWTRzSS2+wMtLne4MqY+EZPLP4uenoXcjvXoOKyB4iDAiEAk11IWtf1ml1yL0MiwO8HU7Dqq28lFz5rGNj8QeMZJ1U="}]}, "directories": {}}, "1.0.0-alpha.6": {"name": "istanbul-reports", "version": "1.0.0-alpha.6", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"fast": "mocha --recursive test/", "pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "istanbul cover -x 'docs/**' -x '**/assets/**' --include-all-sources --print=both _mocha --  --recursive test/", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "dependencies": {"handlebars": "^4.0.3"}, "devDependencies": {"chai": "^3.0.0", "coveralls": "^2.11.4", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^2.3.3"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbul-reports.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbul-reports/issues"}, "homepage": "https://github.com/istanbuljs/istanbul-reports", "gitHead": "daeba6b396c3cc9e3646ad0496fe3c7fad19cd30", "_id": "istanbul-reports@1.0.0-alpha.6", "_shasum": "a52b4c6ff4acb91c7f3ed374a7f778e00c709797", "_from": ".", "_npmVersion": "3.9.3", "_nodeVersion": "6.2.1", "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}], "dist": {"shasum": "a52b4c6ff4acb91c7f3ed374a7f778e00c709797", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.0.0-alpha.6.tgz", "integrity": "sha512-hXqdKdhuGi5j80tshFlq7tLsoFIMK9etcjsYe8/AJJl/jZFFVytYCr+j4G1+Y2SyPBv23irUsF/+xF7GsVx2Jg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDqeA34H8tSH8lyppSRPCBgsJ49frp3mh0uZrByfn1ktAiEAyqTrRNHItjinMgWnAdSqUR8STrLVYu0l5mVmuSOVWrA="}]}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/istanbul-reports-1.0.0-alpha.6.tgz_1466732773431_0.5101215024478734"}, "directories": {}}, "1.0.0-alpha.7": {"name": "istanbul-reports", "version": "1.0.0-alpha.7", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"fast": "mocha --recursive test/", "pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "istanbul cover -x 'docs/**' -x '**/assets/**' --include-all-sources --print=both _mocha --  --recursive test/", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "dependencies": {"handlebars": "^4.0.3"}, "devDependencies": {"chai": "^3.0.0", "coveralls": "^2.11.4", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^2.3.3"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbul-reports.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbul-reports/issues"}, "homepage": "https://github.com/istanbuljs/istanbul-reports", "gitHead": "0969e457e8fcfac51879b87ca3b638e6e676d943", "_id": "istanbul-reports@1.0.0-alpha.7", "_shasum": "5ca30bf44a4866681b6282516a08a08b291ea6c3", "_from": ".", "_npmVersion": "3.9.3", "_nodeVersion": "6.2.1", "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "dist": {"shasum": "5ca30bf44a4866681b6282516a08a08b291ea6c3", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.0.0-alpha.7.tgz", "integrity": "sha512-6wy8ZjLyJHEX7X7J+q7pFI3SO38OH15pexKpTTyGjxUCMTkX8L9ZDEPY4WhIDLFSPMjv8xLN3+2vGahXEmvfCQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQChM7e8Nh2pHmVvtfqpTyfKVGbNzmnlK2Tl7if+2c7FvQIgVLYTdaUc6n+4w6C8xHGdb7iOqSoRTJtP8LTsRTK501Q="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/istanbul-reports-1.0.0-alpha.7.tgz_1467639197469_0.7729339271318167"}, "directories": {}}, "1.0.0-alpha.8": {"name": "istanbul-reports", "version": "1.0.0-alpha.8", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"fast": "mocha --recursive test/", "pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "istanbul cover -x 'docs/**' -x '**/assets/**' --include-all-sources --print=both _mocha --  --recursive test/", "xposttest": "istanbul check-coverage --statements 95 --branches 80"}, "dependencies": {"handlebars": "^4.0.3"}, "devDependencies": {"chai": "^3.0.0", "coveralls": "^2.11.4", "istanbul": "^0.4.0", "jshint": "^2.8.0", "mocha": "^2.3.3"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbul-reports.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbul-reports/issues"}, "homepage": "https://github.com/istanbuljs/istanbul-reports", "gitHead": "10d00e6566e1d8daab4c5ee124ae273ec9427749", "_id": "istanbul-reports@1.0.0-alpha.8", "_shasum": "094830f4c7f3d482e466aac8abda2495f9ae4689", "_from": ".", "_npmVersion": "3.9.3", "_nodeVersion": "6.2.1", "_npmUser": {"name": "gotwarlost", "email": "<EMAIL>"}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "dist": {"shasum": "094830f4c7f3d482e466aac8abda2495f9ae4689", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.0.0-alpha.8.tgz", "integrity": "sha512-iep8KShFfbUJacwOQ1bXUJE18D8rVbXGFYHKAE/VQI6JMKXsZgBuRsAoILzIiF7lGUTaynfkpLQBj+9rrgqD5w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDIoUt/ub84BWUsEsAyz4w9mDiQ+BMHv+MweMrp8X36qAIhAKznvnF9s3h8ZnmsCnDWaW1UntJPKcu1W18JUspkiAT2"}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/istanbul-reports-1.0.0-alpha.8.tgz_1468237762146_0.24699895130470395"}, "directories": {}}, "1.0.0": {"name": "istanbul-reports", "version": "1.0.0", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "nyc --all mocha --recursive test/", "coverage": "nyc report --reporter=text-lcov | coveralls", "release": "standard-version"}, "dependencies": {"handlebars": "^4.0.3"}, "devDependencies": {"chai": "^3.5.0", "coveralls": "^2.11.4", "istanbul-lib-coverage": "^1.0.0", "jshint": "^2.8.0", "mocha": "^3.1.2", "nyc": "^8.3.1", "standard-version": "^3.0.0"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbul-reports.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbul-reports/issues"}, "homepage": "https://github.com/istanbuljs/istanbul-reports", "gitHead": "929e16fa6e41068f7e8e9d887bfcb2e3dcdcb6c4", "_id": "istanbul-reports@1.0.0", "_shasum": "24b4eb2b1d29d50f103b369bd422f6e640aa0777", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.5.0", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "24b4eb2b1d29d50f103b369bd422f6e640aa0777", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.0.0.tgz", "integrity": "sha512-MCXBt4ViVPqfA8kx8bU9hTp83dnaDpic8ZU69ADj3Rg6fxPpH2SHkBkY0vroF1O771HMJTvzrbm8M3V2HaXFQA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDVx6XQ/OI72LHuNd7C/zxtNk3w95pOQSY+PVoyh9Q5UAiAjefFA6mrCaBVwAkvOIeBChtJGHcMs7hrr2NexgR5OzQ=="}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/istanbul-reports-1.0.0.tgz_1476684878444_0.31230472680181265"}, "directories": {}}, "1.0.1": {"name": "istanbul-reports", "version": "1.0.1", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "files": ["index.js", "lib"], "scripts": {"pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "nyc --all mocha --recursive test/", "coverage": "nyc report --reporter=text-lcov | coveralls", "release": "standard-version"}, "dependencies": {"handlebars": "^4.0.3"}, "devDependencies": {"chai": "^3.5.0", "coveralls": "^2.11.4", "istanbul-lib-coverage": "^1.0.0", "jshint": "^2.8.0", "mocha": "^3.1.2", "nyc": "^8.3.1", "standard-version": "^3.0.0"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbul-reports.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbul-reports/issues"}, "homepage": "https://github.com/istanbuljs/istanbul-reports", "gitHead": "c8a05d25949c404cc3863213f950afa41b797d55", "_id": "istanbul-reports@1.0.1", "_shasum": "9a17176bc4a6cbebdae52b2f15961d52fa623fbc", "_from": ".", "_npmVersion": "4.0.5", "_nodeVersion": "7.4.0", "_npmUser": {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}, "dist": {"shasum": "9a17176bc4a6cbebdae52b2f15961d52fa623fbc", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.0.1.tgz", "integrity": "sha512-ZVV/4XlbZzaZ3gmaaLxa0kgyidtNWH0FDQi5PfeR7+FMyvqN6rC5dwdi0ixL+H80EaMlSw/lDap/i89EwbxmjQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCqe/NHaRQElv+p1z1lGrNoNAIBp8XNpmnovz+LkrE9gAIhAIYv6hB5ukuysKllWPuukiDjUR1Yec79uhMFkxrfxytj"}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/istanbul-reports-1.0.1.tgz_1485672779624_0.02705533360131085"}, "directories": {}}, "1.0.2": {"name": "istanbul-reports", "version": "1.0.2", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "files": ["index.js", "lib"], "scripts": {"pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.0.3"}, "devDependencies": {"chai": "^3.5.0", "istanbul-lib-coverage": "^1.0.2", "jshint": "^2.8.0", "mocha": "^3.1.2"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbul-reports.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbul-reports/issues"}, "homepage": "https://github.com/istanbuljs/istanbul-reports", "_id": "istanbul-reports@1.0.2", "_shasum": "4e8366abe6fa746cc1cd6633f108de12cc6ac6fa", "_from": ".", "_npmVersion": "4.4.1", "_nodeVersion": "6.9.5", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "4e8366abe6fa746cc1cd6633f108de12cc6ac6fa", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.0.2.tgz", "integrity": "sha512-tFQsdUJdjsJWoICUBxA3XGSUSJTAZb/b05m0BRpIoA0TsSyOygiq6tFuLz2h45UKH9d46HmWQzDOX7sz6oSdoQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICKLUe54mZzf8eA7kSuNaYoQ5XTgGsf/8EBCPOhNBn0BAiEAlDUJr3gaQ1iaNCLu6U85sQRJE9ntGhquib/+MGaepC4="}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/istanbul-reports-1.0.2.tgz_1490593881536_0.6476666752714664"}, "directories": {}}, "1.1.0": {"name": "istanbul-reports", "version": "1.1.0", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "files": ["index.js", "lib"], "scripts": {"pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.0.3"}, "devDependencies": {"chai": "^3.5.0", "is-windows": "^1.0.1", "istanbul-lib-coverage": "^1.1.0", "istanbul-lib-report": "^1.1.0", "jshint": "^2.8.0", "mocha": "^3.1.2"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbul-reports.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbul-reports/issues"}, "homepage": "https://github.com/istanbuljs/istanbul-reports", "_id": "istanbul-reports@1.1.0", "_shasum": "1ef3b795889219cfb5fad16365f6ce108d5f8c66", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.1.0", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "1ef3b795889219cfb5fad16365f6ce108d5f8c66", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.1.0.tgz", "integrity": "sha512-p6hdXlOnKes/eY5XPhP1fspj4ox4aid75zVmJJgSuhNmiWu2HFCrabVrVw/YtPLJeTVFGOsIrehoD6XtxJZCaQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGTxEBBxeSpzScqX2Di/V7/ZfHYOe02nDbTsRazIL4q6AiBiLcs2jfLvNxy8Duk9tTANquKQRYBkCZXgjzWFerf5Qg=="}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/istanbul-reports-1.1.0.tgz_1493442004757_0.599740678910166"}, "directories": {}}, "1.1.1": {"name": "istanbul-reports", "version": "1.1.1", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "files": ["index.js", "lib"], "scripts": {"pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.0.3"}, "devDependencies": {"chai": "^3.5.0", "is-windows": "^1.0.1", "istanbul-lib-coverage": "^1.1.1", "istanbul-lib-report": "^1.1.1", "jshint": "^2.8.0", "mocha": "^3.1.2"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "_id": "istanbul-reports@1.1.1", "_npmVersion": "5.0.0", "_nodeVersion": "7.1.0", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-P8G873A0kW24XRlxHVGhMJBhQ8gWAec+dae7ZxOBzxT4w+a9ATSPvRVK3LB1RAJ9S8bg2tOyWHAGW40Zd2dKfw==", "shasum": "042be5c89e175bc3f86523caab29c014e77fee4e", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.1.1.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEtOYRUwrudHI6ebpgRHCxKm3deQelVA4EzEOfWQV6wBAiEA9g/LDyZNXrhJ5QwiS5wI6pIav1nfNBUVRvJ0/5eeEWI="}]}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports-1.1.1.tgz_1495919578208_0.8774899295531213"}, "directories": {}}, "1.1.2": {"name": "istanbul-reports", "version": "1.1.2", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "files": ["index.js", "lib"], "scripts": {"pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.0.3"}, "devDependencies": {"chai": "^3.5.0", "is-windows": "^1.0.1", "istanbul-lib-coverage": "^1.1.1", "istanbul-lib-report": "^1.1.1", "jshint": "^2.8.0", "mocha": "^3.1.2"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "_id": "istanbul-reports@1.1.2", "_shasum": "0fb2e3f6aa9922bd3ce45d05d8ab4d5e8e07bd4f", "_from": ".", "_npmVersion": "4.4.1", "_nodeVersion": "8.3.0", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"shasum": "0fb2e3f6aa9922bd3ce45d05d8ab4d5e8e07bd4f", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.1.2.tgz", "integrity": "sha512-SjCXZOQEG+EmIZhUJ4rrzb2aXOTEjnpgpdAsDMtjJkunEcPJ8yc14tTAGK21TDhDt8JCYx7GqkaznU5QZNHP2A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDylE0CqnUyFzj5QLUuEfRjt2JogZNPomJHJ3z6mQn2eAIgZDLc/9blyU/qrBkHuq7bwd6t05jPehvaK+VEWGNlBag="}]}, "maintainers": [{"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "bcoe"}, {"email": "<EMAIL>", "name": "gotwarlost"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports-1.1.2.tgz_1503706439854_0.6207024639006704"}, "directories": {}}, "1.1.3": {"name": "istanbul-reports", "version": "1.1.3", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "files": ["index.js", "lib"], "scripts": {"pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.0.3"}, "devDependencies": {"chai": "^3.5.0", "is-windows": "^1.0.1", "istanbul-lib-coverage": "^1.1.1", "istanbul-lib-report": "^1.1.2", "jshint": "^2.8.0", "mocha": "^3.1.2"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "_id": "istanbul-reports@1.1.3", "_npmVersion": "5.5.1", "_nodeVersion": "8.7.0", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Z<PERSON>elkHh8hrZNI5xDaKwPMFwDsUf5wIEI2bXAFGp1e6deR2mnEKBPhLJEgr4ZBt8Gi6Mj38E/C8kcy9XLggVO2Q==", "shasum": "3b9e1e8defb6d18b1d425da8e8b32c5a163f2d10", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.1.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6FJYFmDejNR/5m2DbUqCmkjx9whD8Nevyq+PMAk0JFQIhAP3P+mWwQ8SmmOCLBtKCsjpwJAJ8frGOeOxX4IN0S5R7"}]}, "maintainers": [{"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "bcoe"}, {"email": "<EMAIL>", "name": "gotwarlost"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports-1.1.3.tgz_1508612370637_0.6891787350177765"}, "directories": {}}, "1.1.4": {"name": "istanbul-reports", "version": "1.1.4", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "files": ["index.js", "lib"], "scripts": {"pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.0.3"}, "devDependencies": {"chai": "^3.5.0", "is-windows": "^1.0.1", "istanbul-lib-coverage": "^1.1.2", "istanbul-lib-report": "^1.1.3", "jshint": "^2.8.0", "mocha": "^3.1.2"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "_id": "istanbul-reports@1.1.4", "_npmVersion": "5.4.2", "_nodeVersion": "8.8.1", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-DfSTVOTkuO+kRmbO8Gk650Wqm1WRGr6lrdi2EwDK1vxpS71vdlLd613EpzOKdIFioB5f/scJTjeWBnvd1FWejg==", "shasum": "5ccba5e22b7b5a5d91d5e0a830f89be334bf97bd", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.1.4.tgz", "fileCount": 27, "unpackedSize": 83483, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDII9+r+vOpzYjfgwUrOiE9OhgW8dWcemr55DrfVYvfcAiAIDbe1phIUTg+v7n2ZmGJJBCrOq7qyKEBKjWjgLKHfjQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_1.1.4_1518500919082_0.6001326467411845"}, "_hasShrinkwrap": false}, "1.2.0": {"name": "istanbul-reports", "version": "1.2.0", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "files": ["index.js", "lib"], "scripts": {"pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.0.3"}, "devDependencies": {"chai": "^3.5.0", "is-windows": "^1.0.1", "istanbul-lib-coverage": "^1.2.0", "istanbul-lib-report": "^1.1.4", "jshint": "^2.8.0", "mocha": "^3.1.2"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "readme": "istanbul-reports\n================\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-reports.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-reports.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-reports)\n\n* node.getRelativeName\n\n* context.getSource(filePath)\n* context.classForPercent(type, percent)\n* context.console.colorize(str, class)\n* context.writer\n* context.console.write\n* context.console.println\n", "readmeFilename": "README.md", "_id": "istanbul-reports@1.2.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.8.1", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-VVcTXdqog67wrnRDwOXvYfw779VIav0zho4icnBF2RWdbl0sYagMOFbdq29hntGPASzogia3Z5zE7zNI0YQSvA==", "shasum": "59a72413daf809b9c3203241c31abb9a48ab1bf5", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.2.0.tgz", "fileCount": 28, "unpackedSize": 87367, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqYSibpmE6i8Xs5ommVRmDasDQlgz7R4YxxZ9XwwDdPAIhAIxYsg9AVUOBf/6ZUKtg7lkedLo3IlK9bLR6qxYqk93o"}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_1.2.0_1520188976299_0.7409712028559465"}, "_hasShrinkwrap": false}, "1.3.0": {"name": "istanbul-reports", "version": "1.3.0", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "files": ["index.js", "lib"], "scripts": {"pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.0.3"}, "devDependencies": {"chai": "^3.5.0", "is-windows": "^1.0.1", "istanbul-lib-coverage": "^1.2.0", "istanbul-lib-report": "^1.1.4", "jshint": "^2.8.0", "mocha": "^3.1.2"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "_id": "istanbul-reports@1.3.0", "_npmVersion": "5.4.2", "_nodeVersion": "8.8.1", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-y2Z2IMqE1gefWUaVjrBm0mSKvUkaBy9Vqz8iwr/r40Y9hBbIteH5wqHG/9DLTfJ9xUnUT2j7A3+VVJ6EaYBllA==", "shasum": "2f322e81e1d9520767597dca3c20a0cce89a3554", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.3.0.tgz", "fileCount": 28, "unpackedSize": 87807, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFPqeEPdqEYpA/P34XknNdX0kb8KXbRFUg4gMZhV1MbsAiBS5Ivw4V0OHx8nrlTAvvPjafox3cB55XFTwKdQXpxokw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_1.3.0_1520633656601_0.0046326192783465725"}, "_hasShrinkwrap": false}, "1.4.0": {"name": "istanbul-reports", "version": "1.4.0", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "files": ["index.js", "lib"], "scripts": {"pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.0.3"}, "devDependencies": {"chai": "^3.5.0", "is-windows": "^1.0.1", "istanbul-lib-coverage": "^1.2.0", "istanbul-lib-report": "^1.1.4", "jshint": "^2.8.0", "mocha": "^3.1.2"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "readme": "istanbul-reports\n================\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-reports.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-reports.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-reports)\n\n* node.getRelativeName\n\n* context.getSource(filePath)\n* context.classForPercent(type, percent)\n* context.console.colorize(str, class)\n* context.writer\n* context.console.write\n* context.console.println\n", "readmeFilename": "README.md", "_id": "istanbul-reports@1.4.0", "_npmVersion": "5.8.0", "_nodeVersion": "8.4.0", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-OPzVo1fPZ2H+owr8q/LYKLD+vquv9Pj4F+dj808MdHbuQLD7S4ACRjcX+0Tne5Vxt2lxXvdZaL7v+FOOAV281w==", "shasum": "3d7b44b912ecbe7652a603662b962120739646a1", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.4.0.tgz", "fileCount": 28, "unpackedSize": 88233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1jcWCRA9TVsSAnZWagAA7coP/ia250RejvunUTJOCeKX\nAfDEBgw3C3EB2DJnUgfWY2l40biCnwCh0f2UELzzPMsClHOZo+ogZVRQr6Z2\nLN4f2n44cdVdmkPUXPLLLG4uH4hrH+6z+y6mj3Zra4XAv+NMQH4isit+ERwD\nSRSrpGx0Wbz/09FEgGpaTI0b0xN+/Dc5O9SsSTxPjBpC0/dc50oZv/FV2wd/\n7B2ezmRA2tGIZ/eyJDZZ0DLOibHm9sXjtnAB2FrmBe2P16BphCmi8uiFz853\nPS5K4laLkb1n7upUDNJmr4l5gYdxOqeUbSzGnIz/x6ok6feNYG2jvWfbjA7l\nWxuqluvFXUIDF9vv444yyuiqEJIjk2x+g1FPbhhf5CsVseTULdv9tLqAAWZX\nX4BjRixEP6cAUWwTtLr9+6JWxOgdYuS0/K3cCnszCpCAbpPSgGa1DRJ5vTlu\nO9QrOtZTDp0QLU9uOcpznjXALNKiI6m4CVnZ29MEdnSgVcoBx05+RJHFaGik\nOlI31VLqJs/aEtckFxdpyFFm3rDfBtsrS9J0znEg5D/vDEnHsQWPiywoyr54\nVu1mIRqyJF1FRUnh5C6Ea2u0ljVll9//9tpY+NKFUbstwqGdDwrQdWB6YI1c\nBYXDR6CvdLkt1Ji0lZ2EjnAygo7DLcSbltlqauleA4pQVe2/l//JZpYVfX1q\nL79w\r\n=YNA6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEi+k5yL7YUFfNFgHVK9GjI/v+Emz1kZgqMNVma1wgO9AiEArM+f0LuWZYpemyyqeTowJFnRNhGrLYV1e1nC5eYEFYw="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_1.4.0_1523988245909_0.20140160613273927"}, "_hasShrinkwrap": false}, "1.4.1": {"name": "istanbul-reports", "version": "1.4.1", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "files": ["index.js", "lib"], "scripts": {"pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.0.3"}, "devDependencies": {"chai": "^3.5.0", "is-windows": "^1.0.1", "istanbul-lib-coverage": "^1.2.0", "istanbul-lib-report": "^1.1.4", "jshint": "^2.8.0", "mocha": "^3.1.2"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "readme": "istanbul-reports\n================\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-reports.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-reports.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-reports)\n\n* node.getRelativeName\n\n* context.getSource(filePath)\n* context.classForPercent(type, percent)\n* context.console.colorize(str, class)\n* context.writer\n* context.console.write\n* context.console.println\n", "readmeFilename": "README.md", "_id": "istanbul-reports@1.4.1", "_npmVersion": "6.0.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-mwOHJ7jDof5IgKYIB5pbLA/gEE9nyjQwqUQbyonu+/Tuc4g1nMHNTdmFhbhqIVBagTryAUprZaYoC08OB0sXCA==", "shasum": "4f2e8e928aa7a05d1da6c427d4098b2655ec7334", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.4.1.tgz", "fileCount": 28, "unpackedSize": 88559, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbD0PaCRA9TVsSAnZWagAAcNYP+gL48VaxOHT7fYJ0G3th\nISqFgDNx2XYn9b/Un1RAmIfOcWIsU/4rxKkyHMBloOsI5Ag7EGzwxL3Q3Drj\n2PCRaXH1oY1oQTMjg4mUiUb7nozeJ4G3Dkc5JVxu09VeCUjEptXLoOJ9TAQF\n/VN6WtUDOU9TQmWqM8JILaaEYHI6oEuSMUdHdNvg84HVHkfTc2B36/9P12Kf\nNHWl3DYQp3tbE1PQbq/IrhLftIhpHN8IsWGKkCgcirIiNRI6ZwSXTXxPmHHF\nHuK6z9d8dn6vWiFD7wAWNHXjL+OCCE9p9dLvJEU+t9qnV5rXrR1etjv8wavl\nnPuRtlmwsuFblRtgwFQz4sTVTr0BdNkUw5k62TJB65HvWkWJ2u6h1PovCj6e\nNJtH0KyqJ5HewSh5sl5gWMoBQ/cy2HzrMt0mcYch87OaIXfQs71J+4Mg0TyO\nSFskaieSU6SbYYMoYggSxG6ZoqG/RjQx44oFFCOsAq0bPEjCt1J4VaA/IOQf\nPzdIVdtp4SCAM08f0SZJvCy9Fdul+3trkNFT8SscJ91txCkibYZbUcSl0hWc\nTsaQzfr+nRoXc66pZ75adO6daK+vfI+3wKRkubzfXToemxS7JPaRK/tDzRHQ\ndONSrVmduRO/n06lw6tm9jY1ybETICB1uQNhMyk/3xETpMke7T0plKwnaB6u\nB7Rc\r\n=y53c\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4FgP+HPkDQMk+8zUtFJyJImSyUblGufqv1NvXa2TA9QIhAKNr0bXyt7Vr35t9ZrDmHt5gNtdeBOk1bsPUl67+3gw3"}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_1.4.1_1527727066171_0.6018411184314041"}, "_hasShrinkwrap": false}, "1.5.0": {"name": "istanbul-reports", "version": "1.5.0", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "files": ["index.js", "lib"], "scripts": {"pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.0.11"}, "devDependencies": {"chai": "^4.1.2", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^2.0.0", "istanbul-lib-report": "^2.0.0", "jshint": "^2.9.5", "mocha": "^5.2.0"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "readme": "istanbul-reports\n================\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-reports.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-reports.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-reports)\n\n* node.getRelativeName\n\n* context.getSource(filePath)\n* context.classForPercent(type, percent)\n* context.console.colorize(str, class)\n* context.writer\n* context.console.write\n* context.console.println\n", "readmeFilename": "README.md", "_id": "istanbul-reports@1.5.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.2.1", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-HeZG0<PERSON>HretI9FXBni5wZ9DOgNziqDCEwetxnme5k1Vv5e81uTqcsy3fMH99gXGDGKr1ea87TyGseDMa2h4HEUA==", "shasum": "c6c2867fa65f59eb7dcedb7f845dfc76aaee70f9", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.5.0.tgz", "fileCount": 28, "unpackedSize": 89199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbFy/ACRA9TVsSAnZWagAAwEoQAIV1R5X3n1Suk17nhWR6\nEdCIy945qZIJ53B6948av5j833Ztmnc1sHdG9+AstCRD+CcQOf1xCf7MN9Gu\nTgFlTN5skjgCaahVGO765E/vMk3xbyRtBWhCK2GxdP4opR7o7Ebp9tUqqEJt\n9tZJvtE9KOBSm8BF+d5myD2thsNPr5qzJkbzZZNhHP6xm/5HGkmq7Ei5P4VZ\nEIx+Rr1OJ/nHHOn+fZgHqP3l0ozExyZt7lpMWRmPCAKpB6gbMZGXrSk8/wJg\nZQWmL815g/q4WtAyyxgvaPG7mbe6lUitZwZy/TQLJ9vY5yVfHnC182pF5RFM\nLXui4jpNNG24SCD0J4O3uBXeJHEJk0Nhl986YqMcdp5+HiBSyEXu3mM4LvOc\nYmq7YIrPKmVi5wPGcVH1yAN6qOp0TC01xtEy1a2tCV1OLqWE0afJH7FSh2JT\nR5Sff/xoRLMNydrIcMS06X9uXIbMIqgx3X+MkVmorlSYTMx7eB0ELHS8Rmh/\n0uSPtAZyBXUpda5tk3Xjxt+fra48mCyzSo/sGobN/CcguNWC5hx+DSfDsJ++\nTAewqEqU1HNVdPNpBrM3UMU938VySBehOpHxSIqcMK/eu8vkzCAidc7tz0b/\nJ2joskjumGXEk6Vb1uAXT6Kqz0Baq4gRRyiAKIPSxY381AfrAf40NEFXlAmk\nBVFa\r\n=mIDr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCdRhzuMQeFNpYoTHOa97Rphl3WVsSuAVNTR/4rps2iFwIgbsviEha555Sz30xMFtvhP5LoixL6Q8RacKKPHBCSaVk="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_1.5.0_1528246207725_0.9661981737697718"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "istanbul-reports", "version": "2.0.0", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "files": ["index.js", "lib"], "scripts": {"pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.0.11"}, "devDependencies": {"chai": "^4.1.2", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^2.0.1", "istanbul-lib-report": "^2.0.1", "jshint": "^2.9.5", "mocha": "^5.2.0"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "engines": {"node": ">=6"}, "_id": "istanbul-reports@2.0.0", "_npmVersion": "6.1.0", "_nodeVersion": "10.2.1", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-d2YRSnAOHHb+6vMc5qjJEyPN4VapkgUMhKlMmr3BzKdMDWdJbyYGEi/7m5AjDjkvRRTjs68ttPRZ7W2jBZ31SQ==", "shasum": "eb12eddf55724ebc557b32cd77c34d11ed7980c1", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-2.0.0.tgz", "fileCount": 28, "unpackedSize": 89615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbQQ25CRA9TVsSAnZWagAA9e8QAIC1NxYNIrJGS6uJy9Ty\nzlI+i6x/TCT5Mv3weCKc8Nr5/e12/yXi1dyoXPBPh1uKj4dVy0QWes7/FP4d\nWZUED1a+6LL8HMHfjX7+yPuQIP8vrsdOUkbWC6GlkcRnkUty4fqPW7z+P5ts\nFL+nhz3+k3agoqmR7j3Nsw3MsmB0f7rSZ98pfyqvHZEHlL5OnRQmTQPPJQDF\nQ9oqO/WQL4hura9bkVJXmDTTxsfqusfTHb/ZRF0eqSp4OlOmPmoHaxYHTHRK\nj19h+hxhYil9RBNRRtIgPOahC6vC9fFByLpQMW5KkAygb3zz16b60oQ0l+dP\nd5H079ewsaqyxCoLKqanO6ul0UJ3lbbskQVIzsRiXVGlyYAOqAjYGL6uo2+R\n3gPjMB40U/Omg9xMa3uhDHY+eosfkqbPR33ItUFIMawd6E+qFqUCokxtBaq5\nPHWfCSfrdTEtzZbsrKAq9hWV9SGW3hpWii1Qir6Yn4Q137v8QfP7jBinl/e3\nTPR86y/mZMtaWAAtLevbR4S5DyyWmdPUtMs6E4/maH1Nd6gz2puMphlSyB2O\nid+EheaRT9y7qOw1LZ+ty3R5qPytAR0YJeppNeUCBbWEEukO7XFWE4gRoYv/\nogz0zgVjnOYx6H961t9tAwhSrFnsXAk4LMvoSUtGjDIM+Ztu9V58AcUb28A7\npVBY\r\n=DEn1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEMCH05+IcLI7JRzvSVEoDi5XnXEsPKODgMPQLslUd2eXAQCIB7uiS+MzT0rz3KKRA8LC0EAxwtTpqC/bk6tRmA2jzsP"}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_2.0.0_1530990009546_0.07846620272987614"}, "_hasShrinkwrap": false}, "1.5.1": {"name": "istanbul-reports", "version": "1.5.1", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.0.3"}, "devDependencies": {"chai": "^3.5.0", "is-windows": "^1.0.1", "istanbul-lib-coverage": "^1.2.1", "istanbul-lib-report": "^1.1.5", "jshint": "^2.8.0", "mocha": "^3.1.2"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "_id": "istanbul-reports@1.5.1", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "dist": {"integrity": "sha512-+cfoZ0UXzWjhAdzosCPP3AN8vvef8XDkWtTfgaN+7L3YTpNYITnCaEkceo5SEYy644VkHka/P1FvkWvrG/rrJw==", "shasum": "97e4dbf3b515e8c484caea15d6524eebd3ff4e1a", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-1.5.1.tgz", "fileCount": 27, "unpackedSize": 84335, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbkFiJCRA9TVsSAnZWagAAlNgQAJ3DKt/HdhZNrIMbNSKg\nCvuBH5lDiNu4Um+Xg8AiGwtXjb8tjwghcYdr+rcRkNG+lApHDmO1qJ2lDgow\n98+/wauDDfow5LxE85AVk6KHJ9LBy1CLm3k81elsg6TU0+AXyhyTi9DyuEGF\n3h7XaLoON78DmfdJEqv5HbAyZiSDwpF27tnV71NZ+I6sN1QHC9RnarMvAB07\n6S01k9855H6jEP4EuL1kdrCbxzM7CurOdJVoojNbTTV3+c9/Gfw6AZUf8Umz\nLS4BHDnxBNk1/Gq1d1klfo3rnqUg5RJpb4Hs3rYQA6MwlGLd5hpJGxPQNPZM\n5PDEnwwRALptNhSSHx8otl1Lj8grGoinkPHk+eqanVGzzKGTlEgndwtw7R9J\nA8ZawUPX2k2k2dmbpQjzOogEt+QBDgZ6KEIDnP09+hZ4OBoEW3EECF1riqEH\nnJobdUJQEIF8EaaoDZ7XpJBeQQLYtAXHipMt8Mumx4nwMABURf9MRnKIhofa\nvYCwgd9LpSX+6Q6mggRayL+fgJPpKlpjqBFESIeNWpSxwnnimtEEkid7P3be\nFnDMfQDXt0LKnsXhJ2m34Zxs7NYFhs4EmquYHLfCxb7IT21cQBiMX9hNr7sh\ntlYDggUTcvX/EXy27pxi23lCZFgpgF71xQwFsNevN1Un7X0ip/Y+GgGL4zto\nlYGh\r\n=XigB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTYSy38eXsrtxj9sKLtQs9WFzuI3K/AX8aiVWm16aM+QIgbqHjlmdrf/0R4xNUdGKsu773rupskzaV7aA0YtI1+cw="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_1.5.1_1536186504856_0.13825094513051384"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "istanbul-reports", "version": "2.0.1", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.0.11"}, "devDependencies": {"chai": "^4.1.2", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^2.0.1", "istanbul-lib-report": "^2.0.2", "jshint": "^2.9.5", "mocha": "^5.2.0"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "engines": {"node": ">=6"}, "_id": "istanbul-reports@2.0.1", "_npmVersion": "6.2.0", "_nodeVersion": "10.9.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "dist": {"integrity": "sha512-CT0QgMBJqs6NJLF678ZHcquUAZIoBIUNzdJrRJfpkI9OnzG6MkUfHxbJC3ln981dMswC7/B1mfX3LNkhgJxsuw==", "shasum": "fb8d6ea850701a3984350b977a969e9a556116a7", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-2.0.1.tgz", "fileCount": 28, "unpackedSize": 89929, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbkHp/CRA9TVsSAnZWagAAg78P/iTPuF43zpdV26p3oT+N\nyI173l3qlqsv6hclcq6nYb83ZWgLUv8Ot4P1JPG94le3OO1V/D5OoOBuNbxD\nzvpqDVJ7gm9kJjvlROUCedYH8VDZUGm9uOOBpCTeutzc3097mO1VJvvXpxy2\nrkDrzgb9laynsljNFhr/3d3ytAiNfuvhJ+hWxh3PpBBUKDmLDBX/SWXXleQB\nqq8rqiyE0q0liy18A2Vj16COVPMghR2wEwMz8xeEqep5zcLpLPyfNIa0Kk0+\nDppOQJs0g7L45k1wF7EqHITa3UaSyUww0ndhg2XbSw+6w5HVuMkzmIcXl2vr\n4AYfchBKyZpTGRLJCra2RYBvTH9pzefQG78BFQODpWsKr75o161jzU+lGJLY\n+QPzTIcr4A/4y1tzYDluOSDvU9oz3M2dzChKUSBUoXy4pf2RXIgKI7BuLSwb\nlvO/5E59KO2jzD7cdurCzxnZQrXC1NkTh3BV40AAcUiBvjlHYChCizC5vWQc\n0hE+SEksPpwkhVdPxsjDXnfPdU9ro5UPfyGtEmK7HhoEwnXqmXddIWhOaOct\nWoDdTANiyqH7dT7p0kpfq6+pHCtX//3migB5uw1Cqg0zuXiY0OL9BmYU9UNh\nLIChz87reb//UoEi4hJXATzvBESLunaEYNz/aJZu86Lp8rcVSy8wDMTzRqSn\n4rfX\r\n=kYGD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDtl7p29TOZpQplTwWdq9wL8W2Bs9Ca81FO9SK5h4Iq7wIhAIH7P0Mr7XYQb1D8G6R5qUL1HFyPk5JK67Qm9oz1SJSj"}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_2.0.1_1536195198231_0.7729890857367994"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "istanbul-reports", "version": "2.0.2", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"pretest": "jshint --exclude=**/prettify.js index.js lib/ test/", "test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.0.11"}, "devDependencies": {"chai": "^4.1.2", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^2.0.1", "istanbul-lib-report": "^2.0.2", "jshint": "^2.9.5", "mocha": "^5.2.0"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "engines": {"node": ">=6"}, "readme": "istanbul-reports\n================\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-reports.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-reports.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-reports)\n\n* node.getRelativeName\n\n* context.getSource(filePath)\n* context.classForPercent(type, percent)\n* context.console.colorize(str, class)\n* context.writer\n* context.console.write\n* context.console.println\n", "readmeFilename": "README.md", "_id": "istanbul-reports@2.0.2", "_npmVersion": "6.4.1", "_nodeVersion": "10.11.0", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-4e7KB6S7iP8J7+h1+ZzPIFX+Je2v/WhUnN6HUi7VSAXhv/dOUC69aHR5Foo3OxGhXudxnbEOaVC8tgtAqsiudg==", "shasum": "6393e43ebfc3708c75584ae78014de945ea2dff4", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-2.0.2.tgz", "fileCount": 28, "unpackedSize": 90346, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcGcTpCRA9TVsSAnZWagAAGlsP/0PM1j2kGISB+ABv5xxB\nUPy//0ayePv2mK5vHFdlNEG0XqNFyehffQj/TicvcIALyf/PE6jAG3fEFijN\nHKDNlGGhOn+n9pnVBAgchB0Z4aBqelNHMMhmvAmGMlbg65G/KTLIHRmOL1NE\n/eJ9fExYETY8jFgd23JcMo+Wy7kyiXJrrue4iL90rllbzo0zstZhfNYGvP3U\nyYlg9oNo5t3VSCFg/zEkvK9kHFkyt+xyr1UqKHd7ZcdTM2Tv9TbJHswMdxSO\nR3O2iXTmlmjM5bKnqnzA1rVs8a0MYY3PWLVuo3r+p3Fketp5w1e560jdAWvP\nKzLg+PKQpmAB8fgS62yO55m4Oid4GuZk8xtTsJkzPlX9rZ3h/UaEzwA3Obwh\nKISR/pBU9fi2NslMSpUIUvE/2EK8avqDf1byBI849UaVApcCG0lsdaV/Cdka\nAEWxn3zrKr8xRcpLbIYKeUG8u+cG0y63XBvkUB/wqjY8D/faUszqcdEySJWf\nxGlI/u8hf1AVxoRLbhIWYwBiyFiEgP4LJXrmR2x+a4sibwPSDzIBSwaHURs2\n9wQhMFrCVpRMLfvPaziTNjvyeI/rsCZcn/QOttDhvEhRrJvDS1af09B0CwcU\nVPdf2cRKUYHzNNm2vee90BdiAS9dM7cP08bt0jw/ttE4R2HlP8mPyIHu9YW0\naVTO\r\n=Z2k0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD2jdschyGUohmD3ag1WHUZzvr9zw7U2NLPOm3AlzRbQQIhAPo/Oe+5jz8RFBvRHku6inkfSbhQCnrYrVy6HxXbiGF6"}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_2.0.2_1545192681116_0.6344749345329102"}, "_hasShrinkwrap": false}, "2.0.3": {"name": "istanbul-reports", "version": "2.0.3", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.0.11"}, "devDependencies": {"chai": "^4.1.2", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^2.0.2", "istanbul-lib-report": "^2.0.3", "mocha": "^5.2.0"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "engines": {"node": ">=6"}, "readme": "istanbul-reports\n================\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-reports.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-reports.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-reports)\n\n* node.getRelativeName\n\n* context.getSource(filePath)\n* context.classForPercent(type, percent)\n* context.console.colorize(str, class)\n* context.writer\n* context.console.write\n* context.console.println\n", "readmeFilename": "README.md", "_id": "istanbul-reports@2.0.3", "_npmVersion": "6.4.1", "_nodeVersion": "11.5.0", "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-qpQ5ZWBkOatTxmTelS+HV5ybPSq7EeXmwXrPbGv7ebP+9DJOtveUcv6hCncZE4IxSAEkdmLEh3xo31SCttbApQ==", "shasum": "332eda684c9ee891f199dfba305c3e776f55fc16", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-2.0.3.tgz", "fileCount": 28, "unpackedSize": 93915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcIXvcCRA9TVsSAnZWagAAFxgP/3x7RTcjcSr/oDLKz54n\n3HTAKdy6G8urT7Zn03h35wR1A/BEf1cuKn8alfc9AeixvaVI2EolwReSlfOI\nOlU3PHNx97Hnl0j5o52HnCQYkkiWiXOSGxklrscKN10wqVkfzjaFLUDjiibS\nBDiW2C2tyqZ77PPT7Bi8fVUTfsELUtXW5G9/xQftDlGPiyDhyBzU2yBmpg3T\nkefc7bV3holsEvMzC+KOyHMU3SUVOttpnyaUI2pLX4YC3QWr7cF6vQeC7PiV\n3TUwl7+AoYIgERYPJjoKYbvXR4K3af07QdmegDY3LGHZ6d6wu+gl+u0gGHKL\nLhN8fnCVKiQEFuW+/LdxMXOtzHTmr/JxfMzf739Nj8ksciNVXClIIxdIPvhf\n3PNDWry3HmGZiEHSnAJ/djn5k4AIF7r4CKyB3fT8+bh1Qn6AR5YaSXuyJR5u\nWoTfyl2Xwrf+5yaOhF2Gs3KSCuM1f51LQ6qBWqBeKbWqYZtKRKE5Qq6RItt2\nbDvuwWkryUp3qOXZwPKSuSUIA0/uPrX/Y8P/aWwpj38R7Z12njsFeAaDRBz3\niaSjegk98soSG4XX+k3lhMRdVFmHJbmP5hZIZE0PfBe8+r4Y5il2VY+YKu7z\nxg4fsCirdwy6TKq3S5duiDI/fRY/34y6sYLLWoEL5dRS6BnPLPFwc/2RgzI2\naTPN\r\n=jcku\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC8MByiTpeLwGO3+eefM9jGBY5lEapmF0hYpRHxaqwE/AiB6xcJtkq40keP3ztVYsMyWLNQiJtHeDONVvX8ya6zxuQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_2.0.3_1545698268101_0.2871657264222469"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "istanbul-reports", "version": "2.1.0", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.0.11"}, "devDependencies": {"istanbul-lib-coverage": "^2.0.3", "istanbul-lib-report": "^2.0.4"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "engines": {"node": ">=6"}, "gitHead": "7875defdc3c3640787ac5d83700246de119e8b83", "readme": "istanbul-reports\n================\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-reports.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-reports.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-reports)\n\n* node.getRelativeName\n\n* context.getSource(filePath)\n* context.classForPercent(type, percent)\n* context.console.colorize(str, class)\n* context.writer\n* context.console.write\n* context.console.println\n", "readmeFilename": "README.md", "_id": "istanbul-reports@2.1.0", "_nodeVersion": "10.14.2", "_npmVersion": "lerna/3.10.5/node@v10.14.2+x64 (linux)", "dist": {"integrity": "sha512-azQdSX+dtTtkQEfqq20ICxWi6eOHXyHIgMFw1VOOVi8iIPWeCWRgCyFh/CsBKIhcgskMI8ExXmU7rjXTRCIJ+A==", "shasum": "87b8b55cd1901ba1748964c98ddd8900ce306d59", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-2.1.0.tgz", "fileCount": 28, "unpackedSize": 94437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcS8VuCRA9TVsSAnZWagAAOr8P/3ZrtEoixxtOlu826q8O\n8foZ9t7r5dXwCQJzVJCdg4LT8BO1Ozjt/gatTtZsebpuwjkcIz1cWdPXsNmO\net9WR8hmX1GSGODxErBNUCjNZ2rQCK1PjKtsDfKUhCj8+VsRWYLBDYsLwX3b\nK2+LKQj5ovz3nEPdOn3VGKCSkb4U6mGmKHJAAoyEQioAJ4LaKUHph1sQ6QAQ\nOVmdEleeolM0NXD2a5X1tWuC7QFgK+uClBu2tU4L41r1BHhZulBtQaYiqboR\nOFZvUtTxLS//XpZTmRmo2uYwaY9r9VK3iGiSPy8K+DnujMbEVSdLfi57uKCb\nr7WVq6l83Dyp60v2y8z+OOyXASyS339xuCR0JysBCTUK3YuJmUHjZfLF6GrY\nBjfqgRG8e38Yh1Bc9AgcE7kIyq8AJkC2CsxU6kobdcH2gXmmXtCMMmtqH8eu\nKgbAXTBf3UnNc/kg+B0wwsE9XMKQYu7igWCVJzVnYoZbbzMcBc8cRxCrwH0U\neXDYfWKa79O8p9T+jSD55AOQN2hJEknnOpmOFhfkBkDjVo2GXrpQi82tcOqQ\nQNNSHg5W+F/ntSbI/OzTgErMu0Odx0km1Ld7NeL4wDK9v5+7GHvgza1jJFmL\nXx+9cyWrfnCLT5gdHd/9l6Og5PaXP9cRSJpm3jM7pBRSmPPgFVIHqC1yJKxj\nKEH0\r\n=ooSt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHRL6VR3XcY/GbqPbnsKTuqCu0+LVQWfkIAaIXxKbynSAiEA5TgrEMB3VanI8m9DMcaXeUB0WOJK9fa4q1cZzaQKqLk="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_2.1.0_1548469613775_0.340620455608001"}, "_hasShrinkwrap": false}, "2.1.1": {"name": "istanbul-reports", "version": "2.1.1", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.1.0"}, "devDependencies": {"istanbul-lib-coverage": "^2.0.3", "istanbul-lib-report": "^2.0.4"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://github.com/istanbuljs/istanbuljs", "engines": {"node": ">=6"}, "gitHead": "c02990692153f87670b3d59ba21a19bcf6fb8e51", "_id": "istanbul-reports@2.1.1", "_nodeVersion": "10.15.0", "_npmVersion": "lerna/3.11.1/node@v10.15.0+x64 (linux)", "dist": {"integrity": "sha512-FzNahnidyEPBCI0HcufJoSEoKykesRlFcSzQqjH9x0+LC8tnnE/p/90PBLu8iZTxr8yYZNyTtiAujUqyN+CIxw==", "shasum": "72ef16b4ecb9a4a7bd0e2001e00f95d1eec8afa9", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-2.1.1.tgz", "fileCount": 28, "unpackedSize": 94759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZWoJCRA9TVsSAnZWagAAzD0P/iQSPcLXgCkDFcVvELuz\nli3ZRj7bJEdMGkz6q7CTSgLdYD59xXdE6PuQR/1JtdVhaTlPysRpMETPCkDP\ne2UEDzB+H2CNgoC5F+umLpUdy22eyQPB1RvtiMTQkKcBSMftlDyF+5ZcHxT6\ne9jly360YPpJNPiOzkX/mOaROlJW+FQBQsTyrbBm5kCK4NcP//ktst+LBx9o\ntGNY025997bFnq5F5tu6OnLLgq/RgmtHoQS7TELoYjAHyPsXlZIMWq7gY2Vy\ncJzCT5JipgnBFrN59iaXWkdUq8ii4R5pfWd42PYD7kkB0IT8S6JP/LwlIAC/\nxm8xveav10WAUY+FatSYg0PNjyH6IEf9ASW/VXic6ooe+gL/3+ekEgpLSBdA\nRjzNPCzV90yZA8afRJiq8C/fUn9xAawF36Puc0iuSKoUmHAWesd/DDjV2Yuj\n98m11d3IY7Fo05KyOrLBN3ucqAC2xmb2sbTsnP1oyQw3uMtOAhP+0i3qCl7S\naJ4tqTu4mf7j5OWfrX7IZLGon6YiS5m1EQx0zBo4qI7vC9X7PMrXUK3L/Mvx\nnUMx+HnAvc0FsBvmiK/8KTGN22Rm0XpbM7LeWLwifpmMLSaYib8A6CBWLjJm\nWo7uTFzTPHlQkvsS+mD6Rzhctu6mVk+YnMiumpIhnpQ8pzkW4b7sdU3LxpZb\ngEP9\r\n=vPfR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDa9oLlPCmf0zN1rzq/DaEtuG5N1be+pUeBQyDN8Qop7AiBfayLUr7/+q6uAqh+g5OjZ6/sM5X54y3nKrGP904dLaQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_2.1.1_1550150153102_0.500112759695962"}, "_hasShrinkwrap": false}, "2.2.0": {"name": "istanbul-reports", "version": "2.2.0", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.1.0"}, "devDependencies": {"istanbul-lib-coverage": "^2.0.4", "istanbul-lib-report": "^2.0.5"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "engines": {"node": ">=6"}, "gitHead": "c81b051d83217947dfd97d8d06532bd5013e98c3", "readme": "istanbul-reports\n================\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-reports.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-reports.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-reports)\n\n* node.getRelativeName\n\n* context.getSource(filePath)\n* context.classForPercent(type, percent)\n* context.console.colorize(str, class)\n* context.writer\n* context.console.write\n* context.console.println\n", "readmeFilename": "README.md", "_id": "istanbul-reports@2.2.0", "_nodeVersion": "10.15.3", "_npmVersion": "lerna/3.13.0/node@v10.15.3+x64 (linux)", "dist": {"integrity": "sha512-/jSU27Q9PaV2QwHY3/WsR0Yu/J616G3inKg6o6rr/a3vIB4kTpiWjSLA+jdkmuvCAMDfPyJMnOl4cfUnAJFJxA==", "shasum": "e71fac8c848c022b771c272beab56c20c1bf3c75", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-2.2.0.tgz", "fileCount": 28, "unpackedSize": 93673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchwfsCRA9TVsSAnZWagAAqyAP/RMOzFUi7yOQ/HtABZey\npHK3WlsM57MkPwsZl9PAk+BTpJ28kt7/uyO8TyMjKgFKV1iq5PIWX0E+HUWM\nlCcDLRmShvgvSTHh61SbaSj0bRxDgm4TKmjG3eU9l4rb42DoYCoRsT5g6Uro\nW61bH2SHPrrLNyTsEkRdi9zcYTFQOqEcr246ErR18925BOk4dbzn6AwZh+hc\noVzYo8glPaHvrrfnbH2N+CgSq3Tk8seJZM+tHpPWS8sUOtOEtol46NebdW+k\nj2T0NULfGA4tDROsYK/rBqblemYb7g9pgphnRQiTN2fOSiuZv7hfy1rrJ+4l\n90uurzizA4jyWNkcWTPczhXzM2yp3gA/eijt2kAILFHCRnA0VgMIovT7OsgL\nzXsguxbWeGcePMf7pxTCW7ZekYQwthqQviScj7v7KAHB22V0mGL4mkbJbNup\n/Ojw+jVsPd+qmwEvt58hzDafK+eXOguM4AJI4xjze83trlBpAjQv/y8CqE2D\nCuT9LUAqW46Yck+CxIYpcclFRgilJkgO8vw/kG7xeUr3JkPO2IjdA9T7avdZ\n93CWeJTvjomd78e61KQhz54yG86xNfSsVSfnIv3T+j0b+VFTvVNdv1qIdw48\nzoQJ3WI1gVVbnU9z9Zu2G6SWd0jU3SKGLvyEWmKhVX0QJNsuIlpN91VDiDmH\nsTBi\r\n=8U+j\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC+8QHnvL/7qMr6jsKaYxHN7Z6n3sCHe7h3A7CZ09yy/AiA3y2VDoVadXIAWttPPeT3DnIORbwKpS5YTfcr73LFmRw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_2.2.0_1552353258582_0.6654212361960801"}, "_hasShrinkwrap": false}, "2.2.1": {"name": "istanbul-reports", "version": "2.2.1", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.1.0"}, "devDependencies": {"istanbul-lib-coverage": "^2.0.4", "istanbul-lib-report": "^2.0.6"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "engines": {"node": ">=6"}, "gitHead": "e8063c799d0854341cb3daaf91c58acd06bd501c", "readme": "istanbul-reports\n================\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-reports.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-reports.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-reports)\n\n* node.getRelativeName\n\n* context.getSource(filePath)\n* context.classForPercent(type, percent)\n* context.console.colorize(str, class)\n* context.writer\n* context.console.write\n* context.console.println\n", "readmeFilename": "README.md", "_id": "istanbul-reports@2.2.1", "_nodeVersion": "10.15.3", "_npmVersion": "lerna/3.13.1/node@v10.15.3+x64 (linux)", "dist": {"integrity": "sha512-ESuM/KfiPrhLrP2pEkUfbHXvtOzXeeNQoltTTbPBKOwXfAx0KMhgJFBIE9ubyAC6r7YNoVE/O45qaL49z1SQZg==", "shasum": "2f4cc22fa8e601d0e589bc3dfadf3b4359ba4a03", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-2.2.1.tgz", "fileCount": 28, "unpackedSize": 93858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpPXCCRA9TVsSAnZWagAA5ScP/1E57bkHznVQrlJakP7D\nnCkj9N8vLfMvHgaNQMpUua7zW/8FwBivcryg7oYWuXR9BnCosoOtQ9UsTezM\ns5+ZR5ZB1xkQkFIGplXXhAyofVWG2IM+6PEnXi6iZaPcl9gQlvR5OMsYOS+w\n9cqUzebzep8MLpPZOKvndemm5ouzo8JxXmCkAuIDlLxXSvtL/lBaokvIhKUh\nUNmI5PETldPaKrnDA/EeghMz6gHYRdr26PpQRBdlAu/5+Fg7wFr4rwJNySkl\nqWg/C7yZMh5o322v1cTBBX13ct3mxR9eMfwBHtChzWllVjgwVRySOGsD89wZ\nte0cq7oTyUf0uThpLH0dr5eiwtoBMb5uUeSBn2/Joky7LDATbbGvZMSIA6LE\nApM4kG1vruy1TyIpb3y9j98qHfKpBWmLESLgvi0aYvOVayGXNM6/pnwiJ2EO\nmyoViIN+E7dX2jSq5WRa/G1Mbs7NMwp3Rh1oI3dzIis2dl6FvrJBmi8YAhz3\nIVy38O6CDj8Pz100JCjnMjKiziTYxD7GVIoHYzblNnqvPnKjDpJhdFwt7Oc/\nBs41cqCbpD4FmZpOtmbNIodd06ixvc5yP+dz0yxChty3fb8OIcaabG7kLwJF\ne0qe0uxKVStiSmdty9JTZ1XR+kK7cmxK5Aa3hSPPiwXrO2D2znNlDHDCULK7\noXbC\r\n=6tCf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDMHyMHdJ5E2/nR4Vp+Bp7suXSDcYbz/BQFvgwjSZ8FFgIgN3918HkSAEh0rCetDZxE/p6rbFakGXFWD+2sKsVSIU8="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_2.2.1_1554314689829_0.5799863946785464"}, "_hasShrinkwrap": false}, "2.2.2": {"name": "istanbul-reports", "version": "2.2.2", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.1.0"}, "devDependencies": {"istanbul-lib-coverage": "^2.0.4", "istanbul-lib-report": "^2.0.7"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "engines": {"node": ">=6"}, "gitHead": "9f8aebf1f08159df20358d77fe98c809d2027c5f", "_id": "istanbul-reports@2.2.2", "_nodeVersion": "10.15.3", "_npmVersion": "lerna/3.13.2/node@v10.15.3+x64 (linux)", "dist": {"integrity": "sha512-ZFuTdBQ3PSaPnm02aEA4R6mzQ2AF9w03CYiXADzWbbE48v/EFOWF4MaX4FT0NRdqIk48I7o0RPi+S8TMswaCbQ==", "shasum": "b84303c46fc0541c3c3b69abae687c42a6907d44", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-2.2.2.tgz", "fileCount": 28, "unpackedSize": 94043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrSjtCRA9TVsSAnZWagAAQkoQAIPtk6Zbw2RHh94T0PAk\nKtjp1mj7z86qysFgUZRyJNNZ1/49xiBIhKL3iqfRy2rKEfz2q1iPj5Y/GprH\nm4HfY1nBeFKRAfyjG9zpfvb4aPPuHsYLvKUmu/rJxWH0jWDALopD40Yaj98I\nD/l56dWes7t3EBlLWOHPBjRjplAfEBRGekO6l6NM1RNBLLp+nfGmfl0DXU2l\nDji6qDEjY/3aNEa6+Xmmc0jETChOXLgwWdXYGKIXbXrBLytN8mxTAdm+xWA5\nOF/SlOekFvddtlqo7W8SbAQbfB7LbV8DmKJldmuCjVzO4lvJEbVzX0aLs6oE\nO2DcfRJ3GYyDKsD+kGVGk+p4VVc6rLa+pxF7jdT4lEdFsKwmH8/Z4SqxIZWf\nsCNVlKfVtKli1Gucx1ZuRaKMb9toVypNNjOi4W44ZtDJePjl0gA+mdCP95VM\nq/kuiWURi2xoyGcrcT9h119veX09JLMzr9EEETbhw3nqr+ZNijr7ENPG2iOj\n77Zj48Gy1xwcOpRwk9LfOHZGO2OyKurWnk8lhqMGRL4BUig8K855lqHw/doT\nMON2iIfz/CjfxmntQcDJohQHWoDrl6jdQeNjEnCnGSeoG+d651ULnx8gZ/Gu\nUuPQpGigXaYfcJkqUYb0bgfwCh2t2nnjtnhRiFAw9zUWDu1mIf3VLX06PTQ6\nhV9U\r\n=02Gg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFEboHhro9+QyrJpnxDLJLNJsjQjEc+fRSxUJT7aS4z4AiEA3cMYd0csd/L82rf/fDtDnSH0WQ0YY8jC+sXC2V4auFc="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_2.2.2_1554852076409_0.5577931037657544"}, "_hasShrinkwrap": false}, "2.2.3": {"name": "istanbul-reports", "version": "2.2.3", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.1.0"}, "devDependencies": {"istanbul-lib-coverage": "^2.0.4", "istanbul-lib-report": "^2.0.7"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "engines": {"node": ">=6"}, "gitHead": "d98bbced044b2416f488bb1bbd37efefd1202a52", "_id": "istanbul-reports@2.2.3", "_nodeVersion": "10.15.3", "_npmVersion": "lerna/3.13.2/node@v10.15.3+x64 (linux)", "dist": {"integrity": "sha512-T6EbPuc8Cb620LWAYyZ4D8SSn06dY9i1+IgUX2lTH8gbwflMc9Obd33zHTyNX653ybjpamAHS9toKS3E6cGhTw==", "shasum": "14e0d00ecbfa9387757999cf36599b88e9f2176e", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-2.2.3.tgz", "fileCount": 28, "unpackedSize": 94367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJctt4DCRA9TVsSAnZWagAA34AP/izFj6dieJTZ2KLzrq2r\n2Ite2+H+nxNd3dtjaljfpTX1ajo0U65CarB6+VnEB+E3mw7jnvg1PeIuMKca\nZFbx9urKGU5cvbXxGwM/mCQ7jIcqatzZ1uIHfT4yPBw9XBho2iVwIJ6z4kqz\nU6KZ4pt9IgO8+3rFO5AkD6aE2EJUpjSuZNrSSuK8IAExFr23DQGL2GfEgUaF\nV34x97CLlNnywTUoivadqPW4PuHoI82mBqmkPyOt523lY1GrK3OwjWKH+gQl\n/WwFGx5uFT0ZRb6+fdPP3wqvTt69M9o5vKJzBSL9xx5qP4Ef8Gz0kBoTmHrd\nvNNVJf/fqqicy1g2/Td5VVKaDwP1pZflpj5DZNITKzojacwz6dSvnXMfS0F+\nKS/pTwL2ZET/1j60GOXuiOmjiPE5GvaHcm/+wYP+DD7GaT6ZJCNynAjWqu6W\nCMJwjo9RinY3gJXRq1/82wZyiVxzxVvEk5AgU1EQFU5SWFY1tfarVYJCiNEn\n1iCVuhi+CHQjdXoibqh1uATHHNnSIR3CBcmwqtA55eoIdYK+CEJ5r3hPTcsj\n/snRznEMthc3ueKirXtDDUWabX3lf52h+/BQyrSfwBYv+tMxNVETxS7iQSbZ\n/A2E/oU9RRqGzoWyyLOfK7kMJPNi4FPgqh/8B6TFl+BVMnlbj58pqsHjmNfT\ncw//\r\n=3t6t\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH7NnEUwIzk6dVfE31pHW9vwydgljKao/x8JIuxVDmAhAiBtLzG8mqJ/RKXWF+6g3KJTIJNLAy3TasNWO26xumC62g=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_2.2.3_1555488258906_0.6767728659475778"}, "_hasShrinkwrap": false}, "2.2.4": {"name": "istanbul-reports", "version": "2.2.4", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.1.2"}, "devDependencies": {"istanbul-lib-coverage": "^2.0.5", "istanbul-lib-report": "^2.0.8"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "engines": {"node": ">=6"}, "gitHead": "90e60cc47833bb780680f916488ca24f0be36ca2", "_id": "istanbul-reports@2.2.4", "_nodeVersion": "12.0.0", "_npmVersion": "lerna/3.13.3/node@v12.0.0+x64 (linux)", "dist": {"integrity": "sha512-QCHGyZEK0bfi9GR215QSm+NJwFKEShbtc7tfbUdLAEzn3kKhLDDZqvljn8rPZM9v8CEOhzL1nlYoO4r1ryl67w==", "shasum": "4e0d0ddf0f0ad5b49a314069d31b4f06afe49ad3", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-2.2.4.tgz", "fileCount": 28, "unpackedSize": 95033, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcwMq7CRA9TVsSAnZWagAAyeUQAIgyZmSoDbe6U3QduO2c\npMmvCM+CTTz3AlA7Or6rTJKPresasFtNed9ipXgrNq3SNjA13LzTEfLLSL5v\njdrS2908unhHmJEZidGxnFmFLgGZVSrd+iJZguJwTxYZ56WoQFXLd3Kl8Hu1\nPDtwjuqxQk82GqqOq0w8RNnFv2qYstuR0E9npmdT3/CRg6ifgF9k6m8ckD/m\nsTUFel7cv97ixwdPkH5/sorZJMDRLjYEfYgEqFk44JmapILsw3RT1fVMvJYh\nw04hHtAO7HwwKTKPtCO2dtajol9NXsqUfi5ZKm3Fb9HDPdkO8GaG+Nl4Cl/w\npesQKLqq3wB8TTMofw0btf+5zkxylbDCqlTYvOU6nlbgFwfdYx/04BAGlCtt\nwt9lJfhXbZm6b9Sq6EA76t3kMKU/5DfN4z67Td6Cfct8+dFE3LG/QwkcnwLJ\nXdONbV3QXwKpL4+5kqZRaaPRHreLsK/P3zmphWvburwZ/eMlAtj8D9MrM22S\n3xEvxIlXDz8sasC8pFrZKcflAtwaSGxKcNy5vcj0YSzifddb6GfD24/SW8DP\nUeSlfXFixkTygQ6QtU1dUz+oSw8e0judk3BwcAZl/iWn90KRwTxNwTT5vM4W\nZStt5JkS3yrfNnFA2WbKb067d9NpIGwG3FrKEInHA8Z1IIghnPOfLb91uMi4\n2hDo\r\n=la5U\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqeAnaYCOP1DmK9UFLoHxiWmEntGxLtCy1NTSkDk/NmwIhAMAlzNRq6/ln4r/undQKqlxHibHYL0YCqJLJtnDQzqE9"}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_2.2.4_1556138682947_0.8561350040829006"}, "_hasShrinkwrap": false}, "2.2.5": {"name": "istanbul-reports", "version": "2.2.5", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.1.2"}, "devDependencies": {"istanbul-lib-coverage": "^2.0.5", "istanbul-lib-report": "^2.0.8"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "engines": {"node": ">=6"}, "gitHead": "679fc641b691442c4e9274e07809fef35272161d", "readme": "# istanbul-reports\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-reports.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-reports.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-reports)\n\n-   node.getRelativeName\n\n-   context.getSource(filePath)\n-   context.classForPercent(type, percent)\n-   context.console.colorize(str, class)\n-   context.writer\n-   context.console.write\n-   context.console.println\n", "readmeFilename": "README.md", "_id": "istanbul-reports@2.2.5", "_nodeVersion": "12.0.0", "_npmVersion": "lerna/3.13.3/node@v12.0.0+x64 (linux)", "dist": {"integrity": "sha512-ilCSjE6f7elNIRxnSnIhnOpXdf3ryUT7Zkl+TaADItM638SWXjfNW40cujZCIjex4g4DTkzIy9kzwkaLruB50Q==", "shasum": "7fd354847124b46861365ac41165a4dfe5f67ea5", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-2.2.5.tgz", "fileCount": 28, "unpackedSize": 95351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyua6CRA9TVsSAnZWagAAsQoP/iGU7CMUhyWgy4YtLfBl\nVOJvnHkxwAon5pFjvzY9A3yE+yNPfK33nr8lhzVdhX9yTnLGa1jKJoPgbk0t\n3zqTD3/YpDv7rX8IKnhJZrwlVv69AAhJl3iY1zDCAu5oVMCow5vdH+hD45Yo\nQgFJ8DkqEBpK3KaH1z328gXDBI/NrphCIB+fCtbLgapi0R4/+5HvREkNfNsd\nqIFzvgCXryUyGwfyPXI8btRXoDR3lR0LcmDGF2dJu5IKtmqeT3rzOGb9ZCoc\nnqNgEBIGLfm2BHDaYYeX6kMnzZ9+HoFNF1MDjChGjHSp7jCd5Jm7MPR/7/l3\nAAwB+/5DLAPs7cNOzmNNjJbT9oGz0LrmoRo0UsVui/Wxrgft4Ms5qTW3G8le\nFNx10rJ/aqQFlfIVnLh06H8l1XYFD0r9dafhGafpcsgoYM5iGs6plEHNuX8D\nk/D6v7B/rZsRyTA2U8pxYD+lwP2W9S1WprsBniQLhCaqz6yNtTfIaWz/MFG/\n/m307YL1jn+fliBepneysjJjQ36/QzAf44HVZVZ+wTKP/9D3x8kKZxEN0uGi\nnApztHc7s5eUFl/tbXIdeNPd2mzLHPFLArzuoelCbWVPS62yf5lteJ2CcBsv\nayEqXZwV2fmkuvkFmLWCKP95QKvQlGftDrU5jP87HfH6VP11Xn4Uo+9Q0qlo\nPut5\r\n=OS+p\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHrZCYPjDlTVBS3Njp/JUZW9UrrPhmvW/xxel0Do8fhGAiAOL1LF7aLOSbsghSiHSrKSti7rKcwNknqXnIXc80H7aA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_2.2.5_1556801210206_0.6302757094029274"}, "_hasShrinkwrap": false}, "2.2.6": {"name": "istanbul-reports", "version": "2.2.6", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "mocha --recursive"}, "dependencies": {"handlebars": "^4.1.2"}, "devDependencies": {"istanbul-lib-coverage": "^2.0.5", "istanbul-lib-report": "^2.0.8"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "engines": {"node": ">=6"}, "gitHead": "90e60cc47833bb780680f916488ca24f0be36ca2", "_id": "istanbul-reports@2.2.6", "_nodeVersion": "12.1.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-SKi4rnMyLBKe0Jy2uUdx28h8oG7ph2PPuQPvIAh31d+Ci+lSiEu4C+h3oBPuJ9+mPKhOyW0M8gY4U5NM1WLeXA==", "shasum": "7b4f2660d82b29303a8fe6091f8ca4bf058da1af", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-2.2.6.tgz", "fileCount": 28, "unpackedSize": 95539, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc28b2CRA9TVsSAnZWagAA1jQP/2S65JmZuAZHapY4B7Lc\nd7ajktEsSvLKnBJh4lkAsMzn8zsWaSag3GlF0B5SIP9XKjb4HG7fosMH2iQr\nbadsEsMsCwdtJQx28BH3qhAvTw9scfhp1Fx7ZWZb0qF6t0121Yv9N2l59RU2\nxO+Xjo+CViHgBL0+Jmx+cmuoXbTqto479XayV+/SdsDoPjx3jGKBjr83yBJ9\nRTvXKAiSlFqAxNZK8h0X7SDSDgiAQe0sQVXfdCat39J2VxeBUeI6OkQz5cO7\nGrRsTUZt0bqyFrSrIy62dagiiOPy8LUqlIeLTaBG911F/lZlRv6O5O/ps4ke\nOEIsuidDs4ElSl5r59Ch6op9f0oEUdpAUA0xTWeGpTciI79flxhodL3ITgqK\n2vlPVMlXxgsHQUozc/L8YuIPCNt7BGrs+qMwJ2dl9wpiZK56bIWev0/SbK11\n5upNpVvQ/groH6vGfJeltoBQvYNrJxd3sLCHWIGlzVnjNYKHLxnKnOxZl0bR\nUHhsgH9md38Ii7oEykQHX/2JiewyV5zv9B3wPfVrcIO1Uk1rAn/xA9Qx61dd\nNsqUYX3n9Haz5I3HR4XCMCFbiE+00Q9Nr9oN2umbHW1tdvoyjk3Aq9ZNwPMe\nTMmy/M+Q7M/A6Di2f62anlees/NZCNQsfjfEYvhL64bKHH7oRmJtuEf8Tri8\nn4Vm\r\n=jweR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICkitk3DaWAOupU8QjFfGIAmStxH3XnoiiDevnaNTXi3AiAx6XPIpfN3U9PaNwZ2MY+9NOBmGUQLeNuZ6JPAk+Pz6A=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_2.2.6_1557907189667_0.38575683721006615"}, "_hasShrinkwrap": false}, "3.0.0-alpha.0": {"name": "istanbul-reports", "version": "3.0.0-alpha.0", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json mocha --recursive", "prepare": "rollup --config lib/html-spa/rollup.config.js"}, "dependencies": {"handlebars": "^4.1.2"}, "devDependencies": {"@babel/core": "^7.4.5", "@babel/preset-env": "^7.4.5", "@babel/preset-react": "^7.0.0", "chai": "^4.2.0", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^3.0.0-alpha.0", "istanbul-lib-report": "^3.0.0-alpha.0", "mocha": "^6.1.4", "nyc": "^14.1.1", "react": "^16.8.6", "react-dom": "^16.8.6", "rollup": "^1.15.6", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-commonjs": "^10.0.0", "rollup-plugin-node-resolve": "^5.0.3", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^5.0.0"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "nyc": {"exclude": ["lib/html/assets/**", "lib/html-spa/assets/**", "lib/html-spa/src/**", "lib/html-spa/rollup.config.js", "test/**"]}, "engines": {"node": ">=8"}, "gitHead": "2e885073a9398806c9b8763dd39418398182ca34", "readme": "# istanbul-reports\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-reports.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-reports.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-reports)\n\n-   node.getRelativeName\n\n-   context.getSource(filePath)\n-   context.classForPercent(type, percent)\n-   context.console.colorize(str, class)\n-   context.writer\n-   context.console.write\n-   context.console.println\n", "readmeFilename": "README.md", "_id": "istanbul-reports@3.0.0-alpha.0", "_nodeVersion": "12.3.1", "_npmVersion": "lerna/3.15.0/node@v12.3.1+x64 (linux)", "dist": {"integrity": "sha512-9NjF4ccVWlXooVv1OOWi/KrEHDJS2042fQq9jt/Oj/UHEtsZO+3LlrNYbRFI1ICsDxQEKcG7zJmsE8rgi/IIcA==", "shasum": "f551accd599644c215627153402dfbc95d38b56c", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.0.0-alpha.0.tgz", "fileCount": 42, "unpackedSize": 270579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCifBCRA9TVsSAnZWagAAhBIP/Rz8M72wsXXOYopE8/9G\nvMpVlydMzhBROgrtf9Io/uTZFPpaG256g0szXjtVT0xeALyDYnBlf2SqRXqI\nxTSfX57Yx/qfPKHGz0vFJOu1qhUq1/OhjGzL/1CntGpBtWQ+EUbwZOWx/YPw\nD5sIOVb62PzOOFlxAaTGlHynj2jnz+0UFfXewaSWkbcw0EoSN9XIku1tHcKg\nAbtX6AxCxXN6a7yIcRWfES4sb90VIHNVXWI3cECnHfOi8El7S1uonLTC4vZD\nbnvdV9IIWEwe8RuxrGvfGf0bQz+f1wP5Cj/jBTx5yzagV4zY7kGX/sLj0xh7\nhUsh6Kv0/Z5j+iXOaJMTMEY3Qy7ZTsUOCu3uP6bcFzv3NrAYRMSU/WvVXMqk\npoNpfyEun7J9mdHT39T3o6NoLvTwtFaK5XYPfAo3yStNTvjScPqPYl8QPCrX\nHsGUko+SUUnE4DB2bhPAn/tstitFXXFUUnh0pAaiWN8fYFiorFZwB2fsUTcX\nplxI2eyJlBoArCefv39hsmHcj7maUz/R/AYiRx/+4+kirDBDeJSG9mwuY7IN\nz0zpgkbws6I5t/AJNb1GCFF83zJRSK2mKC9YuhEaJB7LZlxEE/IHBmqPK6xY\nADtTWKmiNKIL2esoWOeBftNuvW1o/N2kBQhaTHyOIgZFbcXN5XGDlQL5tFTk\nxnZF\r\n=UY0i\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCBjYqIf6hD343q3CllY5Zu47FcHWyNuTUJRWvqbUznagIhAOJ9iGRLSCvK0skQP+XFUouCa8QfnjJQOuY/cDKE83n3"}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_3.0.0-alpha.0_1560946625253_0.3540744527631927"}, "_hasShrinkwrap": false}, "3.0.0-alpha.1": {"name": "istanbul-reports", "version": "3.0.0-alpha.1", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json mocha --recursive", "prepare": "rollup --config lib/html-spa/rollup.config.js"}, "dependencies": {"handlebars": "^4.1.2"}, "devDependencies": {"@babel/core": "^7.4.5", "@babel/preset-env": "^7.4.5", "@babel/preset-react": "^7.0.0", "chai": "^4.2.0", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^3.0.0-alpha.0", "istanbul-lib-report": "^3.0.0-alpha.0", "mocha": "^6.1.4", "nyc": "^14.1.1", "react": "^16.8.6", "react-dom": "^16.8.6", "rollup": "^1.15.6", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-commonjs": "^10.0.0", "rollup-plugin-node-resolve": "^5.0.3", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^5.0.0"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "nyc": {"exclude": ["lib/html/assets/**", "lib/html-spa/assets/**", "lib/html-spa/src/**", "lib/html-spa/rollup.config.js", "test/**"]}, "engines": {"node": ">=8"}, "gitHead": "371adb154f0189b793e636b5bbcc63f183d36c2b", "readme": "# istanbul-reports\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-reports.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-reports.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-reports)\n\n-   node.getRelativeName\n\n-   context.getSource(filePath)\n-   context.classForPercent(type, percent)\n-   context.console.colorize(str, class)\n-   context.writer\n-   context.console.write\n-   context.console.println\n", "readmeFilename": "README.md", "_id": "istanbul-reports@3.0.0-alpha.1", "_nodeVersion": "12.3.1", "_npmVersion": "lerna/3.15.0/node@v12.3.1+x64 (linux)", "dist": {"integrity": "sha512-GOYK4R26EtPuPJ3P+jz812Nx7MqkYwB+cH+I7giDPtYMByxhqgpT9dC33GOh16P0F7nThbxWsT/yelk2DUy96Q==", "shasum": "9bb6e35f41a826edb389603da804dd39a1880bca", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.0.0-alpha.1.tgz", "fileCount": 42, "unpackedSize": 270938, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdC5XDCRA9TVsSAnZWagAAk4UP/2VDwDnBXFOfrklVFlCw\nYmG834lz3WGs/xBLozqrYfDDwIFV2URBU7HESEo7ecKK4eYTdOULL2CUUw8J\nrQMWkv14/+ulkwwOwp1WoqH/wfZrq6saR+dBnlw3Zq546kPAvBB9wGAyr1mx\nPzuYhTGvlctqv3fW6YFFzgKS0fWH94VXvZJkuT+M+UAKKjIMwDVfT93OSC/X\nek17EgH0EB9ERvFJLvpW5qqApIsIibkh6DLLh3AXHnLc+1mix2np1clVfWw8\nhjtWgYRXdPj92Om8DGGR4jK5JOMB5emGRWCAMqeZzVdV6cWIFDstmXnFOZ4P\nKfa5Jy+9o82bulti9/a/nU1yq1jxK2aM0766MD6eDL13yaBgqMA0+YLUbywn\nuLI6ZnInleHgjsbLoREI8ZZGWyymeNL0/HjnnFPh0l+mvmNJCnWzdZyi2ZFz\nX63XsGdWudXdt+J5ue90vhC890BB6X+GBZrCC/fVZGBrVSQGrXo88rfrYykQ\noLIoUg67l1TB0uTIjJwt9IBEy7A8M4Zej91srMRb8+4J36HBUvt2gRpzVf32\nKzoMA2O0hbTZqECMzL2ZvpRsLQ10LhIfSPEoyrlT3FZ97D0wEutkdQUT+y1r\n2bAen50BcvnU1ezA/2wIRU5shdOJaF+sSaIjcZaPac1B6uFoxVYBT1KEsBzm\nKHzP\r\n=R928\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCDGyhE9JptvUNTvWWiDc6ppzkMQLdwJZKO270mID2KOgIgUJz2CPCSlCrCsJ63l/HRwAAqXwvyHFBtcT+PkoXcweI="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_3.0.0-alpha.1_1561040322803_0.8784577162521956"}, "_hasShrinkwrap": false}, "3.0.0-alpha.2": {"name": "istanbul-reports", "version": "3.0.0-alpha.2", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json mocha --recursive", "prepare": "webpack --config lib/html-spa/webpack.config.js --mode production", "prepare:watch": "webpack --config lib/html-spa/webpack.config.js --watch --mode development"}, "dependencies": {"handlebars": "^4.4.2"}, "devDependencies": {"@babel/core": "^7.6.2", "@babel/preset-env": "^7.6.2", "@babel/preset-react": "^7.0.0", "babel-loader": "^8.0.6", "chai": "^4.2.0", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^3.0.0-alpha.1", "istanbul-lib-report": "^3.0.0-alpha.1", "mocha": "^6.2.1", "nyc": "^14.1.1", "react": "^16.10.2", "react-dom": "^16.10.2", "webpack": "^4.41.0", "webpack-cli": "^3.3.9"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-reports"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "nyc": {"exclude": ["lib/html/assets/**", "lib/html-spa/assets/**", "lib/html-spa/rollup.config.js", "test/**"]}, "engines": {"node": ">=8"}, "gitHead": "4d5e777a9bc4847d178ad31f379307124cdd1e4f", "readme": "# istanbul-reports\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-reports.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-reports.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-reports)\n\n-   node.getRelativeName\n\n-   context.getSource(filePath)\n-   context.classForPercent(type, percent)\n-   context.console.colorize(str, class)\n-   context.writer\n-   context.console.write\n-   context.console.println\n", "readmeFilename": "README.md", "_id": "istanbul-reports@3.0.0-alpha.2", "_nodeVersion": "12.11.0", "_npmVersion": "lerna/3.16.4/node@v12.11.0+x64 (linux)", "dist": {"integrity": "sha512-eLug1s/pXGi+WWD3raBorQENqJWnttnlUPLlt9pCXC7Nn3yXKkGHHzvJToIxTzwVzdZkDdjAgnsdi+1P+43zHQ==", "shasum": "c5868adbac74a164d9a708714285d4381ebe377e", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.0.0-alpha.2.tgz", "fileCount": 43, "unpackedSize": 282685, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmT3+CRA9TVsSAnZWagAARUIP/1coFDi1HRmj1zzo4zpv\n+6SDpPDxAKWLIsQR54qFr9h7NPhWEOryq0l2zOJhQlUaelutoUZ59MG4pin2\nSJkhNDUylcRYPpnEyGZJRmSxGeTD/0Gi+9AKjwgOGI7rl+q7B/F/8E5waGgO\nS8aNZV5yy31xS9SZ0pWXibpO8LnO/XPWx+YjMsJVYFSFh+0wNEScFH0zlWZN\nyOox0h208z82VZsd/u0Ky0SekTKqtFzORd/O4CiS4FrjzVzP8foh1Y77SWYX\nrWi4furrK3QxFiuH2ejkNzgmRIVmUWnGwgL9eGUeh9iaTmn2ekYvaEzjqh5x\nmVTlYoNb4Jpd02HvyYb+KRrEt4dsrZ6qHejNHXwZCbULaq36m0wXeMu4/w2R\nNaR0fUSzqoQIny4xim1G6RHsnCOCKgZkKG46PU7Z0tUl+4bDr9CH8+Z9F9Dp\noeS3ur0cIhcjV3p3EeIaFOFod3K8bsjiFqoGWw+JU7/IX10oOrs1bwPbYNCY\ncVbkG7rrbJl2Wvq/O72+69ZPVCGg+jzilhfbioWBZut+aXeQ2ILijaUgas5i\n4QB+UdWI7WKdR2aVtkC1lAWh4tCAZ7jFzlNwPHNJqsGyFivfVCIv8ZaLNjan\nUWfVgmgentq/TrPZl1zjdQOBkZ0wE4d+IHU4akItKbl0sC+X/TYOYutCr6hR\nVQvU\r\n=YSog\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBLaEmOPD1jpgY7G96sTTCzUi615T3OIWeU2QwoAVELiAiEAoU7beGUZZZDlLEgYBlObDPnJCftRdSJ459kPzjHGQ9I="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_3.0.0-alpha.2_1570323965745_0.589216955883002"}, "_hasShrinkwrap": false}, "3.0.0-alpha.3": {"name": "istanbul-reports", "version": "3.0.0-alpha.3", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json mocha --recursive", "prepare": "webpack --config lib/html-spa/webpack.config.js --mode production", "prepare:watch": "webpack --config lib/html-spa/webpack.config.js --watch --mode development"}, "dependencies": {"handlebars": "^4.4.2", "istanbul-lib-report": "^3.0.0-alpha.1"}, "devDependencies": {"@babel/core": "^7.6.2", "@babel/preset-env": "^7.6.2", "@babel/preset-react": "^7.0.0", "babel-loader": "^8.0.6", "chai": "^4.2.0", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^3.0.0-alpha.1", "mocha": "^6.2.1", "nyc": "^14.1.1", "react": "^16.10.2", "react-dom": "^16.10.2", "webpack": "^4.41.0", "webpack-cli": "^3.3.9"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-reports"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "nyc": {"exclude": ["lib/html/assets/**", "lib/html-spa/assets/**", "lib/html-spa/rollup.config.js", "test/**"]}, "engines": {"node": ">=8"}, "gitHead": "70f48b7e6b60d17b8e2b5a03388a004deb6b8785", "readme": "# istanbul-reports\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-reports.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-reports.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-reports)\n\n-   node.getRelativeName\n\n-   context.getSource(filePath)\n-   context.classForPercent(type, percent)\n-   context.console.colorize(str, class)\n-   context.writer\n-   context.console.write\n-   context.console.println\n", "readmeFilename": "README.md", "_id": "istanbul-reports@3.0.0-alpha.3", "_nodeVersion": "12.11.0", "_npmVersion": "lerna/3.16.5/node@v12.11.0+x64 (linux)", "dist": {"integrity": "sha512-rZVjflOwhnHDS9Ch8aCP71yzr9BpKKGyptl1CvXYgIM6WA6glg+yDv7hblAoG0jWOzfsC1NiGG9Wy7fue2l5KA==", "shasum": "4e50001f2aeb390b26dcc3e4841f2ec76274e7bb", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.0.0-alpha.3.tgz", "fileCount": 43, "unpackedSize": 283197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdql0LCRA9TVsSAnZWagAAs1UP/0DfeRkS7s/TxPsAt/jT\nqG4pIEYQPW249lIikouOLRG3ADh4ZLim+Be2VM/DpUij8/7XTyIz8mFFgCJ2\njX2Q+GbjTLpauNPVIlLK/8tZ1AeTU6juvrTaxaNF0Z9BDKowio87HFKKQH3x\n06DVFGOQ+RHtq/x6VScvUKXqSI37n2QwveXkYT72NKZ8IKQodrLE01coXnfT\nSbmKVyNMR98XFzNeEWs89Sf4rT2Fq18NxozYM7n64icrOuIRbHSQGvCpzU0k\nzED8n7T9uabstTJhIbIJDVDTHpEp7gZDn+V3wELbSz7FJIl7uw8nPehPwl3f\nbxkUB3YsauIE0T9oV2nzMUkWCcwuyPoTPlWpgHbAxvUt9FFqbXGsReBEuJUx\nDzRI+98nlb/1yewWU0s4ppsEcDBUzuUxvSJQ2STuDR4D5EHtqvhtOPtjwmWd\nvC/amWpV9euSqMMPXkJFo2O/shgTWGbGr19LnoUaFT6WQTsdtxhPZ5Qa/13m\n3K0GGyOXVRGf/7Qf6Tl0F0G5q/ArNV5cHhNG2GNSiB0gp3pcimcYb+8Ol6Qo\nVROf78aKHeRihuVXZxWKh9lnCQyiZQQDG4TRC+YeUi4837v1PqyW02xu/oFc\njfjIoGOORr4ffUD4lMoT+IMVByn8pHiJSyEJ7brtHtEn7PGDYUZVkHJmDrbI\nCs6I\r\n=TTrl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC3nMi67rhkMigCBal1yu+7gfMizOrkIVxc+4K+3/jQjAIhAONGffwOrZLYNdBjV1wz3fLhlW2oNCX/M/SdFpal5NJe"}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_3.0.0-alpha.3_1571446027224_0.6602524273671082"}, "_hasShrinkwrap": false}, "3.0.0-alpha.4": {"name": "istanbul-reports", "version": "3.0.0-alpha.4", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json mocha --recursive", "prepare": "webpack --config lib/html-spa/webpack.config.js --mode production", "prepare:watch": "webpack --config lib/html-spa/webpack.config.js --watch --mode development"}, "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0-alpha.1"}, "devDependencies": {"@babel/core": "^7.6.2", "@babel/preset-env": "^7.6.2", "@babel/preset-react": "^7.0.0", "babel-loader": "^8.0.6", "chai": "^4.2.0", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^3.0.0-alpha.1", "mocha": "^6.2.1", "nyc": "^14.1.1", "react": "^16.10.2", "react-dom": "^16.10.2", "webpack": "^4.41.0", "webpack-cli": "^3.3.9"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-reports"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "nyc": {"exclude": ["lib/html/assets/**", "lib/html-spa/assets/**", "lib/html-spa/rollup.config.js", "test/**"]}, "engines": {"node": ">=8"}, "gitHead": "c69ce0cce10c1fb394b55ef0169e44e70f14ad24", "readme": "# istanbul-reports\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-reports.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-reports.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-reports)\n\n-   node.getRelativeName\n\n-   context.getSource(filePath)\n-   context.classForPercent(type, percent)\n-   context.console.colorize(str, class)\n-   context.writer\n-   context.console.write\n-   context.console.println\n", "readmeFilename": "README.md", "_id": "istanbul-reports@3.0.0-alpha.4", "_nodeVersion": "12.11.0", "_npmVersion": "lerna/3.18.4/node@v12.11.0+x64 (linux)", "dist": {"integrity": "sha512-TPIUdttWC1oUvTL163ZtS0FqTlYaaAQBQdlVpF9Enu+w6oPOUR0p//WRdRyT/hbDG83PoQoSyatYzV6FrICDKg==", "shasum": "f703c04cc76b27e89b88c4fa74efec7865582e73", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.0.0-alpha.4.tgz", "fileCount": 40, "unpackedSize": 280379, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd0q4gCRA9TVsSAnZWagAAokMP/2Tlg75mufX0B5AI6Yco\nr7imfCRd6MclBvZQoak5c/EkY2W+dsRPDtSf+P7WZneJG/fhyBP5hDSbZCr5\nK5cisOtGygS6kxYvTqPobzUTk+CdUMEl6LvIKDTt1/D2rkwGm8uuW0UyFn5n\nPA4jlDaWNKcA7r0gLynS9FfYI8xH4u7uDXSfskyfW3OYGCUn2FsCtC7Mne0M\nVndV5ee4G0W9/sx1U30ybyxsZqk82TWoOZq2AUqV43vEuLiOIqN+0khgai1H\nUOkEzj3FzYByPHeuZDH/Pdvns0ZsyXO9Vy+wIqQUg9zdr33kZRqlEII67L25\n4rTEfcKGzpWcgDqP6lZFAnuMX/EWkmKDM67BFYMbwVHwSz8WZjHlKDjLT5my\n+nN0diPOT1dtwavqHWw6a+E1tbbld+V+gvBxa+hXe0X9sxa1yxOxXJ5Rk1RJ\nQucGhN4C2awu0XoZBgAmzOY1paJR1Jyvqf7+/Nikk/AnKysB9ppARey4RDQJ\nmHemkNnNB9wZS3qQKsJciuOv528Lg5L1Ht86cjDiPr7zlhK+jzHY4JYRFYJj\nwVI0RGerQI2nGwcOyaNvYP+o5/LbalZdKySlOyGoFdh49Byq/urpbDhT7gXn\nd92Tw9GLfM8ld4AVs/PcTfnrYzEz5l2Dp4/3eorSt411B0xg3rAF7tEHVa4p\n+mqk\r\n=Q8T+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHolFeRraQJe/9OS3NvgNHMtLqIeiOSXabmPWhQk9S5WAiEAqUYKxnjcYxm1RWZSSOAkyIIQ+6/2nUDBJF2PkNHl7HM="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_3.0.0-alpha.4_1574088224114_0.39366178542102426"}, "_hasShrinkwrap": false}, "3.0.0-alpha.5": {"name": "istanbul-reports", "version": "3.0.0-alpha.5", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json mocha --recursive", "prepare": "webpack --config lib/html-spa/webpack.config.js --mode production", "prepare:watch": "webpack --config lib/html-spa/webpack.config.js --watch --mode development"}, "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0-alpha.1"}, "devDependencies": {"@babel/core": "^7.6.2", "@babel/preset-env": "^7.6.2", "@babel/preset-react": "^7.0.0", "babel-loader": "^8.0.6", "chai": "^4.2.0", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^3.0.0-alpha.1", "mocha": "^6.2.1", "nyc": "^14.1.1", "react": "^16.10.2", "react-dom": "^16.10.2", "webpack": "^4.41.0", "webpack-cli": "^3.3.9"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-reports"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "nyc": {"exclude": ["lib/html/assets/**", "lib/html-spa/assets/**", "lib/html-spa/rollup.config.js", "test/**"]}, "engines": {"node": ">=8"}, "gitHead": "bf0141b44a174626f4a3769a19b86a6c14d95a7b", "readme": "# istanbul-reports\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-reports.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-reports.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-reports)\n\n-   node.getRelativeName\n\n-   context.getSource(filePath)\n-   context.classForPercent(type, percent)\n-   context.console.colorize(str, class)\n-   context.writer\n-   context.console.write\n-   context.console.println\n", "readmeFilename": "README.md", "_id": "istanbul-reports@3.0.0-alpha.5", "_nodeVersion": "12.11.0", "_npmVersion": "lerna/3.19.0/node@v12.11.0+x64 (linux)", "dist": {"integrity": "sha512-m70NY8mnytN+e3C/UN8xVZ3OXcWBT+yVtupNdYy7X/nCiSHV7O4ImojikbQ+EgqvKO6JaG+Qowk9lBq9j2pjbA==", "shasum": "e8eb61c0bfdc46cbac5857482c6fa40481c5068b", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.0.0-alpha.5.tgz", "fileCount": 40, "unpackedSize": 280890, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2CcGCRA9TVsSAnZWagAABLQP/i/n+4iCK8bx3aGy4RdG\nsb4IG43rDOuPEPA8/jSOHWIkUfnk/BCzIxL4Nz/zZRjtZy9TsPjF5i+wQ80J\n2UmraMDLCmVnWSjr9M6xnxpqG6Yq5SgNvNANUN83sUwhDgkgZmkUATzlThX5\nuu5zaHbMDYrMxzJa3hGfisE9WeqpPuGKXGnwjiSynVXmj4P3co3U9ndTYVqS\nnlYc8IiLY6cFe41Y4XG5AOMGqlASJ9zoo20OUp4UYPOjPMD19gOlSgL69YbR\nGYudOAM/xZ4+7vBG8mHamJSNYwSwWM8XtQ5x0Drx7hkuIaITgj3Y0mDhXNnA\nsYXL9YCClZfKrGMkt4Fx8co8Uu9aYe1n0LqggDd5ZFzXqtfnMrK4jZqS0hAS\nmbTuGF2rzUyEngF8U+J1S6+vMywhEvRUv+CN4aGfq+kvd9RZ+uXoLc9AW4QV\npnlTvBiHGL5PbYPiiyVEEjuUNXz/lih2dR/PtVtx615dMWHUuUROBbBAhsRd\nbjB74M/Hsx3tn6oUAprmyQS7nZqzPUKpVeuQi4U5kM5x2q4wIR07eWwIbMp2\nzbXF+CIm79oK7Wlry8BtWpIkaf7eCSvyKnYwSK7O0dVN2WnIpflxBPgAxpJN\nGde8P5B20SNyImwpCMWXMvaFy/rmRqmyQllgUVC8KPuM/SLxA3YT/w3GcsFt\nc6K4\r\n=I90J\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDnXyZr+/nHKH9w3qlFcGdl+ghBIdA1DLRYsDZI/euDTgIgRt3kLuVuBk0X9Gza9KfLr7x6XtE9wCWQcz6G+v3itG0="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_3.0.0-alpha.5_1574446854455_0.26272107794332"}, "_hasShrinkwrap": false}, "3.0.0-alpha.6": {"name": "istanbul-reports", "version": "3.0.0-alpha.6", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json mocha --recursive", "prepare": "webpack --config lib/html-spa/webpack.config.js --mode production", "prepare:watch": "webpack --config lib/html-spa/webpack.config.js --watch --mode development"}, "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0-alpha.2"}, "devDependencies": {"@babel/core": "^7.7.5", "@babel/preset-env": "^7.7.5", "@babel/preset-react": "^7.7.4", "babel-loader": "^8.0.6", "chai": "^4.2.0", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^3.0.0-alpha.2", "mocha": "^6.2.2", "nyc": "^15.0.0-beta.2", "react": "^16.12.0", "react-dom": "^16.12.0", "webpack": "^4.41.2", "webpack-cli": "^3.3.10"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-reports"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "nyc": {"exclude": ["lib/html/assets/**", "lib/html-spa/assets/**", "lib/html-spa/rollup.config.js", "test/**"]}, "engines": {"node": ">=8"}, "gitHead": "9546946f0e4bc80714a5b318c59e459781f05550", "readme": "# istanbul-reports\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-reports.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-reports.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-reports)\n\n-   node.getRelativeName\n\n-   context.getSource(filePath)\n-   context.classForPercent(type, percent)\n-   context.console.colorize(str, class)\n-   context.writer\n-   context.console.write\n-   context.console.println\n", "readmeFilename": "README.md", "_id": "istanbul-reports@3.0.0-alpha.6", "_nodeVersion": "13.3.0", "_npmVersion": "lerna/3.19.0/node@v13.3.0+x64 (linux)", "dist": {"integrity": "sha512-JZpNKbc0xL/E5NP/ER7ox01bMw0NC01l5/h8YypGT/NaDPiI1/8AucNncDUxwdj/YhD8/cGD6bev3K6CAXXeKA==", "shasum": "51b1d89e2f41ba60187001baadb7cb56b8988867", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.0.0-alpha.6.tgz", "fileCount": 41, "unpackedSize": 282012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd69ZZCRA9TVsSAnZWagAAnrMP/0OsfFe4ldQ0nlDh/RNK\nOgaxTYpQPqKlLHU4IGTwMST1leMgJM6PPVNrmUdvSlMwkViMSG9cubbnD5VT\n2fvlZblEMR8EVsr61kW+QV7AVQzgO04v7QgP0rhcP/iE4y7z0W/PcSem6o1h\nokX/2jlgu6K7LKbjpoi3I8emkWdTWKMkq0TGUO9UM6xHRePPjq2gAdryHZHb\nvPYTEvwN9a+HpxPEwxFBhMRk3WZb2HvQFaYQRAJi8441xslQ5J3GIZoIrmjZ\nxScqgzNFINe2iUmLi438Ypw08qtS1hdZCgpvk03NimhAh9NcK7f7N0vkHIiG\n2HUgQvYFxfC10U80jCS3i3jaVb8PNFkxOzFOXzUTiTGxBNvsdd1u+KVgxcGv\nExmcdLVFydExNVuLJWe8n2CNQ//EyRVCMBFPIC28SwhVbxbekdul/vlczJFz\nUERUcVANQw870NtGXpRkMtLfrtaH8Bf8aZdXDKwM8HuuK6ysFWcWlF1Dexu9\nmP9o705PzZq1lJVYgbMyJCjbCsWeNPtLdZ7RF3gNp54h6242dDcNHjzvfyaZ\nQhrAtWXq//95btLNDYnalr2kvgrj3nQ6oih/CSDvUIXndTxzoSLXvXFmuOVd\nNfvNQ0YOGr3LcYSnuE4arRonu3eJAWIMh2pI4mwyUYR+P1xGc2g+YC+8t0oj\ndvkG\r\n=vHf4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDCXw7YRZyS2yoeuGPYyS7WgAyyjxdqpSEttCm1VyRh+wIgQVYpSVDRyaQnFcqTK9XUK7ysW+BwlhtP32Mm26vjBFY="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_3.0.0-alpha.6_1575736920937_0.32797415525543316"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "istanbul-reports", "version": "3.0.0", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json mocha --recursive", "prepare": "webpack --config lib/html-spa/webpack.config.js --mode production", "prepare:watch": "webpack --config lib/html-spa/webpack.config.js --watch --mode development"}, "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.7.5", "@babel/preset-env": "^7.7.5", "@babel/preset-react": "^7.7.4", "babel-loader": "^8.0.6", "chai": "^4.2.0", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^3.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0-beta.2", "react": "^16.12.0", "react-dom": "^16.12.0", "webpack": "^4.41.2", "webpack-cli": "^3.3.10"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-reports"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "nyc": {"exclude": ["lib/html/assets/**", "lib/html-spa/assets/**", "lib/html-spa/rollup.config.js", "test/**"]}, "engines": {"node": ">=8"}, "gitHead": "5319df684b508ff6fb19fe8b9a6147a3c5924e4b", "_id": "istanbul-reports@3.0.0", "_nodeVersion": "13.3.0", "_npmVersion": "lerna/3.19.0/node@v13.3.0+x64 (linux)", "dist": {"integrity": "sha512-2osTcC8zcOSUkImzN2EWQta3Vdi4WjjKw99P2yWx5mLnigAM0Rd5uYFn1cf2i/Ois45GkNjaoTqc5CxgMSX80A==", "shasum": "d4d16d035db99581b6194e119bbf36c963c5eb70", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.0.0.tgz", "fileCount": 41, "unpackedSize": 282839, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd/TUTCRA9TVsSAnZWagAAvBoP/3iT6D5pAx82f00YGdoG\nUpWiVgW/BY5q5VAIofeh5IYp9L7vfGetUnjrFNuwZFg3c+112SPXrEH/rSHu\n0gvjLR4E6lESKKNO6g1TvDpkYrouHKD5VSeq4gAI1AVZ6MefiubPFFXxZ5Ro\n6dOV4Hkevy7Tt3HdRMub7jJD8xyQDAstDHHYtDjjhZFnDaYzepH80lKDGd0s\nNbWk8F4BZ4Gd50hBaH71Ok9WmbdAv91FAEArSUKfwfTzzKLY8UO1pWUZI/Gu\nJXd5Isxz0AogppF57kSSyCCRCk0Jy1wh2SyO0h+Uk0dUe+92v81TyTFlvkyu\nAxaUHu2oDl1MfM0yLWy5NqcZY21e8o67yMFUOCd1sYbR2CZultfI8JIV01o9\n0NCfPZvlXA57MHDYiPYGGwQH37gXM8gRn1u0onmBXwGNtWpK32cFyolY2Naf\n15XR3HSf3RcfB+bE2paXT1gCvrAhw+XpZ1qbYzK6hiUp1pF35PkDtxGXpNU3\nu7NlWRWlrZn/t9L6hzVio0SLeRmnks34bYu1x81WZExcCFj4CvF5Y3hspgML\njTx8br1BWiUP9SaDevqGPxZ5CqaXI3ZWYn/1H44vX9qWlnRC/6q3+XSTP2BX\n7aSihVEg8L7Fj9NmsY31/V2zBtnJeXL3+aPHfkvCPwp2ODhZ879JP61RIW45\nXV/1\r\n=UUYs\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH06BeCFWBnale5xUu/QdqXUuQCiU/QuhoZ99r1SrlyMAiAIJ8MbYnSMASROQ9Sc3rVQARa2OsfF5cKjF4DslLNRgQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_3.0.0_1576875282314_0.8870901946773453"}, "_hasShrinkwrap": false}, "2.2.7": {"name": "istanbul-reports", "version": "2.2.7", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "mocha --recursive"}, "dependencies": {"html-escaper": "^2.0.0"}, "devDependencies": {"istanbul-lib-coverage": "^2.0.5", "istanbul-lib-report": "^2.0.8"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "engines": {"node": ">=6"}, "readme": "# istanbul-reports\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-reports.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-reports.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-reports)\n\n-   node.getRelativeName\n\n-   context.getSource(filePath)\n-   context.classForPercent(type, percent)\n-   context.console.colorize(str, class)\n-   context.writer\n-   context.console.write\n-   context.console.println\n", "readmeFilename": "README.md", "_id": "istanbul-reports@2.2.7", "_nodeVersion": "13.6.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-uu1F/L1o5Y6LzPVSVZXNOoD/KXpJue9aeLRd0sM9uMXfZvzomB0WxVamWb5ue8kA2vVWEmW7EG+A5n3f1kqHKg==", "shasum": "5d939f6237d7b48393cc0959eab40cd4fd056931", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-2.2.7.tgz", "fileCount": 25, "unpackedSize": 93277, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGizMCRA9TVsSAnZWagAAOosP/2PWzQ/dNoDx4w9IWL2+\nN8iuXvkn3Jn8NswGgSxCWy/4Ci8lrsBqIZWvTd7qfEaxLLgTh05y5CwJ9T2f\nZ2QRvLtPfLRzdQjY46K1yjtMbC0h/Wpn63M7xRxqFM+IwBTtRcJ8YAZ4mzLc\n4mQaoWRjSrQOuQEm842Ica1x2fn/T2vgSW5pL3S2zlwIDFovwV6gbTnpeMCt\n5m3caLbZIlCzG6nLfyu49sB9DRM22MEvd5aiWOuN+sU9cQAAeng+YYuNAefI\nZ0aewt5fae2NZxfE8AlZgwCgbEUy7HCKFdmEv3mkMYpld6J5twVtwfRvXir5\nPpJjnw8UfILHrn6QedN+htVE8+HfyBQFM7QEbAVG4d2GcUryVjNkfi5VWUHl\nFXUwmmvmRSVoDmvD9pE8O7Z98sDe+1ddxo3xW27GKPmtiDJihcGZLlwX74Mj\npVHbGLP0u81l6k2UbSryZtAETAAPigSY3xB2q526s/mxD/QKppPyj9B9plMh\nTIpcDH/7fFOQdTcLwoCxRLuldW1kws4YK1M4o0QKvkZqUFCIGvmt6EWTZHPN\npTv/+OtvzhN1oHWYFFfsJZiBxGxviwpmTRukR8dVj3HCeVz/8T8a7SxZB13P\nlfBLI3ky/T0aejGw172nvVOBKFKegzAkUsSDTyz9/97+pKEDvVJaURk9mYjw\nB05X\r\n=HWWE\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDCtoxedZe75b0EqRBpxhYyDGE9lHmpd17NSxtlznk72AIgY+aK+CRHjs0KWdM96t425l52KWgjhehxcVC434r9SMk="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_2.2.7_1578773708458_0.027832923740840565"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "istanbul-reports", "version": "3.0.1", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json mocha --recursive", "prepare": "webpack --config lib/html-spa/webpack.config.js --mode production", "prepare:watch": "webpack --config lib/html-spa/webpack.config.js --watch --mode development"}, "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.7.5", "@babel/preset-env": "^7.7.5", "@babel/preset-react": "^7.7.4", "babel-loader": "^8.0.6", "chai": "^4.2.0", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^3.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0-beta.2", "react": "^16.12.0", "react-dom": "^16.12.0", "webpack": "^4.41.2", "webpack-cli": "^3.3.10"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-reports"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "nyc": {"exclude": ["lib/html/assets/**", "lib/html-spa/assets/**", "lib/html-spa/rollup.config.js", "test/**"]}, "engines": {"node": ">=8"}, "gitHead": "df24342395030dc2a40a7ceb0476a9897f3492a3", "_id": "istanbul-reports@3.0.1", "_nodeVersion": "13.11.0", "_npmVersion": "lerna/3.20.2/node@v13.11.0+x64 (linux)", "dist": {"integrity": "sha512-Vm9xwCiQ8t2cNNnckyeAV0UdxKpcQUz4nMxsBvIu8n2kmPSiyb5uaF/8LpmKr+yqL/MdOXaX2Nmdo4Qyxium9Q==", "shasum": "1343217244ad637e0c3b18e7f6b746941a9b5e9a", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.0.1.tgz", "fileCount": 41, "unpackedSize": 284384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefOe4CRA9TVsSAnZWagAAHOUQAJmdYpVTZ7N+NYnaXF6M\nBd90Zoh90LAKKog9aWnb570WmZWasKBBaCOQu9VerT7fEF3cG7MTU33mzD0X\nAid5N/5au/142yVBaCl79j9Iihn2+mCLTZVcdXCobdO+kL08aaCN8zjvIhoO\nvj3TMDNNtX1mwx3cqpfmAViFZtMQMrhtJvDOfux/Wy2p4pXS65Q5XOMM83Ba\nnkElT995ia8Fn3Fq9OO1TZlSuAQ9PF3mRbcDUluYI52NeHYhmR2YNu3PwtkN\nC29597VMT6OGmDiCkdYtw3YTnt69N8XrtK81psk7xasgCzGpCsrBU66ywwOa\nh4KwCSXTGaQyHhrw3odTwsenDX43NUaKshGmkmzbMx92ClamZ0ivjsSTmJgI\nUwEkxejXczxjTNUtX4Fb1+2qOJlC2gDF1ZnZe6Ij/4vGUbicpp0sl4l6YA3u\n/Q1mC/j+9GFIcVtO2geP9cnS1cLrQM5Qv0FQPSeRksgGO95tQgIax5XNQ9hF\n3lwxYFYE+Ma9PSWGCYuxYRok80yF13bjAXuVaacRTpfsKSmTMZUhjmUtviJI\nTwZok03KzeAfrYwte3niGBN72F7MhPLyL811kFlSdsT1FrA0vIPFcCfd0GeX\nS2owdgXqd/USrSd7SnaQWQDUPMInsw0zBAtaD/PzXxhjj1AjHkAcR2Rxjpd8\nshok\r\n=rIBn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCQGsrxYThCieEKlJP0qb/uTLhKS4C0Ayz1IQfLtBTpywIgAPcg0RFWhmNivZN5dsdMmAeZuLJdcs9OdNMTc16aAQI="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_3.0.1_1585244087780_0.5530645138668164"}, "_hasShrinkwrap": false}, "3.0.2": {"name": "istanbul-reports", "version": "3.0.2", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc --nycrc-path=../../monorepo-per-package-nycrc.json mocha --recursive", "prepare": "webpack --config lib/html-spa/webpack.config.js --mode production", "prepare:watch": "webpack --config lib/html-spa/webpack.config.js --watch --mode development"}, "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.7.5", "@babel/preset-env": "^7.7.5", "@babel/preset-react": "^7.7.4", "babel-loader": "^8.0.6", "chai": "^4.2.0", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^3.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0-beta.2", "react": "^16.12.0", "react-dom": "^16.12.0", "webpack": "^4.41.2", "webpack-cli": "^3.3.10"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-reports"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "nyc": {"exclude": ["lib/html/assets/**", "lib/html-spa/assets/**", "lib/html-spa/rollup.config.js", "test/**"]}, "engines": {"node": ">=8"}, "gitHead": "73c25ce79f91010d1ff073aa6ff3fd01114f90db", "readme": "# istanbul-reports\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-reports.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-reports.svg?branch=master)](https://travis-ci.org/istanbuljs/istanbul-reports)\n\n-   node.getRelativeName\n\n-   context.getSource(filePath)\n-   context.classForPercent(type, percent)\n-   context.console.colorize(str, class)\n-   context.writer\n-   context.console.write\n-   context.console.println\n", "readmeFilename": "README.md", "_id": "istanbul-reports@3.0.2", "_nodeVersion": "13.11.0", "_npmVersion": "lerna/3.20.2/node@v13.11.0+x64 (linux)", "dist": {"integrity": "sha512-9tZvz7AiR3PEDNGiV9vIouQ/EAcqMXFmkcA1CDFTwOB98OZVDL0PH9glHotf5Ugp6GCOTypfzGWI/OqjWNCRUw==", "shasum": "d593210e5000683750cb09fc0644e4b6e27fd53b", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.0.2.tgz", "fileCount": 41, "unpackedSize": 284875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehJGPCRA9TVsSAnZWagAA3wYP+gJivfMIpE1RZNgYBcbR\nSifrSPGLzdxMylVtF+KFYF82KLvJuVIZS59VyKn6a6J8/eniwSdHOJGjiN6R\nonaAit88bwwBtIU3pl0ZJ17MaoCBiaLjwddRp0qEPjNo7sSwy3fNBElA+CZQ\nxfBajMmcrl6NUlJImuXJG6QVQGR+gQczsGHtZdML7ttQ0IgVR8EDnDP2idQc\n8cXZIYy2FqQwjgdtLNiOJvlmvNmo+4AhWT+a/fr0dzBkjCLNKMhej6Ri9T5T\nRXNBdxW02YwdJz+LNZhQoKNbnOZ9JjLQUTSqM9vUIy2IxRJCgOJocAhF+8OV\nw1CaMACL3lHZykkaMeBdTaKERIdIYEwgGP+MIheiGwggBjIS4K01fB2ABltH\nDx9wxbldKrxNEsSYK/cOPqcFELjBqajMfOaY9ucnxMCHGZe7gi4XYCLxXxCJ\nDdlkLjBTJvRyaQifO6lqoaCW/Ww4bkmvtIUwjcW8OpuwuUXEOCT4APvbK2ei\nbQk+PhsInB+7WJtXeNFJ8aJCPsOkYh85IKBAm2GfELvjmX3nQ3tygrMnAih+\nrU0pvxFX3sUcEDp99lVnSbI5YpT7AkxGOVWtPlcq8MEv3gXg45bWbWTbMlkY\nIY2R93NOCDli868VrVIMlW347WA4jBE3uUfw2X+KNAdg3b7r+Z7qC+Stw8ER\nmpi8\r\n=hyj3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD6jlNdUhFSMgM6WGEfUTJO5XyL7cP8gXjEVJER0yvvKQIgEbhu0tsGEKJdethbFbohTj1n4GZPKz4185AodKZIzt4="}]}, "maintainers": [{"email": "<EMAIL>", "name": "bcoe"}, {"email": "**************", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "gotwarlost"}, {"email": "jg<PERSON><EMAIL>", "name": "jak<PERSON><PERSON>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_3.0.2_1585746319217_0.5091698822350053"}, "_hasShrinkwrap": false}, "3.0.3": {"name": "istanbul-reports", "version": "3.0.3", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc mocha --recursive", "prepare": "webpack --config lib/html-spa/webpack.config.js --mode production", "prepare:watch": "webpack --config lib/html-spa/webpack.config.js --watch --mode development"}, "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.7.5", "@babel/preset-env": "^7.7.5", "@babel/preset-react": "^7.7.4", "babel-loader": "^8.0.6", "chai": "^4.2.0", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^3.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0-beta.2", "react": "^16.12.0", "react-dom": "^16.12.0", "webpack": "^4.41.2", "webpack-cli": "^3.3.10"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-reports"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "nyc": {"exclude": ["lib/html/assets/**", "lib/html-spa/assets/**", "lib/html-spa/rollup.config.js", "test/**"]}, "engines": {"node": ">=8"}, "_id": "istanbul-reports@3.0.3", "_nodeVersion": "14.18.0", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-0i77ZFLsb9U3DHi22WzmIngVzfoyxxbQcZRqlF3KoKmCJGq9nhFHoGi8FqBztN2rE8w6hURnZghetn0xpkVb6A==", "shasum": "974d682037f6d12b15dc55f9a2a5f8f1ea923831", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.0.3.tgz", "fileCount": 41, "unpackedSize": 285898, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAsv0P/W87Nt3VehvoDRZ/V5uaDQaF6WR4UgEvYiMTQKAiAVSs6gmyz6MJnMu+E0ICMILHAUpnpaWbDKWgLB3LMoHw=="}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_3.0.3_1633533313813_0.3306425232498784"}, "_hasShrinkwrap": false}, "3.0.4": {"name": "istanbul-reports", "version": "3.0.4", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc mocha --recursive", "prepare": "webpack --config lib/html-spa/webpack.config.js --mode production", "prepare:watch": "webpack --config lib/html-spa/webpack.config.js --watch --mode development"}, "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.7.5", "@babel/preset-env": "^7.7.5", "@babel/preset-react": "^7.7.4", "babel-loader": "^8.0.6", "chai": "^4.2.0", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^3.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0-beta.2", "react": "^16.12.0", "react-dom": "^16.12.0", "webpack": "^4.41.2", "webpack-cli": "^3.3.10"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-reports"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "nyc": {"exclude": ["lib/html/assets/**", "lib/html-spa/assets/**", "lib/html-spa/rollup.config.js", "test/**"]}, "engines": {"node": ">=8"}, "_id": "istanbul-reports@3.0.4", "_nodeVersion": "14.18.0", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-b<PERSON>jUnc95rHjdCR63WMHUS7yfJJh8T9IPSWavvR02hhjVwezWALZ5axF9EqjmwZHpXqkzbgAMP8DmAtiyNxrdrQ==", "shasum": "5c38ce8136edf484c0fcfbf7514aafb0363ed1db", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.0.4.tgz", "fileCount": 41, "unpackedSize": 286618, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCDjQkIkioauZ9/We4gg1qOU52fQ2Lw2Ty89sxKZgejqgIgTLilBMUC2IelV1OUGrB/syPviYuLprdH+OZrOkpW8wk="}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_3.0.4_1634004086706_0.6003781528802812"}, "_hasShrinkwrap": false}, "3.0.5": {"name": "istanbul-reports", "version": "3.0.5", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc mocha --recursive", "prepare": "webpack --config lib/html-spa/webpack.config.js --mode production", "prepare:watch": "webpack --config lib/html-spa/webpack.config.js --watch --mode development"}, "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.7.5", "@babel/preset-env": "^7.7.5", "@babel/preset-react": "^7.7.4", "babel-loader": "^8.0.6", "chai": "^4.2.0", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^3.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0-beta.2", "react": "^16.12.0", "react-dom": "^16.12.0", "webpack": "^4.41.2", "webpack-cli": "^3.3.10"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-reports"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "nyc": {"exclude": ["lib/html/assets/**", "lib/html-spa/assets/**", "lib/html-spa/rollup.config.js", "test/**"]}, "engines": {"node": ">=8"}, "_id": "istanbul-reports@3.0.5", "_nodeVersion": "14.18.0", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-5+19PlhnGabNWB7kOFnuxT8H3T/iIyQzIbQMxXsURmmvKg86P2sbkrGOT77VnHw0Qr0gc2XzRaRfMZYYbSQCJQ==", "shasum": "a2580107e71279ea6d661ddede929ffc6d693384", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.0.5.tgz", "fileCount": 41, "unpackedSize": 287075, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDB/lwgvYYE2GASUgiQ1gCv/AHlEjFGK+Pq23ii23mfFAIgOWV5AfLL5nCfYCBOf7WgRzfGxjJtvI5xfMnqm/rt1PA="}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_3.0.5_1634144558391_0.8787603284474099"}, "_hasShrinkwrap": false}, "3.1.0": {"name": "istanbul-reports", "version": "3.1.0", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc mocha --recursive", "prepare": "webpack --config lib/html-spa/webpack.config.js --mode production", "prepare:watch": "webpack --config lib/html-spa/webpack.config.js --watch --mode development"}, "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.7.5", "@babel/preset-env": "^7.7.5", "@babel/preset-react": "^7.7.4", "babel-loader": "^8.0.6", "chai": "^4.2.0", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^3.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0-beta.2", "react": "^16.12.0", "react-dom": "^16.12.0", "webpack": "^4.41.2", "webpack-cli": "^3.3.10"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-reports"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "nyc": {"exclude": ["lib/html/assets/**", "lib/html-spa/assets/**", "lib/html-spa/rollup.config.js", "test/**"]}, "engines": {"node": ">=8"}, "_id": "istanbul-reports@3.1.0", "_nodeVersion": "14.18.1", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-rgeP8yMlXeH4mfd9K/sQXZv1lvcS7xo379zntcotPDdMwkcGYwMxGHGZYo0/+YW5B/nor2YGKz2BH5ume405ow==", "shasum": "57a32bff7ab4b315e401daa08fe2934e0c0b1ec2", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.0.tgz", "fileCount": 41, "unpackedSize": 288787, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhppmBCRA9TVsSAnZWagAAe4AP/2Q4+rCXOSl4cCxLmX+M\nGs6ZRb/TEbn6Mx8BWKPsG21PD0HZbyxnAlLf6zTPz8U+lg3PgIx3jQE8uk3S\nl6Hq+6tMtI4W3y2m3Vdj8neZrHqwSHYXJTFqnLSkRkLqx9eok0GH1rrg/2YB\nw8L4Y8U0NcCYyr5TCLEiKGvyDZRDRCIIdALdKZ7GL0K3etp7xkqPCPX05ZLD\nvMZQXWDIrHhg3IU3UJBznordU9fNgR4BthpGG9xaK5iSiB7oGBOcc56lmVwg\n/j9KBzGh1sl9pVQH75Oe49CFVZKPEBwWdhEz2c7ojXorh+FjluzA4a1ddEp5\nhAdzzcV43VH2SS/6zPUQ6Rj09QEaZu6v1TnPfC+k+Gw96boRpltP/OcEPqte\n4D6yN7G4OkIdxe0A0KLCLdImWAUrOVdp0/OKYK1PE9C89ZUBhBdPCxhK+MZk\nOkyTrtoSEs9Lj7mBRGxmRryuFiNkIAwDYMm43Ti3mqmlIJtQY8/YEcH09QFD\notL/6tSD853oWVr4LBmtYmnilrAdLOTHUZX5vubY8Opv0IVtyrYU3kVEPrzN\nrZilq7Gys5tIrxFG9gmUVvZRaESp3Ys5nSVZBlmEeMCYJYXexrC/xogMEuUi\nlI/FYIEg3foLnncNMAQxDT67qEMeYVPVT6nAYFWDviljsHBhsdzmGxG+ZVmo\nnbNH\r\n=gx/y\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEK1awSgRGfOOgv/g8+MgA3ZN0zX7SPw8XBUfoLiyruyAiBp0hLTbHpJ99RwNYWshjZs0AODQEXIUGenakUHPhBRWw=="}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_3.1.0_1638308225655_0.9263943746203549"}, "_hasShrinkwrap": false}, "3.1.1": {"name": "istanbul-reports", "version": "3.1.1", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc mocha --recursive", "prepare": "webpack --config lib/html-spa/webpack.config.js --mode production", "prepare:watch": "webpack --config lib/html-spa/webpack.config.js --watch --mode development"}, "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.7.5", "@babel/preset-env": "^7.7.5", "@babel/preset-react": "^7.7.4", "babel-loader": "^8.0.6", "chai": "^4.2.0", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^3.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0-beta.2", "react": "^16.12.0", "react-dom": "^16.12.0", "webpack": "^4.41.2", "webpack-cli": "^3.3.10"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-reports"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "nyc": {"exclude": ["lib/html/assets/**", "lib/html-spa/assets/**", "lib/html-spa/rollup.config.js", "test/**"]}, "engines": {"node": ">=8"}, "_id": "istanbul-reports@3.1.1", "_nodeVersion": "14.18.1", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-q1kvhAXWSsXfMjCdNHNPKZZv94OlspKnoGv+R9RGbnqOOQ0VbNfLFgQDVgi7hHenKsndGq3/o0OBdzDXthWcNw==", "shasum": "7085857f17d2441053c6ce5c3b8fdf6882289397", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.1.tgz", "fileCount": 41, "unpackedSize": 289106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhp6eYCRA9TVsSAnZWagAAwMUP/j1H5orobAznc9rD4HrR\nKn1ZEUzsmJVmKeO3yqBwNbcjgtS2SI3KtHoWG74RYPhqDiXvazMFKyg1aU8V\nOb2jlUlN3Bc8xJVo416EB5ybtgKmypdaG24/v+u2oQncMNtC+Is/jB3U+hX2\nBJFLhkAjZhxE1Y4gwvFZsZlFtOCAD4nS6JWyn460VAmsQzud0D9lf3kKYqxa\nKMuJDhLkH+Y0uAR+VzHZLCR1j9kpZs0WjnOURanIRd2P3JA9BzhUqDdV/URT\nEXF0WE/YAE35KwjqD3RGas9RHkv4pIUt2lkX8zezSsES61qSBjQdivShd2C9\n3weJ109Wj6494pg2MPt57KpNXyts8VoZ6mQUEpMEyGvwZfJEG6rxI4G9QKK6\nqZVeEADD5GNcl2e6cL/CRk+N0JQFhWFG3x/jVKQXEXhhVf2ZR9LvLXUseXUK\n31Pn2X5XVmy7xognC81e5FM74+73LHczZ3PfOUUztjfeT2W+lVTYIXevtkfV\nTWIxefPa64MrCr6iNKBLiohNqL5G107bkV5DBrF5kyglcWc6cJy6r78HbHda\nn4pBnI+eXRUGi4sML2uXZAlHAN8jRF0T87MaS6+hmlB4VL6t7cPOETndqgLs\nC4GIpIL6+FuBumMfi2MkrK9xWrVB2/x2TaSQre0fTmViUhdDn/wsGFgp+boK\nF+aF\r\n=qhQR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDgDMbg88tgdFVN55zRHNLWsiBtzjeBhpXwJH7gFlIBmQIhAP8krZU2npGysJb1S83lPg5EPYMLqWEK8K6KcaURGZ22"}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "jg<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_3.1.1_1638377368212_0.07965367111734434"}, "_hasShrinkwrap": false}, "3.1.2": {"name": "istanbul-reports", "version": "3.1.2", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc mocha --recursive", "prepare": "webpack --config lib/html-spa/webpack.config.js --mode production", "prepare:watch": "webpack --config lib/html-spa/webpack.config.js --watch --mode development"}, "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.7.5", "@babel/preset-env": "^7.7.5", "@babel/preset-react": "^7.7.4", "babel-loader": "^8.0.6", "chai": "^4.2.0", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^3.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0-beta.2", "react": "^16.12.0", "react-dom": "^16.12.0", "webpack": "^4.41.2", "webpack-cli": "^3.3.10"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-reports"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "nyc": {"exclude": ["lib/html/assets/**", "lib/html-spa/assets/**", "lib/html-spa/rollup.config.js", "test/**"]}, "engines": {"node": ">=8"}, "_id": "istanbul-reports@3.1.2", "_nodeVersion": "14.18.2", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-0gHxuT1NNC0aEIL1zbJ+MTgPbbHhU77eJPuU35WKA7TgXiSNlCAx4PENoMrH0Or6M2H80TaZcWKhM0IK6V8gRw==", "shasum": "b80e13cbab0120e1c367ebaa099862361aed5ead", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.2.tgz", "fileCount": 41, "unpackedSize": 289376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhxQaqCRA9TVsSAnZWagAAQPAP/AvkGHJNKJXfDQK755sG\nYUQbLZkP1eR7X26FneUSnZUMu9/kqotsqbhoEIYAoptqTb+AGfw0yW4B0Gug\nFymGoFoc7sJ4N7jPWfOc3eEvUCowJ/nya7jyDee+7YQWS/0pbr+ZXIFnVpVa\nGZ+P/f2tYNHPZXKZlZk+WQ4ptqU517IfYetaNKtshBH05csjwRkUjlFNOEUm\ny7YdBMUYvZheOqm8lkhNolfjt4a+s7KYG6etATHsqVY1rJkgPbgHrC60NtLj\n2AT0ZtaXnyYXwPcbUTqaX7u11oZFshfAnweoJVepz1Inh/2mT3nqxKOcNei2\nEaZ+rv0uMNKPb5isx3wZM7ambPmSV0ypTkkcAhx5gORdz82zMppf0WcRYTZh\nciBroceK/9TvRJ1rCSzQkE2fxCSo088yAZLlIf74G+sFKXQziUTD3oSNpIPp\naYZKsHgmlm77brg4LSa0LTEqvwUrGJSHLeN1Xl0eA3LRTmvzxDAhcTyX45p7\nW4Or/6B2NMw+DdHlcGTpd9mAbh2KTB1rnW939jQAVKio9yKBdwr4QCV3tM/+\neEKxRyGyTESiaJfenmX9LZUxOTR79zvIyCXZ/lfLWMXTp5gHiEGkjDVdw7tg\nCpZycueNJ7YXEktJxsCrRc78OYUNy2ve5zCf4IVIvDM4f/ueb2P0U/PFhO81\ndssd\r\n=/bWG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+2Fu7ccThEX0LMsUArzzQHe+XG+LLAVQdjyt2NldrQwIhAO1Quz9jBQMUx53c4Yo6vM63BQx4UZ5GSE3hi7G529vp"}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_3.1.2_1640302249898_0.4544709650376626"}, "_hasShrinkwrap": false}, "3.1.3": {"name": "istanbul-reports", "version": "3.1.3", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc mocha --recursive", "prepare": "webpack --config lib/html-spa/webpack.config.js --mode production", "prepare:watch": "webpack --config lib/html-spa/webpack.config.js --watch --mode development"}, "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.7.5", "@babel/preset-env": "^7.7.5", "@babel/preset-react": "^7.7.4", "babel-loader": "^8.0.6", "chai": "^4.2.0", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^3.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0-beta.2", "react": "^16.12.0", "react-dom": "^16.12.0", "webpack": "^4.41.2", "webpack-cli": "^3.3.10"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-reports"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "nyc": {"exclude": ["lib/html/assets/**", "lib/html-spa/assets/**", "lib/html-spa/rollup.config.js", "test/**"]}, "engines": {"node": ">=8"}, "_id": "istanbul-reports@3.1.3", "_nodeVersion": "14.18.2", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-x9LtDVtfm/t1GFiLl3NffC7hz+I1ragvgX1P/Lg1NlIagifZDKUkuuaAxH/qpwj2IuEfD8G2Bs/UKp+sZ/pKkg==", "shasum": "4bcae3103b94518117930d51283690960b50d3c2", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.3.tgz", "fileCount": 41, "unpackedSize": 289866, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzNkUCRA9TVsSAnZWagAAVZEP/0rxjby43H1Ekk3MDqRZ\n2FsshoIUAxo1aZu/fmOzCx9mtLZl/PeRF5m0LhgXkYPBteTrkMMJ7o2fXh7g\nu7VH7hmB4gjmxDqucZVz/nmRtpTGfeKj/Q7H+u/ihBMNpOP5p/CKrN85cldX\nO6DofW/SGvI0Bke6T72e3rR+KB8dMBSL33agD+K5LcXGmgOgx+n6JveHwjo5\n0JpaH7NFsEUMXcCOA6bWyAbBKg+eoxxxVYpel/eFfH6xsgaR1vbDOIz0kGNy\nNuK0Vhnsv88PKCmBpR+hVBx+8zT4pxR4OZQaTy4SWqUqjqU4B0MmbB/ntEDw\n3QL+m9ZDWcfK8c8ZTIP78FpfyDQMMLw4wwCe7J4/IscQlRb3Lj0NUf8kCzuh\nWPKajfdCS3nmnZ/Osk8qOZUjExincx1wQWxezmvJ2rXmP0cvESrvJrhW9JiV\ntO2iqOc2xcTovPrgHKkAU9dvrpoy7B73xc+OLvKcazN0y9lOkY0mdyVpznoY\n1xnPGe/Fr9NTJ+JBVnMd8SNu3WRsssMsED5DJt1rH0/oTWdJyUY78YA+atop\nsT/bjHg+CnAITMvc8OrImzm3zHKbKU66IxB8rYL7ucV0JzsXz8WJDQzbXRLk\nHJK/4tT5EewlnqUmuml+ZX3xgk27nhcEQ621EGkXnn2BQRNeCOu7xMVeJfNL\nvSKv\r\n=Aiz3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHjXjJ8RmFcfTCfiwtlMR88XmGU2xmj1nERHj7/RfgXCAiBuGhtZ7HVtW1By1VKBR0OEO93TR0AZ9SWQATpJS+LEeA=="}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_3.1.3_1640814868250_0.17119500988122405"}, "_hasShrinkwrap": false}, "3.1.4": {"name": "istanbul-reports", "version": "3.1.4", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc mocha --recursive", "prepare": "webpack --config lib/html-spa/webpack.config.js --mode production", "prepare:watch": "webpack --config lib/html-spa/webpack.config.js --watch --mode development"}, "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.7.5", "@babel/preset-env": "^7.7.5", "@babel/preset-react": "^7.7.4", "babel-loader": "^8.0.6", "chai": "^4.2.0", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^3.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0-beta.2", "react": "^16.12.0", "react-dom": "^16.12.0", "webpack": "^4.41.2", "webpack-cli": "^3.3.10"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-reports"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "nyc": {"exclude": ["lib/html/assets/**", "lib/html-spa/assets/**", "lib/html-spa/rollup.config.js", "test/**"]}, "engines": {"node": ">=8"}, "_id": "istanbul-reports@3.1.4", "_nodeVersion": "14.18.3", "_npmVersion": "6.14.15", "dist": {"integrity": "sha512-r1/DshN4KSE7xWEknZLLLLDn5CJybV3nw01VTkp6D5jzLuELlcbudfj/eSQFvrKsJuTVCGnePO7ho82Nw9zzfw==", "shasum": "1b6f068ecbc6c331040aab5741991273e609e40c", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.4.tgz", "fileCount": 41, "unpackedSize": 291009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/rBaCRA9TVsSAnZWagAAmk0P/1GwwHGzAawNGVBkeQsQ\naTfox9PEtw4QNCgKMfdeuaF6Rh0+JHpIOJMk6KiatYnNwFHB02i64TgW7YIF\nDkCd2E17cMqeseVyrgn2hlaZAIJkn+Fqc9oyTv9RqgiIqW/0X0u5hcspHOHt\n9kfaoa43ABrE65cpX7TvEUe8lkjBt0UYdsx9W0YuLfmXkWOZaKav21YhyoCh\n9wHs02ra23UdCSuvn3nOf4hJ1DimbmuTheyaoIjnGf1us2HcAZlDR94OVc3K\naQm5bJnq10UKPPy5g0ELijKKYDwO/VMK2R8gEStf6t16laBYR1fGFoMNdDWe\nTH4IobYoOm6sdypfG3/M3ZfBLTWjdH9wAlSvRJUKs/fa8l5vQjrwsaMCoQSn\ntofGNp5VikJ4PLaZY4O8EnCDttlJrw1z1ZrdtjaBoK98xkVvZm/pqYulyeoG\nGi2tugliH9qZCnyLwT4N1G42mScwmeGxd/mAnY4qST/e5vd5xL1avomYBuB+\nesl/5S8+uOYxHtK93LMUKlYmcxIuX5Klqz1Gw1rcrkT9lXMyngC1EYDSott8\nPdStItaCMeSw1Q4S6VWJstzGObjlETQe4H+cDbmPN8ANZn/i/mnUQmRlpeVs\nNDcjDaPeTTGNzffi9HwtKIj3x8ktK106Iu/fQmMD8B4UkPxU8uY9JxmmdG1m\n9rgk\r\n=aJA1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC8l7z2gVfYhXQ10nukqRR8mW/RCV4lFUDk/VtO1L2AsQIhAMfLqKU430DBOj7ielidi2HLh/bnh9sKOYTtQmrfOYZs"}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_3.1.4_1644081241964_0.1469938588687576"}, "_hasShrinkwrap": false}, "3.1.5": {"name": "istanbul-reports", "version": "3.1.5", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc mocha --recursive", "prepare": "webpack --config lib/html-spa/webpack.config.js --mode production", "prepare:watch": "webpack --config lib/html-spa/webpack.config.js --watch --mode development"}, "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.7.5", "@babel/preset-env": "^7.7.5", "@babel/preset-react": "^7.7.4", "babel-loader": "^8.0.6", "chai": "^4.2.0", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^3.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0-beta.2", "react": "^16.12.0", "react-dom": "^16.12.0", "webpack": "^4.41.2", "webpack-cli": "^3.3.10"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-reports"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "nyc": {"exclude": ["lib/html/assets/**", "lib/html-spa/assets/**", "lib/html-spa/rollup.config.js", "test/**"]}, "engines": {"node": ">=8"}, "_id": "istanbul-reports@3.1.5", "_nodeVersion": "14.20.0", "_npmVersion": "6.14.17", "dist": {"integrity": "sha512-nUsEMa9pBt/NOHqbcbeJEgqIlY/K7rVWUX6Lql2orY5e9roQOthbR3vtY4zzf2orPELg80fnxxk9zUyPlgwD1w==", "shasum": "cc9a6ab25cb25659810e4785ed9d9fb742578bae", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.5.tgz", "fileCount": 41, "unpackedSize": 292089, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCBlYSDzubLfWoXk6n9Uuc3YVcTw5exR6HFOqQnSpGgvAIhAI3XLFccuM/1B3e47MUKd6z1BA9ibDA0XqiRdaIu5mw1"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJizxf8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmriBA/+NtsQyiljUIUf3mJDTXAobhVWHqz8Vt+G9idGhYUPmqc+XCiQ\r\n2RVBmz46vz3ykzLTj3c9QkrsGigozZJHJ/YoJipb2Yg+ehj40uqEwX063cme\r\naSfYkUkzj12/tPdtBbZz785R077cCZ++9BER72GKYBLvyOWwQjFvxO5MSKeU\r\nHfRv6PEyCkeDjU9uxWz8qYUj17zVEGsEBK4qMwdwNWGNwxbM/5dZiWYnPnvn\r\nD16FrekHZg7eEG+AQhBbf/twwCVlpIZJpfJaHhPlguc51ehZnee0YroJFJ5y\r\n8MRfvJ4q2l7zki6/gCrNLFVgKC7qQzExcBhyw7RusyPQUDypbpSxuvhU+nrc\r\n0I6XBG8Ha8BFwdmIKGwkINBYQeu9pJqYgm34hMKwTAk8NYFze0NjwkT683tK\r\ncfB/nfeV9B1vVyXAOIvyPhnMkMg5xhm41z8OIZAlHmGLmCQ5s8ujFm0u4jtK\r\ni59QZCOaxq7YOukdDwPNpT4jdIpYhrFBvd4annenNwDDlfEbd0CPHN1vkdb1\r\nO8y8EGb6z171fExxrVW2RCnpBXTQg5zZJmkggy0iXVaLPrdy2Hy/X6ma2ccr\r\njpATh3699yvQ9Zvt6vpA0DPYBGPJtmynNwSIPJ+uqbPx0C1fH1tsBVqFPv5Q\r\nubL7Qb6PQpj5iusLoLRMLWTZfo6sgmKguf0=\r\n=BAI8\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_3.1.5_1657739260524_0.5019764585733939"}, "_hasShrinkwrap": false}, "3.1.6": {"name": "istanbul-reports", "version": "3.1.6", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc mocha --recursive", "prepare": "webpack --config lib/html-spa/webpack.config.js --mode production", "prepare:watch": "webpack --config lib/html-spa/webpack.config.js --watch --mode development"}, "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.7.5", "@babel/preset-env": "^7.7.5", "@babel/preset-react": "^7.7.4", "babel-loader": "^8.0.6", "chai": "^4.2.0", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^3.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0-beta.2", "react": "^16.12.0", "react-dom": "^16.12.0", "webpack": "^4.41.2", "webpack-cli": "^3.3.10"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-reports"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "nyc": {"exclude": ["lib/html/assets/**", "lib/html-spa/assets/**", "lib/html-spa/rollup.config.js", "test/**"]}, "engines": {"node": ">=8"}, "_id": "istanbul-reports@3.1.6", "_nodeVersion": "14.21.3", "_npmVersion": "6.14.18", "dist": {"integrity": "sha512-TLgnMkKg3iTDsQ9PbPTdpfAK2DzjF9mqUG7RMgcQl8oFjad8ob4laGxv5XV5U9MAfx8D6tSJiUyuAwzLicaxlg==", "shasum": "2544bcab4768154281a2f0870471902704ccaa1a", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.6.tgz", "fileCount": 41, "unpackedSize": 293614, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC6LJGb6P07t69GpVmEosEVGv5/45GmmwDcppVln8SFdgIgWDh0myUurUeuVg8h9yMuzNzSAOkEGqUs6Nl+FJSR1+s="}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_3.1.6_1690295506323_0.32927873165760113"}, "_hasShrinkwrap": false}, "3.1.7": {"name": "istanbul-reports", "version": "3.1.7", "description": "istanbul reports", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "main": "index.js", "scripts": {"test": "nyc mocha --recursive", "prepare": "webpack --config lib/html-spa/webpack.config.js --mode production", "prepare:watch": "webpack --config lib/html-spa/webpack.config.js --watch --mode development"}, "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.7.5", "@babel/preset-env": "^7.7.5", "@babel/preset-react": "^7.7.4", "babel-loader": "^8.0.6", "chai": "^4.2.0", "is-windows": "^1.0.2", "istanbul-lib-coverage": "^3.0.0", "mocha": "^6.2.2", "nyc": "^15.0.0-beta.2", "react": "^16.12.0", "react-dom": "^16.12.0", "webpack": "^4.41.2", "webpack-cli": "^3.3.10"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-reports"}, "keywords": ["istanbul", "reports"], "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "nyc": {"exclude": ["lib/html/assets/**", "lib/html-spa/assets/**", "lib/html-spa/rollup.config.js", "test/**"]}, "engines": {"node": ">=8"}, "_id": "istanbul-reports@3.1.7", "_nodeVersion": "14.21.3", "_npmVersion": "6.14.18", "dist": {"integrity": "sha512-BewmUXImeuRk2YY0PVbxgKAysvhRPUQE0h5QRM++nVWyubKGV0l8qQ5op8+B2DOmwSe63Jivj0BjkPQVf8fP5g==", "shasum": "daed12b9e1dca518e15c056e1e537e741280fa0b", "tarball": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.7.tgz", "fileCount": 41, "unpackedSize": 293826, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCHNqYWyEOxKMvN+O5K59OO68E7mUtplTxDLYt5uPVgxgIhANKzfGMAvltho3nFj/AbpI08x1KxHIixSTCRJllq2Rxc"}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/istanbul-reports_3.1.7_1708375848327_0.8085344430270094"}, "_hasShrinkwrap": false}}, "readme": "# istanbul-reports\n\n[![Greenkeeper badge](https://badges.greenkeeper.io/istanbuljs/istanbul-reports.svg)](https://greenkeeper.io/)\n[![Build Status](https://travis-ci.org/istanbuljs/istanbul-reports.svg?branch=main)](https://travis-ci.org/istanbuljs/istanbul-reports)\n\n-   node.getRelativeName\n\n-   context.getSource(filePath)\n-   context.classForPercent(type, percent)\n-   context.console.colorize(str, class)\n-   context.writer\n-   context.console.write\n-   context.console.println\n", "maintainers": [{"name": "gotwarlost", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}, {"name": "jak<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "**************"}, {"name": "oss-bot", "email": "<EMAIL>"}], "time": {"modified": "2024-02-19T20:50:48.993Z", "created": "2015-11-22T07:10:57.820Z", "1.0.0-alpha.0": "2015-11-22T07:10:57.820Z", "1.0.0-alpha.1": "2015-11-24T18:38:50.184Z", "1.0.0-alpha.2": "2015-11-27T00:13:15.721Z", "1.0.0-alpha.3": "2015-11-27T00:24:40.949Z", "1.0.0-alpha.4": "2015-11-28T08:38:39.757Z", "1.0.0-alpha.6": "2016-06-24T01:46:17.360Z", "1.0.0-alpha.7": "2016-07-04T13:33:17.979Z", "1.0.0-alpha.8": "2016-07-11T11:49:22.620Z", "1.0.0": "2016-10-17T06:14:40.055Z", "1.0.1": "2017-01-29T06:52:59.867Z", "1.0.2": "2017-03-27T05:51:23.641Z", "1.1.0": "2017-04-29T05:00:05.322Z", "1.1.1": "2017-05-27T21:12:58.280Z", "1.1.2": "2017-08-26T00:13:59.991Z", "1.1.3": "2017-10-21T18:59:30.754Z", "1.1.4": "2018-02-13T05:48:39.129Z", "1.2.0": "2018-03-04T18:42:56.353Z", "1.3.0": "2018-03-09T22:14:16.858Z", "1.4.0": "2018-04-17T18:04:05.993Z", "1.4.1": "2018-05-31T00:37:46.454Z", "1.5.0": "2018-06-06T00:50:07.855Z", "2.0.0": "2018-07-07T19:00:09.640Z", "1.5.1": "2018-09-05T22:28:25.347Z", "2.0.1": "2018-09-06T00:53:18.393Z", "2.0.2": "2018-12-19T04:11:21.328Z", "2.0.3": "2018-12-25T00:37:48.289Z", "2.1.0": "2019-01-26T02:26:53.898Z", "2.1.1": "2019-02-14T13:15:53.272Z", "2.2.0": "2019-03-12T01:14:18.804Z", "2.2.1": "2019-04-03T18:04:49.947Z", "2.2.2": "2019-04-09T23:21:16.721Z", "2.2.3": "2019-04-17T08:04:19.057Z", "2.2.4": "2019-04-24T20:44:43.083Z", "2.2.5": "2019-05-02T12:46:50.518Z", "2.2.6": "2019-05-15T07:59:49.927Z", "3.0.0-alpha.0": "2019-06-19T12:17:05.373Z", "3.0.0-alpha.1": "2019-06-20T14:18:43.021Z", "3.0.0-alpha.2": "2019-10-06T01:06:05.888Z", "3.0.0-alpha.3": "2019-10-19T00:47:07.381Z", "3.0.0-alpha.4": "2019-11-18T14:43:44.266Z", "3.0.0-alpha.5": "2019-11-22T18:20:54.567Z", "3.0.0-alpha.6": "2019-12-07T16:42:01.115Z", "3.0.0": "2019-12-20T20:54:42.456Z", "2.2.7": "2020-01-11T20:15:08.629Z", "3.0.1": "2020-03-26T17:34:47.943Z", "3.0.2": "2020-04-01T13:05:19.374Z", "3.0.3": "2021-10-06T15:15:14.098Z", "3.0.4": "2021-10-12T02:01:26.861Z", "3.0.5": "2021-10-13T17:02:38.523Z", "3.1.0": "2021-11-30T21:37:05.876Z", "3.1.1": "2021-12-01T16:49:28.389Z", "3.1.2": "2021-12-23T23:30:50.063Z", "3.1.3": "2021-12-29T21:54:28.610Z", "3.1.4": "2022-02-05T17:14:02.162Z", "3.1.5": "2022-07-13T19:07:40.804Z", "3.1.6": "2023-07-25T14:31:46.477Z", "3.1.7": "2024-02-19T20:50:48.544Z"}, "homepage": "https://istanbul.js.org/", "keywords": ["istanbul", "reports"], "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-reports"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "license": "BSD-3-<PERSON><PERSON>", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}