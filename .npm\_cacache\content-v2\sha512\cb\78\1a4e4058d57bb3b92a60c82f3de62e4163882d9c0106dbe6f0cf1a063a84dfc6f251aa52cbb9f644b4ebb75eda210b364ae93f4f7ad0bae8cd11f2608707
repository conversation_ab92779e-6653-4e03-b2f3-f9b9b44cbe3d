{"_id": "@types/range-parser", "_rev": "571-06e9799c3a9905b8e6eceafa9dc3f178", "name": "@types/range-parser", "dist-tags": {"ts2.0": "1.2.3", "ts2.1": "1.2.3", "ts2.2": "1.2.3", "ts2.3": "1.2.3", "ts2.4": "1.2.3", "ts2.5": "1.2.3", "ts2.6": "1.2.3", "ts2.7": "1.2.3", "ts2.8": "1.2.3", "ts2.9": "1.2.3", "ts3.0": "1.2.3", "ts3.1": "1.2.3", "ts3.2": "1.2.3", "ts3.3": "1.2.3", "ts3.4": "1.2.3", "ts3.5": "1.2.3", "ts3.6": "1.2.4", "ts3.7": "1.2.4", "ts3.8": "1.2.4", "ts3.9": "1.2.4", "ts4.0": "1.2.4", "ts4.1": "1.2.4", "ts4.2": "1.2.4", "ts4.3": "1.2.4", "ts4.4": "1.2.4", "ts5.8": "1.2.7", "ts5.7": "1.2.7", "latest": "1.2.7", "ts4.5": "1.2.7", "ts4.6": "1.2.7", "ts4.7": "1.2.7", "ts4.8": "1.2.7", "ts4.9": "1.2.7", "ts5.0": "1.2.7", "ts5.1": "1.2.7", "ts5.2": "1.2.7", "ts5.3": "1.2.7", "ts5.4": "1.2.7", "ts5.5": "1.2.7", "ts5.6": "1.2.7", "ts5.9": "1.2.7"}, "versions": {"1.2.0": {"name": "@types/range-parser", "version": "1.2.0", "author": "<PERSON><PERSON> <https://github.com/tlaziuk>", "license": "MIT", "_id": "@types/range-parser@1.2.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "9de85d972f138909c0f71205157bb707908df088", "tarball": "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.0.tgz", "integrity": "sha512-J/Z4hGqSJKyhO3CsZdMjMU+jsfOAU66KQ5pbVznE//PjoPaE55NZa+xdTjmll2a9EYV3KGNebzobGrNcbPDHGQ==", "signatures": [{"sig": "MEUCIQCQqd3Pg1NznIsowYSt3V9jLWP8bKXo8iatxhY9J+CDYQIgDAn7bRxN0Vq7gie4sBQpf2U659l20wmd2/Hz+CD+gJo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for range-parser", "directories": {}, "dependencies": {}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/range-parser-1.2.0.tgz_1486765496963_0.6287025790661573", "host": "packages-18-east.internal.npmjs.com"}, "typesPublisherContentHash": "ef6c0b6cc4719620c4c92a0ce00d45a9b4f0b5e1d6d91245fc80e32d9886f21d"}, "1.2.1": {"name": "@types/range-parser", "version": "1.2.1", "license": "MIT", "_id": "@types/range-parser@1.2.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>"}], "dist": {"shasum": "d5ea5a43288953f7ae0ff4fdca7beee55e1efb1e", "tarball": "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha512-dfJciycCu16iQ80olz+4qqIHmQJ4Qw/EzpVgrZCIvCIxF5hMyrwsgCzd7Pd8EI9A6TIKOyQNAgohK4F6WcpnQg==", "signatures": [{"sig": "MEUCIQDScCsIJVMBzPXFM+jKkh+oKR7kOjB8dlOXG0Py7ih/8QIgLltu2KPyVgPnhKLYjeIxvvlx1WYSb6YLHrq7nUY2JsQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for range-parser", "directories": {}, "dependencies": {}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/range-parser-1.2.1.tgz_1490983644025_0.13351211254484951", "host": "packages-18-east.internal.npmjs.com"}, "typesPublisherContentHash": "03972cff5d6102c2b5e14224a112cb83a99fd55ccbf831c6ba9953809199f9a8"}, "1.2.2": {"name": "@types/range-parser", "version": "1.2.2", "license": "MIT", "_id": "@types/range-parser@1.2.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "fa8e1ad1d474688a757140c91de6dace6f4abc8d", "tarball": "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.2.tgz", "fileCount": 4, "integrity": "sha512-HtKGu+qG1NPvYe1z7ezLsyIaXYyi8SoAVqWDZgDQ8dLrsZvSzUNCwZyfX33uhWxL/SU0ZDQZ3nwZ0nimt507Kw==", "signatures": [{"sig": "MEUCIQCCHDDGw4RY+LTo8l7tng81wzWW884wyB53fWRLMP6fIgIgc+KcemPt10XLef8MPb7L65eluMm7S4NbSp0e4LaPtlk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbFdPGCRA9TVsSAnZWagAAN7MP/2C5Mvyivo1krSPRj6ne\nG3KdxrJl4PBHp983RLXBrwU31DC6/hoz70Jg/RGKsOLIIgkqOXjgzEUIGlol\nT2CUfVvZarwNyQoO4zpuwtio6UfxIs1VKRuvx4LSlkjF9JQg3tpOsDRkp1Tg\nC14syum7LK8yA7m5TY7hxBicrJLAteB2pQcceRtq4c3qHEDwrkrFZYOj4wJe\n6PDp2T/0al9uSHA+2wr/EB6S1pl/XaTsdA1EXun1o5lOykWsank8Bb3JH9QM\n0qj+vWjAR4KIhSGFrwi25YwS5cudLjjuSfJTXnjCkA4tR4sxyhOZDrjwcrqO\nJBF+WSjT0r85w54/SQcKBE27mYwfEes+lCft8smcnFZcz/LubPGWBJHtwJNG\n3GsN9jRlR6sRpp1ajd0riB45HtSo5UoToa4Q4UcU7YiGE1+qXBf6Xl5FR8R/\n8VY4uRzDAIybyQQgAsGOTX9Gh/4z+GrlkX7nQiETASkxTNI4XNBiyH9r2KBq\ncSF0gQ+D1B0FlGzLM/SW97o2YJeWDiWQw/XFbeTRXrUMCxr1c8zPe05tSCRm\nxF8bw9DD1ukFoFHD641PBo/Jw4OMn6KplwydCYHFwhAe1Xwt9R5r1Lis4Qmy\nNhF30jocXFkxZ1q4zXJX4t2zUuOEMiwEl6h1tQjNAULYf+GVCsGSkENLmjEg\npLqp\r\n=NDdV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for range-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/range-parser_1.2.2_1528157125632_0.19075074453775498", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "9aa07423616e63016ffbf0e487540e74ec37bb06827861d502a5a3653e8831e9"}, "1.2.3": {"name": "@types/range-parser", "version": "1.2.3", "license": "MIT", "_id": "@types/range-parser@1.2.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "7ee330ba7caafb98090bece86a5ee44115904c2c", "tarball": "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.3.tgz", "fileCount": 4, "integrity": "sha512-ewFXqrQHlFsgc09MK5jP5iR7vumV/BYayNC6PgJO2LPe8vrnNFyjQjSppfEngITi0qvfKtzFvgKymGheFM9UOA==", "signatures": [{"sig": "MEQCIBmPcwUfwRCLcVmtpewhPbRy1xz473jReKmocP6RtrV+AiANZzcLS1ZeIDwDVpMdJX62O0kdiC0uvbCvC3GQMVuDVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3565, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcCshsCRA9TVsSAnZWagAAkSEP/1I0D+vbiMY/a0nsYzIi\n0pHpgwO432l9IN1teR2KGiagFhvg18F5njUQyBeyxCvnKxqMRHV5ry5GJkD4\n7lcf6l8s1If1ykbbZEFO//ghxsfP2fNOGtXPImMExusSl9fRT3dCYBxb69Hx\nPK90r/6S2f50Bgc/bg41LA39yX42BpVa77Oi3ciS9Ru6qtEgTmmbel7nQelp\n7rcx8ci6hAlfTGhdmYtmRyHBX7M7qMUUZNh3J/+ifAUmds9d5D8F8/9t5ltD\nJP/0/45PqFFyE/kk3vFVbKwZXDd2T8Zhe4xGzSTMb75XmRgXK1YvYT1hPOyN\n2Ix94qXS4vGvc2mKqg7twek6MsRd4n2bULkihc5iNV1lgwyl9SgoGc/ON6Uh\nKqThFCncotggZt1NXmZTnCXlXq1Ox9Q3v8Fi5mDqj6GrXSAwSZNilg/hFP0K\nYm6WoAR/rLj6GK3Q4QXRLAdRMjP4jt9oXoOJMqbgq3TbedDeaeFKFtNWQ4Rv\n02UMn3tSOkbwQt/oezuldBvE6gPstsJVsI0QyEeI/4Don+gokZTrsspALRQx\nfsAsx+StTZmWcXREhkQWtKalQfSmrVSDzhABuFI+P6tkbOqjQmELHplIy+dj\ngmjFKEbXxkRp8Q9Nl7ZTXBDDmDFz63OV/ti5l1iemoNyqD6z+rqX7cLZXriy\nIe0H\r\n=Eoh9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for range-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/range-parser_1.2.3_1544210540146_0.6996875518641452", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "71bf3049d2d484657017f768fe0c54c4e9f03ee340b5a62a56523455925a00ae"}, "1.2.4": {"name": "@types/range-parser", "version": "1.2.4", "license": "MIT", "_id": "@types/range-parser@1.2.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/range-parser", "dist": {"shasum": "cd667bcfdd025213aafb7ca5915a932590acdcdc", "tarball": "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.4.tgz", "fileCount": 4, "integrity": "sha512-EEhsLsD6UsDM1yFhAvy0Cjr6VwmpMWqFBCb9w07wVugF7w9nfajxLuVmngTIpgS6svCnm6Vaw+MZhoDCKnOfsw==", "signatures": [{"sig": "MEUCIQCbCduRqGWeAW3jzLte2UZMZctcnrDa9od9ETBiP8W07AIgGfhx3xMdeV16330TqEX2S0GOIt5K45n9EVTGXO5/IeI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5eL9CRA9TVsSAnZWagAAAtoQAIKbw5pNMyxppNJge85X\n9pbUTQyQnpmmWKEUQW5l1xAl4mDPqdGkRO2Ujmn8PxwhxtUxdCyLijSwBkel\nFdVXzZuYJvWYb0/HDGA/689Q+seo0jprotpv0AMU+zZmXILF7XZSLyB3Iq9s\n5x9jxUE2lgOzhK5Sx/R0GD8zQpa/xLyxDwKjTrvmfkVH66YsyiWQ8wfyA9D4\nfib3b6qLAjHoNb6KAc6ObJyA6MCn6tTaV+U8Y9RXvyLRDYIONpN7TqRaKCJf\ncCxVDqpGQXIUcoIVd9z9g3h0feoVNWsCBg2UmvYTHVrF4i1lLCYT7MNBOl7p\nN9l1O/Te9kTihWSudiily17pc1IMGhd++4l+Xz0oHWsP0Z6zFJBbrWIYGMaj\nQnohEZ+18pQA8WqAe40/IpA+1d+rXPTgU71RteoV9Qg6yxWPcieV39VgCO47\nVkS1BSyEk6Kbey+dZzIZEyUNU5zBM52SeVUfjvvDJY6VYhBfUnj6ZNCsRq4A\neavxBk9sU9w0JQxOIaQxS/p3lkBSW11/EbkIgfotszdn0nTVUFC6dt46RwA5\n6UqUaVNGFcsv93kgdaXCVwu3zXuXvxA3XFUXb82Wky2L5k2JL3t8+9NKPKNY\nLVLQYW/D4Ih9Kv6Uoc7ePQV86P3Cr4Fkm3eayuc2hZ8rGoQWLR/1lgz0gezs\nXsZH\r\n=C+sh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/range-parser"}, "description": "TypeScript definitions for range-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/range-parser_1.2.4_1625678586204_0.4642996673726154", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "60a027a88ae9d7c5ae30935c98266f5033af3c38944121c975bf5e136b9053f3"}, "1.2.5": {"name": "@types/range-parser", "version": "1.2.5", "license": "MIT", "_id": "@types/range-parser@1.2.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/range-parser", "dist": {"shasum": "38bd1733ae299620771bd414837ade2e57757498", "tarball": "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.5.tgz", "fileCount": 5, "integrity": "sha512-xrO9OoVPqFuYyR/loIHjnbvvyRZREYKLjxV4+dY6v3FQR3stQ9ZxIGkaclF7YhI9hfjpuTbu14hZEy94qKLtOA==", "signatures": [{"sig": "MEYCIQDPS0wxTSfbIlq//F2+MAh2sm5xQBhuQX2hWmOiINGb1gIhAKXTsCNKOgOGjnsBjSpiCt6KgfwLkoYwoXwbp4NsnkiL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5083}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/range-parser"}, "description": "TypeScript definitions for range-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/range-parser_1.2.5_1695798957393_0.9340438922580006", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8cae3d90a5b621ab27c73380a07b6fd2a6e37a7b5a67925e388e34a116580b9b"}, "1.2.6": {"name": "@types/range-parser", "version": "1.2.6", "license": "MIT", "_id": "@types/range-parser@1.2.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/range-parser", "dist": {"shasum": "7cb33992049fd7340d5b10c0098e104184dfcd2a", "tarball": "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.6.tgz", "fileCount": 5, "integrity": "sha512-+0autS93xyXizIYiyL02FCY8N+KkKPhILhcUSA276HxzreZ16kl+cmwvV2qAM/PuCCwPXzOXOWhiPcw20uSFcA==", "signatures": [{"sig": "MEYCIQDaBonKE/dSonVAfg+ES7mZDbPuObr5z55vSBO/1L2xtQIhAOTujlGXm1yGqTYyMvnoCvtlP/htnN0kW5Z4iCn7BKBc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4615}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/range-parser"}, "description": "TypeScript definitions for range-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/range-parser_1.2.6_1697629944843_0.9758060640433115", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f26a7f678b4f7cef674af5601ff55e725ce22887f1e42b6c10164abeda1571e3"}, "1.2.7": {"name": "@types/range-parser", "version": "1.2.7", "license": "MIT", "_id": "@types/range-parser@1.2.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/range-parser", "dist": {"shasum": "50ae4353eaaddc04044279812f52c8c65857dbcb", "tarball": "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.7.tgz", "fileCount": 5, "integrity": "sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ==", "signatures": [{"sig": "MEUCIQCrzJ1CA6wSU4xaHYN7gssSJAeB6YWz0mLoGl14lI3rRAIgGbwMrnhMzsG0VGP02/ygVMCR0BQnSnYsU3Dw5o9zNto=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4615}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/range-parser"}, "description": "TypeScript definitions for range-parser", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/range-parser_1.2.7_1699364722194_0.6193198982191708", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "85ed88e3afe8da85360c400901b67e99a7c6690c6376c5ab8939ae9dee4b0a93"}}, "time": {"created": "2017-02-10T22:24:58.914Z", "modified": "2025-02-23T07:35:44.975Z", "1.2.0": "2017-02-10T22:24:58.914Z", "1.2.1": "2017-03-31T18:07:25.837Z", "1.2.2": "2018-06-05T00:05:25.693Z", "1.2.3": "2018-12-07T19:22:20.247Z", "1.2.4": "2021-07-07T17:23:06.370Z", "1.2.5": "2023-09-27T07:15:57.606Z", "1.2.6": "2023-10-18T11:52:25.074Z", "1.2.7": "2023-11-07T13:45:22.371Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/range-parser", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/range-parser"}, "description": "TypeScript definitions for range-parser", "contributors": [{"url": "https://github.com/tlaziuk", "name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}