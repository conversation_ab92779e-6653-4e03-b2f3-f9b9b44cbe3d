{"_id": "string_decoder", "_rev": "47-b5ec1ee49b46afb7b7b953c47c08ca60", "name": "string_decoder", "description": "The string_decoder module from Node core", "dist-tags": {"latest": "1.3.0"}, "versions": {"0.0.0": {"name": "string_decoder", "version": "0.0.0", "description": "the string_decoder module from node core for browsers", "main": "index.js", "dependencies": {}, "devDependencies": {"tape": "~1.0.4"}, "scripts": {"test": "tape test/*.js"}, "repository": {"type": "git", "url": "git://github.com/substack/string_decoder.git"}, "homepage": "https://github.com/substack/string_decoder", "keywords": ["string", "decoder", "browser", "browserify"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "bugs": {"url": "https://github.com/substack/string_decoder/issues"}, "_id": "string_decoder@0.0.0", "dist": {"shasum": "e185bd651285845d5599d23cf7bff58dd4ad7c18", "tarball": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.0.0.tgz", "integrity": "sha512-g1eXK+zCSim+L0NgwIIdxwc0DX0krDMQFpvdOu1hz9XWx2vnHatlSLM+a4ND7aMFANxvDldcrvc4CCqoaXnUiw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCth+kFtE1YJjCFsAu3/+Rq7kkXuSjQIyukwBhNtG78tgIgH6CXT9UYMPIMLAXOKhXK5N13m2WWD2udA0CGv/lqp+M="}]}, "_from": ".", "_npmVersion": "1.3.14", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.0.1": {"name": "string_decoder", "version": "0.0.1", "description": "the string_decoder module from node core for browsers", "main": "index.js", "dependencies": {}, "devDependencies": {"tap": "~0.4.8"}, "scripts": {"test": "tap test/simple/*.js"}, "repository": {"type": "git", "url": "git://github.com/substack/string_decoder.git"}, "homepage": "https://github.com/substack/string_decoder", "keywords": ["string", "decoder", "browser", "browserify"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "bugs": {"url": "https://github.com/substack/string_decoder/issues"}, "_id": "string_decoder@0.0.1", "dist": {"shasum": "f5472d0a8d1650ec823752d24e6fd627b39bf141", "tarball": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.0.1.tgz", "integrity": "sha512-nWi0z/o2vMFV7SJoJDEGqCUPfcpdC/hzCNnbHWhzt6SenBdJ3vVK0aeZuqnVVQ8fPci2h2WXIL6N3O+OJHJhZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCo45+hCYHRbrbZQP8rWtQ/3mrNtik/BP62bumPgtV1fwIgHIwUscV3Oj9j3pkOWcFr6Y3ajuKV0nXGwTJu+arNebE="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "substack", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}], "directories": {}}, "0.10.24": {"name": "string_decoder", "version": "0.10.24", "description": "The string_decoder module from Node core", "main": "index.js", "dependencies": {}, "devDependencies": {"tap": "~0.4.8"}, "scripts": {"test": "tap test/simple/*.js"}, "repository": {"type": "git", "url": "git://github.com/rvagg/string_decoder.git"}, "homepage": "https://github.com/rvagg/string_decoder", "keywords": ["string", "decoder", "browser", "browserify"], "license": "MIT", "bugs": {"url": "https://github.com/rvagg/string_decoder/issues"}, "_id": "string_decoder@0.10.24", "dist": {"shasum": "6902abf7be76f35e9c92292583328ba4c07e86c3", "tarball": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.24.tgz", "integrity": "sha512-5RDdCfHAaEp8yzOkpOpig0jd1qZKv60zWF1h3SIQWUUEJqOKRItvCd2Va8GmNGSsGavULz/rYv5QYBabVUiJdw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA5q0rzYfWPMbBPiw5TeGmmgAgjeZnotwQQOFLqf+RfMAiBJJ44WfElp7itm3d/kIOYD5MsUiYy/qRgvyyuAmw+RVA=="}]}, "_from": ".", "_npmVersion": "1.3.13", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "directories": {}}, "0.11.10": {"name": "string_decoder", "version": "0.11.10", "description": "The string_decoder module from Node core", "main": "index.js", "dependencies": {}, "devDependencies": {"tap": "~0.4.8"}, "scripts": {"test": "tap test/simple/*.js"}, "repository": {"type": "git", "url": "git://github.com/rvagg/string_decoder.git"}, "homepage": "https://github.com/rvagg/string_decoder", "keywords": ["string", "decoder", "browser", "browserify"], "license": "MIT", "bugs": {"url": "https://github.com/rvagg/string_decoder/issues"}, "_id": "string_decoder@0.11.10", "dist": {"shasum": "f0a31205d59b2f1f2e90fdd5032d670a5f9ecf4a", "tarball": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.11.10.tgz", "integrity": "sha512-zUJCCW4PWae2PJ4G80jGK1ITuaZsp/6pUmo4n47Jko8Mq/A0yXLyyb+xntKKTPxYzWa2eZMVwMOLRMu1myE/WQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCRHjwSzRG8YZ/IcSIyBeXb4IsOX7VfK8vrj6kSyjjIXgIgNztZrQ1n6jDQc6sXpHmpr9+4opLplzPl+4YBnPE33Zg="}]}, "_from": ".", "_npmVersion": "1.3.13", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "directories": {}}, "0.10.25": {"name": "string_decoder", "version": "0.10.25", "description": "The string_decoder module from Node core", "main": "index.js", "dependencies": {}, "devDependencies": {"tap": "~0.4.8"}, "scripts": {"test": "tap test/simple/*.js"}, "repository": {"type": "git", "url": "git://github.com/rvagg/string_decoder.git"}, "homepage": "https://github.com/rvagg/string_decoder", "keywords": ["string", "decoder", "browser", "browserify"], "license": "MIT", "bugs": {"url": "https://github.com/rvagg/string_decoder/issues"}, "_id": "string_decoder@0.10.25", "dist": {"shasum": "668c9da4f8efbdc937a4a6b6bf1cfbec4e9a82e2", "tarball": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.25.tgz", "integrity": "sha512-WGUB1Wdc6WmBJheh71tscOt4eVpGBiGLcQcHeGV2gOvFnu5U4OL1yRLB8OBIzCupcugk6UZntguPwNLJ057weQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCr0wa0atp/QaaZgtq3p+K2nLOjxq965DyiUKFo7zdtXwIhAN4VcIH7CMCvx3N0tWLVJdJp2a7BZAy/oz3iFdS6rkYF"}]}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "directories": {}}, "0.11.10-1": {"name": "string_decoder", "version": "0.11.10-1", "description": "The string_decoder module from Node core", "main": "index.js", "dependencies": {}, "devDependencies": {"tap": "~0.4.8"}, "scripts": {"test": "tap test/simple/*.js"}, "repository": {"type": "git", "url": "git://github.com/rvagg/string_decoder.git"}, "homepage": "https://github.com/rvagg/string_decoder", "keywords": ["string", "decoder", "browser", "browserify"], "license": "MIT", "bugs": {"url": "https://github.com/rvagg/string_decoder/issues"}, "_id": "string_decoder@0.11.10-1", "dist": {"shasum": "eb35282cf4612b6e5662fb411b7deeabaa757468", "tarball": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.11.10-1.tgz", "integrity": "sha512-j1ScQF0tt8r9XVLAFpW5rN2RlUynZ5gwm0XNoa3M2cFs07beHTngQ7JQY5QLDkmAmatBv0NvpV6UESNMesF8RA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHbXp8b5CFwigWSC8UkCu8Dj6On3ueouO95IkOt7vIVzAiEA9eSgvpT+pRI5uWhMBPPj/87cFAxLM7ztK5J/graR3oM="}]}, "_from": ".", "_npmVersion": "1.2.30", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "directories": {}}, "0.10.25-1": {"name": "string_decoder", "version": "0.10.25-1", "description": "The string_decoder module from Node core", "main": "index.js", "dependencies": {}, "devDependencies": {"tap": "~0.4.8"}, "scripts": {"test": "tap test/simple/*.js"}, "repository": {"type": "git", "url": "git://github.com/rvagg/string_decoder.git"}, "homepage": "https://github.com/rvagg/string_decoder", "keywords": ["string", "decoder", "browser", "browserify"], "license": "MIT", "bugs": {"url": "https://github.com/rvagg/string_decoder/issues"}, "_id": "string_decoder@0.10.25-1", "dist": {"shasum": "f387babd95d23a2bb73b1fbf2cb3efab6f78baab", "tarball": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.25-1.tgz", "integrity": "sha512-5t9kURmFNpB9j/k9GGyBFo2FFNSOHYWX+6kaYDLxQmwzLA1kPkslQiKA2w2oX8B58qnpxT4kATPn8l/JAkWT9g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHv4wLwuMiYkOjxJBN3MjIzrLLgXa0BhiiOYTuOAJIU9AiB15lZ4mOJzT5bsdBIsszVNHsbVeW7YwBsDkE1nG4BudQ=="}]}, "_from": ".", "_npmVersion": "1.3.24", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "directories": {}}, "0.10.31": {"name": "string_decoder", "version": "0.10.31", "description": "The string_decoder module from Node core", "main": "index.js", "dependencies": {}, "devDependencies": {"tap": "~0.4.8"}, "scripts": {"test": "tap test/simple/*.js"}, "repository": {"type": "git", "url": "git://github.com/rvagg/string_decoder.git"}, "homepage": "https://github.com/rvagg/string_decoder", "keywords": ["string", "decoder", "browser", "browserify"], "license": "MIT", "gitHead": "d46d4fd87cf1d06e031c23f1ba170ca7d4ade9a0", "bugs": {"url": "https://github.com/rvagg/string_decoder/issues"}, "_id": "string_decoder@0.10.31", "_shasum": "62e203bc41766c6c28c9fc84301dab1c5310fa94", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "dist": {"shasum": "62e203bc41766c6c28c9fc84301dab1c5310fa94", "tarball": "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha512-ev2QzSzWPYmy9GuqfIVildA4OdcGLeFZQrq5ys6RtiuF+RQQiZWr8TZNyAcuVXyQRYfEO+MsoB/1BuQVhOJuoQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDj+McnACuNB6r/va0SmCL/b+J/eKMPa8UMU+THG5mv5gIgASHDTmaO2MhTzQp6tCr/Qq3/pPcZ50AscY5IGJMt4rQ="}]}, "directories": {}}, "1.0.0": {"name": "string_decoder", "version": "1.0.0", "description": "The string_decoder module from Node core", "main": "lib/string_decoder.js", "dependencies": {"buffer-shims": "~1.0.0"}, "devDependencies": {"babel-polyfill": "^6.23.0", "tap": "~0.4.8"}, "scripts": {"test": "tap test/parallel/*.js"}, "repository": {"type": "git", "url": "git://github.com/rvagg/string_decoder.git"}, "homepage": "https://github.com/rvagg/string_decoder", "keywords": ["string", "decoder", "browser", "browserify"], "license": "MIT", "gitHead": "847f2f513a5648857af03adf680c90d833a499d2", "bugs": {"url": "https://github.com/rvagg/string_decoder/issues"}, "_id": "string_decoder@1.0.0", "_shasum": "f06f41157b664d86069f84bdbdc9b0d8ab281667", "_from": ".", "_npmVersion": "4.4.4", "_nodeVersion": "7.8.0", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "dist": {"shasum": "f06f41157b664d86069f84bdbdc9b0d8ab281667", "tarball": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.0.0.tgz", "integrity": "sha512-W3xBL2XOY6wH8jdognQCwUGcvT0foutNqg729ypD+ZJlTy28dCQSbAViftVhOEOUQGxZdUki53AzHsRSXw3wTg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGvl4/b46SwxTOCVVwbQSVdS/fWlFzK625bJN1Z2gpldAiAPp0WKOyhdGNDWLYqJ7ESI0yMWiil9OYnBoJm7ECP55A=="}]}, "maintainers": [{"name": "substack", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/string_decoder-1.0.0.tgz_1491485897373_0.9406327686738223"}, "directories": {}}, "1.0.1": {"name": "string_decoder", "version": "1.0.1", "description": "The string_decoder module from Node core", "main": "lib/string_decoder.js", "dependencies": {"safe-buffer": "^5.0.1"}, "devDependencies": {"babel-polyfill": "^6.23.0", "tap": "~0.4.8"}, "scripts": {"test": "tap test/parallel/*.js"}, "repository": {"type": "git", "url": "git://github.com/rvagg/string_decoder.git"}, "homepage": "https://github.com/rvagg/string_decoder", "keywords": ["string", "decoder", "browser", "browserify"], "license": "MIT", "gitHead": "2c3f24bebf91134e0138629bc7109401085b17dd", "bugs": {"url": "https://github.com/rvagg/string_decoder/issues"}, "_id": "string_decoder@1.0.1", "_shasum": "62e200f039955a6810d8df0a33ffc0f013662d98", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "6.10.1", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "dist": {"shasum": "62e200f039955a6810d8df0a33ffc0f013662d98", "tarball": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.0.1.tgz", "integrity": "sha512-Ma/XSGC8lfDvw75eLjgg/a1nWDButtedmpbbNxH5Ruyr0IhqNXOKbG468VtPosrjhRgNOvgonmY54ZnGMdgJjw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGE+0HXe0rPQuib/rvaLig1M6bbpPpNxCCNufgYgcbRrAiEA5asN1VXtKaEC2/PfuLR3NbbHOkm7qhDYzz++Y9gFNDA="}]}, "maintainers": [{"email": "<EMAIL>", "name": "matteo.collina"}, {"email": "<EMAIL>", "name": "substack"}, {"email": "<EMAIL>", "name": "rvagg"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/string_decoder-1.0.1.tgz_1495181944293_0.23547686799429357"}, "directories": {}}, "1.0.2": {"name": "string_decoder", "version": "1.0.2", "description": "The string_decoder module from Node core", "main": "lib/string_decoder.js", "dependencies": {"safe-buffer": "~5.0.1"}, "devDependencies": {"babel-polyfill": "^6.23.0", "tap": "~0.4.8"}, "scripts": {"test": "tap test/parallel/*.js && node test/verify-dependencies"}, "repository": {"type": "git", "url": "git://github.com/rvagg/string_decoder.git"}, "homepage": "https://github.com/rvagg/string_decoder", "keywords": ["string", "decoder", "browser", "browserify"], "license": "MIT", "gitHead": "fb0c8bc0741f8ac25f3d354a17d9106a7714f3da", "bugs": {"url": "https://github.com/rvagg/string_decoder/issues"}, "_id": "string_decoder@1.0.2", "_shasum": "b29e1f4e1125fa97a10382b8a533737b7491e179", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.8.0", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "dist": {"shasum": "b29e1f4e1125fa97a10382b8a533737b7491e179", "tarball": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.0.2.tgz", "integrity": "sha512-ZHS/JKflyPCvJT+8a8OJS/bXK3ckSErTMh0B3/ElQd2KgMB4368ABudiQRXKWpr3MTBETp4ajjIMKCXcife/Aw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC2HolpTrjckwpJ7abSA/l0D1/JcSzXO4/J1tb7F2SgbgIhALZlOJoPBYt/AiC9wHVW+QbHPFExndT8qfK7eRjDS8G1"}]}, "maintainers": [{"email": "<EMAIL>", "name": "matteo.collina"}, {"email": "<EMAIL>", "name": "substack"}, {"email": "<EMAIL>", "name": "rvagg"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/string_decoder-1.0.2.tgz_1496758759778_0.9832893849816173"}, "directories": {}}, "1.0.3": {"name": "string_decoder", "version": "1.0.3", "description": "The string_decoder module from Node core", "main": "lib/string_decoder.js", "dependencies": {"safe-buffer": "~5.1.0"}, "devDependencies": {"babel-polyfill": "^6.23.0", "tap": "~0.4.8"}, "scripts": {"test": "tap test/parallel/*.js && node test/verify-dependencies"}, "repository": {"type": "git", "url": "git://github.com/rvagg/string_decoder.git"}, "homepage": "https://github.com/rvagg/string_decoder", "keywords": ["string", "decoder", "browser", "browserify"], "license": "MIT", "gitHead": "e97f24dd3d047b72b9836518e2a0788e2a6a2fdb", "bugs": {"url": "https://github.com/rvagg/string_decoder/issues"}, "_id": "string_decoder@1.0.3", "_npmVersion": "5.0.3", "_nodeVersion": "8.1.1", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-4AH6Z5fzNNBcH+6XDMfA/BTt87skxqJlO0lAh3Dker5zThcAxG6mKz+iGu308UKoPPQ8Dcqx/4JhujzltRa+hQ==", "shasum": "0fc67d7c141825de94282dd536bec6b9bce860ab", "tarball": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.0.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDcpAGsfp7eS9GbHkiHz8mAPtnRH6nahGMEBKINjiqSGAiEA/wbjCRcUY4Uq3bFfVAdj3nOg6mOSZAtWnEo6V5iic54="}]}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/string_decoder-1.0.3.tgz_1498156574101_0.8198789858724922"}, "directories": {}}, "1.1.0": {"name": "string_decoder", "version": "1.1.0", "description": "The string_decoder module from Node core", "main": "lib/string_decoder.js", "dependencies": {"safe-buffer": "~5.1.0"}, "devDependencies": {"babel-polyfill": "^6.23.0", "core-util-is": "^1.0.2", "inherits": "^2.0.3", "tap": "~0.4.8"}, "scripts": {"test": "tap test/parallel/*.js && node test/verify-dependencies", "ci": "tap test/parallel/*.js test/ours/*.js --tap | tee test.tap && node test/verify-dependencies.js"}, "repository": {"type": "git", "url": "git://github.com/rvagg/string_decoder.git"}, "homepage": "https://github.com/rvagg/string_decoder", "keywords": ["string", "decoder", "browser", "browserify"], "license": "MIT", "gitHead": "be92302c06108985282568b20412a54576a97017", "bugs": {"url": "https://github.com/rvagg/string_decoder/issues"}, "_id": "string_decoder@1.1.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-8zQpRF6juocE69ae7CSPmYEGJe4VCXwP6S6dxUWI7i53Gwv54/ec41fiUA+X7BPGGv7fRSQJjBQVa0gomGaOgg==", "shasum": "384f322ee8a848e500effde99901bba849c5d403", "tarball": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.0.tgz", "fileCount": 5, "unpackedSize": 15296, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+Hhs8bXfgaOQtDhXO6wVuEh83TT2oNLStPBvG9eP0SwIgR13Ra+ZQV9tzikLkB32gEi/vX+psDaCQk7edWhQYqGQ="}]}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/string_decoder_1.1.0_1520331630388_0.7986042423664588"}, "_hasShrinkwrap": false}, "1.1.1": {"name": "string_decoder", "version": "1.1.1", "description": "The string_decoder module from Node core", "main": "lib/string_decoder.js", "dependencies": {"safe-buffer": "~5.1.0"}, "devDependencies": {"babel-polyfill": "^6.23.0", "core-util-is": "^1.0.2", "inherits": "^2.0.3", "tap": "~0.4.8"}, "scripts": {"test": "tap test/parallel/*.js && node test/verify-dependencies", "ci": "tap test/parallel/*.js test/ours/*.js --tap | tee test.tap && node test/verify-dependencies.js"}, "repository": {"type": "git", "url": "git://github.com/nodejs/string_decoder.git"}, "homepage": "https://github.com/nodejs/string_decoder", "keywords": ["string", "decoder", "browser", "browserify"], "license": "MIT", "gitHead": "18c7f89c894ced5f610505bb006dfde9a3d1ac5e", "bugs": {"url": "https://github.com/nodejs/string_decoder/issues"}, "_id": "string_decoder@1.1.1", "_npmVersion": "5.8.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "shasum": "9cf1611ba62685d7030ae9e4ba34149c3af03fc8", "tarball": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "fileCount": 5, "unpackedSize": 15298, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDrBqKj0YOylSE12IL+RyeRmAzbflIj4BqETZYaUMsWfwIgLiuMoz8ppmpPSKK9Wi16iQ5bJpd5quoe5faeuc3ejLA="}]}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/string_decoder_1.1.1_1522397654739_0.2722524344416213"}, "_hasShrinkwrap": false}, "1.2.0": {"name": "string_decoder", "version": "1.2.0", "description": "The string_decoder module from Node core", "main": "lib/string_decoder.js", "dependencies": {"safe-buffer": "~5.1.0"}, "devDependencies": {"babel-polyfill": "^6.23.0", "core-util-is": "^1.0.2", "inherits": "^2.0.3", "tap": "~0.4.8"}, "scripts": {"test": "tap test/parallel/*.js && node test/verify-dependencies", "ci": "tap test/parallel/*.js test/ours/*.js --tap | tee test.tap && node test/verify-dependencies.js"}, "repository": {"type": "git", "url": "git://github.com/nodejs/string_decoder.git"}, "homepage": "https://github.com/nodejs/string_decoder", "keywords": ["string", "decoder", "browser", "browserify"], "license": "MIT", "gitHead": "6e0a9286ed4497badebd4ec6a9a7a4d37793aae8", "bugs": {"url": "https://github.com/nodejs/string_decoder/issues"}, "_id": "string_decoder@1.2.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.13.0", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6YqyX6ZWEYguAxgZzHGL7SsCeGx3V2TtOTqZz1xSTSWnqsbWwbptafNyvf/ACquZUXV3DANr5BDIwNYe1mN42w==", "shasum": "fe86e738b19544afe70469243b2a1ee9240eae8d", "tarball": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.2.0.tgz", "fileCount": 4, "unpackedSize": 14427, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb/WTYCRA9TVsSAnZWagAA7zkP/RGoxWSapn/m8DNEJZVQ\ncZWpaB+5n/df+kgEGP94vYLyDdf7VWCM/B1MRyviERBcv1B0b9E7lH1Vn9fU\nhLb2REa5C4wGbIVqblTON5etuTRWr+gVspUH6riBMMydaLlscGxdv6vgXmrA\nXe4Ykz1xKUudq4hVMf/Uf8wSUn7/CgmqjopCl+14iTDMSH1Sovwc9WnbxJDW\niGEDfLp5xttPQtatrgQ4nxl/fWB4U6HE0nN7MwSQ5nhtSU3SOdbIKHH6fUNO\niTM+mv/FRASkefg53w1V2yLOXlpIGsY5yZE6lxgCQXrzoOKvHgWpO5wqHomp\n2Hk5FdPEvb8LAEWd9hDSQjTuHVchgCUWcnIcVUrpO334a1GXZwZ66OiEmSjw\nq37VjMTN6jToBcDE3LLrc80EFBPaLkZBD7+AP26jwjzlHGAXSmaGSnixj3Kx\nSCKgC31V+reocHzxcUk8gXZtMStn4wwAb1V+da4S70WCwaMH2XBlwLhUKHg4\nvHtkjl1ALRseBoWcA8sbnuXTlXx0q5ckoZpZyBigDJfaFlBZ2Mm8jPr5/d+Q\nt4biwiNeZbqeyOf6YTsBYk6QyIvepoCpzQdxP5c4OJWuq5y4CLkAIHq2oOp1\nQ6Bthb+7uAhYAH+GgAYul8RERpyBb8aYmc/UzSh9LwLTlp+k949EbqcrCStN\nk53W\r\n=fSP/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC4M8xGEzn+Ef+nfQAIlRatkkiyDC+7mmjhGqyuQaTU7gIgX9h5F0iFFD05VtZiAqjBtmKxMa/CoodsPnS6vTSrfE0="}]}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/string_decoder_1.2.0_1543333079384_0.881054143474117"}, "_hasShrinkwrap": false}, "1.3.0": {"name": "string_decoder", "version": "1.3.0", "description": "The string_decoder module from Node core", "main": "lib/string_decoder.js", "dependencies": {"safe-buffer": "~5.2.0"}, "devDependencies": {"babel-polyfill": "^6.23.0", "core-util-is": "^1.0.2", "inherits": "^2.0.3", "tap": "~0.4.8"}, "scripts": {"test": "tap test/parallel/*.js && node test/verify-dependencies", "ci": "tap test/parallel/*.js test/ours/*.js --tap | tee test.tap && node test/verify-dependencies.js"}, "repository": {"type": "git", "url": "git://github.com/nodejs/string_decoder.git"}, "homepage": "https://github.com/nodejs/string_decoder", "keywords": ["string", "decoder", "browser", "browserify"], "license": "MIT", "gitHead": "60db81e031c126112039157ba9437484b1329dff", "bugs": {"url": "https://github.com/nodejs/string_decoder/issues"}, "_id": "string_decoder@1.3.0", "_nodeVersion": "10.16.0", "_npmVersion": "6.10.2", "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==", "shasum": "42f114594a46cf1a8e30b0a84f56c78c3edac21e", "tarball": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz", "fileCount": 4, "unpackedSize": 14427, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdSpflCRA9TVsSAnZWagAAVE8QAIBhyQ4HL/C719h6NHEm\noaNdOGDpxw0zyapxhztRy+YPAEGVOAnS5L/VMQINojzPQQ8YTZDM5jQ5+NyC\naVBB9KtQpCXftizA0DX5NbowL/k9s1ko+PFf0/hOPA4NWhZ9H5Vym+fNNMQJ\n+p0leE2ovcz0PVT8t8/IrntVTYRnhkDnkTJa9byWGZFiumoR4J/y0k/UfuR4\nqKF9NGgDURJrAvdkbaBEc4xs4ZM2Eqfevxki/Ia6cV2eWO/ssWzZXdS0oLfN\ncPieD7D80jTnrZNnh6iEdYFU6GQDgu8ovDURIVnI3qowB/aDBpMAMpKliY32\nMyB1VSAWPADAYwHaW6ldS23Bd1fFA9LJZtL4/2YmR5lTri14j6szJdXQZOCE\n26kivguwcdEsbsDkE4ayRJ6DNC18lvcmJCyBUpHPBzEFP8HvC+En3Cxd4wAE\n+6laaHp7x+Af3KV4cnBsKHsDpugmzi6SWKMjt9ymySNfefvzll7pI5p8mQD1\niFM92XvnHY1RjXtuSwSHo/U8F9B/EogCC4kRSXSyq1xSDvZ530BaWsX27iv4\n2kbmRndn4pImKpIzFsUNaG8IAs90bw3mjc+dmMi7y/mbid5+xesX5RCztajK\nfFWZhV46ePff4+YFJVtCKR/m2LCu6dbSLHxTxpdFVfcjU83qnzpWGYdbHWwG\nxT/z\r\n=TD7t\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFML7Q24wtx22492hRqX8plqsKhaQPZdcGQkdPSBCoCHAiA+TGpH7jPR58FPT5rR/mxxIc/f/yaQ9IfwoVA6Nyxj3Q=="}]}, "maintainers": [{"name": "cwmma", "email": "<EMAIL>"}, {"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "nodejs-foundation", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "substack", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/string_decoder_1.3.0_1565169636487_0.6257381665304729"}, "_hasShrinkwrap": false}}, "readme": "# string_decoder\n\n***Node-core v8.9.4 string_decoder for userland***\n\n\n[![NPM](https://nodei.co/npm/string_decoder.png?downloads=true&downloadRank=true)](https://nodei.co/npm/string_decoder/)\n[![NPM](https://nodei.co/npm-dl/string_decoder.png?&months=6&height=3)](https://nodei.co/npm/string_decoder/)\n\n\n```bash\nnpm install --save string_decoder\n```\n\n***Node-core string_decoder for userland***\n\nThis package is a mirror of the string_decoder implementation in Node-core.\n\nFull documentation may be found on the [Node.js website](https://nodejs.org/dist/v8.9.4/docs/api/).\n\nAs of version 1.0.0 **string_decoder** uses semantic versioning.\n\n## Previous versions\n\nPrevious version numbers match the versions found in Node core, e.g. 0.10.24 matches Node 0.10.24, likewise 0.11.10 matches Node 0.11.10.\n\n## Update\n\nThe *build/* directory contains a build script that will scrape the source from the [nodejs/node](https://github.com/nodejs/node) repo given a specific Node version.\n\n## Streams Working Group\n\n`string_decoder` is maintained by the Streams Working Group, which\noversees the development and maintenance of the Streams API within\nNode.js. The responsibilities of the Streams Working Group include:\n\n* Addressing stream issues on the Node.js issue tracker.\n* Authoring and editing stream documentation within the Node.js project.\n* Reviewing changes to stream subclasses within the Node.js project.\n* Redirecting changes to streams from the Node.js project to this\n  project.\n* Assisting in the implementation of stream providers within Node.js.\n* Recommending versions of `readable-stream` to be included in Node.js.\n* Messaging about the future of streams to give the community advance\n  notice of changes.\n\nSee [readable-stream](https://github.com/nodejs/readable-stream) for\nmore details.\n", "maintainers": [{"email": "<EMAIL>", "name": "cwmma"}, {"email": "<EMAIL>", "name": "matteo.collina"}, {"email": "<EMAIL>", "name": "rvagg"}, {"email": "<EMAIL>", "name": "nodejs-foundation"}], "time": {"modified": "2023-07-12T19:05:48.571Z", "created": "2013-12-03T20:13:26.191Z", "0.0.0": "2013-12-03T20:13:27.743Z", "0.0.1": "2014-01-17T19:53:23.612Z", "0.10.24": "2014-01-18T05:27:35.195Z", "0.11.10": "2014-01-18T05:28:38.924Z", "0.10.25": "2014-01-28T02:21:38.263Z", "0.11.10-1": "2014-01-28T02:24:31.052Z", "0.10.25-1": "2014-02-12T09:51:56.014Z", "0.10.31": "2014-08-23T04:25:19.329Z", "1.0.0": "2017-04-06T13:38:19.155Z", "1.0.1": "2017-05-19T08:19:05.834Z", "1.0.2": "2017-06-06T14:19:20.690Z", "1.0.3": "2017-06-22T18:36:15.114Z", "1.1.0": "2018-03-06T10:20:30.445Z", "1.1.1": "2018-03-30T08:14:15.043Z", "1.2.0": "2018-11-27T15:37:59.670Z", "1.3.0": "2019-08-07T09:20:36.634Z"}, "repository": {"type": "git", "url": "git://github.com/nodejs/string_decoder.git"}, "readmeFilename": "README.md", "users": {"wenbing": true, "simplyianm": true, "xgqfrms": true, "mojaray2k": true, "faraoman": true, "flumpus-dev": true}, "homepage": "https://github.com/nodejs/string_decoder", "keywords": ["string", "decoder", "browser", "browserify"], "bugs": {"url": "https://github.com/nodejs/string_decoder/issues"}, "license": "MIT"}