{"_id": "@types/cors", "_rev": "707-2a08600d575ddefcf50bbf532023bde8", "name": "@types/cors", "dist-tags": {"ts2.0": "2.8.1", "ts2.1": "2.8.1", "ts2.2": "2.8.5", "ts2.3": "2.8.6", "ts2.5": "2.8.6", "ts2.6": "2.8.6", "ts2.7": "2.8.6", "ts2.8": "2.8.6", "ts2.9": "2.8.6", "ts2.4": "2.8.6", "ts3.1": "2.8.7", "ts3.0": "2.8.7", "ts3.2": "2.8.8", "ts3.3": "2.8.9", "ts3.5": "2.8.10", "ts3.4": "2.8.10", "ts3.8": "2.8.12", "ts3.9": "2.8.12", "ts4.0": "2.8.12", "ts4.1": "2.8.12", "ts3.6": "2.8.12", "ts3.7": "2.8.12", "ts4.2": "2.8.13", "ts4.4": "2.8.14", "ts4.3": "2.8.14", "ts4.5": "2.8.17", "ts4.6": "2.8.17", "ts4.7": "2.8.17", "ts4.8": "2.8.17", "ts4.9": "2.8.17", "ts5.0": "2.8.17", "ts5.9": "2.8.19", "ts5.4": "2.8.19", "ts5.6": "2.8.19", "ts5.8": "2.8.19", "latest": "2.8.19", "ts5.2": "2.8.19", "ts5.5": "2.8.19", "ts5.7": "2.8.19", "ts5.1": "2.8.19", "ts5.3": "2.8.19"}, "versions": {"0.0.16-alpha": {"name": "@types/cors", "version": "0.0.16-alpha", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "https://github.com/mihhail-lapushkin/"}, "license": "MIT", "_id": "@types/cors@0.0.16-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "61513d4126e939531f63dce51be66c05148b1dc9", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-0.0.16-alpha.tgz", "integrity": "sha512-tryvGV5zp86mTgngZuFkIZo+qzwZEW+zBVDuTTV7eGVPQGO9GYwYvo4umYXX9zRgnrxUSJ1mBVEREiKNz3AcqA==", "signatures": [{"sig": "MEYCIQDXNyk/YyHx092uHkXXGYubwh6e4K5NAlrUQ464WMtK3QIhAKWRzkOt6DUxbw7F1rCgwCGDNlE08xek7355IFE1+Xio", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\cors", "_shasum": "61513d4126e939531f63dce51be66c05148b1dc9", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\cors", "_npmVersion": "3.8.2", "description": "Type definitions for cors from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"equire('express'": "*"}, "_npmOperationalInternal": {"tmp": "tmp/cors-0.0.16-alpha.tgz_1463460130720_0.9423643869813532", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.17-alpha": {"name": "@types/cors", "version": "0.0.17-alpha", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "https://github.com/mihhail-lapushkin/"}, "license": "MIT", "_id": "@types/cors@0.0.17-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "db769f2b2924b317153e2241df512130d449f4e3", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-0.0.17-alpha.tgz", "integrity": "sha512-Futyn2YG/aKNI8KMc2reCqjipl7uClNuvNlAM2wz5shj6sPlgPkOextfnxSY9ClsMnO71q9R3Ain4sNwMqErxQ==", "signatures": [{"sig": "MEQCIGnBq8o1GLVG5AufvhyQwRLgQYNZ8HsV3nm872N/WrW/AiAm+kjNmNgjmY9jaKadftoYfvgN/+lsi9w0rDuIeY9jEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\cors", "_shasum": "db769f2b2924b317153e2241df512130d449f4e3", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\cors", "_npmVersion": "3.8.2", "description": "Type definitions for cors from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"equire('express'": "*"}, "_npmOperationalInternal": {"tmp": "tmp/cors-0.0.17-alpha.tgz_1463690120955_0.6335323066450655", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.22-alpha": {"name": "@types/cors", "version": "0.0.22-alpha", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "https://github.com/mihhail-lapushkin/"}, "license": "MIT", "_id": "@types/cors@0.0.22-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "ddaacb8ecd3c2d4fa313939bef4dc3d03b9114b3", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-0.0.22-alpha.tgz", "integrity": "sha512-ciI5lO2O+ay/YWz2FJDNcpVlV2PIugyhGQEJC05alUBX/8oSXy59KGCxojbhhVsPPRoAiJxeHgYCyS9tZZSXmQ==", "signatures": [{"sig": "MEUCIQDBmXya6n33lZFI6P244mLkzxf9Hiiq0Mi3mIA2wamd1AIgdTaiOEtwCqIuHUVDDRQCeDKjb6NZ1jsNLvyX55uotR4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\cors", "_shasum": "ddaacb8ecd3c2d4fa313939bef4dc3d03b9114b3", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\cors", "_npmVersion": "3.8.2", "description": "TypeScript definitions for cors", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"@types/express": "*"}, "_npmOperationalInternal": {"tmp": "tmp/cors-0.0.22-alpha.tgz_1463772239561_0.2874596833717078", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.23-alpha": {"name": "@types/cors", "version": "0.0.23-alpha", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "https://github.com/mihhail-lapushkin/"}, "license": "MIT", "_id": "@types/cors@0.0.23-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "e0c4c1c71cc85228fed7d60865e868deced4c7fa", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-0.0.23-alpha.tgz", "integrity": "sha512-6c9awn8qrIqgdctCpov8a6rBYjksgutUG6szh8V7rnZW7iZHF9JGFPCvYsPCmtbPExishTvpRlkE+fjdb7EZ1g==", "signatures": [{"sig": "MEUCIBwN5tv+VBf4l8kWq5nvWdvJguNvjb81ZOrbPlOpst+LAiEAuC7seDlKhNplLJUOrFYhUBjfHLP/P3ISYfLd26cDO9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\cors", "_shasum": "e0c4c1c71cc85228fed7d60865e868deced4c7fa", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\cors", "_npmVersion": "3.8.2", "description": "TypeScript definitions for cors", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"@types/express": "*"}, "_npmOperationalInternal": {"tmp": "tmp/cors-0.0.23-alpha.tgz_1464151323245_0.09293201612308621", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.24-alpha": {"name": "@types/cors", "version": "0.0.24-alpha", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "https://github.com/mihhail-lapushkin/"}, "license": "MIT", "_id": "@types/cors@0.0.24-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "19b97e840e614b371cd4214316b9931a65f3e7a9", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-0.0.24-alpha.tgz", "integrity": "sha512-6ebKmIgIlWALlO8wjSkJRsMM94F79zDdYfr8gqp5HhjBVMdJRFKgHPnYlrrCQ6MDQ9RhUF57H0ldXgfoQ+rYIw==", "signatures": [{"sig": "MEQCIC37mrJ0dxSUNzRdOqwEqcUDTchTk18fVKrBT8YQrrY0AiAYf2qUbrCJD+R2CHj1Xp9EX8535XL3xHJIARU3fnIbwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\cors", "_shasum": "19b97e840e614b371cd4214316b9931a65f3e7a9", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\cors", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.9.5", "description": "TypeScript definitions for cors", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {"@types/express": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/cors-0.0.24-alpha.tgz_1467400217457_0.13403520546853542", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.25-alpha": {"name": "@types/cors", "version": "0.0.25-alpha", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "https://github.com/mihhail-lapushkin/"}, "license": "MIT", "_id": "@types/cors@0.0.25-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "8e3e5bb757de5be35326f2714d5b8f6de53d46be", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-0.0.25-alpha.tgz", "integrity": "sha512-DuCWUVwxFTgppB5OkhGxLDIriTPJcnIe8IQrMU6qTdsgL6ughI8tv/8g/HhRUQ+h+h7kxf+O/BMRZFjPHYB71w==", "signatures": [{"sig": "MEUCIQD8IPZZpqjUVHzKOyR854Oc26rg96JZrclaQCB+Slfb1wIgJbJUGTKyoILnHQWGd+aI8dlh+zrLsyNIuUTPHHkwWBM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\cors", "_shasum": "8e3e5bb757de5be35326f2714d5b8f6de53d46be", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\cors", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for cors", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/express": "4.0.24-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/cors-0.0.25-alpha.tgz_1467412237957_0.8284072666428983", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.26-alpha": {"name": "@types/cors", "version": "0.0.26-alpha", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "https://github.com/mihhail-lapushkin/"}, "license": "MIT", "_id": "@types/cors@0.0.26-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "688f9e5425cc8d9d876aa4595d1ebf10ceb0a10c", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-0.0.26-alpha.tgz", "integrity": "sha512-MzzycuoUBG4+nJEvTIpIvg3XKl4qNymh2VUGCXTBkdETG+IVfIKPvb0tPZ28LutgQcwNqJZdeiF4M4eloDroJw==", "signatures": [{"sig": "MEYCIQCFgD3SlqhT9SaAumFmG7uPfgzwUo8eSUTzl/E/TLpE8wIhAJBXtNj859aHeHAri5yJRaT7gujpLJDErw3b/FYw0/DZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\cors", "_shasum": "688f9e5425cc8d9d876aa4595d1ebf10ceb0a10c", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\cors", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for cors", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/express": "4.0.25-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/cors-0.0.26-alpha.tgz_1467425737511_0.7930498791392893", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.27-alpha": {"name": "@types/cors", "version": "0.0.27-alpha", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "https://github.com/mihhail-lapushkin/"}, "license": "MIT", "_id": "@types/cors@0.0.27-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "6ed63bb7d3de77f87d327d95b56b0e5e5a9645a2", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-0.0.27-alpha.tgz", "integrity": "sha512-Xr+DN3TpdFQ7W0dXkUVio/9FmPazty3jjCp34IKL9a3V8JM54PkhY1cAiLrS/91mWGu5rWUkS1He03sCRqHOvw==", "signatures": [{"sig": "MEQCIEueg2uCOIcHjoqIWZKTQFu6Ou8ZRljfgRlUF3EORXbRAiBqMBXJ90l+ca9rSFBbvQYc91loRnK3WsrTBu0CevsESw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\cors", "_shasum": "6ed63bb7d3de77f87d327d95b56b0e5e5a9645a2", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\cors", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for cors", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/express": "4.0.27-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/cors-0.0.27-alpha.tgz_1467590451380_0.4959877145010978", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.28-alpha": {"name": "@types/cors", "version": "0.0.28-alpha", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "https://github.com/mihhail-lapushkin/"}, "license": "MIT", "_id": "@types/cors@0.0.28-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "51be5f52122d2d6f25f00b3b1b12ba90b57ace50", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-0.0.28-alpha.tgz", "integrity": "sha512-jrA8S2ygXnKeCgjZOAwhElD50YhJb6olFUEqQvFv8PB5dPUXaGm9fYD0ci6wS6q40mc4oqEIS2DsCFvZh38/eg==", "signatures": [{"sig": "MEYCIQCS7NUR3MEMipClg4JJcZkLtBOdRL5v2o0UG0ZKnlsKSgIhAMsNKIkiLcDyINhEnYmRau9d9h4wzYKTmn45y8mkJoQF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\cors", "_shasum": "51be5f52122d2d6f25f00b3b1b12ba90b57ace50", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\cors", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for cors", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/express": "4.0.28-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/cors-0.0.28-alpha.tgz_1467911298400_0.40025015198625624", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.29": {"name": "@types/cors", "version": "0.0.29", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "https://github.com/mihhail-lapushkin/"}, "license": "MIT", "_id": "@types/cors@0.0.29", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "b3077162c44e0160e50f41519dd35adef1c2386b", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-0.0.29.tgz", "integrity": "sha512-fxI7i5JC3kuS5jZDpfCbkcOL+10SBZeD4MJVlVjEW4WrKSD2+eEEFwEAkBUHhfv7iQokdGx+IwNeu0lalOIY5w==", "signatures": [{"sig": "MEQCIGFhkaVXCulbril5CfkOQ2J1eInnu2Tr7QuGrLBjMlk7AiBYhWWTLsg4WsHqLtHYNUcZ62WvWXTs/yUsN40R4iVM6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\cors", "_shasum": "b3077162c44e0160e50f41519dd35adef1c2386b", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\cors", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for cors", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"@types/express": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/cors-0.0.29.tgz_1468506084248_0.5033458049874753", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.30": {"name": "@types/cors", "version": "0.0.30", "author": "<PERSON><PERSON><PERSON> <https://github.com/mihhail-lapushkin/>", "license": "MIT", "_id": "@types/cors@0.0.30", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "c55856eef7317c7b88d9a9129a9569a5058e66af", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-0.0.30.tgz", "integrity": "sha512-TLGy5c7eZM7+i5lODc/iVcxeHWwsbN5dJTZx3WgL44yj9am6IPOxDUi5Oc90w9G1uCiClSlCUPfKA6w2ih5UaQ==", "signatures": [{"sig": "MEYCIQCfMkjoSAgpQ4bYxdCqZpIHV8T8zacfvaHotfz5aWAHhQIhALlos59HSchO5Y+X7hwPeXsUMODgU3ujd0sjc7cRlff+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {"@types/express": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/cors-0.0.30.tgz_1470152998593_0.6102244399953634", "host": "packages-16-east.internal.npmjs.com"}}, "0.0.31": {"name": "@types/cors", "version": "0.0.31", "author": "<PERSON><PERSON><PERSON> <https://github.com/mihhail-lapushkin/>", "license": "MIT", "_id": "@types/cors@0.0.31", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "32ddcffffe52816ccf9ebf22a2dcdfcbbdba5747", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-0.0.31.tgz", "integrity": "sha512-WdJs4o0rFM/KhjKqJhFlLa80QD31LwWjIvMDnHhvj86E++m5jq0y7NsrYZC8LG6th6zvH9jfS+vBHsXQYYoLQQ==", "signatures": [{"sig": "MEQCIGbpNNueKfdSfAsA+ErPdVpIhTtUsX5HeuNzqbiImRVKAiAWFEV/iTfbHURQGxm5lKKDXFCkz632KqNKZ7qhlh2Eiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {"@types/express": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/cors-0.0.31.tgz_1471620177811_0.7358324793167412", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.32": {"name": "@types/cors", "version": "0.0.32", "author": "<PERSON><PERSON><PERSON> <https://github.com/mihhail-lapushkin/>", "license": "MIT", "_id": "@types/cors@0.0.32", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "eb8da83e5fd88e901753ba07d77c472c79e5c7d4", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-0.0.32.tgz", "integrity": "sha512-j8ep6hrL1kXlnP5yyuUz1f6V6hp68jeadDVUfu4AwGCmBkNeZk48mSCeoGN4i7094brftlD5zZdPIGisKv7dGw==", "signatures": [{"sig": "MEYCIQDmSCL9qHY9eMkZl2RqDPVrc+Sr24SQ2Dak/WaOLW2G4gIhAMBCgL2SfEAJhrOu0s+nq8Pbipv2/dAE2CvmhbVaBp7d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {"@types/express": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/cors-0.0.32.tgz_1472150264013_0.7422501500695944", "host": "packages-12-west.internal.npmjs.com"}}, "0.0.33": {"name": "@types/cors", "version": "0.0.33", "author": "<PERSON><PERSON><PERSON> <https://github.com/mihhail-lapushkin/>", "license": "MIT", "_id": "@types/cors@0.0.33", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "05dc50760b93201b81c54a94fb757f21144f5e61", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-0.0.33.tgz", "integrity": "sha512-9gSpeI3P8G1nFkIoK2D/3eUoYXCQoqcgECzQY1p9rLuld4ImZ8ebQd88m+VWWV1xVx7Mc3Ri03JvVzJy4yOwhQ==", "signatures": [{"sig": "MEYCIQCotLzZWEqvUQtlsTmfafBAtImWPfMUULn1pAOJ6glJcQIhAL7ZraMET8WHZgQ08gyBWtUpMoswvLwho47aB5VWpB2z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {"@types/express": "*"}, "_npmOperationalInternal": {"tmp": "tmp/cors-0.0.33.tgz_1474302884100_0.4495333591476083", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "4b4b8a87d6b7f98091fc8aac53ab32a0ebad252862dbbdb46d870c970a618698"}, "2.8.0": {"name": "@types/cors", "version": "2.8.0", "author": "<PERSON><PERSON><PERSON> <https://github.com/mihhail-lapushkin/>", "license": "MIT", "_id": "@types/cors@2.8.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "9e4d0308b862f5306ae8d83859f90abd379199c8", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-2.8.0.tgz", "integrity": "sha512-7SRdTQ2FB+A/pI6J77vVbKQwJySOGpBi8RdRxUEg/fVMORDgesWCaaRD1t++IvELP48O008spWujyakb4mAewg==", "signatures": [{"sig": "MEYCIQCO45vJnVabyM5IivHeTRH2va8K/hhqGzKSBrn1oqW0YwIhAK6GeWMMDrQ9JJzHX73+u7HPlpXVgm8qjyw/7A4z5MRs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {"@types/express": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/cors-2.8.0.tgz_1483564643228_0.8234567667823285", "host": "packages-18-east.internal.npmjs.com"}, "typesPublisherContentHash": "bd5cf283596ed08c392d10c0c1b5fdc5804c1569fcecd47d3edc1e5f8030a80f"}, "2.8.1": {"name": "@types/cors", "version": "2.8.1", "license": "MIT", "_id": "@types/cors@2.8.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/mihhail-lapushkin/", "name": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "54073caf3b7a741e67fb82483f83a6cadfba7501", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-2.8.1.tgz", "integrity": "sha512-8xJpqRrY/nUh35mhvA/rCHaSzNA2kStG891GNicCN/dB+zRZ0dlgPArvxee3QljfUNSBwL1t/Y4vcx9rvBmfzA==", "signatures": [{"sig": "MEUCIQCQAsbyiqRW/LXn6d21NsBwv7fCj+U9eZQwUJxVMfEsSwIganLjE748OCxQkIupwbLMYout1WdBud7fn5yIXkhghks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {"@types/express": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/cors-2.8.1.tgz_1489191274185_0.37953048828057945", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "93d0114a4610eeb2bccaeb89d41464cc662b38f7a17240b33ededc42ed2ce8fc"}, "2.8.2": {"name": "@types/cors", "version": "2.8.2", "license": "MIT", "_id": "@types/cors@2.8.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/mihhail-lapushkin/", "name": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "0afc130f896480308663a93ef3ca767ca8f86443", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-2.8.2.tgz", "integrity": "sha512-E9ic5j9l85SVkT/8p02/+Y7FGnrVoomjxMnXenbkHUlTPZy87uJox79gD3fXilWNFe5/92fCAY8vEqK6YlcDPw==", "signatures": [{"sig": "MEUCIQDiyXMeeCbNyce14H/LPBdem7G6UPrs9skizD0GzlJAvAIgU+E2lo9gBGp4Oj5cBeQ1G5xFWVO8rIxefnhS+UNUD4g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {"@types/express": "*"}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/cors-2.8.2.tgz_1510179439721_0.662114582490176", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "16a63efdf0891cf313d4dd7b5aad878bdefa6fd1433faac36fde187b795e388e"}, "2.8.3": {"name": "@types/cors", "version": "2.8.3", "license": "MIT", "_id": "@types/cors@2.8.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/mihhail-lapushkin/", "name": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "eaf6e476da0d36bee6b061a24d57e343ddce86d6", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-2.8.3.tgz", "integrity": "sha512-wiZ7yYSIKZ005QJeyoUk5OHHEamNHTxaYwaFQWfTPohBjyhgIDHTgV8oGn+zBYTWQCb9WQYg54PhtntFTD7GVg==", "signatures": [{"sig": "MEQCIF+VQ03ofwa/9n93bO53IkeSxCsjVqhf5BS1Tczr/WfDAiAcpl8oWCEuBX20itOfPp8dsT8qtvrK9xbL6jicHoLNYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {"@types/express": "*"}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/cors-2.8.3.tgz_1510354769279_0.2601675905752927", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "92837fc97dc98472b50c21e630e63d08ff9d39f300de75877453ae1d48f1897c"}, "2.8.4": {"name": "@types/cors", "version": "2.8.4", "license": "MIT", "_id": "@types/cors@2.8.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/pluma", "name": "<PERSON>", "githubUsername": "pluma"}], "dist": {"shasum": "50991a759a29c0b89492751008c6af7a7c8267b0", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-2.8.4.tgz", "fileCount": 4, "integrity": "sha512-ipZjBVsm2tF/n8qFGOuGBkUij9X9ZswVi9G3bx/6dz7POpVa6gVHcj1wsX/LVEn9MMF41fxK/PnZPPoTD1UFPw==", "signatures": [{"sig": "MEUCIQD2LWIJtL0zXqV2LskdRAvmqzTZmeWtQliT8yCkl6ue2wIgGVeSE+GQqa4gOEw5KuzI4k4mqaUitAOMIoFrwOhGhEQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3395, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3fa8CRA9TVsSAnZWagAAfJkQAJBVFaimb7LoiS6Vty/T\nboSr/rlXGBu56oHxgUszC4qj4HEiwzqSS+FObgzdLCj2draDMFbBsgTmEv0S\naRPtyOFSb/j2MrHNU7cbAjbfjcOlhkknu0tCyzIEmf2VZ+YP4sMxAPBNpca4\nWs854y9gta3BMgR2X9cQxOarsyyIBwXTmyyXY1uH82ryjGano+n91t7f5UHP\nEzM0rcW7NouwJ541fbJ5Z1d7DiyBqxkcdzIc6QO2xWdMQ1E5KFlGlJcl5vBf\nf+AW3/h9e+Wh519CBSWgVkqFbwId1e3BuTbaXOAjlVeKLiBA2KFiNonoUu6Z\njZDn87g265Vu5zps2A9vxQOPceH1+e83WQylACA0yKC/NnVaXpLrnyqcnxHt\n9XQb5aYNciW6tpfKjZYMowR3CF90I4g/ovYxw+Bq1Hq3Rec8i1TQ8MrgMdV2\nxFzExXdEzXhIr732BnNiLHFGkO/AIX7VBu0AreQzGR0/ehHGqRh0GbEc9sD8\n52lrscBSy8KQlAT3LXCJK0eYl1dMgJz1li5jPRqUkHC27jVpRPnJxZfliMNi\nzpXEj6guWAosKZ/DQjeuLE7il/sBdpOfUwm4MFXKHxD1HSrcOxmctmy8cCvb\n5mqsTS2FG6Myl+shVazrfycjU9WKS4MuAyD9RNqZdidPXWFVjbbKw2tcq2aY\nUTpd\r\n=9fUT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {"@types/express": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/cors_2.8.4_1524496058975_0.3431334859792956", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c206254f8940d61a0375ce4f7a7d68e0ebf14d7b49b8abefec6375a8955615f7"}, "2.8.5": {"name": "@types/cors", "version": "2.8.5", "license": "MIT", "_id": "@types/cors@2.8.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/pluma", "name": "<PERSON>", "githubUsername": "pluma"}], "dist": {"shasum": "c0c54c4e643e1d943d447292f2baf9dc82cfc8ec", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-2.8.5.tgz", "fileCount": 4, "integrity": "sha512-GmK8AKu8i+s+EChK/uZ5IbrXPcPaQKWaNSGevDT/7o3gFObwSUQwqb1jMqxuo+YPvj0ckGzINI+EO7EHcmJjKg==", "signatures": [{"sig": "MEUCIAObHpIwalcGb7zWpw3umGJWUmTz1+abIJeffTOXXLMuAiEAoAMW7E7wQeo88iB+HeFcZxptXXAA7PLGFmtqTDwHFCc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3431, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcwjOtCRA9TVsSAnZWagAA2fkP/A5z6MMR7CkKh+XIn2oP\n9AEu4SY6siuppuPhxMURlQmiqsT1+Yj7UTRwomoYt8SV+wpcfXUCFuhpDMST\nc0aFLye3X0M/XQSFZ/x2iNEHcoEgmgkTXhn35JL3IixcQi9Lkli0s9LvwYXE\nhbAcgTHhkbCb41EbWA819xU7X6C9oD8U2vb3Ggx9noAnS0aAfPoNQ7M4eOSM\n83Lh9sJtROAi/VMt55UbSQDgQApTx9aK0U8YmuZfcNhjdA3lWzDrdJW94+r3\nkF3uDEE/cT7sJT+McDyLlZY4crCjuExKvhSypeKqeqOpXjvoBDsSxKcM1h2r\ntdyBEl5jrrA+zjPth18uB1VzbLfIBr4vT/PZt9aoO1/JhFWOI4dzzY8IpUgS\nIRB2XfRJ/zB0YIfoofrR94tjyl/SbUk2/KLOAdrFLeXqssJCTHlfVMy7gRYm\n3UgBxr2EeeUVcZCd6E5vq6DbIZfTSHgBHTZKAk336J8SGtbHziNZXCfUno3W\nEobaPuZ6girHcvv7uZ/Fqh/GSajsIo27wWo81ID0nunxzmCVtb8N/zAwSkXb\napTvCcIH6h1MRvajQLjMIlZ3uNZ/lKgOYv92iWZyH3RfGd6vuAUGW1YDx4EQ\nx4+vmYfmvpLGOSbrdkHbBYq0VfobnekOu5p7vcpj/CEDzHBbe+SNom9u624C\nCTo2\r\n=fb5u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/cors"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {"@types/express": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/cors_2.8.5_1556231085253_0.13486983700901778", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "171eb48cab588a6eee187a908ec93d25de774cee12bfdcdb87112c128942eaa8"}, "2.8.6": {"name": "@types/cors", "version": "2.8.6", "license": "MIT", "_id": "@types/cors@2.8.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/pluma", "name": "<PERSON>", "githubUsername": "pluma"}], "dist": {"shasum": "cfaab33c49c15b1ded32f235111ce9123009bd02", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-2.8.6.tgz", "fileCount": 4, "integrity": "sha512-invOmosX0DqbpA+cE2yoHGUlF/blyf7nB0OGYBBiH27crcVm5NmFaZkLP4Ta1hGaesckCi5lVLlydNJCxkTOSg==", "signatures": [{"sig": "MEUCIHts0jXFeTvh+GqQ770/SE49XwUT5qXd1PqDu7QDtP7hAiEAz+M4Hi7WC2RUX5ltOu4lcEu8LYhluS0j50xbyMb/NC8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3429, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdWfONCRA9TVsSAnZWagAAxxEQAJEyTg9skyOYHAfzEq/w\ntFJzShDXl3XAJXkzR2WsSuKIcHPWN4rOr9usQAYjpW/+v0UFzX/z0PLm/g+x\nF6x2GRpdGRgryTwMTMa4AvYQi4upvjUMi6i6bupvcIHRA9zAsnkSRGtIsYGf\nxCDvW8yN2dQPu5zOGZmCwFsUZHXbRwdZ3mmoYGUiyn4LhLbOa9DTVunSib4r\n96owbTNU7oyDP8oZnBuPHfTMcXeO6FiHqDIvtCW7EwFJYsXlCWMNy4jaoBMA\npiq82y07MOZP5LuwwFsX5od4UHkxMpV85pnuaCYsy+loB9+EYD3FCe7QBKDL\npKsbE2n1aqWejEHXv4sRBXnjWaU2rFakOCy8IxPwbF4ca2O52OiEFUFc4nCr\nTzLbD5lY6ZbOxTkkVTSOSv7BrJyq3Hz2SGux+d40LggUPycK6okTzBx67Em/\nk439KwSTwU/RcOJo+VuSM8aETR/eb18lQ12yMHgw0/1myHE9SaW0xhg7ISKz\nR0pxKUdKv8oFHx3bDLf3dysCqeucVv8CXPzt6gKKXK+Q/wPbDfrjRhNSxsOE\nuiTYQ2QGqJhNwbAkVILAKxCkNfzmJqre3Q1HoWIB8wibuctr2pgQemTdVveS\nO3IenRyu9vdR+74IbF4fV3BxmvWYK/UzHo3lBGa9T2FE62wKLdSEyaw4ILJE\nRGBm\r\n=Cdfd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/cors"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {"@types/express": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/cors_2.8.6_1566176140924_0.735823321808087", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "721739163a52c3386dedca1e8de02fcd7bdf7d791fc9fe69ea12b1cec63b8f3c"}, "2.8.7": {"name": "@types/cors", "version": "2.8.7", "license": "MIT", "_id": "@types/cors@2.8.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/pluma", "name": "<PERSON>", "githubUsername": "pluma"}], "dist": {"shasum": "ab2f47f1cba93bce27dfd3639b006cc0e5600889", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-2.8.7.tgz", "fileCount": 4, "integrity": "sha512-sOdDRU3oRS7LBNTIqwDkPJyq0lpHYcbMTt0TrjzsXbk/e37hcLTH6eZX7CdbDeN0yJJvzw9hFBZkbtCSbk/jAQ==", "signatures": [{"sig": "MEQCIDT3HouiRJhjLU9sWiilrUs8YKOH95hl1Q5xZabsUMcuAiBJ/6ahOixGjhFFM8zwbVOpuuVSWjaFwDvpw2oVeHdEqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3666, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfJII1CRA9TVsSAnZWagAAf/AP/3MLxjvf9/nN0Y2lyyPp\n1cwjMQjcLNxmnwklgqAGD0Q38HUKhuz1Vo9fbUJiJ1kYdd4IhBkoBmDu/ULm\n7evIaGR1jk1CFlxJYqxFqQpK2xR47YzTFdS+/nF4QtEbTomMk/BN7UiE5boM\npSxq8nxv6p5G/cJSd73btfKIpmWu2AMksFKMTGabU8azYSGBmr9Q6ZXqx3JU\n/1GZR+QZ0WHMchPeEL8tcaFqwBJgLJFEwNDP+yY+euIoxTSpOGRFeJorrm15\nWXtOW57Xy9tc/OREIlTYgI38b6z6KsrZ6T81JUwZdGp1Ae9cXBY355NzPo3x\nus+Jf+DM3FKJKyVpicm3Lhbb/mVMwGwOOY1bd743DUj+ZXUDtPMby3ba5mm+\nLTGx3+b+3J+emD/7QVf5aVDdxXG/XmGeuaViIfi5LvjQh7OaNubbkxEdKjne\nwS9//EWxZM932tsnyM3VvJuZPzDojJE+S1FyWVvqelQS5LXbtWhQPtV461/f\nW1wo0Iobh6fYMJdQxiISVa6NlaHI2cNyyUzkNJTmM1Ran+nJm+q0H3oPH5q2\n6wAdHVA5LcAd7qhmesrvHs1UGAt9n0CuP+ypNeYXI43QnY4ttRmA2g9AE9bC\nmOt0M9hiiy26CjcqKxsbSLNTfqMU6iiOxAK3Hp+z3E10LEGjCZuRFIx7pQnP\ncWbg\r\n=8j0N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/cors"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {"@types/express": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/cors_2.8.7_1596228149395_0.6583498389068416", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "0af19f412cacc75439ed20dc927db5f3ea966ac67564b7213df4bf42ba272ad2"}, "2.8.8": {"name": "@types/cors", "version": "2.8.8", "license": "MIT", "_id": "@types/cors@2.8.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/pluma", "name": "<PERSON>", "githubUsername": "pluma"}, {"url": "https://github.com/gtpan77", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "gtpan77"}], "dist": {"shasum": "317a8d8561995c60e35b9e0fcaa8d36660c98092", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-2.8.8.tgz", "fileCount": 4, "integrity": "sha512-fO3gf3DxU2Trcbr75O7obVndW/X5k8rJNZkLXlQWStTHhP71PkRqjwPIEI0yMnJdg9R9OasjU+Bsr+Hr1xy/0w==", "signatures": [{"sig": "MEQCIE4s+Kwl2sKe9R2QhwoXBHECKJmSns9uW0P48+EXgYhQAiB9yh8iDkSMWgpvCKFow/j2r1VC003nOBiC3b5vTiKkHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3914, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfgUUZCRA9TVsSAnZWagAAwZAP/jn1Y9UoEyMBkyGXcxAD\nsQFpKk9rCdwtGToOKQyMOPcl0kalGKMHqQeun03rJ/8ZLejBpp8vBRX0n165\nLvUCcEHVDvPmH2aGUyMKK8VJqXTxQTUdTQcIHwb+VVpbVTmMKRMnyeOzW8o6\nk2BSAZHg4TAaNk9MO0MxvULY6i0HjjvXmA1zi2Yw2jn5hGo6GR4mZGo1TzwZ\nmCPvdXgJVlJXwJqTGY7tkE+8NXdrJ4v1Yish/fDhVCVZ/au00BG0TsXfufQc\n5t9V41dST4+DeH+00woQyvV8rrgJP3Ehd1J9evpXWpABS0j9om6LbSiJP+gv\ngkG4GKTMkoyAjqgZjmcA0c6mzwO28H6xybwFAZ3Q9pNmp8hQEc7WAYjrVXZs\nX/WfJzbnf6ZWOm/QYmElnSm8cLfPlmGgFjHQzeCxs5il5lds2IhQTQu2u7PR\nviZFhrf1InfsbGM8lZLXYGtSDinaQ1VdKKa6Yo2Tr7cTLk4ekU8JJa3VSktQ\nypo7BxHxdnjT412IpSXbP9sKDplJaqnhjwPmb+np16zwE/1x+epMnHgHXMho\ntadDa5kz9HUKBZ6DhlruKpoP2GUTHsN3MwHLVoAvHGlUFnMbCi7JeP3jD9rM\n/UhpYHBVQ7Zf/Rwdj304FtPF+o708EBamozfXevq21KkQZo6dr2NZvWg5R9p\ncRPk\r\n=seIC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/cors"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {"@types/express": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/cors_2.8.8_1602307352782_0.6245581553992658", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b24d14934ef66476c80faf7287ca41764d0c8dffa662ea161164704a1eae48e6"}, "2.8.9": {"name": "@types/cors", "version": "2.8.9", "license": "MIT", "_id": "@types/cors@2.8.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/pluma", "name": "<PERSON>", "githubUsername": "pluma"}, {"url": "https://github.com/gtpan77", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "gtpan77"}], "dist": {"shasum": "4bd1fcac72eca8d5bec93e76c7fdcbdc1bc2cd4a", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-2.8.9.tgz", "fileCount": 4, "integrity": "sha512-zurD1ibz21BRlAOIKP8yhrxlqKx6L9VCwkB5kMiP6nZAhoF5MvC7qS1qPA7nRcr1GJolfkQC7/EAL4hdYejLtg==", "signatures": [{"sig": "MEUCIQD1nhWgXued/FF/yEesBSRjs+13s9N8xxYoWsFPsnRfIgIgT+mQBH2pEAYKfxuy4aL620BMaP3jcqF4lrXuMkxcKqE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0mP9CRA9TVsSAnZWagAAankP/0OPm01/xWiw09wYnvoL\nrp2+nW8dm+6YZC8spnvz5wAVSsMomy40ABZI63IuPMm9wopG3uSoNFooVbWZ\nPgre4kbgDjOL7tQgYIynHUvGxZ+jLQGiScddXWyapOnQNRwouyib2XshW4hG\nbjyaBT4tM5ixWWP6DA/yduR/Ty2oLTbJWWIi0YEkvcs986EH6JpD1R6Dsxa3\nWsdQ71y6STq9kLQ6CxKEmg14IawFfU8oZ+DbGBgMn4j23wTSPiso/tNML8hQ\n0r32DDoyjAPlSiKilxhHxOBaMG25nkqxrfOGK7DPMiJbO94ZT9QST6NXYOWp\n/s3K0yNbd+emzitWl2TdCLyy5vE6EqA3o3cdJYkoFR/xzZl2FCekt9mUXa10\nIeCvL3BNErn5w/dsd4VulY3TM0zGeHse6UQUnnougrvUqkXfJT2qRCnT6Ocf\ni5d46Zp1dGKKqf4jj1sTWr0/esDbADt6vife1EvjfXKp9QEIThOlwh/JxmYQ\np7aqIltOlLAcZuISoDFXjCuUAIj05p3riUySZjxtdRT8xRnc2lUXodMpS7YW\nZZYIrocZOC5545ByMBEKqzjLAKX0x8JqUH3SbatxvQCWewFHjjExO9LjBv/D\nlwadLxHHFABKn9KvG4UoKTGTMQ0ObsLooTu8AZNlt27La1aqJ0FitCNmwio5\no0Lg\r\n=DqjZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/cors"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.3", "_npmOperationalInternal": {"tmp": "tmp/cors_2.8.9_1607623677051_0.15737293878520386", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "528189d8f95b57800f8c2eb2ee496fed4c04d7ca809373df123b0831f65bb45a"}, "2.8.10": {"name": "@types/cors", "version": "2.8.10", "license": "MIT", "_id": "@types/cors@2.8.10", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/pluma", "name": "<PERSON>", "githubUsername": "pluma"}, {"url": "https://github.com/gtpan77", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "gtpan77"}], "dist": {"shasum": "61cc8469849e5bcdd0c7044122265c39cec10cf4", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-2.8.10.tgz", "fileCount": 4, "integrity": "sha512-C7srjHiVG3Ey1nR6d511dtDkCEjxuN9W1HWAEjGq8kpcwmNM6JJkpC0xvabM7BXTG2wDq8Eu33iH9aQKa7IvLQ==", "signatures": [{"sig": "MEUCIEkyDyfez67eeeyd2x6cUfN9NFXCyIa9IsC44mHkAevqAiEAyqTZsWuj0ab8RJXtTPoKEJbNBMpDBZN8WzPoRJxEP2s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4180, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgKM5jCRA9TVsSAnZWagAALD8P/0sSQ90QUWomqLiZpzan\nuxjo9S1L/Q0nUxAZ0xgUT4reBMbFt0q2auEp9aOrFLHVPeVXSFHZ5NMjNUJp\nB3J2x2A2Z70w8du+oxNA43+nU4DOznPsBehW86IDghohvLVe0wIMpjsL0Zc0\nsmjjMEwZ4wYDvXUFuvWWr2D9NxrpahohweCydFRe4TB3fSoga0gJNhdFAQlj\nEJk0I15zeRFtfGZr5ZWwvA8bcUZ01dBp19aZaQ3GeaHOaL9vY6Ut+HVta7Wr\ndGgzKZCqioUBe8wZXfSqdrc2Mz+lLwCqRZEuyndgsTX85EZsljCw0oQrjomf\nGihHuXDuSB2rXOS1ML8LJNTCFGfk75MHK7P+Nvsa0dwA6KdGjbhxcK6rBRlU\nhvxvNwZk9gwzBRbLdic00sbPh8Zt3g6Rdj1+Psf6uIXxdY4kLvnysYGL/vez\nt5o2a8KML2p+gOCG5HtE5nTze1WSYz/XBNfX5rLoTvmyD8ZXLw9NSllvv0KB\nJqcYGZ1DJslBxLhmtnghWcOYGP3aihyS1wtCoQUFYOHqJHI3d4r+Nax+hohK\nk3pYDR7cr9eJjH+Q8008OIJD/a1VyLDTxeNuWm6nFAnVauKm6ln0y5cjIncx\ntht7plhT38xf0WS0w9/qfYAGjhLkmN+SW+GplHHnAb6KQJca5xmd0AaQuntR\nAxm+\r\n=JOE4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/cors"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/cors_2.8.10_1613287010524_0.5470832237511527", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "211c1b2df33cf0e3701aaff798ccb87f0a813d275560208db31cee05483ae4d3"}, "2.8.11": {"name": "@types/cors", "version": "2.8.11", "license": "MIT", "_id": "@types/cors@2.8.11", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/pluma", "name": "<PERSON>", "githubUsername": "pluma"}, {"url": "https://github.com/gtpan77", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "gtpan77"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cors", "dist": {"shasum": "0bbd036cc6c8c63e0e5d64115fa9692eabb7eaa3", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-2.8.11.tgz", "fileCount": 4, "integrity": "sha512-64aQQZXPSo1fdLEE/utClOFVUqDUjyh5j3JorcCTlYQm4r5wsfggx6yhSY6hNudJLkbmIt+pO6xWyCnM0EQgPw==", "signatures": [{"sig": "MEYCIQDFMxDUJ9EnQJRWQfQQWTJ0mbouPmYjcOS3nRDytyBC0gIhAOyAxO4Zd+82x4tdVXgCDQJqEBdptMDvTAHJCeCVRxy1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6328, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5L7OCRA9TVsSAnZWagAAL1wP/2txWAuYbMRcAD2qbSjU\nJ0v8ZSMa7g29OTSKsr5JXANMZKL94lf1l8mOXrUz7xu1XsTzFNAXR3cBSWi1\nUWMXjhScvhcv/5tGXxI37AWykXF2XS+LDlQTkNECL1hd0FyWCIjgWHWGkoRF\njT94D3Za6J0FpjEdPdbIlrlqxpayD63CCxrh/h+X/9b9HT6C3sruLzlh5oSi\noiINm7LZ6lassJgPhmOFks2qKCRnsHhKjZoMs5ja4Ar9ZG8TDldqeKXDUD18\n9wsN8/X0wcuiduEgRylIc8RYwgmjE1iFhHRLCeoRzqjJ1sQv0xgksytSh2oD\nuS5EjaHOVMRAXzTfeRIdts2KwmsxPOlFOJZY5fTg8cBI8xgzSLyiH3hkAt2E\nf0YZ12KMZZVg2NmT7lv4fsbVVEgZJua9cJr8w0ahOQgVHPp96Wkq+s2g3Yfv\n3Bp4gKE5Fim7HIH+bIQ4VKJukRDn4D+kn94il1yBHreqFU1K64OYU5Ub0rMH\n6h6xoehxsLJe8w8i2Hx0Aw3YnMn1vJbuaJcTueEnRUHr2cxV8/DDuoXGjdpP\nc9qr7P87sg9VITroXoZrgUR3g3Ug+jK0MFMH/euBRuH88MbEeTMO7RMHT4+L\nsvp/gezqsWpCZlzkIZjgLSvq/ET/AXECPBVIprXGR+6iejdqFScYQaPMUcSA\nsL3p\r\n=hTRa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/cors"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/cors_2.8.11_1625603789805_0.5094611470300112", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d8927c540b44862c74d60cbb0b7b7f613567988fdedb216f5e53639dc4b5068b"}, "2.8.12": {"name": "@types/cors", "version": "2.8.12", "license": "MIT", "_id": "@types/cors@2.8.12", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/pluma", "name": "<PERSON>", "githubUsername": "pluma"}, {"url": "https://github.com/gtpan77", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "gtpan77"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cors", "dist": {"shasum": "6b2c510a7ad7039e98e7b8d3d6598f4359e5c080", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-2.8.12.tgz", "fileCount": 4, "integrity": "sha512-vt+kDhq/M2ayberEtJcIN/hxXy1Pk+59g2FV/ZQceeaTyCtCucjL2Q7FXlFjtWn4n15KCr1NE2lNNFhp0lEThw==", "signatures": [{"sig": "MEYCIQDHJqT8y+LvYtiGRcCYHcX2evdMm2M8zX40s1Me0aOOPwIhAK/Ja5OR6TwDLfaGm/BuO0Oz/gwj4QPlXQTS68INnQ0S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6348, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5/whCRA9TVsSAnZWagAAxBEQAJmdRpuhZ1Ri7afiaMaR\nQY87NnZudzAVmzR9eJ3srbs0Xg8AGRgtoG1MRwl8lHHqqZ4r55WOsvlTt0CI\nmypHLBi8dNP39W76ARQCgQTlqaDiPl1T1ab/O8AhYjb5tw0UMABfkiSv9+B6\ndL5BLca5yHrRCMdCu4JIvW2Z7MByc/0WLe1iILhDO8gq5Ch84lT6vNgtFDBO\n+euWjig+7fsfN1UfZUBTjJMJVxlFItr6j9N9FGXyzQv75fA6liUBPdYxuwsT\nMQuyii+PZ2lidvPnNn0lT2NsOTnVf1KtZBWKkJ5S1tqfggBqOhyaeqOlftJl\nUqQKHMIj6JhB1bdV7acNoD8pKZEqRGtRH5NMY2PAYXgqwafjYspX1u8ZcR9W\nMa7WwfiAWHycQtFF+pJNJ+rMDy8wtnoKPhpRAl4lxy2jTgnwpn6zkHlQyeCS\nGg+Sk0rpKiUu8nv+0YUQxgAXshiO3IMtZNCjWmMZoJFdJC5OdZVSRGi4c0iM\nXYI9CsVR7zWt7mAmqMAhKB7vRCRkbbhLiJdnG9c1uDEOfGxkeU6QDW4XbdML\nKuUiyNnNbjn950LkI+cw3Ka7xZKOHGslbT5bV+Lv15YvrsY/rvd5vvOs6EW2\nFVGLcKd/wnythByJGXx9VIXS4YYksnEUF67fiG/zJXLa3CUrYKakO6AHD7aD\nfbjO\r\n=10DH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/cors"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/cors_2.8.12_1625816097094_0.3885416023873247", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "53ea51a6543d58d3c1b9035a9c361d8f06d7be01973be2895820b2fb7ad9563a"}, "2.8.13": {"name": "@types/cors", "version": "2.8.13", "license": "MIT", "_id": "@types/cors@2.8.13", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/pluma", "name": "<PERSON>", "githubUsername": "pluma"}, {"url": "https://github.com/gtpan77", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "gtpan77"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cors", "dist": {"shasum": "b8ade22ba455a1b8cb3b5d3f35910fd204f84f94", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-2.8.13.tgz", "fileCount": 5, "integrity": "sha512-RG8AStHlUiV5ysZQKq97copd2UmVYw3/pRMLefISZ3S1hK104Cwm7iLQ3fTKx+lsUH2CE8FlLaYeEA2LSeqYUA==", "signatures": [{"sig": "MEUCIQCHtJBkcn6Up7ZqhsHQxC0hGNT7QzSx3CLiFE97teJJTAIgAwd78HpYm4JUvn5WLpMKTfSOgpsxUgol9Zakou1pJo4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6492, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjjZ6uACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/BQ/9GvasmWSnabDl2UnstTQT0rVbTBNAff7xnGJQJyTJ/d/AwHdg\r\nzBpsxUKFpH7xustbqNKGG9ifyEuyfHDnFcqJSKPh6VPGjqo1XCg7hb0TmPhq\r\nqSlwpvlLtCSgBxKrB74GCQ//ZLz4pnUd6MPD5iKf68dVfK9qa0O2B3mtlGG1\r\nUtyrdVReiKJJSxxsdfLyIItoZTsCdmt2/2sZCmq3i8NetSIxAxSG+lSpkbyE\r\nX9KJLoWf4Nxf6RL8BtFO1FpFR1tpD6jUnjZHdUJ1gDOa/l+fzpzyOpBnaTlH\r\n5X+hOifMkAAsLVet1iT5aZb935q80ZG3inXhr0rnjxq4cy4IQQqWJL2at92k\r\nU+We9GUt79lODAlPSGm5s/Io+AdEA+c2vGCViSGdcucJjM+l6h6p/nB8hrjP\r\n4D3If5nabBWd+kfetav8FnKwmUiEvqsRf9DqZZCwD51UEgUkFZv9Fe4Dg/PF\r\npnNQGq2zV0rTle41xaXV/5Pcvhhp5apgf6+Q7V3bqQkGcNTrVL/E6nknQhpP\r\nW4B9DW4TIZ4uttzIdGprLFDNqKrcim72Sd1/4ZvEhzpzfAfOBjwMe7uGjsUO\r\n22oXIXi8Z8AulcfviIsIJadIFjp7gme6p6h5FI7Qx57Z1z2PtnilTUMSc3Pk\r\nlUmMtjPY342ocaslo6QbcVXPBh3KYwoXAIY=\r\n=NcBt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/cors"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/cors_2.8.13_1670225582154_0.15977660529452997", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7979c95081a89c6479dfb9a9a432965c861677edef9443df6d4a871a5db924c4"}, "2.8.14": {"name": "@types/cors", "version": "2.8.14", "license": "MIT", "_id": "@types/cors@2.8.14", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/pluma", "name": "<PERSON>", "githubUsername": "pluma"}, {"url": "https://github.com/gtpan77", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "gtpan77"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cors", "dist": {"shasum": "94eeb1c95eda6a8ab54870a3bf88854512f43a92", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-2.8.14.tgz", "fileCount": 5, "integrity": "sha512-RXHUvNWYICtbP6s18PnOCaqToK8y14DnLd75c6HfyKf228dxy7pHNOQkxPtvXKp/hINFMDjbYzsj63nnpPMSRQ==", "signatures": [{"sig": "MEYCIQChuc77IZldAU3tsqjdH+Hl3d2qwoAAUY2AvCSfJzRmEwIhAM3oAFjneG4iSSlZ/lj+HHeXiQqPZ2Ue+0ceBH8lH+7u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6514}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/cors"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/cors_2.8.14_1693844545893_0.14730105394376403", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e4fbb8898e768e27ad521fb9c66aa964ab5cf7859301d685017386ebd41c2d3e"}, "2.8.15": {"name": "@types/cors", "version": "2.8.15", "license": "MIT", "_id": "@types/cors@2.8.15", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/pluma", "name": "<PERSON>", "githubUsername": "pluma"}, {"url": "https://github.com/gtpan77", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "gtpan77"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cors", "dist": {"shasum": "eb143aa2f8807ddd78e83cbff141bbedd91b60ee", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-2.8.15.tgz", "fileCount": 5, "integrity": "sha512-n91JxbNLD8eQIuXDIChAN1tCKNWCEgpceU9b7ZMbFA+P+Q4yIeh80jizFLEvolRPc1ES0VdwFlGv+kJTSirogw==", "signatures": [{"sig": "MEUCIQCyiDb71O6jwxkZIZjHV5zM/SgrP0HuZluzKOeCsASzpAIgSHofYRTzc+njCR/viFO5jlx5E6IOQKiXfmDhOT+4kp4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5904}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/cors"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/cors_2.8.15_1697588915244_0.8875218876559501", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c40386a694c9d1bf849c5628384164926908b523da932e0e9751c2efe774c486"}, "2.8.16": {"name": "@types/cors", "version": "2.8.16", "license": "MIT", "_id": "@types/cors@2.8.16", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/pluma", "name": "<PERSON>", "githubUsername": "pluma"}, {"url": "https://github.com/gtpan77", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "gtpan77"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cors", "dist": {"shasum": "a24bf65acd216c078890ca6ceb91e672adb158e7", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-2.8.16.tgz", "fileCount": 5, "integrity": "sha512-Trx5or1Nyg1Fq138PCuWqoApzvoSLWzZ25ORBiHMbbUT42g578lH1GT4TwYDbiUOLFuDsCkfLneT2105fsFWGg==", "signatures": [{"sig": "MEQCIBjmP+p37BZ11DJvroGv5vt0EAqr/dnwKm1XsEIC+2QkAiAewrYVrieKKIAQxtkXQSjkguJ/6Irqlm9PdWlXQELgAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5904}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/cors"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/cors_2.8.16_1699319304745_0.6205636476608405", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f713b0f644442bb98a880118963bd7fa79cd1d5e02b97cee11f7e0e25ffb5987"}, "2.8.17": {"name": "@types/cors", "version": "2.8.17", "license": "MIT", "_id": "@types/cors@2.8.17", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/pluma", "name": "<PERSON>", "githubUsername": "pluma"}, {"url": "https://github.com/gtpan77", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "gtpan77"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cors", "dist": {"shasum": "5d718a5e494a8166f569d986794e49c48b216b2b", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-2.8.17.tgz", "fileCount": 5, "integrity": "sha512-8CGDvrBj1zgo2qE+oS3pOCyYNqCPryMWY2bGfwA0dcfopWGgxs+78df0Rs3rc9THP4JkOhLsAa+15VdpAqkcUA==", "signatures": [{"sig": "MEUCIQDYUWgxpK870Irqot3K/IrqGV/sZmY1riD38ICaD2Cx5gIgewWEvfNuTJVFi6I56sD8REQX0aheLa06cfjP3D2XwGw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5910}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/cors"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/cors_2.8.17_1700524450863_0.6815335646175904", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "04d506dbb23d9e7a142bfb227d59c61102abec00fb40694bb64a8d9fe1f1a3a1"}, "2.8.18": {"name": "@types/cors", "version": "2.8.18", "license": "MIT", "_id": "@types/cors@2.8.18", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/pluma", "name": "<PERSON>", "githubUsername": "pluma"}, {"url": "https://github.com/gtpan77", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "gtpan77"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cors", "dist": {"shasum": "101e033b3ca06695f3d73c587cd7f9eb348135d1", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-2.8.18.tgz", "fileCount": 5, "integrity": "sha512-nX3d0sxJW41CqQvfOzVG1NCTXfFDrDWIghCZncpHeWlVFd81zxB/DLhg7avFg6eHLCRX7ckBmoIIcqa++upvJA==", "signatures": [{"sig": "MEUCIQD0MrIO0E2r7agAZWtU+lC+2BRMaWHsiPlpa1Jy1Tt5rwIgSjiAQt47G8vt5bUU8UvUCOQ2xPsPT2gInQx9OZkB1e0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5936}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/cors"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/cors_2.8.18_1746702216107_0.8498291974140422", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "bb827b9078703536a36eec6c6a0fff336bf3c2bca470df69b51c6402fd1c2316"}, "2.8.19": {"name": "@types/cors", "version": "2.8.19", "license": "MIT", "_id": "@types/cors@2.8.19", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/pluma", "name": "<PERSON>", "githubUsername": "pluma"}, {"url": "https://github.com/gtpan77", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "gtpan77"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cors", "dist": {"shasum": "d93ea2673fd8c9f697367f5eeefc2bbfa94f0342", "tarball": "https://registry.npmjs.org/@types/cors/-/cors-2.8.19.tgz", "fileCount": 5, "integrity": "sha512-mFNylyeyqN93lfe/9CSxOGREz8cpzAhH+E93xJ4xWQf62V8sQ/24reV2nyzUWM6H6Xji+GGHpkbLe7pVoUEskg==", "signatures": [{"sig": "MEUCICkxnSIYwK0gj9jRi4EpogplO+PsMpiJCNQjdM5ObuAyAiEAgZd8hHJk7UcYH7nxw3/8PAJhDb98zmecY8grW/BH/JM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6148}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/cors"}, "description": "TypeScript definitions for cors", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/cors_2.8.19_1749262586170_0.6836294892668815", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "a090e558c5f443573318c2955deecddc840bd8dfaac7cdedf31c7f6ede8d0b47"}}, "time": {"created": "2016-05-17T04:42:14.431Z", "modified": "2025-06-07T02:16:33.987Z", "0.0.16-alpha": "2016-05-17T04:42:14.431Z", "0.0.17-alpha": "2016-05-19T20:35:23.455Z", "0.0.22-alpha": "2016-05-20T19:23:59.954Z", "0.0.23-alpha": "2016-05-25T04:42:05.663Z", "0.0.24-alpha": "2016-07-01T19:10:19.659Z", "0.0.25-alpha": "2016-07-01T22:30:41.510Z", "0.0.26-alpha": "2016-07-02T02:15:38.099Z", "0.0.27-alpha": "2016-07-04T00:00:54.811Z", "0.0.28-alpha": "2016-07-07T17:08:18.846Z", "0.0.29": "2016-07-14T14:21:26.377Z", "0.0.30": "2016-08-02T15:49:59.564Z", "0.0.31": "2016-08-19T15:22:59.608Z", "0.0.32": "2016-08-25T18:37:45.783Z", "0.0.33": "2016-09-19T16:34:45.937Z", "2.8.0": "2017-01-04T21:17:25.060Z", "2.8.1": "2017-03-11T00:14:34.428Z", "2.8.2": "2017-11-08T22:17:19.802Z", "2.8.3": "2017-11-10T22:59:29.357Z", "2.8.4": "2018-04-23T15:07:39.108Z", "2.8.5": "2019-04-25T22:24:45.352Z", "2.8.6": "2019-08-19T00:55:41.063Z", "2.8.7": "2020-07-31T20:42:29.623Z", "2.8.8": "2020-10-10T05:22:32.895Z", "2.8.9": "2020-12-10T18:07:57.263Z", "2.8.10": "2021-02-14T07:16:50.742Z", "2.8.11": "2021-07-06T20:36:29.929Z", "2.8.12": "2021-07-09T07:34:57.199Z", "2.8.13": "2022-12-05T07:33:02.313Z", "2.8.14": "2023-09-04T16:22:26.104Z", "2.8.15": "2023-10-18T00:28:35.489Z", "2.8.16": "2023-11-07T01:08:25.117Z", "2.8.17": "2023-11-20T23:54:11.024Z", "2.8.18": "2025-05-08T11:03:36.297Z", "2.8.19": "2025-06-07T02:16:26.348Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cors", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/cors"}, "description": "TypeScript definitions for cors", "contributors": [{"url": "https://github.com/pluma", "name": "<PERSON>", "githubUsername": "pluma"}, {"url": "https://github.com/gtpan77", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "gtpan77"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": "", "users": {"morogasper": true}}