{"_id": "wrappy", "_rev": "17-c90b970a11c2f23d934921de964dbde7", "name": "wrappy", "description": "Callback wrapping utility", "dist-tags": {"latest": "1.0.2"}, "versions": {"1.0.0": {"name": "wrappy", "version": "1.0.0", "description": "Callback wrapping utility", "main": "wrappy.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^0.4.12"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "https://github.com/npm/wrappy"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "bugs": {"url": "https://github.com/npm/wrappy/issues"}, "homepage": "https://github.com/npm/wrappy", "gitHead": "2a0cf2a39be2ebb4a53f2f87db739d98f2a5ebc5", "_id": "wrappy@1.0.0", "_shasum": "8aae4fc6b4cd6be32a4553985bcf32b3ee131e4e", "_from": ".", "_npmVersion": "2.0.0", "_nodeVersion": "0.10.31", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "8aae4fc6b4cd6be32a4553985bcf32b3ee131e4e", "tarball": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.0.tgz", "integrity": "sha512-3y8PlpIQuv3/g9rN12ffm6FrWo+fCLlt8mtAruKSVgnPgdV4SoxOF8qmJ+6BXjsfDFe/EMUGFWjCu2dImNHjBQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDWrRxKStHgOvqG0fa7ESlcj8Il3QEuGl9fyomDlesSbwIgICeFpe4kqnMG6kdfOCFsncQzN1SjfME8H+6yXea2VXo="}]}}, "1.0.1": {"name": "wrappy", "version": "1.0.1", "description": "Callback wrapping utility", "main": "wrappy.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^0.4.12"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "https://github.com/npm/wrappy"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "bugs": {"url": "https://github.com/npm/wrappy/issues"}, "homepage": "https://github.com/npm/wrappy", "gitHead": "006a8cbac6b99988315834c207896eed71fd069a", "_id": "wrappy@1.0.1", "_shasum": "1e65969965ccbc2db4548c6b84a6f2c5aedd4739", "_from": ".", "_npmVersion": "2.0.0", "_nodeVersion": "0.10.31", "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "dist": {"shasum": "1e65969965ccbc2db4548c6b84a6f2c5aedd4739", "tarball": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.1.tgz", "integrity": "sha512-42h1d25nW6G/N7l16Oz4vqCOLIFobFBOwZrBYlCxJ/QuS2o1Gdn1PzSoiYndbnL9rgGIGZ6Qn09AIpyhrkepfw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDn2Qrc+DAxw+20ABU0tpbXyFWAazK7SOB9mnc56inccQIgAc9xuBXOmE37NGBaAA1HGv9RoNpukosY3Fi+QuvBoW4="}]}}, "1.0.2": {"name": "wrappy", "version": "1.0.2", "description": "Callback wrapping utility", "main": "wrappy.js", "files": ["wrappy.js"], "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^2.3.1"}, "scripts": {"test": "tap --coverage test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/npm/wrappy.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "bugs": {"url": "https://github.com/npm/wrappy/issues"}, "homepage": "https://github.com/npm/wrappy", "gitHead": "71d91b6dc5bdeac37e218c2cf03f9ab55b60d214", "_id": "wrappy@1.0.2", "_shasum": "b5243d8f3ec1aa35f1364605bc0d1036e30ab69f", "_from": ".", "_npmVersion": "3.9.1", "_nodeVersion": "5.10.1", "_npmUser": {"name": "zkat", "email": "<EMAIL>"}, "dist": {"shasum": "b5243d8f3ec1aa35f1364605bc0d1036e30ab69f", "tarball": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHtPpKlaDF3QN9vNEImDylgjhVgE1cPX5dxgADkO3cbOAiBDRht6eiKjXoYA0jCizl4yJnAJLrUoRkZtth0Uw0e5aQ=="}]}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "zkat", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/wrappy-1.0.2.tgz_1463527848281_0.037129373755306005"}}, "0.0.0": {"name": "wrappy", "version": "0.0.0", "publishConfig": {"tag": "testing"}, "description": "Callback wrapping utility", "main": "wrappy.js", "directories": {"test": "test"}, "dependencies": {}, "devDependencies": {"tap": "^1.2.0"}, "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/npm/wrappy.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "license": "ISC", "bugs": {"url": "https://github.com/npm/wrappy/issues"}, "homepage": "https://github.com/npm/wrappy", "readme": "# wrappy\n\nCallback wrapping utility\n\n## USAGE\n\n```javascript\nvar wrappy = require(\"wrappy\")\n\n// var wrapper = wrappy(wrapperFunction)\n\n// make sure a cb is called only once\n// See also: http://npm.im/once for this specific use case\nvar once = wrappy(function (cb) {\n  var called = false\n  return function () {\n    if (called) return\n    called = true\n    return cb.apply(this, arguments)\n  }\n})\n\nfunction printBoo () {\n  console.log('boo')\n}\n// has some rando property\nprintBoo.iAmBooPrinter = true\n\nvar onlyPrintOnce = once(printBoo)\n\nonlyPrintOnce() // prints 'boo'\nonlyPrintOnce() // does nothing\n\n// random property is retained!\nassert.equal(onlyPrintOnce.iAmBooPrinter, true)\n```\n", "readmeFilename": "README.md", "gitHead": "6ff331c0ea9d4525d17d12fb5a1c7933c8d84c20", "_id": "wrappy@0.0.0", "_nodeVersion": "16.0.0", "_npmVersion": "7.10.0", "dist": {"integrity": "sha512-LqL69QCiUu9q/qMG/a7l9bsJJQBN0QKOFx2l8RpT1NOu3v3BYxTiRVC0RkbW+8KlLCYtwksWopVWH0dY2r0Kug==", "shasum": "adf927968d3c4aa3e5cac7a47e714086086dcc25", "tarball": "https://registry.npmjs.org/wrappy/-/wrappy-0.0.0.tgz", "fileCount": 6, "unpackedSize": 4203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgggQOCRA9TVsSAnZWagAAReQQAKQzNIf0bHTunqO7/g8C\nKHEM0IjZqNZcP47lkWZ7YLq0uy2616wMzgW3+fFb1yKtispVKRMNHlDt1Jkl\n8+z0MkqsCHdm2A8/cRuaXpBwT1kbPJkHlnOboWq/ghUAiKznUWJKr0ENoBzQ\nOa6RgMb5M/Twwi/KFeaxltGw5BTx66CzJxHkS23iL6wjAIHQk0SMnkbFyHuh\n6eysYcoMt4+1VcwbGiXvxya5oiihNkr4G78R/2ZLNL2pQQk5b7pySHHMqwFR\nJ3YM91ZE04oCaz7A9rl9KPFgVdwlrNCT0OcEvs95dWH1rvioSqmgrEsDgaXg\nWvui5X3XGZSTIDskAsFBVKV0/fro6nyiKD0F5ysSXeGA6id7Gkvtm+dY//q0\nZF6TAS/GxyASjPccvVaomqQSUxtF2/1/Yp8AC0Z8ZQ5tZRP9J67cxWndauA+\nDk3RKMpLw7WhEnREPYAQ0VEFiVsYIl4TSmv59OYJtW2m0xJ1uvzCQ/EuQosL\n7uhoDp9tiJpuM7t/TYZka/UoK/wt8agauqg/+lbfKJj9Qxc1YYahdn3i2rkv\nQtCrVw0g8octR4oKQPgWQ3fJ1xfxG6g1wzgOOOH3TUDYNMayLusXYWvg1ULR\nuLLRtQyuoxEqKrKjCnmIF9Rs2akdS9z646Y/xNb0EgN5e287mOBxZt9TXDuU\n5Kh3\r\n=LoNR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF6mVzm0s41VKrmDQ1WYvWoj/topP/d34Ij07TY3IOWjAiBhF8n0zYChG+dFgTp0QFZC2w1Q85OVuyvucAnEw+ts8A=="}]}, "_npmUser": {"name": "isaacs", "email": "<EMAIL>"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/wrappy_0.0.0_1619133453839_0.3872619564603934"}, "_hasShrinkwrap": false}}, "readme": "", "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}], "time": {"modified": "2023-06-22T16:34:09.617Z", "created": "2014-09-18T22:59:23.410Z", "1.0.0": "2014-09-18T22:59:23.410Z", "1.0.1": "2014-09-18T23:13:15.838Z", "1.0.2": "2016-05-17T23:30:52.415Z", "0.0.0": "2021-04-22T23:17:34.192Z"}, "homepage": "https://github.com/npm/wrappy", "repository": {"type": "git", "url": "git+https://github.com/npm/wrappy.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/npm/wrappy/issues"}, "license": "ISC", "readmeFilename": "", "users": {"program247365": true, "klap-webdevelopment": true, "mojaray2k": true, "flumpus-dev": true}}