{"_id": "error-ex", "_rev": "26-f3ffeb11df26b30d0c56d6b4251aa118", "name": "error-ex", "description": "Easy error subclassing and stack customization", "dist-tags": {"latest": "1.3.2"}, "versions": {"0.1.0": {"name": "error-ex", "description": "Easy error subclassing and stack customization", "version": "0.1.0", "author": {"name": "Qi<PERSON>", "url": "http://github.com/qix-"}, "keywords": ["error", "errors", "extending", "extension", "stack", "custom"], "license": "MIT", "scripts": {"pretest": "xo", "test": "mocha --compilers coffee:coffee-script/register"}, "repository": {"type": "git", "url": "git+https://github.com/qix-/node-error-ex.git"}, "devDependencies": {"coffee-script": "^1.9.3", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "mocha": "^2.2.5", "should": "^7.0.1", "xo": "^0.6.1"}, "gitHead": "8b6ab032809e480b2a2eda5107e29866af3c4f78", "bugs": {"url": "https://github.com/qix-/node-error-ex/issues"}, "homepage": "https://github.com/qix-/node-error-ex#readme", "_id": "error-ex@0.1.0", "_shasum": "3cd01058eee2db17bbe0e2ef2e2a463a1eb62fb5", "_from": ".", "_npmVersion": "3.2.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "3cd01058eee2db17bbe0e2ef2e2a463a1eb62fb5", "tarball": "https://registry.npmjs.org/error-ex/-/error-ex-0.1.0.tgz", "integrity": "sha512-s1qFYNJIRtL3U/PW3kqsAKbOqlYNqVE2RfPrHyJhGXrWDKx56O7JW+DGZRM1eXv4HOvBHoYquX1jWZggit7alQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC461UL0XYBiX5n8YmAoqrHlDqZM1GMwhdE2NJEJ3RuGwIhAKo+f29CQ0kYThA+IJHTXI4upMqCfX/amzVoKBrd1gWb"}]}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "error-ex", "description": "Easy error subclassing and stack customization", "version": "0.2.0", "author": {"name": "Qi<PERSON>", "url": "http://github.com/qix-"}, "keywords": ["error", "errors", "extending", "extension", "stack", "custom"], "license": "MIT", "scripts": {"pretest": "xo", "test": "mocha --compilers coffee:coffee-script/register"}, "repository": {"type": "git", "url": "git+https://github.com/qix-/node-error-ex.git"}, "devDependencies": {"coffee-script": "^1.9.3", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "mocha": "^2.2.5", "should": "^7.0.1", "xo": "^0.6.1"}, "gitHead": "54996b917669be65e163a9bba646ed7dd376b246", "bugs": {"url": "https://github.com/qix-/node-error-ex/issues"}, "homepage": "https://github.com/qix-/node-error-ex#readme", "_id": "error-ex@0.2.0", "_shasum": "2264df750573399c5e0206a191fc91fa44f7059c", "_from": ".", "_npmVersion": "3.2.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "2264df750573399c5e0206a191fc91fa44f7059c", "tarball": "https://registry.npmjs.org/error-ex/-/error-ex-0.2.0.tgz", "integrity": "sha512-vt9po/TfRMTkDgpgULcfA7At7hVgVujGFjN7BQZB29uSG1EuDojwLkMLgcEn4uTgYKyWLb8fbiZdLCyYO4J0yA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGiDBLaTyUPvZF+bw2n2467p/nSmAPtMckUgGWIxU7orAiEA5xk2mDhe+fWoYLi61gN0s9k3kqV4rGQNvDcOKUK4BXk="}]}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}], "directories": {}}, "0.2.1": {"name": "error-ex", "description": "Easy error subclassing and stack customization", "version": "0.2.1", "author": {"name": "Qi<PERSON>", "url": "http://github.com/qix-"}, "keywords": ["error", "errors", "extending", "extension", "stack", "custom"], "license": "MIT", "scripts": {"pretest": "xo", "test": "mocha --compilers coffee:coffee-script/register"}, "repository": {"type": "git", "url": "git+https://github.com/qix-/node-error-ex.git"}, "devDependencies": {"coffee-script": "^1.9.3", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "mocha": "^2.2.5", "should": "^7.0.1", "xo": "^0.6.1"}, "gitHead": "cb255e402ddc59c96c13d1afbc1197ad3a057538", "bugs": {"url": "https://github.com/qix-/node-error-ex/issues"}, "homepage": "https://github.com/qix-/node-error-ex#readme", "_id": "error-ex@0.2.1", "_shasum": "56f2b5d07fc98f165a98caddd5e9ea9ffa66e66d", "_from": ".", "_npmVersion": "3.2.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "56f2b5d07fc98f165a98caddd5e9ea9ffa66e66d", "tarball": "https://registry.npmjs.org/error-ex/-/error-ex-0.2.1.tgz", "integrity": "sha512-Kk6VRfwkY7X+Lg8ZRRm0g8fl4iMlBvlYrvGWIn3VrFLiyp0T5HJVpEUGQl0CZRrqCFk1sT+XAUHlGVWjv1WmHw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG7YJXpE2so+8hMaTjXMXSd+l1YoL/LYn1Vrb7U6QuqzAiAiL17JBWDRksWCSrVTNY/Rq5hAUYaTllUY6+bgUgks7A=="}]}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}], "directories": {}}, "0.2.2": {"name": "error-ex", "description": "Easy error subclassing and stack customization", "version": "0.2.2", "author": {"name": "Qi<PERSON>", "url": "http://github.com/qix-"}, "keywords": ["error", "errors", "extending", "extension", "stack", "custom"], "license": "MIT", "scripts": {"pretest": "xo", "test": "mocha --compilers coffee:coffee-script/register"}, "repository": {"type": "git", "url": "git+https://github.com/qix-/node-error-ex.git"}, "devDependencies": {"coffee-script": "^1.9.3", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "mocha": "^2.2.5", "should": "^7.0.1", "xo": "^0.6.1"}, "gitHead": "2bfe0019ead4ea5109fccf78b28106864b9007b5", "bugs": {"url": "https://github.com/qix-/node-error-ex/issues"}, "homepage": "https://github.com/qix-/node-error-ex#readme", "_id": "error-ex@0.2.2", "_shasum": "6b46fd5d5ae9e2378290f1dfd6fbc630690240e9", "_from": ".", "_npmVersion": "3.2.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "6b46fd5d5ae9e2378290f1dfd6fbc630690240e9", "tarball": "https://registry.npmjs.org/error-ex/-/error-ex-0.2.2.tgz", "integrity": "sha512-M3SWtZ+FQViz72wMf6kDLu5I+FMwGCZGhDA2613HFY7TyaDpCZ//RTROApcLUpQdrNr1bXuQDd40EfO4NRzspA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBBaiUKAq+gcN+hb69R8ahx4DRCc6sG8T9gKYBhR498VAiEA55nlnTKPG0EicobUxE8miGK/nEVA9WA0VZ1UTrnYmGk="}]}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}], "directories": {}}, "0.2.3": {"name": "error-ex", "description": "Easy error subclassing and stack customization", "version": "0.2.3", "author": {"name": "Qi<PERSON>", "url": "http://github.com/qix-"}, "keywords": ["error", "errors", "extending", "extension", "stack", "custom"], "license": "MIT", "scripts": {"pretest": "xo", "test": "mocha --compilers coffee:coffee-script/register"}, "repository": {"type": "git", "url": "git+https://github.com/qix-/node-error-ex.git"}, "devDependencies": {"coffee-script": "^1.9.3", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "mocha": "^2.2.5", "should": "^7.0.1", "xo": "^0.6.1"}, "gitHead": "1fff79e107fbb07f27519f07a33019fbeb222939", "bugs": {"url": "https://github.com/qix-/node-error-ex/issues"}, "homepage": "https://github.com/qix-/node-error-ex#readme", "_id": "error-ex@0.2.3", "_shasum": "f07f1a6ffc0b8fc099bd576e95e01b5b04c9d755", "_from": ".", "_npmVersion": "3.2.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "f07f1a6ffc0b8fc099bd576e95e01b5b04c9d755", "tarball": "https://registry.npmjs.org/error-ex/-/error-ex-0.2.3.tgz", "integrity": "sha512-xqVUHv+ppSSYWdtPcxGaDHlYuESOzopKhDRwqXRj3oxKtzFqzxn4LSdOXzxyQFyq/Us5AUxzqm2QD0Hd2xCd4w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICNDaj6cQvwnaPEDrO6NjK99HzU6VY5e806EKlxsY2xdAiEAxl/IWtVlbBlS5wlhZIsVeWXbPop/vfIXRMMJPN5DvRw="}]}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}], "directories": {}}, "0.2.4": {"name": "error-ex", "description": "Easy error subclassing and stack customization", "version": "0.2.4", "author": {"name": "Qi<PERSON>", "url": "http://github.com/qix-"}, "keywords": ["error", "errors", "extending", "extension", "stack", "custom"], "license": "MIT", "scripts": {"pretest": "xo", "test": "mocha --compilers coffee:coffee-script/register"}, "repository": {"type": "git", "url": "git+https://github.com/qix-/node-error-ex.git"}, "devDependencies": {"coffee-script": "^1.9.3", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "mocha": "^2.2.5", "should": "^7.0.1", "xo": "^0.6.1"}, "gitHead": "9f22eb9296b06aa0e5c654390c2fd80030f58c22", "bugs": {"url": "https://github.com/qix-/node-error-ex/issues"}, "homepage": "https://github.com/qix-/node-error-ex#readme", "_id": "error-ex@0.2.4", "_shasum": "558284d2e421e0865e70530037816872dd713803", "_from": ".", "_npmVersion": "3.2.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "558284d2e421e0865e70530037816872dd713803", "tarball": "https://registry.npmjs.org/error-ex/-/error-ex-0.2.4.tgz", "integrity": "sha512-Xtq7eJN0MxBPOwgkUbSCmlhMDaSI4pO129VU9cRbRXY5AEdyAKayL0wrFZPYZEa27CyP9JFTC1Ql3p+UoExwPw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICZnAvqCle4PlycAp0QeOSziznJhVvp09+2ZVHl8Yp4VAiAoAIKmyQnIBEcZB6LLohD99OIlCtlAe2erZIi4L7jIgg=="}]}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}], "directories": {}}, "0.3.0": {"name": "error-ex", "description": "Easy error subclassing and stack customization", "version": "0.3.0", "author": {"name": "Qi<PERSON>", "url": "http://github.com/qix-"}, "keywords": ["error", "errors", "extending", "extension", "stack", "custom"], "license": "MIT", "scripts": {"pretest": "xo", "test": "mocha --compilers coffee:coffee-script/register"}, "repository": {"type": "git", "url": "git+https://github.com/qix-/node-error-ex.git"}, "devDependencies": {"coffee-script": "^1.9.3", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "mocha": "^2.2.5", "should": "^7.0.1", "xo": "^0.6.1"}, "gitHead": "4c63642ce7acf53d2f24e74729e643e81a611fe8", "bugs": {"url": "https://github.com/qix-/node-error-ex/issues"}, "homepage": "https://github.com/qix-/node-error-ex#readme", "_id": "error-ex@0.3.0", "_shasum": "1c84bf75503529d57d4eff6b7698754d648378f5", "_from": ".", "_npmVersion": "3.2.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "1c84bf75503529d57d4eff6b7698754d648378f5", "tarball": "https://registry.npmjs.org/error-ex/-/error-ex-0.3.0.tgz", "integrity": "sha512-QlEOjkqIZvQL/T3m+GW5hhPrIOjEC78+469aa2PcVd9UMXKp3n3YDKvCsCy/4Yu+ChU8n9VcxRbmbjMig+vpkw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGIdfbI1EsBKx+aA58mWXzSeqdEtOSvWWUzCgA2pXETgIgR/p3X5sJBKulivGupJEGWwGjRv886BqqxLD+17liUJ4="}]}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}], "directories": {}}, "0.3.1": {"name": "error-ex", "description": "Easy error subclassing and stack customization", "version": "0.3.1", "author": {"name": "Qi<PERSON>", "url": "http://github.com/qix-"}, "keywords": ["error", "errors", "extending", "extension", "stack", "custom"], "license": "MIT", "scripts": {"pretest": "xo", "test": "mocha --compilers coffee:coffee-script/register"}, "repository": {"type": "git", "url": "git+https://github.com/qix-/node-error-ex.git"}, "devDependencies": {"coffee-script": "^1.9.3", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "mocha": "^2.2.5", "should": "^7.0.1", "xo": "^0.6.1"}, "gitHead": "94df4c3001db532cf74db4aa02914a7e21400577", "bugs": {"url": "https://github.com/qix-/node-error-ex/issues"}, "homepage": "https://github.com/qix-/node-error-ex#readme", "_id": "error-ex@0.3.1", "_shasum": "44d05d1815a8c98d72973463fffea92779639ea8", "_from": ".", "_npmVersion": "3.2.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "44d05d1815a8c98d72973463fffea92779639ea8", "tarball": "https://registry.npmjs.org/error-ex/-/error-ex-0.3.1.tgz", "integrity": "sha512-FkbpF7g+wwPUlEx7A/vRCBPMooddydicTYC7iV3UdW+bdl792yl+ekzlzQf92beEbESLVNbxT3sBjZ2y0lM33g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDkA/s6ogvnrgmkp+cSc3n+jR9tZ/xrergh50gu/ACsoQIhAMzvi2G9QzHAtRN+CBxyOS96YdJw6GcHJSA/smz2OPd3"}]}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}], "directories": {}}, "1.0.0": {"name": "error-ex", "description": "Easy error subclassing and stack customization", "version": "1.0.0", "author": {"name": "Qi<PERSON>", "url": "http://github.com/qix-"}, "keywords": ["error", "errors", "extending", "extension", "stack", "custom"], "license": "MIT", "scripts": {"pretest": "xo", "test": "mocha --compilers coffee:coffee-script/register"}, "repository": {"type": "git", "url": "git+https://github.com/qix-/node-error-ex.git"}, "devDependencies": {"coffee-script": "^1.9.3", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "mocha": "^2.2.5", "should": "^7.0.1", "xo": "^0.6.1"}, "gitHead": "f52949dc3bfea3f4879740bb1eb8bb5666c79c2c", "bugs": {"url": "https://github.com/qix-/node-error-ex/issues"}, "homepage": "https://github.com/qix-/node-error-ex#readme", "_id": "error-ex@1.0.0", "_shasum": "5c533dc618311a1ba6a455eb2fcc201c01441b19", "_from": ".", "_npmVersion": "3.2.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "5c533dc618311a1ba6a455eb2fcc201c01441b19", "tarball": "https://registry.npmjs.org/error-ex/-/error-ex-1.0.0.tgz", "integrity": "sha512-8mXdsWDFPKcVsF0qKsHVBdL8C4WTWi8aNGuqBTUkUu7uhsb8od+bdA065SKDY0lp6w+2M7ZgLo7X4VebyOVHNQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDCDR/W+Z0WEUrd3tk0jZtkMlC0npvXhCsYF3x7JVpPgwIgfbuvlhGmgdkqQW52H5CGeZ3LWhIEAIyQTigw47iyu4c="}]}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}], "directories": {}}, "1.1.0": {"name": "error-ex", "description": "Easy error subclassing and stack customization", "version": "1.1.0", "author": {"name": "Qi<PERSON>", "url": "http://github.com/qix-"}, "keywords": ["error", "errors", "extending", "extension", "stack", "custom"], "license": "MIT", "scripts": {"pretest": "xo", "test": "mocha --compilers coffee:coffee-script/register"}, "repository": {"type": "git", "url": "git+https://github.com/qix-/node-error-ex.git"}, "devDependencies": {"coffee-script": "^1.9.3", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "mocha": "^2.2.5", "should": "^7.0.1", "xo": "^0.6.1"}, "gitHead": "ef4a284a9f8598e27af8445946247292f02d071b", "bugs": {"url": "https://github.com/qix-/node-error-ex/issues"}, "homepage": "https://github.com/qix-/node-error-ex#readme", "_id": "error-ex@1.1.0", "_shasum": "fd5692de4416e56ca6f457ad984adab91ecffd18", "_from": ".", "_npmVersion": "3.2.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "fd5692de4416e56ca6f457ad984adab91ecffd18", "tarball": "https://registry.npmjs.org/error-ex/-/error-ex-1.1.0.tgz", "integrity": "sha512-l26D6mi/NDm3Tcusvpx7UmyFlSC2lP8dMZRQ6r9LE8ZNdGebm4i1rVuP31Vf6pw/hGPrdDgKRjLT/hOxF+IQ9w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD3TaYITxEeQoKyl97C8aK0f8MUOtvo3IvmpvHVwn8RJAIgWBOXFo5H+YNU9bDvgqBQZzel1vrRarR7P+7sEaWoHlE="}]}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}], "directories": {}}, "1.1.1": {"name": "error-ex", "description": "Easy error subclassing and stack customization", "version": "1.1.1", "author": {"name": "Qi<PERSON>", "url": "http://github.com/qix-"}, "keywords": ["error", "errors", "extend", "extending", "extension", "subclass", "stack", "custom"], "license": "MIT", "scripts": {"pretest": "xo", "test": "mocha --compilers coffee:coffee-script/register"}, "repository": {"type": "git", "url": "git+https://github.com/qix-/node-error-ex.git"}, "files": ["index.js"], "devDependencies": {"coffee-script": "^1.9.3", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "mocha": "^2.2.5", "should": "^7.0.1", "xo": "^0.7.1"}, "gitHead": "d85f47b6fd35041bfcd08e23fc9a0f0581b629aa", "bugs": {"url": "https://github.com/qix-/node-error-ex/issues"}, "homepage": "https://github.com/qix-/node-error-ex#readme", "_id": "error-ex@1.1.1", "_shasum": "276207351e7c722dc8e8a78e63ecb9725bf41a31", "_from": ".", "_npmVersion": "3.2.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "276207351e7c722dc8e8a78e63ecb9725bf41a31", "tarball": "https://registry.npmjs.org/error-ex/-/error-ex-1.1.1.tgz", "integrity": "sha512-7jKIw84dBP3Rwfzgh1ChcCGs7hdmY97wLT3PifGC5GK/WLozvaEtNxsyufpdGKky1myzvG1i7NoJ55ESzKcT6A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGH1GUqVPtwNcpTX27ouoozp+PqhhbUIUR+xgPxT24WHAiBXkCkbtFa76JzOrLJV/EUx/OmnJi1nRaGjgOoOVXGhCg=="}]}, "maintainers": [{"name": "qix", "email": "<EMAIL>"}], "directories": {}}, "1.1.2": {"name": "error-ex", "description": "Easy error subclassing and stack customization", "version": "1.1.2", "maintainers": [{"name": "qix", "email": "<EMAIL>"}], "keywords": ["error", "errors", "extend", "extending", "extension", "subclass", "stack", "custom"], "license": "MIT", "scripts": {"pretest": "xo", "test": "mocha --compilers coffee:coffee-script/register"}, "repository": {"type": "git", "url": "git+https://github.com/qix-/node-error-ex.git"}, "files": ["index.js"], "devDependencies": {"coffee-script": "^1.9.3", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "mocha": "^2.2.5", "should": "^7.0.1", "xo": "^0.7.1"}, "gitHead": "ebaf64169768d3a7523e3e4eab24f016dc7f7cae", "bugs": {"url": "https://github.com/qix-/node-error-ex/issues"}, "homepage": "https://github.com/qix-/node-error-ex#readme", "_id": "error-ex@1.1.2", "_shasum": "10e285269d885290bcf1451ffc99fb5e1820f800", "_from": ".", "_npmVersion": "3.2.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "10e285269d885290bcf1451ffc99fb5e1820f800", "tarball": "https://registry.npmjs.org/error-ex/-/error-ex-1.1.2.tgz", "integrity": "sha512-7xu3M8Zuhi511ury3X7XWywI+9N4CSU+WEXGHqqeZZL1IfJMmRZBeB19ZNBXJueyqnfSI/VIqNX7bDKu7LhPlQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDDkW3q7EzDMcVPqHB8iSOFaJchCQt1j8plsZs4yVWgoAiAI46QEAu3s/Ei56vwuL4wGdiIgrxkes8zlSrA0lZSNaA=="}]}, "directories": {}}, "1.2.0": {"name": "error-ex", "description": "Easy error subclassing and stack customization", "version": "1.2.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "keywords": ["error", "errors", "extend", "extending", "extension", "subclass", "stack", "custom"], "license": "MIT", "scripts": {"pretest": "xo", "test": "mocha --compilers coffee:coffee-script/register"}, "repository": {"type": "git", "url": "git+https://github.com/qix-/node-error-ex.git"}, "files": ["index.js"], "devDependencies": {"coffee-script": "^1.9.3", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "mocha": "^2.2.5", "should": "^7.0.1", "xo": "^0.7.1"}, "gitHead": "4655f35e2e85a9f3d4c521e7fb40e3fdb6b206dc", "bugs": {"url": "https://github.com/qix-/node-error-ex/issues"}, "homepage": "https://github.com/qix-/node-error-ex#readme", "_id": "error-ex@1.2.0", "_shasum": "95d5183be6047a32a9755639662d2b737b22a76a", "_from": ".", "_npmVersion": "3.2.0", "_nodeVersion": "0.12.0", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "95d5183be6047a32a9755639662d2b737b22a76a", "tarball": "https://registry.npmjs.org/error-ex/-/error-ex-1.2.0.tgz", "integrity": "sha512-yqHJKRrkef/BUMA0Qfc6MBu+nro/PY2cMGbRxks5sBHUyYk9I0Q/WgxcTifbJ1mPfCxkSV9AATqRBBzWXvvsDg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFXfos6hj7/3e7RYkdCp+mkqwhdGbGSPREpDLqK4KCC8AiB4pdN+9h5quv/pq3MiURII4n6bzpbRBhhgLQsFQPVbDw=="}]}, "directories": {}}, "1.3.0": {"name": "error-ex", "description": "Easy error subclassing and stack customization", "version": "1.3.0", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "keywords": ["error", "errors", "extend", "extending", "extension", "subclass", "stack", "custom"], "license": "MIT", "scripts": {"pretest": "xo", "test": "mocha --compilers coffee:coffee-script/register"}, "xo": {"rules": {"operator-linebreak": [0]}}, "repository": {"type": "git", "url": "git+https://github.com/qix-/node-error-ex.git"}, "files": ["index.js"], "devDependencies": {"coffee-script": "^1.9.3", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "mocha": "^2.2.5", "should": "^7.0.1", "xo": "^0.7.1"}, "dependencies": {"is-arrayish": "^0.2.1"}, "gitHead": "118bb63206f736f2450480e73f9d7d22692ae328", "bugs": {"url": "https://github.com/qix-/node-error-ex/issues"}, "homepage": "https://github.com/qix-/node-error-ex#readme", "_id": "error-ex@1.3.0", "_shasum": "e67b43f3e82c96ea3a584ffee0b9fc3325d802d9", "_from": ".", "_npmVersion": "3.3.6", "_nodeVersion": "4.1.1", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "e67b43f3e82c96ea3a584ffee0b9fc3325d802d9", "tarball": "https://registry.npmjs.org/error-ex/-/error-ex-1.3.0.tgz", "integrity": "sha512-/bdlLeS3YjWNqkrb6Sb9SLYIL0yN5HblxxiMgA7XYnfgCP/bBrg4iYamm9lOJSD089O1hxFadbHHuBYvN30+gQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDoM36HcPSZcdxvFM1oYqnzr/o45t/jxRXFdPPk9DKzuAiAjXaH4l2I/LqHVIDJ258fwCZOGbR+lBx/CLsuUT9b44Q=="}]}, "directories": {}}, "1.3.1": {"name": "error-ex", "description": "Easy error subclassing and stack customization", "version": "1.3.1", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "keywords": ["error", "errors", "extend", "extending", "extension", "subclass", "stack", "custom"], "license": "MIT", "scripts": {"pretest": "xo", "test": "mocha --compilers coffee:coffee-script/register"}, "xo": {"rules": {"operator-linebreak": [0]}}, "repository": {"type": "git", "url": "git+https://github.com/qix-/node-error-ex.git"}, "files": ["index.js"], "devDependencies": {"coffee-script": "^1.9.3", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "mocha": "^2.2.5", "should": "^7.0.1", "xo": "^0.7.1"}, "dependencies": {"is-arrayish": "^0.2.1"}, "gitHead": "002e0ffd7ae9e3427555459c132e823251d7e195", "bugs": {"url": "https://github.com/qix-/node-error-ex/issues"}, "homepage": "https://github.com/qix-/node-error-ex#readme", "_id": "error-ex@1.3.1", "_shasum": "f855a86ce61adc4e8621c3cda21e7a7612c3a8dc", "_from": ".", "_npmVersion": "2.15.1", "_nodeVersion": "4.4.3", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"shasum": "f855a86ce61adc4e8621c3cda21e7a7612c3a8dc", "tarball": "https://registry.npmjs.org/error-ex/-/error-ex-1.3.1.tgz", "integrity": "sha512-FfmVxYsm1QOFoPI2xQmNnEH10Af42mCxtGrKvS1JfDTXlPLYiAz2T+QpjHPxf+OGniMfWZah9ULAhPoKQ3SEqg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFJd21cl3BLZu19TsaV7zt0DUfYbPSM4rVJ0B8smmHT3AiEAp9ovr3UdzVVq3Bcn+r+o5naLo1I4HIAyjro9IRHjq3Q="}]}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/error-ex-1.3.1.tgz_1488583539188_0.11632813210599124"}, "directories": {}}, "1.3.2": {"name": "error-ex", "description": "Easy error subclassing and stack customization", "version": "1.3.2", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "keywords": ["error", "errors", "extend", "extending", "extension", "subclass", "stack", "custom"], "license": "MIT", "scripts": {"pretest": "xo", "test": "mocha --compilers coffee:coffee-script/register"}, "xo": {"rules": {"operator-linebreak": [0]}}, "repository": {"type": "git", "url": "git+https://github.com/qix-/node-error-ex.git"}, "files": ["index.js"], "devDependencies": {"coffee-script": "^1.9.3", "coveralls": "^2.11.2", "istanbul": "^0.3.17", "mocha": "^2.2.5", "should": "^7.0.1", "xo": "^0.7.1"}, "dependencies": {"is-arrayish": "^0.2.1"}, "gitHead": "3953ebb9e4a33287e67676a70a8ad72bbbe38075", "bugs": {"url": "https://github.com/qix-/node-error-ex/issues"}, "homepage": "https://github.com/qix-/node-error-ex#readme", "_id": "error-ex@1.3.2", "_npmVersion": "5.6.0", "_nodeVersion": "9.6.1", "_npmUser": {"name": "qix", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==", "shasum": "b4ac40648107fdcdcfae242f428bea8a14d4f1bf", "tarball": "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz", "fileCount": 4, "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbKKCxCRA9TVsSAnZWagAAsHEQAIvbbbWIpgSZwIuLnHnr\ndOkXe8Hx2ci6E0Pf/VNyt9r6ze2UeGaT5Vo8D2Uaf7BWJgDxNi5nlm+yKOdj\nuppa3VdfXz4/nTw2QJ7gtLInga3RcTbZ8MrCbS2SBdzmhWnYw0kRj5drDM53\nIibS6Hgy4N7Vay8s92LuIH4Ut3cRvAtYb2pvLbX5LB0zCBvLZDyvpRnBXRwa\nywvv6VPzNB6YwKEblXaKFxkDsVZOs8wTLbSrjLrGdiU0+yPwZXeOWgWLHIjW\nUa2+yccy5knag26NXDTp/h6KpQfzM6y39JDrtuekONmDcBrtvoKY16vNn8j6\n4pCEVTLh60VH8J693EzdWtYARWlGe0n9Q5efq0w5x3zda/OtuypZXcKDGH+v\n6fy2ws8Dl3uD24UNfyzpftOpwgPxynr5JxIwwqDS+NfLnvkDUXq+rT2bEguy\nFL/iaxxLHjqgOPi1k9QSRDEWIutZ6CbwsJ4Yk4B8rZDeW5C1oVP/ahZ8HO7H\nJ+fjhqfGXmvesfoQ+k/bRnKB2ZFi6EDv4P/Cg+sinCsvyl44rqWrXucckq69\ndGCtwXikn8toh/0o9c9NHVpH76lgpWyjCmP1L9ITUZHzQ9FZwI0QL6+apXGg\noR5Cr62kLhLBBoN33S+I7p9YUceQxgQBPwoXCWnhn9u0HRV8i2yz47hfJoYV\nMeuh\r\n=jN/Y\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDidhmuW3AFBDL07QpDhd1PQvnTXjNwbMGgij6jYD9g2AiEAgIL2DBAD58m8E7/z1c7x3ZQWLGv9mnsc/zl0dO4//fA="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/error-ex_1.3.2_1529389232741_0.6695053149754291"}, "_hasShrinkwrap": false}}, "readme": "# node-error-ex [![Travis-CI.org Build Status](https://img.shields.io/travis/Qix-/node-error-ex.svg?style=flat-square)](https://travis-ci.org/Qix-/node-error-ex) [![Coveralls.io Coverage Rating](https://img.shields.io/coveralls/Qix-/node-error-ex.svg?style=flat-square)](https://coveralls.io/r/Qix-/node-error-ex)\n> Easily subclass and customize new Error types\n\n## Examples\nTo include in your project:\n```javascript\nvar errorEx = require('error-ex');\n```\n\nTo create an error message type with a specific name (note, that `ErrorFn.name`\nwill not reflect this):\n```javascript\nvar JSONError = errorEx('JSONError');\n\nvar err = new JSONError('error');\nerr.name; //-> JSONError\nthrow err; //-> JSONError: error\n```\n\nTo add a stack line:\n```javascript\nvar JSONError = errorEx('JSONError', {fileName: errorEx.line('in %s')});\n\nvar err = new JSONError('error')\nerr.fileName = '/a/b/c/foo.json';\nthrow err; //-> (line 2)-> in /a/b/c/foo.json\n```\n\nTo append to the error message:\n```javascript\nvar JSONError = errorEx('JSONError', {fileName: errorEx.append('in %s')});\n\nvar err = new JSONError('error');\nerr.fileName = '/a/b/c/foo.json';\nthrow err; //-> JSONError: error in /a/b/c/foo.json\n```\n\n## API\n\n#### `errorEx([name], [properties])`\nCreates a new ErrorEx error type\n\n- `name`: the name of the new type (appears in the error message upon throw;\n  defaults to `Error.name`)\n- `properties`: if supplied, used as a key/value dictionary of properties to\n  use when building up the stack message. Keys are property names that are\n  looked up on the error message, and then passed to function values.\n\t- `line`: if specified and is a function, return value is added as a stack\n    entry (error-ex will indent for you). Passed the property value given\n    the key.\n  - `stack`: if specified and is a function, passed the value of the property\n    using the key, and the raw stack lines as a second argument. Takes no\n    return value (but the stack can be modified directly).\n  - `message`: if specified and is a function, return value is used as new\n    `.message` value upon get. Passed the property value of the property named\n    by key, and the existing message is passed as the second argument as an\n    array of lines (suitable for multi-line messages).\n\nReturns a constructor (Function) that can be used just like the regular Error\nconstructor.\n\n```javascript\nvar errorEx = require('error-ex');\n\nvar BasicError = errorEx();\n\nvar NamedError = errorEx('NamedError');\n\n// --\n\nvar AdvancedError = errorEx('AdvancedError', {\n\tfoo: {\n\t\tline: function (value, stack) {\n\t\t\tif (value) {\n\t\t\t\treturn 'bar ' + value;\n\t\t\t}\n\t\t\treturn null;\n\t\t}\n\t}\n}\n\nvar err = new AdvancedError('hello, world');\nerr.foo = 'baz';\nthrow err;\n\n/*\n\tAdvancedError: hello, world\n\t    bar baz\n\t    at tryReadme() (readme.js:20:1)\n*/\n```\n\n#### `errorEx.line(str)`\nCreates a stack line using a delimiter\n\n> This is a helper function. It is to be used in lieu of writing a value object\n> for `properties` values.\n\n- `str`: The string to create\n  - Use the delimiter `%s` to specify where in the string the value should go\n\n```javascript\nvar errorEx = require('error-ex');\n\nvar FileError = errorEx('FileError', {fileName: errorEx.line('in %s')});\n\nvar err = new FileError('problem reading file');\nerr.fileName = '/a/b/c/d/foo.js';\nthrow err;\n\n/*\n\tFileError: problem reading file\n\t    in /a/b/c/d/foo.js\n\t    at tryReadme() (readme.js:7:1)\n*/\n```\n\n#### `errorEx.append(str)`\nAppends to the `error.message` string\n\n> This is a helper function. It is to be used in lieu of writing a value object\n> for `properties` values.\n\n- `str`: The string to append\n  - Use the delimiter `%s` to specify where in the string the value should go\n\n```javascript\nvar errorEx = require('error-ex');\n\nvar SyntaxError = errorEx('SyntaxError', {fileName: errorEx.append('in %s')});\n\nvar err = new SyntaxError('improper indentation');\nerr.fileName = '/a/b/c/d/foo.js';\nthrow err;\n\n/*\n\tSyntaxError: improper indentation in /a/b/c/d/foo.js\n\t    at tryReadme() (readme.js:7:1)\n*/\n```\n\n## License\nLicensed under the [MIT License](http://opensource.org/licenses/MIT).\nYou can find a copy of it in [LICENSE](LICENSE).\n", "maintainers": [{"name": "qix", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-06-17T00:08:07.139Z", "created": "2015-08-25T21:16:59.317Z", "0.1.0": "2015-08-25T21:16:59.317Z", "0.2.0": "2015-08-26T00:07:29.897Z", "0.2.1": "2015-08-26T00:08:39.849Z", "0.2.2": "2015-08-26T00:26:38.364Z", "0.2.3": "2015-08-26T00:29:59.393Z", "0.2.4": "2015-08-26T00:31:31.833Z", "0.3.0": "2015-08-26T00:54:01.645Z", "0.3.1": "2015-08-26T01:03:11.773Z", "1.0.0": "2015-08-26T01:38:19.291Z", "1.1.0": "2015-08-26T02:04:55.301Z", "1.1.1": "2015-08-26T15:03:25.538Z", "1.1.2": "2015-08-26T15:07:15.029Z", "1.2.0": "2015-08-26T16:33:30.185Z", "1.3.0": "2015-11-04T15:21:01.765Z", "1.3.1": "2017-03-03T23:25:39.450Z", "1.3.2": "2018-06-19T06:20:32.836Z"}, "homepage": "https://github.com/qix-/node-error-ex#readme", "keywords": ["error", "errors", "extend", "extending", "extension", "subclass", "stack", "custom"], "repository": {"type": "git", "url": "git+https://github.com/qix-/node-error-ex.git"}, "bugs": {"url": "https://github.com/qix-/node-error-ex/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"zhenguo.zhao": true, "madsummer": true, "sbruchmann": true, "flumpus-dev": true}}