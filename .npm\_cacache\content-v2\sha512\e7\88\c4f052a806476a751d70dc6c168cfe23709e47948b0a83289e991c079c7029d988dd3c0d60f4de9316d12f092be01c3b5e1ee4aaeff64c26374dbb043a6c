{"_id": "node-opus", "_rev": "36-e93c5af2720f5e078793ce78a723bcd6", "name": "node-opus", "description": "NodeJS native binding to OPUS", "dist-tags": {"latest": "0.3.3"}, "versions": {"0.1.0": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.1.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "repository": {"type": "git", "url": "http://github.com/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux"], "cpu": ["x64"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "nan": "~1.6.2"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "gitHead": "7d2e270418f79364ec13cab884f84b99e8143360", "_id": "node-opus@0.1.0", "_shasum": "d01b08e7609d3006f7b5dda04c655a8dfc5c5cf7", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d01b08e7609d3006f7b5dda04c655a8dfc5c5cf7", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.1.0.tgz", "integrity": "sha512-DGtjDgmPzhjzw4ZsVlpUILAC7jvZpvdRFZRY/y8pEALIijEetUSTbbJl0TaVYh1S8mQ8fNF4WoPsSXtQQZlwgg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCThnBCbSjBt61Op6XaMbVGsOt91PpD7k0GbU694ZpTGgIhAL+d9t1K4P07MXftP0soif5PL18Kz5mexiB74I5G7hwm"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.1.1": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.1.1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "repository": {"type": "git", "url": "http://github.com/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux"], "cpu": ["x64"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "nan": "~1.6.2"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "gitHead": "fa332c62b0f00ccfdacaed0cb7e9222de633d09a", "_id": "node-opus@0.1.1", "_shasum": "e85c796c26cb0c2404fffe66c79167cb1f08312d", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "e85c796c26cb0c2404fffe66c79167cb1f08312d", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.1.1.tgz", "integrity": "sha512-CnbVUdzSq3NvNrZi9jBKmdj6pt2FNh92FYvxVjOAUB01LMlrEZHXYz+mQd64sxYvxcwffeS8oIMw0zuDJ48rFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIETEdk5G5IVfPAWhQOJclcKqSLB6GQXkP97tazcCx/C2AiEAsd0lihsqPXpuFQgBc68mnaCN0dFXCxq5QtdOzKKcIpM="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.1.2": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.1.2", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "repository": {"type": "git", "url": "http://github.com/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux"], "cpu": ["x64"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "nan": "~1.6.2"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "gitHead": "1bb37818fca09dcbd306c6469fb3143012105e7b", "_id": "node-opus@0.1.2", "_shasum": "d487cab2dbf726d5d0665350276a1097d4d67235", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d487cab2dbf726d5d0665350276a1097d4d67235", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.1.2.tgz", "integrity": "sha512-z71lEJ0uyQZAljwJSEZ9/Th9bLCUzq8PvEoXgns0CgvRmq5/pLBb5TzbpVJR6ch1fP+mFZHOx6rSz9QHaOm/EQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQtxVjIB0y1tB7+H3Z5cMP8VstPnVpKSUOu/luv6CFUgIhAI5k7KJ5zjP0OkvJ4qAPfE+yepoZ9Wd8YhA69bbAYlcV"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.1.3": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.1.3", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "repository": {"type": "git", "url": "http://github.com/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux"], "cpu": ["x64"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "nan": "~1.6.2"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "gitHead": "a11835f9b850de150ae89806109775d6cc84bd2a", "_id": "node-opus@0.1.3", "_shasum": "3016b416dac64de6266658a9bd4ba009e6f46518", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3016b416dac64de6266658a9bd4ba009e6f46518", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.1.3.tgz", "integrity": "sha512-wz8lujDn7Gp9/wcvnvOQnqxXBxutfnWttxaAhZxSqYXfDMwSfDXZ5JfhMpsUSAECaSEWnj4s6wskQxzAIbb04A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD+alAjoZyX12WwjGLlbOu5v7PJhrnFXQ6NPzkg8ztH8QIgNkKeqzPFIJ8gtYsVe3JhswNHhJmYejhOpUsdHlaVfc8="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.1.4": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.1.4", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "repository": {"type": "git", "url": "http://github.com/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin"], "cpu": ["x64", "arm"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "nan": "~1.6.2"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "gitHead": "ed816d4d8875b150778579e8036779da35fddb3a", "_id": "node-opus@0.1.4", "_shasum": "ca1da447d97e767fbfb82defebcd3ca797101877", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ca1da447d97e767fbfb82defebcd3ca797101877", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.1.4.tgz", "integrity": "sha512-67ahZj2tDWdf+Y9J+/2/IWNjgj+H1TEuXyDniW32fiYvyBiHLVmtqoecExmWM1QirBaEaEqS8uQNojEq4XFLRQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBhJsy+8gijW8qWX4WZhy7WEewbB1QSOEYYC+Rvq0RshAiEA3HSusLXQednlPPY4l0z7C+yswMtGRE47DUa0pDZKhXE="}]}, "directories": {}, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.1.5": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.1.5", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "repository": {"type": "git", "url": "http://github.com/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin"], "cpu": ["x64", "arm"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "nan": "~1.6.2", "ogg-packet": "^1.0.0"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "gitHead": "ad813d8f39f798dd5ef31690893fb994a61a297c", "_id": "node-opus@0.1.5", "_shasum": "5ddd391437ec4dadec5b7bdb82c2e6e793047d3c", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "5ddd391437ec4dadec5b7bdb82c2e6e793047d3c", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.1.5.tgz", "integrity": "sha512-SzQDCe8rCpnE5amT/Yta0Si3Czo7OSyi3xn+zbVgs/pQVVDXAD3bliNXuBXaF37uqHGa8jqhH6QVObr+F6pxtg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD/xlNX9pkGbaoMa2bexbvffmdLPZHsarDSjqNukCwAewIgWh4XzUhVsu0WPRZ4SFZqid5GgT6klSaXcRJmhQBRtHI="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.1.6": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.1.6", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "repository": {"type": "git", "url": "http://github.com/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin"], "cpu": ["x64", "arm"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "nan": "~1.6.2", "ogg-packet": "^1.0.0"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "gitHead": "2882b26155a4df14502d156a41774544f7f84884", "_id": "node-opus@0.1.6", "_shasum": "dcfdb741e49c76fda150374f623f7fd4c99d8779", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "dcfdb741e49c76fda150374f623f7fd4c99d8779", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.1.6.tgz", "integrity": "sha512-OwMmvNVRzBiVQe8Kupj8AVX98mJBZZ06IJKBlWBMwQGlxOrEjgG+9nigYtWaS1k9Yota4X+32RhN5VSSbp6Meg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCwYwUDh+hnbqEpIWurJ/oyCcise3dzQ23XK8Ikg5WbCAIhAJVm/+4mISrqpOCQPniv29fTnGRlwgvc80vBh9m05TQB"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.1.7": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.1.7", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "repository": {"type": "git", "url": "http://github.com/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin"], "cpu": ["x64", "arm", "win32"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "nan": "~1.6.2", "ogg-packet": "^1.0.0"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "gitHead": "8ef5d5e380bfe03fb046a310eba7b0610bcbde2c", "_id": "node-opus@0.1.7", "_shasum": "250071b0c0827677be35d1d99bbfda01d6cb640d", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "250071b0c0827677be35d1d99bbfda01d6cb640d", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.1.7.tgz", "integrity": "sha512-7Hy7NnnZQrqdBVYeexgsUmpJ9dr3GAGNAqqgVLiEAtZ+WaHmOH2oml0wsFv+r3enePPRwvWV6SfN+EhAdeK9aA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVwHfSRqW4rBrSvSTzaP/llnUeX5M1NDaP5MB2HxOc7AIhAOaIt3XzojLcn0tv8P18LcNNxsIFVSnFVfQZcIqXA41e"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.1.8": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.1.8", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "repository": {"type": "git", "url": "http://github.com/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin", "win32"], "cpu": ["x64", "arm"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "nan": "~1.6.2", "ogg-packet": "^1.0.0"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "gitHead": "1c149e9dedf43e80e1272dec75767bee51f270a6", "_id": "node-opus@0.1.8", "_shasum": "d8aa32d9040387e2981bede5118a9614b334b104", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d8aa32d9040387e2981bede5118a9614b334b104", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.1.8.tgz", "integrity": "sha512-m/1UGc+r1iCGlMwyHTuZop7uwjC7KF1Cn1BL/qIOHgSAjxQek5dl5lHuUhF+w2HPBLpwUBPgL9Q2TBT66ER8rA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICuaA/P9OAqhvfznNHxyX03Jt/gabg7nT0PGbZu02/BfAiAbKGAI6SptG9qMqSO95lk2vQKeQtArL8ni/gdjdbkqEw=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.1.9": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.1.9", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "repository": {"type": "git", "url": "http://github.com/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin", "win32"], "cpu": ["x64", "arm"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "nan": "~1.6.2", "ogg-packet": "^1.0.0"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "gitHead": "480efd92ad9cde917f5d5fbf07e9e0aa58a84bd8", "_id": "node-opus@0.1.9", "_shasum": "7ec70855a239b4ad95e414090e05f6ab384f7d7a", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "7ec70855a239b4ad95e414090e05f6ab384f7d7a", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.1.9.tgz", "integrity": "sha512-FOga3jffXap3tjKDbPr9u/wotORg8sjBJFm2DwVuVi0FJfHH5ZgKmomPoMsnjdR+8w3J8PaTI0N8J8NFYIKgdA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQIRUX2f7tVakMXB5YV5ac2NWZHQODhYOB3o8x4c7QGQIgHJLocB+8S+zfZCobJu0LBfBuY+HuwYibNcf2NjdN7QE="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.1.10": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.1.10", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "repository": {"type": "git", "url": "git+ssh://**************/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin", "win32"], "cpu": ["x64", "arm"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "nan": "~1.8.4", "ogg-packet": "^1.0.0"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "gitHead": "b9c9a57c46826062022e9c9d114f31ae451170ce", "_id": "node-opus@0.1.10", "_shasum": "bd1ffb755977dde59c2ce14d2b6d968f34bd05e0", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "2.3.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "bd1ffb755977dde59c2ce14d2b6d968f34bd05e0", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.1.10.tgz", "integrity": "sha512-TuV61GEKuHV/X2ERBmIiA2ZvFfjNl+r0BbpPdJiqYSOl3Hw4Ncljw3unPGorLRQtcMjn87/ihIfPjbm2N6Y4Aw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBrkjGcdOpIyVe96n9sni34g2BPQ9Mq3yPv8raL1vA95AiEAlvdcfCjdwQKhyyVOEKpIz2IVxx4Uqx16a1QQAfhzeHY="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.1.11": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.1.11", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "repository": {"type": "git", "url": "http://github.com/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin", "win32"], "cpu": ["x64", "arm"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "nan": "~2.0.0", "ogg-packet": "^1.0.0"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "gitHead": "77078bd9108dc3917583f3886588bd9d6f067d6b", "_id": "node-opus@0.1.11", "_shasum": "99554bcf5e7d12d12ac6dce06d98f4e7bf6dea86", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "99554bcf5e7d12d12ac6dce06d98f4e7bf6dea86", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.1.11.tgz", "integrity": "sha512-aF2nsMGqhiY4BjknxySxGGMQT30AUtVZOs6JjePsql9DifkGg1b2OGUxn62D1oQ5z3C/l+S1dY5Z3KxB8Pm38A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDTgKo/ZPLiBZps6R9NCdx0m+4Os3rDEASoMe12+d6xHAIhAJqCRnqDtnGxQLC+UwQVnqDM64kheu7aCMrIBXMf0h4h"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.1.12": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.1.12", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "repository": {"type": "git", "url": "git+ssh://**************/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin", "win32"], "cpu": ["x64", "arm", "ia32"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "nan": "~2.0.0", "ogg-packet": "^1.0.0"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "gitHead": "e37d26df6840ef9cdccb3942e9038835b0e88f51", "_id": "node-opus@0.1.12", "_shasum": "4c58d52f9a868984182298a4c78826bb2aaa88f4", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4c58d52f9a868984182298a4c78826bb2aaa88f4", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.1.12.tgz", "integrity": "sha512-TP+/Zfzm9om+NY9NJrl0b+1+pNSjI4pqiWOFH2h/gSOcRCEdYcvnkVliF9H4hp7HqcyLfmmF1gu2t+9bAfbDvg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJZ2LsvQJFq2fAuxb4sTnNee/wc53USMCTGF/4A78t5gIhAKDA7UvwLJJRJcIr+U8yfqSUDrUzbXSJmhdWZmotGrUB"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.1.13": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.1.13", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "repository": {"type": "git", "url": "git+ssh://**************/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin", "win32"], "cpu": ["x64", "arm", "ia32"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "nan": "^2.3.2", "ogg-packet": "^1.0.0"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "gitHead": "2b328bce5047a712e36b1bd21a3f0184d7511b98", "_id": "node-opus@0.1.13", "_shasum": "41ade74fa241465bd56c978ada414d19cc3223cc", "_from": ".", "_npmVersion": "3.6.0", "_nodeVersion": "5.6.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "41ade74fa241465bd56c978ada414d19cc3223cc", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.1.13.tgz", "integrity": "sha512-XFBmsDodCCPGp1jX2RYJ/QQgNVCKcyob5txzAf/6W9rBcj4VAN4BfkeGSHEiOAFrdKtVHYUa+zyEVeijOVB6yQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGyqih/EZmF/x9tu/pJDbgoXz2jiJSUY2/cRcsVbLhXZAiBo+yPHetorOhfO04FIy45VGMqFNdVyc0sVTMG/Z2XG/Q=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/node-opus-0.1.13.tgz_1462053464263_0.7151059992611408"}, "directories": {}, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.2.0": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.2.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "repository": {"type": "git", "url": "git+ssh://**************/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin", "win32"], "cpu": ["x64", "arm", "ia32"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "nan": "^2.3.2", "ogg-packet": "^1.0.0"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "gitHead": "5fd81b6ec5f55eaef0f747442022d9e2476ef05f", "_id": "node-opus@0.2.0", "_shasum": "ca52e28b555c2746c13fa8f1a5adaefee99e528a", "_from": ".", "_npmVersion": "3.9.3", "_nodeVersion": "6.2.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ca52e28b555c2746c13fa8f1a5adaefee99e528a", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.2.0.tgz", "integrity": "sha512-osm5dBHfc4ro4pIKsYAcDfPAf1JfsIReqNZJH2w5c2sEwJCPd9kqw30ATdiMdsJHp77kDEa/SJGc9ZPzzTYPPQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzdcISGLBkm0HLVQMhNkepKqPGZamw8CB1MVDJDp+F7gIgL82tyxhWYhLi7DWDThUDC7+H89AS5Sl7Zbmv0kCI+hk="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/node-opus-0.2.0.tgz_1465161324854_0.8664342693518847"}, "directories": {}, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.2.1": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.2.1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "repository": {"type": "git", "url": "git+ssh://**************/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin", "win32"], "cpu": ["x64", "arm", "ia32"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "nan": "^2.3.2", "ogg-packet": "^1.0.0"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "gitHead": "f3d1db5c63e5fa1e1af50d578e6c80474304a179", "_id": "node-opus@0.2.1", "_shasum": "2c81e8e7fd9dd411f7655452b1019fb0791b8ebc", "_from": ".", "_npmVersion": "3.9.3", "_nodeVersion": "6.2.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "2c81e8e7fd9dd411f7655452b1019fb0791b8ebc", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.2.1.tgz", "integrity": "sha512-XCTiJtm60B9064yW+x2d9NwXABT8Oxz6HfEHBdbM+id7RTqQJPG9EOfIC8WexD92dZ+YH09E/cfPwP0zmQvDxg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD6QQxyGFh6yjffpK9fM1p27eDAxrU7NOow90f8vF4KKQIge5lmp2/Su6yXMPtEFq4b3kXhwsSlfcaXAy6zyu04YDA="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-opus-0.2.1.tgz_1467036952038_0.5591279631480575"}, "directories": {}, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.2.2": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.2.2", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "repository": {"type": "git", "url": "git+ssh://**************/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin", "win32"], "cpu": ["x64", "arm", "ia32"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "nan": "^2.3.2", "ogg-packet": "^1.0.0"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "gitHead": "1313a538b52198a475b44c497859b6854fdc235d", "_id": "node-opus@0.2.2", "_shasum": "3fe811cd7dacbb5fd70f4a19b82f23901a334db2", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3fe811cd7dacbb5fd70f4a19b82f23901a334db2", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.2.2.tgz", "integrity": "sha512-wBh8I3OmH9PVV3OhLK100v0gLQW9DxvhqJCMK4KC+65RfXxB6LiahI8++OXZcPBEUmMP3IsUTMy28Olx/KIAjw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFECFo/AaW1qcIn/2+OBxsobXECxE8kVoOr3tLUVVn7SAiAf4UB5XKJi9yo+3T4CX1Nm1IR91FSPWaSuafCEvhZx2A=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-opus-0.2.2.tgz_1475182583488_0.290178210940212"}, "directories": {}, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.2.3": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.2.3", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "repository": {"type": "git", "url": "git+ssh://**************/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin", "win32"], "cpu": ["x64", "arm", "ia32"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "nan": "^2.3.2", "ogg-packet": "^1.0.0"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "gitHead": "27142157aa041891c3308b2fc3c0ef015c181bc0", "_id": "node-opus@0.2.3", "_shasum": "968aabb836fcd9869abc44c437fcdd2a01808bc6", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "7.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "968aabb836fcd9869abc44c437fcdd2a01808bc6", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.2.3.tgz", "integrity": "sha512-WYHO30sNxpXTmqLvtw5U6RYyN7O3HSBX8hPZ0M6f0U89C4PAvGiEkDWh7O4v/29igKj1ZQIfxlEgRUfTZXoOMw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICZygRJCcBEOxF7TZWfvm3yR93wsTH9pFTCMBssqnFJNAiACiwjLZe3QeDAIdM47z69blLKSfpCnabLqYgZWe87LrA=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-opus-0.2.3.tgz_1478542863498_0.9333906681276858"}, "directories": {}, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.2.4": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.2.4", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "repository": {"type": "git", "url": "git+ssh://**************/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin", "win32"], "cpu": ["x64", "arm", "ia32"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "nan": "^2.3.2", "ogg-packet": "^1.0.0"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "gitHead": "2a1f0376a6e352b6f8c4bfc46a8c18954feeba5b", "_id": "node-opus@0.2.4", "_shasum": "79fb35cf0e7ad04cfb9398eb362aba0774e30a61", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "7.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "79fb35cf0e7ad04cfb9398eb362aba0774e30a61", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.2.4.tgz", "integrity": "sha512-b1wc<PERSON>yrera7q13yWLJAc565f3Fg8yIts4NJgfNVvKlJaSCilXO27m+udjBZziATt1zCx3EaoctZXp84KxWl2ZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCCwfFr+xPQ6FG7HsiBclWPKtkYJa+e4VFIl7zPU0KRSwIhAPCVef0oid8vh4YE65h8TR1iP8jzNYxjjujatO+18eGA"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-opus-0.2.4.tgz_1478605920387_0.1672456548549235"}, "directories": {}, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.2.5": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.2.5", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "repository": {"type": "git", "url": "git+ssh://**************/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin", "win32", "freebsd"], "cpu": ["x64", "arm", "ia32"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "nan": "^2.3.2", "ogg-packet": "^1.0.0"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "gitHead": "3871ca22005115d14e567ed3d3b6e0f5279767d0", "_id": "node-opus@0.2.5", "_shasum": "ba171b32b2469228abfd67ae879440526b6e0442", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "7.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "ba171b32b2469228abfd67ae879440526b6e0442", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.2.5.tgz", "integrity": "sha512-NpWhAL39JTNDLkfeBT7J/CBQLqbc5a1o3wOehvhPwe3excitmMJWs7FJIa+qHd56NxfsgNWQ0D45/PaZz0qylQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCjdxbjYGW7vH6F6Iv5KtpQ77kz6/A7vR2Y+exJv036KQIhAOb6aDhYImugHoxtHbWtQsG67dah9E3lt20ej/qiv+bU"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/node-opus-0.2.5.tgz_1491154965683_0.5679826459381729"}, "directories": {}, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.2.6": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.2.6", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "repository": {"type": "git", "url": "git+ssh://**************/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin", "win32", "freebsd"], "cpu": ["x64", "arm", "ia32"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "nan": "^2.3.2", "ogg-packet": "^1.0.0"}, "optionalDependencies": {"ogg-packet": "^1.0.0"}, "scripts": {"install": "node-gyp rebuild"}, "gypfile": true, "gitHead": "5df232221744c47ffa3ac9f23b28e4aa251b383b", "_id": "node-opus@0.2.6", "_shasum": "19cd2b99d98bcb1378607b9589841057f0c90590", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "7.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "19cd2b99d98bcb1378607b9589841057f0c90590", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.2.6.tgz", "integrity": "sha512-GrCV8b2qYGbIgVYKDq4GPzrLPxB2MESTMGhs5Rqs+JUks/I8UeBotszcVXTUV1RuJA2uhpaSTAcYTcAxfjjrTw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDVofyI/ifUY8NQHIIC3V/bS+GbRNT1oyjAoOoDCiNePgIgMWX+YjgFHOvulo/PX5HVW9a7aR0kEjeY5hC8gUNDGq8="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/node-opus-0.2.6.tgz_1491598903562_0.3691633266862482"}, "directories": {}, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.2.7": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.2.7", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "scripts": {"test": "mocha", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+ssh://**************/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin", "win32", "freebsd"], "cpu": ["x64", "arm", "ia32"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "commander": "^2.9.0", "nan": "^2.3.2", "ogg-packet": "^1.0.0"}, "optionalDependencies": {"ogg-packet": "^1.0.0"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.3.0", "ogg": "^1.2.5", "stream-equal": "^1.0.0"}, "gypfile": true, "gitHead": "8ea60bb334f8dc4fa1a5682d9cad61d5c7305ec8", "_id": "node-opus@0.2.7", "_shasum": "5b726e29795b0b127b4c87e662d4de816840579c", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "7.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "5b726e29795b0b127b4c87e662d4de816840579c", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.2.7.tgz", "integrity": "sha512-wKN7DR6HxwWHHwBihyRI+ONDjj1LJzAu18E0vPcPZ4ejGF3S42D7xLK8mqGngRKTEL21sZoIrO5xHglnJR8iQw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGr1lt2xeaUB1gFAQScfxPyXptd8ViCQl1rz4fOwXX6uAiEA/gyG2dyyV+avAfIED6u90moMGRwNLZL1YbyzVYHHnaY="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-opus-0.2.7.tgz_1504957139945_0.3851252195890993"}, "directories": {}, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.2.8": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.2.8", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "scripts": {"test": "mocha", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+ssh://**************/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin", "win32", "freebsd"], "cpu": ["x64", "arm", "ia32"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "commander": "^2.9.0", "nan": "^2.10.0", "ogg-packet": "^1.0.0"}, "optionalDependencies": {"ogg-packet": "^1.0.0"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.3.0", "ogg": "^1.2.5", "stream-equal": "^1.0.0"}, "gypfile": true, "gitHead": "7891a8af02e7491dda35c7a661ed24187f6d7db6", "_id": "node-opus@0.2.8", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-galKuq0glSMlTOL5vbXPLPcc2eZ0pz3bk3vkHVqUnrrk4fL1/zdRQDPNQV7DsvdB8z0wdn3MAF3s+Zd/f6bWjQ==", "shasum": "b939deaaa3ce9c9ce83830ab35f1989ad5241408", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.2.8.tgz", "fileCount": 442, "unpackedSize": 7662337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7uLUCRA9TVsSAnZWagAAH6kP+gPBVXBvXPSyNknGPFqs\nmaV0SELE/Uo0lyviCjKZa8+uDA6RVytImTH6B1v0JQ6Pw3VUC5FCbaQMaC/l\n48bnKhn+HGKnVJTeo+cgqNlWKDqWTD19RXJVBS/aUVa8d0R26RUpHyqg0Zop\njB/eEZIxTCnbE21Ke43JZ7vc1p2JHEXS4WhY65kc8LQb+NpDEjzu+HHNDUnM\ngFYl4vFUhMPVBukJIuAk3tkUCH2E4kMSP0E05HDbaghHQUIm2gKaYMnN71bU\nfp6LRb720TEX66XnrjE0fW336GX7dDDHBu3hiReSpOrQE5rS6CJbTE3hUS45\n4deRqyaGiAkrtSPdGDLPW75IL6ZHAtFQ76xAPWhzJTAsFYZlvvJ3OU+88/PO\nXnKwI8OnbhuEbVASpizcSlLNWdCshGZoa1OCmtBTz7yFneP/KzW29+Y+cBzB\nInP/81RY9Np4YVizdv12e7b1KVH/nlBFnVbG4gIFan86mqEcxVS7745diUn5\nW75nf+jqY41oWSgCGpPa2oiOvIy/Y1xPKP1NPrFUKX/5rTCnGeH8uOaVg5HZ\nHyYgK5dJDFtqJPtza5m1wTLsX0yYCldKtjKxK+czp92pyn8PgmDIeOo8e3h7\nouD7axogKZmk+YdsFT5hFBr1OjQDH541FhsnBLIAlrDqK/PGT98Llv8BKPXk\nNex1\r\n=cGCd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD7KW0CbnDmfvM0dt8EAJjpMTLxHk/UjEnEuLGbJA6RTAIgRWRG8Xbtn/4o6DOqkKk8cqbLvojrC0lTX6yDFFpSx8I="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-opus_0.2.8_1525605075144_0.637250004534835"}, "_hasShrinkwrap": false, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.2.9": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.2.9", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "scripts": {"test": "mocha", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+ssh://**************/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin", "win32", "freebsd"], "cpu": ["x64", "arm", "arm64", "ia32"], "engines": {"node": ">=0.10.0"}, "dependencies": {"bindings": "~1.2.1", "commander": "^2.9.0", "nan": "^2.10.0", "ogg-packet": "^1.0.0"}, "optionalDependencies": {"ogg-packet": "^1.0.0"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.3.0", "ogg": "^1.2.5", "stream-equal": "^1.0.0"}, "gypfile": true, "gitHead": "87ce140f912153dfc2f006679c48b9e9425d3410", "_id": "node-opus@0.2.9", "_npmVersion": "5.6.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-+IIOdav5D7vHsuLDNk55t17kK2s6c1w4DbfKw8UQxZ635n+AO/SqDE3RpuO3PZKqpWjPtL/chzYZNVxz8/4TUQ==", "shasum": "a9da56c96b6b5f5c32c61ea8a4593bc7295daa11", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.2.9.tgz", "fileCount": 443, "unpackedSize": 7668002, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7uNBCRA9TVsSAnZWagAANm0P/0I8s68Bjk8PCXWkId4D\nDq5Cs2gKdyhPnQbYWITLHPsq5CwPNqp9kH85aT+J2vF26y6kd/ZznSoJjmPD\nsKa8C9e0eQd1dM4yVYLDu8tXTPwr1F3ts4njjCjlIxZVZMm2kYsp4dD7GDT6\nuojjEB9bLAxvzD3rfgsaVtJSu8N9HoCpylKQq30tfzsy04gAJiMLNZn/+FIK\nb0S82uV6Y4k72l0cDan9jK/ihAhvzOyDlmkuLXBcKYt94fl2s9KMIsUPrzkx\ngMtVtTpknAUeIcDgWUKYgThDZRfV4hhVrvxKhzQkaV/dQ3VC5X74OXGMUF8G\n2h2W+wzzryq112pime69xfBijTEgk8Qnx9B1lu9X+iVkqnp5Y0VEcn50jSXo\nQkigKgAJb7QoJmFpIq9PsDGl1BiQfnaB6Ml7naErFy6oH9AVBkLOQXmvDyRN\n3W5Cko7lwJGCvpeCLbm1D9PLisYyn1LKpXLlY8UMyNnzjkMeVeZassCtdybN\nXYasELifbbcUe45zdvwkltxkj6zu1oLYaxJrA4Ozt7UrlE0q/ll4/6VdFvf9\noU2407JjKjYKMcd1iPb2lqIobbDOy1i4DAL7IoOWxHYaPeWAtBEdYSufRjHr\nXrixrWzxRAoEUGy3es/afmTynNU0+2yqw4NdbcA48C6s/EL/MZSjtenLzOjv\nkh2h\r\n=NcR4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCT4QI0thUugOo57CjwZI4FMMexrVwzqOzntLCgkF4cmQIhAIeYQ2+ec7/B/TdIfvAk8JI/i405SBSxvCrL90FSlkPx"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-opus_0.2.9_1525605184384_0.6626215023993947"}, "_hasShrinkwrap": false, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.3.0": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.3.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "scripts": {"test": "mocha", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+ssh://**************/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin", "win32", "freebsd"], "cpu": ["x64", "arm", "arm64", "ia32"], "engines": {"node": ">=5.10.0"}, "dependencies": {"bindings": "~1.2.1", "commander": "^2.9.0", "nan": "^2.10.0", "ogg-packet": "^1.0.0"}, "optionalDependencies": {"ogg-packet": "^1.0.0"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.3.0", "ogg": "^1.2.5", "stream-equal": "^1.0.0"}, "gypfile": true, "gitHead": "8b8e21f79bca366de601049dd380c2a51e32e67b", "_id": "node-opus@0.3.0", "_npmVersion": "6.0.0", "_nodeVersion": "9.2.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Hih2TCi/vQUY4D9PBiATT6fluJSjMJPLBfCNgAxJx03v6jT+UlVmXUKR62RXYWyijhXNghsFJMyeBe7MBm3cmQ==", "shasum": "d84a96ead8599084cd5a9899f096ce4576980a09", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.3.0.tgz", "fileCount": 443, "unpackedSize": 7668013, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7u/ICRA9TVsSAnZWagAA7W8QAJsHZ43licy0uV6h0mhy\nD9YBfrFX0SKaav3k1aW2+NXIwZdrlSW0ZGN8Lu4YA1GQv7ajU8hYq8BoymAx\ntk1Rh/Xz8yB3CVuLAUYMyckAu47V/fR3EUfXVvVx18An2+BG1TJUKTHPLmDQ\nafR7tWutfjORfWRJwLAuBlkbPsNIR496vnRJ7kiEEysLTI/VhvFtg6csOS2G\nG42vLVfLx+XVgBherU2pluI6ojbEB+OZjwIpF8OahyaDmoHSsmdrWriJ9qjn\nZjl1apRApIRVRikATlfgvkqdqvFT0nB8oRY4RwBgJN050vGGVUZrwx9oAIQa\nft51H6hKDb5IcyzPpzd0mKj7q+RbQapUeZb7+3UAWYMgobvTOOwC7xFNpUSn\nyfNOX4gHEtl/WTaO6vBfd9aVMPQ5dl9d8FLrhbIL/0rigYJOI2C8ByAxAVTy\nXv7yfcNonZJ/bJo12cWdIUFoB9GuyAFh21vf8/pNxnb8th9jrLpWqbqVbtBC\nGGDXN/ciFH7F4hJ3qVO5+V430AVE7x152Oz00MgK7BtQSoZC8z3hkKeF3KOY\nRQ5pIT3vb5OuD0+IdCQh2wMiPUrSuD7/Yis3Evdc9r8p5ATcqKh+Td2ejAg9\nLb5P8kEbMBgMy1L5i7DQDOLkl2r/ItY3RKqBheXazyAqxtQt7/HzqevjQM8d\nV5WV\r\n=g0/M\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDWm7T3MiU35IKYbL2MUjaZYXd6XAA2NCB7HiUhbhJvfgIhANSqSNIw/RHRqQdA0E9f6nrZoP//kTTyQDtoxvqKgjIY"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-opus_0.3.0_1525608391282_0.4133428334005649"}, "_hasShrinkwrap": false, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.3.1": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.3.1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "scripts": {"test": "mocha", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+ssh://**************/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin", "win32", "freebsd"], "cpu": ["x64", "arm", "arm64", "ia32"], "engines": {"node": ">=5.10.0"}, "dependencies": {"bindings": "~1.2.1", "commander": "^2.9.0", "nan": "^2.10.0", "ogg-packet": "^1.0.0"}, "optionalDependencies": {"ogg-packet": "^1.0.0"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^5.2.0", "ogg": "^1.2.5", "stream-equal": "^1.0.0"}, "gypfile": true, "gitHead": "7bbac0185a19de765d822c6c265e855a2f95861e", "_id": "node-opus@0.3.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.10.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-1Cb8OvHhdDspVfeKMjEgbedJabyE1Ib6OcN2BMEsRCU7FIsciuBpOErcie3y0qTf83nclPAY+kBU3Oj+U+oRlQ==", "shasum": "21cbf04030043faa44016e5caf8b7a8c2a5905b3", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.3.1.tgz", "fileCount": 430, "unpackedSize": 7575071, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb2i21CRA9TVsSAnZWagAAERMP/2fnbwK0+AQsVxVcqqRH\na6l8yNkUfn3Xhi5ZMQjScPr4yyRrFa8ddWvb6RA2qyQWo9Dma7LSWQ8Hzy3V\nhJvBD5PU+M1oejgPUYy+WuC3yauYlGvxYickW2nhpw5UukmjQ84ZeQEGUb+A\nMuA07gm/vRBaE5EWlX5GY2heJBfA0yUspkvMAOySshTGUfJNSjqEsPYD2KML\n/K1kibLhVbCMSFq6L6LY8qMphYIulawLApU0VkDYiY4URbf6S8FEckFwpwt1\nOR20BeJ9/hguBJ63KpyX4NKbwgF/u+GzfEmiv7ZwmON7NnDXkH0NOjH70yI8\nmxmfXqKplTJlfoyKss9QwPAWv7UpgwADm3KTUxaVctU1bHRAcSQvJsDyw5UX\nlvAyFjKFeAbI5pL3lUFfCw9F9gaCZvfCsW3BSvDjN0pEOtWBArom3Vsqs87s\nFpIZFFgLM9JdEEB82PvD7zrcimzA5KlmL0cBcrvpD3owf/Sekiq9mEcCw4TY\nIkhRD3DjjqRH+Qwfu50HNHvoMTCLQ+UaXJuSyHPhxYYzTl4mVyX4RDwcdXjb\nJ5NkMPAQWnnMH6ffnODG+jDXTb2W02WTuhzK1r1bYe9152p5CnoHZQssVY+9\n446vL17pYunR3eickEmsPYs9we8UJteeGrTaqzYtZHF2qURMPGfXZMMJuYjo\nriRE\r\n=0JDD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD9LWz2gI7b72gSVVwsowz7fdVu2ny7D1njFp5Dc3JFDQIgPwKzIqwV0w8vTIeom0+kOF/8XSADva7UBpFDNy6hcpw="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-opus_0.3.1_1541025201984_0.6924167129210661"}, "_hasShrinkwrap": false, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.3.2": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.3.2", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "scripts": {"test": "mocha", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+ssh://**************/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin", "win32", "freebsd"], "cpu": ["x64", "arm", "arm64", "ia32"], "engines": {"node": ">=5.10.0"}, "dependencies": {"bindings": "~1.2.1", "commander": "^2.9.0", "nan": "^2.14.0", "ogg-packet": "^1.0.0"}, "optionalDependencies": {"ogg-packet": "^1.0.0"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^5.2.0", "ogg": "^1.2.5", "stream-equal": "^1.0.0"}, "gypfile": true, "gitHead": "f345c0744239788c4bc34e23e59c7c9829b83537", "_id": "node-opus@0.3.2", "_nodeVersion": "12.2.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-o7hKgNncfbgF1j25lCK7cPqs9DEMroP3LWW0BMb/Ismc33cXo7LcaLqS0+/VV+NOotIy43fb7juLg6akflzBEw==", "shasum": "5a20084e02f86116eadc2c7fd49b233e2260190d", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.3.2.tgz", "fileCount": 430, "unpackedSize": 7575319, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc7A47CRA9TVsSAnZWagAAeXkP/iOzfFX6mZMesd4sukbp\nEsPDNY4BVf+sv8443NpAOpvx6L52WmoeCpuROgfKHMCTVuIRLzktof+IOHF6\nu+QigKZKB1qlE+4BLl3VljxgGvWbBQZVZ7Aag0O2q8Rom1PPWEjFJD/utGCk\nJJPnFYQCdC3TWj7Yt2Gq0NXt9hzkdlrcy6wfLdNya/ywuI9lS4ZwKIEOQptN\nF2g89FQfLxpjXj0EUoSs65F4KiIsUipwtlsRloE80vPbY5eRWwaSS9jo5NHy\nT/tbtVUPy4mxf8y4U7MDHvG/0IsRZlhnSuy3TPbiGaNTyHKpEYv+ney6dHws\nJt1q5DM4ZmJ+N3KvcftWqOGrX1iWeLbg5E/2uiQjRt6yFxKXjOaq98f274f/\no9XyhkIucDoKeWUiWm5j2X7AmA65//diAhN9/MDr/ClSVocr1lmH+yo05Nos\nt31sBs/ipGNu1CV7oWGbCWCjnykgLWWQ4RPVGE67G1D8adms8z5dQJbkR2z6\nYuHyiudvZSepdbrl7IR/jlI298UdoOOKg2uYOIjHlf+Bfqn/nMCDpFjXTv9N\nNEsh3aBS3fImYkpsHhpeFc89jjiF6mwkuLjC1zBNOcDB/OKb0PPZp9jOBkJX\n1Dq7W1MBCpB116sEGqnAusMhEUkQMS/yQgDjdmg2UmsqiTzyOXVjeSdozttV\neJN+\r\n=QGJA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD/hBoQx5p7HfSInFHN/F8bSzPHscPQ0No7IbGctz5IBAIhAKOaY/WPjvT9EZEcQiSCD4dxjqkeKpzCu0uy+tI8DWp1"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-opus_0.3.2_1558974010834_0.5743989978741546"}, "_hasShrinkwrap": false, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}, "0.3.3": {"name": "node-opus", "description": "NodeJS native binding to OPUS", "version": "0.3.3", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/Rantanen/node-opus", "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "scripts": {"test": "mocha", "install": "node-gyp rebuild"}, "repository": {"type": "git", "url": "git+ssh://**************/Rantanen/node-opus.git"}, "//": "Platform limitations is due to lack of config.h files. See README.md for more information.", "os": ["linux", "darwin", "win32", "freebsd", "android"], "cpu": ["x64", "arm", "arm64", "ia32"], "engines": {"node": ">=5.10.0"}, "dependencies": {"bindings": "~1.2.1", "commander": "^2.9.0", "nan": "^2.14.0", "ogg-packet": "^1.0.0"}, "optionalDependencies": {"ogg-packet": "^1.0.0"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^5.2.0", "ogg": "^1.2.5", "stream-equal": "^1.0.0"}, "gypfile": true, "gitHead": "e5a29e0f5a350c708d71737ec50a6d2a7ecd764f", "_id": "node-opus@0.3.3", "_nodeVersion": "12.2.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ZQniA8iJ6y/qOTmW6eyzM9m8odt4CIGV0NM9/U03/pYLhGyxy18QXO25WfrWd8XsUYx57tnxll2xxj54CN08uQ==", "shasum": "bde2369b13b7d34f6e35d2a7fc97d7e4936d0211", "tarball": "https://registry.npmjs.org/node-opus/-/node-opus-0.3.3.tgz", "fileCount": 431, "unpackedSize": 7580957, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdpd0tCRA9TVsSAnZWagAAnVkP/1P8kT7r/p0/v785vpya\nNG51zq7DiwKgLQXrqwdAv322aziEuoyH+2+TWuy9ue5znkF2ybvGwFC0AkMm\nds63+10IPfPKYB+g084QUWbXMwD/OZJON2/yIaJUu2jvfmx32A9JYSl9smM7\nPPGVOiwtJD7tZr7gTYMDyhd3um72AY98E0JL51tbL4yAsNp27FfF6S2Su9Io\n8jHtMlN70F6Jppr9K5o0YQOdsKAAd2ao54MqPjctfQiQLbLAM4C/PM9+gpoN\nqhnJaFjzPUeMenjLUy48VuZ6twze46BcvfLXr4Bwvrb63TMOWjLOmME9cFGH\nh41LjDFU65JQ7LxO8ZSETYEg7vGPBSCXGUz36Ym+lXRGxtZwIkE3b/hG8fIi\ntSTpD51VaSQSb99Ecyft3PX+HP135idwhIECUb8zMgvA4Dr1oHFjFp3Q+HQn\nwmLFl6obnblumrvtOgMO4CcMRABkvBf4tI2Jp5ZwAv9e5FJzKKqI/BG9MmMP\nhqCNfv2V36okLUfFcs796IH4qVOdl0Lw6v+Qz2tfKKvjo/AV/Lsi6wZiirBa\nFzustZ1sxXmXYDJHBzpeYMfcaMsm/OKKw1qplcRynCFXEVr1AX+sCpoMp542\nMbvj3UVOpy/SvzRZtDGL+4ew25b4fLibqPNY8+YygwybhX9cdZR9rF9iFdAq\ncTic\r\n=968Z\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCz6Eh2CgOPX+XGel3O9mQUtmOZ5lsmUDjr7dlJQBPy6gIgBG4M7PUp/Ma6IFZAcsypqqIKKPclIq+P2rZDXQHomog="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/node-opus_0.3.3_1571151148893_0.3602704474599021"}, "_hasShrinkwrap": false, "deprecated": "This project is unmaintained. See @discordjs/opus for an alternative."}}, "readme": "node-opus\n=========\n### NodeJS native bindings to libopus\n\nThis module implements bindings for Opus v1.1 for Node.js.\n\n```js\nvar opus = require('node-opus');\n\n// Create the encoder.\n// Specify 48kHz sampling rate and 10ms frame size.\n// NOTE: The decoder must use the same values when decoding the packets.\nvar rate = 48000;\nvar encoder = new opus.OpusEncoder( rate );\n\n// Encode and decode.\nvar frame_size = rate/100;\nvar encoded = encoder.encode( buffer, frame_size );\nvar decoded = encoder.decode( encoded, frame_size );\n\n// or create streams\nvar channels = 2;\nvar opusEncodeStream = new opus.Encoder(rate, channels, frame_size);\nvar opusDecodeStream = new opus.Decoder(rate, channels, frame_size);\n// see examples folder for a more complete example\n```\n\nPlatform support\n----------------\n\nSupported platforms:\n- Linux x64 & ia32\n- Linux ARM (Raspberry Pi 1 & 2)\n- Linux ARM64 (Raspberry Pi 3)\n- Mac OS X x64\n- Windows x64\n\n\nAdd new supported platforms by running ./autogen.sh and ./configure in\ndeps/opus and copying the resulting config.h to deps/config/opus/[os]/[arch].\n\nUse the following flags with configure:\n\n    ./configure --enable-static --disable-shared --with-pic\n\nOn a clean debian-based system, the full flow looks approximately like:\n\n\tsudo apt-get update\n\tsudo apt-get install autoconf\n\tsudo apt-get install libtool\n\tcd deps/opus\n\t./autogen.sh\n\t./configure --enable-static --disable-shared --with-pic\n\tmkdir -p ../config/opus/[os]/[arch]\n\tcp config.h ../config/opus/[os]/[arch]\n\nAnd, then, the last step is to add the OS/Arch to `package.json`.\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2022-06-21T17:41:20.947Z", "created": "2015-04-27T19:12:13.858Z", "0.1.0": "2015-04-27T19:12:13.858Z", "0.1.1": "2015-04-28T18:19:14.595Z", "0.1.2": "2015-04-28T20:54:34.206Z", "0.1.3": "2015-05-06T19:43:32.614Z", "0.1.4": "2015-05-06T21:01:27.903Z", "0.1.5": "2015-05-15T13:34:10.095Z", "0.1.6": "2015-05-19T20:51:38.050Z", "0.1.7": "2015-05-19T22:07:05.949Z", "0.1.8": "2015-05-19T22:12:55.829Z", "0.1.9": "2015-05-20T00:32:33.163Z", "0.1.10": "2015-07-06T14:44:34.278Z", "0.1.11": "2015-09-29T22:49:17.720Z", "0.1.12": "2016-01-23T14:05:42.642Z", "0.1.13": "2016-04-30T21:57:45.352Z", "0.2.0": "2016-06-05T21:15:26.562Z", "0.2.1": "2016-06-27T14:15:55.331Z", "0.2.2": "2016-09-29T20:56:26.067Z", "0.2.3": "2016-11-07T18:21:05.608Z", "0.2.4": "2016-11-08T11:52:03.067Z", "0.2.5": "2017-04-02T17:42:46.244Z", "0.2.6": "2017-04-07T21:01:45.757Z", "0.2.7": "2017-09-09T11:39:01.941Z", "0.2.8": "2018-05-06T11:11:15.335Z", "0.2.9": "2018-05-06T11:13:04.634Z", "0.3.0": "2018-05-06T12:06:31.520Z", "0.3.1": "2018-10-31T22:33:22.214Z", "0.3.2": "2019-05-27T16:20:11.114Z", "0.3.3": "2019-10-15T14:52:29.141Z"}, "homepage": "https://github.com/Rantanen/node-opus", "repository": {"type": "git", "url": "git+ssh://**************/Rantanen/node-opus.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/Rantanen/node-opus/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"angeloanan": true, "lord-chunk": true, "rocket0191": true}}