{"_id": "escape-string-regexp", "_rev": "58-82f264cfb34110ad48aaad6c4273cda0", "name": "escape-string-regexp", "description": "Escape RegExp special characters", "dist-tags": {"latest": "5.0.0"}, "versions": {"1.0.0": {"name": "escape-string-regexp", "version": "1.0.0", "description": "Escape RegExp special characters", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/escape-string-regexp"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["regex", "regexp", "re", "regular", "expression", "escape", "string", "str", "special", "characters"], "devDependencies": {"mocha": "*"}, "bugs": {"url": "https://github.com/sindresorhus/escape-string-regexp/issues"}, "homepage": "https://github.com/sindresorhus/escape-string-regexp", "_id": "escape-string-regexp@1.0.0", "_shasum": "0ca42ef5f3d8499fbc239fa0409ea849857d74c4", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "0ca42ef5f3d8499fbc239fa0409ea849857d74c4", "tarball": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.0.tgz", "integrity": "sha512-XTVnVdVq6UQDfLUzWJC36BLvIdQigTIaDu0PK2kMJF+HM+hpEdI/etmCu3/tfJgOaP0qIcJcppO936K4CWZhlw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCibB3/7KrMUB66sDs7Tm92DuSAWGKWOoKYlJU1RvJ3TAIgMhJoPJ2p1JOoIyxCqAkJDIsVgYBdgWzNCFDQ811dxvc="}]}, "directories": {}}, "1.0.1": {"name": "escape-string-regexp", "version": "1.0.1", "description": "Escape RegExp special characters", "license": "MIT", "repository": {"type": "git", "url": "git://github.com/sindresorhus/escape-string-regexp"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["regex", "regexp", "re", "regular", "expression", "escape", "string", "str", "special", "characters"], "devDependencies": {"mocha": "*"}, "gitHead": "7aba3933a8492584a30cba5bf6c54f7530a9d1b0", "bugs": {"url": "https://github.com/sindresorhus/escape-string-regexp/issues"}, "homepage": "https://github.com/sindresorhus/escape-string-regexp", "_id": "escape-string-regexp@1.0.1", "_shasum": "78c76393afb84f102230d949a6125c7f3cf65904", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "jbnicolai", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "dist": {"shasum": "78c76393afb84f102230d949a6125c7f3cf65904", "tarball": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.1.tgz", "integrity": "sha512-UlEKXCzxdhMPTv50tzcbMWdd48Arcnno04VVAHT+w8+CdIB5Ios9xm/WGCx4O4gfqOyygdbAzjmhzBx5so0G/Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGRo7s961uJBIzcUtuUNY5AXiO6iZ0G+4vYcCCAh7UogAiEApRPU+4KjiY1wGzaM5x0JqobnYEdZ+gp7xXADf+awMjQ="}]}, "directories": {}}, "1.0.2": {"name": "escape-string-regexp", "version": "1.0.2", "description": "Escape RegExp special characters", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/escape-string-regexp"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["regex", "regexp", "re", "regular", "expression", "escape", "string", "str", "special", "characters"], "devDependencies": {"mocha": "*"}, "gitHead": "0587ee0ee03ea3fcbfa3c15cf67b47f214e20987", "bugs": {"url": "https://github.com/sindresorhus/escape-string-regexp/issues"}, "homepage": "https://github.com/sindresorhus/escape-string-regexp", "_id": "escape-string-regexp@1.0.2", "_shasum": "4dbc2fe674e71949caf3fb2695ce7f2dc1d9a8d1", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "jbnicolai", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "dist": {"shasum": "4dbc2fe674e71949caf3fb2695ce7f2dc1d9a8d1", "tarball": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.2.tgz", "integrity": "sha512-cQpUid7bdTUnFin8S7BnNdOk+/eDqQmKgCANSyd/jAhrKEvxUvr9VQ8XZzXiOtest8NLfk3FSBZzwvemZNQ6Vg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJ7cVQaIaIjmjKU4VFUVGK2aGnYRYsm9tKi6umvye+QQIgIwKLXy2wLHqmwZmtibtLaP660U4yxdnwAbjZBaLBsqk="}]}, "directories": {}}, "1.0.3": {"name": "escape-string-regexp", "version": "1.0.3", "description": "Escape RegExp special characters", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/escape-string-regexp"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["regex", "regexp", "re", "regular", "expression", "escape", "string", "str", "special", "characters"], "devDependencies": {"mocha": "*"}, "gitHead": "1e446e6b4449b5f1f8868cd31bf8fd25ee37fb4b", "bugs": {"url": "https://github.com/sindresorhus/escape-string-regexp/issues"}, "homepage": "https://github.com/sindresorhus/escape-string-regexp", "_id": "escape-string-regexp@1.0.3", "_shasum": "9e2d8b25bc2555c3336723750e03f099c2735bb5", "_from": ".", "_npmVersion": "2.1.16", "_nodeVersion": "0.10.35", "_npmUser": {"name": "jbnicolai", "email": "<EMAIL>"}, "dist": {"shasum": "9e2d8b25bc2555c3336723750e03f099c2735bb5", "tarball": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.3.tgz", "integrity": "sha512-Moms4LX81Yz8bfx4CR+r2LQodaAV0SSUHKacBi9bylry3/+ipQX9uOah6/zGXRfBR553xSf1wuOskE22/gAryw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC2u4eKlbV5cXTRBK2NwKa2yWD6emYHZ9as/BlJdrEVWwIhAPoNI4Oe1KuhmRnB/YEMQSE5OGzla0XGQu2ukLOX659+"}]}, "directories": {}}, "1.0.4": {"name": "escape-string-regexp", "version": "1.0.4", "description": "Escape RegExp special characters", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/escape-string-regexp"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "engines": {"node": ">=0.8.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["escape", "regex", "regexp", "re", "regular", "expression", "string", "str", "special", "characters"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "e9ca6832a9506ca26402cb0e6dc95efcf35b0b97", "bugs": {"url": "https://github.com/sindresorhus/escape-string-regexp/issues"}, "homepage": "https://github.com/sindresorhus/escape-string-regexp", "_id": "escape-string-regexp@1.0.4", "_shasum": "b85e679b46f72d03fbbe8a3bf7259d535c21b62f", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b85e679b46f72d03fbbe8a3bf7259d535c21b62f", "tarball": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.4.tgz", "integrity": "sha512-/tdc8o79uSd6C+QB4iQOEfGAFT8Eek6QsO/xFgeULeij8OPp4/Xa4RRphv5lQoC0Frbxa3s9QFswlmBUFgs6mQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICJyssTVXYBz+GEoxlVZmruzeGCRjpaFQTTJeefHZ/EkAiEA3oRJx/cFTonvCtcpQOZKmCLhkuFEFcaYKhNC2okZQZA="}]}, "directories": {}}, "1.0.5": {"name": "escape-string-regexp", "version": "1.0.5", "description": "Escape RegExp special characters", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/escape-string-regexp"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "jbnicolai", "email": "<EMAIL>"}], "engines": {"node": ">=0.8.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["escape", "regex", "regexp", "re", "regular", "expression", "string", "str", "special", "characters"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "db124a3e1aae9d692c4899e42a5c6c3e329eaa20", "bugs": {"url": "https://github.com/sindresorhus/escape-string-regexp/issues"}, "homepage": "https://github.com/sindresorhus/escape-string-regexp", "_id": "escape-string-regexp@1.0.5", "_shasum": "1b61c0562190a8dff6ae3bb2cf0200ca130b86d4", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.6", "_npmUser": {"name": "jbnicolai", "email": "<EMAIL>"}, "dist": {"shasum": "1b61c0562190a8dff6ae3bb2cf0200ca130b86d4", "tarball": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDBnyETIz0cV9oONwgyi0fSb01KrkXtb2t0H+Y3vhoK+AiEA+w+HBfUrCb6fhyO2rhjg0KB4dX7Ro9aj8Fn9XqUsXl8="}]}, "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/escape-string-regexp-1.0.5.tgz_1456059312074_0.7245344955008477"}, "directories": {}}, "2.0.0": {"name": "escape-string-regexp", "version": "2.0.0", "description": "Escape RegExp special characters", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/escape-string-regexp.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["escape", "regex", "regexp", "re", "regular", "expression", "string", "str", "special", "characters"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "21c557c7f14a112eebe196abd98d085f5fcfcf5e", "bugs": {"url": "https://github.com/sindresorhus/escape-string-regexp/issues"}, "homepage": "https://github.com/sindresorhus/escape-string-regexp#readme", "_id": "escape-string-regexp@2.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==", "shasum": "a30304e99daa32e23b2fd20f51babd07cffca344", "tarball": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz", "fileCount": 5, "unpackedSize": 3259, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcttp2CRA9TVsSAnZWagAAH+gP/0jUFye+8JcZ4PlzibdD\nCYz15d9ULMk4brKKIDF8WvOSx+qrkGmQ9ryeyc5pIviaAUmU/gVim8G5vRiB\n8Osea8MHVebWkZNctNpYqS7zqb79RVUVbWDOP/VSr7Se0qdg+4VMo4LwQ1QN\nJ6QhswG5gON4uyWjR6Bpak5LGylVZf4WZLpaFho3LuScSOhnzvCEgFLwtKCf\nMJEnyZEiKB+ZJpJQzm0r3kGw8m8/4v7PjvVGg+lHCn+z5xEZlW0gNrUlrUnB\nMNkGsLDUv2gV3x3AnOeN2JtAOI0M4XWrp5XyiMF+KXTmYmItrx3PpRSOoVKs\nNwrTYpZK3LeqEKf6fsr0wTw/GW1BocBe4qAf+zVJ/se1txnM1YY2OkA+Mk3i\nBUvUCZN1GYkwuqbrhr1iPNv5ln9NbUHS9igHAuo49RlJ3khB0+MvNq4+7Hl2\nd4LdE9D+NW5qRyUfBGwbOdCkz/nFRGB/Ty38IqSPDYTta0gvnFVF0OiCFd6s\n6gF94/kgrIs18+wIpn1qmQOFXRyQAtaZF+T53+PG3XCQ4niBmakXjCl2QLpS\nQSjXAB+dNyYPl2pK1aVmtfclRBmFHXKHbfF6cr7eMJdte2LsTt9DL9l+HXL+\nGMf3gm9sURzxTxJp3udwyD5/c+P782djgu3X/gXTcMneRHnFjL59wgY4KVDN\n4OBO\r\n=x82n\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGYWRahVhWUXx02aRAxhJx5D+PJcDJXaIN6CC3xTF2vOAiEA97QlXedNdAUaxK2nGdKbtKD/p2eQcziB0SyQqeAULtE="}]}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escape-string-regexp_2.0.0_1555487349375_0.5654575039056717"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "escape-string-regexp", "version": "3.0.0", "description": "Escape RegExp special characters", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/escape-string-regexp.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["escape", "regex", "regexp", "regular", "expression", "string", "special", "characters"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.11.0", "xo": "^0.28.3"}, "gitHead": "d248d827a72f75d7bafbe58097b9a5971c560df7", "bugs": {"url": "https://github.com/sindresorhus/escape-string-regexp/issues"}, "homepage": "https://github.com/sindresorhus/escape-string-regexp#readme", "_id": "escape-string-regexp@3.0.0", "_nodeVersion": "10.19.0", "_npmVersion": "6.13.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-11dXIUC3umvzEViLP117d0KN6LJzZxh5+9F4E/7WLAAw7GrHk8NpUR+g9iJi/pe9C0py4F8rs0hreyRCwlAuZg==", "shasum": "1dad9cc28aed682be0de197280f79911a5fccd61", "tarball": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-3.0.0.tgz", "fileCount": 5, "unpackedSize": 3792, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejKp7CRA9TVsSAnZWagAAvH8P/jdWe7j3n/V9b70XfrTf\n5eVtdcAp5mWuj+kY1J+6OT/HmZfaQGWCwbtykn9mc5hE9sSlDqeIj1FiyPGW\nSVpbUrzAsPrLw6zGTdld4KWMS+s4e1P03sml/dAGrVOsbglUVoDA6mOEJPeC\nioQL7gPDBEj6hwne5Ti7d9g0gvj1fWl40D9O+BXotOoDlXiiRkWD5BfJXFPg\nz39wiMUCjPQUcqGMPATyKoZDEEKlfNd8f/gGG10zQfM6v6mVWVO5v9tGCTc2\n4Ol/3idL1TcO6WriQzgeoAOoAxz1kCpueKGP1YxP42LK6h6sp1WnCw43dEBx\n4ztsb+uaeXRoK7AMnvNyOtwdAuwl33dwTjGfSPht1Gt8Zt+C/eqDixbRGom1\nJrojLMlFjxHTZ6K+CM1mvbEQQvsveVID8sByQKGYF/x9IwGk7jcvABDn4Im0\nLP1xBWQ4UxdnNxGoxPO25QXra5kpR9hT736xCjT+TCvlSevhVw2OkoJo2Wi2\nFNjP3Kz/StoQS8NqXNvAEY9KCQwvGCYZ8hMXSPH4DvFFHRu9QNm0xvNIjjTC\n9VPoiaizlb8Fe6UI6X7YC5x5SNxYnUe0nTlShmii2y7wFF6XrMYqjLQo09Rj\naIeZkxYudMEsAERDwntwq5HJbt1FZG3C+y3GqhsYTqtEkWUFJjQlY5ZtCyss\naPht\r\n=jjje\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIASA5LFIqwLHTo1ntXocNCA+nJUZBJpfiQU97OnJlETLAiEAxxH4/ksEsvNS26d6cEMstDBuShUEVVy0oPXD8g0dFTo="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escape-string-regexp_3.0.0_1586276986717_0.39881831235287324"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "escape-string-regexp", "version": "4.0.0", "description": "Escape RegExp special characters", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/escape-string-regexp.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["escape", "regex", "regexp", "regular", "expression", "string", "special", "characters"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.11.0", "xo": "^0.28.3"}, "gitHead": "71083e89129bcf72a10656508de5442d3f97966d", "bugs": {"url": "https://github.com/sindresorhus/escape-string-regexp/issues"}, "homepage": "https://github.com/sindresorhus/escape-string-regexp#readme", "_id": "escape-string-regexp@4.0.0", "_nodeVersion": "10.20.1", "_npmVersion": "6.14.4", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==", "shasum": "14ba83a5d373e3d311e5afca29cf5bfad965bf34", "tarball": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "fileCount": 5, "unpackedSize": 3790, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeoURNCRA9TVsSAnZWagAARCoQAIDrgan6KgXDUu7sPzbz\n9oiZFBPzKrp+vRltzovOSs/js5mGWY2aXUf0H2a8flCfE2kpMboKUOOTcUJ3\nWVDsgIhSr8KM5MP2oM7uWXV9ZamRp+ksHjQbumBU2a8OxakiBLQv6o6eF3DB\nIzZXYD1xb7BoUpHzMOgClabNeXqxgRsOPp3xkb04Q8Y5GotUZKPwdLhnf+io\n2SpW5xUEVzQSQSCqzpzqjyGQcwAlesqqUWc/INud8/suDEIpHSeeOv+dhhmo\nwo5v7x/Nw3uvGmDycwJ/UoTXVh9GXmy3EtY9NyRmdtThRl3MqQ6xi3z9FQ0b\nicl744RN+cexmiiXuaVkMccklpMAbNMX62VpRNMhPUG99HpUe/aAbRqGidDr\n2GTW+4D1JtCZq2yqyH91CTN0gqMZQOEeS5jS0qH0HGedKLZFynQAjSyNWevu\nra4MwFwC4jLbT/iNHE8/PWImYL8dMxKzXs7roPETZPOQuQhab3eAbOylS2dH\n2MY0HdtX0nhLyoM6jdBgCmGKwSuzVqAPeAUoke7ft75ez2WAwcPLK4yaobKO\n/4FYnpTTs5BAH403TGhuyl9mAmk7VbrCiNLUf8d5uHzMifO7bmgeZ0185M02\nGoqJxnF04MCN7cJ4wvtfJh+eAyQJXrXikEcOhJrMFth+NkwhqlFx62cZbcrE\nycp7\r\n=uG6w\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCtnB6D8NP2XpnmL7e2IvMJPdzEG2W8Z4U66+e2oVsQXQIhAPL8R6cRFx2F7KiRUjaUIdU7VF6vZsJjx21isqRe7RX+"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escape-string-regexp_4.0.0_1587627085362_0.5996192452086115"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "escape-string-regexp", "version": "5.0.0", "description": "Escape RegExp special characters", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/escape-string-regexp.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["escape", "regex", "regexp", "regular", "expression", "string", "special", "characters"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "gitHead": "ba9a4473850cb367936417e97f1f2191b7cc67dd", "bugs": {"url": "https://github.com/sindresorhus/escape-string-regexp/issues"}, "homepage": "https://github.com/sindresorhus/escape-string-regexp#readme", "_id": "escape-string-regexp@5.0.0", "_nodeVersion": "12.22.1", "_npmVersion": "6.14.10", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw==", "shasum": "4683126b500b61762f2dbebace1806e8be31b1c8", "tarball": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-5.0.0.tgz", "fileCount": 5, "unpackedSize": 3659, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgewKuCRA9TVsSAnZWagAA/R8QAKIjcAQrtpAGFWhms0M2\nudKffrknPQbQXcgJkJJrZEvxGcUk9Hvcka+R34RHwhm3jYtd3vubQk/AY4qR\n6IgtVJ6yuqq1Ev9NoeniZs6Mn6iS0nqluTK8YoZnupTJHAk/idO+SrW9F1B3\nEOzaRyhPCGaImNRCwNVy1RKXG67kzccqUGdqufz5sui1L0kt7kCvhUYRBQ44\n7iOxLRwTE02PpxyCN59jMEkxP26wAmVmyMMXuu5zOhUHZLX9hgL4AeTdeBrc\n8rP/VDYcck1tDYRHJnhB5VKi+xKqaV0TTF5s0dMJ/AgPoNeYpn9YNEEFZBt7\n3OqzJFm5tt2GwQ6NTh0O9bsGzxyco1wBLGWqUTWBf0mEwtZayLL3OKTNxZFl\nMC5hk8T1NrrVAOmj+Fdwk/eXJ8afhbiPVM8EDMgHFhgVFkjCfH7EN8MR4rs+\nJb2iVZX4F0RPFgmLUhu8cpdrB3+p27tBkeyoSHF35tV4W7LGuVhEMeYLy+HO\nwYTIa5ifCGckRcOVaxSyONM5Dtkv8RCGU5YYz9KXqwiFqtEc+arD43n3vro4\nnq7ZxBHrX0F220uhaxMFqNr1E66UAb9Dfoh25hjdt5U7zNdKQ7kQE/vEfz3U\ngcuO4H7PfM++Y1ZJrENaFM/an9+2xs0m8pmM61EWXM5WCA98/ALZ+PDCLDwK\n8+1q\r\n=iLSV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDriPVVBNO+MIoPnK6z99gUKJJG33qsCk8gF+RolC058wIgA6LiFjLoZbITiGeW3IjuuZtERJD9VzL2kRYgNwF08Ac="}]}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/escape-string-regexp_5.0.0_1618674350223_0.2814730865531563"}, "_hasShrinkwrap": false}}, "readme": "# escape-string-regexp\n\n> Escape RegExp special characters\n\n## Install\n\n```\n$ npm install escape-string-regexp\n```\n\n## Usage\n\n```js\nimport escapeStringRegexp from 'escape-string-regexp';\n\nconst escapedString = escapeStringRegexp('How much $ for a 🦄?');\n//=> 'How much \\\\$ for a 🦄\\\\?'\n\nnew RegExp(escapedString);\n```\n\nYou can also use this to escape a string that is inserted into the middle of a regex, for example, into a character class.\n\n---\n\n<div align=\"center\">\n\t<b>\n\t\t<a href=\"https://tidelift.com/subscription/pkg/npm-escape-string-regexp?utm_source=npm-escape-string-regexp&utm_medium=referral&utm_campaign=readme\">Get professional support for this package with a Tidelift subscription</a>\n\t</b>\n\t<br>\n\t<sub>\n\t\tTidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.\n\t</sub>\n</div>\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-04-25T16:42:41.472Z", "created": "2014-06-24T15:06:23.496Z", "1.0.0": "2014-06-24T15:06:23.496Z", "1.0.1": "2014-07-12T00:34:58.761Z", "1.0.2": "2014-09-13T15:50:57.511Z", "1.0.3": "2015-02-22T09:22:10.197Z", "1.0.4": "2015-12-19T22:16:02.473Z", "1.0.5": "2016-02-21T12:55:17.074Z", "2.0.0": "2019-04-17T07:49:09.559Z", "3.0.0": "2020-04-07T16:29:46.974Z", "4.0.0": "2020-04-23T07:31:25.491Z", "5.0.0": "2021-04-17T15:45:50.334Z"}, "homepage": "https://github.com/sindresorhus/escape-string-regexp#readme", "keywords": ["escape", "regex", "regexp", "regular", "expression", "string", "special", "characters"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/escape-string-regexp.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/escape-string-regexp/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"julien-f": true, "jbnicolai": true, "zhangyaochun": true, "voxpelli": true, "mlm": true, "stretchgz": true, "koulmomo": true, "bsara": true, "princetoad": true, "ex-machine": true, "eliranmal": true, "danielbankhead": true, "mojaray2k": true, "arteffeckt": true, "kael": true, "fleischer": true, "asaupup": true, "bplok20010": true, "jaeger": true, "fatelei": true, "sidwood": true, "infernocloud": true, "bart1208": true, "giovannif23": true, "zhenguo.zhao": true, "heineiuo": true, "nuwaio": true, "chrisyipw": true, "kodekracker": true, "shuoshubao": true, "tedyhy": true, "hugovila": true, "sherylhohman": true, "tommytroylin": true, "kvrao": true, "71emj1": true, "flumpus-dev": true}}