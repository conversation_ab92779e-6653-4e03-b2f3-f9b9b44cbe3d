{"_id": "micromatch", "_rev": "137-0ff5cf5471b5fa224949ae1da0d0109d", "name": "micromatch", "dist-tags": {"latest": "4.0.8"}, "versions": {"0.1.0": {"name": "micromatch", "version": "0.1.0", "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "_id": "micromatch@0.1.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "f58fc1198dc1ba5b97cc2e301d28f0f3ee05ca2f", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-0.1.0.tgz", "integrity": "sha512-GQRzYCrGSoSGGU3QgckEij+psNPgh2N/7EGbW7q92WE9jMmRjAsrEH4pcioy62cNFsOJP2jxURvfQ2O6jQbEMA==", "signatures": [{"sig": "MEQCIFAbX1/2+RqDVCvYAcvICtlS0QMgxVb0P+hp3jHn3tKnAiB20Bnle9M8i96wPZw019hL1G3fGZ2q6Vc703ZBYK8eZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "f58fc1198dc1ba5b97cc2e301d28f0f3ee05ca2f", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "licenses": [{"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE-MIT", "type": "MIT"}], "repository": {"url": "git://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "1.4.9", "description": "Like minimatch, but smaller and faster.", "directories": {}, "devDependencies": {"mocha": "*", "should": "*"}}, "0.2.0": {"name": "micromatch", "version": "0.2.0", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "glob", "globstar", "match", "matches", "minimatch", "multimatch", "path", "regex", "regular", "shell"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE-MIT", "type": "MIT"}, "_id": "micromatch@0.2.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "e0b82107687dfd6e4ff8f085c62adf8a61a24ae3", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-0.2.0.tgz", "integrity": "sha512-vM+tW7aGPXW3tipt1SKW9rUnkAbcXVn8k+qlM2LFD4V3n9RdkdQoslfKwaXWfqaFRQ8NmVH5PUrW5czOewn/WQ==", "signatures": [{"sig": "MEUCIQD1ydC6JxOigfY6OPeyM7aTZDTt/SBeoG3pexyeVoYxnwIgHltI3fLyHOFcn69LXBiZr37HZ/e3k48Lyt8vxyzJmtc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "e0b82107687dfd6e4ff8f085c62adf8a61a24ae3", "engines": {"node": ">=0.10.0"}, "gitHead": "6bb4f0d1058313128ef8a9a3bdafa4457e990da3", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "Glob matching for javascript/node.js. Like minimatch, but 10-40x faster.", "directories": {}, "dependencies": {"braces": "^0.1.5", "unixify": "^0.1.0", "arr-diff": "^0.2.2", "arr-union": "^1.0.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimatch": "^2.0.1", "benchmarked": "^0.1.3"}}, "0.2.1": {"name": "micromatch", "version": "0.2.1", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "glob", "globstar", "match", "matches", "minimatch", "multimatch", "path", "regex", "regular", "shell"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE-MIT", "type": "MIT"}, "_id": "micromatch@0.2.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "2a8684d31401b7d4d4fd70890eda6ab53b3f8260", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-0.2.1.tgz", "integrity": "sha512-zsOlEJzty2Qa2FwqDmLb1n9bTx22omw617rWbejid4ikoFQy+aMhYuty11CBvUvspABurrCsL2ql2xZCeTrOGg==", "signatures": [{"sig": "MEUCIQCD/b4XixJMn24mXt0yX6xoHMsKeyWnTFYFrxNxEaC+7wIgbTL0iZki9OhSQBoDyWUo6rGnrXi0QttYrvQCjIu43Jw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "2a8684d31401b7d4d4fd70890eda6ab53b3f8260", "engines": {"node": ">=0.10.0"}, "gitHead": "27cf83acf882c41a2849690a88fa7634610be116", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "Glob matching for javascript/node.js. Like minimatch, but 10-40x faster.", "directories": {}, "dependencies": {"braces": "^0.1.5", "unixify": "^0.1.0", "arr-diff": "^0.2.2", "arr-union": "^1.0.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimatch": "^2.0.1", "benchmarked": "^0.1.3"}}, "0.2.2": {"name": "micromatch", "version": "0.2.2", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "glob", "globstar", "match", "matches", "minimatch", "multimatch", "path", "regex", "regular", "shell"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE-MIT", "type": "MIT"}, "_id": "micromatch@0.2.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "228dc16354377e1b2dc2e4114b7fc53219b8ae57", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-0.2.2.tgz", "integrity": "sha512-E7LJruEuTXuqjc8g1trRpxh1Ak9DQOj3CedFRFhpNi6KRwYPC5xDJg/dudWkkTsnNYdmonoC5d1X64hGsjP/sg==", "signatures": [{"sig": "MEYCIQDRuCiXSZA7Wps4PfJVLuCjZDw1viOCb6v0kdFiCUxjIQIhAOKiCHdA7ekgzgh/BXEhzcEAtw5mr4I8niAfqNe4Dp1D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "228dc16354377e1b2dc2e4114b7fc53219b8ae57", "engines": {"node": ">=0.10.0"}, "gitHead": "4b56e1baf97100d506cc1abf53044588a6e89065", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "Glob matching for javascript/node.js. Like minimatch, but 10-40x faster.", "directories": {}, "dependencies": {"braces": "^1.0.0", "unixify": "^0.1.0", "arr-diff": "^0.2.2", "arr-union": "^1.0.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimatch": "^2.0.1", "benchmarked": "^0.1.3"}}, "1.0.0": {"name": "micromatch", "version": "1.0.0", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "glob", "globbing", "globstar", "match", "matches", "matching", "minimatch", "multimatch", "path", "regex", "regexp", "regular", "shell"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE-MIT", "type": "MIT"}, "_id": "micromatch@1.0.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "57ddb18dc0edfb6ee882a77581afb61970906b85", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.0.0.tgz", "integrity": "sha512-5XdaJhe3HhSWkvvHy729IhIaLqccppEckCBAXXmOOzeimGekqLOXCisJpXh6Yek+QdvVXgOtdqK0BRPUI5wUdA==", "signatures": [{"sig": "MEQCIC7P8d/ZYzVnO65ZV+QE3s+BYVvZTN2MgHEJ2brv88xOAiBDOsNCurdzJpx6qa+7Kk6WfuYa/vRiZQLrbssBplOn1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "57ddb18dc0edfb6ee882a77581afb61970906b85", "engines": {"node": ">=0.10.0"}, "gitHead": "b29259f63b80a977d0789c4032b22282b14d52e7", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "Glob matching for javascript/node.js. A faster alternative to minimatch (10-20x faster on avg), with all the features you're used to using in your Grunt and gulp tasks.", "directories": {}, "dependencies": {"braces": "^1.0.0", "unixify": "^0.1.0", "arr-diff": "^1.0.1", "arr-union": "^1.0.0", "filename-regex": "^0.1.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3"}}, "1.0.1": {"name": "micromatch", "version": "1.0.1", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE-MIT", "type": "MIT"}, "_id": "micromatch@1.0.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "d22539682fdd903b70df220018d8ac0defeb8434", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.0.1.tgz", "integrity": "sha512-juRHcNORljLfREsoh0AW9oTq8ViIssD4VQzhwiPJEMWyOHLXC9y5CEIUQ72TIjgQQPrPWnXIoL17Or4Z9KeKOg==", "signatures": [{"sig": "MEQCIG4vRT8Lrc0bsW1E/fcp5s2vZoqao2LmM20y0zYZbYAOAiBwAHeHWFrGvNVQKzb//5QDQrBG0FsRDEzr8N1wFBA0TA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "d22539682fdd903b70df220018d8ac0defeb8434", "engines": {"node": ">=0.10.0"}, "gitHead": "0577c2d4a143ea77c5bb9949c2af2ea0e190d853", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "1.4.23", "description": "Glob matching for javascript/node.js. A faster alternative to minimatch (10-20x faster on avg), with all the features you're used to using in your Grunt and gulp tasks.", "directories": {}, "dependencies": {"braces": "^1.0.0", "unixify": "^0.1.0", "arr-diff": "^1.0.1", "arr-union": "^1.0.0", "filename-regex": "^0.1.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3"}}, "1.2.0": {"name": "micromatch", "version": "1.2.0", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE-MIT", "type": "MIT"}, "_id": "micromatch@1.2.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "771a95449e4c2573f4c62df684c9a3fe430ad226", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.2.0.tgz", "integrity": "sha512-jg3y77ZoUZtsAs/OPzIRoSx75piKdZymF2L8sz0uSUJG18w5LsmaQNgV9ZdGsJunyKndAFa/fgJFFazKN3yxUA==", "signatures": [{"sig": "MEUCIQDLNNIp6IXXaROUBwJXol0pWNGjbn7M0c7jz9Ii5eX9pwIgMDKsc/yz9rtMJkSZSCt+nK8eCVzxb93+8noCaIrmRaU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib"], "_shasum": "771a95449e4c2573f4c62df684c9a3fe430ad226", "engines": {"node": ">=0.10.0"}, "gitHead": "7944b878f1202cd36fe04fae3332a007e7b30e01", "scripts": {"test": "mocha -R spec"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Glob matching for javascript/node.js. A faster alternative to minimatch (10-20x faster on avg), with all the features you're used to using in your Grunt and gulp tasks.", "directories": {}, "dependencies": {"braces": "^1.4.0", "is-glob": "^1.1.0", "arr-diff": "^1.0.1", "filename-regex": "^2.0.0", "glob-path-regex": "^1.0.0"}, "devDependencies": {"chalk": "^0.5.1", "write": "^0.1.1", "should": "^4.6.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}}, "1.2.2": {"name": "micromatch", "version": "1.2.2", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE-MIT", "type": "MIT"}, "_id": "micromatch@1.2.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "d38c0ea77731a1714c46d273469cd31322ea02ef", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.2.2.tgz", "integrity": "sha512-ZLecoVApC2mZVyf++chmRd0c9j7Csj/xMQv7gX7PRdC5CWkmn8aRRvilazTp3ZEJ+Ma8xpa9p6jbDx3AFYrRXg==", "signatures": [{"sig": "MEUCIGme+UKyy4cfk6Mcw9ZaSFg2BZ6VW3RhEVfzyrOIw2ArAiEAjSxjPMWr8JWIvFok6C2NUhN4Jr8s8g3SKhKRR/tz3lI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "d38c0ea77731a1714c46d273469cd31322ea02ef", "engines": {"node": ">=0.10.0"}, "gitHead": "b246a926768b299773b0ecdc2c959c8ce30dd1c3", "scripts": {"test": "mocha -R spec", "benchmark": "node benchmark"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Glob matching for javascript/node.js. A faster alternative to minimatch (10-20x faster on avg), with all the features you're used to using in your Grunt and gulp tasks.", "directories": {}, "dependencies": {"braces": "^1.6.0", "is-glob": "^1.1.0", "arr-diff": "^1.0.1", "filename-regex": "^2.0.0", "glob-path-regex": "^1.0.0"}, "devDependencies": {"chalk": "^0.5.1", "write": "^0.1.1", "should": "^4.6.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}}, "1.3.0": {"name": "micromatch", "version": "1.3.0", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE", "type": "MIT"}, "_id": "micromatch@1.3.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "62099439ad54fcad188dc013e184eb7261c68f55", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.3.0.tgz", "integrity": "sha512-b1TBX7YDexNCqZwtxrRy5yTXbBxA8O1GEv1i4d218OdIdIjOBr7z+xQzkn27Qaxm5FbkB8tN4t5mxtO0RZzOVQ==", "signatures": [{"sig": "MEUCIQCsMpSps4V8HS7KKFKmti4AJodQNPm/ukPc41yKMx9yRgIgLt8S0G8D0TWToo3Fi0O464rNB89mshsNyOGA3jgQwiE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "62099439ad54fcad188dc013e184eb7261c68f55", "engines": {"node": ">=0.10.0"}, "gitHead": "31745fbb22545b5226dd73987768abc6f25c3df8", "scripts": {"test": "mocha", "benchmark": "node benchmark"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Glob matching for javascript/node.js. A faster alternative to minimatch (10-45x faster on avg), with all the features you're used to using in your Grunt and gulp tasks.", "directories": {}, "dependencies": {"braces": "^1.6.0", "extglob": "^0.2.0", "is-glob": "^1.1.0", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^1.2.0", "regex-cache": "^0.2.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.0", "glob-path-regex": "^1.0.0"}, "devDependencies": {"chalk": "^0.5.1", "write": "^0.1.1", "should": "^4.6.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}}, "1.3.1": {"name": "micromatch", "version": "1.3.1", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE", "type": "MIT"}, "_id": "micromatch@1.3.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "f96117bc7c34062da5cebd721af5c40b402052cd", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.3.1.tgz", "integrity": "sha512-z5/VwgKwSZNcjsVqtaqgGamNYJzxOM1EKHICHkWUjhBe+P3Z+/EUf0Ak0KnbCBCTt0U4CCZksyurrspGaXxgsA==", "signatures": [{"sig": "MEUCIFvjmisMnG2HiHy9WjV9FvvEC5iRPxnXjvW5TvXnJ3nrAiEA21jgrsezns/oVPMr57bEOZYDMFxjwMu4k0x2iYgZZ6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "f96117bc7c34062da5cebd721af5c40b402052cd", "engines": {"node": ">=0.10.0"}, "gitHead": "0d38859dff6bdf21ab35d2ca9cd07c9f86aa96ff", "scripts": {"test": "mocha", "benchmark": "node benchmark"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "1.4.28", "description": "Glob matching for javascript/node.js. A faster alternative to minimatch (10-45x faster on avg), with all the features you're used to using in your Grunt and gulp tasks.", "directories": {}, "dependencies": {"braces": "^1.6.0", "extglob": "^0.2.0", "is-glob": "^1.1.0", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^1.2.0", "regex-cache": "^0.2.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.0", "glob-path-regex": "^1.0.0"}, "devDependencies": {"chalk": "^0.5.1", "write": "^0.1.1", "should": "^4.6.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}}, "1.3.2": {"name": "micromatch", "version": "1.3.2", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE", "type": "MIT"}, "_id": "micromatch@1.3.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "5706c4cb24c7040fc3f57a2b464f76f112a5b6b9", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.3.2.tgz", "integrity": "sha512-317eXxI7Ql8ucklUPqigrllSwFOYfQHKWrFMZMhPYj69JfxUaKyIeEFbjX5l8oMxg3J7CXrzTM/emt8FP2HLeA==", "signatures": [{"sig": "MEUCIQC3hQOAg3mdVZ3jZe6Wjm05cayPBWgz6oU5RN9fMD69zQIgB1RvgX6xuHcXfe65w6FkSmFbhsIuFMnF1efgrZZ1USY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "5706c4cb24c7040fc3f57a2b464f76f112a5b6b9", "engines": {"node": ">=0.10.0"}, "gitHead": "696e223f68eef4cc6756ec0299010ec16731b367", "scripts": {"deps": "deps -e wip,benchmark", "test": "mocha", "benchmark": "node benchmark"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Glob matching for javascript/node.js. A faster alternative to minimatch (10-45x faster on avg), with all the features you're used to using in your Grunt and gulp tasks.", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.0", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^1.2.0", "regex-cache": "^0.2.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^0.5.1", "write": "^0.1.1", "should": "^4.6.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}}, "1.3.3": {"name": "micromatch", "version": "1.3.3", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE", "type": "MIT"}, "_id": "micromatch@1.3.3", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "86985e4d3dc01d71cab4ec63a3e317c2d129d268", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.3.3.tgz", "integrity": "sha512-k0wScZxxJwPc5on6eBJn0Z3m1LfbSEYpZVWnisV8je1xUlzQm7Og2RFfSDM0FzKbIGU+38IW9rW5QKCZK5mJNA==", "signatures": [{"sig": "MEUCIQD9I3q14wWuAvSAXiBzyZF5Li7lmxQy3VHy0E26XlstEAIgMFwm4nMtjRFpCAHT5r8rdg7bncripGjnWtTUj05ixO8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "86985e4d3dc01d71cab4ec63a3e317c2d129d268", "engines": {"node": ">=0.10.0"}, "gitHead": "696e223f68eef4cc6756ec0299010ec16731b367", "scripts": {"deps": "deps -e wip,benchmark", "test": "mocha", "benchmark": "node benchmark"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Glob matching for javascript/node.js. A faster alternative to minimatch (10-45x faster on avg), with all the features you're used to using in your Grunt and gulp tasks.", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.0", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^1.2.0", "regex-cache": "^0.2.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^4.6.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}}, "1.4.0": {"name": "micromatch", "version": "1.4.0", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE", "type": "MIT"}, "_id": "micromatch@1.4.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "13f9f50b37d31a7138f5c1cdc1e72769083505c5", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.4.0.tgz", "integrity": "sha512-ibVVM8/ZXS3HN6rQPF7dgsnn25E9aVF+1kNNqJ9JqQuOksYicAXznF/MOu2gxSa2BvKLpW119+8ileZpqEpdjQ==", "signatures": [{"sig": "MEUCIEHGOjZJQysFXwP9bAZwLHFP6spnPEZTbzMQ+VQEqPmrAiEArQDYZ+BI0bSRVQrgIg/RvvtbafwgdPJ+wcRe8ygRbdo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "13f9f50b37d31a7138f5c1cdc1e72769083505c5", "engines": {"node": ">=0.10.0"}, "gitHead": "969847ec8d508435d6606b124e34c3d9fe470991", "scripts": {"deps": "deps -e wip,benchmark", "test": "mocha", "benchmark": "node benchmark"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Glob matching for javascript/node.js. A faster alternative to minimatch (10-45x faster on avg), with all the features you're used to using in your Grunt and gulp tasks.", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.0", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^2.0.0", "regex-cache": "^0.2.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^4.6.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}}, "1.4.1": {"name": "micromatch", "version": "1.4.1", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE", "type": "MIT"}, "_id": "micromatch@1.4.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "cc7a81a85441657fdc36de1db9235724e55a6124", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.4.1.tgz", "integrity": "sha512-zzEWNHeBUbNWPhCFz5r7VZ4npFiTEPVgREtAT/KhRHnVvK14dCs9n+tYZE+o3jeG48Ld6apXkny5w0DvckUzWQ==", "signatures": [{"sig": "MEQCIF3xJoZgFotpsGuNLNH+QBZLOonYjFdx2w0sC+dc7YM+AiB8hN/VzNQaHBC8QFfI1xvMTo38JtL+JLRM7wZ3L13nPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "cc7a81a85441657fdc36de1db9235724e55a6124", "engines": {"node": ">=0.10.0"}, "gitHead": "eccda2a62a3014425bd75d7d4582c7eba2f36768", "scripts": {"deps": "deps -e wip,benchmark", "test": "mocha", "benchmark": "node benchmark"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Glob matching for javascript/node.js. A faster alternative to minimatch (10-45x faster on avg), with all the features you're used to using in your Grunt and gulp tasks.", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.0", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^2.0.0", "regex-cache": "^0.2.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^4.6.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}}, "1.4.2": {"name": "micromatch", "version": "1.4.2", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@1.4.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "1b1d1b9ef357c01a33239b170894709b037fc085", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.4.2.tgz", "integrity": "sha512-VNu46T/+GcM841nBmivGaH/j7h/U4IYvlZXgBTD2SjQtj5D59MhjPDStWfggqeflZZqqSCaw1BcyA8ZzDkMysQ==", "signatures": [{"sig": "MEUCIQCnLdq5DVOoc5Z3N0JQ89X7npV1IAguFQkG1J/bTAbXfwIgA49aNXcCJQAIaUG8S9/sxGXNBz8m4ROaHhb+peezWRo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "1b1d1b9ef357c01a33239b170894709b037fc085", "engines": {"node": ">=0.10.0"}, "gitHead": "2a8f1d9b273d680e0ec836a9cfa6e21fb95b3edd", "scripts": {"deps": "deps -e wip,benchmark", "test": "mocha", "benchmark": "node benchmark"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jonschlinkert/micromatch", "type": "git"}, "_npmVersion": "2.5.1", "description": "Glob matching for javascript/node.js. A faster alternative to minimatch (10-45x faster on avg), with all the features you're used to using in your Grunt and gulp tasks.", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.0", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^2.0.0", "regex-cache": "^0.2.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^4.6.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}}, "1.4.3": {"name": "micromatch", "version": "1.4.3", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@1.4.3", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "853731f751b2ca52e04fdf8ee429e997c1d488f5", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.4.3.tgz", "integrity": "sha512-QrM7/3azDMr6wx++iAlfJOCIXdm1Esk4dpkyRCR1/ApMgu3wO5XjCfjCJmIc8btt7HFjaATKAqAeKZ3BIS1Wiw==", "signatures": [{"sig": "MEUCIQD8Riz8+e8lB1ZLaHhIoeUmcEbCaoCF0rZuYKtwzeqLEQIgWAGFQNl2VGbbTcQRYkZlg8rY5rkXOb/o+QhRJz35xlg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "853731f751b2ca52e04fdf8ee429e997c1d488f5", "engines": {"node": ">=0.10.0"}, "gitHead": "1188da22066d7e5fbdbac62bfb3ef233200d05f4", "scripts": {"test": "mocha", "benchmark": "node benchmark"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jonschlinkert/micromatch", "type": "git"}, "_npmVersion": "2.5.1", "description": "Glob matching for javascript/node.js. A faster alternative to minimatch (10-45x faster on avg), with all the features you're used to using in your Grunt and gulp tasks.", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^2.0.1", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}}, "1.4.4": {"name": "micromatch", "version": "1.4.4", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@1.4.4", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "24b2fe56e81ff7701e6290f39a1e88d143402b1f", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.4.4.tgz", "integrity": "sha512-C6mVKf8OPUIqdVGlShQ+5d8jjicC72Qe2maAg7Treiwr6uEwDsPsAF/gG4hmv0GWo6te0g3M0ui9z6ld1s5SbA==", "signatures": [{"sig": "MEUCIFCPUbLE41VAAP/xSh8Mnj+TBAa4UGiilLwMit/aNJjyAiEA2eviqAsvOZgK43jvwMto4WzFAYNE2UVasSad6AKlUiI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "24b2fe56e81ff7701e6290f39a1e88d143402b1f", "engines": {"node": ">=0.10.0"}, "gitHead": "8f9026650351d1ed9717dcb34dc890afa98c5a6f", "scripts": {"test": "mocha", "benchmark": "node benchmark"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jonschlinkert/micromatch", "type": "git"}, "_npmVersion": "2.5.1", "description": "Glob matching for javascript/node.js. A faster alternative to minimatch (10-45x faster on avg), with all the features you're used to using in your Grunt and gulp tasks.", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^2.0.1", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}}, "1.4.5": {"name": "micromatch", "version": "1.4.5", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@1.4.5", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "eced3fa4cc87a8a8f32c142e238c17670600769f", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.4.5.tgz", "integrity": "sha512-BgoJz5iwA+8cw3XUZL2eIFuZcpcPUcZDCfGwhyOCoUnem5yhAq3hDNCfU9h0ma8spNZxBbuCM6JvKd7z56OVtA==", "signatures": [{"sig": "MEUCIQDyXba9VSRsl3TV8vAItTZVoyjkhkvvdKuoOwgf+BVkzQIgDUMIVSmnazyYu+02xo/HhF+MiCO+Vb1hEpe97ZQ3g7I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "eced3fa4cc87a8a8f32c142e238c17670600769f", "engines": {"node": ">=0.10.0"}, "gitHead": "db421469eccb20ff4cdf25f6d665f53fcd89ad5b", "scripts": {"test": "mocha", "benchmark": "node benchmark"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jonschlinkert/micromatch", "type": "git"}, "_npmVersion": "2.5.1", "description": "Glob matching for javascript/node.js. A faster alternative to minimatch (10-45x faster on avg), with all the features you're used to using in your Grunt and gulp tasks.", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^2.0.1", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}}, "1.5.0": {"name": "micromatch", "version": "1.5.0", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE", "type": "MIT"}, "_id": "micromatch@1.5.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "50bd67d41604ad1b749249c055abadee914a5ebb", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.5.0.tgz", "integrity": "sha512-FHsVECj5JHEYp/XTU8bJGxJ30YUpBMY1hMMTLxQMCriDFjvox5O3S6Ze/m6rM71D9J24hL3iK3NHsw9cFoIi6A==", "signatures": [{"sig": "MEUCIQDpsZ4YkkoNQ25W5FT/oizhNEiZywk3z2S1FHn3bS/UMwIgSUPJhNMLa0QniFHNd+e8JfPzd72CxPCOF/YhKM/C2Kc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "50bd67d41604ad1b749249c055abadee914a5ebb", "engines": {"node": ">=0.10.0"}, "gitHead": "5ae218e5bf2e672030f135ab0001581897248ec4", "scripts": {"test": "mocha", "benchmark": "node benchmark"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Glob matching for javascript/node.js. A faster alternative to minimatch (10-45x faster on avg), with all the features you're used to using in your Grunt and gulp tasks.", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^2.0.1", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}}, "1.6.0": {"name": "micromatch", "version": "1.6.0", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE", "type": "MIT"}, "_id": "micromatch@1.6.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "0e5c87d8ea63a02da6d5706c9c4cc3c753129bfc", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.6.0.tgz", "integrity": "sha512-RvJfr0+4VSJFc2LhJu+VOaSjQwvMbDoYFssaJspunSOHHjnn7C57JPNY7SBd8wUBLiAhBKjYiZ/cyw81se9QGg==", "signatures": [{"sig": "MEUCIQCBpfsSaXsjbpgmLlHAbvbW6u+ihlaP2XDg73ro70hJKAIgVyEARVQgPlwgr/L+CfSEGvMhVHLxxYxptWSHVyubFRI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "0e5c87d8ea63a02da6d5706c9c4cc3c753129bfc", "engines": {"node": ">=0.10.0"}, "gitHead": "7767a8b8cbae0a042d1c163db5ae6ec804ba6785", "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Glob matching for javascript/node.js. A faster alternative to minimatch (10-45x faster on avg), with all the features you're used to using in your Grunt and gulp tasks.", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"debug": "^2.1.2", "braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^2.0.1", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "browserify": "^9.0.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}}, "1.6.1": {"name": "micromatch", "version": "1.6.1", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE", "type": "MIT"}, "_id": "micromatch@1.6.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "be9c756f85a6c04f2839625936f37eebd1aa3231", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.6.1.tgz", "integrity": "sha512-6xfdEs0lyvcXVjOa6egEb+LyIRux0OuiimDKnMtzYLZuD6FiYDorTGQgg30XrsMVGm3ZIYMmLkFY+2zgg+kd1w==", "signatures": [{"sig": "MEYCIQDHM1P1sBAZlLHBE/Eg2+i4sVAx0Wq7kYOOW5oYAWEhuwIhAIXz7MMMqE0lVDLbvPeC75HxsZemVcJ1jZ4QPk/Kr0wv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "be9c756f85a6c04f2839625936f37eebd1aa3231", "engines": {"node": ">=0.10.0"}, "gitHead": "7767a8b8cbae0a042d1c163db5ae6ec804ba6785", "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Glob matching for javascript/node.js. A faster alternative to minimatch (10-45x faster on avg), with all the features you're used to using in your Grunt and gulp tasks.", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"debug": "^2.1.2", "braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^2.0.1", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "browserify": "^9.0.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}}, "1.6.2": {"name": "micromatch", "version": "1.6.2", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE", "type": "MIT"}, "_id": "micromatch@1.6.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "38fab47076aacece6ead77ef38472392f7b4bfb9", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.6.2.tgz", "integrity": "sha512-aq9dbJx8tG1narL9gpZKV5QoVobLgwNreir/X7tqZpS+b+7RjAYUqLYk+8DP5iZ8bw7iHmpNiuQ87dbU4WpeuQ==", "signatures": [{"sig": "MEYCIQC5RSbGJjViuaHl1H+2u1TLxDy39l5WOta9FwGrj2gPxgIhANHRGXCw+uQ6m6v2+Zb5DkdQeNsBaBzF3DFCGZ/D6S7X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "38fab47076aacece6ead77ef38472392f7b4bfb9", "engines": {"node": ">=0.10.0"}, "gitHead": "47f96c9962079cafa74d8778a97ef986fda1a50e", "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Glob matching for javascript/node.js. A faster alternative to minimatch (10-45x faster on avg), with all the features you're used to using in your Grunt and gulp tasks.", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"debug": "^2.1.2", "braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^2.1.1", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "browserify": "^9.0.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}}, "2.0.0": {"name": "micromatch", "version": "2.0.0", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE", "type": "MIT"}, "_id": "micromatch@2.0.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "fa102f0e510e0b7861987cdb53e22526448da1a4", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.0.0.tgz", "integrity": "sha512-CU9ZaGKPEkL7hUtzaBYporDZkYCOjviXVJ6gwByyRpnSNgcZdMh2HwRlG6XKPt1tXRM6ErXD272xvMn1Et5OGw==", "signatures": [{"sig": "MEUCIQC2/pNojBNPlU/PjxDlCu4fXZISb1ZQDBng0ihlr13p9QIgIo7n9w1uibqapcVrflNT2gZYQ8L/hx2HWBhp+43/cHE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "fa102f0e510e0b7861987cdb53e22526448da1a4", "engines": {"node": ">=0.10.0"}, "gitHead": "2f3e7f5a6bac4c575fd9ed36fb5f847963c530f1", "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Glob matching for javascript/node.js. A faster alternative to minimatch (10-45x faster on avg), with all the features you're used to using in your Grunt and gulp tasks.", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"debug": "^2.1.2", "braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^2.1.1", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "browserify": "^9.0.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}}, "2.1.0": {"name": "micromatch", "version": "2.1.0", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE", "type": "MIT"}, "_id": "micromatch@2.1.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "48e749678e84b51616045c63c8c1dd1f1773495a", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.1.0.tgz", "integrity": "sha512-zKMrPQVCAx6xEiewqWy5qbv+cMlYC1RgUfTvq9E5oUdtnhdfVEabV/5dYXuCoLeHFUDQvLJ1trKV/NY90UZyrw==", "signatures": [{"sig": "MEYCIQC88k00K/SN6485LhpG4/oHeieVyGbzBXFcnho5AtNH9gIhAN/hXy5OJ0VZOapeuSGELtZkqEuqZ+GeMxRxVaPe5vmj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "48e749678e84b51616045c63c8c1dd1f1773495a", "engines": {"node": ">=0.10.0"}, "gitHead": "af62be3cab30d1ed13adef192c43cf6c057a2c22", "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch (10-45x faster on avg). Just use `minimatch.isMatch()` instead of `multimatch()` or `minimatch()`.", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"debug": "^2.1.2", "braces": "^1.7.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^3.0.0", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "browserify": "^9.0.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}}, "2.1.1": {"name": "micromatch", "version": "2.1.1", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE", "type": "MIT"}, "_id": "micromatch@2.1.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "545300a2564bfbd579fc499c95f99d8a7aed19d9", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.1.1.tgz", "integrity": "sha512-hL6jjzR+N4/ODDcUPdGV8+I/kPw7zfoGQvOgukHc7i3K1LuAp2Cn4v+Ec8WLC0GC1xO1iFSZLqRr0cq63tfbIA==", "signatures": [{"sig": "MEUCIQDJ4mONz6JAxJBV0KpqWO36ZaSTA+cNdOuUL7ZC3oEiXAIgd7eWmybv7TcAfo3kPqfW+cZkjBVEXMyYiQXglgQ8Nw0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["index.js", "lib/"], "_shasum": "545300a2564bfbd579fc499c95f99d8a7aed19d9", "engines": {"node": ">=0.10.0"}, "gitHead": "35b6edbfba9a60324ad557b4414efdd5efa23561", "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "2.7.1", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch (10-45x faster on avg). Just use `minimatch.isMatch()` instead of `multimatch()` or `minimatch()`.", "directories": {}, "_nodeVersion": "1.6.2", "dependencies": {"debug": "^2.1.2", "braces": "^1.7.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^3.0.0", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "browserify": "^9.0.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}}, "2.1.2": {"name": "micromatch", "version": "2.1.2", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE", "type": "MIT"}, "_id": "micromatch@2.1.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "d86316f5b713cce7ac07474fb55971918fb4f48c", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.1.2.tgz", "integrity": "sha512-Q0JVZ8YUWBFZGTZ96v50RSMvrsh0al8tLQFXg5jW0JmwrWj6t2XdFCu7hcaLN6Bp5XJtI6KawmmkPSgr2GhLsg==", "signatures": [{"sig": "MEQCIBwo4IK3ZOFX83tlWr6sHmQCDNrMXl5e4g0ybd2jADOFAiBgfFqzGLFezhasqpZwAYPjvK4nXkndld4JYdSH3A9UGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"deps": {"ignore": ["browser.js"]}}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "d86316f5b713cce7ac07474fb55971918fb4f48c", "engines": {"node": ">=0.10.0"}, "gitHead": "3c9b2c144b06689ac5d49ec5fa8e56e79d3e8ca7", "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. Just use `micromatch.isMatch()` instead of `minimatch()`, or use `micromatch()` instead of `multimatch()`.", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"debug": "^2.1.3", "braces": "^1.8.0", "is-glob": "^1.1.3", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^3.0.0", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "browserify": "^9.0.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.3"}}, "2.1.3": {"name": "micromatch", "version": "2.1.3", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE", "type": "MIT"}, "_id": "micromatch@2.1.3", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "8439bc163e8e12949b67b7e3382675e6c4448892", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.1.3.tgz", "integrity": "sha512-gdLZ5V+NVHKDZ764rFMfqw8x1qZnCWN4y1hgu8eDsW/5L3OzKYKgec7ABVlpGBNmDteleUmgFbYKzxSshThZvg==", "signatures": [{"sig": "MEUCIGol0EMB+j1xbzvNjwPop30RoBmmZgYXtD4uyURhiVRyAiEAsd5yqUT7Mg5E/RLLYDOnxV4bRNSlMj/b1HJ0aqTj+PQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"deps": {"ignore": ["browser.js"]}}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "8439bc163e8e12949b67b7e3382675e6c4448892", "engines": {"node": ">=0.10.0"}, "gitHead": "dbcbcc6953b9d097019a26bf3c1832b788d8b572", "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. Just use `micromatch.isMatch()` instead of `minimatch()`, or use `micromatch()` instead of `multimatch()`.", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"debug": "^2.1.3", "braces": "^1.8.0", "is-glob": "^1.1.3", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^3.0.0", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "browserify": "^9.0.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.3"}}, "2.1.4": {"name": "micromatch", "version": "2.1.4", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE", "type": "MIT"}, "_id": "micromatch@2.1.4", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "4e23f89ae0fa4fd72eac59261fbf7bb41970cdc8", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.1.4.tgz", "integrity": "sha512-Td1DYyBCj7ugwWF+IZRtiwImrkNFUEZ4w1WHKEMknKWQJlzxmL4Zya5wtiMFHgbUUEeb81m0QG/Mj38fUbObTA==", "signatures": [{"sig": "MEUCIQCgjD9+f1W0C8CUjDN8NG4QNPCME6/LVacC9ertnICHfAIgb+h3wIFXFY0Zi3HyoM7QpLWxdrxGXd+rCgOa2D7PuSM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"deps": {"ignore": ["browser.js"]}}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "4e23f89ae0fa4fd72eac59261fbf7bb41970cdc8", "engines": {"node": ">=0.10.0"}, "gitHead": "93d4901e49f89c0441e6fdd54904fdd9b28527e4", "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. Just use `micromatch.isMatch()` instead of `minimatch()`, or use `micromatch()` instead of `multimatch()`.", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"debug": "^2.1.3", "braces": "^1.8.0", "is-glob": "^1.1.3", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^3.0.0", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "browserify": "^9.0.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.3"}}, "2.1.5": {"name": "micromatch", "version": "2.1.5", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE", "type": "MIT"}, "_id": "micromatch@2.1.5", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "e356977873e69f94de02439355978f4a26e8849b", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.1.5.tgz", "integrity": "sha512-FYjzTY02TJXwvxfzEdELtZC+G/wz/KbHUZVnCorE6DNbbNZsxLGZkTXhjmbz5I2ROJtCIdAgiJKVZrB/Q3grMw==", "signatures": [{"sig": "MEUCIA1srSt5hhP5DsRucK9IA/ClQ94f7J/h3139PXY7s+zlAiEA4oXymKrHQgnDC/xs0W58Ypz/Hf1ar64+fWTUeZeVpvI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"deps": {"ignore": ["browser.js"]}}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "e356977873e69f94de02439355978f4a26e8849b", "engines": {"node": ">=0.10.0"}, "gitHead": "b92b72aa90b31684bfb740378048c26972f421de", "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "2.7.3", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. Just use `micromatch.isMatch()` instead of `minimatch()`, or use `micromatch()` instead of `multimatch()`.", "directories": {}, "_nodeVersion": "1.6.2", "dependencies": {"debug": "^2.1.3", "braces": "^1.8.0", "is-glob": "^1.1.3", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^3.0.0", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "browserify": "^9.0.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.3"}}, "2.1.6": {"name": "micromatch", "version": "2.1.6", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": {"url": "https://github.com/jonschlinkert/micromatch/blob/master/LICENSE", "type": "MIT"}, "_id": "micromatch@2.1.6", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "51a65a9dcbfb92113292a071e04da35a81e9050e", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.1.6.tgz", "integrity": "sha512-b6Pr6gJmAZciSjQjFSLXQs9lEs3iY2D6S/uS14gYW9lWYVmCybBPx5XbvTF10BXH8B+jXgWfI5jzsvFxqC8P7w==", "signatures": [{"sig": "MEQCIFZhjU2g50clQKO+IjYJeW64rJOsaXtshw+6V2sWqNSHAiAsc6MPE6l+xxGFRmE+itw5YAZ1upTSp3KNXGccojWMaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"deps": {"ignore": ["browser.js"]}}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "51a65a9dcbfb92113292a071e04da35a81e9050e", "engines": {"node": ">=0.10.0"}, "gitHead": "ebfa820f40cf1c0e5e0da4c5b41ac64cd952595a", "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js"}, "_npmUser": {"name": "doowb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "2.5.1", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. Just use `micromatch.isMatch()` instead of `minimatch()`, or use `micromatch()` instead of `multimatch()`.", "directories": {}, "_nodeVersion": "0.12.0", "dependencies": {"debug": "^2.1.3", "braces": "^1.8.0", "is-glob": "^1.1.3", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^3.0.0", "object.omit": "^0.2.1", "regex-cache": "^0.4.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "browserify": "^9.0.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.3"}}, "2.2.0": {"name": "micromatch", "version": "2.2.0", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@2.2.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "e7281bf971100827b890e375d994f12034898ff5", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.2.0.tgz", "integrity": "sha512-C+bwIqRp177ofuqmibBPxudfy9J4IHY+I/U60ocDbQOoY1H1nj9nL8uN+D9M402/qnQ4LZgv9BLOIWDVxxwaHA==", "signatures": [{"sig": "MEYCIQCv7V94J0tgtFf1K5GK3Ap25f2B6AzJvpp5Fsb73vDjFwIhAMkGAbyAKUi9K7FMCJvIARpGvP71e+3F33J7tWDcpMo9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"deps": {"ignore": ["browser.js"]}, "related": {"list": ["braces", "extglob", "expand-brackets", "fill-range", "expand-range", "gulp-micromatch", "parse-glob", "is-glob"]}, "reflinks": ["braces", "expand-brackets", "extglob", "minimatch", "multimatch", "verb"]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "e7281bf971100827b890e375d994f12034898ff5", "engines": {"node": ">=0.10.0"}, "gitHead": "5017fd78202e04c684cc31d3c2fb1f469ea222ff", "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "2.10.1", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. Just use `micromatch.isMatch()` instead of `minimatch()`, or use `micromatch()` instead of `multimatch()`.", "directories": {}, "_nodeVersion": "0.12.4", "dependencies": {"braces": "^1.8.0", "extglob": "^0.3.0", "is-glob": "^1.1.3", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^3.0.1", "object.omit": "^1.1.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.2.4", "write": "^0.2.0", "should": "^6.0.1", "minimist": "^1.1.1", "minimatch": "^2.0.4", "browserify": "^9.0.8", "multimatch": "^2.0.0", "benchmarked": "^0.1.4"}}, "2.3.0": {"name": "micromatch", "version": "2.3.0", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@2.3.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "951b2c4468c5d77885a6df558ea9a33823e7d238", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.0.tgz", "integrity": "sha512-imzLEnZs3omGptcUytU1GCt7XsA/WZwDg/TIX4ApSCl+JwqFFJedlHRK2yLHj9uPo1vlL8Nw2DR8zFcgUoR8+g==", "signatures": [{"sig": "MEUCICMUjJuTUOkup3fbGZ+faMGFESH6ahw6HLhgRpJf/BvPAiEAkv6gwxuc9GMt/qbCD6gW93m/A8pMc7f41SJlwIF3M94=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"deps": {"ignore": ["browser.js"]}, "related": {"list": ["braces", "extglob", "expand-brackets", "fill-range", "expand-range", "gulp-micromatch", "parse-glob", "is-glob"]}, "reflinks": ["braces", "expand-brackets", "extglob", "minimatch", "multimatch", "verb"]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "951b2c4468c5d77885a6df558ea9a33823e7d238", "engines": {"node": ">=0.10.0"}, "gitHead": "28f5386ef59b78345b2c88488333ab9114e6ecdd", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. Just use `micromatch.isMatch()` instead of `minimatch()`, or use `micromatch()` instead of `multimatch()`.", "directories": {}, "_nodeVersion": "5.0.0", "dependencies": {"braces": "^1.8.1", "extglob": "^0.3.1", "is-glob": "^2.0.1", "kind-of": "^2.0.1", "arr-diff": "^1.1.0", "is-extglob": "^1.0.0", "lazy-cache": "^0.2.3", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "normalize-path": "^2.0.0", "expand-brackets": "^0.1.4"}, "devDependencies": {"gulp": "^3.9.0", "chalk": "^1.1.1", "mocha": "^2.3.3", "write": "^0.2.1", "should": "^7.1.0", "minimist": "^1.2.0", "minimatch": "^3.0.0", "browserify": "^11.2.0", "gulp-mocha": "^2.1.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.4", "gulp-jshint": "^1.11.2", "gulp-istanbul": "^0.10.1", "jshint-stylish": "^2.0.1"}}, "2.3.1": {"name": "micromatch", "version": "2.3.1", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@2.3.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "f9cadd05bf513511288f0f22b89b84e0e93c1646", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.1.tgz", "integrity": "sha512-hjBW51RnqXaFrjCSAe+8Ligdp21bDPTh4i+rlQ4qWXYP2eygnrQeKuCa4l8e2Fymmrm3eaCPH36jUCa3RkSYlA==", "signatures": [{"sig": "MEYCIQC7Gocp+lbslbCvIFSFPaZDTUOjf3coZ9ZtXWZRg3NmQgIhAOWFfWn2MJg5NgxYJTdyyKXRBMmefg1/QYce5eU6UiS9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"deps": {"ignore": ["browser.js"]}, "related": {"list": ["braces", "extglob", "expand-brackets", "fill-range", "expand-range", "gulp-micromatch", "parse-glob", "is-glob"]}, "reflinks": ["braces", "expand-brackets", "extglob", "minimatch", "multimatch", "verb"]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "f9cadd05bf513511288f0f22b89b84e0e93c1646", "engines": {"node": ">=0.10.0"}, "gitHead": "7c430f0042b3e6dec96776a3a4d5f0d3adb4c051", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. Just use `micromatch.isMatch()` instead of `minimatch()`, or use `micromatch()` instead of `multimatch()`.", "directories": {}, "_nodeVersion": "5.0.0", "dependencies": {"braces": "^1.8.1", "extglob": "^0.3.1", "is-glob": "^2.0.1", "kind-of": "^2.0.1", "arr-diff": "^1.1.0", "is-extglob": "^1.0.0", "lazy-cache": "^0.2.3", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "normalize-path": "^2.0.0", "expand-brackets": "^0.1.4"}, "devDependencies": {"gulp": "^3.9.0", "chalk": "^1.1.1", "mocha": "^2.3.3", "write": "^0.2.1", "should": "^7.1.0", "minimist": "^1.2.0", "minimatch": "^3.0.0", "browserify": "^11.2.0", "gulp-mocha": "^2.1.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.4", "gulp-jshint": "^1.11.2", "gulp-istanbul": "^0.10.1", "jshint-stylish": "^2.0.1"}}, "2.3.2": {"name": "micromatch", "version": "2.3.2", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@2.3.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "fec97df21776b01ed72bdff7295f64072b6e2d42", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.2.tgz", "integrity": "sha512-7lDNvYe0dzH7fsyYpLtjCV7Pz9aDrpAUjyFeCeBwLiaSWBnxbAfBLmc3pB/VyoqVEVja4PrAZdmr22P473ZiTw==", "signatures": [{"sig": "MEYCIQDF5dUVRk//EBlMTvCigVYsWabsKWBXJoQSJMlvJknlzgIhAJvQ4SAP7n+Xz7RUfgvgV3KZ3+daV1gqgc4dH9ZNrqfW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"deps": {"ignore": ["browser.js"]}, "related": {"list": ["braces", "extglob", "expand-brackets", "fill-range", "expand-range", "gulp-micromatch", "parse-glob", "is-glob"]}, "reflinks": ["braces", "expand-brackets", "extglob", "minimatch", "multimatch", "verb"]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "fec97df21776b01ed72bdff7295f64072b6e2d42", "engines": {"node": ">=0.10.0"}, "gitHead": "be3de97eb4bcaa5a8ee31ed63cf4bb8e88c20e8c", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. Just use `micromatch.isMatch()` instead of `minimatch()`, or use `micromatch()` instead of `multimatch()`.", "directories": {}, "_nodeVersion": "5.0.0", "dependencies": {"braces": "^1.8.1", "extglob": "^0.3.1", "is-glob": "^2.0.1", "kind-of": "^2.0.1", "arr-diff": "^1.1.0", "is-extglob": "^1.0.0", "lazy-cache": "^0.2.3", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "normalize-path": "^2.0.0", "expand-brackets": "^0.1.4"}, "devDependencies": {"gulp": "^3.9.0", "chalk": "^1.1.1", "mocha": "^2.3.3", "write": "^0.2.1", "should": "^7.1.0", "minimist": "^1.2.0", "minimatch": "^3.0.0", "gulp-mocha": "^2.1.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.4", "gulp-jshint": "^1.11.2", "gulp-istanbul": "^0.10.1", "jshint-stylish": "^2.0.1"}}, "2.3.3": {"name": "micromatch", "version": "2.3.3", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@2.3.3", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "f8357e265f5768f2ca799a3241d1995788d31648", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.3.tgz", "integrity": "sha512-2ijwu2YhIOQ9FCmdZaLdtq8BvE4MTek7RuRu/wdg206/IpJKFmFxJU7TRyJ+vE9J0MauMUAShWUFykLlKl7pRg==", "signatures": [{"sig": "MEQCIGhsZ3OSV7N12sw4GD4XQjqzLaHR03sJp1H0LflBXLSbAiAAuOkhwPYGUN89l2b1LOWWdC92iCVRc5njB0oGBC/s9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"deps": {"ignore": ["browser.js"]}, "related": {"list": ["braces", "extglob", "expand-brackets", "fill-range", "expand-range", "gulp-micromatch", "parse-glob", "is-glob"]}, "reflinks": ["braces", "expand-brackets", "extglob", "minimatch", "multimatch", "verb"]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "f8357e265f5768f2ca799a3241d1995788d31648", "engines": {"node": ">=0.10.0"}, "gitHead": "206f26ecf50ddea0e52e3247c289753be42f562a", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. Just use `micromatch.isMatch()` instead of `minimatch()`, or use `micromatch()` instead of `multimatch()`.", "directories": {}, "_nodeVersion": "5.0.0", "dependencies": {"braces": "^1.8.1", "extglob": "^0.3.1", "is-glob": "^2.0.1", "kind-of": "^2.0.1", "arr-diff": "^1.1.0", "is-extglob": "^1.0.0", "lazy-cache": "^0.2.3", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "normalize-path": "^2.0.0", "expand-brackets": "^0.1.4"}, "devDependencies": {"gulp": "^3.9.0", "chalk": "^1.1.1", "mocha": "^2.3.3", "write": "^0.2.1", "should": "^7.1.0", "minimist": "^1.2.0", "minimatch": "^3.0.0", "gulp-mocha": "^2.1.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.4", "gulp-jshint": "^1.11.2", "gulp-istanbul": "^0.10.1", "jshint-stylish": "^2.0.1"}}, "2.3.4": {"name": "micromatch", "version": "2.3.4", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@2.3.4", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "b17874eb365b371065fcd44c27d6e1585638ae4c", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.4.tgz", "integrity": "sha512-TaQx3Nw4E5HTt//Q1YlwVBFfckcwTfDD9BLGpnx5jBIqkPe0DWa4P+KpVHuMdsJ53SfoK8gxjyiJDwASR1ycxw==", "signatures": [{"sig": "MEUCIAeJk6nJbRGlaZPEEmDBjxbefQf9s0k6pbhG96Q5HzLKAiEAl2lEYqxc63z/xyUeVGNbl4e/dOx/vRQjv7MHhit7gyw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"deps": {"ignore": ["browser.js"]}, "related": {"list": ["braces", "extglob", "expand-brackets", "fill-range", "expand-range", "gulp-micromatch", "parse-glob", "is-glob"]}, "reflinks": ["braces", "expand-brackets", "extglob", "minimatch", "multimatch", "verb"]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "b17874eb365b371065fcd44c27d6e1585638ae4c", "engines": {"node": ">=0.10.0"}, "gitHead": "35795dffcb0e8e2529979df9b3004e167f399528", "scripts": {"test": "mocha"}, "_npmUser": {"name": "es128", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. Just use `micromatch.isMatch()` instead of `minimatch()`, or use `micromatch()` instead of `multimatch()`.", "directories": {}, "_nodeVersion": "5.0.0", "dependencies": {"braces": "^1.8.1", "extglob": "^0.3.1", "is-glob": "^2.0.1", "kind-of": "^2.0.1", "arr-diff": "^2.0.0", "is-extglob": "^1.0.0", "lazy-cache": "^0.2.3", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "normalize-path": "^2.0.0", "expand-brackets": "^0.1.4"}, "devDependencies": {"gulp": "^3.9.0", "chalk": "^1.1.1", "mocha": "^2.3.3", "write": "^0.2.1", "should": "^7.1.0", "minimist": "^1.2.0", "minimatch": "^3.0.0", "gulp-mocha": "^2.1.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.4", "gulp-jshint": "^1.11.2", "gulp-istanbul": "^0.10.1", "jshint-stylish": "^2.0.1"}}, "2.3.5": {"name": "micromatch", "version": "2.3.5", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@2.3.5", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "d8dfed89e28419d073489be55c33f0b05c273217", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.5.tgz", "integrity": "sha512-Xm9hdaMOJrGA+hxR6IpiBxrMDkrzYXgaNJMz8Vst/wb1kv0SigQwFS4wWBvQ0+Hb0afwitHYNe23qTcO3PCz1w==", "signatures": [{"sig": "MEQCIH5+6lb8fGh3J9XNNx5gKuYIkEQDAbTAPTbLf+O3zDfXAiBjs27//7rqZ6KCX1oXfbXWmTyoQz3g/iHxiDadPlnn0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"deps": {"ignore": ["browser.js"]}, "related": {"list": ["braces", "extglob", "expand-brackets", "fill-range", "expand-range", "gulp-micromatch", "parse-glob", "is-glob"]}, "reflinks": ["braces", "expand-brackets", "extglob", "minimatch", "multimatch", "verb"]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "d8dfed89e28419d073489be55c33f0b05c273217", "engines": {"node": ">=0.10.0"}, "gitHead": "ce960d94bfcd5a595a8aa7a6177bcd5b0c46112d", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. Just use `micromatch.isMatch()` instead of `minimatch()`, or use `micromatch()` instead of `multimatch()`.", "directories": {}, "_nodeVersion": "5.0.0", "dependencies": {"braces": "^1.8.1", "extglob": "^0.3.1", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "arr-diff": "^2.0.0", "is-extglob": "^1.0.0", "lazy-cache": "^0.2.3", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "normalize-path": "^2.0.0", "expand-brackets": "^0.1.4"}, "devDependencies": {"gulp": "^3.9.0", "chalk": "^1.1.1", "mocha": "^2.3.3", "write": "^0.2.1", "should": "^7.1.0", "minimist": "^1.2.0", "minimatch": "^3.0.0", "gulp-mocha": "^2.1.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.4", "gulp-jshint": "^1.11.2", "gulp-istanbul": "^0.10.1", "jshint-stylish": "^2.0.1"}}, "2.3.6": {"name": "micromatch", "version": "2.3.6", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@2.3.6", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "67ced00422d36c9f8bb01d391fe69637b45aa866", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.6.tgz", "integrity": "sha512-hee<PERSON><PERSON><PERSON>cib3225WcK6d822rW5wlbDkdhjkZbPWkGNeJQ/EvCSlkFBEdnDY5lGn3AW8TGcS0lkmOXOR/KVXcWcLw==", "signatures": [{"sig": "MEQCIEvmvJp57+dEahm9STS2OaZvsH6cb29UsY7XRDJV0VuTAiA4vmVlYx0xXkrfnj6ZrVMO+Iket4yAfPHxnvtIJ7RxkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"related": {"list": ["braces", "extglob", "expand-brackets", "fill-range", "expand-range", "gulp-micromatch", "parse-glob", "is-glob"]}, "reflinks": ["braces", "expand-brackets", "extglob", "minimatch", "multimatch", "verb"]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "67ced00422d36c9f8bb01d391fe69637b45aa866", "engines": {"node": ">=0.10.0"}, "gitHead": "7332babcd21d977fef867f24ee9601fb5e75e301", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. Just use `micromatch.isMatch()` instead of `minimatch()`, or use `micromatch()` instead of `multimatch()`.", "directories": {}, "_nodeVersion": "5.0.0", "dependencies": {"braces": "^1.8.2", "extglob": "^0.3.1", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "arr-diff": "^2.0.0", "is-extglob": "^1.0.0", "lazy-cache": "^1.0.0", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "normalize-path": "^2.0.1", "expand-brackets": "^0.1.4"}, "devDependencies": {"gulp": "^3.9.0", "chalk": "^1.1.1", "mocha": "*", "write": "^0.2.1", "should": "*", "minimist": "^1.2.0", "minimatch": "^3.0.0", "gulp-mocha": "^2.1.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.4", "gulp-eslint": "^1.1.1", "gulp-istanbul": "^0.10.1"}}, "2.3.7": {"name": "micromatch", "version": "2.3.7", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@2.3.7", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "2f2e85ef46140dbea6cb55e739b6b11b30eaa509", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.7.tgz", "integrity": "sha512-G0mcWeWiyuIQIrb1q+PW2IavR10QlVEYCKpw7R8FjkzhAOSwtj633GCAhQrx1uLQtfc1h3CQWazD8zv+Rf9C4A==", "signatures": [{"sig": "MEUCIHbyzyeqkD2wli+AOLz1qWIYYftcBrP1LURmu6OERuD+AiEAz9Bv3bh6PqYEV/lVR/BFYwdJCBMXxwAmyDIJUehJL5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"related": {"list": ["braces", "extglob", "expand-brackets", "fill-range", "expand-range", "gulp-micromatch", "parse-glob", "is-glob"]}, "reflinks": ["braces", "expand-brackets", "extglob", "minimatch", "multimatch", "verb"]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "2f2e85ef46140dbea6cb55e739b6b11b30eaa509", "engines": {"node": ">=0.10.0"}, "gitHead": "cf7209ba3a3c962613509d5ec61a890aaf2a42a0", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "3.3.6", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch. Just use `micromatch.isMatch()` instead of `minimatch()`, or use `micromatch()` instead of `multimatch()`.", "directories": {}, "_nodeVersion": "5.0.0", "dependencies": {"braces": "^1.8.2", "extglob": "^0.3.1", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "arr-diff": "^2.0.0", "is-extglob": "^1.0.0", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "normalize-path": "^2.0.1", "expand-brackets": "^0.1.4"}, "devDependencies": {"gulp": "^3.9.0", "chalk": "^1.1.1", "mocha": "*", "write": "^0.2.1", "should": "*", "minimist": "^1.2.0", "minimatch": "^3.0.0", "gulp-mocha": "^2.1.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.4", "gulp-eslint": "^1.1.1", "gulp-istanbul": "^0.10.1"}}, "2.3.8": {"name": "micromatch", "version": "2.3.8", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@2.3.8", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "94fbf8f37ed9edeca06bf1c8f7b743fb5f6f5854", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.8.tgz", "integrity": "sha512-PQOHX68M7tTh3MiVCxD0ZqaFLlFCr2akgLvAaMCW1aqSi+eaCJbPsJvRNZ95aC3TsQUUziKVKPjMDXJbIInohA==", "signatures": [{"sig": "MEYCIQCMOZU04hqycyxzBhPHHffkccml/i6SQPGbTZ8BCazOHwIhALWBqYZDEoPC1Gx4rvivdK2y6AAGUdZiYRJoyLuI0GQK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": false, "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "expand-range", "extglob", "fill-range", "gulp-micromatch", "is-glob", "parse-glob"]}, "reflinks": ["braces", "expand-brackets", "extglob", "minimatch", "multimatch", "verb"]}, "_from": ".", "files": ["index.js", "lib/"], "_shasum": "94fbf8f37ed9edeca06bf1c8f7b743fb5f6f5854", "engines": {"node": ">=0.10.0"}, "gitHead": "dc5e49fc2d665bfc5e9c06c6c8e5db74e14311b7", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"braces": "^1.8.2", "extglob": "^0.3.1", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "arr-diff": "^2.0.0", "is-extglob": "^1.0.0", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "normalize-path": "^2.0.1", "expand-brackets": "^0.1.4"}, "devDependencies": {"gulp": "^3.9.0", "chalk": "^1.1.1", "mocha": "^2", "write": "^0.2.1", "should": "^8", "minimist": "^1.2.0", "minimatch": "^3.0.0", "gulp-mocha": "^2.1.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.4", "gulp-eslint": "^1.1.1", "gulp-istanbul": "^0.10.1", "gulp-format-md": "^0.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch-2.3.8.tgz_1461361550189_0.8745819758623838", "host": "packages-12-west.internal.npmjs.com"}}, "2.3.9": {"name": "micromatch", "version": "2.3.9", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@2.3.9", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "26370d1989d8029f91034ab667f5f020ccd4f8fd", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.9.tgz", "integrity": "sha512-QZU30YOYs0E+4Y/CC1QgqBqmeY3RmH2PYwPpyS5e43+I1xvSnJF/p6iBoBzrnLDVm1vjKLOHx8fnXU3T4u3OlQ==", "signatures": [{"sig": "MEYCIQCVM2mA0VjeUYZyHE7ADrWn5SE+mzNykowyOZ6HzG5JdwIhAP/f/r7qeqzjPJVYB/6z4Lb3u1zUicQgb0TtV1PApF7V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": false, "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "expand-range", "extglob", "fill-range", "gulp-micromatch", "is-glob", "parse-glob"]}, "reflinks": ["braces", "expand-brackets", "extglob", "minimatch", "multimatch", "verb"]}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "26370d1989d8029f91034ab667f5f020ccd4f8fd", "engines": {"node": ">=0.10.0"}, "gitHead": "2791eceecd8580bd20b46eb4643d22992542de52", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"braces": "^1.8.4", "extglob": "^0.3.2", "is-glob": "^2.0.1", "kind-of": "^3.0.3", "arr-diff": "^3.0.0", "is-extglob": "^1.0.0", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.3", "array-unique": "^0.2.1", "normalize-path": "^2.0.1", "expand-brackets": "^0.1.5"}, "devDependencies": {"gulp": "^3.9.1", "chalk": "^1.1.3", "mocha": "^2.4.5", "write": "^0.3.1", "should": "^8.3.1", "minimist": "^1.2.0", "minimatch": "^3.0.0", "gulp-mocha": "^2.2.0", "multimatch": "^2.1.0", "benchmarked": "^0.2.5", "gulp-eslint": "^2.0.0", "gulp-istanbul": "^0.10.4", "gulp-format-md": "^0.1.9"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch-2.3.9.tgz_1466541389294_0.023896703030914068", "host": "packages-12-west.internal.npmjs.com"}}, "2.3.10": {"name": "micromatch", "version": "2.3.10", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@2.3.10", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "f4fb3175beec62795a7b8c24d5f745c3680660ab", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.10.tgz", "integrity": "sha512-s4QloPs9pCkHMwjH3NMvZAXpCXEizLcR7QN1+fkoPmmAFx34ukor4ZdN9onkfQrOOye+Z44n2DSJ1vw4Vr7Atw==", "signatures": [{"sig": "MEQCIHzuW7DH73loLHmYyiWfjla2lr13xva85VKriaIaulplAiA5XxNayntnbwIaI8XKDekVu4Ynmu+s/FFH+vo61kom9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": false, "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "expand-range", "extglob", "fill-range", "gulp-micromatch", "is-glob", "parse-glob"]}, "reflinks": ["braces", "expand-brackets", "extglob", "minimatch", "multimatch", "verb"]}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "f4fb3175beec62795a7b8c24d5f745c3680660ab", "engines": {"node": ">=0.10.0"}, "gitHead": "7b957cc79781fa0f070387b455e3dfccfe8eea1b", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"braces": "^1.8.2", "extglob": "^0.3.1", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "arr-diff": "^2.0.0", "is-extglob": "^1.0.0", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "normalize-path": "^2.0.1", "expand-brackets": "^0.1.4"}, "devDependencies": {"gulp": "^3.9.0", "chalk": "^1.1.1", "mocha": "^2", "write": "^0.2.1", "should": "^8", "minimist": "^1.2.0", "minimatch": "^3.0.0", "gulp-mocha": "^2.1.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.4", "gulp-eslint": "^1.1.1", "gulp-istanbul": "^0.10.1", "gulp-format-md": "^0.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch-2.3.10.tgz_1466543592333_0.20977307227440178", "host": "packages-12-west.internal.npmjs.com"}}, "2.3.11": {"name": "micromatch", "version": "2.3.11", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@2.3.11", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "homepage": "https://github.com/jonschlinkert/micromatch", "bugs": {"url": "https://github.com/jonschlinkert/micromatch/issues"}, "dist": {"shasum": "86677c97d1720b363431d04d0d15293bd38c1565", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.11.tgz", "integrity": "sha512-LnU2XFEk9xxSJ6rfgAry/ty5qwUTyHYOBU0g4R6tIw5ljwgGIBmiKhRWLw5NpMOnrgUNcDJ4WMp8rl3sYVHLNA==", "signatures": [{"sig": "MEUCIQCJr3I542G0h7b8tAUZ8AequLEIJui8oUKLmGD/7QE00gIgRSuGepu/6pvjkZKmCBDzg/A6Wl01aZUJ235qcBQBSSU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": false, "lint": {"reflinks": true}, "tasks": ["readme"], "layout": false, "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "expand-range", "extglob", "fill-range", "gulp-micromatch", "is-glob", "parse-glob"]}, "reflinks": ["braces", "expand-brackets", "extglob", "minimatch", "multimatch", "verb"]}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "86677c97d1720b363431d04d0d15293bd38c1565", "engines": {"node": ">=0.10.0"}, "gitHead": "f194c187d04677b03047bb7d8d25643725f7a577", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jonschlinkert/micromatch.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"braces": "^1.8.2", "extglob": "^0.3.1", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "arr-diff": "^2.0.0", "is-extglob": "^1.0.0", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "normalize-path": "^2.0.1", "expand-brackets": "^0.1.4"}, "devDependencies": {"gulp": "^3.9.0", "chalk": "^1.1.1", "mocha": "^2", "write": "^0.2.1", "should": "^8", "minimist": "^1.2.0", "minimatch": "^3.0.0", "gulp-mocha": "^2.1.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.4", "gulp-eslint": "^1.1.1", "gulp-istanbul": "^0.10.1", "gulp-format-md": "^0.1.8"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch-2.3.11.tgz_1468602931475_0.3629888044670224", "host": "packages-16-east.internal.npmjs.com"}}, "3.0.0": {"name": "micromatch", "version": "3.0.0", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@3.0.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "contributors": [{"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON> Reagent"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "http://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>ey"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "c926f11cd75e887dc3b82968909575280731049d", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.0.0.tgz", "integrity": "sha512-jXbEGkzAwEzFuofLH4JGjM+o1puUK0KlJcLYc5t7zXuutGC0+mRKVqKU264ZQzY6nf/lwTqX2JxOMc7D5lRIjw==", "signatures": [{"sig": "MEQCIHGKknQAA/Ac0MAcIQQ/W16q9uNBe1nIuTqBKwhv6YFnAiBatkIwTVeHmqw0jZY3W42X1KwiwM814WMk0L29OF3rEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "helpers": ["./benchmark/helper.js"], "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["extglob", "glob-object", "minimatch", "multimatch", "snapdragon", "expand-brackets"]}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "c926f11cd75e887dc3b82968909575280731049d", "engines": {"node": ">=0.10.0"}, "gitHead": "e91c2f68cdf5f378f670248579e4278a3af63520", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "lintDeps": {"dependencies": {"lock": {"snapdragon": "^0.8.1"}}, "devDependencies": {"files": ["examples/*.js", "gulpfile.js", "test/**/*.js"], "options": {"ignore": ["benchmark/**"]}}}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "7.7.3", "dependencies": {"braces": "^2.0.3", "extglob": "^1.1.0", "kind-of": "^3.1.0", "arr-diff": "^3.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.1.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.2.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^0.2.5"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.4.2", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.3", "bash-match": "^0.2.0", "gulp-mocha": "^3.0.0", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.1", "extend-shallow": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch-3.0.0.tgz_1496038266119_0.6933238150086254", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "micromatch", "version": "3.0.1", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@3.0.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "contributors": [{"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON> Reagent"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "http://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>ey"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "2e1bd0ceda6cf78d04b14d281c922129e48ef323", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.0.1.tgz", "integrity": "sha512-NzCifOxXgufa3ZUdTdOqvPlFsSoqn9CwSEbID5lM5HhvBKg+NozKqkSA39yuP7EE2qlRtNf4qji0Iw4WpVvvZQ==", "signatures": [{"sig": "MEUCIQDiomHRe82ezZkb6d//qgePWMeTufl6k9BE2rO0Rgs+CwIgJS+Sa9kAqjGBkLr6E/C95G6/pkj+sVPAE8Z7vXfIpJg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "helpers": ["./benchmark/helper.js"], "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["extglob", "glob-object", "minimatch", "multimatch", "snapdragon", "expand-brackets"]}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "2e1bd0ceda6cf78d04b14d281c922129e48ef323", "engines": {"node": ">=0.10.0"}, "gitHead": "84a00c17e877c64648114785ebefab12100570ed", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "lintDeps": {"dependencies": {"lock": {"snapdragon": "^0.8.1"}}, "devDependencies": {"files": ["examples/*.js", "gulpfile.js", "test/**/*.js"], "options": {"ignore": ["benchmark/**"]}}}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "7.7.3", "dependencies": {"braces": "^2.0.3", "extglob": "^1.1.0", "kind-of": "^3.1.0", "arr-diff": "^3.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.1.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.2.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^0.2.5"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.4.2", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.3", "bash-match": "^0.2.0", "gulp-mocha": "^3.0.0", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.1", "extend-shallow": "^2.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch-3.0.1.tgz_1496038758515_0.04906583297997713", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "micromatch", "version": "3.0.2", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@3.0.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "contributors": [{"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON> Reagent"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "http://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>ey"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "bf77b8c860f342d73ac96bf4c495ed3cec05875c", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.0.2.tgz", "integrity": "sha512-DJPmTvzNtZ7PExARW6ovsLRP6rxkv6IkdZLVhD8hpfduvMKNRjl2k7+CwqEvURC3R45BVex8TTPeA/YDVEoggg==", "signatures": [{"sig": "MEYCIQC3ss69oCHh9ZlI741njwAVvbzYvftEiyWSjSvNgJek8QIhAPzOK6ZQMD7hQeYq3xUJalNrUHTDEbN1HkNqvw1Wf/E5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "helpers": ["./benchmark/helper.js"], "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["extglob", "glob-object", "minimatch", "multimatch", "snapdragon", "expand-brackets"]}, "files": ["index.js", "lib"], "engines": {"node": ">=0.10.0"}, "gitHead": "6fb55559cb2c9d018522ce481e8a63a4b217d93d", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "lintDeps": {"dependencies": {"lock": {"snapdragon": "^0.8.1"}}, "devDependencies": {"files": ["examples/*.js", "gulpfile.js", "test/**/*.js"], "options": {"ignore": ["benchmark/**"]}}}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "7.7.3", "dependencies": {"braces": "^2.0.3", "extglob": "^1.1.0", "kind-of": "^3.1.0", "arr-diff": "^3.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.1.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.2.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^0.2.5"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.4.2", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.3", "bash-match": "^0.2.0", "gulp-mocha": "^3.0.0", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.1", "gulp-format-md": "^0.1.12"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch-3.0.2.tgz_1496232124114_0.22913403972052038", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "micromatch", "version": "3.0.3", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@3.0.3", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "contributors": [{"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON> Reagent"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "http://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>ey"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "af3339640157ddad39b81a09956d8877cc4b421a", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.0.3.tgz", "integrity": "sha512-uTjtugrI0sC0KbJNLZtCmyrg5oFESH++4QqLUdHLrKugDbCz+9V5983YfjpfJhmxqf5f2H+JV9JJhS0brPS9TA==", "signatures": [{"sig": "MEYCIQC9Xh01MQxoBRVfJCcfJP7KuqiTpxcwPLh9bOOH8OAMBgIhANFDbZTYyor+jTURWB1+MdL0b+vX2pLG/cn0jt6jTc05", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "helpers": ["./benchmark/helper.js"], "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["expand-brackets", "extglob", "glob-object", "minimatch", "multimatch", "snapdragon"]}, "files": ["index.js", "lib"], "engines": {"node": ">=0.10.0"}, "gitHead": "093adbda5b133a78b7fe31cf054a01900e115116", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "lintDeps": {"dependencies": {"files": ["index.js", "lib/**"], "options": {"lock": {"snapdragon": "^0.8.1"}}}, "devDependencies": {"files": ["examples/*.js", "gulpfile.js", "test/**/*.js"], "options": {"ignore": ["benchmark/**"]}}}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "5.0.1", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "7.7.3", "dependencies": {"braces": "^2.2.2", "extglob": "^1.1.0", "kind-of": "^4.0.0", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.2.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^1.0.0"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.4.2", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^4.3.1", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.1", "gulp-format-md": "^0.1.12"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch-3.0.3.tgz_1496394119627_0.33203266467899084", "host": "s3://npm-registry-packages"}}, "3.0.4": {"name": "micromatch", "version": "3.0.4", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@3.0.4", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "contributors": [{"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON> Reagent"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "http://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>ey"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "1543f1d04813447ac852001c5f5a933401786d1d", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.0.4.tgz", "integrity": "sha512-W07uHmC2/7xleHWcYgg4iASgcUSY9TCBnap3gAn+331X7hMQKyRDGzcHzMsadQKa5KNcM/55hFocVggIyTuVFQ==", "signatures": [{"sig": "MEYCIQC9vLItAZxDXKfFO9QzqShFjuMJXiwxDAu7LKeo2tKPogIhANUNtz8kB3joUMSu05zDVBSVCLEaXs83yfME8zdSa2DK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "helpers": ["./benchmark/helper.js"], "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["expand-brackets", "extglob", "glob-object", "minimatch", "multimatch", "snapdragon"]}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "1543f1d04813447ac852001c5f5a933401786d1d", "engines": {"node": ">=0.10.0"}, "gitHead": "12e9d372eaad918c2bb3313bf3dbe7bd5ff3f138", "scripts": {"test": "mocha"}, "_npmUser": {"name": "doowb", "email": "<EMAIL>"}, "lintDeps": {"dependencies": {"files": ["index.js", "lib/**"], "options": {"lock": {"snapdragon": "^0.8.1"}}}, "devDependencies": {"files": ["examples/*.js", "gulpfile.js", "test/**/*.js"], "options": {"ignore": ["benchmark/**"]}}}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "6.10.1", "dependencies": {"braces": "^2.2.2", "extglob": "^1.1.0", "kind-of": "^4.0.0", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.2.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^1.0.0"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.4.2", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^4.3.1", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.1", "gulp-format-md": "^0.1.12"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch-3.0.4.tgz_1499799889882_0.****************", "host": "s3://npm-registry-packages"}}, "3.0.5": {"name": "micromatch", "version": "3.0.5", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@3.0.5", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "contributors": [{"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "tunnckoCore"}, {"url": "http://rumkin.com", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>ey"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "99717da26f83c050cd712ab5ea59f71821f758f9", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.0.5.tgz", "integrity": "sha512-NszdHwRzt/Kj4ee6W0TArIZ0kPN48pUN8jXTdzz6iDpJ16fxiYjB1KjMI138S450+9iKqXcmpY4MTT5pB317Rg==", "signatures": [{"sig": "MEUCIFkLs3TYPZxtN/r6RtkSon0o8SJTaaD7qD18q89itdaqAiEAnKW8MwHuQhn5lYjkCNg29vQR89SpMlQLavYZGe1T1dI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "helpers": ["./benchmark/helper.js"], "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["expand-brackets", "extglob", "glob-object", "minimatch", "multimatch", "snapdragon"]}, "files": ["index.js", "lib"], "engines": {"node": ">=0.10.0"}, "gitHead": "edbfb23d00bd9811038853c4f63dd37c80881c19", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "lintDeps": {"dependencies": {"options": {"lock": {"snapdragon": "^0.8.1"}}}, "devDependencies": {"files": {"options": {"ignore": ["benchmark/**"]}}}}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "5.4.1", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"braces": "^2.2.2", "extglob": "^2.0.0", "kind-of": "^5.0.2", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^1.0.0"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.5.0", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^3.0.1", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.2", "gulp-format-md": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch-3.0.5.tgz_1504837190264_0.12273128144443035", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "micromatch", "version": "3.1.0", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@3.1.0", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "contributors": [{"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "tunnckoCore"}, {"url": "http://rumkin.com", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>ey"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "5102d4eaf20b6997d6008e3acfe1c44a3fa815e2", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.0.tgz", "integrity": "sha512-3StSelAE+hnRvMs8IdVW7Uhk8CVed5tp+kLLGlBP6WiRAXS21GPGu/Nat4WNPXj2Eoc24B02SaeoyozPMfj0/g==", "signatures": [{"sig": "MEYCIQCvS7yKe9ddx1MWNu/OwhKmdQo15nTBDvz7HBVHj5vAbQIhAIK7ILXZppSNxzQDhXNuMCrGInnN2Wlo788kcaUSbB6A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "helpers": ["./benchmark/helper.js"], "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["expand-brackets", "extglob", "glob-object", "minimatch", "multimatch", "snapdragon"]}, "files": ["index.js", "lib"], "engines": {"node": ">=0.10.0"}, "gitHead": "896a3104d70d5729e043c441e55b0a7dbc3df0d3", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "lintDeps": {"dependencies": {"options": {"lock": {"snapdragon": "^0.8.1"}}}, "devDependencies": {"files": {"options": {"ignore": ["benchmark/**"]}}}}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "5.4.1", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"braces": "^2.2.2", "extglob": "^2.0.2", "kind-of": "^5.0.2", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^1.0.0"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.5.0", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^3.0.1", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.2", "gulp-format-md": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch-3.1.0.tgz_1505108343037_0.28811070742085576", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "micromatch", "version": "3.1.1", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@3.1.1", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "contributors": [{"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "http://badassjs.com", "name": "Devon Govett"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "http://rumkin.com", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>ey"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "1c491537524916fb5b60c8f6cccdf25fceaa0def", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.1.tgz", "integrity": "sha512-wSN8CKMA+JP1FEwVDKZBQDz9VaMfA8wNCRG/HxUHu7Mb5TjXq9UvA+Ss1iuUkXUhcCDWtwORNf2mZHJTW9fPBw==", "signatures": [{"sig": "MEQCIG+6yDZtskZXJpjWaXKHqgkXQLqNZzOOTbO6Xt7tngzEAiBfLwJJNMksjy8mqYxKwybyXYoAccZPMCwwoOqsQOI9ug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "helpers": ["./benchmark/helper.js"], "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["expand-brackets", "extglob", "glob-object", "minimatch", "multimatch", "snapdragon"]}, "files": ["index.js", "lib"], "engines": {"node": ">=0.10.0"}, "gitHead": "379db7338e1f141fabd0cb1052bf93bcc776ff0e", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "lintDeps": {"dependencies": {"options": {"lock": {"snapdragon": "^0.8.1"}}}, "devDependencies": {"files": {"options": {"ignore": ["benchmark/**"]}}}}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "8.7.0", "dependencies": {"braces": "^2.3.0", "extglob": "^2.0.2", "kind-of": "^6.0.0", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.4", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^1.0.0"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^4.0.1", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^3.0.1", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.2", "gulp-format-md": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch-3.1.1.tgz_1508546339967_0.6888535884208977", "host": "s3://npm-registry-packages"}}, "3.1.2": {"name": "micromatch", "version": "3.1.2", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@3.1.2", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "contributors": [{"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "http://badassjs.com", "name": "Devon Govett"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "http://rumkin.com", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>ey"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "2737f3b16f4a7f12af3591bc30da4aa4dfbaf23e", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.2.tgz", "integrity": "sha512-dIgY4Cr0Xq1NzICdDCPB9KQvjqNm23VfWXTZOSysk/1SzdjwkjnGozvLHS589VG07iGHOyHL6uYzvvhZ+Pc1pQ==", "signatures": [{"sig": "MEUCIQCF5hdyj8z+aPPpj9iXbqF1CFwkGknREvj/V7tiKBsI9AIgVk+aowGxoz6uPO+eRocwCXKuHGGOzrKW3hOSNnw6FLI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "helpers": ["./benchmark/helper.js"], "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["expand-brackets", "extglob", "glob-object", "minimatch", "multimatch", "snapdragon"]}, "files": ["index.js", "lib"], "engines": {"node": ">=0.10.0"}, "gitHead": "1bd8cd8611b20a4537931e83737704696f2be298", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "lintDeps": {"dependencies": {"options": {"lock": {"snapdragon": "^0.8.1"}}}, "devDependencies": {"files": {"options": {"ignore": ["benchmark/**"]}}}}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "8.7.0", "dependencies": {"braces": "^2.3.0", "extglob": "^2.0.2", "kind-of": "^6.0.0", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.4", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^1.0.0"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^3.0.1", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.2", "gulp-format-md": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch-3.1.2.tgz_1508550095307_0.*****************", "host": "s3://npm-registry-packages"}}, "3.1.3": {"name": "micromatch", "version": "3.1.3", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@3.1.3", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "contributors": [{"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "http://badassjs.com", "name": "Devon Govett"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "http://rumkin.com", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>ey"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "ae1ee52aff9c990a83ff8fb69891aeba2847c85f", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.3.tgz", "integrity": "sha512-gVCSW2StFfuHZYfh/p/HJpdTyB/YX/mr/EATvmw9zMQa6BSUioG4hg4duKEKc47OaXioikzhgFYS/m4EyLmXXg==", "signatures": [{"sig": "MEUCIFzZ8vW5VCCC1gGijBsPWSQLoouzEr8M8vi/GJcd70wlAiEA1JKkPguF7G9+OE/3j7dIxSLc1QbO6Jdn/kW9CywAZe0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "helpers": ["./benchmark/helper.js"], "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["expand-brackets", "extglob", "glob-object", "minimatch", "multimatch", "snapdragon"]}, "files": ["index.js", "lib"], "engines": {"node": ">=0.10.0"}, "gitHead": "83cca3ebc1e1dd638e60d863110df1a7805dbd38", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "lintDeps": {"dependencies": {"options": {"lock": {"snapdragon": "^0.8.1"}}}, "devDependencies": {"files": {"options": {"ignore": ["benchmark/**"]}}}}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "8.7.0", "dependencies": {"braces": "^2.3.0", "extglob": "^2.0.2", "kind-of": "^6.0.0", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.5", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^1.0.0"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^3.0.1", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.2", "gulp-format-md": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch-3.1.3.tgz_1508550798635_0.6492397910915315", "host": "s3://npm-registry-packages"}}, "3.1.4": {"name": "micromatch", "version": "3.1.4", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@3.1.4", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "contributors": [{"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "http://badassjs.com", "name": "Devon Govett"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "http://rumkin.com", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>ey"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "bb812e741a41f982c854e42b421a7eac458796f4", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.4.tgz", "integrity": "sha512-kFRtviKYoAJT+t7HggMl0tBFGNAKLw/S7N+CO9qfEQyisob1Oy4pao+geRbkyeEd+V9aOkvZ4mhuyPvI/q9Sfg==", "signatures": [{"sig": "MEUCIEcCygjisWAx3TdWbHXGmwnpPaCyXKxWblwh8igIyk4kAiEAxRFn6SoT9LdT1B36MNNgKLi6tkrtzinmy/8oaCemMf8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "helpers": ["./benchmark/helper.js"], "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["expand-brackets", "extglob", "glob-object", "minimatch", "multimatch", "snapdragon"]}, "files": ["index.js", "lib"], "engines": {"node": ">=0.10.0"}, "gitHead": "ea5cd63a59e41048dda2e08a036c11b1a2abee85", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "lintDeps": {"dependencies": {"options": {"lock": {"snapdragon": "^0.8.1"}}}, "devDependencies": {"files": {"options": {"ignore": ["benchmark/**"]}}}}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "8.7.0", "dependencies": {"braces": "^2.3.0", "extglob": "^2.0.2", "kind-of": "^6.0.0", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.5", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^1.0.0"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^3.0.1", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.2", "gulp-format-md": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch-3.1.4.tgz_1509351592485_0.7701602119486779", "host": "s3://npm-registry-packages"}}, "3.1.5": {"name": "micromatch", "version": "3.1.5", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@3.1.5", "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}], "contributors": [{"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "http://badassjs.com", "name": "Devon Govett"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "http://rumkin.com", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>ey"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "d05e168c206472dfbca985bfef4f57797b4cd4ba", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.5.tgz", "integrity": "sha512-ykttrLPQrz1PUJcXjwsTUjGoPJ64StIGNE2lGVD1c9CuguJ+L7/navsE8IcDNndOoCMvYV0qc/exfVbMHkUhvA==", "signatures": [{"sig": "MEUCIEbXGYuPU5qx8SFCnFy+J3SPtMWpgRwBHBuSwt8N/dCmAiEA0QhmVGSJrK3hGMsHkHB7aWUo5QCrjvGOj5jdXk8eau8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "helpers": ["./benchmark/helper.js"], "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["expand-brackets", "extglob", "glob-object", "minimatch", "multimatch", "snapdragon"]}, "files": ["index.js", "lib"], "engines": {"node": ">=0.10.0"}, "gitHead": "f29036594b0e9e0891dbe147cd11d80711925668", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "lintDeps": {"dependencies": {"options": {"lock": {"snapdragon": "^0.8.1"}}}, "devDependencies": {"files": {"options": {"ignore": ["benchmark/**"]}}}}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "9.1.0", "dependencies": {"braces": "^2.3.0", "extglob": "^2.0.2", "kind-of": "^6.0.0", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.5", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^1.0.0"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^3.0.1", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.2", "gulp-format-md": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch-3.1.5.tgz_1515387788781_0.****************", "host": "s3://npm-registry-packages"}}, "3.1.6": {"name": "micromatch", "version": "3.1.6", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@3.1.6", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "http://badassjs.com", "name": "Devon Govett"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "http://rumkin.com", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>ey"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "8d7c043b48156f408ca07a4715182b79b99420bf", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.6.tgz", "fileCount": 10, "integrity": "sha512-6hezhKgmSIRZeSCiVB84GOmH1Ajvo8XgnaEq/uPQ/wv0g+MQlaVonSEru7VMDZXzRWFoclakpADfInbg/5FGjw==", "signatures": [{"sig": "MEYCIQDquz7/kHy6HNgZC7lEW+zqiTPECpONjn2miJWSqks/BgIhAKeVX6S6Y7q7my0iqJ2Un5Pqanf5SvDCLchwH8d8Mkwf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84778}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "helpers": ["./benchmark/helper.js"], "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["expand-brackets", "extglob", "glob-object", "minimatch", "multimatch", "snapdragon"]}, "files": ["index.js", "lib"], "engines": {"node": ">=0.10.0"}, "gitHead": "ee0bdc03fdf2af275e4ba90b9b347a6b1b06b98c", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "lintDeps": {"dependencies": {"options": {"lock": {"snapdragon": "^0.8.1"}}}, "devDependencies": {"files": {"options": {"ignore": ["benchmark/**"]}}}}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "9.5.0", "dependencies": {"braces": "^2.3.1", "extglob": "^2.0.4", "kind-of": "^6.0.2", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.9", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "define-property": "^2.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.5.3", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^5.0.0", "is-windows": "^1.0.2", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.3", "gulp-format-md": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch_3.1.6_1518979328617_0.12166821717455067", "host": "s3://npm-registry-packages"}}, "3.1.7": {"name": "micromatch", "version": "3.1.7", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@3.1.7", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "http://badassjs.com", "name": "Devon Govett"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "http://rumkin.com", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>ey"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "ffbb25a79585d9cdbf9cb134ac21c5853dba0eb3", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.7.tgz", "fileCount": 10, "integrity": "sha512-uQTIoHx8MmOgQ/ZKAh9Oa4sGmn+wia5/QLQ5zBR5WCcPrnchTgUJCCEcZerQec67XqUSfE1OUEtUps/gRFYDSg==", "signatures": [{"sig": "MEUCIAfUWu5cT56LztiVw1z5NMK1RlsVGzQkvevsUE9CS4sFAiEAn0CSrWDYJidXMD+J2QMgsv9/bog98/HzifQs5XSADh4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84866}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "helpers": ["./benchmark/helper.js"], "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["expand-brackets", "extglob", "glob-object", "minimatch", "multimatch", "snapdragon"]}, "files": ["index.js", "lib"], "engines": {"node": ">=0.10.0"}, "gitHead": "c94b4b9422718b4f7870e6f3ed39ca52e39cc410", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "lintDeps": {"dependencies": {"options": {"lock": {"snapdragon": "^0.8.1"}}}, "devDependencies": {"files": {"options": {"ignore": ["benchmark/**"]}}}}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "9.5.0", "dependencies": {"braces": "^2.3.1", "extglob": "^2.0.4", "kind-of": "^6.0.2", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.9", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "define-property": "^2.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.5.3", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^5.0.0", "is-windows": "^1.0.2", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.3", "gulp-format-md": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch_3.1.7_1519156068859_0.7488830981175236", "host": "s3://npm-registry-packages"}}, "3.1.8": {"name": "micromatch", "version": "3.1.8", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@3.1.8", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "http://badassjs.com", "name": "Devon Govett"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "http://rumkin.com", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>ey"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "5c8caa008de588eebb395e8c0ad12c128f25fff1", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.8.tgz", "fileCount": 10, "integrity": "sha512-/XeuOQqYg+B5kwjDWekXseSwGS7CzE0w9Gjo4Cjkf/uFitNh47NrZHAY2vp/oS2YQVfebPIdbEIvgdy+kIcAog==", "signatures": [{"sig": "MEQCIDZU0OYhuhDhgYk0xW0MvGNYx7GsyzukhFlDvFKj4yrwAiBcDNc+zTkajJB3ariWqnLkrqBtMCGRxarLrVUWVyYM+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84866}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "helpers": ["./benchmark/helper.js"], "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["expand-brackets", "extglob", "glob-object", "minimatch", "multimatch", "snapdragon"]}, "files": ["index.js", "lib"], "engines": {"node": ">=0.10.0"}, "gitHead": "cd4a5aed01386d428b5387bca2dd206fb7c6563a", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "lintDeps": {"dependencies": {"options": {"lock": {"snapdragon": "^0.8.1"}}}, "devDependencies": {"files": {"options": {"ignore": ["benchmark/**"]}}}}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "9.5.0", "dependencies": {"braces": "^2.3.1", "extglob": "^2.0.4", "kind-of": "^6.0.2", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.9", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "define-property": "^2.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.5.3", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^5.0.0", "is-windows": "^1.0.2", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.3", "gulp-format-md": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch_3.1.8_1519157402444_0.7110008136370796", "host": "s3://npm-registry-packages"}}, "3.1.9": {"name": "micromatch", "version": "3.1.9", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@3.1.9", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "http://badassjs.com", "name": "Devon Govett"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "http://rumkin.com", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>ey"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "15dc93175ae39e52e93087847096effc73efcf89", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.9.tgz", "fileCount": 10, "integrity": "sha512-SlIz6sv5UPaAVVFRKodKjCg48EbNoIhgetzfK/Cy0v5U52Z6zB136M8tp0UC9jM53LYbmIRihJszvvqpKkfm9g==", "signatures": [{"sig": "MEQCIBKxgqIJnynUH8Zr3Pwt+JDZFnPf6ZW35KdvNRG4otNiAiB1bpciOpGUtMJ8rutbn8xPZR3Xgqb5RAu3ykamKiYTxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84810}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "helpers": ["./benchmark/helper.js"], "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["expand-brackets", "extglob", "glob-object", "minimatch", "multimatch", "snapdragon"]}, "files": ["index.js", "lib"], "engines": {"node": ">=0.10.0"}, "gitHead": "f4809eb6df80b47bf00b1d207ac16df3781a1950", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "lintDeps": {"dependencies": {"options": {"lock": {"snapdragon": "^0.8.1"}}}, "devDependencies": {"files": {"options": {"ignore": ["benchmark/**"]}}}}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "9.5.0", "dependencies": {"braces": "^2.3.1", "extglob": "^2.0.4", "kind-of": "^6.0.2", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.9", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "define-property": "^2.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.5.3", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^5.0.0", "is-windows": "^1.0.2", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.3", "gulp-format-md": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch_3.1.9_1519458972450_0.30081077663220257", "host": "s3://npm-registry-packages"}}, "3.1.10": {"name": "micromatch", "version": "3.1.10", "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@3.1.10", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "http://badassjs.com", "name": "Devon Govett"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "http://rumkin.com", "name": "<PERSON>"}, {"url": "https://github.com/<PERSON><PERSON>ey"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "70859bc95c9840952f359a068a3fc49f9ecfac23", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.10.tgz", "fileCount": 10, "integrity": "sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg==", "signatures": [{"sig": "MEQCIGeoxg8qcC+sShxYOjexCAJiJus+IviDFBpP9KD1PG1TAiBTg4Mnf6Mfhk0LjN93oRFEatuLRM5D8lf87pELil9YXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84811}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "helpers": ["./benchmark/helper.js"], "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["expand-brackets", "extglob", "glob-object", "minimatch", "multimatch", "snapdragon"]}, "files": ["index.js", "lib"], "engines": {"node": ">=0.10.0"}, "gitHead": "0628af9a111c791ca69c809a6f8555337813cc05", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "lintDeps": {"dependencies": {"options": {"lock": {"snapdragon": "^0.8.1"}}}, "devDependencies": {"files": {"options": {"ignore": ["benchmark/**"]}}}}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "5.7.1", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "9.7.1", "dependencies": {"braces": "^2.3.1", "extglob": "^2.0.4", "kind-of": "^6.0.2", "arr-diff": "^4.0.0", "to-regex": "^3.0.2", "nanomatch": "^1.2.9", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "define-property": "^2.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.5.3", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^5.0.0", "is-windows": "^1.0.2", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.3", "gulp-format-md": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch_3.1.10_1521747888988_0.6546863443028648", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "micromatch", "version": "4.0.0", "keywords": ["bash", "bracket", "character-class", "expand", "expansion", "expression", "extglob", "extglobs", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "<PERSON><PERSON><PERSON>", "lookaround", "lookbehind", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "negate", "negation", "path", "pattern", "patterns", "posix", "regex", "regexp", "regular", "shell", "star", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@4.0.0", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/<PERSON><PERSON>ey"}, {"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "http://badassjs.com", "name": "Devon Govett"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "http://rumkin.com", "name": "<PERSON>"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "3d9e7a815fabfb009a10fa5adc268242c6d6088e", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-THzpRAtp/NcyqnAzYwvP9V1bMAM4zFs2AR02wwxNLzEbi6Mn2suaQ6lhiD8Ug+X3L3g9grohOe1NGb2m+72eeA==", "signatures": [{"sig": "MEUCIG8Nv2u2v5npz0fskWd8wdUjHxcxhvN4vW0D1nOESCuiAiEAksGXoWEYSjabpNhcDW4S6GGscTEzXIqGjrppFQkWBHE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60867, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrecVCRA9TVsSAnZWagAAJ6QP/RpXqLXDqtzJnJEDwB6y\npMzgcaBqWbIwCfuZpAywyekDFtWYD5PyObgwyWAUupCAw8fqqUiWTIzBZJ/s\nrHklis7ox/angLUFrfTQGEU71THjY3eR2rTHtLJOEwlE5Q3LcHv+39nCfJru\nuiNH19sdV2QJGLdvBRJ7AmvHn7iTi/DrblTAXxtVr/crzu2NHrIYIj3gdwfK\n2swdZB2XC7tyWEOGp/ldBVMZXscbiK0yI5N/J2X9UwY0WBLJorg0uAX1Cnxw\n/HuwEbuRI8yTBvd46JQ7o0QOYCgUaUz2+4Cy4dvkoY3ElGAw/JkcotMi8I5e\nxXRZZCjPn4VfF2hOBxNy0EPhX5rnG9OhrU9kF6TQNuQRWlyFsRV7TwvNBb/1\nVtr5UHozdVmsjRZm/RpuVT498AnVTTCS0YgQZoGXoixKfAbM1UTGmT9+DTOq\nrsir0Dq2zEdwKGdZdi5qFVkYzvtVihVOxA90wozkz0K4UI0qVIURX3p6rkAO\nAyH+C/KbTX7oeckmaczGl/ph4yQLNTySnhls9fMWQlsiw/o5jeBmtO1fR8Ks\nonetBTOjtP5m2ZSx0R06WBNfNHYSD8SMHOYRVVoUTf7tYnA+zFoiPE/fVOsO\nDqVb8hg/ufy+JGmz7ait0hlOIFDNsaQGd/2UXoQ/8gwHvkMF5nxb9iCbeKnE\nwoZy\r\n=oCfg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["extglob", "fill-range", "glob-object", "minimatch", "multimatch"]}, "engines": {"node": ">=8"}, "gitHead": "89efcff8d4676074ef6fb4037c3ccc5cd3001c7e", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Glob matching for javascript/node.js. A replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "11.9.0", "dependencies": {"braces": "^3.0.1", "picomatch": "^2.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "minimatch": "^3.0.4", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch_4.0.0_1554900756329_0.153767293498708", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "micromatch", "version": "4.0.1", "keywords": ["bash", "bracket", "character-class", "expand", "expansion", "expression", "extglob", "extglobs", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "<PERSON><PERSON><PERSON>", "lookaround", "lookbehind", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "negate", "negation", "path", "pattern", "patterns", "posix", "regex", "regexp", "regular", "shell", "star", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@4.0.1", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/<PERSON><PERSON>ey"}, {"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "http://badassjs.com", "name": "Devon Govett"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "http://rumkin.com", "name": "<PERSON>"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "1bebd1a6e8dd8cff4669f3a687bdad62fb18571e", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.1.tgz", "fileCount": 5, "integrity": "sha512-6yawNHAc4S9Dh81xZCkZ5sXKH0/ly0t1DiOc+rnqzi0OvwS4DgRZU+HYTNDIgULgZXTNw5N8Vhxh2va8nEO6BA==", "signatures": [{"sig": "MEQCIGRbsJVQGWegHhEUMDcpjKmVeHvvVJ5qRxVxXYA+mPUgAiBxBHCJCj655l8LrStyDwKnvW+L9ExCbWOVKXcmMVRaMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60935, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrhliCRA9TVsSAnZWagAAqKwP/00itZNKIjobjQSjWOIU\neLDx6eB9KNmhlkVKloBKta1azT+dpfHFdkF2uUvt+0oFXOt5rIj9mF6zD5Cz\nvrQR2+nTZvWnYsDya3Enge/B9+g0G5GtOGuRHD2KdUeVx92g83+RzF2I/EYx\nbs9YYIOV1/X7bx1VnnbLgce6tTP4ZgCxXyj7wAGAX2b+YqbNs6gki1eQpwFw\n/Gq1vrg+sVYr2bj+P6oZ1LWW8lKBIqfL2AtfE+UF4WFAoIuir/CYRqyczKc3\nm4PK61kPJkoRDXcqFsdwJlcAS1p2DrV6J7cYIzklIBHpdaDFRRDLVScu3Pc3\nlwjRjLNk2QO5speTIX7Vvq4pNMYdsytp7Puwv6hORnCR7qRnOFg6FPVXp+2O\n9s3x0CkHt+8HnBheKSxAb4IdEyyymYBWfJf9sq8qQCZY28bRJ9kzPOT58LPm\nas1ZG/SbBL6+pV1VMQmw8sC8fgyFYSPDWRcGDoCkIaHpfnYbYe94F7DPDcr9\nC7IjpKjq+myuQZYwCeB1CZ2/7AmnOM8Prm/o1nqHi5fPB1JBh4IGlQRaYTYX\nq3em8Io2ShysO+kFjLwdoado2CoIwWuNik2W+76tvI2sOoQhbAi+vyzEtGwJ\npefMpRrH/YVSGo9lZVsSksSV9o0lboslHM8ee7a1SzFh41cpwKg7/mPOMPpn\noymU\r\n=bDk9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["extglob", "fill-range", "glob-object", "minimatch", "multimatch"]}, "engines": {"node": ">=8"}, "gitHead": "9882642b36d4d2a52631e98417e256ab75b299d4", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Glob matching for javascript/node.js. A replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "11.9.0", "dependencies": {"braces": "^3.0.1", "picomatch": "^2.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "minimatch": "^3.0.4", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch_4.0.1_1554913633605_0.46743018059672425", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "micromatch", "version": "4.0.2", "keywords": ["bash", "bracket", "character-class", "expand", "expansion", "expression", "extglob", "extglobs", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "<PERSON><PERSON><PERSON>", "lookaround", "lookbehind", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "negate", "negation", "path", "pattern", "patterns", "posix", "regex", "regexp", "regular", "shell", "star", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@4.0.2", "maintainers": [{"name": "doowb", "email": "<EMAIL>"}, {"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/<PERSON><PERSON>ey"}, {"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "http://badassjs.com", "name": "Devon Govett"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "http://rumkin.com", "name": "<PERSON>"}, {"url": "https://github.com/drpizza", "name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "4fcb0999bf9fbc2fcbdd212f6d629b9a56c39259", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.2.tgz", "fileCount": 5, "integrity": "sha512-y7FpHSbMUMoyPbYUSzO6PaZ6FyRnQOpHuKwbo1G+Knck95XVU4QAiKdGEnj5wwoS7PlOgthX/09u5iFJ+aYf5Q==", "signatures": [{"sig": "MEUCIGnf+QkvkGb3nS89NKrdP+irw3EvZmC8I3aEGLArgC8pAiEAtzJB+K98yi4GYnE9tAbutn6SiXqtwtvUtArcPqnz/wE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61091, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcuz5SCRA9TVsSAnZWagAAdtEP/2PjcydeP1zQ8zsCdqIP\ndJHWFH5DgLB/etZdmB9chTBrY2Ns7eSI8wv/Am3S0z3K78xE5ncjXu0RXN/D\n9O1ojsBWsY/tZhMDZ5Gj9rPCpHB5jj44ch4KTlfSQ/aWbKk7pDB8nP5yZkZa\nQYsZvJd/igGO6EI5b7Jfw/OvO2X21MYdVd2LfdLYsTjsdIJ0/D0leYkvfC1R\nWtFlmicwbskUedFyCxhizVIkKhhqYgIKQbeeOyCbmU7U+yoaGd4PXkKiwqkm\nKreiRsy8tLLEcrdZKwMsrM+HC8JwhNcs4WuhaGEYU85aPaESs1XRJ5Nwryac\nb7KiZc82/T/D+1bjZdmi85imDh8gJGlJfzusnFcFHQZ45Y7GJn5ARkVhkcWs\nfHBEYYaAqzjdVcotzS0UAx984/XWllu35HqCj9FGsLMolFFHSOCIkKW6P9at\n1xImdNKCo5Q7cP1ckUS1cRJ0p7glmGz1F2H20fKB+zNl50awIv4VXQOkOWQA\nCecaTn36GGQ1LUe9jJRjJ8rCyfhKkSzOVlHpHDmdOYdZ4Z5LCKOWxZamAMk/\nWffyYGSnfS5oYuyv4+dT/IS/smLa4j92PIicXghcDeIj/9KG+bytehmHM8Qz\njyCvu8NLoHDrQvOLtyaJKwxRRE7jLa00a83Ond77/8swqDbFP4LnF8pZzQod\n/UAK\r\n=zQak\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["extglob", "fill-range", "glob-object", "minimatch", "multimatch"]}, "engines": {"node": ">=8"}, "gitHead": "9107f5abe6f71e36fe19dda33156a53c4debb975", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Glob matching for javascript/node.js. A replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"braces": "^3.0.1", "picomatch": "^2.0.5"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "minimatch": "^3.0.4", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch_4.0.2_1555775057353_0.05828778084513231", "host": "s3://npm-registry-packages"}}, "4.0.3": {"name": "micromatch", "version": "4.0.3", "keywords": ["bash", "bracket", "character-class", "expand", "expansion", "expression", "extglob", "extglobs", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "<PERSON><PERSON><PERSON>", "lookaround", "lookbehind", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "negate", "negation", "path", "pattern", "patterns", "posix", "regex", "regexp", "regular", "shell", "star", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@4.0.3", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/<PERSON><PERSON>ey"}, {"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "http://badassjs.com", "name": "Devon Govett"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "http://rumkin.com", "name": "<PERSON>"}, {"url": "https://github.com/drpizza", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ku8ar", "name": "<PERSON><PERSON>"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "fdad8352bf0cbeb89b391b5d244bc22ff3dd4ec8", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.3.tgz", "fileCount": 5, "integrity": "sha512-ueuSaP4i67F/FAUac9zzZ0Dz/5KeKDkITYIS/k4fps+9qeh1SkeH6gbljcqz97mNBOsaWZ+iv2UobMKK/yD+aw==", "signatures": [{"sig": "MEYCIQD+I18D/0/eRPwauREFlC4jqnNxgOjstANBkRlVHV5rqgIhAOGNYPv4wEVe9x0z7r32EzNtuNGrxVqStPiaOyEb7znM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61070, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgcJ6mCRA9TVsSAnZWagAAyWYP/32yylOWx10uzcBcYU9F\noQMI0YR2Xj7odh/2EQLukgzKILkjyYzib1P1fldyjkoUOah33tkb+2fZeHC8\nePBOU+ofhhFpLNzxt6radc7yiPL5JVNBarCA+9fdciyP4Ztvi9ctjU49fFCS\niLWSv+Et1AgSRnjZORoNwet7XNLnP1Y406KqJWfWLom7ribwLHoyy8Bpn21N\nqI4FNXVgLsuJLmFYcmQ3cIVgnWdf6rCzWW6MhrGW8tn7CoYaPUmyc6ErRtBQ\ns13MXxi/xSNZFEnegEZT+LubNq6Yj2oPYzKW/lyO6YooYbLtrmj60Fz+6/wd\nm/I1hfdefMHbeZZKYL/aHHUAtyBv0td/YJoB9OJoagCTwZZgiWaKpvRMOwqR\nmx58sVvN4XymluXnNtl9VLI8gR84iyCQUBBKgwTVR62d9fbCqhD/op2CULxt\ni9FkG4iBIxG3ZNZVCpl9Pt0v5zIBLaT7ZeaK5L3ElUMx1mDc+lesVDDSVSVx\nCCPLI3Nv5opASPH/rzHcpq6Sjm+AmadzWKxD+A/VLgsjcV3py+fFBRnr4fKV\nmkQV+dDCckawbPh94hWgews64g2KIVMZknFzqAtrNWxrcGxUHy5nD6uFR2Zu\nz8UqH3Ey+4eBF9OYJdwwmEEVU7M6VLbKix2MMXEv1fjhlz2dCJayj0TljaR4\nOS70\r\n=h3Ju\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["extglob", "fill-range", "glob-object", "minimatch", "multimatch"]}, "engines": {"node": ">=8.6"}, "gitHead": "7a63a2a4e33e4910fd5774999111ac96a5e9f518", "scripts": {"test": "mocha"}, "_npmUser": {"name": "danez", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Glob matching for javascript/node.js. A replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "12.15.0", "dependencies": {"braces": "^3.0.1", "picomatch": "^2.2.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^5.2.0", "minimatch": "^3.0.4", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch_4.0.3_1617993381955_0.3543291442572918", "host": "s3://npm-registry-packages"}}, "4.0.4": {"name": "micromatch", "version": "4.0.4", "keywords": ["bash", "bracket", "character-class", "expand", "expansion", "expression", "extglob", "extglobs", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "<PERSON><PERSON><PERSON>", "lookaround", "lookbehind", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "negate", "negation", "path", "pattern", "patterns", "posix", "regex", "regexp", "regular", "shell", "star", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@4.0.4", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/<PERSON><PERSON>ey"}, {"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "http://badassjs.com", "name": "Devon Govett"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "http://rumkin.com", "name": "<PERSON>"}, {"url": "https://github.com/drpizza", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ku8ar", "name": "<PERSON><PERSON>"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "896d519dfe9db25fce94ceb7a500919bf881ebf9", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.4.tgz", "fileCount": 5, "integrity": "sha512-pRmzw/XUcwXGpD9aI9q/0XOwLNygjETJ8y0ao0wdqprrzDa4YnxLcz7fQRZr8voh8V10kGhABbNcHVk5wHgWwg==", "signatures": [{"sig": "MEUCIBiTPFiWkxkgth1Ur4hsZLlgNSwQZVdZ92O74GcPd2gvAiEAhdkBvbSxJdbA/pZrTLkUsTcc62lEv/YVQu3c/+BDn2k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgcY4/CRA9TVsSAnZWagAA1BYQAIteJIuhbmNS9RzGRM6R\n78wakYXfLd9YHjaKU9UwW2xoPFn0OoIIKodcHHDOOfWGHe96rDOIYqvNFo+S\n3cidwhFh7EePKlyL9EpPKCGXaG2JuCeJ3nB76kCt2m4+1AOh/+oDiwBT5wNe\nS1mirQzU+GAyEVRgQHRKWn/VBvtjG4aG4teKhIuAr4ZgJh7hMUy1cJY1pBd1\nORGU0ytLPHsA6C60QHclvV3J5Rser+DtcueoEzHhykrr/5EGVa0A77gW1kz5\naYn0d8Vx0s8Y2LiPOnL2i4s42Z2U7RT3VjivYIxtD3yakB+fkXIghEDxC66D\nTgOvEyBhueXfHhcpS3Rg6rZQXw/fbGwAckgHSTmfck7zmVxs6x8n5RWJzmlP\nWAv343VYqFMHd2m11XL1KKQ1I6mtda+UwpiYrOHr1DeEqe3LaFlFNDVc8qQO\nfCfviEcGdv7T9iBbSYoNlY9qwoku9wDwCXtZ1lZsPhc4xeMmdveXq/uOTv2r\nxCDsWsoRcIXJfKJYjbYChCwpTDs/EabXIHwWyKPFz0FDhGN7qGLLOB7zQIYP\n9lOlRIv6xRZQH7+4NMkfyKBy6e1HncM3JE0oy7SAPE4sfkgfDnUiPYadUL9w\nS6+wf19JBYVYJzqrwgyLc1iQ3rdWc8DKb8IDimJYFToznbnriqo5p2+ZbNqE\nb3Gm\r\n=/5bc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["extglob", "fill-range", "glob-object", "minimatch", "multimatch"]}, "engines": {"node": ">=8.6"}, "gitHead": "5318752abc2f33153b3ccddf9f1f3b7682000a43", "scripts": {"test": "mocha"}, "_npmUser": {"name": "danez", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Glob matching for javascript/node.js. A replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "12.15.0", "dependencies": {"braces": "^3.0.1", "picomatch": "^2.2.3"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^7.2.0", "minimatch": "^3.0.4", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch_4.0.4_1618054718788_0.2184613723247324", "host": "s3://npm-registry-packages"}}, "4.0.5": {"name": "micromatch", "version": "4.0.5", "keywords": ["bash", "bracket", "character-class", "expand", "expansion", "expression", "extglob", "extglobs", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "<PERSON><PERSON><PERSON>", "lookaround", "lookbehind", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "negate", "negation", "path", "pattern", "patterns", "posix", "regex", "regexp", "regular", "shell", "star", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@4.0.5", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/<PERSON><PERSON>ey"}, {"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "http://badassjs.com", "name": "Devon Govett"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "http://rumkin.com", "name": "<PERSON>"}, {"url": "https://github.com/drpizza", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ku8ar", "name": "<PERSON><PERSON>"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "bc8999a7cbbf77cdc89f132f6e467051b49090c6", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.5.tgz", "fileCount": 4, "integrity": "sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==", "signatures": [{"sig": "MEUCIQDMlu9ADvGDYCdgefb3WrC+hs7KxtMOM9aYsev2uDY48AIgAu/o5IQA/odSPqSKWXPeuw7mx4VfBHkCxGcEIlxO1S0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55947, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiPMcjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1vBAAloppMNdWgbswe+RXiqrcsSpra8OFTxbwOOd8l0+tydhwJ8yS\r\n9tANTGLGX8w14fAdSV8IebVjjq1QNWQLlH0ofpRb4TOAYBclFkx4WMPp8s9T\r\n+vUC4fZyJsAd3QzfuWGMcUya9h8AX/S4Np3wwGnkQfg0eqpjxtkUNNyjJ0wi\r\nZeogQsi4SajN4SaJMs6Uef4PqJiyhLYyZVFMMPfXKtFud2q6hlp6LW+/v3k4\r\nV/T31dRooeUuCwADQSynf0Y05Zf9I6aEBk0FuDty6HtWf9pYZrFKGCK9JVBT\r\namijpFImSgxjL1zkytZ+p8d/+L0+IImCBVyhqH7MBEgO7IkVrjo6nMiJU/+A\r\nnh9AeYFZ0Hv0hAUZEtG+f8bKZ7egl3odWFOB9zH6+hfuJe2TwPIGGnQZAUK0\r\n+7n5i5SKsjwbAkpeIgm/1bgqW6QBPHnfGgTF9PyUgn6Hko8/688z2sfmiwbb\r\n7sZckXtWTo/vZ2K/364k19Vr4FI+HIp4BndAk2TA/zxfij99Pfu/I0c8fthV\r\nJq5AEgYu9HFWz7nFHRMHLQF0Y5cQwtMrWOy990USCEYyaQNjkJIL6oVZvNHS\r\nx+WUEmpVBB9BjtxH/ADQ8SAspnEFIs4/8mEOrsp49rF4joloFAMNXfKeSKWv\r\n8GCopBneL1eOO6NG/cXZ/M41/o9lUxbVBrQ=\r\n=lDr1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["extglob", "fill-range", "glob-object", "minimatch", "multimatch"]}, "engines": {"node": ">=8.6"}, "gitHead": "002d0d184c95e76775528fa1dbe0c446518879b2", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Glob matching for javascript/node.js. A replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "17.0.0", "dependencies": {"braces": "^3.0.2", "picomatch": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^9.2.2", "minimatch": "^5.0.1", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch_4.0.5_1648150307533_0.5782404001766006", "host": "s3://npm-registry-packages"}}, "4.0.6": {"name": "micromatch", "version": "4.0.6", "keywords": ["bash", "bracket", "character-class", "expand", "expansion", "expression", "extglob", "extglobs", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "<PERSON><PERSON><PERSON>", "lookaround", "lookbehind", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "negate", "negation", "path", "pattern", "patterns", "posix", "regex", "regexp", "regular", "shell", "star", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@4.0.6", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/<PERSON><PERSON>ey"}, {"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "http://badassjs.com", "name": "Devon Govett"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "http://rumkin.com", "name": "<PERSON>"}, {"url": "https://github.com/drpizza", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ku8ar", "name": "<PERSON><PERSON>"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "ab4e37c42726b9cd788181ba4a2a4fead8e394a3", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.6.tgz", "fileCount": 4, "integrity": "sha512-Y4Ypn3oujJYxJcMacVgcs92wofTHxp9FzfDpQON4msDefoC0lb3ETvQLOdLcbhSwU1bz8HrL/1sygfBIHudrkQ==", "signatures": [{"sig": "MEUCIQCbYyO7EgwmokmY6Vs+PiniiJAHS7rpu7RijTE8hhOBXgIgSp95sJVjqMVu8e9W3JwuS4Q2uvqAIqeLKQZrG2OUQlE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57044}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["extglob", "fill-range", "glob-object", "minimatch", "multimatch"]}, "engines": {"node": ">=8.6"}, "gitHead": "8609d53c15bb5e289a9fb12be4f48f3e052dfa7b", "scripts": {"test": "mocha"}, "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Glob matching for javascript/node.js. A replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "20.12.1", "dependencies": {"braces": "^3.0.3", "picomatch": "^4.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^10.4.0", "minimatch": "^9.0.3", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch_4.0.6_1716282939053_0.859840264170838", "host": "s3://npm-registry-packages"}}, "4.0.7": {"name": "micromatch", "version": "4.0.7", "keywords": ["bash", "bracket", "character-class", "expand", "expansion", "expression", "extglob", "extglobs", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "<PERSON><PERSON><PERSON>", "lookaround", "lookbehind", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "negate", "negation", "path", "pattern", "patterns", "posix", "regex", "regexp", "regular", "shell", "star", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@4.0.7", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/<PERSON><PERSON>ey"}, {"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "http://badassjs.com", "name": "Devon Govett"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "http://rumkin.com", "name": "<PERSON>"}, {"url": "https://github.com/drpizza", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ku8ar", "name": "<PERSON><PERSON>"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "33e8190d9fe474a9895525f5618eee136d46c2e5", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.7.tgz", "fileCount": 4, "integrity": "sha512-LPP/3KorzCwBxfeUuZmaR6bG2kdeHSbe0P2tY3FLRU4vYrjYz5hI4QZwV0njUx3jeuKe67YukQ1LSPZBKDqO/Q==", "signatures": [{"sig": "MEUCIQCoqmLDH3dRKVCJvEJ3ReCeqMKCKVg7rGGyiumTxoU3SQIgEDFoR6C5ze70X6ZhoFpQsadAVMROyLvvy0GYhBM2QNc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56265}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["extglob", "fill-range", "glob-object", "minimatch", "multimatch"]}, "engines": {"node": ">=8.6"}, "gitHead": "07b887056a0be8c0212cffe9dc7e75fd188e4792", "scripts": {"test": "mocha"}, "_npmUser": {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Glob matching for javascript/node.js. A replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "22.1.0", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^9.2.2", "minimatch": "^5.0.1", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch_4.0.7_1716352428951_0.9677974696155767", "host": "s3://npm-registry-packages"}}, "4.0.8": {"name": "micromatch", "version": "4.0.8", "keywords": ["bash", "bracket", "character-class", "expand", "expansion", "expression", "extglob", "extglobs", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "<PERSON><PERSON><PERSON>", "lookaround", "lookbehind", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "negate", "negation", "path", "pattern", "patterns", "posix", "regex", "regexp", "regular", "shell", "star", "wildcard"], "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "_id": "micromatch@4.0.8", "maintainers": [{"name": "es128", "email": "<EMAIL>"}, {"name": "p<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "doowb", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/<PERSON><PERSON>ey"}, {"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "http://badassjs.com", "name": "Devon Govett"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "http://rumkin.com", "name": "<PERSON>"}, {"url": "https://github.com/drpizza", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ku8ar", "name": "<PERSON><PERSON>"}], "homepage": "https://github.com/micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "dist": {"shasum": "d66fa18f3a47076789320b9b1af32bd86d9fa202", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz", "fileCount": 4, "integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==", "signatures": [{"sig": "MEUCIQCbM5vTauiWZIyEboHJF/YXuneSxEyEJ+2VHaEdyLD4zQIgU0seoGyOpfB4e80kPjxYidBvIgonDpZpZzNN8w25BUU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56599}, "main": "index.js", "verb": {"toc": "collapsible", "lint": {"reflinks": true}, "tasks": ["readme"], "layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "reflinks": ["extglob", "fill-range", "glob-object", "minimatch", "multimatch"]}, "engines": {"node": ">=8.6"}, "gitHead": "8bd704ec0d9894693d35da425d827819916be920", "scripts": {"test": "mocha"}, "_npmUser": {"name": "doowb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Glob matching for javascript/node.js. A replacement and faster alternative to minimatch and multimatch.", "directories": {}, "_nodeVersion": "20.15.0", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "_hasShrinkwrap": false, "devDependencies": {"mocha": "^9.2.2", "minimatch": "^5.0.1", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/micromatch_4.0.8_1724430678511_0.3871728344233747", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2014-11-25T08:34:18.841Z", "modified": "2024-09-18T05:25:40.049Z", "0.1.0": "2014-11-25T08:34:18.841Z", "0.2.0": "2014-12-23T10:52:17.669Z", "0.2.1": "2014-12-23T11:14:34.171Z", "0.2.2": "2014-12-23T11:38:01.639Z", "1.0.0": "2014-12-28T17:28:33.699Z", "1.0.1": "2014-12-31T22:33:41.700Z", "1.2.0": "2015-01-30T13:23:11.065Z", "1.2.2": "2015-01-30T14:09:46.938Z", "1.3.0": "2015-02-11T15:07:10.882Z", "1.3.1": "2015-02-11T19:52:26.641Z", "1.3.2": "2015-02-15T21:42:09.237Z", "1.3.3": "2015-02-16T00:44:26.767Z", "1.4.0": "2015-02-17T12:59:05.326Z", "1.4.1": "2015-02-20T10:12:33.185Z", "1.4.2": "2015-02-20T11:54:42.260Z", "1.4.3": "2015-03-01T15:25:34.698Z", "1.4.4": "2015-03-01T18:57:48.920Z", "1.4.5": "2015-03-01T23:29:23.978Z", "1.5.0": "2015-03-04T13:44:58.182Z", "1.6.0": "2015-03-06T20:11:07.047Z", "1.6.1": "2015-03-06T20:31:21.594Z", "1.6.2": "2015-03-06T20:40:29.945Z", "2.0.0": "2015-03-06T23:19:24.208Z", "2.1.0": "2015-03-09T02:45:52.457Z", "2.1.1": "2015-03-27T00:49:51.230Z", "2.1.2": "2015-03-27T01:09:40.793Z", "2.1.3": "2015-03-27T02:12:58.010Z", "2.1.4": "2015-03-27T14:19:52.288Z", "2.1.5": "2015-03-28T03:15:48.589Z", "2.1.6": "2015-04-14T21:06:50.733Z", "2.2.0": "2015-07-24T08:42:17.058Z", "2.3.0": "2015-11-09T05:38:34.744Z", "2.3.1": "2015-11-10T02:02:58.579Z", "2.3.2": "2015-11-10T04:33:31.801Z", "2.3.3": "2015-11-25T21:13:24.810Z", "2.3.4": "2015-12-06T20:33:21.660Z", "2.3.5": "2015-12-06T22:24:58.008Z", "2.3.6": "2015-12-19T22:49:45.905Z", "2.3.7": "2015-12-19T22:56:23.928Z", "2.3.8": "2016-04-22T21:45:52.692Z", "2.3.9": "2016-06-21T20:36:31.732Z", "2.3.10": "2016-06-21T21:13:15.140Z", "2.3.11": "2016-07-15T17:15:32.430Z", "3.0.0": "2017-05-29T06:11:07.207Z", "3.0.1": "2017-05-29T06:19:19.742Z", "3.0.2": "2017-05-31T12:02:05.271Z", "3.0.3": "2017-06-02T09:02:00.857Z", "3.0.4": "2017-07-11T19:04:51.243Z", "3.0.5": "2017-09-08T02:19:51.428Z", "3.1.0": "2017-09-11T05:39:04.252Z", "3.1.1": "2017-10-21T00:39:01.284Z", "3.1.2": "2017-10-21T01:41:36.491Z", "3.1.3": "2017-10-21T01:53:19.815Z", "3.1.4": "2017-10-30T08:19:54.431Z", "3.1.5": "2018-01-08T05:03:10.674Z", "3.1.6": "2018-02-18T18:42:08.701Z", "3.1.7": "2018-02-20T19:47:48.948Z", "3.1.8": "2018-02-20T20:10:03.294Z", "3.1.9": "2018-02-24T07:56:12.525Z", "3.1.10": "2018-03-22T19:44:49.213Z", "4.0.0": "2019-04-10T12:52:36.474Z", "4.0.1": "2019-04-10T16:27:13.748Z", "4.0.2": "2019-04-20T15:44:17.473Z", "4.0.3": "2021-04-09T18:36:22.117Z", "4.0.4": "2021-04-10T11:38:38.918Z", "4.0.5": "2022-03-24T19:31:47.722Z", "4.0.6": "2024-05-21T09:15:39.179Z", "4.0.7": "2024-05-22T04:33:49.109Z", "4.0.8": "2024-08-23T16:31:18.748Z"}, "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "author": {"url": "https://github.com/jonschlinkert", "name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/micromatch/micromatch", "keywords": ["bash", "bracket", "character-class", "expand", "expansion", "expression", "extglob", "extglobs", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "<PERSON><PERSON><PERSON>", "lookaround", "lookbehind", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "negate", "negation", "path", "pattern", "patterns", "posix", "regex", "regexp", "regular", "shell", "star", "wildcard"], "repository": {"url": "git+https://github.com/micromatch/micromatch.git", "type": "git"}, "description": "Glob matching for javascript/node.js. A replacement and faster alternative to minimatch and multimatch.", "contributors": [{"url": "https://github.com/<PERSON><PERSON>ey"}, {"url": "amilajack.com", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/TrySound", "name": "<PERSON><PERSON><PERSON>"}, {"url": "https://twitter.com/doowb", "name": "<PERSON>"}, {"url": "http://badassjs.com", "name": "Devon Govett"}, {"url": "https://github.com/es128", "name": "<PERSON><PERSON>"}, {"url": "https://ultcombo.js.org", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "http://twitter.com/jonschlinkert", "name": "<PERSON>"}, {"url": "https://kolarik.sk", "name": "<PERSON>"}, {"url": "https://i.am.charlike.online", "name": "<PERSON><PERSON><PERSON>"}, {"url": "paulmillr.com", "name": "<PERSON>"}, {"url": "https://github.com/tomByrer", "name": "<PERSON>"}, {"url": "http://rumkin.com", "name": "<PERSON>"}, {"url": "https://github.com/drpizza", "name": "<PERSON>", "email": "<EMAIL>"}, {"url": "https://github.com/ku8ar", "name": "<PERSON><PERSON>"}], "maintainers": [{"email": "<EMAIL>", "name": "j<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "doowb"}, {"email": "<EMAIL>", "name": "danez"}], "readme": "# micromatch [![NPM version](https://img.shields.io/npm/v/micromatch.svg?style=flat)](https://www.npmjs.com/package/micromatch) [![NPM monthly downloads](https://img.shields.io/npm/dm/micromatch.svg?style=flat)](https://npmjs.org/package/micromatch) [![NPM total downloads](https://img.shields.io/npm/dt/micromatch.svg?style=flat)](https://npmjs.org/package/micromatch)  [![Tests](https://github.com/micromatch/micromatch/actions/workflows/test.yml/badge.svg)](https://github.com/micromatch/micromatch/actions/workflows/test.yml)\n\n> Glob matching for javascript/node.js. A replacement and faster alternative to minimatch and multimatch.\n\nPlease consider following this project's author, [<PERSON>](https://github.com/jonschlink<PERSON>), and consider starring the project to show your :heart: and support.\n\n## Table of Contents\n\n<details>\n<summary><strong>Details</strong></summary>\n\n  * [Install](#install)\n- [Sponsors](#sponsors)\n  * [Gold Sponsors](#gold-sponsors)\n  * [Quickstart](#quickstart)\n  * [Why use micromatch?](#why-use-micromatch)\n    + [Matching features](#matching-features)\n  * [Switching to micromatch](#switching-to-micromatch)\n    + [From minimatch](#from-minimatch)\n    + [From multimatch](#from-multimatch)\n  * [API](#api)\n  * [Options](#options)\n  * [Options Examples](#options-examples)\n    + [options.basename](#optionsbasename)\n    + [options.bash](#optionsbash)\n    + [options.expandRange](#optionsexpandrange)\n    + [options.format](#optionsformat)\n    + [options.ignore](#optionsignore)\n    + [options.matchBase](#optionsmatchbase)\n    + [options.noextglob](#optionsnoextglob)\n    + [options.nonegate](#optionsnonegate)\n    + [options.noglobstar](#optionsnoglobstar)\n    + [options.nonull](#optionsnonull)\n    + [options.nullglob](#optionsnullglob)\n    + [options.onIgnore](#optionsonignore)\n    + [options.onMatch](#optionsonmatch)\n    + [options.onResult](#optionsonresult)\n    + [options.posixSlashes](#optionsposixslashes)\n    + [options.unescape](#optionsunescape)\n  * [Extended globbing](#extended-globbing)\n    + [Extglobs](#extglobs)\n    + [Braces](#braces)\n    + [Regex character classes](#regex-character-classes)\n    + [Regex groups](#regex-groups)\n    + [POSIX bracket expressions](#posix-bracket-expressions)\n  * [Notes](#notes)\n    + [Bash 4.3 parity](#bash-43-parity)\n    + [Backslashes](#backslashes)\n  * [Benchmarks](#benchmarks)\n    + [Running benchmarks](#running-benchmarks)\n    + [Latest results](#latest-results)\n  * [Contributing](#contributing)\n  * [About](#about)\n\n</details>\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save micromatch\n```\n\n<br />\n\n# Sponsors\n\n[Become a Sponsor](https://github.com/sponsors/jonschlinkert) to add your logo to this README, or any of [my other projects](https://github.com/jonschlinkert?tab=repositories&q=&type=&language=&sort=stargazers)\n\n<br />\n\n## Quickstart\n\n```js\nconst micromatch = require('micromatch');\n// micromatch(list, patterns[, options]);\n```\n\nThe [main export](#micromatch) takes a list of strings and one or more glob patterns:\n\n```js\nconsole.log(micromatch(['foo', 'bar', 'baz', 'qux'], ['f*', 'b*'])) //=> ['foo', 'bar', 'baz']\nconsole.log(micromatch(['foo', 'bar', 'baz', 'qux'], ['*', '!b*'])) //=> ['foo', 'qux']\n```\n\nUse [.isMatch()](#ismatch) to for boolean matching:\n\n```js\nconsole.log(micromatch.isMatch('foo', 'f*')) //=> true\nconsole.log(micromatch.isMatch('foo', ['b*', 'f*'])) //=> true\n```\n\n[Switching](#switching-to-micromatch) from minimatch and multimatch is easy!\n\n<br>\n\n## Why use micromatch?\n\n> micromatch is a [replacement](#switching-to-micromatch) for minimatch and multimatch\n\n* Supports all of the same matching features as [minimatch](https://github.com/isaacs/minimatch) and [multimatch](https://github.com/sindresorhus/multimatch)\n* More complete support for the Bash 4.3 specification than minimatch and multimatch. Micromatch passes _all of the spec tests_ from bash, including some that bash still fails.\n* **Fast & Performant** - Loads in about 5ms and performs [fast matches](#benchmarks).\n* **Glob matching** - Using wildcards (`*` and `?`), globstars (`**`) for nested directories\n* **[Advanced globbing](#extended-globbing)** - Supports [extglobs](#extglobs), [braces](#braces-1), and [POSIX brackets](#posix-bracket-expressions), and support for escaping special characters with `\\` or quotes.\n* **Accurate** - Covers more scenarios [than minimatch](https://github.com/yarnpkg/yarn/pull/3339)\n* **Well tested** - More than 5,000 [test assertions](./test)\n* **Windows support** - More reliable windows support than minimatch and multimatch.\n* **[Safe](https://github.com/micromatch/braces#braces-is-safe)** - Micromatch is not subject to DoS with brace patterns like minimatch and multimatch.\n\n### Matching features\n\n* Support for multiple glob patterns (no need for wrappers like multimatch)\n* Wildcards (`**`, `*.js`)\n* Negation (`'!a/*.js'`, `'*!(b).js'`)\n* [extglobs](#extglobs) (`+(x|y)`, `!(a|b)`)\n* [POSIX character classes](#posix-bracket-expressions) (`[[:alpha:][:digit:]]`)\n* [brace expansion](https://github.com/micromatch/braces) (`foo/{1..5}.md`, `bar/{a,b,c}.js`)\n* regex character classes (`foo-[1-5].js`)\n* regex logical \"or\" (`foo/(abc|xyz).js`)\n\nYou can mix and match these features to create whatever patterns you need!\n\n## Switching to micromatch\n\n_(There is one notable difference between micromatch and minimatch in regards to how backslashes are handled. See [the notes about backslashes](#backslashes) for more information.)_\n\n### From minimatch\n\nUse [micromatch.isMatch()](#ismatch) instead of `minimatch()`:\n\n```js\nconsole.log(micromatch.isMatch('foo', 'b*')); //=> false\n```\n\nUse [micromatch.match()](#match) instead of `minimatch.match()`:\n\n```js\nconsole.log(micromatch.match(['foo', 'bar'], 'b*')); //=> 'bar'\n```\n\n### From multimatch\n\nSame signature:\n\n```js\nconsole.log(micromatch(['foo', 'bar', 'baz'], ['f*', '*z'])); //=> ['foo', 'baz']\n```\n\n## API\n\n**Params**\n\n* `list` **{String|Array<string>}**: List of strings to match.\n* `patterns` **{String|Array<string>}**: One or more glob patterns to use for matching.\n* `options` **{Object}**: See available [options](#options)\n* `returns` **{Array}**: Returns an array of matches\n\n**Example**\n\n```js\nconst mm = require('micromatch');\n// mm(list, patterns[, options]);\n\nconsole.log(mm(['a.js', 'a.txt'], ['*.js']));\n//=> [ 'a.js' ]\n```\n\n### [.matcher](index.js#L109)\n\nReturns a matcher function from the given glob `pattern` and `options`. The returned function takes a string to match as its only argument and returns true if the string is a match.\n\n**Params**\n\n* `pattern` **{String}**: Glob pattern\n* `options` **{Object}**\n* `returns` **{Function}**: Returns a matcher function.\n\n**Example**\n\n```js\nconst mm = require('micromatch');\n// mm.matcher(pattern[, options]);\n\nconst isMatch = mm.matcher('*.!(*a)');\nconsole.log(isMatch('a.a')); //=> false\nconsole.log(isMatch('a.b')); //=> true\n```\n\n### [.isMatch](index.js#L128)\n\nReturns true if **any** of the given glob `patterns` match the specified `string`.\n\n**Params**\n\n* `str` **{String}**: The string to test.\n* `patterns` **{String|Array}**: One or more glob patterns to use for matching.\n* `[options]` **{Object}**: See available [options](#options).\n* `returns` **{Boolean}**: Returns true if any patterns match `str`\n\n**Example**\n\n```js\nconst mm = require('micromatch');\n// mm.isMatch(string, patterns[, options]);\n\nconsole.log(mm.isMatch('a.a', ['b.*', '*.a'])); //=> true\nconsole.log(mm.isMatch('a.a', 'b.*')); //=> false\n```\n\n### [.not](index.js#L153)\n\nReturns a list of strings that _**do not match any**_ of the given `patterns`.\n\n**Params**\n\n* `list` **{Array}**: Array of strings to match.\n* `patterns` **{String|Array}**: One or more glob pattern to use for matching.\n* `options` **{Object}**: See available [options](#options) for changing how matches are performed\n* `returns` **{Array}**: Returns an array of strings that **do not match** the given patterns.\n\n**Example**\n\n```js\nconst mm = require('micromatch');\n// mm.not(list, patterns[, options]);\n\nconsole.log(mm.not(['a.a', 'b.b', 'c.c'], '*.a'));\n//=> ['b.b', 'c.c']\n```\n\n### [.contains](index.js#L193)\n\nReturns true if the given `string` contains the given pattern. Similar to [.isMatch](#isMatch) but the pattern can match any part of the string.\n\n**Params**\n\n* `str` **{String}**: The string to match.\n* `patterns` **{String|Array}**: Glob pattern to use for matching.\n* `options` **{Object}**: See available [options](#options) for changing how matches are performed\n* `returns` **{Boolean}**: Returns true if any of the patterns matches any part of `str`.\n\n**Example**\n\n```js\nvar mm = require('micromatch');\n// mm.contains(string, pattern[, options]);\n\nconsole.log(mm.contains('aa/bb/cc', '*b'));\n//=> true\nconsole.log(mm.contains('aa/bb/cc', '*d'));\n//=> false\n```\n\n### [.matchKeys](index.js#L235)\n\nFilter the keys of the given object with the given `glob` pattern and `options`. Does not attempt to match nested keys. If you need this feature, use [glob-object](https://github.com/jonschlinkert/glob-object) instead.\n\n**Params**\n\n* `object` **{Object}**: The object with keys to filter.\n* `patterns` **{String|Array}**: One or more glob patterns to use for matching.\n* `options` **{Object}**: See available [options](#options) for changing how matches are performed\n* `returns` **{Object}**: Returns an object with only keys that match the given patterns.\n\n**Example**\n\n```js\nconst mm = require('micromatch');\n// mm.matchKeys(object, patterns[, options]);\n\nconst obj = { aa: 'a', ab: 'b', ac: 'c' };\nconsole.log(mm.matchKeys(obj, '*b'));\n//=> { ab: 'b' }\n```\n\n### [.some](index.js#L264)\n\nReturns true if some of the strings in the given `list` match any of the given glob `patterns`.\n\n**Params**\n\n* `list` **{String|Array}**: The string or array of strings to test. Returns as soon as the first match is found.\n* `patterns` **{String|Array}**: One or more glob patterns to use for matching.\n* `options` **{Object}**: See available [options](#options) for changing how matches are performed\n* `returns` **{Boolean}**: Returns true if any `patterns` matches any of the strings in `list`\n\n**Example**\n\n```js\nconst mm = require('micromatch');\n// mm.some(list, patterns[, options]);\n\nconsole.log(mm.some(['foo.js', 'bar.js'], ['*.js', '!foo.js']));\n// true\nconsole.log(mm.some(['foo.js'], ['*.js', '!foo.js']));\n// false\n```\n\n### [.every](index.js#L300)\n\nReturns true if every string in the given `list` matches any of the given glob `patterns`.\n\n**Params**\n\n* `list` **{String|Array}**: The string or array of strings to test.\n* `patterns` **{String|Array}**: One or more glob patterns to use for matching.\n* `options` **{Object}**: See available [options](#options) for changing how matches are performed\n* `returns` **{Boolean}**: Returns true if all `patterns` matches all of the strings in `list`\n\n**Example**\n\n```js\nconst mm = require('micromatch');\n// mm.every(list, patterns[, options]);\n\nconsole.log(mm.every('foo.js', ['foo.js']));\n// true\nconsole.log(mm.every(['foo.js', 'bar.js'], ['*.js']));\n// true\nconsole.log(mm.every(['foo.js', 'bar.js'], ['*.js', '!foo.js']));\n// false\nconsole.log(mm.every(['foo.js'], ['*.js', '!foo.js']));\n// false\n```\n\n### [.all](index.js#L339)\n\nReturns true if **all** of the given `patterns` match the specified string.\n\n**Params**\n\n* `str` **{String|Array}**: The string to test.\n* `patterns` **{String|Array}**: One or more glob patterns to use for matching.\n* `options` **{Object}**: See available [options](#options) for changing how matches are performed\n* `returns` **{Boolean}**: Returns true if any patterns match `str`\n\n**Example**\n\n```js\nconst mm = require('micromatch');\n// mm.all(string, patterns[, options]);\n\nconsole.log(mm.all('foo.js', ['foo.js']));\n// true\n\nconsole.log(mm.all('foo.js', ['*.js', '!foo.js']));\n// false\n\nconsole.log(mm.all('foo.js', ['*.js', 'foo.js']));\n// true\n\nconsole.log(mm.all('foo.js', ['*.js', 'f*', '*o*', '*o.js']));\n// true\n```\n\n### [.capture](index.js#L366)\n\nReturns an array of matches captured by `pattern` in `string, or`null` if the pattern did not match.\n\n**Params**\n\n* `glob` **{String}**: Glob pattern to use for matching.\n* `input` **{String}**: String to match\n* `options` **{Object}**: See available [options](#options) for changing how matches are performed\n* `returns` **{Array|null}**: Returns an array of captures if the input matches the glob pattern, otherwise `null`.\n\n**Example**\n\n```js\nconst mm = require('micromatch');\n// mm.capture(pattern, string[, options]);\n\nconsole.log(mm.capture('test/*.js', 'test/foo.js'));\n//=> ['foo']\nconsole.log(mm.capture('test/*.js', 'foo/bar.css'));\n//=> null\n```\n\n### [.makeRe](index.js#L392)\n\nCreate a regular expression from the given glob `pattern`.\n\n**Params**\n\n* `pattern` **{String}**: A glob pattern to convert to regex.\n* `options` **{Object}**\n* `returns` **{RegExp}**: Returns a regex created from the given pattern.\n\n**Example**\n\n```js\nconst mm = require('micromatch');\n// mm.makeRe(pattern[, options]);\n\nconsole.log(mm.makeRe('*.js'));\n//=> /^(?:(\\.[\\\\\\/])?(?!\\.)(?=.)[^\\/]*?\\.js)$/\n```\n\n### [.scan](index.js#L408)\n\nScan a glob pattern to separate the pattern into segments. Used by the [split](#split) method.\n\n**Params**\n\n* `pattern` **{String}**\n* `options` **{Object}**\n* `returns` **{Object}**: Returns an object with\n\n**Example**\n\n```js\nconst mm = require('micromatch');\nconst state = mm.scan(pattern[, options]);\n```\n\n### [.parse](index.js#L424)\n\nParse a glob pattern to create the source string for a regular expression.\n\n**Params**\n\n* `glob` **{String}**\n* `options` **{Object}**\n* `returns` **{Object}**: Returns an object with useful properties and output to be used as regex source string.\n\n**Example**\n\n```js\nconst mm = require('micromatch');\nconst state = mm.parse(pattern[, options]);\n```\n\n### [.braces](index.js#L451)\n\nProcess the given brace `pattern`.\n\n**Params**\n\n* `pattern` **{String}**: String with brace pattern to process.\n* `options` **{Object}**: Any [options](#options) to change how expansion is performed. See the [braces](https://github.com/micromatch/braces) library for all available options.\n* `returns` **{Array}**\n\n**Example**\n\n```js\nconst { braces } = require('micromatch');\nconsole.log(braces('foo/{a,b,c}/bar'));\n//=> [ 'foo/(a|b|c)/bar' ]\n\nconsole.log(braces('foo/{a,b,c}/bar', { expand: true }));\n//=> [ 'foo/a/bar', 'foo/b/bar', 'foo/c/bar' ]\n```\n\n## Options\n\n| **Option** | **Type** | **Default value** | **Description** |\n| --- | --- | --- | --- |\n| `basename`            | `boolean`      | `false`     | If set, then patterns without slashes will be matched against the basename of the path if it contains slashes.  For example, `a?b` would match the path `/xyz/123/acb`, but not `/xyz/acb/123`. |\n| `bash`                | `boolean`      | `false`     | Follow bash matching rules more strictly - disallows backslashes as escape characters, and treats single stars as globstars (`**`). |\n| `capture`             | `boolean`      | `undefined` | Return regex matches in supporting methods. |\n| `contains`            | `boolean`      | `undefined` | Allows glob to match any part of the given string(s). |\n| `cwd`                 | `string`       | `process.cwd()` | Current working directory. Used by `picomatch.split()` |\n| `debug`               | `boolean`      | `undefined` | Debug regular expressions when an error is thrown. |\n| `dot`                 | `boolean`      | `false`     | Match dotfiles. Otherwise dotfiles are ignored unless a `.` is explicitly defined in the pattern. |\n| `expandRange`         | `function`     | `undefined` | Custom function for expanding ranges in brace patterns, such as `{a..z}`. The function receives the range values as two arguments, and it must return a string to be used in the generated regex. It's recommended that returned strings be wrapped in parentheses. This option is overridden by the `expandBrace` option. |\n| `failglob`            | `boolean`      | `false`     | Similar to the `failglob` behavior in Bash, throws an error when no matches are found. Based on the bash option of the same name. |\n| `fastpaths`           | `boolean`      | `true`      | To speed up processing, full parsing is skipped for a handful common glob patterns. Disable this behavior by setting this option to `false`. |\n| `flags`               | `boolean`      | `undefined` | Regex flags to use in the generated regex. If defined, the `nocase` option will be overridden. |\n| [format](#optionsformat) | `function` | `undefined` | Custom function for formatting the returned string. This is useful for removing leading slashes, converting Windows paths to Posix paths, etc. |\n| `ignore`              | `array\\|string` | `undefined` | One or more glob patterns for excluding strings that should not be matched from the result. |\n| `keepQuotes`          | `boolean`      | `false`     | Retain quotes in the generated regex, since quotes may also be used as an alternative to backslashes.  |\n| `literalBrackets`     | `boolean`      | `undefined` | When `true`, brackets in the glob pattern will be escaped so that only literal brackets will be matched. |\n| `lookbehinds`         | `boolean`      | `true`      | Support regex positive and negative lookbehinds. Note that you must be using Node 8.1.10 or higher to enable regex lookbehinds. |\n| `matchBase`           | `boolean`      | `false`     | Alias for `basename` |\n| `maxLength`           | `boolean`      | `65536`     | Limit the max length of the input string. An error is thrown if the input string is longer than this value. |\n| `nobrace`             | `boolean`      | `false`     | Disable brace matching, so that `{a,b}` and `{1..3}` would be treated as literal characters. |\n| `nobracket`           | `boolean`      | `undefined` | Disable matching with regex brackets. |\n| `nocase`              | `boolean`      | `false`     | Perform case-insensitive matching. Equivalent to the regex `i` flag. Note that this option is ignored when the `flags` option is defined. |\n| `nodupes`             | `boolean`      | `true`      | Deprecated, use `nounique` instead. This option will be removed in a future major release. By default duplicates are removed. Disable uniquification by setting this option to false. |\n| `noext`               | `boolean`      | `false`     | Alias for `noextglob` |\n| `noextglob`           | `boolean`      | `false`     | Disable support for matching with [extglobs](#extglobs) (like `+(a\\|b)`) |\n| `noglobstar`          | `boolean`      | `false`     | Disable support for matching nested directories with globstars (`**`) |\n| `nonegate`            | `boolean`      | `false`     | Disable support for negating with leading `!` |\n| `noquantifiers`       | `boolean`      | `false`     | Disable support for regex quantifiers (like `a{1,2}`) and treat them as brace patterns to be expanded. |\n| [onIgnore](#optionsonIgnore) | `function` | `undefined` | Function to be called on ignored items. |\n| [onMatch](#optionsonMatch) | `function` | `undefined` | Function to be called on matched items. |\n| [onResult](#optionsonResult) | `function` | `undefined` | Function to be called on all items, regardless of whether or not they are matched or ignored. |\n| `posix`               | `boolean`      | `false`     | Support [POSIX character classes](#posix-bracket-expressions) (\"posix brackets\"). |\n| `posixSlashes`        | `boolean`      | `undefined` | Convert all slashes in file paths to forward slashes. This does not convert slashes in the glob pattern itself |\n| `prepend`             | `string`       | `undefined` | String to prepend to the generated regex used for matching. |\n| `regex`               | `boolean`      | `false`     | Use regular expression rules for `+` (instead of matching literal `+`), and for stars that follow closing parentheses or brackets (as in `)*` and `]*`). |\n| `strictBrackets`      | `boolean`      | `undefined` | Throw an error if brackets, braces, or parens are imbalanced. |\n| `strictSlashes`       | `boolean`      | `undefined` | When true, picomatch won't match trailing slashes with single stars. |\n| `unescape`            | `boolean`      | `undefined` | Remove preceding backslashes from escaped glob characters before creating the regular expression to perform matches. |\n| `unixify`             | `boolean`      | `undefined` | Alias for `posixSlashes`, for backwards compatitibility. |\n\n## Options Examples\n\n### options.basename\n\nAllow glob patterns without slashes to match a file path based on its basename. Same behavior as [minimatch](https://github.com/isaacs/minimatch) option `matchBase`.\n\n**Type**: `Boolean`\n\n**Default**: `false`\n\n**Example**\n\n```js\nmicromatch(['a/b.js', 'a/c.md'], '*.js');\n//=> []\n\nmicromatch(['a/b.js', 'a/c.md'], '*.js', { basename: true });\n//=> ['a/b.js']\n```\n\n### options.bash\n\nEnabled by default, this option enforces bash-like behavior with stars immediately following a bracket expression. Bash bracket expressions are similar to regex character classes, but unlike regex, a star following a bracket expression **does not repeat the bracketed characters**. Instead, the star is treated the same as any other star.\n\n**Type**: `Boolean`\n\n**Default**: `true`\n\n**Example**\n\n```js\nconst files = ['abc', 'ajz'];\nconsole.log(micromatch(files, '[a-c]*'));\n//=> ['abc', 'ajz']\n\nconsole.log(micromatch(files, '[a-c]*', { bash: false }));\n```\n\n### options.expandRange\n\n**Type**: `function`\n\n**Default**: `undefined`\n\nCustom function for expanding ranges in brace patterns. The [fill-range](https://github.com/jonschlinkert/fill-range) library is ideal for this purpose, or you can use custom code to do whatever you need.\n\n**Example**\n\nThe following example shows how to create a glob that matches a numeric folder name between `01` and `25`, with leading zeros.\n\n```js\nconst fill = require('fill-range');\nconst regex = micromatch.makeRe('foo/{01..25}/bar', {\n  expandRange(a, b) {\n    return `(${fill(a, b, { toRegex: true })})`;\n  }\n});\n\nconsole.log(regex)\n//=> /^(?:foo\\/((?:0[1-9]|1[0-9]|2[0-5]))\\/bar)$/\n\nconsole.log(regex.test('foo/00/bar')) // false\nconsole.log(regex.test('foo/01/bar')) // true\nconsole.log(regex.test('foo/10/bar')) // true\nconsole.log(regex.test('foo/22/bar')) // true\nconsole.log(regex.test('foo/25/bar')) // true\nconsole.log(regex.test('foo/26/bar')) // false\n```\n\n### options.format\n\n**Type**: `function`\n\n**Default**: `undefined`\n\nCustom function for formatting strings before they're matched.\n\n**Example**\n\n```js\n// strip leading './' from strings\nconst format = str => str.replace(/^\\.\\//, '');\nconst isMatch = picomatch('foo/*.js', { format });\nconsole.log(isMatch('./foo/bar.js')) //=> true\n```\n\n### options.ignore\n\nString or array of glob patterns to match files to ignore.\n\n**Type**: `String|Array`\n\n**Default**: `undefined`\n\n```js\nconst isMatch = micromatch.matcher('*', { ignore: 'f*' });\nconsole.log(isMatch('foo')) //=> false\nconsole.log(isMatch('bar')) //=> true\nconsole.log(isMatch('baz')) //=> true\n```\n\n### options.matchBase\n\nAlias for [options.basename](#options-basename).\n\n### options.noextglob\n\nDisable extglob support, so that [extglobs](#extglobs) are regarded as literal characters.\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n**Examples**\n\n```js\nconsole.log(micromatch(['a/z', 'a/b', 'a/!(z)'], 'a/!(z)'));\n//=> ['a/b', 'a/!(z)']\n\nconsole.log(micromatch(['a/z', 'a/b', 'a/!(z)'], 'a/!(z)', { noextglob: true }));\n//=> ['a/!(z)'] (matches only as literal characters)\n```\n\n### options.nonegate\n\nDisallow negation (`!`) patterns, and treat leading `!` as a literal character to match.\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n### options.noglobstar\n\nDisable matching with globstars (`**`).\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n```js\nmicromatch(['a/b', 'a/b/c', 'a/b/c/d'], 'a/**');\n//=> ['a/b', 'a/b/c', 'a/b/c/d']\n\nmicromatch(['a/b', 'a/b/c', 'a/b/c/d'], 'a/**', {noglobstar: true});\n//=> ['a/b']\n```\n\n### options.nonull\n\nAlias for [options.nullglob](#options-nullglob).\n\n### options.nullglob\n\nIf `true`, when no matches are found the actual (arrayified) glob pattern is returned instead of an empty array. Same behavior as [minimatch](https://github.com/isaacs/minimatch) option `nonull`.\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n### options.onIgnore\n\n```js\nconst onIgnore = ({ glob, regex, input, output }) => {\n  console.log({ glob, regex, input, output });\n  // { glob: '*', regex: /^(?:(?!\\.)(?=.)[^\\/]*?\\/?)$/, input: 'foo', output: 'foo' }\n};\n\nconst isMatch = micromatch.matcher('*', { onIgnore, ignore: 'f*' });\nisMatch('foo');\nisMatch('bar');\nisMatch('baz');\n```\n\n### options.onMatch\n\n```js\nconst onMatch = ({ glob, regex, input, output }) => {\n  console.log({ input, output });\n  // { input: 'some\\\\path', output: 'some/path' }\n  // { input: 'some\\\\path', output: 'some/path' }\n  // { input: 'some\\\\path', output: 'some/path' }\n};\n\nconst isMatch = micromatch.matcher('**', { onMatch, posixSlashes: true });\nisMatch('some\\\\path');\nisMatch('some\\\\path');\nisMatch('some\\\\path');\n```\n\n### options.onResult\n\n```js\nconst onResult = ({ glob, regex, input, output }) => {\n  console.log({ glob, regex, input, output });\n};\n\nconst isMatch = micromatch('*', { onResult, ignore: 'f*' });\nisMatch('foo');\nisMatch('bar');\nisMatch('baz');\n```\n\n### options.posixSlashes\n\nConvert path separators on returned files to posix/unix-style forward slashes. Aliased as `unixify` for backwards compatibility.\n\n**Type**: `Boolean`\n\n**Default**: `true` on windows, `false` everywhere else.\n\n**Example**\n\n```js\nconsole.log(micromatch.match(['a\\\\b\\\\c'], 'a/**'));\n//=> ['a/b/c']\n\nconsole.log(micromatch.match(['a\\\\b\\\\c'], { posixSlashes: false }));\n//=> ['a\\\\b\\\\c']\n```\n\n### options.unescape\n\nRemove backslashes from escaped glob characters before creating the regular expression to perform matches.\n\n**Type**: `Boolean`\n\n**Default**: `undefined`\n\n**Example**\n\nIn this example we want to match a literal `*`:\n\n```js\nconsole.log(micromatch.match(['abc', 'a\\\\*c'], 'a\\\\*c'));\n//=> ['a\\\\*c']\n\nconsole.log(micromatch.match(['abc', 'a\\\\*c'], 'a\\\\*c', { unescape: true }));\n//=> ['a*c']\n```\n\n<br>\n<br>\n\n## Extended globbing\n\nMicromatch supports the following extended globbing features.\n\n### Extglobs\n\nExtended globbing, as described by the bash man page:\n\n| **pattern** | **regex equivalent** | **description** |\n| --- | --- | --- |\n| `?(pattern)` | `(pattern)?` | Matches zero or one occurrence of the given patterns |\n| `*(pattern)` | `(pattern)*` | Matches zero or more occurrences of the given patterns |\n| `+(pattern)` | `(pattern)+` | Matches one or more occurrences of the given patterns |\n| `@(pattern)` | `(pattern)` <sup>*</sup> | Matches one of the given patterns |\n| `!(pattern)` | N/A (equivalent regex is much more complicated) | Matches anything except one of the given patterns |\n\n<sup><strong>*</strong></sup> Note that `@` isn't a regex character.\n\n### Braces\n\nBrace patterns can be used to match specific ranges or sets of characters.\n\n**Example**\n\nThe pattern `{f,b}*/{1..3}/{b,q}*` would match any of following strings:\n\n```\nfoo/1/bar\nfoo/2/bar\nfoo/3/bar\nbaz/1/qux\nbaz/2/qux\nbaz/3/qux\n```\n\nVisit [braces](https://github.com/micromatch/braces) to see the full range of features and options related to brace expansion, or to create brace matching or expansion related issues.\n\n### Regex character classes\n\nGiven the list: `['a.js', 'b.js', 'c.js', 'd.js', 'E.js']`:\n\n* `[ac].js`: matches both `a` and `c`, returning `['a.js', 'c.js']`\n* `[b-d].js`: matches from `b` to `d`, returning `['b.js', 'c.js', 'd.js']`\n* `a/[A-Z].js`: matches and uppercase letter, returning `['a/E.md']`\n\nLearn about [regex character classes](http://www.regular-expressions.info/charclass.html).\n\n### Regex groups\n\nGiven `['a.js', 'b.js', 'c.js', 'd.js', 'E.js']`:\n\n* `(a|c).js`: would match either `a` or `c`, returning `['a.js', 'c.js']`\n* `(b|d).js`: would match either `b` or `d`, returning `['b.js', 'd.js']`\n* `(b|[A-Z]).js`: would match either `b` or an uppercase letter, returning `['b.js', 'E.js']`\n\nAs with regex, parens can be nested, so patterns like `((a|b)|c)/b` will work. Although brace expansion might be friendlier to use, depending on preference.\n\n### POSIX bracket expressions\n\nPOSIX brackets are intended to be more user-friendly than regex character classes. This of course is in the eye of the beholder.\n\n**Example**\n\n```js\nconsole.log(micromatch.isMatch('a1', '[[:alpha:][:digit:]]')) //=> true\nconsole.log(micromatch.isMatch('a1', '[[:alpha:][:alpha:]]')) //=> false\n```\n\n***\n\n## Notes\n\n### Bash 4.3 parity\n\nWhenever possible matching behavior is based on behavior Bash 4.3, which is mostly consistent with minimatch.\n\nHowever, it's suprising how many edge cases and rabbit holes there are with glob matching, and since there is no real glob specification, and micromatch is more accurate than both Bash and minimatch, there are cases where best-guesses were made for behavior. In a few cases where Bash had no answers, we used wildmatch (used by git) as a fallback.\n\n### Backslashes\n\nThere is an important, notable difference between minimatch and micromatch _in regards to how backslashes are handled_ in glob patterns.\n\n* Micromatch exclusively and explicitly reserves backslashes for escaping characters in a glob pattern, even on windows, which is consistent with bash behavior. _More importantly, unescaping globs can result in unsafe regular expressions_.\n* Minimatch converts all backslashes to forward slashes, which means you can't use backslashes to escape any characters in your glob patterns.\n\nWe made this decision for micromatch for a couple of reasons:\n\n* Consistency with bash conventions.\n* Glob patterns are not filepaths. They are a type of [regular language](https://en.wikipedia.org/wiki/Regular_language) that is converted to a JavaScript regular expression. Thus, when forward slashes are defined in a glob pattern, the resulting regular expression will match windows or POSIX path separators just fine.\n\n**A note about joining paths to globs**\n\nNote that when you pass something like `path.join('foo', '*')` to micromatch, you are creating a filepath and expecting it to still work as a glob pattern. This causes problems on windows, since the `path.sep` is `\\\\`.\n\nIn other words, since `\\\\` is reserved as an escape character in globs, on windows `path.join('foo', '*')` would result in `foo\\\\*`, which tells micromatch to match `*` as a literal character. This is the same behavior as bash.\n\nTo solve this, you might be inspired to do something like `'foo\\\\*'.replace(/\\\\/g, '/')`, but this causes another, potentially much more serious, problem.\n\n## Benchmarks\n\n### Running benchmarks\n\nInstall dependencies for running benchmarks:\n\n```sh\n$ cd bench && npm install\n```\n\nRun the benchmarks:\n\n```sh\n$ npm run bench\n```\n\n### Latest results\n\nAs of August 23, 2024 (longer bars are better):\n\n```sh\n# .makeRe star\n  micromatch x 2,232,802 ops/sec ±2.34% (89 runs sampled))\n  minimatch x 781,018 ops/sec ±6.74% (92 runs sampled))\n\n# .makeRe star; dot=true\n  micromatch x 1,863,453 ops/sec ±0.74% (93 runs sampled)\n  minimatch x 723,105 ops/sec ±0.75% (93 runs sampled)\n\n# .makeRe globstar\n  micromatch x 1,624,179 ops/sec ±2.22% (91 runs sampled)\n  minimatch x 1,117,230 ops/sec ±2.78% (86 runs sampled))\n\n# .makeRe globstars\n  micromatch x 1,658,642 ops/sec ±0.86% (92 runs sampled)\n  minimatch x 741,224 ops/sec ±1.24% (89 runs sampled))\n\n# .makeRe with leading star\n  micromatch x 1,525,014 ops/sec ±1.63% (90 runs sampled)\n  minimatch x 561,074 ops/sec ±3.07% (89 runs sampled)\n\n# .makeRe - braces\n  micromatch x 172,478 ops/sec ±2.37% (78 runs sampled)\n  minimatch x 96,087 ops/sec ±2.34% (88 runs sampled)))\n\n# .makeRe braces - range (expanded)\n  micromatch x 26,973 ops/sec ±0.84% (89 runs sampled)\n  minimatch x 3,023 ops/sec ±0.99% (90 runs sampled))\n\n# .makeRe braces - range (compiled)\n  micromatch x 152,892 ops/sec ±1.67% (83 runs sampled)\n  minimatch x 992 ops/sec ±3.50% (89 runs sampled)d))\n\n# .makeRe braces - nested ranges (expanded)\n  micromatch x 15,816 ops/sec ±13.05% (80 runs sampled)\n  minimatch x 2,953 ops/sec ±1.64% (91 runs sampled)\n\n# .makeRe braces - nested ranges (compiled)\n  micromatch x 110,881 ops/sec ±1.85% (82 runs sampled)\n  minimatch x 1,008 ops/sec ±1.51% (91 runs sampled)\n\n# .makeRe braces - set (compiled)\n  micromatch x 134,930 ops/sec ±3.54% (63 runs sampled))\n  minimatch x 43,242 ops/sec ±0.60% (93 runs sampled)\n\n# .makeRe braces - nested sets (compiled)\n  micromatch x 94,455 ops/sec ±1.74% (69 runs sampled))\n  minimatch x 27,720 ops/sec ±1.84% (93 runs sampled))\n```\n\n## Contributing\n\nAll contributions are welcome! Please read [the contributing guide](.github/contributing.md) to get started.\n\n**Bug reports**\n\nPlease create an issue if you encounter a bug or matching behavior that doesn't seem correct. If you find a matching-related issue, please:\n\n* [research existing issues first](../../issues) (open and closed)\n* visit the [GNU Bash documentation](https://www.gnu.org/software/bash/manual/) to see how Bash deals with the pattern\n* visit the [minimatch](https://github.com/isaacs/minimatch) documentation to cross-check expected behavior in node.js\n* if all else fails, since there is no real specification for globs we will probably need to discuss expected behavior and decide how to resolve it. which means any detail you can provide to help with this discussion would be greatly appreciated.\n\n**Platform issues**\n\nIt's important to us that micromatch work consistently on all platforms. If you encounter any platform-specific matching or path related issues, please let us know (pull requests are also greatly appreciated).\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\nPlease read the [contributing guide](.github/contributing.md) for advice on opening issues, pull requests, and coding standards.\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nYou might also be interested in these projects:\n\n* [braces](https://www.npmjs.com/package/braces): Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support… [more](https://github.com/micromatch/braces) | [homepage](https://github.com/micromatch/braces \"Bash-like brace expansion, implemented in JavaScript. Safer than other brace expansion libs, with complete support for the Bash 4.3 braces specification, without sacrificing speed.\")\n* [expand-brackets](https://www.npmjs.com/package/expand-brackets): Expand POSIX bracket expressions (character classes) in glob patterns. | [homepage](https://github.com/micromatch/expand-brackets \"Expand POSIX bracket expressions (character classes) in glob patterns.\")\n* [extglob](https://www.npmjs.com/package/extglob): Extended glob support for JavaScript. Adds (almost) the expressive power of regular expressions to glob… [more](https://github.com/micromatch/extglob) | [homepage](https://github.com/micromatch/extglob \"Extended glob support for JavaScript. Adds (almost) the expressive power of regular expressions to glob patterns.\")\n* [fill-range](https://www.npmjs.com/package/fill-range): Fill in a range of numbers or letters, optionally passing an increment or `step` to… [more](https://github.com/jonschlinkert/fill-range) | [homepage](https://github.com/jonschlinkert/fill-range \"Fill in a range of numbers or letters, optionally passing an increment or `step` to use, or create a regex-compatible range with `options.toRegex`\")\n* [nanomatch](https://www.npmjs.com/package/nanomatch): Fast, minimal glob matcher for node.js. Similar to micromatch, minimatch and multimatch, but complete Bash… [more](https://github.com/micromatch/nanomatch) | [homepage](https://github.com/micromatch/nanomatch \"Fast, minimal glob matcher for node.js. Similar to micromatch, minimatch and multimatch, but complete Bash 4.3 wildcard support only (no support for exglobs, posix brackets or braces)\")\n\n### Contributors\n\n| **Commits** | **Contributor** |  \n| --- | --- |  \n| 523 | [jonschlinkert](https://github.com/jonschlinkert) |  \n| 12  | [es128](https://github.com/es128) |  \n| 9   | [danez](https://github.com/danez) |  \n| 8   | [doowb](https://github.com/doowb) |  \n| 6   | [paulmillr](https://github.com/paulmillr) |  \n| 5   | [mrmlnc](https://github.com/mrmlnc) |  \n| 3   | [DrPizza](https://github.com/DrPizza) |  \n| 2   | [Tvrqvoise](https://github.com/Tvrqvoise) |  \n| 2   | [antonyk](https://github.com/antonyk) |  \n| 2   | [MartinKolarik](https://github.com/MartinKolarik) |  \n| 2   | [Glazy](https://github.com/Glazy) |  \n| 2   | [mceIdo](https://github.com/mceIdo) |  \n| 2   | [TrySound](https://github.com/TrySound) |  \n| 1   | [yvele](https://github.com/yvele) |  \n| 1   | [wtgtybhertgeghgtwtg](https://github.com/wtgtybhertgeghgtwtg) |  \n| 1   | [simlu](https://github.com/simlu) |  \n| 1   | [curbengh](https://github.com/curbengh) |  \n| 1   | [fidian](https://github.com/fidian) |  \n| 1   | [tomByrer](https://github.com/tomByrer) |  \n| 1   | [ZoomerTedJackson](https://github.com/ZoomerTedJackson) |  \n| 1   | [styfle](https://github.com/styfle) |  \n| 1   | [sebdeckers](https://github.com/sebdeckers) |  \n| 1   | [muescha](https://github.com/muescha) |  \n| 1   | [juszczykjakub](https://github.com/juszczykjakub) |  \n| 1   | [joyceerhl](https://github.com/joyceerhl) |  \n| 1   | [donatj](https://github.com/donatj) |  \n| 1   | [frangio](https://github.com/frangio) |  \n| 1   | [UltCombo](https://github.com/UltCombo) |  \n| 1   | [DianeLooney](https://github.com/DianeLooney) |  \n| 1   | [devongovett](https://github.com/devongovett) |  \n| 1   | [Cslove](https://github.com/Cslove) |  \n| 1   | [amilajack](https://github.com/amilajack) |  \n\n### Author\n\n**Jon Schlinkert**\n\n* [GitHub Profile](https://github.com/jonschlinkert)\n* [Twitter Profile](https://twitter.com/jonschlinkert)\n* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)\n\n### License\n\nCopyright © 2024, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.8.0, on August 23, 2024._", "readmeFilename": "README.md", "users": {"cedx": true, "l3au": true, "iuykza": true, "monjer": true, "tur-nr": true, "albinekb": true, "bapinney": true, "yitzchak": true, "rbecheras": true, "steel1990": true, "sternelee": true, "abdihaikal": true, "insomniaqc": true, "lichangwei": true, "modestfake": true, "oleg_tsyba": true, "flumpus-dev": true, "monsterkodi": true, "soenkekluth": true, "tunnckocore": true, "danielrhayes": true, "elliottcable": true, "hugojosefson": true, "martinspinks": true, "jonschlinkert": true, "shanewholloway": true}}