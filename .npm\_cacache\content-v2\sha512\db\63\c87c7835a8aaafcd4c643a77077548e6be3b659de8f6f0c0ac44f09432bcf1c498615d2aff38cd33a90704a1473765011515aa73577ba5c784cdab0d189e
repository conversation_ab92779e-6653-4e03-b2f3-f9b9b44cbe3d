{"_id": "walker", "_rev": "39-c3c754318a6b9aaa3c023f2df12bf79c", "name": "walker", "description": "A simple directory tree walker.", "dist-tags": {"latest": "1.0.8"}, "versions": {"0.0.1": {"name": "walker", "description": "A simple directory tree walker.", "version": "0.0.1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["utils", "fs", "filesystem"], "directories": {"lib": "./lib"}, "engines": {"node": ">= 0.2.3"}, "_id": "walker@0.0.1", "_nodeSupported": true, "_npmVersion": "0.2.7-2", "_nodeVersion": "v0.3.1-pre", "dist": {"tarball": "https://registry.npmjs.org/walker/-/walker-0.0.1.tgz", "shasum": "121a2c96eddb794dc3347c2adae133732dc9a802", "integrity": "sha512-txveIChTdb6s5qw+s/0UdxCh7Re9BbfaxaAFPvnwh5Zg2l5XJZRBZpy2cbcNl2796VxRORLVLrLAq8naqtbnaQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDNgyu3zUlid9Fp26tNzaP8E0JA/2N26kQAGeUvtPScGwIgVIzQeQqkOJr+bO0rrFX52sk0IM31eSWuwAsnJKwTvTE="}]}}, "0.0.3": {"name": "walker", "description": "A simple directory tree walker.", "version": "0.0.3", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["utils", "fs", "filesystem"], "main": "index", "repository": {"type": "git", "url": "git://github.com/nshah/nodejs-walker.git"}, "engines": {"node": ">= 0.4.1"}, "_id": "walker@0.0.3", "_engineSupported": true, "_npmVersion": "0.3.9", "_nodeVersion": "v0.4.1", "directories": {}, "files": [""], "_defaultsLoaded": true, "dist": {"shasum": "ea65527ddb4c208a78a0a38af9e546661146b4ad", "tarball": "https://registry.npmjs.org/walker/-/walker-0.0.3.tgz", "integrity": "sha512-M6QmhM0yCD9xCWpA7t5HIaRr4XpRJKh2Ar9ZR4rhNaypu2SnWgLKv7dKklQLSBOXdpyhODOS+U4zUsuWOpgAgw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFVD5h2rbdYGu1dU2cEoqYFUcZTmRuVqs1tfskXNQktYAiBqvtWUuhYbXidYqd0aXXL+BTNNcCnT19fL2FFdLkzI2A=="}]}}, "0.0.4": {"name": "walker", "description": "A simple directory tree walker.", "version": "0.0.4", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["utils", "fs", "filesystem"], "main": "index", "repository": {"type": "git", "url": "git://github.com/nshah/nodejs-walker.git"}, "scripts": {"test": "./node_modules/.bin/expresso"}, "engines": {"node": ">= 0.4.1"}, "dependencies": {}, "devDependencies": {}, "_id": "walker@0.0.4", "_engineSupported": true, "_npmVersion": "1.0.1rc9", "_nodeVersion": "v0.4.6", "_defaultsLoaded": true, "dist": {"shasum": "05e0e7554d3c2b4780ba7b4dbab005dc04fd956f", "tarball": "https://registry.npmjs.org/walker/-/walker-0.0.4.tgz", "integrity": "sha512-/YodHJs71Dahjd8aAW0AxumbdjlopmRRATx0RJUhWuoPzuCzmRmkU15EzJl7u47K2LdKlaGLCualBPIPCyBWkA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDFkvIzUG6SeUoZDoqZ1GWBgnZZw9TN0rHemQrhXk2nXAIhAO5o4vZNAsORisH7dj80yKxvkNNxDHtcXugawv9ZXiHZ"}]}, "directories": {}}, "1.0.0": {"name": "walker", "description": "A simple directory tree walker.", "version": "1.0.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["utils", "fs", "filesystem"], "main": "index", "repository": {"type": "git", "url": "git://github.com/nshah/nodejs-walker.git"}, "scripts": {"test": "./node_modules/.bin/expresso"}, "dependencies": {"makeerror": ">= 1.0.0"}, "devDependencies": {"expresso": ">= 0.8.1"}, "engines": {"node": ">= 0.4.1"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/walker/1.0.0/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "walker@1.0.0", "_engineSupported": true, "_npmVersion": "1.0.18", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"shasum": "a88298c29997ccca7854bc3503a0fe5a82e4086d", "tarball": "https://registry.npmjs.org/walker/-/walker-1.0.0.tgz", "integrity": "sha512-Etz4HaSksHhEcMispJprR+gyQYtBUo3i20Sqee4VX79kCVN1FtEY6YTNhke4VKZP6j0LX8ZrDFsNvzVF4yJ1kA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEYMoGhFF4tkZZtGA3hvtWl6H1xcF0PxW1de9HJ7eOErAiEAicApeBCWZ0cqGhb3naPk+f+TsnpI6zFU9YPZqQwsdJU="}]}, "directories": {}}, "1.0.1": {"name": "walker", "description": "A simple directory tree walker.", "version": "1.0.1", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["utils", "fs", "filesystem"], "main": "index", "repository": {"type": "git", "url": "git://github.com/nshah/nodejs-walker.git"}, "scripts": {"test": "./node_modules/.bin/expresso"}, "dependencies": {"makeerror": ">= 1.0.0"}, "devDependencies": {"expresso": ">= 0.8.1"}, "engines": {"node": ">= 0.4.1"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/walker/1.0.1/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "walker@1.0.1", "_engineSupported": true, "_npmVersion": "1.0.18", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"shasum": "76af7f844ed64f712bd8fbb9c430cea0b095e08b", "tarball": "https://registry.npmjs.org/walker/-/walker-1.0.1.tgz", "integrity": "sha512-7DKrqsPLeRqgs3ao012hQ6wXMIYr1kTVa2YsU3ouAxu72ABSoIACknLjnPu1yjn530j12Gig9bmN4nATKennUA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDUCcfeNE8OaTKreYiAGSnLYFT+KB9rTY/NrhlpzBP44wIgeYyigjRTAEwlJ9Ed/M5T7krXrZ4TcZzAaClLubGc54U="}]}, "directories": {}}, "1.0.2": {"name": "walker", "description": "A simple directory tree walker.", "version": "1.0.2", "homepage": "https://github.com/nshah/nodejs-walker", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["utils", "fs", "filesystem"], "main": "lib/walker", "repository": {"type": "git", "url": "git://github.com/nshah/nodejs-walker.git"}, "scripts": {"test": "./node_modules/.bin/expresso -c"}, "dependencies": {"makeerror": ">= 1.0.0"}, "devDependencies": {"expresso": ">= 0.8.1"}, "engines": {"node": ">= 0.4.1"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/walker/1.0.2/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "walker@1.0.2", "_engineSupported": true, "_npmVersion": "1.0.18", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"shasum": "b623aa49b1fc88303f6e16dbea69e53b1550f19a", "tarball": "https://registry.npmjs.org/walker/-/walker-1.0.2.tgz", "integrity": "sha512-GHU0tPTl7skIAYGjBam3KOGA8TZM9+1ZUhbufs4Xb09HqK2+0DN6k2cx3L9oJhmfxj6B1n35q63NzwF6z9ci+Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCPLT1H0PvJi4rXKFWGO3j9SdmFuVeFOjOKPCIxSciBOQIhAKWhKjCtRnQOsa0b02YqZS4M/kfQ+qiEU9NGTf3ruNQx"}]}, "directories": {}}, "1.0.3": {"name": "walker", "description": "A simple directory tree walker.", "version": "1.0.3", "homepage": "https://github.com/nshah/nodejs-walker", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["utils", "fs", "filesystem"], "main": "lib/walker", "repository": {"type": "git", "url": "git://github.com/nshah/nodejs-walker.git"}, "scripts": {"test": "./node_modules/.bin/expresso -c"}, "dependencies": {"makeerror": ">= 1.0.0"}, "devDependencies": {"expresso": ">= 0.8.1"}, "engines": {"node": ">= 0.4.1"}, "_npmJsonOpts": {"file": "/Users/<USER>/.npm/walker/1.0.3/package/package.json", "wscript": false, "contributors": false, "serverjs": false}, "_id": "walker@1.0.3", "_engineSupported": true, "_npmVersion": "1.0.22", "_nodeVersion": "v0.4.10", "_defaultsLoaded": true, "dist": {"shasum": "5e6802d473533e769db74b54e8d5dbdf94a773a2", "tarball": "https://registry.npmjs.org/walker/-/walker-1.0.3.tgz", "integrity": "sha512-TQdPwiiSNTZr+YX+6Va/srtBiGL/O3jefFZsnk9if/lPD6++52dStE991erjtA+ahoE+G2yhTDo33EtLhGxVXQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDWtZ1/7DJ72Bh7jAbSgnP78q4dwO6T8DnM5TJHjpEwCAIhAOcNCLGh0tfoCefJ42NQa06E1J1YNBbn/EW+TInUbLsi"}]}, "maintainers": [{"name": "naitik", "email": "<EMAIL>"}], "directories": {}}, "1.0.5": {"name": "walker", "description": "A simple directory tree walker.", "version": "1.0.5", "homepage": "https://github.com/daaku/nodejs-walker", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["utils", "fs", "filesystem"], "main": "lib/walker", "repository": {"type": "git", "url": "https://github.com/daaku/nodejs-walker"}, "scripts": {"test": "NODE_PATH=./lib mocha --ui exports"}, "dependencies": {"makeerror": "1.0.x"}, "devDependencies": {"mocha": "0.12.x"}, "engines": {"node": "0.6.x"}, "bugs": {"url": "https://github.com/daaku/nodejs-walker/issues"}, "_id": "walker@1.0.5", "dist": {"shasum": "6aac711dcacafddf36fbc0b7098b7394856380bc", "tarball": "https://registry.npmjs.org/walker/-/walker-1.0.5.tgz", "integrity": "sha512-KxrGKk5tev5ytdR8EWhzmpGWyKLWBBMnxf0J1uYviAyyf1FLXwOa3Q3psHoiy3ej/rvICuR4Tzf48U+I+EYitA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCPJ5pqgyuuin8D8jgH90pwbnHT170cyim3CYQOkUGwaAIgMKlVDljVTcd0odE+Mgj77EPu7IHma01Q2pWd/AL9WRI="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "daaku", "email": "<EMAIL>"}, "maintainers": [{"name": "daaku", "email": "<EMAIL>"}], "directories": {}}, "1.0.6": {"name": "walker", "description": "A simple directory tree walker.", "version": "1.0.6", "homepage": "https://github.com/daaku/nodejs-walker", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["utils", "fs", "filesystem"], "main": "lib/walker", "repository": {"type": "git", "url": "https://github.com/daaku/nodejs-walker"}, "scripts": {"test": "NODE_PATH=./lib mocha --ui exports"}, "dependencies": {"makeerror": "1.0.x"}, "devDependencies": {"mocha": "0.12.x"}, "bugs": {"url": "https://github.com/daaku/nodejs-walker/issues"}, "_id": "walker@1.0.6", "dist": {"shasum": "4fe19098c06f1c16f19429acfc483429344da250", "tarball": "https://registry.npmjs.org/walker/-/walker-1.0.6.tgz", "integrity": "sha512-nmCOouONXnawZBThRwpDo84pkfVR/sU4x8n2tibBYLvaL5UghvYZJhEBlnuqV3TI7h1lMEdX2Xy4QMLe+X06HQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCObGK2mixyGWvZi/APa8o0He5DbUGcsls/ylTVmmA0KwIgLdjj2jdEMxtUh7S5XKwWg3/v21k7svC/NWhlTFmfZ4A="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "daaku", "email": "<EMAIL>"}, "maintainers": [{"name": "daaku", "email": "<EMAIL>"}], "directories": {}}, "1.0.7": {"name": "walker", "description": "A simple directory tree walker.", "version": "1.0.7", "homepage": "https://github.com/daaku/nodejs-walker", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["utils", "fs", "filesystem"], "main": "lib/walker", "repository": {"type": "git", "url": "https://github.com/daaku/nodejs-walker"}, "scripts": {"test": "NODE_PATH=./lib mocha --ui exports"}, "dependencies": {"makeerror": "1.0.x"}, "devDependencies": {"mocha": "0.12.x"}, "license": "Apache-2.0", "gitHead": "3f78f80d026fd1efd2adc2f8927a0a4ccae35644", "bugs": {"url": "https://github.com/daaku/nodejs-walker/issues"}, "_id": "walker@1.0.7", "_shasum": "2f7f9b8fd10d677262b18a884e28d19618e028fb", "_from": ".", "_npmVersion": "2.7.5", "_nodeVersion": "0.12.2", "_npmUser": {"name": "daaku", "email": "<EMAIL>"}, "maintainers": [{"name": "daaku", "email": "<EMAIL>"}], "dist": {"shasum": "2f7f9b8fd10d677262b18a884e28d19618e028fb", "tarball": "https://registry.npmjs.org/walker/-/walker-1.0.7.tgz", "integrity": "sha512-cF4je9Fgt6sj1PKfuFt9jpQPeHosM+Ryma/hfY9U7uXGKM7pJCsF0v2r55o+Il54+i77SyYWetB4tD1dEygRkw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDPFjlTHfaAhKBOH5mzPOV/mAE97PbipRC2KZ0kaG9/9wIhAOP0qwLH8NROrU4G0vb9WiAQIJ6E3Vvvtf+CwDh0uwUd"}]}, "directories": {}}, "1.0.8": {"name": "walker", "description": "A simple directory tree walker.", "version": "1.0.8", "homepage": "https://github.com/daaku/nodejs-walker", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["utils", "fs", "filesystem"], "main": "lib/walker", "repository": {"type": "git", "url": "git+https://github.com/daaku/nodejs-walker.git"}, "scripts": {"test": "NODE_PATH=./lib mocha --ui exports"}, "dependencies": {"makeerror": "1.0.12"}, "devDependencies": {"mocha": "9.1.3"}, "license": "Apache-2.0", "gitHead": "afedf7fc2dd65551583c8de1bb1c68640d098f4b", "bugs": {"url": "https://github.com/daaku/nodejs-walker/issues"}, "_id": "walker@1.0.8", "_nodeVersion": "16.11.1", "_npmVersion": "8.1.0", "dist": {"integrity": "sha512-ts/8E8l5b7kY0vlWLewOkDXMmPdLcVV4GmOQLyxuSswIJsweeFZtAsMF7k1Nszz+TYBQrlYRmzOnr398y1JemQ==", "shasum": "bd498db477afe573dc04185f011d3ab8a8d7653f", "tarball": "https://registry.npmjs.org/walker/-/walker-1.0.8.tgz", "fileCount": 5, "unpackedSize": 5799, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3AwqCRA9TVsSAnZWagAAEKUP/3V+pr1jrOXhkdAmxGWy\ncp6T7zJ8Pdv2WGw/n+sXYtkcFQSbvCRAAwBpsbrW58CaB7lYlligOQM24yKt\nWlg+wQ9zSC2MJtNI+cUQX9f9FVFVGMHUzxKjqc1CD7rHmQc/S4Fdr4iL75YE\nuuO5MrKayvf2onjXm9wN5J/jXWA5o0LFccwxuIuRQDBVEYe9x9kBSVz1Mfqn\nCpQLO1p7Mf5jM+YzuDPwAKiS4DJewwfY2/kdDHzlpsDRoJ+IyrP1SjAhZa1h\ncC+7MJrSkkYT3Ur045ePQ44DYgH/0Ic/4OdeV6P8bdSUlfAIIsJTL7VB8KJK\n1qx90qexnF+rPi8e+6ls2aof865eSUCPZHq60YjtWuIcwKJIyi/7EUWmgZ6q\njdbj6bUty0T6s0cJWRF0gIfAH6reIO5qlCfZvnsp1dflaRhyDuGGJTHStre2\nDnXaAgOmt2YZ/Vkdfic1GYYOZ2P6SWcvJQ/c1Ke3VWngZ+74onkX2m+3WOz4\nuIdbDDoUl7UCRpMSC7CB/JfLXdpPbW7m+unoLKVj1er9bdYO/Ec7ZyYhQmkp\nPEKfT8ST+w9elGX4DXQ8LZ2YCMx/XyAhz1GDZ5VOkW45HvzbD9ckAaAVGjzt\nBVPfJtvSfjbM3HjpaIm3vT36tTv1kzMw4evb9elgn98bGtHitE/KJpUtkQ4O\nEUyJ\r\n=3Zcq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIExijBiCKtVaHtLvEycsJxzwhn8qwVAAlpyRUwjto3bDAiEAw3kwZL7VYp3bklLm7PJqQ4Tc+qBUGQzD/48Wd6e7pMI="}]}, "_npmUser": {"name": "daaku", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "daaku", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/walker_1.0.8_1634997814524_0.6480255352259674"}, "_hasShrinkwrap": false}}, "maintainers": [{"name": "daaku", "email": "<EMAIL>"}], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/daaku/nodejs-walker.git"}, "time": {"modified": "2023-03-04T05:09:16.991Z", "created": "2011-02-25T03:14:27.035Z", "0.0.1": "2011-02-25T03:14:27.035Z", "0.0.3": "2011-02-25T03:14:27.035Z", "0.0.4": "2011-04-18T02:16:54.498Z", "1.0.0": "2011-07-24T19:45:54.486Z", "1.0.1": "2011-07-24T19:53:40.513Z", "1.0.2": "2011-07-27T17:41:46.799Z", "1.0.3": "2011-08-17T21:07:44.294Z", "1.0.5": "2013-10-09T18:39:46.815Z", "1.0.6": "2014-04-06T00:45:18.489Z", "1.0.7": "2015-05-21T04:26:20.600Z", "1.0.8": "2021-10-23T14:03:34.682Z"}, "readme": "walker [![Build Status](https://secure.travis-ci.org/daaku/nodejs-walker.png)](http://travis-ci.org/daaku/nodejs-walker)\n======\n\nA nodejs directory walker. Broadcasts events for various file types as well as\na generic \"entry\" event for all types and provides the ability to prune\ndirectory trees. This shows the entire API; everything is optional:\n\n```javascript\nWalker('/etc/')\n  .filterDir(function(dir, stat) {\n    if (dir === '/etc/pam.d') {\n      console.warn('Skipping /etc/pam.d and children')\n      return false\n    }\n    return true\n  })\n  .on('entry', function(entry, stat) {\n    console.log('Got entry: ' + entry)\n  })\n  .on('dir', function(dir, stat) {\n    console.log('Got directory: ' + dir)\n  })\n  .on('file', function(file, stat) {\n    console.log('Got file: ' + file)\n  })\n  .on('symlink', function(symlink, stat) {\n    console.log('Got symlink: ' + symlink)\n  })\n  .on('blockDevice', function(blockDevice, stat) {\n    console.log('Got blockDevice: ' + blockDevice)\n  })\n  .on('fifo', function(fifo, stat) {\n    console.log('Got fifo: ' + fifo)\n  })\n  .on('socket', function(socket, stat) {\n    console.log('Got socket: ' + socket)\n  })\n  .on('characterDevice', function(characterDevice, stat) {\n    console.log('Got characterDevice: ' + characterDevice)\n  })\n  .on('error', function(er, entry, stat) {\n    console.log('Got error ' + er + ' on entry ' + entry)\n  })\n  .on('end', function() {\n    console.log('All files traversed.')\n  })\n```\n\nYou specify a root directory to walk and optionally specify a function to prune\nsub-directory trees via the `filterDir` function. The Walker exposes a number\nof events, broadcasting various file type events a generic error event and\nfinally the event to signal the end of the process.\n", "readmeFilename": "readme.md", "homepage": "https://github.com/daaku/nodejs-walker", "keywords": ["utils", "fs", "filesystem"], "bugs": {"url": "https://github.com/daaku/nodejs-walker/issues"}, "license": "Apache-2.0", "users": {"mightyjongyo": true, "shuoshubao": true, "monjer": true, "dunstontc": true, "xtx1130": true, "wisetc": true, "flumpus-dev": true}}