{"_id": "@jridgewell/sourcemap-codec", "_rev": "14-0c153823f76864ab7f4be779d6c9f239", "name": "@jridgewell/sourcemap-codec", "dist-tags": {"beta": "1.4.16-beta.0", "latest": "1.5.4"}, "versions": {"1.4.9": {"name": "@jridgewell/sourcemap-codec", "version": "1.4.9", "keywords": ["sourcemap", "vlq"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@jridgewell/sourcemap-codec@1.4.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/sourcemap-codec#readme", "bugs": {"url": "https://github.com/jridgewell/sourcemap-codec/issues"}, "dist": {"shasum": "6963babca1e1b8a8dc1c379bd4bd2bf9c21c356a", "tarball": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.9.tgz", "fileCount": 9, "integrity": "sha512-iKsUDLGOrC5pSdVTyb8zJI/f55wItTzGtfGWiWPWTc8h2P4oucax7XOGSRq9V2aA1nwE8qMaGvwdXk3PZRtgjg==", "signatures": [{"sig": "MEUCIQCOb9yMqcfn9MO0xXY06eyS4OwrGx+73fvmbGnAzUOEtwIgCYnnRfjPdp1HfvQ6S+tgyyEQeekZRMlHLtuhFrmTmiE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28436, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/gJHCRA9TVsSAnZWagAAMDAP/3IJYi21SNJ8AtP6iMzt\nuWZ5MOYxa2Ozzpk6OTSDZDtsSu6ecfLzL5s1gHoYdYIRQoU+4jyHUi4Aa5Ue\nkaEVEsfrq0QclgniIxGYM8PycY+YXXfnFV4IUlYWEUuUBe37uxb5q0z4y1qn\n7KOFsojHATLhB2Z/DXdwCx3NAUsAfu4KHfm+4dftxKiz4bAXujoCDmYYRvYu\n1M+lxaA4Y45Vij1z5HMsXFqRF/Wp4TCbYH8ZxR84jmiJWO7rhUVgsuRl8fq2\nzFYO3H147C3bFc6N22UfM9A/72UXueUi9EGsi57x+UZRb1gE0mZKwROJ2Hde\neB3uPYc+5TFn5YyxSyHfNM1qZWyMk0gHRMglwOiH0nC0HPQ9lfZhV+MFJiaG\nceSaSsG6F0rlj4VESCDuDRa5H9+NEXau/PGG0vEvFCi+wEkdtWnvVJ0U++xz\ntSIxI/MKG3fOTFx8bcA8JUeWMaZxUSR+KphybtVU8mKQc1+tzkFf8OWInDE0\nXKp8vYPbVvdkZeQMF2MzpCwD+/IxH05ZoJamzrMxBHZHymPZe8gLmtPI1N16\nct3bFh8tK5ZdCNs5roJt7hohb7tMSN9HhcLckAm6Rccl4alGynsnZQ/HQboi\nSIv6Tbatlh3jbrbz+bN3wskMvAK5XaMJjPcl8Nf4Yly4BQbmSde0A0tJkc2P\nsWVM\r\n=Uw7z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/sourcemap-codec.umd.js", "module": "dist/sourcemap-codec.mjs", "exports": {".": {"import": "./dist/sourcemap-codec.mjs", "browser": "./dist/sourcemap-codec.umd.js", "require": "./dist/sourcemap-codec.umd.js"}, "./package.json": "./package.json"}, "gitHead": "6e2f9e80f7ba29ad1856f0862ab4ae834be88045", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:only", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "pretest": "run-s build:rollup", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:only", "test:lint": "run-s -n test:lint:*", "test:only": "mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "mocha --watch", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "test:coverage": "c8 mocha", "benchmark:only": "node benchmark/index.js", "prepublishOnly": "npm run preversion", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "typings": "dist/types/sourcemap-codec.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/sourcemap-codec.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Encode/decode sourcemap mappings", "directories": {}, "_nodeVersion": "14.18.3", "_hasShrinkwrap": false, "devDependencies": {"c8": "7.11.0", "mocha": "9.2.0", "eslint": "8.7.0", "rollup": "2.64.0", "prettier": "2.5.1", "benchmark": "2.1.4", "source-map": "0.6.1", "typescript": "4.5.4", "@types/node": "17.0.15", "npm-run-all": "4.1.5", "source-map-js": "1.0.2", "sourcemap-codec": "1.4.8", "eslint-config-prettier": "8.3.0", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/parser": "5.10.0", "@typescript-eslint/eslint-plugin": "5.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/sourcemap-codec_1.4.9_1644036679758_0.8290789718945382", "host": "s3://npm-registry-packages"}}, "1.4.10": {"name": "@jridgewell/sourcemap-codec", "version": "1.4.10", "keywords": ["sourcemap", "vlq"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@jridgewell/sourcemap-codec@1.4.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/sourcemap-codec#readme", "bugs": {"url": "https://github.com/jridgewell/sourcemap-codec/issues"}, "dist": {"shasum": "baf57b4e2a690d4f38560171f91783656b7f8186", "tarball": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.10.tgz", "fileCount": 8, "integrity": "sha512-Ht8wIW5v165atIX1p+JvKR5ONzUyF4Ac8DZIQ5kZs9zrb6M8SJNXpx1zn04rn65VjBMygRoMXcyYwNK0fT7bEg==", "signatures": [{"sig": "MEUCICrs6smLQTjkwTfQMeyeeXBjsQnpMGbNNr9rwEHeusW/AiEAzDW2dhfMep9twXLtzn4lQ+zMntZezbs+a20xbBj7gcg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28791, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/mDiCRA9TVsSAnZWagAAcVcP/1BMO380a1QMToFhod5y\nF5WCHq6hjABJLkOU8c0K+Af+Ps8KzXl0gWtzH6omeLLU5iDo+D8mbWO9w8/C\nGhpZcbMJGK5ck+iBq21ApP7B5U7fv2/c2OXlLvHJXgE7X4uq+lPz6ppRKRho\nbnpj8NPQJeffDLhWu9D46OiM2MkvI3j9XSSbmqR9+r6GNxCAU3NiDpDb3pDJ\n59sPr3YDUaiCk5iIZs6gjXWaePsO/eipIxLEjlOONzfKMRVz8SRHOgajFjrV\njU047Qr+CYnvFjAV0xGkLszpPHMiw2v92uXVo2jNO0Wfjb2CAMC/O4gDUQjz\nOSWWE3di1zC5vjZX+A4bGe5FG8DUfG+GoAh5Zxq573qKwp+hB1Eg0WbbhmJK\nBUVksyX57fROaoiJ6PJ2Tw/eJd0XlhiAjclEce1Wx0vYiFJsXG0+8rgG/iak\nF31yTtYxDT6yq1seo3Xr04kUYxKKRlvSJxfgjos8EH0N/paha/gkwMohSwVf\noy6tx/XfPGs1PvEClBgbMnFm2sU+RF8FM+dn53FCnV8ZIPt5Wf/DCm6jDuPn\nmqlgLkPiR1Ad4CkxxkurJjcu9KwgJY7mzBrpD75Y4cgqc7mg+ZNgMO2g3Qs+\nRtqrY8BZjMH95nEpaHPvk/1HpzCSPc/nfV0yEaRWlp/0qJywIX3ogNYCEnQh\n1rln\r\n=zPKh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/sourcemap-codec.umd.js", "module": "dist/sourcemap-codec.mjs", "exports": {".": {"import": "./dist/sourcemap-codec.mjs", "browser": "./dist/sourcemap-codec.umd.js", "require": "./dist/sourcemap-codec.umd.js"}, "./package.json": "./package.json"}, "gitHead": "2464a2b0da325a4c918ebc6e5668d2104fbac624", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:only", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "pretest": "run-s build:rollup", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:only", "test:lint": "run-s -n test:lint:*", "test:only": "mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "mocha --watch", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "test:coverage": "c8 mocha", "benchmark:only": "node benchmark/index.js", "prepublishOnly": "npm run preversion", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "typings": "dist/types/sourcemap-codec.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/sourcemap-codec.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Encode/decode sourcemap mappings", "directories": {}, "_nodeVersion": "16.13.2", "_hasShrinkwrap": false, "devDependencies": {"c8": "7.11.0", "mocha": "9.2.0", "eslint": "8.7.0", "rollup": "2.64.0", "prettier": "2.5.1", "benchmark": "2.1.4", "source-map": "0.6.1", "typescript": "4.5.4", "@types/node": "17.0.15", "npm-run-all": "4.1.5", "source-map-js": "1.0.2", "sourcemap-codec": "1.4.8", "eslint-config-prettier": "8.3.0", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/parser": "5.10.0", "@typescript-eslint/eslint-plugin": "5.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/sourcemap-codec_1.4.10_1644060897901_0.6871697945298141", "host": "s3://npm-registry-packages"}}, "1.4.11": {"name": "@jridgewell/sourcemap-codec", "version": "1.4.11", "keywords": ["sourcemap", "vlq"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@jridgewell/sourcemap-codec@1.4.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/sourcemap-codec#readme", "bugs": {"url": "https://github.com/jridgewell/sourcemap-codec/issues"}, "dist": {"shasum": "771a1d8d744eeb71b6adb35808e1a6c7b9b8c8ec", "tarball": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.11.tgz", "fileCount": 8, "integrity": "sha512-Fg32GrJo61m+VqYSdRSjRXMjQ06j8YIYfcTqndLYVAaHmroZHLJZCydsWBOTDqXS2v+mjxohBWEMfg97GXmYQg==", "signatures": [{"sig": "MEUCIQDVlw8j1iC2BoY5fJeodZLmGRry6e8RFNa5BrBnrod2uwIgAwZyv/Qzqitny7r8l9AQePm51WRrTsFyqdaft/mawa4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28905, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBiDMCRA9TVsSAnZWagAAcGwP/3IskiJbF70fM9yRsUmY\nu3h6KYfMKlkPU0FLkRkSbZ8xYIZecF9SGgTihOnJguErOFnnM+qlOWX+UeIb\nPkamVxr5quu/CkOMEa15atMwbG1/A0OjsXJn1Gdy1Krv/m+hxKOeBe3AoEA8\nPEmD7JztWHZesnGiFfRc9rG5G0FuQoAAsDgP3EaIXZ6359af6a1MLkQ4HS6u\nvYrhlikfw67B8IdTEcZAWo4JGfb8TtxceO0Bt+XWs0HFWiKNtk0PrblBDgXd\nkIhoA/NkysEzDapJgTeXu6skVOQrajB1AIiYOvWVnuNKs+SR7wOJUDOJ54yN\nv3Ul76m+Hc7ZpFIrF+H/IAZ9unH4dYX4Nva2f5X3trr4iTzgUvanoJ1tm2p6\nfrrW/n45fvzxtPJy62dcAShKnIxTJ80uKPO3DG7Bqf/WwGgOPulJrf6Q+gSZ\nMT1Z8VBgk0UV6TWu7UIwlF8xHgcZ6Hgi8W39Dj+CANcsup9MttxANoBmg2ZT\nEJNhxMYKgq0A7Wod1JzHUlFgwcVEvu9zzX/rr4mKzyAnuNzYTGRFYsBHezo/\nK0lqopxSdEWvzqT4WvsULoJgwm6E+PK90uGkR4XS/3abtXLJqvwsL0OdCsC4\n4sn2SZAdIV12KmFljQzaVedGimtrWo8jhgRS/TvdLeXlxbzKvk/OIXepiQuC\nML1N\r\n=Q+sZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/sourcemap-codec.umd.js", "module": "dist/sourcemap-codec.mjs", "exports": {".": {"import": "./dist/sourcemap-codec.mjs", "browser": "./dist/sourcemap-codec.umd.js", "require": "./dist/sourcemap-codec.umd.js"}, "./package.json": "./package.json"}, "gitHead": "2e11c3357477c5715fbb139687b9b38da1310b0a", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:only", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "pretest": "run-s build:rollup", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:only", "test:lint": "run-s -n test:lint:*", "test:only": "mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "mocha --watch", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "test:coverage": "c8 mocha", "benchmark:only": "node benchmark/index.js", "prepublishOnly": "npm run preversion", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "typings": "dist/types/sourcemap-codec.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/sourcemap-codec.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "Encode/decode sourcemap mappings", "directories": {}, "_nodeVersion": "16.13.2", "_hasShrinkwrap": false, "devDependencies": {"c8": "7.11.0", "mocha": "9.2.0", "eslint": "8.7.0", "rollup": "2.64.0", "prettier": "2.5.1", "benchmark": "2.1.4", "source-map": "0.6.1", "typescript": "4.5.4", "@types/node": "17.0.15", "npm-run-all": "4.1.5", "source-map-js": "1.0.2", "sourcemap-codec": "1.4.8", "eslint-config-prettier": "8.3.0", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/parser": "5.10.0", "@typescript-eslint/eslint-plugin": "5.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/sourcemap-codec_1.4.11_1644568780215_0.44314274303373513", "host": "s3://npm-registry-packages"}}, "1.4.12": {"name": "@jridgewell/sourcemap-codec", "version": "1.4.12", "keywords": ["sourcemap", "vlq"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@jridgewell/sourcemap-codec@1.4.12", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/sourcemap-codec#readme", "bugs": {"url": "https://github.com/jridgewell/sourcemap-codec/issues"}, "dist": {"shasum": "7ed98f6fa525ffb7c56a2cbecb5f7bb91abd2baf", "tarball": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.12.tgz", "fileCount": 8, "integrity": "sha512-az/NhpIwP3K33ILr0T2bso+k2E/SLf8Yidd8mHl0n6sCQ4YdyC8qDhZA6kOPDNDBA56ZnIjngVl0U3jREA0BUA==", "signatures": [{"sig": "MEQCIG98xO0zpj7lc/uDTk1BOV5Mcb/H4pYB5cW5oJuSMQJoAiBKOgy2qzdEOuenKSmuA+w/R5QUP63eNM2zDocRRv7Yfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicEmHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoY4A/8Cs8OAf5geGv8XIA/piYkF4C1Z1ZI9bKFe1jGD+ULZNiGBSqu\r\nt/H+0q79ZpTXK8LdNLF0FkN2yYQk197i1IVF86q098v5hqrWawqtQ4YrVmYW\r\nTE+QhUngzB7GLHBqzGrjRb+uUN5IwDOLuOTA98z+k9e1P65gFu3jKQo83SU/\r\nhbZ5aIa5FVwws512jb8gR7W68CYnx486v2eDcUpMx8ne1G5xkJfYKOWxxMA6\r\n+Qps2I3mc9nsWDiEBQSjKtfbefk54QMUzc6rgrBHduNlfIBcqSwQnhdahO6U\r\n2EUX8TXFNeePOJgoOswTy1/l62lsBL9kuz4OTjYcjP6Vt3vcgYa5IYt4f6VD\r\nPeehxF373+L9CcvznqVkcFmgWX3EfjjJ5iN0iuk/nkZPCo6NO3Y99gH1Qh7a\r\nNVAE8t/dcg0NbNxniXM2eKS087vuifO9eZJCWvjve1Qp4oTrEXTWXAqphLe6\r\nENX5irxVLhKj2KmalsJ0vr3HYc9R9t79cs6SsMTMAbsMznOmFV7ZoRE1Qv94\r\n3rEGVNzHQudCk/AEmE8lOoXB4RP4oRz38C26aRZT7Nfn3xO1e0q47nHVh0Og\r\nAsYeR0SQdn2/6gypfL+bxu/ZJpvWNbw8wno4s4DZEnAwJkJcZb2yTm5TDvXd\r\nyUTgbkk5vB6Q2/XzxOSd4gCeVdmg6qrlyPM=\r\n=NWMX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/sourcemap-codec.umd.js", "module": "dist/sourcemap-codec.mjs", "exports": {".": {"import": "./dist/sourcemap-codec.mjs", "browser": "./dist/sourcemap-codec.umd.js", "require": "./dist/sourcemap-codec.umd.js"}, "./package.json": "./package.json"}, "gitHead": "1407758616bf08ad8807c0c6380fda85567a158d", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:only", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "pretest": "run-s build:rollup", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:*", "test:lint": "run-s -n test:lint:*", "test:only": "mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "mocha --watch", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "test:coverage": "c8 mocha", "benchmark:only": "node --expose-gc benchmark/index.js", "prepublishOnly": "npm run preversion", "benchmark:install": "cd benchmark && npm install", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "typings": "dist/types/sourcemap-codec.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/sourcemap-codec.git", "type": "git"}, "_npmVersion": "8.6.0", "description": "Encode/decode sourcemap mappings", "directories": {}, "_nodeVersion": "18.0.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "7.11.0", "mocha": "9.2.0", "eslint": "8.7.0", "rollup": "2.64.0", "prettier": "2.5.1", "benchmark": "2.1.4", "source-map": "0.6.1", "typescript": "4.5.4", "@types/node": "17.0.15", "npm-run-all": "4.1.5", "source-map-js": "1.0.2", "sourcemap-codec": "1.4.8", "eslint-config-prettier": "8.3.0", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/parser": "5.10.0", "@typescript-eslint/eslint-plugin": "5.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/sourcemap-codec_1.4.12_1651526023647_0.29960361655136225", "host": "s3://npm-registry-packages"}}, "1.4.13": {"name": "@jridgewell/sourcemap-codec", "version": "1.4.13", "keywords": ["sourcemap", "vlq"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@jridgewell/sourcemap-codec@1.4.13", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/sourcemap-codec#readme", "bugs": {"url": "https://github.com/jridgewell/sourcemap-codec/issues"}, "dist": {"shasum": "b6461fb0c2964356c469e115f504c95ad97ab88c", "tarball": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.13.tgz", "fileCount": 8, "integrity": "sha512-GryiOJmNcWbovBxTfZSF71V/mXbgcV3MewDe3kIMCLyIh5e7SKAeUZs+rMnJ8jkMolZ/4/VsdBmMrw3l+VdZ3w==", "signatures": [{"sig": "MEYCIQCRqj+c2Xuqs//p86aEtoh7FcSb7eXlbBZEAKEvcWDqRgIhAPWWIt01Wtpce1jcds5YtDYPENx1C87G906FpVxHCxe1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidEPYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmquJQ//aI00sgDB+5E3Q29YU6HcO/MmqhqKCPrPVfYi/01dfADIrd4B\r\ngsNf9Kxd33JX3U7oaNXGtvzue8hD4ZE0O9GMsxRwOsY3rweyXikv9sJ8LLdo\r\n2ydjii88JxzPVXflKa37x8abZleppooVfyMXKjokfWR8eeEj/engQ3MdV6/Y\r\nXDZ5Y3mVWLpXmXDNVi3zSl4ZwguP6u3g8e0VrZ03BPsF/LiYQnXaRYxkoV6Z\r\n9lbKsiHbpGMH0QS0FpH9sDpcldSpLJRt+YT+AjixaQZu4Y6x13G7m2mOhdGW\r\nDvkeGUzwYcHciFeciOV/cDYzMh9a4gxjEnLtjHBOgPQVDSP5ryevAIZWTnMc\r\n6wcTh00szPxZL6cm3FIiwZv78b2ts+c8rdtlVYZBVhsWnWxIWIKFmae6Ur57\r\nKHP2UWwCteh8L/Ga3jHcPAWWmznuzh2tDUUzYj3esvwbKXTjIYcBtnRS75KK\r\nAS6MPzEcbkIwweiSCPRXrOQSo+X/UTA4v6BFbL91jFtPQKSM5yzk1gLHwKLq\r\n3RE6tf5c3ZMF0bKOiSHjIJBZdZx2k143QvIi//v5ffKigCPodTwqasurgaU4\r\nGEbvl/sGfT9tkdDfzfP0BRnKYTtkG2eNA5Fa8RXEtd1Lx5jNHYb57hsYozYQ\r\nrMXGc6PHpUok/8lrmPzdS5MWDBPmZgs499w=\r\n=Q1dK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/sourcemap-codec.umd.js", "module": "dist/sourcemap-codec.mjs", "exports": {".": {"types": "./dist/types/sourcemap-codec.d.ts", "import": "./dist/sourcemap-codec.mjs", "browser": "./dist/sourcemap-codec.umd.js", "require": "./dist/sourcemap-codec.umd.js"}, "./package.json": "./package.json"}, "gitHead": "3090a00f554f0d83110e3de232eeac3109634a0f", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:only", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "pretest": "run-s build:rollup", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:*", "test:lint": "run-s -n test:lint:*", "test:only": "mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "mocha --watch", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "test:coverage": "c8 mocha", "benchmark:only": "node --expose-gc benchmark/index.js", "prepublishOnly": "npm run preversion", "benchmark:install": "cd benchmark && npm install", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "typings": "dist/types/sourcemap-codec.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/sourcemap-codec.git", "type": "git"}, "_npmVersion": "8.5.1", "description": "Encode/decode sourcemap mappings", "directories": {}, "_nodeVersion": "17.6.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "7.11.2", "mocha": "9.2.0", "eslint": "8.7.0", "rollup": "2.64.0", "prettier": "2.5.1", "benchmark": "2.1.4", "source-map": "0.6.1", "typescript": "4.5.4", "@types/node": "17.0.15", "npm-run-all": "4.1.5", "source-map-js": "1.0.2", "sourcemap-codec": "1.4.8", "eslint-config-prettier": "8.3.0", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/parser": "5.10.0", "@typescript-eslint/eslint-plugin": "5.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/sourcemap-codec_1.4.13_1651786712646_0.9068072624866026", "host": "s3://npm-registry-packages"}}, "1.4.14": {"name": "@jridgewell/sourcemap-codec", "version": "1.4.14", "keywords": ["sourcemap", "vlq"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@jridgewell/sourcemap-codec@1.4.14", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/sourcemap-codec#readme", "bugs": {"url": "https://github.com/jridgewell/sourcemap-codec/issues"}, "dist": {"shasum": "add4c98d341472a289190b424efbdb096991bb24", "tarball": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz", "fileCount": 9, "integrity": "sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw==", "signatures": [{"sig": "MEQCIAHlT4768yuL9BvR7EN1O0sBMLfC1XI1wCwietPQyvBeAiBN1Uno9Tmcrlk6U/9UJlXC2DxgHHXxyHBWrTj8GX2OnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuI8PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7cRAAoN7LwbvCS1RBm+DBFO2drGfI8qLoKIxS9tPbaUO+kxDjmW1D\r\nYvJD+KJzHBlW8yOtPcecntUOahT37jDXEBCI0c221MZRO9N+rR1Hq7iNIEC0\r\nqXviwJ4hKC9EsSkiWrh3mTLoc5NCtaSA5mmt3UiVEd1KJeO3PSlKT2X0nqKm\r\n0FvQp4NsIbl75Lr/ENzwiL2EGycc8vnAOYOrpQFdIA3XlYH0gW3eSJG9zI2T\r\n9CCJlq5v3rTMpgn0PWsS4/3YHjY9yUkUG58L7SHCZxTFUCk24rLixGH2/eoR\r\n1UgaEswUvyTqus2ibZgy0O+6yIzTZTZ8hDUVZT6TUizhyVxMfrSsBz7aufzp\r\n+tRC7Zj3iPiR9oDu5QyAbQ4kxljtMoQ+O8mjyq9sK05ZECbb2thdaoLXeXPa\r\nv76Q4hSae+1UAE2V93py2hWbWGL4Yld2Fua2IOh+hmj7pQIKedNpvE7GtIlf\r\nljFHeI5l2uN03J7h4pbKzcSEub6kGDoFkImqEG53ZXwZMQZix5JP13zhNNju\r\nHH1MOEA83tJ+nWjdq4RMckKy1B/uNXtyENf0GHpqm0BtzQRRHDXDofzZLFv3\r\n4Mkw+KgnzkG4Px6voxw7nNv+YNLI0eGOwRfV3NT7TJuz0NanpWx1ePr/fKPJ\r\nuP4PCGTM7HQ5H6LEe663kVIJGYLdr7IJtYg=\r\n=OLiK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/sourcemap-codec.umd.js", "module": "dist/sourcemap-codec.mjs", "exports": {".": [{"types": "./dist/types/sourcemap-codec.d.ts", "import": "./dist/sourcemap-codec.mjs", "browser": "./dist/sourcemap-codec.umd.js", "require": "./dist/sourcemap-codec.umd.js"}, "./dist/sourcemap-codec.umd.js"], "./package.json": "./package.json"}, "gitHead": "802e17965c9b5694b338412618a1b32bd07af166", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:only", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "pretest": "run-s build:rollup", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:*", "test:lint": "run-s -n test:lint:*", "test:only": "mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "mocha --watch", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "test:coverage": "c8 mocha", "benchmark:only": "node --expose-gc benchmark/index.js", "prepublishOnly": "npm run preversion", "benchmark:install": "cd benchmark && npm install", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "typings": "dist/types/sourcemap-codec.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/sourcemap-codec.git", "type": "git"}, "_npmVersion": "8.5.1", "description": "Encode/decode sourcemap mappings", "directories": {}, "_nodeVersion": "17.6.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "7.11.2", "mocha": "9.2.0", "eslint": "8.7.0", "rollup": "2.64.0", "prettier": "2.5.1", "benchmark": "2.1.4", "source-map": "0.6.1", "typescript": "4.5.4", "@types/node": "17.0.15", "npm-run-all": "4.1.5", "source-map-js": "1.0.2", "sourcemap-codec": "1.4.8", "eslint-config-prettier": "8.3.0", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/parser": "5.10.0", "@typescript-eslint/eslint-plugin": "5.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/sourcemap-codec_1.4.14_1656262415475_0.6095138931654185", "host": "s3://npm-registry-packages"}}, "1.4.15": {"name": "@jridgewell/sourcemap-codec", "version": "1.4.15", "keywords": ["sourcemap", "vlq"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@jridgewell/sourcemap-codec@1.4.15", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/sourcemap-codec#readme", "bugs": {"url": "https://github.com/jridgewell/sourcemap-codec/issues"}, "dist": {"shasum": "d7c6e6755c78567a951e04ab52ef0fd26de59f32", "tarball": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz", "fileCount": 8, "integrity": "sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==", "signatures": [{"sig": "MEYCIQCdtqK56anvpPA/m2Ai72LGn1gw+0ahc2XRHEq0HI91cwIhALTiTnS6bn26kJhLcTVIzCHsHfc5nu0p8toDqtXzwPpH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45865, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkL4M1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqODA//W+0XyABM6jaoX+Qy513esgwRWz68vBIIJSJipgRIrD9JkrFS\r\n78BNoBTyUDo+9U8fQafbA6EnlORZ0hkkc970o+eZE1VraJweTyIrAT966u4y\r\nxvOLdZv+FGjs4nO3exZsONbQ2t/xzVEfHWAT2+z0ANqRmoisEigVz51JAzfc\r\nbq3QYr41IzwSpjaQ8IJ16IyCAQIeTDYx1NhHT21XyvaO9HXC3CgPX0ogleI1\r\ny2h2w+cBMUR2EAkhV2mapxfyU3SDdddvDj5QlU0g5R00RbIcLLVRPUVuUj7y\r\nhW3SJodibrObt5DmjqoJjV9AurQwZXHQCOwO32c0x0TdiVFmSDbAnydlfHZG\r\nFFpDoJYgWfUg/lIRpu4oEpmsDYgAvEDUp+8ToVzLbC8qJAEfPRJ3t/ygjqIP\r\n13DOY1tooKJtfpA8lyiHxHLROobi2uXWJ1bZpzKkFGKP77f3pDjOSpdOi3YH\r\nHWJV/Kx99Mh7upeBkdAh3q8hpeDnKKisAsx/G2hrGVOLPPiVxOeA8YI6clLh\r\nJ5oCyJZ4eOflxa9Itq+0OISFzCPUyZ6b/u+JT7z0aQVHtrTjro5Ap2UJ+KUy\r\nvcXvSzqDxDiBMoFCypxkbwSZCvA0IvCRonnNRG7bHO2yPDqiVaHy0sctKuIX\r\nQEgjKGqJn98M7CZVXC2nf5cFLZRLfDxvV/o=\r\n=ACMx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/sourcemap-codec.umd.js", "types": "dist/types/sourcemap-codec.d.ts", "module": "dist/sourcemap-codec.mjs", "exports": {".": [{"types": "./dist/types/sourcemap-codec.d.ts", "import": "./dist/sourcemap-codec.mjs", "browser": "./dist/sourcemap-codec.umd.js", "require": "./dist/sourcemap-codec.umd.js"}, "./dist/sourcemap-codec.umd.js"], "./package.json": "./package.json"}, "gitHead": "f3d95b857d24fc3f1692dec4d1ed724fe12488a3", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:only", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "pretest": "run-s build:rollup", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:*", "test:lint": "run-s -n test:lint:*", "test:only": "mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "mocha --watch", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "test:coverage": "c8 mocha", "benchmark:only": "node --expose-gc benchmark/index.js", "prepublishOnly": "npm run preversion", "benchmark:install": "cd benchmark && npm install", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/sourcemap-codec.git", "type": "git"}, "_npmVersion": "9.5.0", "description": "Encode/decode sourcemap mappings", "directories": {}, "_nodeVersion": "18.15.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "7.11.2", "mocha": "9.2.0", "eslint": "8.7.0", "rollup": "2.64.0", "prettier": "2.5.1", "benchmark": "2.1.4", "source-map": "0.6.1", "typescript": "4.5.4", "@types/node": "17.0.15", "npm-run-all": "4.1.5", "source-map-js": "1.0.2", "sourcemap-codec": "1.4.8", "eslint-config-prettier": "8.3.0", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/parser": "5.10.0", "@typescript-eslint/eslint-plugin": "5.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/sourcemap-codec_1.4.15_1680835381487_0.14823397894902168", "host": "s3://npm-registry-packages"}}, "1.4.16-beta.0": {"name": "@jridgewell/sourcemap-codec", "version": "1.4.16-beta.0", "keywords": ["sourcemap", "vlq"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@jridgewell/sourcemap-codec@1.4.16-beta.0", "homepage": "https://github.com/jridgewell/sourcemap-codec#readme", "bugs": {"url": "https://github.com/jridgewell/sourcemap-codec/issues"}, "dist": {"shasum": "5037f25018ca5d896fa438886b75616bcaa8bdbe", "tarball": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.16-beta.0.tgz", "fileCount": 10, "integrity": "sha512-qiZJiTfyb00BApxRU7Apz/3jtlp4gKgOmCXlGQRlIQ5zg6U0uYIb8lZBfbiJ+TxAEJ+rczfY07+CExd8sTRo5w==", "signatures": [{"sig": "MEUCIQDVo7V+KV09KzwIr0BHCb+WO6FbfL5ZgSmjzSB9XDpQTQIgY3V+0KthOEnXEFQ/3jcx6MCaQEJCsAtjKTACG8YcFCo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133970}, "main": "dist/sourcemap-codec.umd.js", "types": "dist/types/sourcemap-codec.d.ts", "module": "dist/sourcemap-codec.mjs", "exports": {".": [{"types": "./dist/types/sourcemap-codec.d.ts", "import": "./dist/sourcemap-codec.mjs", "browser": "./dist/sourcemap-codec.umd.js", "require": "./dist/sourcemap-codec.umd.js"}, "./dist/sourcemap-codec.umd.js"], "./package.json": "./package.json"}, "gitHead": "593c7e33afa2b5e069d8254282e64d21c1c0c4d3", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:only", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:*", "test:lint": "run-s -n test:lint:*", "test:only": "mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "mocha --watch", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "test:coverage": "c8 mocha", "benchmark:only": "node --expose-gc benchmark/index.js", "prepublishOnly": "npm run preversion", "benchmark:install": "cd benchmark && npm install", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/sourcemap-codec.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Encode/decode sourcemap mappings", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "7.11.2", "tsx": "4.7.1", "mocha": "9.2.0", "eslint": "8.7.0", "rollup": "2.64.0", "prettier": "2.5.1", "benchmark": "2.1.4", "source-map": "0.6.1", "typescript": "4.5.4", "@types/node": "17.0.15", "npm-run-all": "4.1.5", "@types/mocha": "10.0.6", "source-map-js": "1.0.2", "sourcemap-codec": "1.4.8", "eslint-config-prettier": "8.3.0", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/parser": "5.10.0", "@typescript-eslint/eslint-plugin": "5.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/sourcemap-codec_1.4.16-beta.0_1719307099776_0.38140518466058504", "host": "s3://npm-registry-packages"}}, "1.5.0": {"name": "@jridgewell/sourcemap-codec", "version": "1.5.0", "keywords": ["sourcemap", "vlq"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "@jridgewell/sourcemap-codec@1.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/sourcemap-codec#readme", "bugs": {"url": "https://github.com/jridgewell/sourcemap-codec/issues"}, "dist": {"shasum": "3188bcb273a414b0d215fd22a58540b989b9409a", "tarball": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz", "fileCount": 11, "integrity": "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==", "signatures": [{"sig": "MEYCIQDTKlVwpijmiYpDTegYxRDpI1KCdd96r1ALsKPOlAvH+AIhALOiPy+SkgREuRW/t4xOBLy7hxTbOUcFvcha9pjPMB3Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112815}, "main": "dist/sourcemap-codec.umd.js", "types": "dist/types/sourcemap-codec.d.ts", "module": "dist/sourcemap-codec.mjs", "exports": {".": [{"types": "./dist/types/sourcemap-codec.d.ts", "import": "./dist/sourcemap-codec.mjs", "browser": "./dist/sourcemap-codec.umd.js", "require": "./dist/sourcemap-codec.umd.js"}, "./dist/sourcemap-codec.umd.js"], "./package.json": "./package.json"}, "gitHead": "d8dc7fc3f7602043f56f3efa801569f983d756ec", "scripts": {"lint": "run-s -n lint:*", "test": "run-s -n test:lint test:only", "build": "run-s -n build:*", "lint:ts": "npm run test:lint:ts -- --fix", "build:ts": "tsc --project tsconfig.build.json", "prebuild": "rm -rf dist", "benchmark": "run-s build:rollup benchmark:*", "test:lint": "run-s -n test:lint:*", "test:only": "mocha", "preversion": "run-s test build", "test:debug": "mocha --inspect-brk", "test:watch": "mocha --watch", "build:rollup": "rollup -c rollup.config.js", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "lint:prettier": "npm run test:lint:prettier -- --write", "test:coverage": "c8 mocha", "benchmark:only": "node --expose-gc benchmark/index.js", "prepublishOnly": "npm run preversion", "benchmark:install": "cd benchmark && npm install", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/sourcemap-codec.git", "type": "git"}, "_npmVersion": "10.2.3", "description": "Encode/decode sourcemap mappings", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "7.11.2", "tsx": "4.7.1", "mocha": "9.2.0", "eslint": "8.7.0", "rollup": "2.64.0", "prettier": "2.5.1", "benchmark": "2.1.4", "source-map": "0.6.1", "typescript": "4.5.4", "@types/node": "17.0.15", "npm-run-all": "4.1.5", "@types/mocha": "10.0.6", "source-map-js": "1.0.2", "sourcemap-codec": "1.4.8", "eslint-config-prettier": "8.3.0", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/parser": "5.10.0", "@typescript-eslint/eslint-plugin": "5.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/sourcemap-codec_1.5.0_1720553291759_0.7026135378332499", "host": "s3://npm-registry-packages"}}, "1.5.1": {"name": "@jridgewell/sourcemap-codec", "version": "1.5.1", "keywords": ["sourcemap", "vlq"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/sourcemap-codec@1.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/sourcemaps/tree/main/packages/sourcemap-codec", "bugs": {"url": "https://github.com/jridgewell/sourcemaps/issues"}, "dist": {"shasum": "92bc8eb57aea47c13291476e4d2d246928487720", "tarball": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.1.tgz", "fileCount": 27, "integrity": "sha512-mBLKRHc7Ffw/hObYb9+cunuGNjshQk+vZdwZBJoqiysK/mW3Jq0UXosq8aIhMnLevANhR9yoYfdUEOHg6M9y0g==", "signatures": [{"sig": "MEUCIQCMSQ2KLlpjuiPqBFqnoVXIFitfsa8IJrRPruOsCFsbyAIgNxGW09FLOWRGpt9U4F3z3nHYTJ9OV1mRj9x0tFgWyQ4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 87106}, "main": "dist/sourcemap-codec.umd.js", "types": "types/sourcemap-codec.d.cts", "module": "dist/sourcemap-codec.mjs", "exports": {".": [{"import": {"types": "./types/sourcemap-codec.d.mts", "default": "./dist/sourcemap-codec.mjs"}, "browser": {"types": "./types/sourcemap-codec.d.cts", "default": "./dist/sourcemap-codec.umd.js"}, "require": {"types": "./types/sourcemap-codec.d.cts", "default": "./dist/sourcemap-codec.umd.js"}, "module-sync": {"types": "./types/sourcemap-codec.d.mts", "default": "./dist/sourcemap-codec.mjs"}}, "./dist/sourcemap-codec.umd.js"], "./package.json": "./package.json"}, "gitHead": "90648758f7150574cf6cfd073acadb86f37cf24f", "scripts": {"lint": "run-s -n lint:types lint:format", "test": "run-s -n test:types test:only test:format", "build": "run-s -n build:code build:types", "clean": "run-s -n clean:code clean:types", "benchmark": "run-s build:code benchmark:*", "test:only": "mocha", "build:code": "node ../../esbuild.mjs sourcemap-codec.ts", "clean:code": "tsc --build --clean tsconfig.build.json", "lint:types": "npm run test:types -- --fix", "test:types": "eslint '{src,test}/**/*.ts'", "build:types": "run-s build:types:force build:types:emit build:types:mts", "clean:types": "rimraf dist types", "lint:format": "npm run test:format -- --write", "test:format": "prettier --check '{src,test}/**/*.ts'", "benchmark:only": "node --expose-gc benchmark/index.js", "prepublishOnly": "npm run-s -n build test", "build:types:mts": "node ../../mts-types.mjs", "build:types:emit": "tsc --project tsconfig.build.json", "benchmark:install": "cd benchmark && npm install", "build:types:force": "rimraf tsconfig.build.tsbuildinfo"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/sourcemaps.git", "type": "git", "directory": "packages/sourcemap-codec"}, "_npmVersion": "10.2.3", "description": "Encode/decode sourcemap mappings", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/sourcemap-codec_1.5.1_1751233556855_0.5418348554207144", "host": "s3://npm-registry-packages-npm-production"}}, "1.5.2": {"name": "@jridgewell/sourcemap-codec", "version": "1.5.2", "keywords": ["sourcemap", "vlq"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/sourcemap-codec@1.5.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/sourcemaps/tree/main/packages/sourcemap-codec", "bugs": {"url": "https://github.com/jridgewell/sourcemaps/issues"}, "dist": {"shasum": "4f25c8f17f28ccf70ed16e03f8fbf6d3998cb8fd", "tarball": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.2.tgz", "fileCount": 27, "integrity": "sha512-gKYheCylLIedI+CSZoDtGkFV9YEBxRRVcfCH7OfAqh4TyUyRjEE6WVE/aXDXX0p8BIe/QgLcaAoI0220KRRFgg==", "signatures": [{"sig": "MEYCIQCIJrCqpWfJFL1+uICpaGIDdP09aohWKjs7V8rNKHXkrAIhAIthykzGOFr4Dcx25qOW4Gg7qZfbm4tvkmnIrdh4CZPe", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 86968}, "main": "dist/sourcemap-codec.umd.js", "types": "types/sourcemap-codec.d.cts", "module": "dist/sourcemap-codec.mjs", "exports": {".": [{"import": {"types": "./types/sourcemap-codec.d.mts", "default": "./dist/sourcemap-codec.mjs"}, "browser": {"types": "./types/sourcemap-codec.d.cts", "default": "./dist/sourcemap-codec.umd.js"}, "require": {"types": "./types/sourcemap-codec.d.cts", "default": "./dist/sourcemap-codec.umd.js"}}, "./dist/sourcemap-codec.umd.js"], "./package.json": "./package.json"}, "gitHead": "dad47801cf1793389c88aeea2b2f688e98113eaa", "scripts": {"lint": "run-s -n lint:types lint:format", "test": "run-s -n test:types test:only test:format", "build": "run-s -n build:code build:types", "clean": "run-s -n clean:code clean:types", "benchmark": "run-s build:code benchmark:*", "test:only": "mocha", "build:code": "node ../../esbuild.mjs sourcemap-codec.ts", "clean:code": "tsc --build --clean tsconfig.build.json", "lint:types": "npm run test:types -- --fix", "test:types": "eslint '{src,test}/**/*.ts'", "build:types": "run-s build:types:force build:types:emit build:types:mts", "clean:types": "rimraf dist types", "lint:format": "npm run test:format -- --write", "test:format": "prettier --check '{src,test}/**/*.ts'", "benchmark:only": "node --expose-gc benchmark/index.js", "prepublishOnly": "npm run-s -n build test", "build:types:mts": "node ../../mts-types.mjs", "build:types:emit": "tsc --project tsconfig.build.json", "benchmark:install": "cd benchmark && npm install", "build:types:force": "rimraf tsconfig.build.tsbuildinfo"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/sourcemaps.git", "type": "git", "directory": "packages/sourcemap-codec"}, "_npmVersion": "10.2.3", "description": "Encode/decode sourcemap mappings", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/sourcemap-codec_1.5.2_1751259851888_0.0876677820350904", "host": "s3://npm-registry-packages-npm-production"}}, "1.5.3": {"name": "@jridgewell/sourcemap-codec", "version": "1.5.3", "keywords": ["sourcemap", "vlq"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/sourcemap-codec@1.5.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/jridgewell/sourcemaps/tree/main/packages/sourcemap-codec", "bugs": {"url": "https://github.com/jridgewell/sourcemaps/issues"}, "dist": {"shasum": "2d7bbc63fdfe4e4fa6b3478f651ec3844e284c1b", "tarball": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.3.tgz", "fileCount": 27, "integrity": "sha512-AiR5uKpFxP3PjO4R19kQGIMwxyRyPuXmKEEy301V1C0+1rVjS94EZQXf1QKZYN8Q0YM+estSPhmx5JwNftv6nw==", "signatures": [{"sig": "MEUCIDF/rM46dhgQhxtvooOoKl6aHWyMLKbreofWjd7jFrnDAiEAs1lwdEt98SsVZb+pRuj5aQ4gkGYPr0r1nLI8Hibvnyc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 87250}, "main": "dist/sourcemap-codec.umd.js", "types": "types/sourcemap-codec.d.cts", "module": "dist/sourcemap-codec.mjs", "exports": {".": [{"import": {"types": "./types/sourcemap-codec.d.mts", "default": "./dist/sourcemap-codec.mjs"}, "browser": {"types": "./types/sourcemap-codec.d.cts", "default": "./dist/sourcemap-codec.umd.js"}, "require": {"types": "./types/sourcemap-codec.d.cts", "default": "./dist/sourcemap-codec.umd.js"}}, "./dist/sourcemap-codec.umd.js"], "./package.json": "./package.json"}, "gitHead": "d2578bd71829135bee7f510e62f898bcde97e80a", "scripts": {"lint": "run-s -n lint:types lint:format", "test": "run-s -n test:types test:only test:format", "build": "run-s -n build:code build:types", "clean": "run-s -n clean:code clean:types", "benchmark": "run-s build:code benchmark:*", "test:only": "mocha", "build:code": "node ../../esbuild.mjs sourcemap-codec.ts", "clean:code": "tsc --build --clean tsconfig.build.json", "lint:types": "npm run test:types -- --fix", "test:types": "eslint '{src,test}/**/*.ts'", "build:types": "run-s build:types:force build:types:emit build:types:mts", "clean:types": "rimraf dist types", "lint:format": "npm run test:format -- --write", "test:format": "prettier --check '{src,test}/**/*.ts'", "benchmark:only": "node --expose-gc benchmark/index.js", "prepublishOnly": "npm run-s -n build test", "build:types:mts": "node ../../mts-types.mjs", "build:types:emit": "tsc --project tsconfig.build.json", "benchmark:install": "cd benchmark && npm install", "build:types:force": "rimraf tsconfig.build.tsbuildinfo"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "actor": {"name": "<PERSON><PERSON><PERSON>", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jridgewell/sourcemaps.git", "type": "git", "directory": "packages/sourcemap-codec"}, "_npmVersion": "10.2.3", "description": "Encode/decode sourcemap mappings", "directories": {}, "_nodeVersion": "20.10.0", "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/sourcemap-codec_1.5.3_1751306846972_0.201434557942896", "host": "s3://npm-registry-packages-npm-production"}}, "1.5.4": {"name": "@jridgewell/sourcemap-codec", "version": "1.5.4", "description": "Encode/decode sourcemap mappings", "keywords": ["sourcemap", "vlq"], "main": "dist/sourcemap-codec.umd.js", "module": "dist/sourcemap-codec.mjs", "types": "types/sourcemap-codec.d.cts", "exports": {".": [{"import": {"types": "./types/sourcemap-codec.d.mts", "default": "./dist/sourcemap-codec.mjs"}, "require": {"types": "./types/sourcemap-codec.d.cts", "default": "./dist/sourcemap-codec.umd.js"}, "browser": {"types": "./types/sourcemap-codec.d.cts", "default": "./dist/sourcemap-codec.umd.js"}}, "./dist/sourcemap-codec.umd.js"], "./package.json": "./package.json"}, "scripts": {"benchmark": "run-s build:code benchmark:*", "benchmark:install": "cd benchmark && npm install", "benchmark:only": "node --expose-gc benchmark/index.js", "build": "run-s -n build:code build:types", "build:code": "node ../../esbuild.mjs sourcemap-codec.ts", "build:types": "run-s build:types:force build:types:emit build:types:mts", "build:types:force": "rimraf tsconfig.build.tsbuildinfo", "build:types:emit": "tsc --project tsconfig.build.json", "build:types:mts": "node ../../mts-types.mjs", "clean": "run-s -n clean:code clean:types", "clean:code": "tsc --build --clean tsconfig.build.json", "clean:types": "rimraf dist types", "test": "run-s -n test:types test:only test:format", "test:format": "prettier --check '{src,test}/**/*.ts'", "test:only": "mocha", "test:types": "eslint '{src,test}/**/*.ts'", "lint": "run-s -n lint:types lint:format", "lint:format": "npm run test:format -- --write", "lint:types": "npm run test:types -- --fix", "prepublishOnly": "npm run-s -n build test"}, "homepage": "https://github.com/jridgewell/sourcemaps/tree/main/packages/sourcemap-codec", "repository": {"type": "git", "url": "git+https://github.com/jridgewell/sourcemaps.git", "directory": "packages/sourcemap-codec"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@jridgewell/sourcemap-codec@1.5.4", "gitHead": "3c6286e949d58c2bc6e27334385349c6a83c6d7d", "bugs": {"url": "https://github.com/jridgewell/sourcemaps/issues"}, "_nodeVersion": "20.10.0", "_npmVersion": "10.2.3", "dist": {"integrity": "sha512-VT2+G1VQs/9oz078bLrYbecdZKs912zQlkelYpuf+SXF+QvZDYJlbx/LSx+meSAwdDFnF8FVXW92AVjjkVmgFw==", "shasum": "7358043433b2e5da569aa02cbc4c121da3af27d7", "tarball": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz", "fileCount": 27, "unpackedSize": 86928, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCP9uQgKpwScluxfUpwNDd95bXEuhzMJm+RGJQn6jmRJAIhAOjp1KMeaE2bWuzKOn4xiYiE5jXsYj3uMDZpl5l4Brpr"}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "actor": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/sourcemap-codec_1.5.4_1751393896927_0.7656312829476808"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-02-05T04:51:19.696Z", "modified": "2025-07-01T18:18:17.271Z", "1.4.9": "2022-02-05T04:51:19.912Z", "1.4.10": "2022-02-05T11:34:58.044Z", "1.4.11": "2022-02-11T08:39:40.389Z", "1.4.12": "2022-05-02T21:13:43.832Z", "1.4.13": "2022-05-05T21:38:32.817Z", "1.4.14": "2022-06-26T16:53:35.725Z", "1.4.15": "2023-04-07T02:43:01.666Z", "1.4.16-beta.0": "2024-06-25T09:18:19.950Z", "1.5.0": "2024-07-09T19:28:12.902Z", "1.5.1": "2025-06-29T21:45:57.019Z", "1.5.2": "2025-06-30T05:04:12.051Z", "1.5.3": "2025-06-30T18:07:27.130Z", "1.5.4": "2025-07-01T18:18:17.094Z"}, "bugs": {"url": "https://github.com/jridgewell/sourcemaps/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/jridgewell/sourcemaps/tree/main/packages/sourcemap-codec", "keywords": ["sourcemap", "vlq"], "repository": {"type": "git", "url": "git+https://github.com/jridgewell/sourcemaps.git", "directory": "packages/sourcemap-codec"}, "description": "Encode/decode sourcemap mappings", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "# @jridgewell/sourcemap-codec\n\nEncode/decode the `mappings` property of a [sourcemap](https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k/edit).\n\n\n## Why?\n\nSourcemaps are difficult to generate and manipulate, because the `mappings` property – the part that actually links the generated code back to the original source – is encoded using an obscure method called [Variable-length quantity](https://en.wikipedia.org/wiki/Variable-length_quantity). On top of that, each segment in the mapping contains offsets rather than absolute indices, which means that you can't look at a segment in isolation – you have to understand the whole sourcemap.\n\nThis package makes the process slightly easier.\n\n\n## Installation\n\n```bash\nnpm install @jridgewell/sourcemap-codec\n```\n\n\n## Usage\n\n```js\nimport { encode, decode } from '@jridgewell/sourcemap-codec';\n\nvar decoded = decode( ';EAEEA,EAAE,EAAC,CAAE;ECQY,UACC' );\n\nassert.deepEqual( decoded, [\n\t// the first line (of the generated code) has no mappings,\n\t// as shown by the starting semi-colon (which separates lines)\n\t[],\n\n\t// the second line contains four (comma-separated) segments\n\t[\n\t\t// segments are encoded as you'd expect:\n\t\t// [ generatedCodeColumn, sourceIndex, sourceCodeLine, sourceCodeColumn, nameIndex ]\n\n\t\t// i.e. the first segment begins at column 2, and maps back to the second column\n\t\t// of the second line (both zero-based) of the 0th source, and uses the 0th\n\t\t// name in the `map.names` array\n\t\t[ 2, 0, 2, 2, 0 ],\n\n\t\t// the remaining segments are 4-length rather than 5-length,\n\t\t// because they don't map a name\n\t\t[ 4, 0, 2, 4 ],\n\t\t[ 6, 0, 2, 5 ],\n\t\t[ 7, 0, 2, 7 ]\n\t],\n\n\t// the final line contains two segments\n\t[\n\t\t[ 2, 1, 10, 19 ],\n\t\t[ 12, 1, 11, 20 ]\n\t]\n]);\n\nvar encoded = encode( decoded );\nassert.equal( encoded, ';EAEEA,EAAE,EAAC,CAAE;ECQY,UACC' );\n```\n\n## Benchmarks\n\n```\nnode v20.10.0\n\namp.js.map - 45120 segments\n\nDecode Memory Usage:\nlocal code                             5815135 bytes\n@jridgewell/sourcemap-codec 1.4.15     5868160 bytes\nsourcemap-codec                        5492584 bytes\nsource-map-0.6.1                      13569984 bytes\nsource-map-0.8.0                       6390584 bytes\nchrome dev tools                       8011136 bytes\nSmallest memory usage is sourcemap-codec\n\nDecode speed:\ndecode: local code x 492 ops/sec ±1.22% (90 runs sampled)\ndecode: @jridgewell/sourcemap-codec 1.4.15 x 499 ops/sec ±1.16% (89 runs sampled)\ndecode: sourcemap-codec x 376 ops/sec ±1.66% (89 runs sampled)\ndecode: source-map-0.6.1 x 34.99 ops/sec ±0.94% (48 runs sampled)\ndecode: source-map-0.8.0 x 351 ops/sec ±0.07% (95 runs sampled)\nchrome dev tools x 165 ops/sec ±0.91% (86 runs sampled)\nFastest is decode: @jridgewell/sourcemap-codec 1.4.15\n\nEncode Memory Usage:\nlocal code                              444248 bytes\n@jridgewell/sourcemap-codec 1.4.15      623024 bytes\nsourcemap-codec                        8696280 bytes\nsource-map-0.6.1                       8745176 bytes\nsource-map-0.8.0                       8736624 bytes\nSmallest memory usage is local code\n\nEncode speed:\nencode: local code x 796 ops/sec ±0.11% (97 runs sampled)\nencode: @jridgewell/sourcemap-codec 1.4.15 x 795 ops/sec ±0.25% (98 runs sampled)\nencode: sourcemap-codec x 231 ops/sec ±0.83% (86 runs sampled)\nencode: source-map-0.6.1 x 166 ops/sec ±0.57% (86 runs sampled)\nencode: source-map-0.8.0 x 203 ops/sec ±0.45% (88 runs sampled)\nFastest is encode: local code,encode: @jridgewell/sourcemap-codec 1.4.15\n\n\n***\n\n\nbabel.min.js.map - 347793 segments\n\nDecode Memory Usage:\nlocal code                            35424960 bytes\n@jridgewell/sourcemap-codec 1.4.15    35424696 bytes\nsourcemap-codec                       36033464 bytes\nsource-map-0.6.1                      62253704 bytes\nsource-map-0.8.0                      43843920 bytes\nchrome dev tools                      45111400 bytes\nSmallest memory usage is @jridgewell/sourcemap-codec 1.4.15\n\nDecode speed:\ndecode: local code x 38.18 ops/sec ±5.44% (52 runs sampled)\ndecode: @jridgewell/sourcemap-codec 1.4.15 x 38.36 ops/sec ±5.02% (52 runs sampled)\ndecode: sourcemap-codec x 34.05 ops/sec ±4.45% (47 runs sampled)\ndecode: source-map-0.6.1 x 4.31 ops/sec ±2.76% (15 runs sampled)\ndecode: source-map-0.8.0 x 55.60 ops/sec ±0.13% (73 runs sampled)\nchrome dev tools x 16.94 ops/sec ±3.78% (46 runs sampled)\nFastest is decode: source-map-0.8.0\n\nEncode Memory Usage:\nlocal code                             2606016 bytes\n@jridgewell/sourcemap-codec 1.4.15     2626440 bytes\nsourcemap-codec                       21152576 bytes\nsource-map-0.6.1                      25023928 bytes\nsource-map-0.8.0                      25256448 bytes\nSmallest memory usage is local code\n\nEncode speed:\nencode: local code x 127 ops/sec ±0.18% (83 runs sampled)\nencode: @jridgewell/sourcemap-codec 1.4.15 x 128 ops/sec ±0.26% (83 runs sampled)\nencode: sourcemap-codec x 29.31 ops/sec ±2.55% (53 runs sampled)\nencode: source-map-0.6.1 x 18.85 ops/sec ±3.19% (36 runs sampled)\nencode: source-map-0.8.0 x 19.34 ops/sec ±1.97% (36 runs sampled)\nFastest is encode: @jridgewell/sourcemap-codec 1.4.15\n\n\n***\n\n\npreact.js.map - 1992 segments\n\nDecode Memory Usage:\nlocal code                              261696 bytes\n@jridgewell/sourcemap-codec 1.4.15      244296 bytes\nsourcemap-codec                         302816 bytes\nsource-map-0.6.1                        939176 bytes\nsource-map-0.8.0                           336 bytes\nchrome dev tools                        587368 bytes\nSmallest memory usage is source-map-0.8.0\n\nDecode speed:\ndecode: local code x 17,782 ops/sec ±0.32% (97 runs sampled)\ndecode: @jridgewell/sourcemap-codec 1.4.15 x 17,863 ops/sec ±0.40% (100 runs sampled)\ndecode: sourcemap-codec x 12,453 ops/sec ±0.27% (101 runs sampled)\ndecode: source-map-0.6.1 x 1,288 ops/sec ±1.05% (96 runs sampled)\ndecode: source-map-0.8.0 x 9,289 ops/sec ±0.27% (101 runs sampled)\nchrome dev tools x 4,769 ops/sec ±0.18% (100 runs sampled)\nFastest is decode: @jridgewell/sourcemap-codec 1.4.15\n\nEncode Memory Usage:\nlocal code                              262944 bytes\n@jridgewell/sourcemap-codec 1.4.15       25544 bytes\nsourcemap-codec                         323048 bytes\nsource-map-0.6.1                        507808 bytes\nsource-map-0.8.0                        507480 bytes\nSmallest memory usage is @jridgewell/sourcemap-codec 1.4.15\n\nEncode speed:\nencode: local code x 24,207 ops/sec ±0.79% (95 runs sampled)\nencode: @jridgewell/sourcemap-codec 1.4.15 x 24,288 ops/sec ±0.48% (96 runs sampled)\nencode: sourcemap-codec x 6,761 ops/sec ±0.21% (100 runs sampled)\nencode: source-map-0.6.1 x 5,374 ops/sec ±0.17% (99 runs sampled)\nencode: source-map-0.8.0 x 5,633 ops/sec ±0.32% (99 runs sampled)\nFastest is encode: @jridgewell/sourcemap-codec 1.4.15,encode: local code\n\n\n***\n\n\nreact.js.map - 5726 segments\n\nDecode Memory Usage:\nlocal code                              678816 bytes\n@jridgewell/sourcemap-codec 1.4.15      678816 bytes\nsourcemap-codec                         816400 bytes\nsource-map-0.6.1                       2288864 bytes\nsource-map-0.8.0                        721360 bytes\nchrome dev tools                       1012512 bytes\nSmallest memory usage is local code\n\nDecode speed:\ndecode: local code x 6,178 ops/sec ±0.19% (98 runs sampled)\ndecode: @jridgewell/sourcemap-codec 1.4.15 x 6,261 ops/sec ±0.22% (100 runs sampled)\ndecode: sourcemap-codec x 4,472 ops/sec ±0.90% (99 runs sampled)\ndecode: source-map-0.6.1 x 449 ops/sec ±0.31% (95 runs sampled)\ndecode: source-map-0.8.0 x 3,219 ops/sec ±0.13% (100 runs sampled)\nchrome dev tools x 1,743 ops/sec ±0.20% (99 runs sampled)\nFastest is decode: @jridgewell/sourcemap-codec 1.4.15\n\nEncode Memory Usage:\nlocal code                              140960 bytes\n@jridgewell/sourcemap-codec 1.4.15      159808 bytes\nsourcemap-codec                         969304 bytes\nsource-map-0.6.1                        930520 bytes\nsource-map-0.8.0                        930248 bytes\nSmallest memory usage is local code\n\nEncode speed:\nencode: local code x 8,013 ops/sec ±0.19% (100 runs sampled)\nencode: @jridgewell/sourcemap-codec 1.4.15 x 7,989 ops/sec ±0.20% (101 runs sampled)\nencode: sourcemap-codec x 2,472 ops/sec ±0.21% (99 runs sampled)\nencode: source-map-0.6.1 x 2,200 ops/sec ±0.17% (99 runs sampled)\nencode: source-map-0.8.0 x 2,220 ops/sec ±0.37% (99 runs sampled)\nFastest is encode: local code\n\n\n***\n\n\nvscode.map - 2141001 segments\n\nDecode Memory Usage:\nlocal code                           198955264 bytes\n@jridgewell/sourcemap-codec 1.4.15   199175352 bytes\nsourcemap-codec                      199102688 bytes\nsource-map-0.6.1                     386323432 bytes\nsource-map-0.8.0                     244116432 bytes\nchrome dev tools                     293734280 bytes\nSmallest memory usage is local code\n\nDecode speed:\ndecode: local code x 3.90 ops/sec ±22.21% (15 runs sampled)\ndecode: @jridgewell/sourcemap-codec 1.4.15 x 3.95 ops/sec ±23.53% (15 runs sampled)\ndecode: sourcemap-codec x 3.82 ops/sec ±17.94% (14 runs sampled)\ndecode: source-map-0.6.1 x 0.61 ops/sec ±7.81% (6 runs sampled)\ndecode: source-map-0.8.0 x 9.54 ops/sec ±0.28% (28 runs sampled)\nchrome dev tools x 2.18 ops/sec ±10.58% (10 runs sampled)\nFastest is decode: source-map-0.8.0\n\nEncode Memory Usage:\nlocal code                            13509880 bytes\n@jridgewell/sourcemap-codec 1.4.15    13537648 bytes\nsourcemap-codec                       32540104 bytes\nsource-map-0.6.1                     127531040 bytes\nsource-map-0.8.0                     127535312 bytes\nSmallest memory usage is local code\n\nEncode speed:\nencode: local code x 20.10 ops/sec ±0.19% (38 runs sampled)\nencode: @jridgewell/sourcemap-codec 1.4.15 x 20.26 ops/sec ±0.32% (38 runs sampled)\nencode: sourcemap-codec x 5.44 ops/sec ±1.64% (18 runs sampled)\nencode: source-map-0.6.1 x 2.30 ops/sec ±4.79% (10 runs sampled)\nencode: source-map-0.8.0 x 2.46 ops/sec ±6.53% (10 runs sampled)\nFastest is encode: @jridgewell/sourcemap-codec 1.4.15\n```\n\n# License\n\nMIT\n", "readmeFilename": "README.md"}