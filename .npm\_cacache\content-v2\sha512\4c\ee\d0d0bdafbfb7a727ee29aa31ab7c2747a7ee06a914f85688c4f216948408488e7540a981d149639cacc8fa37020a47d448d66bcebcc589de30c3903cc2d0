{"_id": "strip-json-comments", "_rev": "57-5257bb28b18c9edf3abf87b89a1a52d1", "name": "strip-json-comments", "dist-tags": {"latest": "5.0.2"}, "versions": {"0.1.0": {"name": "strip-json-comments", "version": "0.1.0", "keywords": ["json", "strip", "remove", "delete", "trim", "comments", "multiline", "parse", "config", "configuration", "conf", "settings", "util", "env", "environment"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "strip-json-comments@0.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/strip-json-comments", "bugs": {"url": "https://github.com/sindresorhus/strip-json-comments/issues"}, "bin": {"strip-json-comments": "cli.js"}, "dist": {"shasum": "ec01ab67f10b79258dcbcaa03ba82485acb589d9", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-0.1.0.tgz", "integrity": "sha512-QFN1P8dzwpB1nC6jQnCnjptXM/AO9DGps7c6HFieDIf0NxShzy4C89/pASyH4ZeYFOo2psQ67HI0iL4b2875zA==", "signatures": [{"sig": "MEYCIQCCWwetYF/Tg683CPuswtNEulOUukPRDfCIvc3BhDiD6gIhAJor94Xvq7+BsHVz7dJPQT5QSOwGfaOgSpd6oXLm1oev", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "strip-json-comments", "_from": ".", "files": ["cli.js", "strip-json-comments.js"], "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/strip-json-comments", "type": "git"}, "_npmVersion": "1.3.14", "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "directories": {}, "devDependencies": {"mocha": "~1.14.0"}}, "0.1.1": {"name": "strip-json-comments", "version": "0.1.1", "keywords": ["json", "strip", "remove", "delete", "trim", "comments", "multiline", "parse", "config", "configuration", "conf", "settings", "util", "env", "environment"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "strip-json-comments@0.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/strip-json-comments", "bugs": {"url": "https://github.com/sindresorhus/strip-json-comments/issues"}, "bin": {"strip-json-comments": "cli.js"}, "dist": {"shasum": "eb5a750bd4e8dc82817295a115dc11b63f01d4b0", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-0.1.1.tgz", "integrity": "sha512-M60kC/sJQwhrFzgS9r/X011ikc7xsvnmXC/h3m8a0UF1NJBPoj89NoJpernXhJqAjN6sLfGllU74qym9fSHaRA==", "signatures": [{"sig": "MEQCIGb05Av4UhjnUgH1VqUJRYvzz2NgDEWa3iiUvRE9cFlbAiBhOabz1OwlVCly7E5tz7SxUhCir3l/Pwc44f42Tw4HiA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "strip-json-comments", "_from": ".", "files": ["cli.js", "strip-json-comments.js"], "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/strip-json-comments", "type": "git"}, "_npmVersion": "1.3.14", "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "directories": {}, "devDependencies": {"mocha": "~1.14.0"}}, "0.1.2": {"name": "strip-json-comments", "version": "0.1.2", "keywords": ["json", "strip", "remove", "delete", "trim", "comments", "multiline", "parse", "config", "configuration", "conf", "settings", "util", "env", "environment", "cli", "bin"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "strip-json-comments@0.1.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/strip-json-comments", "bugs": {"url": "https://github.com/sindresorhus/strip-json-comments/issues"}, "bin": {"strip-json-comments": "cli.js"}, "dist": {"shasum": "a5f9cc48c488c9b8cf1303f880cd0959923a9ee1", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-0.1.2.tgz", "integrity": "sha512-rSiJWCy2RKuHrt8/VLwtNn7ksshM53wosSSJI5zWSgC3YuN9BBf1FuYn0f/y/QeRsxnWmhNzgbRV9FgXwiVdIA==", "signatures": [{"sig": "MEUCIQC2Y51Nw8edA4yYwP+Kwnw9LYOo579S5IePqO0R632i4gIgcOtpeTDUzn5eG0WGh0Dgbldmd0QPoepgO+xtyu19DQA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "strip-json-comments", "_from": ".", "files": ["cli.js", "strip-json-comments.js"], "engines": {"node": ">=0.8.0"}, "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/strip-json-comments", "type": "git"}, "_npmVersion": "1.4.6", "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "directories": {}, "devDependencies": {"mocha": "*"}}, "0.1.3": {"name": "strip-json-comments", "version": "0.1.3", "keywords": ["json", "strip", "remove", "delete", "trim", "comments", "multiline", "parse", "config", "configuration", "conf", "settings", "util", "env", "environment", "cli", "bin"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "strip-json-comments@0.1.3", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/strip-json-comments", "bugs": {"url": "https://github.com/sindresorhus/strip-json-comments/issues"}, "bin": {"strip-json-comments": "cli.js"}, "dist": {"shasum": "164c64e370a8a3cc00c9e01b539e569823f0ee54", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-0.1.3.tgz", "integrity": "sha512-d2RPtrkLs8TurFFAIhW8IdM0+cOq+QFETWBGKHO+93eZ4Zt4P1CeJB5LHKW4EfEwabEpPL8/UTO3QX94+lqxwQ==", "signatures": [{"sig": "MEUCIQD5kPjpLpeviZt/WIjkVmmC2VvnLK5cDs2etXJS4uWIQgIgf9YcWtb8pwOGMLynk8hWC8XqPUXFpg5TpBNzT9JLGvg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "strip-json-comments", "_from": ".", "files": ["cli.js", "strip-json-comments.js"], "_shasum": "164c64e370a8a3cc00c9e01b539e569823f0ee54", "engines": {"node": ">=0.8.0"}, "gitHead": "cbd5aede7ccbe5d5a9065b1d47070fd99ad579af", "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/strip-json-comments", "type": "git"}, "_npmVersion": "1.4.13", "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "directories": {}, "devDependencies": {"mocha": "*"}}, "1.0.0": {"name": "strip-json-comments", "version": "1.0.0", "keywords": ["json", "strip", "remove", "delete", "trim", "comments", "multiline", "parse", "config", "configuration", "conf", "settings", "util", "env", "environment", "cli", "bin"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "strip-json-comments@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/strip-json-comments", "bugs": {"url": "https://github.com/sindresorhus/strip-json-comments/issues"}, "bin": {"strip-json-comments": "cli.js"}, "dist": {"shasum": "5e284febd8826287c8266f224ed2b1fdc7220e9f", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-1.0.0.tgz", "integrity": "sha512-XnFRxmff7W8ynHhw5B2uti0LAqXVm5K+7mFr65/3EKAw2j9tFev2FmgBfctiEXYQKnniY7x4mgkHnoaEk8kTrw==", "signatures": [{"sig": "MEYCIQDtyavtBsnqvUYfsEWjkDK9vxFxHzci/pXP8JhYkQ4qIwIhAPR4cX30FumVe62DqLU6aHNtusADqQl875QyIVq+e1d5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "strip-json-comments", "_from": ".", "files": ["cli.js", "strip-json-comments.js"], "_shasum": "5e284febd8826287c8266f224ed2b1fdc7220e9f", "engines": {"node": ">=0.8.0"}, "gitHead": "321b0d02b9e511271cd28e5c2133e96d3bb357c7", "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/strip-json-comments", "type": "git"}, "_npmVersion": "1.4.14", "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "directories": {}, "devDependencies": {"mocha": "*"}}, "1.0.1": {"name": "strip-json-comments", "version": "1.0.1", "keywords": ["json", "strip", "remove", "delete", "trim", "comments", "multiline", "parse", "config", "configuration", "conf", "settings", "util", "env", "environment", "cli", "bin"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "strip-json-comments@1.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/strip-json-comments", "bugs": {"url": "https://github.com/sindresorhus/strip-json-comments/issues"}, "bin": {"strip-json-comments": "cli.js"}, "dist": {"shasum": "a91252d95ddee4ff38a66135cd6c78de5709f374", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-1.0.1.tgz", "integrity": "sha512-rAMmGzKuo6VWsB6mHVUa7DHUbUxmddSv3HZ7GZ23qEl4XqFsJvbpD7xabc6LEW1Fa3gGi+L3ayWTAnYAnfWDaQ==", "signatures": [{"sig": "MEUCIGmc6udJHiglZX22s3tpb9mFuMuazrNIu1zKvXts8mo1AiEA+BB9Uoip06WK+3Hgs91UjEmOImaoBL1ivN3yXEvsDwQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "strip-json-comments", "_from": ".", "files": ["cli.js", "strip-json-comments.js"], "_shasum": "a91252d95ddee4ff38a66135cd6c78de5709f374", "engines": {"node": ">=0.8.0"}, "gitHead": "69a1a17b2ef36056492c5223e7cf910c496b4024", "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/sindresorhus/strip-json-comments", "type": "git"}, "_npmVersion": "1.4.14", "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "directories": {}, "devDependencies": {"mocha": "*"}}, "1.0.2": {"name": "strip-json-comments", "version": "1.0.2", "keywords": ["json", "strip", "remove", "delete", "trim", "comments", "multiline", "parse", "config", "configuration", "conf", "settings", "util", "env", "environment", "cli", "bin"], "author": {"url": "http://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "strip-json-comments@1.0.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/strip-json-comments", "bugs": {"url": "https://github.com/sindresorhus/strip-json-comments/issues"}, "bin": {"strip-json-comments": "cli.js"}, "dist": {"shasum": "5a48ab96023dbac1b7b8d0ffabf6f63f1677be9f", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-1.0.2.tgz", "integrity": "sha512-zRzQSDu8YVDoLt1cVFfznCS6Y0wxWphJiG6+2d9YLZQJKooieRoVa518RFGK/P7m9ZsNyDEiknOJkdPQu7exVw==", "signatures": [{"sig": "MEYCIQDNRiMEUhXbihlUDLU7izwDJNV6YRpMb2mCa7MVG2YXUAIhAOyk/oU5PGkf71yBwJfqHojSTr5SAu1j+uYggafmx1CM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "strip-json-comments", "_from": ".", "files": ["cli.js", "strip-json-comments.js"], "_shasum": "5a48ab96023dbac1b7b8d0ffabf6f63f1677be9f", "engines": {"node": ">=0.8.0"}, "gitHead": "142dd671c71f90fb7fdba440184b1bb64543acb3", "scripts": {"test": "mocha"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/strip-json-comments", "type": "git"}, "_npmVersion": "2.1.2", "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "directories": {}, "_nodeVersion": "0.10.32", "devDependencies": {"mocha": "*"}}, "1.0.3": {"name": "strip-json-comments", "version": "1.0.3", "keywords": ["json", "strip", "remove", "delete", "trim", "comments", "multiline", "parse", "config", "configuration", "conf", "settings", "util", "env", "environment", "cli", "bin"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "strip-json-comments@1.0.3", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/strip-json-comments", "bugs": {"url": "https://github.com/sindresorhus/strip-json-comments/issues"}, "bin": {"strip-json-comments": "cli.js"}, "dist": {"shasum": "c250d2646a24fc5603e27043005dd97dfb409fc4", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-1.0.3.tgz", "integrity": "sha512-S6nXPW6pGGX6okQDMaxGLyXhbwsrmYp8GF0IrPomr1rjURI2QtnGQrg6Kc5zlk03f9dIJCaKIkjdM4L/+qLqUQ==", "signatures": [{"sig": "MEUCIQDIJJ+CMvq/o1+o6jq2pIW1DMgUlK3oc3wD8O9mAR/h4gIgPuHP9RgfiksVbp8qjd161/QUQeRsYTKqu8IOY6bG524=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "strip-json-comments", "_from": ".", "files": ["cli.js", "strip-json-comments.js"], "_shasum": "c250d2646a24fc5603e27043005dd97dfb409fc4", "engines": {"node": ">=0.8.0"}, "gitHead": "11420df0403854fb0cdb303edce3564322b5af45", "scripts": {"test": "mocha --ui tdd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/strip-json-comments", "type": "git"}, "_npmVersion": "2.11.2", "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "directories": {}, "_nodeVersion": "0.12.5", "devDependencies": {"mocha": "*"}}, "1.0.4": {"name": "strip-json-comments", "version": "1.0.4", "keywords": ["json", "strip", "remove", "delete", "trim", "comments", "multiline", "parse", "config", "configuration", "conf", "settings", "util", "env", "environment", "cli", "bin"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "strip-json-comments@1.0.4", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/strip-json-comments", "bugs": {"url": "https://github.com/sindresorhus/strip-json-comments/issues"}, "bin": {"strip-json-comments": "cli.js"}, "dist": {"shasum": "1e15fbcac97d3ee99bf2d73b4c656b082bbafb91", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-1.0.4.tgz", "integrity": "sha512-AOPG8EBc5wAikaG1/7uFCNFJwnKOuQwFTpYBdTW6OvWHeZBQBrAA/amefHGrEiOnCPcLFZK6FUPtWVKpQVIRgg==", "signatures": [{"sig": "MEUCID1wVLqluPc7gzNB16J8OlOmTdolhl6l7GEpq1NvdpicAiEAvCKLoWcdHyDj5uC3I7jXBy0dMx66OgItygHn2XMdFO4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "strip-json-comments", "_from": ".", "files": ["cli.js", "strip-json-comments.js"], "_shasum": "1e15fbcac97d3ee99bf2d73b4c656b082bbafb91", "engines": {"node": ">=0.8.0"}, "gitHead": "f58348696368583cc5bb18525fe31eacc9bd00e1", "scripts": {"test": "mocha --ui tdd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/strip-json-comments", "type": "git"}, "_npmVersion": "2.11.2", "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "directories": {}, "_nodeVersion": "0.12.5", "devDependencies": {"mocha": "*"}}, "2.0.0": {"name": "strip-json-comments", "version": "2.0.0", "keywords": ["json", "strip", "remove", "delete", "trim", "comments", "multiline", "parse", "config", "configuration", "conf", "settings", "util", "env", "environment"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "strip-json-comments@2.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/strip-json-comments", "bugs": {"url": "https://github.com/sindresorhus/strip-json-comments/issues"}, "dist": {"shasum": "413fc5a34ef34a29e4376c7eb632c884d2e97f95", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.0.tgz", "integrity": "sha512-Dt1tiQ53ogvscvhfuFzRzD245iTm+eS7BMicLBzmZDTm05kB56Y0pfmU9bTZH8+sITZXukohx7nElw91WmtlRQ==", "signatures": [{"sig": "MEUCIBvQhES4zVt6VcjIox4c/vj1FuFA83ydiECcvYAdnEZRAiEAzVXEo2rtMPBf1JEWaehV//57FXFcjWzCoNvFx1EWU4o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "413fc5a34ef34a29e4376c7eb632c884d2e97f95", "engines": {"node": ">=0.10.0"}, "gitHead": "e79a73bfc184e6c8d3a0ebf07f164b3d2ddb5519", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/strip-json-comments", "type": "git"}, "_npmVersion": "2.14.7", "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "directories": {}, "_nodeVersion": "4.2.1", "devDependencies": {"xo": "*", "ava": "*"}}, "2.0.1": {"name": "strip-json-comments", "version": "2.0.1", "keywords": ["json", "strip", "remove", "delete", "trim", "comments", "multiline", "parse", "config", "configuration", "conf", "settings", "util", "env", "environment"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "strip-json-comments@2.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/strip-json-comments#readme", "bugs": {"url": "https://github.com/sindresorhus/strip-json-comments/issues"}, "dist": {"shasum": "3c531942e908c2697c0ec344858c286c7ca0a60a", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz", "integrity": "sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ==", "signatures": [{"sig": "MEQCH0CCCa3qkaq75YUmE7ZN4C+dLkGr4SEbkjI08ACExooCIQCav4TQvZefgBbxcpxeHODw/cEHgJF7pts4shhMhPO3Hg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "3c531942e908c2697c0ec344858c286c7ca0a60a", "engines": {"node": ">=0.10.0"}, "gitHead": "1aef99eaa70d07981156e8aaa722e750c3b4eaf9", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/strip-json-comments.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "directories": {}, "_nodeVersion": "4.2.4", "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/strip-json-comments-2.0.1.tgz_1455006605207_0.8280157081317157", "host": "packages-9-west.internal.npmjs.com"}}, "3.0.0": {"name": "strip-json-comments", "version": "3.0.0", "keywords": ["json", "strip", "comments", "remove", "delete", "trim", "multiline", "parse", "config", "configuration", "settings", "util", "env", "environment"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "strip-json-comments@3.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/strip-json-comments#readme", "bugs": {"url": "https://github.com/sindresorhus/strip-json-comments/issues"}, "dist": {"shasum": "86456f81e4bd10d314576c1adf7719ed0894ce3b", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-+yvKOYsd+oXGSoNMD0PgjwWn4MrgXTK/t/Fh54NXLf1cOOf3ceP/JvP5JRDP8jeKBsmtXS9PeoOr2eh1RKWSqg==", "signatures": [{"sig": "MEQCIDf7Z7koNzcqdPIVs1eyGvckSW02BZjqORoKrOI9l1TKAiARJCtjhYaqx4nmqXxalploS/gTQquucM2U02+WhyXkDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyD4qCRA9TVsSAnZWagAA2R8QAJUeXxjRJnF3A6Lz9ipv\nH+q/1iMewZ5bU1BgtPLNaF6e/r5H36/N3TWsYZ1btNtkSBNiOSjoy7Jc/yp1\nkUOoA89QwE5z8TER7V4rHwksFezpBuxjcBuDxMpSSSatrheI63nfqhza+LiT\nxIUbbTc6JVZGf9tzWmDrsNqS7m+cuVljTHG+dBPfGZx8iCmHVy2gBz/98DuX\nRNIp4HjfibeY79DAMRf6jV7+Jtp2xG0d/L7cMUQN0TGOdrWKNt3vIcuRh/Ub\nEQ2quEi2NXF2bguPQ+O+w6/VzaILxrL7+GmapeZak25d/p7hG0MQqi6+rFC3\n9pZmcQH8UUEqvKpeBTe+aRg7bzSQ9qIK9Tf3OTBc3mp/dxS6IsXzjT9vs7NL\nABYXaBQfXVaYE1uAzz9x7dniHdHXmfvCJ3H0SORubQW2P+lXzjm8V4dhLi7U\nK6TThhODPodo88M9QliOHZbTge1s44dFHDyMRpxCXy5mj+fd/603GuMPKsuT\njkECgdhSttUyJ3Kw2fZ1vV/kos1w+OgBq3mkVDVZXSoFTo0jBZg8CSOZK6Xh\n7khUgJ7sLSlvwTMdCfo2nKYX+cVgnUq+nODouNmIG9fPhaaW3cMRByz3AjE0\nmVYFVGzMTlIanZERd97CgQOVUxZlSICaJJnDGwNGX/5NL5zxek55UJ5NuGZH\nwU5k\r\n=Hpj3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "5f5fdd3db64cdbe8cd940a240fbb52a634f3a145", "scripts": {"test": "xo && ava && tsd", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/strip-json-comments.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "directories": {}, "_nodeVersion": "10.15.3", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2", "matcha": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/strip-json-comments_3.0.0_1556626985690_0.4353346723662954", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "strip-json-comments", "version": "3.0.1", "keywords": ["json", "strip", "comments", "remove", "delete", "trim", "multiline", "parse", "config", "configuration", "settings", "util", "env", "environment"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "strip-json-comments@3.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/strip-json-comments#readme", "bugs": {"url": "https://github.com/sindresorhus/strip-json-comments/issues"}, "dist": {"shasum": "85713975a91fb87bf1b305cca77395e40d2a64a7", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.0.1.tgz", "fileCount": 5, "integrity": "sha512-VTyMAUfdm047mwKl+u79WIdrZxtFtn+nBxHeb844XBQ9uMNTuTHdx2hc5RiAJYqwTj3wc/xe5HLSdJSkJ+WfZw==", "signatures": [{"sig": "MEUCIDeLmpK7GNq2pj0npyXcauTPv6+IE+wzOLhPOTjWlqFwAiEAz3stw4y4ajY9M+qAHUTzOqQv0DtHN84oIevtpzvyd/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyIG3CRA9TVsSAnZWagAAoFkP/2Fg4JABehZLiTFnJiUw\n5ToGpDbTz2vg7WeG8U78uyTYEVQUfnEBnu/kpi9jzM+RWGR1JS/DBvq300kf\n2baOxBKgprjew2H+rMnuPj3eBJ0OXP+hNnzWxHQnlwmBDDHA/0CjnKdxD75z\nJV3X2KtIX/0/oK+c9rgVvcc+TUwAWMnu8J2HraEVQnUCXH0c/9foWBvKkbai\nzLyirUpRon01FzXgx7U/CII49KPFIUVALX+ME8PuAqUP4/iJQDbp8tIXSMeV\nCgcctZv26GgLXV9gyrKN+cRE4hcs2an8Ex0TVJigbd1V6mJWwsu2I8koVxSv\nPBGBDxw7jGaAad7uC0sAbp6q4kUC9Vo3WsRrL70khnfqcINhYDF9oc8xhNj5\nbAIYtp2oe3KR9vmNmKmv/wX9OhlT0fRJFmaNvRL+N0KqVQW0xYzdK+hoyCJe\nEq27Ng+ke/IXerDVv5Z9zaFDxb7Z5h+mC5FCSjlcHYb+eiESRfNzXUQuRVa5\n9Miedd+tcsoWe9EVts715PtArAlUxyy2I4qvdjERIYpG4LPnX0Rjcj6ZreI3\nE97wwEenIIVHhN8HSrCPRaI4FK9kmE3jsm1qc3T5Yy0meBrjiLTp3/6mmI8N\n0XV84neY0vC/0arYRytSZUBITysBU3KWSWzMGwMqf2IioV3hGtwySzDcylRH\n5rpw\r\n=e0gI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "7eb8be0decd8111ffcc21d2e9deec917657e220f", "scripts": {"test": "xo && ava && tsd", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/strip-json-comments.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "directories": {}, "_nodeVersion": "8.16.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2", "matcha": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/strip-json-comments_3.0.1_1556644278971_0.0530254014196585", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "strip-json-comments", "version": "3.1.0", "keywords": ["json", "strip", "comments", "remove", "delete", "trim", "multiline", "parse", "config", "configuration", "settings", "util", "env", "environment"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "strip-json-comments@3.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/strip-json-comments#readme", "bugs": {"url": "https://github.com/sindresorhus/strip-json-comments/issues"}, "dist": {"shasum": "7638d31422129ecf4457440009fba03f9f9ac180", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.0.tgz", "fileCount": 5, "integrity": "sha512-e6/d0eBu7gHtdCqFt0xJr642LdToM5/cN4Qb9DbHjVx1CP5RyeM+zH7pbecEmDv/lBqb0QH+6Uqq75rxFPkM0w==", "signatures": [{"sig": "MEUCIB5cYYsPmNk7rEOtf85v4UCZnE226GAzpmC06V7B5wIkAiEAhYhks/kSsyTMQ6AWSKAQ0/bptQ5dXZPfLSkd+MRGomw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6878, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeiuE4CRA9TVsSAnZWagAAyacP/0muJ+pdHtzbxZVVwJWo\npgWHr1x+KEIY5H7ng1Nj3FfPGYjkwftLig8zQhInGuseV9iwFJf8Vj3pRLRQ\na0xNU+H8y6R1qy/awHHtSyAQz7T0tu9nSWUN9w8rsf7x0yLqsVKrl31e0WmQ\nirfvXPxZBH80ts7yaKCJoiCWi4bkfpqMG2HCnPW0MGxFZIZ0zErPK2uN+hOt\nHS80PkJJX4WMSaZVLJi2MyzopVryh7R+Rui14++H9Vi72g6cdnxqCRiTl866\n23MGLEeKRXC5Zuob17ZjXlRzPe/s3gP72cct1DLCMExN0A/dLDrsONFhUok6\niVVqNyx0P5/jWv2ziu/l/rZSu0RTX1qasTGS6+jIOwnKmx2IUNDNAeZMlQmy\nEiYbhlQ/Ss39MG5nIV6wqraCwY8Wcd1GAuaeBrjWnQ4xoaF/IVW9+dko0IiG\nY4kJM4CIpybyF918G19J2Ul7fEsFUn7Gg9LJiSEgHaAqypNNkIiOKEaRsO2m\nEq1f+Hqh5NtFwOQ9s7REHex7si1S6x3PyuMiVG5xl+59x8L2OeGj0WCllf5I\ni5csgxHOto/fCwmc4QjPDE94bMvyjnupqiFZD6A99G8DTRDKg/Yqvjhzv5Z4\nw2byr2Nwfn6pdTHWR7vnWU4BmXUlQKY8JBHlTFrvgHm1VeEyond1G+3BFZAO\nRGQ5\r\n=ycK0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "159140d19a5c2ec08a9895ca255c1f1699915e66", "scripts": {"test": "xo && ava && tsd", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/strip-json-comments.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "directories": {}, "_nodeVersion": "10.19.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2", "matcha": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/strip-json-comments_3.1.0_1586159927646_0.07435095027153582", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "strip-json-comments", "version": "3.1.1", "keywords": ["json", "strip", "comments", "remove", "delete", "trim", "multiline", "parse", "config", "configuration", "settings", "util", "env", "environment", "jsonc"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "strip-json-comments@3.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/strip-json-comments#readme", "bugs": {"url": "https://github.com/sindresorhus/strip-json-comments/issues"}, "dist": {"shasum": "31f1281b3832630434831c310c01cccda8cbe006", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "fileCount": 5, "integrity": "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==", "signatures": [{"sig": "MEUCIQDEFfhulC6y1p9gNW/jxefxWYUggxPZSjv94NI8zWE/ewIgUZDs7mGYPrXV44hHUWraHUlY1mZYV3iPANgcELxoggg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCyHoCRA9TVsSAnZWagAA2l4P/igAOF4E3r0qoWCs4i37\npaZvhlr1axuDaxKmyT15bAp2TGYcIl0TAGm6+uubqlKU2YW8wAM2h94YzWNT\nXOHyml2tvKA/MtAQylUMXekaY+GEAoDVCyFRGHnD6lZpxcmPOAtcnEKBk4NO\n53O8kl/+LdnvfMwx500L2bFRPZWESIjVLWlw6YIUTOTEgYlOvy4kz3gC3ajG\nUH4ZP2nOKe0v5QOKPsZEXnOjTka97jHowxICgO+v14Z0OrduCOwPzyzpX635\nT7TGPRyKpe5atz1J/p5aCMNPEoQl3F6A1NEglefVINDsOZvV/DPItEsEr53K\nVhuYh8JtZ0WZ4TT1Zj7rrq/kNU0wkqdhwfYHofXqRdWxwIbUvK54E0dAZpaJ\nLBGysB4/fSHSlpHIJTNiNOxlBmGk6dY3ammlyjnO9qeVFbk3i+lH5cYH1/1M\ne3lRFpYvGklgYRPDRZi6UOfJ6715HYRwWm4twWC/ytvCLWxa3QNDNGBXG+Gy\nkfbym+fsXNMkcTweVf26+tBQt0vY8PkeA0ahY3GmUxD/0nNidtZtIFlfNaeZ\npR4JBABun7xbjQtU2tQyEs6NQJz6SK5rBc70HXHB2/cFTPimZWFX3JgGOykZ\n+x4j/E6DceZkAVt4qutuUt3Tv4QrF3EgOGW/jq9JJkfOKfCHpyLDG9VwDMzo\nxCZG\r\n=XcoI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "6355b5792682c7c7a2480e5cbe73fc7ad578e432", "scripts": {"test": "xo && ava && tsd", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/strip-json-comments.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "directories": {}, "_nodeVersion": "14.5.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2", "matcha": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/strip-json-comments_3.1.1_1594565096474_0.8923371545868197", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "strip-json-comments", "version": "4.0.0", "keywords": ["json", "strip", "comments", "remove", "delete", "trim", "multiline", "parse", "config", "configuration", "settings", "util", "env", "environment", "jsonc"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "strip-json-comments@4.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/strip-json-comments#readme", "bugs": {"url": "https://github.com/sindresorhus/strip-json-comments/issues"}, "dist": {"shasum": "6fd3a79f1b956905483769b0bf66598b8f87da50", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-LzWcbfMbAsEDTRmhjWIioe8GcDRl0fa35YMXFoJKDdiD/quGFmjJjdgPjFJJNwCMaLyQqFIDqCdHD2V4HfLgYA==", "signatures": [{"sig": "MEUCIQCq/nwqSCvAaccFwocVlmbhpslvsb21DZCPS2IX5/om1wIgPmrXA2umKwQWdTkxg7Auz/ZJafuGLWto168OQ8Od+TQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhHp6ICRA9TVsSAnZWagAAbFAP/19k3xc4mnD1TJy1na5a\nXc0mFghpbOzKD+R/Vz7JdLfKj2pTcmTqa1cy33qPxcpLwDwiwuFJhmYDP5gm\nJjebjGETZlv/hjWZrVlhet1D3Z5WUiRPI+qL4Wusbc6unnV7d7cIjzuRdpyD\nbaA+fYJDmtNdRU4NhMoIxKZwW+XzSsJT1aWAkb7wzJS1MG+4Qm3KCD8iUCKg\ndDrBzI2mzOx2V21lmwQl4iK5ieTHPun8Ajf+BbZpfJg4v7wvrUNMtyDmKRQC\nvN08m317YiGlPbOxwV1cpWBtZYyxw6PEuaMEkkVEO5XK3q4H+HhsHRFOTIWO\nIOeD4LUQrzRS3qo8fjW8TuzLwwdk416idTkRt44Sd7loocJoqd3w7rfwjQ7C\nD6r0r7fqSCKwur8PxYy7ultCE2MplVB9mSLcsnvV2m1Cef+kHvAex/dqcL0+\nvvJMvkOzXSOGZursVPWKKdZcdAecj+7B9X8LuChc6sR7ZDEmVhyXoTqIPkcD\nF4Z2pjEj6opsIUQUh7B+DDkgNRrb82br7/vLmQLM8adZVugWBJbN1ULfqJKI\nDLHV2pDVYHaW2EVYQVbEdBXDh9UaVX29xFliU0+vkTRVYybNF+jO3QzmGWbl\nvx7DEgf67hjMuPlZbGHUryMSXPvGGFqENE6qiR98vhpSQ2fl1ULNHLfJUH2k\n7o7j\r\n=qugP\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "78e8234e8dc935a3584f8200ffb250a247621ffe", "scripts": {"test": "xo && ava && tsd", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/strip-json-comments.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "directories": {}, "_nodeVersion": "16.6.2", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.44.0", "ava": "^3.15.0", "tsd": "^0.17.0", "matcha": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/strip-json-comments_4.0.0_1629396616558_0.7014931465036216", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "strip-json-comments", "version": "5.0.0", "keywords": ["json", "strip", "comments", "remove", "delete", "trim", "multiline", "parse", "config", "configuration", "settings", "util", "env", "environment", "jsonc"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "strip-json-comments@5.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/strip-json-comments#readme", "bugs": {"url": "https://github.com/sindresorhus/strip-json-comments/issues"}, "xo": {"rules": {"complexity": "off"}}, "dist": {"shasum": "ec101b766476a703031bc607e3c712569de2aa06", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-V1LGY4UUo0jgwC+ELQ2BNWfPa17TIuwBLg+j1AA/9RPzKINl1lhxVEu2r+ZTTO8aetIsUzE5Qj6LMSBkoGYKKw==", "signatures": [{"sig": "MEUCID/8xYhw0rKNw4jhVY44Z4pPPaTSWpyw1O61ErCWEydZAiEAmBAGpM0FKVEk4TrEsd2AEBayaiL5bTspynT9U2F7Jis=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8460, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2nJdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNog//bW/1811nVrwhwYN/wL0Xf+ht4OiLzA0cjiEMquNI8e5uXJgY\r\nkxh9QwDMuzKgCIHtD6+veA/i5eXbh4ACLAjAvigVpemggVxqhYU3P/dUxw7F\r\nqn+mR4sGpmPgfoMUUVdUytcvpheSig6Gmc1Kx/1KeXHX7aUoeP6aN6qHKSOb\r\nVAKbbs0c/2D9T4KSKPLedFqyRIMA16PgKrNflRzwY2NT7SiWIWIGy1KZhe5G\r\nidH19mT/mIpJhLKEu7a9rP+d6fuJhxpbrdIxEnF/CaC1h0HDR+0OcblYKhRm\r\nFzkEIoJIfdwJtl5+O7YhWdLMMRPGok2kqmJyEAnTt4rLgF/IBdAvaqfBceEK\r\nfjmIVHYsbt4rtzmPmhotNZhwWVXasWncn7apgV8veSlFgkSkLFdVRtT2QK1I\r\npDit7SdxVFjznla63W8xAjW2OqhueIT3mscsvd47SdWbIxaJK5XUUUD/OWGw\r\nlEPfcyIjIHm2D/TE9chWVMgwk/Fy3MdisbyVmZYqvWmZDbmak/ex6TfFtYH2\r\nn7dOnidX+8GNNRqL1TvxXfAg9TN4VHquW7lma5vxOhAaoGC8bOl+/97qI9eO\r\nFubLQJMaFT0sWG1AiwQkq1VVyS2RVfh5pUdxDgWN/KHjnwKPPPsB15ioS6G+\r\nBmg9fOEPxbiW6dVEZAL6CZe4nx86pDq6y+s=\r\n=5eOQ\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "34b79cb0f1129aa85ef4b5c3292e8bc546984ef9", "scripts": {"test": "xo && ava && tsd", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/strip-json-comments.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "directories": {}, "_nodeVersion": "14.19.3", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.51.0", "ava": "^4.3.1", "tsd": "^0.22.0", "matcha": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/strip-json-comments_5.0.0_1658483293603_0.36134643515738607", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "strip-json-comments", "version": "5.0.1", "keywords": ["json", "strip", "comments", "remove", "delete", "trim", "multiline", "parse", "config", "configuration", "settings", "util", "env", "environment", "jsonc"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "strip-json-comments@5.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/strip-json-comments#readme", "bugs": {"url": "https://github.com/sindresorhus/strip-json-comments/issues"}, "xo": {"rules": {"complexity": "off"}}, "dist": {"shasum": "0d8b7d01b23848ed7dbdf4baaaa31a8250d8cfa0", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-5.0.1.tgz", "fileCount": 5, "integrity": "sha512-0fk9zBqO67Nq5M/m45qHCJxylV/DhBlIOVExqgOMiCCrzrhU6tCibRXNqE3jwJLftzE9SNuZtYbpzcO+i9FiKw==", "signatures": [{"sig": "MEUCIQDhcInXvpnuH/rxnhx2+i2ntYCwcpRQLX/n3/G5B7ExGwIgTK3ETqlDJK9XLjF1c160HE80398JdRCpRkflJUT12wQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8003}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "85611bf8a07246bca27f949c997a1460c57bbe9f", "scripts": {"test": "xo && ava && tsd", "bench": "matcha benchmark.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/strip-json-comments.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "directories": {}, "_nodeVersion": "16.20.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.54.2", "ava": "^4.3.1", "tsd": "^0.22.0", "matcha": "^0.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/strip-json-comments_5.0.1_1688469764035_0.3767870379570428", "host": "s3://npm-registry-packages"}}, "5.0.2": {"name": "strip-json-comments", "version": "5.0.2", "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/strip-json-comments.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "sideEffects": false, "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava && tsd", "bench": "matcha benchmark.js"}, "keywords": ["json", "strip", "comments", "remove", "delete", "trim", "multiline", "parse", "config", "configuration", "settings", "util", "env", "environment", "jsonc"], "devDependencies": {"ava": "^4.3.1", "matcha": "^0.7.0", "tsd": "^0.22.0", "xo": "^0.54.2"}, "xo": {"rules": {"complexity": "off"}}, "_id": "strip-json-comments@5.0.2", "gitHead": "ba1182cbeae33349b8f8d6094d34772d631ac7d4", "bugs": {"url": "https://github.com/sindresorhus/strip-json-comments/issues"}, "homepage": "https://github.com/sindresorhus/strip-json-comments#readme", "_nodeVersion": "20.19.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-4X2FR3UwhNUE9G49aIsJW5hRRR3GXGTBTZRMfv568O60ojM8HcWjV/VxAxCDW3SUND33O6ZY66ZuRcdkj73q2g==", "shasum": "14a76abd63b84a6d2419d14f26a0281d0cf6ea46", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-5.0.2.tgz", "fileCount": 5, "unpackedSize": 8153, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCk7tU66Y9u4PZNoL7BzZQs70gA9VSBw2777h8si8Pf/gIhAOpdRaRhje9tlvoWNxgzQanJZxnJHD8ky3t6Zr6yQMK3"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/strip-json-comments_5.0.2_1747428441524_0.16247267779995456"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-11-17T15:19:04.627Z", "modified": "2025-05-16T20:47:21.899Z", "0.1.0": "2013-11-17T15:19:07.474Z", "0.1.1": "2013-11-17T15:22:21.059Z", "0.1.2": "2014-04-29T19:52:43.968Z", "0.1.3": "2014-05-28T10:38:42.692Z", "1.0.0": "2014-07-26T00:26:03.567Z", "1.0.1": "2014-07-26T00:34:50.278Z", "1.0.2": "2014-10-16T13:25:20.301Z", "1.0.3": "2015-08-01T01:15:27.783Z", "1.0.4": "2015-08-02T00:37:05.861Z", "2.0.0": "2015-11-18T14:23:05.438Z", "2.0.1": "2016-02-09T08:30:06.455Z", "3.0.0": "2019-04-30T12:23:05.813Z", "3.0.1": "2019-04-30T17:11:19.115Z", "3.1.0": "2020-04-06T07:58:47.761Z", "3.1.1": "2020-07-12T14:44:56.568Z", "4.0.0": "2021-08-19T18:10:16.697Z", "5.0.0": "2022-07-22T09:48:13.708Z", "5.0.1": "2023-07-04T11:22:44.178Z", "5.0.2": "2025-05-16T20:47:21.731Z"}, "bugs": {"url": "https://github.com/sindresorhus/strip-json-comments/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "license": "MIT", "homepage": "https://github.com/sindresorhus/strip-json-comments#readme", "keywords": ["json", "strip", "comments", "remove", "delete", "trim", "multiline", "parse", "config", "configuration", "settings", "util", "env", "environment", "jsonc"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/strip-json-comments.git"}, "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "readme": "# strip-json-comments\n\n> Strip comments from JSON. Lets you use comments in your JSON files!\n\nThis is now possible:\n\n```js\n{\n\t// Rainbows\n\t\"unicorn\": /* ❤ */ \"cake\"\n}\n```\n\nIt will replace single-line comments `//` and multi-line comments `/**/` with whitespace. This allows JSON error positions to remain as close as possible to the original source.\n\nAlso available as a [Gulp](https://github.com/sindresorhus/gulp-strip-json-comments)/[Grunt](https://github.com/sindresorhus/grunt-strip-json-comments)/[B<PERSON><PERSON><PERSON>](https://github.com/sindresorhus/broccoli-strip-json-comments) plugin.\n\n## Install\n\n```sh\nnpm install strip-json-comments\n```\n\n## Usage\n\n```js\nimport stripJsonComments from 'strip-json-comments';\n\nconst json = `{\n\t// Rainbows\n\t\"unicorn\": /* ❤ */ \"cake\"\n}`;\n\nJSON.parse(stripJsonComments(json));\n//=> {unicorn: 'cake'}\n```\n\n## API\n\n### stripJsonComments(jsonString, options?)\n\n#### jsonString\n\nType: `string`\n\nAccepts a string with JSON and returns a string without comments.\n\n#### options\n\nType: `object`\n\n##### trailingCommas\n\nType: `boolean`\\\nDefault: `false`\n\nStrip trailing commas in addition to comments.\n\n##### whitespace\n\nType: `boolean`\\\nDefault: `true`\n\nReplace comments and trailing commas with whitespace instead of stripping them entirely.\n\n## Benchmark\n\n```sh\nnpm run bench\n```\n\n## Related\n\n- [strip-json-comments-cli](https://github.com/sindresorhus/strip-json-comments-cli) - CLI for this module\n- [strip-css-comments](https://github.com/sindresorhus/strip-css-comments) - Strip comments from CSS\n", "readmeFilename": "readme.md", "users": {"usex": true, "martl": true, "pftom": true, "beatak": true, "cr8tiv": true, "xngiser": true, "erikvold": true, "forivall": true, "kwpeters": true, "tdmalone": true, "backnight": true, "calmwinds": true, "haihepeng": true, "mikestaub": true, "mojaray2k": true, "yyscamper": true, "ianvanness": true, "mysticatea": true, "raycharles": true, "flumpus-dev": true, "dominik.palo": true, "prabhash1785": true, "gamersdelight": true, "becomethegamer": true, "joaquin.briceno": true}}