{"_id": "resolve-cwd", "_rev": "7-960d9cd14425c495d011a8a2f237ec01", "name": "resolve-cwd", "description": "Resolve the path of a module like `require.resolve()` but from the current working directory", "dist-tags": {"latest": "3.0.0"}, "versions": {"1.0.0": {"name": "resolve-cwd", "version": "1.0.0", "description": "Resolve the path of a module like `require.resolve()` but from the current working directory", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/resolve-cwd"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["require", "resolve", "path", "module", "from", "like", "path", "cwd", "current", "working", "directory"], "dependencies": {"resolve-from": "^2.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "cdf0ae71148bb01e796165b1799996ae1ff2fa18", "bugs": {"url": "https://github.com/sindresorhus/resolve-cwd/issues"}, "homepage": "https://github.com/sindresorhus/resolve-cwd", "_id": "resolve-cwd@1.0.0", "_shasum": "4eaeea41ed040d1702457df64a42b2b07d246f9f", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "4eaeea41ed040d1702457df64a42b2b07d246f9f", "tarball": "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-1.0.0.tgz", "integrity": "sha512-ac27EnKWWlc2yQ/5GCoCGecqVJ9MSmgiwvUYOS+9A+M0dn1FdP5mnsDZ9gwx+lAvh/d7f4RFn4jLfggRRYxPxw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGvRXG66N5eGwm1MH+1D055APBqbioFySTKvEtpoc3JpAiANnV8w2X5llo87s/VzKDmfURCs1GDGRXNRsugvsShaDg=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "resolve-cwd", "version": "2.0.0", "description": "Resolve the path of a module like `require.resolve()` but from the current working directory", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/resolve-cwd.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["require", "resolve", "path", "module", "from", "like", "cwd", "current", "working", "directory", "import"], "dependencies": {"resolve-from": "^3.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "8c41f4b51c0b12792127ca2974101f8cac800a3b", "bugs": {"url": "https://github.com/sindresorhus/resolve-cwd/issues"}, "homepage": "https://github.com/sindresorhus/resolve-cwd#readme", "_id": "resolve-cwd@2.0.0", "_shasum": "00a9f7387556e27038eae232caa372a6a59b665a", "_from": ".", "_npmVersion": "2.15.11", "_nodeVersion": "4.7.3", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "00a9f7387556e27038eae232caa372a6a59b665a", "tarball": "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-2.0.0.tgz", "integrity": "sha512-ccu8zQTrzVr954472aUVPLEcB3YpKSYR3cg/3lo1okzobPBM+1INXBbBZlDbnI/hbEocnf8j0QVo43hQKrbchg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDY79Fl7Om4iSlp8CAYfHlSrgenglOFnISfJxOVr6LQHAiEAtYzUoo/wecxMQ81kUXRjc/Vt0piPWUQlu5NixHhMGaM="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/resolve-cwd-2.0.0.tgz_1493370863917_0.16202757763676345"}, "directories": {}}, "3.0.0": {"name": "resolve-cwd", "version": "3.0.0", "description": "Resolve the path of a module like `require.resolve()` but from the current working directory", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/resolve-cwd.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["require", "resolve", "path", "module", "from", "like", "cwd", "current", "working", "directory", "import"], "dependencies": {"resolve-from": "^5.0.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "55afe697667744f61fd78787526d6c4d989d75fc", "bugs": {"url": "https://github.com/sindresorhus/resolve-cwd/issues"}, "homepage": "https://github.com/sindresorhus/resolve-cwd#readme", "_id": "resolve-cwd@3.0.0", "_nodeVersion": "10.15.3", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==", "shasum": "0f0075f1bb2544766cf73ba6a6e2adfebcb13f2d", "tarball": "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz", "fileCount": 5, "unpackedSize": 4984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyEVECRA9TVsSAnZWagAAuD8QAIYlycAr3jQxT+pyQrA3\nYA2rYG6peBfZUdagDFOLMY+8z8BWgTDoCip9PSMqUCcUF79oHgYrso3xbO5C\nDUYnStY3bNqsoP50YPbvbo5dkbwh6lMrEWuOM2/0LpqrmzYaScT3YBR6EcNz\nyO762gsxVpecLNlDVHP55LCrB9m5dFCfuN0SUAizNj6qH6Lgaw7jjtX1BUnH\nXhTi95SAgPm8uKwPx/N1X4ffouaa6vKYBDaRZ/qOQtLEqL2l+yPWl7Tin+4d\n2dlrKH/WIowKGoPA2jZT2XO9JWb8hNg5zgOK9ILkQRmNcjGPAqI91DiGeyNF\nOxJcXtq5/w+z2iAJCRZvahJuyj2Rx1nUGsV1Utl4Vpdv4KBpVgwzpl47epIY\nm587ogdMvPFfCxjBpmY1w6CPb7wqUUY7i/MwjpAQY4QHkRTxOpkxSo8gJZ9y\nj5CL3EM9kr9LrB7zpTNCSsx9q1UBsXo4HuJJZ2wiXC8b5cJYT0gr4+dhXKTQ\nSCWZLg0/oHJ4N6t80bAHs7qC5OYj3szKscKyFkFCQoDQmsbd8+DIEhraWMmt\nLBctFgf/zWPOEiWEbGjs6CNoR7e0wwwHoNcHCkWaOPOb5fMzEn4kJI4Q4oWv\nEmS5A7wYPBrYJ+9mPv534Oyxcoqs+pkUx2KATvoym6MONHuEl7v+KY/j3Vi8\nvDha\r\n=FN61\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEAxLl+ieUhl1z/3Sa3h9fSYhyE4sBRbI9q4p3ekDkqzAiBkGx7tYLK1jNFOMYrrLTr1Gf4UUW12m2ou3uSdrUxIhw=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/resolve-cwd_3.0.0_1556628803761_0.5546372287820458"}, "_hasShrinkwrap": false}}, "readme": "# resolve-cwd [![Build Status](https://travis-ci.org/sindresorhus/resolve-cwd.svg?branch=master)](https://travis-ci.org/sindresorhus/resolve-cwd)\n\n> Resolve the path of a module like [`require.resolve()`](https://nodejs.org/api/globals.html#globals_require_resolve) but from the current working directory\n\n\n## Install\n\n```\n$ npm install resolve-cwd\n```\n\n\n## Usage\n\n```js\nconst resolveCwd = require('resolve-cwd');\n\nconsole.log(__dirname);\n//=> '/Users/<USER>/rainbow'\n\nconsole.log(process.cwd());\n//=> '/Users/<USER>/unicorn'\n\nconsole.log(resolveCwd('./foo'));\n//=> '/Users/<USER>/unicorn/foo.js'\n```\n\n\n## API\n\n### resolveCwd(moduleId)\n\nLike `require()`, throws when the module can't be found.\n\n### resolveCwd.silent(moduleId)\n\nReturns `undefined` instead of throwing when the module can't be found.\n\n#### moduleId\n\nType: `string`\n\nWhat you would use in `require()`.\n\n\n## Related\n\n- [resolve-from](https://github.com/sindresorhus/resolve-from) - Resolve the path of a module from a given path\n- [import-from](https://github.com/sindresorhus/import-from) - Import a module from a given path\n- [import-cwd](https://github.com/sindresorhus/import-cwd) - Import a module from the current working directory\n- [resolve-pkg](https://github.com/sindresorhus/resolve-pkg) - Resolve the path of a package regardless of it having an entry point\n- [import-lazy](https://github.com/sindresorhus/import-lazy) - Import a module lazily\n- [resolve-global](https://github.com/sindresorhus/resolve-global) - Resolve the path of a globally installed module\n\n\n## License\n\nMIT © [Sindre Sorhus](https://sindresorhus.com)\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-06-09T21:38:04.121Z", "created": "2015-12-06T16:26:27.080Z", "1.0.0": "2015-12-06T16:26:27.080Z", "2.0.0": "2017-04-28T09:14:24.319Z", "3.0.0": "2019-04-30T12:53:23.953Z"}, "homepage": "https://github.com/sindresorhus/resolve-cwd#readme", "keywords": ["require", "resolve", "path", "module", "from", "like", "cwd", "current", "working", "directory", "import"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/resolve-cwd.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/resolve-cwd/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"flumpus-dev": true}}