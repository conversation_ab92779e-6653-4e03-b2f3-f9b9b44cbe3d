{"_id": "prism-media", "_rev": "37-9213a12a52122dadae47337eb752b3b1", "name": "prism-media", "description": "Easy-to-use stream-based media transcoding", "dist-tags": {"latest": "1.3.5", "alpha": "2.0.0-alpha.0"}, "versions": {"0.0.1": {"name": "prism-media", "version": "0.0.1", "description": "Makes transcoding media easier", "main": "src/index.js", "scripts": {"lint": "eslint src && eslint test", "test": "npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"eslint": "^3.12.2"}, "gitHead": "c9125f673743d26335fb358887879d3215087351", "_id": "prism-media@0.0.1", "_shasum": "a3425c9cabd50d1c6c02e543941a11895667bd10", "_from": ".", "_npmVersion": "3.10.6", "_nodeVersion": "7.9.0", "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "dist": {"shasum": "a3425c9cabd50d1c6c02e543941a11895667bd10", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-0.0.1.tgz", "integrity": "sha512-aa5xyk+DyPNRefugHuCEJtJ9wOc70jbsK0rUTgbzWCAXAA2MNLJQO587fVNr+D2aD2Uev3pACUVm57yzdstFFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID5pz3BKezOKN5oX6Ud6m0JHb6UucuHsPp5L8QD6YVQnAiEA2AaUnZTS5Kz4sw8mkynBmn8P3lU7d3h/nXe3fGGaGa0="}]}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/prism-media-0.0.1.tgz_1492281170625_0.09007600648328662"}, "directories": {}}, "0.0.2": {"name": "prism-media", "version": "0.0.2", "description": "Makes transcoding media easier", "main": "src/index.js", "scripts": {"lint": "eslint src && eslint test", "test": "npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"eslint": "^3.12.2"}, "gitHead": "3b8089ef336558ef85b6608491ca709cdf21e5c9", "_id": "prism-media@0.0.2", "_npmVersion": "5.3.0", "_nodeVersion": "8.6.0", "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-L6yc8P5NVG35ivzvfI7bcTYzqFV+K8gTfX9YaJbmIFfMXTs71RMnAupvTQPTCteGsiOy9QcNLkQyWjAafY/hCQ==", "shasum": "aa917b084576c4df6488e4ea8e7d6d44aed4b411", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-0.0.2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFnYE+jdb1kuw6ZmkK6u2n9X8cTWtEBL6aDJuiFNor+wIhAPYODVBZoyOsd0sC2TzZJHa76Fyc8LyittQKZd9SJMAn"}]}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media-0.0.2.tgz_1508683451759_0.3343057236634195"}, "directories": {}}, "0.1.0": {"name": "prism-media", "version": "0.1.0", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"eslint": "^4.16.0", "ffmpeg-binaries": "^3.2.2-3", "jest": "^21.2.1"}, "peerDependencies": {"opusscript": "^0.0.4", "node-opus": "^0.2.7"}, "gitHead": "21b5f63bb865ca348a955fc0c903f01c389a6e1c", "_id": "prism-media@0.1.0", "_npmVersion": "5.5.1", "_nodeVersion": "9.3.0", "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-epDeAyS5POw5nwPRn/qFNiLwJ9LTrmb9m4Q38zbMxuUqa7RTk03s5OfTbNpTubBC7x8/e9CItpdVpHVFX7SCxg==", "shasum": "efe47ee7660912be98ea4db817c28764111ea8eb", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-0.1.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDP3Z9XBjzjGl5/rPNkfPNq+EFopSYybBrCJV0erxplsQIgO576a6En1xmrOs+3ivLneek/qMduPPJIEOdH17BI4wg="}]}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media-0.1.0.tgz_1516556540610_0.6894513389561325"}, "directories": {}}, "0.2.0": {"name": "prism-media", "version": "0.2.0", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"eslint": "^4.16.0", "ffmpeg-binaries": "^3.2.2-3", "jest": "^22.1.4"}, "peerDependencies": {"opusscript": "^0.0.4", "node-opus": "^0.2.7"}, "gitHead": "b367fca09ecea193b17f3611cb41a05154c9d682", "_id": "prism-media@0.2.0", "_npmVersion": "5.6.0", "_nodeVersion": "9.3.0", "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-lMqWIqNQ/PYjIfCWCmw0dC2buCgyRxu6mn6NXfXLI3EDjen18XcF2C0FWf6iK60DaLFDW5xmVxFwe3moWw5GhA==", "shasum": "82c183da430c41c3025df3610a92a61b107b1918", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-0.2.0.tgz", "fileCount": 20, "unpackedSize": 42019, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE9T1pLeKnIPhfmYSOWJyVg+2AusCI1iJwP9OLsqXHciAiBCcEDYjV61UxDlURU4EfttFzL4X3Xf6Tx05pq68cWf1Q=="}]}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_0.2.0_1520278132466_0.43113836886415857"}, "_hasShrinkwrap": false}, "0.2.1": {"name": "prism-media", "version": "0.2.1", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"eslint": "^4.16.0", "ffmpeg-binaries": "^3.2.2-3", "jest": "^22.1.4"}, "peerDependencies": {"opusscript": "^0.0.4", "node-opus": "^0.2.7"}, "gitHead": "f675af4f2e5b9587f2980a727613cbf68667cf74", "_id": "prism-media@0.2.1", "_npmVersion": "5.7.1", "_nodeVersion": "9.3.0", "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Kfp1+6gzjY6X8mqKHa6D3brX+BtMUPFwzAkz4zgtVPgbkA2XxhITROdfQXVurU4fuJsylFRwqo7ciQlQCm9hAw==", "shasum": "7968f2c7194588887c4c48081968d8c28e0d2469", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-0.2.1.tgz", "fileCount": 20, "unpackedSize": 42011, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIErt21QZ5KG/kUZ+YJt7Ua9EsSDWELhuZUkigU0hnNxmAiEAtT1aby9pCjndNZT4bm70ev7JUviBMiW03soWxenFq5k="}]}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_0.2.1_1522418912289_0.8427226378557859"}, "_hasShrinkwrap": false}, "0.3.0": {"name": "prism-media", "version": "0.3.0", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"eslint": "^4.16.0", "jest": "^22.1.4"}, "gitHead": "f4877bf8172609f34f204431a5317050a2d37eea", "_id": "prism-media@0.3.0", "_npmVersion": "6.0.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-XQ+j419m2UJQgBTiryVj6Y3/uEJ6F3s/u2LDa5Ek7OlS6//9bZE+ixUvQosK5M/re1GZPO5qnHp4UCcMUwhY1Q==", "shasum": "c6d9b4c884db7623724b51f2ef29ce1b54738fbc", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-0.3.0.tgz", "fileCount": 25, "unpackedSize": 47671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbKq3tCRA9TVsSAnZWagAA+SUP/iP83zSpw5iDu1sp+iRP\noyaQUPZaxStLyrUkoIipwZIozcR3/o7IOLsi8dDPjQ1wZmCK8yKXJd0UjgFx\nt/tqewLezsqOENIYOxyHA5/a1RE+93ts81Hpp1xL/UYP6kqUa3abD2qVOPNn\nfrP1q9/yz0eWtsXL2zenY1qGR2Vq78naCKI6fchLOCyHScIkikn80w/CkTGg\n7U6hjJf0UO9CbEn8csvcs1slllrGHVDQq32mnmeiViD19N0I8zOAnBZ85hNL\nXgrmzSSOhWdIGQ2XjLqQt3Y8pmFpe0pAyYY1foJVrXADLJ0wZJnxUk5cY<PERSON>zi\nsPyKaTuTuDMUiDxs//+sPcynvj6+uOKaPpFMLCIO662AEZBbuyu6ehe+//aJ\nO/qNdtzoG2wBn+QdQ53XnyNwnbJnskNvbV+rJzkd6Ki2qjdJ+S2jIT23RJod\nSlPlUoxAFOz195JZP7UN1Ry2zrmqPmPHF5QS8uFDzK7HWE7gEyVSAQDScwkS\nxHopfmODmwCCqeGDWCQeaKfEzI9jAKAkp3CHgd0FVwMLvB/75z4a2H2MJ166\nqWVEToxZsh3IRNMKVo2w/zHv3O9O6PUuUXlMIJR8GPx5Y/D9/LYitHgZk4ud\ncC8NJQw8TGCLqR/BekJvXMSpNOETnrIQkI5fr/es3WRQiISLO5epmUWd73Re\n8aN+\r\n=wLT2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD6SjLE1GHXwBmW4IVvNEVu58jFKW9TIt8AflpjTgFKigIgGEbL/x4zXNWpZzaZSuj7vzmdsQNd8X8IKm8rnl0vJcw="}]}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_0.3.0_1529523692813_0.7205651317250743"}, "_hasShrinkwrap": false}, "0.3.1": {"name": "prism-media", "version": "0.3.1", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"eslint": "^4.16.0", "jest": "^22.1.4"}, "gitHead": "7f54444be3adcc6ac764613e2a993b07ff6347f0", "_id": "prism-media@0.3.1", "_npmVersion": "6.0.0", "_nodeVersion": "10.0.0", "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ZAzpXm6n7IaMDrcm7gB6wEkhF796cFLBZPY91rse5DKsASrZZgo36y9QC4+FnlbWt14aQSZUnKMHnkg6pEDfiQ==", "shasum": "5331522d1194b544c65db4a66ea3854e6916a258", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-0.3.1.tgz", "fileCount": 25, "unpackedSize": 47683, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbLTs8CRA9TVsSAnZWagAAsbMP/2yRd7DN+2c+HDxrgIMB\n0Mgl5gPoXrVijaj6KZHjP+ySs5CW/J/QitK9zdeVdKjhxo8Si7gsVQJuTiIU\nyI84I78TajHvkSfu1TNJbNDOELExMAuK4ydxa6g59iblRwp8RFlke1kMThXc\nNb70SAKoeJt6fRa3EdB+NPEoYlmXiIlaFlQ9h6ZGShV154+KxGbJSOsnSDJC\nhHCCQ59C3AdG/39VmIp1luDTIMEfRbpboQUU559TNmCBt4UgWB3VYjAsipWU\nchzyyykNS46AwU684yw/qpJ5aTXxcysbo9yO+wUjT/q3yCxX560ngROI/W5p\nQht/wK2fwVo5gFp4GX6xBrtQBDsVtSJeN/ndA42cdSXsJzyokA6qRJE/+lQD\ng9HUnPe9pqlBGtGsHEqsLVWd6jeY68QfNXUrpieWWjLrGYSvDWc4WZM/Q9+u\n5jKRpIeL22Q/hBDAzmimSH46Bw6kslK9lT6fJHSh1unXrpzDWTe1h7cJ/JSW\np72eNrsq1u14YpbaXmB2Pt9U8OWtrPfeps7fb5JjyYgDaEXwIJrTYUIc+qDa\ns96jPOBr2zqLptyKgZiZc601sW41y5tVB4lMSyNhv0dEnMeLs28OC8t9e0Xy\n0p7VJTgKZ1sbLgNZNhPijBUG2I4LiaXYDKAUyeoDRhSsRoO8soMxDTZGsyUD\nBTfP\r\n=y6kQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCpn6ZLiSigAhp74JIoe1TqOX8GECl2mYSW/Xa+noWWrQIhANSVwHRI5ViXJv6zDUzrnCqUuqx6a6fF9ELukovBj/ZH"}]}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_0.3.1_1529690940785_0.5831096467096559"}, "_hasShrinkwrap": false}, "0.0.3": {"name": "prism-media", "version": "0.0.3", "description": "Makes transcoding media easier", "main": "src/index.js", "scripts": {"lint": "eslint src && eslint test", "test": "npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"eslint": "^3.12.2"}, "gitHead": "a49a75ee5e14a94f27612f17883c9b2afba51761", "_id": "prism-media@0.0.3", "_npmVersion": "6.2.0", "_nodeVersion": "10.8.0", "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-c9KkNifSMU/iXT8FFTaBwBMr+rdVcN+H/uNv1o+CuFeTThNZNTOrQ+RgXA1yL/DeLk098duAeRPP3QNPNbhxYQ==", "shasum": "8842d4fae804f099d3b48a9a38e3c2bab6f4855b", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-0.0.3.tgz", "fileCount": 15, "unpackedSize": 25943, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbafMnCRA9TVsSAnZWagAAgvUQAKGPLPjz9lrjNo0UFO0I\nq9eGfRaq0HFZKtFZ39VklKaMGOzvvSqouMAE9+NWHXAccyqP/f9qcNvyjLej\nm4uxGjjQcKFlVufJjjWZR5DQ9OFOpqiWuttI2Bn1eA4WOPtYqpvb1B6mZs3q\nD3Tldb2KI0b4DdTPqoBUKlmL36K/0Bh1y2Hb5NxeYSHbJAPqTk5aZM0IXAMz\nr/neVidHDeMaRJl2gqQ/cXjFKFGazcVx6mDNyr2gYeliWHM0AQMrX9FCWeLD\nvspvRQLySf911fd3BoZX/brPSkhhuk25NI4qehCzOASeFziXQuK6DPpWUzTH\nTGUzpVxzI08nLOOp+mBHD2EJEtOkY4FGDRtBfUh54F5XLGHCS5aFjrzqOw/k\npU/K2I/xvFzgb+fSSw28Z6nYqGDGlW98YHJrkg+fX2fN5HcbJebex38iR37X\nxMBAsDUVT5kAc7Q0gEEsWXMBrH/hCEGJQw8sB4VTz4gdL4E83Q5DjWCqRAYX\ndFMJuzlio214mBQE7ywWtZKItLvcfy/cYI5k5A5k+XdZc8LIcwrzKGJ+ghgI\nbg5KPk6jBNEaA9P86JC1YdqPdMjYLqMzQTYYkkDbBq2X7fU+gm7T6AmRmjxk\n6YMqgqpTGaD/tcSXDWCvXigwkbiDKxomGodV50MV54iDQSqreta7VR66xFMq\nhDvi\r\n=O/kX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDYRBBRiHvqNRtjBj3u4gR9kWqVeErYwJBNsuNjjHD0IAiEAw7rMoSDIH8uMt31PrcA2pZr2NnqO/OoHOC0gXh8iVkE="}]}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_0.0.3_1533670183381_0.41893489705029485"}, "_hasShrinkwrap": false}, "0.4.0": {"name": "prism-media", "version": "0.4.0", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"eslint": "^4.16.0", "jest": "^22.1.4"}, "jest": {"testURL": "http://localhost/"}, "licenseText": "                                 Apache License\n                           Version 2.0, January 2004\n                        http://www.apache.org/licenses/\n\n   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION\n\n   1. Definitions.\n\n      \"License\" shall mean the terms and conditions for use, reproduction,\n      and distribution as defined by Sections 1 through 9 of this document.\n\n      \"Licensor\" shall mean the copyright owner or entity authorized by\n      the copyright owner that is granting the License.\n\n      \"Legal Entity\" shall mean the union of the acting entity and all\n      other entities that control, are controlled by, or are under common\n      control with that entity. For the purposes of this definition,\n      \"control\" means (i) the power, direct or indirect, to cause the\n      direction or management of such entity, whether by contract or\n      otherwise, or (ii) ownership of fifty percent (50%) or more of the\n      outstanding shares, or (iii) beneficial ownership of such entity.\n\n      \"You\" (or \"Your\") shall mean an individual or Legal Entity\n      exercising permissions granted by this License.\n\n      \"Source\" form shall mean the preferred form for making modifications,\n      including but not limited to software source code, documentation\n      source, and configuration files.\n\n      \"Object\" form shall mean any form resulting from mechanical\n      transformation or translation of a Source form, including but\n      not limited to compiled object code, generated documentation,\n      and conversions to other media types.\n\n      \"Work\" shall mean the work of authorship, whether in Source or\n      Object form, made available under the License, as indicated by a\n      copyright notice that is included in or attached to the work\n      (an example is provided in the Appendix below).\n\n      \"Derivative Works\" shall mean any work, whether in Source or Object\n      form, that is based on (or derived from) the Work and for which the\n      editorial revisions, annotations, elaborations, or other modifications\n      represent, as a whole, an original work of authorship. For the purposes\n      of this License, Derivative Works shall not include works that remain\n      separable from, or merely link (or bind by name) to the interfaces of,\n      the Work and Derivative Works thereof.\n\n      \"Contribution\" shall mean any work of authorship, including\n      the original version of the Work and any modifications or additions\n      to that Work or Derivative Works thereof, that is intentionally\n      submitted to Licensor for inclusion in the Work by the copyright owner\n      or by an individual or Legal Entity authorized to submit on behalf of\n      the copyright owner. For the purposes of this definition, \"submitted\"\n      means any form of electronic, verbal, or written communication sent\n      to the Licensor or its representatives, including but not limited to\n      communication on electronic mailing lists, source code control systems,\n      and issue tracking systems that are managed by, or on behalf of, the\n      Licensor for the purpose of discussing and improving the Work, but\n      excluding communication that is conspicuously marked or otherwise\n      designated in writing by the copyright owner as \"Not a Contribution.\"\n\n      \"Contributor\" shall mean Licensor and any individual or Legal Entity\n      on behalf of whom a Contribution has been received by Licensor and\n      subsequently incorporated within the Work.\n\n   2. Grant of Copyright License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      copyright license to reproduce, prepare Derivative Works of,\n      publicly display, publicly perform, sublicense, and distribute the\n      Work and such Derivative Works in Source or Object form.\n\n   3. Grant of Patent License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      (except as stated in this section) patent license to make, have made,\n      use, offer to sell, sell, import, and otherwise transfer the Work,\n      where such license applies only to those patent claims licensable\n      by such Contributor that are necessarily infringed by their\n      Contribution(s) alone or by combination of their Contribution(s)\n      with the Work to which such Contribution(s) was submitted. If You\n      institute patent litigation against any entity (including a\n      cross-claim or counterclaim in a lawsuit) alleging that the Work\n      or a Contribution incorporated within the Work constitutes direct\n      or contributory patent infringement, then any patent licenses\n      granted to You under this License for that Work shall terminate\n      as of the date such litigation is filed.\n\n   4. Redistribution. You may reproduce and distribute copies of the\n      Work or Derivative Works thereof in any medium, with or without\n      modifications, and in Source or Object form, provided that You\n      meet the following conditions:\n\n      (a) You must give any other recipients of the Work or\n          Derivative Works a copy of this License; and\n\n      (b) You must cause any modified files to carry prominent notices\n          stating that You changed the files; and\n\n      (c) You must retain, in the Source form of any Derivative Works\n          that You distribute, all copyright, patent, trademark, and\n          attribution notices from the Source form of the Work,\n          excluding those notices that do not pertain to any part of\n          the Derivative Works; and\n\n      (d) If the Work includes a \"NOTICE\" text file as part of its\n          distribution, then any Derivative Works that You distribute must\n          include a readable copy of the attribution notices contained\n          within such NOTICE file, excluding those notices that do not\n          pertain to any part of the Derivative Works, in at least one\n          of the following places: within a NOTICE text file distributed\n          as part of the Derivative Works; within the Source form or\n          documentation, if provided along with the Derivative Works; or,\n          within a display generated by the Derivative Works, if and\n          wherever such third-party notices normally appear. The contents\n          of the NOTICE file are for informational purposes only and\n          do not modify the License. You may add Your own attribution\n          notices within Derivative Works that You distribute, alongside\n          or as an addendum to the NOTICE text from the Work, provided\n          that such additional attribution notices cannot be construed\n          as modifying the License.\n\n      You may add Your own copyright statement to Your modifications and\n      may provide additional or different license terms and conditions\n      for use, reproduction, or distribution of Your modifications, or\n      for any such Derivative Works as a whole, provided Your use,\n      reproduction, and distribution of the Work otherwise complies with\n      the conditions stated in this License.\n\n   5. Submission of Contributions. Unless You explicitly state otherwise,\n      any Contribution intentionally submitted for inclusion in the Work\n      by You to the Licensor shall be under the terms and conditions of\n      this License, without any additional terms or conditions.\n      Notwithstanding the above, nothing herein shall supersede or modify\n      the terms of any separate license agreement you may have executed\n      with Licensor regarding such Contributions.\n\n   6. Trademarks. This License does not grant permission to use the trade\n      names, trademarks, service marks, or product names of the Licensor,\n      except as required for reasonable and customary use in describing the\n      origin of the Work and reproducing the content of the NOTICE file.\n\n   7. Disclaimer of Warranty. Unless required by applicable law or\n      agreed to in writing, Licensor provides the Work (and each\n      Contributor provides its Contributions) on an \"AS IS\" BASIS,\n      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or\n      implied, including, without limitation, any warranties or conditions\n      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A\n      PARTICULAR PURPOSE. You are solely responsible for determining the\n      appropriateness of using or redistributing the Work and assume any\n      risks associated with Your exercise of permissions under this License.\n\n   8. Limitation of Liability. In no event and under no legal theory,\n      whether in tort (including negligence), contract, or otherwise,\n      unless required by applicable law (such as deliberate and grossly\n      negligent acts) or agreed to in writing, shall any Contributor be\n      liable to You for damages, including any direct, indirect, special,\n      incidental, or consequential damages of any character arising as a\n      result of this License or out of the use or inability to use the\n      Work (including but not limited to damages for loss of goodwill,\n      work stoppage, computer failure or malfunction, or any and all\n      other commercial damages or losses), even if such Contributor\n      has been advised of the possibility of such damages.\n\n   9. Accepting Warranty or Additional Liability. While redistributing\n      the Work or Derivative Works thereof, You may choose to offer,\n      and charge a fee for, acceptance of support, warranty, indemnity,\n      or other liability obligations and/or rights consistent with this\n      License. However, in accepting such obligations, You may act only\n      on Your own behalf and on Your sole responsibility, not on behalf\n      of any other Contributor, and only if You agree to indemnify,\n      defend, and hold each Contributor harmless for any liability\n      incurred by, or claims asserted against, such Contributor by reason\n      of your accepting any such warranty or additional liability.\n\n   END OF TERMS AND CONDITIONS\n\n   APPENDIX: How to apply the Apache License to your work.\n\n      To apply the Apache License to your work, attach the following\n      boilerplate notice, with the fields enclosed by brackets \"{}\"\n      replaced with your own identifying information. (Don't include\n      the brackets!)  The text should be enclosed in the appropriate\n      comment syntax for the file format. We also recommend that a\n      file or class name and description of purpose be included on the\n      same \"printed page\" as the copyright notice for easier\n      identification within third-party archives.\n\n   Copyright {yyyy} {name of copyright owner}\n\n   Licensed under the Apache License, Version 2.0 (the \"License\");\n   you may not use this file except in compliance with the License.\n   You may obtain a copy of the License at\n\n       http://www.apache.org/licenses/LICENSE-2.0\n\n   Unless required by applicable law or agreed to in writing, software\n   distributed under the License is distributed on an \"AS IS\" BASIS,\n   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n   See the License for the specific language governing permissions and\n   limitations under the License.\n", "_id": "prism-media@0.4.0", "dist": {"shasum": "231f65764191bd9d49a8c27029844bb3f20d72dc", "integrity": "sha512-dyWYpQq6CaQznavhUnuo/2ohxiNw4N6TcpF7OFMJEKjyigdrXxn0ctrV9IzILbdXKit3SO+UvicLd11Qz8ELFQ==", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-0.4.0.tgz", "fileCount": 72, "unpackedSize": 267091, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcacW3CRA9TVsSAnZWagAAtCYP/RtG8gXixw3XCw2ZmSh5\nDK9FxTGpVNqAcaNJZnIaWi6nAFWGqSI66sot2EUhKAgxRYoGtiVg6I/Pum5g\nV/v5Hbywvizsp5y00Xf2zTEq8TFqnpaNZcTAW4jwv4M/wIVgitY3B1ZDAAgA\nWkktfKKd7iwP8WIMCN+eeFzEAHXfY45PS/aBsEbJZu3Lm0avdACVnjLolVBZ\nLK+mG6d4OHtz7ROcZi3L8+haNbx9yqd7H6oXYniNMeykkXwT0XH4g5nYpRR1\n/GLKCsQ0ZI4e2VPpCLVGlCW7pXpc2akWguv53Un01wSqiU0IzKCTKXVnkx/w\nNPfn9Q7RWOE6ab8B7cX8USbVFVkBqVfyhMHEW4HvYitSJh8pl4K9WzbjstCO\nVvYoQVyVAQgG+GGIdZmm5hw6ZEdxAyP3sYEMn+h6/AVZw9IJgqfA5QhIFAEG\n6NfZEnIcvX8DoO40nW9qq1fB+WAwRZATo9zIxlNXyEsIpOtNe6r/dvPcsd3A\nHt1HDBsn8KHC38j/qUFmV2nr0M8FJlqlab6KTDY9ljjJZ9JTRVXYgjrP87qF\n5OMBUDq4sOiXLyuwdnw2dlcoxN0/mKnff8o70bFSG+IJgECKIAlngMEt/Y9r\n4yVIf3OH8g38Khj+MetCGVDQG/ogxERjw6WNVx9EZfDrDp/QmjseXat2fak4\naRe6\r\n=5rmW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCHUV0EjT6t/skipVKB4EoSN5XmLTGqOr04sIL1UMBwygIhAOzb/WrVTzZh8/8e5CwUsc9t2RGZIkfDRCdXoFw03ovO"}]}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_0.4.0_1550435767089_0.2313182714291"}, "_hasShrinkwrap": false}, "0.4.1": {"name": "prism-media", "version": "0.4.1", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"eslint": "^4.16.0", "jest": "^22.1.4"}, "jest": {"testURL": "http://localhost/"}, "licenseText": "                                 Apache License\n                           Version 2.0, January 2004\n                        http://www.apache.org/licenses/\n\n   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION\n\n   1. Definitions.\n\n      \"License\" shall mean the terms and conditions for use, reproduction,\n      and distribution as defined by Sections 1 through 9 of this document.\n\n      \"Licensor\" shall mean the copyright owner or entity authorized by\n      the copyright owner that is granting the License.\n\n      \"Legal Entity\" shall mean the union of the acting entity and all\n      other entities that control, are controlled by, or are under common\n      control with that entity. For the purposes of this definition,\n      \"control\" means (i) the power, direct or indirect, to cause the\n      direction or management of such entity, whether by contract or\n      otherwise, or (ii) ownership of fifty percent (50%) or more of the\n      outstanding shares, or (iii) beneficial ownership of such entity.\n\n      \"You\" (or \"Your\") shall mean an individual or Legal Entity\n      exercising permissions granted by this License.\n\n      \"Source\" form shall mean the preferred form for making modifications,\n      including but not limited to software source code, documentation\n      source, and configuration files.\n\n      \"Object\" form shall mean any form resulting from mechanical\n      transformation or translation of a Source form, including but\n      not limited to compiled object code, generated documentation,\n      and conversions to other media types.\n\n      \"Work\" shall mean the work of authorship, whether in Source or\n      Object form, made available under the License, as indicated by a\n      copyright notice that is included in or attached to the work\n      (an example is provided in the Appendix below).\n\n      \"Derivative Works\" shall mean any work, whether in Source or Object\n      form, that is based on (or derived from) the Work and for which the\n      editorial revisions, annotations, elaborations, or other modifications\n      represent, as a whole, an original work of authorship. For the purposes\n      of this License, Derivative Works shall not include works that remain\n      separable from, or merely link (or bind by name) to the interfaces of,\n      the Work and Derivative Works thereof.\n\n      \"Contribution\" shall mean any work of authorship, including\n      the original version of the Work and any modifications or additions\n      to that Work or Derivative Works thereof, that is intentionally\n      submitted to Licensor for inclusion in the Work by the copyright owner\n      or by an individual or Legal Entity authorized to submit on behalf of\n      the copyright owner. For the purposes of this definition, \"submitted\"\n      means any form of electronic, verbal, or written communication sent\n      to the Licensor or its representatives, including but not limited to\n      communication on electronic mailing lists, source code control systems,\n      and issue tracking systems that are managed by, or on behalf of, the\n      Licensor for the purpose of discussing and improving the Work, but\n      excluding communication that is conspicuously marked or otherwise\n      designated in writing by the copyright owner as \"Not a Contribution.\"\n\n      \"Contributor\" shall mean Licensor and any individual or Legal Entity\n      on behalf of whom a Contribution has been received by Licensor and\n      subsequently incorporated within the Work.\n\n   2. Grant of Copyright License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      copyright license to reproduce, prepare Derivative Works of,\n      publicly display, publicly perform, sublicense, and distribute the\n      Work and such Derivative Works in Source or Object form.\n\n   3. Grant of Patent License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      (except as stated in this section) patent license to make, have made,\n      use, offer to sell, sell, import, and otherwise transfer the Work,\n      where such license applies only to those patent claims licensable\n      by such Contributor that are necessarily infringed by their\n      Contribution(s) alone or by combination of their Contribution(s)\n      with the Work to which such Contribution(s) was submitted. If You\n      institute patent litigation against any entity (including a\n      cross-claim or counterclaim in a lawsuit) alleging that the Work\n      or a Contribution incorporated within the Work constitutes direct\n      or contributory patent infringement, then any patent licenses\n      granted to You under this License for that Work shall terminate\n      as of the date such litigation is filed.\n\n   4. Redistribution. You may reproduce and distribute copies of the\n      Work or Derivative Works thereof in any medium, with or without\n      modifications, and in Source or Object form, provided that You\n      meet the following conditions:\n\n      (a) You must give any other recipients of the Work or\n          Derivative Works a copy of this License; and\n\n      (b) You must cause any modified files to carry prominent notices\n          stating that You changed the files; and\n\n      (c) You must retain, in the Source form of any Derivative Works\n          that You distribute, all copyright, patent, trademark, and\n          attribution notices from the Source form of the Work,\n          excluding those notices that do not pertain to any part of\n          the Derivative Works; and\n\n      (d) If the Work includes a \"NOTICE\" text file as part of its\n          distribution, then any Derivative Works that You distribute must\n          include a readable copy of the attribution notices contained\n          within such NOTICE file, excluding those notices that do not\n          pertain to any part of the Derivative Works, in at least one\n          of the following places: within a NOTICE text file distributed\n          as part of the Derivative Works; within the Source form or\n          documentation, if provided along with the Derivative Works; or,\n          within a display generated by the Derivative Works, if and\n          wherever such third-party notices normally appear. The contents\n          of the NOTICE file are for informational purposes only and\n          do not modify the License. You may add Your own attribution\n          notices within Derivative Works that You distribute, alongside\n          or as an addendum to the NOTICE text from the Work, provided\n          that such additional attribution notices cannot be construed\n          as modifying the License.\n\n      You may add Your own copyright statement to Your modifications and\n      may provide additional or different license terms and conditions\n      for use, reproduction, or distribution of Your modifications, or\n      for any such Derivative Works as a whole, provided Your use,\n      reproduction, and distribution of the Work otherwise complies with\n      the conditions stated in this License.\n\n   5. Submission of Contributions. Unless You explicitly state otherwise,\n      any Contribution intentionally submitted for inclusion in the Work\n      by You to the Licensor shall be under the terms and conditions of\n      this License, without any additional terms or conditions.\n      Notwithstanding the above, nothing herein shall supersede or modify\n      the terms of any separate license agreement you may have executed\n      with Licensor regarding such Contributions.\n\n   6. Trademarks. This License does not grant permission to use the trade\n      names, trademarks, service marks, or product names of the Licensor,\n      except as required for reasonable and customary use in describing the\n      origin of the Work and reproducing the content of the NOTICE file.\n\n   7. Disclaimer of Warranty. Unless required by applicable law or\n      agreed to in writing, Licensor provides the Work (and each\n      Contributor provides its Contributions) on an \"AS IS\" BASIS,\n      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or\n      implied, including, without limitation, any warranties or conditions\n      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A\n      PARTICULAR PURPOSE. You are solely responsible for determining the\n      appropriateness of using or redistributing the Work and assume any\n      risks associated with Your exercise of permissions under this License.\n\n   8. Limitation of Liability. In no event and under no legal theory,\n      whether in tort (including negligence), contract, or otherwise,\n      unless required by applicable law (such as deliberate and grossly\n      negligent acts) or agreed to in writing, shall any Contributor be\n      liable to You for damages, including any direct, indirect, special,\n      incidental, or consequential damages of any character arising as a\n      result of this License or out of the use or inability to use the\n      Work (including but not limited to damages for loss of goodwill,\n      work stoppage, computer failure or malfunction, or any and all\n      other commercial damages or losses), even if such Contributor\n      has been advised of the possibility of such damages.\n\n   9. Accepting Warranty or Additional Liability. While redistributing\n      the Work or Derivative Works thereof, You may choose to offer,\n      and charge a fee for, acceptance of support, warranty, indemnity,\n      or other liability obligations and/or rights consistent with this\n      License. However, in accepting such obligations, You may act only\n      on Your own behalf and on Your sole responsibility, not on behalf\n      of any other Contributor, and only if You agree to indemnify,\n      defend, and hold each Contributor harmless for any liability\n      incurred by, or claims asserted against, such Contributor by reason\n      of your accepting any such warranty or additional liability.\n\n   END OF TERMS AND CONDITIONS\n\n   APPENDIX: How to apply the Apache License to your work.\n\n      To apply the Apache License to your work, attach the following\n      boilerplate notice, with the fields enclosed by brackets \"{}\"\n      replaced with your own identifying information. (Don't include\n      the brackets!)  The text should be enclosed in the appropriate\n      comment syntax for the file format. We also recommend that a\n      file or class name and description of purpose be included on the\n      same \"printed page\" as the copyright notice for easier\n      identification within third-party archives.\n\n   Copyright {yyyy} {name of copyright owner}\n\n   Licensed under the Apache License, Version 2.0 (the \"License\");\n   you may not use this file except in compliance with the License.\n   You may obtain a copy of the License at\n\n       http://www.apache.org/licenses/LICENSE-2.0\n\n   Unless required by applicable law or agreed to in writing, software\n   distributed under the License is distributed on an \"AS IS\" BASIS,\n   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n   See the License for the specific language governing permissions and\n   limitations under the License.\n", "_id": "prism-media@0.4.1", "dist": {"shasum": "374011bb419f181421e2b362e3b43a1946362b8d", "integrity": "sha512-ROZWaMKEHJQfPKK9uccepJIb4JpgpcU1KXI1GNUoxjDmmqi9cq4pvgfd1ysD9/5vbu6/hT9JUEBuLZSc/+2Tyw==", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-0.4.1.tgz", "fileCount": 39, "unpackedSize": 365115, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcacZTCRA9TVsSAnZWagAARbQP/32LSHlxKIk5CcAoOE4P\n44qcFao6lzlKKseJ3gAZO2RZCnrZTnIUR1lYkb7JSU5iUSicDOLB4QMwJXWb\ndtikS7KQAYj7P8PGY832OIuyYQFIYv5bJGcFoHHCyQB9BjufltOpaB5572Ee\ndMkCy0sto4FV4d6u3gy2JM+a/UvxjxX/vSkx+1DdWTewsor2or/IB7s2kKb1\nI8vGtG7Oxj9GKJW8TqwAjvjRiGDYc2DdRmWhbK4oVdpBsaF7u3HgxX9CpU1s\nL48eNT1iorBvF+3YWGgNsbyEV+ApehwKcBVz7QlX3TAnJ70qTFz9FlwB5Zjq\n8snGEVJoaOVNNjjTJdkT/iBTb8A0BmqL2EQeajX3VXype+mie+N+w5rB/hxg\n5d69c81u4ixj41r3Tx+XpCdnZz5CPyLRpdjIkI0Lc3BaIKZdr9rwkmtkktxb\ngxvLUOm8sAins1MUu8IzPATYRaDr7Zk1K9/ms1gDUh5BPeR5FqYHIywVXJ4n\nLLOLbJEmmbzvfUC4hIVCc1XLJZycbbuaGQWXOKTqeGIe23jVldT9hK/gdmqy\nd33vdy1W8jMh7SgDyMy9shH5iQs9KR9PBOy1UWfltBy/WkIDzE+sxHVsB2hP\noLkL6NIzng0AjfHNusmfPZ1BfCM4GVu6Av5y7oX5RFrWM1haTmWP7SPCkt4D\nmFc7\r\n=4MUT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4PwnBdDUYhrKs4Ppm4O8LEftnvasmyuwmZ1BNucVKvgIhAIlNL0sybAGlwEVzo4064L6W2i9POD09Qurq/+fkoOHk"}]}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_0.4.1_1550435923096_0.7008641968790974"}, "_hasShrinkwrap": false}, "0.4.2": {"name": "prism-media", "version": "0.4.2", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"eslint": "^4.16.0", "jest": "^22.1.4"}, "jest": {"testURL": "http://localhost/"}, "licenseText": "                                 Apache License\n                           Version 2.0, January 2004\n                        http://www.apache.org/licenses/\n\n   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION\n\n   1. Definitions.\n\n      \"License\" shall mean the terms and conditions for use, reproduction,\n      and distribution as defined by Sections 1 through 9 of this document.\n\n      \"Licensor\" shall mean the copyright owner or entity authorized by\n      the copyright owner that is granting the License.\n\n      \"Legal Entity\" shall mean the union of the acting entity and all\n      other entities that control, are controlled by, or are under common\n      control with that entity. For the purposes of this definition,\n      \"control\" means (i) the power, direct or indirect, to cause the\n      direction or management of such entity, whether by contract or\n      otherwise, or (ii) ownership of fifty percent (50%) or more of the\n      outstanding shares, or (iii) beneficial ownership of such entity.\n\n      \"You\" (or \"Your\") shall mean an individual or Legal Entity\n      exercising permissions granted by this License.\n\n      \"Source\" form shall mean the preferred form for making modifications,\n      including but not limited to software source code, documentation\n      source, and configuration files.\n\n      \"Object\" form shall mean any form resulting from mechanical\n      transformation or translation of a Source form, including but\n      not limited to compiled object code, generated documentation,\n      and conversions to other media types.\n\n      \"Work\" shall mean the work of authorship, whether in Source or\n      Object form, made available under the License, as indicated by a\n      copyright notice that is included in or attached to the work\n      (an example is provided in the Appendix below).\n\n      \"Derivative Works\" shall mean any work, whether in Source or Object\n      form, that is based on (or derived from) the Work and for which the\n      editorial revisions, annotations, elaborations, or other modifications\n      represent, as a whole, an original work of authorship. For the purposes\n      of this License, Derivative Works shall not include works that remain\n      separable from, or merely link (or bind by name) to the interfaces of,\n      the Work and Derivative Works thereof.\n\n      \"Contribution\" shall mean any work of authorship, including\n      the original version of the Work and any modifications or additions\n      to that Work or Derivative Works thereof, that is intentionally\n      submitted to Licensor for inclusion in the Work by the copyright owner\n      or by an individual or Legal Entity authorized to submit on behalf of\n      the copyright owner. For the purposes of this definition, \"submitted\"\n      means any form of electronic, verbal, or written communication sent\n      to the Licensor or its representatives, including but not limited to\n      communication on electronic mailing lists, source code control systems,\n      and issue tracking systems that are managed by, or on behalf of, the\n      Licensor for the purpose of discussing and improving the Work, but\n      excluding communication that is conspicuously marked or otherwise\n      designated in writing by the copyright owner as \"Not a Contribution.\"\n\n      \"Contributor\" shall mean Licensor and any individual or Legal Entity\n      on behalf of whom a Contribution has been received by Licensor and\n      subsequently incorporated within the Work.\n\n   2. Grant of Copyright License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      copyright license to reproduce, prepare Derivative Works of,\n      publicly display, publicly perform, sublicense, and distribute the\n      Work and such Derivative Works in Source or Object form.\n\n   3. Grant of Patent License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      (except as stated in this section) patent license to make, have made,\n      use, offer to sell, sell, import, and otherwise transfer the Work,\n      where such license applies only to those patent claims licensable\n      by such Contributor that are necessarily infringed by their\n      Contribution(s) alone or by combination of their Contribution(s)\n      with the Work to which such Contribution(s) was submitted. If You\n      institute patent litigation against any entity (including a\n      cross-claim or counterclaim in a lawsuit) alleging that the Work\n      or a Contribution incorporated within the Work constitutes direct\n      or contributory patent infringement, then any patent licenses\n      granted to You under this License for that Work shall terminate\n      as of the date such litigation is filed.\n\n   4. Redistribution. You may reproduce and distribute copies of the\n      Work or Derivative Works thereof in any medium, with or without\n      modifications, and in Source or Object form, provided that You\n      meet the following conditions:\n\n      (a) You must give any other recipients of the Work or\n          Derivative Works a copy of this License; and\n\n      (b) You must cause any modified files to carry prominent notices\n          stating that You changed the files; and\n\n      (c) You must retain, in the Source form of any Derivative Works\n          that You distribute, all copyright, patent, trademark, and\n          attribution notices from the Source form of the Work,\n          excluding those notices that do not pertain to any part of\n          the Derivative Works; and\n\n      (d) If the Work includes a \"NOTICE\" text file as part of its\n          distribution, then any Derivative Works that You distribute must\n          include a readable copy of the attribution notices contained\n          within such NOTICE file, excluding those notices that do not\n          pertain to any part of the Derivative Works, in at least one\n          of the following places: within a NOTICE text file distributed\n          as part of the Derivative Works; within the Source form or\n          documentation, if provided along with the Derivative Works; or,\n          within a display generated by the Derivative Works, if and\n          wherever such third-party notices normally appear. The contents\n          of the NOTICE file are for informational purposes only and\n          do not modify the License. You may add Your own attribution\n          notices within Derivative Works that You distribute, alongside\n          or as an addendum to the NOTICE text from the Work, provided\n          that such additional attribution notices cannot be construed\n          as modifying the License.\n\n      You may add Your own copyright statement to Your modifications and\n      may provide additional or different license terms and conditions\n      for use, reproduction, or distribution of Your modifications, or\n      for any such Derivative Works as a whole, provided Your use,\n      reproduction, and distribution of the Work otherwise complies with\n      the conditions stated in this License.\n\n   5. Submission of Contributions. Unless You explicitly state otherwise,\n      any Contribution intentionally submitted for inclusion in the Work\n      by You to the Licensor shall be under the terms and conditions of\n      this License, without any additional terms or conditions.\n      Notwithstanding the above, nothing herein shall supersede or modify\n      the terms of any separate license agreement you may have executed\n      with Licensor regarding such Contributions.\n\n   6. Trademarks. This License does not grant permission to use the trade\n      names, trademarks, service marks, or product names of the Licensor,\n      except as required for reasonable and customary use in describing the\n      origin of the Work and reproducing the content of the NOTICE file.\n\n   7. Disclaimer of Warranty. Unless required by applicable law or\n      agreed to in writing, Licensor provides the Work (and each\n      Contributor provides its Contributions) on an \"AS IS\" BASIS,\n      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or\n      implied, including, without limitation, any warranties or conditions\n      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A\n      PARTICULAR PURPOSE. You are solely responsible for determining the\n      appropriateness of using or redistributing the Work and assume any\n      risks associated with Your exercise of permissions under this License.\n\n   8. Limitation of Liability. In no event and under no legal theory,\n      whether in tort (including negligence), contract, or otherwise,\n      unless required by applicable law (such as deliberate and grossly\n      negligent acts) or agreed to in writing, shall any Contributor be\n      liable to You for damages, including any direct, indirect, special,\n      incidental, or consequential damages of any character arising as a\n      result of this License or out of the use or inability to use the\n      Work (including but not limited to damages for loss of goodwill,\n      work stoppage, computer failure or malfunction, or any and all\n      other commercial damages or losses), even if such Contributor\n      has been advised of the possibility of such damages.\n\n   9. Accepting Warranty or Additional Liability. While redistributing\n      the Work or Derivative Works thereof, You may choose to offer,\n      and charge a fee for, acceptance of support, warranty, indemnity,\n      or other liability obligations and/or rights consistent with this\n      License. However, in accepting such obligations, You may act only\n      on Your own behalf and on Your sole responsibility, not on behalf\n      of any other Contributor, and only if You agree to indemnify,\n      defend, and hold each Contributor harmless for any liability\n      incurred by, or claims asserted against, such Contributor by reason\n      of your accepting any such warranty or additional liability.\n\n   END OF TERMS AND CONDITIONS\n\n   APPENDIX: How to apply the Apache License to your work.\n\n      To apply the Apache License to your work, attach the following\n      boilerplate notice, with the fields enclosed by brackets \"{}\"\n      replaced with your own identifying information. (Don't include\n      the brackets!)  The text should be enclosed in the appropriate\n      comment syntax for the file format. We also recommend that a\n      file or class name and description of purpose be included on the\n      same \"printed page\" as the copyright notice for easier\n      identification within third-party archives.\n\n   Copyright {yyyy} {name of copyright owner}\n\n   Licensed under the Apache License, Version 2.0 (the \"License\");\n   you may not use this file except in compliance with the License.\n   You may obtain a copy of the License at\n\n       http://www.apache.org/licenses/LICENSE-2.0\n\n   Unless required by applicable law or agreed to in writing, software\n   distributed under the License is distributed on an \"AS IS\" BASIS,\n   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n   See the License for the specific language governing permissions and\n   limitations under the License.\n", "_id": "prism-media@0.4.2", "dist": {"shasum": "6ad976f6a4688cc18ab1227e32eef43344f4c043", "integrity": "sha512-a8hsrMdVaSvaxnQTL31LIA1mUDsnDO1glOZQ8dMvCG+fxPO67UMYS2cNbdGk0RndCiwt1hkqMKLM3yiB1Hqjog==", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-0.4.2.tgz", "fileCount": 38, "unpackedSize": 261369, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcadYUCRA9TVsSAnZWagAAApgP+wab7OxWBTfdpfAg2nr7\n9Ew3jE5BwBApYm3lYcc4WmJ3bFjQeavo8Ys3WCbBWraaOuSjK9xFLXClu2W9\n4LU4mkqpqoVfInqDOBZximpbtEyTJXeTUhvCaLU5ZJya1t2ixZQS1ow/wbbo\n3fw11BhiFCbSkT7Lod28CpbOcJ9tjozio0zTS2Pjw7Xxp5pcw8huoTPBWDMp\nOqB5SFVlMezyc5zSdMwV8q5O7b5yIyrxXALZgAINbj9zTAefRN1jxFPsEEHI\nNfOGSVB4Zlfyf7EkgTDlXk6R49/kbV+fLOdaZOVO4enj1Vy3YGJWOqN0y8uZ\ni8tPdVntdrkKZq8GV1qReLPNH2VIy3BC5uLCjDhZOvIg9LdnJRySc6+IilHE\nDdm+qmTQELUms0DE7ZwHtJ9EO/LL3wFu2si9lHFk2LTntAuQqfPXb/Ugmh6q\nzcWsvVSLq9T1+15QVaP7UZrV/lRveJ1ArGaosZz8MXUIJ/W5H5vXu3cDDzpp\nqo/Gl5vNPpossOMVPG0pZmR6uFYEfN1xGxxA1nhLogB91XKWLc67ot48qnze\nP/bTCXI3C/YxNfxifYd0cTncEB1y1Igh9AaSznY7ju8SkytO89v40Y8GmP/s\njJ8cU26lNBAbfa4O9ib+22xTmR08rYy/xnlxbiTpUbqf8htNcV7ivFbX7rNk\nUF+i\r\n=e/j1\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBx6prrxPTlymUiAsS3bgmkVT6kdw09d4Dawwd7lj5LwAiEA4mutCxRvsxzupJlMADR2ghLyVAj/SEba2wCQ29rcVfE="}]}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_0.4.2_1550439955691_0.6260316404431738"}, "_hasShrinkwrap": false}, "1.0.0": {"name": "prism-media", "version": "1.0.0", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "yarn lint && jest", "docs": "docma"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"docma": "^3.2.2", "eslint": "^4.16.0", "jest": "^22.1.4"}, "jest": {"testURL": "http://localhost/"}, "peerDependencies": {"ffmpeg-binaries": "^4.0.0", "ffmpeg-static": "^2.4.0", "node-opus": "^0.3.1", "opusscript": "^0.0.6"}, "peerDependenciesMeta": {"node-opus": {"optional": true}, "opusscript": {"optional": true}, "ffmpeg-static": {"optional": true}, "ffmpeg-binaries": {"optional": true}}, "licenseText": "                                 Apache License\n                           Version 2.0, January 2004\n                        http://www.apache.org/licenses/\n\n   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION\n\n   1. Definitions.\n\n      \"License\" shall mean the terms and conditions for use, reproduction,\n      and distribution as defined by Sections 1 through 9 of this document.\n\n      \"Licensor\" shall mean the copyright owner or entity authorized by\n      the copyright owner that is granting the License.\n\n      \"Legal Entity\" shall mean the union of the acting entity and all\n      other entities that control, are controlled by, or are under common\n      control with that entity. For the purposes of this definition,\n      \"control\" means (i) the power, direct or indirect, to cause the\n      direction or management of such entity, whether by contract or\n      otherwise, or (ii) ownership of fifty percent (50%) or more of the\n      outstanding shares, or (iii) beneficial ownership of such entity.\n\n      \"You\" (or \"Your\") shall mean an individual or Legal Entity\n      exercising permissions granted by this License.\n\n      \"Source\" form shall mean the preferred form for making modifications,\n      including but not limited to software source code, documentation\n      source, and configuration files.\n\n      \"Object\" form shall mean any form resulting from mechanical\n      transformation or translation of a Source form, including but\n      not limited to compiled object code, generated documentation,\n      and conversions to other media types.\n\n      \"Work\" shall mean the work of authorship, whether in Source or\n      Object form, made available under the License, as indicated by a\n      copyright notice that is included in or attached to the work\n      (an example is provided in the Appendix below).\n\n      \"Derivative Works\" shall mean any work, whether in Source or Object\n      form, that is based on (or derived from) the Work and for which the\n      editorial revisions, annotations, elaborations, or other modifications\n      represent, as a whole, an original work of authorship. For the purposes\n      of this License, Derivative Works shall not include works that remain\n      separable from, or merely link (or bind by name) to the interfaces of,\n      the Work and Derivative Works thereof.\n\n      \"Contribution\" shall mean any work of authorship, including\n      the original version of the Work and any modifications or additions\n      to that Work or Derivative Works thereof, that is intentionally\n      submitted to Licensor for inclusion in the Work by the copyright owner\n      or by an individual or Legal Entity authorized to submit on behalf of\n      the copyright owner. For the purposes of this definition, \"submitted\"\n      means any form of electronic, verbal, or written communication sent\n      to the Licensor or its representatives, including but not limited to\n      communication on electronic mailing lists, source code control systems,\n      and issue tracking systems that are managed by, or on behalf of, the\n      Licensor for the purpose of discussing and improving the Work, but\n      excluding communication that is conspicuously marked or otherwise\n      designated in writing by the copyright owner as \"Not a Contribution.\"\n\n      \"Contributor\" shall mean Licensor and any individual or Legal Entity\n      on behalf of whom a Contribution has been received by Licensor and\n      subsequently incorporated within the Work.\n\n   2. Grant of Copyright License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      copyright license to reproduce, prepare Derivative Works of,\n      publicly display, publicly perform, sublicense, and distribute the\n      Work and such Derivative Works in Source or Object form.\n\n   3. Grant of Patent License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      (except as stated in this section) patent license to make, have made,\n      use, offer to sell, sell, import, and otherwise transfer the Work,\n      where such license applies only to those patent claims licensable\n      by such Contributor that are necessarily infringed by their\n      Contribution(s) alone or by combination of their Contribution(s)\n      with the Work to which such Contribution(s) was submitted. If You\n      institute patent litigation against any entity (including a\n      cross-claim or counterclaim in a lawsuit) alleging that the Work\n      or a Contribution incorporated within the Work constitutes direct\n      or contributory patent infringement, then any patent licenses\n      granted to You under this License for that Work shall terminate\n      as of the date such litigation is filed.\n\n   4. Redistribution. You may reproduce and distribute copies of the\n      Work or Derivative Works thereof in any medium, with or without\n      modifications, and in Source or Object form, provided that You\n      meet the following conditions:\n\n      (a) You must give any other recipients of the Work or\n          Derivative Works a copy of this License; and\n\n      (b) You must cause any modified files to carry prominent notices\n          stating that You changed the files; and\n\n      (c) You must retain, in the Source form of any Derivative Works\n          that You distribute, all copyright, patent, trademark, and\n          attribution notices from the Source form of the Work,\n          excluding those notices that do not pertain to any part of\n          the Derivative Works; and\n\n      (d) If the Work includes a \"NOTICE\" text file as part of its\n          distribution, then any Derivative Works that You distribute must\n          include a readable copy of the attribution notices contained\n          within such NOTICE file, excluding those notices that do not\n          pertain to any part of the Derivative Works, in at least one\n          of the following places: within a NOTICE text file distributed\n          as part of the Derivative Works; within the Source form or\n          documentation, if provided along with the Derivative Works; or,\n          within a display generated by the Derivative Works, if and\n          wherever such third-party notices normally appear. The contents\n          of the NOTICE file are for informational purposes only and\n          do not modify the License. You may add Your own attribution\n          notices within Derivative Works that You distribute, alongside\n          or as an addendum to the NOTICE text from the Work, provided\n          that such additional attribution notices cannot be construed\n          as modifying the License.\n\n      You may add Your own copyright statement to Your modifications and\n      may provide additional or different license terms and conditions\n      for use, reproduction, or distribution of Your modifications, or\n      for any such Derivative Works as a whole, provided Your use,\n      reproduction, and distribution of the Work otherwise complies with\n      the conditions stated in this License.\n\n   5. Submission of Contributions. Unless You explicitly state otherwise,\n      any Contribution intentionally submitted for inclusion in the Work\n      by You to the Licensor shall be under the terms and conditions of\n      this License, without any additional terms or conditions.\n      Notwithstanding the above, nothing herein shall supersede or modify\n      the terms of any separate license agreement you may have executed\n      with Licensor regarding such Contributions.\n\n   6. Trademarks. This License does not grant permission to use the trade\n      names, trademarks, service marks, or product names of the Licensor,\n      except as required for reasonable and customary use in describing the\n      origin of the Work and reproducing the content of the NOTICE file.\n\n   7. Disclaimer of Warranty. Unless required by applicable law or\n      agreed to in writing, Licensor provides the Work (and each\n      Contributor provides its Contributions) on an \"AS IS\" BASIS,\n      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or\n      implied, including, without limitation, any warranties or conditions\n      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A\n      PARTICULAR PURPOSE. You are solely responsible for determining the\n      appropriateness of using or redistributing the Work and assume any\n      risks associated with Your exercise of permissions under this License.\n\n   8. Limitation of Liability. In no event and under no legal theory,\n      whether in tort (including negligence), contract, or otherwise,\n      unless required by applicable law (such as deliberate and grossly\n      negligent acts) or agreed to in writing, shall any Contributor be\n      liable to You for damages, including any direct, indirect, special,\n      incidental, or consequential damages of any character arising as a\n      result of this License or out of the use or inability to use the\n      Work (including but not limited to damages for loss of goodwill,\n      work stoppage, computer failure or malfunction, or any and all\n      other commercial damages or losses), even if such Contributor\n      has been advised of the possibility of such damages.\n\n   9. Accepting Warranty or Additional Liability. While redistributing\n      the Work or Derivative Works thereof, You may choose to offer,\n      and charge a fee for, acceptance of support, warranty, indemnity,\n      or other liability obligations and/or rights consistent with this\n      License. However, in accepting such obligations, You may act only\n      on Your own behalf and on Your sole responsibility, not on behalf\n      of any other Contributor, and only if You agree to indemnify,\n      defend, and hold each Contributor harmless for any liability\n      incurred by, or claims asserted against, such Contributor by reason\n      of your accepting any such warranty or additional liability.\n\n   END OF TERMS AND CONDITIONS\n\n   APPENDIX: How to apply the Apache License to your work.\n\n      To apply the Apache License to your work, attach the following\n      boilerplate notice, with the fields enclosed by brackets \"{}\"\n      replaced with your own identifying information. (Don't include\n      the brackets!)  The text should be enclosed in the appropriate\n      comment syntax for the file format. We also recommend that a\n      file or class name and description of purpose be included on the\n      same \"printed page\" as the copyright notice for easier\n      identification within third-party archives.\n\n   Copyright 2019 Amish Shah\n\n   Licensed under the Apache License, Version 2.0 (the \"License\");\n   you may not use this file except in compliance with the License.\n   You may obtain a copy of the License at\n\n       http://www.apache.org/licenses/LICENSE-2.0\n\n   Unless required by applicable law or agreed to in writing, software\n   distributed under the License is distributed on an \"AS IS\" BASIS,\n   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n   See the License for the specific language governing permissions and\n   limitations under the License.\n", "_id": "prism-media@1.0.0", "dist": {"shasum": "a289eeb9fa37ca523dc41667ec39c3bebab3d34c", "integrity": "sha512-yPnRPlBT4cgZlZW7951Fjjo8NCUhVnqgzbQEJIqtawRWdxFbI4D5BU3sbZ/LRH5ZsWyNRHDqxnUeTTSDrKAZMw==", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-1.0.0.tgz", "fileCount": 36, "unpackedSize": 43742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbpBsCRA9TVsSAnZWagAA02kP/2zEjOaFueZMEAJRNjpn\nDA6UfEzheu4kP0IGLxH97CUqyX6cRSvERHQ2rZ9xoW4O9h9qoZhENR3FEs3m\npRYNREeAEhklqroU9/OJW8cKjV64j57rDcpThAjUd/3P7qvwd39+Ip7/+NlG\nVlC9N01t5KbaiofhuIme2J+jzkjImYV0yPW3Wc68HGDJmw55MHldcrjn+AAa\nJQdrySZtc1eDswGaVA11kB7BqSiWTy6tauZPjFvsOzlsFsNHJHZ2Sv2Srl9y\n4WUHnO2gARBMUzHrXf0dWqgw+4JAYRBH9stUwD6YvKr0LncH57T9SdqajYep\nuwhX2wkBzv2xai6f9tsQJ44jr5fF22ZcxqSQG0WavrRj28il7tRHuB349RAO\nvNME/nWjriJe9erU173ShSG7+HH+zsSX5dNIjOMci/LaJuULcOxn/Nm2N52m\n4rV3rV2vafZDsFu8jxTDCprNQXMMhDjuQNDRvnZrLeED7k3ta0+n82hmfwsh\nls/YddxNVd6sDBxkd/5zG4E7FXyE6cmXCPk/yp0sMdNIe1JvQPejQi1tjyAg\nWEw9ZOP0yU1chxUEQkLjU8ux9yXuEEYy3XEtYkJ0RBuepFqGY4M6tIOl7PS1\n9IlzRtNiJRuFaoBsI+wMkYuTArRPpVlIcUNyky67x4SqgVJameP1TywVNRDl\nCJPP\r\n=1LnD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFL7pEo8OS0B3bm81E8EKpSLKCNZZuyiNOdOvEMs9n2wIgXnV6+7NPpqDNlu96mcGNcAzIcMIAYBMjfAFBMCQ3Rks="}]}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_1.0.0_1550749804165_0.6877026258573802"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "prism-media", "version": "1.0.1", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "yarn lint && jest", "docs": "docma"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"docma": "^3.2.2", "eslint": "^4.16.0", "jest": "^22.1.4"}, "jest": {"testURL": "http://localhost/"}, "peerDependencies": {"ffmpeg-binaries": "^4.0.0", "ffmpeg-static": "^2.4.0", "node-opus": "^0.3.1", "opusscript": "^0.0.6"}, "peerDependenciesMeta": {"node-opus": {"optional": true}, "opusscript": {"optional": true}, "ffmpeg-static": {"optional": true}, "ffmpeg-binaries": {"optional": true}}, "licenseText": "                                 Apache License\n                           Version 2.0, January 2004\n                        http://www.apache.org/licenses/\n\n   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION\n\n   1. Definitions.\n\n      \"License\" shall mean the terms and conditions for use, reproduction,\n      and distribution as defined by Sections 1 through 9 of this document.\n\n      \"Licensor\" shall mean the copyright owner or entity authorized by\n      the copyright owner that is granting the License.\n\n      \"Legal Entity\" shall mean the union of the acting entity and all\n      other entities that control, are controlled by, or are under common\n      control with that entity. For the purposes of this definition,\n      \"control\" means (i) the power, direct or indirect, to cause the\n      direction or management of such entity, whether by contract or\n      otherwise, or (ii) ownership of fifty percent (50%) or more of the\n      outstanding shares, or (iii) beneficial ownership of such entity.\n\n      \"You\" (or \"Your\") shall mean an individual or Legal Entity\n      exercising permissions granted by this License.\n\n      \"Source\" form shall mean the preferred form for making modifications,\n      including but not limited to software source code, documentation\n      source, and configuration files.\n\n      \"Object\" form shall mean any form resulting from mechanical\n      transformation or translation of a Source form, including but\n      not limited to compiled object code, generated documentation,\n      and conversions to other media types.\n\n      \"Work\" shall mean the work of authorship, whether in Source or\n      Object form, made available under the License, as indicated by a\n      copyright notice that is included in or attached to the work\n      (an example is provided in the Appendix below).\n\n      \"Derivative Works\" shall mean any work, whether in Source or Object\n      form, that is based on (or derived from) the Work and for which the\n      editorial revisions, annotations, elaborations, or other modifications\n      represent, as a whole, an original work of authorship. For the purposes\n      of this License, Derivative Works shall not include works that remain\n      separable from, or merely link (or bind by name) to the interfaces of,\n      the Work and Derivative Works thereof.\n\n      \"Contribution\" shall mean any work of authorship, including\n      the original version of the Work and any modifications or additions\n      to that Work or Derivative Works thereof, that is intentionally\n      submitted to Licensor for inclusion in the Work by the copyright owner\n      or by an individual or Legal Entity authorized to submit on behalf of\n      the copyright owner. For the purposes of this definition, \"submitted\"\n      means any form of electronic, verbal, or written communication sent\n      to the Licensor or its representatives, including but not limited to\n      communication on electronic mailing lists, source code control systems,\n      and issue tracking systems that are managed by, or on behalf of, the\n      Licensor for the purpose of discussing and improving the Work, but\n      excluding communication that is conspicuously marked or otherwise\n      designated in writing by the copyright owner as \"Not a Contribution.\"\n\n      \"Contributor\" shall mean Licensor and any individual or Legal Entity\n      on behalf of whom a Contribution has been received by Licensor and\n      subsequently incorporated within the Work.\n\n   2. Grant of Copyright License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      copyright license to reproduce, prepare Derivative Works of,\n      publicly display, publicly perform, sublicense, and distribute the\n      Work and such Derivative Works in Source or Object form.\n\n   3. Grant of Patent License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      (except as stated in this section) patent license to make, have made,\n      use, offer to sell, sell, import, and otherwise transfer the Work,\n      where such license applies only to those patent claims licensable\n      by such Contributor that are necessarily infringed by their\n      Contribution(s) alone or by combination of their Contribution(s)\n      with the Work to which such Contribution(s) was submitted. If You\n      institute patent litigation against any entity (including a\n      cross-claim or counterclaim in a lawsuit) alleging that the Work\n      or a Contribution incorporated within the Work constitutes direct\n      or contributory patent infringement, then any patent licenses\n      granted to You under this License for that Work shall terminate\n      as of the date such litigation is filed.\n\n   4. Redistribution. You may reproduce and distribute copies of the\n      Work or Derivative Works thereof in any medium, with or without\n      modifications, and in Source or Object form, provided that You\n      meet the following conditions:\n\n      (a) You must give any other recipients of the Work or\n          Derivative Works a copy of this License; and\n\n      (b) You must cause any modified files to carry prominent notices\n          stating that You changed the files; and\n\n      (c) You must retain, in the Source form of any Derivative Works\n          that You distribute, all copyright, patent, trademark, and\n          attribution notices from the Source form of the Work,\n          excluding those notices that do not pertain to any part of\n          the Derivative Works; and\n\n      (d) If the Work includes a \"NOTICE\" text file as part of its\n          distribution, then any Derivative Works that You distribute must\n          include a readable copy of the attribution notices contained\n          within such NOTICE file, excluding those notices that do not\n          pertain to any part of the Derivative Works, in at least one\n          of the following places: within a NOTICE text file distributed\n          as part of the Derivative Works; within the Source form or\n          documentation, if provided along with the Derivative Works; or,\n          within a display generated by the Derivative Works, if and\n          wherever such third-party notices normally appear. The contents\n          of the NOTICE file are for informational purposes only and\n          do not modify the License. You may add Your own attribution\n          notices within Derivative Works that You distribute, alongside\n          or as an addendum to the NOTICE text from the Work, provided\n          that such additional attribution notices cannot be construed\n          as modifying the License.\n\n      You may add Your own copyright statement to Your modifications and\n      may provide additional or different license terms and conditions\n      for use, reproduction, or distribution of Your modifications, or\n      for any such Derivative Works as a whole, provided Your use,\n      reproduction, and distribution of the Work otherwise complies with\n      the conditions stated in this License.\n\n   5. Submission of Contributions. Unless You explicitly state otherwise,\n      any Contribution intentionally submitted for inclusion in the Work\n      by You to the Licensor shall be under the terms and conditions of\n      this License, without any additional terms or conditions.\n      Notwithstanding the above, nothing herein shall supersede or modify\n      the terms of any separate license agreement you may have executed\n      with Licensor regarding such Contributions.\n\n   6. Trademarks. This License does not grant permission to use the trade\n      names, trademarks, service marks, or product names of the Licensor,\n      except as required for reasonable and customary use in describing the\n      origin of the Work and reproducing the content of the NOTICE file.\n\n   7. Disclaimer of Warranty. Unless required by applicable law or\n      agreed to in writing, Licensor provides the Work (and each\n      Contributor provides its Contributions) on an \"AS IS\" BASIS,\n      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or\n      implied, including, without limitation, any warranties or conditions\n      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A\n      PARTICULAR PURPOSE. You are solely responsible for determining the\n      appropriateness of using or redistributing the Work and assume any\n      risks associated with Your exercise of permissions under this License.\n\n   8. Limitation of Liability. In no event and under no legal theory,\n      whether in tort (including negligence), contract, or otherwise,\n      unless required by applicable law (such as deliberate and grossly\n      negligent acts) or agreed to in writing, shall any Contributor be\n      liable to You for damages, including any direct, indirect, special,\n      incidental, or consequential damages of any character arising as a\n      result of this License or out of the use or inability to use the\n      Work (including but not limited to damages for loss of goodwill,\n      work stoppage, computer failure or malfunction, or any and all\n      other commercial damages or losses), even if such Contributor\n      has been advised of the possibility of such damages.\n\n   9. Accepting Warranty or Additional Liability. While redistributing\n      the Work or Derivative Works thereof, You may choose to offer,\n      and charge a fee for, acceptance of support, warranty, indemnity,\n      or other liability obligations and/or rights consistent with this\n      License. However, in accepting such obligations, You may act only\n      on Your own behalf and on Your sole responsibility, not on behalf\n      of any other Contributor, and only if You agree to indemnify,\n      defend, and hold each Contributor harmless for any liability\n      incurred by, or claims asserted against, such Contributor by reason\n      of your accepting any such warranty or additional liability.\n\n   END OF TERMS AND CONDITIONS\n\n   APPENDIX: How to apply the Apache License to your work.\n\n      To apply the Apache License to your work, attach the following\n      boilerplate notice, with the fields enclosed by brackets \"{}\"\n      replaced with your own identifying information. (Don't include\n      the brackets!)  The text should be enclosed in the appropriate\n      comment syntax for the file format. We also recommend that a\n      file or class name and description of purpose be included on the\n      same \"printed page\" as the copyright notice for easier\n      identification within third-party archives.\n\n   Copyright 2019 Amish Shah\n\n   Licensed under the Apache License, Version 2.0 (the \"License\");\n   you may not use this file except in compliance with the License.\n   You may obtain a copy of the License at\n\n       http://www.apache.org/licenses/LICENSE-2.0\n\n   Unless required by applicable law or agreed to in writing, software\n   distributed under the License is distributed on an \"AS IS\" BASIS,\n   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n   See the License for the specific language governing permissions and\n   limitations under the License.\n", "_id": "prism-media@1.0.1", "dist": {"shasum": "3d033c214f9d79ca157bae7985938be65f521c02", "integrity": "sha512-Unmeibfk7b8wiiQFPlCC0GgCRaAejSpe2ElgfDkMi9ZmsqzeTbUnmiLNjyqONeKK+HMYINYHOgJFox5l7FU7YA==", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-1.0.1.tgz", "fileCount": 36, "unpackedSize": 43776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJccA44CRA9TVsSAnZWagAA6yUP/jZcy7NCY7Ch1yPB3aCg\naSPdZx11qSLbaRuZahsIn/i8J025hGycPfpNUnLPI2MLumtFzYEdm/yPGWTk\nUP2HKQNoxtPoB7KR6jgyEYuorsrAZ5n2v5t7icbLCU8n81Qg/wv95GFiBhqb\nrzs4fcegQnMblZYE2NLOInD6o67JnmLqxCe5RZ280mC+wEQsQt2KtXH/VlOB\nsS8XN8zhF7SIQhJNTSrQ7OnYnwCWH4NRFuSUx7+g2fpc279CMXBQqWFv4z+F\nNiQ+3HeSQHLeV8Zgm5e4evnChK/hhGXZzYqGwvGWejz3++yCA0lLfM9iWhzN\nlBV/B3qUeet37eZA35fGR1kGzneztkgW5xjB9oNCjWccO4IeEQYJsFrJbGky\nxfyArclqX73j8q5yk3F9rg5hu85w/AM4K0PZNpHmXSoA7VxAE9AdYicIzWql\nEgkH9M2BBreppmE2A7GPior0+tbsZByGK96EySk+YfVPjlefXn+/XhRYKcHW\nODmoRjQcFk26KcALRMozLWbifbW/eJF6qnX9bH5y0csjlZogVJRgQQxaWTjZ\njMLihBxoj4l01os5PXYpe9rQLyvzz+v6xaBTvgVh0H3Qzqdv+9rKxWCzb+uR\nsrCzeCOyrp2lu80We+fpy8K1yDh9C9OucOKlAa3X+aatLsDoqtDrK9TU5xy2\nPNBg\r\n=aZ9Z\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDuaaVRQbxtxBN5j9qbXUP+92weCW0La/KiJHqIMPbY0gIhAN0Z1zN6DDMKpjADgXFyBj3tCSg8aScaxgfS78F2uclC"}]}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_1.0.1_1550847544057_0.6273653435191484"}, "_hasShrinkwrap": false}, "1.0.2": {"name": "prism-media", "version": "1.0.2", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "yarn lint && jest", "docs": "docma"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"docma": "^3.2.2", "eslint": "^4.16.0", "jest": "^22.1.4"}, "jest": {"testURL": "http://localhost/"}, "peerDependencies": {"ffmpeg-binaries": "^4.0.0", "ffmpeg-static": "^2.4.0", "node-opus": "^0.3.1", "opusscript": "^0.0.6"}, "peerDependenciesMeta": {"node-opus": {"optional": true}, "opusscript": {"optional": true}, "ffmpeg-static": {"optional": true}, "ffmpeg-binaries": {"optional": true}}, "licenseText": "                                 Apache License\n                           Version 2.0, January 2004\n                        http://www.apache.org/licenses/\n\n   TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION\n\n   1. Definitions.\n\n      \"License\" shall mean the terms and conditions for use, reproduction,\n      and distribution as defined by Sections 1 through 9 of this document.\n\n      \"Licensor\" shall mean the copyright owner or entity authorized by\n      the copyright owner that is granting the License.\n\n      \"Legal Entity\" shall mean the union of the acting entity and all\n      other entities that control, are controlled by, or are under common\n      control with that entity. For the purposes of this definition,\n      \"control\" means (i) the power, direct or indirect, to cause the\n      direction or management of such entity, whether by contract or\n      otherwise, or (ii) ownership of fifty percent (50%) or more of the\n      outstanding shares, or (iii) beneficial ownership of such entity.\n\n      \"You\" (or \"Your\") shall mean an individual or Legal Entity\n      exercising permissions granted by this License.\n\n      \"Source\" form shall mean the preferred form for making modifications,\n      including but not limited to software source code, documentation\n      source, and configuration files.\n\n      \"Object\" form shall mean any form resulting from mechanical\n      transformation or translation of a Source form, including but\n      not limited to compiled object code, generated documentation,\n      and conversions to other media types.\n\n      \"Work\" shall mean the work of authorship, whether in Source or\n      Object form, made available under the License, as indicated by a\n      copyright notice that is included in or attached to the work\n      (an example is provided in the Appendix below).\n\n      \"Derivative Works\" shall mean any work, whether in Source or Object\n      form, that is based on (or derived from) the Work and for which the\n      editorial revisions, annotations, elaborations, or other modifications\n      represent, as a whole, an original work of authorship. For the purposes\n      of this License, Derivative Works shall not include works that remain\n      separable from, or merely link (or bind by name) to the interfaces of,\n      the Work and Derivative Works thereof.\n\n      \"Contribution\" shall mean any work of authorship, including\n      the original version of the Work and any modifications or additions\n      to that Work or Derivative Works thereof, that is intentionally\n      submitted to Licensor for inclusion in the Work by the copyright owner\n      or by an individual or Legal Entity authorized to submit on behalf of\n      the copyright owner. For the purposes of this definition, \"submitted\"\n      means any form of electronic, verbal, or written communication sent\n      to the Licensor or its representatives, including but not limited to\n      communication on electronic mailing lists, source code control systems,\n      and issue tracking systems that are managed by, or on behalf of, the\n      Licensor for the purpose of discussing and improving the Work, but\n      excluding communication that is conspicuously marked or otherwise\n      designated in writing by the copyright owner as \"Not a Contribution.\"\n\n      \"Contributor\" shall mean Licensor and any individual or Legal Entity\n      on behalf of whom a Contribution has been received by Licensor and\n      subsequently incorporated within the Work.\n\n   2. Grant of Copyright License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      copyright license to reproduce, prepare Derivative Works of,\n      publicly display, publicly perform, sublicense, and distribute the\n      Work and such Derivative Works in Source or Object form.\n\n   3. Grant of Patent License. Subject to the terms and conditions of\n      this License, each Contributor hereby grants to You a perpetual,\n      worldwide, non-exclusive, no-charge, royalty-free, irrevocable\n      (except as stated in this section) patent license to make, have made,\n      use, offer to sell, sell, import, and otherwise transfer the Work,\n      where such license applies only to those patent claims licensable\n      by such Contributor that are necessarily infringed by their\n      Contribution(s) alone or by combination of their Contribution(s)\n      with the Work to which such Contribution(s) was submitted. If You\n      institute patent litigation against any entity (including a\n      cross-claim or counterclaim in a lawsuit) alleging that the Work\n      or a Contribution incorporated within the Work constitutes direct\n      or contributory patent infringement, then any patent licenses\n      granted to You under this License for that Work shall terminate\n      as of the date such litigation is filed.\n\n   4. Redistribution. You may reproduce and distribute copies of the\n      Work or Derivative Works thereof in any medium, with or without\n      modifications, and in Source or Object form, provided that You\n      meet the following conditions:\n\n      (a) You must give any other recipients of the Work or\n          Derivative Works a copy of this License; and\n\n      (b) You must cause any modified files to carry prominent notices\n          stating that You changed the files; and\n\n      (c) You must retain, in the Source form of any Derivative Works\n          that You distribute, all copyright, patent, trademark, and\n          attribution notices from the Source form of the Work,\n          excluding those notices that do not pertain to any part of\n          the Derivative Works; and\n\n      (d) If the Work includes a \"NOTICE\" text file as part of its\n          distribution, then any Derivative Works that You distribute must\n          include a readable copy of the attribution notices contained\n          within such NOTICE file, excluding those notices that do not\n          pertain to any part of the Derivative Works, in at least one\n          of the following places: within a NOTICE text file distributed\n          as part of the Derivative Works; within the Source form or\n          documentation, if provided along with the Derivative Works; or,\n          within a display generated by the Derivative Works, if and\n          wherever such third-party notices normally appear. The contents\n          of the NOTICE file are for informational purposes only and\n          do not modify the License. You may add Your own attribution\n          notices within Derivative Works that You distribute, alongside\n          or as an addendum to the NOTICE text from the Work, provided\n          that such additional attribution notices cannot be construed\n          as modifying the License.\n\n      You may add Your own copyright statement to Your modifications and\n      may provide additional or different license terms and conditions\n      for use, reproduction, or distribution of Your modifications, or\n      for any such Derivative Works as a whole, provided Your use,\n      reproduction, and distribution of the Work otherwise complies with\n      the conditions stated in this License.\n\n   5. Submission of Contributions. Unless You explicitly state otherwise,\n      any Contribution intentionally submitted for inclusion in the Work\n      by You to the Licensor shall be under the terms and conditions of\n      this License, without any additional terms or conditions.\n      Notwithstanding the above, nothing herein shall supersede or modify\n      the terms of any separate license agreement you may have executed\n      with Licensor regarding such Contributions.\n\n   6. Trademarks. This License does not grant permission to use the trade\n      names, trademarks, service marks, or product names of the Licensor,\n      except as required for reasonable and customary use in describing the\n      origin of the Work and reproducing the content of the NOTICE file.\n\n   7. Disclaimer of Warranty. Unless required by applicable law or\n      agreed to in writing, Licensor provides the Work (and each\n      Contributor provides its Contributions) on an \"AS IS\" BASIS,\n      WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or\n      implied, including, without limitation, any warranties or conditions\n      of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A\n      PARTICULAR PURPOSE. You are solely responsible for determining the\n      appropriateness of using or redistributing the Work and assume any\n      risks associated with Your exercise of permissions under this License.\n\n   8. Limitation of Liability. In no event and under no legal theory,\n      whether in tort (including negligence), contract, or otherwise,\n      unless required by applicable law (such as deliberate and grossly\n      negligent acts) or agreed to in writing, shall any Contributor be\n      liable to You for damages, including any direct, indirect, special,\n      incidental, or consequential damages of any character arising as a\n      result of this License or out of the use or inability to use the\n      Work (including but not limited to damages for loss of goodwill,\n      work stoppage, computer failure or malfunction, or any and all\n      other commercial damages or losses), even if such Contributor\n      has been advised of the possibility of such damages.\n\n   9. Accepting Warranty or Additional Liability. While redistributing\n      the Work or Derivative Works thereof, You may choose to offer,\n      and charge a fee for, acceptance of support, warranty, indemnity,\n      or other liability obligations and/or rights consistent with this\n      License. However, in accepting such obligations, You may act only\n      on Your own behalf and on Your sole responsibility, not on behalf\n      of any other Contributor, and only if You agree to indemnify,\n      defend, and hold each Contributor harmless for any liability\n      incurred by, or claims asserted against, such Contributor by reason\n      of your accepting any such warranty or additional liability.\n\n   END OF TERMS AND CONDITIONS\n\n   APPENDIX: How to apply the Apache License to your work.\n\n      To apply the Apache License to your work, attach the following\n      boilerplate notice, with the fields enclosed by brackets \"{}\"\n      replaced with your own identifying information. (Don't include\n      the brackets!)  The text should be enclosed in the appropriate\n      comment syntax for the file format. We also recommend that a\n      file or class name and description of purpose be included on the\n      same \"printed page\" as the copyright notice for easier\n      identification within third-party archives.\n\n   Copyright 2019 Amish Shah\n\n   Licensed under the Apache License, Version 2.0 (the \"License\");\n   you may not use this file except in compliance with the License.\n   You may obtain a copy of the License at\n\n       http://www.apache.org/licenses/LICENSE-2.0\n\n   Unless required by applicable law or agreed to in writing, software\n   distributed under the License is distributed on an \"AS IS\" BASIS,\n   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n   See the License for the specific language governing permissions and\n   limitations under the License.\n", "_id": "prism-media@1.0.2", "dist": {"shasum": "1e0f097f67d799211177a50d5276fdd2ef1c7fd7", "integrity": "sha512-KCu3SlT1EtS0lvF74NGLSTzr5jrkRajE9n+S2jnUvvdiN5SpjsGj0/HgR1QK9+985Yv8cxjwZnpFRaj+SimDGA==", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-1.0.2.tgz", "fileCount": 36, "unpackedSize": 43898, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcxdX/CRA9TVsSAnZWagAAiYoP/RueWRqiShbCkXQMo/ED\nuAbP5WsrYIB+zUXoYjybTlGfPppA6FwM0wL7meAlKuaVgtZv7+wllv7iCiKf\nUuHRpYSdV4b+6j8McWQYSYPnLy5ft3320PTrYF1+PGty50qkx2El1oCPtd4t\n+1rIai1d/1ReySd8FVBHtzIxehSvEm+pxM9FwzvCeMEQsge57f8eiGBrUKbH\ny2TObCPPCUBzUw0HJr1hs+v06VeqVpx8utnL5jnpsY7U1Do0J4mmllwuadXL\nY2TWIPMZawEh/et+ph+lQHJFH2Lq3z3ZxuwD+HA/aqEfwbCX6llqtuMqWkQ3\n2xihSeejjSpExOv4/lrelavunxEKJ7pi4XnfyUdX2nVxuigCJtCZ25XaYiCV\nWAy4fHxVD8yeELq0ee2sNYflpOblwtiVJVSAHMssh1r3uVyhySs/4wz3+jcV\n86ZKX9vaNcKstJH+S5o5jsvcrwoBSDnyLAMIpgpLQ5n0fGwH/ZMamVvNy1Gw\nFBOswyJg0tO/Vk7ZiWo5Y983uBmWDMpDNyPaSH9KbWWj2vcX9l+l/iaOhaK/\nYV8R5XHu85uNS72T112hrNt8KcjDHdIA+bq9SwodLegVDv4z+6RioBganiFD\ntohKilBU4oL8S5rlJBAiX8dTwWhAW+Q+ZJMf23FjqLjGL4Ebspi609ZMZXVC\nksEQ\r\n=XAhn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDqsl8x7hkQ6Y/tiaJ+MVLybYV5EO/VMECD+Hf11q1Y5wIgU6nTEhAZnCCiCQgsLe1ne856KEGUcsMg3R9r9dWC/KM="}]}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_1.0.2_1556469246662_0.9145613986077197"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "prism-media", "version": "1.1.0", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "yarn lint && jest && yarn docs", "docs": "docma"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"docma": "^3.2.2", "eslint": "^4.16.0", "jest": "^22.1.4"}, "jest": {"testURL": "http://localhost/"}, "peerDependencies": {"ffmpeg-static": "^2.4.0", "node-opus": "^0.3.1", "opusscript": "^0.0.6"}, "peerDependenciesMeta": {"node-opus": {"optional": true}, "opusscript": {"optional": true}, "ffmpeg-static": {"optional": true}}, "gitHead": "0c32f731121c037b6b6b84125483f794a597a4f1", "_id": "prism-media@1.1.0", "_nodeVersion": "11.10.0", "_npmVersion": "6.8.0", "dist": {"integrity": "sha512-W+oxjRyjtd7hw3pefNZuc7YEZ6VICORJvVNfCPs0+7CsJ43CqMjGAYGjPL3hQ82vw03EVra+CiX4zisqOBUUGw==", "shasum": "64fd9abf64719d627b99360bfbe1a81d3ac0c593", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-1.1.0.tgz", "fileCount": 19, "unpackedSize": 44901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXn6uCRA9TVsSAnZWagAAgV8P/0+Yt+z6Qo1CnUpWsxBB\nRy0IpG6do3JRE77QQzNcLRuzo9EeLK8JoouPuoMXPB8Td6IfPt2FYSkePBuw\n8kx9/ZtUIIPi1OxsUixB4K5Hv+Dyw51NuQv5DlNmVBsussxWXZZUoucc18II\nl30mtYIcn4uuTfSYTUClUtfaX+bIc4o4/Rl+19qOQ0o7QCEHWXAdHOx9WoJ+\n+366x7kARDtLdI9Ezs2QiM9a7s9d9gx+FARusGSdcZAkFUUANZ5Ijp+ho6bM\nW5xLTFrLKFsiYbO4bQRbvjQ9phoq7fS5Jy4qejRKIhsvx8hkGMjwgBELCKRx\nLWZv+InyRl03uU5uQav75OuQ0bx2d4t4PoA1AW5Zim4BkkkDL1JT3nEHbh/P\nPxlKIXLcKVjrZWdZbTskqv0eu3WMKJoLLWgjGkii6HPe6ra5UtUgfO2viF8l\nMjxfhC+M+nyiSLbaEwD1dIPH8hlNvnK4CRP9S3qDsJwLhHrRqNuTGQ+TE5Ov\ndolD7ua04ns/4uzY4sGmTaxRDwUdPMdBATjk+S1DZfblECZbfRydlx8voRsY\n7hBBOSa7tsP6YeHIhFUCYgJ7xsspBkv6M72fNLCa+qTudtqAxnY9ymbl8LFR\nVFIYLWqWpwMyG2QptuNIq114pw2gVYrzKPT62+7CABAJgg2zj0dg1sSHzmKD\nrAmy\r\n=epI/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDOfXQW3jAy3gXwqtBj0JUOTExzT1ntUagYOX2gwLa31QIhAMEPtU9YuBabev2LnTV4OjT9ijSRWcjq0Np8b3REkEtq"}]}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_1.1.0_1566473902338_0.5794015104088024"}, "_hasShrinkwrap": false}, "1.2.0": {"name": "prism-media", "version": "1.2.0", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest && npm run docs", "docs": "docma"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"docma": "^3.2.2", "eslint": "^6.8.0", "jest": "^24.9.0"}, "jest": {"testURL": "http://localhost/"}, "peerDependencies": {"ffmpeg-static": "^2.4.0 || ^3.0.0", "@discordjs/opus": "^0.1.0", "node-opus": "^0.3.1", "opusscript": "^0.0.6"}, "peerDependenciesMeta": {"@discordjs/opus": {"optional": true}, "node-opus": {"optional": true}, "opusscript": {"optional": true}, "ffmpeg-static": {"optional": true}}, "gitHead": "ae4b35d72bfe2e57681eb8f69585287d863326a8", "_id": "prism-media@1.2.0", "_nodeVersion": "12.14.1", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-zjcO/BLVlfxWqFpEUlDyL1R9XXMquasNP4xpeYDPPZi/Zcz0i6OXoqcvxOLgbRVPsJXVd29vlYmRx2bts+hzEw==", "shasum": "8528aa71dc906bfc1566e9bb59d57986a7fd0e20", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-1.2.0.tgz", "fileCount": 19, "unpackedSize": 44008, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeIETzCRA9TVsSAnZWagAAq1gP/Rvs4pzoifw0jlPUrZ+2\nEsFzNRY8PnwBLXapVW6gmJ9ZmJz6QahJcln/GURqINtGexwTeBjVHVnfo4lK\nWOY8wrmInx7HKObjmRHnbMf4FcZEiEbbURsre/ZjPqfQxbeCcPM9IqJqRYLz\ncgaDG4muOQ/uTIwKJOsYp2zqWFyFf9hZGSQ/+G+kmHgB7TJJczQN1AX2pYLA\nU7a5+kpRdizmxVprcw0rO8k1Tky1jk4X1cMXuPEdXPZhimaccy1B0B5f07Ez\nxPvPiabG24y8uJZX3T4MqvgyDFIxfwn7GOorFhLGT3COaDdFhzX28iNTYHBT\njpas5AWFdARsLC2exhM7nmiiy0KtfuhLi54W3nNkcqgnnvvUpqF46DoJcEFY\nkT29NTS/6BsT3PO4t5gNZ8Iyxl9643SpkQjOcsws8JFBrHYMcyuYWu1wCK0Q\ncM3qlQPxAHXaHjNKMnuTkMt9njxn5Upld/CQHAVPaa47u4zH+mycw1446iYI\ng32xHPy0xX0kdF/16OJIe+7POwe4EIqep6uwbqS6PDo2yXhbfEZTkCbZwrCr\nYd4nYRQ0182oRrIDD+6YfPidqJNLiq644IVyG4Nqrc2ppUmzZjFJt/sbKbsA\n8qObyNvl6p3xSj0zi7EElrK/W+fVmIgrWkV1dJ0t7A+LP+9+ZddLygJNGMqJ\n29sH\r\n=Huke\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCsrCEKjGYPrP1Xgoc6bQL6oyN1n7UbJ3LsCLcQcVDLNwIgePCn3tv6coN/U1q/ptNn3kBsMcJdY7BWeUC/Moh414I="}]}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_1.2.0_1579173105993_0.22272372911318072"}, "_hasShrinkwrap": false}, "0.0.4": {"name": "prism-media", "version": "0.0.4", "description": "Makes transcoding media easier", "main": "src/index.js", "scripts": {"lint": "eslint src && eslint test", "test": "npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"eslint": "^6.8.0"}, "gitHead": "43662f645f2128f0c01e4146ecfd6b4664387dc6", "_id": "prism-media@0.0.4", "_nodeVersion": "12.14.1", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-dG2w7WtovUa4SiYTdWn9H8Bd4JNdei2djtkP/Bk9fXq81j5Q15ZPHYSwhUVvBRbp5zMkGtu0Yk62HuMcly0pRw==", "shasum": "df5ddc6463670c97ff0e9cbac3c3e0db18df326f", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-0.0.4.tgz", "fileCount": 14, "unpackedSize": 26096, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeIE4LCRA9TVsSAnZWagAALgYP/j+kBp29ry3QyuX1sWjw\nXqF5ldXxilcN6oxoZTafo/Xc3VmkkPKEFGV+dl3zBB59DmSMv+Mu5b5+kgn6\nBtMasIv7x8ADvabilVOm1vMARJ9N/FjIe0RBB0ke1qBqCffiJtbkCKy4HQUb\n0tSwH5L/WCEg//b3SR/hysK5+7rPicWGGtxrBCwFJ6hoTa94hUbQ2SVB9+Av\nL3CESrPYk3n7AmIKZ8daBZNp3EYYRmFE+1jR2uM3FKVFS4OycvI7msVapH48\ndOVrMJknZK+uICD+u6uJ8O6cFwzgjYjkGn3sePQnlNhXeROEJPRL2O1zqsun\nxXhGSq4ZKbpOUgFxZz48i8jFeHCSmiiBwta4V3zrE2QoREDRv/Rp2G+FQRDE\nMYig1McWsRmBI0+v22LTHR5HOI5flJlMOpAkD6ptRRaglfQkxAt2O4WW7nlM\nGjM1khDZcmeOIpUc+XK8lsZ+Lo6O73NcX1BHnx7gB6MdfGBXmxBfRx+T1bmf\nB9bZr0ZdeAMFydFz0H0KoRSU9L3AQGDG64m9+F8a//e1MeIVZWmm0o9c0X1i\nWqBplUblte/qfHzRTq/bLYVDoyyua2SlkylwB8MoyonEUNC4mFDc/zimnT7Y\nxLTo+pMuArENX2BhxeSBqdJ0MmtypdMX/SegElaVTPRhEN8bGQS2WNAB1MQd\nrE5q\r\n=cK2p\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQClwVtJ5Lbh3f0VbZTnFAuNjgpIOiuL2hJwlrR1YAV3oQIhAKSUn0MtbEyJxJHG89LZrsV0HbLawjOyyoSRCUfgCoFK"}]}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_0.0.4_1579175434891_0.3898660414628732"}, "_hasShrinkwrap": false}, "1.2.1": {"name": "prism-media", "version": "1.2.1", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest && npm run docs", "docs": "docma"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"docma": "^3.2.2", "eslint": "^6.8.0", "jest": "^24.9.0"}, "jest": {"testURL": "http://localhost/"}, "peerDependencies": {"ffmpeg-static": "^2.4.0 || ^3.0.0", "@discordjs/opus": "^0.1.0", "node-opus": "^0.3.1", "opusscript": "^0.0.6"}, "peerDependenciesMeta": {"@discordjs/opus": {"optional": true}, "node-opus": {"optional": true}, "opusscript": {"optional": true}, "ffmpeg-static": {"optional": true}}, "gitHead": "bbf60e0e22f9194586a4b30ffb9f49d20255041c", "_id": "prism-media@1.2.1", "_nodeVersion": "12.14.1", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-R3EbKwJiYlTvGwcG1DpUt+06DsxOGS5W4AMEHT7oVOjG93MjpdhGX1whHyjnqknylLMupKAsKMEXcTNRbPe6Vw==", "shasum": "168f323712bcaacb1d70ae613bf9d9dc44cf43d4", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-1.2.1.tgz", "fileCount": 19, "unpackedSize": 43438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVnUxCRA9TVsSAnZWagAAOR4P/25KTn8UQL4DZtCCae3u\nBxE8CM8/l0ulTe0NnDIyJdndLSQFZDd2SBDJz8ECDA7ocTaFSQKlDqd7Y6In\nzP/LDqc5xclSj6WHmpl8zt1OgDybJtV8NSFP1MYZagFZ6jHvAj+4jCDuDp0j\njNHTJmxaAdiWbXBffi33EEGG+LOiYUosS6JRmGI+da18CHvdw3wngLyemM9v\nc1ROpSNPgbqRgYAgAd1dQM86JcgmjOdWwjPHzuG1TsAj+1j3Zep7U31NVmw4\npX/VCkBjyQa7oL3j18u3zS13SSGPK7VgTHio3y5SOvP4Uqfssi28e4vzplYs\neMAJR1asCm74Xn41CJfIwSLfN57w85MaxdqQIviXjk6X48Id76GU9zJx0LBi\nIYL3rvMTpjpEfhod1/16lvfQE/ylyGupcf/Ywa/lXLmQTZ0y8X5LkQv+pmdb\nKgUfYZXqlnZXEPK21/fkcUPj46ZcFWco2DNAInqvU2rDBU+LQnfyV74Q+2lP\n0gVb6Y4MuIyXmIRf56+xYpFfUW+QGlgPSk2gh/cNtt7ubL/SJKGu2eH3lNRT\nRlkcScHu+BSoK1+MZsW+I29YadI6SUi/qJAtXjhNyKtIjIx8PwPAso0ilewc\ncjwwysKXQTSdvHYUBdHvlu4rFRz1ACiXmWs4PONLrR5Sh3BTf57BVmaHtCZZ\ngZvV\r\n=WTE3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2g62pppBULFfkBsug1GSzEg69iJ2k8tvLG8LTyrX0EQIgcS2de3H5hDwOnLU1Tc1KZ32XMsptRMk1TArjA6OFfy0="}]}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_1.2.1_1582724400736_0.20763864591975834"}, "_hasShrinkwrap": false}, "1.2.2": {"name": "prism-media", "version": "1.2.2", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest && npm run docs", "docs": "docma"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"docma": "^3.2.2", "eslint": "^6.8.0", "jest": "^24.9.0"}, "jest": {"testURL": "http://localhost/"}, "peerDependencies": {"ffmpeg-static": "^2.4.0 || ^3.0.0", "@discordjs/opus": "^0.1.0", "node-opus": "^0.3.1", "opusscript": "^0.0.6"}, "peerDependenciesMeta": {"@discordjs/opus": {"optional": true}, "node-opus": {"optional": true}, "opusscript": {"optional": true}, "ffmpeg-static": {"optional": true}}, "gitHead": "3512c91be6d7bf954ac95dc50e6e39f6ffe3bf99", "_id": "prism-media@1.2.2", "_nodeVersion": "12.14.1", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-I+nkWY212lJ500jLe4tN9tWO7nRiBAVdMv76P9kffZjYhw20raMlW1HSSvS+MLXC9MmbNZCazMrAr+5jEEgTuw==", "shasum": "4f1c841f248b67d325a24b4e6b1a491b8f50a24f", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-1.2.2.tgz", "fileCount": 19, "unpackedSize": 43484, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerVlBCRA9TVsSAnZWagAA1WsP/1CN2KJwfuU2DVEbOJxn\nl7uAAQXWFu7c/d0Mk6tDeqHPq86rQSZp5lEA1vKmBH0OGzOVjrBV2DVocNcw\nzsVmgk7a3G2zj0ScelZGB5R841bobPAcLiNHxiKH0wCyCjrWGPgGavL0TmhA\nD8wOctf1WP5N8ssfG3INJ+euHFls3AiPmh5qYm8xJaIm5eyQJiDEqWZsx2+3\nHbIXhbLPwmtVEYx3KmpCY0KYoSDDC8muhhixGfyMUPZJIFL9ZMRJyVIrX5Zc\ndt+4cNeEcNTEVutUWscSR4H4TMokdgpppc5PBxiNpC4ykDfldhml5OfqpGka\nZF7sqkhx1JDMwchMQcU/N66CTCgAum5/zm9koKx5d5p/jn4O+qLlzR075EqX\nAFIibbjQ+Dwrh7keU5XezSt3VhsvZpkPErb/Oz/8AC17INLjChcRwX06u9OH\ndVO3Oh+gU1Ur+gdxvRqSWBe41K5e4zOIGSVMfiUtSSaHW3x2+dJV69v9UCm0\nJicQi4tbOjD6Q5BgnDEEenPe1kjyrxekN3V9iTBdzTK4SA2SsEVtAM67CexH\n3+Q5gg3u+yMwqUaSWYOz7jWBNhZxLLHd1lYyvZRFUPJB1pBht64afG5iXCeu\njSYAeqpCwP9bB2S6ivsaMNdelc2d53MdSEZKWeEIW4R1jBnp6AMOROrakwbR\ngj0u\r\n=K72D\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDFA7GV8hDGjCWRN92aN2MC5io/XHlCxO22R0AMWT0ZpwIhANiM5cfEGUkuCtcL0TgCoh2ChF3nepBkhaP3Xx3Dw+SY"}]}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_1.2.2_1588418880694_0.7073651169738735"}, "_hasShrinkwrap": false}, "1.2.3": {"name": "prism-media", "version": "1.2.3", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest && npm run docs", "docs": "docma"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"docma": "^3.2.2", "eslint": "^7.14.0", "jest": "^26.6.3"}, "jest": {"testURL": "http://localhost/"}, "peerDependencies": {"ffmpeg-static": "^4.2.7 || ^3.0.0 || ^2.4.0", "@discordjs/opus": "^0.3.3", "node-opus": "^0.3.3", "opusscript": "^0.0.7"}, "peerDependenciesMeta": {"@discordjs/opus": {"optional": true}, "node-opus": {"optional": true}, "opusscript": {"optional": true}, "ffmpeg-static": {"optional": true}}, "gitHead": "0dea3cb80f8a9e4c9fec166d666e84b8506ad81e", "_id": "prism-media@1.2.3", "_nodeVersion": "14.9.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-fSrR66n0l6roW9Rx4rSLMyTPTjRTiXy5RVqDOurACQ6si1rKHHKDU5gwBJoCsIV0R3o9gi+K50akl/qyw1C74A==", "shasum": "37bbb11726674a73fe56a2df4de76aa91d2141b7", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-1.2.3.tgz", "fileCount": 18, "unpackedSize": 44530, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfyBDWCRA9TVsSAnZWagAA3MwP/j33mPKyemCk2ZVY7715\njFHFH7SWv+IvWoH67oNoq90Zk0U1EQfn8PmgyKx4TbVyi0R/sjeB01YnChTt\nPYEMF1Rz7NAVVqmxWJrFw193IzAbvTUai1a9tnCK3r1Az5TV+gPu5he3pbiq\nW+gZYpjSG3NlBrNuTRsVutT4sX2xROrakTTJ4N2PBIx7R3/E0zcmwA/Eh06y\noN9KLPI5wt3K53/1b+p3t5qoiOGWo2izY6q5njnYfidIMbVQm3AAZOeSSrlO\nUx20ybYB9Df6kNyCE1DRwM2St4nkvBiAMoMDEuEmb32HNGajJO4VF22GalP/\nd9gB/d2SrAYHBB/vw+c4AKPcuPuX/VmWIbWGgCuPfFphsluoHLJJ76QqHE3v\n3ffC5WpZPCs8oWSRpb8ukGtRCf+EZzb4C5jLgspcS4X3D/SNczaU+TPpNJji\n/bY7l++MXha7lLklcL7eEt/D1BY1D018ooODMw/4a9uMRSnBDT5yCgiDNkzA\n8jkipdsgmKWAd6WNaOXgfes7v9BIZuI65/ffkSyYieRdRT/9P59yRf6GT1Ju\nf8HZRcUpXuzxAVQbh1yPn74sexsqffwjS8AMYvmVS5lwbzTp/IKojlcP4tpc\nn9w6tizQBGrFqvprK8N25LzUpwSMIPl7OEP9B5Xgw87pJj1Jn+8Jy4zcg3ec\nC9I7\r\n=h01G\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEDf0+BHIRYVTLd/Rtd5dWYZS2K6/Up544T6BfuWvr8aAiB1c0k6zJoCpmk21XzyaQZAvQRrqnpjeYRbpyuVGNm9SA=="}]}, "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_1.2.3_1606947029350_0.6194960917747061"}, "_hasShrinkwrap": false}, "1.2.4": {"name": "prism-media", "version": "1.2.4", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest && npm run docs", "docs": "docma"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"docma": "^3.2.2", "eslint": "^7.14.0", "jest": "^26.6.3"}, "jest": {"testURL": "http://localhost/"}, "peerDependencies": {"@discordjs/opus": "^0.3.3", "ffmpeg-static": "^4.2.7 || ^3.0.0 || ^2.4.0", "node-opus": "^0.3.3", "opusscript": "^0.0.7"}, "peerDependenciesMeta": {"@discordjs/opus": {"optional": true}, "node-opus": {"optional": true}, "opusscript": {"optional": true}, "ffmpeg-static": {"optional": true}}, "gitHead": "1eec0ac4f507ee878bf6d643100c30d6890cf4f3", "_id": "prism-media@1.2.4", "_nodeVersion": "14.9.0", "_npmVersion": "7.5.2", "dist": {"integrity": "sha512-ZETNoW9UMRzNSIZiTQTPn4Bv+nnnfX/S+C/I2HMzFrMFU/oPzorQpcmygVj8lGt7JQs+sq8fMQ0pywvd1uoRvg==", "shasum": "dcd9a07b13ca01f0be6374284d9c48b6cc6e6032", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-1.2.4.tgz", "fileCount": 18, "unpackedSize": 44606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGuqrCRA9TVsSAnZWagAAnskP/0PoBLKvm3FoQO7rIA21\n+WINeObeDo/ddtdnwX6JkaqIMBHwITkYH8q8Ez9oaUIl5OeMotMXy2UOUrBq\nJnid3ksn1JIgn+jK2zK3tDYTaY6v/+PfK3bd+bkTaaLJPHfsHbgwXA9ARTXT\nuS6LJnnw7qLVl0qJDtLlsthTtVRTq3KibmI0ZJ5Q12h052oTkfr0/s21HyrY\nfLJaRllqLd3dSwuKOfGY3Yg6lfjQj/Bki6M1DxYTTRLwJK+gHJDSAgKzn7YY\noBskD0Iu8OCIZoEwlYnYT2pPzFI9B8MKDuqL0Y4GzoKFi2jPYGJLgfvl1fce\nizY3DWUERhtshUNKSBhj/KKlmi61Yf8evhyiGVC3Idk1cz1goFWR9nUGrLvx\n6VAMjRhzFjettPzhRyQmGbWDv6zdjceZ2QpgXZVsqNqyYZcqYy51OSuLk+vU\nF2YtTR8h1k3GV60LKg/x++qP5OCOrhkzjZFX4fWz7DR+1I1fc/rkEVKQngzk\n0s6Npnp6SayFtDQ3aN1iy7kT2Ywn/7MjIJtMIh07oCzppvNNdMtwYB3FY4Tp\nHcI+bXCWkH8Ad/3B8qkN9p/nOdaEQ1h1Jf1Y6NTjlmZ44e+z5w6x5SwozsB7\n2rXfwOuK3ijx2q1SgeWerZNdUwkzSD8TmpnOWilpYTRLoPBXOdQ875cZSpWX\nACNE\r\n=hmlk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDgxPBEb0pQ8OtFoqIv0DJQnUJSvFkUtDZ0VS7yCfv9yQIhAL58KowHYAl1hEOW+A9J75yEfm0ClW0396WgCQJSZ2qz"}]}, "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_1.2.4_1612376747268_0.26233219579569145"}, "_hasShrinkwrap": false}, "1.2.5": {"name": "prism-media", "version": "1.2.5", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest && npm run docs", "docs": "docma"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"docma": "^3.2.2", "eslint": "^7.14.0", "jest": "^26.6.3"}, "jest": {"testURL": "http://localhost/"}, "peerDependencies": {"@discordjs/opus": "^0.4.0", "ffmpeg-static": "^4.2.7 || ^3.0.0 || ^2.4.0", "node-opus": "^0.3.3", "opusscript": "^0.0.7"}, "peerDependenciesMeta": {"@discordjs/opus": {"optional": true}, "node-opus": {"optional": true}, "opusscript": {"optional": true}, "ffmpeg-static": {"optional": true}}, "gitHead": "be6917576993fe13b49f80a06800e947e376b90f", "_id": "prism-media@1.2.5", "_nodeVersion": "14.9.0", "_npmVersion": "7.5.2", "dist": {"integrity": "sha512-YqSnMCugv+KUNE9PQec48Dq22jSxB/B6UcfD9jTpPN1Mbuh+RqELdTJmRPj106z3aa8ZuXrWGQllXPeIGmKbvw==", "shasum": "a3fcb4f7873336d6fcf420213434c1bf5272efe1", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-1.2.5.tgz", "fileCount": 18, "unpackedSize": 44606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGvrCCRA9TVsSAnZWagAAb8IP/2LORd4BtlIJEBz+5WE+\nx15wrFGZSd1GNCZ901QeOX65yUs0KxMAhkJcRzG+jkG7IMYYKuhA3PQ5C6Ae\nS/lj/k7h2YZtSmiNux3sFzjkyyxyar9BIupg2h38PrKgP4OCLwKFTsBXlCsn\n3Hjo1AwEDr8958i9733CbcsRdEvx1JdVUfvhYO42DTU2RtxKKsykC82/8OYR\npq1EDOotixQUwLCKfU6n5YAoqd8lmh6oHxHmW9aXTks3OtsFfA50Rm9VEPm7\n6+itYhHxO/HXvXs/mwXjSpW/B1iJKrxjOv/mLM0ckQ/jE1fyrAc9l7wgRH+U\nu0YeTD2Tw1eTyUuHHB3U7lsCUNDhDYi01nBZDEHbPc0F4uKfRP7cCqWmqrne\nCvGw71TEf1eKvx4bPMsFsZJnmKDVFc8VDxtBd165H9DXnsw0nZvSnRnoBS5Q\nmOTVEBAqd8rC0pr4XaqUA1tF4g/mHn53TTw1dqM+9/LjDv0tKXqQ0F0vCKb3\nWy5LVQuw/2/yehpmTfq8K7OF03ncW778/awZW7TqfkeA+IBYb3A/BlGUOsX2\nbX9EQ9DO+u7FF9lkldnnHomhgpfprAGendGmiiPFLWeu8hVx/A2P2kUMgEnQ\nj7B6Jtlmc5UMbug7EJ4ndgl6bowhcKiq1YpuS067CN24sAs4ughkWRk4CCxd\nLjyW\r\n=2FrD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBpRvVTmP5clnQDn8fXxNL5K6RZVOLUpcsC01+YM+/jfAiEAl2tOjRiNpl2ZwKTxTtBDAPNPAnkLhEIHRT66dcTCP+k="}]}, "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_1.2.5_1612380866272_0.17016589684911687"}, "_hasShrinkwrap": false}, "1.2.6": {"name": "prism-media", "version": "1.2.6", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest && npm run docs", "docs": "docma"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"docma": "^3.2.2", "eslint": "^7.14.0", "jest": "^26.6.3"}, "jest": {"testURL": "http://localhost/"}, "peerDependencies": {"@discordjs/opus": "^0.4.0", "ffmpeg-static": "^4.2.7 || ^3.0.0 || ^2.4.0", "node-opus": "^0.3.3", "opusscript": "^0.0.7"}, "peerDependenciesMeta": {"@discordjs/opus": {"optional": true}, "node-opus": {"optional": true}, "opusscript": {"optional": true}, "ffmpeg-static": {"optional": true}}, "gitHead": "9a0ad8eeea96f8dd1d96320c6f160137b5622c2b", "_id": "prism-media@1.2.6", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-I1Ys8HA+9aSKQ2jbkO3r6p9Z+tMpSssGhucgxXvc0sSpOi0kK550rDQnKtAS7Z5TzPQeLJdBmK2Br8x+5137lg==", "shasum": "9e86f69c14342f546c96b7c1461cc8b3e46b48a3", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-1.2.6.tgz", "fileCount": 18, "unpackedSize": 44619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgKA7/CRA9TVsSAnZWagAAWwYP/0IO6hAwMknvr8SZfo0p\nJMvKq3xRYPWIk/+jX3Qwf6vGJkCNGe7uxxMgxSrcnixj3TypQweG3z/GD1lJ\nnWt8xzueC7sWbLWnri+qDDGcddK+2vG6T/cSOcC5URuHYnThdfruVKoPY88m\nKNiO4s8nrAXwV28yvbtupotKjk5OOpn8GH7OxXSwXy/UWPu0JwWW2WX371zm\nkDMBD81LOhiTDR3FuSvhc4Ilz39M7x1PuvtaBLE7btPM7gcDzhgc5sIzly6h\neid78zNFMfx3iSivR/tciUXcpkp1I48SM+sVu3r1PyqlK1Ynk13XJeLM5TCH\nYKz2URZ6RnSNhEnOLjF/o5hmztlARYeSlZMqWt2/FISQeSalRCzOs6FSp+nq\nIy22DnpVQ9DogPSzfnpk6qRAJ4eGysYuKorJc9GfNVauHbTp50kEVfPVjmMH\n0ggKCJTP5GEUc9SRJCdwci5tSMjYHVICRQAlLH5umZz2WtFEVaQ/7szq+hRK\no5B8SFi6YYdWqD0Kt+FjgMzn04Jo5cRVyVdnQaKf0yWGEsF6OM9/Km950SqZ\ntHHkjMmG79gN0u5z7r5p1L8Yx9f4qm9fr6VJi0DsIl99FH2AEos8ywXUp2to\ne7M+WUvKRYrGu1tjgsRDdhQGdkPmEWEbpg9OStzjbP0dkNsPNaS73ASzCupW\nEMc/\r\n=3h6Z\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGl9xBxFRki9vgzAU8RXPIrhh18gydPzhnFpmoyxu/5tAiEA8Pz3Y3oI4G0BKmt6tlhBBaPCo+faPM2kArQvnpreJ4E="}]}, "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_1.2.6_1613238014432_0.9057701224202697"}, "_hasShrinkwrap": false}, "1.2.7": {"name": "prism-media", "version": "1.2.7", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest && npm run docs", "docs": "docma"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"docma": "^3.2.2", "eslint": "^7.14.0", "jest": "^26.6.3"}, "jest": {"testURL": "http://localhost/"}, "peerDependencies": {"@discordjs/opus": "^0.4.0", "ffmpeg-static": "^4.2.7 || ^3.0.0 || ^2.4.0", "node-opus": "^0.3.3", "opusscript": "^0.0.7"}, "peerDependenciesMeta": {"@discordjs/opus": {"optional": true}, "node-opus": {"optional": true}, "opusscript": {"optional": true}, "ffmpeg-static": {"optional": true}}, "gitHead": "361cb91cc27b2bcc590075d6449708044cda0f32", "_id": "prism-media@1.2.7", "_nodeVersion": "15.8.0", "_npmVersion": "6.13.0", "dist": {"integrity": "sha512-thS1z3L6BDmf724sqLC73bHGjSYArFTYHa7cqInyS3EdDNTHKgDCXy7l+IhRvlnX7aFNiUb8jJcC+R8ezxwgMA==", "shasum": "697c9630e2a473a5dfab19c71eba398a083c2bf0", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-1.2.7.tgz", "fileCount": 18, "unpackedSize": 45970, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLOeUCRA9TVsSAnZWagAArWcQAIFVXYNJPe9tcub0pYVT\n1LLERyOIaDAhJ0J9FPuOpbuz8ifd6BtTkt7I9fTjwStTcKAQ6kZJV46kl7B2\nitdrOuVVS6HiYpgS/4HFtTVlVNxSc5hCYqimgtQT1mYA34gbAGkOZBDG8Bfg\n69G4G7z0XWD6y9i3RyqWVgQmUwEMlmhHGgw4KeuNEvZaiEtxRYYMSmJL8sje\nzFRlCZSVWz42bcmP2SegG54pQymsC+GVkoqfM+vBVJgzxKRtDrm+nx+9dQkM\ntvsch2aIGyAlcNhB2e3B0aNpgrCN/IY+R5usHR50yEvzLGDjBreHZax+QqZ8\n9lSIEoln0DW97lYNEsUQxSCJk1avKeHGavU/wMGtvQrLXUn5fs2NLDhxQ/R4\n1EJiKyBIaJ9xvTlA1tIR0fFMD9fjrk0mB2PZgZ1LensrIg7kAOo5TRDZsS2o\nkNfFr06/QsPPmGIXshfUcO8xpt0jouroc1o6k9z3sYkFPLQW9OUItwQkrQfx\nrp9ZKaI2Wn6TOOUFa+emt7ZpVvMPgrTEK2Rhr5OiUW+2cVB2f1qph3NUkiiX\n5Ge8CER0x3S04w19r9sBRnKqKSKeQwHIJX2HBDaxwxjU0pBiG7w5TJeoqxSB\nWeohXLOylK5DjfsB22rnqlXXvR6GsJhpdq/EJLz4ipSeGtycG2bSKSWlNk2O\nAv4o\r\n=NWdq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICisEb+Vf2ZgKC7TGYM++RdalAnZqTf/emIVKZ1e/HERAiASY8szd0i5hLaXOLzUdhwzoiPVHkOzVjMfNosSSrBTwg=="}]}, "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_1.2.7_1613555604288_0.9823251129216359"}, "_hasShrinkwrap": false}, "1.2.8": {"name": "prism-media", "version": "1.2.8", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest && npm run docs", "docs": "docma"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"docma": "^3.2.2", "eslint": "^7.14.0", "jest": "^26.6.3"}, "jest": {"testURL": "http://localhost/"}, "peerDependencies": {"@discordjs/opus": "^0.4.0", "ffmpeg-static": "^4.2.7 || ^3.0.0 || ^2.4.0", "node-opus": "^0.3.3", "opusscript": "^0.0.7"}, "peerDependenciesMeta": {"@discordjs/opus": {"optional": true}, "node-opus": {"optional": true}, "opusscript": {"optional": true}, "ffmpeg-static": {"optional": true}}, "gitHead": "24d8a7cdcf7a02132df8ff80687ff3a4ee80c984", "_id": "prism-media@1.2.8", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-bJ8J9PKpUdG6GmtnlaPSi2cMdGDLsS9o4iOlOncJasku73uJucgcN9Yr7/jlENqfh7hoR6LDqPr17JEzp6srjg==", "shasum": "41e0d0c788eeb12068960cb60c546fef82fa9d44", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-1.2.8.tgz", "fileCount": 18, "unpackedSize": 44694, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgTLaDCRA9TVsSAnZWagAAu6gP/Rj1nbjLa9izCr9bHRTT\noMfc0tegKrNy4gD+qFvam3Cr7QUr3AgFhvTzMlxBmS9Qt4QoqLvQD26jL49x\nSqwv7PARZ1SukAEf446QiYO59A1q9/Vnl7M6ty2PMjmUaR5F4I2AkyGhiSqX\nKIW0svMFFJMcW5KLOwCBntWDibDtV4otaXyMOnym0M0+aV3Ec4yVtzBNW68f\nVE2t1xipR2nBiaQLjG9bRN/bE5ZyZpHwi6QWNdmGlN6g4vcMJOhJK8I/gVxj\nftIdRvWv3GvnwQcmc7MCsYPNHVwiSBhwNw+qnLex1UTXvHMW0lG4CokXhyJE\nUO91GPrD8LIKAz1pBBk1RQc9rt008/NMAOtbfZv0JD9nztqaPpWGw8Z6dUkA\n+dOi6X4ZIpwTijgDX7kciOBbTwW82eZHTQmsHvjlSlanTq9aGXjAqNaU9jpF\nEP94Q1ZxNRK+MF0eIo47/8oAZTcodI0D8lFr8XcO9z1NgedXlQiIR4h2x8Yi\nmdDLYJGyALhB0lLOOC3h8AO5YVDijIoAXFM09lLWSYe80KGo9NoncK0GOqBm\nzqwuiESiaetfDLB+ZXEyjlVdnUDRoezmCWW2cqvWt9f+ArBW5VGq3j1GO+NU\nj8V2eOg7rq0BsPfu9GQERJNwm5NcchcrxiGStpOaMLGJTA2AR4zj5kFM9b+p\nt5aT\r\n=0mrS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDRDrf5SclU2BIYVCVHL93KbVpIstsfjieOGj1QVui7kQIgXce1PXWN6Mv7SUJP38wtzu9yvcpUbkVGp2UX66tGHMc="}]}, "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_1.2.8_1615640195338_0.8690242580540772"}, "_hasShrinkwrap": false}, "1.2.9": {"name": "prism-media", "version": "1.2.9", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest && npm run docs", "docs": "docma"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"docma": "^3.2.2", "eslint": "^7.14.0", "jest": "^26.6.3"}, "jest": {"testURL": "http://localhost/"}, "peerDependencies": {"@discordjs/opus": "^0.5.0", "ffmpeg-static": "^4.2.7 || ^3.0.0 || ^2.4.0", "node-opus": "^0.3.3", "opusscript": "^0.0.8"}, "peerDependenciesMeta": {"@discordjs/opus": {"optional": true}, "node-opus": {"optional": true}, "opusscript": {"optional": true}, "ffmpeg-static": {"optional": true}}, "gitHead": "75951648c5813ae10972c5d38a3cbb4f355d16bb", "_id": "prism-media@1.2.9", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-UHCYuqHipbTR1ZsXr5eg4JUmHER8Ss4YEb9Azn+9zzJ7/jlTtD1h0lc4g6tNx3eMlB8Mp6bfll0LPMAV4R6r3Q==", "shasum": "8d4f97b36efdfc82483eb8d3db64020767866f36", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-1.2.9.tgz", "fileCount": 18, "unpackedSize": 44697, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgVi30CRA9TVsSAnZWagAAyAoQAIo6NuuyIIjW8iKQyFCz\nsL5GsAQA1MfazMhrPL6lx0q7KEpxocYXELcA+WELjDHG2plVO8+xbg8koday\nughYR0HgDPe13m1n4Na5uS9MTPJ3t5BO+3MU6Mnn3sE8idiYyP1uX65RJBNH\nfHHl9NOTzrnBwbxAYwoHHJldXdnhumRiZi2aX2AkyE3LMyDSzCgLtPMU/SjK\n6xeFQWkHtfMYh/NWySpB6wx7hzFdT0fpjQFDI2k2ZblF4UO1gMvE7ZbIbsHl\nBFntGQdeLFnONTWf58FVNZDkDvPtD08lcvsScKyrIsqpS+w1KF/DmYyKhEzX\nBSkgzYkUNViAjcJw+G16iSKTiHcVTYZ6l3x8Pc+t4ZAlQ9vmJBKHt6vloce4\nSZxLGBJtqPT6hci8Cc+synCJHKp+4IYRD9PvQamnfxFKndBscRhzi60HDa4+\npnIGT7ZpuLygo/SLzX/8ZLiI7EV1F/QX6oiKwaCyXXGPbf4U6MtrMRHQER03\nhMGXxKb083tf2ncIl0MxAZRHSs5tUGfe5Rb73KdOCS6CYFQHeCHRpECbUFKQ\nKY413pRXvVFsTVHQ5xToWj8EwnSFCh95oYWyqWAeyYs1ewhty1BAZjgqiYpT\n+LdmQ3bsYLwmunZ1ioxBhEnPHy4+RwNmkBIYCRcMKMkFaHtAGwzoPO3oNRpy\nCj55\r\n=Mf2c\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD2pfJekATIydPwq4qim2YGrhtZIKR7+vGfl9sIiFEk0AIhANyezgvpgdulNNcOU37hiSXmBlnena6fdRBJVyiO/Kti"}]}, "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_1.2.9_1616260595925_0.12105900770089595"}, "_hasShrinkwrap": false}, "1.3.0": {"name": "prism-media", "version": "1.3.0", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest && npm run docs", "docs": "docma"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"docma": "^3.2.2", "eslint": "^7.14.0", "jest": "^26.6.3"}, "jest": {"testURL": "http://localhost/"}, "peerDependencies": {"@discordjs/opus": "^0.5.0", "ffmpeg-static": "^4.2.7 || ^3.0.0 || ^2.4.0", "node-opus": "^0.3.3", "opusscript": "^0.0.8"}, "peerDependenciesMeta": {"@discordjs/opus": {"optional": true}, "node-opus": {"optional": true}, "opusscript": {"optional": true}, "ffmpeg-static": {"optional": true}}, "gitHead": "14a513ce5a3a092f7b1313b4e234be031b0d1585", "_id": "prism-media@1.3.0", "_nodeVersion": "16.1.0", "_npmVersion": "7.16.0", "dist": {"integrity": "sha512-BTG7js7vn57Iy1StRDKqCWBLwO8yhf9hzSzxWfDMOyiF5UcMo/jZIFgkCKXZma/S4v1nJEIn86ScLnRrGVhnCg==", "shasum": "beecd3cd6bec238fdad70174580dfef844e91f8f", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-1.3.0.tgz", "fileCount": 18, "unpackedSize": 44923, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgxIxlCRA9TVsSAnZWagAAQ2YP/3w+62IPZwJPnHzwlV1U\nXcZ36jBqoVZqiXXB2y2s6aSwoOmpV02vEgU1hg1nnmhYJxKZRde9PpGCB5nh\nH+HWQB3TJx9PgoydJWYvNtyS0wxzb1Qu01LcnkKgutlD6viScnHM5sZGrPmm\nFBejDWinwPxa7HkdcoQMreO0w5NMJIFLe5nAf0YyNA51TPFns7/CGMsnPIEz\ndVDDjHR8q5bH0gyiFG8yRQYmOwtrtulkhX1E5i52LdWIM1JJ2k3pBIM+DT1Q\nr3/3MxORbrz7WGIdB0VONBG3BOkuz3mGQhoU2O9QO2DfcTtGdxPW10fSwXRf\ntwJMH6Zvqn/r4yT01STMVZBz4Bg8h6Lox9jg5Wq171UE0mf0BtF3ug2BpFvP\nGlVx8UaoP3sAefAvXJ0vTPg1XGHo6ZKUN0bWiuI03oax2KiaYK9btboc3lNc\nZuj7KgwlcURAiqmA6i4ks4r64SC4Kw9jrPIBwI0oHmEG2FMqLp9hqgJZA/Vd\npw/Oo1DupWUebJbW9jbTOxITj5O7q+hnGQzlgQLNS9SqMLLH6Cr455EcfAGu\n11unaHUmr7AG81Id0TSjLn3gnw7zXKY5HMVLjYkjmS+nlrVeB2NLM+8urgxR\nChZtJseiDXBTWEBFQBbzaTvz0/VjvZE3oFMn/MT/P7OkYBMTo16+JIbLxeRe\nVLT4\r\n=oDva\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGyPTEj9qt4TJRySex1v9QbkzyZDJTpzJVI1C5wBt17EAiEA2VIfs08wARSnlmWpwSCh5my4mHXgGnmGzg1VZTLP4k8="}]}, "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_1.3.0_1623493733498_0.9304478028994767"}, "_hasShrinkwrap": false}, "1.3.1": {"name": "prism-media", "version": "1.3.1", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest && npm run docs", "docs": "docma"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"docma": "^3.2.2", "eslint": "^7.14.0", "jest": "^26.6.3"}, "jest": {"testURL": "http://localhost/"}, "peerDependencies": {"@discordjs/opus": "^0.5.0", "ffmpeg-static": "^4.2.7 || ^3.0.0 || ^2.4.0", "node-opus": "^0.3.3", "opusscript": "^0.0.8"}, "peerDependenciesMeta": {"@discordjs/opus": {"optional": true}, "node-opus": {"optional": true}, "opusscript": {"optional": true}, "ffmpeg-static": {"optional": true}}, "gitHead": "1ade982edb2b280e50b530a74659e8fedeb62b4e", "_id": "prism-media@1.3.1", "_nodeVersion": "16.1.0", "_npmVersion": "7.16.0", "dist": {"integrity": "sha512-nyYAa3KB4qteJIqdguKmwxTJgy55xxUtkJ3uRnOvO5jO+frci+9zpRXw6QZVcfDeva3S654fU9+26P2OSTzjHw==", "shasum": "418acd2b122bedea2e834056d678f9a5ad2943ae", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-1.3.1.tgz", "fileCount": 18, "unpackedSize": 44895, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgxI60CRA9TVsSAnZWagAA56sP/jy/4wersOnPBS6UKOSU\nPZsXy1aB0iEbJtRwVsxf92Xs5JF71V3H7sUBMtWZKOBClOqTT4TD/m13t+PF\nMfiZR19wQVoI2iLtdcAqE0A/cuNECJQ3NJAlH/YETCSrnXD5j/PKE1FBnZgv\nkUQVEcd9wm8RQ7xQ2fNaeg1rsokYaHv5hu0Y45Ru5On4VEyCxDdkeytV2vkJ\nA8gSdA/zw35d9o3OypL4zaO6/RPF9sw8e1dxgIRxw/9nSABqz2rdB0huodqe\nbDTNorw4+S+8p7rRMIWyON2sY8CBnRR1OQoyNy3l6n9CmC6HN3psc78e5Ngz\ndeNUjtJIF3zFGcVBZrrvhB1SMj9/onLxe51pYCT2c6n5Dq/y+blLxqNJwdd4\nvEnZGoN+c5iW1XvCPJvzCd5VL+Cg2f0xDfqeRJlx7XVoErQTl+UGFP5MYDku\nVPu1d215PdRuQbhwolE7gGyoySa+TGa8yeMUKKQFPgKj87MV+AilnywvGMha\n7e/jUPpTcSRnFm5Bxr3CQZnDwkWUihZzwBJuuL20SP+htl92x5ugwYkBiowY\n1kjG9pGplH1ERhi9R19ykW95EUr4b91CBmEoP+IH/5cg6a2N9xlMMHh9/u/b\nm4R3YxU+wwB6QDii2AUsvVU/T+yypeMTuufQKb7bEjkrCM0lZr388lsE0qXZ\nZ+qr\r\n=OQgJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH25fY/qnQ6McWvYsASB+dzJunmVGP5qGpkdd7ualo22AiEA5SxsLAbooQWfibDeAtKumq9bVDqmQ5KCaarDjVVhcgc="}]}, "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_1.3.1_1623494324718_0.503926451211054"}, "_hasShrinkwrap": false}, "2.0.0-alpha.0": {"name": "prism-media", "version": "2.0.0-alpha.0", "description": "Easy-to-use stream-based media transcoding", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"test": "jest test/", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "prettier": "prettier --write **/*.{ts,js,json,yml,yaml}", "build": "tsc", "build:check": "tsc --noEmit --incremental false"}, "repository": {"type": "git", "url": "git+https://github.com/amishshah/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/amishshah/prism-media/issues"}, "homepage": "https://github.com/amishshah/prism-media#readme", "devDependencies": {"@commitlint/cli": "^12.1.4", "@commitlint/config-angular": "^12.1.4", "@types/jest": "^26.0.23", "@types/node": "^15.12.2", "@typescript-eslint/eslint-plugin": "^4.26.1", "@typescript-eslint/parser": "^4.26.1", "eslint": "^7.28.0", "eslint-config-marine": "^9.0.6", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.0", "husky": "^5.1.3", "jest": "^27.0.4", "lint-staged": "^11.0.0", "prettier": "^2.3.1", "ts-jest": "^27.0.3", "typescript": "^4.2.3"}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "lint-staged": {"*.{mjs,js,ts}": ["eslint --ext .ts --fix"], "*.{json,yml,yaml}": ["prettier --write"]}, "commitlint": {"extends": ["@commitlint/config-angular"], "rules": {"type-enum": [2, "always", ["chore", "build", "ci", "docs", "feat", "fix", "perf", "refactor", "revert", "style", "test", "types", "wip"]], "scope-case": [1, "always", "pascal-case"]}}, "dependencies": {"duplex-child-process": "^1.0.1"}, "readme": "[![Logo](https://hydrabolt.me/assets/prism-media-logo.svg)](https://hydrabolt.me/prism-media/)\n\n<div align=\"center\">\n\n[![Build Status](https://travis-ci.org/amishshah/prism-media.svg?branch=master)](https://travis-ci.org/hydrabolt/prism-media)\n[![dependencies](https://david-dm.org/amishshah/prism-media/status.svg)](https://david-dm.org/hydrabolt/prism-media)\n[![npm](https://img.shields.io/npm/dt/prism-media.svg)](https://www.npmjs.com/package/prism-media)\n[![Patreon](https://img.shields.io/badge/donate-patreon-F96854.svg)](https://www.patreon.com/discordjs)\n\n</div>\n\n## What is it?\n\nAn easy-to-use stream-based toolkit that you can use for media processing. All the features provided have predictable\nabstractions and join together coherently.\n\n```js\n// This example will demux and decode an Opus-containing OGG file, and then write it to a file.\nconst prism = require('prism-media');\nconst fs = require('fs');\n\nfs.createReadStream('./audio.ogg')\n  .pipe(new prism.opus.OggDemuxer())\n  .pipe(new prism.opus.Decoder({ rate: 48000, channels: 2, frameSize: 960 }))\n  .pipe(fs.createWriteStream('./audio.pcm'));\n```\n\nThe example above can work with either a native or pure JavaScript Opus decoder - you don't need to worry about changing\nyour code for whichever you install.\n\n- FFmpeg support (either through npm modules or a normal installation) \n- Opus support (native or pure JavaScript)\n- Demuxing for WebM/OGG files (no modules required!)\n- Volume Altering (no modules required!)\n\n## Dependencies\n\nThe following dependencies are all optional, and you should only install one from each category (the first listed in\neach category is preferred)\n\n- Opus\n  - [`@discordjs/opus`](https://github.com/discordjs/opus)\n  - [`node-opus`](https://github.com/Rantanen/node-opus)\n  - [`opusscript`](https://github.com/abalabahaha/opusscript)\n- FFmpeg\n  - [`ffmpeg-static`](http://npmjs.com/ffmpeg-static)\n  - `ffmpeg` from a [normal installation](https://www.ffmpeg.org/download.html)\n- CRC\n  - [`node-crc`](https://github.com/magiclen/node-crc)\n\n## Useful Links\n\n- [Documentation](https://hydrabolt.me/prism-media)\n- [Examples](https://github.com/amishshah/prism-media/tree/master/examples)\n- [Patreon](https://www.patreon.com/discordjs)\n\n## License\n\n> Copyright 2019 - 2020 Amish Shah\n> \n> Licensed under the Apache License, Version 2.0 (the \"License\");\n> you may not use this file except in compliance with the License.\n> You may obtain a copy of the License at\n> \n>    http://www.apache.org/licenses/LICENSE-2.0\n> \n> Unless required by applicable law or agreed to in writing, software\n> distributed under the License is distributed on an \"AS IS\" BASIS,\n> WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n> See the License for the specific language governing permissions and\n> limitations under the License.", "readmeFilename": "README.md", "gitHead": "84dc76fa1564801a5b04869cf1896999c281f824", "_id": "prism-media@2.0.0-alpha.0", "_nodeVersion": "16.6.1", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-QL9rnO4xo0grgj7ptsA+AzSCYLirGWM4+ZcyboFmbkYHSgaXIESzHq/SXNizz2iHIfuM2og0cPhmSnTVMeFjKg==", "shasum": "f893ce1cbf0e191c8da11631ab66a8e3550943c3", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-2.0.0-alpha.0.tgz", "fileCount": 76, "unpackedSize": 158058, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhEPqoCRA9TVsSAnZWagAAWGQQAIpI8FMkf6mAqvixpTa6\nPjCOZZzw5A1xiGpnoTINg51CL2dMWSC4TBVgNyVom0TXUplqfwglrrzhjhpa\naXQt9zpqCVhfkVcHRmn04U8OfDTCxYtR37466ajHkx0eH4yWVryfepS9Re1V\nXWeH+vaq8R8ze5JdY5OpF5QGbZYrp4KhqLfnsMvt7SF2kX4yqm53KR/SUrpZ\nNEZ/F3mu601al8yNZKUV3+FkHlrxMIgeuZrpNTjaUhwTKzqbfu0INtXTRaFk\nlyl/TH4u1iJSMotFp6pZ5lRjMYs32sa3FWSfyhd9h+iVh7RJdgDc0SHPC+AD\n2C1z191z+gKedJ4ftb6NSTaf0tevIeyj5+GpugaBhYKc74yCj0VSekLyM9ew\ndrrztsECVqL5iMxcZFZGmbYnhT7FwzJKOQLFxiQAR7yhdTbE+yOfPLHC7a43\nPxU9EWtORZAlU9j9KqlxTOjFC78B2WMjmT+G+4cHQKOwOMEU8F6Vmmr+MQ3p\neSIwo84MruX3YAkEHP6SFzno25Kx7RShBEsppI4fhWQDlnV6K+Cz2m5LI5zW\n6xSZTReOlYPxbP2qZgMXeHdPz6nUafgzRRCIs5af4r/iLpEChXhttr62BsZT\nocyi1CyJcGQM5cfLckOmkZD/2VNgj4RVaco+8he4Lmd16GkgUIcQkjZw2/CR\nAf6r\r\n=BYAG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCkhDZF0Vnw99GBH3NtGXiiJxLwf3fkvAdEMANHZPyLXQIgFeVWsSm6uWg1mN68XDPjCZ/x3Mc3dUmzhGbGLdNg/ms="}]}, "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_2.0.0-alpha.0_1628502696109_0.7157794170933791"}, "_hasShrinkwrap": false}, "1.3.2": {"name": "prism-media", "version": "1.3.2", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest && npm run docs", "docs": "docma"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"docma": "^3.2.2", "eslint": "^7.32.0", "jest": "^27.0.6"}, "jest": {"testURL": "http://localhost/"}, "peerDependencies": {"@discordjs/opus": "^0.5.0", "ffmpeg-static": "^4.2.7 || ^3.0.0 || ^2.4.0", "node-opus": "^0.3.3", "opusscript": "^0.0.8"}, "peerDependenciesMeta": {"@discordjs/opus": {"optional": true}, "node-opus": {"optional": true}, "opusscript": {"optional": true}, "ffmpeg-static": {"optional": true}}, "gitHead": "9f9d00adeef5ef067caee0449a78e1f0d7979b82", "_id": "prism-media@1.3.2", "_nodeVersion": "16.6.1", "_npmVersion": "7.20.3", "dist": {"integrity": "sha512-L6UsGHcT6i4wrQhFF1aPK+MNYgjRqR2tUoIqEY+CG1NqVkMjPRKzS37j9f8GiYPlD6wG9ruBj+q5Ax+bH8Ik1g==", "shasum": "a1f04423ec15d22f3d62b1987b6a25dc49aad13b", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-1.3.2.tgz", "fileCount": 18, "unpackedSize": 44907, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhEYCaCRA9TVsSAnZWagAA0VcP+wcQYYPFOyxaUcwd1aUN\nGZE9EnfDAh6fzYFvKsOWgb0SbNozeYpF4GDb0sldudj0Eb4EIjkIBQj6GQK1\nKib1Oaru1ZzNXO8txO9wAIJTQ+1A1dDmsV+K01Q4GKjyNKxsBDMA/bL/5syr\nc+xOyt/w8//mpfrc0aKGGU8mcXeTghJlDUYwxJKqj1u5wwmSRZTCzbwUKavP\nLFDomFzOhQDLc5ATVRvPiB8LZ5D+ZGX5rP9moK09bydd0RS3Lo2l/0akeYXe\nAAAEhcCGI7DSvzYmUT9wKMKndFdQsNbFcL0yJ3G/BCLOuiJfKrWg10ROlGxU\nshtWcy22LPbR5/kd3fMg1MeS0k49puSAL+HsGyEBgLplfG/0zbZOxyYmayvN\n2nBA7+0SaNdsfzFm/o62EfkuelqlWxlaN1XKOmnDnkWF4ob2LY3EFqJIYXPk\nUMfzQ1PU8h0PYEfuLKjalUythqf86MImWxpKV++QJJ7l1PEi3xnQE2bNJrfl\nMA5CJpgMr/AMjkycslhptF9jUpWVOrnNM4C9CzeLmw6UXO8fkN2/8eFfbZzQ\niU9KJkh9VBCMTinGlvrBAJQG3zsdmPJpf+1kUqiCr0dH+GxXJOHV4kg6yDPq\nr8u2HuRFZ6WKqMQdHwvOkxr8dFCrZc/RD+E45pKae7zW2Iht2YiyZmq6eHVF\nvdz5\r\n=XDF+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGckppXMQ2vue0CdPNXO++sLTS1nLI/Y2cJgP32ofKvjAiBFJzrldldSOx3yQ43fBiqKBf8I+4JxXw/PXoNhm083pw=="}]}, "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_1.3.2_1628536986193_0.9669522394349321"}, "_hasShrinkwrap": false}, "1.3.4": {"name": "prism-media", "version": "1.3.4", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest && npm run docs", "docs": "jsdoc src/** -d docs -R README.md"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"eslint": "^8.19.0", "jest": "^28.1.2", "jsdoc": "^3.6.10"}, "jest": {"testEnvironmentOptions": {"url": "http://localhost/"}}, "peerDependencies": {"@discordjs/opus": "^0.8.0", "ffmpeg-static": "^5.0.2 || ^4.2.7 || ^3.0.0 || ^2.4.0", "node-opus": "^0.3.3", "opusscript": "^0.0.8"}, "peerDependenciesMeta": {"@discordjs/opus": {"optional": true}, "node-opus": {"optional": true}, "opusscript": {"optional": true}, "ffmpeg-static": {"optional": true}}, "gitHead": "fc562b17de217753030b257a3796c74fc1046d92", "_id": "prism-media@1.3.4", "_nodeVersion": "18.5.0", "_npmVersion": "8.12.1", "dist": {"integrity": "sha512-eW7LXORkTCQznZs+eqe9VjGOrLBxcBPXgNyHXMTSRVhphvd/RrxgIR7WaWt4fkLuhshcdT5KHL88LAfcvS3f5g==", "shasum": "7951f26a9186b791dc8c820ff07310ec46a8a5f1", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-1.3.4.tgz", "fileCount": 18, "unpackedSize": 45056, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFX1wQTyZTCbynFWgcOOWBXbN1yXro1HjA/K+OfgWUPXAiB28izVFKONIhI6jpbbAsMa/x+2020TtH2N6tr+kepaCw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiyWCHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqcIg/9EU1qb817/xaUuZVH5qD/PwfGo8nL/z3lZwg41stFJm4uF5Hc\r\n1zqvGscgUcaB9yjpejEBBPM5KTTH8nBikhV3rnDG81wk4Mtv51WBpbb6pMkn\r\nynqXoZCrnGtU83DOJ874Oy7qVGITBCcS0iuS135SphJAakhA2743XWOzxO3U\r\nQuPzni56sRcdxm9w0xBnz/8qoOWgM1NtyViN9ABnB/TIgsEW3ae6SfeITFP3\r\nd7kU0qrHjqovqxc/xtf6V78Gkk3B9dCRmeaunq2BN8mPn4sAbCOrv24MqVKB\r\n9+cENr5VDlk4k7tRQGSvo3+dBZOtlAH+59R1Nwy7Df/wEQ0usCg1IB8SROyO\r\nRtsmIzT35EU5lL1IlC4VmFmP3fosNWQ4iWCuaDMPGdw84Dtsv5EpDD5tK0ET\r\nZ1kwmZ5055BEEKPtDz22ikde5lzfEWnhaYOLjzj21AZu+/oa35sIeoksB9ab\r\nerzwGfMhJcWIc16ubWuzGvkdyoq/so6pC3cYyXrZQtAORRBpBrQXdRCE92kc\r\njX/udhif19Qagjxr1Wk5g2JereRB2Cb32itdVQW7EEQfDxj4ZJGIvdUQFvGy\r\nHJgsDludDgtU7H/dP9hGHNA/ZAY+JfnQRZ1HiCKyD1D0Q+spDgzThY5xni63\r\nVRIr8a4xyrlqIxCcvfychtNT5KDtHAwCqyM=\r\n=C8jh\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_1.3.4_1657364615198_0.9360356866571107"}, "_hasShrinkwrap": false}, "1.3.5": {"name": "prism-media", "version": "1.3.5", "description": "Easy-to-use stream-based media transcoding", "main": "src/index.js", "types": "typings/index.d.ts", "scripts": {"lint": "eslint src", "test": "npm run lint && jest && npm run docs", "docs": "jsdoc src/** -d docs -R README.md"}, "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "devDependencies": {"eslint": "^8.19.0", "jest": "^28.1.2", "jsdoc": "^4.0.2"}, "jest": {"testEnvironmentOptions": {"url": "http://localhost/"}}, "peerDependencies": {"@discordjs/opus": ">=0.8.0 <1.0.0", "ffmpeg-static": "^5.0.2 || ^4.2.7 || ^3.0.0 || ^2.4.0", "node-opus": "^0.3.3", "opusscript": "^0.0.8"}, "peerDependenciesMeta": {"@discordjs/opus": {"optional": true}, "node-opus": {"optional": true}, "opusscript": {"optional": true}, "ffmpeg-static": {"optional": true}}, "gitHead": "4ef1d6f9f53042c085c1f68627e889003e248d77", "_id": "prism-media@1.3.5", "_nodeVersion": "18.8.0", "_npmVersion": "8.18.0", "dist": {"integrity": "sha512-IQdl0Q01m4LrkN1EGIE9lphov5Hy7WWlH6ulf5QdGePLlPas9p2mhgddTEHrlaXYjjFToM1/rWuwF37VF4taaA==", "shasum": "ea1533229f304a1b774b158de40e98c765db0aa6", "tarball": "https://registry.npmjs.org/prism-media/-/prism-media-1.3.5.tgz", "fileCount": 18, "unpackedSize": 45105, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD5gDKH0cEmtKfqmb98z33C9BJP1I8pjtHqFtVXpnHP9QIgdlryrsc/To5S+EoDhXMZLZ4tldCuNMscRTYxtctnLjA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/NNHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqmCQ//VNyf3oMfAq2dRPMW/FnQo8sd4kdK5R3i+MpvwpWX+nLfsxft\r\npCjXH57EQaQQWYxMf0zBh5Y4aJif4nR9tVzP2S9YxxPKia6JyDQr9/x80kST\r\nX1uwubp2ehY0Sj+1baDuQ2fhRzu5Yw3nTPQbrnh4Rrwfc0sq1NrcPrQ+PzMn\r\n4uEYSvlXxLYoDy4cQPW2Ho7KNg8zFBSXZOg3ZRfitbGigpcAUuOFT0H/rG/l\r\nA6h0BlqrGII05WENYhOq2w3V399dm44ayYrX0gVaJJNSqX8bxUkgiDqcUFhJ\r\neVsdSrVHmLgIWxMUDYIRV3fqp64VSRKy+1Rd40JPBXEvgxFBSDVNeHX+fCgO\r\nORxKNll4bV0a1W7yePxX1sKL7H00xhHYVE2tS7nvAqJWgrNRhxZSGbBEkgNU\r\nnv0cSYHbbPSkTIEAZTxe8bPjGnJN2EGdNwbhD/1u25mR0zuLMUkaPVHHEvF0\r\n10xljkvRi8KWU0f3fQzcu+RS57qp4Rf1OT0WXFJAEooiQHiB/iH47Il9pblH\r\nGZ/Cjw7w8VTAeEdYdhmwyFGdt365VzuClFVRnAqlFdXfvG1/IcpyT2DRZeOo\r\nndvsLzTsRhlvbOq6jUVt6/MUECiJLDfQ3embqgA28cdn/ZlQE7mj2dumLkzS\r\nW0opPlhpyXqWA9+UpAS+gJU5fH4hFiuZTuY=\r\n=ND5r\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "hydrabolt", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prism-media_1.3.5_1677513543426_0.6543893786945019"}, "_hasShrinkwrap": false}}, "readme": "[![Logo](https://hydrabolt.me/assets/prism-media-logo.svg)](https://amishshah.github.io/prism-media/)\n\n<div align=\"center\">\n\n[![Build Status](https://travis-ci.org/amishshah/prism-media.svg?branch=master)](https://travis-ci.org/hydrabolt/prism-media)\n[![dependencies](https://david-dm.org/amishshah/prism-media/status.svg)](https://david-dm.org/hydrabolt/prism-media)\n[![npm](https://img.shields.io/npm/dt/prism-media.svg)](https://www.npmjs.com/package/prism-media)\n[![Patreon](https://img.shields.io/badge/donate-patreon-F96854.svg)](https://www.patreon.com/discordjs)\n\n</div>\n\n## What is it?\n\nAn easy-to-use stream-based toolkit that you can use for media processing. All the features provided have predictable\nabstractions and join together coherently.\n\n```js\n// This example will demux and decode an Opus-containing OGG file, and then write it to a file.\nconst prism = require('prism-media');\nconst fs = require('fs');\n\nfs.createReadStream('./audio.ogg')\n  .pipe(new prism.opus.OggDemuxer())\n  .pipe(new prism.opus.Decoder({ rate: 48000, channels: 2, frameSize: 960 }))\n  .pipe(fs.createWriteStream('./audio.pcm'));\n```\n\nThe example above can work with either a native or pure JavaScript Opus decoder - you don't need to worry about changing\nyour code for whichever you install.\n\n- FFmpeg support (either through npm modules or a normal installation) \n- Opus support (native or pure JavaScript)\n- Demuxing for WebM/OGG files (no modules required!)\n- Volume Altering (no modules required!)\n\n## Dependencies\n\nThe following dependencies are all optional, and you should only install one from each category (the first listed in\neach category is preferred)\n\n- Opus\n  - [`@discordjs/opus`](https://github.com/discordjs/opus)\n  - [`node-opus`](https://github.com/Rantanen/node-opus)\n  - [`opusscript`](https://github.com/abalabahaha/opusscript)\n- FFmpeg\n  - [`ffmpeg-static`](http://npmjs.com/ffmpeg-static)\n  - `ffmpeg` from a [normal installation](https://www.ffmpeg.org/download.html)\n\n## Useful Links\n\n- [Documentation](https://amishshah.github.io/prism-media)\n- [Examples](https://github.com/amishshah/prism-media/tree/master/examples)\n- [Patreon](https://www.patreon.com/discordjs)\n\n## License\n\n> Copyright 2019 - 2022 Amish Shah\n> \n> Licensed under the Apache License, Version 2.0 (the \"License\");\n> you may not use this file except in compliance with the License.\n> You may obtain a copy of the License at\n> \n>    http://www.apache.org/licenses/LICENSE-2.0\n> \n> Unless required by applicable law or agreed to in writing, software\n> distributed under the License is distributed on an \"AS IS\" BASIS,\n> WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n> See the License for the specific language governing permissions and\n> limitations under the License.", "maintainers": [{"name": "hydrabolt", "email": "<EMAIL>"}], "time": {"modified": "2023-02-27T15:59:03.719Z", "created": "2017-04-15T18:32:51.307Z", "0.0.1": "2017-04-15T18:32:51.307Z", "0.0.2": "2017-10-22T14:44:12.728Z", "0.1.0": "2018-01-21T17:42:21.553Z", "0.2.0": "2018-03-05T19:28:52.683Z", "0.2.1": "2018-03-30T14:08:33.189Z", "0.3.0": "2018-06-20T19:41:33.104Z", "0.3.1": "2018-06-22T18:09:00.914Z", "0.0.3": "2018-08-07T19:29:43.456Z", "0.4.0": "2019-02-17T20:36:07.236Z", "0.4.1": "2019-02-17T20:38:43.304Z", "0.4.2": "2019-02-17T21:45:55.807Z", "1.0.0": "2019-02-21T11:50:04.343Z", "1.0.1": "2019-02-22T14:59:04.171Z", "1.0.2": "2019-04-28T16:34:06.789Z", "1.1.0": "2019-08-22T11:38:22.431Z", "1.2.0": "2020-01-16T11:11:46.805Z", "0.0.4": "2020-01-16T11:50:35.027Z", "1.2.1": "2020-02-26T13:40:00.837Z", "1.2.2": "2020-05-02T11:28:00.781Z", "1.2.3": "2020-12-02T22:10:29.697Z", "1.2.4": "2021-02-03T18:25:47.402Z", "1.2.5": "2021-02-03T19:34:26.390Z", "1.2.6": "2021-02-13T17:40:14.858Z", "1.2.7": "2021-02-17T09:53:24.430Z", "1.2.8": "2021-03-13T12:56:35.491Z", "1.2.9": "2021-03-20T17:16:36.106Z", "1.3.0": "2021-06-12T10:28:53.661Z", "1.3.1": "2021-06-12T10:38:44.896Z", "2.0.0-alpha.0": "2021-08-09T09:51:36.213Z", "1.3.2": "2021-08-09T19:23:06.383Z", "1.3.4": "2022-07-09T11:03:35.428Z", "1.3.5": "2023-02-27T15:59:03.606Z"}, "homepage": "https://github.com/hydrabolt/prism-media#readme", "keywords": ["audio", "media", "ffmpeg", "opus", "pcm", "webm", "ogg"], "repository": {"type": "git", "url": "git+https://github.com/hydrabolt/prism-media.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/hydrabolt/prism-media/issues"}, "license": "Apache-2.0", "readmeFilename": "README.md", "users": {"hintss": true}}