{"_id": "@bcoe/v8-coverage", "_rev": "8-2b684c34c87d58f731b12b435b583869", "name": "@bcoe/v8-coverage", "dist-tags": {"latest": "1.0.2"}, "versions": {"0.1.0": {"name": "@bcoe/v8-coverage", "version": "0.1.0", "author": {"url": "https://demurgos.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@bcoe/v8-coverage@0.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://demurgos.github.io/v8-coverage", "bugs": {"url": "https://github.com/demurgos/v8-coverage/issues"}, "nyc": {"include": ["build/test/lib/**/*.js", "build/test/lib/**/*.mjs"], "reporter": ["text", "html"], "extension": [".mjs"]}, "dist": {"shasum": "93ba92c5185a1f6e68ee97fb3bdf61b37bd01908", "tarball": "https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.1.0.tgz", "fileCount": 65, "integrity": "sha512-UdVB1rSL7H8TS8674fH02p5lRbhfIqQ18YKLxLKEnHFztHUH6bhMqjebMxgSTmWVrs5raS5JSLJIKKHFT4WfPg==", "signatures": [{"sig": "MEYCIQDO+zU/ajU5THEVhq386YmeKcCrZxRmN1H0TizXGX4nkgIhAMdK3NRLM11REJ7f0iG+qeGyNNzhkuPtvSA0PVi/OH4w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 678924, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcRBQPCRA9TVsSAnZWagAAWoMQAJrioXhxZXAhcNb/9iXs\n45mLCt4lQG5L/qEz10VsCz8r6SHpdQzWWVHMQ5exWVxlRibVY4IAo10JM3y8\nRH1Ls+4hCJ16IArpFBqPxP/krlPeM3BoQdBBYiXD1jGvsPe+PaSTw3ibz0TG\nw0/i/Nz4bD4gDvOa13wh4KwEa6wHLUZPj8AYDRDEUV7V9oF+6ZvabsUlzWoG\n3ja2WwBzzEFsYu0njOlYDg44XJCh8zeDo99gNO8kd4H+VkYC9JOBCmMOG8se\npOkM4fpPNopGpjd5aSL96nESygraveRxKy1qfkfScvr/hRunLv1jsApGi/tv\n3TvPQ+QqFb4CCUA/JLGGPWxDUDAs+RN5Fc20Dr40Fjd8M/pa6j/TjhKAcPv/\nU9xRpEcjpyRg08Y1c6PtBv3xGzKrylm2YfZVWabhgMeg9/ux5MKx4IkMfl16\nOhne7SQhAgm27iKxcStYAeC/5ici0nf+JZmrVpFXGXwJuQuIm1Eqfia74fy0\npIQ6WoutR7hLLDbP6z8uhSvj3f5boDygJeUUGQXbXDwKw+gzWXrKfVx3YHGP\nnE6bjp/4f0IdUCq8vxSkOLtBzjTUdfG03OiSrCXyAnBczukOnMZzykGDm1vP\nWoPz0SN6T3RWOPsiEV4mfEo4IXKxYBhUEgZ1O/un8WLEXZxB9UmH24/Ut26q\n6dYW\r\n=M/q4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index", "types": "dist/lib/index.d.ts", "scripts": {"lint": "gulp :lint:fix", "test": "gulp test", "prepare": "gulp all:tsconfig.json && gulp :tslint.json && gulp dist", "pretest": "gulp lib:build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/demurgos/v8-coverage.git", "type": "git"}, "_npmVersion": "6.6.0", "description": "Helper functions for V8 coverage files.", "directories": {}, "_nodeVersion": "10.12.0", "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.2", "gulp": "^4.0.0", "codecov": "^3.0.2", "ts-node": "^7.0.0", "gulp-cli": "^2.0.1", "minimist": "^1.2.0", "pre-commit": "^1.2.2", "turbo-gulp": "^0.17.1", "@types/chai": "^4.1.4", "@types/gulp": "^4.0.5", "@types/node": "^10.5.4", "@types/mocha": "^5.2.2", "@types/minimist": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-coverage_0.1.0_1547965454742_0.3949568257052454", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "@bcoe/v8-coverage", "version": "0.2.0", "author": {"url": "https://demurgos.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@bcoe/v8-coverage@0.2.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://demurgos.github.io/v8-coverage", "bugs": {"url": "https://github.com/demurgos/v8-coverage/issues"}, "nyc": {"include": ["build/test/lib/**/*.js", "build/test/lib/**/*.mjs"], "reporter": ["text", "html"], "extension": [".mjs"]}, "dist": {"shasum": "09c5d0a9322aa668967a1c5add9cee5a0aeb3774", "tarball": "https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.0.tgz", "fileCount": 63, "integrity": "sha512-OxtUMQYLQGwNw/4WEr7jMv8bGu2k2wm3tvUdW0te1jjlM36sWozzDVdPyT6IOEzFdDQvhijMSBtdcO1yC8w7yg==", "signatures": [{"sig": "MEYCIQDodu4GK0BCmGztC5DE8gXP1qi/dpEyiNcJQdwH/K1FSwIhALWRrj0pn3Jp5U2F6Uc9H5dkejfiVu4mA7+ap4YD52vx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 624463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcy22uCRA9TVsSAnZWagAAuhcQAI0XvhSnsqzy7q3OPau3\nZ88wZ25uQnUf0k3LoTp56GwSdnVna+tslkDVJU/KbLTV0M0zHWBLQOW5o4PR\nU8vNFMUXv32ME/DAjBDdvtoWZw6Li/clKEMC80UmcIE2GPZQsphU7dTGK4r+\n6tMtaPyAT2LGO0XPhJeOCXXpVfnueiLvlObPeYraQPIr/9BmPGU7sc8GxUDb\ndEMfYypKH8vSsG3SaxLdiUW3XXQQrDVrGTkZS+QiMMHsODjN0cf3w0a2HdQm\nfpfWTDJmZRWGoI7RljwzqS7GdfGqwbeT1E6wmbdVTJtWN2QD7dCKyftmHvr8\n9UdBUxCxX3D0MD9/G/lYNUd1iuaxKYhAYGF1ZOAKJC8lBIpHDQ0MoF6wyVyo\nVdadgsOg5v8pYi40XXr+v+I69yTvJn6s3oR6F0YUioVWoogjqfJlKBLtXGIt\nSISNBvDmhPdyq3i2ZkeKxQrm+ie3rxPVhcYiJo3BJ+m23ZN2GO02Hbf23+M6\nCKiJpc+uInTysJysBC3IczmrpJSMHLY2x8P0fuMBDZhikmryA7YvXHEbZ48X\ndedpx1ncTH+ktXO3DrrwLVxK+/Zey3EIWy2kqFgXyLHNqMEMT119+ZKp5I+z\nKD3v9kvcLWUnwJfudYe9VxUunnmaJIYYfweJPPsHmocdg0g3nDm34h1YOjSE\nCaYc\r\n=85J6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index", "types": "dist/lib/index.d.ts", "scripts": {"lint": "gulp :lint:fix", "test": "gulp test", "prepare": "gulp all:tsconfig.json && gulp :tslint.json && gulp dist", "pretest": "gulp lib:build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/demurgos/v8-coverage.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Helper functions for V8 coverage files.", "directories": {}, "_nodeVersion": "11.14.0", "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.2", "gulp": "^4.0.0", "codecov": "^3.0.2", "ts-node": "^7.0.0", "gulp-cli": "^2.0.1", "minimist": "^1.2.0", "pre-commit": "^1.2.2", "turbo-gulp": "^0.17.1", "@types/chai": "^4.1.4", "@types/gulp": "^4.0.5", "@types/node": "^10.5.4", "@types/mocha": "^5.2.2", "@types/minimist": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-coverage_0.2.0_1556835757401_0.04143057839779818", "host": "s3://npm-registry-packages"}}, "0.2.1": {"name": "@bcoe/v8-coverage", "version": "0.2.1", "author": {"url": "https://demurgos.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@bcoe/v8-coverage@0.2.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://demurgos.github.io/v8-coverage", "bugs": {"url": "https://github.com/demurgos/v8-coverage/issues"}, "nyc": {"include": ["build/test/lib/**/*.js", "build/test/lib/**/*.mjs"], "reporter": ["text", "html"], "extension": [".mjs"]}, "dist": {"shasum": "f153c0c6c8e51b0dfdee8eb46aa5dc23529deda3", "tarball": "https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.1.tgz", "fileCount": 63, "integrity": "sha512-KioOCsSvSvXx6xUNLiJz+P+VMb7NRcePjoefOr74Y5P6lEKsiOn35eZyZzgpK4XCNJdXTDR7+zykj0lwxRvZ2g==", "signatures": [{"sig": "MEUCIQDrAtncuSUXC3RCQLJSSz1A9tlF7Cz4zYZc3bs5MA9RBAIgMR8EsykeEsUx7Evv1Yo0aTD8RV+6hscbhC3vllysHN0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 624853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcy3IYCRA9TVsSAnZWagAACAsP/3SNKslTEbbiYWeTM5Do\nQsEXUnmBtYv82/i7K3+T0sH4R+Ui+iqdejAW3ihgypd4d1l+MUSWFSk6orlP\nTpe2wRKWimIDSH4Zxjm1doLtHp2YPBtqFOwuBVSGw1RJl5mjrpuJMsZrCoJU\nqSnSNj+QfVEQdLBlKRsMeMSnOkUmG0M74huigqZu2QN8neMz9xAblbRSs8vG\nihd1enkrKL3WR5/ODr8xbY7yzTclVcWXU2uELNKtqLzI5ZDxAiXioJi54pFf\ndYDGjM9bm9WK375BALtBjCa8LMHLOiDCbu9HxVdAl99iYPiaRByjVtpTF3vA\nuUF0scDx1rM1q8FS1DQr1pOufg2hlluty4TO6/hKEn/8TdTtlzgbqwOifv83\nnGSrLlKXtZ3jCmwEe4v2mwDwiR9w7CX0sOP3QYdJsmvGfDl3Sx3JtID4b0c4\nPAxZY9H5W0Ew4rnxEQxc2NkwWt0xjVBuX0mn4vzm4KPEmEXUrMkmCiId6YQP\n0zxBytjNQ0XGAxpBvSlAsuAUnmxRUR5yPfreJvfb9zrFhn0qVt9l64PADqMI\naNuK0f0SkWOf6ic18NGPJMeC9vFcutpmrztmNsJP1rotUCwz0RUWZTShmH+/\nk+BsqJXQPGXa7gy7/fNjmPTjoC1sFtjqJUPZaXXMx4yRQbTMtV4bex7HwGio\nKY5y\r\n=FVcA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index", "types": "dist/lib/index.d.ts", "scripts": {"lint": "gulp :lint:fix", "test": "gulp test", "prepare": "gulp all:tsconfig.json && gulp :tslint.json && gulp dist", "pretest": "gulp lib:build", "prepublishOnly": "npm test"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/demurgos/v8-coverage.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Helper functions for V8 coverage files.", "directories": {}, "_nodeVersion": "11.14.0", "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.2", "gulp": "^4.0.0", "codecov": "^3.0.2", "ts-node": "^7.0.0", "gulp-cli": "^2.0.1", "minimist": "^1.2.0", "pre-commit": "^1.2.2", "turbo-gulp": "^0.17.1", "@types/chai": "^4.1.4", "@types/gulp": "^4.0.5", "@types/node": "^10.5.4", "@types/mocha": "^5.2.2", "@types/minimist": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-coverage_0.2.1_1556836887928_0.5728484847434072", "host": "s3://npm-registry-packages"}}, "0.2.2": {"name": "@bcoe/v8-coverage", "version": "0.2.2", "author": {"url": "https://demurgos.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@bcoe/v8-coverage@0.2.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://demurgos.github.io/v8-coverage", "bugs": {"url": "https://github.com/demurgos/v8-coverage/issues"}, "nyc": {"include": ["build/test/lib/**/*.js", "build/test/lib/**/*.mjs"], "reporter": ["text", "html"], "extension": [".mjs"]}, "dist": {"shasum": "facfada4a2a9eae8c420ddceae404558905914ef", "tarball": "https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.2.tgz", "fileCount": 63, "integrity": "sha512-kbNtW3K2OGN9BVdAZIRcUGfceHQubK+U4QjF/lyWoYDqaOl60btZJM/BCtZFr8w/g40t+cZOA4nqfQYR+P0v+g==", "signatures": [{"sig": "MEQCICI/nPSSqRL0ICe0xbmn2c9ZS2kxbMN602gbOAnwSCFxAiAWR2LqSyGOjfFJctM86FulN7WPdoUXNQDxGtB0kYsSCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 311413, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdctatCRA9TVsSAnZWagAA+5wQAJ/dMoAfuyJzo/xm2I8x\n4JdNpZIzYJu3IN2fQoNZ77wdJlqC2Mx9oXY2WOWVEmyXQMJdo1GEZyI/bqkW\n6KH8lRUhtPS5pJcnT/W/MPMStrS7qBWn2fF91iuwCx6vIdz0L2YfvpRuVHIO\n7CHi5pMlYFDOZmUJJ1GcdbqwCFW1TmL05sRmWi4hBFtbExwFUiTwRuA7+u9Y\nqZSEfFt0HMIinfKl7NYc2TmWxMZd4BgA0wNR9/8fVCZLvedGb6KDIG3n28R1\n0Q9rebaDsV0AGlUaxQM7wQlKqKGTpr4S1pLy2MrqB3tDdAMdgQIyjqRGQFGi\nuZ7aYJkp4mYs29qgOPgxRDN8VrrzdBjfNPo5qrvqU246bifMm+We0XiwJ6wZ\nxaAkVVF02jyV+d68IWFOsgy2Zd34IoOp5uazPhT57C9YdwekV7pV9HUXy+ea\nk6MM0iq6q1/zJ0Ms2dJ/HYUiOa9GAzLFEAEELE3pQF+BheLOgTn5bwTJ3Wed\nDncHhIAs/DeQacghUtLiKloYoh7pBu81npKzs49mvfzELQuOHTxGnzJzzojz\noYipvBzn/1Br6nWn0n8lm2J7K8oC0h4BiZnBtT7CfhRR+pgH380uu/6b2n3b\nYd6Q50mQqzLAwr3bWuDHkfnJMXSasghwIRIS6CfORbwNFvv85ge06InZ9Td0\nb8n5\r\n=n/QW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index", "types": "dist/lib/index.d.ts", "scripts": {"lint": "gulp :lint:fix", "test": "gulp test", "prepare": "gulp all:tsconfig.json && gulp dist", "pretest": "gulp lib:build"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/demurgos/v8-coverage.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "Helper functions for V8 coverage files.", "directories": {}, "_nodeVersion": "12.9.1", "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.2", "gulp": "^4.0.0", "codecov": "^3.0.2", "ts-node": "^8.3.0", "gulp-cli": "^2.0.1", "minimist": "^1.2.0", "pre-commit": "^1.2.2", "turbo-gulp": "^0.20.1", "@types/chai": "^4.1.4", "@types/gulp": "^4.0.5", "@types/node": "^10.5.4", "@types/mocha": "^5.2.2", "@types/minimist": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-coverage_0.2.2_1567807148946_0.8879793374609626", "host": "s3://npm-registry-packages"}}, "0.2.3": {"name": "@bcoe/v8-coverage", "version": "0.2.3", "author": {"url": "https://demurgos.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@bcoe/v8-coverage@0.2.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://demurgos.github.io/v8-coverage", "bugs": {"url": "https://github.com/demurgos/v8-coverage/issues"}, "nyc": {"include": ["build/test/lib/**/*.js", "build/test/lib/**/*.mjs"], "reporter": ["text", "html"], "extension": [".mjs"]}, "dist": {"shasum": "75a2e8b51cb758a7553d6804a5932d7aace75c39", "tarball": "https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz", "fileCount": 55, "integrity": "sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw==", "signatures": [{"sig": "MEUCIQCbjHW/4bRN0uRRP13cXTWYKi1zvcGc+0JwftsjrUYVLQIgJvRJzmml8XUgzk1uDvpavpFqQxaGvlqLW7m4MklyTxE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 276877, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdctcUCRA9TVsSAnZWagAAI5EP/AkYiKlJeKLqpE3m1hO7\nn5GVvxVH4dSxIJoFBL/GAaLZBzIv91KuODeru1MF9R00I8qukyPtDQQBBDUw\nrHdeFbrOOYaAtvXSBncpdExRKmpzYT5V0Ev8wqxYclDThrrISLxwnkndtxTk\n9X20aUoXCnGcVIKopaqb0xJAD4YX9HFpuqIPMS0JHW21RklG1CL/LGKAPwqc\nCbChbrOZIhVGJcLOfcJ7ZeV9khyujeVU+ZJerrLBhWFpIPKENXv0ciHdtHex\n8AIr9CWBhK7YmP3s+PDePlzKvgTg1gZ9h+jrENJWZxGTTzpWhGVx6WzLuorH\npjZhnI9J5SgysMKw1+UUlWrv8tgiRU23mcP0ixO7qDczaDldNMoH41BqaBya\nS2Tj94NiXB1D4uz7iggMtcm88TlDdUJKLqu4Bh+4E7MVJOQcH5UMKo2zbQcN\nhLsoo2ictwm3SVsIGAoZm2LwiTu4gUTpz5POWhOJYdvvnX6MowlAPffJfjPB\n899nM59Wkj6anb8J6bkgyLwxKKm+VNCtMxARwaFR7sATGggNF5TAyaKCR5QR\niplE9vomixdTDlPYPGWBLg89rcvO5oZl9JGJRwf7Wop8z1n+ZYO8Qc5+YtYN\nnbNvWsPBr7hS12CXYVjsnWnUr0LMevmtk5sfwN0gLZ/bRX8po2ablusF8gfW\nrEUp\r\n=boa9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "dist/lib/index", "types": "dist/lib/index.d.ts", "scripts": {"lint": "gulp :lint:fix", "test": "gulp test", "prepare": "gulp all:tsconfig.json && gulp dist", "pretest": "gulp lib:build"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/demurgos/v8-coverage.git", "type": "git"}, "_npmVersion": "6.10.2", "description": "Helper functions for V8 coverage files.", "directories": {}, "_nodeVersion": "12.9.1", "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.2", "gulp": "^4.0.0", "codecov": "^3.0.2", "ts-node": "^8.3.0", "gulp-cli": "^2.0.1", "minimist": "^1.2.0", "pre-commit": "^1.2.2", "turbo-gulp": "^0.20.1", "@types/chai": "^4.1.4", "@types/gulp": "^4.0.5", "@types/node": "^10.5.4", "@types/mocha": "^5.2.2", "@types/minimist": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/v8-coverage_0.2.3_1567807251533_0.10774594074584232", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "@bcoe/v8-coverage", "version": "1.0.0", "author": {"url": "https://demurgos.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@bcoe/v8-coverage@1.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://demurgos.github.io/v8-coverage", "bugs": {"url": "https://github.com/demurgos/v8-coverage/issues"}, "dist": {"shasum": "a2793166e900a3bb9fca55fbe9967b842e9b359c", "tarball": "https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-1.0.0.tgz", "fileCount": 11, "integrity": "sha512-lT2aHdNzyiYvBQekBbbctR2zF7+LC2YBjmK63brzbL3ON4HLDiPvEhX6VoR61vMK18dikfK997XrcgIrF/CI4w==", "signatures": [{"sig": "MEQCIEeDYYQLomAtex3+2o4Ht3DVuxKtTI+7PIJGBCARJgVNAiAVF2CGE8MJ7U2EabPviGUZ+H6CTFHHOf2VVmw1RbX0Tw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25824}, "main": "./lib/src/index.js", "engines": {"node": ">=18"}, "gitHead": "16040f5f71f36eabb7a4eecfc9400ed739538753", "scripts": {"fix": "prettier --write ./src/lib/*.js", "test": "c8 mocha ./src/test/*.js", "posttest": "prettier --check ./src/lib/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/demurgos/v8-coverage.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Helper functions for V8 coverage files.", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.2", "chai": "^4.5.0", "mocha": "^11.0.1", "prettier": "3.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/v8-coverage_1.0.0_1733854582850_0.6131311534801358", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.1": {"name": "@bcoe/v8-coverage", "version": "1.0.1", "author": {"url": "https://demurgos.net", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@bcoe/v8-coverage@1.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://demurgos.github.io/v8-coverage", "bugs": {"url": "https://github.com/demurgos/v8-coverage/issues"}, "dist": {"shasum": "d72197747b8c7f7d63faa4f91de26fa649955a6d", "tarball": "https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-1.0.1.tgz", "fileCount": 11, "integrity": "sha512-W+a0/JpU28AqH4IKtwUPcEUnUyXMDLALcn5/JLczGGT9fHE2sIby/xP/oQnx3nxkForzgzPy201RAKcB4xPAFQ==", "signatures": [{"sig": "MEQCIHVVqtn7FJYmCCYFWXJKY3MXJHGnkeoMTdmfcBP3g5vmAiA06yiDQiv+hwA3/4BWbRYPuEAsky9V8k9Oo8inwQPOsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25824}, "main": "./src/lib/index.js", "engines": {"node": ">=18"}, "gitHead": "346650d390c999ecc1b4567c99a43b92bd2c2b31", "scripts": {"fix": "prettier --write ./src/lib/*.js", "test": "c8 mocha ./src/test/*.js", "posttest": "prettier --check ./src/lib/*.js"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git://github.com/demurgos/v8-coverage.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Helper functions for V8 coverage files.", "directories": {}, "_nodeVersion": "18.20.5", "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.2", "chai": "^4.5.0", "mocha": "^11.0.1", "prettier": "3.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/v8-coverage_1.0.1_1733855252061_0.24837895930181064", "host": "s3://npm-registry-packages-npm-production"}}, "1.0.2": {"name": "@bcoe/v8-coverage", "version": "1.0.2", "description": "Helper functions for V8 coverage files.", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://demurgos.net"}, "license": "MIT", "main": "./src/lib/index.js", "repository": {"type": "git", "url": "git://github.com/bcoe/v8-coverage.git"}, "scripts": {"test": "c8 mocha ./src/test/*.js", "posttest": "prettier --check ./src/lib/*.js", "fix": "prettier --write ./src/lib/*.js"}, "devDependencies": {"c8": "^10.1.2", "chai": "^4.5.0", "mocha": "^11.0.1", "prettier": "3.4.2"}, "engines": {"node": ">=18"}, "_id": "@bcoe/v8-coverage@1.0.2", "gitHead": "afd9fde183bf65fda8ea4cb27bcf470ae51a33f7", "bugs": {"url": "https://github.com/bcoe/v8-coverage/issues"}, "homepage": "https://github.com/bcoe/v8-coverage#readme", "_nodeVersion": "22.5.1", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-6zABk/ECA/QYSCQ1NGiVwwbQerUCZ+TQbp64Q3AgmfNvurHH0j8TtXa1qbShXA6qqkpAj4V5W8pP6mLe1mcMqA==", "shasum": "bbe12dca5b4ef983a0d0af4b07b9bc90ea0ababa", "tarball": "https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-1.0.2.tgz", "fileCount": 11, "unpackedSize": 25764, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCIH1DWgwqRcLNNSlkwANqSTnvOBIAWPN73p0ZhBjTWBAIgJ/APgUF6w3ou4XNc2+O4o4UUcKttkoEgeJg9ic5ubpE="}]}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/v8-coverage_1.0.2_1736962244087_0.4750662028524004"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-01-20T06:24:14.742Z", "modified": "2025-01-15T17:30:44.481Z", "0.1.0": "2019-01-20T06:24:14.929Z", "0.2.0": "2019-05-02T22:22:37.620Z", "0.2.1": "2019-05-02T22:41:28.159Z", "0.2.2": "2019-09-06T21:59:09.061Z", "0.2.3": "2019-09-06T22:00:51.652Z", "1.0.0": "2024-12-10T18:16:22.993Z", "1.0.1": "2024-12-10T18:27:32.245Z", "1.0.2": "2025-01-15T17:30:44.306Z"}, "bugs": {"url": "https://github.com/bcoe/v8-coverage/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://demurgos.net"}, "license": "MIT", "homepage": "https://github.com/bcoe/v8-coverage#readme", "repository": {"type": "git", "url": "git://github.com/bcoe/v8-coverage.git"}, "description": "Helper functions for V8 coverage files.", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "readme": "# V8 Coverage\n\n[![npm](https://img.shields.io/npm/v/@c88/v8-coverage.svg?maxAge=2592000)](https://www.npmjs.com/package/@c88/v8-coverage)\n[![GitHub repository](https://img.shields.io/badge/Github-demurgos%2Fv8--coverage-blue.svg)](https://github.com/demurgos/v8-coverage)\n[![Build status (Travis)](https://img.shields.io/travis/demurgos/v8-coverage/master.svg?maxAge=2592000)](https://travis-ci.org/demurgos/v8-coverage)\n[![Build status (AppVeyor)](https://ci.appveyor.com/api/projects/status/qgcbdffyb9e09d0e?svg=true)](https://ci.appveyor.com/project/demurgos/v8-coverage)\n[![Codecov](https://codecov.io/gh/demurgos/v8-coverage/branch/master/graph/badge.svg)](https://codecov.io/gh/demurgos/v8-coverage)\n\n## License\n\n[MIT License](./LICENSE.md)\n", "readmeFilename": "README.md"}