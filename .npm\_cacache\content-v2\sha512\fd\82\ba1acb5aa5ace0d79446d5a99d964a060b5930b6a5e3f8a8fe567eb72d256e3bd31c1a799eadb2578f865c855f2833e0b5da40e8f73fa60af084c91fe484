{"_id": "@babel/plugin-syntax-nullish-coalescing-operator", "_rev": "70-5dde0170f4003e206794e36a15f645d4", "name": "@babel/plugin-syntax-nullish-coalescing-operator", "description": "Allow parsing of the nullish-coalescing operator", "dist-tags": {"latest": "7.8.3"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.4", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.4", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-CZehtOszohoIUxcspIGEV2xmxVh7fytkeF0OL/UXvhAj+5oNdKLT58ZE/E5T53zkNrYZ9RpeqIIyDFVQZGSnpw==", "shasum": "590de568872a8ac3d0c9288ca1ea74e0873edabf", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.4.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCWRhIUfZOs/fAF4Oz6VEmrM+ExLsYYTigpqV83p9jy/AIgW6FqsOH8fs2qtNBxGoai/sAfOsMeSeSgN4ECh/zQWMs="}]}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.4.tgz_1509388460883_0.613324873149395"}, "directories": {}}, "7.0.0-beta.5": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.5", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.5", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-xdynErN6a+1VwQsdhBVE2KOwzV0n6SWIeIzemecvmActBiHOwwAAT0iepFJmIIl5po77rsopP9AW15wjjnIHiQ==", "shasum": "98e84dc55edf2e4591b5d2384ab1f3582d86d190", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.5.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDQsdF8d9ClE6bjnaL6UbzuMfd2E0qXf51agPtYSDF0YgIhAMnI+Dd7KJWAd1Bm0u+8Fn/yTDHx5RCwfswizElhnbk4"}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.5.tgz_1509396963701_0.40660152677446604"}, "directories": {}}, "7.0.0-beta.31": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.31", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.31", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-OsXzRAwpHyDle7y29+DM3Ffh9r+xMEme1cxkltqokV/RTKr9YdOyCfTucUrE3FLgE5AA0hAlB+OWUseEXvUtJw==", "shasum": "6b152a763043e3b0ad10e0f96c17272656c73050", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.31.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJovoVKTpp32KihnbOatlpwg7+XxCpqdLJzHsP1dxfNQIhAONtWc5KfoeR4NQ5ClmpjZKWkpAqciLaJmz4nTF+biL+"}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.31.tgz_1509739396052_0.6699443662073463"}, "directories": {}}, "7.0.0-beta.32": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.32", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.32", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-eVbSjOELV8t5UxEu3K7QZZw1tATjvTh8ex39xBUH6mpon3Ji6CDreNHp6Plfj+5EkjlBQxj876Oo9i760RZiaw==", "shasum": "dfa6c96314fb15d3cd4081d164f2e4d2e64ed2f7", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.32.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCml0e/McjnyMElDgo6Hqy1Et/+P0Q6Mh7BSRYAehrRgwIgPaH0fGCCU2GJdkOr0TTzX6m5IuHAQOKAJeVgUt/4+44="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.32.tgz_1510493583625_0.26631048833951354"}, "directories": {}}, "7.0.0-beta.33": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.33", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.33", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ZKpOcOf6GyyqKNtIIaDO8xBGyXlrr6X/L/dDgtX74ZSBGXk1dV7G19QIi8X+baok+mFW6fxERV1obwt9wV45jw==", "shasum": "d7af121caa2cfefe8706a1cdf391b99d560bedc1", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.33.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDyo6fV3x8td2+383oAjdHGBIE1dpsagYpVGq5H/OHvKQIgLIAgRDR7FsbkjHsVDUJ+T1T21/YUKC300GgRn0JSdPo="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.33.tgz_1512137833729_0.42847350402735174"}, "directories": {}}, "7.0.0-beta.34": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.34", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.34", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-5P3I3SAOpcf+fi+/eYGkxP5RA9GwPjFC7uttfWTJwm0STr6s09w7lXlQEqGbSLQFOxO+PMJ4wMPVeb2+puNpoQ==", "shasum": "db2c95fe440a984d9437556053c232c5116f69dc", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.34.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDHykwh3TcqpkTIkIL9EsZ7bOSg7OlrZwf7teiBQCyk4AiEAvvN9ZF5Kmbf8wwd5LkH2udV1/3nnvqAwKgNQp5HxQqM="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.34.tgz_1512225548086_0.06983535620383918"}, "directories": {}}, "7.0.0-beta.35": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.35", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.35", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ojHYh+dDwd9yHpGeO20OUbBjeAqnfr+F7ZUqEGIla359NWAPGLF3hxpVXYzkDVO9G1fApgTwMjQqsnl1xbcSfA==", "shasum": "0e4318e8bd28ee28d7088715ede08ea101728c0f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.35.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCN2xrt/ls1Ovvd4/hEK4edsbAefejkYqTaNrpVW4TCIQIgVT8UYdvp2Eo3gXhk0+mzc6U8Q+lhwFWJc7xiSJOqc3A="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.35.tgz_1513288055808_0.2688905799295753"}, "directories": {}}, "7.0.0-beta.36": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.36", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.36", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-IDfUmmCuxaHV0tXQkf4jYfPi4rXioImzTytPDt77l6A7K6ykZNBbTlJpHo5vUHc0I/+JTXG/j8bi6PAMHrUzhw==", "shasum": "350c655474bd4d9a95e5b8f549fcfa3c8f344bf0", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.36.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFr7JoJnYsta3SRHh625j3XiPRAGDUmkyTGj4x9tUm6eAiAk8tJlvYbJlxFNEksbuc8/i1kTTFh83e5SognyoPWHZQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "kentcdodds"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "not-an-a<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "mysticatea"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.36.tgz_1514228663781_0.15456548426300287"}, "directories": {}}, "7.0.0-beta.37": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.37", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.37", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-+nbYd4aUI32ouGxO0xahCkhGecBo33JURCpDpz643IsLTEDK3oBI+yWIZ8fV5CvS9Ue39B9SUcG93dBQNNO4Bw==", "shasum": "b30dd53061fd431b7ebff833d8ca00b1fbc50984", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.37.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC91+SwDRwOVONpnbpXEUmMxR8o5+OyYoldcg9bzC06dAiAjSYQdB9cl+nApk6F1V34GjV7ORWUFPQvX5cWOTCJqSA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.37.tgz_1515427343320_0.8664554932620376"}, "directories": {}}, "7.0.0-beta.38": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.38", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.38", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-jw1/j1gydzmFMQUwccNmorJ6xgUCzyvtJg1PgFSmoCN4cBf27APLX4IsKIywndTBTA53RrjS0f+rTyJ/15TygA==", "shasum": "89d6c554d4476a675abb39509b45afbe910f228c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.38.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCydOBD3kXniKBCEX/Z3JNtlL0wJ7a5aJEsbwpSk9vrkQIhAKUk+oXYyBDUea9rD++XjB+V5EnMCGLnDxzmZ02Uc9B/"}]}, "maintainers": [{"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.38.tgz_1516206703381_0.8147818627767265"}, "directories": {}}, "7.0.0-beta.39": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.39", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.39", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-pmkhzsVAe3uDsHo+LpXffeR1p6pAtDq6YSXcylmLBHHEHEp+FgiMWqXKm067m5lwbnZh62IzCHIA6iqfApLnJg==", "shasum": "95f0937730ae77c8efa63189d0e47364bf23ceb4", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.39.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICtqANJS7pudg0GHy1+i2pDokfzrKETX2JeR6k8wuEgoAiEA8YnS2XduPnmHh5YH/rfLCvOr8nvj5Vqzmoqntvngo48="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.39.tgz_1517344047424_0.9479727610014379"}, "directories": {}}, "7.0.0-beta.40": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.40", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "devDependencies": {"@babel/core": "7.0.0-beta.40"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.40", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-DAHO3xQ5OrXx+tiBgCYT/C7rZBB8d+Q1h5P1ok0Dyt7SrqvGtOMTT0kRMy0eqGfx1SRxEOGMezy58TFpKwzyjg==", "shasum": "1bd13137a2053b1bab0bb4e914e141fd67a10d7e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.40.tgz", "fileCount": 3, "unpackedSize": 1325, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCNaN7Hwx5QSXveOokWunQG50vGrPdatGFMTAz+FVgHxgIhAOQnKt/tadhNXyNLx7g0fiiUVajei1C9TeeI2oscMvN/"}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0-beta.40_1518453684214_0.8199663083797233"}, "_hasShrinkwrap": false}, "7.0.0-beta.41": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.41", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "devDependencies": {"@babel/core": "7.0.0-beta.41"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.41", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-94ymRbycJA4vvgyu221A77px5po62xvUpDIvYgl6oBWkaHHyFQFXpLx5ZGQ/PlQS8r8pZoczUlGBqPrb5iWbUA==", "shasum": "5406a412a0ddf6406b8faa9dd2fd742947a8e0ea", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.41.tgz", "fileCount": 3, "unpackedSize": 1560, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICTgzqLjZQqDM7K6pu5iB8tmYk/w/H8l8FHmcg/y2LQLAiA+7fBK8jCQJOPGUTFHr+b5G5o+mCjM1xBEWD3zO9M4ww=="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0-beta.41_1521044756400_0.12200101602482039"}, "_hasShrinkwrap": false}, "7.0.0-beta.42": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.42", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "devDependencies": {"@babel/core": "7.0.0-beta.42"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.42", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ojHaK8RZy/FteoUIPimfn1sJ9xNlLImbT/dVLVKiyPs6O+61VKARcISjc6DruUV1e8kq76Ts9lFhM29wV2nO1Q==", "shasum": "139dd877bb5ab6db075bbe2a39cd1b1a3f856d04", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.42.tgz", "fileCount": 3, "unpackedSize": 1560, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCxW1EAdYTDjFa4ppYOKaXOKKDi5w2emolbswoBsDdB7AIgOQPmZVZwKsG4HXXWNIZHa9mGOOtNYymRkxsBH2xgzcs="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0-beta.42_1521147093397_0.9266187021481977"}, "_hasShrinkwrap": false}, "7.0.0-beta.43": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.43", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "devDependencies": {"@babel/core": "7.0.0-beta.43"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.43", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-e7zzmgOYNm/UZQtDFJVCBdTq7wx4/5I8eF2ik/IgB5BWMC8wq2L9UahaPpTADIpeumEWxG3zzKo9yevkOGlj4g==", "shasum": "15857e8148e36bf738a2f963617205c399cbb121", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.43.tgz", "fileCount": 3, "unpackedSize": 1665, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHbuTmYIyp6XBG+Sdfid2fjq/G4XI18pPyGHJwPwp4wwIgeUqUGmTR6lDPUG72moaaY7nMhc2VZFsMTgiS8DlxBDc="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0-beta.43_1522687697530_0.4596242025116053"}, "_hasShrinkwrap": false}, "7.0.0-beta.44": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.44", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "devDependencies": {"@babel/core": "7.0.0-beta.44"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.44", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-XgyuyBfgxLUtx3CjWIUBIyExPkM3SlHkOE7GmLN9d19WRtY0eHbRgAuyMLIyWTYnm+Af2wRAIdJ+pdD8x/ghRg==", "shasum": "a2b8a342636f0c43a17bcdebef558aa74ef2794b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.44.tgz", "fileCount": 3, "unpackedSize": 1716, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDjPefOe5pe+EespDK9J4fuK5p1/Kcm72B/yJ8Phi7CpQIhAJoFyAHLPtXDyCO1SSVw0NyER0QHo33+r4R9f5Q9GeOi"}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0-beta.44_1522707599366_0.9182187747505113"}, "_hasShrinkwrap": false}, "7.0.0-beta.45": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.45", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "devDependencies": {"@babel/core": "7.0.0-beta.45"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.45", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-wt5t6x3uj0PlKvbFbLz8U8G0DG8SVFq5ygMSzXCx6ivGecWn3RMC02zb9MBzaQNZESd5D+9f8yhlEcKpPCTcvw==", "shasum": "b07cf567196b4983d22b8a52e6ef91c9e506d08f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.45.tgz", "fileCount": 3, "unpackedSize": 1716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1FCRA9TVsSAnZWagAAjdEP/jFg9avT8iphJTgGn9bf\nct4pIZB2mFQypbopGuJ4Ocr6gAZZrwEkcQxDgW5DLVWlDAykO4W7R4oCsUxa\nXW9B2CS28imG/CGNTu0mO8ymIvhLjeuWavpRbIyZLqacBcslM6RyDnHo4FYc\nnPqGclUx/9xeupGo773jIdC/Jzc7DWB1coiDCFIhUSnwcQaWg3S0YE9Mz9s/\nqKbkvKjkTndZTCyqjBoKF2vk2oxaTjbkk7GT5VT2Dse5jIfIu6KG+3LRr7Qb\nIAtXC+vXRJxRaGhicZcC3yXLb85g+lRiL8Dvgc22KHgbjeZOy5aCDgkp18m2\n+TRQ1UYDE8gMp3Ulk8aYPeT7O0RGtyu2MEpWvxqDQ6FoGV5EVV+a9Qp+JQl/\nVQ8YrsuRN46hGY94bgqpt/AoeSdyAOCqhRtqf7j7LZbcw/7jIKVmBqXhTJVI\n0mngdsi8TZlGu/Yk0bxkNnVg/vKeKKWWLnPzQxHte2tNAMeYDoklbnTXdci8\nFBEy2VA5LQgSxAzKQULwxViMFKkJ0vuXIq9B94IcB7sBYTXdYX5OXqtfI16l\n4HvGrc2qtn6y/jyLVeTYFGVYzsQeaNbKA/OE3vQ79Jk5GiiTI8YCHkloHPR+\n7OH7pNFtihQ5uvHpJQeFWslKHoEPuJqyH9zgbL6d/DmT3nXxgOKHZeusUlTs\nPBeE\r\n=+ClY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD/lUMl9ImFHcFzi0Wik8L3HwqbEQTfy3y0JJWwGZC/gwIhAITozGwAIpJym0GzejgWskbs+ZmoHjRVramQyY2CBqAn"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0-beta.45_1524448580309_0.011423649274837855"}, "_hasShrinkwrap": false}, "7.0.0-beta.46": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.46", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "devDependencies": {"@babel/core": "7.0.0-beta.46"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.46", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-C3CNjd1r43Id6iqmPqZvtA6EymRCrhZsJdKY4L2NXayfyZmjHXaO/rRQU0Xh5G0jKOimU6qUkkIu+4YV0Vrd1w==", "shasum": "714544b7e3a9bc2540ee2e80319ae157ebb34d7d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.46.tgz", "fileCount": 3, "unpackedSize": 1716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WF5CRA9TVsSAnZWagAAN1UP/iewW0ZpZKWcOEdPJZcC\nePW61s1eRezfhxqhax3IdL+mHEaJ53rgwke7G5HZj+KfZew6pbC9Rgzywj1G\nfVt/QAHvDXBrPYayeJ3leYLZ+dtUheiTnbGNmIRe8Jgxp9AeGD8FDvUbRFT1\nc1+NNX+3kC+1Lwlvfk7P1EfWkTJPclUJWGGq1zan44bcQZII1hSCLecB8263\nUdgxFCu2OJJyGtebFAA7mXVlAROmRuGJTM4Cm2MIke9kb6RABxrAxOI6sGhz\npX1dEsdZutdsNL7zUEahgJZWLSNSzg/9lyFEThmCRTYRdsaK6357fH+Y4MIn\nCKVPMkaOgzjumQnzWgdhjoXRzgFmoYVIJBdvh2cs3r+bPOmhhmbtvnYJIMzn\njHmYufzXhBLrK7HgFaLIUbZkGll3NGevbvBKh/ocBnl/RpgcSyfDECIoetFZ\nHTw1ADeM6iWdbWeEF9Gf25np7rX+dHAtkD6oyzoYBOc7nHiHdY97hAOkCOag\n7BUNbxIbOixhkWajt/a2+YoZ2u/KccJ4f7/PfTVf3wBpkvysA4gVEgmqgJSC\nRHJXHHvlZQqxfrvL1r5b7k9lR7OLcuNK3lmZw68q52p2q/cCZSrgGv4HNdj7\n3PqrTq7IszZHu3GqBMUFpUTfNWO0H8nhaKs0dUZa3FAY4msCWYBixOuEQoFE\nlS5H\r\n=66qd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCOpoL9XSawRi0t3L347cEnSBa9y4xh5NP/rVIJV9s+IwIgE+Ne3DYhHI0bRg7R05QhFoQ5UDyCbB9wTLJwKxcNGYE="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0-beta.46_1524457848543_0.47418459317390793"}, "_hasShrinkwrap": false}, "7.0.0-beta.47": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.47", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "devDependencies": {"@babel/core": "7.0.0-beta.47"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.47", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-o0v9WRQwatyMSGoPIdYoK8VTDrjdHU3MQgHLcbveetueKHZGYN3MhZvkCFa86l5WKUGDF81FOk/mta/7QuDI9g==", "shasum": "24043fa9b2cdd980d4ff18b9d451569565725ebf", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.47.tgz", "fileCount": 3, "unpackedSize": 1692, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iT5CRA9TVsSAnZWagAAc9sP/isXkwXvmQkEB5AACCrD\nOewq49LlrdGOGRf++ObYthIenrRPWf4P4GwQ+KUJZrlhmUHg0ggn5inB9i4J\nwbkPr6lB94amkmErzhxbHauKV6qypaS3wVNEUgqxQIvxAmjfzdHh3swXgDwC\nd/gX8boy8c/1cT1+6hkClgI4ga69HC/+GfEKttd9I8XhSRDZlLIqsOZgRkd4\n0U5isJkIaJ03pEXDyoIBpXZfm/vDVGMuZJzBewp93jh/2cn1VGn0BnDHb6bb\ng4SaYuI228Q622q5RDEUaM1q0WXeU3WpLwjK/lHPqodCkLd8sWFjUIVXPUB+\n3kNdDifgvQPgeAI+Uxa5Vt5TK16t943iLgpFEaUrdcZZCsgh9mHu/63wbfyg\nT7qqGE3ZOjVJrajw3AI//WbT3GYtS0+CHO/0qE/PL4ZwWHDYeAr6QcN2RQIx\nxLV/A1kNYvUjznLfQoFdEWxZ3F0ffJRNBaxBtDOl9mXMrQvBPdQf6KWiu8Rr\n8r5BJoZ8msgiE3V++/ZJtjpjio/4nAA7ul0iTAhqpFtpgCkJ1UEdWdyp3G08\nud1QOvQnWpoXFoP2i2PFEAviYpTqG7ngq038yiaAwkprVMi2s17GYqCGd16+\nUOAoAf6A6qdpRnukXpM5x2LmpBbU4huenYqoR58Pv3OMO9KCBxmm+3a/elq/\nu8z6\r\n=mV5q\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCgnb0PYoSp7eKfP11X0heP3qAG/MyBVBeZ8TLn5LtvhwIhAKpeIvz+24c2OgybMZ+wI1TJVO7RkNAaa03jKqDNzQAm"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0-beta.47_1526342905012_0.11748762927274115"}, "_hasShrinkwrap": false}, "7.0.0-beta.48": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.48", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "devDependencies": {"@babel/core": "7.0.0-beta.48"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.48", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-c0A/Fz0BhAjbw39Ls2C8GtMp+m8SKg6zP/sxykqcriXqwyXj4E3xnPhGdKi1oU3Gq3HzaQE2h9LSEhWUMwxdew==", "shasum": "b21ba303d90d3ba87ab0d0b4a6691c639e4287b4", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.48.tgz", "fileCount": 3, "unpackedSize": 1674, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxDOCRA9TVsSAnZWagAAm8UQAIDizN0kvH+W8VEWDGjh\nD6Jjk8MkQwdUdg6eLdxVlmvZ+dEPVBdAj7vv+gdViaeF9jflVBhcvk924Zzj\n5nxqh2pqFNjzhIlPShdOzL1eAIae8Vzf9qp9WHLi46zBJucpEgXR6TH2NLb4\n8Oz6FSU6tX0C/L3p1Rkb4zewEya7zRyJ5LXoTFszV5sXi3B8vRTts2xrsfW4\nAx/Xbmq/S+/uy6rPPajbTB8gB1EvBnHA18/FtmkepnZ2vvxy37GsJ/pGR3xs\nfzGDQkYtmZaAdEaBmUv8zOxAkB6BsfHZB4itSBqhKeR2Zv44tfO/a6IQkex0\nmq77Faeo+WUKcUp9ceO/rlIUu4KwMcXYgc4eXzZdWRYg1OYZEhUH/VAkuAuV\nnC0XZZjo7smmXYAAUfKj9wU0KWiKM6w4gAWjxezL9mRd76B7RzjYXOnoeR3e\nac2ATdoVzd8i15ykf7ELPWOto4sJZB6jYwH4odGsKMRci5n41PiYTcXAT1m2\nOP4XLO1X6TcE9s/DYM9Sis+4OpsC3zbM+Gkv5W6NCUQ2W2rIdOgnpbQYmi3I\nskytIO9zkCA2wv0POxPDCmAcv+q2RCVvaGHPv5WPYmtXYMerQlQILondJMMp\nP+XRGVDa6QoyH95iA/6oZPnhQK2xZxdp+fKsM6v1T8EPoeYAx9x0q0bEvRxt\nmqn9\r\n=yQRn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAP/WdJq5SoDWphVI51Mngwf7CA0X53S4pIhg4PtXxFdAiB6y3DpIBxXDOJ7Qx7ytcoaemgDfC/TsEXJqxWckP7kKg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0-beta.48_1527189709729_0.9022587973953078"}, "_hasShrinkwrap": false}, "7.0.0-beta.49": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.49", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.49"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.49", "scripts": {}, "_shasum": "db947b8ac44b7fa6a7e31a258d1cad610bedd1d7", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.12.3", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "db947b8ac44b7fa6a7e31a258d1cad610bedd1d7", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.49.tgz", "fileCount": 4, "unpackedSize": 1689, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDNXCRA9TVsSAnZWagAAQv4P/jXO9F69QXoN89REQk+1\n2SuqllhaE6f3LnEHlp1rymtJzhIDRtYIj/sXIX1Pts+tyBLizblizgNAossa\nt8/a673X2wA4HwB+ipIXmti8yfAG9SfTJ/nGbmTfYydf9dgnHfc1/O4ihHwb\n26NT62DCD3vnlnflSnSXMtWc56LaMkhatkiAgcjrfT0isou/1Y/iQJb6nuf3\nSXcn8JX6+DPikn9zkOlWBe4pV/9fxisWpH8vTnni5dPZED+7XlLh2m9EYx7L\nE/T3q0OpjlUt2eDwqtLxIRsB+rPRMnIcbLkubYlSoD58HrVLxgi/j79oc/s7\n1OYXRrvL/XxPEbwOHeAZRcixQ/xiwfa8VQc6AeSG5B/IxANb/aVvQ747/ne+\nW5n17zIqPpV1f6LBZdwjEtjfsalyxm11Sz5gf1w3PhIUoVQ/imYNjR+PXo5w\nv1WfvIYc0C/q0Gh9U7dqCcAE0iu1L3q2GRcgGopjZ+Ri1sBjDbyco6eSSBnm\n/W6g4XLF79n1z+KPeSR8Vx3kFkpz5iy0yTNiczfViqHjWCgTXRBPe75JJdOT\nl1gShXoyCa/ySqNQvUTreOIe5dL2ca8AE0gFq+OUzyCqgDVG7F9qFLKs/GCI\nXa+Tt1kQFSPFRSH2+PW0ph1/bJKhiySXPt4KcBb6q531rNUzJOJdgmuFm34/\nFj1/\r\n=tSiH\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-2opJCQtNg+QHWTlaVUK+zMXpdlYgcydFJey4/c1gRZpSXHEhCYdbaCbSUJZoCoJtElb/5I/I9/mBF8owbXmAyA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAy0nNakH+WaXMuh85pnIhXeGiSXoQmC30Knj0IP3hPrAiAw7ICrzEzESyZGxcE/zCa65mFtYCeUAF6+nXQoaRQfjw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0-beta.49_1527264086471_0.6952610406649113"}, "_hasShrinkwrap": false}, "7.0.0-beta.50": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.50", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.50"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.50", "dist": {"shasum": "dffe190fed5a5ac8b7be94a25479b03e6d6f1ac4", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.50.tgz", "fileCount": 5, "unpackedSize": 1560, "integrity": "sha512-qYkmw/i/mFcLTP3ixH1pA5h08ob63f/0YbydPm8eSwwXwEzsMmdK+2tcR2bF4adQQFwdgpdnDUAV4McAzhq6Og==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMBQy7oZKADEVMoVyDDx7ZNsojg0hf+ebNEvnaMPyL/AIhAOBqzWcAh5c0DzhqziRceUWcrFUC1Qo+j1fnInuoXGdS"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0-beta.50_1528832827037_0.7402472802592275"}, "_hasShrinkwrap": false}, "7.0.0-beta.51": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.51", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.51"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.51", "dist": {"shasum": "6aa6f241453fe0f219fdb8160a0f20c293c47916", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.51.tgz", "fileCount": 5, "unpackedSize": 1574, "integrity": "sha512-9FTX/1R2uy6fWfFzjkQbdlfyLgaJ3zoAyTKAi1GOLF+DVJl3z6SI0xca9v5XOXmhdLtWSaSG8v9TJVQZfZ741Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2YgVNdJO+IHLcoVZymfR/shh8URCYXIC44IK1ESwQgQIgRJ5B2AItOZBE1PQ75Bce6+C/uzl6Hl70NklMsYpAet4="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0-beta.51_1528838374995_0.7606065161311937"}, "_hasShrinkwrap": false}, "7.0.0-beta.52": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.52", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.52"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.52", "dist": {"shasum": "06c4d6f3054e04b26685e3a7775050b3bed20e04", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.52.tgz", "fileCount": 5, "unpackedSize": 1573, "integrity": "sha512-ifXpVyEaNa2Li/37Wb/tIqbt1TjIV5kLbm9wrvUVB5OkeOIKfaKm7X1nnxQQywgobPXWMr5zHv61Sm2etZbOFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH6rCoUWPQqmbXSnwMRGUhE4QA5DN+1R8GRhTGAxlmmeAiALXTRAk7pp+m25BK6Yu7ee1Ypar0QC3hS0691B/TztgQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0-beta.52_1530838760295_0.0663786745660564"}, "_hasShrinkwrap": false}, "7.0.0-beta.53": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.53", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.53"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.53", "dist": {"shasum": "a2cf7f4ae8bea821988287ec8fff9f19277899a1", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.53.tgz", "fileCount": 5, "unpackedSize": 1573, "integrity": "sha512-4EyO4oR2JvBm8mOvNvXVhut/E7pb7hJuh7laMwklVUTc2cBOjt4HmhT/Dg869H61hYka4D65OlEsY8/BT96Luw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC4d1Ud7kctcF7ljxBID8iStJyNqgg6lSrFJAO1Zbj3lAiB0kniMMYE2flSpbuyE3OTaI320CvYG0zlNPbl/7hrV0g=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0-beta.53_1531316410151_0.6338566201709213"}, "_hasShrinkwrap": false}, "7.0.0-beta.54": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.54", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.54"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.54", "dist": {"shasum": "872b7be782ba170f12196ff5421c14fdfb8e6f9d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.54.tgz", "fileCount": 5, "unpackedSize": 1573, "integrity": "sha512-psiwBEFhJHwlR+5GcUBRcHEb0/CKCI5iiQxG6y9pHOGNOiNCBZYtpGefyi54MDVNgh8nUBLGhIsYTfHHZNrdww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEGziAuTGOmMp2GX+EW41/S6awZfbgoY9tSMZ3s4LAhyAiAbJO6FFGkjhp5C8KDS+l+NvTB3f7d0iqKnuiL/R0mnFA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0-beta.54_1531764000577_0.8151384418819516"}, "_hasShrinkwrap": false}, "7.0.0-beta.55": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.55", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.55"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.55", "dist": {"shasum": "3329baa514ee5ad830d0507e196b4d1db83c20e5", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.55.tgz", "fileCount": 5, "unpackedSize": 1573, "integrity": "sha512-8bShH7vp2/Yc0e+QdVOUrL3Uwcl5Xhd433bDeQa7lHlZyxsmomrHxqh0ybVdEttAIkk41a/UudA4TO7r2ikN6g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDCzSvJBeg6Is6DVKi8sOho8yeE294wlfHqIAJXhoUX8wIgBdDfr+QCnFnlS/Zytq0UjoL4/SR5picqsJT3Ux7Roeo="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0-beta.55_1532815626090_0.13374035650521598"}, "_hasShrinkwrap": false}, "7.0.0-beta.56": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-beta.56", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.56"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-beta.56", "dist": {"shasum": "cfaf395ee529a3533b6cef66d67014dbef407d1d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-beta.56.tgz", "integrity": "sha512-IN/k15g88E6YCd8QHH3Q3PT3BGMUGe65vUVKtyaYjRO0FlJJV1KtqZDbSucZZPoCpbtK4AaYlnlEfCCGACtxWQ==", "fileCount": 5, "unpackedSize": 1573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPupCRA9TVsSAnZWagAAHnkP/A97Qvm3XTgQf0U9Dayy\neBFtK+WOhA6qnbWLrlktOh2E35MIRiaTlzyyKyyl/Orjsv0JLvCyDmZ5dkUW\ncIz/pveoatvEWxFY/1cuhe6jCiYfp7BsCcNxy1KsLeu43DZStWi3ofks/FYH\nggmQIVhz2vPm5acP5421o0g+hLJdFq2owfhM1kc9VfReNkL1WcADpSsDmKyj\nnato9Bu0mCDJWuShsBF/5/mUDwYdP6tADpXOtHPH8SnP1exNI3WR2KX/vwDH\nNuS0uufm9wFE+aEX9lSItOkrfGpyhLT+A8JAZFs4uHroeSIHhCy/061TRSLX\np0V7MVRQRI3EBLBH3QXvevBp0b7NCl7OWz2eVe4nPHjy7sczz2FdE8nvhDDu\nk/d9A86LzFZBFmCCaUwykjE4eeg+aLYh/Wv5Ii0qKi+49tOvbZhg/Y+Seqd0\nJiIMPmf3IuQ0Cux/KN99HLoRAmFJqsU2DYLoEMdnDbR7uNb0DrXLTPA+dFlV\noilfi4u8AoAxDxnP7q01/lKW3pa4hL7Jubz2hHWA9RdKaKpO6YSfGtDGcRFX\nGoCbN3yorsr0Ilf2bqAxQek2xEJyv2HfxHf9b6Mn2FlaWWZQ83SCllGbYan0\n0nxsycBS3vFcJvltwIkUzYPWzow30ozIKMkenBXNJz4MvZB6vaP4YUnR9Os1\noC53\r\n=aaql\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID+ixlHXFVMYZwrHx7X6MYCt0y5QpQFTtdIslKAfFmAzAiAFzQJK0OZvOPqXoek9NImcBUMvERyFwifD8x1YITklXg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0-beta.56_1533344680985_0.9680684384922458"}, "_hasShrinkwrap": false}, "7.0.0-rc.0": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-rc.0", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-rc.0"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-rc.0", "dist": {"shasum": "9b1996ac10b5435423ba01b5960f4eb725ada38f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-rc.0.tgz", "integrity": "sha512-7WEC7zqZ7m1E6IGEmVBnIXq8FU73r1KJoGWgdYbpaYdGKuf2j9S5QpSyUZgxLhE3b8BXBmIzoyzaGOo2mUHItw==", "fileCount": 5, "unpackedSize": 1564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGR5CRA9TVsSAnZWagAA08YP/2Y/i1nbX7HoijZmIoLy\nQjtXuOGXo/xGqh7hJdCJc85s24bS3HNWC0Pu5QMsAaJDX+G69oe8c3+VsRgU\niQOLx3fzrmxVs2H58RL5YSXZycqwhC5NMCJA92vyf743nffK0KG07x0FemYS\nwAvC2XOFb9ZzBdimufcyOJCWjS9qIZx42hrhkwed2br1sMVjG6wmV1I4g2t8\n7hGvKsVGwgntszez5zWnajj7w4yiPjnXEbUMOY3VX83sqsgYwvrciMM0bqlR\n7e6EaaV/xGKhVRT7HvRrJxJcPsrtj37KAuL5ibsoiXJLjc4/he1BKYilFOf0\nuW9spjC+Ag2OlYGtQnyEoj4TulazBpQ/4u9lDrtiMlhHYxoaK/HFmXLDIE9M\nHaYvlioAPsNUKM8ImS7Kud/Uy5rl0JGNp7dXojP0L8+kC5CZtbh2zy29G5b4\nrvddqozxCAAtBs0MPmkCEuknI31PeFPIcBEHomDbzvTiRqGC8ta6BG1b8on4\nmeaVKc/HcNAlrg+5jqBSZPvVyrMu8WZBrf2XVwORK3nygO+hZTcJ8x6FEkNR\n9cjQSu/1IRfcZTuCTYgVftKAHtNgSny1Zy1CGQo85Y1HEAxV6leThX5+OsFo\nQQcCX8qfpw4IDSjKKltWQZkXXCoEsHaNIWo1T5Mr+wgPn3/t6ylN0n5tvwRM\nuRg9\r\n=Liyo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFXr9EqvCpYSnK3empvzcac0ibU7wDidxZq0T0ns4ZTKAiB9tGE7tvWOq3hJuVS6canxHn+hm1qWzvWOB5M+BCtK6g=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0-rc.0_1533830265386_0.9520971949009098"}, "_hasShrinkwrap": false}, "7.0.0-rc.1": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-rc.1", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.1"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-rc.1", "dist": {"shasum": "5a05132bd76745261298e2b3b6114b750c288461", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-rc.1.tgz", "integrity": "sha512-IIa5yMX5If5euenr+8r2mzG1skOWO8FGs5wRjn0sgA7M2jTQQOw9E5obXQ2YlAmNL9Df+kxqk5dS0LksLabxlg==", "fileCount": 5, "unpackedSize": 1545, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ8BCRA9TVsSAnZWagAAgtUQAJ2hmhyxBcU4MQsnWZ84\np/KSw4YG1JTuRIzWGZQB3Bsvc8Xg+D0tBaCw6MWuEKRII2Hoc2SYCZMl+9iC\njNgC5i013j1bkSi4IPMIh0OmmTXEXsKehGpU0FVsg/SbdXeRKSU0PS/znNcO\n0MrhF7s7kZZdjLumRvHFwdFH9ImDFfet/T4trVvweY9KBFIPtrvzyicQfbNs\nXd2Uuck3rxRb8fxQyGpm2kMybxUWvAS+YV0MPWwnCMc+k2e0kX3KiyO69duk\n11EMNceyrTr1eKNeXXmPPMrCzVsZEiV/KCXwVUD5t8euUZIhkrqdGZExlaNu\nXVV1eO0V0iAegTudzMGAVfxyKNX5AplObX0pjBjl9GsQOmhKvB8cbTIqgF/g\nGM65+4ZfvCEJJtuzaItOpx+QYN6rVxk3FtuUG+1EvV7UHi8VD+T6a+ia3VLe\ngIoxOclsqk6wF9G/5RsUfIJjCWX2f1C0roShBskE+b9wtGsf/0+DDfmX6TPt\n4UJ6PpBg4FQ271V0scCu/iAADbRsq90ezK6ePQM3rej5pUCFd3aMF+qwyhqJ\nD1/zHcGfM/FsqY7IhgpFLFbPAlc74iDFYo1Aq2Za3L9rUV2f7bzg7jq3vQ+9\n9S+V1auCuBkv4OHy8IpjLCWnmGOH1JnNrLSm4m4v03nNIqM2uqZBqtw0kZDA\nsEnN\r\n=SJ+l\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCEUp6AZFMhjgY6ogQ6g2mZ1He2/ZxNpkgU7PK3vMR/nQIgA1ZI/XmjckshQ2CkqsMrSjFYe438Hii/Do4GBNrt0jI="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0-rc.1_1533845248814_0.7277866133861755"}, "_hasShrinkwrap": false}, "7.0.0-rc.2": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-rc.2", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.2"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-rc.2", "dist": {"shasum": "f4d82a077b810b2e621af0ce723baebb8fa84b0d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-rc.2.tgz", "integrity": "sha512-G8qqMS9dBOPx4wZtqf5qGL/sddNsrrQOmdTK+zarFu1dz1Yc8Tlv31Mm0s5B1g9sp3IGlczqew6lSm1fXylLzQ==", "fileCount": 5, "unpackedSize": 1545, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGatCRA9TVsSAnZWagAAMBUP/jzagIbY5r/MjIkJ5wXo\nxyAlC48d1505COtyn1UA1YN6cZdgV54PMxEdirxhK+nUMGI2W0BWDDpI2c2t\nZQd5FHlWMNanWQLc1nJ4HYbLCo4WXLgVNG8/W4EJSaHUGds2Napz/DfSg7Gk\n/dacA8uqchypONFLCgLxrjdmcScePQGyM2q3z7511UVNFbq8madhGmf8oWs1\n10mJSsgDtGYb0e8m4mM4RfAg0Z+crQY2hNmiU5UEcS3fm0Kt7FFKvWKerFCR\nfNWhCEeJgQ57tkrQp6/vSB0kjZxBkw+luUgdBH8h/lN5N+yCCWs2yJSQhfbR\nlHrAZ54AuUfnGFxzTLpLupRcQ38ih+nvp8p1mgcOPvdgSCvfeRlih4Qumgsq\nCB3LHGu8AAhi0ORBnOR7otKgr/BECY1qiDM+q8HdiQ4uULmKrvqfLP1HsO2C\niPmz4Dd5OOuv76X1xzahYHG1tUupbW3bYTOZrqnhh4mPpQhs5mIKto5IyZxe\ngSYccRe9RXZHHzXN76feDqkPPqUFAIH7d6OCZ3/ZGV3Q20EY/ha2LlEtVkfF\nVhuWo/qLefd0aNIf91bzrwop/gTZsEFhVUEtgl8wWCqW5jexGFz9h8LgeF7o\nu5o85SWp2i6xk02eNViDQbo9Rps2cVo0kar7F9UC3Mg8F53YPo7qCDAao3lz\nHNBN\r\n=JBhP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG7bDnd9YzMXxWWF2oiwsHJSnkkDX1jFLgHqVaCMB7GwAiEA4ihkS28yJroQ3V+wDHLwkoDhzcKE0f36Li57I2IFAdU="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0-rc.2_1534879405089_0.09220239901919669"}, "_hasShrinkwrap": false}, "7.0.0-rc.3": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-rc.3", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.3"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-rc.3", "dist": {"shasum": "98d28a1c3c400644dbad3535b3fa55eee8860572", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-rc.3.tgz", "integrity": "sha512-GRvWIOhC/i7c4L6r0i8i+tpdx62L2H1/+KMM8nQ0eW5t3kHvj8DADI8/f+0WPoSX9fNr4witOk4bNpQk2L1L3w==", "fileCount": 6, "unpackedSize": 2644, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgElZCRA9TVsSAnZWagAAbkEP/ifN8c4lnjancfB0j5VS\n7MHKb9Yw8y0zYa0nLunfUbzyJAioYOshzgbJkElc7DRm2xxmfdjKOyw922oG\nYfKaDasfzU2f1YJNmDlosVAKExgSjV7t0nAJNLP9E5oK/vCUeEnfFMBqQZQQ\noFAifhGbOIIl27uLUP2+9xxiy3HUYEGAyJa9h8v2fVSMxQylUEnqEqSvWDzb\nK2Vyq+I/7bURa2cWm3eJQzmiHP3VaP2Lz47nhm5HgVxgzNtzkVxJMj6xhEkl\nxhQIIdRnC+kLxD+6f4TdOvV3JwGaBdZ8tnQgz2qyTOkc2k1vgaX9C/LsfOXC\nmke4Z4zXIRv6SnBcyF4WEOEXx7HbBYmlgIFjOzwiyk52PPpSTI6tMqwookUZ\nygA61XSOQbqbx0XJOzy+R8SvWYQTfSPwk1GV2EpeoYbdhxihL+RSBvhUjr9E\nKVy+mOkk6wHj+4WAskm9xABjL3XLrrUe00v1mV6FRm+iUuO+6SCnPQalmbFP\nc7oUxjNHx9UcJ4wIPsTnW8jTVopgQYbzSmFgq9AdKieOoSEPviTr4uUg9Od4\nGDQiP9HT37nLsHzHqWe2Us40uUovGtaXJiEWcevbAa+Hun4FUbaO7jeToFgs\nqVJMwZVEZM/yNhI9wBhChvNUItFT8JroJGl//MODKR1Ryb1XiXyvqUa2AIh+\nTHnN\r\n=kOP7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEt9sU+OsHbK5Bh+J16KK+klDuWqWJNO/L+96TAskGQnAiBCQzLIwyhrvRsOj+gMFH7XuFTff1eyuqdC3rUEEmW/4w=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0-rc.3_1535134041246_0.3564593559642508"}, "_hasShrinkwrap": false}, "7.0.0-rc.4": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0-rc.4", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0-rc.4"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0-rc.4", "dist": {"shasum": "ca0d7f5dc310f94843f2ffe1601324df65e69bec", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0-rc.4.tgz", "integrity": "sha512-iuH5vV4XVlFProO/6YZknZqzUlB+dpp2GTlp+vxGPlYGyFBB1YraXDrPjytcRW+1iK4NZYPczMKlqxqBvwtyKg==", "fileCount": 6, "unpackedSize": 2646, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCo4CRA9TVsSAnZWagAAKiUP/2Y/oo/PU64RfaDTNwh1\nWTwylq3NFXwRwdAuWKzJtLRo3YJMte64TaZnFKhWvig/8Hz7jaXjZ5p2edRd\nAluGtel8vhatwO40qoytGT2GE0Dg9f5/hgrElu/LesXxFCmmthFK4/sXwXa0\nxVN6h7mDI/ZL9pYpPzHP9ZuffqmHx8IY1gsgtpIJ/3vSVQjUIhsyoHOOPcsz\nKl+6013RG0BIgoDKzv9/vjwITi/5uHK/E0hPbL58TMgH5NqArbkKXIxkh4hw\nFk60OJCY0OPWZn9L+SJdqa/2aACOeEdrFWqF2IDnEsyUBtbbuVbZ7DKWnW+O\nQwVfq1OMWgeItv8m/FXZ+dwZfO+ZS1M4fEDYgd1YW9jjUeyVbQlIh4WVO6MM\nv7YHbtFxVPuRbe3zGiwwxc2uSrjDnndT+mMGZVdTl976Qucmfn2yyqxZAzgG\ndu/v9UyJPs42/qPtQB3OtP7yiMUtQIGeM7+vANbBsgqO0DJeqNT9eSrLlOjp\nv5rwWKGWzoFpN9lJRUn9UMLsv22yNZ4z9J7wwW7BbKdAo4ITGwlwZ0VCI8l/\nimDmMoyWZMiBUQ+SUWMnjf5tP+6l9t69IZRAeT4/+tnxnTpp472abDd/eFsy\nBKhnaK9zyQRL1nRUS15qUSHdAtghjYD+WO310wqOWxmMcuyQckGTgqYMoVMG\nqMIp\r\n=TWgk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEsgedtFBwTHTOf0ab1SVjqiEdgj/UEPyTOH2N1hLgsOAiEAhRSt7fLRJPFpQI65Sas8WfiriVChFQloNVaZp6H2pfM="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0-rc.4_1535388215792_0.7936671924195304"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.0.0", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0"}, "readmeFilename": "README.md", "readme": "# @babel/plugin-syntax-nullish-coalescing-operator\n\n> Allow parsing of the nullish-coalescing operator\n\nSee our website [@babel/plugin-syntax-nullish-coalescing-operator](https://babeljs.io/docs/en/next/babel-plugin-syntax-nullish-coalescing-operator.html) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-syntax-nullish-coalescing-operator\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-syntax-nullish-coalescing-operator --dev\n```\n", "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.0.0", "dist": {"shasum": "b60931d5a15da82625fff6657c39419969598743", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.0.0.tgz", "integrity": "sha512-oAJmMsAvTSIk9y0sZdU2S/nY44PEUuHN7EzNDMgbuR4e/OwyfR9lSmoBJBZ2lslFZIqhksrTt4i+av7uKfNYDw==", "fileCount": 6, "unpackedSize": 2631, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHBTCRA9TVsSAnZWagAAW9wP/iBxIAAtZWMxXXkLkpcY\n1I+35ZpB3VGA4knuPd2bOdPWa5vz7cfnHF04Y9qb1WHOSVWucKw77xB4sLpm\nQaafVDBnJuvvYkKvDz0JM+6tub76dC40s0hERaMT21dn8u/y7Mbi3S8Q/uQf\n/yNs9+EC7CcTkbCS16nI8JPbpbX0cJSiwbRFOsajAj6l1/JcbzNzVjac6/Jf\nmwXm+TCDCOSd0WG3ZwzjVaL7o1wnTaoVZFgtk2Dz8GbQ5eQcgooyENjWsVBv\nXtb743wJuAmVl8GFLFqyfKreiKoNJhRIpAiGmrdeYDTqZaermsM7B2x6RDI/\nt1leBsRkhVGqozw5ZQ3jhJvi1kDl6LyDW3M8zQ8IyJJj31FSfVcYfj2JaFGw\nl1nDbFqfT51Ed7W+H9WgNSHJj+W2/oPHH5oalaOcmKAu4qiU0KnFbDeYxYVs\nf3xQeYNvAS2S4cjeAppDmDUxacCI6L3lBu6NCCn+3aa2FgKW0lu72bCJHd0T\npNYo9QZZ8WrK30mWZaASb5vV+zdmPtKJtSIzN85Kp2M19RXbYogk2wpN2NKF\nFFwEiRsBe7LI9yo1qLME4akb8Ep2tyn6iKAfXXjYuTJftGDqgJzIzSuhle5q\nloolNLUii3lUyGldu1XaxiLT6kVkOAkK+PijipIS4RW/hE/A4N6wznlDcbUc\nfmp5\r\n=AApL\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAKvKB1ef8B4Ang4GU2q1P69KaAbLuqHyPxdqPy7LQlqAiBTwjqHpiLsLl9foGqwR1JYs+nXuFN8P8EX41t1fmorSA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.0.0_1535406163266_0.41603827301444696"}, "_hasShrinkwrap": false}, "7.2.0": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.2.0", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.2.0"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.2.0", "dist": {"shasum": "f75083dfd5ade73e783db729bbd87e7b9efb7624", "integrity": "sha512-lRCEaKE+LTxDQtgbYajI04ddt6WW0WJq57xqkAZ+s11h4YgfRHhVA/Y2VhfPzzFD4qeLHWg32DMp9HooY4Kqlg==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.2.0.tgz", "fileCount": 6, "unpackedSize": 2733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX1pCRA9TVsSAnZWagAAohgQAIw0PE8PtbESbFDnxxe9\nGjFVw8ZPbsA5/X/TJE6MB+c3BiK9WgvWdcUmg3y/Ufpn/zooMcQ3nrcSB36n\n2RT+cduTsAvimPyJgpTWcXUoAp1ByN0C3pjfGbthlirTs8yl+godOj6+OBPf\nNurq45skWw0cEei0swtH4/gQgz3UORX3DkFbaXKNFoYVoTtCttp7jZx0KM69\njQd9L7NOAimplhllHfuGRpTC5E40U4sBeD+ayK1C8D1pTm/aMDEJlpn3PAPB\n44RKIZmavcGeMSbOn94e5U+rVaw0x3GYqtNg6o13ofeQbr44xoN29piINg+5\nVfgBGHZLePqlDG3PYho/Due6/DY5Ct25dn9jAqqxUJVAbMLcszMZXpOc78Qm\nMR9oENzRn52wPLrjZ2kdq1ZEWPoOGUiRfIFjpKLprDs9tKPsKqR+X+7+pJ0S\nq7ElW0qx72PRNsU8GkSxRaNb4mxswpDo5lXC41uhDz4zjvA63uA0OGlG1NhQ\naO9Xzi4p1z4Q903w0P1b9eIZXWI24X1qImjbFublIrnyQRgZ8gxO1P+eU8tK\nL/Jj8lc8DZGbfFv+VQD8cViLsXBp4Z7pHauKqAZ2DjtnbDFd0AJeqyggfDJE\nDO2CIHHWYc5awNYTakfNJDp4HjNWWa9j+y1/K+KxUKbqIWhibzsdcgl6O4i5\nUgU7\r\n=mIWI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCElao73i7mSD7BIB1SrTxoDzwCo21ms/YR6LahKho4PwIgTbbTjoRxP7N3DVPHQDirtShTEHzMPn65qhtlPHsIrK8="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.2.0_1543863656696_0.654487906761237"}, "_hasShrinkwrap": false}, "7.7.4": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.7.4", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.7.4", "_nodeVersion": "13.1.0", "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "dist": {"integrity": "sha512-XKh/yIRPiQTOeBg0QJjEus5qiSKucKAiApNtO1psqG7D17xmE+X2i5ZqBEuSvo0HRuyPaKaSN/Gy+Ha9KFQolw==", "shasum": "e53b751d0c3061b1ba3089242524b65a7a9da12b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.7.4.tgz", "fileCount": 4, "unpackedSize": 2691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/yCRA9TVsSAnZWagAAoWYP/RoOo/34YEfLAUCA82/0\nke7UNBJoEzveFUHJN5su9E++9fbcabZTVbbd1QjXNAKH7Y7DQQ4wCufmcdU+\nzyex96qweTXtreN1gtiler3BedmwuCni7yxmVthcShVrAZ9+l8RVxZit4YL7\nzSWSqwqazSJ3fy9ZP4QjK6sU1dIRXtGorI+Cbw8f5hj5Afov2PuysZWH5ify\nZegb6T3VPas1M5ZTAqrBzmXzUZ5FgktRMmNeP7rM9EBb4oVNS3CBglstKsKZ\nS2E75QtNmd42+VsiOuQgvdxyhBHzIgtAkLaKA10hNT2YOIlZxPMQQ4P1D969\nUTpwfmL/UD3QR/h/bjrstmKFAitJqkkSySv8vsVXeIDQJRAkwWvKmLsjpaRX\nKLGTy8VBCaa6Z2quce4HlMDKu1zhdHn97gsUFMKfbrDzoHgAitTrLQoKuViH\nkJXDZ4Gcl+Z5d4xdnetcDgJ38jHQxWjH2MC9lzRmJG+EgcLl9WZltnVNnNZl\nOts/N2I8VYCiuDJTvkMxTVMihp8NVYBMF0gTMWAXe8pH8tQYEuKHCbKNw5C3\nu5RLmDzvsirAqfJxamjsp2NL3Uko05HiUpjWlqJALNBI8Zy1mWK05vpEpBYn\nmdfVYkmI665S02JSUA4+MZJUvcWXmY87+hivkPpfYuR0y71aD9ACOFEqDnXY\nPcqo\r\n=OJpA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCvTFZxzuOrHo69GDgJf2aWfveV1D3QhjtrxmUQ4vp/GwIgWEz3KFWBNmRj43UNvluWzNSbwLu0HhGcEd2r0EGC7yQ="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.7.4_1574465521924_0.06828920040189601"}, "_hasShrinkwrap": false}, "7.8.0": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.8.0", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "type": "commonjs", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0"}, "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.8.0", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"integrity": "sha512-Rv2hnBToN6rbA9hO2a4vtwXZLzNa+TWkoSIMMvUezFz5+D9NPeX7SFrArwtFzzbwndmWiqboTr5rNpzAz0MPpA==", "shasum": "5d8f24ecffa4ae74164e53264953c5ea8ba6d149", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.0.tgz", "fileCount": 4, "unpackedSize": 2713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVWCRA9TVsSAnZWagAARGEP/1QFko6UM4VFXwsVSm0Z\niIcShClKnc8VUT5s/Q8h0CKbQHKc6LCf/WIhpxTDHSIJkqZd7s8r4avX6E4l\nxKShkuaeD0jLoG4sk/OYCrREX+zyFSn0i6tZx7Ln3GaULrLQ6Jf+Y4A6oQ56\nmQMiMwgh6WeMj4H5kGsXwDS7mExIE2aWNFzjUzTyr3JalK74RxPCyeUkF7AD\nQHCGjTBndFyCk+j4RYw/yFmXu28Z9Tto/7sp1V+vM3xKBe269r8SKL2dcr/L\n+5hEShtO/R8z7W303FQz9j/fv1zZ4NQfSOExIY5GHJAT/irnJ/rhyPGAKrdw\nH2ViOscv+ZFBbLV4oy9G/6GbrEDEzeQabt7l3qlyD0MqZlZfU7OYWqUe4ujg\n9KhQ418LdoL/flR4LnykVBdNxCSMyWxSvll5vH4pY9J5JWuCiaxh66iclEDv\nKL4GVHA+Zl6vfM84K68ltX9CMpbQW2ks8CqFiLl4yBVijhadGqLinWtgbHkb\nrm2PUXWwIhgYNKDyM3MP7av5Mjl6uVvjwblCpQZtZheexXasnNt7Te2lSoSf\nmk4O8EKvydiBz4hlFIaKV4LyOnQRjir9O8bD0IVzLFGXkRk5LvkretZpq60a\njKjMb3nbkdo6CaXfEuYtVsf+JSa9zhSqEMaXfJgT0BoFH3zgodsySIb8ZBqS\nKoOG\r\n=AbUE\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAOMVSYQVisqyHvpu23yMFesCTa/LOLIptzYG8Vo3YiXAiBNaTcVLp+hifaeQgs84fD4+kp6LM59r+NvhZw3FN9Jbw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.8.0_1578788181776_0.17993992374235424"}, "_hasShrinkwrap": false}, "7.8.3": {"name": "@babel/plugin-syntax-nullish-coalescing-operator", "version": "7.8.3", "description": "Allow parsing of the nullish-coalescing operator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0"}, "_id": "@babel/plugin-syntax-nullish-coalescing-operator@7.8.3", "_nodeVersion": "13.6.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==", "shasum": "167ed70368886081f74b5c36c65a88c03b66d1a9", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "fileCount": 4, "unpackedSize": 2634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHN2bCRA9TVsSAnZWagAAD5AP/Rx8tcDVxLkwF1+y1ttv\nNgMv+ge4L2TGpYvoeIxN00+dP3O1oxNwTFk4EmLcgtjMSYzLUV8zO6mtfkNn\n3Fba6KxQoZV/omfQyofjdVa5pdS2RWkq15W3ZCA4x9JCbdsqUxYLPBvxbWcb\nes215v5JZEHd25i5dPeJm7+oARXAXyVoIqaNkuwP8obFYf/wwU2g9I4mKuwb\n6g9VbyD2Xr7FnRMWprCeAomH/CH1tB2f3MkqomAWgMedNeuj7up2dKpf6E/a\n/qJ7Ki7jXh6qApGE0XfhEluNWbFSaTDM4W45gfpyZRtQh0fyhK0iM2cwAF8m\ngsR6vfOrD5XQMHaLNZOo+8AbrLy6wH7mu+YQTwlgwS854QrBYWbTbndGedww\n0u9WRvRu3wTmG3Qs01+a6lB+A+sTJkB4nb9fLS1YwGoFScL2dRaW0jZRwQFr\n6efzk8OchDXFWPCEqNgmbBCixJ1BGxmnX50d3bg6lWfJboEqwVUliZnN70/W\nyNQmkd6qghvQsqXGRzMH8AMvGOST/kpWwIimjZPDJlT8/GkkeFj8vVDkJKcy\nBCSroookYHUJ0T/JiyLoY46JjLpr1SJ9Br958JBPPm30x5ktHcDl0gNzRqvR\n+q5UcqL1XNhLw0ohB97Zi9AeHN+tfb/IZV5Y+PZ/uJwOCkehwVmlWvOL3W6X\nStTf\r\n=TscM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDQ9sBnQEMt2ek3Mxa3v7FMPaJv2z+k5I/wUhYD3zPKfwIhAKe6+Ezq0lcX78oYQJlke4MM5r47VqZvUTKBpIeckZXG"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-nullish-coalescing-operator_7.8.3_1578950042950_0.5249796385166032"}, "_hasShrinkwrap": false}}, "readme": "# @babel/plugin-syntax-nullish-coalescing-operator\n\n> Allow parsing of the nullish-coalescing operator\n\nSee our website [@babel/plugin-syntax-nullish-coalescing-operator](https://babeljs.io/docs/en/next/babel-plugin-syntax-nullish-coalescing-operator.html) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-syntax-nullish-coalescing-operator\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-syntax-nullish-coalescing-operator --dev\n```\n", "maintainers": [{"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}, {"email": "<EMAIL>", "name": "jlhwung"}], "time": {"modified": "2022-06-12T15:01:03.501Z", "created": "2017-10-30T18:34:21.006Z", "7.0.0-beta.4": "2017-10-30T18:34:21.006Z", "7.0.0-beta.5": "2017-10-30T20:56:03.757Z", "7.0.0-beta.31": "2017-11-03T20:03:16.146Z", "7.0.0-beta.32": "2017-11-12T13:33:04.489Z", "7.0.0-beta.33": "2017-12-01T14:17:14.651Z", "7.0.0-beta.34": "2017-12-02T14:39:08.968Z", "7.0.0-beta.35": "2017-12-14T21:47:35.893Z", "7.0.0-beta.36": "2017-12-25T19:04:24.599Z", "7.0.0-beta.37": "2018-01-08T16:02:23.378Z", "7.0.0-beta.38": "2018-01-17T16:31:43.553Z", "7.0.0-beta.39": "2018-01-30T20:27:27.502Z", "7.0.0-beta.40": "2018-02-12T16:41:24.344Z", "7.0.0-beta.41": "2018-03-14T16:25:57.820Z", "7.0.0-beta.42": "2018-03-15T20:51:33.444Z", "7.0.0-beta.43": "2018-04-02T16:48:17.759Z", "7.0.0-beta.44": "2018-04-02T22:19:59.432Z", "7.0.0-beta.45": "2018-04-23T01:56:20.392Z", "7.0.0-beta.46": "2018-04-23T04:30:48.614Z", "7.0.0-beta.47": "2018-05-15T00:08:25.115Z", "7.0.0-beta.48": "2018-05-24T19:21:49.813Z", "7.0.0-beta.49": "2018-05-25T16:01:26.538Z", "7.0.0-beta.50": "2018-06-12T19:47:07.130Z", "7.0.0-beta.51": "2018-06-12T21:19:35.137Z", "7.0.0-beta.52": "2018-07-06T00:59:20.353Z", "7.0.0-beta.53": "2018-07-11T13:40:10.191Z", "7.0.0-beta.54": "2018-07-16T18:00:00.624Z", "7.0.0-beta.55": "2018-07-28T22:07:06.129Z", "7.0.0-beta.56": "2018-08-04T01:04:41.133Z", "7.0.0-rc.0": "2018-08-09T15:57:45.437Z", "7.0.0-rc.1": "2018-08-09T20:07:28.893Z", "7.0.0-rc.2": "2018-08-21T19:23:25.170Z", "7.0.0-rc.3": "2018-08-24T18:07:21.302Z", "7.0.0-rc.4": "2018-08-27T16:43:35.980Z", "7.0.0": "2018-08-27T21:42:43.386Z", "7.2.0": "2018-12-03T19:00:56.797Z", "7.7.4": "2019-11-22T23:32:02.046Z", "7.8.0": "2020-01-12T00:16:21.898Z", "7.8.3": "2020-01-13T21:14:03.100Z"}, "keywords": ["babel-plugin"], "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator"}, "license": "MIT", "readmeFilename": "README.md", "users": {"dumplings": true}}