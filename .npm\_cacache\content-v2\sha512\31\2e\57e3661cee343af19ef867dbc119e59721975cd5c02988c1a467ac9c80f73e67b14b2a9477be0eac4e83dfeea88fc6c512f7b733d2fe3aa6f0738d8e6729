{"_id": "balanced-match", "_rev": "34-4b79c477b535c95b4e41227edc231cc0", "name": "balanced-match", "description": "Match balanced character pairs, like \"{\" and \"}\"", "dist-tags": {"latest": "3.0.1"}, "versions": {"0.0.0": {"name": "balanced-match", "description": "Match balanced character pairs, like \"{\" and \"}\"", "version": "0.0.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/balanced-match.git"}, "homepage": "https://github.com/juliangruber/balanced-match", "main": "index.js", "scripts": {"test": "tape test/*.js"}, "dependencies": {}, "devDependencies": {"tape": "~1.1.1"}, "keywords": ["match", "regexp", "test", "balanced", "parse"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "bugs": {"url": "https://github.com/juliangruber/balanced-match/issues"}, "_id": "balanced-match@0.0.0", "dist": {"shasum": "86efc32ae583496c1c1fbb51cd648de0363ebb03", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-0.0.0.tgz", "integrity": "sha512-daYFGv8RHJKIcx7l5jAzeS86+pMEgTAcbF7Q89qnrgRVI1GEDkuGABNGzkcWYrUwUZJ4+uUf8hF4n3SZMIPVOQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHVHvgnAs1bDFoDI6+/pz7mkkZXVfz8thHg0hQ7Y9K/8AiBABPKzjsOSHdHyBJ6tGAMBNjzJTn1XASzfvfo3CnZfAQ=="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.1": {"name": "balanced-match", "description": "Match balanced character pairs, like \"{\" and \"}\"", "version": "0.0.1", "repository": {"type": "git", "url": "git://github.com/juliangruber/balanced-match.git"}, "homepage": "https://github.com/juliangruber/balanced-match", "main": "index.js", "scripts": {"test": "tape test/*.js"}, "dependencies": {}, "devDependencies": {"tape": "~1.1.1"}, "keywords": ["match", "regexp", "test", "balanced", "parse"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "bugs": {"url": "https://github.com/juliangruber/balanced-match/issues"}, "_id": "balanced-match@0.0.1", "dist": {"shasum": "2c408589c3288fc8a152c535ed853f77763899ae", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-0.0.1.tgz", "integrity": "sha512-obnFpTIt83MxrUxnHfs4npfChWAw0YcBQui+hI1awrVPzIqpKKkQ7KTunVRKAfauTptPQXZohaPs1hf38HJ05A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCO2l/CrUtV26QU2sOMNhCk02ZePiXNQy7szGHJTneWmgIgNhB9Yc4EEGiKzMSbdGAEskqxf6DeIZLLX63/pzCdMrE="}]}, "_from": ".", "_npmVersion": "1.3.11", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.0": {"name": "balanced-match", "description": "Match balanced character pairs, like \"{\" and \"}\"", "version": "0.1.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/balanced-match.git"}, "homepage": "https://github.com/juliangruber/balanced-match", "main": "index.js", "scripts": {"test": "make test"}, "dependencies": {}, "devDependencies": {"tape": "~1.1.1"}, "keywords": ["match", "regexp", "test", "balanced", "parse"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "bugs": {"url": "https://github.com/juliangruber/balanced-match/issues"}, "_id": "balanced-match@0.1.0", "dist": {"shasum": "b504bd05869b39259dd0c5efc35d843176dccc4a", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-0.1.0.tgz", "integrity": "sha512-4xb6XqAEo3Z+5pEDJz33R8BZXI8FRJU+cDNLdKgDpmnz+pKKRVYLpdv+VvUAC7yUhBMj4izmyt19eCGv1QGV7A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIADDYF41QH1NuQ+/2uuuSzZelNXhFB1Tqi2YjQq7OuYaAiEA2BMkJ/3Tbk/knnCvb/33vauA8Rw/9xhG5PA90ipzB/U="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "balanced-match", "description": "Match balanced character pairs, like \"{\" and \"}\"", "version": "0.2.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/balanced-match.git"}, "homepage": "https://github.com/juliangruber/balanced-match", "main": "index.js", "scripts": {"test": "make test"}, "dependencies": {}, "devDependencies": {"tape": "~1.1.1"}, "keywords": ["match", "regexp", "test", "balanced", "parse"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "ba40ed78e7114a4a67c51da768a100184dead39c", "bugs": {"url": "https://github.com/juliangruber/balanced-match/issues"}, "_id": "balanced-match@0.2.0", "_shasum": "38f6730c03aab6d5edbb52bd934885e756d71674", "_from": ".", "_npmVersion": "2.1.8", "_nodeVersion": "0.10.32", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "38f6730c03aab6d5edbb52bd934885e756d71674", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-0.2.0.tgz", "integrity": "sha512-kuRgl0wyQa2pmUzVVyVQp0E04p//9u7J6Hi0Hd7fpF2Le1waUYUPmOcp6ITXNBYtBfzu9zw+aTG5eLLfYWHd1A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDorE0C4ozrLlU3/RXjoBGDnTQ1vGfaj6q66FYyGhfNsAiAWgloiwUeWMBJxB1SfnfDam7lkrmul37OR/Jb9PSXVQQ=="}]}, "directories": {}}, "0.2.1": {"name": "balanced-match", "description": "Match balanced character pairs, like \"{\" and \"}\"", "version": "0.2.1", "repository": {"type": "git", "url": "git://github.com/juliangruber/balanced-match.git"}, "homepage": "https://github.com/juliangruber/balanced-match", "main": "index.js", "scripts": {"test": "make test"}, "dependencies": {}, "devDependencies": {"tape": "~1.1.1"}, "keywords": ["match", "regexp", "test", "balanced", "parse"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "d743dd31d7376e0fcf99392a4be7227f2e99bf5d", "bugs": {"url": "https://github.com/juliangruber/balanced-match/issues"}, "_id": "balanced-match@0.2.1", "_shasum": "7bc658b4bed61eee424ad74f75f5c3e2c4df3cc7", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "7bc658b4bed61eee424ad74f75f5c3e2c4df3cc7", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-0.2.1.tgz", "integrity": "sha512-euSOvfze1jPOf85KQOmZ2UcWDJ/dUJukTJdj4o9ZZLyjl7IjdIyE4fAQRSuGrxAjB9nvvvrl4N3bPtRq+W+SyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBJ3wjKXLgAZjSqy9mOktUcOqNoQh8JSEPhMyfNsbo5hAiEA8V3Y/Vugo26oLm+5dp6W9C4PB4wKNnoPMiESz7Lj3yg="}]}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.3.0": {"name": "balanced-match", "description": "Match balanced character pairs, like \"{\" and \"}\"", "version": "0.3.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/balanced-match.git"}, "homepage": "https://github.com/juliangruber/balanced-match", "main": "index.js", "scripts": {"test": "make test"}, "dependencies": {}, "devDependencies": {"tape": "~4.2.2"}, "keywords": ["match", "regexp", "test", "balanced", "parse"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "a7114b0986554787e90b7ac595a043ca75ea77e5", "bugs": {"url": "https://github.com/juliangruber/balanced-match/issues"}, "_id": "balanced-match@0.3.0", "_shasum": "a91cdd1ebef1a86659e70ff4def01625fc2d6756", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "a91cdd1ebef1a86659e70ff4def01625fc2d6756", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-0.3.0.tgz", "integrity": "sha512-bgB9RrUMd3G7drkg5+Gv+dMZTUSFbfrrp61qsQGlTdCdIPqdzF9UG2G5Ndlg6zR3ArNeGGXMIYSYFZRRtZaT9Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCFVZKtSpYwgtaTT2Kqf1h7zkwzrSJagLcLLzTDM+RwSQIgPQSY7OnNwUniyQQvJ0f8cHvHD6YlDcpo6cvfszyv234="}]}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.4.0": {"name": "balanced-match", "description": "Match balanced character pairs, like \"{\" and \"}\"", "version": "0.4.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/balanced-match.git"}, "homepage": "https://github.com/juliangruber/balanced-match", "main": "index.js", "scripts": {"test": "make test"}, "dependencies": {}, "devDependencies": {"tape": "~4.5.0"}, "keywords": ["match", "regexp", "test", "balanced", "parse"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "d23ba945af2e80d035dde2a9d7e4ec62efbd440b", "bugs": {"url": "https://github.com/juliangruber/balanced-match/issues"}, "_id": "balanced-match@0.4.0", "_shasum": "84818b70e91d9ac8b4d77df20e9239e80c025089", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.4.1", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "84818b70e91d9ac8b4d77df20e9239e80c025089", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-0.4.0.tgz", "integrity": "sha512-0fxU/CUKHz4ojATahMymHO3MC7xccEcNISC+fNroLYitQjVUP3rEAwV8lsviJMjTlrLza4cH/TCH9kBHvSDf1Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIARCRZyc0v8nLUsZoBLVE8r0ayj+33hDa41xwLM8c7m/AiBnlZGwQD0ZJGpLfKojVsbLVxsG7CKYEgLd/AL1D791KQ=="}]}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/balanced-match-0.4.0.tgz_1460018817576_0.08597791171632707"}, "directories": {}}, "0.4.1": {"name": "balanced-match", "description": "Match balanced character pairs, like \"{\" and \"}\"", "version": "0.4.1", "repository": {"type": "git", "url": "git://github.com/juliangruber/balanced-match.git"}, "homepage": "https://github.com/juliangruber/balanced-match", "main": "index.js", "scripts": {"test": "make test"}, "dependencies": {}, "devDependencies": {"tape": "~4.5.0"}, "keywords": ["match", "regexp", "test", "balanced", "parse"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "7004b289baaaab6a832f4901735e29d37cc2a863", "bugs": {"url": "https://github.com/juliangruber/balanced-match/issues"}, "_id": "balanced-match@0.4.1", "_shasum": "19053e2e0748eadb379da6c09d455cf5e1039335", "_from": ".", "_npmVersion": "3.8.6", "_nodeVersion": "6.0.0", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "19053e2e0748eadb379da6c09d455cf5e1039335", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-0.4.1.tgz", "integrity": "sha512-vgW4YcTHFsmsL5q8x0ovPQfwzEdFCoQXv6HBse+E46uZNwA+lE5+V1G9ap3IaUz0oM9JPFiJ8tnDZjqdReFSqA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAytkJMWi5v5v1dWj3sCpqNsfuHH8wnB0+ogc3MURhfqAiEAn3aM52dUjjGxYPD6DhQHUkMuJlqw/RQRUZbTpgvycUk="}]}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/balanced-match-0.4.1.tgz_1462129663650_0.39764496590942144"}, "directories": {}}, "0.4.2": {"name": "balanced-match", "description": "Match balanced character pairs, like \"{\" and \"}\"", "version": "0.4.2", "repository": {"type": "git", "url": "git://github.com/juliangruber/balanced-match.git"}, "homepage": "https://github.com/juliangruber/balanced-match", "main": "index.js", "scripts": {"test": "make test"}, "dependencies": {}, "devDependencies": {"tape": "^4.6.0"}, "keywords": ["match", "regexp", "test", "balanced", "parse"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "57c2ea29d89a2844ae3bdcc637c6e2cbb73725e2", "bugs": {"url": "https://github.com/juliangruber/balanced-match/issues"}, "_id": "balanced-match@0.4.2", "_shasum": "cb3f3e3c732dc0f01ee70b403f302e61d7709838", "_from": ".", "_npmVersion": "2.15.8", "_nodeVersion": "4.4.7", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "cb3f3e3c732dc0f01ee70b403f302e61d7709838", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-0.4.2.tgz", "integrity": "sha512-STw03mQKnGUYtoNjmowo4F2cRmIIxYEGiMsjjwla/u5P1lxadj/05WkNaFjNiKTgJkj8KiXbgAiRTmcQRwQNtg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwR6gkoRTPsOQQNI/+S71bhdZoeEMHWYyKDMsSzVwixAIhAIllfa3v0fyWYS51UxB+4wbQk2LCtxJhWSjseBejXl9z"}]}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/balanced-match-0.4.2.tgz_1468834991581_0.6590619895141572"}, "directories": {}}, "1.0.0": {"name": "balanced-match", "description": "Match balanced character pairs, like \"{\" and \"}\"", "version": "1.0.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/balanced-match.git"}, "homepage": "https://github.com/juliangruber/balanced-match", "main": "index.js", "scripts": {"test": "make test", "bench": "make bench"}, "dependencies": {}, "devDependencies": {"matcha": "^0.7.0", "tape": "^4.6.0"}, "keywords": ["match", "regexp", "test", "balanced", "parse"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "d701a549a7653a874eebce7eca25d3577dc868ac", "bugs": {"url": "https://github.com/juliangruber/balanced-match/issues"}, "_id": "balanced-match@1.0.0", "_shasum": "89b4d199ab2bee49de164ea02b89ce462d71b767", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "7.8.0", "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "89b4d199ab2bee49de164ea02b89ce462d71b767", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.0.tgz", "integrity": "sha512-9Y0g0Q8rmSt+H33DfKv7FOc3v+iRI+o1lbzt8jGcIosYW37IIW/2XVYq5NPdmaD5NQ59Nk26Kl/vZbwW9Fr8vg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDN5U38zzaYjzNgiGzGDWu9nnWtcbrB6JezTyfWwriLJAiBjOrytimT7VRffO2Y/7LWXIOmsJFjo5toVuTAXyucXZg=="}]}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/balanced-match-1.0.0.tgz_1497251909645_0.8755026108119637"}, "directories": {}}, "1.0.1": {"name": "balanced-match", "description": "Match balanced character pairs, like \"{\" and \"}\"", "version": "1.0.1", "repository": {"type": "git", "url": "git://github.com/juliangruber/balanced-match.git"}, "homepage": "https://github.com/juliangruber/balanced-match", "main": "index.js", "scripts": {"test": "prettier-standard && standard && tape test/test.js", "bench": "matcha test/bench.js", "release": "np"}, "devDependencies": {"@c4312/matcha": "^1.3.1", "np": "^7.4.0", "prettier-standard": "^16.4.1", "standard": "^16.0.3", "tape": "^4.6.0"}, "keywords": ["match", "regexp", "test", "balanced", "parse"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "91e65ccc2a89ae0d81bb57e287131011f41a20db", "bugs": {"url": "https://github.com/juliangruber/balanced-match/issues"}, "_id": "balanced-match@1.0.1", "_nodeVersion": "15.9.0", "_npmVersion": "7.7.6", "dist": {"integrity": "sha512-qyTw2VPYRg31SlVU5WDdvCSyMTJ3YSP4Kz2CidWZFPFawCiHJdCyKyZeXIGMJ5ebMQYXEI56kDR8tcnDkbZstg==", "shasum": "4f46cf3183a02a6a25875cf9a4240c15291cf464", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.1.tgz", "fileCount": 5, "unpackedSize": 7083, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbBCwCRA9TVsSAnZWagAAyIwP/3NssbM+7PI+JjP3izQc\nc6+ePWLbBz5smmilqFyHnv8z2Ouv5PBBO6EVyyRX80DPy7KPPFUFXNOS00Vw\n8yHZ+EyaWzamt6yVDRNxx2DGf8jDzB1Axh8NwkIQKfnwsBxt/wVJFojLo6Rn\nuGOXhy2n5nbZ1JavWL8aquTx/6maPoyEu3omopwrDEhxcAmz50czBRPb8sPH\n+fQYl9SgkJdMUDAUNr65pj77v+gR4glViT838GWsoa32f/Wt/e8Na034+IeU\nzSwnEmA0cvGj2/ubkiAifPIshIXDXcEm0aSRn5lrCzmInGKtD124F5vinY3d\nXZ7CD1YGv2zQ703HZLVhAugd2/4l1Ac3Uf8bGSOFc4ipzwYXUOH8OUlIWKDU\nQ/ktMaueuBENMU4cs/ys3th5qZQFmv0vT8L8VAC1ybJ+tDF80bvvNnIlhwgb\nj2elsnB4uj5DjvDq/hjRHLODAXSJWnikm9gDtRHMcIOy6tJI39UIRfG6br0K\n6MqtN7TE4UkkkPaUEEKPv53fhABsCkkhWVKd2oW04i+hn53Iu7FZm2Pgx5yu\nC82gcej2byggT97RO9PtiNbCtkvJXm52I5dg6lIJ2/Xsqb16O///kgmD7n8/\n8u/9HNauhKbhg0wGuZJgcy0hBbOYFSJOfM+S26UHADWiPcEkJ4fc7nlqG9vv\nmMTG\r\n=ftTQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDlcpMP2h5zkcX0B8nr6vV/qhflEMPCULRG0/JGC4+B3AiAQ6pXxP1MfFPEU2M5/jichFV38qfuD5MdVDPrp4Wu9eA=="}]}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/balanced-match_1.0.1_1617694895808_0.352537932704025"}, "_hasShrinkwrap": false, "deprecated": "this package has been deprecated"}, "1.0.2": {"name": "balanced-match", "description": "Match balanced character pairs, like \"{\" and \"}\"", "version": "1.0.2", "repository": {"type": "git", "url": "git://github.com/juliangruber/balanced-match.git"}, "homepage": "https://github.com/juliangruber/balanced-match", "main": "index.js", "scripts": {"test": "tape test/test.js", "bench": "matcha test/bench.js"}, "devDependencies": {"matcha": "^0.7.0", "tape": "^4.6.0"}, "keywords": ["match", "regexp", "test", "balanced", "parse"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "c7412e09b95d6ad97fd1e2996f6adca7626a9ae8", "bugs": {"url": "https://github.com/juliangruber/balanced-match/issues"}, "_id": "balanced-match@1.0.2", "_nodeVersion": "15.9.0", "_npmVersion": "7.7.6", "dist": {"integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==", "shasum": "e83e3a7e3f300b34cb9d87f615fa0cbf357690ee", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "fileCount": 5, "unpackedSize": 6939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbFk9CRA9TVsSAnZWagAAZCkP/2oCPlLyH1O+2fxJepxC\nP64dIPH4FmdtcuRV6m9JSSnNayjLyl7KZSkzngJveJAVMwBH2oSO40HVruAc\njNGdawU0sm41Tvkxm0K9AhiT5pfqBHv6KBj/sR5+2iF56zAM7pxrc8eTsgj9\nHBAYq5ZoePKf+Kki77ilWwK1Z7VXekk3KNgPd4jsbZ58JGL2dLVmqJcOPAfx\nTRECI9NV5oyHl+EsOGnMnAB8Z7GvNH+/sVo5lWZkldStJDjlj3mZq9fxMo5I\nw/2pmVPI8dvYYA6r3mp55YYDyvWA49CoRgTHXqEy4tpHmmdTAdB2Je+3j/n0\nvbJm74Ab6CnZnwa9Oaowz+VcKkcczXICTxPj0D+ddvVksD+6VpnAz79Jyia5\nqApDNXnYv+8bdnMwhnA2tQ0vz10HANuZ1xfpXE9Yy4Py/1LsTvExovYsie1G\n9RQ1GkIpGwwyOuzbDqHtrRjduAy35VNtIw2nQTCRLz87w/7DV+RbTvaT1Fp7\nb4WQN9z6BoX0Bl/Qi8PXTDN5J8M83MsRThoYm20M0nAVeGbxrfHTMJoXvxF9\ntlHuV3E7W7x3lvG0za7wLn9p76uOzxDX8Osr5POJ/GpEVciz0PWcbHQHFHUm\nxB+x3O0C9eAdKW/9/7/YA9zMqdqcMuwg6f26neIYIk10oZQyRriBoV6OZtIy\ntw1n\r\n=eH8s\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHRQpAKwqTgs0SDP5KcV7MzsuTPMEkHeNqJFBOy5hYMwAiB/QgzhE/4zo/h6mn5Sl6u4YP0UZKqPYCZe5GhyLntdKA=="}]}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/balanced-match_1.0.2_1617713469141_0.18827857838975826"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "balanced-match", "description": "Match balanced character pairs, like \"{\" and \"}\"", "version": "2.0.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/balanced-match.git"}, "homepage": "https://github.com/juliangruber/balanced-match", "main": "index.js", "scripts": {"test": "prettier-standard && standard && tape test/test.js", "bench": "matcha test/bench.js", "release": "np"}, "devDependencies": {"@c4312/matcha": "^1.3.1", "np": "^7.4.0", "prettier-standard": "^16.4.1", "standard": "^16.0.3", "tape": "^4.6.0"}, "keywords": ["match", "regexp", "test", "balanced", "parse"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "gitHead": "1c56fa33180a54e0e69a3fae9d60c191e74c4174", "bugs": {"url": "https://github.com/juliangruber/balanced-match/issues"}, "_id": "balanced-match@2.0.0", "_nodeVersion": "15.9.0", "_npmVersion": "7.7.6", "dist": {"integrity": "sha512-1ugUSr8BHXRnK23KfuYS+gVMC3LB8QGH9W1iGtDPsNWoQbgtXSExkBu2aDR4epiGWZOjZsj6lDl/N/AqqTC3UA==", "shasum": "dc70f920d78db8b858535795867bf48f820633d9", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-2.0.0.tgz", "fileCount": 5, "unpackedSize": 7083, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbFnBCRA9TVsSAnZWagAAnBUQAIwSae9EWp8mawlco66Y\nsYcwEHdv5Cc7shxnCSIeYYGgowelCQgirX5QrJHKmPEj10UfrJJvCnHu4uMC\nvyztZIDLxtg3xWMaTObZfVRCO23S90Po81YDJBvOtrRciRGqQmZ+HWmuRYDu\nI7rtvXMK/yc31dnkOjTPBd6FjufQRfH+OyS1cPJP5/ZyXxZsiNi28jIDe/1R\nKETSdx279AtQo+vUL6uK+OnKF9Rxo8GXeabM+4dRezqWtYW1B2RugEKuhSk5\nlwXOrjJEioG+TaIozgXY8X/0hiyRW6mCisMtFE3aYxhgp/WxPwlyNV6k+dtz\nqsnrwPLlZyVg0IX16MbHXJBbr0yvynSbN2t1eUZ1kX36wquzuIMDk6H/1XNY\nhhAydNkpFGICPedeLkFVvVFjpx+zeVryhMj3sq+P5FYdIDcHkhxFDX8s3cfp\ntIrtY7Y59hMsdDnIUwp4qqOvxG7DuuEFprWG38BIVCa0hE3yA+vQ5+ACUmBo\no6DM/RUgXwuqFghoYRX00fxKSedVIWfX8f6nPyG0WhN5svfdPlC/0qayvE0r\nGllsfW6la8n1yVN8jey9we2x0OdLutG4rYB5gEzl91DoLJjP9TopCdNnZhhC\nuM3d6jLW/BVRgPTU7z6YBCXiESw0lntphDMotVZBPeXz2CTqGTNbE4vKAJq4\nROwK\r\n=8h9J\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCwPXtClY2xRtpUhfN8Otf+E02dH+DO55UcSuJ0vi+LrAIhALiaQS2V+1k2zJKf/lBKrxIRH8shIVFuYbpjfo7ABZ38"}]}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/balanced-match_2.0.0_1617713601488_0.5951607210108762"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "balanced-match", "description": "Match balanced character pairs, like \"{\" and \"}\"", "version": "3.0.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/balanced-match.git"}, "homepage": "https://github.com/juliangruber/balanced-match", "main": "index.js", "type": "module", "scripts": {"test": "standard --fix && node--test test/test.js", "bench": "matcha test/bench.js", "release": "np"}, "devDependencies": {"@c4312/matcha": "^1.3.1", "np": "^8.0.4", "standard": "^17.1.0", "test": "^3.3.0"}, "keywords": ["match", "regexp", "test", "balanced", "parse"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "engines": {"node": ">= 16"}, "gitHead": "7faf963591218df292de64f542bccbb5a85de93f", "bugs": {"url": "https://github.com/juliangruber/balanced-match/issues"}, "_id": "balanced-match@3.0.0", "_nodeVersion": "20.3.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-roy6f9Ri49dpBe1EUBikUsqhJfEVlW+oLV7JFwGm17PdkZ81xVreEYNEIsytl9NQ6fvvvJRXHyVe60O5ve6i1w==", "shasum": "c47006ef8f61f4c7ffbecbd69b2fe9c56fb8773c", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-3.0.0.tgz", "fileCount": 4, "unpackedSize": 7127, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHfh6+e4G478Rijxgz6qRQLhcQiHzmBYVuJ1mzlE6FC7AiB5VfbD/aHfrYbKC3EUC85l/DO4yGx4JK96abS4fwZ5bA=="}]}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/balanced-match_3.0.0_1696493478956_0.8748467054054856"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "balanced-match", "description": "Match balanced character pairs, like \"{\" and \"}\"", "version": "3.0.1", "repository": {"type": "git", "url": "git://github.com/juliangruber/balanced-match.git"}, "homepage": "https://github.com/juliangruber/balanced-match", "exports": "./index.js", "type": "module", "scripts": {"test": "standard --fix && node--test test/test.js", "bench": "matcha test/bench.js", "release": "np"}, "devDependencies": {"@c4312/matcha": "^1.3.1", "np": "^8.0.4", "standard": "^17.1.0", "test": "^3.3.0"}, "keywords": ["match", "regexp", "test", "balanced", "parse"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "engines": {"node": ">= 16"}, "gitHead": "bb2612142d2d40f46636319ce50197deb6254425", "bugs": {"url": "https://github.com/juliangruber/balanced-match/issues"}, "_id": "balanced-match@3.0.1", "_nodeVersion": "20.3.1", "_npmVersion": "9.6.7", "dist": {"integrity": "sha512-vjtV3hiLqYDNRoiAv0zC4QaGAMPomEoq83PRmYIofPswwZurCeWR5LByXm7SyoL0Zh5+2z0+HC7jG8gSZJUh0w==", "shasum": "e854b098724b15076384266497392a271f4a26a0", "tarball": "https://registry.npmjs.org/balanced-match/-/balanced-match-3.0.1.tgz", "fileCount": 5, "unpackedSize": 12334, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEHsKepAWqy0XBNt9lRc2IKfkNV2LfAzNNev+dVSGip1AiEAvZJxo1yLwJNtvZRe+9qcUinlJ6fC6btDmG+KvqyP4+g="}]}, "_npmUser": {"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/balanced-match_3.0.1_1696685643512_0.5915305955862984"}, "_hasShrinkwrap": false}}, "readme": "# balanced-match\n\nMatch balanced string pairs, like `{` and `}` or `<b>` and `</b>`. Supports regular expressions as well!\n\n[![CI](https://github.com/juliangruber/balanced-match/actions/workflows/ci.yml/badge.svg)](https://github.com/juliangruber/balanced-match/actions/workflows/ci.yml)\n[![downloads](https://img.shields.io/npm/dm/balanced-match.svg)](https://www.npmjs.org/package/balanced-match)\n\n## Example\n\nGet the first matching pair of braces:\n\n```js\nimport balanced from 'balanced-match'\n\nconsole.log(balanced('{', '}', 'pre{in{nested}}post'))\nconsole.log(balanced('{', '}', 'pre{first}between{second}post'))\nconsole.log(balanced(/\\s+\\{\\s+/, /\\s+\\}\\s+/, 'pre  {   in{nest}   }  post'))\n```\n\nThe matches are:\n\n```bash\n$ node example.js\n{ start: 3, end: 14, pre: 'pre', body: 'in{nested}', post: 'post' }\n{ start: 3,\n  end: 9,\n  pre: 'pre',\n  body: 'first',\n  post: 'between{second}post' }\n{ start: 3, end: 17, pre: 'pre', body: 'in{nest}', post: 'post' }\n```\n\n## API\n\n### const m = balanced(a, b, str)\n\nFor the first non-nested matching pair of `a` and `b` in `str`, return an\nobject with those keys:\n\n- **start** the index of the first match of `a`\n- **end** the index of the matching `b`\n- **pre** the preamble, `a` and `b` not included\n- **body** the match, `a` and `b` not included\n- **post** the postscript, `a` and `b` not included\n\nIf there's no match, `undefined` will be returned.\n\nIf the `str` contains more `a` than `b` / there are unmatched pairs, the first match that was closed will be used. For example, `{{a}` will match `['{', 'a', '']` and `{a}}` will match `['', 'a', '}']`.\n\n### const r = balanced.range(a, b, str)\n\nFor the first non-nested matching pair of `a` and `b` in `str`, return an\narray with indexes: `[ <a index>, <b index> ]`.\n\nIf there's no match, `undefined` will be returned.\n\nIf the `str` contains more `a` than `b` / there are unmatched pairs, the first match that was closed will be used. For example, `{{a}` will match `[ 1, 3 ]` and `{a}}` will match `[0, 2]`.\n\n## Installation\n\nWith [npm](https://npmjs.org) do:\n\n```bash\nnpm install balanced-match\n```\n\n## Security contact information\n\nTo report a security vulnerability, please use the\n[Tidelift security contact](https://tidelift.com/security).\nTidelift will coordinate the fix and disclosure.\n\n## License\n\n(MIT)\n\nCopyright (c) 2013 Julian Gruber &lt;<EMAIL>&gt;\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of\nthis software and associated documentation files (the \"Software\"), to deal in\nthe Software without restriction, including without limitation the rights to\nuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\nof the Software, and to permit persons to whom the Software is furnished to do\nso, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "maintainers": [{"name": "juli<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-10-07T13:34:03.844Z", "created": "2013-10-13T12:26:00.713Z", "0.0.0": "2013-10-13T12:26:03.806Z", "0.0.1": "2014-01-08T10:12:05.995Z", "0.1.0": "2014-04-24T12:44:58.954Z", "0.2.0": "2014-11-30T09:50:01.532Z", "0.2.1": "2015-10-22T13:13:58.153Z", "0.3.0": "2015-11-28T12:37:27.893Z", "0.4.0": "2016-04-07T08:46:59.982Z", "0.4.1": "2016-05-01T19:07:46.040Z", "0.4.2": "2016-07-18T09:43:12.562Z", "1.0.0": "2017-06-12T07:18:30.595Z", "1.0.1": "2021-04-06T07:41:35.956Z", "1.0.2": "2021-04-06T12:51:09.276Z", "2.0.0": "2021-04-06T12:53:21.623Z", "3.0.0": "2023-10-05T08:11:19.087Z", "3.0.1": "2023-10-07T13:34:03.685Z"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "repository": {"type": "git", "url": "git://github.com/juliangruber/balanced-match.git"}, "homepage": "https://github.com/juliangruber/balanced-match", "keywords": ["match", "regexp", "test", "balanced", "parse"], "bugs": {"url": "https://github.com/juliangruber/balanced-match/issues"}, "license": "MIT", "readmeFilename": "README.md", "users": {"dantman": true, "klap-webdevelopment": true, "scottfreecode": true, "arteffeckt": true, "puranjayjain": true, "flumpus-dev": true}}