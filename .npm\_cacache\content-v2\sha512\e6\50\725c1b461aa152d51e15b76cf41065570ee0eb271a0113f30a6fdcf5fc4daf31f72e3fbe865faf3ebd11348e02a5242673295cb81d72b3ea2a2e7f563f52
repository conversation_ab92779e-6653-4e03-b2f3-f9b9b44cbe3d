{"_id": "@sinonjs/fake-timers", "_rev": "57-81ccc6da4fdeaafbe587e01724cbb9a1", "name": "@sinonjs/fake-timers", "dist-tags": {"latest": "14.0.0"}, "versions": {"6.0.0": {"name": "@sinonjs/fake-timers", "version": "6.0.0", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@6.0.0", "maintainers": [{"name": "peer5", "email": "<EMAIL>"}, {"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}], "homepage": "http://github.com/sinonjs/fake-timers", "bugs": {"url": "http://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "b64b0faadfdd01a6dcf0c4dcdb78438d86fa7dbf", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-6.0.0.tgz", "fileCount": 6, "integrity": "sha512-atR1J/jRXvQAb47gfzSK8zavXy7BcpnYq21ALon0U99etu99vsir0trzIO3wpeLtW+LLVY6X7EkfVTbjGSH8Ww==", "signatures": [{"sig": "MEUCIHVZcfQb5epOO0zZFyqVMjAZMj38BL9hVLTHE4GGeKOFAiEAlaq48pxvXJzydoHBnU4lqBUkl0sM4ZyD3pfOxL7xZAQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeOViACRA9TVsSAnZWagAAHpQP/14fVkwsL7Q81GZRUXvs\nuGaHnaFprQ/hCT82KFJC2uE2bi8kqsiQFVBvWcLsiQAoihiJluULaeQkkzAV\ngEGsbsqeGsVUINiIja8RJoxrIzZ5VMqtEi+ALomyFgxB3wobjXw1rqjgv0JA\nYpVILDdwGJuVEBDYqGAPQCIKEN6bzK14wPzclGuv8oSEdqk6cueHCzZ6RYFf\n8UI6GtiR+ndVklUZSIJP6KWbHbL496wo2rnnzmwt4/9NgR9vHSjQxaeQseuL\nSBN8iffCYvYlguqTdWQrJGLe4V1n6OsCSvnhQo8tKos4dqh4mJvbp3M5HXS4\ncw8th36Xw6TUghyKwEO6SBNHX/cVsBCOYUGlZHK2VOki2qgyeRbYfLnGL72T\nmiMZXDcSrELb9kG2yAdKmDIRPAj4Sm8gpmWQvyvsPsHCKQHd079yp3nSjEsd\nX4Ft0F8GISGDdX8qhrUToMQ+dTBlrs+c0USsvqCqp8RtmnPgMO+I8ga7SPKT\nvJZfPxs6Gb9yojp7lw9qR+tXpZdY5Kij0jhmLOs+VAkzrno7Bgp9qyJ89OJz\n8NuPTd70yCDBIAAdDDUKjy0u9beVNPvVfEVPgQvB12OCt6mIe2NP+tyfmQP+\nJHOPXpO8nEZJ25rjsOB25Z0Mf1BEHTF6Ex1GHriE/L5i6Z/bav0lE5p1yjYM\nuDBb\r\n=D3qW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/fake-timers-src.js", "husky": {"hooks": {"pre-commit": "run-p lint test-node"}}, "module": "./fake-timers.js", "gitHead": "684a3ad408075c68debd70d42f3d011810a82a5f", "scripts": {"lint": "eslint .", "test": "npm run lint && npm run test-node && npm run test-headless", "bundle": "browserify --no-detect-globals -s FakeTimers -o fake-timers.js src/fake-timers-src.js", "version": "./scripts/version.sh", "test-node": "mocha test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prepublishOnly": "npm run bundle", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint"}, "_nodeVersion": "12.13.1", "dependencies": {"@sinonjs/commons": "^1.7.0"}, "eslintConfig": {"rules": {"ie11/no-loop-func": ["warn"], "ie11/no-for-in-const": ["error"], "ie11/no-collection-args": ["error"], "ie11/no-weak-collections": ["error"]}, "extends": "eslint-config-sinon", "plugins": ["ie11"]}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "husky": "3.0.8", "jsdom": "15.1.1", "mocha": "6.2.1", "eslint": "6.5.1", "mochify": "6.6.0", "prettier": "1.18.2", "browserify": "16.5.0", "lint-staged": "9.4.1", "npm-run-all": "4.1.5", "eslint-plugin-ie11": "1.0.0", "eslint-config-sinon": "3.0.1", "eslint-plugin-mocha": "6.1.1", "@sinonjs/referee-sinon": "5.0.0", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_6.0.0_1580816511416_0.7594819586846902", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "@sinonjs/fake-timers", "version": "6.0.1", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@6.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "peer5", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://github.com/sinonjs/fake-timers", "bugs": {"url": "http://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "293674fccb3262ac782c7aadfdeca86b10c75c40", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-6.0.1.tgz", "fileCount": 6, "integrity": "sha512-MZPUxrmFubI36XS1DI3qmI0YdN1gks62JtFZvxR67ljjSNCeK6U08Zx4msEWOXuofgqUt6zPHSi1H9fbjR/NRA==", "signatures": [{"sig": "MEUCIDRL7ILfU3jpjZDpT4z1KUxeDI9f0C+PPOuU3XiJLs9sAiEA0r1OvijtY5wWZgh4LU4DEIkOh0oHuAZQLxSXqdAwERU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 155690, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeefJsCRA9TVsSAnZWagAAc8sQAItdPztOvrarsFoFB3FJ\nEtuShn+jS8PghYXrxyARgx8Z5WQYRrogV9FtuDYEteq2AhQtn1vV3bvOOOVa\nWC0X46elOsXFOYlbFsZUsXWBHEKlHUtR1ygixbtIoGhaljZbBc5/SF8eZrmh\nowOKiyGH5RVzbU/9xgWfJsZ4h+5S2NiDH2/JyKnwgweuAnznHn2N6qBk7CiE\nOU0IM1e/HlNff1j5sU3NzQX7r9aBmYyUrUPXypn/iIxyGpo6SPBZBrbzjcpn\nwB0Q8BjrDG5A/BkciAeOblsOJC3rIZ8ubtgWKwaOqBV1VlA1+6VZcjGtwJFv\no2JXGx0BQ+/DADFhrnE5LfKqX5/OfaeIcmQNqMqbs+czVGejtumncqyYi4Tg\neUfznCSwEupMP0GVScyXxo+r2Yrnq4K4K1GD9kWYSS+QEgMjXQqLk1NZpKVl\nFxBWjbkf8ag4GIAcjpe593CVbS4LzUd2atzbvDqLHYbY0qDC3hGcb4DSyfCc\nDb4FynO7POWDB4sqCvn6kBPZvPztjV7zv3sQKw4pZX3ZiW6W2YUrpSEg5B0Y\nBQl0/XGVR/JnEaG2gJ83H5AwKAfWuEHKIoWYDt26xYiDHZlAnkJJ/MADsb41\nNBHrlWx9eSYlk+OY+vGDMFWmuOK3dZ6lUDbkgPutQSlUWFB/9dFRUbm6/wJB\nsL0y\r\n=bOTZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/fake-timers-src.js", "husky": {"hooks": {"pre-commit": "run-p lint test-node"}}, "module": "./fake-timers.js", "gitHead": "480f68f94b7f6b4b321168026014dc77e147ad4a", "scripts": {"lint": "eslint .", "test": "npm run lint && npm run test-node && npm run test-headless", "bundle": "browserify --no-detect-globals -s FakeTimers -o fake-timers.js src/fake-timers-src.js", "version": "./scripts/version.sh", "test-node": "mocha test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prepublishOnly": "npm run bundle", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "6.14.2", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint"}, "_nodeVersion": "12.16.1", "dependencies": {"@sinonjs/commons": "^1.7.0"}, "eslintConfig": {"rules": {"ie11/no-loop-func": ["warn"], "ie11/no-for-in-const": ["error"], "ie11/no-collection-args": ["error"], "ie11/no-weak-collections": ["error"]}, "extends": "eslint-config-sinon", "plugins": ["ie11"]}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "husky": "4.2.1", "jsdom": "15.1.1", "mocha": "7.0.1", "eslint": "6.8.0", "mochify": "6.6.0", "prettier": "1.19.1", "browserify": "16.5.0", "lint-staged": "10.0.7", "npm-run-all": "4.1.5", "eslint-plugin-ie11": "1.0.0", "eslint-config-sinon": "3.0.1", "eslint-plugin-mocha": "6.2.2", "@sinonjs/referee-sinon": "6.0.1", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_6.0.1_1585050219557_0.7603783446258647", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@sinonjs/fake-timers", "version": "7.0.0", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@7.0.0", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}], "homepage": "http://github.com/sinonjs/fake-timers", "bugs": {"url": "http://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "0e52d092d8cf38ae38afca66e0758fda209cb0a4", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-z+0YKn3lGU6u68nICTl8AnKmx6ANzqARqDusNgBHXFCqG8tMp/4TYbRNjyghpwM7X3fq/hNEK8uEIqfC6IQb/A==", "signatures": [{"sig": "MEYCIQC3r7V6n1wbISzkDoPda4XN0/zU0iLzBZQopoSy8yjxwwIhAPIW+s9jK3uNHPBsLm3qpRwp/6gVDs1WEgCFadOeYP9z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74049, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/eGbCRA9TVsSAnZWagAAZMoP/03BN4EDb1pwoTcWtgnm\nwR9+JrZY2A0sZSd/nR+cWX1jKT6uZXFqfPvQsxjuICRX8jjgOHMeX2ip8AbW\namsqyjEBNsUdymT2qm3V1i3Qs3bbfEDh70vy7hSxGt2xLD34Sd1G3m+w2EQW\nDRndfL4dAl2Bh4qk90j+a0ny+c+4h09Cx0ONAUv81zWT7w8ku6T2v8P7yR/T\nXzCO7AEghqAzKcJdmMt94acyfNHum6BZOuJXo1tHFpEhm6iVXGDJ+JByUIwx\nSTLoipH8qKuKum8hkN14LvhmNFFM8eQhglJ2Yj5XU3HqLjEorEW0p2DiFJVy\nM6O2SzXeQr/jQ4YFlD4qdwzFzauc7hKbEYvzPOd9JaJvLXMWA+OuEP/HGizT\n0H66FT4Lkge7A+Yua13CexgIljOJQHirjV+rL1hDqyMu3X22h0GSGZ/KC/df\nS6VznIp/7msk0MZSycJvCkcVjyYqn3ziSbqhLFsydI+vUN8ineeqIswKWNSy\ntRBg/f0P5RISnpcN77yeY679qujolx7ksaHEGV80Wi7JzT+3ibWrxbCO5feK\nphP+bGGGEyodzYQ6+KJXsNXcXkoLRXAeFCOD8XlzKU9Pm62J/p0zE+VuikK+\np5xXiiHPsde8WynG3vqfM5p2TepXLmE+i1SEfIEJdvUGQDXXia/zx7mdeQyd\nKbFE\r\n=q2sS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/fake-timers-src.js", "husky": {"hooks": {"pre-commit": "npm run lint && npm run test-node"}}, "types": "./types/fake-timers-src.d.ts", "gitHead": "7bb6e784f72e7661a12472f9a7413d22d55d7ba4", "scripts": {"lint": "eslint .", "test": "npm run lint && npm run test-node && npm run test-headless", "build": "rm -rf types && tsc", "version": "./scripts/version.sh", "test-node": "mocha test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prepublishOnly": "npm run build", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint"}, "_nodeVersion": "14.15.0", "dependencies": {"@sinonjs/commons": "^1.7.0"}, "eslintConfig": {"rules": {"ie11/no-loop-func": ["warn"], "ie11/no-for-in-const": ["error"], "ie11/no-collection-args": ["error"], "ie11/no-weak-collections": ["error"]}, "extends": "eslint-config-sinon", "plugins": ["ie11"]}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "husky": "4.2.1", "jsdom": "15.1.1", "mocha": "7.0.1", "eslint": "6.8.0", "mochify": "6.6.0", "prettier": "1.19.1", "typescript": "4.1.3", "lint-staged": "10.0.7", "eslint-plugin-ie11": "1.0.0", "eslint-config-sinon": "3.0.1", "eslint-plugin-mocha": "6.2.2", "@sinonjs/referee-sinon": "6.0.1", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_7.0.0_1610473883389_0.9139888142346992", "host": "s3://npm-registry-packages"}}, "7.0.1": {"name": "@sinonjs/fake-timers", "version": "7.0.1", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@7.0.1", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://github.com/sinonjs/fake-timers", "bugs": {"url": "http://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "3d6a1a07584637f77827d61ebebfa50c2b7fa991", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-7.0.1.tgz", "fileCount": 6, "integrity": "sha512-EUWub7DGK3if4F8S1Gx7QjodtU8rV6267Oi40APc4jLDIEUiXAL1sIfvsAPMEWgFwz41RsyA4NpSp/IaJ0y/Gg==", "signatures": [{"sig": "MEUCIHvwWSEQM65SBmuQZAzGPpvaZi3ke4J5V+p/O/OEXm6KAiEA03iDupevEyiNeVNOrpXZ/6Sijfn2KcxQ794NhmU0mzM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73518, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgACmXCRA9TVsSAnZWagAAoX4QAIRqFkg/oYcvTj1wWI0X\nXl2x6g4NUwoP7WjxgQ8riBU6BdzxTH9iXQjU1Oj8VSsL86szQyMMDpfN5uAI\n76nK3uLyh0CAZAHBh/zx7Idg0RJh52+4e+wtr3ofUJ4/uD0riyEU/cuy/zuM\nvy0bY4Sn/TEy0ah6mAfUgDzGjvib2WiyMMGDp3Rlae6vCPs2ybnPL1RV30ZR\nyhSjaivw3fcykPZFoBXmeOGrX4ixm2dZMSUm6A75QpLQLnqdNmfiBP9WCi/F\n1BaG4Kghbf/2KSbu34WHNcCZUtbm++IMfMDRfBLfjSwDyT3JrSz+gYfdCq7O\nrAcym5LfzalJAc4P2FRjBYqsWN+Y5SMPZFiDMVQTn/TeGmddVWnSwvY/cpSh\nJuuz76HPDFbLOZjCUcXq0p3bUMK+ZF38LZa8M1oZJYEyCoXYCV9jHf3b4NO+\n+3N1WkEyPwb8Q1X58UE6s0kwftGLR32uEioNHANgCaLMo65Cy5NQiFhWtY00\niYzqSItl7oTxFiJ9LzfEN8wH/zS3i/0No9IVw04iSJtwGETZXEm88wPLHGNB\n52lCIDT9+b//Q6CL88kf6A7dLKejDqbvG/1Ya4ydiSuN5dsYSacfpGQ1WPgf\nosJ6l9UwhfBsh9j4r7Y//BwHNZifJIdedwyPIWvAZiG/ubi60eUdwm4Qnicc\nUkBt\r\n=10OL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/fake-timers-src.js", "husky": {"hooks": {"pre-commit": "npm run lint && npm run test-node"}}, "types": "./types/fake-timers-src.d.ts", "gitHead": "f3c2cc0d9f4958782f1eb22be59d0b3e575fe803", "scripts": {"lint": "eslint .", "test": "npm run lint && npm run test-node && npm run test-headless", "build": "rm -rf types && tsc", "version": "./scripts/version.sh", "test-node": "mocha test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prepublishOnly": "npm run build", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint"}, "_nodeVersion": "14.15.0", "dependencies": {"@sinonjs/commons": "^1.7.0"}, "eslintConfig": {"rules": {"ie11/no-loop-func": ["warn"], "ie11/no-for-in-const": ["error"], "ie11/no-collection-args": ["error"], "ie11/no-weak-collections": ["error"]}, "extends": "eslint-config-sinon", "plugins": ["ie11"]}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "husky": "4.2.1", "jsdom": "15.1.1", "mocha": "7.0.1", "eslint": "6.8.0", "mochify": "6.6.0", "prettier": "1.19.1", "typescript": "4.1.3", "lint-staged": "10.0.7", "eslint-plugin-ie11": "1.0.0", "eslint-config-sinon": "3.0.1", "eslint-plugin-mocha": "6.2.2", "@sinonjs/referee-sinon": "6.0.1", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_7.0.1_1610623382512_0.8251708155862472", "host": "s3://npm-registry-packages"}}, "7.0.2": {"name": "@sinonjs/fake-timers", "version": "7.0.2", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@7.0.2", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "http://github.com/sinonjs/fake-timers", "bugs": {"url": "http://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "a53e71d4154ee704ea9b36a6d0b0780e246fadd1", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-7.0.2.tgz", "fileCount": 6, "integrity": "sha512-dF84L5YC90gIOegPDCYymPIsDmwMWWSh7BwfDXQYePi8lVIEp7IZ1UVGkME8FjXOsDPxan12x4aaK+Lo6wVh9A==", "signatures": [{"sig": "MEUCIQCdQstDOQSXEavziluaJAgg3e+SXAWynhOhsfUUBSY2FAIgJv4XG3Jb6HDOuXPVsVoNTQdrMyV3pcBebwQD0lPEMrc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75158, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgBbOwCRA9TVsSAnZWagAABfIP/iPyb4YTUN5WHz8hh3JH\nLeQvIcT2slZRpNJdiBbM6BcIb3OZ+nlo0MXEnxr8Tzt9vNb7ByRg90LpXqvv\nbasNdtDjzi0tENeNr+JBVw2T/ipax2dfg2PnSN7U+oOsuKTZBfJF6e5T9Pz9\neqoin5SG2eUADvb1Gj2u5Z3yAaOTgFtr5tn0pFshsExKaiy9ejdxlVKMywkq\nIt6ElkVgWP8433y+3W3a52TlSEAkrITMiMn/BVNnY0qFP59o0LQDRC1G1NVJ\n/KpDsntvx1OTWY/l4h+fxUd5j5nCTPJxWZhVaRqT6Kgi9hctWAlc2Qo47KTQ\nEmET5JmoQGVC8uRo7KuT6AtOXaeG2dP8LZ/cHYYt87GiFHZXu0TW2Xt9xNcq\nbfrYk6k5SYi8dQnNfvy82dyou9WS0guLXJADbvWcJjOOTZmEZcAkDze1lxFO\nXENrqVcvIAuBigr32aaVY5mmTKWDRsoiJnX04F8LuQW7xz6iXjAD0jiUcHu1\nxMHmM3cOdP0nuGLTAskLAvwz0snGud4ljlnJEhmpL6M2NjTj6uNHlrl0u6NC\nT0INePbEcUF2OSmbbnxjL9K1pjZhobRkrjFIS/VROtPoWqgMgSeFvMvrGgG0\nVte3JnVChPQMGnnNr/q8tdWK7TzyHAbs1s1QnSGqMU/1Hhd3awALK9d0Y9f7\nLDWC\r\n=digd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/fake-timers-src.js", "husky": {"hooks": {"pre-commit": "npm run lint"}}, "types": "./types/fake-timers-src.d.ts", "gitHead": "7f5f89bd24512c3cf289c47f3612567566adf64a", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "build": "rm -rf types && tsc", "version": "./scripts/version.sh", "test-node": "mocha test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prepublishOnly": "npm run build", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint"}, "_nodeVersion": "14.15.0", "dependencies": {"@sinonjs/commons": "^1.7.0"}, "eslintConfig": {"rules": {"ie11/no-loop-func": ["warn"], "ie11/no-for-in-const": ["error"], "ie11/no-collection-args": ["error"], "ie11/no-weak-collections": ["error"]}, "extends": "eslint-config-sinon", "plugins": ["ie11"]}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "husky": "4.2.1", "jsdom": "15.1.1", "mocha": "7.0.1", "eslint": "6.8.0", "mochify": "6.6.0", "prettier": "1.19.1", "typescript": "4.1.3", "lint-staged": "10.0.7", "eslint-plugin-ie11": "1.0.0", "eslint-config-sinon": "3.0.1", "eslint-plugin-mocha": "6.2.2", "@sinonjs/referee-sinon": "6.0.1", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_7.0.2_1610986415866_0.3591775707327094", "host": "s3://npm-registry-packages"}}, "7.0.4": {"name": "@sinonjs/fake-timers", "version": "7.0.4", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@7.0.4", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "4c95dd62c506824a9c969d231e6174f4d545b07c", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-7.0.4.tgz", "fileCount": 6, "integrity": "sha512-fW3SzjLF0sjI0x1Opc7cUG4J/Nr4U0TXPNnKNAgrxA4xXsQNk6nypZK0yJg5FNw5cCo2yC/ZMdaVhDTKeeF6zg==", "signatures": [{"sig": "MEQCIEagVHWDAG5XcLsfc8lH/YCJhgXTwlBXCTQwvp81z1sfAiBoLsxZCHVrv1vH/W14nAcsK4Voh1yGjyM/AGL5wrDaBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82440, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbrr8CRA9TVsSAnZWagAA3f0P/2RNXNjfCJKqJUwKtooR\nnkvLSrye+jvX8vckiBS8IfCZZLn8/sRm+nt1a+EUIKndAyOko2zc6RQwuXfQ\nMR50ytvY8Ocoolp2xw4//xveryEWxriXIpDUA8HfWpw12zo3+0PTz/sL5Y90\nv1IRDiw20CwWCDYFuuYlsVF6xTzTYRYcpBL1DmuFhZUqURkQFmQgUoT8JMC7\nkaFkizOAmA0+nyzYuhlVBlgHhzE+fbUThPbdJXinxZaxD8Lo5LJmAZ9qiDGh\ncfNfjYX0GtOyasES3rmKlvmOzKZXVhBYBufibOiRKTuHUkhPuT0FyXqz5VX3\nzJyym9R+wqDipKvVVUVV7sAcMXuGEtoKGqRrtf4Kx2qWzYYvNgndQ6qq9vVb\nn3Q3b4LUVHICs6txpLcdLpKv7Qz6Xmh1ys3++lVA2IbHqViCB3SUqpxsqAIc\nj1sUdohQzGh8AVpQNLvDHuxJ7ynWylg1SJarXkktO6OJV4mIBw8frJ1NPGcw\nHGryn1VsANcqzrXQWVRs6whBtBHAhAq4rsvfF6rSnYc0/FXOODC1OSPeYb6x\nLGJ9gbIuHVsV4hFPHuAjAN1DvJSI7bW3U7xlgvAC1DMYkqdhq/ZGJOHKss+2\njGQzCLUJT6LqHvKOPnG7x/18NjxNCBN09z6kaWrp6c4WPncRVehE28K/oPtm\nzvdY\r\n=MJyn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/fake-timers-src.js", "husky": {"hooks": {"pre-commit": "npm run lint"}}, "types": "./types/fake-timers-src.d.ts", "gitHead": "54852f6d270295f164e7c1d8c217955523e22cfb", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "build": "rm -rf types && tsc", "version": "./scripts/version.sh", "test-node": "mocha test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prepublishOnly": "npm run build", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "14.15.0", "dependencies": {"@sinonjs/commons": "^1.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "husky": "4.2.1", "jsdom": "15.1.1", "mocha": "7.0.1", "eslint": "6.8.0", "mochify": "6.6.0", "prettier": "2.2.1", "typescript": "4.1.3", "lint-staged": "10.0.7", "eslint-plugin-ie11": "1.0.0", "eslint-config-sinon": "3.0.1", "eslint-plugin-mocha": "6.2.2", "@sinonjs/referee-sinon": "6.0.1", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_7.0.4_1617869564363_0.42730588435222017", "host": "s3://npm-registry-packages"}}, "7.0.5": {"name": "@sinonjs/fake-timers", "version": "7.0.5", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@7.0.5", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "558a7f8145a01366c44b3dcbdd7172c05c461564", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-7.0.5.tgz", "fileCount": 6, "integrity": "sha512-fUt6b15bjV/VW93UP5opNXJxdwZSbK1EdiwnhN7XrQrcpaOhMJpZ/CjwFpM3THpxwA+YviBUJKSuEqKlCK5alw==", "signatures": [{"sig": "MEUCIDx/oPlK9BcUOq+fxXWEje1nz3697NHrLHzCuyS+4i4sAiEA30WPkJsEVrjQcUTgWZfFcWHs6kB0/R1VTv7bper+1xE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82698, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgctb1CRA9TVsSAnZWagAAlFUQAIPBqmA9FpE4udCvh5Js\n+3DQQvs4SOg1iuJr2u6/JwUITe6HyuJ6SBwUBXUKH58MU6YrusxQeRwiHZUw\nksgQnBhSf3TDHwJInbULss48+SuzqwQEDofOEGxCo/w+4NeNGb0L5QCPbvij\n7CTbypIM7AiBVR4iXmNtVUNxExm7onIkPzFbassqLALOuLsOeS/U+FlB3tsi\n+6eWmYw1uE5DPACWrIo+8ZtsleKz6Hgzu9MZpH7TDBQV92ws0kR1/EAMVm4X\noItakNaVZMNV8zRvFEsvZpbQk7EtUHntfPdo2CBfOdf0vGzjZGX/NedI6Nbv\nWmqdiWxoJDPSBpN5MQg0YwCnY/KZhTS8DO2PvHKx1Hddl+KGnPHAncrxiwU5\npR95nBHSzoirzqYlIqYlKd+MSSicjRGgRatnByN2Vz5fjSkk4yPPQVaAHzqx\neMoScnucZB8DF36MTm0wVFBdyKs9LRf6y4A6YS/bKMpTjDq33jVzR84f2qta\nDFjN1w587+lILomWEzOrZ2ErM8h9RcfjdgD/FwiHw6riygzBy14VgR+0StnE\nTphgJJWeZ53ZidYkyAvfUfelACHHndfZ5ku9rAMx7EI7PGW2RXRYs4fBNKDe\nj1PvWi56OXxr7egcy9W0J4E9dc2cDIYp64dd2ve4giZza2pqjBOdFATji1kD\nTnMs\r\n=tW7W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/fake-timers-src.js", "husky": {"hooks": {"pre-commit": "npm run lint"}}, "types": "./types/fake-timers-src.d.ts", "gitHead": "de1e67f539f63da2b6a4bcee26ad0f509c5b4023", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "build": "rm -rf types && tsc", "version": "./scripts/version.sh", "test-node": "mocha test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prepublishOnly": "npm run build", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "14.15.0", "dependencies": {"@sinonjs/commons": "^1.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "husky": "4.2.1", "jsdom": "15.1.1", "mocha": "7.0.1", "eslint": "6.8.0", "mochify": "6.6.0", "prettier": "2.2.1", "typescript": "4.1.3", "lint-staged": "10.0.7", "eslint-plugin-ie11": "1.0.0", "eslint-config-sinon": "3.0.1", "eslint-plugin-mocha": "6.2.2", "@sinonjs/referee-sinon": "6.0.1", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_7.0.5_1618138869268_0.051048247704378547", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "@sinonjs/fake-timers", "version": "7.1.0", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@7.1.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "tsd": {"directory": "test"}, "dist": {"shasum": "8f13af27d842cbf51ad4502e05562fe9391d084e", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-7.1.0.tgz", "fileCount": 6, "integrity": "sha512-hAEzXi6Wbvlb67NnGMGSNOeAflLVnMa4yliPU/ty1qjgW/vAletH15/v/esJwASSIA0YlIyjnloenFbEZc9q9A==", "signatures": [{"sig": "MEQCIBeo/vQSe5RmZ3mClOvLPFaKivnq0b/t43uv5hQhVKkrAiAp8rE92q6owZygBm8bRpCQBwKLhSj9G2hcqNgsdoXuYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86343, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgp7TQCRA9TVsSAnZWagAAAFQP/3zuCPUax/7j2pICfGJm\nHIYgKrM7yg3enyOdyfBMGiX1MORiMoqSNub6VE7iyIPMA11krH9V/CWX/q5+\nB5Py/vLxpcnCpyr2BBLn/aPH6/nxgEBfDuZGSXwhMgZhbEAsD2Wu4v8aOgPE\nqYbcBRyfmjupnV/1UZnX6fC+gSyKAOCOW8EKbA7y68f82YIHAICXHTV4IWuD\ntCqox7tPn6I4GCDw+jlVdYVlVNxUFZHcHBuxFwrpWeKMNT2kyKXyDtd4FPuZ\nsNvxZ0fTLwtNakXxmKD10shv4hNRU2xoh7DTeym5OoT0MC7yY6FnE2AmIbht\nm1Ul1fOhTKBGC/C/GbeB7kiZ0tgH6O+htaCH26IV3jej303YcyN6JUs8T6HN\nSCoqhuw2X13T5KjKZSXk+6zUTywM5mu0fAkYJv3lL8vlHlbi7l9QrSb1xZ+m\n5z+jLTYn3tyL3lU2US9r8bAKMFF/kqtWLojXhoxrua7rbxhzZL8vD37MtLn8\n9vNo81+WAwbYgb9hOkd1dEWjBbfxzYahmZu9Fqpx3mrikLCIZWWsJv3M8tMO\nzjTUxE1NekyNqv5zOZY6RKG3uDVubTtq7qWE2LdPijRzvSPtj9BLyyugHCtJ\nv2tc3BKI2XZH4x7wb8I+VcVLYCFO/4NgOF/xpWL681MRVdP+8aQchPNzJDPZ\nv/Nb\r\n=yS8v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/fake-timers-src.js", "husky": {"hooks": {"pre-commit": "npm run lint"}}, "types": "./types/fake-timers-src.d.ts", "gitHead": "f5d9b3adf1d51e04969d3519f6c9a300f41ee1f2", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "build": "rm -rf types && tsc", "version": "./scripts/version.sh", "test-node": "mocha test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "test-types": "tsd", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prepublishOnly": "npm run build", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "14.16.1", "dependencies": {"@sinonjs/commons": "^1.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "tsd": "0.14.0", "husky": "4.2.1", "jsdom": "16.5.2", "mocha": "8.3.2", "eslint": "6.8.0", "mochify": "7.0.0", "prettier": "2.2.1", "typescript": "4.1.3", "lint-staged": "10.0.7", "eslint-plugin-ie11": "1.0.0", "eslint-config-sinon": "3.0.1", "eslint-plugin-mocha": "6.2.2", "@sinonjs/referee-sinon": "6.0.1", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_7.1.0_1621603535918_0.610649492525795", "host": "s3://npm-registry-packages"}}, "7.1.1": {"name": "@sinonjs/fake-timers", "version": "7.1.1", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@7.1.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "tsd": {"directory": "test"}, "dist": {"shasum": "7a6ac09ed4c3fe1854a2002e08db15be6c8570b8", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-7.1.1.tgz", "fileCount": 6, "integrity": "sha512-am34LJf0N2nON/PT9G7pauA+xjcwX9P6x31m4hBgfUeSXYRZBRv/R6EcdWs8iV4XJjPO++NTsrj7ua/cN2s6ZA==", "signatures": [{"sig": "MEUCIQDnjauBzcAsdYkZ/YO9wcZIWpWGm37JraRDD4dBbIMyJQIgO6WOiyEUGZMH5/7obb/3zx8HCO6Wt3MzdsZoJCnIoPo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86429, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgr4cOCRA9TVsSAnZWagAAlD8P/3G6sCTaoG6hny/TO/Or\nz6UAMHoAqeK4iXmGRPKliS+uN6zZE6f7u8v4sOVaaHHYpi1Kic+nvC2J+YJd\nJ7L3SzfMUT0WBXgd1V/nDupnX+fXEmrjFQ8JUZJB6Tlp9iSxR/MOZLUgEYII\nUg2Q8xf8ESDTHvpy33NULKITRrH9oQz8PkrYI0Z8fRp9PT/jBhkurasxhu4N\nWQbque8B7/CrNLfMOqXvnV7J3zOpdtiar/2mHBQU6cx8x6SMug5CO6d14Uqi\nbFuC0lCr5b2re1Qq1CnM8EntD/HPJUkWaOUZ0/VVbUmtdyaB58pQZ7whyikN\noaV8PiQ7Q0eMZIWN+dg4EtffPFHCrbNK6i/nsUImCMvYyFzjm/gZApoPBFFU\nab0aJsZZfIAhS95v9shsz5tBa1W9sDdHlh00Y4rq8Uujy/KwnnKYyZsDhoqY\n5rhe0n9JOMUfM8v3biTFl974uk+lru+UCHmfQl9XAUlmMZ0V8iK3hBpvbcPb\no2w2WL6xrVmJypsAiGhqY6S3fEQIdTcU3uQgH0MHwBTeGRMLCKLZbk9Fvtb0\nOzi3X4WYSo7CWNSEF3cWfjwvVOEbkbDZiF8Bv+Bjc7cskvt7c/Ml8eLCaXmU\nNpWAs+EFmEaOk2byYXHTsEmrsZZbZgFTa8BiIsZZa5XCHULodS0Ue+SWjKqw\n2oL9\r\n=MIGv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/fake-timers-src.js", "husky": {"hooks": {"pre-commit": "npm run lint"}}, "types": "./types/fake-timers-src.d.ts", "gitHead": "8c1c1857ca890d026a7c0b62a0cf9a6d44464772", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "build": "rm -rf types && tsc", "version": "./scripts/version.sh", "test-node": "mocha test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "test-types": "tsd", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prepublishOnly": "npm run build", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "12.20.2", "dependencies": {"@sinonjs/commons": "^1.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "tsd": "0.14.0", "husky": "4.2.1", "jsdom": "16.5.2", "mocha": "8.3.2", "eslint": "6.8.0", "mochify": "7.0.0", "prettier": "2.2.1", "typescript": "4.1.3", "lint-staged": "10.0.7", "eslint-plugin-ie11": "1.0.0", "eslint-config-sinon": "3.0.1", "eslint-plugin-mocha": "6.2.2", "@sinonjs/referee-sinon": "6.0.1", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_7.1.1_1622116109470_0.05732100053791611", "host": "s3://npm-registry-packages"}}, "7.1.2": {"name": "@sinonjs/fake-timers", "version": "7.1.2", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@7.1.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "tsd": {"directory": "test"}, "dist": {"shasum": "2524eae70c4910edccf99b2f4e6efc5894aff7b5", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-7.1.2.tgz", "fileCount": 6, "integrity": "sha512-iQADsW4LBMISqZ6Ci1dupJL9pprqwcVFTcOsEmQOEhW+KLCVn/Y4Jrvg2k19fIHCp+iFprriYPTdRcQR8NbUPg==", "signatures": [{"sig": "MEUCIBWvat59qIz1zm0WdWGnB/JIs/m+mbyup09ZSgFAv6pEAiEAvV2NpaGehyceNbyVTOsejIbz4XjxDaSL6iKsWw9TrFk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86561, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsP8fCRA9TVsSAnZWagAAR7MQAKLZy28Km9iabJCySReC\nJ+ZEGAFXI45ayluXuqV0Ss9vESfC8wzsfCkUlQu2EX9OGb2IfV7qFhvTvxVG\nyOoAuDcz4gAogF1FbU9VFK8UQ3BDhCW8c0df7xjrtdl7WrZPz48KvCjdugq0\nuyO3wgiXjZDNeX7AD5huDYetfsNKdGkvRC0f/tsd5bLPPz63ZmNf1FKrzJcB\n//V1ER5z8bJKxCeP/NSGy97tfyA8eKhxOWcOd/xWsk/R1+hV+ElJi83LJzyX\nJ+aw3CzxEC7IyoAOl0RvT7ek1XpjB+6anGflO7sXyrNjeWGzSGQtXYGqUmhw\nANojBhrtYL+GB0zviemmalAoy1/QkhVIaHoLLk8cNNy/d55CkQcLmUhP5Wwb\n680EZWcm1E8YfYSHN8LMpBnWP0oQjhW6+U7yeUIMPksdNQgkxEBtfk3EfoyT\nVs0m2Mt/CibQBpfcx9RE7c+kDB2BN+yykpO/0/hDMpeIg7zJhlJSaSdSRRy5\n1adVWe4EI4/xt1o17R2tFZjegqbMOn0u6Ew4nVtyIxJ1W8NIO2d4//raGmZ0\nXrun53tpDol+uHB5vbVtzrxLj9S/8kBIdwl69J09J0uAGJ4XJzqJ5Y/cVzJ5\ngwV67w/qKbSkgztdX8SxukiXUMO57oQ/BNGV4mtNDDDNnRLoRT68JqiZj4QO\nP2Gd\r\n=M2ne\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/fake-timers-src.js", "husky": {"hooks": {"pre-commit": "npm run lint"}}, "types": "./types/fake-timers-src.d.ts", "gitHead": "ec67ab3663a4a49ce9173ea5f5ab58dec7e2ee1a", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "build": "rm -rf types && tsc", "version": "./scripts/version.sh", "test-node": "mocha test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "test-types": "tsd && tsc --noEmit test/check-types.ts", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prepublishOnly": "npm run build", "prettier:check": "prettier --check '**/*.{js,css,md,ts}'", "prettier:write": "prettier --write '**/*.{js,css,md,ts}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "12.20.2", "dependencies": {"@sinonjs/commons": "^1.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "tsd": "0.14.0", "husky": "4.2.1", "jsdom": "16.5.2", "mocha": "8.3.2", "eslint": "6.8.0", "mochify": "7.0.0", "prettier": "2.2.1", "typescript": "4.1.3", "lint-staged": "10.0.7", "eslint-plugin-ie11": "1.0.0", "eslint-config-sinon": "3.0.1", "eslint-plugin-mocha": "6.2.2", "@sinonjs/referee-sinon": "6.0.1", "eslint-config-prettier": "6.10.0", "eslint-plugin-prettier": "3.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_7.1.2_1622212383407_0.11539544550116787", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "@sinonjs/fake-timers", "version": "8.0.0", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@8.0.0", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "6bb7313d33bc046cd46f72e823fb25df798130d8", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-8.0.0.tgz", "fileCount": 5, "integrity": "sha512-tkG1KcBncpi0CBGwKn/KUn3hIJ12B9iuAIlpIvAgqFfOcDKMEt1vyybN84IVIsDHCl52SBMUcKCZPDQLEuIG7A==", "signatures": [{"sig": "MEUCIQCAkRdI8u+Gc1QNTae1QEEdr7C9wc1S3DJ5Z6+ClidqnQIgdIFMrn7JJ6Mde+xWArSdSRYpH0dpVovxPkFU0LJ4TJU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85923, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQdMRCRA9TVsSAnZWagAAOXEP/iCIilo0CpQaUvz/FrBn\ni1ODw7Da/b0Lhk8vwFchoPV7ZD/RLvpOHjTmYyyA6j0BGfql3RIaUxVK9zjJ\nzN+B57uUwsCVM7AsXcV0ufM/swJ6OSKoVB7a2+sZjxunJdHBmuWssgsSPY3v\nEfvuLVbHO44vhlAPqcjoqlUdMl6zkiebi01UZtoTN5BRV/QkSjJRXnPNk7gz\no6ag+M/NtoqnhO6gdQ51b8n+uu8wzaAN/EfB1mjCPwxCB15Yw6WbuorMtTgn\nrzHilnDuTOI34K0BFe+o53IfeaPlfszuJZ3Up2SLbQwl5fWFCHzTZer5WcnX\ncXkIYpCexyo7gUBmDJdfWATVQxlwHcA8cSZMownF4v5etop/SdDTm/asjFdD\nN479aeDFmvUpI2FQ4AXjg2AgtJ0WJRhUY43wtXC6cZ0LZbEPRjlTrUrFVpvW\nlGusqILuofpwAN34o4t18QAW71/VHQgLyx2tFM6LVodOdZG5f9mMzl5oZYW/\nYryVBOEwN5Fkt3n7jv21GyRS5TjWcx40zYHmLYo/bLTorB4MF5Arm0YvVTrX\nEH2XClhsOehSiTiWzAUPRKyk7HHm+IxvycJ/cL8dow6Tfzq1RU80Sm9fRTpg\nVCuenSLukd43s+8X3eDOPhIxqcsU7QTc7XSatQGCFrmDpo3JYe7lDXX5YlMq\nivcg\r\n=j1S6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/fake-timers-src.js", "husky": {"hooks": {"pre-commit": "npm run lint"}}, "gitHead": "55f24068d5f9df395a7e5bc0bde304e0701094fb", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "version": "./scripts/version.sh", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "14.17.5", "dependencies": {"@sinonjs/commons": "^1.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "husky": "4.2.1", "jsdom": "16.5.2", "mocha": "8.3.2", "mochify": "7.0.0", "prettier": "2.2.1", "lint-staged": "10.0.7", "@sinonjs/eslint-config": "4.0.2", "@sinonjs/referee-sinon": "6.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_8.0.0_1631703825733_0.610476010194271", "host": "s3://npm-registry-packages"}}, "8.0.1": {"name": "@sinonjs/fake-timers", "version": "8.0.1", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@8.0.1", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "1c1c9a91419f804e59ae8df316a07dd1c3a76b94", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-8.0.1.tgz", "fileCount": 4, "integrity": "sha512-AU7kwFxreVd6OAXcAFlKSmZquiRUU0FvYm44k1Y1QbK7Co4m0aqfGMhjykIeQp/H6rcl+nFmj0zfdUcGVs9Dew==", "signatures": [{"sig": "MEYCIQDbr+k+FUBwoWzi7cGc5NieGZRCCkgSxVAHXMs2H8yYuwIhANXzeM27Jc3QqWH57eVIZjMuUCe4Dt8Bicy+/sAq3p+T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74619}, "main": "./src/fake-timers-src.js", "husky": {"hooks": {"pre-commit": "npm run lint"}}, "gitHead": "93f3f626593f646535d1045fcb517cb6a107253f", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "version": "./scripts/version.sh", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "16.5.0", "dependencies": {"@sinonjs/commons": "^1.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "husky": "4.2.1", "jsdom": "16.5.2", "mocha": "8.3.2", "mochify": "7.0.0", "prettier": "2.2.1", "lint-staged": "10.0.7", "@sinonjs/eslint-config": "4.0.2", "@sinonjs/referee-sinon": "6.0.1", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "3.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_8.0.1_1632752427797_0.3225326090745011", "host": "s3://npm-registry-packages"}}, "8.1.0": {"name": "@sinonjs/fake-timers", "version": "8.1.0", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@8.1.0", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "3fdc2b6cb58935b21bfb8d1625eb1300484316e7", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-8.1.0.tgz", "fileCount": 5, "integrity": "sha512-OAPJUAtgeINhh/TAlUID4QTs53Njm7xzddaVlEs/SXwgtiD1tW22zAB/W1wdqfrpmikgaWQ9Fw6Ws+hsiRm5Vg==", "signatures": [{"sig": "MEUCIQDxJpc1b138VxfGvVb7qmiXy23tcgg6EQzEkshe3VD41wIgWVtVTNy/0BJxkYm6nXr8Lw1f1RKPkT0nkVNtEZSyeks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89082, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2avbCRA9TVsSAnZWagAA2S0QAIAMr+LqE78Cv7YJHZkz\nq4drJtDsknPyAIyM1gCtUFrNI93PZZPMSy0Uwzn2YPsgCRIbRIg+irYEJbPy\nS8Ar6nsEEBlSJqaUOphFOgtJNEd/i5f4W0hsR1oDp4uYdLpq3jc+wPy2PYH9\n7cZLxzx4Q2coDRTRbxccng+nG+Ue8ALzSDaitHIojs25xFha3kneDnHEsQIY\nAoXM7KexgriJZai41/lrAXrJhPLAfqOaSmHV68i257Anaz6hRdgtwHAuecHl\n0jlhxXruoUN1QdjEgCqrMcBeog4DrZFnixyjpLFSlcvkbzGe1e3DF8Giq2i+\n5iqu7nCS4J53xoU4+ZeF3zk+70BpIqa9v00z/NVPi76mdlnDnP4caTJduEtJ\nfnjifRicgzGIeBv1XQSs7YteRwvnD21UjCNySXkBZMYOBHkKUIm/4iGeFWmk\n2gnkLodXmKYbVAYrT2qdZSoP6N/hYWU7J2mMun3gasG6Z2ao7+9sSvB4Dd5y\n6XG9OzPTU0JY8602y8u+g9KILzDHSCGPwHqN4SF1WH8K7vPpIp/0Tlr5MwXY\n1RE8sHyS1aspF/KIQWMEfLHvlt1I7VITxDMIpNBnerFxmW/Qpu5VWrK9PDyP\nTGtsNVlLBSEbiUqp23C8F6LkRjjd2HGX12GYFuHM9F/YwNv413G4TlyGxcnm\n1CDb\r\n=Ne6p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/fake-timers-src.js", "husky": {"hooks": {"pre-commit": "npm run lint"}}, "gitHead": "4e7da5c44207ccee06a85773f4d3cea907476720", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "version": "./scripts/version.sh", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "15.14.0", "dependencies": {"@sinonjs/commons": "^1.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "husky": "4.2.1", "jsdom": "16.5.2", "mocha": "8.3.2", "mochify": "7.0.0", "prettier": "2.2.1", "lint-staged": "10.0.7", "@sinonjs/eslint-config": "4.0.2", "@sinonjs/referee-sinon": "6.0.1", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "3.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_8.1.0_1635949806473_0.28834026639257315", "host": "s3://npm-registry-packages"}}, "9.0.0": {"name": "@sinonjs/fake-timers", "version": "9.0.0", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@9.0.0", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "6854809cd058c6e25b3da2fa063153eaea4ffc72", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-9.0.0.tgz", "fileCount": 4, "integrity": "sha512-+shXA2X7KNP7H7qNbQTJ3SA+NQc0pZDSBrdvFSRwF8sAo/ohw+ZQFD8Moc+gnz51+1eRXtEQBpKWPiQ4jsRC/w==", "signatures": [{"sig": "MEUCIFlwRAIG2/QkLMlA6Dx/psH7jRxdVdIWl/Tl7p2o3JhBAiEAwsMyV850LCzklbWQorXBmPjGhUkEKpwRzSRs68lGz8g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78440, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh898GCRA9TVsSAnZWagAAlKgP/j3pjMJTlglOQcYGhJPb\nY86kxs1+x1g8S+1Fclw+/6YagUPVMCmkCOCHmTIWNeTJ+CFLr6zEa7OXi40r\nqztZhOwfIsxSYPrhTsjQyuOCnBmahkFYLQL6ra5diATffy5DPI8lEFZUblXY\nVbwKB8LXk8SqKX+8k/wsqP6AHRQlUef32EhQs0iNsW/+BlfLJMwnjQY7D1Ky\nZBsWh/jwdYHLxdLm8QLoB3Bt4FABRI92T/iZFVWsmhvGjUvxlN4qxLuWxu17\nITkr0Pz6MwhMe0Vsyt6e6S/c1qU0r+1gadQZsRSZWC/mWa4ob3s/BZ7Rmhne\nM0B67LZQne3XG5ojx/3IDJCpWVogQJZ3F/keZaqDFBwIbDZmUCqM+5d63ag0\nrLM4lNz1o420VpqSuzhN+YNUllJRunE0T1Wv7Bk5SZZFHXOd38aJzHlNqvSZ\n1y17npon3cl2bwBY0dSWNMTT3qjy299WyfoWFUC9b04t78U5uiDzchaEyI52\nCEjTaLC4j5oQ+G+K7ku3L0jMuQhdxk/02mNILgYLrV4xqJQwS6zavkdPCc46\ngzlQUdk7uTxtR31u36KpeT3BzapHAML9S8x2trRXlfp2D7Cwne5+XhQkI8gL\nWCCF3H7HfwuTCfI12VoMoOIiHL+XS+3mqBW5HePPvgWRO0VilyCdeHN9BP5R\nrJkB\r\n=c7yP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/fake-timers-src.js", "husky": {"hooks": {"pre-commit": "npm run lint"}}, "gitHead": "fdc06484e65208e0c05d1c105414a12f4045e1d3", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "version": "./scripts/version.sh", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "8.1.4", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "17.2.0", "dependencies": {"@sinonjs/commons": "^1.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "husky": "4.2.1", "jsdom": "16.5.2", "mocha": "8.3.2", "mochify": "7.0.0", "prettier": "2.2.1", "lint-staged": "10.0.7", "@sinonjs/eslint-config": "4.0.2", "@sinonjs/referee-sinon": "6.0.1", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "3.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_9.0.0_1643372294454_0.831696718080321", "host": "s3://npm-registry-packages"}}, "9.1.0": {"name": "@sinonjs/fake-timers", "version": "9.1.0", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@9.1.0", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "8c92c56f195e0bed4c893ba59c8e3d55831ca0df", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-9.1.0.tgz", "fileCount": 4, "integrity": "sha512-M8vapsv9qQupMdzrVzkn5rb9jG7aUTEPAZdMtME2PuBaefksFZVE2C1g4LBRTkF/k3nRDNbDc5tp5NFC1PEYxA==", "signatures": [{"sig": "MEQCID5Z8wAaYY1h3mQ/KO/G1ShPA8nvG/adasg2LSJKdb0AAiBQNZLRFNHQzgtj/ooeqnU+4SxTvPKmRIa19WWHrsrqpA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78644, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+7zqCRA9TVsSAnZWagAAIOMQAJdd0BpeM07+c1AiZv7F\nCdR2gAt4w3k3zb8/hL0uiwXqppoFBbCDHLUL3Ckiet3a27UK2QduSQL1TtKW\nv/3gybbMPBbYMdpFhvLwehXJCCMdaFe6nYR8+4ztgHC0uj5k8N7UZaasbCZW\nym4EBkAQsjXRY4KpBt8b2RZXXO8BqQ7rV+vIS7gPgUIgyro2gS3fUxcyp5Gy\nJArdBOaSmiKM96xTbi5CkrQgnw9KLBub/t05c9Eu5tIqWjoxXYEF5UA8ps6f\nor1bJlaEERHGKgsUFMQ8If8d3YwAq+kuReREF9pM8MSM5lFbxYUw+1J3cbLC\nlCe2HUP0snkyIX8+mZJJNjs5yZvZvjvs760t/Fa9WJC8GZcX9FS3CVOEhgxQ\nbaeu03pETNeMZmxVV3o2ZitNgkYemUXiB12OnyMiReXGM2XWJl8KUP15TYWh\nxKLFCX4SQ7EvZj1oy5mR8/2lqQNQEPSWd4O1yXYeOyeQhKJ06Drf2eq7vkBr\nYb8RH6BTkiQZd3RCT55nc2c6vro8f8Kw48mmBCRyhVM7rYI1RNzeupdj+QoP\nRs5CGiaW/iOuNuUCuPxHzkvkIvW2BPTYE0Njb36v0nzYeP/hlUUavLlQTgUs\n5KKkdcGmzJWt7xfZ/p3A+ZXTVICwy1GFfdedvfzoEIEPZ2crQ7lV6wmNxe2z\n3ZMg\r\n=dBB1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/fake-timers-src.js", "husky": {"hooks": {"pre-commit": "npm run lint"}}, "gitHead": "12b0afe79c1312d0638c1fb09ca721e2804fb56e", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "version": "./scripts/version.sh", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "7.21.0", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "16.8.0", "dependencies": {"@sinonjs/commons": "^1.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "husky": "4.2.1", "jsdom": "16.5.2", "mocha": "8.3.2", "mochify": "7.0.0", "prettier": "2.2.1", "lint-staged": "10.0.7", "@sinonjs/eslint-config": "4.0.2", "@sinonjs/referee-sinon": "6.0.1", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "3.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_9.1.0_1643887850328_0.1558935981393048", "host": "s3://npm-registry-packages"}}, "9.1.1": {"name": "@sinonjs/fake-timers", "version": "9.1.1", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@9.1.1", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "7b698e0b9d12d93611f06ee143c30ced848e2840", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-9.1.1.tgz", "fileCount": 5, "integrity": "sha512-Wp5vwlZ0lOqpSYGKqr53INws9HLkt6JDc/pDZcPf7bchQnrXJMXPns8CXx0hFikMSGSWfvtvvpb2gtMVfkWagA==", "signatures": [{"sig": "MEUCIQDvHZNwnu0YRbaj02i7glcBj6hxEzHvIWISg//UfEuxbwIgV4oPbvZam8wPlLTOWkn3FujMLXSecGWVqAKZKv+wpEc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91191, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIxjfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmolbQ//ZiNhv+LZVCEdKNyq/pKpj957Xu5rO0DIwcYslosyMKoUJm30\r\nOqRWe27he8cqrfEnXWMfXbV2AtAYVQnwj1quVLqyMHrERHPzUJaHgSDitzi0\r\nzZ1BdhVeXC/77GECCSU+eiI9zLP6oX74UPxEAKc37NS2MOo8SFszB2wtVrnv\r\n0nzlOClsEo897NlyXuUmWAiSyd7+3StkbetQzNJILXnLQI9Y+0rSuii5ul4e\r\nly1u7bjA4xkzS6WKkPTeUFEcnemNDQyM2n6WVPSHVusdtJHmE1cd4TYInn82\r\nanDIe38sBXWusTqxDqfTDVCLz8yeitTRN/Ar0rBjzXA300bg2NTiE9lc8IEn\r\nU1IsgU5QuQnQIUtOuejMU7JsJ8PAHB8twNR7xCV0LeoddIOe3nt/6LBmv7ZV\r\nq2M3W/LffpKYhBOaf0mAqOkYhUnwTm3ljUkJ2Q0uz1GHkP/NBQ29xEsFiMhP\r\nVdyt6AbEVR74kGHRRqee88pMP2MwPbgiXFexwOnLbPehQBSWq7kCSV9sDUce\r\nDMzXaYo2JlO6RRvtzCjASofw5c8y7quVUl0AJvZj0rsO3S0GwKKv1/VRGciQ\r\n5+96PWQ0SlzrzwtHSByFgB5zPFyzMg2AMr/3R5TpoJbUYgEePdjNmACju5bh\r\n5PXVdX0YRZ9RXXy9LEwLRXt6ss00N+bb0N4=\r\n=PBfi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/fake-timers-src.js", "husky": {"hooks": {"pre-commit": "npm run lint"}}, "gitHead": "f8a09c9096715789147b1132c645281eb0c3e341", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "version": "./scripts/version.sh", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "14.18.1", "dependencies": {"@sinonjs/commons": "^1.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "husky": "4.2.1", "jsdom": "16.5.2", "mocha": "9.2.0", "mochify": "7.0.0", "prettier": "2.2.1", "lint-staged": "10.0.7", "@sinonjs/eslint-config": "4.0.2", "@sinonjs/referee-sinon": "6.0.1", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "3.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_9.1.1_1646467295652_0.8974982472405524", "host": "s3://npm-registry-packages"}}, "9.1.2": {"name": "@sinonjs/fake-timers", "version": "9.1.2", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@9.1.2", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "4eaab737fab77332ab132d396a3c0d364bd0ea8c", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-9.1.2.tgz", "fileCount": 5, "integrity": "sha512-BPS4ynJW/o92PUR4wgriz2Ud5gpST5vz6GQfMixEDK0Z8ZCUv2M7SkBLykH56T++Xs+8ln9zTGbOvNGIe02/jw==", "signatures": [{"sig": "MEQCIDaDd+kSrebGMyTeClC+PrmmqBGd1knmWKhVTf8aJSFaAiBgcns7v3mBICFL1vImeTyZOMcB3ohOeVAyzvvJHQ75yw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVowZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9PQ//Rtvbz0MrMK4Zrp0Z6d9CtK4pdrn7swSiAi1d4zgdSK7EZXhM\r\nOI4YB2+abVaqFKBIBtwsW0Rf3F+871A4pqxBQkgaFrQjs+f0X+DpTzKYsnG4\r\nGSYbLARkLa6UNEHe3ABWmCSs7d0kDslctFfLhcsB92QVJW0Y0MzxlkJACjHC\r\nS8UzCrJVQaX9jNiximZnYni0ZYfsHZSrPEJXE7f6p0x669xPiOCjlPg3G8a2\r\njz091ZYUUc3LHrhlQX1w2pXWtLPtdMiJhMrtHpxgmJILBWXBiOm84ti+Yd0W\r\noQZkeRyaJYSNUwGWT4tJlxWyEt3hJNf4Jj+yC0AEEUTwAixtwRmuuwgXVEDY\r\n1HDki7anJ3AWKn8XKcJYhv3JpFi4vmtg7j3tciOKzSuDh/be8yGiI1fIynIU\r\nqRgF1THFweDOjsrPtrUVbp7yeHS+PCUXzR5e+bHLhWB5PwAxF/vlWNP4s4lL\r\nyPx8n+72125r9koNolcxjOSrL94h0TIpgSxjbPQNiZiDp1uiA0aYEsbNMOMl\r\nwRV7RcYK3yVSVZ2Hwmj9Brwah0PGRS1024VNR2SSJkX1xkEVs6qy/8/yZre2\r\nfCbzTe0l7FMSXSjqVDS4ijd0uIfrlgKH0MmtQvFhkYOVCeOCw4jHFVFZuBtP\r\n/X3wVRqHJp51o27MG37Z4eJ11dZWcMW3nN8=\r\n=D2gO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/fake-timers-src.js", "husky": {"hooks": {"pre-commit": "npm run lint"}}, "gitHead": "486125b046d2de129d223b0abb4d41999a368b46", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "version": "./scripts/version.sh", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "6.14.14", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "14.17.4", "dependencies": {"@sinonjs/commons": "^1.7.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "husky": "4.2.1", "jsdom": "16.5.2", "mocha": "9.2.0", "mochify": "7.0.0", "prettier": "2.2.1", "lint-staged": "10.0.7", "@sinonjs/eslint-config": "4.0.2", "@sinonjs/referee-sinon": "6.0.1", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "3.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_9.1.2_1649839129717_0.0445675055298389", "host": "s3://npm-registry-packages"}}, "10.0.0": {"name": "@sinonjs/fake-timers", "version": "10.0.0", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@10.0.0", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "b6fe200d1d44f70d430d200d4dd85c640e576e2e", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-10.0.0.tgz", "fileCount": 4, "integrity": "sha512-OjRc0IcyLLGLmu/vkJmqEYULU2mG/S7dLxPD+aONYWvTX7yia4mxKHs8Lz1ymfDv8KX3Adp/kRWUxi19ouaPsg==", "signatures": [{"sig": "MEUCIQCrydZ52YbUlSY93FohTarQ9MjnQlj3UE9t4Dkj6Ya5fgIgUpGrxuZxj3bbvvQANzsMJFZW+hDYXLt6+UV2kxwcptY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79010, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjanJ4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpu/xAAnbQF7vP2wF3fBltBCaTe2EeJOdLs/Wxa4zixf690sNuIUJ4Q\r\ndnqQSpzlQvkdtHe1D+T5DMB8LnVOH3YyLc53OFeKvg2AtRCs0MOCG5ECMyp3\r\ns/QqmpsWLJe73PoAwUu5VPTMXDGPLJC/dpXMxJliI3OrZX8GjWGMAme0p6Pl\r\nEFoBjLSo9F3yFiT/PeYCzwYXkgG2UDyKj2I42dPh2jylNLftaRpAWKkCwRN1\r\nrGVV1gYKCGiZOIw2g1DmbPPyQ77ki6FBSwIqMTzbzVZJB8wkU1uXVtYGly+2\r\nd5tIyZSXsJk1pfNfZnB1DKXe+MTuO/lv3Kcz4iHHiRJxrf67Y4UpKwsWtz6F\r\n30bx4ikrKnHE/DLnorxQrkXUN15a7jEoyy27+SzPIedNRZEmcLKsxKqOKaH6\r\nU6OF3t/kLC1N2cOSQeF0oGjTm8e3zIXtk2co8A0r+pTzyjzaD4MN1khzFHfc\r\nd6IblR9ufWR9yBpiyCJoIQr6KzcecgAeWdIpggl++YpF1jURVy+j0z6h3RMJ\r\nLV8kNAvVOi8fQxu99KQ9JndHFxs1mXu3n8cNdoxecEuCSsBL10mWNTWW/AC4\r\nr9zPFoALeVXwVGxyAyuLwkK2v2PgRVzxDm9ViTB8ZRuk8SKvcIq98wXKPh/T\r\nBe03ECmpDQ7SGE6qJ6BPLDAwfiow+R9DvdU=\r\n=Vw18\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/fake-timers-src.js", "husky": {"hooks": {"pre-commit": "npm run lint"}}, "gitHead": "b0e7dc58b4d967f9c88772c432f5bec69f15861c", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "version": "./scripts/version.sh", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "16.14.2", "dependencies": {"@sinonjs/commons": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "14.1.1", "husky": "4.2.1", "jsdom": "16.5.2", "mocha": "9.2.0", "mochify": "^9.2.0", "prettier": "2.2.1", "lint-staged": "10.0.7", "@sinonjs/eslint-config": "4.0.2", "@sinonjs/referee-sinon": "6.0.1", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "3.4.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_10.0.0_1667920504100_0.7477487448946607", "host": "s3://npm-registry-packages"}}, "10.0.1": {"name": "@sinonjs/fake-timers", "version": "10.0.1", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@10.0.1", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "dab8bea8160d432757266b7582d2909afed8431c", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-10.0.1.tgz", "fileCount": 4, "integrity": "sha512-TyKuXJrxxoX+VrQ0uymFToB7zPpfarK4J2JhFanl7TwWEur+5h9Ho/eGIQRqRV/Alk1VFXnk9Jiw8ZV6RzZOmA==", "signatures": [{"sig": "MEYCIQCnjiEx/yf3vXnMVScTfxX72YbPr8PaZBt0tLN8jF7NRAIhAJL4HBo1A/muaiIO1y74ebF4qhnX/7Uaf7THxbZ4buSf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmzPxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoftw//bi2gUacOuApslzE/ZsXV+Cafuiua2ma9NLOoGRJkvaCb/DOJ\r\n1GC1iLC8mts0eqNnFvuSXkan+Fv9gnumj9aqDWf9Re1dMgGmuWoOH6jNfiKO\r\njBMOUP64B5JNcWL5aMypg7c/iHXIz2juBZK6sQFzhTzGLzyEcdd2/OeHuUO9\r\nRssa87DlmedoCg9cj7GCuqOC1wcA81mTXutHORapVf6KPTrothEKOcWSVEGj\r\ndCzi2WnIeUp8eJyU2yCGU/O/rkBhSgIfZx77SdW+RAsfnDwy+9YAAZef0Uer\r\nXquJT44Sh6Bq76yDsLOfPXJQn6f7XfQNTsq6MKtqOJW5BRGnKSAdf0iPk1FV\r\nwuQ+dsuCNcMe+ml4V0s8EHGt89bRtQYOY3jPsSCowA8mmvTTcI2/ktQcbvw8\r\nm213txKa3QcaBo7Lo8KcBDXByGgl6T5Pv9xJd4wmiXcAQiqdc54n4TzbbJew\r\nkT7ic0FBKZ1tgCCgr3NCQRK8V8NTRpJHJB3d8GyDtYrk51Dor1OkWzeD/0M2\r\nWKPMY+LXnAx+43lZ+X88oRWLJYO/3BMmjfIEKxEyhkXNWhfVrp29Uz6bME0U\r\nu+kFLWZ32fDHpLVnuoTvh49wUuooGyaMRZdE3kel4S+IH3i6l4mx6eaHQdY/\r\n7asA9eqaTu/Obb0t56aiGjxUVHMIm7hd8Lc=\r\n=GkyC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/fake-timers-src.js", "gitHead": "3729da618f47b88010b0a82648b90c8b000e9f72", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "prepare": "husky install", "version": "./scripts/version.sh", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "16.19.0", "dependencies": {"@sinonjs/commons": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "husky": "^8.0.0", "jsdom": "20.0.2", "mocha": "10.1.0", "mochify": "9.2.0", "prettier": "2.7.1", "lint-staged": "13.0.3", "@sinonjs/eslint-config": "4.0.6", "@sinonjs/referee-sinon": "10.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_10.0.1_1671115760814_0.16530720984195768", "host": "s3://npm-registry-packages"}}, "10.0.2": {"name": "@sinonjs/fake-timers", "version": "10.0.2", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@10.0.2", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "d10549ed1f423d80639c528b6c7f5a1017747d0c", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-10.0.2.tgz", "fileCount": 4, "integrity": "sha512-SwUDyjWnah1AaNl7kxsa7cfLhlTYoiyhDAIgyh+El30YvXs/o7OLXpYH88Zdhyx9JExKrmHDJ+10bwIcY80Jmw==", "signatures": [{"sig": "MEUCIQCXmKvztWoINl+oS9WmVHsIfiPtZY2eN/jWlZbQE6+WbgIgTn1R7AEf/m+eDsTDzv0QYhn0V5x7FaJ/dDaRQ3cpRSM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmzRwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrpXA//fvZrusa0xtb68dN7Bv5sL8DzmrUZFUc0dv9csr2EjIlzlOaY\r\nAYsTZxRR2tqe5fGxPRmNzgVSLVti3BC9ZPKNuY4w+mdMamACI5CfJ+TtUKml\r\nkUky9jG7KWPmzh3TNIJzWsmgoSd/CvrRmYHAZyuAoZXDLQLVuEOcLEbL9b8X\r\nLhdR0n3LxkYytZPSD9qB7Tin5iVEBGPfV5bgRWmQgbNl+WtOTx1hC6B6MQEO\r\n2AmPnBI4dcnfMv2/0FY5TVsGvJKRnfT7GUWhQ/IQ5PWqhHeY1fECSL3vCpSP\r\ndGbqemVqnW+EusX2WFvfrUyrQbzpqnQ7Arh1AIClgiTVauzEwyotd04DA2UB\r\nrAWtfoldDQaqViyUHFF0BwU2WxXMP8OLaMqTyX6Aj9EPoaRwis2/7sarkQGg\r\nYDgTTMXo9A8hvhuAzwcUoFXfKgaX7fdi8KO/0U8aZzqni49J5fz62gSp/nrp\r\nXiNWvzEGFWBPKkLgf1WHgB07+Mjxv6nGjKsHtHycV6B+KWTnNUyIJDoDvLPR\r\nMXU7EkVZbIyjjnQuHKgkH8ijhTJWgbZ5ZcZ07Yth1aIfCADzV0vo00e5xuOP\r\n51xxIbzX9G21aycNBZVrrNzsVRtSb8DcL8HbCxtEIanI/Bdo7c8tbR3GTDHg\r\n+n4ScJJE49d+dncG24YMyT2h/MZKKOvzA7I=\r\n=mCqX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./src/fake-timers-src.js", "gitHead": "993a906080a7f9b189614cbed803a12ed8746fe6", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "prepare": "husky install", "version": "./scripts/version.sh", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "16.19.0", "dependencies": {"@sinonjs/commons": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "husky": "^8.0.0", "jsdom": "20.0.2", "mocha": "10.1.0", "mochify": "9.2.0", "prettier": "2.7.1", "lint-staged": "13.0.3", "@sinonjs/eslint-config": "4.0.6", "@sinonjs/referee-sinon": "10.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_10.0.2_1671115888250_0.4825686993184817", "host": "s3://npm-registry-packages"}}, "10.1.0": {"name": "@sinonjs/fake-timers", "version": "10.1.0", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@10.1.0", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "3595e42b3f0a7df80a9681cf58d8cb418eac1e99", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-10.1.0.tgz", "fileCount": 4, "integrity": "sha512-w1qd368vtrwttm1PRJWPW1QHlbmHrVDGs1eBH/jZvRPUFS4MNXV9Q33EQdjOdeAxZ7O8+3wM7zxztm2nfUSyKw==", "signatures": [{"sig": "MEUCIQDLV9rPyr2GdLmNhQLZGcLYH4qloaOY3vfjtTWNq8pYSQIgeQSkrS3wB7OdU6C64oMCtlC4uk8hhbGRG82z2c4NNxE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80098}, "main": "./src/fake-timers-src.js", "gitHead": "dbdf02ac03f0057dddc8be9320170cba075a478e", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "prepare": "husky install", "version": "./scripts/version.sh", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "18.13.0", "dependencies": {"@sinonjs/commons": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "husky": "^8.0.3", "jsdom": "22.0.0", "mocha": "10.2.0", "mochify": "9.2.0", "prettier": "2.8.8", "lint-staged": "13.2.2", "@sinonjs/eslint-config": "^4.1.0", "@sinonjs/referee-sinon": "11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_10.1.0_1683983750671_0.8232424550848103", "host": "s3://npm-registry-packages"}}, "10.2.0": {"name": "@sinonjs/fake-timers", "version": "10.2.0", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@10.2.0", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "swenzel-arc", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "b3e322a34c5f26e3184e7f6115695f299c1b1194", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-10.2.0.tgz", "fileCount": 4, "integrity": "sha512-OPwQlEdg40HAj5KNF8WW6q2KG4Z+cBCZb3m4ninfTZKaBmbIJodviQsDBoYMPHkOyJJMHnOJo5j2+LKDOhOACg==", "signatures": [{"sig": "MEQCIFnmDqoegmpxLBVLThX9HzER4SiKlXWZEFfQxR7+qIxoAiAF+p5c6BC+EgrBUHLfGAOboS13oM3tMXKofbJCzUfOdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81533}, "main": "./src/fake-timers-src.js", "gitHead": "2fc83240262c8f0e8690e75d9d18dca1fd61b952", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "prepare": "husky install", "version": "./scripts/version.sh", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "deprecated": "Use version 10.1.0. Version 10.2.0 has potential breaking issues", "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "16.19.0", "dependencies": {"@sinonjs/commons": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "husky": "^8.0.3", "jsdom": "22.0.0", "mocha": "10.2.0", "mochify": "9.2.0", "prettier": "2.8.8", "lint-staged": "13.2.2", "@sinonjs/eslint-config": "^4.1.0", "@sinonjs/referee-sinon": "11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_10.2.0_1684417204513_0.8582461018825891", "host": "s3://npm-registry-packages"}}, "11.0.0": {"name": "@sinonjs/fake-timers", "version": "11.0.0", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@11.0.0", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "swenzel-arc", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "14100fe7e5dc74973bc458eb7205537038d60b58", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-11.0.0.tgz", "fileCount": 4, "integrity": "sha512-bqiI/5ur6ZOozG06BeJjbplIqHY/KftV1zaewbZHORH902GrHURKwl7H1G/4OC5EaxDYQJlrD0OLJ1XD6x01dQ==", "signatures": [{"sig": "MEUCIHwGRQk+ns7yl5Q/w6rZ+lalhs5vOjkHHTonA4saRbGCAiEAiBu1qtxm33lZHFtSO2tR8nO7XWu/o+0FbA/A3285z8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81533}, "main": "./src/fake-timers-src.js", "gitHead": "a4c757f80840829e45e0852ea1b17d87a998388e", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "prepare": "husky install", "version": "./scripts/version.sh", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "20.3.0", "dependencies": {"@sinonjs/commons": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "husky": "^8.0.3", "jsdom": "22.0.0", "mocha": "10.2.0", "mochify": "9.2.0", "prettier": "2.8.8", "lint-staged": "13.2.2", "@sinonjs/eslint-config": "^4.1.0", "@sinonjs/referee-sinon": "11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_11.0.0_1686575388676_0.4643002303389583", "host": "s3://npm-registry-packages"}}, "10.3.0": {"name": "@sinonjs/fake-timers", "version": "10.3.0", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@10.3.0", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "55fdff1ecab9f354019129daf4df0dd4d923ea66", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz", "fileCount": 4, "integrity": "sha512-V4BG07kuYSUkTCSBHG8G8TNhM+F19jXFWnQtzj+we8DrkpSBCee9Z3Ms8yiGer/dlmhe35/Xdgyo3/0rQKg7YA==", "signatures": [{"sig": "MEQCIGgWvsv+lhuHL4QVn+VgLTMFhBPIZkdX+HLqwV/OmA4NAiAdFeDbIAUkd1dsb5oYT/6MV3f8Rr73aJeNKpQDHWyhRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80098}, "main": "./src/fake-timers-src.js", "gitHead": "e650e39536ad8cbf262996195850c3655b36a82b", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "prepare": "husky install", "version": "./scripts/version.sh", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "16.20.0", "dependencies": {"@sinonjs/commons": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "husky": "^8.0.3", "jsdom": "22.0.0", "mocha": "10.2.0", "mochify": "9.2.0", "prettier": "2.8.8", "lint-staged": "13.2.2", "@sinonjs/eslint-config": "^4.1.0", "@sinonjs/referee-sinon": "11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_10.3.0_1687269220311_0.21742658615509725", "host": "s3://npm-registry-packages"}}, "11.1.0": {"name": "@sinonjs/fake-timers", "version": "11.1.0", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@11.1.0", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "75191941+<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "swenzel-arc", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "5ad7b44514a61bbd04a3ddec863e21edd6efc2da", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-11.1.0.tgz", "fileCount": 4, "integrity": "sha512-pUBaWhXoa9N0R/LeYKLqkrN9mqN3jwKBeMfbvlRtHUzLmk55o+0swncIuZBcSH/PpXDttRf/AcPF22pknAzORQ==", "signatures": [{"sig": "MEYCIQCjZVFtmbrVxMwBP/YnIE6y3JYW3oFwfM3shN8OkLXgOQIhAJnVUcruDKADjIor78UBHqMffp5nkCZtFRRKvMhtdKzk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82137}, "main": "./src/fake-timers-src.js", "gitHead": "c85ef142837afdbc732b0f73fdba30c3bd037965", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "prepare": "husky install", "version": "./scripts/version.sh", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "20.5.0", "dependencies": {"@sinonjs/commons": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "husky": "^8.0.3", "jsdom": "22.0.0", "mocha": "10.2.0", "mochify": "9.2.0", "prettier": "2.8.8", "lint-staged": "13.2.2", "@sinonjs/eslint-config": "^4.1.0", "@sinonjs/referee-sinon": "11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_11.1.0_1692295937201_0.07288184524434027", "host": "s3://npm-registry-packages"}}, "11.2.0": {"name": "@sinonjs/fake-timers", "version": "11.2.0", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@11.2.0", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "75191941+<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "swenzel-arc", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "ddf074a800edb38d96d7d579109f163c2bf6e206", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-11.2.0.tgz", "fileCount": 4, "integrity": "sha512-X2VlTY93hUCccYgsQVlpwvS7vMODIRT05X985ybEk2DhtdEiaOdYHSRrFJZGP22bSpC6WCt6MZ930Vv78RxzYw==", "signatures": [{"sig": "MEQCIAfX0YzVoY5WIkNOzzC5ZpZYOiJecgoGSdCCAQWJ2q4DAiAs6GHTThtHHb/w7Nym+V5LFZcZSK0rO9jEfHR2Dm2d4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82919}, "main": "./src/fake-timers-src.js", "gitHead": "4756cf1db42ac1bf54305e8b508139b32b0e2fdc", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "prepare": "husky install", "version": "./scripts/version.sh", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "18.17.1", "dependencies": {"@sinonjs/commons": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "husky": "^8.0.3", "jsdom": "22.1.0", "mocha": "10.2.0", "mochify": "9.2.0", "prettier": "3.0.3", "lint-staged": "15.0.1", "@sinonjs/eslint-config": "^4.1.0", "@sinonjs/referee-sinon": "11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_11.2.0_1697701350611_0.14831151990169356", "host": "s3://npm-registry-packages"}}, "11.2.1": {"name": "@sinonjs/fake-timers", "version": "11.2.1", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@11.2.1", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "75191941+<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "swenzel-arc", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>o", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "92a511680aa6ddd8c0af1072a53c3acb63796c74", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-11.2.1.tgz", "fileCount": 4, "integrity": "sha512-CiDPMFTZtdaEhKB6Rl2v2CmOMTbTNEOC0p3fSBCYtd0g2re4zu3ArYN8RxUeU8aftNi1Yvpm8f+UqgTPJ8mymA==", "signatures": [{"sig": "MEUCIQC+QJ7vCbwjC9Z9PNNn4Kz8wmz+PrCZGyKFDVGLGozIWQIgDyTZyH0vsGmnzDWGR9vV6DcYdjkU/I1QpaMlHKRKD/k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82919}, "main": "./src/fake-timers-src.js", "gitHead": "f6ef3927ee9d42858d499379d7767c27f9f48c05", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "prepare": "husky install", "version": "./scripts/version.sh", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "18.17.1", "dependencies": {"@sinonjs/commons": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "husky": "^8.0.3", "jsdom": "22.1.0", "mocha": "10.2.0", "mochify": "9.2.0", "prettier": "3.0.3", "lint-staged": "15.0.1", "@sinonjs/eslint-config": "^4.1.0", "@sinonjs/referee-sinon": "11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_11.2.1_1697701574197_0.6408928371516376", "host": "s3://npm-registry-packages"}}, "11.2.2": {"name": "@sinonjs/fake-timers", "version": "11.2.2", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@11.2.2", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "75191941+<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "swenzel-arc", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>o", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "50063cc3574f4a27bd8453180a04171c85cc9699", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-11.2.2.tgz", "fileCount": 4, "integrity": "sha512-G2piCSxQ7oWOxwGSAyFHfPIsyeJGXYtc6mFbnFA+kRXkiEnTl8c/8jul2S329iFBnDI9HGoeWWAZvuvOkZccgw==", "signatures": [{"sig": "MEUCIC1b08CJjbqhuwsXvdZaIAYxyVUaizA+muzcHzrzTAm5AiEAia4yT5cVHP0lV777xdXRRAewW1AukV7BOFQepeX3c50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83016}, "main": "./src/fake-timers-src.js", "gitHead": "757676124c2302ab622af7b5fbe46f9f61e70f7f", "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "prepare": "husky install", "version": "./scripts/version.sh", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "mochify --wd --no-detect-globals --timeout=10000", "postversion": "./scripts/postversion.sh", "test-coverage": "nyc --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --no-detect-globals --timeout=10000", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "18.17.1", "dependencies": {"@sinonjs/commons": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "15.1.0", "husky": "^8.0.3", "jsdom": "22.1.0", "mocha": "10.2.0", "mochify": "9.2.0", "prettier": "3.0.3", "lint-staged": "15.0.1", "@sinonjs/eslint-config": "^4.1.0", "@sinonjs/referee-sinon": "11.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_11.2.2_1697807759177_0.26392954428563353", "host": "s3://npm-registry-packages"}}, "11.3.0": {"name": "@sinonjs/fake-timers", "version": "11.3.0", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@11.3.0", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "White Autumn", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "75191941+<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "swenzel-arc", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>o", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "1dd7479954c880bbac93478200baf2aa6b86d518", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-11.3.0.tgz", "fileCount": 4, "integrity": "sha512-FIOPDrG9bjKn6Ab/N+xNj2EScAEJ2suAQbC0Pmi9Y4QCXXv6wkxLPa/9/9V6mJCbTHWTceshKBrtuZUG7X3riQ==", "signatures": [{"sig": "MEUCIQCJgW5bRZUKgesZlkh/MSsXabLIsJ5m6svlSSCCPp+ylAIgX6nSeJiGiGLfHf0b+ObqOnf2Q5zblPNs5C+DT3rT2Fw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96488}, "main": "./src/fake-timers-src.js", "gitHead": "cc6be59ac02ded7af6a485ccc0a6ede25ee05f08", "mochify": {"spec": "test/**/*-test.js", "bundle": "esbuild --bundle --sourcemap=inline --define:process.env.NODE_DEBUG=\"\"", "timeout": 10000, "reporter": "dot", "bundle_stdin": "require"}, "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "prepare": "husky", "version": "./scripts/version.sh", "test-edge": "BROWSER_NAME=MicrosoftEdge mochify --config mochify.webdriver.js", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "npm run test-edge && npm run test-firefox && npm run test-safari", "postversion": "./scripts/postversion.sh", "test-safari": "BROWSER_NAME=safari mochify --config mochify.webdriver.js", "test-firefox": "BROWSER_NAME=firefox mochify --config mochify.webdriver.js", "test-coverage": "nyc -x mochify.webdriver.js -x coverage --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --driver puppeteer", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "20.16.0", "dependencies": {"@sinonjs/commons": "^3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "17.0.0", "husky": "^9.1.5", "jsdom": "24.1.1", "mocha": "10.7.3", "esbuild": "^0.23.1", "prettier": "3.3.3", "lint-staged": "15.2.9", "@mochify/cli": "^0.4.1", "@sinonjs/eslint-config": "^5.0.3", "@sinonjs/referee-sinon": "12.0.0", "@mochify/driver-puppeteer": "^0.4.0", "@mochify/driver-webdriver": "^0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_11.3.0_1724484546518_0.8975206899084605", "host": "s3://npm-registry-packages"}}, "11.3.1": {"name": "@sinonjs/fake-timers", "version": "11.3.1", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@11.3.1", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "White Autumn", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "75191941+<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "swenzel-arc", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>o", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "51d6e8d83ca261ff02c0ab0e68e9db23d5cd5999", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-11.3.1.tgz", "fileCount": 4, "integrity": "sha512-EVJO7nW5M/F5Tur0Rf2z/QoMo+1Ia963RiMtapiQrEWvY0iBUvADo8Beegwjpnle5BHkyHuoxSTW3jF43H1XRA==", "signatures": [{"sig": "MEUCICxqJ/Y3rRhWJSWkIXGqgHEddJqbwCJV35cyPqhY17TVAiEA3EatvSeCjPyN14hjt9qsM8vzie+99G8C0J+uqtLN/Yo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96488}, "main": "./src/fake-timers-src.js", "gitHead": "c1368306d459d93b4c260d782309cc36ee344bad", "mochify": {"spec": "test/**/*-test.js", "bundle": "esbuild --bundle --sourcemap=inline --define:process.env.NODE_DEBUG=\"\"", "timeout": 10000, "reporter": "dot", "bundle_stdin": "require"}, "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "prepare": "husky", "version": "./scripts/version.sh", "test-edge": "BROWSER_NAME=MicrosoftEdge mochify --config mochify.webdriver.js", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "npm run test-edge && npm run test-firefox && npm run test-safari", "postversion": "./scripts/postversion.sh", "test-safari": "BROWSER_NAME=safari mochify --config mochify.webdriver.js", "test-firefox": "BROWSER_NAME=firefox mochify --config mochify.webdriver.js", "test-coverage": "nyc -x mochify.webdriver.js -x coverage --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --driver puppeteer", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "20.16.0", "dependencies": {"@sinonjs/commons": "^3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "17.0.0", "husky": "^9.1.5", "jsdom": "24.1.1", "mocha": "10.7.3", "esbuild": "^0.23.1", "prettier": "3.3.3", "lint-staged": "15.2.9", "@mochify/cli": "^0.4.1", "@sinonjs/eslint-config": "^5.0.3", "@sinonjs/referee-sinon": "12.0.0", "@mochify/driver-puppeteer": "^0.4.0", "@mochify/driver-webdriver": "^0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_11.3.1_1724485948587_0.8348739624154269", "host": "s3://npm-registry-packages"}}, "12.0.0": {"name": "@sinonjs/fake-timers", "version": "12.0.0", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@12.0.0", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "White Autumn", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "75191941+<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "swenzel-arc", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>o", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "7cd4e0d2726268cb34abb8c121e814447e69eb95", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-12.0.0.tgz", "fileCount": 4, "integrity": "sha512-bockPohsu/W5uL2w7km2fQcLBm0hi4k2h6Z3/tfzNGQ2BTuFEhJoFrMMpmGAW7zHqop0wtuXDm/WHPNY6JLoHA==", "signatures": [{"sig": "MEUCIQC5JjAbV1t1zvEW7uLx2br8ExbkkdmH44BESFm242kHTQIgI65ZGJQfgXm/wW2IFdPqsf4sku07gqr9Ks+96mfq49Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96232}, "main": "./src/fake-timers-src.js", "gitHead": "92e247e61a7eefeaa75fb362cdbda8fddd266c03", "mochify": {"spec": "test/**/*-test.js", "bundle": "esbuild --bundle --sourcemap=inline --define:process.env.NODE_DEBUG=\"\"", "timeout": 10000, "reporter": "dot", "bundle_stdin": "require"}, "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "prepare": "husky", "version": "./scripts/version.sh", "test-edge": "BROWSER_NAME=MicrosoftEdge mochify --config mochify.webdriver.js", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "npm run test-edge && npm run test-firefox && npm run test-safari", "postversion": "./scripts/postversion.sh", "test-safari": "BROWSER_NAME=safari mochify --config mochify.webdriver.js", "test-firefox": "BROWSER_NAME=firefox mochify --config mochify.webdriver.js", "test-coverage": "nyc -x mochify.webdriver.js -x coverage --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --driver puppeteer", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "20.16.0", "dependencies": {"@sinonjs/commons": "^3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "17.0.0", "husky": "^9.1.5", "jsdom": "24.1.1", "mocha": "10.7.3", "esbuild": "^0.23.1", "prettier": "3.3.3", "lint-staged": "15.2.9", "@mochify/cli": "^0.4.1", "@sinonjs/eslint-config": "^5.0.3", "@sinonjs/referee-sinon": "12.0.0", "@mochify/driver-puppeteer": "^0.4.0", "@mochify/driver-webdriver": "^0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_12.0.0_1724522090597_0.060707159594807214", "host": "s3://npm-registry-packages"}}, "13.0.0": {"name": "@sinonjs/fake-timers", "version": "13.0.0", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@13.0.0", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "White Autumn", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "75191941+<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "swenzel-arc", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>o", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "2798d453bd8e3443cbde7dd51fee8602e01c5448", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-13.0.0.tgz", "fileCount": 4, "integrity": "sha512-YxGw8l0gqNDeKhk0crDD2B12tLjy7EqLmEheRSwGR3u5e1oEEoEFeQTPX8+071oJ4CwZOyaLXTkrPwFgUjbCRA==", "signatures": [{"sig": "MEQCIEKT2z6eqH0d3hYcIC/orWXFclLSYXhBcVqndI3hoPAMAiBrTU+ddy/++PbMXHRUBJHinbnNBK3sSlcazEAM44h43g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96600}, "main": "./src/fake-timers-src.js", "gitHead": "97d93129f1f46f5085ef150ae46114e0b5d4b978", "mochify": {"spec": "test/**/*-test.js", "bundle": "esbuild --bundle --sourcemap=inline --define:process.env.NODE_DEBUG=\"\"", "timeout": 10000, "reporter": "dot", "bundle_stdin": "require"}, "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "prepare": "husky", "version": "./scripts/version.sh", "test-edge": "BROWSER_NAME=MicrosoftEdge mochify --config mochify.webdriver.js", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "npm run test-edge && npm run test-firefox && npm run test-safari", "postversion": "./scripts/postversion.sh", "test-safari": "BROWSER_NAME=safari mochify --config mochify.webdriver.js", "test-firefox": "BROWSER_NAME=firefox mochify --config mochify.webdriver.js", "test-coverage": "nyc -x mochify.webdriver.js -x coverage --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --driver puppeteer", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "20.16.0", "dependencies": {"@sinonjs/commons": "^3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "17.0.0", "husky": "^9.1.5", "jsdom": "24.1.1", "mocha": "10.7.3", "esbuild": "^0.23.1", "prettier": "3.3.3", "lint-staged": "15.2.9", "@mochify/cli": "^0.4.1", "@sinonjs/eslint-config": "^5.0.3", "@sinonjs/referee-sinon": "12.0.0", "@mochify/driver-puppeteer": "^0.4.0", "@mochify/driver-webdriver": "^0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_13.0.0_1724611541593_0.09913082939009765", "host": "s3://npm-registry-packages"}}, "13.0.1": {"name": "@sinonjs/fake-timers", "version": "13.0.1", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@13.0.1", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "White Autumn", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "75191941+<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "swenzel-arc", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>o", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "7724d20630f0d306c706fdf36d0a29c8dbd525d0", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-13.0.1.tgz", "fileCount": 4, "integrity": "sha512-ZEbLYOvZQHccQJzbg2E5r+/Mdjb6BMdjToL4r8WwUw0VTjTnyY3gCnwLeiovcXI3/Uo25exmqmiwsjL/eE/rSg==", "signatures": [{"sig": "MEUCIQCOYNxkjbUaXS4QwIOQc0L/u6Rpy1DaZGciEWTB3cZJiwIgblfMMM7gGIqsMUgrNLWza3yqixb1mTDtP+V9KT3gZw8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96766}, "main": "./src/fake-timers-src.js", "gitHead": "710cafad25abe9465c807efd8ed9cf3a15985fb1", "mochify": {"spec": "test/**/*-test.js", "bundle": "esbuild --bundle --sourcemap=inline --define:process.env.NODE_DEBUG=\"\"", "timeout": 10000, "reporter": "dot", "bundle_stdin": "require"}, "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "prepare": "husky", "version": "./scripts/version.sh", "test-edge": "BROWSER_NAME=MicrosoftEdge mochify --config mochify.webdriver.js", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "npm run test-edge && npm run test-firefox && npm run test-safari", "postversion": "./scripts/postversion.sh", "test-safari": "BROWSER_NAME=safari mochify --config mochify.webdriver.js", "test-firefox": "BROWSER_NAME=firefox mochify --config mochify.webdriver.js", "test-coverage": "nyc -x mochify.webdriver.js -x coverage --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --driver puppeteer", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "20.16.0", "dependencies": {"@sinonjs/commons": "^3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "17.0.0", "husky": "^9.1.5", "jsdom": "24.1.1", "mocha": "10.7.3", "esbuild": "^0.23.1", "prettier": "3.3.3", "lint-staged": "15.2.9", "@mochify/cli": "^0.4.1", "@sinonjs/eslint-config": "^5.0.3", "@sinonjs/referee-sinon": "12.0.0", "@mochify/driver-puppeteer": "^0.4.0", "@mochify/driver-webdriver": "^0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_13.0.1_1724665421585_0.27018625148168085", "host": "s3://npm-registry-packages"}}, "13.0.2": {"name": "@sinonjs/fake-timers", "version": "13.0.2", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@13.0.2", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "White Autumn", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "75191941+<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "swenzel-arc", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>o", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "3ffe88abb062067a580fdfba706ad00435a0f2a6", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-13.0.2.tgz", "fileCount": 4, "integrity": "sha512-4Bb+oqXZTSTZ1q27Izly9lv8B9dlV61CROxPiVtywwzv5SnytJqhvYe6FclHYuXml4cd1VHPo1zd5PmTeJozvA==", "signatures": [{"sig": "MEUCIQCRZmba371TkgUWt9HbIV+FKQS70UA/5VHhHqH3AYz8OwIgBxmDsxdSbNabkQEE9wFcUlvPqfLoAfuVGfuK5qNWb90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96985}, "main": "./src/fake-timers-src.js", "gitHead": "78024200ae5eb0398310b133cfcc106b0250ecad", "mochify": {"spec": "test/**/*-test.js", "bundle": "esbuild --bundle --sourcemap=inline --define:process.env.NODE_DEBUG=\"\"", "timeout": 10000, "reporter": "dot", "bundle_stdin": "require"}, "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "prepare": "husky", "version": "./scripts/version.sh", "test-edge": "BROWSER_NAME=MicrosoftEdge mochify --config mochify.webdriver.js", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "npm run test-edge && npm run test-firefox && npm run test-safari", "postversion": "./scripts/postversion.sh", "test-safari": "BROWSER_NAME=safari mochify --config mochify.webdriver.js", "test-firefox": "BROWSER_NAME=firefox mochify --config mochify.webdriver.js", "test-coverage": "nyc -x mochify.webdriver.js -x coverage --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --driver puppeteer", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "20.5.0", "dependencies": {"@sinonjs/commons": "^3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "17.0.0", "husky": "^9.1.5", "jsdom": "24.1.1", "mocha": "10.7.3", "esbuild": "^0.23.1", "prettier": "3.3.3", "lint-staged": "15.2.9", "@mochify/cli": "^0.4.1", "@sinonjs/eslint-config": "^5.0.3", "@sinonjs/referee-sinon": "12.0.0", "@mochify/driver-puppeteer": "^0.4.0", "@mochify/driver-webdriver": "^0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_13.0.2_1726235756479_0.9310277366565394", "host": "s3://npm-registry-packages"}}, "13.0.3": {"name": "@sinonjs/fake-timers", "version": "13.0.3", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@13.0.3", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "White Autumn", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "75191941+<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "swenzel-arc", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>o", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "3338263cbdfdb5cd40be61c3c315f360bf4f3a3b", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-13.0.3.tgz", "fileCount": 4, "integrity": "sha512-golm/Sc4CqLV/ZalIP14Nre7zPgd8xG/S3nHULMTBHMX0llyTNhE1O6nrgbfvLX2o0y849CnLKdu8OE05Ztiiw==", "signatures": [{"sig": "MEUCIQCoIPXw0eQFxaT+7Z5hDBRwmv6KLqMFajC3Itz8gJdaLwIgWEP39p7/yZ0CjKiJXkMgRGOuuPHZxjWfUx7CgbiQm5k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97172}, "main": "./src/fake-timers-src.js", "gitHead": "2eb02fe1ef38fc3a3e70dd017c1311d7216040ff", "mochify": {"spec": "test/**/*-test.js", "bundle": "esbuild --bundle --sourcemap=inline --define:process.env.NODE_DEBUG=\"\"", "timeout": 10000, "reporter": "dot", "bundle_stdin": "require"}, "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "prepare": "husky", "version": "./scripts/version.sh", "test-edge": "BROWSER_NAME=MicrosoftEdge mochify --config mochify.webdriver.js", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "npm run test-edge && npm run test-firefox && npm run test-safari", "postversion": "./scripts/postversion.sh", "test-safari": "BROWSER_NAME=safari mochify --config mochify.webdriver.js", "test-firefox": "BROWSER_NAME=firefox mochify --config mochify.webdriver.js", "test-coverage": "nyc -x mochify.webdriver.js -x coverage --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --driver puppeteer", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "20.16.0", "dependencies": {"@sinonjs/commons": "^3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "17.0.0", "husky": "^9.1.5", "jsdom": "24.1.1", "mocha": "10.7.3", "esbuild": "^0.23.1", "prettier": "3.3.3", "lint-staged": "15.2.9", "@mochify/cli": "^0.4.1", "@sinonjs/eslint-config": "^5.0.3", "@sinonjs/referee-sinon": "12.0.0", "@mochify/driver-puppeteer": "^0.4.0", "@mochify/driver-webdriver": "^0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_13.0.3_1729190204702_0.9956201308704691", "host": "s3://npm-registry-packages"}}, "13.0.4": {"name": "@sinonjs/fake-timers", "version": "13.0.4", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@13.0.4", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "White Autumn", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "75191941+<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "swenzel-arc", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>o", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "cacb89257e650f3214f9da5d9236f72c9658a607", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-13.0.4.tgz", "fileCount": 4, "integrity": "sha512-wpUq+QiKxrWk7U2pdvNSY9fNX62/k+7eEdlQMO0A3rU8tQ+vvzY/WzBhMz+GbQlATXZlXWYQqFWNFcn1SVvThA==", "signatures": [{"sig": "MEQCIAbEn2Do3FE7I8htsD66x0vlWWiStQZ/Ns3xywDLVqYhAiBzjPRmiDXvVwWCgmq1bHS4JS0jO/lzpu86ehcIpvaKnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97178}, "main": "./src/fake-timers-src.js", "gitHead": "77a516cd61379c63dc544971701dd9ec39432acb", "mochify": {"spec": "test/**/*-test.js", "bundle": "esbuild --bundle --sourcemap=inline --define:process.env.NODE_DEBUG=\"\"", "timeout": 10000, "reporter": "dot", "bundle_stdin": "require"}, "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "prepare": "husky", "version": "./scripts/version.sh", "test-edge": "BROWSER_NAME=MicrosoftEdge mochify --config mochify.webdriver.js", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "npm run test-edge && npm run test-firefox && npm run test-safari", "postversion": "./scripts/postversion.sh", "test-safari": "BROWSER_NAME=safari mochify --config mochify.webdriver.js", "test-firefox": "BROWSER_NAME=firefox mochify --config mochify.webdriver.js", "test-coverage": "nyc -x mochify.webdriver.js -x coverage --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --driver puppeteer", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "20.16.0", "dependencies": {"@sinonjs/commons": "^3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "17.0.0", "husky": "^9.1.5", "jsdom": "24.1.1", "mocha": "10.7.3", "esbuild": "^0.23.1", "prettier": "3.3.3", "lint-staged": "15.2.9", "@mochify/cli": "^0.4.1", "@sinonjs/eslint-config": "^5.0.3", "@sinonjs/referee-sinon": "12.0.0", "@mochify/driver-puppeteer": "^0.4.0", "@mochify/driver-webdriver": "^0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_13.0.4_1729594186224_0.27833474366407884", "host": "s3://npm-registry-packages"}}, "13.0.5": {"name": "@sinonjs/fake-timers", "version": "13.0.5", "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "_id": "@sinonjs/fake-timers@13.0.5", "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "White Autumn", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "75191941+<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "swenzel-arc", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>o", "email": "<EMAIL>"}], "homepage": "https://github.com/sinonjs/fake-timers", "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "nyc": {"lines": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"], "branches": 85, "functions": 92, "statements": 92}, "dist": {"shasum": "36b9dbc21ad5546486ea9173d6bea063eb1717d5", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-13.0.5.tgz", "fileCount": 4, "integrity": "sha512-36/hTbH2uaWuGVERyC6da9YwGWnzUZXuPro/F2LfsdOsLnCojz/iSH8MxUt/FD2S5XBSVPhmArFUXcpCQ2Hkiw==", "signatures": [{"sig": "MEUCIHCNG487OpCW1RGazS5Xx0rionfBM+yFH4Jw1UzWQONlAiEAsNNU6gO1xOsmJWqbJ7f72YuAljkEqmQxeyF1P0QIlEw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97290}, "main": "./src/fake-timers-src.js", "gitHead": "7861c930e8f9aaa9d227431623cc82347aff0291", "mochify": {"spec": "test/**/*-test.js", "bundle": "esbuild --bundle --sourcemap=inline --define:process.env.NODE_DEBUG=\"\"", "timeout": 10000, "reporter": "dot", "bundle_stdin": "require"}, "scripts": {"lint": "eslint .", "test": "npm run test-node && npm run test-headless", "prepare": "husky", "version": "./scripts/version.sh", "test-edge": "BROWSER_NAME=MicrosoftEdge mochify --config mochify.webdriver.js", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "preversion": "./scripts/preversion.sh", "test-cloud": "npm run test-edge && npm run test-firefox && npm run test-safari", "postversion": "./scripts/postversion.sh", "test-safari": "BROWSER_NAME=safari mochify --config mochify.webdriver.js", "test-firefox": "BROWSER_NAME=firefox mochify --config mochify.webdriver.js", "test-coverage": "nyc -x mochify.webdriver.js -x coverage --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test-headless": "mochify --driver puppeteer", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "test-check-coverage": "npm run test-coverage && nyc check-coverage"}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sinonjs/fake-timers.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Fake JavaScript timers", "directories": {}, "lint-staged": {"*.js": "eslint", "*.{js,css,md}": "prettier --check"}, "_nodeVersion": "20.16.0", "dependencies": {"@sinonjs/commons": "^3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "17.0.0", "husky": "^9.1.5", "jsdom": "24.1.1", "mocha": "10.7.3", "esbuild": "^0.23.1", "prettier": "3.3.3", "lint-staged": "15.2.9", "@mochify/cli": "^0.4.1", "@sinonjs/eslint-config": "^5.0.3", "@sinonjs/referee-sinon": "12.0.0", "@mochify/driver-puppeteer": "^0.4.0", "@mochify/driver-webdriver": "^0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/fake-timers_13.0.5_1730196701041_0.7163828184800818", "host": "s3://npm-registry-packages"}}, "14.0.0": {"name": "@sinonjs/fake-timers", "description": "Fake JavaScript timers", "version": "14.0.0", "homepage": "https://github.com/sinonjs/fake-timers", "author": {"name": "<PERSON>"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/fake-timers.git"}, "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"lint": "eslint .", "test-node": "mocha --timeout 200 test/ integration-test/ -R dot --check-leaks", "test-headless": "mochify --driver puppeteer", "test-check-coverage": "npm run test-coverage && nyc check-coverage", "test-cloud": "npm run test-edge && npm run test-firefox && npm run test-safari", "test-edge": "BROWSER_NAME=MicrosoftEdge mochify --config mochify.webdriver.js", "test-firefox": "BROWSER_NAME=firefox mochify --config mochify.webdriver.js", "test-safari": "BROWSER_NAME=safari mochify --config mochify.webdriver.js", "test-coverage": "nyc -x mochify.webdriver.js -x coverage --all --reporter text --reporter html --reporter lcovonly npm run test-node", "test": "npm run test-node && npm run test-headless", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "preversion": "./scripts/preversion.sh", "version": "./scripts/version.sh", "postversion": "./scripts/postversion.sh", "prepare": "husky"}, "lint-staged": {"*.{js,css,md}": "prettier --check", "*.js": "eslint"}, "mochify": {"reporter": "dot", "timeout": 10000, "bundle": "esbuild --bundle --sourcemap=inline --define:process.env.NODE_DEBUG=\"\"", "bundle_stdin": "require", "spec": "test/**/*-test.js"}, "devDependencies": {"@mochify/cli": "^0.4.1", "@mochify/driver-puppeteer": "^0.4.0", "@mochify/driver-webdriver": "^0.2.1", "@sinonjs/eslint-config": "^5.0.3", "@sinonjs/referee-sinon": "12.0.0", "esbuild": "^0.23.1", "husky": "^9.1.5", "jsdom": "24.1.1", "lint-staged": "15.2.9", "mocha": "10.7.3", "nyc": "17.0.0", "prettier": "3.3.3"}, "main": "./src/fake-timers-src.js", "dependencies": {"@sinonjs/commons": "^3.0.1"}, "nyc": {"branches": 85, "lines": 92, "functions": 92, "statements": 92, "exclude": ["**/*-test.js", "coverage/**", "types/**", "fake-timers.js"]}, "_id": "@sinonjs/fake-timers@14.0.0", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "White Autumn", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "75191941+<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swenzel-arc", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>o", "email": "<EMAIL>"}], "gitHead": "48f089fdc830e39fcec31dd23099cc360da0bab2", "_nodeVersion": "20.9.0", "_npmVersion": "10.1.0", "dist": {"integrity": "sha512-QfoXRaUTjMVVn/ZbnD4LS3TPtqOkOdKIYCKldIVPnuClcwRKat6LI2mRZ2s5qiBfO6Fy03An35dSls/2/FEc0Q==", "shasum": "54b3daa30da6fcca71b2da934ff96b3095b3e28d", "tarball": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-14.0.0.tgz", "fileCount": 4, "unpackedSize": 97565, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDV91ht82NXlHkolnPym6KuFof6NyH1/BfjRtDNO7SYTAiEA5Wd4TKFEL7tbf3Z1lrvj277ybyZAxQQHnXcRo7Z2FIk="}]}, "_npmUser": {"name": "fatso83", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/fake-timers_14.0.0_1733807992862_0.05550385640517641"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-02-04T11:41:51.250Z", "modified": "2024-12-10T05:19:53.265Z", "6.0.0": "2020-02-04T11:41:51.645Z", "6.0.1": "2020-03-24T11:43:39.704Z", "7.0.0": "2021-01-12T17:51:23.492Z", "7.0.1": "2021-01-14T11:23:02.710Z", "7.0.2": "2021-01-18T16:13:36.090Z", "7.0.4": "2021-04-08T08:12:44.498Z", "7.0.5": "2021-04-11T11:01:09.390Z", "7.1.0": "2021-05-21T13:25:36.096Z", "7.1.1": "2021-05-27T11:48:29.650Z", "7.1.2": "2021-05-28T14:33:03.554Z", "8.0.0": "2021-09-15T11:03:45.853Z", "8.0.1": "2021-09-27T14:20:27.968Z", "8.1.0": "2021-11-03T14:30:06.615Z", "9.0.0": "2022-01-28T12:18:14.585Z", "9.1.0": "2022-02-03T11:30:50.508Z", "9.1.1": "2022-03-05T08:01:35.809Z", "9.1.2": "2022-04-13T08:38:49.942Z", "10.0.0": "2022-11-08T15:15:04.341Z", "10.0.1": "2022-12-15T14:49:20.975Z", "10.0.2": "2022-12-15T14:51:28.400Z", "10.1.0": "2023-05-13T13:15:50.925Z", "10.2.0": "2023-05-18T13:40:04.709Z", "11.0.0": "2023-06-12T13:09:48.885Z", "10.3.0": "2023-06-20T13:53:40.548Z", "11.1.0": "2023-08-17T18:12:17.681Z", "11.2.0": "2023-10-19T07:42:30.864Z", "11.2.1": "2023-10-19T07:46:14.418Z", "11.2.2": "2023-10-20T13:15:59.590Z", "11.3.0": "2024-08-24T07:29:06.648Z", "11.3.1": "2024-08-24T07:52:28.760Z", "12.0.0": "2024-08-24T17:54:50.809Z", "13.0.0": "2024-08-25T18:45:41.732Z", "13.0.1": "2024-08-26T09:43:41.768Z", "13.0.2": "2024-09-13T13:55:56.665Z", "13.0.3": "2024-10-17T18:36:44.976Z", "13.0.4": "2024-10-22T10:49:46.393Z", "13.0.5": "2024-10-29T10:11:41.303Z", "14.0.0": "2024-12-10T05:19:53.045Z"}, "bugs": {"url": "https://github.com/sinonjs/fake-timers/issues"}, "author": {"name": "<PERSON>"}, "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://github.com/sinonjs/fake-timers", "repository": {"type": "git", "url": "git+https://github.com/sinonjs/fake-timers.git"}, "description": "Fake JavaScript timers", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot[bot]", "email": "49699333+dependabot[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "dependabot-preview[bot]", "email": "27856297+dependabot-preview[bot]@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "itay", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "43081j", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON> Blyžė", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Icebob", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Re<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "White Autumn", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "75191941+<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "chris<PERSON><PERSON><PERSON>@hotmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>, Ph.D", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "JadenSimon", "email": "31319484+<PERSON><PERSON>Simo<PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "22315101+niekcan<PERSON><PERSON>@users.noreply.github.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@alumni.maastrichtuniversity.nl"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "griest024", "email": "<EMAIL>"}, {"name": "life777", "email": "<EMAIL>"}, {"name": "mAAdha<PERSON>ah", "email": "james<PERSON><EMAIL>"}, {"name": "medanat", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "swenzel-arc", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>o", "email": "<EMAIL>"}], "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "readme": "# `@sinonjs/fake-timers`\n\n[![codecov](https://codecov.io/gh/sinonjs/fake-timers/branch/main/graph/badge.svg)](https://codecov.io/gh/sinonjs/fake-timers)\n<a href=\"CODE_OF_CONDUCT.md\"><img src=\"https://img.shields.io/badge/Contributor%20Covenant-v2.0%20adopted-ff69b4.svg\" alt=\"Contributor Covenant\" /></a>\n\nJavaScript implementation of the timer\nAPIs; `setTimeout`, `clearTimeout`, `setImmediate`, `clearImmediate`, `setInterval`, `clearInterval`, `requestAnimationFrame`, `cancelAnimationFrame`, `requestIdleCallback`,\nand `cancelIdleCallback`, along with a clock instance that controls the flow of time. FakeTimers also provides a `Date`\nimplementation that gets its time from the clock.\n\nIn addition in browser environment `@sinonjs/fake-timers` provides a `performance` implementation that gets its time\nfrom the clock. In Node environments FakeTimers provides a `nextTick` implementation that is synchronized with the\nclock - and a `process.hrtime` shim that works with the clock.\n\n`@sinonjs/fake-timers` can be used to simulate passing time in automated tests and other\nsituations where you want the scheduling semantics, but don't want to actually\nwait.\n\n`@sinonjs/fake-timers` is extracted from [Sinon.JS](https://github.com/sinonjs/sinon.js) and targets\nthe [same runtimes](https://sinonjs.org/releases/latest/#supported-runtimes).\n\n## Autocomplete, IntelliSense and TypeScript definitions\n\nVersion 7 introduced JSDoc to the codebase. This should provide autocomplete and type suggestions in supporting IDEs. If\nyou need more elaborate type support, TypeScript definitions for the Sinon projects are independently maintained by the\nDefinitely Types community:\n\n```\nnpm install -D @types/sinonjs__fake-timers\n```\n\n## Installation\n\n`@sinonjs/fake-timers` can be used in both Node and browser environments. Installation is as easy as\n\n```sh\nnpm install @sinonjs/fake-timers\n```\n\nIf you want to use `@sinonjs/fake-timers` in a browser you can either build your own bundle or\nuse [Skypack](https://www.skypack.dev).\n\n## Usage\n\nTo use `@sinonjs/fake-timers`, create a new clock, schedule events on it using the timer\nfunctions and pass time using the `tick` method.\n\n```js\n// In the browser distribution, a global `FakeTimers` is already available\nvar FakeTimers = require(\"@sinonjs/fake-timers\");\nvar clock = FakeTimers.createClock();\n\nclock.setTimeout(function () {\n    console.log(\n        \"The poblano is a mild chili pepper originating in the state of Puebla, Mexico.\",\n    );\n}, 15);\n\n// ...\n\nclock.tick(15);\n```\n\nUpon executing the last line, an interesting fact about the\n[Poblano](https://en.wikipedia.org/wiki/Poblano) will be printed synchronously to\nthe screen. If you want to simulate asynchronous behavior, please see the `async` function variants (\neg `clock.tick(time)` vs `await clock.tickAsync(time)`).\n\nThe `next`, `runAll`, `runToFrame`, and `runToLast` methods are available to advance the clock. See the\nAPI Reference for more details.\n\n### Faking the native timers\n\nWhen using `@sinonjs/fake-timers` to test timers, you will most likely want to replace the native\ntimers such that calling `setTimeout` actually schedules a callback with your\nclock instance, not the browser's internals.\n\nCalling `install` with no arguments achieves this. You can call `uninstall`\nlater to restore things as they were again.\nNote that in NodeJS the [timers](https://nodejs.org/api/timers.html)\nand [timers/promises](https://nodejs.org/api/timers.html#timers-promises-api) modules will also receive fake timers when\nusing global scope.\n\n```js\n// In the browser distribution, a global `FakeTimers` is already available\nvar FakeTimers = require(\"@sinonjs/fake-timers\");\n\nvar clock = FakeTimers.install();\n// Equivalent to\n// var clock = FakeTimers.install(typeof global !== \"undefined\" ? global : window);\n\nsetTimeout(fn, 15); // Schedules with clock.setTimeout\n\nclock.uninstall();\n// setTimeout is restored to the native implementation\n```\n\nTo hijack timers in another context pass it to the `install` method.\n\n```js\nvar FakeTimers = require(\"@sinonjs/fake-timers\");\nvar context = {\n    setTimeout: setTimeout, // By default context.setTimeout uses the global setTimeout\n};\nvar clock = FakeTimers.withGlobal(context).install();\n\ncontext.setTimeout(fn, 15); // Schedules with clock.setTimeout\n\nclock.uninstall();\n// context.setTimeout is restored to the original implementation\n```\n\nUsually you want to install the timers onto the global object, so call `install`\nwithout arguments.\n\n#### Automatically incrementing mocked time\n\nFakeTimers supports the possibility to attach the faked timers to any change\nin the real system time. This means that there is no need to `tick()` the\nclock in a situation where you won't know **when** to call `tick()`.\n\nPlease note that this is achieved using the original setImmediate() API at a certain\nconfigurable interval `config.advanceTimeDelta` (default: 20ms). Meaning time would\nbe incremented every 20ms, not in real time.\n\nAn example would be:\n\n```js\nvar FakeTimers = require(\"@sinonjs/fake-timers\");\nvar clock = FakeTimers.install({\n    shouldAdvanceTime: true,\n    advanceTimeDelta: 40,\n});\n\nsetTimeout(() => {\n    console.log(\"this just timed out\"); //executed after 40ms\n}, 30);\n\nsetImmediate(() => {\n    console.log(\"not so immediate\"); //executed after 40ms\n});\n\nsetTimeout(() => {\n    console.log(\"this timed out after\"); //executed after 80ms\n    clock.uninstall();\n}, 50);\n```\n\n## API Reference\n\n### `var clock = FakeTimers.createClock([now[, loopLimit]])`\n\nCreates a clock. The default\n[epoch](https://en.wikipedia.org/wiki/Epoch_%28reference_date%29) is `0`.\n\nThe `now` argument may be a number (in milliseconds) or a Date object.\n\nThe `loopLimit` argument sets the maximum number of timers that will be run when calling `runAll()` before assuming that\nwe have an infinite loop and throwing an error. The default is `1000`.\n\n### `var clock = FakeTimers.install([config])`\n\nInstalls FakeTimers using the specified config (otherwise with epoch `0` on the global scope).\nNote that in NodeJS the [timers](https://nodejs.org/api/timers.html)\nand [timers/promises](https://nodejs.org/api/timers.html#timers-promises-api) modules will also receive fake timers when\nusing global scope.\nThe following configuration options are available\n\n| Parameter                        | Type        | Default                                                                                                                                                                                                                        | Description                                                                                                                                                                                                                                                            |\n| -------------------------------- | ----------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |\n| `config.now`                     | Number/Date | 0                                                                                                                                                                                                                              | installs FakeTimers with the specified unix epoch                                                                                                                                                                                                                      |\n| `config.toFake`                  | String[]    | [\"setTimeout\", \"clearTimeout\", \"setImmediate\", \"clearImmediate\",\"setInterval\", \"clearInterval\", \"Date\", \"requestAnimationFrame\", \"cancelAnimationFrame\", \"requestIdleCallback\", \"cancelIdleCallback\", \"hrtime\", \"performance\"] | an array with explicit function names (or objects, in the case of \"performance\") to hijack. \\_When not set, FakeTimers will automatically fake all methods e.g., `FakeTimers.install({ toFake: [\"setTimeout\",\"nextTick\"]})` will fake only `setTimeout` and `nextTick` |\n| `config.loopLimit`               | Number      | 1000                                                                                                                                                                                                                           | the maximum number of timers that will be run when calling runAll()                                                                                                                                                                                                    |\n| `config.shouldAdvanceTime`       | Boolean     | false                                                                                                                                                                                                                          | tells FakeTimers to increment mocked time automatically based on the real system time shift (e.g. the mocked time will be incremented by 20ms for every 20ms change in the real system time)                                                                           |\n| `config.advanceTimeDelta`        | Number      | 20                                                                                                                                                                                                                             | relevant only when using with `shouldAdvanceTime: true`. increment mocked time by `advanceTimeDelta` ms every `advanceTimeDelta` ms change in the real system time.                                                                                                    |\n| `config.shouldClearNativeTimers` | Boolean     | false                                                                                                                                                                                                                          | tells FakeTimers to clear 'native' (i.e. not fake) timers by delegating to their respective handlers. These are not cleared by default, leading to potentially unexpected behavior if timers existed prior to installing FakeTimers.                                   |\n| `config.ignoreMissingTimers`     | Boolean     | false                                                                                                                                                                                                                          | tells FakeTimers to ignore missing timers that might not exist in the given environment                                                                                                                                                                                |\n\n### `var id = clock.setTimeout(callback, timeout)`\n\nSchedules the callback to be fired once `timeout` milliseconds have ticked by.\n\nIn Node.js `setTimeout` returns a timer object. FakeTimers will do the same, however\nits `ref()` and `unref()` methods have no effect.\n\nIn browsers a timer ID is returned.\n\n### `clock.clearTimeout(id)`\n\nClears the timer given the ID or timer object, as long as it was created using\n`setTimeout`.\n\n### `var id = clock.setInterval(callback, timeout)`\n\nSchedules the callback to be fired every time `timeout` milliseconds have ticked\nby.\n\nIn Node.js `setInterval` returns a timer object. FakeTimers will do the same, however\nits `ref()` and `unref()` methods have no effect.\n\nIn browsers a timer ID is returned.\n\n### `clock.clearInterval(id)`\n\nClears the timer given the ID or timer object, as long as it was created using\n`setInterval`.\n\n### `var id = clock.setImmediate(callback)`\n\nSchedules the callback to be fired once `0` milliseconds have ticked by. Note\nthat you'll still have to call `clock.tick()` for the callback to fire. If\ncalled during a tick the callback won't fire until `1` millisecond has ticked\nby.\n\nIn Node.js `setImmediate` returns a timer object. FakeTimers will do the same,\nhowever its `ref()` and `unref()` methods have no effect.\n\nIn browsers a timer ID is returned.\n\n### `clock.clearImmediate(id)`\n\nClears the timer given the ID or timer object, as long as it was created using\n`setImmediate`.\n\n### `clock.requestAnimationFrame(callback)`\n\nSchedules the callback to be fired on the next animation frame, which runs every\n16 ticks. Returns an `id` which can be used to cancel the callback. This is\navailable in both browser & node environments.\n\n### `clock.cancelAnimationFrame(id)`\n\nCancels the callback scheduled by the provided id.\n\n### `clock.requestIdleCallback(callback[, timeout])`\n\nQueued the callback to be fired during idle periods to perform background and low priority work on the main event loop.\nCallbacks which have a timeout option will be fired no later than time in milliseconds. Returns an `id` which can be\nused to cancel the callback.\n\n### `clock.cancelIdleCallback(id)`\n\nCancels the callback scheduled by the provided id.\n\n### `clock.countTimers()`\n\nReturns the number of waiting timers. This can be used to assert that a test\nfinishes without leaking any timers.\n\n### `clock.hrtime(prevTime?)`\n\nOnly available in Node.js, mimicks process.hrtime().\n\n### `clock.nextTick(callback)`\n\nOnly available in Node.js, mimics `process.nextTick` to enable completely synchronous testing flows.\n\n### `clock.performance.now()`\n\nOnly available in browser environments, mimicks performance.now().\n\n### `clock.tick(time)` / `await clock.tickAsync(time)`\n\nAdvance the clock, firing callbacks if necessary. `time` may be the number of\nmilliseconds to advance the clock by or a human-readable string. Valid string\nformats are `\"08\"` for eight seconds, `\"01:00\"` for one minute and `\"02:34:10\"`\nfor two hours, 34 minutes and ten seconds.\n\nThe `tickAsync()` will also break the event loop, allowing any scheduled promise\ncallbacks to execute _before_ running the timers.\n\n### `clock.next()` / `await clock.nextAsync()`\n\nAdvances the clock to the the moment of the first scheduled timer, firing it.\n\nThe `nextAsync()` will also break the event loop, allowing any scheduled promise\ncallbacks to execute _before_ running the timers.\n\n### `clock.jump(time)`\n\nAdvance the clock by jumping forward in time, firing callbacks at most once.\n`time` takes the same formats as [`clock.tick`](#clockticktime--await-clocktickasynctime).\n\nThis can be used to simulate the JS engine (such as a browser) being put to sleep and resumed later, skipping\nintermediary timers.\n\n### `clock.reset()`\n\nRemoves all timers and ticks without firing them, and sets `now` to `config.now`\nthat was provided to `FakeTimers.install` or to `0` if `config.now` was not provided.\nUseful to reset the state of the clock without having to `uninstall` and `install` it.\n\n### `clock.runAll()` / `await clock.runAllAsync()`\n\nThis runs all pending timers until there are none remaining. If new timers are added while it is executing they will be\nrun as well.\n\nThis makes it easier to run asynchronous tests to completion without worrying about the number of timers they use, or\nthe delays in those timers.\n\nIt runs a maximum of `loopLimit` times after which it assumes there is an infinite loop of timers and throws an error.\n\nThe `runAllAsync()` will also break the event loop, allowing any scheduled promise\ncallbacks to execute _before_ running the timers.\n\n### `clock.runMicrotasks()`\n\nThis runs all pending microtasks scheduled with `nextTick` but none of the timers and is mostly useful for libraries\nusing FakeTimers underneath and for running `nextTick` items without any timers.\n\n### `clock.runToFrame()`\n\nAdvances the clock to the next frame, firing all scheduled animation frame callbacks,\nif any, for that frame as well as any other timers scheduled along the way.\n\n### `clock.runToLast()` / `await clock.runToLastAsync()`\n\nThis takes note of the last scheduled timer when it is run, and advances the\nclock to that time firing callbacks as necessary.\n\nIf new timers are added while it is executing they will be run only if they\nwould occur before this time.\n\nThis is useful when you want to run a test to completion, but the test recursively\nsets timers that would cause `runAll` to trigger an infinite loop warning.\n\nThe `runToLastAsync()` will also break the event loop, allowing any scheduled promise\ncallbacks to execute _before_ running the timers.\n\n### `clock.setSystemTime([now])`\n\nThis simulates a user changing the system clock while your program is running.\nIt affects the current time but it does not in itself cause e.g. timers to fire;\nthey will fire exactly as they would have done without the call to\nsetSystemTime().\n\n### `clock.uninstall()`\n\nRestores the original methods of the native timers or the methods on the object\nthat was passed to `FakeTimers.withGlobal`\n\n### `Date`\n\nImplements the `Date` object but using the clock to provide the correct time.\n\n### `Performance`\n\nImplements the `now` method of the [`Performance`](https://developer.mozilla.org/en-US/docs/Web/API/Performance/now)\nobject but using the clock to provide the correct time. Only available in environments that support the Performance\nobject (browsers mostly).\n\n### `FakeTimers.withGlobal`\n\nIn order to support creating clocks based on separate or sandboxed environments (such as JSDOM), FakeTimers exports a\nfactory method which takes single argument `global`, which it inspects to figure out what to mock and what features to\nsupport. When invoking this function with a global, you will get back an object with `timers`, `createClock`\nand `install` - same as the regular FakeTimers exports only based on the passed in global instead of the global\nenvironment.\n\n## Promises and fake time\n\nIf you use a Promise library like Bluebird, note that you should either call `clock.runMicrotasks()` or make sure to\n_not_ mock `nextTick`.\n\n## Running tests\n\nFakeTimers has a comprehensive test suite. If you're thinking of contributing bug\nfixes or suggesting new features, you need to make sure you have not broken any\ntests. You are also expected to add tests for any new behavior.\n\n### On node:\n\n```sh\nnpm test\n```\n\nOr, if you prefer more verbose output:\n\n```\n$(npm bin)/mocha ./test/fake-timers-test.js\n```\n\n### In the browser\n\n[Mochify](https://github.com/mochify-js) is used to run the tests in headless\nChrome.\n\n```sh\nnpm test-headless\n```\n\n## License\n\nBSD 3-clause \"New\" or \"Revised\" License (see LICENSE file)\n", "readmeFilename": "README.md", "users": {"mcalavera81": true}}