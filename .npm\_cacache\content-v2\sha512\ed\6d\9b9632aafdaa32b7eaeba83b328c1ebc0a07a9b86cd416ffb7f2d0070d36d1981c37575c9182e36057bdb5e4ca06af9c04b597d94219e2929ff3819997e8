{"_id": "@babel/plugin-syntax-optional-chaining", "_rev": "69-fd96e6918aec101f7a6c754ecad87cd1", "name": "@babel/plugin-syntax-optional-chaining", "description": "Allow parsing of optional properties", "dist-tags": {"latest": "7.8.3"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.4", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.4", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-dv2XX8gY8eMcQJAitQcMWQbgfqNeTYNWLCfKuNEAZc01CdUEKeno8BOjoZHO0svEkDHhkIVTMO/sTvUomYxkOQ==", "shasum": "5a6d0aa443f679f25cc0fa4f44256155c25e398f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.4.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCpJQsOxjNi463ZrWmfeq/ZCJ1ordYVGf7UmPyHJsgJSQIhANf8eKjw067K3oXQFFQXLJGQxzoz1izozZP85+/iER7N"}]}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining-7.0.0-beta.4.tgz_1509388465427_0.8521463833749294"}, "directories": {}}, "7.0.0-beta.5": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.5", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.5", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-yTQtlOUwCWsMi1rWbM8EnVaxVI6F2GvZP8++akkSGYu2lfUtzCAByhH9MCQsMlKjf6Lk+wOqH/7rkMyB7ApXPA==", "shasum": "1f378c29bcf73fc1d8392a85d686f06b1889c366", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.5.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEMCHzQXLfH9X6F8b6HpYxwLmO7jEPtzVN8mEZQGFyMODJACIFo6GAFje4Myeebuuqp0FfH4Q2hYGhPMyEDHs7gH0WE5"}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining-7.0.0-beta.5.tgz_1509396967415_0.09705364261753857"}, "directories": {}}, "7.0.0-beta.31": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.31", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.31", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-tGiFPQbNNSb1u205t5hQXAgi1mWO949QUeJZ8ZYfvi7uxeXThS+QCl/LAZCboKSQ03NWwGmR0/FVSog7yWAdZg==", "shasum": "cd28c5ffc45fc95c39a6f45a9c490b6067e8d0c8", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.31.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDY9VYvcgmlwVY5v+tQM/rZIoQF3Z4zXHz5SqdC2OXwxAiB1hpNWVvkI4VZW3risNWWS8F1VWn8VHKlhCS7t10VaMw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining-7.0.0-beta.31.tgz_1509739398098_0.9687959102448076"}, "directories": {}}, "7.0.0-beta.32": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.32", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.32", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-fvIxOk9sPj+OJVDDmY+PGEIalNTowvhQ+ftDqhJ/RgPeYlgt0wqv7GVKlBFgUGMj1HKxrHsExYEX8giHGrGPVw==", "shasum": "766b7578e582f366a8c6e7a9e84da760f48cb30c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.32.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG93pJ52m471NICqJ/t4L0uBAtvCkJqV5vTLG+6KrnWJAiBa0N7/inXTmIbcUsgc4PQysiL9A/Dsj+MusJmVibDHfg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining-7.0.0-beta.32.tgz_1510493586086_0.1896652728319168"}, "directories": {}}, "7.0.0-beta.33": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.33", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.33", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-RJbJO+2Dn/U22ccDBbpuwPseB7tV3XSceGWn96Okd4kO/Q2IYmN52jL2Pydasmwvb+pCy2r0TwpNm1ajK40WYg==", "shasum": "6986c958276e7e2dde570a9dca118cec8e0d3404", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.33.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDKBI+McgyZNHoVy54tswQ4K+U1fBDCdJWxF/3q4lQFBAIhAMZ4lBuJTWISpnFbVFZO2XSAVTOngWa/41JBGlFmNaoI"}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining-7.0.0-beta.33.tgz_1512138489425_0.3680225603748113"}, "directories": {}}, "7.0.0-beta.34": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.34", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.34", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-UqJawxVv4TC/02LyFmJMC5f9pLjre1K0prKY4fB3W5f8HUYSdXmqiAVAGbAoCO3MuvfqidUjBCxmfeAJklX6KA==", "shasum": "9fab733887cf2c8e4edc2218ffcdca6a2ab843f2", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.34.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2uem3PQtcEUmuShjXnF3X7LHj5W9JdNRJgzCiR2FmBwIgSST3AfX7yeMAIOM5E43qhfPigk0gXuwgc95i7/zePjo="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining-7.0.0-beta.34.tgz_1512225549580_0.33037123433314264"}, "directories": {}}, "7.0.0-beta.35": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.35", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.35", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-bQ8XiwREPZLrE2JbJggaIApGV8udovzPFNZ2Cd8iLns+bZqR169TbqNOTg1g+DtpUZ/95eDYoIZ+k+FUs40hKA==", "shasum": "24b0ec03f41faaf4081fdcbafc4fdb20f54a9d5f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.35.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCSda++VcG6WGaN8cfJ8HmV20m8F0EGaSzyXlOWcmVgDgIgFDDKTLzoRuhyU/Vzm9HMihLVvG4yuq6p4e0tIC9iXo0="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining-7.0.0-beta.35.tgz_1513288057775_0.09760610549710691"}, "directories": {}}, "7.0.0-beta.36": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.36", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.36", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-I1mUCEzMwPL02baXizZNNDPQ+KKkmhFrEX/f4w5HnudvrzwWvpTxBmWt7e89KA8r+Nd/RHfIpCajz312jwIkjQ==", "shasum": "c1f7704684f05e4b728c1aaa45f44dd344af71a7", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.36.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDY0+9gzr3+yZyGya5Q8wJd+KYB9ZEffuOGdiMl3xPb/gIhAJLTLOqrcXbvV7onbybRWJJLY0FbWjr7DFjxsT4g6rc6"}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "kentcdodds"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "not-an-a<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "mysticatea"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining-7.0.0-beta.36.tgz_1514228666859_0.8948769015260041"}, "directories": {}}, "7.0.0-beta.37": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.37", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.37", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-JdU0v0uXgXDkwyGJ7d4ay4+yL+ImS6ZaeU8f6Nbx1VKduIbW6KyaqIg4Le90yH2TydUrr5tGc+9rj1eb0hCJLA==", "shasum": "66a3ee225977423b01719ac5404d7fae84b4104e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.37.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDLAY4yWhJGBpB4JCsAwNe9I2O0hDbXBOcbL2zqPloLYwIhAK5W2zQ7kmuzZiK9istTuEMAbg7kBA2T/2SbZs+N7Isr"}]}, "maintainers": [{"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining-7.0.0-beta.37.tgz_1515427344757_0.3211245352867991"}, "directories": {}}, "7.0.0-beta.38": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.38", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.38", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ou452cDrsUPwHFKIsHygcwzCkP/YdlVi434YN0LHZV21omadI1l5i5xO+5zTrE6xXnvCW3X4ODWUXMdiq426cg==", "shasum": "70d26cd1f4a2645984ffdce967c17a342f3286b2", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.38.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDX8/2SMOB2m2i25hmrxUxjWYQl14C3pMmaO594TxrkRgIgKHGoMj/clE0+Wfew3C7DQT92T+3yuKjngyRJW34JJZ4="}]}, "maintainers": [{"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining-7.0.0-beta.38.tgz_1516206706185_0.7500229084398597"}, "directories": {}}, "7.0.0-beta.39": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.39", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.39", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-cA/6EITC5+hrRqYEpYCZojTpeJu18Cs4ztCrv5WmshAKbLZZ1WVQPD3D/ubl8WdL/wVXmS9h9v6yR9XFl9pVbw==", "shasum": "28c593e8921000c3f8ed749cdcab55b08824f988", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.39.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAb8+CWiQy29d4c1ekOpPWskB8zya1ZIcWPV7gt5UroTAiAx/GK6vBlqhRqicw+Ibb9gB+i32BCRtmgxLnQ47J7DBA=="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining-7.0.0-beta.39.tgz_1517344048700_0.02275737700983882"}, "directories": {}}, "7.0.0-beta.40": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.40", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "devDependencies": {"@babel/core": "7.0.0-beta.40"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.40", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-LJktjt101/yePLOWy1HSrdIMBqFOY5er6JDWJynKl+mKzYD549zEZcn2oAhaAmv0iu+BXTC6flm6EEmYUK+3Fw==", "shasum": "0a2628511e8f7f35666f5977fb07e1a8d1d363eb", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.40.tgz", "fileCount": 3, "unpackedSize": 1229, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFP1k/vJGIQqBSu1UqO701dCHYkl1kB5MlzGjRz0oxXVAiAg3B5OVrlC30rlqlqIzr93EQPHmoaJqa5pvOvy8+hQFg=="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0-beta.40_1518453686756_0.6571838054809158"}, "_hasShrinkwrap": false}, "7.0.0-beta.41": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.41", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "devDependencies": {"@babel/core": "7.0.0-beta.41"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.41", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-iaJ4JZimEg2EyWdXFVgJ55f/F08ioeQPoElZ7NvNLxHx3idSDPsyB/S5qKOVDY1JjMiXKT0GZBwInjorOLgY+Q==", "shasum": "4db9b5c84140dfb8bfcbe6ec744161cd5d2ae465", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.41.tgz", "fileCount": 3, "unpackedSize": 1464, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHRYZORD0VTYRdBoqisDHmwxHfNXYkDZETGpihLpMoSaAiEA50xBNGM9rswgA3g2//DkjR9KLUsazkHBqeN9bU20UiI="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0-beta.41_1521044758484_0.06836406406731799"}, "_hasShrinkwrap": false}, "7.0.0-beta.42": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.42", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "devDependencies": {"@babel/core": "7.0.0-beta.42"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.42", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Bz0aix2BkgEIQ+9+Qb7Y6FoapXiGZ8w1Zn9mYaTTnxwODRrvYeH+cARzHWsXEf0hv3SI5ONaZXnDvwtsxQyxXQ==", "shasum": "2c4e2ffc276e8e22577c68cea6a8ca472c147802", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.42.tgz", "fileCount": 3, "unpackedSize": 1464, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDNbf70vkNyZoLY01cCz51T6iom3TDV2Jf/M9H7TP24egIgAnRsMi2XBMzc2Qfu8ruqKdRG4KhyWqdY+K7/4AgqPnQ="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0-beta.42_1521147035077_0.4070084434286363"}, "_hasShrinkwrap": false}, "7.0.0-beta.43": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.43", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "devDependencies": {"@babel/core": "7.0.0-beta.43"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.43", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-8STQeiBHbGGBfm+CExEy+qAi6Oh+jHsOW2vVU9An/bO7S1g8gQtPWn9hW8kakSpCiAfdUBVdM6Fl78NKgyEFhw==", "shasum": "3c806300785c5938884c3db12a5eebfa7ddebe72", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.43.tgz", "fileCount": 3, "unpackedSize": 1569, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDzEY4GL4iZicG77QZ/zAwSAegHm0ktYeygPPGME3Wq5QIhAMGeEvYeuquKXCVc74Xc90eOj4MSDaJxNvWJbEqMM8BK"}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0-beta.43_1522687698991_0.49615710720809925"}, "_hasShrinkwrap": false}, "7.0.0-beta.44": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.44", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "devDependencies": {"@babel/core": "7.0.0-beta.44"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.44", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-81KCYLImcO1FfLu0dWbJLn1pg7fXQJHh2AE5Mbz3NdWjb2AMCbnYIHY01HGn/3mydhPHZmqlRrrAkOu00lRQUw==", "shasum": "3e41156dc1cc94b1b6cc21cfb96e50dda4ad9986", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.44.tgz", "fileCount": 3, "unpackedSize": 1620, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC2WQd5XF1beNu4/KTcfjQvyEq0ppxavmO3gjBQUPcjywIgGFdAFbmOQoZHx1pZb+LK6nVkd0iL6C1UCtK0seZgXkY="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0-beta.44_1522707601172_0.6804336440027963"}, "_hasShrinkwrap": false}, "7.0.0-beta.45": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.45", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "devDependencies": {"@babel/core": "7.0.0-beta.45"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.45", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-dxU+PsHEg+MaUmbUam/9fkkvng78aFoaDJsKVtjudN9ql2uJ8nNYIjxkgvMNqKMJtwYOvfePZh0xjs82yiVJ9g==", "shasum": "a5310569f36b64a70132115ed6afc5633459259e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.45.tgz", "fileCount": 3, "unpackedSize": 1620, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1LCRA9TVsSAnZWagAAIukP/32KtpSLh1KPv9CYq3q1\nxWSjun8I02ZnAEhHM528BQzE6PMo7tjv1mpQ5C2ZHvp4mEVvhzoaH8BwGfn4\nuwFTMaP/Xg0awj5NPds6hWgLKK+MscfEJm5i9vUTQJWL5NhM2ZMKJhjQFvvQ\nHgyTIkp/FPXtXud2QBGDL10k+3LTMJn7Ea0LM20LGwALozNK8g6+XqamxhTm\naizusgjSVCeX0k9IDTUd3dDsoiCLB/TbR18FZ11VHbHpyrdwfAznoaz0Rlb7\nOBUxahv9VEu8uZn3DO4TuBmTz1WuYkLrVQuZ936+ugwVRlLaJgDAtge5qzA0\nICZ0SNcmsjCYs5KY3Hlpt3T/8FwAnjoTcoeo1u6BFWIwM9g7j40hscNgeT05\nnj5Ql8NMEe+PLu5AaooT87pJjYzL7SAWGcaMH5/a2dGdhqO1LIoBk9B+4tcs\nPzWwWUWHXQmpRA828zNI6p10eMqPUzbs9QSsXCPjxiPeHJwWVj9EX0jMbYMn\nP4+nNvRgsEJ2XDd+PJTlAzgZAogoRUoXS5gZP7INeLnh/S5llnPx1TWCcu6n\nyuXBpiAr1mMfmN4G3+Tmhaxd42kHJmJZ8ZgGSJnWMOyml5HBzmsapTbURL+h\n3QkqqjHf0336Y7TLgCVuT29Ian9Asqwxe6JHPvuVkU6+RMHMlGnaDDP9YJbL\nAH+p\r\n=w+C9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH2X8nivhVY/p1uPsBXBKwFt/UhZBMdTLAxhae7RjKwXAiEAx0qrvfO6A0UbwPd+iwPXkiABRuilQJrgmSaIRvTzIjY="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0-beta.45_1524448587453_0.786687695077025"}, "_hasShrinkwrap": false}, "7.0.0-beta.46": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.46", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "devDependencies": {"@babel/core": "7.0.0-beta.46"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.46", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-/TTGzn3twKp7PhFi0ZKA0M+29pm+L3KeUygZZ9FupD4f5i3GNeW/HBBivYcIdZz+Wa1AoUffYtLJSVdpshFKkw==", "shasum": "77c51b180a9895d701b398441d94815c56946a75", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.46.tgz", "fileCount": 3, "unpackedSize": 1620, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WF9CRA9TVsSAnZWagAAjqQP/j96H2iHH2mS7+2pAihI\nzcgDvU5h6rnLP2KjzbbD4FGI9WI34RhZcJUy5i/NE7d7REuUXIV6qWy+jmUq\nW2KgjncR1eHlabz4WrFGefRETegnBPOm+UlGnd0U2Yp4pPDMCi7OAihsEt/m\nEX8oDNdypWxlIVmWCPLfkvJt2nNmqWuN9lfLbS9IgGKWMO3qIRQuLaTZQE3K\nODAeaJQkRWKCZnrr/eIS/MqYMmTtFTm8r6rPApjBB8cP8wvqsxLy8otA6AAT\n7vLzZGQBAF3rp49Xn9C6dAZXFnYjft1pY3+H/GWrlXp/1XnPe18HMZ2Th5ix\naTn6NQjU3Mv3JOGhur1kJ4cUKBqgbrEOU+6r62v0uPLt85ZGGZcsRBj5E/Ot\n2SmX7dYGVPYK1jwrg1ZNp8xmlAJkAtRGar88WfvNwUVtVlr6b3iCJnsEpDky\nPTLntUkL0gtolruOgjf1fWPwrbPYdf+f600TCy23fNbpQZCDJ9Nf7nNydPK6\nwU54P0tcT7FiBqxIXEv6eV4QcuaNCPlBt2547pvJbeqlt/+7g7sElSBSjVZe\nOm/K9fITlnO1ONhAj5a2/Z30+3lBUObeyFkTQUnKTGClM4X27/I6X788b3Z/\nS2ns6yhlwvq3spCk/YSQ9ZflqEGGrqOqOSgxzy5jboo7UYDgewlVGuxA/j5d\nJAnO\r\n=twW3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGiOAXgjgy3tk1IRWm+y6ehNCCC+J7EvFzntwuJbJ0YWAiEAn6qoH+gCBR5ytTWIwN4v7qwjuYJh79LTdlu3R1Ll+Fs="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0-beta.46_1524457853198_0.11035322927853475"}, "_hasShrinkwrap": false}, "7.0.0-beta.47": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.47", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "devDependencies": {"@babel/core": "7.0.0-beta.47"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.47", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-lt6JV/D7QeAEf3qqUT4JTPkbU6vNCfeMW7BB7JD+HYivITkmXuGIVl7w4JrRB9LkfjkYE5vgiz3Nc733AD7v8w==", "shasum": "f1febe859d9dde26f2b2e1f20cf679925d1fab23", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.47.tgz", "fileCount": 3, "unpackedSize": 1587, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iUBCRA9TVsSAnZWagAA6lEQAJhYJvzUKxPcoQl0kpCM\netki2qs+BwXjMEGewYWMZcD6UZZpm/37wxuDnHvF32o8dV/G0Jifw28G2Fx1\nWv5rVKmfUEuoi/DjCn2WqbuIEZO4w+WbGxQpZ8rW+xvfY2hErgOsWgyWhfL8\nf7bfAn917MtOP4BBeECfdynV5zKMDu4ex3iZF7f2msJ8kGhWDkPtJp4861pg\nDa7DMEzTf249diJSH9AcM76dgNWTVRqeTCep3kw04GTmTvrZJPzBlaBKtrSJ\nXYELDGZL81tsDRNC+i6Vxh7irYx+tUMpkrVi+fFMxgn71kTJEhU/wZyDWy/T\nEylM+GuV94PMXVKGbFYB07Ozm98q9DwmqacomnK1oeE8Qb3rq4KP9UzUiJkS\nHXOMOCyHg0JfCsc6ZcwthPYy9y1HO/8UKv94/IdB8tkjY3uTaR45sdMAsdRd\nOKcFZpJUcXv8ZS1UY/4+6ybU8ZJm3n2TTrJUSvDM6AiHygs2p38Ka59rYacp\nc/bGo7tqKlOk1Z9c6u3C2wdqkh7IRPCz5ywfN/D5IoA5ZykQnoNLq29EHNMa\nw4EHNp9fzVV305bbaU/kTNzYtuouLqYChX/Gu8yzxdfal741e/wqVxDEUJBT\na1NJOJf8OaxH1L3+LDjFHUsCL1ZCJ1Jzmh8OIfY74BeOCKiHnyNM24TVAexH\nIUSS\r\n=rSbJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCd5AcFPFsHdiVk38kY3VmVekoAhz8RkwlFGxpmdRs+lwIhAMYZxDWojoEhXJcXhJ9f1EiQfEmUJa1hL7j+hdeukk0a"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0-beta.47_1526342912672_0.8430295861753037"}, "_hasShrinkwrap": false}, "7.0.0-beta.48": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.48", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "devDependencies": {"@babel/core": "7.0.0-beta.48"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.48", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ZtZ7lb06u0zfG8yPo2CfAznn1w5xEXHm0fyaTo4R5mAx6xTofrZpSBxfiKxmM9p2mIwY0QPSAYORwNtbsojrng==", "shasum": "a1460e57201f8ed1444e62a854bf1f5b2a236178", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.48.tgz", "fileCount": 3, "unpackedSize": 1569, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxDUCRA9TVsSAnZWagAAlCMP/A30xLjXSQlSNDYBHbG8\nG5UOeb2tTfPfquTcof6nCWEOMUYhBp28BPK/5r3/lYZyJY44Plgg0mXmxQDx\ni54X4SiMGF3Zyj7JcIljSMf4X6ubJg149PbSQT+rTpDDQ/8uAZR51/9S4ra6\ngHkoD46oiZxBtuDpY3anUdZfGuHO2Emc5XlAXzy/jmIA4DHPqz8Vr97QKlcW\n9fKWsBfbyRymrM41u7Hny1/Feen5133vaPC+soteSKByTY5psVlJoX2/2yh6\n7o2kdgym0ZlN2SVv8ujIl+BH0zL54UYzIgt+5FvljtlT9gTpbtTaWCITCAcG\njmjyvbtEuqzUQTAbTDZCLZzf3WBE+5FsNXTH7ur9dLSrgdMZbY/baNz0NREz\n/s8LApfyrY325JuVv34Nte4FRblv1L4QNi51GxF9GzHI1ZkANUgA4qRm8Ecz\njZiSNT1rGt1iXjHJ+vJ3rb3VmAV6Zi1jOw9HI+wA2ye3cf5tCIXxm5Y/Ygua\nWVE46bUXydJgEj06BwJjhhPf/MQixgNmqziyeicz9TEe/8gZPB6IjgcKZmC7\n2j3LUMlrceAoCklt9IU6WtNwRrbN8vZfvayEGyDI2WkJjolnRPSfjFQ/RP+O\nYeyWSksLQnEFbQ2y3lzZbuMdqz3f/8amQ7YX/NJzZv9tZrGckrHv4Dp3qDe3\nFctG\r\n=VLUV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFtAGETIwdRAvJHPfPcz2rSl+3B5Lp6xJo1x5ZbMtGKcAiEA+qpHZYnu+DTEEuTlFLFK/XUKnJr2JHYFuezfNyIpkkk="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0-beta.48_1527189716359_0.09329737052742737"}, "_hasShrinkwrap": false}, "7.0.0-beta.49": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.49", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.49"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.49", "scripts": {}, "_shasum": "007b5e7ef639b96d58e763bb5c8da1449844b976", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.12.3", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "007b5e7ef639b96d58e763bb5c8da1449844b976", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.49.tgz", "fileCount": 4, "unpackedSize": 1584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDNhCRA9TVsSAnZWagAAtGwQAIVqspwdjh7qhTHtdzYI\nC1x1gvKOG7CYDi+KKkP4w2pqtbxcQ3g4dm+vLODrTTg1dgc26jOihNeCwauj\nJjbtDh3iFmXqcRlHvw1ulCjDi7JFSSyxw50uY6uH5rYMK8hyk4OwiJfd/scz\n6iEJYhF83h/JY72zaBM3geax8+DjoKcQlIrfT+zLFBCBDjCd2I00iIs1ODY3\nEKTCpERrmiytPIGk4Xl4fDJQFkFdS5FY5apqpsKSTMRd//PP8hjEjIE6PhD3\nK7NuDwo0hk1nZQWD8VTwhQO86kFpvMp8Mmog9H6cWDsIeD6D6G/Zowg2QHf8\nzlfXf3Cvvp8Gp3FU9oPaOMu8LukD1afdJHo1nWpQwWljAHyXJEpyLRhKRnB8\neRgR+MZFzTjZUp/f0FRe22GNS4Pgj+Gfv/rs4JddjbyoVWNuiwoG07q01jMH\ntY8rzrZMv2gTdRpPqkCAwNi3UKzNAa9suy0REljTcIKnJ5QgSB5cMg5l/ho2\n8ViuxrnaiKrso7z5fV901ceqHEmoHEy5G586l0v8uztkwOaD/v1yKJ5n6tXA\n+D1OZv+hMkT+5PWt5K7+MkjdPrt/euF8PIBBwtsdNwdd2wLcDhA/by2/Ti8D\nX2pKwuq+/9dlFTGK5NZoCWLj+s6A2Y9N9hrOmzrTt16grBsA8+jcUnzipFSa\nwUxB\r\n=qe5h\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-M5idnzgpT0pHxd7Iq8W37Ogwds2tJWBm90FaIZJHIuiQpGlBmVu7u80nqg7DRC8fcvdTP8flAsjbGnWRSbk97A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDoKVreCXOQ8yEFGIw/XCRyUAqDcNRvsV+Ta0OvQNf7aAiBX9jg1UEU0hNS1hGZaPXOLVMmFDoOh7WbznDSyl9ypfQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0-beta.49_1527264097280_0.22546319800239378"}, "_hasShrinkwrap": false}, "7.0.0-beta.50": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.50", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.50"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.50", "dist": {"shasum": "4d8fd9758cbff8628f3055f13be23e00ee6eb2d2", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.50.tgz", "fileCount": 5, "unpackedSize": 1457, "integrity": "sha512-jkGHcIRFXEYRGnb4vmai1LLC4WwOQ+BRhkHKazMqhOzXFQBHwzHBv7sr8gETBe0DFhHCWX4SetaLUq5NBiEPlQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID3wFWwTpnodbYOaba9xo/SE4yEKpi4xfvafi44K7fGXAiEAibV2vsxrYIiwJHsNmYH/BeqegeDpvA86m4jPeSf86wg="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0-beta.50_1528832831533_0.2534773450972698"}, "_hasShrinkwrap": false}, "7.0.0-beta.51": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.51", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.51"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.51", "dist": {"shasum": "51022b28dd19bd5e4e33196e1f1124cfcd2d4b6c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.51.tgz", "fileCount": 5, "unpackedSize": 1471, "integrity": "sha512-pQKzQYXVeXn5XGvm0nEneqEwVNQkyEofP00KsJr9OfqkoLaHsLuRA84WIU+Y07OEIWEciclu0XEbuErsLPo+cg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGNReZRbiuPWUFe+zc3VsMAdjzXK9djajxu1nz68WFO5AiAe+IbQB0gd9MKUCp6H1zbKu+4Gefvu2eZPSNYk8aOWaw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0-beta.51_1528838382306_0.49368021425234465"}, "_hasShrinkwrap": false}, "7.0.0-beta.52": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.52", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.52"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.52", "dist": {"shasum": "75a69988519ac6c6b2f699e7dd9fa5c3619e46c6", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.52.tgz", "fileCount": 5, "unpackedSize": 1470, "integrity": "sha512-ZVEoLSVDKTSEjAw54zv6m2RUARyZn2VZdWktcvqJTTuCnVE81JP+a2iNHaEEMtEwT2ubGsZP1RHON7zYnEcRlA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGK92y5UD+PFhSfC4dnnkPFwu+zlkMeyafHnDCXFHwqnAiEAtS7ycSeuYSkVxb5g/vSAQusSt5c+H3Q7pVN5ddlGkDg="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0-beta.52_1530838761404_0.2597232991009637"}, "_hasShrinkwrap": false}, "7.0.0-beta.53": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.53", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.53"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.53", "dist": {"shasum": "c061c833ba1cde9bed09d6207269cbd53cc7e05c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.53.tgz", "fileCount": 5, "unpackedSize": 1470, "integrity": "sha512-rIw8JELlyXpp45f22hsyBMEKT1EELyr+vvULdh40ZAHlPmFGaM3ucUkKjmolZcagWFDGt+xWn9X6SIv5i16rMQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCoxXcNFb6RT2ghyfSkCwwyeJWsX7pOXbHR8X6P4G+53wIgG3NoOBkgWzid00yZdvLHKn6zYeCtbO+MkC4eu12lCdg="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0-beta.53_1531316411324_0.8100770527044945"}, "_hasShrinkwrap": false}, "7.0.0-beta.54": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.54", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.54"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.54", "dist": {"shasum": "40dc10c00d9acd523c6ac1f9332652b1e5af4f20", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.54.tgz", "fileCount": 5, "unpackedSize": 1470, "integrity": "sha512-KS6oss69+4hnM02TTXq+ThrLx6zspl7edvIvltQNl0K6BqVyxMm5eOcjfxIp5JfWMkmMfzDnrhfdnhNcQiYZOw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMOCeRaQ2venot+/8l1OVnUe2LgOL3m8nk0ipWiaIljgIhALQ4gGdZOzNmtVVADcmeUBwUmyld4ZiNPvwq01QXcrlB"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0-beta.54_1531764002189_0.8882478474206514"}, "_hasShrinkwrap": false}, "7.0.0-beta.55": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.55", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.55"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.55", "dist": {"shasum": "8e6027315419617dd50b7cbbba917d516cd66772", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.55.tgz", "fileCount": 5, "unpackedSize": 1470, "integrity": "sha512-gk2lHzR3MIjGOb5KNNFTi43owqBpOafR6vm7wZQaudK0yjS2EVFEHV1257EFOOxcFSStzXuAPxZiPwNcXyf2kQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD8MrYY0sYuoVO4C+6DQJzHXtAdl09XvzGnotWsPc4vRAIgAkvYt6xailovAb1FRn0GJHlTMQsF6ucPGqIlojIKWkY="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0-beta.55_1532815628563_0.5885344085344282"}, "_hasShrinkwrap": false}, "7.0.0-beta.56": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-beta.56", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.56"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-beta.56", "dist": {"shasum": "233eaff8ea1b2d57dcdb5e66ffc7b133734c1e9b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-beta.56.tgz", "integrity": "sha512-vFGHKxH68oReSlepPP9ek/nauuDgD1dq+Tnpu7IzBJSH3nz2lozusHGrbxANtXTg6NslD4y8NGuEU1yoq3+DEw==", "fileCount": 5, "unpackedSize": 1470, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPu0CRA9TVsSAnZWagAApVgP+QE4IbSwNUHUSwIt8UxU\nZ9H3BUcsFjH5Js5RGmsrRhdZVU1rZWUdaPppXumXLpdHUaRHEe7F6Nmvvz7C\n3r9OH/l8PqiOc0I76RvoHMkB/r19sRb2e3FYUZ/AR+N4jsiULKrFZ7tOHtM3\nJzhKh09v5OMdpvU5y1AAbLj6MTDZPia+AkBhC5/Glc69zJpnzoDPzjfLoEFC\nrMrRNKrQBvnawRDtmy5nHKFbF6ygvfQbjjup+WHCCghfwrYnE0XBpV5V3gxQ\no+THgshoxOy66k1zyvUksVoN7hoaXdrfBDXKYt7DlUgR5P3dPVQ/oVIAEG1M\nBTKn2UuaCw7tpUCY875ihrxOzA0uZvITCoKell/Py3xgrHA9+v+2I130YttS\nFJse43b59CmYkvNhreFRzwGt0m79TKUFbMzPXKxxwPK8LBCP5PQ6iIPr0w8c\nhER+6otKJeBizAQlCVZYcFpEP60iG30X9xRYoX0hdRF05dRLMPa9bMBpa/Ur\n06pnfSs9VLmxHOcLVFtvLDNJBhoa4erfXQbaNAi3NsazE0S09rJjy1S7/btk\njJs31+GWRcS8oamB/u/hszSydSe7yHolby+wwb4DiFc1Ohyfy6k2FUViAjNf\nmr8Z4H0oCXJAZIetjR5Bwb0JFIGleIwYLEvDDGRydYmbWOWiLTQEi22gH1Kx\naONh\r\n=0Cos\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC7N2RlUr1cF04S9wn1q4FFUAWZvihDDdzSxv1NKUUu2wIhAILDKSXJNqYyU4H1NV5QPtpemaZV8pv39x6kF264VQYk"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0-beta.56_1533344692016_0.19575234085432913"}, "_hasShrinkwrap": false}, "7.0.0-rc.0": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-rc.0", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-rc.0"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-rc.0", "dist": {"shasum": "f7a124258bc79b016000279794f88d2ea06a657d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-rc.0.tgz", "integrity": "sha512-LCD3LnOq1keiUtJu6+d751f1WG1mLZ94DtC/rIesQgKiBWHb9EXPWpN/FJvStThhgOdgfyKgDWNBy2HQOG6SqA==", "fileCount": 5, "unpackedSize": 1461, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGSACRA9TVsSAnZWagAASHAP/2hwRR0RidSfp/ispk4a\nuepPFcsQprDrIE0fiT/+MFZu2ob4NNMootvzex+8jctZDL6rOfNXvwg052xY\ngejkrxvZEPyr0cW9ZSBxyUWMHzG+twUcHliSNVF75f3niiSnHYI951+bkFiE\nC1WTTyubqmy/a+AgG9ZfQ4Ztf3bLR6Tr0EBGBum5hbQbEBUfsEA5Pt8jNyce\niH3X7igj3abhSGbPnRzD+8xA4r6LDCuy6FS9optX5S7TtaJd5yV3uTR5lSya\nNit3MfivNTObTapDbzv/KRbX40vNbJtalDLCm0NwOn+eRoGRVecRx+7mFLsb\n9/aCHu1CE9Z5vCCJTRlW8lZsdDf60Y4z1zql+RoWKlhSvU4emsL8QGvWr+Ij\nR63kgFgvv6ZGQp2mzlXGhhjitQKNaB1KrxOzK9yy42HmoWxj4bVLuLNQi4dl\nEwpk/pi1nNP3gcVO+0yrLBOiSMujOyy58zvJAlJpb1Cv9O59azYSfX3FtBK+\nLq5pbC4JhavSKwAsosG2I0fI6z0xxhcB0DNJ2BZ0jN1Aozc4wpEnTr12Nl41\nC3l476PQnPZEvF8wQLkgsGN9ZaibuvYyrtTsH35ctXkxV/nawuh/kC9a7SwQ\nZLwIMcAY/lrUfuLk3E6tlo5+Nt2b7ZzpvrYSeoylhEzZkpdt8AHxQCymwxTr\nrfUY\r\n=UyGy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDZGMVesaX6UXwkjPQ81gYvbbUuH+KQr8xw98rvf/5xcQIgfjLVgKsW2pG9jJ6sf+3iGIczr0J/KKFoCVvlt3Pw1qk="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0-rc.0_1533830271704_0.6639977386653928"}, "_hasShrinkwrap": false}, "7.0.0-rc.1": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-rc.1", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.1"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-rc.1", "dist": {"shasum": "897d7896dd106dbc84f222766f9cab0a7ae5fc35", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-rc.1.tgz", "integrity": "sha512-H/K9M9CMq+XtbZbJU14Fy8JzGAXXX5hG8fU9OjWFPPguLY4z6Ir/uEgSG8Zbidk6zdkK6FpayBOYav8MaTPOgg==", "fileCount": 5, "unpackedSize": 1442, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ8ICRA9TVsSAnZWagAAUUQP/AyahkGrjpePVs8BiAwj\nevIaAXcalbCUoxXQ0agjRNRK1n+hULMeTIyL5DXK1Xsq9CSvZ+osWTY5cg/O\nhmU/Qg6rp8O4HU5SjaBhzk2rbHjI23gh+HqtpUEm3MXaA1LCG2cAT8Q0RjdQ\nRVMna/948M7ZVl+20+TQFxXDzUijobGuaQO2gLPdLjkyUuij93Isuz8cwLaS\nyoK93u3d0eTWS579F80QQW9waWO40T4iWelfqxGnJ9XCyYhnd3HrGuP47PKV\n23TE0cyJhtzegEzkNvDMKbC8UZKi8wX6Mal+AfP85KGeOe2SHxdxXP+iBQa3\nWdsbGCr8F1DQ/wwXenruk0gPbXwVNGAX7i/da5z+p91DxIkm0t6XQFVKbPkL\nunYpfpkDP5sobzmTxGmQaO7ibRa+XGqegVkezwwAMEIVOEuJjuCawVLKBNET\nbAl6K8xcuBA4CNYEWIImif1a9wP8RGMs8LOi8b1YVoKdH6FDPwK3rz8hO8Os\nEvNo45V6mRiO69lAyEUJtwIYDHX7kJOZSiVSDc+cS2HNVYPMiArftoPvuR6d\nMs+c0XZQbEuHhwldwKhR2c9GHzV5OWxLNVCfQlWVhVTLmRJFpIcPFiOO0SlY\nKzdy08dJqm2mJcRNiTWPZPy1fNK52cn4F9REvW1e6nnJZFpZBcrSuOB86w8V\nlUKi\r\n=lHqB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICcTazld//VV574RyodP14Ny3Zwy8PnrY/+1rGSp1zxoAiBHsHoemdMAobP6VGCrZla63LiD93hYg9/7YTRS69ps6g=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0-rc.1_1533845256179_0.34613995429629196"}, "_hasShrinkwrap": false}, "7.0.0-rc.2": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-rc.2", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.2"}, "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-rc.2", "dist": {"shasum": "bf8cba58f2a7b1a93fda8120a4d2d6d99be3152e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-rc.2.tgz", "integrity": "sha512-Gu2nk4dgKcAIeHmu6mN0U3MPuAhGP19fxSkqlrTtzRdY8aiDyU2VFHspWWirj++y6zAIw2uIOfZ8mTMUjdlSWA==", "fileCount": 5, "unpackedSize": 1442, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGa0CRA9TVsSAnZWagAAK3AP/1OHQt3YrOApSNfg0fsm\nCP6rQhYB14DuHglGASfA2p1VmycQBuadk2jowHffluFemvVtRBLNudABwsuJ\nTZy5K+SwVWPiXaikG3F0Pe7NHMaoznzLTb/x1i+dBYADxOCxoWe5HH7gRakP\nfMbUQdCDfDYc6LE3QTdzDq9ziId4mwHtLYAiN1bcycKfYRGhCLjnN1U+m+OI\nFi7c2C1RDU172hFxKBZrasPURQU+NOfOA7XBmzIqtkv79upW5QCWa+aL53Zk\n5idvEcRYPA9We4Y6wn9PFVD7V08DTE7uTafIBv27ralEiftwDPCoY8zGDdiG\n+w1R3jwEMA+KLEB11VTpgUMQq533CHuLEqbu9SqBnguAdWgUj1fO7cmvSL0K\n7hYH/l2KCDy8VewEDQIPMcd+PqHlP1jAcHkifxR8RK/9Lg5ObJzQTTMJjcdA\ndFxMRmsoBTuwFzpSVWpOhocL6wcc0x2alXRpm3s0UWSiUgzGBu49b23Z919y\ngGl8zTnQdTkkEvy6//mCkDCiA/FuRC9S+uP8BOL6OjYBQjP5PZLhsW00IBTx\nYijBv9dJPBTBWfpOtsQdbm65KxnmxRZh2E8ZqStqtR7enoMQPxRhMSEv3CRJ\n6hPN9d96nZWheyNJ0rxqNew1Ezr74LPFQvyiwHi0MHPw+Vli+dPAEHkHmaCU\nTQTT\r\n=GlAj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHUO75Rj+1WRznimW04lXvN2WgX0L8Oo3HTA3sdc+DpkAiEAiYducMnq4arvJTFhDwOen5uNK5LwNOqc/22T4wy28O0="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0-rc.2_1534879412352_0.2630302027973628"}, "_hasShrinkwrap": false}, "7.0.0-rc.3": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-rc.3", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.3"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-rc.3", "dist": {"shasum": "7b58819dc26830444ea0adf082d80516a6b9e71c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-rc.3.tgz", "integrity": "sha512-rodbP/WHFdggV0sj7h3YFZ3eIfxftOOTYu/sw22dh+Qm8HGsGYN+iG2FkfWGSHbQWuzgufPHyDgLPZaraoLdnQ==", "fileCount": 6, "unpackedSize": 2541, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgElgCRA9TVsSAnZWagAAbWwQAKJgVtlFhPh4Pm1DQWGm\nvH6cQk/MFzFo+nV3OpfPRBm2JhjkZIyFyOrZyPz18lOb6nJQM9Px3xtQOOXj\nU2/T5WYShmi51yRrr6SLWiMqLPl6LQ4bBJBZNiEmNmHT23+lhZ9ox75l/Hfs\nRLlHN7hVtQD3m8J58+yZ1w+n0PzPZssN24jVz3F/kxrdoFdhI8UN9nN3ikpm\nsWRtGtuir/h77c9a0fGx2SAd0gN5icPiuL0YofOs0kKTYgaz/1Y9ok3niXis\nDBrZvHg+McL8KDVypMvqV2F3eTTmamvY8zWOOaMHePfsyfFM92HUTdHIa14r\nB9D7VLLY5anapsOg2aodmrygV5mYKywG59bSHWweX10sj0w6eVKLd00rokw8\n5YYQVmEkFXyzsuVrjhNoWC7u6WBdKFxZA2Cpyzv0wQ044FUBtkB63Keswvcp\nXVT8NjGWJt0R5k1AJRTizrkELSS6jrLq1D3Xscvmoawi3gA6Yh/fzYMhmD/1\nYfLdkd17wdKCfCPf08TCm7oKwo0ZLphplsMHGM9hurL5wtagwKjYayxopJYi\nbr+wuGu2x8NipKsFDr7Km1w3s8rPzPVPbKxkvINHM80Ie9crhg4Vn9WBMF5I\nIsnbPHGhwnRfx8jU/InpxsO087pob3iIfkgFLDQADAWSZcH5vRxrhPpg0SpB\nUpg5\r\n=9sbv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCh6SDCwl+/8AeZ0f4ZpORhkMRZYjfJVzqcPIuuDMUdiAIgbbsV/rIpkk3PZaFJIdh8glBqIcFGkgzXA8Fk0I4Aw2I="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0-rc.3_1535134048086_0.35184549012005406"}, "_hasShrinkwrap": false}, "7.0.0-rc.4": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0-rc.4", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0-rc.4"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-optional-chaining@7.0.0-rc.4", "dist": {"shasum": "923c4fc95903ce495aed392238b66c29ec0496b0", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0-rc.4.tgz", "integrity": "sha512-6qk83dAegPhgWSObILqGBgPwR0QEVn/dH8SK1CaDQn5yTqVusFBFdCluF3YrdxLxVIFI8OhTzFM8aM5XJcVqkg==", "fileCount": 6, "unpackedSize": 2543, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCpBCRA9TVsSAnZWagAA3coP/ji+6dHkRbnkv1g+xnHe\n/FCDoliNIR/oOA0qB4OwS1357AHF9It7bpc6OA2584SJeos/iz5Cv8cICa86\nZQvcFyoOqc7sZAo7dWnzBYYA88x22/ScSVfP5LTaMixcZS3BxuIS3zFfrhkM\n8qfHgIe3c5rwSZzoiWRcMBAR7/CasggwcdBetE7HYHt9qlhW4CLxxYF81vAW\n+MqQvZVQYbnNDCCdp7bDx+BzXo2oscKokYGf+BX/zSpy9UFQeVVPK4XHYiLb\n72Sl/5wECFBHwXd8MrLuxg9xVuwzN9x5yYNZxfkBYJvd1vouSVb++AgXMgBa\nkNMW4uJoKFt+2yOnBEEsu/SLbahTTjhNu3A8FJLq8LGuX9kXqbKSe6J5/xHg\nCTm/Q9UEd220hH1j0pVRuQRDOfqXSsAhzMQ4r+2RsCwXF3B0S+EA9KONdozd\nldxHiuik7xNcE5LirPpcrYLvwW3M0bnQhvNqrNN/4kIDu7YUq60l8o8I+WnI\nQ/ZBLxVg6qC8VrjCFW7wMpcj9E4zsPS759EDg8JhK0QayENoSZH95r/GMG02\nc8RFoAAFl0ofkn3mOoYNRKFoKff7IVtANNW9u31Z6MYXZsJGpMc0AlsxC7Si\nC3qLugev0J81fTR2kqNfJZfHNs2VHghXshFF0AEwOWoxZ/7fSKOE91kKFCuc\nH1Id\r\n=v6pn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICbiyiVHcPrTW72dEFCnpFFArQyRi0xrSwbr0wL53EflAiEAnsVL5OmgWavyEs36eoqjiChQeo5Tz73ZmdVQasPVyqU="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0-rc.4_1535388225370_0.2796868731076203"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.0.0", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0"}, "readmeFilename": "README.md", "readme": "# @babel/plugin-syntax-optional-chaining\n\n> Allow parsing of optional properties\n\nSee our website [@babel/plugin-syntax-optional-chaining](https://babeljs.io/docs/en/next/babel-plugin-syntax-optional-chaining.html) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-syntax-optional-chaining\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-syntax-optional-chaining --dev\n```\n", "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-optional-chaining@7.0.0", "dist": {"shasum": "1e6ecba124310b5d3a8fc1e00d50b1c4c2e05e68", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.0.0.tgz", "integrity": "sha512-QXedQsZf8yua1nNrXSePT0TsGSQH9A1iK08m9dhCMdZeJaaxYcQfXdgHWVV6Cp7WE/afPVvSKIsAHK5wP+yxDA==", "fileCount": 6, "unpackedSize": 2528, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHBZCRA9TVsSAnZWagAAgUIP/3Bvz6QtCWDgtZgIrZw4\nmR6pwBWGQY0JwsW468aQUdpMAyJ+kwES26C7+vKz0lQxE7gUU3z9emfvj9Af\ntJ1i3iQpPwpJ4nwRfY9wqNkxynMTh92Ipdr/tzJ2MvDhOyNqkwqRItR1EoeA\nzdl4RtFzPDPjstfcFyt+uwRl20Ng19i1dowHPaivMwiEzGyw8cqqfNBbzas2\n1aswVyDriOet+kjHk8lmjuuY3sCjoFiTVWT8kxlezG7WB9IX5enYgO3TGOn4\nnRwihuVne6IUTOyjyalgiyfEomie5qW9UYeyx551yGs6lVhFGg4j04x9FLiD\n4w5ReWJwhAXNwCzJ+/SJzQZq+qa+EEkZ+lCzV/TJmQI5mMeAoaqNkWIyTVCh\ns/HOdas4ZKkb9wjlXlmrVBd2Kprx5HgaluJju2Laxa1RtRlV4b/4EVwHtBp9\nIgVFt6hBxwKU2CUIWTCuu73DDQKwGSgq+xSDPywdutkK8f/er96AsNQHtrDJ\n+Wbk2bm4pf0Ydgx9aWXnEkf0QbqcBfMP7JKtjH2v3HA86YkL2xBxfRbyFKMd\nkm2hUexKUGmE5vvXkop211GVNxs3pujIlRojW7CKzYn3x3E9oiIyElOFhmZb\n1yMrUyrrcSkkjbWoiRCeBVXudPqWHrVrcstba5BXkCpxA8jn+gNgSyw2JzRv\nYr9s\r\n=LwzD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFB37cQvaMD5bnpSzj/sfFLjikw262WsUUgEmHiIgemjAiBjk+vXnj5ehdHxzo4mnPkbzaOYs6ZRsAWK8QmisrHbQw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.0.0_1535406169155_0.8598034240379013"}, "_hasShrinkwrap": false}, "7.2.0": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.2.0", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.2.0"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-optional-chaining@7.2.0", "dist": {"shasum": "a59d6ae8c167e7608eaa443fda9fa8fa6bf21dff", "integrity": "sha512-HtGCtvp5Uq/jH/WNUPkK6b7rufnCPLLlDAFN7cmACoIjaOOiXxUt3SswU5loHqrhtqTsa/WoLQ1OQ1AGuZqaWA==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.2.0.tgz", "fileCount": 6, "unpackedSize": 2620, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX1zCRA9TVsSAnZWagAAfWEP/igQEHnTwl2UHUYxSZ9f\n8nowf0eHY+0mtLykPe2SCbnk1gyd9gJu9o8YrhlGEXOH5SOvGvQQ5XL32rET\nQ+sVBWKVmNK6Izw+FhOzCn+z28PXwUsMgkh88B0anlIp4BY4w+yH1zuv63sI\nAd3gt4UaRymY9zoEmvuUYBHDGKdEq85u4kkjeokbywtfPr3e8b4/9uQGOWwc\ngEAiggYXnlQOBNnXFOShLwM+06oRpNSDeq203lTRe9JrNWHP8agqH2VCJYRj\nhUh9+xvaBT/kmmFjUi2fBRQWVJ2z5EjKispBLPbDncTz072QuQVHT7Kxy+Sg\nnvkE4v8TunbHkmW0sVy71lixJpXbbPgAZaI38UqxPI5af2PxUZTP/ommlxdf\nG0FLFnIvNs3yb5qP9I/z2/1Vg8FhXvRfhjRkK4eP7Yen/1I2M3vzytlKNl7l\nVdgadEDC9PMbnHfsbq5y5rOzeg0zyENKEkk/6uY92gSG3uzut/Wh6wxkoBFX\nkz20Y7eiDWwJ3mMqQwXNXOjxjbUdp/+Mz6o9b8pAjFXUWQrl10GLgnUNXmgk\nDpMmSdyO1HmFYwbHmIV7pBOpgoOEsaYZScEOQlgsVzl1WHqxHjQDixfMUc1c\n7luibtzU+4Q0grXjUJqUAfSRgiowbQKRQQICiN4rhQsmiINvsSsDi3riNRO8\nQbR1\r\n=0ITZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBBd/PAuu+8NO07eZ+c/6GrdQeOgvfTTN9ankN1+SSzgAiB23jIpxChbYhIqKrzlQ16gk3k1k++fVxkR6v1TTXJmYQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.2.0_1543863667100_0.4375919555979755"}, "_hasShrinkwrap": false}, "7.7.4": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.7.4", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_id": "@babel/plugin-syntax-optional-chaining@7.7.4", "_nodeVersion": "13.1.0", "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "dist": {"integrity": "sha512-2MqYD5WjZSbJdUagnJvIdSfkb/ucOC9/1fRJxm7GAxY6YQLWlUvkfxoNbUPcPLHJyetKUDQ4+yyuUyAoc0HriA==", "shasum": "c91fdde6de85d2eb8906daea7b21944c3610c901", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.7.4.tgz", "fileCount": 4, "unpackedSize": 2578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/vCRA9TVsSAnZWagAAN/wP/RmE8m23/peDakLZIHF9\nuQ1KsTBz4IIEmGmIgjXa7pa/syiRzLeApUpZvEZKpHx8aUBUs89xAWw6J9E7\nzmR6qQtlluUq/MT/HmANlNuB5da2x2FjVJmzvJNRWFozI8aXk3ghoCMW3npn\nqSPyfATz2aWxwxrdF+u9NEIMCRZTSL3Rpk8ivTlS68L2t0P4c0H3mxxyu4LN\npMbBOkFMSW2ckg5uI4unhpnNs3yOajktr/s9db4anlR/ULVsZvxr/m2q4MfX\nf368IszGyZT14tjv+0WYNJFwh34Yw9AKPXHs/HHlYyaOYpxMoQOcz0mHq/Gz\nv2Sw//nkTbV3w0G73HGjOnbeCj15iwh191b18whcBt+LlMYw7XSqE2OSq8h8\nXPFrcVdBkttic38TKtn2X1GAJoJSeLtsFE9hzFF3HBtBNFwWKAlp0DC7xi6L\ncMM4q/BzFeVrVzwYaLUvM1tQcb6AVUH5Ek3lywyjNpRx7HZKAfTor89cLJ7c\n4neRJlBFmG88grxzkvvvR1R7qDA61iilnV7Bx1Ktu8SFWA1Y2rYO0VFr77FR\nK0lA2ssKSd98gbeXuB9qKGKISykUT+WiIznBuipifM/yiGBqB4NCKLXHvwAu\now9Xqr1I+0WEWiTAdI6caiO939D2w8KdrqMzIY+WR+H2haDvxNJ5Ab6OjGSk\n7e4n\r\n=Bg3A\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCoBg3Qrrr/or2yyLBA7Rpq3rwRUlDyzdThOfNiRM7D4AIhAIf/mXvRui1r8kbO2cQYFjbaLyRy/5pA6L7gMYfFOT11"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.7.4_1574465519130_0.7224989764797336"}, "_hasShrinkwrap": false}, "7.8.0": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.8.0", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "type": "commonjs", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0"}, "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_id": "@babel/plugin-syntax-optional-chaining@7.8.0", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"integrity": "sha512-LV1c+TTAO8Vawe3t+WXBHYWbS7endP8MSlqKPKEZOyWPEJX2akl3jfvFG828/OE7RpyoC3JXfLJDFj/jN7A8hg==", "shasum": "c40f4d4d6a4f5e71d2bfd949b0a7f1e1e6792fe0", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.0.tgz", "fileCount": 4, "unpackedSize": 2600, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVYCRA9TVsSAnZWagAA8UAP/jQVle3daKHlKNU95HRH\nccKCC2lq4soxp9aqKl7YNjVyDTlGNzvElpAQAljbjsvuk/tqYkwQrdoavSsd\nLZeOsXk3ETsklUCaXTqZJqSmM8VF+vMpTDMU5uGoAlwY8SZGBEq6IHGxE631\nv8ycx7RP74mTWnN9Po5c8viBhrw7tRC87kTGaRHa3ouSVLZc6X4WwcSNhlOi\nHMwx7ZbWQPgGz31zCYRR86hKoLMoaPg8drJQUeVCtPVoLQH1MQh1XO0EktY4\nSKWBk5TuN9Ylg6c1ULhX2vyOeVTx/SMuE57xUwB0W7GyZlAPKzQthXqi8qtk\nYyQsi7/MKPltCqC3SsCHR7PhpJ8LgJmEAbBJUwcHV+MuWGKNHDLXzSRPaWof\nJhCAwnTH9oYYr8jOZh8vGYLY+rlGIFHXTEE56H3Z3h2Di95YkisQiSuvMGt6\nw4sg0WH++UTOyP8McPIrf4w98AlAkY6mEB/2gI27BVa4lFdXObRh/au093zC\nVD5OppQMaITC0qA74erfC9WLSvWFTemDjGWpPdPyCbWdJjYw9Aa5oFCqZ8sO\ncAs3wpd06Rk/DW7Dwv6S3qg9jfMUcKr+O3ItdiP7iTmQSV4jVNycGU5TMN0U\nh2/G+esDYjR2tvC4cILbI/QdRRqQ+1Yv2ku9cgTyIrSulifvLarAEm7WkCMP\nqJyW\r\n=VSyF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG/8VhT03hratKiwqV6UCCkoUHcenJGVmwn8PpH8EDAFAiAg2aAQM24ok+KEroysFrg1mxp/LQstpMk2Ygnpc68eeg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.8.0_1578788184140_0.6083344359342013"}, "_hasShrinkwrap": false}, "7.8.3": {"name": "@babel/plugin-syntax-optional-chaining", "version": "7.8.3", "description": "Allow parsing of optional properties", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0"}, "_id": "@babel/plugin-syntax-optional-chaining@7.8.3", "_nodeVersion": "13.6.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==", "shasum": "4f69c2ab95167e0180cd5336613f8c5788f7d48a", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz", "fileCount": 4, "unpackedSize": 2521, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHN3DCRA9TVsSAnZWagAAEGQQAJXJsvYdlWuvRe97PqwV\n0RDTOm9oJ4LMZLY0X7u3wxXu1xyq1TPH6B2Of4LjEK5bTpApP38NZVkADIbp\n4nyNmDe+uL6WKj0TT/LfT+rPmK3FVRpL26J1XgWoLDlKn8eo8OfG69T3/Fgl\nopcHuWfp117ZAOgJI5+Vh/RaRk+1RmjQA3T3WTNUFermhQtKPT9xFXlsA6Sk\n77BY4g9/o8mv9tqWto1RWSN4x3FV/p34945dF7Fk2miu8RPDkpB0cYKkKzgJ\n8LnK77O3DqZKWc16IqNbDzkaOQKKEK4B7OFqCDliaDUx/PscWbbULcDOIgeQ\nYN/YEFNwix7GCOAxHOurasT7v0wz2EcgBJDa0I7nPAZBnyMyOW3BYPF21G3s\ndqxD/Hk65f6cDjTtuQbMR4EeU2oxNgL3HoocXQC619D+w1K5zpy1isNcSU0s\nfndsDvekCpb5OBMLg4jejnExjL0vCNPKiKlHE7yYB6+ifH7Y4MtXl0s2q3h2\nDUN8e1Q+QwK4PN7nGM12v6GyzGSVYCoNh6I+NxNpFJX3KwFOf91t56svAbVU\njpM0n92TMTITW8uVhdGeX+TFsNqkXsZh76Lo/Ws8BdZS04F+/+2Cl3qZAnFi\nXdB8d4A8pmUBbLbP2qf2NGXAa6gILpnZd7S0JQJSCOdbBT7Ieos+qIArfDIX\nBO3S\r\n=NtxA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD9Pa6TXAKFFqqSvarT1775Xknt1hjTmArx1+dClHdKZAIgc+NsPr/lviy558GsNRulANI9AzX9zdzPBepfiudUT54="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-chaining_7.8.3_1578950083126_0.2264598031701468"}, "_hasShrinkwrap": false}}, "readme": "# @babel/plugin-syntax-optional-chaining\n\n> Allow parsing of optional properties\n\nSee our website [@babel/plugin-syntax-optional-chaining](https://babeljs.io/docs/en/next/babel-plugin-syntax-optional-chaining.html) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-syntax-optional-chaining\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-syntax-optional-chaining --dev\n```\n", "maintainers": [{"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}, {"email": "<EMAIL>", "name": "jlhwung"}], "time": {"modified": "2022-06-12T15:01:04.811Z", "created": "2017-10-30T18:34:25.485Z", "7.0.0-beta.4": "2017-10-30T18:34:25.485Z", "7.0.0-beta.5": "2017-10-30T20:56:07.509Z", "7.0.0-beta.31": "2017-11-03T20:03:18.158Z", "7.0.0-beta.32": "2017-11-12T13:33:06.986Z", "7.0.0-beta.33": "2017-12-01T14:28:10.311Z", "7.0.0-beta.34": "2017-12-02T14:39:10.444Z", "7.0.0-beta.35": "2017-12-14T21:47:39.137Z", "7.0.0-beta.36": "2017-12-25T19:04:27.776Z", "7.0.0-beta.37": "2018-01-08T16:02:24.841Z", "7.0.0-beta.38": "2018-01-17T16:31:46.309Z", "7.0.0-beta.39": "2018-01-30T20:27:28.753Z", "7.0.0-beta.40": "2018-02-12T16:41:26.812Z", "7.0.0-beta.41": "2018-03-14T16:25:58.551Z", "7.0.0-beta.42": "2018-03-15T20:50:35.149Z", "7.0.0-beta.43": "2018-04-02T16:48:19.107Z", "7.0.0-beta.44": "2018-04-02T22:20:01.243Z", "7.0.0-beta.45": "2018-04-23T01:56:27.556Z", "7.0.0-beta.46": "2018-04-23T04:30:53.268Z", "7.0.0-beta.47": "2018-05-15T00:08:32.757Z", "7.0.0-beta.48": "2018-05-24T19:21:56.444Z", "7.0.0-beta.49": "2018-05-25T16:01:37.347Z", "7.0.0-beta.50": "2018-06-12T19:47:11.628Z", "7.0.0-beta.51": "2018-06-12T21:19:42.356Z", "7.0.0-beta.52": "2018-07-06T00:59:21.466Z", "7.0.0-beta.53": "2018-07-11T13:40:11.366Z", "7.0.0-beta.54": "2018-07-16T18:00:02.252Z", "7.0.0-beta.55": "2018-07-28T22:07:08.615Z", "7.0.0-beta.56": "2018-08-04T01:04:52.093Z", "7.0.0-rc.0": "2018-08-09T15:57:51.797Z", "7.0.0-rc.1": "2018-08-09T20:07:36.263Z", "7.0.0-rc.2": "2018-08-21T19:23:32.452Z", "7.0.0-rc.3": "2018-08-24T18:07:28.169Z", "7.0.0-rc.4": "2018-08-27T16:43:45.439Z", "7.0.0": "2018-08-27T21:42:49.240Z", "7.2.0": "2018-12-03T19:01:07.243Z", "7.7.4": "2019-11-22T23:31:59.260Z", "7.8.0": "2020-01-12T00:16:24.240Z", "7.8.3": "2020-01-13T21:14:43.325Z"}, "keywords": ["babel-plugin"], "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining"}, "license": "MIT", "readmeFilename": "README.md"}