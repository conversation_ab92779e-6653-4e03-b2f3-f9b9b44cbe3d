{"_id": "string-width", "_rev": "32-05e1743af59333777a2c0338dc1ea83e", "name": "string-width", "description": "Get the visual width of a string - the number of columns required to display it", "dist-tags": {"version4": "4.2.3", "latest": "7.2.0"}, "versions": {"1.0.0": {"name": "string-width", "version": "1.0.0", "keywords": ["string", "str", "character", "char", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dist": {"shasum": "facc17cd1dc81311b6ef9014eb7ffa4e89858f3b", "tarball": "https://registry.npmjs.org/string-width/-/string-width-1.0.0.tgz", "integrity": "sha512-coWD9MT7XKWTVBdUfXMG3WIxQAk3SJHbltAAfcy04J+pTBo0u4jBuYgNgEtWrhyLZfKwo22FvbJ3Zxd0XxPwLw==", "signatures": [{"sig": "MEQCIB5A7RQPAYlk/84AseWwgNww8B3IBtByc2emX753czkSAiAXJ5HdNR3CHbOZP/htBa02cRbZzsZN1asD/QE+JUS43A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "facc17cd1dc81311b6ef9014eb7ffa4e89858f3b", "engines": {"node": ">=0.10.0"}, "gitHead": "4bbfa2814ce7411a12f2dea7395316606b9d2c39", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/string-width", "type": "git"}, "_npmVersion": "2.11.2", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "_nodeVersion": "0.12.5", "dependencies": {"strip-ansi": "^3.0.0", "code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0"}, "devDependencies": {"ava": "0.0.4"}}, "1.0.1": {"name": "string-width", "version": "1.0.1", "keywords": ["string", "str", "character", "char", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@1.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dist": {"shasum": "c92129b6f1d7f52acf9af424a26e3864a05ceb0a", "tarball": "https://registry.npmjs.org/string-width/-/string-width-1.0.1.tgz", "integrity": "sha512-sPfd1Io/7WC2MqQA7ckl7uJRKnZsUpEJamPx+l3wcD+YGqGWDMBWXkLEQdsfrFXgw+T5hdKA0+AKTlccLCjL1Q==", "signatures": [{"sig": "MEUCICokHVA6PEleOirJkhVtZs0pG4n47o3LLVbGhKKA6VKOAiEA4ZR+Bkx67im5K+q8ZKmT9aMA+h2TPM2UAkDd0JmDCp8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "c92129b6f1d7f52acf9af424a26e3864a05ceb0a", "engines": {"node": ">=0.10.0"}, "gitHead": "f279cfd14835f0a3c8df69ba18e9a3960156e135", "scripts": {"test": "node test.js"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/string-width", "type": "git"}, "_npmVersion": "2.11.2", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "_nodeVersion": "0.12.5", "dependencies": {"strip-ansi": "^3.0.0", "code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0"}, "devDependencies": {"ava": "0.0.4"}}, "1.0.2": {"name": "string-width", "version": "1.0.2", "keywords": ["string", "str", "character", "char", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@1.0.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width#readme", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dist": {"shasum": "118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3", "tarball": "https://registry.npmjs.org/string-width/-/string-width-1.0.2.tgz", "integrity": "sha512-0XsVpQLnVCXHJfyEs8tC0zpTVIr5PKKsQtkT29IwupnPTjtPmQ3xT/4yCREF9hYkV/3M3kzcUTSAZT6a6h81tw==", "signatures": [{"sig": "MEQCIFW89A2g2xM10bZqgSolvp12o/30ZV06L7tUI3K28dcAAiBrnrNHwatKtF/cByRJtLUeZJWujC5jvEtq8T/kbe36xg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3", "engines": {"node": ">=0.10.0"}, "gitHead": "282cf3d53918a92cc3ee0778dcf938039bcbc47b", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/string-width.git", "type": "git"}, "_npmVersion": "2.15.5", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "_nodeVersion": "4.4.5", "dependencies": {"strip-ansi": "^3.0.0", "code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0"}, "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/string-width-1.0.2.tgz_1471188233009_0.6573935742489994", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.0": {"name": "string-width", "version": "2.0.0", "keywords": ["string", "str", "character", "char", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@2.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width#readme", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "xo": {"esnext": true}, "dist": {"shasum": "635c5436cc72a6e0c387ceca278d4e2eec52687e", "tarball": "https://registry.npmjs.org/string-width/-/string-width-2.0.0.tgz", "integrity": "sha512-w+YQpeOppRYnIHRftgHpjGYUj9m0XKeam1C4ahbh+vErWcY8JJCcrHi/YhUFhHoVeWADkhplCWYdYwX5Nmhiyw==", "signatures": [{"sig": "MEYCIQCxcTX2O27qyB3LO/YXUA+rfquYo0i6vYczJqML9lOfzwIhAL78b1ThqylhQ6BETTj0CtE5yhz2LX9Sfb5kggPOeM39", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "635c5436cc72a6e0c387ceca278d4e2eec52687e", "engines": {"node": ">=4"}, "gitHead": "523d7ba4dbb24d40cde88d2c36bb1c7124ab6f82", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/string-width.git", "type": "git"}, "_npmVersion": "3.10.7", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "_nodeVersion": "4.5.0", "dependencies": {"strip-ansi": "^3.0.0", "is-fullwidth-code-point": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/string-width-2.0.0.tgz_1474527284011_0.7386264291126281", "host": "packages-16-east.internal.npmjs.com"}}, "2.1.0": {"name": "string-width", "version": "2.1.0", "keywords": ["string", "str", "character", "char", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@2.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width#readme", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dist": {"shasum": "030664561fc146c9423ec7d978fe2457437fe6d0", "tarball": "https://registry.npmjs.org/string-width/-/string-width-2.1.0.tgz", "integrity": "sha512-+kN/kDJ6eIiJHUx7CjQFo1Nlj019tKM3GNhx4uKXpVGfQiRh8hNmaZNOJpy6YhSdcBm+vmOGOkfJdNddNAcdrA==", "signatures": [{"sig": "MEUCIAGqZ+WAhz27kcSJUI/gFXWtA0n18T/ERnEC3KkFAKRlAiEAlcmPOmBHemF/JDsjoaJB/jNIJyUswcnH2dm7+qcWKGo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "030664561fc146c9423ec7d978fe2457437fe6d0", "engines": {"node": ">=4"}, "gitHead": "175b26f4cd503d6236b5ea2a1664263b56f1e352", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/string-width.git", "type": "git"}, "_npmVersion": "4.6.1", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"strip-ansi": "^4.0.0", "is-fullwidth-code-point": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/string-width-2.1.0.tgz_1498493708563_0.051171303959563375", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "string-width", "version": "2.1.1", "keywords": ["string", "str", "character", "char", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@2.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width#readme", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dist": {"shasum": "ab93f27a8dc13d28cac815c462143a6d9012ae9e", "tarball": "https://registry.npmjs.org/string-width/-/string-width-2.1.1.tgz", "integrity": "sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw==", "signatures": [{"sig": "MEUCIQD0QEscnKyvRpCxyjflFgll2NLG0pHj7WTPBjB5r0nYJwIgNyVWMoOnZbXUI6QFF0f4gUgvOUU8VcxH2n0kkdADykA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js"], "engines": {"node": ">=4"}, "gitHead": "74d8d552b465692790c41169b123409669d41079", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/string-width.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"strip-ansi": "^4.0.0", "is-fullwidth-code-point": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*"}, "_npmOperationalInternal": {"tmp": "tmp/string-width-2.1.1.tgz_1500376154382_0.7171604454051703", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "string-width", "version": "3.0.0", "keywords": ["string", "str", "character", "char", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@3.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width#readme", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dist": {"shasum": "5a1690a57cc78211fffd9bf24bbe24d090604eb1", "tarball": "https://registry.npmjs.org/string-width/-/string-width-3.0.0.tgz", "fileCount": 4, "integrity": "sha512-rr8CUxBbvOZDUvc5lNIJ+OC1nPVpz+Siw9VBtUjB9b6jZehZLFt0JMCZzShFHIsI8cbhm0EsNIfWJMFV3cu3Ew==", "signatures": [{"sig": "MEUCIQDEZMiZvkTPk2rkNKl+AqjMmOIwdf3okD1HYYPmHBC9zwIgCGUZ/Zg2p1YyVR71QWoXd5BpDROS7Zh46EqN7IqeSAc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3928, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFQxRCRA9TVsSAnZWagAAUGcP/RMaOMcTiEPzw6CKx8Aw\nVYY9DOqoxRxEarNwUFerDX10ezHNzS+vhw1bB6LDIWwOFqy0KFYsUCkzgcIT\ngwrVWLIRJIHppLc0/4cwgmaj+bwkq7zLqiFR/rRfSbFAJYUxucMokgX1S80Z\ntQabeDKKh6PAyrrSZFauSiUz5mEkxB0yLPKvvLnLl0t7xBzcMl4F1LxTgZ8g\nusM931jqRgf3SE9AHnKZGlL2OkaGOPEoRIQwcWkWzEyqm0suULZ0So9f6127\nMSPi8q/v3t1VnfD6w9XeQwfyHu1AbPGwoYRlUjILwyAhxzyftb1D7wzeZK0n\nHEwR9FBgjBK2rgK+4IuDu9U27p5H49Qez4W8zHxtYLLA5HF6QI48ut9t6hMC\nvt/g0kbkV5TNILl8L55cH8Q4DyGwzpNQY0XAl0hKzt1WYuAxcp2QVa/xlyaU\nXznYCBJ4VitsxLPi+MAgj1hUTtzZKTbc14EJ9mgFpWdApfBxU6GRiaHmC1Cw\nMvuqgHHdJqFpvetWC7COo9fXgEVAbHaKaTARYp1Z/uwKPBwHBs2ab9v2GB8g\nb4euKRjobYQ6DdIXKMfRgezzFDDy6LUwbv+4QrlaVWCxT43NQogRkOIV1dOz\nW3DDrR7wQ+T0/a7vtFYvMsw0Rq1Ldr1TT+uOP7bXaKbGZs4NmELjk7gZvM/w\nodqZ\r\n=M9ad\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "58147d249083e44c15c00f9de032d1f25e2039fe", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/string-width.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "_nodeVersion": "10.13.0", "dependencies": {"strip-ansi": "^5.0.0", "emoji-regex": "^7.0.1", "is-fullwidth-code-point": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "ava": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/string-width_3.0.0_1544883280763_0.9155794194309814", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "string-width", "version": "3.1.0", "keywords": ["string", "str", "character", "char", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@3.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width#readme", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dist": {"shasum": "22767be21b62af1081574306f69ac51b62203961", "tarball": "https://registry.npmjs.org/string-width/-/string-width-3.1.0.tgz", "fileCount": 4, "integrity": "sha512-vafcv6KjVZKSgz06oM/H6GDBrAtz8vdhQakGjFIvNrHA6y3HCF1CInLy+QLq8dTJPQ1b+KDUqDFctkdRW44e1w==", "signatures": [{"sig": "MEUCIDztdTLUiglugMoqomzwvM7KSVC9eZ0M9VWBXdh18ounAiEAkhPkUOnQrWmkMQ0MaabqLnWjS9e95kg7YjvValSCT5g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgiZYCRA9TVsSAnZWagAAii4QAKDSW3PDbYUGmg95Fi36\ncRIzc/l97f2Y4Smf4yx5KgDASaiW4AO3PlQnoCFs8EGX/TLpgUpcS/XL2IQz\np83UuTYMqkNnCR7KCXm5SlpENLO/tJ/9i6M+nROCdlSvwVzZGCQrd6wsvNUo\n6gQMgimFy0YspA9H8tkmPOkNrz0kTCPWvYzdBXe1u7RtpBLTO6WvK1banY/K\nyIyWPK1Fy94Xc3mK6M1ha2FOI2eR0yem7wzCJGLZ1g0w2SUX+0DToNV7Ag50\n36Wpy02qy3hErZ4Dx0XE3xuRCX4i6OBlTKtvODlndYeIpF7Mtt8ThcbuAyOE\nZMjd+zTIqyA2dK3IsMKwTmCEGowoVRGGw/IABtft6KgPMSNTuteRXtGFSN0o\nrjxdHU94e8l2xeo6818S3teigw55WCuZn4lBswJAXSchYSt5Gs71bE90OrJM\n4qFj2j6ACz2/Amvsi9O+i43ezvQiePJeIkv18bqgGARtSBJIwoK7InbbIhLo\nsznU1y+gi3CAWcGog8wD9bYmCOUStUyhIrXXFb7wwihbTgEvKBZ89RlupXmp\n7+ONMZZhnWLgvX2UtHAGb7xTCDE+ny/z1Q+9QZv5LZ7G83CvEWxzt5UO7b4s\nOEwFREjr2dJfTIDBGYSNchZihQi91RWr1H7w0T6sHrQ5cYAP/sEeoeImdFrN\nHAN4\r\n=c+uK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "gitHead": "3b3da68dfea82e193f9d6deee77224cf8e5c3803", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/string-width.git", "type": "git"}, "_npmVersion": "6.8.0", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "_nodeVersion": "8.15.0", "dependencies": {"strip-ansi": "^5.1.0", "emoji-regex": "^7.0.1", "is-fullwidth-code-point": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.23.0", "ava": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/string-width_3.1.0_1552033367672_0.5762074411129379", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "string-width", "version": "4.0.0", "keywords": ["string", "character", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@4.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width#readme", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dist": {"shasum": "44fe19da01e4e52ba868ef6734207f5bbe11be51", "tarball": "https://registry.npmjs.org/string-width/-/string-width-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-r6JqKDbluBXG8TvRNZNA7ZNQgDc1q1Uw5Po/fHQMiDfQNxIdBGkiJ3sE0HfdTaOmKjh7kVCOjL9pNKiowqtIHw==", "signatures": [{"sig": "MEUCIEIlvsudLIro4vP4fl6U+/uYgCmm2NwtrCFU0200taIFAiEA6riXAyoce7I4+Gd3pOtdreUNGCwa83CfdOxqQUNpoxw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4598, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcj1WVCRA9TVsSAnZWagAA2zMP/RJZ7wHDL/ZlZs7T8w4X\nnusZFm54hKVonAuc02lcNVqD0af3yWPXplyWIiDw8GA3uWtXC1GXY8NSzDpf\nnm1n0ThHpl4/HGWx2EVtT0ya4BJZcE2HFod+eQz6mrjY+uZEXxsjyx4NMD39\nGuff40EyQ9jovo4a9hRnwLGKoVLXxKg1V/nHqvNHUuK9emxDmv2MHiU/hT16\n2ClfMEl1ObnvxkAvzSlmVj3px6A36okoATm9GqVlExnxxEhkgzXSEwN4xtVT\ngSjEdknN9xFPklRtv8BIntSBLisVyfmWlHnuYvXmvaJ1rZGQglFlBLjM7hLN\nPxkRAt0fSa+0o05cx/fVMG5SpDWPMmHqGTgM2voETH4CRqt9QTyt2TFxYjXi\n+zPGqASGimFLPq7v2gB9u4f0J7WzZczjb1UB85zA2IY4SG4+nA/dZLnQlEQG\n8IBIx/f9eezQ1FRzgPdTqxz3/SrMPgGM1mSiqEmztLuxldTaemzcrD24wwI6\nee6uAJ3Z83Syr6tyFF8n0HFBo6TAkx+2tretqvEfAbCC/SgbD86CsboFkOoU\ns2H4AgeM6NoKEeKZeEy3aBQWUgTciwNXh4rQ3MdVVAMvJgyVJln2JH9+IoXn\nrzPpIxQ+tKYm49hBECZSIRwgKeL6uDxvzJDPoG1M+qA+lzJAkoa3f38eborK\nnBfF\r\n=+AGL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "2ca2bc7443a6408feab185200ae55fd6333199c6", "scripts": {"test": "xo && ava && tsd-check"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/string-width.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "_nodeVersion": "8.15.0", "dependencies": {"strip-ansi": "^5.1.0", "emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.3.1", "tsd-check": "^0.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/string-width_4.0.0_1552897428563_0.6535148093489709", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "string-width", "version": "4.1.0", "keywords": ["string", "character", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@4.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width#readme", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dist": {"shasum": "ba846d1daa97c3c596155308063e075ed1c99aff", "tarball": "https://registry.npmjs.org/string-width/-/string-width-4.1.0.tgz", "fileCount": 5, "integrity": "sha512-NrX+1dVVh+6Y9dnQ19pR0pP4FiEIlUvdTGn8pw6CKTNq5sgib2nIhmUNT5TAmhWmvKr3WcxBcP3E8nWezuipuQ==", "signatures": [{"sig": "MEYCIQDGglZqCMCMPnAV0LlQy5B4Uzaw7zVpU51U8zR9Qhy7vAIhAL6GV9RdeT0yeHe0zoqYVB/CpUje55t6lPLU4aZBx5pR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4871, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcoK6qCRA9TVsSAnZWagAAWcgP/jVyAs1x3o6qWfpQXJR5\n+SIxdMk06A7izF3BZAZ0CGwxt49I4VZt2k6G8qDZOuex8ajxRKepGPROSUNW\n6u+R0SICrATI21i40GfV6DPhDio6f0Ps7uMachUY8+X9sA+rFhHIC4jMrY3d\ndyBXqsN+FJO4Vo1+uvO4Pl9Fjkrfa3hFIzUR7sjGZO+v7j/uw0BbdPnl3GT7\n0TPe+iwfStsQyfYYGkrOiR9S4mfwg6gKwOAZimwhV5wq7FVt0SfcoE0NfxjL\nr8NRgkMtFs5lOrK9fvRnSq+/6dOUeur5XCwuom1yyO94BzLGh6QfHwIDGKvs\nXCd8DPWvzWdanNYS3te5j6oBqlBB1hvxjrbDaHEMhAoI/8qOf1EcKRwgUw3x\nOCY33spl3Ba5l/aZzzu3zDuq8XtMwlyXIt5h8cdJTyO1+GUmpx6IafaFUUd0\n5Y8zE76gAwEsrY0sziBUrYPg8Pg7Fgdy1ZIhYJSQF3Q25TGTFveBYBOjATH9\ne8/t6mw9ILFToj9VvjQlPqli399gDujTJqo8MAQTGwjpE2xdPhB5BDd7pk2r\nJ7vH2L3iGqLlYZS2RSD5MOMWXYxtjtbjAfOJjfM6jnFW58DiMdO2HEOmtZUC\nVlkDOCjbgC1wbTTLk4Ek4psXdeEdiHib0zO0cOX0c+M6lEHkgADzZ1lMAPmu\n17lX\r\n=SM2+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "272fccd2b2f90d74e0ab45299d109644bbc1751b", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/string-width.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "_nodeVersion": "8.15.0", "dependencies": {"strip-ansi": "^5.2.0", "emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.1"}, "_npmOperationalInternal": {"tmp": "tmp/string-width_4.1.0_1554034345729_0.021962184962448505", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "string-width", "version": "4.2.0", "keywords": ["string", "character", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@4.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width#readme", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dist": {"shasum": "952182c46cc7b2c313d1596e623992bd163b72b5", "tarball": "https://registry.npmjs.org/string-width/-/string-width-4.2.0.tgz", "fileCount": 5, "integrity": "sha512-zUz5JD+tgqtuDjMhwIg5uFVV3dtqZ9yQJlZVfq4I01/K5Paj5UHj7VyrQOJvzawSVlKpObApbfD0Ed6yJc+1eg==", "signatures": [{"sig": "MEUCIQC33SW/TuhHGQ24H1k3Dz4Q3l9u9/lXFVTQ8VKkwDXddAIgB6bitz8XEVVXq2uLEsl/p+Olq82QUMGMiuUjdhyzYOI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5251, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyYcNCRA9TVsSAnZWagAA6ysP/jiZ2cV8jap8OG/y9gS1\nDthT5O3BNBZT1noi8/2VzwyyImFCxdf66cYUk1vCRzaJH2TWFY5bWVjtFUuS\n7Oo3oaPRr8wMvAoPJHc5cKLgUMEfkbxisK+M2ChMz4Hn3zJ09xUbVq0/fVSL\nGYIMFgJZ7N3pdHSc42xPKi1PkaOlhaa5xRUXndX7B5XQyq9y2wHRTd8dS1XE\nwf5QYBJGA8YF8FR4SVv/gJ8ncaMNtBONBopidGVJCqAhT87y4D7ihYNAcDxi\n/mrsejhfTFOweByoMfSLcsxsZD+deDK49/bdHa+kQv6iXkBrNSceTZgmkqEr\n53aYJ3RRerobU8ShtoG/2wQXgixmKdbqL5wYljg7o8+dUNd+GLLBl34rrlrv\nzI4X42SfOQ4Joxgmm6fwxAdqyFm6b17gI2xhm/CVJ2dMMwJYb8072mj4tQjc\nzXtYvjsjD11FxtrIK2AmaB+AHbQF2RbGsAnrrGMX2wd/6lJk/Q4SnNkU1T0w\nOTSoyL2w50BoTTvEYkoicnDXKJCfHFH07K++K2BlkB7CF/1bvODPFPEh7p1h\nbo8WGlkilw/rvcAjkAtff5SuOBldifmaql4P8jl0A/LrErZ3p4HSUc/oP3AN\ngfkl97TUmejnGCs+V5WBcEJeSaq/4Y52s02VYv/Xz10atWk9KxTa2S13KUwV\noZb2\r\n=jm7P\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "34bca56b5b301b46fef0258aba4446792d794dab", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/string-width.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "_nodeVersion": "12.13.0", "dependencies": {"strip-ansi": "^6.0.0", "emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.1"}, "_npmOperationalInternal": {"tmp": "tmp/string-width_4.2.0_1573488396754_0.6519093851604172", "host": "s3://npm-registry-packages"}}, "4.2.1": {"name": "string-width", "version": "4.2.1", "keywords": ["string", "character", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@4.2.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width#readme", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dist": {"shasum": "1933ce1f470973d224368009bd1316cad81d5f4f", "tarball": "https://registry.npmjs.org/string-width/-/string-width-4.2.1.tgz", "fileCount": 5, "integrity": "sha512-LL0OLyN6AnfV9xqGQpDBwedT2Rt63737LxvsRxbcwpa2aIeynBApG2Sm//F3TaLHIR1aJBN52DWklc06b94o5Q==", "signatures": [{"sig": "MEQCIBnVKQt/EoUs6iGejgtpLpq1Qmqv/A5MNB/D18wtz94nAiBJPLbLzabiopLKaRjibqHjqmVI+h1P6S/ye9au6hPl7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5161, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgOMAcCRA9TVsSAnZWagAA7+sP/AzO2wPTZWuvK1P9W+Za\nEBfB9v1V4gQIsKFHCxlnyFk23qifGmpuxsHM+mcE4XmCS9fIkLOnZJFkEPmj\njuoW1MDPynTAkNr9TCeKM0Zkx/F6eRkD23YMtVwQB0JRvYSb9EchBaR0dU5F\noC9OPh8seaUOwXa7aY1VPB93LlUHsLtKi1GdheUTCBOzXbDGz6A+fwPYhOtX\nc/PqRnTxoeZ4A5G/n6iTwvN3Vys9FMpleNATzjnwEmoaN1NU/BZQ2YJZHevu\nqW/fL7Us4UyIFFXyEANFSJuu+zo43rIllnN6t9ggQBYRTmzNckdOYsvbWMrq\nb7zyI4+sraiFrgxgkGPnXXVYTm2j7DB9Ba6zk1UZ0l1uSHZv4jIzKmH0F0Rs\nETiwZMP0Y3LOcC3OnOeJCkVXbtqVc8HACBRAPwG6V0/YJFy8mFPnCL2lpYX9\n58OoHS4bXHQ8nfvMFDROFiNah2xnSSU6IV+9DWs3vC5e42fyR0B6qX6z5773\njsY139pem2bvklxQO37aOPob0Q4rYb/lRaeuzGlSrURv4PlqzeX6VqHsxJYv\nQ1tW0h9Gou80XRDQ5AUFgt82eASeylGSO/GJZxxCD8Tb5ZHW/IfAXOl12C8H\nmS85m9VBB7N1vWj/dDLhOALDzvc8e83fk0c9euXW8rSZ/d2JerhC+zdmSfkN\niez0\r\n=HVkv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "0ba0a95f5067d05527d528248c7e4047d9acda79", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/string-width.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "_nodeVersion": "15.10.0", "dependencies": {"strip-ansi": "^6.0.0", "emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.1"}, "_npmOperationalInternal": {"tmp": "tmp/string-width_4.2.1_1614331931593_0.30415572767746735", "host": "s3://npm-registry-packages"}}, "4.2.2": {"name": "string-width", "version": "4.2.2", "keywords": ["string", "character", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@4.2.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width#readme", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dist": {"shasum": "dafd4f9559a7585cfba529c6a0a4f73488ebd4c5", "tarball": "https://registry.npmjs.org/string-width/-/string-width-4.2.2.tgz", "fileCount": 5, "integrity": "sha512-XBJbT3N4JhVumXE0eoLU9DCjcaF92KLNqTmFCnG1pf8duUxFGwtP6AD6nkjw9a3IdiRtL3E2w3JDiE/xi3vOeA==", "signatures": [{"sig": "MEQCIFKdqL3NZon0o2Om8Zzn9Q3vC1g7vrtGWxPK2/xKPborAiA1gihBn6s2jTRl7Oo4hrAk5+QwFvxbs7eOx2G5shq9aw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5161, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgO6cXCRA9TVsSAnZWagAAtdIP/R287RCpU1n8n8j/J3Pt\n+VVonw3NMGqs96yx/4Z7YBmLHhmnnDAwYgDg+G3L1rX/KCwouwCp5hh1mptO\nolyy9TJYZ7LfpEGGjSlh5M0IsgEjJ90akak7WBFre+X3Mm4A2yiAGvKnrBoR\nm1wJWIRJfWDI6O1DhG9VhqsZT3G60pa12NJr/he1ZoVmmOMK+hutIQdqflie\n44Ym4IYJ1htUnCRsOjoBwzqgaiUMDUWaffgiDiNIRUSI8t0/v3zoZ8rrXF0h\nkeDxLMCZ11KOPzVY0SDGn0uFYDawrwBY/iiS+6QyitsfZqnZKQwuwH2Hitk0\nDhBZDU+t7aBrTdY4ElBuXp7Qjkm9LAiycqXsVoxFYcdddKnBjIYhhlqRYOHO\nSqaQcOwM26H5FP0m6rhLT8Z+HImTtEtg6NKa5RHGZXFa4WFO+xtXOYGgVMOA\nVXSIl3NdR32Fa8SLvmTdyefE/dsJgdc2spa7FG8XawMRAx6x6yjl3hQpQaaH\nww6Aj3hmak5iT+6xc4AKtWty18C8fIRHTmTokCBtHIeRYntAUQ8fBRCl71ye\no7fqWy+bBpPytHrff34zZL6ZeoCd83+0VeLzMr7KOnsPSotyMHwCYwEMCjpw\nA3cqxDaUpGnrWxvlA3YMI3DjB9s/XLQjy7aDHknNn/YZzbzPmN54aU1QtG/Z\noGL0\r\n=oNOQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "8158802b4c776b7d9fce2053aeb8583dfabe6349", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/string-width.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "_nodeVersion": "14.15.1", "dependencies": {"strip-ansi": "^6.0.0", "emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.1"}, "_npmOperationalInternal": {"tmp": "tmp/string-width_4.2.2_1614522134739_0.46236345886432173", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "string-width", "version": "5.0.0", "keywords": ["string", "character", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@5.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width#readme", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dist": {"shasum": "19191f152f937b96f4ec54ba0986a5656660c5a2", "tarball": "https://registry.npmjs.org/string-width/-/string-width-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-zwXcRmLUdiWhMPrHz6EXITuyTgcEnUqDzspTkCLhQovxywWz6NP9VHgqfVg20V/1mUg0B95AKbXxNT+ALRmqCw==", "signatures": [{"sig": "MEQCIE1XR5ZLjNRmTnYGFfokH7JoAG9eURsYwyQLydLDQXQKAiACjtqUjyYV8TpikVrq1LzXv0DtuQsPNbY+Kq7nK9g1LA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgeT5PCRA9TVsSAnZWagAAFyMP/2BTipsU0alCI2Qf9tn4\n86ElW1h938bIUU/oXhoB7fsSwKJ5flETLriAxwXLhP//Xe8aU5YAIYkTx63p\n37Yqoust7UyZo5DP0R/F017+fiGuwiTxHwLivTxzuUJhAFey/jy2tGCIuZtC\nZ2xiffw+j29A0j7QqsjgKMDr1k7xdImJg9YkZ3PEGH+JK1VcEu3f18TZupi7\nOhwZYwbgrAtqmk/PAZnoK7JOSsDxQrNhZMdOapX7WpBCRbPdwZJc/h39mFGR\nGdi8R4tMXbSuXL2HEa0pL82mb15ndLdQdHdQtzarozoU9r8o6ogE65F6GMWM\n4HDcHUbyKM0JfT0ISGUBEf7VaNj0upFNnjMY3BA6eBHLmXO/3wQpL3m4Jjt5\n7jT92iY8nnirfBjyLMhpu0SSA7RZFbJm+ePblVItzi3uZg9TmPcMhtcZ/6fl\n3DTbEe+gKwu9CRd/5jHCK/AuxU8ALBNfb4b11CCPCe+KjjX1VYxOQNNiIkIi\nVb+j3DQgGKD5yh6tSqYPinFHgjjIfV4Tq8QdreTJLM3ibhobcmt+YrXQ4yBU\n4+6Onw0Wto/7tboUuNDjUfkIrgvHnc7utIara8088WU1/BnItLLQknPamlOA\nVDVgRZkteDBaU5hx7Fk5iNAiuwBm5WC4TCBeZoFixMLrlkYrjr0duIiUfei0\nTE2M\r\n=WwVn\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": ">=12"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "89eb79aafd2b7db982dc1f5498424232d55dee53", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/string-width.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"strip-ansi": "^7.0.0", "emoji-regex": "^9.2.2", "is-fullwidth-code-point": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.38.2", "ava": "^3.15.0", "tsd": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/string-width_5.0.0_1618558543071_0.9832092763644933", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "string-width", "version": "5.0.1", "keywords": ["string", "character", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@5.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width#readme", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dist": {"shasum": "0d8158335a6cfd8eb95da9b6b262ce314a036ffd", "tarball": "https://registry.npmjs.org/string-width/-/string-width-5.0.1.tgz", "fileCount": 5, "integrity": "sha512-5ohWO/M4//8lErlUUtrFy3b11GtNOuMOU0ysKCDXFcfXuuvUXu95akgj/i8ofmaGdN0hCqyl6uu9i8dS/mQp5g==", "signatures": [{"sig": "MEUCIEIWNr5vC8p59gxm/bmVzo1y7WealDMZ/va5WqD+YgztAiEAhUKH4dH3lBzOmd7c01s2RDC9BGsi6cEeYPwd27z/bd4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4962, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhP324CRA9TVsSAnZWagAA9jwP/jR7H5hZoNYYxRK0N2wV\na0rtLp9D5qt3tV5dDmzlCbVZ7yUzmKZsjkKYb+D1cDyt0DsDhnsXnUK7vxZb\ndoc4qIJs0vu6fSa2kPp+yYtAWVQx8O0sdYGhrTYYHIbhzI0GY1q7+cRP3dzR\nnGor0jD9j67avo4QPhi+ULCix0iwvgFN60ZCQOIXLhpQ+1GeUId4c0QJADcB\nVi6a+xHLlo2/VNA8gzWsQB1qQowQuQ6DtgFbYD8r39ONM4snfkMYqaFVgjQB\nZy80/gKhAK2ytszFM5FOVYlIUXLWTJ0Et6vs540ir87NV9VTu8TD24fy5M4p\n1J3dKZn3XnFOlkFGak6lBmckp1QbcFq7wbbD6cTB5wg0bE1pzlv7uV029lAU\n2OZ3Eo9RYsTMb2GU2mkc2b8/9KkkaFEcxY9mD2OFTogpYsE9ZW016aqUNlxU\nMaNaO0WuJqItMAJhHtRb/1P/LbHitON5FDTnegyu36iSWwwhijgoeVjeMDj4\nX76g3tV0tTtw4ZiPNAGYn5OhfL1/Lnpq9ikjn1PaBvSfRa7/0NNCmrEGmjQd\nKx+Q0oFCBz6fZ+d55sX+7+QuzBgPp4u1fhXpdA4wzYCiOUiEgjMMXNPTuu8N\n9feCGkcrIg6oV8WJB3dmR8tLeS1q4Q4AM4+eBfBAUgtDdWnRCFVlb1Bn/hey\ne3Eh\r\n=g83u\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "engines": {"node": ">=12"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "fd575096e07f69b3999e1d0fcb89298de403e55c", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/string-width.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"strip-ansi": "^7.0.1", "emoji-regex": "^9.2.2", "is-fullwidth-code-point": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.38.2", "ava": "^3.15.0", "tsd": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/string-width_5.0.1_1631550904827_0.5378580983431569", "host": "s3://npm-registry-packages"}}, "4.2.3": {"name": "string-width", "version": "4.2.3", "keywords": ["string", "character", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@4.2.3", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width#readme", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dist": {"shasum": "269c7117d27b05ad2e536830a8ec895ef9c6d010", "tarball": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "fileCount": 5, "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "signatures": [{"sig": "MEUCIQCdE8iSHW3j8YsH6u/KbZmvj9DtpGTXHmhmmp6oeUG6/AIge8jaI1Qk/Z/GED/SrwJ+yNQAEzkZ28FrbwLFqeAqdSg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5161}, "readme": "# string-width\n\n> Get the visual width of a string - the number of columns required to display it\n\nSome Unicode characters are [fullwidth](https://en.wikipedia.org/wiki/Halfwidth_and_fullwidth_forms) and use double the normal width. [ANSI escape codes](https://en.wikipedia.org/wiki/ANSI_escape_code) are stripped and doesn't affect the width.\n\nUseful to be able to measure the actual width of command-line output.\n\n\n## Install\n\n```\n$ npm install string-width\n```\n\n\n## Usage\n\n```js\nconst stringWidth = require('string-width');\n\nstringWidth('a');\n//=> 1\n\nstringWidth('古');\n//=> 2\n\nstringWidth('\\u001B[1m古\\u001B[22m');\n//=> 2\n```\n\n\n## Related\n\n- [string-width-cli](https://github.com/sindresorhus/string-width-cli) - CLI for this module\n- [string-length](https://github.com/sindresorhus/string-length) - Get the real length of a string\n- [widest-line](https://github.com/sindresorhus/widest-line) - Get the visual width of the widest line in a string\n\n\n---\n\n<div align=\"center\">\n\t<b>\n\t\t<a href=\"https://tidelift.com/subscription/pkg/npm-string-width?utm_source=npm-string-width&utm_medium=referral&utm_campaign=readme\">Get professional support for this package with a Tidelift subscription</a>\n\t</b>\n\t<br>\n\t<sub>\n\t\tTidelift helps make open source sustainable for maintainers while giving companies<br>assurances about security, maintenance, and licensing for their dependencies.\n\t</sub>\n</div>\n", "engines": {"node": ">=8"}, "gitHead": "e7a2755c834246c59a4cc5de9471bef2d531a6b1", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/string-width.git", "type": "git"}, "_npmVersion": "7.5.4", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "_nodeVersion": "16.9.1", "dependencies": {"strip-ansi": "^6.0.1", "emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "readme.md", "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.1"}, "_npmOperationalInternal": {"tmp": "tmp/string-width_4.2.3_1632417441184_0.9029691587206747", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "string-width", "version": "5.1.0", "keywords": ["string", "character", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@5.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width#readme", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dist": {"shasum": "5ab00980cfb29f43e736b113a120a73a0fb569d3", "tarball": "https://registry.npmjs.org/string-width/-/string-width-5.1.0.tgz", "fileCount": 5, "integrity": "sha512-7x54QnN21P+XL/v8SuNKvfgsUre6PXpN7mc77N3HlZv+f1SBRGmjxtOud2Z6FZ8DmdkD/IdjCaf9XXbnqmTZGQ==", "signatures": [{"sig": "MEQCIHRTptilD0K3Au9QVjE8mtvLnOpHzYGFBB1ncOjZiACrAiAxodwzsVjsna4KjFGjJKcGrxfqF75G4RnicbonYFuU1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2nreCRA9TVsSAnZWagAAVF4QAJ8OCuZSzO9mTaLPvAk6\ndoCgWdOlCoksRqYmznJrqlm1eF0zRuwVZhGFZsyDYlw8jD7yzpoVLaFCJdQ9\nNRvP/tPXX7fpqfKEhy9HCBDnqRS48/CJ4RrwSJV7ucOq3qzTZsxmPUpbYl7b\noZxcVdqzDqWu/vLGlbCVQKk1z/U/BoiUuPKYQipBMP58P/Iwm0o1b+y/EJep\n0Q+Pmd1jLAgClC7dauLG/TcFrW56kM/UTvIeBGlYsH0aoN4xA62OyBX9od1/\n9ee1iQct9egjnxHIA8Kq2rbpNHhLwFuvLBqNv5MIzi8MbEntUbPiezvSwteh\nz6Oxpfkn97js+VLH0SAU+O9atlNHPMZ1dVcnp/u47CMlQA5uJ/lTYmYTNbvl\nwGs4g5WdvIIPkIGvlJz0hOAnfOWpNEmRe6Wwi8Q8ovb3URLDNBKHok+Eruh5\nfjSeWA0stySlnLJ526GO/jcx43Bm27QxWZjwiMrnxaMabD3IB0totmQAVuYh\nA5zCH2fy4RHjUjPQdU4aIW1ZaMGkwR3bR4LzZFPyBj8OSuN/ckuoA+joeIzV\n7THt5b7zrMWgF2BYMahNCuSaRfCr2q1tqPLDnRFYiwWjMvjz1SaYlLnvvxow\n+fklKxnvdLsK97iRHfis0OaPRpGVeU92YeeW/Q3MsDJJVjEhbX/rh9sqcUPs\n0/5S\r\n=inf+\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=12"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "56a47ea07ed6ee0b8c260b2c581b10f4cb268085", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/string-width.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"strip-ansi": "^7.0.1", "emoji-regex": "^9.2.2", "eastasianwidth": "^0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.38.2", "ava": "^3.15.0", "tsd": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/string-width_5.1.0_1641708253934_0.8945724791832834", "host": "s3://npm-registry-packages"}}, "5.1.1": {"name": "string-width", "version": "5.1.1", "keywords": ["string", "character", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@5.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width#readme", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dist": {"shasum": "8825ca5f6b8dd5566a674e4f85001475a4b9c46e", "tarball": "https://registry.npmjs.org/string-width/-/string-width-5.1.1.tgz", "fileCount": 5, "integrity": "sha512-V4jFFhDoJzUOpG491nUrDginBuZi+wKtG+omlT5hgdBLYGPNNVT/o75JWwsSqClbrYMEV9Dvm3fXjZzxyOjk2w==", "signatures": [{"sig": "MEUCIQCoAfidDQSDK5Px8Ekn1PqU3J/Ax4fw+E01YWxgMs+IDAIgC7CT8UD+VhTaQ1aDUtHTcW9qsm+InYubdQ4cPj4ei4c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5813, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiG0GvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQew/+K9T1kfXrroCQ7m6IwC4jbkZsL9Yc2EZ/LZBkHERwmoEnXCLG\r\nT26kxmAXlAS94lzl342R0fivQyuJYyouoNUCbjzQ6DXuM7hRQkN3EqLhK5fh\r\nD8lV20dWCHnqY2gFOckJtee1eUS5XQ2fjDNnFxRVu9sb3+njH8+TpYhG4KnE\r\n5q03RrWtrPg4D5P9YDUEfQGIJ4bYKgxJyLVgAOdiW1mwpu+IRplafWqnj8Bt\r\neUoCVhykdtni+H6p8WLPyQXR4gC9DPUQgvFZRCIJheLxldyY8MnrYymAOTtB\r\n75sR6PbwBklnxoGzowHuzC20lXECRCxO4QrqSCFAmTATBOqi4fpfKzGXPzMT\r\nZRKZJbnFlCAx771Nbu6Zfh7pZCnqcUUb8MxSSujWr0gbRw2q6jpJWGwXBfFj\r\nvM4m8QS8lkWFW8bLQSv1I8L6+BRNiOH6yOS2trbDSGFUVuMK0pDcC5eJWVQU\r\nUo2pqbGG+XaqKHwoyxhHA+SNV5+SYLZIFm0OA36qIDPtvDCX15DMputMET6r\r\n6/yQzyBzpYcCir/sfWElpUDNOwwYpydyPGXPGz3pB9bbwL/6wDluLVdfoTYE\r\nuifUV6Lms8dF/yVgN6GL6T3uXXhUKfMRjr249dn/FJSJccIr7ZHLkcSK9OPD\r\nyZFi86I5/Ae9U4sWI9oAt1r4yh8OXDfC3UI=\r\n=JCyP\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=12"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "d0d53dad1b465ab4a70d90bf19bb7efb5f33bbb0", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/string-width.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "_nodeVersion": "14.19.0", "dependencies": {"strip-ansi": "^7.0.1", "emoji-regex": "^9.2.2", "eastasianwidth": "^0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.38.2", "ava": "^3.15.0", "tsd": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/string-width_5.1.1_1645953455216_0.34973692301085135", "host": "s3://npm-registry-packages"}}, "5.1.2": {"name": "string-width", "version": "5.1.2", "keywords": ["string", "character", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@5.1.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width#readme", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dist": {"shasum": "14f8daec6d81e7221d2a357e668cab73bdbca794", "tarball": "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz", "fileCount": 5, "integrity": "sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==", "signatures": [{"sig": "MEUCIFh4IjLA+aBQo7r/VZcidTSCApNLobLlmjMKnBREpog9AiEApke6FV5/3E44vAJKBbdIZ832/aqqaNMi8TFY+Px6Rwc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5781, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHIYtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqVLA/+M/8rKbhdnMv4nDBxBiwykx9T/GKuPldo2n9HpmPoqdaYbsUR\r\nKqB4g+cLAbMdr3kSMkt82Ou2Ceb8b23wBVNZbzYRQS+OKzzQKwwgi8PggU0t\r\niAm9V1k14zoV9r+hXKAG0MTp7+hEoqjjVYBEiz+z7TL5W0XR4c5iHBjUgNfg\r\n3G3flpdODgSZ4c5wgcODfSbEly/8GAh8hKzNNdYqgVVXLdPwzag4ArCP64TN\r\nBry4KwaNEguk3bqK0BGM4IZc3iz4bkZILEb5FgTUZuO2HbkBkNg0TD7rQz57\r\nTzzjnNJRzzF15NNijEVuuWj7BJBzEx7SUeZ6rBQbRAtMpZDgnYMjfnm1UOvs\r\nRLRaJt6phv/mYtLNVjW/Q5k8QAZHZr7aVAcOH314OFo1TopJwYfZfd8HdtVo\r\nYT2smUy2iVMs3KbHKQ+JKlnlA5u+x/0jv2eD4BvY1F2j86JHI8XoLT3JwYDS\r\nfZogtRhjGClSSyXyhihqkV/DlfeQxglZ7QFkAWlNt8xhUupzZ2fezqOX+15M\r\nimEdQ67SYm9akBBvxsbLvWaaMPlIdJW9fIspHwOKHctD00qjPmcwcJFKJAmt\r\noETedpGcdQHFko/wFjqSIeHThKrodnbWH5s/7C8c0Eie4Ran/NSihB4GSFgx\r\nlXX/hq0UhZhoc6EmR3kBXOealyRvPm0sA14=\r\n=ZoNR\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=12"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "9f90691968ad356c807aaad1a5ed98d795749932", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/string-width.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"strip-ansi": "^7.0.1", "emoji-regex": "^9.2.2", "eastasianwidth": "^0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.38.2", "ava": "^3.15.0", "tsd": "^0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/string-width_5.1.2_1646036524867_0.49005836872664243", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "string-width", "version": "6.0.0", "keywords": ["string", "character", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@6.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width#readme", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dist": {"shasum": "4bcf4a60f04bf104eb40b8291dd891a74d7ff45d", "tarball": "https://registry.npmjs.org/string-width/-/string-width-6.0.0.tgz", "fileCount": 5, "integrity": "sha512-u8lEUVNWDt8saL8Jvzs5qX7PH4goaVhjZ6awtN/m0OWc8xvLjlJdkt9f7YBzXsJ0o/dvAizqLUQbWof6XzMoPg==", "signatures": [{"sig": "MEQCIDeuZr+/8PRrNp1h7RktVRkP/nC2AlPSu0PbEp/lE2zLAiAqYT43wa/SQHYcx10MUPNMQTb1QRA3XstCEqNlRiFYGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5944}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=16"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "102c82c1e36b0292bc5dd2197ce5c4ad7b7534d8", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/string-width.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"strip-ansi": "^7.0.1", "emoji-regex": "^10.2.1", "eastasianwidth": "^0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.54.2", "ava": "^5.2.0", "tsd": "^0.28.1"}, "_npmOperationalInternal": {"tmp": "tmp/string-width_6.0.0_1683362389193_0.9132725656914205", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "string-width", "version": "6.1.0", "keywords": ["string", "character", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@6.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width#readme", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dist": {"shasum": "96488d6ed23f9ad5d82d13522af9e4c4c3fd7518", "tarball": "https://registry.npmjs.org/string-width/-/string-width-6.1.0.tgz", "fileCount": 5, "integrity": "sha512-k01swCJAgQmuADB0YIc+7TuatfNvTBVOoaUWJjTB9R4VJzR5vNWzf5t42ESVZFPS8xTySF7CAdV4t/aaIm3UnQ==", "signatures": [{"sig": "MEUCIQDgOJLNPMjhK8OoaeTV2mBf7ISQGlOuhi+WqTF81hyL1QIgEhzD2PemqAWOk83JNoSeWL9NcrWnwk2+/CS56LLp/CQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5820}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=16"}, "exports": "./index.js", "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "f6e70c4c8de68ea2797a8765f1c7350ef0929a83", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/string-width.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "_nodeVersion": "20.1.0", "dependencies": {"strip-ansi": "^7.0.1", "emoji-regex": "^10.2.1", "eastasianwidth": "^0.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.54.2", "ava": "^5.2.0", "tsd": "^0.28.1"}, "_npmOperationalInternal": {"tmp": "tmp/string-width_6.1.0_1683401577191_0.49559183180210153", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "string-width", "version": "7.0.0", "keywords": ["string", "character", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width", "east-asian-width"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@7.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width#readme", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dist": {"shasum": "14aa1b7aaa126d5b64fa79d3c894da8a9650ba06", "tarball": "https://registry.npmjs.org/string-width/-/string-width-7.0.0.tgz", "fileCount": 5, "integrity": "sha512-GPQHj7row82Hjo9hKZieKcHIhaAIKOJvFSIZXuCU9OASVZrMNUaZuz++SPVrBjnLsnk4k+z9f2EIypgxf2vNFw==", "signatures": [{"sig": "MEUCIBkzPj/ThAp7pT74osrspBLy2Q1CwWgdVbyAc6j7TJ3XAiEAjjw5TfFh0i5cNH2WHyD6tsmZyHLfoLoTnlXo/RkTnZo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6505}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "210a997cbee0cc6290307809a494d4d35658f6d9", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/string-width.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"strip-ansi": "^7.1.0", "emoji-regex": "^10.3.0", "get-east-asian-width": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^5.3.1", "tsd": "^0.29.0"}, "_npmOperationalInternal": {"tmp": "tmp/string-width_7.0.0_1698495429861_0.6900918547788994", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "string-width", "version": "7.1.0", "keywords": ["string", "character", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width", "east-asian-width"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "string-width@7.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/string-width#readme", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "dist": {"shasum": "d994252935224729ea3719c49f7206dc9c46550a", "tarball": "https://registry.npmjs.org/string-width/-/string-width-7.1.0.tgz", "fileCount": 5, "integrity": "sha512-SEIJCWiX7Kg4c129n48aDRwLbFb2LJmXXFrWBG4NGaRtMQ3myKPKbwrD1BKqQn74oCoNMBVrfDEr5M9YxCsrkw==", "signatures": [{"sig": "MEQCIFvsvMb+WbMDwEGL/3vwTBuLyoTa3Q517J7fzcVwKbLzAiBRuDykBGKz6Fin4NqM0DKwvKDpMbXdr8q6jZi26kxZgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6612}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sponsors/sindresorhus", "gitHead": "8e3727e2382026f237ffb8fc3dea7dbed8a0eb20", "scripts": {"test": "xo && ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/string-width.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Get the visual width of a string - the number of columns required to display it", "directories": {}, "sideEffects": false, "_nodeVersion": "21.5.0", "dependencies": {"strip-ansi": "^7.1.0", "emoji-regex": "^10.3.0", "get-east-asian-width": "^1.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.56.0", "ava": "^5.3.1", "tsd": "^0.29.0"}, "_npmOperationalInternal": {"tmp": "tmp/string-width_7.1.0_1705920485261_0.17125484808711855", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "string-width", "version": "7.2.0", "description": "Get the visual width of a string - the number of columns required to display it", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/string-width.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["string", "character", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width", "east-asian-width"], "dependencies": {"emoji-regex": "^10.3.0", "get-east-asian-width": "^1.0.0", "strip-ansi": "^7.1.0"}, "devDependencies": {"ava": "^5.3.1", "tsd": "^0.29.0", "xo": "^0.56.0"}, "_id": "string-width@7.2.0", "gitHead": "ac09208fd52063c2ffaa4396c9b339fec05cab67", "types": "./index.d.ts", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "homepage": "https://github.com/sindresorhus/string-width#readme", "_nodeVersion": "18.20.2", "_npmVersion": "10.6.0", "dist": {"integrity": "sha512-tsaTIkKW9b4N+AEj+SVA+WhJzV7/zMhcSu78mLKWSk7cXMOSHsBKFWUs0fWwq8QyK3MgJBQRX6Gbi4kYbdvGkQ==", "shasum": "b5bb8e2165ce275d4d43476dd2700ad9091db6dc", "tarball": "https://registry.npmjs.org/string-width/-/string-width-7.2.0.tgz", "fileCount": 5, "unpackedSize": 7773, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE+qFjJEflSd5eYtVryvQT4cd1nIj+bQUaQXzN+OjzH8AiBmOvV0KS1FDTUTb7RoHt3GeghCtLjBzfFQps5oLYrQUA=="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/string-width_7.2.0_1719758896481_0.648876534343015"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-07-16T22:08:17.757Z", "modified": "2024-06-30T14:48:16.809Z", "1.0.0": "2015-07-16T22:08:17.757Z", "1.0.1": "2015-07-20T01:31:09.758Z", "1.0.2": "2016-08-14T15:23:54.869Z", "2.0.0": "2016-09-22T06:54:45.828Z", "2.1.0": "2017-06-26T16:15:09.531Z", "2.1.1": "2017-07-18T11:09:15.295Z", "3.0.0": "2018-12-15T14:14:40.848Z", "3.1.0": "2019-03-08T08:22:47.824Z", "4.0.0": "2019-03-18T08:23:48.708Z", "4.1.0": "2019-03-31T12:12:25.885Z", "4.2.0": "2019-11-11T16:06:36.863Z", "4.2.1": "2021-02-26T09:32:11.791Z", "4.2.2": "2021-02-28T14:22:14.935Z", "5.0.0": "2021-04-16T07:35:43.206Z", "5.0.1": "2021-09-13T16:35:04.956Z", "4.2.3": "2021-09-23T17:17:21.341Z", "5.1.0": "2022-01-09T06:04:14.102Z", "5.1.1": "2022-02-27T09:17:35.378Z", "5.1.2": "2022-02-28T08:22:05.036Z", "6.0.0": "2023-05-06T08:39:49.319Z", "6.1.0": "2023-05-06T19:32:57.382Z", "7.0.0": "2023-10-28T12:17:10.078Z", "7.1.0": "2024-01-22T10:48:05.404Z", "7.2.0": "2024-06-30T14:48:16.656Z"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/string-width.git"}, "keywords": ["string", "character", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width", "east-asian-width"], "license": "MIT", "homepage": "https://github.com/sindresorhus/string-width#readme", "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "readme": "# string-width\n\n> Get the visual width of a string - the number of columns required to display it\n\nSome Unicode characters are [fullwidth](https://en.wikipedia.org/wiki/Halfwidth_and_fullwidth_forms) and use double the normal width. [ANSI escape codes](https://en.wikipedia.org/wiki/ANSI_escape_code) are stripped and doesn't affect the width.\n\nUseful to be able to measure the actual width of command-line output.\n\n## Install\n\n```sh\nnpm install string-width\n```\n\n## Usage\n\n```js\nimport stringWidth from 'string-width';\n\nstringWidth('a');\n//=> 1\n\nstringWidth('古');\n//=> 2\n\nstringWidth('\\u001B[1m古\\u001B[22m');\n//=> 2\n```\n\n## API\n\n### stringWidth(string, options?)\n\n#### string\n\nType: `string`\n\nThe string to be counted.\n\n#### options\n\nType: `object`\n\n##### ambiguousIsNarrow\n\nType: `boolean`\\\nDefault: `true`\n\nCount [ambiguous width characters](https://www.unicode.org/reports/tr11/#Ambiguous) as having narrow width (count of 1) instead of wide width (count of 2).\n\n> Ambiguous characters behave like wide or narrow characters depending on the context (language tag, script identification, associated font, source of data, or explicit markup; all can provide the context). **If the context cannot be established reliably, they should be treated as narrow characters by default.**\n> - http://www.unicode.org/reports/tr11/\n\n##### countAnsiEscapeCodes\n\nType: `boolean`\\\nDefault: `false`\n\nWhether [ANSI escape codes](https://en.wikipedia.org/wiki/ANSI_escape_code) should be counted.\n\n## Related\n\n- [string-width-cli](https://github.com/sindresorhus/string-width-cli) - CLI for this module\n- [string-length](https://github.com/sindresorhus/string-length) - Get the real length of a string\n- [widest-line](https://github.com/sindresorhus/widest-line) - Get the visual width of the widest line in a string\n- [get-east-asian-width](https://github.com/sindresorhus/get-east-asian-width) - Determine the East Asian Width of a Unicode character\n", "readmeFilename": "readme.md", "users": {"gzg1500521074": true, "scottfreecode": true, "shrimpseaweed": true}}