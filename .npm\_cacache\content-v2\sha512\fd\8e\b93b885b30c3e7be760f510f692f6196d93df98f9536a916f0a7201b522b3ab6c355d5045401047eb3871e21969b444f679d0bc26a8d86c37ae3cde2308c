{"_id": "@types/express", "_rev": "871-fd1c7aa16e0a3540e52f7046b5298820", "name": "@types/express", "dist-tags": {"ts2.1": "4.0.35", "ts2.0": "4.0.35", "ts2.2": "4.17.0", "ts2.4": "4.17.2", "ts2.6": "4.17.2", "ts2.5": "4.17.2", "ts2.7": "4.17.2", "ts2.3": "4.17.2", "ts2.9": "4.17.6", "ts2.8": "4.17.6", "ts3.0": "4.17.7", "ts3.1": "4.17.8", "ts3.2": "4.17.9", "ts3.3": "4.17.10", "ts3.4": "4.17.11", "ts3.5": "4.17.12", "ts3.6": "4.17.13", "ts3.9": "4.17.13", "ts3.8": "4.17.13", "ts3.7": "4.17.13", "ts4.0": "4.17.13", "ts4.1": "4.17.14", "ts4.4": "4.17.17", "ts4.3": "4.17.17", "ts4.2": "4.17.17", "ts4.6": "4.17.21", "ts4.7": "4.17.21", "ts4.5": "4.17.21", "ts4.8": "5.0.0", "ts4.9": "5.0.0", "ts5.0": "5.0.1", "ts5.6": "5.0.3", "ts5.3": "5.0.3", "ts5.5": "5.0.3", "ts5.9": "5.0.3", "ts5.2": "5.0.3", "ts5.4": "5.0.3", "ts5.7": "5.0.3", "ts5.1": "5.0.3", "ts5.8": "5.0.3", "latest": "5.0.3"}, "versions": {"4.0.16-alpha": {"name": "@types/express", "version": "4.0.16-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "_id": "@types/express@4.0.16-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "577a09ecc7f91c076820ea9e683ef56bc04c99cc", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.0.16-alpha.tgz", "integrity": "sha512-dWA4rzmMAnWavHg/E5yQnpHKlhlrBiOjxqsuCUTgX8xnftAZCJCo44tnB30jv7cWkNv0+moXuqRLcRzSs0vzhg==", "signatures": [{"sig": "MEYCIQDx0VesDBDWriGszrJdRboYyo5PtwAb1NCzTxeNbiUp/AIhANqR9I9XZ6l3naePjxPKVEEr1agXTdd9q5JkaTanX3wW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\express", "_shasum": "577a09ecc7f91c076820ea9e683ef56bc04c99cc", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\express", "_npmVersion": "3.8.2", "description": "Type definitions for Express 4.x from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"../serve-static": "*", "express-serve-static-core": "*"}, "_npmOperationalInternal": {"tmp": "tmp/express-4.0.16-alpha.tgz_1463460772751_0.03159188851714134", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.17-alpha": {"name": "@types/express", "version": "4.0.17-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "_id": "@types/express@4.0.17-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "4ad1f6b1c3aa7093d91a45cca8ba08171bafd545", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.0.17-alpha.tgz", "integrity": "sha512-SLzBFBg/n51W8DWJTxvhd8Z1TTHf5uuoIklJ9rfo1UnpVW8mL6DYtd920OgoH82u3cfX+L+XGD/godCkV4a65g==", "signatures": [{"sig": "MEYCIQDsdIrmNmmmU+jmv1XdUr0fNJLpt1cgvbx1DSRTe/GE8gIhALnEEUIWh7XlrI0jAVXhLT5k3JLTUckT70caYxeTQr5g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\express", "_shasum": "4ad1f6b1c3aa7093d91a45cca8ba08171bafd545", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\express", "_npmVersion": "3.8.2", "description": "Type definitions for Express 4.x from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"../serve-static": "*", "express-serve-static-core": "*", "@types/express-serve-static-core": "*"}, "_npmOperationalInternal": {"tmp": "tmp/express-4.0.17-alpha.tgz_1463690924707_0.6485122456215322", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.22-alpha": {"name": "@types/express", "version": "4.0.22-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "_id": "@types/express@4.0.22-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "119db67e8d9bbc079f533c41f4af91c6af9ebdbd", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.0.22-alpha.tgz", "integrity": "sha512-pT9H6i+Se+mn+v/UubGtr4BLQ8TKD/MIkbHiSdlVZMCY1xG39DQ+AwpA6gA/K6nGpBngW1T0kvngfcom+GRA6A==", "signatures": [{"sig": "MEYCIQDviMJZaJl6v2Vf4yEnd8IWHQxi337O3Lew1VInr9oZ6wIhAP56pSnaKPveMEL5Xc36ho3SK7bJEpv0ZRiWerJV8gzn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\express", "_shasum": "119db67e8d9bbc079f533c41f4af91c6af9ebdbd", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\express", "_npmVersion": "3.8.2", "description": "TypeScript definitions for Express 4.x", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"@types/express-serve-static-core": "*"}, "_npmOperationalInternal": {"tmp": "tmp/express-4.0.22-alpha.tgz_1463772782709_0.07826171116903424", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.23-alpha": {"name": "@types/express", "version": "4.0.23-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "_id": "@types/express@4.0.23-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "c1300873e38536ac37b1f0075a5bbbd4c5973f92", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.0.23-alpha.tgz", "integrity": "sha512-PMuIez3UN0wbjd2Ttil43F7X1SJFbW6JIzE51mwTW7/Lbrh8dkPjkDlDrqAKWVJPqyn1PB7jPw6OugPSqnRf2Q==", "signatures": [{"sig": "MEUCICbMO9nzBaw1GnYH0UwKX1bYkTbbjoDOfytD2c1JeXNaAiEAkCdoFx1XE08OG1m8crbgt+4Y7MMGYz9gzMmPOeMrapM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\express", "_shasum": "c1300873e38536ac37b1f0075a5bbbd4c5973f92", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\express", "_npmVersion": "3.8.2", "description": "TypeScript definitions for Express 4.x", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"@types/express-serve-static-core": "*"}, "_npmOperationalInternal": {"tmp": "tmp/express-4.0.23-alpha.tgz_1464151875838_0.9312994808424264", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.24-alpha": {"name": "@types/express", "version": "4.0.24-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "_id": "@types/express@4.0.24-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "eca7932141d1013cefe2ee714724a98de11a7164", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.0.24-alpha.tgz", "integrity": "sha512-qgps/sXVp28ljrhxppCKKfU6nxX2AiOC1c7iNq4weuUxeF83chz2mqICOcUxU4cAz1EMmGtj8/V6+JsoTx/zFQ==", "signatures": [{"sig": "MEYCIQCZxcyXFCHSrBWClANY05TQUygVGYhztvFFdxqbvghTWQIhAIi1jpNGNReTWGhy/t7dxfsN470nh9idr6USo33xmXtN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\express", "_shasum": "eca7932141d1013cefe2ee714724a98de11a7164", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\express", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.9.5", "description": "TypeScript definitions for Express 4.x", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {"@types/express-serve-static-core": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/express-4.0.24-alpha.tgz_1467400924420_0.17103134817443788", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.25-alpha": {"name": "@types/express", "version": "4.0.25-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "_id": "@types/express@4.0.25-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "2d7c3f53721727692f84ce6bb8da5a7930ece460", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.0.25-alpha.tgz", "integrity": "sha512-eyZ7dF5I3FNgKhI1AuO9bXiXYTAAzDmAgqpB/xPqEEH7O5CpjCfMRAACRk1IEKr7vHjyzP6noVlTggVTvQ3fpg==", "signatures": [{"sig": "MEUCIFFPh7GMfRiwMZYBCwMxHjVUn2F7Q6G2d/DpjHmvu2UGAiEAjyLNdMLqtXpqFohZJlCNrxKGNdY4+ruSIj50YwlhTrw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\express", "_shasum": "2d7c3f53721727692f84ce6bb8da5a7930ece460", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\express", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for Express 4.x", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/express-serve-static-core": "4.0.23-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/express-4.0.25-alpha.tgz_1467412765326_0.001893854234367609", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.26-alpha": {"name": "@types/express", "version": "4.0.26-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "_id": "@types/express@4.0.26-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "81ef5392a0bfe5a5918af2b2a8dd59bf411ed911", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.0.26-alpha.tgz", "integrity": "sha512-b8oenXypKJ1p907l1ShozM4wz889WR0RTHyLiO/0Ho2piqgttWbJK34uZjQg2t0BU2J4h7zaBpyxAoul5LqSsA==", "signatures": [{"sig": "MEUCIQC4wdkqXrFSvIJK+XOjSyG/mIDm+fpTE7ZeFXdEimNYuwIgQN6vwPkJ6AOvDub9bWnb392m5LwsoeFToNaGHsxcTZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\express", "_shasum": "81ef5392a0bfe5a5918af2b2a8dd59bf411ed911", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\express", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for Express 4.x", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/express-serve-static-core": "4.0.24-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/express-4.0.26-alpha.tgz_1467425956813_0.3038533120416105", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.27-alpha": {"name": "@types/express", "version": "4.0.27-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "_id": "@types/express@4.0.27-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "675233fb8856e8480d93e88ba34e4f8f1ae4cd1f", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.0.27-alpha.tgz", "integrity": "sha512-0ZyhrEDShkccuU8uxZ22AGM7QvkJbVM4Uv7/U6qLkfUdjRCU5upzY+dZhTsuMLlJUFEeV+QJyAiLTBwjCE5uPQ==", "signatures": [{"sig": "MEYCIQCkQE5sw3JnwOYLYu/aG0SRbT0SZCWTqzZrTMi8eY+1NgIhAMOQenDT5PlyCu4ijMJefPOdNF7q+xIF7egViumfDg0z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\express", "_shasum": "675233fb8856e8480d93e88ba34e4f8f1ae4cd1f", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\express", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for Express 4.x", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/express-serve-static-core": "4.0.26-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/express-4.0.27-alpha.tgz_1467588064810_0.41114497371017933", "host": "packages-16-east.internal.npmjs.com"}}, "4.0.28-alpha": {"name": "@types/express", "version": "4.0.28-alpha", "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "_id": "@types/express@4.0.28-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "158e746f042f417e7cb2325592ba118f8be6369c", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.0.28-alpha.tgz", "integrity": "sha512-+I824G068p+2+NrDE+l4PE/gOV8ES0Z7aOl44ygG78VP/DFUqINBhgqzN8suSudIhKBRTY2VybtK1ta6W30OYQ==", "signatures": [{"sig": "MEYCIQD+PRXKYI6T2aSzFOxuNh+0GyUW+WDokkGmbyMboffldAIhAO0SzhhOXAlH/HwCABPeWCLnGhMsuCZqPTEiTnOcxtxQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\express", "_shasum": "158e746f042f417e7cb2325592ba118f8be6369c", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\express", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for Express 4.x", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/serve-static": "1.7.25-alpha", "@types/express-serve-static-core": "4.0.26-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/express-4.0.28-alpha.tgz_1467840710458_0.7282508085481822", "host": "packages-16-east.internal.npmjs.com"}}, "4.0.29": {"name": "@types/express", "version": "4.0.29", "author": {"name": "<PERSON>", "email": "https://github.com/b<PERSON><PERSON><PERSON>/"}, "license": "MIT", "_id": "@types/express@4.0.29", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "1b72ccf48e871e015be7184fe39c3bcd1a3a56d6", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.0.29.tgz", "integrity": "sha512-XfrKW4C4huxUqcckQ2gP5+Ef6D3viUTlKO+1bW6tULmFHi9TDj/Tu+H5Fnj9KRYQhPPQ3JMtzOFH3ICGHdtB6g==", "signatures": [{"sig": "MEYCIQD6C3RcK2ilK1yczNLnNCuGHvMRcwg2M+eWTykaCsgySwIhAKfjW3/6uSsMmsY/DzhdVcKl5h4odJ5GWJMxMZ5K/MV3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\express", "_shasum": "1b72ccf48e871e015be7184fe39c3bcd1a3a56d6", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\express", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for Express 4.x", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"@types/serve-static": "1.7.*", "@types/express-serve-static-core": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/express-4.0.29.tgz_1468506756504_0.2337088098283857", "host": "packages-16-east.internal.npmjs.com"}}, "4.0.30": {"name": "@types/express", "version": "4.0.30", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "license": "MIT", "_id": "@types/express@4.0.30", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "09014ceae4e738a55ffad6884d7bcb8bf14c6713", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.0.30.tgz", "integrity": "sha512-YcmWccxwviuH99ETe9W8NFtqZvefdzuXzAzCTuop5auYvGkqc5auaDOL9QKT9Vn6WMZfVKStuAH33QR/vvh93w==", "signatures": [{"sig": "MEUCIH3PdilimLlrnYofgXy1ZCnvrIXFLhAHbqGcB31CHeRzAiEApONmT27VbW6mHTBRS7TlcNjbPjxN7oGai1untCMxLMk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express 4.x", "directories": {}, "dependencies": {"@types/serve-static": "1.7.*", "@types/express-serve-static-core": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/express-4.0.30.tgz_1470153128322_0.21815698291175067", "host": "packages-16-east.internal.npmjs.com"}}, "4.0.31": {"name": "@types/express", "version": "4.0.31", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "license": "MIT", "_id": "@types/express@4.0.31", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "f194ad070b32a7ab7115085203b6fcc97b51ae29", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.0.31.tgz", "integrity": "sha512-yOwdVd3j6Wy2jf//NIbM8YHw4feYTSJphcIU3Ooybo+TpV7XX3we1FfLvJuHNJtoIkhJj3jpCYgB/orvHauhcA==", "signatures": [{"sig": "MEYCIQDGdHmc0pNu6S39viGl7fPTm7VuDXLY0Wmb2tLZd/AwrgIhAP/JR0rPxLquQbd1FZKS9/39mhwbzSWi2ptYVpdr1Loh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express 4.x", "directories": {}, "dependencies": {"@types/serve-static": "1.7.*", "@types/express-serve-static-core": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/express-4.0.31.tgz_1471620292492_0.2945292096119374", "host": "packages-16-east.internal.npmjs.com"}}, "4.0.32": {"name": "@types/express", "version": "4.0.32", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "license": "MIT", "_id": "@types/express@4.0.32", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "56c1772d9f14219f2afadd2ae9bbe005744aa020", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.0.32.tgz", "integrity": "sha512-VGWBtVEWpnjJzZrteai1zssODBkv/arv5rKcKiUCzb7bz14n8Q1R18xOwPEjFCOZWiwE/UOQ0vX4gu5hD/SNlg==", "signatures": [{"sig": "MEQCIG4ypE3KP+SkZ7J5QGZX/M/wEhMBc2mhNWFQq0bc3n7XAiB/+npJtqr+loQkIrpZYATh0FlX+UNHpK0vjwaOQ1P9ng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express 4.x", "directories": {}, "dependencies": {"@types/serve-static": "1.7.*", "@types/express-serve-static-core": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/express-4.0.32.tgz_1472150494079_0.7518135118298233", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.33": {"name": "@types/express", "version": "4.0.33", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "license": "MIT", "_id": "@types/express@4.0.33", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "9212b6c67e02e09ee9f80740ded04306050739ab", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.0.33.tgz", "integrity": "sha512-FvkfyDA7nImXjJ9AObcBeXK/eyWhSHC+r9xJtqKBK/G4UqfXkELSDmxOOo1YT4INRvEqL/RgqziLsnFGn1F4JA==", "signatures": [{"sig": "MEUCIQD8TqFnPxDcEBhKkTK7WzSgRhVJzezrAhyFXIDcw3LV3QIgUdGDUR8B0jEtjWNVK+me8yTYMEzTYfMYcmCe+YUHuoc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express 4.x", "directories": {}, "dependencies": {"@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "_npmOperationalInternal": {"tmp": "tmp/express-4.0.33.tgz_1474306218987_0.4083134310785681", "host": "packages-16-east.internal.npmjs.com"}, "typesPublisherContentHash": "f90dd99bbc97c7740fc818b879061f6a5c5c49275069d6380ccfa0a65ccf077a"}, "4.0.34": {"name": "@types/express", "version": "4.0.34", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "license": "MIT", "_id": "@types/express@4.0.34", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "cdc0afd69d70d2295b81b3aa47f26f672afcde1c", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.0.34.tgz", "integrity": "sha512-EME+zSrClFeh1WxiHBnjsNE8J1jxkUhgr+K0Dmj6bMR+UGz6cxhjXjTMDWUaI+E0p3sHB/DPARXeKM0hR1gwCg==", "signatures": [{"sig": "MEUCIBqJtUkqbolSP6iCWl1z3lqcxLvNtmbH/RnymFz9hupDAiEAiUCTMkBrAU3Q8qgC1E4TzncxiiSXBXP5zwZhpPfMVmU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express 4.x", "directories": {}, "dependencies": {"@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "peerDependencies": {}, "_npmOperationalInternal": {"tmp": "tmp/express-4.0.34.tgz_1479221773267_0.6036220847163349", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "cb2883774f9ff8bf15e334e96c3f98fd01820ea5da7fe8c99b6a1131b8816784"}, "4.0.35": {"name": "@types/express", "version": "4.0.35", "author": "<PERSON> <https://github.com/boris<PERSON><PERSON>/>", "license": "MIT", "_id": "@types/express@4.0.35", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "6267c7b60a51fac473467b3c4a02cd1e441805fe", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.0.35.tgz", "integrity": "sha512-B1BCEmQLo4Z5VM6SZhmHkYA6z8z8M2v+Wq3mySX7rZVhb0dySOzqbyaCV6CZI1NZkb0PO7Zr0TkVQCfXqOZ9ww==", "signatures": [{"sig": "MEYCIQCLGRnbpJF7XTXAJ3xd+WXRzVD56Myx+W9dShdJ9OlFwAIhAJAO1AejXP/OvWtCzjvBtCBwDYuSMTSe7pis1LN1ggH6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/express-4.0.35.tgz_1484719613769_0.4645449265372008", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "c3c508a3f9eda6225a5545f1006a32e40f1f377672b30c5e4548d9763dbef964"}, "4.0.36": {"name": "@types/express", "version": "4.0.36", "license": "MIT", "_id": "@types/express@4.0.36", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON><PERSON>/", "name": "<PERSON>"}], "dist": {"shasum": "14eb47de7ecb10319f0a2fb1cf971aa8680758c2", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.0.36.tgz", "integrity": "sha512-bT9q2eqH/E72AGBQKT50dh6AXzheTqigGZ1GwDiwmx7vfHff0bZOrvUWjvGpNWPNkRmX1vDF6wonG6rlpBHb1A==", "signatures": [{"sig": "MEUCIQDhP7kfvU9c3WKCEP4OLUbvs7GauwLzXGwEzJWcZlbNEQIgK/R39LcpW4bpsA/fsUM7ZkWIPapSSxKlWi/WqArFvsU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/express-4.0.36.tgz_1497557617706_0.2121476698666811", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "effdeb917f7cb7d5aa60f870d758d6a1b228c3014a095584f3fd722612116008"}, "4.0.37": {"name": "@types/express", "version": "4.0.37", "license": "MIT", "_id": "@types/express@4.0.37", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>"}], "dist": {"shasum": "625ac3765169676e01897ca47011c26375784971", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.0.37.tgz", "integrity": "sha512-tIULTLzQpFFs5/PKnFIAFOsXQxss76glppbVKR3/jddPK26SBsD5HF5grn5G2jOGtpRWSBvYmDYoduVv+3wOXg==", "signatures": [{"sig": "MEYCIQCyCH0r73H7ah0dtjzS0Xtgajua8sNexRIGpPcg0iiNPgIhAPiJ4ixVKLjRVjoH+l/69LM74yncyclvxVRGd78f9Gji", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/express-4.0.37.tgz_1503352300083_0.37321534869261086", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ee3f3950a5c96d4bc07687b8843650db92fe1f16e5e011958979b9b96518e44f"}, "4.0.38": {"name": "@types/express", "version": "4.0.38", "license": "MIT", "_id": "@types/express@4.0.38", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "500e0524cb57ea8e3119efd5ca7ab8fca29ad9a5", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.0.38.tgz", "integrity": "sha512-1HfDZFzzmcusHNTl/hRhrLIoVGZt3yqJVIZtMaQm/K14naptdjU9fEeXl1gCDzE2E9BqnoL6B5oidi7da3etbQ==", "signatures": [{"sig": "MEUCIHPDaSOIYUVehWgzPzlY+2c12YdBpoO0056Jo5+ToZx0AiEAkfP1erL06BXWq7/u+o7kVl4EHTxX4CeLFQFJ3EjM83I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/express-4.0.38.tgz_1508890926288_0.7479865993373096", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "32dc234bc1b9e25982326a18a6f8d09e9fd40e678721ea07f2d82d89d8ec3f45"}, "4.0.39": {"name": "@types/express", "version": "4.0.39", "license": "MIT", "_id": "@types/express@4.0.39", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "1441f21d52b33be8d4fa8a865c15a6a91cd0fa09", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.0.39.tgz", "integrity": "sha512-dBUam7jEjyuEofigUXCtublUHknRZvcRgITlGsTbFgPvnTwtQUt2NgLakbsf+PsGo/Nupqr3IXCYsOpBpofyrA==", "signatures": [{"sig": "MEUCIQDEVpZf7+uVkBoU9tfBWCtCsxwKwLTPY6Q/jJ2wOD3dxAIgWV5NqQTng+VVSnW2rExQ4MRECkQXyMju2yCExyu5+Y0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/express-4.0.39.tgz_1509046329544_0.5551460727583617", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "51446cee04134591547aa8f623ea4a6dabeccd7870b7684f00dccb7d4f289bc6"}, "4.11.0": {"name": "@types/express", "version": "4.11.0", "license": "MIT", "_id": "@types/express@4.11.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "234d65280af917cb290634b7a8d6bcac24aecbad", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.11.0.tgz", "integrity": "sha512-N1Wdp3v4KmdO3W/CM7KXrDwM4xcVZjlHF2dAOs7sNrTUX8PY3G4n9NkaHlfjGFEfgFeHmRRjywoBd4VkujDs9w==", "signatures": [{"sig": "MEUCIQDIuBxpH3afRfxR4hwcB/q1mDftt7IG2FhR1ihGEgz4aAIgIuPGsnT5vGAF4ytW1RqXSt302kOtWCFMbYt4UR2THeM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/express-4.11.0.tgz_1513781512181_0.3758411444723606", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "6e3a4d9ef67dc6ae8499529a28f059f784fd4fdb3f155902a4b2f552880a144e"}, "4.11.1": {"name": "@types/express", "version": "4.11.1", "license": "MIT", "_id": "@types/express@4.11.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "f99663b3ab32d04cb11db612ef5dd7933f75465b", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.11.1.tgz", "integrity": "sha512-ttWle8cnPA5rAelauSWeWJimtY2RsUf2aspYZs7xPHiWgOlPn6nnUfBMtrkcnjFJuIHJF4gNOdVvpLK2Zmvh6g==", "signatures": [{"sig": "MEUCIGigucFI6RvP40Gl6ExQLBxv2y2+BWz5DcTgiLy8mEaRAiEA9E297+NrpLLOo3HQLOBSNaY5zCfMgAdrNTIO9M6CWuo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/express-4.11.1.tgz_1517523996588_0.7968565141782165", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "bea29462c13a5204859d5ac3bb917bae720aff3de620dd36b71440b71556efa5"}, "4.16.0": {"name": "@types/express", "version": "4.16.0", "license": "MIT", "_id": "@types/express@4.16.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "6d8bc42ccaa6f35cf29a2b7c3333cb47b5a32a19", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.16.0.tgz", "fileCount": 4, "integrity": "sha512-TtPEYumsmSTtTetAPXlJVf3kEqb6wZK0bZojpJQrnD/djV4q1oB6QQ8aKvKqwNPACoe02GNiy5zDzcYivR5Z2w==", "signatures": [{"sig": "MEUCICzzbF0hSEQ7LKjxxOGpfWwQjPAQ/C5sTb0f6jRzPh6iAiEAgDPkpFL/qDDPI1VbSyD1gOutNGb0eROJRW21eqi7a9g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbFdNQCRA9TVsSAnZWagAAbuIP/iqKvUBuKefDYz+Sn8G4\nB/jZqQ67NzjrgyZTrmxetFvB/Yg1QOAqeVj8WH4qZZRSrL2DRCM2Xh+/MolG\nSS/vejLFzrSVgl74hTs9ZwHaJmOrOIwcdC+iQkJq4P8Do+87YisgZYBFjVsd\n3jQNRLXDzgjp+rolcLshVHGAxXyXGVHUQTwwVqhp0TOjmsgUqcKLmAboLelq\n0wsW+X89IkhlV++RcQDIrQJiOAQ6PfYdzbQvSEE+qm1B/uRneIj8gCrjaeQI\nCMS9+DDQQo1EaLb+piVF7JKxlxxFun6N5Qap7Tjb3bbdyh5E3kli85o7LAo6\nj2enBb6qcAhN6eTHRmSedukZTbc1Gy5QvCZxOvrHLAUD8PFjHIreJDZlo6qs\nyeBoBb1W9zE0TQlS9S4wTXke+sprgMa7bDSE2l0uVxXZ7smS581WcCXGK1lR\ngxhEQBBcl8oDPx9RbOhdvXlToqE2SHi3yB2NmX84uwfjX9t1SoxmU2Xj17hj\nhxlIgjOW0fT8iOBnwogO4zybApPQbVTUUDS5yh4LFdlchSieHDUw/KDCJhL6\n9k8RqS5tDc3E2yd1ESzu2Ztbz2zmtNXc0Z1d3wesB8XINhOGdBSumZJM46+M\ni7zz7Ydm6jYt1zurJcTxPAxtq/9B0ZYWKL/ie7kMZFMn/DrgjL/2JJUrhhQp\nEnKG\r\n=a1zh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/express_4.16.0_1528157007702_0.03606732189482997", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ad0e06d4038090c42b1301290bf770b34ddab9a112d674ea06b9d70c51262116"}, "4.16.1": {"name": "@types/express", "version": "4.16.1", "license": "MIT", "_id": "@types/express@4.16.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "d756bd1a85c34d87eaf44c888bad27ba8a4b7cf0", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.16.1.tgz", "fileCount": 4, "integrity": "sha512-V0c<PERSON><PERSON>ow23WeyblmACoxbHBu2JKlE5TiIme6Lem14FnPW9gsttyHtk6wq7njcdIWH1njAaFgR8gW09lgY98gQg==", "signatures": [{"sig": "MEYCIQCQ81D56Agh/z1o/IGWFIxmXLWkeV4DBlfsg97RIc5P+wIhAIDDj8q08jvBGcSK7d+4JVB0RzvEcUC7ToB312jbL4gf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcR1gfCRA9TVsSAnZWagAApeEQAJ2iv1LP8v4GTGirYQhz\nWBvDlmV8+b8ymd5dylVOBHBbBJ3bvI/o7LWwmrE3eSMr9mh9pZY08HdfyHSU\n7yCYRbK2x04CHsPVdufVgsiZRnmPicpxJqPFuZ4NYml6IIcFiM706VjfcYtU\nsEgQuwCrvVI0/aFpmWqagxpawVbQoQV+jetNYi7zDFtDRVVE8Qy5TJP8EtXz\n7Qd3N+09tu3f0TvMi9GnSenMklP8xDpAgTWMF+OYiWWIBkZhRPbcPXsl5tka\n3uPHLAgrYxcgi2fuQJ1U+1oYlZC9mpiAOG+PMDk8Y4kiW/WjdJoc6oc4FM14\ncFm4AfD0Q0j2lWU9nLrCHGCHddUs0e0zGQLRNnJiJzlnluOqHRNjf3B/tiSn\n1vZFp0dCTs32c3x0T9gjLuPku6KCmNoKH2dOT/kM5TWtrikmUoz7DnAPLdyW\nTeLClFjBEn21rKOc7PW3Zuo3XzhMaOnJusq52jxV5MyuaR0DycXrqrNl0pnU\nLGG3HyhkLQ7Mnx1zsbBVL6SkVcRpUHEh1z7liRbHSDrCi38kfwJEWQ8k7e5Q\nfGT8Qj3H+h2UJ0gNxSsnDIXpskVpmzzX2NW6Bf20DEs/fNcUMfR4LMyz/tJv\n+6Iy6/v+NyQi80CGwtCfbngBpkXv6wX6aDyapbsH8In9UFUCt4Pz+lr8hTHL\nkFB0\r\n=NhXz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/express_4.16.1_1548179486948_0.8355080677906364", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "dd6d4d84c129accd75bc39e8829c791da5dea05f65a0063887b77b72e2f54042"}, "4.17.0": {"name": "@types/express", "version": "4.17.0", "license": "MIT", "_id": "@types/express@4.17.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "49eaedb209582a86f12ed9b725160f12d04ef287", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.0.tgz", "fileCount": 4, "integrity": "sha512-CjaMu57cjgjuZbh9DpkloeGxV45CnMGlVd+XpG7Gm9QgVrd7KFq+X4HY0vM+2v0bczS48Wg7bvnMY5TN+Xmcfw==", "signatures": [{"sig": "MEYCIQD5HYtUH1GuiSIUzTgDK0BRz9pror2z2rvbXeoPUWJUigIhAO3aKbG1XstKIsoMGPQzRtU/8iOUbFOADB5yTCUsskxG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6093, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc9tiQCRA9TVsSAnZWagAAuzwP/2LmNavOUwt80M+kxWjI\nrv9yVEZ0NnYZP0t1bPkwNQbPc+3SnSr3D16N54uBUEWybiXgL5tldSQJ7XSX\nh6mxFfh3YNoZOWdjIDyPHTMCmC323+dJ4AAmwRE3h07+SU1hz9bk3Va84tsC\n11PghsXjePHrilst8eT2bUGyb204jVdlBC0uXIhR14sG5OGi+JWmcMfbQeNX\n7tcCqOrJ5MIumbzIZNW/+AouUQCR4wZodcFE7feqHQwjucSzRt/N+tjeLqmZ\nvuJ6PHmhoZlE9UF2AKlahfTg2qc4Gprj2xpSLPV7I3bve8rkEVtLOCy8g4wD\nBOgKMMIasXi7mrjkYrobPnsDsyR14DKRtSIZSq4U9ukp9wRsutbIizvXN7n1\nXOlkjkuma/rnoOmrhxGLpRSrCkUhYjiqqaKI616VKkE+jg7Ej3DIPU/zeOs1\nkTB88j1y8pTJe2lw4Nm4ck8c3e6OmqDAix3kJD/YjJ5kYJOqkPsb9st6MIvS\nMIVQm9YusDeKlX2UR+CETuuH4ZnY+Ev2pcvIDEWmgU1QVS3u5UHNswVdmfV+\n2jG4fn4eG3Mxi+OXx8KIw8Eu/0FXdkQNjTxaCX1NnXeXJBRO/6IER6QNPR/n\nqLXvi1pGKYikCy9/82qpHSZF6zHW0xPZiR5PNQU5B7VOs9PG3lEEqNVAw27r\nuqLX\r\n=z0Jw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.0_1559681168104_0.8583758900213656", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "39d6f8d89e5d9f653b4e7b4d19c2042beb580d2c3e4908f242c4b9f48257ad7b"}, "4.17.1": {"name": "@types/express", "version": "4.17.1", "license": "MIT", "_id": "@types/express@4.17.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "4cf7849ae3b47125a567dfee18bfca4254b88c5c", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.1.tgz", "fileCount": 4, "integrity": "sha512-VfH/XCP0QbQk5B5puLqTLEeFgR8lfCJHZJKkInZ9mkYd+u8byX0kztXEQxEk4wZXJs8HI+7km2ALXjn4YKcX9w==", "signatures": [{"sig": "MEYCIQDFT+oFLjhA3Q4q4gxXK3mlDUqWtfpqphgiKoMY9mO76QIhAI4WzHmA/kGMUQGKpwijBDLYReOZ0p3YmP+xOO+zOFFj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6091, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdWfPfCRA9TVsSAnZWagAAuoYP/RcuNQLRmq5N9x+TnU88\nLh+cxnAB/jg+wYo5z/5gtWVi49iu+Fbp+WLlp0gZ2omjnXEImY0hpLdAgHGV\nT8+ECLNXPVB41eLwv6vG5fRE8Qm72Pi7lseXvtEWiYvLv/+Lk52CqLjNLtPZ\ntyryY3kMH12RZ/Di/xk3XYii3u3F8iM4O1+DG2zBDO+fcM0MVRGlDz6mcKN+\nNpeUletPvHe3YBxwVJk9rgdojGWK1QvmalCaUV01rhd2FI1xO1MXmqZGQXRK\nx2jiiBX6yq3AjEViitb8v/1I7UpRxgYBbDT5pfFShiGw8qyatBCdGg8IK+Yc\nWR3RLKfSWXVETanPAHg6fI2LLkFHxaOz2xD2z0pa8FXIBY5u317C46hUGFkG\na/dWA8SxxBc0eGd42WSk/RgB2qf4Ohx1FXS8o8x0Dfxc61Hy5sKQrWx8s/by\nJu+6o1Is1bZQafPdF0RpRefxZJe4bHBrTXPExi7bOj4+OPhEbklmpHd9ZkN6\ncXDqb4bAKSt74ar4R/e9AfuYPeNYIQfO8OWCcPcWkOdbntbbhLRyTuLefl8L\nyZ/RMdk0B8xc8Pxmb00dQFOseuQ/sXjMAD6gB67OHrX8oKkjkRByizjvVkrH\n8cq29OBxWiGoIj+iHiInRc5YJfbsvBgr16M4340eZPvrlKxL8DC4wRkoREKK\n6lMo\r\n=3dj0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.1_1566176223185_0.21494387851900054", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3326fa16931c66beb8881132d339f23366535f6eeafe3db1f4351d12d84cc64c"}, "4.17.2": {"name": "@types/express", "version": "4.17.2", "license": "MIT", "_id": "@types/express@4.17.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}], "dist": {"shasum": "a0fb7a23d8855bac31bc01d5a58cadd9b2173e6c", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.2.tgz", "fileCount": 4, "integrity": "sha512-5mHFNyavtLoJmnusB8OKJ5bshSzw+qkMIBAobLrIM48HJvunFva9mOa6aBwh64lBFyNwBbs0xiEFuj4eU/NjCA==", "signatures": [{"sig": "MEYCIQCw+6txI6MNe3n43zwR8pr+bCMahm7luq7Q0FLzkO+nvgIhANgVamR1whrUjI0CAtPIEKYvsR5dw6gnFsKwK3juPEHT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6543, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdvGpoCRA9TVsSAnZWagAACegP/iDqvNNfOBdxUGtkDQTa\n4vDOlp6xxxIPUvDYKYT8FY4gXWWV39YmSHKGYLUN5wgW1w44bU6UeQQob1Gu\nVaNXnFQT+uD0YrvIPTgYqGjfZiAFYsYpN+1ZIOcpGShn13C2MLrAzhly5Vv+\n+FoRyIUWq3OJq8BQDC3Llbcd1H4OyLymO3DY9aTpXknwtZnVgdNO5TIg3rXr\nbYYkm5f8TXG8WqHBQjQ5CnNt84IB6uwmFjUPm/DNZnOfGCGNBAd5Tx7/LoCy\nEpzjKWyKUN9FVkLHm+VZAtxntb0Lpe7YpXNjeKlk9dBdwtJ+mAdZtDyuyVFy\naCKZnZRgLlVZFTX0REpVSlbRQQSMmjq+dH2CITFKdOF6wwYHVJpcRSFEI9gW\nav6j6x84ZzUqX6YdYMZeGsHAeVA2unUcwcs5DinlO58gnSYJwXvM4Ti91iOu\n05l6Xncmy/iTPoZq/izdnQ7ADKhFjrcZlp36yTUYSeQvU5BIr81I5sGvEvYj\nlipVlzJWIPRXmW5BunMZlJ8Yv+crGbg3QWGK4ugq4UMXdEZOlxkui4rRESJU\nhxwHhH6iiFwrAVOaw8Duw9Br2sitVh8UXkjzb+Mx9OPs3AamVHybfu6wuHwz\nXWb+y0E+nG1Scr6dUv98KoZMQss6RjldhRxgfsHXbLw4kGEiqk4nkwCfYU1A\n4PiZ\r\n=oEWA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.2_1572629095855_0.47162890299013727", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8b71f3f86019a2933648e51284d5f964f1c64717b31814b1311316de4e68790e"}, "4.17.3": {"name": "@types/express", "version": "4.17.3", "license": "MIT", "_id": "@types/express@4.17.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "38e4458ce2067873b09a73908df488870c303bd9", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.3.tgz", "fileCount": 4, "integrity": "sha512-I8cGRJj3pyOLs/HndoP+25vOqhqWkAZsWMEmq1qXy/b/M3ppufecUwaK2/TVDVxcV61/iSdhykUjQQ2DLSrTdg==", "signatures": [{"sig": "MEUCIA7TyqJ+nw5MzN4TaM5pBfSktYTD/K+XjbHll47oASFYAiEA2GDKXoBK88YLiOg7Rg3fryK+D47TuZC8Lh1J44sn8UY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6993, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeXqgXCRA9TVsSAnZWagAAumkP/ifSQeLQcwiHxSHWdf9v\nwUELsPco/GuFkYE0kbR697mcKq4EKYmYgPTPLhyAmDxZuW7viIbXVolB9ypR\nB1sdtJv4QeSRubiz+cLKjqR27ZvBMZiNIzgGCDaFuG0mxWHYSE6yMLN/Y8VY\nNxSG1GzlTr22znt6IwsJZnFJaOnqxjrj00/gRepgV43BPbbw7Uf8PUiD4/gh\nwuXg/6rCDug6Tc28kVMXVl2aqvOlgSCziCwtmO46sxgseogm4Rl8dxdYquSM\nkM4q1Uc/4EGYkxKtnu18vZi+1NCttPN9yqfpbbwOsJAH34c6f9Y1gJLy4P30\nZBvdSeTBmQDkla/vH0HVylK7j/W1bUIBhGALfjZJJPdNsnFJCsc2iUBrH5bD\ne4moyvY1Imk1Cmi99V01xLY/JD+F5iTcRu+HIOqeAJKuyoAeX+apvUNsdMox\n/g6dDitJCuOHqsb4rBMtZh+y27XJmY40O8ICi5yw09Ww9jDU9HsnSAzA6zC7\nCxrGiwcYlPEA0ZN/tfV9sz64GifdyJZ8G51KZ0blh1C6kzN8YQ9r8Up9mFLC\n+16cyVJrzKXogEvaUNDMWmF6ZAetNoH9RZeb+HmUx4sYxnUcJnZxb+Bha766\nzsnyyTQsjXx1+D5oed1ZVuP8a2Ae4pWo7bq4BXudXBZ6lS2InNGNzzOoK1sI\n9zSJ\r\n=gK/N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.3_1583261718921_0.7880166132175628", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "3c5eb756cb320608cab3b3623c0be6270e9d470f33151e8a17ae593e98b2e9fc"}, "4.17.4": {"name": "@types/express", "version": "4.17.4", "license": "MIT", "_id": "@types/express@4.17.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "dist": {"shasum": "e78bf09f3f530889575f4da8a94cd45384520aac", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.4.tgz", "fileCount": 4, "integrity": "sha512-DO1L53rGqIDUEvOjJKmbMEQ5Z+BM2cIEPy/eV3En+s166Gz+FeuzRerxcab757u/U4v4XF4RYrZPmqKa+aY/2w==", "signatures": [{"sig": "MEUCIDmsUmiEqvLvFmtVqzWw+zbQlcXxyTETG40lrrO75BiLAiEA13Bbfccbzw5xNwkDrPNwhLLsJTowbTEFd916lYQXY7E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7568, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeg88nCRA9TVsSAnZWagAAdmUP/ArEyEgkfa3b+M6zs/4X\nI6hJJr2qqqhXUPzrNRao+ziQliZm4kQck9rPWNDYYoBsaZaQzcp8cxrVWpjb\nshIkkVub6ccmOnM51QfEcks3uOkzwWb4bn3+Ps9KVq8/qI+0V6hKH0/yAdhM\nPoEkKISbDrosZE7EmI4rPdPhFHDX986TLxT/oYDsgrKjzDhE9JhBh1+1loYK\ncxl50jXHBnKO8wvhiFvCpTqc9hJ7FeehfBsw21i7Ox3jz8KQVAyudppnaVuS\nEePz/Y0qfcBTlxJPS7uQ4KcOWoCsrFGRML9f/tjicyNEpsV9JoOhQXcJKJ7m\ndAPuBPHEev67yZjeqzOC6gJrRkCRLSHsfAVElS5tG+TKM+Pjhm0wby3d1ixo\n6nwICdp+54iD8CDQUgisNbB4tRWoF/VRP5SPQ9yaT62sp0tXqWnBimSXNazD\nBLwmpQN36xTjz1rg9nsIF70FmE6HKbr97y+/+0Z3cB07YK3BRywoMs/QuJ83\ngdkkNfxQ+AFP0XXQkL10PCXC6uctzprtjuJBcxY18q4meT0FgdAPSsPgh1nz\nDjFsET9MDY5gOWy4E0FZ0AdhQ586yDO/Ief/J5iWHwBq0CV7Bkr48UK9e6Df\nzMgH5G6XAhYSx0LrM/7PD1f7bofdcHYrhs71bNRtnjPmUz8JiKHYMBInwwou\nx8gM\r\n=8lg9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.4_1585696551545_0.6791876980816665", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ebe0948d1568a4e4a90eacb8cc722f4a7d90cf05bc984c01d9a08e414dc852cd"}, "4.17.5": {"name": "@types/express", "version": "4.17.5", "license": "MIT", "_id": "@types/express@4.17.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "dist": {"shasum": "f7457497c72ca7bf98b13bb6f1ef2898ed550bd9", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.5.tgz", "fileCount": 4, "integrity": "sha512-u4Si7vYAjy5/UyRFa8EoqLHh6r82xOZPbWRQHlSf6alob0rlyza7EkU0RbR8kOZqgWp6R5+aRcHMYYby7w12Bg==", "signatures": [{"sig": "MEUCIHGHHhH4aXhbLasRLyyM0jWySltYWGShKPR7+mY6NTUzAiEApPZRTyiNyd4VPLvOfw+/2FM5BH0vshBtzMjCVZOLh5o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejf5LCRA9TVsSAnZWagAAFqkP/1bI7NlXIC33DmTEc+cc\nde0WBMuEMisyQCnr/58762my3WAZ4a1H6QA5WBuRxFvt0f0ra+S8s0Z1TWyv\nb86cWhRv6z4lfNk5TovxKVVoMyRYiZ/MRVNC5aWQIGf9XYgLFOecX5dsIdtI\nhTbN7f12qRgP6P1NUTXtdn1fIdcdC6ai9U/rCBqZoKbW8UQMLkEQJEfpTZXe\nwUmp+BlRvxLrveYMF1nIoh9eL7wbVt7ZWbCZeaACvZTBBhwNTHp/IePepAI9\n6ltgq0zbNOZe4K6rkFxh7nTEGyMg9IxiKft8VdwZhNM5Er0HvVyv6IrPU6Cs\nbE2gEMig4tQ9Zi8AWuW6oz3oY5t36WgbwwknNbaoKXEG1XEIgZ1YA5yDRHjy\ngMdl7saZFLwOy5DgSRKuVdHubMrJHoFMt6NerP3wDsDtnbUHwtVhPzhWNIfs\nZK+uabx3K3rhSR5eGanGD5GsxHyUfPqCqPrTFTuQPPsN1h1lD3PqSe6ZLUaO\nDB/LKAAq3vcjOEAzGDcKRf7xgRvdspkr2CSbnb3FjPD4o26X5gFan6cjLERf\nXBH7tljQnIqy3hIUCcB64gd8lfXQw7dm3FIZDiLUkzb/w40kDVR58a33MVPC\nMQjPc8LGLPnT8884dztm0SIsZApJI3Iy5TxHGqnY+HV6caBU9jK1FyRu9OAo\nJYWT\r\n=ANFl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.5_1586363978585_0.6803676053246901", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "cddb7e0f74b1c428978e5c11a6f9cb553c274aaecc94e64fd0c791a920c0f4b2"}, "4.17.6": {"name": "@types/express", "version": "4.17.6", "license": "MIT", "_id": "@types/express@4.17.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "dist": {"shasum": "6bce49e49570507b86ea1b07b806f04697fac45e", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.6.tgz", "fileCount": 4, "integrity": "sha512-n/mr9tZI83kd4azlPG5y997C/M4DNABK9yErhFM6hKdym4kkmd9j0vtsJyjFIwfRBxtrxZtAfGZCNRIBMFLK5w==", "signatures": [{"sig": "MEYCIQCfyC78Pv9JjGsHGFcd/7Kxu/sWjGZa21z1u4joi1xEXAIhANvNnKjJsdjo+0ZcCKQi910WrDT9KAHH6rqZf0uersVM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7819, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJej4vyCRA9TVsSAnZWagAAItAP/inTv3TW1TD604keLxq1\nM/6wRu4WHh1WSRiAmbokreRt+Bv4C8mC/ivyWYuIAbCXCsqcfcqla7vEzi2R\nnCa+6vAEHfSeyqv25dzIdl95rzmS+JzX4l8HNpBtf5uaSEFc1ga3C5bLZ2W6\nQxQXo1MISg80Dy9h/XAdvKXDUPf0wOQhNmHJvWQWYmq//K+K+fh6yLJO8eRm\nGC9Su8B4t1DMTCP6/N5zcSqou1Ib5gJWgfVXzQynuBwhzyLYGJ43sEA+umpG\ncZ2lhTH9u9bitewui6o5n4ZtkpdFXxnONlacXRl6dYWWHj5rq3wOWKATqlr8\nxRsEp0OMsbRsl7fDiZ/Clays4Cs2gC1YZLCNjwAYzfBjDG/gQn9vP24pR9Gr\nMGkrygz4bgIE2aQp6K/R/XQQLZr/A7Fr8P0Ih/0lzUTLCLZ+NTNi8sUDjqnz\n8sdRsdn75gzA5PpUnOKlyvvGzzzz7ogKnaLqn36qKeigL+u7RWk3NYPEuc+y\nFafN452HKq3nIkCEo6DqjNy7b3W65ShIJ0nXKaJ9OGpk/6LIwtEDPWKmO15N\nzj4nZFBUB0vxRILQ7JG0cknVRIiVr9VOzvGI872U8X6l0ywKvEaUL786GTvZ\nHLP6YWuS9I8IrU9t9sx22t5oa4/xYvN8C1aZ+VbVUW9GXqhDNgazu6PqZTCg\nXgkS\r\n=gr4T\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.6_1586465778267_0.8777667869692583", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f81de6cba6637f88e36510f46978b3f1e8429a4d83afb0e1b090385e39bbad2e"}, "4.17.7": {"name": "@types/express", "version": "4.17.7", "license": "MIT", "_id": "@types/express@4.17.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "dist": {"shasum": "42045be6475636d9801369cd4418ef65cdb0dd59", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.7.tgz", "fileCount": 4, "integrity": "sha512-dCOT5lcmV/uC2J9k0rPafATeeyz+99xTt54ReX11/LObZgfzJqZNcW27zGhYyX+9iSEGXGt5qLPwRSvBZcLvtQ==", "signatures": [{"sig": "MEUCIBstJQi7Ei4gxwq4MErAz+lzGaTwVYFfNolqitb9xgh4AiEAxhU83OXtmU84+NT+ItMU3PLi6m2w0VgqJpe894Jc0Rg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfA6ioCRA9TVsSAnZWagAAQ70P/2bH2umE93XnJOTqavCX\nHrPnvCljFywJeRd8Fb064JgthUOjqbFq7hiz/jviUhvnoAqwzypNk5wYVccV\nZ48LAazRzv3Amy6r5oTQ9ezn5+CBOmBANUI1jm1k4f043wP94u8mUO2gUD5Y\nk6bM0X/c+jcQMc3nzIhpVLuEvwM8KcxtbWPyT6mDmUCUN5vnu5lLIY07vpVE\n+joQyVGvLRUrPwPMW5m8X720Avr+1qHi2plbSMtsKpu0EzGZdsY90agnXHXw\nMshz/cfamdVIpUeVJdW2foDf9tZkG/zzX0cmYxaPgLF/viqdeiLRb9V4tK4d\nEBF+Lo+6cEJynucsdgebyt65/2p/L5jFCIhrSZbDaYAEa/UeUc3UlNID7/qe\nxDwfQWnLxnSeIIhZIwELodJs31tN//J/0MQadHklCc4A28NqHtTxvoU3rlBE\nr0r7CtnIjekvCEMivnOVdTL6tjAVufFOJikU+CP5rkAGLaG5c2P1x2VXwDG7\nmfjRVYGp7BC8L+/5drt6+Fg/kV6ylxkRDsFVxDsdk5TjWFI8JJFtkh6rWJHE\n1xuPoBGpoL0iDcs2S6aEY2MNogMMQKQkG7gBcthE92g8s6khocUfXjgEAanR\ngtwhAGvE2U+VPCfKcE9dub6UIP9Y6w4EgtWptDmnmcerge78dTo2btg3Y6xx\njR+n\r\n=7/Ta\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.0", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.7_1594075303452_0.3261856573044659", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "2d5d7b5ea5d674c61265136248d6b905af2b400bbe3d1bde75eecaeda711c681"}, "4.17.8": {"name": "@types/express", "version": "4.17.8", "license": "MIT", "_id": "@types/express@4.17.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "dist": {"shasum": "3df4293293317e61c60137d273a2e96cd8d5f27a", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.8.tgz", "fileCount": 4, "integrity": "sha512-wLhcKh3PMlyA2cNAB9sjM1BntnhPMiM0JOBwPBqttjHev2428MLEB4AYVN+d8s2iyCVZac+o41Pflm/ZH5vLXQ==", "signatures": [{"sig": "MEYCIQCILuO4Jbn4y7HSoOOjPXynniz/ry1DyzntqFHZmpY/cwIhAPFYL6WxO8gIdNb4QoBkcfWG2u2s5wevQHCAEMHJQiMq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTlCxCRA9TVsSAnZWagAAebUP/RnYLsUIuJKp7nQLzGGg\nF2LwQ41o6azUYbTvYtbR/Jb4dqjw9GBg1BGhtIGPGpOhVkLmxEI7mlhvztOc\n5wKZ7vtgrtcwV1+P6tG3Uf5fcQu8LYoqhrhRvkcrhQ+RaobTeE9/aDfOhZ0v\nyiqnLbGAQiqs/inVbtBUWHJNxx1uZUT2Xv/J65MZniCZ/b05vvRboE3uUfFR\nFcRRiiAqF20kYHlKVPrdtcp1nN2oWgC6jUOSazoqAac3MY+xB3P4VI3nZZXU\nFQFWeCaxbm/4MUlNz+7j3YuhFOXOWtccQ+DIWIjeO4BrkqvWsSk8H1y/H2QU\nAeLL2EJ+TXDtxzlcuLatOkVD6q+gUZRpi6u3B751v7wGu6/3u5vZA4NdSF3w\n5m9pbJpEQbcrxaYFOLm0lR3b92/GfjjiRYKWSy6zACJLegm7ku/OemJ5jsi8\nYS+g1iU4m/xeTH91/TM5z8WfLvYMQejMt/QSz2RMwjI0pybk1FX1caZ5fBtp\nEMoZc2n4gkvzRGQkGM0ank6qSJu6f0rui8JJoxGfjeUvHhBwoEzFCIF/YwNq\n8LnUmf/UBgMltC6vF3O7zXFWR8zARYo7brfqasDkDtm28Zvfn4EGZjqYgioL\nN5sfP5pT+DDiNSF2st8gYGeQD1U0TwrQWXVkr3j9gGbOXjCMFs0+67pLg5bO\nqWQ/\r\n=qAlQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.1", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.8_1598967984701_0.7384959242760181", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "96005d6f6692d0f0137502480774a884f89e10f2b3d6fc74e5f9e69501336a5f"}, "4.17.9": {"name": "@types/express", "version": "4.17.9", "license": "MIT", "_id": "@types/express@4.17.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "dist": {"shasum": "f5f2df6add703ff28428add52bdec8a1091b0a78", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.9.tgz", "fileCount": 4, "integrity": "sha512-SDzEIZInC4sivGIFY4Sz1GG6J9UObPwCInYJjko2jzOf/Imx/dlpume6Xxwj1ORL82tBbmN4cPDIDkLbWHk9hw==", "signatures": [{"sig": "MEYCIQCgUU/R4kmYGEQH3ukHLgbpW0+yQ8ISa/ceK3ZeTguzugIhAMlhRL+9Ze9TVmdVfl+TjhOmbzaNVjWrqWItMhzmiybG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfqzgZCRA9TVsSAnZWagAACO0P+wZkFv2YJ6y546Uz005H\n8gA0MNd17xGcgS4zB6vsWKXe6NM83EyU9jLCOZkW38aUP7CTwVVBm83AuM0j\nz5WoV+rccjI6bsZHbWW6Rva7C1CWB1P4v2Re3m7kmwBTMVYXOukEXCJmhqDO\n8DCyBMUr46KZF1MabkwIb7ooXmqcQ5SgQ6AhtoVPPnyWMV7X/Ny4II1+vaR7\nrDfwtZbBu1b6Ai+0oUeZ22oi2MfSO7Rn6hRpEN1yHVJbQe6RvivX89dWAt/3\nX30OwQkgXFhJ8sRJ7Uu8AAHTNAGOYEIxp7U5tIUHD4InoaH0Mq27UOH3+FGa\n75hgVYPS19qRG79SeCPAgWR9UrHdPGEbNlPU/KgQ58cRvA5alT7gxtwX81lh\np2Qj0KdzMya1VCgsbMYFwj1CqdxHZA1NJcQrGMdIp4gQMZPTBZYgdsYqU7cu\nDH0A3qZ+8kzAVO/7xmE2wToLj3l88abJJ0IyPvGmD7W0NSAP8/niHzuLQJac\nb4AgCenABQXmToh0yTdv7creOpMfhadEasiZ1gdb3b1xj2oCRnadETjEw/67\neQ0Q/5XrchlMeEh/rRSD+2TT+xEfdGJsWAmQkG/xysoHKwDfuPjrCtZqpPOk\nWwdKUwu/ztgFJWGysOb9eHw0nV3d5z4dUgP+u04VG8GaF4EtDMM8ZCcdIn5t\nXdR3\r\n=HCzW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.9_1605056536811_0.9632944274222708", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7ab86361b26b09c4776f6242516a0597c3d2fe95d50942d8b6934633c75e5ac0"}, "4.17.10": {"name": "@types/express", "version": "4.17.10", "license": "MIT", "_id": "@types/express@4.17.10", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "dist": {"shasum": "850fe07d6d887412d522a4295fb84a685e69f683", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.10.tgz", "fileCount": 4, "integrity": "sha512-GRwKdE+iV6mA8glCvQ7W5iaoIhd6u1HDsNTF76UPRi7T89SLjOfeCLShVmQSgpXzcpf3zgcz2SbMiCcjnYRRxQ==", "signatures": [{"sig": "MEQCIGBFutmthQzpxfIgKlo14FTIGglBNb5+jW3izE6yS8HWAiBZX4/CnCxhtuu9t//Uzn2XZvy7AceDxBFBoTDy2wKjSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/M34CRA9TVsSAnZWagAA5E4P/Ag9lRn49mj+77+1b6q/\nEBZnPebyx7LkCAiyGzeRB8i2fBOvEYDfHOlBmhxvaTnCmqQD6xzRE86CDh2/\nhLmPQ3Lq+QDAwzW86fmbfqXncA0RtlMUcRO65YCoo9kd3WZY5MylWli/hxxp\nda4X+PcHP81hafVysU1JhNkWJEiIPWwrX/ABYxDjzCgF+jtodhyYWVAVslqa\nY0w6qknlmXuuyUapnNHJwygXEFuKEB2FqpyLnm5hkuSgE400hambydr561Ly\nl9oT0SDhdwi+YJhldi4dD2IykeEeQRmxdV3Twnj+J55akxSTgZNQaBmEXsaS\nTpaHA46I5STwa9pEtY41zbXh/S5kJZI56gDq3v3qQtmEvmJyeesL2L4lGxK9\nn6DOK9orN6QMbC1SQugb9kLxfozwm8vIMMP7uSI5pd82PzslXFdomwSg2bBN\nIElxmQR6Q/c45S/RgO+eFLafA+qhjWWD8Hq16uTaTEyzTXP6eYbFgriRD0gr\nM6CFQlJ6QjbQ3pfD0TEmizsoXe/9UZHv9Jfc6xKDZHFax2nOVAPo2sPdacf1\nAc/gEp/f5jfeEWWRKG202rGtJzLRQjpN7GmHSwwP/mFrMErqL09pYafl3cel\nszXS6BDtXXO+lHQtF7DR74tHSuoq+fPJ4nYFkjPc3WgSLPthwswtwOhWeQd5\nE5KY\r\n=8VH8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.3", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.10_1610403320292_0.7378392088201524", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "cab83c5ce0e118f64236605dc6370cb115347f6e0cea8c47dffc89d0bd5526f1"}, "4.17.11": {"name": "@types/express", "version": "4.17.11", "license": "MIT", "_id": "@types/express@4.17.11", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "dist": {"shasum": "debe3caa6f8e5fcda96b47bd54e2f40c4ee59545", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.11.tgz", "fileCount": 4, "integrity": "sha512-no+R6rW60JEc59977wIxreQVsIEOAYwgCqldrA/vkpCnbD7MqTefO97lmoBe4WE0F156bC4uLSP1XHDOySnChg==", "signatures": [{"sig": "MEUCIQDTKQ6c8YfrAk6qF3ie18+5TLhkN1AYujLXKjEwgLo8kgIgLmh6Ve2W3CAf9LqVdh6acd53bVJGGL00qLF4ReZO5D0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/hf5CRA9TVsSAnZWagAAL5cP/iWUfioDRViNQFxtK6ri\nTGJQCAGFv1KzqZ/tM/RKMKv52nRmhG1fwjVSZEr3ToqgOKDJOSxlGaqwsrao\nQ+SR0GrebugUdzNk0kYmecLoCdLhI87kIsdw/yt29FbCjCxiFjPcjoo0kud/\nw6wqSf8uQCkPtFEsj8/xKwjPTWi8hDMLUS4BgFvXMcHm+bQ5Lb/4V1wFAc0f\nxxER+wfnqzY7b6jdJOlc3jsWBo4dy0B6nw46ntCVgkkCyDbNF4xlsK5wW/hk\nuq5d+FK36BNKvv44mw3d6vLQGJvsnqbruJYWVV1RAU2tbcoaiD2258u7/RkF\nmhmopfAV1fsrnE7Q1GX9rWq+NA9ed4u4CxsmGid2vcHT13es/4EjMXEhNl6S\nMnr6qs5yXVw1xJyKFZ/ex0a3SdnknZufok+4hnF/2BORlJzdSCF725Vn1M3G\nyL/K1PsP0DTg7h/NHkL3zxVi6mqp0CABz3sAyMYeAuXCjW7W4qSs8mm3Ki1v\nlazQ9Zz2lKm/SkQIJ+8bGF+6nTka+JeonpbdtVzgCCFgB5XPQ9BhKMHPbf/7\nYZCPlmS5iC2ig6q6iYnHZZ8lxoz1jq1LZCp0BhZcAg8759yclMMziN+tN1PN\nEmHtIlWOkQgXVWXbjjjsION65oBtFrJyLaqxEQT2COo/G/VHfcOVj9I6zgGj\n3Jk/\r\n=Lb23\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^4.17.18"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.4", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.11_1610487801527_0.8561602524891956", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "51b7ca65fbad2f43fbcff8d74c47db7b8f36b31f71458c1fb328511d0075ac5a"}, "4.17.12": {"name": "@types/express", "version": "4.17.12", "license": "MIT", "_id": "@types/express@4.17.12", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "dist": {"shasum": "4bc1bf3cd0cfe6d3f6f2853648b40db7d54de350", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.12.tgz", "fileCount": 6, "integrity": "sha512-pTYas6FrP15B1Oa0bkN5tQMNqOcVXa9j4FTFtO8DWI9kppKib+6NJtfTOOLcwxuuYvcX2+dVG6et1SxW/Kc17Q==", "signatures": [{"sig": "MEYCIQCwcnOPtdYGgqHASc3UVS5GO44F1s644iNMiDLxTG7gxAIhAIAQXLqC9P/jIqvFYEVZrAZoUUO3f7uxQBBJ/fXon4gr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12382, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrS1yCRA9TVsSAnZWagAA84QQAJmqr5KdzYZiq7t48MIT\nO4inbJTnZE7cY/0lH72l4075WyDqrKFyRjIY+biK0ee5zdFtqTeJc3/3kEFh\n3MDUiaknuhIPzU07jekxACT+an/UliATeg+G6+++V2gzliDA2h+lIyZd0JJ2\nUrW0FkMwUgHlw9Zzkgl95Pn+1burTYCiY8vuX9PnDFowXMFoZ7uhJwesw339\nKOv0YPAEAxNKkQ3RTmuysHyg+7nxCpNtAMoQjb1GO4OEAXfDXabtpSTPivOl\nBlsFlNGAORhStlNyH6AtVz4nruIxvTj8IecJ9oxejIHsztUHbQ4tY77UGDa1\nqZuhxO015Jfr0ckam/o2aHLC8Vebj8IsrezFogAT2fEoNr5OxvSytZEsx0qB\n2PfW/G3rvRvlu6AAIDQKX12zTlKaIyKiSc6yX9McSguaNrld+IRI4YSpYZUM\nzxCJJkF0frlFzJcAlW5B6ysaIRCWJVi2nHxv7mqrVmwSRONfeLLH4v27q84N\nPi8vyocAaKNK8nYX9efrRi9GiplSQyyh69A7PFEPeBiFuGWEHyUnKv55MliN\nfCRhfkzZ1M31OiozrIEA9gX4JnFX24z42LCgXaIcmOnrJgfZWT9T4YNRhgO1\n7znwNUhBpf41SyJuQIgD7sVjJnQ3k3+K1UXiJf2aVSNGykOJBrYL6s/9pkqS\nDSbb\r\n=AQDy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^4.17.18"}, "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.5", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.12_1621962097586_0.6830464842194817", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d049caa40a56607a828a6217665f711f33b7a3a9055f4722521e06beb76b0c20"}, "4.17.13": {"name": "@types/express", "version": "4.17.13", "license": "MIT", "_id": "@types/express@4.17.13", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "dist": {"shasum": "a76e2995728999bab51a33fabce1d705a3709034", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.13.tgz", "fileCount": 6, "integrity": "sha512-6bSZTPaTIACxn48l50SR+axgrqm6qXFIxrdAKaG6PaJk3+zuUr35hBlgT7vOmJcum+OEaIBLtHV/qloEAFITeA==", "signatures": [{"sig": "MEUCIBN89m9R6edbHlCxKvA/DUQ8ASWSKQq5GZLc0qYaAb5pAiEAw1UVskntrur4yyTY0w+ET+wjgqhKK0xDIArOQJ5ZDSA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12514, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5MHpCRA9TVsSAnZWagAAXywP+wQOB2OG0fXCLJbSyE4T\n9UXlTvi5nC3NVqZygoOFciO6DT7Xmmi1oS4EaXPdZwzXPH5F8gvEap0rUsCx\ndDvu6npfJCexFPskxHFHOu6gP4HvRDmY9NHjmse9v+64pY4DAHmYKbjMBfJR\nqgoutvnVzNj0npdcBowwVIYuaQn8eumxwy2htHfbmOLIxAke90aa6qKcXEV/\nMMxMWffz+jHtZ0RMRps+kNvYqR1C0MiP7npRVifpXHHuHvO8QMpNT4NoPXYp\n5xqkKdCuv//tbHG9Kv5uNkRAZAfU9mb91XGQa5wjS0kj4PflupneUHVD0nbx\nURMximk8TQI09WrhezUGJzVZ5C9ls0KUbaVVj+w0pldRinTPJKolWLD1AVGx\nWL6O3/jndaMtOMtxOt497QxFFc4kOMMgZTl6TNu+CW2FjdiR8tEv2wMjn4UW\nLoM66AkUyDh8wqY7nRirGiq7yLpr674HAwkOm0K50zSFdnxqA5zs/VV5oRNy\nrDz3eK6Dp3qsZbfUie+NHJmjRi19Y0p+XS10YVlYjvAxT/osdIMBKdPbH7Yc\nDy+G2QYPWGncaos2ayvhBaYfcWzvuneHnwURWk8J26LtWrK7KhMucK+1PLop\nPwe/nZI2WlJbwAKFRGRI5m9c1HBmexuN/keERfIFenP7Rnxii5/hjvSyA5Mx\nhCbW\r\n=YtG/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^4.17.18"}, "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.13_1625604585057_0.16287282726138108", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "df0c9de39b435f4152916282f0ae9e98f0548d6b50f6bb6aedddc52e4e3f25a7"}, "4.17.14": {"name": "@types/express", "version": "4.17.14", "license": "MIT", "_id": "@types/express@4.17.14", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "dist": {"shasum": "143ea0557249bc1b3b54f15db4c81c3d4eb3569c", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.14.tgz", "fileCount": 5, "integrity": "sha512-TEbt+vaPFQ+xpxFLFssxUDXj5cWCxZJjIcB7Yg0k0GMHGtgtQgpvx/MUQUeAkNbA9AAGrwkAsoeItdTgS7FMyg==", "signatures": [{"sig": "MEUCICaFoW85pq2wmoXN/i+Kc6Q5XNxZvSf8gda6E5xIU/gpAiEAtX1VIMasS67JsujJNT1S9KxlREr4ZP7K/fKBgRsNp3o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8243, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIMtVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJ/RAApGLyPezxzMZqju+88kArMyJ8F4/t9KYfUwWwLL9eI6F89woL\r\n74C09CF/waQZztNJcUi1HUM3qWeMO5YeVgOsMWtXKYVgwPBgym6Cum88P9/W\r\nxHDsHugTSE5oOaQErgBusiqgctpRWuw6B+b8h2vPxKsm09l810Yr7UhJDy57\r\nDaKFjf8Ob4Zjqt7WbHPCbzIF/RRkCKavOM4ViRBF91q04OcjwZjUvLLa5DWj\r\nR7hh1l7is82OvgOibYpHUsuznwne3LsxfbPbAXx4PZstv+nNbWuLu5Dh2c/c\r\nz+4X5/SAKMbTWfBTWJWtECtdajqDpu3q5Ck/Pmrn6trEEiJK78iJHtUrP5dT\r\n3Iz7fMrEoXXN/x+nJOIBId2db+KWRsYG1JTzMTqppCSiJsEecR67unHBdSQy\r\nQnit5HSEceBlDGzt0VudgogLGJpafwfXo6lCzomyhgI8V29nmpAEKKfuKkHa\r\n2l7FefqIuOjfhJePyD0T/7JKRh/S/iS5oXb5d1A143Z+KYRKuRAJrg2QMFNo\r\nOlLdsa+2Zumd9jD8tKVNLTAiNnI4WeS8rh4NXdfxDVwoUinmE/yWb48ZHHZX\r\ny5V///2WEYeALXmHqoRnCX2OtuApUUBMNKSo8P10qpelD6e/VEGWIH1tlfEK\r\nNgMIPQI+fYKBLCvPvzENc0jKOPeAFLDDBVQ=\r\n=fx4c\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^4.17.18"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.1", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.14_1663093589056_0.7295734765833677", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c1bc3eb1de87678353401acb958785c75095c71fec66004c015b6aba2aeee230"}, "4.17.15": {"name": "@types/express", "version": "4.17.15", "license": "MIT", "_id": "@types/express@4.17.15", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "dist": {"shasum": "9290e983ec8b054b65a5abccb610411953d417ff", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.15.tgz", "fileCount": 5, "integrity": "sha512-Yv0k4bXGOH+8a+7bELd2PqHQsuiANB+A8a4gnQrkRWzrkKlb6KHaVvyXhqs04sVW/OWlbPyYxRgYlIXLfrufMQ==", "signatures": [{"sig": "MEUCIQC4mAJUOYilmupQedDUV60cXX+yTl9JhQlPU6TLky6ojQIgZ/vbkToYL8kCqi15o/e4X9grt8mY5u/JZGEsxP/apSE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8243, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmQvHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrCxQ//XVd9GwjblGVhUzf+Y+yornYQg3S8ImbU5PFO6pdOyYrjqOd2\r\nkspskNz9buDupJtNW7ZoQyPx8uoICy52B6qGuuxwgIzn81q7xQzYrLDZ6mnu\r\nsAU/x9qvk+lTYXNEYRGfaxdWASHEgVS5SkYgI+Idp0AntgexrcJcAs7TlJCF\r\n5ESol/9eDNDQS1yKEIh6Skf+647WVGe+IrmDZE6yZSS+rsUxtQ5LWAPj36GS\r\nHj54zTqH4/h6nE5Kjjcp2eDwYy/eTtzXOqTwYBLZU8dyb2AFYPv6cP5sKa9g\r\nzDtPpSUKwpDynrVeCfFkIp2i2UME4vPogrku9PVdTzPDv39jkRfB2iJMPkEx\r\nLB85Iiibcigy4IlTVUg7sBCXR2Fd3QEwGI9gi6lCb7ij48m86Fxg75ZxHUMp\r\nW06sb3cPKTTkF3aVH9kmjuvtriuRsdA6hNDOtWgk5dKPAQ3FbzTA1RGO7lDz\r\nrc/Y/agoFakBM81IFjnSF/lFViFRuNzwbH15EprB+FHiW/Af93hGJ7gokCNs\r\n5m/cXoYLUaZLmWoH+NJpzcc0v5KrcULi1CFoy+tsAjsJbobWGWdjk6O4ucGV\r\nlt9VKOiscAO5S0zEEIx8kqazEgDEVm8kL9VX4dw2Ny/mx9C1bUlLj2GKHYCT\r\nhZfBMNTChcFRkUriNUNND1Ly9MpxUqJxYUA=\r\n=/X4H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^4.17.31"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.15_1670974406883_0.12898919972866207", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a3b86a7c7eb6f9cd2c24e3ecba223a93a983155d3c2e3bdd1a9d006150af8455"}, "4.17.16": {"name": "@types/express", "version": "4.17.16", "license": "MIT", "_id": "@types/express@4.17.16", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "dist": {"shasum": "986caf0b4b850611254505355daa24e1b8323de8", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.16.tgz", "fileCount": 5, "integrity": "sha512-LkKpqRZ7zqXJuvoELakaFYuETHjZkSol8EV6cNnyishutDBCCdv6+dsKPbKkCcIk57qRphOLY5sEgClw1bO3gA==", "signatures": [{"sig": "MEYCIQDxobnAzj8+xgGylY3GMT2dFhvJOC+NWa58LZZTeNZ4DgIhAOleo6r08gIdoPAuz+iXnPC69g6GvA0xJC+dkhOG/DMu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8294, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzvz6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpgdBAAiS9h4BL8dHmolJwBIIrAzN//RoSPWxAS2VQpHMz2SjvMK+j3\r\nBah+T8B34DtKqvY2/hg6CKI9wJXch5erOBYfBjAP3V/zMbqMe7IHCpgA42Ec\r\nS6ufdXCcYGtULRdfwiEyVTlVW/dnJOZ5FarjxxkwfvXxwDm6x2MMFec3X9kn\r\nrFSgBIjclARg7LNchFZMPiMJIOSAwuvvsSxjunAqGC+Rf3FEN8hBeMGFdg1M\r\nypTBTyNplc5T/RCe2bRJDjQLSZfXt0LsO3ZorOsHks56y0UQDq22WUmkX/NC\r\nNMbrFsGeW5CCZxLKO4aqfOckjVUpLleD7PHxl7sDIxh4rZ+98Pl/JhjIOCg8\r\nXP8lgi68KiVul0BznHLDcjTAEpRv2e32a+nPei+VVsa1u+QxRUSHjeh7nym9\r\nRcxwnisHtLHHTbGZd4KP3dZeoH5WuAOLavJ7PuGIBO2yFNTEV4CQOlxglpAh\r\nyhjWLdkmKEAlGpej79s8qQmooA6y3ulflu9JVRo9zdHwn5FVsddxpN9Cy1lX\r\nu/ZvYjIUuvJpchNztoGVTWUPSQ0QkG169rdKEzoF9yj0zV5oTKz4gH85ao9D\r\nNMJw16zioZRU/92dqxHW6yQxVNaV/XluuUl9u6+qaW2aZkKcLVu2fOSpDnpU\r\nmXh/4wVZiu1gyMM+7cMTHFMZa6EKVbYYQxQ=\r\n=ubhb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^4.17.31"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.16_1674509562772_0.23783647751108594", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d11eb6e6fa9b8300c36502ac43c9937dc56b8cb2799a3cac32548afc0e404298"}, "4.17.17": {"name": "@types/express", "version": "4.17.17", "license": "MIT", "_id": "@types/express@4.17.17", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "dist": {"shasum": "01d5437f6ef9cfa8668e616e13c2f2ac9a491ae4", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.17.tgz", "fileCount": 5, "integrity": "sha512-Q4FmmuLGBG58btUnfS1c1r/NQdlp3DMfGDGig8WhfpA2YRUtEkxAjkZb0yvplJGYdF1fsQ81iMDcH24sSCNC/Q==", "signatures": [{"sig": "MEQCIA11fbhLm1dizt7XkFQzd2SW6phjE/oxpY5U3cQ32FZGAiA+Rh6Jja6XIHGQxUOgPs+k3gHbtbQm0w9W/cL5Ybp94Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8294, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj3X2EACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXUg//Qme0xUmjLAiUB8/Xea2wdsZeoeUC382/EUOUUmIKLxIw4LRD\r\nW2C8AlDsn8lLErNpvneWDsG35KzyamrGhBu74G/TqM/JtxYB3el9vTh3uuF8\r\nW62Wegso532dP5g0dTwjNbA31KFm8FQ6CwWsbBGe5BZV0CZ6QU3ILLuVtfaG\r\nDa2nMYxCOqwLOmk3P+/LoOk4/D9Ucv4fQpapNiNb020KL3zcnj7rwQy13WjV\r\nbmK2BzltQyldqMWWTkFOuCdFJ0deT8wqL4IL8qXhESZeEBGUu6vmLwrZXpqn\r\nMdI64hjjDLxi5HSwjuhDIwTHY/0qSWe1g1xrSx3hGc4uj4Fx7qG2/TadWK9f\r\n75NjfYF85e+mWR5fKcegM3vmZkdwjbmzgpIK1Olnf0KuyJPr29YwMrLVp6g7\r\nd4H9+9BUQP0NqOLgPoQiCckXtfUOaVaJ0OsXsM7CS4IlMr6g3qZM/5hZz1Xg\r\nc5M29zChSARn7+Q/v0+OCf8LD6/mzxehT9lsq3Pzx/hqDjQUctrJWM/0aXDp\r\n+9I2jjw+voygA11+gGlAj96qRIX1SfPFOdsa1+kHqbKzI0uw3lnzYFr+q7eH\r\nWCMmRqgWbJiTfE5oUZ9fwHhortfxQ7spIfXNxiS2RE066SGTj27Vbt/Vl0zQ\r\nv8TPFMCTwRjOjArow/H6tdziNO41yAD25Q4=\r\n=3Gtj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^4.17.33"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.17_1675459971964_0.4443981834023867", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a4c3d98898199c22408b7c0662e85011cacce7899cd22a66275337ec40edac14"}, "4.17.18": {"name": "@types/express", "version": "4.17.18", "license": "MIT", "_id": "@types/express@4.17.18", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "dist": {"shasum": "efabf5c4495c1880df1bdffee604b143b29c4a95", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.18.tgz", "fileCount": 5, "integrity": "sha512-Sxv8BSLLgsBYmcnGdGjjEjqET2U+AKAdCRODmMiq02FgjwuV75Ut85DRpvFjyw/Mk0vgUOliGRU0UUmuuZHByQ==", "signatures": [{"sig": "MEUCIQDLO+Y/2iaa/Fmb3znHS98X50oHcOenpRc908UFHd7e2gIgVEBmb3QEW4v45nFj2XvBl856wNXzcHhB1yFWyeUFKH0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8298}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^4.17.33"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.18_1695489990430_0.30414175405847765", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ded87c8062ca0c1458a29d8e8d3372e0ad8b5291a4e9c71a39298c22ba4113df"}, "4.17.19": {"name": "@types/express", "version": "4.17.19", "license": "MIT", "_id": "@types/express@4.17.19", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "dist": {"shasum": "6ff9b4851fda132c5d3dcd2f89fdb6a7a0031ced", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.19.tgz", "fileCount": 5, "integrity": "sha512-UtOfBtzN9OvpZPPbnnYunfjM7XCI4jyk1NvnFhTVz5krYAnW4o5DCoIekvms+8ApqhB4+9wSge1kBijdfTSmfg==", "signatures": [{"sig": "MEYCIQDDGEGwmf31y3uuTNkN5YDgXyl3e7AoGm8f8tryX7WLrgIhAK1PNFDJXdU+Fsoh1mdr2F0v3PvmNFh7a3U7Mr5eMhWf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8298}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for Express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^4.17.33"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.19_1696961599850_0.7662507941780505", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c5300aeff579e82d7905c9a505efcfaacad1da8a23a07e89191a708cee5db18e"}, "4.17.20": {"name": "@types/express", "version": "4.17.20", "license": "MIT", "_id": "@types/express@4.17.20", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "dist": {"shasum": "e7c9b40276d29e38a4e3564d7a3d65911e2aa433", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.20.tgz", "fileCount": 5, "integrity": "sha512-rOaqlkgEvOW495xErXMsmyX3WKBInbhG5eqojXYi3cGUaLoRDlXa5d52fkfWZT963AZ3v2eZ4MbKE6WpDAGVsw==", "signatures": [{"sig": "MEQCIGQ+bVHDfmLfU9j7YtAWPb6XfM9Qah3eM9Htv5vAhtNHAiAu170XodT8ctNKpNXGiYN7vVWCTIDoAIwuz86sXGsGXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7863}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^4.17.33"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.20_1697594859582_0.9537531376905848", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "564df074e8079566345f143877a68744d44358d987c7c16e87eefda6409e5750"}, "4.17.21": {"name": "@types/express", "version": "4.17.21", "license": "MIT", "_id": "@types/express@4.17.21", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "dist": {"shasum": "c26d4a151e60efe0084b23dc3369ebc631ed192d", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.21.tgz", "fileCount": 5, "integrity": "sha512-ejlPM315qwLpaQlQDTjPdsUFSc6ZsP4AN6AlWnogPjQ7CVi7PYF3YVz+CY3jE2pwYf7E/7HlDAN0rV2GxTG0HQ==", "signatures": [{"sig": "MEUCIQD8zJWmmvEQdadTcvlxPtLQO2UjlOJen+Qke6udwwvFcgIgchEEcoRHauBppor2g91fRZ2FfIrI1cDS6XyVQdOjsdQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7863}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^4.17.33"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.21_1699326610331_0.06397851099336571", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "fa18ce9be07653182e2674f9a13cf8347ffb270031a7a8d22ba0e785bbc16ce4"}, "5.0.0": {"name": "@types/express", "version": "5.0.0", "license": "MIT", "_id": "@types/express@5.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "dist": {"shasum": "13a7d1f75295e90d19ed6e74cab3678488eaa96c", "tarball": "https://registry.npmjs.org/@types/express/-/express-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-DvZriSMehGHL1ZNLzi6MidnsDhUZM/x2pRdDIKdwbUNqqwHxMlRdkxtn6/EPKyqKpHqTl/4nRZsRNLpZxZRpPQ==", "signatures": [{"sig": "MEQCICV0QvN+HhKerOZFwlGQJh88vaNlP9gJAx7HRu8SsrAQAiBDEUORZ+zhjnd7hwwWgBuKEQN6L7b48JR9q850UwaQ2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7859}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^5.0.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.8", "_npmOperationalInternal": {"tmp": "tmp/express_5.0.0_1727292029659_0.981321677373928", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "906f793a5f72703639a36afa9b7c5b41256100f5efc93138ed2551c101aea99f"}, "5.0.1": {"name": "@types/express", "version": "5.0.1", "license": "MIT", "_id": "@types/express@5.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/CMUH", "name": "China Medical University Hospital", "githubUsername": "CMUH"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "dist": {"shasum": "138d741c6e5db8cc273bec5285cd6e9d0779fc9f", "tarball": "https://registry.npmjs.org/@types/express/-/express-5.0.1.tgz", "fileCount": 5, "integrity": "sha512-UZUw8vjpWFXuDnjFTh7/5c2TWDlQqeXHi6hcN7F2XSVT5P+WmUnnbFS3KA6Jnc6IsEqI2qCVu2bK0R0J4A8ZQQ==", "signatures": [{"sig": "MEUCIAeCZwXaM9TRnAyle59x1VS7XsmWJ6a9OrfBQvNMagWvAiEAt/H3g/j7Od6DLsmk9Lfyiw3tfflV+jqzjAIsSswEq+o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7584}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for express", "directories": {}, "dependencies": {"@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^5.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.0", "_npmOperationalInternal": {"tmp": "tmp/express_5.0.1_1742412732850_0.6368596021591244", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "fc4e2b97399a28edf0ea8af60a18a56b71622eab908009e7e3a18677f98a80d8"}, "5.0.2": {"name": "@types/express", "version": "5.0.2", "license": "MIT", "_id": "@types/express@5.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "dist": {"shasum": "7be9e337a5745d6b43ef5b0c352dad94a7f0c256", "tarball": "https://registry.npmjs.org/@types/express/-/express-5.0.2.tgz", "fileCount": 5, "integrity": "sha512-BtjL3ZwbCQriyb0DGw+Rt12qAXPiBTPs815lsUvtt1Grk0vLRMZNMUZ741d5rjk+UQOxfDiBZ3dxpX00vSkK3g==", "signatures": [{"sig": "MEUCIQD9kdTGxzFVq82Z3pmIKJ30xRu9wDS7D6sfSqzgIlXVdwIgNPYrR8zy7oQPIvaf2gcZNDhZcaONp/TeHAsMzQlNWFo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7361}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for express", "directories": {}, "dependencies": {"@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^5.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/express_5.0.2_1747438455675_0.47231925586696577", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "008dc0ec8e811b6dd78e7cf776bcc8ae50f3d97097463bc36e3b416fd22cc7e9"}, "4.17.22": {"name": "@types/express", "version": "4.17.22", "license": "MIT", "_id": "@types/express@4.17.22", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "dist": {"shasum": "14cfcf120f7eb56ebb8ca77b7fa9a14d21de7c96", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.22.tgz", "fileCount": 5, "integrity": "sha512-eZUmSnhRX9YRSkplpz0N+k6NljUUn5l3EWZIKZvYzhvMphEuNiyyy1viH/ejgt66JWgALwC/gtSUAeQKtSwW/w==", "signatures": [{"sig": "MEQCIERgs6b9CLCqTp0Z/VZXulPBsMiAsW7v62qISYesVQqOAiA4rOY/tO79g+0Gz8fjtFoUaqd3+tF2BwK2q6BslEsnkA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7671}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^4.17.33"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.22_1747438463860_0.4383349896613731", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "0419587ffe8374d5cf260318c152eae67d48eb163a0fe4677e05b55886ea22d6"}, "5.0.3": {"name": "@types/express", "version": "5.0.3", "license": "MIT", "_id": "@types/express@5.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "dist": {"shasum": "6c4bc6acddc2e2a587142e1d8be0bce20757e956", "tarball": "https://registry.npmjs.org/@types/express/-/express-5.0.3.tgz", "fileCount": 5, "integrity": "sha512-wGA0NX93b19/dZC1J18tKWVIYWyyF2ZjT9vin/NRu0qzzvfVzWjs04iq2rQ3H65vCTQYlRqs3YHfY7zjdV+9Kw==", "signatures": [{"sig": "MEYCIQDhFIe3x4G+hr+QCBteVScHgtiMZKModd79Ewam4N1JFAIhAPbK6SlR9eMg3bcluUiEmdrx48FJOB3BrcYpfeiHqkng", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7573}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for express", "directories": {}, "dependencies": {"@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^5.0.0"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/express_5.0.3_1749262754995_0.982238418877152", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "50a15d13b8596ea6054e9e404451e427c71c41ab505b7dc01a72b122e2ee16f6"}, "4.17.23": {"name": "@types/express", "version": "4.17.23", "license": "MIT", "_id": "@types/express@4.17.23", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "dist": {"shasum": "35af3193c640bfd4d7fe77191cd0ed411a433bef", "tarball": "https://registry.npmjs.org/@types/express/-/express-4.17.23.tgz", "fileCount": 5, "integrity": "sha512-Crp6WY9aTYP3qPi2wGDo9iUe/rceX01UMhnF1jmwDcKCFM6cx7YhGP/Mpr3y9AASpfHixIG0E6azCcL5OcDHsQ==", "signatures": [{"sig": "MEUCIETg5jztb7RY4LcxJpCT+GOzE9mqLlp54ykYIYXsdezZAiEAxmFf5TFMlio00xjHRvkiSB5l0kvk+hhtrKIWA/dFoX8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7883}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for express", "directories": {}, "dependencies": {"@types/qs": "*", "@types/body-parser": "*", "@types/serve-static": "*", "@types/express-serve-static-core": "^4.17.33"}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/express_4.17.23_1749262773550_0.6722893092872222", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "c7c276342e7f5808be5916c5dec3e614fa8b6e60b3323e369f793c389ee6f860"}}, "time": {"created": "2016-05-17T04:52:53.278Z", "modified": "2025-06-07T02:19:34.917Z", "4.0.16-alpha": "2016-05-17T04:52:53.278Z", "4.0.17-alpha": "2016-05-19T20:48:45.330Z", "4.0.22-alpha": "2016-05-20T19:33:03.103Z", "4.0.23-alpha": "2016-05-25T04:51:16.283Z", "4.0.24-alpha": "2016-07-01T19:22:05.018Z", "4.0.25-alpha": "2016-07-01T22:39:25.963Z", "4.0.26-alpha": "2016-07-02T02:19:17.535Z", "4.0.27-alpha": "2016-07-03T23:21:08.123Z", "4.0.28-alpha": "2016-07-06T21:31:51.543Z", "4.0.29": "2016-07-14T14:32:38.620Z", "4.0.30": "2016-08-02T15:52:09.301Z", "4.0.31": "2016-08-19T15:24:53.704Z", "4.0.32": "2016-08-25T18:41:36.080Z", "4.0.33": "2016-09-19T17:30:22.258Z", "4.0.34": "2016-11-15T14:56:13.496Z", "4.0.35": "2017-01-18T06:06:53.985Z", "4.0.36": "2017-06-15T20:13:37.806Z", "4.0.37": "2017-08-21T21:51:40.144Z", "4.0.38": "2017-10-25T00:22:06.413Z", "4.0.39": "2017-10-26T19:32:09.597Z", "4.11.0": "2017-12-20T14:51:52.241Z", "4.11.1": "2018-02-01T22:26:36.771Z", "4.16.0": "2018-06-05T00:03:27.771Z", "4.16.1": "2019-01-22T17:51:27.213Z", "4.17.0": "2019-06-04T20:46:08.489Z", "4.17.1": "2019-08-19T00:57:03.297Z", "4.17.2": "2019-11-01T17:24:55.969Z", "4.17.3": "2020-03-03T18:55:19.030Z", "4.17.4": "2020-03-31T23:15:51.660Z", "4.17.5": "2020-04-08T16:39:38.706Z", "4.17.6": "2020-04-09T20:56:18.411Z", "4.17.7": "2020-07-06T22:41:43.587Z", "4.17.8": "2020-09-01T13:46:24.822Z", "4.17.9": "2020-11-11T01:02:17.094Z", "4.17.10": "2021-01-11T22:15:20.561Z", "4.17.11": "2021-01-12T21:43:21.668Z", "4.17.12": "2021-05-25T17:01:38.171Z", "4.17.13": "2021-07-06T20:49:45.161Z", "4.17.14": "2022-09-13T18:26:29.199Z", "4.17.15": "2022-12-13T23:33:27.097Z", "4.17.16": "2023-01-23T21:32:42.951Z", "4.17.17": "2023-02-03T21:32:52.154Z", "4.17.18": "2023-09-23T17:26:30.610Z", "4.17.19": "2023-10-10T18:13:20.034Z", "4.17.20": "2023-10-18T02:07:39.878Z", "4.17.21": "2023-11-07T03:10:10.569Z", "5.0.0": "2024-09-25T19:20:29.866Z", "5.0.1": "2025-03-19T19:32:13.062Z", "5.0.2": "2025-05-16T23:34:15.854Z", "4.17.22": "2025-05-16T23:34:24.033Z", "5.0.3": "2025-06-07T02:19:15.168Z", "4.17.23": "2025-06-07T02:19:33.740Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/express"}, "description": "TypeScript definitions for express", "contributors": [{"url": "https://github.com/b<PERSON><PERSON>kov", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/puneetar", "name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/dfrankland", "name": "<PERSON>", "githubUsername": "dfrankland"}, {"url": "https://github.com/b<PERSON><PERSON><PERSON>bas", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": "", "users": {"olonam": true, "tedyhy": true, "maxisam": true, "fleischer": true, "redstrike": true, "hongbo-miao": true, "maycon_ribeiro": true}}