{"_id": "napi-build-utils", "_rev": "5-c6190562b03a7d0fc484a91508c803f1", "name": "napi-build-utils", "dist-tags": {"latest": "2.0.0"}, "versions": {"1.0.0": {"name": "napi-build-utils", "version": "1.0.0", "keywords": ["n-api"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "napi-build-utils@1.0.0", "maintainers": [{"name": "inspiredware", "email": "<EMAIL>"}], "homepage": "https://github.com/inspiredware/napi-build-utils#readme", "bugs": {"url": "https://github.com/inspiredware/napi-build-utils/issues"}, "dist": {"shasum": "0a578fd10a75586b5ffd1d6b4a6ba81e4d27a119", "tarball": "https://registry.npmjs.org/napi-build-utils/-/napi-build-utils-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-yfPCChW8vj0jiO8FGNg2k0pe+ZptJ6ynVZvrDl/qJ85SjmxOcW04W9Xk0R2Z65gpAg6/WJW5J6pQkzaUG0yWKQ==", "signatures": [{"sig": "MEQCIGH6ajVtmUBxnOjo5mD2vOBbgRsFvbX/8Wt4pn6DVQc0AiAFe2OP5tu0iWRdcn7Y5/4YS6COHkGqcQ8JdEO1X+Exqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15996, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbmZ0KCRA9TVsSAnZWagAAY1AP/2aqfdbysmO3zAZucylb\nv/9F0U7rmRtwtYXw+JuD7IGhLk7AEFDj62KS/Prj7Z7x4uU0wg4NgKyEiaLB\nzcU/adASZzqppS2TkZYRJT+0XQoHIfJRBAjoqp8ITE7HVqnn36W6rNxVu1cZ\nX59N6IUuXt+YBfYjyNNbXdNaN/3kBQBkOzUvnPTrfTAkyHZiIQZhyZ9/LfDz\n0TyDPaBWxOn2YmjaP/bNAiAIdgSyuob+dZhm8fLQ+bueXbC5pqWucX++wyEt\ni9MRwmWRw5RQ0ceoLHBFAb7ccBPDqF0cWeaHHlT+YYcK6XeFwkYIkwOe37ED\nTng2EBbZuJfqwAvVEJgbtEYnigZZ8unkJjvK8nEfAKr8/DImfP5yIFd7XqV1\ndGraye9+McLYZzMinov/b2YS6xcZJDLOetqOIE/KtsOM/O30EoM5JepQGClz\natRVFSvKDpoJS3dR84+DeBQndoA95nnYCqyDuKPcJPKbkCLJQNQSsQPQZ8UD\nUv3xw8zzz3wZsCUAtmFy1b/fusy0/2+z76veg762lq4Jp+7sNIn6naqkccul\ncHaI2rM8zIHU2NPj875PX6o40QizO/bBoWd6Q4xqp3RM4jBunIMcvJ6fkugQ\nRZQa136/dOzgrGxCFEXW5LLIHZt8nPszYGesgxvYVuPtUh3yiG2UhL6jwHql\nyI0R\r\n=p4si\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "binary": {"note": "napi-build-tools is not an N-API module. This entry is for unit testing.", "napi_versions": [2, 2, 3]}, "gitHead": "9258d6192437620313306cc143290dd39f652891", "scripts": {"doc": "jsdoc2md index.js >index.md", "lint": "standard", "test": "mocha test/ && npm run lint", "prepublishOnly": "npm run test && npm run doc"}, "_npmUser": {"name": "inspiredware", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspiredware/napi-build-utils.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A set of utilities to assist developers of tools that build N-API native add-ons", "directories": {}, "_nodeVersion": "9.8.0", "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.2", "mocha": "^5.2.0", "standard": "^12.0.1", "jsdoc-to-markdown": "^4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/napi-build-utils_1.0.0_1536793865489_0.9390335545482287", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "napi-build-utils", "version": "1.0.1", "keywords": ["n-api"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "napi-build-utils@1.0.1", "maintainers": [{"name": "inspiredware", "email": "<EMAIL>"}], "homepage": "https://github.com/inspiredware/napi-build-utils#readme", "bugs": {"url": "https://github.com/inspiredware/napi-build-utils/issues"}, "dist": {"shasum": "1381a0f92c39d66bf19852e7873432fc2123e508", "tarball": "https://registry.npmjs.org/napi-build-utils/-/napi-build-utils-1.0.1.tgz", "fileCount": 6, "integrity": "sha512-boQj1WFgQH3v4clhu3mTNfP+vOBxorDlE8EKiMjUlLG3C4qAESnn9AxIOkFgTR2c9LtzNjPrjS60cT27ZKBhaA==", "signatures": [{"sig": "MEYCIQDDpxXCCA3lIUU5+wJI34nOks7B82bPa7CzzzgNT03F0AIhAJlLCBhsYbyYFHumZREMP+Y1xS8qEcw4DQJPKn93AtIP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41857, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbmadcCRA9TVsSAnZWagAAgJwP/RXkZJLEz0oEmMjJAuEZ\n7dIutrjcMCBbxc4LLI3DrOb/3G7L4ciAh29XUEK22lFj0orypOEIhdR+MjRv\nikvti/0fnSn+1pdnutPGKc09vFn75/RPGC5yskSKyyJvNqS1K+1E7/WBncSW\n6smgo1Ulgy84hGzbLG3ENQM1iVkk0TxrC0nNyA4vW0cO8bFQUFZYPOPex1VN\nx8eeWK6YO/i0WGB6nxLyj5FBh4poUDy2/PaltEbmZ+8rgRwMKyBTgjbhbohS\n2Zq5OatlqNmMyp5nZU31vFQzNyS7pUDjxkv/9Ajh/nOs7TVo6XG6/rF30F97\n0gowjnNqrivUOxLkwrPyfZhwSpzB/cejRXD0CCu3Is8842BsrClIV1yPvVLI\nFlw6SuzFgJnXg03Ud3VuxgCYEtOkcoIaXp5ad2+gIOQkjHUQdKRnHxprYPJq\nnvMAli+Dzh/DDmYvuNiXN8f5p3gTloxV8w8O989LJKpdXUuYqzk1onrFwhHg\nClEgNw+CwpECYIWPar9WqIBSX+5ZznbnYKgygdabAvMRE4AtmpVXslyDpm9m\nAMz2i6Hp60ZvHXntuZAPDKgTmRmI1B1JNm9YSM2CXTu33QZnrxoEvQeP1C8e\nTn86oTscblKONY1bi+6h5aEBjrKI5ux98tC3Hny9a1RGSwp75d3ddea2GTS0\n1D1a\r\n=L4mj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "binary": {"note": "napi-build-tools is not an N-API module. This entry is for unit testing.", "napi_versions": [2, 2, 3]}, "gitHead": "9258d6192437620313306cc143290dd39f652891", "scripts": {"doc": "jsdoc2md index.js >index.md", "lint": "standard", "test": "mocha test/ && npm run lint", "prepublishOnly": "npm run test && npm run doc"}, "_npmUser": {"name": "inspiredware", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspiredware/napi-build-utils.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A set of utilities to assist developers of tools that build N-API native add-ons", "directories": {}, "_nodeVersion": "9.8.0", "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.2", "mocha": "^5.2.0", "standard": "^12.0.1", "jsdoc-to-markdown": "^4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/napi-build-utils_1.0.1_1536796507380_0.010627767909799024", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "napi-build-utils", "version": "1.0.2", "keywords": ["n-api", "prebuild", "prebuild-install"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "napi-build-utils@1.0.2", "maintainers": [{"name": "inspiredware", "email": "<EMAIL>"}], "homepage": "https://github.com/inspiredware/napi-build-utils#readme", "bugs": {"url": "https://github.com/inspiredware/napi-build-utils/issues"}, "dist": {"shasum": "b1fddc0b2c46e380a0b7a76f984dd47c41a13806", "tarball": "https://registry.npmjs.org/napi-build-utils/-/napi-build-utils-1.0.2.tgz", "fileCount": 5, "integrity": "sha512-ONmRUqK7zj7DWX0D9ADe03wbwOBZxNAfF20PlGfCWQcD3+/MakShIHrMqx9YwPTfxDdF1zLeL+RGZiR9kGMLdg==", "signatures": [{"sig": "MEUCIQDXoLojxRQVaGhs/HHKSNQn3eo6easJiItPlmznhFQibgIgU1aIlXzrxDaUeRlTzv75PqPq1lkHf/59cf8ThuGj1bA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15524, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYYCCCRA9TVsSAnZWagAA8P8P/1W90WjRkJDAvLBay2Gl\nWqXtD0r1akSDhfnuuNBifTmDG0Xe5FmizbxnA5crTIGd9ECtEftjzKgbOxfN\n9rlMehqoaPCMifUWu/ZCQt1T+PxeOGupVL8TMKjn53nX5lSkLRuRB2odLgT9\nOZccVoQABkGg7uKBPMoiX0Skxhg7zdEDwILlMtt8D8IJQiswjdAsMGWs2qx6\n5IuII7+1zFjeQdncGKvUSh49Kp63kEAhuh2c7FRRJysbcpzduM5yCLam6yxE\nzc87IWEb0VYR+knQkMQwsdJbAWv9hzcSN0pTDgQRNS3O6gFSV0zvytuGRhAW\ncdRJ8m8997Ia84BZoxOHd1jmVirLJWwqlMxnnaO8x6/3DiUMwOVWishasRI8\nayGgQ21AGwTnjMFMN8iRe4kFqNqdHJbAbPCs7SJh1k8tmQYzG6Rn3KqqnfhW\nQ92+dk+bOGgxlh3Rh8QR9dwgHC/lOrVU9CHrF6bj7i6FKa7OLlKrp1sRqOBC\ns9boxonRDnNV0Xq3eExwJpSjLRSv4HgYkxuhnHyFyfojCKUi+t7S+vLFcoZR\n7dh8A0lqZ4qZT33XKU27MwevqGOzybGsSQLw2e4RK/NDa+3GlF7m4P4RHUgS\n8Utg94T5KyZ6DZk38aKHSvaelQ4T41OPpHuZsMBE28RUwESobnme9y+siGAG\nF58J\r\n=ckMG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "binary": {"note": "napi-build-tools is not an N-API module. This entry is for unit testing.", "napi_versions": [2, 2, 3]}, "gitHead": "220b7d37a1ccca05e6be9addff06a15bf0e523d2", "scripts": {"doc": "jsdoc2md index.js >index.md", "lint": "standard", "test": "mocha test/ && npm run lint", "prepublishOnly": "npm run test && npm run doc"}, "_npmUser": {"name": "inspiredware", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inspiredware/napi-build-utils.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "A set of utilities to assist developers of tools that build N-API native add-ons", "directories": {}, "_nodeVersion": "10.19.0", "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.2", "mocha": "^5.2.0", "standard": "^12.0.1", "jsdoc-to-markdown": "^4.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/napi-build-utils_1.0.2_1583448193765_0.7920376656517816", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "napi-build-utils", "version": "2.0.0", "description": "A set of utilities to assist developers of tools that build N-API native add-ons", "main": "index.js", "scripts": {"doc": "jsdoc2md index.js >index.md", "test": "mocha test/ && npm run lint", "lint": "standard", "prepublishOnly": "npm run test"}, "keywords": ["n-api", "prebuild", "prebuild-install"], "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/inspiredware/napi-build-utils#readme", "repository": {"type": "git", "url": "git+https://github.com/inspiredware/napi-build-utils.git"}, "bugs": {"url": "https://github.com/inspiredware/napi-build-utils/issues"}, "devDependencies": {"chai": "^4.1.2", "jsdoc-to-markdown": "^4.0.1", "mocha": "^5.2.0", "standard": "^12.0.1"}, "binary": {"note": "napi-build-tools is not an N-API module. This entry is for unit testing.", "napi_versions": [2, 2, 3, 10]}, "_id": "napi-build-utils@2.0.0", "gitHead": "29c3682f819178d70c58e5fdc40aa529fb75f7b7", "_nodeVersion": "23.6.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-GEbrYkbfF7MoNaoh2iGG84Mnf/WZfB0GdGEsM8wz7Expx/LlWf5U8t9nvJKXSp3qr5IsEbK04cBGhol/KwOsWA==", "shasum": "13c22c0187fcfccce1461844136372a47ddc027e", "tarball": "https://registry.npmjs.org/napi-build-utils/-/napi-build-utils-2.0.0.tgz", "fileCount": 6, "unpackedSize": 12670, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDqW2khYCUMAzDACJOgeHkSZ8L6/nH2KrYIDzJ1zKmiNAiAYnzGyXwh6Ae6R9mRv3OydXTi6l+a/SsRtc3kix64ttg=="}]}, "_npmUser": {"name": "inspiredware", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "inspiredware", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/napi-build-utils_2.0.0_1737083220974_0.8132843464995596"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-09-12T23:11:05.489Z", "modified": "2025-01-17T03:07:01.327Z", "1.0.0": "2018-09-12T23:11:05.603Z", "1.0.1": "2018-09-12T23:55:07.569Z", "1.0.2": "2020-03-05T22:43:13.884Z", "2.0.0": "2025-01-17T03:07:01.150Z"}, "bugs": {"url": "https://github.com/inspiredware/napi-build-utils/issues"}, "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/inspiredware/napi-build-utils#readme", "keywords": ["n-api", "prebuild", "prebuild-install"], "repository": {"type": "git", "url": "git+https://github.com/inspiredware/napi-build-utils.git"}, "description": "A set of utilities to assist developers of tools that build N-API native add-ons", "maintainers": [{"name": "inspiredware", "email": "<EMAIL>"}], "readme": "# napi-build-utils\n\n[![npm](https://img.shields.io/npm/v/napi-build-utils.svg)](https://www.npmjs.com/package/napi-build-utils)\n![Node version](https://img.shields.io/node/v/prebuild.svg)\n![Build Status](https://github.com/inspiredware/napi-build-utils/actions/workflows/run-npm-tests.yml/badge.svg)\n[![js-standard-style](https://img.shields.io/badge/code%20style-standard-brightgreen.svg)](http://standardjs.com/)\n[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)\n\nA set of utilities to assist developers of tools that build [Node-API](https://nodejs.org/api/n-api.html#n_api_n_api) native add-ons.\n\n## Background\n\nThis module is targeted to developers creating tools that build Node-API native add-ons.\n\nIt implements a set of functions that aid in determining the Node-API version supported by the currently running Node instance and the set of Node-API versions against which the Node-API native add-on is designed to be built. Other functions determine whether a particular Node-API version can be built and can issue console warnings for unsupported Node-API versions.\n\nUnlike the modules this code is designed to facilitate building, this module is written entirely in JavaScript.\n\n## Quick start\n\n```bash\nnpm install napi-build-utils\n```\n\nThe module exports a set of functions documented [here](./index.md). For example:\n\n```javascript\nvar napiBuildUtils = require('napi-build-utils');\nvar napiVersion = napiBuildUtils.getNapiVersion(); // Node-API version supported by Node, or undefined.\n```\n\n## Declaring supported Node-API versions\n\nNative modules that are designed to work with [Node-API](https://nodejs.org/api/n-api.html#n_api_n_api) must explicitly declare the Node-API version(s) against which they are coded to build. This is accomplished by including a `binary.napi_versions` property in the module's `package.json` file. For example:\n\n```json\n\"binary\": {\n  \"napi_versions\": [2,3]\n}\n```\n\nIn the absence of a need to compile against a specific Node-API version, the value `3` is a good choice as this is the Node-API version that was supported when Node-API left experimental status.\n\nModules that are built against a specific Node-API version will continue to operate indefinitely, even as later versions of Node-API are introduced.\n\n## History\n\n**v2.0.0** This version was introduced to address a limitation when the Node-API version reached `10` in NodeJS `v23.6.0`. There was no change in the API, but a SemVer bump to `2.0.0` was made out of an abundance of caution.\n\n## Support\n\nIf you run into problems or limitations, please file an issue and we'll take a look. Pull requests are also welcome.\n", "readmeFilename": "README.md", "users": {"flumpus-dev": true}}