{"_id": "better-sqlite3", "_rev": "168-0d59347e2d88a11509d75e5125d77803", "name": "better-sqlite3", "dist-tags": {"latest": "12.2.0"}, "versions": {"0.5.0": {"name": "better-sqlite3", "version": "0.5.0", "keywords": ["sql", "sqlite", "sqlite3", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@0.5.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "a2dd0fb5c4b72d7fd6e82afdd7b2e00fce3c9031", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-0.5.0.tgz", "integrity": "sha512-iVEIUMpBxozPu1RFoR37gJRHl6mudl1fUVgThSSo9CtkFkScjc3jtvBOP3gY4m/nVrgHmfUR1vDz7jkTKQ5YwA==", "signatures": [{"sig": "MEYCIQCP/Y6VXAGDS/YXi1oKi074eOevLcO8RQnpheUlV5XIDwIhAPFw2JAh19fAChKx6mTkUZ/lasg6s8l6fDam6eoXG8Jt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "a2dd0fb5c4b72d7fd6e82afdd7b2e00fce3c9031", "gitHead": "4221a5f0a95abc2ab963498c3b639c2df6b9cbb2", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --timeout 5000 --slow 5000", "install": "node-gyp rebuild"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Simple and expressive SQLite3 bindings for Node.js, with full transaction support", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-0.5.0.tgz_1473269782271_0.21975734480656683", "host": "packages-12-west.internal.npmjs.com"}}, "0.6.0": {"name": "better-sqlite3", "version": "0.6.0", "keywords": ["sql", "sqlite", "sqlite3", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@0.6.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "a7238b246b23d900f47d2b8ea23dfc1ad772a97b", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-0.6.0.tgz", "integrity": "sha512-HS4K+3k71kBT+ivzhcA/oFD5DdnFnx5ERNBvaQzhJdeeojOdBevayMfjVlt7oMn8Wo5aadhAIy4WgpEE2mP80g==", "signatures": [{"sig": "MEYCIQDnR+4I0WNiwGpDfZ3O0f/E6nKoVQFJPkWmGebnKa0p6wIhAOJp3VQZgfO7o8fl0XrVYTCBNo/l3fBu3e9dJDyx+hv2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "a7238b246b23d900f47d2b8ea23dfc1ad772a97b", "gitHead": "0704e78369c410e74910f3235bb2ffd48404764a", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --timeout 5000 --slow 5000", "install": "node-gyp rebuild"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Simple and expressive SQLite3 bindings for Node.js, with full transaction support.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-0.6.0.tgz_1473285993163_0.2808101049158722", "host": "packages-12-west.internal.npmjs.com"}}, "0.6.1": {"name": "better-sqlite3", "version": "0.6.1", "keywords": ["sql", "sqlite", "sqlite3", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@0.6.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "173d5ae081b3c8c1cc62f35915e5fc4058172b72", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-0.6.1.tgz", "integrity": "sha512-JdJISV6Q6gc8XSVfv218cXORJHFvsaQa7qoKSOZgDXkP/EYBV3nKu9FnStETPSQjaDIcmT+sZh58U8Vdbkx6gg==", "signatures": [{"sig": "MEQCIG8/plwppZbuTZDbgp+HBoaM8vu2EaTmk4U2fCJqdsIUAiA1rMjnJFmqF5yghWEjqGS39B4g0agjWBVAjNMohi1aCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "173d5ae081b3c8c1cc62f35915e5fc4058172b72", "gitHead": "9f289eaa667ecf47d4c8adf68219abe6f364765f", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --timeout 5000 --slow 5000", "install": "node-gyp rebuild"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Simple and expressive SQLite3 bindings for Node.js, with full transaction support.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-0.6.1.tgz_1473286503648_0.7985809207893908", "host": "packages-12-west.internal.npmjs.com"}}, "0.6.2": {"name": "better-sqlite3", "version": "0.6.2", "keywords": ["sql", "sqlite", "sqlite3", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@0.6.2", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "1ff331f741da3106499b4309c241f7bbc1c33ab8", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-0.6.2.tgz", "integrity": "sha512-CQB2FZ/wLKYuAFfkreHAyhlEiDtERSHejeRyal9Huyou55xvVkYrqNzjg8pwnLANmE7GBySVF2jHo1uaQO+nZw==", "signatures": [{"sig": "MEUCIQCeWkUuRNKiednGQN/3G3UvKW7WCYOke/NRxxeXHgp+aAIgPPaBe7xyJMH0GBsPDWRhUk3VMpI2hByErhIeKRHQ+CY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "1ff331f741da3106499b4309c241f7bbc1c33ab8", "gitHead": "e3d3ae727533e20442344bf96953207f30ce31b5", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --timeout 5000 --slow 5000", "install": "node-gyp rebuild"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "Simple and expressive SQLite3 bindings for Node.js, with full transaction support.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-0.6.2.tgz_1473292019230_0.34449172276072204", "host": "packages-16-east.internal.npmjs.com"}}, "0.7.0": {"name": "better-sqlite3", "version": "0.7.0", "keywords": ["sql", "sqlite", "sqlite3", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@0.7.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "568929b34eccb1d1fbb24c735748d0bfbd0a9fad", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-0.7.0.tgz", "integrity": "sha512-mowdytWW+tZZNxi2bZ41axHgozPpV7Y5Xq6fiCS4DLGtsrOkvEXDW1fNPrnP2+VVGgh8tASTqSXWggb/jNCShg==", "signatures": [{"sig": "MEUCIQCMBzBDKu2+xKIwa8FJGpEyc43mFLMy8EqF2qchjIMliwIgVRqYrjYn/2kRQlwPvLrkijKcBNRXbhhTH+Gp1mhUtfM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "568929b34eccb1d1fbb24c735748d0bfbd0a9fad", "gitHead": "545a866982ae4a49a3f5abeba1a352c458bd65ae", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --timeout 5000 --slow 5000", "install": "node-gyp rebuild"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and most carefully designed library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-0.7.0.tgz_1473314699342_0.6070796309504658", "host": "packages-12-west.internal.npmjs.com"}}, "0.8.0": {"name": "better-sqlite3", "version": "0.8.0", "keywords": ["sql", "sqlite", "sqlite3", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@0.8.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "e1fa94cd685b1bc07c7ac754d42ea5b56d85a255", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-0.8.0.tgz", "integrity": "sha512-RPj1NrBOLoLwoLTenEqUPtDHRklppXmodE2IAdRnKioyP9Is+ku+W7fw1mBGm5ezce/RIWlQ1tpVgQioP6ue1g==", "signatures": [{"sig": "MEUCIGwTuIiEPy7cIIhKj00/zRNTDoaGM7KtGxs7WUMHSOiBAiEApobzZaRXf+TRb9H8DbFlx2hlrZbwhHxCpd16Ut3Hj1Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "e1fa94cd685b1bc07c7ac754d42ea5b56d85a255", "gitHead": "8534916222c70c25e0f196c1960e3da9ae3d4f02", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --timeout 5000 --slow 5000", "install": "node-gyp rebuild", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and most carefully designed library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-0.8.0.tgz_1473456348994_0.4491522735916078", "host": "packages-12-west.internal.npmjs.com"}}, "0.8.1": {"name": "better-sqlite3", "version": "0.8.1", "keywords": ["sql", "sqlite", "sqlite3", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@0.8.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "4e67a4a54272c7bbb83fec66367d2c10aa8780c5", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-0.8.1.tgz", "integrity": "sha512-LAqq0E6GioKwaJlCepV16CjMWXeWPZsdVKEzVQS8wUm7PeFTas16LljlAyBzcP9zwKyOCrNlQF9XDDM37ei72A==", "signatures": [{"sig": "MEQCIFpUQZAHgk8d9ptbjEwkKAqVGAG0gzczVXt/lIlxvvurAiAF7zV6sa795TbStXpGZ7Uw/Z8MIAOBGGbOyujvISAv8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "4e67a4a54272c7bbb83fec66367d2c10aa8780c5", "gitHead": "bd06c327b1383c3b2aad615d83b9bd13000d1f6e", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --timeout 5000 --slow 5000", "install": "node-gyp rebuild", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and most carefully designed library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-0.8.1.tgz_1473456667676_0.08138133212924004", "host": "packages-12-west.internal.npmjs.com"}}, "0.8.2": {"name": "better-sqlite3", "version": "0.8.2", "keywords": ["sql", "sqlite", "sqlite3", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@0.8.2", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "ef888d058bf8c3d3af6d7f60861f4a850fa25f77", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-0.8.2.tgz", "integrity": "sha512-G2N3AhPEu/ucZ2T4D+pa+ypGhwrlPn8rtKg0TpLhA2eAuo1TUlNHNbdk75n7b5x1BeivTdeinpaKAOGEZqwamg==", "signatures": [{"sig": "MEYCIQDfd3hk79BCEH7UdxdYGLnpL4tK8GVepT9lGHmAibUTjwIhAL/LQ6F+V6pkTyllgMeup8pIScNHquscGzTykBLc8V00", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "ef888d058bf8c3d3af6d7f60861f4a850fa25f77", "gitHead": "c01198636afda981a7b7ca0889692aae5371734a", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --timeout 5000 --slow 5000", "install": "node-gyp rebuild", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and most carefully designed library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-0.8.2.tgz_1473461179903_0.024764024885371327", "host": "packages-16-east.internal.npmjs.com"}}, "0.9.0": {"name": "better-sqlite3", "version": "0.9.0", "keywords": ["sql", "sqlite", "sqlite3", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@0.9.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "b7ae66666a8144c44c13c1319c2a42a0cc766e9e", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-0.9.0.tgz", "integrity": "sha512-efUeRrrEoPOgWVl8LgU+/jDfVkFq+VchKnHIHkZycuxncmDOJQHoZjyJS7dsReqMOCRqJRBvi+621g/e7/XP7A==", "signatures": [{"sig": "MEUCIQDeObv2ZGgtBAZwRAsPDET3dqOtWVOvZ8s53nyai4BrXgIgFjvdhTGWsE0ZxlBuZd+dY+gihgqfkWOw1fro2G9/wJw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "b7ae66666a8144c44c13c1319c2a42a0cc766e9e", "gitHead": "3a98b8ed71c3a8429a7fe2ee20adebd2466fe002", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --timeout 5000 --slow 5000", "install": "node-gyp rebuild", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and most carefully designed library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-0.9.0.tgz_1473623875546_0.6910425273235887", "host": "packages-12-west.internal.npmjs.com"}}, "0.9.1": {"name": "better-sqlite3", "version": "0.9.1", "keywords": ["sql", "sqlite", "sqlite3", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@0.9.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "993c90f9717c958814c3de89de9b13ce7344d314", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-0.9.1.tgz", "integrity": "sha512-xqEZg0ImIuIS0E7G9LHrFIFn1xVtmYmS69ngiC5pu8NTAYE0JdPd6qxMhYyD41Ih4NeKne/0ug4s7lWsAPgZFA==", "signatures": [{"sig": "MEYCIQCBNxIH+DZgAzGqIICAbda6XhwkCvKc69qlRW1TQIKbzgIhANxCIq55IotnQHMI31xYeQ6lG42FkR3dVkN3SNLMdg/I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "993c90f9717c958814c3de89de9b13ce7344d314", "gitHead": "9ec8d8f799bf0dbe8f4f38345041a1ef395d4c65", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --timeout 5000 --slow 5000", "install": "node-gyp rebuild", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and most carefully designed library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-0.9.1.tgz_1473625308038_0.20102432207204401", "host": "packages-12-west.internal.npmjs.com"}}, "0.9.2": {"name": "better-sqlite3", "version": "0.9.2", "keywords": ["sql", "sqlite", "sqlite3", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@0.9.2", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "4651a8083e346124c9c7a9f114db1330d9810f02", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-0.9.2.tgz", "integrity": "sha512-O3TANM3TKi1MJGru4J/xyfKkHhBtyW1Urxb6wqdUnfk72JQIKh9gVvL/uKJiW49oimphVh94vAIttJlvAPC8cw==", "signatures": [{"sig": "MEQCIAHKj6ix8H6RWa4MgN2pxC5xJirMPMqOf2pxTdKBBgq5AiAy5lSlbJemZdpU07xH6XmeP4P+0GumSGeoVp9qIz9RLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "4651a8083e346124c9c7a9f114db1330d9810f02", "gitHead": "c95a877c7661944331917c914cb0b947ed3a679e", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --timeout 5000 --slow 5000", "install": "node-gyp rebuild", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and most carefully designed library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-0.9.2.tgz_1473683188773_0.7329005398787558", "host": "packages-16-east.internal.npmjs.com"}}, "0.10.0": {"name": "better-sqlite3", "version": "0.10.0", "keywords": ["sql", "sqlite", "sqlite3", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@0.10.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "e3994a7c19fa5c72d12610009c88a55c1d688df2", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-0.10.0.tgz", "integrity": "sha512-m5YvW3URpm6zV+ymfyFHUhXcAF9CELUP7VTR8F+IIpALKEyos56eXuDDImfhgL+5yo+MVYNMoTvHXFk4yiYCyw==", "signatures": [{"sig": "MEUCIDZLKMzssYe4EBwTKswu1RHG/zikep2QeUBPiJdernocAiEAkjHwbe01HIYscqTamUW+B2rm7emkZwizXzQMiwao92Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "e3994a7c19fa5c72d12610009c88a55c1d688df2", "gitHead": "9f01a28c8a022c646bc6c64d85fcb883975b780d", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --timeout 5000 --slow 5000", "install": "if [ \"$CI\" = \"true\" ]; then node-gyp rebuild --debug; else node-gyp rebuild; fi;", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and most carefully designed library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-0.10.0.tgz_1473729226597_0.06618989328853786", "host": "packages-16-east.internal.npmjs.com"}}, "0.10.1": {"name": "better-sqlite3", "version": "0.10.1", "keywords": ["sql", "sqlite", "sqlite3", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@0.10.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "e7f495303073211d2c764cc7e44a817ef7c21826", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-0.10.1.tgz", "integrity": "sha512-C2u4emFzTKb4895KUweKLKrn61rarLy75rjP1xGVv28zOC+CdOr1BHbiux22hGnC2bwSOQftZRrjyfLBA1A3og==", "signatures": [{"sig": "MEUCIA2neDSCqFXk1CDI27DJXEuRCshIm/INjxcLcwtw9bU6AiEA315nxn2sDfLlwSzaN9G6PTJ/fYqktOsK10V6EeeOVOQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "e7f495303073211d2c764cc7e44a817ef7c21826", "gitHead": "662b3cda0745e21020002bab383f2cedbef75d92", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --timeout 5000 --slow 5000", "install": "if [ \"$CI\" = \"true\" ]; then node-gyp rebuild --debug; else node-gyp rebuild; fi;", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and most carefully designed library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-0.10.1.tgz_1473729895376_0.08902804064564407", "host": "packages-12-west.internal.npmjs.com"}}, "0.10.2": {"name": "better-sqlite3", "version": "0.10.2", "keywords": ["sql", "sqlite", "sqlite3", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@0.10.2", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "312fa96b0dbf3aad341af46f2fb3a79331c1737c", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-0.10.2.tgz", "integrity": "sha512-U65C4i2yHqDFtFnyN7RpXs7lDjuhpLArz4oe30fLLsMJvcK7YC8KI0DdlhYmKNFUVjFPbeJY1mahpD/t1MPCxA==", "signatures": [{"sig": "MEYCIQC8Ha+FJG2J6+o9atnO84INXnnNCrbzkO+Ks8Y6/zGuFAIhAKKE6Zn2M1BTmX8v+QjVLgjKxq2UqBYpn2TlnhKHDynI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "312fa96b0dbf3aad341af46f2fb3a79331c1737c", "gitHead": "0f4be0bbc8d116a4155f9afaa199e04addc75d59", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --timeout 5000 --slow 5000", "install": "if [ \"$CI\" = \"true\" ]; then node-gyp rebuild --debug; else node-gyp rebuild; fi;", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-0.10.2.tgz_1473736075828_0.6594806800130755", "host": "packages-12-west.internal.npmjs.com"}}, "0.10.3": {"name": "better-sqlite3", "version": "0.10.3", "keywords": ["sql", "sqlite", "sqlite3", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@0.10.3", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "8d75e078a50621dd0a5d88bd2374caa5fa0ae474", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-0.10.3.tgz", "integrity": "sha512-C56rjpknp8B1c1n2zW3V2jq+EA90vulnFWVVBbAMmRYV6XQdas8uVZB7zoOG2T4bIdq4xmrHDNaTBrFOsPZDMg==", "signatures": [{"sig": "MEUCIGJ2DGKW0BI3O5lPjRSeEQ0O97x+jAlo3JMxZ37EHXMRAiEAg0JlrFSq3rE1uO/XMcFFbXTCnS03/1eN3LchoaSkNV4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "8d75e078a50621dd0a5d88bd2374caa5fa0ae474", "gitHead": "bc95f0b83f468d8a0e1c35624bfa06befea22b77", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --timeout 5000 --slow 5000", "install": "if [ \"$CI\" = \"true\" ]; then node-gyp rebuild --debug; else node-gyp rebuild; fi;", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-0.10.3.tgz_1473737030772_0.8011072434019297", "host": "packages-16-east.internal.npmjs.com"}}, "1.0.0": {"name": "better-sqlite3", "version": "1.0.0", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@1.0.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "de45aa7df5c356ca3bda9c46e3d350d2aa0b16dc", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-1.0.0.tgz", "integrity": "sha512-W9L+u3UPT5R3/KsNUI9SLMEEYN6khDF0o5CRnLjsFcAz3ikIes3qkfQQs4pyUnUn5SgVqFKclRMJ/90AOI5xmw==", "signatures": [{"sig": "MEUCID3oPJ7un+QLUjttYth61s5AcwN0P2toh6OB4WJxlCxHAiEA356bDwgzlSjd3uPaBiwJe/avcAtjuoB2A5YAxBliJkk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "de45aa7df5c356ca3bda9c46e3d350d2aa0b16dc", "gitHead": "473316aa66049e5ec5f3608e55da08f8a779cabe", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --timeout 5000 --slow 5000", "install": "if [ \"$CI\" = \"true\" ]; then node-gyp rebuild --debug; else node-gyp rebuild; fi;", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-1.0.0.tgz_1473783814453_0.16618876764550805", "host": "packages-16-east.internal.npmjs.com"}}, "1.0.1": {"name": "better-sqlite3", "version": "1.0.1", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@1.0.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "19dff7b8cd6006c7522d2cd36bb6b2ab32a8719a", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-1.0.1.tgz", "integrity": "sha512-d1IvynrLZR79EvOYFEJ64VXZdapzO6ga4ufN32gVe71bO91zGJoah6xmWsU1M4F9DYRopEe9G1EgLGxUDe3uKA==", "signatures": [{"sig": "MEUCIEuf2Z0gbOz5Otgqs73aAHnONLoZdkcP/6DOiWbaykcFAiEAr+zVYuuIwEa3CfN3apeijvSH+czJwIFKYf5+sIukI6s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "19dff7b8cd6006c7522d2cd36bb6b2ab32a8719a", "gitHead": "e5866c7d789e87ce9ffa5ec19a31ae757043bd42", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --timeout 5000 --slow 5000", "install": "if [ \"$CI\" = \"true\" ]; then node-gyp rebuild --debug; else node-gyp rebuild; fi;", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-1.0.1.tgz_1473819096685_0.4651081080082804", "host": "packages-16-east.internal.npmjs.com"}}, "1.0.2": {"name": "better-sqlite3", "version": "1.0.2", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@1.0.2", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "9c45dccd8eacfa9ff5bb7db302c518e2cdc07171", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-1.0.2.tgz", "integrity": "sha512-Xo4i9vCEqfRcmbMAPaJ3d3H7XdO73qqI4Afp4JLQkzILHke3hevhl5KvN0I8ltdute7kD/RPkI/WtYMGJtRcbw==", "signatures": [{"sig": "MEQCIGom9whcixENSiknRf3ryOp42vSE5gyhKCJTl3hB5hnTAiBOTxojygTqequh/nbucudjyWQWCAK0yafQJ4Yym6U36A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "9c45dccd8eacfa9ff5bb7db302c518e2cdc07171", "gitHead": "ce56f508eab408d1a36d843e1c2e82804f4d672f", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --timeout 5000 --slow 5000", "install": "if [ \"$CI\" = \"true\" ]; then node-gyp rebuild --debug; else node-gyp rebuild; fi;", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-1.0.2.tgz_1473870333135_0.1652839609887451", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.0": {"name": "better-sqlite3", "version": "1.1.0", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@1.1.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "7d09ad7fd9733e28d540945ab0b5121a5bec3789", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-1.1.0.tgz", "integrity": "sha512-FzJud2cdt7gbgvrydaETmvJ2J9xo/wzlmK2/Aql5TPZa+zQV8yQgBS/iZ2glCDR8MH21wjBEBJ/KpeenDyGq7g==", "signatures": [{"sig": "MEYCIQC8ejqYVa14P9W8tesuTO6W5rnliJEruFaKdcyPzGw/lwIhALWhZw8nOtltODJwDdFNFTQXa/gXkw0QyPaKW9YKppIp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "7d09ad7fd9733e28d540945ab0b5121a5bec3789", "gitHead": "57c783a84db706da804409c4602f464200ec056f", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --timeout 5000 --slow 5000", "install": "if [ \"$CI\" = \"true\" ]; then node-gyp rebuild --debug; else node-gyp rebuild; fi;", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-1.1.0.tgz_1474065043140_0.6643892079591751", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.1": {"name": "better-sqlite3", "version": "1.1.1", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@1.1.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "5ade99cb8ea3cbaee466d18be5a3f61f19d161a7", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-1.1.1.tgz", "integrity": "sha512-YqseBte8yi6POzE6CTO13gnkwT7AmIO+8mcpWzYlPzVv5t1s26XRxhIdQdKS+hgozoN2/fvkJRWx3GJ+JB7Iww==", "signatures": [{"sig": "MEUCIFs6stBoSe5ZCdAs4eEn/7l7nHz6m/kOZm0nHHZ5hdvIAiEArJpuoJlqOmRwUNvOb84kuXIXuBIhTute+ojp1/uztCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "5ade99cb8ea3cbaee466d18be5a3f61f19d161a7", "gitHead": "67d52b75b9fd3b59aa7a19a0cc26111f4fa1ebe2", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --timeout 5000 --slow 5000", "install": "if [ \"$CI\" = \"true\" ]; then node-gyp rebuild --debug; else node-gyp rebuild; fi;", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-1.1.1.tgz_1474129625105_0.7814448266290128", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.2": {"name": "better-sqlite3", "version": "1.1.2", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@1.1.2", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "f80572613edb998f5ef6fa16aeed0398d9803613", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-1.1.2.tgz", "integrity": "sha512-Z4kT2dztptU8PRQgAs85XUWVto6rKfWzy0mqyUaz2y2Ask5+5hXbLIILSN3WRmdXzbEQ9ZxQJWea9x3fZ0jMzg==", "signatures": [{"sig": "MEQCIEd/WHozJOhy7EsbUqwGbeBQwSFELDCEfbkjtsWF5V2KAiBT9UjEtCfMsVDor6oyRII4Cr7os1dFAcnyYr9EOlZKlg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "f80572613edb998f5ef6fa16aeed0398d9803613", "gitHead": "2dd256e52319b9e988e560bbc0e6cfb4e245b4b9", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "if [ \"$CI\" = \"true\" ]; then node-gyp rebuild --debug; else node-gyp rebuild; fi;", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-1.1.2.tgz_1474142281029_0.7356479584705085", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.3": {"name": "better-sqlite3", "version": "1.1.3", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@1.1.3", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "7f591c7df52691ae084c7aa3e15dc788db6d206c", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-1.1.3.tgz", "integrity": "sha512-RpfM3RwzeMb3zon7UiafrqjnS96JsMiGZ3s8oe5toeuGggcJdzWV8oy7JVywSK/JHLMCAWQgzMu5mwiwB1phlQ==", "signatures": [{"sig": "MEUCIF6KJYeZlEqGGVKL2Pn1zOU2UpKTI1KT05sKxW7UdVqEAiEA/UjGZHj8hzn+JtPQjwaTI2P4uTcBSPJbrrx8ar3blrU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "7f591c7df52691ae084c7aa3e15dc788db6d206c", "gitHead": "3c70f61ef26609629b518229bcbede306e6f9f1e", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "if [ \"$CI\" = \"true\" ]; then node-gyp rebuild --debug; else node-gyp rebuild; fi;", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-1.1.3.tgz_1474156728196_0.014681276865303516", "host": "packages-16-east.internal.npmjs.com"}}, "1.2.0": {"name": "better-sqlite3", "version": "1.2.0", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@1.2.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "9387367a0901ef6c0ad88c2fd00de9babe098574", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-1.2.0.tgz", "integrity": "sha512-MHWIiD25POlPq6IJKRuhlSZt4MtcpvHAASWRr9KosJU2gyUEA/nGxnmgLhrWV18rMQ6c/ffWwBz0hHyM4p2H+A==", "signatures": [{"sig": "MEYCIQCFd2ZDWnf1q169Q779xIvw7AH9KWMynVQE2NCCbbPDhAIhAJWKCQgK31rtze9B6hSXUsX+/Ir7145TgMrx0ijDLlsJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "9387367a0901ef6c0ad88c2fd00de9babe098574", "gitHead": "197329cf7b9700396b9bb363df095d6057a6a246", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "if [ \"$CI\" = \"true\" ]; then node-gyp rebuild --debug; else node-gyp rebuild; fi;", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-1.2.0.tgz_1474172487989_0.4282037827651948", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.1": {"name": "better-sqlite3", "version": "1.2.1", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@1.2.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "cbd9963cd92f6a6a50ccc8c62a882af4cb702a0e", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-1.2.1.tgz", "integrity": "sha512-+8OLrNrCnpHvwY9ERQM+YGN60NgNwEowJuaV5nEa/uRlltD6saAZzULB+/aw/hoOuqdccQ9/csBA3mHp+/c2Rg==", "signatures": [{"sig": "MEQCIDhAEn/9VN5dGe0R/Us44qJTtWH5bo0C7fgkYFThx2IhAiBlMf020UK9NJ5NpJi52mMBbzepD7ygpkbfuO0gmd1y/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "cbd9963cd92f6a6a50ccc8c62a882af4cb702a0e", "gitHead": "840ab403783109a8950cce064d6ff61e59aab871", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "if [ \"$CI\" = \"true\" ]; then node-gyp rebuild --debug; else node-gyp rebuild; fi;", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-1.2.1.tgz_1474664320139_0.36855031992308795", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.1": {"name": "better-sqlite3", "version": "1.3.1", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@1.3.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "311a0ffacfb036c7b68d7f5f64539419c43a491a", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-1.3.1.tgz", "integrity": "sha512-S8QaYet7qiv05c49cHM8X3Gs0Kc+SsP4FiFixqCCsapku2LQhJqkW+9fLjEMwtfED2aHSFh9JCMeFRu4SHyqoA==", "signatures": [{"sig": "MEYCIQCczsVisBX5h/tK8Up1JrvmVRZ93ViytGcHK7wnHHfv+gIhAIuNT96knMTeb3yN++uFqQv6LD44S73Yq6zHfvXqb6bY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "311a0ffacfb036c7b68d7f5f64539419c43a491a", "gitHead": "a7a6d654e0032410c57d5cb880369e44aa85673a", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "if [ \"$CI\" = \"true\" ]; then node-gyp rebuild --debug; else node-gyp rebuild; fi;", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-1.3.1.tgz_1475896018368_0.33405659487470984", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.2": {"name": "better-sqlite3", "version": "1.3.2", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@1.3.2", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "667a3637ee04578a12ffea50d793387e43770a29", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-1.3.2.tgz", "integrity": "sha512-5BMK3uhxu7/yCg2KeYiRvCCdzNSceNXpTu8jjXUkn5cs7KmgshzL2JaoNRqKhEP1JMFRNyAfRNyGMcOge0uWNQ==", "signatures": [{"sig": "MEUCIEKPLQNGQhqysJ5Rr624WTh6Dvvqco7J62SX/YViSG/TAiEA4nPVEsedGnGrH13dYikz2UhiQr4aBIsafYgTw3f7ZDA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "667a3637ee04578a12ffea50d793387e43770a29", "gitHead": "9786df20e88044e6d23cba0f7c867e43c0a16840", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-1.3.2.tgz_1475956825369_0.12692883028648794", "host": "packages-16-east.internal.npmjs.com"}}, "1.3.3": {"name": "better-sqlite3", "version": "1.3.3", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@1.3.3", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "df9856df770baf4d6328f60df7a13c670c57ad72", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-1.3.3.tgz", "integrity": "sha512-Xs3evzD2wDX3l2hRD+z9ggqhMwcajxaltovcEeWW+STCmuU9/jaiLQ/3Ozgptn3h89AEpxElvi+uso7uain0iw==", "signatures": [{"sig": "MEQCICpWvwXNfuGyy/rnytHswhzpVc4zt/MZ0JXAhI8b09juAiAgltxTKk0Dm0lMoKA8yoeL1lFO4mjpqSMVR3JyefZRkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "df9856df770baf4d6328f60df7a13c670c57ad72", "gitHead": "878c18f35b86dbfc6e8e4fc82ae435f2b9afcb7a", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-1.3.3.tgz_1475969957668_0.7818182772025466", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.4": {"name": "better-sqlite3", "version": "1.3.4", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@1.3.4", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "8bde37d00fa9afae819fc595d81baea48cec8602", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-1.3.4.tgz", "integrity": "sha512-m+ZdoqNmlyapNzdUOQ4WEkRWpQkPnwqt63CrWL3TqQ5/dxUQXueZWFIUwB+ijgipJHT7zGmhki0Z1KW3cJ7F9g==", "signatures": [{"sig": "MEYCIQCvIT+X0eoqeHkA3vAXHaxAbQ9VlfWo4FWyk3Lk7vuFRgIhALblEXOu5210avccFRVm7g/AR99ORR8kexmpS4LJ2/m1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "8bde37d00fa9afae819fc595d81baea48cec8602", "gitHead": "bb8c535fefb30d51b7c6c773c2615eff95d32731", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-1.3.4.tgz_1475970565994_0.9612368559464812", "host": "packages-16-east.internal.npmjs.com"}}, "1.3.5": {"name": "better-sqlite3", "version": "1.3.5", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@1.3.5", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "89504fc3f5e011f2b64ab7caee7f9f6f12d7c3dc", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-1.3.5.tgz", "integrity": "sha512-ij6KjptKwoFHk0Hd4ClrBQf7OtbbGLbtdloTEhFua/tA/mP8SugI1uX1hiao6JblD6qClSboNTzfbUWyURyGoQ==", "signatures": [{"sig": "MEUCIQCr5OEiaZ4sjQUKKG2MAQWlolqtUgMKgfzUw1MeiB5UuwIgRVMftP/03KUgpxW64W0Yc6IM1umKUtkU3/M3zYD+mTE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "89504fc3f5e011f2b64ab7caee7f9f6f12d7c3dc", "gitHead": "2522681b0d8a59ac00eaccc8aa304efddf01b7f3", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-1.3.5.tgz_1475971062334_0.05612980853766203", "host": "packages-16-east.internal.npmjs.com"}}, "1.3.6": {"name": "better-sqlite3", "version": "1.3.6", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@1.3.6", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "01d9d8654cf827c0fac763289a2a08749ad6c932", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-1.3.6.tgz", "integrity": "sha512-kqri6sQy9Ou9ArltpV8Xbnx6Lh5kq0+z7PfnFbdRQ4TYa3CsRJLH9dA0SXhVW5o0hlTaQIEz+BzUa+IXu4RbhA==", "signatures": [{"sig": "MEUCIQCt1mZJ/+YkL6ugE0tE5VgSL52A6JNZuEmoosxdJswHMAIgQuCUJsvkvxBKwd2Stdijh+V703p1nk93e2fRyjE2ApI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "01d9d8654cf827c0fac763289a2a08749ad6c932", "gitHead": "a8152b64a5473b089eff981fbd8553c4dcc54a00", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-1.3.6.tgz_1476224395983_0.8823113569524139", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.7": {"name": "better-sqlite3", "version": "1.3.7", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@1.3.7", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "1f9091f1ce2fb1a81a2a646f6776ffa91f9213c4", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-1.3.7.tgz", "integrity": "sha512-d9HBiltpq6mX9upnL4oTX61P+aiVGDoPMnutlTszR/pvdEoSup7DEH2YEK+H5jgVRl2WhtbFWJf/br04dVCnpA==", "signatures": [{"sig": "MEQCIDddp0jPMilp+K3c2vXyfRB5A4Sw5QRX8030B6iYyFSSAiAKRIplAXFI8lr42IKuDQ7Yr48dJTAbX2y/YUjSFs72Fw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "1f9091f1ce2fb1a81a2a646f6776ffa91f9213c4", "gitHead": "2606eef344cdf22f23ec8bd403f4aeb305f644cb", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-1.3.7.tgz_1476258384416_0.32970964163541794", "host": "packages-16-east.internal.npmjs.com"}}, "1.3.8": {"name": "better-sqlite3", "version": "1.3.8", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@1.3.8", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "43768487cd3db8559d7af945cb8c2db43860d979", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-1.3.8.tgz", "integrity": "sha512-QbaiQDNcBrDQmM/haWIPn9b1bznett1EVOEeRJLe4X/hhS+snuMio3gV+jIFrfBUrPqF2DHUMFLw6iFWkYNJmg==", "signatures": [{"sig": "MEYCIQDn2ntW6Wzy+9ychpIkLawJu4U0lfQnV8IpcKxSeewYKAIhAIqITbCDTZPEGRAheYT7OWMiJwDaRhR2bX9ymC6V4Rjf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "43768487cd3db8559d7af945cb8c2db43860d979", "gitHead": "681a4dd4951d26a1a67895de212b8d72729be42e", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-1.3.8.tgz_1476340828797_0.6122547152917832", "host": "packages-16-east.internal.npmjs.com"}}, "1.3.9": {"name": "better-sqlite3", "version": "1.3.9", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@1.3.9", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "73c2a576111d3057d329dbbc29c44898a99380bb", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-1.3.9.tgz", "integrity": "sha512-gji5XoeDmLhf6Ghugfsy9ptpo2C4mmF08SNYoN1tWOsoAnmEtgOo0d4eZn20Kns8XTnbQY0MYf5u3pJT+ot8YQ==", "signatures": [{"sig": "MEQCIAHW3PEQEZOqmZLioFxKAlwIsiLHo7YKOzi5b/0BLb34AiB7n37w8YZz5PT5YXXV52r9l5bFppLMwCMkoQvmWz1cCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "73c2a576111d3057d329dbbc29c44898a99380bb", "gitHead": "464e175b8a192ba1e4379cb4d7ccafdc6a7a294e", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"nan": "^2.4.0", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-1.3.9.tgz_1476482110735_0.02183528384193778", "host": "packages-12-west.internal.npmjs.com"}}, "1.4.0": {"name": "better-sqlite3", "version": "1.4.0", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@1.4.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "bbb0b7f12720817b1e8c789255b8ccc3e9d467f0", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-1.4.0.tgz", "integrity": "sha512-WsxiWfsEp5GapSFMaFHuV4irfMSqjrEiKduu4kfy9lsAoVXrnj8+8NaayBvJatE7iOi5oSWCb1NVNmzVD+j5nw==", "signatures": [{"sig": "MEYCIQCUreS8/NNPAuVuklg2elNOxWSqbE8Mr3oAXMUNgnFT4AIhAMPtkJt8VU05ArN/vz0pihatIggccLFgUN+Vvr3zhqtO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "bbb0b7f12720817b1e8c789255b8ccc3e9d467f0", "gitHead": "6d03dc79547e110e4e5ba54b2140ba30915c7fce", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"nan": "^2.5.1", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-1.4.0.tgz_1485124687441_0.7729592528194189", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.0": {"name": "better-sqlite3", "version": "2.0.0", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@2.0.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "fe5fea7d80a9ee7000aff5859b830d185b8406dd", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-2.0.0.tgz", "integrity": "sha512-x0MWh39PRtX5mH5H9vVi1ALzuhPUzBMuqUj2k2PZDpqFJug0r+bQdJw8mtX/Yc4n09iljIR7TD/zcrb3CTPSwg==", "signatures": [{"sig": "MEYCIQDCBHEptoU/rYyjNt/SgOCFcfXnUF2HEG9hhmOsDXkEngIhALXsDUe0To36KyfIx5Yxp6/y4c4pClBhntm/MttzdqEy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "fe5fea7d80a9ee7000aff5859b830d185b8406dd", "gitHead": "5fbd19eb21ff33f7b58f6ddb1d73f168c7dc179f", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "7.6.0", "dependencies": {"nan": "^2.5.1", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-2.0.0.tgz_1488382051517_0.46311302203685045", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.1": {"name": "better-sqlite3", "version": "2.0.1", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@2.0.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "6341612df3562be39e749b9dd9cb00caee75af77", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-2.0.1.tgz", "integrity": "sha512-DJ<PERSON><PERSON>lmqRhWtYRyw3Lzx3HmxwzHEsjKHh/dlq9tbO3AKeaTYPkHkP2PJC9uCUSjNUqKPCZw+0GT6oH7qnodYw==", "signatures": [{"sig": "MEYCIQC58q8Cq3r0IirD1pJIFx2r/4D2mj0SljPjZzEj+yPbEQIhAIjfvU2PxAB8g79YWyaeDLx3ZOdNdKFYyhamb9pa3Pwy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "6341612df3562be39e749b9dd9cb00caee75af77", "gitHead": "561b96d355ebc9f56a8bbfee041866a736268801", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "7.7.4", "dependencies": {"nan": "^2.5.1", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-2.0.1.tgz_1491265982612_0.2096420272719115", "host": "packages-12-west.internal.npmjs.com"}}, "2.1.0": {"name": "better-sqlite3", "version": "2.1.0", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@2.1.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "92433b27741438ffbb905091e3c776f70f203395", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-2.1.0.tgz", "integrity": "sha512-IGBkKcDefm5nIlpBLwozplDQQpFlM2OXmYazdDJxoMucIPMWUPzsoxyblSrifPkh/expFj5d4i3o0Xzch/UBZA==", "signatures": [{"sig": "MEUCIAKIQJFdzbwRlLmJ0w4o2wQmEb+8MkJ2CwBx1qsDsWgwAiEAjJ+xCdCskHhAtAMvZnO4YzVWCRvvIbw5NCveXfVDg68=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "92433b27741438ffbb905091e3c776f70f203395", "gitHead": "f33d201c9a54ce2497bfe5521c6ffb6bf6de0fab", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"nan": "^2.5.1", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite3": "^3.1.4", "fs-extra": "^0.30.0", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-2.1.0.tgz_1491620923630_0.9168357443995774", "host": "packages-18-east.internal.npmjs.com"}}, "2.2.0": {"name": "better-sqlite3", "version": "2.2.0", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@2.2.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "003a0953e425e6a02de22ab0a3b51afe41db0358", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-2.2.0.tgz", "integrity": "sha512-7PcVARC+YnEPO7KC5jgxAXEWDeg/yNcn7JzyjFmrhQqSP5tIq0P5C5OP8jXkaPb2cvac/Gg62XIJ1EKLhMfx+A==", "signatures": [{"sig": "MEQCIF+meedCm/6peS+oWjD1akGNE98Azg2u6s0XtZz2qpAXAiASQSuw0cFUOwxVJ/IsWl54cpIiAJZcYB5wMC7SP6XlMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "003a0953e425e6a02de22ab0a3b51afe41db0358", "gitHead": "01d58ab92b0b3b0c499c71c5f0d09d2fd02d2b43", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"nan": "^2.5.1", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite": "^2.5.0", "fs-extra": "^0.30.0", "benchmark": "^2.1.4", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-2.2.0.tgz_1492023713178_0.23257335019297898", "host": "packages-18-east.internal.npmjs.com"}}, "2.3.0": {"name": "better-sqlite3", "version": "2.3.0", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@2.3.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "9684ef71486e1e1d9d772f8f5b329aa808b273ad", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-2.3.0.tgz", "integrity": "sha512-1wrZmhgKt1yvqaIsxRDvbABO1Te93brg7KAov4NVHeMXWn6RraDeGs6/LRnHSdVERCd4uQtuwL6v9ip+3kNL0Q==", "signatures": [{"sig": "MEQCIEmAG2V52z5bgajg3A4JhrKkao1aus4IIkg5XMkSX9rCAiBbyVtQGZ9gQYhaZt1r2zPnc0GDtJYoXKnQo1yhWe4N3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "9684ef71486e1e1d9d772f8f5b329aa808b273ad", "gitHead": "4b46676b74c2544360a9ca7014e9b0acb854a0de", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"nan": "^2.5.1", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite": "^2.5.0", "fs-extra": "^0.30.0", "benchmark": "^2.1.4", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-2.3.0.tgz_1492063287626_0.7581701099406928", "host": "packages-18-east.internal.npmjs.com"}}, "2.3.1": {"name": "better-sqlite3", "version": "2.3.1", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@2.3.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "69ebc1631379130d5c69ae8049f3091a3b2aa678", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-2.3.1.tgz", "integrity": "sha512-YfMSzmN8V1EGVDVeWhAMMWEIeXaBGs38pdeKsgLXd80cRXSYPURaPeWZ99to+7MKlr0CsWAUUeLTbQzg68gvLQ==", "signatures": [{"sig": "MEUCIG7Iy9KgyhOV9iBvo12+GAG5KZu+DyCWWyUh55KunrW3AiEAyEeixSdghuXan8Oq1R93SVimdiC3+0+/Wp7WlAIzxgo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "69ebc1631379130d5c69ae8049f3091a3b2aa678", "gitHead": "216968f5315101b9e70ca5b33fb23623e075d891", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"nan": "^2.5.1", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite": "^2.5.0", "fs-extra": "^0.30.0", "benchmark": "^2.1.4", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-2.3.1.tgz_1492203377249_0.18095687474124134", "host": "packages-18-east.internal.npmjs.com"}}, "2.3.2": {"name": "better-sqlite3", "version": "2.3.2", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@2.3.2", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "d3faae0f0744eab3a90dca4707e88e88718040d1", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-2.3.2.tgz", "integrity": "sha512-FfxPhAAsiUZMq10DYZOGl+ZiQQ2H5X+mii8PP4qzCEud5j24PTTPIRd2kG+VgrxYZtgej3+pLZxYxkkTLh/Xfg==", "signatures": [{"sig": "MEUCICPy+nhySVFBUEsnMr1CPWbXhmwVINjnxkJ1uiyEjsqWAiEAne8jOCxp3lsimDf17OKctzWoeEO5QFUFJx1SHJPDuIw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "d3faae0f0744eab3a90dca4707e88e88718040d1", "gitHead": "029cfd3eb6d27a9de98aaa7225914a9cb1770f4e", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"nan": "^2.5.1", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite": "^2.5.0", "fs-extra": "^0.30.0", "benchmark": "^2.1.4", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-2.3.2.tgz_1492204803597_0.209458694094792", "host": "packages-18-east.internal.npmjs.com"}}, "2.3.3": {"name": "better-sqlite3", "version": "2.3.3", "keywords": ["sql", "sqlite", "sqlite3", "int64", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@2.3.3", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "1c7d8ae23cd1b1ff5da794a6d353d4f58469c9a9", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-2.3.3.tgz", "integrity": "sha512-gZgtYGHy9NNRRrl8OJfCzjav5C8KCqmFvn8LCfr8CeBiRxhBGBLS9vjPtPz5GW3Ox1f1kmGGQ0gfZHtnm66J2A==", "signatures": [{"sig": "MEUCICShX+AQYNzSNdOzW6YUTM84WyZxQ+rO0usygsKoS8J3AiEAo4QIut4tN2cxkX+p/7QY3UZkhTpVyHJpl+GcBbNyTfc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "1c7d8ae23cd1b1ff5da794a6d353d4f58469c9a9", "gitHead": "5ae145e91ecec123f366eaac9936a7f557d0ec04", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "7.9.0", "dependencies": {"nan": "^2.5.1", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite": "^2.5.0", "fs-extra": "^0.30.0", "benchmark": "^2.1.4", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-2.3.3.tgz_1493527693222_0.37318005692213774", "host": "packages-18-east.internal.npmjs.com"}}, "3.0.0": {"name": "better-sqlite3", "version": "3.0.0", "keywords": ["sql", "sqlite", "sqlite3", "custom", "aggregate", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@3.0.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "6f2b3554000186308b35d1093c2b0d941e79e283", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-3.0.0.tgz", "integrity": "sha512-LXw3t1lemiiXZ7/Nosf0YjwTmN7HKJ2dHDrudzwUHbtg4+oyM55QwQ8seKIWGs9Yy0V4fuQbOMuykSRjf3qo5A==", "signatures": [{"sig": "MEYCIQDEORqwQwozdiMbjOOpyGMYpkWGgpk2v4xWk6comvRTKQIhAI1Fx+N2VAKg0Kq7Y54GoAs0YW/0Vx+uLAiEWt06K5Sj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "6f2b3554000186308b35d1093c2b0d941e79e283", "gitHead": "0f158a6c36626ca7fa6f7babbf98b94cfcdefb1e", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node tools/install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark", "install-debug": "CI=true node tools/install"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.10.2", "dependencies": {"integer": "^1.0.1", "lzz-gyp": "^0.3.3", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite": "^2.5.0", "fs-extra": "^0.30.0", "benchmark": "^2.1.4", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-3.0.0.tgz_1496725028982_0.2242020156700164", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "better-sqlite3", "version": "3.0.3", "keywords": ["sql", "sqlite", "sqlite3", "custom", "aggregate", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@3.0.3", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "9dab3b5b2963aa0dc93d153343e844aaae61baae", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-3.0.3.tgz", "integrity": "sha512-0tR9Lv4nQUJld8J2S3xajdpad+t1JE0sNZukXWzmZLBPJ6b0gvWtHdUxdcil3M119IW2ULClbMPI0lKDgUVCug==", "signatures": [{"sig": "MEQCIHF1yp0m6JjngDnQa+jhXcMtiWLm1IrI5FB45rpLirDxAiB4Dsi47qo0pbWGjkpQ3p5yJmcaMxXWSUi4HCv2vIP8ig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "9dab3b5b2963aa0dc93d153343e844aaae61baae", "gitHead": "7e5a46fd8a88ccac7a0d02ce4c4238d1f4c2fe79", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node tools/install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark", "install-debug": "CI=true node tools/install"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"integer": "^1.0.1", "lzz-gyp": "^0.3.3", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite": "^2.5.0", "fs-extra": "^0.30.0", "benchmark": "^2.1.4", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-3.0.3.tgz_1496940126013_0.18389102164655924", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "better-sqlite3", "version": "3.1.0", "keywords": ["sql", "sqlite", "sqlite3", "custom", "aggregate", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@3.1.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "58d2ec3a43ca1101a48fce780dbfd382c8143ebc", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-3.1.0.tgz", "integrity": "sha512-cLRCtl4iQw8KoefxFFDPOtvFfqybtudarCuuVqjWMGUb8cTFF1GbWWY6lDeL/KZPaGlaLdpp6JTnReyaAi5aNQ==", "signatures": [{"sig": "MEQCID3Bpplf1IReOQVXIgk6Pt78D0XWTrhC9RKUxhGQplPoAiAkGxANsxHY0JZRHkSid9z06y5hHjGp5Vysaato8A2n1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "58d2ec3a43ca1101a48fce780dbfd382c8143ebc", "gitHead": "1ed2e21da168e9871f209cd5e07fa5642657ec7c", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node tools/install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark", "install-debug": "CI=true node tools/install"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.10.3", "dependencies": {"integer": "^1.0.1", "lzz-gyp": "^0.3.3", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite": "^2.5.0", "fs-extra": "^0.30.0", "benchmark": "^2.1.4", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-3.1.0.tgz_1497239769029_0.6990713693667203", "host": "s3://npm-registry-packages"}}, "3.1.1": {"name": "better-sqlite3", "version": "3.1.1", "keywords": ["sql", "sqlite", "sqlite3", "custom", "aggregate", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@3.1.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "59307d285aef13d0d904f58e3089394c92969bbe", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-3.1.1.tgz", "integrity": "sha512-e/cxQJTNjfcRFiOhB1dmRNZ/yb8cYt7NWdU4P+iIY+QoUy3JMfKt0xDk3RDTCXXyASJREQWOp8EDVfE/QVunGQ==", "signatures": [{"sig": "MEUCIBquwRk7bi47YnttvJKEZrnLowkq/o+61uQi1MRlTAhxAiEA9Nvqt7MHIEuWoVfKNKyy4V+kPq9uBlKAEMkZa7Iygi0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "59307d285aef13d0d904f58e3089394c92969bbe", "gitHead": "28bc2bf9400251c7f7d3446571e0fc7afb730300", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node tools/install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark", "install-debug": "CI=true node tools/install"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"integer": "^1.0.1", "lzz-gyp": "^0.3.5", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite": "^2.5.0", "fs-extra": "^0.30.0", "benchmark": "^2.1.4", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-3.1.1.tgz_1498244203784_0.45343715813942254", "host": "s3://npm-registry-packages"}}, "3.1.2": {"name": "better-sqlite3", "version": "3.1.2", "keywords": ["sql", "sqlite", "sqlite3", "custom", "aggregate", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@3.1.2", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "cc8d92cf9ec284f10e604538a775c13213cd6a12", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-3.1.2.tgz", "integrity": "sha512-4U+RObAZOcGBDg2rd7kFFFi6C58KU5yBoJa0Di6alVG+ah6mAP1hdHQYnqlR9viGOL+1PoO4eiG/tmxPVkjIog==", "signatures": [{"sig": "MEUCIQDSjqBILWgUxfDAfLNyJUn+LD1st2XPnbghXLCmVk7KYgIgRXwkwGijLGbaRgujrE1PVgkETTzJm4C9qtThEJ9+GN4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "cc8d92cf9ec284f10e604538a775c13213cd6a12", "gitHead": "4c9f30711daf778788796f6711b177b8d4891f8d", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node tools/install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark", "install-debug": "CI=true node tools/install"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "7.9.0", "dependencies": {"integer": "^1.0.1", "lzz-gyp": "^0.3.5", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite": "^2.5.0", "fs-extra": "^0.30.0", "benchmark": "^2.1.4", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-3.1.2.tgz_1500478020045_0.36839743005111814", "host": "s3://npm-registry-packages"}}, "3.1.3": {"name": "better-sqlite3", "version": "3.1.3", "keywords": ["sql", "sqlite", "sqlite3", "custom", "aggregate", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@3.1.3", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "0831c5e582114d90b904d9dd67dccea6ff914ade", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-3.1.3.tgz", "integrity": "sha512-DxeZsUAOQnXIFcy9c2QmVug/DPolaE/PFtZFsUD+xc+eG/M34KMOvpzz+jKqfavZU1m86/VU3uFTe0XCH281FA==", "signatures": [{"sig": "MEYCIQCtzWXad9Ppth4xBFRn2cMVQiCPzsF3p0Jwrsp+VlSJFQIhAL3BfAhhGMDS+swJehC/42K/IrwoUdvbjzhPU4jub83z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "gitHead": "cb2b2672a6fb2c4cc0c125cd8bc74f1697886a7e", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node tools/install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark", "install-debug": "CI=true node tools/install"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"integer": "^1.0.1", "lzz-gyp": "^0.3.5", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite": "^2.5.0", "fs-extra": "^0.30.0", "benchmark": "^2.1.4", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-3.1.3.tgz_1503593848508_0.7950414780061692", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "better-sqlite3", "version": "3.2.0", "keywords": ["sql", "sqlite", "sqlite3", "custom", "aggregate", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@3.2.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "3cf99e904d563635decb08b2ce65a7bde2ef1368", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-3.2.0.tgz", "integrity": "sha512-h+mjHviBPloO32ZkxWdU6PUe41erUmymrLZVNduozcSRhNRc7zeMEv0zOmrFQXTe7D1O/PhN5L46L35Dlz/83w==", "signatures": [{"sig": "MEUCIQCjAhkDyZdA45hTgdPxX6V4K4VukJlFg1t76M9EBcS1eAIgItIlmBYgL64R51iYSgHOCuLSs6h7YucOf/x65/dK5XE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "gitHead": "af5760653af15e19664d0459a25bd447739d1844", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node deps/install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark", "install-debug": "CI=true node deps/install"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "8.3.0", "dependencies": {"integer": "^1.0.1", "lzz-gyp": "^0.3.5", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite": "^2.5.0", "fs-extra": "^0.30.0", "benchmark": "^2.1.4", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-3.2.0.tgz_1503613420888_0.042867450043559074", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "better-sqlite3", "version": "3.3.0", "keywords": ["sql", "sqlite", "sqlite3", "custom", "aggregate", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@3.3.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "9f589923a2bb95b734c230c52a1f049d5d82c3da", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-3.3.0.tgz", "integrity": "sha512-eiPBYl6Hzrvg4H3GM6S7Kv1my31CKbnTC3tJwXnn+t0WshspsPxllZ055DhQ2WtonTaVDOew6KKfY4qtBFsK6g==", "signatures": [{"sig": "MEQCIHCYfQV6O7BkkabITESH2M55Ue0RGOkeHju8Lvu1dXQQAiA3pUDB8VL4qIg6XXzf6+RKt6gUaLqxFYqtEseMicafEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "gitHead": "20836a7bcc75a64dffe2e06651af9df781364a94", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node deps/install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark", "install-debug": "CI=true node deps/install"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "8.3.0", "dependencies": {"integer": "^1.0.1", "lzz-gyp": "^0.3.5", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite": "^2.5.0", "fs-extra": "^0.30.0", "benchmark": "^2.1.4", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-3.3.0.tgz_1503889239866_0.9495576827321202", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "better-sqlite3", "version": "4.0.0", "keywords": ["sql", "sqlite", "sqlite3", "custom", "aggregate", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@4.0.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "90d077c94c14e3a483f8086c48ce79ea57955417", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-4.0.0.tgz", "integrity": "sha512-OcgQnwPEMTAi2HzcLU7qUft7uycyNG81/N97c3G5YlxYKb0KGwws0lFG7CtSWJc+h9pwiPQpsIob0jZqrKfyew==", "signatures": [{"sig": "MEUCIFCJYTAxhVRAxvaQ6UrjDrVvR2gvtLblk4tCaUmrOvBYAiEAp5cjjBUSUq3PdMe36BnP+Lbg+Pu8BG/iFPwbKTQH7s0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "90d077c94c14e3a483f8086c48ce79ea57955417", "gitHead": "2d2a53829354031ca403eab3d6042e9d74ba468f", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node deps/install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark", "install-debug": "CI=true node deps/install"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.10.3", "dependencies": {"integer": "^1.0.1", "lzz-gyp": "^0.3.5", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite": "^2.5.0", "fs-extra": "^0.30.0", "benchmark": "^2.1.4", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-4.0.0.tgz_1504009206122_0.19696887768805027", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "better-sqlite3", "version": "4.0.1", "keywords": ["sql", "sqlite", "sqlite3", "custom", "aggregate", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@4.0.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "849435d7a1a6657989dfbe501249501bcc379a68", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-4.0.1.tgz", "integrity": "sha512-Yt3fJM9uqHgP9uqA5EV/yTq6WXqSvnDa01E0Mp6ZqS+a+vK1nruI0nxGO2aB80tA2jsAQGnRWsmojbzOSfYdKQ==", "signatures": [{"sig": "MEUCIQCGaTvZFXiAa3nYHykBwO7EGkk196OuUZtyqTtv+p+JTgIgfyzSNBy5p+cJ5GL6q75fSMfvRhnzqoQucFrCw1cWfgc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "gitHead": "e10d7553fb79f6e346929f21b884bbe0dbca9464", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node deps/install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark", "install-debug": "CI=true node deps/install"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"integer": "^1.0.1", "lzz-gyp": "^0.3.5", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite": "^2.5.0", "fs-extra": "^0.30.0", "benchmark": "^2.1.4", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-4.0.1.tgz_1504010846387_0.10762364719994366", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "better-sqlite3", "version": "4.0.2", "keywords": ["sql", "sqlite", "sqlite3", "custom", "aggregate", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@4.0.2", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "3a5542252e199ffcc3a58116255a39882fe06eeb", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-4.0.2.tgz", "integrity": "sha512-WoUXe6CFJ1ncxTPvWgGmg0TBGVaj/kMPcUfYJtf3KA4WrrtL4OVH+1RDhe5miKly3VO+BJ35t+a7xNGDVU7Qww==", "signatures": [{"sig": "MEQCIFLlKXwmYBW0YZAPCkgspmXmzbFktALVcgEH6ZEMTG6bAiBqb9yR4bNMLWFo/P331JXNZA8Eu6excxIXUD+yQDfd9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "gitHead": "46930f854fc45fa5d5e33a2facdcc548e40b5294", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node deps/install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark", "install-debug": "CI=true node deps/install"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"integer": "^1.0.1", "lzz-gyp": "^0.3.5", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite": "^2.5.0", "fs-extra": "^0.30.0", "benchmark": "^2.1.4", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-4.0.2.tgz_1504218427089_0.43222084105946124", "host": "s3://npm-registry-packages"}}, "4.0.3": {"name": "better-sqlite3", "version": "4.0.3", "keywords": ["sql", "sqlite", "sqlite3", "custom", "aggregate", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@4.0.3", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "e1968227e9181f57cfde40087f399a2b019c8bf0", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-4.0.3.tgz", "integrity": "sha512-zfKbjkAC1HVX5NMyBFpIPHu2jbU1YL3cak2z/nybnMTBskLz4O2GZgLrCwgpzXSPCy1YFqFHhh6aQetuu+1d1w==", "signatures": [{"sig": "MEQCIExchfxD035zGKRj1VLc8vTiAKb5C6kdzoSmUWaRYO3NAiBrZ9S58RzQ5uI1V2c9NPdEnVbsSj2nXdxn2htA3EO8dQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "_shasum": "e1968227e9181f57cfde40087f399a2b019c8bf0", "gitHead": "c95ab510bc06a84a154b102fdc85f67cacd037eb", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node deps/install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark", "install-debug": "CI=true node deps/install"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"integer": "^1.0.1", "lzz-gyp": "^0.4.2", "bindings": "^1.2.1", "to-descriptor": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "mocha": "^3.0.2", "sqlite": "^2.5.0", "fs-extra": "^0.30.0", "benchmark": "^2.1.4", "cli-color": "^1.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-4.0.3.tgz_1506364760748_0.9293614109046757", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "better-sqlite3", "version": "4.1.0", "keywords": ["sql", "sqlite", "sqlite3", "custom", "aggregate", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@4.1.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "1dbb0b13b280ce1eebebad431502deb08b78941e", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-4.1.0.tgz", "integrity": "sha512-IYcH1F14DfdIzDzSg7TF+0b+GpYmf6UFgV0ZWsaaczy548/S5LFMkYeKHl8EAOQQb1mSHt0lkJA0BGHMRwGOcg==", "signatures": [{"sig": "MEUCIQDlLKY0T2f7l5JpTRufTUgto878MpSvJcTMRxXPdKYT/wIgYMcrWFCN+4BrFwMVz3/EWODYz2QocdEf0slU/obtCqA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "gitHead": "fc1910aab4ecd2c625c42330d59c3d815ece5602", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node deps/install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark", "install-debug": "CI=true node deps/install"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"integer": "^1.0.3", "lzz-gyp": "^0.4.2", "bindings": "^1.3.0"}, "devDependencies": {"chai": "^4.1.2", "mocha": "^4.1.0", "sqlite": "^2.9.0", "fs-extra": "^5.0.0", "benchmark": "^2.1.4", "cli-color": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3-4.1.0.tgz_1516448173203_0.33977346145547926", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "better-sqlite3", "version": "4.1.1", "keywords": ["sql", "sqlite", "sqlite3", "custom", "aggregate", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@4.1.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "090a82bcebbd77f003dcf1b07a6ce1b65ec5c2fe", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-4.1.1.tgz", "fileCount": 70, "integrity": "sha512-D2EOD+fMdz8GsGQiVz82MpeGedH6AvFW8NZaUCKFWAPZnGsDDQIP4TIRst2TYoJonhCPHBSpa7PlojeG0gSWrQ==", "signatures": [{"sig": "MEYCIQCB3ZLlEdCE6+veeVcpAvwc2D56LTtRAmwPqDV0fjigqgIhAKviX2YLXF56NQzAgagsbcylg4y6gaaNU7PFw2ONBhZZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2779108, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBfgHCRA9TVsSAnZWagAAcFoP/RdnkwFyWJGXNjyU3D9j\nYvnyqaGTm7velMFpr2d30d7ptia7cYdUTu86aEhZcvBegzQFCfxArNFIfD4S\nxU8ADBmJ0Wb9iIpmF92s7PvunlqHC+0vzY6d+p/DBBkUiO7BEnSr8tfU+UDF\nxGE7GPRiysWNZnI9aPhUJ9QsJjb4FFcc/m7CGZHjhD+mtcjM3QCXe4YfrcGb\nS0ueJ9Nyzc1Yur/16963IHp/xmDRD0n1bWifA3nCoAdqfeVQmAbNDthQNYUB\nnIRCFDCnq4Rv/W1v+bGpcl81hSURez93dxXyDNxLpLMeHbfMbRfaEQ+66FDq\ny25punjN6L0XG0WVHMjnxlzkeL5kzDi3pTV/gny1rD4znQa7fcJqRIMV9bUx\nvvzFUAkbzMgQM2yazO2ThkAZkKC9RiuaJZ2KnOL4BNurFdjZ4ZWFbpOOA6cW\n/ptaQRsulJg6YBLSY2/E0H3AZcknSGzHXIYg2cWSt/xtp4hNfUC3gy1KMzs5\ngWmHq1ULbl8C06iPfOqI68Czm1T3AQ5G+kyeKCqzmLfMuJp6YHVgK4297MyL\n2YMyTHf38sNVE0yq5RLvMqf1CuGEIhnrziVwd6DvgHoZ1IcOXJbQV+CKMP56\nQvR4mIpzmSzE9/KPHFUBc6js30Dhm9QhfwvOSJkE6MhVt6mx5gIAm3B7bpd5\narBG\r\n=iU2g\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "4b992e0ff3f743d0e9b3b6687d1b4ca10ecd3755", "gypfile": true, "scripts": {"test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node deps/install", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "posttest": "rm -r ./temp/", "benchmark": "node benchmark", "install-debug": "CI=true node deps/install"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"integer": "^1.0.3", "lzz-gyp": "^0.4.3", "bindings": "^1.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.2", "mocha": "^4.1.0", "sqlite": "^2.9.0", "fs-extra": "^5.0.0", "benchmark": "^2.1.4", "cli-color": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_4.1.1_1527117830499_0.5092368281290138", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "better-sqlite3", "version": "4.1.2", "keywords": ["sql", "sqlite", "sqlite3", "custom", "aggregate", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@4.1.2", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "7b1ee734beea3dd68abee35ef7fd3c37640e096d", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-4.1.2.tgz", "fileCount": 71, "integrity": "sha512-zpWR+I9ZSay6aoSEKfKRwgoQHuiZpbLuv6oY24NP8jATKXwZp9p1CrRxyu7NoWXzZjqPQWq0Wwo7COFZLeFSdg==", "signatures": [{"sig": "MEQCIDqIVhQzqE97ilhuzTjPjznprpuwbPS1oD7gmSdZAhJ8AiBbkJIdVzxjIpwjquPWxnPJO3l5sOz/1Wzj1vmVl7nmLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3061280, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbIKZhCRA9TVsSAnZWagAA2UMP/jT+T4hmqJ2WC+mr9E1E\noeVl2EcUfqxV0H7LkSsfmMys9S6ApXLk1z1F5mHaU/6GUfGUPNVSeL+FNWyV\nxayJgXtwTIJJLZRWd8NMWekgtskitxrF5NlpRk8yY7a9SyI2NQ5MwDK3I6ZB\ny7Ahg5ywYBn0aQa+rAfYsA4IQRTgZC/61jKpC5GVr0/ESaFhJjib9ieryFf9\nZhse5MkxWdfsfMfNwflBjrt9qUOhewq9OI0UNVgt1F9Pef28ySMoRK7EEqv8\n4Ht3lejCmkT3fGAl/QJxcxmNMDavUdIOT4+D/wBukVBlxFMRXw2ayyqAMJdi\nQimb+yd76n5bEwmawPL2bBzVnG24JiDdxvrpTltXIms8lRoDEwe50ME68zzX\n8rEDAiKJG99DsV3swW66GSu1AzH33x4BfaQZIpyZUKlEjPw6BxZ998FSltES\nzsm/1oSLg3XItyYEF5RnMD0sF1fhA1VoGxMSMV/AxQ3+yjbC8WF9flqb7zjS\nRLFNm7VGwMke93Q7bpa0IGf7JXU2BaDrTsvPTaWw2UGbfgAvaBQkkqauy0O9\nv/RXxN7+oODCEscguA1jnZwgDTGZCpI+zv8YFrWp2i8Y+3tBEl18cohTF7cF\nGdcmi1qwLJ3TmGA4NMUlvuPaRIpSk4DkZGMauqjpuMBKFLlkp0u284ELWAzY\nrONu\r\n=GkPT\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "e9693574ab9f3d084ab53dd200afd0077112818b", "gypfile": true, "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node-gyp rebuild", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "rebuild": "npm run lzz && node-gyp rebuild", "posttest": "rm -r ./temp/", "benchmark": "node benchmark", "install-debug": "node-gyp rebuild --debug", "rebuild-debug": "npm run lzz && node-gyp rebuild --debug", "prepublishOnly": "npm run lzz"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"integer": "^1.0.5", "bindings": "^1.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.2", "mocha": "^4.1.0", "sqlite": "^2.9.0", "fs-extra": "^5.0.0", "benchmark": "^2.1.4", "cli-color": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_4.1.2_1528866399673_0.8543656927777632", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "better-sqlite3", "version": "4.1.3", "keywords": ["sql", "sqlite", "sqlite3", "custom", "aggregate", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@4.1.3", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "54d60c45a3947ae8293a0c339dc72d863319f309", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-4.1.3.tgz", "fileCount": 71, "integrity": "sha512-6eDFMjg4BGCrGHh6ZJLkmjsGR4OaQ4S5ZRGeNutVg8vkazI9Rcr+0/IzV9Ih0hT/0Ft17SEp6o67mFtgEWWl2g==", "signatures": [{"sig": "MEUCIHdBcuIqrHyCvt7LPk9J27Fjfk6mwOCbXmk6fFApSwNJAiEAtumuXH+3zT08u1ekPNVoAcFthpAgLcm5DHIXI9fnzcc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3061436, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbIPfvCRA9TVsSAnZWagAAb/kP/1RKNWcIcxsGu252grDd\nGRL+EKJg2vl6eZ8ijxAn/JeyWkfq70/SrLRCc34GV5wZ30E1rh7OFXQFqfPc\nPcfqWH6MZENpSZePU3q5QhykSRKThMguw7qincl9sn6VItDViYVCszTzBCHH\n9GqjxgFTbGHIH7/4ENnJD89ZZjxpMk6Z1c4h1pM47OoHqEgi8aArrW82EicB\n2w4ne/Md1E12rAZXh4dUWO6ST2VYLwt9j67zVqVnlamREQjv9/3ALRPLAtVv\nX0mFLxzN8UiFnufxKodJKc9XlKUDZ4oJGIW6BKcUxrwV6DXvEsUeLIofzvJ9\nTgJ0Ot5QcE1BFLCXljW9TNwJcY1Wpjblqp0I8XAEg0PALVT8hGhrxKGnDITG\nmI3qa/ZcBCSr0HL9bkTjrrDZRhwPsFswCNzIctuJWDMOPV3SjpFGqaVBV6tc\nNpN2VfN4dSBNP9YvdI/0Gk+gPVRqAXXPLn0YWW93YDX1HSMat1KvjNWkpge+\nA28EDJmNceH0PXLwcOmaqnrfiPVb5uhYp1uRsMGKUOI5dW6Q+5QwWrxRLYZN\nv6r3me+02rnmQmw11vjB9Huz51o5fEGzMJXaFxGAjYQUeIyMPTX1RkXisC9Y\nLUf4DBRbH+RPPoDeiSNysmDvG7kznnBdkWoXjsiKw2XAxgvam8qvszys76e4\n82dC\r\n=igGh\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "420b3d525d4a5007946158f8892a662808f114b5", "gypfile": true, "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node-gyp rebuild", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "rebuild": "npm run lzz && node-gyp rebuild", "posttest": "rm -r ./temp/", "benchmark": "node benchmark", "install-debug": "node-gyp rebuild --debug", "rebuild-debug": "npm run lzz && node-gyp rebuild --debug", "prepublishOnly": "npm run lzz"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"integer": "^1.0.5", "bindings": "^1.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.2", "mocha": "^4.1.0", "sqlite": "^2.9.0", "fs-extra": "^5.0.0", "benchmark": "^2.1.4", "cli-color": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_4.1.3_1528887278451_0.716596290487554", "host": "s3://npm-registry-packages"}}, "4.1.4": {"name": "better-sqlite3", "version": "4.1.4", "keywords": ["sql", "sqlite", "sqlite3", "custom", "aggregate", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@4.1.4", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "9fe1dcf7b699087b98b1997cbb00261e265897e2", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-4.1.4.tgz", "fileCount": 71, "integrity": "sha512-Y11HN9PQ9YUeKFMrmiHyOLAKElk2ATJzBZJvuzNwTMxoS7vUEEyLnUCtcBFqViLwbomr0RQwp2MBy/ogxF50PA==", "signatures": [{"sig": "MEUCIQDhH1e/KiX0X89b9VEhXRe1RvxFZe9A3pOtV3DP6H0NVgIgTKMUcBHsXwhQe2gdlGtcAqw3DBlJ5l4cTiu+0dOcbQo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3061466, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbIPl5CRA9TVsSAnZWagAAs0cP/1Zl86RC21i4UXtYkOxq\nxwHx5eRRTLBsTV7dX7gAo3H6UATwCMlWdJD/03Z8BfW0Zft4Tfxs6hNSqJd/\ndhpikV53YqX47vSXRHo1K/rX2AzlpbCipsW/BjyGVFri3YC+JeV/j3HB2IMS\nA1/4HM7r5/8kbeT3DoBOmmX4bIhUO+a+aQmGI/Ofju0316rrrX/hSoe44ca2\nv+2z0F6QcocyroPgV0+WpKT8dFCITKac6Ce91gdOEY9ordeWKhNIYlibwYp1\nQGKw+H5t26zma3rhTxzX/v/rLB43duFihIuJOMYsWodclMGu95Mp0B2gFKLt\n0bFHhlKqL7p3kTr4bMn7i0qWqx8CJtnYgkFIzNq7qH6sKeznxNt9EddJiWP1\nLwOBkHKAodZUd4uC1GY8VdZ9/8IgCEmtJfJwUT5avT3UOwgMiWUk2ILtMH4z\nPvOuNvHb2q3H8e42PI5L61mnH3UF4BlTgAi0bERbTJFWhT3MPyWwlEtl7jkt\n7F/hXSpy6cjEnymrHUTSjZYlMcbaug6XELvMjtynJIKNtcFRvdAIrb1PkKkb\nHlQcGehAsn+D6TvEx0QzhSGl3y0x2+enftAkLeNieeRDZztoM7VDA9PPyO9B\nKBL4ZS4CDe4ozoPp964U4rwcPSwx+jggo+djB9uBLn1x4LOXLnLIKGGGsVWr\nj79Y\r\n=pKRA\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "89579fd576e193a3cfefdce5fd8302db181ab7e0", "gypfile": true, "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node-gyp rebuild", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "rebuild": "npm run lzz && node-gyp rebuild", "posttest": "rm -r ./temp/", "benchmark": "node benchmark", "install-debug": "node-gyp rebuild --debug", "rebuild-debug": "npm run lzz && node-gyp rebuild --debug", "prepublishOnly": "npm run lzz"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"integer": "^1.0.5", "bindings": "^1.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.2", "mocha": "^4.1.0", "sqlite": "^2.9.0", "fs-extra": "^5.0.0", "benchmark": "^2.1.4", "cli-color": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_4.1.4_1528887673003_0.9953327610861158", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "better-sqlite3", "version": "5.0.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@5.0.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "71eba29b5b993c2be94e05ed1dd8b5c52f0f053b", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-5.0.0.tgz", "fileCount": 73, "integrity": "sha512-hyW3zEMMsooGENvITcZseRXAZLo3VSvQu6QnQl2eehRAw0ygVZc3TcHK/QLcCRPJaQKVs/Ii0hv7NH0GOOjLJw==", "signatures": [{"sig": "MEUCIQDV2q1Eq1IXt1btZ91JFYdDRbDsmTZYES3VsNc5GvwIJQIgcxIwRPRSYu7s4umzVT4O/c1ivuRV8RP8/NK4cFsMMGs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2510218, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbvME9CRA9TVsSAnZWagAARacP/21WShKeoal5aLDmOJSk\nbsoXNdaSagBiS3FCXrg8FYjfQsJKu9ymkmfi8je/NR0REByAQTaLg6gc85Fl\nCxn1Xqeu6umYtYmYs+Y2JRrUUpKlLso22PyPKKNpwh+mjsaMH8NHbEFhjVoM\nw3i8pwks7uYhGdlbW/g7wChO5NE8L8g2dnJuXgYHW6slWOyiwKxP1xhx0/5w\n5lEW4T0dS4+vmAYzmjcCl58oBr+e2khHcJWT220PDDbpNfQplTk+LivnAp8Y\nUqcRyk8qqvXPYOKBiSGKuowDg5sjFVunCEpx25yZiUoIYbE77kKc/FrxcETz\nn9O5RgzuFkoa3UpDeUovHzUqe4T+PoWbK8b9jqFp6wYAeiLDd8h7Xhv8rB39\nL6mQjevEBpi5SY/OOelWrwM0usVh2KVscnHMWm6Dk0B3CaOvaJiXw/bgYfTn\nU4+O54HftofsBdjg1iluWEOmcrcgghi9/N5l35MVQuyIN1OC30CQqtFme6Wx\n86cp4BshBeQbx23B0qexwU1nwQ82xgzbqE0iAUatYYS6gVsjeJ3VhlrsZRud\nYh4wOuyn5f0IJSbDdskNMjeRWWro5TKQ5zQuk4PkuBYnhNijFf1ZGGEIOp3q\nllsAebIDeg03Mud+8E3usxYDM1TxBf28LDlENVxPWh/iYvTCFMx9Nmppjm9L\nKQya\r\n=I7yU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "04fe76111f2b2fb0da0dbe5fc65c800389e9fff1", "gypfile": true, "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "$(npm bin)/mocha --exit --slow=75 --timeout=2000", "install": "node-gyp rebuild", "rebuild": "npm run lzz && node-gyp rebuild", "download": "sh ./deps/download.sh", "benchmark": "node benchmark", "install-debug": "node-gyp rebuild --debug", "rebuild-debug": "npm run lzz && node-gyp rebuild --debug", "prepublishOnly": "npm run lzz"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {"tar": "^4.4.6", "integer": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "mocha": "^5.2.0", "sqlite": "^2.9.3", "fs-extra": "^5.0.0", "nodemark": "^0.3.0", "cli-color": "^1.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_5.0.0_1539096893031_0.5231518274612441", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "better-sqlite3", "version": "4.2.0", "keywords": ["sql", "sqlite", "sqlite3", "custom", "aggregate", "database", "transactions"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@4.2.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "68b0c52e5d95022d86dbe837b96e1a7938e1652c", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-4.2.0.tgz", "fileCount": 89, "integrity": "sha512-m6vGein6wZWexQ+yaBSHM8MSoSJ9hU2pO+4Ap5THwS0WSro/RKDvwO9a5m1WYvMTh74snnPTVMWeUmiB12UitQ==", "signatures": [{"sig": "MEUCICK4b0D3p40RvlZJ4P7w4EzXvwtdQJxATjsIGqry/g8jAiEA1HpD76Gd43u0vPGwnsu96UBtI5BCHf62DApWtuTIos4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11991659, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbvMbhCRA9TVsSAnZWagAApK0P/i9MRhUTohd5ITSE54Gq\nRJpTJucZ2S1Rqd/xNkhaTytFneFLy9vr1LM1ooWKzXpCNLvPyMMS62ARJsZT\nvnatJYQ1cvzuFsU6C5puWCTIR04zp4Z8ep12uI1QAxzVaSHkmoomB17dp0JQ\nYQ4VHtwO8xMFLH3zL5dcsvjaRpDbofjAG5eBdGxL3cMu6JUoLnm/4iUpPdP+\nlN3VhPTYEvfHeFG9BUVqc9dKyXCvyV71ADFkA8ZMEMPcOyFufLXcLwxoBdKl\nnnQplju7XqWrsWGGLwkJvz5z/6RBmdbgBWNcnZ9dUVImAShY6eEXcp33DW14\ndVdeehrn46NtlsQpNz/NG6P/uL23hVFvy0VrItPZQhbtoIPJdkN4x+/Y9h75\nlZ7+qtOgTl+VIzb9oBfrU5u/OGCykjPxsF1QzKuvvsrmrgrfMRLf0WACCokD\nL3sCnuDuq2FnTHwHa9JoVVH/XS+/+ZnH2E9sK/+Hi0gNE9JvvkLYrOmRF1WX\nLGxQCbdXv7o45VpV34ar20Rj6IFHoGZVspty0818fzAFDb9lp/VwHEYVgAan\n2+eDdTcOgdpG5vwIpsLwiJh3PUsyiDW79O6zVQb5vjzgn4C1hVRcufdFg0Ky\ntAKYeYH2J8+oo5HSfzjItpeeQdL3A0sWrHGtHkiZaVzjDNSPawfLm9KNNqRV\nsdrs\r\n=k7xC\r\n-----END PGP SIGNATURE-----\r\n"}, "gitHead": "f2f0aa919c9c2369606411e1b69e95a5ddead657", "gypfile": true, "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "$(npm bin)/mocha --bail --timeout 5000 --slow 5000", "install": "node-gyp rebuild", "pretest": "rm -r ./temp/ || true && mkdir ./temp/", "rebuild": "npm run lzz && node-gyp rebuild", "posttest": "rm -r ./temp/", "benchmark": "node benchmark", "install-debug": "node-gyp rebuild --debug", "rebuild-debug": "npm run lzz && node-gyp rebuild --debug", "prepublishOnly": "npm run lzz"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {"integer": "^1.0.5", "bindings": "^1.3.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.1.2", "mocha": "^4.1.0", "sqlite": "^2.9.0", "fs-extra": "^5.0.0", "benchmark": "^2.1.4", "cli-color": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_4.2.0_1539098336180_0.008533776610723187", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "better-sqlite3", "version": "5.0.1", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@5.0.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "5addb5dcf18c9374c570a1eab7694e67fc9437a3", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-5.0.1.tgz", "fileCount": 73, "integrity": "sha512-dyZk+gDYNPw14maYX5LG/2SCUTiB7jCvETd+bBYqhFyji3oG+UQFN452sUWSjCHCmfg1JtMbLT7WmqB8GLq8Gw==", "signatures": [{"sig": "MEUCIQDOLokaCnN0eKHongMLOVDjGiT7xNLuwD2uShT0GYA+JQIgHegfrRn4ApfeJ/4xELIXDLPX0pqduqvWCMsTv9G1gjg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2510257, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbvRhMCRA9TVsSAnZWagAADc0QAIm7XN9p2Gjt8Gki1Y/U\njISXdXQmuxBvcIOTV7G5ryHeNARJ0CLtL6RwnKToPnO1i7jhuwjEllTa8JnK\nSdX6mXMPDYooP3uysTsFnu+etq7UqWU3UNtvDqbOip1qkgRIgEoTZYgUUjSE\noBgxkkgW/Ds8s9BJU+3FHu60cc6RrPYXxosGYfbGr8tf3DUQ2Gb7qEJQutXl\n1JKB8ML0yGHSt9hwe91qDGH+cHSuDofRsTPXt2EOucHtkDTCipdMWHkPo8xU\njRq8Sk3Kw79gw5k8RMF5CqZqnIs/2evrE338zbSYdIbopNxGCFvMc5gBgvYA\nscIz4a2Vsq8RbR9RzmozFzo4QgK39/6e7ZHoVZS+Dv+J2zByn6LXJ0wS5KFm\nFhNyQdoZcM88AtLWIyjCVjHpFRwA4TcHbBNedj8XteABk7/Qd23LKpns1EzK\n0iaDrN1JiQeryQteCleBlVCFFzdZnuKbEczWlh5CXrUdKhjyxEZuwWutKkcu\n6aee/6+QoSZItqJtnkLdmfnA/10z9nWl1L9hky+p/nOccaRdRqmtDluAB6uh\ncOqpff1axt/krk5ux3rZ3h+vrcGBJw8mMDKB61XorPuBiF0VN8c1w3BHp6gG\nVJmqONzA/xFeOav/THE22/lgM+VLJW3zilGDiwHX24gFtDJU/ZkxmpbJaBz1\nB+sF\r\n=4aIS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f02cf1edd98bc30eef1d399e8c11ff116723be04", "gypfile": true, "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "$(npm bin)/mocha --exit --slow=75 --timeout=2000", "install": "node-gyp rebuild", "rebuild": "npm run lzz && node-gyp rebuild", "download": "sh ./deps/download.sh", "benchmark": "node benchmark", "install-debug": "node-gyp rebuild --debug", "rebuild-debug": "npm run lzz && node-gyp rebuild --debug", "prepublishOnly": "npm run lzz"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {"tar": "^4.4.6", "integer": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "mocha": "^5.2.0", "sqlite": "^2.9.3", "fs-extra": "^5.0.0", "nodemark": "^0.3.0", "cli-color": "^1.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_5.0.1_1539119179546_0.4131033979476353", "host": "s3://npm-registry-packages"}}, "5.2.0": {"name": "better-sqlite3", "version": "5.2.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@5.2.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "5e99744e44bfe7bf1aef71bcf4a6079f7a9181d3", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-5.2.0.tgz", "fileCount": 75, "integrity": "sha512-jcrsuySida9dmIJ2+XOklH6Xw8bnYJfT+KoSs9JCNH93Ji6TMoVRkbqR9jyfBqDdAR2BEy4BTC/FrX0nRxu9yA==", "signatures": [{"sig": "MEYCIQCd28rZXKDB1AYYCgsaobN7yivJigp+NnNAmen0HREtrQIhAJEHW1bjwe72+qsgr7CA5ZstFZhucVrJzqnKhs+CrJ7u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2542286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFQQCCRA9TVsSAnZWagAAU5IQAJAoo+Sr2IbMC5M30531\n36CQ0qhYPm8eLoV9TFGgBo3b629RDDQkoQV1ySRFyDmJ0jih+76j/i7zeyn1\nOZNZZecz2hgRDmXqGmIL+PMjBArqy/5MMSiRoJp4pUK368LT8Y7tDOQEb5r5\ntw3HPEM7B0GVkrvyyzVGsMlwr4CJ5/B9KL548Uj3ty/u+J44C2O9le6ULMsh\nhVgTrtiM3r406tz2pv+GGN3AnOnTGiLdLBU/IGJvLA3EvZ2mlqd52F2Rc1n5\n0N3kOVhIsWuFjLd94D3Cm/GAPnp7X5o3fhOiw0KokvnSaRMUyB7mQ6kNObd0\nXA6OMNkqwDFK2jhRCDTc2on3gDdlt3VVlSgbAWz/U+oaFiqQtfSFA1bVp+/Q\nDlry83gRa8hv2fWGPH6FrFjuKhn+LO2VJORVb7Df/9s9usW/D99/xx+BA2HZ\njikkdYFkPF3YgCuP/n/Ltt/UbLtHzfM2NhQsnJduHvfdl1jhAlYyRUY9bi97\nYXmLQRqZwCnFRWwRzgCDkVLgSnDIJotxkMkJ7ol+Ui7ga2JbEWhY2lJWbQOl\niAV6DJXC+yvz6D7RmaNQrYsLHpnRIISv07QbeeEE0++WlzW2mYK3D46fwgsc\n4uxvIX8yYRcvDsTQwu3iHc/IySQnXUBSMj7TKxtc4LIH+8GnTxSeGs1+DL8H\n2V/1\r\n=dMh8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "1f8c66d0a61967e44c65a91b693a402216f845db", "gypfile": true, "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "$(npm bin)/mocha --exit --slow=75 --timeout=2000", "install": "node-gyp rebuild", "rebuild": "npm run lzz && node-gyp rebuild", "download": "sh ./deps/download.sh", "benchmark": "node benchmark", "install-debug": "node-gyp rebuild --debug", "rebuild-debug": "npm run lzz && node-gyp rebuild --debug", "prepublishOnly": "npm run lzz"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {"tar": "^4.4.6", "integer": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "mocha": "^5.2.0", "sqlite": "^2.9.3", "fs-extra": "^5.0.0", "nodemark": "^0.3.0", "cli-color": "^1.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_5.2.0_1544881153471_0.931697856236283", "host": "s3://npm-registry-packages"}}, "5.2.1": {"name": "better-sqlite3", "version": "5.2.1", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@5.2.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "30a6e3290da0f6f17482231bdb59f934dd8dd456", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-5.2.1.tgz", "fileCount": 76, "integrity": "sha512-FAqPi+jUl6VBBDZpIZPtva+VcYAxw3sbXKjImNBPvK1TmU6bCuaPn42XuNk6VL6Vhjl5KBZpeIesxJlDqTFBqA==", "signatures": [{"sig": "MEUCIQCsbHPjhRaiQeLzYMNbypHUoQeQXJCZ+2p9f/jxWlobDAIgQ9eIC3H3UGIl/LkSYFre7xvGkatN634UeLD2TOTMfgM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2542941, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcF+T7CRA9TVsSAnZWagAAPw8P/Aibw9WEziaHnBlQs8vi\n1bpzQFFNr5ma8EJpBArR/xrnt17h5IxhxS0NAa/+KYnqyuicDRMTrdLd/ku3\nVqLiaWjHIRmM9awx1K5ofkNvTiTTbLsq9coFn4g7kaEMqroSzjF3hiMkE3MQ\nDD/67fjkA6+95HtIkZyPJG1kiODBQPRxVW3t32dVu3CVEeXW+DaC2+fXCdEy\nKJRsP4i2SSwByNVlNPt8Lf97LIFkuJ5qQmPzbp09dW6vZhoCkdUwrOg9f+Ze\n8onuRPCd7f7kwHY4uuVdE0Cbo/gn8PJ3noZBsHMeven6yPATpGcHk2mh+D2c\ni9GtBaaPRiRvxW+Kxs+sUaRYanRqgqjajRXN+qrJQo0r7Mh6drsBqQoQYWkq\nn/dVyH3Ge7HYbqSMCFSj2PXjEIdkNLxYuDHGrsE3WAl6BYL+YLhopN3gDTTJ\n2SJDGXSs7LUxhwzymN5ow+6POZiYSyZ/cHupPnOi8NOWOSqqEA9D87MjJLYv\n+fiihZPtoRi+IuHOae8rw3Y5SJ6JLz4C5dDxYngrUZT0hLk1Bf17m5UUqwiY\n7mu/oiLR7ekTOpLZKzH+ltv4+VjwlZk/T4LLxxE7VsitFLTfCCYYMfa8QYDB\nWjRNnL+b0D2CapxNQozL1h5FnD8ykrRmpvHiWK/cacCuVtYhA8/XNydFeIoW\njw8m\r\n=DLJE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "971c12de366a050696483bc235321bf833808cb3", "gypfile": true, "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "$(npm bin)/mocha --exit --slow=75 --timeout=2000", "install": "node-gyp rebuild", "rebuild": "npm run lzz && node-gyp rebuild", "download": "sh ./deps/download.sh", "benchmark": "node benchmark", "install-debug": "node-gyp rebuild --debug", "rebuild-debug": "npm run lzz && node-gyp rebuild --debug", "prepublishOnly": "npm run lzz"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "5.0.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "8.0.0", "dependencies": {"tar": "^4.4.6", "integer": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "mocha": "^5.2.0", "sqlite": "^2.9.3", "fs-extra": "^5.0.0", "nodemark": "^0.3.0", "cli-color": "^1.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_5.2.1_1545069818999_0.4212419909121268", "host": "s3://npm-registry-packages"}}, "5.3.0": {"name": "better-sqlite3", "version": "5.3.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@5.3.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "64b5614582886eb5e6c2becfec03d9949f1e101a", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-5.3.0.tgz", "fileCount": 75, "integrity": "sha512-IZWjZsbS00Ys3DHeaB6WzJcJsC4SFiNP5p6doLhYfmZqGOEL84j/O2N/GjvsEmLJzTlYlBu7iVsv2CMbUPPfvg==", "signatures": [{"sig": "MEUCIDSGKVq+AGP2cjamAEMaillirh/JyU7l+gqeskgkUXLrAiEA6XHrm0W1dDX3AZutUETHUbgG0eT6U6EZz/We27uErqc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2543082, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcOM3lCRA9TVsSAnZWagAA55EP/1tFyvqcplrfq+yqlH9Q\nbq+t4CBKcJXDSjMVclam8pxd8yTjYjNFVwHT7VCewRvQDoGF4yIPLzu5Q+O+\nqdLzZN5jDr+wwmdis/kCS3aoSA2ZiK68wD3Blc6FvWTWx/KqEibIOGKRr3As\n3AG6PSwVm8AiXklUySfDDEQWx0NikneqqoIktTgaA21pHkmoeS8y31WB5nNh\nZD93xaZBwCWJcm3H62/sfJHlpil7fp6RGXH4iu1VA3qnnVs/tM3J7Ruo68fo\nHsaAfamFaWuTailcJeZ5KmOfAKX50R6dhIOLyWFSOXrkaRa3RVrfRYDvTVUv\n6XzYB2H71jH5wiouCqWhlDBdUX49yILzZ0mHzqrpJF2O9RpiCEoYloPmp3w4\nczDbi1DqmSqf8tT5OGDjMhgOokdSBeOZQIBt2kBGFzRpwpU9AGIjriYw//OO\njAdt+jaO5okh9TemqeMqimx3qVMdf413CZtlfnm+iLPvEVQFdXMabaQ/GPbi\nPQvgQM3t/iRBT8eSAW6IdnBhGtGfkaDnaDoyF1fiZydlIV3W/SJF0iXLQcB0\nNjKvaOE0zr+PcClzgUwHuw35nitpLOsI0LIt2QKD5CNpONaww5LcirJfXHjo\nkGF6QtXgE+kFcjDuZxghPKJ0fonQudVaNuIYmnuurqPh5WssAnqt5ZGcVyqJ\nCWc/\r\n=okeO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "b965776ecae455657613d790412f97ed928ef4f5", "gypfile": true, "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "$(npm bin)/mocha --exit --slow=75 --timeout=2000", "install": "node-gyp rebuild", "rebuild": "npm run lzz && node-gyp rebuild", "download": "sh ./deps/download.sh", "benchmark": "node benchmark", "install-debug": "node-gyp rebuild --debug", "rebuild-debug": "npm run lzz && node-gyp rebuild --debug", "prepublishOnly": "npm run lzz"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "5.10.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"tar": "^4.4.6", "integer": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "mocha": "^5.2.0", "sqlite": "^2.9.3", "fs-extra": "^5.0.0", "nodemark": "^0.3.0", "cli-color": "^1.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_5.3.0_1547226596653_0.194039829784028", "host": "s3://npm-registry-packages"}}, "5.4.0": {"name": "better-sqlite3", "version": "5.4.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@5.4.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "88c4894f6c0484dc0a89a9ba12268976cd26b1da", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-5.4.0.tgz", "fileCount": 80, "integrity": "sha512-Uj1ZYOcq1GtFyFgJgqMVDoDLTy1B1pM9+bULnlX8szRX4cPjE/7JbKxCzQGhYlZlLkHQvtXXhCZ3skqsQ2byMA==", "signatures": [{"sig": "MEUCIQDMrr5lEr2wy2yD1F5MM0LzJCPx8TjVhVPJJNoKANOMeAIgMGAMQ9l5I+xcce3j32/NEtvw6o3jbTNMURfxkD2Yr/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2607494, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcR2y1CRA9TVsSAnZWagAAH8EP/17ONrX2le6bFPEh+cWl\nqYUB2Q+ceUOQoGQ34kHm5bT+mIsbgXl42FhLUXUxOAghW2VPpaPci4mKfkzW\nd+216jLMjz7bhcvxejuR12o72Qov151oX3YKyxCTYUzazcbdq7vBg5v4+MzT\nFk1XT6hNHBH2Atl0nrKnM17LUKHxD9mGcVLL67k6I1hWl/Aeuj+5SqwgGQnp\nXIDbeIzgans3aSQna70FrXh0nv2wKeYom0VTlXp3BHbcwoR1HwSyCmvTV8nk\nElRIw0UNNQsj7QSUkIxLZj3x7N/15yuM86Dyq/+QkHnveNoR4v2QAwRNaiiM\nBPFRB49pVj170K4EAXdakMm44sBCoRFPuTw9ngu4Y1EXwF8aCkmy4I4s5pW8\nQveGYFz/gE914yJt210dh7Wy32Hwpvi9PDPq3dRZ7Mr5CxTC7mQPBUux+xLi\nx5bB3ztdU2Tgp4SwDNmglFeay9LtoMTAU1sI+UPAakGlm5AGIwqKG82tBvH8\nYLebYc7McJvLOL5ncnavx+kURat4zAwOwBWzbs0jGYLx11XxLuZ6RH6YwZ7u\n1OGUu0vo6m2uLxupLiAvOnUs3Wa8G/XnSNhhV753qu646YTvYn5vSHRZzQkL\nipd07NOMbEO6vKmYZA/W5DfjaU1yon4t1ktgzFLLllwoTxOgLUS5wgfaS+6A\nZVL0\r\n=zvHR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d3a879dcbaa8f77ece0d9090821e9f0bd97ae15e", "gypfile": true, "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "$(npm bin)/mocha --exit --slow=75 --timeout=2000", "install": "node-gyp rebuild", "rebuild": "npm run lzz && node-gyp rebuild", "download": "sh ./deps/download.sh", "benchmark": "node benchmark", "install-debug": "node-gyp rebuild --debug", "rebuild-debug": "npm run lzz && node-gyp rebuild --debug", "prepublishOnly": "npm run lzz"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {"tar": "^4.4.6", "integer": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "mocha": "^5.2.0", "sqlite": "^2.9.3", "fs-extra": "^5.0.0", "nodemark": "^0.3.0", "cli-color": "^1.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_5.4.0_1548184756838_0.4620713058379837", "host": "s3://npm-registry-packages"}}, "5.4.1": {"name": "better-sqlite3", "version": "5.4.1", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@5.4.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "13412a61ac01f2fe20091e6b1c4281e1e19149e2", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-5.4.1.tgz", "fileCount": 80, "integrity": "sha512-uFIjBdnSbzZT8NEqgH1IuARJaA4wtbJ2VmM/ibxlkY+9a0Z8dlyJZBBeKTg2l8L8fPvfpXearuZbjtr+5x8MiA==", "signatures": [{"sig": "MEUCIBkQCPl4sK/9zG3Ifgph6iGp7D+2cS/EAclbL8vle5mkAiEAlzy5quibyiZjfHkpk/7CLL0xm2sw6O03mQAtp4uRUkg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2639015, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdMNWGCRA9TVsSAnZWagAA3S4P/RKeIauPCUooo6UZ7Wdz\nQV2zLUI3a24KYnS+6QBWMmuTecLoLcUeaR67c9+RC9cjyar/nEEN8GTD2hUg\nN4eLMpSfveP30i4qc8JetOHQ0fgCQekkRfnZZDbVUpgNy6E1fprrLSk+fp8X\nBR6P7mW3LheMfA2uB9h2qZ9LEMO2HQmZPfDHa8oVcgdaU8njCQT7dFk7UYKr\ns0c176wYyhUaVqHDJsQTcW8nLvlJqHfpUVGEJOZIcZ2RsyrPhXbaX7nNZWv1\nyvIQRgJGNcLUC82x69ad4vES0oymcewmtTc4C3kDm7R/KNFpo3fb8nsAgyTq\nQQ+vWG2N3ou0h0zHl/jsiqf3fywX493QsIn3lQG4JOuk4NdGK4HrGRLfD9jI\neH7UeQjYLO4W1kl+fTFE5kzv0zUnzZ5ZxTzKM7kup3KwaN6qGNUsgWZuWXoj\nTLtYmEOQDLnkaFqdNf1gP0TEmIo1ysZAnM2T5ZNX20DmQNhnm8nkvrlGeUZn\nCtBheU19HpwOxj5kJ5F9IkhoEVhdwJqZx3INA9pJ7xJXJ1zN2kkChW08uG5f\nNSWw+hpTcRxZ66Plb8kuR3HNSkemm+BvASCCW8Im3BWso3xDhN1Fl6PAFRPO\noF+XL7e9iE7abErA9raSvb6eqUl+LIc46ErmB5oOUagDxmy8IVPZveouJpcg\nA1hV\r\n=BHJp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "1d43e611871e0d215329d3d15853f40033b4e752", "gypfile": true, "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "$(npm bin)/mocha --exit --slow=75 --timeout=2000", "install": "node-gyp rebuild", "rebuild": "npm run lzz && node-gyp rebuild", "download": "sh ./deps/download.sh", "benchmark": "node benchmark", "install-debug": "node-gyp rebuild --debug", "rebuild-debug": "npm run lzz && node-gyp rebuild --debug", "prepublishOnly": "npm run lzz"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {"tar": "^4.4.6", "integer": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "mocha": "^5.2.0", "sqlite": "^2.9.3", "fs-extra": "^5.0.0", "nodemark": "^0.3.0", "cli-color": "^1.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_5.4.1_1563481476772_0.3972557894915272", "host": "s3://npm-registry-packages"}}, "5.4.2": {"name": "better-sqlite3", "version": "5.4.2", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@5.4.2", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "8eaa7bc5e66c67a4665d45969363cfedbec7ccc1", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-5.4.2.tgz", "fileCount": 81, "integrity": "sha512-BtRGasolxvhCwgsak876fBZiXMn0YNOfvy9RYXT0RO3G8RkL9z45qXTuavuMXUL6bx4cxdxP1k4pC0xOfVKwvg==", "signatures": [{"sig": "MEYCIQDvu9jDpsGA/Hu71RoAVpuoQhzLnPYdUYpbwFqqSb6iiAIhANjOlPgFx5cH4QxLqr4/i7j15yC1rfov/Z3A88CiKY6l", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2641003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdSFGNCRA9TVsSAnZWagAA+VkP/R4DGsDhsyWuq2+Njud3\nBHOQK7AnqZ8jy8Etx7sWBDQhGZ/XieoDe3546piAU1LOrQdp8YeD7yJw3anO\n/jyMAbWOVHYXgoPEEH9XO6GrBiMabMx1gPI6kF1daSonV+zBaVdua1D5EjSx\nqrWv67QtMw8oY3umcA3kUACY8ZM220AqdtF2JBcSC3WPqac9zdhUlL0pntB/\nVyE1kQn7UEK/CWua1pa6B6fxEHplEP2EZlmHBLM64R5T8sapW3iBL8YlJy44\n/cdK5gVzBzUiO+JDfWDFTMIzvOh4fcjtq8Ehl6psKLXmY+CO5ieu555P6xXE\nvtVU16XKiAPG4JhztzlPqdc2/AwX+G3vPohbSdqwKO/BwfbbYY749PQK3ffN\npRkgOT5PFnWuwCFf1YXadj3OKqGD7QHxRDIp7AoE5TbE6Ke0eIgh9/UYdm6k\ndKNoFpMbPQuVTainWrmEZHKVvRPExWVrOivgY5tmeTF9i0fn/ivDD+naAGV4\nG1IBWPqWpFHU7lXHEePSoaicRiXPGgWfygJE6cmwgcKkCub8Os4K8JhPKeYg\nt/jreKVkApnbEp1hZVfcZlfGwr2tDpcmv6efJ1sy7xFLUKcW5OS8LYgCY4H9\nxLMQFOCKVG8wNaNANa7CosnCAtb3NxeFsqbhOrpPEv4PGUWsQ7ivHqAgarVg\n4T5/\r\n=8927\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "687ab16a374e616208da074d004b3874274d64fc", "gypfile": true, "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "node-gyp rebuild", "rebuild": "npm run lzz && node-gyp rebuild", "download": "sh ./deps/download.sh", "benchmark": "node benchmark", "install-debug": "node-gyp rebuild --debug", "rebuild-debug": "npm run lzz && node-gyp rebuild --debug", "prepublishOnly": "npm run lzz"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "5.10.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"tar": "^4.4.10", "integer": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "mocha": "^6.2.0", "sqlite": "^3.0.3", "fs-extra": "^8.1.0", "nodemark": "^0.3.0", "cli-color": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_5.4.2_1565020556184_0.9957594526190992", "host": "s3://npm-registry-packages"}}, "5.4.3": {"name": "better-sqlite3", "version": "5.4.3", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@5.4.3", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "2cb843ce14c56de9e9c0ca6b89844b7d4b5794b8", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-5.4.3.tgz", "fileCount": 81, "integrity": "sha512-fPp+8f363qQIhuhLyjI4bu657J/FfMtgiiHKfaTsj3RWDkHlWC1yT7c6kHZDnBxzQVoAINuzg553qKmZ4F1rEw==", "signatures": [{"sig": "MEQCIBhebu/vN1wxUdc0zw+9sZRTVYkLG3iEw27PeSI5PPD8AiA4Rjr1LzLI62+Roo5dwim8/a3dknYHTPima7cwKlMhaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2640979, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcB4ICRA9TVsSAnZWagAAgSoP+wWchaeDoHijL8Srqhp2\nfCymIcqorA6K5Z1P5KYMtmeXiZ1eQQzHmTMnOTnBXBkXy45VbRcKMWz5Mj4r\nWAmjJq8cy7hK5smKk/aqJpzXGzYPmrXxBlJTMYwlwJ2YNulCKpeitxYu218Z\nav9Keg0KtiigBFlAx2FnzI7tV/X0XUHcU1MrNUu9t8k+NYulaP5hwwuQrVZJ\nL4mcrx8qiHMGC4bSxpXCxi7eFRB3c/BnWZRbgFaTSpJjhiz39bX2c0IZ3Hr7\n1g/S9dIcxR7pr/fXBvWlRsrIv0AzZEU97JOWnDWRynKFs+TgPWp9jEY+w1cN\nOE4q+Jnb5DJTldUCyd+M61nxXfsh8BbTbzbnlKs8GJXoIcJaMtYK6lFfFMdC\nTrOicDFMB+XbuHENR0wPZTumRJKFBhUEcVh9RVBSUC+ZrlzI9RzUN2ccv2D9\nL5tP15BCDZSJX2eoVH5BZmdMa/ZrtzqhWBtQSL13VlQKU/MQeON/SC2lYyIJ\nEpEN1qjeout7cqszWKAuQdLxtQ/Lc5lQFfl84wlU/Rq5yaO0YS8mlMmIdP0H\nWrJ97S8Y1GnS9IXM6sZcRTDWmy3w5k68hbGyK97hQa/1DWUVp6OxKEsy+WR9\n6988SYaHTbJa7pyy2Ah/MHujTB6k5jo2qKKnIdlDuxLcI05PTiiF2h5Hi7fD\nvf5o\r\n=ciNv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d6299aa4cc5908ecd17c10caeb17cb2459575f7f", "gypfile": true, "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "node-gyp rebuild", "rebuild": "npm run lzz && node-gyp rebuild", "download": "sh ./deps/download.sh", "benchmark": "node benchmark", "install-debug": "node-gyp rebuild --debug", "rebuild-debug": "npm run lzz && node-gyp rebuild --debug", "prepublishOnly": "npm run lzz"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "5.10.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"tar": "^4.4.10", "integer": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "mocha": "^6.2.0", "sqlite": "^3.0.3", "fs-extra": "^8.1.0", "nodemark": "^0.3.0", "cli-color": "^1.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_5.4.3_1567628807697_0.2760269915045537", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "better-sqlite3", "version": "6.0.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@6.0.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "a42566ac2262bb181b23450b43619343a5bee233", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-6.0.0.tgz", "fileCount": 81, "integrity": "sha512-Q9c91lQwOt2rImk6ZF6tmnxpTnFohdTEJxOsLxdE7i2tuolCpUJ42IZW6Jt0mcNjwOxjGNGl/SGO1bbSJJPPLA==", "signatures": [{"sig": "MEUCIQDBGjTNEx3fDVDexJ9/RP7+Oi25h3S3S3sL8I2W59N5dwIgcNX4ttypmzvBBLGqaB0vTvb2FUnS9I8dOp2S6yDj9rE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2694746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeU9TTCRA9TVsSAnZWagAAKDEP/10DZ5jzS7H/IFv/qIMb\nkWxZ5p56JnQUAtw4NxzcF3LJEVEJ9X+Q42nobI5HmQhVBIKW7FJUaqnhRrys\nec11HN9DqcIkH2UAFVGfzH3nmKNOlI9t0g6GRc5nDEPTqPNoqhebQhWW+fwQ\nap8g9tux/yqvfQrULqIOf7pIBvrfocdD2JckGcTFtRtme/fY3FkOqulW9nZ3\njUWT83MlQX5ecGHsxbUk/EGUQbUkQ17HmLedti1EfWQ5GvhsZMixDKIjYCF6\nRNKdJ3i3r76dl5b18E2pG4pturSR7LcDG9RBgChm+nBm21uLRnKgbgJWFeFO\nxsNKMwx/DFjtnXbR5aArKPG6Mg4SR/LSx60/DzTtXqNa3U4APP62VXTyk1dd\nn1HPD7DOQo+dfsZdDqPKHFRL+tp75k+AhU4m2fzpfxSqys49vHw3fU8jPBul\nyEhsm3ZhuCZ3qGPaOEcZDfQJWkz/ZMrIj75UxS8Id8iw2wn3u97LIon1S4to\neWBow9M3wn/+vy+P8jLgdzkmhpArpYh0wbN18rD5l8j1k9eDY/sQqN9QqIpt\nV1BP6sLDZVWV1G8iPE6oHVaHX1ypP2OwzuWwSyhJIByukwGqB3zQciMs32KK\nzTYeAmxmBSnQr6y2ym0vHT+I7fQRHilTrH8L1lel6BoJCf7RbQLrPwb6DJq/\n2zPq\r\n=CxGK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "ee6733de9a34036405fc116f209338dbf7d81c8a", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "sh ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "prepublishOnly": "npm run lzz", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"tar": "4.4.10", "integer": "^3.0.1", "bindings": "^1.5.0", "prebuild-install": "^5.3.3"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "mocha": "^7.0.1", "sqlite": "^3.0.3", "fs-extra": "^8.1.0", "nodemark": "^0.3.0", "prebuild": "^10.0.0", "cli-color": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_6.0.0_1582552275235_0.7840316902835189", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "better-sqlite3", "version": "6.0.1", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@6.0.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "391f827c119fbc4e92b1d4969c558f07c17c5c15", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-6.0.1.tgz", "fileCount": 81, "integrity": "sha512-4aV1zEknM9g1a6B0mVBx1oIlmYioEJ8gSS3J6EpN1b1bKYEE+N5lmpmXHKNKTi0qjHziSd7XrXwHl1kpqvEcHQ==", "signatures": [{"sig": "MEQCIC9dFEPvgAyfYrkmx/Pmk+qGnXxQsc4sJ0zAmj/jySBfAiBkt7o/h4I01lN7u0BJIJ2jZztF0MwaUT7+aSa1O3mqNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2694752, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeU9x6CRA9TVsSAnZWagAATKMP/iNK9NZMFyu2KurQ+FyQ\nlda6APLO5tKQrvKiGY0YaMWEjYv7lD1LDgEoe/HBxYo++/YyxniIpLdDCSSP\nXQ9P6i2nVKKB2w0fjUFqPRLB6PeUP5XUU0tKLJrKYTecgCtHpUSGnar/g5Oi\nEJbaRx1Pkl0aBfmx5+TqaRQRM+HGP2wcpJorikDTG7sjksqcRrcViYDQSuok\nt4pPA3pe4+fNKPso1JiMcG+hJYM9LArkY6XFnq11llp7i4XT3UDZCWQhSMv6\nFxxJsFdUkPYnwEbqSEK1AYat2Xi4qBHG2b26UxTChy51XxsPu7SFW+yG3Qi/\nmPP36qdftqlvG33NOr+I5r5hKeuwBUJMzQJX7knv0X9ct6rG152zx/TYHql5\nxtSBNB4UhlcHSlADGC1gumr/N6io6/kmCXTUjaGzfCg4b+Z2G431wIZwT0S0\nmLm2fDF+4eooPnTc/+JLqIP9RHK0XTvaio6V3IblPgc40fFUB6oQyRW0tULG\nEa59VHyCG0q3wEeTQYY7uC0LpwH2Viygq077rSLydGL8gUIVJd5aKLmxAg6E\nbZiub24U8yEjYsimG6TBUvEbveY4ddEi792s3GpfLNHOC7Rp3d7iCeo1udZY\nQAcvn40JzXNbU85PUj0PlEbhKjTM+Ti8pKmTGHzKEJBWQZNUDdWe2WW2SHYl\noCJU\r\n=6XE5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "66fcb8675b9420ffe0c4851627a3ca7b6cf78de6", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "sh ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "prepublishOnly": "npm run lzz", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"tar": "4.4.10", "integer": "^3.0.1", "bindings": "^1.5.0", "prebuild-install": "^5.3.3"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "mocha": "^7.0.1", "sqlite": "^3.0.3", "fs-extra": "^8.1.0", "nodemark": "^0.3.0", "prebuild": "^10.0.0", "cli-color": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_6.0.1_1582554234383_0.6134069825089807", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "better-sqlite3", "version": "7.0.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.0.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "13e0840e7ed9f66f3f2205b072d63dab932ece15", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.0.0.tgz", "fileCount": 85, "integrity": "sha512-3i3r8djWlZyjF5IEhc0oJhJP726Vh1zK3w6BBvMgWNDolA6Qbq/gHx5/Eu3JlHbLYsa5UpIKBsT4PzxaZZpC9A==", "signatures": [{"sig": "MEYCIQCOuLELtLmkt4zFPhh0lwUh4nr8hXY9hdsLPL7srmv9dwIhAN9K8MErmhVS3Yl7xiW25xG82uKfz8gO4nb8Gay/nwKA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2695223, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepFYwCRA9TVsSAnZWagAA0CMP/j569/YyCLXkGdyWOifa\nA3LplzTXFbX4KmT2X1ZrnKmYwZKeucEkE6cKS/1bxmjtMV44jMShqQJ2rdmq\noYGwd39QT495FNGClzYqejgB7a9vW5dTeiwJ/0QA+M5daJMh6TDizVbezXCQ\n/P30kXQp1k7zbqto2HqFnA0sKP/81ZLx0bcNH0o+2+5f3ASC5ov/13lkI626\nuVBAjVIGJU0itRRMaZxJkDBB++4TuyxWkf5yz0mC3MRdmxObCR8njI/n5oc6\nFt9st54ytdVVm2htjG+6i+Ej7MK4py+NH1H1IdiQWTWzUr+3deVkrPVaeKHJ\nGxzH9KJJ1LDr15cJeuwtucisKnFfbAIMaaMpWUxqnXPfDlToEazE87YnnRwQ\nkORjAKMydpiuKleWyXxic5s9cCw48z3PEfS+BMGP3gJghH3WI0rpACUvAs+A\nC5DF4xoubk5lxU+gr3HvyK1JAiG91R7s49li3taC4BZdBiVhBRai6zLC5s3/\n59HkFN4WmEWoaZF4ys0oDJysioHtQaqQOJL5z0OF6mAM66OhNRqXjW3ZGfo9\nmVE/s1iV0EHh3fM8VQdJ3Z/Iv82bpYozRrpX+PrHnp7nnJb4iagPSZVyDRyK\nKhPlSqctuDqIilenyoQcN1CRYT3KJQL0aj3HUk+63jiNqk1rKVbu0pAAiqFx\n4+Jo\r\n=skhJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f87669f40a1151ad4599de588ecd9292df809d1a", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "sh ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "prepublishOnly": "npm run lzz", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"tar": "4.4.10", "bindings": "^1.5.0", "prebuild-install": "^5.3.3"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "mocha": "^7.0.1", "sqlite": "^3.0.3", "fs-extra": "^8.1.0", "nodemark": "^0.3.0", "prebuild": "^10.0.0", "cli-color": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.0.0_1587828271879_0.08813409019771234", "host": "s3://npm-registry-packages"}}, "7.0.1": {"name": "better-sqlite3", "version": "7.0.1", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.0.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "7d086130b42d7d5c9d2847899c57ee59eec9cbb3", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.0.1.tgz", "fileCount": 85, "integrity": "sha512-fsVcoGXxnseNajbfFbth0BKnDWebPY6x77UWfrBHApCKq1yo41DlWnFsffOsLpjkQ4VKgDU5C3aUtiw3wGGU0Q==", "signatures": [{"sig": "MEUCIQC+KTJDBYTVHtgq6urHINOx/vYxanllUACzhyCm81dKVwIgJBlf9BvENMr3fofBZlQCAe4X06yB3BCwzHRF2MMFXPo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2695208, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepNq7CRA9TVsSAnZWagAAqzgQAILjCeDlITzeUF8XjQ/t\nVY9+B1wiXJK6UI3y7Yvol9HYlOLbfPdQunMDSzIBXjJ7n0e35X1/R+CUYpBx\nobx+BlSKGIoUWt1pjkLwezwHbA+Pod5Q+QdaUx4hhBykIo+MgXLRZNje5PpM\nzY/9bNbNbQP9iJbiArdxOXs60d4+I+CO/eEmK7ZYNafOXQR2HfhVL76w2GbY\nhtKQ28Ij/5pXPn5eNp6SIuMl5zUHWD3lkHFSdEGmsZJgfJ97lzCq/B48XpGn\nVtawyQkKEFl+2Zz3CS2QHOPAJvrQtflSI8cQEDuHDH0gk00AP/bI5wSN1g2K\nfUacGj2L+YpOuoO4gTS1Xv/hWBYL28afKlRz1/pB20h+mvW2f06ndXk/uhNo\nUBesygBojeBTb/cX5qukkl0/jwhk4gHTJPMfTpzS3F9JSON9b2ZpR71fHd1Y\nIKCbaqXTO0O9yU2I7c+sFA/Xskwmwe4u0rz/GQ8Oee03q8Gnem3Tgk1h1pJw\nmNr4BcYxV4Y95ihFZJvs1eRZxOeW58AyMuQ/VrDdSNZq6Djlm4f8Yxq7BvyO\nDzL0FKUvuYlFYW1jyDP1ASLDw1eO9xtQcJGMbZgJVvsB7lqbFBGmvCRAeG2t\nPYuc5OchNWXWOZpSHPR/IP9FCPzA1hlDwolcfseL14tnhdrmPAbGDjiNuieJ\nJPDr\r\n=yvDt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "32dfdc053c5aba4a3b98cdee4d02554afedd5a27", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "sh ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "prepublishOnly": "npm run lzz", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {"tar": "4.4.10", "bindings": "^1.5.0", "prebuild-install": "^5.3.3"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "mocha": "^7.0.1", "sqlite": "^3.0.3", "fs-extra": "^8.1.0", "nodemark": "^0.3.0", "prebuild": "^10.0.0", "cli-color": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.0.1_1587862202526_0.6543420213177822", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "better-sqlite3", "version": "7.1.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.1.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "4894d0fa4002bd7859a0a539577f14f24b245f6a", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.1.0.tgz", "fileCount": 85, "integrity": "sha512-FV/snQ8F/kyqhdxsevzbojVtMowDWOfe1A5N3lYu1KJwoho2t7JgITmdlSc7DkOh3Zq65I+ZyeNWXQrkLEDFTg==", "signatures": [{"sig": "MEUCIE0AeJz7VS8l4AgXRSSRDm/jqzi2k4hWQQD8W8ulPxUlAiEAkhaJNhqmMSmJVGlM7/e5Vd/Oh8RsAvNPXeEqZGZt5JI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2707907, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe4StDCRA9TVsSAnZWagAADPsP/0J1e1xZkWrG3iDLNz68\naqIUwFURXd6hek9eh4lkj+kiKo1EnVMtnUmFKdLeyjNo16hwRfGPBCRiTJCW\n29lJPMjK8fzVMSNoaNkKmLm6ddY9BJ9ffGzsWe9sL07e6wpFAakAx5cwM6ID\nA4Fo5eUbA93X5rsbncvn2kH/jmq0sxHKYnzda/nhhDrI0R3zpL4x9AyFVcaW\n4dRxeFnWKf0HNu7KEtO6Pn1tX+cMPezqzCT9+O/G616EogmD274fYWJkbxHk\noKcyTJ9cApcOmTKrr567feIUADEQVpIgGgmFD6/UyZH5XhxOU/tehLdw0l8C\nPr2MzMwBgTH/U5xaBDL+hNND6msIxQS6YF83o+PFVVvWzp7Se3ZOlw8lDinp\nJGBI8qQFFZxESl+IyzwUHf2rg5Ye6mwFYRsoTuQMGoRV5fQvQLgKskeeEgpL\n7ZFh1fgXAaVltqEhdo+yyzhtaTnVjtLIGyzANQRlt072SCm7KDiX414EcG6c\na9gOxXokdByTOi7brAjhEr2cEQTSbZC4Un2987uHRITQ7HJ33BkOamnMYU1z\nWZwZC7kgYIg1Ys0j9cgjHwlrXYShQ/t78bNwzvDKlHQ4zupSYt0TzKBvE+LV\nDfcS8DTm8h/eDla+27J4GI7kfmdZIUOZ+WXpU7eLZjqa3ShR1CjiPROPB9Ba\na257\r\n=SLSc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "6be0b0ec618864764a4b2b28ea83d2fe1126a2a3", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "sh ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "prepublishOnly": "npm run lzz", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"tar": "4.4.10", "bindings": "^1.5.0", "prebuild-install": "^5.3.3"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "mocha": "^7.0.1", "sqlite": "^3.0.3", "fs-extra": "^8.1.0", "nodemark": "^0.3.0", "prebuild": "^10.0.0", "cli-color": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.1.0_1591814979044_0.8633718631743954", "host": "s3://npm-registry-packages"}}, "7.1.1": {"name": "better-sqlite3", "version": "7.1.1", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.1.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "107457a8b770cfb16be646e347c17b42bc204dd3", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.1.1.tgz", "fileCount": 85, "integrity": "sha512-AkvGGyhAVZhRBOul2WT+5CB2EuveM3ZkebEKe1wxMqDZUy1XB/1RBgM66t0ybHC4DIni8+pr7NaLqEX87NUTwg==", "signatures": [{"sig": "MEUCIQDGZZFk7V4yqueOAyeIclg9EOfKqLPpBTB9yP19FRVdWwIgG/5eNQtQ0jz/d9Z7yf3/d8viwpMAjki/GaERFssKO9A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2711727, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfX9BfCRA9TVsSAnZWagAA7AQP/jg60dXHL/gp70lEcgVp\nzWBUOyxsogYqog1lskalYy53cKU7z7tc5dEUw+7swt60Xsh6C2BTz0NAr/wz\nb+ZA0qiEKz9sY1Fzxu+dXdpMW2rbG8GBJwG7yb+SV5Uc8tVp5yhcnLLVUQ1J\nQqfQaZRk0T73ffMsYox10DVps3uqHootbaFFVZWV5OOa117q6KrMoO2S1PRU\nGUc3HFrH+ZP9nQRh0KRtsRpujEGMTBwqNN1iuDteOFxuvMkOahgSF7hLH4qi\nOQiWu/QIKG9YLpxQ2lgvJ/zsCCLhnfNy6WBIjh8sr669R+3NuMIOuwsdytXY\ng/fm1z9vWN+b+ZCCAsgOlY+dMSdtR8oo6t5zz62Ils7sbA8MQ+OvHx3bzUfo\nHDF19/Na/346N53kp48JtqT/8eyzP27doxDABa5oc0rGsPrj3gk73FDqugET\nhorZcNzUX3mU9x+2lB7hsdalS/SMCQFZ0OYyZeURRQhrRpoU5HsD3ItF9fez\nwZrfNgbUp1K7ojUrr4/T1NmPDrcpAvV9JNPHkPxZllcrs1VLbyUeUBiW6+8a\norqpeMitYqpM5K5IOggv3gdQ91r72DEH9ybvD9QpTt0HtRl5dbX2bkZjB0OO\n2sxeGD78lou+AQ96YnIdTDUZ8lmJkYBSAwpGThgs2L9TOiETeqIVHMiZovr1\n3maw\r\n=Rwty\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "03f382a3ba1977feeb57ff1349c628b846d8b8b3", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "sh ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "prepublishOnly": "npm run lzz", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"tar": "4.4.10", "bindings": "^1.5.0", "prebuild-install": "^5.3.3"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "mocha": "^7.0.1", "sqlite": "^3.0.3", "fs-extra": "^8.1.0", "nodemark": "^0.3.0", "prebuild": "^10.0.0", "cli-color": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.1.1_1600114783042_0.23078257903791255", "host": "s3://npm-registry-packages"}}, "7.1.2": {"name": "better-sqlite3", "version": "7.1.2", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.1.2", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "95565757a834093f1ecae0d4457f60820ed5dd2a", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.1.2.tgz", "fileCount": 85, "integrity": "sha512-8FWYnJ6Bx94MBX03J5Ka7sTRlvXXMEm4FW2Op7nM8ErQZeyALYLmSlbMBnfr4cMpS0tj0aYZv0a+26G2YJuIjg==", "signatures": [{"sig": "MEQCID+23i0wGW2KYgZgwthm989MLWIIv4AA6JnfOVB2TO7BAiBXKt9AFbs+MjDWlUvAY6H4AwN0hBkdVxVcn5EZUUYK4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2723489, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf3n5ICRA9TVsSAnZWagAAZvIP/3+N8HCZiIC4/JsRnvwH\nL3xFi3j6sFRcT6sWlbrCs7clGonYV+Ljzm3bgKP5h/nRt2Z/Ggd8RS/h/qKf\nZhlnU99g8Oq8n5bVsVtHPzaxcc/oAKkv/9fUn0PbtzLZ2qtX7J2Y5Ng41QtQ\nHR4si+rfTtSF3AnWWv95LtPjQ4F467oy6o1zL8Cfb9ijSh+UWMtkY2Tu/RAK\ngPpKd0hIeEst1ENDvru8kfd3xwPD3V46FtM8ZG7FUZ5zTDY8QJBdfwa3pxvo\nuWiraEfWLBe5JffWbLt1D/dEa1wDaerP2q6CE8z8/Zv3rAUTnth6tfVdixUt\nIuzgBcLHkjSL+AJq0X2OgVkqIKIi4j0C2aqiJP3FPZToaUrL4rL8/Xw+z6ga\nlk87kLxemdDM7VR+SBlzN5GLHabL6DgCkLipHQFpZ2/zdwfLTGrVcTmOyb3n\n4o4VdoN4xYBoC/N+R6OZ7V8bJBIfmLldTWnHetkGu81LZz2MOMQWmjs7x/sC\nK8Gu6QCE4DwjpA3aVR9rMJIGrT+FtzkwQX7JeIwzFbTOQClHaRDmja48rD7n\n3VTVgHUgFufWElpSs4pRMWZZsE860iG0veTCXY/SJPnWSWybIYOhx87nu49U\nqaW5o81tuaYwWLztnJq46hLqzP8P10mWmfIqqPWT8dsbm1W6A1m8S5LQ+VXf\nS1B2\r\n=6Y+z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "6d05e9e4e796c0a1e0f97bd5c7a535a743caf6ef", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "sh ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "prepublishOnly": "npm run lzz", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "6.14.9", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"tar": "^6.0.5", "bindings": "^1.5.0", "prebuild-install": "^5.3.3"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.2.0", "mocha": "^7.0.1", "sqlite": "^3.0.3", "fs-extra": "^8.1.0", "nodemark": "^0.3.0", "prebuild": "^10.0.0", "cli-color": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.1.2_1608416839934_0.3719719419826497", "host": "s3://npm-registry-packages"}}, "7.1.4": {"name": "better-sqlite3", "version": "7.1.4", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.1.4", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "19ba6aa10b92f81410944f498d9f63b000f0b9c1", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.1.4.tgz", "fileCount": 86, "integrity": "sha512-9BvUSm8/xSyxZbnWpcRAwEZsfTK0196qi4592N5WoVMM3dAH9g0E3/colX0dxhGVSLcVfkiBUoCEBkFz8uvySw==", "signatures": [{"sig": "MEUCIGjk9Kji0pWcZBOo772dXynM2UaLcrMlX+0o9DgVIMECAiEApzVi+0U/7iWoDsdZcAx6xCZetaEeBp37qlnKZG65o+8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2746320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgY6tLCRA9TVsSAnZWagAAJ0YP/09YucXiWHWexC3a4vxS\nROVDoyIFFbFyfxaFpSdVCQO/rf/Z5/+xwDpgGtMc42ZK2Nz8muOtsMiGiJya\nnv27AVAiLs2Fkj3ozJsziQhDuLXlJm44b9MRxxNc4mQfYUcxDmEkZGuTgGfD\nhslpORwMkvWAv6YU0cuU9QfmVDVqCqXBl4MPj+VWk4BcgIHgdmJgTJW+TTOE\n29VZ8FD8/M9DwypZlS6u8sm9yVSXpkOab8/4X1XMi5LlXoESC7HYSU9ATyLa\ns2JOQbGK3Oh1Cz4cTXIBPseHcqOimHWWD62KRB1FFLkXCvbfnysBZbqLrkPX\nPYk+hX44YSKtXoOVt+tLZnogCbNEpssdoyvUx/J1m7aW6O8XgpSBKy0mv4Sb\niRCWV2+rJWXMlHlJBV1ZHYmTcbyBDci/vBSFJPRGXwp0AaBvMJFTKlJOJV5p\n4JHut764IUzrdwXaoS56hMqIySuC72RhRTJtsjosZW1vpGd9mSo0kVo6+R0V\nLswoURxud7xkbokmW/mOjWBqy3x8dBlyRNNJmx0OFPbG3yl+Q3xOk2RBftZL\npETw1smXSaatweNrKNvbBPw4BlCqYyxWKLLRR3gHu3xPwxhyBL4qQD64NXRA\n9d9MptVmuFVwYtugy59NQPPB4klwcnLZNOtb6kfp1vELAr2j5VrJsrG7A3+V\nbwwU\r\n=lyCz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "4dc52f1dce355fc5894edf0566f8fd3eb0af214f", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "14.16.0", "dependencies": {"tar": "^6.1.0", "bindings": "^1.5.0", "prebuild-install": "^6.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.4", "mocha": "^8.3.2", "sqlite": "^4.0.19", "sqlite3": "^5.0.2", "fs-extra": "^9.1.0", "nodemark": "^0.3.0", "prebuild": "^10.0.1", "cli-color": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.1.4_1617144651428_0.05541415433337815", "host": "s3://npm-registry-packages"}}, "7.1.5": {"name": "better-sqlite3", "version": "7.1.5", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.1.5", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "3eb0fcc81bed463ddf2da4a02aad7e02726205c0", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.1.5.tgz", "fileCount": 86, "integrity": "sha512-HX5dN0zLllJLQOJP2tXqV42bvjXPnbe/Nl5o6nD1jj99h0148B39zbfGZVoJBLtDAd/CJc4Zdm8K+GhHIq84IQ==", "signatures": [{"sig": "MEQCIDo1yNyl2mNx8RqqguwFNvEkXQeimitlEfetLoCk53yXAiBtT0K5Kfm7OpPaz7NjIU8ac+fOzzfL1JWQRG3k7D7Xbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2747375, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgd3Q7CRA9TVsSAnZWagAASi0P/Rrkc57GCF+tq3By625k\nYwK+G7nR3IvwaVrOq8Q0seso+o87teuQldR1jeJKFNkSICTe+Vsf6z0XRCNP\nHzSgeEh8DXARfZeMY/Sznj1EwOBprBjdJzOnB8Bb8PRVaf4XPdLGrVsLNerR\nVdhk6y4sqNEEaxZL8zBA10jsI7h68iffL7qjr+rEwobNzA6OxBgziHT4j97M\ndonnDYxZMsaY4oxXYFv5/zJ2fwaY4fj+0UV6HsQ9Ar2qA+eRDLHOvbHwMxSL\naijq7JSqNSj0JJqU/z8HjEZVEwVHFeVy+nBYLRSzkjspOAup9EcUK9Zob1kP\nBOn4LlpTzOpKoUbsa8Pswx0/7N0l7pZScRE7pFldEtWIZw2YM5DgztIlHHFC\nRfNiI5bjisNc1G5tmj1k7H/JvELPBBo5UiNr2+nBgUoc2ImClO1zpUoIhQns\nzVCsc4+5qq/hfTrtZRFVyIiVGB9MlJjm8Ckz6Sg+IvVhpet3SXj+ogQ5Lds2\nWMv3kpGZcQoSXwJQy2OGyp1bQ9slC98Lzn4ocV4ZuHwPWPuDxNm3EN4XKheI\nRuzQHmVqBH5JsdCHGNTh6R2KsqZGNcT+Dp8nZKjV0cBYbugXJ5SvqPDToQ3v\nyseGRBoKulRaKTJo+uNVBzrPYM/XfdKduYEmnfgbhY+ZeecMowPJtlukhDVs\nSg+g\r\n=zB7r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "4e68294e1c565c8e4a658b2e2a4d06768bb82f42", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"tar": "^6.1.0", "bindings": "^1.5.0", "prebuild-install": "^6.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.4", "mocha": "^8.3.2", "sqlite": "^4.0.19", "sqlite3": "^5.0.2", "fs-extra": "^9.1.0", "nodemark": "^0.3.0", "prebuild": "^10.0.1", "cli-color": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.1.5_1618441274819_0.9757983311166147", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "better-sqlite3", "version": "7.2.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.2.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "fbc460ba92391e5cafbfd96496ff83f7ac17225b", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.2.0.tgz", "fileCount": 86, "integrity": "sha512-pVhcTgsDJbv7K3us5o2Cyz6MIuGurHIgcDViHOmN1zs/FWf5A59tllLExHprP5NjZOlISsSeRSchjPkj4Sbg3g==", "signatures": [{"sig": "MEUCIHYG+IEUm3iDTeb2EzAJungy5w3+U/2Nyzk3PTtW1+4AAiEAgVi0oYUGU/rjaFgux63UyVFKODZPJZMscC3563PstZw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2748601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkOg5CRA9TVsSAnZWagAABlEP/AqQIOh7y8/X2gC/ohKu\n4zpnNzmHSD4pmwXIhoItDahBof3MLtAxceKrfH3LraLEQMTJ9+4hVmQAhbxm\nTandYSxB0NF5ZXozxQJ67gs+6pKzun4JwCzCaRFfPA91+A+zQK9WN/9jaOVv\nAqcsrSy3qAlZolEpUEZM0KHYI9R9pTXcpHWOdjgIy1s7+033bAVe5mkl95G6\n4WzHxSFiMccPrc+GEikStDvrDGsQPHchnnXYzu+wB0f1h3eaQxLp/YwGxRdX\nW6HadkTdmqIUdW/7aCle/qZu61C5BtEDTIc1rNGrdszSyMnd11qTWH5k1dvJ\nvzoksVEFalhogcXPfKoYd8KDcQoyxOUrIomHT/kSPCqnTx4A8eIIRDSTrO6W\nayymGU8QgdpgyjMscfg9WQJrk/ezdy67CkITQIa0OYnMzoVRRETq+/Kkuhuh\nbONgnNIGT/tLsZttahg1BibYpbbMuz8WHFObE6dZWpJnoYIDR18TOizCpato\n9xL+Wn6bnGFPAuQsVen+Vl9iOLahVmFjlajQXPIwm5aAdIzUddjXJpwK0gTM\nxF8sdlhRYD4AaJTADO/qF0kKsXMzPqSNpXrWWIS4MmPbHUluPXNoopBBnfaP\nYHVdRyBr1ebdNs1A9rHrPshu95w4kLtXU51CuJspDCAlTx5dNKiEwroJUu+W\nnCWp\r\n=ATyl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "898946e5801f892a7a26bba3260b4a67bb52fe57", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"tar": "^6.1.0", "bindings": "^1.5.0", "prebuild-install": "^6.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.4", "mocha": "^8.3.2", "sqlite": "^4.0.19", "sqlite3": "^5.0.2", "fs-extra": "^9.1.0", "nodemark": "^0.3.0", "prebuild": "^10.0.1", "cli-color": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.2.0_1620109368618_0.9402807161183839", "host": "s3://npm-registry-packages"}}, "7.3.0": {"name": "better-sqlite3", "version": "7.3.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.3.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "bec5ecb0e16cd86691c0e355b964f4b98bafc8bd", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.3.0.tgz", "fileCount": 88, "integrity": "sha512-MBelKCfD1z2e46agu80XniVNWjgX2j0qvi5HavcculJV5nu1hz4rBRkS97NNdi9j2ab5qEo27Uo+OSnta/itQw==", "signatures": [{"sig": "MEYCIQCOhL+8frD69vKHNj88T8xNjqFhvtE1ZjPz+JBdGrcndAIhAKgJ2tWP/ofB3f1oZ+g3FhqgY9iOrpeIrl8NtPaK69u0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2762597, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkfNSCRA9TVsSAnZWagAATfMP/R+BxsxkqCoNl+JnS10n\nvqXZWKCDXBkEHhdLnze5DOlmlsZ0HuglOxMTylLvQ/LeD4GFw3JW7BY2vFA/\nb9LGulccwUhnPeSn+i6u0QSjH5jTNuLOZu8buhpODKO2vNNT5HZoTnCEMrHu\nbdv768PgFNYqRRZq1Kegp9GjSS35TFx9esnTO4VtXZgCRMIcZtsfC2+QubpW\n1B99/5GJZr8UXqUZurM55bRkkhiSdDJR7nRuXzLnDyIAACdCDexsrleeNYUO\nnbzMt395X4D09RiunTO3Uvdxa30SihcNE79n4xauZnaopux3zjEHFzC10Tqy\nMNL0PmmwgAgASEjZXhMI+lVXLwW9j294K3xm+s8Xy7Ufgplzbsr4JX15UkJh\ndByx4oZe1bi5WLQkfoJV3ybOz0/vItBHNZNtqhD20w6jbTiHgW8kTt5IoOpm\nwDIr1a6JI+2lDz+uraChczXNLjMpztdS+DiM5nCQ/cjKIhGx9Zi1e5NTMQD3\nvoZIM7PQ3x9TRrrI4gbP4NL6SMZs89zfXrLXixHTD2b8PEzEHEqmpKUzUnSX\n01EQCTlNm/Ozm/NNzES6fOwF+GOIGLrgQCaMFSbWwkXCnm9r2Aoiw8/dO6AM\n90XEcNlr/cqI2y/yY8QWarY8zjkOcujfolNU2IVbHxUn8gCdq6KMaMUfQPyC\nBuoL\r\n=8RBW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "3893b33f0b7d668533e23ba5338f1a2a353e249d", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"tar": "^6.1.0", "bindings": "^1.5.0", "prebuild-install": "^6.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.4", "mocha": "^8.3.2", "sqlite": "^4.0.19", "sqlite3": "^5.0.2", "fs-extra": "^9.1.0", "nodemark": "^0.3.0", "prebuild": "^10.0.1", "cli-color": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.3.0_1620177745503_0.796228123480661", "host": "s3://npm-registry-packages"}}, "7.3.1": {"name": "better-sqlite3", "version": "7.3.1", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.3.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "2dcccfa4c34c544073d12fbb172281a13925fbb8", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.3.1.tgz", "fileCount": 88, "integrity": "sha512-Io0eWFWEtHsA7KS7Ehm45AGwi5SHeCD1hIHd+b1nj26Tf7rFTBqMltuVDimNMNMJ6f+Oy29RT7XWinv3yWKvIQ==", "signatures": [{"sig": "MEYCIQDRWFMZPeZ61ExiadWG+i87y681lwYxvau2urxlKSnE/wIhAK0ANJfyXqJKvB9GnIkfS1wP/pWyI4/tPWezklkBRmQV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2763034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkqb9CRA9TVsSAnZWagAAlnkP/AzhNt0Bebr+0z+/bGIB\nTdNK8mLyqKb4PwWqJKs5N6Smp1nKD/vPkSI7zRPvYIHJYVYuLllXm8LWQFYE\nOFRLDjYJ0KylEJYGIk+pXmZEScB+4g+1GzgxwuSBl+ZK8K/gWro4qKhuRqMZ\nUTIRGirJ5IS/wBpQWKgm++c4+ASV3qIUzfcXX+tZTKxBg9S8QCuKxqnfb9W5\n/VzGWxrQctM6GXhCsO694a6csOgJ9/mjs5ZA2yg+J+gayREvF5jfoRNkXM8M\nnRW6bizgPk5u2FdoEj+/wX0u5m0A/Fpszxi4gkzgq9ubfJTWycZ587S5pNIn\noh6D1/P3B9IeKCkX9Hsyik8avmKQ/9yQJOgf7IiLlZdDPuBXFVTBq3v9LZCn\n2pMteeVF5N1VvIj28ftzyxceRsiJz1AVziCRJC9ViOrzDUtUgtpsths/il1w\nl1aJu8/LnXP8AkVxu8veYD7sPkbuvrqHAPmqhqh+IH/BoVUxLWOf2TZoB2k0\nRappeE5LAkOxjfEtLPaN2TLkISX1yFgg05JAgXyDJpttSF+8w+CUOQjjjHXF\nlfFPmQvKA7NvImMePiMzxB6ujk0OFkgIZqFkprRyfISddrnjnU1ITxh04jmz\nbOfA/aSCF7i2kJO28/Be4o29NEnYPq78qm1cwppiHTD5TJeEJ/xNjOR/2rXB\nwh5m\r\n=7LxC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "696719b8f59a40f47704b1cb72f950adb33f7c8f", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"tar": "^6.1.0", "bindings": "^1.5.0", "prebuild-install": "^6.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.4", "mocha": "^8.3.2", "sqlite": "^4.0.19", "sqlite3": "^5.0.2", "fs-extra": "^9.1.0", "nodemark": "^0.3.0", "prebuild": "^10.0.1", "cli-color": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.3.1_1620223741022_0.937020648342179", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "better-sqlite3", "version": "7.4.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.4.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "178cec863aa3090cbb0c5392f920925811d6ac87", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.4.0.tgz", "fileCount": 92, "integrity": "sha512-hXwwaFvtYwRfjBSGP6+woB95qbwSnfpXyy/kDFzgOMoDttzyaWsBGcU3FGuRbzhbRv0qpKRCJQ6Hru2pQ8adxg==", "signatures": [{"sig": "MEUCIQDi4fapHsuxpRP0WJHmY6EDcDNFyhEcXuiyA01Fc10SrgIgaHc1RC2i2YE5MfocPHJVFPX/3LbOy71nlNNQk1hSwnU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2846228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnoz4CRA9TVsSAnZWagAArdcP/3MF89vMQTi9pINvuBrL\nTAg0UNpkC6ZazqbOvfET0l5he7cno+GbWhMqx2DbZ4vfBTA2j5jBLtSaxYj5\nDtuJqxxvGGsqFLWjqr0y6xDrbZBPyRHJj821lQKWygKI645xcU7X04JGxLhC\n9c/8pvgG25o8waKngEJdYt/CzQN7rtoCGz622auuWz6zQmVTEJ4gw1FGDv/j\nMKcQ6sWRjYnn7z2Hn1hPIJvIDDdhHcD/GEDERvAZXpcErbNhPgpaiY6TyWMk\nr7GWEniXoe5Wh3oEG8jFayrzWIxdrnGb6ZC4ex5GvH/spr5y6PyfBmwg8Lg7\npyUD4se2roBAio7ZlO6ZdlP2WHtmx8HW2Tlz9/nG94Llfns8ybSYUFmIVcBo\ngigZyz9tePLRSeGZVASzgDS3gz17kYtJSb+rkNrQsVs5/fzulOrWDgFx5mtr\ngEy+vnAckJt47JwHeCHNpqAK/o3+WTKjqrDEZZ2L+YUGEhbM++FKGHeNFNV8\nBsliHAzgxoVyMB3bB7xw7A4pyENsMXN+gZp3/6XRhPOwc7BTGDQBrMXHsR1B\nkb9ZpfVETF9gDybc1Hi3p4Qww/kr77OiwClzFkeU9Ft4LFW5c/922iLBckT/\nLnW4L3zO6Xbi1KAlSYonexK5ky3b0wEr+KcLZ0L8DlEpKO0gL+z0LxMoXWvY\nuRpo\r\n=CUqR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "8d06b17720c44b16e45a79f4f1a2dd6127cc5921", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"tar": "^6.1.0", "bindings": "^1.5.0", "prebuild-install": "^6.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.4", "mocha": "^8.3.2", "sqlite": "^4.0.19", "sqlite3": "^5.0.2", "fs-extra": "^9.1.0", "nodemark": "^0.3.0", "prebuild": "^10.0.1", "cli-color": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.4.0_1621003512248_0.7739121125885426", "host": "s3://npm-registry-packages"}}, "7.4.1": {"name": "better-sqlite3", "version": "7.4.1", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.4.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "9daab6fb5be44460772992478ccda2db53605b7d", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.4.1.tgz", "fileCount": 92, "integrity": "sha512-sk1kW3PsWE7W7G9qbi5TQxCROlQVR8YWlp4srbyrwN5DrLeamKfrm3JExwOiNSAYyJv8cw5/2HOfvF/ipZj4qg==", "signatures": [{"sig": "MEQCIBurztkYZzN91jZb92b9SLqzKDVSJRONF+n/iTL24kWdAiBp3lD06VY85pOUEqnN/t1bYDtiZ7uy/63u0csOyC2VBA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2846519, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvDQvCRA9TVsSAnZWagAArJYP/jxXa2RspXFQB3Bj5TnC\n6Snigx1ul61pJVTUoFOR0Sx9EDS/4jPBCB/GcZkCZ/BUi98XUeJU80K7trCR\njpVuaKO+kLcZRf7/HHy3Dv5FSS/xLK38fE67s91rkAYIuj82lmOZMsEhNOwx\nWhDXnzJWbRYO1sWMeIRIh7CZ5l/7Oag+KgP67kF52kKj+6epmXWEWEMk2OuP\n1b70s8TbsHSo0wdwHcQ9v5132pY+afXtB8tX25jO2xR0hmYhV8a9p3OBKM79\nwAo/L4hDYt5lSHLPiaoxNb2+JYuyZJpB/vjMVsmtPHXHZHPZ4eIq/0m/SzjO\nFyv6+6+4pyffg8eJVoahQitbht9V+45vldeUBLWoDJZZ35eK+qwVqgI2vDqG\nwZnMEVkGEdkeuS7CKOnXv5zqjX//aB3UgCOp26NdE695Wq+jP9BaXSwSAeKh\nkNNH7ghCXn3TPpuvsv5ottrlM7qR2VWQB+MDxlo6qAFCbk26QYodXrIPaj+V\nna22g7fIHcFaDcJSWxnrvJvZpD+jvuUVMe83awieBdLrjhSckyVVIKOGKseB\nhUxY1dSfRdWhr0O+x/nI6eZTKqrBmjVcvB4JSURO4OUqmnoj6W6sz4YsFLYB\n6Lobfm2V/wiPnNUYUvM4Qe3uHJKCB6nr8ucL0Axxufu/0cdDs8/KMjkP7oOL\n2sp8\r\n=9GsT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "309708be17c62492f9796b63003d2a82a939dee5", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "7.15.1", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.3.0", "dependencies": {"tar": "^6.1.0", "bindings": "^1.5.0", "prebuild-install": "^6.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.4", "mocha": "^8.3.2", "sqlite": "^4.0.19", "sqlite3": "^5.0.2", "fs-extra": "^9.1.0", "nodemark": "^0.3.0", "prebuild": "^10.0.1", "cli-color": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.4.1_1622946863088_0.4248187858674697", "host": "s3://npm-registry-packages"}}, "7.4.2": {"name": "better-sqlite3", "version": "7.4.2", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.4.2", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "12fc2089f1434888cb3c710f59c849e0e740c16e", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.4.2.tgz", "fileCount": 92, "integrity": "sha512-AnLxapjr0EnWDC0qmcTqOH3n/9YGK1sr7wCCAdGBzUhO/LWx+Fh2gemSnKTM9lD6VKWSf337T6NqbTgnVVz7Zg==", "signatures": [{"sig": "MEUCIQC/KZwNZ1Zo90RBBnFuEvDM+23Yx60OaVGWt8ZFRgezsAIgc40EhuziG+Ey4RepQn/55KaKObvSO1jV1X1Fp/wtSmA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2859520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9QSHCRA9TVsSAnZWagAA5y8P/2qTs+VT6pyNF6tbTB9u\nS8UVg7UoabQTZ1QxjOS17ZiHatFTMgwhsUPbr39mGYMdJhUDT01KmO/LZJi/\n8NNqZY8n+VQL0pim2zgXgmjV4Ts0yOGTDesW3yx1hD0ns6OpTHCG0BOKIMTs\nz1jW8vIjBow5rLzk1a9ifRV1HXraN3aRoJ5KK+x3HiKaetGrw5ZiM5r2Io90\nlnSpjuo/BxnHWpfLzG3w5O2qzR/SD6jbnORl7VEWSBcpfGRJ6gUrryLCFvIO\nqO9AXulxWJPh/bZHkGJ4arx7+E8SIfzaQfhDf1L1+uiW9gmaTxmdmORetD+F\npauo093rb+CP+WOX2YsjEH+BbQ69Hrg15JEORTum+scI2XeKH2WgP5YaDcro\nOIwzlAwnLM4RwHBq/5G1VQgu++1xJyNTg+TixuZy0hGSXy3HP8wD3rx5kKZy\nAqupNBXas+PY8uzoLLP0trSOoCJV17Gs1BnrGEsBHwCiWkDXhhEl4+CnmOQJ\ntRcXzq3dPkH+eFFc4/qJvOTKbhfDXZkzXXPkdAfHzDi4NaEIm8GoHYA0LboH\nyB/TWXWP4mwD4Ihmgn3O8ssd78IpkXzkiXZsVvjQBUKQw3Nu5DV19IzgwOWe\nhC96maePddlX2Z+LvndBKtZ3VDALH/YgH4yjc4ryoYC5lyQLqJfjM0Vu95Yp\nRG+5\r\n=XtP0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "3467abdd4c02741dd8b6ecf772721ae1d5e075ef", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.5.0", "dependencies": {"tar": "^6.1.0", "bindings": "^1.5.0", "prebuild-install": "^6.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.4", "mocha": "^8.3.2", "sqlite": "^4.0.19", "sqlite3": "^5.0.2", "fs-extra": "^9.1.0", "nodemark": "^0.3.0", "prebuild": "^10.0.1", "cli-color": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.4.2_1626670215106_0.19662175374084012", "host": "s3://npm-registry-packages"}}, "7.4.3": {"name": "better-sqlite3", "version": "7.4.3", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.4.3", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "8e45a4164bf4b4e128d97375023550f780550997", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.4.3.tgz", "fileCount": 92, "integrity": "sha512-07bKjClZg/f4KMVRkzWtoIvazVPcF1gsvVKVIXlxwleC2DxuIhnra3KCMlUT1rFeRYXXckot2a46UciF2d9KLw==", "signatures": [{"sig": "MEYCIQDeddhJnpWt68Ustgs2iZ75WduFQ/JUN7d+mzuTgWUVBQIhAND3fIKmxBf2zh877xiXJ3+YSMluEfKxS0zufwrKrky0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2860254, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9QvYCRA9TVsSAnZWagAALHQP/1udfuUnKmoJdjCxu8Nh\ndQ+jky/BnHP1hg0ZGCe2mrReFTZ5/vaUjnPDkCOrDEBFxVQzUWBwMYVyxjjD\nE8SgcbXbNllH++iEoEQ7BTnLdegV+Q0/HLJ88qsIlsHyUYRkQzUziHr666HN\nt9l8be9CEQw/7csUtw4E8Eh4TdNVSFxBJxC9L6S5kJDuNmX/+p5Q2nLuF3pB\nA9mY9dMS8dc5ualVOT55b582a7476uwzAPDef6amBTt0m+iI2TpGPVu0qkHI\nWMb+mOOquOoyOSc0YoTfKir1nq3VIeX8Y/DGFLhZZEV1j7Fnfe0bWiWoQMzv\nIGijR7tnUgJr3eCgf/NTxwxgrhue4HBhtZdJWElnoTE7o8gF8dZBBUgIGVSd\n5PAWR4dAIEklFomhzM1Nn3O/3Wvdhmet/KOpYH9x0HfjyZ4R+kIYrKtDk144\nKEuLsvkqS7UB093RSdcGMiXV0PsFJCHlUO/kW6sLjvxbS54urSwfBOuRBtHr\nmaTakpzcDYZXjmEweerC1pBQ9aJG0dF8s7d9wwZQCUNDVR8bUYLBmJpuIQ/H\ncGOsKTcQe+zc/NVhi3O1ShfsJbMB8pDEyHfJPAO80KfNnsjsDiwdTZ5V5hqJ\nm24vxKguQueHsuVKWfv1+u8f6zD5lhCXyvnRO5NjH+w6Va0uxE2XHqlfj/Xf\nNyNp\r\n=ssb3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "1fa3c77e9016b93678b25e542d285ea4f8986f5e", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "7.19.1", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.5.0", "dependencies": {"tar": "^6.1.0", "bindings": "^1.5.0", "prebuild-install": "^6.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.4", "mocha": "^8.3.2", "sqlite": "^4.0.19", "sqlite3": "^5.0.2", "fs-extra": "^9.1.0", "nodemark": "^0.3.0", "prebuild": "^10.0.1", "cli-color": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.4.3_1626672088167_0.47924453682177703", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "better-sqlite3", "version": "7.4.4", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.4.4", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "bf0f595c082b7e85551fdcb6c5ff2b74b78048dd", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.4.4.tgz", "fileCount": 93, "integrity": "sha512-CnK1JjchxbEumd2J6lqfzSG5nT4B/v+J9P0AKSm3NHSfcPsEGE4rHUp9lDlslJ1TL701RM7GWlTp3Pbacpn1/Q==", "signatures": [{"sig": "MEQCIAMKcEuQZ2E5avAuNwNX3601iBUf49rYcYLuHcfSh/IBAiAAw0K52DfkzhSDVjrKAjjOws3aW/V4zgYoGJKAaaB3Ug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2861178}, "main": "lib/index.js", "gitHead": "1ac101d7dea3e60b973fd384ed4d45de288072a2", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.12.0", "dependencies": {"tar": "^6.1.11", "bindings": "^1.5.0", "prebuild-install": "^6.1.4"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.4", "mocha": "^8.3.2", "sqlite": "^4.0.23", "sqlite3": "^5.0.2", "fs-extra": "^10.0.0", "nodemark": "^0.3.0", "prebuild": "^11.0.0", "cli-color": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.4.4_1635170681576_0.5854568896499805", "host": "s3://npm-registry-packages"}}, "7.4.5": {"name": "better-sqlite3", "version": "7.4.5", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.4.5", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "acfc48d786114227f550a0a45e22ace51336e2d0", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.4.5.tgz", "fileCount": 93, "integrity": "sha512-mybC3dgrtJeHkIRGP36tST7wjBlIMgTRAXhhO4bMpPZ17EG23FZxZeFcwKWy6o8mV1SKQFnQNyeAZlQpGrgheQ==", "signatures": [{"sig": "MEQCIAnTk/+fMANmU+Kw7HRI+kGAVKlgS14/PWiIvvjUZ38+AiANysRS0Yv3vgDV4gBtA/b2wPMrINreYI7oUBMSyOlSxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2861816, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhmCg9CRA9TVsSAnZWagAApvEP/RRrYXxN3DsUR6S5YGpv\n69H3zbeSJScfv3kbvMmV8c/NV+wMml5uDhrk76hkcyurCPLMFa4BXx9vUgyC\nQqMMNsKT5tI0dBchRJzC6g3BC4jnJx/nq2KxQjM79LY2GiBnT9TTFwwn5VR8\neN6t/zpvGB7sre2474GwGXuy6/IK5CsCKlWI6lEpHG6dlZf2tlTRUX7euMSV\nvZKdltXelRJyOP2giQFMVmYsw1IhU/45oHL9sQc98Op3FLh6MIQnSmpWWCuT\nEV8LjqxH186paYyx+OEJ21UzDGFWruUux0tEmNobkYOYWHIQKyZ8KJrbXc9q\nghrMRVFCc41BFn42NkPg9NsRcm4iErYgB6Bxgz454ZDV7KaMRj9SjB+5qc+n\nWVxgBwu7pZd+Ck/VOCscG7teoNxmKPS+fh/OfsSWgZ5mzw/QyzThEyUlyCEC\nMCGoklZ/GJohwH1zP+gpqELWUGa5+cwmYkb/j96LYg+KIosEojA8OTnh0by6\nZbkJ+UMqSUVlv96qA6KGzFcWNt8kU0iaHXWuOTQm11sTKmNyoa/kyY0MSOL3\n/LUwXfsEIFDcc1OVYIEVwBcJJ19ymjyhMtty+DbyXzI9gwwcO7wHovNiJIRH\n3oLs+4khQU6WTTxvOt1nMnml84eX+trhmVuAa6b37tmweQXOsLrtq02Fd3Bz\nnJr5\r\n=S1Hw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "fedcca3e318d5e95fef76b87bb9d7cf885fb2c2e", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"tar": "^6.1.11", "bindings": "^1.5.0", "prebuild-install": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.4", "mocha": "^8.3.2", "sqlite": "^4.0.23", "sqlite3": "^5.0.2", "fs-extra": "^10.0.0", "nodemark": "^0.3.0", "prebuild": "^11.0.0", "cli-color": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.4.5_1637361725462_0.3339132074466349", "host": "s3://npm-registry-packages"}}, "7.4.6": {"name": "better-sqlite3", "version": "7.4.6", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.4.6", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "c5dc35c71bb9c9ef5d9e16019686371ff6a5f25e", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.4.6.tgz", "fileCount": 93, "integrity": "sha512-LB/UxnMhcJY12bRCDXl2jTk0lsbXHCHOLn3cPjGhy3GCcVPGq45sCGJPUdfBZnfXGN14tYTJyq0ztUI3lGng8A==", "signatures": [{"sig": "MEUCIQCTK0EkOdyHcKtUvvEuZmUkIBBKjWN++rQ4feiG60LLHgIgROvPNjOmmaYDMDza0s04AJ1nfL8OQr29W/x9btv8qGo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2881686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzPExCRA9TVsSAnZWagAA2fkP/R+k1gSQc27MYjWu8ZzK\nQZ4aUtl6TzjMWg1wcuL4XVdpChBtT8SvKZrM0abxoiE1nPmnD9/Pb936NKjl\n1e/XTfBkRnsZSfBz0fgU3aAwtNL0+QT/277hi8VXHO7mxEBS91OZ5xM9KzHX\n0IGX+sIiD66piaPQfaMKEvDBGWpKbqUOL7kUMRzRjlR3isEj8BcA/axEk7vf\ndlhOHPdYuu34vjFX25dDB9ppA9O+7hAXOP7b1snbybgiP0FMto43u0w2se8g\nQG6J1l9CWRxUBGFhcyf5JLhwdoKeOj3ZHe7URJj5jtqiX7vXbQdLltTNjEXG\nOPWIfEWNXzbdPZxq/Hen/UButOy6FddwQQZ0oFj7smQXRtArIjhuxrUyDdyT\nWNzSuA9cYnsXjK/+Yb2XPqj575GGyWEVHaF9/yjpATITFnpVn0XuiyJTFucE\n7+zGNLi3rYMjUh2crFMNNqy2gF289+PE36RdTsgq3GwH/rLjxHd23hKl2tYs\nunTp2GScZtj2O4wpOYf707S1rqYA9w3K0SfJcPlCSbQoM+VaZXzt5HqUaCgn\nAa2nmygt+i/OtO/Lroo0OBM/I49MRK9f23vEUy8C5x8uL+SEWsv3Ez2Jc7HN\n/iJwX3786wdJUkfmsChXRdVzicKaug8QYfiXXdxNMyKE6Fv9OgDhCvoFKN28\naA2V\r\n=iMZG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "230ea65ed0d7566e32d41c3d13a90fb32ccdbee6", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"tar": "^6.1.11", "bindings": "^1.5.0", "prebuild-install": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.4", "mocha": "^8.3.2", "sqlite": "^4.0.23", "sqlite3": "^5.0.2", "fs-extra": "^10.0.0", "nodemark": "^0.3.0", "prebuild": "^11.0.0", "cli-color": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.4.6_1640821041468_0.9051056223556524", "host": "s3://npm-registry-packages"}}, "7.5.0": {"name": "better-sqlite3", "version": "7.5.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.5.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "2a91cb616453f002096743b0e5b66a7021cd1c63", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.5.0.tgz", "fileCount": 28, "integrity": "sha512-6FdG9DoytYGDhLW7VWW1vxjEz7xHkqK6LnaUQYA8d6GHNgZhu9PFX2xwKEEnSBRoT1J4PjTUPeg217ShxNmuPg==", "signatures": [{"sig": "MEUCIFYxib2kYIF+LDmxDKC9YoPJeWu2GncoQqcL2pjC0AmwAiEAs6FhoZB9GUXtHXtpM1ATHDy35puQ95XuNByAqEoVVII=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9234602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh53pqCRA9TVsSAnZWagAA9B4QAIKpNc4avW5ZgaZK49FU\np5ji2Q5pB/G3QT8ntKWcIjAAh0l5LmLsUzbsLE0+X6l01f92MLU1/NhQ7VtZ\nBv4qIgLBdtnKmgD9aXA0I/ZUu3cbnc4hMcGOXMiT+TziSDvrLHuean2HLfFO\nw7kxRQ0pTLHxDA42Yr8YVm5Q4/BnpiSAkbNi2hjnlMXV6kq4T8gmGthKFX06\nx4ezxurC9nqjmk4XyqQzsbC1cDpPA6uUn/EmIqdaNUN/zBYXWkRiSB3VJpju\nuX7hOlY2Jw1l2F7vRWy9iruljGYNTBT96mI5chQjwMO0W5MynCuJtvkksuzn\n+Fw3x5PT+gakfuoHHjaiJGlB7QdKWoNsYJ4XS3x8xLHgBFAvcggpv2vQ62H8\nTEmeRLCmwx5Id1DCl7oCYeWR98uH3WitohmlgzxvJeeKLdbxTaKjxN2alvH0\nqpeYRfW7AJExa0qKjgksQ5TE+y4MG8mqPae3X6xCuyDn7oN0O/w/IUh9r8fa\nTsoTw72/3bOZh3IWYstldUjWY5ViV3AGxFkrKkg8YY73++bygduom2PZt3tt\nd6Dfg+nZc2LzvJ7fQlA991Yc9Cl8QgmhucEEmrzQYeATGNzmV1lPUZhjpJ/G\nAyrT/PuAjBKzww5Qw+ILNt4zp+T4GFHNnkofVv6O1y6UBn698mO++Sa0XaOg\n8FjN\r\n=h8gt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "98095c4edbfec12e425c37121a98093367f6f82d", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.1.2", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.4", "mocha": "^8.3.2", "sqlite": "^4.0.23", "sqlite3": "^5.0.2", "fs-extra": "^10.0.0", "nodemark": "^0.3.0", "prebuild": "^11.0.0", "cli-color": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.5.0_1642560106107_0.7953890604466674", "host": "s3://npm-registry-packages"}}, "7.5.1": {"name": "better-sqlite3", "version": "7.5.1", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.5.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "22c87135b871afd60f7cb0469d73d5339ad38665", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.5.1.tgz", "fileCount": 28, "integrity": "sha512-+i6tH1y9KEIol1iYpZJrqDwBDQZGHioDENU49Rnidorp3bSXvw/QTYDjQGq9+TFF7RX4q0YV1sEOIRq4vDZdRg==", "signatures": [{"sig": "MEYCIQCm6WwRdwU/h7Ocqhly2GnfzNyJ4fUKD27Q/gkfyTZZhwIhAODT6wU2aq1Yg0j3QZWXXYGFjyAAohgmZ34XzXiOGlhX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9326205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTm8bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLRA/+M+Avd2zj4YswaOHvBk3lAg4M+cEUUJ3QAMA0GCYVW04ERG4j\r\n5TR4WyGEwmgaw0tCpFaxba05939PCmaukpNUsHdAqcqA64PwNiJUQ9YXIaz5\r\n+xpy5MOaGKYyOrXj16UTe0j/RiXRg4yXbpDxGvsasx47Ksv5LYPYQ/czENdo\r\nMRZMakSwzO2JQRPvlxatcteU7UKBeIrdavYOF9gp/MiAlEuRaNWq+G4RCBD8\r\nk454aWZUo4PXxx2llY7I41MekzwzfJL1tSIiW9XDM/zPPHf91QrxMlOrDe8a\r\nxRj/lGI+1rQ3aIkrb3e53KvtnveJ7WNUx+2lXPyeaWNpQpfG/6+Ow8SHF0Zm\r\nAcvcRmyPVagExOvMDuvZepF3z/VtEUIQxd1a2L1z7J/RCxcLlHbtOxKeEdPW\r\nqaqxJ5p4Fw9ceGFIgB5AsNO5tTLv0GpHX/FB3MBpFkQl2gLSz93RhFvq7Zo+\r\nkRE+W89Q9VpUFTNjoH4REzFgMZHDVALD3EigcR+jSQLCPRQErIW7A4JIVPFc\r\n3bHkXZ8SzjUvT7VKgX4y46j7DmtHqAJHykfpMNmMHejhi8Nd/2cnOFBaWDIR\r\nvSW9tr2J8XwhEyQKdW2OUxZGlJ1ou9bdLrJvuFyap5/elbcOAXXMPw4+fe/1\r\n9PrhWkFhOUdBXQZ5egcLsfKOpu59o2C2DaE=\r\n=Z1Uc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "feba6d6cc33fe1e68425f8cd552ef0da5670ce60", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.5.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.4", "mocha": "^8.3.2", "sqlite": "^4.0.23", "sqlite3": "^5.0.2", "fs-extra": "^10.0.0", "nodemark": "^0.3.0", "prebuild": "^11.0.0", "cli-color": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.5.1_1649307419197_0.824485111580175", "host": "s3://npm-registry-packages"}}, "7.5.2": {"name": "better-sqlite3", "version": "7.5.2", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.5.2", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/JoshuaWise/better-sqlite3", "bugs": {"url": "https://github.com/JoshuaWise/better-sqlite3/issues"}, "dist": {"shasum": "830ba8f4083acb7d2fb20fe4c1c7d6e724ad3ef5", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.5.2.tgz", "fileCount": 28, "integrity": "sha512-qJHsZRS3bo0zC/pBbhE7yuRMMLGVCGukqdWvs+EpcrT/ZDnvLiiCv+RNqu7YuiE3qpDqHdsm+IkwCU9dXZsh+Q==", "signatures": [{"sig": "MEUCIFvRW/4mFYbOI6HrB3GlI+0zW/2ZaGYNVgRv/PM4cafQAiEAtOqrgKsYIeaxf0w/ZcftAoquy0Jt+iKtRwKQyiYIfd8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9328510, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigSZFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqH1A//V/3Bn1/hK6Ql3psSuXhFOCoyIfRhr7x54UaAWdvQjFqs0G08\r\nE/bkPvLjvx3JiJRbhikNutRYwEzCe51jyipg1BLmmDoPyhZUZOWXWeo+dLH/\r\nultrJru1+HZ8oEd49JeQAGOM3L3/UBRhECRa14hhYd3PsSdAIzgWq7wKhXPV\r\nauHYaBOI+tOrCGOxLBxoRSYYbnTFajAOWr1Q6O1NbWp7IzFFv5GgjG6GQVDH\r\nFE0e8d35+pFMQi7aNDUNv41SeyHIcMG0Gr1L0oQRANbGU477h2F588bwML5z\r\nPMWIRwPVqjCMurvWJX4cCGCIX/4aXS8D79KNw2tbWzaJ7YlyK3ryxl5hTRQ0\r\nr6sybcb9KCXRSLpnxIXlLiA1my9OLxkJxD8ONIviaVlMWJqjrSvVSnb3Eoq9\r\nm9+Z5tuYf2efvjF2nRdZesRE9DxMZtJPekOOaJvRBW+gkl/VDooOBD7mD7QC\r\ntWjsNc3+2H9X4eFOTG7thUQjZURqnNFp8q3C3EtMF0fDN4XC60RvI30TSXmQ\r\nVH1rQcOT1z3b8N5JbrGDXWsb2K7BSlvfEU0qHL1lfoYuZhfrvC23M/H504Kg\r\njQdZBwZC1wD1H8ch4bgaGwI1SE02kWlb3lSOrBcluELy+PTKAEV10XkYxLu3\r\nS7XF/DEFMNjq2B9AX7nBVwM28KVaDtHa+dY=\r\n=s/bh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "6da9e569dbd3ad6d3e04b4cbd61c788c039d2ef3", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "deprecated": "ancient versions of better-sqlite3 are not supported", "repository": {"url": "git://github.com/JoshuaWise/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.6", "mocha": "^8.3.2", "sqlite": "^4.1.1", "sqlite3": "^5.0.8", "fs-extra": "^10.1.0", "nodemark": "^0.3.0", "prebuild": "^11.0.3", "cli-color": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.5.2_1652631109323_0.30674484966541016", "host": "s3://npm-registry-packages"}}, "7.5.3": {"name": "better-sqlite3", "version": "7.5.3", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.5.3", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "b42e02941f918cb8048971273abc458d937ab2b9", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.5.3.tgz", "fileCount": 28, "integrity": "sha512-tNIrDsThpWT8j1mg+svI1pqCYROqNOWMbB2qXVg+TJqH9UR5XnbAHyRsLZoJagldGTTqJPj/sUPVOkW0GRpYqw==", "signatures": [{"sig": "MEYCIQChxoPSZm5yZUjURZn+FsmMq8h2cA1nSwLxdc5Ogwe85QIhAMyWl9wspYdE2qKTH6b5iF/1o+wA+tL/VRf/YCkRkDBW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9328620, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigfKkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7Uw//eGXgWo2OZpo8dYzFze3YnzwXLac0Ng+WDg6W4N7uQl9N9bym\r\nIZR9csRl0xFixsM/8P69/wjM4pDQerVor7LJ3NoyfYB2y4zZ0EYgkgfnZiLZ\r\nk3la+UcFmelTRjRTEJX6Z0zRYGoluk7RvrpfhTLIYgARMgJVI7qeyL8VEUDg\r\nwhSlAEe6MF4Vaa8+mVSwp6l8MSMRWwf7lg2vvNAY1nhva0sakexyBuQwrHQe\r\nm+akPwPhBgRK+PzeyCdfrChgy8Zwj38DdM/L5wvkGaqspEA7te204VREo9Du\r\nUONyO/xGsraqPxQwzllYgihyUZJY7zdmQW8jKwuanHBez5TZrtcW8UU+FgXv\r\n5wDBnHtzr8jFD6xPISSlNmioa38lANUtS/s7/C9+A3IfNsBmIEKS1ukUy9rV\r\nHzWoYac334B78fifwnyq81uDbGh3ZSOrAdJEDYMQh6dPWlwwIn2qKpg0o3Dw\r\nc8LUc8f1ze16yrSQuWowY9iYypXn8CX+7ssjcwNCZMDcvTA/ME0ag2OPDXQS\r\n4glzc5Yjfum9f+Z1a9GQl8hakQjK6XyQ8ImjqKbpJFC3mlcNi6jz9ZUs3UmS\r\nNmRtJH23dbeFp+7yPy7dSQuxhaZiRIqwwQ6sYV1OadVqnZyWL1Jt2N2ZE6dv\r\nKUsXsIseHtRRMnD01yLRL9urgtzQtpiNOv0=\r\n=py5C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "8fd426f3e5d2a5a9d3ddc91ef1a6533cf3905f1b", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.5.5", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.6", "mocha": "^8.3.2", "sqlite": "^4.1.1", "sqlite3": "^5.0.8", "fs-extra": "^10.1.0", "nodemark": "^0.3.0", "prebuild": "^11.0.3", "cli-color": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.5.3_1652683428263_0.12090286417062868", "host": "s3://npm-registry-packages"}}, "7.6.0": {"name": "better-sqlite3", "version": "7.6.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.6.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "66444ba12985071b9070b2b16d10e586ae4ad581", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.6.0.tgz", "fileCount": 28, "integrity": "sha512-wYckL8S8RHP+KKNsZuJGZ7z/6FFmVgwd0U8jSv6t997C+EFR1yvi8p2WIpTb10jiV5rRA5VtMdgtAZFcAnK3Iw==", "signatures": [{"sig": "MEYCIQCOAWHKAeTC53/1lK2HIcqCr2UuD5fJ82TTpxBVmpMwtQIhAO8ERpVb7P8NnhkYP0aEbGxZX0T5ONwNM0v1ZxCinEUd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9411051, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiyRQhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoPQg/6AlMnOlpM476MP3QtJ2vkql+tIg5PCBCeo1RPIEPapE7OY00R\r\nslFfmUXcdOpHpGFjWJiz9AHZvRzqsKGSzU6HcxjTCDO4Gdka7pCocqnCWS94\r\nKLZEBXUafn3Qin/t5JmSbZ/oQ5wluZsZ1wJ/IMXhFV60heSxkom+LyahHMYA\r\n45l/gZySLKPOexaD5OgomezwQe5cbUM2XIqGmpyX1mrVx4MTTdGnYPHeRJk1\r\n5jCoYaX62/ItFbvM06wk8Uy6VSyY4TtTvLwyS6GWXLbKsWOWefXhcc9S5PxJ\r\n/ID2HC4AioMZvV++f5RQGZxkuD2zgS+0SJramn/ghIYR+PO5MYk2QMfUTCSZ\r\n+sfxUQmyAsxgpCJlCHiSjAjQdw3O3/5hHngrh7okwhJ8R4rfGEMnB0Fu1biF\r\np/1PW/5Fn0oQfuuiRvefX3NI//GiEjW9/4ReJqjsEhefO7qdsa+e6RoALgks\r\neYfWkcYgvEAgEPM49e8OQ9vwhm6dMVbRldaglj51MZf7aOCX0TZ1g6guI03z\r\njxKdScC2J6e0qxyg7lL3pb4ozW7ybTcOOXDvyovIy5t2ymHvUl5DSxFFCcWK\r\n1YkrNwAQngsVJ/xr5g9aIrjAKjxLXR2qDJ9guuuM8YokCBpC6D1q5qz2NCSx\r\n85YcunHLAxM1jiFEFQI5ab0RCmdJMsVRgGQ=\r\n=Nysl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "793c6b2f84a975651aba6e04f848795878858c6a", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.6", "mocha": "^8.3.2", "sqlite": "^4.1.1", "sqlite3": "^5.0.8", "fs-extra": "^10.1.0", "nodemark": "^0.3.0", "prebuild": "^11.0.3", "cli-color": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.6.0_1657345057764_0.06656642554148084", "host": "s3://npm-registry-packages"}}, "7.6.1": {"name": "better-sqlite3", "version": "7.6.1", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.6.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "bffc5515552ab950668f5e9f6c6d77463ab9f5e5", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.6.1.tgz", "fileCount": 28, "integrity": "sha512-kI2NV1qjKyO2I7iDKgrsbpYR7CxNVrObiGVxbrwYJRy6Sjn/4N6YwZmi0LP2kWZswSqeJlnnEThoYJ7eKCmqAg==", "signatures": [{"sig": "MEUCIQCxOvQ4XG608xj6q+KutFlYCn+dON1sj821Yx9Bu+BraAIgdGjo+J4KXY8UBiV5u8BNtlAO2LLYdOKY0+EjXwZn8FE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9413264, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiz15DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOdg/+L1yx+vjoL0QrHKlcC6z2kF6o2BDfTG4WlCVlmlfE0nUeYlKx\r\nTc9uZlF+i20iyue2rMkyUr+nUgfNdjnIxKfegjh+gOxM/6eT6T2j2SgluDnU\r\nepqR4yurZwFVjWkejIoloMyGG/ii1fWKKVSj5KP9fLtH1WarjlpC27REkUci\r\nKclwrH9uc9RvmJ8tHtsZzJOebA+gez7g6cmBS2uY4uoQbTOaMEyLzHkItMkU\r\nkSO1KDX9GOqu2mEUhNyB9H5RFVZYxfYpojXJgsOSjxdkR3rKc6R+ZRjeSqak\r\nX2osO781TIkfXgYFQnJwoThpD6YtyrHCDZGFRgxthirCpIAI3X7gNvPcBHfb\r\nn9EuNBQ5b2L/EJ9gkkraci1+6WpeqyKT2nMkGKdf7nw71aQu0n3KvtAzUY4+\r\nX5KM8XobHpVqJHc2TcZWXLESiyVaNTfNzVAd0DkfOvH23C5YItTNX/I9Jvpm\r\n28yzKozP/k2fcu9jjlfEyAybrtcuTziucn2bcd4MjMawvJJk52+QnRYUyCb+\r\nw4Dy83YYABfgAukaLWc3nuGY0vxERJ1zWbxvMfBJG6vaGRcAb9jEMhNQKoFF\r\nJDr+NxyOueRhEZwaTJXNAw5urq9PrWpxDeaEwyH3lj6YRPFk+7CTgJ6ELlL4\r\nmbiMeUhDkKWW00fbhdNBKgH0ceP1Gd1VrJM=\r\n=y8SM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "e7d1b668f2f1f40b19749f21636242176a052188", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.6", "mocha": "^8.3.2", "sqlite": "^4.1.1", "sqlite3": "^5.0.8", "fs-extra": "^10.1.0", "nodemark": "^0.3.0", "prebuild": "^11.0.3", "cli-color": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.6.1_1657757251620_0.843181406506301", "host": "s3://npm-registry-packages"}}, "7.6.2": {"name": "better-sqlite3", "version": "7.6.2", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@7.6.2", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "47cd8cad5b9573cace535f950ac321166bc31384", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-7.6.2.tgz", "fileCount": 28, "integrity": "sha512-S5zIU1Hink2AH4xPsN0W43T1/AJ5jrPh7Oy07ocuW/AKYYY02GWzz9NH0nbSMn/gw6fDZ5jZ1QsHt1BXAwJ6Lg==", "signatures": [{"sig": "MEUCID6rKZCvSSnQDsCAA+9FNUJCwRR87mWoqZXRQXbfKW4HAiEA4t4EfwpsHjRUn0Q3AsAp4CxiCaqGRgSZECU0pGoQIls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9413264, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi0fJtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPZBAAoUTL0gd8LI79zai7sfC0kJIG5akPzhSvjtHGyk966WBjBkvL\r\nWOFeNmVvCaqPy+1oKeXpAV9KPsEfK5hpRH4lUCbtwUne/1vLyRimTRxycC7b\r\nXZ/zwGokuAJ28+Pycc2buonItfJpcIMaqvjFsmb8O5y+Wk1ChxvbdsnIDmba\r\nuK1z8hhtC1q4kdUTM5y/Re+unXttz4FmIbMZxZaWWeuc/fb7QFMo6RyS6EGN\r\nfqaQJdrn8f/jvKkc4zRRjLi9d/sC+MVp4n3MKwntJQbjRABmi0m2XhrHwi4m\r\nCq6mBvr6ouFjyJVAwOPgtVE+sjysndkZLd4troNpVSJotajt78puQxGRsLeT\r\nmqVa4RcCGhsHqSyqvN7WAM2uoIU9XtH9EGkQajHgLzkclFR9PCgBwbsk7ukP\r\nnX4AjXLIwfKL6AnSY0xs6IeDmCiva58sSuJ8bW9YyS+GXTL+CXV0qHIPx+J8\r\nEpkfSEyPURZnrUU3tgXuievC2yZ6QCjFYaXP8NRbR6Nlvo3mKclxmkwODGil\r\ngO+GkZtxGF+yA9jpRayjnP719vTG7aJrdoh6N7x8OifEP4AD95Yxl4CACGa+\r\n0PSbKxwmAMAd4OvHmV8Jkk5k11dx4+m/tSVh7wdtLy4MhGUQkVKs+rE2Xytt\r\nGg25BO5kXnQQARwyvLh+yYZDJk6SW7a2p8k=\r\n=J3N9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "0c42307437140dbe5217a097739e418a7bd88f01", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || npm run build-release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.11.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.16.0", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.6", "mocha": "^8.3.2", "sqlite": "^4.1.1", "sqlite3": "^5.0.8", "fs-extra": "^10.1.0", "nodemark": "^0.3.0", "prebuild": "^11.0.4", "cli-color": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_7.6.2_1657926253416_0.23825528259540096", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "better-sqlite3", "version": "8.0.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@8.0.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "aa350edf5defccd726749b8139881e5918b098b6", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-8.0.0.tgz", "fileCount": 28, "integrity": "sha512-DhIPmhV+F3NBb9oGCNqNON8Cg4nP3/7NOwx412SL6JJUclYjAKmqNtbL6xBfG2RcG0uZWUS/TEHRy4AFLeq5Zg==", "signatures": [{"sig": "MEYCIQDllOESYYDk7gpvqYYpPoGycbyv26k0V57PsCn+uPIG9gIhAPFUYMn20JtpdPnbB90qbRghI6/fspreGh76794LyMol", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9480952, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjfCNfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSjRAAlBH4TiOh1AXMA4tSWqbGnN5imBZ3uUyE8XlinrqVPnXtrgdf\r\nm7Wp8cR/kXv4SjvjjPG2JJjeoW7QpZvZ7YTnf18AVmjBtyFN1fVS13zUKOWl\r\nsVmF9kX3RewOA17qDowiMd3Mj/G6K+cz9JLFDk0abJVuUaNYXjGRO++FBQ0p\r\nlRbaLX9B90iMAbegxk+IOvi0sEn1pe8uVtmtQGDPrNF585vqxXZnyrJTxAfX\r\necrFDCbJCGRCFKYpDozmjSAuu0X8acVbvFm3x8eLN65ByZ4xpAyDu2/zW/Ml\r\nn/y3RhgbnaN8pFwyeg1vyICaQF3eNmva8l47VKrs+EelxX5O4keg9b92qm92\r\n+tkr3Z0Idyq25Lzldcx7zFpkXkqs5GsWu0Q6bYahm9OteeDHz54zm/LMpc7f\r\nYGi0d8Dwb0vIMdPTfQLyeeRfuyWXug+06y3ARetheavAXndEbCHcpQm8V3wc\r\nxbWd9yRO66FKTur+CWklw2i/p4EtCGGIJww6/WDADvSua2JytuUEXj1YI1KT\r\nxwERgsSYdgv97qkfysZFCLZgq/enZSUwTbMhxwVZqpRsxyDdSTRU48ruBSnR\r\nEV1f69Lh+eTNLe67CZmcXgkPVtaB9VrGymHv8QcwNEvXI+UqhfMerRZu41/T\r\nEHxPLIrPkjv3Y0HuwaUXvBck8zstbTXo0c4=\r\n=ZU9B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "dc76c77326b83f5ee96b9bfa437e53269ee62f2a", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "overrides": {"prebuild": {"node-gyp": "$node-gyp"}}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.17.1", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.6", "mocha": "^8.3.2", "sqlite": "^4.1.1", "sqlite3": "^5.0.8", "fs-extra": "^10.1.0", "node-gyp": "8.4.1", "nodemark": "^0.3.0", "prebuild": "^11.0.4", "cli-color": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_8.0.0_1669079903205_0.9440921938163029", "host": "s3://npm-registry-packages"}}, "8.0.1": {"name": "better-sqlite3", "version": "8.0.1", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@8.0.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "3a596d21fbcefadf36f94e126c5cf24d5697d0b8", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-8.0.1.tgz", "fileCount": 28, "integrity": "sha512-JhTZjpyapA1icCEjIZB4TSSgkGdFgpWZA2Wszg7Cf4JwJwKQmbvuNnJBeR+EYG/Z29OXvR4G//Rbg31BW/Z7Yg==", "signatures": [{"sig": "MEUCIGqAFi+7EIuKczAYsOWokcYnNPSUSFOJfnnXV8EBRrQqAiEA+ehzslJpKViMYS11EzNlG3hnyzDiAZdkYooe11/7oEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9480952, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjiV5iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrnew//fTEsytrhlrNHhI42G8Vmp2mHAl0wruZ4llFHjpWAo0ASTMgR\r\nuR69b5vpR4acCQdR3TV8eHCzCvq86XhgNN9AVSMxlx00/VyzMbnRQsbXU/Hw\r\npLsPcTh8KUUQlEOPPnBrJfvfGnQ1r8wBw+T6my+RFUbmVVh07spUDWV1Oe3q\r\n2NZ1MarR6OAU8Nia0GcZaRTBAK5/gnrKXuMfutD0qc9cu+3AgbQPYJRb+Arq\r\nJyeQvNYDz9RtlVXxaP6c3lQUECQr7Q4cLipzsbcwB3syi2i9VLqkBR6n9gSK\r\n1ul4Ombqroh5p4sz4Tp0FmlDmphNRlZefmczQ515kUhpFkOzfQVAZ5wFa5f5\r\nOiB0OEoGGgTtHQ1Y6tMlaHyI92fqTgUaHDjr4JIWvT0oupGC1U2qtTMyNTu1\r\nxhHfg57Mlkl8TKNoV4ASrjAMvBM/h9w7DLItdsvQQ0MIjMBfj1QkMR+AIBxl\r\nnz0PFIQdjLA1s/uk0hZT+Gi3dfsJqh7xJilKAjCFfsW8CRrbiqj/92VoJ9pr\r\n2npYuUAyZY/x5498TUB/xLZ6MzOLpQNIWpfgGn6sB0TBlTKWZ6k9ndNK+PGg\r\nco5p5cLvvclYZc/2sif6qB0EnUG7Epd6a7C3nYu48pmqrrzpo3FgsMS0fw9h\r\n1vq8gaaowXnnFx765h7S1NL1nrmp8ZlGLno=\r\n=errO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2194095aa1183e9c21d28eafadeac0d4d4d42625", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "overrides": {"prebuild": {"node-gyp": "$node-gyp"}}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.15.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.17.1", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.6", "mocha": "^8.3.2", "sqlite": "^4.1.1", "sqlite3": "^5.0.8", "fs-extra": "^10.1.0", "node-gyp": "8.4.1", "nodemark": "^0.3.0", "prebuild": "^11.0.4", "cli-color": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_8.0.1_1669946977666_0.9106839928136299", "host": "s3://npm-registry-packages"}}, "8.1.0": {"name": "better-sqlite3", "version": "8.1.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@8.1.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "a0039c5dfdc04b733cac3c8dbe1b71f3e5fc62d3", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-8.1.0.tgz", "fileCount": 28, "integrity": "sha512-p1m09H+Oi8R9TPj810pdNswMFuVgRNgCJEWypp6jlkOgSwMIrNyuj3hW78xEuBRGok5RzeaUW8aBtTWF3l/TQA==", "signatures": [{"sig": "MEQCIFIdfhwkODg6BRwSHySMQNwZ2qVrWGZzY3G0rELfmBHqAiAOWTYFlHYSNDK+Ldk4dbnHCxp1+tFKHmUCaJHckm/qKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9484244, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4a0OACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpyvRAAoWA3wYYyQN5bvv0nixN8MJIuA/oKUuN5/iKoo/axXFrAjquh\r\nPzwD31x9TlzZFmBq/BnOdBCveV9IAHGd4YmFIXzUmDbFq4vtS6RpbGZvn6wb\r\nEZ3+YRIhIH+wolopbNMoZnafWAmYAsNHcNQss5o4oK+6BUa7ePK8EtIvTM//\r\n/ptbh933OU0NakcK+fDg3XuarIv4phIyZYIVY86fPjoWv+TZ2yozASLImVuT\r\njISMMSn+CbtS6xL7CHdufnlEJ5om1ny2f11d5ak6w/QQpVJrJdGbw3OH9szw\r\nfGo7+SjjhISzEKMb1cYCCjozVELhwIZxrCtMLg0u1hnPG4Ec+enMUoCOftbf\r\nyujLqJj0QcxUlMPaGmTog2zOqlyB0xsOjcmBemDTkjXVfiR9Xo86atC4RRes\r\nNrevM8XM9q39zQAlFv9+zBDdyYZjytxf9Yuwj9oyXt+NwV9t+zpXTIDZbIL9\r\nB4r1eAAbs7ZvpOYgoHXDA0A0i0zgO5nL2tFzjkK1Y2XwD6CAa1NUp8KqM0rZ\r\n/8dlEnhGZsIBaphpWbmyR+Jz1RaohUfu8sEHg4x7Uf1WdBzLuHhHtbp7D+Jr\r\ne3xcwu8qnqpX0Oq5ZhAnARd4SXXc1Ei9ReqUrh1eBduKAAw3xmj8ZoTQqgwT\r\n2JbCtT+PK9eMETTIrFw1PTODu7VJ1gRKA/M=\r\n=oKXK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "17ed0689217aafee4c78ce42dec559c716cf4ead", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "overrides": {"prebuild": {"node-gyp": "$node-gyp"}}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.6", "mocha": "^10.2.0", "sqlite": "^4.1.1", "sqlite3": "^5.0.8", "fs-extra": "^10.1.0", "node-gyp": "8.4.1", "nodemark": "^0.3.0", "prebuild": "^11.0.4", "cli-color": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_8.1.0_1675734285963_0.9046700123718878", "host": "s3://npm-registry-packages"}}, "8.2.0": {"name": "better-sqlite3", "version": "8.2.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@8.2.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "4ef6185b88992723de7e00cfa67585ac59f320bd", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-8.2.0.tgz", "fileCount": 28, "integrity": "sha512-8eTzxGk9535SB3oSNu0tQ6I4ZffjVCBUjKHN9QeeIFtphBX0sEd0NxAuglBNR9TO5ThnxBB7GqzfcYo9kjadJQ==", "signatures": [{"sig": "MEUCIQDougnR8CzvhLK0oqSGG+xJKEsbMPc3EQThPRlhzn3xlgIgc+QCSXjkim83vt+H13bc1ju7HIZ9mgUPOrbuvXYTmlg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9540960, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCQ6ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpw0BAAi+Ef3f0IvNNVzj6YnC0j8U02NVNNUCbCb9ccJkNg59dxGU12\r\nT/bHpBy/HBurzAszpBJ+Vyhs/x+uJynenz7Ykd8OYauGZPTMMTEEuQ+8vlwN\r\ndnG9xs/iBJXms+iGo+JCrjkFDj3dCypmlUioUflT8iA6crRTatwfc8MzuREP\r\nYdP0pWaGXqKBWHNgl8nvD+YRAdXeTRQjKR49hh+Tywy2nUI5W5PEH1odVNox\r\n4JfSm0KHWIO5Ap7fe6Wna+VT4moxOaw3mlY8orNBRouPfhHn3EYlUkTrjS8w\r\ncR8W/m+GRlvmGikTmuHx9cRG+sVXYdhC4e/XvBwMVAxQnoJvBm4ynO+RZzO9\r\n19VJn3+ZFzSWbMOJuuJaoUkE5Et8LQRIVBe+fqEdDtYpFy0Q0iXgd7M0vxOG\r\n2imDiVkaJdaMpmflkLkofn2rHxAudSmUOHxhtNtqt4nh6IX/fx6lqxjgDas+\r\nXlxeRePfKEs6akmcFLHkwoqCld6CDSzAOPyc7cSuiOc+1Op0OkzttF9GaUPy\r\n13xF7+4FOhkANXCrU/Jtxm507wiQnBfAw0oVhpcxRCzRaSOIcwTQopF5jn8Z\r\nE/m2Bg6qJRHM545nL1f+lP9yumntT3Pj/WmMT4N7c9e7wylNk5m0lyAEVa8K\r\n1hWIBj7bUbXnHWoUy71vIJnByfb56pF+E+w=\r\n=R5ND\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "37b7714ace3792a9122ec7e67ceda5a28b1d00a3", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "overrides": {"prebuild": {"node-gyp": "$node-gyp"}}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.3", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.19.1", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.6", "mocha": "^10.2.0", "sqlite": "^4.1.1", "sqlite3": "^5.0.8", "fs-extra": "^10.1.0", "node-gyp": "8.4.1", "nodemark": "^0.3.0", "prebuild": "^11.0.4", "cli-color": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_8.2.0_1678315161011_0.9255934289875494", "host": "s3://npm-registry-packages"}}, "8.3.0": {"name": "better-sqlite3", "version": "8.3.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@8.3.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "3873ddfd9f2af90b628657e8ff221786c93d1984", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-8.3.0.tgz", "fileCount": 28, "integrity": "sha512-JTmvBZL/JLTc+3Msbvq6gK6elbU9/wVMqiudplHrVJpr7sVMR9KJrNhZAbW+RhXKlpMcuEhYkdcHa3TXKNXQ1w==", "signatures": [{"sig": "MEQCIHI+X3s/1Qi+c5vbeNaNuAAh6aD0yElINxBemeFA+tLuAiA+iA9RaBm//znBpPpzVBYkLX/xcJnMJG/yu7J3Dy6fng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9544306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMcmWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrUUA/+I879CS6BVwAv45p4cK3CcoklyYitkebwyhnBimJhSPwCWS2w\r\nClkASCLBcMW7rWkfdJ4sL2mY9e042y+GNilznBGruc2kducYsDsPHiXBjAxd\r\nPO5blc9Ga+Cvv+y2V32vWkhY0Ed7r5WzXXMM1hds1iQjQZymp19agt60V1Bq\r\nQwuz9O6hCY04HjbMGm4grV8YdumIINoh5TyQ5R70V589mTg+GFQtkJDO0TnQ\r\nyudEJRCug7x9rdW8dxWhWX/gZrPZOUGG2Xj/+1OQlthbUwzRkpd7jIk7vyad\r\nmKinY+Vvgv9v0NANlztjTy5IOWOLiM8sbsK+D9/00yTnxfIN0UjbG1ooXF/z\r\nu4wSlDELliG0wQJmE9zNEPd7De0lHF6p72zb9XLFx/jpfMUICVysKCJPJ0HS\r\nnLMerwsZ5SSM4UZJp74OM7fy0rlPOxub96Nn8hcUioPi/5izCQziqGBbYR5R\r\nWs8HY50GLkXbzGfcLwMMIWHwDxrN38eBTJEL5vua9K1VVMhe3hTqKNbGJfc6\r\nAJFO+oMDluSNWhd2jCOdhZbBeU3ngb6tMloyzhKkvud50CBP49P2xW4KtgJU\r\n5mRZl+jxZM04hUquPHw+UXhpIfGfYw269i6Yod7G/rSoDQHrQEXpdEdQMxmX\r\n2+cfC3C3xu45H1cPjaVIFjn9d47iBWikA7w=\r\n=57pn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f190fefc9959cf6806b0bec5f3e57df508e1d573", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "overrides": {"prebuild": {"node-gyp": "$node-gyp"}}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.20.0", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.6", "mocha": "^10.2.0", "sqlite": "^4.1.1", "sqlite3": "^5.0.8", "fs-extra": "^10.1.0", "node-gyp": "8.4.1", "nodemark": "^0.3.0", "prebuild": "^11.0.4", "cli-color": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_8.3.0_1680984470260_0.7247829848916414", "host": "s3://npm-registry-packages"}}, "8.4.0": {"name": "better-sqlite3", "version": "8.4.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@8.4.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "aa27bbc6bb42bb438fc55c88b146fcfe5978fa76", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-8.4.0.tgz", "fileCount": 28, "integrity": "sha512-NmsNW1CQvqMszu/CFAJ3pLct6NEFlNfuGM6vw72KHkjOD1UDnL96XNN1BMQc1hiHo8vE2GbOWQYIpZ+YM5wrZw==", "signatures": [{"sig": "MEUCIQDkNBAr1Kh8bTfJiTK8f6F1NC/EW0DFqeDqUAz/h5i8iwIgdkC5IkFa27We+j653zvjmw6K69nEIcZt8tn6eqhYtNM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9624978}, "main": "lib/index.js", "gitHead": "e00d110135a85fd05aa3ac2bb1c537ccd4903f4e", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "overrides": {"prebuild": {"node-gyp": "$node-gyp"}}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.20.0", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.6", "mocha": "^10.2.0", "sqlite": "^4.1.1", "sqlite3": "^5.0.8", "fs-extra": "^10.1.0", "node-gyp": "8.4.1", "nodemark": "^0.3.0", "prebuild": "^11.0.4", "cli-color": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_8.4.0_1684609860819_0.4966766303859713", "host": "s3://npm-registry-packages"}}, "8.5.0": {"name": "better-sqlite3", "version": "8.5.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@8.5.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "b13d12d0e477be69191a39628fc8b723ee1c79d4", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-8.5.0.tgz", "fileCount": 28, "integrity": "sha512-vbPcv/Hx5WYdyNg/NbcfyaBZyv9s/NVbxb7yCeC5Bq1pVocNxeL2tZmSu3Rlm4IEOTjYdGyzWQgyx0OSdORBzw==", "signatures": [{"sig": "MEYCIQC8LwcJr3upce+FxKDRhOLZc26G0QzARysp/14rrNtLSQIhANa/aFQpwV9412EtKETO2j5Xqx4jsVXk/qnoAxRBTwy7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9624978}, "main": "lib/index.js", "gitHead": "38554b54756338d8a331f7202724e8a46afa1dba", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "overrides": {"prebuild": {"node-gyp": "$node-gyp"}}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.20.1", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.6", "mocha": "^10.2.0", "sqlite": "^4.1.1", "sqlite3": "^5.0.8", "fs-extra": "^10.1.0", "node-gyp": "8.4.1", "nodemark": "^0.3.0", "prebuild": "^11.0.4", "cli-color": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_8.5.0_1689785516095_0.4200458129653999", "host": "s3://npm-registry-packages"}}, "8.5.1": {"name": "better-sqlite3", "version": "8.5.1", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@8.5.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "984f7645303afa76289569804ee56e211d8ffa66", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-8.5.1.tgz", "fileCount": 28, "integrity": "sha512-aDfC67xfll6bugnOqRJhdUWioQZnkhLkrwZ+oo6yZbNMtyktbwkDO4SfBcCVWbm4BlsCjCNTJchlHaBt+vB4Iw==", "signatures": [{"sig": "MEUCIGUGe0L9hEjU+bU7YXJnyJzVVyRYlKBOld0aB8WiqJw+AiEA/5vRJ30+qqrhV1puKcs0HgzJODeP87TqeLarH4yrq+8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9624978}, "main": "lib/index.js", "gitHead": "f97ec71639aa3554f99fc3620c8e89250ab8680d", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "overrides": {"prebuild": {"node-gyp": "$node-gyp"}}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.20.1", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.6", "mocha": "^10.2.0", "sqlite": "^4.1.1", "sqlite3": "^5.0.8", "fs-extra": "^10.1.0", "node-gyp": "8.4.1", "nodemark": "^0.3.0", "prebuild": "^11.0.4", "cli-color": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_8.5.1_1692285514178_0.03647327409410428", "host": "s3://npm-registry-packages"}}, "8.5.2": {"name": "better-sqlite3", "version": "8.5.2", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@8.5.2", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "a1c13e4361125255e39302e8b569a6568c3291e3", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-8.5.2.tgz", "fileCount": 28, "integrity": "sha512-w/EZ/jwuZF+/47mAVC2+rhR2X/gwkZ+fd1pbX7Y90D5NRaRzDQcxrHY10t6ijGiYIonCVsBSF5v1cay07bP5sg==", "signatures": [{"sig": "MEUCIHZwJ2iXVgdbWxYnVzjRFNLNY1sCeJqpxqtxFl8vs0ygAiEAlidWU6RT5dGuba8h57Q8J4ozl51JtEFkPc8M+1k+Wfw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9624978}, "main": "lib/index.js", "gitHead": "5a0ee2fdfcc85a6f00ed77f79e0578834aa37944", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "overrides": {"prebuild": {"node-gyp": "$node-gyp"}}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.6", "mocha": "^10.2.0", "sqlite": "^4.1.1", "sqlite3": "^5.0.8", "fs-extra": "^10.1.0", "node-gyp": "8.4.1", "nodemark": "^0.3.0", "prebuild": "^11.0.4", "cli-color": "^2.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_8.5.2_1692889282475_0.9789034371922358", "host": "s3://npm-registry-packages"}}, "8.6.0": {"name": "better-sqlite3", "version": "8.6.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@8.6.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "a20717993742f816158f17e2cccd88a979e77e58", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-8.6.0.tgz", "fileCount": 28, "integrity": "sha512-jwAudeiTMTSyby+/SfbHDebShbmC2MCH8mU2+DXi0WJfv13ypEJm47cd3kljmy/H130CazEvkf2Li//ewcMJ1g==", "signatures": [{"sig": "MEUCIFB1iKbwD44Cl5/qgsqfvA+87Pe79MC9upEofXLm/JtoAiEAnS+UtU/QuUYRdnkM5K1ju9hLwXtmNnyLM+Oj9leQKRQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9731207}, "main": "lib/index.js", "gitHead": "ae23e690b02c00d075d543c66ae7e26c98c46f74", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "overrides": {"prebuild": {"node-gyp": "$node-gyp"}}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "node-gyp": "9.4.0", "nodemark": "^0.3.0", "prebuild": "^11.0.4", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_8.6.0_1693683586839_0.15497807476562686", "host": "s3://npm-registry-packages"}}, "8.7.0": {"name": "better-sqlite3", "version": "8.7.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@8.7.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "bcc341856187b1d110a8a47234fa89c48c8ef538", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-8.7.0.tgz", "fileCount": 28, "integrity": "sha512-99jZU4le+f3G6aIl6PmmV0cxUIWqKieHxsiF7G34CVFiE+/UabpYqkU0NJIkY/96mQKikHeBjtR27vFfs5JpEw==", "signatures": [{"sig": "MEUCIDznuSu9l5SpbF/lwi0EOY6Uap9u336uzfa3fl3rDO1CAiEA20oGGblSOrDKwRfbeaBxR6lY+gHbZjhOMdpZ91w5RV4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9731467}, "main": "lib/index.js", "gitHead": "e3f916d803dd50e7bc11142136ca760e96210f74", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "overrides": {"prebuild": {"node-gyp": "$node-gyp"}}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "node-gyp": "9.4.0", "nodemark": "^0.3.0", "prebuild": "^11.0.4", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_8.7.0_1696107605943_0.3658578911406878", "host": "s3://npm-registry-packages"}}, "9.0.0": {"name": "better-sqlite3", "version": "9.0.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@9.0.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "bca6026fa1e9e5af62bfef448a7d8402d4549958", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-9.0.0.tgz", "fileCount": 28, "integrity": "sha512-lDxQ9qg/XuUHZG6xzrQaMHkNWl37t35/LPB/VJGV8DdScSuGFNfFSqgscXEd8UIuyk/d9wU8iaMxQa4If5Wqog==", "signatures": [{"sig": "MEUCIC3aF7lycllMlqrNuR5qG6p3N/KEeKnXUXiUJ12JEYbcAiEAhFCehWtdYxUCK7aHMubNN/w58TRoaKX6H9xXjGOQp5M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9731692}, "main": "lib/index.js", "gitHead": "007d43e229190618884a9f976909c0b14a17d82c", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^12.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_9.0.0_1696974455296_0.29588689917955", "host": "s3://npm-registry-packages"}}, "9.1.0": {"name": "better-sqlite3", "version": "9.1.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@9.1.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "8ea12348a6aed7e916ea613e720300ab638b63f7", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-9.1.0.tgz", "fileCount": 28, "integrity": "sha512-h6c5myXUp5GNkr1z4HmM7ySayBLWtIoe9fRWPl2f+z9ZMZGYTo7aw2QON3i/sd+tWDAzD+roPBWlDng5AM/osw==", "signatures": [{"sig": "MEUCIQDUmvQUn0WE28/NdGGbxijQ/UIQxsmUJBIhwJ6RHSEHsgIgeU1/V9j1K/OKozCw+bT/XdHbFSFN3VaBUi8lmaXFTlc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9801936}, "main": "lib/index.js", "gitHead": "20e96c4c14bb0ab5f6a5e5819595a681432938f9", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^12.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_9.1.0_1699476719362_0.5934657828635017", "host": "s3://npm-registry-packages"}}, "9.1.1": {"name": "better-sqlite3", "version": "9.1.1", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@9.1.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "f139b180a08ed396e660a0601a46ceefd78b832c", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-9.1.1.tgz", "fileCount": 28, "integrity": "sha512-FhW7bS7cXwkB2SFnPJrSGPmQerVSCzwBgmQ1cIRcYKxLsyiKjljzCbyEqqhYXo5TTBqt5BISiBj2YE2Sy2ynaA==", "signatures": [{"sig": "MEQCIDf/6Uqt8ODn0tZkOE6tkpxYU28D0i97CggITbbdXiAGAiABMDXVpYwll10D4i7ck7R/jrQBNB+Fn0nSGx102wZ8Lw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9801936}, "main": "lib/index.js", "gitHead": "6108911f70bb47af2cfc877706d8ce2e9bcca3fe", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^12.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_9.1.1_1699504441858_0.7186817420781999", "host": "s3://npm-registry-packages"}}, "9.2.0": {"name": "better-sqlite3", "version": "9.2.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@9.2.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "0c7d54ccd22c2a77b1efc57bf100d71e46c41511", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-9.2.0.tgz", "fileCount": 28, "integrity": "sha512-MEm49nfxCU6Yn5rTyXJhnTmjuom0YFrU76/7AC+7PMcftAwIVJ2VtBF6HSPRwQ/WWXw/sSbnPRPUSZ6vMXMniQ==", "signatures": [{"sig": "MEYCIQDNoNCKavll7QBkTicHsGlJUn7O1w7c3b06+RJdYLr3uAIhAMVWsJuS02m8JHvbv5jOUaVEZp9Ncuh4AiehepIkPjw/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9810420}, "main": "lib/index.js", "gitHead": "746d8e30c669387a896dbfff06cef07f5c071373", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^12.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_9.2.0_1701479885900_0.6733351088142063", "host": "s3://npm-registry-packages"}}, "9.2.1": {"name": "better-sqlite3", "version": "9.2.1", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@9.2.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "a84d89ca520ce6461e81b7ef6b25814c60f028cc", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-9.2.1.tgz", "fileCount": 28, "integrity": "sha512-X+ptcOFb1PFn/bZ2N5qyvc4ba/474FN1Xi5nC/dnMLn2CY1VopVmEJcaHF4cGuzJMnp12roIiz2Uf096TlrpHg==", "signatures": [{"sig": "MEUCIBItEMFag0SptPwmiHiD2cSWOsszNDCaf02kyu7ZXXnuAiEAvPyiBmLbOycb+AqalTlQMs3+NL5VgaxyBH3/nQ4/Rq4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9810420}, "main": "lib/index.js", "gitHead": "2c79043e7615e8672e32a949dcc68f79fe52f7fe", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^12.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_9.2.1_1701739048913_0.34382409816737725", "host": "s3://npm-registry-packages"}}, "9.2.2": {"name": "better-sqlite3", "version": "9.2.2", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@9.2.2", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "3ce1ed24f327ed8c9b0c39b825cdc2341aeb249f", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-9.2.2.tgz", "fileCount": 28, "integrity": "sha512-qwjWB46il0lsDkeB4rSRI96HyDQr8sxeu1MkBVLMrwusq1KRu4Bpt1TMI+8zIJkDUtZ3umjAkaEjIlokZKWCQw==", "signatures": [{"sig": "MEYCIQCci9fYX2FLITjKUmN40OHOF82Vubnpm0ksV0q3CEsGDwIhAMaARI/Oh8f0fe0cQXAlm882pKFZ3GZXSuLzwxdeGDu8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9810420}, "main": "lib/index.js", "gitHead": "2babc216eb8240f145219cd290783d54500abfaa", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^12.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_9.2.2_1701745910311_0.1499421216041461", "host": "s3://npm-registry-packages"}}, "9.3.0": {"name": "better-sqlite3", "version": "9.3.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@9.3.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "2a8aaad65fa0210a4df5e8a0bcbc9156f6138d56", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-9.3.0.tgz", "fileCount": 28, "integrity": "sha512-ww73jVpQhRRdS9uMr761ixlkl4bWoXi8hMQlBGhoN6vPNlUHpIsNmw4pKN6kjknlt/wopdvXHvLk1W75BI+n0Q==", "signatures": [{"sig": "MEQCIGKImipSG8t+saZC7KmnwPCXmeDPiiEhplsdF7nWK5P9AiBAerVnLU6BHj/gIj1k3oBLfH9iN1fbJF+PZ7Pl5DHInA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9912641}, "main": "lib/index.js", "gitHead": "bd55c76c1520c7796aa9d904fe65b3fb4fe7aac0", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^12.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_9.3.0_1705451064113_0.943659212848958", "host": "s3://npm-registry-packages"}}, "9.4.0": {"name": "better-sqlite3", "version": "9.4.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@9.4.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "f3e70ceab8e5950dd45516977c0ff7cf167f8455", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-9.4.0.tgz", "fileCount": 28, "integrity": "sha512-5kynxekMxSjCMiFyUBLHggFcJkCmiZi6fUkiGz/B5GZOvdRWQJD0klqCx5/Y+bm2AKP7I/DHbSFx26AxjruWNg==", "signatures": [{"sig": "MEQCIFihJLnUuHgfXnmwZctbNLxZCtK8wPH/0GgjwBqG+VOSAiAqTbvFmECR6b1LTDgEMzw1vQvrf4NzqHgb9s4Ct5X5fw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9914804}, "main": "lib/index.js", "gitHead": "543c0f5c706088f82a4d5b5ac5847de6cf8a43fc", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^12.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_9.4.0_1706909499163_0.9919604441610528", "host": "s3://npm-registry-packages"}}, "9.4.1": {"name": "better-sqlite3", "version": "9.4.1", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@9.4.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "006bc6a899a69166c5fe89c9cff64509295d12b2", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-9.4.1.tgz", "fileCount": 28, "integrity": "sha512-QpqiQeMI4WkE+dQ68zTMX5OzlPGc7lXIDP1iKUt4Omt9PdaVgzKYxHIJRIzt1E+RUBQoFmkip/IbvzyrxehAIg==", "signatures": [{"sig": "MEUCIF22ug0kyOtwgMK0vut60vIXnqPzMnzqV1YZgdyyJG8pAiEAs6FVvhBCTN25UVNogFuy+4RXLHlsR2l10Q7JT9tin9M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9914804}, "main": "lib/index.js", "gitHead": "a36b8e47c3ba8b57067dc4d49b785ae398481c79", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^12.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_9.4.1_1707609194035_0.3975879027168623", "host": "s3://npm-registry-packages"}}, "9.4.2": {"name": "better-sqlite3", "version": "9.4.2", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@9.4.2", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "d16f07a62d61486ef8bd1a8b71bff54c8f700381", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-9.4.2.tgz", "fileCount": 28, "integrity": "sha512-7chehyKCTIbVxRhhdWhzCrbrrQZ/cUCBSrxj37fk+SWwWdmQn8wdMXOeO4Ulsf81/N4/c2WBakStTzBUaaQPew==", "signatures": [{"sig": "MEUCIGmzcM9D6jkXVC+8UC1t4rOYfvcdovk8E0hZU8KtTVl+AiEAyulP9NCP49Ho16pVYxGwfEB7x15YizPywWhdu0/bqCA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9914804}, "main": "lib/index.js", "gitHead": "a28bf42b7481d94f8fd5216e99bc5ff772b75b1b", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^12.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_9.4.2_1708548271646_0.37472753645671886", "host": "s3://npm-registry-packages"}}, "9.4.3": {"name": "better-sqlite3", "version": "9.4.3", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@9.4.3", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "7df39ba3273fbb7c0561cf913572547142868cc4", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-9.4.3.tgz", "fileCount": 28, "integrity": "sha512-ud0bTmD9O3uWJGuXDltyj3R47Nz0OHX8iqPOT5PMspGqlu/qQFn+5S2eFBUCrySpavTjFXbi4EgrfVvPAHlImw==", "signatures": [{"sig": "MEQCIFIOdZRPoxZfpQqOA5GVL+EQu4WEjya42RnWbTi7so3uAiA9ow+lTjl+qLIbESJkcl4eip8ya96sc3oAR8CyriaOGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9914804}, "main": "lib/index.js", "gitHead": "b35e08901c9ec3cc58856814dcb25e9dbfade109", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^12.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_9.4.3_1708563073755_0.5763090964893027", "host": "s3://npm-registry-packages"}}, "9.4.4": {"name": "better-sqlite3", "version": "9.4.4", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@9.4.4", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "e7cb1ea334b78a6299e1202c8635c84e6749c7f5", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-9.4.4.tgz", "fileCount": 28, "integrity": "sha512-h/gmKmmcTBHg9kDE05ZfocXyfQ7UzPlLVwHVD3yCZ5iXdj3x0hK5x2VTm4BRavBqxTGBOsmYcjOOHHMxuM0BVw==", "signatures": [{"sig": "MEUCIQCXXckivXxTKb5PsdMoIBhJhQKjWL/nCbRcC2q5jIjPqgIgDm0cgn09m7dopZpocv0uBSakvg3D8mtosbE0/YnVlfg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9914800}, "main": "lib/index.js", "gitHead": "080f863bfa853f87f1488a17dbee58f9265407c6", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_9.4.4_1712120690598_0.712345154626175", "host": "s3://npm-registry-packages"}}, "9.4.5": {"name": "better-sqlite3", "version": "9.4.5", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@9.4.5", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "1d3422443a9924637cb06cc3ccc941b2ae932c65", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-9.4.5.tgz", "fileCount": 28, "integrity": "sha512-uFVyoyZR9BNcjSca+cp3MWCv6upAv+tbMC4SWM51NIMhoQOm4tjIkyxFO/ZsYdGAF61WJBgdzyJcz4OokJi0gQ==", "signatures": [{"sig": "MEQCIHHvztd85fxFhK8+vi5N/gvWLvWKBnkWievAaCnKq2tcAiAh+g+Sp5vZbge5imPDmWjXRUfJAq2mAc3aHhumOU2E0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9914800}, "main": "lib/index.js", "gitHead": "e09670c5f6ec4416be6d7d168d08116fbd6f2c81", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_9.4.5_1712184221906_0.45528116254964135", "host": "s3://npm-registry-packages"}}, "9.5.0": {"name": "better-sqlite3", "version": "9.5.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@9.5.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "0e10766cfb7f9b8916be3ab95ad9d5bcc4e6e6fd", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-9.5.0.tgz", "fileCount": 28, "integrity": "sha512-01qVcM4gPNwE+PX7ARNiHINwzVuD6nx0gdldaAAcu+MrzyIAukQ31ZDKEpzRO/CNA9sHpxoTZ8rdjoyAin4dyg==", "signatures": [{"sig": "MEUCIQDTquGrmz2ys5gpYVUtASvW6E9wpwL9ioghHun9Rd6qYgIgcp4xEXUI9fliXIw5XjkEqAHICWxUw4FVayFCLu5o/wM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9919479}, "main": "lib/index.js", "gitHead": "67d69e5b3dc67539d42a2b3a06492246ff0b68fa", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_9.5.0_1712715466024_0.6885214666533022", "host": "s3://npm-registry-packages"}}, "9.6.0": {"name": "better-sqlite3", "version": "9.6.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@9.6.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "b01e58ba7c48abcdc0383b8301206ee2ab81d271", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-9.6.0.tgz", "fileCount": 28, "integrity": "sha512-yR5HATnqeYNVnkaUTf4bOP2dJSnyhP4puJN/QPRyx4YkBEEUxib422n2XzPqDEHjQQqazoYoADdAm5vE15+dAQ==", "signatures": [{"sig": "MEQCIFRkCzdKglEeMmoOAC0IFxhDd03/PZZ41BWOe1HgFnEBAiActu2BB2Nuay36irmaUj5RnZd3dp9WJqlcp1cCUbePRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9925311}, "main": "lib/index.js", "gitHead": "cfc69bd0613c27b0c4f8688b0c83fbdf05fdfdb6", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "16.20.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_9.6.0_1714107359560_0.7968393336386634", "host": "s3://npm-registry-packages"}}, "10.0.0": {"name": "better-sqlite3", "version": "10.0.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@10.0.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "ac1ce4a71ab7c338f48cc877bc0569d3eca2cbd2", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-10.0.0.tgz", "fileCount": 28, "integrity": "sha512-rOz0JY8bt9oMgrFssP7GnvA5R3yln73y/NizzWqy3WlFth8Ux8+g4r/N9fjX97nn4X1YX6MTER2doNpTu5pqiA==", "signatures": [{"sig": "MEQCIC0cMSqPDsWCUbwWrk0ZSeqc2qJt5Tnw2C630YSBpRgcAiB09N4sF2OslKfhkOi73CtEsq0FP9qbhkiPKCOt78qj+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9925312}, "main": "lib/index.js", "gitHead": "a86a92d7bcc335c243bf95c8b2cae9d8d28b6e56", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "18.20.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_10.0.0_1715488838925_0.2962853856402443", "host": "s3://npm-registry-packages"}}, "10.1.0": {"name": "better-sqlite3", "version": "10.1.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@10.1.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "8dc07e496fc014a7cd2211f79e591f6ba92838e8", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-10.1.0.tgz", "fileCount": 28, "integrity": "sha512-hqpHJaCfKEZFaAWdMh6crdzRWyzQzfP6Ih8TYI0vFn01a6ZTDSbJIMXN+6AMBaBOh99DzUy8l3PsV9R3qnJDng==", "signatures": [{"sig": "MEUCIFCtJAh8G+8TKGicSHNEWvUzuEdQIyy+5fgH2EjFhTwNAiEAttCxq9hIjSdSCgUiWwa/WZzqb64UhP7A45iUBVNECJk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9988859}, "main": "lib/index.js", "gitHead": "5aa855e26213b5473f7ebfa8c3833c9cd06b45f9", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "18.20.3", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_10.1.0_1717090955524_0.0175091673215908", "host": "s3://npm-registry-packages"}}, "11.0.0": {"name": "better-sqlite3", "version": "11.0.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@11.0.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "12083acfe0ded6abdba908ed73520f2003e3ea0e", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-11.0.0.tgz", "fileCount": 28, "integrity": "sha512-1NnNhmT3EZTsKtofJlMox1jkMxdedILury74PwUbQBjWgo4tL4kf7uTAjU55mgQwjdzqakSTjkf+E1imrFwjnA==", "signatures": [{"sig": "MEQCIF1WDX268wCzh41wEV8zCyndjRX31ITDGEuDupLYJIP9AiBRvynzcuNcHWz3YL6wvFFnLKhwIEFY12nh74iDBZXC4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9988859}, "main": "lib/index.js", "gitHead": "6acc3fcebe469969aa29319714b187a53ada0934", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "18.20.3", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_11.0.0_1717118458607_0.07330883531820609", "host": "s3://npm-registry-packages"}}, "11.1.1": {"name": "better-sqlite3", "version": "11.1.1", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@11.1.1", "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "dfca2ca6fc7c2d91fd1a275d0c41ddd65ee7ad39", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-11.1.1.tgz", "fileCount": 28, "integrity": "sha512-bAlQQb7gwCgxNpDYafK0O4AaIOiTwA7srfqRtBbw0Nsiq6P+qxEYGl3hLw+9C5jX2FVjKW7oxkSouxlJ+3VX8A==", "signatures": [{"sig": "MEUCIQDBU5i+ecmyYl58j3aM+71JRV6LNaWgxcN1aAP50cnAagIgKgWGNWtIVOhjlMI5+jXzuXo3X1m5oUMiknhPfv7AdRc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9988370}, "main": "lib/index.js", "gitHead": "309cb378056267e5854ef6fd283f9d2ea733abaf", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "18.20.3", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_11.1.1_1719510191381_0.8698674376578528", "host": "s3://npm-registry-packages"}}, "11.1.2": {"name": "better-sqlite3", "version": "11.1.2", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@11.1.2", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "6c9d064c9f1ff2a7f507477648ca0ba67bf564a3", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-11.1.2.tgz", "fileCount": 28, "integrity": "sha512-gujtFwavWU4MSPT+h9B+4pkvZdyOUkH54zgLdIrMmmmd4ZqiBIrRNBzNzYVFO417xo882uP5HBu4GjOfaSrIQw==", "signatures": [{"sig": "MEQCIFrhGRIqBQAlGs35B1NsnkZ8lexV5zRrqQB3pzrOn7stAiA69VOu/cychui0CBvjtrbQRfX9SmEmRze8Pyd0sfCD4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9988370}, "main": "lib/index.js", "gitHead": "254b8e93d78b1b03c9a2c777f4d304a0ea1530c6", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "18.20.3", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_11.1.2_1719938683696_0.11639350137989113", "host": "s3://npm-registry-packages"}}, "11.2.0": {"name": "better-sqlite3", "version": "11.2.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@11.2.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "16d3df47121d1980f6a9a9ea2f027bf1c89b4e7d", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-11.2.0.tgz", "fileCount": 28, "integrity": "sha512-3Dz8d4CoIf0aa8hLnGRVoLicHEp6Js5LRxXHNmOaUhOM8WLbnNkGdzc2soEwR3YFap49s0k4u5JFNaRbW2BYQw==", "signatures": [{"sig": "MEQCIGwhcEM0AyKwGNhQ9yXyGgblKUdNG3QZNO+kRrEr5+UXAiASrNQp4/Nr7KcL43caAfkNDDmdF8Izyb23IN4FkcNkeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9991641}, "main": "lib/index.js", "gitHead": "c3c239317aa1be61c04b270baac2a688ca6dd130", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "18.20.4", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_11.2.0_1724221823686_0.5287978047203763", "host": "s3://npm-registry-packages"}}, "11.2.1": {"name": "better-sqlite3", "version": "11.2.1", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@11.2.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "3c6b8a8e2e12444d380e811796b59c8aba012e03", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-11.2.1.tgz", "fileCount": 28, "integrity": "sha512-Xbt1d68wQnUuFIEVsbt6V+RG30zwgbtCGQ4QOcXVrOH0FE4eHk64FWZ9NUfRHS4/x1PXqwz/+KOrnXD7f0WieA==", "signatures": [{"sig": "MEYCIQD9Jn2ztAcQHY9KK2S2Hucw9IqF0KBIxeSB9KZstUNl3AIhAMWH/BxbTYJx6mcdO9byfZIhqWQemkwkw4wvSERHXUZG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9991641}, "main": "lib/index.js", "gitHead": "7896456a42a4c3dfd878fe4abc68cd67603f6d98", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "18.20.4", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_11.2.1_1724253428533_0.07230974345877894", "host": "s3://npm-registry-packages"}}, "11.3.0": {"name": "better-sqlite3", "version": "11.3.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@11.3.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "f10b32ddff665c33176d148e707bd1e57dfd0284", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-11.3.0.tgz", "fileCount": 29, "integrity": "sha512-iHt9j8NPYF3oKCNOO5ZI4JwThjt3Z6J6XrcwG85VNMVzv1ByqrHWv5VILEbCMFWDsoHhXvQ7oC8vgRXFAKgl9w==", "signatures": [{"sig": "MEYCIQD5ihn+KXOvLlDZ8YI3rmM2w8n03ClxR10aFFnH8kHZdwIhAIiFhs616D1hSOvhT8uSi3yyEM2G8IvVKeTDBy3zAxkU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9993211}, "main": "lib/index.js", "gitHead": "f01e0e42da4b8ba509961e772132e69ef5aa11c0", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "18.20.4", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_11.3.0_1725943641130_0.0777074863765852", "host": "s3://npm-registry-packages"}}, "11.4.0": {"name": "better-sqlite3", "version": "11.4.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@11.4.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "1821eca4257ea4b49ab1caf0e936421749f11d8a", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-11.4.0.tgz", "fileCount": 29, "integrity": "sha512-B7C9y2aSvtTwDJIz34iUxMjQWmbAYFmpq0Rwf9weYTtx6jUYsUKVt5ePPYlGyLVBoySppPa41PBrzl1ipMhG7A==", "signatures": [{"sig": "MEUCIBm2G992IYF4gkMSQrEEk1mhksqr2dX3UWlh0x9rsl4FAiEA4PffDN0ZRt23bU5GcbysfzOc4kXVH7/LhezrdMnd35Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9993211}, "main": "lib/index.js", "gitHead": "667d2f31843a2d76b3ebdc92097d323616e16a9b", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "18.20.4", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_11.4.0_1729197896671_0.36449604494540666", "host": "s3://npm-registry-packages"}}, "11.5.0": {"name": "better-sqlite3", "version": "11.5.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@11.5.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "58faa51e02845a578dd154f0083487132ead0695", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-11.5.0.tgz", "fileCount": 29, "integrity": "sha512-e/6eggfOutzoK0JWiU36jsisdWoHOfN9iWiW/SieKvb7SAa6aGNmBM/UKyp+/wWSXpLlWNN8tCPwoDNPhzUvuQ==", "signatures": [{"sig": "MEUCIQC6nVUMoLvUEqH49nUHOXhgnvrrhzwKl0U8Be090dBK4gIgdQnJFt7d2Hrl4xnRm3BLrH+lelXR5pdEycTLSVZHA+w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10103604}, "main": "lib/index.js", "gitHead": "5d410bb7ac6eb32e071527bf5709cba8d2bfadb5", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "18.20.4", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_11.5.0_1729566826383_0.5627617885007654", "host": "s3://npm-registry-packages"}}, "11.6.0": {"name": "better-sqlite3", "version": "11.6.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@11.6.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "e50736956e6fe1c30dc94f1bc94a9c15d63b7b6b", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-11.6.0.tgz", "fileCount": 29, "integrity": "sha512-2J6k/eVxcFYY2SsTxsXrj6XylzHWPxveCn4fKPKZFv/Vqn/Cd7lOuX4d7rGQXT5zL+97MkNL3nSbCrIoe3LkgA==", "signatures": [{"sig": "MEQCIDyjvXTBWyJDudhlr5Ns3P215RxDDrRJKDtn7hjwCi7dAiBQ2UuBoQubURSVuxrWXayL+Y0Sl1Xu9hyUrueDuiNyLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10104634}, "main": "lib/index.js", "gitHead": "03f2463ecc2f25375be18a53130182d2aeaea00a", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "18.20.5", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_11.6.0_1732585891957_0.24968825854794074", "host": "s3://npm-registry-packages"}}, "11.7.0": {"name": "better-sqlite3", "version": "11.7.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@11.7.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "3eaa0f54f9e57d0a100d980e42320f8b9a4cd676", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-11.7.0.tgz", "fileCount": 29, "integrity": "sha512-mXpa5jnIKKHeoGzBrUJrc65cXFKcILGZpU3FXR0pradUEm9MA7UZz02qfEejaMcm9iXrSOCenwwYMJ/tZ1y5Ig==", "signatures": [{"sig": "MEYCIQCycSq366aAM8Kz9AsBy0P1HNaqct2Sp8maXva+CuMSWAIhAM3Mgyc37awpi6pmXSTo0V+xEegRUPFE3sl11eP9HHJH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10104776}, "main": "lib/index.js", "gitHead": "61b7e58749274bbe06cc97e565bc40f9f5d2473a", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "18.20.5", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_11.7.0_1733625784677_0.0690768111347202", "host": "s3://npm-registry-packages"}}, "11.7.2": {"name": "better-sqlite3", "version": "11.7.2", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@11.7.2", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "66b738894a6f51b87e26d76d8fbca60b75568660", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-11.7.2.tgz", "fileCount": 29, "integrity": "sha512-10a57cHVDmfNQS4jrZ9AH2t+2ekzYh5Rhbcnb4ytpmYweoLdogDmyTt5D+hLiY9b44Mx9foowb/4iXBTO2yP3Q==", "signatures": [{"sig": "MEUCIAFV52aG0lZad34rieoylyZ2uJjA5r04nhQpqCu12LE2AiEAj7lsq0aN2dgJ6+4LmUcojZZy10mDxZbM0WAwD936KV4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10104776}, "main": "lib/index.js", "gitHead": "618714a53d47f1755712c17b396c1ba3415f38fc", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "18.20.5", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.0", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_11.7.2_1736020518615_0.7551297833181947", "host": "s3://npm-registry-packages-npm-production"}}, "11.8.0": {"name": "better-sqlite3", "version": "11.8.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@11.8.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "3416449f9cb9ac5a1b6dcdb785faae61e6b876a7", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-11.8.0.tgz", "fileCount": 29, "integrity": "sha512-aKv9s2dir7bsEX5RIjL9HHWB9uQ+f6Vch5B4qmeAOop4Y9OYHX+PNKLr+mpv6+d8L/ZYh4l7H8zPuVMbWkVMLw==", "signatures": [{"sig": "MEQCIBA35MSYTpsCvosG9Qyi0HIG1JkuXgvYCKXXP11nu6E4AiB4Nb3kFinGexOJoJFUoA0zFTBZf/rUyFFedDJiycWmYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10125638}, "main": "lib/index.js", "gitHead": "c12f1091db645d701c9ee9f37e9472fbe9087a80", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "The fastest and simplest library for SQLite3 in Node.js.", "directories": {}, "_nodeVersion": "18.20.5", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.1", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_11.8.0_1736910014716_0.35473960996960674", "host": "s3://npm-registry-packages-npm-production"}}, "11.8.1": {"name": "better-sqlite3", "version": "11.8.1", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@11.8.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "bcb1c494984065a7ed76a5df5ecbcb0f068d47fa", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-11.8.1.tgz", "fileCount": 29, "integrity": "sha512-9BxNaBkblMjhJW8sMRZxnxVTRgbRmssZW0Oxc1MPBTfiR+WW21e2Mk4qu8CzrcZb1LwPCnFsfDEzq+SNcBU8eg==", "signatures": [{"sig": "MEUCIF2UaIa68ycNioTLX9LfjBG13P82IYypC1uzRTnBi+8OAiEAkenrKeOWZhFAXyv0oZ4tdj0+FN1b1kfhgN0RNh+Qf2w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10127006}, "main": "lib/index.js", "gitHead": "d07834f4bf04a1338fc51930793deb30ad54d3cc", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "The fastest and simplest library for SQLite in Node.js.", "directories": {}, "_nodeVersion": "18.20.5", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.1", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_11.8.1_1737182301573_0.7457844660199184", "host": "s3://npm-registry-packages-npm-production"}}, "11.9.0": {"name": "better-sqlite3", "version": "11.9.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@11.9.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "465eb9f382ce1c1d1bde7ad7a5e5618c2056437c", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-11.9.0.tgz", "fileCount": 29, "integrity": "sha512-4b9xYnoaskj8eIkke9ZCB42p5bOPabptSku8Rl4Yww70Jf+aHeLvrIjXDJrKQxUEjdppsFb+fdJSjoH4TklROA==", "signatures": [{"sig": "MEUCIEMyaTDPIwgPhssGh0Mki6QrX+DbnhN/rYrJlkaSNAuDAiEA2bKuDaSrCA2J4cDth0JXHeFZoCz7a+OVnRV+YIZrTfM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10150236}, "main": "lib/index.js", "gitHead": "c7572501d35d51f483381808460612bf610c709b", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "The fastest and simplest library for SQLite in Node.js.", "directories": {}, "_nodeVersion": "18.20.7", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.1", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_11.9.0_1741920836946_0.493334275106051", "host": "s3://npm-registry-packages-npm-production"}}, "11.9.1": {"name": "better-sqlite3", "version": "11.9.1", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@11.9.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "0540da2f2ce24cbd766bb35db412f4be2c75b8bb", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-11.9.1.tgz", "fileCount": 29, "integrity": "sha512-Ba0KR+Fzxh2jDRhdg6TSH0SJGzb8C0aBY4hR8w8madIdIzzC6Y1+kx5qR6eS1Z+Gy20h6ZU28aeyg0z1VIrShQ==", "signatures": [{"sig": "MEYCIQC6wkLWFff4War1YpPmW9kd0Z8Gcu6DtbGskqiyLzWORQIhAJyLq87BJNq3knfuYkrA3sylJn8ZLywmMYnaV+GkdIyf", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10150236}, "main": "lib/index.js", "gitHead": "df8a6a408008379dd4a600ce77af5e7b5d7e2d83", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "The fastest and simplest library for SQLite in Node.js.", "directories": {}, "_nodeVersion": "18.20.7", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.1", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_11.9.1_1742264030854_0.18502343623207418", "host": "s3://npm-registry-packages-npm-production"}}, "11.10.0": {"name": "better-sqlite3", "version": "11.10.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@11.10.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "2b1b14c5acd75a43fd84d12cc291ea98cef57d98", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-11.10.0.tgz", "fileCount": 29, "integrity": "sha512-EwhOpyXiOEL/lKzHz9AW1msWFNzGc/z+LzeB3/jnFJpxu+th2yqvzsSWas1v9jgs9+xiXJcD5A8CJxAG2TaghQ==", "signatures": [{"sig": "MEYCIQCzMfSsdLG/zgPHGbPeoB9qUjCCfVLnZis3oD1VNAYMUwIhAK3xnuLATkDZZI82cHgCW4dmrk9NAA3dhpjbmpjXxQYc", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10150864}, "main": "lib/index.js", "gitHead": "335ccad4f0d24d4377eed1118bea5daec0b61647", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "The fastest and simplest library for SQLite in Node.js.", "directories": {}, "_nodeVersion": "18.20.8", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.1", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_11.10.0_1746676703925_0.6175399348648789", "host": "s3://npm-registry-packages-npm-production"}}, "12.0.0": {"name": "better-sqlite3", "version": "12.0.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@12.0.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "8a38992e571dc681b5bcf5c3178c608c8aca5526", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-12.0.0.tgz", "fileCount": 29, "integrity": "sha512-ElLgwbEth4MHBrDXEqzkE7Hm2+ACw5+KKBhkLArcjJrVFJyOXvzcE/if2dx7/m5pXTc8vqJjsCQUt1AFQY+TTQ==", "signatures": [{"sig": "MEYCIQCa15iWFmKLSLShzNidzcMuCZQbMXc3ZHELG8u897QbRgIhAN34jXz5v0AwfH/0m4YMkPwZkF4VgwPBSWXR59h8S9IM", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10151185}, "main": "lib/index.js", "engines": {"node": "20.x || 22.x || 23.x || 24.x"}, "gitHead": "a6607df5d60c9fa0ad84ef9e5aa60fb43be3feda", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "actor": {"name": "joshua<PERSON>", "type": "user", "email": "joshuathomas<PERSON>@gmail.com"}, "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "The fastest and simplest library for SQLite in Node.js.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.1", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_12.0.0_1750538042272_0.2186835732325645", "host": "s3://npm-registry-packages-npm-production"}}, "12.1.0": {"name": "better-sqlite3", "version": "12.1.0", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@12.1.0", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "172297cae046be62dff7e861d47e6b2630da39a6", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-12.1.0.tgz", "fileCount": 29, "integrity": "sha512-8f5xvlc6bcGZ57kYzQmeWYHqbKj57RRK/WiTrBGUowiX8ZbrgStWznBPIMGJi5MP7SNhJCKSc4TBaX9hhQL6Iw==", "signatures": [{"sig": "MEQCIDMq/09jtXhEHhdDnbRV66xsIpIzvV0Xbv6bMn5QHplmAiBj+oQcKlFLw3YVy0QhHJKkx+39bNDB8CJ4lM7v4wkLIA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10151257}, "main": "lib/index.js", "engines": {"node": "20.x || 22.x || 23.x || 24.x"}, "gitHead": "60a3f9acce98baf7dc96b38cd3829d8d47d3fe67", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "actor": {"name": "joshua<PERSON>", "type": "user", "email": "joshuathomas<PERSON>@gmail.com"}, "email": "joshuathomas<PERSON>@gmail.com"}, "overrides": {"prebuild": {"node-abi": "4.9.0"}}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "The fastest and simplest library for SQLite in Node.js.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.1", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_12.1.0_1750720094521_0.7714633980700754", "host": "s3://npm-registry-packages-npm-production"}}, "12.1.1": {"name": "better-sqlite3", "version": "12.1.1", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "_id": "better-sqlite3@12.1.1", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "homepage": "http://github.com/WiseLibs/better-sqlite3", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "dist": {"shasum": "a317fb6586fe3739b49629e6134103e3dbb73cce", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-12.1.1.tgz", "fileCount": 29, "integrity": "sha512-xjl/TjWLy/6yLa5wkbQSjTgIgSiaEJy3XzjF5TAdiWaAsu/v0OCkYOc6tos+PkM/k4qURN2pFKTsbcG3gk29Uw==", "signatures": [{"sig": "MEYCIQDk1Z3k2tGeLPHnmwhzbqo1oFtCwo0taJX5WTE4PFWARwIhAJJ9GH5N7FAtApRjx0aOtB2La25/qKhO5VFs1ST9lCvf", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10151257}, "main": "lib/index.js", "engines": {"node": "20.x || 22.x || 23.x || 24.x"}, "gitHead": "2c87902df98d442c4267c541eb1c697be15cee94", "scripts": {"lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz", "test": "mocha --exit --slow=75 --timeout=5000", "install": "prebuild-install || node-gyp rebuild --release", "download": "bash ./deps/download.sh", "benchmark": "node benchmark", "build-debug": "node-gyp rebuild --debug", "build-release": "node-gyp rebuild --release", "rebuild-debug": "npm run lzz && npm run build-debug", "rebuild-release": "npm run lzz && npm run build-release"}, "_npmUser": {"name": "joshua<PERSON>", "actor": {"name": "joshua<PERSON>", "type": "user", "email": "joshuathomas<PERSON>@gmail.com"}, "email": "joshuathomas<PERSON>@gmail.com"}, "overrides": {"prebuild": {"node-abi": "4.9.0"}}, "repository": {"url": "git://github.com/WiseLibs/better-sqlite3.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "The fastest and simplest library for SQLite in Node.js.", "directories": {}, "_nodeVersion": "20.19.2", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "_hasShrinkwrap": false, "devDependencies": {"chai": "^4.3.8", "mocha": "^10.2.0", "sqlite": "^5.0.1", "sqlite3": "^5.1.6", "fs-extra": "^11.1.1", "nodemark": "^0.3.0", "prebuild": "^13.0.1", "cli-color": "^2.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/better-sqlite3_12.1.1_1750836640656_0.5764277726919762", "host": "s3://npm-registry-packages-npm-production"}}, "12.2.0": {"name": "better-sqlite3", "version": "12.2.0", "description": "The fastest and simplest library for SQLite in Node.js.", "homepage": "http://github.com/WiseLibs/better-sqlite3", "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "repository": {"type": "git", "url": "git://github.com/WiseLibs/better-sqlite3.git"}, "main": "lib/index.js", "engines": {"node": "20.x || 22.x || 23.x || 24.x"}, "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}, "overrides": {"prebuild": {"node-abi": "4.9.0"}}, "devDependencies": {"chai": "^4.3.8", "cli-color": "^2.0.3", "fs-extra": "^11.1.1", "mocha": "^10.2.0", "nodemark": "^0.3.0", "prebuild": "^13.0.1", "sqlite": "^5.0.1", "sqlite3": "^5.1.6"}, "scripts": {"install": "prebuild-install || node-gyp rebuild --release", "build-release": "node-gyp rebuild --release", "build-debug": "node-gyp rebuild --debug", "rebuild-release": "npm run lzz && npm run build-release", "rebuild-debug": "npm run lzz && npm run build-debug", "test": "mocha --exit --slow=75 --timeout=5000", "benchmark": "node benchmark", "download": "bash ./deps/download.sh", "lzz": "lzz -hx hpp -sx cpp -k BETTER_SQLITE3 -d -hl -sl -e ./src/better_sqlite3.lzz"}, "license": "MIT", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "_id": "better-sqlite3@12.2.0", "gitHead": "0380025df4ba3547a24c3d340263131c07179cdf", "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "_nodeVersion": "20.19.2", "_npmVersion": "10.8.2", "dist": {"integrity": "sha512-eGbYq2CT+tos1fBwLQ/tkBt9J5M3JEHjku4hbvQUePCckkvVf14xWj+1m7dGoK81M/fOjFT7yM9UMeKT/+vFLQ==", "shasum": "de7c3466074f2d1a5d260f510647e822e42684d2", "tarball": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-12.2.0.tgz", "fileCount": 29, "unpackedSize": 10202618, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDUmG1RH+PiFvQ0c/FKz7AAw3J+VIExMOJhvbwLolcF/AIgZ68omF75RYsM2NP+hTc8LmbKBsyHbAsV/J6zbhHaUN0="}]}, "_npmUser": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com", "actor": {"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com", "type": "user"}}, "directories": {}, "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/better-sqlite3_12.2.0_1751151923270_0.8927727132859937"}, "_hasShrinkwrap": false}}, "time": {"created": "2016-09-07T17:36:24.578Z", "modified": "2025-06-28T23:05:23.682Z", "0.5.0": "2016-09-07T17:36:24.578Z", "0.6.0": "2016-09-07T22:06:35.619Z", "0.6.1": "2016-09-07T22:15:06.081Z", "0.6.2": "2016-09-07T23:47:00.414Z", "0.7.0": "2016-09-08T06:05:01.771Z", "0.8.0": "2016-09-09T21:25:51.652Z", "0.8.1": "2016-09-09T21:31:10.146Z", "0.8.2": "2016-09-09T22:46:21.546Z", "0.9.0": "2016-09-11T19:57:58.558Z", "0.9.1": "2016-09-11T20:21:51.164Z", "0.9.2": "2016-09-12T12:26:30.745Z", "0.10.0": "2016-09-13T01:13:48.752Z", "0.10.1": "2016-09-13T01:24:58.535Z", "0.10.2": "2016-09-13T03:07:58.730Z", "0.10.3": "2016-09-13T03:23:52.875Z", "1.0.0": "2016-09-13T16:23:36.604Z", "1.0.1": "2016-09-14T02:11:37.492Z", "1.0.2": "2016-09-14T16:25:34.151Z", "1.1.0": "2016-09-16T22:30:46.081Z", "1.1.1": "2016-09-17T16:27:07.016Z", "1.1.2": "2016-09-17T19:58:02.994Z", "1.1.3": "2016-09-17T23:58:50.095Z", "1.2.0": "2016-09-18T04:21:30.987Z", "1.2.1": "2016-09-23T20:58:42.508Z", "1.3.0": "2016-10-08T02:45:28.662Z", "1.3.1": "2016-10-08T03:07:01.506Z", "1.3.2": "2016-10-08T20:00:26.014Z", "1.3.3": "2016-10-08T23:39:20.579Z", "1.3.4": "2016-10-08T23:49:26.588Z", "1.3.5": "2016-10-08T23:57:43.006Z", "1.3.6": "2016-10-11T22:19:58.672Z", "1.3.7": "2016-10-12T07:46:25.189Z", "1.3.8": "2016-10-13T06:40:29.762Z", "1.3.9": "2016-10-14T21:55:13.990Z", "1.4.0": "2017-01-22T22:38:10.108Z", "2.0.0": "2017-03-01T15:27:34.412Z", "2.0.1": "2017-04-04T00:33:05.336Z", "2.1.0": "2017-04-08T03:08:44.218Z", "2.2.0": "2017-04-12T19:01:53.921Z", "2.3.0": "2017-04-13T06:01:28.257Z", "2.3.1": "2017-04-14T20:56:17.881Z", "2.3.2": "2017-04-14T21:20:04.349Z", "2.3.3": "2017-04-30T04:48:13.806Z", "3.0.1": "2017-06-06T03:50:17.063Z", "3.0.2": "2017-06-06T03:56:25.194Z", "3.0.0": "2017-06-06T04:57:11.048Z", "3.0.3": "2017-06-08T16:42:07.979Z", "3.1.0": "2017-06-12T03:56:11.298Z", "3.1.1": "2017-06-23T18:56:45.772Z", "3.1.2": "2017-07-19T15:27:02.118Z", "3.1.3": "2017-08-24T16:57:30.830Z", "3.2.0": "2017-08-24T22:23:42.798Z", "3.3.0": "2017-08-28T03:00:41.676Z", "4.0.0": "2017-08-29T12:20:08.582Z", "4.0.1": "2017-08-29T12:47:44.103Z", "4.0.2": "2017-08-31T22:27:09.147Z", "4.0.3": "2017-09-25T18:39:22.904Z", "4.1.0": "2018-01-20T11:36:15.140Z", "4.1.1": "2018-05-23T23:23:50.741Z", "4.1.2": "2018-06-13T05:06:39.810Z", "4.1.3": "2018-06-13T10:54:38.680Z", "4.1.4": "2018-06-13T11:01:13.158Z", "5.0.0": "2018-10-09T14:54:53.229Z", "4.2.0": "2018-10-09T15:18:56.450Z", "5.0.1": "2018-10-09T21:06:19.764Z", "5.1.0": "2018-12-15T08:49:13.333Z", "5.2.0": "2018-12-15T13:39:13.810Z", "5.2.1": "2018-12-17T18:03:39.316Z", "5.3.0": "2019-01-11T17:09:56.855Z", "5.4.0": "2019-01-22T19:19:17.010Z", "5.4.1": "2019-07-18T20:24:37.061Z", "5.4.2": "2019-08-05T15:55:56.372Z", "5.4.3": "2019-09-04T20:26:47.923Z", "6.0.0": "2020-02-24T13:51:15.426Z", "6.0.1": "2020-02-24T14:23:54.553Z", "7.0.0": "2020-04-25T15:24:32.066Z", "7.0.1": "2020-04-26T00:50:02.781Z", "7.1.0": "2020-06-10T18:49:39.402Z", "7.1.1": "2020-09-14T20:19:43.249Z", "7.1.2": "2020-12-19T22:27:20.139Z", "7.1.4": "2021-03-30T22:50:51.601Z", "7.1.5": "2021-04-14T23:01:15.039Z", "7.2.0": "2021-05-04T06:22:48.865Z", "7.3.0": "2021-05-05T01:22:25.782Z", "7.3.1": "2021-05-05T14:09:01.325Z", "7.4.0": "2021-05-14T14:45:12.511Z", "7.4.1": "2021-06-06T02:34:23.334Z", "7.4.2": "2021-07-19T04:50:15.351Z", "7.4.3": "2021-07-19T05:21:28.414Z", "7.4.4": "2021-10-25T14:04:41.832Z", "7.4.5": "2021-11-19T22:42:05.742Z", "7.4.6": "2021-12-29T23:37:21.730Z", "7.5.0": "2022-01-19T02:41:46.353Z", "7.5.1": "2022-04-07T04:56:59.466Z", "7.5.2": "2022-05-15T16:11:49.585Z", "7.5.3": "2022-05-16T06:43:48.536Z", "7.6.0": "2022-07-09T05:37:37.933Z", "7.6.1": "2022-07-14T00:07:31.823Z", "7.6.2": "2022-07-15T23:04:13.698Z", "8.0.0": "2022-11-22T01:18:23.418Z", "8.0.1": "2022-12-02T02:09:38.003Z", "8.1.0": "2023-02-07T01:44:46.203Z", "8.2.0": "2023-03-08T22:39:21.278Z", "8.3.0": "2023-04-08T20:07:50.530Z", "8.4.0": "2023-05-20T19:11:01.054Z", "8.5.0": "2023-07-19T16:51:56.310Z", "8.5.1": "2023-08-17T15:18:34.546Z", "8.5.2": "2023-08-24T15:01:22.851Z", "8.6.0": "2023-09-02T19:39:47.118Z", "8.7.0": "2023-09-30T21:00:06.254Z", "9.0.0": "2023-10-10T21:47:35.688Z", "9.1.0": "2023-11-08T20:51:59.732Z", "9.1.1": "2023-11-09T04:34:02.132Z", "9.2.0": "2023-12-02T01:18:06.300Z", "9.2.1": "2023-12-05T01:17:29.174Z", "9.2.2": "2023-12-05T03:11:50.630Z", "9.3.0": "2024-01-17T00:24:24.328Z", "9.4.0": "2024-02-02T21:31:39.471Z", "9.4.1": "2024-02-10T23:53:14.317Z", "9.4.2": "2024-02-21T20:44:31.910Z", "9.4.3": "2024-02-22T00:51:14.094Z", "9.4.4": "2024-04-03T05:04:50.872Z", "9.4.5": "2024-04-03T22:43:42.181Z", "9.5.0": "2024-04-10T02:17:46.264Z", "9.6.0": "2024-04-26T04:55:59.816Z", "10.0.0": "2024-05-12T04:40:39.189Z", "10.1.0": "2024-05-30T17:42:35.774Z", "11.0.0": "2024-05-31T01:20:58.872Z", "11.1.1": "2024-06-27T17:43:11.604Z", "11.1.2": "2024-07-02T16:44:43.911Z", "11.2.0": "2024-08-21T06:30:23.979Z", "11.2.1": "2024-08-21T15:17:08.796Z", "11.3.0": "2024-09-10T04:47:21.330Z", "11.4.0": "2024-10-17T20:44:57.041Z", "11.5.0": "2024-10-22T03:13:46.681Z", "11.6.0": "2024-11-26T01:51:32.249Z", "11.7.0": "2024-12-08T02:43:04.945Z", "11.7.2": "2025-01-04T19:55:18.857Z", "11.8.0": "2025-01-15T03:00:14.948Z", "11.8.1": "2025-01-18T06:38:21.822Z", "11.9.0": "2025-03-14T02:53:57.223Z", "11.9.1": "2025-03-18T02:13:51.069Z", "11.10.0": "2025-05-08T03:58:24.171Z", "12.0.0": "2025-06-21T20:34:02.532Z", "12.1.0": "2025-06-23T23:08:14.800Z", "12.1.1": "2025-06-25T07:30:40.899Z", "12.2.0": "2025-06-28T23:05:23.502Z"}, "bugs": {"url": "https://github.com/WiseLibs/better-sqlite3/issues"}, "author": {"name": "<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}, "license": "MIT", "homepage": "http://github.com/WiseLibs/better-sqlite3", "keywords": ["sql", "sqlite", "sqlite3", "transactions", "user-defined functions", "aggregate functions", "window functions", "database"], "repository": {"type": "git", "url": "git://github.com/WiseLibs/better-sqlite3.git"}, "description": "The fastest and simplest library for SQLite in Node.js.", "maintainers": [{"name": "joshua<PERSON>", "email": "joshuathomas<PERSON>@gmail.com"}], "readme": "# better-sqlite3 [![Build Status](https://github.com/JoshuaWise/better-sqlite3/actions/workflows/build.yml/badge.svg)](https://github.com/JoshuaWise/better-sqlite3/actions/workflows/build.yml?query=branch%3Amaster)\n\nThe fastest and simplest library for SQLite in Node.js.\n\n- Full transaction support\n- High performance, efficiency, and safety\n- Easy-to-use synchronous API *(better concurrency than an asynchronous API... yes, you read that correctly)*\n- Support for user-defined functions, aggregates, virtual tables, and extensions\n- 64-bit integers *(invisible until you need them)*\n- Worker thread support *(for large/slow queries)*\n\n## Help this project stay strong! &#128170;\n\n`better-sqlite3` is used by thousands of developers and engineers on a daily basis. Long nights and weekends were spent keeping this project strong and dependable, with no ask for compensation or funding, until now. If your company uses `better-sqlite3`, ask your manager to consider supporting the project:\n\n- [Become a GitHub sponsor](https://github.com/sponsors/JoshuaWise)\n- [Become a backer on Patreon](https://www.patreon.com/joshuawise)\n- [Make a one-time donation on PayPal](https://www.paypal.me/joshuathomaswise)\n\n## How other libraries compare\n\n|   |select 1 row &nbsp;`get()`&nbsp;|select 100 rows &nbsp;&nbsp;`all()`&nbsp;&nbsp;|select 100 rows `iterate()` 1-by-1|insert 1 row `run()`|insert 100 rows in a transaction|\n|---|---|---|---|---|---|\n|better-sqlite3|1x|1x|1x|1x|1x|\n|[sqlite](https://www.npmjs.com/package/sqlite) and [sqlite3](https://www.npmjs.com/package/sqlite3)|11.7x slower|2.9x slower|24.4x slower|2.8x slower|15.6x slower|\n\n> You can verify these results by [running the benchmark yourself](./docs/benchmark.md).\n\n## Installation\n\n```bash\nnpm install better-sqlite3\n```\n\n> Requires Node.js v14.21.1 or later. Prebuilt binaries are available for [LTS versions](https://nodejs.org/en/about/releases/). If you have trouble installing, check the [troubleshooting guide](./docs/troubleshooting.md).\n\n## Usage\n\n```js\nconst db = require('better-sqlite3')('foobar.db', options);\n\nconst row = db.prepare('SELECT * FROM users WHERE id = ?').get(userId);\nconsole.log(row.firstName, row.lastName, row.email);\n```\n\nThough not required, [it is generally important to set the WAL pragma for performance reasons](https://github.com/WiseLibs/better-sqlite3/blob/master/docs/performance.md).\n\n```js\ndb.pragma('journal_mode = WAL');\n```\n\n##### In ES6 module notation:\n\n```js\nimport Database from 'better-sqlite3';\nconst db = new Database('foobar.db', options);\ndb.pragma('journal_mode = WAL');\n```\n\n## Why should I use this instead of [node-sqlite3](https://github.com/mapbox/node-sqlite3)?\n\n- `node-sqlite3` uses asynchronous APIs for tasks that are either CPU-bound or serialized. That's not only bad design, but it wastes tons of resources. It also causes [mutex thrashing](https://en.wikipedia.org/wiki/Resource_contention) which has devastating effects on performance.\n- `node-sqlite3` exposes low-level (C language) memory management functions. `better-sqlite3` does it the JavaScript way, allowing the garbage collector to worry about memory management.\n- `better-sqlite3` is simpler to use, and it provides nice utilities for some operations that are very difficult or impossible in `node-sqlite3`.\n- `better-sqlite3` is much faster than `node-sqlite3` in most cases, and just as fast in all other cases.\n\n#### When is this library not appropriate?\n\nIn most cases, if you're attempting something that cannot be reasonably accomplished with `better-sqlite3`, it probably cannot be reasonably accomplished with SQLite in general. For example, if you're executing queries that take one second to complete, and you expect to have many concurrent users executing those queries, no amount of asynchronicity will save you from SQLite's serialized nature. Fortunately, SQLite is very *very* fast. With proper indexing, we've been able to achieve upward of 2000 queries per second with 5-way-joins in a 60 GB database, where each query was handling 5–50 kilobytes of real data.\n\nIf you have a performance problem, the most likely causes are inefficient queries, improper indexing, or a lack of [WAL mode](./docs/performance.md)—not `better-sqlite3` itself. However, there are some cases where `better-sqlite3` could be inappropriate:\n\n- If you expect a high volume of concurrent reads each returning many megabytes of data (i.e., videos)\n- If you expect a high volume of concurrent writes (i.e., a social media site)\n- If your database's size is near the terabyte range\n\nFor these situations, you should probably use a full-fledged RDBMS such as [PostgreSQL](https://www.postgresql.org/).\n\n## Upgrading\n\nUpgrading your `better-sqlite3` dependency can potentially introduce breaking changes, either in the `better-sqlite3` API (if you upgrade to a new [major version](https://semver.org/)), or between your existing database(s) and the underlying version of SQLite. Before upgrading, review:\n\n* [`better-sqlite3` release notes](https://github.com/WiseLibs/better-sqlite3/releases)\n* [SQLite release history](https://www.sqlite.org/changes.html)\n\n# Documentation\n\n- [API documentation](./docs/api.md)\n- [Performance](./docs/performance.md) (also see [benchmark results](./docs/benchmark.md))\n- [64-bit integer support](./docs/integer.md)\n- [Worker thread support](./docs/threads.md)\n- [Unsafe mode (advanced)](./docs/unsafe.md)\n- [SQLite compilation (advanced)](./docs/compilation.md)\n- [Contribution rules](./docs/contribution.md)\n- [Code of conduct](./docs/conduct.md)\n\n# License\n\n[MIT](./LICENSE)\n", "readmeFilename": "README.md", "users": {"dwqs": true, "duxing": true, "horpto": true, "isayme": true, "soladmd": true, "jsumners": true, "wujianfu": true, "azusa0127": true, "baseplace": true, "rocket0191": true, "theflyingape": true, "usingthesystem": true}}