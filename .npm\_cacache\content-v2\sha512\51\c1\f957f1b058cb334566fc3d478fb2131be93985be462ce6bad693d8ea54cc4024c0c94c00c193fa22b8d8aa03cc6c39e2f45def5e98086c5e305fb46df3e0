{"_id": "prompts", "_rev": "55-45baf4865a46b72393415e064fbb434d", "name": "prompts", "dist-tags": {"latest": "2.4.2"}, "versions": {"0.0.0": {"name": "prompts", "version": "0.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "_id": "prompts@0.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-m7ee1XiIV4f8srJDmmxTXm4KALiydqfJEFROFL2Tnxho7HEq8HLSwaYjtpAQyKsHkNfO8VjJwf/tK+bTdMAEOA==", "shasum": "97e9f526d490a017353672427d258b57d4f66091", "tarball": "https://registry.npmjs.org/prompts/-/prompts-0.0.0.tgz", "fileCount": 1, "unpackedSize": 203, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCGNsaEpr9trzkAERvioXlxxfr6v28Bd8/I6xFrFDrk6gIgD1t3w9//znUGgRpnqr7IsZ3CRNhTfF7WHB1jEHtL48g="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_0.0.0_1518987309803_0.544504880851508"}, "_hasShrinkwrap": false}, "0.1.0-1": {"name": "prompts", "version": "0.1.0-1", "description": "Lightweight, beautiful and user-friendly prompts", "homepage": "https://github.com/terkelg/prompts", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "lib/index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "files": ["lib"], "scripts": {"start": "node lib/index.js", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input"], "dependencies": {"clorox": "^1.0.1", "sisteransi": "^0.1.0"}, "devDependencies": {"tap-spec": "^4.1.1", "tape": "^4.8.0"}, "gitHead": "69b3761347b4e81883198e4338e200a0731babc7", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "_id": "prompts@0.1.0-1", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-5CD4Hz4ZSCnLse/BslAKDCuX2T9gT8myGF36bG7CJGei159DdHHUlLTkZ/8e25nGMPmsiySez2KYbduax5zuYg==", "shasum": "50e3de76b4315e5c1cb94a79748f9bc0c6e42a75", "tarball": "https://registry.npmjs.org/prompts/-/prompts-0.1.0-1.tgz", "fileCount": 23, "unpackedSize": 56724, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDfwzo/J44CXcstzAXIkCuX7Pa5DYiIRSvhZt/DCvp1AgIhAOzMLrk8fZWYtvuridW0jQ+DfO0VlP1nVwBPIRzsg94o"}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_0.1.0-1_1519467162182_0.6554553001332062"}, "_hasShrinkwrap": false}, "0.1.0": {"name": "prompts", "version": "0.1.0", "description": "Lightweight, beautiful and user-friendly prompts", "homepage": "https://github.com/terkelg/prompts", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "lib/index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "files": ["lib"], "scripts": {"start": "node lib/index.js", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input"], "dependencies": {"clorox": "^1.0.1", "sisteransi": "^0.1.0"}, "devDependencies": {"tap-spec": "^4.1.1", "tape": "^4.8.0"}, "gitHead": "22b388ed32bb15c5bceb880e08670344e2e39f32", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "_id": "prompts@0.1.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-+RlN8IAcTCkg1DSu3+SNH8fYhfHVOz67Dw8wa9uaoCAutDEr+R1iMxK8TUujWPZTdb/5AF6amQ9fXG/Ji5N42w==", "shasum": "e436dc651e1b6fca10697d2cc16ca9dd9ced4d10", "tarball": "https://registry.npmjs.org/prompts/-/prompts-0.1.0.tgz", "fileCount": 23, "unpackedSize": 57577, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBzCFr0pS1PJmSpTimAgS9iG6tHa48wXjyWZFLsXebiEAiA8xn0wjX06GvJ7sw+RWyfu9l/FA/NUGXGgw5GaC5SkKg=="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_0.1.0_1519515734455_0.2015390620582045"}, "_hasShrinkwrap": false}, "0.1.1": {"name": "prompts", "version": "0.1.1", "description": "Lightweight, beautiful and user-friendly prompts", "homepage": "https://github.com/terkelg/prompts", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "lib/index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "files": ["lib"], "scripts": {"start": "node lib/index.js", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input"], "dependencies": {"clorox": "^1.0.1", "sisteransi": "^0.1.0"}, "devDependencies": {"tap-spec": "^4.1.1", "tape": "^4.8.0"}, "gitHead": "6055b64441979479173028abb3df285e8ec814d1", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "_id": "prompts@0.1.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-BaodziZuwqbW2qy/Oo5VqbvcpEstmFNlhc5t1GNcyxwS77D2cH1x99dcMNUwCh1mpaDtw/Iq7jLUkH3smzCafQ==", "shasum": "ee9c86ec59ba2a1662392a490e02e14886ace62c", "tarball": "https://registry.npmjs.org/prompts/-/prompts-0.1.1.tgz", "fileCount": 23, "unpackedSize": 60272, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAyjPp10vIVvmTqH2u8hl1tr5AxKAxHQRNKiOsOxgMzVAiBm//2M7vFudpUibtKubagCtkDSTPvq93/D43r09C0rRA=="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_0.1.1_1519715483867_0.8529380125262322"}, "_hasShrinkwrap": false}, "0.1.2": {"name": "prompts", "version": "0.1.2", "description": "Lightweight, beautiful and user-friendly prompts", "homepage": "https://github.com/terkelg/prompts", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "lib/index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "files": ["lib"], "scripts": {"start": "node lib/index.js", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input"], "dependencies": {"clorox": "^1.0.1", "sisteransi": "^0.1.0"}, "devDependencies": {"tap-spec": "^4.1.1", "tape": "^4.8.0"}, "gitHead": "793a4c92ba2841a0964b3cc5b7867eeea461f9a2", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "_id": "prompts@0.1.2", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-t7IffM1thvhdWDv3OsfLMMzymfY0DZ3ZL9kUbl8geuV4rP1bHME59MfDHAoAGq2al/HLsasxA4D7BwUTdPA2ww==", "shasum": "efea24c5bd2017d4cd15a45baf71ce1c6ab5dee6", "tarball": "https://registry.npmjs.org/prompts/-/prompts-0.1.2.tgz", "fileCount": 23, "unpackedSize": 60277, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBfNduNomKdbbysVWgokR8sGL+4IfPn3rV9unxoOPP7WAiEA4d2CN6V8y7evU1VGKryEWimtoOBBDhQJKzprGIfBLvU="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_0.1.2_1519790259560_0.5973086706771944"}, "_hasShrinkwrap": false}, "0.1.3": {"name": "prompts", "version": "0.1.3", "description": "Lightweight, beautiful and user-friendly prompts", "homepage": "https://github.com/terkelg/prompts", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "lib/index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "files": ["lib"], "scripts": {"start": "node lib/index.js", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input"], "dependencies": {"clorox": "^1.0.1", "sisteransi": "^0.1.0"}, "devDependencies": {"tap-spec": "^4.1.1", "tape": "^4.8.0"}, "gitHead": "420515aee3617c83a5a45d823dd97f4dfc627c4f", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "_id": "prompts@0.1.3", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-tA4w6cquRUvxqy3AWnDEQ5ZK4mc7JwaJOTfxAT8RxxaNxoy6rERh76OhEmbv3SFuDTdjJM01bwv6w5hXz5c2NQ==", "shasum": "975bf9d30716ced5d116a7397aae8204a8a12b7f", "tarball": "https://registry.npmjs.org/prompts/-/prompts-0.1.3.tgz", "fileCount": 22, "unpackedSize": 60616, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDu2k0D01KoJD8CQ+jUW9a4xxjt2f/UnMzPzeCUA7EUFAIgHCx+Eu0hYiZooAasExgk3rVY3TKRWU9fB8RwrIm9VCA="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_0.1.3_1519806206706_0.032065731357912464"}, "_hasShrinkwrap": false}, "0.1.4": {"name": "prompts", "version": "0.1.4", "description": "Lightweight, beautiful and user-friendly prompts", "homepage": "https://github.com/terkelg/prompts", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "lib/index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "files": ["lib"], "scripts": {"start": "node lib/index.js", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input"], "dependencies": {"clorox": "^1.0.1", "sisteransi": "^0.1.0"}, "devDependencies": {"tap-spec": "^4.1.1", "tape": "^4.8.0"}, "gitHead": "b4c2b2b319d6d3a7b9e1920f60f379d87769249a", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "_id": "prompts@0.1.4", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-+1EqQSfG43l4Ab16vu5vB8wH4F7BjGHvk8rroX6Eiah6euOo2XXlA2ZG7/qnHhnBwF4o8jYrPj+jFcOYNV0Ckg==", "shasum": "d5376a0441edb8c26a65a208ef964ec287ee2b6e", "tarball": "https://registry.npmjs.org/prompts/-/prompts-0.1.4.tgz", "fileCount": 22, "unpackedSize": 60621, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQ0SU37gjn1uaHlobOZHyTjK3jRE7OObKNwOOL6SMAuwIhAL/zhaOEugtloYvvPcuHU+NfS54g+nHutSxP0dMSJWdO"}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_0.1.4_1519837630717_0.9252952771012415"}, "_hasShrinkwrap": false}, "0.1.5": {"name": "prompts", "version": "0.1.5", "description": "Lightweight, beautiful and user-friendly prompts", "homepage": "https://github.com/terkelg/prompts", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "lib/index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "files": ["lib"], "scripts": {"start": "node lib/index.js", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input"], "dependencies": {"clorox": "^1.0.1", "sisteransi": "^0.1.0"}, "devDependencies": {"tap-spec": "^4.1.1", "tape": "^4.8.0"}, "engines": {"node": ">= 8"}, "gitHead": "2ad8dc06be4b86a253a54d57fddbef41453901a4", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "_id": "prompts@0.1.5", "_npmVersion": "5.6.0", "_nodeVersion": "9.7.1", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-XxpK0kbwe3LV8aFoDLXfrrD9Al5w/8OKKvvOtF8bNVjd6vdV1oOqaSIYYFE4wMXfHfiElMwkju2hO4s7tDQdrg==", "shasum": "c71becc063f22b80f0b1c042e55afb9fc6291f6c", "tarball": "https://registry.npmjs.org/prompts/-/prompts-0.1.5.tgz", "fileCount": 22, "unpackedSize": 62575, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGLXgst960BP+4X8i15nM1WcP7cV2v/YXzD7DKlEv7QuAiA8X0f+/bJYurVDN56iLQJ26VLext9zpKp0DhjF4Ux7Uw=="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_0.1.5_1520401874527_0.09999661640627067"}, "_hasShrinkwrap": false}, "0.1.6": {"name": "prompts", "version": "0.1.6", "description": "Lightweight, beautiful and user-friendly prompts", "homepage": "https://github.com/terkelg/prompts", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "lib/index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "files": ["lib"], "scripts": {"start": "node lib/index.js", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input"], "dependencies": {"clorox": "^1.0.1", "sisteransi": "^0.1.0"}, "devDependencies": {"tap-spec": "^4.1.1", "tape": "^4.8.0"}, "engines": {"node": ">= 8"}, "gitHead": "8b0c602d20810a3997858c758cbbf6edc40cbaa3", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "_id": "prompts@0.1.6", "_npmVersion": "5.6.0", "_nodeVersion": "9.7.1", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-0sSgh+5zrCziv0UBCd3G5RORZfxjMw0UHhu1Cm6vrMb50Vc280rzJTOSD/Z24XCNMi4w6ETvydYgKkEKjOBeEA==", "shasum": "77f141fde860445edd8195b131a80c2ad56fc2bb", "tarball": "https://registry.npmjs.org/prompts/-/prompts-0.1.6.tgz", "fileCount": 22, "unpackedSize": 62649, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCb2f3XFj8zku+dFh4taApSFJgykBzhzrXek0HwczHQGAIgEXHUUfDhUWDB1O7qF3jI36loI83l/otrQnBgIoZsFyw="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_0.1.6_1520588959809_0.12670301785648141"}, "_hasShrinkwrap": false}, "0.1.7": {"name": "prompts", "version": "0.1.7", "description": "Lightweight, beautiful and user-friendly prompts", "homepage": "https://github.com/terkelg/prompts", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "lib/index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "files": ["lib"], "scripts": {"start": "node lib/index.js", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input"], "dependencies": {"clorox": "^1.0.1", "sisteransi": "^0.1.0"}, "devDependencies": {"tap-spec": "^4.1.1", "tape": "^4.8.0"}, "engines": {"node": ">= 8"}, "gitHead": "e2f7a2086764a5aaa0198d934e6a3761102dcef1", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "_id": "prompts@0.1.7", "_npmVersion": "5.6.0", "_nodeVersion": "9.7.1", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-EOyq4cehZdkTsByOGI/KHH68l/QrzFufSlmcc6H84XuipcUtua22baKPeV16d8opnSxhNKAIaMeQzmVNa6lx2w==", "shasum": "c326f5cf7a9627f39d3019bba5f812805665aad5", "tarball": "https://registry.npmjs.org/prompts/-/prompts-0.1.7.tgz", "fileCount": 22, "unpackedSize": 67220, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDWplZY1kgmSMjBoQkddlLb7KKa+1Df1+MshXFR0tPyXAiAh+upV5PXBd6pxWZLv2diQCcW+PAt9dKXEVw0EXYdl0Q=="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_0.1.7_1520807457400_0.9111213819183499"}, "_hasShrinkwrap": false}, "0.1.8": {"name": "prompts", "version": "0.1.8", "description": "Lightweight, beautiful and user-friendly prompts", "homepage": "https://github.com/terkelg/prompts", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "lib/index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "files": ["lib"], "scripts": {"start": "node lib/index.js", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input"], "dependencies": {"clorox": "^1.0.1", "sisteransi": "^0.1.0"}, "devDependencies": {"tap-spec": "^4.1.1", "tape": "^4.8.0"}, "engines": {"node": ">= 8"}, "gitHead": "6197ae453df612a3077cfcd298f6340d09d93878", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "_id": "prompts@0.1.8", "_npmVersion": "5.6.0", "_nodeVersion": "9.7.1", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-IAV6uUb0fj4BgGyuWbjx0qZuJuUiIUhpzPq6yZyyEVmY9SHMkA+qIR7PVY2zl2nOJNVjqO8UfYgwUILU39vtQQ==", "shasum": "6c43ec1f8812479d02d278e536db913f6694381c", "tarball": "https://registry.npmjs.org/prompts/-/prompts-0.1.8.tgz", "fileCount": 22, "unpackedSize": 67220, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCalYata8n9xx1JoYNsNx+x72Zb2pPU1GfXnOq8L5kMGQIgDQ8/bVnlyAnPn+mVtuqObYePkDxTwiYUqkB9KvKcFJI="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_0.1.8_1520909651130_0.3212697160561022"}, "_hasShrinkwrap": false}, "0.1.9": {"name": "prompts", "version": "0.1.9", "description": "Lightweight, beautiful and user-friendly prompts", "homepage": "https://github.com/terkelg/prompts", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "files": ["lib", "dist", "index.js"], "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublish": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input"], "dependencies": {"clorox": "^1.0.1", "sisteransi": "^0.1.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.7.0", "tap-spec": "^4.1.1", "tape": "^4.8.0"}, "engines": {"node": ">= 6"}, "gitHead": "0b1a85198b8140e773f18ab904e1717ed2a4914a", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "_id": "prompts@0.1.9", "_npmVersion": "5.8.0", "_nodeVersion": "9.7.1", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-RMRvwAUDVUMhP/z3YfDW6igMwT0UnL+w3XCUUNxxHjgwJnVEdHWYJVjM7hQMPub8HCk12xZYAqWlbgLBnqebwg==", "shasum": "be109f62f794f138c6bbd35c25370c5c526d9d78", "tarball": "https://registry.npmjs.org/prompts/-/prompts-0.1.9.tgz", "fileCount": 38, "unpackedSize": 91739, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbKIPfCRA9TVsSAnZWagAA8AsP/2UyjEYkzw3rzT9fy7yr\nUPJJ1hq+OrVITat/dvKijQsxpkuGEQUkOr0Iwp7q3CLtT8IbxZRavEMowvu0\n/TWZdbOUXNDzDVzUY9OrvPZ6J7fNOOwuWGdzM1M3SzoyQD8u2asK6FH6NDPr\nNePv05LOFYScXyAEdsAHt3UzKeLnPh1f7F9qz7y2Nwbfr/w3zn4CExNghCG6\nCDayeq5bxeU2QGZuu0pdLEVvQLclb++SifOWpChEppVgNzoyTPk4avLIW75u\nRdlj/BZyngvAyeknfJfkjMt8q/MKw0rfwwOPvNVOFDLBLVrWJP2pTiLyiKpC\nwg6YDU8Uc5LDbI3zUPAg6nRm2nKLoZf+PuWhEHBklZCkw36aciVMThAGzm1T\nH4a83JwIs14GoDZXmuovfv0Vi64vyzPDs15kp3RAS5XcwBB0VhLI9iHJVdDy\nsqiY0LcKl5KTMwMry8KSfO2TGt0Z8BQFll5zvGh70BabzfV/p7DdwvA4FkJ3\nO0cwmAz11QDRC+qcBOIdAxUHSEPBSiyL7P7Cr+msPwJgoWnry3vjFJRjCXNs\n6dTdoIa7ajKWDkOQnFvQisztiQD+Wy5VHqVCP+CuUmY/BtRvz2u+AVfRKNve\nJoCIZj/7izjDkZapjyJrZuj0s/an3Q0iNtpyHOBre7wrkRlnE1tkwLCk/p3/\n0w4w\r\n=X3fJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCYf3QkZxTjVg232Bp0fZD5TkgoLAYZDcM1bxHcMxU8vQIgYQUdGozbipqXMpjpCOylEgvqRqgK4QkZaEGcaDowkrA="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_0.1.9_1529381854087_0.15333786691516282"}, "_hasShrinkwrap": false}, "0.1.10": {"name": "prompts", "version": "0.1.10", "description": "Lightweight, beautiful and user-friendly prompts", "homepage": "https://github.com/terkelg/prompts", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "files": ["lib", "dist", "index.js"], "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublish": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input"], "dependencies": {"clorox": "^1.0.3", "sisteransi": "^0.1.1"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.7.0", "tap-spec": "^5.0.0", "tape": "^4.9.1"}, "engines": {"node": ">= 6"}, "gitHead": "b0910726bd866df2ce9acddcc23de01d7f1f6386", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "_id": "prompts@0.1.10", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-/MPwms6+g/m6fvXZlQyOL4m4ziDim2+Wc6CdWVjp+nVCkzEkK2N4rR74m/bbGf+dkta+/SBpo1FfES8Wgrk/Fw==", "shasum": "832cbf6116ecb121d6884e84643bb2cf92b3ed2c", "tarball": "https://registry.npmjs.org/prompts/-/prompts-0.1.10.tgz", "fileCount": 38, "unpackedSize": 91856, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbNH2jCRA9TVsSAnZWagAAjHEP/09Osm8cTCYzq/W/DLCn\n8k0Td2dp5EAF1eVHCIQlooyCPoXZdV0g+k/+ivsu9z+CmhfF0rFd6enMc8OY\neLjY/PljvS7Sww9cprFkB0mkE1Hqs/HvsOj05jIMiMEPgAqgNdBrL3i/+2Sr\n8F7axo+cbqA2s4qqM4Cvq7sKjPzUKNv76ykvV7w7qHPX/4BnrLh+Z792fACk\n3WqYqeUXgBTSu2qV9lGfwR+2fp4v4PFwmxlsqskNowJXJn+xUkfNe7xn6MlH\nZ12LNaa0hoXgK8+Bg8CxVXMzv6Wt/RlsnypRde1CaNqk81a3Egay0VmiZoqa\nKfOd4oyav/e3hUJlnAmC6GRcm5t0Wm6cxg/JFaSdmEI4vmHSObjb7DhYAXCY\nddZqxua1SuTsZP1qe9m6hynLjcC99A44bO0BfTCemYm3P2zamMkH1lgbCyaU\nsYvxciafz7h7Zfpf7wKK7xDiuRoQm1aso0ngrD8IR3DLH5cVPnnWCcPdzEZ8\nt/JlonbI+epb2bQVqsgX6v8PY0iqSGP7NeXhzi+YjXTue/BfoXryaIFs7w46\nLKV23d3TPUOXOJyIUL6hTPjREYNsDoMpU54UToW2hoeUlGClAmjrELA83KSe\nyE9s+ASmoX9vVkBN21sntgJ9KxzZe1ze4DObAlQAhG2S7gOTMSCiFiIidfmT\nuXD2\r\n=jKuO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDDnJEtUFhPz6ZGk34mjVDybVtwSb7QgWuED1YzZ00m9gIhANFm8mFgc8as6/eNddjr1KDkcJHcMzQsh44ZSycpcZFC"}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_0.1.10_1530166690986_0.4807273600719595"}, "_hasShrinkwrap": false}, "0.1.11": {"name": "prompts", "version": "0.1.11", "description": "Lightweight, beautiful and user-friendly prompts", "homepage": "https://github.com/terkelg/prompts", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "files": ["lib", "dist", "index.js"], "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input"], "dependencies": {"clorox": "^1.0.3", "sisteransi": "^0.1.1"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.7.0", "tap-spec": "^5.0.0", "tape": "^4.9.1"}, "engines": {"node": ">= 6"}, "gitHead": "aae368f38da9dbe7e3dd954f2ae612d298080e15", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "_id": "prompts@0.1.11", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-6ZFzPasWMEgZ/cDpQQshV/l/fWISILGjq4VctCipG6RVfaO4FAafGR8iKXSFxoHIYgPcPYuDupyLtVNc/aDG8Q==", "shasum": "fdfac72f61d2887f4eaf2e65e748a9d9ef87206f", "tarball": "https://registry.npmjs.org/prompts/-/prompts-0.1.11.tgz", "fileCount": 38, "unpackedSize": 91844, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbQNbzCRA9TVsSAnZWagAACzcP/RBHPp7Ghi9YWk7CPXLd\nMr115WzxNhDFBxuhhR8hgmqxt5mVVQkWjO9UhiL9RuPcevTolY4VKUwN54aO\nB7XleszmqElf6+YkTV788GXQDrBjzq5VgO/7IMUi8pfKh8MPca1rbW7DlWi1\ngoa1lblSIV3M0Ig8+/IhFb+5AoxiCiJXlXPBCb2NgYpxsnrJZCd2oFweuhTx\nQ5zrCL2ztc3qvjKvZIz58zzUvYpMhXjQ6+P2YgCUL3NMVUJsrQWaxfKSeZx5\n7oDNFrrQrmJqIJJSxNK4pYAdZ/D/lqWGONaEaafQmkLOV+RS7N01aBYZdS2f\n5/kz7Jx0ASpJSc2iZ1aXKaiwzBBlTEnIDlyZcG5AJ8+FbGwolioBeA0BU/jN\n8r7Mt6PoM1Tzbp5ATNB4My5bbxLD9nxhmx8D1v6QKKYR4z6LpzzQGK3KzOP5\nvLrd4Y6MQOz5yRyvnenhh9/i/47sNr+UfBIjtmDsgDJ/kOf0opdbhIctQgW1\nA6Si6H7HS0MYgl9I8OpQ2lcK2V2HDZuYiE6JqEtVZY356PJ/MIllT8IjfjwI\nf6843WFGmivwfho3k849kqymDkL9hQ/Uhku2sUzttq4ZjVIxDaipiRbC2AGW\nVypCUW7eLhb2VuHPstZ2WTvFoQNwazGRiN082IOOS/tPySbE3edRx2eGWyUR\nzBjP\r\n=fjHD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDIhazFFnR0Cp3W91e7qNCe2YNfcD5PgtB1B89slR9sSgIgQj4AIErxOtUnvGTGKO3mWL6pttZ07rp6IeNotEwwM0U="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_0.1.11_1530975987870_0.19221955329541318"}, "_hasShrinkwrap": false}, "0.1.12": {"name": "prompts", "version": "0.1.12", "description": "Lightweight, beautiful and user-friendly prompts", "homepage": "https://github.com/terkelg/prompts", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "files": ["lib", "dist", "index.js"], "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input"], "dependencies": {"kleur": "^1.0.0", "sisteransi": "^0.1.1"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.7.0", "tap-spec": "^5.0.0", "tape": "^4.9.1"}, "engines": {"node": ">= 6"}, "gitHead": "36328bef22c70cfc76a58e361ec91865d1716c64", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "_id": "prompts@0.1.12", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-pgR1GE1JM8q8UsHVIgjdK62DPwvrf0kvaKWJ/mfMoCm2lwfIReX/giQ1p0AlMoUXNhQap/8UiOdqi3bOROm/eg==", "shasum": "39dc42de7d2f0ec3e2af76bf40713fcb8726090d", "tarball": "https://registry.npmjs.org/prompts/-/prompts-0.1.12.tgz", "fileCount": 38, "unpackedSize": 91826, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbR8HzCRA9TVsSAnZWagAAgysQAJcgcmMYb1yjtmKMDBBZ\nJuC57oUR6ZWTFMFs+AusgTMn/E+nN2fgaayu/oHWNnEcDx0Ywiy8sV/DE0yN\nZcz2c+SqapaviPRVTIqu4iAeBKvEuufpvdQlUya0Ix6ccVlHFnxsL5xMofh5\n0vehfKzPbHr7sHKSj5D3l7JvAHJnO64M/qGXkcs6Wvrg+aEcKYxI26hBR0Km\nOVD4akmRkPOoObdqCgd0Z51+c6P05KRlOKCWODUehh0aocgtUnP3SNj3k41x\nqe6eY5DyPPSrfD55383QeQaE6dVKurL3j5TZOESAmj2xe8HSYumbT5wZS/rh\nIEYcm1esP8fLtjGcRJrMFul74zdOkya6dYMiu8TfgZU8BwUsnrxGQyL4iFuQ\nijFV9tq/BTAIjvX4dg59+nuLcAsGbtOdhrUSqfO5eYyAK0Va/BZyBwzg+R5U\nfbjahMT/jEw0PJpFpNLexnZLxfL2PZNJ62Wa85sYO6f7ZTkwyrf3CeBkjM/F\nP54TYpD+Y6mUTBM1ZVZF+GadY5F/FPTGAOdL/hpnQohQF/nP1w44Im0rzfkk\nQeuifTaZ56nTwJwZjuI0ivdQLD7bQo2p59y3N7MQplOd23ymUE82JSSUpXcz\n0Qxt8atw5PEMo9NKNCKv4jYQJ7bTFQEqfbg/9TpupzTJF+JB0CvJ2P8wOqOx\nAdDG\r\n=OO5x\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDtys0cwUlwhO7qTvArYZtxFtFot/1HAqQ0EPNnMFJ8vQIhALmp5lOtT4+po+leqnugITZRHB0LjqYq66kGMBB6C465"}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_0.1.12_1531429363811_0.38076602767865575"}, "_hasShrinkwrap": false}, "0.1.13": {"name": "prompts", "version": "0.1.13", "description": "Lightweight, beautiful and user-friendly prompts", "homepage": "https://github.com/terkelg/prompts", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "files": ["lib", "dist", "index.js"], "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input"], "dependencies": {"kleur": "^2.0.1", "sisteransi": "^0.1.1"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.7.0", "tap-spec": "^5.0.0", "tape": "^4.9.1"}, "engines": {"node": ">= 6"}, "gitHead": "8501013546099cddf165acbc045638cef8046e14", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "_id": "prompts@0.1.13", "_npmVersion": "6.1.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-5SXno8Svo4bo+aBiY0YjlnjN/ZIwMDz60dADwAxSAonDQiq8WKpB+gnP50D9PgPYtZ1MvpS4RoVa0dX4B9lrcw==", "shasum": "7fad7ee1c6cafe49834ca0b2a6a471262de57620", "tarball": "https://registry.npmjs.org/prompts/-/prompts-0.1.13.tgz", "fileCount": 38, "unpackedSize": 91910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbWs5rCRA9TVsSAnZWagAAGsYP/R4ZJvaIhcC/MwsC6+m0\nMgXtPvQ/VPKPTm4QmB2eF36PC4JvtYhGXDU9twm+mpdwwW1wR3tV4pSZq/yE\nCuFIFlGPs99RLEg002Q3mkz33hviCcMFVn0jYi2z+x21HM+fTXeypCj7vUu2\nkcvbpixcoojYEnWaZRfwoDGNjvQYPvMcaIT5Ijv7DloNVTI2Fu08xI/DBVLa\nUevf0i7oJm8d7gqMWNRdfVjpDf5h+K7/UV4LNIdpqeSDyeo2xHtRedpICqCX\nPr5w7aIBOdNz+boofTOgAEGpGFj1hJ/6GVSWUF2D4Lt+xdZ71Lv8cwJD/Zzq\nSmigSGwmLBSFXB+WLNI6kxNdPAn4L3NE/B0qXsQuMoIthWLGvK0ZLG+Vk7QO\njGOKsBaJfvzjZ+r+OkjcAkhdVFMjVbcmyqEAdxDKSCCbXqfdxi7ZXI0jzV8D\niQzlQ+9HVffKViaXvAuzwTAUGPOKAjjYTqXRGWq+6UhOBVRh2CsKTyXTJ+uC\nZOujc724zN3qM22/F+Pln4kIQr4Wp0FGg+fjKTcyeK+OGP4/rlK+1e8B0q9e\nRdiHbmeGiEQqQ2r4T84uS1+FR+UL124XlyCzihY5DVNJ7dOqMWyPYzIXGoXB\nV5qcahM5+Sh9KJ4Dhpc42FBH3pvz3jAY6vtERvA7tkvYAXA+GaQuJvSulXCZ\nRBAV\r\n=l4gv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCkY4VwAbPRKOkKeBYUz5SHgd7wk5OviecQ+Oqceph34wIhAIaeeE8ESOaOuNGBMZ+kn1C0bVxlEkKade7MyJJGwdvF"}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_0.1.13_1532677739700_0.00385717900316096"}, "_hasShrinkwrap": false}, "0.1.14": {"name": "prompts", "version": "0.1.14", "description": "Lightweight, beautiful and user-friendly prompts", "homepage": "https://github.com/terkelg/prompts", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "files": ["lib", "dist", "index.js"], "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input"], "dependencies": {"kleur": "^2.0.1", "sisteransi": "^0.1.1"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.7.0", "tap-spec": "^5.0.0", "tape": "^4.9.1"}, "engines": {"node": ">= 6"}, "gitHead": "0932a5daffe68ec67782fc6f74850a6456a37671", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "_id": "prompts@0.1.14", "_npmVersion": "6.2.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-rxkyiE9YH6zAz/rZpywySLKkpaj0NMVyNw1qhsubdbjjSgcayjTShDreZGlFMcGSu5sab3bAKPfFk78PB90+8w==", "shasum": "a8e15c612c5c9ec8f8111847df3337c9cbd443b2", "tarball": "https://registry.npmjs.org/prompts/-/prompts-0.1.14.tgz", "fileCount": 38, "unpackedSize": 92083, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZbOwCRA9TVsSAnZWagAA3IkP/AvmRD8JYV/nkAB5p1wa\nzDGGYZLNtln30ekfL8LjYB6Rl8Sp4eebJNykATmiOt4U+aO1p7DqpqlkcQ7q\n3p0aUNPhQ4pj98t35B7tw85rGbVBqsssiEWdBs7903OU2lfCQ1lLyID1yIYZ\nY359/duQxVzZCXpGz44PzmtL/yflj8AsjOCDUsOg1l1god7XJUtvDQh+uxs5\nycwm+8l6AoJ5R34aceXHDeVYiPdm6KnEGsuVuga7C8vJB0C+2a0gxMqnWo4q\nHCvRXt66KJ8OFVqQ6KyXFp/qhpKqQK6lneTdJdjXfVP4AZiIDD3lSwRLoOaB\nUUtkF7cnxi3RnwLzjM05Sb69D+1Q0Zgt9g8ktAsWR+SQuBYyJwbBOZf3hnjt\nJbHbqj8Sx5ptDzqFdX3aDrZYq3DJ9QDW1pcUT60wy0CkRL7FmQDeuwHJCvkO\nxEvzANHNVm0gSI3FpOGEHsxUWVP5GIS7nXGBe1CMMuTuhCO4i0Zk4nKsroIZ\nbRXsgIoFTv//pirRTyVkfdZsBNesR5xZ6bZr22X2Wa8nbRIhvMySkKSfEi9S\noyFPSWrotyFjIsJ+NY/4XcbYjzG0a3NounrfestsK/7+wQ4Gf/rGF3Oc6u7I\n/iYMlTfcd6NCPf6eLPsgcXXWWze3XfU3lejTQtBUwrikrfk9WdJON4toEWAs\nwE/2\r\n=+8kb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBjVNFaYk19TDPWo68Ooou7f1dKsaQfklkmBmfaPf6mYAiBed95G4J7P5JclQSbWeL/ip4wes2zrEB/HQMRl9pRt5w=="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_0.1.14_1533391791984_0.7303060005967956"}, "_hasShrinkwrap": false}, "1.0.0": {"name": "prompts", "version": "1.0.0", "description": "Lightweight, beautiful and user-friendly prompts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "files": ["lib", "dist", "index.js"], "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input"], "dependencies": {"kleur": "^2.0.1", "sisteransi": "^1.0.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.7.0", "tap-spec": "^5.0.0", "tape": "^4.9.1"}, "engines": {"node": ">= 6"}, "gitHead": "135250ce39dbb70b42ffc667f8a54fbeaff6c668", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "homepage": "https://github.com/terkelg/prompts#readme", "_id": "prompts@1.0.0", "_npmVersion": "6.4.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-yxzOCRV6MrZDQg/Q9MIhzGNDfYEQMdloblsrgbT1HLvHykpMhdU/LRRYX+h+t0P9l00QlN9/0dRxDmd7QoCIvQ==", "shasum": "c3560198e8b2e686daadd957bca585adad03526e", "tarball": "https://registry.npmjs.org/prompts/-/prompts-1.0.0.tgz", "fileCount": 38, "unpackedSize": 107734, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbg7HqCRA9TVsSAnZWagAA8XoP/iTKS+ccc4e8eQ+JUTJN\n5esRP7YblA/eZgXE6o7qLpJwdDH+Kq8u3v6bf1xBe0S098mEqOCIw4mcGzcB\n/KF1/A+qYwrbBc+Wv4C+THWjChFvJBUUYGM8ABnKtPfKw/4/KPYGHYvTeLRL\now0mh9KbUQJgcCRW9OTOIGYQY0WSqyQ50udaDRr/8dOjmJ2UDGSDkLSLXJW/\nhJBAudQatjknaVQoLlcGx2cqRhh0aPNgyyCcZAcs4jQCfo/am7H7WfE0qcX9\ngVfx0zFEmwC58eyI4+Nu6yXQMUxJ4TUXWGUevdqkXC4WGXtGMOREKY9/X7K/\n8GSK57qNqSRsiX6ocrOGKQ3vvOY65yp80MOYrrBv1vGHOJVew++XmEj8UvH6\naEwG+t0C24wW6ns/0s7yUel5cKKF2XZ1/fJFh0JgllVhT2YfKfBuHZV+YVfN\nCXQeg7867Bum/CVRW8pt+BcXPArjoVCWrgLLK/tIy+nP731vLvWRgjGGbW4x\nGpYpT3uzH1DgFv+XdyXTenLLvsKD32XVk8nJf17gcO0bKxNeGGnSxajuUUIN\nASaA4g3/wJvOo5BblnJBKw7b/44akNkA5pfcyB0/v6J5ssHH03K+/5kK5Sty\n2dq9D5zfvulpdAFoYESb25O5ZC/qYHanhfVsMJbGj1Gk0v6Igzx9vzwYeslO\na0AQ\r\n=oTUh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHT5Nq4cMU636DbRAkP4EJ1dSbvcDrdLM8Ed5Tz5HdfwAiEAlGUOY3ZAaUS2GPsjjYxjUy2HsuK6EeWGTqrnhQJTyDQ="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_1.0.0_1535357417500_0.21167067548378027"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "prompts", "version": "1.1.0", "description": "Lightweight, beautiful and user-friendly prompts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input"], "dependencies": {"kleur": "^2.0.1", "sisteransi": "^1.0.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.7.0", "tap-spec": "^5.0.0", "tape": "^4.9.1"}, "engines": {"node": ">= 6"}, "gitHead": "aec95dae3c6daf9db4d0b9f90337604c443da06d", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "homepage": "https://github.com/terkelg/prompts#readme", "_id": "prompts@1.1.0", "_npmVersion": "6.4.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-VbHgVvxagRhlvKA/y3UXdYZnvD9A5CMmBmqnxqW/UY6vLSnG7QaB7w+gZeZ5m0ZXUCY66fBCS1Qp9fn8BsZhNw==", "shasum": "8a8b33030943e8355e06f81bebc436f7aa669d42", "tarball": "https://registry.npmjs.org/prompts/-/prompts-1.1.0.tgz", "fileCount": 38, "unpackedSize": 107976, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJblUiwCRA9TVsSAnZWagAAma8P/3Vbqs7XK9bP0ww1OWyI\nv28luacIfbbzwpi0kV4Op9ft1/GnMOSlEBKzNaBE0e1KvAskssTd+MSBYC4J\ncxqtpb9UEPx0aQrIqMC4RnGZ6TOnE38pYcTZJX2ock3nX55U2uUZUcmFFg30\nK6tTeoLlKCogLp1VANaDv4sf4llmKfEKpPoXsg1j7qkMndpJC/wO/Lli0hTA\n/wrVZbB8ax6F708hEovUM4y5Kx6vJTXoJO25M/tElDD/Yi/oIQMg0YlYbGUl\nXT3PTrDfXvKFk82JQxUWwU1w8RJ9ld7YLtjYNzNkUTmIicw9m0lsfrA/PoB3\nE7ywBxutUBZ5UrfO+GyzCuH7YatUFN0e3gRUs9HcNlH9p5472/WcYsdSR3P3\nyfEP3VS4qH3H/0eHrmgvYrc1Wazt+b2xH0k9XlvMqMF8gdSmlCYORXGyEAib\n1kh0jiFmaKe3C/vUBvrbJuLuDJJ+zl+oELEWoYcJMY4aAooVVo0IxOx+xNVc\n4sgHFkvJITuWWbP7Hd8b2bHAj0csfJXALt2KoqYSD/nMJPG7/l3XsG5/uLoG\nIyk9A2LdkTP7gyS6bm8KtCcMuKcXUjH7+cR8UcPf/wJ7oDshdlPbR1q6oJ9X\nyRaPcv/6mVRLIUIqHrTSHblTcJ4jpO+aHZ6eacowQRdDBvwkRsGV5bKq/4bU\npj8o\r\n=1AnT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDhSVCpYX/WULrgqxqCSqVcJ/7GpdQa/8xRhdj4ypZ9wQIhAKJqO0DZC7WmIQ9pt/rhmYEqttrr8SQhGTfu1EJITd6i"}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_1.1.0_1536510127268_0.28897256428928975"}, "_hasShrinkwrap": false}, "1.1.1": {"name": "prompts", "version": "1.1.1", "description": "Lightweight, beautiful and user-friendly prompts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input"], "dependencies": {"kleur": "^2.0.1", "sisteransi": "^1.0.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.7.0", "tap-spec": "^5.0.0", "tape": "^4.9.1"}, "engines": {"node": ">= 6"}, "gitHead": "f1dde7c2b113519e523c44c974b769d674ddff8d", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "homepage": "https://github.com/terkelg/prompts#readme", "_id": "prompts@1.1.1", "_npmVersion": "6.4.0", "_nodeVersion": "10.5.0", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-lC0+ifgWNKhTNF28Wj41TOXE+gEzrHcDqkCRHMbv39afuGT1ClekTgcVF+r2VuSgNr3Fy2hq6Pu3Mlt43u+QlQ==", "shasum": "c687590e472c08cd84cfa4b8694b1cab5de45144", "tarball": "https://registry.npmjs.org/prompts/-/prompts-1.1.1.tgz", "fileCount": 38, "unpackedSize": 109175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbonhcCRA9TVsSAnZWagAApmIP/RbzSi7QZnCB7NxgmwrF\n2ITkWJA+dI62P1ni7hN7GaJwrYfL2Nq6Md6WIPuMC4nAJVOO/9KhHLfuOqSB\nP6cSmVOb9ivjyBYwCe7XGjnSqe4KSQhOjBRMf7aJCV+L73rigv57d8WY06cu\n1iBd77MbbidiSc3K+bTUJ/bXpb/QCxJUqFw+iUz7TRR7zkt/Rvt8m0zjcILV\nRJqVps2iLgjlz5p4IcQm6giITyzrJjD7YDDS5fmNSIZegPJU5nhp4yunoXyn\n+OO1iiHJEtfpTSpN7LF6y0OlJjK1O3YZ1wfJ42zHqq30x8GKiGJXfGNeuABq\nHuc+lkdcujGq6Yo2DOxDWL6yWVECGRnX1A948TJI5ed+FhLJDuD+VhQIfndK\nHBvSXgnLeKIc8i65w7LgFh7sHGg/2EzCyvfhLmuPrZ3K0egUV1+EBH3DJn+M\nTLzMT8RwXFWgCbUnddiLibOnSLu2agWSkrFtP0k1IAAj7fbjcD1TzBOzGtCw\nLGh6arrdyLOlO0fNJiPGdHRlBNFLGAm+vPrScWjOhL6iK2Ukz3GDXpPDkcIu\nx60HYItRQcT46Fc2gYLXNPPfFufBCKrwRvt0zm2kiwd8QzPPxgYU4R8tbsYg\n9bbXW8aKo9g7zbFlgwl1eZ+iRf/I3FaIIQyQvjngZhlcPqJSEClMixKNz7Gt\nMMC1\r\n=mAxu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICxW+B9JktvqX5uSwDPjlsOSqgC+veIrgqdTUyvh9fJSAiBnmGd7/NZyrKAxsR4B6RQ2/jVtmHYyXZW4AvTpdoAyGQ=="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_1.1.1_1537374299343_0.5891071531788536"}, "_hasShrinkwrap": false}, "1.2.0": {"name": "prompts", "version": "1.2.0", "description": "Lightweight, beautiful and user-friendly prompts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input", "command", "stdin", "menu", "ask", "interact"], "dependencies": {"kleur": "^2.0.1", "sisteransi": "^1.0.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.7.0", "tap-spec": "^5.0.0", "tape": "^4.9.1"}, "engines": {"node": ">= 6"}, "gitHead": "d6c0c53d4337566b17878a685eeeb4c799984bb9", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "homepage": "https://github.com/terkelg/prompts#readme", "_id": "prompts@1.2.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.11.0", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-g+I6Cer6EefDTawQhGHpdX98nhD7KQrRqyRgKCb+Sc+GG4P64EWRe5DZE402ZNkwrItf97Asf0L1z0g3waOgAA==", "shasum": "598f7722032fb6c399beb24533129d00604c7007", "tarball": "https://registry.npmjs.org/prompts/-/prompts-1.2.0.tgz", "fileCount": 38, "unpackedSize": 115717, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb10qCCRA9TVsSAnZWagAA2/0P/i9DEAUG+9UYb4Iuo7aW\nkgyjufF710PGU8nHGJasd1UFGEkq8U8VYy4o+cbCvqDAk+U4Q15k3Itp6bfp\nsJxHXqJ1gZPFMgK3n92BeNTDDdwMPW+HhxJI8JDtSXmKFADAwD+7pONF2tBU\nZkyibH6L4Ecr9HNU5ZPyKN7PhXiz9hFgPUTW7kEop/KUxXEqIEuYwI1hELMW\nN1ywGjl4B6eopn/4YLMVfoTmo+r/vhjnNi8yujQ77VSM/NstvAXtC9GG1SUK\nd5d/Q1t1tGVL/PXlf0ThYtzNvOTHNEtIE4CGzXlXtgsHwTNT1jbGVKLu67ul\nCVxsuim+Ie4J0yxsUJE4rUQ4Bhw3cXo1Pwvo8n7aSfLZIJkITQmKq8KX8Sjk\nd9kSwDXwHuwEuwRq7doPfovdD1H3aV3bSgVfaFLySzA7khRLM/ohAhpB1ipJ\nkAN7DOAm5IU7N5/qJDNGRobi06eXWfCinzhTimHQ74RFFrCyW1zgokkCLCKR\n2hORzDdvS8fMqC0HUqdIanLQ8iQ/2L9KYN3YMmyOwY7+PqKeoZetZS+spczn\nZjcFFReyEtg/aG2Dz3030VyvZ6CLK+OpjRClGoaru6F3J4ZljVKKSj/8uE3J\n8hlbPEJs1huqoO4XIA29ldRWlCUM7K5wFPV6vrYLF9/+go1iY1xHDWdxt6F0\nXw6b\r\n=A/EK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGK/Ez6iQ9BN51xTPHlx/X0aB1Ne98BKt3Y8MX3sFlLvAiAF2KLjKwTRhSYvr+O28T9sMWKfLcqgLYaAQL7SIZoJsw=="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_1.2.0_1540835969798_0.25854367884233853"}, "_hasShrinkwrap": false}, "1.2.1": {"name": "prompts", "version": "1.2.1", "description": "Lightweight, beautiful and user-friendly prompts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input", "command", "stdin", "menu", "ask", "interact"], "dependencies": {"kleur": "^3.0.0", "sisteransi": "^1.0.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.7.0", "tap-spec": "^5.0.0", "tape": "^4.9.1"}, "engines": {"node": ">= 6"}, "gitHead": "c62f91d3b3d3ef4c912672c448535a289bb766c7", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "homepage": "https://github.com/terkelg/prompts#readme", "_id": "prompts@1.2.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.11.0", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-GE33SMMVO1ISfnq3i6cE+WYK/tLxRWtZiRkl5vdg0KR0owOCPFOsq8BuFajFbW7b2bMHb8krXaQHOpZyUEuvmA==", "shasum": "7fd4116a458d6a62761e3ccb1432d7bbd8b2cb29", "tarball": "https://registry.npmjs.org/prompts/-/prompts-1.2.1.tgz", "fileCount": 38, "unpackedSize": 115765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb7fv/CRA9TVsSAnZWagAAwKgQAI24t102+cKS5G+P+pkf\nE03jhY7/6HbDHXj6V0/IuASEfo7EsV/5PwPmKyUu9AoiNBxbouhREx+WCoZs\nfSRmUg6OvkDklJqFImpJAjf2MFLwQVyWAcPCQDfJnF2Q1CaIXVBr4sNxtWwT\nzkxB8WOvRvE6+yj8a+H29LUF8wwmyOjXb+mjIEKTtS9mJnmTILLDpW2BpbRY\ndlQyGVEpzcTOW/Q6zvayeG5HOyhnXQj9GRzpJwhCDuZVk5Mw78Hw2KiENwAf\nD3aftThQDCVQNBXhZSXhgaTq+Do8/3QU0dM7Xc0nersWx4Y090jw34OKoKol\nUL/JDpIDy1rCoO44p0Ud2lecWjJr1PhcudOrFxWOvCeg3o1XSrCXtKaTiryx\nwKmrtVXrr73UEwnwFVXEXDKw3vxpTxByHtiIQW74V1Re1WGmnS7QKG1VKyux\np+B/dNbOnRbHQajuCAvG3PcBpWoaDaO7cbPhybVQOPG/4e88fCF/srrXKFDR\nHSht+k+mGm2uEFnObGWvJ2keMEPjhwQvXusA9qrqAMJLLlP2u09obxVkfgDW\nQQt5rr9R1voKQ5/LHVgUQuupE2DDO8m2Pzj4VEA4zr8X121HzOhnLtvOkrBJ\n0FpVHCv974O9tEyc9IAnjQdq3XE5IJXiKz6DDUgcQuLdLhmO9KaPCaRoCem+\n+VL0\r\n=Tfez\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDy4MXPlEKKWJauiiYL3RGWqGHg8dHbD58KO8uv57mGkwIgAW4m5dvPpPYQxJbNJd6l9uT8UYD1qb8pPa3Lk3SrVDE="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_1.2.1_1542323198713_0.47315212058295497"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "prompts", "version": "2.0.0", "description": "Lightweight, beautiful and user-friendly prompts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input", "command", "stdin", "menu", "ask", "interact"], "dependencies": {"kleur": "^3.0.0", "sisteransi": "^1.0.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.7.0", "tap-spec": "^5.0.0", "tape": "^4.9.1"}, "engines": {"node": ">= 6"}, "gitHead": "9d23c50e05c091d72ec1ba01514e6f6d62440a35", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "homepage": "https://github.com/terkelg/prompts#readme", "_id": "prompts@2.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.11.0", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-DG8VBhhzNKj/61pAFa02SBR+iOjU21uLTalRTBLLwHv1KcTQlgZloXQLMIWSXGaqk2Yq+Rd2GNtjPdUgYFcT0w==", "shasum": "d36c6c044ea58825c9f531949e30f9e8bd39a29e", "tarball": "https://registry.npmjs.org/prompts/-/prompts-2.0.0.tgz", "fileCount": 38, "unpackedSize": 116369, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcCXJeCRA9TVsSAnZWagAAvc8P/iyF3r+DtWunDF6SF6Ug\nHAzuZz9Nt1DF0ghNYGpXoOCvi/+aFGLtd8YG8YSqoVjESC4lhNkf/eulrc7O\nVsU2pgTtuLMWunMVpj8DGkLA10VtCCJNWDgt6R/QS+/ZE0VATvLvEcBd9bC9\nWkPgLzA1MIdw6s0brGG5BpMYHLcGNDVG07atLFuOH3D8dLhDGO4VRmM0VQse\n2AmoAG4bm8yVlWG2LOQbax2RGHJgEGBfguBw29xCfuCUb4f6CdYUiz/4NBl6\nfsvohZQNS9Jo/sDozY8Mpu6KXQMkQ0a9oqTx1xcF/J/L5Y9jhVCiyCSWv10S\ny0wSaM4ukNqD9H2S9Z1Y3jU6kVQtFOMcqRYtpiGpx14OMcHHK6nNya8eDboL\nfLQCELGLAYDUSlWsFFT4NpUCfoWxcCIjbsIeBkA6kZTHd1QJCX75QAdcDrMO\nGavyg1ztSX0Hq/5pc7a1xhV9I8VUYV983oJwhMh4KTOwIMDXPUkUgYiErLEa\nLMrlkHbbD0tmHK51ors8qXNydQpfUA1QiDh4lJvgYKGmehTMB6a9QLbzzUrd\nksAYKChCa5M1OYT/Gf+pQVWmy1pCU6je0vXiw+60yLhPSJSKgAiTsSkgFsLx\nA0i0SfMhU+ZmTCySu98gSNjHKmcAsocnfsvvXQ7imkHWbka891wTDZEVZx9k\nU117\r\n=YhAz\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCkuQGq2t+s40xbgLn6bL1a/FXZDQXzLhuUr4vF2ZuoNgIgJCJZBAy4umj449/iQVRk7a3rEH+ollCzgLsH6I96kWA="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_2.0.0_1544122973939_0.8528721345410399"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "prompts", "version": "2.0.1", "description": "Lightweight, beautiful and user-friendly prompts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input", "command", "stdin", "menu", "ask", "interact"], "dependencies": {"kleur": "^3.0.0", "sisteransi": "^1.0.0"}, "devDependencies": {"@babel/cli": "^7.1.5", "@babel/core": "^7.1.6", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/preset-env": "^7.1.6", "tap-spec": "^5.0.0", "tape": "^4.9.1"}, "engines": {"node": ">= 6"}, "gitHead": "94ec6e2492c8975a8a49abe7b14eb82816fb5d83", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "homepage": "https://github.com/terkelg/prompts#readme", "_id": "prompts@2.0.1", "_npmVersion": "6.4.1", "_nodeVersion": "11.5.0", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-8lnEOSIGQbgbnO47+13S+H204L8ISogGulyi0/NNEFAQ9D1VMNTrJ9SBX2Ra03V4iPn/zt36HQMndRYkaPoWiQ==", "shasum": "201b3718b4276fb407f037db48c0029d6465245c", "tarball": "https://registry.npmjs.org/prompts/-/prompts-2.0.1.tgz", "fileCount": 38, "unpackedSize": 117826, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcKJOMCRA9TVsSAnZWagAA1qcP/2zLo1b9ycSQ8uXFCTgW\nS+nSD+5+xKvOFo6LgRvQVoP0MKKlzOkxpe6mLo0vhY2PBMvnTl5hcIUkPybY\netmc2IxQCUuL/Up0D6VIweNPH3QeKFuEcgtPCjxAf3OjoaE2LJnAxjO8BLcW\n2tfolBPY6gaG1aojIChGAezwI4QTvH17xno3xwn3L1DGmsr+WuUVPI8L1MOP\nrQ4HIC6zuCTBKx3r/QYN1PSz7zLKKCQszT1zZO0RuvZH2yYI79bwRAEyWYer\nL1qEUZmoZrxYeOeMjwqhe0fRvfWZSmBkLmrccFKnRJzJ2TZV1vPwd5KNUxSl\njsAVxENgIKMEtPfZWYsSvHz+VcaUu8wMAKR2HF0gXD3rdvioChHdkU3mPdcU\nd05Hmqx++F722erLD6b0XaFzEZy9bDWRitZtAn7QX4KiMx2RqtzxUvg9LYHX\n5hKKswjRhJvycKG+6Fe+Pk4imeJGCS0al8+VQdS1Sbuzqg4syciOFu93udQL\nTedLfGWlbIepRO4qWEm5UfbKKsndKsOt93udUvweVokjmkp8yisHrhBCdS4u\nZwoWM4NoVVJTzxHPs6aqk/scZ/lEGd5iais8jauAWJx5PungVyMnpqMfuuOt\nrTCzd43e+nC0FYu+n3F7/aYMYPSwb6G5i2sPiRh+rez7k0epXTsbecqnrJrt\nIHJT\r\n=Zkde\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD6hyy5AYG96rHWnEzmpMllBeZ0KBfiBjY7lukUegDS2QIgHtV1oDtc0/C+BDm6C1i8gxxyDWStQqeHPB1I9NerL8w="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_2.0.1_1546163084213_0.630296291771349"}, "_hasShrinkwrap": false}, "2.0.2": {"name": "prompts", "version": "2.0.2", "description": "Lightweight, beautiful and user-friendly prompts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input", "command", "stdin", "menu", "ask", "interact"], "dependencies": {"kleur": "^3.0.2", "sisteransi": "^1.0.0"}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.2.2", "@babel/plugin-proposal-object-rest-spread": "^7.3.2", "@babel/preset-env": "^7.3.1", "tap-spec": "^5.0.0", "tape": "^4.9.2"}, "engines": {"node": ">= 6"}, "gitHead": "81aae85233240aa280948ff95a686058a21c1f00", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "homepage": "https://github.com/terkelg/prompts#readme", "_id": "prompts@2.0.2", "_nodeVersion": "11.5.0", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-Pc/c53d2WZHJWZr78/BhZ5eHsdQtltbyBjHoA4T0cs/4yKJqCcoOHrq2SNKwtspVE0C+ebqAR5u0/mXwrHaADQ==", "shasum": "094119b0b0a553ec652908b583205b9867630154", "tarball": "https://registry.npmjs.org/prompts/-/prompts-2.0.2.tgz", "fileCount": 38, "unpackedSize": 117891, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcXdB4CRA9TVsSAnZWagAAQAoP/1uXJq0OeUvbwt2mQ3ds\nmO6/KrphAamX0b9SKufhPMDPruTIkIdBwIWAsCvVxyIr0++SBV+wK39bVjY/\nWXb4ilTM3koKcSwoBcb8u1oFTqpwbJdkadfxbzPIimGnKoH5HW7Mmoodqd2f\nu6XQb7j3ohmwqFSbB07zNVmJI06NA2NHBNsRpNnVDnAiAUEDIR05yuDJOIHv\nOSc4gn8iVrn5UqYK/ecxVhnMZKGgC8SkSVhghUPkA4zOD6cG5iozbgKg/ehM\nX7ufhFoXLvTbWp23q65fdoueCALZgtJsSPaSUkFAR19Os4ixvrSpsczmp/5w\nRgr7LJ07kkxqVt9y3jQzLLKKUgtfaw4wNGmWXZQ92F4qMruNj1Zrf3NtLXkQ\nTot9hEWA4B+HIR4eDTkjQmkvlUxtiaVLEydT1qqmg5RprhDOM2Nt8cdNh/Ov\niejXKGgCYQn21ml+m4Y1DjeJ+rV9oyt7oKcH4jaQEO7n0l4fzly2n9oYuYl0\nqbn/nRZyoNWQNjB+AjrdV1OS6xhbgVOYXEU2hRh0N8ymJHbVfiFxdXQiXTl2\neUjUnejp48g59aqFxXsYmXmzKLnhQJodQvlcUGR+yji7Dn0RJZucCyCROZjG\nJRtQ4hYnjaZg3iIDSTVZKLA2Xg/jgspt/dU283Hf5rhU38VvtOP7BCnxMzqr\nCVT2\r\n=1LHl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBZmCR6fa5T737yVD9feOo53ojZqhEmP1YUemCScL+tpAiAXlneH+IdIuqETVDl+IZNekhpfSF8nqPRL86wtdSeT3Q=="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_2.0.2_1549652087797_0.037914427079088986"}, "_hasShrinkwrap": false}, "2.0.3": {"name": "prompts", "version": "2.0.3", "description": "Lightweight, beautiful and user-friendly prompts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input", "command", "stdin", "menu", "ask", "interact"], "dependencies": {"kleur": "^3.0.2", "sisteransi": "^1.0.0"}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.3.3", "@babel/plugin-proposal-object-rest-spread": "^7.3.2", "@babel/preset-env": "^7.3.1", "tap-spec": "^5.0.0", "tape": "^4.9.2"}, "engines": {"node": ">= 6"}, "gitHead": "605be8ab7ce79e2800ced6f9f7d4f4ae1b838bd7", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "homepage": "https://github.com/terkelg/prompts#readme", "_id": "prompts@2.0.3", "_npmVersion": "6.5.0", "_nodeVersion": "11.9.0", "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-H8oWEoRZpybm6NV4to9/1limhttEo13xK62pNvn2JzY0MA03p7s0OjtmhXyon3uJmxiJJVSuUwEJFFssI3eBiQ==", "shasum": "c5ccb324010b2e8f74752aadceeb57134c1d2522", "tarball": "https://registry.npmjs.org/prompts/-/prompts-2.0.3.tgz", "fileCount": 38, "unpackedSize": 117925, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcaEzkCRA9TVsSAnZWagAAlOQP/iJ4AhkU8PHwjRtblc2c\nUUp3BxXxx/nUaZBprfYeGJmmDHhgSu6835UdUVUgxyTfH2HCU0ihdcspootC\n+qOd8IsLnJ/ATjWj31nBaSTXrlZ37BeQXzm534+uWXGWyeb8tuHIeNoJ3lgk\nuBWjLe+V9lEMpbULmzHP2AtZ0ruY99RqXWsDNvYhAH6lhAq0312nCOHt6OON\nf8fxo8y+xi6wE434O/rqr9Nl4UQdozczFovnMTB98os0HMXfaX7WSdQ64DtJ\nG+UajWgVIJAABZEmL3CiTu4Ea9z0eiEpCvrFygchEEqhK2wzoL4LBrsS+kTE\npKq9HoN82mNWQJ+Ksfr+15baoOZS/lsuytegrTktG1knBCQwoLXnQIxvfGjf\nwswT2M7RQG8fmhYmE+FwyzaYkh5NZsnbj6TngL2Noz7nHeBdw2sFfE0PCH/r\ntfZZPH18HnGiNUnVgfF5nZuQ5YN4Sb5/0uXePQC2uX0qowOaYCVU9RjVYfFx\neejLP4E+PtMBbpOLt7DMHT7cFk1820ISG/HBdYAsxdGP/iLHrFSzBXdYI34A\n/x+osGjawPka/3G3gJLgyRwVIDbY4/uRG4DhPyvOazqim+LOa6/ws7cC+8qx\nNfWH/cUpRUfAtOk9lK97bv82KCB2qGRgxCuUf00RtdDzYxT8PRNJUuDSQRU+\nxr1X\r\n=xU+g\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHwjwtX26yPIDOf46Fz7pAakCCt0a0uweQvnS2oZ1vnFAiA32ipINQ4rksWt1l6ginm2aYDare4BeyHxyese82J5pA=="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_2.0.3_1550339299323_0.4764073290946105"}, "_hasShrinkwrap": false}, "2.0.4": {"name": "prompts", "version": "2.0.4", "description": "Lightweight, beautiful and user-friendly prompts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input", "command", "stdin", "menu", "ask", "interact"], "dependencies": {"kleur": "^3.0.2", "sisteransi": "^1.0.0"}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.3.3", "@babel/plugin-proposal-object-rest-spread": "^7.3.4", "@babel/preset-env": "^7.3.4", "tap-spec": "^5.0.0", "tape": "^4.10.1"}, "engines": {"node": ">= 6"}, "gitHead": "c75d5673614cd961125590345919a8e1f718082f", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "homepage": "https://github.com/terkelg/prompts#readme", "_id": "prompts@2.0.4", "_nodeVersion": "11.10.1", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-HTzM3UWp/99A0gk51gAegwo1QRYA7xjcZufMNe33rCclFszUYAuHe1fIN/3ZmiHeGPkUsNaRyQm1hHOfM0PKxA==", "shasum": "179f9d4db3128b9933aa35f93a800d8fce76a682", "tarball": "https://registry.npmjs.org/prompts/-/prompts-2.0.4.tgz", "fileCount": 60, "unpackedSize": 153012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcjR7kCRA9TVsSAnZWagAAZoQP/RvTaZuTvA3qM2F46A/H\nVghl7nEYYqJHjRxFROONmG2kjXj2isXQk3GvPEGdbEveLwy+n0k3Ynu+3ZiF\n54+4Rl2asw30tMHWbfL1Ec49mxp5tja6/hib1cjMm2CuVZyRKNumPR/K4BPd\nBMs/ums0JsxZRDPu6ig9css6NGT5I+ThHmnBOrD4q463Mtctl3OUv5Xo3k3y\nQLcdFqcQ90Lw9YaLepgKl2W+6GA5Jwh5/aERoSDyfCgHRK9k1mS69LD0fpxz\nhh7GeC57tzvGoNDf23y4yUY69uLC4oNgXWMa1SmbObDCtR0h46iPfGeNZWwv\nO/N4MjHZxY0xPOJT9BhpVX/euu7Jbw6wMlXIZSJmYtM3g+MwQLd+PxikYtde\n0OkqyVezHPlrtucreYAHvN8Q6dbyAbvenhtCaBH5+wamcwGOtausGoB/H5C9\nmyz/9BkWFDgszPeXGSIWhve92lcMT/v8UHoxMZUlyJSh0QTpHOypeoxagB8l\nKjsSTmDEcA6XpU5MBM4Lkdsjxdc/IDm9Pp8isDkrctCYz+YIPABC51Vi+31B\n+qfEnScjlpqPlZY2LL9Ft3rAgS4dRg4alPKIl9gisK+VcmusmPD8DdXsk3XL\nvFhMdqrmAeBL8qWH60J3LFpsg2m5yqAQOSRQ2UagrftSvQj7nCfVrJjE2nFI\n4oim\r\n=SweV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFvgqndtqql9sAWX9Pba5Z0J/lPdUuNM4EjOwGg6lgDqAiEA2eWtq0aRL6BVjWRTZcW2Bcy2XzaSk8BpWfHj24973oI="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_2.0.4_1552752356180_0.6178370900707724"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "prompts", "version": "2.1.0", "description": "Lightweight, beautiful and user-friendly prompts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input", "command", "stdin", "menu", "ask", "interact"], "dependencies": {"kleur": "^3.0.2", "sisteransi": "^1.0.0"}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.3.3", "@babel/plugin-proposal-object-rest-spread": "^7.3.4", "@babel/preset-env": "^7.3.4", "tap-spec": "^5.0.0", "tape": "^4.10.1"}, "engines": {"node": ">= 6"}, "gitHead": "482874231f6a19b65f21dac30e229385b31757bb", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "homepage": "https://github.com/terkelg/prompts#readme", "_id": "prompts@2.1.0", "_nodeVersion": "11.14.0", "_npmVersion": "6.9.0", "dist": {"integrity": "sha512-+x5TozgqYdOwWsQFZizE/Tra3fKvAoy037kOyU6cgz84n8f6zxngLOV4O32kTwt9FcLCxAqw0P/c8rOr9y+Gfg==", "shasum": "bf90bc71f6065d255ea2bdc0fe6520485c1b45db", "tarball": "https://registry.npmjs.org/prompts/-/prompts-2.1.0.tgz", "fileCount": 62, "unpackedSize": 169250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc5IqcCRA9TVsSAnZWagAAW2oQAIzW0plk8AjHsIr4+XqZ\nb6AOrYpwJ+8n5cfukNhMKkuwrGZ2skCR2Wq2U1MwBXlOLxFiszotIWX/pXn7\nOW0y0AUbxv1RMdSp9Nh89D0NKwrUCUNqlh6SSOp1mra39hbR0PxPdQEGhyL2\nWIJx+odNNoTLHsy+X0cNhJxI0msoQt1BF23KNqGomGqsKqoUk/ygSpMK5l2k\nc/KwZ3OzFymecJgQ1hIIcxqBC41Wde0x/80J3RXcclGAiM2vlKHJGoktfB0z\nyVHxLK2JSOQ1BqMHTWz5kqhZD0csOgzn9dihaHjyNkcCfeHHpC/yl34/l4Fz\nP9TJV7/BxXUgPb8ZhI/YZ3OrMjMMQK4C0aL/7LJRD+rQEQAwU1NJ+LcpWpoq\n2ri5d36VejUufqd2LyBk2v/SzItggzDHhlqTu0K9jou1RPr1us4Uminb3VoE\nG+JW1Ez0HxwN0jaEclfhErfwJ50XEJ1AEEXRcCQ+2C6ck6aeHWnLJiEMNTiP\n1+Td42VmGxQRlgSj0NPh0395XoijmI3u/B55jZr+qasjukxLZjy/qsG8Zi83\nwQspIHxy0KDCR4m42Gw8bXhyoHX7LlfZ9CDCVfDGGvzHYA/TtYpLaZ4bg9Yo\noSznaGubPEHl5NcKL/SYAta+i18JzHNyUB/FIUN8bgJoLFHAJa9Ovapw6zo2\nETVN\r\n=thCm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDbZ/lwob1yuK68gJIO/+YLoDP4UCL0IHrxtrSK/je3gAIhAJXAHLPHK5Byg0zFgmIVt3zPnSkIerubHnxkLv7jIhWY"}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_2.1.0_1558481563408_0.71765990547717"}, "_hasShrinkwrap": false}, "2.2.0": {"name": "prompts", "version": "2.2.0", "description": "Lightweight, beautiful and user-friendly prompts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input", "command", "stdin", "menu", "ask", "interact"], "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.3"}, "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-proposal-object-rest-spread": "^7.5.5", "@babel/preset-env": "^7.5.5", "tap-spec": "^5.0.0", "tape": "^4.11.0"}, "engines": {"node": ">= 6"}, "gitHead": "c7ffd5b218e62a66620fb360bb78acceb3fac3bd", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "homepage": "https://github.com/terkelg/prompts#readme", "_id": "prompts@2.2.0", "_nodeVersion": "12.7.0", "_npmVersion": "6.10.0", "dist": {"integrity": "sha512-JikREJP4nOyQXcYxRKkvCyHvAba4RZ3IOq2ljlLzvmW1rloZ1JT4+9QCGaLva/7Q3h5XL1crTtQ426XKWhV+7Q==", "shasum": "dfefa4ac4cd44b36a5d2f827e561e67a2b8f4168", "tarball": "https://registry.npmjs.org/prompts/-/prompts-2.2.0.tgz", "fileCount": 66, "unpackedSize": 174644, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdR+dRCRA9TVsSAnZWagAA2W0QAJ09TeFZ5xL8zsdJJsNp\nfY9AuJsfm50frR3qerP3kvyjxVlfG+bsfAr7f7LOtBd3sqyLi8EzmLI0OA8X\nuGS2IO+UYZMHtMBuRXifbKjS7tDpSb1LLmz3/UQ538kF8iCENm8buIw8cRv2\nufGfAe7bLWhx3TZvJSfhGmzV4IgPN4ZDvOZXEkPJLQBIIyGAYreHikIuwNdT\nlVBi//iF/GdrAlx61mflz+LkREvs+Hc9vh7AZj9x3U7YOjf7sLVh84qbu7Mm\navplfNMt5G/kLW7Gr8WykO0U6pZUU1VOtaHN9wv8+TojaFmsHI0c9QSnqsgb\nhub8elwfSpfKR6kwZ3lHTcFGuISVbO/CqSeRI/gA0Ur3Jpkr6B2LCzY5XnU5\npEonS81e5xRjd6hFQgkPNN/RznchlBja2uqI9PGtGnBM6XT5U9ZodxAONMV3\nd6syy23bAgTVF3zRbO65avLNkHp/ZtRNm0091P/m0fJOB/Wf+THS+61wUFC/\nTL7VDmhSJLhoLjXKWq+z25lJx9Yg/JrQOo4m7Swx20kCrWBVoAWRxIejft0I\nqikD4lcR+/7o2yTe6MF00CCWkkPeHVurKiD8pttWUwKjMYNkUcWG3REub3Io\nBIseV9+cGbEHk1GWfaQeekWE8yghLk8Q+/JziXXkuAPzLY1Nl5ljyQ35HoiS\n8QcH\r\n=OEl5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC5aEASmaMefoj7bWFXpd7MlgDwzINf1BbdNdBE//9oxgIgFz1CQQNpQMLRw1oSebT7PGAMm6snIob6hG+/fKeYapg="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_2.2.0_1564993360873_0.23270921178306536"}, "_hasShrinkwrap": false}, "2.2.1": {"name": "prompts", "version": "2.2.1", "description": "Lightweight, beautiful and user-friendly prompts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input", "command", "stdin", "menu", "ask", "interact"], "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.3"}, "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-proposal-object-rest-spread": "^7.5.5", "@babel/preset-env": "^7.5.5", "tap-spec": "^5.0.0", "tape": "^4.11.0"}, "engines": {"node": ">= 6"}, "gitHead": "201f5eba463501d9271c6bd3254ab0cc0a94132a", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "homepage": "https://github.com/terkelg/prompts#readme", "_id": "prompts@2.2.1", "_nodeVersion": "12.7.0", "_npmVersion": "6.10.0", "dist": {"integrity": "sha512-VObPvJiWPhpZI6C5m60XOzTfnYg/xc/an+r9VYymj9WJW3B/DIH+REzjpAACPf8brwPeP+7vz3bIim3S+AaMjw==", "shasum": "f901dd2a2dfee080359c0e20059b24188d75ad35", "tarball": "https://registry.npmjs.org/prompts/-/prompts-2.2.1.tgz", "fileCount": 66, "unpackedSize": 174644, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdR+l9CRA9TVsSAnZWagAAXawP/39rS2oJ1mcY+o+v3lrQ\noOjBDYFkQStQ5Xgu3qE3TY7ogLL5Iq3KnDant10/bWJrBftkGK/zVnuwbUi8\nwW015C1yfsgHAfxM6/kYovKpOJIT4szLPsrARJhsQvjabQFYtKWsbtCPLjKS\nywxGrv2kuivtn7ts/jEe+opBWMUPd3KIgOVN4ecIOfRs/KyJvUGkqr+BOoMR\n33y4kceeNduCw0huiksjEyjZIUs/KsCCREQRyPfVpcz7/4ipi4FGn0wUsO1T\nAJTMDOTnWU7Vw3JQt1ouonIgQzeB85eFk/QKRap7e72p3Qz9nZcICKvXH232\nbM91ksvpds16lokwQRm+DXVVnSneZGstc46cQ9nMBsMpLSofLpQuk1leAw+l\nfr0WjiRhNoi4F04gxaE/a5joKRwEeV/rSG/KcFquF/0vZZtwR7gXtpqckWqL\n/nQBw/fEVcQ7w1YX6rfM9llYgLr2ipSEhciQSF9H1ZlGE0goMcFgiX2Eca67\n1/SGgFA3Zw8WbJzc988kQa1xcTQq9KL2Ue0AYncVcYAGQHJx1RVk/1aYpjBY\nJCsx6WkBy1mgg1r+r+V7pJuLaAu6b4cdX7Hzhq++i3oubsjGirz2bnV4lN6E\ny4HDOGfkuvOBmztdZFIVG7zl4XgfLMRCg0SdLcPTAS+pgJ7KrCZ18AjA6Ok1\nj407\r\n=qXjR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBGWgXXWc+XF1YtTLWibiAMTKG4e6G+Pa7+os5Eu1+XWAiEA9jDuSwKg3EwSeodpzeeXVMcnaVkVBCMwHQBp6eyMTKY="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_2.2.1_1564993917119_0.9690857903326737"}, "_hasShrinkwrap": false}, "2.3.0": {"name": "prompts", "version": "2.3.0", "description": "Lightweight, beautiful and user-friendly prompts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input", "command", "stdin", "menu", "ask", "interact"], "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.3"}, "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-proposal-object-rest-spread": "^7.5.5", "@babel/preset-env": "^7.5.5", "tap-spec": "^5.0.0", "tape": "^4.11.0"}, "engines": {"node": ">= 6"}, "gitHead": "07981e4f7b3b5fddc6c92e121e1546d9ffd31e46", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "homepage": "https://github.com/terkelg/prompts#readme", "_id": "prompts@2.3.0", "_nodeVersion": "13.0.1", "_npmVersion": "6.12.0", "dist": {"integrity": "sha512-NfbbPPg/74fT7wk2XYQ7hAIp9zJyZp5Fu19iRbORqqy1BhtrkZ0fPafBU+7bmn8ie69DpT0R6QpJIN2oisYjJg==", "shasum": "a444e968fa4cc7e86689a74050685ac8006c4cc4", "tarball": "https://registry.npmjs.org/prompts/-/prompts-2.3.0.tgz", "fileCount": 68, "unpackedSize": 179014, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyFolCRA9TVsSAnZWagAAMaYP/jtjCi1OYB/fR88Y1/Ho\naP5cxEVrbVDbInVZGT5ejYynJTlS3+O2IdFBmLQSkTWCam0Mxu2PJ2BHbnr/\n8mGQcnwDOqUbL/Kh/8wAvQ25kJ4bwXImhfCC86J2Evh6B3c5zcTmltvglqoW\nhwG2EQ51A/vRmvKnVjz+3iat/PtsMx5e8YBr67VTqlv8TCE31i6Lm7oCSCiL\nmyXN93kdWZUgBHx/qnkTIdA3+cxm8Z/Xq+vBI9xCaQX85Eru5ITiyf45OYkE\nj0qWGu1SK8uvOTmcaoD2SLbTjNnqyeZHCpdCi8EL1+Z3ANYyN+Ye7PWYT0dD\nmvt9vq4YCv8fvrtq1wavMX5h019ILnkdMn7/x5HYvmj6ROjXsZH1ZEo8Y0dt\nohog1vKw2cUqfue9tPIgRYtxgWtmMn2qBkvRiDDmYs6hZMOjnEmHT6h6gaZ5\nOnVTTZXguF5c8/FUzxtdqrbqiCnr1qH9FJhPs241YvjpjQtyyiah7bsBSneG\nBb/MyOl6Xnk5z7CmuK8k3WjaBQfy24HdxCfi+SonNIskY46l6VCjdKz8bsxi\nFrwdsbVMluaYi+9RQGRTRXmN0Fi0ZDcLPBPLbY8bQ27zvpgMrW3sWwGC9Cwp\nV6PHAXaCnqEM/2PgQ5Dz6yCXUkm0fVoDXzoZ3KoZy7NxmKvZ4f7PrLPNAI2f\nqnVg\r\n=o1ok\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCCARzRZR5zTSLvnx0GuweHlVA2VyUoErgajaCZfADEmgIhAJvDmFpGsIb04rNluWcNQ+uQJysj6257J3coYpNESytR"}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_2.3.0_1573411364889_0.3547571328628243"}, "_hasShrinkwrap": false}, "2.3.1": {"name": "prompts", "version": "2.3.1", "description": "Lightweight, beautiful and user-friendly prompts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input", "command", "stdin", "menu", "ask", "interact"], "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.4"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.8.4", "@babel/plugin-proposal-object-rest-spread": "^7.8.3", "@babel/preset-env": "^7.8.4", "tap-spec": "^5.0.0", "tape": "^4.13.0"}, "engines": {"node": ">= 6"}, "gitHead": "2047fcd61ce28f91ce12510e53ed9ec334e94523", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "homepage": "https://github.com/terkelg/prompts#readme", "_id": "prompts@2.3.1", "_nodeVersion": "13.2.0", "_npmVersion": "6.13.7", "dist": {"integrity": "sha512-qIP2lQyCwYbdzcqHIUi2HAxiWixhoM9OdLCWf8txXsapC/X9YdsCoeyRIXE/GP+Q0J37Q7+XN/MFqbUa7IzXNA==", "shasum": "b63a9ce2809f106fa9ae1277c275b167af46ea05", "tarball": "https://registry.npmjs.org/prompts/-/prompts-2.3.1.tgz", "fileCount": 68, "unpackedSize": 179277, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeRxAICRA9TVsSAnZWagAA7f0P/25Zr8eLa3Ol3sQj/5xp\nACPY4vApDiCiMd7A1hqLXwo0KaoYV4bWilngbvAiBsK+47ile0LH5LsxkOu7\nfvsTtHpDSMsLz8gwWOl+fdPMZnl+S1tgG4C+2jg2esVPUOy47/PYhk3EftDX\no1u5bemxSxgLGZiOTHAmEwaGhu9JdmtN07GgLbIbqHDglexZEBP2tWVUAIca\nxhtlV9NgV7S+adOCNjr1muWA/12p3KYhVf36iYb0ZXJVUU4jCr3Xdzx5Sj+p\n8dZDUA8Ss0/hhAfI3qg8gE702/jNexQdgoiB/IP7fw1Ny2EyObKfDr6e7cHX\nKIVuy6YlxvDp3LMD3AgaysB84CJlvTJk+eJoKWvTeY3BUB/GCj+4klP8b4Mz\nnrJZrkPoqg94vzMGqZxC4ryrgy87mnvHaG4plJ5aLEjaQ/CCuXMOKPOosgdj\nNGdUYO4UsL/V+WsyZPyxjGw8O+Otr4Q4xDiZnVqFkYhG5QV5yr72LQyyoagO\nWBgh6LulIg78mUVXuj+c4o0D44lVRM2Iz4bmTTyfDrnDdTugTXXD35EpzKaN\nqSgzXb7GWg7KWxgFCQDA0BcDPD/Q5FbRSLjPWvDtevNBEU5Pv1w1cowNnP3b\ns78YcE9XU9+D8rUWZEv9mDGevwswdR9lEdrXf5w/3iXWqYVLvAXQ9yOKsCM1\ngGwy\r\n=zdAW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDMUnHt2qY93l9EoTKBFJn2zDQxt7/L0CpTjbXCq6zUlQIhAJU8irmXBjsMlgG8q6Acaynheott7LuA3sGRApxn8Kvn"}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_2.3.1_1581715459134_0.6604927838759957"}, "_hasShrinkwrap": false}, "2.3.2": {"name": "prompts", "version": "2.3.2", "description": "Lightweight, beautiful and user-friendly prompts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input", "command", "stdin", "menu", "ask", "interact"], "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.4"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.8.7", "@babel/plugin-proposal-object-rest-spread": "^7.8.3", "@babel/preset-env": "^7.8.7", "tap-spec": "^5.0.0", "tape": "^4.13.2"}, "engines": {"node": ">= 6"}, "gitHead": "603e823ad9fd8acbbcbddd39633f27d495628f3c", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "homepage": "https://github.com/terkelg/prompts#readme", "_id": "prompts@2.3.2", "_nodeVersion": "13.7.0", "_npmVersion": "6.14.2", "dist": {"integrity": "sha512-Q06uKs2CkNYVID0VqwfAl9mipo99zkBv/n2JtWY89Yxa3ZabWSrs0e2KTudKVa3peLUvYXMefDqIleLPVUBZMA==", "shasum": "480572d89ecf39566d2bd3fe2c9fccb7c4c0b068", "tarball": "https://registry.npmjs.org/prompts/-/prompts-2.3.2.tgz", "fileCount": 68, "unpackedSize": 179244, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJechgtCRA9TVsSAnZWagAAr5kP/AtbdyQ+qMB9gPnZpWR2\n8fGKao7QMW9kPKmq6gl8l+bqNiwho3WKMq6G9TkXwL+oUASil6K8gpvr/3rz\nt8gwWnCRH4g5+SPVNyeitzwbkfupQX9eokNRLtmj7Q0e5ijCiiRFl/lgPEYL\n7nTAP5AVycNFAjoD/03vTb5H04kENOWJc8WaNqL5i38HIGOzotl/MxIFUQiW\nsaQZkZgYU2wdBQjaE9qaNHdBQSG1QDFJ3UprHig7opeVOK3Qr7/agtJomDXW\nw0OyvIJ+VEXXvBcM8R0JO0E7N6aigMUFyTgqdAgrq5tesah3kQVf3HclWxnc\nft3gpEdiikyTrnpQhSIVrdnVDOR0FteEkxuUZJqp8T0NfTMjGSiLdPOKPZ/G\nBHAWa6DUq4Z9oPP8DzsNvieR6sL248xwZHYmXO5G9+RJuIAjvaLT5FvEZsus\nNWkEbjoTL4XhhxJ9hS0S0iG+tuYM+FF9FYUeWHfwvjL38M7MJE6TkvR6hoZz\ns4MGEQ2wBOVYjkqQB2N3GuoKgNVofSyzc0j9jguQr81uqEUsCBpMfVazKGtJ\nz5C5/PyavR5qmiBofNQs7sIIulcltQCZ/KIQ0c7gxbBM+OUKF5S7k9hC3xmh\nEuY6z35FhA8IAY2tyNDgka/8qBd7Ng1NBkqhyY3dtB9EHJzYV7pJzwuQcDAl\nau+9\r\n=OV3I\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDqcJb+7yRv5Ipwsii3CJEzcF9BmuJ1jC4n0ykmkDQa4AiAJixQEzvWtvhcQZyRMNVHjb1z4BO81Tbt/jkXVIb/m8w=="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_2.3.2_1584535596753_0.40498675315914934"}, "_hasShrinkwrap": false}, "2.4.0": {"name": "prompts", "version": "2.4.0", "description": "Lightweight, beautiful and user-friendly prompts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input", "command", "stdin", "menu", "ask", "interact"], "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}, "devDependencies": {"@babel/cli": "^7.12.1", "@babel/core": "^7.12.3", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/preset-env": "^7.12.1", "tap-spec": "^5.0.0", "tape": "^4.13.3"}, "engines": {"node": ">= 6"}, "gitHead": "972fbb2d43c7b1ee5058800f441daaf51f2c240f", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "homepage": "https://github.com/terkelg/prompts#readme", "_id": "prompts@2.4.0", "_nodeVersion": "13.7.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-awZAKrk3vN6CroQukBL+R9051a4R3zCZBlJm/HBfrSZ8iTpYix3VX1vU4mveiLpiwmOJT4wokTF9m6HUk4KqWQ==", "shasum": "4aa5de0723a231d1ee9121c40fdf663df73f61d7", "tarball": "https://registry.npmjs.org/prompts/-/prompts-2.4.0.tgz", "fileCount": 68, "unpackedSize": 185400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJflsztCRA9TVsSAnZWagAASfcP/3TF/hqnYzCg2dXpIXvY\novcxqvSt8P46SJDZQcu7sprNL5Y8i253rIwAlq2LYZIZ4IhZl6ckGqVjmfj/\no5ARM45yQiYq4OSOSVwF+LC2gSoZhTvwJ/eJ/VYc0KVjyKZmxNpc6HG3QtOh\nUU+xwYKAjzvBMNbNwqSyqX42LWU+dT3lm5PMWx49Zux+zUjTV5jyZDCoxxCw\nBbQgI3315jjCuNiTEY/fMWyh4q4yfyjVJoOrYzw5JU0VdAMqcLod+dQ2qAJn\ncTLPQVmJYsyS7nBPvrS6eYahmLPEEBaVl8S54otZIWPwcbrFD2JC2LDsDU7v\nGEh2FJXaBsMhBlTNyvtbZkdwC/cBRDM6eBCF8nVZClBEmmuYu8qYC/jeTiJK\n20VvUjIxvYxP1n6Wur5ZIMPYO4eII7hq6jo3A6+iLJvR6e8p/zrQRe2MaJ38\nJSHY7RmaFaDXtyuQpYNflN/ZSAXC2ccs4awCt2CsYzomaYcewf1GWXC1DP7F\n7104Mm7kAFD4bZ4y4Wj/4VUF4O/jelDpdcv/NaTnpMtKZZGgr4Qi9ujEBUOl\n9at1K3iGR5NYQGjb+Wz7eeKnJl1eGwzP/WA5kR9zPLZTKHy09dhyv8WOfbCC\nCpSvoD4+d7PqLXLKnOGJgaoHm0cTPzHYePJEtoock8Tch/kYeADz4Yy5Puya\n96Ui\r\n=foyK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtDBxL7/b549r91PP1OTLMt2dHA+d8vz3+j+GBvEI0JwIgVylI5UvN9gF6gaqLvrEKrf90qlKADX62xVjA/T1cvwM="}]}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_2.4.0_1603718381054_0.7905886303559266"}, "_hasShrinkwrap": false}, "2.4.1": {"name": "prompts", "version": "2.4.1", "description": "Lightweight, beautiful and user-friendly prompts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input", "command", "stdin", "menu", "ask", "interact"], "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}, "devDependencies": {"@babel/cli": "^7.12.1", "@babel/core": "^7.12.3", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/preset-env": "^7.12.1", "tap-spec": "^5.0.0", "tape": "^4.13.3"}, "engines": {"node": ">= 6"}, "gitHead": "22fe104947687f9916f2dac53e658965b325870f", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "homepage": "https://github.com/terkelg/prompts#readme", "_id": "prompts@2.4.1", "_nodeVersion": "14.15.1", "_npmVersion": "7.5.3", "dist": {"integrity": "sha512-EQyfIuO2hPDsX1L/blblV+H7I0knhgAd82cVneCwcdND9B8AuCDuRcBH6yIcG4dFzlOUqbazQqwGjx5xmsNLuQ==", "shasum": "befd3b1195ba052f9fd2fde8a486c4e82ee77f61", "tarball": "https://registry.npmjs.org/prompts/-/prompts-2.4.1.tgz", "fileCount": 68, "unpackedSize": 186703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZR8ZCRA9TVsSAnZWagAAj9sP/2sgQ/6n+cYijfOxvyP9\n16/RCykdu4VMZ5hXENaxphqJqCgHUJxWMoC0Trl0c38bOhWOuCisYBX4fI3I\nPsOp4ISnob73lOYXIZp48a69n9uCK67l2Bts+mje1sRugH60y/sNUP9g3SZj\nDc1SIj+iwsplFnPI9fZK4Cqb4BHEd5LpjDIQ1GXNMViJE7bcsQ2JZuUE0+1y\naTMBKseG5+k8usrhlQw/XAMYuFMx6rmoVBXd+K5SAXxKj0ff/z+mbt4jrHGa\nYu7dTVSAfTMjYNkGWY9xMgh0BjM61G5V4M/lxVln7UPjWSomKOycz7FEUZ9A\nn40fH7CPhFCC791k9DC7x+FT4QAwNzI5eevZB0/c+ADXx2w+dXUNVyjmPy0Q\n/Tx8JKTNOGjxYCkUMNPhrYtI2Y4T3UK+PKfqEJ7OhE9D1jWuaZQpDZrvyUHM\nRXZtOnprPkRxGf/7Xo8COInqGnHQbepEQ8JlN5VNn8wn5us86leJMqGEOp7K\naPEDXmy3RjzYXvZ3zSdBWfF1IB997R4dF1SQcsKhzLc1rTdkhS2ivJxh4IOd\nwphpyXU0SVlvZsSnTUuYWNs8DhwQHSA7+NRk0QZRiZIYllRehP0a2AnU77kd\nb/BT/y1885kHRbXWEMGbeqVXjOhY8/S8WS6v8btb1uilQtzIFqgSV6E/Qy9I\npiwe\r\n=NICQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHPO4UbKkeJOH3OEnbkkrgBXA6Pw/Yyx4twywdaOZIQhAiEAq1Y+GYZHQN+Abs5ga/yUyy95d54ia1DyGXTW/oRdo80="}]}, "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_2.4.1_1617239832243_0.11117411582489001"}, "_hasShrinkwrap": false}, "2.4.2": {"name": "prompts", "version": "2.4.2", "description": "Lightweight, beautiful and user-friendly prompts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "main": "index.js", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "scripts": {"start": "node lib/index.js", "build": "babel lib -d dist", "prepublishOnly": "npm run build", "test": "tape test/*.js | tap-spec"}, "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input", "command", "stdin", "menu", "ask", "interact"], "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}, "devDependencies": {"@babel/cli": "^7.12.1", "@babel/core": "^7.12.3", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/preset-env": "^7.12.1", "tap-spec": "^2.2.2", "tape": "^4.13.3"}, "engines": {"node": ">= 6"}, "gitHead": "66ccf0bda0e1aa18d9efcf128018dfbad4f7ca0e", "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "homepage": "https://github.com/terkelg/prompts#readme", "_id": "prompts@2.4.2", "_nodeVersion": "16.10.0", "_npmVersion": "7.24.0", "dist": {"integrity": "sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==", "shasum": "7b57e73b3a48029ad10ebd44f74b01722a4cb069", "tarball": "https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz", "fileCount": 68, "unpackedSize": 186815, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh23TLCRA9TVsSAnZWagAAKgsP/2IvkzaAEHMZwMCAwqiW\nJrtdULgOd8aTM0VcIjPJVFo+N5QG37Zfseo3S0B4V+9ynqwVqLC25sHidCnA\n0U+LKg1W95KaLei/QXls2lEERCekEzkpFwYIRHQ2JgX2JRfOFQo3qbAezut7\n5DYni8oMr/LxCFF1CAH2ltm2iR5VNhgChOwgosgmJ7sAin0L6cFNOwtLVFJv\nHyA3MgGM1HLGbGIxCQsT0UDWDBxPxaQuWNKY7gPO41eaeluxpk7lcQZXY3m3\n6F8mgpn28wJ20v90jykdfT7GHGTNsHaP13WM522xMGuuUjES5jQayVepNuNy\ny7cuLoDT+3J4pPyM7rd1HKdcAqIQrh4jxEDZq93NPuX9/SHqKMV2tmb9JMQm\nuAvrvz5HK+9sZPBtLmk5VRBRPF2P82+qysi6/+BQBogOVIcd/SohTzq2I65b\nb7MGcKUXajUBgn+3iJ0rPMgypbof1E0bWGZvoD3X+bfryqwId30OBNBpqR3H\n9xf01xWdwWl07quF1XneVwtgriX7d/XEbKUTSiy7nq0m7M6DhexAXTXAVoLs\nwtzr/SEKQ4q7A7dOeVhtxwSHpylCHWELtb9Ku6LyG2m4q39DB82VVPCRMumt\nQ13386Gr1E6Qbwx7flAQyLh15eIi0XNajJc+dYwnUmSCG2rfOFRmXOhuZSTQ\npO05\r\n=n1NA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCWRNHF4FULknRmKmxczc982BMcnXXaCJCZF0WDgWh/EAIgIMsc9sdanOEH8YX7DxCj8s7KvuenvNxLzOMXz9AvKZg="}]}, "_npmUser": {"name": "terkelg", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/prompts_2.4.2_1633641837056_0.21802162082263776"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-02-18T20:55:09.802Z", "0.0.0": "2018-02-18T20:55:09.879Z", "modified": "2023-10-21T01:41:35.063Z", "0.1.0-1": "2018-02-24T10:12:42.296Z", "0.1.0": "2018-02-24T23:42:14.553Z", "0.1.1": "2018-02-27T07:11:23.919Z", "0.1.2": "2018-02-28T03:57:39.634Z", "0.1.3": "2018-02-28T08:23:26.805Z", "0.1.4": "2018-02-28T17:07:11.386Z", "0.1.5": "2018-03-07T05:51:14.604Z", "0.1.6": "2018-03-09T09:49:20.133Z", "0.1.7": "2018-03-11T22:30:57.494Z", "0.1.8": "2018-03-13T02:54:11.221Z", "0.1.9": "2018-06-19T04:17:34.395Z", "0.1.10": "2018-06-28T06:18:11.144Z", "0.1.11": "2018-07-07T15:06:27.946Z", "0.1.12": "2018-07-12T21:02:43.902Z", "0.1.13": "2018-07-27T07:48:59.798Z", "0.1.14": "2018-08-04T14:09:52.084Z", "1.0.0": "2018-08-27T08:10:17.584Z", "1.1.0": "2018-09-09T16:22:07.490Z", "1.1.1": "2018-09-19T16:24:59.829Z", "1.2.0": "2018-10-29T17:59:29.946Z", "1.2.1": "2018-11-15T23:06:38.808Z", "2.0.0": "2018-12-06T19:02:54.074Z", "2.0.1": "2018-12-30T09:44:44.329Z", "2.0.2": "2019-02-08T18:54:48.013Z", "2.0.3": "2019-02-16T17:48:19.446Z", "2.0.4": "2019-03-16T16:05:56.329Z", "2.1.0": "2019-05-21T23:32:43.540Z", "2.2.0": "2019-08-05T08:22:40.975Z", "2.2.1": "2019-08-05T08:31:57.283Z", "2.3.0": "2019-11-10T18:42:45.049Z", "2.3.1": "2020-02-14T21:24:19.268Z", "2.3.2": "2020-03-18T12:46:36.906Z", "2.4.0": "2020-10-26T13:19:41.193Z", "2.4.1": "2021-04-01T01:17:12.667Z", "2.4.2": "2021-10-07T21:23:57.263Z"}, "maintainers": [{"name": "terkelg", "email": "<EMAIL>"}], "readme": "<p align=\"center\">\n  <img src=\"https://github.com/terkelg/prompts/raw/master/prompts.png\" alt=\"Prompts\" width=\"500\" />\n</p>\n\n<h1 align=\"center\">❯ Prompts</h1>\n\n<p align=\"center\">\n  <a href=\"https://npmjs.org/package/prompts\">\n    <img src=\"https://img.shields.io/npm/v/prompts.svg\" alt=\"version\" />\n  </a>\n  <a href=\"https://travis-ci.org/terkelg/prompts\">\n    <img src=\"https://img.shields.io/travis/terkelg/prompts.svg\" alt=\"travis\" />\n  </a>\n  <a href=\"https://npmjs.org/package/prompts\">\n    <img src=\"https://img.shields.io/npm/dm/prompts.svg\" alt=\"downloads\" />\n  </a>\n  <!---\n   <a href=\"https://packagephobia.now.sh/result?p=prompts\">\n    <img src=\"https://packagephobia.now.sh/badge?p=prompts\" alt=\"install size\" />\n  </a>\n  --->\n</p>\n\n<p align=\"center\">\n  <b>Lightweight, beautiful and user-friendly interactive prompts</b><br />\n  <sub>>_ Easy to use CLI prompts to enquire users for information▌</sub>\n</p>\n\n<br />\n\n* **Simple**: prompts has [no big dependencies](http://npm.anvaka.com/#/view/2d/prompts) nor is it broken into a [dozen](http://npm.anvaka.com/#/view/2d/inquirer) tiny modules that only work well together.\n* **User friendly**: prompt uses layout and colors to create beautiful cli interfaces.\n* **Promised**: uses promises and `async`/`await`. No callback hell.\n* **Flexible**: all prompts are independent and can be used on their own.\n* **Testable**: provides a way to submit answers programmatically.\n* **Unified**: consistent experience across all [prompts](#-types).\n\n\n![split](https://github.com/terkelg/prompts/raw/master/media/split.png)\n\n\n## ❯ Install\n\n```\n$ npm install --save prompts\n```\n\n> This package supports Node 6 and above\n\n![split](https://github.com/terkelg/prompts/raw/master/media/split.png)\n\n## ❯ Usage\n\n<img src=\"https://github.com/terkelg/prompts/raw/master/media/example.gif\" alt=\"example prompt\" width=\"499\" height=\"103\" />\n\n```js\nconst prompts = require('prompts');\n\n(async () => {\n  const response = await prompts({\n    type: 'number',\n    name: 'value',\n    message: 'How old are you?',\n    validate: value => value < 18 ? `Nightclub is 18+ only` : true\n  });\n\n  console.log(response); // => { value: 24 }\n})();\n```\n\n> See [`example.js`](https://github.com/terkelg/prompts/blob/master/example.js) for more options.\n\n\n![split](https://github.com/terkelg/prompts/raw/master/media/split.png)\n\n\n## ❯ Examples\n\n### Single Prompt\n\nPrompt with a single prompt object. Returns an object with the response.\n\n```js\nconst prompts = require('prompts');\n\n(async () => {\n  const response = await prompts({\n    type: 'text',\n    name: 'meaning',\n    message: 'What is the meaning of life?'\n  });\n\n  console.log(response.meaning);\n})();\n```\n\n### Prompt Chain\n\nPrompt with a list of prompt objects. Returns an object with the responses.\nMake sure to give each prompt a unique `name` property to prevent overwriting values.\n\n```js\nconst prompts = require('prompts');\n\nconst questions = [\n  {\n    type: 'text',\n    name: 'username',\n    message: 'What is your GitHub username?'\n  },\n  {\n    type: 'number',\n    name: 'age',\n    message: 'How old are you?'\n  },\n  {\n    type: 'text',\n    name: 'about',\n    message: 'Tell something about yourself',\n    initial: 'Why should I?'\n  }\n];\n\n(async () => {\n  const response = await prompts(questions);\n\n  // => response => { username, age, about }\n})();\n```\n\n### Dynamic Prompts\n\nPrompt properties can be functions too.\nPrompt Objects with `type` set to `falsy` values are skipped.\n\n```js\nconst prompts = require('prompts');\n\nconst questions = [\n  {\n    type: 'text',\n    name: 'dish',\n    message: 'Do you like pizza?'\n  },\n  {\n    type: prev => prev == 'pizza' ? 'text' : null,\n    name: 'topping',\n    message: 'Name a topping'\n  }\n];\n\n(async () => {\n  const response = await prompts(questions);\n})();\n```\n\n\n![split](https://github.com/terkelg/prompts/raw/master/media/split.png)\n\n\n## ❯ API\n\n### prompts(prompts, options)\n\nType: `Function`<br>\nReturns: `Object`\n\nPrompter function which takes your [prompt objects](#-prompt-objects) and returns an object with responses.\n\n\n#### prompts\n\nType: `Array|Object`<br>\n\nArray of [prompt objects](#-prompt-objects).\n These are the questions the user will be prompted. You can see the list of supported [prompt types here](#-types).\n\nPrompts can be submitted (<kbd>return</kbd>, <kbd>enter</kbd>) or canceled (<kbd>esc</kbd>, <kbd>abort</kbd>, <kbd>ctrl</kbd>+<kbd>c</kbd>, <kbd>ctrl</kbd>+<kbd>d</kbd>). No property is being defined on the returned response object when a prompt is canceled.\n\n#### options.onSubmit\n\nType: `Function`<br>\nDefault: `() => {}`\n\nCallback that's invoked after each prompt submission.\nIts signature is `(prompt, answer, answers)` where `prompt` is the current prompt object, `answer` the user answer to the current question and `answers` the user answers so far. Async functions are supported.\n\nReturn `true` to quit the prompt chain and return all collected responses so far, otherwise continue to iterate prompt objects.\n\n**Example:**\n```js\n(async () => {\n  const questions = [{ ... }];\n  const onSubmit = (prompt, answer) => console.log(`Thanks I got ${answer} from ${prompt.name}`);\n  const response = await prompts(questions, { onSubmit });\n})();\n```\n\n#### options.onCancel\n\nType: `Function`<br>\nDefault: `() => {}`\n\nCallback that's invoked when the user cancels/exits the prompt.\nIts signature is `(prompt, answers)` where `prompt` is the current prompt object and `answers` the user answers so far. Async functions are supported.\n\nReturn `true` to continue and prevent the prompt loop from aborting.\nOn cancel responses collected so far are returned.\n\n**Example:**\n```js\n(async () => {\n  const questions = [{ ... }];\n  const onCancel = prompt => {\n    console.log('Never stop prompting!');\n    return true;\n  }\n  const response = await prompts(questions, { onCancel });\n})();\n```\n\n### override\n\nType: `Function`\n\nPreanswer questions by passing an object with answers to `prompts.override`.\nPowerful when combined with arguments of process.\n\n**Example**\n```js\nconst prompts = require('prompts');\nprompts.override(require('yargs').argv);\n\n(async () => {\n  const response = await prompts([\n    {\n      type: 'text',\n      name: 'twitter',\n      message: `What's your twitter handle?`\n    },\n    {\n      type: 'multiselect',\n      name: 'color',\n      message: 'Pick colors',\n      choices: [\n        { title: 'Red', value: '#ff0000' },\n        { title: 'Green', value: '#00ff00' },\n        { title: 'Blue', value: '#0000ff' }\n      ],\n    }\n  ]);\n\n  console.log(response);\n})();\n```\n\n### inject(values)\n\nType: `Function`<br>\n\nProgrammatically inject responses. This enables you to prepare the responses ahead of time.\nIf any injected value is found the prompt is immediately resolved with the injected value.\nThis feature is intended for testing only.\n\n#### values\n\nType: `Array`\n\nArray with values to inject. Resolved values are removed from the internal inject array.\nEach value can be an array of values in order to provide answers for a question asked multiple times.\nIf a value is an instance of `Error` it will simulate the user cancelling/exiting the prompt.\n\n**Example:**\n```js\nconst prompts = require('prompts');\n\nprompts.inject([ '@terkelg', ['#ff0000', '#0000ff'] ]);\n\n(async () => {\n  const response = await prompts([\n    {\n      type: 'text',\n      name: 'twitter',\n      message: `What's your twitter handle?`\n    },\n    {\n      type: 'multiselect',\n      name: 'color',\n      message: 'Pick colors',\n      choices: [\n        { title: 'Red', value: '#ff0000' },\n        { title: 'Green', value: '#00ff00' },\n        { title: 'Blue', value: '#0000ff' }\n      ],\n    }\n  ]);\n\n  // => { twitter: 'terkelg', color: [ '#ff0000', '#0000ff' ] }\n})();\n```\n\n![split](https://github.com/terkelg/prompts/raw/master/media/split.png)\n\n\n## ❯ Prompt Objects\n\nPrompts Objects are JavaScript objects that define the \"questions\" and the [type of prompt](#-types).\nAlmost all prompt objects have the following properties:\n\n```js\n{\n  type: String | Function,\n  name: String | Function,\n  message: String | Function,\n  initial: String | Function | Async Function\n  format: Function | Async Function,\n  onRender: Function\n  onState: Function\n  stdin: Readable\n  stdout: Writeable\n}\n```\n\nEach property be of type `function` and will be invoked right before prompting the user.\n\nThe function signature is `(prev, values, prompt)`, where `prev` is the value from the previous prompt,\n`values` is the response object with all values collected so far and `prompt` is the previous prompt object.\n\n**Function example:**\n```js\n{\n  type: prev => prev > 3 ? 'confirm' : null,\n  name: 'confirm',\n  message: (prev, values) => `Please confirm that you eat ${values.dish} times ${prev} a day?`\n}\n```\n\nThe above prompt will be skipped if the value of the previous prompt is less than 3.\n\n### type\n\nType: `String|Function`\n\nDefines the type of prompt to display. See the list of [prompt types](#-types) for valid values.\n\nIf `type` is a falsy value the prompter will skip that question.\n```js\n{\n  type: null,\n  name: 'forgetme',\n  message: `I'll never be shown anyway`,\n}\n```\n\n### name\n\nType: `String|Function`\n\nThe response will be saved under this key/property in the returned response object.\nIn case you have multiple prompts with the same name only the latest response will be stored.\n\n> Make sure to give prompts unique names if you don't want to overwrite previous values.\n\n### message\n\nType: `String|Function`\n\nThe message to be displayed to the user.\n\n### initial\n\nType: `String|Function`\n\nOptional default prompt value. Async functions are supported too.\n\n### format\n\nType: `Function`\n\nReceive the user input and return the formatted value to be used inside the program.\nThe value returned will be added to the response object.\n\nThe function signature is `(val, values)`, where `val` is the value from the current prompt and\n`values` is the current response object in case you need to format based on previous responses.\n\n**Example:**\n```js\n{\n  type: 'number',\n  name: 'price',\n  message: 'Enter price',\n  format: val => Intl.NumberFormat(undefined, { style: 'currency', currency: 'USD' }).format(val);\n}\n```\n\n### onRender\n\nType: `Function`\n\nCallback for when the prompt is rendered.\nThe function receives [kleur](https://github.com/lukeed/kleur) as its first argument and `this` refers to the current prompt.\n\n**Example:**\n```js\n{\n  type: 'number',\n  message: 'This message will be overridden',\n  onRender(kleur) {\n    this.msg = kleur.cyan('Enter a number');\n  }\n}\n```\n\n### onState\n\nType: `Function`\n\nCallback for when the state of the current prompt changes.\nThe function signature is `(state)` where `state` is an object with a snapshot of the current state.\nThe state object has two properties `value` and `aborted`. E.g `{ value: 'This is ', aborted: false }`\n\n### stdin and stdout\n\nType: `Stream`\n\nBy default, prompts uses `process.stdin` for receiving input and `process.stdout` for writing output.\nIf you need to use different streams, for instance `process.stderr`, you can set these with the `stdin` and `stdout` properties.\n\n\n![split](https://github.com/terkelg/prompts/raw/master/media/split.png)\n\n\n## ❯ Types\n\n* [text](#textmessage-initial-style)\n* [password](#passwordmessage-initial)\n* [invisible](#invisiblemessage-initial)\n* [number](#numbermessage-initial-max-min-style)\n* [confirm](#confirmmessage-initial)\n* [list](#listmessage-initial)\n* [toggle](#togglemessage-initial-active-inactive)\n* [select](#selectmessage-choices-initial-hint-warn)\n* [multiselect](#multiselectmessage-choices-initial-max-hint-warn)\n* [autocompleteMultiselect](#multiselectmessage-choices-initial-max-hint-warn)\n* [autocomplete](#autocompletemessage-choices-initial-suggest-limit-style)\n* [date](#datemessage-initial-warn)\n\n***\n\n### text(message, [initial], [style])\n> Text prompt for free text input.\n\nHit <kbd>tab</kbd> to autocomplete to `initial` value when provided.\n\n#### Example\n<img src=\"https://github.com/terkelg/prompts/raw/master/media/text.gif\" alt=\"text prompt\" width=\"499\" height=\"103\" />\n\n```js\n{\n  type: 'text',\n  name: 'value',\n  message: `What's your twitter handle?`\n}\n```\n\n#### Options\n| Param | Type | Description |\n| ----- | :--: | ----------- |\n| message | `string` | Prompt message to display |\n| initial | `string` | Default string value |\n| style | `string` | Render style (`default`, `password`, `invisible`, `emoji`). Defaults to `default` |\n| format | `function` | Receive user input. The returned value will be added to the response object |\n| validate | `function` | Receive user input. Should return `true` if the value is valid, and an error message `String` otherwise. If `false` is returned, a default error message is shown |\n| onRender | `function` | On render callback. Keyword `this` refers to the current prompt |\n| onState | `function` | On state change callback. Function signature is an `object` with two properties: `value` and `aborted` |\n\n**↑ back to:** [Prompt types](#-types)\n\n***\n\n### password(message, [initial])\n> Password prompt with masked input.\n\nThis prompt is a similar to a prompt of type `'text'` with `style` set to `'password'`.\n\n#### Example\n<img src=\"https://github.com/terkelg/prompts/raw/master/media/password.gif\" alt=\"password prompt\" width=\"499\" height=\"103\" />\n\n```js\n{\n  type: 'password',\n  name: 'value',\n  message: 'Tell me a secret'\n}\n```\n\n#### Options\n| Param | Type | Description |\n| ----- | :--: | ----------- |\n| message | `string` | Prompt message to display |\n| initial | `string` | Default string value |\n| format | `function` | Receive user input. The returned value will be added to the response object |\n| validate | `function` | Receive user input. Should return `true` if the value is valid, and an error message `String` otherwise. If `false` is returned, a default error message is shown |\n| onRender | `function` | On render callback. Keyword `this` refers to the current prompt |\n| onState | `function` | On state change callback. Function signature is an `object` with two properties: `value` and `aborted` |\n\n**↑ back to:** [Prompt types](#-types)\n\n***\n\n### invisible(message, [initial])\n> Prompts user for invisible text input.\n\nThis prompt is working like `sudo` where the input is invisible.\nThis prompt is a similar to a prompt of type `'text'` with style set to `'invisible'`.\n\n#### Example\n<img src=\"https://github.com/terkelg/prompts/raw/master/media/invisible.gif\" alt=\"invisible prompt\" width=\"499\" height=\"103\" />\n\n```js\n{\n  type: 'invisible',\n  name: 'value',\n  message: 'Enter password'\n}\n```\n\n#### Options\n| Param | Type | Description |\n| ----- | :--: | ----------- |\n| message | `string` | Prompt message to display |\n| initial | `string` | Default string value |\n| format | `function` | Receive user input. The returned value will be added to the response object |\n| validate | `function` | Receive user input. Should return `true` if the value is valid, and an error message `String` otherwise. If `false` is returned, a default error message is shown |\n| onRender | `function` | On render callback. Keyword `this` refers to the current prompt |\n| onState | `function` | On state change callback. Function signature is an `object` with two properties: `value` and `aborted` |\n\n**↑ back to:** [Prompt types](#-types)\n\n***\n\n### number(message, initial, [max], [min], [style])\n> Prompts user for number input.\n\nYou can type in numbers and use <kbd>up</kbd>/<kbd>down</kbd> to increase/decrease the value. Only numbers are allowed as input. Hit <kbd>tab</kbd> to autocomplete to `initial` value when provided.\n\n#### Example\n<img src=\"https://github.com/terkelg/prompts/raw/master/media/number.gif\" alt=\"number prompt\" width=\"499\" height=\"103\" />\n\n```js\n{\n  type: 'number',\n  name: 'value',\n  message: 'How old are you?',\n  initial: 0,\n  style: 'default',\n  min: 2,\n  max: 10\n}\n```\n\n#### Options\n| Param | Type | Description |\n| ----- | :--: | ----------- |\n| message | `string` | Prompt message to display |\n| initial | `number` | Default number value |\n| format | `function` | Receive user input. The returned value will be added to the response object |\n| validate | `function` | Receive user input. Should return `true` if the value is valid, and an error message `String` otherwise. If `false` is returned, a default error message is shown |\n| max | `number` | Max value. Defaults to `Infinity` |\n| min | `number` | Min value. Defaults to `-infinity` |\n| float | `boolean` | Allow floating point inputs. Defaults to `false` |\n| round | `number` | Round `float` values to x decimals. Defaults to `2` |\n| increment | `number` | Increment step when using <kbd>arrow</kbd> keys. Defaults to `1` |\n| style | `string` | Render style (`default`, `password`, `invisible`, `emoji`). Defaults to `default` |\n| onRender | `function` | On render callback. Keyword `this` refers to the current prompt |\n| onState | `function` | On state change callback. Function signature is an `object` with two properties: `value` and `aborted` |\n\n**↑ back to:** [Prompt types](#-types)\n\n***\n\n### confirm(message, [initial])\n> Classic yes/no prompt.\n\nHit <kbd>y</kbd> or <kbd>n</kbd> to confirm/reject.\n\n#### Example\n<img src=\"https://github.com/terkelg/prompts/raw/master/media/confirm.gif\" alt=\"confirm prompt\" width=\"499\" height=\"103\" />\n\n```js\n{\n  type: 'confirm',\n  name: 'value',\n  message: 'Can you confirm?',\n  initial: true\n}\n```\n\n\n#### Options\n| Param | Type | Description |\n| ----- | :--: | ----------- |\n| message | `string` | Prompt message to display |\n| initial | `boolean` | Default value. Default is `false` |\n| format | `function` | Receive user input. The returned value will be added to the response object |\n| onRender | `function` | On render callback. Keyword `this` refers to the current prompt |\n| onState | `function` | On state change callback. Function signature is an `object` with two properties: `value` and `aborted` |\n\n**↑ back to:** [Prompt types](#-types)\n\n***\n\n### list(message, [initial])\n> List prompt that return an array.\n\nSimilar to the `text` prompt, but the output is an `Array` containing the\nstring separated by `separator`.\n\n```js\n{\n  type: 'list',\n  name: 'value',\n  message: 'Enter keywords',\n  initial: '',\n  separator: ','\n}\n```\n\n<img src=\"https://github.com/terkelg/prompts/raw/master/media/list.gif\" alt=\"list prompt\" width=\"499\" height=\"103\" />\n\n\n| Param | Type | Description |\n| ----- | :--: | ----------- |\n| message | `string` | Prompt message to display |\n| initial | `boolean` | Default value |\n| format | `function` | Receive user input. The returned value will be added to the response object |\n| separator | `string` | String separator. Will trim all white-spaces from start and end of string. Defaults to `','`  |\n| onRender | `function` | On render callback. Keyword `this` refers to the current prompt |\n| onState | `function` | On state change callback. Function signature is an `object` with two properties: `value` and `aborted` |\n\n**↑ back to:** [Prompt types](#-types)\n\n***\n\n### toggle(message, [initial], [active], [inactive])\n> Interactive toggle/switch prompt.\n\nUse tab or <kbd>arrow keys</kbd>/<kbd>tab</kbd>/<kbd>space</kbd> to switch between options.\n\n#### Example\n<img src=\"https://github.com/terkelg/prompts/raw/master/media/toggle.gif\" alt=\"toggle prompt\" width=\"499\" height=\"103\" />\n\n```js\n{\n  type: 'toggle',\n  name: 'value',\n  message: 'Can you confirm?',\n  initial: true,\n  active: 'yes',\n  inactive: 'no'\n}\n```\n\n#### Options\n| Param | Type | Description |\n| ----- | :--: | ----------- |\n| message | `string` | Prompt message to display |\n| initial | `boolean` | Default value. Defaults to `false` |\n| format | `function` | Receive user input. The returned value will be added to the response object |\n| active | `string` | Text for `active` state. Defaults to `'on'` |\n| inactive | `string` | Text for `inactive` state. Defaults to `'off'` |\n| onRender | `function` | On render callback. Keyword `this` refers to the current prompt |\n| onState | `function` | On state change callback. Function signature is an `object` with two properties: `value` and `aborted` |\n\n**↑ back to:** [Prompt types](#-types)\n\n***\n\n### select(message, choices, [initial], [hint], [warn])\n> Interactive select prompt.\n\nUse <kbd>up</kbd>/<kbd>down</kbd> to navigate. Use <kbd>tab</kbd> to cycle the list.\n\n#### Example\n<img src=\"https://github.com/terkelg/prompts/raw/master/media/select.gif\" alt=\"select prompt\" width=\"499\" height=\"130\" />\n\n```js\n{\n  type: 'select',\n  name: 'value',\n  message: 'Pick a color',\n  choices: [\n    { title: 'Red', description: 'This option has a description', value: '#ff0000' },\n    { title: 'Green', value: '#00ff00', disabled: true },\n    { title: 'Blue', value: '#0000ff' }\n  ],\n  initial: 1\n}\n```\n\n#### Options\n| Param | Type | Description |\n| ----- | :--: | ----------- |\n| message | `string` | Prompt message to display |\n| initial | `number` | Index of default value |\n| format | `function` | Receive user input. The returned value will be added to the response object |\n| hint | `string` | Hint to display to the user |\n| warn | `string` | Message to display when selecting a disabled option |\n| choices | `Array` | Array of strings or choices objects `[{ title, description, value, disabled }, ...]`. The choice's index in the array will be used as its value if it is not specified. |\n| onRender | `function` | On render callback. Keyword `this` refers to the current prompt |\n| onState | `function` | On state change callback. Function signature is an `object` with two properties: `value` and `aborted` |\n\n**↑ back to:** [Prompt types](#-types)\n\n***\n\n### multiselect(message, choices, [initial], [max], [hint], [warn])\n### autocompleteMultiselect(same)\n> Interactive multi-select prompt.  \n> Autocomplete is a searchable multiselect prompt with the same options. Useful for long lists.\n\nUse <kbd>space</kbd> to toggle select/unselect and <kbd>up</kbd>/<kbd>down</kbd> to navigate. Use <kbd>tab</kbd> to cycle the list. You can also use <kbd>right</kbd> to select and <kbd>left</kbd> to deselect.\nBy default this prompt returns an `array` containing the **values** of the selected items - not their display title.\n\n#### Example\n<img src=\"https://github.com/terkelg/prompts/raw/master/media/multiselect.gif\" alt=\"multiselect prompt\" width=\"499\" height=\"130\" />\n\n```js\n{\n  type: 'multiselect',\n  name: 'value',\n  message: 'Pick colors',\n  choices: [\n    { title: 'Red', value: '#ff0000' },\n    { title: 'Green', value: '#00ff00', disabled: true },\n    { title: 'Blue', value: '#0000ff', selected: true }\n  ],\n  max: 2,\n  hint: '- Space to select. Return to submit'\n}\n```\n\n#### Options\n| Param | Type | Description |\n| ----- | :--: | ----------- |\n| message | `string` | Prompt message to display |\n| format | `function` | Receive user input. The returned value will be added to the response object |\n| instructions | `string` or `boolean` | Prompt instructions to display |\n| choices | `Array` | Array of strings or choices objects `[{ title, value, disabled }, ...]`. The choice's index in the array will be used as its value if it is not specified. |\n| optionsPerPage | `number` | Number of options displayed per page (default: 10) |\n| min | `number` | Min select - will display error |\n| max | `number` | Max select |\n| hint | `string` | Hint to display to the user |\n| warn | `string` | Message to display when selecting a disabled option |\n| onRender | `function` | On render callback. Keyword `this` refers to the current prompt |\n| onState | `function` | On state change callback. Function signature is an `object` with two properties: `value` and `aborted` |\n\nThis is one of the few prompts that don't take a initial value.\nIf you want to predefine selected values, give the choice object an `selected` property of `true`.\n\n**↑ back to:** [Prompt types](#-types)\n\n***\n\n### autocomplete(message, choices, [initial], [suggest], [limit], [style])\n> Interactive auto complete prompt.\n\nThe prompt will list options based on user input. Type to filter the list.\nUse <kbd>⇧</kbd>/<kbd>⇩</kbd> to navigate. Use <kbd>tab</kbd> to cycle the result. Use <kbd>Page Up</kbd>/<kbd>Page Down</kbd> (on Mac: <kbd>fn</kbd> + <kbd>⇧</kbd> / <kbd>⇩</kbd>) to change page. Hit <kbd>enter</kbd> to select the highlighted item below the prompt.\n\nThe default suggests function is sorting based on the `title` property of the choices.\nYou can overwrite how choices are being filtered by passing your own suggest function.\n\n#### Example\n<img src=\"https://github.com/terkelg/prompts/raw/master/media/autocomplete.gif\" alt=\"auto complete prompt\" width=\"499\" height=\"163\" />\n\n```js\n{\n  type: 'autocomplete',\n  name: 'value',\n  message: 'Pick your favorite actor',\n  choices: [\n    { title: 'Cage' },\n    { title: 'Clooney', value: 'silver-fox' },\n    { title: 'Gyllenhaal' },\n    { title: 'Gibson' },\n    { title: 'Grant' }\n  ]\n}\n```\n\n#### Options\n| Param | Type | Description |\n| ----- | :--: | ----------- |\n| message | `string` | Prompt message to display |\n| format | `function` | Receive user input. The returned value will be added to the response object |\n| choices | `Array` | Array of auto-complete choices objects `[{ title, value }, ...]` |\n| suggest | `function` | Filter function. Defaults to sort by `title` property. `suggest` should always return a promise. Filters using `title` by default  |\n| limit | `number` | Max number of results to show. Defaults to `10` |\n| style | `string` | Render style (`default`, `password`, `invisible`, `emoji`). Defaults to `'default'` |\n| initial | `string \\| number` | Default initial value |\n| clearFirst | `boolean` | The first ESCAPE keypress will clear the input |\n| fallback | `string` | Fallback message when no match is found. Defaults to `initial` value if provided |\n| onRender | `function` | On render callback. Keyword `this` refers to the current prompt |\n| onState | `function` | On state change callback. Function signature is an `object` with three properties: `value`, `aborted` and `exited` |\n\nExample on what a `suggest` function might look like:\n```js\nconst suggestByTitle = (input, choices) =>\n    Promise.resolve(choices.filter(i => i.title.slice(0, input.length) === input))\n```\n\n**↑ back to:** [Prompt types](#-types)\n\n***\n\n### date(message, [initial], [warn])\n> Interactive date prompt.\n\nUse <kbd>left</kbd>/<kbd>right</kbd>/<kbd>tab</kbd> to navigate. Use <kbd>up</kbd>/<kbd>down</kbd> to change date.\n\n#### Example\n<img src=\"https://github.com/terkelg/prompts/raw/master/media/date.gif\" alt=\"date prompt\" width=\"499\" height=\"103\" />\n\n```js\n{\n  type: 'date',\n  name: 'value',\n  message: 'Pick a date',\n  initial: new Date(1997, 09, 12),\n  validate: date => date > Date.now() ? 'Not in the future' : true\n}\n```\n\n#### Options\n| Param | Type | Description |\n| ----- | :--: | ----------- |\n| message | `string` | Prompt message to display |\n| initial | `date` | Default date |\n| locales | `object` | Use to define custom locales. See below for an example. |\n| mask | `string` | The format mask of the date. See below for more information.<br />Default: `YYYY-MM-DD HH:mm:ss` |\n| validate | `function` | Receive user input. Should return `true` if the value is valid, and an error message `String` otherwise. If `false` is returned, a default error message is shown |\n| onRender | `function` | On render callback. Keyword `this` refers to the current prompt |\n| onState | `function` | On state change callback. Function signature is an `object` with two properties: `value` and `aborted` |\n\nDefault locales:\n\n```javascript\n{\n  months: [\n    'January', 'February', 'March', 'April',\n    'May', 'June', 'July', 'August',\n    'September', 'October', 'November', 'December'\n  ],\n  monthsShort: [\n    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',\n    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'\n  ],\n  weekdays: [\n    'Sunday', 'Monday', 'Tuesday', 'Wednesday',\n    'Thursday', 'Friday', 'Saturday'\n  ],\n  weekdaysShort: [\n    'Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'\n  ]\n}\n```\n>**Formatting**: See full list of formatting options in the [wiki](https://github.com/terkelg/prompts/wiki/Date-Time-Formatting)\n\n![split](https://github.com/terkelg/prompts/raw/master/media/split.png)\n\n**↑ back to:** [Prompt types](#-types)\n\n***\n\n## ❯ Credit\nMany of the prompts are based on the work of [derhuerst](https://github.com/derhuerst).\n\n\n## ❯ License\n\nMIT © [Terkel Gjervig](https://terkel.com)\n", "readmeFilename": "readme.md", "description": "Lightweight, beautiful and user-friendly prompts", "homepage": "https://github.com/terkelg/prompts#readme", "keywords": ["ui", "prompts", "cli", "prompt", "interface", "command-line", "input", "command", "stdin", "menu", "ask", "interact"], "repository": {"type": "git", "url": "git+https://github.com/terkelg/prompts.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com"}, "bugs": {"url": "https://github.com/terkelg/prompts/issues"}, "users": {"raybenefield": true, "reyronald": true, "sternelee": true, "daveskull81": true, "robsoer": true, "isayme": true, "tg-z": true, "aim97": true, "yang.shao": true, "thcheetah777": true, "wangrongding": true, "z164": true, "flumpus-dev": true}, "license": "MIT"}