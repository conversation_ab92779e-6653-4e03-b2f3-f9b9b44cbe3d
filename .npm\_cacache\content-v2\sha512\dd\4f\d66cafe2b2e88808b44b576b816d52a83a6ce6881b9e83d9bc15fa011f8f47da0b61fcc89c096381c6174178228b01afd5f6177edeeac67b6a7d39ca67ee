{"_id": "buffer", "_rev": "181-3a450708a6d261d064597ac4fa2633fd", "name": "buffer", "description": "Node.js Buffer API, for the browser", "dist-tags": {"latest": "6.0.3"}, "versions": {"2.1.1": {"name": "buffer", "description": "Node.js buffer API that works in the browser", "version": "2.1.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org/"}, "bugs": {"url": "https://github.com/feross/native-buffer-browserify/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}, "devDependencies": {"benchmark": "*", "browserify": "3.x", "tape": "*"}, "homepage": "http://feross.org", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/native-buffer-browserify.git"}, "scripts": {"test": "tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "_id": "buffer@2.1.1", "dist": {"shasum": "f5ae3a059e367994ebae8babd0c13495d07f0999", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.1.1.tgz", "integrity": "sha512-XsOtTDYX7Dw8YCun5y8JkJ6mL49iI95mX2fLHV9UbzXIZeHCfa0Ay9Ni33cO3eTa71GeFSaPgFuLHARyw8Ck4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEVyY61UoiTJ+8Sqqixy1EZTIFuuUu5AB9UQ6Ixhv16OAiBGcnyXxaFj6tFQ+A/oVvDBMBeCqHcyyJlR3ocsEtdWgw=="}]}, "_from": ".", "_npmVersion": "1.3.25", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.1.2": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.1.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org/"}, "bugs": {"url": "https://github.com/feross/native-buffer-browserify/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}, "devDependencies": {"benchmark": "*", "browserify": "3.x", "tape": "*"}, "homepage": "http://feross.org", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/native-buffer-browserify.git"}, "scripts": {"test": "tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "_id": "buffer@2.1.2", "dist": {"shasum": "db239d9d20f06217b01d1803d043fdbd63a4e5a4", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.1.2.tgz", "integrity": "sha512-DoNqoNq0ayuHtf59PjT78hl6DudKNvIpgrJLGwwOj4u65VfnFCxaVXW9jre+IuMkNFifodbgNHgvHkZKB64dDw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCwBfHF33FWHFB4WDBTLQ4OHoFokuyo1otU2bCgkUbUaQIhAKUmBW/szUwYXucqHivhyPxpwj/k0EZEvxhtahr07+Z4"}]}, "_from": ".", "_npmVersion": "1.3.25", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.1.3": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.1.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org/"}, "bugs": {"url": "https://github.com/feross/native-buffer-browserify/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}, "devDependencies": {"benchmark": "*", "browserify": "3.x", "tape": "*"}, "homepage": "http://feross.org", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/native-buffer-browserify.git"}, "scripts": {"test": "tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "_id": "buffer@2.1.3", "dist": {"shasum": "ba4bb542ea753589886ad18f1a5e0537bd543761", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.1.3.tgz", "integrity": "sha512-DvaT/DmVVA9o9yAiQ7XGE/IWM/w2uMK3jpOJdkkm+kx5Vctg7CWDCJlzdy4VS0WZqLqd62+563K2U2rYCbu8rA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCBRQ+/XuUXYtgdxsNRFNYbnDlUAmZmMQKG8SKe6DNnkwIhAPyt22mjDgrwg90TFOvF1eCBJd9Pe+O4Sy+V7TzXqHC6"}]}, "_from": ".", "_npmVersion": "1.3.25", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.1.4": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.1.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org/"}, "bugs": {"url": "https://github.com/feross/native-buffer-browserify/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}, "devDependencies": {"benchmark": "*", "browserify": "3.x", "tape": "*"}, "homepage": "http://feross.org", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/native-buffer-browserify.git"}, "scripts": {"test": "tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "_id": "buffer@2.1.4", "dist": {"shasum": "0283e62870b4f450b636e00eb9fd527cd9ae1341", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.1.4.tgz", "integrity": "sha512-kR4d8IXZ8bm532CBsQKLt6P0/ltEIT/irgnltIgDodVbiIyFv/KUTYvc3ZnwBZg6GiYdSSeHBJtCvYalgPm2kQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDcC9pEkgXC2pwwv6BIpNaeYYQbVSj3uBpp8k9bJWstUgIhAPmesIANO1DDS/tgTgky4HhRDgiyJjklKzkz4xrQi9O/"}]}, "_from": ".", "_npmVersion": "1.3.25", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.1.5": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.1.5", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org/"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}, "devDependencies": {"benchmark": "*", "browserify": "3.x", "tape": "*"}, "homepage": "http://feross.org", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "_id": "buffer@2.1.5", "dist": {"shasum": "15373190bc584061de5cbc155db82a0dc1a462de", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.1.5.tgz", "integrity": "sha512-1p5j/jaBJ/0foY3HiYhNwMe/7m2n5yHvQpKQQbpn/AKj+o+gQLeoahovZ1sJTYDthOTmRZAwllelGk2uVYL3gQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDSa4q8uV8AQqW/Jsmn7Jq+UOno1IA6weCqi5VtRtdHywIgJlHaPmqL8BQbvpcGLLKq3U6frOd7o5PsYcjelRpbEpo="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.1.6": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.1.6", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org/"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}, "devDependencies": {"benchmark": "*", "browserify": "3.x", "tape": "*"}, "homepage": "http://feross.org", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "_id": "buffer@2.1.6", "dist": {"shasum": "7704440344bd2a24e78e03940754cd02e8e30aa7", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.1.6.tgz", "integrity": "sha512-EThAWkVXPO2z3/vxvx2wudb/qY9yg9subUlIK49jHzV0y7fQJJwld0F8oDeMt/eTidQVjIWNthakqeveVCWiDQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC1QzEgndKyepmzh+AAgwOFL3+PsvTNRvqBF/BWEKU/eQIgOkRDGvseMLh7qgV6VQ4rd36xxsJwAMOMSznEfZmpVu4="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.1.7": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.1.7", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org/"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}, "devDependencies": {"benchmark": "*", "browserify": "3.x", "tape": "*"}, "homepage": "http://feross.org", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "_id": "buffer@2.1.7", "dist": {"shasum": "958161f6a68ae690d14040ffc90f513d0ce4e768", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.1.7.tgz", "integrity": "sha512-Yb2VRnhDu5ygULk5eyFfbBqv4Yx0xfQF2BcN0k1G/X6oGdv1o/1u6oRX2HygbbNX20WZ76iOYnK5bbGB6IGkzw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDPaV8IypzHbSn2Y2OUvLgfHdGlylbUwi9+B9AZvIMhxgIhAKMdoX8aKFw/qgphqp5BQv6PiuRLr4DMF6RhaeEMzbS3"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.1.8": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.1.8", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org/"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}, "devDependencies": {"benchmark": "*", "browserify": "3.x", "tape": "*"}, "homepage": "http://feross.org", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "_id": "buffer@2.1.8", "dist": {"shasum": "8e13c9f98914b1d52ac0f529fabfe2b361b6587d", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.1.8.tgz", "integrity": "sha512-1Kw+24Ino3wzBI0DaH6XjcPaB8/J5PxUL2sSrlKunAamiD1nKyve28Bbt24iaxt9qlu7W3bMkw0zVM9+yL55bQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDP7O1O3RQCHd1mEtaveKWERt4JgBqHpJfN/ZyanDEXrQIgRYI+seNDE+gm8PYQQIeyTAJVQV74L37qaOEOcVlB6nY="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.1.9": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.1.9", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}, "devDependencies": {"benchmark": "1.x", "browserify": "3.x", "tape": "2.x"}, "homepage": "http://feross.org", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "_id": "buffer@2.1.9", "dist": {"shasum": "58e465c7da09901e1e22e1af9d3df6a6e4041cc9", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.1.9.tgz", "integrity": "sha512-U7UNoJmHFKgZ6SIeCmMA2xwnh5EpB13N8Q0cYC0FzvCeg7xloplnJnaXmOSHv+Gn+lHdnUw3mFO4T+2L96NG9g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCph8JDOUTTQK01xS/2X/MGNkbOpOT2Br0OXTYiCP1+BgIhAKUoB5RoPX8QitU7527iAwacmsCLwZQNlQhyBl2AIEVV"}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.1.10": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.1.10", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}, "devDependencies": {"benchmark": "1.x", "browserify": "3.x", "tape": "2.x"}, "homepage": "http://feross.org", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "_id": "buffer@2.1.10", "dist": {"shasum": "1ff0004281ba7b1c7d755016bfc188fb24867a6c", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.1.10.tgz", "integrity": "sha512-9S+bAcmR2aeXpYsR2f5tSGxRqHg/vID62zOvPLcOE3XHPvDqRzSszu5fdBp6zESfSAbi/NNGLbzCP+Nknym3fQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDopmqR1HUT8TyJ22eGaWRHfX/r5VwBd0gTZREVwuJ0pAIgJZQIAH3bUrWrm2BE+JO+R5pVueQ4wgp1py8efvdfyig="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.1.11": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.1.11", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}, "devDependencies": {"benchmark": "1.x", "browserify": "3.x", "tape": "2.x"}, "homepage": "http://feross.org", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "buffer@2.1.11", "dist": {"shasum": "a0291a4e7291c60c07cd6928bac4f7f4e1611b4a", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.1.11.tgz", "integrity": "sha512-Bo1TkiluVtFlGwTxn2Noi5nA++4pQAFm6FV6ScCLjxfMlnzmDQCPijNQtQAMt1lUZNJTdMXeD44KCIeygAMd6g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTHfiygXaFC3+bymtGp+UmYjWJCiM/Neg3aXXg+UhQpwIgJIkO699NYVImZqogQvHhDFHlltTsGXJo0g4d8FkbKwg="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.1.12": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.1.12", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}, "devDependencies": {"benchmark": "1.x", "browserify": "3.x", "tape": "2.x"}, "homepage": "http://feross.org", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "buffer@2.1.12", "dist": {"shasum": "da6270d276406da92cdf357b5fb09e1edc547f22", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.1.12.tgz", "integrity": "sha512-wm4tFP3KncO3Clg9Uv0vol4GZ3IsJ4frjm28RgncS6lBsK6YEleqcfEMj88RvBBNRukXb/TmB7SJ6he8f/EbVQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAjKSkn0ztkclyeHQelyYYUgxpmMin4P9cC8BGlgdLtYAiEAvTRwsnORNzwCp2e1Bw2gG61xoEv1hkaf+0n31YyEzFQ="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.1.13": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.1.13", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}, "devDependencies": {"benchmark": "1.x", "browserify": "3.x", "tape": "2.x"}, "homepage": "http://feross.org", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "buffer@2.1.13", "dist": {"shasum": "c88838ebf79f30b8b4a707788470bea8a62c2355", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.1.13.tgz", "integrity": "sha512-MBwiv6k5+FIvbBMZSCn8ol6hzf//muWr8CuIFEZK3KhSQbClDcm99ayh9mEuZXcOTE9Y3J6wC+iOQyVbpMFmEQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC4mGQGjIhFwuVo9+ha6teOgZcexhhVrxDavcOd19CsSAiAUwEggq0RhWJOA8vK+GUnWXnvN9YHPehdPxvQuNLerjA=="}]}, "_from": ".", "_npmVersion": "1.4.3", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.2.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.2.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}, "devDependencies": {"benchmark": "1.x", "browserify": "3.x", "tape": "2.x"}, "homepage": "http://feross.org", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "buffer@2.2.0", "_shasum": "d19d0ab2bdea90d06a5c7bae0675b6fb6ea1aa5c", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "d19d0ab2bdea90d06a5c7bae0675b6fb6ea1aa5c", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.2.0.tgz", "integrity": "sha512-fuGvVBtZcp7TPeKqd0eB6q4h/9esd9nd+WG0kkKpNJClvDxLHFsYL/YZIgS9jwpuvnllhgLvN6c7LtYcPWGRbQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC3bbtT4BhmsYi85rwvuw6gwaMJPXZgsyK8jLfv+5OE6AiEAt8n0njWprTJs5Q/PSaN8H+eF6xmFG4NQsO5G+zDGWdk="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.3.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.3.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}, "devDependencies": {"benchmark": "1.x", "browserify": "3.x", "tape": "2.x"}, "homepage": "http://feross.org", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "buffer@2.3.0", "_shasum": "fe817aa92f962b591a2b9a013e6a01a9ef3ebd02", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "fe817aa92f962b591a2b9a013e6a01a9ef3ebd02", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.3.0.tgz", "integrity": "sha512-KsUj4/A0ct2H5+rigLLOzWUAoaof4G6OP3o2ECofk0RllgTSGeSG9hDrcL912nB161WJHtJxkPZZJWATwjldYg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHq+Fu8fL5Rck6x1HbaDazDAUKsUUd+R3/wuuyyB/TuaAiEAu59Gysw4cU67FOm44bapwbtkdGVhE3IQ5SqP28p/vpI="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.3.1": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.3.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^3.46.0", "tape": "^2.12.3"}, "homepage": "http://feross.org", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "863b2f3690ccb4e076aaf2bf6028e943af849484", "_id": "buffer@2.3.1", "_shasum": "7f89c89d62e4e207fd0f10181918314330c88ee9", "_from": ".", "_npmVersion": "1.4.13", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "7f89c89d62e4e207fd0f10181918314330c88ee9", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.3.1.tgz", "integrity": "sha512-fpPIlNcqnugi9p7mz1Ww86X9FoXBl1GZOPP/c79VZL9cGH82i/QdzKRaNFWIZqfPRCHjuYoTrk7izAlWfKCaww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAcB5z2erBSKZWgndan637q9LGbZzjJT3KWsuzJhye7cAiEAis0oYsQjLFjLecOc1KI/6XNmkOZQQvNqVIG+3/o/kM0="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.3.2": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.3.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^3.46.0", "tape": "^2.12.3"}, "homepage": "http://feross.org", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "buffer@2.3.2", "_shasum": "05f14d173c73d24f21045a9f83e1c396ae34d74b", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "05f14d173c73d24f21045a9f83e1c396ae34d74b", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.3.2.tgz", "integrity": "sha512-DM3<PERSON>+bL8Pfa7H/Am9eXXVHmYtyMSez8K8KdaqbSi7hhWP94oColJZTo5ZK+Sq/Up9SyiTfD3Wr9/7yYPvm/SYw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCmhLk66HnF0Riv0Iwa4VCz8q0I0+eFgAXOIiFc1NX/sAIhAJGZIXXED2qXGMn/85gWrT2aIjGcUSDrq4uYOhdPNaY4"}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.3.3": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.3.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^3.46.0", "tape": "^2.12.3"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "73f5f9780aebd4240bbe01a8ac77314fd28ff7fd", "_id": "buffer@2.3.3", "_shasum": "eabd37c725a67ecab17a29dc3ae2b50cbd181181", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "eabd37c725a67ecab17a29dc3ae2b50cbd181181", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.3.3.tgz", "integrity": "sha512-+f2rV1+vRMZ2ToeZ8JAxhcQQtW3LglQUB1IORGGwqAMwlUIg2PsD15cdJGhQ1CadabVjwzWi0fzQMVs4OChifw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHRYyzXxQ0HB/hvFAoBpmysTJ69Z8r8BR/YqzVqUB2ucAiEA/1/jcHe/feg/FkvoeC5eLTCk/sw1jaMkFA7TQfyx8Ws="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.3.4": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.3.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^3.46.0", "tape": "^2.12.3"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "buffer@2.3.4", "_shasum": "7e4af5a23c15e13fcbfd5c5a1ec974cb61668a4c", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "7e4af5a23c15e13fcbfd5c5a1ec974cb61668a4c", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.3.4.tgz", "integrity": "sha512-MNqufiw8CQ3yoNU7qRiccXWFd4NlelGBNTJYbauYlOCj+0SEJOIsvvrlhSopsoW0FxZAAq4yXO53I/VREIaGKQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHvsRb2HAJDHZRUc2QYr+QJcfEfPdc9uNV1Fz48i7ULJAiB7VQ9P8LTq28b7plrG5Bni8TDJWblVU6RH3On7JE856A=="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.4.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.4.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^3.46.0", "tape": "^2.12.3"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "buffer@2.4.0", "_shasum": "2df0569b20a7ea00e3ab43f90acfaa64a756f9f6", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "2df0569b20a7ea00e3ab43f90acfaa64a756f9f6", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.4.0.tgz", "integrity": "sha512-pa/AxN5+QNijznJGxVspzNb8Z7ktl4pn0DQVTQGi39NGgvcPbKMystf9hX3js9E0n+b/wxpBuuNI06oIIc4A3w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDI2c7hsbV2vSp11QjJrmr4NPggbjHSwSSEgHtnIYMsGAIhAOGIAnMzLb8uabf7bOssVgcsYgOcMXO7aW7NxpYpVsAo"}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.5.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.5.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^3.46.0", "tape": "^2.12.3"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "582b9bac3594d01954866e45606cce76ea9503d7", "_id": "buffer@2.5.0", "_shasum": "ee451ce8cd122dc922027674338dcef9e0eadd9a", "_from": ".", "_npmVersion": "1.4.14", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "ee451ce8cd122dc922027674338dcef9e0eadd9a", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.5.0.tgz", "integrity": "sha512-WW7O+l3DT8WpkZDCpdKlRI9TtcJHKgXCWWtG79oGU2hd7POcRVsRRmLgznAYzQivCNnTzRRacRN80zu5rLhM9A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDQ5k30mIK/1LZyO3ksULlTR+XbPbGaJdPgBPJnHwLJXQIgERXzhS+/9QPMIfTWGd6PyBH8eUr4qUWFZ/DOJvKZ0QY="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.5.1": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.5.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "~0.0.4", "ieee754": "~1.1.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^3.46.0", "tape": "^2.12.3"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "3b7954e95ddbe5cb787f865f5f553a9fba4b86f3", "_id": "buffer@2.5.1", "_shasum": "00c6fec92134c0e3326bf3b33e76390800e00299", "_from": ".", "_npmVersion": "1.4.23", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "00c6fec92134c0e3326bf3b33e76390800e00299", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.5.1.tgz", "integrity": "sha512-aUmEjEuGR/moOTfuunK7kt8e3w6RV6cidpyAtTWZH4cAA8eoIZeFq2FbWNFaaPhuV3CyFBF2fJMJteNwpk/3+w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDUAdsMSuwDxuCNkGJ2lovsKpO5d13xPguFXxRBWO8GtAIhAIfu4dPSIG+6nhInshfWps5BvVog/RK9yc6E0kC604o0"}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.6.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.6.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.7", "ieee754": "^1.1.4"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^5.11.1", "tape": "^2.14.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js && OBJECT_IMPL=true tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "buffer@2.6.0", "_shasum": "63a301efecda11a858ed31394eaefa63be73287d", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "63a301efecda11a858ed31394eaefa63be73287d", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.6.0.tgz", "integrity": "sha512-cjtVTmP1ULWSE1vOCP6O7Ak8fG97koT611NZLpBAfh4AhY4SVyB+3TrSjaS5bWURdbJa9Zn5lytKTEjMlPNGhg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHQs042pIkiPlfXOW6dMTJwC4LUj8E2zlFddma8sAYKeAiA4UgiLUjM3dKUDYZa2nXtWMPnPA9NJcxe2B9yACWpelw=="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.6.1": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.6.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.7", "ieee754": "^1.1.4"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^5.11.1", "tape": "^2.14.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js && OBJECT_IMPL=true tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html", "perf-node": "node perf/comparison/bracket-notation.js && node perf/comparison/concat.js && node perf/comparison/copy-big.js && node perf/comparison/copy.js && node perf/comparison/new.js && node perf/comparison/readDoubleBE.js && node perf/comparison/readFloatBE.js && node perf/comparison/readUInt32LE.js && node perf/comparison/slice.js && node perf/comparison/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "buffer@2.6.1", "_shasum": "c1dcbb37b6f814433d5da789639980d7651fbe39", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "c1dcbb37b6f814433d5da789639980d7651fbe39", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.6.1.tgz", "integrity": "sha512-fm65W87EBDO7KxRUTOyXTSUFlU4PlTfe+aGN29HhC5bheELE2b+ODPVAdh071dFmq0Ynd9ZULzPzn59OrCycXQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCOvSd8tboT4g2nEW9EsEkyeW/IlnnDkdGhw3CN+ZnkkAIgfJqgtUd5glnT1vK3sg3M3m03FocsLSrwbyfOMIMfDWQ="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.6.2": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.6.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.7", "ieee754": "^1.1.4"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^5.11.1", "is-nan": "^1.0.1", "tape": "^2.14.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js && OBJECT_IMPL=true tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html", "perf-node": "node perf/comparison/bracket-notation.js && node perf/comparison/concat.js && node perf/comparison/copy-big.js && node perf/comparison/copy.js && node perf/comparison/new.js && node perf/comparison/readDoubleBE.js && node perf/comparison/readFloatBE.js && node perf/comparison/readUInt32LE.js && node perf/comparison/slice.js && node perf/comparison/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "buffer@2.6.2", "_shasum": "bd552e3b834a80ae2fd8e80c2087eceec7353ad3", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "bd552e3b834a80ae2fd8e80c2087eceec7353ad3", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.6.2.tgz", "integrity": "sha512-/TLIAsqUzCFF/jdvnnWLLBdaHOFjirL3sogJQR1r/qmUsl02AQ382rkjCrz+VsSJxjtOR8K+NxDVPuxwBomFAA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFrbCcNPz7gIGOofYo2c/eoBeWNvGT9UATAZEy+e39PAAiA+etfLFkkEoaqbpb+OPnGV2pA9z9BQGRP9Rh19YWm+Ww=="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.7.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.7.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.7", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^5.11.1", "is-nan": "^1.0.1", "tape": "^2.14.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js && OBJECT_IMPL=true tape test/*.js", "prepublish": "./bundle.sh", "perf": "cd perf/solo && browserify --debug readUInt32BE.js > bundle.js && open index.html", "perf-node": "node perf/comparison/bracket-notation.js && node perf/comparison/concat.js && node perf/comparison/copy-big.js && node perf/comparison/copy.js && node perf/comparison/new.js && node perf/comparison/readDoubleBE.js && node perf/comparison/readFloatBE.js && node perf/comparison/readUInt32LE.js && node perf/comparison/slice.js && node perf/comparison/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "_id": "buffer@2.7.0", "_shasum": "02dfe9655c097f63e03c1b1714ca6e3d83d87bb2", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "02dfe9655c097f63e03c1b1714ca6e3d83d87bb2", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.7.0.tgz", "integrity": "sha512-qp/xUZWFGlAFeTRCwhOwxM/jM3dZobC5I/25J/ZSWCBlYzHBZlC3mjUX0bzC18b0T1YWvyktzo6p/mZpllahYw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDoftVChq9j7X/MiBdOky0p5zdP2hVRFQYamj7js1qJugIhAOjCkxNMkk0ZWkX8Me1hqYz+K7QPEyV9eKCicEh93YtJ"}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.8.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.8.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.7", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^5.11.1", "is-nan": "^1.0.1", "tape": "^2.14.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "tape test/*.js && OBJECT_IMPL=true tape test/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "fbecb716ccbcfeefbd53f9feed2c970236b06124", "_id": "buffer@2.8.0", "_shasum": "f6b5aa5822b51507af1da77c65921386ca215478", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "f6b5aa5822b51507af1da77c65921386ca215478", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.8.0.tgz", "integrity": "sha512-14/mr9cHEybX0FYT6Ni13AlqLMJi5jNNUcsrzlQxdszUtWEsemGOzkmvEGw1P30PKpgQm3BWZW67+65j/CI7CQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8ROCoIyjS07m1Gp02s7VbW30tasRcWgRmKUXF6cQGTQIgLoINOa/QEyhTZ8OA8IWAlj2ejyyERpGjFUeFXNf/Z+I="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.8.1": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.8.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.7", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^6.2.0", "is-nan": "^1.0.1", "tape": "^3.0.1", "zuul": "^1.12.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "npm run test-node && npm run test-browser", "test-browser": "zuul -- test/*.js", "test-browser-local": "zuul --local -- test/*.js", "test-node": "tape test/*.js && OBJECT_IMPL=true tape test/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "2e04bb00bf3d4b1ea2b2c1801b3423f48b1e39cb", "_id": "buffer@2.8.1", "_shasum": "6c632bf47cb7ec86509254ed42ab080937986114", "_from": ".", "_npmVersion": "2.0.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "6c632bf47cb7ec86509254ed42ab080937986114", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.8.1.tgz", "integrity": "sha512-d1KV8OTNNPHlT4+oxKmgO2F7nqI+VJHU5qVUCUfqrAY75Vwq45hQ1QHYBm2kZcsjJb/YLAx6QTxLU1EmR2dA5w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCyy2RPhvtayl3NbbxuLDASvSQGBWpZD9Iv3+Mr8v9s5wIgRubZ9uyZfxiPVldxl+NoYlUvU3xsyAFoIeJ6Ys0HtAk="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "2.8.2": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.8.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.7", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^6.2.0", "is-nan": "^1.0.1", "tape": "^3.0.1", "zuul": "^1.12.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "node ./bin/test.js", "test-browser": "zuul -- test/*.js", "test-browser-local": "zuul --local -- test/*.js", "test-node": "tape test/*.js && OBJECT_IMPL=true tape test/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "30387775a76229d8c39298b0e857ac158b8782ae", "_id": "buffer@2.8.2", "_shasum": "d73c214c0334384dc29b04ee0ff5f5527c7974e7", "_from": ".", "_npmVersion": "2.1.11", "_nodeVersion": "0.10.32", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "d73c214c0334384dc29b04ee0ff5f5527c7974e7", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.8.2.tgz", "integrity": "sha512-H5ab/m4kriHgeOEYtIKTZLHrkls2xJAPZz2xChJ9q+arlSqQuTfSr3plKOVW7FxTo/cr9q8a5BA6O7Ar0v+7/Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDQxWtYeYWIZsBYkeGj96ACUr6GpKFL4tSASqJNmrFkrwIhAL4/7wJUoHpcwsHksXKqPIT1LDH+gLVUsBl0wUGBPWSl"}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v2.8.3 or newer"}, "3.0.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.0.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.7", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^7.0.3", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^0.3.2", "tape": "^3.0.1", "through2": "^0.6.3", "zuul": "^1.12.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "node ./bin/test.js", "test-browser": "zuul -- test/*.js", "test-browser-local": "zuul --local -- test/*.js", "test-node": "tape test/*.js && OBJECT_IMPL=true tape test/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "580e30376ab273d7314243a9a98bf9c3a600b93a", "_id": "buffer@3.0.0", "_shasum": "38a0925db67e125cd6c7a34c25afbf3e46117b7a", "_from": ".", "_npmVersion": "2.1.12", "_nodeVersion": "0.10.32", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "38a0925db67e125cd6c7a34c25afbf3e46117b7a", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.0.0.tgz", "integrity": "sha512-MJLVphUZZhS+8nHdqrEF1fnFxe99jUwBnSHBW6p9ypEHK225yuiBk4rWYWuEDehDhHSZchiv7rnsdt0QfZds/Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID/4PeWCcamM5itrAi3Dfp80ClSu1o0+QgHrL9+82/TXAiEAjnEFEiFd19yEWRvDjQ4xvu1RMzDL9ihErE530dbY5XI="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.0.1": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.0.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^7.0.3", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^0.3.2", "tape": "^3.0.1", "through2": "^0.6.3", "zuul": "^1.12.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "node ./bin/test.js", "test-browser": "zuul -- test/*.js", "test-browser-local": "zuul --local -- test/*.js", "test-node": "tape test/*.js && OBJECT_IMPL=true tape test/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "7be200aea0ce70e3eaf7798a1b63325f6978e46a", "_id": "buffer@3.0.1", "_shasum": "d2743fff2b1d92ad532dd5716ecd9217838dfb3e", "_from": ".", "_npmVersion": "2.1.16", "_nodeVersion": "0.10.35", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "d2743fff2b1d92ad532dd5716ecd9217838dfb3e", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.0.1.tgz", "integrity": "sha512-4NOMWvuyGW3pkMRbruG6TkPeoNdFpCO8j26a+EUORW5WE8Rkpe7BQOLTrKgc5bpeYIcxoeAX2XYqyEcNtSB9xg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB40E8mcDaGl8xVqpJIzw3N6VV/UUyKVQJ5BG0VcgS1cAiB++M+GH5OB1WMLz4jZtW4Zc4NQ07TEbmOIPY/4/ly8wQ=="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.0.2": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.0.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^7.0.3", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^0.3.2", "standard": "^2.0.0", "tape": "^3.0.1", "through2": "^0.6.3", "zuul": "^1.12.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/es6/*.js", "perf/*.js"]}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "50398b7ce6ed5c256b050479782bce2dcde4a011", "_id": "buffer@3.0.2", "_shasum": "4f6513750dbe278300fa903da9d7b4b1745b480e", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "4f6513750dbe278300fa903da9d7b4b1745b480e", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.0.2.tgz", "integrity": "sha512-MquisEYtZgKOpL9lHWYdZSoOYgyOVa7ORTbGUazd1dTCdF9yo/lPfGucDHR+qss5zlS3t4HS1XIfj1a37EZvbw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCE2E5xNCoZAU/zexH++WiZaFptbR9VVH+oOtOueuMzlgIhAIq4EqIi6R2wMKTRbASDAWReMPZS0wWjJado5aXYTJp7"}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.0.3": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.0.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^7.0.3", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^0.3.2", "standard": "^2.0.0", "tape": "^3.0.1", "through2": "^0.6.3", "zuul": "^1.12.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/es6/*.js", "perf/*.js"]}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "5b50c8e641e87021f0d368eb3273baf39fa33af3", "_id": "buffer@3.0.3", "_shasum": "93d8a236e8ee37941cdaf801eb8cd4117192ece6", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "1.2.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "93d8a236e8ee37941cdaf801eb8cd4117192ece6", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.0.3.tgz", "integrity": "sha512-aFdrOEagqV2ZhspPfHUTq0Iv8nQFIk6LjgSVEz2qgeT4AzdL6Kctr9m07K8vugH8lkFmhkYQNpmLCRJd8Z3Tow==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICGCkrNqlrHbZ6DLMVVXuNzKmT1o3IALXGgDLc/9Yoy0AiEA9OlvAMto+MS5UYTm6CRk750fPFy9/qN3xBkAHSuTSW0="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.1.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.1.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^9.0.3", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^0.3.2", "standard": "^2.0.0", "tape": "^3.0.1", "through2": "^0.6.3", "zuul": "^2.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/es6/*.js", "perf/*.js"]}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "268ef0a9230e0bcf53de7b43d6e6dca81ad6d4d6", "_id": "buffer@3.1.0", "_shasum": "525ca35ba81f1b240072c312ac3b6477da6fe10b", "_from": ".", "_npmVersion": "2.7.0", "_nodeVersion": "1.5.1", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "525ca35ba81f1b240072c312ac3b6477da6fe10b", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.1.0.tgz", "integrity": "sha512-VlwDKVgkX2f6bTUuHk2g+bCSLoivNdamh6VfNPcY7I31Jd++Fe7xEYexnd7oFZgOxDGhcmqJMpmN3G+zM6nrFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/HSWokeMvtS4o9gBPNVeczAnHEFKQUfZSJZgWD0wz6AIgXe+xbN8dpgEQCceqrSZC744QZJxt243H0cASQ//hVjM="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.1.1": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.1.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^9.0.3", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^0.3.2", "standard": "^2.0.0", "tape": "^3.0.1", "through2": "^0.6.3", "zuul": "^2.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "6535f1789cddf6c9dbc60e85ef4d5a455afc6beb", "_id": "buffer@3.1.1", "_shasum": "c2ab41165bd3cf22077af7404e4b6a42df6c1b6e", "_from": ".", "_npmVersion": "2.7.0", "_nodeVersion": "1.5.1", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "c2ab41165bd3cf22077af7404e4b6a42df6c1b6e", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.1.1.tgz", "integrity": "sha512-xODcs9H76hTKt9OFU9pkD/s24CI5WjuQ4YoIdEt8L+ILl2Y/VCMk5iACaaZRRChpeUiHzNxko/ut1orcZkxq1Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEjWuq0QIWKBCc4zP6rRxzaPX7vU9whdJtvU9kZp6cO9AiAUEsX0qOYxxmCknyJH0FNjMbVg0zBxXSbicN/ZyWl+Vg=="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.1.2": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.1.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^9.0.3", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^0.3.2", "standard": "^2.0.0", "tape": "^3.0.1", "through2": "^0.6.3", "zuul": "^2.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "8aad53291695224b82845facce8d15c69dedd59b", "_id": "buffer@3.1.2", "_shasum": "1c679611b961edf16b9c4daf44fb66beb9daa9f0", "_from": ".", "_npmVersion": "2.7.1", "_nodeVersion": "1.6.1", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "1c679611b961edf16b9c4daf44fb66beb9daa9f0", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.1.2.tgz", "integrity": "sha512-trlQOKyrB7HnjcSnC+qQdnKPATuwgJnOS91HRMu4bkkDhiP+sGGfBS95OfcEVImXg04E7WGOzCmAJXdcsM9mKw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGLTgoNU6r4gbU6RWm3gO2vz3yb/dMZS5H1SJSN656NHAiEA1bO1Ski82wRrm6HjEauuKns8APAmTjACzz2UEm5fT4I="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.2.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.2.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^9.0.3", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^0.3.2", "standard": "^3.6.1", "tape": "^4.0.0", "through2": "^0.6.3", "zuul": "^2.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "4d72d149e969ba7b6988a1ab1b36703fd7ca3837", "_id": "buffer@3.2.0", "_shasum": "18ff6e56a51412774ef65b0ec059898319f0c0f4", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "18ff6e56a51412774ef65b0ec059898319f0c0f4", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.2.0.tgz", "integrity": "sha512-H8SgJ+b8ekQjm65pQVe+7OHC71+nBtD2nJ1rqbonm+5aSOmLn1Uafa/++0zp27NU2l+zhcU1eYh1BsiMTQBEOg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHwPbcicK+v6Xmjp1sqyW15ThNmL7v6M0Waag87wdy+lAiA2tMOBai/vJrjsrv8MKHb9gQAbJ98wcudCXUoCByPJTg=="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.2.1": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.2.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^9.0.3", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^0.3.2", "standard": "^3.6.1", "tape": "^4.0.0", "through2": "^0.6.3", "zuul": "^2.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "b9d9372418395565b3f398a391e5c554c2877251", "_id": "buffer@3.2.1", "_shasum": "2ed75374e505cacd2517a51d6b354954b2c59c05", "_from": ".", "_npmVersion": "2.8.3", "_nodeVersion": "1.8.1", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "2ed75374e505cacd2517a51d6b354954b2c59c05", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.2.1.tgz", "integrity": "sha512-ydMF/R27LF130IE1Wp2qjn0o9hsNeUzY8rW0Owyv90A48R82aW76+MxA/RdgHsp697xffWK3aMi52A+ZXKma1w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzVgbyPLIPMrijQmkd+49fMSawQwNcGTn/cvyTbJUi4QIgVUZqG8PdFThWM44lvnn2S0D1YxtPaxLhVvNOTpHJlLs="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.2.2": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.2.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^10.1.0", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^0.3.2", "standard": "^3.6.1", "tape": "^4.0.0", "through2": "^0.6.3", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "0b25bfb16c0b4dfb0656fe1d2daa09a3c15c6dcd", "_id": "buffer@3.2.2", "_shasum": "15d3ead5b994e8170e228540d7ff1286c25aa53b", "_from": ".", "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "15d3ead5b994e8170e228540d7ff1286c25aa53b", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.2.2.tgz", "integrity": "sha512-2dM9BPYaVyATbWbrpaCeCqKkHxnxf36nD64DEVfYd0e0hJ5QaXFK+dJulSSTsi7MPqDHqUsi+AI7xZwUSH2Bbg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBh9zjTqZryLr1ukuZUbn5cKTcl85+tHp85p4WsKx4F4AiAoYjip5sy+yjgfs86IpmPwRbbRFrqz3iwggquP3c7d9g=="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.3.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.3.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^10.1.0", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^4.3.2", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "gitHead": "414f1804a76755919e450e1cddcdd4a76c00a3a1", "_id": "buffer@3.3.0", "_shasum": "f0b86b9c24492f4b621f8e0da7a75cf74e4d9bc9", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "2.3.1", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "f0b86b9c24492f4b621f8e0da7a75cf74e4d9bc9", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.3.0.tgz", "integrity": "sha512-phXgQOOqSIYjh3sicjfW1P9ouFcPPBrGe9aVZB+k8xHki1KxxAa6LryGbtwz0wAtzWPiCFXrQjznCx1DDGDNmQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD3VYAhCpAzbGYBBVZEjSlfb/P6hKRDseB4G+JIaIKWSwIhALSYSWNujDZCnvXUMxxwir0pboo2noIw92pJ0zUPDekq"}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.3.1": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.3.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^10.1.0", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^4.3.2", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "gitHead": "2faa3e5a76c68923b0bd1660f9016ab12170f0a0", "_id": "buffer@3.3.1", "_shasum": "c87bf2db2aa8e82f78d41fcfb82b40bb033bf44e", "_from": ".", "_npmVersion": "2.11.2", "_nodeVersion": "0.12.5", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "c87bf2db2aa8e82f78d41fcfb82b40bb033bf44e", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.3.1.tgz", "integrity": "sha512-aoulYhFqLdeUYFidNT+Sa636DM6oq+BSl8zlvQ43N9ywKAzNpgZ8uRFYPXEQUrspASat7DCcpvKrOj0wvSA5uQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICcoHwbZuJ2uTdyWZgBj5oarip2KXxCMBRhZXGp6IycEAiBK/q6oAF1gjpUFGmF3DINoaapzKVE+c1WqBUeg4Xjtxg=="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.3.2": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.3.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^10.1.0", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^4.3.2", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "gitHead": "f4927defc0092e52aa6b6e77814d2930d01a2b6f", "_id": "buffer@3.3.2", "_shasum": "cf64be33cba8e62a98e67276429a4f3b5ece5f81", "_from": ".", "_npmVersion": "2.13.2", "_nodeVersion": "0.12.7", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "cf64be33cba8e62a98e67276429a4f3b5ece5f81", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.3.2.tgz", "integrity": "sha512-30ODLmvAKdUpinnfeDwu+26qLna6RuMRMlbAny83rL0ZLV0HiHtRCL8vYwcfD2IN/WxJvM6PgFeQ4qbCNq8VYA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBA09dBqyUykodbCv4BcndwnKENafRR3TY6vFwh68xczAiEA41RuVCr9wEkNOo/e5nkfFiFgZoLonvAS/MUWn4XnpPc="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.4.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.4.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^11.0.1", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^5.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "gitHead": "07014a3b0ea8ec11117710d3505fbb5446d6460b", "_id": "buffer@3.4.0", "_shasum": "7ded568ab4faaaa35246af2fe26522317f0d1ee7", "_from": ".", "_npmVersion": "2.13.3", "_nodeVersion": "3.0.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "7ded568ab4faaaa35246af2fe26522317f0d1ee7", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.4.0.tgz", "integrity": "sha512-trhjMu9EbfZZ2ivK6YmnVV7FboxVwp14QVKQk6vOcev34470DfdcbobJP6hXMDwZLhNcoLfOiXVf6DN/dpe4+g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDuKGFxlhR0zVN7Xw0HbiSzOvIjDZLLARIvBfWYGhGzzwIgGp/GT/Zct8X0ghQ/CbK9+l9eB9XSqI+BHxzkxSDBQiU="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.4.1": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.4.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^11.0.1", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^5.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "gitHead": "39f91335057725b22a1d76347f6cdbef6f564789", "_id": "buffer@3.4.1", "_shasum": "bfe6597d5b1adb7e9749e479d14bab6150fcf1d9", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "bfe6597d5b1adb7e9749e479d14bab6150fcf1d9", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.4.1.tgz", "integrity": "sha512-0cG50iqTo0uwI8xdMR11fFYKU3MvdsPs628ruPi7YicZDUyWTIh5jagSeonG4+B2i6Z6Iq0oigU7kcWvwwynOQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCMMDm8vDKYATVnKWo8P/Zi27Jk6zVr0zWJ3NNoX1C4QwIgcWtCA1TyrkBeEzFC4fiCW06DTAUc2wAdnIBLwSjICns="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.4.2": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.4.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^11.0.1", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^5.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "gitHead": "2a9b4284efb4d78d22b48f822fa67c463d920ce5", "_id": "buffer@3.4.2", "_shasum": "2276a34ca2e4052a0fc606bb9a19f2d19af93518", "_from": ".", "_npmVersion": "2.11.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "dist": {"shasum": "2276a34ca2e4052a0fc606bb9a19f2d19af93518", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.4.2.tgz", "integrity": "sha512-RwovZg9iXbueo7mwH2tgDOHx7mX1TwBeaRECVdEMkztIwo2Sq/gwp63YAV9Lg9QLlnzUUf/eEuWsa6SmXz6KSQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCeNM3lUG61vTQLOErHytNm6gbnM8AkZ+5BKlh60Wfy1wIgDPdQ5Nc9huU3F3UGuxyhXhPENdwnuz1T5MHXfJg8GU4="}]}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.4.3": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.4.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^11.0.1", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^5.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "gitHead": "5ac05a575dc18be18ba91e9f1e93e53b98d5d435", "_id": "buffer@3.4.3", "_shasum": "b35ec60e7e06ab42b6fb020f45f07e7c58ca9f3a", "_from": ".", "_npmVersion": "2.10.1", "_nodeVersion": "2.1.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "b35ec60e7e06ab42b6fb020f45f07e7c58ca9f3a", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.4.3.tgz", "integrity": "sha512-ZQeAHJIQ7PN01WUWDkNeFMaHytdMrPUx5tjIonEOw+WkxndpBpK1R+oCOZGjfm3RzwzEVWQEs6ATjH8sjgudQQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDKEErq27DfuMvTWRIoZqO/OCMTsBjLFTzl6VIwEXDdUAIhAM3GPYvb50rKoZab0DDMlgo8pcnLw00rNJmphG4wb5uk"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.5.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.5.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^11.0.1", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^5.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "gitHead": "84ec676ec1915d7c5dbafd5a288219ebd55c09f9", "_id": "buffer@3.5.0", "_shasum": "809127f9b4b6e22cfa5bc12857fef12c5b51c5e5", "_from": ".", "_npmVersion": "2.14.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "809127f9b4b6e22cfa5bc12857fef12c5b51c5e5", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.5.0.tgz", "integrity": "sha512-qoL/z+eUom9DTiH2HKU8NKpXJml6NBLqPY/YQuUDED8HD/yrEidP4k0UhbOngf/nwP6CBECN1mKmQ8qq70Nqjg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIET15N0Aay0hWOljbhqa+/9vA9LnhTJZL7S/H3+jaccNAiAsIyUCJ5I7gxwCH8jvNcSmJhaiY8sqyvwyIsrmipHNrQ=="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.5.1": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.5.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^11.0.1", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^5.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "gitHead": "2b3c6a6b3b95716147ceeb2f2f0209ddaa47fd18", "_id": "buffer@3.5.1", "_shasum": "0549d54138f82c0fbef643307e654052ec987fe0", "_from": ".", "_npmVersion": "2.14.4", "_nodeVersion": "4.1.2", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "0549d54138f82c0fbef643307e654052ec987fe0", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.5.1.tgz", "integrity": "sha512-XA9GO3lIzYdlrT8c6wet+ObnrqcCnDpJmgGqc9Lgfz5nyjzEa5YcxO3fVkKvJ3D5XAQiEVD1kI7Ur4snrd4u4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDMXA93YfTYpP8AxavAWtnadIYvc6Ls5cIRHZ05BGH23AiBgodSnVkErY/zgT2GHBNc7Gi3AjQ7rwAGS63FXJ/C0Wg=="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.5.2": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.5.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^11.0.1", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^5.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "gitHead": "d1c1e280da5501ca0aa4e13e0da0af32151c556f", "_id": "buffer@3.5.2", "_shasum": "184a1016a31c2f0628c7ca0f717cea9863a552cc", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "184a1016a31c2f0628c7ca0f717cea9863a552cc", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.5.2.tgz", "integrity": "sha512-e4puLhjPqdwz6m4Yx6++cvKTbJWFON8rgK3xzVqZJCtSY/npmjFDQeCownxK6sepGbD1SEW+wZhlDmy42Dm/6A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDaeirRYZE4Ixhix6MEakMyUCP8x/Gg7erjnUo3y/YGyAiEAwDkWHemcpGL93gpHorjzGC6Fzyfm/ObP2GsjZOXPPak="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.5.3": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.5.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^12.0.1", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^5.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "gitHead": "1c66c1b6e752cc27e281f6b7c8c8ecd6161f9ed3", "_id": "buffer@3.5.3", "_shasum": "38152c7df9ae8275b54a8800ea6dc504696690b4", "_from": ".", "_npmVersion": "3.3.12", "_nodeVersion": "5.1.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "38152c7df9ae8275b54a8800ea6dc504696690b4", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.5.3.tgz", "integrity": "sha512-rMlMhug58zJx44YFDqodhOSBGM7Z9SeAuPE4Q8afv1flf62FWmq+Pe/0yOdZHMoeg/5ACONPCkEaPD+ATw1a6w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDH4hhN82hfbf8TVTrwBIPAmWz9s9R9j3O6GVr+8gpW5gIgIuJDD+W0Djk88luLWG1THAIVrdVbBxS/HulvlF1NP2U="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.5.4": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.5.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "isarray": "^0.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^12.0.1", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^5.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "gitHead": "0355b5c05100562cffc8d560971b0cb08e404417", "_id": "buffer@3.5.4", "_shasum": "dddda17eecf7843e1064f43f5cdb4346528a9d49", "_from": ".", "_npmVersion": "3.5.0", "_nodeVersion": "5.1.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "dddda17eecf7843e1064f43f5cdb4346528a9d49", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.5.4.tgz", "integrity": "sha512-iiWZblj44OV9meBDjtrSgV8ereuV1UthVS4/9cPulchZmaOwYi96IoMUcDxvXN64UwB53Dqgdjt3WUkrmvGhOw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG9+30Hztyb9OT6Wllvk1lM6j5wyLH1YVArElfY8TSjpAiBjrfd/WJfEtZzjSOXEsnY84rulvS5MmPu/Ye43WBq73A=="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.5.5": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.5.5", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "isarray": "^1.0.0"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^12.0.1", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^5.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "gitHead": "d767d6c63ee9f6141ca388dcfe5e7655dd9d1012", "_id": "buffer@3.5.5", "_shasum": "f7c55f7c2c634aa00efaabd864969a44eba82cf4", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.3", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "f7c55f7c2c634aa00efaabd864969a44eba82cf4", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.5.5.tgz", "integrity": "sha512-ZNluPXVlEj+yHTW7eAePCU8CE5BUI4CvObUsEPeXrenTWttB20LBbAb1SrlHI1mHZirvKA19BvW+Tzh+3+tRWg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICDy00BU0gx1n/eATjP74Q7nEd1r4EyMdt7Bg+bhDftPAiAuGa2K8V7YDqwYXg6ZP9ZkWTg6ePj3+Muqps6AOsDyuw=="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "3.6.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.6.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "isarray": "^1.0.0"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^12.0.1", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^5.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "gitHead": "73e77e481f6ebc7a97be87468ddf9e8daf72a93c", "_id": "buffer@3.6.0", "_shasum": "a72c936f77b96bf52f5f7e7b467180628551defb", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.3", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "a72c936f77b96bf52f5f7e7b467180628551defb", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.6.0.tgz", "integrity": "sha512-8EPF2aotiJKZKH/gBXOKDfD0P037yZRRY1oSzhpEUszwlwNR2rLcghiEoJERwKlhd4+ylH5TrSEyLqItPuP0pA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEhkK4TuiVM5u2ENn2EGLLWe35VrkQOG93WodlwXjvlAAiBIzv6snvGdaF1b5fJlcWXGL9wslP0r++GWDoE6LVNyLQ=="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v3.6.2 or newer"}, "4.0.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "4.0.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "isarray": "^1.0.0"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^12.0.1", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^5.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "gitHead": "ada621b6a1c08ea79e30dfe0a9594511b08e6b64", "_id": "buffer@4.0.0", "_shasum": "88db5491021ab319cbdc6faf5d3c720fb887ea74", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.3", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "88db5491021ab319cbdc6faf5d3c720fb887ea74", "tarball": "https://registry.npmjs.org/buffer/-/buffer-4.0.0.tgz", "integrity": "sha512-u2rWG2S8aVkdLuxy3S4RAjxH4tr+1dNBXhiT5/6gA+8Y4pA5XFGSBSXHLU9rIHMpKi0VWVOlj5yNJyTlyVAbpg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCHgQghTcowgoe+1GKseyRqSt035TuXa9wL080L0Xn9yQIgbdnsW54h3HlsZCwQn2mpfJaK/KoWi8Vl5wcnYbkmVBk="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v4.9.2 or newer"}, "4.1.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "4.1.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^12.0.1", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^5.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "typedarray-to-buffer": "^3.0.4", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "gitHead": "a7ce52860bf764a56c5388217194c46356ac2dd9", "_id": "buffer@4.1.0", "_shasum": "272bff7142f4135eda982567fa2d964878e33483", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.3", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "272bff7142f4135eda982567fa2d964878e33483", "tarball": "https://registry.npmjs.org/buffer/-/buffer-4.1.0.tgz", "integrity": "sha512-DiCqXoWolp06MP9cmnSsFf36OUQmet63Gbc8Wt9pBHARYdY4qwvbpS/nUDlGliSUm/NFBibUAc0gOcHuPmb3Ow==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwmJvdMSJb4weP93LKepwEnZehO4KRSUYpbgq5VOBFygIhAKzfYeqT2mJjbb6wUWBKQrU2nbMiQf/RicxojBTL2AsK"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v4.9.2 or newer"}, "4.2.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "4.2.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^12.0.1", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^5.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "typedarray-to-buffer": "^3.0.4", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "gitHead": "bb1691412301f576c697d32e1bdbf0ca348f681e", "_id": "buffer@4.2.0", "_shasum": "599a2f911f6cb2879a8f2ef138de045e89330c0b", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.3", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "599a2f911f6cb2879a8f2ef138de045e89330c0b", "tarball": "https://registry.npmjs.org/buffer/-/buffer-4.2.0.tgz", "integrity": "sha512-rrVSQty1OInZjfMr0NOpTmNqv6Aq9wOYZTWv+jWW67/8f7au8Fz88sgESGxwgnKfnAoik1jgaxVaY5hPk6NXMg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHmGSFCjQnSyoyM61yjin2R7hXfVnJftGcSPFWopAbFgIgHzPhxxdV3sIWrYCZm+HU+JwV25i9YhRc8RdYJ2Rpf8M="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v4.9.2 or newer"}, "4.3.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "4.3.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^12.0.1", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^5.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "typedarray-to-buffer": "^3.0.4", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "gitHead": "e52c33af77a79060c4d423a5447bd77c331ee9c9", "_id": "buffer@4.3.0", "_shasum": "b09b39dbee314233104d7d0cbd6edf928d89e4bd", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.3", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "b09b39dbee314233104d7d0cbd6edf928d89e4bd", "tarball": "https://registry.npmjs.org/buffer/-/buffer-4.3.0.tgz", "integrity": "sha512-hPZf3moWY7qf5gM3kPKrGNyIVgNC0JKH9svFuBPRbppcyo06aZWROn9lbC0I1V2/nH1ObGhOoBqizk3OLGOw5w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDMPnLMmPcPa4tUPVr5hTyPeoiszanwh5/Uj3EJUUgHdAiB84YAC/J9hue99odA+W1trlJrj6g+S0UayLwCqH40yKg=="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v4.9.2 or newer"}, "4.3.1": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "4.3.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^13.0.0", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^5.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "typedarray-to-buffer": "^3.0.4", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "gitHead": "96b30755e433bbd350fad307006bd14091754ad2", "_id": "buffer@4.3.1", "_shasum": "0e65fd01cc3e9154d152f6b3c934b5b8a1b6733c", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "0e65fd01cc3e9154d152f6b3c934b5b8a1b6733c", "tarball": "https://registry.npmjs.org/buffer/-/buffer-4.3.1.tgz", "integrity": "sha512-TYLeYN2F7tmYFF3XJ8/CPzm8OiNkgxQvI9eCi3T6IaKcylIJlcJmmEEnyAE03IXk/SGI1nWtkw7eOZc6oyE+nQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCni8hCUr2DcvwKjApOb7MKlhsosT4/aLB+Y2pkC1J0KQIhAMPKjcUJ8Qq4UFYPkxehNf38QAMt6fa10K5jCmvgNVNF"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v4.9.2 or newer"}, "4.4.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "4.4.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^13.0.0", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^5.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "typedarray-to-buffer": "^3.0.4", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "gitHead": "37800d0e1e15668b6baebabfc61e29e248aa93bf", "_id": "buffer@4.4.0", "_shasum": "cf9b5949fcfe93400cc17035d962843d35410e15", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.6", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "cf9b5949fcfe93400cc17035d962843d35410e15", "tarball": "https://registry.npmjs.org/buffer/-/buffer-4.4.0.tgz", "integrity": "sha512-J<PERSON>+mx8U5k77FA/WDklzcaL/rGLcTK3vxply0kYXUyOzW3r8rvXecIr1vSmXTGfYp0sYIlj2vbxP31AJWaga8Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIErMSu3ZNwXER4tYQo0RJYLdA3mv7MHHHcn2z209UboUAiEAmOeaJFvnC6Hvw2+qHpn4w0QMyCO6DI1VvxVlD1OLIQM="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v4.9.2 or newer"}, "4.5.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "4.5.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^13.0.0", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^6.0.5", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "gitHead": "0c409fc59e038a3b8d988cd5b3ccc7dc72001c5d", "_id": "buffer@4.5.0", "_shasum": "fb5d78719e9c49b30ced84e36d4a622430f84cac", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.3.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "fb5d78719e9c49b30ced84e36d4a622430f84cac", "tarball": "https://registry.npmjs.org/buffer/-/buffer-4.5.0.tgz", "integrity": "sha512-gqNYG2f/kLWAAgjVt7x2YmP+ulitypNOauU4lb+qO4ylNYY+slj/fnvnmSlqgutGHgHa1U6rxud//pPiIP8S8w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDPItldr+lJm36X0LPsdwvNdaoN7zAiHskHkGNSODIpDwIgMyoqxKwM6rF0gssnkUDpMNUNOvh1ZR+pULbZ83gfiTI="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/buffer-4.5.0.tgz_1455608387841_0.41181276459246874"}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v4.9.2 or newer"}, "4.5.1": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "4.5.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^13.0.0", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^6.0.5", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "gitHead": "b9781a9434aaaa3d3099e7281c15a6a5f51c9b51", "_id": "buffer@4.5.1", "_shasum": "237b5bdef693c4c332385c1ded4ef4646e232d73", "_from": ".", "_npmVersion": "2.14.12", "_nodeVersion": "4.3.2", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "237b5bdef693c4c332385c1ded4ef4646e232d73", "tarball": "https://registry.npmjs.org/buffer/-/buffer-4.5.1.tgz", "integrity": "sha512-imXpnaHIKRymvk9QIeHTsYodjQRPRWqSNOTQ7UeLbRQmOsjX4vUwgd4AKsqvfSRFEBxFxjwyTw/VJEJiuUqjaQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDTfmiSE5AjD9IA5vQEYRWw9023091S9uURQcjRRo72WQIhAKHSyKC1VjhQEQaJptaiqr8ShMd3TTcOXZTZaPm4WvAK"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-13-west.internal.npmjs.com", "tmp": "tmp/buffer-4.5.1.tgz_1458788498069_0.22429057699628174"}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v4.9.2 or newer"}, "4.6.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "4.6.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^13.0.0", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^6.0.5", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser-es5": "zuul --ui tape -- test/*.js", "test-browser-es5-local": "zuul --ui tape --local -- test/*.js", "test-browser-es6": "zuul --ui tape -- test/*.js test/node/*.js", "test-browser-es6-local": "zuul --ui tape --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js && OBJECT_IMPL=true tape test/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/_polyfill.js", "perf/*.js"]}, "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "gitHead": "caf10c7b2f209d8ed3df7fe32e475be3d6819625", "_id": "buffer@4.6.0", "_shasum": "fe50a7de503ebaad1b568d05967207be4024c348", "_from": ".", "_npmVersion": "3.8.3", "_nodeVersion": "5.10.1", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "fe50a7de503ebaad1b568d05967207be4024c348", "tarball": "https://registry.npmjs.org/buffer/-/buffer-4.6.0.tgz", "integrity": "sha512-5CZobukY8PAKXhbU6se7zTGyCt065ZePdMX3aY5X989LjSKTIlAgnGjsP6DpmbjxYOk1m6Ag07x5js3Z0Va6uQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICAPgVYNy9NU1oUKaAMOfYDu3wAcby1XC+CzOLYwMInaAiAqTsbHTzfcBzii+SgJL1/+U3pxb70OBzPAw6SiW6Egag=="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/buffer-4.6.0.tgz_1461134201214_0.012851420091465116"}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v4.9.2 or newer"}, "4.7.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "4.7.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^13.0.0", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^7.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser-es5": "zuul --ui tape -- test/*.js", "test-browser-es5-local": "zuul --ui tape --local -- test/*.js", "test-browser-es6": "zuul --ui tape -- test/*.js test/node/*.js", "test-browser-es6-local": "zuul --ui tape --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js && OBJECT_IMPL=true tape test/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/_polyfill.js", "perf/*.js"]}, "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "gitHead": "a1cab29b7aed04b221622efec2f6bf47f989314f", "_id": "buffer@4.7.0", "_shasum": "f32ca787cbaac88a62b230f7040ee431655c71f3", "_from": ".", "_npmVersion": "3.8.9", "_nodeVersion": "6.2.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "f32ca787cbaac88a62b230f7040ee431655c71f3", "tarball": "https://registry.npmjs.org/buffer/-/buffer-4.7.0.tgz", "integrity": "sha512-4nCji0nHH1gmo6GYIqV7sw8gOrT+1d1PnSLIr/PimN/e65nOAncbv7rPhebTlhxPxtSwPbyiShTfD/zXV4PnkQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE12dsIbBk00m1t8vmkmdcfWkI5XyKtiUxFvq/M6OeV4AiEA3bkLkKKekLKWVpDDDXanjlQnmnBE0FYljP/6rtT1+U8="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/buffer-4.7.0.tgz_1466732113659_0.4496456526685506"}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v4.9.2 or newer"}, "4.7.1": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "4.7.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^13.0.0", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^7.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser-es5": "zuul --ui tape -- test/*.js", "test-browser-es5-local": "zuul --ui tape --local -- test/*.js", "test-browser-es6": "zuul --ui tape -- test/*.js test/node/*.js", "test-browser-es6-local": "zuul --ui tape --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js && OBJECT_IMPL=true tape test/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/_polyfill.js", "perf/*.js"]}, "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "gitHead": "1aa54804c7cad3df0418ddd7cbdb3c4d37dd07f3", "_id": "buffer@4.7.1", "_shasum": "6e5235437edb46ea2d4596d6396116b1548bca60", "_from": ".", "_npmVersion": "3.8.9", "_nodeVersion": "6.2.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "6e5235437edb46ea2d4596d6396116b1548bca60", "tarball": "https://registry.npmjs.org/buffer/-/buffer-4.7.1.tgz", "integrity": "sha512-P+bgsCGp+SOWGy7A7ZrKUB8LIf+rCgSKZBpArgwifl6M+61ENZEgQGJcM5ZeWAcEWVnQeQ9yEuIPlHw7Yd48pQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB0Vs56wRyIEYwI7laVUVv3q89qTj/UDsgrbXVSBQFU5AiEA2fOy7fpX46fAWZV2SOd5qsnJ3d17dfkhhcu81eqcC+U="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/buffer-4.7.1.tgz_1468566371042_0.9159472172614187"}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v4.9.2 or newer"}, "4.8.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "4.8.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^13.0.0", "concat-stream": "^1.4.7", "hyperquest": "^2.0.0", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^7.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser-es5": "zuul --ui tape -- test/*.js", "test-browser-es5-local": "zuul --ui tape --local -- test/*.js", "test-browser-es6": "zuul --ui tape -- test/*.js test/node/*.js", "test-browser-es6-local": "zuul --ui tape --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js && OBJECT_IMPL=true tape test/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/_polyfill.js", "perf/*.js"]}, "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "gitHead": "02b0eb31459b245e4ac5cfbd837e902796b1a425", "_id": "buffer@4.8.0", "_shasum": "d6e5022de9ee6c4af67767eece1e7993599b009f", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.1", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "d6e5022de9ee6c4af67767eece1e7993599b009f", "tarball": "https://registry.npmjs.org/buffer/-/buffer-4.8.0.tgz", "integrity": "sha512-SoStgWwmr+FHj8rOWlWQEaFfH+5evHdjvZjpyYk26ZVM7kiUPhTxp72hNifKHFXN4AXQ3uUhKpQTH+sWazdR/g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF8nv2A50OBV2kREyE5l1gjNlgtL2JmZiE84mu4Jr0waAiAYYhRGo21sjX2XooP8FKLEiog7/LiiCqh0LwZ/a419BQ=="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/buffer-4.8.0.tgz_1470644936861_0.3429739410057664"}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v4.9.2 or newer"}, "4.9.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "4.9.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^13.0.0", "concat-stream": "^1.4.7", "hyperquest": "^2.0.0", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^7.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser-es5": "zuul --ui tape -- test/*.js", "test-browser-es5-local": "zuul --ui tape --local -- test/*.js", "test-browser-es6": "zuul --ui tape -- test/*.js test/node/*.js", "test-browser-es6-local": "zuul --ui tape --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js && OBJECT_IMPL=true tape test/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/_polyfill.js", "perf/*.js"]}, "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "gitHead": "38fb25bc04e3cfd7d1e86325d899f19610eb96d5", "_id": "buffer@4.9.0", "_shasum": "f114fd8db10a51549964b88499ec2727ecc66f19", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.1", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "f114fd8db10a51549964b88499ec2727ecc66f19", "tarball": "https://registry.npmjs.org/buffer/-/buffer-4.9.0.tgz", "integrity": "sha512-Z3YIy+907y+Nvatroy4xFnWW61rAmdF7C85PeNHmXRn43Vl/pUWOizGMHrdyye0BBxDIlQLlRJy/qlIHOCVL9Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHQ+YDw/a+T6gEo/WfQqYjpWj/OOCG+AbXIbcHeIA6wVAiEAkh5UKLSGS1B642cG6OxiFuzX6VOraziAlxrPldzSheA="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/buffer-4.9.0.tgz_1470646910012_0.8597736358642578"}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v4.9.2 or newer"}, "4.9.1": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "4.9.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^13.0.0", "concat-stream": "^1.4.7", "hyperquest": "^2.0.0", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^7.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "zuul --ui tape -- test/*.js", "test-browser-es5-local": "zuul --ui tape --local -- test/*.js", "test-browser-es6": "zuul --ui tape -- test/*.js test/node/*.js", "test-browser-es6-local": "zuul --ui tape --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js && OBJECT_IMPL=true tape test/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/*.js", "test/_polyfill.js", "perf/*.js"]}, "gitHead": "2152e6ac4f8b47dc46eba44e07fad7c9d3e30563", "_id": "buffer@4.9.1", "_shasum": "6d1bb601b07a4efced97094132093027c95bc298", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.4.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "6d1bb601b07a4efced97094132093027c95bc298", "tarball": "https://registry.npmjs.org/buffer/-/buffer-4.9.1.tgz", "integrity": "sha512-DNK4ruAqtyHaN8Zne7PkBTO+dD1Lr0YfTduMqlIyjvQIoztBkUxrvL+hKeLW8NXFKHOq/2upkxuoS9znQ9bW9A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDcN0Tuzt2e98/9Gmn5jqWRGQ4pIix2eHV3ikaY3S2a0wIgYOtSQ+FRBx6D345Pjhgwuu9grwSBl6S0F8wYYDQzDxY="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/buffer-4.9.1.tgz_1471491999032_0.9881124331150204"}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v4.9.2 or newer"}, "5.0.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "5.0.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^13.0.0", "concat-stream": "^1.4.7", "hyperquest": "^2.0.0", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "*", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "zuul --ui tape -- test/*.js", "test-browser-es5-local": "zuul --ui tape --local -- test/*.js", "test-browser-es6": "zuul --ui tape -- test/*.js test/node/*.js", "test-browser-es6-local": "zuul --ui tape --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/_polyfill.js", "perf/**/*.js"]}, "gitHead": "617ce169cf44b8ec1071dd4ef67a1fef669c3bdf", "_id": "buffer@5.0.0", "_shasum": "a65f428104a402563108d06b9c85a2f0c9713652", "_from": ".", "_npmVersion": "3.10.3", "_nodeVersion": "6.5.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "a65f428104a402563108d06b9c85a2f0c9713652", "tarball": "https://registry.npmjs.org/buffer/-/buffer-5.0.0.tgz", "integrity": "sha512-O5IN69Sg8UEiP7qhgScAf34wb5YjVCaEHts2jCmDS3eetLu0/SMG0BVSdygb0CnbZ7xmtCks2xIeeHghhbVz0A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBTo16whjYfspSM9e3+s+rl/ikpCZ9aYRKPPCBNKZxoEAiBXTT1bckdZPPrrR1Mzi2tDZLxfnXVrL5XFJbM7AK21sg=="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/buffer-5.0.0.tgz_1474943916676_0.4056296891067177"}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v5.0.8 or newer"}, "5.0.1": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "5.0.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^13.0.0", "concat-stream": "^1.4.7", "hyperquest": "^2.0.0", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "*", "tape": "^4.0.0", "through2": "^2.0.0", "uglify-js": "^2.7.3", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "zuul --ui tape -- test/*.js", "test-browser-es5-local": "zuul --ui tape --local -- test/*.js", "test-browser-es6": "zuul --ui tape -- test/*.js test/node/*.js", "test-browser-es6-local": "zuul --ui tape --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/_polyfill.js", "perf/**/*.js"]}, "gitHead": "83a5575f05f332b438c545091036542b717a1888", "_id": "buffer@5.0.1", "_shasum": "28165188f46d451b516b8be3e611b00029573486", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "7.0.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "28165188f46d451b516b8be3e611b00029573486", "tarball": "https://registry.npmjs.org/buffer/-/buffer-5.0.1.tgz", "integrity": "sha512-A9o97/FBSOouY3bZ2MxBoGnLNBTQmgWmInlucdXgH9ULH9tzqAK2hG/H5I3Y45vc/4RbIIBu5gmh3PughawIAg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHIxcJFQQQ5AQqwYwZ3l3h8rAu3RXwpxC0jBt2s4VLXwIgeKS0M5Re+sjnAY3XyhFm+zNcKOFiBINJpCHn+Pn4U9Y="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/buffer-5.0.1.tgz_1478506442847_0.6941425669938326"}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v5.0.8 or newer"}, "5.0.2": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "5.0.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^13.0.0", "concat-stream": "^1.4.7", "hyperquest": "^2.0.0", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "*", "tape": "^4.0.0", "through2": "^2.0.0", "uglify-js": "^2.7.3", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "zuul --ui tape -- test/*.js", "test-browser-es5-local": "zuul --ui tape --local -- test/*.js", "test-browser-es6": "zuul --ui tape -- test/*.js test/node/*.js", "test-browser-es6-local": "zuul --ui tape --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/_polyfill.js", "perf/**/*.js"]}, "gitHead": "d14a864f68f80ff1c6b591929da5d03d81af7cc5", "_id": "buffer@5.0.2", "_shasum": "41d0407ff76782e9ec19f52f88e237ce6bb0de6d", "_from": ".", "_npmVersion": "3.10.8", "_nodeVersion": "6.9.1", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "41d0407ff76782e9ec19f52f88e237ce6bb0de6d", "tarball": "https://registry.npmjs.org/buffer/-/buffer-5.0.2.tgz", "integrity": "sha512-fkQlxf79RLFVKjah0loMHrtd1rUBQkTHjXht5rI/+gfEIHCVd5hrG5GG0J6tw53nXAu4H4uRmEZcIbj8AKVCVg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEg29c27auYMvOR44NE+PqNgXdCBST9y1ELNzVrEdDfvAiBpiszSaZctBNFVzPIMeuY+iQwkavmU5G1dkeimO1pu0Q=="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/buffer-5.0.2.tgz_1480720208927_0.706338755087927"}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v5.0.8 or newer"}, "5.0.3": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "5.0.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^14.0.0", "concat-stream": "^1.4.7", "hyperquest": "^2.0.0", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "*", "tape": "^4.0.0", "through2": "^2.0.0", "uglify-js": "^2.7.3", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "zuul --ui tape -- test/*.js", "test-browser-es5-local": "zuul --ui tape --local -- test/*.js", "test-browser-es6": "zuul --ui tape -- test/*.js test/node/*.js", "test-browser-es6-local": "zuul --ui tape --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/_polyfill.js", "perf/**/*.js"]}, "gitHead": "de56df89422ad1e9c3bebbd86b9fec21b27427b2", "_id": "buffer@5.0.3", "_shasum": "90d5b2dbcef4004e7e307d0e488595a302e1f8fd", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.2", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "90d5b2dbcef4004e7e307d0e488595a302e1f8fd", "tarball": "https://registry.npmjs.org/buffer/-/buffer-5.0.3.tgz", "integrity": "sha512-Pw6U2Mj3bODBGi/eKuQuTcufIZ2LcVfbrZZq+eypjyG9UI8gWQfH7pLKA4xUfuy7wT6MpniBYjQdNsqglbWGfA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCsNEYvpbNVEa93L0nGeArv9KqWxKhYIuJlQpnzvdbGHwIgUXaB+QgWovwcLlkQ4224/e03grMnnypnjJ7t2RPsaus="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/buffer-5.0.3.tgz_1486071661746_0.7777809230610728"}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v5.0.8 or newer"}, "5.0.4": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "5.0.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^14.0.0", "concat-stream": "^1.4.7", "hyperquest": "^2.0.0", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "*", "tape": "^4.0.0", "through2": "^2.0.0", "uglify-js": "^2.7.3", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "zuul --ui tape -- test/*.js", "test-browser-es5-local": "zuul --ui tape --local -- test/*.js", "test-browser-es6": "zuul --ui tape -- test/*.js test/node/*.js", "test-browser-es6-local": "zuul --ui tape --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/_polyfill.js", "perf/**/*.js"]}, "gitHead": "051039c1e0d08476bab10f6b5d62adec229f6971", "_id": "buffer@5.0.4", "_shasum": "d76fee8f6dbe7c112d6312e492ed9979127b34dd", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.5.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "d76fee8f6dbe7c112d6312e492ed9979127b34dd", "tarball": "https://registry.npmjs.org/buffer/-/buffer-5.0.4.tgz", "integrity": "sha512-/TP7OakmmulZ0RfafXnfpgr41thsCQ3sJoDyU7JWqdIytRs4nTe8HWz5X58J9IoqCmYmAAEhwMNsfJoGBqL3bg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB/Srzzr7H+BStXFzmaoQBN4vIccvwDz7YEkjuWzUEoxAiEA8DgAbbT0xX75s9kUMIR1mKKUjCBt6c5b4Z6QO5OldOE="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/buffer-5.0.4.tgz_1486617250999_0.9144826866686344"}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v5.0.8 or newer"}, "5.0.5": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "5.0.5", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^14.0.0", "concat-stream": "^1.4.7", "hyperquest": "^2.0.0", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "*", "tape": "^4.0.0", "through2": "^2.0.0", "uglify-js": "^2.7.3", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "zuul --ui tape -- test/*.js", "test-browser-es5-local": "zuul --ui tape --local -- test/*.js", "test-browser-es6": "zuul --ui tape -- test/*.js test/node/*.js", "test-browser-es6-local": "zuul --ui tape --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/_polyfill.js", "perf/**/*.js"]}, "gitHead": "706a59cbb6bf2e7de701aefba437951ec6c0fb2a", "_id": "buffer@5.0.5", "_shasum": "35c9393244a90aff83581063d16f0882cecc9418", "_from": ".", "_npmVersion": "4.1.2", "_nodeVersion": "7.5.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "35c9393244a90aff83581063d16f0882cecc9418", "tarball": "https://registry.npmjs.org/buffer/-/buffer-5.0.5.tgz", "integrity": "sha512-ye69HVxM0Htf+E5YhFLOfHX8dXOkammTDtqCU5xLFMlPYTQkNJ7Kv6I90TjZiPH1yXBHQdRvSAK28EZSwSm3Dg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDc4VfR/sAAbTZLWfh9w/gElWtBBU1SFe1uz/gWKsKeggIhALlShx3bG/CtpHJurrnhrq3rSCUep+Wxsz3L8mCeymVl"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/buffer-5.0.5.tgz_1486678297324_0.04846473457291722"}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v5.0.8 or newer"}, "5.0.6": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "5.0.6", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^14.0.0", "concat-stream": "^1.4.7", "hyperquest": "^2.0.0", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "*", "tape": "^4.0.0", "through2": "^2.0.0", "uglify-js": "^2.7.3", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "zuul --ui tape -- test/*.js", "test-browser-es5-local": "zuul --ui tape --local -- test/*.js", "test-browser-es6": "zuul --ui tape -- test/*.js test/node/*.js", "test-browser-es6-local": "zuul --ui tape --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/_polyfill.js", "perf/**/*.js"]}, "gitHead": "cf0c27e9bc645fdcfeefaac9f8da0172475a5277", "_id": "buffer@5.0.6", "_shasum": "2ea669f7eec0b6eda05b08f8b5ff661b28573588", "_from": ".", "_npmVersion": "4.4.4", "_nodeVersion": "7.8.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"shasum": "2ea669f7eec0b6eda05b08f8b5ff661b28573588", "tarball": "https://registry.npmjs.org/buffer/-/buffer-5.0.6.tgz", "integrity": "sha512-wKYKDa5TnAgkN7hPSs4e3f4Swfif8Iz8a/CwdPk3bgAyPjxnTo9wxv+nh4Yp6mVufEykIgiG7aKhBWsatTBDvQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICStra4XwAs50wgVvVpo9YJTZMh0p54TlCwMYNgnB6GbAiEAn8t2RC2/gxuDAJEllgvCeCR/yiHgSaWtBUBEiWlDZMc="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/buffer-5.0.6.tgz_1491419123937_0.48741885321214795"}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v5.0.8 or newer"}, "5.0.7": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "5.0.7", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^14.0.0", "concat-stream": "^1.4.7", "hyperquest": "^2.0.0", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "*", "tape": "^4.0.0", "through2": "^2.0.0", "uglify-js": "^2.7.3", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "zuul --ui tape -- test/*.js", "test-browser-es5-local": "zuul --ui tape --local -- test/*.js", "test-browser-es6": "zuul --ui tape -- test/*.js test/node/*.js", "test-browser-es6-local": "zuul --ui tape --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/_polyfill.js", "perf/**/*.js"]}, "gitHead": "a50b8e35991255b21dbad23a189fd32d66b6868c", "_id": "buffer@5.0.7", "_npmVersion": "5.3.0", "_nodeVersion": "8.1.3", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-NeeHXWh5pCbPQCt2/6rLvXqapZfVsqw/YgRgaHpT3H9Uzgs+S0lSg5SQzouIuDvcmlQRqBe8hOO2scKCu3cxrg==", "shasum": "570a290b625cf2603290c1149223d27ccf04db97", "tarball": "https://registry.npmjs.org/buffer/-/buffer-5.0.7.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG9NfqWMJyVLgIaRVRacEo6Ni8qiOFu+K7k/f0+9jAq0AiAfBi1X59nSS1Su0jKiDEJuZspP1g1OytqNBhvKTzk63Q=="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer-5.0.7.tgz_1501897534750_0.6458459889981896"}, "directories": {}, "deprecated": "This version of 'buffer' is out-of-date. You must update to v5.0.8 or newer"}, "5.0.8": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "5.0.8", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^14.0.0", "concat-stream": "^1.4.7", "hyperquest": "^2.0.0", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "*", "tape": "^4.0.0", "through2": "^2.0.0", "uglify-js": "^2.7.3", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "zuul --ui tape -- test/*.js", "test-browser-es5-local": "zuul --ui tape --local -- test/*.js", "test-browser-es6": "zuul --ui tape -- test/*.js test/node/*.js", "test-browser-es6-local": "zuul --ui tape --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/_polyfill.js", "perf/**/*.js"]}, "gitHead": "8bba72251eeb636cfcc9a7d3d3fe690f205bf4a8", "_id": "buffer@5.0.8", "_npmVersion": "5.3.0", "_nodeVersion": "8.6.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-xXvjQhVNz50v2nPeoOsNqWCLGfiv4ji/gXZM28jnVwdLJxH4mFyqgqCKfaK9zf1KUbG6zTkjLOy7ou+jSMarGA==", "shasum": "84daa52e7cf2fa8ce4195bc5cf0f7809e0930b24", "tarball": "https://registry.npmjs.org/buffer/-/buffer-5.0.8.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDWIgBQdaAEtpXlDNPU0/LsH+T4YwKucnF55wYBUpgDXQIhANJa4nRTQnQBeJMY7Bu0jet8ojjHaZx2C9FYgp20Dzd9"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer-5.0.8.tgz_1506728927930_0.79850033367984"}, "directories": {}}, "5.1.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "5.1.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^14.0.0", "concat-stream": "^1.4.7", "hyperquest": "^2.0.0", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "*", "tape": "^4.0.0", "through2": "^2.0.0", "uglify-js": "^2.7.3", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "zuul --ui tape -- test/*.js", "test-browser-es5-local": "zuul --ui tape --local -- test/*.js", "test-browser-es6": "zuul --ui tape -- test/*.js test/node/*.js", "test-browser-es6-local": "zuul --ui tape --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/common.js", "test/_polyfill.js", "perf/**/*.js"]}, "gitHead": "9c74805b314ed7824fa4f585a583be3626ae157c", "_id": "buffer@5.1.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-YkIRgwsZwJWTnyQrsBTWefizHh+8GYj3kbL1BTiAQ/9pwpino0G7B2gp5tx/FUBqUlvtxV85KNR3mwfAtv15Yw==", "shasum": "c913e43678c7cb7c8bd16afbcddb6c5505e8f9fe", "tarball": "https://registry.npmjs.org/buffer/-/buffer-5.1.0.tgz", "fileCount": 39, "unpackedSize": 244377, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCaWBbsIrGQAnaiATRKMHEsWlbXwc7EwakhYxZZs8rlUAIhAPGcTU6sJAV966iPsGzfQYrdEZVM/gzkf8z2clscRp7W"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer_5.1.0_1518765399884_0.7736473989237913"}, "_hasShrinkwrap": false}, "5.2.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "5.2.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}, "devDependencies": {"airtap": "0.1.0", "benchmark": "^2.0.0", "browserify": "^16.1.0", "concat-stream": "^1.4.7", "hyperquest": "^2.0.0", "is-buffer": "^2.0.0", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "*", "tape": "^4.0.0", "through2": "^2.0.0", "uglify-js": "^3.3.12"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "airtap -- test/*.js", "test-browser-es5-local": "airtap --local -- test/*.js", "test-browser-es6": "airtap -- test/*.js test/node/*.js", "test-browser-es6-local": "airtap --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/common.js", "test/_polyfill.js", "perf/**/*.js"]}, "gitHead": "e55ff09a8ce79348d52b3e06812dcc025982bcd0", "_id": "buffer@5.2.0", "_npmVersion": "6.2.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-nUJyfChH7PMJy75eRDCCKtszSEFokUNXC1hNVSe+o+VdcgvDPLs20k3v8UXI8ruRYAJiYtyRea8mYyqPxoHWDw==", "shasum": "53cf98241100099e9eeae20ee6d51d21b16e541e", "tarball": "https://registry.npmjs.org/buffer/-/buffer-5.2.0.tgz", "fileCount": 6, "unpackedSize": 80664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbWsbmCRA9TVsSAnZWagAAickQAJjmubcAFTynFv1CowJJ\ndbANxMnhp0u9Nb4O7v+s53gXJMXophzhREJhVVhXDJQaLDnPjISydxNh4jqF\nDezSPr0J8SgeF45lfNh2ofEd8w4/UsiRSJYy0Vs9+b3RIUpzFQw6YU3Hyslr\nq4A8eM3q35CrLrIaFjvIAsIo0PHLhGbt1NNhGYdFV/hSC8dbw8WsUX1F7cfJ\n7T2JakP7OnQ9XtJNK9UXsyU+dMhVuobK3e6MhJYX1F8kmRbQc4TlLYRL8Xr5\nkoQl827DW8Pg6LiAbFTDtebHvt5N6gNh6uCwlSbD2xXAuBTInEvFesX9xv3v\n9rvry+0mYPK6QNhyycwWgzSfIs6BrpkLiAATDh2ynvFo1CfJa1U+cGV957WY\nmLM7GiNgXueyvTXP7rjqS+wYDd3sUlnNN5/4j/weFKAGvGZNArpXipIxESxR\nolhK4gBtkrDborY0LCfWRkCkKuvU89YVcsj4ge3KjxUuA6xOXmYvl7LWXv5P\nhL4jE9AXwJvnPgTX8rq7ZpIq2AgpxYnukda2LdxhIKjHjzOWId6aZkCJ+Epg\n6uUxG5YIJ8hf8yv7hcKdFNsuSCOjfRbH2dJi5GsyXjAyliMYb5g9I5/GN0ex\n8jlZmPUshNseErCdIUcXGf/0k83AdoqlmaNDleGFN8iTyyCG0y1RwEaKc5bj\nJ8O9\r\n=EuCg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD//8rFrhkRaUaZmLOPLvS4mLrJ06DR7Sv76V/KHLdmoQIhANz5JSmZXJH1eVeie7T0Ure+lQQfGaXJmcAvHTE04t8g"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer_5.2.0_1532675814278_0.7465353563167112"}, "_hasShrinkwrap": false}, "5.2.1": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "5.2.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}, "devDependencies": {"airtap": "0.1.0", "benchmark": "^2.0.0", "browserify": "^16.1.0", "concat-stream": "^1.4.7", "hyperquest": "^2.0.0", "is-buffer": "^2.0.0", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "*", "tape": "^4.0.0", "through2": "^2.0.0", "uglify-js": "^3.4.5"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "airtap -- test/*.js", "test-browser-es5-local": "airtap --local -- test/*.js", "test-browser-es6": "airtap -- test/*.js test/node/*.js", "test-browser-es6-local": "airtap --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/common.js", "test/_polyfill.js", "perf/**/*.js"]}, "gitHead": "c8e3dc7af4e9367bcb14efd3d95c0bf467a67bee", "_id": "buffer@5.2.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.9.0", "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-c+Ko0loDaFfuPWiL02ls9Xd3GO3cPVmUobQ6t3rXNUk304u6hGq+8N/kFi+QEIKhzK3uwolVhLzszmfLmMLnqg==", "shasum": "dd57fa0f109ac59c602479044dca7b8b3d0b71d6", "tarball": "https://registry.npmjs.org/buffer/-/buffer-5.2.1.tgz", "fileCount": 6, "unpackedSize": 79940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbiGVzCRA9TVsSAnZWagAApaIP/Rb4pP0x+Xzpm6ZbyG99\nzIOkTnEol19cZnAiJzDhN1bSZ0IgsORBWIpILvKj8RzL7EPRacZ1zJOAKBY8\nXKqFaS1JvdOa9O0T2pbPWSdaPu3PCLTay3AZTWN7Bjl6i4hbQ/EFi/VHcZU/\nEhXqaaqwZUmX2+5ouxpZxLveYD5vLJwvb5Vz7aQ11gyN6dkVh4jx20gAbuWI\n+gF44ry3efHIreDDGWP9+i9d6dsFQXX+GtkepOQYa/gdBmJSuOKy5PM8ukFR\nvplR11Q2LvjpA7Dv7IbP5XlCfmzdcZKvTFrDnjZqSnSAB6/OnFGKOe1/nm1s\nXvRbduy3sRZ0Pxpo+eZojl/Z06wqYgi/ri4qQTtErfTMKAVTy3HrsusK0SHI\n0/ymBiHVt6j9JJD3kK9APx6bUmqjkQlHB1TPdXSUefyyWYPsdyD17D1rhrfD\ne+sQxdTS4p4R7hh1exudk6XVlZt7CuH3NZnDMK2VqjKFFerOMjzDRn5y2upg\nIWoqrblFHoP1NZO3QMq7sRn7JfIg5/bJfZhZhbhosAo98a/vc3a/6Mm/amus\nvexzOvdlDrw4fWWUNW5ESGZ32Ub8B+lZVzuv4XX1N6W4n5LanGO/QcR76WRB\noD8NfTj7QrvnInk3DttX+MtWdQW1eUaa9gBBnUqIoULMo+hraw1AbRhBJMBe\nZKQs\r\n=nd43\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQComWWLjSFdR1dQIxuWh2hPc8A6i8+3Ao613P7BEtmViwIhAMnOOSHPm4tNnPFK7rXpkcerKXyp3QpWg4PLE73hc9xZ"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer_5.2.1_1535665522800_0.02050128191672096"}, "_hasShrinkwrap": false}, "5.3.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "5.3.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}, "devDependencies": {"airtap": "^2.0.3", "benchmark": "^2.0.0", "browserify": "^16.1.0", "concat-stream": "^2.0.0", "hyperquest": "^2.0.0", "is-buffer": "^2.0.0", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "*", "tape": "^4.0.0", "through2": "^3.0.1", "uglify-js": "^3.4.5"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "airtap -- test/*.js", "test-browser-es5-local": "airtap --local -- test/*.js", "test-browser-es6": "airtap -- test/*.js test/node/*.js", "test-browser-es6-local": "airtap --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/common.js", "test/_polyfill.js", "perf/**/*.js"]}, "gitHead": "c70a8489ac825f6f2872ea2d08f44081f04526e9", "_id": "buffer@5.3.0", "_nodeVersion": "10.16.0", "_npmVersion": "6.10.2", "dist": {"integrity": "sha512-XykNc84nIOC32vZ9euOKbmGAP69JUkXDtBQfLq88c8/6J/gZi/t14A+l/p/9EM2TcT5xNC1MKPCrvO3LVUpVPw==", "shasum": "5f9fa5fefe3939888d0fdbf7d964e2a8531fd69c", "tarball": "https://registry.npmjs.org/buffer/-/buffer-5.3.0.tgz", "fileCount": 6, "unpackedSize": 80315, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdUMt0CRA9TVsSAnZWagAAxDIP/RqzE+RhusHDB3D3nlPm\nfAdOWACfvhxguBHFqbp9LrfAhDSnDA4yZVI0p6CEFutDEkNeeHlvsqMK3/oh\npm45eAj9YszKPmTH4nx3UzdoG1xrzE2ztIC3MOOSXaL/hVyZO3kSATG+8K0b\nMlzb1rN1mBjxE+HlCRkdKfCsQpL3wrKbmCminW0b8T+2/9fiOByt9nQQtfhC\nMLgMowxQ6fpuSa95whegwpfMH3SKrT8yMY69kPoQsWWfU1Ft9jtefLo3I8lP\nsMq+Zs7m/lM0zHF0ipr8Q8Rc3UrXYNA2kVxwn13D20DnVUXSWFtEfcAADXw/\nmzeS+WYh3DOP4TyCvaKBSGSHotpa9+xUJPpjpIpFFMpR6P4hNTgHrObUsoCZ\ngdmf+aO6W+kkzRrBIXBFN8GXb8hJAd8iObI6tz73d/QWxrDpkHh3Gcvugvla\nGosrP+n6mLazzDNw3Wq/IGQg2zQ+/Mcf8QKrq1CKTcA67u8aSunrLf5RoIOA\nC5f5k80RJRmAGFKYS7TG/r7qyiMtvsORREXGTfOchE+5wXYoTBp0dTb9KMZB\niHJXb9saXR8e3Eku444V3kQIsYyPYlKW0jdM9BjkZCeGolFGZ0t0QOStAahW\ntKWuXisB1CeOEiZqg+hmKuDcG0z5FwtzcnGEdzPKXjiJUwhG/mYYdTQiqBNN\n1lBL\r\n=Ar2H\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA6J5kjMqcpsWUq5HDqacma8zbXUa5JbBYC1xcCxxeAvAiBPNdiF4jjHU/R5Yp27D9at0nCuXiKuytjCIwtRH85Sng=="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer_5.3.0_1565576051937_0.9965722667003907"}, "_hasShrinkwrap": false}, "5.4.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "5.4.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}, "devDependencies": {"airtap": "^2.0.3", "benchmark": "^2.0.0", "browserify": "^16.1.0", "concat-stream": "^2.0.0", "hyperquest": "^2.0.0", "is-buffer": "^2.0.0", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "*", "tape": "^4.0.0", "through2": "^3.0.1", "uglify-js": "^3.4.5"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "airtap -- test/*.js", "test-browser-es5-local": "airtap --local -- test/*.js", "test-browser-es6": "airtap -- test/*.js test/node/*.js", "test-browser-es6-local": "airtap --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/common.js", "test/_polyfill.js", "perf/**/*.js"]}, "gitHead": "fd0246ea00d318f3640ea782bfb499cc1eefae71", "_id": "buffer@5.4.0", "_nodeVersion": "10.16.0", "_npmVersion": "6.10.2", "dist": {"integrity": "sha512-Xpgy0IwHK2N01ncykXTy6FpCWuM+CJSHoPVBLyNqyrWxsedpLvwsYUhf0ME3WRFNUhos0dMamz9cOS/xRDtU5g==", "shasum": "33294f5c1f26e08461e528b69fa06de3c45cbd8c", "tarball": "https://registry.npmjs.org/buffer/-/buffer-5.4.0.tgz", "fileCount": 6, "unpackedSize": 80550, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdUxxgCRA9TVsSAnZWagAA9MkP/jYowna1Q7Cn58n4qY5O\nyUPWMY/YyxJjKC5AYMepwjiuMOjIvB0oWuONvlYLRapgam5qg7GTUy6Jctwp\ncxnNZH1rYxmUziF+YAT75GdSszbNWSXqF5v/+X19gogs0ijLEGpX8wiWvzHJ\nxsHd0FYj7zVZQHeM8CNrp+bUH9rAqqg/v76pgO7M5wMelJjri3ZQxCHNQL0W\n1aJkMNGm/0om9SfI16gKdUMXyORYfeUP5EaU+39Fi07su8kMufQk3Y7+dRlh\nP5rqNBFs/Y2BtZz2CQqSzAjanJNdqVWA9KM/AtZCvExQUV4jhg8IMCWJ3sVK\nXqTG8kBu9h9ar9Z1GFRQOX0gSnXVvxqTPcwDJOM4azNHOeWdO7ToJws1QLb/\nc5x6dbhwoqsDv+bsnCvt5KqHqvZ7CUeP15hrQUIpjJVG4qne7OXSs1Le0uiB\nGo3wKlTHQzSiuuTOxc3pP5t2nd5wpiHp7v+1DlmVgO8GzYWbFak3EDJp4YyP\npq0E/VbBUuyJLoM72SMbfYKzXM/2x2660xpkzThiqZbwVY/Ivx4QlxDQG+pf\nmq8XPhSycZj9InI9zWzTrIMKVBidBSHuWRLqoPnnLKFOTQxsb7/ordaqTAme\nZJ/ylwEpDksAlv5FVTptvdQ25UNjFOyqcpbdqZYPWZeNJe6but/Ke3xo4zWM\nRARj\r\n=Ba0Y\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDlOILroy0oqg83PeF1olALoqJ/hN97Sit2EK7cpWOknAIhAOW5u8QNNXfgBo3XHr8UCUpgwMfnGzuX2Bo4B7N7TOEp"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer_5.4.0_1565727839847_0.992790683292089"}, "_hasShrinkwrap": false}, "5.4.1": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "5.4.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}, "devDependencies": {"airtap": "^2.0.3", "benchmark": "^2.0.0", "browserify": "^16.1.0", "concat-stream": "^2.0.0", "hyperquest": "^2.0.0", "is-buffer": "^2.0.0", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "*", "tape": "^4.0.0", "through2": "^3.0.1", "uglify-js": "^3.4.5"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "airtap -- test/*.js", "test-browser-es5-local": "airtap --local -- test/*.js", "test-browser-es6": "airtap -- test/*.js test/node/*.js", "test-browser-es6-local": "airtap --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/common.js", "test/_polyfill.js", "perf/**/*.js"]}, "gitHead": "6719b400ca602759c2bd0345b8490b5689078ce4", "_id": "buffer@5.4.1", "_nodeVersion": "10.16.3", "_npmVersion": "6.11.2", "dist": {"integrity": "sha512-boQoQJ3Xqnh3XtUK+3NPcL/HOOMA133IyYBLsh9nWxH6XBGsBJVv/yhlZoIDu9it7LdGSWhdbn5jKR+slIflmg==", "shasum": "d5e8e2c5dae9d695fd7ac985d02ca33f3c148114", "tarball": "https://registry.npmjs.org/buffer/-/buffer-5.4.1.tgz", "fileCount": 6, "unpackedSize": 80971, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdZaIcCRA9TVsSAnZWagAAlPUP/2lEsKdi6RznPpcxfR3w\n6wuB2/qnVfhH6hRAZyqkGhCV0zLbB9zThvpQ8ikQi99N8wH81CCmOFP2D19j\nITL2WK+1nHr46tzONNDQh8i/+DRq+9Cw7/tDNwDRarxHhBW7bVctXENFDUU1\nreZf0uyVeOBOZ5528kRpeR+CLZ503EK9W2oSRiOrfGtL1xPuxdcfChgmsRIV\n2FAdO1kCgMrrgpKgAofFBECjnso4aFixJrgYfcMqd36Vq3R/9ba9xX6zT1eT\n/qoFE5TQl71/yzvqs7yq4boWDln3/hYqQvC2rZ0I4UNu0CFMcaf4m60w/tH/\nysWxsZ+Gl4/HKBaL+IiQR6xhTQcLokPYkz6GxldaFEhF0frCtyacWSzL0Pbj\nQMEscO9vy7Oyv+iNKzAnp45a1qnB4Thz7BfEr3wsXYE3XkX2XX2WA3JwBEsk\nE8h/hxlBMV8fm3BKl2ASnKqbw5IYlfxDa9DmhyMcuXdh9SDkqGo340D0sI3V\nwcNyb2Hoi2Hs1KT3ZBEHEpDbKXvTLqrF1cqRTmIzozlS0Am5AuvMUxzBN+/v\nwSvegdF2Oww4NnUJg0J0GnW1gvCb5dz3scYiwzYYAcBUKQZllzxoq6E21FWT\np4HM503DEa9LwRW+VvhnK3kGWnnJTPSvfBrGz5NwBzxDO+Ie2KijWfLi60N9\n68wh\r\n=GX+z\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDn8fEWoW1BF5kRqH0yh15fTwh6D08ozNq4s0OPWFu1FwIhAKhdarffJwtzc/ouzaww0H1mrssDgGon7EnPGRDs5bUy"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer_5.4.1_1566941723558_0.11852010978929828"}, "_hasShrinkwrap": false}, "5.4.2": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "5.4.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}, "devDependencies": {"airtap": "^2.0.3", "benchmark": "^2.0.0", "browserify": "^16.1.0", "concat-stream": "^2.0.0", "hyperquest": "^2.0.0", "is-buffer": "^2.0.0", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "*", "tape": "^4.0.0", "through2": "^3.0.1", "uglify-js": "^3.4.5"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "airtap -- test/*.js", "test-browser-es5-local": "airtap --local -- test/*.js", "test-browser-es6": "airtap -- test/*.js test/node/*.js", "test-browser-es6-local": "airtap --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/common.js", "test/_polyfill.js", "perf/**/*.js"]}, "gitHead": "b3dbaab202e043f7ecfb4cca9d594d60b0d80381", "_id": "buffer@5.4.2", "_nodeVersion": "10.16.3", "_npmVersion": "6.11.2", "dist": {"integrity": "sha512-iy9koArjAFCzGnx3ZvNA6Z0clIbbFgbdWQ0mKD3hO0krOrZh8UgA6qMKcZvwLJxS+D6iVR76+5/pV56yMNYTag==", "shasum": "2012872776206182480eccb2c0fba5f672a2efef", "tarball": "https://registry.npmjs.org/buffer/-/buffer-5.4.2.tgz", "fileCount": 6, "unpackedSize": 81019, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdZaVRCRA9TVsSAnZWagAAx/UP/jONb435bF3g8mH+o2VP\n+OT7vZRztcf1EPf/pCCXbzz0cPSA4IFPWo0kC7PutuCebNyRZ8tRhyXK4ITw\ni5MZU2of8ifVdJcddq01M1FEweSOkqJF3qV0PdH3JbMrHksdDycYovr3Y2j2\nfuLMZIzE+HejuTS59+2inG12X4rHNGDk2xAdrncSTw1CqdOQv23qNyYuOPLi\nw2PS6z0iCtxgGOec3D+005IZiuvjOTGD4Zd9JvOJuJWs6tyd+pMh79vb6BUv\nQ2qBNIKQTGYygk4swB+1HKlHCosIbFyLRrb0kVrmlTUXHpdkgUX4JRX9PoB1\nnIYFq8LHHtV5gAYo+/nyGHkMX1/Lr8bVxzlgAC6Y9LXyQsd76g1hkDAoaZdk\nhdYj4sjGET7E0fzVY0ivRiZmowez+TW1VYxaaR/4+6sZmO48r2zBle5bATcF\nIh+GUEsoW7ACj/oTxDixpcYt6xae6Ax9R7MCCAGJ9VH0xuVpT/HlYsoZo29U\nwdvorPJjEuG00B8SrCzdLt6ydrtU0NmCZ6Pxhawb1U8HktUYgYkpX0Rx30uC\nk9jCMa4oJb2CJPOXD5F5V7ZMdE4F2E7Hccp8rnwoqTQoBeI5y/QZXLj0D2Li\neO1vIxxDRNOS6xNfQR3bfMJRAhCnhITmYGQw6hXBsJSh9zV6I5AsQNBdp+ml\n/OKK\r\n=XbvD\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCnjp9tIk1HN1HddUfPx6J/y+xlS9sCTjsuWN9tyz7O7gIhAKJImU9A9rC1rZYKN36DG5pXpwSl4RMQQt3jqbNAExwL"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer_5.4.2_1566942544516_0.9141900465307107"}, "_hasShrinkwrap": false}, "5.4.3": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "5.4.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}, "devDependencies": {"airtap": "^2.0.3", "benchmark": "^2.0.0", "browserify": "^16.1.0", "concat-stream": "^2.0.0", "hyperquest": "^2.0.0", "is-buffer": "^2.0.0", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "*", "tape": "^4.0.0", "through2": "^3.0.1", "uglify-js": "^3.4.5"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "airtap -- test/*.js", "test-browser-es5-local": "airtap --local -- test/*.js", "test-browser-es6": "airtap -- test/*.js test/node/*.js", "test-browser-es6-local": "airtap --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/common.js", "test/_polyfill.js", "perf/**/*.js"]}, "gitHead": "c7f5cd77aaa7640f98c46c1b52b9b0006284c5c0", "_id": "buffer@5.4.3", "_nodeVersion": "10.16.3", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-zvj65TkFeIt3i6aj5bIvJDzjjQQGs4o/sNoezg1F1kYap9Nu2jcUdpwzRSJTHMMzG0H7bZkn4rNQpImhuxWX2A==", "shasum": "3fbc9c69eb713d323e3fc1a895eee0710c072115", "tarball": "https://registry.npmjs.org/buffer/-/buffer-5.4.3.tgz", "fileCount": 6, "unpackedSize": 81315, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdebOfCRA9TVsSAnZWagAAsBYP/A0hWN/J0ODf9AOzghpa\n2T5nMRuQcm3unE5n8iqn41FECFqXkViCDDfMun9Vqv36BlIeWcoKwtSX0QQm\nammIevFOxJme4OecdukGZyHAuSd/mWTU7/jPfUetiCnGaj9ygGtL8gSvN8ZQ\n62ZYqS1ojB4UZlAiOeOdJ6wJpy48eWb7sqkwurVh21FnRGmAcU7sh3+c8zsD\nqUamZfmtG0jUaoOYMVGzfJlSIdl+lQds9b/WDetzUvylICYNXD4tVF2x7uv2\n6XrdFDneHrKZhK8Ilz/jS8qNXBohVLeOWTt+ACflxZJfJmaK172E2l5Yer0Q\nZZOmuMSm9ce5pq+xLCZJolItogVvl5gki3zlWGQehpkfLUU09HKukp+qaggo\nhuQXu5+fu0NLv+Ne2XZgqqbioaoVWwxgFWy5Hu/HOb5dD63XDNC+8Rq/EGQ1\n4tDJGVsPbDAwjiN8XcAIYJGOLRWTFk8A1qOeGhCheNPSTBde2mbi/I+nL1HG\nbCb7FkEXq3PrqT1nkEGyR4cePLkeprl6Q2a8lAneO/aTpiNrC/GJUjm1ZXub\nmOxO3UAC//uFbRAXE4JiyK2gGIRlz9noAjpW9CcQPz9Se8HrNKqQx25mEYdt\nhCB72lF772j/jm4gONYuz+3DrarXZLbRZ2nswYSB44yELPEmXg/ytFhoc9lT\ndoCw\r\n=BVmF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCfgF0CCp1aDktf+fh/EIXuGoNExzrBwnb1FZR/hwpcLwIhAIFKFkdVrwTXJHiboRD6DTcdAKsMViVEvuJUw238VRvR"}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer_5.4.3_1568256927004_0.36045588201420076"}, "_hasShrinkwrap": false}, "4.9.2": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "4.9.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}, "devDependencies": {"benchmark": "^2.0.0", "browserify": "^13.0.0", "concat-stream": "^1.4.7", "hyperquest": "^2.0.0", "is-buffer": "^1.1.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^7.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "zuul --ui tape -- test/*.js", "test-browser-es5-local": "zuul --ui tape --local -- test/*.js", "test-browser-es6": "zuul --ui tape -- test/*.js test/node/*.js", "test-browser-es6-local": "zuul --ui tape --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js && OBJECT_IMPL=true tape test/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/*.js", "test/_polyfill.js", "perf/*.js"]}, "gitHead": "da2e3fcbf0416b79ec0c56c348bc7efd98d9ff37", "_id": "buffer@4.9.2", "_nodeVersion": "10.17.0", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg==", "shasum": "230ead344002988644841ab0244af8c44bbe3ef8", "tarball": "https://registry.npmjs.org/buffer/-/buffer-4.9.2.tgz", "fileCount": 44, "unpackedSize": 263614, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdxf6FCRA9TVsSAnZWagAA660P+wZtHWkkW9B2l52L+V/a\nUFtrTs5IiovWKeAmzOempmiAhHtEdt/eId3KVqjvm4tbfgskxttoPUqgGNq2\nu/MpmlV3HDcWdrrtkSFOo2uy++yJs8cdLVUSUdybP3NhXPwMdfVFIGfFbE45\nhDsNLFtLJfJQSi/Ctq3F3IKvA2kep0V+21l4vrtP/eBFtsWQIZMet/GyeMl+\nOcDrFuKICk2JOlZPRqe2cIW2VEQHDPXfBV2b9KxO++UPsS3VZv4BwNb2iOLJ\nOQDSld+Ey4PcXvZMVZ4tnOX1MPDcWgF/++cORZA8u+e4fLh96QHgufUuYGbD\nMlPiD/mTlNvY/wgOV8rRBm51kN49FklFcYhphdTr3yyEO1h4Kzx9K94+Nskk\nADU+v9LBBSsYRNnNItHQl8YKteHK9qBU3nRRAEnTaFxGT8GAcpc7lKPqbbLq\neaoHQeCo7Y8jldoliwGwIl6CMMZCl3Dcymtr9h1tfc2SlupV3Qp/ysSfnTM9\n+5XJ/ZJ77OyxOtGj2vNdtT79j9PVRTdocFEA3cyX6/86J9rIR2MAD31SLucy\nSCs45oBfD76mOfqjveKL69TT2AqVB3n21AFdINW1TiUhiWlpC5qDjwU8lJmW\n24EhaDZ9Drux0wZdbU765UablzliLMt9y4K+/fgEVYGNh57oU8lGLEW6q3il\niXF0\r\n=2RLl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFwRn2eQe0Ah3AE4VbKgjzvcQKvDXRz92bL0EtTWMl8rAiAxyO0XLRHdJoJkDB+SxkJHykGIwJqkOlguxXpS61et8g=="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer_4.9.2_1573256836349_0.9392586515103449"}, "_hasShrinkwrap": false}, "3.6.2": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "3.6.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.8", "ieee754": "^1.1.4", "isarray": "^1.0.0"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^12.0.1", "concat-stream": "^1.4.7", "hyperquest": "^1.0.1", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "^5.0.0", "tape": "^4.0.0", "through2": "^2.0.0", "zuul": "^3.0.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "standard && node ./bin/test.js", "test-browser": "zuul -- test/*.js test/node/*.js", "test-browser-local": "zuul --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js test/node-es6/*.js && OBJECT_IMPL=true tape test/*.js test/node/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "standard": {"ignore": ["test/node/*.js", "test/node-es6/*.js", "test/_polyfill.js", "perf/*.js"]}, "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "gitHead": "26ae942c0462e8a32b2a33136f3291f1013b7216", "_id": "buffer@3.6.2", "_nodeVersion": "10.17.0", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-c3M77NkHJxS0zx/ErxXhDLr1v3y2MDXPeTJPvLNOaIYJ4ymHBUFQ9EXzt9HYuqAJllMoNb/EZ8hIiulnQFAUuQ==", "shasum": "d83b530100c4a2c598bfc8ae24fff29812cf49de", "tarball": "https://registry.npmjs.org/buffer/-/buffer-3.6.2.tgz", "fileCount": 30, "unpackedSize": 144773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdxf7ECRA9TVsSAnZWagAAEooQAJWxb5pdfCwaYVd+WasB\njQlJ5DMKBRQ1t9eNWxlkLt3u96EjGlzxpXywZxYY34H8v1ESMthyMU+eJnmh\nap0IaEduaTHprimM5+wDSYpEjRiEzEmdVtnoRnk9v7DhHjKSb9L84hOQKQlG\ndaVOgeBS6a/lNIEEW0nc1OmUYInyt7cUKeMG+ybnXRFQtFwyr9jkw04skaPW\nR1mnBMJPAEv9flvcqkWNX4B0ahRhwRBV14uCirYjFsjFufSaJTgGG8xH08Tf\nhj7oC20CHtQtpH1Jt6fdcpBR5fEWjusdIhwxfWvbaMXm1BNWIh3BLKvqloD0\noMrqMhHbAujWRqFFlAFyyslp6Lvy0tFP2Ntc7hrbCA4kxNN2uB9oghwMYmp4\nJqm4jAieSuGBU+2cB3QDfgUnIAkd9KNGlRHPoPJ+mONDb1MBLmZw/d7LLbkL\ngaQ9+bKHWHED+e2y38HigVvLy7rhO9oPl50nWB0D1tDJ1+8o5cyISznydomT\njdhR7Nbh3Gzm3Tn4TtxF3mKJBtAFFkkmf7aI+Jv9LTu3jL3wSjwirTieGE+X\nZfdKjDJGhXcajvy0D7+8whs2yn6Qlq0tsq1j2mKSnxF6ZQplyQ6S04kp5dad\nmSMQzPpvYZWbPAupJYX/DIxeJspzRFPvAIzPMRbMI6nXZx7wxf7E6Xzvxta0\na/tn\r\n=bT4a\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE1rgq3fl8Ds/aaBfKCyzLqY19XJFhPiw/qvU5gveFDXAiEApJOyW6m+yqidiM4u6NaViM41+Uqip6OCcK/mmq/IUXg="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer_3.6.2_1573256900455_0.2627936746707136"}, "_hasShrinkwrap": false}, "2.8.3": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "2.8.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "0.0.7", "ieee754": "^1.1.4", "is-array": "^1.0.1"}, "devDependencies": {"benchmark": "^1.0.0", "browserify": "^6.2.0", "is-nan": "^1.0.1", "tape": "^3.0.1", "zuul": "^1.12.0"}, "homepage": "https://github.com/feross/buffer", "keywords": ["buffer", "browserify", "compatible", "browser", "arraybuffer", "uint8array", "dataview"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"test": "node ./bin/test.js", "test-browser": "zuul -- test/*.js", "test-browser-local": "zuul --local -- test/*.js", "test-node": "tape test/*.js && OBJECT_IMPL=true tape test/*.js", "perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/4..latest", "firefox/3..latest", "safari/5.1..latest", "opera/12.0..latest", "iphone/6", "ipad/6", "android-browser/latest"]}, "gitHead": "088fc8a3cc5460e3517f9faee89c848c315ce59d", "_id": "buffer@2.8.3", "_nodeVersion": "10.17.0", "_npmVersion": "6.11.3", "dist": {"integrity": "sha512-dyatqxbSWlkhnG5lthQ7TDh2NfShsKesnKiGyt5DmiJfvKJ1zBq1AvC3+neSY565BziAiYwbothV2tizAr2WRg==", "shasum": "74ad36487fff7413f4a6fdfb299e38ef3612ff47", "tarball": "https://registry.npmjs.org/buffer/-/buffer-2.8.3.tgz", "fileCount": 19, "unpackedSize": 68341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdxf9eCRA9TVsSAnZWagAAoGUP/0GidML9yDVJ7sFH5+R1\nVomYSzAluq6RlBJrsEgrTa4caPmMnUMCt5PHb1oYaoUUJaBrG94nxHz02XJj\nIWBRmZAXLPTREeF9aDBd+pAspSfoVs9v8uzMUcaNF6pfTgdKU3Stv8MHZ5I6\nN2YxLS8y8Cyuom0FKtJv3WsxAnJwNDYfu6rx1w4KKR1pbK0yt2liCY4pBGpK\ngmDCW8/XeuW9UVs2Wz0nifWNa9E+OSU/+Te7orXM34f0R5lkC3l5KYfKXQJo\n4V/4ppyGd1AHC2oFoT0dO1smHPxeNATZEgH3IaKBRBSlYHq3LWDTyG34pyQ9\nFbzT8CspySKlsrL1AroMHtKBTw2ZSewsbgMHIYXEQ7ew9lPZMxntyj1kEBiz\nVDzjRvDtew7MMTjZyzFPmZWJQnYU9rlSzt7XsV470XewO5/hDteMaYLUwc98\n83sr/8AA5LsrZmUVP2mHcMuUdUwbB4BBUIjKsfUZPbpPpO8+Zc9z0H0PZM6p\n6jfJ0IU2W3GQ8fkDLprrBwAFa+w3BpFYLM1AFnGANBKFMGIX6ORPiGR00jxT\n0p4Q13jtGzeUR7C59GCgUAtvHwNKZ1HPYpeoZ6zO14n4sIoqUz8qqDSK9hEd\nbeqVmfdwaOiPhNqa/Tbk5ULKD0mxOu7Haa5CNl0yUnAEMI0jmwokcr0tj1eO\nCL0U\r\n=pv6t\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEbj2pDvOZRATGsl7h9Ta0tBFdHWdNphnhHbtMj1eDhxAiBgld575spi1CAfuNeOdffQ4kIeyGWm2EWRroSBoTz0zw=="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer_2.8.3_1573257054078_0.17965496499842715"}, "_hasShrinkwrap": false}, "5.5.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "5.5.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}, "devDependencies": {"airtap": "^3.0.0", "benchmark": "^2.0.0", "browserify": "^16.1.0", "concat-stream": "^2.0.0", "hyperquest": "^2.0.0", "is-buffer": "^2.0.0", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "*", "tape": "^4.0.0", "through2": "^3.0.1", "uglify-js": "^3.4.5"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "airtap -- test/*.js", "test-browser-es5-local": "airtap --local -- test/*.js", "test-browser-es6": "airtap -- test/*.js test/node/*.js", "test-browser-es6-local": "airtap --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/common.js", "test/_polyfill.js", "perf/**/*.js"], "globals": ["SharedArrayBuffer"]}, "gitHead": "b0a6de5f2131c3e339f14801bb342d16d580ac5f", "_id": "buffer@5.5.0", "_nodeVersion": "12.14.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-9FTEDjLjwoAkEwyMGDjYJQN2gfRgOKBKRfiglhvibGbpeeU/pQn1bJxQqm32OD/AIeEuHxU9roxXxg34Byp/Ww==", "shasum": "9c3caa3d623c33dd1c7ef584b89b88bf9c9bc1ce", "tarball": "https://registry.npmjs.org/buffer/-/buffer-5.5.0.tgz", "fileCount": 6, "unpackedSize": 82061, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYqq+CRA9TVsSAnZWagAAsVYQAI/paHnOYhfxZj+ooYAq\no1iRv3lUGee0z3qs8lZw4IK2BOgM50vh09p+9EXiTDNxPfoCeivu7k+K/vKq\n1Z0NyKK74Fo4kIC6zdTj7kZHKemITK1IRmIjY806SrtkNj+6bKKz+1A2sKZ4\n5n3va+QoDm2dte9Sx8sdQV+6Rql7ZgapFh8I2cjN/kg8KvULzbUPRYtWHU0t\nCQIjSeBiiFfP1rCcg6+ys0+MlM/iHsggh9BC4UMxtgi0ISwLN8xbk28uC4Ar\nmSH97DMj3OitRgNNnrWWDNfJTKy5Z3w/7bLuxoWNPpAQKDicUtdUZL/inQHN\nJlFPjR844t0O6XvivQfbvxFoOnx7jFSLQUhd1g/bM1kE7BiXTajREjdAZNdY\nd5/4zavqewlKuP6T1511yXwLxESyGxxinu+Dr8YdGI4B56pnHxdgEQrv+8ET\nOG6IBm45Vt9ocFjYEjraS2zFUkHKOZvlzHu8Njye3a0uCEF5eDIQS6weO+/6\nxrv3dVqOpxY3VQ/iiGHktZIfX91k94EEad3yta5S94wqGZhvi3Np1rWrI/EB\nwpZ0+MIgBegK67Ebp0viTzmit/wbG+SOXg2m3Kh+z3dgplksLzU9WVM01TJf\nUBvLwwGqiA1v52TMDwn02ABL3mNbjk5A0OnAQy0D8HI5y19YCqaHCXOLjzIv\nwZaR\r\n=jRQQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH0AVfmwp7Vh8T02t9QdINMtXFleHXuwC/J11yQbV4RgAiEAhqKBs9h0+p0bB59ErvyyvxHXdtRaTsa+lOYQajyT5jA="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer_5.5.0_1583524541733_0.9409099275946293"}, "_hasShrinkwrap": false}, "5.6.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "5.6.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4"}, "devDependencies": {"airtap": "^3.0.0", "benchmark": "^2.0.0", "browserify": "^16.1.0", "concat-stream": "^2.0.0", "hyperquest": "^2.0.0", "is-buffer": "^2.0.0", "is-nan": "^1.0.1", "split": "^1.0.0", "standard": "*", "tape": "^4.0.0", "through2": "^3.0.1", "uglify-js": "^3.4.5"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "airtap -- test/*.js", "test-browser-es5-local": "airtap --local -- test/*.js", "test-browser-es6": "airtap -- test/*.js test/node/*.js", "test-browser-es6-local": "airtap --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/common.js", "test/_polyfill.js", "perf/**/*.js"], "globals": ["SharedArrayBuffer"]}, "gitHead": "d7b15a178294ebc85368aac87b0831dc48d6fba8", "_id": "buffer@5.6.0", "_nodeVersion": "13.12.0", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-/gDYp/UtU0eA1ys8bOs9J6a+E/KWIY+DZ+Q2WESNUA0jFRsJOc0SNUO6xJ5SGA1xueg3NL65W6s+NY5l9cunuw==", "shasum": "a31749dc7d81d84db08abf937b6b8c4033f62786", "tarball": "https://registry.npmjs.org/buffer/-/buffer-5.6.0.tgz", "fileCount": 6, "unpackedSize": 81740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJelk8SCRA9TVsSAnZWagAAYcMQAICQS02OwuDr6T1BPVPR\nLazVSpJZaIrnoVDIl7n85fNFezaE1Y3z6ywtJOdgB3C/NXst7lb+nrFEVpIP\nUVE6q1BeB4tdHoqGmJS5cAdjtJjH37NuNFJ0Cg+xlYILSQK9HJF316oiJLhm\n4wV6P7UwALHmYWO51DglSrQN1Jn4ZOJ2Y43yn5NJiBW9RPnWX76iflau2CI+\n8RO6AF/TGK/7gyWk+9LpL6QNHtrder/p8NwDiHYnwibzvdJasdQaX1bDbPEV\npjDWTUZ0JdmJJChFE4JMXCa9x/Mkc3xBmtXT8jyJWVLIHTePwrnQGoWF1Zo4\nckFUGDjumU+Ilsu9Ojy2Nr/l0J1wiIl09aIirtPbnWrc/LbMHnKUWMX2aevg\nUEBzpnmpwFNKtdAQ7OnJk+EfgzG1Wozik505dJiAuikXwj/gUL9PZkSBseSf\nnDYT8oa5XMj8DCN4IDKWM4UDbrjQEd86ciJawClpHmA/swAk6x2SVEnPv1Cn\n+hvsLEu9HlW0G8CT1HYrCa77kkPuhJHWsbFPZAx3Nku2QfvaleevzcQ4TKfh\nB9Ua4Dfx4zM4zbC14qoOk+ho2StuDsok4DknfR8x3Ca8wAUsAHYQukngm7y2\nhbKpHOogklo+rsJJXcUdkYwO0Z7OlckWiKHEvpx8tasviOWFuDg20hFcwIEA\n9cAE\r\n=TDZl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBf3rGJ9e3Eor9r9FGftSi/DOmYdIGZ0JZhG+PG69a4TAiBZZL38wJEGKH4tUsJ5zfyjjNieWg7IG0nul5yLvbSPgQ=="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer_5.6.0_1586908946240_0.442043685850807"}, "_hasShrinkwrap": false}, "5.6.1": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "5.6.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}, "devDependencies": {"airtap": "^3.0.0", "benchmark": "^2.1.4", "browserify": "^17.0.0", "concat-stream": "^2.0.0", "hyperquest": "^2.1.3", "is-buffer": "^2.0.4", "is-nan": "^1.3.0", "split": "^1.0.1", "standard": "*", "tape": "^5.0.1", "through2": "^4.0.2", "uglify-js": "^3.11.3"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "airtap -- test/*.js", "test-browser-es5-local": "airtap --local -- test/*.js", "test-browser-es6": "airtap -- test/*.js test/node/*.js", "test-browser-es6-local": "airtap --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/common.js", "test/_polyfill.js", "perf/**/*.js"], "globals": ["SharedArrayBuffer"]}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "gitHead": "78ec2f29ffb2fb15148c8cb65587914b1f106cd8", "_id": "buffer@5.6.1", "_nodeVersion": "14.14.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-2z15UUHpS9/3tk9mY/q+Rl3rydOi7yMp5XWNQnRvoz+mJwiv8brqYwp9a+nOCtma6dwuEIxljD8W3ysVBZ05Vg==", "shasum": "b99419405f4290a7a1f20b51037cee9f1fbd7f6a", "tarball": "https://registry.npmjs.org/buffer/-/buffer-5.6.1.tgz", "fileCount": 6, "unpackedSize": 82052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfk0EOCRA9TVsSAnZWagAAW1sP/0rMR0DjY6XpTxOn7/u+\na0yifMJRI34XCMPdPRy96odNpyRQw3QCIXm3WWJTXcffeQY2KFZhauinkvRO\np1ac530US1Lphyorj1o5bLHezN3NrCRgdqNgzRZreYob45+0lb5qQkJqF28C\nxigLFbff6LphKpgpJlWvXiux3kfsCQn/OikuPr17c1JeLefdeQZ9yG4yV93U\nl99OolRlvaCbtMPb19m2I0qaeF5A4EzKu9mgfxde5S2aKroDpO22CF2/315H\nLw2b75lP6Sc6Wkf45VR+74HNpI/v8tGvU3RkDcFLBrRV6/HP2neNEtIwXa0G\n7SDiQGjySDK02SAtUzLnOMus1tkGL1Nga7SiHZnc1Mw34GXdV2neDQN78ddb\nyRHCdHk5P8C4Ivdw6sbEAR66KP/e3SyWQ4YJ+a0qK/ScN2BPjbGSIHbi7qWn\n7JDkFKmuV0DiGVA0q8hh9JHqz5kYfhma2UPkp3KECSqWgeTnHW83VAJSoRHa\ndxebXiXaJ7o6mvg+rs2BW2HKNN7bcI8ECkeQHHq2beqlcN2W63OTHhRIu2D3\n93CNbISm8NOphy157j8Hca5XLhOwsp+t2JY6n9jSohwDoki3yoBvPD0uLxTO\nkemL0C29CxBcKg8l++4WgddPO7VAygqF7/iTIpaekGwRBPAKycmjZ7b4fQbM\nX5FP\r\n=Vrd/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCyX7/SBgiCR0I1u1dLvH576sOumIvCVH/r7YtXbXS6AgIgGoa0XuvMfbNkbBXrTIQg99iPPbYxep5YYFJ3PtWL2Us="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer_5.6.1_1603485966012_0.9445442552371313"}, "_hasShrinkwrap": false}, "5.7.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "5.7.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}, "devDependencies": {"airtap": "^3.0.0", "benchmark": "^2.1.4", "browserify": "^17.0.0", "concat-stream": "^2.0.0", "hyperquest": "^2.1.3", "is-buffer": "^2.0.4", "is-nan": "^1.3.0", "split": "^1.0.1", "standard": "*", "tape": "^5.0.1", "through2": "^4.0.2", "uglify-js": "^3.11.3"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "airtap -- test/*.js", "test-browser-es5-local": "airtap --local -- test/*.js", "test-browser-es6": "airtap -- test/*.js test/node/*.js", "test-browser-es6-local": "airtap --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/common.js", "test/_polyfill.js", "perf/**/*.js"], "globals": ["SharedArrayBuffer"]}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "gitHead": "b0713925abe5686d1e0b0a8310f58dc97cdb5fce", "_id": "buffer@5.7.0", "_nodeVersion": "14.14.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-cd+5r1VLBwUqTrmnzW+D7ABkJUM6mr7uv1dv+6jRw4Rcl7tFIFHDqHPL98LhpGFn3dbAt3gtLxtrWp4m1kFrqg==", "shasum": "88afbd29fc89fa7b58e82b39206f31f2cf34feed", "tarball": "https://registry.npmjs.org/buffer/-/buffer-5.7.0.tgz", "fileCount": 6, "unpackedSize": 82374, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfmJfqCRA9TVsSAnZWagAA6ukP/2Qy877Da8y5l75Uz4GO\ni3qyfkpElZHpLv4cd8gjrGBNWKqunJDbn57Y0xqpD1YZzEsnZot4eXP7IEXM\nlB8frADd7LtU/QHXgSj0ZFDiqOtUW60Enb4A2TpFmbbSF7XpQHDafv4IKKwO\nRtCyuL0yC1IaSu1pL6UPoVXnOnDHIjEKHfhKNGfdKtsQPFCrucz/Ciwr033H\nGIcX+xfPI4umh4DUgJ6WpZKurXlNCKCPou59skptogG+YI+Dkk0Tp1VAErRI\nHCb7tGPYWRGGK1F+gb4F7Wyid0HvtgIWigpEBm/BY72ImLWE6l2C5y6WDROv\nuIn91d7IplNGMwjrgSNmv9/EZ77bEx/PLC38YAhalRF/zxTjF4V7JwVUKzAf\nA1T6VrvZGrkNLZNLD/l9HJns6pkNZqGVK9fWvar1lAIryh04YP61yG0JaqAR\nrcEfeGJ4rBKgN3jytem6GU6Q2/W4jitW6lFDNBRugGLK1jiJWSLlGoDJwReJ\n+urC1NKVQBy2KfY2SEYGqxPb8BjSECUHSatpz3c9yaa+kEkcbSE6pqP/gi6U\n2OdXvZKFsHGd0mnxNJQR6rmGFUbNOKQl4hbmm44rXNx/4jkjOf49+CIAkblT\n6z+6ABEj11SP5kx0NzDCSvYTaxR6fJr7Vnq56SR/fxKK6zZTNRVRevyf6fHt\ncfbl\r\n=pfy3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICvyScpkjR3q3lkpwvlcePQHODxx9NsrC1z6FIyETSoEAiBtWifLvW44HJIUkBcaxR6JVE3n8xBbhy0MrAivhbBtgw=="}]}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer_5.7.0_1603835881428_0.5387066206933739"}, "_hasShrinkwrap": false}, "6.0.0": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "6.0.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}, "devDependencies": {"airtap": "^3.0.0", "benchmark": "^2.1.4", "browserify": "^17.0.0", "concat-stream": "^2.0.0", "hyperquest": "^2.1.3", "is-buffer": "^2.0.5", "is-nan": "^1.3.0", "split": "^1.0.1", "standard": "*", "tape": "^5.0.1", "through2": "^4.0.2", "uglify-js": "^3.11.5"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-old": "airtap -- test/*.js", "test-browser-old-local": "airtap --local -- test/*.js", "test-browser-new": "airtap -- test/*.js test/node/*.js", "test-browser-new-local": "airtap --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/common.js", "test/_polyfill.js", "perf/**/*.js"]}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "gitHead": "f44c2f25148d40395edda67aa465ff7bd98cd987", "_id": "buffer@6.0.0", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-Sxdxq98A+Y9kRjO/3+mc2IAxIyTAKqzBiYKpeo5EluWnw9535rI4fN8DeMGsiQqpqqaWtFtTdxQgHnku6IEjCA==", "shasum": "5af63d0ccb2a3f72ceb347344a43a610c7dd583c", "tarball": "https://registry.npmjs.org/buffer/-/buffer-6.0.0.tgz", "fileCount": 6, "unpackedSize": 90560, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfonQzCRA9TVsSAnZWagAAiQoP/3rN6ELD/8W6eeVypYKp\nHlB3Wuc6bjDMXzWm5XiezUxsYGUv7xtU0ecl/wAc17w49md0BucHfCDlA44g\ntD4O36B6FtvoPagIa5ABsW4RDVWpUNazfs/vHtBxlFXj4MqUlTKbF5X9ovFi\nqrNSSkq3aIqd3WXEnBbHxFR2xz+ed7OIZy4V4TOASh11rkF5XzkLtCvcx8xk\nzQvNOHWKQiPS6hXVamkOpPpb5zhUTxzGC8Aze8z255zLhA0cdsUyk8oMO4fQ\nAOKQ3V0z/9tuzXUp2d1Ps9ZzLKrYaZh67d+W7MZMAVJjWpM891L/jokQP6np\n/lMBEpbd+MeO7Na/MF1avhbO6/87JAy+srpgnAsGR22tIZOzjhkrJMC/wzJ3\nvgbUapWP6FHQ//9QkJbgN9R7PcRP+hgjog3kEkQoBCMp7mSh8K/RX+m/0Lda\nlMIixk85KvIzNQJEnxLiDp7oVoEyI32z/ieLi6w3LQTouEFjZfFZZvp3XPpy\nL2+5GglNm1nWfA4+e3f2BRxtSFTjsvJQYx16XQLQk3Ily8xpDqzzc+88ZSDU\nzsvRCh+19J2ZZ/ygOziODZbt/HKkJza6hKUMg6hUHN+N27/jmOxebDKZprQ0\nQ0GBtS6Pyy04ENY72j/hYVh7ct2W01XVYdJmpRtt7B+Gkp8ZE3uXMg2ozHtw\nh5xt\r\n=14xG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAaxOBTY+CMrqC+vRav4lS6yv9t8UdOLo+ftv+JjfIXaAiEAqyFW/bGv5j8PfI8YVpn1a4do711Hxo6T56qfxhtJCnA="}]}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer_6.0.0_1604482099132_0.19426225783131246"}, "_hasShrinkwrap": false}, "5.7.1": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "5.7.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}, "devDependencies": {"airtap": "^3.0.0", "benchmark": "^2.1.4", "browserify": "^17.0.0", "concat-stream": "^2.0.0", "hyperquest": "^2.1.3", "is-buffer": "^2.0.4", "is-nan": "^1.3.0", "split": "^1.0.1", "standard": "*", "tape": "^5.0.1", "through2": "^4.0.2", "uglify-js": "^3.11.3"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-es5": "airtap -- test/*.js", "test-browser-es5-local": "airtap --local -- test/*.js", "test-browser-es6": "airtap -- test/*.js test/node/*.js", "test-browser-es6-local": "airtap --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/common.js", "test/_polyfill.js", "perf/**/*.js"], "globals": ["SharedArrayBuffer"]}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "gitHead": "edc667080ba3220ac35d4d1f64f2a78d89127c1b", "_id": "buffer@5.7.1", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==", "shasum": "ba62e7c13133053582197160851a8f648e99eed0", "tarball": "https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz", "fileCount": 6, "unpackedSize": 82527, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoyJoCRA9TVsSAnZWagAAiH8P/1CL/bEXlza64QzxZAgI\nYTF6rLWBxO3mypgWxbhnzZ/m/AeU0a4iH2wIY7GYOgs9LYK2bu1vnM729z2P\nNsxHqt7rHwREOQGbE/zQW23/5DT3BFiZWyLQqkv40IuE90yPCsZHQSnF6geY\nRGSqRKS7ebmlxt7k4Ao9/NqC+jrsezAM6hShhHdPx4wkZ3MnZYK1w9O8t1eI\nymRxtVWpTe5N8Qa02KdVja5O/muH9TR5131hC4JzbUN7sv7drn+8NPBHIOK2\nDVans5leF9nilMmSBV2s6F0v4aHyIEJiEiRPwWAJXNsdzZ72crnHWtaHSnaq\n/PkrQHQUmo0HNVyC009Ke0ajISdxoS36o3ZTdSbIY2p9Dr1CR7geX3QF8UiH\nhKWUch3MgrVOY0SkHWGDgVsmMVGuT6D7RBD3g2G7QTmkBUQIUZZ3S55V1Q98\njMWjbgXm/+gc7cteuKLMH06N7Bct34XQS/6/A5P6GFQuvtAPFC/sgg78uapp\nqzZ0cNA2IdobxvpeOsMDE190HGQwcjV3k593+Iem7Im3H3O6W6yxEPTjJIam\nFK+i0N0lzoeFJ94qJBUDJwxN24T+actNlHbZ6n6MwzpIZ+vQMhSfk611cXl8\n/9c0OrhHMqnOYq/dNQEcOgyl9aqjKKEkOLF0YKwIoe5R4nY0lxVbEjmNDwfv\nTvqT\r\n=tlSl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHobVsRRO61NY46sFBGhHZXHIxcvJmUESIJ4DAer0GBpAiAPkpbbX2V7/JRyMT9Qn/IS3nxeIzHw/A51FEKgbU5UBA=="}]}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer_5.7.1_1604526695408_0.6185328367244336"}, "_hasShrinkwrap": false}, "6.0.1": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "6.0.1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}, "devDependencies": {"airtap": "^3.0.0", "benchmark": "^2.1.4", "browserify": "^17.0.0", "concat-stream": "^2.0.0", "hyperquest": "^2.1.3", "is-buffer": "^2.0.5", "is-nan": "^1.3.0", "split": "^1.0.1", "standard": "*", "tape": "^5.0.1", "through2": "^4.0.2", "uglify-js": "^3.11.5"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-old": "airtap -- test/*.js", "test-browser-old-local": "airtap --local -- test/*.js", "test-browser-new": "airtap -- test/*.js test/node/*.js", "test-browser-new-local": "airtap --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/common.js", "test/_polyfill.js", "perf/**/*.js"]}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "gitHead": "74899b7fbd0e3247931fa3cfb9fc27e96a995d21", "_id": "buffer@6.0.1", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-rVAXBwEcEoYtxnHSO5iWyhzV/O1WMtkUYWlfdLS7FjU4PnSJJHEfHXi/uHPI5EwltmOA794gN3bm3/pzuctWjQ==", "shasum": "3cbea8c1463e5a0779e30b66d4c88c6ffa182ac2", "tarball": "https://registry.npmjs.org/buffer/-/buffer-6.0.1.tgz", "fileCount": 6, "unpackedSize": 90758, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoyQbCRA9TVsSAnZWagAA79QP/0FmXUivbhefRptMkySE\nfr7zZTN+bMh/q+ohi7LQswzxUnYppC5HwUvdPxtzSi5EiJcjJXtVJJbm84F+\ncq4w2uookAsC7q9jLUpRDHooZF/GBeisMEBSqpL2n2EOb09T3NM0wwvPeSz7\ng6YoII9RgZhYLhRMdEpXNfqwz6H3Cb1DQCc3fLowxY03gjHqUDVGlfUtVw/K\nOZ//YCwf+dad4WhO07xaUrv9jk7xhrATi4yFHlsakMhfbKpzhnkJSgbzWsDP\ng9wm9pZ20VC2zAT44xiJuL3ve1WahtuBHGYYUH1EiDs3k4Jpl8itmPa43NZf\n8TWFzWpjyr5QY/+Cu7eMu4gkMDsriYU1PbzTUMsueQ6YpRIHl1596xn5biGY\nEA0SJHx6eDFoGkvQC9ZnkohKxYXnUm2L9UhDIU42LzWYH8SoTNCsHOQQtZ5J\nVACgPfKOuJN67RYSvvIG1cqwrFl/HcRepqPgZo687+ivq4+FgCMgnTeRU8rf\nFSZMd/eqvPax995F13HK06dh3qtug/LqUJGGshfk1d9NBmF1OqZTJJVvV5jV\nKcXunFHG61LP30BbKkAJq8D3msYu34/0O/NrmFgnZlatxNn37cf+qf9BUVu7\nuKxkzooyh7BL6gzPWR7WDLajHdzjqOEC7Yh5hpoxeCmQfitoZq4fLkfsdKoA\nD2kQ\r\n=87m6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHUf62rijgje2ZfjwtuO38LJykv/VC4BRvKENhoRhVfqAiEA1Xkujc+V8Njbjj7F4C8gVfHv7gvjEzZulMi/ojPcUG4="}]}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer_6.0.1_1604527130847_0.03636976423334981"}, "_hasShrinkwrap": false}, "6.0.2": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "6.0.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}, "devDependencies": {"airtap": "^3.0.0", "benchmark": "^2.1.4", "browserify": "^17.0.0", "concat-stream": "^2.0.0", "hyperquest": "^2.1.3", "is-buffer": "^2.0.5", "is-nan": "^1.3.0", "split": "^1.0.1", "standard": "*", "tape": "^5.0.1", "through2": "^4.0.2", "uglify-js": "^3.11.5"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-old": "airtap -- test/*.js", "test-browser-old-local": "airtap --local -- test/*.js", "test-browser-new": "airtap -- test/*.js test/node/*.js", "test-browser-new-local": "airtap --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/common.js", "test/_polyfill.js", "perf/**/*.js"]}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "gitHead": "2b1286b3e0c61f22d68c9f7bdca2c11012d87dfe", "_id": "buffer@6.0.2", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-XeXCUm+F7uY7fIzq4pKy+BLbZk4SgYS5xwlZOFYD3UEcAD+PwOoTaFr/SaXvhR1yRa8SKyPSZ7LNX4N65w7h8A==", "shasum": "ca9ab87dffd0e864977f541f09844f06a60a8acd", "tarball": "https://registry.npmjs.org/buffer/-/buffer-6.0.2.tgz", "fileCount": 6, "unpackedSize": 91215, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfqbz3CRA9TVsSAnZWagAA+3QP+QHnoLf4xolJPRTcRfwX\nr86JM7uEaSvr0dqffG+a1O0qfZxyFYZesCiM3DMUd4KA6IOJLesWOP/Cw/d5\nXgumNd5I+3YyvmnAPnCYAxwVK7+EdhwHDoPWcn1WR6dPElHfdbzixed+8IZt\nQO8vB7JDKuT9b8bnob6WDXFwHf5cvieTY+4BbpDseOwK1/XnUoFxvQbJbxX7\nwaacfMp8lnKvtA+ROqhj2VH88BGm5fwibukg2qz8b7xLNhxHp2aqvLn26HWD\nJBsmyrKxhv5UYIW3UF3mRMLhhvYhm8rIF8CkWIXfegIW76WW1czEaw78bUi4\nr5VzDgzXT7TKwy1ZII6jirzXj62Eo3KasB/oPIWeBCNyOLx0WwrFvQHIEoEK\nktgohiZqBz9RhPU9BLmrY5l5SC3WoJVDcQGyXEwfnwRnXRrVL2ODFleHlxY9\nFxV6T0Ckff5GH6kLIzNM0NDyPhszf/Jsi8/jZCwu6q2P5uX27UbTfpTmu8Mj\nlgIIGiqaYvEvB2wGYPoZ5LqSunI2R8zlCugQi+jlMdApDZgxzqCZtUuUwEuJ\ngJvYJhAqU++xGdYxcIAMuVMRS9OrjrNI/VIU/G0mIhxZr4H8OsT9q0cbfju3\n4ca6wB/xesPInXMkotTLWY3mk0tSirmYzfgX2BmflEpqloMnhCgJml+HG306\n/8UK\r\n=p30X\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHfV3KL/XFTPEh+uc5hU+gDru1m0h+IzTw+RLNRdxVUeAiEA+zV+a5zhiFl2Sll18Wg1ryg9O6xyYsytuFfYabuigmU="}]}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer_6.0.2_1604959479363_0.43853673897423606"}, "_hasShrinkwrap": false}, "6.0.3": {"name": "buffer", "description": "Node.js Buffer API, for the browser", "version": "6.0.3", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}, "devDependencies": {"airtap": "^3.0.0", "benchmark": "^2.1.4", "browserify": "^17.0.0", "concat-stream": "^2.0.0", "hyperquest": "^2.1.3", "is-buffer": "^2.0.5", "is-nan": "^1.3.0", "split": "^1.0.1", "standard": "*", "tape": "^5.0.1", "through2": "^4.0.2", "uglify-js": "^3.11.5"}, "homepage": "https://github.com/feross/buffer", "jspm": {"map": {"./index.js": {"node": "@node/buffer"}}}, "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "license": "MIT", "main": "index.js", "types": "index.d.ts", "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "scripts": {"perf": "browserify --debug perf/bracket-notation.js > perf/bundle.js && open perf/index.html", "perf-node": "node perf/bracket-notation.js && node perf/concat.js && node perf/copy-big.js && node perf/copy.js && node perf/new-big.js && node perf/new.js && node perf/readDoubleBE.js && node perf/readFloatBE.js && node perf/readUInt32LE.js && node perf/slice.js && node perf/writeFloatBE.js", "size": "browserify -r ./ | uglifyjs -c -m | gzip | wc -c", "test": "standard && node ./bin/test.js", "test-browser-old": "airtap -- test/*.js", "test-browser-old-local": "airtap --local -- test/*.js", "test-browser-new": "airtap -- test/*.js test/node/*.js", "test-browser-new-local": "airtap --local -- test/*.js test/node/*.js", "test-node": "tape test/*.js test/node/*.js", "update-authors": "./bin/update-authors.sh"}, "standard": {"ignore": ["test/node/**/*.js", "test/common.js", "test/_polyfill.js", "perf/**/*.js"]}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "gitHead": "f52dffd9df0445b93c0c9065c2f8f0f46b2c729a", "_id": "buffer@6.0.3", "_nodeVersion": "14.15.1", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==", "shasum": "2ace578459cc8fbe2a70aaa8f52ee63b6a74c6c6", "tarball": "https://registry.npmjs.org/buffer/-/buffer-6.0.3.tgz", "fileCount": 6, "unpackedSize": 91279, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuyOvCRA9TVsSAnZWagAA+CcP/RqdAHf8jHEXiCmx0/BL\nI0TczYC1vCOS9uirWNlNTB1HJuZhYB3mdwhCp6mkZhujukgqrWJvan/mOzym\nSMfMueCnpEZXuGuS6CSMViYdlHX1GdtONvizNBtXhSMYEPbiPEzy7BDfO+M6\nTRXY2TRNIHA/8LsHN37b6m98matc/cdj+sO1EwlVVOQGeqg9dRK83CHWVwf7\nTtdfCRX0qhtzsa13y3OwVWMr0s9rB4OzBExpokob/wveH1OPqaJ4Ov0rKOSG\nYOIzJp4ppPRcmp642aXtjrGQJlBrasyBIb6QwXTvkJp7+WprnVRDYG1dPqVt\nkaSSBJqS6VX8w00nEAt19rg+8CnfLJhWdNDLcz81+fh4joFJcFQ2tbIbmzU1\nZsKJgYS4xSWj4HWtGV4uByEQLoir0F0jx1D7sC+/3gRWTroMevnCxiEqGge/\nhx8bR3pDlpFJ7jLvx2UizuWiDbXBy8uAgftHYngXB3pS4FMEFA9I1piw6+Jf\nA0LVGoTk6JghOy7KVqgN5jXR55v/0LMOUP03Ue7O4jv41BNaSvPPYcpnE5Vc\nK3OuOAUpGwvkKvcJRbXDf9qtG0ofRpGvihydf4gupLRWn80RiqA1dxixPIF0\nHHS5nNAr8oAb15l2/04okMikaS0r5FAgRnZpP2ddAKop3Jwj6GIPggRbrb0O\ntd4m\r\n=imnn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBrz0MIRmBvI5+8i44uTM7EWLfzeMFWNvYNwxq4lhMY+AiApp8F3n4e9AC/A6B/q82Y0Da5x5Xyq016+M71sasdrDQ=="}]}, "_npmUser": {"name": "feross", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/buffer_6.0.3_1606099886902_0.0006190103421770399"}, "_hasShrinkwrap": false}}, "readme": "# buffer [![travis][travis-image]][travis-url] [![npm][npm-image]][npm-url] [![downloads][downloads-image]][downloads-url] [![javascript style guide][standard-image]][standard-url]\n\n[travis-image]: https://img.shields.io/travis/feross/buffer/master.svg\n[travis-url]: https://travis-ci.org/feross/buffer\n[npm-image]: https://img.shields.io/npm/v/buffer.svg\n[npm-url]: https://npmjs.org/package/buffer\n[downloads-image]: https://img.shields.io/npm/dm/buffer.svg\n[downloads-url]: https://npmjs.org/package/buffer\n[standard-image]: https://img.shields.io/badge/code_style-standard-brightgreen.svg\n[standard-url]: https://standardjs.com\n\n#### The buffer module from [node.js](https://nodejs.org/), for the browser.\n\n[![saucelabs][saucelabs-image]][saucelabs-url]\n\n[saucelabs-image]: https://saucelabs.com/browser-matrix/buffer.svg\n[saucelabs-url]: https://saucelabs.com/u/buffer\n\nWith [browserify](http://browserify.org), simply `require('buffer')` or use the `Buffer` global and you will get this module.\n\nThe goal is to provide an API that is 100% identical to\n[node's Buffer API](https://nodejs.org/api/buffer.html). Read the\n[official docs](https://nodejs.org/api/buffer.html) for the full list of properties,\ninstance methods, and class methods that are supported.\n\n## features\n\n- Manipulate binary data like a boss, in all browsers!\n- Super fast. Backed by Typed Arrays (`Uint8Array`/`ArrayBuffer`, not `Object`)\n- Extremely small bundle size (**6.75KB minified + gzipped**, 51.9KB with comments)\n- Excellent browser support (Chrome, Firefox, Edge, Safari 11+, iOS 11+, Android, etc.)\n- Preserves Node API exactly, with one minor difference (see below)\n- Square-bracket `buf[4]` notation works!\n- Does not modify any browser prototypes or put anything on `window`\n- Comprehensive test suite (including all buffer tests from node.js core)\n\n## install\n\nTo use this module directly (without browserify), install it:\n\n```bash\nnpm install buffer\n```\n\nThis module was previously called **native-buffer-browserify**, but please use **buffer**\nfrom now on.\n\nIf you do not use a bundler, you can use the [standalone script](https://bundle.run/buffer).\n\n## usage\n\nThe module's API is identical to node's `Buffer` API. Read the\n[official docs](https://nodejs.org/api/buffer.html) for the full list of properties,\ninstance methods, and class methods that are supported.\n\nAs mentioned above, `require('buffer')` or use the `Buffer` global with\n[browserify](http://browserify.org) and this module will automatically be included\nin your bundle. Almost any npm module will work in the browser, even if it assumes that\nthe node `Buffer` API will be available.\n\nTo depend on this module explicitly (without browserify), require it like this:\n\n```js\nvar Buffer = require('buffer/').Buffer  // note: the trailing slash is important!\n```\n\nTo require this module explicitly, use `require('buffer/')` which tells the node.js module\nlookup algorithm (also used by browserify) to use the **npm module** named `buffer`\ninstead of the **node.js core** module named `buffer`!\n\n\n## how does it work?\n\nThe Buffer constructor returns instances of `Uint8Array` that have their prototype\nchanged to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of `Uint8Array`,\nso the returned instances will have all the node `Buffer` methods and the\n`Uint8Array` methods. Square bracket notation works as expected -- it returns a\nsingle octet.\n\nThe `Uint8Array` prototype remains unmodified.\n\n\n## tracking the latest node api\n\nThis module tracks the Buffer API in the latest (unstable) version of node.js. The Buffer\nAPI is considered **stable** in the\n[node stability index](https://nodejs.org/docs/latest/api/documentation.html#documentation_stability_index),\nso it is unlikely that there will ever be breaking changes.\nNonetheless, when/if the Buffer API changes in node, this module's API will change\naccordingly.\n\n## related packages\n\n- [`buffer-reverse`](https://www.npmjs.com/package/buffer-reverse) - Reverse a buffer\n- [`buffer-xor`](https://www.npmjs.com/package/buffer-xor) - Bitwise xor a buffer\n- [`is-buffer`](https://www.npmjs.com/package/is-buffer) - Determine if an object is a Buffer without including the whole `Buffer` package\n\n## conversion packages\n\n### convert typed array to buffer\n\nUse [`typedarray-to-buffer`](https://www.npmjs.com/package/typedarray-to-buffer) to convert any kind of typed array to a `Buffer`. Does not perform a copy, so it's super fast.\n\n### convert buffer to typed array\n\n`Buffer` is a subclass of `Uint8Array` (which is a typed array). So there is no need to explicitly convert to typed array. Just use the buffer as a `Uint8Array`.\n\n### convert blob to buffer\n\nUse [`blob-to-buffer`](https://www.npmjs.com/package/blob-to-buffer) to convert a `Blob` to a `Buffer`.\n\n### convert buffer to blob\n\nTo convert a `Buffer` to a `Blob`, use the `Blob` constructor:\n\n```js\nvar blob = new Blob([ buffer ])\n```\n\nOptionally, specify a mimetype:\n\n```js\nvar blob = new Blob([ buffer ], { type: 'text/html' })\n```\n\n### convert arraybuffer to buffer\n\nTo convert an `ArrayBuffer` to a `Buffer`, use the `Buffer.from` function. Does not perform a copy, so it's super fast.\n\n```js\nvar buffer = Buffer.from(arrayBuffer)\n```\n\n### convert buffer to arraybuffer\n\nTo convert a `Buffer` to an `ArrayBuffer`, use the `.buffer` property (which is present on all `Uint8Array` objects):\n\n```js\nvar arrayBuffer = buffer.buffer.slice(\n  buffer.byteOffset, buffer.byteOffset + buffer.byteLength\n)\n```\n\nAlternatively, use the [`to-arraybuffer`](https://www.npmjs.com/package/to-arraybuffer) module.\n\n## performance\n\nSee perf tests in `/perf`.\n\n`BrowserBuffer` is the browser `buffer` module (this repo). `Uint8Array` is included as a\nsanity check (since `BrowserBuffer` uses `Uint8Array` under the hood, `Uint8Array` will\nalways be at least a bit faster). Finally, `NodeBuffer` is the node.js buffer module,\nwhich is included to compare against.\n\nNOTE: Performance has improved since these benchmarks were taken. PR welcome to update the README.\n\n### Chrome 38\n\n| Method | Operations | Accuracy | Sampled | Fastest |\n|:-------|:-----------|:---------|:--------|:-------:|\n| BrowserBuffer#bracket-notation | 11,457,464 ops/sec | ±0.86% | 66 | ✓ |\n| Uint8Array#bracket-notation | 10,824,332 ops/sec | ±0.74% | 65 | |\n| | | | |\n| BrowserBuffer#concat | 450,532 ops/sec | ±0.76% | 68 | |\n| Uint8Array#concat | 1,368,911 ops/sec | ±1.50% | 62 | ✓ |\n| | | | |\n| BrowserBuffer#copy(16000) | 903,001 ops/sec | ±0.96% | 67 | |\n| Uint8Array#copy(16000) | 1,422,441 ops/sec | ±1.04% | 66 | ✓ |\n| | | | |\n| BrowserBuffer#copy(16) | 11,431,358 ops/sec | ±0.46% | 69 | |\n| Uint8Array#copy(16) | 13,944,163 ops/sec | ±1.12% | 68 | ✓ |\n| | | | |\n| BrowserBuffer#new(16000) | 106,329 ops/sec | ±6.70% | 44 | |\n| Uint8Array#new(16000) | 131,001 ops/sec | ±2.85% | 31 | ✓ |\n| | | | |\n| BrowserBuffer#new(16) | 1,554,491 ops/sec | ±1.60% | 65 | |\n| Uint8Array#new(16) | 6,623,930 ops/sec | ±1.66% | 65 | ✓ |\n| | | | |\n| BrowserBuffer#readDoubleBE | 112,830 ops/sec | ±0.51% | 69 | ✓ |\n| DataView#getFloat64 | 93,500 ops/sec | ±0.57% | 68 | |\n| | | | |\n| BrowserBuffer#readFloatBE | 146,678 ops/sec | ±0.95% | 68 | ✓ |\n| DataView#getFloat32 | 99,311 ops/sec | ±0.41% | 67 | |\n| | | | |\n| BrowserBuffer#readUInt32LE | 843,214 ops/sec | ±0.70% | 69 | ✓ |\n| DataView#getUint32 | 103,024 ops/sec | ±0.64% | 67 | |\n| | | | |\n| BrowserBuffer#slice | 1,013,941 ops/sec | ±0.75% | 67 | |\n| Uint8Array#subarray | 1,903,928 ops/sec | ±0.53% | 67 | ✓ |\n| | | | |\n| BrowserBuffer#writeFloatBE | 61,387 ops/sec | ±0.90% | 67 | |\n| DataView#setFloat32 | 141,249 ops/sec | ±0.40% | 66 | ✓ |\n\n\n### Firefox 33\n\n| Method | Operations | Accuracy | Sampled | Fastest |\n|:-------|:-----------|:---------|:--------|:-------:|\n| BrowserBuffer#bracket-notation | 20,800,421 ops/sec | ±1.84% | 60 | |\n| Uint8Array#bracket-notation | 20,826,235 ops/sec | ±2.02% | 61 | ✓ |\n| | | | |\n| BrowserBuffer#concat | 153,076 ops/sec | ±2.32% | 61 | |\n| Uint8Array#concat | 1,255,674 ops/sec | ±8.65% | 52 | ✓ |\n| | | | |\n| BrowserBuffer#copy(16000) | 1,105,312 ops/sec | ±1.16% | 63 | |\n| Uint8Array#copy(16000) | 1,615,911 ops/sec | ±0.55% | 66 | ✓ |\n| | | | |\n| BrowserBuffer#copy(16) | 16,357,599 ops/sec | ±0.73% | 68 | |\n| Uint8Array#copy(16) | 31,436,281 ops/sec | ±1.05% | 68 | ✓ |\n| | | | |\n| BrowserBuffer#new(16000) | 52,995 ops/sec | ±6.01% | 35 | |\n| Uint8Array#new(16000) | 87,686 ops/sec | ±5.68% | 45 | ✓ |\n| | | | |\n| BrowserBuffer#new(16) | 252,031 ops/sec | ±1.61% | 66 | |\n| Uint8Array#new(16) | 8,477,026 ops/sec | ±0.49% | 68 | ✓ |\n| | | | |\n| BrowserBuffer#readDoubleBE | 99,871 ops/sec | ±0.41% | 69 | |\n| DataView#getFloat64 | 285,663 ops/sec | ±0.70% | 68 | ✓ |\n| | | | |\n| BrowserBuffer#readFloatBE | 115,540 ops/sec | ±0.42% | 69 | |\n| DataView#getFloat32 | 288,722 ops/sec | ±0.82% | 68 | ✓ |\n| | | | |\n| BrowserBuffer#readUInt32LE | 633,926 ops/sec | ±1.08% | 67 | ✓ |\n| DataView#getUint32 | 294,808 ops/sec | ±0.79% | 64 | |\n| | | | |\n| BrowserBuffer#slice | 349,425 ops/sec | ±0.46% | 69 | |\n| Uint8Array#subarray | 5,965,819 ops/sec | ±0.60% | 65 | ✓ |\n| | | | |\n| BrowserBuffer#writeFloatBE | 59,980 ops/sec | ±0.41% | 67 | |\n| DataView#setFloat32 | 317,634 ops/sec | ±0.63% | 68 | ✓ |\n\n### Safari 8\n\n| Method | Operations | Accuracy | Sampled | Fastest |\n|:-------|:-----------|:---------|:--------|:-------:|\n| BrowserBuffer#bracket-notation | 10,279,729 ops/sec | ±2.25% | 56 | ✓ |\n| Uint8Array#bracket-notation | 10,030,767 ops/sec | ±2.23% | 59 | |\n| | | | |\n| BrowserBuffer#concat | 144,138 ops/sec | ±1.38% | 65 | |\n| Uint8Array#concat | 4,950,764 ops/sec | ±1.70% | 63 | ✓ |\n| | | | |\n| BrowserBuffer#copy(16000) | 1,058,548 ops/sec | ±1.51% | 64 | |\n| Uint8Array#copy(16000) | 1,409,666 ops/sec | ±1.17% | 65 | ✓ |\n| | | | |\n| BrowserBuffer#copy(16) | 6,282,529 ops/sec | ±1.88% | 58 | |\n| Uint8Array#copy(16) | 11,907,128 ops/sec | ±2.87% | 58 | ✓ |\n| | | | |\n| BrowserBuffer#new(16000) | 101,663 ops/sec | ±3.89% | 57 | |\n| Uint8Array#new(16000) | 22,050,818 ops/sec | ±6.51% | 46 | ✓ |\n| | | | |\n| BrowserBuffer#new(16) | 176,072 ops/sec | ±2.13% | 64 | |\n| Uint8Array#new(16) | 24,385,731 ops/sec | ±5.01% | 51 | ✓ |\n| | | | |\n| BrowserBuffer#readDoubleBE | 41,341 ops/sec | ±1.06% | 67 | |\n| DataView#getFloat64 | 322,280 ops/sec | ±0.84% | 68 | ✓ |\n| | | | |\n| BrowserBuffer#readFloatBE | 46,141 ops/sec | ±1.06% | 65 | |\n| DataView#getFloat32 | 337,025 ops/sec | ±0.43% | 69 | ✓ |\n| | | | |\n| BrowserBuffer#readUInt32LE | 151,551 ops/sec | ±1.02% | 66 | |\n| DataView#getUint32 | 308,278 ops/sec | ±0.94% | 67 | ✓ |\n| | | | |\n| BrowserBuffer#slice | 197,365 ops/sec | ±0.95% | 66 | |\n| Uint8Array#subarray | 9,558,024 ops/sec | ±3.08% | 58 | ✓ |\n| | | | |\n| BrowserBuffer#writeFloatBE | 17,518 ops/sec | ±1.03% | 63 | |\n| DataView#setFloat32 | 319,751 ops/sec | ±0.48% | 68 | ✓ |\n\n\n### Node 0.11.14\n\n| Method | Operations | Accuracy | Sampled | Fastest |\n|:-------|:-----------|:---------|:--------|:-------:|\n| BrowserBuffer#bracket-notation | 10,489,828 ops/sec | ±3.25% | 90 | |\n| Uint8Array#bracket-notation | 10,534,884 ops/sec | ±0.81% | 92 | ✓ |\n| NodeBuffer#bracket-notation | 10,389,910 ops/sec | ±0.97% | 87 | |\n| | | | |\n| BrowserBuffer#concat | 487,830 ops/sec | ±2.58% | 88 | |\n| Uint8Array#concat | 1,814,327 ops/sec | ±1.28% | 88 | ✓ |\n| NodeBuffer#concat | 1,636,523 ops/sec | ±1.88% | 73 | |\n| | | | |\n| BrowserBuffer#copy(16000) | 1,073,665 ops/sec | ±0.77% | 90 | |\n| Uint8Array#copy(16000) | 1,348,517 ops/sec | ±0.84% | 89 | ✓ |\n| NodeBuffer#copy(16000) | 1,289,533 ops/sec | ±0.82% | 93 | |\n| | | | |\n| BrowserBuffer#copy(16) | 12,782,706 ops/sec | ±0.74% | 85 | |\n| Uint8Array#copy(16) | 14,180,427 ops/sec | ±0.93% | 92 | ✓ |\n| NodeBuffer#copy(16) | 11,083,134 ops/sec | ±1.06% | 89 | |\n| | | | |\n| BrowserBuffer#new(16000) | 141,678 ops/sec | ±3.30% | 67 | |\n| Uint8Array#new(16000) | 161,491 ops/sec | ±2.96% | 60 | |\n| NodeBuffer#new(16000) | 292,699 ops/sec | ±3.20% | 55 | ✓ |\n| | | | |\n| BrowserBuffer#new(16) | 1,655,466 ops/sec | ±2.41% | 82 | |\n| Uint8Array#new(16) | 14,399,926 ops/sec | ±0.91% | 94 | ✓ |\n| NodeBuffer#new(16) | 3,894,696 ops/sec | ±0.88% | 92 | |\n| | | | |\n| BrowserBuffer#readDoubleBE | 109,582 ops/sec | ±0.75% | 93 | ✓ |\n| DataView#getFloat64 | 91,235 ops/sec | ±0.81% | 90 | |\n| NodeBuffer#readDoubleBE | 88,593 ops/sec | ±0.96% | 81 | |\n| | | | |\n| BrowserBuffer#readFloatBE | 139,854 ops/sec | ±1.03% | 85 | ✓ |\n| DataView#getFloat32 | 98,744 ops/sec | ±0.80% | 89 | |\n| NodeBuffer#readFloatBE | 92,769 ops/sec | ±0.94% | 93 | |\n| | | | |\n| BrowserBuffer#readUInt32LE | 710,861 ops/sec | ±0.82% | 92 | |\n| DataView#getUint32 | 117,893 ops/sec | ±0.84% | 91 | |\n| NodeBuffer#readUInt32LE | 851,412 ops/sec | ±0.72% | 93 | ✓ |\n| | | | |\n| BrowserBuffer#slice | 1,673,877 ops/sec | ±0.73% | 94 | |\n| Uint8Array#subarray | 6,919,243 ops/sec | ±0.67% | 90 | ✓ |\n| NodeBuffer#slice | 4,617,604 ops/sec | ±0.79% | 93 | |\n| | | | |\n| BrowserBuffer#writeFloatBE | 66,011 ops/sec | ±0.75% | 93 | |\n| DataView#setFloat32 | 127,760 ops/sec | ±0.72% | 93 | ✓ |\n| NodeBuffer#writeFloatBE | 103,352 ops/sec | ±0.83% | 93 | |\n\n### iojs 1.8.1\n\n| Method | Operations | Accuracy | Sampled | Fastest |\n|:-------|:-----------|:---------|:--------|:-------:|\n| BrowserBuffer#bracket-notation | 10,990,488 ops/sec | ±1.11% | 91 | |\n| Uint8Array#bracket-notation | 11,268,757 ops/sec | ±0.65% | 97 | |\n| NodeBuffer#bracket-notation | 11,353,260 ops/sec | ±0.83% | 94 | ✓ |\n| | | | |\n| BrowserBuffer#concat | 378,954 ops/sec | ±0.74% | 94 | |\n| Uint8Array#concat | 1,358,288 ops/sec | ±0.97% | 87 | |\n| NodeBuffer#concat | 1,934,050 ops/sec | ±1.11% | 78 | ✓ |\n| | | | |\n| BrowserBuffer#copy(16000) | 894,538 ops/sec | ±0.56% | 84 | |\n| Uint8Array#copy(16000) | 1,442,656 ops/sec | ±0.71% | 96 | |\n| NodeBuffer#copy(16000) | 1,457,898 ops/sec | ±0.53% | 92 | ✓ |\n| | | | |\n| BrowserBuffer#copy(16) | 12,870,457 ops/sec | ±0.67% | 95 | |\n| Uint8Array#copy(16) | 16,643,989 ops/sec | ±0.61% | 93 | ✓ |\n| NodeBuffer#copy(16) | 14,885,848 ops/sec | ±0.74% | 94 | |\n| | | | |\n| BrowserBuffer#new(16000) | 109,264 ops/sec | ±4.21% | 63 | |\n| Uint8Array#new(16000) | 138,916 ops/sec | ±1.87% | 61 | |\n| NodeBuffer#new(16000) | 281,449 ops/sec | ±3.58% | 51 | ✓ |\n| | | | |\n| BrowserBuffer#new(16) | 1,362,935 ops/sec | ±0.56% | 99 | |\n| Uint8Array#new(16) | 6,193,090 ops/sec | ±0.64% | 95 | ✓ |\n| NodeBuffer#new(16) | 4,745,425 ops/sec | ±1.56% | 90 | |\n| | | | |\n| BrowserBuffer#readDoubleBE | 118,127 ops/sec | ±0.59% | 93 | ✓ |\n| DataView#getFloat64 | 107,332 ops/sec | ±0.65% | 91 | |\n| NodeBuffer#readDoubleBE | 116,274 ops/sec | ±0.94% | 95 | |\n| | | | |\n| BrowserBuffer#readFloatBE | 150,326 ops/sec | ±0.58% | 95 | ✓ |\n| DataView#getFloat32 | 110,541 ops/sec | ±0.57% | 98 | |\n| NodeBuffer#readFloatBE | 121,599 ops/sec | ±0.60% | 87 | |\n| | | | |\n| BrowserBuffer#readUInt32LE | 814,147 ops/sec | ±0.62% | 93 | |\n| DataView#getUint32 | 137,592 ops/sec | ±0.64% | 90 | |\n| NodeBuffer#readUInt32LE | 931,650 ops/sec | ±0.71% | 96 | ✓ |\n| | | | |\n| BrowserBuffer#slice | 878,590 ops/sec | ±0.68% | 93 | |\n| Uint8Array#subarray | 2,843,308 ops/sec | ±1.02% | 90 | |\n| NodeBuffer#slice | 4,998,316 ops/sec | ±0.68% | 90 | ✓ |\n| | | | |\n| BrowserBuffer#writeFloatBE | 65,927 ops/sec | ±0.74% | 93 | |\n| DataView#setFloat32 | 139,823 ops/sec | ±0.97% | 89 | ✓ |\n| NodeBuffer#writeFloatBE | 135,763 ops/sec | ±0.65% | 96 | |\n| | | | |\n\n## Testing the project\n\nFirst, install the project:\n\n    npm install\n\nThen, to run tests in Node.js, run:\n\n    npm run test-node\n\nTo test locally in a browser, you can run:\n\n    npm run test-browser-es5-local # For ES5 browsers that don't support ES6\n    npm run test-browser-es6-local # For ES6 compliant browsers\n\nThis will print out a URL that you can then open in a browser to run the tests, using [airtap](https://www.npmjs.com/package/airtap).\n\nTo run automated browser tests using Saucelabs, ensure that your `SAUCE_USERNAME` and `SAUCE_ACCESS_KEY` environment variables are set, then run:\n\n    npm test\n\nThis is what's run in Travis, to check against various browsers. The list of browsers is kept in the `bin/airtap-es5.yml` and `bin/airtap-es6.yml` files.\n\n## JavaScript Standard Style\n\nThis module uses [JavaScript Standard Style](https://github.com/feross/standard).\n\n[![JavaScript Style Guide](https://cdn.rawgit.com/feross/standard/master/badge.svg)](https://github.com/feross/standard)\n\nTo test that the code conforms to the style, `npm install` and run:\n\n    ./node_modules/.bin/standard\n\n## credit\n\nThis was originally forked from [buffer-browserify](https://github.com/toots/buffer-browserify).\n\n## Security Policies and Procedures\n\nThe `buffer` team and community take all security bugs in `buffer` seriously. Please see our [security policies and procedures](https://github.com/feross/security) document to learn how to report issues.\n\n## license\n\nMIT. Copyright (C) [Feross Aboukhadijeh](http://feross.org), and other contributors. Originally forked from an MIT-licensed module by Romain Beauxis.\n", "maintainers": [{"name": "feross", "email": "<EMAIL>"}], "time": {"modified": "2023-07-22T20:56:41.505Z", "created": "2014-02-07T03:37:21.186Z", "2.1.1": "2014-02-07T03:37:21.186Z", "2.1.2": "2014-02-07T05:28:27.645Z", "2.1.3": "2014-02-07T05:55:42.114Z", "2.1.4": "2014-02-16T02:56:20.611Z", "2.1.5": "2014-03-07T06:11:30.082Z", "2.1.6": "2014-04-05T18:54:07.959Z", "2.1.7": "2014-04-05T18:55:05.902Z", "2.1.8": "2014-04-08T07:38:41.091Z", "2.1.9": "2014-04-08T08:00:27.847Z", "2.1.10": "2014-04-08T09:03:19.837Z", "2.1.11": "2014-04-10T03:58:22.929Z", "2.1.12": "2014-04-10T09:29:30.226Z", "2.1.13": "2014-04-18T04:28:13.958Z", "2.2.0": "2014-05-08T00:07:26.982Z", "2.3.0": "2014-05-08T00:24:52.527Z", "2.3.1": "2014-05-27T01:45:04.094Z", "2.3.2": "2014-05-30T18:43:18.527Z", "2.3.3": "2014-06-30T06:42:56.917Z", "2.3.4": "2014-07-17T05:25:21.687Z", "2.4.0": "2014-07-17T06:03:58.447Z", "2.5.0": "2014-07-22T08:47:44.782Z", "2.5.1": "2014-09-11T19:46:34.086Z", "2.6.0": "2014-09-12T00:16:46.979Z", "2.6.1": "2014-09-12T00:37:03.251Z", "2.6.2": "2014-09-12T00:51:56.485Z", "2.7.0": "2014-09-12T11:16:57.773Z", "2.8.0": "2014-10-29T04:44:59.979Z", "2.8.1": "2014-10-31T03:43:56.161Z", "2.8.2": "2014-12-07T20:18:22.355Z", "3.0.0": "2014-12-24T10:27:31.308Z", "3.0.1": "2014-12-31T06:46:01.572Z", "3.0.2": "2015-02-11T23:03:49.996Z", "3.0.3": "2015-02-19T03:13:37.092Z", "3.1.0": "2015-03-09T22:40:27.507Z", "3.1.1": "2015-03-11T23:57:28.729Z", "3.1.2": "2015-03-20T23:34:08.534Z", "3.2.0": "2015-04-21T10:37:25.517Z", "3.2.1": "2015-04-23T00:08:55.786Z", "3.2.2": "2015-05-05T02:09:44.928Z", "3.3.0": "2015-06-30T23:36:17.170Z", "3.3.1": "2015-07-07T19:58:25.131Z", "3.3.2": "2015-08-04T13:59:16.564Z", "3.4.0": "2015-08-05T12:49:17.977Z", "3.4.1": "2015-08-06T12:26:28.753Z", "3.4.2": "2015-08-13T11:17:25.638Z", "3.4.3": "2015-08-22T13:07:55.979Z", "3.5.0": "2015-09-16T06:31:16.526Z", "3.5.1": "2015-10-07T02:30:54.211Z", "3.5.2": "2015-11-14T07:13:16.682Z", "3.5.3": "2015-12-01T00:43:07.592Z", "3.5.4": "2015-12-06T03:01:41.615Z", "3.5.5": "2015-12-11T11:22:05.267Z", "3.6.0": "2015-12-25T06:38:12.168Z", "4.0.0": "2016-01-02T23:19:46.748Z", "4.1.0": "2016-01-09T17:25:14.745Z", "4.2.0": "2016-01-11T15:02:21.180Z", "4.3.0": "2016-01-12T22:59:30.743Z", "4.3.1": "2016-01-27T14:41:44.278Z", "4.4.0": "2016-01-28T22:34:04.751Z", "4.5.0": "2016-02-16T07:39:49.767Z", "4.5.1": "2016-03-24T03:01:38.458Z", "4.6.0": "2016-04-20T06:36:41.602Z", "4.7.0": "2016-06-24T01:35:17.067Z", "4.7.1": "2016-07-15T07:06:11.537Z", "4.8.0": "2016-08-08T08:28:59.948Z", "4.9.0": "2016-08-08T09:01:53.151Z", "4.9.1": "2016-08-18T03:46:39.280Z", "5.0.0": "2016-09-27T02:38:39.348Z", "5.0.1": "2016-11-07T08:14:03.098Z", "5.0.2": "2016-12-02T23:10:11.301Z", "5.0.3": "2017-02-02T21:41:03.760Z", "5.0.4": "2017-02-09T05:14:13.118Z", "5.0.5": "2017-02-09T22:11:39.215Z", "5.0.6": "2017-04-05T19:05:26.129Z", "5.0.7": "2017-08-05T01:45:34.970Z", "5.0.8": "2017-09-29T23:48:48.079Z", "5.1.0": "2018-02-16T07:16:40.013Z", "5.2.0": "2018-07-27T07:16:54.371Z", "5.2.1": "2018-08-30T21:45:22.928Z", "5.3.0": "2019-08-12T02:14:12.056Z", "5.4.0": "2019-08-13T20:24:00.050Z", "5.4.1": "2019-08-27T21:35:23.713Z", "5.4.2": "2019-08-27T21:49:04.642Z", "5.4.3": "2019-09-12T02:55:27.156Z", "4.9.2": "2019-11-08T23:47:16.446Z", "3.6.2": "2019-11-08T23:48:20.601Z", "2.8.3": "2019-11-08T23:50:54.246Z", "5.5.0": "2020-03-06T19:55:41.858Z", "5.6.0": "2020-04-15T00:02:26.412Z", "5.6.1": "2020-10-23T20:46:06.127Z", "5.7.0": "2020-10-27T21:58:01.617Z", "6.0.0": "2020-11-04T09:28:19.229Z", "5.7.1": "2020-11-04T21:51:35.578Z", "6.0.1": "2020-11-04T21:58:51.060Z", "6.0.2": "2020-11-09T22:04:39.557Z", "6.0.3": "2020-11-23T02:51:27.107Z"}, "readmeFilename": "README.md", "users": {"holgerkoser": true, "guybrush": true, "bmpvieira": true, "simplyianm": true, "qodefox": true, "vbv": true, "pandao": true, "po": true, "timdp": true, "mfellner": true, "demopark": true, "gamr": true, "hr.": true, "arssly": true, "roxnz": true, "tdmalone": true, "feross": true, "shanewholloway": true, "monjer": true, "xinwangwang": true, "thejeshgn.com": true, "thejeshgn": true, "chinawolf_wyp": true, "pid": true, "kiaratto": true, "sunshine1988": true, "solenw_in": true, "hualei": true, "tcrowe": true, "flumpus-dev": true}, "homepage": "https://github.com/feross/buffer", "keywords": ["arraybuffer", "browser", "browserify", "buffer", "compatible", "dataview", "uint8array"], "repository": {"type": "git", "url": "git://github.com/feross/buffer.git"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "bugs": {"url": "https://github.com/feross/buffer/issues"}, "license": "MIT"}