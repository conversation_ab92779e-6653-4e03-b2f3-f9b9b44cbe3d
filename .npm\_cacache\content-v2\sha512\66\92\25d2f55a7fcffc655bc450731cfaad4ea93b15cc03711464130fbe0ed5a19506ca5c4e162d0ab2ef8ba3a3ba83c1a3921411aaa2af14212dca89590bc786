{"_id": "bl", "_rev": "177-9c3ad5eadf1efd8352c09ae19f232c6f", "name": "bl", "dist-tags": {"v0_9_x": "0.9.5", "stable-3x": "3.0.1", "stable-2x": "2.2.1", "latest": "6.1.0"}, "versions": {"0.0.0": {"name": "bl", "version": "0.0.0", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@0.0.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "ff9976e3a0f612ab25fa189e7718316bfd736ded", "tarball": "https://registry.npmjs.org/bl/-/bl-0.0.0.tgz", "integrity": "sha512-ENTu6h2yDyVdiYZBhJdsz2YrJIGpEbj6DfqaXyuXLE1gdsbGjM5mAmtGhpv4MeG5j1yJF9oM4wZX7mfKqDe4ew==", "signatures": [{"sig": "MEUCIBkqtNll1CUbPhwOtPyuUqvV/KpGkKPCib5ctFRt9zgfAiEAmqsu2fAtdEVbjUC0I72fx+o623a0Y08n0VPVfIZ4qDQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)"], "scripts": {"test": "tape test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "1.2.30", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "dependencies": {"readable-stream": "~1.0.2"}, "devDependencies": {"tape": "*"}}, "0.1.0": {"name": "bl", "version": "0.1.0", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@0.1.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "4109c45afeda64238c29a9771f2343d72c394755", "tarball": "https://registry.npmjs.org/bl/-/bl-0.1.0.tgz", "integrity": "sha512-D98zVXd5icwGH6id99eearlLzu9It/fn6jH+o0W0PuQCjXmIX71VJjIN1WTz1Wr0Dn6VwGfemfN0Xsc6nIQWHQ==", "signatures": [{"sig": "MEQCIFdh2eGvU4KAQUlr+0tQGlinp//dackb9nuh2l+50kTEAiAlWks/uIT7xXz4IpQPf7ccHJlG6E8gOCvjnyQL7XrR6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)"], "scripts": {"test": "tape test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "1.2.30", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "dependencies": {"readable-stream": "~1.0.2"}, "devDependencies": {"tape": "*"}}, "0.1.1": {"name": "bl", "version": "0.1.1", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@0.1.1", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "97301af51723a061ecf9a5017c22165eb40504c9", "tarball": "https://registry.npmjs.org/bl/-/bl-0.1.1.tgz", "integrity": "sha512-9Kli8AAh6g/9eCMYZbzLw/CfUw12nzMYGq4YtuDpCFna1H+pedFR9a0F6SnZq74xVV2KGKuTWkhq8EiXxxMV9w==", "signatures": [{"sig": "MEUCIQCEm4fD+Uc0jKlEdLG9ENX345dzoeM9IOftcZhfg4vZzAIgeaePmnodHJhE/O1nqMEPGn0vXDs4ati4qOYlICT6EWA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)"], "scripts": {"test": "tape test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "1.2.30", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "dependencies": {"readable-stream": "~1.0.2"}, "devDependencies": {"tape": "*"}}, "0.2.0": {"name": "bl", "version": "0.2.0", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@0.2.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "dfc5bedd197b0b7fa6fdb54cc0c6fe30ea53865d", "tarball": "https://registry.npmjs.org/bl/-/bl-0.2.0.tgz", "integrity": "sha512-V16DaqoOidBjl1DM1z7G6BTRpMdhlHAVW7/emJVl0HZ5wl2QIwGkfo0p7CjrJABtvEW/WbKUul3+PpJ3lz44Xg==", "signatures": [{"sig": "MEUCIQCICUroc/5IgNLv/ecUqyJv5hsqNZH+HYCqzIWrSsSMVwIgOulRaAVvKuRnoVnfDwoBS5BMHvL6NzIVlt8tUuSY9EA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)"], "scripts": {"test": "tape test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "1.3.2", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "dependencies": {"readable-stream": "~1.0.2"}, "devDependencies": {"tape": "*", "hash_file": "*"}}, "0.3.0": {"name": "bl", "version": "0.3.0", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@0.3.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "6e9c33e6f41f6887b5d2604ce13ef55e3a70c08b", "tarball": "https://registry.npmjs.org/bl/-/bl-0.3.0.tgz", "integrity": "sha512-Lf3rzPTFnWMtVYdiK+tBh0YT7hX+jPBqQcEmg2S3x2QHQFGAxwLnBJbdLdA5+BpDKK5s6ZoHlhIwwBxxfXOn8g==", "signatures": [{"sig": "MEUCIQDzmX0gXlqwiVn0U4oUpwcN4Pomr6qwm51FVGAvQe/12gIgdaUJeXibbmP4WrT8LCd84onYCKCLbnnSOp3PtWIQCTQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)"], "scripts": {"test": "tape test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "1.3.5", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "dependencies": {"readable-stream": "~1.0.2"}, "devDependencies": {"tape": "*", "hash_file": "*"}}, "0.4.0": {"name": "bl", "version": "0.4.0", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@0.4.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "6064458cbf0905a1c8affabbc4d19bcfd67da6f4", "tarball": "https://registry.npmjs.org/bl/-/bl-0.4.0.tgz", "integrity": "sha512-n0PePCCt3kLecyvKQYTKMeQVeoRph+oAb2YOBZ6CxWgzizMC16HMk4j06k5kaRx4UICx24TquD84xu7gSrWwbA==", "signatures": [{"sig": "MEQCIF9TX/L9yIKAXSKkMbIi8pDDz1uXR6rgCPeuzwlQrbpUAiA+dMVqUgR9gUmn5bqx1OhWz1YshlWFrroHlZxSeAPAGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)"], "scripts": {"test": "tape test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "1.3.5", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "dependencies": {"readable-stream": "~1.0.2"}, "devDependencies": {"tape": "*", "hash_file": "*"}}, "0.4.1": {"name": "bl", "version": "0.4.1", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@0.4.1", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "56c68b83017927ecadee12919e4cf0c745972ab3", "tarball": "https://registry.npmjs.org/bl/-/bl-0.4.1.tgz", "integrity": "sha512-jDVz3XTiMf3gU1pU9Vp5ulqcMEVKSKXeLR91seMScfXL1+mXwZtfhtdssJcoSmS43+P9Izdt7w1g8DnHx48uLw==", "signatures": [{"sig": "MEYCIQCSOmWOJn8spdjPP5Mlokd4I4mhy7n0bpeoAcXtKz7K1AIhAJeeike79muEcClRpoL+6hbQgJ1i2WdcnpB+J7CGV0FT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)"], "scripts": {"test": "tape test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "1.3.5", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "dependencies": {"readable-stream": "~1.0.2"}, "devDependencies": {"tape": "*", "hash_file": "*"}}, "0.4.2": {"name": "bl", "version": "0.4.2", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@0.4.2", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "5db31d72f038c54e68adc39578125fe3b0addc96", "tarball": "https://registry.npmjs.org/bl/-/bl-0.4.2.tgz", "integrity": "sha512-/Jhsskdr/kVmqiA+zn6A1h0Z9pRLXrx/yxOlhKVgwaiCMtb+/UhZOHlefRAqArQVRRuOxOu+MvzQh/yIvFMZlQ==", "signatures": [{"sig": "MEYCIQDqN8nnhJ+ryoS9QigTfJ6f90WCbMyjgeup8KcZl4Mv3wIhANfc3vQG8ERQPGJa5ah8EtqX2tUDe0EVCdeLjT3yIc97", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)"], "scripts": {"test": "tape test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "1.3.8", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "dependencies": {"readable-stream": "~1.0.2"}, "devDependencies": {"tape": "*", "hash_file": "*"}}, "0.5.0": {"name": "bl", "version": "0.5.0", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@0.5.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "11ffd01650412b31aa6fa9878ffd3d4fa77a7185", "tarball": "https://registry.npmjs.org/bl/-/bl-0.5.0.tgz", "integrity": "sha512-0qrVJn4c8L4+0Kf+tLLmTnc9Dyfxt1jgBkuV9fyI80hAKPacp+qQDT/0PSadnxJAa5DTcHnrVuUWy9tPRxz20Q==", "signatures": [{"sig": "MEUCIQDzO14Z/4dd+QRBMr1fcXSUtNEr3mc5SJj8+fGCHsSYrQIgUx2E2u5y+APstu3N0AaNQ2SwtKb+X9HxfiLZl3nsDcg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)"], "scripts": {"test": "tape test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "1.3.11", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "dependencies": {"readable-stream": "~1.0.2"}, "devDependencies": {"tape": "*", "hash_file": "*"}}, "0.6.0": {"name": "bl", "version": "0.6.0", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@0.6.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "30910299937294133844ee34aa479a5344b4cd29", "tarball": "https://registry.npmjs.org/bl/-/bl-0.6.0.tgz", "integrity": "sha512-98ke4XLV+p91yckeYH83KM79s7La2KH5wulqGNh+emmzpUp4mIwm/WRM2AMmaxYyW+wLkknmFw9t0liDYPrzdw==", "signatures": [{"sig": "MEQCICXCj6TRRdaLC6rmw2e/CZD/ZIngXfxuNJrAeEuTDGVfAiAC2ZqrFtd1KSaS1jlZcWiaPvuWZBcuwMKcQGmJ0kQwcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)"], "scripts": {"test": "tape test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "1.3.14", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "dependencies": {"readable-stream": "~1.0.2"}, "devDependencies": {"tape": "*", "hash_file": "*"}}, "0.7.0": {"name": "bl", "version": "0.7.0", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@0.7.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "3fb0670602ac2878eb770dc2039f1836be62ae5b", "tarball": "https://registry.npmjs.org/bl/-/bl-0.7.0.tgz", "integrity": "sha512-r0xvhvr9uH9VZ4s7lL/NNk/AJzTbSnC1V8LTAU/0Mrsb6brr3BcjIyothOdQUt2yJ+BBCkkwMCZaUHLTWM+IbA==", "signatures": [{"sig": "MEUCIA/1M8vkrxbu4X51usId1wxdS6c4Oe+M0H1qE7WrkVn7AiEAudmGC161SoDWRyZKyU1Q6QwZt6zsR0S1RG6eqCTzZHg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)"], "browser": {"readable-stream": "stream-browserify"}, "scripts": {"test": "node test/test.js | faucet", "test-local": "brtapsauce-local test/basic-test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "1.3.21", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "dependencies": {"readable-stream": "~1.0.2"}, "devDependencies": {"tape": "~2.3.2", "faucet": "0.0.0", "hash_file": "~0.1.1", "brtapsauce": "~0.3.0", "stream-browserify": "~0.1.2"}, "peerDependencies": {"stream-browserify": "*"}}, "0.8.0": {"name": "bl", "version": "0.8.0", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@0.8.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "378b1c638b5ea57467e57c4f33a36ff6761f18fa", "tarball": "https://registry.npmjs.org/bl/-/bl-0.8.0.tgz", "integrity": "sha512-v+nZaXJ+sLdU0TXOd5dgwYXFIM4SzvAMjzeOp08Ez7u1W4RfvI7rVr5kZc4rFdcfHS0agt/36RbHEiaPvijXNw==", "signatures": [{"sig": "MEUCIHErhbLe8RUeGRhXChrpeJbjKveQsY9KzfcsVPNgDD4wAiEA8Gr1AxpRUcXwgHDQoE3zgwulmKz3O9oR1Ms0YplBVBA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)"], "browser": {"readable-stream": "stream-browserify"}, "scripts": {"test": "node test/test.js | faucet", "test-local": "brtapsauce-local test/basic-test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "1.4.3", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "dependencies": {"readable-stream": "~1.0.26"}, "devDependencies": {"tape": "~2.12.3", "faucet": "~0.0.1", "hash_file": "~0.1.1", "brtapsauce": "~0.3.0"}}, "0.8.1": {"name": "bl", "version": "0.8.1", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@0.8.1", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "3b478a0874c3357e3a6a8559419abd93b92abde0", "tarball": "https://registry.npmjs.org/bl/-/bl-0.8.1.tgz", "integrity": "sha512-d2F5qfJboeddq5mFgOf+FlXolSkfa/L1QpTWQ2RIniBJ/SfnsIgXKitOw0rsybm85qF5jFHSfuAvEe8tQIROjQ==", "signatures": [{"sig": "MEUCIQCOwEgFoiKQ4MLZkKG3PMD4bu5o3diQgz9mlc7LW0usMQIgbJmaIB2id5mduKO55YDgeXIXIf8HRcVD76gFweG0s4Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "_shasum": "3b478a0874c3357e3a6a8559419abd93b92abde0", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)"], "browser": {"readable-stream": "stream-browserify"}, "gitHead": "ee75b2be282d218be1139f4999f446e27a2647ac", "scripts": {"test": "node test/test.js | faucet", "test-local": "brtapsauce-local test/basic-test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "dependencies": {"readable-stream": "~1.0.26", "stream-browserify": "~1.0.0"}, "devDependencies": {"tape": "~2.12.3", "faucet": "~0.0.1", "hash_file": "~0.1.1", "brtapsauce": "~0.3.0"}}, "0.8.2": {"name": "bl", "version": "0.8.2", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@0.8.2", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "c9b6bca08d1bc2ea00fc8afb4f1a5fd1e1c66e4e", "tarball": "https://registry.npmjs.org/bl/-/bl-0.8.2.tgz", "integrity": "sha512-pfqikmByp+lifZCS0p6j6KreV6kNU6Apzpm2nKOk+94cZb/jvle55+JxWiByUQ0Wo/+XnDXEy5MxxKMb6r0VIw==", "signatures": [{"sig": "MEUCIQDBxmu7NrQyIOCWeoarlmUEQJBQsHNGQJCBfUy8L0KvVwIgU4cO96qEzKWZT6XgxaWAu/2OYJfauvzhbmfiSupIxaE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "_shasum": "c9b6bca08d1bc2ea00fc8afb4f1a5fd1e1c66e4e", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)"], "gitHead": "1b67e141ebf47eb0c2ace434bd510286842d9959", "scripts": {"test": "node test/test.js | faucet", "test-local": "brtapsauce-local test/basic-test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "dependencies": {"readable-stream": "~1.0.26"}, "devDependencies": {"tape": "~2.12.3", "faucet": "~0.0.1", "hash_file": "~0.1.1", "brtapsauce": "~0.3.0"}}, "0.9.0": {"name": "bl", "version": "0.9.0", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@0.9.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "ab5ffaeccb30acd3da0af0c8df63b904990a76dd", "tarball": "https://registry.npmjs.org/bl/-/bl-0.9.0.tgz", "integrity": "sha512-9jRr8pH8kYgAogEDndpsBm/nywgmrXseeX1+xOu4ixIl7ZB/E606qQr6D8ulaVgl7asqLFN68LxbVyhcJa2mhQ==", "signatures": [{"sig": "MEUCIQDe0Q7DxPvxAzcPL7G9RgxNL5h/KFkRvn1aE2uI9B67AgIgJ02QpeXlYvsiUv8UFfAWpzXe+J9DXUICT2z/x1zSz0U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "_shasum": "ab5ffaeccb30acd3da0af0c8df63b904990a76dd", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)"], "gitHead": "6b5296f09f8a781bdf90f6cf4140a553c0aab5e6", "scripts": {"test": "node test/test.js | faucet", "test-local": "brtapsauce-local test/basic-test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "1.4.14", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "dependencies": {"readable-stream": "~1.0.26"}, "devDependencies": {"tape": "~2.12.3", "faucet": "~0.0.1", "hash_file": "~0.1.1", "brtapsauce": "~0.3.0"}}, "0.9.1": {"name": "bl", "version": "0.9.1", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@0.9.1", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "d262c5b83aa5cf4386cea1d998c82b36d7ae2942", "tarball": "https://registry.npmjs.org/bl/-/bl-0.9.1.tgz", "integrity": "sha512-2AGS64+2DXLZ2HmuB1zTLvb13CLLm4Eoz3Wkg+osEHVSRFkepXzkw3W01K/Ib/tCBuA0YuVFqr4veWI7TuXXLQ==", "signatures": [{"sig": "MEUCIQDd8pZS/A6QhggjojnwRNrtSM7xrOOojZVutowTNURJKQIgLwtFvFcQZHWarVB0q4loPm9SUIt3X+jI/BLnVZOCqB4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "_shasum": "d262c5b83aa5cf4386cea1d998c82b36d7ae2942", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)"], "gitHead": "53d3d10e39be326feb049ab27437173b3ce47ec4", "scripts": {"test": "node test/test.js | faucet", "test-local": "brtapsauce-local test/basic-test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "1.4.21", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "dependencies": {"readable-stream": "~1.0.26"}, "devDependencies": {"tape": "~2.12.3", "faucet": "~0.0.1", "hash_file": "~0.1.1", "brtapsauce": "~0.3.0"}}, "0.9.2": {"name": "bl", "version": "0.9.2", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@0.9.2", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "0233b18ab6d83e370f274d67c25cb4cc58c6a885", "tarball": "https://registry.npmjs.org/bl/-/bl-0.9.2.tgz", "integrity": "sha512-+YdZx2gfod0JNZC/dcLyCxfBOaU5nCrr8qrRfkv2K5X3FE8QnCM30VGjF8F6vZL/fBcFHuahzHN77C7DfIaU1w==", "signatures": [{"sig": "MEUCIDsrs85b9Jl/JgUwx5Ivfu7IVHYgeiLTWb3jx1pIsoWkAiEA+rSoLie9Z1ICADFKOnEtuXSiD9jIjAgxc5ZG7kjDgm0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "_shasum": "0233b18ab6d83e370f274d67c25cb4cc58c6a885", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)"], "gitHead": "58699b7ea3823c9cff418e0e8d2d132c9efe9ec9", "scripts": {"test": "node test/test.js | faucet", "test-local": "brtapsauce-local test/basic-test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "1.4.27", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "dependencies": {"readable-stream": "~1.0.26"}, "devDependencies": {"tape": "~2.12.3", "faucet": "~0.0.1", "hash_file": "~0.1.1", "brtapsauce": "~0.3.0"}}, "0.9.3": {"name": "bl", "version": "0.9.3", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@0.9.3", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "c41eff3e7cb31bde107c8f10076d274eff7f7d44", "tarball": "https://registry.npmjs.org/bl/-/bl-0.9.3.tgz", "integrity": "sha512-/aV00jkx8NuUXDfelGoZTD3F4/RpVoIfeKxVT/dW0J3rQ0JbBARJBV2hzg2JEGTjLJORIRUpLubPve53l7smYA==", "signatures": [{"sig": "MEUCIACjhEupA5LTySQx/5BIi8vlEVCpksuX5iH6sOqQ+HQmAiEA3miTGe2n+2Y41OQC+LXZZCEHvxRtNXEW2qNINNexVIE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "_shasum": "c41eff3e7cb31bde107c8f10076d274eff7f7d44", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "4987a76bf6bafd7616e62c7023c955e62f3a9461", "scripts": {"test": "node test/test.js | faucet", "test-local": "brtapsauce-local test/basic-test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "1.4.27", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "dependencies": {"readable-stream": "~1.0.26"}, "devDependencies": {"tape": "~2.12.3", "faucet": "~0.0.1", "hash_file": "~0.1.1", "brtapsauce": "~0.3.0"}}, "0.9.4": {"name": "bl", "version": "0.9.4", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@0.9.4", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "4702ddf72fbe0ecd82787c00c113aea1935ad0e7", "tarball": "https://registry.npmjs.org/bl/-/bl-0.9.4.tgz", "integrity": "sha512-4isX0sidWA1yZWImUEMQBsxHHsI5bsgG7QbBpcWckROSJmTxLCdW9wJXAGsEPLO+kV9J8SAaoecVqcBPyRKPrQ==", "signatures": [{"sig": "MEYCIQDpH8HC+T15sCb8DGLRY2ZWUVBuNQxsMwG+EBAiXqoY3QIhAJtHancHLx7fGgoeq4PQxSkZo728xk+L1kP5z+2UYpbK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "_shasum": "4702ddf72fbe0ecd82787c00c113aea1935ad0e7", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "e7f90703c5f90ca26f60455ea6ad0b6be4a9feee", "scripts": {"test": "node test/test.js | faucet", "test-local": "brtapsauce-local test/basic-test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "2.1.18", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "1.0.3", "dependencies": {"readable-stream": "~1.0.26"}, "devDependencies": {"tape": "~2.12.3", "faucet": "~0.0.1", "hash_file": "~0.1.1", "brtapsauce": "~0.3.0"}}, "1.0.0": {"name": "bl", "version": "1.0.0", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@1.0.0", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "ada9a8a89a6d7ac60862f7dec7db207873e0c3f5", "tarball": "https://registry.npmjs.org/bl/-/bl-1.0.0.tgz", "integrity": "sha512-JtvStMZpC5sQ5/5+cKdQPFsSQInvBla1pcDIPlNtD6pRtEXPWkB7XhLT4AYAF3su4KZQoDpQJOABJN5XtIAAuA==", "signatures": [{"sig": "MEQCICBKvcy3yMslFOQDjWY/9D/4boqIUJ6oxpSuRL4KMCpIAiB4EkCNE7Od6sSbw45hLIeipLIs1StbouuwwuRLaBcRLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "_shasum": "ada9a8a89a6d7ac60862f7dec7db207873e0c3f5", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "1794938be6697a6d1e02cd942a4eea59b353347a", "scripts": {"test": "node test/test.js | faucet", "test-local": "brtapsauce-local test/basic-test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "2.9.0", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "2.0.1-nightly20150618d2e4e03444", "dependencies": {"readable-stream": "~2.0.0"}, "devDependencies": {"tape": "~2.12.3", "faucet": "~0.0.1", "hash_file": "~0.1.1", "brtapsauce": "~0.3.0"}}, "1.0.1": {"name": "bl", "version": "1.0.1", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@1.0.1", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "0e6df7330308c46515751676cafa7334dc9852fd", "tarball": "https://registry.npmjs.org/bl/-/bl-1.0.1.tgz", "integrity": "sha512-Q/iehiXGpICgOOI2BAji/d2+LAu2QoNWIeDlgYH4MN7O8KSD9cK3XJdn4j7zMyhrvGA4cGzBDMiOGfbD5sgANg==", "signatures": [{"sig": "MEYCIQCCKEFMD15vrSYY8WNhnVUW1PWRvuCyMsMkNloLNF3j6wIhAM8bUWx/gjrBS1XugN/2fn6dQ7VlAe/B64v+NcFE21+c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "_shasum": "0e6df7330308c46515751676cafa7334dc9852fd", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "9eced07e8c4f4b44b42e6e99ac33d4c390b4e74a", "scripts": {"test": "node test/test.js | faucet", "test-local": "brtapsauce-local test/basic-test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "6.0.0-nightly2016011666b9c0d8bd", "dependencies": {"readable-stream": "~2.0.5"}, "devDependencies": {"tape": "~2.12.3", "faucet": "~0.0.1", "hash_file": "~0.1.1", "brtapsauce": "~0.3.0"}}, "0.9.5": {"name": "bl", "version": "0.9.5", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@0.9.5", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "c06b797af085ea00bc527afc8efcf11de2232054", "tarball": "https://registry.npmjs.org/bl/-/bl-0.9.5.tgz", "integrity": "sha512-njlCs8XLBIK7LCChTWfzWuIAxkpmmLXcL7/igCofFT1B039Sz0IPnAmosN5QaO22lU4qr8LcUz2ojUlE6pLkRQ==", "signatures": [{"sig": "MEUCIG9XrJIBKQq38DhfZKlQTSvUgBmK7weTDH6hifcphX55AiEAp5PZzmRf/lbNKITdXOLpcizw+9GlLI/Cd8MGofLxd9s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "_shasum": "c06b797af085ea00bc527afc8efcf11de2232054", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "fe77435118490ac7d07f93c0b52896c67fb2601f", "scripts": {"test": "node test/test.js | faucet", "test-local": "brtapsauce-local test/basic-test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "6.0.0-nightly2016011666b9c0d8bd", "dependencies": {"readable-stream": "~1.0.26"}, "devDependencies": {"tape": "~4.4.0", "faucet": "~0.0.1", "hash_file": "~0.1.1"}}, "1.0.2": {"name": "bl", "version": "1.0.2", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@1.0.2", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "8c66490d825ba84d560de1f62196a29555b3a0c4", "tarball": "https://registry.npmjs.org/bl/-/bl-1.0.2.tgz", "integrity": "sha512-Gqno6lkTlQIyqOxtI8NuaRAGjz6HGVKEIzVLchY1+eUSQ9gYIY4hyiRqqE22wUgMBetHyqRaMlS6X9H3hEJ8Vw==", "signatures": [{"sig": "MEUCIAR0R43ido+YK7GykQApVJJiidX5HJwT84cboXfBws2cAiEAkvE+Z3+Bb2UA8PqjF2F9g7DVeoYmQpqu0XyTMK/SamY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "_shasum": "8c66490d825ba84d560de1f62196a29555b3a0c4", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "9f1e7ce410e28d68c0a6f678b93b4cc2273e585f", "scripts": {"test": "node test/test.js | faucet"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"readable-stream": "~2.0.5"}, "devDependencies": {"tape": "~2.12.3", "faucet": "~0.0.1", "hash_file": "~0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/bl-1.0.2.tgz_1454532811740_0.7871121023781598", "host": "packages-6-west.internal.npmjs.com"}}, "1.0.3": {"name": "bl", "version": "1.0.3", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@1.0.3", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "fc5421a28fd4226036c3b3891a66a25bc64d226e", "tarball": "https://registry.npmjs.org/bl/-/bl-1.0.3.tgz", "integrity": "sha512-phbvN+yOk05EGoFcV/0S8N8ShnJqf6VCWRAw5he2gvRwBubFt/OzmcTNGqBt5b7Y4RK3YCgf6jrgGSR0Cwtsgw==", "signatures": [{"sig": "MEQCIHVaQkLwYyhdRAENXU0mvIdIuuy5e0JtZaT9SGfDdaw9AiBUBeloDtybIY5e0pZ4aHV8ESjOiTvDEPeyM0n+EwsxaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "_shasum": "fc5421a28fd4226036c3b3891a66a25bc64d226e", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "906e0dd6e811c9989a2c1d46fcca22c8da9f8f5b", "scripts": {"test": "node test/test.js | faucet"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "3.6.0", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "5.6.0", "dependencies": {"readable-stream": "~2.0.5"}, "devDependencies": {"tape": "~4.4.0", "faucet": "0.0.1", "hash_file": "~0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/bl-1.0.3.tgz_1455187279627_0.9823597683571279", "host": "packages-5-east.internal.npmjs.com"}}, "1.1.1": {"name": "bl", "version": "1.1.1", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@1.1.1", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "f81b4108185afc9bb6ea9a1885c5c5c7b1dae25c", "tarball": "https://registry.npmjs.org/bl/-/bl-1.1.1.tgz", "integrity": "sha512-WNx1Z08UpbhaNL5rMMHtN0idnFXGIwk8OOXJ/VcqtKBLCkYd+sBtWL224rK44c2whD7XfacI70d5DKilJfA7bg==", "signatures": [{"sig": "MEUCIQDHV9eyXdevsyDDIhCvREi2QivCelPIZjytpnBgGOxdTQIgdt8uP/ovg/A4IL+IhUVcvFG6XGeKzdDIcXCsrR0iCSA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "_shasum": "f81b4108185afc9bb6ea9a1885c5c5c7b1dae25c", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "62a04992e9c5c5ab0943d59d37928971576218f6", "scripts": {"test": "node test/test.js | faucet"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "5.3.0", "dependencies": {"readable-stream": "~2.0.5"}, "devDependencies": {"tape": "~4.4.0", "faucet": "0.0.1", "hash_file": "~0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/bl-1.1.1.tgz_1455246147968_0.6150420815683901", "host": "packages-9-west.internal.npmjs.com"}}, "1.1.2": {"name": "bl", "version": "1.1.2", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@1.1.2", "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "fdca871a99713aa00d19e3bbba41c44787a65398", "tarball": "https://registry.npmjs.org/bl/-/bl-1.1.2.tgz", "integrity": "sha512-uVVYHEQk+OuWvCi5U+iquVXvvGCWXKawjwELIR2XMLsqfV/e2sGDClVBs8OlGIgGsStPRY/Es311YKYIlYCWAg==", "signatures": [{"sig": "MEYCIQCYIDm5hAgOf82aWqK+SUyQ3FDEjQn5ukVavvDO1toZuwIhAMKEpuHYZmiReM8GD3Nya9dByJlj0kr4xSmCN2ncJDY4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "_shasum": "fdca871a99713aa00d19e3bbba41c44787a65398", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "ea42021059dc65fc60d7f4b9217c73431f09d23d", "scripts": {"test": "node test/test.js | faucet"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "3.3.12", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "5.3.0", "dependencies": {"readable-stream": "~2.0.5"}, "devDependencies": {"tape": "~4.4.0", "faucet": "0.0.1", "hash_file": "~0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/bl-1.1.2.tgz_1455246621698_0.6300242957659066", "host": "packages-9-west.internal.npmjs.com"}}, "1.2.0": {"name": "bl", "version": "1.2.0", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@1.2.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "1397e7ec42c5f5dc387470c500e34a9f6be9ea98", "tarball": "https://registry.npmjs.org/bl/-/bl-1.2.0.tgz", "integrity": "sha512-DvHHAkFsYiwt6YJQLY7vzpyLvJzsyZbpsJH4U2+2a6U2+NHN5s+Ms9ui+3q6wrPDIxXpBeLkl6wYR6LMPDxQMQ==", "signatures": [{"sig": "MEUCIBWROeXSOSH8wUdzm0YfLXHxwLSxhHChuOZ+cEoxifyJAiEAkm9JW/473OlNvg15pkdhXxfEp6yTncNskXJ8Z1pV260=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "_shasum": "1397e7ec42c5f5dc387470c500e34a9f6be9ea98", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "199e5c1807c378c8af5d81be0bb3c30921ce530a", "scripts": {"test": "node test/test.js | faucet"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"readable-stream": "^2.0.5"}, "devDependencies": {"tape": "~4.6.0", "faucet": "0.0.1", "hash_file": "~0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/bl-1.2.0.tgz_1482392005359_0.8425999039318413", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.1": {"name": "bl", "version": "1.2.1", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@1.2.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "cac328f7bee45730d404b692203fcb590e172d5e", "tarball": "https://registry.npmjs.org/bl/-/bl-1.2.1.tgz", "integrity": "sha512-QrWsZru4tcbQ0MouVzeYR/HINAo2Q+vGpeA/ildEiekYgK2eYo/tyKXVyEDfa63VbR0wAIgFCk3Fetz3kE8ElQ==", "signatures": [{"sig": "MEQCID0uH/G5XdyFgDbsZmI0zB21suOP4osceZKUO919+MKTAiAN2lKWBxkMAv3tYyJzrEQ+ebvJQHB71wXbLqgvp7yUfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "bl.js", "_from": ".", "_shasum": "cac328f7bee45730d404b692203fcb590e172d5e", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "d71a2a0ca3aba1fe81cbe62ed85f2d2e18759d24", "scripts": {"test": "node test/test.js | faucet"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "6.10.1", "dependencies": {"readable-stream": "^2.0.5"}, "devDependencies": {"tape": "~4.6.0", "faucet": "0.0.1", "hash_file": "~0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/bl-1.2.1.tgz_1493627681200_0.7146802595816553", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.2": {"name": "bl", "version": "1.2.2", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@1.2.2", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "a160911717103c07410cef63ef51b397c025af9c", "tarball": "https://registry.npmjs.org/bl/-/bl-1.2.2.tgz", "fileCount": 7, "integrity": "sha512-e8tQYnZodmebYDWGH7KMRvtzKXaJHx3BbilrgZCfvyLUYdKpK1t5PSPmpkny/SgiTSCnjfLW7v5rlONXVFkQEA==", "signatures": [{"sig": "MEQCIHeue3wlGaNPE2DtTw5x34dHN9y9LGUgqFP8Pt79jFCUAiAfT8v13IndBR8NEps20DNvD+uQv85J0tt4mrVzdA0hjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38223}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "feaaa4ca34417d7d2e659d25455a73a85ef989c3", "scripts": {"test": "node test/test.js | faucet"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "5.7.1", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"safe-buffer": "^5.1.1", "readable-stream": "^2.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "~4.9.0", "faucet": "0.0.1", "hash_file": "~0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/bl_1.2.2_1521669598170_0.7406366214040565", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "bl", "version": "2.0.0", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@2.0.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "faa080459216250225f431e888aec87fc6aeac48", "tarball": "https://registry.npmjs.org/bl/-/bl-2.0.0.tgz", "fileCount": 7, "integrity": "sha512-nqdgzTR3A/DdvWXolezBa/lJbF3+e/KXarzx5LmQGZP/jbgeOmC+fi2v9iaJllcnVQBOh6WTK+CuDlbsvdX22Q==", "signatures": [{"sig": "MEUCIHvpS8GtQMSMLGOR4L9/JOyUqNokEmrAcdj7apqcYlXYAiEAsLRLaoTsMNHfRB8G+ha9Mafs4XrzRaBlkdOTwd4f6hc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39960, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+p7HCRA9TVsSAnZWagAABTkP/RMzvCXJNbffHnH+WNIo\nglgb4Pa7JWP/qx31+3DKR5SaSI9OLqlKE5u+aSKBZWscfwfPv1OZy049ENuA\nz0XSx1Gk+JWjQGqhZBZxj7omQ8K34QJ7URZYjHOBVVgBQwe2Q8BDnHq+oLoA\nCk03aEjJPHD10PmWBY4S1yOkCpxAK/JzKdwkLdhljO0wc8mymvmaAxyUis4S\nuWElufMkO7/0antWdm3RfuJ9Zd/nDXR/Qerm3CK4xK3yyyqo6bkFcJHeXLvo\nyFvmJzYh9NgThoTDL2a5K6qi8huu55c0ozU9Pp9bKH/rfheQfpMi6GGfv/Au\nK2x3WnORwAEaYFcib5Ry1puGTBUKgDtXo/XRtxE8etTuxBzwa/RmWM5z1ERp\nLdxuZZjHmJNPv5wWkbn+7v9sj8jGxYCGfOxueO/g7+RIfxHQh/IvnUzPESk3\nb2cUdi4Imlscx1eoFY8UIRzZ0gppZDVQxjEPd0b4NawkD0QBgYQf7s2lWcQW\nseo9yQZTD+5DRMWtSkev0M6JAm7nzIR9CUnmg4qeIkZkTTmNjJUCWVaJPC3P\n6FNH0VkdAYiHFC5Htp7uefAKXXGDxrkAss2XRbtCmUYlL/vmvsROAebpBgZj\nmuXldIyHuOA8rJ0pFOLvkv6uoTVF5FIaA2cf+Qtcn7xl6ZKh+Alwtq+ZDRUD\nRve5\r\n=6Ktr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "52564cc1b2091374f66ca4544f686f199fd4537d", "scripts": {"test": "node test/test.js | faucet"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "6.0.1", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "8.11.1", "dependencies": {"safe-buffer": "^5.1.1", "readable-stream": "^2.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "~4.9.0", "faucet": "0.0.1", "hash_file": "~0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/bl_2.0.0_1526374086098_0.07346820948207466", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "bl", "version": "2.0.1", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@2.0.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "1de8346d50c3078d088d6a60c2bbc8c516248bc5", "tarball": "https://registry.npmjs.org/bl/-/bl-2.0.1.tgz", "fileCount": 7, "integrity": "sha512-FrMgLukB9jujvJ92p5TA0hcKIHtInVXXhxD7qgAuV7k0cbPt9USZmOYnhDXH6IsnGeIUglX42TSBV7Gn4q5sbQ==", "signatures": [{"sig": "MEYCIQDcLUAq9i3CGf/JKRZrgazY7GZ1imcn1hqhktoGe2973wIhAOwfE8ZaoIU0/91GWlPEKF+kWS4/Q4dz9KkW7IeWkHRF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39960, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbInKoCRA9TVsSAnZWagAAmJYQAJIa12CqwtTVMnwe6M5d\nJfSY6PqVq0Et9rE6VB7E5lDkbhU4xj+5ljLPcT3726GxLJ4CQvJGp/oLQKW6\nAUFmH29ru8PXk8xBIr/67/M5BBO/FZIFMrBGU8mklILBRNf+VdWDWWzUVnQn\ndiH3SRAQBfg9K42zou+Gscg8fMHktDsq9U8Ev/Yz1V0o9lnSeBbzjXBPfWR1\nvrZU4weTu7ynsY2MN5Wfq7M7TAA+4n7uuIGO/4eVjVS0ovQKEGrBrgYik3yx\nmp0LRtojomQVF9bO2GLZnRRLn5jvLGqoJvcQvr0hipi7H4vPcqSEj2Y6Gfbx\nKG7E/nrBQIV+mK4qmX4FT0iwIwq5py2K2Wolp/Hq+zbsWM5G8lvKvYVUQyi5\n3to3RSlcHay+O6nqHpw0DbLTDXhMr4N9AiJtUjZspqr2LC6be2ax+GlnzL0O\n449XJtTo5078fh0pM2ffe6Ale8O2233ltKZOZeR3PQumDd1upaN2O5VMWkXS\nqiGVg7dvHUmA8FY8ltNUYj3cpBlPReiWX8USKjdO4ratj6Yvtapt89zDLUyE\n7AtxvZLO4UtzH9rVCsUEccmTgc2qfNPAW9i/vEcBHaPQg+HPBD7a8NSe77xJ\n0QOdNcgY1/8Wgm3/SAQpT8vZlHeu399vso/6NJrI2MuizmyTRv+4akXnf4yL\n07OE\r\n=rDDa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "f13bdda1b18adc545c5a576dd40ae14e2cabd4ca", "scripts": {"test": "node test/test.js | faucet"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "8.11.2", "dependencies": {"safe-buffer": "^5.1.1", "readable-stream": "^2.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "~4.9.0", "faucet": "0.0.1", "hash_file": "~0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/bl_2.0.1_1528984230595_0.11921599579759667", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "bl", "version": "2.1.0", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@2.1.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "e5dba41d9be27eb3cb48ed53463cabd025b2a70e", "tarball": "https://registry.npmjs.org/bl/-/bl-2.1.0.tgz", "fileCount": 7, "integrity": "sha512-5ACYemes58YSYaL06sZuUQBMprbF5ig7B+TNdJXLe5yEgNxrjFyPZbkL7RzJ9ogngDJzZwLCw/qB9Bw9CYT0CA==", "signatures": [{"sig": "MEUCIBlO8oPHoAbISH1uv6t+QQ6N6DzmBcy+U7hK8V+ZAmT5AiEAkX5X4fb1i+wpyMoFPmj7RopXrUfWWEZVnLJCESQ6bW8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56847}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "8c596a8968864b1d5fd2fab74083aa9c5e842742", "scripts": {"test": "node test/test.js | faucet"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {"safe-buffer": "^5.1.1", "readable-stream": "^2.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "~4.9.0", "faucet": "0.0.1", "hash_file": "~0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/bl_2.1.0_1538651323017_0.12253795029321246", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "bl", "version": "2.1.1", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@2.1.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "bc883a864d8243b2836cf5b13857f512e7559860", "tarball": "https://registry.npmjs.org/bl/-/bl-2.1.1.tgz", "fileCount": 8, "integrity": "sha512-YTmzlmPyCuKGFSTLL3P7nlZHk+CDC3ddehCT+/ZwcI35jUjnpfSXlrAAr3hIEoD/+4TvKy27vMp7yc/q8aa6tA==", "signatures": [{"sig": "MEQCIHY3lZfVhFcGc+MyhFJnfvCx2E/pSiO7tAqKFZY/s6DIAiAK0n7EN/eZCCM+qHTT5+SJEEnpUFkS6zOE9hdkZoA6zA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57543, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbuuVrCRA9TVsSAnZWagAAqegP/R8rkjaOPmguR/844/BK\nPog94/PtHSTVtvFvNj9xP1zOJMvBKouKK8nFgrH8lCpa7cE1TJjuHDam3xEv\nptXjspcnlf2SfnjBwVzORw9+OC9Gle0tNTlZgNsyiymLb+45xupVgIVMYyK4\nvLbY5Jw54Xo3x2AJpy4wjJKhn4W4fhrge0UDKdSngwef+vB4CZK26jyV3VsO\nzsQBQQCq6tRx2X96VqvPVdCLxzAsOrAj4o6mrQJ5Glw1VGq/rYodZrOTs3wW\n9Tphfmsc60MDfG73V0jsLyz3oPdw5vE4plLlqDI93eSJcQMJ/qcjDUeGKtX+\nbJ74DeQiJy4cXaJ06Pk7FNqzedOwLq44rY7L7os7rTAHAnsSvYZjRsUqp9/V\nYiflHK559cyfj/R86jVQJLPEWKeTlkhAfwFs6yufryp7iZGjGYwncGKGriAa\nVFshWZ7UcQtxO4o8YGFvj1n1NfvNbANYQ6RABHFx4Xtcxl4asHVK0SuzM0gN\nt9kmPyb1LK2Fw6L+V/hAE60gTfreGll674e/ieB222Tij5ZI5P+TDhSanl3K\nvqrpz2lZjwSX72/wnE90V0qBDbCeBIhZY1RVOgl3b+G882NDMAmr5+mHuvDb\nts34yqmTJCD/kp81m1zoH6U1tA6TNfE1JgXEt2JSSyPWkUobNG7dPvrCWbpM\ndLCQ\r\n=QZnj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "6702c3affadab89d0db48921aabd16e30170767a", "scripts": {"test": "node test/test.js | faucet"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {"safe-buffer": "^5.1.1", "readable-stream": "^2.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "~4.9.0", "faucet": "0.0.1", "hash_file": "~0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/bl_2.1.1_1538975082712_0.6966946210943161", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "bl", "version": "2.1.2", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@2.1.2", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "591182cb9f3f2eff3beb1e76dabedfb5c5fa9a26", "tarball": "https://registry.npmjs.org/bl/-/bl-2.1.2.tgz", "fileCount": 8, "integrity": "sha512-DvC0x+PxmSJNx8wXoFV15pC2+GOJ3ohb4F1REq3X32a2Z3nEBpR1Guu740M7ouYAImFj4BXDNilLNZbygtG9lQ==", "signatures": [{"sig": "MEUCIFhmsDJafkcYNc3mbT774zcxQrzQMxSDLtyg/Yd8bnj3AiEAi9nnGNNrsKbm8r/AfEK9TBfzSwunsqKpWaYbH9yqA3A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57549, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbwOdyCRA9TVsSAnZWagAA5s8P/R+BamLl6TfuvFI3N7jN\nPxE/GJWYHjtFJTh+HCnY1iFp6jD0SUXQ2CoRByUBQsSsJKjoY03uTWwHW8R0\ns/NuLnYBlygdrf73d9M28w8P/aZqi5tZ7OONBZJivULZLDNgKvay7IVIFEfp\nVLcYVtJ/OMhtjrHoQp0JfN4tEwlWYO3p0vxLhXRgy2r+L0+oQ6R5pI7PdTQ2\nSmu3GiDR56SgTOpLcxRqv94w9MFqcggjvFsRgc2ynmBfk5BFsW8gYutGxZkn\n0KuZxoKMAjUN7lkb0iSVp8E9udU3dd/nsgHCudVZyHQ0P5kMIXbeu+T0Parq\nznJc7C75jY8hgJBYVl0lONEwAo6pm+G3cgmGnsiRUiLe0qIxKSeUpnQYgAPp\npsfzrq2rRRhp8He6Y3lz2rnJtjipbD50QyRuJ8fMwCFPdvxP8ij6X3MMVjid\nGy3IqI5Z0B/viW5WxXFXB+diAKZqlrgFqvgKey9ofWEladDrCaAVqBXF5d4M\nhHktq2jSuxxe/5fwm0W4BCzj/z3mAALrFoDe3y+Ac24fZplhBYhli185nj+4\nET5EJTmjciHEaAc2BmeZwDkz7BYGucSsLpgoQxSwBKHhJSN8a7GrmaoW9dXV\n5Aaw0sTYBW13sElHtYssZ7e7DLu2cRXz5IU+TicmpsoGON5VsIQLiET7FkJc\nJizB\r\n=AUQE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "270e5f5e8d1a3e1bb323e09a1e951d0f0d323ada", "scripts": {"test": "node test/test.js | faucet"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "8.12.0", "dependencies": {"safe-buffer": "^5.1.1", "readable-stream": "^2.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "~4.9.0", "faucet": "0.0.1", "hash_file": "~0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/bl_2.1.2_1539368817313_0.775657452433625", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "bl", "version": "2.2.0", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@2.2.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "e1a574cdf528e4053019bb800b041c0ac88da493", "tarball": "https://registry.npmjs.org/bl/-/bl-2.2.0.tgz", "fileCount": 8, "integrity": "sha512-wbgvOpqopSr7uq6fJrLH8EsvYMJf9gzfo2jCsL2eTy75qXPukA4pCgHamOQkZtY5vmfVtjB+P3LNlMHW5CEZXA==", "signatures": [{"sig": "MEQCICbnYy0pJ9yTfL1nLV+siLykMHgNbkiZwmfLsFI0vgbJAiBx3vegr6rs0J835jrOjz3H/pl+dIi74rOftwwSRDwiLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58589, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcVqY7CRA9TVsSAnZWagAAzoYP/2T28EHNevUmzZz/mqtI\n7kk7RXGycVXjuGcMK/OfTenSOxeZ3eQWOelWW7LGJI5O611iPUM8Yvp9X2Hq\nBa217h3KDZ0kaooNS85seflVDLufjgsh2SsPz9zP58UjTCRK0768U1xwB7A/\nT4ExS63s5NjqRfxTbydcpvt4ObCrib+z5aSSHRbhiml18oYfWlKHqknsgWfe\nBNAUz7tPEzHtnlY1nprup2n96kZwBrOD7m1SX0X1wh6ND2Yuex8LGwdtgzL6\nSmadUyv7c6Pvzkb4JVGBGd3flKdIWzexFNscgLNEpkihi6NQLTmSaMrzxVdt\nc8gpbbTgA4IBiX5sYZ3s70/iBR0baxVlLe43yiHXr4B2MvcmJVQT1SYiSZoP\nMM8vSYkA3FgO8gRLozyZ7lLrK0DqYJpWwhOVMh5BU+9Tk9FD0cKmmo0QT19V\nhmr30NfIN/JKzvwj+6eIuJfEJ+l0Lpn/tRpnIP4ZFWKo1APs6IqSDTR7bQWP\nRsguv7C2lzcxAsf7MqNpQFbI4VGr5P6o2hW7rp4cWb3zrjij0PKyxNQkeDji\n5IVJ3KRL1jAnthiHMdTofGlh5fs8bUqb5WRzhxmRaV7lPKthwIg/YHU34HJ/\nmDA3d99iz4+4vh7u89YAJw06C+TIFhHart3TKoWd3XNJ6mVJS9QVXzv8pb2T\nJtfg\r\n=6SMP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "b6284a824a8aae312e640afb0d59ac24139f0a36", "scripts": {"test": "node test/test.js | faucet"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "10.14.2", "dependencies": {"safe-buffer": "^5.1.1", "readable-stream": "^2.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "~4.9.0", "faucet": "0.0.1", "hash_file": "~0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/bl_2.2.0_1549182523075_0.8810481806404531", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "bl", "version": "3.0.0", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@3.0.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "3611ec00579fd18561754360b21e9f784500ff88", "tarball": "https://registry.npmjs.org/bl/-/bl-3.0.0.tgz", "fileCount": 8, "integrity": "sha512-EUAyP5UHU5hxF8BPT0LKW8gjYLhq1DQIcneOX/pL/m2Alo+OYDQAJlHq+yseMP50Os2nHXOSic6Ss3vSQeyf4A==", "signatures": [{"sig": "MEYCIQCkF3kLxx2icpzE/boKLIuIPXj7FPUAfdqkBpOoT7QcXQIhAOTY2qddlEEOtZvxSDLRCzKYf6fYTRr6HRcpcTsKj3AE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58774, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcd7VtCRA9TVsSAnZWagAAIoEP/0/VcjmLqeHP+6V9fNBL\n3EbmcPAmZbtDFzzNSzfTSavXudliVM9GtGZbgUJ8nW4MJaOxu3uFS2HsaS5q\ndEMmpzqG45/yu8wCQRbCNMInMksFRbvtDIii9+EixFZgKZSYM79ibg18Z9y/\na0ZBz+E5PuJYUqP6Yul01V3cqowVrRIf9dhKwpZ1BFhxNUNMp+nASDWUsYJv\nPshcTj/ST7VS5FxTqzOodixMAHoFAohX7Tf1qBmGU75AOd/V4Jq32eZtRTzt\njLNSskOxwOBRW9FUR5JIuCRaq6r/kgCMRFk30ekyPN7KWrUSM97gNOneF9Dg\nsBvERMzLq1jSbH9qlvdbiV5PuQW+NfdFVesqddYdNjDn6o4ajShEunJirBRv\n0cBaEIZfberDA8vZHSeeNXU866ye7HTikYTwm1+weMYh06XBVdy3XtOcc0Ew\nvv8IKvRkXwz8NTi6cyMcP6zUG85kl48GK6BCNp4XkRri+738WcrvhzH60kvS\nWxrkTgDcVFSmoWJocs3yMaQFoFjkQOHdGu3EyIdn9dEiJgSMjOtu2fpHlFPx\nH2+fcwNh/do5+hCI08zTZGPsKMtp6fKVKUiZH06iIdvx/0zpt9z9NPTCas7O\n2IatNP1Q8/DVVrIlcEsImf7h/qZAmIL2v76QJpuKCJKuQaycvdkq38061BV2\nPLF2\r\n=42QJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "ecc0cc51ef62f41ae151e82d61a8eac71c2ca672", "scripts": {"test": "node test/test.js | faucet"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "6.8.0", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "10.15.1", "dependencies": {"readable-stream": "^3.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "~4.9.1", "faucet": "0.0.1", "hash_file": "~0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/bl_3.0.0_1551349100582_0.4647639136198465", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "bl", "version": "4.0.0", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@4.0.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "fc7351c473992110749d3552087e1fe942021e65", "tarball": "https://registry.npmjs.org/bl/-/bl-4.0.0.tgz", "fileCount": 10, "integrity": "sha512-QwQvAZZA1Bw1FWnhNj2X5lu+sPxxB2ITH3mqEqYyahN6JZR13ONjk+XiTnBaGEzMPUrAgOkaD68pBH1rvPRPsw==", "signatures": [{"sig": "MEUCIQCNXboGDIZVsbILOmV6g5cVglcFIoNyAY9yjs4NBZ6M7wIgBO+ALhnHTEuZ6OdRe/5xfsNSC+Sjorjm2/MP9zDdxyY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62912, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdguN3CRA9TVsSAnZWagAAeZEP/jdHp4PO81DRUisjvHfH\nkZfcLvf5/s1vhFtgfF9FYXbxindbYcLcXahZrIsIyHdkChEXOh6j1tu9aoBq\nnZDbEpjLdCuxDD9+b3DtB5c2Ntor5AwxXHeOX8O578xkR0i6TrJg+iUakoyc\nj/4cNFeojZ0YA+L/W47v4B7+NJZ0YBbLB1LqDJ34/VtzNzKN4Im9iZg6LwCL\nJ6luN2VHx1Oeqa6Z7VC58I+F35Akfz9xujYAnXZfcaaJAseKhZQXklKKGsB9\nX6zxBULZImngAwjYkf5wOaayLngloBfUxdhV4mBIdrvcnrjG5CxCAKM53YZx\nYsaiJhDNT6g0fM9FdNNS3ZHO4e1gx64mlyddpccYespAaebcDkix0xHQc2pu\nCoxdfXsbrHmlmLqDBlizt3b4JzKFlfmXwG5DTocV+LbP9PKyftIuEfFdjO/j\nnECXhghntBH1kbehHSQuLXXR/1SEkqe8++4mZ5GW8gAmuEimGvNXC5JNGiC4\n6zHnUNGsTH6VySAx3O2LkLtOWDMpDtTr9JBBqqniNiQlhtzy7GK60FEM6hQr\nZj6zY4hVS7QDIQunmPpq2pZg32rqpv+c07z7glH/FhooOpzRzPIuFuLiYUPa\nZG1HVvE2UEpzZ68mG05crqqpm1ltd3s2NG5U+2Ux/YfOidxMN4PfnHd6kYAc\nmJVA\r\n=2JEL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "8ca2ec434a053d48b399c5b5cb06ad00202cc5d4", "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && node test/test.js | faucet"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "6.10.3", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "12.10.0", "dependencies": {"readable-stream": "^3.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.11.0", "faucet": "~0.0.1", "standard": "^14.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/bl_4.0.0_1568858998592_0.6674685522448147", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "bl", "version": "4.0.1", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@4.0.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "8c9b4fb754e80cc86463077722be7b88b4af3f42", "tarball": "https://registry.npmjs.org/bl/-/bl-4.0.1.tgz", "fileCount": 10, "integrity": "sha512-FL/TdvchukRCuWVxT0YMO/7+L5TNeNrVFvRU2IY63aUyv9mpt8splf2NEr6qXtPo5fya5a66YohQKvGNmLrWNA==", "signatures": [{"sig": "MEUCIQDcpYwam9y5MM4kr1mI0jXbuPZ8qPDQ3XxdhSWNfPGzIwIgEKdnEFV90x1LmhErToIxcJ3j9RsHbdu9KrCEhGsrnCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeX9CfCRA9TVsSAnZWagAAnvMP/2xALS6ZWT4UYGJbuBz0\nzoqDvkbIlIpuM3/jKd2PJZEPVyXXS1jWn8FwSLDOHKKg6Vkz5bmjZvV9LQKu\nGFJgeh9GYX4nbftlUXVK+0IhQULm/wG6HvTzuxp1LfpN/Hhv1NSwepWX0Fat\nx+ipiIOaCSZyfTN3orlNWYH2qW3hbJneJ1gFFHMbekjgoWnj/NtbpizGNBY5\ninqn03rVU3qPTcqyOfejOmabd400CKk4HMTJnVcUrPzsDsp/AGIuOV4LyAZn\nI1G3jxWl2B1+6rO3PjK0oCUALEQXCevDcFwUEROWTCqBvilM5TXDWVOOI/UZ\nmvB3uve6BOuc3BTIBWJQM3C1veFhrUzjU8BA4p1GNOfmTO30g+QvjtYxTiWm\nmu95pbWlfR6tlfYM2FPEtFG3gPb90NNB2/0WXAHHGeBnzmn4JiXqWtRqjrSF\nYBqbroNdck4HKcPAGejYzH1VV4CaDwnZoLzVKk+YTgeR0ruxIoMG/yn3fkJS\n7hKEjPG5i3+f/hegyJIoQ9fy0z2vNiH7rRQ7V7BxaxANWQ3oK42naZwrGiG0\n0stlnemHrRy+LmDErK5kLRomhHUGmjdCOkWLOGwBsh7VWkZJAVLqh51Q9UZh\nIVLepQvvHqU0IQUMah1q8uVVAKYSvo/+jtW0ECSve2JLPKrKvTT1c3MiAtSX\naPNG\r\n=xL+P\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "90a713b863ffb368aa44ab4b5e16dd91d71db3b0", "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && node test/test.js | faucet"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "6.14.1", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"readable-stream": "^3.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.11.0", "faucet": "~0.0.1", "standard": "^14.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/bl_4.0.1_1583337630846_0.7707008757876899", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "bl", "version": "4.0.2", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@4.0.2", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "52b71e9088515d0606d9dd9cc7aa48dc1f98e73a", "tarball": "https://registry.npmjs.org/bl/-/bl-4.0.2.tgz", "fileCount": 10, "integrity": "sha512-j4OH8f6Qg2bGuWfRiltT2HYGx0e1QcBTrK9KAHNMwMZdQnDZFk0ZSYIpADjYCB3U12nicC5tVJwSIhwOWjb4RQ==", "signatures": [{"sig": "MEYCIQCxR/obnRDWGWbmyMXcZ0aBI5oOt3ynCIofe7K5lvaqAwIhAOm+4pt6NDAMo8gLGOPXyA+7xbf4LgFMtxh+b4W7EnXW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62981, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJecZUgCRA9TVsSAnZWagAA+DMP/2AkicRTuNSRP/jdl1Yl\n9xp0y3pWx+inwXfYa49IoZn6lizcP0Rh+mdN19uUgJYrNHT//Xi8bgIvUXuD\n/SNrwrk7ZzFpP97bVYdSaCBaTGC46Wpgp6TlmL2YeoE7CyvEkwY84yUui6GB\nSD1+Kczi/9mIfGXPsedgLusgDcnLBLlnEBSU++w8Ey+oQpyO0pHTDbIFn1hn\nR6RNtX1obL4ztlVsFA68ul5+ECUyApuadXMmiOnCPm+3oEyrR3kTzePPHU9X\nUrUx/CYpZAyXT0zA8GY/HzxpIFud3pEhDM0HgVpdZU4QGGqRL5kCc8vtM8VZ\nYZUItHWvDoLXN9W0KQCPP1uGzyszJvmeaN0ra+nKAig6YQjPg7qC5XsOwICd\nL2LD1QQJNauma0jpSQ8kNpLI/wULcyIi7FHzY06TmfWhC4HtiVKXnZJpTPfw\nFWfDljSzmyPK8z/pvYQSbNNYMOHT+5ok4y7jEbWSRD3ZgBrIyZAyXIBv9ix0\nQLoFVbOHcx5guPQ6g9odF8DP/n6RsWp0N1t4SOWZHpbPns/g/SwXlxEByue3\nYSQ41JQ6nppMOUAr4A9P5E0HSav5QEIwOW/HAZSJOTua2dU3/9WwvBb4/NQR\nYFPMErweR6FlKZ65fE+B+pVJ6unym4HnAIKpVpFa65XzNPZyOjgUCkl3mYyA\no1QA\r\n=KP+U\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "5059a24564be3f123e1641d1f0da3109e7e2b632", "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && node test/test.js | faucet"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "6.13.6", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "13.8.0", "dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.11.0", "faucet": "~0.0.1", "standard": "^14.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/bl_4.0.2_1584502047774_0.44151413433527265", "host": "s3://npm-registry-packages"}}, "4.0.3": {"name": "bl", "version": "4.0.3", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@4.0.3", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "12d6287adc29080e22a705e5764b2a9522cdc489", "tarball": "https://registry.npmjs.org/bl/-/bl-4.0.3.tgz", "fileCount": 10, "integrity": "sha512-fs4G6/Hu4/EE+F75J8DuN/0IpQqNjAdC7aEQv7Qt8MHGUH7Ckv2MwTEEeN9QehD0pfIDkMI1bkHYkKy7xHyKIg==", "signatures": [{"sig": "MEUCIQDiVy2TUNv2K8Gt1gi7u39WPBIdnscJsvQurGLEFE6NqgIgJl57qLqOenyWcNpqOYVhENZLArmFuM286faisfAQYNY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63707, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfRhogCRA9TVsSAnZWagAAw3oP/1otJdQEx3ccjdohiuFW\ny0ubljzLchkQwNRe1TgtM9Jv1LRaa1IIuI3DTf6H0Dzkik9fXKNsFn9A55sH\nG+nY1WTk7cOvfZUftoPq3Fj2yUUNSVDD0BP6teSpK6uTM94Wr4Ww15l8ToZH\nwYuLL105FWvkUizRLf8SdRM1bXjFzuLm954o+MU7GyKCIojxm4fdp02EdRuX\nNXAdkGgjkI4cH3zfHfmm7yP3zaXxrAxXajXZAjNHT5w515buGXMR4BXGkMh6\nYENkHs4gWyIDWNiSdt6tH7x1R2ZrMOnZm6G8q28K1ELrI5WQCeoAjWEqAmFR\nDXZ4nzSQ1FwHPj3RndsXSCJ1/7qXvTsdYtIc4LUOYCL577lKHhL6NuH3gALC\nJ6a2fMZjvL+0BPXPFaRBOHbnCHTGkmF+kMVfQPqYX9L6RhOcgWev2gsyaRqd\nyM8W04bqDUiA2sEKSAb9wvqu+mKEGnNP37XUuZHIRtCK0N5DKF19kpYCEciZ\nK69dvsr1Tdi9+D+HLWUOaG0p+tvxIza2uCkcMOw1+w1g/VsVvkm13ttW1jk/\nNceuFwG5Cn0ikl4uLhb8zG3IYwrIF9vNhfKpd7cBnbaKIIvO5XxTdz4tduKi\nzQQL4CTOoY6YfaHhWnS+LXoVWdLwIBALJhGRJjOuIuJkAP87/NEqREVcpdZJ\nC/Px\r\n=vuvj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "f659836cc84211cad41b73bad89c78f7f874c626", "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && node test/test.js | faucet"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.11.0", "faucet": "~0.0.1", "standard": "^14.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/bl_4.0.3_1598429727636_0.5134502697805872", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "bl", "version": "3.0.1", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@3.0.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "1cbb439299609e419b5a74d7fce2f8b37d8e5c6f", "tarball": "https://registry.npmjs.org/bl/-/bl-3.0.1.tgz", "fileCount": 8, "integrity": "sha512-jrCW5ZhfQ/Vt07WX1Ngs+yn9BDqPL/gw28S7s9H6QK/gupnizNzJAss5akW20ISgOrbLTlXOOCTJeNUQqruAWQ==", "signatures": [{"sig": "MEUCIBXcVYwRIaxYNElBxCNgfELeKwoe/D1UmSv+y8lLL5k8AiEAkCI6LewVIOrv/NJqOxY7vfW4a3AaNaZp2euwDjwrEx0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59483, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfRhusCRA9TVsSAnZWagAAWC8QAIVVneVswT6jEqwcEd5p\nyt9kJvfdUK5HYrrf3pMjIMd0lhoDBQmMAYX1V658jsq35QmTZvokwR27GKTU\nLLcoX2pL+UvC4Td5aS7T26mbCERO8tKdQgnQJRbxSTVl2nwXKqq8tTj21ZWR\nwmyPgdaHd84nuc30uxRIvHg5+oWpMD7IBzrQDgnFlU669nMx66SRz2RBQftD\n42DPBWRTroWc1hQUliaXIntaHQIMK3cev7sMwqXdFlYUSkWJswSt3Z8vTkvC\ntD4fxJCZuW2/Xc3didOI7eCgZ+pWFpO33xiJgn3BzYFEBPQ+x0CW8Cskljzl\nL+bv5pP3V2D9jrFWPgvE3+DPUleuf7WH+KGXwoZaTbd/fVkk67D71KznYs1F\nCSV6TcFjy4a/lneRywTtvKyAGVU/zEMetInHBI3PiZbrSVG8zgwVOp1l3NqI\nVCbJ10IfMZ7lI9TAM5xuRLbkCeb9+K0Uu2O7EL2M4qDe/EL+iLhaPHSk27va\ny4AQ0NRfAbXpTpqnBdiJjKlNeml2sDQZOrYxTaj9BeetqtjfRD2WHY0NKxm5\n+3UGnfyPhMLNCvhur63zD0qQYq6hBg2I3qblOoMiLzAbP8AE/HE2ppr5DUfn\n/YlPLJlP/FegWYXlVEDiCwctJHwS1Q5i9H+6K2Mrqn3tzN/tmWXto3NEtyPM\neDsy\r\n=tYLL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "84e1852465572927d9151e6484f351d712b94dea", "scripts": {"test": "node test/test.js | faucet"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"readable-stream": "^3.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tape": "~4.9.1", "faucet": "0.0.1", "hash_file": "~0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/bl_3.0.1_1598430124152_0.3891710583184578", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "bl", "version": "2.2.1", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@2.2.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "8c11a7b730655c5d56898cdc871224f40fd901d5", "tarball": "https://registry.npmjs.org/bl/-/bl-2.2.1.tgz", "fileCount": 8, "integrity": "sha512-6Pesp1w0DEX1N550i/uGV/TqucVL4AM/pgThFSN/Qq9si1/DF9aIHs1BxD8V/QU0HoeHO6cQRTAuYnLPKq1e4g==", "signatures": [{"sig": "MEUCIQDpRBPx8BsPAOiEf3kx5XdWmNIprq0nXEu8OpcVQisfjgIgaK+8sG4kmXTcMqM5zSXg0gLP6bbSsiHhdaAgnn3JIMk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfRhx5CRA9TVsSAnZWagAA9XgP/jBc3pRhEaU/ZP21pPqF\nNDCmCY7wjUAxCcJArKtImXGPD62phlnvsoRuvYxFX9nPdPylqIb/jcMjhm3p\nLM3LtAlDf5MZl7JvDCs3QaGHjkjBS/3jHqYA4379IVpZrSKUMQumkLtdepMx\nqvmLapnHy2eZb9MHNkUWN/+YB5DjmZbXZlU8F/W2eIXkU9VMe4qR4L/u2MY5\nvd+r/MaysnFQXplx+gSFXzca+EAyYqd/OfCkcbsqx+ccT2VmC0J9PiA8vD16\nyrQbcMNJ5nmAWKy2aP2NbVymhLYNEyFV8AptehMCR9durvfX2cc+M1kEDaiZ\nzVXOMbUljQY95bSkK/RS6Vi1LnAsh8IADdVeqMF8y8sWyWNUQ9AMT1xVj/pw\ncmNqXLx+N6FxwNOp9aPQb4JeXkMG/+/+Ew9bYIXPgyhCKEnBOLmhKIu5fkb4\nIdih3k2Ir6NvLVVVwo75+QKly3RcvYFvLP9zZt5lJu80oOxsnKPyRbvEUSwK\n9zyhhIFSKm7hjALrMWNIsrU01RIN8urlwOhjZNx0NKYw0zDkUqiZtabW//85\nIUjJDQwpgA+F1T7ySWmvF6ngdfUOFgFXwNXQxvcLEQt/nVpH2uC7LO7FMRP6\nJZ6DxZOoy0IoeH3/WKvA+Furq4AhK4xlcFZ7waJJsGzkg1EqqAwxiVvFdbjL\nmvHt\r\n=vy5x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "8cb93f455f0b44fe0ffaee75a3c74c84579dc8c1", "scripts": {"test": "node test/test.js | faucet"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"safe-buffer": "^5.1.1", "readable-stream": "^2.3.5"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tape": "~4.9.0", "faucet": "0.0.1", "hash_file": "~0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/bl_2.2.1_1598430329381_0.5070763241499299", "host": "s3://npm-registry-packages"}}, "1.2.3": {"name": "bl", "version": "1.2.3", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@1.2.3", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "1e8dd80142eac80d7158c9dccc047fb620e035e7", "tarball": "https://registry.npmjs.org/bl/-/bl-1.2.3.tgz", "fileCount": 7, "integrity": "sha512-pvcNpa0UU69UT341rO6AYy4FVAIkUHuZXRIWbq+zHnsVcRzDDjIAhGuuYoi0d//cwIwtt4pkpKycWEfjdV+vww==", "signatures": [{"sig": "MEUCIAgM4/Xfd1zrdbXIoqqzH+ck7+ebMRXWdre7Mg4dBllwAiEA6UiXPc3oKHFGVcB2wmUYMyNF0vc0PGDbSUtehno0uYY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38900, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfULzRCRA9TVsSAnZWagAAf9oQAIdJdQPE0Bo0Qf9msbrM\n3h5Qh2cH0nqo8QOIsG/5Jm/S65OKpsaTu6hnPhepfi1Ie7/Z7waeHaRbEyCB\ne3F0DrbXYBS032DISV7lqzP4ZpTRekXGFCcRK3/tbhH2ZHvrzY/lwmbcAiSn\nv4BjnrDQpjgVlxQ91tghZ3Bn4y+L314Fxd1bvoXL05wvYI46WL2V0Vx5PiTQ\nyZznKbI3G51OyY/P00pEALDgNFRQXoYbVwLosYrZApPr40GWo8tFOgI77l4W\nidZVwqtIh/Hm6GFq9AYP/mOhy/+oX1uUkvE7pDFjRLd4GNXjT566lppsfrTx\nbjJvLYhayQ8QtZ0LAn/1ANvsDek7ecYlKIoWHkoW7tn+nuvcYBxoh1EdVfpF\ngVXgACB8YDiqm8/VbphQEjM28AIAQodolCmvhrOYv2FZzHdEuqDlZH9VF6LO\nDiRNusSNlDUacSjK+ZVHCWr5BmEt9i2ELv+DS12mJPapHWoBLQUDQpZxCTc9\nUr/HYIR2t40UF1tKesJwqf0xdoFvw4mCRUyT3K9YKqO91BKG3mBGkyURhbS+\nteV0OnSjpwASN8bF96P4npGIa0nR4rHgBSbYat5Ofy9zW/nC9H1TCbwqrLHq\nHptedd829cDWsNmnPKFTlATpFZUny4zHIHooOftTH6kxa6ukX3+9pqVWXx5C\nGXhC\r\n=N4L/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "d69edfd66f2b1d8b50abc0ecfe42216437540178", "scripts": {"test": "node test/test.js | faucet"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "14.9.0", "dependencies": {"safe-buffer": "^5.1.1", "readable-stream": "^2.3.5"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "~4.9.0", "faucet": "0.0.1", "hash_file": "~0.1.1"}, "_npmOperationalInternal": {"tmp": "tmp/bl_1.2.3_1599126736504_0.6225669441727384", "host": "s3://npm-registry-packages"}}, "4.0.4": {"name": "bl", "version": "4.0.4", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@4.0.4", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "f4fda39f81a811d0df6368c1ed91dae499d1c900", "tarball": "https://registry.npmjs.org/bl/-/bl-4.0.4.tgz", "fileCount": 10, "integrity": "sha512-7tdr4EpSd7jJ6tuQ21vu2ke8w7pNEstzj1O8wwq6sNNzO3UDi5MA8Gny/gquCj7r2C6fHudg8tKRGyjRgmvNxQ==", "signatures": [{"sig": "MEQCIAVdvHTBV4zeik4o3UL3r/AqNfJPZ4yU928RCZB+HGEyAiBLkGwLzD0nfhkKjVviiqPFyW5sy61nLhH5pGIPfWH5sg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgG9NgCRA9TVsSAnZWagAAo6YQAIidzJYGydzIuxbODG0a\nuVQgwXV1O+DtCtfUtkBS0wlTk7W+VgbQ1QzTlrPSlokS/uaXOYOfHnn0Sa4x\nU0bxDNrlG31PFQXuhCQ07kDfgL4SCKjjAiBA8r/7lHxkH+FQiT2viqY2W75q\neSlVtGOyH6xpuFeYtkqXSMTrBv4xXpij3JuViUiHsdDE+MSMUEXKQP6Y5gON\nxgO4ISCLG1X17F70KMs8dwLiWPFLqc+cpzQrTGVPeHJoakUrCWD7kR/wN8j/\n3ln/JEBl1B1LwKnEc9i7Oaqhk69Tscg7sZpJuWfMOQsXe4Q0PLSnaBjw9x7M\nqvyQl8fsKZ51JOX4emxn8E3QMyswhPJyQR1qCW+Rr+wihoA5wIAZ7dQMTDyY\nT6KLMTtspd1TNoFX+2f07jYl2bUXOH/oQELdu/Z800N8jqN0/8HztcAKSuXi\nh5yzJ8qcDP7dYGHGSXGhBDYk0f8tYojpYsG6hxDjrsP8oweVS57TQDV8O8fg\nC32E1in4YW1XNwR5XPg2UzUq2H5SnN/igEPm5jqlSIOFhlj7Ce1jUYb+elnn\n208hOM3aqEdAtugP0a94bUeXLQ6gkXZBnwV+/ZK+eH1PLnDTrv2Rp+jj4Rct\ntmxscUS1o+o76n4bRZ9V1dFlQj/BURiv2nIMaABUNlFZOLjqvk9ZUiRwHZ5y\nVPdH\r\n=Plzt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "bd6fea1f5cf2b428a5203a39e7bfb6ce81d1e65f", "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && node test/test.js | faucet"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "7.5.1", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "15.8.0", "dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.11.0", "faucet": "~0.0.1", "standard": "^14.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/bl_4.0.4_1612436320277_0.07253720635983685", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "bl", "version": "4.1.0", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@4.1.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "451535264182bec2fbbc83a62ab98cf11d9f7b3a", "tarball": "https://registry.npmjs.org/bl/-/bl-4.1.0.tgz", "fileCount": 10, "integrity": "sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==", "signatures": [{"sig": "MEYCIQC2Cmc1+Ec0pZFBU76li1AQDuga0SjcbGZRxEhe7YZC2wIhANTKuyLhqenxHPyvP3RBc4DYHI6T2mZRAHwZpaTVahqc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64427, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgImXvCRA9TVsSAnZWagAApEoP/2zb/ecxSdeg7mY8sa0Q\nci+nVR4svCoAkRojRaOYfqg+6dqhYYOuC1U+K8FFn1TCOe6f5NWesm/f9EAV\nSlhwqcm3jCxMARKtnNZlFqMYuAROctaW1QXMhc5+bVQwZyeW6TcTteFFXcXm\niOilgfR3DoRnQTbr1eZzZXk4m5ZbtIkKSfqpYgGfZBthdC9YDoIZ1NMk2P9v\n53cpdZSPPQI/9Hr0dpn4ggzY7FgcxthLUbT3MtpRKIbq78/3urgMKZLCwjqJ\nyAGxfkIvEQgYRDx/l8C4dEt5MFoUpNAOExaRe5rrYo+tPhEHQK7Vno6P6A3G\nVjA80USZDt3hZh7RJzbEOREmBUxMjCBPv0WkBLG3JYEYHTxK3v3mljm/JCst\nQVkLyLls8aerqm1/sd6F7+etaE0pV7rX0kA39TlZXc6nEybhkgqnzoQRvNzm\nMFylkXUuBiOenXXJaG0RFWoWCYInv3uZ6mhg2bBdu1nmWVAkhJU8MDxlStuH\nyQ77lzwtchKWT2tdJgBfJWFU7rLn31X5YpMhycg6OP2hwmNx3kIZ7uXVL8S5\nTP+jbdak1GyN1MxDwe8cYea5gkVYkYUtjO6LlSAUbsXb4Q9V5HlFOSC/s0SU\ns5OlEkVE2AU6nejVGIIoFpElwaVhoyi/bKi1TC7PCRKDGlsNFG41e+HuXK9D\nRKua\r\n=nNwq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "75052a573fcc5174190799f87503cf5da8f6022f", "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && node test/test.js | faucet"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "14.15.4", "dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^4.11.0", "faucet": "~0.0.1", "standard": "^14.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/bl_4.1.0_1612867055327_0.31540342671855837", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "bl", "version": "5.0.0", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@5.0.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "6928804a41e9da9034868e1c50ca88f21f57aea2", "tarball": "https://registry.npmjs.org/bl/-/bl-5.0.0.tgz", "fileCount": 10, "integrity": "sha512-8vxFNZ0pflFfi0WXA3WQXlj6CaMEwsmh63I1CNp0q+wWv8sD0ARx1KovSQd0l2GkwrMIOyedq0EF1FxI+RCZLQ==", "signatures": [{"sig": "MEQCICBEkOPExGC5xNRPiHRYN8r8d5UQXabVSsvseWytmyi0AiBAqeEq7RHZTmJna59cjNY49D5afmwe9SlG59EmR65MDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64426, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZ0ldCRA9TVsSAnZWagAApvYQAIPfpR96hZSzn1LJb+k2\n/PH+aMK1wBNbMic5PN/pELU4xQphJwwnvKOb0bcg2LkDV1HKD/j5+mMV8ns7\nxUN2+CmPZVjMpo2i+ME4bbmNFcEzR4MKAX92IRS3aLzho0gUsew18LXXaCs2\nHcyGAZsiUYmqYjo1E9AaXA1OrknTfJJgUkCJXRQcIZodpzSe8Sw0Pohf0MEA\nwI7+XMWNjz09wEoxJhRMDytc8UnclhKKkVM3q8Bzd6p6E1gyhXgt3XDdzedS\ntfexHEqqZbEjNbnK4R8R/3jlqa5eCbg1Y3eIjQqUuznQkFSJdfT+4WVMTbpu\nZrvx9stWfgluwyfv+sW3mPPqQKSSzA5C7UydTwLl0SFIHd7/BjJ571N9crB8\nyy2YKe4S8yVRQ3nKUhhNHMU8D/8Od3NeISDuS6xPi/vWhcTU/sp+KRc1SS2G\nUOUF/lhYfmpDxT5tY7zUwYd90Jo2WSClRdhC4tsNdyh470jFofUnYIJfJfPl\nf7Wl2ba7/XPTQP8iSQI6AHebdKzeaS+4Jz0tro9MHAi//hu7No+8dF9c9fLt\nSJ+kCpkQbkJJ6Cbzz0+BlwfRsyfn9d2mmpNDvf1VLTIo3vzRP/yyyWe2iC1J\n48bdAAHi3bnFiSPlaw2p0N11c9myxq1qPUri0giOvgcX1loaaFPYSk4CyYMb\nZPPM\r\n=ERxs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "f7a00711cbf04a20d42f7aebfe2fa948390b9ccd", "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && node test/test.js | faucet"}, "_npmUser": {"name": "matteo.collina", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "7.7.6", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "14.15.4", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^16.0.3"}, "_npmOperationalInternal": {"tmp": "tmp/bl_5.0.0_1617381724813_0.28561452114368446", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "bl", "version": "5.1.0", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@5.1.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "183715f678c7188ecef9fe475d90209400624273", "tarball": "https://registry.npmjs.org/bl/-/bl-5.1.0.tgz", "fileCount": 14, "integrity": "sha512-tv1ZJHLfTDnXE6tMHv73YgSJaWR2AFuPwMntBe7XL/GBFHnT0CLnsHMogfk5+GzCDC5ZWarSCYaIGATZt9dNsQ==", "signatures": [{"sig": "MEQCIHyLWHMLYKrsvISFHeFa1kuuHP1j/KCFDx5UL0mTj4qWAiBbqKSjPKVu5mnwE0Htv6KB8zMNUsQ43D/bjR4b1i8tcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTpOiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrzLA//UdIOYH4kwHrGWc3MC19JsGTvQ8wJ1S0LxUKIJmcVakukVSxS\r\nfAbmXhsgdtt56uq0+I0OjS3Uwpb3eh+J7nHNiHpKkqnPLIFjTbq3jzUp5FdY\r\nLpIZmKo5jj99KlgO642KqlgeDfTCEnEkG5iPKbHpbnXXL5palJAp3Wtva7zj\r\nr08f0Y+ZICZQjI3zcygerhI8ojEjEY4N4Qn4NoLwfjK2hH4ECUxIEv8/NLm7\r\n2PPKJU7ZpL4EmosxSb/Zn8RFAbqmZoASp6fQOi07mSemcjfpxEv990m+3APh\r\nWwwAxkij+C6UeNI0ga2wyDFLS6F/25l/te6S7qzFEa2pbXUvgTRCBg7P126d\r\nEWydc0s6W40y/QKYSrDvAXW1w8b40gQQZBjiPBTRIkvtNawAfbzByhdIGAuC\r\nKbb8xwomOag7bj3mKjm8lt1Oa1MU8XsEU7iyuSBmUE92z9BoJ2wxJqS7I6DH\r\nDVYlsnOY1AuX/ojXeUuAb08xAx7nqSpwyLrQdoSa4Nz4ysEUD509ZVfnRXmy\r\ng9iIsOoVXaMBnwhdTOosxhz932eStCFNBP70FCIbbIDZBN+XHPUCsE0bwXne\r\nzIt81jsoJzWIrmMQc6Qtls5A605/wTlPTcR3c+7KY1203LuPUCRopt97wdaU\r\ns4DH1Y4W65RtTiamN1S984tbfgF+wojyR3s=\r\n=N0wU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "3af8c54d33433c4683be4a74588a9739270ca4d4", "release": {"plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"release": "major", "breaking": true}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "build": "true", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --allowJs --noEmit test/test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "14.20.1", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^17.0.0", "typescript": "~4.7.3", "@types/readable-stream": "^2.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/bl_5.1.0_1666093986198_0.9488209612512704", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "bl", "version": "6.0.0", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@6.0.0", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "1f10c18a3289d412053add35cca09019075cff2d", "tarball": "https://registry.npmjs.org/bl/-/bl-6.0.0.tgz", "fileCount": 14, "integrity": "sha512-Ik9BVIMdcWzSOCpzDv2XpQ4rJ4oZBuk3ck6MgiOv0EopdgtohN2uSCrrLlkH1Jf0KnpZZMBA3D0bUMbCdj/jgA==", "signatures": [{"sig": "MEYCIQC2DXN3yM/AFES0ugmUuFH7GQQUmMbkorY7tv6ddSTW6AIhAIFZgYSlw2/VzfsPDtbuNv4xZiQgeiKRM3M/vKu/DO5k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88064, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjT5ifACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOGQ/9FFcvTTDxsJaTPYa3b5q9x6iLLyUKtlIaC37ue1+mmla6j4xA\r\n5kzCbKCwygiPXsEwT6mrj6FkOBT8mH82TxUV+CkLna0pDX6pl47ItjEVIAeL\r\ngpj+G6NlB+v65yjU/SdXAo3d60Cze/Ykqu6YhlgfrHg0JbGoSAAVhSyUNWM3\r\nM5vQqeVnBMSprWBXwnkDe2IsTLMNA96/byASyI0KgLrd42AGSoYs3WzdBqUg\r\nPv0XWayPcPKtmhi4u7xYYFpB/K3i1z49p+jCVdDLsdZ2cbTrurWmqmjbWv0M\r\n85Ucwwm8ceRM2RQoheiuMla4VzFOKemXA5QiYugO1ubxXMAa8AvSRSlwNL18\r\nOfMQPupXHNWu7u07q1mgqpZOC5WRymzRpdzTFAIJqTykIx4KeUiqlx2HjXTN\r\nQwL8kEHYO89MFOarvU1kxYy4SlfgszbnWwNP3nEn0q4UPPQGtt+uyXkTWBn5\r\n3WYWyNDsluVfVU/sav9i2RgOB7lk1F2rgXjqxwsCC1kB8/GTTLn97imUIog8\r\n8YttGhaG6ZSDquo2FAmQyU5L2VAPhqfoeXH7pyXBou5Cwj+sggtMvA0XoEJ5\r\nNRH/aZNG6k2gslbk80JcEWXYKac2okGxTD+8mX745O4Gkbxd8RvUMruxOxX3\r\nJD3FEq3ubSuZOKvQ0Scyu8wcDLxbhmpaFsQ=\r\n=ip10\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "a59710d5abddf33e7ed71963b9071d53926156aa", "release": {"plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"release": "major", "breaking": true}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "build": "true", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --target esnext --moduleResolution node --allowJs --noEmit test/test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "14.20.1", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^17.0.0", "typescript": "~4.8.4", "@types/readable-stream": "^2.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/bl_6.0.0_1666160799411_0.15322696115013845", "host": "s3://npm-registry-packages"}}, "6.0.1": {"name": "bl", "version": "6.0.1", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@6.0.1", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "f35547d073b5f920512943a3fe0ec9ad5854fcb7", "tarball": "https://registry.npmjs.org/bl/-/bl-6.0.1.tgz", "fileCount": 14, "integrity": "sha512-zk1P1eAEBHhhB+4NfGxqmuV6NgwECnIoRgsOq2ObdEsmoFVIYzJ/Jjcgaj7JOY/8ekH27bIHSV4Si2T+evqu+Q==", "signatures": [{"sig": "MEUCID33MkummTrpCTDGhLHcZD77ZQMW9i8bvZUqyfmvBhDvAiEAlMDW9tIK29D7jU1TH+Vq88DBxjYHMQyUHqxp0SNsvpw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88838, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkE/5lACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqJTg//TRkxOfJyA+/QDbW0t45uBhaKPVcEuOenc1qGEFiM/ndQl5pD\r\nl6VNRLOECGcpMimgXheB2YbisWmTCLBV3ekgIvBdSsy9G1xcea8JKpe+/xoY\r\n/4ReKpF56HjlDpi8bne2gCOMVBH1v1MU2gC4f9hxgl0Zxid+cIc79/Lc+UCA\r\nb77A/MLWZctuvZk58Etu/zrwuGBEFbx3PoplYkxaTUZ4gs9LacCK2JYfMCZC\r\nJFpVlQw4F5Dz5oqBiKtCyJEJ1dIuQCb1I56WV81Ed/QAHwa8CosgXxwepeQe\r\n94vcqsVqVafKWRBI79mwDrB2hMY818t2ZCcgLqFY2pe3oYON/vyMBlGjA+BM\r\nnZuEQPgMMmRvba5Y0EUnXDtMhY9oBfiSil+Eg68CUiooiV43+iVy2E4+Z6e5\r\nP5sZmao9YCQyPyEaQzdG3yh2ymL1PhKbA4asjkY2JM1iN/1QnAp7Y/zahyrl\r\n4bq/gfCEkKl3rVxywnrC4YeybmS0ZbMyxWqCmRN32rVS4yMd11zh88wSM2JV\r\n6qB6C5aJMFWIaytQ2qfuG6La+f84UNtUzSuQBzuKKOqsrFJG70Ip7Y1gF7a+\r\nwB/m0XAEZ0KJ4Mbei7IRe21wpwUw10i0F1cdAmGmLXNSG10iTnAB41auqVfU\r\nV9j9yj9x8CiKihRXEjBtLNk6XSrNEGvwlFo=\r\n=Xg9W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "bea5abff74692cc2011eeb4f9b807631a30b3aaa", "release": {"plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"release": "major", "breaking": true}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "build": "true", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --target esnext --moduleResolution node --allowJs --noEmit test/test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "8.19.4", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "18.15.0", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^17.0.0", "typescript": "~5.0.2", "@types/readable-stream": "^2.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/bl_6.0.1_1679031909627_0.8943865185629178", "host": "s3://npm-registry-packages"}}, "6.0.2": {"name": "bl", "version": "6.0.2", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@6.0.2", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "b45308efeb30d3d453d5c67e47c5f0ff1fa237ac", "tarball": "https://registry.npmjs.org/bl/-/bl-6.0.2.tgz", "fileCount": 14, "integrity": "sha512-/ivXMGCGDI0EB4JI4zCqppp79j03vUgZz/zakw7TworE2NVjIuPxpL1Ti0InSsarKqFG5NLFreCBcCCSjtrTQw==", "signatures": [{"sig": "MEYCIQDGHV+njM+03YaetgbOWHOZAbIAuusZoE+wh+qhq1fLJAIhAMiWV3HOELuYM0viJ1Uf0cqAnMJrczoMzhrvJtraiGYj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89080}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "e2097d3cc28a07debe3d0486d0f1e02d41108546", "release": {"plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"release": "major", "breaking": true}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "build": "true", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --target esnext --moduleResolution node --allowJs --noEmit test/test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "9.6.7", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "18.16.0", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^17.0.0", "typescript": "~5.1.3", "@types/readable-stream": "^2.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/bl_6.0.2_1685933659377_0.35970367161714023", "host": "s3://npm-registry-packages"}}, "6.0.3": {"name": "bl", "version": "6.0.3", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@6.0.3", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "9534f75ee27c22dfb6d08871947ad25e5fb09907", "tarball": "https://registry.npmjs.org/bl/-/bl-6.0.3.tgz", "fileCount": 14, "integrity": "sha512-ZmReEQkPP4zOjCHVzGpXYLvf95/HnvwsNZ1sh2dhoy6OxqX9Sl3JF7UmoKXlXE40AjldnWlsSxvqDiDrgSCJDA==", "signatures": [{"sig": "MEQCIFeRAsYBJYrGH6rVV4nKmRAlxNJ/2iDOJZZDlQ06pJ4UAiAQi2cAtwrES0QAGuQ/MHsPyZRpdxN52UXkLYtYupICBA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89326}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "691b65b34e945bc034a995657630bbc17042b3d5", "release": {"plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"release": "major", "breaking": true}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "build": "true", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --target esnext --moduleResolution node --allowJs --noEmit test/test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "9.8.0", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^17.0.0", "typescript": "~5.1.3", "@types/readable-stream": "^2.3.13"}, "_npmOperationalInternal": {"tmp": "tmp/bl_6.0.3_1688714545612_0.16628856454437413", "host": "s3://npm-registry-packages"}}, "6.0.4": {"name": "bl", "version": "6.0.4", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@6.0.4", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "57eaf7ca2b5698dbd717bef3e05a14058257c28f", "tarball": "https://registry.npmjs.org/bl/-/bl-6.0.4.tgz", "fileCount": 14, "integrity": "sha512-GGacz5iDrO9gmt3Pa19rYe7AJECJDYAErDN+JuKJG6WhGZG26mE1Lbk4pabK+cMWzvpECzk4KAMfMaWD67l1SA==", "signatures": [{"sig": "MEQCIBYPLZKnN4+hFhiziK5dNvywanFBVs/XPp/Y/yheSF+bAiA9zV8VARsGpoCebK5AKyOuWbPxkazlSnejuxNW6lTpig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89580}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "43a41edbba15d4a713e911bf0cfb18314dbef85c", "release": {"plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"release": "major", "breaking": true}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "build": "true", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --target esnext --moduleResolution node --allowJs --noEmit test/test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "18.17.0", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^17.0.0", "typescript": "~5.1.3", "@types/readable-stream": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bl_6.0.4_1691386235061_0.28454104552471016", "host": "s3://npm-registry-packages"}}, "6.0.5": {"name": "bl", "version": "6.0.5", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@6.0.5", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "21423dc497bd6c9b374b2c86a6154d60affff501", "tarball": "https://registry.npmjs.org/bl/-/bl-6.0.5.tgz", "fileCount": 14, "integrity": "sha512-1Vh2BOuJSWLrfMF1RHwPQhxBnm0DTxA7C3b12hV4r9Phjr0lv/9KpW22YMT5JgNIsKcTLStl9909exCijdqnRg==", "signatures": [{"sig": "MEQCIGb9ZM/aGRXS0d5XvB4uJpqonaLjMkXSo+iw99FRiWNfAiBEfQhjcIONacisbFFCqpBvgUuZJjAQS7B7HhNJnlhXqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89826}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "c5e3d961e7bb729a1783a6d73e574c3df1ac1e93", "release": {"plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"release": "major", "breaking": true}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "build": "true", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --target esnext --moduleResolution node --allowJs --noEmit test/test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "18.17.0", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^17.0.0", "typescript": "~5.1.3", "@types/readable-stream": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bl_6.0.5_1692072584626_0.1143156550432396", "host": "s3://npm-registry-packages"}}, "6.0.6": {"name": "bl", "version": "6.0.6", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@6.0.6", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "a9373de876e3619c707d0fb15af011037cc380a7", "tarball": "https://registry.npmjs.org/bl/-/bl-6.0.6.tgz", "fileCount": 14, "integrity": "sha512-NqSAj8YLlID2Tbd7/ZB2Gj+N+2wwsAfDD38T1beT8KYytNwJEnxFTGOpvrGY0MTWq7VkWzuQ4PC3nBNgrMGWBQ==", "signatures": [{"sig": "MEQCIFZo+KQ3CE3IN8XoMOCTDhMYZKR3/WzmSVNYOWfRVmWsAiBMm06COgsKNRMwa8fP0Vr3CbeeqtRtK+wvVUuI7ze/zw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90072}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "a98feaaa1d17a70c4bcba5c005708455f2438bf1", "release": {"plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"release": "major", "breaking": true}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "build": "true", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --target esnext --moduleResolution node --allowJs --noEmit test/test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "18.17.0", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^17.0.0", "typescript": "~5.1.3", "@types/readable-stream": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bl_6.0.6_1692321349078_0.16705192267064173", "host": "s3://npm-registry-packages"}}, "6.0.7": {"name": "bl", "version": "6.0.7", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@6.0.7", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "1ee70a88044ad3ca9219a6ff94206a193dd4c39d", "tarball": "https://registry.npmjs.org/bl/-/bl-6.0.7.tgz", "fileCount": 14, "integrity": "sha512-9FNh0IvlWSU5C9BCDhw0IovmhuqevzBX1AME7BdFHNDMfOju4NmwRWoBrfz5Srs+JNBhxfjrPLxZSnDotgSs9A==", "signatures": [{"sig": "MEMCIA+sL5EW0bs5W5/hQQQD26KEeT64541sBP7T6P3LeqoAAh8wniZS3gBGEnTQOGivIcpBU+O0Qpo/YfcGcFcNB8Yv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90314}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "1559d9e65a6327ba8e154e1f1515b63dfa606d0b", "release": {"plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"release": "major", "breaking": true}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "build": "true", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --target esnext --moduleResolution node --allowJs --noEmit test/test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^17.0.0", "typescript": "~5.2.2", "@types/readable-stream": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bl_6.0.7_1692924527524_0.83648917956396", "host": "s3://npm-registry-packages"}}, "6.0.8": {"name": "bl", "version": "6.0.8", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@6.0.8", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "737d57f2d1e25b9ca45e0f4c5d84409731a86c04", "tarball": "https://registry.npmjs.org/bl/-/bl-6.0.8.tgz", "fileCount": 14, "integrity": "sha512-HCRq8z0+3vrGCjEKrbnK6blpDZ1xzhfZKCCuyvPC7upGcfXZSmaCumpVao/jC8o1hs/fOqJoCSPMabl+CQTPXg==", "signatures": [{"sig": "MEYCIQCL0ikbsYx6856dNPm4vmjlIYfL154lWmPDGnDj1mTj2QIhAMbKAAk6QIYWAYZUfjlQGk8hiFYjSPBpk7hf9hJv1WZl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90695}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "dedeaf6166def7bad8600a848926a574d3a3d6d3", "release": {"plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"release": "major", "breaking": true}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "build": "true", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --target esnext --moduleResolution node --allowJs --noEmit test/test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "10.2.1", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^17.0.0", "typescript": "~5.2.2", "@types/readable-stream": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bl_6.0.8_1698192579280_0.12315336394551779", "host": "s3://npm-registry-packages"}}, "6.0.9": {"name": "bl", "version": "6.0.9", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@6.0.9", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "df8fcb2ef7be2e5ee8f65afa493502914e0d816f", "tarball": "https://registry.npmjs.org/bl/-/bl-6.0.9.tgz", "fileCount": 14, "integrity": "sha512-Vh+M9HMfeTST9rkkQ1utRnOeABNcBO3i0dJMFkenCv7JIp76XWx8uQOGpaXyXVyenrLDZsdAHXbf0Cz18Eb0fw==", "signatures": [{"sig": "MEYCIQDsxsJCbKjlipUIYgKt/No7q5MgOVmD+Juy2RDetuEO/QIhAPEKqRsEum6vq68HBPtdwXmKZ2AR+oPJTJB5KfnQZ4Jq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90937}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "8d72ce7740f7f4f6c626e4670a3b7997ab4df7e1", "release": {"plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"release": "major", "breaking": true}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "build": "true", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --target esnext --moduleResolution node --allowJs --noEmit test/test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "18.18.2", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^17.0.0", "typescript": "~5.3.2", "@types/readable-stream": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bl_6.0.9_1701051686240_0.14443010870741868", "host": "s3://npm-registry-packages"}}, "6.0.10": {"name": "bl", "version": "6.0.10", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@6.0.10", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "91865ef0f07c9f2c41c99dc56189b96e036b921a", "tarball": "https://registry.npmjs.org/bl/-/bl-6.0.10.tgz", "fileCount": 14, "integrity": "sha512-F14DFhDZfxtVm2FY0k9kG2lWAwzZkO9+jX3Ytuoy/V0E1/5LBuBzzQHXAjqpxXEDIpmTPZZf5GVIGPQcLxFpaA==", "signatures": [{"sig": "MEUCIQDOxoOuvg1gzKSiJISg+ikYaYSwFY/NzWlzBxz93Zl4LAIgKVjnSLsp07WSJXAjpLT28Cita1m4m7hrwdED5f1YLjA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91186}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "715cbf9ee0908dd0af5b19cb1ff522ddda1d153e", "release": {"plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"release": "major", "breaking": true}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "build": "true", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --target esnext --moduleResolution node --allowJs --noEmit test/test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "10.2.5", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "18.19.0", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^17.0.0", "typescript": "~5.3.2", "@types/readable-stream": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/bl_6.0.10_1704153091804_0.3620189055578351", "host": "s3://npm-registry-packages"}}, "6.0.11": {"name": "bl", "version": "6.0.11", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@6.0.11", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "e264651d5f4adaaedf1813c94642cb24d3d30aeb", "tarball": "https://registry.npmjs.org/bl/-/bl-6.0.11.tgz", "fileCount": 14, "integrity": "sha512-Ok/NWrEA0mlEEbWzckkZVLq6Nv1m2xZ+i9Jq5hZ9Ph/YEcP5dExqls9wUzpluhQRPzdeT8oZNOXAytta6YN8pQ==", "signatures": [{"sig": "MEUCIAw5mIibKSYDbCTC5LNweL6UhMXB0rtr+5eoaPLR1GV7AiEAyCBTIiH3TBpt88eBEf70mUT6WmRdlX2Fk/hqH1oDHbw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91553}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "53d0bc9bbe6cdfdefd93983e859cf7a2ae3443d8", "release": {"plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"release": "major", "breaking": true}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "build": "true", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --target esnext --moduleResolution node --allowJs --noEmit test/test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "10.4.0", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "20.11.0", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0", "@types/readable-stream": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^17.0.0", "typescript": "~5.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/bl_6.0.11_1707363905145_0.5977677361428171", "host": "s3://npm-registry-packages"}}, "6.0.12": {"name": "bl", "version": "6.0.12", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@6.0.12", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "77c35b96e13aeff028496c798b75389ddee9c7f8", "tarball": "https://registry.npmjs.org/bl/-/bl-6.0.12.tgz", "fileCount": 14, "integrity": "sha512-EnEYHilP93oaOa2MnmNEjAcovPS3JlQZOyzGXi3EyEpPhm9qWvdDp7BmAVEVusGzp8LlwQK56Av+OkDoRjzE0w==", "signatures": [{"sig": "MEUCIHLjiE6SHwyRLDoVWh8eIU3+g9nz0C1F4DCNw75SKC+kAiEA8ZzwpuEycmXdpLR/m7Mc87sV8DFDkkAKY8sfsnloCQ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91798}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "afa4b71485ce48e219063af18daba0c97f21e98c", "release": {"plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"release": "major", "breaking": true}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "build": "true", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --target esnext --moduleResolution node --allowJs --noEmit test/test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "10.5.0", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0", "@types/readable-stream": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^17.0.0", "typescript": "~5.4.2"}, "_npmOperationalInternal": {"tmp": "tmp/bl_6.0.12_1709786082492_0.9708433421604572", "host": "s3://npm-registry-packages"}}, "6.0.13": {"name": "bl", "version": "6.0.13", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@6.0.13", "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "dc5f288d3f849771bb6112b29477abee4c0a9d96", "tarball": "https://registry.npmjs.org/bl/-/bl-6.0.13.tgz", "fileCount": 14, "integrity": "sha512-tMncAcpsyjZgAVbVFupVIaB2xud13xxT59fdHkuszY2jdZkqIWfpQdmII1fOe3kOGAz0mNLTIHEm+KxpYsQKKg==", "signatures": [{"sig": "MEUCIB3vvTJBXOox0R4vfRtH9JJr9xwU6Xf471GlYjyVSdsqAiEAlrmr+sRK3BDPFXXRAIG7UekECnrsbPv7Umf2uHoaVH8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92042}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "8db20b5da6b55b82086592e8b34b2236f844a793", "release": {"plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"release": "major", "breaking": true}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "build": "true", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --target esnext --moduleResolution node --allowJs --noEmit test/test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "20.14.0", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0", "@types/readable-stream": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^17.0.0", "typescript": "~5.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/bl_6.0.13_1718933895468_0.42799172886933823", "host": "s3://npm-registry-packages"}}, "6.0.14": {"name": "bl", "version": "6.0.14", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@6.0.14", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "b9ae9862118a3d2ebec999c5318466012314f96c", "tarball": "https://registry.npmjs.org/bl/-/bl-6.0.14.tgz", "fileCount": 14, "integrity": "sha512-TJfbvGdL7KFGxTsEbsED7avqpFdY56q9IW0/aiytyheJzxST/+Io6cx/4Qx0K2/u0BPRDs65mjaQzYvMZeNocQ==", "signatures": [{"sig": "MEUCIQCGYAr9NQ5clERUwK2oK8yMKqlhSxIfdKyTSGIichJaawIgfo/yGjPHhOkOLCpUfWetnofkAe8VGsBbnKPRZx8a0eE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92290}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "b67da0bef403232f48401e2ffad9d8923e68f56b", "release": {"plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"release": "major", "breaking": true}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "build": "true", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --target esnext --moduleResolution node --allowJs --noEmit test/test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "20.15.0", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0", "@types/readable-stream": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^17.0.0", "typescript": "~5.5.2"}, "_npmOperationalInternal": {"tmp": "tmp/bl_6.0.14_1720577241408_0.2581728595248578", "host": "s3://npm-registry-packages"}}, "6.0.15": {"name": "bl", "version": "6.0.15", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@6.0.15", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "eeee4af50c6f16eb08fc5ddbca6f2d9f2ff12d78", "tarball": "https://registry.npmjs.org/bl/-/bl-6.0.15.tgz", "fileCount": 14, "integrity": "sha512-RGhjD1XCPS7ZdAH6cEJVaR3gLV4KJP2hvkQ49AH5kwScjiyd0jBM8RsP4oHKzcx+kNCON9752zPeRnuv0HHwzw==", "signatures": [{"sig": "MEUCIQDvkxWvx2YfU8hy2qE7LLuFuXM7qrSajsUDRe5YV11LoAIgHEyx31MxXuMrCZAss0MkTh/fai90p3yUYySfQg0Hirs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92534}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "074b33ae82bad1ca4d00c2a94cda09a07685205c", "release": {"plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"release": "major", "breaking": true}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "build": "true", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --target esnext --moduleResolution node --allowJs --noEmit test/test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0", "@types/readable-stream": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^17.0.0", "typescript": "~5.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/bl_6.0.15_1725928010824_0.49268367740874996", "host": "s3://npm-registry-packages"}}, "6.0.16": {"name": "bl", "version": "6.0.16", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@6.0.16", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "29b190f1a754e2d168de3dc8c74ed8d12bf78e6e", "tarball": "https://registry.npmjs.org/bl/-/bl-6.0.16.tgz", "fileCount": 14, "integrity": "sha512-V/kz+z2Mx5/6qDfRCilmrukUXcXuCoXKg3/3hDvzKKoSUx8CJKudfIoT29XZc3UE9xBvxs5qictiHdprwtteEg==", "signatures": [{"sig": "MEUCIEWDk+aPGgrZG/fe1fhaeFnpgPAaPQ/uoyHUtBOp/RZLAiEAhRVXSAh7bMXq7aE8CRSxdMiKdhMKYk/g8dZjSXIqDFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92782}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "2dd9100c82a1d691d193a65bd2885c5fe3ec4266", "release": {"plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"release": "major", "breaking": true}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "build": "true", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --target esnext --moduleResolution node --allowJs --noEmit test/test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "20.17.0", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0", "@types/readable-stream": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^17.0.0", "typescript": "~5.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/bl_6.0.16_1727246520854_0.25913115088891936", "host": "s3://npm-registry-packages"}}, "6.0.17": {"name": "bl", "version": "6.0.17", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@6.0.17", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "c39d6540c5d61cd1b50cad8bf266e290da5a92c8", "tarball": "https://registry.npmjs.org/bl/-/bl-6.0.17.tgz", "fileCount": 14, "integrity": "sha512-JefHDTdhP1If6svGja+TOfFkGosSrx+WZ9OMrqBcrVNX9DkCS2S7zgwlIU670GG3cs6AJH74iaf3YE0Ufi9MBg==", "signatures": [{"sig": "MEQCIA+W8V88YJeQbzl77+HmCqV880Yuf0paHyLScfWxDtWNAiBLeq6Dm5I+ilhrFGRlZ4I4/lNyz5JAIeAbnKvkoHUS9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93016}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "da1fcf7b804bfdbdcce36f8598aa9643583cf262", "release": {"plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"release": "major", "breaking": true}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "build": "true", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --target esnext --moduleResolution node --allowJs --noEmit --skipLibCheck test/test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0", "@types/readable-stream": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^17.0.0", "typescript": "~5.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/bl_6.0.17_1735528060806_0.2683525114007619", "host": "s3://npm-registry-packages-npm-production"}}, "6.0.18": {"name": "bl", "version": "6.0.18", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@6.0.18", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "eaf008d00fbb53eaade7ffc2fb8c527b9f66ead5", "tarball": "https://registry.npmjs.org/bl/-/bl-6.0.18.tgz", "fileCount": 14, "integrity": "sha512-2k76XmWCuvu9HTvu3tFOl5HDdCH0wLZ/jHYva/LBVJmc9oX8yUtNQjxrFmbTdXsCSmIxwVTANZPNDfMQrvHFUw==", "signatures": [{"sig": "MEQCIDf9P62mNOfrIJV+T0hVatOoaAPPobttLm3zjzNN5NRTAiAgy8GRHfhKb+6jVTTexxAKucbuZZwJ9tMUnnqjXyXEtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93454}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "e0b40392a722b3294b5cbcc513b4619fdf0fb215", "release": {"plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"release": "major", "breaking": true}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "build": "true", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --target esnext --moduleResolution node --allowJs --noEmit --skipLibCheck test/test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "22.12.0", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0", "@types/readable-stream": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^17.0.0", "typescript": "~5.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/bl_6.0.18_1735528267022_0.8001867729518946", "host": "s3://npm-registry-packages-npm-production"}}, "6.0.19": {"name": "bl", "version": "6.0.19", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@6.0.19", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "c4487282bb18768186f02fec6be1be3b5b93677b", "tarball": "https://registry.npmjs.org/bl/-/bl-6.0.19.tgz", "fileCount": 14, "integrity": "sha512-4Ay3A3oDfGg3GGirhl4s62ebtnk0pJZA5mLp672MPKOQXsWvXjEF4dqdXySjJIs7b9OVr/O8aOo0Lm+xdjo2JA==", "signatures": [{"sig": "MEUCIHRG6ghTNzKaxrW61Br2BPPvfeUbIohFzrFnmpPNjyCUAiEA2HoclOF1K9XpmhObw8ltcChnM62PrAoZwFAMNgH06kY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 93751}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "92d9b7ce3b236323bfd70d37d6ffdfc77a199d97", "release": {"plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"release": "major", "breaking": true}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "build": "true", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --target esnext --moduleResolution node --allowJs --noEmit --skipLibCheck test/test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "22.13.0", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0", "@types/readable-stream": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^17.0.0", "typescript": "~5.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/bl_6.0.19_1738041073269_0.7801886400678808", "host": "s3://npm-registry-packages-npm-production"}}, "6.0.20": {"name": "bl", "version": "6.0.20", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "_id": "bl@6.0.20", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "homepage": "https://github.com/rvagg/bl", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "dist": {"shasum": "70527afc27d2158b88db55a99ad8813ab7d82914", "tarball": "https://registry.npmjs.org/bl/-/bl-6.0.20.tgz", "fileCount": 14, "integrity": "sha512-JMP0loH6ApbpT4Aa9oU5NqAkdDvcyc8koeuK8i5mYoBCVj3XCXG0uweGNN2m6DqaCO2yRHdm+MjCeTsR5VsmcA==", "signatures": [{"sig": "MEQCIDPmjxL2ELxqOhfCfkko6GK6LJGFmuZS0PbNu0BCxDKJAiBvCnzx/lJo2ujVQ6CbaowdanvDEnnkG1Gj/8J3i0fckw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 94054}, "main": "bl.js", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "gitHead": "1585decb38b0c49cef92eb115aa1a3d2b06a0e4a", "release": {"plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"release": "major", "breaking": true}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"], "branches": ["master"]}, "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "build": "true", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --target esnext --moduleResolution node --allowJs --noEmit --skipLibCheck test/test.js"}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/rvagg/bl.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "directories": {}, "_nodeVersion": "22.14.0", "dependencies": {"buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0", "@types/readable-stream": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"tape": "^5.2.2", "faucet": "~0.0.1", "standard": "^17.0.0", "typescript": "~5.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/bl_6.0.20_1740964185533_0.47174065985856384", "host": "s3://npm-registry-packages-npm-production"}}, "6.1.0": {"name": "bl", "version": "6.1.0", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "license": "MIT", "main": "bl.js", "scripts": {"lint": "standard *.js test/*.js", "test": "npm run lint && npm run test:types && node test/test.js | faucet", "test:ci": "npm run lint && node test/test.js && npm run test:types", "test:types": "tsc --target esnext --moduleResolution node --allowJs --noEmit --skipLibCheck test/test.js", "build": "true"}, "repository": {"type": "git", "url": "git+https://github.com/rvagg/bl.git"}, "homepage": "https://github.com/rvagg/bl", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "dependencies": {"@types/readable-stream": "^4.0.0", "buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0"}, "devDependencies": {"faucet": "~0.0.1", "standard": "^17.0.0", "tape": "^5.2.2", "typescript": "~5.8.2"}, "release": {"branches": ["master"], "plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"breaking": true, "release": "major"}, {"revert": true, "release": "patch"}, {"type": "feat", "release": "minor"}, {"type": "fix", "release": "patch"}, {"type": "chore", "release": "patch"}, {"type": "docs", "release": "patch"}, {"type": "test", "release": "patch"}, {"scope": "no-release", "release": false}]}], ["@semantic-release/release-notes-generator", {"preset": "conventionalcommits", "presetConfig": {"types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {"type": "chore", "section": "Trivial Changes"}, {"type": "docs", "section": "Trivial Changes"}, {"type": "test", "section": "Tests"}]}}], "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"]}, "_id": "bl@6.1.0", "gitHead": "db8475668cc42917253bd767c7f58d350867f870", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "_nodeVersion": "22.14.0", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-ClDyJGQkc8ZtzdAAbAwBmhMSpwN/sC9HA8jxdYm6nVUbCfZbe2mgza4qh7AuEYyEPB/c4Kznf9s66bnsKMQDjw==", "shasum": "cc35ce7a2e8458caa8c8fb5deeed6537b73e4504", "tarball": "https://registry.npmjs.org/bl/-/bl-6.1.0.tgz", "fileCount": 14, "unpackedSize": 98342, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDoM4btZyTdhCzB3D5tDEuguCo9bZMZfCMTbFFzSGlL2gIgeDtG312CPG89m7Y6CVAKp03i16olXHW7j7ygsD7sOeQ="}]}, "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/bl_6.1.0_1741717349683_0.4316382749722556"}, "_hasShrinkwrap": false}}, "time": {"created": "2013-06-15T12:35:42.237Z", "modified": "2025-03-11T18:22:30.092Z", "0.0.0": "2013-06-15T12:35:47.051Z", "0.1.0": "2013-06-16T01:04:03.168Z", "0.1.1": "2013-06-16T04:59:00.527Z", "0.2.0": "2013-07-23T08:11:36.873Z", "0.3.0": "2013-08-07T23:39:24.286Z", "0.4.0": "2013-08-08T13:51:22.510Z", "0.4.1": "2013-08-08T14:02:15.734Z", "0.4.2": "2013-10-02T09:48:29.941Z", "0.5.0": "2013-10-10T21:47:31.190Z", "0.6.0": "2013-12-03T03:23:07.669Z", "0.7.0": "2014-01-09T09:41:05.970Z", "0.8.0": "2014-04-07T09:56:19.606Z", "0.8.1": "2014-06-27T02:30:06.185Z", "0.8.2": "2014-06-28T04:45:15.739Z", "0.9.0": "2014-07-22T12:26:24.857Z", "0.9.1": "2014-08-20T10:17:06.829Z", "0.9.2": "2014-09-10T01:49:52.743Z", "0.9.3": "2014-09-10T01:54:50.700Z", "0.9.4": "2015-01-19T23:52:52.258Z", "1.0.0": "2015-06-21T06:35:02.302Z", "1.0.1": "2016-01-19T05:32:39.741Z", "0.9.5": "2016-01-19T05:36:26.056Z", "1.0.2": "2016-02-03T20:53:32.695Z", "1.0.3": "2016-02-11T10:41:22.390Z", "1.1.0": "2016-02-12T02:58:34.031Z", "1.1.1": "2016-02-12T03:02:29.633Z", "1.1.2": "2016-02-12T03:10:23.327Z", "1.2.0": "2016-12-22T07:33:27.682Z", "1.2.1": "2017-05-01T08:34:43.085Z", "1.2.2": "2018-03-21T21:59:58.272Z", "2.0.0": "2018-05-15T08:48:06.878Z", "2.0.1": "2018-06-14T13:50:30.662Z", "2.1.0": "2018-10-04T11:08:43.234Z", "2.1.1": "2018-10-08T05:04:42.845Z", "2.1.2": "2018-10-12T18:26:57.507Z", "2.2.0": "2019-02-03T08:28:43.245Z", "3.0.0": "2019-02-28T10:18:20.693Z", "4.0.0": "2019-09-19T02:09:58.708Z", "4.0.1": "2020-03-04T16:00:30.974Z", "4.0.2": "2020-03-18T03:27:27.970Z", "4.0.3": "2020-08-26T08:15:27.747Z", "3.0.1": "2020-08-26T08:22:04.327Z", "2.2.1": "2020-08-26T08:25:29.499Z", "1.2.3": "2020-09-03T09:52:16.710Z", "4.0.4": "2021-02-04T10:58:40.420Z", "4.1.0": "2021-02-09T10:37:35.452Z", "5.0.0": "2021-04-02T16:42:04.941Z", "5.1.0": "2022-10-18T11:53:06.393Z", "6.0.0": "2022-10-19T06:26:39.590Z", "6.0.1": "2023-03-17T05:45:09.772Z", "6.0.2": "2023-06-05T02:54:19.543Z", "6.0.3": "2023-07-07T07:22:25.850Z", "6.0.4": "2023-08-07T05:30:35.247Z", "6.0.5": "2023-08-15T04:09:44.785Z", "6.0.6": "2023-08-18T01:15:49.282Z", "6.0.7": "2023-08-25T00:48:47.778Z", "6.0.8": "2023-10-25T00:09:39.555Z", "6.0.9": "2023-11-27T02:21:26.393Z", "6.0.10": "2024-01-01T23:51:31.959Z", "6.0.11": "2024-02-08T03:45:05.398Z", "6.0.12": "2024-03-07T04:34:42.636Z", "6.0.13": "2024-06-21T01:38:15.631Z", "6.0.14": "2024-07-10T02:07:21.660Z", "6.0.15": "2024-09-10T00:26:50.992Z", "6.0.16": "2024-09-25T06:42:01.005Z", "6.0.17": "2024-12-30T03:07:41.019Z", "6.0.18": "2024-12-30T03:11:07.205Z", "6.0.19": "2025-01-28T05:11:13.439Z", "6.0.20": "2025-03-03T01:09:45.727Z", "6.1.0": "2025-03-11T18:22:29.926Z"}, "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "license": "MIT", "homepage": "https://github.com/rvagg/bl", "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "repository": {"type": "git", "url": "git+https://github.com/rvagg/bl.git"}, "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "maintainers": [{"name": "matteo.collina", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}], "readme": "# bl *(BufferList)*\n\n![Build Status](https://img.shields.io/github/actions/workflow/status/rvagg/bl/test-and-release.yml?branch=master)\n\n**A Node.js Buffer list collector, reader and streamer thingy.**\n\n[![NPM](https://nodei.co/npm/bl.svg)](https://nodei.co/npm/bl/)\n\n**bl** is a storage object for collections of Node Buffers, exposing them with the main Buffer readable API. Also works as a duplex stream so you can collect buffers from a stream that emits them and emit buffers to a stream that consumes them!\n\nThe original buffers are kept intact and copies are only done as necessary. Any reads that require the use of a single original buffer will return a slice of that buffer only (which references the same memory as the original buffer). Reads that span buffers perform concatenation as required and return the results transparently.\n\n```js\nconst { BufferList } = require('bl')\n\nconst bl = new BufferList()\nbl.append(Buffer.from('abcd'))\nbl.append(Buffer.from('efg'))\nbl.append('hi')                     // bl will also accept & convert Strings\nbl.append(Buffer.from('j'))\nbl.append(Buffer.from([ 0x3, 0x4 ]))\n\nconsole.log(bl.length) // 12\n\nconsole.log(bl.slice(0, 10).toString('ascii')) // 'abcdefghij'\nconsole.log(bl.slice(3, 10).toString('ascii')) // 'defghij'\nconsole.log(bl.slice(3, 6).toString('ascii'))  // 'def'\nconsole.log(bl.slice(3, 8).toString('ascii'))  // 'defgh'\nconsole.log(bl.slice(5, 10).toString('ascii')) // 'fghij'\n\nconsole.log(bl.indexOf('def')) // 3\nconsole.log(bl.indexOf('asdf')) // -1\n\n// or just use toString!\nconsole.log(bl.toString())               // 'abcdefghij\\u0003\\u0004'\nconsole.log(bl.toString('ascii', 3, 8))  // 'defgh'\nconsole.log(bl.toString('ascii', 5, 10)) // 'fghij'\n\n// other standard Buffer readables\nconsole.log(bl.readUInt16BE(10)) // 0x0304\nconsole.log(bl.readUInt16LE(10)) // 0x0403\n```\n\nGive it a callback in the constructor and use it just like **[concat-stream](https://github.com/maxogden/node-concat-stream)**:\n\n```js\nconst { BufferListStream } = require('bl')\nconst fs = require('fs')\n\nfs.createReadStream('README.md')\n  .pipe(BufferListStream((err, data) => { // note 'new' isn't strictly required\n    // `data` is a complete Buffer object containing the full data\n    console.log(data.toString())\n  }))\n```\n\nNote that when you use the *callback* method like this, the resulting `data` parameter is a concatenation of all `Buffer` objects in the list. If you want to avoid the overhead of this concatenation (in cases of extreme performance consciousness), then avoid the *callback* method and just listen to `'end'` instead, like a standard Stream.\n\nOr to fetch a URL using [hyperquest](https://github.com/substack/hyperquest) (should work with [request](http://github.com/mikeal/request) and even plain Node http too!):\n\n```js\nconst hyperquest = require('hyperquest')\nconst { BufferListStream } = require('bl')\n\nconst url = 'https://raw.github.com/rvagg/bl/master/README.md'\n\nhyperquest(url).pipe(BufferListStream((err, data) => {\n  console.log(data.toString())\n}))\n```\n\nOr, use it as a readable stream to recompose a list of Buffers to an output source:\n\n```js\nconst { BufferListStream } = require('bl')\nconst fs = require('fs')\n\nvar bl = new BufferListStream()\nbl.append(Buffer.from('abcd'))\nbl.append(Buffer.from('efg'))\nbl.append(Buffer.from('hi'))\nbl.append(Buffer.from('j'))\n\nbl.pipe(fs.createWriteStream('gibberish.txt'))\n```\n\n## API\n\n  * <a href=\"#ctor\"><code><b>new BufferList([ buf ])</b></code></a>\n  * <a href=\"#isBufferList\"><code><b>BufferList.isBufferList(obj)</b></code></a>\n  * <a href=\"#length\"><code>bl.<b>length</b></code></a>\n  * <a href=\"#append\"><code>bl.<b>append(buffer)</b></code></a>\n  * <a href=\"#prepend\"><code>bl.<b>append(buffer)</b></code></a>\n  * <a href=\"#get\"><code>bl.<b>get(index)</b></code></a>\n  * <a href=\"#indexOf\"><code>bl.<b>indexOf(value[, byteOffset][, encoding])</b></code></a>\n  * <a href=\"#slice\"><code>bl.<b>slice([ start[, end ] ])</b></code></a>\n  * <a href=\"#shallowSlice\"><code>bl.<b>shallowSlice([ start[, end ] ])</b></code></a>\n  * <a href=\"#copy\"><code>bl.<b>copy(dest, [ destStart, [ srcStart [, srcEnd ] ] ])</b></code></a>\n  * <a href=\"#duplicate\"><code>bl.<b>duplicate()</b></code></a>\n  * <a href=\"#consume\"><code>bl.<b>consume(bytes)</b></code></a>\n  * <a href=\"#toString\"><code>bl.<b>toString([encoding, [ start, [ end ]]])</b></code></a>\n  * <a href=\"#readXX\"><code>bl.<b>readDoubleBE()</b></code>, <code>bl.<b>readDoubleLE()</b></code>, <code>bl.<b>readFloatBE()</b></code>, <code>bl.<b>readFloatLE()</b></code>, <code>bl.<b>readBigInt64BE()</b></code>, <code>bl.<b>readBigInt64LE()</b></code>, <code>bl.<b>readBigUInt64BE()</b></code>, <code>bl.<b>readBigUInt64LE()</b></code>, <code>bl.<b>readInt32BE()</b></code>, <code>bl.<b>readInt32LE()</b></code>, <code>bl.<b>readUInt32BE()</b></code>, <code>bl.<b>readUInt32LE()</b></code>, <code>bl.<b>readInt16BE()</b></code>, <code>bl.<b>readInt16LE()</b></code>, <code>bl.<b>readUInt16BE()</b></code>, <code>bl.<b>readUInt16LE()</b></code>, <code>bl.<b>readInt8()</b></code>, <code>bl.<b>readUInt8()</b></code></a>\n  * <a href=\"#ctorStream\"><code><b>new BufferListStream([ callback ])</b></code></a>\n  * <a href=\"#getBuffers\"><code>bl.<b>getBuffers()</b></code></a>\n  \n--------------------------------------------------------\n<a name=\"ctor\"></a>\n### new BufferList([ Buffer | Buffer array | BufferList | BufferList array | String ])\nNo arguments are _required_ for the constructor, but you can initialise the list by passing in a single `Buffer` object or an array of `Buffer` objects.\n\n`new` is not strictly required, if you don't instantiate a new object, it will be done automatically for you so you can create a new instance simply with:\n\n```js\nconst { BufferList } = require('bl')\nconst bl = BufferList()\n\n// equivalent to:\n\nconst { BufferList } = require('bl')\nconst bl = new BufferList()\n```\n\n--------------------------------------------------------\n<a name=\"isBufferList\"></a>\n### BufferList.isBufferList(obj)\nDetermines if the passed object is a `BufferList`. It will return `true` if the passed object is an instance of `BufferList` **or** `BufferListStream` and `false` otherwise.\n\nN.B. this won't return `true` for `BufferList` or `BufferListStream` instances created by versions of this library before this static method was added.\n\n--------------------------------------------------------\n<a name=\"length\"></a>\n### bl.length\nGet the length of the list in bytes. This is the sum of the lengths of all of the buffers contained in the list, minus any initial offset for a semi-consumed buffer at the beginning. Should accurately represent the total number of bytes that can be read from the list.\n\n--------------------------------------------------------\n<a name=\"append\"></a>\n### bl.append(Buffer | Buffer array | BufferList | BufferList array | String)\n`append(buffer)` adds an additional buffer or BufferList to the internal list. `this` is returned so it can be chained.\n\n--------------------------------------------------------\n<a name=\"prepend\"></a>\n### bl.prepend(Buffer | Buffer array | BufferList | BufferList array | String)\n`prepend(buffer)` adds an additional buffer or BufferList at the beginning of the internal list. `this` is returned so it can be chained.\n\n--------------------------------------------------------\n<a name=\"get\"></a>\n### bl.get(index)\n`get()` will return the byte at the specified index.\n\n--------------------------------------------------------\n<a name=\"indexOf\"></a>\n### bl.indexOf(value[, byteOffset][, encoding])\n`get()` will return the byte at the specified index.\n`indexOf()` method returns the first index at which a given element can be found in the BufferList, or -1 if it is not present.\n\n--------------------------------------------------------\n<a name=\"slice\"></a>\n### bl.slice([ start, [ end ] ])\n`slice()` returns a new `Buffer` object containing the bytes within the range specified. Both `start` and `end` are optional and will default to the beginning and end of the list respectively.\n\nIf the requested range spans a single internal buffer then a slice of that buffer will be returned which shares the original memory range of that Buffer. If the range spans multiple buffers then copy operations will likely occur to give you a uniform Buffer.\n\n--------------------------------------------------------\n<a name=\"shallowSlice\"></a>\n### bl.shallowSlice([ start, [ end ] ])\n`shallowSlice()` returns a new `BufferList` object containing the bytes within the range specified. Both `start` and `end` are optional and will default to the beginning and end of the list respectively.\n\nNo copies will be performed. All buffers in the result share memory with the original list.\n\n--------------------------------------------------------\n<a name=\"copy\"></a>\n### bl.copy(dest, [ destStart, [ srcStart [, srcEnd ] ] ])\n`copy()` copies the content of the list in the `dest` buffer, starting from `destStart` and containing the bytes within the range specified with `srcStart` to `srcEnd`. `destStart`, `start` and `end` are optional and will default to the beginning of the `dest` buffer, and the beginning and end of the list respectively.\n\n--------------------------------------------------------\n<a name=\"duplicate\"></a>\n### bl.duplicate()\n`duplicate()` performs a **shallow-copy** of the list. The internal Buffers remains the same, so if you change the underlying Buffers, the change will be reflected in both the original and the duplicate. This method is needed if you want to call `consume()` or `pipe()` and still keep the original list.Example:\n\n```js\nvar bl = new BufferListStream()\n\nbl.append('hello')\nbl.append(' world')\nbl.append('\\n')\n\nbl.duplicate().pipe(process.stdout, { end: false })\n\nconsole.log(bl.toString())\n```\n\n--------------------------------------------------------\n<a name=\"consume\"></a>\n### bl.consume(bytes)\n`consume()` will shift bytes *off the start of the list*. The number of bytes consumed don't need to line up with the sizes of the internal Buffers&mdash;initial offsets will be calculated accordingly in order to give you a consistent view of the data.\n\n--------------------------------------------------------\n<a name=\"toString\"></a>\n### bl.toString([encoding, [ start, [ end ]]])\n`toString()` will return a string representation of the buffer. The optional `start` and `end` arguments are passed on to `slice()`, while the `encoding` is passed on to `toString()` of the resulting Buffer. See the [Buffer#toString()](http://nodejs.org/docs/latest/api/buffer.html#buffer_buf_tostring_encoding_start_end) documentation for more information.\n\n--------------------------------------------------------\n<a name=\"readXX\"></a>\n### bl.readDoubleBE(), bl.readDoubleLE(), bl.readFloatBE(), bl.readFloatLE(), bl.readBigInt64BE(), bl.readBigInt64LE(), bl.readBigUInt64BE(), bl.readBigUInt64LE(), bl.readInt32BE(), bl.readInt32LE(), bl.readUInt32BE(), bl.readUInt32LE(), bl.readInt16BE(), bl.readInt16LE(), bl.readUInt16BE(), bl.readUInt16LE(), bl.readInt8(), bl.readUInt8()\n\nAll of the standard byte-reading methods of the `Buffer` interface are implemented and will operate across internal Buffer boundaries transparently.\n\nSee the <b><code>[Buffer](http://nodejs.org/docs/latest/api/buffer.html)</code></b> documentation for how these work.\n\n--------------------------------------------------------\n<a name=\"ctorStream\"></a>\n### new BufferListStream([ callback | Buffer | Buffer array | BufferList | BufferList array | String ])\n**BufferListStream** is a Node **[Duplex Stream](http://nodejs.org/docs/latest/api/stream.html#stream_class_stream_duplex)**, so it can be read from and written to like a standard Node stream. You can also `pipe()` to and from a **BufferListStream** instance.\n\nThe constructor takes an optional callback, if supplied, the callback will be called with an error argument followed by a reference to the **bl** instance, when `bl.end()` is called (i.e. from a piped stream). This is a convenient method of collecting the entire contents of a stream, particularly when the stream is *chunky*, such as a network stream.\n\nNormally, no arguments are required for the constructor, but you can initialise the list by passing in a single `Buffer` object or an array of `Buffer` object.\n\n`new` is not strictly required, if you don't instantiate a new object, it will be done automatically for you so you can create a new instance simply with:\n\n```js\nconst { BufferListStream } = require('bl')\nconst bl = BufferListStream()\n\n// equivalent to:\n\nconst { BufferListStream } = require('bl')\nconst bl = new BufferListStream()\n```\n\nN.B. For backwards compatibility reasons, `BufferListStream` is the **default** export when you `require('bl')`:\n\n```js\nconst { BufferListStream } = require('bl')\n// equivalent to:\nconst BufferListStream = require('bl')\n```\n\n--------------------------------------------------------\n<a name=\"getBuffers\"></a>\n### bl.getBuffers()\n\n`getBuffers()` returns the internal list of buffers.\n\n\n## Contributors\n\n**bl** is brought to you by the following hackers:\n\n * [Rod Vagg](https://github.com/rvagg)\n * [Matteo Collina](https://github.com/mcollina)\n * [Jarett Cruger](https://github.com/jcrugzz)\n\n<a name=\"license\"></a>\n## License &amp; copyright\n\nCopyright (c) 2013-2019 bl contributors (listed above).\n\nbl is licensed under the MIT license. All rights not explicitly granted in the MIT license are reserved. See the included LICENSE.md file for more details.\n", "readmeFilename": "README.md", "users": {"amio": true, "detj": true, "dralc": true, "etsit": true, "syzer": true, "dudley": true, "dyohns": true, "matsgm": true, "nuwaio": true, "tzookb": true, "vladan": true, "davidcz": true, "deubaka": true, "flyslow": true, "geekwen": true, "jahhein": true, "maur1th": true, "studi11": true, "vinchik": true, "wenbing": true, "2dxgujun": true, "alanshaw": true, "cheng470": true, "coalesce": true, "cslasher": true, "danwyand": true, "dim_0628": true, "djamseed": true, "ernusame": true, "iamiurii": true, "koulmomo": true, "pnevares": true, "snehlsen": true, "troy0820": true, "artivilla": true, "chandimal": true, "fridurmus": true, "jabbalaci": true, "max_devjs": true, "pixeleate": true, "jaggedsoft": true, "leizongmin": true, "mickeyzhou": true, "nickleefly": true, "pengzhisun": true, "smithlamar": true, "stonestyle": true, "fiveisprime": true, "flumpus-dev": true, "italoacasas": true, "jhermsmeier": true, "rolandbrake": true, "soenkekluth": true, "uptownjimmy": true, "wangnan0610": true, "nickeltobias": true, "tobiasnickel": true, "diegorbaquero": true, "leobakerhytch": true, "markthethomas": true, "program247365": true, "squishleheimer": true, "jeffb_incontact": true, "nguyenmanhdat2903": true}}