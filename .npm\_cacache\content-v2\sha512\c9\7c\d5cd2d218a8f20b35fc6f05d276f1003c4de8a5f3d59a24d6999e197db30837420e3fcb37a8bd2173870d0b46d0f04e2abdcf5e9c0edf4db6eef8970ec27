{"_id": "is-stream", "_rev": "19-357fcf4198ba14e4ae60fa36fdcb224a", "name": "is-stream", "description": "Check if something is a Node.js stream", "dist-tags": {"latest": "4.0.1"}, "versions": {"1.0.0": {"name": "is-stream", "version": "1.0.0", "description": "Check if something is a Node.js stream", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/is-stream"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["stream", "streams", "writable", "readable", "duplex", "check", "detect", "is", "type"], "devDependencies": {"ava": "0.0.4", "tempfile": "^1.1.0"}, "gitHead": "a5327fd78cff686b4a9e32e676e179b620fa748b", "bugs": {"url": "https://github.com/sindresorhus/is-stream/issues"}, "homepage": "https://github.com/sindresorhus/is-stream", "_id": "is-stream@1.0.0", "_shasum": "aa0ce914e0e917064222acf7ddf6140d718865d7", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "aa0ce914e0e917064222acf7ddf6140d718865d7", "tarball": "https://registry.npmjs.org/is-stream/-/is-stream-1.0.0.tgz", "integrity": "sha512-rJQ9jrSmjKDMFcqluM70NLMOCJCpxHgUctFcLphRDDVFFv48wo1o3u0o3iaHdYEDbKZwL8sVZwkr2Eym1pcmng==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDkWep+63uvpAISi5NLmoXCZDGlogisqm1N+75bWNgHMAIgX6+uhneBB+WTjrRNJx16WJ42PDSYGg7Y+vtBrGGIlvc="}]}, "directories": {}}, "1.0.1": {"name": "is-stream", "version": "1.0.1", "description": "Check if something is a Node.js stream", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/is-stream"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["stream", "streams", "writable", "readable", "duplex", "check", "detect", "is", "type"], "devDependencies": {"ava": "0.0.4", "tempfile": "^1.1.0"}, "gitHead": "424a6904c768f5f446fbc36c03a31be8b1209ca6", "bugs": {"url": "https://github.com/sindresorhus/is-stream/issues"}, "homepage": "https://github.com/sindresorhus/is-stream", "_id": "is-stream@1.0.1", "_shasum": "b44ce45b1f0c3df583f6b5debf84dcf9743ac8b5", "_from": ".", "_npmVersion": "1.4.28", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b44ce45b1f0c3df583f6b5debf84dcf9743ac8b5", "tarball": "https://registry.npmjs.org/is-stream/-/is-stream-1.0.1.tgz", "integrity": "sha512-Yy/yK7rks2mKynRwFfh64byiskuJoMPdtwI2nvo+FF13izGOp1orzqnChGshg+0HUmuVG+fBSL6pfL/WPIywSw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDneVDnmQLrVNU/6x8M9rIhsKAp9A57vN+v7dQwkDJ1XQIhAIbuPUSvIgBE2ixho8uwlF3ZX7nFO5cxhXm+W0EgocEi"}]}, "directories": {}}, "1.1.0": {"name": "is-stream", "version": "1.1.0", "description": "Check if something is a Node.js stream", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-stream.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["stream", "type", "streams", "writable", "readable", "duplex", "transform", "check", "detect", "is"], "devDependencies": {"ava": "*", "tempfile": "^1.1.0", "xo": "*"}, "gitHead": "e21d73f1028c189d16150cea52641059b0936310", "bugs": {"url": "https://github.com/sindresorhus/is-stream/issues"}, "homepage": "https://github.com/sindresorhus/is-stream#readme", "_id": "is-stream@1.1.0", "_shasum": "12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44", "_from": ".", "_npmVersion": "2.15.0", "_nodeVersion": "4.4.2", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44", "tarball": "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz", "integrity": "sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGn9YmSwMikgbLp21drM9E7JO12WPiT9PAZPcvYmgi1OAiBEMflwPt7827f1fANQYbgZitav7xV/4xlteef7wokE1w=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/is-stream-1.1.0.tgz_1460446915184_0.806101513793692"}, "directories": {}}, "2.0.0": {"name": "is-stream", "version": "2.0.0", "description": "Check if something is a Node.js stream", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-stream.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["stream", "type", "streams", "writable", "readable", "duplex", "transform", "check", "detect", "is"], "devDependencies": {"@types/node": "^11.13.6", "ava": "^1.4.1", "tempy": "^0.3.0", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "1a70a7f985a00bf25de8437e68c849bf0e434bfb", "bugs": {"url": "https://github.com/sindresorhus/is-stream/issues"}, "homepage": "https://github.com/sindresorhus/is-stream#readme", "_id": "is-stream@2.0.0", "_nodeVersion": "8.16.0", "_npmVersion": "6.9.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-XCoy+WlUr7d1+Z8GgSuXmpuUFC9fOhRXglJMx+dwLKTkL44Cjd4W1Z5P+BQZpr+cR93aGP4S/s7Ftw6Nd/kiEw==", "shasum": "bde9c32680d6fae04129d6ac9d921ce7815f78e3", "tarball": "https://registry.npmjs.org/is-stream/-/is-stream-2.0.0.tgz", "fileCount": 5, "unpackedSize": 5691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcu1CuCRA9TVsSAnZWagAApkwP/1LBH7WhFysLIo7y37ZT\nZWM6y/PcOCxrRAAM8qFNv9VQEGVEtjJLZBJNBuqGpGIsfctYn5cViUF1ZQNQ\nL5rEzvJBy8aNzKRNmQf/itbBGRkC75NMWCZISPPM3XxgvP2o6Bnw7P2Nu0if\n75j93Hqg4GcYFQjV9rCYagSdxJEPEIDI6d8PH2Tgdwj+581X0aNx90oEu6vU\nk33vTvMbhcsuc+19W/Asr5BpHJ2foNcTLTN1QvLCKkE1sZfCQ42EEvTmp600\n875RGh8GbId9D8kpK94pulnog1ARRpkLpvJ+ONFw/16p2hoKBLHOxUoSKZY8\nK8RPCkrdC6J2JgIyh1Yhysu0biN6jT9wgwnIcJraIUEkbNHXerCBgug+Fl3I\n4sQ+2IEIoLne+t0oqwJKoQPTgsJSVdPOVzyDps94vhxX3nB+82gOWP6qquQ9\nfTAWcmkB3cRDImlq8uCd2kAtLmXZcG+AyC1pCqA3PIPrdZG+2lWOX38Geqnw\nion1Ws4jI6d+/aJ+hs/DFEeXVvKpc+9cnFbqFKYtFRutmywquZ8mVOgSl6ez\nnV282upQ8cMpU4txk7R+PDgOz0uhIa2AA+hQ9L863HDsfOwr4CgHYGxZt/AL\nkYUoaAo6MxUdkL7rjePxho1ZDTJtK26k+5Eg+iyD3xDzVyhu2HCxZNPiRWoA\npfkb\r\n=yjey\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDOfB8f+8WY8o1Poa7faUhW/Y/unfQUWYjDOKIq05+HSgIhAIv1XVqBHYFk1WwIhopVp3Ys/VzTSv+fz8ZA5m8Kudjn"}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-stream_2.0.0_1555779757689_0.1309729016157941"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "is-stream", "version": "2.0.1", "description": "Check if something is a Node.js stream", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-stream.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["stream", "type", "streams", "writable", "readable", "duplex", "transform", "check", "detect", "is"], "devDependencies": {"@types/node": "^11.13.6", "ava": "^1.4.1", "tempy": "^0.3.0", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "fb8caed475b4107cee3c22be3252a904020eb2d4", "bugs": {"url": "https://github.com/sindresorhus/is-stream/issues"}, "homepage": "https://github.com/sindresorhus/is-stream#readme", "_id": "is-stream@2.0.1", "_nodeVersion": "16.2.0", "_npmVersion": "7.10.0", "dist": {"integrity": "sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==", "shasum": "fac1e3d53b97ad5a9d0ae9cef2389f5810a5c077", "tarball": "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz", "fileCount": 5, "unpackedSize": 5927, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg/pOYCRA9TVsSAnZWagAAJYkQAIV52MJHUd2aZVuekvvH\nRF/jfy0giF2yOIm6LJ1d1IBBlsFB6jLtpCTcx+/Vl3ogxqDrXH/ByqBnF6nm\nq8ZA/V2vaqDKrEYDjxZRL4i/JMqRhmTQ8OisdQhNHhXc+UztUqeOpTjt3Uaf\nPDuKTS/roFWLZXjA2A6UobMjKltBSbRC/lencho9TRn7F/emrmRWPmL3ToSi\ns9+9tuDjapS4oJc/kxvLYZgHIhQelsC7ZXdS52tVhoLBiIs3/6fAP+DMQUcf\n3I80AVq0S0sFDAA4U8RP28Jz+L6RTw9jbVzS15hgTBT8v2OYMBNooIY/kNy2\ncWpW53pUmDQYZNr/ctrJ0dI4aDoMPLfeFgdbSdmvCd1hHtwFKQvplWQJtwPB\nsOxM/y38teJizivtE5g6hZozrQAPeu2H9/K0TQGess2zPGx99gadvynfaS3q\nnnUO5c+bXBBPirUGfm0h2A4mayA9JLuwp2KIFCh+7YcHXxkK+G0s44M4xlFw\niqvo7ACCb0MCtG8STQ7SbmjAYK1bR3DheOt1OU4HlnlrVY8g6V+GTRsRJT3D\n5/klG6re8AcOTh+zOKRd1pLowQ2kjHMPWRpQoLSJkQRB485Yt3uObIA+nwLC\n17Y9Jf8WOh2SZlE0DmAPTkXgbiKFtOZO9igWBk02emhfdhO61J6Ta3xuu+hi\nWrbY\r\n=ZDg7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDlLKvse7Rd9Gqz1epSDDJPZk5qe+uFTtDeGn9t2/cdHAIgUzlhX4fI/lE+33CIfMaZN5SFdE6kNwxldrnwZo9e77A="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-stream_2.0.1_1627296664370_0.3696773205895987"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "is-stream", "version": "3.0.0", "description": "Check if something is a Node.js stream", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-stream.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["stream", "type", "streams", "writable", "readable", "duplex", "transform", "check", "detect", "is"], "devDependencies": {"@types/node": "^16.4.13", "ava": "^3.15.0", "tempy": "^1.0.1", "tsd": "^0.17.0", "xo": "^0.44.0"}, "gitHead": "6913e344ab2dd63041bb7c03095876ce5a7e0a8b", "bugs": {"url": "https://github.com/sindresorhus/is-stream/issues"}, "homepage": "https://github.com/sindresorhus/is-stream#readme", "_id": "is-stream@3.0.0", "_nodeVersion": "16.2.0", "_npmVersion": "7.13.0", "dist": {"integrity": "sha512-LnQR4bZ9IADDRSkvpqMGvt/tEJWclzklNgSw48V5EAaAeDd6qGvN8ei6k5p0tvxSR171VmGyHuTiAOfxAbr8kA==", "shasum": "e6bfd7aa6bef69f4f472ce9bb681e3e57b4319ac", "tarball": "https://registry.npmjs.org/is-stream/-/is-stream-3.0.0.tgz", "fileCount": 5, "unpackedSize": 6229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhElpeCRA9TVsSAnZWagAA72MQAJMmnD9mO2+Kk9zsoDkX\nL1sMTBdLtafPwSD2ekM8F1UIX/EjA1IQJ9dR3eBR1Z2EGOvwpbXa6Sm/AO/l\nJ2/f3AQ96EcDwIZslOjEY3tE69hbjufj4P+bhDlwMxg3x07fJBQ0SuZahY8I\nfE4d64K/U5Z9fnWW7X5T5AQ8hWUO/7wE4S1C+7yRUtVYBATVeBdqLDWkdin6\nEayXVZ+Q28KdvIREC7t4FoX88qVCnWPGyE51o1InKJSG6IGsf2wMpODCLEnL\nvJ7c8+MIbWiSVIdnpmPCevRid0SKuO8FAXzt+bB9eVbWU6z/5jU2bPDW5Y25\niZnzo4/o6UeFlec8f/psb2IsuCCpmh3DDgHnGAkLwjSCrkM5zyLOR9AxrxHh\nlpYdjXni/dCvjxihqbJNXgE0zbvlGfgJXekDIppp28eZXwfCsnoD97HztMAh\npeZRywBgRYPF5Rh9dwJD1LTx2RkH07xm9JK8Tg0nFGAxEQi/xmHDFtHSG0QE\nQUUoEPI4W6XgvKzzKDWWjw4zhvAUSO6rH7DQdf5M9rDBDmC/yqBioGtg7Lko\nLlAWI/L2r+Ssee2uzqVXaLmTuSf0uGQgpkooetKm6CiKb+zGC2hLaimzhKRC\nhFGB9zR6PJhDDKK4+tiGO7F6FJ+GUMh425qM4Cmq4ZVXr317pHhKceNVu+E8\nz6H6\r\n=Qw8f\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDyFpzPfZJJNgFDsd/ld+zN3lglG5RXXP7WbvbZdgc0nAIhAOPtiT7Kdt+6V2dUqHip8ImhSmNM5MFDVUbFeaGIRxa3"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-stream_3.0.0_1628592734094_0.10015535082882487"}, "_hasShrinkwrap": false}, "4.0.0": {"name": "is-stream", "version": "4.0.0", "description": "Check if something is a Node.js stream", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-stream.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["stream", "type", "streams", "writable", "readable", "duplex", "transform", "check", "detect", "is"], "devDependencies": {"@types/node": "^20.11.19", "ava": "^5.3.1", "tempy": "^3.1.0", "tsd": "^0.30.5", "xo": "^0.57.0"}, "types": "./index.d.ts", "gitHead": "29ddffc78580fea909e06b3fc2a0b2d3ba56da85", "bugs": {"url": "https://github.com/sindresorhus/is-stream/issues"}, "homepage": "https://github.com/sindresorhus/is-stream#readme", "_id": "is-stream@4.0.0", "_nodeVersion": "18.19.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-URLxBTBUl6EnwuIoR8OyYiFxL9jrmc7JzQp1W72ol8yk5c/7amjKrSlNgqU7kBiR0Wf8INMEXj+ghiClA+F62Q==", "shasum": "3bd637761e6f9e9e30331744bcef71aba4c9a9f2", "tarball": "https://registry.npmjs.org/is-stream/-/is-stream-4.0.0.tgz", "fileCount": 5, "unpackedSize": 7749, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICE1R2gSj5DMKxDmidSreCtXB8GkJ/XajZtZClINMPXkAiA55/OJT8/dDrHitFZOrhCQpdwY9+0wched1fa7Kuf/AA=="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-stream_4.0.0_1708369505088_0.4158994141219907"}, "_hasShrinkwrap": false}, "4.0.1": {"name": "is-stream", "version": "4.0.1", "description": "Check if something is a Node.js stream", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-stream.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["stream", "type", "streams", "writable", "readable", "duplex", "transform", "check", "detect", "is"], "devDependencies": {"@types/node": "^20.11.19", "ava": "^5.3.1", "tempy": "^3.1.0", "tsd": "^0.30.5", "xo": "^0.57.0"}, "types": "./index.d.ts", "gitHead": "0868e196b222faa5e4b58c86c5bc6c480a94a966", "bugs": {"url": "https://github.com/sindresorhus/is-stream/issues"}, "homepage": "https://github.com/sindresorhus/is-stream#readme", "_id": "is-stream@4.0.1", "_nodeVersion": "18.19.0", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-Dnz92NInDqYckGEUJv689RbRiTSEHCQ7wOVeALbkOz999YpqT46yMRIGtSNl2iCL1waAZSx40+h59NV/EwzV/A==", "shasum": "375cf891e16d2e4baec250b85926cffc14720d9b", "tarball": "https://registry.npmjs.org/is-stream/-/is-stream-4.0.1.tgz", "fileCount": 5, "unpackedSize": 7619, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCVUZ5C3saftedvOFBl/kC+7AUKUufNPqhTIGd5Q6EG7AIhAPgxV7RGk9Hu6zwEv7D0MxlyoxrZkUwhWg2g3LCqO+1b"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-stream_4.0.1_1708370052095_0.5622682672651913"}, "_hasShrinkwrap": false}}, "readme": "# is-stream\n\n> Check if something is a [Node.js stream](https://nodejs.org/api/stream.html)\n\n## Install\n\n```sh\nnpm install is-stream\n```\n\n## Usage\n\n```js\nimport fs from 'node:fs';\nimport {isStream} from 'is-stream';\n\nisStream(fs.createReadStream('unicorn.png'));\n//=> true\n\nisStream({});\n//=> false\n```\n\n## API\n\n### isStream(stream, options?)\n\nReturns a `boolean` for whether it's a [`Stream`](https://nodejs.org/api/stream.html#stream_stream).\n\n### isWritableStream(stream, options?)\n\nReturns a `boolean` for whether it's a [`stream.Writable`](https://nodejs.org/api/stream.html#stream_class_stream_writable), an [`http.OutgoingMessage`](https://nodejs.org/api/http.html#class-httpoutgoingmessage), an [`http.ServerResponse`](https://nodejs.org/api/http.html#class-httpserverresponse) or an [`http.ClientRequest`](https://nodejs.org/api/http.html#class-httpserverresponse).\n\n### isReadableStream(stream, options?)\n\nReturns a `boolean` for whether it's a [`stream.Readable`](https://nodejs.org/api/stream.html#stream_class_stream_readable) or an [`http.IncomingMessage`](https://nodejs.org/api/http.html#class-httpincomingmessage).\n\n### isDuplexStream(stream, options?)\n\nReturns a `boolean` for whether it's a [`stream.Duplex`](https://nodejs.org/api/stream.html#stream_class_stream_duplex).\n\n### isTransformStream(stream, options?)\n\nReturns a `boolean` for whether it's a [`stream.Transform`](https://nodejs.org/api/stream.html#stream_class_stream_transform).\n\n### Options\n\n#### checkOpen\n\nType: `boolean`\\\nDefault: `true`\n\nWhen this option is `true`, the method returns `false` if the stream has already been closed.\n\n## Related\n\n- [is-file-stream](https://github.com/jamestalmage/is-file-stream) - Detect if a stream is a file stream\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2024-02-19T19:14:12.687Z", "created": "2015-01-18T18:59:27.432Z", "1.0.0": "2015-01-18T18:59:27.432Z", "1.0.1": "2015-01-19T12:39:43.945Z", "1.1.0": "2016-04-12T07:41:55.666Z", "2.0.0": "2019-04-20T17:02:37.870Z", "2.0.1": "2021-07-26T10:51:04.495Z", "3.0.0": "2021-08-10T10:52:14.251Z", "4.0.0": "2024-02-19T19:05:05.233Z", "4.0.1": "2024-02-19T19:14:12.256Z"}, "homepage": "https://github.com/sindresorhus/is-stream#readme", "keywords": ["stream", "type", "streams", "writable", "readable", "duplex", "transform", "check", "detect", "is"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-stream.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-stream/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"waynedpj": true, "monjer": true, "rocket0191": true, "nicknaso": true, "zhenguo.zhao": true, "flumpus-dev": true}}