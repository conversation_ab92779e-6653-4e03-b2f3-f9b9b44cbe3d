{"_id": "@babel/plugin-syntax-numeric-separator", "_rev": "71-4532d4de2b53d9b10728c5fc1ea340f6", "name": "@babel/plugin-syntax-numeric-separator", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "dist-tags": {"latest": "7.10.4"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.4", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.4", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-SV4tHlIY5iIsfsPqRA7UFwZKz/RPUVLlcRJ60I7sBELis7VQNzYZezuP5TqthfWdKU4MS5pwAK/Ugq5XnB3uMQ==", "shasum": "2916ae7c40051e456b80258af4e30797bb5bdb1d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.4.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIED8yD4Gj6conEc68KK1VzRRFX8rcCqA510ielPi6iiDAiBcM/z86JZsZP6qA6iv640m5yWNZginjdtUB0uKg5G+kg=="}]}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator-7.0.0-beta.4.tgz_1509388463114_0.7357197902165353"}, "directories": {}}, "7.0.0-beta.5": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.5", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.5", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-gjkxwY4GMK341KVQpdgeXcN+/W2TEv+PLOroQz9j8Gf1uCm2YtXm+3iq5PMBvJd5rPwHw8s0I030+f7oYAhKCQ==", "shasum": "47d4b7098b0c07cdd6c8318ff6170195452edc78", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.5.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCKpmebjxNyhte/4ZliXaWh/lYusH6gj+J/87UJmSu6iAIgWR/bZSuNYQes0ym/a5qwTKvmaZ/YWMift4eUl3ecazY="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator-7.0.0-beta.5.tgz_1509396965584_0.1445636609569192"}, "directories": {}}, "7.0.0-beta.31": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.31", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.31", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-y/6xUgwDeAwpu+MSCPAWwSoJbe8RvM9OKYGHd+/D/LVy1BWLzNhdF90fMFD+7V02B0WF3SqxxJUINzWnJux5kg==", "shasum": "264dabd554ef50bdefb908110824693b27a8e1f2", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.31.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD3Whbma3CTlDuKr5y6i31AhAl/2a4mAPEbpWiLThTJmAIgLto86hfexwNPmlVscENWduSo3NaCpIt17kB7PcUf+MU="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator-7.0.0-beta.31.tgz_1509739396882_0.0036993848625570536"}, "directories": {}}, "7.0.0-beta.32": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.32", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.32", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-odJ+y056XG2CFtsMJUrTzkjIVdFGcS+3zVq0pXWEhJ4szbpOXYocayB0l3mgj+7MvIGRzjI3569pXhW4Fuctow==", "shasum": "f0febe10952a9ac8a4b7ccf74be1afe27367fe35", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.32.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDL9JLi4r0Xg8dNLGTlxaEqcezHf1JGeCs6iS0cTGVxoQIhANzJ8P1QfxPkMTxLj4w77QTMkKjVDNxEQ+vqOKVd11nJ"}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator-7.0.0-beta.32.tgz_1510493584848_0.8278786719311029"}, "directories": {}}, "7.0.0-beta.33": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.33", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.33", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-0USct0itnJTVCA06B0/SE7Hl64swKSPzEnUh8UeQkqxypHB1ENioAWa2kAI7UYN1yOGpBrkuWjiE96a2XR8JHg==", "shasum": "af23d4d6f4255a8ef4a7a0676e5debe72d6cc455", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.33.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH+fJQR5Fpd//PL4JqifIJMg9Q8u5EV1EHfUbbWrITrUAiEAsvvIUohfALoUkER+X8eyGD/z/Zy/ZrEA3ntrj4ZxdjI="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator-7.0.0-beta.33.tgz_1512138488127_0.12083078944124281"}, "directories": {}}, "7.0.0-beta.34": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.34", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.34", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-<PERSON><PERSON>hmQRKoOhV99oj43mRkBHUGdfZ1lf/fY9zSKntVnZM9cp+WCN2Aj46cu8jG7rNaCLkG48hcbEJHegHYhp9w==", "shasum": "8f04c8efc9e6a9cdad7edc31d5b167c836dabc53", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.34.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDptT8aUa+wcg/wRZc5jM249p21vnWk3JmAtoTzCHziiwIgViOV5cvf3pxayogRPDsqFRUcqUJ7ziGzFZpfGEeB52c="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator-7.0.0-beta.34.tgz_1512225548047_0.09041470335796475"}, "directories": {}}, "7.0.0-beta.35": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.35", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.35", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-AA7cDKw5yWcGjHMfQFseu7mzssgrm8lCl8p9VeIK/+IqZW0W62DEL1z1Q2i9WKnrCcYs87jZBVdTn5nosdamDw==", "shasum": "e4147eb72720d42d2045f9dfc0af95644c11071f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.35.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDrMFcx0nHqdmPPxY9uk//mJNtkJZXdskRc/S4SgA395wIhAMoOpTuyUoDJ/HFnDkQLrQPj2Ji8BNgutSJ7R/+4Ki9a"}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator-7.0.0-beta.35.tgz_1513288055848_0.1267609482165426"}, "directories": {}}, "7.0.0-beta.36": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.36", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.36", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-PgrhhYtut37uOEPv335D/xGEAHk1THFDtLSdBWVxvODI6xvANelwo8QAyC/QszRGO2VxWCqq2AfAeODzZHh7Ew==", "shasum": "3b7194f453fff9d4a6d3b221108ce49501cab1bd", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.36.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDCyFG4JZrAg7BrJCGPdWfQrZfwU3THbZFUxGG718IORQIhAPG/HBIlC9e8AnDW7Ms/VHyCiW6n4fSJOpPT2VSFcrp4"}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "kentcdodds"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "not-an-a<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "mysticatea"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator-7.0.0-beta.36.tgz_1514228663955_0.6369474767707288"}, "directories": {}}, "7.0.0-beta.37": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.37", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.37", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-hiDephLTJw3Det6qVsZIgCw07gsy1rJzCuzrLD+avd088wVZBfX8VWYpCki597+oKEtjLuL1qViO81ovkgkEQA==", "shasum": "45cf626436e0938a087ca7931ef32ad8a49b1815", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.37.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCqnDmmzsTBNOarN6S9m5T/jwSfZ2u4vQnNLBOBPtIfAwIhAJVw19OkBgcY+lAo7jaLSqNNyxr3AXf2Ym1lTLp0FbH1"}]}, "maintainers": [{"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator-7.0.0-beta.37.tgz_1515427343386_0.18650501570664346"}, "directories": {}}, "7.0.0-beta.38": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.38", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.38", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-XyBtATBwLgU+cCwagMmVZ8tsRVIKEsCZuQ+wcB4YW550uuY9XRNrKCjlRbFQ9wQiaAecNkM/CCic/n6J8l50mQ==", "shasum": "fc13dc1050e5784572d3b8f108e4cd63b99174cc", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.38.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDo0OuvqDQddyPjJrCDcs6MLQOrPdfGIp32Dl6AyD2UWgIgUsOlYjHHGOxaLMgeYBJOvckCeNLHzNdxWTiC9PDO1ss="}]}, "maintainers": [{"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator-7.0.0-beta.38.tgz_1516206704425_0.5151258914265782"}, "directories": {}}, "7.0.0-beta.39": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.39", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.39", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-5r5UZRBZk6opyx/ovx5rx0yQuTzeQ1y/9X8V1IM+59aZ1RP9iK4CID4mcZF0yb+Iyj+MzItYC2dcWJIKmJYIsw==", "shasum": "347231ff2acf2479d38e4789a976c82c00d38818", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.39.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICbUkyJGZQNOh2ZIYsZo9hhWi9dAn0BtWDsO4i3QHKzHAiEA8us0n9EK8yHUTUZVyX/zOis6eBNQEsz9raKVWcaVgDc="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator-7.0.0-beta.39.tgz_1517344047790_0.636719906469807"}, "directories": {}}, "7.0.0-beta.40": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.40", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "devDependencies": {"@babel/core": "7.0.0-beta.40"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.40", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-FlBu/0pr3UdJKDt9SrGDPUmLgcjdjJDXd4wj7Lr7yPohOTZMSd1ZPJEp1FFIKxqnD0tBgkATuCIMUXqAbPcQ8A==", "shasum": "d63dd89919b94632310a83c8be4d50b5c479b3a5", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.40.tgz", "fileCount": 3, "unpackedSize": 1364, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDE9XfWrt0UPABAM4eVA4Xbhbl+rhCv3CFUG+DWK1e23wIge1BflNpgamfRd6tMNSb2ysGsi2QDAmchXjvMn/slnTc="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0-beta.40_1518453684468_0.3622299168387011"}, "_hasShrinkwrap": false}, "7.0.0-beta.41": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.41", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "devDependencies": {"@babel/core": "7.0.0-beta.41"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.41", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-eT/+GsRcWJM7yRO/7xn3e4sR8mnIf0sLQBv9J8qWqY+bT6OIoxICRG8EsVbcaDI3vjcGDNeWs+qSRV8BnlcZDg==", "shasum": "39fb60bbcc9d85782d53065fdc7438bd162b3732", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.41.tgz", "fileCount": 3, "unpackedSize": 1599, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDZ0n4LOIAg+ZB+lyRwZBZrXLDAuuDGd3nesTUhfXk1eAiAOcYoKFPI7XFsaW3rpp6Q9kkbFLNAiwm9/CMmvJ1927A=="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0-beta.41_1521044756351_0.9705097198146884"}, "_hasShrinkwrap": false}, "7.0.0-beta.42": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.42", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "devDependencies": {"@babel/core": "7.0.0-beta.42"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.42", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-JoDcUoo1iUqNhDucb7Vck04FQjOvrRHDTBFmW9QSx1SlGJaYLw5Nuuy39Gnn+3yvl8NmqKjrL6imfqpTROy5Vg==", "shasum": "810f0836261d9d1619a0809e81cc4c26f8374f7e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.42.tgz", "fileCount": 3, "unpackedSize": 1599, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCPDPzAa0LtbL2YP5YrOK/RlkR9968eJ1I/QwF46CFu0QIhAPMiAKwEE4W47avjBRw7ow4byC/VY3aAqtASW1ZUr3YH"}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0-beta.42_1521147033156_0.09219978309326748"}, "_hasShrinkwrap": false}, "7.0.0-beta.43": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.43", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "devDependencies": {"@babel/core": "7.0.0-beta.43"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.43", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-l80F/ZIVjKZpAHC4aeDAP+Rs87eCtN+WNZtFhJUUHEjkHDzM2olMFHsknrrVieB5YLoL+OEtUunJRF8mM2ttNw==", "shasum": "3bddf23eaba7da3ee12de89a6180fdba93c6fa37", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.43.tgz", "fileCount": 3, "unpackedSize": 1704, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCawGHS9mv1CT2+J70NwvqXS05br5e9OVa7lnxyQs6DIAIhAIx7st0Dri3usJKr623HOgyqFc87ivlAjLZHrjpLCRoD"}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0-beta.43_1522687697834_0.38589960405404766"}, "_hasShrinkwrap": false}, "7.0.0-beta.44": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.44", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "devDependencies": {"@babel/core": "7.0.0-beta.44"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.44", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-8ya4r1L1UIby4px596ABbNVIbk1Fbrj8baBvu50h20ozrAv59mA/xv6mAG7mxB4jWg0qCWxcUp9JbfTKp7bcWw==", "shasum": "f692545d4011e541bf948338b6e856cefb78e27e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.44.tgz", "fileCount": 3, "unpackedSize": 1755, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBIriEZmj79Kx718ns+Fjem9ZZQECXUAxVQoDQC9fiM7AiBop22dg6vIg49oTHDZOiV1az3ceNn0UYdCvmzw9k+Yrg=="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0-beta.44_1522707599563_0.19397273813802984"}, "_hasShrinkwrap": false}, "7.0.0-beta.45": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.45", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "devDependencies": {"@babel/core": "7.0.0-beta.45"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.45", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-hyodRG2g+eWBcUWpUVWGaEGmFkNH5tqMC9g9ldGC+jVri61D87xo8EJHmlQGMIy+eidj1jMuUq3aNxcwLoJGKA==", "shasum": "8f4dc387c10f445ca94894746247b05f8e161a6c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.45.tgz", "fileCount": 3, "unpackedSize": 1755, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1GCRA9TVsSAnZWagAAbN4QAJK8Ud+L1ZYh5b5Wa6U5\nwdtwewyxcu7ZpWWqOZlhhKVKRbvwDUTdqP0xa3eC2bLyo4M7TmukuSfA0nhN\neVSfhb06Jg9179dPKhooSOGdGfn8B4syqKG3sWO0j+AJw/MQdipb5qVDZ+VZ\nVIrOVWZ4HmFVjSrsuA0DcrdEQkFEnW2wOkqR8CarYpv0IBbCYMeit/eAAp3a\nJy3VIw1VfTBq7xg4clVksjrERr4CuhTwRW5IN9SVcLNyDpo1W02lPGC4s7J+\n7PVbIDgxnlRWRFfEyqpQ63HuZjbDMll9XlGpUEMTw3GQSGdP5Fdt7MJAPX/V\nWWTrq9UL4AZqY7qgYq9uN6fh01IaHoFFUXfrAtKLX0iD8+tMG8OdGK0zoax0\n4LX3Ox5kZxdm8sxtw/LNyY4Ch3KQ6V9MXgBVhFlVJhL+GotxDu+4Uhkn7jFt\nB9raa6hFwirtCBYvyD4hNWTIN4k2xE3Ykx/N61h7kA80ixlzBw8WQy29ZNhF\nSM2bVSWj51B9VzHxzEkoUirD+2hUSJC5vbUduW7reO99JI81RCIE4a3r1KjZ\n1xgnbq3lMUJfWW6xN7vWJ0tOODjTigN+F2NqxPqZgBQis3LhxrAhiPcpMC1n\nuJn3ckPI4LkZZP0TUYYwDWPEPEUKTnPfT2EpAf5X+6Nu/q8/PSxon/sLhzHf\nkFri\r\n=WnLY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICN4EIjVTPk6cwgA5wIl+Ig6+lExrLH1K4DaJEGD5cu2AiEA3nYXrRtBCkc9XRD6kVDKUyB2IMaPW28naXXnPIpd498="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0-beta.45_1524448581383_0.7115470599008524"}, "_hasShrinkwrap": false}, "7.0.0-beta.46": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.46", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "devDependencies": {"@babel/core": "7.0.0-beta.46"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.46", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-xLcA7vqeCFDQbpbo0rrJvrTMjnjx2i9WwOkSbw+zXy9M/bmVwGAgLGROVxeuWq1Ou8Ku827DuhCzXgyf5dWvoQ==", "shasum": "54c820bf32192378db78a68586e0d69bcf5f2943", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.46.tgz", "fileCount": 3, "unpackedSize": 1755, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WF7CRA9TVsSAnZWagAAOLkQAJ4s+e7KXCgWQXBzG9Vp\n1F5Yaj82XdwQsifOoQ/zclTGMhJAZpQe7f4Jlz5W2kNW9aw5b3nx7YghFTCj\nQNQQWojYdaYm6r6vdey+qY8KOSbGsCnHEmEpsLzZ1Q6zEZ7q+pyYKW6wW8Wv\nekxETCHA1kwteltCJ9BNLTkdeyvorFgvp9RNztnBpSpGgrpiGAODYD+o1yoi\nDfuJBTEuSHQ2ZGyqWhhh2YZBsi73aG7HafWPw58/TbESeC/P+Qzpk00fzDf4\nfqK1uo/DA11LU8z5TBQniGMpPwI8rdBG9xeWe9hr17dGxsusk2OIWo3YAcIw\nUURij3yj/boXKo3qFKVPJ0M4LDE7D8WGq65Xc+De4LWPku9qTGxS2soec+T+\nfLRR8Zea3jje1E05Mi16eSWkA9zKuHYJECIPCVmf8eCrgE39rkrTq1rA22yr\n5xpQCkjCBOSTVq3xBD/hvSaxWlIbH8s8RBxIHVlM4rrH/PQABPC9JGiD5y7d\nWd8guUMb9tv5GMNXD9SHvVnHw/ujMvuznYf7a5vm45xDsKohx0+tJOHLrw9U\n1Pbp3J3sTkT8VnHD9vmjWX4+ed2L0DvOx+F6xJ/V0R3tCEeW+2+PQqGAFlWY\nkXsimTJ7bc6fvFKN+0TcE0K6L4Qt3qmWk7sd2aMA+YiC2tDkgzWezAuGadYh\njdyW\r\n=CYa3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF04Bc5UnLGjtwbZwFx3Xg5LdqbR+QlMP9c1jH3F/DahAiEAgGWp+RCGPJvuhn+ajPg4mfLj7TkZn/JLyrks6thvNZc="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0-beta.46_1524457850764_0.4113946159317894"}, "_hasShrinkwrap": false}, "7.0.0-beta.47": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.47", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "devDependencies": {"@babel/core": "7.0.0-beta.47"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.47", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-qAGv7jHtZWNWy4n23OzKLrVk+xfaEO4LYRK3zCMyudXRfB3FPaer6NJNjU5rebvJzC4wB2EVb2nPwVENNNh2jQ==", "shasum": "9f06cb770a94f464b3b2889d2110080bc302fc80", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.47.tgz", "fileCount": 3, "unpackedSize": 1722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iT+CRA9TVsSAnZWagAAcs4P/j3tbowBMVlHZwHrQijF\n0IR2Fk13NGkNKctWHlSIuwiugy14a9GP83Ozo5l+NO/2S8Tl8LtcEyLXgoka\nH/0OoPvj3QQfMV77lJs17JTqXuTT7alTLuRqWP/4OHVgZYGrUk4r53EGKLRj\nQtKb6l0NqTRB0SG6Jn8sBbWAo0lgavrl8hNeRCeCVixlWcyMMTfIpmFHTEUd\niw4sbhkDIEndsHxOy65zky0bHhf3qFibINTVePNL5shLfP20xXF7w+jyPXmG\nwM9a8pFExDTpDp17fLbLvea5AfG+LCDlTZXv6FSHjXp6DJ8vaSf3ha3Jv3Uo\ngoOGPBkPjfJWkABzveMydBH8lRBuTcGJdN3sCn/MgXYEnQ9Gt3SwDWtHFZFU\n2cqU3TeSLjxeJMFdXIXDdloFU9q2HKViLqiL1oP7aV2txSfBUP8Wk5XuUxtO\nIPnfnsT/Ui6y53ivWRJOiMX+9Oo1gwEf4lwRY49QQusghgKP+vBpo7oGd0xK\nbZyA6q16JIyvarib1RSNandXP9Y/THAscPAD5PYdFDL2DYb80tlxifbTdV4J\nMYrpmcSq2H37HJxNjDB1rab+vmH0kievvujJPVTjhLvAY/mEpGhrZoSjjvjC\nX6pAFqA4czN9ZxnQ7Lc4mrI2HANrGM8HxjBvQHX5/BonKQwB5HiKXQGEhEqG\nJECc\r\n=bT4l\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA3Cd01Ayxp35LAuTwWeDjcX2T6UZ/EBiphp8XyjG44mAiB7VCKrf0hhDhsaUq6/7VNyFWJcoeeqEhr65P1JMt0keg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0-beta.47_1526342909480_0.996469215730853"}, "_hasShrinkwrap": false}, "7.0.0-beta.48": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.48", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "devDependencies": {"@babel/core": "7.0.0-beta.48"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.48", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-2cGTDAs8TjlSvCDreJPRUYVGc8nnOCqniYwAd3Bw22P+jfezOpsi5/uZkG78NIMFwbNdRluG8MbKsvlBho2B6Q==", "shasum": "af254d381c73e2e2ed7ab469859c8715db60a732", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.48.tgz", "fileCount": 3, "unpackedSize": 1704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxDPCRA9TVsSAnZWagAAA0UP/iSOc2O0Jefwkau5NszK\n7w/nPdaezo5JtXc6ER/Dlxo1p1xRPd9LOiVhGPQidLzZe+rblg8/eWwe0IIn\n4BqGBuIjn6jpslsp49ZcMbvxPU5qZPccG/1hbJUf5CHG7h6kaNSdiTivpNuK\n+I107Wlqn8/4NVi+TQg/aL2aljuB4kASYz2tu75dEZLLS0Wym1nqnmz0cOxk\nCp3ldkVHvOHImZXT2XM4OqjFZ5Y7PeT+EleLQV666ttu/8lPhbuWKpS4YF7j\nrG2g281IbAQ4uBtNCjQXIo2yBhHg9hBYEjjhYr+aZ3EfyXUgcGAQo8cL5sGd\n8q0yuq83TDp4AfmUNykSLZGGkKL+fEXUx1KJ5VhgrMR92hOhXvD9MesWk0zI\nOrJEtfS4wU4ywV16hK4HCuGHwKFTMsFyxzJ8ufiTB+ndPr6amVQsPI9/xbtw\nQOyOaZ2zH8KHwJtLo4btv5sEBEJFU6XO8UuvuvUxR2TurfeTZFZ8BHEDMONS\n0n5jQYQnwXk0EU7S7X+FI2z9GGMZURuaakqArij06eSy7TZA4psgB+xyKj3m\n/t29suBrMUbc3/7Gdb/EGl6GvPj6ANACak2VdKSNuKoF2W2CGT2jWcNw28Zm\nm0q/3mqC8R9kspfNHaD2iNSbuokqMu9mgHNVndMFUMThIAZrnenMN1CkaMyx\nB1+N\r\n=w8Dg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHriHLrRlg2AxOlxU2N+WLi5/fiL1nvXOeKVtRzAWfN3AiBcnIDqRduIMC/7tzVGwzEu/9k8UvrQ5s3gk8Jm66X/7g=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0-beta.48_1527189710772_0.6421687123754154"}, "_hasShrinkwrap": false}, "7.0.0-beta.49": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.49", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.49"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.49", "scripts": {}, "_shasum": "00331bfcedbe0124eb53255e667fe58a2b329ca9", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.12.3", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "00331bfcedbe0124eb53255e667fe58a2b329ca9", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.49.tgz", "fileCount": 4, "unpackedSize": 1727, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDNaCRA9TVsSAnZWagAAn2gP/2GTYFPgFrAGn+76kIzP\nLeZ5Z8zpnQZ3vXQxZxOmC9c95tAlytdR82PeCEyKJHtsH70w1qSgJeuxkOrV\nngnIwD1W9w1CSg0xxv/ZDvq1hFyYWI5mZCNIHbSytZFzKIxNHZhtCob+7qvJ\n9NenVT/JjbOcux4irRP/uZnhG6K2VVEj5TK0C/sAOQchPkhB4HHFTVd+dQVx\n/+zy2bTtRHMgHz1rdAh5teM3wySL20EhdUPUXcZnQlMsgz2fuZFHrPMl+jjQ\n0+z55LAMzZo/GyHMMeBaxCsz2dniSy51opjdbsVf2k0UApAcR2FnZ7N9K46x\n4MkZpTeu1T8Jl+07grhKG/v95G5saVaaq3AlSDhPwiZUiiaMNL9aZ4QrmVP3\ndZXghO+t6R8Spf3Zk0WOYQLv1ZrsVGvHX6x8QhGLBxI0TbDOH8FW8EoHKAHB\nDVG5t6nSB5TFASjanJ7bHPR6TRKa+I6LIZgYwCncetEHrCRSz6PX7LWDxJbX\nqvLaBS/JCKGmWCd7DItwvqjPoqdeWjNKdWwLG73bgAXiC6z6J6UsAhDyYxMo\n4Mj4Kl3VXwsb/+mMlcGk5s/FeKjhrHjAjlWxq9U0y18BaOr/PseydvBjupex\nfFSfVjjAegHQ1QCz83Pvj+lJuiHIPGmJsJymlOJ8nys6mSzMhO70HUQKIGIE\nkPy+\r\n=jrox\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-vjxixVl55q1y6FymQeKPxuhgW9AskOPYyHoxn0HgUenyrde+IbVb+frmblkpGpGxlew9gCVgD7C1UhfN4Wo8hA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEo35c5a1SBvwZ0rJuy0JKrc9aeU00HCzyPpcyp1UnRnAiEA8ghKBxkIZrnTBiFda2taH5UOChZpQhnESFEsrvwSet0="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0-beta.49_1527264089936_0.9873942339501691"}, "_hasShrinkwrap": false}, "7.0.0-beta.50": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.50", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.50"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.50", "dist": {"shasum": "3377f8c941e8c27ea2f67e4743be7720bf8001d6", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.50.tgz", "fileCount": 5, "unpackedSize": 1579, "integrity": "sha512-0gc2Bd3gRGGYOD6aOFrLd/e7bHt2z+CF57OIeNdh5/0DsTBWgEHN5mjYoj4CqNZ1rp2GRoTOaxbEMNIJvir4ZQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAcnD5HSiQ4fRmVZd7Q4z5/jvzjc1rRx6sARaDeLfHaiAiEA2rXy5Nq0yeXdoYuRur6PuNQxE+lp7h2phz6V7hklnr8="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0-beta.50_1528832828776_0.23634437121769913"}, "_hasShrinkwrap": false}, "7.0.0-beta.51": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.51", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.51"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.51", "dist": {"shasum": "6a60faeb28cfab141674342d14a994f5537457d3", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.51.tgz", "fileCount": 5, "unpackedSize": 1593, "integrity": "sha512-X4DitHENOtKCNh+vho1kB7GNY8uwTs0y41npLHue5jitxQ6n29jmOS/OP7kb7E8y+2nDOzu2+L6C3u7SerED9g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA49KWxMyHIS9/lNZToHFAV3MV302xSU8NiJNl4JP90fAiB7fVUzXZX6Jz+qBzm7WDp6pSzugPfvMT6r8Tx2aK0GVw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0-beta.51_1528838375057_0.7441886742332493"}, "_hasShrinkwrap": false}, "7.0.0-beta.52": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.52", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.52"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.52", "dist": {"shasum": "4514264019b8fead3af36d5e873bc1ac004e9aa9", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.52.tgz", "fileCount": 5, "unpackedSize": 1592, "integrity": "sha512-uHgkiGfrqV7AatMhUz2x4mhf9W3Wkq2KUn4GAh5bBmxzzUgclqF09AnpXP6TJuaRrhlopg9GI2EEBViUknE51w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG1H8Eb9CAfP9Ss5HbGwZ82wlqJeG8MNbC6TIpAdGaaXAiEAyb6xw2eQLFZgAubNWcMixYuhqojkPiIMvj6TDx0yMyc="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0-beta.52_1530838760212_0.5471427946155885"}, "_hasShrinkwrap": false}, "7.0.0-beta.53": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.53", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.53"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.53", "dist": {"shasum": "3152faec6a99d9d1226872c0d0a07ffd148d7678", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.53.tgz", "fileCount": 5, "unpackedSize": 1592, "integrity": "sha512-mwimHaxBIp/AzhJmgD7eNt8Ii5PFG4JaUWBa9+dTESk8Rze/ZEnUC28LE3BIAPOjWkeTUxn47ceA5QEwPvg8Mg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC25u37hBsaE1rEUP1lLnIVc25y7cLK9hgfGltyl8gavAIgcnpn/WNvvBECP8StbBKAXtWvZm703QSsxEpdS/3GsEo="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0-beta.53_1531316410643_0.6152631651487033"}, "_hasShrinkwrap": false}, "7.0.0-beta.54": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.54", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.54"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.54", "dist": {"shasum": "9e4f56f78e0dc28cc81bcce95da4c33b9e58115e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.54.tgz", "fileCount": 5, "unpackedSize": 1592, "integrity": "sha512-ZEKHk4fkU59ejk+sd+BGPId19KOs4tNHshYmt5hl3dNh5h+hWGLgtfsNZ+LsqTJ1DppBT92Pmxf7E8wQY1YLAw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCdJM0mhpqnQd2lmS+RQJG2Yzk0DXIDaMR+DbGKubrMZAIhAJl7W5CSCe9NpilizH3e98uZJfNneShRb+7KlAkmTjTd"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0-beta.54_1531764000597_0.9132327002895151"}, "_hasShrinkwrap": false}, "7.0.0-beta.55": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.55", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.55"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.55", "dist": {"shasum": "0b829102a3f184a4ccbcb463478dfd8d2134665a", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.55.tgz", "fileCount": 5, "unpackedSize": 1592, "integrity": "sha512-a7I85m+Rb6I6WzXRQ7MDZnGvTs9g/IOBFUbvoinNW7ex+ZWii9LBnvyxCNPJsFi58J2izCpsdqTAq6hPtCRu/Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICQKd1jR6YoOaf3DqXE3RwPcUMtJpN+Iukq8Sr3YQeNRAiAFIEJKJtZPG6lZNSVtCcIO5M0pIL94tWSQk7LyqSWDRw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0-beta.55_1532815626803_0.3831273894701892"}, "_hasShrinkwrap": false}, "7.0.0-beta.56": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-beta.56", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.56"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-beta.56", "dist": {"shasum": "10e795ebba5b4fdc4d35a2e9895b72682f13d947", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-beta.56.tgz", "integrity": "sha512-GsOEExTOdk7y6qC2E7Qm1IkAHlGVUBmE9KRBi802klDCPYu4AUIZBn6pHrIZ3KWZySOt21PpDowqkfmv4vW6DQ==", "fileCount": 5, "unpackedSize": 1592, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPuqCRA9TVsSAnZWagAAbFsP/091ZDoQde9cTSTg48dp\nj9dd+U8jlAYXJMsjspSY6dg+3dVnt5Url2E2a1Ip6dwa1uMNQrcC/v+pVFtX\nTxyrNaOnoLxPAKPXbpsIo+anak/Y8G5NohsTXOqEiQNukXZQoo6EZR459MoH\ntCTwVGtIRyqh2LeIVdrrZK4x5OA8CmwvCAuYqBCzZZ1kU7+t62DPHI/BSTCm\nGiK+mMT60sBAaE5IQtGQMo7cTn0QUOpDKLAibjPvOuDZ71RSr45RSYUWV+96\nKOXPnFR4xAfGpp1apIE0MmJMsaFMF8bwErTJq0tSTBzVCpu0xrwCX9Xcl8DM\nlf0Wm+m4F7QOd/1k8wuylwnVqAaa694ijmDvwwesxxAMbsnD3CIvaiV31F9W\nIDLD38DVEk7TIcLQQNeAxnGcOMCuG7sf8jl1v9sj5Wlx5snMKMk2NO7eoSFC\npHVdFuT3M0jZEPURS+mFXxMT5Ld1TH+jsxV5SO9PRXLOHrg2qmnw3Tk/DbmX\npkclL57xty2xo2aM3idsxrtmvXxTXF/TD/pOWibh+XfH4wkEwXlcCPek0r/E\n/zPxySAFIs71bZnX5lHDxdUZK2aOXtqQRD1iMQWq83uYTco/VitJcA6I8J61\nLGXs5ccm8cLJsv0lLwJL4yrz/KLPqIpkxzkEQUsdpxtBzAGPPM7bCX6xvKCE\nI9iO\r\n=SYkT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC1CKpf/LnjLrBOz/GUvVB1AhxmGRIdrROhZoYTGxQcFgIhAN6CPSg6cBSDIJ29lUuXO3xpWZ6u7SyyUw5eAar9ME5d"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0-beta.56_1533344681563_0.8071337576483981"}, "_hasShrinkwrap": false}, "7.0.0-rc.0": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-rc.0", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-rc.0"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-rc.0", "dist": {"shasum": "41d0b8133c4cdb27dab24ef9efd9319e4f1166b5", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-rc.0.tgz", "integrity": "sha512-WQYzfOqKOZf24fcG5dWCAQ1hgpF5tLMZYy8yrSaCcslEHJXTsUZka7i3ABia+RM1PuLLYf3ThE73Z+bCdGF1mA==", "fileCount": 5, "unpackedSize": 1583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGR6CRA9TVsSAnZWagAAvfMP/jc0RvuaIuj92Hk9sLnp\nZgxNwxz0y+s8IYNFXKuUMbZeIJovw2DIrXaihPmIluw88Aocgh86XHf6Lo1S\nYrhktrcRAJzJGri8sTz/9YasU/ssUT59xP2SfdyhZa/UVhM6gcM48w3K4uJt\ndlnlgZd0hKR6WWm041iaG6Jydw6xRYIaCpOyx6Dp/YhZ1EAdSU3p9+J0yOiY\nh046oIu+dEUYhfkFMvIESVKPxkHlZYF+ZQIacYQKnHAFjfx2SUFNuqMjU/eJ\nEC2fNLJSIleEI3Z0BzJj9EJ2BUmGBEMdXW9XC8NYfVG0zrz+OUrXKg8BdcHz\nUN2GuEvbi8b2PHUo8QmDsmZUftXri/NIhCon/0IjlVQSN2xsc1/Fbm5DZr7i\nDSCm7314jJFBLVbJD8l8EELLLYSXn8HfmcZiCkJDShc5vqfJDTab93gMreu8\nTgq0xI1mQTGkaEJ9ldYBSx14jMae8P256mGcpFKWOzTsKXfHf+uaMQo26bYP\n9j14QdgD1J9TZ0G9onnas6Ihsgl9rhsbuQ2e2r9ZajWUtrQDJZOezEAyU0FO\ntww1F/NcVAcfFlwO+uy37L8Wyd59IDad20cMc+wOVtFjnnZawyulsxkG8djm\nWqX4S1SGH7g9uKkoWw9zU0riSx8MIMbRLmPkKpBOgHlRP12mTU93pdZCVjHJ\n/A2D\r\n=Xu0J\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICfGOzgUNePZQQ6LjEG74LESBcrdLwVaFfGDgtpu8qLfAiEA9L8s4MhWwhDf3FYXW/zjBhi5hkNwTco9cfL00uKCx5E="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0-rc.0_1533830265830_0.7887964960165814"}, "_hasShrinkwrap": false}, "7.0.0-rc.1": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-rc.1", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.1"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-rc.1", "dist": {"shasum": "0f3abbf0e8c649f4fdb6da072333e1e995f16db2", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-rc.1.tgz", "integrity": "sha512-wBBYQ2tENOY9hYnEp5YFNNcGfOQAvZiuhb4vgutq75s5OrW/WNtGU4pQ5TxbhEb1vLmlJawA9Qyk0H7iOBJb3w==", "fileCount": 5, "unpackedSize": 1564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ8DCRA9TVsSAnZWagAAhUkP/3+O+Xq4GrKRyrlI+P4w\nB4dbKAOLx36DCdKOTWitPJ/wnguimJx54yVAaiFPfxkb3aGLipEXBrEtPOTj\njpq/7crQc2hhN2uOXhLkeEtl/DjUxpfF7tW+uZuGR2KwUmVFRtSBNRKqIAEQ\nZyQKNpZl9lMaX83j5nR0U5wmcjA/P+bcGLBN6P3g7CqNOlVCVbbM4E1q/x+3\nwL0jNjZDIpUzNYrlNGVBy7yEybp/w7fUpgx+abpVIqM1nujf1lBG62cDs4uJ\n65OqveRAzIwy7CJeQaIwOD0e+uRtnqxZivRVUgt0REjmvoC2kv6UqmyyzDv6\nBQe9sph5ZEOAECxL/MzZIjhcZ8z1Xu4QSo+nkAsYfD6vUe5Q7Ax0ZQnNsmmW\njs5FX+BdsK17dhoz6Fx2ssEiUWFEib9JJYkjla+zOIuJdsxcSpBV1ji9Jgso\nWa0ZPtgo77YZF+09zjAQyd+oU5+Kc482pUSLiJrwGnxzGvzYQvKpaQCDoCGq\ncu3reC4WlpcATw4uhf/hF1mD5ju5wGcZhRu+JMDJ4b2KKl1FW36hQywwZGcd\nCe/rtFIhUIYL4OiHSj3x8xSN9+n31QyNnCNOf8OjDOiNfwsUH3wuOykhSm/h\nOFlMggqfUPBC15h+7L6NU2E073Nqhfdn2KLeavlt3E2fQq2KTWt28AfRVYo1\nS1a7\r\n=0R7D\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCPUQ6BcynH3bxkx5o96N7fZu8DvI/YDn1yrIKJ4WdYSQIhAJeu0ggAfld3trWPTm3nYyrT95AcC7ntxK0PpgoNNn3R"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0-rc.1_1533845249893_0.9267460172914703"}, "_hasShrinkwrap": false}, "7.0.0-rc.2": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-rc.2", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.2"}, "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-rc.2", "dist": {"shasum": "d3c70bb72c3fc5d9d319e2f43c09871b350a4d72", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-rc.2.tgz", "integrity": "sha512-zit+3wOGq0RshXeVGE7fqxGQunSmVO3clSb73AoKpThkSBcwl1doBTTfuURBUvxhryk8Wtw2+4H59CmX4VpVHw==", "fileCount": 5, "unpackedSize": 1564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGauCRA9TVsSAnZWagAAKTgQAIVDE6E1chyoHvK73NHW\nwlROy1RFiGH59Rvw/SXTrx1PQIUpbiViiVVFtiVtvdi4kVeRO4D/ZCghunnE\nEW97VS4xd3X87vz7IvB08+31TMZDtTtwXMaIYu4BfbEzejR2x1GqCN9dIsuz\ndB2KMFsXqPBUHoe2MIaQMmAMZ0mmCLGZ+/P4N19Qm+u3D3AuqiOx0WcaUa5K\nQnLwMQrvbDNWgPU2QTOHIQUCR3miXSIFvu/EXhk0t89zwWiqIR+ClvUwwEEM\nEA4okvSN7yS11Byb9DzPy8Hz/PMLqWkscZiSf4O8+88Pop49wAZ6pKqF/nYm\n/W2iL78gmBg03/W2TEhCY3QBZH5Z6g1NBYYdkRFVGrmZ2c/LK95gdCDOYbqe\nMdG0F1K/D1tg1CEGqvlQzkzp2eWCgIVfDVMFW7uwW42pxg7qlTxcH/2vESgn\nuMxz+BHFehciE6PotSg94j8iD4/Gxs+iOrBVrqJZOVe/vE2HzjuiNKCikknW\nN7yk9ApZZyChFc8dTCHIZbkemeOvvOf4ayar8/lcXALXMmwj6KJMM5SWzNMD\nqmA0+JqcsfRFL5g6Gi8+V3xbhUGSZnfMc90AVcUDSCe9jCSyqpjfptStMGXq\nfQ1WXp3qTIBBsMrWNiprxupsfG3uXudt31ek0TzKvs7xzwtKNMxL/nqWihga\nUJgZ\r\n=J0PF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAjDqp7M5I4ODdakBAKrware+SwEVlbUBc8N9ic84z6XAiA0ox0dgtQhenUaV2tCCZvYiglsV7g74YaE6az6bmGSFg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0-rc.2_1534879405479_0.7936718628667148"}, "_hasShrinkwrap": false}, "7.0.0-rc.3": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-rc.3", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.3"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-rc.3", "dist": {"shasum": "af44cb34c55738373cef2aacf39113e3ac1bb6a9", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-rc.3.tgz", "integrity": "sha512-VLSFEgtNDHaan9e44imqTB+QaKOCQwNNmY+2QYOKAN0VR3Krop1OWFXJ8V3QAU16iZZ62IRWtna9xDqc1A6EKA==", "fileCount": 6, "unpackedSize": 2663, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgElbCRA9TVsSAnZWagAA3qYP/3sYrQVQIsOjbzMdq4L7\n4iwcnsW6mfbQP/xgaw1M4PH75nmkNKeWR6odUk9XN/b9s29/ljIfP6HqHXRU\nkwDCJIYaz9+i4XVwYk2MDVMACHFxFaXtSSSGz+nOc7aAQYFQs7UfmsNryH4s\nLGeISWpePauGSuPRjDUHh/X8lUnD38HzfCk0Nk4Fjea6C9vZeNn7PlAETRgS\nuxWffd1+g4voSgHvybzk2pcbed80s17say8FZDjhfsL6VPLgLJ4IcIuNTSoA\nGgCm1ZNRlev7Y4n3V+jlJooo6XVy9nmaUg6UvWCDo9nhd3Kxmpa6J38cxVt2\nkNXXHOhwVXJKt1fsuy6aGh2BDqfDyQJZHifvWzR1p72DwCMmsuzgA5gVigCt\ncacHDmirA3gYjxJJjOoEg8UyyipdZKQozl9wSxZt3gxZAxHdyIWRMwAPoot3\nHpVTdkoOd4Dpu0L0SNaVp60itl4IYWFo8OjGGF4xtgQwOOLdIxMQrK0ABJ9S\nHirCT5CQs1ytoYPHZkjE/5XJsxitG7o/ro/JAoFPl3jbLXQXy6bSFDDr7wz9\nyWuyCr/aaP97+KF8ZKR0AtQHmn8goxBn6sjYTjwxO4hu8yg9tPSldLexZ9No\nBgpisfuFyYTLBfFuO0lkTMMRYYuTACaRzBhwbmv4zvq6+CsJjME+p4ZWBEPs\nSzB6\r\n=VQCO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICt2a8wuHcqoNh9haQEIGoyAQ1wHSCoRByt4K2ZytdP7AiAoQODmjXsocRsBVgE7N0Ztd+l7qYR4txEYyHxv88MwtQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0-rc.3_1535134043103_0.6766504202746897"}, "_hasShrinkwrap": false}, "7.0.0-rc.4": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0-rc.4", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0-rc.4"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-numeric-separator@7.0.0-rc.4", "dist": {"shasum": "43bb3394c86895559c9c7a8aff5852f3064cac32", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0-rc.4.tgz", "integrity": "sha512-zuwKy4i8IghGoNMwTiLRjgPR/tO8z3MpcKcXeaGOu6WAH42lxxzJ6+FN70G+uYEm6DhCX8RoUwAp0XmfhuQgoQ==", "fileCount": 6, "unpackedSize": 2665, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCo4CRA9TVsSAnZWagAAIn4P/18IVeRzvAuj1urJ3vsc\nMm94qcnLwllJcDQDQEwt5ps/U1ZtG+u8ycNKhnP4jE+707iET6ocOOoj0+3M\nr9vhudRHKeELueJJPFv7nQHu/XMjwXvIrqIGjnNoAKbA5CBO+YakrOQRmJco\n1MsGnQLITPcIoX1SgqdrnSl4sQXK7ObRnri3NGjgCsssA7I/GRAt/eVGZUbW\nztXR6QbfnFRs+YKaptQ0JBr/odjsbeZHWgfaIHMfX4h+e86q1mXhS8NoqCoy\nV3mjFShgtnOP9qf5G+hKuSw5YATbOqIGW3g5j3FvA3JIte8ySI8nsXhbm7lK\n1xmfIFMqdRdZ65i5IDCmEc0aW4Ckw4fxIaXVoDbQtxMe1QdC0q6WAnrMLlrl\nqCtenIiXleA4Bvys83GPULt9u5oB7vThZBs15koS+aVZ6njmqgcdSy5q3M+B\npUVpOHM2tDFWyE83n5S1XGJCW26JCY5bRNt1QP4H3XVa5mB0m5OrtuhpQKME\ni1VCfd6PXRCEYvHGZLA1P5q+uWcqpeOASTVPL07hmh+p6mnqTTMKd6T7gDee\nlz149gtJ48wGBo30urF6W+OW1WRDilPn0UBhkZyQhc+HQnlG0c34rGQr3CUH\nT5Yy3nbh7zpKXRJCaEmvViNgyvjkouK0puur2C3npktBy8X73Qou20WQaIuq\n1skM\r\n=PRbY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGn56Mtjw6lqToKtPoTP76gkF3zM9fi8MOlvFT5W8LsDAiB026B4ET4ULM6T8kfELPgMx33UFthV2n0yPAZzTldqyw=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0-rc.4_1535388215734_0.5517712948808964"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.0.0", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0"}, "readmeFilename": "README.md", "readme": "# @babel/plugin-syntax-numeric-separator\n\n> Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator\n\nSee our website [@babel/plugin-syntax-numeric-separator](https://babeljs.io/docs/en/next/babel-plugin-syntax-numeric-separator.html) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-syntax-numeric-separator\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-syntax-numeric-separator --dev\n```\n", "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-numeric-separator@7.0.0", "dist": {"shasum": "9594c7ce6ce8089a14d732cb9f6b1eeb047413ba", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.0.0.tgz", "integrity": "sha512-t9RMUPWsFXVeUZxEOhIDkVqYLi1sWOTjxFBAp8wJtaARilvkGlEQvSObd2W5YKicDktINI9XmdV0sB2FZaLOpw==", "fileCount": 6, "unpackedSize": 2650, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHBWCRA9TVsSAnZWagAAM/QQAI50vh41Z70oPNDUhRH+\neibCK70BtffWHiSYxm/xKwmPbR2sEtD75qxkSTjZrPsiIGUwSpEN2268fsVe\nssE7XNJIlR+Gtj5wjkW4zGgt6hozftQVAxLsMc9RKu24ZVrp8hYA9FWuLAAt\nTX1DSjolxjrfXkJPvALGekaTAMkEj1fXj2Ps54u7f3grAPtr7PNa8BUsNxst\naPrlcbLHwvv/QNCqXRIV4aCrVGj8nqH3k4jEv3KMWVb7lS/p2v/i04FH9BUV\nLLZSt8Z7+mftO1eVLSomNGaWkkdFrsBmkAfSKeuJwXOIVlpRCUi45Yi/TKWx\nC4zFFfTL5nkwMuvqpRWYLdwr67C+zut/T4AmGuJhctuFcmFganDRgPOjoFOm\nF89Ahs9BzD2Tokt3+VrPetfcpyAzveezeQoSyRm751lO9k1hvrBlXj5y7d+8\n++pC1/iB5HTq0ccaW36y2boUe42NHlRAP+k6oqylaZroED/XEm1wUXvRbtW1\nCyIO7W+ISpbNuFBEZSZpRMQ3w+V+OmzSYrU4Y7zhsSprHxTDUf0t+SBnfKZe\n8kw0WDxy/pbGzP5f+TvShYR5KZzyGNOehyFO5yFaucSsXRHHwTBIbIbdK0kE\nC3zifBtvVPAmxd+oblzQgoB9hwancpkfe15VfhKRFAPv2zNuRRXjU72pacHb\nBz6K\r\n=Ktmy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEwbauH8pjHj4FKKLjwBTBFf86dJzXs+vtL37vyMitrZAiB6s2iTgKbAhcf0Jo0ivFt6bRZNGZz/XDWoa/b3CLf/xQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.0.0_1535406166243_0.7778291243872035"}, "_hasShrinkwrap": false}, "7.2.0": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.2.0", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.2.0"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-numeric-separator@7.2.0", "dist": {"shasum": "7470fe070c2944469a756752a69a6963135018be", "integrity": "sha512-DroeVNkO/BnGpL2R7+ZNZqW+E24aR/4YWxP3Qb15d6lPU8KDzF8HlIUIRCOJRn4X77/oyW4mJY+7FHfY82NLtQ==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.2.0.tgz", "fileCount": 6, "unpackedSize": 2742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX1pCRA9TVsSAnZWagAAg+YQAKBN8het7DH5OjPPf15I\nw+8xfkKL5i1Y+mc6hTpzl++xGs2GJgIi/JGFBa2XUPUOnFOGP4rPAQTO6dji\nlp/UXOk68aw2UVULMVPOPlZ6hBKROvuR3sNlvsycA4QgSaD+h/kTWY+iRjva\nw2yX2imTq34PI8gVjqmpvnhtiMEO+4ZxZbGLT8ouLITOhFepWUFrL2Kzy0wj\ny7sQX3TuI20Uuwj9vwzg574D9mLIoB9bEq3YW85WuXamGPDrzslUV/oLqK1u\nSAut0prD0LcN4tRPIGArCav5bk9Sy6Wh3VdpLlCmab4uhjbm7HGNEwAFwMTg\n4WhJVjGwbT72Pu/UV1Gep6wlQbF9rSc+UTDZJmmuxA4RXetwyy27HZEdVcll\ntxxtU+t1yb9yx1iqL8sKaF288VucExGUhiLqzIwuBNqk8Kxp1ri+N0QnhDhR\nnSyXycqEFhVRDgDExIjHh5DsdR5L5D2m2EpEPG92ctG/osUBx0GVrv7HBDh5\nHkyw80MjXMAF1Msqsim9z0zmHYLMHif01Bw1ckIJNi/Oupz5U38bbnZH3m7R\nb5afCMdXjrXqkIaXRV+f0BXN25dNiEjTRrdBGlBis0Z8YdAGsjhZE/gU9XrK\nWgQYwBhzQui8wUphRFuduUMf1ZxN22zRxDjN+7zu4kj3PEwEL55QzlRwvkPL\n9ZhP\r\n=tgKm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC2duKRMIcufBS1ZQ9JPIUdOtLpVhJLI7N1sDzRneNgswIhAOA2jR016B/8r8m8tkjBPK50CTQS+R8P6RoR7mlSg+no"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.2.0_1543863657192_0.9258637585196037"}, "_hasShrinkwrap": false}, "7.7.4": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.7.4", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_id": "@babel/plugin-syntax-numeric-separator@7.7.4", "_nodeVersion": "13.1.0", "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "dist": {"integrity": "sha512-vmlUUBlLuFnbpaR+1kKIdo62xQEN+THWbtAHSEilo+0rHl2dKKCn6GLUVKpI848wL/T0ZPQgAy8asRJ9yYEjog==", "shasum": "39818f8042a09d4c6248d85d82555369da4da5c4", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.7.4.tgz", "fileCount": 4, "unpackedSize": 2700, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/zCRA9TVsSAnZWagAAFWgP/3S9JRiPXeGoYo3b9/m0\n1z/Q2iVGZofwH1j2P0Ek43mD/1xH5XUB9r0Egf/zhLmnjzMbUeFjyOZRV0Zz\noYr4fIxqpL266ui6ZgAnponcocUcgk+SmcNjUEnk0D1l3eyLpbqLMcEbOEnP\n4rV3mjFlCzxx9Q4MLTWXo2r5E8v2lkpKz4BW/4HSnuW8HJNMhBmy4GBJhXzg\nJmLAcqx74p4/MJ3Yixrr18nHk17pd9nBv1YvwdANYvHz4jOi5VPdZ/CAFLqN\n/TMICZqO8rTOgJ8q0nbm1lPIsEv39VHEIdxGKq/aqaaGM0DwKe4yflTc6MWx\nZ+aAzVtuKE4AyUWp18F5/cCA2BMx6rcLgrkiRX4in+JubBGgU+RiXqCfVtzK\nBgHvPKSKghxuHquuS12a2NhVL1b35Y1knsxW3XL+zAk4YThfLnf1QnRncCCt\n3IGtzRK1DUCYAWri9x4dSV9qkbO2xzMLCoILHtq3yEKi5vkWRc2/bxQZuuT4\nBqxzbPfXWw7FhvzKRv55e+5XKAYejhYHKQa8+k1cnFaF5rHuH2hpVmN2zPvM\nGg907heBXII/X9+NI/7Uo3Z7aPFeuDMY6l2QYhfR/1QFBE9E20/Z1UTXgESL\nnzeVTOAcLUw7UMZE4YpaVO/Zf4bl3SlT+HKlpUB8FBr1Cv2ThSM6od9GhrAd\nnNMX\r\n=Ijdt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHMS1TNljEcWpmQbNFlifGz+4rF1rgXWxW1Q3v6+VEOZAiB6fSeRrPqS4JALxxA15hhtYIZVH2mt4SrVBhY+bWslHQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.7.4_1574465522817_0.972523290220036"}, "_hasShrinkwrap": false}, "7.8.0": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.8.0", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "type": "commonjs", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0"}, "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_id": "@babel/plugin-syntax-numeric-separator@7.8.0", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"integrity": "sha512-GEYjlQAr/zXMVuce0Nq/boG4wNjJxdIOc4RKcgBEo/r/J3LrghZz8+ZYo8k+OuLJKvvV22k84tBuw1YunnLgCQ==", "shasum": "a85ef0a526f856af73bb9fa646894adfe9d1d5b1", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.8.0.tgz", "fileCount": 4, "unpackedSize": 2722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVWCRA9TVsSAnZWagAA4gsP+QAXBeNT8BEo1AgRlVvr\ndyXcEz4vzgxYr2ju2E4BY4J7wJZZl4Gljqy8HzwgnEx+IyIyyhS5OhZwkGVG\nn8FpUPddZSAoip7XqdYZB9g1BFqG4Eyrd6yEl/NGGK5PAzWRLGHUuYt/XPm8\nnKDL+1Jnm64TL6z2iXQEoeiwKOXCRx7sXr+DG5LjaSAQtc3kf7f3MJIwbvMK\nM5rYZLy3559sdp+zymtL9JJ4FzY5GWM3GKW3bu8TX7ikVvFM7T7ai1FKx7VP\nk7ZWRhx18NNGmMsQUSWLeAeHwS/36ZXi8KtbLILrYlmLSQT72MQGfhmNEboY\nKASJPKzuIWGgcrm7SECgQg8U9fPWgZ2gWsC3+zxgfkPTK4Xt7FTWsJDveMJd\n0fzyCdLLqxSyTm/5QHCHgJJm4Aftw365OYo9Dalv8q8uEpF7YWk+rpC++Ssc\nnYreV6bTFSjB5NtTWzWMuPz0qWuKSYbDVdnOlGJIktV59tMAdSei8Z6ons7u\nvaXmEqFvULTgbOK6pxyOgTtQuFgCfQcrr9SnlmadP5LX/Ap0CtQg9LYHjX79\nkUV5Id4hdvhNqhkzqzync6xS47lx8qmqEgCsUzmA6L25YbSdBUu9+e3SUZxs\n6ocimqiufUH+vM8/gjDrcM6fpKwSFHXnjYIz52wT4eEAhVl96xjaGB2MvV2n\nyzcs\r\n=+YGv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCuJzyuREx+xRUlPNaAlsMERvP519yAXlQ45t2rt6dCsgIgRPBIo5IqugGbmIIWEiRXNeM7j62kf7J4MC6xT0Go4zM="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.8.0_1578788181814_0.7948007140803144"}, "_hasShrinkwrap": false}, "7.8.3": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.8.3", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_id": "@babel/plugin-syntax-numeric-separator@7.8.3", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"integrity": "sha512-H7dCMAdN83PcCmqmkHB5dtp+Xa9a6LKSvA2hiFBC/5alSHxM5VgWZXFqDi0YFe8XNGT6iCa+z4V4zSt/PdZ7Dw==", "shasum": "0e3fb63e09bea1b11e96467271c8308007e7c41f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.8.3.tgz", "fileCount": 4, "unpackedSize": 2700, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQACRA9TVsSAnZWagAAFKoP/2ylZ4DG3YUx7k505xKj\n4bFtjZwMXU4cKdvdY16jzJOsr3CwTKgmygsV1JjXWct4WKYmQ8TbuE1gKZZs\n9xi+z0JLKTXigisFrNzus4aETvcjP5ZO8RaRc2dkWK5HojaEBjS+omHDCCsH\nqJOZMxURgAe27y9ejKCffN87B99triVEZL4P34zL+X63WJhO4l3MVnvPiEfp\nBfdXcFVUwtgdF3G2mW3IBdjrGpgbnt0j7dNK4JIWyyfw7EZJXWjXmpUPTVE2\nLgZhKHnHpMsMDWIhHgo3iH5T2/VhoVh7lvNyU636W06AkNUw9oj2YPjugy6J\nN2xsScLA/eaqN2V3oHIVal4Uzqarm/zo2dTqMw6H2Hp0gIfEqoxNvtsmCXhN\nqk3WlyHi8TlT88k3F1gsNUv4IUohN2KbVEtCrll8hHf2CQ4GWgNWYVPKVRLX\nhFTMLDhph+RhomEZG87DoWpnvmhHil/BqIBRPICWcU1risrGrUx8NqhybtYA\nKINtBuB+2Yy79nmNV7y3cbX0+sF9YcH/ThsXULjjwCrTKX187GTcD2iFtEnb\n8+tPCaDGW52o2TVDPIG+bqv8W0J0WkL3KnRUavtwWUV+PrpdeOHry8gYsE2B\nKwkP/AGn3HW35HYZLCZvfR5tF+1Jy36oYnOHQnd+9HGVtKVH9ZKpSYgG+Die\nDus+\r\n=Kr/a\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCV8it8PJcfpj2luFNxahrRNFhndkiXDXORvaM+uDfBRAIhAKj/BVb0JxUBQ/fmlSwhwmTujO6liHsEXyLveik1yxhF"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.8.3_1578951680240_0.42675514841668116"}, "_hasShrinkwrap": false}, "7.10.1": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.10.1", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.1"}, "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/plugin-syntax-numeric-separator@7.10.1", "_nodeVersion": "12.16.3", "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "dist": {"integrity": "sha512-uTd0OsHrpe3tH5gRPTxG8Voh99/WCU78vIm5NMRYPAqC8lR4vajt6KkCAknCHrx24vkPdd/05yfdGSB4EIY2mg==", "shasum": "25761ee7410bc8cf97327ba741ee94e4a61b7d99", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.1.tgz", "fileCount": 4, "unpackedSize": 2751, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSOCRA9TVsSAnZWagAAF1oP/2hjKOT88mmswWbWs6FY\nXNNo5bXjU7YpSYHZL9vQq3r07HO4JLFWM1+9ZEqKSrUfGCyQ1jGIJMZZ77ty\n1pzPNrPRekFZ/6GJwBHeR3nXh11ZLHaABozxe0/nMrvwGOdnMVi8/ugm8J6r\noEZPuvRlqxR3W2/0fmr59gk5aUPjA64TMh4VWksLgvHOjCFhcFVo+5IPSy72\n/E/5jqoJid3CLp4D3PaOvSqgKE0vLTXmvgkXyTRFveB/+TwcBfOm79IS5vNs\nYdQLRgbUHi2ss+F4AOjkzltrAGNMY+CodPCKr0j81h4ruWOKbyJw6wFNrxsE\nMwG8PLLtN3g7WaHt9VHCB9lo/dzXb3lIQG2xXykhBespRa5M0DuNhnflZV3n\nnDSfXh8qGiBFpn4AkuqjYQLGepnHLI8tPQn98QNZQ9H2hFbPC8LKr/mcbPRz\nxQfi005piHdQrGjJZoIQJuhLApSYd0IMChpfi3ryl6/h4HerfSDLjoiqWkQ0\n9GNOHC82AnY9r+qTk2/+A55NaZ7jtURmofS4wk47B/Vvanbm2r4ViNRTC+gB\n/bcZ/7Qqw9P5oIEpBlPWgLyCcNiwleB/dVDM1m59ztolKSkLz2FdgTUvWAIp\nmrH/hzfxVFouYE+0jq++GD1ddhYM/+qsRTTZMrtpGry4btxL020fQD1bOctX\n326o\r\n=zrEM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDy3KM2cSp96OZ/BPLkLC1Ot5uqMtBRJJUZf/r9yKngKQIhAJkoNmNZM3bqex1WhXI2YoQV4wkUYeFi9tgDEi6stdVN"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.10.1_1590617229601_0.023484025794332286"}, "_hasShrinkwrap": false}, "7.10.4": {"name": "@babel/plugin-syntax-numeric-separator", "version": "7.10.4", "description": "Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/plugin-syntax-numeric-separator@7.10.4", "_nodeVersion": "14.4.0", "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "dist": {"integrity": "sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==", "shasum": "b9b070b3e33570cd9fd07ba7fa91c0dd37b9af97", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz", "fileCount": 4, "unpackedSize": 2751, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zoWCRA9TVsSAnZWagAAJBQP/3WYOBSY18gf2/ZUiSkT\nx3cwhfAsZmX2OLSkudocYd05fHK25xnXuGvGAfLspBoCG18Y5FadxI8w8iNp\nZrCR1cs1QY40lLr59fvkfuMBpmte9TzCU2MDGYcbUfYChgVJYqsPojMtJwbq\nkiDw1/DdkoxB0LUr48KhiaABttyB6SsRmxcKzg4DX3zw8Gv1LlrljUf1euRn\na+iTjyncIIiMWd2ZzImxZxxAH1nuMpghmoCqKcty0LlzIT/eULRFfos7mZF4\nB4XjAxEGtILM60f8a64s79GuV5iokataSmI7D46fv90YPwJRIXVoX1Ud/wlq\nNX0MGW4nWfN+8FAz46y+YvT/gLXujkC6gb6ep16PZZAoSoTWgvmO1mtkQC8N\nhw08H/HE2/RUzesydyw+3MMjlDcaCTKo9aspTQivLa5Trv3q+7B5W88TsHJw\nWQlnDeydt9rg0p0YA1AomPkr/9V9PYkCBX4KrRcmDhMjocl8GGZ4LD0uOjY5\n7miGCyw0QPIUzfPWJcxJliUzbRUojmkddWITlHPl++VBK/ZqgJwRQBM7vHd4\nIqg+MbGXMRLaX0r3oryBLIyCxpvTv+w+pmBNL64cm6lWC3rKVbePYCUtZ5/V\n2+7pmmYVYLC8oqBHZVSOFoU8Pz6rn0e1wlOb79HyK89OLKb7PaP2RzfmTf8X\nbis1\r\n=ADU+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGuou/xITpWKOBSjGZSBrR2s/7AJMSxpTpSJTsHP4STkAiBbm182XAgbKVeU0AZ9hkw5CrSqbhM8SjQAqCztFg6rlQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "jlhwung"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-numeric-separator_7.10.4_1593522709585_0.9098885821153804"}, "_hasShrinkwrap": false}}, "readme": "# @babel/plugin-syntax-numeric-separator\n\n> Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator\n\nSee our website [@babel/plugin-syntax-numeric-separator](https://babeljs.io/docs/en/next/babel-plugin-syntax-numeric-separator.html) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-syntax-numeric-separator\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-syntax-numeric-separator --dev\n```\n", "maintainers": [{"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}, {"email": "<EMAIL>", "name": "jlhwung"}], "time": {"modified": "2022-06-12T15:01:03.817Z", "created": "2017-10-30T18:34:23.212Z", "7.0.0-beta.4": "2017-10-30T18:34:23.212Z", "7.0.0-beta.5": "2017-10-30T20:56:05.692Z", "7.0.0-beta.31": "2017-11-03T20:03:16.955Z", "7.0.0-beta.32": "2017-11-12T13:33:05.733Z", "7.0.0-beta.33": "2017-12-01T14:28:09.061Z", "7.0.0-beta.34": "2017-12-02T14:39:08.908Z", "7.0.0-beta.35": "2017-12-14T21:47:35.933Z", "7.0.0-beta.36": "2017-12-25T19:04:24.865Z", "7.0.0-beta.37": "2018-01-08T16:02:23.516Z", "7.0.0-beta.38": "2018-01-17T16:31:44.488Z", "7.0.0-beta.39": "2018-01-30T20:27:27.890Z", "7.0.0-beta.40": "2018-02-12T16:41:24.555Z", "7.0.0-beta.41": "2018-03-14T16:25:56.431Z", "7.0.0-beta.42": "2018-03-15T20:50:33.198Z", "7.0.0-beta.43": "2018-04-02T16:48:17.878Z", "7.0.0-beta.44": "2018-04-02T22:19:59.629Z", "7.0.0-beta.45": "2018-04-23T01:56:21.857Z", "7.0.0-beta.46": "2018-04-23T04:30:50.833Z", "7.0.0-beta.47": "2018-05-15T00:08:29.596Z", "7.0.0-beta.48": "2018-05-24T19:21:50.933Z", "7.0.0-beta.49": "2018-05-25T16:01:30.057Z", "7.0.0-beta.50": "2018-06-12T19:47:08.844Z", "7.0.0-beta.51": "2018-06-12T21:19:35.389Z", "7.0.0-beta.52": "2018-07-06T00:59:20.281Z", "7.0.0-beta.53": "2018-07-11T13:40:10.694Z", "7.0.0-beta.54": "2018-07-16T18:00:00.644Z", "7.0.0-beta.55": "2018-07-28T22:07:06.867Z", "7.0.0-beta.56": "2018-08-04T01:04:41.666Z", "7.0.0-rc.0": "2018-08-09T15:57:46.289Z", "7.0.0-rc.1": "2018-08-09T20:07:30.736Z", "7.0.0-rc.2": "2018-08-21T19:23:25.957Z", "7.0.0-rc.3": "2018-08-24T18:07:23.180Z", "7.0.0-rc.4": "2018-08-27T16:43:35.814Z", "7.0.0": "2018-08-27T21:42:46.296Z", "7.2.0": "2018-12-03T19:00:57.331Z", "7.7.4": "2019-11-22T23:32:03.022Z", "7.8.0": "2020-01-12T00:16:21.901Z", "7.8.3": "2020-01-13T21:41:20.366Z", "7.10.1": "2020-05-27T22:07:09.721Z", "7.10.4": "2020-06-30T13:11:49.810Z"}, "keywords": ["babel-plugin"], "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-numeric-separator"}, "license": "MIT", "readmeFilename": "README.md", "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}}