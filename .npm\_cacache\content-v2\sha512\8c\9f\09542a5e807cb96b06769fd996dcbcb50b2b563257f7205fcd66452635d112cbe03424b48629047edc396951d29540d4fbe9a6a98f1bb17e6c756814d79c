{"_id": "@babel/helper-validator-option", "_rev": "45-1290e4a8f4b153e1d3acaef9f68a6f97", "name": "@babel/helper-validator-option", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-beta.1"}, "versions": {"7.12.0": {"name": "@babel/helper-validator-option", "version": "7.12.0", "license": "MIT", "_id": "@babel/helper-validator-option@7.12.0", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "1d1fc48a9b69763da61b892774b0df89aee1c969", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.12.0.tgz", "fileCount": 6, "integrity": "sha512-NRfKaAQw/JCMsTFUdJI6cp4MoJGGVBRQTRSiW1nwlGldNqzjB9jqWI0SZqQksC724dJoKqwG+QqfS9ib7SoVsw==", "signatures": [{"sig": "MEUCIQCM9L2CB9tqoUl5Axjwo0Sffm1ZraunDG79gYi9o6+J4AIgPBz3Kd1Hqs6oLHpW1n+woFYea8jUmQhzMWFb9WnCRhE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfh1lbCRA9TVsSAnZWagAA06kP/22/azOIbd0m4z3r7Ky1\nbyUz6BvnRc/TnnQvwk5xopeyel+PI6vcuBCOsjdS9ll3TSmuiHBt0lwLULbJ\nEHl6kNEJFLSta6GW2TOidZA4CYxUzsgKpCABJ43AXsSop8WE5YaJrEtzXNoI\nan56eli25kfFBreki9vYIm7vHbDF9FqS/Gf91G5T3dk+eX1T/RLv6kTbQp7b\nv6jyaxnaXj2WPSmjE/0hLySxeHY+7dGcVQzT3/L2rQTdG31gend41HXWtLPb\nJvBg9mmeCg3n7eA52FBkOncymyqB5SRNZQi8sj8FJMUnA+hnqYlEhKnhtCBE\n1XcGzj+bgPzWMCTCxCUvz/Z8iHsf4ulMEeZUFl2hMXUuC6ALfuEZcEyqkwFk\nQryF4keNz08GUBP3+OwgT1Ow5KdN6gV9j4AcY8M2720PWMwz1hSfW+F/HTgc\nbpywUrNxcaqdf3UKE++foKr75tsintEBo2DRwVLdD7WyPZ1TqHz+VKAKpuqI\nhB5IpyaxnlJvkwhkLBstcs+qg2mePZ7948oiB6ULmhDK/Q7xgLO2c2TSWnNq\nTl6n562H3EesgDNxfC5a4IolyVEcQFUB/dNufTdvXaEMBeSepKw6pSH9KX29\nu81DlTUFzz0f91laCunnzOHccoxD4IepdQJ2PKYwEVnPoOIiJfYK59HPVp+M\n7T3P\r\n=UcKU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "exports": "./lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.12.0_1602705754883_0.3419333339795487", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/helper-validator-option", "version": "7.12.1", "license": "MIT", "_id": "@babel/helper-validator-option@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "dist": {"shasum": "175567380c3e77d60ff98a54bb015fe78f2178d9", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.12.1.tgz", "fileCount": 6, "integrity": "sha512-<PERSON>p<PERSON>absXlJVWP0USHjnC/AQDTLlZERbON577YUVO/wLpqyj6HAtVYnWaQaN0iUN+1/tWn3c+uKKXjRut5115Y2A==", "signatures": [{"sig": "MEUCIQCYoe8YR+AdpikCr8owLDzOU9AAI511rUkjzUk8YJHArgIgHac1xRA95iHMLlBLBFaw6M/9ShcJYVLw3jQMDEIeUh4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM+XCRA9TVsSAnZWagAARMIP/1oElgEAzk6y9So/GDQ1\n6UITFoSIfzzQaEJ7yTI3voGGIPUv2QsUL8IeIM0Wm3QPAyGZ2a9PmDW7GgIx\nwyA0Qn44mxwsdCJ3BkNP/yHO3/YkIWT8n14+w8Dv+SIS/uhqagL8fj5HHGBo\n0esfhYOQL00rmjDebXx+Ya13JI4w12l8PkXyrisIViregPdk5dfPKrtd7hZq\nLv1mdjc+JxyBRDv3QvLDnZ6ASZVaOSNhYTzv4PftnziIQ9X7KHaS9ExqGCnK\ne5w545l80vPH8B4GLyhnF48edW5n8moA3qeg6JkYmgpBZAQrSQeSR5tDqaof\ns7KQUe6l3hmsiNVuHPz2503EShCG8uZhiWtefMLqtEYCTh9g5wB9phNyO00L\nWWpxR2+chBRvKKJGyhXasQSnr5qLXU0hms2M2iGxV2VLnczavt55rhTjGczA\n1QBamGBsCYcsn+VgW/wPv8LDBl08yaR75/+SfbYb8wlhfYk892h16p8Mj8df\nYUocY6iEkFB+crj6USjGpqIomyczaMvfsXojtUR+z64ZUn/chkCM2E7ArCaY\n85mIUTyFccjtyFxKqC8xzf0VbiqAHrCCPw/UJ+vw1LrN2SMAe0Ay4Bh1hfax\nDhOl96HlDsMftaojzGSwSBzGZ8Dx/UpLnaAfGACi2Yd2uGVOPQ6pJhQKBMLv\nq2f6\r\n=BUf+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "exports": "./lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.12.1_1602801559451_0.8035683032542031", "host": "s3://npm-registry-packages"}}, "7.12.11": {"name": "@babel/helper-validator-option", "version": "7.12.11", "license": "MIT", "_id": "@babel/helper-validator-option@7.12.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "d66cb8b7a3e7fe4c6962b32020a131ecf0847f4f", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.12.11.tgz", "fileCount": 6, "integrity": "sha512-TBFCyj939mFSdeX7U7DDj32WtzYY7fDcalgq8v3fBZMNOJQNn7nOYzMaUCiPxPYfCup69mtIpqlKgMZLvQ8Xhw==", "signatures": [{"sig": "MEYCIQC64g0wPwTIVnB0oOTHy57xu1P/UqrJUCH6OdkR9GqbRgIhAPA2sP011ajDxXpi/nHp69grcE1Gcw3dgB1EhOK8iVTl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4435, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2U3TCRA9TVsSAnZWagAA4TgP/Ah5lskn+zF8YreOscqK\nOxbfAENOsEDEKBZ42mwNUEf/D+Ug9nDFNF/Vn6DPZTRJiwR9/z08He10ZEel\nLg9y58A5zH/y0CweI3UY3sGBtz7CFtV4gHBxXv8vcGIzdHUhbbmW7QOyiE9E\nGtvQ3iEwVjvbSxbCRSKggDGxx/Xwiui+snpgQIZVrABA9BX/KTgVftb5apHi\n5ezeJwXcULwwQmeJWFWIE4Gg0CwaqA2U6S73xkWNrQQcmTjpib3Sg8viorP3\nXYHPND4dUUxoHbRYGKXO6aUguVyJtaw1Ocsxv+cQQuf7g7xkxYFo3gW2tgfT\nX8CVBbM6u6n+uTbZh2M0m6z0/dDkbF+ygxmPBae8lK/PfXpoBwvye0Z8DiG4\nlOZFKgp5ghUX+EyoO/Hv/lo5ux9t4xrkQakpcZ9oYmPcKmthuLyZ577jqCuk\njH0MlCG8SqPMEw0saXTGb6DePSL7akYRJNY7fl7QXjTI6ufBKQlVgW3p58+g\niOJ7rf7X5UZi817IEddleAXpDSGgME5lna0fmJuirsDyH90n+FqfbnMXSxvr\np5OYWtjhTJqLZSyEfgPEk4bFVsl/qmllqeRlyj2j8694/udN6/Td8Wiw4313\nCBDY+7TdgQ9fG5682wWLwxsPkzx7YLPNg2901tE5ASWOdl2pHaK9QGW7vQIt\nHEya\r\n=Px/z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "exports": "./lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.12.11_1608076755419_0.7592676030810566", "host": "s3://npm-registry-packages"}}, "7.12.16": {"name": "@babel/helper-validator-option", "version": "7.12.16", "license": "MIT", "_id": "@babel/helper-validator-option@7.12.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "f73cbd3bbba51915216c5dea908e9b206bb10051", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.12.16.tgz", "fileCount": 5, "integrity": "sha512-uCgsDBPUQDvzr11ePPo4TVEocxj8RXjUVSC/Y8N1YpVAI/XDdUwGJu78xmlGhTxj2ntaWM7n9LQdRtyhOzT2YQ==", "signatures": [{"sig": "MEYCIQDhz13sZ8LrSHob/El5LEKCrbm9CaMXqy3g2FKEdqOEpAIhAOG2PwlPoqa6aTQWQSucIdkWYZrVILfgH0N6cnwgkVgF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3326, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJbPeCRA9TVsSAnZWagAAsVgP/ibDuwzopgfKSGs0bFqG\nljac+g6+yaOLfwgcOmADOd5W9aMvFYqE0Vw7jtBkhUnyJTXwhSGKHqf8VQbr\nEEjBzL68gKTbtNkuNZ5Q9WtqqRBlgCQpHquyneOdSeliM79cbTzdYd40r3Bk\nffZbw/p9AYb6qdckeor2HZ8fs4fx5Y5nBuvY04YIV/7D1JQElYDJJoqckB/m\nyWFHNnmEyO4QGLuKmKRjtbVNE4u+l4L2JXge+bz8mtUe5gvgRamgqub4XMB7\nzMPVedfpFmbOvglRBqhclWxMv5wuaT598r2Nv+KnbNJvZApcLiMnBfozkM1u\nrwMkhOAXQQpkv3cd6Nsa/iZUdhoW/A0ziNnaA7gUNenN4WrrJq/6wU3F6Rbx\nFIw9tEY5uSOyW3vKCPCiXyOGIKV2Po4GU/j6j7th/bC5Wh2aG1i0O1uvKM9b\n0nUBSIpkJ9UmhI3bbpV+3FzXW6j/NPF61UfiIhLhQVlrd0szoZB28OpMBj71\nZL/bYWVfwAb1D1Dd08/GLF5OdkOLjvt+wUSaBAMkezcVbMnHAyjyngGSDb5W\nSXXuVYsOe5nERzTgu9zt2u0awmCjbRacx3ogs84NE0mJ5Zrt2Onpzp4ht67f\n9djSgx1qw8NOYtKXPjI5PqhfVgoXhuQvi62WEtdFDhwkgMbgzgzHseWmIghH\n9L5A\r\n=0Ib+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "exports": "./lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.12.16_1613083614266_0.46375468923971197", "host": "s3://npm-registry-packages"}}, "7.12.17": {"name": "@babel/helper-validator-option", "version": "7.12.17", "license": "MIT", "_id": "@babel/helper-validator-option@7.12.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "d1fbf012e1a79b7eebbfdc6d270baaf8d9eb9831", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.12.17.tgz", "fileCount": 6, "integrity": "sha512-TopkMDmLzq8ngChwRlyjR6raKD6gMSae4JdYDB8bByKreQgG0RBTuKe9LRxW3wFtUnjxOPRKBDwEH6Mg5KeDfw==", "signatures": [{"sig": "MEYCIQCWFgVjOVXkw+hHNk34OcZrfxEjbz8lHYZvO5b9kkqgTAIhAI4MPq6tnLnIOBQ+V1AqR6lmD1CnWyc8q2HJlQV2+/cj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4432, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLoPWCRA9TVsSAnZWagAA2N4P/jv8/BwkbUB7Nt0bDc/4\njdvkG5oBqOmKBkmgATi+2RVmVmaK3QIK1m1DYi5eDurwFgY2MuII0CxpYTn7\nRCakHGfmqpur8UMT0xkyj3ZEwrHa7UvOCJPUn99z9QO2D5QxtHdjVH4IF6R8\nVhA0dB/Ofs9ox74k37TyEjb9JnC1uDrBhF7tACZUaIxNoywGr0+RX7wUDkcs\ngWz8C+3XIJyBuX2WJTNZbDIosjoN7CSt+FfPgLmROYKHPQ3TEfouKAzGFj/0\npmwCpr+CiLlROeYO+Q4HyUBfNAFYlVYibVgIPp+xFxc5Qyl69Is8CaCi7D7p\ntFzkoGPeyJb5j0h/nyUvgzqf+jdrCOiXVo7k2kd4AqX8mcaQj7yge3t2FLNv\ndg+gLNl+wuQPh1foH8y3ihUkR3gYs22JGx042WH/6euKaHgKidHtsfXMlRHG\n3aDRCWZ1h2ClEqx+IB7Xt4ob54QuXMCpufaCXUWH7yyTqr4gpiri8wVzqL/j\nTycPzE+//ehTAnjyKLykcMzHG5nQl1eLqRHcHeqVBLzF01nqsgJguBfrmGIi\n9bNTASsUCoR0CCXWNRDfG8LGcuOJ27lu4b2Pe20ycIRnUrgFxROCLSbifUBu\nl4Y/z5QhS7qsxs95xnZzLNM5sl0aK2+0D4q+E6hU8bFwV61k8V91Icjb2Ahb\nurpd\r\n=Awxn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "exports": "./lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.12.17_1613661142004_0.8397554078087612", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/helper-validator-option", "version": "7.14.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6e72a1fff18d5dfcb878e1e62f1a021c4b72d5a3", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.14.5.tgz", "fileCount": 6, "integrity": "sha512-OX8D5eeX4XwcroVW45NMvoYaIuFI+GQpA2a8Gi+X/U/cDUIRsV37qQfF905F0htTRCREQIB4KqPeaveRJUl3Ow==", "signatures": [{"sig": "MEUCIHmRjmhV18Ag/d8XgbJ1YDgqWSYlexOogMZ27UpMjjohAiEAmEzcrCy9Jr1BuGKexzBUK8YiL7yIEV4FNvU/uTfBFnA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4528, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUqXCRA9TVsSAnZWagAALToP/AxEH+RbN4pQYphACnMP\npYKkG3jm59dVZss5uf83cahU4lEvEXiW2ALxGbGWNvO2VEHrl/UbB2BQsSP5\naIRBbQhKC0dXKe2K7Zamy7KDuVqhns56OiIistrKZaGVN2cw3heNlsDUx0yd\n+pOAlkbU1jgX3SjIVCeAQ4pEQ708TfSH8/YD4VZXXlZqOJXyyYpv32CgM27/\nzScns2gPfB5R0WMeuqjJ3HzG6uVDebaAipMc3QccX/Qk+s4XpOfie3H0QVho\nr5Y4rwdiPAtgQzO9Ob8dPHK3ZEOq1DdQ9FpGMFMDtLHDJBeeXaSwCV7Veeaf\nRV7JGJxqUijz0Cm20NAWbxJzo6woVEESCYKiHpEPGnWSL0fdd2d3BzlzIX3u\naCCef0peCMSiLaRImxwaJE3zavIRbrQtak9uZpExE/GhTQ0YiF3peo04Wu2z\n9JQgErGErI5XKqyaMDeZYu+gnVDcwCk+vSpdnaYSkCr65UBbvt8lD0bVLiI+\nDHZbWd/PmvBmuguzFIL2L+4WBALDPTWL2Xs/d+VTIzhwVDsUabul7lZ85MKV\nwhL2ibzc0YkYykvF572ycDtXpIvFFxRkM/NunEhuSRH39jULPnswu+/dTYu0\n1CAlEHVOrEgZCo7Zx7YTpyaDc/OGiOygQILGO1uudaHKtOWwNeYybwVu+fAD\nWGPb\r\n=UnGT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": "./lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.14.5_1623280279696_0.3098651585220653", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/helper-validator-option", "version": "7.16.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "b203ce62ce5fe153899b617c08957de860de4d23", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.16.7.tgz", "fileCount": 6, "integrity": "sha512-TRtenOuRUVo9oIQGPC5G9DgK4743cdxvtOw0weQNpZXaS16SCBi5MNjZF8vba3ETURjZpTbVn7Vvcf2eAwFozQ==", "signatures": [{"sig": "MEYCIQD5FDUjiszOD12IdwRsTKTKeYlr1JDt4wTsXAlDJ5ZUbwIhAKiBulfxeCJJVOl7LJQcr3z1V8NYc84G7BBBCk/jPNIF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0ACRA9TVsSAnZWagAAVHwP/AnQ4Enhpu363RRYqPmw\n84oo07LLwj0xfaeEPKJo0ZZEPrEx+pNjqkffzoMu+R5yCXT4//maNtkHO+OL\neOfJEJmFGtQ0SEBbyiifEK9ZrTMKfalgg5v+LoiBTUZ47t3qrQeAg4U8x9ME\nBSv9P9liF0M5MpZwb0CcxFqG2zingquCQoYZM0szqShK1YJjiDyE2HTo5JFg\nOKGKFPN9tDMlz5ZyqyRSAdpDH+cShMZryr/6JsGggtpJVPdugzcGo2ecSA5n\nb6SZBDZ31ktNqM+/TiT7CZGEQdXBszFRZjRwSR6A3gHqLK263udEJ6djdir1\nc0tQB4YOUZEoi7ufltowZJ4zBCDqfoSkPLmqpuDmqAWk86aUB0ByPaaw5jvn\nri7W0fqNuzdtrzb3GZQQCbbN9wFWyvV/5ny5BAgwNOFq6qmtLoUHpVUElRKr\nsfxYrO3E9IPutTr/xDD0X4pclyA54RFf8gAOqtT+7cLW09oiZS7zyCbtiXap\nRwkSFZX4gqkWkk7v5/ODlw7pFpKyuqx09w0Ed9AX8iYjwvqFgjUTBlPgfgR6\nJ8oNl1BpLqiNX0RAgeZ3Evqh4KvVoOTZiHndjR/ZmX6Zeek9U8SFmYB86yW+\nEp+FTOIFT5Q5TCqE6EhuMhWJd4UXqeWQCElWs46NbLQFDCQpWlp6fbwutn3f\nIKPs\r\n=DS2+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.16.7_1640910079899_0.060843454454916124", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/helper-validator-option", "version": "7.18.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "bf0d2b5a509b1f336099e4ff36e1a63aa5db4db8", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.18.6.tgz", "fileCount": 6, "integrity": "sha512-XO7gESt5ouv/LRJdrVjkShckw6STTaB7l9BrpBaAHDeF5YZT+01PCwmR0SJHnkW6i8OwW/EVWRShfi4j2x+KQw==", "signatures": [{"sig": "MEUCIQDefE//ozIumGIlaeKIddw5E9wmG0y4adFFmSFH/gG9UAIgUcDmE8giYwQULmnDl9pV8SqNT+E6xS/zCUA2dqZl6vc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4595, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmruLQ/+OSm5l4M5xSR4qvr9uLYF3wieB/L36XqiaawOkP1Zr75K8aF9\r\nN8vkRfvo431j9zJz+ScOWCM/HITT8imbeuEzcw7PoDbyk8bCqi2y5bo6ARRy\r\nE+lUp6Gn8wu/5qnLh1LWZTcTbGn5GIjD7i6g9rrwSPpz2iF96BWFSRNFyy/T\r\nhpwteSyzsGCn2s+vKj9paHvCkKRXY1L+0VVKKI4WAPj+8x+FekmCnQZONhy6\r\nZmtvj2G1I5mlnsHMkwkTiAcKL6V7tSh5IVD0G+XHM8epWRWBrsuVzcuxoc8P\r\nmLNnx8xYjC2Rbffrvu7Y26Tr6rG1Ybo15CU9G5FktLacMrMP1IP12m6nG2mC\r\nNGwZeUxN0q2F6wVW3au2Jq/a+awBuUBStm1ddPhOO/GY5CIEgSUlHCm+rENZ\r\nUERU8AUoYIEKsgHjpYvzbeWp+j/2ZiMAiMxzgGEo1q+HTWQxZZKTW55c+bpV\r\nUIfU3aSQlFa9GQXFzQhDM1UnwRYtMPzuUYqI8GQicWNOjtcuczgJO56dA7xi\r\nCTlf2vN/hioCFinU/jOQfeIpwtxF9yN1eKf20+uLUrG80XBbLqsbztivW4Go\r\n+EANOdly8P+TFqgFaRy+nDwUAmA/sIwvUZSVnvfOx5pZF5aj+gu+Odmc2Ek9\r\nA0iXkj8YWRArpJSKGUmEAkQ1ILyzdB51cSM=\r\n=kP+R\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.18.6_1656359385134_0.34619892373215233", "host": "s3://npm-registry-packages"}}, "7.21.0": {"name": "@babel/helper-validator-option", "version": "7.21.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@7.21.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "8224c7e13ace4bafdc4004da2cf064ef42673180", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.21.0.tgz", "fileCount": 9, "integrity": "sha512-rmL/B8/f0mKS2baE9ZpyTcTavvEuWhTTW8amjzXNvYG4AwBsqTLikfXsEofsJEfKHf+HQVQbFOHy6o+4cnC/fQ==", "signatures": [{"sig": "MEUCIHhAlDR4tptb6lv7tw9DusdYSz54z665g7KGpd8qTzPlAiEA8buds9rkO29QwZuyxsEmnnu/CvgEPcaY4UyixHyRGCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11444, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj85IuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWDA//YZyjCDPMgBVUIv+U7ikhe0K/ugVVfQ8y4IC7Q1TTcpa3JPae\r\nzPZWXZ63aKjd8kO76oNoDcx7uqj7Hr82ZiEDcpOXFpj+eAPnCkuc9vU93Blc\r\nkBOrN2UBLFxOlNt/G4qjBSil6B/IuY5GNnFxUMtPoVRhbIoa02YoySjdjNzu\r\nRfCTA74+pHcdXXmmoZGdpxb3KxejqIV0FKvhW5SQzdkZM5OXTI+2upnhahVU\r\n0wajuLRyDinfp/9u7nuWXwxzzisjGkUpddKGbu6w6G+jYuZI+RHxh5rCZUEX\r\nuSMt/K7oJ8lmWq2GJHg+QtvCXepZpbxMyQeaAk/aeGPRxEXrxB0aq3t5ajjq\r\nRlwCjG5+HCOn8dICpZoDsGb+3lGlWnSm9rv42nGtK1EBEjCFgzYWaA2qrqEH\r\noGxv5rm4DAJ2uJsyG61Gz7By3qoLlKbqPx/1fUTa91vGf4iluGhw+OunSSz2\r\nXUDs0FKW8U2Y6oTp2ZSq48da8BeLYqYa76a3N2bBIM5ht/4tDULGBnyla8ZA\r\nHlHMqyhnUTpUYXvkBByMlCGpnaK85ykcNZYgzPMBdb7PqAir7JTE7x3Q31l9\r\nt/FluoaGvPx43DYhnz3vPQoA0KwSOGZA03ScBLedqPp6KuPaQXO83LctmyoD\r\n4FTy5Mr7aT5LhVDbqppCnie5HJJOaDKwAhY=\r\n=bNRV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.21.0_1676907054156_0.22800137202459014", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/helper-validator-option", "version": "7.21.4-esm", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "24a17e53b12d3cc66853e47343d06ba4ae4d1487", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.21.4-esm.tgz", "fileCount": 10, "integrity": "sha512-9/eDaDbhGCfqg8QCW+FujS+5PthZu6eUf6PJJpJgt0e2FLhjKjy/lStXofozfPZsINhkaSRwBx8Ztq+LRqqHhQ==", "signatures": [{"sig": "MEUCIQDJ8DUSQrWgduNtBmp64lITtzHRaSkhi9LBeBtXMrJYAQIgfxQrvcUK5gkNE8Bz27V3mmICDfayzsZ/WhB8NGTUKxs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC9+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqIag//T2wSFtuFHcG8+JhE0iWtBHD+uq2WSxU2uLkg8IX6cyGz5PL4\r\nNT0YFuDJy7Es7UX2E4YldclUqfh+tiTcE4Ts8l29p1zd1WrZ8aNqansiZSdO\r\nFv4DMZBLS4jusWq54uwC10ZAzd7Olm+/VzrcGHeQ+94GnagJpfI8L4Yzj+Hf\r\n4S8tB2w7UWzLhrEann+mEUhQHcxCqleKgk2Tgey8Fx89jzZSxhNQ8X4V2rXm\r\nP1GGnWXhPwUsmCxl1uUEp00dWgtQLIthW+vr1Vo3PTUV18NDgQxzXNBT90JV\r\nxk2PRrGBJ0R1Z620ZaT0CChTDnm5fYTnnI/I2Z9hlWLSGrd38zBSjH9Od/u9\r\n5M5dKQcfLo3qez0LR7b9uN+oA10S2CwMP5/YL6YMqKZ0s71jRgEheaR+ep4c\r\nmg8ZyqEpLY6+JE9Gk0YlwOWQZBE7XNEadMjL0MupBAGZxmTeGQ56j7kNpWdc\r\nWy8g8Q7rB0sYaYB30H+EPoJ+pUa+LdNJn6HMQTuBHKQdRDx08zhY456OA6Vj\r\nWN2lhlVVkasHWKU7Gcay7WYlMTij9JeZbR38Bk0T2lzMJRj/cXyG0ao7nqMr\r\nIwik1VjKp+Ooc9XUGMJgCMn3DG6OeY4ZNrRYytWjugYy6XSf5KvphpwUbemj\r\n+wkSB6/4eTOrWbCRDTDOJTuQejqFSarZWqY=\r\n=2Bur\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.21.4-esm_1680617341987_0.4926551554542071", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/helper-validator-option", "version": "7.21.4-esm.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "db7ab06f2efe92d282aa6104a28740d8b04ebc46", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.21.4-esm.1.tgz", "fileCount": 10, "integrity": "sha512-W7kE6yVpGxVVsws5ewPz6zntV6UVrz5wx6Czkt6SXD3+eLokDVk4bt6CotYY8ogCfZ7z+zCAFuPqZi8K3Ay5Iw==", "signatures": [{"sig": "MEQCIDxQ+iqm6mqBNtRrGN+d1y56HT0aNbeEzgeM1tc/Jp8mAiBW3yOIcLPWSGK2j4vmjzodtG99VmCfBVMFqs+iCkJ60Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo6lg//RTEMI70V1fb4+fmsLHoEB77g7E62XV97uV4CI0wpfIMZ+Uko\r\nf5U7S0N6bMosYcGeO1P2Bl2eMf+xfDnUdtgBI3dMltYQvmZMbHNOHDfsTVC0\r\nmlHYSg9ioufquamRxYaVFMPEqDtQOfOS6WDK6ooxrQCqDamabE2T8Zx7lp2Q\r\ng0l0AM9t1CS6tlPIDcujXgxE7M4u8dtn8DqOl11DE41WcSbowyP4ASk8VROS\r\nMX1zUj6yAXenrhviARgoFoWrU/kRBQDKv8ZScbQbsYXEcLaPVgDVxuXOG2VT\r\neY5Cpnvcd8yiALnsTK8JoYqiRAy91KsYgD3Vyyu6SrqddjNQP3U8E6Oh3M5z\r\nmtYZ55JlghVy9DSa5hvsEfWJoG5ED/2gKjqfrwGMJKAB/h/Yw4uOdnB4EJRE\r\niFoUO9nwk9zDUy3B1qBL5rbIoju7pqxtsvBVNFmDpmNDlwpAbJd2OxzaNtxi\r\nSOutBC8ePiaM37sZsotK2rlhg5yXNJqspI2LfSJUNnU4cCu8W0ea3SSBvVgx\r\nY/Kbpo/30S3qLueD0+RQzxNZBrVOWyLq7C1jhKrjbjrhfKc/U0AAB89bQ/b2\r\nYFHSvQpnI0cDoGqLrN7XBdukHe7TwVRMoV9l3RrhKM8O5hdg7kENQRia5mKl\r\nMVi+bIL150QhZvyIbexLEPiiQuLXBn85vJ4=\r\n=xPpr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.21.4-esm.1_1680618053440_0.419701059565323", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/helper-validator-option", "version": "7.21.4-esm.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "21d9131d1f7e60b74f5849fd5cebff9dc6d0cd4d", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.21.4-esm.2.tgz", "fileCount": 9, "integrity": "sha512-X2MBKvoZOZe76KWqKEjUQmDMhboHWSBLyfdcgg+0TN5x6BnGwYl8m+XZd6iAwOgNfQ/YMuzJmvwJJvQTL9YIfg==", "signatures": [{"sig": "MEUCIDDXS0Ku9VC17yjeQv3uoGPHi0Tpwl7GKco3EPYgt1NUAiEAlIefaJbQrgymdigve8WnM1ilHtTZI47WDUplUWvmuW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10897, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDZrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoIDw//UQLATWNf5/dEDf3+hhnWy0fQW7ZLFluFcUe7pTDdcYsfwC1J\r\nWWrSI80RO6RInVG4rwXUDt8dD6P7g5+tumOM2tUVvHXMm4ba+TU1o/1+7vJe\r\nQdyUD98A1iCyGs9FEZdbriUgzrRiVBcYL8qigGbD/oBgpxo3Ce9a6cMVbqLx\r\nW6JJc6HbSUoz26AVlyZeVzXiWNpkeH9tatS2MABccbRX3gbIHOQX2sIQw/qN\r\npn5642h05gW3p46SHbGqqPbnxMIBeLBhZfX7yLqX8tF7Bua4nkWMu4T0fZh6\r\nRXr+jZjknacoB8gDOGN4SKml0MqbAQnZulLMMZ84Zg4/Lq/nwMAFP9lW89OL\r\nDfRYoXZkFIJUm8WIexgTeXlbhN37Pbr9O0QDGbIs+uxRG03fkHfv3yw0Wxo2\r\nWEptKoujsGWlJG3QVxh3swd4eYS1h8yj6zUr5iVavD2CcmD+QKjwEqpAsgBi\r\n0kfkCXwWm9L3lCUwq9nS+rWxrKpvDRxmwFezbCN0eIyJhsMJU3dha3OCkf4L\r\nL4ntd/CP/LuIs4K00r4g8GEsrkNyeCYTdf7IsnNYNx+APm2Nv9s/9gr/M/Fn\r\nRFh45ArdYMuTEd0DG4vh7ee/+w2rxv5YZqvrT+y2kO450/iBH84fUqg9XoFd\r\n4oXxH0ePlazPMoF0a9ho0N2Hbq65PHNJkRA=\r\n=d+Sr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.21.4-esm.2_1680619115382_0.23783931188667595", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/helper-validator-option", "version": "7.21.4-esm.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "12e4605d38187494cc2256dee192e8a540c80812", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.21.4-esm.3.tgz", "fileCount": 9, "integrity": "sha512-pRVDfVa/bo5KNkvnnIgVBO5TLCUaxlNR43wQJ6YStbMrw7mDyF3Hu572cQAVH9k4OAMo3TyGjNjbGtxqHQlMwg==", "signatures": [{"sig": "MEQCIDgurt/b0K6O2czr03ZFNv4fVYzuPr86otyXWymUzKSfAiA458c6PA3vuU62+3pDpy8/P7VnxgM36AgGbIwFEvf0Xg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDp0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqurg/8CpwtLb9YV9M4680YGWHUR2C4nVKfjkeRdPNqptcBF2fAxR6a\r\nP/BFkCft8QHdpOVVPDFPNrq4j+4Y9C+g9WQFVFQJVo0sGGqeEP+vTZDDs4Gh\r\nud/dmW9ViG639+lxc2V6EdY8YYrayi8t44jExIE9jyX+nc1JPEXP1tABWdCW\r\npQghM5MgmRsRqXpk80TwOvjo8KhS4yqdDyHn8mbjTGTOm4iLO/N9KKEd3ZTQ\r\n2+26wfunwxH+O8kt8LX1KFkTQ0wc+snaWLRDuNSyP4pp5j4xwGVn03mAwkLP\r\nyrFOLwGLZdDLeSZRixzonufqH06g4U6LTYR9wVdaGDSzMcN4BYXURgrYw/S5\r\n15u/3Gksy/5p5z15Za69LA8tFgQouxU7daqytG/1MZiikHsWuDBaUWtUjirr\r\nZHmukGXNtVRcaiC9eYLZfGaCIN4NjnWXgPTaAhkX6maVGUWWJijVp0zsY10R\r\nUKCuAa/0rXQKrtUo+i+6/ikvpG498H0ctQH/YNDh98YnGUETKBzr/XwS1u4u\r\nRCC3juBnKThFgRe0bQOWiWjIRa4g6LWh5aqAV9ifjQ61QikLVCUIWX71HAMp\r\nuO2alVY+/F3djuam7NYTVMI4cbPOL12ttBzYeGBZuMbubgfj4bYLz8AJF+dg\r\nruXzTpvOMs2Hn+4OhSjPXY20qyQxzI1w9Hc=\r\n=WFiY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.21.4-esm.3_1680620148181_0.43520900201492574", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/helper-validator-option", "version": "7.21.4-esm.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "5094e3818aa4429e0da6428abf0fed02dc9b125f", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.21.4-esm.4.tgz", "fileCount": 10, "integrity": "sha512-dsPtf5S85fq0zJRIWXupOE1bqDKMq/5H3GbcI1kE55nrSW6U+pkeXmT8rFv3LHmjMjhe1RD38UaW2sqDsdHGZg==", "signatures": [{"sig": "MEUCIQCnmjkxtVxr9hpCng9Q2TVfe3OVuhg3oEzn80Z5lE3m4wIgdx4fQVHThsSGG1cMhF4bHd6uvKDRXraHTkMZFuJAfU0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD5+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmohQhAAh7sz0AOeYcr58o9FB9aSXaxCopKWWhd4bcK8LtzRWD7SoBEB\r\nTGztqeRL74JXal+EadvBNKgBdAQzs700cm1wwBA7+g5ezy+16XSEw5QR9Er+\r\n8eIq2fiSYbhHNI7OK0HI8/UPAskgt6S+DEO53ND/ADEMyW/oL6Wz+R+wyqXE\r\nCNI78BzHHnUCOLbx2KdEBszGFN0XZ7DpRtigAYblyT0+Gy/gq49H08CkaVIz\r\n+Gmzs6x5ma56wnCELn2QcDvpuJEcJGu7c9xt/qEAC4YuuNzJmYxciba5SReX\r\nd5L/wD+n81vclN5IK87bZVl/fJa5EsSSQlBdB0cPWfcHNlLjQPYNaZ93kqDi\r\n+nsOE6IqJelIF1r7R21WddSHHMHJChknAXChbC+aenVsGCgS7MbqzIHhEwRK\r\nz6nRSmyhGAdXXxhiPxxkkWmXK6Ib7/AoSszdTcs7poNpTXrogIMja/LyyZIT\r\no3djQkP1HFD/BmG2U021w9RE/a1Nzz1XKZTIVKvsXFf2sCXUpnFcMuxOq4Et\r\n9NMH3RJ/Hbh8ZLAH3XH3TmS1RsS4pNd42RJ54f0hwEZqi7epUvA/jv3BnQej\r\nJQRmhySCbdHAy12RBY8iii9OKc23g/LaGsZH/r/rwZH7YhSuJwxcT3dI7koC\r\nYIw0IsZNZIj18+vMyvYGngWHZGxIcAX3ctc=\r\n=tIf5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.21.4-esm.4_1680621182434_0.6285578562105565", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/helper-validator-option", "version": "7.22.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "de52000a15a177413c8234fa3a8af4ee8102d0ac", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.22.5.tgz", "fileCount": 9, "integrity": "sha512-R3oB6xlIVKUnxNUxbmgq7pKjxpru24zlimpE8WK47fACIlM0II/Hm1RS8IaOI7NgCr6LNS+jl5l75m20npAziw==", "signatures": [{"sig": "MEUCICS8rj5BZLEUzlpk+2IW7Nt7wPdxUjHidL+M231gP4tVAiEAtLXyIi/4iIPU7K6Tv/BH6v4xJjB+rWWjI7azUnxgIdA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11621}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.22.5_1686248462245_0.7947985807639784", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "ea9e38129b52ad69ea6c3cb444206017467da1d3", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.0.tgz", "fileCount": 9, "integrity": "sha512-Mo<PERSON>tKypiB0SYbdndBIXlfwvJnxSX3VZQK3OJvS7G98KnEMM8jzFOzq0EroJ4hezocVGg3HKuoZm7nMaMq5xtkw==", "signatures": [{"sig": "MEUCIBOeLKDqGoNlygqHzr0mpcpjz/HjX7atZq0D9obt92BuAiEAjRPMmchtiPXTiYRSeSWEbpetWjEbovyw8sQ3b3k3gE8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19151}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_8.0.0-alpha.0_1689861575036_0.9143823781620128", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "3b6387be63e248fee77bffdaae3e81188023d5a7", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.1.tgz", "fileCount": 9, "integrity": "sha512-E5<PERSON>kcIWQE5CnSCow8pNw21OOh0ow5kJPGAAFoEjSgtCZmSh2GPwlkMVbqbt3bDctrAQLZTiIjQrKua1bLlUnTw==", "signatures": [{"sig": "MEUCIQCMXGi7EA+4zOmaAqmFrLdz55oIpB3Hy6ju2pk7NsGJkQIgcoatQ/mr+db+fCDvfZMcFmitRBfE9lnr99DvGewFeBU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19151}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_8.0.0-alpha.1_1690221055807_0.9125753344617533", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "2fc8ca5eb91b1729390ce71e776d5c533f33201f", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.2.tgz", "fileCount": 9, "integrity": "sha512-Hy4K/dCtwnRNssFDySSR8hQgcCVGdWJ38ECXu2lSbIo+omHKhqynD8tpOfvRxPe0A90FAeq/jAVrOzilUtw/9w==", "signatures": [{"sig": "MEUCIQC0NqIN5NHGl6SBaaaAj8RJ5bGLp2JKC0DGcobSk/j1lgIgEp3nZBelYNjHJZr0tdnYiCx60GOPW7x/uo/+9W/Wub8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19151}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_8.0.0-alpha.2_1691594072225_0.040633448423662566", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/helper-validator-option", "version": "7.22.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "694c30dfa1d09a6534cdfcafbe56789d36aba040", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.22.15.tgz", "fileCount": 9, "integrity": "sha512-bMn7RmyFjY/mdECUbgn9eoSY4vqvacUnS9i9vGAGttgFWesO6B4CYWA7XlpbWgBt71iv/hfbPlynohStqnu5hA==", "signatures": [{"sig": "MEUCIDFiCATG0okeKwsF4qz6LarhDzRVHE3druSXn11Q694UAiEApD5y/wkmIXHURmILbjFoxt+NX1/rWSWNyRivq5fcwDs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11637}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.22.15_1693830303205_0.4955042253922126", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "65dd6f5c80448548a1290ba8f412423faaaf5ca6", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-VVh9QCblSPzAmKJL7i38CCuFZJsUOjzAKNS5187cO7/4HSIOhuNqIzWfuDlZVJwe07M/X8VG984q9RHah6aWAQ==", "signatures": [{"sig": "MEYCIQCjx19IlS7A3b+7HZ1pcuwKCbm8nNs3zp/RL6wVDfh2gwIhAJb2AkarJC9HYqr3GBeU/w3R0nWFCzQc88n6HQC51TXu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10715}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_8.0.0-alpha.3_1695740185493_0.3682568523496823", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6c7c81999cab944b1cda8997112f66fb24504017", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-wYTWCCERu+CsHoF6hERujZx4Lvzsn9SlXaY0vs37Um78RxKkAWVuDbD7ZVGn8fS8UcTByILX09jm/HnoPUkdmA==", "signatures": [{"sig": "MEUCICUp162Pq62RD2O8vXycrNvGbKE/J0yeLOezvv6h6go8AiEAnC3eZ0aRBS/3k07lEWr1TovIhndUP+U9Nvvj/iqyDz8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10715}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_8.0.0-alpha.4_1697076353408_0.031753636580835076", "host": "s3://npm-registry-packages"}}, "7.23.5": {"name": "@babel/helper-validator-option", "version": "7.23.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@7.23.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "907a3fbd4523426285365d1206c423c4c5520307", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.23.5.tgz", "fileCount": 9, "integrity": "sha512-85ttAOMLsr53VgXkTbkx8oA6YTfT4q7/HzXSLEYmjcSTJPMPQtvq1BD79Byep5xMUYbGRzEpDsjUf3dyp54IKw==", "signatures": [{"sig": "MEYCIQCnkSW7ZrABPzrG0Esux3VI7CVPxTF90qz4804TJkaTbAIhANN73QdJ3j8NXCNKX/H2gq8gjZMe6ybPE3u0SOb07Vkl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11667}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.23.5_1701253538741_0.7198352630403453", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6455cc901fbcb1a4fc89a5765e33b842857a379f", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-KGmJG37KmlK5SHVQ4P1NHwEWM27PU2xYQ2mVY+4EJ2gkcdSADSbIC7c3cPsxyeUvTM6hXYwlk5xEv6peDpiddg==", "signatures": [{"sig": "MEYCIQCPpexcDR/kluV85zRG6wxOD6OwUWqIm1PJqCdIywJwvAIhAKQDB0cmZ96hVDrO3QVFFFrVe1L2I+qWzKk66whBWVLq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10746}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_8.0.0-alpha.5_1702307896136_0.1102311097560369", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "de6c868fb5fd7c5e914dd944a04581c6efa7d468", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-jHHD6msYmhaMHHLGLbUVyy9UPyAOSvjBTF3qXuBnTg1ZRyuh5z+srbpfagh1tv8igfq15h+PPexA0kGqOnzi8Q==", "signatures": [{"sig": "MEUCIG3Ff+N3nnqdJcu5ptHbEQKrWaxqcowp7a3w9fYMmOKbAiEAnJed980EZfw9m9TWgJkRyrLzIYIEDywLXNPGjk3QpOg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10746}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_8.0.0-alpha.6_1706285621737_0.9249739011169058", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "2c06780521a18e7fb1be31fb570e5065cd0369b2", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-yimhDdcX3wtG1ea3pNxYJrrS00jQJTfe5Cxeu9szJlcnGhtgqw1iJqz+gVUa/aWzaaUIXibXQnYJzEWHpEvQ2g==", "signatures": [{"sig": "MEUCIQDWqk6R1YuLIQovRUdJLTVL8kIHiPXw389YsmpBnakrngIgBlVf4uIeBYdNdZ4lUWZssaHanzLHd+A8f5MkqHkDMB0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10746}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_8.0.0-alpha.7_1709129047038_0.7107207480290392", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "8fea1c6b102be95b0f0979d838f8aa592ffd38f2", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-AFwOpQwBzkiXk9vCoI9aAQu4CvydxgbJ2odZokWGBpFjbpBuLLS/iZOWKTJEmKg/Pz4UFVVEXjNiNXUJQ1fX8Q==", "signatures": [{"sig": "MEUCIQDVNcA1xxPSwcQhJy2XnLxcXH56l/Rb59L0sHltto8obwIgBNw+kY4I0U2S/ypUDnBSLvXXG53/trW4UYgY5V5Lpfs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10746}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_8.0.0-alpha.8_1712236764348_0.04270564907493157", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/helper-validator-option", "version": "7.24.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "59d8e81c40b7d9109ab7e74457393442177f460a", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.24.6.tgz", "fileCount": 9, "integrity": "sha512-Jktc8KkF3zIkePb48QO+IapbXlSapOW9S+ogZZkcO6bABgYAxtZcjZ/O005111YLf+j4M84uEgwYoidDkXbCkQ==", "signatures": [{"sig": "MEQCICnMsqfPDheSWRiNAUoDiwuuWkIVyLdLlR8rYg1hA/yKAiA6myVOw09DX4t5lh/YiDOSoAIeMnHlvpSKgJ1QqNNMoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11775}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.24.6_1716553449291_0.4040615925712441", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "01bc58ddba42473660f6ee3f5262cc5be6b81920", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.9.tgz", "fileCount": 6, "integrity": "sha512-sIhiBJ3c1m+EUHMCpFda8MlD0KqiBcqXQkwCrkdjvQTLulVIEueCLzSk6n2xs4IHqwASPEWKu/5irsbZqsy9UQ==", "signatures": [{"sig": "MEQCIGsBkCQDo1prmlmaW/JffzrzUnIKs7zzaaBtfCryb3trAiBAdoYXAH8eTrJunEPuwmwAXgteKhlilX4XpnIt+XruzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12304}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_8.0.0-alpha.9_1717423425714_0.43738375222314496", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "deef0d4736d6985d6eb5c224e330fc4581e82f32", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.10.tgz", "fileCount": 6, "integrity": "sha512-FpZ/2O7zovTj92sP9/xBAH702lodvrnZS9JHBVJrYL/EPRl+arrTQNYfC8BthNSBZtZIf+3bvyubIJdWWNIrZw==", "signatures": [{"sig": "MEUCIQDNKpbtwx40UNpFMCmNtC8mtGHjw7VYVlXWv6FL0a9EIwIgJUgVwa87qIamUa2RTHNfzhmU0wD64I5iBKkwLTklkK8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12305}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_8.0.0-alpha.10_1717499979136_0.9800003312971777", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/helper-validator-option", "version": "7.24.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "24c3bb77c7a425d1742eec8fb433b5a1b38e62f6", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.24.7.tgz", "fileCount": 9, "integrity": "sha512-yy1/KvjhV/ZCL+SM7hBrvnZJ3ZuT9OuZgIJAGpPEToANvc3iM6iDvBnRjtElWibHU6n8/LPR/EjX9EtIEYO3pw==", "signatures": [{"sig": "MEUCIAFKu8+JxxV2HHyJ75m4QAyzEPBclaredGvvsp+JYuGIAiEA5c13Fy2/TQQof6pfSuCzRBBDt4XMF4ot34GyNj2Rr3c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11775}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.24.7_1717593295574_0.8773665569874407", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.11", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6359db8c8e6d1624a3142cd8cdeb89a5efbd1cac", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.11.tgz", "fileCount": 6, "integrity": "sha512-Wmd/J81AWA5ZP93jU4JL1hYK16kg1f9atdWKYycEWh+aZ+1p9HLm17sjiaTwQ+wp+bG9+ftXEuuQWa0XmQQcxA==", "signatures": [{"sig": "MEUCIQCzf8Mv1GMnmOHuL7e7d3oO1lFarA4ZyiUgvHmP2a56lgIge3MtoT+5xleHxzVoVtV5Ee3K0te05UdcsObOrKlQsqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12305}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_8.0.0-alpha.11_1717751710656_0.4924720561014255", "host": "s3://npm-registry-packages"}}, "7.24.8": {"name": "@babel/helper-validator-option", "version": "7.24.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@7.24.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "3725cdeea8b480e86d34df15304806a06975e33d", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.24.8.tgz", "fileCount": 9, "integrity": "sha512-xb8t9tD1MHLungh/AIoWYN+gVHaB9kwlu8gffXGSt3FFEIT7RjS+xWbc2vUD1UTZdIpKj/ab3rdqJ7ufngyi2Q==", "signatures": [{"sig": "MEYCIQCPOFo/yfVZ5g6EEsfuKSlycbv3zZiOpfSDgAa0fbUa5AIhAN3Wm6IrbXblEyE0ccisdOcnhSMCOWUITMH4T+VsYBkV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11775}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.24.8_1720709682829_0.3778232233419805", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "f06cf04bf810bdd49930a9c08406164578ca5632", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.12.tgz", "fileCount": 6, "integrity": "sha512-4Tn2waWYgkuyM5t2QP6Qz9tL54HCWGrtwW9cwpuMtk5VG0yu6vj9PVDyLT1FsQqEtc5jjI+fC84as0E36JqhOg==", "signatures": [{"sig": "MEUCIQDnFC5Z6/jzllzGHp8huIyzDIwckyPKyJ6De/8YE/YCaAIga/04er/SpGwlwE5tOmBYCcXDS6NkmKUKPxGba9BzEQU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12305}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_8.0.0-alpha.12_1722015186166_0.9725822422296617", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/helper-validator-option", "version": "7.25.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "97d1d684448228b30b506d90cace495d6f492729", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.25.7.tgz", "fileCount": 11, "integrity": "sha512-ytbPLsm+GjArDYXJ8Ydr1c/KJuutjF2besPNbIZnZ6MKUxi/uTA22t2ymmA4WFjZFpjiAMO0xuuJPqK2nvDVfQ==", "signatures": [{"sig": "MEQCICUFO/oKvTMd6gJYdKSoF9mcJAgTDFi28fTfKCTeqYJ+AiBlEuRMksV1DcJChPyrwG/dy1qrMe8JvuQgMcrqyHw60Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54681}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.25.7_1727882064096_0.7050201856293006", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/helper-validator-option", "version": "7.25.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "86e45bd8a49ab7e03f276577f96179653d41da72", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.25.9.tgz", "fileCount": 9, "integrity": "sha512-e/zv1co8pp55dNdEcCynfj9X7nyUKUXoUEwfXqaZt0omVOmDe9oOTdKStH4GmAw6zxMFs50ZayuMfHDKlO7Tfw==", "signatures": [{"sig": "MEQCIF9kMznYhGzNuxgNYXWhks9OLc/9b4F3eHMoVhiKv822AiBKjX7E3KBUF9fFfdyJnHcFXJu++MUYnJ8/qPU2a5dHMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11775}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.25.9_1729610439263_0.2767879583019994", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.13", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "73661256805d19b3808206eb76dbefedcdac2ce4", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-qjrC/A2ee/XFnq+h+3mTyKuTzgGY3yPNPZoZWJ+zu0r9qz7UgJgDmJsRkz/voo8wRp/CoSRulX5fkP6puzD2ew==", "signatures": [{"sig": "MEUCICt4NFwZwyPmIPX6okejFWpoEQcY9EzAISTFSgGkvvCGAiEA/EXwVzAxPhszB/e7BiPC4oRA2s2uhyPa8Nke6nIF0uk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12305}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_8.0.0-alpha.13_1729864423422_0.4981112689388758", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.14", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "239725f75b598e6abd223ed982a736e168865371", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-15I9Xplev7++FYC5wC1Hp7MlwzhhAusdclqPqQHvGZ1/KETbL0TrKI989NoANzbvfmTsZi20z3vl2F4vYCWcuQ==", "signatures": [{"sig": "MEUCIQCq6ss8eoF84PJvKh/sQKxv9PuYVtuwhOlJmYAmNhguZwIgUReWs3USNPzlqG7pL1lvh4VpA0kg8TgtuhxQ9OxwHBs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12305}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_8.0.0-alpha.14_1733504014959_0.044351054287718794", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.15": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "14cce959f2b58ac87c6a1cfe8c7290ca51888641", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-S+G+1QY7zQb0Alr4socYr6nR0uKZsZRKphAuiq1eVS/dVRv4Mc35Femsa1RfMZfb4I03dEzPA0tOaF3Oxpc5vQ==", "signatures": [{"sig": "MEYCIQDvCSQ838Qz1lWyeQoDa0EHCU6B0HjaCdGCoHauvS2U+QIhANwKoJPnj7DNEZ8ZEVXZofFFMNx3KPD0PbmW2LbCQNlK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12305}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_8.0.0-alpha.15_1736529838852_0.6179853342352306", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.16", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "435aa662884ebe76bceeb9160d5c46d8a418b1ad", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-iicLQ529SexR0UnW8ZL8RK80aD30WGnc/DRg2IrXj4m0qJx0Kyi2Ifx+Mly0xtBUzsWX8z1YSQnZ/sKmVIZo8Q==", "signatures": [{"sig": "MEUCIGXANiJ+ngHw0ANSgKfsG8SFj+ii4VFLC/Mv67L6ie8cAiEAwV48JXlS3mfnVO7em4Nx11LNKtQVGSincisJ50RNNNU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12305}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_8.0.0-alpha.16_1739534316783_0.41560775009710804", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.17", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "6404bbb1482280d78243c3f71fd2b8790fe01343", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-VqaLxWOsdD/XA3MwZPPmqCDkMRJo8ckA8svQIIJ9At49gGXQXbQ1O6vAYhQT/32c+nYie9KuZ4I/+gH2cyTMew==", "signatures": [{"sig": "MEUCIQCXqUjUNHybS75EnMKsqzmykWXG3fdt74oxaDVp8Q5i5wIgHjVu1albGigbnK3Dg/C7CcQU/TsonMBgCZ7RKoZBdcE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12305}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_8.0.0-alpha.17_1741717466637_0.08445923919888987", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/helper-validator-option", "version": "7.27.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "fa52f5b1e7db1ab049445b421c4471303897702f", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "fileCount": 9, "integrity": "sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==", "signatures": [{"sig": "MEUCIQDOWF2w0fibyKxW6i4fhkrN25B7gyO6GSTK8+OBRNskTwIgJYvokDKH+OQj2TosdArObqpEfVHUF57HwFTxE6l0EKs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11775}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_7.27.1_1746025707618_0.8003633949488422", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/helper-validator-option", "version": "8.0.0-beta.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/helper-validator-option@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "dist": {"shasum": "72ff1ef1bc72b0f6d52a15a8ea797072137349c9", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-8fTqV1kueXks7gctldhVJgFRo8Ge4AZxnVH742DN5MEtfy93EhPobTIrJTGSvZeTLZNEVE9A2KiHo/8B2f1r8g==", "signatures": [{"sig": "MEYCIQD3+0kWQDBIp8gIwQAguXjDFrlct+ZzXp7LpYOt/viC+QIhAIdiKR6qelWaqjZeEL/58fGzwAFh/Q1uGFDuEEwhFqXD", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12292}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "directories": {}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/helper-validator-option_8.0.0-beta.0_1748620239356_0.514875007957537", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.1": {"name": "@babel/helper-validator-option", "version": "8.0.0-beta.1", "description": "Validate plugin/preset options", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-validator-option"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "module", "_id": "@babel/helper-validator-option@8.0.0-beta.1", "dist": {"shasum": "7545f55ec96df12651b36201606037d5bb574776", "integrity": "sha512-wmEesbdHVtN6BBcfmoSgzwWjJneWGWZPv4wMpyn4oPYgH21SOU4NyU+xMUf29JjhixcZqSkXnxSsig9CasXaLA==", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 12292, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCvsaxLsM1boJqa86ongx5QWsTbRvjLoOhxjl2s+Oro9QIhAO+MY/lQdW9OaaW+txHlV8aCl1qE2AirE32qZ6VxMpXc"}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "actor": {"name": "nicolo-ribaudo", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/helper-validator-option_8.0.0-beta.1_1751447035176_0.15278505699804978"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-10-14T20:02:34.621Z", "modified": "2025-07-02T09:03:55.612Z", "7.12.0": "2020-10-14T20:02:35.036Z", "7.12.1": "2020-10-15T22:39:19.635Z", "7.12.11": "2020-12-15T23:59:15.651Z", "7.12.16": "2021-02-11T22:46:54.429Z", "7.12.17": "2021-02-18T15:12:22.221Z", "7.14.5": "2021-06-09T23:11:19.822Z", "7.16.7": "2021-12-31T00:21:20.034Z", "7.18.6": "2022-06-27T19:49:45.284Z", "7.21.0": "2023-02-20T15:30:54.382Z", "7.21.4-esm": "2023-04-04T14:09:02.172Z", "7.21.4-esm.1": "2023-04-04T14:20:53.606Z", "7.21.4-esm.2": "2023-04-04T14:38:35.534Z", "7.21.4-esm.3": "2023-04-04T14:55:48.447Z", "7.21.4-esm.4": "2023-04-04T15:13:02.594Z", "7.22.5": "2023-06-08T18:21:02.413Z", "8.0.0-alpha.0": "2023-07-20T13:59:35.236Z", "8.0.0-alpha.1": "2023-07-24T17:50:55.964Z", "8.0.0-alpha.2": "2023-08-09T15:14:32.404Z", "7.22.15": "2023-09-04T12:25:03.466Z", "8.0.0-alpha.3": "2023-09-26T14:56:25.684Z", "8.0.0-alpha.4": "2023-10-12T02:05:53.642Z", "7.23.5": "2023-11-29T10:25:38.928Z", "8.0.0-alpha.5": "2023-12-11T15:18:16.318Z", "8.0.0-alpha.6": "2024-01-26T16:13:41.871Z", "8.0.0-alpha.7": "2024-02-28T14:04:07.175Z", "8.0.0-alpha.8": "2024-04-04T13:19:24.522Z", "7.24.6": "2024-05-24T12:24:09.446Z", "8.0.0-alpha.9": "2024-06-03T14:03:45.888Z", "8.0.0-alpha.10": "2024-06-04T11:19:39.287Z", "7.24.7": "2024-06-05T13:14:55.735Z", "8.0.0-alpha.11": "2024-06-07T09:15:10.791Z", "7.24.8": "2024-07-11T14:54:42.982Z", "8.0.0-alpha.12": "2024-07-26T17:33:06.322Z", "7.25.7": "2024-10-02T15:14:24.416Z", "7.25.9": "2024-10-22T15:20:39.426Z", "8.0.0-alpha.13": "2024-10-25T13:53:43.620Z", "8.0.0-alpha.14": "2024-12-06T16:53:35.167Z", "8.0.0-alpha.15": "2025-01-10T17:23:59.055Z", "8.0.0-alpha.16": "2025-02-14T11:58:36.993Z", "8.0.0-alpha.17": "2025-03-11T18:24:26.820Z", "7.27.1": "2025-04-30T15:08:27.857Z", "8.0.0-beta.0": "2025-05-30T15:50:39.514Z", "8.0.0-beta.1": "2025-07-02T09:03:55.361Z"}, "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-helper-validator-option"}, "description": "Validate plugin/preset options", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}