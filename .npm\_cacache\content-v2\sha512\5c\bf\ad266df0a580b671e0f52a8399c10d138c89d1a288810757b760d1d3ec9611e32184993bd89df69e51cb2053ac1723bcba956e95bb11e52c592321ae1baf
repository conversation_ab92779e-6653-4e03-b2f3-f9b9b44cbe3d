{"_id": "yargs-parser", "_rev": "109-9cad5e1af7d3babf0d4a3062cf468632", "name": "yargs-parser", "dist-tags": {"alpha": "1.1.1-alpha3", "latest-13": "13.1.2", "next": "20.2.6", "latest-5": "5.0.1", "latest-15": "15.0.3", "latest": "22.0.0"}, "versions": {"1.0.0": {"name": "yargs-parser", "version": "1.0.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@1.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "dist": {"shasum": "0a64d7fffaf2063ef1d817ffbb29b0a553e75f1d", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-1.0.0.tgz", "integrity": "sha512-iiT6ftv5p4Fh0mB/Yr6+Va1DdqDBdxBikqUqS0Dq2/u4bpjSzeOZ2bsFVzvpMzPZwqQzsU6ZSmrN7WL57D530w==", "signatures": [{"sig": "MEYCIQDQTLvS+fmRgewPGYxLinxg1zFIf8feJdPWnIfcnsg7HAIhAP/krjMSKlpzcPD/TsIZfu2LS3SXeApxbL6oczCX58fh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "0a64d7fffaf2063ef1d817ffbb29b0a553e75f1d", "gitHead": "220bddc3f010f158703a0401234bea611cde49b9", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "_npmVersion": "3.3.0", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "3.2.0", "dependencies": {"camelcase": "^2.0.1"}, "devDependencies": {"nyc": "^5.3.0", "chai": "^3.4.1", "mocha": "^2.3.4", "standard": "^5.4.1", "coveralls": "^2.11.6"}}, "1.1.0": {"name": "yargs-parser", "version": "1.1.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@1.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "395ac847eb10c8298cb6c5547b4bc8484c3c0a9a", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-1.1.0.tgz", "integrity": "sha512-Q0SZpDbSwRSYu6cTcLsaVxg9Z3bi/X21lWhmdSfFFXAihsG6/m1n9sIjwQ8K4Pv0HOMj3/dvrXeZQRS/CZyhgg==", "signatures": [{"sig": "MEQCIDr5Il8qT2Sw4BXNsep5ZXi+ZVHGJMku+J/9RFnIPY8iAiAKTb/eB8tqVmCgeIoGxStJEe4SlanODD7v68KZioZEaw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "395ac847eb10c8298cb6c5547b4bc8484c3c0a9a", "gitHead": "56a1e3b3f473b5111201bf3c024b7a823c764f4e", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "3.3.0", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "3.2.0", "dependencies": {"find-up": "^1.1.0", "pkg-conf": "^1.1.1", "camelcase": "^2.0.1"}, "devDependencies": {"nyc": "^5.3.0", "chai": "^3.4.1", "mocha": "^2.3.4", "rimraf": "^2.5.1", "standard": "^5.4.1", "coveralls": "^2.11.6"}}, "1.1.1-alpha": {"name": "yargs-parser", "version": "1.1.1-alpha", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@1.1.1-alpha", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "cec04c7c9bd83630bc27a75f47a1200ea2a17a7d", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-1.1.1-alpha.tgz", "integrity": "sha512-wPr26pbqZjcejk4KmyP5J+JXLdSZUtQ0nHXHUbQSEi4bfiMHvHMJU4yiUMIgOhQ8i7I0W3tgdMeXhxKHDh5K+Q==", "signatures": [{"sig": "MEQCIE6/qfWQwGbtYuv4nWTzZkymnMKfSgQZeXYN+rweTVyLAiBveqOGdDVrqc3aBlWYEvX6BCfpexLjfogmS8AA0wmiYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "cec04c7c9bd83630bc27a75f47a1200ea2a17a7d", "gitHead": "6aa47d9b5a01f58eef52eeb03ff7dfbe38dee41c", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "3.3.0", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "3.2.0", "dependencies": {"pkg-conf": "^1.1.1", "camelcase": "^2.0.1", "app-root-path": "^1.0.0"}, "devDependencies": {"nyc": "^5.3.0", "chai": "^3.4.1", "mocha": "^2.3.4", "rimraf": "^2.5.1", "standard": "^5.4.1", "coveralls": "^2.11.6"}}, "1.1.1-alpha2": {"name": "yargs-parser", "version": "1.1.1-alpha2", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@1.1.1-alpha2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "900e0893db36fff838457735550907a0e67d80e6", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-1.1.1-alpha2.tgz", "integrity": "sha512-IS+IGiiHPEhjQEw0Pt7Nx436Tp4RQpnzMBQJSfXdp+F23jTp6eYS7NOZaOYySFgciWVwVnZ1Mq0P3p0qEXXfDQ==", "signatures": [{"sig": "MEQCICHhL29xQDZtIdIYcdZ5F/X8HETelJzSgzYrciHYRG0+AiAP8qjxJailBG4nk4nHcBj+DJeEKu/Y3CfAoXvchG30KQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "900e0893db36fff838457735550907a0e67d80e6", "gitHead": "56a1e3b3f473b5111201bf3c024b7a823c764f4e", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "3.3.0", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "3.2.0", "dependencies": {"pkg-conf": "^1.1.1", "camelcase": "^2.0.1"}, "devDependencies": {"nyc": "^5.3.0", "chai": "^3.4.1", "mocha": "^2.3.4", "rimraf": "^2.5.1", "standard": "^5.4.1", "coveralls": "^2.11.6"}}, "1.1.1-alpha3": {"name": "yargs-parser", "version": "1.1.1-alpha3", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@1.1.1-alpha3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "b2aa0257f333b15302818770d1b0c7eb6b80ce49", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-1.1.1-alpha3.tgz", "integrity": "sha512-vwYruU3GM3k7jQoz1quuWXFv28vbfgZV2AUJMCR4o7wUvTre48bhSQpuhhuM0yTJYvWxaNO7N4zT+Pf8y26kTw==", "signatures": [{"sig": "MEUCIFZvutJU7P+7C85ZwNt8AjFY/8cYdLVg/dK4eZP0quXxAiEAt5u07/lkTLjFZsc6oGyvgfTSV97a0bOsU/LZGlgc7TM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "b2aa0257f333b15302818770d1b0c7eb6b80ce49", "gitHead": "7f957e37be87f2b70c33cf855c02cc5b19684856", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "3.3.0", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "3.2.0", "dependencies": {"pkg-conf": "^1.1.1", "camelcase": "^2.0.1", "require-main-filename": "^1.0.0"}, "devDependencies": {"nyc": "^5.3.0", "chai": "^3.4.1", "mocha": "^2.3.4", "rimraf": "^2.5.1", "standard": "^5.4.1", "coveralls": "^2.11.6"}}, "2.0.0": {"name": "yargs-parser", "version": "2.0.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@2.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "7fa0be0f4f48d6ffaa467dfbc8b1aa8f776ba5c2", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-2.0.0.tgz", "integrity": "sha512-VOkZvFaclaj4BvntyIWkfAHWbx0kf8SFPCkPX0a6rR1tJ/EYpr3/p//8Zypxh7+VWxejAm2y00hXTREuLLLgqw==", "signatures": [{"sig": "MEYCIQDtZE/0C/PUElxZn+uQybrV6Q3vpjlS0ADOM7FaJ4MooQIhAJZmI8rjnozrUe4vT2rtrLFiweZKNhtWvuggDqg3KcHu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "7fa0be0f4f48d6ffaa467dfbc8b1aa8f776ba5c2", "gitHead": "1c5dcebaa87e28a00d50a9255ea5035549f96d1e", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "3.3.0", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "3.2.0", "dependencies": {"camelcase": "^2.1.0", "lodash.assign": "^4.0.2"}, "devDependencies": {"nyc": "^5.6.0", "chai": "^3.5.0", "mocha": "^2.4.5", "standard": "^5.4.1", "coveralls": "^2.11.6"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-2.0.0.tgz_1454791157643_0.925775176146999", "host": "packages-5-east.internal.npmjs.com"}}, "2.1.0": {"name": "yargs-parser", "version": "2.1.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@2.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "5f214362f948a085389abf34a78d8b5eef0b1350", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-2.1.0.tgz", "integrity": "sha512-fsXTdITN82dIU3qk9YjvB8/gfXi1qPd+U9/FBoqkzM9GqTjewZu4AoMxp7Y/UAt+EZ4BOB94bjVtG7kwPixJuA==", "signatures": [{"sig": "MEUCIQD2Fuh5KPY0KM6B6vqhefCfuA1J+kp71ZnUezHahdQ4jwIgXaTJGGHC7TonaxKZp0IsZZfO5mvLlhdi17p3dgpuQlA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "5f214362f948a085389abf34a78d8b5eef0b1350", "gitHead": "78f49ffde9a3305facb66b2faeae862b33d0745e", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "3.3.0", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "3.2.0", "dependencies": {"camelcase": "^2.1.0", "lodash.assign": "^4.0.2"}, "devDependencies": {"nyc": "^5.6.0", "chai": "^3.5.0", "mocha": "^2.4.5", "standard": "^5.4.1", "coveralls": "^2.11.6"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-2.1.0.tgz_1455440640589_0.058524578576907516", "host": "packages-9-west.internal.npmjs.com"}}, "2.1.1": {"name": "yargs-parser", "version": "2.1.1", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@2.1.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "6bbb36d28cc03e2d57d172b4017d612e5ff6a0b6", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-2.1.1.tgz", "integrity": "sha512-LTX1keS+RA7+pQA6rysKzZgIRik4nz6JWIvOKwVjR9pM0oNbAJ/U2DXNnHdlvu3roIpMwQQVuG5KbQNbwWf5Ew==", "signatures": [{"sig": "MEUCIQDTTZv+wARI4BBDe4IZ+LJ6YM1lBXKeWAYB9cj6CtKipgIgVuncLolBm0V5VF+V6Ei/x8QTCV2uh+QqJYd2HrpcdXY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "6bbb36d28cc03e2d57d172b4017d612e5ff6a0b6", "gitHead": "05c8df71668228f022c21c995a6cf281564313b1", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "3.3.0", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "3.2.0", "dependencies": {"camelcase": "^2.1.0", "lodash.assign": "^4.0.2"}, "devDependencies": {"nyc": "^5.6.0", "chai": "^3.5.0", "mocha": "^2.4.5", "standard": "^5.4.1", "coveralls": "^2.11.6"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-2.1.1.tgz_1456210467734_0.12809900217689574", "host": "packages-9-west.internal.npmjs.com"}}, "2.1.2": {"name": "yargs-parser", "version": "2.1.2", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@2.1.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "e675aa728c72d9e95bd419c962c75a9e92f1c9da", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-2.1.2.tgz", "integrity": "sha512-yJOl3PSM4apoId+QlghCA4vbNBR8RsYUrtSYe+fiWANk2sCqDceSyH1vh0EOeTlKwc4BO/uS1W5DquXe3k2dPQ==", "signatures": [{"sig": "MEYCIQDmC0A6gHyc3ZlokfF+/Gj19PNivjDQDmB9hqRe5kafcAIhAIBz2GFo4spNFVv3ikGVHsUkft4KL1aOjs/KKi/MpecJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "e675aa728c72d9e95bd419c962c75a9e92f1c9da", "gitHead": "4ee3318d1420c97b0bd8b4e5d1641eb33cb122e6", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "3.3.0", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "3.2.0", "dependencies": {"camelcase": "^2.1.1", "lodash.assign": "^4.0.6"}, "devDependencies": {"nyc": "^6.1.1", "chai": "^3.5.0", "mocha": "^2.4.5", "standard": "^6.0.8", "coveralls": "^2.11.8"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-2.1.2.tgz_1458500649814_0.29023746121674776", "host": "packages-12-west.internal.npmjs.com"}}, "2.2.0": {"name": "yargs-parser", "version": "2.2.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@2.2.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "29c4185b9c42c259f6ebbe054d1f46014e71d73b", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-2.2.0.tgz", "integrity": "sha512-c8E<PERSON>flyREfWweB8B45kITPMrStvblvQ7OYaASrVnnFS2peSZ52uc14CLPWkd0AAAhNLaFGlv58d5LvMcDMBrw==", "signatures": [{"sig": "MEUCIQDRSPUePnMJYdapYkhtmmTHcWUv7dUXkNXPKCAa3rZJKQIgK06X/ZugESzD4NFNbBOrfFMLIAc4Sprr6mcCy9nmLOo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "29c4185b9c42c259f6ebbe054d1f46014e71d73b", "gitHead": "48b1e6a338f80417b3fd0fc3e39ae181332fdcb5", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "3.3.12", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"camelcase": "^2.1.1", "lodash.assign": "^4.0.6"}, "devDependencies": {"nyc": "^6.1.1", "chai": "^3.5.0", "mocha": "^2.4.5", "standard": "^6.0.8", "coveralls": "^2.11.8"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-2.2.0.tgz_1459318523723_0.7542532840743661", "host": "packages-12-west.internal.npmjs.com"}}, "2.4.0-next": {"name": "yargs-parser", "version": "2.4.0-next", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@2.4.0-next", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "537d617a12162a652c875eec7d3ea9df0fe12cf6", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-2.4.0-next.tgz", "integrity": "sha512-7T/+nxBLjcywd8FZb0TVOhNaPPQHl/b+FgZjmZ1XeW1POY6z1nXMryVO0UE9i12Oi5EYQRpfI7M+LREg/WdyYg==", "signatures": [{"sig": "MEUCIFrdKtl+0v2YcNRDPAsAXveKmJPabr15bu6CCl7swsDuAiEA01sPjVMEc7HYyzBcpfaPLEREOvW+hJ1EVgEG+4i5qDk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib", "index.js"], "_shasum": "537d617a12162a652c875eec7d3ea9df0fe12cf6", "gitHead": "4a504775a708b270822511c25531db8d69119a04", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "3.3.12", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"camelcase": "^2.1.1", "lodash.assign": "^4.0.6"}, "devDependencies": {"nyc": "^6.4.0", "chai": "^3.5.0", "mocha": "^2.4.5", "standard": "^6.0.8", "coveralls": "^2.11.8", "standard-version": "^2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-2.4.0-next.tgz_1460343866053_0.5493791722692549", "host": "packages-16-east.internal.npmjs.com"}}, "2.4.0": {"name": "yargs-parser", "version": "2.4.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@2.4.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "1f367dc9c6cfa5660b6971230f3b277fc5e3adca", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-2.4.0.tgz", "integrity": "sha512-8ws1cCd9jRrDOQ2TRsWxcEMAE1uYtrKSY6H8ebD5lqIUKGY0oqNB+MV8lRD1tONRKbsWRHEkLKTtYSLn6SsHcg==", "signatures": [{"sig": "MEQCIFdCr4cEGLiQOABwKAGcvkOdvJequuXgI0neTDMfJ4siAiAiveSzxL4BOWgefNUxdFSnpq/S3YktBPWo6iXSMCwTMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib", "index.js"], "_shasum": "1f367dc9c6cfa5660b6971230f3b277fc5e3adca", "gitHead": "4a504775a708b270822511c25531db8d69119a04", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "3.3.12", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"camelcase": "^2.1.1", "lodash.assign": "^4.0.6"}, "devDependencies": {"nyc": "^6.4.0", "chai": "^3.5.0", "mocha": "^2.4.5", "standard": "^6.0.8", "coveralls": "^2.11.8", "standard-version": "^2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-2.4.0.tgz_1460354642776_0.13956820615567267", "host": "packages-16-east.internal.npmjs.com"}}, "2.4.1": {"name": "yargs-parser", "version": "2.4.1", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@2.4.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "85568de3cf150ff49fa51825f03a8c880ddcc5c4", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-2.4.1.tgz", "integrity": "sha512-9pIKIJhnI5tonzG6OnCFlz/yln8xHYcGl+pn3xR0Vzff0vzN1PbNRaelgfgRUwZ3s4i3jvxT9WhmUGL4whnasA==", "signatures": [{"sig": "MEQCICb5YgfeSO+EDDVL7RjJQU6HhFeWlCm8+MC8XYcrI2SJAiA8rBVY1Zp9YNT5cg2gp56W4OdOdhxS1Vs+GX9h2+L4JA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib", "index.js"], "_shasum": "85568de3cf150ff49fa51825f03a8c880ddcc5c4", "gitHead": "e7c71d5b8e0088596aef17a4e2b0261a85fca713", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "version": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "3.3.12", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"camelcase": "^3.0.0", "lodash.assign": "^4.0.6"}, "devDependencies": {"nyc": "^7.0.0", "chai": "^3.5.0", "mocha": "^2.4.5", "standard": "^7.1.0", "coveralls": "^2.11.8", "standard-version": "^2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-2.4.1.tgz_1468709489453_0.7414652374573052", "host": "packages-16-east.internal.npmjs.com"}}, "3.1.0": {"name": "yargs-parser", "version": "3.1.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@3.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "5bc511dc1b0551275bfb15344874ded8d22d2604", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-3.1.0.tgz", "integrity": "sha512-H2EwbqfYTsyjEE5wEd+9soWVxXFA/ERE8jequ/RmqS99BVd7sVzA90v3RehYgrv3dRE554pS+nXCtv5bIJ9q8A==", "signatures": [{"sig": "MEYCIQDM/k6kLl4QwAIb7AwynYbqxYPvQQ94RH8M5O9zEhnDXwIhAKSTGM2pL+qbzYSa4TjLE5GlfcyiUOljgEbF/C/omdKB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib", "index.js"], "_shasum": "5bc511dc1b0551275bfb15344874ded8d22d2604", "gitHead": "948e1cde41adb6142f47c0ce38992f8d1f58e10f", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "3.10.6", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"camelcase": "^3.0.0", "lodash.assign": "^4.1.0"}, "devDependencies": {"nyc": "^7.1.0", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^7.1.0", "coveralls": "^2.11.12", "standard-version": "^2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-3.1.0.tgz_1470723974935_0.08871294185519218", "host": "packages-16-east.internal.npmjs.com"}}, "3.2.0": {"name": "yargs-parser", "version": "3.2.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@3.2.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "5081355d19d9d0c8c5d81ada908cb4e6d186664f", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-3.2.0.tgz", "integrity": "sha512-eANlJIqYwhwS/asi4ybKxkeJYUIjNMZXL36C/KICV5jyudUZWp+/lEfBHM0PuJcQjBfs00HwqePEQjtLJd+Kyw==", "signatures": [{"sig": "MEUCIEbQhuCHIvzQHQSuhUbZjdKv0FR/UYSFRtaujfOLtZ67AiEA3uyOm7OmLlDDnQOiohN4dEJdl8mBKNKnDHGmyvf3a50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib", "index.js"], "_shasum": "5081355d19d9d0c8c5d81ada908cb4e6d186664f", "gitHead": "a4c4f0c4e9417c968feec98caa39d69983bf31ec", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "3.10.6", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "5.1.0", "dependencies": {"camelcase": "^3.0.0", "lodash.assign": "^4.1.0"}, "devDependencies": {"nyc": "^7.1.0", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^7.1.0", "coveralls": "^2.11.12", "standard-version": "^2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-3.2.0.tgz_1471118009225_0.3763321761507541", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.0": {"name": "yargs-parser", "version": "4.0.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@4.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "a36825f971a02bb6059797116d14e6588be586f0", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-4.0.0.tgz", "integrity": "sha512-LdeYYVms4x8dFJpP5KvSJmQngnYNSzBZDGMeekOk8Rw2e37K7kLOCvp2Y0H0avN+53jhzZpktvaur4rclgHFWg==", "signatures": [{"sig": "MEYCIQDugEjgja4e+gvXx7jruz80v8BIuMJg+fIW/YLU6YjgaQIhAMV5t+sMou2+QzZl4rvxc90PAEwnuwxejSl00bIOB8Yv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib", "index.js"], "_shasum": "a36825f971a02bb6059797116d14e6588be586f0", "gitHead": "ec6df1ca43f110250dfaf8cc5660e8d827a72667", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "3.10.3", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"camelcase": "^3.0.0", "lodash.assign": "^4.1.0"}, "devDependencies": {"nyc": "^8.1.0", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^8.0.0", "coveralls": "^2.11.12", "standard-version": "^2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-4.0.0.tgz_1474868319878_0.024560872232541442", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.1": {"name": "yargs-parser", "version": "4.0.1", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@4.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "7f6fbfa24bbf512846240529c68e88130c6c713b", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-4.0.1.tgz", "integrity": "sha512-KkfrslhxqgipWuvAaOpfW7+15nInJzl5mftHz/KXXUHG+62NuTMVxr3bY2GhCGcNppv5X2jjuwmQFPV1YY42/g==", "signatures": [{"sig": "MEUCIDzdPylUZp+rLy6caZMXhD7cewuRXkqCKuZZ305ZV4kNAiEA6thAVEl787Mdx/7W4+B4o+2kZtBedqHzGHB5eerzT1E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib", "index.js"], "_shasum": "7f6fbfa24bbf512846240529c68e88130c6c713b", "gitHead": "b43b7c1b3d92307bf1ee4b319d30a07656d25617", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "2.15.1", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "0.10.47", "dependencies": {"camelcase": "^3.0.0"}, "devDependencies": {"nyc": "^8.1.0", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^8.0.0", "coveralls": "^2.11.12", "standard-version": "^2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-4.0.1.tgz_1475217375019_0.16979361232370138", "host": "packages-12-west.internal.npmjs.com"}}, "4.0.2": {"name": "yargs-parser", "version": "4.0.2", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@4.0.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "7f7173a8c7cca1d81dc7c18692fc07c2c2e2b1e0", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-4.0.2.tgz", "integrity": "sha512-feHRNN1ZO0vCSbl0wpkJvOzufe8I5xFNFKwjlDrc1Or77ITu5FZXe0tK8mcHy6ctxKaDloT49EiwzzhNlbypQw==", "signatures": [{"sig": "MEQCIEbIKw+yfF6TOjfZ5fSmCUbkHoQxIkvaO5d+Sm+RdLQHAiAe/NxKAe0w41E987Lb8ueov6nsnL9MNktzcqorZmQosQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib", "index.js"], "_shasum": "7f7173a8c7cca1d81dc7c18692fc07c2c2e2b1e0", "gitHead": "466e38b97c1ead7b6b777500cf4392a2c7645921", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "2.15.1", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "0.10.47", "dependencies": {"camelcase": "^3.0.0"}, "devDependencies": {"nyc": "^8.1.0", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^8.0.0", "coveralls": "^2.11.12", "standard-version": "^2.1.2"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-4.0.2.tgz_1475218337015_0.34343091025948524", "host": "packages-12-west.internal.npmjs.com"}}, "4.1.0": {"name": "yargs-parser", "version": "4.1.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@4.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "313df030f20124124aeae8fbab2da53ec28c56d7", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-4.1.0.tgz", "integrity": "sha512-wygozUoC91CSRuLNvQWywv3CHrMsjv0r7nV+HYb4mbsmCgnv97mIe1JNYx3rXV1R0Dp+PFFq8g7k5Q4YLzw14A==", "signatures": [{"sig": "MEYCIQDSHWXshOpuwvVKftrbHuT8y/uVoceY44HLBwuXzD2E5QIhAJi7wBehWy4RIfejLuhiEyLbW+tFMO1WWjgAuDjVVbKm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib", "index.js"], "_shasum": "313df030f20124124aeae8fbab2da53ec28c56d7", "gitHead": "a491feb6711a8b6eda11755bebf73ead8e182d30", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "3.10.3", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "6.5.0", "dependencies": {"camelcase": "^3.0.0"}, "devDependencies": {"nyc": "^8.1.0", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^8.0.0", "coveralls": "^2.11.12", "standard-version": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-4.1.0.tgz_1478500309828_0.6643792404793203", "host": "packages-18-east.internal.npmjs.com"}}, "4.2.0": {"name": "yargs-parser", "version": "4.2.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@4.2.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "6ced869cd05a3dca6a1eaee38b68aeed4b0b4101", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-4.2.0.tgz", "integrity": "sha512-+8ziwnV1zaBVzROO48iqU/ursaR0zJAg22ukymU3CT+lm9Wr/rWY65CqFr5Ut+Zicxwzo0RKk2QhktgrfMPZHg==", "signatures": [{"sig": "MEYCIQCsMmYf4bjR3t2PonQI/8Az0y5hnyO4+X/3dmujczXNPQIhALFOZKZmkmPlS30wXOXG3q3RzSbes/Y64d2NJvh+mz4G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib", "index.js"], "_shasum": "6ced869cd05a3dca6a1eaee38b68aeed4b0b4101", "gitHead": "941d8f3a5eaa008986816185c05b02a12ff0abeb", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "3.10.9", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "7.2.0", "dependencies": {"camelcase": "^3.0.0"}, "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^8.0.0", "coveralls": "^2.11.12", "standard-version": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-4.2.0.tgz_1480618162618_0.04404683620668948", "host": "packages-18-east.internal.npmjs.com"}}, "4.2.1": {"name": "yargs-parser", "version": "4.2.1", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@4.2.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "29cceac0dc4f03c6c87b4a9f217dd18c9f74871c", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-4.2.1.tgz", "integrity": "sha512-+QQWqC2xeL0N5/TE+TY6OGEqyNRM+g2/r712PDNYgiCdXYCApXf1vzfmDSLBxfGRwV+moTq/V8FnMI24JCm2Yg==", "signatures": [{"sig": "MEUCIAOx2c3fpj4GMyrlWHGp4D1yNd2LBRaK+fw3v5Ok63PjAiEAz63+coQGbd1V8BlLjz6HxKO5Dr22W5GK26wFP5/ToCU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib", "index.js"], "_shasum": "29cceac0dc4f03c6c87b4a9f217dd18c9f74871c", "gitHead": "924b014ac25ecbaa2a01f40c2100512a68bdba1d", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "3.10.9", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"camelcase": "^3.0.0"}, "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^8.0.0", "coveralls": "^2.11.12", "standard-version": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-4.2.1.tgz_1483386157947_0.938966978341341", "host": "packages-18-east.internal.npmjs.com"}}, "4.2.1-candidate.0": {"name": "yargs-parser", "version": "4.2.1-candidate.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@4.2.1-candidate.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "182c95b17190db70c80c5cfb6dec3609acfe51ee", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-4.2.1-candidate.0.tgz", "integrity": "sha512-mXLFCvaFSyxgVW4yeDef1ZdFDoykeVSVhpaUxk0xo639wMdoADpMi2hpoIP0VsOjUBkOX5tmke57H42bJqlxdw==", "signatures": [{"sig": "MEUCIQCaV7dbIKWeOTbteBSNjKIij715Q/kS43Ur1DKDTVU5UQIgXWuEdlKM7nzyDa/iv5l9E8h3wyQo2aFSKj1ahNS63UM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib", "index.js"], "_shasum": "182c95b17190db70c80c5cfb6dec3609acfe51ee", "gitHead": "924b014ac25ecbaa2a01f40c2100512a68bdba1d", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "3.10.9", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"camelcase": "^3.0.0"}, "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^8.0.0", "coveralls": "^2.11.12", "standard-version": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-4.2.1-candidate.0.tgz_1483743098860_0.751906061777845", "host": "packages-18-east.internal.npmjs.com"}}, "4.2.1-candidate.1": {"name": "yargs-parser", "version": "4.2.1-candidate.1", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@4.2.1-candidate.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "6982d85251771d897f9b96788e9fe9a53a42db32", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-4.2.1-candidate.1.tgz", "integrity": "sha512-161joKJ82lMDDZz6JccY9stf6HYmTi1MAM0YHScJEmgB5VgTTJCZuf3F2WBCBMDSLTuHVn1+/6rfMNGPqLhLbA==", "signatures": [{"sig": "MEUCIQDKQ/9o1Gv02zrSatlP6xutwF5nCuos4PdmUBsWafkmfgIgNwd15qDxC/EO1hn3gMD10iCLEL/0eXzcnzX3LsMAJJM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib", "index.js"], "_shasum": "6982d85251771d897f9b96788e9fe9a53a42db32", "gitHead": "924b014ac25ecbaa2a01f40c2100512a68bdba1d", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "3.10.9", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "7.1.0", "dependencies": {"camelcase": "^3.0.0"}, "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^8.0.0", "coveralls": "^2.11.12", "standard-version": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-4.2.1-candidate.1.tgz_1483744955649_0.30080202664248645", "host": "packages-12-west.internal.npmjs.com"}}, "5.0.0": {"name": "yargs-parser", "version": "5.0.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@5.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "275ecf0d7ffe05c77e64e7c86e4cd94bf0e1228a", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-5.0.0.tgz", "integrity": "sha512-YQY9oiTXNdi9y+RJMjqIwQklfEc4flSuVCuXZS6bRTEAY76eL3bKsZbs6KTsWxHsGXJdSgp1Jj/8AmLpGStEnQ==", "signatures": [{"sig": "MEUCIQCFcMB1yrGwRG4O34dXItgwpMRmfNomH+l4ZrnKKtP9AAIgASnwPdjSNZFq8bHAi2HHfD05fowSE4QvQ7NjabLZtAM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib", "index.js"], "_shasum": "275ecf0d7ffe05c77e64e7c86e4cd94bf0e1228a", "gitHead": "2c95ba9e5ad3b8bb6248bf41f013d9bd3700d56f", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "2.15.1", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "0.10.48", "dependencies": {"camelcase": "^3.0.0"}, "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^8.0.0", "coveralls": "^2.11.12", "standard-version": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-5.0.0.tgz_1487447930908_0.674228576477617", "host": "packages-18-east.internal.npmjs.com"}}, "6.0.0": {"name": "yargs-parser", "version": "6.0.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@6.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "d8c91626b560b3fd1173390c28b882aa4171920d", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-6.0.0.tgz", "integrity": "sha512-j<PERSON><PERSON><PERSON><PERSON>seIDVUJsxCBfmN5YQOgVwXQx7RYWKlo7Agu8pRCeOZfGHvTOLXSlAi9FsbytEBvDRkqY6ZhaAuv1768g==", "signatures": [{"sig": "MEQCIA+OleSPPh+WxKYnUAy62eiRTLMxKjrZbWhUXlMkKzg/AiAUcvAluiBWdxTcRG+TPT1/ujYF0ABTKifpRtMjSJOCHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib", "index.js"], "_shasum": "d8c91626b560b3fd1173390c28b882aa4171920d", "gitHead": "4a383cf0902f4f7205847267ef0f2c4220ace660", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "2.15.11", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "4.8.2", "dependencies": {"camelcase": "^4.1.0"}, "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^10.0.2", "coveralls": "^2.11.12", "standard-version": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-6.0.0.tgz_1493599951495_0.06887614144943655", "host": "packages-12-west.internal.npmjs.com"}}, "6.0.1": {"name": "yargs-parser", "version": "6.0.1", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@6.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "0026e672e284e7df459111d2e51462efad725350", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-6.0.1.tgz", "integrity": "sha512-qkCu+pCT+pCRLOBY+T1RzFK0uol5QlQV/dODl+cAnL62NcCYFRRmkfREOI+vdsaUQLYaTcfzf2Lhxztm/VcM4g==", "signatures": [{"sig": "MEUCIA/AJCCu8YBNy3X8CU1IiBszIYb94Vt2HrEI/Ppa3A+3AiEAps5HPCbnosI5979LkQ4hvVDar5H8Yb2rEXXta5/fU5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib", "index.js"], "_shasum": "0026e672e284e7df459111d2e51462efad725350", "gitHead": "05522182f903a74e3458fb5adba92a4bfbf9653d", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "2.15.11", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "4.8.2", "dependencies": {"camelcase": "^4.1.0"}, "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^10.0.2", "coveralls": "^2.11.12", "standard-version": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-6.0.1.tgz_1493618237360_0.13729879376478493", "host": "packages-12-west.internal.npmjs.com"}}, "7.0.0": {"name": "yargs-parser", "version": "7.0.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@7.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "8d0ac42f16ea55debd332caf4c4038b3e3f5dfd9", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-7.0.0.tgz", "integrity": "sha512-WhzC+xgstid9MbVUktco/bf+KJG+Uu6vMX0LN1sLJvwmbCQVxb4D8LzogobonKycNasCZLdOzTAk1SK7+K7swg==", "signatures": [{"sig": "MEQCIBBNxQhhLheTUUxMak09LiBqR12Q6IziDO64D8CMTle/AiBXby1MbpDzHYVq759QQ7DXMHpJ0SjAAud1wLnxt5dTCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib", "index.js"], "_shasum": "8d0ac42f16ea55debd332caf4c4038b3e3f5dfd9", "gitHead": "8ae91cddffc69f535e1d9ad1ce321444be84a483", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "4.2.0", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "7.9.0", "dependencies": {"camelcase": "^4.1.0"}, "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^10.0.2", "coveralls": "^2.11.12", "standard-version": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-7.0.0.tgz_1493704782540_0.1645174971781671", "host": "packages-12-west.internal.npmjs.com"}}, "8.0.0": {"name": "yargs-parser", "version": "8.0.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@8.0.0", "maintainers": [{"name": "nexdrew", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "21d476330e5a82279a4b881345bf066102e219c6", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-8.0.0.tgz", "integrity": "sha512-llYtLZW7X4oI2dd0wMAvP6yFCElBbw0k2525q+SAi5hobRi1VtjHmd/xS0Vu3yoxaAvtyP72Dl7Uji0jTnzmlQ==", "signatures": [{"sig": "MEUCIGyqOQOrQUeVe4evlOskBLJosDOUMgT+nwFguJpacfPnAiEAjt0QspwNFyV+5UCGiAjfkijJWHmgmG2MtQ6Q2ahyupo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib", "index.js"], "_shasum": "21d476330e5a82279a4b881345bf066102e219c6", "gitHead": "c7eee910d73ad24970f3addbf884cfa47092b3f8", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "3.10.10", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "6.11.4", "dependencies": {"camelcase": "^4.1.0"}, "devDependencies": {"nyc": "^11.2.1", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^10.0.2", "coveralls": "^2.11.12", "standard-version": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-8.0.0.tgz_1507184524796_0.6527229540515691", "host": "s3://npm-registry-packages"}}, "8.1.0": {"name": "yargs-parser", "version": "8.1.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@8.1.0", "maintainers": [{"name": "nexdrew", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "f1376a33b6629a5d063782944da732631e966950", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-8.1.0.tgz", "integrity": "sha512-yP+6QqN8BmrgW2ggLtTbdrOyBNSI7zBa4IykmiV5R1wl1JWNxQvWhMfMdmzIYtKU7oP3OOInY/tl2ov3BDjnJQ==", "signatures": [{"sig": "MEQCIE7UOvofW4BsnnAVQ4bCEp4HDmJpmnGH+4qtzFIguon8AiBmlIWA450wOKrP6GF9UiywUYCKMepJeb+qApRdsWQZ1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["lib", "index.js"], "gitHead": "29b02489b2111be89de94bf24f36e8ec8a6ba9e2", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "5.5.1", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"camelcase": "^4.1.0"}, "devDependencies": {"nyc": "^11.4.1", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^10.0.2", "coveralls": "^2.11.12", "standard-version": "^4.3.0-candidate.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-8.1.0.tgz_1513750813446_0.41336237522773445", "host": "s3://npm-registry-packages"}}, "9.0.0": {"name": "yargs-parser", "version": "9.0.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@9.0.0", "maintainers": [{"name": "nexdrew", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "1026f0827af0567ad73a65cdebe60c5f11a6117a", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-9.0.0.tgz", "integrity": "sha512-oAML+0t20XZQCnf0M4H5qjYV4gVQY0HAjxi4Kg7on/+X9WN9aEMPrHSEN3Df2noU/u3iU7T8vaJ5bug7MO2OuQ==", "signatures": [{"sig": "MEUCIQD0b9xPyBzR84boxiLeZ4h+GedO4mL8PVZdEYp5q6KeZgIgLhvFoZBr1ahoED5W+Nk+ZGoqlFSXvIKM8+k+L/lFrzQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib", "index.js"], "_shasum": "1026f0827af0567ad73a65cdebe60c5f11a6117a", "gitHead": "9734d9c24d6f6aa94ebd7f1d5093e309ae1d2a0a", "scripts": {"test": "nyc mocha test/*.js", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "2.15.11", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "4.8.7", "dependencies": {"camelcase": "^4.1.0"}, "devDependencies": {"nyc": "^11.4.1", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^10.0.2", "coveralls": "^2.11.12", "standard-version": "^4.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-9.0.0.tgz_1516488463416_0.8724576800595969", "host": "s3://npm-registry-packages"}}, "9.0.1": {"name": "yargs-parser", "version": "9.0.1", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@9.0.1", "maintainers": [{"name": "nexdrew", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "d1feb4b244d2ccd04a2ed2a81f4998387ed81a68", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-9.0.1.tgz", "integrity": "sha512-1VhwM/pTsLJMm0eAksPV5yuvB7RkMb8gY0kYUieXA2IGCmDshu2w4Xgty/POmfzPLUtKFDLXiZTOnAwWQJ2XLw==", "signatures": [{"sig": "MEUCIQChL/5RndobnMfc+vGeKKpNosINnTPvmccH+CaAA2yapwIgbZdGMzJgAbjIssBfBWi7a+J+Q1hYTG1H8k0G3w8taB0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "files": ["lib", "index.js"], "gitHead": "cf0d9d0b3e9e2565498c5ccf3c771828204dd88c", "scripts": {"test": "nyc mocha test/*.js", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "5.4.2", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "8.8.1", "dependencies": {"camelcase": "^4.1.0"}, "devDependencies": {"nyc": "^11.4.1", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^10.0.2", "coveralls": "^2.11.12", "standard-version": "^4.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-9.0.1.tgz_1516489420320_0.9541788336355239", "host": "s3://npm-registry-packages"}}, "9.0.2": {"name": "yargs-parser", "version": "9.0.2", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@9.0.2", "maintainers": [{"name": "nexdrew", "email": "<EMAIL>"}, {"name": "bcoe", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "9ccf6a43460fe4ed40a9bb68f48d43b8a68cc077", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-9.0.2.tgz", "integrity": "sha512-CswCfdOgCr4MMsT1GzbEJ7Z2uYudWyrGX8Bgh/0eyCzj/DXWdKq6a/ADufkzI1WAOIW6jYaXJvRyLhDO0kfqBw==", "signatures": [{"sig": "MEUCIQD5ibJ/t2yrjuagRz/hYzv0olTyIxl1d8RsE9yZ6iNPSQIge7m7RfkfRHE2BzWVd3gts8dSIbbf9sOiQ5pV8VaKMBg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "files": ["lib", "index.js"], "_shasum": "9ccf6a43460fe4ed40a9bb68f48d43b8a68cc077", "gitHead": "523aecd52ac5aec7c1e4aabf0fbd4992ba660bd0", "scripts": {"test": "nyc mocha test/*.js", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "2.15.11", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "4.8.7", "dependencies": {"camelcase": "^4.1.0"}, "devDependencies": {"nyc": "^11.4.1", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^10.0.2", "coveralls": "^2.11.12", "standard-version": "^4.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser-9.0.2.tgz_1516490623727_0.7419105849694461", "host": "s3://npm-registry-packages"}}, "10.0.0": {"name": "yargs-parser", "version": "10.0.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@10.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "c737c93de2567657750cb1f2c00be639fd19c994", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-10.0.0.tgz", "fileCount": 6, "integrity": "sha512-+DHejWujTVYeMHLff8U96rLc4uE4Emncoftvn5AjhB1Jw1pWxLzgBUT/WYbPrHmy6YPEBTZQx5myHhVcuuu64g==", "signatures": [{"sig": "MEUCIQDviVs85GcqbSTI6K6iyQ/1G2mtmS0frnY9IWMwGRsgkwIgVFtE7PmTMuwWcWiSaSXb7DQIXR9dZWcbStrDxslQEDo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47232}, "main": "index.js", "files": ["lib", "index.js"], "gitHead": "8c9706ff2c16e415fed6a89336c6cbfde7779eb3", "scripts": {"test": "nyc mocha test/*.js", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "5.6.0", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "9.10.1", "dependencies": {"camelcase": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.4.1", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^10.0.2", "coveralls": "^2.11.12", "standard-version": "^4.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_10.0.0_1522807810210_0.12159316833474909", "host": "s3://npm-registry-packages"}}, "10.1.0": {"name": "yargs-parser", "version": "10.1.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@10.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "7202265b89f7e9e9f2e5765e0fe735a905edbaa8", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-10.1.0.tgz", "fileCount": 6, "integrity": "sha512-VCIyR1wJoEBZUqk5PA+oOBF6ypbwh5aNB3I50guxAL/quggdfs4TtNHQrSazFA3fYZ+tEqfs0zIGlv0c/rgjbQ==", "signatures": [{"sig": "MEQCICrx2/R9xy2u9xlktsM4y3Xo+0yWEeCqBouJ5HCTE+CPAiBXkNX7+Eaft9Z5JpYeTi882tIJo/wVY19trbXWpO42nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48440, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbNcBKCRA9TVsSAnZWagAA7twP/jujSXkBJs7Lw44Pqk2z\ngCfoX/Ppfb9mqAvwrZxQCc9qQXUSQbxoaBQwt5J33W16eimw6vW7vrOaRTRE\nOzG5w1PwclKKoCFSx6/b+tzijg4DbsXeK6tr9i5W+xInB4YPDa3uUGtt8aiE\nzz3MEvs4No7gIeO8Ul448oIvQi3mfTNFiKAa8XLNuxqWJVdIPEuDVqo+SIv5\nrCuBjAHzNIUj3x1t7d76yf5Uug11TfNATtWdc3Z8cj6tSJrhaGE1IboJVn7e\n8nBknPSND7LArffjB48omgz0H/Kks3xjt7+8f7B+tLDJVOzqpxdEcsb85eqk\n2Ez+pPB+KpHX4bWN5tW8atSJgAzTF71qwwtwVbusQF6mgo7BKuFyo4lWbiEh\nM78FKcRffWZZUrsNLRLN6Is60DMDcdob805weBPemz/E7QEnW3uERth/GCio\nD/Wzb4ZLgxUDw84JdNjakxme+uQZSIy/1BM1k4BAUlslxu7aozdK11dZClqc\nMjOgpy4q46r8467D687b4VrU2mGUK85HeYjipoF/MqJa621zqFK/+U0RQFkZ\nfhnIgT3AGYpPMiaWHvpIYc24WhA4PSeRHKeEnh5PYAGb1KuJ7C8VwTKYhbFV\nfKNkX7Ti+P2k+iot9zhg0yRCLUO2mKSDxx4Zeb+ja9sZh05iCyKDEYXYdoeq\nGl7v\r\n=OGds\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "files": ["lib", "index.js"], "gitHead": "82f4ea5810ca36dbe934de593eb0d45cdcc724c2", "scripts": {"test": "nyc mocha test/*.js", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "6.1.0", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "10.5.0", "dependencies": {"camelcase": "^4.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^11.4.1", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^10.0.2", "coveralls": "^2.11.12", "standard-version": "^4.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_10.1.0_1530249290353_0.47473899653433116", "host": "s3://npm-registry-packages"}}, "11.0.0": {"name": "yargs-parser", "version": "11.0.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@11.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "d9fd0f0cd551a2a2ef9bbf42606ffb6211634232", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-11.0.0.tgz", "fileCount": 6, "integrity": "sha512-dvsafRjM45h79WOTvS/dP35Sb31SlGAKz6tFjI97kGC4MJFBuzTZY6TTYHrz0QSMQdkyd8Y+RsOMLr+JY0nPFQ==", "signatures": [{"sig": "MEYCIQCae1EdOmaZaJGWwhj4KAZ0xfoNcip4x3v9rSSxRfJ0+gIhAKLaK5egBrvpJGLAqtpcbRhYcudiyDOjhIXWPI+yvRQ0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50922, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbuTysCRA9TVsSAnZWagAAK+QP/AsMdMYiSl536Prv+ima\nokxa5FzL6Yb3Qm4Z7sUNHba+eSSagQlheSDERuaAWb42Pt8fdk0/XTxOphHP\nRqMrOeNtnYnnAvZjnzjUjKfkuz7KZTiu+OiHeugwGMiVEsx+YxSZHFj1WwY+\nLQuxeiHs0PC03S8PHv02UHithuWvYX1WbzcFHjb151h1WpaCQpS4/crlt6a3\ne6FfoM7zjJ/wRJHA+85Vuyetex3q5uC/9D62uBIvvK1URyilRveiXTIdue3Z\nXXdFVfxDfiK5OXHKlFlMBUMjCvS8Gu2Zez2ERYw7WiWdHFRg9rzkZJdbpkHZ\nN7GEepm1q/H8tkP6IJ1tPwsro3DygxXoAzn/bneP49yfEHGVbyP/f50RlKEZ\nUUGDW/PPNZgxG5wRme4JQCFEdbphYUAzlYl/vF6mKi08dDuLNXss2YXftakc\n/FbcR6CBEGvun/5Hk4opAj2FKXfkrD8oKQEd/oYtJlEJChjxkCgpalJcvb2z\ntMwXtnBW4rYfOryDUux86Bfur33ijiiKKdxKYBo7EDb+wqnXBHGFL6H3mk8d\n4NjrXmGfhl06janaWOvuYOF+S1wO7XjmkbI02LZXpVLrLxv0+po4fejdqBfZ\ny/SY+I6+rhhL58/wh8pDfNoCu9Gzbih75YYzcCgkIrulfaBFdrLsvDDMZHkl\n20/9\r\n=kcNw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engine": {"node": ">=6"}, "gitHead": "68dd3a18f04b819c25b48f523a861c81c17c82b8", "scripts": {"test": "nyc mocha test/*.js", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "6.4.1", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "10.10.0", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.0.1", "chai": "^4.2.0", "mocha": "^5.2.0", "standard": "^12.0.1", "coveralls": "^3.0.2", "standard-version": "^4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_11.0.0_1538866348083_0.0617863942875192", "host": "s3://npm-registry-packages"}}, "11.1.0": {"name": "yargs-parser", "version": "11.1.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@11.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "0b8104116367bf663089b24e5801438ab50396a3", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-11.1.0.tgz", "fileCount": 6, "integrity": "sha512-lGA5HsbjkpCfekDBHAhgE5OE8xEoqiUDylowr+BvhRCwG1xVYTsd8hx2CYC0NY4k9RIgJeybFTG2EZW4P2aN1w==", "signatures": [{"sig": "MEUCIGzilR3XQdwx06CqjQFQw1lhtUQUujEVmSasJY1J8IzqAiEAgSe5fV0WvHokNpod3OWFhCzj+Ksm0p/0Dnim57Qna1Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5iaMCRA9TVsSAnZWagAAvnAQAI+c3D1ikLTJyCrDhrKV\nKW+oJU96aDtkgwILS/3QIxs8Kbm1RubpkhZdPUiG0ZIfBFWKbv5wMFmpbmZT\n83dbPq0P7H98K6vWkjVwG5E8vyYVHVSizudfnfeYo3/aXmu+iy6cplqXpukC\njDTbzgXaP6l99xnI4k69RdPS1+/xMMDdpDxAlenixeV6aHjBWg88saUY6/Ao\nFhURySohjhN7DjTKQVUc6jf8ZYlE+Tv8qlZwlofTSo5n0/rdeL1iDOt6Q6yi\nhn7dJjLPJdslVdEOdsEcHHDpCvw/mBjHaO27yewDoxfdRZT1Zu9dSXmDg8LE\nVeOS2Fct2LKAo1Jv12GF+swJG4JyhrzQDWccmBQWcX9nF7Zq4Ol5Qug7fV5p\nXITcFG0gJiLFs3/R2HoQhC/cGHDOzGKLs6yV3jk2KDN0+g7Y8g67d/nxhN2V\npSTI7ZJmqyuDhugT5LSP6uXOa3EDMudGwhS5KlQ0gNSwGJmF1C844GMhBI7b\nOb4e3q6EJizDsKxz4+nzJvf4E64n6OkCXU7zXLX41/9qVumXpjDnT9m3nM7D\nwXAZq9iegN8+V3uusLBnZ3Y1YXMVxq379pHqPbDJ4g8O5hEpc5+St8MqGcUi\nSAjYit8f7uDDb9Xb0kA9XH+I3t3b0gFzB44ky/t8NTdMpN/8OSVkLfyEf5lK\nIzqV\r\n=9CHh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engine": {"node": ">=6"}, "gitHead": "1eb726b30e78f6f76c1ba66dbcb49a92cbc4b6ff", "scripts": {"test": "nyc mocha test/*.js", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "6.4.1", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.0.1", "chai": "^4.2.0", "mocha": "^5.2.0", "standard": "^12.0.1", "coveralls": "^3.0.2", "standard-version": "^4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_11.1.0_1541809804088_0.014179245602807145", "host": "s3://npm-registry-packages"}}, "11.1.1": {"name": "yargs-parser", "version": "11.1.1", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@11.1.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "879a0865973bca9f6bab5cbdf3b1c67ec7d3bcf4", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-11.1.1.tgz", "fileCount": 6, "integrity": "sha512-C6kB/WJDiaxONLJQnF8ccx9SEeoTTLek8RVbaOIsrAUS8VrBEXfmeSnCZxygc+XC2sNMBIwOOnfcxiynjHsVSQ==", "signatures": [{"sig": "MEYCIQCvR4sw9rsgTKy8a4B+gd2+vP6xdT5WyGIxqvhugCnbtwIhAJbePRp5qNOw5m+MwHiXJzIneDikdY1EwP7/fF0n2JCp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb80iACRA9TVsSAnZWagAAxs0QAIRQ2lljZeAf38/jR/2I\nRRkUHNxY9KOCKuprpUeTgpKEVd3XLNHhra9K7/HegBZJM1vVYv6PJGHBBNwa\neTDdbZqS5+f96xAg8IrzOeiFIdxB0YRYZKb2CWVhXmMiM/bkF8hZik7OyD9H\nFv+iOdnVTPaJkN2jfNgTV6HJPWz9nqjoRSXgiPCdBTP+F9aNedL1iuXkBkm+\nUesV4A6iV8tvEK/tR27hv6ddS+S9nOKiRDoxQPdpKb2CdGc5mibwvcGpaWYO\nJXuqbIJ6z11Y8vEX0+mMO8CcDqjeK9NhGupPyndNLpDThiykvvMsNXWWtsOI\nVcax/maPi1UOCCA++1q5Ln7x7vJIkvhfNrXWSgs0Swf/l19MIm6NfE7eSc/s\nqzq9WT/CqDTV04YD0fk657nundlY+CRKufKbBFgcHUBG8i7xXQMzz6VJl8hX\nkgL/kz9ihMJCCrWkkg3YzCwiuzwhvt4PXzFbHUlTyIgZKrNkN/UBxXWIqg5Z\nqrkIANPjzoFQTGcQR3zugRCMPudWmxiOgys5h+Z8D4VdSBr8ZuVNfVna6ckx\nNKPztRUckneWlS06+DDbgxtzi0YzKIt7S2l33RyAv4i1oQt8hAQ44yp0zLH1\nz8HgMP/Z8bHOG4UvemudHi6pY5vAxvmf/69x8BAp6ycGbRsIaZ0LzptLzbya\nvhlx\r\n=9XdU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engine": {"node": ">=6"}, "gitHead": "ee122f89ab9417241b06c6b3ce6aabb2ac8ce5f2", "scripts": {"test": "nyc mocha test/*.js", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "6.4.1", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "10.11.0", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.0.1", "chai": "^4.2.0", "mocha": "^5.2.0", "standard": "^12.0.1", "coveralls": "^3.0.2", "standard-version": "^4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_11.1.1_1542670464133_0.8836926394269116", "host": "s3://npm-registry-packages"}}, "12.0.0": {"name": "yargs-parser", "version": "12.0.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@12.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "18aa348854747dfe1002d01bd87d65df10d40a84", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-12.0.0.tgz", "fileCount": 6, "integrity": "sha512-WQM8GrbF5TKiACr7iE3I2ZBNC7qC9taKPMfjJaMD2LkOJQhIctASxKXdFAOPim/m47kgAQBVIaPlFjnRdkol7w==", "signatures": [{"sig": "MEUCIQCl1jAOdQ/9X5G4o1h9ZMigG/0+ftYVB7UYfFLxfZdS+AIgKoHVuEQyL2UdH9cDi2z4lv8GF/yFwaqScNAZkMQuvho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53259, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcT6MgCRA9TVsSAnZWagAAvVMP/iWJvhv782eOzAovDFCV\n+bNfRXFBzZxj+4TRV+gt3BIOkVXGEkJRsfKpHkJGUiS8CLdtfMrmvo38r0LB\nb+VANq1VMCK8VinReDHgzfGMr8FvGjnxgrZLDmU/qj+G7bOuF3hVUV73BEJw\nf7rt8aEmPcKMbSXESZxYJ5LUUjkklvxJKRYuwOwDkTNX1N6CqspFYpwUCMU1\nw2V0B0Lx5kwvra+HafCRRadooTj6AwO/Yu4NfCx9eMq2IEgM+HobVbgUCB2l\nTAMASc2K6fr1Cuj855MKTtUfZfy2EUIXOWLkOk752veFE/oB/On8u/9A2UqK\nxBr4ijb6NsiNPCH1NA9YpYnzGzgVhlXdTHzzg22E3pROLeyNC0g+0Ahyrk/d\ntIG/lJyGJd9hPrRRYk2cW/Gb5NijHAz+65jIY0/B2Y2p8IhjdTY3uRAM5QNt\ngVHVRGn02zC72GE/03eoz4sRM9n+0CKBhUPGlqAjfGE/uQZDa/TkqF2TbTHQ\nvx/AbZEjpDkwIJtfrTv4/wBwQihm0p12uUnLPjffKWkhyN4lzk//d+aeB0Ni\nHs97FsCwuYqIjA7w/DFfpp4wlXtLk0iDB4LAJzpE2TuroX17X5azTb+NRWFK\nPBRRcstdXi/iv3Us1qAuoiye2OoN0y9baxv/mTDbNIo83IpDhNFzpO2F9sv1\n0CYH\r\n=zn6L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engine": {"node": ">=6"}, "gitHead": "ea6ce0572b32797e965c620ee93a47a9a02201ee", "scripts": {"test": "nyc mocha test/*.js", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "6.5.0", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "11.8.0", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.0.1", "chai": "^4.2.0", "mocha": "^5.2.0", "standard": "^12.0.1", "coveralls": "^3.0.2", "standard-version": "^4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_12.0.0_1548722976357_0.9759821039563321", "host": "s3://npm-registry-packages"}}, "13.0.0-candidate.0": {"name": "yargs-parser", "version": "13.0.0-candidate.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@13.0.0-candidate.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "b7257bfab97e63007bb8e41a62aed9f509a5e64b", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-13.0.0-candidate.0.tgz", "fileCount": 6, "integrity": "sha512-TP9wKVkaqhvHIf2Y6UMGBZN1Ipm5XUJhy0Iz+GUGkFz/s+QFI9n19EvjGy8WxW325s5xL+yejdQ8BWLleTaPDA==", "signatures": [{"sig": "MEQCIHw875j4WgEyBygSe9RQVTFVmk6AEw2ibIHTUM9Gct5IAiBo+DCfr3VuqtxAsy/eKFY8PsJk6gTd2C2oc+CS+e1rGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53273, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcVgEBCRA9TVsSAnZWagAA/O0P/iaRrz318YkvSg5qJYsB\n0fydeD7YHE2xNHmR1CxdO3smZ8r8Y4jr5XE2Ko/Mn6WxtLFI9h4K7iDay69R\ny47LGjFJed9U6bCcyc1ROCdgerIiu+ndLaHQlGMyeE8OWBFsfo4y/M9LstGG\nmvACQX9KFsqAt9Dc7I9lLiF+U9NPTSppBf6iy/ymW5b/RBy5PkhyAzQjKKlN\nCy+RDVYQTc8m+B3kdT1YqN25f1EgHZ8AlrG0SwZpH5m4exq/+7efpwbujesi\ntAQaDccdiXwCB4tVegTMKKCZP5Bp8l/VD1mdk37PSSwnXxAmQlLHMXGHrRHC\n+93Rf2TRU/NzB5kyX1ijqXTCY/eBwJvO3MuUg61rYdojSEWEYr4mdw78nWvG\nTPGROAAz88LSuh9nfQP9Lagxb4M4WAzaodzcWgVdi05cgymaBCLPNu8PaRj3\n2wyprOXP6y+9Q0yMd8xVPJkZvydnwT3ubfHB0C+7hwWNJuCcGDEHMNQnbrjm\nVi2YS2x5WWMFQfRsSNAe04n9k/Vsy1WyRcJwvQQBzkgZTB6R6HaoBmD98My5\n78Lg+B8gRMpzyZCbYbD85BX8ia+K/WhdFxcCZBU2FC+i7VBAHLgAm1UlpkAZ\nrGEBBuOQDf3/9WPuAcEeQeEknee4T2oqTsUqCGDyMDC3cJek8hHJxrTnhCY7\nTB5b\r\n=1w9r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engine": {"node": ">=6"}, "gitHead": "3cb7ce250ee4be1de80b6d4cb51586bfeae54c96", "scripts": {"test": "nyc mocha test/*.js", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "6.6.0", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^13.0.1", "chai": "^4.2.0", "mocha": "^5.2.0", "standard": "^12.0.1", "coveralls": "^3.0.2", "standard-version": "^4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_13.0.0-candidate.0_1549140225376_0.7639099993697989", "host": "s3://npm-registry-packages"}}, "13.0.0": {"name": "yargs-parser", "version": "13.0.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@13.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "3fc44f3e76a8bdb1cc3602e860108602e5ccde8b", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-13.0.0.tgz", "fileCount": 6, "integrity": "sha512-w2LXjoL8oRdRQN+hOyppuXs+V/fVAYtpcrRxZuF7Kt/Oc+Jr2uAcVntaUTNT6w5ihoWfFDpNY8CPx1QskxZ/pw==", "signatures": [{"sig": "MEUCICF8oCuQaCTy7wWotlAEdpG/Jz1TTlFTONwjQlHHey7CAiEArPV9neK1C/CiTIGwUpzJmJSk2235aw3xcXcR7zSLZ40=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53893, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcVgwSCRA9TVsSAnZWagAAiNoP/2lufBp/ShQWvz8NVP7M\nyjTqle1y2mHeUU7i5ZP5VTSArHwJnr6iIQ8FnWKuTdvRAYMCyLCBbSkidl9L\ngUUrhYAo/QxlF2RC8EHwfNIUaJEmLaukYl8VNNOm2NO3NAnX+PR91koQ2WRL\n72fMQvrk+QJcFUMEzdxKcjTPjnXKxKP02N/oMYka0F9i5kEyLwyGTHrxXaa1\nEfPxP0qRzwu4R+ftkM8QVw0qpon7E5NACCxNpV2Jksvp5g1hwCVlJ8W46ZxP\nuJxUbB+ZELlPOlBy01OX14e2COeEEY+fKBQOtcdEFHGjTOGULCqhIeLZ6V6L\nY3ndKWj1yYIY1p22q/FSrvzqrAWrh6bwDVMy3bdoS6o/e8ueMyzOI7PRZ7RR\na8gmxH2F1zsSEZcktq8tOoRZCi9JFKCKkQ8dncotSfdq8wdcjZ2wFn/3C2MS\nnds9de3wW9k2EE/iu7hTncz6nmc0XDqR1qeK7cG73c9//bwn7oJktAKpFs0R\nOj3kYkzztBTvTexpZo4GuKtqv63ZzIVKWg67JnCmN6kMx1jcOeeZ2qGzXjXi\nwliK3w9MazW3aMc7kj+pD3BfqLJ3nLXlaeBQe5cG5jZ9/+bPFGLn9/XC4P3F\ng768Rn7AvY595UdTTgyrvnGIXFBuDgD3rPq/6Mca16wP/zepTwdgwYIfrZu7\nyst+\r\n=LhQA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engine": {"node": ">=6"}, "gitHead": "1404f79dd9bfda5fbab3d4474e3132e1bfff31a7", "scripts": {"test": "nyc mocha test/*.js", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "6.6.0", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "10.12.0", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^13.0.1", "chai": "^4.2.0", "mocha": "^5.2.0", "standard": "^12.0.1", "coveralls": "^3.0.2", "standard-version": "^4.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_13.0.0_1549143058131_0.7755253777302005", "host": "s3://npm-registry-packages"}}, "13.1.0": {"name": "yargs-parser", "version": "13.1.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@13.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "7016b6dd03e28e1418a510e258be4bff5a31138f", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-13.1.0.tgz", "fileCount": 6, "integrity": "sha512-Yq+32PrijHRri0vVKQEm+ys8mbqWjLiwQkMFNXEENutzLPP0bE4Lcd4iA3OQY5HF+GD3xXxf0MEHb8E4/SA3AA==", "signatures": [{"sig": "MEYCIQDiVoGJ2hSSnd8TYzD0E+aluff0l+UTQeOJZbHS7wTWggIhAKQoxaTZqPbNecTaHTo7lVNyUIZowFlstDOG/DICru9l", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55362, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcz1aRCRA9TVsSAnZWagAAtpMP/jACleJupqg3CZdCqSxG\n0jSvwBTvTIKZ0eHVRAH6KMN7rZcx2JLwgJ3Ac9qwZBNO4Fzvt+FEqPUuvX+6\nOsU/zRB//K7aGRuvESaZQPcyIFwMzo/6PwmhXvvwYnEzN5EmePLTkRP+H30w\nnFdTW1zC+JXEp32C2ggKE6PAqo+6j9A/+YSpcaMx2DHFuxxyhFOG1asrtCYj\n6OsXnShPUlCdT3/CyackvK+9eNEruWPBRjlgizz2gsC/63jVhzWtNNSo0TPT\nQYqgeXfuLgsLqChF6OxeZ91qsHJK6lkx5A6JeO2jOvYiZdPp3ZLAtc98Tg1G\nvW0NeiZCkPLNkhLwBl5E9DVDMwlLSjdTTY29KGNHgYkV18pPKwmeYxC1oBgG\n+pL/Za4qlv28cHx5JIznrFtOp6cps0BbM5NCI0UQqzetWQdw+uqAyXs6+6kV\nVJpJtvcSRArVS11NR7YnIOTd4V8sFudLOKvKxlFM69Eqp4pDMmpnPPU/d8yE\nTyIr8mvvAO6twabpxlI14zhSfmxCV9a8tSNWvQyGvBWHyYMKK33eBfMafrGu\n/BfoFugYTFgkR8joEjVe32uW8deJoMvk7ApdUZViM1x+uq/LPbTpjGioEx73\n1NSdoWFzrVqg0+pG8deBxMtE+6929Rgu4v3TncrbWFhBoL+JQCbKhV/79/Yi\nbIy0\r\n=P+yU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engine": {"node": ">=6"}, "gitHead": "69ddfedf03024ac0e9e45d0a18daa0964e4c1c34", "scripts": {"test": "nyc mocha test/*.js", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "6.7.0", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "11.15.0", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.0", "chai": "^4.2.0", "mocha": "^5.2.0", "standard": "^12.0.1", "coveralls": "^3.0.2", "standard-version": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_13.1.0_1557091985001_0.36882552076158537", "host": "s3://npm-registry-packages"}}, "13.1.1": {"name": "yargs-parser", "version": "13.1.1", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@13.1.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "d26058532aa06d365fe091f6a1fc06b2f7e5eca0", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-13.1.1.tgz", "fileCount": 6, "integrity": "sha512-oVAVsHz6uFrg3XQheFII8ESO2ssAf9luWuAd6Wexsu4F3OtIW0o8IribPXYrD4WC24LWtPrJlGy87y5udK+dxQ==", "signatures": [{"sig": "MEUCIQCSSkeBwoakIpRQtVVf3ilbm7sNhftXMCl3hIYUsW4BEwIgawzEEmdzPol7Nx5hK8gAqVcUWEzYPikMlymA08a4/Z4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc/a/HCRA9TVsSAnZWagAAT1MP/0Jd8mvdED68tv36sBfV\nAfRFtdrw+eU3tI0UFs+bWSFu70TDS3b78GL/Wu5RbMtRvH/9B0Y6kAKAnPQc\nAkOtAyQoEP3dwR63A829WMg0qvosxiip0IciIwpj5qx7gIijixJl72VDrfJl\n8+AiZym6RVl+nuV8Dl7a1WZJyy0XTGGELXZSWOHHEPzQY7BzNm4rU+V82BoZ\nkNaOfj2QWwWIxYacl3rYGULY6Me1Oik2oWRdfsj+JP5Nh03apJ+NW2snPPT5\nTEU8jZDTzMk7PXSABQaf4Py2gWPbxskCi9/zmHqhvFj8KYPL0ImsO4nXbUkt\n5exPB/c5XLWBm1fQfRdbKfkp2NFRXIIwYh4mwp5t+bwvUIbwqcFVleuIruWK\nFmyE4NjaKtaRuGj3vs/OyLWbhSFegozbMQ8tb22Ekusewp8XfGRcEgDwmG9H\nCvWbgPZqad1+I/L02Jo52oFA0L0CIuww9F9a5fcRIljLv0bZMmYZp7CQO/du\nplM0vuck4R0yAUrs3n/f8cccLj2BybHE+/zdKiNYpwU8SmTZc3lRFybBgV5w\nEcEukyWs8/XkBrPuKLjDLutUuONrmIjq7Y9dlaTMIlzTzV/4LdZ4+kpUFFSv\nTQfyZMBmpFonQ2TdXWWP9ztvc06MYHclZsFOXhBKg1lSzd1S2usPr2BE5wGY\nyqYU\r\n=m06t\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engine": {"node": ">=6"}, "gitHead": "7e01a2c8d1ba75d9d5472e5839e2852bdf2af3db", "scripts": {"test": "nyc mocha test/*.js", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "6.7.0", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "11.15.0", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.0", "chai": "^4.2.0", "mocha": "^5.2.0", "standard": "^12.0.1", "coveralls": "^3.0.2", "standard-version": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_13.1.1_1560129478957_0.3540378163143141", "host": "s3://npm-registry-packages"}}, "14.0.0": {"name": "yargs-parser", "version": "14.0.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@14.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "42e25777b06718ec99eac2c3a98ad3de73b6818f", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-14.0.0.tgz", "fileCount": 6, "integrity": "sha512-zn/Mnx+tbFjkCFUodEpjXckNS65NfpB5oyqOkDDEG/8uxlfLZJu2IoBLQFjukUkn9rBbGkVYNzrDh6qy4NUd3g==", "signatures": [{"sig": "MEUCIHcbqqbjoZvjQjIoxVF7h0skp0VULDqJK/YfKEXK5JmzAiEA+xI69rlWjud93zY2vn22jfbMeiv/MGiJVb43DnG/bmE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61025, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcrU9CRA9TVsSAnZWagAA1TwP/1wgn1MME3UlYTEiib45\naQ8ZJOJ/Yr1z9kcbiRKhUfWI+YaSPG6rNaxLuEzPH97K48f178emhp+biXV9\nPQb/Ssm+hr51Grw2EP1CcXLn4zLkXl1ypw8FMwzQwjH8iueFhC6DFXoXq3sb\nl9blWhOSTOAv+Ib+QxJCJtm5AwIldGNR5H3WfUoWdHiK55aHk02KN9+T64qn\nL5ju9dcRpoQSA4b4Xpfg4fd0wzWrhawx4z0v+jqdLa30uVhfRasXI9t5Q9Q/\n1hhqnnL7RV4M8fVZwgf33JrtIT7wNiVyMw9hh4xSN+Yz51tDXHFIGGgiZaHh\nMvyymjARsygqYtOACeoBUHBgoRijpIj22+FZO+DYSyP6urgPtUdYVH4qPA2t\nd8maE8FUd3zBu5qk1JKEcB3guybT6hzlSqmZVXjwlpes7+ORTY8IODvJh5Fd\nAfe772ALQD+uSpXpn5Pqr0DbIBU1J7pJu4aoZXAFarxSnGnny4H/G0vX84XO\nzX/d2QjQfsZHh8Q1pCTYnT8jDzlFnTxjVHSMgCKOM+5XPheBoOE3uEC6R7YY\nXMs3ClPCupT+6qVDKPZ+2H+JvgB+z83Fb6dGhqUmr2SavMfbRywHqDkrLSs/\nE4goE3uWIcqKDjvDU7ldjm8XAFOwoVyNkSJc16pZSMbSgPIbTm93AEi3JoM9\n1xFz\r\n=QjNO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engine": {"node": ">=6"}, "gitHead": "ac11361ab90204698499ca06e292fac47088971a", "scripts": {"test": "nyc mocha test/*.js", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "6.10.2", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "12.9.1", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.0", "chai": "^4.2.0", "mocha": "^5.2.0", "standard": "^12.0.1", "coveralls": "^3.0.2", "standard-version": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_14.0.0_1567798588617_0.6216679368978903", "host": "s3://npm-registry-packages"}}, "15.0.0": {"name": "yargs-parser", "version": "15.0.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@15.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "cdd7a97490ec836195f59f3f4dbe5ea9e8f75f08", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-15.0.0.tgz", "fileCount": 6, "integrity": "sha512-xLTUnCMc4JhxrPEPUYD5IBR1mWCK/aT6+RJ/K29JY2y1vD+FhtgKK0AXRWvI262q3QSffAQuTouFIKUuHX89wQ==", "signatures": [{"sig": "MEQCICeTwmfZDWd4aE30sUA3vANawzOPqNDpye17tJMsNHnHAiAn3ROW2jsTM556g3nXPxOT7B5aUpV0hDSCPLtpYXfhzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61655, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmoIvCRA9TVsSAnZWagAAKa0QAIRfC8WxIrBv0uw7HmAc\nWEVGuA4klCHcvc6PEHo8iLZ+psJW8eAJYUHGgGfzhKU9zh1jl2LHTPAwzcBw\nLHvK38lTNDcC4CGiBveHGb4PaXTLaTULefD11QkSUIwVkHstdcIF19cT8EKJ\nPzviBYLhj9thys3SPWXNJFoet7y4/eRfLUE1f8kYwd4fO8bV8JR6clz30X0N\nlbUrLlIEUi7x+yXv23cqL3ZyZWCSWmkoXeE1o0Y+kldU/6XdE2amDz4PjEah\nnvOmoVj+G1AL6xPqdzYbLd9kUdAnJNKbM5c1nc2SpSQhIQFuYGEJdeQ1HB9O\nBvVc0baNpTxiKOhQVclA6F5Eod2JmBH/NDKZ1APUXvQpGEAEVq6NdUxDb9uK\nFasMhOK7Al9nqdCldiw+8IIXYDCOgvF/x14isXWAri6Vsre2Y+T42WnisKou\n0Qpacbl08SAV+Hv5k0fvm06FdFZ7ZVoqwBK7sP4kIJhTUx0D6xIqAdYCP+N2\nxRdu0wwRBhEcMb//nUzIY2BCFp1HzTVfp9KMSV8BF49T7p93VcAeiellp8BF\nNmImrNfkJ29Sj+mF1eU9qGljT33up7p0i8kNfGj8meL9ZnQomP4YkR0NAmJf\newzn6B0yFjdNxhMnsghnxzL6Um49pd7sAPJ0MpENh+yolNokXZgdT77DeKIO\nbeu6\r\n=RciI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engine": {"node": ">=6"}, "gitHead": "eab0cb6511adcf8db9405307fc082493b4709bba", "scripts": {"test": "nyc mocha test/*.js", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "6.11.3", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "12.11.1", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"nyc": "^14.1.0", "chai": "^4.2.0", "mocha": "^5.2.0", "standard": "^12.0.1", "coveralls": "^3.0.2", "standard-version": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_15.0.0_1570406958777_0.6645281426519596", "host": "s3://npm-registry-packages"}}, "16.0.0": {"name": "yargs-parser", "version": "16.0.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@16.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "534baab6f1c1c0c80adc2f57d1bcd55117a56bb2", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-16.0.0.tgz", "fileCount": 6, "integrity": "sha512-WyNdFYED0penIaLqLSDSeFksC5lVjrjp9vNkG7IKfgQNiAYa6FfHuJXjuaQgt0eOVN2z+gJhHJSX7uRhJKPc7Q==", "signatures": [{"sig": "MEUCIA6a1o4cfi6nFOqFY+oHAYCnlRQ3rqRnKuabG8clQsxuAiEA2kRgOnX0l4nx1OwPegeWybaqoVqk9fMRsgk5sWJrDuA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63013, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdtQbhCRA9TVsSAnZWagAALzQP/RbK0iSt5A0GPbEh10X5\nrxD5J4vU5VFZURKjgIYJv3Ndl22FSnmJTXZ2hmchd1f7AwgrgFjfAJpI3VN6\nYCRwbD2XQCnF3Zpvx+8051lnAIngJflLDcQjdbMG9owd1SvZVOQ9B0q6oEMf\nbcPf65LKTQzUFblrTOGUPiYThnY/cjt7g/fXFDafsVs69vuhIQ/qieZUWd0R\nq0/Gy19hDhoJeErL5L1x53wCu9mEtGkkt9sBLxuNTBVYskQl3+JJvCeYb49Y\nz+sY88x+dGpRWbtXuieqymtUss1DYjZN0qPASWPBNKdSdiSbHQi48wgP4rpZ\n23CGfcA/rKSzn1jjF8u58Vxm59CPBKDJ8/GD58w8C9r9vt1ZTVCiAVrwQUS4\nWOYk2mp3Gjmhd+piHavE/AbZxK2uLkkqGlbA+biVWDXJI8t98WnNZGtD+QTP\n/1K5N6iZTE7IyxJAdM75hBAZkZ2BlV+xdc6KxHyiVK7vouBXUDokVCtMaOSb\nDfruoxGduqGWbMPdydQnM93izKYoe9YvMDEp6vN1GRflnIdFuNvScHo2QIp6\nioYQOJePA6xQk56Bx8al7ftn3Va5/wpK2W5Yyhb7H4YhhCIg3w/TlMNUYgWv\nZSFaVyc3M55dx0rlMxaZycDKbyJTNqr+1LBzt++4rrVLeh6WVEi8F5T+Qlyd\njjwG\r\n=qxzu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engine": {"node": ">=6"}, "gitHead": "212814fe5219d0033abffac90aecc908f2f399ec", "scripts": {"test": "c8 --reporter=text --reporter=html  mocha test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "6.4.1", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "8.16.2", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^6.0.0", "chai": "^4.2.0", "mocha": "^5.2.0", "standard": "^12.0.1", "coveralls": "^3.0.2", "standard-version": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_16.0.0_1572144865121_0.8044309101457034", "host": "s3://npm-registry-packages"}}, "16.1.0": {"name": "yargs-parser", "version": "16.1.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@16.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "73747d53ae187e7b8dbe333f95714c76ea00ecf1", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-16.1.0.tgz", "fileCount": 6, "integrity": "sha512-H/V41UNZQPkUMIT5h5hiwg4QKIY1RPvoBV4XcjUbRM8Bk2oKqqyZ0DIEbTFZB0XjbtSPG8SAa/0DxCQmiRgzKg==", "signatures": [{"sig": "MEUCICTQEiD8ZewKdAQiNiP6sahDLfzh9dX3FNZRV0xF9lCpAiEAt0xBwPQVAvv/bejfsG6Z+IneS2wH5iR3fNOrtcppjSk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63609, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdvLSBCRA9TVsSAnZWagAAg5YP/19x1Ih5H1Zups4O2rac\n03ir+PGK3jSUJf3YdFtBbJ7jMkTKkEYeduRSFUsA71jZzkZon2X1XGYznwt9\nobNPtPJl7reh1GcxFEatT4kR/0h/e4xORGs4KGu0ehmNcV0tMVD9LsopJtiY\nneB7uEV5AVLxK8Kf3B2+/shpH6W8KJMFjc3zvDPyiKS9E5+4PPhGw7T524d+\nfxsULi04vgwSGO07Lp898CBphstydV95CTJYsS8FBsEFHguTo3tNBHKa+Mfq\n6SRyhfdpJYeyRb3TmSBVRoA95VjC0TExWD16mv5VtFTYjaFT5TVgLEGEHTyi\npXTaAcOnW6FaG6uvnO+oeyH9lZvnH4vc0vmRXlzWEXtW+XRPckiQPZ+wbhn/\nBR1F05lFmRtYm6E8EOxI8KQHdkXxWpJwiJPq685U4PZmACKjg5+xk0edbIea\nvbrRk6xcmDB0mK6/X1dnrRjOZofdXQWWL1Xd1FRQ9nPHEnYRSgG4cwcKCJgk\n6IipYpbsCU0Vn41fkMT5S2+OF9UrtiTAzwdkuS/8QQoBJrElVpTlwdORiPEb\nvLe++PwTufdnR4FijVWrg6y/CuzTrC/2QMB2YVBVyFKtOVysSqgCCKO56QO1\nUXKRsv4ge/zPTOVJeWPS6gw7QHp1SfCjog2lBWWyxWlA3cWMfhI7qQpxItaT\nqRUa\r\n=BS8T\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engine": {"node": ">=6"}, "gitHead": "36d31b6666b634c056a04ab52b352b6077b30a56", "scripts": {"test": "c8 --reporter=text --reporter=html  mocha test/*.js", "release": "standard-version", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "6.7.0", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "11.15.0", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^6.0.0", "chai": "^4.2.0", "mocha": "^5.2.0", "standard": "^12.0.1", "coveralls": "^3.0.2", "standard-version": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_16.1.0_1572648065317_0.8458569388254156", "host": "s3://npm-registry-packages"}}, "17.0.0": {"name": "yargs-parser", "version": "17.0.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@17.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "cb2586e6fd445b17ced264dd4a38c60bdb35b7ec", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-17.0.0.tgz", "fileCount": 6, "integrity": "sha512-Fl4RBJThsWeJl3cRZeGuolcuH78/foVUAYIUpKn8rkCnjn23ilZvJyEZJjnlzoG/+EJKPb1RggD4xS/Jie2nxg==", "signatures": [{"sig": "MEUCIQCbaIgqxhvLbPfdNVpwA8IZwZKfqmbT4oQz0UktM8qE2AIgHoblEdcSVJR+ZrNxfFzCJ9f/4M0GSbp9bwE2/RJj9xY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQNNsCRA9TVsSAnZWagAAzpEP/05iTl6BWwrnRDAca10K\njmhTgj5pVnDjTmXDiVptN2V3+IOd4vSVVpdSp2jVKUWTb9Bp1Wd95Yq5CsLt\nfHTY0/wzsiVA6/Ma71TrJ66x4tQhY7L4s2w24fz8wiEHriKHxpO5rkMCvDXk\nJlaIchaV0/VBBlJ+jjXMX3eeAImBWKDzalPBEjHfKGKn5xlpsPg9oRmuxw20\nYHLU95RVCV5aP9KurMzFqzrEsoWuuTfq+qEU3tl6DmNhSFgvRX8DxXyjtxbK\niXI14oZc+tDcEuXT2Lk9h45Rnqsz59jHW/lc1pvxUJOfKUnE4pb63QbKrFZq\nJF+FVnH4nn4PGDtxumSmpvvZypaHsCVPaO5NegagkkG+rLfAod/cFGqIWMFL\nqjxG1qhsOtm+0SRqUGBOo0t6a1nToTew06zhKEOpKZJ0XTPodwe9saFvu9JQ\nMaAA2yRMg5PoEFdHx0gzFrxgGABUTqRrD+dFtwktjaZXwNRqUK3TggdaMFbZ\nRytJB5XXqO+SEsvi9jkgNhNL0SEkDl9INLDjYdFpKUmdTnmi18FWXMhFBZuc\nQC1QZbEcL0GuWmnYGw0nSMxqN7yXzT4YcrUhjawnF92oK36x5NszzPtylfuy\nDpazTGig3CMS2AXbgQE+oN6AARLzCTO+ja4Qhm7XCJ6tstq9+jeVJNB8iSp+\njcv8\r\n=vjhD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6"}, "gitHead": "485d2817ba77aa1a930964d03b8ea4ddb265b686", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=text --reporter=html  mocha test/*.js", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "6.13.1", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "13.3.0", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.0.1", "chai": "^4.2.0", "mocha": "^7.0.0", "standard": "^14.3.1", "coveralls": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_17.0.0_1581306731725_0.9270805280711054", "host": "s3://npm-registry-packages"}}, "17.0.1": {"name": "yargs-parser", "version": "17.0.1", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@17.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "e1c421e9cf4ea30ff159e2580f94ece3bc492392", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-17.0.1.tgz", "fileCount": 6, "integrity": "sha512-3hNhDUzFB66GVJus5AJ5ui32NyU5dRAhzvmCWzrcEO4Ijl92/+G3q8vBqMiOkGqNjygs4XcvNow0S/VtmaJwfA==", "signatures": [{"sig": "MEYCIQC/23U4U1fpwgG2R0KEn2m4mimTf4pm7SPHxg5sWyzOwwIhAJB7xue651kUa4E2HRWM3vkAZLAQRK5LFtuT1w4CTfC+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67157, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeWtGgCRA9TVsSAnZWagAAzpcP/2uETs/FxeHWt2hd6c6P\nYPS1aKBxffrV7pnUitS0U+BN3RsK4L31U/bWuEYE8e+fxdJl/CXKXQmDJ4Op\niLbdW2IFrCTqOGmSCk+JMJzKxX6toTvgm811/zbip/cmbBLz3hjMrTatCbMu\nTkSMLSiS1yGzVYuynjDgQ/hVpDFbBQZmOU4ynq9TlYlhR1q6Y8ooyNJewV2c\nrv/QIySeUtErQx4OzUQZQsOk5vZcspvzaAKgvZE+Yy8lTSoyD7vAyA4qw8iF\ncMibvJsCB8Ai/SB4ub6eXhMcpc9ZwaSOd1TQRuYgMQ0QWuJNoKBjmQJ+wRcA\nKq6NlXXI8kbsZAsIu42ObFir20w6yE2MKd6kCAVjWzNfr8/VMOoXrmxAlCcu\nAHisajOxaBwCGF/lxtumxtP/5S3ddgKSsqfHxhYxnHv8Rfdubsp7L8wR/jxj\nDBqIOjMGkm0szIiK5mhRUvPP5aFRrprSZVw2r56FAhAFVyCxKgRhbXgzl+lg\new9pTGf12MFw2RfvfnM2J96V7iEkpmry5twgmUVbHJcnuLPxRc1w/+Cwock2\nrOV0K7YZFF9nEHq2L5lb/Ry49DxnLN2hXd0eyl3qlHoIZxadDnHre05pNY9z\nz+Zz8A8ZPiboH0IoXa+ENnglmUFvHLDapUt8pvhS+S+8+en+RkEfI9WtGCdI\nlPg6\r\n=zUbT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6"}, "gitHead": "b0d65c62a6658b98f5c0947e95206b57dd56b808", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=text --reporter=html  mocha test/*.js", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "bcoe", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "12.15.0", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.0.1", "chai": "^4.2.0", "mocha": "^7.0.0", "standard": "^14.3.1", "coveralls": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_17.0.1_1583010207521_0.9968647433543869", "host": "s3://npm-registry-packages"}}, "17.1.0": {"name": "yargs-parser", "version": "17.1.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@17.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "b95ff3201e98b89e86070f92bef636016a0b0766", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-17.1.0.tgz", "fileCount": 6, "integrity": "sha512-67zLl4/kWtp9eyVuxX+fHZ2Ey4ySWh0awDJlk/EtT0vzspsXbzrFsh76WjYSP3L++zhSwHQRUE3MCBe754RuEg==", "signatures": [{"sig": "MEQCIB5YzM5nA3R6axty68WXBZshsyIp0ugB1xfi4JdQQfKGAiBWbjlexbvDAKD+kjbvP2wnVidVGVkI6oO04sRlqba8bQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67949, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeWxFuCRA9TVsSAnZWagAAtFYP/1jBUksH0hnNsVkX5QdE\n6hSW8AI5w+PFqegWmEh6vdcHWqQDNw5XrcmEP7EpniQ1NbN27fY6epUgwC6g\nPvuMI8obr9J6CSiipK07SJJtB/0d799uTQMIcdcfTcP0J9AlF8cs18enh+wq\nVU0pCK62V9+RERkN2etnjd4U9CAUqFFh8Zsr9wYZNeS9XKt4o01SNW8lv4Rz\nX0to719qZDMkn0t+mbrh8VAr18gH5pursUthwq4CeA5hrzd0T2sJWpxaez94\nCjJrDt29rxcMYbLD0S4nhmPlEuFw6aF8GpXbMjj4Ba17VwDFPjw0y3Gxk6hh\nNYz98numlkLFBpkm2Uoe582sYEySQIJca8S4MQvhTJIjrM+EmW0SUTt977QB\nvKQyMq9sHoJid+uqwb2JgsgBg8JkuAxELAaB4/8+MbGCUx7F8sFKLf6iZtYq\ndS/xqpA9hqWRqyCqLk3aQdLgosmxI+6vYZ+ViHXjmfwh4q7WqP+8S+Oo8ESw\nBTx2cQA0xJtKx9/cc/YoY6m99S0UC4Ae4wFOwsYr8bsTxQDDsmsSUljG563n\nhbt/4YA+yfIlUt1eY1QRiXlMDW7gpJ3ej4/bZK4dN9w4J29wcnbvDz6S2Dnw\nqAYHfZ2bCXqRHZ0MCbbpkT2yhaEi4HOvkut5sop4ZzBSgvqlB71xf4rUw3j2\nwbW5\r\n=CU/4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6"}, "scripts": {"fix": "standard --fix", "test": "c8 --reporter=text --reporter=html  mocha test/*.js", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "10.18.1", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.0.1", "chai": "^4.2.0", "mocha": "^7.0.0", "standard": "^14.3.1", "coveralls": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_17.1.0_1583026541794_0.7428620499821355", "host": "s3://npm-registry-packages"}}, "18.0.0": {"name": "yargs-parser", "version": "18.0.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@18.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "cb32a77e173f7ab758fe32b8a498b16433fc977e", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-18.0.0.tgz", "fileCount": 6, "integrity": "sha512-dEcTjO1rkFcERdsoh++v+o7PQ6+POeIS4lOHr5Xy3WuYWhU4+inNSpJSvnsAEd3TOgzytS4DrueTuswP3VAmWQ==", "signatures": [{"sig": "MEUCIQCXebN0KnIZMDDRBrn+xHQLXk/ivAf5cdLRpeXZ1ATelAIgQuyPLQWt8GpxV9i3b29amd8di7+5FyIr8PZ5lnD3y0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68857, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeXKFPCRA9TVsSAnZWagAAc08QAJkD0uA2RrfB5H9GN3Er\n9tF5iKyF9caYV4JegkhD1J+swIQ+clzjmkigAFmuA8Tm4g6wan1mDInxUa9T\nRo7JfbCqjdqIAJvUo5xLOdzeCTTuYedMTKpbTeYyyCoTp/xZbGHeFLSR9AXx\nLxFrzS5+RLpWjhaJdYYPYQLI5rxjoXcZNEcxUJegcU6n5Go9BeM0C9OqFbGg\npTgEEW2cfhZEbzZhDLXpmdC3JnMX6LJC9fEQS1+mEvlwZ9B/lZHJZvefgCEp\nxw8nLjFUplzlNI+kuj5RDx+xPxgphAhivbm3tnnbDC7YsS/uPFendjUXkRTI\nr+Ogv/Ptwy5blLrB7Bmgq92EzxbgHUn1HbZQpMYR11sUUljV26WTnPlJvDLM\n3QIdqSFmpa3s42USaH1H6pXYx1HYgtJJKhZNSIMgApe/lFwQQ73CBJK1x79t\nvJmwTxIB8CGw98f9ZMRDrcrE9P7ocJB8d5op4TWW/QEfFDiKoCIeK4Ihm8xw\nbt75GDPruTbNpyzu6Lfumo8PP3ffxlauJpGcIJHwcwJfpcZTnIMaFM9hz/+s\n5xTKImbvxcNE0LZd+B26Y/cRKU1lsOAsxaLmGCByMlEPH+cePvdxvZGxbra2\nkC3uX8Cxe76hNzqL5/CZWq9Wgk7amsQxyKUJDID8n5V37HXAnHy/8064y/lH\ndZYw\r\n=LGTd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6"}, "scripts": {"fix": "standard --fix", "test": "c8 --reporter=text --reporter=html  mocha test/*.js", "coverage": "c8 report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "10.18.1", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.0.1", "chai": "^4.2.0", "mocha": "^7.0.0", "standard": "^14.3.1", "coveralls": "^3.0.2"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_18.0.0_1583128910795_0.8276999530541411", "host": "s3://npm-registry-packages"}}, "18.1.0": {"name": "yargs-parser", "version": "18.1.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@18.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "1b0ab1118ebd41f68bb30e729f4c83df36ae84c3", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-18.1.0.tgz", "fileCount": 6, "integrity": "sha512-o/Jr6JBOv6Yx3pL+5naWSoIA2jJ+ZkMYQG/ie9qFbukBe4uzmBatlXFOiu/tNKRWEtyf+n5w7jc/O16ufqOTdQ==", "signatures": [{"sig": "MEUCIQDPQ4pHsGJpLqJA+SQAKQ/5eEdBL9pBzhpKBVp/L5tfJwIgCR8/yfL0jBtTY658eZAZfrNNteswMGtZWs/Eo3Rvv+c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeY/lICRA9TVsSAnZWagAACHgP/iDvyXN/xouC2z1gAOai\nRZuhH+Rzjr7fHrRRLIrbEGC1ulE3SGeOvXLfM/KvDUxB7uw1bl3MGUUaUAjh\nw6fDk0JvHz8vitr4+GhVyCImxjZsl9VTz4zN8jo4k66M+Dsbrm3LW7fMGRXJ\nSdgm2m/ZB8vEzHV10P91KFDARxSOtWrKs7zYW4h3NB19lUo20WyF+BF79sai\n9OdV4XokSz69oa9pjLi6EYGnFY5vLZR9IQd5P075lTUzJ4KzYiPXYc4e7W3S\n0ipYF3UZ+T0XOLfG1AiBG/3GSA6PuUzPRpwzn/xPyGm1/48WpXXvbi1NwGBB\nRPoHhMnB7pLLI+eh5akHYkxeO4XU6R/P1qibJ+jlBTyFVWXE0g5Yep3dfmwp\n5fSkpY+tI0JoXCRptys1t+DtpkmLiyWz0tCsqGaUwyQ99xQVMCXZgzJoeeGs\nhPkgkiK16JIys59aQzs00DCb0BpSukHE1/7igNBA4zIJGqGAoOZRE+x8eKLv\nBNeZUdYIFpiqZ0xoRm6gYe1QIc9VXHdeDnn6oIsgzufNG5SuWFTT3/7U0XLc\n8J4zHRDOlMupt2A9GraQWmpSeCVbHh5RHcyEdH4k0nJ4yw/U0vn49Adg3e8G\nwoDNj7vYlG6/ZqC9qRxl8Cb9Yge+JjmYilk6/qbgXeT0iZSBQmkA+qHxrfLq\nseWY\r\n=cabl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6"}, "scripts": {"fix": "standard --fix", "test": "c8 --reporter=text --reporter=html  mocha test/*.js", "coverage": "c8 report --check-coverage check-coverage --lines=100 --branches=97 --statements=100", "posttest": "standard"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "10.18.1", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.0.1", "chai": "^4.2.0", "mocha": "^7.0.0", "standard": "^14.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_18.1.0_1583610184250_0.37246233287754427", "host": "s3://npm-registry-packages"}}, "18.1.1-beta.0": {"name": "yargs-parser", "version": "18.1.1-beta.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@18.1.1-beta.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "fd67039f77ffbd4bb1c928272d7138cd9282c52f", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-18.1.1-beta.0.tgz", "fileCount": 6, "integrity": "sha512-Kug96Ox8+Dz+q4wylPkw/Ry7ACvU9Dc4Pa2NR3GNNWGMji7HEOq9V4l2tWSXOL9wHgD5iYpu81yS+y/B2t/IOA==", "signatures": [{"sig": "MEQCIE+0ap7Cq3eTcBfZ9yGJbjJanb1IaGpnqZMcJ0C24q/0AiBB9Jtd/7vXfoW/RwqqjNb1NGQBpVKbsTYp9RkdyJYk8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69667, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJean1cCRA9TVsSAnZWagAAV3IP/A7mKRtUndmybKWK1KlO\nN34DWGSQg69MZpI1K7OETZExFzOtl0/o4NMavsWiB5EHt9y07yoWZtJgUz7L\n3yTjlrmNghpTiEOtwgpn02aFLv9nnp9C60GGDYpPfKttT/+E7+gDuNGDpBIJ\nPt0J9o1ZTDHom599tl/fqbOxul8COYtW5dDLNQSmqkS1+ekPzFnE2PolUIDI\n9Ufz3/v15WXYvcLrYQ3flHZCdh0QY6keTmT0cwYv297EhSAGNBgUEOyQZ+ui\nuWUew3Yoi88IOyrbwi6+IPI19xp4lYFYUYt27hNNJ5u7c944J9yoGWR6am0Q\nIuovhb92sikpgFwdAcLOQrVGDWBrlwBWZD6nbT5Ns7U5gENvaDV1M5FBtv4J\nfT4hR7P22MvcjCBWkM/V5UqYKxbCRGNhfWtorXZkcJYHPDoJ1uNK0BBc7Udr\nmjX7pPNB+UEwMMEfvdnahQqVbxQczDNDS/bCE6B8ZOFXAtyaxewHTFH27x+F\nVIhfhvCM8rhfDNBNLHDCr7suWOzr8nJy10sxhCM/nvxG27ZfXroruSm0IPyA\n5rWf4txywp1Hi6t5Wg72c5ElFJSrpBNxf2bRTMBfeJKnf76rfFo4NsrUGyyn\nDPV0GlT2zR2muH4KhRUehgTPFYUdcbtHGyeKQ49Bj0TcvCh/JrYMss82YS3a\nbOae\r\n=yF9Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6"}, "gitHead": "366a9ae6a1ca4ebe0a90a55028e35d45247a8240", "scripts": {"fix": "standard --fix", "test": "c8 --reporter=text --reporter=html  mocha test/*.js", "coverage": "c8 report --check-coverage check-coverage --lines=100 --branches=97 --statements=100", "posttest": "standard"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.13.7", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "13.10.1", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "^7.0.1", "chai": "^4.2.0", "mocha": "^7.0.0", "standard": "^14.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_18.1.1-beta.0_1584037211673_0.6470280159620365", "host": "s3://npm-registry-packages"}}, "15.0.1": {"name": "yargs-parser", "version": "15.0.1", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@15.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "54786af40b820dcb2fb8025b11b4d659d76323b3", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-15.0.1.tgz", "fileCount": 6, "integrity": "sha512-0OAMV2mAZQrs3FkNpDQcBk1x5HXb8X4twADss4S0Iuk+2dGnLOE/fRHrsYm542GduMveyA77OF4wrNJuanRCWw==", "signatures": [{"sig": "MEYCIQDsSxjUqIOSgcS7RDaXhnAqZ8RyuREQgGq8P2+ZEhJqFQIhAP6rxVXOQLFMyNK1O47IIxGmhyY3Q6yF1GK54eotShik", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJea/ORCRA9TVsSAnZWagAAyS4P/RbWL4llEOLVA34e8Nex\n5Lx+TLTrdMq5CoRh1OI8+nQvsUEQZZdipBTSs9ar0WPKRhPK9cr5nyg9afhj\ny3vsbDMbgyi5y8GzZCeHDGj2CfiJjRjQH/fDDib2GnsTi24w2prqKPvgbWdT\nIiqwkM6WeqUobsamNCvAYhid2zi8496KNDnzl5Ol2Wj413BQZtI/e2fRnA3g\npEKUTo6WUS+/iP5KyE8QevChxYGDSvrf67liVVpdJn6komrYh0F1I5DXiZvX\nK9SNExBF9FBNsog7PLYjiB8Ck4+YXBbbNBiT0H52bU4Wbg+GNss5FmwPPNXq\n0IsVT/eOiVs7jkdPxWnm2Nhzu4pA1/FFdTGWlBNQyUab/yEtSagmQB6o8EnF\nlYM9Ai4/O8kpHuB6Nn+9NTOE7vLu3Ye72TQ5tS2uhZeVjeAlMgWRFpHvfHTv\nTlFN4iZ53/Q/r4Sz29y41KWcULc1UBsWFZ07zrbZPlgvLPkre42KkoPIJju1\nRAxCElSsSpJUXLztYnoAI9nrCafRGS2kdmb0j5icRGC8Le3xAXBsiYuK59dY\n6cJ0xTSRFdsmt2mQANAEK2+yCrw9vIRYMHvcIH7SQRNOvZJkl8DbxXoCGJzE\nj8jwgUmnAyQylyIWqTKCjPhIDQlAC+JkOI6CmMbHRfglWo3oJL/GIGGJcVXt\nwvvV\r\n=+PNq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engine": {"node": ">=6"}, "gitHead": "c893d3072f7d31243b750b1d599b0826b8aaefa4", "scripts": {"test": "nyc mocha test/*.js", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "6.13.7", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "13.10.1", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^14.1.0", "chai": "^4.2.0", "mocha": "^5.2.0", "standard": "^12.0.1", "coveralls": "^3.0.2", "standard-version": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_15.0.1_1584133008538_0.9364694488660432", "host": "s3://npm-registry-packages"}}, "13.1.2": {"name": "yargs-parser", "version": "13.1.2", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@13.1.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "130f09702ebaeef2650d54ce6e3e5706f7a4fb38", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-13.1.2.tgz", "fileCount": 6, "integrity": "sha512-3lbsNRf/j+A4QuSZfDRA7HRSfWrzO0YjqTJd5kjAq37Zep1CEgaYmrH9Q3GwPiB9cHyd1Y1UwggGhJGoxipbzg==", "signatures": [{"sig": "MEQCIBQVahl55NiPtJF7TQIB8I8AXti2n0xtDX05weM1QD7CAiBWXefutQcqy5WPUOTomqCNYvHyAVfTVu4ssOoiZ2/E0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57117, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJea/k/CRA9TVsSAnZWagAA4zUQAJh3qle21SQOUNQZEYfN\ngJNea6A76tVUVy/gCj9XxDPAhrgP2QQUp6rUntdllM5QtynKnVdgP55Vew8D\nIxRPnz0xw8SqSajj1By8vyxwqOVzWHsp8G/pjYVdNUuXMjFEweM8B5hnGMM0\njZ5EJ8iSF7BktAWx7ODX8E5rvFItQ9tuIzlUgIoaTt9+sj9jdDyPnqcDV2TR\nWDMt9PdkowsPMiYIdjH9IYgOOYp+7b9qgfL4kxelqBPKooxzCQcy/sLEs6tb\nT9NsY604Qy6KEYinWG4Q73Nk8kSmgCpy2HTZHYTwV9DhnZNDI+7ILw3YA3Y5\nfK0UTSypwS0oMiRmY1K8gs+MO8A7eBADgmLrxdrxexxWMip8IvoQVnd8ETqQ\nxe1HpW0o1QsTzd1sVkfaiqPvX+kRb370q+jIP6qrYh89N3n5T9T8N717frzu\n45ifMyuwdT87kaLrgjvGnPgcgLdG/Y0XWoWDymj1vUGDXjjzoldunbmRv7kI\nZAyoLCWQsNWTtAIVzh/ruBji/1UjE9+5h3vpD9nV1uXwyrYcgShu7Wy+LKf6\nqnn2QZPLPplRhrQgVEd+nzkVl+ht4zVHZPEuxcTpKBQCAwM3ymmwtdzPh9Jk\nSWarVzF3ioXRvVuJl8aGrs0pO3NkNbojaWKEZHT2wRiGFTCNgJAoqOx6WzP+\nP9pR\r\n=uy9Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "_from": ".", "engine": {"node": ">=6"}, "_shasum": "130f09702ebaeef2650d54ce6e3e5706f7a4fb38", "gitHead": "034e7c0ebf1047a866eb84b529aeea9216669d4a", "scripts": {"test": "nyc mocha test/*.js", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "3.10.10", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "6.17.1", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^14.1.0", "chai": "^4.2.0", "mocha": "^5.2.0", "standard": "^12.0.1", "coveralls": "^3.0.2", "standard-version": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_13.1.2_1584134462747_0.9801618541798658", "host": "s3://npm-registry-packages"}}, "18.1.1": {"name": "yargs-parser", "version": "18.1.1", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@18.1.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "bf7407b915427fc760fcbbccc6c82b4f0ffcbd37", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-18.1.1.tgz", "fileCount": 6, "integrity": "sha512-KRHEsOM16IX7XuLnMOqImcPNbLVXMNHYAoFc3BKR8Ortl5gzDbtXvvEoGx9imk5E+X1VeNKNlcHr8B8vi+7ipA==", "signatures": [{"sig": "MEUCICOTSPzJOeXHC7yzS/QueILCFx+4a1424QQ4VPNCP/LAAiEA6Ee9cfBKI9Husi60hr2digpNVx68STThlN/9NZPPkiU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJebyiVCRA9TVsSAnZWagAAW2IP/0e9lW4D4m+FGqxIJ1Tm\nfwMDQMeTdDrjVbPFl9jgmUpTzbCMWFU4WeJDXN/LknxlMSRCS7LFWkV/Ur/y\n4XTAgHNI6dZrtu4eEjMQoK7V/onywG8hJ0l0pnCGBZpHvja1x/rqre0Yy0kB\niHxtvxITL1W61dora//q6yNpNvipeaSvUvxY2mY/rFHwKV0LBOfqplna4/nH\nEyqQ68ePLCDUOtHAFPzOUvv0OGYKz7kLJR43TXhkCqfrDJ4jnIWopsS98rxF\nRTZtmtA7a2F9D+Ybdua0CwGs2ik9rOpin2DWin6aUf1ymmIZNK3BigGDj93C\nPwSqQyS1U0OXOklIL7VQANV4gzsCrJvzySE/jydS/N9Qq/CSf3SRXm3ng60s\nZMyzkR5UzepyzfaKJlA3jKElxT5IvSpmBnjYXRLam2Sc9+NQM0DPfQP7ygMM\nyI5zz2qZvgAHjiv9w6izxX+XrbluvaZSZfUe/i275ATps8VANtkG4jP+94Ui\nbTMYnXIAQebD+tzV2AbQC61fUIu4VTqMCq4XuF98MQou8CpyN/kOJTjCqXDg\n/zrhMHXSsfOScsI0bN764axW3Y2s+ddqcNDUFmnLYvmTMOj2wni2Mm5JLnpA\nA2z9/aresU0J2xiBHArlEHpv7Hg/RF88PmopThvTH2HuvPBPqREJy29l6z+Y\nDaOO\r\n=dQaY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6"}, "scripts": {"fix": "standard --fix", "test": "c8 --reporter=text --reporter=html  mocha test/*.js", "coverage": "c8 report --check-coverage check-coverage --lines=100 --branches=97 --statements=100", "posttest": "standard"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "10.18.1", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.0.1", "chai": "^4.2.0", "mocha": "^7.0.0", "standard": "^14.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_18.1.1_1584343188407_0.9606261345395488", "host": "s3://npm-registry-packages"}}, "18.1.2": {"name": "yargs-parser", "version": "18.1.2", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@18.1.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "2f482bea2136dbde0861683abea7756d30b504f1", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-18.1.2.tgz", "fileCount": 6, "integrity": "sha512-hlIPNR3IzC1YuL1c2UwwDKpXlNFBqD1Fswwh1khz5+d8Cq/8yc/Mn0i+rQXduu8hcrFKvO7Eryk+09NecTQAAQ==", "signatures": [{"sig": "MEUCIE517NvM94AgHRIvdpsxuCg9f+DnpPv0NPEP/lerKLkLAiEA8hdXH1MmnM+/Jhqfbn8jFPZn596MvOJHd26hMoxFeWc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70943, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefOL0CRA9TVsSAnZWagAAqA4QAKJgmXjM6Hi9AcNa/RgN\nx5zeTM8wkUgWV2k+re+p4luavBl+XtartXnNkOv63rh3f4rpRSpr4MWcrdFm\nV6T515WpNHdk56JCTHp0iqyuB+kpI3kGakKTM7TK6lSJiEoZlhy1XJ4D1cjF\nZvmH+3oSNoprNicBVlVBVwwKoymm4kMDCULsE3rULgHWLCwHEsO1zdDPu7bi\nGl0UYkCPIflnZUaxN9zhC4ykPuTECUlnhj56r/dPkIz1SOBab2Tsc9qO/W9b\nZxJYxFnygz6ZkxTdED7YveoFe3lZnKAGRqdjM698LcguNLP+qcALn7a9xi1f\nf1NRLWGJJ/cel0Os2YCRQqqKpG3PMLJcB8OLJahMumrx86RpZFqjRCw2BIzK\nLdj+jmMBfF7u6s37amdd9BTgIW3++71hVpq1b4VYaCnaSugxgwHJz672cxmC\nM77Haymct1cp5JUefjgNyQWj/NdNbB3pr18z1LvQvba+uP/SYpsZf1v/CwQ+\nEyx7H4Gkr2DjdIAZDx78T1kg86kmoROIzYmFzPhVhZRgIz2jokk5aEeaDnzq\nwXadohDHzGoUPTgHKAkTHD0Ts0UFCXsY12VfxH3HYXjjfJnEWYK7hmmpcOGV\n4j54O+opmKrvpiz+b04gDlhXXeKO5TPw18afKya4ETm3oMd35dcNgSb5QrnC\nPLDW\r\n=IKdL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6"}, "scripts": {"fix": "standard --fix", "test": "c8 --reporter=text --reporter=html  mocha test/*.js", "coverage": "c8 report --check-coverage check-coverage --lines=100 --branches=97 --statements=100", "posttest": "standard"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "10.19.0", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.0.1", "chai": "^4.2.0", "mocha": "^7.0.0", "standard": "^14.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_18.1.2_1585242868059_0.9371700418010991", "host": "s3://npm-registry-packages"}}, "18.1.3": {"name": "yargs-parser", "version": "18.1.3", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@18.1.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "be68c4975c6b2abf469236b0c870362fab09a7b0", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-18.1.3.tgz", "fileCount": 6, "integrity": "sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ==", "signatures": [{"sig": "MEQCIE3TX4aYklJ3eu0lTOvNyYQ7eH9k/ATQNLbe6CebxSjhAiBkc2NSzUAVUzcd4ODEfrzOgpwuNFeZwMhWEEKbP9K3sw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJemLxeCRA9TVsSAnZWagAAT1gP/0A7aL9K40jHeMWV2Vn/\nFQDWGlY48+bHPahfORZND1moiouemlkEzHR23f2T5yuW6x49Zb+lQnRXbfdH\nOsSod2RjtG2lqvJ6ULGjGad7cEn9AaIkb9u3/x2u03oLHGVT/6wpP4HIsZJ7\nnI085HPN4hVksM1qqnU/cjHvzul9Ul4VLwQhb8DH/ofCpbohPKP7icND0jYr\nQHIy5Vec3EUKjG7+B3jD2NVewtb/HmsyiN24wbjaLqvaJ9McaeYd+oe2VIOn\n0DdDkMwqukkJc7vVzhMk8wOGSQGak6OOWstk0firuqaPNFrdG5/kCeYfUOON\nQzxjK0TzOxZRUutnOp0QiTaWan2ChVPaTGR7ssLMOL+7vep9CIoeZlm8539y\n+kuEPVpe+4GJBc5TCObrBLU7bVDx7ozvGI7MpXQRY3czloQcO5lbmR1r749p\nNmzvAt2ErfIiV1E5eIo8Q+5HVBKbTViUPtBx0rKNYhd98XbNvfPsB2GKMgyI\nB2fHV8dig9t5gUY1Y8Q8mCV5vd7EWjd+OyB1Gfja4bvL8fuP327NCInWh9+c\n1F0glN/3wsDYhV/m0/Z0fptQ05IOeM5TNGCS496osap9sZkrlU4XX9rBDmeh\nIsogwKlrixNPax+W+y7Wznn7UUB7+63mDJ+Fjv/1FuxXb+l0oe9bbNvLvlMS\nR9En\r\n=DwLX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engines": {"node": ">=6"}, "scripts": {"fix": "standard --fix", "test": "c8 --reporter=text --reporter=html  mocha test/*.js", "coverage": "c8 report --check-coverage check-coverage --lines=100 --branches=97 --statements=100", "posttest": "standard"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "10.19.0", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.0.1", "chai": "^4.2.0", "mocha": "^7.0.0", "standard": "^14.3.1"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_18.1.3_1587067998257_0.4259469980804451", "host": "s3://npm-registry-packages"}}, "5.0.0-security.0": {"name": "yargs-parser", "version": "5.0.0-security.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@5.0.0-security.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "4ff7271d25f90ac15643b86076a2ab499ec9ee24", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-5.0.0-security.0.tgz", "fileCount": 6, "integrity": "sha512-T69y4Ps64LNesYxeYGYPvfoMTt/7y1XtfpIslUeK4um+9Hu7hlGoRtaDLvdXb7+/tfq4opVa2HRY5xGip022rQ==", "signatures": [{"sig": "MEUCIQDoqbBJW5IjqfXT+EGZBEwSNZ9oxsWFFpwGFhnIYrRzVwIgXRIRWK8vcmNxc/R5mxGI/vPLtRuodLXviIu3bhyAQ+8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38215, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJexx2KCRA9TVsSAnZWagAAijUP/iE3WyvzwErbjkCGZUzI\nuYdGgViAKxTI6rG8Ix7IelKtZ2Alai+UcDsqwxNcnBb9N9lMZ99lQgXdRz9w\npJ8Ba/t+IZ8H72zoQ+as8YqopWD+5GD6Ot7ea+ZERDv6SnKjALfUumE945um\nbNRbU5xd0dG+GlW23mPsu4kNYajlJ1gtoF5X08OC1+tooMSRv0xk3T5V00hE\nDOMNewiyEWGrYK5vk6Y6lPhCIHfBvztlDkhMvN1AregQcUA5MLptKQd6JFtL\nvJ/GxB/pj3K0bE6AoTI4p9XAUfZaRaDMm3XP4Dy4dJiegcd8JrZ65+a6jA3y\nGogxiAWzmIKZxJpi42c1xgtvFB7XyFODqKudqG0wHO+zyP55qG/5JdCvN924\n0zh1tF2w6zkYjICW6WchNTsdxxrN/dbrNYhY7cuQcSSzr26MVDdXJIt7423L\nLKq1qD2eJmQxqPM62c2fKOYKFzJPvzEMA6TTOvcP6POcGFEV+46+jIrFf0y7\n15nwsBoGQNwXSbiTLQ/PH8IMwNvlKzSEVKfJ9//GdfIqMPeMMQfZu1i7FsMl\nGb/m0k5qVd7hsTnh8dF0pT69pRouKK9YGyRUBnLXQtu/cgRHuA1dyJHw5cr1\nCQLYe1Ep6rR00pLIIdVC/zvz5zzKAUysy5ypu/a3lQI5hA35hu8AV9CHo+CY\nKEqE\r\n=TMAU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "47742078426f0e4e02aa988062b5fb0fa61182b9", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "6.14.4", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"camelcase": "^3.0.0", "object.assign": "^4.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^8.0.0", "coveralls": "^2.11.12", "standard-version": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_5.0.0-security.0_1590107529717_0.46046677875730246", "host": "s3://npm-registry-packages"}}, "19.0.0-beta.0": {"name": "yargs-parser", "version": "19.0.0-beta.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@19.0.0-beta.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "88826b3e6277ae168dc8525f2adac6cc3ef3a249", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-19.0.0-beta.0.tgz", "fileCount": 12, "integrity": "sha512-j+w4xWZ3RL5rJWiWV5xUafx8XxMkyL9Cg7ZG9ZhU7xekrtlKYSh3Esq+owLA0mNdVov1QF29dJZ8RIrRyPV1/Q==", "signatures": [{"sig": "MEUCIQDUNtsMLGXM1JiLc67xjyYsbh9PHoaGUU6Ja691uDzthgIgPKR6Z7isEnMTd80ilGVau1Pr8ESsR4z4PadtYbfUIA4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89788, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfE40zCRA9TVsSAnZWagAAUysQAJG7BYtROeMYVpjGoxkQ\nhCqhqz3BmUIGKgdtSkwnK5/x9bNZ7uQP6SuGe6EhCDGuYu+U1UVHUX/+u+B7\ntPU8GtgsJTpOHUxoahv/Afvdz5Cjv3Rezwa9GxY5dQCm4nudiWQLAAHQpdyb\nY2Be85gUlk4RLJg7uFtTCbEcDTvUfqbHdOr+rSKVnmtAFI6mxI4AD5uQpT7P\nWiG/18feH4sS9y4VTVoWqc7IqrHDezBBIwd6VbqQ9WmvSN1VCFdjrO/ht+Bq\n64EK5qKL70fxulrk2Gjqj2oUJhhm/fERHo5hcx1rcUOMFmg8MpnQHSlys4D8\nVxbeBwLaJqzZDv/mPuWmph/zQGX/y5n5SXOJDVlkuZJi3bt97Z7llms6iIEQ\nvtsnwLdks2PEcxq46EkhqMv2zNeA3ILyauvzdZi5DQem8LhrcnroBgS8C05f\nfd1RlIyeQLrU+igKYyKU2bMIQHh7s89f3RuAwLHCSAuF4gxKjOyHeLYUgXVu\najVAWC5B9fPO0tQpzct/1yJHD1zMzoO0Kssah8KI0fJo+Ykr8J9IUurV2ao8\ncMYrB7F6dexkC6MwPA7STUWI7puopu6RTkoYmzG1NmkdBuo77EpNB799+Arl\nfpE4RcRmVPXSYrdthIub7Cmm71ACwTSn0l+Rzq/bcykcpVCX3Y0H5zYDPJZl\nPm6z\r\n=ePof\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">=10"}, "scripts": {"fix": "standardx --fix ./*.ts && standardx --fix **/*.ts", "test": "c8 --reporter=text --reporter=html mocha test/*.js", "check": "standardx ./*.ts && standardx **/*.ts", "compile": "rimraf build && tsc", "prepare": "npm run compile", "pretest": "npm run compile -- -p tsconfig.test.json", "coverage": "c8 report --check-coverage", "posttest": "npm run check"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standardx": {"ignore": ["build", "example.js"]}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "10.20.1", "dependencies": {"camelcase": "^6.0.0", "decamelize": "^4.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "^7.1.2", "gts": "^2.0.0-alpha.4", "chai": "^4.2.0", "mocha": "^8.0.0", "eslint": "^7.0.0", "rimraf": "^3.0.2", "standardx": "^5.0.0", "typescript": "^3.7.0", "@types/chai": "^4.2.11", "@types/node": "^10.0.3", "@types/mocha": "^8.0.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-import": "^2.20.1", "@typescript-eslint/parser": "^3.0.0", "@typescript-eslint/eslint-plugin": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_19.0.0-beta.0_1595116850495_0.1820343685833763", "host": "s3://npm-registry-packages"}}, "19.0.0-beta.1": {"name": "yargs-parser", "version": "19.0.0-beta.1", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@19.0.0-beta.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "f62e950d437550774e9a1a720841c7a39322f16a", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-19.0.0-beta.1.tgz", "fileCount": 12, "integrity": "sha512-C7h489kf9plpMxE1SR1HZt38hwZPWeNtQ7nAjlD9J8rFGPo/1bk57XaPLbWKXQakxda0NYqY0WGRcZC6lPku5g==", "signatures": [{"sig": "MEUCIQD7Dz0YcgYIysv8ON9hlk0CkZwK78xKH8NPhKLicQMSQQIgAXScUCrurtifZ+4/iNrmy7WV+9M0oXl6usUs/iCVs5Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89790, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfE61jCRA9TVsSAnZWagAAAMAP/0moxf9PxqQJNH/BRC2m\nkhaPd70Ff+cbgEqNCe48TWBkx/ou1DzTiO0Pj59oZ4E0vnBOhFQpH+K7L7dO\ntiK8E7csM17SkxCkU4Noglj441nTexsqAnCE4rDRciV3c4MUL5Gi3VRHLpkx\nNkFNoAFyJ+pVCAJd33zg6EvFQt6d8Sf7sOjMTBdO19IhVWKjd9/B08fApfBC\ng9RYcZ0JU9Iwmwa9YOU8NNtqPZXJ2MWCWtedrjxl1UoBypus2mSdr4jywx6T\nesGXQpST70yUowURH95Zb3sX0pSMsqm6OxY8Kz+eg6pzwDAA6UkV7pEnOG6j\nf4sf4jQMawygzAUxcSn/427tKKpJvMrnVAHpxzTGPxlQwXMrVRIZGzPfjtSg\nwJN9BgaXxnH6vq8Vpv5djtAHfxyXUny4l6JCAIvq0oL6Xp2Ha1EiHaVyapIR\nTiUXKzFRuf7VaqlaLibAaoZ2tuXrm3206zrbr1FdW3RTDmKz4f/HsZzTMbDj\niquzSDWKnMBnbRmzFCMqOQ7b/NPfm6+h48UhzEcaXn/KLs7zjtsqEvWgWd2u\n9vwFi/BERnVLaAszYm/9quJWyKztdr9ZggrosvuCpZzwL9+KXqPh97mkJ2E5\nB7YzK74ypDVUqyrw/UHgrnRBguANHGykEbVw8H1CJHdI6LWe1Vg4+KvY5X3b\nSzzZ\r\n=qB2I\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">=10"}, "scripts": {"fix": "standardx --fix ./*.ts && standardx --fix **/*.ts", "test": "c8 --reporter=text --reporter=html mocha test/*.js", "check": "standardx ./*.ts && standardx **/*.ts", "compile": "rimraf build && tsc", "prepare": "npm run compile", "pretest": "npm run compile -- -p tsconfig.test.json", "coverage": "c8 report --check-coverage", "posttest": "npm run check"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standardx": {"ignore": ["build", "example.js"]}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "10.20.1", "dependencies": {"camelcase": "^6.0.0", "decamelize": "^4.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "^7.1.2", "gts": "^2.0.0-alpha.4", "chai": "^4.2.0", "mocha": "^8.0.0", "eslint": "^7.0.0", "rimraf": "^3.0.2", "standardx": "^5.0.0", "typescript": "^3.7.0", "@types/chai": "^4.2.11", "@types/node": "^10.0.3", "@types/mocha": "^8.0.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-import": "^2.20.1", "@typescript-eslint/parser": "^3.0.0", "@typescript-eslint/eslint-plugin": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_19.0.0-beta.1_1595125090951_0.6617361432774247", "host": "s3://npm-registry-packages"}}, "19.0.0-beta.2": {"name": "yargs-parser", "version": "19.0.0-beta.2", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@19.0.0-beta.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "2fb2cd25ae344c9f4c899610b1ef42a5ef2496dd", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-19.0.0-beta.2.tgz", "fileCount": 19, "integrity": "sha512-ZII/iHZANrFKq2C9K2GgZyKpDi8dJl1qfgd+TCxhSe+DPOWVGIDAaFT4aYYJc5oCHwFmxnvfCG/2gADu1Mgsew==", "signatures": [{"sig": "MEUCIDZoutw6gvGgUxYSsiINwHkeHngwEThTgyaDYMi0xf5PAiEAiPSDwXZyElIfAO8sefLQt5MAkO6/MYAWdrpdCpjT1mY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKPEICRA9TVsSAnZWagAAaIkQAJNze8CB7gpSANgmlTuM\nt4SrL61Dck3Goeho/N/DogoxKBu9rgrhMU+nv8P20pcj5L/7m+NPGuEIv+Al\nd2gN5ViwO1m7nG+blRkMOBxWTdl+dUU97aA6gAhO+COtYDw9ocFnW65HkOy6\nKtkZvcJ+PjH4GinGuwJ5EwghUIOSR3vHN/9MaN4c/1dMp0knqLAbyPcmPGox\nGtzPqlNWTasqkzO7CrN9SdQHm3ogXHgPYCV5uvzROr+yOVOejwqt98v86FPc\nYAobfv/IKeRe1B7pODhtPv8eniOFR+ZBA2ufzrjOyUCN6Nal1xnZ0dbhcv/b\n/pfLwpLeV1PEV+abwti+tYiAde32DbCNfNKya4fzjF2BGbkyTxPmM68cATF9\nPpXVZXWONNug/WUDJZT67FytEdmI8+BtFFXVN9wcvKArf1k5ZYAfauH+u4QO\nh/HaS+fHS2hfQv9573VwytWojeDSDmTcmNxFzNLTQ20iEpy5LN5ZIgSazWHx\n5N+6xGJiKk1MtIU2P+FL4Dyb5CignKteP0PIzCW6/6SZbIYLqygI/X2+rcs3\nMSw7sritp7QWeqQIqW4DZDD3zV+mQ53eusFwvHMCp7yxXU/eCbVVoGaDr5Sk\nFZ+c6aqwY9AD5cE3rRLdCBbys7t+z3//6cEad4ABDVSRbTENrNOvLRUPHfLF\n32YJ\r\n=Rv65\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "types": "./build/index.cjs.d.ts", "module": "./build/lib/index.js", "engines": {"node": ">=10"}, "exports": {"import": "./build/lib/index.js", "require": "./build/index.cjs"}, "gitHead": "6c2d6406e04c33b8c0086708c9f5245fdd3a6467", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "posttest": "npm run check", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs", "test:browser": "start-server-and-test 'serve ./ -p 8080' http://127.0.0.1:8080/package.json 'node ./test/browser/yargs-test.cjs'", "postbuild:cjs": "node scripts/replace-legacy-export.cjs", "test:typescript": "c8 mocha ./build/test/typescript/*.js", "pretest:typescript": "npm run pretest"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standardx": {"ignore": ["build"]}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "12.18.2", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "^7.3.0", "gts": "^2.0.0-alpha.4", "chai": "^4.2.0", "mocha": "^8.0.0", "serve": "^11.3.2", "eslint": "^7.0.0", "rimraf": "^3.0.2", "rollup": "^2.22.1", "cross-env": "^7.0.2", "puppeteer": "^5.2.1", "standardx": "^5.0.0", "typescript": "^3.7.0", "@types/chai": "^4.2.11", "@types/node": "^10.0.3", "@types/mocha": "^8.0.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-import": "^2.20.1", "start-server-and-test": "^1.11.2", "@typescript-eslint/parser": "^3.0.0", "@wessberg/rollup-plugin-ts": "^1.2.28", "@typescript-eslint/eslint-plugin": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_19.0.0-beta.2_1596518663711_0.24804913162630826", "host": "s3://npm-registry-packages"}}, "19.0.0-beta.3": {"name": "yargs-parser", "version": "19.0.0-beta.3", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@19.0.0-beta.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "549c9cf969266709cd512df1fa1e80fec5d17d92", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-19.0.0-beta.3.tgz", "fileCount": 19, "integrity": "sha512-nm82bJIxk01S1sAv/awIMjWS3SbKS04wCeSEkIUpU991HPzjk9rDm1eVUIoe/KAniQGv3FZufOJ3+5cjY11dMw==", "signatures": [{"sig": "MEYCIQDnWfj28pgLNyHK92FWfQLKCYbxC1uR3wJqXV6DBPSZuAIhAJ0Nh1OXq68v/DwfcfkBitp3tTC6YAqyEvKKNp/YetDD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134970, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfK4QgCRA9TVsSAnZWagAAuf4P+wSjyJ3heS6pObmd7uY0\nnCGdOEDwMUrltvChg1Xf2X/8Y8HXsgoQXH3ZxW6hAsG1Yyo05wMyDw4rSLwP\nT2otrM4rrH/50SIK8QZE9aoMXgXoOw33V9asG9b6p2AdbT0BZONHRG8xolG3\nkujR3j4o2ky2IM5k8oBmeY3Z2hfkwuoQWXDzbhKsHhGIgyhe4x6yWhaaHTtt\n3OFX8WB2dilHj0/c/uULyWj5iW8xdlbpp9D0p7/gkIs5gB68djnCo7W6Snp1\nNP1PPnnloj2qz8hUm5vCuhSGqKTD2TMMoVJs3wm4cPlI8+dgVgtvycRWqxVA\nYxWKWKxKVX5w+HGcxI8Jlb9e2mLa8oOROz01NBTGr9yKseu5ZHREUqfphf0q\nAG354gwBJN0XNs6QwBhO6QWSCxasGM3sbMhxFjRG3apXdEJgWf+guLCzrCSC\nEbsPY6LrSyhQshLFLau7cHi6WgoFqNDpdcBxMCMwqSusFPFzagGlYE9wM3+J\n0llnZd76FTCCWhjqJwbUUbXSD6MfRODxPAsoHWP4Bf1Ql5Ty71wxOF/tLLmA\nDouYBZptPEQMGOn/pnDyUSDRTjpmIs4nlb51b5cMpvh+rfwYFtAvbKeQU9gj\necmaGchHZn/zpu2MkGCM0d5mtjdCYKpIdWyOQ7xg5C71AvnXXE0p0SxUdTSa\n0KwY\r\n=2RKY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "types": "./build/index.cjs.d.ts", "module": "./build/lib/index.js", "engines": {"node": ">=10"}, "exports": {"import": "./build/lib/index.js", "require": "./build/index.cjs"}, "gitHead": "1a925d86b27f325dcf6e028f5990d7d420d071bb", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "posttest": "npm run check", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs", "test:browser": "start-server-and-test 'serve ./ -p 8080' http://127.0.0.1:8080/package.json 'node ./test/browser/yargs-test.cjs'", "postbuild:cjs": "node scripts/replace-legacy-export.cjs", "test:typescript": "c8 mocha ./build/test/typescript/*.js", "pretest:typescript": "npm run pretest"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standardx": {"ignore": ["build"]}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "14.7.0", "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"c8": "^7.3.0", "gts": "^2.0.0-alpha.4", "chai": "^4.2.0", "mocha": "^8.0.0", "serve": "^11.3.2", "eslint": "^7.0.0", "rimraf": "^3.0.2", "rollup": "^2.22.1", "cross-env": "^7.0.2", "puppeteer": "^5.2.1", "standardx": "^5.0.0", "typescript": "^3.7.0", "@types/chai": "^4.2.11", "@types/node": "^10.0.3", "@types/mocha": "^8.0.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-import": "^2.20.1", "start-server-and-test": "^1.11.2", "@typescript-eslint/parser": "^3.0.0", "@wessberg/rollup-plugin-ts": "^1.2.28", "@typescript-eslint/eslint-plugin": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_19.0.0-beta.3_1596687392232_0.5382567348196192", "host": "s3://npm-registry-packages"}}, "19.0.0": {"name": "yargs-parser", "version": "19.0.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@19.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "41ca31398dd1ef8f7988970c5b493e2dc1e049b5", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-19.0.0.tgz", "fileCount": 19, "integrity": "sha512-YNvMZVZP6nZIiwSBfd/ll5nJMXqFMLtDNKsbQ+PrjabgAIXcL1hBmfQ7iWBwbY+aITCwRrPsVseNhdbKYejqYA==", "signatures": [{"sig": "MEUCIEzq1O7p4EXVhtBw7OWD4oUrAffNDJXlBVCnYWpQly1UAiEA7hPpgSR3ClREYBmiEeYEytILHfr/JQq+gZCxGrNGy64=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfL3iOCRA9TVsSAnZWagAAJckP/0Gema6aTnPH7GfxPrKw\ngfF/NyGcMr6ezdS4w2L4L0ZGKH1a4Vp4luqv85J2jbAgbuJ2E4dOUWI9CXQ8\naWlB3EGGoiNwhEc6UCwjU5hAyiHCdK14Tm20JIFAGHSd32sUiIQpaFkYmPMw\nAnQEPW0y8kp2PD6HsgSiFNiuGXnuMXtSMDHdRRqBPTT92zNMeDtM4Chc2Zay\nIaY5bscw9N1anDlfNYR1B1Bx4gRCHqnyk3zG+IOvpKDx1OaCAylFDFaskHiK\nU55fEiPQzF2/LF7iuoAbqIXRfJlrkW3ONwMkm/eYSJjBBjM5OUyolKEs8zvd\nk3Xt186PZ9u+OJo6iz5ckHDO3RPXEZP0rRQBk9YieTeb9vxmXOzyOMc/JBXf\ndxyGyi+dpQU5S2PPVJG1UwA7db76dsjGPiQdP1xrm2NkPRiNviAUsrmUCiE5\nYoGr0AZXt/Zn9ROvYoNxnuGFniSgzk/Rr1mlXtFV2GXI9uw5qfGak7hyRnZk\nQ18lUL5HFlDnPFf/ALvpcvI1WsMa6d2RQ79Z7qAI9SGt5xCFUwhDffk/xUMx\nf/4IKWui+hbfenkCJ61aSN+TdhfE3htcyWepiZAk3AowQkiUQv6BXQ5rGdM6\nHk/0UselCvbGPExedQ5Hwe0y5yyExXhn1UB6CGUpjTa9lXnwLrBxNPMDh4zp\nn/Xv\r\n=cz1V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "types": "./build/index.cjs.d.ts", "module": "./build/lib/index.js", "engines": {"node": ">=10"}, "exports": {"import": "./build/lib/index.js", "require": "./build/index.cjs"}, "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "posttest": "npm run check", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs", "test:browser": "start-server-and-test 'serve ./ -p 8080' http://127.0.0.1:8080/package.json 'node ./test/browser/yargs-test.cjs'", "postbuild:cjs": "node scripts/replace-legacy-export.cjs", "test:typescript": "c8 mocha ./build/test/typescript/*.js", "pretest:typescript": "npm run pretest"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standardx": {"ignore": ["build"]}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "10.20.1", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^2.0.0-alpha.4", "chai": "^4.2.0", "mocha": "^8.0.0", "serve": "^11.3.2", "eslint": "^7.0.0", "rimraf": "^3.0.2", "rollup": "^2.22.1", "cross-env": "^7.0.2", "puppeteer": "^5.2.1", "standardx": "^5.0.0", "typescript": "^3.7.0", "@types/chai": "^4.2.11", "@types/node": "^10.0.3", "@types/mocha": "^8.0.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-import": "^2.20.1", "start-server-and-test": "^1.11.2", "@typescript-eslint/parser": "^3.0.0", "@wessberg/rollup-plugin-ts": "^1.2.28", "@typescript-eslint/eslint-plugin": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_19.0.0_1596946573647_0.45474221602457354", "host": "s3://npm-registry-packages"}}, "19.0.1": {"name": "yargs-parser", "version": "19.0.1", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@19.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "34b444580905d0f67e89f31283d8dc1f8bd53d47", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-19.0.1.tgz", "fileCount": 19, "integrity": "sha512-2UuJKZmPN9S9/0s3FSCG3aNUSyC/qz56oJsMZG0NV2B44QxTXaNySp4xXW10CizmUs0DXgPY0y114dOGLvtYHg==", "signatures": [{"sig": "MEUCIC2XVh+NTfmpNxwJRU1O1tyFE0nFlaAwWJucNETzejWzAiEAxOV0ScVFeqAXy1NZiJScWU9i6bNxGuV7ad95Lb5j+Pw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138033, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfL3s7CRA9TVsSAnZWagAAXqAP/3czVFEyxOaHL/hpHaz8\njoxnV/tFe2Mi+DY8Icg8G9XcsGw1wiqhOzk0SnnqDkFwAEWNVOjIuaUqP4pc\ncRCMM8p4evxkzY3eAk9+b7ZcsAa8+uFXIVjNGB/b2deTgU5t510b1oxQ/dFC\nGlUAAvPLdCL1d8m/ssQ7T2Dq1L+a9kh/8NBtmV8oa0jGgXZyYJITSEU/vIOd\nymaP9YXRlSkMUuFV1aBd7F2H21Tl6AjyHrDcGYvB4eS32N6co3lN99wkUDlM\ncGPZMoAznIshktEjTTzbzllvZoS0pKX1AkqSTGwBhIK+i7uSTTYfp5Rnc5Hh\nks2BcXEfhM9vwEIRI6symo6f0/soT1Hj5uUlBmXM0yMq7wYNSXb+Bn/3NfmR\nikqvtz6FHQpsiqrT/IGAuQCp96e/zkcKdBbKUvPW4KVoHyZTKA8iyqk/CbJp\n+cAvc391iD4eNk4HpSTzRwNBFE6NVaDBPMxTR1klFy6g4yOfCbgq+MvPMSIu\neae0nuNoP8rtUJaxUv1iiNr6B3272CCD3volr2jiGMiAPEGG8AqmCfvKeedt\nsjOgQLn97Yyk4jU0xQ/l0+7KyO8mEoVzNWjcxrCizSPAx+UOWXqtDkGoGyzh\nK1pViGTQH+Cd2jDnYpv7tERuGA1b8bapxm1lQV85JA9DV83UdhfH9lI40qTh\n0lrL\r\n=kKFh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "types": "./build/index.cjs.d.ts", "module": "./build/lib/index.js", "engines": {"node": ">=10"}, "exports": {"import": "./build/lib/index.js", "require": "./build/index.cjs"}, "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "posttest": "npm run check", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs", "test:browser": "start-server-and-test 'serve ./ -p 8080' http://127.0.0.1:8080/package.json 'node ./test/browser/yargs-test.cjs'", "postbuild:cjs": "node scripts/replace-legacy-export.cjs", "test:typescript": "c8 mocha ./build/test/typescript/*.js", "pretest:typescript": "npm run pretest"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standardx": {"ignore": ["build"]}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "10.20.1", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^2.0.0-alpha.4", "chai": "^4.2.0", "mocha": "^8.0.0", "serve": "^11.3.2", "eslint": "^7.0.0", "rimraf": "^3.0.2", "rollup": "^2.22.1", "cross-env": "^7.0.2", "puppeteer": "^5.2.1", "standardx": "^5.0.0", "typescript": "^3.7.0", "@types/chai": "^4.2.11", "@types/node": "^10.0.3", "@types/mocha": "^8.0.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-import": "^2.20.1", "start-server-and-test": "^1.11.2", "@typescript-eslint/parser": "^3.0.0", "@wessberg/rollup-plugin-ts": "^1.2.28", "@typescript-eslint/eslint-plugin": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_19.0.1_1596947259223_0.9111392300384109", "host": "s3://npm-registry-packages"}}, "19.0.4": {"name": "yargs-parser", "version": "19.0.4", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@19.0.4", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "99183a3a59268b205c6b04177f2a5bfb46e79ba7", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-19.0.4.tgz", "fileCount": 19, "integrity": "sha512-eXeQm7yXRjPFFyf1voPkZgXQZJjYfjgQUmGPbD2TLtZeIYzvacgWX7sQ5a1HsRgVP+pfKAkRZDNtTGev4h9vhw==", "signatures": [{"sig": "MEYCIQDRXBtL7hFE0wZ6zlwRf0gUg3a+oVTIKp+u66ULAo+9FAIhAPC9N7maQUEt4o0RNBoPQiWGBw2gg7dTJUFztr+cl0Lr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 130387, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfR0bvCRA9TVsSAnZWagAAjiAP/2teUdRRyrfk9+Ot9epF\nQi5hRzdiXtMYRfjf8b8JYgee0RLNEl5bsdktecTlKsBp1m2H2SksoUn7G1hl\nKmAk2WV2HE8TNJxSkUoipka+3A/vRqNV+kbfLkce8DlAGwWv85qZlWtS5Hyk\nzlO2Y/px1lCitddZSuHavqb9SuUQfVOCLXiBA6se5R10KLel1P8n9bKf7Fc8\no0BVmfLZAtnliM/RqvXaFwIUzyYHjgJRHfyqK51/sU+1LxZt5DwX7764ruV+\nd0mgsMLUJoPjMeMVTHPGvjCToacpRGZh/0K3TIVFp/vk12Hu0m55ndbKHHJ9\nKTs2bSmjl0K/PkP9fGCXFSHr+aN8UydcHtvq8IDKjIEzDOpBPFRhbQpArvE+\nJZZ+0xYyT0Dqpdql58+pelMq/Bu2RsUCxnvUs/DQu5uGKvwZODPXLr8lHoQS\nrZ8aowAbSrzXGbXQfK97MDBIzoT9v0E/PUcwprSDVQd1ja4h2q4xhW+bAMxz\n2Fq2nrYNpd+Yu6shwhIKPg9REWbCLKSzF6evsMPFsCm/JyQQnFmzETFd+0a5\nca8KOddG8TLkFB/r5pCpYOgl9zoGufuA2FnyWlM8AUZXu8Ci5PxDhkYQKgyj\n+ryzNrg7F8BOWHXQA9x23FP1e3PfhHRfYFvpGfNEaS4F1QFeUqJk1QGR3BZu\nI/qk\r\n=Z821\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "types": "./build/index.cjs.d.ts", "module": "./build/lib/index.js", "engines": {"node": ">=10"}, "exports": {"import": "./build/lib/index.js", "require": "./build/index.cjs"}, "gitHead": "a02860af8d2e254f6c6a5fc7e14a9ee871c8470b", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "posttest": "npm run check", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs", "test:browser": "start-server-and-test 'serve ./ -p 8080' http://127.0.0.1:8080/package.json 'node ./test/browser/yargs-test.cjs'", "test:typescript": "c8 mocha ./build/test/typescript/*.js", "pretest:typescript": "npm run pretest"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standardx": {"ignore": ["build"]}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "14.8.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^2.0.0-alpha.4", "chai": "^4.2.0", "mocha": "^8.0.0", "serve": "^11.3.2", "eslint": "^7.0.0", "rimraf": "^3.0.2", "rollup": "^2.22.1", "cross-env": "^7.0.2", "puppeteer": "^5.2.1", "standardx": "^5.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.11", "@types/node": "^10.0.3", "@types/mocha": "^8.0.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-import": "^2.20.1", "rollup-plugin-cleanup": "^3.1.1", "start-server-and-test": "^1.11.2", "@typescript-eslint/parser": "^3.0.0", "@wessberg/rollup-plugin-ts": "^1.2.28", "ts-transform-default-export": "^1.0.2", "@typescript-eslint/eslint-plugin": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_19.0.4_1598506734640_0.34169996729973806", "host": "s3://npm-registry-packages"}}, "20.0.0": {"name": "yargs-parser", "version": "20.0.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@20.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "c65a1daaa977ad63cebdd52159147b789a4e19a9", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.0.0.tgz", "fileCount": 12, "integrity": "sha512-8eblPHTL7ZWRkyjIZJjnGf+TijiKJSwA24svzLRVvtgoi/RZiKa9fFQTrlx0OKLnyHSdt/enrdadji6WFfESVA==", "signatures": [{"sig": "MEUCIH4bMglBXj7VZGefOv8/Yyvn9uX+R/m6PnAEJmT/M+2ZAiEA3KSIihaBcX0Regt5vBRywKoHsZEi1IcKteGmLbwj7p0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 116381, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfWRagCRA9TVsSAnZWagAAhQ8P/3hyDJUt9nmcXWJ6KX2r\nU9q1WjFzrUxJBMk3/S2Jk6cB/5U1O7xaogfr7h5hmKUmqpmbhUxE2IY/A9KW\nAeuSKCyAm3kuNymW85cVu5eu0um262QkfI1ixzLvZ8xL6LnGQA3QZcMIqNE9\n4TKILpmek3/ydcQH4xbeeCqvdIIBs/fQ55tYRWlRBMv8NvASK2cqAEXulfsm\np//fVEtVS2iTg6dY+rQxeqaNB0Q3b0ERvbX88BGDcynKJ7GW7w8KMm0Q5oW/\n7drjYE3/u4dNn3BgaH/ClalFCy3ZW7e1kWcxRw/H/xrWK2vKfLFvq6rLHjHC\nZX2IHyfwxgUUgcH+AcwyZVRP5aZH24TIwBJIOr1dn0D5m7C2FhC+uoy/w8vO\nuAEpH041CmTIIxk+cWRFNZkpMs9cRX6eN2lMKrUe2LdBfdPag7guzbLQVIy/\n7SgkFtQolrrnbcfokhPrv2icAbDBJHa0Onh1AhEjnP1Oxnvb60CjQKJakEi2\n/8KLJ+s9kDR+VPW63K4uCIwHaGIFZXOnJ1kcWykGU5tvSNNkgUANRiTQKugi\n1GTis626QLKUREfNG7hYXJtmoVLVYC+roW7S4POPxoyfRRtw3ldlAWwA0UXa\naIJMMruQN4oPXfIVe/a67MHcHe4lpkYa+LHcMnUnO4LNjfT39Ky1l1ibVe0N\nFgx4\r\n=Cd02\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "module": "./build/lib/index.js", "engines": {"node": ">=10"}, "exports": {"import": "./build/lib/index.js", "require": "./build/index.cjs"}, "gitHead": "1639a00e4667a36a459d6c796e66ca04cc60274a", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "posttest": "npm run check", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs", "test:browser": "start-server-and-test 'serve ./ -p 8080' http://127.0.0.1:8080/package.json 'node ./test/browser/yargs-test.cjs'", "test:typescript": "c8 mocha ./build/test/typescript/*.js", "pretest:typescript": "npm run pretest"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standardx": {"ignore": ["build"]}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "14.9.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^2.0.0-alpha.4", "chai": "^4.2.0", "mocha": "^8.0.0", "serve": "^11.3.2", "eslint": "^7.0.0", "rimraf": "^3.0.2", "rollup": "^2.22.1", "cross-env": "^7.0.2", "puppeteer": "^5.2.1", "standardx": "^5.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.11", "@types/node": "^10.0.3", "@types/mocha": "^8.0.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-import": "^2.20.1", "rollup-plugin-cleanup": "^3.1.1", "start-server-and-test": "^1.11.2", "@typescript-eslint/parser": "^3.0.0", "@wessberg/rollup-plugin-ts": "^1.2.28", "ts-transform-default-export": "^1.0.2", "@typescript-eslint/eslint-plugin": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_20.0.0_1599674016326_0.5179191473459579", "host": "s3://npm-registry-packages"}}, "20.1.0": {"name": "yargs-parser", "version": "20.1.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@20.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "9abade5095ac4391156496743e60d5ec9a5b486c", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.1.0.tgz", "fileCount": 12, "integrity": "sha512-RV4YEjMLfjWkK9jNV/aZytlJ5uz+JBk7t29FofILa41jJAU/yCwghgsjH2xT0h7eu7b6MrCDJb1qZjeDJ/jI1w==", "signatures": [{"sig": "MEYCIQCdLUlc++v+BWWINYhwrUQmFbT/t32fCMpwRbYSYZGR8gIhAPyglZ3OSox3z6TrWxiE1WDksMGxjzTHv8Pqo6JOExND", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117551, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZtvACRA9TVsSAnZWagAAO+oQAIZC/th6FAnsRJTcWlBV\nuUXbFPI4SLcpDeGFBY2XYahlHE/wNVSm2OclGG9tf72S+oU1IAFWmWBkaGvN\nnua9kG+w2BQHl26deKrB3n2GbpEpAqqQrp1Y+t5PguWr+y+12FjIXzW0Szom\n9st/1cY/RPdo2QQN2nlIVcgyL7xcWLEqEONn4f16xU5z8Mkjaf4hIjmwtf5z\nAJQ5Wgf07oY8NWJZv7PVyWh0Sj9zB2pc/X7HRDmxDzEP8bdHX6A66M20/2m7\nJcmD7hEqlJfuk1G0y54wXmZV/fb9kyOl9SdAwDb3uePDpKnx91cvJw9IOKXd\nQoyZIcxH/sbC49DqBVIwGgQ8fVDLpdR7L8YKW7zVXWRbHZg65bsOhAGFciyF\nrs8BURq2m5dVkGyMYLQ1xsT5TPTlcRB3TOJ8XhagFL2MTerpM2sugjkBg8EK\ntJVaBibOHiowSIQnVDV8acRjCgqOK70R6THoilJJvzsKDR1hLq0Jum35vJmQ\nRTv9uzdiDArbOvO2eJ4q0doJ4k12ayj6+R9lXL9MpDcXgMvvPt8AslkWIwcA\nNmUa6Bzz3kdpWbBjd0JvmPiTdxSFAooDk34IKfcVCLSNMYVb+NiLhAdlXBgA\niMAasdAPxYCMahNbbLqaXITSoBbNVmu24gvjIbLXK7wq5WtoBpu/XsT2ejhU\n8qOu\r\n=g/Rv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "module": "./build/lib/index.js", "engines": {"node": ">=10"}, "exports": {"import": "./build/lib/index.js", "require": "./build/index.cjs"}, "gitHead": "4ef11c7a8b8ef0f0eae3a7873585849467bcfef2", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "posttest": "npm run check", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs", "test:browser": "start-server-and-test 'serve ./ -p 8080' http://127.0.0.1:8080/package.json 'node ./test/browser/yargs-test.cjs'", "test:typescript": "c8 mocha ./build/test/typescript/*.js", "pretest:typescript": "npm run pretest"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standardx": {"ignore": ["build"]}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "14.10.1", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^2.0.0-alpha.4", "chai": "^4.2.0", "mocha": "^8.0.0", "serve": "^11.3.2", "eslint": "^7.0.0", "rimraf": "^3.0.2", "rollup": "^2.22.1", "cross-env": "^7.0.2", "puppeteer": "^5.2.1", "standardx": "^5.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.11", "@types/node": "^10.0.3", "@types/mocha": "^8.0.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-import": "^2.20.1", "rollup-plugin-cleanup": "^3.1.1", "start-server-and-test": "^1.11.2", "@typescript-eslint/parser": "^3.0.0", "@wessberg/rollup-plugin-ts": "^1.2.28", "ts-transform-default-export": "^1.0.2", "@typescript-eslint/eslint-plugin": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_20.1.0_1600576447371_0.7613555040774866", "host": "s3://npm-registry-packages"}}, "20.2.0": {"name": "yargs-parser", "version": "20.2.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@20.2.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "944791ca2be2e08ddadd3d87e9de4c6484338605", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.0.tgz", "fileCount": 12, "integrity": "sha512-2agPoRFPoIcFzOIp6656gcvsg2ohtscpw2OINr/q46+Sq41xz2OYLqx5HRHabmFU1OARIPAYH5uteICE7mn/5A==", "signatures": [{"sig": "MEYCIQCL0f+FFyhJ1O42X4jM6RyB0RMqr7Z1rs4uGilpumwspwIhALInmG39Uc0ilY1Wb496HMBlGkOS+FI7hN6XzBEsYV2O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 118382, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfaBXQCRA9TVsSAnZWagAANFsP/ROCjumnOdoKDu3vr23p\nWqDWojQPw78TktP2wNQvpaUHkt5YXEMoNGcdWkOHy8YFoRQ3BhiWM5X6cNck\nhhNpJsE5bvaaBKzK2iKOd+uXoS16TlqZ6GGEwMSbjay+nwgzfPuiDRHwQ9P7\nHlKZgKrcy5ZWhhYTb98VDvUTrgxt3JRfT89JNPr5r+eqPqY6iSg/ad7H6FGP\nJG/+/xL5H+Ono8IMWigOUvfhwSNxcODm8YytKW2cpDsAbcs49gl0KMWCBkaV\n+j4VGaq3TGWtQqj6eSPerzDy70NujZCwcd8xN/xrzH3RNiuNbq1I6fP4/up9\nskQJnweB/FEtdmqiKdk5Yqoi7UpkOIqa/hOhk5+PAXZSmRLTZXF1yegCPi3k\njZhoGFYqCFLtOzBq1i2NMZqQRkmCJK3cYv3NG4wPkyBWHNIkuH+gb7Cb/rGl\nHcmERlgW9GWc+Oh0dg55/bDkqbRuVGQPH8AhSxHyvP+Az6nSFcjLwk1Pd8J+\nSAhfllpRAX/N8kI24jashjsnDzb7mBL4P16LvvHs5XI96Pg46AVFEPzG08mF\nZiv0D7tnRLS3lwfQrtdPO92+ZHVhztdmpw4aosVNEb7AVIMBq6tphT8rIfxW\nWux0NpeVnhQbTSny/TlPfTjwpua72C2w75WNvpMGaE9V/RHZexgbqXm005j+\nbH7J\r\n=E3b7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "module": "./build/lib/index.js", "engines": {"node": ">=10"}, "exports": {"import": "./build/lib/index.js", "require": "./build/index.cjs"}, "gitHead": "f0f5566b41b1ba75aad24517103a68a59b1db364", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "posttest": "npm run check", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs", "test:browser": "start-server-and-test 'serve ./ -p 8080' http://127.0.0.1:8080/package.json 'node ./test/browser/yargs-test.cjs'", "test:typescript": "c8 mocha ./build/test/typescript/*.js", "pretest:typescript": "npm run pretest"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standardx": {"ignore": ["build"]}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "14.10.1", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^2.0.0-alpha.4", "chai": "^4.2.0", "mocha": "^8.0.0", "serve": "^11.3.2", "eslint": "^7.0.0", "rimraf": "^3.0.2", "rollup": "^2.22.1", "cross-env": "^7.0.2", "puppeteer": "^5.2.1", "standardx": "^5.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.11", "@types/node": "^10.0.3", "@types/mocha": "^8.0.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-import": "^2.20.1", "rollup-plugin-cleanup": "^3.1.1", "start-server-and-test": "^1.11.2", "@typescript-eslint/parser": "^3.0.0", "@wessberg/rollup-plugin-ts": "^1.2.28", "ts-transform-default-export": "^1.0.2", "@typescript-eslint/eslint-plugin": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_20.2.0_1600656848435_0.09022688866373763", "host": "s3://npm-registry-packages"}}, "20.2.1": {"name": "yargs-parser", "version": "20.2.1", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@20.2.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "28f3773c546cdd8a69ddae68116b48a5da328e77", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.1.tgz", "fileCount": 12, "integrity": "sha512-yYsjuSkjbLMBp16eaOt7/siKTjNVjMm3SoJnIg3sEh/JsvqVVDyjRKmaJV4cl+lNIgq6QEco2i3gDebJl7/vLA==", "signatures": [{"sig": "MEUCIEPLQdQ8Em4TJTFQHbe8j9Nv/BEaoRaFXihlcfxHWzbZAiEAwYF/6yjl8qHolP0PQZ33/8PKqmasQc1CJ2yvEmiZT8o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 118702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdh6jCRA9TVsSAnZWagAAarkQAKIKYOlcqGqrV/RtTfbS\nmvmi2PsSSb5wE/RkO92m2LyljNrWaSlPx3VKvsLWuKQkaBm+60Za60SUAYsq\nD7HRdgpE36vdCviKqhN23Qv7JV2gfjxpdpfW58gTZQe5ii7C7S3c929DdVug\nm78IfUoon3d8qdyM/E6SITUbAKzn5fJmDuCuFf979SkkZu3ZlrR1OUtXRXHg\nLg/kNDx7DAboTWPJZvK74Vw5XOTgK4JHHxZPpxoIc0geYQS1vLolTVw9JAMf\nRnFQ9EtM1uK7GgJqq5WJSSNiSHNM3shZmw/A40bDOqNSwULtbeGExAzhoHVB\nCsAixLFGmldO6Hqc2W5kAh/ATcDsibQMzYAO6PnHdAym3SFsqHLKa0XXa83R\nA1UIoO30JG0+BdHN/WWAdfijr0uHY3pFAZF8kCZkMgfPu0vrgSwx7dPj9rR+\nTc6+tkkB47eYZ1JzlRsREHqA7zEdlZjQ4LZ9MJlW9FJMUCSsCnPyd7b29eGu\nCggDRV5nInwsbZ0os8lFkcwBnV+il8y23FnxProVudes0Rcp83HjeWYGAr6b\n/8XDjUXdmZuO9LwJq1QILyLGp6ogSYS1oLsUnDtAW698oYjrPsLyb08jAMnM\nIFNJVQ1o5NEQKIdnHwdm2syiULwCW33jiYUcC0WNZcFJb5bI1gEBDxiyqKVB\nFSNy\r\n=g1yY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "module": "./build/lib/index.js", "engines": {"node": ">=10"}, "exports": {"import": "./build/lib/index.js", "require": "./build/index.cjs"}, "gitHead": "0bbec4a3e6b79cc3163bbf3727cfe69eaa25e94b", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "posttest": "npm run check", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs", "test:browser": "start-server-and-test 'serve ./ -p 8080' http://127.0.0.1:8080/package.json 'node ./test/browser/yargs-test.cjs'", "test:typescript": "c8 mocha ./build/test/typescript/*.js", "pretest:typescript": "npm run pretest"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standardx": {"ignore": ["build"]}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "14.11.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^2.0.0-alpha.4", "chai": "^4.2.0", "mocha": "^8.0.0", "serve": "^11.3.2", "eslint": "^7.0.0", "rimraf": "^3.0.2", "rollup": "^2.22.1", "cross-env": "^7.0.2", "puppeteer": "^5.2.1", "standardx": "^5.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.11", "@types/node": "^10.0.3", "@types/mocha": "^8.0.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-import": "^2.20.1", "rollup-plugin-cleanup": "^3.1.1", "start-server-and-test": "^1.11.2", "@typescript-eslint/parser": "^3.0.0", "@wessberg/rollup-plugin-ts": "^1.2.28", "ts-transform-default-export": "^1.0.2", "@typescript-eslint/eslint-plugin": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_20.2.1_1601576610954_0.2034961510597899", "host": "s3://npm-registry-packages"}}, "20.2.2": {"name": "yargs-parser", "version": "20.2.2", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@20.2.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "84562c6b1c41ccec2f13d346c7dd83f8d1a0dc70", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.2.tgz", "fileCount": 12, "integrity": "sha512-XmrpXaTl6noDsf1dKpBuUNCOHqjs0g3jRMXf/ztRxdOmb+er8kE5z5b55Lz3p5u2T8KJ59ENBnASS8/iapVJ5g==", "signatures": [{"sig": "MEUCIFMP9pE0VbbSpHFidyznTGbLfSJa9IwbR0/4nWRdtd4aAiEAouphsjQ7l85f6/jp1qdLkHdSbBfYqDKz5qooWS0m/Vk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119070, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfh0rICRA9TVsSAnZWagAALKUP/itQDaQQHfU0MRpPShx7\nE1j6MGXkuE5wc2MHgvKMWWlSxinRXKsF5x2X9ql+rimny3P98jPLCCwz1jB4\nxXw/tvmWOb+1Aub28V5uO/i3AErFl6pY/oHyRV8SRXELTOAyJEUNcwGFuF14\n8RhTkCq90mmIE0xw6VqbR83ZIvy+gf4VL4lhOn5KZs6lyvsy41cOkYYCElFK\nBIx24viHKJKhMhfKg1OnY0WKVx+07M3PQ6PA88R0+ZWdAfI0FGUCR4oSgSRt\nVL4TjKJS7vaNcnm6ChNvCsFqD/49QFZ2e9Q87kgk1AWoRWtZfa83yBoO/ecG\nfu2fOLfF9pPr6l9m9ZJrQ8AgtUzKAp1Y6ZS/nfpYCrzx2upB6fdrGTqz8nNr\n1bDOElKhydZIhW8ZjSjb9i2KHySkxDAy9/ZPSxZGWMqW51quxW1POe/hLa3x\nFW9XutbLEHFAw7cqfxdY0ltvu8aaqQewPxyKeF9jjwZWjCPzhD58SjzukmuH\nh4NC6JMB72Xtw6/NadQWprttS77VSSKrs4ISFKz2jRSxgrNkOcIUh0dy3/1b\nKg/F8rDPOKiHs2H5Ywu8LSvgH5Q0Pt0M0VA92ukatr3I6+EdJf0NAWXwawvJ\nkH0eNfyxrNKRDED55FqjQj6MlLeBj5DMGSeliUhswb5WuBY0WiOkRP9tIy+s\noxd5\r\n=/jJe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "module": "./build/lib/index.js", "engines": {"node": ">=10"}, "exports": [{"import": "./build/lib/index.js", "require": "./build/index.cjs"}, "./build/index.cjs"], "gitHead": "2c1614c828e6266a7a7b88822ba34bff9ca6cb0e", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "posttest": "npm run check", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs", "test:browser": "start-server-and-test 'serve ./ -p 8080' http://127.0.0.1:8080/package.json 'node ./test/browser/yargs-test.cjs'", "test:typescript": "c8 mocha ./build/test/typescript/*.js", "pretest:typescript": "npm run pretest"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standardx": {"ignore": ["build"]}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "14.13.1", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^3.0.0", "chai": "^4.2.0", "mocha": "^8.0.0", "serve": "^11.3.2", "eslint": "^7.0.0", "rimraf": "^3.0.2", "rollup": "^2.22.1", "cross-env": "^7.0.2", "puppeteer": "^5.2.1", "standardx": "^5.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.11", "@types/node": "^10.0.3", "@types/mocha": "^8.0.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-import": "^2.20.1", "rollup-plugin-cleanup": "^3.1.1", "start-server-and-test": "^1.11.2", "@typescript-eslint/parser": "^3.0.0", "@wessberg/rollup-plugin-ts": "^1.2.28", "ts-transform-default-export": "^1.0.2", "@typescript-eslint/eslint-plugin": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_20.2.2_1602702023891_0.3416707625294577", "host": "s3://npm-registry-packages"}}, "20.2.3": {"name": "yargs-parser", "version": "20.2.3", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@20.2.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "92419ba867b858c868acf8bae9bf74af0dd0ce26", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.3.tgz", "fileCount": 12, "integrity": "sha512-emOFRT9WVHw03QSvN5qor9QQT9+sw5vwxfYweivSMHTcAXPefwVae2FjO7JJjj8hCE4CzPOPeFM83VwT29HCww==", "signatures": [{"sig": "MEUCIF2G+VNI02H1SodWcYIa0/T8hY3qV4qJZwndlchS76oqAiEA8NeZR9T9L1GJrPLU8fYhuuEDhyyihdKlDQsa6FUffvU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119466, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJficAMCRA9TVsSAnZWagAAlPgP/0+shnOx0yAbKFf8vaIY\neQslqIRFRpke5tJhqdlL0X/kfpU1QUI80HjWyv9B+9fXsWuBpsQCJNkjjGEC\nhJK4YCrq9LANT9bR0UYXTg1/yUVCpNthIlG06liKXNJsHovF+c75Rn82+EiD\njFWm4vszWhty3Iy+GecOQ7l/j4y+SSXJsu+3SYtm2oN8yycMIcfD1eq10cxJ\nOtRHJdh3alij8PwgI21yxvLoHHPnLXw9FQejTftN77OJTcgp3YZG6wQD99XP\n09cfzflua56I6gkL9y0FgiBDjtomS/Mqzic3RrpTzgbkZGYYb6Sgq7XXLrds\ng+7+bjBJXNo3Weg3yJtWDFX2ABupCJOFaY4BACRpPMFfC5U2ZmnXgxFqWZfo\n+rpbBT2lfL4QgmmYyyyUX15xUQ4UiUplH7BwpVaxkY+4NdMq4VLJntd1w+7r\ntW6Jg9KYEwvk5lnAMF4sj3O+tNzTecTvzPth2zQ+6lnoBJYRFpdYT9lde9gZ\nDqPk6uKjpMxKnBrHlrzwXAT1kRlJBHCLXe8/pgAm4es+BsGUNVddFyVeR70S\n1zOsf60gAe1zQpXwhYc8iex058PlZIm2oeI54j4Qmby0wnCtPYcXCmoH4vOV\n7K4qNOgf15CkqgLwtUhGOVR0Q7URpvlsSwQzl+STD5UTxUvxZwG0xB/UBOMA\n5ZLi\r\n=TXpp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "module": "./build/lib/index.js", "engines": {"node": ">=10"}, "exports": {".": [{"import": "./build/lib/index.js", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "gitHead": "03049080f34e3393b6329a8da0b301b23ff2e445", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "posttest": "npm run check", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs", "test:browser": "start-server-and-test 'serve ./ -p 8080' http://127.0.0.1:8080/package.json 'node ./test/browser/yargs-test.cjs'", "test:typescript": "c8 mocha ./build/test/typescript/*.js", "pretest:typescript": "npm run pretest"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standardx": {"ignore": ["build"]}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "14.13.1", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^3.0.0", "chai": "^4.2.0", "mocha": "^8.0.0", "serve": "^11.3.2", "eslint": "^7.0.0", "rimraf": "^3.0.2", "rollup": "^2.22.1", "cross-env": "^7.0.2", "puppeteer": "^5.2.1", "standardx": "^5.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.11", "@types/node": "^10.0.3", "@types/mocha": "^8.0.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-import": "^2.20.1", "rollup-plugin-cleanup": "^3.1.1", "start-server-and-test": "^1.11.2", "@typescript-eslint/parser": "^3.0.0", "@wessberg/rollup-plugin-ts": "^1.2.28", "ts-transform-default-export": "^1.0.2", "@typescript-eslint/eslint-plugin": "^3.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_20.2.3_1602863115545_0.23707586160715777", "host": "s3://npm-registry-packages"}}, "20.2.4": {"name": "yargs-parser", "version": "20.2.4", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@20.2.4", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "b42890f14566796f85ae8e3a25290d205f154a54", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.4.tgz", "fileCount": 11, "integrity": "sha512-WOkpgNhPTlE73h4VFAFsOnomJVaovO8VqLDzy5saChRBFQFBoMYirowyW+Q9HB4HFF4Z7VZTiG3iSzJJA29yRA==", "signatures": [{"sig": "MEQCIAEVQUXQCs/k+6TDTHh6frWGYtsJO0KUVmdo6NTY81oXAiBMSuIgJEoSP/8DsdVtqAxv+ThQo94hrI7Uq2TvKSnd8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119753, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfqKBtCRA9TVsSAnZWagAAEIIP/iw8OBGi01zvEmj+YLK3\nQpRCQpq+Cvqz+vVL+XNfCPSOAK+vgfihN2teVPq+j5WlTsYmrrZWwZBItDvX\ng8VEyvDaywuEvnOItH8CWftRI4fRtejb3U0Tg+hMtvt0iSZI63c4XGST3a5d\n2lJjr1bmcJNmxJK/YnNfdeds38rcd1zRSLG/zx1w7K7hxiwLFgvEsltDRhZG\nZPUrqrTf8KR98gdQToMLavb5OJE9KJfmxcMl86RVa4OBkj3TXpzzB11hjrGB\nrQyXZxqgeytZuaHjOSrPGvgcfP43L+DwKfnvjstpXAUkxyGQIbiwFndTW0zV\ntQ7iAXppx9pvlKvcrcPoiGYlU6IuzdQOOUUrOqntVGn2wUP1Jhw2CChmcQt0\nONFdWJ/odxE2XzrPP0EHsvFZfW7O+O7FSFhgUNZJd5OT6L/vlyf9KZ/7Ecg8\nPP4rsAx89GNwqeyjnhYxtgVHTZzwbQ2t9EcpbEV2FuhR4+sgXTE1E6ArlgG2\nkF7gjiO+Dtcg6lvEQBbEw2YFb4MD0l6QBmbFginZSLj8iNoZ8kyv1ZHbvPWS\nCaNuA8a9Ky+KC7QwoqiLJ6mgLfbNVjMPgHm+iu6GPUgQ0skNFgswTUi1pfos\nsmNrKxIfNRFo3xb39Ya+a4Cq0WSdUsG8gVEbEOhax0k1/AlGWqMEYvNt0DGC\nQRaq\r\n=daLg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "module": "./build/lib/index.js", "engines": {"node": ">=10"}, "exports": {".": [{"import": "./build/lib/index.js", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "gitHead": "a5014eb27e49cce89ce34eb11a9403ea08b85166", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs", "test:browser": "start-server-and-test 'serve ./ -p 8080' http://127.0.0.1:8080/package.json 'node ./test/browser/yargs-test.cjs'", "test:typescript": "c8 mocha ./build/test/typescript/*.js", "pretest:typescript": "npm run pretest"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standardx": {"ignore": ["build"]}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "14.15.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^3.0.0", "chai": "^4.2.0", "mocha": "^8.0.0", "serve": "^11.3.2", "eslint": "^7.0.0", "rimraf": "^3.0.2", "rollup": "^2.22.1", "cross-env": "^7.0.2", "puppeteer": "^5.2.1", "standardx": "^7.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.11", "@types/node": "^10.0.3", "@types/mocha": "^8.0.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-import": "^2.20.1", "rollup-plugin-cleanup": "^3.1.1", "start-server-and-test": "^1.11.2", "@typescript-eslint/parser": "^3.10.1", "@wessberg/rollup-plugin-ts": "^1.2.28", "ts-transform-default-export": "^1.0.2", "@typescript-eslint/eslint-plugin": "^3.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_20.2.4_1604886636613_0.31004224086724186", "host": "s3://npm-registry-packages"}}, "20.2.5": {"name": "yargs-parser", "version": "20.2.5", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@20.2.5", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "5d37729146d3f894f39fc94b6796f5b239513186", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.5.tgz", "fileCount": 11, "integrity": "sha512-jYRGS3zWy20NtDtK2kBgo/TlAoy5YUuhD9/LZ7z7W4j1Fdw2cqD0xEEclf8fxc8xjD6X5Qr+qQQwCEsP8iRiYg==", "signatures": [{"sig": "MEYCIQClptO3cwCVUQHHgzsylWwmimi4FjP6BBHAyZQw4tIFHwIhAICNbyNL2wPRlAHL2MF8VvBPSbuK85RY5ac9Tb/ROB1E", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120389, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgKDLbCRA9TVsSAnZWagAAlVMP/2QL8dLEpAmoqJEirCtz\nI0iy81GoHC+BDHzK796ZePKinVTBESstYPtsjeAyj4nOvlq2v7t4fLNx1uZG\nN6bkmcLOo0EmtJ8eSGOrteozkUzxSAmkIXWSNBjaYdFGI2Xfy4DSfNTmgY51\nnftEBKtabYOUFe0JcNs2s6Ql0jL7lPNKNx1EnfVv9J4YWTvgKTosaOxfHyZ1\njQsGlhZzzu3WfCgHfJa2v5q7PUrne7Don/iFrLN9F3rAetmTLK2FaXKTmJDC\nmCMs8TWXcD3R2A/axoWK12sNb0DM1ii/pSTfGTot4NDT5H+1hbGEHoffpLrp\nOloQ3CJDVP7WXwVCx+KsOKRNosiwS4rB4tEAViZi8JfYI7B3M7LyorkRnv9d\nukD0f0AG5M/ARBHX6WTunQW2wzHNgjFiNKQ7JMMuemP2hN7a2IoFa+QH2fkO\nKxgdEouTnHJko/mBntCbH+OAYAF/cw3kheUAL2XWOLuXFtpForAAKcDO/CXL\n7nKIBXOXMyau+cFTFiG65dBfYWsf56dfPYKQHJbH+rwX6tKwUIF0GsZkPOp0\nfnf4Std7KWMm70uUXEcXRTHRWBfKdKHowPGpACjNxmG/i88WK+rEob/gcLfY\nClF5Lb58pMrBCxVZlgYswDtza6sBkDqpHZd5FNaTGPoVw6YELTmMXAKWH71c\nb0rL\r\n=OCpw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "module": "./build/lib/index.js", "engines": {"node": ">=10"}, "exports": {".": [{"import": "./build/lib/index.js", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "gitHead": "43d32af4f8864699cc1fe0228bd10d9efcfcdadd", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs", "test:browser": "start-server-and-test 'serve ./ -p 8080' http://127.0.0.1:8080/package.json 'node ./test/browser/yargs-test.cjs'", "test:typescript": "c8 mocha ./build/test/typescript/*.js", "pretest:typescript": "npm run pretest"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standardx": {"ignore": ["build"]}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "14.15.4", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^3.0.0", "chai": "^4.2.0", "mocha": "^8.0.0", "serve": "^11.3.2", "eslint": "^7.0.0", "rimraf": "^3.0.2", "rollup": "^2.22.1", "cross-env": "^7.0.2", "puppeteer": "^5.2.1", "standardx": "^7.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.11", "@types/node": "^10.0.3", "@types/mocha": "^8.0.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-import": "^2.20.1", "rollup-plugin-cleanup": "^3.1.1", "start-server-and-test": "^1.11.2", "@typescript-eslint/parser": "^3.10.1", "@wessberg/rollup-plugin-ts": "^1.2.28", "ts-transform-default-export": "^1.0.2", "@typescript-eslint/eslint-plugin": "^3.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_20.2.5_1613247194671_0.14218219037807622", "host": "s3://npm-registry-packages"}}, "20.2.6": {"name": "yargs-parser", "version": "20.2.6", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@20.2.6", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "69f920addf61aafc0b8b89002f5d66e28f2d8b20", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.6.tgz", "fileCount": 11, "integrity": "sha512-AP1+fQIWSM/sMiET8fyayjx/J+JmTPt2Mr0FkrgqB4todtfa53sOsrSAcIrJRD5XS20bKUwaDIuMkWKCEiQLKA==", "signatures": [{"sig": "MEYCIQD1uCAAiPdW+WD7iB+D6wKfK0RgwzxU0EO4YaKbGy2VsgIhANzmw63+aTaI5G+Cbhe+2ymcnbSMiAa4xjJ+LFUQB+LA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121058, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgMxsaCRA9TVsSAnZWagAAJCgP/AvY/DpeA8yiadt9Qj2l\n7fl4T/ckxGJ4AKdN5PSl1EUU9pyV37k3URXXq4T4t0NkCm2Ezd5tJl20K48x\natQ3PK2WZJFXdT6QKapI1zmdUnkzuNA1uXTGSEHwtS/RMJj1h23uWLGhHToN\nYSZXlLhtPVBzwjn8nxKX7qqCUDVjWRMBHX2ntGF8VWU6PLV+QYj7HcXRuWrU\nrrOXPU9YvE9QSlS9WeGDCjTEpkZp6tkLd6xP7Y2t63IARwJkhrL7ZOOvLoBV\nk9CX09fxryrm/2IGda92NqvNWXG616o8bOaYiADRBAHFNkJcrJL8LHMpyCo7\nt8FqNixCLUF7dxsHjduHGW+9d+izbUSpMc/SNxBY2envVvzf+HeFSeuXZG/a\nsO/oikCyNlRPQskfQEbDa/04UgMpTuPs+rr9XcObnmpledN1BkzmnP/jPA3n\ni+bnqR27aEWD3oauqsk1MwIuHraT9nNe2cFK3UM6eUpIR70iRTubRJzYpFWf\nIMPlg8kK2AwN/hIzCYMeerqk5/Vmo1xG7IhzMKiX3bBdn3l/go9sj4cHGlPr\nAS4+x6WWGwhCrOcov9Fniim7bXxH8HJQnvYksc4QLjrEJ5BFuj3EgeLXTTI7\nVJ5Okwe73MkFF0VF2sk8SoqN9hw1ZMX7i3AZTLxCUGkoUflZyrVmVljT/D/v\n2QjY\r\n=L5HG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "module": "./build/lib/index.js", "engines": {"node": ">=10"}, "exports": {".": [{"import": "./build/lib/index.js", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "gitHead": "735c6b34ec07e3c179b5b5e779d5e7e9fda93cc7", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs", "test:browser": "start-server-and-test 'serve ./ -p 8080' http://127.0.0.1:8080/package.json 'node ./test/browser/yargs-test.cjs'", "test:typescript": "c8 mocha ./build/test/typescript/*.js", "pretest:typescript": "npm run pretest"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standardx": {"ignore": ["build"]}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "14.15.5", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^3.0.0", "chai": "^4.2.0", "mocha": "^8.0.0", "serve": "^11.3.2", "eslint": "^7.0.0", "rimraf": "^3.0.2", "rollup": "^2.22.1", "cross-env": "^7.0.2", "puppeteer": "^5.2.1", "standardx": "^7.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.11", "@types/node": "^10.0.3", "@types/mocha": "^8.0.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-import": "^2.20.1", "rollup-plugin-cleanup": "^3.1.1", "start-server-and-test": "^1.11.2", "@typescript-eslint/parser": "^3.10.1", "@wessberg/rollup-plugin-ts": "^1.2.28", "ts-transform-default-export": "^1.0.2", "@typescript-eslint/eslint-plugin": "^3.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_20.2.6_1613962009905_0.9267543411262082", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "yargs-parser", "version": "5.0.1", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@5.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "7ede329c1d8cdbbe209bd25cdb990e9b1ebbb394", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-5.0.1.tgz", "fileCount": 6, "integrity": "sha512-wpav5XYiddjXxirPoCTUPbqM0PXvJ9hiBMvuJgInvo4/lAOTZzUprArw17q2O1P2+GHhbBr18/iQwjL5Z9BqfA==", "signatures": [{"sig": "MEUCIQDZMsmgFdwCcNHN5zFGLqPsMoatmUjy5AQIk35zG4Ds8wIgZdCYOueTfJR5MJ9JcZKUKTJ/0+Un9lbUShcAhVXkCt4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38524, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgSRweCRA9TVsSAnZWagAAcrQP/1LUhjZe1jgnRg4vIc10\nLh3GOAXj4Vrsg7KIEjmGn8C8HbFcqYtxDnULdgnFYH+YPycZ0jDrF/dnTHr9\nQC83DMyc65C3i26PDdXDMGykn/JDv0w7wPmhaWoqrKgTvV0naUKKRbNmd9Ge\nyGlbHNyTCJLklgpnRDREJvryzbFzuYovSPzCa00+DhebAkTC3il92L788JCl\ngb8rHphjMrlC27Awo0n0wZoNVUyAxI3f2K8HoeYbRJi3xIfVuYYOeGzUX3ku\ne+S2AepIM2rv2bBNxYqDw9SElEAVmpwOQbVXk+E1pa0PYcRQE+JuuSLmfPas\nRH07DAk4mEWeelm6aoVxHbhCGzRaSZPYhUaGDW1hBAVr58UCM4yEOIX/CL3i\nFZ08ccrqoq95EN/i5JAqMgWRsgwgNrk5x3KiXzLwGlvZc+yS8J65l6lqu8Ek\ng0pISjl2yv+0QCQgEKCfJ63S2dHEiueOZ6KiWARFE3nZO1pTzjlr/KWGThcu\nkgKSuVn335ODhWBxHmiFFqWc46QkR+S96Sf3csB1vfZMVlt1IVxk3kjeZh7k\n4Yui0ujb3EzHVDAl49R1D94tGWsj7FxNB97rBtyXWFBYaNAcxElyPz9/BmPr\nRURQIasatkBpVfbd6+rNmvWnfWyufAfLpN/w4ayD4pe2tn+P56eh+K7cicy2\nncMe\r\n=//t3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "gitHead": "eab6c039888bd5d51f33dda7a98808564acfa938", "scripts": {"test": "nyc mocha test/*.js", "pretest": "standard", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "6.14.11", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "12.21.0", "dependencies": {"camelcase": "^3.0.0", "object.assign": "^4.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^10.0.0", "chai": "^3.5.0", "mocha": "^3.0.1", "standard": "^8.0.0", "coveralls": "^2.11.12", "standard-version": "^4.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_5.0.1_1615404062188_0.6496584798507412", "host": "s3://npm-registry-packages"}}, "20.2.7": {"name": "yargs-parser", "version": "20.2.7", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@20.2.7", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "61df85c113edfb5a7a4e36eb8aa60ef423cbc90a", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.7.tgz", "fileCount": 11, "integrity": "sha512-FiNkvbeHzB/syOjIUxFDCnhSfzAL8R5vs40MgLFBorXACCOAEaWu0gRZl14vG8MR9AOJIZbmkjhusqBYZ3HTHw==", "signatures": [{"sig": "MEQCIDkz0UAlQmgZ9nvzWuIS2KAqlorhFmIgzokwlmu9rZVcAiAuDRTvLr2janmCzDafetBGem5bj3LvQ7C9SgIvoG1hBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgSR8eCRA9TVsSAnZWagAAm1sP/j2e3CchAw8aPqgSBtwe\no5xW5nfh0+/M/jc/rTgYT3ilo25dvSv6IUCgd3PHydfyUGdkvEObuuxNi8xu\ncu/MCXDT0Re5ymSI7NJ/Rk0cfIadLgC35uW/KjUKEL4fCxG3WXJ2QYRJnfl6\nmMz13o+3j1mz/TUmUU0Jdlje3DCfqkyUacEkHYYk2rnQDRkut4TjCIwETH2A\nQcWpAOcX9tKv+HbOuEJfSCzAZIO//eKqEXvJST37lManY4xxEI4JmO26Hvy+\nFvSeuaNElSWr+qYF7cTFx+QWSso1tMYe4UqKUk+NpWUNtAueAIpaj1cEMhqP\niv/5BbtwAevINu/KrYi6h2XGwVTF4kt+R0GNWkuIiOAL8xIN42Wz2rRknM/p\niXgI/4ix7UoONjynuIXp1ifJzeJOhs7sM0ipJiMBfuzrmwvAGjkc6UQgTxQw\nNyNcWnQz5udFLmvAAahkj5C1CF/ouoqfaGhMkav2DL6/oNEFNstGxopdpe46\nn2ON6aHRbesKfeJbmwdsw53DK15CN+JeZ4tRdpr+duMxDgKJ27s4ZVoskOgY\nDXuLmGzwtMUEf+qdf1jlan+yH1AYMBVLwpbBsWp0JAg7YxgQnqSrft3OLIpg\nSAEhcYWkuntA6XUlT2qJDDDDrM0HEySpv1nRxrZ8HApox1zEowk6/j7RFVU8\nmSG0\r\n=+A1P\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "module": "./build/lib/index.js", "engines": {"node": ">=10"}, "exports": {".": [{"import": "./build/lib/index.js", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "gitHead": "a73f4356e5b994980d89c66222b4849c991ea754", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs", "test:browser": "start-server-and-test 'serve ./ -p 8080' http://127.0.0.1:8080/package.json 'node ./test/browser/yargs-test.cjs'", "test:typescript": "c8 mocha ./build/test/typescript/*.js", "pretest:typescript": "npm run pretest"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standardx": {"ignore": ["build"]}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.14.11", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "14.16.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^3.0.0", "chai": "^4.2.0", "mocha": "^8.0.0", "serve": "^11.3.2", "eslint": "^7.0.0", "rimraf": "^3.0.2", "rollup": "^2.22.1", "cross-env": "^7.0.2", "puppeteer": "^8.0.0", "standardx": "^7.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.11", "@types/node": "^10.0.3", "@types/mocha": "^8.0.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-import": "^2.20.1", "rollup-plugin-cleanup": "^3.1.1", "start-server-and-test": "^1.11.2", "@typescript-eslint/parser": "^3.10.1", "@wessberg/rollup-plugin-ts": "^1.2.28", "ts-transform-default-export": "^1.0.2", "@typescript-eslint/eslint-plugin": "^3.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_20.2.7_1615404829662_0.2738987988098238", "host": "s3://npm-registry-packages"}}, "15.0.2": {"name": "yargs-parser", "version": "15.0.2", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@15.0.2", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "7dcfd94904f2b1ce3053948ca809f7750f074e27", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-15.0.2.tgz", "fileCount": 6, "integrity": "sha512-5ts4tHdbL2CM+EXz5PysXLLvi1DCbLwf/XKuAi1su/HRTc2coxO8gxEONed/p+0d+NIVcYXxKXGuUuAZvgPICw==", "signatures": [{"sig": "MEYCIQCDNuNEv3OOWqtmpYTk/StrkjgNdeDP1zqqXXnBZyj5ggIhAJRiMQjinF2xSLbY4iX7ms9PpHOGn/aD7n9/RLc/isJR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgz8axCRA9TVsSAnZWagAANBUP/RSHShjx0c0MHH0rIdiG\nXCEItJ3qiZeUq8vY6kO/urePjSIR/gRC4g5eu8QWhAlRvA97zlk0QMAt81Ku\nqJeoEc/Tds/bxu4A1UDeWfukinF8x5bW5qHrQpXsRDiQC1mD9lC1OGLOHAF6\nlmBUDaibE2wGyFS3Mq01M97Bt7p3RUiWtSxLUGUFb0MK0DeV71QhWO4TcD1Q\n/9d+EVUQFPC5tBFoMD7VPtL6FSQTf1URS1Qdb/6/e6F4uOtNaOWrm4P+7I4t\nyip/P50pCCVUoXI3khJQklDt9qD2sa8CMTtqjIVpI9NhW7VttzYGAG//NhOW\nBoVUAtT5aim63ExUHrvzWmir9hXhdVPwsmXm32lbbE6cBaNYmehFSwfduRPK\nbgB+g7vI0wjHUHwt/aeH4I1PG5ZdwrvQ3ls57QZZzA5MHVXHeqQnn+/2IvWe\nPuplCP05YIz69q69VmTfjcMmXSotrJ23mfHzMoyUAOW1cWI/wuwLN7LAKX5I\nCI4ZgehN3/mrU+OjPg10A5id6WnXdKWiPmzgxKj3bvqa2W58skgeg8WcWBdt\n5+qIWyN6tqBqVnVkoDRkER9OwbEPpFGXP62D0bReV20EC2Xp9LG9v21/P6ts\nlaIjQ/Wiv8eaR0DeA5nsb5M6tU3/Ckzu7kPZFYHPUT6oyoRXLrkaII/ItfRI\n+DyY\r\n=AyL3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engine": {"node": ">=6"}, "gitHead": "49ea4ef042f7d9af78d7bb4090a5f37bff067bc3", "scripts": {"test": "nyc mocha test/*.js", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "6.14.12", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^14.1.0", "chai": "^4.2.0", "mocha": "^5.2.0", "standard": "^12.0.1", "coveralls": "^3.0.2", "standard-version": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_15.0.2_1624229552794_0.03604810751077081", "host": "s3://npm-registry-packages"}}, "15.0.3": {"name": "yargs-parser", "version": "15.0.3", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@15.0.3", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "316e263d5febe8b38eef61ac092b33dfcc9b1115", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-15.0.3.tgz", "fileCount": 6, "integrity": "sha512-/MVEVjTXy/cGAjdtQf8dW3V9b97bPN7rNn8ETj6BmAQL7ibC7O1Q9SPJbGjgh3SlwoBNXMzj/ZGIj8mBgl12YA==", "signatures": [{"sig": "MEUCIQC1UZTq/3c7xH3/PEDr7F4ofZ0VLNC8midxTabttQXdlgIgHi1QrwjvzvuduoOxKmu4RCDGjJEXPwrlwJULKDqWLdI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62866, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgz8bJCRA9TVsSAnZWagAAUCEP/23o+ACHD0xrrK/iU2rM\nfY1K59lL4ueZNG4rrePXXp7inJ9yRk9k+qCiFsSEjNJZQr9rVL7Xty4pe/VQ\nkL4Lg9HAZir1Ye/uw6MxH+FbvU7QE6nOKKoTYfsrUqC/x/U+oT3sm7mPH8PL\nuuMamiGbB1q1yOiR2FnH5QW5xf5h/DhekEB1kw5pCrhKeaaIIWJ/ubRXl3Ui\nid81RMdYuM/C+0JBf7e9fRDorhq2fg7R9l7MaLnzz4YUc+C7kr6u6xwaB2S4\nNL0EkyeWaK3LlhOuvX012kobTo3Jef+PZebywAJEnrzAdWQCQpcbhXSE4jkC\nvuk92apOV/3GuSnj3Y40GseBJsWNxV8dwfp+tpmVqUxoS2b4OAgunLCW2Gpp\nCdOSoF7tzaPsN0g2ByyDlUiPf0/NvL6DDDsf/n4dRX5sTMlFMYi7AVRVhMq8\nHLpUYY9qSEjkRpKeOyGTwnc7DH4oBm9q8OMSfuLaRW5eiEV6tvxJsD/iIveB\nOD+8ADpIbcF+6baeSpMpVteJStT/T8J6+zfV7FHjmpanKFoRm7/YVsTgVuxd\nKWiJz09ORNrAPxfXcxj13r5Qd9i5ygu9PGoavvkX3TY7YXYQ9k8yG/ZH2EF4\npxktwDI+MZp+kwZ+JDXaV3eM/eAmr7zGF22BOc3DfzvBPp2qEqcwKzRZx0PM\n3kQS\r\n=Rr2b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "index.js", "engine": {"node": ">=6"}, "gitHead": "50a7aeb43f3f723c74e043c355ef474ff7db644c", "scripts": {"test": "nyc mocha test/*.js", "release": "standard-version", "coverage": "nyc report --reporter=text-lcov | coveralls", "posttest": "standard"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/yargs/yargs-parser.git"}, "_npmVersion": "6.14.12", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"nyc": "^14.1.0", "chai": "^4.2.0", "mocha": "^5.2.0", "standard": "^12.0.1", "coveralls": "^3.0.2", "standard-version": "^6.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_15.0.3_1624229576730_0.869088214577121", "host": "s3://npm-registry-packages"}}, "20.2.9": {"name": "yargs-parser", "version": "20.2.9", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@20.2.9", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "2eb7dc3b0289718fc295f362753845c41a0c94ee", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.9.tgz", "fileCount": 11, "integrity": "sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==", "signatures": [{"sig": "MEUCIDTn054qirqCc/IImwD1wTaQUGP++4PNecIe+4LQdLtyAiEAghSBF4DUMDVdRHdw+6z/Do46deGXT+m3RjflKTrrKKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 124417, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgz9U4CRA9TVsSAnZWagAALGsP/R7f+2Z3/zu0cgjEDlR4\nnVGJLTB7nL2yW175yVWhnZqa2sIPVnkZ7z0yrlPUttVWtKgn+K/1Ew+5pHUa\ndNfyNA07csIhGgqzr9M2NwHTgcWqJOabJVsgBmZQHJRWIKn2bpPV/wBewVGR\n3EkQ2TcNOf2CIwHpq/ufl15DnSL1SYNIwSfUfKZXczjh14z3goQ7fGzmmwfJ\nDwmIdF1jzd/BcRSBvTRlHNPQm6EYZHfXxn7XHLgS7kD3aq+QAZ4YGLqE8/+1\n+yZmaDZaE+YC6NWdZriWMFM6Oqe+7KPIZL95SzEI4SZ9DlqbI537LPBlM1CT\nilUA8ZNtihG9H0F9MKX0eW6RI0Cs0/eTO66rdkxdmUOtwfzOvBfZJahGiMG+\ncPri6r7hIVw4XV6FPb2FvSd2pDeWwdDLJ3d86ENsRpq9X6ARcmsMoRnH0pPd\n5Qj7jz1I5LdFtKQNF91MkM++ficCUy/1Q1UpyRIqIhVA66oj2XyC8FxscgHv\ncdw+r0NUYyrD90tX4jdKFNQHjuP4Uu6se634Xtb1/3NJhyZi6lR+t1BbxQDV\nEZl/eGtXS7IjM9bRwMNre2A0NTly56uIKA/G/UDFd7gcUN+agvd8vwKXJ9U0\nHgN297re1xR2hsKAKi8zmaHhxhw5P4P19DgFZC2x1+Ba3gdqq+xVqe8WjB42\nPZGT\r\n=mEyy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "module": "./build/lib/index.js", "engines": {"node": ">=10"}, "exports": {".": [{"import": "./build/lib/index.js", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "gitHead": "23cb0f3b6e91bf869a84e2539f314e03e74d4f5d", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs", "test:browser": "start-server-and-test 'serve ./ -p 8080' http://127.0.0.1:8080/package.json 'node ./test/browser/yargs-test.cjs'", "test:typescript": "c8 mocha ./build/test/typescript/*.js", "pretest:typescript": "npm run pretest"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standardx": {"ignore": ["build"]}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.14.12", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "14.16.1", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^3.0.0", "chai": "^4.2.0", "mocha": "^9.0.0", "serve": "^12.0.0", "eslint": "^7.0.0", "rimraf": "^3.0.2", "rollup": "^2.22.1", "cross-env": "^7.0.2", "puppeteer": "^10.0.0", "standardx": "^7.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.11", "@types/node": "^14.0.0", "@types/mocha": "^8.0.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-import": "^2.20.1", "rollup-plugin-cleanup": "^3.1.1", "start-server-and-test": "^1.11.2", "@typescript-eslint/parser": "^3.10.1", "@wessberg/rollup-plugin-ts": "^1.2.28", "ts-transform-default-export": "^1.0.2", "@typescript-eslint/eslint-plugin": "^3.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_20.2.9_1624233271838_0.5676894447660543", "host": "s3://npm-registry-packages"}}, "21.0.0": {"name": "yargs-parser", "version": "21.0.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@21.0.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "a485d3966be4317426dd56bdb6a30131b281dc55", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.0.0.tgz", "fileCount": 11, "integrity": "sha512-z9kApYUOCwoeZ78rfRYYWdiU/iNL6mwwYlkkZfJoyMR1xps+NEBX5X7XmRpxkZHhXJ6+Ey00IwKxBBSW9FIjyA==", "signatures": [{"sig": "MEQCIHSZKa0r7IKubLqW7KdPp522IhXtQpAuB1KNCdHp9vCFAiAfyBiBaCatA7G2VhfC+XlBh6t1sJ7PYW8DnlorESvOng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126036, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhkxwgCRA9TVsSAnZWagAA+9sQAJhTMdjbO5W5RKVBrPtv\nSPfT8zHee+wFfjDT56bdK3b4jX5U7lVk9LTG3fVzrhshmRioYFUsQXti3zA9\nUVdFKKi6oKE/5+g7sTBbGELPeKwLbpAcWLa4uqpWgKaZe3Ni/Xfs5zGwFRbM\nzypKcQYgFsuk9mfCs0VtwP4hPpDB52laUuansGQHOIbMabXarsWlYZUELNKQ\ne5laFw8u3qJFPYDMjSKEcsUMOq7tEWluInsBBtdfsRdp5ToKn1uTmvCMWqPM\nguljcZ0MNklyfEQ0obkZUtB5gn9RxbvVWPnyvbRyi7Lg14UgOpkXRXLUWxrW\n/d5hXkPivQdwnmfJ3IUK+Iazjd768fKFsvQm5s76TtnAjuo6Bw4vkD0Dywlz\n1JdAfKACtGCbHxfBuzrhtuMD/ObVEznK3ofUGB06I/Y27PF8O2IFlKsYwrXq\nfUJXRtIYSzBwo2M0P+QLkjvUmMXrY6wKO7TJOPSsvpws2CmkakooXOJtlBMw\nAAHE+MmCYo2vkytQTcIr+qq1oA4R7LGJFqr/RjloivBefUKbDV93c7sTktxE\nQNt1WpruTimD++LKk91JBNROZ/cciEYfj/fICewDmZ4K69nXwEC9hmTlLB8w\nv7QjsF3qon/ZHKoQtXNdmUT64oimzV0eHoSGWdRBkfmUTs7GROsQw+GpWtbf\n8n1L\r\n=+x8S\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "module": "./build/lib/index.js", "engines": {"node": ">=12"}, "exports": {".": [{"import": "./build/lib/index.js", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "gitHead": "46a9f2fedd031bf8208f6f7bb824ecc0b665fff9", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "test:esm": "c8 --reporter=text --reporter=html mocha test/*.mjs", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs", "test:browser": "start-server-and-test 'serve ./ -p 8080' http://127.0.0.1:8080/package.json 'node ./test/browser/yargs-test.cjs'", "test:typescript": "c8 mocha ./build/test/typescript/*.js", "pretest:typescript": "npm run pretest"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standardx": {"ignore": ["build"]}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "14.18.1", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^3.0.0", "chai": "^4.2.0", "mocha": "^9.0.0", "serve": "^13.0.0", "eslint": "^7.0.0", "rimraf": "^3.0.2", "rollup": "^2.22.1", "cross-env": "^7.0.2", "puppeteer": "^11.0.0", "standardx": "^7.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.11", "@types/node": "^16.11.4", "@types/mocha": "^9.0.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-import": "^2.20.1", "rollup-plugin-cleanup": "^3.1.1", "start-server-and-test": "^1.11.2", "@typescript-eslint/parser": "^3.10.1", "@wessberg/rollup-plugin-ts": "^1.2.28", "ts-transform-default-export": "^1.0.2", "@typescript-eslint/eslint-plugin": "^3.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_21.0.0_1637030944005_0.9326063880576452", "host": "s3://npm-registry-packages"}}, "21.0.1": {"name": "yargs-parser", "version": "21.0.1", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@21.0.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "0267f286c877a4f0f728fceb6f8a3e4cb95c6e35", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.0.1.tgz", "fileCount": 11, "integrity": "sha512-9BK1jFpLzJROCI5TzwZL/TU4gqjK5xiHV/RfWLOahrjAko/e4DJkRDZQXfvqAsiZzzYhgAzbgz6lg48jcm4GLg==", "signatures": [{"sig": "MEQCIGl3nRigT59SHtykeHjPzuPGKnF+avv4Dd0WUr99zFxzAiA8refEPwgNZ4Tt7SmeNVnadue7ySr8LZeg6lMEnjVkAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126343, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiG5RqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpW9g/+J08WJtDXKJfQK170eH7OQ0OHgr1eQyB3iyT6eAreLsZxyL2R\r\nvtKBRMMAI3N4hhWbkeGXvkS5mq5zE4sBGF1ikebyKXr/jEERXFCOabZl86ty\r\nbQ9rPO8NJ0s+YzYPo1Khv1z1alpVFEm41v+66CZu6IeRBmoFDMqW5mSnkSGo\r\n4Uu5a9ySCH78Yi2Rg4OO2n9X1ZJmQ80oVlBknKknjcf2p04D+h8zbWyejCuG\r\nsbZJbqYHpcU38FwPgSw7s/pLY/E7/oqcuA9b3o3tLEYuFflRk79i0OScK7fx\r\nLKwdNJqRqqAY04qWGGs/3jdH/FbFQhWPfZWHsVn0FRcJCoo99josdZiYMlYz\r\nADGB9ZKSh7xX6cUqurGsvg0+s5QNWsWuJZHiPqqLO6znQXVg2TbMznpHcA3+\r\nOuqlQ6rPG8f2lJfsIrQaP8JiKrdsDsF6VH2nFdtvYdAaNcgFycyZN3n1oOCS\r\nmhPUKmcXkJ98nIrGDHpYk5eqpoEiRv3QGaFRorJkKS6VaSMCpaLoby7LLnw+\r\nV48iOPdHHDLQyzFSAo+PiZqAjroXM5A0E1DoPj/m1I8rmVkrj6N6lAZ9N/6w\r\nUY1/roSVZSMs27sqUoNuLYNAXLPNyiACgaspnl8vhSdgs5vR5CsK5QoZwbVe\r\n5sg6meoLxKUcDadeOE2QjwDfPbkckS3UHP8=\r\n=5dPm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "module": "./build/lib/index.js", "engines": {"node": ">=12"}, "exports": {".": [{"import": "./build/lib/index.js", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "gitHead": "fd496b68b7d6439531901396d37275a20750cce4", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "test:esm": "c8 --reporter=text --reporter=html mocha test/*.mjs", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs", "test:browser": "start-server-and-test 'serve ./ -p 8080' http://127.0.0.1:8080/package.json 'node ./test/browser/yargs-test.cjs'", "test:typescript": "c8 mocha ./build/test/typescript/*.js", "pretest:typescript": "npm run pretest"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standardx": {"ignore": ["build"]}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.14.16", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "14.19.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^3.0.0", "chai": "^4.2.0", "mocha": "^9.0.0", "serve": "^13.0.0", "eslint": "^7.0.0", "rimraf": "^3.0.2", "rollup": "^2.22.1", "cross-env": "^7.0.2", "puppeteer": "^13.4.0", "standardx": "^7.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.11", "@types/node": "^16.11.4", "@types/mocha": "^9.0.0", "rollup-plugin-ts": "^2.0.5", "eslint-plugin-node": "^11.0.0", "eslint-plugin-import": "^2.20.1", "rollup-plugin-cleanup": "^3.1.1", "start-server-and-test": "^1.11.2", "@typescript-eslint/parser": "^3.10.1", "ts-transform-default-export": "^1.0.2", "@typescript-eslint/eslint-plugin": "^3.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_21.0.1_1645974634220_0.465545989946186", "host": "s3://npm-registry-packages"}}, "21.1.0": {"name": "yargs-parser", "version": "21.1.0", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@21.1.0", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "a11d06a3bf57f064e951aa3ef55fcf3a5705f876", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.0.tgz", "fileCount": 12, "integrity": "sha512-xzm2t63xTV/f7+bGMSRzLhUNk1ajv/tDoaD5OeGyC3cFo2fl7My9Z4hS3q2VdQ7JaLvTxErO8Jp5pRIFGMD/zg==", "signatures": [{"sig": "MEYCIQD5Jv7MRyfOPJNPyXnArk4fQJeSeUQ4eV/j4D7Cf4sfgwIhAMukPmrqMIJfj8htsSzEFvu+MnI7OKu71r5EP+sal7pg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134475, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6st4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpbtxAAgrcD+M7j1HptVLogk2MKd44oOjoYvZzbum4FUTgUswJ8FX68\r\nKAP4VhmvPYhpWGBPDDtG0weJ6adXzYwyzaN01Rjj6aGenFFJcwPLgYk+mpLP\r\nBK0xugIJ6gicz8llwuVrcsJVB4GklQHCOYu1fqTQbxtRlpup9uRJKBd5s3Gy\r\nEQSef+O2tXWCBBg7075e4PYmRhyoTLw9I0Hf2KFyfnKBn5SBgKV6qYBMijVx\r\nGXmCrLljWHEA8FciXOOztmx/7uRiTniPDcixoj+XdpIB+da5iXwlsZykWQb4\r\n5oPUkbad7Rx8pKm19TvMCbLNYKBkrfShQ6ACcgcZS9FMD4/GclZvNumXmxt/\r\nLpZ5yQWSL1ssXRzQNX9daN8qi6dW2de87or0jSSJxHVdI5Em2vqJKJP/bAuD\r\n97tcP4aYxJ1G1DQU5FjI8tI72+Oi7v8eOV3tR/uuS/6Cg9SwDR/b1TrZC67v\r\nsnrrK4WireGhkdRUV8/uIchcr+KUgMty/4G+dvCN5+CaHEQK/1o30a0hsz3C\r\nxVh5ipzYMvLZsrQ2nAoCRf6eWNZNdaCC1n8CbaCkd5wSKsvldq+m4ORwHIqu\r\nVlrfZqwjUFX4ok350fD7a2uYw5yEbreAZtq329qJ7zSU0YSZ6ItNsANEiyXK\r\nfXQSsknCafBGhOiif2LzyTCj7lPA6BkBQqQ=\r\n=8iW+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "module": "./build/lib/index.js", "engines": {"node": ">=12"}, "exports": {".": [{"import": "./build/lib/index.js", "require": "./build/index.cjs"}, "./build/index.cjs"], "./browser": ["./browser.js"]}, "gitHead": "20b352b3b874c1e1e306c798669ee2049b1994f5", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "test:esm": "c8 --reporter=text --reporter=html mocha test/*.mjs", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs", "test:browser": "start-server-and-test 'serve ./ -p 8080' http://127.0.0.1:8080/package.json 'node ./test/browser/yargs-test.cjs'", "test:typescript": "c8 mocha ./build/test/typescript/*.js", "pretest:typescript": "npm run pretest"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standardx": {"ignore": ["build"]}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "14.20.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^3.0.0", "chai": "^4.2.0", "mocha": "^10.0.0", "serve": "^14.0.0", "eslint": "^7.0.0", "rimraf": "^3.0.2", "rollup": "^2.22.1", "cross-env": "^7.0.2", "puppeteer": "^16.0.0", "standardx": "^7.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.11", "@types/node": "^16.11.4", "@types/mocha": "^9.0.0", "rollup-plugin-ts": "^3.0.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-import": "^2.20.1", "rollup-plugin-cleanup": "^3.1.1", "start-server-and-test": "^1.11.2", "@typescript-eslint/parser": "^3.10.1", "ts-transform-default-export": "^1.0.2", "@typescript-eslint/eslint-plugin": "^3.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_21.1.0_1659554680488_0.4365370062091616", "host": "s3://npm-registry-packages"}}, "21.1.1": {"name": "yargs-parser", "version": "21.1.1", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "_id": "yargs-parser@21.1.1", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "homepage": "https://github.com/yargs/yargs-parser#readme", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "dist": {"shasum": "9096bceebf990d21bb31fa9516e0ede294a77d35", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz", "fileCount": 11, "integrity": "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==", "signatures": [{"sig": "MEQCIDraKLgLMzz98HWUYI2w8Fw5U2vUTXKrKpV7q+Jipd2pAiB5GNXkF2TfGIfcu5p4sR9Hmo6klCm+YKpaeNyuxbq5cw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128470, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7DaHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrEkg/+NPt0MF8maSskX5GhvW8oIDaVq7uxG6WYKp21El0ItRaj8L+g\r\nlKW//DI6CV7TT8QzrTJbo8UFPj1nx/sq3Q4erbHXPJrTo/VxVZdLjPC3wIws\r\n/B1cA8ArQoNPhpAY0pWIiWrYYwuwaJHMhFgjClEMPARe3/RSjSaDJ5tdZx7g\r\n06g6keFxXs++E2c/nrsszA5+gHpVDN5x1LZIQWy5n23aVQVqqrQdgeX6NVaA\r\nOU3vR6s0Et93i6HZc64+3yJcoCJdsFA1wlfKfFJGlaB5HySlIiBzPP0LC99g\r\nal+6GE8sjyVl41rfh471Loq+cegrWdN34PEDXA65WH2ov/Esc+Cs3qB4eD5Y\r\nDuLabxECCu1Gb8+Y+evq0m6pWg06yrLDIk4QYIzxxTm4a/4RjhOx6ESw4iUP\r\nkGD6xCV0hkZ928qKs/HCbGSxy4Lf5ZeqDtLmjsAPM6vNLikWP1ey0TP6Q420\r\nrf53blbwMSHEtKX1njZe4/xAzHdfVbdw73T1RN6fRp1NlJu0kmzXhx6TgTND\r\nXn2Y/KHt6Y4Kz0h0kCX1KTj9DjH8FGJtLq2MzTuP9wsu+CdRR2kjLfwuD2XO\r\ni7cFiV5qUDAR4aVtbjs4ursToH2TWUI7un71mpNdtKXLwHlTl3vBNu4USfjQ\r\npesL2B7fBOl6y5OilVZUlcTjbd3kYefyKWI=\r\n=OXwo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.cjs", "type": "module", "module": "./build/lib/index.js", "engines": {"node": ">=12"}, "exports": {".": [{"import": "./build/lib/index.js", "require": "./build/index.cjs"}, "./build/index.cjs"], "./browser": ["./browser.js"]}, "gitHead": "ba6421b843ea5271d84489168314bba26ffa757f", "scripts": {"fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "test": "c8 --reporter=text --reporter=html mocha test/*.cjs", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "coverage": "c8 report --check-coverage", "test:esm": "c8 --reporter=text --reporter=html mocha test/*.mjs", "build:cjs": "rollup -c", "precompile": "<PERSON><PERSON><PERSON> build", "postcompile": "npm run build:cjs", "test:browser": "start-server-and-test 'serve ./ -p 8080' http://127.0.0.1:8080/package.json 'node ./test/browser/yargs-test.cjs'", "test:typescript": "c8 mocha ./build/test/typescript/*.js", "pretest:typescript": "npm run pretest"}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "standardx": {"ignore": ["build"]}, "repository": {"url": "git+https://github.com/yargs/yargs-parser.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "the mighty option parser used by yargs", "directories": {}, "_nodeVersion": "14.20.0", "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.3.0", "gts": "^3.0.0", "chai": "^4.2.0", "mocha": "^10.0.0", "serve": "^14.0.0", "eslint": "^7.0.0", "rimraf": "^3.0.2", "rollup": "^2.22.1", "cross-env": "^7.0.2", "puppeteer": "^16.0.0", "standardx": "^7.0.0", "typescript": "^4.0.0", "@types/chai": "^4.2.11", "@types/node": "^16.11.4", "@types/mocha": "^9.0.0", "rollup-plugin-ts": "^3.0.2", "eslint-plugin-node": "^11.0.0", "eslint-plugin-import": "^2.20.1", "rollup-plugin-cleanup": "^3.1.1", "start-server-and-test": "^1.11.2", "@typescript-eslint/parser": "^3.10.1", "ts-transform-default-export": "^1.0.2", "@typescript-eslint/eslint-plugin": "^3.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/yargs-parser_21.1.1_1659647623212_0.499435369846577", "host": "s3://npm-registry-packages"}}, "22.0.0": {"name": "yargs-parser", "version": "22.0.0", "description": "the mighty option parser used by yargs", "main": "build/lib/index.js", "exports": {".": [{"import": "./build/lib/index.js"}, "./build/lib/index.js"], "./browser": ["./browser.js"]}, "type": "module", "module": "./build/lib/index.js", "scripts": {"check": "gts lint", "fix": "gts fix", "pretest": "rimraf build && tsc -p tsconfig.test.json", "test": "c8 --reporter=text --reporter=html mocha test/*.mjs", "test:browser": "start-server-and-test 'serve ./ -p 8080' http://127.0.0.1:8080/package.json 'node ./test/browser/yargs-test.cjs'", "pretest:typescript": "npm run pretest", "test:typescript": "c8 mocha ./build/test/typescript/*.js", "coverage": "c8 report --check-coverage", "precompile": "<PERSON><PERSON><PERSON> build", "compile": "tsc", "prepare": "npm run compile"}, "repository": {"type": "git", "url": "git+https://github.com/yargs/yargs-parser.git"}, "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "devDependencies": {"@babel/eslint-parser": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@types/chai": "^5.2.1", "@types/mocha": "^10.0.10", "@types/node": "^22.15.3", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.31.1", "c8": "^10.1.3", "chai": "^5.2.0", "cross-env": "^7.0.2", "eslint": "^8.57.1", "gts": "^5.3.1", "mocha": "^11.1.0", "puppeteer": "^24.6.1", "rimraf": "^6.0.1", "serve": "^14.0.0", "start-server-and-test": "^2.0.11", "typescript": "^5.8.3"}, "engines": {"node": "^20.19.0 || ^22.12.0 || >=23"}, "gitHead": "66f0bb2d2c8a2c9689489784cfe2e5128b0abfc2", "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "homepage": "https://github.com/yargs/yargs-parser#readme", "_id": "yargs-parser@22.0.0", "_nodeVersion": "14.21.3", "_npmVersion": "6.14.18", "dist": {"integrity": "sha512-rwu/ClNdSMpkSrUb+d6BRsSkLUq1fmfsY6TOpYzTwvwkg1/NRG85KBy3kq++A8LKQwX6lsu+aWad+2khvuXrqw==", "shasum": "87b82094051b0567717346ecd00fd14804b357c8", "tarball": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-22.0.0.tgz", "fileCount": 10, "unpackedSize": 85628, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIGFTYObKkOhRH7u+HPHxFvWKGBHHlL3TL7tOLtyy3Jv3AiBbHto0FXWi+xLefaz4E1xMcailvFwq2/QqJ/Djn3ggkA=="}]}, "_npmUser": {"name": "oss-bot", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/yargs-parser_22.0.0_1748290320654_0.07480536797615156"}, "_hasShrinkwrap": false}}, "time": {"created": "2016-01-23T21:12:29.644Z", "modified": "2025-05-26T20:12:01.075Z", "1.0.0": "2016-01-23T21:12:29.644Z", "1.1.0": "2016-01-24T22:43:03.382Z", "1.1.1-alpha": "2016-01-29T06:18:15.006Z", "1.1.1-alpha2": "2016-01-30T20:29:52.493Z", "1.1.1-alpha3": "2016-02-01T06:13:57.343Z", "2.0.0": "2016-02-06T20:39:19.897Z", "2.1.0": "2016-02-14T09:04:02.449Z", "2.1.1": "2016-02-23T06:54:30.923Z", "2.1.2": "2016-03-20T19:04:12.726Z", "2.2.0": "2016-03-30T06:15:24.185Z", "2.4.0-next": "2016-04-11T03:04:27.243Z", "2.4.0": "2016-04-11T06:04:03.901Z", "2.4.1": "2016-07-16T22:51:31.076Z", "3.1.0": "2016-08-09T06:26:17.564Z", "3.2.0": "2016-08-13T19:53:29.492Z", "4.0.0": "2016-09-26T05:38:41.527Z", "4.0.1": "2016-09-30T06:36:16.800Z", "4.0.2": "2016-09-30T06:52:18.892Z", "4.1.0": "2016-11-07T06:31:50.418Z", "4.2.0": "2016-12-01T18:49:23.364Z", "4.2.1": "2017-01-02T19:42:38.707Z", "4.2.1-candidate.0": "2017-01-06T22:51:40.976Z", "4.2.1-candidate.1": "2017-01-06T23:22:35.884Z", "5.0.0": "2017-02-18T19:58:51.724Z", "6.0.0": "2017-05-01T00:52:33.251Z", "6.0.1": "2017-05-01T05:57:19.165Z", "7.0.0": "2017-05-02T05:59:44.561Z", "8.0.0": "2017-10-05T06:22:05.819Z", "8.1.0": "2017-12-20T06:20:14.482Z", "9.0.0": "2018-01-20T22:47:43.499Z", "9.0.1": "2018-01-20T23:03:40.481Z", "9.0.2": "2018-01-20T23:23:44.868Z", "10.0.0": "2018-04-04T02:10:11.110Z", "10.1.0": "2018-06-29T05:14:50.431Z", "11.0.0": "2018-10-06T22:52:28.232Z", "11.1.0": "2018-11-10T00:30:04.238Z", "11.1.1": "2018-11-19T23:34:24.250Z", "12.0.0": "2019-01-29T00:49:36.441Z", "13.0.0-candidate.0": "2019-02-02T20:43:45.492Z", "13.0.0": "2019-02-02T21:30:58.258Z", "13.1.0": "2019-05-05T21:33:05.139Z", "13.1.1": "2019-06-10T01:17:59.099Z", "14.0.0": "2019-09-06T19:36:28.730Z", "15.0.0": "2019-10-07T00:09:18.911Z", "16.0.0": "2019-10-27T02:54:25.234Z", "16.1.0": "2019-11-01T22:41:05.473Z", "17.0.0": "2020-02-10T03:52:11.910Z", "17.0.1": "2020-02-29T21:03:27.629Z", "17.1.0": "2020-03-01T01:35:41.934Z", "18.0.0": "2020-03-02T06:01:50.935Z", "18.1.0": "2020-03-07T19:43:04.413Z", "18.1.1-beta.0": "2020-03-12T18:20:11.828Z", "15.0.1": "2020-03-13T20:56:48.713Z", "13.1.2": "2020-03-13T21:21:02.921Z", "18.1.1": "2020-03-16T07:19:48.621Z", "18.1.2": "2020-03-26T17:14:28.160Z", "18.1.3": "2020-04-16T20:13:18.388Z", "5.0.0-security.0": "2020-05-22T00:32:09.868Z", "19.0.0-beta.0": "2020-07-19T00:00:50.663Z", "19.0.0-beta.1": "2020-07-19T02:18:11.056Z", "19.0.0-beta.2": "2020-08-04T05:24:23.841Z", "19.0.0-beta.3": "2020-08-06T04:16:32.347Z", "19.0.0": "2020-08-09T04:16:13.833Z", "19.0.1": "2020-08-09T04:27:39.335Z", "19.0.4": "2020-08-27T05:38:54.838Z", "20.0.0": "2020-09-09T17:53:36.447Z", "20.1.0": "2020-09-20T04:34:07.610Z", "20.2.0": "2020-09-21T02:54:08.617Z", "20.2.1": "2020-10-01T18:23:31.154Z", "20.2.2": "2020-10-14T19:00:24.131Z", "20.2.3": "2020-10-16T15:45:15.708Z", "20.2.4": "2020-11-09T01:50:36.736Z", "20.2.5": "2021-02-13T20:13:14.829Z", "20.2.6": "2021-02-22T02:46:50.058Z", "5.0.1": "2021-03-10T19:21:02.408Z", "20.2.7": "2021-03-10T19:33:49.831Z", "15.0.2": "2021-06-20T22:52:32.960Z", "15.0.3": "2021-06-20T22:52:56.892Z", "20.2.9": "2021-06-20T23:54:31.941Z", "21.0.0": "2021-11-16T02:49:04.176Z", "21.0.1": "2022-02-27T15:10:34.404Z", "21.1.0": "2022-08-03T19:24:40.722Z", "21.1.1": "2022-08-04T21:13:43.411Z", "22.0.0": "2025-05-26T20:12:00.864Z"}, "bugs": {"url": "https://github.com/yargs/yargs-parser/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "ISC", "homepage": "https://github.com/yargs/yargs-parser#readme", "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "repository": {"type": "git", "url": "git+https://github.com/yargs/yargs-parser.git"}, "description": "the mighty option parser used by yargs", "maintainers": [{"name": "bcoe", "email": "<EMAIL>"}, {"name": "nexdrew", "email": "<EMAIL>"}, {"name": "oss-bot", "email": "<EMAIL>"}], "readme": "# yargs-parser\n\n![ci](https://github.com/yargs/yargs-parser/workflows/ci/badge.svg)\n[![NPM version](https://img.shields.io/npm/v/yargs-parser.svg)](https://www.npmjs.com/package/yargs-parser)\n[![Conventional Commits](https://img.shields.io/badge/Conventional%20Commits-1.0.0-yellow.svg)](https://conventionalcommits.org)\n![nycrc config on GitHub](https://img.shields.io/nycrc/yargs/yargs-parser)\n\nThe mighty option parser used by [yargs](https://github.com/yargs/yargs).\n\nvisit the [yargs website](http://yargs.js.org/) for more examples, and thorough usage instructions.\n\n<img width=\"250\" src=\"https://raw.githubusercontent.com/yargs/yargs-parser/main/yargs-logo.png\">\n\n## Example\n\n```sh\nnpm i yargs-parser --save\n```\n\n```js\nconst argv = require('yargs-parser')(process.argv.slice(2))\nconsole.log(argv)\n```\n\n```console\n$ node example.js --foo=33 --bar hello\n{ _: [], foo: 33, bar: 'hello' }\n```\n\n_or parse a string!_\n\n```js\nconst argv = require('yargs-parser')('--foo=99 --bar=33')\nconsole.log(argv)\n```\n\n```console\n{ _: [], foo: 99, bar: 33 }\n```\n\nConvert an array of mixed types before passing to `yargs-parser`:\n\n```js\nconst parse = require('yargs-parser')\nparse(['-f', 11, '--zoom', 55].join(' '))   // <-- array to string\nparse(['-f', 11, '--zoom', 55].map(String)) // <-- array of strings\n```\n\n## Deno Example\n\nAs of `v19` `yargs-parser` supports [Deno](https://github.com/denoland/deno):\n\n```typescript\nimport parser from \"https://deno.land/x/yargs_parser/deno.ts\";\n\nconst argv = parser('--foo=99 --bar=9987930', {\n  string: ['bar']\n})\nconsole.log(argv)\n```\n\n## ESM Example\n\nAs of `v19` `yargs-parser` supports ESM (_both in Node.js and in the browser_):\n\n**Node.js:**\n\n```js\nimport parser from 'yargs-parser'\n\nconst argv = parser('--foo=99 --bar=9987930', {\n  string: ['bar']\n})\nconsole.log(argv)\n```\n\n**Browsers:**\n\n```html\n<!doctype html>\n<body>\n  <script type=\"module\">\n    import parser from \"https://unpkg.com/yargs-parser@19.0.0/browser.js\";\n\n    const argv = parser('--foo=99 --bar=9987930', {\n      string: ['bar']\n    })\n    console.log(argv)\n  </script>\n</body>\n```\n\n## API\n\n### parser(args, opts={})\n\nParses command line arguments returning a simple mapping of keys and values.\n\n**expects:**\n\n* `args`: a string or array of strings representing the options to parse.\n* `opts`: provide a set of hints indicating how `args` should be parsed:\n  * `opts.alias`: an object representing the set of aliases for a key: `{alias: {foo: ['f']}}`.\n  * `opts.array`: indicate that keys should be parsed as an array: `{array: ['foo', 'bar']}`.<br>\n    Indicate that keys should be parsed as an array and coerced to booleans / numbers:<br>\n    `{array: [{ key: 'foo', boolean: true }, {key: 'bar', number: true}]}`.\n  * `opts.boolean`: arguments should be parsed as booleans: `{boolean: ['x', 'y']}`.\n  * `opts.coerce`: provide a custom synchronous function that returns a coerced value from the argument provided\n    (or throws an error). For arrays the function is called only once for the entire array:<br>\n    `{coerce: {foo: function (arg) {return modifiedArg}}}`.\n  * `opts.config`: indicate a key that represents a path to a configuration file (this file will be loaded and parsed).\n  * `opts.configObjects`: configuration objects to parse, their properties will be set as arguments:<br>\n    `{configObjects: [{'x': 5, 'y': 33}, {'z': 44}]}`.\n  * `opts.configuration`: provide configuration options to the yargs-parser (see: [configuration](#configuration)).\n  * `opts.count`: indicate a key that should be used as a counter, e.g., `-vvv` = `{v: 3}`.\n  * `opts.default`: provide default values for keys: `{default: {x: 33, y: 'hello world!'}}`.\n  * `opts.envPrefix`: environment variables (`process.env`) with the prefix provided should be parsed.\n  * `opts.narg`: specify that a key requires `n` arguments: `{narg: {x: 2}}`.\n  * `opts.normalize`: `path.normalize()` will be applied to values set to this key.\n  * `opts.number`: keys should be treated as numbers.\n  * `opts.string`: keys should be treated as strings (even if they resemble a number `-x 33`).\n\n**returns:**\n\n* `obj`: an object representing the parsed value of `args`\n  * `key/value`: key value pairs for each argument and their aliases.\n  * `_`: an array representing the positional arguments.\n  * [optional] `--`:  an array with arguments after the end-of-options flag `--`.\n\n### require('yargs-parser').detailed(args, opts={})\n\nParses a command line string, returning detailed information required by the\nyargs engine.\n\n**expects:**\n\n* `args`: a string or array of strings representing options to parse.\n* `opts`: provide a set of hints indicating how `args`, inputs are identical to `require('yargs-parser')(args, opts={})`.\n\n**returns:**\n\n* `argv`: an object representing the parsed value of `args`\n  * `key/value`: key value pairs for each argument and their aliases.\n  * `_`: an array representing the positional arguments.\n  * [optional] `--`:  an array with arguments after the end-of-options flag `--`.\n* `error`: populated with an error object if an exception occurred during parsing.\n* `aliases`: the inferred list of aliases built by combining lists in `opts.alias`.\n* `newAliases`: any new aliases added via camel-case expansion:\n  * `boolean`: `{ fooBar: true }`\n* `defaulted`: any new argument created by `opts.default`, no aliases included.\n  * `boolean`: `{ foo: true }`\n* `configuration`: given by default settings and `opts.configuration`.\n\n<a name=\"configuration\"></a>\n\n### Configuration\n\nThe yargs-parser applies several automated transformations on the keys provided\nin `args`. These features can be turned on and off using the `configuration` field\nof `opts`.\n\n```js\nvar parsed = parser(['--no-dice'], {\n  configuration: {\n    'boolean-negation': false\n  }\n})\n```\n\n### short option groups\n\n* default: `true`.\n* key: `short-option-groups`.\n\nShould a group of short-options be treated as boolean flags?\n\n```console\n$ node example.js -abc\n{ _: [], a: true, b: true, c: true }\n```\n\n_if disabled:_\n\n```console\n$ node example.js -abc\n{ _: [], abc: true }\n```\n\n### camel-case expansion\n\n* default: `true`.\n* key: `camel-case-expansion`.\n\nShould hyphenated arguments be expanded into camel-case aliases?\n\n```console\n$ node example.js --foo-bar\n{ _: [], 'foo-bar': true, fooBar: true }\n```\n\n_if disabled:_\n\n```console\n$ node example.js --foo-bar\n{ _: [], 'foo-bar': true }\n```\n\n### dot-notation\n\n* default: `true`\n* key: `dot-notation`\n\nShould keys that contain `.` be treated as objects?\n\n```console\n$ node example.js --foo.bar\n{ _: [], foo: { bar: true } }\n```\n\n_if disabled:_\n\n```console\n$ node example.js --foo.bar\n{ _: [], \"foo.bar\": true }\n```\n\n### parse numbers\n\n* default: `true`\n* key: `parse-numbers`\n\nShould keys that look like numbers be treated as such?\n\n```console\n$ node example.js --foo=99.3\n{ _: [], foo: 99.3 }\n```\n\n_if disabled:_\n\n```console\n$ node example.js --foo=99.3\n{ _: [], foo: \"99.3\" }\n```\n\n### parse positional numbers\n\n* default: `true`\n* key: `parse-positional-numbers`\n\nShould positional keys that look like numbers be treated as such.\n\n```console\n$ node example.js 99.3\n{ _: [99.3] }\n```\n\n_if disabled:_\n\n```console\n$ node example.js 99.3\n{ _: ['99.3'] }\n```\n\n### boolean negation\n\n* default: `true`\n* key: `boolean-negation`\n\nShould variables prefixed with `--no` be treated as negations?\n\n```console\n$ node example.js --no-foo\n{ _: [], foo: false }\n```\n\n_if disabled:_\n\n```console\n$ node example.js --no-foo\n{ _: [], \"no-foo\": true }\n```\n\n### combine arrays\n\n* default: `false`\n* key: `combine-arrays`\n\nShould arrays be combined when provided by both command line arguments and\na configuration file.\n\n### duplicate arguments array\n\n* default: `true`\n* key: `duplicate-arguments-array`\n\nShould arguments be coerced into an array when duplicated:\n\n```console\n$ node example.js -x 1 -x 2\n{ _: [], x: [1, 2] }\n```\n\n_if disabled:_\n\n```console\n$ node example.js -x 1 -x 2\n{ _: [], x: 2 }\n```\n\n### flatten duplicate arrays\n\n* default: `true`\n* key: `flatten-duplicate-arrays`\n\nShould array arguments be coerced into a single array when duplicated:\n\n```console\n$ node example.js -x 1 2 -x 3 4\n{ _: [], x: [1, 2, 3, 4] }\n```\n\n_if disabled:_\n\n```console\n$ node example.js -x 1 2 -x 3 4\n{ _: [], x: [[1, 2], [3, 4]] }\n```\n\n### greedy arrays\n\n* default: `true`\n* key: `greedy-arrays`\n\nShould arrays consume more than one positional argument following their flag.\n\n```console\n$ node example --arr 1 2\n{ _: [], arr: [1, 2] }\n```\n\n_if disabled:_\n\n```console\n$ node example --arr 1 2\n{ _: [2], arr: [1] }\n```\n\n**Note: in `v18.0.0` we are considering defaulting greedy arrays to `false`.**\n\n### nargs eats options\n\n* default: `false`\n* key: `nargs-eats-options`\n\nShould nargs consume dash options as well as positional arguments.\n\n### negation prefix\n\n* default: `no-`\n* key: `negation-prefix`\n\nThe prefix to use for negated boolean variables.\n\n```console\n$ node example.js --no-foo\n{ _: [], foo: false }\n```\n\n_if set to `quux`:_\n\n```console\n$ node example.js --quuxfoo\n{ _: [], foo: false }\n```\n\n### populate --\n\n* default: `false`.\n* key: `populate--`\n\nShould unparsed flags be stored in `--` or `_`.\n\n_If disabled:_\n\n```console\n$ node example.js a -b -- x y\n{ _: [ 'a', 'x', 'y' ], b: true }\n```\n\n_If enabled:_\n\n```console\n$ node example.js a -b -- x y\n{ _: [ 'a' ], '--': [ 'x', 'y' ], b: true }\n```\n\n### set placeholder key\n\n* default: `false`.\n* key: `set-placeholder-key`.\n\nShould a placeholder be added for keys not set via the corresponding CLI argument?\n\n_If disabled:_\n\n```console\n$ node example.js -a 1 -c 2\n{ _: [], a: 1, c: 2 }\n```\n\n_If enabled:_\n\n```console\n$ node example.js -a 1 -c 2\n{ _: [], a: 1, b: undefined, c: 2 }\n```\n\n### halt at non-option\n\n* default: `false`.\n* key: `halt-at-non-option`.\n\nShould parsing stop at the first positional argument? This is similar to how e.g. `ssh` parses its command line.\n\n_If disabled:_\n\n```console\n$ node example.js -a run b -x y\n{ _: [ 'b' ], a: 'run', x: 'y' }\n```\n\n_If enabled:_\n\n```console\n$ node example.js -a run b -x y\n{ _: [ 'b', '-x', 'y' ], a: 'run' }\n```\n\n### strip aliased\n\n* default: `false`\n* key: `strip-aliased`\n\nShould aliases be removed before returning results?\n\n_If disabled:_\n\n```console\n$ node example.js --test-field 1\n{ _: [], 'test-field': 1, testField: 1, 'test-alias': 1, testAlias: 1 }\n```\n\n_If enabled:_\n\n```console\n$ node example.js --test-field 1\n{ _: [], 'test-field': 1, testField: 1 }\n```\n\n### strip dashed\n\n* default: `false`\n* key: `strip-dashed`\n\nShould dashed keys be removed before returning results?  This option has no effect if\n`camel-case-expansion` is disabled.\n\n_If disabled:_\n\n```console\n$ node example.js --test-field 1\n{ _: [], 'test-field': 1, testField: 1 }\n```\n\n_If enabled:_\n\n```console\n$ node example.js --test-field 1\n{ _: [], testField: 1 }\n```\n\n### unknown options as args\n\n* default: `false`\n* key: `unknown-options-as-args`\n\nShould unknown options be treated like regular arguments?  An unknown option is one that is not\nconfigured in `opts`.\n\n_If disabled_\n\n```console\n$ node example.js --unknown-option --known-option 2 --string-option --unknown-option2\n{ _: [], unknownOption: true, knownOption: 2, stringOption: '', unknownOption2: true }\n```\n\n_If enabled_\n\n```console\n$ node example.js --unknown-option --known-option 2 --string-option --unknown-option2\n{ _: ['--unknown-option'], knownOption: 2, stringOption: '--unknown-option2' }\n```\n\n## Supported Node.js Versions\n\nLibraries in this ecosystem make a best effort to track\n[Node.js' release schedule](https://nodejs.org/en/about/releases/). Here's [a\npost on why we think this is important](https://medium.com/the-node-js-collection/maintainers-should-consider-following-node-js-release-schedule-ab08ed4de71a).\n\n## Special Thanks\n\nThe yargs project evolves from optimist and minimist. It owes its\nexistence to a lot of James Halliday's hard work. Thanks [substack](https://github.com/substack) **beep** **boop** \\o/\n\n## License\n\nISC\n", "readmeFilename": "README.md", "users": {"usex": true, "fm-96": true, "tmurngon": true, "xueboren": true, "yitzchak": true, "mr1letter": true, "rubiadias": true, "tstonelee": true, "kentcdodds": true, "shuoshubao": true, "danielheene": true, "flumpus-dev": true, "jameswomack": true}}