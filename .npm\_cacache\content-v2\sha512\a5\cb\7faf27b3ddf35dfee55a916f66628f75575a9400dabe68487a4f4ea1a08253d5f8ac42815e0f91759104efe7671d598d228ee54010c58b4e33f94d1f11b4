{"_id": "is-generator-fn", "_rev": "8-31c3924e5c0e4b3c48a8d27078eef14f", "name": "is-generator-fn", "description": "Check if something is a generator function", "dist-tags": {"latest": "3.0.0"}, "versions": {"1.0.0": {"name": "is-generator-fn", "version": "1.0.0", "description": "Check if something is a generator function", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/sindresorhus/is-generator-fn"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["generator", "gen", "function", "func", "fn", "is", "check", "detect", "yield"], "devDependencies": {"ava": "*", "xo": "*"}, "gitHead": "0ba5937996916914a50e519e9dff2a0cf58c5974", "bugs": {"url": "https://github.com/sindresorhus/is-generator-fn/issues"}, "homepage": "https://github.com/sindresorhus/is-generator-fn", "_id": "is-generator-fn@1.0.0", "_shasum": "969d49e1bb3329f6bb7f09089be26578b2ddd46a", "_from": ".", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "969d49e1bb3329f6bb7f09089be26578b2ddd46a", "tarball": "https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-1.0.0.tgz", "integrity": "sha512-95jJZX6O/gdekidH2usRBr9WdRw4LU56CttPstXFxvG0r3QUE9eaIdz2p2Y7zrm6jxz7SjByAo1AtzwGlRvfOg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDOt6Eqecgh0ZfvY2n2ValxFfTwwDAnFtCE8tLWUg7a7AiEAjUO9JfVJNZRhRgnr/bEUoY4/ivNDG/cbEb0lrUGrKC8="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}}, "2.0.0": {"name": "is-generator-fn", "version": "2.0.0", "description": "Check if something is a generator function", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-generator-fn.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd-check"}, "keywords": ["generator", "function", "func", "fn", "is", "check", "detect", "yield", "type"], "devDependencies": {"ava": "^1.0.1", "tsd-check": "^0.3.0", "xo": "^0.23.0"}, "gitHead": "a75455cb94ee9541b2025fd3c41e4350c0495e13", "bugs": {"url": "https://github.com/sindresorhus/is-generator-fn/issues"}, "homepage": "https://github.com/sindresorhus/is-generator-fn#readme", "_id": "is-generator-fn@2.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.12.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-elzyIdM7iKoFHzcrndIqjYomImhxrFRnGP3galODoII4TB9gI7mZ+FnlLQmmjf27SxHS2gKEeyhX5/+YRS6H9g==", "shasum": "038c31b774709641bda678b1f06a4e3227c10b3e", "tarball": "https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-2.0.0.tgz", "fileCount": 5, "unpackedSize": 2822, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcJ5BCCRA9TVsSAnZWagAANS8QAID4a5TVEOjXiYCoFzYE\nc1TMHABLMpzd5s208DE+Bq+U6fYU7jWpeCwpg9WFmXOG6zRpchZGKsiMzcLq\n+9xav9FIuN9bkp/PBqOdhrawELa0I5H8IjY0r12TA4cLz/r488l6ku0t5qIb\nhGkKgXiMm2PXPG/fa+3PwzlZPl+Jk6QxiR1/Ib2sf/9jvvuLQ7oeiqLP2wFI\ncDXfVlDnO86Y57h34borCPj1OilgjbIWC3x6MRigjx2FA1G6Of7jA/TywAFe\nXVaC54h7fFkdptntT3CmqF9C+CGvIX0k2kmR/pSAcV85zhrGCcXx/3cXqSxZ\nVZxSkXrWoSCf7J/NpuMKzLZPBPHdvoGZd0IwcRlJImUAkl2acsi5a0LUj+up\nrGW5hY20w4qnIFbU5KGU/lLhU3WfpMNFoVwE/dozecYhVe/GZikXO3jcYvaI\nOsF2km1/LcL9QeBFi7qVWc65RAS5ZamZHl7tAWo/rmHkBrsW5F3w+0Iv6DHI\ny7nOqmHeI9vDrU4ZaldlEorfwX2ebXZVZprn2YihURZmrZOoQIxxSz4Ix6rI\n8IYpNMhU0eo4UOYJLnVRWQ/d0mwiKuBUtBWFwcpISGJMfKcX/yBtuzr3vsKJ\nh1mTyVg3MzMDTHroGsf9bkUKnyjdQJ6BVlBmStQd5d9ZTShDhCfYHh0WqJkE\n/J73\r\n=A6W2\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF8du9swb539q8CIDTQSRwK72C30c7Xo3y7y5t2ERRvRAiBRNt9KWQUqXhnGFPCxkx7G1QHKZvGzUb28dhr9e7BRpA=="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-generator-fn_2.0.0_1546096705561_0.7863427559745162"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "is-generator-fn", "version": "2.1.0", "description": "Check if something is a generator function", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-generator-fn.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["generator", "function", "func", "fn", "is", "check", "detect", "yield", "type"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "gitHead": "223059ea49e662e9811270912517db26eb4bdc80", "bugs": {"url": "https://github.com/sindresorhus/is-generator-fn/issues"}, "homepage": "https://github.com/sindresorhus/is-generator-fn#readme", "_id": "is-generator-fn@2.1.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.15.1", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ==", "shasum": "7d140adc389aaf3011a8f2a2a4cfa6faadffb118", "tarball": "https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-2.1.0.tgz", "fileCount": 5, "unpackedSize": 3284, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqIuHCRA9TVsSAnZWagAAzMoQAKIolqD8a5Px1Q2zI5Jt\nEeu5HCuYUEWEPD2FJt2MkBQKuR7JJ7XnFQ6DMXUo06FFEyMmKgtWY8/HfqNo\nhiV2ifXcKJvRRiebpCq0Gznt28ms4qwsY1xTVg3xGXn5rMYGi+RGpNOUGoRx\ntRjkQi4wgMqeYevXLaPz/nGEwNWPFSoWhjYpGigRWddbyRYZvsYYyGlN/RVk\nN9spLo24ZzYBPoicgj+NdctOvdM2lVwPzlMjMFN16u0OM2usa/kCCSxo70GS\nn8hIzDMK+vMh5VL8j9X9T3LQL25sNl+DW4ucFAS82wYS06yQjliZlEg8/fk5\nIWSo4x0JetFM77L6bEVTzBb02ktNVctRlHk5yhc/7UJk3/7qeYjqXHiUE6y5\nFnUuJehCwdIbdQVt0iKvtLmN7YHUFgYmmaYE/+0k9hiAJPsOXDl/vWJ9lhEv\nmnqC+duzNOZgRIPdvuRMzhibFW7wLV0TladM3f/8oQaRcsmKrMVgjKhXjUoX\n0FecdmV/+OkGdcXs5yyKKigZv82jxIMudkqTliemd67h99Y9f66AVuMLc3bq\nyCFUz9KBKVpH3ixyHGhoT/K0XFFd0RV/MPP3hwLUmPTaBtrQxfRDR2bSm9am\no0IPesttbIGZvqWnTNa0xFp/mpr8NCMaYffEBXzMwgwMEmwiwxl/JhK1//Q3\nkeSc\r\n=1yX4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGzJt3+2YQXhRByLvHI/l3nOjsCxLXXLaZCZNRxI8RjDAiEA3HGf/IQIPopG+v4TtAziGcg6KKPdpAz4h8KjlLFL9XM="}]}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-generator-fn_2.1.0_1554549638713_0.5193523673699398"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "is-generator-fn", "version": "3.0.0", "description": "Check if something is a generator function", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-generator-fn.git"}, "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "keywords": ["generator", "function", "func", "fn", "is", "check", "detect", "yield", "type"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.44.0"}, "gitHead": "****************************************", "bugs": {"url": "https://github.com/sindresorhus/is-generator-fn/issues"}, "homepage": "https://github.com/sindresorhus/is-generator-fn#readme", "_id": "is-generator-fn@3.0.0", "_nodeVersion": "16.2.0", "_npmVersion": "7.10.0", "dist": {"integrity": "sha512-Kn+4AXo1HHewGjikHNfxCFv58+XGeXAalWE5Ci1bgm/DkmDte9zFyqMn6xrzQKCgyhefxz9I8rseWUGI0jSecQ==", "shasum": "196814cf2e44a75212d34c12f3ce6f43a7821e7c", "tarball": "https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-3.0.0.tgz", "fileCount": 5, "unpackedSize": 2918, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhE8a/CRA9TVsSAnZWagAAekUP+QCIPyj202RibdAU9lr+\nJYpqRu5gWCtC2C17DmcmvKaTncrdjvwugJskQYvtInEszUDQohAK7o0s2gtw\nO/SDdDc6Q6N8xpCaEOiauWCgMBHfS8rPDpu3Ha56gImiAHfVjE2VzJLEenir\nJ1RxvS62IDe1BelS3GquPj+bMSMr+HdHpQYSKz04XfzIUwODgF6wkbq1tbln\n4ieKauskLPr+Osr2zG56Eck+neL8z7YvI5BSSZsM6FrYyG4mMAELUZpnftSG\np5AVSX+gwSSA3aLG/1g8sH2zu5SRrR+8zJIrPdiZuudCSJDsnABB7NmCeaDc\nbxceBeNkmqxv/kT/bB99a2EAiQ1v+7WO5fzLD0v4g9+rFy6x22Xb7nPpbfbp\nqvWGAtH1UNHqQ4P+H2Sm4j/LEwLi8UbSwpbv0zaHwy+OgWoeBvdlA3Pp+qpQ\nufrvIsjljo7ByCfK6Blaxjc8yOu8qxEUuFrm4jBvniOUUMT0zP7yveqprBTY\nMuwVEVFr4ytgTgARqYaJv84GOtbXvtf+GSJEGC2SlAq/AZOCyso+3jQiIe7H\nu0xRAAzHi5yeep97taHttxhp30bXhHxgk8VbR27veTm0GPdykcsPNdnhArP1\n0RNGG6uf2G4WgeqdpKLxJ7LpSYFOhwBToSA+oNVMiWhX9RI+gpwKSi1eAXmk\n5WQ0\r\n=YeHv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC0Vp47c+ANnln6M8sfzTtmAXi5MxYWQMCMv0S6PJpekwIgBS9D8Ll/fal5Btq6uUiPUnQKlPEvayMnFEVmyU1ZqXU="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-generator-fn_3.0.0_1628686015543_0.7798368784710901"}, "_hasShrinkwrap": false}}, "readme": "# is-generator-fn\n\n> Check if something is a [generator function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function*)\n\n## Install\n\n```\n$ npm install is-generator-fn\n```\n\n## Usage\n\n```js\nimport isGeneratorFunction from 'is-generator-fn';\n\nisGeneratorFunction(function * () {});\n//=> true\n\nisGeneratorFunction(function () {});\n//=> false\n```\n\n## Related\n\n- [is](https://github.com/sindresorhus/is) - Type check values\n", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-06-17T00:09:01.857Z", "created": "2015-11-15T16:28:23.204Z", "1.0.0": "2015-11-15T16:28:23.204Z", "2.0.0": "2018-12-29T15:18:25.744Z", "2.1.0": "2019-04-06T11:20:38.862Z", "3.0.0": "2021-08-11T12:46:55.710Z"}, "homepage": "https://github.com/sindresorhus/is-generator-fn#readme", "keywords": ["generator", "function", "func", "fn", "is", "check", "detect", "yield", "type"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-generator-fn.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-generator-fn/issues"}, "license": "MIT", "readmeFilename": "readme.md", "users": {"flumpus-dev": true}}