{"_id": "@vladfrangu/async_event_emitter", "_rev": "66-cde778b1d20ade3ee8c0b8aa818c8ec0", "name": "@vladfrangu/async_event_emitter", "dist-tags": {"latest": "2.4.6", "next": "2.4.7-next.676750d.0"}, "versions": {"0.0.2-next.492f589.0": {"name": "@vladfrangu/async_event_emitter", "version": "0.0.2-next.492f589.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@0.0.2-next.492f589.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "9f58a85411fd32f8fa9354024d9e5490c02c0c50", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-0.0.2-next.492f589.0.tgz", "fileCount": 11, "integrity": "sha512-6GH89K5bgGkPi76ShLzpHw9ci77HUvtRjUpj0kCIec6azwpnCgZHKapaMp9Fuh22MqwTquBuK0GkLDHerkZ8Fg==", "signatures": [{"sig": "MEUCIA/Lo43rNZR+lnajDN92lig8ompooLVse3KjbVWFE8MhAiEAqYKhPHLGje7lJz3UIN+mrxv6WSApPjwrL8CwpRt/dfQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10363, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitIHPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFbQ/9GGjgOY5pZ0v/wfVLZGTxxx4ydZU14CeaWJOJv6oWt/w7b6RB\r\n5ZtBERnJo4EJzIqyTCjw+L4JIwMeAPr0fGN6Y7ckZOtsg0/HQ1Q2UkaYdv89\r\ny62agE2+4iSHEFSbEgSF0fqNVKja6OdvescmN9tKlFaWyrouHdWiKspFZ0Zc\r\nIrNJSM2W0jO7dWS4XMtWXv03kWjdwH6xa9QbkOUs9aT3JhyzJziNsE8jMpkd\r\nDBBJJlzekdhtULKu9XgHpA7/B6ZAeQWqdAiCrt28vqIUPaYErdHpRfhnLFFS\r\nmjb6Ljmre8I2vxavxuoDEa7Biy+IGuF7mn93ZqWDnWL7rXHxq5Uht2BCJs7J\r\nMD5hOiAx6o9+PrIeb7aNRNbS5oOd0td65FBsZWB5+CsnQYEQs+OZ+605fQAK\r\nDWmkQepJIIbczjf/v4fI7v+8IzASrfWO3wDJTYY+pvMSUlpVHQi3ZSj0JZF4\r\ntB9cR0ECi5PAYG9uuqo1rHG3zOhFN2rBdaFr11AwFtGTJVsN/sxXKtbc++DB\r\n44uRfKbN6He8hyr3+YOgCvPAGOztoxcXm2/MHXUhE5R2r1i5YxZ8EzPhAoPW\r\nS0fHEmw7L4Xtye3iluFmFwQ8nAhJRASaLSXkUClCGILuWroz6FKZ2tnuuC7p\r\nUzfWrWz/cC4AijGeA7/fsf3sAsFpEMvLm78=\r\n=WNzE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "492f589b5b9c94cdf0c8cdb6df8f9a1ff8338b1e", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"c8": "^7.11.3", "tsup": "^6.1.2", "husky": "^8.0.1", "pinst": "^3.0.0", "eslint": "^8.18.0", "vitest": "^0.15.2", "typedoc": "^0.22.17", "prettier": "^2.7.1", "typescript": "^4.7.4", "@types/node": "^18.0.0", "lint-staged": "^13.0.2", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.0.2", "@sapphire/ts-config": "^3.3.4", "@favware/cliff-jumper": "^1.8.3", "@favware/npm-deprecate": "^1.0.4", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "@sapphire/eslint-config": "^4.3.7", "typedoc-plugin-mdn-links": "^1.0.6", "@sapphire/prettier-config": "^1.4.3", "@typescript-eslint/parser": "^5.29.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^1.0.8", "@commitlint/config-conventional": "^17.0.2", "@typescript-eslint/eslint-plugin": "^5.29.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_0.0.2-next.492f589.0_1655996879519_0.3268716984216882", "host": "s3://npm-registry-packages"}}, "0.0.2-next.889dac7.0": {"name": "@vladfrangu/async_event_emitter", "version": "0.0.2-next.889dac7.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@0.0.2-next.889dac7.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "d55b5bcc32440a38c9a770de15ff7b3e8b116fde", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-0.0.2-next.889dac7.0.tgz", "fileCount": 11, "integrity": "sha512-8LpxqPn/tACqxqQAn2+2CwOB1gsLpS30F/F3Dmv2EAOSalF4OjoI0s4KYR8L4etfgoBF3BIzlLK2CSKlqa+roA==", "signatures": [{"sig": "MEYCIQCOLHSp43X881A98Xljmbxl4ujrbrmQnvp2ZUYKrKVRfwIhAOdZZRXvZcaL6MdFYGgAjwiBBvykJ8IeMs5NGLYHETIA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivHf5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNbQ//TMlrBhEC2XqPfJ/km/iHjh4hwfWVFETKC87nKngqHw6oO/Y0\r\nfIPcxI6XvoOMtMwuZ2OOQyWwcC0B6aSs5uwrtWVZmGSGFKHq1THf/5xfH0Od\r\naOrlc6ihJ9+KmB+UhBkqyXViQxmJhegQUcx3dCYYWZeACKtAmPAS0+G+6UhN\r\n59/WdGXOTDDTs9qJYYEFADhIRpvlx6mqIKORlfF8spU2kDHDanIYgTOsARGs\r\neJN+nUD9NQgjuF89x9jlMI2YfbVX8HjfgjQFkkUCM7xF+Rg2gut4wmgq1ROB\r\nyeSws01CEMFNYjl7gFgqdyylHJ2hZjmDc5YHYGTvYEY7g7W6CxqmCqNT6Aj8\r\njpL/0V9nLDAsEngQtgDCebBSH5355TpSGYhfH+qZtdsC85w1rMEJ/CtWX+yp\r\nRFp7yxlkNob1E+QdmE+oO7QCOzIny2YMMjkvhANUJYJa2IzcrEA1wSVJjOoM\r\n1QgC0xvu3gwBFdP9xvjXoONQPXuG9pf57DhQhx3wazWN/9mFgUpffYASAhiO\r\nWO+1l5rvPjOYb1Eq7MYnPvRjvrZPW/amj5QCwxTYwsTE0QwU1FIzr+9uadWK\r\nqGgFgLkAwhwq/llT+KIrs+FaeWpxN6hPoCSEM2efsnKQw7P9d2HfWASbAEJI\r\n3qxUkP8A82X+6WtPg8T5a8e3BN4sV+/zVUs=\r\n=yhUH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "889dac7941e574aafd7b16712efa4fdc2221c990", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "dependencies": {"uuid": "^8.3.2", "@types/uuid": "^8.3.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"c8": "^7.11.3", "tsup": "^6.1.2", "husky": "^8.0.1", "pinst": "^3.0.0", "eslint": "^8.18.0", "vitest": "^0.16.0", "typedoc": "^0.23.2", "prettier": "^2.7.1", "typescript": "^4.7.4", "@types/node": "^18.0.0", "lint-staged": "^13.0.3", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.0.3", "@sapphire/ts-config": "^3.3.4", "@favware/cliff-jumper": "^1.8.4", "@favware/npm-deprecate": "^1.0.4", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.1.0", "@sapphire/eslint-config": "^4.3.7", "typedoc-plugin-mdn-links": "^2.0.0", "@sapphire/prettier-config": "^1.4.3", "@typescript-eslint/parser": "^5.30.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^1.0.9", "@commitlint/config-conventional": "^17.0.3", "@typescript-eslint/eslint-plugin": "^5.30.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_0.0.2-next.889dac7.0_1656518649511_0.22862797960020464", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "@vladfrangu/async_event_emitter", "version": "1.0.1", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@1.0.1", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "7f6aa9cac02e8f78dde22689e578613b7c077088", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-1.0.1.tgz", "fileCount": 11, "integrity": "sha512-rkcSxwTBXQN6fyHygfcJBz5Zeq1DOZ1t8h+yxHu8iVo9CJkVGFboWPW6P+ZqsxGx0v92kUltNTSP/hUf1IlBiw==", "signatures": [{"sig": "MEUCIAVsbJ7EG7UXmnLIvbrkGpuXc92N//6DWQuVCHwnxZrbAiEAx02hWCwAginwsqQh1chXB3vLRJ803F5qQXjYZc+v3Ug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153221, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivHggACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrlqA//X5WF9QU/FYScZRyih/Xbme0TZRzTUeW9nwbT7/G4NlrQyut3\r\nS39B3zZDDKScCYC2iTYzjw0uHAuwdutZVXvn2ucke/1RkFsSPW1JHJT8nXVv\r\nAeeRCoap3eDRk6FvipqCug4xcTW7ThI66YEEO7uyrR381nXd1X0UQzGqylpa\r\nim8n4IKLTUhziOxuyR3u8RCwWWKHyvNFWmCkHd5H5Gsq0AAEyddV/A7tJXBU\r\n3TAAf2/lYBsDD8Pdd9ZFVOO5zIXEbKEdjoM+irZ4/IZsGoJgKEX8M9OMKj3V\r\nDCGR4DsDCA9s5xjfDiJHIz9gBKh0GTNObt24oqt6RzGid+//YelaN+iUhQpV\r\nlHdEyjiqnpR29r77GZJKPKWTmFac1Zq2AA7NkLti2rTv+LgmI4wOKMcxPV1n\r\nRJ1ek4fM4S0Z2q1YzR+KSgUZ0nKht3oyBVQKNp6SUcKGcw4928RFb82jPGNa\r\n8/kZZ8k9Hvp06O12g+DFXGHCbTc5WLEavUA9lE+3wWSYaRwoXH/Togyn/peb\r\nH/JObVh1LD+ktLNQctdHXmPyLeGKVFCz9h9HKdXAezra9lm7FFwfcL+4Ksrd\r\nlI0YGNTYdrUcIdcHYmpvnjA0xM3JSpbSJJCrjmG3QzX6g1UWfLCM63sNfY9e\r\n7cv7keO2jL03hx7xFSEBUab4Eo9SFMEmR8w=\r\n=JOUK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "626ec45857d6272610918760c7f425933bbce37d", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "dependencies": {"uuid": "^8.3.2", "@types/uuid": "^8.3.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"c8": "^7.11.3", "tsup": "^6.1.2", "husky": "^8.0.1", "pinst": "^3.0.0", "eslint": "^8.18.0", "vitest": "^0.16.0", "typedoc": "^0.23.2", "prettier": "^2.7.1", "typescript": "^4.7.4", "@types/node": "^18.0.0", "lint-staged": "^13.0.3", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.0.3", "@sapphire/ts-config": "^3.3.4", "@favware/cliff-jumper": "^1.8.4", "@favware/npm-deprecate": "^1.0.4", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.1.0", "@sapphire/eslint-config": "^4.3.7", "typedoc-plugin-mdn-links": "^2.0.0", "@sapphire/prettier-config": "^1.4.3", "@typescript-eslint/parser": "^5.30.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^1.0.9", "@commitlint/config-conventional": "^17.0.3", "@typescript-eslint/eslint-plugin": "^5.30.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_1.0.1_1656518687883_0.5065170965592689", "host": "s3://npm-registry-packages"}}, "1.0.2-next.e19c8c4.0": {"name": "@vladfrangu/async_event_emitter", "version": "1.0.2-next.e19c8c4.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@1.0.2-next.e19c8c4.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "b72693c844c2ae0a43e33ce3da21066e55180179", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-1.0.2-next.e19c8c4.0.tgz", "fileCount": 11, "integrity": "sha512-JidD5AkZ3mlrjaoB7R8mOdJP9ToE/vBCT1zBNx7yZuqYdATDEj7ExyrNV8ZsF0lYeNs+jDeK4jjpX4SHjMx2xA==", "signatures": [{"sig": "MEYCIQCIiPTGbCoQaMr7Ia2d6dXc5b66djFw15Wpi4uz9Qic+gIhAPVJMZsUQFIspfxop9Hutraatzl8k4f4rXzn7ou5FtYK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivHhWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpgJRAAjApxiu6H+1ceHpT9wAcyia+b9TF/tDU7o8fViMrdAd/gPQ93\r\n56O2KQPNSOuRwMq18zN03pcSYPf9mFlX/opCrAaJRN2nDCW3N3o+5uHGFOqZ\r\nqoxHF0ZnwdK8gv1WO5r9Jsl2GMsH4pKPt04tuK1aiqD1uTtpD9rPwgXWIkMn\r\nIEmwBMu2fVra3f+2zfB0YIjNjMzLcGTvB6rX6reqaBYTkwywa+pbRKJwDiBb\r\njg1qjTEULovGPYrKnhrrsZmERIFa/7XOF0jvqLv734vO6hBU0YSvUbsF2lj4\r\nFMLTq+vfWSs1BILSHoqBDqAhaYRzlqcoKPbzx6hRjI3OLNuGnd1e7fkgZJIP\r\n0KjdWYttjLhtXBOpcDd3haFoHP3Tt8vxhXlTA22bRRFLq2kR+7ZrXJA+pU1H\r\nl2AsMN7QxfMrJeGX0fuvrAfwv0zy33IqFVpveFcWfitSJ0tr3YLMBpRIyjvx\r\nSDL96aNGMBizjXHciJ8Ps6vWNtBi6pA1l7QuBHUSlSCCG9bhVhhsVnu0AB1A\r\nfoIIY2FTMdoqGS3tisgplAeCeFuM60FZv9auCu+DAKpnIXRLfDp9LPjaCqBB\r\nlXW0z/a5iuA+ld0QsP0aaz1jvR/PME8bvQowvCBR0Emc1QofgrG4jLehaHh3\r\nioYeKxH33nTMkR9HyVo2w2qZ7nRijgxCADM=\r\n=14h0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "e19c8c496ba51a000bc5cb3386098df34f3716e1", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "dependencies": {"uuid": "^8.3.2", "@types/uuid": "^8.3.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"c8": "^7.11.3", "tsup": "^6.1.2", "husky": "^8.0.1", "pinst": "^3.0.0", "eslint": "^8.18.0", "vitest": "^0.16.0", "typedoc": "^0.23.2", "prettier": "^2.7.1", "typescript": "^4.7.4", "@types/node": "^18.0.0", "lint-staged": "^13.0.3", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.0.3", "@sapphire/ts-config": "^3.3.4", "@favware/cliff-jumper": "^1.8.4", "@favware/npm-deprecate": "^1.0.4", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.1.0", "@sapphire/eslint-config": "^4.3.7", "typedoc-plugin-mdn-links": "^2.0.0", "@sapphire/prettier-config": "^1.4.3", "@typescript-eslint/parser": "^5.30.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^1.0.9", "@commitlint/config-conventional": "^17.0.3", "@typescript-eslint/eslint-plugin": "^5.30.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_1.0.2-next.e19c8c4.0_1656518741814_0.13959831995988248", "host": "s3://npm-registry-packages"}}, "1.0.2-next.d052952.0": {"name": "@vladfrangu/async_event_emitter", "version": "1.0.2-next.d052952.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@1.0.2-next.d052952.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "f45a828c47b19e3107888ff15a57c314d89ee8a5", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-1.0.2-next.d052952.0.tgz", "fileCount": 11, "integrity": "sha512-yqw39k5Mpg6omKxV1PCbie0j/kgLCaxRNf3vJJuGVH/CnTC+YCZAzL1OYOqzri9bHpxCPsL50qfqHjEI6nPo9g==", "signatures": [{"sig": "MEUCIFv4zLRmqA9LTLXaMlSy/x99W6XM8SxCPBF56zhD2uLwAiEA1GfuWh//3rkB79Bkzl4OmtKgiWb1fD9KtqGWtgNThEs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivHklACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpkrg//amSh2BDs2T4sD+++9JsE2FFIUasQcXuE38tmDa3uD2OWS6jc\r\nI0w9gX7YyC5qfAvCiaYemGqmt2GXBidAdIPgNjwicBpfC+F5JhFobwXOB2U8\r\nNY6l/8/YU3JXEPVna7+vkUAFd361W6EdUi6M6+NTvCL3554yXc8Q/genIZ2j\r\nTqteR7CI04ggwNeQWxiwlZZpSd+1TkBUArfhnIIVPoUFf3Gh6FiOJ83diEEY\r\nD2nc1D6S+nzbkzOplbkBGVzgsFLevMqwhNnY2xTuHv3X20A84NSsxQMFS01C\r\nlNS//DNtcAbpqdjMyOmhu4MWqzgUawdYewRiXUos3SCZYC24qZK2vU0byHHc\r\n6FmQYcY9quFNFe4+jIHMBP8cUr3M44OPoYkNqap2kUaevIBk+5Bbx0I6xhJg\r\nbCc7rF0fDNWY5rwoxgFYDtFtxP9DHrDL0qBVvceFN3UyNYiNt28QIfAQp+cz\r\neK7stO13NP/IxIt+7uA71oCjD6HNYTg+e5zntygGQQrAakw04Jd3HraTC3fW\r\nf/lfXCXAg8SNiQIJeVeoAzP+kU9pBqmOw0XFvMT5KEI+w8V2yrhdzVdlEXhn\r\nJvKkmBeqACVvenD1+knRbhKcihzTXyynGEFfdHUE4k8aXfot2XQL3fimahYt\r\n6d/b8e7U0o8J0+dwNqnbmXTmfSvROBI5kS8=\r\n=kDir\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "d05295272f4a14181f36f90d994f935b638e3c92", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "dependencies": {"uuid": "^8.3.2", "@types/uuid": "^8.3.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"c8": "^7.11.3", "tsup": "^6.1.2", "husky": "^8.0.1", "pinst": "^3.0.0", "eslint": "^8.18.0", "vitest": "^0.16.0", "typedoc": "^0.23.2", "prettier": "^2.7.1", "typescript": "^4.7.4", "@types/node": "^18.0.0", "lint-staged": "^13.0.3", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.0.3", "@sapphire/ts-config": "^3.3.4", "@favware/cliff-jumper": "^1.8.4", "@favware/npm-deprecate": "^1.0.4", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.1.0", "@sapphire/eslint-config": "^4.3.7", "typedoc-plugin-mdn-links": "^2.0.0", "@sapphire/prettier-config": "^1.4.3", "@typescript-eslint/parser": "^5.30.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^1.0.9", "@commitlint/config-conventional": "^17.0.3", "@typescript-eslint/eslint-plugin": "^5.30.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_1.0.2-next.d052952.0_1656518949353_0.9297460218881006", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.0.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.0.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "67477e5fc31cc749574bee43263adb430f9f5997", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.0.0.tgz", "fileCount": 11, "integrity": "sha512-Gd+mI901BeQMgaZjWeEnJ7v3n/1OD2JZS1vOusT4T3KGb36Jro5hNHTS3ck1drsRxl65QcQwvzxtoKcABNBeWw==", "signatures": [{"sig": "MEUCIDdjGYyNd6lfRyV/qmQXJkgcm6KdEvZ2W3Ru/ZS0G1OUAiEAtTg2rvt/QFKFVgvcFPDJhM5AFDyxmYDnHh64itrHMxc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108127, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivIQ5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/ng//dWits2ylNNWxVVu9fIa6vbt94tpVxpdv3ILZ96/E7B8n0fxL\r\n9+VyQUqoU/KvMXyKDuyTXQljaVtzplBsk7wICZDL516YXPFVYb0t5jIF4Sh2\r\nF4vJXLTS8YwTOWLikGx9MYy7BD2FmCOIPagaVWAD+oKJ2ZUXexNCJj0FxVwB\r\nRgFgm4twqmsYZ2yt/H/Q9mplYNxEp4+gDeiDpVmyOre21Jdj3ZS8mkadfBPs\r\nOCqKl/1l0TT+MzehpMxYPl0sAISr5OSSJyCwbep0JxMYZQ20xzC4slf34Ytn\r\nvBuGBpTYiPurebm2A/Rstk2nMDgS7LAV3Ht4K/6GC5BINp24chJqyBq7USfB\r\nVBs3QB29EOWt1Ag2mwPpxI+V96IsEwScvP16iCt4atpEzywJv4pN7lsdl+OR\r\nyn0IdZnkKmdQCjqVkFVkgM+7t4azYnFe6UVQWW9b8TIGFsHE+lTo3j0sSfV/\r\nXqb1XfAyVi4iQ0NSQLZnVC5GCIVSjKTlsa5TKMDjlSVpcHNrt2mHU+grbjQE\r\n+3fdunEobqj7e1GFf2B/jMagzQ0diKHu/RuXFB9LEuiXISfGpqKNc1+9LJwc\r\nGuKNxoGhy9/Gg8tkSV7KU34w1YhcC030pNh9K96qqc0CyXDZZLWCdXcv4ZH9\r\nl9h225Cw6le5k2Xd8jyAZAoVskOOZdW5z4c=\r\n=+TEf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "af0da8336cae5b1ccd59c1fac047d850a38c97ac", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"c8": "^7.11.3", "tsup": "^6.1.2", "husky": "^8.0.1", "pinst": "^3.0.0", "eslint": "^8.18.0", "vitest": "^0.16.0", "typedoc": "^0.23.2", "prettier": "^2.7.1", "typescript": "^4.7.4", "@types/node": "^18.0.0", "lint-staged": "^13.0.3", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.0.3", "@sapphire/ts-config": "^3.3.4", "@favware/cliff-jumper": "^1.8.4", "@favware/npm-deprecate": "^1.0.4", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.1.0", "@sapphire/eslint-config": "^4.3.7", "typedoc-plugin-mdn-links": "^2.0.0", "@sapphire/prettier-config": "^1.4.3", "@typescript-eslint/parser": "^5.30.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^1.0.9", "@commitlint/config-conventional": "^17.0.3", "@typescript-eslint/eslint-plugin": "^5.30.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.0.0_1656521784855_0.7898375861203499", "host": "s3://npm-registry-packages"}}, "2.0.0-next.8c69419.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.0.0-next.8c69419.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.0.0-next.8c69419.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "59d2d0840588775bff734fa01f6c6f666382d5ab", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.0.0-next.8c69419.0.tgz", "fileCount": 11, "integrity": "sha512-alFPDUPwsGpBQjydnb5q9J/nTRq87h6Dhc4YSHdVUbIL4KACpFJuScylZrvG5KiCX8t2vRQ6U4RNf05PGzMI8g==", "signatures": [{"sig": "MEYCIQCOa2q6Kq4yUUxbvfOahhzWlKkSnImaGvyCmwGg8CC1vwIhANxR/kvDuyqbx8DcdDcQN5Xvl+/yNFT85O7mrmAA9O+m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivIReACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqX2w/9EAh9QTe5c2fcJy4+E9p6TZBf4TomS2F3B88/SlnrV7X04Hnl\r\n1TmRmOEiA4TSIAy9tQj9+gdxH+c5UmkqmCbMD4ZgZeR/3hSgUTYT3K2/KZlR\r\nYTQWt1vultT7XJCqnUh+nG7ldclpSePilTgmYC6IPkyP3uNwoXO2237EeMIG\r\n0AeLMW4CaR80TYDjYjRwtclhaK2755Og9UUaab+BKh704Dg/ev2UeHgSxKNB\r\nL8ht614cQFK7b/Nx24HHg8hUmZrWB2B0RXRy2L/fSU0JuhkFROmi55zZy7GD\r\nwZKVFXEHoSWXhlcGFDAUhKUw+IuHZXLDUsP+S+pUCEmtafKLLxB4C0Hlwjy1\r\ngq4+MxInNKcHYc4ztp/2lti0uAzXV8SH5WXD/mw9ToQ4kj1UU78OhUiYa2dO\r\nQ1VVwzqzgDbWsUgg4QYlT0oZ71fczdLD6K3L6Des9hZWVokZfVnqB1reoCE+\r\nhfyTDGYBnf5WOOLJM0cRWHMsFmRtO28LJd+z8RkrU4GlHIaVlxFtShRgNkTG\r\nbyB9ZptS3UT8hDHMGoT4Tt15XcnIj9tngw61Bo5SBWzU9/pAlI+PF4kvvcOH\r\nyjSQXvV8zUWrMRx9Zg05IbUzOazrURsK/BZed5aNxiXWeNHVWIyr4VpiBs2T\r\nJ2uFPz59XQGzyj2SIwr6+r3qJXpidi94qUI=\r\n=t4eR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "8c694199da1a0a231feb1be3b0d7cfdb18cefd0b", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"c8": "^7.11.3", "tsup": "^6.1.2", "husky": "^8.0.1", "pinst": "^3.0.0", "eslint": "^8.18.0", "vitest": "^0.16.0", "typedoc": "^0.23.2", "prettier": "^2.7.1", "typescript": "^4.7.4", "@types/node": "^18.0.0", "lint-staged": "^13.0.3", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.0.3", "@sapphire/ts-config": "^3.3.4", "@favware/cliff-jumper": "^1.8.4", "@favware/npm-deprecate": "^1.0.4", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.1.0", "@sapphire/eslint-config": "^4.3.7", "typedoc-plugin-mdn-links": "^2.0.0", "@sapphire/prettier-config": "^1.4.3", "@typescript-eslint/parser": "^5.30.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^1.0.9", "@commitlint/config-conventional": "^17.0.3", "@typescript-eslint/eslint-plugin": "^5.30.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.0.0-next.8c69419.0_1656521822597_0.5327490193318498", "host": "s3://npm-registry-packages"}}, "2.0.1-next.af0da83.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.0.1-next.af0da83.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.0.1-next.af0da83.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "86a5ac4f0651c770d6bb88543c4e3af69ab09805", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.0.1-next.af0da83.0.tgz", "fileCount": 11, "integrity": "sha512-E5lvgCq83j81zhu2WkRhig5ymPqhDo8+WxCdK6Jn5uf1en0cTdttYlvQSbAINCNNUStuXXgXJD2dEmOc1R2vOg==", "signatures": [{"sig": "MEYCIQCkvqGaTZVtA9S2G5x3DD5fploAfS5CtBfxXRoOWeSamAIhANipQ0BJTBZmAvOGVkQu1u1wUPXZD940kG8EJD+ANJ/E", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108142, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivISFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpojg/+PM8gUl2AK3xvch3FA3zpAPZrI07ivmbwaeTPy02Wvk3xUesg\r\nRt/0iDVQm6h146jV+gSg9z6e9Rcu8mZFzmhe9H718aIRPMTrVyPUGBqYkXTL\r\n0PANBhc93DMCQwMvF4vZ3Al/xDFmqaQxMKBQERYyEc/CG6J37CdY65oErJWq\r\nYD007GopyJ4N/H5TMZ3ZF1nTv/1HOhwMyR9uWseotAwVEtxRSs4iWPChW2Ue\r\nwiPwaUcKeuM5OO7IE3RFSG3vhibalkePUXJhihgrdxKAjv4pioGpCg5/XAqz\r\niRfFX9tLxjbD7P+oJ37nDH03z/kt9VK4rgu8/8JDaeqtiuUFqfHjywNF9YzX\r\nZ7A6Xpo9VwmZ487oOVU9l/y4q99XKFElp18QEPcMOummQ7k4MmSNBtdSZEAN\r\nX3UTCjMZNlZJSN3LazePCKFQhie2p0UgE6vGR7F5Fr8mruyLD3y/WEGMkeLM\r\ngU/HrtRClkF25DbNHHY/E1NP20Htm2ZSk4yzis9msFJCWtGwxw7BrN0N/XXL\r\nkEfP0SrvuteKptxBy1pnIWXnhGeU0XuFMr8pJ3qvJs3m983QMNNZLJJiKCGl\r\nbHqECYEjxS5Zn2HDfgqWiEPPeO+f4wPG4/CGvqgcdkfXV0qiYYeJEVyQgjbO\r\nqnybAnXXrrZUuhFwZk8wnH40OVBzWQjb6aU=\r\n=lChP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "af0da8336cae5b1ccd59c1fac047d850a38c97ac", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"c8": "^7.11.3", "tsup": "^6.1.2", "husky": "^8.0.1", "pinst": "^3.0.0", "eslint": "^8.18.0", "vitest": "^0.16.0", "typedoc": "^0.23.2", "prettier": "^2.7.1", "typescript": "^4.7.4", "@types/node": "^18.0.0", "lint-staged": "^13.0.3", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.0.3", "@sapphire/ts-config": "^3.3.4", "@favware/cliff-jumper": "^1.8.4", "@favware/npm-deprecate": "^1.0.4", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.1.0", "@sapphire/eslint-config": "^4.3.7", "typedoc-plugin-mdn-links": "^2.0.0", "@sapphire/prettier-config": "^1.4.3", "@typescript-eslint/parser": "^5.30.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^1.0.9", "@commitlint/config-conventional": "^17.0.3", "@typescript-eslint/eslint-plugin": "^5.30.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.0.1-next.af0da83.0_1656521861024_0.2579186095048085", "host": "s3://npm-registry-packages"}}, "2.0.1-next.08ebbc4.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.0.1-next.08ebbc4.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.0.1-next.08ebbc4.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "4047380de5d0372436438e858d0bc12045902008", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.0.1-next.08ebbc4.0.tgz", "fileCount": 11, "integrity": "sha512-ZG7avA4q/wJPkwl2ewlN5b0SAakDJBcP/NsLjFMzG3kUgZKBXuPN2ExVJCZouXGejD+GyMzzjZKFbacn6vWVVw==", "signatures": [{"sig": "MEUCIGjsAJL0RF0clndmnK2lfscOMq8m219tevDeBTI1uVCPAiEApt9HPrMV0ePd23I5Yc8JEHozFXgkYUk+ZTtiIC4doJc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiwZ2PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpxIxAAi9TeDPcdQGpuRR9UPEaAq6N2rsB58ORKC3tSebSdjFlJPnc4\r\n1DDlv5WDH8aqMZdwyQouNaWXvn6QQlxrDt5PyuU09AL0jeQZ2ghA8cOH9NM9\r\nX82naqjYqjM37sinC2j0DEHvd1Ajh+oYDcZX7S7BDAhyp76W8Ttpt6DTFRMg\r\nn702CKtDRGBVswf4JWnKEPLfM5fqxiZY662Pr3+Mkf4NQQ7mM2bEUQvQmpA1\r\nDuewBXpiIyt0t0XvkOCXs8wnG72bnxZJOCnAi4oaL5ZHCUju0jlSjjIX192M\r\noOG4MlwU9dF4XzMU1xuu+dkfigcAmHAx9lTsKxu7HhW1be68QTRmAGRFu37e\r\nBS4q51SFSsHkVv3EDVZTMxivN2wtZtU/BW0nxhrP/g/Ng8ODuX0Nn3GAVYFe\r\nImgQVakR9deho0vT98ceXFcFovZlsBz+7Vo9hFJSSd+pOowMaKB3Z9W0cO5W\r\nKfEZBbkDFCjsCPRVZgYWM0Er0uic58F0JJPQ0/RPKwHW1sqKLdUf2DJTbJyI\r\nKeBtS/2h/dBJMMxXqnS9EJfBjGwH1Fb6GxdNEDNDfTP29KwsH7VHH9m0lA0c\r\nPX555RG3U72dgKr6xqqYATIIAlrH8+tE55BT6lMuFyIkd29QiF+XSOYBd/L/\r\n+9Ze4DfJLPCkgNeg7eI85O8uM9qdnCQqaKg=\r\n=WyeT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "08ebbc4d01d28e06cf7cf06275fd7806d5a7eb32", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"c8": "^7.11.3", "tsup": "^6.1.2", "husky": "^8.0.1", "pinst": "^3.0.0", "eslint": "^8.18.0", "vitest": "^0.16.0", "typedoc": "^0.23.2", "prettier": "^2.7.1", "typescript": "^4.7.4", "@types/node": "^18.0.0", "lint-staged": "^13.0.3", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.0.3", "@sapphire/ts-config": "^3.3.4", "@favware/cliff-jumper": "^1.8.4", "@favware/npm-deprecate": "^1.0.4", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.1.0", "@sapphire/eslint-config": "^4.3.7", "typedoc-plugin-mdn-links": "^2.0.0", "@sapphire/prettier-config": "^1.4.3", "@typescript-eslint/parser": "^5.30.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^1.0.9", "@commitlint/config-conventional": "^17.0.3", "@typescript-eslint/eslint-plugin": "^5.30.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.0.1-next.08ebbc4.0_1656855951343_0.4392137661605571", "host": "s3://npm-registry-packages"}}, "2.0.1-next.b849b38.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.0.1-next.b849b38.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.0.1-next.b849b38.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "3998220e9689b590cbf3138477aff86715b2aab6", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.0.1-next.b849b38.0.tgz", "fileCount": 11, "integrity": "sha512-dEzF0Mlq3Ar92yJFq6wFIXG8jvYp8+wY0084XNGAHD8gTpmqHQ1zFHtdG2SuN2iWvmi+QjLURzubX99cIQN4iQ==", "signatures": [{"sig": "MEUCIQDKSmEgvq12vBl1gz4FgduW/i0Ew5y8IwCXiZaXtdsCpQIgOyyUKKbIZ3DxriSY0AbYDoEx+ey18hquJK3lt1Ivglc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108190, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiydqDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqvsA/+MUKd5rJcji8UxMbWrgzTtDMO8jfUnXEDugz0A5P4zGRaUlFU\r\n3C4rA3PzayYnAIUL+iUo0hcU3ioCbnGtBzWb+Mb/ZW9w/06heXMAsRKy3GNZ\r\nf+WmgaQbR3pdqlIURNhjz7GN72HLTUKTlqEspaaVdzPHmtHXRn7/shjifdQz\r\n67j5qMj9l5e9zmjesxXYr5TqwlVB8G6qSfED9AjTrEE2DWSq7luBvrqvMYCD\r\njjkhGf5yL3sEB9B7Em5MvL2EvKMMcJBbzaFwB/kfqqKcZgDdG/8zJZpH1oWO\r\n7cTlW4FBsS7oHiILAx5zYjR0i9JaStIlxiyiEW4Wk8PgEQV7lawZeTkdgsKg\r\ncggk1b02J2HFrmy+vcGZS8KFm4p+E0+QrNR7yAlldhUULrmyfdTei7Llw05w\r\nWYa6S8IubKi/xGu4CE+QYaes/trnrxzXVM3aVDC+AS8vUX+YVYRfAmq1C8ke\r\nhT5hpM48a/TqXKexACcAxr/aeKI2gGwNd6EvKpnQMyqeGqIhMWsx/CCr9AJ3\r\n6SwYMLnq1Sajh/zkXYQRZzhAKRdtx94jVrhyT1sIbRinA7nAyoj+cUQRD/oa\r\nWmmdww8pd9llAYuvkezp8lQDoXz3j33//bwzYgMPBefpJJHyj9+FlMxb9m7V\r\n2LbCukpPlZtTHZCMlZRtjv3Z9J/r0tG/yM8=\r\n=LjNG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "b849b387c36515c60234c06681bfd4ec32ee5336", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"c8": "^7.11.3", "tsup": "^6.1.2", "husky": "^8.0.1", "pinst": "^3.0.0", "eslint": "^8.18.0", "vitest": "^0.16.0", "typedoc": "^0.23.2", "prettier": "^2.7.1", "typescript": "^4.7.4", "@types/node": "^18.0.0", "lint-staged": "^13.0.3", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.0.3", "@sapphire/ts-config": "^3.3.4", "@favware/cliff-jumper": "^1.8.4", "@favware/npm-deprecate": "^1.0.4", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.1.0", "@sapphire/eslint-config": "^4.3.7", "typedoc-plugin-mdn-links": "^2.0.0", "@sapphire/prettier-config": "^1.4.3", "@typescript-eslint/parser": "^5.30.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^1.0.9", "@commitlint/config-conventional": "^17.0.3", "@typescript-eslint/eslint-plugin": "^5.30.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.0.1-next.b849b38.0_1657395843732_0.6582553339174151", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "@vladfrangu/async_event_emitter", "version": "2.0.1", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.0.1", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "1c8cb9f6fcbea716ca2aaa3df22e345cb3f33aef", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.0.1.tgz", "fileCount": 11, "integrity": "sha512-zCOcPuMcCHAqTLqYvltYBGRUrYiDZ/RLpih4G/AsSlMgoSdv8EvtnAtyiM3gbRIzrdc42e55VM2UUyLRcR8ufg==", "signatures": [{"sig": "MEQCIEwaqSK1/QgWa9r5hCkn6KwYK8RVGARxNY+ABllZm8ZWAiA3L+qUKSR/R9SroMmysFoYBN9dpc6Y7J28gkt83CyL5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108443, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiydqFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmotNw//YzzQil3bWhTNvYxcF2tscCgccjuEpZtZVihrjVkyTuykYP4c\r\nWUK3sOm3cQLV3j9fKadnb2IgAxkvkMLeS0BNk18reK6MFVL2t/fGgznC4ivF\r\nDlsQcqsdTvfgfokXUetY2lbfUI/VohZ9bP28vpUH6zsG2d5a7pljVu2+yWW2\r\nLUHCbKzIqBl6ZK32OHYlg85Hy8Y00CQyQAurQqD1iTgNGYoZEAuKU1I1K3wE\r\nF7U00oSO8i7308rAkhnYFUOJsKW04K1uYS69xEwIahSDD7xFNSamIfqVVKLo\r\nhJWHVVprK3pP2+h+2/MjHvLZL1uedQi4Kpb5jEk6lnOj+fdsEPT5559eOeK/\r\nLFoK+Y4cgRo/xvsT9OkY/559eY3PtxZwtMs1n02HNuyP3cso9YvsSkT+hQ14\r\nGfSpsYokzu8JetTbTRqfC8JyDqTTMdLWsAIWQFzyd2+TzYRawmoCDH55Tg9g\r\nr1Wow34BEqASXeLRlyJKZIFk2pacAKEFkIstwBkoXVGZ3pUu+VcP3gDVY2Cl\r\nPuPQc5T3zMpDPHXzHcRrQpltkZzgOx4s1YQGVySyScl8RgbmUWlciTziva3P\r\nI6d3GbRyhWugA68OnGaVZWwneexRAAmVUiJuKRvTyHWJDDHqNKVPhj4rKsMF\r\ne8jjgtvda5B/jexfV3en6Zezslh2wMKkjPM=\r\n=+3dP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "b15991ea749ad8c4dae1c67d8fbbd30d412533ac", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"c8": "^7.11.3", "tsup": "^6.1.2", "husky": "^8.0.1", "pinst": "^3.0.0", "eslint": "^8.18.0", "vitest": "^0.16.0", "typedoc": "^0.23.2", "prettier": "^2.7.1", "typescript": "^4.7.4", "@types/node": "^18.0.0", "lint-staged": "^13.0.3", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.0.3", "@sapphire/ts-config": "^3.3.4", "@favware/cliff-jumper": "^1.8.4", "@favware/npm-deprecate": "^1.0.4", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.1.0", "@sapphire/eslint-config": "^4.3.7", "typedoc-plugin-mdn-links": "^2.0.0", "@sapphire/prettier-config": "^1.4.3", "@typescript-eslint/parser": "^5.30.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^1.0.9", "@commitlint/config-conventional": "^17.0.3", "@typescript-eslint/eslint-plugin": "^5.30.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.0.1_1657395845637_0.16771803995314838", "host": "s3://npm-registry-packages"}}, "2.0.2-next.b15991e.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.0.2-next.b15991e.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.0.2-next.b15991e.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "e8e21c534dffded4a0b2a8fb58a27ec4e5211331", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.0.2-next.b15991e.0.tgz", "fileCount": 11, "integrity": "sha512-2VJXFIkYMnnDkCUo/5EXw/KXwyTySDGY/Ybv+c9ABrU6MODE/og187q6HCd/2tc3mUXffudfjqrgpajQUWzwtg==", "signatures": [{"sig": "MEUCIQCOeFopTjlhKF0y+76Sk9x430x/MmO9MjWwMmdx0p/tFAIgBs/oBfIXAFPgQZd6C4hR9MnfClCKlup8Z7Hc4bVzCIw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108458, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiydqSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmroiQ//TS3PRoVJWM4bbsaR+EGoCzuz1nzbDjNYLNA9Y6YhtkkcpzDc\r\n8/2+aqmv1nlPsi+hrSJCPaaZ+YyV/FlqRL57TmRRbR+AwfrItFEoLrBQZzY4\r\nMELmzq1VT3AYglEvtG8nhbiiKR3NUPmB8NN5HxYBmTXn1d9x6npKnqhhhjBP\r\nhwrVhl1w9IZGka3ODwyoH0xgHMijvBaMqjzCkcXUvviXD1OBllrW3bnwyDy4\r\niEeBOPESY7l6rLdemsmHhBREpUUT6Pf+2wcOZ/hPUcnoiNnnz+VOeynIW/G0\r\nzGwWH6wp8RKpMbDmhySi+sqGDk+Fxv9ZV4GMuIP2hm3MmV9u9lZsFBDNKl5T\r\noc0YQ4PSrWEY0fVKAi9D0Zs/qg9LFfGxLs/kqYRfnPK47jV9VhQzJI8l807L\r\nZzySiPVMwATgsCXIzhtzCsmXYRgtkQ3hTwlyvNOzYuSynLHEpH6NqvICkWL0\r\nxYGU7suprPZZQIONXulPPcQ5U26A6Z61UC0nUVqbXYDqsfMGCUuC5/JaScym\r\nHSpZ34+ltQhBzzAA+niNU9M4ca/McJ02EO0huN4giMta9DjoFbuz7iDCcgvm\r\nWbYXyFAUyZjKloQAaAKVcX7sVomtkXE8GUv9V9Rf+ArRpTZ9oQhvPk20lf0A\r\n+VsnQgyFxj3EDukBBg+m7WCPOSU7nCzeNXM=\r\n=XQ3w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "b15991ea749ad8c4dae1c67d8fbbd30d412533ac", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"c8": "^7.11.3", "tsup": "^6.1.2", "husky": "^8.0.1", "pinst": "^3.0.0", "eslint": "^8.18.0", "vitest": "^0.16.0", "typedoc": "^0.23.2", "prettier": "^2.7.1", "typescript": "^4.7.4", "@types/node": "^18.0.0", "lint-staged": "^13.0.3", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.0.3", "@sapphire/ts-config": "^3.3.4", "@favware/cliff-jumper": "^1.8.4", "@favware/npm-deprecate": "^1.0.4", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.1.0", "@sapphire/eslint-config": "^4.3.7", "typedoc-plugin-mdn-links": "^2.0.0", "@sapphire/prettier-config": "^1.4.3", "@typescript-eslint/parser": "^5.30.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^1.0.9", "@commitlint/config-conventional": "^17.0.3", "@typescript-eslint/eslint-plugin": "^5.30.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.0.2-next.b15991e.0_1657395858626_0.8348905903344845", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.1.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.1.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "367bc5c39a39f949aa61c1639cf5b3af63007bab", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.1.0.tgz", "fileCount": 12, "integrity": "sha512-6ZHxlLyfKuOE190c5YachZe1XCXA/NVUIP74hFZ1wiaNMZdV3aHjNprH7kmUJqazyE2AGMIH/DC3l03CMRJEZQ==", "signatures": [{"sig": "MEQCIEvykvT1DhurZS+QcuncT7Z/UyRsjnIjDO527ATScwYKAiAIR72Y1eLeYCYAdrbjk2sdCPfmw7x1SWimzxfRLvuvHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 166458, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjJ34IACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrRcQ//RA5J2o3sOrALiCQCB1fUCK0Z3M6UwSHQXAU10jpCqJ+47nqp\r\nGsBQcgf/t81x/346pewPpnOjNL6UQzCuJ6OKqLp4/RLZZr0AC1QtWZ3WrzDQ\r\nfbBdeWsZbUkX8BIXITG662yXIrRXLhLM4USXA40bckSvJvStKOPE53ij5YRH\r\nIqkrWLPamUyws+NxU2FLtZuldEqR54UpjipjzyOYBeotq4CZ0YHE27Kurk2s\r\nDED4cImCyH0CK88V/BIB4mRnqWD045k1dPbOWcQD9N6CrGgVWjOom36RiqWb\r\nq3XEu9t2qCizn9yKENBz+JX2mbjuGsnm+pM7R+WpvubBlKcyzHPlT6PbplSc\r\nujm+YPTvq5jffkeRXNDqAntZk6yQ+MxLEh0GRb6ZThYPo/IvWM6MNahOVZMr\r\ngIiV3z5Qz3dJW9yc58kN+2c/FnTSu3tG9HTiq52P5h6L5k8PnVKKRRGa1s8x\r\nDnEXjR46ReFUcPGVoV8tqNXTqvdk/DiNkZbtdWz2trizZ7SnRhMf6R7sQZL9\r\nKp4UDxv23zKSAZ54tVzanOBhHoArCdDdWTsALqvRP3fpEgEOjlXYupKAcXhL\r\nHsdTylPciA/pFC5PzlfRvWeaJ5t/172A9eu9MuhFKZUMhSLAKVpfmHbQBCex\r\nHgbbX0r7AK7wSdDrwkUc+WzmU4z6XHQyctg=\r\n=il4L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "300c5b6d4f52f39ba185479069b2af3f4ea1280b", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"tsup": "^6.2.3", "husky": "^8.0.1", "pinst": "^3.0.0", "eslint": "^8.23.1", "vitest": "^0.23.4", "typedoc": "^0.23.15", "prettier": "^2.7.1", "typescript": "^4.8.3", "lint-staged": "^13.0.3", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.1.2", "@sapphire/ts-config": "^3.3.4", "@vitest/coverage-c8": "^0.23.4", "@favware/cliff-jumper": "^1.8.7", "@favware/npm-deprecate": "^1.0.5", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@sapphire/eslint-config": "^4.3.8", "typedoc-plugin-mdn-links": "^2.0.0", "@sapphire/prettier-config": "^1.4.4", "@typescript-eslint/parser": "^5.37.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^1.0.11", "@commitlint/config-conventional": "^17.1.0", "@typescript-eslint/eslint-plugin": "^5.37.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.1.0_1663532552011_0.38272139573212494", "host": "s3://npm-registry-packages"}}, "2.1.0-next.5a14ed0.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.1.0-next.5a14ed0.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.1.0-next.5a14ed0.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "625dd5cc34f70d7c4aa8e1ada251e283fd68e265", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.1.0-next.5a14ed0.0.tgz", "fileCount": 12, "integrity": "sha512-INw1CJ0XneER2kfFUBv0m6lCCyp1zbCIT+sQ79jT3eN42AiWq2mIAnNEqCjXBTVj3eQuc1L6AXgCtRR/fNkP+g==", "signatures": [{"sig": "MEUCICmFuf/9UiZ6YZR3WC9eOKXOGDOXb/xHC0xuijxtkeO7AiEAvA2136/apU4Svg3tDwe/iUdtKOrapl/8aHuN4scnQ2Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 166200, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjJ35UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp8ng/+PoPR3xw8Uij2TykPQ/FYtLGmld1Iko4xVrY1itQWGWlkHtKS\r\n5B+2AePCuYZSfqiPDWKTTo8ngke/5W/NT1XDjpiO4Ofe08NwDN3igVgTiVTR\r\nSAWidC/9zZiKaqUERtO/5nENLKOMutNU2MBqw/mDjzVWGtkB1Y5z+HsTitPh\r\nLarSZ/VealjqSq94IUuGZFia5s2bG7HFk8xWaAxZTsItyRCVY0d72jObBC9l\r\n2hlCbrFkw5BA1YahLXGTWHXMqrBq5ouBrR7ixR6lyp7P8Q5e1N+ABOiFkVW+\r\nSj3VJLY2MgiCvwKQmfG6wYnsxXLsAIVH1kXJ+ccKeWcztXo/s9y53YbyyK3D\r\nfhHLUroyAenwnllZzAOOMwM0IG7bxu98ouKzhW7742FSSq/otMvJFOhmFMS2\r\n/I1F0Ioenl18zbnrTOkKNDk/uRnjGglolCYkCtVXdvHTHcw6eVuZKf377M71\r\nWRUIMEOWT0S38IKsqTN0EjZRNNvUQn/USTOVs8BVM/cR14SHYoR/b4koXuyn\r\nKbfysZmLgF16lZq+a5TbS27jFniZrSA3Vbby7yESOyfgfDM7hAL7RuOCsMgd\r\nN/JGb/aKWzqpW9CA83B5cjwHHnsJyA4jqLlKdFs3IaDgQtfd5JnzdwYjT97B\r\n1Tug2hzcgRWChDq9GBe2/87Qg5dh8KuJcig=\r\n=4laB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "5a14ed04bf87ec6a34cd33e26e3f25f101f87bcd", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"tsup": "^6.2.3", "husky": "^8.0.1", "pinst": "^3.0.0", "eslint": "^8.23.1", "vitest": "^0.23.4", "typedoc": "^0.23.15", "prettier": "^2.7.1", "typescript": "^4.8.3", "lint-staged": "^13.0.3", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.1.2", "@sapphire/ts-config": "^3.3.4", "@vitest/coverage-c8": "^0.23.4", "@favware/cliff-jumper": "^1.8.7", "@favware/npm-deprecate": "^1.0.5", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@sapphire/eslint-config": "^4.3.8", "typedoc-plugin-mdn-links": "^2.0.0", "@sapphire/prettier-config": "^1.4.4", "@typescript-eslint/parser": "^5.37.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^1.0.11", "@commitlint/config-conventional": "^17.1.0", "@typescript-eslint/eslint-plugin": "^5.37.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.1.0-next.5a14ed0.0_1663532627871_0.3147071771786054", "host": "s3://npm-registry-packages"}}, "2.1.1-next.300c5b6.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.1.1-next.300c5b6.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.1.1-next.300c5b6.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "e3b7a8ed657a12bb33524e3ddbf3fd90f4352f0d", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.1.1-next.300c5b6.0.tgz", "fileCount": 12, "integrity": "sha512-wXQh9IyoEN8b5PoOuBREPUuKytFdveHpOKvJX+W4afVhJntff2gk4mmL3BF+lJagyi5nnqUZrrWTHLNIDnfbqQ==", "signatures": [{"sig": "MEQCIDJ64uH5khvopAzt+QxKK+XMTI9r9MvBAxfLMb0R3FxnAiBTsG8oXQuKfHIVTdgjXARZkIFvxdpQCpHwiovWeG+oig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 166473, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjJ35fACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo/BA//SWRLFzobHOI+BRnmcnBLmT7z+2nVYlfxYMZxoUYxcuboB2ST\r\n7CqIYp3y4gfhInExfi2EWujltjmhk94dareRXfK45NeA/WGYizVYVCxJw8LY\r\nrCXIwTOmxrBVINLq5A2VJqV04LSz6xTpAffslj9S3YfuU1hYKbkqVj5mfRTl\r\npRhWtn27OUpHl/fE5Tm1w3MTpqg36TFnuJ/JFGTiYwWSHXkLmG0dSziB0Qha\r\ncGQXEYAtCtz8qyVoObzfaYS61SDGUOV7WuUpxUxJMcYP3RctY10sUVj6CeF5\r\nOUS/8fEbRhXXTee1tHUfXQdzGRNNYWtvSKoyTRyLWJYvwbtp2TkY3uqf5bRW\r\njupogClQtCMzZ20ZOZGK/YrxBmAoMMQqGlG5ubRQkXHp4BL7ElQ1ggGN5uQt\r\n2ZiMdh5Reba55UeaJH/ik+Hx5X8atH6gpHg/W3Y5S3n0TUdFTx2CnGHVCY5H\r\nUGsXYGcF7XaLr+lhCA3RXDfJGnl1G5pl/OabqJ6M51vTmk1+sR77ZKM/tNES\r\nTjUdycLQxrkf+cjyq7Qw0iWJSyOaFEF85hGzBPOYNeXtWGXnSMLiptQpkjh5\r\ncZQJ06jj/JEKluK/U1H+Byk3syGCXDDedNb9ig8SWmw3H1NgQZPuMedzCSuc\r\nxs5MJXzPrEha8LJ9WZqKb7Eom4rNot+D5kU=\r\n=3Aq8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "300c5b6d4f52f39ba185479069b2af3f4ea1280b", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"tsup": "^6.2.3", "husky": "^8.0.1", "pinst": "^3.0.0", "eslint": "^8.23.1", "vitest": "^0.23.4", "typedoc": "^0.23.15", "prettier": "^2.7.1", "typescript": "^4.8.3", "lint-staged": "^13.0.3", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.1.2", "@sapphire/ts-config": "^3.3.4", "@vitest/coverage-c8": "^0.23.4", "@favware/cliff-jumper": "^1.8.7", "@favware/npm-deprecate": "^1.0.5", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@sapphire/eslint-config": "^4.3.8", "typedoc-plugin-mdn-links": "^2.0.0", "@sapphire/prettier-config": "^1.4.4", "@typescript-eslint/parser": "^5.37.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^1.0.11", "@commitlint/config-conventional": "^17.1.0", "@typescript-eslint/eslint-plugin": "^5.37.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.1.1-next.300c5b6.0_1663532639740_0.24945325761272685", "host": "s3://npm-registry-packages"}}, "2.1.1": {"name": "@vladfrangu/async_event_emitter", "version": "2.1.1", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.1.1", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "0fd3fbbfa74c3cf824a802dbc664a5c61ed52242", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.1.1.tgz", "fileCount": 12, "integrity": "sha512-bjOygnpXTxGke5TdQjhnq09s6tngmqLZ8MQxtk3pPRYpptuNTWK9GRa8XxbZa4dEaoWzbYzCgyxgSWRrhOIlrg==", "signatures": [{"sig": "MEQCIEUSSJ1KYK8zDBa2xocBpml4QvUxMAIudRGi5DMH/RZ+AiAuA0RFMm8zQ/uXmbeu163Ww5PELokVFaQNax9IXWLKSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 166399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKH74ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoiog/+KoVzmQtSBloSDifqGgB8+cNbsPvyuKsIpzmzP0ZerDGsTmFS\r\nrAQBOFwiZsLdZV4H/GeYjVSaCqPmGYjdd3ToT/R50HbHM6LWWHToHcSVy4iO\r\nbdbPfQsZZmaX6X4UyxuypdAsDiIR0pQJfbQLvbyZnjKSG4hW0u8hvFgwFV2Q\r\niWhn3vwH/gKvBulHgV5Etk26FrNns29IJwnD8R2X+0H0c8b/jhdLibL0DvAn\r\nqz0Z/KYak2XkWr/S/Po0PnN6xSFws8f1WbZYhjPtE0LMH0yJ+2X+OOAdicRC\r\n3vHLhv/E7WpB7/Utb7ReL5vLOfTOpYCLljIwLAd7mEN+34EQlHrlIjeimtNf\r\nzVDC83HRpyFLmb0sKWxYiGmM41LVMlJwEylFEsllYHHuVMmjqYmeisL6Z9y6\r\nrrehIJvKOgOCOYSXWY18sGMbnLBLY4tJVMNotJXLpSAt6JQW5sXksaR8JZIA\r\n2bceMiKnmyVsRceoD+HhuYWjrR9P2PBcxwpjabna0Sf0SKXJrUuglT2rTrtw\r\n3+271ahnMSBc2hGDdBIux5lwfVEN6mFQg9TwskOMAa/KdLCCxJJWMs3uwbHw\r\nDWhtJAHJC53V5dG6tXNxd/qpiiRLEyQjNAzK8k2azujQyfLnrl2znL56Pfd0\r\n8U+VL/QqDSESF+JNBzxjKuprakPFaXe7tZM=\r\n=hvrS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "ecbb444042910802544704ead26ca5b41440ee3c", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"tsup": "^6.2.3", "husky": "^8.0.1", "pinst": "^3.0.0", "eslint": "^8.23.1", "vitest": "^0.23.4", "typedoc": "^0.23.15", "prettier": "^2.7.1", "typescript": "^4.8.3", "lint-staged": "^13.0.3", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.1.2", "@sapphire/ts-config": "^3.3.4", "@vitest/coverage-c8": "^0.23.4", "@favware/cliff-jumper": "^1.8.7", "@favware/npm-deprecate": "^1.0.5", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@sapphire/eslint-config": "^4.3.8", "typedoc-plugin-mdn-links": "^2.0.0", "@sapphire/prettier-config": "^1.4.4", "@typescript-eslint/parser": "^5.37.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^1.0.11", "@commitlint/config-conventional": "^17.1.0", "@typescript-eslint/eslint-plugin": "^5.37.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.1.1_1663598328079_0.29507543084142474", "host": "s3://npm-registry-packages"}}, "2.1.2-next.ecbb444.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.1.2-next.ecbb444.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.1.2-next.ecbb444.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "9c5e156fbeebf94119369c75bea74131fbfc16c3", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.1.2-next.ecbb444.0.tgz", "fileCount": 12, "integrity": "sha512-iMuSUXsJYHSdm8b/wqzo5fguTFVC9AqLjJ+aKpFfzPEwwG1G33WprJo2LJU8Ed4+g0CfFQl3V9GE74uLkakT8w==", "signatures": [{"sig": "MEYCIQCuh4OLyrw0dp4l1uC8IVcNzbTqfQVhOvbXY8HDBq6OVAIhALSOOVrEjMfpNO//Ig822LeUCffM+CoiuN83BwDyhA/G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 166414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKH8SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqIag/+Jjc5ZEKf1HqtiRyhQy86avyug1OjS3VxJxK8QA8yNxQPBU43\r\napQDc15cOzGU/c4utgVUmOvQfCcufcun574C6c/rnr9UDuSeAKd9bJqn9Qah\r\ng6W5pjHGJscbal1EA8T+fJgwy6wagPGZNxL6d8FwJuOMLtqG3/5IuEGKRhuy\r\nsU/HU/3RvMEJ+JMhM6+MMds9It0TKptrfLaOl6Jrfn1Rc3CNtlW25zvpJTND\r\nA7fTq8hjZSgTPzHSkUaipX7VRGvwKbnYEq0aK9yidKJRZMJOLruO7wqCR7gs\r\nMshUtYUUmDf+GOn3/WgiuNGnkY3jE3qbAJMqsPbHyJGKqQo4Kh6QnrDip0ZF\r\n+FTIQOs5luNNMnT8yjO8bo1WkzzWXY2NUSm77SWgdkEtkBt/FENlaHGGKIrs\r\nJZ3NlyWaXygBB4kINlaBW5j2b7SF7tHEipg8YCruTe97OcMeO7GUiCh27GpV\r\nY/fCVg9kVAoDTvHj2kTOHtaQfLyUygWzI0lN0scZKewu2ec6hQxdzUaXMzTK\r\nQmUtCzVaKiiTy6nQvv3O+CwMVZwuJJ5YdI852vHOhl2imngKkVOien2xCxM7\r\nkyCgrZ3lxdiciEoQ5HAQ96UzBwbEF5lPw9NtHLELgPjEOM0IF5K6KOp04ryG\r\nhX5s+0VzZdVVTN+dP9ttsBuy0Cmy+MVVHlY=\r\n=AJvI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "ecbb444042910802544704ead26ca5b41440ee3c", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"tsup": "^6.2.3", "husky": "^8.0.1", "pinst": "^3.0.0", "eslint": "^8.23.1", "vitest": "^0.23.4", "typedoc": "^0.23.15", "prettier": "^2.7.1", "typescript": "^4.8.3", "lint-staged": "^13.0.3", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.1.2", "@sapphire/ts-config": "^3.3.4", "@vitest/coverage-c8": "^0.23.4", "@favware/cliff-jumper": "^1.8.7", "@favware/npm-deprecate": "^1.0.5", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@sapphire/eslint-config": "^4.3.8", "typedoc-plugin-mdn-links": "^2.0.0", "@sapphire/prettier-config": "^1.4.4", "@typescript-eslint/parser": "^5.37.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^1.0.11", "@commitlint/config-conventional": "^17.1.0", "@typescript-eslint/eslint-plugin": "^5.37.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.1.2-next.ecbb444.0_1663598354029_0.24738662215979002", "host": "s3://npm-registry-packages"}}, "2.1.2": {"name": "@vladfrangu/async_event_emitter", "version": "2.1.2", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.1.2", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "85f04380d474edd1e1e5bd380dee04bc933fe995", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.1.2.tgz", "fileCount": 12, "integrity": "sha512-s/L3xf+BPb8EUOcVeqQ1pUwZNaWGl0br/GCCsdui5DTqn8bpGGu1S2JRTO3cyAytWObieetgTzOPuDN8nwNtGA==", "signatures": [{"sig": "MEYCIQDe+IeOVqSMooIxMy9A34FOHZF0+GkJPxbkX7XSmjUt+QIhAOuV7NyQme9GEgEXS+x4tOohpZZci/ySWWdu7FvrMQaU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 166948, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKH+nACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrnzA//dWEEEnnN2rS/dcmMNckIDtqm3nG564Ul34ssMpMRIhHusqVP\r\n3IGBnYu9L+9UFaUYd+c7Nxi0YkIiJs7XNZ4TXGAppCfcRNMhaL/bdk0Qrie0\r\nQeO90GQEifTiu18TqwJuMSzewvysCFAHt/WaaUx2HQ7A2vPM8UzSW5I41Gmb\r\nzk9aCEhhAOdBbb11CLrzN5eepETJkm9wr2ba8k5Nz05oT5nq38dBHoG8UIRp\r\n6y2fXBnpg1GEWcewdPmxnzUCmrbQZEF5Tcfs2xlRZbQNjmaPF+O2gDy0Qhby\r\nz83iQ8LXTTNE2vC6wTPt5KIVZyf79mHu9Iuygm4o8ma7Kzj6kPwEaTDzAjj0\r\n87qs4+6N9J0jaFL9VCxHD7aW1Sn7ZtFwumkHZlAKjo1HQ2jjGU3uiNbHMXoy\r\nW3OOEER22oXNnojINxGKTB7TuVMcXmhP1xQrg/x2jXmVMMC/4Ew8D3gjt9KH\r\n/od0/V9lYZeJu8IkVAYz1bZVOseBAVsLo82qq/jKRmMJaYv6yMs2O93JkI3X\r\nGJvH3/bGLcV/ttVgEnKubPgbXolAii4UdmzD03iUYU2rQMNJ/aJ6SyJ8cdtw\r\nxL1gNOtE8IWp5Ga4ZR7ERfa0R3AfcVPe6OIBK8QOboKp6hP/KaFXC0CtblZW\r\nPXT8wm8hJ3RIHx94ZDKE13kr71Qa6SO1IjU=\r\n=MmiB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "7c3e9f103e9a552c0c4984d9910b6db279792e9a", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"tsup": "^6.2.3", "husky": "^8.0.1", "pinst": "^3.0.0", "eslint": "^8.23.1", "vitest": "^0.23.4", "typedoc": "^0.23.15", "prettier": "^2.7.1", "typescript": "^4.8.3", "lint-staged": "^13.0.3", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.1.2", "@sapphire/ts-config": "^3.3.4", "@vitest/coverage-c8": "^0.23.4", "@favware/cliff-jumper": "^1.8.7", "@favware/npm-deprecate": "^1.0.5", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@sapphire/eslint-config": "^4.3.8", "typedoc-plugin-mdn-links": "^2.0.0", "@sapphire/prettier-config": "^1.4.4", "@typescript-eslint/parser": "^5.37.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^1.0.11", "@commitlint/config-conventional": "^17.1.0", "@typescript-eslint/eslint-plugin": "^5.37.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.1.2_1663598503606_0.6623915261949023", "host": "s3://npm-registry-packages"}}, "2.1.3-next.7c3e9f1.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.1.3-next.7c3e9f1.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.1.3-next.7c3e9f1.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "0a5a76f17679c3dd2bd46001f55ea61422a4be5c", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.1.3-next.7c3e9f1.0.tgz", "fileCount": 12, "integrity": "sha512-XmutVLAN9rWFWyGgxdLw4uOLceUsA3xfVZpHxeRfwI6waLpa5UOVqKU6ou6W8PvLmG7klD6hBYkms4Ez+KdsKw==", "signatures": [{"sig": "MEUCIGcEoyAmiKiZnTPrIdNJ/+zD+hvmRqAjcKwq5IY9EOxqAiEAlrUwgU3cQ7FYp+/VBdy/d9GG9TAAKeW1ya5qfaCQYCg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 166963, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKH+/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDvxAAhU8g30cqvdwGcQopb2IJrLW9VdQ3grpyxInbw7j0R9lSTx+W\r\nAo1iT+2SqWJ8Ci2vIvQd4XaSsNNuvChnVPoyM1zm/kRs6yKauP8a9UMLvhLg\r\nKF3a/v2vt08DbVWeZ5stHHwfFngFlIjGudbDgACyetMlWX6hwYX7JoikX47z\r\nUWs9m0SMb4pOnFoCniAFBvuf4sgYLk8BOmg16ZmJFsF1CPfhIgpg0HxlWsIb\r\nrbf5ei3naWEopYQoWJBQ1eM5GR9bn9HSQerj1uZegzKDrJQ9ykshETYs7a78\r\n1t1Yycvnq6I61ztABigIpMv4RisiOJVpcpyl5SULG+SQEJUFvTVo6KeFixI0\r\n+GvcObkrb0YHsPXyQ76vybv0nUQBiRpiKwEewBR20jgXORRX1VaaBc6oQ/ts\r\nCjKoghFDdM2tVX79d06id41Xqn4uGEs7SDSQhwJyZ7H0M7+E0CyP3yVn053k\r\n+ToDVeEKVLU2OJOGOo/AK9/LNjnbpi/hmRdJxGJ76fqR+26U7EVl1eIfXCi2\r\n1q+Qu4a7BLMFC9YnzPw39G97y5YN9hgVY1Lp8AJOIlAT7u81RAcJxP0z6qbx\r\nYY2lDxB1pMOfgIlekTurIluqZbs2NrYng8CTzMW+z64bttdh4rtutO/zOK8b\r\n1t7331y7Fv7g2CK9pYAb4j1a3hqzb0gqPVg=\r\n=liM2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "7c3e9f103e9a552c0c4984d9910b6db279792e9a", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"tsup": "^6.2.3", "husky": "^8.0.1", "pinst": "^3.0.0", "eslint": "^8.23.1", "vitest": "^0.23.4", "typedoc": "^0.23.15", "prettier": "^2.7.1", "typescript": "^4.8.3", "lint-staged": "^13.0.3", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.1.2", "@sapphire/ts-config": "^3.3.4", "@vitest/coverage-c8": "^0.23.4", "@favware/cliff-jumper": "^1.8.7", "@favware/npm-deprecate": "^1.0.5", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@sapphire/eslint-config": "^4.3.8", "typedoc-plugin-mdn-links": "^2.0.0", "@sapphire/prettier-config": "^1.4.4", "@typescript-eslint/parser": "^5.37.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^1.0.11", "@commitlint/config-conventional": "^17.1.0", "@typescript-eslint/eslint-plugin": "^5.37.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.1.3-next.7c3e9f1.0_1663598527284_0.01797745706243825", "host": "s3://npm-registry-packages"}}, "2.1.3": {"name": "@vladfrangu/async_event_emitter", "version": "2.1.3", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.1.3", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "92f0919d0efa26f5ce74087d487c77ac36f92679", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.1.3.tgz", "fileCount": 12, "integrity": "sha512-o09Pcdu8T9eL9iWcVWuJQnHLvQMYqMNaR5E2PD80A+uuL/u3CR6j0pCmaP1ILVspdAN7yeLnoyo8wljMXArj7Q==", "signatures": [{"sig": "MEYCIQDzskKIWlsQa9+efjN36mnnFuKF7q7/4mA25HjK7tr5jgIhAKKLvjUmMWdf5PCBxafmG8sFoxBZviZcVGfyqevO4Su6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvJ6PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrsow/+KJQlM8b9lu941oyzmOpP7mZwng4JYSx8VOzl58FnsWRWrAW1\r\nRk0zkQoIUIpzK6yVF+gqFNcnHp9s2/708GfdN9agqhs9UABsfdfRBBBCuuUo\r\nXzj8wr2BCgedZcEU13Bf22CbbYb3ob8JkZLw6sMgCPd5k3XPk/+vpQE4zE8z\r\nnqNSBmv+ZmOgsbibdlsFzOeoABTBCo1w4TOGZS+0QNq8iBpMtopNiXNRoFVk\r\nqMFJmt5UGjWj7zhzs0i4FABhGaTyZv253FAkQu/6tPcdzNub4TS9GhP1eg+T\r\nSlEtTAsWngZub7kkF/Dp396jxZsMFqF1dOzJjqUgnZn0xy04nJr7yF7VL0Vc\r\nT8Ge/ors/QC6vKTMnC+LbCOBULsP6mShFLQXj9XdE3jyeFhmZIHAj5ssHAXa\r\nEFhAm7UALI4t9r9qUzfnVLBxmlECgIQVrTCoaF8vrwhjMa1Q/7NelgnWe7HP\r\nt7A5sfWLZklFOTfyyICFUEEN2oGk6K1PJwuGV2iSdjxOoYDtpP6aqk3Xp6Za\r\n+TFp1jq+PonA5useAqbmTCLDpzgo0z2tEm7Hy5JTRjI2JqzsdUezT+zoSjUC\r\nOGoJLQ75GcBXTi03Y+UxlT21cjt9olQ4TlH2CHFkgF02vjWjTQWcRw7I9uet\r\nNFZE8cQIoSw0h4OhijtbgXgPpjj72Y4KWd0=\r\n=TLEG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "552c09dfc5ba1b5d34f35d623f8dc2dcd4843887", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"tsup": "^6.2.3", "husky": "^8.0.1", "pinst": "^3.0.0", "eslint": "^8.23.1", "vitest": "^0.23.4", "typedoc": "^0.23.15", "prettier": "^2.7.1", "typescript": "^4.8.3", "lint-staged": "^13.0.3", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.1.2", "@sapphire/ts-config": "^3.3.4", "@vitest/coverage-c8": "^0.23.4", "@favware/cliff-jumper": "^1.8.7", "@favware/npm-deprecate": "^1.0.5", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@sapphire/eslint-config": "^4.3.8", "typedoc-plugin-mdn-links": "^2.0.0", "@sapphire/prettier-config": "^1.4.4", "@typescript-eslint/parser": "^5.37.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^1.0.11", "@commitlint/config-conventional": "^17.1.0", "@typescript-eslint/eslint-plugin": "^5.37.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.1.3_1673305743444_0.7379969325665203", "host": "s3://npm-registry-packages"}}, "2.1.3-next.1533208.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.1.3-next.1533208.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.1.3-next.1533208.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "d988ba968b42f4b8bb3d3ab55345388dd6ef7256", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.1.3-next.1533208.0.tgz", "fileCount": 12, "integrity": "sha512-TgzGiDccZl3GT0axtoDgeQt55u3OicX64KvOkeu0ydEZ6PybmzMaGl8/E/rizvoT43BhKCxeqp+O9IHNhWK3Xw==", "signatures": [{"sig": "MEUCIQCMM0v1Zt8WrdKxdC3To2PqB4be6/Dw/tQVXuz/DPhJRgIgee+L1DgliEFmpbNKUwi1voTkjQ31mViH2IrjQveech4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvJ7QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq06BAAgN/b4Ynk0q7TUHu/Or+I2WGh+gXoW5/3ox0bhHYBVUXd+HUr\r\n7Myhw97u85XvjrQVMoBMBOx8py6I+ufZBjT0UF2B8E1x02HfF3p7oUfHuCV7\r\nUyCKen6mIGgssiCyT9bLA0+YcYRwyK1TzFzIidpYnRdw8IJc6WieeKk+vFxb\r\ndlwlb/OwmsNeXMrn7WSehvNI3jXWeoXEG4Bh4gg/AcaHB1yNbA49L36ZTKRI\r\niDRyY8H0ED/EHp5Kb46ohAtj4QR9uWGMSYE1iVR7hoqtcud3OvRuFlG17zt5\r\nDCfwbutoLTpyyPteF7DH6SxYBM7x6M89uce90azlDFmgY65ucZ/RF2KQAeg4\r\nmsU7mKUSnUGlQ6zJPwjS5RKL4sqFYXrbmVyUX2nfn8OIR9zMaa29Im4ZKGYZ\r\nuw5ykabfHHxDzTJGCYM/FBCMxnOGHUhoeZuHGydSUxOwW5EXCeduK2SxCnHJ\r\nMkxS62GfHXHnOJqXilbeH0fT6y3uQUW4kYBi3/HymP9F6+jUZTar1tOnNLwQ\r\nWRXyTJatYmQNB1w8CMMU6ZLDrCQtfVGW0DXl5aZ73IBua4Oe1GcpsEfIR5b2\r\n08TR5RgO4ofk/UQitwB9J6mgUPKVxkyzxkkP1aL4DgVFb7HnqEHybiWcP+eS\r\nNQavLcWPcBwaJD7WmWrDPyqN8NZv/ti0O+A=\r\n=YpWv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "15332086302645ad251dd3c05c7da1764c95824c", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"tsup": "^6.2.3", "husky": "^8.0.1", "pinst": "^3.0.0", "eslint": "^8.23.1", "vitest": "^0.23.4", "typedoc": "^0.23.15", "prettier": "^2.7.1", "typescript": "^4.8.3", "lint-staged": "^13.0.3", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.1.2", "@sapphire/ts-config": "^3.3.4", "@vitest/coverage-c8": "^0.23.4", "@favware/cliff-jumper": "^1.8.7", "@favware/npm-deprecate": "^1.0.5", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@sapphire/eslint-config": "^4.3.8", "typedoc-plugin-mdn-links": "^2.0.0", "@sapphire/prettier-config": "^1.4.4", "@typescript-eslint/parser": "^5.37.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^1.0.11", "@commitlint/config-conventional": "^17.1.0", "@typescript-eslint/eslint-plugin": "^5.37.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.1.3-next.1533208.0_1673305808290_0.5879018559499043", "host": "s3://npm-registry-packages"}}, "2.1.4-next.552c09d.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.1.4-next.552c09d.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.1.4-next.552c09d.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "5b29f6a3b3f272c243e1200ae3e1d636dd2b6e99", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.1.4-next.552c09d.0.tgz", "fileCount": 12, "integrity": "sha512-z1qCuBOCwUKf8QndFQ7VwlnhTG3dbfDpFB1MQ+k9+r3h9TOZisRe/ptaY2SkLMkNXWKjIsFBGMbVLcySFCU59w==", "signatures": [{"sig": "MEYCIQCh4jxIJATqf2FK7Utf88IurLlu/mOhy0Dtc3UBd1TyPwIhAPjy635IgI9wOLjEOlMhhS3UoiFxuossrz0B57dYvSvW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvJ7vACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQlw//UDmN2jFGxfcPkFS9AV1/wLq4cVV9WGCRHjKTl/oMRr88/wTi\r\ntnhnb9IXEt4Bk3LBgNDe40U+wQeIhZWR4r0lzndKH+c5EiAbGU6CButrKFA2\r\ndoeJQ/q4mlZwVNJe6tWZdgtgIOzfai4nnbIaR0yoDFapAU4kNNbEuA5oSLwi\r\n1clsDx+mGc5thxnEPIk95M5Lp1qy3trLMfBNfjliApkpVwJxz6NKc8+8agEU\r\n4+DXulYVKkBzFN1I3CXfywRC5+NaJ194nmqG0m643WY47A398pFn2HJbnoZt\r\nautx8wWh4g1aEgoCa8G6VdZjb7fw3FBiOnWc9viGi7tXe1s7QkkhehscPDk5\r\nuBeDWE55xV3I8kV/c18O1Juyg19hXD6W1fwgSjCCuBX7KR/AkpWHsAeivPSh\r\nMYh+7KfLCw2zKUuOQpzOjvblCYz0S7jYQgw/qY+e4viV/uJuze/6KnzA3Qsv\r\npDd0FMiAb13YI/Xs5nofk51b0bYqpsvjK0RWWiNU68tucjlLjHSsrjlj11F1\r\neLdcavfJw0CA+iKyD8LeXDuxcbSw78uFYNAUgcf85Jyg0T8gNt2Hqy5+hZKF\r\ndB/5M0JLVRvYvjA/aYgMgGsmx2CrRffrjgGQ8FNz4VmfV4U9vqF84rH3xbgq\r\nsVU+g9MgZmrP2h03L6voCjezJgmFJiCsHgI=\r\n=ZskL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "552c09dfc5ba1b5d34f35d623f8dc2dcd4843887", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"tsup": "^6.2.3", "husky": "^8.0.1", "pinst": "^3.0.0", "eslint": "^8.23.1", "vitest": "^0.23.4", "typedoc": "^0.23.15", "prettier": "^2.7.1", "typescript": "^4.8.3", "lint-staged": "^13.0.3", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.1.2", "@sapphire/ts-config": "^3.3.4", "@vitest/coverage-c8": "^0.23.4", "@favware/cliff-jumper": "^1.8.7", "@favware/npm-deprecate": "^1.0.5", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@sapphire/eslint-config": "^4.3.8", "typedoc-plugin-mdn-links": "^2.0.0", "@sapphire/prettier-config": "^1.4.4", "@typescript-eslint/parser": "^5.37.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^1.0.11", "@commitlint/config-conventional": "^17.1.0", "@typescript-eslint/eslint-plugin": "^5.37.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.1.4-next.552c09d.0_1673305839209_0.9800450894135555", "host": "s3://npm-registry-packages"}}, "2.1.4": {"name": "@vladfrangu/async_event_emitter", "version": "2.1.4", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.1.4", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "0d20cc9a8055f14da8b32d5d75dc8320ceffa3dd", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.1.4.tgz", "fileCount": 12, "integrity": "sha512-/YeRg3IvYCfRTH9BKHJZQvbG4Z9A9Fl24WzRpCny+eF/eyvDrHbWDV4GdqF+JSs/CdMdtbQ2qqfVzDfIfcQppw==", "signatures": [{"sig": "MEMCH2w7rXP7Sf1phKygfBSi+qO+oMIIgzkNb9sBIZHeyscCIEakNCppa6mKKU46bKdzCN2r74uXJAEhQK8gYnqRHQRO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164293, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8P1eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2qg//aPadR4PJKT7yJDjj3PHbO2NSPs097457JTmn+PnHb6ua9NYO\r\nq12v47j1x2mDst+RpbwbmlnyyS16sT88TMzaY8U9YYHCmHcCm6Uqz2Il+9Be\r\nYGVGF8PiWLpT3TytN/kZiWxmvyQiXt3BczYdEhZ0k9A91ws+3L3ASoO02RKu\r\nQv/At7loHoU4Hx3U6zB7TeBxIIDRFgtSSJv2B+h6nYhTIpKvpv+Q4nO99sTw\r\nfPSFFbDOMq4sFiYD+pvsstu+4uQLVT4kxHoZpgaGY2WC2N2dQV+H/kAbSRlq\r\nevIYDzqV7qKZMmLCOZGXN1hrPfyIljNRNG1204ij3jEdA5yTYBqoKApK2Miv\r\nblInwXty50l+fWODnt0cwgZdzOCYpAk/EF8P/yB+LTk3BaOSfUBB85N513ob\r\njg11vGM836bSwD1n8t1ZAypHtNtmoE6D3+NbCyUJXhpWlOVRkBmMMNNPkAbF\r\nzrLzRwERe0UW1aXTP6izNdrkEILBZ1y/t7eyth4n0K3IH1+z4TC7HJrZa35Q\r\nKatb/HzgRvkqtSEs7RGtK0Lb7bYKpy8jQV2VFagd6MoB6xGNtaM/Dq6Hz/7B\r\nSzC7LAkOunQtbzl5nGDECIfLAP+2cEuf9ufJYqQuW26f0Ja+06EIKkwXn9kc\r\ncYOeH4d3/SINOMpw0zzjxj8uFo45HxMnnTY=\r\n=7FJv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "922972700ffbbb94a650a7b7dba3e43efc671e38", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"tsup": "^6.6.3", "husky": "^8.0.3", "pinst": "^3.0.0", "eslint": "^8.34.0", "vitest": "^0.28.5", "typedoc": "^0.23.25", "prettier": "^2.8.4", "typescript": "^4.9.5", "lint-staged": "^13.1.2", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.4.4", "@sapphire/ts-config": "^3.3.4", "@vitest/coverage-c8": "^0.28.5", "@favware/cliff-jumper": "^1.10.0", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "@sapphire/eslint-config": "^4.3.8", "typedoc-plugin-mdn-links": "^2.0.2", "@sapphire/prettier-config": "^1.4.5", "@typescript-eslint/parser": "^5.52.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^2.0.0", "@commitlint/config-conventional": "^17.4.4", "@typescript-eslint/eslint-plugin": "^5.52.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.1.4_1676737886715_0.6878398447362946", "host": "s3://npm-registry-packages"}}, "2.1.4-next.4224bbe.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.1.4-next.4224bbe.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.1.4-next.4224bbe.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "37e78da1c2e3312ac47a6479c0e67f79d7a3ee4a", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.1.4-next.4224bbe.0.tgz", "fileCount": 12, "integrity": "sha512-g0twfYaZkMIzsMjAVbS3tXHhLhO31gFIJJUQpob5cd7UIU9Qlx0gDl2BclygVeJiOWGWllAgAuU1IU1RTeXIZA==", "signatures": [{"sig": "MEUCIQDx6wqxh4LLJvUg9PITY2+orXmeMAAihweFnds5h/oECQIgCniGLJIANZZgN9ZOLjRsY7uOGp8SHO44rm8x3F21org=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164024, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8P2hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmokZQ/7B/SjyEfQK3VfYLne6H0A8ZnPFA7CJy5zn9DyzOje4UHOaNLF\r\nLf2CMvCPUqs25XiR54WX+pVMS1ACIOEutFpoTirHyvO/2RA/nTTlUL4rZZuh\r\neeMcZKXBhBsxWGKhQfkcnAFoi73ljR5fLmMyElS5kCJ5kaGoCdpxnCE81Riq\r\nqp0Kt5yFjvwtJEIyDJH7il3/MJNKLrgdBjR0h+XAJeZX/M6i8O5Qzax7adPe\r\niCn4QQvihc3XwFHOsoYQKhUENPt52WheeN9sxgu86POawJE/OwHo6NCJlC4E\r\ntIqCO5fF4cJVyaT2iksibmvfMGbJH+LSjrGGJVINRMQ9hVpXN9GVClLyULtc\r\nJ8LB1K04SdGnHAg4iYllLt8YgoxqsjS9zMtK77H3tEtOY7XSlNRk7SyK25yV\r\nG5M3GhtxGL7QKQSjB7YY8BnXxTVoobUWV4VtY40nOuUo5de+t04EsZ2hWAp5\r\nrLYkSPHwBhx9eRvGKxWm/sj91e2zV48maiEye4k3n0BTEREmSgAKKT2kc55p\r\nq40Adaioh1j9yoN2VnKP825iDrQR+Wq1v/2xQW2U8hbgX+TDnwJlu5Rh64xL\r\nnXbAhy9AVETLXt1LK+2CwUDPncX5vRRhAJJ2DcY1j/FDqdO/tpVDLB/dvqBh\r\nq0huY8PJN92SvGWbUZMFwZYtDvWNs0StSto=\r\n=rjoY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "4224bbeae5c25cb94d4073600a9dff7ae3abcceb", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"tsup": "^6.6.3", "husky": "^8.0.3", "pinst": "^3.0.0", "eslint": "^8.34.0", "vitest": "^0.28.5", "typedoc": "^0.23.25", "prettier": "^2.8.4", "typescript": "^4.9.5", "lint-staged": "^13.1.2", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.4.4", "@sapphire/ts-config": "^3.3.4", "@vitest/coverage-c8": "^0.28.5", "@favware/cliff-jumper": "^1.10.0", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "@sapphire/eslint-config": "^4.3.8", "typedoc-plugin-mdn-links": "^2.0.2", "@sapphire/prettier-config": "^1.4.5", "@typescript-eslint/parser": "^5.52.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^2.0.0", "@commitlint/config-conventional": "^17.4.4", "@typescript-eslint/eslint-plugin": "^5.52.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.1.4-next.4224bbe.0_1676737952978_0.2701964798294758", "host": "s3://npm-registry-packages"}}, "2.1.5-next.9229727.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.1.5-next.9229727.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.1.5-next.9229727.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "fab0a5d598df764d65c1ca4affa522c4e34c77c4", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.1.5-next.9229727.0.tgz", "fileCount": 12, "integrity": "sha512-WhdW/4TmCjzbXiu27w+9bGDN0gKEfQzkJybTXjfeJo+43Gt1FAYisT7+d/KoFD56VCsEUcf1q3ZqoQLm6iYKcw==", "signatures": [{"sig": "MEUCIQCBhvoEmdb5GtYe5QOck2CEaF7ACIaGFUJlq9Hi2dL6TQIgX39IMAISYgCAqhrVecnlnLka+6ZZt4FCy4ZD/pX/QWE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8P2sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUVBAAgEELUX3jFiHgsZXd3ZlZs/9SswxI2zBJVGlVRo+vy326Q/XX\r\nelGRklQIMv2oiPwi93SnCijkDMDYWjx1oTstqvCDl94W1wI2Tn9EZPgO4aw5\r\nybx/iuVjQC5VWwy3IXIlvoFBr370MYPL9gkn0zFDjXARPodCFAxsbcBhdFSr\r\n5/5GQ8rzGczWaUddgl94lT1ingfFt5JkSHDaSGCogcLqIf8pK8OCLmrPdvvF\r\niMKk0SUy/YOeE0n6vaUILAShgDCGebhenL7vGsHtPo7xpc157YlNMNslRZak\r\nt+fDb+blEyCLAw5VD25H+3uqhldRtPv41gmRnmo05aM1OeqtgGEuq5k6We6E\r\n1ullO4R2QhCc+acGecpL6m7s5jfVeoKi+lbLw8EGGiz1bDg5wD6eaBu0bgzW\r\n2zJS0s2btbMN0vIiK+tGFrQzymzXwu9aZq5C2PCMoT3QdUOm2he9yv5Q5zki\r\nRQTgsY7PsQgbcBIYUr9/rxgCutvTSelNi7BjzjPFuQ/ZEVmZUIJzaQ+330eJ\r\nEq/c6dQo53cik3p837rDkdl9pnDy/fqAI1ZTsczDaoJBTvbvORsBR9rELr89\r\n6S6Wsud23F3QX6zhP2sDRB8wTuz1viM7B7yUZIemq9INUs7L6spOKY13GaXP\r\nU0WF/HQzgEd2HkdzaMM9Hx6mKNojBrGxs/I=\r\n=pu3J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "922972700ffbbb94a650a7b7dba3e43efc671e38", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"tsup": "^6.6.3", "husky": "^8.0.3", "pinst": "^3.0.0", "eslint": "^8.34.0", "vitest": "^0.28.5", "typedoc": "^0.23.25", "prettier": "^2.8.4", "typescript": "^4.9.5", "lint-staged": "^13.1.2", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.4.4", "@sapphire/ts-config": "^3.3.4", "@vitest/coverage-c8": "^0.28.5", "@favware/cliff-jumper": "^1.10.0", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "@sapphire/eslint-config": "^4.3.8", "typedoc-plugin-mdn-links": "^2.0.2", "@sapphire/prettier-config": "^1.4.5", "@typescript-eslint/parser": "^5.52.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^2.0.0", "@commitlint/config-conventional": "^17.4.4", "@typescript-eslint/eslint-plugin": "^5.52.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.1.5-next.9229727.0_1676737964632_0.2735082167155114", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.2.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.2.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "8c26e2eba5cd9d4ff25a15b79bf34f750b5fee6f", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.2.0.tgz", "fileCount": 12, "integrity": "sha512-euXJ2bzXoDeFHLy5rdxzDTRfcHIv/amZFzv5pQjWrj9UGpIDLQa0gG4jttha6+UTMkK05pleZmj9+OUZa92QXQ==", "signatures": [{"sig": "MEQCIFx8cvueMUkUINnAOMKSyXjXwOn+rUUjkUr+dWSRu7W1AiAsJzE7YlkdjCsIUNFA4b0U3L7z0JT25SWfs+QbsCxJ/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 184040, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMJzHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjjA/+PkVxLT1PPM/zkfBTeCUM/s2qUC8Dii1cWnXnzOAP2n/3TF+z\r\nuI677spjeodGBS7QWh0Z+uEgY2tSsguC02Jz3otEfaYEIqtNiTGvkfkft80G\r\n/kCGU/jjEliHOlGzlO4AqQxP10G1ml2q+IDp7ZMY9tqFqxy/5QKLtJWJsOKb\r\nCuUIyX+fJPW6VoVya5NMxyuwxmK2h1rcoyQ4KforUtAo0A/fsUlTIlwZyNzl\r\nLuGjJtSXxPeb/00YH9nKQ7k3dnGj+u95XS13c8xFBi6taB0WrHMAHSjxcyWC\r\nsEHom3dg1c+asV0Ka5eO3qZ+6vYAvjGvGb9FLm3Jn/1pr65qtZjwKy6w64W5\r\nIyEa46Cc4mq0+82HLjryi/6SDSFeMadx9K+Qz8QY2uJnY/TfBnc4p9JXAOYY\r\nLqM1VNsnyBltJctTswB/m/Dt1xRQSE/1T085VqFXCk3b4t/E0E5/9N9idxQv\r\nopRlyw+EYdmtvuDB1yx9Ssy1N2GRJsyqLBFSAKcbNP91COEGgIHzBiKVKnwQ\r\ngvdzkHpqvks4G/W+xZR0hj9rrXtOSSVWtLHI9UoGgsXxNJ866XWI6/VaeEFr\r\n5YRZhrWjswXb6dhpjNPJ1E5n+nBnFwqmHjO6RfWj67rPFgUHtU2Bbm+qQ8AF\r\ni9kIfhtrXM06lRsylWR3mFdXfk9vXkFcyDY=\r\n=Ibvu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "17379f51256e951efaf6e850ddce4872b9553a97", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"tsup": "^6.6.3", "husky": "^8.0.3", "pinst": "^3.0.0", "eslint": "^8.34.0", "vitest": "^0.28.5", "typedoc": "^0.23.25", "prettier": "^2.8.4", "typescript": "^4.9.5", "lint-staged": "^13.1.2", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.4.4", "@sapphire/ts-config": "^3.3.4", "@vitest/coverage-c8": "^0.28.5", "@favware/cliff-jumper": "^1.10.0", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "@sapphire/eslint-config": "^4.3.8", "typedoc-plugin-mdn-links": "^2.0.2", "@sapphire/prettier-config": "^1.4.5", "@typescript-eslint/parser": "^5.52.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^2.0.0", "@commitlint/config-conventional": "^17.4.4", "@typescript-eslint/eslint-plugin": "^5.52.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.2.0_1680907463225_0.04595581748390343", "host": "s3://npm-registry-packages"}}, "2.2.0-next.23eb908.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.2.0-next.23eb908.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.2.0-next.23eb908.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "51357ec1d130472e18424b9d55964de65b9669d7", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.2.0-next.23eb908.0.tgz", "fileCount": 12, "integrity": "sha512-WUeeKq80cnCD6U6wgm5M+KMwIC5yQTi9POYPLKF8GUgEuAuwPSYDOxnvoCK6nzhEL9lX7+LtHiNgEy5ZollXjg==", "signatures": [{"sig": "MEUCIG403Oe0djA2u22dFnBS+dZ5wohFUMOJ8HZ1cGZ5uw8lAiEAmChedLeC8kEA7/TrH5J1OyA267E8Z8sJBIVPTNvk5aA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMJ0RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqkCBAAhLBUckty1MkygsVmEYVaQ6a6PaIWU6qd1hdhcnoh8iT7Gy/N\r\nchbd1SuXl4/X5WqGT6JNeqiRIpwlx7LaACnm7FjzPMehu/lrDsY2d1Gu0rL3\r\noqFnwQUcogsYglFxyWb+qKTIPpbjORngatMGSMbEcQWv7Bb0GI7Brnz4zkSq\r\n1Eve5rnBBO13SMMNs8JVBWHE5U1pIah1KWiWYzas98+hrbFyIPewpUnD6vdM\r\nCtVlcN4cOF4vESJBgRdXSLoDVx9xQma5u4020NMUix6jXyEvoW4tT5WgBHT8\r\nYK/oHveWYL3HBBACgj3N/vqkYzhmPEPGIwGC6JMxd40RSjgrjNM/+7Pz+v9b\r\n609qRnZxZzl+1HZEBiAawU53jyZfMfCMS6WGbPYElH7tEvpqL2R3QIDvBAdC\r\nB5LOhuKMufWRvE0a4Tcf28Mr38suVDXC5cJd58rovCxUVtZjbMWn/HhAOS0N\r\nZuXvFbG+nnHPltRYVxrkiYq+Ye/jhHNhNqFw7gAVsvTIaU44N9BqEHZlhbrf\r\nra1itlIiXXMkaPiqw0yz7AG7CLTbcf9/ycDlv2QQx31trqVKI12lmnJSrr7S\r\nQPprDmnotC2RqAlXz4qb9ZsHIgY4hUB+3UxGhOUY9W38DyTPHpElOkSTiB5b\r\nf6XXVxEB8LF1/+HizzJ4C55JMxYWCw09dFM=\r\n=Ps5I\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "23eb90852ff8a6ceb4d6105c6df44c646642efae", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"tsup": "^6.6.3", "husky": "^8.0.3", "pinst": "^3.0.0", "eslint": "^8.34.0", "vitest": "^0.28.5", "typedoc": "^0.23.25", "prettier": "^2.8.4", "typescript": "^4.9.5", "lint-staged": "^13.1.2", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.4.4", "@sapphire/ts-config": "^3.3.4", "@vitest/coverage-c8": "^0.28.5", "@favware/cliff-jumper": "^1.10.0", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "@sapphire/eslint-config": "^4.3.8", "typedoc-plugin-mdn-links": "^2.0.2", "@sapphire/prettier-config": "^1.4.5", "@typescript-eslint/parser": "^5.52.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^2.0.0", "@commitlint/config-conventional": "^17.4.4", "@typescript-eslint/eslint-plugin": "^5.52.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.2.0-next.23eb908.0_1680907536900_0.0049189173829140564", "host": "s3://npm-registry-packages"}}, "2.2.1-next.17379f5.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.2.1-next.17379f5.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.2.1-next.17379f5.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "4bc83609bdfcec72f4b5c3e06199f4fa240a5b4c", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.2.1-next.17379f5.0.tgz", "fileCount": 12, "integrity": "sha512-Nmh+AdTf4nXiNvWjWl5fn4FwJEYoPL4WHdFxiMpSseFG2eEMnRBDVPo6zpt1Gl6ItVeYH4vTWkzcFBSH2qc1YA==", "signatures": [{"sig": "MEYCIQDMEpIIFgpQkcPI6B8vodOkDh32qNhz4ixtlJ+8c3WOEgIhAMOIPdlev9eT2674uZO6DejwDAu9XuTUsPiar/ldLCnQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 184055, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMJ0pACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXYBAAi1g9HetootevWpJo5nZbJHLSYINkBjSFk+JmlgIsm1I5+RxB\r\nHp78rV0vvdZF/dgvBRMLEojt+3rR4+7ZxmwNVWW4r5OI25El8OQ72la5L6uI\r\nGcMTYvLXRHt+5WmnVQK3ts9QxmXO10Gux8nuyKFmJOJmg6dNr38WOTVH+d/O\r\n6GCY592Dn7HBbj6499VcK27KfB0RkxpoSSGanlQYcGP4hpjNRwC6o4Fb7fHF\r\nu+PANAQSlxHL1/AKVXtmR+2l0+dXNnFjYMsv/Psk0wtw1aDz6ZF47RYdStRW\r\netGJ5mHRE9E0vcUw8ZVht3tSas6DQ8WJPpv17WUEo8pPM15UkvIp30Tuw6aO\r\nzTFNLZVwdsJweVttjR/W8BqKSQVZx5gvd/2vC0wGG4yisGRmVJ1gbNXeIqky\r\n5I9tCOJUWxcexEl6IwArBslchLGhKbMs9GR3pjA4Uml2V4oj67RJ91PyDdhp\r\neMlgQZ1Gnv/jgz8Y7fZyNtWuCFneTbloSzlmIeFKxSspd/OTkBLQ6AALwdNK\r\nwcR03Lm54YCqTKw2gAu/QvIi3TjaG89K2FtUk6Mtpx0VyzXHrlK2jGHfvaLf\r\nJqNLvohlKR0LB5jmzjgsffvfiyLeFtk1JGFmUn/3OED0isAC/MeIVC79uxxF\r\n78Oxnbyjmzis1e4u0ReptyB1lZ5achmVoQU=\r\n=lwyc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "17379f51256e951efaf6e850ddce4872b9553a97", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"tsup": "^6.6.3", "husky": "^8.0.3", "pinst": "^3.0.0", "eslint": "^8.34.0", "vitest": "^0.28.5", "typedoc": "^0.23.25", "prettier": "^2.8.4", "typescript": "^4.9.5", "lint-staged": "^13.1.2", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.4.4", "@sapphire/ts-config": "^3.3.4", "@vitest/coverage-c8": "^0.28.5", "@favware/cliff-jumper": "^1.10.0", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "@sapphire/eslint-config": "^4.3.8", "typedoc-plugin-mdn-links": "^2.0.2", "@sapphire/prettier-config": "^1.4.5", "@typescript-eslint/parser": "^5.52.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^2.0.0", "@commitlint/config-conventional": "^17.4.4", "@typescript-eslint/eslint-plugin": "^5.52.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.2.1-next.17379f5.0_1680907560843_0.25216992647731407", "host": "s3://npm-registry-packages"}}, "2.2.1": {"name": "@vladfrangu/async_event_emitter", "version": "2.2.1", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.2.1", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "5f44b3095d5edd695a9d12079f81b41826ad7831", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.2.1.tgz", "fileCount": 12, "integrity": "sha512-XtUEAS0m6uVddXW+EImGunLiJZzWNWAZQBoQCUneowrYXPQ6y7c0iWEm/wVYyGpTixTIhUfLRSoYCwojL64htA==", "signatures": [{"sig": "MEQCIAUAdaOVgmZb11rPitIjUtnzc4jUT/1R5txXnu7tF72hAiAPUFU+lNSmfplhvY2QlUEHMy138ewXbgC5yyKnfsU8ug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 184479, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMXUdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo2TQ/+L7XpXPPG7Xd9EKb8xAIEAFkuZBMTtmuDQGN4eToCRqhm/D37\r\nk3+lIMoAQk5SppY8kPkIUDN5QIVkaqAHd+M2SGRXPV48xeEZs63UiUmu877Y\r\nxgkQSQevtA2lG6Ihpgkxr43qLugE/MgpDOAC7wncYhNhCx7klnPYX/EObMVe\r\n3Ga/5REwdJQxjoIV4cdjasUKgkJ9PLwaDzqcoobK8x8X6oPe6XfQW1MxwhbM\r\n0VAwmyJMXeW6zZuTrYs618ZcJ9RfxaD1jNDmF0VGwBL4RQ9seLqlhwbA1Kd1\r\nfCoWLJ19CMsB2JKA/HCzWz75+Y6Kj9CiUPgMzfqshRBuxdG9FH/Vhm53qsGO\r\nY+WnPaNE0qHCzS+ydMZA/J1lzQeqRaN7iUJRnezvrn/u5vi8Pt4YeN07MxEy\r\na6SoLciDLZJiY7dX+Dj3YICwFJ2JcaIX7YDEUtLBAyNpGmqOdQcs3Jz0zwb+\r\nx9HqhBDsxHMh3+woAFooLxHNLiBfeEmh4aTKd0VcFCpAYnqRN8cVFO/GQRhj\r\ncbhAeqBX73qyTdvUmc1n4SCfaC7utKzJ7Gr/UqB9bTNuKyaS5hn0wHKzOdSR\r\npPdupaMNHFyZ0x4KzzngSBhDGMyQBn1LpscnSFrAhV3QSQOF/X7TgjxvrOZV\r\nU1hiIkcd7LDl9HJ3Dtgu8K8sP2udK2iABXI=\r\n=JIYR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "0f2e9a942b655dda744c0fbaaa97703269e892f2", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"tsup": "^6.6.3", "husky": "^8.0.3", "pinst": "^3.0.0", "eslint": "^8.34.0", "vitest": "^0.28.5", "typedoc": "^0.23.25", "prettier": "^2.8.4", "typescript": "^4.9.5", "lint-staged": "^13.1.2", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.4.4", "@sapphire/ts-config": "^3.3.4", "@vitest/coverage-c8": "^0.28.5", "@favware/cliff-jumper": "^1.10.0", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "@sapphire/eslint-config": "^4.3.8", "typedoc-plugin-mdn-links": "^2.0.2", "@sapphire/prettier-config": "^1.4.5", "@typescript-eslint/parser": "^5.52.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^2.0.0", "@commitlint/config-conventional": "^17.4.4", "@typescript-eslint/eslint-plugin": "^5.52.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.2.1_1680962845660_0.2554663615784656", "host": "s3://npm-registry-packages"}}, "2.2.2-next.0f2e9a9.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.2.2-next.0f2e9a9.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.2.2-next.0f2e9a9.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "669075f5483953d60724133dd52d796e42f1dfb0", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.2.2-next.0f2e9a9.0.tgz", "fileCount": 12, "integrity": "sha512-1+lTHWjYEE//TdOtPJ3eLFDoaL/sKCixvX1dQmPEQ18GBxwOVAShTIlzYEctW8tZE/pfrHbsogZDaj1iW0d9Dw==", "signatures": [{"sig": "MEYCIQCYU8tg8bL1tsl6u3uZUtMaqmZMlge57KCMUPvcIJmKrQIhANJtbxU06jklQhwD/p85kKMkGypu/ulmisfOioHoL7PB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 184494, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMXVBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoyHw/9GEUs2aZyzNbsbWr9oRhJYkcQwXUTRoeYJoPP1w2BkLWHXXf4\r\nmCx0PZHKDAAz7hp2xPy85cUlLEVB2s2DcgYwV6xYYtfoYu0emzlBu8DET1Kd\r\nZfSwT+XrER3FH9D7/bZuhguV7R8GO2vVQ5DOljfh6/dEC9QV2Xj2wCfyNztu\r\n5kLygly8uZLDvKNjU4k+emXoYD0cjBWvZStFo3cDo8NdSU1CB+013TnmHX/s\r\nN5idghp7TTObYlHS4VY2efHtXCd4NB0nbtf/XmN69iYlo61omokjCrpz7Qpd\r\nLfMKXj7ZPU1XHwgZh/YdExLox5wDGFxz3i5bhQglc5aS4pWqXxb5/A1wPgcq\r\nfDoCQmfkDyVKfXfLFfEechHf4+s627Y1saAxaC0WiQrZShtYawaabCx4MIzF\r\nHxH7TCPSXzRBLFSbsx1RuapnRhq/dZY3EieHyikWgXDIQq0jMNIoyCCqiNeC\r\nXqZkfYK+B0L+DKGB9+06Xo4W/CIscF+NDbJlPtohcajQ06eIziVgiOYb4qrV\r\nQmcDdQ6WvXyX6rfkZ63fwXMOnBoq724RxqLWf+CFxDMvgNlxkWC94AapSNCj\r\nrQh4E6PBj1UghyyfLckIksl5pQEsHXGPeUOtezKVrY6kCRXbvOM2y4D1Ivd9\r\nPcgxUEIjYCVgM4PnO9WQyX/it4QRI0L3/QI=\r\n=S43k\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "0f2e9a942b655dda744c0fbaaa97703269e892f2", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"tsup": "^6.6.3", "husky": "^8.0.3", "pinst": "^3.0.0", "eslint": "^8.34.0", "vitest": "^0.28.5", "typedoc": "^0.23.25", "prettier": "^2.8.4", "typescript": "^4.9.5", "lint-staged": "^13.1.2", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.4.4", "@sapphire/ts-config": "^3.3.4", "@vitest/coverage-c8": "^0.28.5", "@favware/cliff-jumper": "^1.10.0", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "@sapphire/eslint-config": "^4.3.8", "typedoc-plugin-mdn-links": "^2.0.2", "@sapphire/prettier-config": "^1.4.5", "@typescript-eslint/parser": "^5.52.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^2.0.0", "@commitlint/config-conventional": "^17.4.4", "@typescript-eslint/eslint-plugin": "^5.52.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.2.2-next.0f2e9a9.0_1680962881156_0.22631242736938173", "host": "s3://npm-registry-packages"}}, "2.2.2": {"name": "@vladfrangu/async_event_emitter", "version": "2.2.2", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.2.2", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "84c5a3f8d648842cec5cc649b88df599af32ed88", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.2.2.tgz", "fileCount": 12, "integrity": "sha512-HIzRG7sy88UZjBJamssEczH5q7t5+axva19UbZLO6u0ySbYPrwzWiXBcC0WuHyhKKoeCyneH+FvYzKQq/zTtkQ==", "signatures": [{"sig": "MEQCIC0au+2Tl/bQx7KWpa6N8A0xNVIyD4k0sx/gBr3s25OxAiAh7d0aXSpuVqqWwO2t2jk59WIa39W0OKPJshj/2DUIvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185818}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "73d1465f4914caec26cff310eafbc2196904c51a", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"tsup": "^6.6.3", "husky": "^8.0.3", "pinst": "^3.0.0", "eslint": "^8.34.0", "vitest": "^0.28.5", "typedoc": "^0.23.25", "prettier": "^2.8.4", "typescript": "^4.9.5", "lint-staged": "^13.1.2", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.4.4", "@sapphire/ts-config": "^3.3.4", "@vitest/coverage-c8": "^0.28.5", "@favware/cliff-jumper": "^1.10.0", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "@sapphire/eslint-config": "^4.3.8", "typedoc-plugin-mdn-links": "^2.0.2", "@sapphire/prettier-config": "^1.4.5", "@typescript-eslint/parser": "^5.52.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^2.0.0", "@commitlint/config-conventional": "^17.4.4", "@typescript-eslint/eslint-plugin": "^5.52.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.2.2_1683786369908_0.31454882692052166", "host": "s3://npm-registry-packages"}}, "2.2.3-next.73d1465.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.2.3-next.73d1465.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.2.3-next.73d1465.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "090d3ffb82e5005979c0ba3102ae0875f004d187", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.2.3-next.73d1465.0.tgz", "fileCount": 12, "integrity": "sha512-Mv6H+86eA9sxEx/GHKVVyvRRtk29ARNq+OJx1TeH3Vb2xp6/xa3eU53iMHNBy8Ku6A0N31HLzIPIFHTwAYH8tg==", "signatures": [{"sig": "MEUCIQDjrLP0E5E4FA9/wFMbV8jg6ElPUe5Btbqa4SO+9l3yrwIgSCK30xU61EWo+Unnwz5lA60nPgpqilnaj5gpwoKABkQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185833}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "gitHead": "73d1465f4914caec26cff310eafbc2196904c51a", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsup && tsc -b src", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "rollup-type-bundler && pinst --disable", "postpack": "pinst --enable", "typecheck": "tsc -p tsconfig.typecheck.json", "test:watch": "vitest", "_postinstall": "husky install .github/husky", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@3.2.1", "devDependencies": {"tsup": "^6.6.3", "husky": "^8.0.3", "pinst": "^3.0.0", "eslint": "^8.34.0", "vitest": "^0.28.5", "typedoc": "^0.23.25", "prettier": "^2.8.4", "typescript": "^4.9.5", "lint-staged": "^13.1.2", "pretty-quick": "^3.1.3", "@commitlint/cli": "^17.4.4", "@sapphire/ts-config": "^3.3.4", "@vitest/coverage-c8": "^0.28.5", "@favware/cliff-jumper": "^1.10.0", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "@sapphire/eslint-config": "^4.3.8", "typedoc-plugin-mdn-links": "^2.0.2", "@sapphire/prettier-config": "^1.4.5", "@typescript-eslint/parser": "^5.52.0", "cz-conventional-changelog": "^3.3.0", "@favware/rollup-type-bundler": "^2.0.0", "@commitlint/config-conventional": "^17.4.4", "@typescript-eslint/eslint-plugin": "^5.52.0"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.2.3-next.73d1465.0_1683786423795_0.2706072361677885", "host": "s3://npm-registry-packages"}}, "2.2.3-next.6cce45a.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.2.3-next.6cce45a.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.2.3-next.6cce45a.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "e53e46437b9d23a9853d53b50acfc7ed9e756943", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.2.3-next.6cce45a.0.tgz", "fileCount": 5, "integrity": "sha512-gvsmhupsS6vPH+Cgj4C4qNvJjtPmfVS+Gf7+1eqJyhPs0nItVSqnPvNuj6Hs0qhMl+kKo0oQYA4oG8yvSfBMBg==", "signatures": [{"sig": "MEQCIGJPq0aT79Jt9TZb4CjDROf+gEU/JwJzIi0EQEWEkyoxAiAIUpMWVTr6lCDnyhZs3+xzu9O7fL30AJ8Igi/NGwsnoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10565}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "gitHead": "6cce45a004514e578c397bef46575a14407b11fd", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && tsup && node ./scripts/postbuild.mjs", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.0.2", "devDependencies": {"tsup": "^8.0.1", "eslint": "^8.55.0", "vitest": "^0.34.6", "typedoc": "^0.25.4", "prettier": "^3.1.0", "typescript": "^5.3.2", "lint-staged": "^15.1.0", "@commitlint/cli": "^18.4.3", "@sapphire/ts-config": "^5.0.0", "@vitest/coverage-c8": "^0.33.0", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "@sapphire/eslint-config": "^5.0.2", "typedoc-plugin-mdn-links": "^3.1.6", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^6.13.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^18.4.3", "@typescript-eslint/eslint-plugin": "^6.13.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.2.3-next.6cce45a.0_1701568971485_0.13634016890355483", "host": "s3://npm-registry-packages"}}, "2.2.3": {"name": "@vladfrangu/async_event_emitter", "version": "2.2.3", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.2.3", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "545914c29484f9e6f863ee0f2a498da05b550a6d", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.2.3.tgz", "fileCount": 13, "integrity": "sha512-JSDoNoZ1VLVAD5xZbKiExFBJNtaNBJ1SmiDGFSOf8nFbUafENSUCURYcJeNIqrcVbECzAYyJ3kgXdR2qBF1IIQ==", "signatures": [{"sig": "MEQCIGqtZ8nkclCew1ez96Zszb5yIVW4D890IXiO4sgkxjWAAiBY1r8McVa4nOLrcPpBry+N9wvRMC6ACVUKho1wfhcG3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194046}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "gitHead": "9a42f92809eb93e662efb3e5964063836e9548a7", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && tsup && node ./scripts/postbuild.mjs", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.0.2", "devDependencies": {"tsup": "^8.0.1", "eslint": "^8.55.0", "vitest": "^0.34.6", "typedoc": "^0.25.4", "prettier": "^3.1.0", "typescript": "^5.3.2", "lint-staged": "^15.1.0", "@commitlint/cli": "^18.4.3", "@sapphire/ts-config": "^5.0.0", "@vitest/coverage-c8": "^0.33.0", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "@sapphire/eslint-config": "^5.0.2", "typedoc-plugin-mdn-links": "^3.1.6", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^6.13.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^18.4.3", "@typescript-eslint/eslint-plugin": "^6.13.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.2.3_1701568986625_0.2166718407637378", "host": "s3://npm-registry-packages"}}, "2.2.4-next.9a42f92.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.2.4-next.9a42f92.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.2.4-next.9a42f92.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "700d52086ea6842433ea1d58578f46486e717a3a", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.2.4-next.9a42f92.0.tgz", "fileCount": 5, "integrity": "sha512-HC1dvFoqZkPqth1xEVUiRudt/5tUtcrOBGcKgVloIg3AhzIATmUFVMInAXdYaHsf7Xp6KjQs2u/T24UQSg/9Uw==", "signatures": [{"sig": "MEQCIHVZ241sWQ5ZOagdAU0SrjVWj87g6UXPHA+gqXL/595tAiAUKW2MXu67lpa8SnQPUsDUwVANeulI59c8sXaxRx9H2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10565}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "gitHead": "9a42f92809eb93e662efb3e5964063836e9548a7", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && tsup && node ./scripts/postbuild.mjs", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.0.2", "devDependencies": {"tsup": "^8.0.1", "eslint": "^8.55.0", "vitest": "^0.34.6", "typedoc": "^0.25.4", "prettier": "^3.1.0", "typescript": "^5.3.2", "lint-staged": "^15.1.0", "@commitlint/cli": "^18.4.3", "@sapphire/ts-config": "^5.0.0", "@vitest/coverage-c8": "^0.33.0", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "@sapphire/eslint-config": "^5.0.2", "typedoc-plugin-mdn-links": "^3.1.6", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^6.13.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^18.4.3", "@typescript-eslint/eslint-plugin": "^6.13.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.2.4-next.9a42f92.0_1701569001096_0.4767732518000396", "host": "s3://npm-registry-packages"}}, "2.2.4-next.5ca1cb7.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.2.4-next.5ca1cb7.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.2.4-next.5ca1cb7.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "9e2b8a4de5e9ee9a78f607c9efb1ed52ff085990", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.2.4-next.5ca1cb7.0.tgz", "fileCount": 5, "integrity": "sha512-fe1iQGz6QVDmiZLdFt8Vghe5BrF87j6tJLg2cAhoc1UKNdYxQ7tJEMEFe32zqn9Vl4Gpo3XxWVSBh9AB5Vcn2Q==", "signatures": [{"sig": "MEUCICvtfW7y2Xjxk7kAL1jMXFPyZhLwTcQ8bqebqfZMZzFBAiEA6f0vk5ey9AtagCaAWGV7t77Ah3UczPcuG4VSofHpTZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10603}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "gitHead": "5ca1cb7bfec363c11c4391c7cdd046b26cb739d3", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && tsup && node ./scripts/postbuild.mjs", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.0.2", "devDependencies": {"tsup": "^8.0.1", "eslint": "^8.55.0", "vitest": "^0.34.6", "typedoc": "^0.25.4", "prettier": "^3.1.0", "typescript": "^5.3.2", "lint-staged": "^15.1.0", "@commitlint/cli": "^18.4.3", "@sapphire/ts-config": "^5.0.0", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^0.34.6", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "@sapphire/eslint-config": "^5.0.2", "typedoc-plugin-mdn-links": "^3.1.6", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^6.13.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^18.4.3", "@typescript-eslint/eslint-plugin": "^6.13.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.2.4-next.5ca1cb7.0_1701569091275_0.11587724773501162", "host": "s3://npm-registry-packages"}}, "2.2.4-next.6235ff3.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.2.4-next.6235ff3.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.2.4-next.6235ff3.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "a64ee6b60f0900c806c00e54534dc63fb69d4d2c", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.2.4-next.6235ff3.0.tgz", "fileCount": 5, "integrity": "sha512-2btqe57f+9ioS1d6J2fg9bdy8za4qvD4pkT+f0TAKzhWpas9jEk5jzBL4e/gIv14n7CovP5/X2fd1bqs3rw2PQ==", "signatures": [{"sig": "MEQCIEnA+16yuezpg98oRqHB8wVq0Xfjcn2Gx23mvDC5nPo9AiB/cqY+HiO28jw8G5JfoxWkDezES+OLwp05RC0FMZhKnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10603}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "gitHead": "6235ff356c88297436336d031d515e23d7565137", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && tsup && node ./scripts/postbuild.mjs", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.0.2", "devDependencies": {"tsup": "^8.0.1", "eslint": "^8.55.0", "vitest": "^0.34.6", "typedoc": "^0.25.4", "prettier": "^3.1.0", "typescript": "^5.3.2", "lint-staged": "^15.1.0", "@commitlint/cli": "^18.4.3", "@sapphire/ts-config": "^5.0.0", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^0.34.6", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "@sapphire/eslint-config": "^5.0.2", "typedoc-plugin-mdn-links": "^3.1.6", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^6.13.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^18.4.3", "@typescript-eslint/eslint-plugin": "^6.13.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.2.4-next.6235ff3.0_1701569227398_0.6952601973412489", "host": "s3://npm-registry-packages"}}, "2.2.4-next.91d4d86.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.2.4-next.91d4d86.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.2.4-next.91d4d86.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "ee71ccdc90232786a2805c86b78690222a5677e2", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.2.4-next.91d4d86.0.tgz", "fileCount": 5, "integrity": "sha512-llarzvziG+43/xIfj8WHuattzQONGark8cQGQo0xjqLVVooAogTfsz9qfGig6W4ZtuvYFzPftd7W+GDcaIe3ZQ==", "signatures": [{"sig": "MEQCIAV803KnQrVeQ7OQoiadfE8pI/u9SJUtKI2/+gJSbE0fAiARRKjZ0wD/gG1ne3P11AmwTEuW/ansmJbk+CVh19Q3Jw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10622}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "gitHead": "91d4d86c83299ffe92c70eda6b938d142e1e81b9", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && tsup && node ./scripts/postbuild.mjs", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.0.2", "devDependencies": {"tsup": "^8.0.1", "eslint": "^8.55.0", "vitest": "^0.34.6", "typedoc": "^0.25.4", "prettier": "^3.1.0", "typescript": "^5.3.2", "lint-staged": "^15.1.0", "@commitlint/cli": "^18.4.3", "@sapphire/ts-config": "^5.0.0", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^0.34.6", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "@sapphire/eslint-config": "^5.0.2", "typedoc-plugin-mdn-links": "^3.1.6", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^6.13.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^18.4.3", "@typescript-eslint/eslint-plugin": "^6.13.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.2.4-next.91d4d86.0_1701569419811_0.30204746068080945", "host": "s3://npm-registry-packages"}}, "2.2.4-next.8dc308c.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.2.4-next.8dc308c.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.2.4-next.8dc308c.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "568634e2e2e3a98e90ac9741c49220729a3f8028", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.2.4-next.8dc308c.0.tgz", "fileCount": 5, "integrity": "sha512-SyVFHdvPOiS0Fr1U5zgT0iajUMAySzANCndB+7Nh+YcmwMv+0s+//M0yo85zV7jazzhOfup9az3754cW6AA9kw==", "signatures": [{"sig": "MEYCIQC3ydjBs9gWaIwIuSg9XavzgZRusxd1BXdFop28jssg4AIhALUkejUykPdGzzffoCsOiRbtYCyBiK25ftG17TAzzCKC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10625}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "gitHead": "8dc308c43fdf9a024a7d48e7219d75e4c9f597dd", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && tsup && node ./scripts/postbuild.mjs", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "deprecated": "This version has been automatically deprecated by @favware/npm-deprecate. Please use a newer version.", "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.0.2", "devDependencies": {"tsup": "^8.0.1", "eslint": "^8.55.0", "vitest": "^0.34.6", "typedoc": "^0.25.4", "prettier": "^3.1.0", "typescript": "^5.3.2", "lint-staged": "^15.1.0", "@commitlint/cli": "^18.4.3", "@sapphire/ts-config": "^5.0.0", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^0.34.6", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "@sapphire/eslint-config": "^5.0.2", "typedoc-plugin-mdn-links": "^3.1.6", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^6.13.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^18.4.3", "@typescript-eslint/eslint-plugin": "^6.13.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.2.4-next.8dc308c.0_1701594373891_0.48310478441902704", "host": "s3://npm-registry-packages"}}, "2.2.4": {"name": "@vladfrangu/async_event_emitter", "version": "2.2.4", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.2.4", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "d3537432c6db6444680a596271dff8ea407343b3", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.2.4.tgz", "fileCount": 13, "integrity": "sha512-ButUPz9E9cXMLgvAW8aLAKKJJsPu1dY1/l/E8xzLFuysowXygs6GBcyunK9rnGC4zTsnIc2mQo71rGw9U+Ykug==", "signatures": [{"sig": "MEQCIEU2xnXCCfbD1HUwYpNPxrwYhHSlzV7uusf/GYuPY9tHAiBsphkcGxRQkG9LIBidaLqk7O4q8dUbXv4E5CamlOcuKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194391}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "gitHead": "b6839ad5e5b6b5ef4f0439b6940ee1aa30002df8", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && tsup && node ./scripts/postbuild.mjs", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.0.2", "devDependencies": {"tsup": "^8.0.1", "eslint": "^8.55.0", "vitest": "^0.34.6", "typedoc": "^0.25.4", "prettier": "^3.1.0", "typescript": "^5.3.2", "lint-staged": "^15.1.0", "@commitlint/cli": "^18.4.3", "@sapphire/ts-config": "^5.0.0", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^0.34.6", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "@sapphire/eslint-config": "^5.0.2", "typedoc-plugin-mdn-links": "^3.1.6", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^6.13.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^18.4.3", "@typescript-eslint/eslint-plugin": "^6.13.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.2.4_1701594400979_0.9999924132833364", "host": "s3://npm-registry-packages"}}, "2.2.5-next.b6839ad.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.2.5-next.b6839ad.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.2.5-next.b6839ad.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "c42b6775ad2e1bc9cdd2b66d6777a32ef073b3f3", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.2.5-next.b6839ad.0.tgz", "fileCount": 5, "integrity": "sha512-FFJVl7GKo1OlUQe+UkmjljZZBSV7WArwUf1CXBMKjTWaqGWbX9T86iS1s9WV5oAZITjYefZ0FHs8k07/TT3JLw==", "signatures": [{"sig": "MEYCIQC5P4gTvNcAK6aXUot/bN+qRpJasKmo0cCsCn5W5IOOMgIhAO/dn3sA5TFpqdwyxPlO8JdW+yS6nmSlXPzMHWpCYYiz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10910}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "gitHead": "b6839ad5e5b6b5ef4f0439b6940ee1aa30002df8", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && tsup && node ./scripts/postbuild.mjs", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.0.2", "devDependencies": {"tsup": "^8.0.1", "eslint": "^8.55.0", "vitest": "^0.34.6", "typedoc": "^0.25.4", "prettier": "^3.1.0", "typescript": "^5.3.2", "lint-staged": "^15.1.0", "@commitlint/cli": "^18.4.3", "@sapphire/ts-config": "^5.0.0", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^0.34.6", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "@sapphire/eslint-config": "^5.0.2", "typedoc-plugin-mdn-links": "^3.1.6", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^6.13.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^18.4.3", "@typescript-eslint/eslint-plugin": "^6.13.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.2.5-next.b6839ad.0_1701594408200_0.8519659611341153", "host": "s3://npm-registry-packages"}}, "2.3.0-next.0f4029e.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.3.0-next.0f4029e.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.3.0-next.0f4029e.0", "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "057109646f2be8d8dc24c5891e6a2f442ca5ae13", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.3.0-next.0f4029e.0.tgz", "fileCount": 5, "integrity": "sha512-Pz3iUgNgVziy7rifO3OzHOUFI4Zhy29dNeemFmVM6XR3FDIYLXuwtKVKo+B2DxzNzKwAoe0r4vLUoDDAhOWwXw==", "signatures": [{"sig": "MEUCIQDrmENpnKqZkYFpaUt5+31YGE8/+fCU6Dpm3magv+nEvAIgc3KIJihgUhI4puibsg5DhLA6Gmj2dIa4EZqwerJKUXY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10878}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "gitHead": "0f4029e9935373e15073288da1841ffb39cc22d8", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && tsup", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.0.2", "devDependencies": {"tsup": "^8.0.1", "eslint": "^8.55.0", "vitest": "^0.34.6", "typedoc": "^0.25.4", "prettier": "^3.1.0", "typescript": "^5.3.2", "lint-staged": "^15.1.0", "@commitlint/cli": "^18.4.3", "@sapphire/ts-config": "^5.0.0", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^0.34.6", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "@sapphire/eslint-config": "^5.0.2", "typedoc-plugin-mdn-links": "^3.1.6", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^6.13.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^18.4.3", "@typescript-eslint/eslint-plugin": "^6.13.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.3.0-next.0f4029e.0_1719001947760_0.7793628252876807", "host": "s3://npm-registry-packages"}}, "2.3.1-next.a50001c.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.3.1-next.a50001c.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.3.1-next.a50001c.0", "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "7c0ec28c8553e38ddced79cb4ad488c7bcd0b805", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.3.1-next.a50001c.0.tgz", "fileCount": 5, "integrity": "sha512-Bc3EGN9FILWLLSuHGgSMWSEacR+irL74NsspjyYDPbfyI5EESCDq5/cOjIrwxCR1oDVBV45k+A1CUq68U/zn3g==", "signatures": [{"sig": "MEUCIGNL7ULE0iSn8TiQ9sGOLC8fVTyIfCCJMdAaW1/mJ/32AiEA9rDx0vAJP/v8bclLJScTgJcg7VRX724cyI0gJI+2rsk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11135}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "gitHead": "a50001c9a43a436d6de6bbbd43418be09c08bf38", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && tsup", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.0.2", "devDependencies": {"tsup": "^8.0.1", "eslint": "^8.55.0", "vitest": "^0.34.6", "typedoc": "^0.25.4", "prettier": "^3.1.0", "typescript": "^5.3.2", "lint-staged": "^15.1.0", "@commitlint/cli": "^18.4.3", "@sapphire/ts-config": "^5.0.0", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^0.34.6", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "@sapphire/eslint-config": "^5.0.2", "typedoc-plugin-mdn-links": "^3.1.6", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^6.13.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^18.4.3", "@typescript-eslint/eslint-plugin": "^6.13.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.3.1-next.a50001c.0_1719001964815_0.22777436984199007", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.3.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.3.0", "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "666c71134f45100718efab6e35c505ff3eec54c4", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.3.0.tgz", "fileCount": 13, "integrity": "sha512-pmkbRGAqTM3N7R4KCObOAqqoZzeURR6oC11amaXmY4ZLShu4MdLymm1/F0fZssYKAsSE79TibRbdXwfvYMIt0Q==", "signatures": [{"sig": "MEYCIQD7El3ENMsDk81l1nyLnCXpN84UB4SKGcgjgu+xl2nS8gIhAOH80Ea23elDxF7RzKrx/SbDNGPJZrmH4c9hbzlBD3rI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194616}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "gitHead": "a50001c9a43a436d6de6bbbd43418be09c08bf38", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && tsup", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "prettier": "@sapphire/prettier-config", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "resolutions": {"minimist": "^1.2.6", "ansi-regex": "^5.0.1"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.0.2", "devDependencies": {"tsup": "^8.0.1", "eslint": "^8.55.0", "vitest": "^0.34.6", "typedoc": "^0.25.4", "prettier": "^3.1.0", "typescript": "^5.3.2", "lint-staged": "^15.1.0", "@commitlint/cli": "^18.4.3", "@sapphire/ts-config": "^5.0.0", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^0.34.6", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "@sapphire/eslint-config": "^5.0.2", "typedoc-plugin-mdn-links": "^3.1.6", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^6.13.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^18.4.3", "@typescript-eslint/eslint-plugin": "^6.13.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.3.0_1719001966450_0.39683266628444147", "host": "s3://npm-registry-packages"}}, "2.4.0-next.c1b0f75.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.4.0-next.c1b0f75.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.4.0-next.c1b0f75.0", "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "167694c7f1a01629399193c521cda77dc2db0804", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.4.0-next.c1b0f75.0.tgz", "fileCount": 5, "integrity": "sha512-pyyWguvgnOACS+WZ1ud8bXxariQqnSr5o8SAi3LMUZME5VGBKpBAkGfn7aJrpuHD3GN8o0DHSvfGrWukfVfsew==", "signatures": [{"sig": "MEYCIQCRvmm4Q3ZncEQGpyrvGrMBjtEXUjGgQbO7A3fMWPvW5QIhAPu9e31vfSXtZU2+9l4D+EzfrfkY/6CT4mApXXVhV3K/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11069}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "volta": {"node": "20.15.0", "yarn": "4.3.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "gitHead": "c1b0f751f597d9dac5d10870f49f30b8b1dc4908", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && tsup", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.3.1", "devDependencies": {"tsup": "^8.1.0", "eslint": "^8.57.0", "vitest": "^1.6.0", "typedoc": "^0.26.2", "prettier": "^3.3.2", "typescript": "^5.5.2", "lint-staged": "^15.2.7", "@commitlint/cli": "^19.3.0", "@sapphire/ts-config": "^5.0.1", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^1.6.0", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "@sapphire/eslint-config": "^5.0.5", "typedoc-plugin-mdn-links": "^3.2.1", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^7.14.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^19.2.2", "@typescript-eslint/eslint-plugin": "^7.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.4.0-next.c1b0f75.0_1719516812920_0.34049877504226855", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.4.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.4.0", "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "18a178010953052265ff2fb584f7e3d41abfca73", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.4.0.tgz", "fileCount": 13, "integrity": "sha512-eNb/9DMwNvhhgn1UuQ8Rl90jhj9PBkYH4oQ522TkiWUVWRfbh3PjdOTFkVGNKs5+xUXalkgFrUSwtY8u0g0S4g==", "signatures": [{"sig": "MEUCIEUkuHF3M7SpUAqWAgOcffXrd0pkE94jXUXJiHBTtKTDAiEA9YnNOSW+tKO0Gk/3ei8DiWCeW/ycwKWq0yPqFHmsvfc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 218608}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "volta": {"node": "20.15.0", "yarn": "4.3.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "gitHead": "2f46e1a11ff8dffad20b42f1e9bc483a8a07f0de", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && tsup", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.3.1", "devDependencies": {"tsup": "^8.1.0", "eslint": "^8.57.0", "vitest": "^1.6.0", "typedoc": "^0.26.2", "prettier": "^3.3.2", "typescript": "^5.5.2", "lint-staged": "^15.2.7", "@commitlint/cli": "^19.3.0", "@sapphire/ts-config": "^5.0.1", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^1.6.0", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "@sapphire/eslint-config": "^5.0.5", "typedoc-plugin-mdn-links": "^3.2.1", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^7.14.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^19.2.2", "@typescript-eslint/eslint-plugin": "^7.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.4.0_1719516838685_0.7289785580081221", "host": "s3://npm-registry-packages"}}, "2.4.1-next.2f46e1a.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.4.1-next.2f46e1a.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.4.1-next.2f46e1a.0", "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "6d54963453b0a2a5eda6fea2319fa3841530bd2b", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.4.1-next.2f46e1a.0.tgz", "fileCount": 5, "integrity": "sha512-/HKKe9yZ8QT+nHtIYOfv+xtiS9IMSV7QtATiH+I+iJQQxrVfTh0m16wwmlL7gg7yKYVysPEKBiikYb452AX/PQ==", "signatures": [{"sig": "MEUCIQDiR6phf2byRjdFoJ9I8HWlo3DLoYF1njIQ98xzHOZXvgIgAL65nPxAYRCMT1JB6IPNNMcLiyXzUxUSh4gHEJLbSBc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11347}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "volta": {"node": "20.15.0", "yarn": "4.3.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "gitHead": "2f46e1a11ff8dffad20b42f1e9bc483a8a07f0de", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && tsup", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.3.1", "devDependencies": {"tsup": "^8.1.0", "eslint": "^8.57.0", "vitest": "^1.6.0", "typedoc": "^0.26.2", "prettier": "^3.3.2", "typescript": "^5.5.2", "lint-staged": "^15.2.7", "@commitlint/cli": "^19.3.0", "@sapphire/ts-config": "^5.0.1", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^1.6.0", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "@sapphire/eslint-config": "^5.0.5", "typedoc-plugin-mdn-links": "^3.2.1", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^7.14.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^19.2.2", "@typescript-eslint/eslint-plugin": "^7.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.4.1-next.2f46e1a.0_1719516845932_0.6136185483327685", "host": "s3://npm-registry-packages"}}, "2.4.1-next.75c7e19.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.4.1-next.75c7e19.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.4.1-next.75c7e19.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "bbc3a7ce2b2b80e2f7121acca944438f1096fd89", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.4.1-next.75c7e19.0.tgz", "fileCount": 5, "integrity": "sha512-erNuEQQvIh5UVqjX03s0r1CNn14ETs+j7yyZTPDM5qnxQBHlzHtd8cgaGkh112v28kDSeSKJjlaLAi3suimflw==", "signatures": [{"sig": "MEQCIGW1HIwC6KrhMa3AQWrX1odgF652og4dSlVGV2i2fZu0AiAlTGp0orcSe93XCyvLCArPPbsKMbLmBbHwO3S2trpJZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11347}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "volta": {"node": "20.15.0", "yarn": "4.3.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "gitHead": "75c7e191ffa65d2cfccf2dda4e4395427fb979f6", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && tsup", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.3.1", "devDependencies": {"tsup": "^8.1.0", "eslint": "^8.57.0", "vitest": "^1.6.0", "typedoc": "^0.26.2", "prettier": "^3.3.2", "typescript": "^5.5.2", "lint-staged": "^15.2.7", "@commitlint/cli": "^19.3.0", "@sapphire/ts-config": "^5.0.1", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^1.6.0", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "@sapphire/eslint-config": "^5.0.5", "typedoc-plugin-mdn-links": "^3.2.1", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^7.14.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^19.2.2", "@typescript-eslint/eslint-plugin": "^7.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.4.1-next.75c7e19.0_1719969381375_0.27010412838732556", "host": "s3://npm-registry-packages"}}, "2.4.1": {"name": "@vladfrangu/async_event_emitter", "version": "2.4.1", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.4.1", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "473ac36b0ab2f76897510db71e3a5c3490eb584e", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.4.1.tgz", "fileCount": 13, "integrity": "sha512-cedU1DrzO4oliUigSAOqSgts6wEfGGSbpO1hYxvKbz8sr7a0meyP3GxnL6hIUtBK0nMG6zHfIYWcqOIb+MRI7w==", "signatures": [{"sig": "MEQCIH+TCIVREEoM99jsFaU1yROQ5KUEasPqwwU19bK1TltIAiBMqURJYFcCL7/b2ZQ9xTt258rG0lgIGyUZDvLYk/xWWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 218904}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "volta": {"node": "20.15.0", "yarn": "4.3.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "gitHead": "ce4e323fa74f04374d032435c56c2d72d450c97a", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && tsup", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.3.1", "devDependencies": {"tsup": "^8.1.0", "eslint": "^8.57.0", "vitest": "^1.6.0", "typedoc": "^0.26.2", "prettier": "^3.3.2", "typescript": "^5.5.2", "lint-staged": "^15.2.7", "@commitlint/cli": "^19.3.0", "@sapphire/ts-config": "^5.0.1", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^1.6.0", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "@sapphire/eslint-config": "^5.0.5", "typedoc-plugin-mdn-links": "^3.2.1", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^7.14.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^19.2.2", "@typescript-eslint/eslint-plugin": "^7.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.4.1_1719969390031_0.0808903138960757", "host": "s3://npm-registry-packages"}}, "2.4.2-next.ce4e323.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.4.2-next.ce4e323.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.4.2-next.ce4e323.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "18ae6225ce1d7f1b6b79c9d2aad0ef3aa6beb60c", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.4.2-next.ce4e323.0.tgz", "fileCount": 5, "integrity": "sha512-U1IYX0Nn9WYbVgS7cfKkSz71AFkHbCWOqz9MOeMJI6aOuhsnuyE3mUFrvBBj3dOjEbLsBlBQqjx72x8fqEHJtg==", "signatures": [{"sig": "MEQCIHBGDcRSmpDviXvwwk6iM8ZJaFMZlBw7zhLXtMRcJQNsAiAGQToh6jjhgai/b2fCwQRuS87sX4V6BaOEcu2qcvS4nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11643}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "volta": {"node": "20.15.0", "yarn": "4.3.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "gitHead": "ce4e323fa74f04374d032435c56c2d72d450c97a", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && tsup", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.3.1", "devDependencies": {"tsup": "^8.1.0", "eslint": "^8.57.0", "vitest": "^1.6.0", "typedoc": "^0.26.2", "prettier": "^3.3.2", "typescript": "^5.5.2", "lint-staged": "^15.2.7", "@commitlint/cli": "^19.3.0", "@sapphire/ts-config": "^5.0.1", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^1.6.0", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "@sapphire/eslint-config": "^5.0.5", "typedoc-plugin-mdn-links": "^3.2.1", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^7.14.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^19.2.2", "@typescript-eslint/eslint-plugin": "^7.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.4.2-next.ce4e323.0_1719969396768_0.7096448925083063", "host": "s3://npm-registry-packages"}}, "2.4.2": {"name": "@vladfrangu/async_event_emitter", "version": "2.4.2", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.4.2", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "6c52f27bce60751f979c1b87ae53521f77b7b628", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.4.2.tgz", "fileCount": 15, "integrity": "sha512-RFFVuRluNt1ZVyQYyMwPzla5zqO5LloGV28JIJe/ETOXU+kI4qg2hSc7/6AaqewdhEeFPQB02b+yzxWbFIDEbg==", "signatures": [{"sig": "MEUCIQCQp6LFaCP1ytTD1VNwQIm51BYL7LsnKVaZfcNLcw+6xwIgXuJuRpBSk0uSpnkEzIX8P435ruDwZh5JiLjSVyb37N4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 233524}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "volta": {"node": "20.15.0", "yarn": "4.3.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "gitHead": "eeb48494a534fc97ddbf835c13953277f6d1f9b9", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && yarn clean && tsup && tsc -p src --emitDeclarationOnly && tsx scripts/post-build.mts", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.3.1", "devDependencies": {"tsx": "^4.16.2", "tsup": "^8.1.0", "eslint": "^8.57.0", "vitest": "^1.6.0", "typedoc": "^0.26.2", "prettier": "^3.3.2", "typescript": "^5.5.2", "@types/node": "^20.14.10", "lint-staged": "^15.2.7", "@commitlint/cli": "^19.3.0", "@sapphire/ts-config": "^5.0.1", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^1.6.0", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "@sapphire/eslint-config": "^5.0.5", "typedoc-plugin-mdn-links": "^3.2.1", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^7.14.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^19.2.2", "@typescript-eslint/eslint-plugin": "^7.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.4.2_1720526376508_0.17672016151529268", "host": "s3://npm-registry-packages"}}, "2.4.3-next.eeb4849.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.4.3-next.eeb4849.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.4.3-next.eeb4849.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "abd6a1f2a58fd347c355aa781fe64f9ef6662f95", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.4.3-next.eeb4849.0.tgz", "fileCount": 5, "integrity": "sha512-Re<PERSON><PERSON>pyagoQR2zzjRDA8IEEXje8J/w3lDpMTDyTc3zyfRwucdMWcYSEkaq0Xj8P4A71QeCnaUamZWWHlEOSjSMw==", "signatures": [{"sig": "MEYCIQC3eHENC2AAXdxItQXu7DbjIy0cPkR8bN3VPXDOYioonQIhAK/wNGAYo7NlXXdAM3VmCc+eTfk1N4aayhjC+sjvVjY0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12060}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "volta": {"node": "20.15.0", "yarn": "4.3.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "gitHead": "eeb48494a534fc97ddbf835c13953277f6d1f9b9", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && yarn clean && tsup && tsc -p src --emitDeclarationOnly && tsx scripts/post-build.mts", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.3.1", "devDependencies": {"tsx": "^4.16.2", "tsup": "^8.1.0", "eslint": "^8.57.0", "vitest": "^1.6.0", "typedoc": "^0.26.2", "prettier": "^3.3.2", "typescript": "^5.5.2", "@types/node": "^20.14.10", "lint-staged": "^15.2.7", "@commitlint/cli": "^19.3.0", "@sapphire/ts-config": "^5.0.1", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^1.6.0", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "@sapphire/eslint-config": "^5.0.5", "typedoc-plugin-mdn-links": "^3.2.1", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^7.14.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^19.2.2", "@typescript-eslint/eslint-plugin": "^7.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.4.3-next.eeb4849.0_1720526383755_0.5177431514949844", "host": "s3://npm-registry-packages"}}, "2.4.3": {"name": "@vladfrangu/async_event_emitter", "version": "2.4.3", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.4.3", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "74ad582db6aecb19d108ea4731ab37bc41742cb4", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.4.3.tgz", "fileCount": 13, "integrity": "sha512-wn15EJHUk2RDtCw6wVJndUhFbfJYwyNhSD9s+yiQi5c2MmXDy3KLWvZ5LrcOzt3CHdscoAnaJrxIRKBI1QQgGw==", "signatures": [{"sig": "MEQCIB85NH0rDmjXe0b9lEtKdmaWMWnEhLtnvPsno+9N3MpGAiABMbSW/CDTfP39DWL6CERj5Jlc6jBQxi4X++ZUiKKf9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 219887}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "volta": {"node": "20.15.0", "yarn": "4.3.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "gitHead": "74d2d0950cae4f2a9bf1c6b2afca0bd164b32d4e", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && yarn clean && tsup", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "yarn build", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.3.1", "devDependencies": {"tsup": "^8.1.0", "eslint": "^8.57.0", "vitest": "^1.6.0", "typedoc": "^0.26.2", "prettier": "^3.3.2", "typescript": "^5.5.2", "lint-staged": "^15.2.7", "@commitlint/cli": "^19.3.0", "@sapphire/ts-config": "^5.0.1", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^1.6.0", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "@sapphire/eslint-config": "^5.0.5", "typedoc-plugin-mdn-links": "^3.2.1", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^7.14.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^19.2.2", "@typescript-eslint/eslint-plugin": "^7.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.4.3_1720527247725_0.9221073123587951", "host": "s3://npm-registry-packages"}}, "2.4.4-next.74d2d09.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.4.4-next.74d2d09.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.4.4-next.74d2d09.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "8d2fc306ce802083bb73298851a195cade6b8dec", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.4.4-next.74d2d09.0.tgz", "fileCount": 13, "integrity": "sha512-ViBIF1QUb2PQaox+WOjVJ6a0iiFJ6VZK0cUoI7Hy3xwA5ega98movp0AHoYCkzEGLZWcNliy/vKqJUpWmWOM9Q==", "signatures": [{"sig": "MEUCIB5EOBMCd0ABygG3WsI1wUCjLL222tVjbyk1IxqkoxZBAiEA0YXCV/X132KAtDsuy5HgfKsdCqGuxH3FwMc//Kesr28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 219902}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "volta": {"node": "20.15.0", "yarn": "4.3.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "gitHead": "74d2d0950cae4f2a9bf1c6b2afca0bd164b32d4e", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && yarn clean && tsup", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "yarn build", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.3.1", "devDependencies": {"tsup": "^8.1.0", "eslint": "^8.57.0", "vitest": "^1.6.0", "typedoc": "^0.26.2", "prettier": "^3.3.2", "typescript": "^5.5.2", "lint-staged": "^15.2.7", "@commitlint/cli": "^19.3.0", "@sapphire/ts-config": "^5.0.1", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^1.6.0", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "@sapphire/eslint-config": "^5.0.5", "typedoc-plugin-mdn-links": "^3.2.1", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^7.14.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^19.2.2", "@typescript-eslint/eslint-plugin": "^7.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.4.4-next.74d2d09.0_1720527264912_0.6485900941959664", "host": "s3://npm-registry-packages"}}, "2.4.5-next.4d3e73d.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.4.5-next.4d3e73d.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.4.5-next.4d3e73d.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "2eb9deded7870953e9035ee539d86e52cb21a597", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.4.5-next.4d3e73d.0.tgz", "fileCount": 13, "integrity": "sha512-foKt2fweTYGmCrFfxf2+5DPhcTLTkWMh02ewfQFAJoHCrk6IBifJQunpHsRAFOydC6OSzbGhMPBqXvfFHfmv5A==", "signatures": [{"sig": "MEUCIQD081PKibxoSrqdYC0pPY8gGX0hZb2pz/9BTDnAmPVjHAIgcYCnZBDhdE4IyeUZ9PvjX8kH2Enh/elC39RkhRxM7og=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 220241}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "volta": {"node": "20.15.0", "yarn": "4.3.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "gitHead": "4d3e73d6faf4b2c4dcd948bcd77058587994b6e8", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && yarn clean && tsup", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "yarn build", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.3.1", "devDependencies": {"tsup": "^8.1.0", "eslint": "^8.57.0", "vitest": "^1.6.0", "typedoc": "^0.26.2", "prettier": "^3.3.2", "typescript": "^5.5.2", "lint-staged": "^15.2.7", "@commitlint/cli": "^19.3.0", "@sapphire/ts-config": "^5.0.1", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^1.6.0", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "@sapphire/eslint-config": "^5.0.5", "typedoc-plugin-mdn-links": "^3.2.1", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^7.14.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^19.2.2", "@typescript-eslint/eslint-plugin": "^7.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.4.5-next.4d3e73d.0_1721078895628_0.19816627629496852", "host": "s3://npm-registry-packages"}}, "2.4.4": {"name": "@vladfrangu/async_event_emitter", "version": "2.4.4", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.4.4", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "0d358c48170a391c8607ec3a1d4dc4a49ab108aa", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.4.4.tgz", "fileCount": 13, "integrity": "sha512-ZL62PFXEIeGUI8btfJ5S8Flc286eU1ZUSjwyFQtIGXfRUDPZKO+CDJMYb1R71LjGWRZ4n202O+a6FGjsgTw58g==", "signatures": [{"sig": "MEQCIDAvBrNb+2rOF+t/9c6a8lTTrxWB5jWYClFwMiK4St8HAiAuie9Evt3I8BRa+mYxm1t1zd5teurKqneqF/CSaQ1yZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 220226}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "volta": {"node": "20.15.0", "yarn": "4.3.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "gitHead": "4d3e73d6faf4b2c4dcd948bcd77058587994b6e8", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && yarn clean && tsup", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "yarn build", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.3.1", "devDependencies": {"tsup": "^8.1.0", "eslint": "^8.57.0", "vitest": "^1.6.0", "typedoc": "^0.26.2", "prettier": "^3.3.2", "typescript": "^5.5.2", "lint-staged": "^15.2.7", "@commitlint/cli": "^19.3.0", "@sapphire/ts-config": "^5.0.1", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^1.6.0", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "@sapphire/eslint-config": "^5.0.5", "typedoc-plugin-mdn-links": "^3.2.1", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^7.14.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^19.2.2", "@typescript-eslint/eslint-plugin": "^7.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.4.4_1721078901339_0.2600108367266296", "host": "s3://npm-registry-packages"}}, "2.4.5": {"name": "@vladfrangu/async_event_emitter", "version": "2.4.5", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.4.5", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "7bc35026fdc3398a5e1aac801edd21b28cdf4cfa", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.4.5.tgz", "fileCount": 13, "integrity": "sha512-J7T3gUr3Wz0l7Ni1f9upgBZ7+J22/Q1B7dl0X6fG+fTsD+H+31DIosMHj4Um1dWQwqbcQ3oQf+YS2foYkDc9cQ==", "signatures": [{"sig": "MEUCIArEAVkWguHNE1OOdHHm12TtGSLqT8lZuScHmg+UpxjRAiEA0giWyVqfs8fiYjxEgLZ32NN8xuIO56EK5E5WS2yd+E4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 222143}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "volta": {"node": "20.15.0", "yarn": "4.3.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "gitHead": "154c197b2e7aa89042d655868c67d1eb2ba1275b", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && yarn clean && tsup", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "yarn build", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.3.1", "devDependencies": {"tsup": "^8.1.0", "eslint": "^8.57.0", "vitest": "^1.6.0", "typedoc": "^0.26.2", "prettier": "^3.3.2", "typescript": "^5.5.2", "lint-staged": "^15.2.7", "@commitlint/cli": "^19.3.0", "@sapphire/ts-config": "^5.0.1", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^1.6.0", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "@sapphire/eslint-config": "^5.0.5", "typedoc-plugin-mdn-links": "^3.2.1", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^7.14.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^19.2.2", "@typescript-eslint/eslint-plugin": "^7.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.4.5_1722497246295_0.30360781298970263", "host": "s3://npm-registry-packages"}}, "2.4.6-next.154c197.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.4.6-next.154c197.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.4.6-next.154c197.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "f37ac0ab8f40c2f29df3f40a92919e9a64cdf7bb", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.4.6-next.154c197.0.tgz", "fileCount": 13, "integrity": "sha512-1Ype0Jy57dgNvEj6LI2CAYTJUeZbg9uOwfmDhWTamfFs1Fm6ncYIdFmT3+6fBNq8eaD1dusiw6/q1waV1//49g==", "signatures": [{"sig": "MEQCIHUG/CKfhkK5wdt6tNl+Qf5VwqfS9wkoZusSDRSOh0IYAiBTlE+fAC7JzRUsu64QSp5QMkso/KW7qFGbBbcDgOufZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 222158}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "volta": {"node": "20.15.0", "yarn": "4.3.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "gitHead": "154c197b2e7aa89042d655868c67d1eb2ba1275b", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && yarn clean && tsup", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "yarn build", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.3.1", "devDependencies": {"tsup": "^8.1.0", "eslint": "^8.57.0", "vitest": "^1.6.0", "typedoc": "^0.26.2", "prettier": "^3.3.2", "typescript": "^5.5.2", "lint-staged": "^15.2.7", "@commitlint/cli": "^19.3.0", "@sapphire/ts-config": "^5.0.1", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^1.6.0", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "@sapphire/eslint-config": "^5.0.5", "typedoc-plugin-mdn-links": "^3.2.1", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^7.14.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^19.2.2", "@typescript-eslint/eslint-plugin": "^7.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.4.6-next.154c197.0_1722497258744_0.12738183350482402", "host": "s3://npm-registry-packages"}}, "2.4.6-next.77ad774.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.4.6-next.77ad774.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.4.6-next.77ad774.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "9350c4efb90bde706749fabea539b4f401b4a85d", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.4.6-next.77ad774.0.tgz", "fileCount": 13, "integrity": "sha512-nTt3wFVNnau60G1UD4ALRTaNBXRu1y6qoV6Y2fbm86ZHWiYoQo7zYf1hPZNT9EYqIdrWHtDTuWf12RAeg2jkng==", "signatures": [{"sig": "MEUCIQCMCIKrOytS7CXs/h0sjKBD9l0NCaONa+bAWLvYW0Q7UwIgYNirbxf1NWkOAYHloWZsmbqfOzp+Hn7RPTmlJLiOr+k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 220386}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "volta": {"node": "20.15.0", "yarn": "4.3.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "gitHead": "77ad7741f75eeb499d217d7e93ce3fb5aab483ea", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && yarn clean && tsup", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "yarn build", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.3.1", "devDependencies": {"tsup": "^8.1.0", "eslint": "^8.57.0", "vitest": "^1.6.0", "typedoc": "^0.26.2", "prettier": "^3.3.2", "typescript": "^5.5.2", "lint-staged": "^15.2.7", "@commitlint/cli": "^19.3.0", "@sapphire/ts-config": "^5.0.1", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^1.6.0", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "@sapphire/eslint-config": "^5.0.5", "typedoc-plugin-mdn-links": "^3.2.1", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^7.14.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^19.2.2", "@typescript-eslint/eslint-plugin": "^7.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.4.6-next.77ad774.0_1724277257801_0.6826879263966088", "host": "s3://npm-registry-packages"}}, "2.4.7-next.676750d.0": {"name": "@vladfrangu/async_event_emitter", "version": "2.4.7-next.676750d.0", "keywords": ["event emitter", "async", "event", "emitter"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@vladfrangu/async_event_emitter@2.4.7-next.676750d.0", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "dist": {"shasum": "d9efe6ce0988a40d3cbf8e88bfe3f8e69d7c929d", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.4.7-next.676750d.0.tgz", "fileCount": 13, "integrity": "sha512-Aq30bByAfW21R5kechxectKvrux1fzDRf7F1HZwcQTdYw7Q6SR2yiPYn6R6+NPQP7cqjuebPH5DeTW2mXiMMLA==", "signatures": [{"sig": "MEQCICH6DFCYB8oPRZbO9Argk+8Ii+2uYOu/yKO1Npdho60DAiBfzNSz8+w4EVClwC10W3M2cJIH2bjty40I4gCFShr5rw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 220664}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "unpkg": "./dist/index.global.js", "volta": {"node": "20.15.0", "yarn": "4.3.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "engines": {"npm": ">=7.0.0", "node": ">=v14.0.0"}, "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "gitHead": "676750d940680f829328993dc2eda3f7daeeb2cc", "scripts": {"bump": "cliff-jumper", "docs": "typedoc", "lint": "eslint src tests --ext ts --fix", "test": "vitest run", "build": "tsc -p src --noEmit && yarn clean && tsup", "clean": "node scripts/clean.mjs", "format": "prettier --write \"{src,tests}/**/*.ts\"", "update": "yarn upgrade-interactive", "prepack": "yarn build", "typecheck": "tsc -p src --noEmit", "test:watch": "vitest", "check-update": "cliff-jumper --dry-run"}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "repository": {"url": "git+https://github.com/vladfrangu/async_event_emitter.git", "type": "git"}, "description": "An event emitter implementation with async support in mind", "directories": {}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "sideEffects": false, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "packageManager": "yarn@4.3.1", "devDependencies": {"tsup": "^8.1.0", "eslint": "^8.57.0", "vitest": "^1.6.0", "typedoc": "^0.26.2", "prettier": "^3.3.2", "typescript": "^5.5.2", "lint-staged": "^15.2.7", "@commitlint/cli": "^19.3.0", "@sapphire/ts-config": "^5.0.1", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^1.6.0", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "@sapphire/eslint-config": "^5.0.5", "typedoc-plugin-mdn-links": "^3.2.1", "@sapphire/prettier-config": "^2.0.0", "@typescript-eslint/parser": "^7.14.1", "cz-conventional-changelog": "^3.3.0", "@commitlint/config-conventional": "^19.2.2", "@typescript-eslint/eslint-plugin": "^7.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/async_event_emitter_2.4.7-next.676750d.0_1724277302533_0.9070960563487029", "host": "s3://npm-registry-packages"}}, "2.4.6": {"name": "@vladfrangu/async_event_emitter", "version": "2.4.6", "description": "An event emitter implementation with async support in mind", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "browser": "./dist/index.global.js", "unpkg": "./dist/index.global.js", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "default": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}}}, "sideEffects": false, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"lint": "eslint src tests --ext ts --fix", "format": "prettier --write \"{src,tests}/**/*.ts\"", "docs": "typedoc", "test": "vitest run", "test:watch": "vitest", "update": "yarn upgrade-interactive", "prepack": "yarn build", "build": "tsc -p src --noEmit && yarn clean && tsup", "clean": "node scripts/clean.mjs", "typecheck": "tsc -p src --noEmit", "bump": "cliff-jumper", "check-update": "cliff-jumper --dry-run"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@favware/cliff-jumper": "^2.2.3", "@favware/npm-deprecate": "^1.0.7", "@sapphire/eslint-config": "^5.0.5", "@sapphire/prettier-config": "^2.0.0", "@sapphire/ts-config": "^5.0.1", "@typescript-eslint/eslint-plugin": "^7.14.1", "@typescript-eslint/parser": "^7.14.1", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^1.6.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "lint-staged": "^15.2.7", "prettier": "^3.3.2", "tsup": "^8.1.0", "typedoc": "^0.26.2", "typedoc-plugin-mdn-links": "^3.2.1", "typescript": "^5.5.2", "vitest": "^1.6.0"}, "repository": {"type": "git", "url": "git+https://github.com/vladfrangu/async_event_emitter.git"}, "engines": {"node": ">=v14.0.0", "npm": ">=7.0.0"}, "keywords": ["event emitter", "async", "event", "emitter"], "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "lint-staged": {"*": "prettier --ignore-unknown --write", "*.{mjs,js,ts}": "eslint --fix --ext mjs,js,ts"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "publishConfig": {"access": "public"}, "packageManager": "yarn@4.3.1", "volta": {"node": "20.15.0", "yarn": "4.3.1"}, "_id": "@vladfrangu/async_event_emitter@2.4.6", "gitHead": "676750d940680f829328993dc2eda3f7daeeb2cc", "dist": {"shasum": "508b6c45b03f917112a9008180b308ba0e4d1805", "integrity": "sha512-Ra<PERSON>5qZo6D2CVS6sTHFKg1v5Ohq/+Bo2LZ5gzUEwZ/WkHhwtGTCB/sVLw8ijOkAUxasZ+WshN/Rzj4ywsABJ5ZA==", "tarball": "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.4.6.tgz", "fileCount": 13, "unpackedSize": 220649, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDB/QMaqcrL9EdkqgcpjA5cPAUjqAQogjjnkmpBkPk/vgIhANlsbDdm9YXEA3LjoCa4hMOMtp/sMcGQ+PcSml52rnsX"}]}, "_npmUser": {"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/async_event_emitter_2.4.6_1724277306088_0.8705285589489928"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-06-23T15:07:59.461Z", "modified": "2024-08-21T21:55:06.411Z", "0.0.2-next.492f589.0": "2022-06-23T15:07:59.743Z", "0.0.2-next.889dac7.0": "2022-06-29T16:04:09.748Z", "1.0.1": "2022-06-29T16:04:48.203Z", "1.0.2-next.e19c8c4.0": "2022-06-29T16:05:42.080Z", "1.0.2-next.d052952.0": "2022-06-29T16:09:09.623Z", "2.0.0": "2022-06-29T16:56:25.027Z", "2.0.0-next.8c69419.0": "2022-06-29T16:57:02.793Z", "2.0.1-next.af0da83.0": "2022-06-29T16:57:41.225Z", "2.0.1-next.08ebbc4.0": "2022-07-03T13:45:51.482Z", "2.0.1-next.b849b38.0": "2022-07-09T19:44:03.943Z", "2.0.1": "2022-07-09T19:44:05.819Z", "2.0.2-next.b15991e.0": "2022-07-09T19:44:18.814Z", "2.1.0": "2022-09-18T20:22:32.219Z", "2.1.0-next.5a14ed0.0": "2022-09-18T20:23:48.013Z", "2.1.1-next.300c5b6.0": "2022-09-18T20:23:59.926Z", "2.1.1": "2022-09-19T14:38:48.282Z", "2.1.2-next.ecbb444.0": "2022-09-19T14:39:14.223Z", "2.1.2": "2022-09-19T14:41:43.808Z", "2.1.3-next.7c3e9f1.0": "2022-09-19T14:42:07.487Z", "2.1.3": "2023-01-09T23:09:03.605Z", "2.1.3-next.1533208.0": "2023-01-09T23:10:08.493Z", "2.1.4-next.552c09d.0": "2023-01-09T23:10:39.451Z", "2.1.4": "2023-02-18T16:31:26.876Z", "2.1.4-next.4224bbe.0": "2023-02-18T16:32:33.150Z", "2.1.5-next.9229727.0": "2023-02-18T16:32:44.865Z", "2.2.0": "2023-04-07T22:44:23.428Z", "2.2.0-next.23eb908.0": "2023-04-07T22:45:37.103Z", "2.2.1-next.17379f5.0": "2023-04-07T22:46:01.066Z", "2.2.1": "2023-04-08T14:07:25.841Z", "2.2.2-next.0f2e9a9.0": "2023-04-08T14:08:01.366Z", "2.2.2": "2023-05-11T06:26:10.066Z", "2.2.3-next.73d1465.0": "2023-05-11T06:27:04.023Z", "2.2.3-next.6cce45a.0": "2023-12-03T02:02:51.660Z", "2.2.3": "2023-12-03T02:03:06.849Z", "2.2.4-next.9a42f92.0": "2023-12-03T02:03:21.283Z", "2.2.4-next.5ca1cb7.0": "2023-12-03T02:04:51.460Z", "2.2.4-next.6235ff3.0": "2023-12-03T02:07:07.568Z", "2.2.4-next.91d4d86.0": "2023-12-03T02:10:19.999Z", "2.2.4-next.8dc308c.0": "2023-12-03T09:06:14.068Z", "2.2.4": "2023-12-03T09:06:41.197Z", "2.2.5-next.b6839ad.0": "2023-12-03T09:06:48.418Z", "2.3.0-next.0f4029e.0": "2024-06-21T20:32:27.944Z", "2.3.1-next.a50001c.0": "2024-06-21T20:32:44.991Z", "2.3.0": "2024-06-21T20:32:46.630Z", "2.4.0-next.c1b0f75.0": "2024-06-27T19:33:33.060Z", "2.4.0": "2024-06-27T19:33:58.862Z", "2.4.1-next.2f46e1a.0": "2024-06-27T19:34:06.088Z", "2.4.1-next.75c7e19.0": "2024-07-03T01:16:21.530Z", "2.4.1": "2024-07-03T01:16:30.234Z", "2.4.2-next.ce4e323.0": "2024-07-03T01:16:36.908Z", "2.4.2": "2024-07-09T11:59:36.635Z", "2.4.3-next.eeb4849.0": "2024-07-09T11:59:43.912Z", "2.4.3": "2024-07-09T12:14:07.901Z", "2.4.4-next.74d2d09.0": "2024-07-09T12:14:25.097Z", "2.4.5-next.4d3e73d.0": "2024-07-15T21:28:15.816Z", "2.4.4": "2024-07-15T21:28:21.523Z", "2.4.5": "2024-08-01T07:27:26.487Z", "2.4.6-next.154c197.0": "2024-08-01T07:27:38.930Z", "2.4.6-next.77ad774.0": "2024-08-21T21:54:18.011Z", "2.4.7-next.676750d.0": "2024-08-21T21:55:02.683Z", "2.4.6": "2024-08-21T21:55:06.225Z"}, "bugs": {"url": "https://github.com/vladfrangu/async_event_emitter/issues"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "keywords": ["event emitter", "async", "event", "emitter"], "repository": {"type": "git", "url": "git+https://github.com/vladfrangu/async_event_emitter.git"}, "description": "An event emitter implementation with async support in mind", "maintainers": [{"name": "vlad<PERSON>ng<PERSON>", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}