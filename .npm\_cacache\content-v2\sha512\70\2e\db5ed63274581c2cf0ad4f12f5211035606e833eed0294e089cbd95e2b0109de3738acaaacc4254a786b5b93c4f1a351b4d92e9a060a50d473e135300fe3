{"_id": "@babel/plugin-syntax-import-meta", "_rev": "56-63e9cd7ee905163a2e05c566a842c0e7", "name": "@babel/plugin-syntax-import-meta", "description": "Allow parsing of import.meta", "dist-tags": {"latest": "7.10.4"}, "versions": {"7.0.0-beta.36": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-beta.36", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-beta.36", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-wl/6rUeViE1T+c6Zc05pbavniXO7djr1TMo9wz5AbSZxNOHTwGWTVdt46lz3seyMmAQ526ijqp7PS4mIUaIgHQ==", "shasum": "21b766be17e2ae4f9fd6040f5b6dc7431c1cb2f9", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-beta.36.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDKrSGJA8JRxCSIJRx5TOCdd/ucQ4pIONn03HjbJVB52wIgd2O12J721l4AjSTVTq63e7ErrVI9Si3zjyiwDAUozpE="}]}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta-7.0.0-beta.36.tgz_1514228662881_0.9299045125953853"}, "directories": {}}, "7.0.0-beta.37": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-beta.37", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-beta.37", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-dqOX5Ll2TTuwx3Re8NUp8nPJwcsh9QlFbVAChj1hpSIPbmO4egWpNyk8JOIhOdxxsiQxmU5UInx5D8AMyvQ+wg==", "shasum": "3ccd614cc8d49a324298cd26f7f2f2f7f1651949", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-beta.37.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBI9XZROjKPRyA7Fnu8k93SlEbzVMr7Nncd+AHmH4YkQAiEA1719y6m/ywvsFgxeqT35VRuUKGDcYqamNldQEKitorw="}]}, "maintainers": [{"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta-7.0.0-beta.37.tgz_1515427342384_0.08263004897162318"}, "directories": {}}, "7.0.0-beta.38": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-beta.38", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-beta.38", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-F1PAWpIy9Pa0ieIlX+qZYJRUMGcVNO3fVLWM4EihDMspiztQ+30ToaAWp9yXpbx0l5x6JTIcYaLiie9Nk1VrlQ==", "shasum": "0da0626a421cc21993326612d7bc92f25a3ab9cf", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-beta.38.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDJ3/uVq5Ej4wsDa7p04oUQZC5upnROdEYD5swHH27gFAIgS9MlK/sJW8TV/gZ5eAC+4cEM+dTbeJ1+FqmQlwUX+2g="}]}, "maintainers": [{"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta-7.0.0-beta.38.tgz_1516206702280_0.1369135002605617"}, "directories": {}}, "7.0.0-beta.39": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-beta.39", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-beta.39", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-BSNFp9v7fEA42/t6MaE80GcQnUuGvE+Zphz1gW47GXt+szTSdo3G+85djs6p5bQEXn8YDc18AnGmDOXHd3GGhw==", "shasum": "56658e7487227ca7c350b67031c0e16ce987558d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-beta.39.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDfyf35wZTClOyeQ7mPfh1AYLt/nIQHUVEQk4R7YIW9mAIgV4Uv/jZsNYrnlp2dzCaa7rshLc5dgIunlUurBPfR4u0="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta-7.0.0-beta.39.tgz_1517344046458_0.6706113799009472"}, "directories": {}}, "7.0.0-beta.40": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-beta.40", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "devDependencies": {"@babel/core": "7.0.0-beta.40"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-beta.40", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-yeFzflDuVYQoGEN6IhzYg0meX8/fBNh9QyeargRnb3iBzsMFR4jlANGzfoJiCznrCuxD5PwyN3ukQDvn23F2LQ==", "shasum": "40b2f7508d418fe624148e81030e2355b689f104", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-beta.40.tgz", "fileCount": 3, "unpackedSize": 1169, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC+Tb/FYFN4l8pa9wKSV8aZzyu/JywtOxGvsuKQxUppHAiA7GR3ypY4/GUGhwwTKaM6DI5nCpAejuoihwHjeoEDxYA=="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0-beta.40_1518453683709_0.5714888108553828"}, "_hasShrinkwrap": false}, "7.0.0-beta.41": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-beta.41", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "devDependencies": {"@babel/core": "7.0.0-beta.41"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-beta.41", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-REgvGndMFmeVqWa/FwSzlhq2wFEliqX45+TkoCCWfHNzTLTQ4kUSmML0FLSARh9GRc+QixX1uXTZX3gNyFQYig==", "shasum": "a225e7ae1806a1083e82b53fbcb7dd67338590cb", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-beta.41.tgz", "fileCount": 3, "unpackedSize": 1404, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzgm7HySZmntfCo4xJ5nixHnhdNnqARomA6Nbb7TVWVgIgam44+EDFsXKcdt4JU2OecosqZAXRefOE6406wvdAOZo="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0-beta.41_1521044754154_0.26497836556929455"}, "_hasShrinkwrap": false}, "7.0.0-beta.42": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-beta.42", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "devDependencies": {"@babel/core": "7.0.0-beta.42"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-beta.42", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-w31ILwzgUTpFw2mHddl85yuoiFJGXQqA6aqwZE18/1klWZJ79n9UrysmQPD64FQ90AxCYMO+HkGt/M6YHopFPQ==", "shasum": "bf3b41136b894c90e497c7d9cbfdd31de4f2ffd5", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-beta.42.tgz", "fileCount": 3, "unpackedSize": 1404, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHMU+di+IYzN4uTQC8RBEUyvijCloi854xBgCre0nTwZAiAP6gY2xvAZJdQeuP8tZ3PW326ZCDXVPfo8BDTj/haeXQ=="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0-beta.42_1521147030791_0.29497232866626266"}, "_hasShrinkwrap": false}, "7.0.0-beta.43": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-beta.43", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "devDependencies": {"@babel/core": "7.0.0-beta.43"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-beta.43", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-BukOjhOeJZPY+aEtlHRy2j6eH6QUr1qy9EFRlBB+qG8dMN9E/sKAefdmB+HXWhebKWR4jGAvTBD4hm3uU6Lhuw==", "shasum": "da0163089b4fa7cd05349d045797c3aa7ee1cd33", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-beta.43.tgz", "fileCount": 3, "unpackedSize": 1509, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDnGcUAPY0DvBe4PmOtDlqXmAtxQzgl0sQL0dDylV17LAIgXVivsLjqyxtN0WmTc55pvCPfIkwX29GjeSrizfcmFGo="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0-beta.43_1522687696246_0.17390785540813147"}, "_hasShrinkwrap": false}, "7.0.0-beta.44": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-beta.44", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "devDependencies": {"@babel/core": "7.0.0-beta.44"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-beta.44", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-hI/CI2f0eTdyiuM6OOqQEdYEm0RlzAduzBXCy0hERtBh3njc3ZhAM7L19sLbJafEIUAzno7tC0dzk/gv+GaM1A==", "shasum": "48d0af27aac06938542cc71abda014301af2dc13", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-beta.44.tgz", "fileCount": 3, "unpackedSize": 1560, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGnUaRWDSbvAkYJX0jeIGl1V5nX7KhE/IjZ4maBE8T17AiEAlEDFVq5Vt7tw9pCHugd/587pRVAT7aM5hs85Sj5rfzM="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0-beta.44_1522707597953_0.18683686628296448"}, "_hasShrinkwrap": false}, "7.0.0-beta.45": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-beta.45", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "devDependencies": {"@babel/core": "7.0.0-beta.45"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-beta.45", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-60euADy+aVvWRhWj7sty/gaC44ZpNzhj/AgXjrE3r6t4520kU4nnGp14DC8xsiReaO7E+1R2kaMa+PCSOq1mdQ==", "shasum": "9dd5be78fc1a665e9e4cb4f2f2aea0d0935091f9", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-beta.45.tgz", "fileCount": 3, "unpackedSize": 1560, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1ACRA9TVsSAnZWagAACRgP/jwI6EaPvJYCs3EP+m+F\nn8qsa66kV4jKvCb+/bFOdYC2ax2hvLKEHJqK/mIomiOd/D5gd/JNfAsuecWO\nm8O9xMYZtfMxGL86TXEsLtGlStAR2Hd2K2qeqHmL6OCgiffQXEcLWcD6LchC\nEDXmPeEQO9zk5XAYUfR0o5Rn1ge6wRHQZ/BMl24WzA+13y6xwKJJApvNBdPk\nMt66VAAgQAZu6JVgQgGuwESWB3kCD+EsJdvMtgwDQzx/8rnri85TD7urjKfl\nKB17PIjFLQonpchvnTVb857MZwQa9wwpOood8X5A+94UZnCr4/kGbPqSrlh4\nOqv1bHmBa73i6JF/3c99d4oBU2n8Sy2KNXT+iKX711BfJE+mCU7ILxALkif+\noRBt03ezqnB2CwPwLGTNe+ZeyjNVvwCu2x8XTqq9/TuuzsJ42CqdikH8LOCv\ni0mLlWpNtm0ks1MkRQrcA4A5rXF/fCfHcJjk1tsH7pPLlv0jDSgB0IcfYEU1\nh56jpD0sAZbizBoIZIkhyV4eMxnPABZEws4+4pRgmvD86jPjgXuwz3IwZdtl\nyn1lWqXK1pZwqqxLBww5V4IBFcAZXaNLV9nEtgGxupWvdkDlJI9Ah2zNyoIO\nNbE3IfZqeCAXOYiSg2w6/HtfCH9w8LCTwaqpNB2CjChg4qPx5rEz/JtfTih0\nkQLK\r\n=hM2B\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDEtj5OyGHuOu0n2+PdgMdX/OZmLYBWhO2/b2afQhHmYwIhAMv2DLGH0d/nlE8yURZl25HGTi5DYjpqFod5y4FNWbZi"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0-beta.45_1524448576301_0.9812278251387849"}, "_hasShrinkwrap": false}, "7.0.0-beta.46": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-beta.46", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "devDependencies": {"@babel/core": "7.0.0-beta.46"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-beta.46", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-/vOa4lDBN8CpJr9MwdavcZKwqX7ueul5KwSWrxhY6rwkpq0z50vlNoWWShzDMOmQ0VWV8B6T/mV3GEOofUzh/g==", "shasum": "b86f3ceb94b1744555b3c9271be51ca31b1aedf3", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-beta.46.tgz", "fileCount": 3, "unpackedSize": 1560, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WFzCRA9TVsSAnZWagAAoS4P/jMQsXM7Rk1l2ua1TMEl\nFhF25aXdDIFnWtBHW+14stX0g4WRSBtXUvpMTgBH3p5g5kMz32NIYBFp2MMp\n3bY0SkFH1pUQbBqkyVo34uVc+Tw7BttyVRAIQMz5rd1BZNcFJ46XvISEqOLs\nHBzAgG9xcWHUAVoCimLDzBuHDwhntjj+Brvm6SbW3180WdFwH172JPFXOifu\nMSdqmwQvfazvzUA4fzhfQKAUaILTRokdgElPJkhIMfxKbHVRoylJXdHGlLI3\n9GL4PhKxs6k+V+hrIIfxH9Pd5vq4JNP0kZG6b/bQqoL/iuStS+ZSLiKKNQrA\nyi/hAMio5rJKCLM6YT7Os+nfeZvrUhtlw9P+fBLVcnOV0Yss3sLnpv17VsFA\nakxfsV7/1dVnUB1iRXLUMio1OhRxLPS1R9Orbksprqx42okFZrDzN5Tj5r5Y\nPPQBbggvpX6cxkrr3DnP2M5wZyzMfPai/4qf1jEtb3MJ7JgqbjX7pxGMsyy7\nuBdejeHN51GByJ/vEECn4EtcncOHSCJBo72n3cnbdSeUs6sKwXFK3SSyDBl0\nlF8kIrfAq/jXZZ47wCYkS8ZV5X2WYtuMNhA3YZRcifUt+C/iYVnOWh7N8IoO\nji+1eI1G4rIu180fq/0nNqogbhHlu2huSPnfdIr4FyEefBDTaRB7NjLT/eph\nS3XR\r\n=PLGm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHlhSGUulTVTQTw/rh0ALVKc2a4LxJ012xwsonGhchrCAiEA22yDDppG8N3ks+oC37kF8a5VD2X0gNh635mRAdHt5qM="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0-beta.46_1524457842772_0.8495475961996592"}, "_hasShrinkwrap": false}, "7.0.0-beta.47": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-beta.47", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "devDependencies": {"@babel/core": "7.0.0-beta.47"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-beta.47", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ISVXJZw9Q2Q0pwcXNv3nfA1p9Ia0clDMTtYxlqIGE5/3WaIQoHkkeaOYsckBKiIIL0hfU+GEjL1g5hRBYPlMUA==", "shasum": "8ab5174209a954b91e327004a7d16737bcc4774d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-beta.47.tgz", "fileCount": 3, "unpackedSize": 1527, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iT3CRA9TVsSAnZWagAAD7gQAIsBejuRMdRYZQ0mNWV+\n9WzzHfGVB9C/u4FW4sqXg1+ehYaoUIpSUfODdgqi35/5WieEbm5T2dD6Z338\nUqvaVhssYLMFeEv7/Vd26V49pmyaeRZJvJTSa65r773cVNs/QngbRStn+A/7\nLLfAcfCK3MgE6aGi3Gmxl4HioiNM/tKG1knyg9mZXkUtQvbahY8IdynEYujP\n4HG2jlMaKhCUs36lyVdu8TghSRIL6hP+qxh1BK0UvQ07GbqEojHEqM2OhIdN\nt+g7kQylbCLNCk8w79F+Ucl9aq2iLmPwDnAPdHsy3LfgcrrDBu9/Ksl7dabU\nXBlV44ZrJxYXVVsmcZabbsSkuAC4tpttH+G3In2dwV+qPlSKg3CAQv+ujh3D\na9pdn21ox4FleMkLZfUEnLz2DywVx9F78HSEGEoNLS0xTqZcMEzQKqqsED9c\nPXH0eq67c+AIOtB1u0GDeYrPAULXNZ6EbE5HrgVdUvedamFWRcV/LhqBEtAO\nl1qXaEwq40XVqHhVLKvWDvQpRPDolLcohTLbE1KQ1BcbOyf38FwwdC7p+UnS\nw2ZxWiSQv7tDpXM0GhcVTS12Tpmv5UxL4cYgztZ7iTQ/7j9LsVDGb7PMpqWb\ndRUgOco/CvTXidphlLc8RrnEDQlxyOh/12T0TNjhoLQQMCHdoK/eQX51fSXI\n26jC\r\n=/Lm+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCs6dAxqFnli37otwNFfSImVQDN4+4jkU82q83fO4wKjQIhANSy16NHI/fvhU1dj8lzQvhz9XhB8WBY8oNoydlhVwV3"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0-beta.47_1526342903229_0.10914776379114732"}, "_hasShrinkwrap": false}, "7.0.0-beta.48": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-beta.48", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "devDependencies": {"@babel/core": "7.0.0-beta.48"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-beta.48", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-9klJYjXNZVtyErKRV6f6JxHiahITPUCOrwGgCV1aamk9QithAps1djLvx5wX6h5GUOBbzO64+SBBR1aQgxWFdw==", "shasum": "cbf0d011f61f3a0bdd603c65a3127c48b88ed946", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-beta.48.tgz", "fileCount": 3, "unpackedSize": 1509, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxDHCRA9TVsSAnZWagAAKeAP/jemPoQkNL1bHP8XE9iU\n/UYxSMU59nMvw1KdFOQaXtKMHv8r0uKkGdGhndoNcX3ykcY0iesZUERImPvP\n55uaPlvJXebkbeqhxsMtscnH3HO6KQuIILqWhKm9NY75HFGl3BdGsIXzSE7a\nbl8QOPmmx5i9nZtWqZEQhWuZBHtBowJC2K5gHBQTxIc6tStUUL6JrjKUHoNj\nUnJR1/e62q8lTQ9yUGMpkP63hP1hSfoF4XzzxaCqrQ1QZRCSW/m3uVAGEI5g\n0M4jVv/Iq9Y7XQkySCVUOng/fXjp0GRe3VJOZ9H/J3bx8QzK8KWYXnBuulmS\nRsQqQ+0/c8QOvTHDdci2iZsTCx5DnPvmVn6o8/O4FBv7OZmzisCVmhNB4YbA\ng073TSx2PXPXBtDqYdb9BE8NplBAcD5qhmrBYWMABWOJ7qboheE7r5GXWtx4\n0XUOFn6UVdNoHwjMAkUJxKY0zTIlmDfLL+Am99du2pA4IRHwgCaxI/Yz2A3M\nVV74M4FTF2zRIGrlOqA80ZDCImKkdkTEb6S3pY6238zxhTh+Y+yskttusQlg\n9DsfueNP8DSbJoMWkGbJBcvg7WkY1qvHIzjlKxgVl2VorMck+yMX7SURZylp\noOc32vUmcbnfs89MYRuGE4YULHUNVzQP8xRh8b/xzoK066VtnjnOUfIh3f7x\nFU7j\r\n=b6kQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCVWkU+1JpK74QOsTVsOLm1L78AyUW7+I3coacFktjr9wIhAJXXWkLlq74fHDf1y578wHGTxZovmnpUxwVBdU2xeyaI"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0-beta.48_1527189702235_0.3729480761546371"}, "_hasShrinkwrap": false}, "7.0.0-beta.49": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-beta.49", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.49"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-beta.49", "scripts": {}, "_shasum": "c7acc8a536658313f624f4f595cf09147ac8426c", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.12.3", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "c7acc8a536658313f624f4f595cf09147ac8426c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-beta.49.tgz", "fileCount": 4, "unpackedSize": 1524, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDNQCRA9TVsSAnZWagAAzYEQAIq4kmAil/WvcJlXPrCV\nqfzFycuKfLsTxS/hgb7OM6vKQ0g/Dy78xHsSHzf5eD4MOWrHOQvYMiKG4w31\nEXUbhD5RbQSCORxJHekRuw6yzTmVsQq1JQajU+f/oHkVWs1cfad3Z+KXaiod\nI3B61SVXGlc/jVhbvuf/EQ/1l/BOGp9LnvUag4NlTRUrKxopSuyQ8rOPYcfC\nadEzmpizstdtD3M3k0tPaRLM5SBCI3ZUemmTftK5a8AIQ5+JBpGm/jrdzFY/\nxKhGDpEotTpcQZ/qqbpcs7n8Rld00P2OWz0AvebUdB/z/uHWc8SLmclCV46J\nUjJKJ1ZB1PDz66t7Mpp31Sk5x63jIs26KYDykzGdRLQ8OIPCc6iE29FfFi3n\nyLzlxLIAopYJC0Z4ESiXpZtt1RHfZzE1WudMsW3t64W+pR8kxKY2/3bhkdWu\nntdJdpJipOFDffBC6fdbT3NYzEDDQO6HDCkdrRhOufEeNDvyAli9xr0ZbIeC\nCRe5eYjlFSBDAkPVlvZ3bA5MtIszWowbkrsMugMp8XiwvZhRpIU02h8oI+AU\nn3LDOAr5cx6NzdVat2D4kEPdDrHZkFPkUzjN/1neUFl5KE23Jozjl51i0spM\noOjHB6PlyX9UGUiOQTRFCTFaQYA7OSWaiVDNUxO3EIkNciWPgJTQkxz0zDQX\nzycl\r\n=d5t+\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-CrU0zRwLhRNimf04lnFQHirvuWQxiK5fnM9TlWOY6nJB5nfzqFQrixP/bALm7l1j3Y3c3R3X/SCoygrvTgGHAg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID1NTOVgIEM/bAAyDGpslAX3cnutUxLHOWAvthURD/d3AiAcFlbTQpR18epvX0PotiP1tk3a1VsgKfdqZzNDCsFCxg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0-beta.49_1527264080322_0.6106253433422062"}, "_hasShrinkwrap": false}, "7.0.0-beta.50": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-beta.50", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.50"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-beta.50", "dist": {"shasum": "fec52d4809904d465c3ba3c2b1d6791c0fe24373", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-beta.50.tgz", "fileCount": 5, "unpackedSize": 1393, "integrity": "sha512-avFp4vppaWvoc+iQqs9ft09zb9ykdDV7ZTvc1TqM5lgMZhqLinvOu+zER1FbOEwUPcctAF7me61RPiFglUcGHw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCVrRjW480pwE+Rm79rH2Sy2PXJdYlVXKNUxB6E5KPhggIgHz0WHO1SvL7mTKh+Cabi456DTDyaClO5FfRBvO1Lpa8="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0-beta.50_1528832825433_0.824915337990261"}, "_hasShrinkwrap": false}, "7.0.0-beta.51": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-beta.51", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.51"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-beta.51", "dist": {"shasum": "11f95e493649231962271ba891776fa2ec499823", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-beta.51.tgz", "fileCount": 5, "unpackedSize": 1407, "integrity": "sha512-He5MSznr4wk1mjr7sw2nbksHlpWXYWTK9FxfFDrswmfAuk2DumdNAT9Tr886301+Fk4fKEWvF2yLLvzjWmH7vg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCULeIHYhnVCEAsTZI/Yq8n84W+r/46ElrRDc5np1NTqwIgIv2KpbB9Oad2sZzwo8utkN5McjHOppW9LGdGeZlrZts="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0-beta.51_1528838373027_0.6066868939703716"}, "_hasShrinkwrap": false}, "7.0.0-beta.52": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-beta.52", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.52"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-beta.52", "dist": {"shasum": "b7f9e8929b7b112333f60428c6fb958f6487ff47", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-beta.52.tgz", "fileCount": 5, "unpackedSize": 1406, "integrity": "sha512-lH8IUJufX+/ZsfkJWQ524xZ2QmTpQb7cfbNIkHrr7q/D9pY2LGqUrgB7sa1Ymk0beAHorRJm8xHNRr6d+Tzy4w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCAMQvyGQiR72B0IazhosU2vE38iFHGqqBZ1m+Xt7By6AIhAM6HR4HOWX7hSMkZg+6mpzgDZDJKQh66Qg8Ii/bG06mr"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0-beta.52_1530838759115_0.10728361534975472"}, "_hasShrinkwrap": false}, "7.0.0-beta.53": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-beta.53", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.53"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-beta.53", "dist": {"shasum": "e1929899c2eaaf534a6db2458e53a7234c01d268", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-beta.53.tgz", "fileCount": 5, "unpackedSize": 1406, "integrity": "sha512-O+mvWW0J+5T+gFzOChsp/y1oeOV5IUTiTgPsLvBvA7/ZRlp/dPr5/U1MgrEC7DhhiJyn4oTtWcTenlhFRZhaTg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCzzGyeLFqxSSa0WCIJn0dlQCOqHF3tC4wl1rU9h5yXNwIgBa1N2fI9msNImJZRdMJzF5fvHVAJ6Eh3M3SOd1qCZ64="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0-beta.53_1531316409175_0.7111148442119049"}, "_hasShrinkwrap": false}, "7.0.0-beta.54": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-beta.54", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.54"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-beta.54", "dist": {"shasum": "3c54d07aafe09714303b995ac4ad6b10b0fdbc30", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-beta.54.tgz", "fileCount": 5, "unpackedSize": 1406, "integrity": "sha512-FT<PERSON>uaqJFAEm9trLq5NzzysSJcLS6Psf/7l3xFQZphCiDsk6fq4Temk2uwHlZtTV3vcrFLWU3+gYW/fJ+RA5AgQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCgKv5N6IEdQ6yYU3tpQArpG8uIS2M+wq8GUJCwAbCRygIgG9UzD0TqHFk+XF+muTRtbwpE6MKJXlISQY2VPmXwWtQ="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0-beta.54_1531763999265_0.2368786392459563"}, "_hasShrinkwrap": false}, "7.0.0-beta.55": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-beta.55", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.55"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-beta.55", "dist": {"shasum": "53b2cb3b288c2ff2614a24cf094ae340c5c8b11b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-beta.55.tgz", "fileCount": 5, "unpackedSize": 1406, "integrity": "sha512-wNQp9JOxESjv/1QfxNXwwsXePMeBERjee1pDzq7ZzKgtNgJqEml+lraByT3Fp/uarm2uFatMoRJPmBx3qB9tew==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDOzWFMQxPAUXHr5vUJmqEfj4Vjq3WFx4p5n1V/1NOs0wIhAKKRCU6v/Ugv6DWIkC1YRo6F9u5tB5M7kGhWPz+YAvHV"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0-beta.55_1532815623599_0.9971373388935851"}, "_hasShrinkwrap": false}, "7.0.0-beta.56": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-beta.56", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.56"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-beta.56", "dist": {"shasum": "c606f48918bbfb4f06a3d9e6325d903aecdcb6c1", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-beta.56.tgz", "integrity": "sha512-TJUFi8zpUCAK9yk4fn3g/nyH7fMHT8FZKw9EBqQDAt3xKFpZk0xWNdUJl3giqdKMu16BqHV+wmzX+HV4XsZRvA==", "fileCount": 5, "unpackedSize": 1406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPufCRA9TVsSAnZWagAAb0QQAKEUbMfwnm0FFje863pq\nhGCeg/KtGEbgto3Uy0AWv+DKJq/VrJBCsgjHgr7W3mf3++1h9zqRDgvoi8JB\nRYveHs/BFlrUFSLh3Az6kLmZp0XO4r4Sa7IJCOnx8u61vATNgmRmLib5nd7q\nZm6IKdS1xScdZ3QnEdzSQeWDFiEQZFDDqooYycymG+JDhcTIMkwliz7V0pO3\nkqOAWaeGXVZNIK3aA0j65Vj+02V3FGrwoKQ1S6n6yW3Yspaw/p4FWRLjZKpk\ntVombNIv7iFJxZjsaTToVHyiec731W3yPC2RNIrwti0rQQf3UGSrSDs9aWz1\nWLf6HyudJZExXyePPJQhUKWoa5ldVqwuiUnmAbrNJ1D9G8H7deddsqX/HT0w\nmdyyY9HR3WjrbvZCCTXElKjH9CAQJ7PccRcjEZYeWe8wtJUosdWWrJEXnIvL\nyTLcjvKROFpbMAra3Kq7HFAolNWttsMQoRvtmA+kpmc/SyrvljKMf0M1+pG2\nBbhStjxRVgo7AEFLoFHO299xujk71POOmUrE6Y/c7t3LUiTL6A8tI2UV0w01\nEEcoxrKyq2CTDzPITeuQ1HZiWhEp4MYCv2/TrrkDt6FQzVqAEfjl6Pz7p0/h\n3/nHN+gbdLWWEPeNZTsjWz4e2s9RcdighjDDngfzThKqL+g48B7eh7IxT7MS\nWQRl\r\n=maPI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDL+DMzDRbfWeDYLvBy4JKZt7FvqhdF4M2cexB/wolgGwIgEpCOauCMXGeFDp6rgzMkf79B9W+B1jGteKDfb+CzKqw="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0-beta.56_1533344671086_0.9324974362964635"}, "_hasShrinkwrap": false}, "7.0.0-rc.0": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-rc.0", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-rc.0"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-rc.0", "dist": {"shasum": "5634375648ffeffbc1f0a138dd74a73d337f31a9", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-rc.0.tgz", "integrity": "sha512-WosZndoqDU/4wkgePBda9rkWlWdWAzSOu/O9AKSGru5JMplI706UH1g9SppBusZwHPrnouZFlpUnH4rzO9MVDg==", "fileCount": 5, "unpackedSize": 1397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGRwCRA9TVsSAnZWagAAv1wP/1GQ5GGRGHNTFIAdletM\nNIlLXwgD4eAsHvlwHdvSyKCPSWznC8BF3YXOnuWZwb1JzxHE0pCUK5cfzvPe\n48Vf0EUxj9C6wpdab4Ly2RfiKmOG8lZ9b9FU3mpRaSfjt+hn3mDr5uN2x3yb\nBRU231pwKQUxCN1NRuYNM+V4Bf9azJonsXu3wCqk7zP6CxGr1Bzj37peylDy\n8sq3Y2xZm43vxsF+gzlTdDo6G76Lb9qc++9bxMp4OuEKxKek4THg6sEDFBMM\nwshqYXmE8yxsyxvl443sjGI2ulcKNDeHJGQNcnunGrBDYdvx7+Ox6LAgF77i\nEq3E4PcAfQjAaWD8Zzldd/6ORmgFPT4g+U+GbQv5eK1rOZzjaZ8fkfbthrB4\nokAeg6AJwemUS95GnR1eeI9701B7EjWOWTNr+ql4o101q1IJTpXBbOj2+PGk\nnttBJB76sF3ZV9HQtHqmbbTlmfG37o5MTp3YYo1l6YwaDip2+v834w3mP/S2\ngel+M0LNDrYoyG86Scv8H8GyOFk4u1x9gV0UImKSUHBE9MfH5IMoKdtjB+pG\nJbxTakY7t5eihUUx+Xj6Qwcm76ZIFCGPbeNAgMQ50H3UcCiz1WP5ameNS+Fr\nVJtxPD+o43kJZDAruCccTQMj169Y1uuVTcUQgP5yFTZM5kMybEs4XlRfuQdO\nkFgG\r\n=2x+q\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDEiBrJVtzOU3/VhUCp4aCHhRbNeN7fetFj3m2VMaJMlwIhAKcoJ9niV2LSEEbMz32vEb08GOLjxcTzujCMmZRz8yus"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0-rc.0_1533830256333_0.637563942094715"}, "_hasShrinkwrap": false}, "7.0.0-rc.1": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-rc.1", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.1"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-rc.1", "dist": {"shasum": "e2325e2de6b6cc6fade37ab1165a2859b2bc6e44", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-rc.1.tgz", "integrity": "sha512-pECmr/Eh3GVtzzJYKOOaTcRvNW2+IOD7M/xPONlQ65KgbpMJVygVXS3lMIrdZx2M3buQeTgLGUplq0r28zA0NA==", "fileCount": 5, "unpackedSize": 1378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ74CRA9TVsSAnZWagAAkDYP/jSZ9tyLunPwbI0Pywvd\n7PP1AT1iNnHc+TUwOyU14h/vvvBAGWMqrikXfbpDEFK2LlzPeXKL+r7hprEx\nZ1gsR5m6f+zP3by/OhDf8UvWXjWkjruvBo2YfkqmzwlQzzdXzGYbiuqGOoCF\nwMc6w22JXoJd64bPYIlndTG15q3AQDmfGHxHWZcAV3Xck3RQQ6EO4sSWw6MR\nqeuvarLBGrjfk6gC3+VNh+f5hj7oEk/9FNP0yMqRwaHPfhDmyfsbH4iD0L4g\nkGcBFhC26k0yHbNxtGX8NYaS8ZYNLhstY5aWZOs1iAOC0HxpQQWcCFxzW9a8\nwHDmR5oZ4dmzE0YkkLM6Jwyk3PJiJCyZi1RxrDOWE3a53tAqROc4h65oM7Do\n0PfBxzeCz5e0fwFDNXHmNzkG+cROpIi6GdojbRw8zcPElRHp1J85jvyEX+HI\nh3IXnofCu4cCnSAD4WQ13VImcvCj/Pe9GtRcLuIBr/1FDOaIMtnQ14AdaHFk\nNA/FIOf3Scwc26R8zq1LH1rIXDSdr74q5LxcIcZhd0FcP2iGRDD4YOn31mlR\nYcDN/QYdo1fiYZVZrI4pZcSY3gNpQfbWBsVP9k1x8KwFRJJPgfx1XfmvBVbR\n3T6Xr/wGHIhCfaOio5Ght0JSnCi8p1KszugUliD5YWtCxn8o7AW6vVX7BezD\ndS/g\r\n=wacs\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEKOcW/O+RgTVP+NxPXJg+a9cu8AF4g4SoxkvNqwUUh1AiAt+eFhpXm6wWL1XHsQ3M/MZpKiysQWWzY9Y7YnQ8I8YQ=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0-rc.1_1533845240072_0.5587680245203326"}, "_hasShrinkwrap": false}, "7.0.0-rc.2": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-rc.2", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.2"}, "_id": "@babel/plugin-syntax-import-meta@7.0.0-rc.2", "dist": {"shasum": "a208393b4da6c815510031011b44acf347f40091", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-rc.2.tgz", "integrity": "sha512-rAoIL3trykJNy+S481XBT9zPXs8ZD4W7P7uk2aEawyVeExg0B7oh19oWK15qbyGur3GkIwvSdDL6PxTODH9Uag==", "fileCount": 5, "unpackedSize": 1378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGamCRA9TVsSAnZWagAAlY4QAKQJOv7sPwh55Rw7vjrW\npFKhJWijI83xvCqhRj96acG1nMraimBT2OKqoFfuMbC6DfbeETAbLtGTTgoM\notPv64ctbtXKDA6wbbh+fQn6rdVmRubDVvfMGjwdhTqtXPf6/OduQrFTRPOu\ngL6TqO4L0uf4axFOMhIidgFOf6IZGxetv+LL+y+jVOgz3vCvgMZ2rt8KCcIx\nXAgFCNZfBXXHXTqXbWU04irmgvOrKGrDIPiAjuKlKIx0/CGGltHRPHCHYac7\nPyL18zUNhxYz1yTRVKCPabi8EA0gIBIVLVBnIt2UaHkbL9nWcSQlNe4wXlHt\nXCulyRjGJSANN60JlOEMyziOYcdNdqha6JO6hLvuDzqEI8JuwpEWJK/wmhl0\nLL7dRtz/4fQdSPRcUJjo0xbbyZ1HYhkEHDTnNnCIJ+adKj2j8OoyTRXRaMGM\nMyzGdMykys7hTqcF23d9eBGyUd8nJYDbaNxcT2zPVld1zBkLxdFzEolmx8dO\n6ooFVF33Iu2jhYMzRY0v/8gihzW7ofnd1AC09LmDnE0258tgGSUIDUfSeGVn\ntlggjMJG/F/sN6v7s5WXhggtZV9tqXTe5ZiQ681N4S33CIeHMtxpPe+n/nAb\nDgj5B7cPLNizNKWBJzjf/sStc5DgyTgjEHKQKE5MXz6gstvoUx3G6oOpAMQx\nDM8p\r\n=Wrbv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCy2L7Otpq6P5G/PpvUMRzLHq4GXLtMjl91ZsKRLfmOsQIgIYSb8fvHvTFRGdJxi3xp0KnihqaGkP5unMzwAmQLdvo="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0-rc.2_1534879397585_0.46658954224989113"}, "_hasShrinkwrap": false}, "7.0.0-rc.3": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-rc.3", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.3"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-import-meta@7.0.0-rc.3", "dist": {"shasum": "54690d85e3e2e7efab7a119bdd7f556f7a755470", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-rc.3.tgz", "integrity": "sha512-hpFdbxF231Dh+LGJi3G7MmT5N6ew36XDfGYrM9s3WgvcL9hi7l47vPx2SKjLR6tDN6OmwC8D9ayt2ZeJQRnwIw==", "fileCount": 6, "unpackedSize": 2477, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgElTCRA9TVsSAnZWagAAZscP/jbOxWUWwVw4EpflS0YZ\ni7Fel0m0yjj00ConqiYpcy51ZmDhFQAFLGJ5oKVtYXyPZpyybszgCt6EAK4A\nCHkFsw6Q7g5U2jJBDXw1dMAKIvzePKtSUdHEbqn6a4bYO6UYwCUMcNSs7REB\niVGq1nPfWkPenu7iLLAbnymS5ldNdNtGzLbfJ+69vBvYR8A5eEyVd2d8cK/Z\niXoW6g+dvW+WaBr0jD18q1OBL2zNQS39kBgMRx36A52xP70daH4sceokOifE\nsG+GSGzFiaKoNACyVlXEOXgqsYdqC8HaExuZzcsVV8JaNRWs0CHv+TfUjQ38\niBGDauRqbW7qM4lDCIaELdKPn+5c06j7Svpejs8cWmrm7u96wt8hG5uyl9lI\nOzNUhY0XwLadpwuh/RrSh5yesZ930A6VY3F7i6/l0NXvK1ehDhCBdi7I+ubR\noaCFoj6CUuqGgDTYOGOCYo8rNIPLpuXIJuJSBKFU3CiO2OuHIuZu38QrFtAG\nxq7IO0YNQxuARv5FkpXQ25Lzn3LJm7w+SpkDdhm7fx6WY1jOV9+eckUb3xQP\nrjzXTBe4JlFXlhB4s9SP/Of08rJVQl/zK8NqY8/AjAREVH9mxN5QpW7xMNwG\nf3FGX03bv/wa0hYx1hsTurIvFEVB1zn5E6JFIHYji3Su9qrWF2gm9AbnU9yI\nG5oK\r\n=z6Tn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDjG4nzyfjLkADotUlx4Lw+wtmMpTX7mfDg+5k1Llka7AIhAI8Nma0uc4AsD0ZXlkiY4f8/gu2oi2oqYrjelBfIIhXK"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0-rc.3_1535134034943_0.3599417925382551"}, "_hasShrinkwrap": false}, "7.0.0-rc.4": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0-rc.4", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0-rc.4"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-import-meta@7.0.0-rc.4", "dist": {"shasum": "ba8fdedeeb084afe80d81815813dd87a85c57eec", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0-rc.4.tgz", "integrity": "sha512-Cu31iHPW2YHlFNOCn4t542hqRFlGNZMMSiXGZd51PRT2Uu7W1n7ATLnYMyi5ghT7idp8I0VNdpuy1nrK3s4jrg==", "fileCount": 6, "unpackedSize": 2479, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCoxCRA9TVsSAnZWagAA6okP/RQRYsTxVFUiLCkpzGCa\nahRmsS7/yVDigcuX7r6Sj7+LOgcEBgw7dCYBcEnRI6EMWKaUXn/O87oy3uhv\nqZV4yJc9+O9q8c9lpcieI03ALV/7cAg8Hk59Gwu+W2a56usg2l1u00e34PhV\niBUxeNpyRR5xnHZuGZ3+ae0sjpZ36UJIjk1kwyTDvCgnZIu172VIU4JdAuuw\nMO0j1ekm5HubKBD7nUbcVNJx7uXIwm5+SKjhNm6Gdd9jvfWkdFTTVk/CPYme\nuOWz9D+xbrSrB7JIvBXKX9uPAdpL7KD/0MCHV8UoIv0vorpMZbvLwW6xdnfh\ns3er73sxoV5vWS51h8cFevyF3r1Gn4E9N53he/6rNande2e95Q75ArrsNyiR\nQ+lJcGnWIr0fa26svRPucesNmzaEq+hz3p6G0phrkjAPN4RTWOjUkjCUvK2r\nSJuh75kt01xtwHYrboWz5AK+JQ2XbJdNMubUL4qigKtWIxnjfv9Rm2hDnRjr\nKzONn1zC55vgM4+ilfaR0POZZaA8tGj2jwNqrwUugdwCWD9Q4vS1LcmuvEzC\n2Yo+cyW+RrIHHQ2V6laCK54Y6ihas6vFWw3bRD3AnCWDncyPlex1qui7VBZ4\nv1gMEE7u0L/PvsKjdE7H1IN4iuHPhpqhuNZfLOrZMr5a/j7qMe+sAHcwvO4T\n7m86\r\n=QDhK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID0RSgrAOggLR1RP63B9KQl3lba67aow+SAsyv0vGb90AiEA9fr0uwyzvzxT0PrsDcvbujYsbe3KdAXXN5xfwu7MmVc="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0-rc.4_1535388208756_0.5247876235247455"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "@babel/plugin-syntax-import-meta", "version": "7.0.0", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0"}, "readmeFilename": "README.md", "readme": "# @babel/plugin-syntax-import-meta\n\n> Allow parsing of import.meta\n\nSee our website [@babel/plugin-syntax-import-meta](https://babeljs.io/docs/en/next/babel-plugin-syntax-import-meta.html) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-syntax-import-meta\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-syntax-import-meta --dev\n```\n", "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-import-meta@7.0.0", "dist": {"shasum": "ca946b73216c29c39a55ef2d739097fee8a85d69", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.0.0.tgz", "integrity": "sha512-FEoGvhXVAiWzpDjyZIlBGzKyNk/lnRPy7aPke3PjVkiAY0QFsvFfkjUg5diRwVfowBA8SJqvFt0ZoXNSjl70hQ==", "fileCount": 6, "unpackedSize": 2464, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHBNCRA9TVsSAnZWagAAMdoP/1kDChWk2vu69ws9o8w+\nRzdkkcyb9AF0+RqAQtAh5tCZ1C3wPVD0exC3EQY8swH71MghyG2ZoB/5VywK\nWLZeiD4ymWAOe1Nrth8GpdPA45MOb0/KOvPfDmQ299k2HoLluLd/29GMkO+E\nsGiU5uBX5oGj0SFSWprXi1xRtM6/hotStcyuRCIqu4jivNT2PP+EIHtU4+yi\ne27qUIDh70tRQDCOEJ3f88KDvN26f4UnJ547jG/BBMA5BaWHOf+7JczPP+9T\ncONFkwFemGLceqBu1FLDeJhmyD6cnUfeXJfpxqgvMIF3kR84r3lsuOdTlyyQ\necn5/WqLf+is/ktQzGIM9VLvpfBLQ1dcyAKQn3n8O3+AXqcdTacG6j9UfwT/\nYFFmicr7EwMiDy9nX/9J/cyVWHoOzRu+gErAzsewpTSvRZvbERKoSdz0kSn7\nq5SlDJf/qpeHc/+T3pJqOIqSEw5I1VOKV63kdvxz+bEoVfvHYD1VI2YKuFVv\nI+P60E7cfoTtFtikyOyZb3uSyJ5CKrYAj+UdO77ptAP5XN4sbTgfBeUL/K/N\n2uQ1oVNGCoG6t3XjC1lzH0hY5DH9mPwTkWEUOos82rf+pDPg5gXl/3OJMSvy\nGLq1DvoLLODvR8hijoC+PPhLKzYXjbUgtga6ID+ntRKzieravQ0KVyDQG+et\nz+mO\r\n=BD0/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCWXSa0Q2yZbqr/gkVwf5hbz3POz7PwgAuxGZW/2U0Y1wIga80iF6qpBUG92vCHoR1qkQhFrUlAIqQZ6h8z/pMgwI0="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.0.0_1535406156535_0.4154661229564991"}, "_hasShrinkwrap": false}, "7.2.0": {"name": "@babel/plugin-syntax-import-meta", "version": "7.2.0", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.2.0"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-import-meta@7.2.0", "dist": {"shasum": "2333ef4b875553a3bcd1e93f8ebc09f5b9213a40", "integrity": "sha512-Hq6kFSZD7+PHkmBN8bCpHR6J8QEoCuEV/B38AIQscYjgMZkGlXB7cHNFzP5jR4RCh5545yP1ujHdmO7hAgKtBA==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.2.0.tgz", "fileCount": 6, "unpackedSize": 2550, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX1kCRA9TVsSAnZWagAAkUMQAKCcrr9KjJ2PUBG0+2NP\ns2Ca041hm0IuSGFsgnpTnuEmF+VXE/pCvilEICpHw6TvaaQJZvkLjl464nwf\nZRcxj9qvhbd4YY+s6tzQSrk2yTmtm2VN36EOA7T7Fyq6ehcBY8QdhK5s3zeo\n3bbVWfGl2cvg8ASLLF1F63Vb0mA3qxphjsQXvcF2zN5HMQEvzhyrPhJyWaYA\n3FXE/y+cFCsQkaNFNP3UIUa+Z7RtHsm5IWKuQVCsTIXaM04kCirBvIPZ8tWo\nNzVTpgDfiI1JnQFau3qbM9i3aZDk2pLr+Xfgjj+xs0eyao1sIAvirzDECCdZ\nORthTgp+i0fBS+ec8SO0mABzs0/amMziQFP21iy817v+vCgAq60RntipMoDj\nZH6Ze62dM0FgvBNpEnpelIaQ7PG7KE6Bo9Rg5FiBVL5gBGIgOMzkAeTgXLxu\nludXpyJzoDaISrjj29a0yZOxCGWoDFrsd75ViGfX7hsYSw9nPTP42MrCC3bg\n3CmoUqGMSGAMDHN4XofmZgx0/oBDVNqHm0wos9C+uZs3iL4E15sc+Mx28Ix9\nv+NRnuGqDaUWB6g1Pjtip5eP3bQsJ0BdivcY7Nr4Ni1HirucMYT4/HoZTQTM\nJ0qpb4mlgFrOe1Vhok14J4Zli2q3MqNCPeLFqhA582wlWRFgeo2koZymzjTM\n1FEK\r\n=DQUZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDqtUyuZ/2x4DG5kG9P50hyqVVj4NZqH3kjBv2pXNoDnAiA0qtnhO9snPxc+OBqQIb+aBAeKZKR7DquvU0zXqpvfcA=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.2.0_1543863652224_0.8265806942818026"}, "_hasShrinkwrap": false}, "7.7.4": {"name": "@babel/plugin-syntax-import-meta", "version": "7.7.4", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_id": "@babel/plugin-syntax-import-meta@7.7.4", "_nodeVersion": "13.1.0", "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "dist": {"integrity": "sha512-hl6aKgm2IbVB+X9Z2xcKvGObOOS//vbohHPpNvd8iaKA3XQi3Sz/3oOyb3GLes6hY871mkbZQYK7lxriVECRAQ==", "shasum": "3e9e4630780df5885b801f53c5f68d75e99e5261", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.7.4.tgz", "fileCount": 4, "unpackedSize": 2508, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/sCRA9TVsSAnZWagAAqzMP/12sksfmsVdeCdRvKTRn\ncDd77Os6ZP65uN0AAi3B2knxkXnO0DMQjVMa+zY9Ypupo/Bb5//cDu/K2BuT\nE3Or80H4wH9BQOnUSgbFZe0itRvbvNMVRy4NL9NQL1xl6vWCUcDGrUYjGled\nMenVdj/jJtFu2EOIhWv6mraAsl4379/+lQo3xI8zCuNO5GtDifLLUygfpYMO\nd0axXY15w/QbIChi/G5d51hj1z6VP/wk01Lw4rUTJWev3tX+3Uwgb3oFPmvB\nzjZHm6n5NU3AApQpMb8bHMj21KCwAAXrtYj4hWzJ9DpoNPREYTe4NYT2B5fC\nrCVHT0v5IlDHwUphp4MKGxCF3dYqfvI/EmedTTNZpR/RGMNB9S1yjfdPx0OE\n53Qy+lZy54bB6x9xH/uqrv81BWlCpg8gznV9j3XZwwWJw9MUVOCA1r+gF691\ny3YTK5VYlrNVyzcmosiF674qoBgamlLB6mvF52RwA8G5cEVKutKPKVYIQ22o\nJgmQrsQA6et6VXtO2tNJBfxQtwVuyKRYHO0JWBC9rBmnXI3Z/DV4Go1oXA+F\njtXLC3QNPFPATWOIxgk3dzt077xc4ndIxcsJxJSgrRn9S+QgAZTp2zecTx5m\nR1cEyvSS+EiIRp7ttDmCqns0vF+FW/d8IySDWM8gtnPPcyISMuEnzk+UM9tf\n48iT\r\n=5M7I\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCoAOBIx3SLMSArXXx9douAZqvIOo3snxUinK6CK+9bsgIgGZpZDy6RtTDOfHuvUSKNVQm539TDzN7OYGH/QeayA3o="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.7.4_1574465516100_0.7007187312749481"}, "_hasShrinkwrap": false}, "7.8.0": {"name": "@babel/plugin-syntax-import-meta", "version": "7.8.0", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "type": "commonjs", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0"}, "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_id": "@babel/plugin-syntax-import-meta@7.8.0", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"integrity": "sha512-970CshtbhzPcoLl6hzfiPhz4I837Xnr1J7VHJCvUREq6OeBTSfy0EdKOsDE0Owa0PwPHqcEo6Zn/lT8EeUE4lg==", "shasum": "d2b5146b838272e346aeaa68465bcb23568b3188", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.8.0.tgz", "fileCount": 4, "unpackedSize": 2530, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVRCRA9TVsSAnZWagAAt+IP/3nv8MHLANd4tXCCsZ+m\nZzzx26sVyyJj9MWai1rkLFPpb7nKclFmSXRSP6vw8EzL32loGyoaUW7cMPMd\noWlIsR2VxMg03BD8nyejd4tpzsupmLBwC6z4EzpTfPfb24n2ZlZVIvdyTqIP\nSlkVleEUQlAXjLNyIJ3YovsxDidzR98qTWNXkDQL21l+xhJTdgtVcakcqzXC\n/HVQwbVREdNDbsNShtf9jU3mD19khtVPqoNMeWln23geeFzGqWP7LW4v/Vu8\ncLhcl1b6H51dcnUwYxoYkG5yp/CKDzJq9eKIQkRRlQHRZ2W01h1bHhFl1P/d\nKD8tm4ppTzJ2hCljDjdEzeEDBKJJcbTc9aNRRlvubFBcy/xXJZHHVsdu2KTI\nZ0htJB8E3xTvTcY5Zl+PhZAbiiXB4Vaffs2iApzNINLccSqH0uqVyg4UWvia\nIQhcGsYKGvZ/a3eBEXZdlDDSUqdDAN9H6+Enhvl25qGckmixjVy1SZ57gVMd\n2rS1QwNmVNPp9OTEsvdUIL8ljjeEIReRHMTD2UelM8Fi3fCV4PCSZVWnOw7/\nRj8CNFaO1J46z9fU7jbclh8TT5UquDabT4wcToht5uQtTlGkjLoe1NfQ5Xd9\nWQVXLcj0oe688WvvSI5wlTcvOY81KJVgfo/uVHDZHdzYYQnchBFfYUloXX/p\nHEvv\r\n=Vsbq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDNUAh/ao8tBJcTuWhYDsZdGFnKTrmUn0+/YvskwuchmAiEA8CHN+A4GhSslUn5ltFD/i/zXAjteEQ+nqVo/VHldCZU="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.8.0_1578788176952_0.515260345504734"}, "_hasShrinkwrap": false}, "7.8.3": {"name": "@babel/plugin-syntax-import-meta", "version": "7.8.3", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.3"}, "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_id": "@babel/plugin-syntax-import-meta@7.8.3", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"integrity": "sha512-vYiGd4wQ9gx0Lngb7+bPCwQXGK/PR6FeTIJ+TIOlq+OfOKG/kCAOO2+IBac3oMM9qV7/fU76hfcqxUaLKZf1hQ==", "shasum": "230afff79d3ccc215b5944b438e4e266daf3d84d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.8.3.tgz", "fileCount": 4, "unpackedSize": 2508, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOP/CRA9TVsSAnZWagAAB4QQAJgCDtHG3v37ddZBRlKc\nJLRdhIcoT6NuxfkCU0VST+PoCgf3k1LlYH5WangYwaSEtUz15dwYvq5WNAhB\n82bTr7olZUYt29xgc99y85aJ9zZ5fQwbiqAehO1DVXAh34ZT5ZJNQ4rusYck\nppucHgD2F3+yGf+YvFUFuEcHKAoNL2S5QvnJItUolGzcCK9KxIMNUrMZPUaM\nsC9qTcJtpaewxTOxs8ZliY+6y4OpC3awg4xRyytcCzSL57egdKgRN/bE1mze\n0zMUAF37F/5J26LcXowqXWzxXsKmz7X2gCCl9aXVwsdN+pmKLEBwjuoJ3sAm\nOivxD0oBizFHxHL5J6BHYsxbbg+BFrTR1k9mekJvQx8+bqqEm6zEMF9c1D10\nxMGQyNcL2A1FJniyeToe0cRklh02gRAs7pxzLEtJNRdD/zUtqPRelAxipMXh\nNTQnxO7Gbg/5N4AJFxrtgrrdfoh+Yb/xFf9DQMmh55bMh9UrA8yhDMjlRb9Q\nIKIPSeU9xHDaoUQ5FGvRk7v5G+bC5+fZMRO+joMqvlfog6dRaaJ2RgHpormz\nKawgn1hu67n8kkjfD8Gj96zJD+Rmb2udEImBXJf9tsPS2IoRtWiaPn9Yo4fX\n7Ct28T4sQLoykJ08aQU/t7Vb9rXxfcbeDf/ZG3l+WsGJDhwYE8eihrehjk9G\nJus0\r\n=RKpX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCO2bw7gkDLE9DUsBApWn44hOpDKwO5AXAmQ8uU512qgwIhAOnE2GVT+qphHHAOyAlKJqRUGvZOa9u0ruznnlihrPfb"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.8.3_1578951679172_0.044068063601654606"}, "_hasShrinkwrap": false}, "7.10.1": {"name": "@babel/plugin-syntax-import-meta", "version": "7.10.1", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.1"}, "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/plugin-syntax-import-meta@7.10.1", "_nodeVersion": "12.16.3", "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "dist": {"integrity": "sha512-ypC4jwfIVF72og0dgvEcFRdOM2V9Qm1tu7RGmdZOlhsccyK0wisXmMObGuWEOd5jQ+K9wcIgSNftCpk2vkjUfQ==", "shasum": "3e59120ed8b3c2ccc5abb1cfc7aaa3ea01cd36b6", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.1.tgz", "fileCount": 4, "unpackedSize": 2559, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSUCRA9TVsSAnZWagAA0w4P/03sxZC1vRK6uulKG6HR\n+OYNPqF0fNSW06ZvyRnl5iy9jR5MOG8z9IzvuJzcRRhYrgonNL30f9Wx2eJV\nS0GpUc17wfYYG+T9f0r9+lNjf8/ZI/ytCPiXPGGLb7MctrI3/Yux03GNgKGP\naTBREiFCC3+KPdy5D4EOjomYE5oaQMCEd/acOvLa6lMXJvo24u39hXbjefs2\nKENZGkmP3Z2XnMiLur2zr32X4pJvl9stu5YIvIDQCSo9IoFt+u+FB2Dk6mXy\ngrGohH+IYKs+E412GUhiasjmvFVB6qul9QOyQMgO9OVPEmUXpqLl4fqS0VRc\nDCBtn+i7kpzB4U1DeXEvCQIZbQ+XSfiuo3Lx2H/jfaJZZmSjcl97hIkppn3z\nLTCyTjSD9jW2KoB2m6H4bbhwTcg+Cl7YDM+KYeXEWuj+7iXpJrGSYTwbDOwd\n+Wmrykbi8j5DtC9ntV2GdJ0hBMfq8TTtcYphfov3V8PSIhTdMu+3BXshh5zF\nXbwmWATkmasMs+NLaMaHHgqhSxXCcO13ZpHw7DMFzCkARX0Iy0PtgE25862M\nR/oM/3yZ+v4E7Sb+VLzVQTd+jG9YAP8j2uNxncoZBqDUFXw9JdulXL0aRDtw\nTOgPVo3CiWHN3cMyxV7Wy5u7kkuZnohmLUix6o2nCsmnxKy0X2I3NO28i3LO\nSHz4\r\n=KT5l\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEiEtBg+GNcCZX6i1oOk2O6jathke5jSrX7QnlZwVuZ/AiEA1MqHeSjik+ohPduS1vrae1xEpJ1TeXBb6NxV6e0JkYk="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.10.1_1590617235584_0.24404618051182436"}, "_hasShrinkwrap": false}, "7.10.4": {"name": "@babel/plugin-syntax-import-meta", "version": "7.10.4", "description": "Allow parsing of import.meta", "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.10.4"}, "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://github.com/babel/babel#readme", "_id": "@babel/plugin-syntax-import-meta@7.10.4", "_nodeVersion": "14.4.0", "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "dist": {"integrity": "sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==", "shasum": "ee601348c370fa334d2207be158777496521fd51", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz", "fileCount": 4, "unpackedSize": 2559, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zoSCRA9TVsSAnZWagAANg4P+QEIyGFm4B742siLDUI2\nuDE2GLdVBNxZqRzHMNLj1MIZmgiuLRL4pljSgGGqQ/7qYl049ieGBBQkI3RB\nqA+3sy48FCGSXBDCH4ggW/l3mP+pz3L8xTTyfQceP4LGy+UxsVPRLAdLu1Fx\nnWrVIK5gMeHna1ncLP45AhnFGgTjZ1rsX/G3LaodltIkXFmyQQr9DCo6wlCY\ni3PagaGk0wqXirOJK8+LPht6WZO2/gSemk5bMPRJMypl90r9LbAtyDjXrKVg\nUDPpDC+Qb/MC0iTR7BRirBhMkOJvfEEAyiqi9zjFqmJeKcjv78MF8+EkrZ7b\nNpFfUCoJr1dhM2a+ZQC4qqt+F/kfUHHjKOaYQY1Z3qwlJDUhhvyiuUGR7NBu\nmy6+r9cuIJVsB3nKqCBuOqb0bx64E8hqA1/Sk1Su/8bC9To7ye9VrIjGPms8\nFcp/c9u7VUiPoNL1or8Qg6mbA8Ei3WWvzuhG1T6hYc/OhT9y0nY89tZHphd2\nxpvcXCNIFtv1ZzjncJCr8Ezk2+inZyUGUCiZdDQmyT+6+ifpebL0uhhSKabJ\n5fJUSgQwBTh0u//4HrzyJ3e4RLdjxB0sEB+EKO4B9fHY3GxF5nn2JSyOn4Ew\ncQEDN4iuOdbZU5b3r4CgUwOEBIpnN83p3GZP8oP3PbQk5Ef0s7O7zTFBfY4X\njq42\r\n=9EUm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCLT5A2PlK2jeD/DYzOa5S+TOoloKIRGr/xRNE1pMNoPQIgC2GLqeUUrRx7RXpkCxBpVxjbDOet8jW7uVDZ1tDVOVw="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "jlhwung"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-import-meta_7.10.4_1593522706107_0.8781805696200315"}, "_hasShrinkwrap": false}}, "readme": "# @babel/plugin-syntax-import-meta\n\n> Allow parsing of import.meta\n\nSee our website [@babel/plugin-syntax-import-meta](https://babeljs.io/docs/en/next/babel-plugin-syntax-import-meta.html) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-syntax-import-meta\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-syntax-import-meta --dev\n```\n", "maintainers": [{"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}, {"email": "<EMAIL>", "name": "jlhwung"}], "time": {"modified": "2022-06-12T15:01:02.254Z", "created": "2017-12-25T19:04:23.762Z", "7.0.0-beta.36": "2017-12-25T19:04:23.762Z", "7.0.0-beta.37": "2018-01-08T16:02:22.539Z", "7.0.0-beta.38": "2018-01-17T16:31:42.347Z", "7.0.0-beta.39": "2018-01-30T20:27:26.570Z", "7.0.0-beta.40": "2018-02-12T16:41:23.750Z", "7.0.0-beta.41": "2018-03-14T16:25:54.211Z", "7.0.0-beta.42": "2018-03-15T20:50:30.852Z", "7.0.0-beta.43": "2018-04-02T16:48:16.577Z", "7.0.0-beta.44": "2018-04-02T22:19:58.036Z", "7.0.0-beta.45": "2018-04-23T01:56:16.357Z", "7.0.0-beta.46": "2018-04-23T04:30:42.909Z", "7.0.0-beta.47": "2018-05-15T00:08:23.384Z", "7.0.0-beta.48": "2018-05-24T19:21:43.572Z", "7.0.0-beta.49": "2018-05-25T16:01:20.416Z", "7.0.0-beta.50": "2018-06-12T19:47:05.498Z", "7.0.0-beta.51": "2018-06-12T21:19:33.067Z", "7.0.0-beta.52": "2018-07-06T00:59:19.177Z", "7.0.0-beta.53": "2018-07-11T13:40:09.215Z", "7.0.0-beta.54": "2018-07-16T17:59:59.309Z", "7.0.0-beta.55": "2018-07-28T22:07:03.656Z", "7.0.0-beta.56": "2018-08-04T01:04:31.142Z", "7.0.0-rc.0": "2018-08-09T15:57:36.407Z", "7.0.0-rc.1": "2018-08-09T20:07:20.167Z", "7.0.0-rc.2": "2018-08-21T19:23:17.666Z", "7.0.0-rc.3": "2018-08-24T18:07:15.037Z", "7.0.0-rc.4": "2018-08-27T16:43:28.843Z", "7.0.0": "2018-08-27T21:42:36.611Z", "7.2.0": "2018-12-03T19:00:52.442Z", "7.7.4": "2019-11-22T23:31:56.206Z", "7.8.0": "2020-01-12T00:16:17.071Z", "7.8.3": "2020-01-13T21:41:19.314Z", "7.10.1": "2020-05-27T22:07:15.796Z", "7.10.4": "2020-06-30T13:11:46.202Z"}, "keywords": ["babel-plugin"], "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-syntax-import-meta"}, "license": "MIT", "readmeFilename": "README.md", "homepage": "https://github.com/babel/babel#readme", "bugs": {"url": "https://github.com/babel/babel/issues"}}