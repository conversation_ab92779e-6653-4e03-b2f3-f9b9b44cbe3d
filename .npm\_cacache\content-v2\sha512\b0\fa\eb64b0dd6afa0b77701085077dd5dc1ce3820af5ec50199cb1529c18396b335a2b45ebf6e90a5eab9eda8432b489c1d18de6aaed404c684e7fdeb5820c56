{"_id": "@babel/plugin-syntax-optional-catch-binding", "_rev": "69-6b58347da7dd6846ed151cbc5ada5152", "name": "@babel/plugin-syntax-optional-catch-binding", "description": "Allow parsing of optional catch bindings", "dist-tags": {"latest": "7.8.3"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.4", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.4", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-hleSYx1QaZS0jIDeBMaJThxqGrY9TL0dB0jgBguXSC+JnGw8PF8r3PCmFwD0l9tFcnfaZYVAbeSn5qD4LO7a2A==", "shasum": "01e3fda789ec302bbbe74dcde78ecbf403c6255e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.4.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC8CjDzHXKnO+2MAob50bloiRAWZACrp76xhO+gGQ5DHAiEA+Suk5rLYSOJ+FisPrgucaFaZqG2/rlTWKnaW0AuKPIs="}]}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding-7.0.0-beta.4.tgz_1509388465222_0.273294708924368"}, "directories": {}}, "7.0.0-beta.5": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.5", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.5", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-UzpsO1zA+tlDPd9bj7tmaJ0SE9Lkk6p8tAZg0nYxD86sQcgyWtf241RyHIcNngDiGGnWnXxkrf9IFL5ssRt3cg==", "shasum": "453d8c261ab50d9368c089e7b736d37d359232fd", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.5.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDERLgf9lgu+nEqQYEA9BqV05yJVtVFq4VbKW9jO4JoQAIhAJqV+ik9X3yyRvRPHCoBUgErI30Mshr5zm1i5RwccJbp"}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding-7.0.0-beta.5.tgz_1509396967395_0.5607712909113616"}, "directories": {}}, "7.0.0-beta.31": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.31", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.31", "_npmVersion": "5.5.1", "_nodeVersion": "8.1.4", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-vM3JWBl8ocm4jUvEMG37ldJe3eESrdswcoGoa7Jysdo+41kl6ddxmPVQbOk0nBpC4wBcqeNctndD+S+dvQTw2g==", "shasum": "9a999ecc49c6c55bd040622bcf6661824bdaa088", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.31.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC81ecDO0NLB6PzUYt5GEwgrWK/MYiLA4l/CTXNOVIhvwIhALl9mhjjlfpwtx7RKSuLlIgImdjjy0cDaAy0W7IoUc7W"}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding-7.0.0-beta.31.tgz_1509739397405_0.1495993419084698"}, "directories": {}}, "7.0.0-beta.32": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.32", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.32", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-wu8wvUVE3FhnyGY1d1R1sWkgnPwV9pqsHRq/XLZcNIcFwDdUvWWzyCGqkajzvvgFV+9w/QMd2WBcOUsUGXkEFA==", "shasum": "414e871378d1a800f91997f98f8ab85743043b8d", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.32.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDBpMGbE0UlPkTDX+g7CFWsfc5zNKDzD5HW7n+oHyJLEAIhAIKF5snCdyA+APzNW/Lr1E7pf6/hiodRqjWVVL1oNSoD"}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding-7.0.0-beta.32.tgz_1510493585975_0.15415657684206963"}, "directories": {}}, "7.0.0-beta.33": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.33", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.33", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-EMdSvAWlMxpl3Dz5wXV25rNx5dc2QbxJbdjUE7NTEvD3HCeG7yEyXpKVjZTfDW2sUtmK/GqrD3GVMihk7NU2qA==", "shasum": "e0ea477b52a899112fac403ddc6781b2bd664278", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.33.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA+TnYOlFVVqehIVmewUdCNVMzEmHZeKrr0aMSq3T0zqAiAoUD2M//Q9+JbiuBNzaX2tANA/LDtKLC1ebeeb4FDnwg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding-7.0.0-beta.33.tgz_1512138489131_0.06341435108333826"}, "directories": {}}, "7.0.0-beta.34": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.34", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.34", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-zrWVkoPmR5aABRNhB4rbCwYq4SykVI7lmcJwLhFTN8yDqoydjj+KE583ikFJCuNjbZ+byU8rMm7bOhwJvrioWw==", "shasum": "b677c0863cd2373b46fc57728277212f331d6c2e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.34.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC1R7e9YDDlUPFk/xDYgtoLW4pVJvvU33+8/N5IFjHMjgIgRCR0eqDvghk4EbVr7pqDJHi3/S4ZLTA7I+KWZx7e02Y="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding-7.0.0-beta.34.tgz_1512225548759_0.12331564957275987"}, "directories": {}}, "7.0.0-beta.35": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.35", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.35", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-RNr1NuPGLSBL+5vQq7hvDm6FPQyK+E6rKVlEwMFTyHNq584J305hC6ZFDtt9TqVDN00yBfldguSUikHNZSyfnw==", "shasum": "a2451f5dd30f858d31ec55ce12602f43d2cf5cdc", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.35.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBbrYCBUzzCROWLZVAhIOMMZcHYJdoX/tH2YN8cdsWG1AiEA6mZANAd9CpgeK7dPq+ht9gSpAPSUC3ySyItLCnmIMtI="}]}, "maintainers": [{"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding-7.0.0-beta.35.tgz_1513288057534_0.7056493707932532"}, "directories": {}}, "7.0.0-beta.36": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.36", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.36", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-k/kQYxD7oQd2H6CPGu8R0iVNqLMW6Udcge1vs1cUyMdDpMuVaTKYknhxDhd6S5ovZFgHyQC2QbguMHHQbipWPA==", "shasum": "8d73f03976280315d6475679d89d4583ba9deafb", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.36.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHssfi18JRi/7tU57ALhsTI9je0bQ0iBTauEL5I67oKsAiEA9H6zhYaEXP3LY+Yvoon0hVjlMVgqeQ6IxdBFqDft9oE="}]}, "maintainers": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "kentcdodds"}, {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "not-an-a<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "mysticatea"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding-7.0.0-beta.36.tgz_1514228666696_0.7455163334961981"}, "directories": {}}, "7.0.0-beta.37": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.37", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.37", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-e8f+0Qrk4zBIKYh7qi4BYviLjM+19csb7WXT6uLaFdwFUNXHIYBNrwVnxqXTOwazONjAM5eoZOUIC/+FDRzAnw==", "shasum": "d0fe8d2d8d301eb677696eecf159a3089d57b23a", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.37.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEtlu1pehaBVH/QyjF3ANPR7QK2H64thYvM4le7oRvQ4AiEAm8Bi9lODRFWiYX+F0IMzUmm9aig8gCh4UEtO5fXjZV4="}]}, "maintainers": [{"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding-7.0.0-beta.37.tgz_1515427344483_0.04056426230818033"}, "directories": {}}, "7.0.0-beta.38": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.38", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.38", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-682EA8vPvHjQzmt768dp214gykrVFCxybbmLOTWql7w3XMUE5x4VGiXBITvu5rAMdzr6jX9dC9wSA0tp+efcgw==", "shasum": "99248ece1c5c6111d71182e206682048d93759e4", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.38.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCnLLKQa23USxovslGWI/u16qcR0vBLSy6kMlpTecSveAIga+YL0b2qkUkaUrNwsOLskY1SEsV0hvkwCZrtAL1BGOo="}]}, "maintainers": [{"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding-7.0.0-beta.38.tgz_1516206705260_0.7296503384131938"}, "directories": {}}, "7.0.0-beta.39": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.39", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.39", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Ur5ys8M9uWXJ8B5eZkVNwZZ/jOAkoCo/c5YFOtSw/v2lQlIfd4Ct++y+3/bPlLBvpEN6qkXzWpKsqqrHkWJD1A==", "shasum": "b6d3c27d2ceab69cfd132ba19a6d22fd37817fb2", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.39.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEd9T/FVOug+fbDEzMm44ReHm3epf0S/dvHTD9SfHDNRAiEA9IlzcoThMEU/KzFul/OCkFPRYSjMzE7DZAZd36jeKPc="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "xtuc"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "hzoo"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding-7.0.0-beta.39.tgz_1517344048114_0.7038615918718278"}, "directories": {}}, "7.0.0-beta.40": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.40", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "devDependencies": {"@babel/core": "7.0.0-beta.40"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.40", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-YP2Zqhi6r5mnp6LALz19pvF7szhEzBFybw4KqGFj6OwGmfF1nrvCG3h6cOTRhIKSwkfM7IlqGZm+GBhbYRYxGA==", "shasum": "2e3de0919d05136bb658172ef9ba9ef7e84bce9e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.40.tgz", "fileCount": 3, "unpackedSize": 1569, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICbZgZRElG1hiTzP1cFR3a3PdOPfMtWw+T/Ma5Icz1x4AiAjgDddsn5JUYdqU17q/SCJ2C7OaQwPAheBZyuesbuqmw=="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0-beta.40_1518453686427_0.4960128594291031"}, "_hasShrinkwrap": false}, "7.0.0-beta.41": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.41", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "devDependencies": {"@babel/core": "7.0.0-beta.41"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.41", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-mbjIkxz+1IYqpfR/09h07WmCW6h+mKQJskOWYy0OUoPiqiDxpFgg4AQmZuFBX+Qx1j449952WKtWDxt2ay3zgw==", "shasum": "115df4798d408731aed14cba981d369d40a7700e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.41.tgz", "fileCount": 3, "unpackedSize": 1804, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIA8nkgLN4Rpvs0kAMRlgOw8UkzbkitXYtqd2cMG3WbhLAiEAmHJkcZRyxXDxHwCaU0BpdtbHD2sOrz34k5Jy2PZJkN4="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0-beta.41_1521044758533_0.12680460126248616"}, "_hasShrinkwrap": false}, "7.0.0-beta.42": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.42", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "devDependencies": {"@babel/core": "7.0.0-beta.42"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.42", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-TXJpGMX/M9r0tkJPgZtoaBCdrhFMeAZtOiwNtVf5dWoA6TOmDwnnnbaUqynbBZm16jE7quWW5VhfFjDlOP1vMA==", "shasum": "d3ebfaa463f42f5a35be5cbd2f27c1fc3bf96c1b", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.42.tgz", "fileCount": 3, "unpackedSize": 1804, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIESkoHbzIahTDUMg3efbDwDbk8z1/7ZhgQWYauyYgHzkAiAJQsGChZnhlUIXp62ex9zSQ1Vh7QZU88kdOc6lQRxbyA=="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0-beta.42_1521147033139_0.3524464724992875"}, "_hasShrinkwrap": false}, "7.0.0-beta.43": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.43", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "devDependencies": {"@babel/core": "7.0.0-beta.43"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.43", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-aAqcfLbKa+QWxbik2WUEr10TlVrm9rovNfsh9wx2Ao0vfhoZBP81K2ETEYpo6dc9B91WuQvXn31honZILtwBWA==", "shasum": "486aa07340457956f7a4d351608a447b3f5b1174", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.43.tgz", "fileCount": 3, "unpackedSize": 1909, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVCK2OvLtuSl9CxKFgZKi1hWUra+sjUJvED2j/OU6VMgIhAOuSbWzqpwaTx9IyDGHXJq4QRMFLSsf/sNGTEW4vJY5d"}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0-beta.43_1522687698687_0.7382589409924236"}, "_hasShrinkwrap": false}, "7.0.0-beta.44": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.44", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "devDependencies": {"@babel/core": "7.0.0-beta.44"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.44", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-JnkSHFn+F3KHhFXRtc5qdkvOcFVOZR4NqIdgoNp87pNrLksmcHRwPtbJPuL2vSyUlr1Fd0QuAt6CDNpgf+55Mw==", "shasum": "c79ee93c371831b104bb0a1cc9c85ac5373af4f3", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.44.tgz", "fileCount": 3, "unpackedSize": 1960, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF2lBNajWtY898KgWFvgQZ1arEj3TTjZ8OiFGFOgdqjSAiAycYGdsqbvt5yJYdns88bTowp/249w25/Iqd1wxpMZDA=="}]}, "maintainers": [{"email": "mate<PERSON>zburzyns<PERSON>@gmail.com", "name": "andarist"}, {"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0-beta.44_1522707601065_0.3493170331231785"}, "_hasShrinkwrap": false}, "7.0.0-beta.45": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.45", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "devDependencies": {"@babel/core": "7.0.0-beta.45"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.45", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-1jXarxVX1LYZtIy4kemDEvaFolfAFmRvHVDb+MFfb+NfAJTuGNZ1pfOv89x0CS8Fb/Ia59yChGGOl/4J409jzw==", "shasum": "2ff99796afba1d4c11c89c9962c065718489b671", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.45.tgz", "fileCount": 3, "unpackedSize": 1960, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T1LCRA9TVsSAnZWagAAMUcQAJm43+dYT1vJMVmQ7bk2\ndlEP4R/XY6eVuTo1AGHxkCYZFwh7kMLXs/k1oLY8vYCuCSYbOYNlXeTcNwwh\n2MCfqolcncIsOSXDELX3+Ziq1sGHnWjcHZ+M1bxtlZW+dUBnN83LmK8b4TJE\n8Vhr370Y91FGOptjFZAHlPIXWZAMFSxv5j9s3o1UKqcekZUfvP6KehQQBL8a\noCpjcJnBtbBQW28VrCrhBSXG5VAOCYvcSlrly/kHQYIOf+jTILw4Btxk7HM9\n05QCUFkhtUzLYhQdigxiKDUzGZ7rMSbs7EwcJ4w8d7B3U3+H4QnE2MVzhg1a\nEig8DceLzPTCgf8mZbmAHTcy5V2rcekIcJqHS5Yi9d4zST9ejAuC1tF6/fk2\nNUvatgej8QZPfJNDP6MD2lps2/vJdQjjuLcwSKNZAR139kL56lrqIwsZN0TT\nh1VTQ6/E3vtV3dnx303Cq2koM3/vWDrTGpr39iXY/+dFx2ZWnOhrCTldHhCj\nvekMXl1H0voFAjCxTb+zmM4G4E3xBGxVuAoBjr5gSKt9PVd+KCjBndw0jv4H\nL2GJ5EekN0kbaOa2tuvXkjJ/LdNPrg/OFejtw3JJ0qyQt7ZxXt/3y6mGoPT0\n4SAw5trPFJ6ywgpiLALdjDEzBwvTxyi5KvgtG6TVSXb+xeh/CF9WwVcss7Rv\n07Oc\r\n=jNNe\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC6HJ+N557FNL5QQjoy1g1LTFHcG41Z0riBLnjK8FJEQAiEAnWDaqLyW5X5LINLIYl9v0qxStOoFBGCI2GZd5mJNax8="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0-beta.45_1524448586728_0.23661412550818306"}, "_hasShrinkwrap": false}, "7.0.0-beta.46": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.46", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "devDependencies": {"@babel/core": "7.0.0-beta.46"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.46", "_npmVersion": "5.6.0", "_nodeVersion": "9.4.0", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-BRutzJrTLEPUidyRP1n4O2ySAww0wuJw2gIoT4iJ8Pm6qx4fqm/DM0+++TB8nR3/Tp456pHRm0bVOqpkMJdJbg==", "shasum": "701ba500cc154dd87c4d16a41fa858e9ffc6db89", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.46.tgz", "fileCount": 3, "unpackedSize": 1960, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WF8CRA9TVsSAnZWagAAltgQAJigYPIs4oxbifZhOFaQ\nrOPaaGl4iipIURNEPGUHt0V3YqQ6oUNwMl2+aV8k1Hbq+Q7Y0TkXi+HPbmkx\nNv+htwD1QE7w9RqHgamK4Kc8jxc29kujU2KmT3N9bbxeCzzY3A4yKYQOpWDu\noDXE7cDQJxWUYWURQjhMnykXtryq3ePhI4kNRwfsx0+zjz5LcgMTzAQIi/v3\ncpZG4ABwbALn+unGOoOAN+TxfgmtP5lGhRP3WTJQqHEjjCRQPzMwdc7fipCa\nvMGk4yA42QQPqYno3vA0uyjnoiwURYRPpn2sK88xHJauf4NMlHjvpySMV5BD\nFe+PYXt2cb1Kx8oc27IVmXaCOA27dEvswoQwLruUrXYeXG2KBuMOEEXOJgqV\nATRi4Me8r23Z4eGzVEQS7VsjSrGbjAZOwsz+5T4X514/ywx/KKeOkDKF3Fyo\nA5/+QEdx+s/7ql7Um6DQRxgnL+9WB4//97ZNgrWNBTC/V7V7eSxJpE6MfDqm\nTL9NnD785nNFGs2wO5KppUlV/Y3Tw83VQAh467fEvanICtwvqYm9ywy6ugJL\n3Er0lZhOT8S3VUF3QapFQqwVx8GszrYACImYFSvgmChX96tYgNVpp0rQHt7n\nbUx9OTs8f5wOnfgVIKOHSNwOvqVIb4oBUVDblQCJP5Z5snDwfa/G5N2XSTwX\nigfR\r\n=RNrw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGfMd/NuaOkXaHhXg+K3OgTQedSKfebIS/cR05dRtyv9AiAxDI1gFr6No1RxDF7ycXqSCfJvtolQSDf6Wx67QzCA8A=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0-beta.46_1524457852404_0.885908372064873"}, "_hasShrinkwrap": false}, "7.0.0-beta.47": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.47", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "devDependencies": {"@babel/core": "7.0.0-beta.47"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.47", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Dmq+REMR95Syg+VjV3bh63DD/tDxdraNQ4ErAOXFobfSCDbfov9YGkqSJ4K61LHTQwinQ0+dIUlgdFL2kbedIw==", "shasum": "0b1c52b066aa36893c41450773a5adb904cd4024", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.47.tgz", "fileCount": 3, "unpackedSize": 1927, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iUACRA9TVsSAnZWagAAF6EP/i8Uyxy15ZdCWPpG8bJG\nCK9TD8NZaClE4XRnqkXjzwp2izvw9H4EMFUMcgdOzUbS8CxILBCfQAhX6VrF\nHbcxXbiT8RxF2Rkopk2gRRznnQS/ngzzf6yHLqAwVFu0ogcLtMlDVb6iZ3LR\n2USSaRhsBNsrcudbhFqUPVoePnJ+ufXIwAWiZk8qzAQZt37w/QyeE/6npbH8\n/Yp1t45YCElGnDk4Wa6WhoSbjkwKOm2nkQuM/31xS0GnQ2+rcp1WS2GRnRIP\nfRYFxcvKLhiLYkZRy3Qre/tWdLYciSbNjrLIuJtsl7Vq4bXwU7d+YCo86+8B\n9lTmaKHHk+xaLKuQ8vVj8CQJSDitIZaFURc94WfJtwsc8/KksYcO9ZyvdqYI\neOyO/qNN61CHKVX4Q3PI1fnmb7hresPI+MqiyTfJrVfB5r+X1/3CgMrbEx2w\nk/cgzFj9aRnmLK5x8fJ9ht5YTMZE7xcJVbGTFJcry2XvhpqUfAhGCqfIEp+F\nP6xyuL6ARSZDa2q5N+0dnQJAgQEZc83mxI7uOZ0VcRqf4t6cZ9/IqzV02GUH\nWVfKVrsK6SPfi41QHViM/Yfhxxf5iXSSK2c9VGoIm0j7rmZOwpJA/vlCwGJq\nlSkH0f8scch6lPLMeOHNjshcZMNQVyxvQ9mqdS7VrhWX7v+1Vp5BYjemWsgz\nJAkV\r\n=ew+J\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID3IhJ+GAMYPwsLcDLPFz+aKKwimvRvrlwB5ekNdok3lAiBIVpZ7bvF4coA6LGp9aFIFvGzgce8F59qrLrACXUbAew=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0-beta.47_1526342912281_0.04252795283309396"}, "_hasShrinkwrap": false}, "7.0.0-beta.48": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.48", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "devDependencies": {"@babel/core": "7.0.0-beta.48"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.48", "_npmVersion": "5.6.0", "_nodeVersion": "8.10.0", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-1aQCrKkJBhOXk8DqGiFXnTz9nnSTkesq30k6tzHvHcxu1AzcrcOJPPNLFfl85wamhoSIS4+A0twJIFz6E2sgMQ==", "shasum": "1ad6a8d215954cf3d9bf57cb08bc11099626c371", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.48.tgz", "fileCount": 3, "unpackedSize": 1909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxDQCRA9TVsSAnZWagAA+EcP+gKmryZs4JzyYDx3vJkZ\nNI5pFA9oEv5HBSZ9FK+/ztbUpBnbRkV2OfQ7Rd3C9FqGvP7hPDZ0avEJDDWi\nKQgVQnh6ztAs3mFRivjs4dlyimY0BX8VCQaBOV1x/9CSckfNC9LeQsevF7Gw\ngDPMYh6V6P5WsLTMdB+gbXuZRZcDxGay4cKj8woJZg1of4OcQz8Ofam02hbb\nEhqw8qcXkeh2QZAv+fKQVNU1UjhjutmD32nu5aPtdk+2a0X/ODvPioRZGo8m\nP0NpcpHGShHiGKEgvCQ3KQu811OtnLCC5RVmXZ0e035HBIyi0dW+HKJlmcXe\nfT0vxzuwTKWAmuJhNmCtuyMvxfKdU8aR2bz7oVDtFgzIMsOKFG5aubmlItGX\n/1v4T5iVLWGts+Usqf/8g37xosolzwlnmpHEzkenY+C8XmT1DZDgDqHByqdI\n+Llt5aCcFnNrBoRURuKlMwf4m/+1TEaihPsCYRLsI3OSF3BRfuEQ+y34QzTn\nhGM37wuqD6t0Bj4OqMESvSx/AJ0AZoOk21yJKfw2UyyknVd4zfFWcwKgg+F5\nx/FkxXB44Rh1xqpCOtTIZVegWT8WpgoHoKnhS2Tw7oj/AU17kgKTR9ihJmag\nJmC5QpOjndowrZp3uIWJVwXtqudheg52C5aEeamfsPlhjuBWHu2EXkFyZuPr\nebEz\r\n=EmDW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFIufgC3Pe5KsYOVC6u/nGmNJ8j1KIxouoxKz7zeHX0IAiAkGVsfoB11ZgPpntuoTfm47lGX+EtqgXGs2vj0tB3Tyg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0-beta.48_1527189712456_0.766930417419704"}, "_hasShrinkwrap": false}, "7.0.0-beta.49": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.49", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.49"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.49", "scripts": {}, "_shasum": "3e1dd3d5daeb4270e4ee4863641d4faa06bbcd11", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.12.3", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "3e1dd3d5daeb4270e4ee4863641d4faa06bbcd11", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.49.tgz", "fileCount": 4, "unpackedSize": 1924, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDNeCRA9TVsSAnZWagAAaucP/1jXEUtW2BBNTTE2xJfg\nAwLqNHx6TGzgrK09Dakg3t1AOjvKxHQWdl59CxBDSBM+Gc+5oKr0+Jy4oyP+\n07VGGEsSboJmfy6cde2/2J08Sp5p/p8YSsO7WQpLv9LPCZ+GGn+UKjBoeoWL\nRKJkNw7HcYkYvduHQEfJ4D+EfDVZ/m1vzZjQa8meDzAg/LZJgYh5jABwr5TI\nkCbaHl1nVuypIHqax/lVEN0jN4YQy8037/i/ERyGdLySfykS+x279SQKhvE7\nE3U7nqawMNFoQCmolwtFfMZbL10vHXstK407IEf0OeQ77P5wLPRIeK/E1d8/\ny4KxcXYW/yKC3rRKydRNYGl58HvcwECfWU0kkc93S4GJivoTlxlvrW5ouVkv\niwXGTUdMcV40QwcQOnyEYkrOA1creJwKP3PJR4NpxUI5S40wbVYFMIn2N/7U\nID2XEAa7DuABwhq2Txy3K0AzpB2/6NzE55uOxVurjCSgAU6+k0zsv+5OLgoF\nX+HFP6tn3P76lceT6wB4aGNqkKSf+C3uDRI7+7MPymu8cHt87bQuWbUkQ3zG\nx6aQNSEdaJEhwFzFlT1cVOlzIV390oVANYs9Lq/726okYyxYY+ovgzDtU7ky\nqxt2QCF4NKJ6LWptgs3gFlrLCOTKdd1v+oiXuaI+MJIXzoIRxvBBggwy4CxB\nYiaS\r\n=mF6M\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-1EQRu36WpOyLYl43qIWuMIZLNQoMycj+cc4M5cn3iFZp3ULlf3p0sdviaG+U3ebswxRQ95AwPqirxozkjOUp5Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQAALZBlSFeEAzsmooScSp6W9Am0nfu4MqGDaKPEb0BQIhAL6iGgX0m7oBVjLcGbgXPsb/5JNdC5TUHgy5JUQI5fuT"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0-beta.49_1527264093731_0.10923927684012846"}, "_hasShrinkwrap": false}, "7.0.0-beta.50": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.50", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.50"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.50", "dist": {"shasum": "6442159654efbde3a6ae4e847999844e1e45074e", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.50.tgz", "fileCount": 5, "unpackedSize": 1504, "integrity": "sha512-HMueHLjiihXW3iq+iR/XecRtHrjgaQ62e6d69tm4QWjIdIxTyTBob4VdeLxqoD19SgjSg4XYBFcE6JRC1aYdFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDW5p+wqqKDpVblSmQzxezfqcQcietI1O/75nCwxg7+QQIhAOyqFIdkge9TRNrqWlkkX6Pq5BCb6jn2PcTJpsVOd8W0"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0-beta.50_1528832829027_0.7496892310541698"}, "_hasShrinkwrap": false}, "7.0.0-beta.51": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.51", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.51"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.51", "dist": {"shasum": "ce2675720cb41248c26433515c90c94b9d01a6fd", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.51.tgz", "fileCount": 5, "unpackedSize": 1518, "integrity": "sha512-CK+Ijawl0P2ZYVhjyosi/gAOddBwWaafEFT6TmzRYgo3wVBHspgmFdhoZ/j7sz0bfL/WevNrX8YqmTckKz8quw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD64efbuD4mdOHOq3EX5dOYw64Dyul/XQtVdus0vlp//wIgZI/RXmEm5yL074yaPfbWU89IUGLWTGkWt0uET36tiKw="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0-beta.51_1528838376975_0.4793704034897126"}, "_hasShrinkwrap": false}, "7.0.0-beta.52": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.52", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.52"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.52", "dist": {"shasum": "1e5a568cb477af25ee9a07f6c865b73b0533e9e9", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.52.tgz", "fileCount": 5, "unpackedSize": 1517, "integrity": "sha512-WR5puCpYcVZpYMDwDgAyVBt5763z0T1Z9R1rJCNxkt1ayQA+FTkUoaAfIbHJBfa2pO+4U/mNLMCYeReD8xKgwA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE+jU0auv5mZB7rccYh1hy45Qg6d6ISUw3RUy2i0m4DcAiAM8q00SqgU53ISAGPSWAcIF4OyMDemBvwRZaqdF09aPg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0-beta.52_1530838760918_0.19947377355994123"}, "_hasShrinkwrap": false}, "7.0.0-beta.53": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.53", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.53"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.53", "dist": {"shasum": "a5cf6ccc6aab369fc2ca57ae1f4a63b3dc3825eb", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.53.tgz", "fileCount": 5, "unpackedSize": 1517, "integrity": "sha512-pbtHwXzTNdoxjWzU9M0bkpU78EqhJ2SkDKQYBsMyogURlFNVmvnvh65fuD5hrnzptbqvIV0g8rOaxhL1kpQzww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDHZWjR/1KqrQ8lmWUZzB9ldRTQuu0e0i84lZxCyAcvuAiEA28IXjD4UoXupxNXaduQSJlTjky7+uq0vBwLSjpoYiMI="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0-beta.53_1531316411045_0.1532816373082424"}, "_hasShrinkwrap": false}, "7.0.0-beta.54": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.54", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.54"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.54", "dist": {"shasum": "2eb8ddde19ddf73a343d087a087159ed44e54809", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.54.tgz", "fileCount": 5, "unpackedSize": 1517, "integrity": "sha512-1SnhmwjFhdjKWgx2AQbPWsz6wir+XfZAm+Ilv38MZn6rTw6wTZOWoTEIq5+0gY03cglZhNTOWOo4DJjrflSzWg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCTWkBNI4xMS8OAwiq2nTL7cGhdlWHmsedvM1WXbEmaXwIhAN1Qrt2wUcAymvmO8sKMlOtVIpSA3KGsUsUO0kFlwaSu"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0-beta.54_1531764001908_0.8387247024162476"}, "_hasShrinkwrap": false}, "7.0.0-beta.55": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.55", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.55"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.55", "dist": {"shasum": "ef903fee2dbc3621773d7db2dec9861c8f976c12", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.55.tgz", "fileCount": 5, "unpackedSize": 1517, "integrity": "sha512-hKuv8SF3wjZpEkeB67AGzEtfKdnexuYokITPhZl0dpCLt0EodOv7S/X/FP22UocOOtd9RoJUAkp2weDBFpGgEw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCTw1h504kW0hKSKNdqgXt/rkYVgIB93rQjUu4cNYWvuwIgJsCxWVqB/auV15u2TpsYsQHdZQhknSlq8328+orTot8="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0-beta.55_1532815628189_0.47743162477239065"}, "_hasShrinkwrap": false}, "7.0.0-beta.56": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-beta.56", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-beta.56"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-beta.56", "dist": {"shasum": "3606e382384507b71adaf2f2f239395c5695ec2c", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-beta.56.tgz", "integrity": "sha512-rpXmUoqQRwV3QaRUIwKTrVh/pzYe1mMmV43TXJNkP3BX4phimxsF+/orJY8MjqZs9QfHwQkfyb+b6BURLY62kA==", "fileCount": 5, "unpackedSize": 1517, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPuzCRA9TVsSAnZWagAA5VcP+wa4xS0FJs2zJC3blbOZ\nYZ/L8QTTx74Cqs+djPJyx8naQwAlL86SbGR7GMbu3paLGhzGv+6NJf2zphNh\nvztu1ndA6kv0KsQ4Vk1jiGHfzpa4Aoad4jrNOq98K+NjD3xE9wSSJoY6BTgV\nmXv9sQP1zyNcZ4nlfZa4x4LEK22IhliKfvfprUFbtjcWdgD8eySwY0TJbP25\nrGF1wwcXVmZvMBenW0eRdt1QZnOXFS+iM/sXdHDdiYgcfiPRn0q39Urh3HOu\n6zxZbCset3YbdJ/34NrvwRFd5adrW+FSbJxNfOtAG47ll4eSnE6UYRycfnIo\nd2BDwL0rDu7/DrXoIB31e3ZyiqXlRgy2WUNoX7yyyZrpEXIXaWcKdb2+5MoP\nMIxuhZsHMow4A2/7XFeJ74o7YcTnOQygwjxxonFsrPuYvBH/1HhOjhwTOB4k\n/X/iED6dVZKb3ku3E5dQHvN8ik6TDl3sHl7X+OXZM5Z2OS7n3cU2u5x9u49s\ngVgs+hu83uHxYn+mBZjJr6Cmy8f4ExN0hmQpGk6SJ4BxfjDnT7mW1U8WEFxa\nZelp6DYal419ikgiLWfGAF24x2EWpa3wISqPo/H2UZzq5K54MlKxK5o1xNSI\n/C8DSWhrkY/rIIJXcddZdWMqPI2Dr1f6TP5Xh04ETDgtn0TMU5/mzuh4pglj\ngENW\r\n=nafX\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCF05Ft2f9mV58HwjM1aqVvjbbNDwZarUDTA9uSiGWppQIhAICw/QFauZZ/mSbtvSoECfX6756EOhzWqcmC6ZRE69/5"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0-beta.56_1533344691059_0.8738289388182459"}, "_hasShrinkwrap": false}, "7.0.0-rc.0": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-rc.0", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-rc.0"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-rc.0", "dist": {"shasum": "d105bdf34e56069e92a2a4992d8ec3b5fa12613f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-rc.0.tgz", "integrity": "sha512-5It5hyumWc2So6wVF5RDGkB53S8tTRuJL+mMGcp4FHajfQqNneR6pK33EDJX3UocLktVzMTSptKdAHHVmsdqDw==", "fileCount": 5, "unpackedSize": 1508, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGSACRA9TVsSAnZWagAAw+sP/jYSM8fnL8Bw1iO1Jb53\nFOInBiLNQtXHjJO3lCpFQlFU03ZS2RkjCrlWTJnPcJ8M5T8ru4WpPwHV64Ox\nPu37vKe+YzA30jia4u/OtP+ped/+tNVSmN9Bw3BPD1HwfqfmaxYUgDmG1c0e\njOIut5OwewMtkVg5Ih2VaL9btCac2mGnG1JfnYubV2e8JyKi3ORaVNz2yNBZ\nbHPorwPrX1LwKpyyJXjaGieTiC3Oxg6d/y7XlV0PSGCjemU8wFYkUB49s4Do\nON+V2Eaj9N+n5sgNzi0GwR9SFRwDcy072B/gQcBlb+QRExoLrCxj22sOimoq\n+9k/H9V9abHENrDZxYfgIEKI6PMYDH8ydibPPVpX2VyBnL/MvFO/dNEyvaWh\nit13IGHpRLEV/l0UTpQhg2GEGIuTJTHmAqYPPyMs/UfPc1OsAgiLd/qu4+yq\nN7PsNMYzwfwkN5FOtpSZytEaRiHTxrOsNW0UCjwr3wzSa0YH+kE4GUTukADl\nV84Md191jcHo3vt4HhJjCswepxsGNMgRzECJ57aepvzVhD6EV5lsxRKTMMMr\nD7kfQABvD1CjhGq1INHRIG5OsoLuhS8Qa6EmmsLJonxBJA49fbI9JSmkHCc6\nDJJk7S138E9ZRrsVZH5xXFo+5roPn6EsqLzbbv+j/fZ/oQiWL1yTRB8sxVFv\n2+qF\r\n=42qn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBBwUoLusVl4Aby7QuCKgLlBMZ1eVYnPXMnCkp+a3QXiAiEA57yym406IsJE3950yYG9/BW3gsaq/9+hctXN0Gr6uf4="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0-rc.0_1533830271673_0.6790381616636756"}, "_hasShrinkwrap": false}, "7.0.0-rc.1": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-rc.1", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.1"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-rc.1", "dist": {"shasum": "c125fedf2fe59e4b510c202b1a912634d896fbb8", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-rc.1.tgz", "integrity": "sha512-e4dGUnZGhg1LWTvyQ6/m8nKZ9bUrtPwl9M487CEVhTA5lVUvYxASHBCEtkVWPwT16NzcWlFR/PghsHeLFGIw7A==", "fileCount": 5, "unpackedSize": 1489, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ8GCRA9TVsSAnZWagAAyLQQAKLUED3KpoeAGwn5JCki\nVTPGsFCdxuh1YtLzpg8Xvx4IWzN435wzYSQThx/v+SBdyJtVkIDGgkAL1Q+P\nqD7BhJFyp4LwoBT0UaGjRaIFv616EsPjb76olFROFR2e9ej3G5ux5pqM5nKy\n6pRiwP31dj/uF9P6yfHcZziA0gt81LzWHip0PFSout3OIQcV2rwnA2g3hVLt\nupVmfYQ0R2qfC03KK3jRffDB70W54ovVUhUw/pUJ+rIt8eBu9k2UzIIN9/wT\n6WMcINNpYJcLARfIhWUKsjWuuKCMD6f+fJJrQAA3qytCHUTmR/YGDCWu+Az8\n7op9aqUVe0JJ9mOg54seSVzemrhOnzsSVKCNppKLe+cc7BBYrE9V4WSEVx7+\nWvR+22hunx269txi0E2ghZRzC0tSLNDPGYkQoTvmf1Nf8OF+SWT8xK75+jru\n6e0P9Ubk3TPRWtu3ziBultRzKPwCpSWXtZzk3ZTfQMTII7J4Q6Sjw+sHrSuz\n3Piwohq4Isobo6AixhPeM/tBp1Yxm9roTAtDnujjutVHBkjBA2sNao7Djnz7\n7fVPXuo1arOr+NqOMcbqeT3dudDwpciWhLcPuudumePFSY9jqTbIWfuyLP3m\nxsoL/gHqe95IQBlanQ/6R0gBf0cfXras4Wz2lArKyxjnZNG3Z767NLPQqbLk\n3Rrc\r\n=niFt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAH10dcZp8eTLpNDJgBXOxIkcvtV7eHv/REA+JYsDVnFAiEAnXnBWfvPUJDqUjK5DvGxBx4rQU7vZhZXuUyNmyXitHI="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0-rc.1_1533845254340_0.38890355840894286"}, "_hasShrinkwrap": false}, "7.0.0-rc.2": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-rc.2", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.2"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-rc.2", "dist": {"shasum": "8c752fe1a79490682a32836cefe03c3bd49d2180", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-rc.2.tgz", "integrity": "sha512-uZyLBqAFX4142Thord85PcTxD3nTiQE+0BiBL1rHMGc7ln63Auj/EJoWdYa9reb7Bax7OcuwY1lhDbxrgKUYiA==", "fileCount": 5, "unpackedSize": 1489, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGayCRA9TVsSAnZWagAAR9UP/j1Pi0okekXDCb4Z/1vC\nPe90A2VOrwVyCAv893LCGQpkiFhZ3DcgnX1Afzqblpk6iPziDjUPEM2N/5HV\nvhh4bnLZHO4M+rrRxjrSDzrO3vpyG17UZ8/W+B8CFz37B21CGBuIpz71LXwu\nwzW+Y/MjhM6yt0YlJllwrHdnvYMVJ0i6N5wb/+mewtalgdTEbWXlCo6hw7WQ\nxPR9pS4YoWT6Ur67fSX8s00aMRpFvD59w0CmmhukdraQphUTpFYF0VB9IlNB\nGoUzGIq/3OskBjfwex+IEV+PfwGKD8lcaP9hrGD2yDmwLxPZJ7QDK9oeDcsh\nYwKY0COMtZulcqkMTslj4y/F4qESWFCvoOc4rcP6JpC7hQHp9XnLPkcyGeBp\nle/lCfa4todNXmiDc/qnw8B/tR/kocUMFavlRXxWkBtXuB6po4trDVoPpSrQ\nd0W/WyNDyQq/LygJRnrDX3vSuzVOKQwS0KJPd5ldXzCbqqVxSZT++ox1o3Ko\n3FnI3uHSzlEhcSrmTjQIRMVQV94k0cL3daCeU3F0u69yJ8Viy0eJMltanOEh\nmJpBEKKu2FZUlyCjQHqj/dk/HY+KiC/PNgkPIGjvdh0ZYoRS8pte0FbLmLFk\nq9/0bEKozX5cOJ75s/IIkJVNtQmcpE5YnWwXBA3hD8SSV6yXi5HxwO+Jh5eD\n32+E\r\n=9SpG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDGbMmvIIzIrCRjOYjX4WMdiIeW+e57gi0r2tS+CuHEOQIgXY6LOxDU5tZCmS+Q6FLReI2ra7+5UPdKR+jrTUfQ0KQ="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0-rc.2_1534879410222_0.08628330218624836"}, "_hasShrinkwrap": false}, "7.0.0-rc.3": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-rc.3", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "7.0.0-rc.3"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-rc.3", "dist": {"shasum": "5fe5ed7c95a98e2e0b8fc38c4c64904045580ae3", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-rc.3.tgz", "integrity": "sha512-0NT804rUoHUBO9P9eEK9HqWm0vjj/5ddia8M67SAJITN7Io+vo3prNW2Gd6w3UHpUqP4tI6g/Ync6VlEyH0iwA==", "fileCount": 6, "unpackedSize": 2588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgElfCRA9TVsSAnZWagAA9W8P/0YmlxvLgyypIlWg1V/A\n5bSWTVF2yO8t52rmtdZTORnBSLs6llT1AuvO9BFw28uQaTtxaiwEfI4GSNI0\nWOHIlxPA/LwWb8lrOCqgxVjblSqZomgA/in6pODnw9EhIzQzlQTAa/7jmB7L\nI2Aej306rkoZo+Xz7dDN/IqYOXsIsHSK49V3HhvYqIW4z6WutD8Oh8alU6Em\nQfU36SYsDpwArF/ERi1LqbAMI0LIvdhv1sc3EieghuL50v5/QxwvsFI8D6hA\nYV64nafvYxLGjoFjMYPQoCiOn7YwDYefrSpYz05V/gi4AmYM5CIWWmPvSgID\nirgLhRagRhSlQXv/YMMgREYA8B3i65Yz98MYnla2dWqHsRmJokJgg3M2/5RR\nilKPYDzvLjvED6We9bqmkg63rifs7yvDhL/7Ru9/wr7qQO/PEmUHzQ49mRPC\nMBhVC9epn7aE3JXBjpYG00Z5a5msQQfKW/CorLiBC1p9hyJ7WO0MffhkHcIv\nIkKMkGfK7jG0fUTh6Bg8PRmB/SymruMt7RE3Jq3jFNB8HGbeiEpT9SxZlSDF\nBrAYvhuOGj2rqsYIVa3r3tR2t1S0t05jZ+ZeTKnljeXSlH5I7JnqAvdqBBSd\nGEFCbWz+3DkijkklgNG5bVT9m4MGMSUz+AfZsk1DNyOTPvkNnppyWeCfKRPH\nVeYp\r\n=5Nym\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDpskYtTGIRSHuug4YcUwWJnZPNdjxgj9YZLWaixKkeFAIgNSLFXR0YIOvB+EwjmPz9qtzWLkwAvaPxgy+RbyutiBQ="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0-rc.3_1535134047272_0.2925423146788042"}, "_hasShrinkwrap": false}, "7.0.0-rc.4": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0-rc.4", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0-rc.4"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0-rc.4", "dist": {"shasum": "3c5f2005549dd7133ea1ad44f4bc021e8c87673f", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0-rc.4.tgz", "integrity": "sha512-+XP7AFrxuZ/LzCXXyWYmarSBavNnC01KJue6Sfk9hn7KGW3zynGLAFvZThCM2wPR+hMakZlQlu5lkbNopOlTDg==", "fileCount": 6, "unpackedSize": 2590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCo/CRA9TVsSAnZWagAAAJoP/0L4gcZwjkzif8A1oItt\n+ihvhNprGW92X6DnkDLaTmnvqtrqx9GVYJOirmwCDtIySkTgHeudv/oRKx+r\nGg65DIiav/wj6g1FKlavrbwg+tKOjv+PmR39/GXjgXDtT2ZPr8zIZD8mRxW+\nuC7dqCFS/Sl8Cz7nSHSty823hGxpT/qhYU67NxTcTqmzMHVnx1jf0+c2BrLd\nglwkYm0vEBvYJLUvu0Ij1bIKafp+xhl8O3/m4EtlY48IGV5uGFx8MP+Lxx9X\nx/1ZlQioe1awffZUVrkzEXzyE99tybLw5d44baf7JlZjBainx5SPDmBwT9Hp\nMACxtiRhC4FG3ZPSFQaAFkzan+JgDOcKZhf312VJ9ssYlLihwElQ7ojCGmVg\n86X9/F8fuWX4DJ5n3QD9ZtG25YbNU4qVSFSbTobZD2kqT2C577duoznWV+Tr\nhSUNF/H7odIV6TI4VLOa+i4bnEg2GYX9lbmaC0m7LUK6lu0ER6GXfJswoP0Q\nPgFz1kWu+THxBllE9nrM9sQEvW7e7ro1xxYG3NqTT30tD2x/GAH4dq66SbxN\nr3rIcU5XER/rvdnzeyPGBxOhUlXxOGz5jodEoNlavVYdJ9z/b37ge+SFD247\nqPBdDGg8UiKznLv1TcebSNrXQR5olX4Q62DZU4D13hUbYZ2cthkvhNmJzS57\n0mxv\r\n=kfnT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDadCEg/wBp0McAzT6/3NOv8uwAuFiK/3ylMYPPKOUg5AiAG9nxh6FG1M5aq8ennjDLY1AYIM4CjmjOmZfdgSIzDxg=="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0-rc.4_1535388223255_0.7217426051438196"}, "_hasShrinkwrap": false}, "7.0.0": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.0.0", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.0.0"}, "readmeFilename": "README.md", "readme": "# @babel/plugin-syntax-optional-catch-binding\n\n> Allow parsing of optional catch bindings\n\nSee our website [@babel/plugin-syntax-optional-catch-binding](https://babeljs.io/docs/en/next/babel-plugin-syntax-optional-catch-binding.html) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-syntax-optional-catch-binding\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-syntax-optional-catch-binding --dev\n```\n", "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-optional-catch-binding@7.0.0", "dist": {"shasum": "886f72008b3a8b185977f7cb70713b45e51ee475", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.0.0.tgz", "integrity": "sha512-Wc+HVvwjcq5qBg1w5RG9o9RVzmCaAg/Vp0erHCKpAYV8La6I94o4GQAmFYNmkzoMO6gzoOSulpKeSSz6mPEoZw==", "fileCount": 6, "unpackedSize": 2575, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHBbCRA9TVsSAnZWagAAw2MP/1kwtY/g3Wz0xpSvMYld\nrVQ3eLU/6VCX5r2883cAek0lGFRjONtaV4nChLgaHq7QX34Dey8w/W3nlp6x\nRYQerr1iI6VJrVGJgIMhPtAW+DWfy9H/RGMvBXBMa32/1BMAAL1GrR3gmrff\nCGHFVJu+Zw2otrzA5Mce3g8ktiJ0gHqFlSyxiHkOQC15GAj7oyL8dfTzV6cH\ntAwLrXvXNQ+9OldDZZVdFT76dfW5/6mcvrh1AbdOtRUEnDMFCPyDegCDxe6e\nqgAZu1fEtOLpbAsMuQoWA110WybfOhW6qh7oCey5Z1n0r2JjCVFGh1ebqB3n\nXTX/zVfvajVboTTa8A/b25XNDYyEwN6Gq62IUoYRteTKJEcFT6iPIoDn2lib\nEi7XVQygcrsIvEQ/OdPrrQFwTLCBEBfpv8DkjmCPFtD8yETp7JrpUhGsb0ck\n5ogtZ5j0+plQua4GWZR1ecF67z4/2LSVUEy+OzB4YNQY16a2WwM4idARZ29/\nWJGM41n6b5zlgoQAbWH9ClN/VMalQGGvXvhug9B9dBkRo3NFwJvnmvULLIku\njVtdfGJPkkTQQZqVarEDgl+lBnMywyhsqhJPsaaDzIrQxjNJABQVZrcecdal\nl3San29sTBYOug07qpGKxAvWnA29Umr252aGY65+RSsVgK84sditkAqs+aXU\nt2WZ\r\n=CpfY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAmzKHs8m1JsIkjrKGOfeHMOyqryZ18rMLgT6FkXLNgxAiEAtqr1iR6ID5LQw1O04pz+rLk6NQm8YaPObxWQN6t2No0="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "xtuc"}], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.0.0_1535406171186_0.5741431810768873"}, "_hasShrinkwrap": false}, "7.2.0": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.2.0", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.2.0"}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "_id": "@babel/plugin-syntax-optional-catch-binding@7.2.0", "dist": {"shasum": "a94013d6eda8908dfe6a477e7f9eda85656ecf5c", "integrity": "sha512-bDe4xKNhb0LI7IvZHiA13kff0KEfaGX/Hv4lMA9+7TEc63hMNvfKo6ZFpXhKuEp+II/q35Gc4NoMeDZyaUbj9w==", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.2.0.tgz", "fileCount": 6, "unpackedSize": 2672, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX1tCRA9TVsSAnZWagAAih4P/1Fth7kjJWKZSevH9dui\nZ2Rg5GuAMRUfevGNYPn1TGSNmHNAYKxpeNG6D9Yrk4DUhuLKr1aDGZ49se+3\nuTa/znA5SEGfhrvWXFYef+QPscOw6pNxXrd0FkQ+o1Kf3NrK3HTyYETp5euT\npXlhYZ19nTNfkO+Q5YnQ6YC5yqxzegtrbEivTmczDH/ywp+3HlEXDXouPrHo\ndBHQcvdNLMQ+HkNyRRgaHWpWz/TfsM8keiRB58zLaBnymyfzpc2a1h+UdnKk\nswoJ97rihwrPgJri4rcmXSKI97pJcYev2QLcrbHjTuhUJUJotnoXjy1aKryV\nnKG+tH8tSSB6FS7SRQDq0X0RzF7EarmHjShA0JT//WEX4sEMAYu/xPG3xZZG\n+oa4MRv1579V3CVnvUijtmpxHkpM4JFxoaNutbco+SgHLqr6EHtYz7JD9ptN\nA6ffWBdeS+nr5g8IZHvblgZo+tJ+fGzzxaFv0ckBDAjJae4dvUnHH920/ilq\nb4J+xrV3IYhqlgEV6jDOG96fY27FA4H2yE/mONjjJgEXR4mnjDMVL53IF+BN\nktyNV2ZiwODnP8t0bfECc2z9xIfcxzpYFtKzzbM5BYUe2A6qFeKW7iabx4X6\nAFmaYpuD7/isYoirv29Bfasn+3a0/Vl1gbscX3Il1pfRTPoqWKHYMU4GV9C5\nBcgC\r\n=Adm0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDlHrTqYszhJGEYR4tp5obx3PRcFBp3u6Adb30QaBfjOwIhAMoSmdkct0p3DjcQ+j/QJq8eRj0FBjMJ3bQjChDGfIlO"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.2.0_1543863660517_0.2004554657331059"}, "_hasShrinkwrap": false}, "7.7.4": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.7.4", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.7.4"}, "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_id": "@babel/plugin-syntax-optional-catch-binding@7.7.4", "_nodeVersion": "13.1.0", "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "dist": {"integrity": "sha512-4ZSuzWgFxqHRE31Glu+fEr/MirNZOMYmD/0BhBWyLyOOQz/gTAl7QmWm2hX1QxEIXsr2vkdlwxIzTyiYRC4xcQ==", "shasum": "a3e38f59f4b6233867b4a92dcb0ee05b2c334aa6", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.7.4.tgz", "fileCount": 4, "unpackedSize": 2630, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/tCRA9TVsSAnZWagAAnqIP/REXY8hreIN+2WdOuleY\nsPy3euUZHiN6JXhTlBYnNsvLOiAmBSRfG+wLfC9kz+GsQkaJ2zV6aiidM1V2\nyPcQzyZNfR2va+wM6VYTzd2916Ft1zTIwl63OumJLhOeOxfkrO1KC5zihqpD\nVDH2kKjZAtE0J9r7jLPS2vl/6NEr7yWdGjy+gG6S894EN1NrfRIAqBDk7XXb\nZugdUKib/dc0HSkYyOy+fjc9wRkQ1qiT9kPxf3YwvQRnTgwMwWSL2mkBrLlY\n2OlBj/ldJ9vVFS7vnEmVw0ZLQEACxMl+95S5U3VHF7mHlhEJqjWOR4xBcS00\nRP7dPUY5YXDAl3NdJvTypS0VxOMoL5QMOgX3LfV/1WOAH3S5Xk79S+Pi9dqI\nLDfxw/0jJ9RgT0l7bZe9rmnM9JlfOUWMrQkv4bsad42wJttiSdaJvW3ykHPB\nW2RIR2K1kMkInvp1s2Qluzmi+MK9qsk6eC2vhI53vlDvicwudC+mPM3D4YtJ\n5k20faGj71EttlnVp+52L0WmBvney378PSNoZuIO4y1jO1Vyl7Z6zQasdcCx\nVjXe1g7SWzLwX57lybp8iaTvDSrerLJioy0xElmgv9d88q9sVoEEHJszKvN4\nuzCR5f5e/xghnFCcLhc6iLHRTXbp6857FmnOg+eoKw+9lxkSHcNO6BhTuVWS\n1Jzg\r\n=UkTO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD6px5JqV9bwLjRo8kl9AP9RSKSVV4F+FkxqfUaqxEl3gIgeyB+AlSEQce4cO3dgvoaTbJbNo1fC8/4SV+19cgiFsg="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.7.4_1574465516860_0.864787874183218"}, "_hasShrinkwrap": false}, "7.8.0": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.8.0", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "type": "commonjs", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0"}, "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_id": "@babel/plugin-syntax-optional-catch-binding@7.8.0", "_nodeVersion": "13.6.0", "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "dist": {"integrity": "sha512-EIgJVy+u1RvR2gJfX4ReLwAupO/twllUue1wPrRxhu18+eC3bGTEcOSXLQdaE9ya9NG1rE0eQs0GSiloUGFEwg==", "shasum": "180c7bdd6b7fd81cc6d18269de12d5ddd60cabce", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.0.tgz", "fileCount": 4, "unpackedSize": 2652, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVTCRA9TVsSAnZWagAATUcP/RTnQdSLd68exmajxdyf\nhQyMWUxXL8L9so1pqfcW43uClOponpnVDrmsvf17AfcN6XRGZuhDVCukkS6m\nzAz/OXYOeQ7LGbi/+godTp7q9UM8h+zD6NO/g+CZgdser9883rPzga83OneC\nGg/n2a65OgoeekQkIa7ra5gr2CbusjPifsa3ysHsV99FYGgIpnfxzpDhCtvM\ngQMZPjOrgIGqFK8POBi1mO/qrNwzQr/UXXTrYVWNa9Ce8tJGH2dTWPIAXck0\nMZOod3SoDcKQVKPhkaLMo0FTciVSokWtaWyRHV0QNES2sWilrFN3XQrPhhaL\nV5kgnCAPmaBPYz+opWRwTgMjmA3Zdlp+jU9avHSMfSjgPbNhbBwPTeKfOCCq\nk8LUhkDXIF945uFHfHPmHxOnJIjYqkkMwHw1oE8X70BjZIM3KOO2lD1NpHYl\nUkA3RFIvSa1oIqDmvRAVDNkr2neMdfz9pcr0z4qmJ5kdxJLMzi1Dulr416AA\n/pV6rWpHdFXekbzhnTGNpXec2NuA3dOTP4P6t7gIzhSLjQv6NfAn/dbpYLDy\ndZAhaTGxuvChB4a80O0Pz0y5Ckgi7YePyEvCGCy8tTMIhaiG4s3yAw5YrjCb\nV/v/qTalxsOX/lxBM9Py/VEKXTxxRmyHEuPLR8JwGIcvFOJ/zpm0l7dAQhEv\nCoGz\r\n=zLmL\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHzF7FGeaMUXxZE25LElZiJW25l17EUc/f74zuxwREEUAiEAtUgilc3rwC+bgHsXzXU+bX/3LKjh3rLG1lWv2VTES5k="}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.8.0_1578788179589_0.5625115715111464"}, "_hasShrinkwrap": false}, "7.8.3": {"name": "@babel/plugin-syntax-optional-catch-binding", "version": "7.8.3", "description": "Allow parsing of optional catch bindings", "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.8.0"}, "_id": "@babel/plugin-syntax-optional-catch-binding@7.8.3", "_nodeVersion": "13.6.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==", "shasum": "6111a265bcfb020eb9efd0fdfd7d26402b9ed6c1", "tarball": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz", "fileCount": 4, "unpackedSize": 2573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHN23CRA9TVsSAnZWagAAETkP/3M4CMZ/D/tMXEvByfcG\na3zuoNS6lEXRu1hmTJLnRJj5wshKvLh+TT5Hln2eYTXw0K7yqcYbSY4BloCL\nL8MZ7uKFQSpIPIbei43K3UbWkQvmt68fgWgJ7pnHmz/eZr8BeXI7wWNnxNaa\nNyq2U3E2enxWLMdnNiAi13guccoY3pz2k/Nt2CTeMvqgwkHyfVWAhYlJV0mx\n+JW+l0FSqG7VXM7xWybvo8ijMzgyzuIqGA3GhQ41V4hzhnX0jVZsYdiKpi+U\n6Xc3H9O7aHM10a10VD6DvRX/r56oN5ZzgUN9zC6p3aFnAymQg8F7+SyHyUIj\niSDCSW8p/V0lqeuCBshipfczDWBfwXdMl1DtEmmciiTDs7gnZ9eXg9/EX5Io\nr2lbrObXeNIyMoKNQ+2BL2lARudAZ0yJIg+Z3GCjKdB1r5kbe7vIqfiSj0qA\nKKCj+dXDGI9mvyGf3XiCUeujI3sWc1/1McJhJPx5Ih8jXtMnOhVqgdutRFNw\n1Vu/IkxV7m3smgameUvANJeOYrikERqCOIbYAlnc0nnrQLBk7Wl+mQVsIjjF\nZ+mYhbElz8hwKNgJgj5di0mv5H8cn7S2J5kghhH69/fch3zW5Ucqy0+TIF8K\nNIRMGza5FM+rZhvTD4a/3VAzKHU1AaFade2PPdjmmjvVI1SdSR3snqX2cV2H\njEm3\r\n=Q5rd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDn6W5IHrNagH/2YD/S5vgJWX7KozhEA5ZU0NqNNfYGIgIhAKi4T25QDC7VIoK/LEhDd2ZvA8xCkon7WdFYj2yPTDoS"}]}, "maintainers": [{"email": "<EMAIL>", "name": "danez"}, {"email": "<EMAIL>", "name": "developit"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "<PERSON>an<PERSON><PERSON><PERSON>"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}], "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/plugin-syntax-optional-catch-binding_7.8.3_1578950070654_0.7933307626862351"}, "_hasShrinkwrap": false}}, "readme": "# @babel/plugin-syntax-optional-catch-binding\n\n> Allow parsing of optional catch bindings\n\nSee our website [@babel/plugin-syntax-optional-catch-binding](https://babeljs.io/docs/en/next/babel-plugin-syntax-optional-catch-binding.html) for more information.\n\n## Install\n\nUsing npm:\n\n```sh\nnpm install --save-dev @babel/plugin-syntax-optional-catch-binding\n```\n\nor using yarn:\n\n```sh\nyarn add @babel/plugin-syntax-optional-catch-binding --dev\n```\n", "maintainers": [{"email": "<EMAIL>", "name": "hzoo"}, {"email": "<EMAIL>", "name": "existentialism"}, {"email": "<EMAIL>", "name": "nicolo-ribaudo"}, {"email": "<EMAIL>", "name": "jlhwung"}], "time": {"modified": "2022-06-12T15:01:04.521Z", "created": "2017-10-30T18:34:25.297Z", "7.0.0-beta.4": "2017-10-30T18:34:25.297Z", "7.0.0-beta.5": "2017-10-30T20:56:07.456Z", "7.0.0-beta.31": "2017-11-03T20:03:18.120Z", "7.0.0-beta.32": "2017-11-12T13:33:06.783Z", "7.0.0-beta.33": "2017-12-01T14:28:10.054Z", "7.0.0-beta.34": "2017-12-02T14:39:09.693Z", "7.0.0-beta.35": "2017-12-14T21:47:37.588Z", "7.0.0-beta.36": "2017-12-25T19:04:27.529Z", "7.0.0-beta.37": "2018-01-08T16:02:24.570Z", "7.0.0-beta.38": "2018-01-17T16:31:45.321Z", "7.0.0-beta.39": "2018-01-30T20:27:28.210Z", "7.0.0-beta.40": "2018-02-12T16:41:26.534Z", "7.0.0-beta.41": "2018-03-14T16:25:58.600Z", "7.0.0-beta.42": "2018-03-15T20:50:33.179Z", "7.0.0-beta.43": "2018-04-02T16:48:18.749Z", "7.0.0-beta.44": "2018-04-02T22:20:01.127Z", "7.0.0-beta.45": "2018-04-23T01:56:26.828Z", "7.0.0-beta.46": "2018-04-23T04:30:52.481Z", "7.0.0-beta.47": "2018-05-15T00:08:32.327Z", "7.0.0-beta.48": "2018-05-24T19:21:52.579Z", "7.0.0-beta.49": "2018-05-25T16:01:33.829Z", "7.0.0-beta.50": "2018-06-12T19:47:09.123Z", "7.0.0-beta.51": "2018-06-12T21:19:37.028Z", "7.0.0-beta.52": "2018-07-06T00:59:20.984Z", "7.0.0-beta.53": "2018-07-11T13:40:11.115Z", "7.0.0-beta.54": "2018-07-16T18:00:01.984Z", "7.0.0-beta.55": "2018-07-28T22:07:08.258Z", "7.0.0-beta.56": "2018-08-04T01:04:51.168Z", "7.0.0-rc.0": "2018-08-09T15:57:51.760Z", "7.0.0-rc.1": "2018-08-09T20:07:34.406Z", "7.0.0-rc.2": "2018-08-21T19:23:30.270Z", "7.0.0-rc.3": "2018-08-24T18:07:27.344Z", "7.0.0-rc.4": "2018-08-27T16:43:43.330Z", "7.0.0": "2018-08-27T21:42:51.273Z", "7.2.0": "2018-12-03T19:01:00.655Z", "7.7.4": "2019-11-22T23:31:56.991Z", "7.8.0": "2020-01-12T00:16:19.723Z", "7.8.3": "2020-01-13T21:14:30.870Z"}, "keywords": ["babel-plugin"], "repository": {"type": "git", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding"}, "license": "MIT", "readmeFilename": "README.md"}