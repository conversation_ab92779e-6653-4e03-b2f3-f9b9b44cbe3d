{"_id": "@sinonjs/commons", "_rev": "29-ed060175eb78017c18524f2f7843fb70", "name": "@sinonjs/commons", "dist-tags": {"latest": "4.0.0-alpha.0"}, "versions": {"1.0.0": {"name": "@sinonjs/commons", "version": "1.0.0", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "scripts": {"lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.js": "eslint"}, "devDependencies": {"@sinonjs/referee": "2.3.0", "eslint": "5.2.0", "eslint-config-prettier": "2.9.0", "eslint-config-sinon": "1.0.3", "eslint-plugin-ie11": "1.0.0", "eslint-plugin-local-rules": "0.1.0", "eslint-plugin-mocha": "5.1.0", "eslint-plugin-prettier": "2.6.2", "husky": "0.14.3", "jsverify": "0.8.3", "lint-staged": "7.2.0", "mocha": "5.2.0", "nyc": "12.0.2", "prettier": "1.14.0"}, "gitHead": "92e0841cf5c4a6661ac16f7b0ed4d571e7bd861b", "_id": "@sinonjs/commons@1.0.0", "_npmVersion": "6.2.0", "_nodeVersion": "10.7.0", "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-P4ISdVzg6bpdo4ujKfx0gKbqSTkaYFqDJf89S8SMhFDGniI7wClKk1eOFHz0R273McF0hpDsykR1/y+IrJ3T2w==", "shasum": "9a805dab8ce90b4fd3b66f9fc19856503d8d0639", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.0.0.tgz", "fileCount": 23, "unpackedSize": 14699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbY/sGCRA9TVsSAnZWagAA9AkP/3Q8TA17Vp03dytz6xoF\nfF7nFRZzNB9RW9mc7cHWP7NmMMr/YeH54Ze8m97y942Z4sUsKBCbAtl5ZIUa\nxcMOxUO1qbEChlx09AbMJ3t2liJFfudIpAFAUj/WCMIIreB4NOuQKAFaOOJe\nW3Wzr8TpePCLUSPvL/8VKNWGix7Y4S40IOHYNHNAgt2aJwBjpUHbc/Rq2Yx9\nN0U/8Mj3Gw/Z88Cko1YXzNwRAmJwooT73LMgVhj8YtMgoczjZnOL7GdYNj8m\nxNFop0mgOISdJOe9x5BK33ltSt19whkKO8sNBq6v4tdOEALnfc807i6a9j9k\nVxC49Ad5vzNJTFmgddYA+p6DwKpfEdtcLwLJ/ix4+beqtebeZhUxwKV1kfb+\nIXpW1IFjvzdpgLPzkU7JKF8N3HNFYqMnQWGOycu1eEBlkC3tnfBUrsNrD4PB\nkxJD+rZd5h6FMZRUjR7r4vZD5zSoPA4cmcs//ryqw1MMu8Pc5WJZiNFOYjLu\naQGlLH6qyFx4ua2CpCBK6Y/9FxE7AYcRwGxzk5JevtnbJovlAroYc4DS2rDw\np8DGWVB8ALlIZKcDgMkHRvujCyVzzsH2ExfUwJ4DL/RB++CV2erH8QwR0erZ\nz6KU6d3bIl81P9vY6Qi8NdgsVY1BSmXMW9GRRvYxEHP6jRpqXF+cJ86elfVN\nebx4\r\n=lOux\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEbt+Dqg3yYOxjB806Bm/Hs5rV3pHpcBY3DhVPQDYDhqAiEAgOpTU2VAp6VOLC2fOOJdD0DHOncHiM5CLbRKK7s05aw="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "peer5", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_1.0.0_1533278981477_0.4296158569646138"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "@sinonjs/commons", "version": "1.0.1", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "scripts": {"lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.js": "eslint"}, "devDependencies": {"@sinonjs/referee": "2.3.0", "eslint": "5.2.0", "eslint-config-prettier": "2.9.0", "eslint-config-sinon": "1.0.3", "eslint-plugin-ie11": "1.0.0", "eslint-plugin-local-rules": "0.1.0", "eslint-plugin-mocha": "5.1.0", "eslint-plugin-prettier": "2.6.2", "husky": "0.14.3", "jsverify": "0.8.3", "lint-staged": "7.2.0", "mocha": "5.2.0", "nyc": "12.0.2", "prettier": "1.14.0"}, "gitHead": "3a5b1ab3681ed5afd683cf2bdc3054eaaad53fed", "_id": "@sinonjs/commons@1.0.1", "_npmVersion": "6.2.0", "_nodeVersion": "10.7.0", "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-y<PERSON><PERSON><PERSON>/Ng24V/E2jM56qoF6Guqntdw69wgh6IcL5WwuO9aDQb5nUBSrudPaaI9RE8QEMSF/Wvhhg2FZd/frnuQ==", "shasum": "405aa1117ce5005391257c11284084db2e14b0e6", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.0.1.tgz", "fileCount": 23, "unpackedSize": 14877, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZqupCRA9TVsSAnZWagAAYTgQAIcIkd3nk24DB7eSS3ik\nWbQHYuQsrA4e4EgrfyhFIYGiMjjKJGyZpmQGaF1vZPHBPnG+ubt9v+9xjgr9\nkhL2g9ZLc9GHEnDSbTB1d30rU/DXQ0gzS4at8wE2yfHPkhSs5+7yGqxNxrRc\nIXcOZDJqMz9w1k5TTjXNs2InR6NX/LP/nNBW8SP2FliMKSmfouTWkLGnyvC2\n45VHrlGf8EXDEtWB1HIPeGLoQTe4NrOQ2SyduZc5Nhxu4kE6zV69eHPAB493\nqGrYmr42/XvogAdGJVAHUvx0vDMS5yjBJPfY3U/2TiuHWotii/7a8diXwQOp\nWM1J9Iq9yuLlHlXr2pdpcVUdDl4a5TwCzRTKWLdcw/VyTCBi9mucloT0od+V\n3u2Gfh84Cald8YEFaFQilRjmPdjkA/lvufnEME+z2OKJEON0vcHN8DxnRe8j\nUg4WMO1POOgGUu7HqNHjzLsfi8eMSXjMd7uF1YAtrPYbdlon8o5VQm7nCFJS\nE4xQqdMHgf5dXY2xGROTKShh0iTe29J1ZbAqk3w5mOTZGfpn/esyYdURVp23\ndLbahgANZloapzz892D9JOG1nDPL4rJ5fijwuogFSRZa+JZNgjB59wR+fSAr\nDfH77iO3TbgCjWsyzopE7hzpImB3oHcFgPuVDeeEaYzj6liEzeFDRlizeGZ9\nZ/Tw\r\n=VbSB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIApFNKmzAJQswrbhpb/vzRPg3Znn5Rw44BMYCriIlq3sAiEAuKVJCccVVYLKc03Xe5UPLkiFryVb7cAaqXbomU7LXgw="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "peer5", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_1.0.1_1533455273479_0.5022031399679285"}, "_hasShrinkwrap": false}, "1.0.2": {"name": "@sinonjs/commons", "version": "1.0.2", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "scripts": {"lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.js": "eslint"}, "devDependencies": {"@sinonjs/referee-sinon": "4.1.0", "eslint": "5.2.0", "eslint-config-prettier": "2.9.0", "eslint-config-sinon": "1.0.3", "eslint-plugin-ie11": "1.0.0", "eslint-plugin-local-rules": "0.1.0", "eslint-plugin-mocha": "5.1.0", "eslint-plugin-prettier": "2.6.2", "husky": "0.14.3", "jsverify": "0.8.3", "lint-staged": "7.2.0", "mocha": "5.2.0", "nyc": "12.0.2", "prettier": "1.14.0"}, "dependencies": {"type-detect": "4.0.8"}, "gitHead": "64738e7d7ff02382cf8fbfb4271f632e4f0c0d7a", "_id": "@sinonjs/commons@1.0.2", "_npmVersion": "6.2.0", "_nodeVersion": "10.7.0", "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-WR3dlgqJP4QNrLC4iXN/5/2WaLQQ0VijOOkmflqFGVJ6wLEpbSjo7c0ZeGIdtY8Crk7xBBp87sM6+Mkerz7alw==", "shasum": "3e0ac737781627b8844257fadc3d803997d0526e", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.0.2.tgz", "fileCount": 27, "unpackedSize": 18717, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJba+4OCRA9TVsSAnZWagAApmwP/i9Ys6rKemq6fg/ZZNU5\nIxNYAAEdzhOKsb+1JONmM5Z4h++kxRBcPUK3XF9KeSG9n8CbIJHwe21uL6m7\nGvSpVUO8xTAqSdxjqqcs7220t3375MruY/k/CMMs2zfB3GzMxbdx4rZvCfGe\nyUUErPuDaaJpCPzMKbcxTc8s4iLEhtU75NlGZhHBVihrhpIeBdA9usI5qTcF\nomueDbvMe5ReuUG1lh3uKYt4X/U9clUJUAvpcWNcskT4v+7+qucicGMT2I4B\ngXvnKxeOdA93IwSQT2aQ5IWf3OCxOkNCPNC+iDrGgN6P3s6t8kb8teRhzz1U\nBZA1rwVCBzdVYZdl8jwZMSBSCGL15TPSfRNjWQWRZe4Z/THLwRIJLiQ1hDTx\nqNEskORhWOxmsqQ3l9gB3G1RUnvqqfoNJ02bXk9IOOCxMDqp9LMYk+/vGruf\nZ4YJK4xP/448sJNMDoExvlVwEsC2BOkWKhnMtmOzaF85VGunsJ9yxQyX1q2j\ngOg5wqmatdozpy1FShym7PjNvlqBlApfhRhzzvtuZunj1TC3vuJoq/kS+rvk\nHcZNz/qIy7D/BNjNBmf7s0sH5CK2XbQ3Ask1r7V/ScfHZ9xRq+i+mDnCOsD+\nyTAhCcdyRr0h079sOS/P9BuWzjl8LvWj9Sc8ZWxAGv5Esq7ZLrlUwsDSaAJs\n+/J5\r\n=MS7X\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCQHXa4wULhx9Kn6BZ48pHMnpz7cBkQ7sQbiCpgXkDthAIhANFwY9oJMdW3QbaIq67vlcIN2OoJ1M99cChX3mSXX84b"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "peer5", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_1.0.2_1533799949583_0.5275886693694376"}, "_hasShrinkwrap": false}, "1.1.0": {"name": "@sinonjs/commons", "version": "1.1.0", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "scripts": {"lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.js": "eslint"}, "devDependencies": {"@sinonjs/referee-sinon": "4.1.0", "eslint": "^5.8.0", "eslint-config-prettier": "^3.1.0", "eslint-config-sinon": "^2.0.0", "eslint-plugin-ie11": "^1.0.0", "eslint-plugin-local-rules": "^0.1.0", "eslint-plugin-mocha": "^5.2.0", "eslint-plugin-prettier": "^3.0.0", "husky": "0.14.3", "jsverify": "0.8.3", "knuth-shuffle": "^1.0.8", "lint-staged": "7.2.0", "mocha": "5.2.0", "nyc": "12.0.2", "prettier": "^1.14.3"}, "dependencies": {"type-detect": "4.0.8"}, "gitHead": "93d45929c7f9947421ed9b17a9f5b743c0f138f2", "_id": "@sinonjs/commons@1.1.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.7.0", "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-Q9qZJPjnkK53nLsLiz+Xi0aSPrdAghfMpmHeV5M2KSjRMygsw4uBF7/4EHkiKOikaEql+6vunebc+NHCly7T9g==", "shasum": "374422d26c027931155b771dff543c3b914f1850", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.1.0.tgz", "fileCount": 29, "unpackedSize": 20750, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb1EFSCRA9TVsSAnZWagAAD9wQAKGNgCEWdiAzqO+ioc7s\nN4GPFHnVo/cUq3WauAH8HXl1K+2aCpQfOP/RZ6Hacx/BZWfEXY6hawI74He/\n0slorPV2Rr2f59TvfQSSlNYqonOn8eXVlQei8XMoJqjFATMdC2xleRXHMk4G\nYzTKryPQRJN56qEtCqKzmT+kdreV5oRvI3XC4Z9ySR7HX+y2PcWF6sgen3mo\nkP5A5h5XjBzhb0q931Lox5fo4yP97D1Fp5URmuFsL2ICEN7DvY62KU2d6OdL\n783od5tOmiXT2nlINILdLDDiD/N9d4EsN4+Gw5Rm4Q66avdBnH4TajUyW1MO\ndLMIdUsQBuB7Z5aNDw2WMEP85GjK0sSCCR6VW3jn/cvr8bP4GoRCuO/xUQv1\nhbMbZUDe4htb3E8G1WAbUb5SmDmzG0a8UQRMq1TSEebLhm0wx+YxC4dXF2J6\nIj9q4srWX+y3LWsTNTlbGU1W68Jv3lh8KZrf+8Z21abVWeIKwCikAPash806\nbFhm3DERi3nRyUa12K0JbS4XLfWBhNwraM1jOSvHXGHxJleEoCUvm4XQfsrP\nKI0+28zhGGrJmwhZzEIEJqSdULzljQ0iolgg7yLgS3TK9uaR37LfDs7vxS8I\nGr1K1hhWTabyLVT008mz6kDHMXteEUoNdDroK9tZIR+UjDijCdBc/EFS7A6x\nUdbA\r\n=prTk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID95T/M/peT3ekU8IANz2pbfHZljg04ZIN8jcR1fD0YXAiEAodY6j6mnwfoBQ8G9eLkQmbAbdpeWsQI9j3xS/42qCzg="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "peer5", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_1.1.0_1540637009091_0.2955370783769906"}, "_hasShrinkwrap": false}, "1.1.1": {"name": "@sinonjs/commons", "version": "1.1.1", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "scripts": {"lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.js": "eslint"}, "devDependencies": {"@sinonjs/referee-sinon": "4.1.0", "eslint": "^5.8.0", "eslint-config-prettier": "^3.1.0", "eslint-config-sinon": "^2.0.0", "eslint-plugin-ie11": "^1.0.0", "eslint-plugin-local-rules": "^0.1.0", "eslint-plugin-mocha": "^5.2.0", "eslint-plugin-prettier": "^3.0.0", "husky": "0.14.3", "jsverify": "0.8.3", "knuth-shuffle": "^1.0.8", "lint-staged": "7.2.0", "mocha": "5.2.0", "nyc": "12.0.2", "prettier": "^1.14.3"}, "dependencies": {"type-detect": "4.0.8"}, "gitHead": "08de19839fa04f75a73070b7954640115439325d", "_id": "@sinonjs/commons@1.1.1", "_npmVersion": "6.4.1", "_nodeVersion": "10.7.0", "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-01oT1itdQEK4Ah2oU/+ln9sc3B0jv8QyMPBWOj+E4FoDQbzjiG8HHfnkIe9zNuILgTo7q5kNvtq9PB/bdRJ4vw==", "shasum": "f1419a0b30cd693352385d4ee09aa8fa2378e0e7", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.1.1.tgz", "fileCount": 29, "unpackedSize": 20834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb1ELxCRA9TVsSAnZWagAAsSMP/ittg20hgyMH+IGMHPt+\n+In+jJ211Tzu68YtUNuZoe14RSUakxekvrs+Ay4Scaw4VgXRuoPbnUR0CtLV\nGMuWr1U9Cg61rxRylozyQyVUMtdmEOL7Z9sZ/bC3CGxDTEytWZRWEksEbz53\nkIfZM9//sMhvZ9pPIbIIH03uOBeqLH0G7E4Hc1ZBf+xdZAZDpaQSJxAxQenV\nqPFcJkQ5hg2OLNWLwPGXoXmR7LY58+EV1+O8eV63ulZI/gS4t0jSNsjIbaef\nYjKOLhpSI16qSSqI/+5e04nTXpq+zvWbziqifktGHefEZXxcxulYhvc07M3E\n/RLtbz5LowuTqlWp1HWRnoT11jnwpvk7xjo2/L7j8viB4a5TDasmtsSoDO2v\njGXfSnyj/XtFoxr2cw2VcG5kHcTKLrSp/SklECWj/ZgYsZKvGCu0GugKOW7H\n77Cc9JK9ZXpVDheZ61kKTFWCG90lqiKTFkfTe0hWXd5OtyqJu4WuuOp0Ybnb\n6GsDWGomVuxVAof4/G/e/5uo07jTLdi+hAkEX1PzPUvwNsZeALTZn1ggAND2\nE1JZ4fgZYV3VtSuQZ1IRkeHnLY5Sk+mrLB8O0OQ3q3FhgtOHbgQYgPfVL+Ic\nMzHmYTkJl+3otbLBgPY3kCPKJdDCmYFsmPMH6NhSeNfbHYNug+P3xm2AnQXN\nQSzz\r\n=q7AE\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE2SHFPxiVEN+anDFsCcHtsU+RGYoBCDp9odFnbSjjVGAiBgdYLvIE9WucfhkVzWum1hO7pWykhFzu8MvxKS/0NhIg=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "peer5", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_1.1.1_1540637425343_0.9034652642247896"}, "_hasShrinkwrap": false}, "1.2.0": {"name": "@sinonjs/commons", "version": "1.2.0", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "scripts": {"lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.js": "eslint"}, "devDependencies": {"@sinonjs/referee-sinon": "4.1.0", "eslint": "^5.8.0", "eslint-config-prettier": "^3.1.0", "eslint-config-sinon": "^2.0.0", "eslint-plugin-ie11": "^1.0.0", "eslint-plugin-local-rules": "^0.1.0", "eslint-plugin-mocha": "^5.2.0", "eslint-plugin-prettier": "^3.0.0", "husky": "0.14.3", "jsverify": "0.8.3", "knuth-shuffle": "^1.0.8", "lint-staged": "7.2.0", "mocha": "5.2.0", "nyc": "12.0.2", "prettier": "^1.14.3"}, "dependencies": {"type-detect": "4.0.8"}, "gitHead": "bad38b582e6b6433064b7a8f5cfdc74db4ad2a5e", "_id": "@sinonjs/commons@1.2.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.7.0", "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-oZEHtyTw+Xnj7hra125osc2mxsecQ6r7XD2gQrtULn4+orMf8rlqTtMVRoKFpZIB2Bfzxzs/Fgc8LdZ+dgDdzg==", "shasum": "f565569b641564497a9b3988d7ed4ed9e8d6a0da", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.2.0.tgz", "fileCount": 31, "unpackedSize": 25434, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb1FqoCRA9TVsSAnZWagAAKmAP/AzSaNVDU1YkPlxkIHvN\nKaYPh8nbEdqScUq0+qb1hQvAQEavpiYQQw+iwn7z2QMQc0d52+6ZeGP29QJV\nseJbJpJeHV3atqlWpotdvEalwiQSTF7ZADZTwVK1WWCeyFu1KRuOKW45+T3t\nM8lAx7YtqvAWTWgBhp8Pj137LNaDVhaPor6En8hpdICcNJs9h/qh0PduxdHi\n8C/2jGeb3dTOGPBHxEneY2CouRn41YSCyhnaVg93OwoqmGcZyzkQ3VCYY345\nm351YNxyCopapZHXJZI6W8J6GZgsf8WKOxB4ku4TzKaAnCalQjFLA7EUiC2O\nypdqzsTxE3qdlaKyYFy9BrYBF2XEp+SO7t44K3GQyZhiRjP8vNdJY0ET/WpI\nbXlSnKmuFvcwHFWEsStDftTOHd2ioX/GqiD6gqYabNLjynESg14X+ZKXmZhc\nYRXsXwtpYlU9GoJZmfGuY05l0Ckeh1SIOY64mBQvixK0w2OA4AO/b7rvo5rp\nK99JEkKZkEpdFbJNK7tBctX1Uchi9VYQd9jaxQlOGRwu3fXzsSQGIAQTxP3S\nv3KClUMGh2xrIN9oQcMlGeqDkHtBJsuglymhsf2pygaGGtc+3Ceai7yoTGPF\niTOVvR3v8ENiDgImR1VixYSs7Xvn5rUoEmNwBGoisLqhiec+C6G81rsB23tZ\nlHoZ\r\n=SeTd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDSa5Xh5sWIbdIq2CNg9TYBaqRcQq0avT1O5gCWKww35AIgZ0gUDqXYUj/awSH70EzYqbus4ZegKwhXrFxTyorVb+s="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "peer5", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_1.2.0_1540643495464_0.5161487010500558"}, "_hasShrinkwrap": false}, "1.3.0": {"name": "@sinonjs/commons", "version": "1.3.0", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "scripts": {"lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.js": "eslint"}, "devDependencies": {"@sinonjs/referee-sinon": "4.1.0", "eslint": "^5.8.0", "eslint-config-prettier": "^3.1.0", "eslint-config-sinon": "^2.0.0", "eslint-plugin-ie11": "^1.0.0", "eslint-plugin-local-rules": "^0.1.0", "eslint-plugin-mocha": "^5.2.0", "eslint-plugin-prettier": "^3.0.0", "husky": "0.14.3", "jsverify": "0.8.3", "knuth-shuffle": "^1.0.8", "lint-staged": "7.2.0", "mocha": "5.2.0", "nyc": "12.0.2", "prettier": "^1.14.3"}, "dependencies": {"type-detect": "4.0.8"}, "gitHead": "b3c4360618164d07fa247cb4cae8f044332c9a8a", "_id": "@sinonjs/commons@1.3.0", "_npmVersion": "6.4.1", "_nodeVersion": "10.7.0", "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-j4ZwhaHmwsCb4DlDOIWnI5YyKDNMoNThsmwEpfHx6a1EpsGZ9qYLxP++LMlmBRjtGptGHFsGItJ768snllFWpA==", "shasum": "50a2754016b6f30a994ceda6d9a0a8c36adda849", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.3.0.tgz", "fileCount": 33, "unpackedSize": 27562, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb1bP+CRA9TVsSAnZWagAAqjcP/1m340P//98X5i/+kFEZ\ng7E5NyW6jlKb4Yjcq/92yHbQYb7nEVD6uYXuSZMHfPDkSmjmdzrEEY9ACXzL\nSZ81pp3jAaJXuqHFJv/q3vheyIW5vs4Fp2ElmFzTf+qZM9M5vHcaWsPXCUc4\npYxCcgbus7CJhvAJ+AbN5pza6ZJpMCb+yKkup/tDQ32M3/sWB5ZzY1KNQy2U\nr0kBtMYHdKVWJjYs02U9YWLQiqa0WGbbl93tsTND2iNhtmcVS4dxbKVeDAke\nb8/S5djbJAT79CYv5UzBJIkHaHY7PyycmXGbv4H0JCyMoL9wPtCVlkwP135M\nroZfkwuRXxY1ODIxyBliQAU3gnttishaUM5lciqs0l6z9gB6MwReI6+VQv8e\npKfj96o2pg79TJXnudc2BghPwqPs4JfUGId4tv5SlqQq0xLxmIjx5v+C8wXM\nA01l2u1GCOGT0tAwB4LYN3/sdTTSIqBD9KZ9aO0v0jtGwqYUQaYGQpk6v2n8\nDQwBkZZXGh/P7J0vvl4M7qTIfb3Eoq/9YlwSD3PELAb/V8pHEEwN05WRlWWR\nJdEMmoTuV7ixLW8BsDcKsO3UoCjjYKcMHN0FjyOxAn0TKwmZO5fAbL6fPRpk\nXxUekL/xo12849ch5EuQK4TiAzcGwD0KwTTM6V7868IFZi1ovvjPlQ4l2kC2\nkxIW\r\n=rcXv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAQb0hHvXKvZgtaIUUJxTVbmbzrttvuZw5i7yg+2c76fAiEAgYNas1nh8LEnFazpooPy8zX1pd1fltygDeJLWuOvv/I="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "peer5", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_1.3.0_1540731901302_0.11444728838730911"}, "_hasShrinkwrap": false}, "1.3.1": {"name": "@sinonjs/commons", "version": "1.3.1", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "scripts": {"lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.js": "eslint"}, "devDependencies": {"@sinonjs/referee-sinon": "4.1.0", "eslint": "^5.8.0", "eslint-config-prettier": "^3.1.0", "eslint-config-sinon": "^2.0.0", "eslint-plugin-ie11": "^1.0.0", "eslint-plugin-local-rules": "^0.1.0", "eslint-plugin-mocha": "^5.2.0", "eslint-plugin-prettier": "^3.0.0", "husky": "0.14.3", "jsverify": "0.8.3", "knuth-shuffle": "^1.0.8", "lint-staged": "7.2.0", "mocha": "5.2.0", "nyc": "12.0.2", "prettier": "^1.14.3"}, "dependencies": {"type-detect": "4.0.8"}, "gitHead": "91910858d5bd159b8323c605c6caa040dd7293bc", "_id": "@sinonjs/commons@1.3.1", "_nodeVersion": "11.3.0", "_npmVersion": "6.8.0", "dist": {"integrity": "sha512-rgmZk5CrBGAMATk0HlHOFvo8V44/r+On6cKS80tqid0Eljd+fFBWBOXZp9H2/EB3faxdNdzXTx6QZIKLkbJ7mA==", "shasum": "ba4ae5fa908f7d8b96b0ec8df0665aca2d8695a4", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.3.1.tgz", "fileCount": 33, "unpackedSize": 28029, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJceNrzCRA9TVsSAnZWagAAkLYP/0gphK2wPWifytHyClsA\nqBrM4r/XFsUShXJAMiR/w4yLDo2623WDbTuA5WSp6+YYEL9CTpRu+R2Oi0Tv\nJpzFOeSQ3sGrWAzTt2lw6B26pabamQowW3/82gvNqxXFKkT0272jeVscs4e8\n7sV1lyZGs5684X+0hH3ODsO1n+y2v61BMBUaReJPfQUWgssSzAWIvkd48enT\nY2TescQ49SigsP6F0POhF2lOQajve5hDkvgixs4Ds1TFoZRnL4bKS+0zgOVQ\n9Y5aeidA0u1y9V2i9wSH8KflXW4Z0hLkGDclAPMf5kRZfUTENWbq+Wvrk8Ku\n5uzUbh1uBgMefj6UH3BTcezJReQivXUzpYX1xkcTrJj6u8+LRkCM0fZShR7x\nWC9hbYMdMwXWxhP/HP3YZ2uPzNqY+grPjO/ZzAJWfPNwEvAFTVXtS634Isup\nAEvIOadWITx6JgxvHWfYBFmuqG1Q1/i6bXn1iYkJPMMAZg9uoKWZn21Hc8Kl\ndwmKho8A0EFVRhf1pEWmXypiIT4gJ5p4BaJukDLQs12NURNlh7E4y/MEjMoO\n2Niwx51c9pz1dpNgSU0L7mO+WZCW0+nHECSlUnzv0HP0awI6JOlPdft6b3To\nte/ev5bfxd6Bk8c7UmEj2B+4UjF+n+N4tgmoWPHTr22+iPlzWEBPJJHLhnmx\n2y1N\r\n=bQkf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICzIQqJ9lNY2tNlFjtjU70yV+AWXewgZkTB8OBdTYjHgAiEA8mccjpY+z4AMGZWhFbFl3ZCZRZh7MKLJuUPXI/p3+BQ="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "peer5", "email": "<EMAIL>"}], "_npmUser": {"name": "mantoni", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_1.3.1_1551424242885_0.34431990510506183"}, "_hasShrinkwrap": false}, "1.4.0": {"name": "@sinonjs/commons", "version": "1.4.0", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "scripts": {"lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.js": "eslint"}, "devDependencies": {"@sinonjs/referee-sinon": "4.1.0", "eslint": "^5.8.0", "eslint-config-prettier": "^3.1.0", "eslint-config-sinon": "^2.0.0", "eslint-plugin-ie11": "^1.0.0", "eslint-plugin-local-rules": "^0.1.0", "eslint-plugin-mocha": "^5.2.0", "eslint-plugin-prettier": "^3.0.0", "husky": "0.14.3", "jsverify": "0.8.3", "knuth-shuffle": "^1.0.8", "lint-staged": "7.2.0", "mocha": "5.2.0", "nyc": "12.0.2", "prettier": "^1.14.3"}, "dependencies": {"type-detect": "4.0.8"}, "gitHead": "aef9596b5bbc9f426bd7db737f9f1c111df9ebeb", "_id": "@sinonjs/commons@1.4.0", "_nodeVersion": "10.14.2", "_npmVersion": "6.7.0", "dist": {"integrity": "sha512-9jHK3YF/8HtJ9wCAbG+j8cD0i0+ATS9A7gXFqS36TblLPNy6rEEc+SB0imo91eCboGaBYGV/MT1/br/J+EE7Tw==", "shasum": "7b3ec2d96af481d7a0321252e7b1c94724ec5a78", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.4.0.tgz", "fileCount": 35, "unpackedSize": 32132, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcf+BlCRA9TVsSAnZWagAAH6cP/3YwLq/weNopUDJan9K6\nCbDiImIriotigQxYvBoufUyqDPP7bbF8ZTSJZawJZMDaxGrOquKCPZI2hmx0\ns/v0qNr9L8eiBYFlqyskZks/KPk/0pNTZ97+1USInqOHqjBIodxYb/PscN0b\njJnK0s54qC/7ZU2mSfIn6fT1dNkxJBpOeADikzdemNBbTRfqoRfwKJnPBMTf\nv4MA2GJvclxzuoUl9JFut7vOgGeYNeX1qe0EMXy4I+wlZtApbRE73C/1R2MR\n9a3pZINsjsrOy3o3IY/x02FncgcqKxnAbXp7xJAR0qjyti0ZePmN9e389AI7\ncJl4+JjAFbN8exrd35kqBcoiKejfSVwMCT/TW1/166tgDM9w1z6Sy04a24oA\nTJo/lQ5/lJKwJ33VWpo/ZY2spgWkY+oXdWH5i7PS7OixsbbyrgXShuy5N9EN\n8U0J789KzV6eah1hHc7E4xmP2zlYfLvbTtQp2c/b2Uic1QWO8Ha5YZ7fbeYR\nMmNlY8JiaK/b81Llm8dNHC6ybWm2OgfOaYxr+Q1jXqudDmSwsyKW5kNUhOJi\n4if7I8HS9Kb1EA+Bkx7ncXTOu8jdwn6SOBRiQeVros0OcMp1vF47ZTOmV8Me\nai5M2Bp9b+xVaICF5vwB8fjje9bPq1US/fA2upYaZHZMnsGFXt5eTmdPZTgi\nUMLi\r\n=E39Z\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDfACmMiX+prkWO12S0ySki4VemX9VnaDYOyBRIdz4gnQIhALJcFknaan1rW4qtvJgO5BKnNDycA3wVPsFbjQSc6SpB"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "peer5", "email": "<EMAIL>"}], "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_1.4.0_1551884388904_0.26500799310585577"}, "_hasShrinkwrap": false}, "1.5.0": {"name": "@sinonjs/commons", "version": "1.5.0", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "scripts": {"lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.js": ["eslint"]}, "devDependencies": {"@sinonjs/referee-sinon": "4.1.0", "eslint": "^6.1.0", "eslint-config-prettier": "^3.1.0", "eslint-config-sinon": "^3.0.0", "eslint-plugin-ie11": "^1.0.0", "eslint-plugin-local-rules": "^0.1.0", "eslint-plugin-mocha": "^5.2.0", "eslint-plugin-prettier": "^3.0.0", "husky": "0.14.3", "jsverify": "0.8.3", "knuth-shuffle": "^1.0.8", "lint-staged": "7.2.0", "mocha": "5.2.0", "nyc": "12.0.2", "prettier": "^1.14.3"}, "dependencies": {"type-detect": "4.0.8"}, "gitHead": "1f0dc2e88392add643283b797a70f0968684edb9", "_id": "@sinonjs/commons@1.5.0", "_nodeVersion": "10.16.0", "_npmVersion": "6.10.3", "dist": {"integrity": "sha512-Pc3lriv/00zQnyKXI1mLN0lH+0At1xxtC/OGIMDSbLBv3Z9rwMyfuiFBoYM+6alSf9Yb5o6hXyrX9r03ujwbHg==", "shasum": "785c7f7f27c8963d22f9becf6881016527cd1b13", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.5.0.tgz", "fileCount": 36, "unpackedSize": 32450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdWw1fCRA9TVsSAnZWagAASUwP/jjyNqpsNxrnJqnESQLM\nuCnMmxIq7XhANwJdvTtZQgmKHNpjQ3030IGFHoJ2GMqpHH1BnkuEddWoU0tA\nbtTslNPxaiF7o+cjwxiSWcWT8HRrSZq5W8LW4fOr1U7mWF9dtrclQMQ8Hpjv\nBzUPCCld1TppRIIB3BoLWmYAYylutG6utaFTQ6nwYgLFpV/Qybfu8WRXfIpP\nVyKsvCvl6jfydfyjg//NkNoBXRk2kUr0pHhS6HSCJ+bbVFv34QbM2orCfdOu\noBvwNnXdXu+FyjogIRiTiMLxxkIBWYXH6+ldXokoQMcav+360s2cigtvJTBD\nC9xDBtd74n5M5QTq+fqIopqBEEuQHXXu97jBTyDzpJmTOYW4GgEETwEjygff\noerhPmDxb0iTu48NcXrAuAvclFHhEUeSSfl5fdXP6iOnfJmwvvu0wIZapcZR\nJoWhUEa1kmohSDjyKC9LMblvvaSpfTw6XUlqA2vOcOZP/D5w4EFjqtAv2VFB\nrKMTfAD+zxuLDxihRDzfSkRcegQ7FeDVf9cPQ4zuvRJ9UkPWSGchQ99/RLi6\nv6yOmyXF1znYfXCCC5KUzr4Z1LsYNxHiKeiQlazL63Yv9FJyw+ODou1CNWvn\n7NyoqbXFChOzg7oD2R2aY7pSu3lr909r00VJjnoB+yOioq7extqd7pQZEP2n\nQ844\r\n=HQ2A\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEwCPXGcZdKq/N2txP69SYXjPzEIdmabxWlhQIZPmY2LAiBIuS6OYW32Yunlp/No8T4Ebdh5hM3DObTwUfxo20Bf5w=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "peer5", "email": "<EMAIL>"}], "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_1.5.0_1566248286950_0.715551299612144"}, "_hasShrinkwrap": false}, "1.6.0": {"name": "@sinonjs/commons", "version": "1.6.0", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "scripts": {"lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.js": ["eslint"]}, "devDependencies": {"@sinonjs/referee-sinon": "4.1.0", "eslint": "^6.1.0", "eslint-config-prettier": "^3.1.0", "eslint-config-sinon": "^3.0.0", "eslint-plugin-ie11": "^1.0.0", "eslint-plugin-local-rules": "^0.1.0", "eslint-plugin-mocha": "^5.2.0", "eslint-plugin-prettier": "^3.0.0", "husky": "0.14.3", "jsverify": "0.8.3", "knuth-shuffle": "^1.0.8", "lint-staged": "7.2.0", "mocha": "5.2.0", "nyc": "12.0.2", "prettier": "^1.14.3"}, "dependencies": {"type-detect": "4.0.8"}, "gitHead": "ca32735499cebfc186364e8145129128bad4ceb0", "_id": "@sinonjs/commons@1.6.0", "_nodeVersion": "10.16.0", "_npmVersion": "6.10.3", "dist": {"integrity": "sha512-w4/WHG7C4WWFyE5geCieFJF6MZkbW4VAriol5KlmQXpAQdxvV0p26sqNZOW6Qyw6Y0l9K4g+cHvvczR2sEEpqg==", "shasum": "ec7670432ae9c8eb710400d112c201a362d83393", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.6.0.tgz", "fileCount": 37, "unpackedSize": 32711, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdWw+rCRA9TVsSAnZWagAA0+UP/jAb+3ZKKNg+grE9vaz2\nZeiFqrXAtAR09SdZc67mWic5brrZfpFB4v+KrYIHOGF01K5Wl1mvT71DaXc1\nZ0ccUdGns5iRafUamxSwVLNour15NniN8gjAjHoY6erVWWhpxIz8pA6tfyqs\nO5zCsRgq3OipWeT10iKHBHKyRQUVbP3o6NnJT2tHyJfXsj8Rh58ADqN+fVlN\nVUu7y0Vej0Bfr5u4AS4q8UzKoG3nrouuCowurn21FHcSpANW++oxtvrTkMPV\npUA291NpQWzzpLoOucyHIOKzx6trSOictCS9rYsbiG5ArpcZDFo5rqa3dT4H\nQLV6E9hmSJYPh/HLKbGORPqNBkhVnqWpfQpHgkFKYGC0wv/mNa0WSNcuVCBI\nFDB21Uio1KdcmcZAKE9WX/mT/3bY2vQt896AxmQO+vRyUuVNHcZTJLgC4A4V\nxWW6CdoW8Liad0oDnyf3TZ7RsuPFU+U0xrXZUKSwiW+4DuJNQYGZRpocjSYA\nzX9oWDm9p+dfmN9u5T09swF+K+XQvIeK3cwH4Dnks6NH6GkLQ7/zlNUUmexW\nICW1E220Mjrsi6XLQPi+egUYTA1mXLutN09XTvqilnw5jqhqLKBo8FQ3h/zc\n0nMnVxMhexDiLjWyQ37ZuIVwhMyzM5bxm8kBiVelw1fJYV9DpoLvWhnOuyJ3\nNQN8\r\n=Gv+p\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEa1LJ+x+Rx4CpXCL8cBelgpXtlJp+95g38DQuPuRnXdAiAZGP8AwHd0qlC5O4bxKJw7XiagFY+mL5hwjJekX77f0w=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "peer5", "email": "<EMAIL>"}], "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_1.6.0_1566248875182_0.8838072305695119"}, "_hasShrinkwrap": false}, "1.7.0": {"name": "@sinonjs/commons", "version": "1.7.0", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "scripts": {"lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.js": ["eslint"]}, "devDependencies": {"@sinonjs/referee-sinon": "5.0.0", "eslint": "^6.1.0", "eslint-config-prettier": "^6.3.0", "eslint-config-sinon": "^3.0.0", "eslint-plugin-ie11": "^1.0.0", "eslint-plugin-local-rules": "^0.1.0", "eslint-plugin-mocha": "^6.1.1", "eslint-plugin-prettier": "^3.0.0", "husky": "3.1.0", "jsverify": "0.8.4", "knuth-shuffle": "^1.0.8", "lint-staged": "9.5.0", "mocha": "6.2.2", "nyc": "14.1.1", "prettier": "^1.14.3"}, "dependencies": {"type-detect": "4.0.8"}, "gitHead": "59bed154b7ae30feb19e72bbad35c4cd47cd25e1", "_id": "@sinonjs/commons@1.7.0", "_nodeVersion": "12.1.0", "_npmVersion": "6.13.4", "dist": {"integrity": "sha512-qbk9AP+cZUsKdW1GJsBpxPKFmCJ0T8swwzVje3qFd+AkQb74Q/tiuzrdfFg8AD2g5HH/XbE/I8Uc1KYHVYWfhg==", "shasum": "f90ffc52a2e519f018b13b6c4da03cbff36ebed6", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.7.0.tgz", "fileCount": 39, "unpackedSize": 34579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+kchCRA9TVsSAnZWagAA4DQP/0OK1GXeaszJqFK+Yp2N\nfm9TglUSrZp6s68vVGHHiZLV0su7co2di8J7oHbUPvJuB8c5N6HWMQuLPLxw\nYFbUScg9JKXzwzFkMUkQz9JusIAS3isW2cm18ZB1hQ+D5phATXlJMuwAPR5V\naKg2iCZG/+aztHqlJKhOJNSnYJxfI60qIIv8PpWVRFarSLMtT9lYCAcVydAw\nt+BX77ECVMPgW6Q7QrHWGedpJTAmmkIVhCgdbjqcoTYcHQsL+xHk3O8mkvkX\nD5/NcV4v6MBIAdfGerfiUlgV3YX5fImlh72AjPVJRuZMjpkkIqnBI2kuHgSw\n6mfvdqZb9aeMqOfIqrdeN7jINqSHmgy6wcfiPffL+N21yxTExBfXR5RfhKWu\nx/9dCTdt6cFi+cqgdFEiZgd98Z4vDacWgqSIWqBRnYr1XEL+HXwBQsABJGxO\n/UtMJN1yQ/N47Yj1ZKkVjCz2zi2b4NeI9RBibm5GyAU7n9H1Zydth8NAiRsO\nZaKJSAF/yIvysOT16U7h52dMSaWLKcq8UlpTazXPbb+hKN0NdOjcxYIje0W6\n6sfVQ4g3FwXWBdTJweOmwzHtRJdjPDcZjIYw+j2J9d3U0lY0tcM3ZUh7ZAuW\nzv0i6yQv3PefrG3oSl0MnNDsnAY82N9iPsIemwfeQ+Zm6UPpgSgzM8BJsduA\nE07v\r\n=Cust\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCuxIBER3ZHG5IGRhsGQ1njzG9pPPaoonsUvnRFGv5ydwIgPPsLKBoMZUG65Q5Ge2Ipkoj1yEF6CyLu1YZuBT03M9A="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "peer5", "email": "<EMAIL>"}], "_npmUser": {"name": "mantoni", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_1.7.0_1576683296136_0.713699867957446"}, "_hasShrinkwrap": false}, "1.7.1": {"name": "@sinonjs/commons", "version": "1.7.1", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "scripts": {"lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test", "preversion": "npm run test-check-coverage", "version": "changes --commits --footer", "postversion": "git push --follow-tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.js": ["eslint"]}, "devDependencies": {"@sinonjs/referee-sinon": "6.0.1", "@studio/changes": "^2.0.0", "eslint": "^6.1.0", "eslint-config-prettier": "^6.3.0", "eslint-config-sinon": "^4.0.0", "eslint-plugin-ie11": "^1.0.0", "eslint-plugin-jsdoc": "^21.0.0", "eslint-plugin-local-rules": "^0.1.0", "eslint-plugin-mocha": "^6.1.1", "eslint-plugin-prettier": "^3.0.0", "husky": "4.2.1", "jsverify": "0.8.4", "knuth-shuffle": "^1.0.8", "lint-staged": "9.5.0", "mocha": "7.0.1", "nyc": "15.0.0", "prettier": "^1.14.3"}, "dependencies": {"type-detect": "4.0.8"}, "gitHead": "9998b1ea824bf5cc79a8f97c3a4cca7be1318b8a", "_id": "@sinonjs/commons@1.7.1", "_nodeVersion": "12.15.0", "_npmVersion": "6.13.7", "dist": {"integrity": "sha512-Debi3Baff1Qu1Unc3mjJ96MgpbwTn43S1+9yJ0llWygPwDNu2aaWBD6yc9y/Z8XDRNhx7U+u2UDg2OGQXkclUQ==", "shasum": "da5fd19a5f71177a53778073978873964f49acf1", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.7.1.tgz", "fileCount": 41, "unpackedSize": 42641, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeTRhlCRA9TVsSAnZWagAAN+cQAIDZ6ZYkYo3kMGZf4mX4\nUgBcuBx6Y8QW759usEBWmvEtdlePRvqjoam/d1oCfF1n9Ds9kd/AwnEOByD7\nKyrGHOohhisyHDpEpwu9X7u36eoHHENxfDzYP21cD0oRSCVm41Joesso5LHX\nd+n+4illiRGobMQjkRgzOSDdRxH2L3Gb3hbUTx0OeblQuJ235CeAXyjzuHik\nXm31dSOOzwQp198bKO04bTYZrBAu4NMM/s+5ZwQCwXWmosFtxX99mLdjh3Jk\nWzqNhmF2YOjVhY61PqLk86vo/TrNPpZQSY/zgFvIgVeoTkRTiYnAMoXSdPxI\ns2W/nCRrpOWMoMiSLtyKNp/pvyotRqHJI81a41Ib366bYbvbhFIenXNkJtGY\n5qENSa1CRkKXH3+cpGrXDJ/xH/5RPY9Ojrzn6Cjr0JUYUiWOFKspanAkageH\njZmkgEelsqv2LXSXaElWH4YZeL11J1FAAP+PK3c02PJXCJgeRZijciWRJAuc\nkR2/rAYWYERMs2w6lDSbIV8owL9aX95E80H36bOg9e9eld7B0bwrotvTNQj7\n70VyOqrboDCkAIqnroqNAxhquvURzM/zBJE2wdsV+bihAmx/TJRJ1I3F5vCC\nvhKf8ob5kSroawlxiiXCNfxqpgSfMe6rj1QAr66x3v2OnbyFkCGHyA5N27bX\nbGWf\r\n=CXIS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC9XXpl/3p947WZyCNIEwiusVVqWznq/Qzl+6xNeIByWQIgE9e4MSExKn56OR2Pi8Fl8l0x+jL59po6uDlbfVcCBRM="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "peer5", "email": "<EMAIL>"}], "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_1.7.1_1582110821120_0.17504665090766158"}, "_hasShrinkwrap": false}, "1.7.2": {"name": "@sinonjs/commons", "version": "1.7.2", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "scripts": {"lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test", "preversion": "npm run test-check-coverage", "version": "changes --commits --footer", "postversion": "git push --follow-tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.js": ["eslint"]}, "devDependencies": {"@sinonjs/referee-sinon": "7.0.2", "@studio/changes": "^2.0.0", "eslint": "^6.1.0", "eslint-config-prettier": "^6.3.0", "eslint-config-sinon": "^4.0.0", "eslint-plugin-ie11": "^1.0.0", "eslint-plugin-jsdoc": "^22.1.0", "eslint-plugin-local-rules": "^0.1.0", "eslint-plugin-mocha": "^6.1.1", "eslint-plugin-prettier": "^3.0.0", "husky": "4.2.3", "jsverify": "0.8.4", "knuth-shuffle": "^1.0.8", "lint-staged": "10.1.1", "mocha": "7.1.0", "nyc": "15.0.0", "prettier": "^1.14.3"}, "dependencies": {"type-detect": "4.0.8"}, "gitHead": "c546661f789b01efdd163f5ff1851dc7c0d18919", "_id": "@sinonjs/commons@1.7.2", "_nodeVersion": "12.16.1", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-+DUO6pnp3udV/v2VfUWgaY5BIE1IfT7lLfeDzPVeMT1XKkaAp9LgSI9x5RtrFQoZ9Oi0PgXQQHPaoKu7dCjVxw==", "shasum": "505f55c74e0272b43f6c52d81946bed7058fc0e2", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.7.2.tgz", "fileCount": 41, "unpackedSize": 43645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejbGPCRA9TVsSAnZWagAAyRAP/0BpIp/Q0/FNFStHKASV\nu+Gr9J0XNRd9qj3FuwTGmm1nMXpnHT6tKChcjPMH6pZKwfWigLYzWcaBn3hk\nl3r1u1eQISXhV01nh1zFUgib1WQbuwYnhEx6htlnyVBZbE4oaO6o5aNS40bm\n6WnHAy8XPJtgKwj7lWUooFpXlBkTpfrgIbSS3OliFHTZ0etnied4+BVEyuqG\nwO37xjka1xpzYMcRM+VRGDf3lxArZbnB8MCEnPGLc0kCY24fQfSVks2x0Yy6\nlUzPflTKcjAPrmtphSee7EqMIbh/QexEtNZfG2PF3dIO6u9SCo3xUuwI0lGz\nrV1CrCn+pE4/SK0+laPX0sxLAmRfxiF7VQmKh3wqXHSRwOjaQ+t7tB915YpJ\njqivhm7gFiWAeiAGOh9cHKtct9rBg/p/+luujR5l+3D9G+lAxZS6d8Pa3pgy\nTDsbuwaboAHg7FcCBualfUOjp8PRqNUacKXMHldEscOmyjBTSTSJ+unAsK0h\nNlyMNWKihimHigflxDyb0rz204ZzOfts9C1+mQUmfbTpRp5vavX75G8Kjl1l\nsg7dE/tGHHirew4XCnLfJZ369liBHP7IHhKwzHxxGfqbiALS6AxSwbJ7L4qC\nIjGTK6E4vZAUbGv9x6eYlrHZd1NaLgZFIEn+YFp7INucRNgCeHJh0mq4Dflh\nc/0s\r\n=GgNw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDNUffyz7g5FCkr2+D8NjvmqIZveBSCXz0xVSA9Cy3LUwIgEVgEJMZQ9vq+G0dYXz53vk5qUd3NElJuRcTtRb9+MGk="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "peer5", "email": "<EMAIL>"}], "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_1.7.2_1586344334694_0.3255223920603838"}, "_hasShrinkwrap": false}, "1.8.0": {"name": "@sinonjs/commons", "version": "1.8.0", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "scripts": {"lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test", "preversion": "npm run test-check-coverage", "version": "changes --commits --footer", "postversion": "git push --follow-tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.js": ["eslint"]}, "devDependencies": {"@sinonjs/referee-sinon": "7.0.2", "@studio/changes": "^2.0.0", "eslint": "^6.1.0", "eslint-config-prettier": "^6.3.0", "eslint-config-sinon": "^4.0.0", "eslint-plugin-ie11": "^1.0.0", "eslint-plugin-jsdoc": "^22.1.0", "eslint-plugin-local-rules": "^0.1.0", "eslint-plugin-mocha": "^6.1.1", "eslint-plugin-prettier": "^3.0.0", "husky": "4.2.3", "jsverify": "0.8.4", "knuth-shuffle": "^1.0.8", "lint-staged": "10.1.1", "mocha": "7.1.0", "nyc": "15.0.0", "prettier": "^1.14.3"}, "dependencies": {"type-detect": "4.0.8"}, "gitHead": "859f805e02ccb352d82853d3b6f2e7eb5ecd8a98", "_id": "@sinonjs/commons@1.8.0", "_nodeVersion": "12.16.1", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-wEj54PfsZ5jGSwMX68G8ZXFawcSglQSXqCftWX3ec8MDUzQdHgcKvw97awHbY0efQEL5iKUOAmmVtoYgmrSG4Q==", "shasum": "c8d68821a854c555bba172f3b06959a0039b236d", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.8.0.tgz", "fileCount": 41, "unpackedSize": 44387, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJexT7hCRA9TVsSAnZWagAA+VcP/iNfi7eQwYT1C7Xm8is4\nC3SV4pBCERvHFFrOd5vPsWXhmRyDG8rYyVM70r/ZxX96Ex2LnbrDOrjBuyWr\nym+piKyVh9dVDSeB1d0j94Kb1nt0GUxS1iF2ExIoRS3ws3cXrY96hWR/IGhv\n8KLybq+ZgoMyD+hGPBhqmKwa9E+Jm99qOWmUMvfq4FIksud/3JbLIyT0Ll6c\nkSiTsyucSPm/2UtjAOlAf8u7wmCzZDKDjn87mevhEGucCrlv9RiPQaV7Ge+o\nBAzW4F0ahdZddiBiUn8zE0ylfA0lsas0R41vM4PZ4lC0EZjeZR6fEr9DtyY9\nPaJczUZcLVQQRditZRpQjDz+piyMKHS79kf/a3vMOH2WAMoevtJYIXNpcD1g\npxQW28cmK7RMmbz0zbtXMtnzBFjstE3XtIFDjKgXujbztg+fJJf/MJdtnqO6\n9bJuxaawFity5QLa+PvxrvobYlWvXVJj7YAZBei4MvARSr5vUGqV6lnIT3AB\nAmowW7D/0sSkRHYfU8ql/qxGg7HY04VdBb1VI1qWBszDFZBT020UtlDTyA+g\n+CKBZ/PdWTB31oGi0s7n38cV1RCvdTYlfiWvrr/8XLHem/u8bBFELni6eORr\nG1JEYExCd/spb+8mjhfuIdvdIQOl+BB5FS4hmnm9HB2aSDPUYaeefGz1uHh6\n3n6Q\r\n=LEft\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCCtGKRi2ZhtbyCzBOwkZdvK7fIYNZutHgq/51dv75wLAIgKycPjMDc9xxacBQp33I1cPEHEsIBLjGS0M/k8apn9YI="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "peer5", "email": "<EMAIL>"}], "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_1.8.0_1589984993343_0.20500523845595975"}, "_hasShrinkwrap": false}, "1.8.1": {"name": "@sinonjs/commons", "version": "1.8.1", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "scripts": {"lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test", "preversion": "npm run test-check-coverage", "version": "changes --commits --footer", "postversion": "git push --follow-tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.js": ["eslint"]}, "devDependencies": {"@sinonjs/referee-sinon": "7.0.2", "@studio/changes": "^2.0.0", "eslint": "^6.1.0", "eslint-config-prettier": "^6.3.0", "eslint-config-sinon": "^4.0.0", "eslint-plugin-ie11": "^1.0.0", "eslint-plugin-jsdoc": "^22.1.0", "eslint-plugin-local-rules": "^0.1.0", "eslint-plugin-mocha": "^6.1.1", "eslint-plugin-prettier": "^3.0.0", "husky": "4.2.3", "jsverify": "0.8.4", "knuth-shuffle": "^1.0.8", "lint-staged": "10.1.1", "mocha": "7.1.0", "nyc": "15.0.0", "prettier": "^1.14.3"}, "dependencies": {"type-detect": "4.0.8"}, "gitHead": "9ff5918ff57da8e13de4919af8cd6102019560b6", "_id": "@sinonjs/commons@1.8.1", "_nodeVersion": "12.16.1", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-892K+kWUUi3cl+LlqEWIDrhvLgdL79tECi8JZUyq6IviKy/DNhuzCRlbHUjxK89f4ypPMMaFnFuR9Ie6DoIMsw==", "shasum": "e7df00f98a203324f6dc7cc606cad9d4a8ab2217", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.8.1.tgz", "fileCount": 34, "unpackedSize": 32790, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfEWYBCRA9TVsSAnZWagAA8jkP/0H5I5Ypgr+tojPgGTR/\n3jAwkdOFbFzqdUxz2VpMVrfz/R7sBCrvP9n38iQpe8AQrASOep7MygiFc1sE\ncwrEhSwDnRzDcbnylSCvDUxYEbjTzZk+2wI+boiY99/Fyp3ViKjAOK0u6EVt\nasumXyiY+fOrYi/s1IR8tHFpjHzQBKIGjEqYIi5NBktMrpb6xW5id4/3/NvV\ngNgtLgsNWs88gr6rbhvu/liL0sfUV+pbW2zKEPSgMhDt3rJPd/HKO6KjDcrb\nh4MQj4BtigGZLq+XMIoHE33dxUThJopRiGz2PMfg3XQ8w5wMBFSxy3jlsnax\nD3zM8SmDJWOTHIX/5pK+fgSTDGFdPQ187Vr/IV66JaJrjd9S8NI/XBLPYojG\nC4VNAAuuTZfp9N1RfozkFbGRxNfDZQX8zpzm/cmy992C8tf/br/MtpIlw/VT\nw85Nk1lq5dAFt3Iw6E7Q+TtgoVIPHdeQSUdGv1azDaKS3PhhdqgJ4HHNCvjS\naUChI7TQ+taP7c21ldQsD2yW33b/T0zcsFHkpR9zSb/iPyPYsv1ZhBlmS4fm\nAkgMEHhkoBZBp/ISmcJ597K65OzzeBhEcbzBAOyVavmofQP1wEL28R8Mo8vq\n2xTvvvNh2MXpO2eaF3To8magDEPBG6s5sIv4NKDNbyAMD3R70H4oVV+oOZHK\nE1VT\r\n=zbkp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDk4882B+TLomCarkUGKPHk4pGUeBkKeM5qoFY0zly9NwIgLIZevnnEMTRQ8HOb7GMGA1tT0IPwBQtfzuKKqEVAlyU="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "peer5", "email": "<EMAIL>"}], "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_1.8.1_1594975744936_0.018319038096953788"}, "_hasShrinkwrap": false}, "1.8.2": {"name": "@sinonjs/commons", "version": "1.8.2", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "types": "./types/index.d.ts", "scripts": {"build": "rm -rf types && tsc", "lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test", "prepublishOnly": "npm run build", "preversion": "npm run test-check-coverage", "version": "changes --commits --footer", "postversion": "git push --follow-tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.js": ["eslint"]}, "devDependencies": {"@sinonjs/eslint-plugin-no-prototype-methods": "^0.1.0", "@sinonjs/referee-sinon": "7.0.2", "@studio/changes": "^2.0.0", "eslint": "^6.1.0", "eslint-config-prettier": "^6.3.0", "eslint-config-sinon": "^4.0.0", "eslint-plugin-ie11": "^1.0.0", "eslint-plugin-jsdoc": "^22.1.0", "eslint-plugin-mocha": "^6.1.1", "eslint-plugin-prettier": "^3.0.0", "husky": "4.2.3", "jsverify": "0.8.4", "knuth-shuffle": "^1.0.8", "lint-staged": "10.1.1", "mocha": "7.1.0", "nyc": "15.0.0", "prettier": "^1.14.3", "typescript": "^4.1.3"}, "dependencies": {"type-detect": "4.0.8"}, "gitHead": "5d6a5b12ddacc02c3640382fe86d0fa0b0b3b1d4", "_id": "@sinonjs/commons@1.8.2", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-sruwd86RJHdsVf/AtBoijDmUqJp3B6hF/DGC23C+JaegnDHaZyewCjoVGTdg3J0uz3Zs7NnIT05OBOmML72lQw==", "shasum": "858f5c4b48d80778fde4b9d541f27edc0d56488b", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.8.2.tgz", "fileCount": 52, "unpackedSize": 37264, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/uCRCRA9TVsSAnZWagAAdGsQAJI4xiAb+F84Ew0cxXkV\n+wixlyXhDSNr6yRR5DstA2lvb6gfG6WXHHmUChB/cWiKJKhy2jCjoyKP2oVh\nkEv9tFue0C+kCN5/AdQV5zQzf6hdchGsxQ4JeKWhlN9RDz6J88usDXHdoVsy\nXSYK91qgrWurICwcjBkRUwSqWTF616D8/5/pTSOdSE2Bix53zWuTFrL0WRjx\nf08IMvf/LfLBgfZRH19gUJmDxjCTtU2QQcwkbLKA4ZVNfjq7z0n+qyzBaiMv\ngdr54K/SApeN+pbhQhePS0g6YqvBjF4Sv5G8YtBMO2biQZXDOLda4fPrew+d\n6666Njci7TQudoda47/6l22oBWt/qd8qEMzG+h948yb+BvFCYX7wsn5ZYsa0\nMl6G2bSv+cg5k3L+YdzM6xJsRkB0fe1jpuDTXD0fy+q/WlAKEIX5gsdWMBmi\n2MlsTsK7NrhZ2aRB/t7mjTwWA9X71YqwQ9W0I1xRb6JGHDrK9uCM3NcqIigy\nsMsYjNqg/Kf/nFCMypWOTt50t9CUchVnRbmc7JiPeCuYnKppWNQCIxtY92js\nzV6kQp5g6J7ZBBJ0+hHn5YhcWfGkcJSj6Z7r3TivR6scywyvfvbIr1AB7Fe/\nwJnRlTHzJzTlF1QyCs61wMzkmjE2gRaw6sFYiKDihbC8u47HGOfCHu6rLbfY\n/6SZ\r\n=02tP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDr8KOC2eIdken10B6qgthywfkCsngnehF9hvWgEq9t4gIgPvZYT69ZTu02jlnV4Wxff4Y/wCAy8NtctVY51KIjWjQ="}]}, "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_1.8.2_1610539152986_0.5824078644666462"}, "_hasShrinkwrap": false}, "1.8.3": {"name": "@sinonjs/commons", "version": "1.8.3", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "types": "./types/index.d.ts", "scripts": {"build": "rm -rf types && tsc", "lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test", "prepublishOnly": "npm run build", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "preversion": "npm run test-check-coverage", "version": "changes --commits --footer", "postversion": "git push --follow-tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.{js,css,md}": "prettier --check", "*.js": "eslint"}, "devDependencies": {"@sinonjs/eslint-plugin-no-prototype-methods": "^0.1.0", "@sinonjs/referee-sinon": "7.0.2", "@studio/changes": "^2.0.0", "eslint": "^6.1.0", "eslint-config-prettier": "^6.3.0", "eslint-config-sinon": "^4.0.0", "eslint-plugin-ie11": "^1.0.0", "eslint-plugin-jsdoc": "^22.1.0", "eslint-plugin-mocha": "^6.1.1", "eslint-plugin-prettier": "^3.0.0", "husky": "4.2.3", "jsverify": "0.8.4", "knuth-shuffle": "^1.0.8", "lint-staged": "10.1.1", "mocha": "7.1.0", "nyc": "15.0.0", "prettier": "^1.14.3", "typescript": "^4.1.3"}, "dependencies": {"type-detect": "4.0.8"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "gitHead": "e909e9746c32ba6c3c67e64b9ca5b62644cf343e", "_id": "@sinonjs/commons@1.8.3", "_nodeVersion": "14.15.0", "_npmVersion": "6.14.11", "dist": {"integrity": "sha512-xkNcLAn/wZaX14RPlwizcKicDk9G3F8m2nU3L7Ukm5zBgTwiT0wsoFAHx9Jq56fJA1z/7uKGtCRu16sOUCLIHQ==", "shasum": "3802ddd21a50a949b6721ddd72da36e67e7f1b2d", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.8.3.tgz", "fileCount": 52, "unpackedSize": 38021, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbrK9CRA9TVsSAnZWagAAHSEP/Ri2dtZfWr89aGvv/fN9\nPfUyCPaJHlsyr0TlXYpAQnMv+/U+oiJTJb3BIgEWGdAnXaUOq43FZPqpDFl2\n18XM5mSiXQKU5SLX6R/XaBn59hmkJn57wmkv9cjk4c4gts4gNVhPr/1wnYU+\nwXX6NZgQxzrMMxsJXunQZV5hKwq85tplOCpSIN+hZ3xsFFQoTLuYGetknS0C\nkrvBXnHF550ztDFxAhbwM9/noTYSVojcv6YNvQbVxj4OjLzvT2UKqX+9xe8N\n5zMbVFzWwNnBZOuqAMZAbrpgSAdCQ8sUmeJL+/HmGJYobZEc9OCzudWFh9rF\nSGExDGdzEd1hssZ5JYZA4ySMau8eHGMKfTAlfNfGFkbcnsqob+7LEp7jT2/H\nooNgOQjlqOTx3yIosgja5riMDkdF+wunoakzy10pnAqHBDmK4S5wyaneSJH6\norMf+cKC8w09ZllTXwswnyd/m4XhJugceqO6b6qrj+gPMkHlzIJeyyRXLU4/\nh0whigdGzWB5KU8fsSpp/c55WoSp1tGgOmq3OwptC18IHUw17Cnfxs5CRyos\nWoXZf7vIG2Q56y+ZVilUcZIpmf4uOzpoy+OrulQ9Ge//wQTN5tnJl5Szyh+s\nOfQuknYCiaFijd4keSlaY1kXIv5oDY5FyFCzRqoGBGrJnIn5CqgDWyQ5w69D\nEgML\r\n=xawU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCjF8PKr+YMdzj9pTt897iwFBufEGN7T+EQO2wGCvdwdAIgbuJbbtmJkOMxTeKsi55F88JkSbJanxcZWdFHBAhOC1c="}]}, "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_1.8.3_1617867453047_0.5612547595643116"}, "_hasShrinkwrap": false}, "1.8.4": {"name": "@sinonjs/commons", "version": "1.8.4", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "types": "./types/index.d.ts", "scripts": {"build": "rm -rf types && tsc", "lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test", "prepublishOnly": "npm run build", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "preversion": "npm run test-check-coverage", "version": "changes --commits --footer", "postversion": "git push --follow-tags && npm publish", "prepare": "husky install"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.{js,css,md}": "prettier --check", "*.js": "eslint"}, "devDependencies": {"@sinonjs/eslint-config": "^4.0.6", "@sinonjs/eslint-plugin-no-prototype-methods": "^0.1.0", "@sinonjs/referee-sinon": "^10.1.0", "@studio/changes": "^2.2.0", "husky": "^6.0.0", "jsverify": "0.8.4", "knuth-shuffle": "^1.0.8", "lint-staged": "^13.0.3", "mocha": "^10.1.0", "nyc": "^15.1.0", "prettier": "^2.7.1", "typescript": "^4.8.4"}, "dependencies": {"type-detect": "4.0.8"}, "gitHead": "5b9515368e717cd5a6ac7531c0ea2498abb4771b", "_id": "@sinonjs/commons@1.8.4", "_nodeVersion": "16.14.2", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-RpmQdHVo8hCEHDVpO39zToS9jOhR6nw+/lQAzRNq9ErrGV9IeHM71XCn68svVl/euFeVW6BWX4p35gkhbOcSIQ==", "shasum": "d1f2d80f1bd0f2520873f161588bd9b7f8567120", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.8.4.tgz", "fileCount": 54, "unpackedSize": 38629, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDazALPC+PRF+etUBrXoKSI7iKNEG1xqtxjUaShVKYn4gIhALOxi5Z4jwATAqOHd+yQ0JHvBmBaZwMXlpy7Db/nkRqk"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYqOHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqH5w/+OxsEcbPhWMX6r61z1fUeGMG+N3aPtl9QaX7ELaNprQuCCKP0\r\nhdkxMQFJhOHDgVSUw4DWcTnuu3pIcobE5rwBrjp4u/15hhE8iMwW2tiuZnuD\r\niwN60y4NpnYCmfd/dCvhSd2C1kBVliGkW7+xuYbhYY7HnNzAkyd4Rzd1at3K\r\n07Z+Dmz/EitN8vTQVSL3O47D6Fx0/dsZz3ffrJgD+jMPnqis5fJlNEsYKuLW\r\nsfZ4Qh7v1FCQmeg+cKS2GvC3Yf/f/pZ6orMqePUEJ7Jy01Sj/dzk2YqRU4jF\r\nwtmC1YnJURN5AXha9j6tqQWRLXNXZVNjRTxNj9o/ml/CeOWMfz5U+6mhdN47\r\nVH9Mel4PKwBuH33VQv3/nLrQll4OM1JlGjdJHr7Eacnhb4c/qpcTjwYLL936\r\nBUsmoSeBbWFIEe58pWTf/UT4Lfawg5EthjIL2L/eBO2cH3llDUyd6L5nqdll\r\nliWTqTE2yHd4hZEx0asUa1/A2pqq3QnCvp0Jm63VCYZ//Cxr0ZSugxI6M/f2\r\n5XTOsd+MFH4uhzx2qGPbSwvM9efbupP0v/JqjvRVh4DJQfvvNLv/9U/kYoue\r\nzwAzY+UP6ugB6ryaOD4M6Eh4QrJHOC+xrDxRxazsW+XXWjZDoAB8q1bI3wwB\r\nZ08MgqUffYrosdoX3nj/WuwdIGNNQg95nBk=\r\n=wJiV\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_1.8.4_1667408775146_0.33337446184330743"}, "_hasShrinkwrap": false, "deprecated": "Breaks compatibility with ES5, use v1.8.5"}, "1.8.5": {"name": "@sinonjs/commons", "version": "1.8.5", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "types": "./types/index.d.ts", "scripts": {"build": "rm -rf types && tsc", "lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test", "prepublishOnly": "npm run build", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "preversion": "npm run test-check-coverage", "version": "changes --commits --footer", "postversion": "git push --follow-tags && npm publish", "prepare": "husky install"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.{js,css,md}": "prettier --check", "*.js": "eslint"}, "devDependencies": {"@sinonjs/eslint-config": "^4.0.6", "@sinonjs/eslint-plugin-no-prototype-methods": "^0.1.0", "@sinonjs/referee-sinon": "^10.1.0", "@studio/changes": "^2.2.0", "husky": "^6.0.0", "jsverify": "0.8.4", "knuth-shuffle": "^1.0.8", "lint-staged": "^13.0.3", "mocha": "^10.1.0", "nyc": "^15.1.0", "prettier": "^2.7.1", "typescript": "^4.8.4"}, "dependencies": {"type-detect": "4.0.8"}, "gitHead": "bb22646fe3ff3c86cd2d5a3059e59b1cb2dedb5e", "_id": "@sinonjs/commons@1.8.5", "_nodeVersion": "16.14.2", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-rTpCA0wG1wUxglBSFdMMY0oTrKYvgf4fNgv/sXbfCVAdf+FnPBdKJR/7XbpTCwbCrvCbdPYnlWaUUYz4V2fPDA==", "shasum": "e280c94c95f206dcfd5aca00a43f2156b758c764", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.8.5.tgz", "fileCount": 54, "unpackedSize": 38633, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCrPjlgYCz13rcM3rwN1BG1k0tjI27WnjXd/10UGpmBIgIgKAB+dBcsJhzdCWfEVWQ7nfNuHZMgA33j6quDpXvVNYg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaTYEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmohDxAAlF5VO4pR5YZ4MUBodG8MEgqqScB6gcZnMyAqyfDjuDJj8des\r\n61m2LA7VbfkBradrvbnNEbN627yhkaEEnu5hcsY/KzPzumi+PeAPBoRz2yHZ\r\n/gt6tLDhPGthZNkgamoLwfEF5BA1zVR6NqZK6h809sxnheyq70bWGc9gaIy1\r\nHlaM5ptMt4r244i9TacUir83epewlapMuaj8zqPX0MEYofKK6jyiICyxUu1h\r\n/XhH2LzqPJ2YtfBhZSk8XHp9ESYUsZ9F73AZ+9zJt9mnnAlXnn+FIYuL5bjY\r\nn9r6xWcujJ+K48JC8oSrcymt3Z3mbZ+MJg+v9zKGIu/mawW7RHb9L6dZ2+Vb\r\n4v9zTohB3JYseClBt8yNhX9jQqrt1kTHh1JofCZG7WMenUkQfTkGIfFcAI6l\r\n9pRuVhgQVpRZjRPa7aR1G8CiNd/ca575UJq28zyEZ/RpAbWhoU/KKbXeEoHo\r\nAx138lqcpSTWVPBfUQvJZUWd2mMI5U6j+L2ZME/NPOnJ2t3B/jJDkOXK70Xm\r\nbi6Yl8v19M9/qWea2ZPOywTw5TuClXt5XY3bSHZq42cbX/sdHQG01n0iL4ei\r\nrKA9l6Rps1sbc/BYPltvCDzHyODLfpW3SWujBRZdHBUxTq/SDk81RWB8VYU8\r\nJJEKiYRy3nWJ4hHZOoziv2wDJm8EHgpZtvE=\r\n=y0aK\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_1.8.5_1667839491881_0.2730993454389554"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "@sinonjs/commons", "version": "2.0.0", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "types": "./types/index.d.ts", "scripts": {"build": "rm -rf types && tsc", "lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test", "prepublishOnly": "npm run build", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "preversion": "npm run test-check-coverage", "version": "changes --commits --footer", "postversion": "git push --follow-tags && npm publish", "prepare": "husky install"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.{js,css,md}": "prettier --check", "*.js": "eslint"}, "devDependencies": {"@sinonjs/eslint-config": "^4.0.6", "@sinonjs/eslint-plugin-no-prototype-methods": "^0.1.0", "@sinonjs/referee-sinon": "^10.1.0", "@studio/changes": "^2.2.0", "husky": "^6.0.0", "jsverify": "0.8.4", "knuth-shuffle": "^1.0.8", "lint-staged": "^13.0.3", "mocha": "^10.1.0", "nyc": "^15.1.0", "prettier": "^2.7.1", "typescript": "^4.8.4"}, "dependencies": {"type-detect": "4.0.8"}, "gitHead": "de03b23999bd5148358afd69371775266d8cec7e", "_id": "@sinonjs/commons@2.0.0", "_nodeVersion": "16.14.2", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-uLa0j859mMrg2slwQYdO/AkrOfmH+X6LTVmNTS9CqexuE2IvVORIkSpJLqePAbEnKJ77aMmCwr1NUZ57120Xcg==", "shasum": "fd4ca5b063554307e8327b4564bd56d3b73924a3", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-2.0.0.tgz", "fileCount": 54, "unpackedSize": 38629, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAwHE1Wp0EXDiRkGjJDfzv1JPpPLkeUmAJXnrZ6ojXTqAiEA83UaFBK04/aM3Vcb5PogOX8lDbZwKKuQffWiyFnC1xw="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaTdBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSqRAAgHP9fvCt8QnfOLmZHaOJnlnNU/c65ysn5ijfc1dgP40p3fFL\r\nHuZ/FVXB5BbYMvJI/D5f6oQ0qokJyWlRZ9qOuROo0mbnvcLV8SyfjTwd4hbq\r\n5V7sdw9Icmt6hHiLsxqSVoSkcwP8Ku3h3wBsJCdtHz6hlZEyS5R1qn3EZaa7\r\nweIjsIEMoX7ullgjgMNCk5Uw53mfgdJMfqCRqHlj7/TmAFCskB1j9J6tWjM/\r\nDnL5CR98elkIOP81+B8ww10LK1kbStuAuEm06jU0brfTEn4ffT9mSZD7YtF/\r\nA77wWnLMw4a5Ptunv0LzeFMIpvNdNKYbbdk3oClYaJZTOxlz0/4Ni2EZYEdm\r\nWTJ1h93oAUvG+HMIp8ouSINn4JzEh4LIqUXiM7jyw2DIbMsnRzZa3srPp307\r\ntzqrDknRSSzoczN2QYSnBiej+/A3O0krEl8AjFpQsWPKw1kGX3dvu3xbnBaF\r\ntOI/l34mTUP7ANGcnetBOYz2LRsLOeK4jIT2zmzuRsibunOeXla/ZnKbnanS\r\nmFdodTRzpJKhxsC1G3zFLhsBliYAWpymGstjkmkaU+7gTZX/XfKbTbbSh6R5\r\nmWvLdMQZgmPBbquYlS87ou2JyQ3tdCan3dTR4/j7rLOvfJ4NjPlQlle2F/rn\r\nkYCxpnX9LNaq8MC9JiKSuRg3iG/+sGQAmbM=\r\n=9fAW\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_2.0.0_1667839808988_0.01671860990566021"}, "_hasShrinkwrap": false}, "1.8.6": {"name": "@sinonjs/commons", "version": "1.8.6", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "types": "./types/index.d.ts", "scripts": {"build": "rm -rf types && tsc", "lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test", "prepublishOnly": "npm run build", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "preversion": "npm run test-check-coverage", "version": "changes --commits --footer", "postversion": "git push --follow-tags && npm publish", "prepare": "husky install"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.{js,css,md}": "prettier --check", "*.js": "eslint"}, "devDependencies": {"@sinonjs/eslint-config": "^4.0.6", "@sinonjs/eslint-plugin-no-prototype-methods": "^0.1.0", "@sinonjs/referee-sinon": "^10.1.0", "@studio/changes": "^2.2.0", "husky": "^6.0.0", "jsverify": "0.8.4", "knuth-shuffle": "^1.0.8", "lint-staged": "^13.0.3", "mocha": "^10.1.0", "nyc": "^15.1.0", "prettier": "^2.7.1", "typescript": "^4.8.4"}, "dependencies": {"type-detect": "4.0.8"}, "gitHead": "280b51059b367386575f8d21b415d99417ae2de6", "_id": "@sinonjs/commons@1.8.6", "_nodeVersion": "16.14.2", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-Ky+XkAkqPZSm3NLBeUng77EBQl3cmeJhITaGHdYH8kjVB+aun3S4XBRti2zt17mtt0mIUDiNxYeoJm6drVvBJQ==", "shasum": "80c516a4dc264c2a69115e7578d62581ff455ed9", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.8.6.tgz", "fileCount": 54, "unpackedSize": 38716, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDvpey9hhfFRh/ul8ROhfB3CYGmwrSw1FgsfmwZufnHFAIhAPDNSen3q8jdped+0ibf0MuDKf9G20/nw0cgV/NPQRoz"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhPHgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmquAw/+JElUsW//tKYrBADj4Joj8aNcZjFl21nSX9fu6qQttYRCN0RU\r\nnuUEPUOS6CSiUucgd6DOoe/gC5ue8sWDVB+46J/53LtJzEumtbonBTMoxuct\r\nDXpIo03zxHFpvyZ9non5KDDVI4/UEh+D5Sai7AXCv1aH7V+yJkpgm/gvAKjZ\r\n2D2/ZqfpyKXuRRHaHVS5Evp3drqhxeBL7NhWXuCIlmIup6AiqVSetndD7aDk\r\nhxVLswLFImIo9+Eb2/2O2x/KWZeonK9Ro3YUcDRxUaQxov1GnSxY5DF9Lqps\r\nwvYDhx6hKcEfZvEfzAJtUUbNdc8OWx84OgACbXorNAorrVZpvzb0tZqyitUK\r\nv2POXDR5AO9tS9iAiP2J48Ig1eO3Nt3RwdUh8lPcyboDsjQ8Mz42uABZaPc9\r\nRYq/dHWns9cn4GxscFtTfU8XwEZMWegeT25LwV/FLtBcahDV9Cck0IT+0ecY\r\nDlgb72qs2J4hatgDW8xB6lDwgn+itZmu9QvzVvLVTDqN3kRek5oavnC41lp2\r\n2nuV+AweYOr5j1FvSly1U/xNtDL+V4eN+3TI7L/dAq/PCHpjfzfgtCwTu+qi\r\nFX37s5V7TZW7Uyfy2mBFrRIhEP0N/OD1PJk6VXgwZ9aHs+WdqLzU02AdjeDf\r\nT5E2i5mILw+tGn/HaC2J+YnZmGiUW4iZ3lM=\r\n=ulPt\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_1.8.6_1669657056142_0.7237354751039231"}, "_hasShrinkwrap": false}, "3.0.0": {"name": "@sinonjs/commons", "version": "3.0.0", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "types": "./types/index.d.ts", "scripts": {"build": "rm -rf types && tsc", "lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test", "prepublishOnly": "npm run build", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "preversion": "npm run test-check-coverage", "version": "changes --commits --footer", "postversion": "git push --follow-tags && npm publish", "prepare": "husky install"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.{js,css,md}": "prettier --check", "*.js": "eslint"}, "devDependencies": {"@sinonjs/eslint-config": "^4.0.6", "@sinonjs/eslint-plugin-no-prototype-methods": "^0.1.0", "@sinonjs/referee-sinon": "^10.1.0", "@studio/changes": "^2.2.0", "husky": "^6.0.0", "jsverify": "0.8.4", "knuth-shuffle": "^1.0.8", "lint-staged": "^13.0.3", "mocha": "^10.1.0", "nyc": "^15.1.0", "prettier": "^2.7.1", "typescript": "^4.8.4"}, "dependencies": {"type-detect": "4.0.8"}, "gitHead": "6079ac1955057bc589697f265bdebe541c636211", "_id": "@sinonjs/commons@3.0.0", "_nodeVersion": "16.14.2", "_npmVersion": "8.5.0", "dist": {"integrity": "sha512-jXBtWAF4vmdNmZgD5FoKsVLv3rPgDnLgPbU84LIJ3otV44vJlDRokVng5v8NFJdCf/da9legHcKaRuZs4L7faA==", "shasum": "beb434fe875d965265e04722ccfc21df7f755d72", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-3.0.0.tgz", "fileCount": 54, "unpackedSize": 38629, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGwzX9a6Qabrke+hiEaqr5jpLh/K7as5zg7yiHouzuJSAiBK+8uVZN4O3IsZ6QI1w8vNSPFu3axR7BzXMw/JHccDlA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhPOuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjrA/9GHiZJ1hWqaACgTEm1CiZU/FI2kEBan3MbkdXUrQdDKv3GIp2\r\nh3BSLYIgh1LNF66WwXvS3Ss3q72hYZ2P3/xtmrnjBE4NrT+dzIzo53EG5Kq7\r\n0HvJWMI8s03mWdoNsnFB6JSOnsjdGvByJVg9H9gBPcOaHHanSlbRywE2LH9X\r\ntDdUZ4uZ+F76IrVUk6Wl7s6J/JgoPVHhgflCJlpulPmCWOtlkcjMu+g1ulq0\r\nLtIGBD5U6SqLr/CCGwkEcIc5kyckPQbwMG5RjRwXXIgicsq8I1uXgRjzjs8E\r\n1Ilp1enzN7gLdPxxr4qdVs/OEPoW7j7ITM6VoPyxYAe9qrNukkWm9R8eiAO7\r\nLNVw61T9YDemQprbF1WKcKDHkuo5JTGZ+UNBnFG11QVWkumTOpGomlJkK5DY\r\n/rZCBHG85XOpq2G13NER1v+SwLnCysIWc6Xok3LTPC2C8nDaTbuoY8DzsJVf\r\nDniE8vznCx/93sk1g00RDgjIq/Ms36ZdqrSji42FNpNaM4Do9/soV622ckvo\r\nMXJUR6xGq/unPMok+Wwa/gPlVwJhZum4X/Y5YhrcMyPbH2a6scxAQcK76q4X\r\nQ38rPLdNSY8yNp4M/ibWT6V88xWplgv1cMTXjcqFr1AEb1aaL+fHPr+VqRni\r\nV9qRzSX6AOr5AEhVcGpv2pg2dDh7wMu/LsI=\r\n=PuPN\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_3.0.0_1669657517794_0.9806894083645947"}, "_hasShrinkwrap": false}, "3.0.1": {"name": "@sinonjs/commons", "version": "3.0.1", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "types": "./types/index.d.ts", "scripts": {"build": "rm -rf types && tsc", "lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test", "prepublishOnly": "npm run build", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "preversion": "npm run test-check-coverage", "version": "changes --commits --footer", "postversion": "git push --follow-tags && npm publish", "prepare": "husky install"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.{js,css,md}": "prettier --check", "*.js": "eslint"}, "devDependencies": {"@sinonjs/eslint-config": "^4.0.6", "@sinonjs/eslint-plugin-no-prototype-methods": "^0.1.0", "@sinonjs/referee-sinon": "^10.1.0", "@studio/changes": "^2.2.0", "husky": "^6.0.0", "jsverify": "0.8.4", "knuth-shuffle": "^1.0.8", "lint-staged": "^13.0.3", "mocha": "^10.1.0", "nyc": "^15.1.0", "prettier": "^2.7.1", "typescript": "^4.8.4"}, "dependencies": {"type-detect": "4.0.8"}, "gitHead": "4f87da4eda7bfc3ec81296bf22524240ce562cb2", "_id": "@sinonjs/commons@3.0.1", "_nodeVersion": "18.12.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-K3mCHKQ9sVh8o1C9cxkwxaOmXoAMlDxC1mYyHrjqOWEcBjYr76t96zL2zlj5dUGZ3HSw240X1qgH3Mjf1yJWpQ==", "shasum": "1029357e44ca901a615585f6d27738dbc89084cd", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-3.0.1.tgz", "fileCount": 54, "unpackedSize": 37955, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDkGpcSVvsrMyLfF2lpcOqIcFdOkk3oP4WhwEv/UOpUugIhAKjKMtzgM92upCh0wCjxYenxG8Wj8zwn86mDC/MzgWrk"}]}, "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_3.0.1_1705761728921_0.5850544122012746"}, "_hasShrinkwrap": false}, "4.0.0-alpha.0": {"name": "@sinonjs/commons", "version": "4.0.0-alpha.0", "description": "Simple functions shared among the sinon end user libraries", "main": "./dist/index.cjs", "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.mjs"}}, "type": "module", "types": "./types/index.d.ts", "scripts": {"build": "rm -rf types && tsc && npm run bundle-cjs && npm run bundle-esm", "bundle-cjs": "esbuild lib/index.mjs --bundle --format=cjs --outfile=dist/index.cjs", "bundle-esm": "esbuild lib/index.mjs --bundle --format=esm --outfile=dist/index.mjs", "lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.*\"", "test-check-coverage": "npm run test-coverage && c8 check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "c8 --reporter text --reporter html --reporter lcovonly npm run test", "prepublishOnly": "npm run build", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "preversion": "npm run test-check-coverage", "version": "changes --commits --footer", "postversion": "git push --follow-tags && npm publish", "prepare": "husky install"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.{js,css,md}": "prettier --check", "*.js": "eslint"}, "devDependencies": {"@sinonjs/eslint-config": "^4.0.6", "@sinonjs/eslint-plugin-no-prototype-methods": "^0.1.0", "@sinonjs/referee-sinon": "^10.1.0", "@studio/changes": "^2.2.0", "c8": "^9.1.0", "esbuild": "^0.19.10", "eslint": "^8.56.0", "eslint-plugin-compat": "^4.2.0", "eslint-plugin-jsdoc": "^46.9.1", "eslint-plugin-mocha": "^10.2.0", "husky": "^6.0.0", "jsverify": "0.8.4", "knuth-shuffle": "^1.0.8", "lint-staged": "^13.3.0", "mocha": "^10.1.0", "prettier": "^2.7.1", "typescript": "^4.8.4"}, "dependencies": {"type-detect": "4.0.8"}, "gitHead": "b35b006a6338ff21148844f8052e0f732656128b", "_id": "@sinonjs/commons@4.0.0-alpha.0", "_nodeVersion": "18.12.0", "_npmVersion": "8.19.2", "dist": {"integrity": "sha512-D6UsFb7vj6k/iDBr3TVks/9BHc9vvClRK7JbM3NtxmmV/r2/i/6uh8Z78y4NGMaj2JTRjN2Hy3TTMB5+gPaK5g==", "shasum": "41fa7570fdc19265981f48ad8520d79703be4ecd", "tarball": "https://registry.npmjs.org/@sinonjs/commons/-/commons-4.0.0-alpha.0.tgz", "fileCount": 44, "unpackedSize": 63987, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC894QFNG2L8njhlPFaz7NbiXI99HU7gbc0S3lG7XVj1gIgMHX6W1gto8y8wbUmu/EwSv7OXE5gOiVx0J2NHSCiATc="}]}, "_npmUser": {"name": "mrgnrdrck", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/commons_4.0.0-alpha.0_1705764051689_0.3437045568854229"}, "_hasShrinkwrap": false}}, "time": {"created": "2018-08-03T06:49:41.343Z", "1.0.0": "2018-08-03T06:49:41.538Z", "modified": "2024-01-20T15:20:52.068Z", "1.0.1": "2018-08-05T07:47:53.608Z", "1.0.2": "2018-08-09T07:32:29.666Z", "1.1.0": "2018-10-27T10:43:29.924Z", "1.1.1": "2018-10-27T10:50:25.516Z", "1.2.0": "2018-10-27T12:31:35.617Z", "1.3.0": "2018-10-28T13:05:01.536Z", "1.3.1": "2019-03-01T07:10:43.034Z", "1.4.0": "2019-03-06T14:59:49.001Z", "1.5.0": "2019-08-19T20:58:07.066Z", "1.6.0": "2019-08-19T21:07:55.401Z", "1.7.0": "2019-12-18T15:34:56.360Z", "1.7.1": "2020-02-19T11:13:41.352Z", "1.7.2": "2020-04-08T11:12:14.821Z", "1.8.0": "2020-05-20T14:29:53.521Z", "1.8.1": "2020-07-17T08:49:05.066Z", "1.8.2": "2021-01-13T11:59:13.094Z", "1.8.3": "2021-04-08T07:37:33.231Z", "1.8.4": "2022-11-02T17:06:15.349Z", "1.8.5": "2022-11-07T16:44:52.030Z", "2.0.0": "2022-11-07T16:50:09.203Z", "1.8.6": "2022-11-28T17:37:36.337Z", "3.0.0": "2022-11-28T17:45:18.037Z", "3.0.1": "2024-01-20T14:42:09.135Z", "4.0.0-alpha.0": "2024-01-20T15:20:51.832Z"}, "maintainers": [{"name": "mrgnrdrck", "email": "<EMAIL>"}, {"name": "fatso83", "email": "<EMAIL>"}, {"name": "mantoni", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "benjamin<PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "description": "Simple functions shared among the sinon end user libraries", "homepage": "https://github.com/sinonjs/commons#readme", "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "license": "BSD-3-<PERSON><PERSON>", "readme": "# commons\n\n[![CircleCI](https://circleci.com/gh/sinonjs/commons.svg?style=svg)](https://circleci.com/gh/sinonjs/commons)\n[![codecov](https://codecov.io/gh/sinonjs/commons/branch/master/graph/badge.svg)](https://codecov.io/gh/sinonjs/commons)\n<a href=\"CODE_OF_CONDUCT.md\"><img src=\"https://img.shields.io/badge/Contributor%20Covenant-v2.0%20adopted-ff69b4.svg\" alt=\"Contributor Covenant\" /></a>\n\nSimple functions shared among the sinon end user libraries\n\n## Rules\n\n-   Follows the [Sinon.JS compatibility](https://github.com/sinonjs/sinon/blob/master/CONTRIBUTING.md#compatibility)\n-   100% test coverage\n-   Code formatted using [Prettier](https://prettier.io)\n-   No side effects welcome! (only pure functions)\n-   No platform specific functions\n-   One export per file (any bundler can do tree shaking)\n", "readmeFilename": "README.md"}