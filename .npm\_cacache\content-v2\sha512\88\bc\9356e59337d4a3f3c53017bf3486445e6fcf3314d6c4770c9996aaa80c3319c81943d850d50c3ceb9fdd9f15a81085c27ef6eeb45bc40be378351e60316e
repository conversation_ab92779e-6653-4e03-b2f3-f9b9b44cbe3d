{"_id": "emittery", "_rev": "40-f61ffad85cd8498601bbc55910066149", "name": "emittery", "dist-tags": {"latest": "1.2.0"}, "versions": {"0.1.0": {"name": "emittery", "version": "0.1.0", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "dist": {"shasum": "56168835bd4f19404a211e48e27f1dd1d812612c", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.1.0.tgz", "integrity": "sha512-b5qyMulWLI/PiQvB0YNTqDq7iPWVP0OHRtR17oRCCDiLRKJIsAr64EZl5+To7Uihgld8a/IA1SwtTjzrkQpSMw==", "signatures": [{"sig": "MEQCIHtp+7nsFiN4/WEXyjcOYLl/o/dd3XwLsjouApHZv+W7AiB8wCYfk0GX1A7m1n08RUdvbFY6dU0ZvWwPaR3nsryDJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js"], "engines": {"node": ">=8"}, "gitHead": "1be929d47f6226ac513476061bc867957ce15930", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "8.9.0", "devDependencies": {"xo": "*", "ava": "*", "delay": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/emittery-0.1.0.tgz_1511817492647_0.355037200730294", "host": "s3://npm-registry-packages"}}, "0.1.1": {"name": "emittery", "version": "0.1.1", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "dist": {"shasum": "26ac3c9e0c2de0977e6bf6de3c98b45cec404e08", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.1.1.tgz", "integrity": "sha512-YEHPnTwz+V8wrzszb2C6EI+CV58lOuXb0+fPZLQPuvuDuK9AU/3ttDg52k8TytbWMbYXM2sGRo1c0FVSQ02dRw==", "signatures": [{"sig": "MEUCIEe0MnzDyMjMe2g/e366NqPhKv2nTMGkvFGBGhQUm6AwAiEAq/lo2jkZpwu5mH+IkcrI9Y70lLcq8FySfMfQVSbKP1E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js"], "engines": {"node": ">=8"}, "gitHead": "6248f777e01367dce1e8628ec2719ed5fd7c48cf", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "8.9.0", "devDependencies": {"xo": "*", "ava": "*", "delay": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/emittery-0.1.1.tgz_1511894200117_0.7775801541283727", "host": "s3://npm-registry-packages"}}, "0.1.2": {"name": "emittery", "version": "0.1.2", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.1.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "dist": {"shasum": "d272967368f7cad542a5357cd8d49128669c53e9", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.1.2.tgz", "integrity": "sha512-E2cNU5Hdb01OKd6eWsXGE2KqiXcDrGnysIFRCoe7wm1O9JiAYdVI7YddbjyP4ptR80/clCelTma4Y3uiNobYyg==", "signatures": [{"sig": "MEUCICaoHWqEU4o7i+tosu1WEinnAGES/zNBpJ8/Q3d37jJKAiEA0rZ244NtnvXkJI+hnU+1A+HuAKmlNljiNJMs9n/QN90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js"], "engines": {"node": ">=8"}, "gitHead": "548d9a5043244c9c9497dc093a7a6aa8088c3bb9", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "8.9.0", "devDependencies": {"xo": "*", "ava": "*", "delay": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/emittery-0.1.2.tgz_1511949398376_0.6574320155195892", "host": "s3://npm-registry-packages"}}, "0.2.0": {"name": "emittery", "version": "0.2.0", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "dist": {"shasum": "1dc7a7ef2d0beb38623bafb5eb654a38dcc6df9b", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.2.0.tgz", "integrity": "sha512-ubE6YRfH5jeTRrrhpdFcmqT+zdLB1bjsjgTGKeLl9XP/MqB1vFkBIL9ZjEzY9ZWjCo7FM+zjZo8eF3vXRJ6c9Q==", "signatures": [{"sig": "MEYCIQDaOLisZ/PrGD2sJAiudWDsUgOctUObpsqUziZ3WGXVzwIhAN2tTJCOocj5aWdv9k1/exUG0CcIGp1bt1YjtNU0hCtp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "babel": {"plugins": ["transform-async-to-generator", "transform-es2015-spread"]}, "files": ["index.js", "legacy.js"], "_shasum": "1dc7a7ef2d0beb38623bafb5eb654a38dcc6df9b", "engines": {"node": ">=4"}, "gitHead": "26d3c76014c4250a99499efd624a6b88419e4789", "scripts": {"test": "xo && ava", "build": "babel --out-file=legacy.js index.js", "prepack": "npm install && npm run build"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "4.8.4", "devDependencies": {"xo": "*", "ava": "*", "delay": "^2.0.0", "babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-transform-es2015-spread": "^6.22.0", "babel-plugin-transform-async-to-generator": "^6.24.1"}, "_npmOperationalInternal": {"tmp": "tmp/emittery-0.2.0.tgz_1511983626817_0.2340813591144979", "host": "s3://npm-registry-packages"}}, "0.3.0": {"name": "emittery", "version": "0.3.0", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.3.0", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "e6dcedabae804b5478c760335ecbbaf159da645c", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.3.0.tgz", "fileCount": 7, "integrity": "sha512-Bn/IFhx+BQIjTKn0vq7YWwo/yfTNeBZMqOGufY5FEV07tbwy5heDROFDCkMO2PcO5s7B9FDDXZc+JGgl6KzBOQ==", "signatures": [{"sig": "MEUCIB14ERZul2YeD8uIXFDvDB1jn9TQoEW1bmc7p4UiUmFlAiEAxlOxv3Awo2y15O4/h4hqAPfJQG0D3V0ktky02Kv6zyc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20686}, "babel": {"plugins": ["transform-async-to-generator", "transform-es2015-spread"]}, "files": ["Emittery.d.ts", "index.js", "legacy.js", "legacy.d.ts"], "engines": {"node": ">=4"}, "gitHead": "7b8eb95060e4a95c7d3578f914ca34f4a4891c13", "scripts": {"test": "xo && tsc --noEmit && nyc ava", "build": "babel --out-file=legacy.js index.js", "prepare": "npm run build", "build:watch": "npm run build -- --watch"}, "typings": "./Emittery.d.ts", "_npmUser": {"name": "novemberborn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "8.9.4", "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.3.0", "glob": "^7.1.2", "delay": "^2.0.0", "codecov": "^3.0.0", "ts-node": "^4.1.0", "babel-cli": "^6.26.0", "babel-core": "^6.26.0", "typescript": "^2.7.0", "@types/node": "^8.5.2", "babel-plugin-transform-es2015-spread": "^6.22.0", "babel-plugin-transform-async-to-generator": "^6.24.1"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_0.3.0_1518711261599_0.23412399689669905", "host": "s3://npm-registry-packages"}}, "0.4.0": {"name": "emittery", "version": "0.4.0", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.4.0", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "b5d194e28f89f494aaad7b308d143e52ac9d0b43", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.4.0.tgz", "fileCount": 7, "integrity": "sha512-YGZwdo56upmuh93RYWUXtFzJ5DuWwp0prZ+OU6XTc6WvTSGXlASc6Ws+PwVayelOhORX1ckx5QTbkfs/l+ToUw==", "signatures": [{"sig": "MEYCIQDkP87dkzUrBozAD7l9KgaHaPFO+W2UXTGbICRN7rdeoAIhAO5trfSgytPf3vQCj3VQNxdrTvdYN5+8WOK1B/mGhTqz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGS73CRA9TVsSAnZWagAAs4AP/0DduKd4V1O/D6sM0Qt9\nzKBMd6LERPkKJrkNb9PX4w8UnJSMEvaFyDKycXtB6z8ROR21QXkniKM44YGF\ndX3UMYJJSpkvkIiZEFySFrEwRdC0f5pTpg7V8WXE63d7Lw1WVNgUHGmN3OpM\ncuT9h3slFGUCcRI/sTr4zUHocUSRMjI8omPJFwh06t8JdmXIZut4UxndeYRA\nOJzGke8uaMLpXIuSmnRfjfX/p4fZaR+i8q+JXbT+JnIY7VCAAYy59a79K2ht\npQKF+10ZS0ePJ9wUM8dVvsc6lzTT5RhkoqdyRVIjg3dnGU7NleIyFQfOX1Us\nIuz4kGND/d4TpUBBXDNzk8qXKn+e6hV57WTSZ0Lyh65TNpw0EgLcmUW8g36u\n7I8CJ6xsp0x1gb7w/AbDxsw4AVKFOuJR1VUU8NbXRiCSaJn3Xf3Um6IiFok0\nwsz59R/60YNDahBAYeIMpj1xccE6w3tkVbhHuDErSlqi3uwgBXDqIqbQNJvS\nsFDxoNRmaoLn5oEuGbNixz1Dhxwuy5q/ZTurBmm/I6s8uoDe8TIWZaDPsSSi\nk+/ekrVRhcoB69D3WCu2llkDObhBId+1E04Fya/CH9bmHDcct/bFj3M9kr8P\nBSuQxo22OivJQ4Tg5E4UiS4Ng3LVth5VHbQb3x5mymkPLY8lWaSsIjWRq0KS\nrxT2\r\n=BVPf\r\n-----END PGP SIGNATURE-----\r\n"}, "babel": {"plugins": ["transform-async-to-generator"]}, "files": ["Emittery.d.ts", "index.js", "legacy.js", "legacy.d.ts"], "engines": {"node": ">=6"}, "gitHead": "5e5156a08efd87fd3de5ae427770905018e1b1fb", "scripts": {"test": "xo && tsc --noEmit && nyc ava", "build": "babel --out-file=legacy.js index.js", "prepare": "npm run build", "build:watch": "npm run build -- --watch"}, "typings": "./Emittery.d.ts", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "8.11.2", "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^12.0.2", "glob": "^7.1.2", "delay": "^3.0.0", "codecov": "^3.0.0", "ts-node": "^6.0.3", "babel-cli": "^6.26.0", "babel-core": "^6.26.0", "typescript": "^2.9.0", "@types/node": "^10.1.2", "babel-plugin-transform-async-to-generator": "^6.24.1"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_0.4.0_1528377077719_0.6522371971868492", "host": "s3://npm-registry-packages"}}, "0.4.1": {"name": "emittery", "version": "0.4.1", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.4.1", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "abe9d3297389ba424ac87e53d1c701962ce7433d", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.4.1.tgz", "fileCount": 7, "integrity": "sha512-r4eRSeStEGf6M5SKdrQhhLK5bOwOBxQhIE3YSTnZE3GpKiLfnnhE+tPtrJE79+eDJgm39BM6LSoI8SCx4HbwlQ==", "signatures": [{"sig": "MEQCIGkJF5NOp2B9Qr+6dndFs/JHtUgAth4xMs34Kx1uCDXWAiBa1uKgPEgFv5krYe4hPnBMG2MZp4UUsmOXJprYYe0GJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20333, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbR4CmCRA9TVsSAnZWagAAh1wP/R1Kf0DJh8hIBsSBNp9K\ntM5tKKM4lKXgoX/qaEfSHBr7PprPBTlpONyODGn7oFRgGVA4g0fiUe6Msvjx\nPnDozRubQEjeePZQcIbhQ9lZPnLPgt51n452bHB90DPOe609/TXrTIoUVqUX\n3v4D/wl/1XSGpboqFLItkLcO6qf6sLcNGkv2dp9UvAq1Vqsf9y0/+AXzi0nw\nHYPL/lYgAkln0t/ZXJe2KlXqyArnteWxPoGJRjI931yTlmV9IMFVVsbCOMLb\nFnaxjU3R0xuaT0spMKjuwcE98xDqfWIYcSuz6/73saycShI7z3HwGh1Lb1wr\ncJlpbDS92EK/6l9NlxKhsG/jSN2xDtGbYCMEzl72L++XxOTNAvJf9NkVka2s\n58bprrXXWkrLLGXRmVHoZo6uysKbkheLUyiR1w9zROYM+eM8A5r6RQUVeIos\nhFmE3JiTo7y1oN07bvzLLQdAHmdKTmWzXA+3Olu/p3rEPe5O4Y9SjuGl8h16\neaH2TMR/QTEn5sCQKSgDRwu3S4fgBF4rkXcb2IqayhNWPQKqaRp7BeKEjGwx\n90Kvq0GtdW8qDegCgsBMlD26G/hK3oy8l/5Dk5eA4ql3BGV0zhV2t3BRT8yH\nObeVpjpBpZDnDE1o7U7hxUEnFTvAUzl/9Mhot8/cZq+DghvZefT4oKC/JhhB\nOJuE\r\n=8sEH\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["Emittery.d.ts", "index.js", "legacy.js", "legacy.d.ts"], "engines": {"node": ">=6"}, "gitHead": "a5d38ece06272aeecd83107e4ff6819c7fe5993f", "scripts": {"test": "xo && tsc --noEmit && nyc ava", "build": "babel --out-file=legacy.js index.js", "prepare": "npm run build", "build:watch": "npm run build -- --watch"}, "typings": "./Emittery.d.ts", "_npmUser": {"name": "novemberborn", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "6.1.0", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "10.6.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^12.0.2", "glob": "^7.1.2", "delay": "^3.0.0", "codecov": "^3.0.0", "ts-node": "^6.0.3", "babel-cli": "^6.26.0", "babel-core": "^6.26.0", "typescript": "^2.9.0", "@types/node": "^10.1.2", "babel-plugin-transform-async-to-generator": "^6.24.1"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_0.4.1_1531412646641_0.4531685657065383", "host": "s3://npm-registry-packages"}}, "0.5.0": {"name": "emittery", "version": "0.5.0", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.5.0", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "d38c565bcb2eb5b0bc00c3a595227607b6d3857d", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.5.0.tgz", "fileCount": 5, "integrity": "sha512-NTvZ9CPTp4ehb0MSWHGuvMUDEDSRXBuuA9vt8pUfJ4afU5EqakJsuGPwnhbw/smOqmFdtgFcziIeYZ9uQleSOA==", "signatures": [{"sig": "MEUCIFj6/Y7J7CxuZR/+ZWc7mCuiolAEZoqnTUrQc2Oi71z4AiEAjQZ7z08Ly4uSFOAcgsPIovLU1oXCXC9WVI7udhutu7Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25771, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdgOzjCRA9TVsSAnZWagAArccQAIj7IXnjFNIdB62Ohbn9\n/fSJxOsG4+bYS6iK4Q2GaklXGTqKCFolNi6yFGUZO6JgN4MjwjDYQ1i8LK+T\n09bHZOlR5PNQD8awW57s8tSklYyZtjf+h6X8z0p6uqTHqMgq51DyT+/AZ0XH\nnQq+xV2/zs99FiK3bSXRH8QKaEpk53bfGWSgwGlFBX7fxpieUVX7s/bqB8L+\n0mAkomoJcPewQXmlbaR17qkYWXW1Cgtf7DyS8UhzxJDgtYhiXuE4CLq3H30I\nle6gPscZyJpORAgq6rKc0LoZhg9QkaV+qJsLeIW5G8aYY/Cf74g8RFcUsQ8/\naaXo1R4V3ExJdo1HINPQ75msi5oACEl+vxJyRRTuRGzxqiU5/M5N2ix6pmuY\ntrBXMap97vGM1RMgHVG0Oq5HEGK6Hj8Nx8zUexj+vr1KHIdebvCXf8xU8XeC\ncQmx5gIMmtXkR+TWVnm+dMxdUJC2pwz0PxmkZNSCGiafLYxtQ58QCEHBNN34\nx+NA54nZMnui538RqdpOzTHCpY7D32N0InCHP6Qeq9+S1mIWh7c2T1oVtmyo\nFA2n7OIaJk4cM2BBWdMP9UKEVImbUqCPoy3zD5U88k7qE9h2J7A6uTuL9pfP\nTi7WT4qBRY9h4GRsReopVgBp7uA9eh6r1dbboFQvsS0OeNWt9M0HOMXUgvlE\nN+wD\r\n=ZDQ0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "ca130470d4fa827bd783cfe3c9fea2e2e1f48c8e", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "12.10.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.4.0", "nyc": "^14.1.1", "tsd": "^0.7.4", "delay": "^4.3.0", "codecov": "^3.1.0", "@types/node": "^12.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_0.5.0_1568730338647_0.24966302857897316", "host": "s3://npm-registry-packages"}}, "0.5.1": {"name": "emittery", "version": "0.5.1", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.5.1", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "9fbbf57e9aecc258d727d78858a598eb05ea5c96", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.5.1.tgz", "fileCount": 5, "integrity": "sha512-sYZXNHH9PhTfs98ROEFVC3bLiR8KSqXQsEHIwZ9J6H0RaQObC3JYq4G8IvDd0b45/LxfGKYBpmaUN4LiKytaNw==", "signatures": [{"sig": "MEQCIFe1j40hp8iUbOnvQ7ATofp8awZlS/lnoAEGtFlUEYIjAiBMbU9RMZe/1A9Rx0Zc7M+omN1frt6J5smqwWHlnzziUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26089, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjPNNCRA9TVsSAnZWagAA8wYP/2HvpMyxZa1FLX0G3T+3\nwo+etM0orW25qEwmN4ZxQhe+ils8SsgpbzEnp5MCN974otR0oZOJTXz+up1v\nxx7bPonQLTHjyZp7AiyK36cXOjQyoOtgvNclPYfkbyOQwHUeGOqqhfR+4dd0\nYfrtlvHqWLMyyeQ4wmxtWBdaJzn0BwSwVB6b7stAlxkzmS0nHpg+80fB7yXs\nLgSFHLcCEhXBmHA4SmcMWoixHp+h5qQA5Yce3GVpd3h8Wj/YkpSYAAGvXYY4\nNysoRwUUYMJxNeBmCVgQ4mYCwWk4hhODwC/V6to+aaQ7p852NNY8PKgD3Rfq\nt2m31q4ntVF5bPCouFibuaseU+ZlowMkAcwZYCMce5GbML7pOo5So6oILKoB\nCRFF3oowFV3bFtxjJdM+h+55jq9jwJ1PgEat+uSUqoK7WTs14axHa7Jo0eEq\n4od7IwzhW6vckfQsSmjT2EG8VvglrGMBvsblt8y2uuJXUCE07FAdXdnPJ6ku\nzlah0kkeBJVjIO61V/d9j1qPsXVfi/g6ahj1bnLzCeNNM3r/xsGZKr3F/CQS\ngzlmrONPMzGi6fIMaQGD/obi/XQ9N23YAexbSTpjzw1TS4EuqtwOi6A5P7Qe\nLPTem+dyGFLC4UdaMOj3XHZ4yVASn77pgP0qCeI5DOiI8/wBcoYBk/pffbu0\nxLOS\r\n=FUtx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "3334cff4daf83bfcf7054d35edc51957e3f70f8d", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "8.16.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.4.0", "nyc": "^14.1.1", "tsd": "^0.7.4", "delay": "^4.3.0", "codecov": "^3.1.0", "@types/node": "^12.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_0.5.1_1569518412612_0.9865897044311112", "host": "s3://npm-registry-packages"}}, "0.6.0": {"name": "emittery", "version": "0.6.0", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.6.0", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "e85312468d77c3ed9a6adf43bb57d34849e0c95a", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.6.0.tgz", "fileCount": 5, "integrity": "sha512-6EMRGr9KzYWp8DzHFZsKVZBsMO6QhAeHMeHND8rhyBNCHKMLpgW9tZv40bwN3rAIKRS5CxcK8oLRKUJSB9h7yQ==", "signatures": [{"sig": "MEYCIQCsoRBJJluP3bx4be4QioaZfYImG1GHq/3CTMK7m6ibegIhANpfxUgQ0yFQw6900O+LCrcFM9PCvE2y62Jg2g/9xr4U", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeZf3aCRA9TVsSAnZWagAAYqgP/jgOI1uhZkdpaUiX63/s\n7mDClz0QochmjL+C00kv0mP9D4tlInc62fQa7ML5TBVGxsQ7/3bfoYS2CZ48\nVO8iRT65OxBI46ldlYIOYuppXjunx2F7vFyhbVovXoF0TT5ouxGbxOfYYXD1\ngWkWkMVnEwvoztUhwPyh5W8zrZQQMtqADq6kPOqB9gUEhw15tVuYiozE59It\nDbEqpqMvnoeM7DZyw39mFAE6464aInOymewW5O4L1HTcr5k0S5xiYf1BC3ZB\neRMnM3DIJca5j/sO59nZOI5PzQKTBntysQOc46h2geY9KoSfW85RWto6lLAY\nc7C4YmB6mAz3TYBJcpSSpLQqxNl3iIncC9NYbJgecfqTRZzgyKdIuInuXL7I\nW0U0cZZfJIvIQQPk1rq+D3Br4cOCbSdcL2nYWGYSu01fEhN2ZmYW2UM5w7qk\n19L4CPyA+SA/x/Ey0yCqJbx2eiF6IyHtAbTz9bvpKtAAETrchgHKhf6qMssK\nSMdZCC3duvdDimRqJvrI5Z5OZ7Tn4xIXnyiqPJ1TQPbQ1ldCrmx5bj/8ErnQ\nbbKqyNHhylYlbl/GICNr3ttHV47UcnVBmM1OtLi9NxrpeFgL5/03CbSMrP3A\ng4oIbw07QzpAsE9RS1sOj8HGFjzAAUEp7pfKnbtcMUnsWCoImAbLf71YX7sI\nIVzN\r\n=HI9a\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "gitHead": "540339bcb3350ddb3f442a2930bc9c4c302367a9", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "6.14.2", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "10.18.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.4", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.11.0", "delay": "^4.3.0", "codecov": "^3.1.0", "p-event": "^4.1.0", "@types/node": "^13.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_0.6.0_1583742425647_0.9569512157636983", "host": "s3://npm-registry-packages"}}, "0.7.0": {"name": "emittery", "version": "0.7.0", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.7.0", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "0f0789ea90e03f3de7865feb806e4f0916d16c93", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.7.0.tgz", "fileCount": 5, "integrity": "sha512-/kshvS+tZaggOPQDLGzXopumRRIzxciGILDlYTGIU+PmqbSfhn4wDVphFPry4H+2TNl2QxLduexPhxcWLULA5A==", "signatures": [{"sig": "MEQCIFiH+MQdvjPxqmeEaQAfDYNjEplZ5PqX9jiQ+6W3JHR2AiAA/9cxtndaOIbv/U+23aHyG8rYwiybr4B2zGq86EUCng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34711, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeuV5ECRA9TVsSAnZWagAAbqcP/3k6D1+Du4+Vb2e5EPnC\nPAVtl5/7Pgcm3gkQLwdjg5/ygpJaXqp2cxWoaHRG5+HNaYNGyIbDQocj4xZL\nPWVTnuRX32hxIWcwNzY3N1o0ZRxo86ZGO0W3Q09vYfqTLit9kXDGxa71iwqh\nrALRwC2BDQiDJcam4dmw5r2tezPmpYp/VJlOJVfXmMYz2eMBwn2jVE/zmohC\nh5iIrlDvMKpbdgC8wNrO/b8k61GN/9nuyxhK3WKzpZd8/F3q0dxAGJoCqCY2\nB1bA+7OZllcYLH8S0TD2fNuUMdruXFhbxVGSW8IRowpjxWJAr84breJ6rVJh\nlCFwYotG1+YdSNN/ijTEFZO+c8Xm7JcW7AbtbFbuVQyUQLu3BCNHH3VvD+mR\nQUupjdWpuvRc5WJeV/6IiOHp+yj5X2+7TnodsBdcZTL24ygkBgCxi+HkkwLV\nkuE0cBF+vFuHx8HNYwUJrB+IlND4NEBzF6ycdXMCb2OVtL36jMYzwntsQABB\nUhmjdZdfoHJZKe8WaWCol5yA5EbgDfP9eJMe94WSI7WyBpGgJlH19mPuhbRg\nF31OYF2u/kqVJzYtQQnPFwljqYLBWtz8Imeyn9tpd+arbvYiQMjDMsdd4jUM\nQX3niyCmX9BiyYIXnFT5+3cOpwYG2yzMzMs94H/mDfpLQhzRRG6Fv3i3+RCg\nH7w1\r\n=Tk7M\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "gitHead": "e8206618e30d961b9cb98533486a17d9a7fa0c2c", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "10.20.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.4", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.11.0", "delay": "^4.3.0", "codecov": "^3.1.0", "p-event": "^4.1.0", "@types/node": "^13.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_0.7.0_1589206595442_0.5836524014914579", "host": "s3://npm-registry-packages"}}, "0.7.1": {"name": "emittery", "version": "0.7.1", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.7.1", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "c02375a927a40948c0345cc903072597f5270451", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.7.1.tgz", "fileCount": 5, "integrity": "sha512-d34LN4L6h18Bzz9xpoku2nPwKxCPlPMr3EEKTkoEBi+1/+b0lcRkRJ1UVyyZaKNeqGR3swcGl6s390DNO4YVgQ==", "signatures": [{"sig": "MEUCIQCVr0WFDhuOi5SpcOwXsT35GqST9XyAPI/b79J7ZupIOwIgKj4wTG+T1pUXJZKobiLFrU9wUSn3tDHQoYT6JvWyTiI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34777, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfAdt1CRA9TVsSAnZWagAAoDMP/RXZ3ve5zadLUvjyQRyZ\nWkMdXLF+qjjLQHrqdHXt7mS+QFLWEcx1ZLxQb7oHFZ/tV6mbUNC0GiOq1fRt\n38O9/S9ggGRLrIenlGJdSz+3Z7VDXcUXG05Kw9hxzd17Tv6+39VNQn99hFTk\nCoAh/j1Osm+339uVqPi5+NIbn+hDdYYjLW7/th0DIEZ/6qIjFfn5ByR/d1sl\nwgMRGdQNWir0zKGtya9Xz7WeBaDuEBbgw1+GZXRwplOQYwxWx+OONL6y81lW\naWYsjpbLMGft6Rkv83nuoGan/QuCPvWA7LpWZTnEyHGUNBGRIYN+OT/oYogK\nsOrYeSMlZTX7o1Xi74PMlSvNFc8loFE1sFwbwUZNPVT00C8rp04v3NzDnk/m\nhci7cnykK+I+adhV57enMnfVXBT8wk8ANC83qIHDUz1urdL14jGsGV3jq3DQ\ntlnWsn/QlqmUZILJnj91m9Qq6TUFO0Xm/vZNRUuUs5Q83Z3MzKWVTrXN9DCO\nP/YfDh0EJ4Yc185opShCHJ8vHmuVnAR39qlyM2BAJNSsb7/fwMNxecft8wWm\nSA6zLN/aXNJc0n8wvmKXFOBtsYYKz16dIWNe5DVskdEU6QznSikZJDu2xStF\nlLCoWVGtenKTzAnJd/emm5M3Zypj/7tVMWhBgtvh6WVuH77EE2t38fOcyVJs\nR2RJ\r\n=YHuE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "gitHead": "1afed6ab1f8b22a6fa36448a23ea3b829431b5f9", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "14.5.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.4", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.11.0", "delay": "^4.3.0", "codecov": "^3.1.0", "p-event": "^4.1.0", "@types/node": "^13.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_0.7.1_1593957236695_0.024101371620112833", "host": "s3://npm-registry-packages"}}, "0.7.2": {"name": "emittery", "version": "0.7.2", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.7.2", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "25595908e13af0f5674ab419396e2fb394cdfa82", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.7.2.tgz", "fileCount": 5, "integrity": "sha512-A8OG5SR/ij3SsJdWDJdkkSYUjQdCUx6APQXem0SaEePBSRg4eymGYwBkKo1Y6DU+af/Jn2dBQqDBvjnr9Vi8nQ==", "signatures": [{"sig": "MEUCID1ZXpah0yLKihpuBlyAnZ91+QK0fzYRH9dcdoOpRR4tAiEA9cII7pIwWoHpiM+/A5oKnx0KdgiZ/16fbvI4OB4TvIU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34786, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfijV+CRA9TVsSAnZWagAAEg8P/jY31F0S87rVZQRHDB3/\nmjTOGz3EwxfATVqQDY3dQSl7v0D8STxOu4CZCbMqtk2gvAsR7XukyTrmAgMk\n1w0X7IfmzzsC5zxkF6tIPvWJP/ZO6tqPlY1NngweAuKufpy8Gj5WXixoNzJU\nDS80KZNUdBqhVPblNeeQ2mvaNUauZeNu0U2sJEb9KMBgy9gx6fWeg6InTCh2\nEVFEV5DH6HVkh4UBfJvJnW/R700K2kFO5Ry6R5T2m3X1PzOy7/KFIksnNiem\nkiVCrYbZxVKEuctfmnub4htv1iaBJ5x8f3/zwWUfUrnEDWLmG0rbMwS7mfUq\nZx+k+X6FbZdAZbkMwAPG2FPuaIms76Qzu5voAF4fddaFBdC77RHH6DB9JUHN\nECCVuV9sGmdBs+1/hGddBj0MDtgHRsKTaiV/4VwYXfOOA2Yx0TI+u6DRF700\n9Csth5HQKadcl0/JeIqWUoUo35dG9ha66q9MSmucdkaEWeJOfBiOl6EGyaIZ\nZUZ9Xji99tGk/Wro2ZE9hRgKSvVv+Kws4aRwmLqGlGQZyH0JckrM0o3N76Zx\nP1ue0I+HcTicQP9i1caaJ5/zHrLPMDBN/+kSCBs/m9DRTWMGN4HmRqmgXmEJ\nSE4PRLHeCGvPXbPG+SsoMoeQzlw7ogaZjkyiWJX+RFglm9aISw1wd/aoqIff\nGgAY\r\n=2CY0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "gitHead": "b771d536acd89986fda21fd64d0d90dcb2de80c9", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "14.11.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.4", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.11.0", "delay": "^4.3.0", "codecov": "^3.1.0", "p-event": "^4.1.0", "@types/node": "^13.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_0.7.2_1602893182339_0.3802915258306516", "host": "s3://npm-registry-packages"}}, "0.8.0": {"name": "emittery", "version": "0.8.0", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.8.0", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "28e3f2f450bebb969df42b00fd3d6b3de6087905", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.8.0.tgz", "fileCount": 5, "integrity": "sha512-XMGToId3CejfmZg/0qOzdTT3WFuAN8fQYtcKXccabRfCzGiWMSTydMshHGLyx9C/ejMl4nw9tvqrn12QVFPIUg==", "signatures": [{"sig": "MEUCIQD5P4dH1ME1em2yc7hIK8wBrS71Xy4NQfFKZIfh66SUNQIgQxTc6Pb8pljFHLtQVMNPQTflhaiQhBg7QDfbRXIWjX0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35540, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf7B/WCRA9TVsSAnZWagAALGIP/2/IVsFJIyzQs1hw12CI\nvCRb2zKH39gDtl8sF9TAouuzSuoOLdySkAt7Zl5aQqp8LCAldAHcmQCGwSPw\nL8ww69LtkhVlw205G+6XwPlrQZPCjws8Pms5lDklTZz+y64PcQnzR/OQUYlG\nguIMDZGaEVjeoQW8JuyFGB2ZMwQsRpMb1x9B6rg03mR1/ZmjPo022BNfF+Vs\nVkAnWdzq7NN+cfI49GcyjI6V4HNQ2g4+0tsWrSNALDseT+1TzbY7J34+/j0g\nEzgNzdF6ycmzKi83R0Hi43DrbuLyrs+SBDgcMjuCkxkEBPzmNSDzelaj4Puh\nU2ZSl/LeQ8KH8CQ+JzaFXKHvWr82o/RWGh4o/WXJ5deX8xYIOzZiUkzbn5g/\nbg03GFu1GMFDc5/zj2kbM2nUGYqBZ3DbyEZK5Qi0CNRyC9Lqf16OVX38znvk\n/3Q8o9lByXztYzMLTpfStK7oOQJgPBToQX+kqCAshODZ8Bht1KkfKUuxUIMi\nMBCwdVUshDuzCjYWfi/Ww0GE5qw8+OYTgfk6R3o3CopBl0cZeJ+GK1cpV8je\n3/2oxNvt05JxuakwzNbeLu8+FiZHmg2XMnTrlv0f93CWjuqWd1OiuhbjylEc\nFNRltpOSSWb6F6M1D0gERwsH5OA005zMBpYPHFNhQbn0KzNE7ir+XjEiROuL\nsvRd\r\n=OwAV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "gitHead": "cefdb663ac079dfaa2e90733f749ea15860b2b2e", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "15.5.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.14.0", "delay": "^4.3.0", "p-event": "^4.1.0", "@types/node": "^13.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_0.8.0_1609310165685_0.15525182836747575", "host": "s3://npm-registry-packages"}}, "0.8.1": {"name": "emittery", "version": "0.8.1", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.8.1", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "bb23cc86d03b30aa75a7f734819dee2e1ba70860", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.8.1.tgz", "fileCount": 5, "integrity": "sha512-uDfvUjVrfGJJhymx/kz6prltenw1u7WrCg1oa94zYY8xxVpLLUu045LAT0dhDZdXG58/EpPL/5kA180fQ/qudg==", "signatures": [{"sig": "MEUCIQDn3I3qvoLjS5mnS1Tyziq8/XuAAiuic1RhFzjH2fgMSwIgVoqgJccZgltQlEaZEELABU/J6CjAYkGGCr+uHKwutkU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35534, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf8o1OCRA9TVsSAnZWagAA0ekP/2nlXqGlkF1+6IeIgq9i\nWPTp72qk4R4WEfTMmEc9ShlWBgZwxgzaGs3Y1spsjlYIAL9cuAkldLHPJLqy\n57pmtuiAICltSebnqazhOg793BUdtshUu3RQwTZ1EJ6wnlkaU4TOj3oMbcTq\nDAYqS8MBR4CcE0KruTwdcD77QOkupLdXcdxPQe3VIxyvvJFIaoYpjCIhNYcm\ngi7hQT8BYksYsZoKTuCEpPZKF7+xVEYM/mZ++FtZjLKGaxYY4wZ5qh52PZMQ\nty4Yxdirf3sePY+q+d5oaGsrpFnZtxF4o72QE018j5jLppMFfRZLVzvVzPOC\nZd7Di5yihdUjy4DKPNC9GdSRgjnn0eyeN5/u/pt5zP6aIHPmSsXBup0FHuqv\n0/Sw0WLjX4iFX5W6SqWs7pLrAFIrJ9Y1AoIT/ffY9JCJt+HNa14LO3k+gtE1\nz5bExVJJ6y6LuSVb594sPGPBmzRAm4901wVdUBaNJ4YT5T6Jpy9D6HiTqLDQ\nYfIMR5b6v4Sc/g3Ek8F+/F8YrGsnWa+sCVSivWP/BqH701dypwRKxFQQPuFP\nVZEzzik1X8ygwxVMvXT6+JadxZrLNWPSwS/YpFtzPIA+OX9m5B2Zp4oC1DNx\nFkg1vpaj+2dZneRy5P3RbQDA/p18HwfyHROReLZAuJvLCsDfaQjp2Ge9gsr1\n3UW9\r\n=6YLB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "gitHead": "c120cfd596c13eaac229835d1b9ca19ccb977c85", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "6.14.10", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "15.5.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.14.0", "delay": "^4.3.0", "p-event": "^4.1.0", "@types/node": "^13.7.5"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_0.8.1_1609731406082_0.7548288510563024", "host": "s3://npm-registry-packages"}}, "0.9.0": {"name": "emittery", "version": "0.9.0", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.9.0", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "32b76a690f4c9fcd14dd8be1123b45fc7500cf7e", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.9.0.tgz", "fileCount": 5, "integrity": "sha512-sr3pHyhtKervRVWa0ESMma8tjKxkyo95ICdtQWv5HGPZdoFqypE2eL2fRmy7K41zsJu2Fc94QESxAzEz3t1GEg==", "signatures": [{"sig": "MEUCIQDQA7FM5jPljm3QoSLZIeskvVN0dwZbvobLeQvwnTh8wAIgGbaO/I1g8Gg0xBzWsvDwyu45EM51XMo3bT8jj5eb38s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43949, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsRsPCRA9TVsSAnZWagAAS3kP/31Ebfl1lFsn25RvYky2\nZKtuKiqrKneE/Z6Rmj15ZdfoaufiUIhCz8oe7Yo+7yr/qprev9OhNml+HC2K\n8tfaLcGa9iInBf9gnnnvVjui6valnzM+gr7al0lfZV4AyTqPPL+w67iO+vkC\nDw3Bl2fUhsXp9GW+WRZbDPq5a7LiOlkC58s6oF6dH2634PD+I8nqqrnXFs23\nxVolimuBqVD3NTnmvED8I5lCbvjvP2ZvaQmVshepWHF+v+3X60VZd1XEucvz\n0rYybZ/f4THt3fQmy32cT15r8tbcvlCFpMaZiKNjcS2CI2647OVarLtScHcY\nDUhOLZ3qX5Weo6gCiikawZNPWLrel4hYZypntd0igOgNOS0WE2lGOY2SIcbm\nUiR1E/lkH7QtIHUDM6VgN3ZHnPU9ppRhI1N1jasn8xMI0PIT84Bq0jy8aFSN\n84fRv57QOIT7QzijtOr+hXismbOy/ZOzml3uPeVjRKcZlLdb4IEDPc3h9jgl\nSyr7q0sYO0vHr1/Aip3I5x+FYTxs7IxkGCyRkCUEYAPHSggKDI1tXb5TXQ31\ne2xjSOBpyV7ZL9fEJS32lw2MLOgfXjNFYtDSF7BzLxUjcdXeb+gnr2wq97KQ\np6wKpunDnospw4BY9pmd02J+Diii5rX/Hi+TfX00zRWCTtynfHagEe3oNQAa\n4aor\r\n=7VWJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "gitHead": "02b18ef0fdac61ddc79cdc7c4f3d2357e0908a57", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "12.22.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.39.0", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.16.0", "delay": "^4.3.0", "p-event": "^4.1.0", "@types/node": "^15.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_0.9.0_1622219534569_0.7808372068906948", "host": "s3://npm-registry-packages"}}, "0.9.1": {"name": "emittery", "version": "0.9.1", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.9.1", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "750d14e2741690062afe71c39dc84488bb4e0b9d", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.9.1.tgz", "fileCount": 5, "integrity": "sha512-Q1yJovdxzQ6HaroiNF1szXB46alLNccSTmeMxiVbZgOwSIF772togQ2ALlUqtL5CrAJqHmJ8sdFp+MLehMR7Uw==", "signatures": [{"sig": "MEYCIQCwyANAsORmXkA1LIOO2mZC8ezriDsh6/PJmF/jPFPDkgIhAM5c3mjdLaQAC+tI8fXRM6ePNTAL2BklS0GKV8mAqm5o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43971, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgtmDqCRA9TVsSAnZWagAAZWsP/3hiVtViEC2Ws8gYUvah\nn8+arf1Jn2WZ4Tp6LSGAbnWyQm5m6mi12+vWWtSnejLkB0QRANpRIebRTSni\npc9xy0d8AFGULSkom8BHhTnHqLg3HtNyvX0ReyiBb1xibEfZc3JfyRAdg5Hk\n5AuOcL+67zzX57mQsEqBw+2KGyaiw7G2Mex6dbzImWNiRmSfCAH1mvQWwMUH\nsWcYAzvqY79N25bgYsamQn3lH/wuQUH8ejCk3VAld68cCaa+hVP81hLNYLiJ\n6/HWgNMx2a/fZGC1+QXn8KpspEPW4Y9UDGv4O9csHSJzFS6gvy3+grKBFk+F\nmNb3eCi4tfD2RKu0PQzxFJmjvlhn8yv1wBsfHQKJdlb9koQ4ZEDz/B0YBgjV\nSpf+cx6OHcJPonC/NhAW7Ultg7zIGsTYQIBDvjVePMG4buPpf80b4YGk57FX\nf5Rkl713htkxzvjfX29e5s/r916B8eU1hUrrtwFr4A8Xe9M25C402Vt1KCLt\nidRwVsRE3fmZwfNUlGD0DWVIcAoTjZK4hjK74cQFW8sDdXguTAAqrR/7pdaw\nn6tZfMGVOzjSQe+ualb0za0WjiGelaLntcF5qRoMO2xF87KQDuHdOSBmiD5B\nzk5BcfLcFQ8sfgbjTN9W/+tXuFUjbze369mu3/zK89x9g+V7nnEzzVqt9lH0\n4xGr\r\n=HJC2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "gitHead": "04b9daa8270b2150c0dc3412de7c8bc0308bce2d", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "16.0.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.39.0", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.16.0", "delay": "^4.3.0", "p-event": "^4.1.0", "@types/node": "^15.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_0.9.1_1622565098225_0.03046211460221704", "host": "s3://npm-registry-packages"}}, "0.9.2": {"name": "emittery", "version": "0.9.2", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.9.2", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "0a57c40773c6da380115c85bd67d381dd9e22f6f", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.9.2.tgz", "fileCount": 5, "integrity": "sha512-sweWHu3j4dQm+NjLPu17pv+m5lCeK7g4Ov0NgfbRUEyzLc59DYDeRYXqlxEvuolaToI0VR3ThjFAghzl7Acjfw==", "signatures": [{"sig": "MEQCIA95gxWW8z3BEY2swAV1cI1UdpNAfASJb5oSDgDjpvGrAiBmGlWA/SFYZWgXs+0cVzV1l5rIC4ZtrslQXZLLTvzTqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44045, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgt4gpCRA9TVsSAnZWagAAg7AP/iErJpwuMOlOPnn1sUP8\nPAKHBvG8Sy9Ktw2omaH7lpKTkXUIwU9OjvQTn7pwH25GeS8JrJ7SRYxJhoJY\nFNyY9ajOPl63auzA29ERhR8sXmFqGnbgVYWweCS48ZW2BknTU3H6s2Ue7tq5\nbxL3jWxWQ8ptut9GBAeqxVHZNRrl4ejbZNQ+zsxz6344jpkqPl0WU/zPmbts\nIf0y7sE73lj4s+oK8RW+1XdchsIluDPZ/30fMttfrM3W0LY99uhakLamVXvH\nwBAPykv1utn2F7LAjiZAB9iIn01AjIuHVkkt/Q066eRos6KKEr03FOGr+gfV\ngOQ5etmvYa7QW8vlcFqC5lqUOU3AcycilqJj0MTRTbzN0LSrjM25vnqbysfV\nZze6pch9nQAKREr0uFTNmTs8t4QQoXlzkCJjQlhITwq82IfMY2AGQD83CcXX\nUzIxmIPqyq9prEFZyx1wMuE/gYXHZU5PqD5eNmHj6rvkP67rkEDNyppRgMGs\nKQYACpYOWqzh8yTf9lGmy6N9H/gAwLqjo5DgLvk3bhTleu1sOvrKIrwxCvXz\n+uJsNRMbC9vzUHWxGFM0qlSCR2YdbxjL56iPWziYAJeRTT6n4v6Hzw/eMR3J\nIfePLEkJp+qQDKHUw0KnwwrGPhhZ7dnjk0VvbYrXllVnJ9QkKbz5YvxzRGUj\nkBH8\r\n=x3o8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "gitHead": "175ec7ba544a46d3b13e79fcc623642ff3686897", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "12.22.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.39.0", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.16.0", "delay": "^4.3.0", "p-event": "^4.1.0", "@types/node": "^15.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_0.9.2_1622640681212_0.9076657550321403", "host": "s3://npm-registry-packages"}}, "0.10.0": {"name": "emittery", "version": "0.10.0", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.10.0", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "bb373c660a9d421bb44706ec4967ed50c02a8026", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.10.0.tgz", "fileCount": 5, "integrity": "sha512-AGvFfs+d0JKCJQ4o01ASQLGPmSCxgfU9RFXvzPvZdjKK8oscynksuJhWrSTSw7j7Ep/sZct5b5ZhYCi8S/t0HQ==", "signatures": [{"sig": "MEYCIQCl0qgLZlb4gl6r+Hbt80RW0PV3O/kqVCQMHQGm5EoT0QIhAN66gysIpmCazE5rzgyjSg3jU+kO8EZ0isAlUxTT01Tl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44129, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPN9fCRA9TVsSAnZWagAAEPUQAIc1bDYu7CXXN7ffcB+K\n+fJsuN01syHykrH+WL0BOgOyfo/IrryrSybKA4tGYSUHErmb0CeQB1jtCf8U\n81tFMLUERxlrFqKcCWZZbbM3I69fbQvnLITir+1m0gterUPyjXUN4BLAx7B6\nxkJ8VqwxyW4k4WRkd8H1OZuw2D4EAIKi9DQXEDK/nkQQwCpTa6TolB3MNpeV\nYoBGaKFiKye1Kp9gNcR5sV0WTMiHA7QEmYW5y6ydOCD/N5ClsK2HGOdMMCXB\nMOj5JXyfOIMEoYis5SrfO9RGJ6w3T40sFTKoFluuAlwL3nAdJAuDgEaxJVX4\n3lYNRoCSRykVkDoOHAk+aaaZLa48sGE/wfMmSEVjVIQP0CTTfbFqf6U1ydUz\nhr3Z0/wWuzbxUrONTPrt0QHp+FeutZ2IJBq208ughE3Rl/eO/qZzZHz5OiaM\nmZX/1rIrKzRDhxgDBf5EbnuWIIjoazfh/KEXedNj3R91Bi8j+lZmXEIiKT+9\nYQI4GHUULzH12+C7ecwtMJXFDguNA5aTZiOONlUkwfwY6lHJTZ3oRxSzGDP7\nUkhelBqGsugsmkNIHkI84J/B1S+/dAXVYRZPdUC+y0zNNpCRsY4TiqJmKgcQ\noNJgaQXAs+K5ZKSAWfeP8vwzprqt5Ty9FvtaMtx7a+51onEA50R8EOFgyxNm\nGNGD\r\n=S2jH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}, "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "gitHead": "de3d78d67f9e099beff6384d1a59213c801562b6", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "7.20.3", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "14.17.5", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.39.0", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.16.0", "delay": "^4.3.0", "p-event": "^4.1.0", "@types/node": "^15.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_0.10.0_1631379294989_0.14373970805273073", "host": "s3://npm-registry-packages"}}, "0.10.1": {"name": "emittery", "version": "0.10.1", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.10.1", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "3d01ab87e2b6542681f8fd6cbd6597a66daa1869", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.10.1.tgz", "fileCount": 5, "integrity": "sha512-OBSS9uVXbpgqEGq2V5VnpfCu9vSnfiR9eYVJmxFYToNIcWRHkM4BAFbJe/PWjf/pQdEL7OPxd2jOW/bJiyX7gg==", "signatures": [{"sig": "MEUCIAQdw51NVlnKrw8SceZnq4e7bh3wbEpLS8NhHpQR/unZAiEAhdDfjKHzh8+bnGtDazJXZKfRqUzcBYwW78eTxEEWg7s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/fQzCRA9TVsSAnZWagAADS4P/0tfWV7NQYekUSwQX0dN\nFOusX90Az2dBclEEwwbMDs+1CUAVAyZhO98azQarQaZDeHvaRuAp1ziceUtS\nU3tZpQjAD3FOWznkDO4pVdvv9a40VU5oGly9ef1dd6Aw7ZBWGd9vRlq0Loyi\nCInoVC1jDiTkxye9xWK0hEQS8TZshP675+r+i3sVJD8fOkkh0s84QKxd5qF/\nabx6NgzDklOpQWycESTCgvEXpU6sKaHVkhbE2fhWf/C+GiX325yJnYLvG/CZ\nr1G83aeUC+rsmULzES1l8nMVs31aIdCqv8O2D6m6vfDSN/F81v3itxMvuNej\n+jE7ZiAguKJGqREvTqD8TsgbSYLBnNvUa6MKEizQXeyIPDO86EeZ3oMEVhNc\nh+inH1+v6nGQy9EOkxKx/wAWpa3wX0o7qtTSFIeCkRJLbb5snv6nOGiLu9Z2\nE3m7ZLIH1MPV0GzBG33HSnJxTjJdMeXRy48IIQj0dLmug0mYcWJF981etpJ0\nC6SQbUh7afdVG1PWbdUDZaaKWk5F8iPUgfiXSnJkjNopvJwMVDvtq1X+KrT+\nziy+xhSwfcjgAUisuzuQHamTXW+DEzFT5R6cypb1CbPcgbYCZXqF2XPahjYu\nv0A4iwgOgqlk/yyz+6QBuP/aG5tXhYE0FZXvBSVIZFJXbK+uynTwdAUR+GMz\nhnfK\r\n=e71M\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "engines": {"node": ">=12"}, "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "gitHead": "837a94d69de7571857f90fd2a676fef2eff967fe", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "12.22.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.39.0", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.19.1", "delay": "^4.3.0", "p-event": "^4.1.0", "@types/node": "^15.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_0.10.1_1644033074993_0.9696814202244541", "host": "s3://npm-registry-packages"}}, "0.10.2": {"name": "emittery", "version": "0.10.2", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.10.2", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "902eec8aedb8c41938c46e9385e9db7e03182933", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.10.2.tgz", "fileCount": 5, "integrity": "sha512-aITqOwnLanpHLNXZJENbOgjUBeHocD+xsSJmNrjovKBW5HbSpW3d1pEls7GFQPUWXiwG9+0P4GtHfEqC/4M0Iw==", "signatures": [{"sig": "MEYCIQCf1FVcRFolyjcmIIFfKAVdk8/Dfud6cADzdwTsXm3x3QIhAI/BrKszlGA4boROtenuVA+FvVu4aIx7wthIprti9KQY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiR1fOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmolFRAAju5p0ilT74rZ1UCawxRxWmiZ6I6QWvDiVUX23JTCIUD1UtD7\r\nwuS6egZTbMDc7z3FnmmyRCCLtTwsfV0HEB21qE6LTiENv4Oz8olBaChwwIVa\r\nVchBE75U6cBdPfJMKKfQBswDfP1tkjCYfzqJHhApkIRwoeLE/vyLcpwNhIJV\r\nT60e6QJQojk+DwiZYMAyGKC+QxTo8eYbHvwgutlFoFs9K1xGQfopcaNB+Uh5\r\nMBkHYaVkW+OH6CWtAUd07XteShFO4GoMYTqkykWWx+31BSShN8nLFgp4PKrH\r\ndqtPv8m7sIl0TgEWMGwgiXqpwDXuc89ZBe6I5vJe3dEMnD2YJEt7H1GRiQE2\r\n0BdscVPTTDWDdI+7j6I0Jl9/G+DWZ4jUfHhIEi/rGxOOHpivv/ArUJbMyELP\r\nmWCOC/iWgxnOkFEEDu77ldUPE9GrEjxd7iZRcEnsqBOb8KzG7ir4mPgFCOzp\r\nN+F7Eh+Pr40HMagYKH3f1owMM58cwyzyw2XrOnKpwbeMPTfCbQDRtJHO4wU0\r\nrynAxgXVl6QnXJQ55yw3PrQ8mwxqBV9rMTSk2GECyPj60z+0BTEzqX44aLHZ\r\nrVq89qxHRA/dL5+3KAfnmZ+SX3MZb61ZQ08JXf15p83k1Dy0fqDz4Zhpvg+e\r\nU0r6umNc1ikyp3DqHg5IuQr2PKMJQxjW+C4=\r\n=WXXV\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "engines": {"node": ">=12"}, "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "gitHead": "3cf4a0a2c9344d0ab0d1f1f23957e3bbde98f617", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "17.8.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.39.0", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.19.1", "delay": "^4.3.0", "p-event": "^4.1.0", "@types/node": "^15.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_0.10.2_1648842702200_0.6731599253912686", "host": "s3://npm-registry-packages"}}, "0.11.0": {"name": "emittery", "version": "0.11.0", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.11.0", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "eb5f756a200d3431de2c6e850cb2d8afd97a03b9", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.11.0.tgz", "fileCount": 5, "integrity": "sha512-S/7tzL6v5i+4iJd627Nhv9cLFIo5weAIlGccqJFpnBoDB8U1TF2k5tez4J/QNuxyyhWuFqHg1L84Kd3m7iXg6g==", "signatures": [{"sig": "MEQCIBJ0a1ll/uh2cicT109BoHNVl0XXqFvfu0XFd0YT2fASAiBO6AP5WxBo4WYlV0cEDVcUFt/V3uY0geDxlg+1OLgD4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45089, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJib0WwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqkJQ//Q73v9zZ3ys8+JGeGV4K8GdrGU6iN1gHTNx8+1si3upDJbtQy\r\nM+C2ISMTpkcuHOeypyPHbQiRaxPbIWoKmg7jZ8HGV4X5mNKG+Aplj4K7V+tJ\r\nlghRib6Y8LQhMmnSBc+m/o+WlFuN+hpUL/Z3gttch8KXBz9+SHglMQjbic7g\r\nE+dUnXSqQsr5J/b+a+ntyVQTdCawFCQtd0a6OAHme9oVknQZKj5vTWPxu32l\r\n+bjEZJ+Ud66jOBshodRu6QJ46ydSywHJpmwxKoblVZbduRNzSmD2vORwcGCx\r\nI2JoGbmPhBE0xaV70q/0orAafX08lCjNHuTCgaA/P9fmytxeuM0cXVOdtpYu\r\nxMGl1K3dnCVVvPQjFwY/L6h5Ps3DjyyTYyi1Dg/uhmH9By+Vtw2xvIePBxp4\r\n93UKllt8jrRAmwuihhQGTkSG1x8ZDSf24Gqg6phEuEB1ApQTPR/CgxMMI0nz\r\nEJRUG3LWxQeBIYtQ+ylnSXIHrFO3qskdOcUp5vKw0Quer+rBuDFW3oUbSJKL\r\nPiNP2qdvZiu9nSaNojzG81mrWwWqBztPqLDze+qICYZAwwDH/kEaj2v1+fur\r\nWSnb79OpP0YllHxDd7/wNV05XnItgbYr7bie/nxWsaUEtIeB/CreaIgc293b\r\nIz3NoKrmXMJ1Kw9O9hKwXYGIvTt+UV76ezI=\r\n=ds8m\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "engines": {"node": ">=12"}, "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "gitHead": "f61b87dbf7e0ca372b8a1972fd5d42a2a1f9bfe0", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "14.19.1", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.39.0", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.19.1", "delay": "^4.3.0", "p-event": "^4.1.0", "@types/node": "^15.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_0.11.0_1651459503873_0.9379837132017104", "host": "s3://npm-registry-packages"}}, "0.12.0": {"name": "emittery", "version": "0.12.0", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.12.0", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "023e186c2634be64e986f37a51beb482a21e434d", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.12.0.tgz", "fileCount": 5, "integrity": "sha512-Tg0E2rMgVVrjgrE4xOBk05F/weoWD59lw7LHTyX5sXmJE0jA0osiCmtD3/tArZ6YVckajyB1/iEx/ZQF3Qi2ag==", "signatures": [{"sig": "MEYCIQDN1DZP/IPuckguk/qPup6Cpd+49wpHFYUxrRzmN7AdLwIhAIiuKdnbNH2mdTLLORkZ9q0hwDN4ZWI5eWmY8FlWstVV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45459, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8QTkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrFuxAAgqCU0XIfw/fgaKRC4yVMEqwhnepOzmoQTIW/MtYBlJ0MWKpx\r\nwCNZ6Yr+wc+k1IGB43FDkBnDi77gHtyV2Eni1bGWvyFOiVYAfwHysi+nnZlZ\r\nty5GepN5yx7bCf7EV4rEq5M0Wgdiiy5h/sIKhyt3xo3OPLat53KiVwWYF7yG\r\n1vItfj4KUcMGClkA/e3my9uc7bOpeyb4BJ6xIfZtW6X6v1fhLevXS/IxFg3U\r\n1pZiyDhuTW2eebYqB6jG4nzoSTZP4Dz0B9b3tBINDAU2PltBzOkiMYa8A+xh\r\n3ElavI4/PWXZjg/cRzBWJ3nqim2TJmS0I4dYw7gNkVzvEFpUjgF4Ug6V5Ad0\r\nywCbm3ERRTyZ9sMO1FDyWmpO8N2RJEyi3eqm8tiIBF6clRKBJXlvpNE5pyMk\r\n7aSLl4XgLM0iurQmbRzs9kgDOp88FPO/sSCyHlzOUMPaeUUOtPC/FWM9gHXG\r\nIAkoaSUV5CyEiHOCmyrpd76TKlbFITQkfuykpQ2kZlSqIepEqmnNrLLgvqyJ\r\njLAQ5Fau9a89LmqD7eBWoY8eUnRf6rtMse/pbxgxlGhROY/l34huN+eCqFxL\r\nCrxgps6gL+F0P0wgCHxs5NX4CCSQfRl0171rjGhRfICa8CWZOWrh2URFVexO\r\nOkuxQdaqp9Z25r8iQePZJOFORrvuZd4tB9U=\r\n=/PlL\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "engines": {"node": ">=12"}, "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "gitHead": "fb190e1229da64e2f1eb7fd3808c0aafbd58ebeb", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "18.7.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.39.0", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.19.1", "delay": "^4.3.0", "p-event": "^4.1.0", "@types/node": "^15.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_0.12.0_1659962596209_0.05639232198263522", "host": "s3://npm-registry-packages"}}, "0.12.1": {"name": "emittery", "version": "0.12.1", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.12.1", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "cb9a4a18745816f7a1fa03a8953e7eaededb45f2", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.12.1.tgz", "fileCount": 5, "integrity": "sha512-pYyW59MIZo0HxPFf+Vb3+gacUu0gxVS3TZwB2ClwkEZywgF9f9OJDoVmNLojTn0vKX3tO9LC+pdQEcLP4Oz/bQ==", "signatures": [{"sig": "MEUCIQCoHSKlrTlyJFhXusji+kHNnA1X2LkuxM/pt+v2DceybAIgS6ogYlq0g0dguy3HABFXt+dfoygNn8MX0SziQfKS9Rw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9PFPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQuA//VZdX+15eqKMGTX1sqnCXCjE8ghI7xvKbwsug0Itd4vlGpxaO\r\n0n9G+mqZGz6mh69zd6G0Dg5egcksRS+GzrRqmTTBTK7g6Vid1pzDuDNsdPNd\r\n5iCPsgkthZUo1c9f4QoV+x6+N0Y7I3zPz9dUaKI8AxHYl6Bro0jSJbW0ezuX\r\nid2zz/69CH89kFtMeXd9YcnrAzuY7meMmibQ1fOTfn1ZH9zpJ8ma8A411U7z\r\nrAL3dQiqVUC6X5rNWu/6qT2iQovN51fp5VVf78JSmWZls7C8/SqpkxijRhvv\r\nELJz+3tc+8SXHHRUDCGpq+XZFjWwRAQfdh0IHfniKKN0BnylK6s7NOhLPt0F\r\nEheKybAzuz/iH35Yd5oWe+nRqMmPgshCn+lbgdPS3Le0Mq838auwunr0MCtU\r\nIXim4ilQuAWR9DCZG6378KBNjpuiYy7RPW9IOGaaqyIizEIGqlOZ6vb3gD9q\r\nLmGxrs+a2n4rxxaeuttBF3eDmubw68MhcuWiiiY4+d0ocls3UL+Y/iTSSlyS\r\nrKXjo+3X3Q7lym5kR7NsVuSjwILWbwrfm43JBkSxQKwDG9GA7MW/1sPVpgyg\r\nWsLgqE0YS4NJPFRtFduWfqYvzX6+fLNC2wZvLXvPy4IqmE6x4/KOd/6cZdhr\r\nHoSmz+nkILLcUzhkBLnerdbaBBbGMt/i4gk=\r\n=yDCM\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "engines": {"node": ">=12"}, "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "gitHead": "ca9ed93904970ff8b8ca1d3aaf441b663ab83441", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "16.15.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.39.0", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.19.1", "delay": "^4.3.0", "p-event": "^4.1.0", "@types/node": "^15.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_0.12.1_1660219727480_0.7593125526879376", "host": "s3://npm-registry-packages"}}, "0.13.0": {"name": "emittery", "version": "0.13.0", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.13.0", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "809da9404edcae3f3971607024ca4d960ccb03a7", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.13.0.tgz", "fileCount": 5, "integrity": "sha512-Zqo4NbvGfYJnVn333jj7FMIsFpCrKTFovx8zfwcpxKDO0+0BBCrxasTZZK0F03yhQ+eFSOeYmHrsN1x9w6fy8Q==", "signatures": [{"sig": "MEUCIH8IxNoIuujKXDKZJr0+HZoyZboRYAvVlbdYrEMoQjNPAiEArvCZQ/EgpTFW7qwZfPIlOPeI5eezQtSFUMCi0fG6uBA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBu5SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJzg/8DMBK3UUQg9ZT1HGqKBkilf4WxJ3wWNFg6rSUQLhxhE4Ei/dT\r\n0HhRpo7oGK/IUNiXJfuvf16bF8OJBuA/tYsc0IqOSkj166r4DwzlMvOgLvgM\r\n5yw9hRScbz5ACTEXr94QTfJH9FXttl06stdXsV3X4HvXJym6rfq68TVnyd5U\r\nJJx2OfMptEvaTCmJkoO+k2jG8BuQBZSytCMZNXKJKelNVveCDHLUpQ8ItPJT\r\nRnKb9oOod4vYkDeRLasMyA6d+PHCP2jHAMClfDEVaTYm2jPQle5RQEqv1CSF\r\nmHP17sE0jQLIv3hIgrlYl6kOphvfyGH1CzJqTyaECsHT77lpn9i/gtO1r0SG\r\n/9kXbT86fggyCRDxRAPaJ3fftfQB0VyAAXHWe35xycb6ODm8vjpk9TSjijZw\r\nVgEdG9EtjnePJJYQqum0bpFs9Z8A/dY3ADHeC+sdgR3+kZp9ayropXuSSsl9\r\nuDzzI23ozSMft1PwbpJ54RTUhRKcSD86YXSjWVqwfMfovmWUgRXVU3JSk/RP\r\nd7p2Hu6cwip//m1yP6E1p8Y91AyM3wEqA7ypzgvvVtZKS++7kuCCJyqbDVRb\r\n5JNxz5jvL7b3Hjxn5HxCkupO/0b1RSEijEzJ3OnnJIJYiDw76tlQLM5wMIa4\r\nSlBjrBqtKzgKl2okHsCGovk5xKjyTfg4oAU=\r\n=/DGT\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "engines": {"node": ">=12"}, "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "gitHead": "c402f6cfcee3b79ba929364063aeb9fcda9e8814", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "14.19.3", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.39.0", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.19.1", "delay": "^4.3.0", "p-event": "^4.1.0", "@types/node": "^15.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_0.13.0_1661398610167_0.2524850230860489", "host": "s3://npm-registry-packages"}}, "0.13.1": {"name": "emittery", "version": "0.13.1", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@0.13.1", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "c04b8c3457490e0847ae51fced3af52d338e3dad", "tarball": "https://registry.npmjs.org/emittery/-/emittery-0.13.1.tgz", "fileCount": 6, "integrity": "sha512-DeWwawk6r5yR9jFgnDKYt4sLS0LmHJJi3ZOnb5/JdbYwj3nW+FxQnHIjhBKz8YLC7oRNPVM9NQ47I3CVx34eqQ==", "signatures": [{"sig": "MEUCIQDqu3OziHtHpPrlXB+QD3LSH8/m8KgybagxYg9l3TmGtgIgLTCKNoDJ1mRFzdNG18B9PBeZhkdmxjhx+mA6711B/lQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB0GkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpLzQ/+OvLo4aLUy7M1/v8Op1XQHxjwZcaMmCvWhN/50KZA4p1mPjpz\r\nXUey7bW1ulJdfHcxvUUXDcV/ewoaC2O/cCXDQutHOC819pItSXaL3rTX1fsB\r\nYHy2HM1zBULrpd9QiwppLl/omhVIVVC7TMzv/YB3FOZAhA9zUhVBX+7y/ku1\r\nXAu5GykrxXTG8XbGnSLW8YqleqcsXp9jiKDAQNH70f1lWKsC+bUeIgLG6+rS\r\nSqnNRlXjJdmltSRQqcPgVi869FgLkOgUeCyYrTXcWHKQVxSxneHCfeXpMVGI\r\nTBcdtH+ixgjres2aQTeOMnkl/rRViOfMQseak+xmL+L6Y/jB5ZN9J3ckl2oz\r\nRfImwgmikNKp2Tk/qg1Gua04h45prCH0vsxMZQjF4MiRmY4TJkxRD3GSoLBu\r\nBCi8G63v0FT0H+SbtnfUUWUFwS9vCONEdcCXWBszCWTr3PsZdw5gQu81Xuk1\r\nbKJ27EOnLbdi+xrj8Ys5bHdvSQSVz6tMeP3rqeyF6gIxAajDKfxskyeSy8kV\r\ns/JPfESo6efhX/MwGrY29t6+KuBR/A5MaiX3FEHV3rMGNVCy8bMOBN0wBxNu\r\nDHQQXvO42VLy1wdxDX+IdFJbS6WWVz/7cpLIB3KwjZ9Jti54sb+j64jFmNPy\r\nCAeJlK25ZawIk/VO2H9I3dIx3QotRjQ2sO8=\r\n=86rH\r\n-----END PGP SIGNATURE-----\r\n"}, "types": "./index.d.ts", "engines": {"node": ">=12"}, "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "gitHead": "5d814d924c5093389e1f947e6674d4e91a76a75a", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "14.19.3", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.39.0", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.19.1", "delay": "^4.3.0", "p-event": "^4.1.0", "@types/node": "^15.6.1"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_0.13.1_1661419940240_0.9269759504917212", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "emittery", "version": "1.0.0", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@1.0.0", "maintainers": [{"name": "novemberborn", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "7321364e493c8623088bb22483f2a035fd6c2673", "tarball": "https://registry.npmjs.org/emittery/-/emittery-1.0.0.tgz", "fileCount": 6, "integrity": "sha512-TD/u5aAn5W2HI2OukSIReNYXf/cH7U0QZHPxM4aIVYy0CmtrLCvf+7E8MuV2BbO02l6wq4sAKuFA8H16l6AHMA==", "signatures": [{"sig": "MEQCIBz3lN2/fCshAF/9l2suEbREw525S7hUx8QPv6mlIVl0AiBlL4izhMhRq41mN2b3wCEDO8I/aWTcYyowtWz1K+boMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFuqyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrwpw/+N8smN503z/brvPMdJQ0Bks5Fn6iPfzlM6dFdhLDdRnbRQ6JG\r\nQaNBqGQA0eloeqL1OeWuMm/2+qRgny6X83+k8WoSnlHFCo4IjLZikvxpFeL1\r\njYSAF5DVYEmKa/GXEz37gwy0HE73SN5dE24/l+7hgxMtnY2haozKLI1KzMzX\r\ndM0ZOTQhGJQVQUh52k7MgGn4Ya9BSRMx+h5xRHtv1XYaUm6NE0dXJ3+KYqwe\r\nGlpfjSIIsyzLjWcGiJsZRgj+YVreZw51QnfPjrcGdPEW27GMcRmTnlW/DZdH\r\ndk9rwkSMUQDsFDyGRivgWDnGg1pckNF8x2iBAno2CeTAHAt8oAuW4yr+6djA\r\nDYLxTgiJmyQ7kZW04dX56cLpS5k+RBHXckG6dx+AC4QkKLtdPLCG/FggfRsd\r\nqJigF5ffTwIXmBj2rvqP3THA4qmKhH7Bx1YdmsbbcGV8vrUuiMGtBh28AjEn\r\nZoLxFKYEF63EQPXtRoLi46DVaMP4DKEBMDsOjMLTsJWOaIIKxoqp8wEp0H6k\r\nGj9w9fkzHxkNprk7E2SiUPfJ4/KirTPecpUXLSYZRLUlD/PxFqB4Jiom+cD7\r\nhSkWUHlU33MO6U0sFlI4aVX7McXbRlDems2Oma5LkPVAvalRbO37Jmb6qE71\r\nIpjSu8yhs4VhgXi7AGDQ5nfNfadSxcIYCrk=\r\n=w6hm\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./index.js", "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "gitHead": "dc6878c42c4889b86ea148179cb09dadd14958dd", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "14.19.3", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.52.3", "ava": "^4.3.3", "nyc": "^15.1.0", "tsd": "^0.23.0", "delay": "^5.0.0", "p-event": "^5.0.1", "@types/node": "^18.7.15"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_1.0.0_1662446258321_0.5897195240463708", "host": "s3://npm-registry-packages"}}, "1.0.1": {"name": "emittery", "version": "1.0.1", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@1.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "e0cf36e2d7eef94dbd025969f642d57ae50a56cd", "tarball": "https://registry.npmjs.org/emittery/-/emittery-1.0.1.tgz", "fileCount": 6, "integrity": "sha512-2ID6FdrMD9KDLldGesP6317G78K7km/kMcwItRtVFva7I/cSEOIaLpewaUb+YLXVwdAp3Ctfxh/V5zIl1sj7dQ==", "signatures": [{"sig": "MEQCIButB0md0a9EuiJ3k7jDqITeM5OFrcnPhPHEOz16N5PZAiBWVBgnrlCcHdODGDd5pSIO4LiFc9kH874vkz65F1SVMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZ8JaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUag//Wgl240/Kw1XsE083LS6zUuOf52160x40oJAcTjpupvRdLvvS\r\nm82MgWsOAImQHhC14yigQOMSUjjx2eiEY9+nq0dGGCDb79mCKxqQ+aBGw2FI\r\ns0WDkCEhQq/x1QwWF/C54fuP1lttiWNePVM0ByrUqxzyT8GtJXGCmV1PRG2F\r\n3NNRHcP/Pew5y2s2VnhhRlRWFhLGw+XVKqwxAdMB/e5Kgt7ld07vwnYh/k0M\r\nQHKAGGK6+EQi6uXl25g2l1kKYrNiYHgn5m3vpPzXH98CQc2Vyu6lMmTG6U4d\r\nYXYEC4TDnTTVGRBXIrzgkax1cXJA0mHzGth4pmzdUmFv6oA/WztyulclQoc9\r\nvQAE26RhFk24mmmnD9RW/kgvk17Q49uWS+tckhT6nsGzuJesemAUA9IA/acS\r\nwXMQERIY5N7JM99HTbvWJKWBupdSe7g4xDqQLOgMoLuivSUmQ1dec8PYtrcm\r\nTHETxjBZvrAtjcTd+wJbv247GIuM/3nHr3EgIa+PqcVWLiCDecEOr90yejhs\r\nkkmjj3fE0lT+HqOeX6Gfft3JSGZpRwK9jap6+Y3Eu3OGhVbbwI3UnqWNIZYk\r\nPByP/rS5NcfxD2ag3PnEfNBDvzTHhRpIWZhowveaDBhRE0JZYztlTL4ziHQc\r\nRCGRHHhrLpJZjT/aYq1rcAG2ZSq9kpRqaRI=\r\n=D2FV\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./index.js", "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "gitHead": "f0b3c2bf8dc985a7dde0e39607e30950394be54b", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Simple and modern async event emitter", "directories": {}, "_nodeVersion": "14.19.3", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.52.3", "ava": "^4.3.3", "nyc": "^15.1.0", "tsd": "^0.23.0", "delay": "^5.0.0", "p-event": "^5.0.1", "@types/node": "^18.7.15"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_1.0.1_1667744346199_0.20264845925757857", "host": "s3://npm-registry-packages"}}, "1.0.2": {"name": "emittery", "version": "1.0.2", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@1.0.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "5cab3c35bb8d13e8c29219586bf8d8f6ab1dfa42", "tarball": "https://registry.npmjs.org/emittery/-/emittery-1.0.2.tgz", "fileCount": 6, "integrity": "sha512-PqHdP6JJrxiSXQzCAzII77dVsfyGWu+7V0ghqkaCej2shF1cnIPeFE9kKZcVTqvBjrRMDVOdNXKEYcjxkznS1g==", "signatures": [{"sig": "MEUCIEqJlQ5yZ2qLctR2id+8Bi19hnKHaW8o6DSt+9ZRPo6TAiEAns6QVBTNB3g1RuMQNfRJzpRq8ERrIswjhS1latjyyqw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46560}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./index.js", "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "gitHead": "253f457c7428e3f848c639d6a2268f8de584a12c", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Simple and modern async event emitter", "directories": {}, "sideEffects": false, "_nodeVersion": "20.11.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.55.0", "ava": "^4.3.3", "nyc": "^15.1.0", "tsd": "^0.23.0", "delay": "^5.0.0", "p-event": "^5.0.1", "@types/node": "^18.7.15"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_1.0.2_1706512234241_0.6463044039543442", "host": "s3://npm-registry-packages"}}, "1.0.3": {"name": "emittery", "version": "1.0.3", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@1.0.3", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "c9d2a9c689870f15251bb13b31c67715c26d69ac", "tarball": "https://registry.npmjs.org/emittery/-/emittery-1.0.3.tgz", "fileCount": 6, "integrity": "sha512-tJdCJitoy2lrC2ldJcqN4vkqJ00lT+tOWNT1hBJjO/3FDMJa5TTIiYGCKGkn/WfCyOzUMObeohbVTj00fhiLiA==", "signatures": [{"sig": "MEUCIQCgqVPmvD13IXEEGvCSwc5MTADIsLfHrQdi9lAKBxB+uwIgc1IDoiYnl9vdLt+RxtMiAqnr7hQ5q32GitpSIMXqUtM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46560}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./index.js", "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "gitHead": "69193ca096fdebeb73bc860a2dbd64d8781c4970", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Simple and modern async event emitter", "directories": {}, "sideEffects": false, "_nodeVersion": "20.11.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.55.0", "ava": "^4.3.3", "nyc": "^15.1.0", "tsd": "^0.23.0", "delay": "^5.0.0", "p-event": "^6.0.0", "@types/node": "^18.7.15"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_1.0.3_1707840044662_0.928636192528582", "host": "s3://npm-registry-packages"}}, "1.1.0": {"name": "emittery", "version": "1.1.0", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "emittery@1.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/emittery#readme", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "dist": {"shasum": "7b5a00ec9a315fa9bfabeba0673920a1bb385f1b", "tarball": "https://registry.npmjs.org/emittery/-/emittery-1.1.0.tgz", "fileCount": 6, "integrity": "sha512-rsX7ktqARv/6UQDgMaLfIqUWAEzzbCQiVh7V9rhDXp6c37yoJcks12NVD+XPkgl4AEavmNhVfrhGoqYwIsMYYA==", "signatures": [{"sig": "MEYCIQDtQreecuu3o4pMw32ZEhm3ejknVjAqnHIGsUxVtZ5u9QIhAJMYvoNSTkM4AoCYVbtmP2YljOsvlOUL5xNbJBY4MyR/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47488}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=14.16"}, "exports": "./index.js", "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "gitHead": "0b718882c3368b53419cb941eb1c2312fa97d518", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/emittery.git", "type": "git"}, "_npmVersion": "10.9.0", "description": "Simple and modern async event emitter", "directories": {}, "sideEffects": false, "_nodeVersion": "23.3.0", "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.55.0", "ava": "^4.3.3", "nyc": "^15.1.0", "tsd": "^0.23.0", "delay": "^5.0.0", "p-event": "^6.0.0", "@types/node": "^18.7.15"}, "_npmOperationalInternal": {"tmp": "tmp/emittery_1.1.0_1737984404590_0.03126005916458374", "host": "s3://npm-registry-packages-npm-production"}}, "1.2.0": {"name": "emittery", "version": "1.2.0", "description": "Simple and modern async event emitter", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/emittery.git"}, "funding": "https://github.com/sindresorhus/emittery?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "types": "./index.d.ts", "sideEffects": false, "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && nyc ava && tsd"}, "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "devDependencies": {"@types/node": "^18.7.15", "ava": "^4.3.3", "delay": "^5.0.0", "nyc": "^15.1.0", "p-event": "^6.0.0", "tsd": "^0.23.0", "xo": "^0.55.0"}, "nyc": {"reporter": ["html", "lcov", "text"]}, "_id": "emittery@1.2.0", "gitHead": "cfde903357ccfa6fb4d7e1888da17aa84dc2c009", "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "homepage": "https://github.com/sindresorhus/emittery#readme", "_nodeVersion": "20.19.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-KxdRyyFcS85pH3dnU8Y5yFUm2YJdaHwcBZWrfG8o89ZY9a13/f9itbN+YG3ELbBo9Pg5zvIozstmuV8bX13q6g==", "shasum": "466edb32bada8d9b35f779c3ee8c514ac2df8dc0", "tarball": "https://registry.npmjs.org/emittery/-/emittery-1.2.0.tgz", "fileCount": 6, "unpackedSize": 48631, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDo2Hz3qsD2RfIszOxXnnt3Wd9TJNUL4P8gMMXqbhGZggIgEtNkv1UQSgKFPqpIoGHOa078C5PArUd0mJHJTdhzG6c="}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>", "actor": {"name": "sindresor<PERSON>", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/emittery_1.2.0_1750446274481_0.6701994129365008"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-11-27T21:18:15.608Z", "modified": "2025-06-20T19:04:34.865Z", "0.1.0": "2017-11-27T21:18:15.608Z", "0.1.1": "2017-11-28T18:36:40.282Z", "0.1.2": "2017-11-29T09:56:39.280Z", "0.2.0": "2017-11-29T19:27:07.743Z", "0.3.0": "2018-02-15T16:14:22.395Z", "0.4.0": "2018-06-07T13:11:18.841Z", "0.4.1": "2018-07-12T16:24:06.729Z", "0.5.0": "2019-09-17T14:25:38.742Z", "0.5.1": "2019-09-26T17:20:12.779Z", "0.6.0": "2020-03-09T08:27:05.774Z", "0.7.0": "2020-05-11T14:16:35.566Z", "0.7.1": "2020-07-05T13:53:56.828Z", "0.7.2": "2020-10-17T00:06:22.486Z", "0.8.0": "2020-12-30T06:36:05.820Z", "0.8.1": "2021-01-04T03:36:46.226Z", "0.9.0": "2021-05-28T16:32:14.734Z", "0.9.1": "2021-06-01T16:31:38.336Z", "0.9.2": "2021-06-02T13:31:21.356Z", "0.10.0": "2021-09-11T16:54:55.142Z", "0.10.1": "2022-02-05T03:51:15.134Z", "0.10.2": "2022-04-01T19:51:42.416Z", "0.11.0": "2022-05-02T02:45:04.089Z", "0.12.0": "2022-08-08T12:43:16.503Z", "0.12.1": "2022-08-11T12:08:47.757Z", "0.13.0": "2022-08-25T03:36:50.364Z", "0.13.1": "2022-08-25T09:32:20.391Z", "1.0.0": "2022-09-06T06:37:38.498Z", "1.0.1": "2022-11-06T14:19:06.345Z", "1.0.2": "2024-01-29T07:10:34.467Z", "1.0.3": "2024-02-13T16:00:44.787Z", "1.1.0": "2025-01-27T13:26:44.769Z", "1.2.0": "2025-06-20T19:04:34.675Z"}, "bugs": {"url": "https://github.com/sindresorhus/emittery/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "license": "MIT", "homepage": "https://github.com/sindresorhus/emittery#readme", "keywords": ["event", "emitter", "eventemitter", "events", "async", "emit", "on", "once", "off", "listener", "subscribe", "unsubscribe", "pubsub", "tiny", "addlistener", "addeventlistener", "dispatch", "dispatcher", "observer", "trigger", "await", "promise", "typescript", "ts", "typed"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/emittery.git"}, "description": "Simple and modern async event emitter", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "readme": "# <img src=\"media/header.png\" width=\"1000\">\n\n> Simple and modern async event emitter\n\n<!-- [![Coverage Status](https://codecov.io/gh/sindresorhus/emittery/branch/main/graph/badge.svg)](https://codecov.io/gh/sindresorhus/emittery) -->\n[![](https://badgen.net/bundlephobia/minzip/emittery)](https://bundlephobia.com/result?p=emittery)\n\nIt works in Node.js and the browser (using a bundler).\n\nEmitting events asynchronously is important for production code where you want the least amount of synchronous operations. Since JavaScript is single-threaded, no other code can run while doing synchronous operations. For Node.js, that means it will block other requests, defeating the strength of the platform, which is scalability through async. In the browser, a synchronous operation could potentially cause lags and block user interaction.\n\n## Install\n\n```sh\nnpm install emittery\n```\n\n## Usage\n\n```js\nimport Emittery from 'emittery';\n\nconst emitter = new Emittery();\n\nemitter.on('🦄', data => {\n\tconsole.log(data);\n});\n\nconst myUnicorn = Symbol('🦄');\n\nemitter.on(myUnicorn, data => {\n\tconsole.log(`Unicorns love ${data}`);\n});\n\nemitter.emit('🦄', '🌈'); // Will trigger printing '🌈'\nemitter.emit(myUnicorn, '🦋');  // Will trigger printing 'Unicorns love 🦋'\n```\n\n## API\n\n### eventName\n\nEmittery accepts strings, symbols, and numbers as event names.\n\nSymbol event names are preferred given that they can be used to avoid name collisions when your classes are extended, especially for internal events.\n\n### isDebugEnabled\n\nToggle debug mode for all instances.\n\nDefault: `true` if the `DEBUG` environment variable is set to `emittery` or `*`, otherwise `false`.\n\nExample:\n\n```js\nimport Emittery from 'emittery';\n\nEmittery.isDebugEnabled = true;\n\nconst emitter1 = new Emittery({debug: {name: 'myEmitter1'}});\nconst emitter2 = new Emittery({debug: {name: 'myEmitter2'}});\n\nemitter1.on('test', data => {\n\t// …\n});\n\nemitter2.on('otherTest', data => {\n\t// …\n});\n\nemitter1.emit('test');\n//=> [16:43:20.417][emittery:subscribe][myEmitter1] Event Name: test\n//\tdata: undefined\n\nemitter2.emit('otherTest');\n//=> [16:43:20.417][emittery:subscribe][myEmitter2] Event Name: otherTest\n//\tdata: undefined\n```\n\n### emitter = new Emittery(options?)\n\nCreate a new instance of Emittery.\n\n#### options?\n\nType: `object`\n\nConfigure the new instance of Emittery.\n\n##### debug?\n\nType: `object`\n\nConfigure the debugging options for this instance.\n\n###### name\n\nType: `string`\\\nDefault: `undefined`\n\nDefine a name for the instance of Emittery to use when outputting debug data.\n\nExample:\n\n```js\nimport Emittery from 'emittery';\n\nEmittery.isDebugEnabled = true;\n\nconst emitter = new Emittery({debug: {name: 'myEmitter'}});\n\nemitter.on('test', data => {\n\t// …\n});\n\nemitter.emit('test');\n//=> [16:43:20.417][emittery:subscribe][myEmitter] Event Name: test\n//\tdata: undefined\n```\n\n###### enabled?\n\nType: `boolean`\\\nDefault: `false`\n\nToggle debug logging just for this instance.\n\nExample:\n\n```js\nimport Emittery from 'emittery';\n\nconst emitter1 = new Emittery({debug: {name: 'emitter1', enabled: true}});\nconst emitter2 = new Emittery({debug: {name: 'emitter2'}});\n\nemitter1.on('test', data => {\n\t// …\n});\n\nemitter2.on('test', data => {\n\t// …\n});\n\nemitter1.emit('test');\n//=> [16:43:20.417][emittery:subscribe][emitter1] Event Name: test\n//\tdata: undefined\n\nemitter2.emit('test');\n```\n\n###### logger?\n\nType: `Function(string, string, EventName?, Record<string, any>?) => void`\n\nDefault:\n\n```js\n(type, debugName, eventName, eventData) => {\n\tif (typeof eventData === 'object') {\n\t\teventData = JSON.stringify(eventData);\n\t}\n\n\tif (typeof eventName === 'symbol' || typeof eventName === 'number') {\n\t\teventName = eventName.toString();\n\t}\n\n\tconst currentTime = new Date();\n\tconst logTime = `${currentTime.getHours()}:${currentTime.getMinutes()}:${currentTime.getSeconds()}.${currentTime.getMilliseconds()}`;\n\tconsole.log(`[${logTime}][emittery:${type}][${debugName}] Event Name: ${eventName}\\n\\tdata: ${eventData}`);\n}\n```\n\nFunction that handles debug data.\n\nExample:\n\n```js\nimport Emittery from 'emittery';\n\nconst myLogger = (type, debugName, eventName, eventData) => {\n\tconsole.log(`[${type}]: ${eventName}`);\n};\n\nconst emitter = new Emittery({\n\tdebug: {\n\t\tname: 'myEmitter',\n\t\tenabled: true,\n\t\tlogger: myLogger\n\t}\n});\n\nemitter.on('test', data => {\n\t// …\n});\n\nemitter.emit('test');\n//=> [subscribe]: test\n```\n\n#### on(eventName | eventName[], listener, options?: {signal?: AbortSignal})\n\nSubscribe to one or more events.\n\nReturns an unsubscribe method.\n\nUsing the same listener multiple times for the same event will result in only one method call per emitted event.\n\n```js\nimport Emittery from 'emittery';\n\nconst emitter = new Emittery();\n\nemitter.on('🦄', data => {\n\tconsole.log(data);\n});\n\nemitter.on(['🦄', '🐶'], data => {\n\tconsole.log(data);\n});\n\nemitter.emit('🦄', '🌈'); // log => '🌈' x2\nemitter.emit('🐶', '🍖'); // log => '🍖'\n```\n\nYou can pass an [abort signal](https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal) to unsubscribe too:\n\n```js\nimport Emittery from 'emittery';\n\nconst abortController = new AbortController();\n\nemitter.on('🐗', data => {\n\tconsole.log(data);\n}, {signal: abortController.signal});\n\nabortController.abort();\nemitter.emit('🐗', '🍞'); // nothing happens\n```\n\n##### Custom subscribable events\n\nEmittery exports some symbols which represent \"meta\" events that can be passed to `Emitter.on` and similar methods.\n\n- `Emittery.listenerAdded` - Fires when an event listener was added.\n- `Emittery.listenerRemoved` - Fires when an event listener was removed.\n\n```js\nimport Emittery from 'emittery';\n\nconst emitter = new Emittery();\n\nemitter.on(Emittery.listenerAdded, ({listener, eventName}) => {\n\tconsole.log(listener);\n\t//=> data => {}\n\n\tconsole.log(eventName);\n\t//=> '🦄'\n});\n\nemitter.on('🦄', data => {\n\t// Handle data\n});\n```\n\n###### Listener data\n\n- `listener` - The listener that was added.\n- `eventName` - The name of the event that was added or removed if `.on()` or `.off()` was used, or `undefined` if `.onAny()` or `.offAny()` was used.\n\nOnly events that are not of this type are able to trigger these events.\n\n##### listener(data)\n\n#### off(eventName | eventName[], listener)\n\nRemove one or more event subscriptions.\n\n```js\nimport Emittery from 'emittery';\n\nconst emitter = new Emittery();\n\nconst listener = data => {\n\tconsole.log(data);\n};\n\nemitter.on(['🦄', '🐶', '🦊'], listener);\nawait emitter.emit('🦄', 'a');\nawait emitter.emit('🐶', 'b');\nawait emitter.emit('🦊', 'c');\nemitter.off('🦄', listener);\nemitter.off(['🐶', '🦊'], listener);\nawait emitter.emit('🦄', 'a'); // Nothing happens\nawait emitter.emit('🐶', 'b'); // Nothing happens\nawait emitter.emit('🦊', 'c'); // Nothing happens\n```\n\n##### listener(data)\n\n#### once(eventName | eventName[], predicate?)\n\nSubscribe to one or more events only once. It will be unsubscribed after the first event that matches the predicate (if provided).\n\nReturns a promise for the event data when `eventName` is emitted and predicate matches (if provided). This promise is extended with an `off` method.\n\n```js\nimport Emittery from 'emittery';\n\nconst emitter = new Emittery();\n\nemitter.once('🦄').then(data => {\n\tconsole.log(data);\n\t//=> '🌈'\n});\n\nemitter.once(['🦄', '🐶']).then(data => {\n\tconsole.log(data);\n});\n\n// With predicate\nemitter.once('data', data => data.ok === true).then(data => {\n\tconsole.log(data);\n\t//=> {ok: true, value: 42}\n});\n\nemitter.emit('🦄', '🌈'); // Log => '🌈' x2\nemitter.emit('🐶', '🍖'); // Nothing happens\nemitter.emit('data', {ok: false}); // Nothing happens\nemitter.emit('data', {ok: true, value: 42}); // Log => {ok: true, value: 42}\n```\n\n#### events(eventName)\n\nGet an async iterator which buffers data each time an event is emitted.\n\nCall `return()` on the iterator to remove the subscription.\n\n```js\nimport Emittery from 'emittery';\n\nconst emitter = new Emittery();\nconst iterator = emitter.events('🦄');\n\nemitter.emit('🦄', '🌈1'); // Buffered\nemitter.emit('🦄', '🌈2'); // Buffered\n\niterator\n\t.next()\n\t.then(({value, done}) => {\n\t\t// done === false\n\t\t// value === '🌈1'\n\t\treturn iterator.next();\n\t})\n\t.then(({value, done}) => {\n\t\t// done === false\n\t\t// value === '🌈2'\n\t\t// Revoke subscription\n\t\treturn iterator.return();\n\t})\n\t.then(({done}) => {\n\t\t// done === true\n\t});\n```\n\nIn practice, you would usually consume the events using the [for await](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/for-await...of) statement. In that case, to revoke the subscription simply break the loop.\n\n```js\nimport Emittery from 'emittery';\n\nconst emitter = new Emittery();\nconst iterator = emitter.events('🦄');\n\nemitter.emit('🦄', '🌈1'); // Buffered\nemitter.emit('🦄', '🌈2'); // Buffered\n\n// In an async context.\nfor await (const data of iterator) {\n\tif (data === '🌈2') {\n\t\tbreak; // Revoke the subscription when we see the value '🌈2'.\n\t}\n}\n```\n\nIt accepts multiple event names.\n\n```js\nimport Emittery from 'emittery';\n\nconst emitter = new Emittery();\nconst iterator = emitter.events(['🦄', '🦊']);\n\nemitter.emit('🦄', '🌈1'); // Buffered\nemitter.emit('🦊', '🌈2'); // Buffered\n\niterator\n\t.next()\n\t.then(({value, done}) => {\n\t\t// done === false\n\t\t// value === '🌈1'\n\t\treturn iterator.next();\n\t})\n\t.then(({value, done}) => {\n\t\t// done === false\n\t\t// value === '🌈2'\n\t\t// Revoke subscription\n\t\treturn iterator.return();\n\t})\n\t.then(({done}) => {\n\t\t// done === true\n\t});\n```\n\n#### emit(eventName, data?)\n\nTrigger an event asynchronously, optionally with some data. Listeners are called in the order they were added, but executed concurrently.\n\nReturns a promise that resolves when all the event listeners are done. *Done* meaning executed if synchronous or resolved when an async/promise-returning function. You usually wouldn't want to wait for this, but you could for example catch possible errors. If any of the listeners throw/reject, the returned promise will be rejected with the error, but the other listeners will not be affected.\n\n#### emitSerial(eventName, data?)\n\nSame as above, but it waits for each listener to resolve before triggering the next one. This can be useful if your events depend on each other. Although ideally they should not. Prefer `emit()` whenever possible.\n\nIf any of the listeners throw/reject, the returned promise will be rejected with the error and the remaining listeners will *not* be called.\n\n#### onAny(listener, options?: {signal?: AbortSignal})\n\nSubscribe to be notified about any event.\n\nReturns a method to unsubscribe. Abort signal is respected too.\n\n##### listener(eventName, data)\n\n#### offAny(listener)\n\nRemove an `onAny` subscription.\n\n#### anyEvent()\n\nGet an async iterator which buffers a tuple of an event name and data each time an event is emitted.\n\nCall `return()` on the iterator to remove the subscription.\n\n```js\nimport Emittery from 'emittery';\n\nconst emitter = new Emittery();\nconst iterator = emitter.anyEvent();\n\nemitter.emit('🦄', '🌈1'); // Buffered\nemitter.emit('🌟', '🌈2'); // Buffered\n\niterator.next()\n\t.then(({value, done}) => {\n\t\t// done === false\n\t\t// value is ['🦄', '🌈1']\n\t\treturn iterator.next();\n\t})\n\t.then(({value, done}) => {\n\t\t// done === false\n\t\t// value is ['🌟', '🌈2']\n\t\t// Revoke subscription\n\t\treturn iterator.return();\n\t})\n\t.then(({done}) => {\n\t\t// done === true\n\t});\n```\n\nIn the same way as for `events`, you can subscribe by using the `for await` statement\n\n#### clearListeners(eventNames?)\n\nClear all event listeners on the instance.\n\nIf `eventNames` is given, only the listeners for that events are cleared.\n\n#### listenerCount(eventNames?)\n\nThe number of listeners for the `eventNames` or all events if not specified.\n\n#### bindMethods(target, methodNames?)\n\nBind the given `methodNames`, or all `Emittery` methods if `methodNames` is not defined, into the `target` object.\n\n```js\nimport Emittery from 'emittery';\n\nconst object = {};\n\nnew Emittery().bindMethods(object);\n\nobject.emit('event');\n```\n\n## TypeScript\n\nThe default `Emittery` class has generic types that can be provided by TypeScript users to strongly type the list of events and the data passed to their event listeners.\n\n```ts\nimport Emittery from 'emittery';\n\nconst emitter = new Emittery<\n\t// Pass `{[eventName]: undefined | <eventArg>}` as the first type argument for events that pass data to their listeners.\n\t// A value of `undefined` in this map means the event listeners should expect no data, and a type other than `undefined` means the listeners will receive one argument of that type.\n\t{\n\t\topen: string,\n\t\tclose: undefined\n\t}\n>();\n\n// Typechecks just fine because the data type for the `open` event is `string`.\nemitter.emit('open', 'foo\\n');\n\n// Typechecks just fine because `close` is present but points to undefined in the event data type map.\nemitter.emit('close');\n\n// TS compilation error because `1` isn't assignable to `string`.\nemitter.emit('open', 1);\n\n// TS compilation error because `other` isn't defined in the event data type map.\nemitter.emit('other');\n```\n\n### Emittery.mixin(emitteryPropertyName, methodNames?)\n\nA decorator which mixins `Emittery` as property `emitteryPropertyName` and `methodNames`, or all `Emittery` methods if `methodNames` is not defined, into the target class.\n\n```ts\nimport Emittery from 'emittery';\n\****************('emittery')\nclass MyClass {}\n\nconst instance = new MyClass();\n\ninstance.emit('event');\n```\n\n## Scheduling details\n\nListeners are not invoked for events emitted *before* the listener was added. Removing a listener will prevent that listener from being invoked, even if events are in the process of being (asynchronously!) emitted. This also applies to `.clearListeners()`, which removes all listeners. Listeners will be called in the order they were added. So-called *any* listeners are called *after* event-specific listeners.\n\nNote that when using `.emitSerial()`, a slow listener will delay invocation of subsequent listeners. It's possible for newer events to overtake older ones.\n\n## Debugging\n\nEmittery can collect and log debug information.\n\nTo enable this feature set the DEBUG environment variable to `'emittery'` or `'*'`. Additionally you can set the static `isDebugEnabled` variable to true on the Emittery class, or `myEmitter.debug.enabled` on an instance of it for debugging a single instance.\n\nSee [API](#api) for more details on how debugging works.\n\n## FAQ\n\n### How is this different than the built-in `EventEmitter` in Node.js?\n\nThere are many things to not like about `EventEmitter`: its huge API surface, synchronous event emitting, magic error event, flawed memory leak detection. Emittery has none of that.\n\n### Isn't `EventEmitter` synchronous for a reason?\n\nMostly backwards compatibility reasons. The Node.js team can't break the whole ecosystem.\n\nIt also allows silly code like this:\n\n```js\nlet unicorn = false;\n\nemitter.on('🦄', () => {\n\tunicorn = true;\n});\n\nemitter.emit('🦄');\n\nconsole.log(unicorn);\n//=> true\n```\n\nBut I would argue doing that shows a deeper lack of Node.js and async comprehension and is not something we should optimize for. The benefit of async emitting is much greater.\n\n### Can you support multiple arguments for `emit()`?\n\nNo, just use [destructuring](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/Destructuring_assignment):\n\n```js\nemitter.on('🦄', ([foo, bar]) => {\n\tconsole.log(foo, bar);\n});\n\nemitter.emit('🦄', [foo, bar]);\n```\n\n## Related\n\n- [p-event](https://github.com/sindresorhus/p-event) - Promisify an event by waiting for it to be emitted\n", "readmeFilename": "readme.md", "users": {"jsumners": true, "flumpus-dev": true}}