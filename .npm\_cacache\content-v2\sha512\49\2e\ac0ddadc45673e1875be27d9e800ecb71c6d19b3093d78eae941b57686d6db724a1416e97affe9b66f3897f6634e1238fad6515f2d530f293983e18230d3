{"_id": "json5", "_rev": "74-a0d632332cdc73bcd33e3ab78bcf56c2", "name": "json5", "description": "JSON for Humans", "dist-tags": {"latest": "2.2.3", "previous": "1.0.2"}, "versions": {"0.0.0": {"name": "json5", "description": "Modern JSON.", "version": "0.0.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "json5", "scripts": {"test": "node test"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "json5@0.0.0", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.21", "_nodeVersion": "v0.6.16", "_defaultsLoaded": true, "dist": {"shasum": "f6e55f0133dc3b2c66c1fd3a51707b8bb6149f11", "tarball": "https://registry.npmjs.org/json5/-/json5-0.0.0.tgz", "integrity": "sha512-ZDjkQaNThtnUkaozqQf0uzMYj7DwVigcBoOCSyZKSAryxtFMcAO+/zYiCAsBClAowpoDHohTmYGZr9WhZb8kmg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGfZggGYHeR74e7+AdfHPvYZRcyi+z3Fyr5PXzfVjqNbAiEAkh6DVhDyj+uwRIzi7Te9uPC2QNVPonabgOhkiVemMHM="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.0.1": {"name": "json5", "version": "0.0.1", "description": "Modern JSON.", "keywords": ["json", "json5"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "main": "json5", "dependencies": {}, "devDependencies": {"mocha": "~1.0.3"}, "scripts": {"test": "mocha --ui exports --reporter list"}, "homepage": "https://github.com/aseemk/json5", "repository": {"type": "git", "url": "git://github.com/aseemk/json5.git"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "json5@0.0.1", "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.21", "_nodeVersion": "v0.6.16", "_defaultsLoaded": true, "dist": {"shasum": "d9611910a4a438138b3ff4c9de68f1fa63cbde5b", "tarball": "https://registry.npmjs.org/json5/-/json5-0.0.1.tgz", "integrity": "sha512-SK4a+2DrenC8IvBzDtJbXWmmhVBoee2y3HfJzpv893NxTCRP+iGXSg0I6pKGULoo0cdCYDoXdHTPNdQKQ9TO+g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC7/T3XKqyNCpnNa0RFfsONBaAvOb+fqjAhvQGO1jkCRgIgYChQSzV4P2EM920uvg12ipq4bZ//W68HD+2ZrJDkLFM="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.1.0": {"name": "json5", "version": "0.1.0", "description": "JSON for the ES5 era.", "keywords": ["json", "es5"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "main": "lib/json5.js", "bin": {"json5": "lib/cli.js"}, "dependencies": {}, "devDependencies": {"mocha": "~1.0.3"}, "scripts": {"test": "mocha --ui exports --reporter spec"}, "homepage": "http://json5.org/", "repository": {"type": "git", "url": "git://github.com/aseemk/json5.git"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "json5@0.1.0", "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.21", "_nodeVersion": "v0.6.16", "_defaultsLoaded": true, "dist": {"shasum": "b23045f81f720422b0f3c7f3842be035765ccf2b", "tarball": "https://registry.npmjs.org/json5/-/json5-0.1.0.tgz", "integrity": "sha512-AHUF8eY1d/DMQN7I95o80l7t/kq5ORGFQfsm/aNMpRw1RAas2YtKPPd1zplyUo92CDM25mBHkxJ+TgcyEpvFgg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDxUIjtbmRbtQSaY3zBiDmOcOkdxIiqR0wp4VpBrg1gEAiAOzTu949H7Db3bg1iZCR3mLvpYXLsI+oMYYuVtCXmxXw=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.2.0": {"name": "json5", "version": "0.2.0", "description": "JSON for the ES5 era.", "keywords": ["json", "es5"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "main": "lib/json5.js", "bin": {"json5": "lib/cli.js"}, "dependencies": {}, "devDependencies": {"mocha": "~1.0.3"}, "scripts": {"build": "./lib/cli.js -c package.json5", "test": "mocha --ui exports --reporter spec"}, "homepage": "http://json5.org/", "repository": {"type": "git", "url": "git://github.com/aseemk/json5.git"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_id": "json5@0.2.0", "optionalDependencies": {}, "engines": {"node": "*"}, "_engineSupported": true, "_npmVersion": "1.1.19", "_nodeVersion": "v0.6.16", "_defaultsLoaded": true, "dist": {"shasum": "b6d7035c70c4570f883c7edc759de3ae03db3343", "tarball": "https://registry.npmjs.org/json5/-/json5-0.2.0.tgz", "integrity": "sha512-jzu3hxGhztAzldgKTbsW240mtPIgR6foddu9HqQgpv0ML2RcjE0mjyLro0XE92YAQYpTpcByY80vVzlKOM64xA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDp0Ek0D86yz2hVqeKs1qAOnJHT2IpPrR43KrBxkOnKUQIhALyliOcQvDX1p5PSUsPWqMDtcJrN7O3ipjX1aV+SpEUi"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.4.0": {"name": "json5", "version": "0.4.0", "description": "JSON for the ES5 era.", "keywords": ["json", "es5"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "main": "lib/json5.js", "bin": {"json5": "lib/cli.js"}, "dependencies": {}, "devDependencies": {"mocha": "~1.0.3"}, "scripts": {"build": "./lib/cli.js -c package.json5", "test": "mocha --ui exports --reporter spec"}, "homepage": "http://json5.org/", "repository": {"type": "git", "url": "https://github.com/aseemk/json5.git"}, "bugs": {"url": "https://github.com/aseemk/json5/issues"}, "_id": "json5@0.4.0", "dist": {"shasum": "054352e4c4c80c86c0923877d449de176a732c8d", "tarball": "https://registry.npmjs.org/json5/-/json5-0.4.0.tgz", "integrity": "sha512-5EEuuI7oad0d6c2PcrTRLoLH2JNuI/aJxHsVT2hVFK6fKHu+MXONdhzzzNAlb3JXMeuN1o+kDU78fV1YH6VmKQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH714i2QrT2XCQu+YGwxe/ynpNs4MN0epHcyUJiJTI3pAiBUbssnJoSJyxUEgp0mVFRuMJC536UAFgDSUuXjUXZYAg=="}]}, "_from": ".", "_npmVersion": "1.3.21", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}}, "0.5.0": {"name": "json5", "version": "0.5.0", "description": "JSON for the ES5 era.", "keywords": ["json", "es5"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "main": "lib/json5.js", "bin": {"json5": "lib/cli.js"}, "files": ["lib/"], "dependencies": {}, "devDependencies": {"gulp": "^3.9.1", "gulp-jshint": "^2.0.0", "jshint": "^2.9.1", "jshint-stylish": "^2.1.0", "mocha": "^2.4.5"}, "scripts": {"build": "node ./lib/cli.js -c package.json5", "test": "mocha --ui exports --reporter spec"}, "homepage": "http://json5.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/aseemk/json5.git"}, "gitHead": "c58c026a58dd0b71401f7aa99e891291a60820e3", "bugs": {"url": "https://github.com/aseemk/json5/issues"}, "_id": "json5@0.5.0", "_shasum": "9b20715b026cbe3778fd769edccd822d8332a5b2", "_from": ".", "_npmVersion": "3.7.3", "_nodeVersion": "5.9.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, "dist": {"shasum": "9b20715b026cbe3778fd769edccd822d8332a5b2", "tarball": "https://registry.npmjs.org/json5/-/json5-0.5.0.tgz", "integrity": "sha512-WDgahySBucTVnQuzQHoVh6BKKg3TFBUExSwYOPwA4it9xtspn3erHYkdEx1AXXkHN38L7O6v6lmqLiXh/GnxhA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBmtEfZTLKwFk6O8iyXi+m0N6fIJy9bfgbjPd8uNzkrKAiEA4irgxACPbdoR1iSQ1XLdI+Ez0zngHbPh74Y3OpW6nEc="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/json5-0.5.0.tgz_1458238912216_0.23146007652394474"}, "directories": {}}, "0.5.1": {"name": "json5", "version": "0.5.1", "description": "JSON for the ES5 era.", "keywords": ["json", "es5"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "main": "lib/json5.js", "bin": {"json5": "lib/cli.js"}, "files": ["lib/"], "dependencies": {}, "devDependencies": {"gulp": "^3.9.1", "gulp-jshint": "^2.0.1", "jshint": "^2.9.3", "jshint-stylish": "^2.2.1", "mocha": "^3.1.0"}, "scripts": {"build": "node ./lib/cli.js -c package.json5", "test": "mocha --ui exports --reporter spec"}, "homepage": "http://json5.org/", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/aseemk/json5.git"}, "gitHead": "6be6a70e250e6fbbf42db75cd1f6a1aadeeeeb07", "bugs": {"url": "https://github.com/aseemk/json5/issues"}, "_id": "json5@0.5.1", "_shasum": "1eade7acc012034ad84e2396767ead9fa5495821", "_from": ".", "_npmVersion": "3.10.9", "_nodeVersion": "6.9.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, "dist": {"shasum": "1eade7acc012034ad84e2396767ead9fa5495821", "tarball": "https://registry.npmjs.org/json5/-/json5-0.5.1.tgz", "integrity": "sha512-4xrs1aW+6N5DalkqSVA8fxh458CXvR99WU8WLKmq4v8eWAL86Xo3BVqyd3SkA9wEVjCMqyvvRRkshAdOnBp5rw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICJ8a/nbvIS5MEGcA4wfsTHtMQRPaq8UXWWd8Angn5CBAiB+R9n0Qj1SbYR+Sgka0V6IcsXTml1gl8ho/JCoPJ9HEA=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/json5-0.5.1.tgz_1480284434295_0.5308296058792621"}, "directories": {}}, "1.0.0-dates": {"name": "json5", "version": "1.0.0-dates", "description": "JSON for humans.", "main": "lib/index.js", "bin": {"json5": "lib/cli.js"}, "browser": "dist/index.js", "files": ["lib/", "dist/"], "scripts": {"build": "babel-node build/build.js && babel src -d lib && rollup -c", "lint": "eslint --fix build src", "prepublishOnly": "npm run lint && npm test && npm run production", "pretest": "cross-env NODE_ENV=test npm run build", "preversion": "npm run lint && npm test && npm run production", "production": "cross-env NODE_ENV=production npm run build && babel-node build/check-package.js", "test": "nyc --reporter=html --reporter=text mocha"}, "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/json5/json5/issues"}, "homepage": "http://json5.org/", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.0", "babel-register": "^6.26.0", "babelrc-rollup": "^3.0.0", "cross-env": "^5.0.5", "del": "^3.0.0", "eslint": "^4.7.1", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.1.1", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1", "mocha": "^3.5.3", "nyc": "^11.2.1", "regenerate": "^1.3.3", "rollup": "^0.50.0", "rollup-plugin-babel": "^3.0.2", "rollup-plugin-commonjs": "^8.2.1", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "sinon": "^3.3.0", "unicode-9.0.0": "^0.7.4"}, "gitHead": "0e39932e5cbf843bb8b55968316a301fc805d3cc", "_id": "json5@1.0.0-dates", "_npmVersion": "5.3.0", "_nodeVersion": "6.11.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, "dist": {"integrity": "sha512-oRVnDhzcXyhsn6Bie8Uhzf08sk8vgl1terZbaHyoU43njf/XrRX7TaT7c4X3+UrT3v4pTz4V7IZae3dPywLjaA==", "shasum": "dbaef53fb06f3cc1868d280805240642d9672d2d", "tarball": "https://registry.npmjs.org/json5/-/json5-1.0.0-dates.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC01uVdhbIM8vwovz8YlZZwu1bfpvNw7ugL5ZR9USHpqAIgaGObo0JRBgjRzqfEqajYTsEaqVtNO8X4Eq9P49/2J2Y="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json5-1.0.0-dates.tgz_1506214104567_0.6818019594065845"}, "directories": {}}, "1.0.0-regexps": {"name": "json5", "version": "1.0.0-regexps", "description": "JSON for humans.", "main": "lib/index.js", "bin": {"json5": "lib/cli.js"}, "browser": "dist/index.js", "files": ["lib/", "dist/"], "scripts": {"build": "babel-node build/build.js && babel src -d lib && rollup -c", "lint": "eslint --fix build src", "prepublishOnly": "npm run lint && npm test && npm run production", "pretest": "cross-env NODE_ENV=test npm run build", "preversion": "npm run lint && npm test && npm run production", "production": "cross-env NODE_ENV=production npm run build && babel-node build/check-package.js", "test": "nyc --reporter=html --reporter=text mocha"}, "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/json5/json5/issues"}, "homepage": "http://json5.org/", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.0", "babel-register": "^6.26.0", "babelrc-rollup": "^3.0.0", "cross-env": "^5.0.5", "del": "^3.0.0", "eslint": "^4.7.1", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.1.1", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1", "mocha": "^3.5.3", "nyc": "^11.2.1", "regenerate": "^1.3.3", "rollup": "^0.50.0", "rollup-plugin-babel": "^3.0.2", "rollup-plugin-commonjs": "^8.2.1", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "sinon": "^3.3.0", "unicode-9.0.0": "^0.7.4"}, "gitHead": "6bafe5a11e87bcd36e713bfb962b818906301770", "_id": "json5@1.0.0-regexps", "_npmVersion": "5.3.0", "_nodeVersion": "6.11.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, "dist": {"integrity": "sha512-+EiGNxMroEMZltFs+BMzQ5uDABfTdCQcezFlPqvxaZyTspya6wIiLkf5HruO+tAAyA+N06OhokhDKeitxGUrIw==", "shasum": "b9104fab83c81ee0e6f375ed1a191ed6d16af6f5", "tarball": "https://registry.npmjs.org/json5/-/json5-1.0.0-regexps.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFmIm2wkLjy8aEYiKKx+DsJTasqEEaqV28ZgiEoAGrEtAiEApgQ/Ap1VnArZlQ1apu0DdReOvWs2D0BAWBoOSRKtCdA="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json5-1.0.0-regexps.tgz_1506216081686_0.4857315414119512"}, "directories": {}}, "1.0.0-beta": {"name": "json5", "version": "1.0.0-beta", "description": "JSON for humans.", "main": "lib/index.js", "bin": {"json5": "lib/cli.js"}, "browser": "dist/index.js", "files": ["lib/", "dist/"], "scripts": {"build": "babel-node build/build.js && babel src -d lib && rollup -c", "lint": "eslint --fix build src", "prepublishOnly": "npm run lint && npm test && npm run production", "pretest": "cross-env NODE_ENV=test npm run build", "preversion": "npm run lint && npm test && npm run production", "production": "cross-env NODE_ENV=production npm run build && babel-node build/check-package.js", "test": "nyc --reporter=html --reporter=text mocha"}, "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/json5/json5/issues"}, "homepage": "http://json5.org/", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.0", "babel-register": "^6.26.0", "babelrc-rollup": "^3.0.0", "cross-env": "^5.0.5", "del": "^3.0.0", "eslint": "^4.7.1", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.1.1", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1", "mocha": "^3.5.3", "nyc": "^11.2.1", "regenerate": "^1.3.3", "rollup": "^0.50.0", "rollup-plugin-babel": "^3.0.2", "rollup-plugin-commonjs": "^8.2.1", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "sinon": "^3.3.0", "unicode-9.0.0": "^0.7.4"}, "gitHead": "cc8fd89487f0107014c214ce593f84947e011798", "_id": "json5@1.0.0-beta", "_npmVersion": "5.3.0", "_nodeVersion": "6.11.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, "dist": {"integrity": "sha512-Opyud0vCd0lggCT1y9KcfqK1U4IigqsoyA2r63xmq3QrJFA4O4iwgA712s7o5RX5x+bY9le0EN1Xh3JNFzXp7Q==", "shasum": "ae83de99802cef1fa29b7b60a9330658b898eb1e", "tarball": "https://registry.npmjs.org/json5/-/json5-1.0.0-beta.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDYxEGQJ7G4IHjyeqmM8hW/GkA0DHz2ryzmgBjy17m9+gIgeGMhJaLKvmAwwTokE9yi1AKtDHGiEipKF2VcJmsl/wk="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json5-1.0.0-beta.tgz_1506326565554_0.24072266649454832"}, "directories": {}}, "1.0.0-dates-2": {"name": "json5", "version": "1.0.0-dates-2", "description": "JSON for humans.", "main": "lib/index.js", "bin": {"json5": "lib/cli.js"}, "browser": "dist/index.js", "files": ["lib/", "dist/"], "scripts": {"build": "babel-node build/build.js && babel src -d lib && rollup -c", "lint": "eslint --fix build src", "prepublishOnly": "npm run lint && npm test && npm run production", "pretest": "cross-env NODE_ENV=test npm run build", "preversion": "npm run lint && npm test && npm run production", "production": "cross-env NODE_ENV=production npm run build && babel-node build/check-package.js", "test": "nyc --reporter=html --reporter=text mocha"}, "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/json5/json5/issues"}, "homepage": "http://json5.org/", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.0", "babel-register": "^6.26.0", "babelrc-rollup": "^3.0.0", "cross-env": "^5.0.5", "del": "^3.0.0", "eslint": "^4.7.1", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.1.1", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1", "mocha": "^3.5.3", "nyc": "^11.2.1", "regenerate": "^1.3.3", "rollup": "^0.50.0", "rollup-plugin-babel": "^3.0.2", "rollup-plugin-commonjs": "^8.2.1", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "sinon": "^3.3.0", "unicode-9.0.0": "^0.7.4"}, "gitHead": "29a9a53d10c06525bcf9debc9046c6b2c29d8bc4", "_id": "json5@1.0.0-dates-2", "_npmVersion": "5.3.0", "_nodeVersion": "6.11.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, "dist": {"integrity": "sha512-+vdcrGOSPf+7U1WkAMaBTMZbg15pdWjyTYsQpLBgSgJWhu+ES1gcGC9t4vJz6gTzdMn9/KVN/EdckJp9DHy1bQ==", "shasum": "87452e3f68b42fa8e7dfd51ad504cf54fc70397e", "tarball": "https://registry.npmjs.org/json5/-/json5-1.0.0-dates-2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDa5dJWZ+MR8AM0NsliZfcC2sasFto02H7cW+wkV7XH6wIgXeoF+isX3GxEKoaeYWfOxipAswpAFzDBF7CTbIJygpA="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json5-1.0.0-dates-2.tgz_1506360666057_0.23160419380292296"}, "directories": {}}, "1.0.0-regexps-2": {"name": "json5", "version": "1.0.0-regexps-2", "description": "JSON for humans.", "main": "lib/index.js", "bin": {"json5": "lib/cli.js"}, "browser": "dist/index.js", "files": ["lib/", "dist/"], "scripts": {"build": "babel-node build/build.js && babel src -d lib && rollup -c", "lint": "eslint --fix build src", "prepublishOnly": "npm run lint && npm test && npm run production", "pretest": "cross-env NODE_ENV=test npm run build", "preversion": "npm run lint && npm test && npm run production", "production": "cross-env NODE_ENV=production npm run build && babel-node build/check-package.js", "test": "nyc --reporter=html --reporter=text mocha"}, "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/json5/json5/issues"}, "homepage": "http://json5.org/", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.0", "babel-register": "^6.26.0", "babelrc-rollup": "^3.0.0", "cross-env": "^5.0.5", "del": "^3.0.0", "eslint": "^4.7.1", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.1.1", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1", "mocha": "^3.5.3", "nyc": "^11.2.1", "regenerate": "^1.3.3", "rollup": "^0.50.0", "rollup-plugin-babel": "^3.0.2", "rollup-plugin-commonjs": "^8.2.1", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "sinon": "^3.3.0", "unicode-9.0.0": "^0.7.4"}, "gitHead": "da36c192b92455fef3f3cd181260173e16f07b21", "_id": "json5@1.0.0-regexps-2", "_npmVersion": "5.3.0", "_nodeVersion": "6.11.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, "dist": {"integrity": "sha512-2d/NVn+0xhqc0yZ1YKcUjC14OrC5WByLFnti88Sj/supkFczwTlpyuM8gJsn6xoO9Fh5bEqoYXBvnw3Xt1bdeA==", "shasum": "a631fc69924f9494f14ebf4ba037c871f2e38349", "tarball": "https://registry.npmjs.org/json5/-/json5-1.0.0-regexps-2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAl+MIaXWUo3BvZe9FLP7UUKOPFm/Zi1IBLugY0Q5+57AiEAihDiukVzWD1K/Dy/ofrlnzKYMfAUKXJbjOYujTuZ9Y4="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json5-1.0.0-regexps-2.tgz_1506360743120_0.1735003616195172"}, "directories": {}}, "1.0.0-beta-2": {"name": "json5", "version": "1.0.0-beta-2", "description": "JSON for humans.", "main": "lib/index.js", "bin": {"json5": "lib/cli.js"}, "browser": "dist/index.js", "files": ["lib/", "dist/"], "scripts": {"build": "babel-node build/build.js && babel src -d lib && rollup -c", "lint": "eslint --fix build src", "prepublishOnly": "npm run lint && npm test && npm run production", "pretest": "cross-env NODE_ENV=test npm run build", "preversion": "npm run lint && npm test && npm run production", "production": "cross-env NODE_ENV=production npm run build && babel-node build/check-package.js", "test": "nyc --reporter=html --reporter=text mocha"}, "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/json5/json5/issues"}, "homepage": "http://json5.org/", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.0", "babel-register": "^6.26.0", "babelrc-rollup": "^3.0.0", "cross-env": "^5.0.5", "del": "^3.0.0", "eslint": "^4.7.1", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.1.1", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1", "mocha": "^3.5.3", "nyc": "^11.2.1", "regenerate": "^1.3.3", "rollup": "^0.50.0", "rollup-plugin-babel": "^3.0.2", "rollup-plugin-commonjs": "^8.2.1", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "sinon": "^3.3.0", "unicode-9.0.0": "^0.7.4"}, "gitHead": "92e1ef7d278605be76348b2c80b1af1d36eada86", "_id": "json5@1.0.0-beta-2", "_npmVersion": "5.4.2", "_nodeVersion": "6.11.2", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, "dist": {"integrity": "sha512-0+bPwFn6BoYvmvCTMgJQHp41gyRi3qx0rgxDRdMLAj0Z2ZnAvV66uMen2sl6T9Mc1GAT1T1gIam4t9O+DJojQg==", "shasum": "49302613041a604cc5de1f53c500e5e3e8bcd3a4", "tarball": "https://registry.npmjs.org/json5/-/json5-1.0.0-beta-2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGMh4Oy1em9ImH8V3dkFpRda6d4Ch/6vJe+Z/HJkBXK6AiAX2yv7Q+w2H2lMBbXDaj9NY99Yf3sxpllHZKkzlMEsdg=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json5-1.0.0-beta-2.tgz_1506748952512_0.3764345832169056"}, "directories": {}}, "1.0.0-beta.4": {"name": "json5", "version": "1.0.0-beta.4", "description": "JSON for humans.", "main": "lib/index.js", "bin": {"json5": "lib/cli.js"}, "browser": "dist/index.js", "files": ["lib/", "dist/"], "scripts": {"build": "babel-node build/build.js && babel src -d lib && rollup -c", "lint": "eslint --fix build src", "prepublishOnly": "npm run lint && npm test && npm run production", "pretest": "cross-env NODE_ENV=test npm run build", "preversion": "npm run lint && npm test && npm run production", "production": "cross-env NODE_ENV=production npm run build && babel-node build/check-package.js", "test": "nyc --reporter=html --reporter=text mocha"}, "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/json5/json5/issues"}, "homepage": "http://json5.org/", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.0", "babel-register": "^6.26.0", "babelrc-rollup": "^3.0.0", "cross-env": "^5.0.5", "del": "^3.0.0", "eslint": "^4.7.1", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.1.1", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1", "mocha": "^3.5.3", "nyc": "^11.2.1", "regenerate": "^1.3.3", "rollup": "^0.50.0", "rollup-plugin-babel": "^3.0.2", "rollup-plugin-commonjs": "^8.2.1", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "sinon": "^3.3.0", "unicode-9.0.0": "^0.7.4"}, "gitHead": "f4123c513692f51cc71dcd4ff7eeb470c90ec51d", "_id": "json5@1.0.0-beta.4", "_npmVersion": "5.5.1", "_nodeVersion": "8.9.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, "dist": {"integrity": "sha512-GRMqinNVDjenUZXIYmabsbdgfIRm/5+qOtfu9DynLtM/w3nG5GsYCipq0hh2yk/yJu5ZBiOKbb+hdqkdKNwiYg==", "shasum": "32cd529f822b28574dce7458502b05e1fa78a311", "tarball": "https://registry.npmjs.org/json5/-/json5-1.0.0-beta.4.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCs7PJrmG/u4EujRWZJVEW54dpbCcVhoPEP7EdvFazR5QIgaowTbFGldkg62lPet1D/DXSxjZha3+B27ajSDAScvE8="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json5-1.0.0-beta.4.tgz_1510685717699_0.614513021428138"}, "directories": {}}, "1.0.0": {"name": "json5", "version": "1.0.0", "description": "JSON for humans.", "main": "lib/index.js", "bin": {"json5": "lib/cli.js"}, "browser": "dist/index.js", "files": ["lib/", "dist/"], "scripts": {"build": "babel-node build/build.js && babel src -d lib && rollup -c", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint": "eslint --fix build src", "prepublishOnly": "npm run lint && npm test && npm run production", "pretest": "cross-env NODE_ENV=test npm run build", "preversion": "npm run lint && npm test && npm run production", "production": "cross-env NODE_ENV=production npm run build && babel-node build/check-package.js", "test": "nyc --reporter=html --reporter=text mocha"}, "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/json5/json5/issues"}, "homepage": "http://json5.org/", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.1", "babel-register": "^6.26.0", "babelrc-rollup": "^3.0.0", "coveralls": "^3.0.0", "cross-env": "^5.1.4", "del": "^3.0.0", "eslint": "^4.18.2", "eslint-config-standard": "^11.0.0", "eslint-plugin-import": "^2.9.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-promise": "^3.7.0", "eslint-plugin-standard": "^3.0.1", "mocha": "^5.0.4", "nyc": "^11.4.1", "regenerate": "^1.3.3", "rollup": "^0.56.5", "rollup-plugin-babel": "^3.0.3", "rollup-plugin-commonjs": "^9.0.0", "rollup-plugin-node-resolve": "^3.2.0", "rollup-plugin-uglify": "^3.0.0", "sinon": "^4.4.2", "unicode-9.0.0": "^0.7.5"}, "gitHead": "1d64ece245f33d714938bf8513f65f71886b9941", "_id": "json5@1.0.0", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, "dist": {"integrity": "sha512-4HE2dgY5QAv1hryDi6Nt9p6cKEppXljgBB1z8bn+vrPlXzQTzkE4Qzm5yieSVMnRTcHBaLJgCg5WW7v8FYt4jA==", "shasum": "45f7f2db63d918e468a13197df6cfd9c06448054", "tarball": "https://registry.npmjs.org/json5/-/json5-1.0.0.tgz", "fileCount": 13, "unpackedSize": 87561, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDt0z4P+y8p4GmHw2CGp2TD8tBbUkAV5FdHwS+KY/NTSAiEAoEs/nrNFLvlWSBROfeYdRvYsxRGsmjNwnMU4j5b6iN0="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json5_1.0.0_1520805436077_0.14153615220362936"}, "_hasShrinkwrap": false}, "1.0.1": {"name": "json5", "version": "1.0.1", "description": "JSON for humans.", "main": "lib/index.js", "bin": {"json5": "lib/cli.js"}, "browser": "dist/index.js", "files": ["lib/", "dist/"], "scripts": {"build": "babel-node build/build.js && babel src -d lib && rollup -c", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint": "eslint --fix build src", "prepublishOnly": "npm run lint && npm test && npm run production", "pretest": "cross-env NODE_ENV=test npm run build", "preversion": "npm run lint && npm test && npm run production", "production": "cross-env NODE_ENV=production npm run build", "test": "nyc --reporter=html --reporter=text mocha"}, "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/json5/json5/issues"}, "homepage": "http://json5.org/", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.1", "babel-register": "^6.26.0", "babelrc-rollup": "^3.0.0", "coveralls": "^3.0.0", "cross-env": "^5.1.4", "del": "^3.0.0", "eslint": "^4.18.2", "eslint-config-standard": "^11.0.0", "eslint-plugin-import": "^2.9.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-promise": "^3.7.0", "eslint-plugin-standard": "^3.0.1", "mocha": "^5.0.4", "nyc": "^11.4.1", "regenerate": "^1.3.3", "rollup": "^0.56.5", "rollup-plugin-babel": "^3.0.3", "rollup-plugin-commonjs": "^9.0.0", "rollup-plugin-node-resolve": "^3.2.0", "rollup-plugin-uglify": "^3.0.0", "sinon": "^4.4.2", "unicode-9.0.0": "^0.7.5"}, "gitHead": "072eb402fc107a2f568ba78962d3d99de95032a9", "_id": "json5@1.0.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, "dist": {"integrity": "sha512-aKS4WQjPenRxiQsC93MNfjx+nbF4PAdYzmd/1JIj8HYzqfbu86beTuNgXDzPknWk0n0uARlyewZo4s++ES36Ow==", "shasum": "779fb0018604fa854eacbf6252180d83543e3dbe", "tarball": "https://registry.npmjs.org/json5/-/json5-1.0.1.tgz", "fileCount": 13, "unpackedSize": 88338, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDiMFzoY6UvgCwfgrqJmDes4K1ShCOvSkzTGiPNLsjsRAIgAPpHEPBbICt0FbEZZQctsY+bf//zIZIvdJDsZeXuFSo="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json5_1.0.1_1521344408616_0.5838247870477473"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "json5", "version": "2.0.0", "description": "JSON for humans.", "main": "lib/index.js", "bin": {"json5": "lib/cli.js"}, "browser": "dist/index.js", "files": ["lib/", "dist/"], "engines": {"node": ">=6"}, "scripts": {"build": "rollup -c", "build-package": "node build/package.js", "build-unicode": "node build/unicode.js", "coverage": "tap --coverage-report html test", "lint": "eslint --fix .", "prepublishOnly": "npm run production", "preversion": "npm run production", "production": "npm run lint && npm test && npm run build", "test": "tap -Rspec --100 test", "version": "npm run build-package && git add package.json5"}, "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/json5/json5/issues"}, "homepage": "http://json5.org/", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"eslint": "^5.3.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-node": "^7.0.1", "eslint-plugin-promise": "^3.8.0", "eslint-plugin-standard": "^3.1.0", "regenerate": "^1.4.0", "rollup": "^0.64.1", "rollup-plugin-commonjs": "^9.1.5", "rollup-plugin-node-resolve": "^3.3.0", "rollup-plugin-terser": "^1.0.1", "sinon": "^6.1.5", "tap": "^12.0.1", "unicode-10.0.0": "^0.7.5"}, "gitHead": "3b44f2e496a2cec54ce0c01fa37897c7906836c8", "_id": "json5@2.0.0", "_npmVersion": "6.4.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, "dist": {"integrity": "sha512-0EdQvHuLm7yJ7lyG5dp7Q3X2ku++BG5ZHaJ5FTnaXpKqDrw4pMxel5Bt3oAYMthnrthFBdnZ1FcsXTPyrQlV0w==", "shasum": "b61abf97aa178c4b5853a66cc8eecafd03045d78", "tarball": "https://registry.npmjs.org/json5/-/json5-2.0.0.tgz", "fileCount": 14, "unpackedSize": 147350, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbdktkCRA9TVsSAnZWagAASwwP/iHfO7OIEckQkRPb07vs\nh/n7n2Z3Dc4HWGmxHd5fyLJFsCaa/0DzzhyEHECQ+I5QZbkZg7bVUq+a8d/v\nB/ICBW9ecysFpW0l6HYY7u3Tv+gyyCujqxBC38ak0w+N016Abs4gUe1Th80V\n5dTxgZlvjmrdSi4CyylSrP9ZLaUd4FMFRNF18vi8MmzBWdAyaL9hJkgkcupY\nBkL1uw8jx5RkxoijqjdjCWQeBkIqYqii/9ahlkIVzCnmd3ZOJJ1b0uaAhaKE\nFRYe79SCAY6b+nbqgNGWN3U4DVwgqfiP+Nct+0cFdkRaHmWbmbVNWh4bZFMe\nemibVFdg1QIL15Y9iIGQ9YqdSWxF/CkYVXh/FSn1ey4Qt5UTNu7GFN+6aojD\nQ70/pO9sBogDxOXZ4hT3pIzSIY+tkIyWfmZWroYKMZZNpTrOj4DQss2mG8NK\nRDJco3MZbbWPSza//XmFwvcJrRcMDJbgmlXYDTca2V3CgJMZ+2OzRYRhC/cU\nRC9x7vf6XLulmA+rHLmp0sNnMioXZOulPIDPJxh3GxQXnbdHrjcH30OJSrug\npqbUgVYFG4n1mAtv7uQ/NQ5UojMcGOcGLU1oPDox/UVR+JQQOy8tjFFUOhzS\nr0vm+kyRuDnKVmmCCzQ3PvmL6w0DivZClJseYhr/xh5MNNgwrrmWncaUQX70\nqTl4\r\n=fzaU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHiZE1Ux30wUbDO4BEaXCWaAd9e9jI3aNxW2XwXYuqdWAiBDjLubLC5B5amHAIFYoHZNTk/omG7SF2akGm0kmVyhaA=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json5_2.0.0_1534479204287_0.22685984380044588"}, "_hasShrinkwrap": false}, "2.0.1": {"name": "json5", "version": "2.0.1", "description": "JSON for humans.", "main": "lib/index.js", "bin": {"json5": "lib/cli.js"}, "browser": "dist/index.js", "files": ["lib/", "dist/"], "engines": {"node": ">=6"}, "scripts": {"build": "rollup -c", "build-package": "node build/package.js", "build-unicode": "node build/unicode.js", "coverage": "tap --coverage-report html test", "lint": "eslint --fix .", "prepublishOnly": "npm run production", "preversion": "npm run production", "production": "npm run lint && npm test && npm run build", "test": "tap -Rspec --100 test", "version": "npm run build-package && git add package.json5"}, "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/json5/json5/issues"}, "homepage": "http://json5.org/", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"core-js": "^2.5.7", "eslint": "^5.3.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-node": "^7.0.1", "eslint-plugin-promise": "^3.8.0", "eslint-plugin-standard": "^3.1.0", "regenerate": "^1.4.0", "rollup": "^0.64.1", "rollup-plugin-buble": "^0.19.2", "rollup-plugin-commonjs": "^9.1.5", "rollup-plugin-node-resolve": "^3.3.0", "rollup-plugin-terser": "^1.0.1", "sinon": "^6.1.5", "tap": "^12.0.1", "unicode-10.0.0": "^0.7.5"}, "gitHead": "59eee02b882351268b88ac3b018ae26281240e8b", "_id": "json5@2.0.1", "_npmVersion": "6.3.0", "_nodeVersion": "8.11.3", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, "dist": {"integrity": "sha512-t6N/86QDIRYvOL259jR5c5TbtMnekl2Ib314mGeMh37zAwjgbWHieqijPH7pWaogmJq1F2I4Sphg19U1s+ZnXQ==", "shasum": "3d6d0d1066039eb50984e66a7840e4f4b7a2c660", "tarball": "https://registry.npmjs.org/json5/-/json5-2.0.1.tgz", "fileCount": 14, "unpackedSize": 158770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbeGhfCRA9TVsSAnZWagAAVFQQAIT3bHmfbfs1bMDybcfr\nF4Q7gR74UrMpwozFmeWfz0TAYln2w5khp1RkO6iFBY5yJKRnhkPFnhgvbTjH\npptW2lVHVfYTpyK+dw3w/b/ni6FNL11r0LDuUti4dez/IFxcE1qV8uLgx2UA\ng/qXYb56iNLbUpuFpMQwfk4z+jVC+qN6yzoDxv1d5r5PnhVsaeUIoYWNY1cZ\nZyyJkb7KnZLNStqLHqSr+h9Ra82sAs7xkmYRfa52hjfrQyo6lwBNAT0KFPlf\nzEYJAjr7AjBzDTvuBTb2DQ8pHiUPt5kF0063/B0GWuBz3PVEtkg/pHAfODkg\n3QufgBiz9n1hnRfuAIMNRFEJ+nDcVMBYwsXZyk/+heFHYHezrBa9XlU3eKWf\nYENFfftMpdSHM9W2tDMF6BVJRqxNytq8G0C8jD7/qb5Dds5I+mWDzXXMaQW2\n2sYItH9P8V0N/3s25tzvVVg4MryHYS2gUKANL43xHj/FGZDFroeIBVS88dPU\ncA+6qP5dO2j93bu27FxVeq12kV3hJxX/dP0h4++VdbOKimlikgb2aw9kSHKM\nARNau2a528duNppF/kLH0hXSQQSGxfVj4bVcxdNuOKGJynwQqIXnke9IlUpf\nexRqoDf4q4spsr6fOOGRlctz/BBomWB1RKEpAWK0TDUA0r1keJlw2AkomxZ+\nLdmW\r\n=6qOI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC2gMWBZgtNd5KtnbN6NZxhN64so4bskmEAwlK/7SSI+gIhAIsF1Srk+YzHEy/+HVPgmOHETXJscnxoToGrMH39foEI"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json5_2.0.1_1534617695246_0.8986631311655922"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "json5", "version": "2.1.0", "description": "JSON for humans.", "main": "lib/index.js", "bin": {"json5": "lib/cli.js"}, "browser": "dist/index.js", "engines": {"node": ">=6"}, "scripts": {"build": "rollup -c", "build-package": "node build/package.js", "build-unicode": "node build/unicode.js", "coverage": "tap --coverage-report html test", "lint": "eslint --fix .", "prepublishOnly": "npm run production", "preversion": "npm run production", "production": "npm run lint && npm test && npm run build", "test": "tap -Rspec --100 test", "version": "npm run build-package && git add package.json5"}, "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/json5/json5/issues"}, "homepage": "http://json5.org/", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"core-js": "^2.5.7", "eslint": "^5.3.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-node": "^7.0.1", "eslint-plugin-promise": "^3.8.0", "eslint-plugin-standard": "^3.1.0", "regenerate": "^1.4.0", "rollup": "^0.64.1", "rollup-plugin-buble": "^0.19.2", "rollup-plugin-commonjs": "^9.1.5", "rollup-plugin-node-resolve": "^3.3.0", "rollup-plugin-terser": "^1.0.1", "sinon": "^6.1.5", "tap": "^12.0.1", "unicode-10.0.0": "^0.7.5"}, "gitHead": "69c4a75d345a58a773148dd9c05ce74e668dc87d", "_id": "json5@2.1.0", "_npmVersion": "6.4.0", "_nodeVersion": "8.12.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, "dist": {"integrity": "sha512-8Mh9h6xViijj36g7Dxi+Y4S6hNGV96vcJZr/SrlHh1LR/pEn/8j/+qIBbs44YKl69Lrfctp4QD+AdWLTMqEZAQ==", "shasum": "e7a0c62c48285c628d20a10b85c89bb807c32850", "tarball": "https://registry.npmjs.org/json5/-/json5-2.1.0.tgz", "fileCount": 16, "unpackedSize": 233133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbrbgVCRA9TVsSAnZWagAAamIP/0ocnmt26H7vE3NHdpA1\noyu/1NUXH4KlkglZnAhPiqcq6xyVf9KMM4Nq+1i4tpqDCZth1j0MCu5DanmU\n1FHY/XQEiW9YyNK1mNIorYMxGfcyFAhzGzQmKnW2Kf49yNRyIlD9lgVH+O4E\nfJxGkkVlGVxnxaUBufYzE5JPRGGhljg5nBAt5g7Q5tyBvDLLVDf69SsIsfRF\nPRpFJGZB2zYyxY9PUTGsSt7o9awZJKeH3OQnz9LuHLP0+y5DAEDQkyjR/g8R\nIpkUQ+wXIttVaOagvO3hHRZF0llA10hpyASmjvT+042KOe2njo41xZzNuPr2\nAbBpq8JXuTey1O7Ie4M7tlc6JkOwr18ZVws8YQr01RXYHueC9WhU34RNUAII\n84NkWkb43qBMOr+brq1rAs3QILrDsYbhK38mCD6IzJQSDJejfP6xnpAmtwU1\nXnu6FrDn+4fnOxGEfxIKct7NzEl1mVzG6X/SpT30EdgTKmpPB6AEALfNM+F+\nidirdG5IU6i6JJYNiyqC2JJOWgb/paKGYWkHtRGpM19bJImd+1Hnb/9Gg26Z\nsQ2mTZd3adJhwurZY6lUnDcDrLyVYYRnuhJBnUKrSTLDK/VkdhzhkblvAi3d\n1O6I6VLEYcxUijJE8dEs0PFRw6EeMJ0B6Lr9TLEprqcxbgNXEOEaqOhP2Prc\nr4HH\r\n=n65n\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC2dKDBj1xa9CVl4wNXSJY77btfQ7cYL7iaQDQHjqYH7AIhAOrVdq5xL90VRuYQba+AgtTMaGwL+NPNpTaR8SE8x/r8"}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json5_2.1.0_1538111508433_0.060465334055555875"}, "_hasShrinkwrap": false}, "2.1.1": {"name": "json5", "version": "2.1.1", "description": "JSON for humans.", "main": "lib/index.js", "module": "dist/index.mjs", "bin": {"json5": "lib/cli.js"}, "browser": "dist/index.js", "engines": {"node": ">=6"}, "scripts": {"build": "rollup -c", "build-package": "node build/package.js", "build-unicode": "node build/unicode.js", "coverage": "tap --coverage-report html test", "lint": "eslint --fix .", "prepublishOnly": "npm run production", "preversion": "npm run production", "production": "npm run lint && npm test && npm run build", "test": "tap -Rspec --100 test", "version": "npm run build-package && git add package.json5"}, "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/json5/json5/issues"}, "homepage": "http://json5.org/", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"core-js": "^2.6.5", "eslint": "^5.15.3", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.16.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "regenerate": "^1.4.0", "rollup": "^0.64.1", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.2.1", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-terser": "^1.0.1", "sinon": "^6.3.5", "tap": "^12.6.0", "unicode-10.0.0": "^0.7.5"}, "gitHead": "4425e8e422e05f5925a88f1f370cff9486281b51", "_id": "json5@2.1.1", "_nodeVersion": "10.16.3", "_npmVersion": "6.11.2", "dist": {"integrity": "sha512-l+3HXD0GEI3huGq1njuqtzYK8OYJyXMkOLtQ53pjWh89tvWS2h6l+1zMkYWqlb57+SiQodKZyvMEFb2X+KrFhQ==", "shasum": "81b6cb04e9ba496f1c7005d07b4368a2638f90b6", "tarball": "https://registry.npmjs.org/json5/-/json5-2.1.1.tgz", "fileCount": 16, "unpackedSize": 235263, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdlQ2aCRA9TVsSAnZWagAA7wYP/3zt+LDk86xnARG4b2KI\nejD9LOxo421J1vHFhPwE6jIZuat3qvo5F5MLUWLqD1JkzoSUtZW7xEubSbXf\nco80S1gPjlxcBRo1PhxT8mfBcxP0tSb1Y55b9stg08dWbHc2jbx5zvssJ+sd\nEgKaG8q9RtYfjn+X1yvvmWl/pNjwKWwHiikDc8HGtBX0aeXVAFv/oIZim1vs\nH5RK795alMP5fLHq4Ss4DT/2bk2GPZ+aumPr/STx0aw5XWNPedEwR2ceThLN\nKSn2VR6dt/VDP58aQ6rQwGPZm/kQZgxdVeCPf1BeALp8SrZykndQM3zRTTrR\nshJl9Qs4mInTB6684YybqhlNdmwPYeuBeFmpAJorIMSbcLB9R6c1CWpwRgB2\nVVbp1RBQMFPG9NAOn1XAGkFwigg/EYyYsAt7Wk1AWa6KKjWKA0COyfv2UkkV\nGchDDt/zwUL1NOBGcYN+NYEiTUGjD9qb/oVdKg2UsDvOpGoEL4ehXh2oTWsq\nT54j37UK7U5ocGyjaYZmmKVGKE4Ci/DBFh76guAF90hraclQ0ueh8tMeu4LZ\nam418RMmkW4YVWzEx/nZQyBlYKDHcd+C4X2uLHhkXI8BHOoDeeZ8AF/uh8s3\nuNvDBY8TKs8P3nJDJxclKDjSVpYpnqPBAMxF+a5CgQjwkxzjT3yItqdyADRY\nknS8\r\n=rj8Q\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICl3kdVH7cocHdNAQA4RbidD1QHn2PkIWw1+eTP6cjyvAiB1ZwMFfSSEeIhYEa/2lEc5zPpdK/edPJP5u0GZDf1E5Q=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json5_2.1.1_1570049433724_0.02392821940890033"}, "_hasShrinkwrap": false}, "2.1.2": {"name": "json5", "version": "2.1.2", "description": "JSON for humans.", "main": "lib/index.js", "module": "dist/index.mjs", "bin": {"json5": "lib/cli.js"}, "browser": "dist/index.js", "engines": {"node": ">=6"}, "scripts": {"build": "rollup -c", "build-package": "node build/package.js", "build-unicode": "node build/unicode.js", "coverage": "tap --coverage-report html test", "lint": "eslint --fix .", "prepublishOnly": "npm run production", "preversion": "npm run production", "production": "npm run lint && npm test && npm run build", "test": "tap -Rspec --100 test", "version": "npm run build-package && git add package.json5"}, "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/json5/json5/issues"}, "homepage": "http://json5.org/", "dependencies": {"minimist": "^1.2.5"}, "devDependencies": {"core-js": "^2.6.5", "eslint": "^5.15.3", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.16.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "regenerate": "^1.4.0", "rollup": "^0.64.1", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.2.1", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-terser": "^1.0.1", "sinon": "^6.3.5", "tap": "^12.6.0", "unicode-10.0.0": "^0.7.5"}, "gitHead": "d2796dd2a4db42313d755bf6b62e16110f9c0627", "_id": "json5@2.1.2", "_nodeVersion": "12.16.1", "_npmVersion": "6.14.1", "dist": {"integrity": "sha512-MoUOQ4WdiN3yxhm7NEVJSJrieAo5hNSLQ5sj05OTRHPL9HOBy8u4Bu88jsC1jvqAdN+E1bJmsUcZH+1HQxliqQ==", "shasum": "43ef1f0af9835dd624751a6b7fa48874fb2d608e", "tarball": "https://registry.npmjs.org/json5/-/json5-2.1.2.tgz", "fileCount": 16, "unpackedSize": 235557, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeb9hxCRA9TVsSAnZWagAAMz8QAJyodmBx/6/GrlNkWWe4\no+GeI3fE/VoeYPE2t6PYpIyA2jtnWfiMYbc5UEzPEFjVoVLjG9gyLyyraKNR\nVtC5tKL+J5go4QqN+GZDdxSI573fCsmHeD17LfFeMwpH3kES9ULwTmmcSz9Q\nhw15eKvTibgltXqUR9jzFFt1lQZUNtIairGhpd9fQ8+F/aZc5MVzP16B27fv\nxJzpEt6+80Taii+gxY9brZDd+ToL3K4j2ezKXQVVnwNffI1CT+WPOdYHgkes\n+9qV3WjHCO/yV8KvD4hDwrEhZa5WjpnBeSq4Yl65nlGd/gOT7bKJC2gBVyOn\nfpqb5bNfRjv9LyK/hqQcru9SzbIEy2xFYQTJaE6WnQ+DCJh85Mkns7HcLMHx\nGwviGPoVTaVpKsdQ0ZePhTZEBCZDUL/fsgKdz1XlDV8Sf7w0Vt+pdyyT3dN5\nJfatWgnKWqMryZ47fwbFv/+Z9ruRKvDkbO+jyYxr2wBrRNs1YDEbboSja8FI\nGzDq1Q4VvC6mReSq0RgmRPgfeYLge/8gDUq6D10oPKvCVV80Eduvt17pYQqc\nSSFa6KRBXe5AzRfXGUuHmigYC4yKgXm3hyvSwf02pVpjbz6mrKpn+bG4F40h\nJnIoRyNGI3r4ak/tRowodqu7cFRiU51fhgRZDEjPry/LFvMUDNi91bWeBIRs\nhzcJ\r\n=LHov\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFRcBxeH0Q9Wh7AKNou25A2bskuLXw+FcbXXabuavteyAiBL2q8eayTOT4vK/bVvgAUP0Fj9TdfaXkv9Vf66q8J5Qg=="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json5_2.1.2_1584388209027_0.5538679380467166"}, "_hasShrinkwrap": false}, "2.1.3": {"name": "json5", "version": "2.1.3", "description": "JSON for humans.", "main": "lib/index.js", "module": "dist/index.mjs", "bin": {"json5": "lib/cli.js"}, "browser": "dist/index.js", "engines": {"node": ">=6"}, "scripts": {"build": "rollup -c", "build-package": "node build/package.js", "build-unicode": "node build/unicode.js", "coverage": "tap --coverage-report html test", "lint": "eslint --fix .", "prepublishOnly": "npm run production", "preversion": "npm run production", "production": "npm run lint && npm test && npm run build", "test": "tap -Rspec --100 test", "version": "npm run build-package && git add package.json5"}, "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/json5/json5/issues"}, "homepage": "http://json5.org/", "dependencies": {"minimist": "^1.2.5"}, "devDependencies": {"core-js": "^2.6.5", "eslint": "^5.15.3", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.16.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "regenerate": "^1.4.0", "rollup": "^0.64.1", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.2.1", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-terser": "^1.0.1", "sinon": "^6.3.5", "tap": "^12.6.0", "unicode-10.0.0": "^0.7.5"}, "gitHead": "32bb2cdae4864b2ac80a6d9b4045efc4cc54f47a", "_id": "json5@2.1.3", "_nodeVersion": "12.16.1", "_npmVersion": "6.14.4", "dist": {"integrity": "sha512-KXPvOm8K9IJKFM0bmdn8QXh7udDh1g/giieX0NLCaMnb4hEiVFqnop2ImTXCc5e0/oHz3LTqmHGtExn5hfMkOA==", "shasum": "c9b0f7fa9233bfe5807fe66fcf3a5617ed597d43", "tarball": "https://registry.npmjs.org/json5/-/json5-2.1.3.tgz", "fileCount": 16, "unpackedSize": 236336, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeiREsCRA9TVsSAnZWagAAlCMP/1y7UinfaaTR/JjVT5Bh\nEuSlDfdo7la2tYNEh3dwn8R6dnEPZAwdzimdJ9gzNVaQL2ICs1a4XPcgbUHM\nIkz4AQ79FoSoUkzKmkcWVSuqSIrShGh5J8iSWdQfxxzijbWyCQtq9C3YSWBl\nvNWJNBYnQ7OA/+txjWAaYI2rokcdZv3VsE/dbexxZFjboKKSsR+ZRnu9F6+t\nfqn32olMTOLSBV3g7VF+vVpdBWa3GMCKeZ7NFx05bOtost0/SAVwPXAnDZFn\nhShY7/V/VTx2tnhYaYrFogzP55vLJSru9DXnS8TVDdAbHQ6+EsLAqcPIDR7R\nsVaaxXP8fnGcL6FeLyzljUHELe2P/l7DRkaoF5vRUSTwn+MNvBV5oGaGhehi\n/0K7UYEeduKXghVa2o9bXxIItTrIjygl0bBULAgvSHD2o99OiWDbNnFWFA06\nxAJnbJ8qEZfDUYkKIryDuBvk0ROTpBTAe3PfRHNk2QD0ZFQcghXcVHCs+40X\nZ97sQ7pc4XtsOFM9FiF+rzqg3OLy7BfeADPJJxuLGxMpaFkxRw/SN5Q8e2RQ\nUVPwOBi2qihYyCRoHACkALF59U5dYQ3OTATic+jSEAyF5a26rAbfXOxpePo4\nf9GkQjTsY6f0CE/h5rCYDSFZm+6w9rODvYpXCWbosYFXYI7Dn/RCpoliRTaw\neRo5\r\n=aFBt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBIumz2zsyEMkBiQ6nCCyoLtQnjwlkKyMwhdlG93rZYVAiEA9fDBlGG/tvsfMOfvb3z82f8Cen0CUuUpL8xQeFbfWxI="}]}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}], "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json5_2.1.3_1586041132131_0.5302923352394322"}, "_hasShrinkwrap": false}, "2.2.0": {"name": "json5", "version": "2.2.0", "description": "JSON for humans.", "main": "lib/index.js", "module": "dist/index.mjs", "bin": {"json5": "lib/cli.js"}, "browser": "dist/index.js", "types": "lib/index.d.ts", "engines": {"node": ">=6"}, "scripts": {"build": "rollup -c", "build-package": "node build/package.js", "build-unicode": "node build/unicode.js", "coverage": "tap --coverage-report html test", "lint": "eslint --fix .", "prepublishOnly": "npm run production", "preversion": "npm run production", "production": "npm run lint && npm test && npm run build", "test": "tap -Rspec --100 test", "version": "npm run build-package && git add package.json5"}, "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/json5/json5/issues"}, "homepage": "http://json5.org/", "dependencies": {"minimist": "^1.2.5"}, "devDependencies": {"core-js": "^2.6.5", "eslint": "^5.15.3", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.16.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "regenerate": "^1.4.0", "rollup": "^0.64.1", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.2.1", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-terser": "^1.0.1", "sinon": "^6.3.5", "tap": "^12.6.0", "unicode-10.0.0": "^0.7.5"}, "gitHead": "4cf57da675f55c619f959132eb58a5683ca4a9c7", "_id": "json5@2.2.0", "_nodeVersion": "14.15.4", "_npmVersion": "6.14.10", "dist": {"integrity": "sha512-f+8cldu7X/y7RAJurMEJmdoKXGB/X550w2Nr3tTbezL6RwEE/iMcm+tZnXeoZtKuOq6ft8+CqzEkrIgx1fPoQA==", "shasum": "2dfefe720c6ba525d9ebd909950f0515316c89a3", "tarball": "https://registry.npmjs.org/json5/-/json5-2.2.0.tgz", "fileCount": 21, "unpackedSize": 242315, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgF2BKCRA9TVsSAnZWagAA+gkP/A/cB1upj1OY6FDHeLcx\npL/j1KQ8SIjXrMdtu0Cl182OPx3lxBsfNiYS15C6MKYzLVvukJ64Ht1HRBdI\nwpnD5FXyJydzKDpD13/oKmLCCxe+Ujckm4eyVBsyrUSY96z7idR5nxIGTbzx\n08+O4bOOJ2V2v3m+/LWd7J5SU4x6rebCHRt6C8yKe2HeJcJOAMkZzGtt4hhk\nU/2V/Tpu2H52rfggnmILk0vUudUddaLjkYZjcPTsxbAfJCeyfZx0Iw45e9a2\nPnE0JAWRNBsQYSDicm6/lmnCT4oRo7B0q1d8AaKXqlcLVTwyWVII6wDGySIg\nSFlHH2pQZdriy4E1vXtHikFWBbWRO0+5keiI9w2msWSxxgpRv9m1PhgWgTaa\ni69xfXcZBZfCcIQbgHom3B4gJfY7NwLEz/u+igILTVzaSJS/b2nXxSW485nh\nToHQ7IUDbHPyZQJsA7egE6ZADqDiMNWWgvbq4ZhErEQmYyeU0Kw/g5cLUYCA\nLdSG7jZLjPGTf5FbU5ynxcj058FWx9fCL3wLkxi/EBjcAlupXxh4qKfbXPnP\nWELZTf1Q1A8D4RzwEi0P+4ftCbjkrLeeQ+L/zfydCp897ZBz1FLHTPXb+WsS\n2ncocfPWCYrLDhFBUdy8UqKMERsfbsNRSP96MmLV3RrTpaz3cgKYvc6aBXjI\nQd9t\r\n=fbzO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFFyU4dMfi691Isu57fv54LD52YFuU7rz60pMxxP7+NTAiBVQHrM3fIgocP48lGx9+r1FXY9YvpNDoUggkZ6MvuI0w=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json5_2.2.0_1612144714333_0.3778284592428618"}, "_hasShrinkwrap": false}, "2.2.1": {"name": "json5", "version": "2.2.1", "description": "JSON for humans.", "main": "lib/index.js", "module": "dist/index.mjs", "bin": {"json5": "lib/cli.js"}, "browser": "dist/index.js", "types": "lib/index.d.ts", "engines": {"node": ">=6"}, "scripts": {"build": "rollup -c", "build-package": "node build/package.js", "build-unicode": "node build/unicode.js", "coverage": "tap --coverage-report html test", "lint": "eslint --fix .", "prepublishOnly": "npm run production", "preversion": "npm run production", "production": "npm run lint && npm test && npm run build", "test": "tap -Rspec --100 test", "version": "npm run build-package && git add package.json5"}, "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/json5/json5/issues"}, "homepage": "http://json5.org/", "devDependencies": {"core-js": "^2.6.5", "eslint": "^5.15.3", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.16.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "regenerate": "^1.4.0", "rollup": "^0.64.1", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.2.1", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-terser": "^1.0.1", "sinon": "^6.3.5", "tap": "^12.6.0", "unicode-10.0.0": "^0.7.5"}, "gitHead": "502da86f8e8e2168e301dc5157919935082d0f7b", "_id": "json5@2.2.1", "_nodeVersion": "16.13.2", "_npmVersion": "8.1.3", "dist": {"integrity": "sha512-1hqLFMSrGHRHxav9q9gNjJ5EXznIxGVO09xQRrwplcS8qs28pZ8s8hupZAmqDwZUmVZ2Qb2jnyPOWcDH8m8dlA==", "shasum": "655d50ed1e6f95ad1a3caababd2b0efda10b395c", "tarball": "https://registry.npmjs.org/json5/-/json5-2.2.1.tgz", "fileCount": 20, "unpackedSize": 228701, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiOKiUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrc1A//SuuKE6imiqSY0/zKdRwp5cXeHbu8vi/GaCwNu9m8Ab50PZGT\r\nDlEoFppkYYqa1pJiwFW4OxXYJH8yIeaBFYqu1XeSvlWJrYWvlL5Hnf/e7hS+\r\ndF6YfFgHquMdf+e6wY1jhSaA4qodJauwk7919KTSTDLvtQU0dP7u4HRpKNUZ\r\nSzlzhIP8/PD4BUv4VDTwrzHSxIEd3rKYJgNpChvkkPf/FnK7OWROJNy3n8+q\r\nwxYDAb366euyw4vw0phlWS+5JeHgOZbjIZNeR+ndjfkMZHQZpcUAew231Kw+\r\nPh0xgLHe3rMbV4ioDNtm2WmzFzrQtfFZqNgLySOHcC1jGBiFBCBFy9aAv6uN\r\nGacxM9vcGOGV29XTKhfR4Lxzho93JECRrK0GShSZjD8Q0Kv/QJZSc4lmj1sL\r\nxm0JCbhlo3kW3sbxopPOrMHvSdbZHMTAOWIlAJKrko7YrKtnpuaj3S9WqYRt\r\n31pfXj5RpIRtdvKJv5+fLkEYTWS/zk/efOS0w+G8n0bwZxC4AiSs71P+ya2b\r\ns2K0RxxPD3wyOx8PW1eJkjyxySXQy6MBaKazCCR1uN1Df04sqT04PAwu8U1o\r\noOSzm9W6uOLnVUWnZiv2otkTmZaHwRjidsIV5dteWcR47xVx2E9/54RU1S1Y\r\n4vYHm7wMEwIKo2jjz1vrmxpsMqRGUjt4mFY=\r\n=HXXR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDO2LJBVui71RDqZAsMwMEj5a/4iVzpUpMFzAERCZtvVAiB3AOTzvcWOIXUy73ankaCmMM5TtWnhUoTEj5tn3oma0g=="}]}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json5_2.2.1_1647880340028_0.4886187859668456"}, "_hasShrinkwrap": false}, "2.2.2": {"name": "json5", "version": "2.2.2", "description": "JSON for Humans", "main": "lib/index.js", "module": "dist/index.mjs", "bin": {"json5": "lib/cli.js"}, "browser": "dist/index.js", "types": "lib/index.d.ts", "engines": {"node": ">=6"}, "scripts": {"build": "rollup -c", "build-package": "node build/package.js", "build-unicode": "node build/unicode.js", "coverage": "tap --coverage-report html test", "lint": "eslint --fix .", "lint-report": "eslint .", "prepublishOnly": "npm run production", "preversion": "npm run production", "production": "run-s test build", "tap": "tap -Rspec --100 test", "test": "run-s lint-report tap", "version": "npm run build-package && git add package.json5"}, "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/json5/json5/issues"}, "homepage": "http://json5.org/", "devDependencies": {"core-js": "^2.6.5", "eslint": "^5.15.3", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.16.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "npm-run-all": "^4.1.5", "regenerate": "^1.4.0", "rollup": "^0.64.1", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.2.1", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-terser": "^1.0.1", "sinon": "^6.3.5", "tap": "^12.6.0", "unicode-10.0.0": "^0.7.5"}, "gitHead": "14f8cb186e8abdfaccf6527171da7b1224374650", "_id": "json5@2.2.2", "_nodeVersion": "16.16.0", "_npmVersion": "8.18.0", "dist": {"integrity": "sha512-46Tk9JiOL2z7ytNQWFLpj99RZkVgeHf87yGQKsIkaPz1qSH9UczKH1rO7K3wgRselo0tYMUNfecYpm/p1vC7tQ==", "shasum": "64471c5bdcc564c18f7c1d4df2e2297f2457c5ab", "tarball": "https://registry.npmjs.org/json5/-/json5-2.2.2.tgz", "fileCount": 20, "unpackedSize": 235025, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICC0CeC8FMiPUQwBriLANhdc7Dxh4m9/YdnHOQ7ItsAdAiAsSKPZyyD5JFpKpVurHIkWIYHkKFcebR/aVHfckuZ8zw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnBJnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmruZRAAmLfrYCNkUckPI2IIxxSoIlc1mPpTYDJ8XN24rs/OMk9ffamB\r\nX7KPaTCLwgfT3gDl2/7d49YTr8hUdP02LuiOsvgYGTLIDHbXf+XrfUrbUS0d\r\n0pFGEm9LJAzSIOqJJcgBRqkIzJI+3f1NxhNk6LQGCw8r7CPqJzvUGOVBns9D\r\n+057N8l7jcfHQvyGqQ8XiSGltASdH06wZrXRGxckLj9YX3BzO9tg/JAgDt30\r\niLknxmb4+0EXWGLVwCk+TIqRJTB+lFuIYSJyYdeeNadk6TlGxCwyE5LRUp7U\r\nH4t1rJluK/58jbwBwhpdXvFGuJsVhs5jb5JUKrPdlz/P0b1bQKNFMWzin31d\r\nyvjK47QL87j4WVHuPm8+3Df3QE83AZwrEzEyDP6Yx9iweBtMsAEcBBpuXz3r\r\nVKx8MHma2zvFR+qpBQ1BF2fo546U5FqAYgKF2K9IrsZXIGM/awGSYkpn/c9j\r\nnRlxkFPeaaPllPSVC04pq1dwDkwoKY35wxgEk0NWtfUSfaxdKgIxherndK5d\r\nv4uy6YiUsO1KO3Dp2HZ76/C+bSQRUA1kWy1oSw1naglQnG2UuBOsFEHCRhmt\r\n2MxM6YbnqZyNrmucXgiHiVa9fHQlZStp5lKREPCMCzPnXpJ+HBX0J48q9sxO\r\nvyRBQoYBuE2gs5w9PUo+5ItPoMFjBfgZUzE=\r\n=ZSFY\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json5_2.2.2_1671172710839_0.5338538472578511"}, "_hasShrinkwrap": false}, "1.0.2": {"name": "json5", "version": "1.0.2", "description": "JSON for humans.", "main": "lib/index.js", "bin": {"json5": "lib/cli.js"}, "browser": "dist/index.js", "scripts": {"build": "babel-node build/build.js && babel src -d lib && rollup -c", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint": "eslint --fix build src", "prepublishOnly": "npm run lint && npm test && npm run production", "pretest": "cross-env NODE_ENV=test npm run build", "preversion": "npm run lint && npm test && npm run production", "production": "cross-env NODE_ENV=production npm run build", "test": "nyc --reporter=html --reporter=text mocha"}, "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/json5/json5/issues"}, "homepage": "http://json5.org/", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.1", "babel-register": "^6.26.0", "babelrc-rollup": "^3.0.0", "coveralls": "^3.0.0", "cross-env": "^5.1.4", "del": "^3.0.0", "eslint": "^4.18.2", "eslint-config-standard": "^11.0.0", "eslint-plugin-import": "^2.9.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-promise": "^3.7.0", "eslint-plugin-standard": "^3.0.1", "mocha": "^5.0.4", "nyc": "^11.4.1", "regenerate": "^1.3.3", "rollup": "^0.56.5", "rollup-plugin-babel": "^3.0.3", "rollup-plugin-commonjs": "^9.0.0", "rollup-plugin-node-resolve": "^3.2.0", "rollup-plugin-uglify": "^3.0.0", "sinon": "^4.4.2", "unicode-9.0.0": "^0.7.5"}, "gitHead": "a62db1e51e1031d92ac260f5bb38bbed1fdbc754", "_id": "json5@1.0.2", "_nodeVersion": "18.12.1", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==", "shasum": "63d98d60f21b313b77c4d6da18bfa69d80e1d593", "tarball": "https://registry.npmjs.org/json5/-/json5-1.0.2.tgz", "fileCount": 12, "unpackedSize": 78288, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDwJqRTX/IozS44xtpjqVGLP2by4uSiYfI9zPZWqytwnAiEAhN0bCIdi2fmBbGw524rigqGJKIw/QX6EcY5It+mosXw="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjrxtWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqMAQ/8CdnPfppIS1NhVAtGfJMvdtpMMGLR5eXLSYNkuiw/JuzsBF46\r\neCxagzBhM3o6fnP4FwcXpEjkrTgRLdD3o3+/qhsWy7F6jlIfayQlQAaxM/DQ\r\n859vOWzBBDU5GxpaK1iu2wSs0Rryce2dQJvnsUUJf0yxCeC2mGSDYSJbVLiS\r\nCmNHGRQCPS90+Yg6lb+T4+TSFdCx06XMEleCOX4XjvHA6KzORjDy5D2jvFIw\r\nsSqCtiAUhOL/HPy8UEkHBrPdMv96uKY3reeK6fzVig9BeUGn23AhTm4g/2uO\r\nblE42q//urqVFrDgOhEoL64w/hdgQ6Wp/xao5h65fM7bgDA/9uOgWqjq9tJL\r\nTbxzoKXfJ8jYwxe6V8OkjJyu0xs37s3ClD9t5UoukCBeViD1pRoZzoqyp01l\r\nXeCNqzph/cj1VyYX4aeLUGhq4wfgagBe0fEGgw7bZlzKGI3XlFTd4cH9ifXK\r\nDQA+x/34cBF63JkS6rFqyAgPElgLMj2z9dlQDTomFUwzTtvOAcvl71sOsUxw\r\nD/w3tQT/fijv/z7RPqxBPl9o+aFySl/tipi7j6FexSYS2M7zGJI6S1wH55WF\r\nWCEjd+UFMcltOak5L0c+h7OS3t6p3gkkWVQG3laEWXpaU3+/I53M95Dkfgw2\r\nSlYPBkxtHr+GSU4PEdfaxz0rQSgyLmyic70=\r\n=1f2N\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json5_1.0.2_1672420181972_0.5277952929599428"}, "_hasShrinkwrap": false}, "2.2.3": {"name": "json5", "version": "2.2.3", "description": "JSON for Humans", "main": "lib/index.js", "module": "dist/index.mjs", "bin": {"json5": "lib/cli.js"}, "browser": "dist/index.js", "types": "lib/index.d.ts", "engines": {"node": ">=6"}, "scripts": {"build": "rollup -c", "build-package": "node build/package.js", "build-unicode": "node build/unicode.js", "coverage": "tap --coverage-report html test", "lint": "eslint --fix .", "lint-report": "eslint .", "prepublishOnly": "npm run production", "preversion": "npm run production", "production": "run-s test build", "tap": "tap -Rspec --100 test", "test": "run-s lint-report tap", "version": "npm run build-package && git add package.json5"}, "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/json5/json5/issues"}, "homepage": "http://json5.org/", "devDependencies": {"core-js": "^2.6.5", "eslint": "^5.15.3", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.16.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "npm-run-all": "^4.1.5", "regenerate": "^1.4.0", "rollup": "^0.64.1", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.2.1", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-terser": "^1.0.1", "sinon": "^6.3.5", "tap": "^12.6.0", "unicode-10.0.0": "^0.7.5"}, "gitHead": "c3a75242772a5026a49c4017a16d9b3543b62776", "_id": "json5@2.2.3", "_nodeVersion": "18.12.1", "_npmVersion": "9.2.0", "dist": {"integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "shasum": "78cd6f1a19bdc12b73db5ad0c61efd66c1e29283", "tarball": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "fileCount": 20, "unpackedSize": 235198, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGYgWBvGgtEDWvyYrn00kJYCK8D0UCZEVYyCOYToyoUTAiBsZaKBJLypUDXZCZaRq3f0hbUDquj5NBmJX95feSPTPg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjsG1EACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSEBAAl31BrAqhv7nx0GI6xZTmLpos6GmugbTl6ZTHWVih3SSYkkMs\r\nER4OCchqVYheF54SB6xG2FzptxWIthW3/i0+7/1nkm/Pj0bzVQ9dMIB4cS7O\r\nuHORhg7eHuxoSuaMamKZ5nYEKf38mOsB0B5rQUrafiLeK/MH0MHD2+u9SYCF\r\nAsiYxf14OtZsvQVGVYwLdFv2IjxidZCRnMtB6WF81fmCbmlgbO0hmv9Y7xCd\r\naysaZ+M+RE5fDVUdz2qj62oO2hYiODcng5Z+doMqVHOCb/rjaE0LFdK8OyNt\r\nRH8WKR2TMsAOorcQgbvj5df7JvFetdSTV9N/bGmsHf+u1um8OOGu2gCncgBl\r\nnL40jYyw1+Cjor8BpTLsvoZjPU0zuThgwJBmFx6MSkrWJtjXi4Ajzx2lwVXQ\r\nE8Ph1DL//D/TeYibBGKPJtGr2poOtUBEl1QvlHxpNatWRUjokLlyyUaVkZHG\r\nxZJTJoFFe5FN/yHZpV6/9R4o56kktUZiPh/xkfm6g/Nwu8J6pNLypClMRUt+\r\n9DoHy6gJchbXZ/f9rNmRKwX09Decf5rLxRBteWCuKc03GJzFLCs2AYcROuKk\r\nWe4jw0dYqAmRz1Z/ddDBHZFJR8Mca/TV6WjtRxH2KtidxXvbqgJ0euIhtxx4\r\nVOhM334ovfcgbtP97EjdwxO36Utk9DWT/pE=\r\n=PrQ7\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/json5_2.2.3_1672506691888_0.8752285018523356"}, "_hasShrinkwrap": false}}, "readme": "# JSON5 – JSON for Humans\n\n[![Build Status](https://app.travis-ci.com/json5/json5.svg?branch=main)][Build\nStatus] [![Coverage\nStatus](https://coveralls.io/repos/github/json5/json5/badge.svg)][Coverage\nStatus]\n\nJSON5 is an extension to the popular [JSON] file format that aims to be\neasier to **write and maintain _by hand_ (e.g. for config files)**.\nIt is _not intended_ to be used for machine-to-machine communication.\n(Keep using JSON or other file formats for that. 🙂)\n\nJSON5 was started in 2012, and as of 2022, now gets **[>65M downloads/week](https://www.npmjs.com/package/json5)**,\nranks in the **[top 0.1%](https://gist.github.com/anvaka/********************)** of the most depended-upon packages on npm,\nand has been adopted by major projects like\n**[Chromium](https://source.chromium.org/chromium/chromium/src/+/main:third_party/blink/renderer/platform/runtime_enabled_features.json5;drc=5de823b36e68fd99009a29281b17bc3a1d6b329c),\n[Next.js](https://github.com/vercel/next.js/blob/b88f20c90bf4659b8ad5cb2a27956005eac2c7e8/packages/next/lib/find-config.ts#L43-L46),\n[Babel](https://babeljs.io/docs/en/config-files#supported-file-extensions),\n[Retool](https://community.retool.com/t/i-am-attempting-to-append-several-text-fields-to-a-google-sheet-but-receiving-a-json5-invalid-character-error/7626),\n[WebStorm](https://www.jetbrains.com/help/webstorm/json.html),\nand [more](https://github.com/json5/json5/wiki/In-the-Wild)**.\nIt's also natively supported on **[Apple platforms](https://developer.apple.com/documentation/foundation/jsondecoder/3766916-allowsjson5)**\nlike **MacOS** and **iOS**.\n\nFormally, the **[JSON5 Data Interchange Format](https://spec.json5.org/)** is a superset of JSON\n(so valid JSON files will always be valid JSON5 files)\nthat expands its syntax to include some productions from [ECMAScript 5.1] (ES5).\nIt's also a strict _subset_ of ES5, so valid JSON5 files will always be valid ES5.\n\nThis JavaScript library is a reference implementation for JSON5 parsing and serialization,\nand is directly used in many of the popular projects mentioned above\n(where e.g. extreme performance isn't necessary),\nbut others have created [many other libraries](https://github.com/json5/json5/wiki/In-the-Wild)\nacross many other platforms.\n\n[Build Status]: https://app.travis-ci.com/json5/json5\n\n[Coverage Status]: https://coveralls.io/github/json5/json5\n\n[JSON]: https://tools.ietf.org/html/rfc7159\n\n[ECMAScript 5.1]: https://www.ecma-international.org/ecma-262/5.1/\n\n## Summary of Features\nThe following ECMAScript 5.1 features, which are not supported in JSON, have\nbeen extended to JSON5.\n\n### Objects\n- Object keys may be an ECMAScript 5.1 _[IdentifierName]_.\n- Objects may have a single trailing comma.\n\n### Arrays\n- Arrays may have a single trailing comma.\n\n### Strings\n- Strings may be single quoted.\n- Strings may span multiple lines by escaping new line characters.\n- Strings may include character escapes.\n\n### Numbers\n- Numbers may be hexadecimal.\n- Numbers may have a leading or trailing decimal point.\n- Numbers may be [IEEE 754] positive infinity, negative infinity, and NaN.\n- Numbers may begin with an explicit plus sign.\n\n### Comments\n- Single and multi-line comments are allowed.\n\n### White Space\n- Additional white space characters are allowed.\n\n[IdentifierName]: https://www.ecma-international.org/ecma-262/5.1/#sec-7.6\n\n[IEEE 754]: http://ieeexplore.ieee.org/servlet/opac?punumber=4610933\n\n## Example\nKitchen-sink example:\n\n```js\n{\n  // comments\n  unquoted: 'and you can quote me on that',\n  singleQuotes: 'I can use \"double quotes\" here',\n  lineBreaks: \"Look, Mom! \\\nNo \\\\n's!\",\n  hexadecimal: 0xdecaf,\n  leadingDecimalPoint: .8675309, andTrailing: 8675309.,\n  positiveSign: +1,\n  trailingComma: 'in objects', andIn: ['arrays',],\n  \"backwardsCompatible\": \"with JSON\",\n}\n```\n\nA more real-world example is [this config file](https://github.com/chromium/chromium/blob/feb3c9f670515edf9a88f185301cbd7794ee3e52/third_party/blink/renderer/platform/runtime_enabled_features.json5)\nfrom the Chromium/Blink project.\n\n## Specification\nFor a detailed explanation of the JSON5 format, please read the [official\nspecification](https://json5.github.io/json5-spec/).\n\n## Installation and Usage\n### Node.js\n```sh\nnpm install json5\n```\n\n#### CommonJS\n```js\nconst JSON5 = require('json5')\n```\n\n#### Modules\n```js\nimport JSON5 from 'json5'\n```\n\n### Browsers\n#### UMD\n```html\n<!-- This will create a global `JSON5` variable. -->\n<script src=\"https://unpkg.com/json5@2/dist/index.min.js\"></script>\n```\n\n#### Modules\n```html\n<script type=\"module\">\n  import JSON5 from 'https://unpkg.com/json5@2/dist/index.min.mjs'\n</script>\n```\n\n## API\nThe JSON5 API is compatible with the [JSON API].\n\n[JSON API]:\nhttps://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON\n\n### JSON5.parse()\nParses a JSON5 string, constructing the JavaScript value or object described by\nthe string. An optional reviver function can be provided to perform a\ntransformation on the resulting object before it is returned.\n\n#### Syntax\n    JSON5.parse(text[, reviver])\n\n#### Parameters\n- `text`: The string to parse as JSON5.\n- `reviver`: If a function, this prescribes how the value originally produced by\n  parsing is transformed, before being returned.\n\n#### Return value\nThe object corresponding to the given JSON5 text.\n\n### JSON5.stringify()\nConverts a JavaScript value to a JSON5 string, optionally replacing values if a\nreplacer function is specified, or optionally including only the specified\nproperties if a replacer array is specified.\n\n#### Syntax\n    JSON5.stringify(value[, replacer[, space]])\n    JSON5.stringify(value[, options])\n\n#### Parameters\n- `value`: The value to convert to a JSON5 string.\n- `replacer`: A function that alters the behavior of the stringification\n  process, or an array of String and Number objects that serve as a whitelist\n  for selecting/filtering the properties of the value object to be included in\n  the JSON5 string. If this value is null or not provided, all properties of the\n  object are included in the resulting JSON5 string.\n- `space`: A String or Number object that's used to insert white space into the\n  output JSON5 string for readability purposes. If this is a Number, it\n  indicates the number of space characters to use as white space; this number is\n  capped at 10 (if it is greater, the value is just 10). Values less than 1\n  indicate that no space should be used. If this is a String, the string (or the\n  first 10 characters of the string, if it's longer than that) is used as white\n  space. If this parameter is not provided (or is null), no white space is used.\n  If white space is used, trailing commas will be used in objects and arrays.\n- `options`: An object with the following properties:\n  - `replacer`: Same as the `replacer` parameter.\n  - `space`: Same as the `space` parameter.\n  - `quote`: A String representing the quote character to use when serializing\n    strings.\n\n#### Return value\nA JSON5 string representing the value.\n\n### Node.js `require()` JSON5 files\nWhen using Node.js, you can `require()` JSON5 files by adding the following\nstatement.\n\n```js\nrequire('json5/lib/register')\n```\n\nThen you can load a JSON5 file with a Node.js `require()` statement. For\nexample:\n\n```js\nconst config = require('./config.json5')\n```\n\n## CLI\nSince JSON is more widely used than JSON5, this package includes a CLI for\nconverting JSON5 to JSON and for validating the syntax of JSON5 documents.\n\n### Installation\n```sh\nnpm install --global json5\n```\n\n### Usage\n```sh\njson5 [options] <file>\n```\n\nIf `<file>` is not provided, then STDIN is used.\n\n#### Options:\n- `-s`, `--space`: The number of spaces to indent or `t` for tabs\n- `-o`, `--out-file [file]`: Output to the specified file, otherwise STDOUT\n- `-v`, `--validate`: Validate JSON5 but do not output JSON\n- `-V`, `--version`: Output the version number\n- `-h`, `--help`: Output usage information\n\n## Contributing\n### Development\n```sh\ngit clone https://github.com/json5/json5\ncd json5\nnpm install\n```\n\nWhen contributing code, please write relevant tests and run `npm test` and `npm\nrun lint` before submitting pull requests. Please use an editor that supports\n[EditorConfig](http://editorconfig.org/).\n\n### Issues\nTo report bugs or request features regarding the JSON5 **data format**,\nplease submit an issue to the official\n**[_specification_ repository](https://github.com/json5/json5-spec)**.\n\nNote that we will never add any features that make JSON5 incompatible with ES5;\nthat compatibility is a fundamental premise of JSON5.\n\nTo report bugs or request features regarding this **JavaScript implementation**\nof JSON5, please submit an issue to **_this_ repository**.\n\n### Security Vulnerabilities and Disclosures\nTo report a security vulnerability, please follow the follow the guidelines\ndescribed in our [security policy](./SECURITY.md).\n\n## License\nMIT. See [LICENSE.md](./LICENSE.md) for details.\n\n## Credits\n[Aseem Kishore](https://github.com/aseemk) founded this project.\nHe wrote a [blog post](https://aseemk.substack.com/p/ignore-the-f-ing-haters-json5)\nabout the journey and lessons learned 10 years in.\n\n[Michael Bolin](http://bolinfest.com/) independently arrived at and published\nsome of these same ideas with awesome explanations and detail. Recommended\nreading: [Suggested Improvements to JSON](http://bolinfest.com/essays/json.html)\n\n[Douglas Crockford](http://www.crockford.com/) of course designed and built\nJSON, but his state machine diagrams on the [JSON website](http://json.org/), as\ncheesy as it may sound, gave us motivation and confidence that building a new\nparser to implement these ideas was within reach! The original\nimplementation of JSON5 was also modeled directly off of Doug’s open-source\n[json_parse.js] parser. We’re grateful for that clean and well-documented\ncode.\n\n[json_parse.js]:\nhttps://github.com/douglascrockford/JSON-js/blob/03157639c7a7cddd2e9f032537f346f1a87c0f6d/json_parse.js\n\n[Max Nanasy](https://github.com/MaxNanasy) has been an early and prolific\nsupporter, contributing multiple patches and ideas.\n\n[Andrew Eisenberg](https://github.com/aeisenberg) contributed the original\n`stringify` method.\n\n[Jordan Tucker](https://github.com/jordanbtucker) has aligned JSON5 more closely\nwith ES5, wrote the official JSON5 specification, completely rewrote the\ncodebase from the ground up, and is actively maintaining this project.\n", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "jordan<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "time": {"modified": "2023-03-04T17:04:33.373Z", "created": "2012-05-27T20:32:39.534Z", "0.0.0": "2012-05-27T20:32:41.061Z", "0.0.1": "2012-05-28T02:13:56.934Z", "0.1.0": "2012-06-03T17:05:09.050Z", "0.2.0": "2013-01-28T05:33:40.777Z", "0.4.0": "2014-11-05T03:27:01.312Z", "0.5.0": "2016-03-17T18:21:52.997Z", "0.5.1": "2016-11-27T22:07:14.862Z", "1.0.0-dates": "2017-09-24T00:48:25.674Z", "1.0.0-regexps": "2017-09-24T01:21:22.755Z", "1.0.0-beta": "2017-09-25T08:02:46.557Z", "1.0.0-dates-2": "2017-09-25T17:31:07.206Z", "1.0.0-regexps-2": "2017-09-25T17:32:24.109Z", "1.0.0-beta-2": "2017-09-30T05:22:33.708Z", "1.0.0-beta.4": "2017-11-14T18:55:18.837Z", "1.0.0": "2018-03-11T21:57:16.159Z", "1.0.1": "2018-03-18T03:40:08.742Z", "2.0.0": "2018-08-17T04:13:24.376Z", "2.0.1": "2018-08-18T18:41:35.320Z", "2.1.0": "2018-09-28T05:11:48.600Z", "2.1.1": "2019-10-02T20:50:33.832Z", "2.1.2": "2020-03-16T19:50:09.154Z", "2.1.3": "2020-04-04T22:58:52.387Z", "2.2.0": "2021-02-01T01:58:34.482Z", "2.2.1": "2022-03-21T16:32:20.181Z", "2.2.2": "2022-12-16T06:38:31.044Z", "1.0.2": "2022-12-30T17:09:42.178Z", "2.2.3": "2022-12-31T17:11:32.047Z"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "users": {"delapouite": true, "zeke": true, "bsnote": true, "lestad": true, "darkylin": true, "antixrist": true, "froguard": true, "nmccready": true, "anoubis": true, "kangas": true, "leonzhao": true, "nilz": true, "chinawolf_wyp": true, "xueboren": true, "icognivator": true, "qingleili": true, "kakaman": true, "rochejul": true, "erikvold": true, "xfloops": true, "d-band": true, "raycharles": true, "shuoshubao": true, "serge-nikitin": true, "kontrax": true, "dwqs": true, "bcowgi11": true, "dopustimvladimir": true, "flumpus-dev": true}, "homepage": "http://json5.org/", "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "bugs": {"url": "https://github.com/json5/json5/issues"}, "license": "MIT", "readmeFilename": "README.md"}