{"_id": "@types/stack-utils", "_rev": "465-f826bf0f18a86de757ef2140ee8268e1", "name": "@types/stack-utils", "dist-tags": {"ts2.2": "1.0.1", "ts2.3": "1.0.1", "ts2.4": "1.0.1", "ts2.5": "1.0.1", "ts2.6": "1.0.1", "ts2.7": "1.0.1", "ts2.8": "1.0.1", "ts2.9": "1.0.1", "ts3.0": "1.0.1", "ts3.1": "1.0.1", "ts3.2": "2.0.0", "ts3.3": "2.0.0", "ts3.4": "2.0.0", "ts3.5": "2.0.0", "ts3.6": "2.0.1", "ts3.7": "2.0.1", "ts3.8": "2.0.1", "ts3.9": "2.0.1", "ts4.0": "2.0.1", "ts4.1": "2.0.1", "ts4.2": "2.0.1", "ts4.3": "2.0.1", "ts4.4": "2.0.1", "ts5.8": "2.0.3", "ts5.7": "2.0.3", "latest": "2.0.3", "ts4.5": "2.0.3", "ts4.6": "2.0.3", "ts4.7": "2.0.3", "ts4.8": "2.0.3", "ts4.9": "2.0.3", "ts5.0": "2.0.3", "ts5.1": "2.0.3", "ts5.2": "2.0.3", "ts5.3": "2.0.3", "ts5.4": "2.0.3", "ts5.5": "2.0.3", "ts5.6": "2.0.3", "ts5.9": "2.0.3"}, "versions": {"1.0.0": {"name": "@types/stack-utils", "version": "1.0.0", "license": "MIT", "_id": "@types/stack-utils@1.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "35433f375f564d0f0b17aba8d6effb0d3c0920c3", "tarball": "https://registry.npmjs.org/@types/stack-utils/-/stack-utils-1.0.0.tgz", "integrity": "sha512-htwptwc+pbPmVwRxSCwVz7XPLp1HChDWZ1l/AhF/j6dYQXJX1a/F7V+BHK1mMLqbUL6Z643aP9HOxdVqa1b17A==", "signatures": [{"sig": "MEUCICfim04Z15unCTzQaqCbM09CRD3xD0r4Rme0YsU1kcUfAiEA7VrGmtWV9QNvG6ywcWp6jTNzMi9n2SxcnWEay6PZLBg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for stack-utils", "directories": {}, "dependencies": {}, "peerDependencies": {}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/stack-utils-1.0.0.tgz_1502458953657_0.28087693359702826", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "4a8bdd8a246bd85e4539290064622166c024f461333238af5e98b66a4acab162"}, "1.0.1": {"name": "@types/stack-utils", "version": "1.0.1", "license": "MIT", "_id": "@types/stack-utils@1.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "0a851d3bd96498fa25c33ab7278ed3bd65f06c3e", "tarball": "https://registry.npmjs.org/@types/stack-utils/-/stack-utils-1.0.1.tgz", "integrity": "sha512-l42BggppR6zLmpfU6fq9HEa2oGPEI8yrSPL3GITjfRInppYFahObbIQOQK3UGxEnyQpltZLaPe75046NOZQikw==", "signatures": [{"sig": "MEUCIHWhXWHPESpgoAfpeADWl8b2dAABYYdyutN6N6y39b4cAiEApIcQDjlDPYFLJGnUg7/yqkPVvFWhO2J7ZbyBgesyfUY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for stack-utils", "directories": {}, "dependencies": {}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/stack-utils-1.0.1.tgz_1510076996586_0.37928241747431457", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "c3d5963386c8535320c11b5edfb22c4bf60fb3e4bcbca34f094f7026b9749d86"}, "2.0.0": {"name": "@types/stack-utils", "version": "2.0.0", "license": "MIT", "_id": "@types/stack-utils@2.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "7036640b4e21cc2f259ae826ce843d277dad8cff", "tarball": "https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.0.tgz", "fileCount": 4, "integrity": "sha512-R<PERSON><PERSON>rrySY7A8havqpGObOB4W92QXKJo63/jFLLgpvOtsGUqbQZ9Sbgl35KMm1DjC6j7AvmmU2bIno+3IyEaemaw==", "signatures": [{"sig": "MEQCIHnIMdwCVNn/iXtgDJBEchoW+ZKbwk7K0iW7HchbtmP4AiAAsRbnGyaRzpn41HAJkrpKTOE0gmvOYoxSj2v2ZHTezw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfaUYtCRA9TVsSAnZWagAAWqgP/Auzh5u+WYf1eUruq0eT\nUj5kHRzqVTxz+6htq/sqDwhzgkjJ0SJdjEj3g8N7H1nKz8dFbgKPRdkQh78M\n82kl5q6VvHDVpGMXFMmELAEHopu/7SET/QizyRsS5mu6+34E+Q6cG3nHg6Kt\nRvredZTGmCtb2o+PlnK8Pi1+G1IAEAqRUrMq7+oGm9H/J0/xxIdzMPpTSkD6\nwB150B3pzFYsoFwrzmvkRUskpwNky+wMTOq1z0mpWrreBCpzWI9HoSlRbaaX\nI+C/FaBmLmEmhNMZLXJwxirE+8c2YvaXauUTsTZzmOruzSEch+OFx3iW3PAH\nCyutRDEu56J3UOPg1+tIS0XNli4hrpcLsb591P+D5GvmgcchH9baclKD8OAF\nQ5xMS9NtIcY9PoFduKWgrLORp9vOz1JQidZsP8iiA8t4kj+HeILRimRnw+dJ\nVPGNVTi0u9bx+g4QSWxA9imawU2eIe4kbNIRZX51QJH9FlgJVO71xVH1pJ2g\ndNzWRoF/8hCKppflT6T7yWWBV0L/tisRTmal4kNksPAqplE9i39hDJHFM1dW\noaAWcdVIbV/fzdRfL7L+kWizGWTtxEv+CG0sQ0TStA0ckaYTjJwDk7JQJxl4\ngpM5UiwC1t8UMwOIR16o3QRoIbzUxPU3FJ+6cLR5+/JfMNToCiCSEu3CUOAs\nrd+L\r\n=lUx+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/stack-utils"}, "description": "TypeScript definitions for stack-utils", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/stack-utils_2.0.0_1600734764668_0.7071325380885607", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "53c84779043276d5e313653eabfc4fedae5b103c7dc645555065fdc3b4b0a982"}, "2.0.1": {"name": "@types/stack-utils", "version": "2.0.1", "license": "MIT", "_id": "@types/stack-utils@2.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/stack-utils", "dist": {"shasum": "20f18294f797f2209b5f65c8e3b5c8e8261d127c", "tarball": "https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.1.tgz", "fileCount": 4, "integrity": "sha512-Hl219/BT5fLAaz6NDkSuhzasy49dwQS/DSdu4MdggFB8zcXv7vflBI3xp7FEmkmdDkBUI2bPUNeMttp2knYdxw==", "signatures": [{"sig": "MEQCIBeWwbmK8iyptiWcWR68f7IYvgVaW+WuJmcHeNYRkDekAiBbCWxWvWPHWoAoKoKUKtmFnJePhWef+CDRmW2ywxJXOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg35RRCRA9TVsSAnZWagAAPKkP/RfbCaCC1/XNaSoH33uD\nAzi6mn/bAEbkbe0O0eUco19G0CyaNwDw/YwVLsxBIxF/0aDqKmlqyoarpaLh\nkNyGPpj6s5n2yD+0ZGQub6Rn+17nsiFRVDRaU+55pYS5/TZfmoj2DbvIm4wh\nhTcz3iAlyFe2jXyuxm6jFEZyEsn9bhnbGNnxVZAmSXrQUqw/AxIUyCdjK0Ft\nstY3K99Jr5gX1fBOJknu8XJ1ord1MqyaroD1hthjpg8E9Zo9VZw8nAehoJA3\nv6iwtnHT4yhjJsSeFg/AlgiF72NnFtOQSKsMk9M7XBtQ2kBuTKuyWmmFqnGU\nL8ny+P39G9m5STT5sjRhMOjOV+N7hPrwHjLop9/9hYg5fWy1MQw1ts1ws027\nVoCG/iGRhUS2fRp4SAY0fLKoDK+UbgXhFQmXjxlZcky4s9eg6TL/Apf6g5z5\nopheoC4JKZxK5LhIlYkSD+nIQAxASwbmu3TaZ5szEIpRhhuiECTYyw5XwNGe\nyqp7t11MHfLtYas+6rIeS2CHhwLI2tG+1gFRDN/FyEmXn/W6R7tUGkPDBwgI\n2O5LRgWdYuo1h9b1w5zp5LQUjGksQZujUW3/ksaGRqR6yyoABBMlz1KlgtFI\n3GdgCjW0DFkuPm9ENaVEL2TktNU7PpiCgGCD879xPEt9Y+yauI1t777oIsvQ\nieGL\r\n=xI9m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/stack-utils"}, "description": "TypeScript definitions for stack-utils", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/stack-utils_2.0.1_1625265232935_0.2895202044317464", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "bfc4261f4e970131a82704cd51ce862a2f5c7e33c5447030510300c6fe1ee268"}, "2.0.2": {"name": "@types/stack-utils", "version": "2.0.2", "license": "MIT", "_id": "@types/stack-utils@2.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/stack-utils", "dist": {"shasum": "01284dde9ef4e6d8cef6422798d9a3ad18a66f8b", "tarball": "https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.2.tgz", "fileCount": 5, "integrity": "sha512-g7CK9nHdwjK2n0ymT2CW698FuWJRIx+RP6embAzZ2Qi8/ilIrA1Imt2LVSeHUzKvpoi7BhmmQcXz95eS0f2JXw==", "signatures": [{"sig": "MEUCIQD48Zm/8SpUSuNs3Yi1j6Pi5oOXj7CtHcvYcFMjxTAI8AIgFuNGexi9y9dh9cXcI0R8hmA253Rcnv/cwF/XwutDCnw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6426}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/stack-utils"}, "description": "TypeScript definitions for stack-utils", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/stack-utils_2.0.2_1697643078897_0.11400343378811062", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "80f04f2e68d110acd200ffbfdd0ab9defadd3c9e99d7249aeedb5da0e95fae87"}, "2.0.3": {"name": "@types/stack-utils", "version": "2.0.3", "license": "MIT", "_id": "@types/stack-utils@2.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/stack-utils", "dist": {"shasum": "6209321eb2c1712a7e7466422b8cb1fc0d9dd5d8", "tarball": "https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.3.tgz", "fileCount": 5, "integrity": "sha512-9aEbYZ3TbYMznPdcdr3SmIrLXwC/AKZXQeCf9Pgao5CKb8CyHuEX5jzWPTkvregvhRJHcpRO6BFoGW9ycaOkYw==", "signatures": [{"sig": "MEUCIQDMjw6oVM5EaOyw9v/0jcVSclkhAEUg1A1O7Y7Yym4PlwIgMalKOH23Ol8Qc5oZMgLcMd5ZE7yOJ3rLQYtKsiO5t3E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6426}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/stack-utils"}, "description": "TypeScript definitions for stack-utils", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/stack-utils_2.0.3_1699376438422_0.7620630988387651", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ab51d155e7946b0b0e0edab741811a35172a48de2674195feb31eaaf7bf992b7"}}, "time": {"created": "2017-08-11T13:42:33.725Z", "modified": "2025-02-23T07:52:32.642Z", "1.0.0": "2017-08-11T13:42:33.725Z", "1.0.1": "2017-11-07T17:49:56.648Z", "2.0.0": "2020-09-22T00:32:44.863Z", "2.0.1": "2021-07-02T22:33:53.060Z", "2.0.2": "2023-10-18T15:31:19.106Z", "2.0.3": "2023-11-07T17:00:38.573Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/stack-utils", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/stack-utils"}, "description": "TypeScript definitions for stack-utils", "contributors": [{"url": "https://github.com/BendingBender", "name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}