{"_id": "execa", "_rev": "120-20cc2f59ffb61264640cdd3cfe9b5c1f", "name": "execa", "dist-tags": {"next": "2.0.0-alpha.0", "latest": "9.6.0"}, "versions": {"0.1.0": {"name": "execa", "version": "0.1.0", "keywords": ["exec", "execute", "fork", "child", "process", "execfile", "file", "bin", "binary"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@0.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "xo": {"ignores": ["test.js"]}, "dist": {"shasum": "4aa2ee98eaeb921a394aeca1e0966e50765b8053", "tarball": "https://registry.npmjs.org/execa/-/execa-0.1.0.tgz", "integrity": "sha512-83wxT8ms6ThmP+C3tbEf8vDKdi7xL+4zNEkYsEm6eF/9vjPwHMX91Oji9XZ3KGXbTv4W0OwRQ7n3F22X4APvXA==", "signatures": [{"sig": "MEYCIQDRA6zyJDJriIpWb2QmAXC5zIPGrNfXdsE+dkIUXKhhlwIhAN+HhLN/+0TTo9L5A4/nasSG1QoZJSvVjdViZrlnZcOn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "4aa2ee98eaeb921a394aeca1e0966e50765b8053", "engines": {"node": ">=0.12"}, "gitHead": "a9d06c8d1e2e7c2b8a2a368bb07a9dbabfc16bd9", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/execa", "type": "git"}, "_npmVersion": "2.14.7", "description": "A better child_process.{execFile,exec}", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"strip-eof": "^1.0.0", "object-assign": "^4.0.1", "cross-spawn-async": "2.0.1"}, "devDependencies": {"xo": "*", "ava": "*"}}, "0.1.1": {"name": "execa", "version": "0.1.1", "keywords": ["exec", "execute", "fork", "child", "process", "execfile", "file", "bin", "binary"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@0.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "xo": {"ignores": ["test.js"]}, "dist": {"shasum": "b09c2a9309bc0ef0501479472db3180f8d4c3edd", "tarball": "https://registry.npmjs.org/execa/-/execa-0.1.1.tgz", "integrity": "sha512-UnD+pXMpCodPQoDLxea4c/tLdAFsd/863M7eBfFrzQwrAMx42BYDurkmB3FsLNEdMZ1htfkI9zpx8r2V9m5ogg==", "signatures": [{"sig": "MEUCIQD+qeVUalQz0nl3zRwAFbxUrbtUyjnOmk2ef+155777TgIgdaIYELk48GOao+wHgJxwZHqlHSSMF1/O/wLb5oCbhVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "b09c2a9309bc0ef0501479472db3180f8d4c3edd", "engines": {"node": ">=0.12"}, "gitHead": "10df60c077ec53b10f083c89f5267c3d19af8248", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/execa", "type": "git"}, "_npmVersion": "2.14.7", "description": "A better child_process.{execFile,exec}", "directories": {}, "_nodeVersion": "4.2.1", "dependencies": {"strip-eof": "^1.0.0", "object-assign": "^4.0.1", "cross-spawn-async": "^2.1.1"}, "devDependencies": {"xo": "*", "ava": "*"}}, "0.2.0": {"name": "execa", "version": "0.2.0", "keywords": ["exec", "execute", "fork", "child", "process", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@0.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "dist": {"shasum": "3178cf42a19d63726f2731e30239007c19f5bf4d", "tarball": "https://registry.npmjs.org/execa/-/execa-0.2.0.tgz", "integrity": "sha512-EkjFWks0mOv3IPcrCszeG5Tuboyy54KMrg0YjldoxVDBPPQkMNzDpemyJ6uEKHn+pOv71m8e3RDeUUwxdJlA8w==", "signatures": [{"sig": "MEUCIFP9KhzPOIo4aXZNnkblfESxHB36s5CuwsnyeJQpwgNtAiEAq/PmYlBQXYVmSCMVHOBTgrapNzdePrhUosbV6b4J3DQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "3178cf42a19d63726f2731e30239007c19f5bf4d", "engines": {"node": ">=0.12"}, "gitHead": "c210feb770aad510b6f1689b589d2a0b4bbba435", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/execa", "type": "git"}, "_npmVersion": "2.14.12", "description": "A better `child_process`", "directories": {}, "_nodeVersion": "4.2.4", "dependencies": {"path-key": "^1.0.0", "strip-eof": "^1.0.0", "npm-run-path": "^1.0.0", "object-assign": "^4.0.1", "cross-spawn-async": "^2.1.1"}, "devDependencies": {"xo": "*", "ava": "*", "cat-names": "^1.0.2"}}, "0.2.1": {"name": "execa", "version": "0.2.1", "keywords": ["exec", "execute", "fork", "child", "process", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@0.2.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "dist": {"shasum": "14d4f6eb9c9264220da410be73b9bdba69c90d19", "tarball": "https://registry.npmjs.org/execa/-/execa-0.2.1.tgz", "integrity": "sha512-m3/FSRwd5ZwcWud4ElGMqH+tZOaAUasTvnfagm4tPM0DPwIzZ2ZXQ3cx2nkXus2+6OuS6KztYIOGw1e4hnXqYw==", "signatures": [{"sig": "MEUCIQDUiK3TgPnvACXWQ8/RkKhTs1AXzFBEo7oVcbvJWMtkYAIgWxpRrRGWcZq9D7Tz0ruBsAm98pceh5tXbe1grV6OKTg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "14d4f6eb9c9264220da410be73b9bdba69c90d19", "engines": {"node": ">=0.12"}, "gitHead": "6e1d0c74408a6028115b16adfb12e3ae3322e3a4", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/execa", "type": "git"}, "_npmVersion": "2.14.12", "description": "A better `child_process`", "directories": {}, "_nodeVersion": "4.2.4", "dependencies": {"path-key": "^1.0.0", "strip-eof": "^1.0.0", "npm-run-path": "^1.0.0", "object-assign": "^4.0.1", "cross-spawn-async": "^2.1.1"}, "devDependencies": {"xo": "*", "ava": "*", "cat-names": "^1.0.2"}}, "0.2.2": {"name": "execa", "version": "0.2.2", "keywords": ["exec", "execute", "fork", "child", "process", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@0.2.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "dist": {"shasum": "e2ead472c2c31aad6f73f1ac956eef45e12320cb", "tarball": "https://registry.npmjs.org/execa/-/execa-0.2.2.tgz", "integrity": "sha512-zmBGzLd3nhA/NB9P7VLoceAO6vyYPftvl809Vjwe5U2fYI9tYWbeKqP3wZlAw9WS+znnkogf/bhSU+Gcn2NbkQ==", "signatures": [{"sig": "MEUCIAVQoSREmaDRHy7XfkqTmDOz1MbLJDqQ3AASMhrJA0uyAiEAgHxwR4HQOV3Mu2+PRu6FapKn7wrrmnr4uNxBoR8gUOE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "e2ead472c2c31aad6f73f1ac956eef45e12320cb", "engines": {"node": ">=0.12"}, "gitHead": "f63fefa5dad103be2ae8ac74a2d1412cf68d4a7e", "scripts": {"test": "xo && ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/sindresorhus/execa", "type": "git"}, "_npmVersion": "2.14.12", "description": "A better `child_process`", "directories": {}, "_nodeVersion": "4.2.4", "dependencies": {"path-key": "^1.0.0", "strip-eof": "^1.0.0", "npm-run-path": "^1.0.0", "object-assign": "^4.0.1", "cross-spawn-async": "^2.1.1"}, "devDependencies": {"xo": "*", "ava": "*", "cat-names": "^1.0.2"}}, "0.3.0": {"name": "execa", "version": "0.3.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@0.3.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "dist": {"shasum": "a144cf33f2c1b69d4c0e29fa4c5afc956346e24c", "tarball": "https://registry.npmjs.org/execa/-/execa-0.3.0.tgz", "integrity": "sha512-7evPG+NsML0PuUTKprHnSDKBZXfouIuSLcc5WqJxnKBVpLMwrOZNPssiZIUHE6S7mW2pD+aPuVg4MNUcnqIwnA==", "signatures": [{"sig": "MEQCIDaFg8Pny1XZeHVOytdB8uy9FjCv+5HYjEn2qjQycPF3AiB7w9JRg1TFHtJOmf2I9gqS23yaR6qgeOrH5P0CAcv1rw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "a144cf33f2c1b69d4c0e29fa4c5afc956346e24c", "engines": {"node": ">=0.12"}, "gitHead": "f7863497e034c1bbbf3fbef6e7522c0643c7e309", "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "3.8.7", "description": "A better `child_process`", "directories": {}, "_nodeVersion": "4.4.2", "dependencies": {"path-key": "^1.0.0", "strip-eof": "^1.0.0", "npm-run-path": "^1.0.0", "object-assign": "^4.0.1", "cross-spawn-async": "^2.1.1"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^6.4.0", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "get-stream": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa-0.3.0.tgz_1461359594289_0.6561324871145189", "host": "packages-12-west.internal.npmjs.com"}}, "0.4.0": {"name": "execa", "version": "0.4.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@0.4.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "dist": {"shasum": "4eb6467a36a095fabb2970ff9d5e3fb7bce6ebc3", "tarball": "https://registry.npmjs.org/execa/-/execa-0.4.0.tgz", "integrity": "sha512-QPexBaNjeOjyiZ47q0FCukTO1kX3F+HMM0EWpnxXddcr3MZtElILMkz9Y38nmSZtp03+ZiSRMffrKWBPOIoSIg==", "signatures": [{"sig": "MEUCIQDudxHuiJP5mUijRZo+NwzUcSmFMCrEL823DK2n4xYrZAIgK0ooBmOCRS8CI/3Tq6BGiLPZ+cPkF4TGclrnBNqBigw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js"], "_shasum": "4eb6467a36a095fabb2970ff9d5e3fb7bce6ebc3", "engines": {"node": ">=0.12"}, "gitHead": "c72609605b1ce2fcf42b913cd91e74503cd8d629", "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "3.8.8", "description": "A better `child_process`", "directories": {}, "_nodeVersion": "4.4.2", "dependencies": {"path-key": "^1.0.0", "is-stream": "^1.1.0", "strip-eof": "^1.0.0", "npm-run-path": "^1.0.0", "object-assign": "^4.0.1", "cross-spawn-async": "^2.1.1"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^6.4.0", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "get-stream": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa-0.4.0.tgz_1461691655825_0.48842903040349483", "host": "packages-12-west.internal.npmjs.com"}}, "0.5.0": {"name": "execa", "version": "0.5.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@0.5.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "xo": {"esnext": true}, "nyc": {"exclude": ["node_modules", "**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "dist": {"shasum": "a57456764b990e3e52f6eff7f17a9cc2ff2e7ccc", "tarball": "https://registry.npmjs.org/execa/-/execa-0.5.0.tgz", "integrity": "sha512-wtvDYrVKSSUfxb6jwgFNX8RhtE34ErP9s9ychsBAJQB5+vlpcKHMg0ea5ewQYEwXKDnQ2MkJyHLh5BqORap1XA==", "signatures": [{"sig": "MEYCIQC5HB6BnwoKnm/dKtXYIRVlFKuu7/Jmv64k1zal/kgMfwIhAOrwpX1M0p4Mzrx06kRJkldp4QVXlYCG8yP6UBbHbOfk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "a57456764b990e3e52f6eff7f17a9cc2ff2e7ccc", "engines": {"node": ">=4"}, "gitHead": "842be7c11a33c71177ea03ff12412047f3d8bd07", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "A better `child_process`", "directories": {}, "_nodeVersion": "6.6.0", "dependencies": {"is-stream": "^1.1.0", "strip-eof": "^1.0.0", "get-stream": "^2.2.0", "cross-spawn": "^4.0.0", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^8.3.0", "delay": "^1.3.1", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "is-running": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa-0.5.0.tgz_1475656096594_0.8247949031647295", "host": "packages-16-east.internal.npmjs.com"}}, "0.5.1": {"name": "execa", "version": "0.5.1", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@0.5.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "xo": {"esnext": true}, "nyc": {"exclude": ["node_modules", "**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "dist": {"shasum": "de3fb85cb8d6e91c85bcbceb164581785cb57b36", "tarball": "https://registry.npmjs.org/execa/-/execa-0.5.1.tgz", "integrity": "sha512-R66dW/hW3I8yV77Wg4xn6zMguRPUgt59VLm5e85NrOF05ZdPn7YOfPBSw0E9epJDvuzwVWEG+HmEaQ4muYuWKQ==", "signatures": [{"sig": "MEQCIGUEPVPF6LMUrU2r2iALEZrIpWUSnOvPSgeWgXzlWkJEAiAnHO1Obp8jrcF6PSewRQSNLptwme6H7TIlo2wIQeR9zw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "de3fb85cb8d6e91c85bcbceb164581785cb57b36", "engines": {"node": ">=4"}, "gitHead": "e5598cf42a5433ff1f7954f9cd31a57b429d4875", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "A better `child_process`", "directories": {}, "_nodeVersion": "4.6.2", "dependencies": {"is-stream": "^1.1.0", "p-finally": "^1.0.0", "strip-eof": "^1.0.0", "get-stream": "^2.2.0", "cross-spawn": "^4.0.0", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^8.3.0", "delay": "^1.3.1", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "is-running": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa-0.5.1.tgz_1483889519424_0.4603614055085927", "host": "packages-12-west.internal.npmjs.com"}}, "0.6.0": {"name": "execa", "version": "0.6.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@0.6.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "xo": {"esnext": true}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "dist": {"shasum": "934fc9f04a9febb4d4b449d976e92cfd95ef4f6e", "tarball": "https://registry.npmjs.org/execa/-/execa-0.6.0.tgz", "integrity": "sha512-U6/rTP/MZk7s9cWW9mBADmnsoFfr1xh4zKMhEpIFxkL9xy0Mf7gQ6Frdh1fZs4mCQ1+uTFnZXAGyNKrUaG9/xA==", "signatures": [{"sig": "MEUCIQCsdwbrbq8Lm3OA78yxirpDuiGMIGvpVfwneKyk6kmqvQIgUzaJlXl1vn/EwON97EgReNSAF24HGmXQrQwdOysrRUs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "934fc9f04a9febb4d4b449d976e92cfd95ef4f6e", "engines": {"node": ">=4"}, "gitHead": "af6667af5efcfc1470606ce5eb433017c3b3ae0a", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "A better `child_process`", "directories": {}, "_nodeVersion": "4.6.2", "dependencies": {"is-stream": "^1.1.0", "p-finally": "^1.0.0", "strip-eof": "^1.0.0", "get-stream": "^3.0.0", "cross-spawn": "^5.0.1", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^10.0.0", "delay": "^1.3.1", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "is-running": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa-0.6.0.tgz_1483954675252_0.4171230620704591", "host": "packages-18-east.internal.npmjs.com"}}, "0.6.1": {"name": "execa", "version": "0.6.1", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@0.6.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "xo": {"esnext": true}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "dist": {"shasum": "79eda42ade78c387718b0aad48e0f573b5525cde", "tarball": "https://registry.npmjs.org/execa/-/execa-0.6.1.tgz", "integrity": "sha512-K/iPrbzE77EgAv5oc7yDOIMaEsdfOzW+OOthWTA0gaxrU0Ht8ropx1iwbkzwSmEAuLuxuRW34MhsVJDZi2sZzA==", "signatures": [{"sig": "MEQCIB6/BddzgUudJwmtw0a+fCFv7t+icGTL8wzNqpGT8FbTAiBwfscQL1LYfC/Zitl5MWIRtkGzcnvA23M3g582wQlNsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "79eda42ade78c387718b0aad48e0f573b5525cde", "engines": {"node": ">=4"}, "gitHead": "f2d80c68df34804a039af441fdfeffb84624d100", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "A better `child_process`", "directories": {}, "_nodeVersion": "7.7.2", "dependencies": {"is-stream": "^1.1.0", "p-finally": "^1.0.0", "strip-eof": "^1.0.0", "get-stream": "^3.0.0", "cross-spawn": "^5.0.1", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^10.0.0", "delay": "^1.3.1", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "is-running": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa-0.6.1.tgz_1489386883556_0.7629855342675", "host": "packages-12-west.internal.npmjs.com"}}, "0.6.2": {"name": "execa", "version": "0.6.2", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@0.6.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "xo": {"esnext": true}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "dist": {"shasum": "04e9e38dec6b8e770cf0fb6cf7ef945260c67bbb", "tarball": "https://registry.npmjs.org/execa/-/execa-0.6.2.tgz", "integrity": "sha512-hToO3Nj6/4Kt1Smql/8BX1ZF7l+2apBz8DFSa/bZ20uXWcEYIk2/4UshrvU328gUo0nx7habO4MvdBll3UqeQw==", "signatures": [{"sig": "MEUCIQCjwzcnzIPo1wAToep+kKT4DhjkPVX3PR7L2pBX7XtauwIgSrRoYUFQwnFEI7BMZEf7HnokxyJIdg4+/e+lvhYGy5w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "04e9e38dec6b8e770cf0fb6cf7ef945260c67bbb", "engines": {"node": ">=4"}, "gitHead": "f28c01d854f1427674be2c9c2f9b220fce4dd970", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "A better `child_process`", "directories": {}, "_nodeVersion": "4.7.3", "dependencies": {"is-stream": "^1.1.0", "p-finally": "^1.0.0", "strip-eof": "^1.0.0", "get-stream": "^3.0.0", "cross-spawn": "^5.0.1", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^10.0.0", "delay": "^1.3.1", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "is-running": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa-0.6.2.tgz_1489989437493_0.39953839825466275", "host": "packages-18-east.internal.npmjs.com"}}, "0.6.3": {"name": "execa", "version": "0.6.3", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@0.6.3", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "xo": {"esnext": true}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "dist": {"shasum": "57b69a594f081759c69e5370f0d17b9cb11658fe", "tarball": "https://registry.npmjs.org/execa/-/execa-0.6.3.tgz", "integrity": "sha512-/teX3MDLFBdYUhRk8WCBYboIMUmqeizu0m9Z3YF3JWrbEh/SlZg00vLJSaAGWw3wrZ9tE0buNw79eaAPYhUuvg==", "signatures": [{"sig": "MEQCIBFa7Grkbh7FZP1CJkJt9FSuijfSZ2UwA8notKG6yejNAiAJSWilOXZDGiljBbiexWfaXRo4JIloef0GO4cYu2iC9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "57b69a594f081759c69e5370f0d17b9cb11658fe", "engines": {"node": ">=4"}, "gitHead": "2892941f582608f2e1f79b584761392906553feb", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "A better `child_process`", "directories": {}, "_nodeVersion": "4.7.3", "dependencies": {"is-stream": "^1.1.0", "p-finally": "^1.0.0", "strip-eof": "^1.0.0", "get-stream": "^3.0.0", "cross-spawn": "^5.0.1", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^10.0.0", "delay": "^1.3.1", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "is-running": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa-0.6.3.tgz_1490099460925_0.15050783357582986", "host": "packages-12-west.internal.npmjs.com"}}, "0.7.0": {"name": "execa", "version": "0.7.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@0.7.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "dist": {"shasum": "944becd34cc41ee32a63a9faf27ad5a65fc59777", "tarball": "https://registry.npmjs.org/execa/-/execa-0.7.0.tgz", "integrity": "sha512-RztN09XglpYI7aBBrJCPW95jEH7YF1UEPOoX9yDhUTPdp7mK+CQvnLTuD10BNXZ3byLTu2uehZ8EcKT/4CGiFw==", "signatures": [{"sig": "MEQCIBDnforPODYtZLUoigWoHvhlnEl/6If6vWkOAF0D2viVAiBG/DYW1VO4g1IhTuRmZB8GpDB9DGF4MCnBHg2Nc0t6jw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "944becd34cc41ee32a63a9faf27ad5a65fc59777", "engines": {"node": ">=4"}, "gitHead": "b4d1c8613fd068e3c36f11e7bff672d008ac88f9", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "A better `child_process`", "directories": {}, "_nodeVersion": "4.8.3", "dependencies": {"is-stream": "^1.1.0", "p-finally": "^1.0.0", "strip-eof": "^1.0.0", "get-stream": "^3.0.0", "cross-spawn": "^5.0.1", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "delay": "^2.0.0", "tempfile": "^2.0.0", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "is-running": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa-0.7.0.tgz_1497045041009_0.3423430174589157", "host": "s3://npm-registry-packages"}}, "0.8.0": {"name": "execa", "version": "0.8.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@0.8.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "dist": {"shasum": "d8d76bbc1b55217ed190fd6dd49d3c774ecfc8da", "tarball": "https://registry.npmjs.org/execa/-/execa-0.8.0.tgz", "integrity": "sha512-zDWS+Rb1E8BlqqhALSt9kUhss8Qq4nN3iof3gsOdyINksElaPyNBtKUMTR62qhvgVWR0CqCX7sdnKe4MnUbFEA==", "signatures": [{"sig": "MEQCIEvhknIVDwvs9eo7+ys/lJwfNAm4wRm9+v9xQfGNkZhQAiAfBg6l9jymVKiW6wkgkMIrYjmntN1iHjvoPBIBkNXfUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_from": ".", "files": ["index.js", "lib"], "_shasum": "d8d76bbc1b55217ed190fd6dd49d3c774ecfc8da", "engines": {"node": ">=4"}, "gitHead": "dc7e21b0754985b75a5e89917c9035beac1f1f2e", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "2.15.11", "description": "A better `child_process`", "directories": {}, "_nodeVersion": "4.8.3", "dependencies": {"is-stream": "^1.1.0", "p-finally": "^1.0.0", "strip-eof": "^1.0.0", "get-stream": "^3.0.0", "cross-spawn": "^5.0.1", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "delay": "^2.0.0", "tempfile": "^2.0.0", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "is-running": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa-0.8.0.tgz_1501965886445_0.050055725732818246", "host": "s3://npm-registry-packages"}}, "0.9.0": {"name": "execa", "version": "0.9.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@0.9.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "dist": {"shasum": "adb7ce62cf985071f60580deb4a88b9e34712d01", "tarball": "https://registry.npmjs.org/execa/-/execa-0.9.0.tgz", "integrity": "sha512-BbUMBiX4hqiHZUA5+JujIjNb6TyAlp2D5KLheMjMluwOuzcnylDL4AxZYLLn1n2AGB49eSWwyKvvEQoRpnAtmA==", "signatures": [{"sig": "MEQCIE1v1tOAzLcbVMuT+2weviApdz8tpLrCa8/YGmKp2vTOAiBQKEiRxAEbgC5xlN26rWVKU9t+/C24GI4rDPymqK8Tdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "files": ["index.js", "lib"], "engines": {"node": ">=4"}, "gitHead": "b01989521f514f3578c48c8302e648008ed4fd0d", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A better `child_process`", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"is-stream": "^1.1.0", "p-finally": "^1.0.0", "strip-eof": "^1.0.0", "get-stream": "^3.0.0", "cross-spawn": "^5.0.1", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "delay": "^2.0.0", "tempfile": "^2.0.0", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "is-running": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa-0.9.0.tgz_1515713388594_0.54020432732068", "host": "s3://npm-registry-packages"}}, "0.10.0": {"name": "execa", "version": "0.10.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@0.10.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "dist": {"shasum": "ff456a8f53f90f8eccc71a96d11bdfc7f082cb50", "tarball": "https://registry.npmjs.org/execa/-/execa-0.10.0.tgz", "fileCount": 6, "integrity": "sha512-7XOMnz8Ynx1gGo/3hyV9loYNPWM94jG3+3T3Y8tsfSstFmETmENCMU/A/zj8Lyaj1lkgEepKepvd6240tBRvlw==", "signatures": [{"sig": "MEYCIQDqlttFc0uGGZRUZJUrLyNelkQwgWUm5hHbonuqkTQ2ngIhAKWaS0IUcxbxfy32jVBhN50JJ7kbPT8kvhZbL8nAqhax", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19657}, "files": ["index.js", "lib"], "engines": {"node": ">=4"}, "gitHead": "658f2201116ae3014b26a25e8a9ae63ba2ac41ca", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A better `child_process`", "directories": {}, "_nodeVersion": "8.9.4", "dependencies": {"is-stream": "^1.1.0", "p-finally": "^1.0.0", "strip-eof": "^1.0.0", "get-stream": "^3.0.0", "cross-spawn": "^6.0.0", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "delay": "^2.0.0", "tempfile": "^2.0.0", "cat-names": "^1.0.2", "coveralls": "^2.11.9", "is-running": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa_0.10.0_1521089995380_0.5877823423115993", "host": "s3://npm-registry-packages"}}, "0.11.0": {"name": "execa", "version": "0.11.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@0.11.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "dist": {"shasum": "0b3c71daf9b9159c252a863cd981af1b4410d97a", "tarball": "https://registry.npmjs.org/execa/-/execa-0.11.0.tgz", "fileCount": 6, "integrity": "sha512-k5AR22vCt1DcfeiRixW46U5tMLtBg44ssdJM9PiXw3D8Bn5qyxFCSnKY/eR22y+ctFDGPqafpaXg2G4Emyua4A==", "signatures": [{"sig": "MEQCIG0h7WrKN78Y2vJjZSLhK+M3jC3YNyl5Dpm+UBpiI53JAiBVSN7GWtkKU4QLK89bZaokMUbM8IJ7L4tC05JG6bDlZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbenSGCRA9TVsSAnZWagAAjWkP/0L0L1ZM7PSdEOxI/s3H\nyh1oubc/ek2l3amjnFU37EzbhsDPRxEdFxmjl8m327L2SKxZbSETaf0Z/tnc\nvsoZ0Cveb32RvJCZYmQTEYmYQSk/sXfoeNlIaI/lvzndINHwakq37W8INHIz\n36SbmwSPQFNR9vGuHExGx6jB93+vkPHDHgFhYLDlGZfH05SpGf4M3JDBAXBw\ny+gdLLDmqCgSJlhH5bBlcEvHnXusX+vleyxp5Y0/hDpL+Yd1Lln4u32fBPqP\nbXFj14F+0xzXS8baBKjB/OfOF+5GsFuw3WWhmC1/EecFloYpNlMhg1qxkYhL\n/Ves1LDYxwEm/NV7mfJ3lK7dV1KpTqhmpcDwo+1ME3eiEpkHDOVkaxIAVjmJ\nckcYEQPi2XBKWdQ3qO6+1k30ZUQmV8zHPFmk/0ZvZZ4QwE//rhLh5FsekOUo\nsZcASLcvYvPHFhQcsCNk61oZGMIWoohB3FfapAylFZdn24SQhQIufNAxlC19\n8gGN29tlcfhq5BD22RJJBXOmHbrz2zrI/kx+nNqAVaeg5p5WD197KO4is0zN\nqyM5t1QIc3oHg7XsszYaLm5Er4ZCKzAq4NovEpg3lWs0r47cA3DmSFBdViae\nLWTnLT02DLv3hSmJIHkJHErnnYpQZWo9/hbDfEgOz0ci2Uvl9vLK0XM5HRU8\nqpGB\r\n=D7f/\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["index.js", "lib"], "engines": {"node": ">=6"}, "gitHead": "20ab0a1c10b815fb4eac1e11638803d6d09b3f1d", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A better `child_process`", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"is-stream": "^1.1.0", "p-finally": "^1.0.0", "strip-eof": "^1.0.0", "get-stream": "^4.0.0", "cross-spawn": "^6.0.0", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^13.0.1", "delay": "^3.0.0", "tempfile": "^2.0.0", "cat-names": "^1.0.2", "coveralls": "^3.0.1", "is-running": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa_0.11.0_1534751877736_0.15763076989916147", "host": "s3://npm-registry-packages"}}, "1.0.0": {"name": "execa", "version": "1.0.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@1.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "dist": {"shasum": "c6236a5bb4df6d6f15e88e7f017798216749ddd8", "tarball": "https://registry.npmjs.org/execa/-/execa-1.0.0.tgz", "fileCount": 6, "integrity": "sha512-adbxcyWV46qiHyvSp50TKt05tB4tK3HcmF7/nxfAdhnox83seTDbwnaqKO4sXRy7roHAIFqJP/Rw/AuEbX61LA==", "signatures": [{"sig": "MEUCIQCOWOrgDmqTobyyEUTdH1RO/RYmWTuj+n8k36GBDGvAfwIgDgZSiHBQmLEgjzEDxq2LEpob8Er+dVP8Q1L6+eXguDg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19885, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgubQCRA9TVsSAnZWagAAIsEQAIs35D02H15nByFfN/Ht\nMtyJPq3y8M4HKouIKuHWiO1auu6fmt/uhFpzNJJfnwhJoZBUPs/OZBi2w8jr\n4zglnRGX5w323oLVFEZHWIUny6YSNnDsxjKnDkrKw6j/3Z/dAo6o/QPO4/4n\nVgyLhQQuhvNnB6z6iH9tgpxTwyVcVetWqJOBT3T4idkbgAmZAsE/m/1RoUUN\nuq/72dV8H4jomYCvKvoeVsj+RpeTuCGoQFwoN/xjkuHh0EqWp3D13wJyc8Pc\n2tS2+mf/0F<PERSON>aMFEELoMV6W6dBI320xSBgD1mc0zJ8fy//oDmjctiIKVKEQRJ\nCPiQIuKKZ2HlC7cHUnGK9KNHk3DLiHVi6gJxepzdoSUArrF4J23v7JyQxzpm\nR12+DwMIL+TF3e/+XljqfpmNlbShh1Bvmx56ih2to9JafN0BobOHRMrQcqSz\nmcnFQCpQIGx4Cck0/TaumL3mZDsZ9DS6jPKrYEeJ5MkgU0Y2/p1MIB6dbhae\nvGJRmYlDuXm7DNTDdpuUo+vTYtRWvXA+O7K1V7lwavUbJ/eA+dlwxfcAKJFD\n6QKrVKtnCJmQqfP9rwJSXCYtWQ90+gGfoNDijcpoQj9H3pqcCnM0bAKaJbOm\n0s+k2TpbCFu9sqlGAYLZl3uwV3FqdyjrAWW/zjvn/S7O6cxjiYFk/Qhn+OS4\ns9FU\r\n=B9g6\r\n-----END PGP SIGNATURE-----\r\n"}, "files": ["index.js", "lib"], "engines": {"node": ">=6"}, "gitHead": "c8dccf7de66c65f4b9b821ec00871fea386fb35f", "scripts": {"test": "xo && nyc ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "6.4.0", "description": "A better `child_process`", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"is-stream": "^1.1.0", "p-finally": "^1.0.0", "strip-eof": "^1.0.0", "get-stream": "^4.0.0", "cross-spawn": "^6.0.0", "signal-exit": "^3.0.0", "npm-run-path": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^13.0.1", "delay": "^3.0.0", "tempfile": "^2.0.0", "cat-names": "^1.0.2", "coveralls": "^3.0.1", "is-running": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa_1.0.0_1535305424092_0.2806930373405232", "host": "s3://npm-registry-packages"}}, "2.0.0-alpha.0": {"name": "execa", "version": "2.0.0-alpha.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@2.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "dist": {"shasum": "e9525fa152c1d1da165448f77505db3a7aa57c0d", "tarball": "https://registry.npmjs.org/execa/-/execa-2.0.0-alpha.0.tgz", "fileCount": 6, "integrity": "sha512-hYFS8zsUOomvssfNT8E7U5FJ3JLQgOxSE4s1ZVpxgWzL8eZX0g+GOg/FDrIXnLMlcEIyYHtpsvskn3eHaEUBpg==", "signatures": [{"sig": "MEYCIQCeAVtvLcM/OMgnMLMEgO93vXICVrwcU3RUJNoqNCiYCAIhAL3gsiOlD7DZct8ZOWBn5rrSw6flBwIFffb+pVW0dJv5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43829, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCSObCRA9TVsSAnZWagAARiwP/3ars0mbLZWXz99tPO+N\nieaaHQa/ixJAxs2QY7pGwzL7nFG+YSeHExvwkbL7+lUhuGU+8oy57m2Y68c4\np6fX15GrMF0PpekBamHbH0ntveAwit97M3RnYm2FdZV4oTVeopur8Vik0Dis\nYdsrIscRqxdsM2L7bYr0VciggJEgynjStvCxYs8CCJTyH8VsRt03A8YRC1aU\nBaSZeqKxBZejQEm2yxkNtFO9PQXYSt0gwU2q1FUxe4+4s499Q7V8pLtqF6p9\nNFyWfeqbbHJLa+dcZBqjNi7OvBzatna3LqycY/sJKetnZeR7CYqBoYE0q/La\nF2D8utEdJFfQW9BVAcJecu1qqtQNCCdiiNQUrIdCP6yfJLsLobetu155jfQS\nI9XvyTZg7Cf8pIz7jLzLp3+HNNYBoN29LSmDbuwudTzvtqPvBBkD6vGZAVp8\nGGlYcfnOezyRILmlEa6LhNdPpOv9OK94XuZDo6IwnqYEV9cSpbPuUrozFNQq\n0v1oPeD08QexlGEsnBnS9YW8vTNWEJpneDtUNV0WTHAZjYKqClebuEjIjA9Q\niLBfS1tK3nwq2ccs+hqjAHKKKdh030eoI1HdgEb7Y5dc0dvcr5QG2GiNMGGE\nYdkHjZ+zSPeR4h68pNvbZCPc4sTu02NfClu8RiFfq8Xnei/VJ0gZINEtxiPQ\nKPSD\r\n=wTql\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "1880029f1090e9419010d6a43e85e9d20b305069", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "8.16.0", "dependencies": {"is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^6.0.5", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^3.0.0", "strip-final-newline": "^2.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "readme.md", "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/execa_2.0.0-alpha.0_1560880026342_0.693813967595307", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "execa", "version": "2.0.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@2.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "dist": {"shasum": "5524c9739710e603e97c6dfc3f6ff6bff2819885", "tarball": "https://registry.npmjs.org/execa/-/execa-2.0.0.tgz", "fileCount": 8, "integrity": "sha512-+ym7S09yUVPHEhYBsdLm53ZjCmCSeAQVtM/iN9dDj9tbvcBnCeBXTXHPWR9HXzht+vslGROteM8bSUdr4YszUg==", "signatures": [{"sig": "MEQCIBfOIBxR0PiIbNdaMG6DUIRr4TRnjAc4B039noHtMID2AiBHTPtgSsfTDXF3DzMy+O6dtvJcGnB6DuFP38rtYd1Qcg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45890, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEgTzCRA9TVsSAnZWagAAlpwP/0bQY0rGMMR2Nc3aN+gI\nflCPqr4gPHOLYm+BAbN9mRX7FdligHlfD2NxIxZRt6tzm+lutU6176GGJAic\naljEbzuh7ISrTNMtQqh9ZbZygBxrr44CLp6EkzZZHO3Wcq3T6/lTSP5E1daE\nzYSA/40n/g9++k4WOean+x1A5j8oci2UHsyCi8YhBEFr9OLhZ59KU6hst/8O\n3O5saM67YIquIj7fbQtumgleeCTUDon8+73iiriHloO8vvcEQF0kvAXEoC6l\nS86R9wfbAg1/J4J9X10k2OApICs9TnBBdXYULHEjiV4Fc5J9NVEPp9Q3uKHn\nkVfrAkz9PKOr2a5Ornt8FFw57qFNj3sl1JD0a7VGHlW3c5gx+Bg05EHpAL7A\nEFmoRlsoLQpVLwhNbIEQgaR0LuKAdc3ONYJdPhxeQiK8lP7mhRFrMjWITTl2\nIIkzOVAeM/VWSgzb6GuKElGiUc0sBrqZKy7VJQ5Hp7eEPu+KgXwffhnYKt+3\nXE7dglGwswDttDvHqVBsZswxUh5uRrV/aDqZsRYURguSW0WmThV7NMps02h9\nkElOTjrWieeBQBpcRMm9G9ehRCNNDu7v3VIxCzPZfyrlBwd1ldIjHmKPTWfe\nEF/aenUFuPLh2Leq7Z4iCKD2Ldjeh9ACkNGYf5lp0gVeuA5Ou+B2d64Te9nY\n3n4Y\r\n=LuG8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "gitHead": "ec10a4c7b5732edb76e7a20e29ffa3996bcde80a", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "12.4.0", "dependencies": {"is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^6.0.5", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^3.0.0", "strip-final-newline": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/execa_2.0.0_1561462002347_0.23088144698910895", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "execa", "version": "2.0.1", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@2.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "dist": {"shasum": "546a5be56388953409cbf24972d2fd1bb36dbfcd", "tarball": "https://registry.npmjs.org/execa/-/execa-2.0.1.tgz", "fileCount": 9, "integrity": "sha512-pHGXlV7S7ilDda3eaCTcr6zmFTMA3wJo7j+RtNg0uH9sbAasJfVug5RkYOTBLj5g4MOqlsaPUn3HKa/UfTDw8w==", "signatures": [{"sig": "MEUCIC6Sqd9nVse7SRfCdYSjG6jz5QDKPtxZSrkHzRhe0gPZAiEAttyR+g5GCTb46qikWc6LOqHwOr0tVHwsoHxZ8Ul4W90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdE0VKCRA9TVsSAnZWagAABdMP/i1JInnH6kQ0ol50grPQ\nmPbQTWMgKMmVinDV7QXK/sh8LDXh4BYpjIUvmeNvdCJsZ7a4yjxCts6UZmgq\npl0I0ykbZo189vg6J7JCU4gB+lRra1WoD/hItQ1Oceo1rvgqckGnsbWEAJyn\n5OClVzAkyT4divp1E+2agKPuYk33iSfPMeQqoWboqTbEIbIISV2mjlZansAH\nbPmG+zjk5sppny4uD3omoNpS/fb1WWbUhHD1xQ0xS5bWFOikO3xdIYXouci+\nVEPX4puXWliy6FkJ+7Jr5Vlzuqeh4vyxwP26yzvvybZmZbj3dUtywE3jpXqp\nI3dS+MnSBKRU0FZit/y5F4bUEizBvNVIM/0HqbDCgIt93j/pmbPZRL9/kJnH\nLvAHHI/njPfim+0X0Se4KRAy1h0baJ4WPqWN90KThv14fotw2prAQA2szhyI\nRzPHoHo8IXyhzKR0Q3nuoGFqXpK/41t+Wczjnvbiih+LgA9Orua+qdtKlnfT\n/DqXDe7PDwslk8IIceb0hCMShIkxTU3B2YsRzglyHgqtKbiwSU5qsI0tKpN7\nahOSerjkXJv8ff+KHGa3VKBo230AzQN1ErBdRwhXWAilhg+uiLG2pY1dXQE+\nf0XQFgZSjrMa4SSMVvhn24d2YJha/BnYI+Bhh2GBuraV1AYPzzj+XisgH7ft\nvjTJ\r\n=D4qD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^8.12.0 || >=9.7.0"}, "gitHead": "97ef87d5d30542041d69cb4942bc10f98fc7579c", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "12.4.0", "dependencies": {"is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^6.0.5", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^3.0.0", "strip-final-newline": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/execa_2.0.1_1561544009863_0.7631312059436064", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "execa", "version": "2.0.2", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@2.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "dist": {"shasum": "3af2650be2b719549dc011a53118ecff5e28d0a2", "tarball": "https://registry.npmjs.org/execa/-/execa-2.0.2.tgz", "fileCount": 12, "integrity": "sha512-CkFnhVuWj5stQUvRSeI+zAw0ME+Iprkew4HKSc491vOXLM+hKrDVn+QQoL2CIYy0CpvT0mY+MXlzPreNbuj/8A==", "signatures": [{"sig": "MEQCIE9sl4uQr+qPX8/tD2PDYsJoBsIu2JRSGc+mtKju2RTqAiBkq9Pn8v6yDF22X6cR6lVgqDhppV4AyTU7ereVardoJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48201, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdGd/eCRA9TVsSAnZWagAAXAUQAKP7eiP/GC9qFWPmWNBb\nbTCDkt+g/8m+NL84TMqpxe9iEK/qZmGw6WZ/YPrH6Q77VAmpTQfbJ449O52Y\nG2TbhdtRsP8CHIhwtpL7CdljgrmxoUWqLvMPthVSDmY48U/lgL7pmLKXSlgx\npq1zws7f8hOX78EGYmCms1VlHYWv2WNUuYplJiHdC6x7nPxPmBdvayE++seE\nW9NQ1fkJJwvfcrhZSEMbE9V0Q5avW4eAeXkp0trdUKPWfuNZr0w0qqGfn618\nn+zSIjkF+n6Cb8uQrmy33m9KkN6VrCuR9HJ8pxjLbZexSsderHHMeVifPpAi\nlAlcKVlSPgEpZ18kOS43B8HsdmfiSLU0j+BZpnud0rj8lQatwZzbz8Hv43PF\npdppKqAKBQhKDFXgYq3wy/6Hbl3Oy3Zq86pPwMmz6Co/mz8YvObR+AvZOKZP\nma0YxBBbINRgzGwVmcNZy5ZFiGbwt5Py7HBsBDFzhmj/Tkr2eN8TzF8hV6kP\na7gJH5OsFtLgssWHCZprNybOupKnOxYV3Tx0udIO0IsnK1BVTRnk0pWRUkuq\nNCMpVTWLjVSwJjfv0G8jR3WwPPIWTtPGixtV1X+a9GmpuYmaSGpLTo2vHtyT\nfHK2Jf+wt/YFghYvoBo4BqbvQLuO83azrS3NUizJ9VyfnkpZKKomU5E+cU3Y\nFJTM\r\n=E3BZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^8.12.0 || >=9.7.0"}, "gitHead": "8977752a1036a50223b6a572ae5d1a103853c158", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "6.9.2", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "12.5.0", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^6.0.5", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^3.0.0", "strip-final-newline": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/execa_2.0.2_1561976798094_0.39987737236232634", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "execa", "version": "2.0.3", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@2.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "dist": {"shasum": "4b84301b33042cfb622771e886ed0b10e5634642", "tarball": "https://registry.npmjs.org/execa/-/execa-2.0.3.tgz", "fileCount": 12, "integrity": "sha512-iM124nlyGSrXmuyZF1EMe83ESY2chIYVyDRZKgmcDynid2Q2v/+GuE7gNMl6Sy9Niwf4MC0DDxagOxeMPjuLsw==", "signatures": [{"sig": "MEUCIQCa0E+9FboqkHoR94Hp3UNauk2ZmTTck905u9ErYxatcAIgKXakXqw4W+xvtQD1oo9DqpAyC5AUBPtgiauhyak6Uu8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHwvpCRA9TVsSAnZWagAAmiEQAJa8bkwYfcJcMAS1JFbT\n7nXhQjoKnQpx9+z4NY10dVpyEAcR1DujgE+60w8yt9Vf+ionFO6QA08iyMXE\nTaeOAzcgT0lgN58sw8apxG2DniM3F0iRhe4lf5LUnATn7oBAAv6G7cNAKtvz\nv9ttFBPWXllJk6R6NR6uPsIikKzfppWxepkFZsYIsGbK1/tHVBv4oe9tH3JD\nQl4HqRQtK5Sd+5I9DxulpVm/CLgOvhUhhDWscz6znrhRx7f6cYrTzZNGsBqD\nIfVqLqxWg/cbCmloI5ufaqd7keujhMwEc9R64BOvu03HLofoIahPpaZTh90b\n3Kx9I5JuMcEsMeGQBtIOxUs4jRc+mpKojYWS1k4CHTOlaKgVWSi7WQ42iF/5\n/9h6sB+mNjnuNZNnzlmouYiJwzykJaqONRzjOu0dy+SpphtFywiSfuLJBfiw\n6IWLuLGZ2kbGlan3roC9UbpLQ2es+kTSVX/OdbcrgncGV6TThalW8wNe3mua\njI3IDYy3B6yu1x0Juo5wtZLlSYxb3jyxw5YY0dL5hOAgrzXD1YfBZ+/oyqs2\noo+ft4fTQMgtqFLqK6F0BgE1CkgmWgbJRKab/FT2YeggHGrtc5lAPFxhGzWA\n6QlWJp39HMUQhEOu0Zs/AuUU4ndQXJ2UltKyHFEpgQDAnHX5T3in8W66PTwF\nVzjU\r\n=xW4d\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^8.12.0 || >=9.7.0"}, "gitHead": "0cdc62cbdc9ad7879316363dce9b17ce1e6239c9", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "6.10.0", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^6.0.5", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^3.0.0", "strip-final-newline": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/execa_2.0.3_1562315752728_0.1958378699659593", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "execa", "version": "2.0.4", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@2.0.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "dist": {"shasum": "2f5cc589c81db316628627004ea4e37b93391d8e", "tarball": "https://registry.npmjs.org/execa/-/execa-2.0.4.tgz", "fileCount": 11, "integrity": "sha512-VcQfhuGD51vQUQtKIq2fjGDLDbL6N1DTQVpYzxZ7LPIXw3HqTuIz6uxRmpV1qf8i31LHf2kjiaGI+GdHwRgbnQ==", "signatures": [{"sig": "MEUCIQCutIAikPBE0hbVEN+pew02XAViYCPcYwJyPLLqYN02AQIgfHSSEOAhPMQdyQNKLWU7EjEDiNY+4rvhC85wH2T067o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48211, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVTTdCRA9TVsSAnZWagAA/6MP/1r1dCYF5tJKR4KOWS98\nVhLZa9tSQpqfzNbSMXNkxeezDBC33T0CFbaRN3blg/ZSMqMJyxvht7tDT8pO\nIBvC84WYenDWdh0f7HshyKtYVwjhcbHWVJTsgouJHcDUfmn97G4bgQM9WRzX\ny2I90r5M72/ismLareNH9Cm/ew3Z2C4oiZZz8yF4FJ31QOwGADZn1D5CrxMn\nkO7P02rWz5uzuN6LIovy77aqUfBDf9Lc0Skv/PhXVexfFeUIPzVye7XJeatv\nzBaddVyW0+2MfY7U9SOJqKtZYVECFjwqFBrdDYo3gbMRPAUcYZOuZ64opPRS\n+wAJw6U2uRVC3V4auSh5Z2YWTWZ10apn3ctB342UOoXzk7dYqFBo0JPdq5g1\n7ooAOSDoYnm8C/Jr0PCwWXDVWOtTfL7+lGMroG/ivoSZTbtGuXWwZ4AuvI+D\n7o0dHW+czNFuQub6t8uP3cjNuX2DeWzsms3d8iwT0cJa6KEEN43qclCXkvT1\n01SCJmyrvLgIff5OKG1gslJS92t/jobiTLktJAzQCpMk/7Xp7rBrcQyCNFit\nNUSaTmaBt9WlCJ4AW8M4CxtaVam9x5gxsUfUUm38aWq3G9cDPMn/caPkSrEc\nDQoE9aT18qKIGhb7gjPD+ylgRdtHgc20Kl3/UsZq766zqr2oj2K1ygRh52N2\nFJPt\r\n=xww6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^8.12.0 || >=9.7.0"}, "gitHead": "ac2bc15e1598317e4698eba5ba171927e4159655", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^6.0.5", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^3.0.0", "strip-final-newline": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/execa_2.0.4_1565865180934_0.5626446401512586", "host": "s3://npm-registry-packages"}}, "2.0.5": {"name": "execa", "version": "2.0.5", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@2.0.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "dist": {"shasum": "5be3e2ea7e61bd038da5a0e11dc6ab2097357f2f", "tarball": "https://registry.npmjs.org/execa/-/execa-2.0.5.tgz", "fileCount": 11, "integrity": "sha512-SwmwZZyJjflcqLSgllk4EQlMLst2p9muyzwNugKGFlpAz6rZ7M+s2nBR97GAq4Vzjwx2y9rcMcmqzojwN+xwNA==", "signatures": [{"sig": "MEUCIFSnDEq74EIntdfLZlJIx7JkjxcAghsxiIRGwAnws011AiEAvfoEVEHWJgW9CF7bDmBwEEvAj+ksSMliZnDpPP9D8vc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48198, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdlv1kCRA9TVsSAnZWagAA6WoP/Ruhb5Zm2Xt9DAUQC8mM\n2aOMw5rY1InHESuvwnASMOGlzqf6BQOQfQ2FqL/Yj7UVQvRbDrt5c7gZxLts\nfuqVVxHJfizwqkD8QR4NByj9Btvrhr7Gqxzhe/6bjsK4/c0xttv3bge2La+2\nVGOCbi7/5RNluvieHVW0+ivkaSVzIw6QLr8VazcLwOSx/DosnoXVuluHEBtY\nz/hxamrFsmSdxqyZKwjVMmHyme3xoI5U7l6eL6g0gpsTD4qo4ewupbeIVkcI\ngvzf7UOg8N6XIFWDa4frzf8efN44kVZvtJV+zgDcrWBr7sAbAFRJmMp/LrUK\n3d5jCGEjKEB1+MxACmA8iUnk8TYhKujWFaMhf2v7ii7I9ZFTamnQtMxsFryn\nhmcwfgM+xh8ZbmccSyXMzWJlDevBJy0sZUm8y+OGTLjf1hX/Rvz5ke4aNOSV\nqmawCmGcFwGnRYhUxwBiQy1fO80fa/tmu3y/jHWWty0sRNtuPERAiGywXEmC\nsgJGo2f2lEXCi7JlDFQTIyb/9Iihxhf8XmP2dc91cPJKk5fy8uh//tH/4XVA\nHQes4NOo0O9tFGkm8tUo++t3vpiieInl6BNFaCiX6UDLV8lVBGvvCgXpouG6\nisOyXsFYw7fDaTlUZ6LtxxHLze4/KfwQzMRIBiqG4x5sbNVuGFo5RtdZQHrd\nGtT0\r\n=TZDC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^8.12.0 || >=9.7.0"}, "gitHead": "603537aa05e933d0585645fbe4f94da3e20746d6", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "6.11.3", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "12.11.1", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^6.0.5", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^3.0.0", "strip-final-newline": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/execa_2.0.5_1570176355504_0.6711622418588583", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "execa", "version": "2.1.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@2.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "dist": {"shasum": "e5d3ecd837d2a60ec50f3da78fd39767747bbe99", "tarball": "https://registry.npmjs.org/execa/-/execa-2.1.0.tgz", "fileCount": 11, "integrity": "sha512-Y/URAVapfbYy2Xp/gb6A0E7iR8xeqOCXsuuaoMn7A5PzrXUK84E1gyiEfq0wQd/GHA6GsoHWwhNq8anb0mleIw==", "signatures": [{"sig": "MEYCIQDv26Ng8cRSPAvI1O2C6hUSwdEodtTMwy/ZbZ6IlIlukgIhAOSZ2nohGzYwcBB2aMnfkByiflC2941l6o9vuILq+onU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49164, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnaQSCRA9TVsSAnZWagAASL0P/02jQZx5VhDm7oPvuJyG\nOFhgQC0whEsBhWIcAUTEBvzsXEka649Xi86B6SdnbHmIQjMoCGJ9ArpBSVG2\n46PaLLtmUCO+gZLYmS/Lb3AA1VEa8XbdYdtCdPuOAAu27lAtxcmd2H9dK2Ro\ntPSFcq/6hrWGkzips9riXKpHD4YBbUCkgVz8NHx7CQMHv9YWxI9rfQllAhIe\ngxdsbTJ7sV39ZaWvz8B3wcNRgHsZt97xgAbcg4kij1pu+1ywLJl/VX86RIj9\nT0S3b+/uZZBjJDQhWrmrGNZdvKjtAScv15QYTKXDoHuMMu45HLB3GRHgLn86\ngsDEXDLa/1jWZXgoOQrppugdBI44IUTswwdcDfdC9i11v3BTbjzoeL/HQ7aN\nj+/CwRxJ9okDidN9OsJiMlOpO9X/YL7xLq6BfVUekcXmmrWOUZgFu1UIZ9LS\nnCSF7gpysKci2ZTSDWO/rdkWG19EUqKsL6iwuoT/s6AeLNSyi46aq7icyeDK\n84/X3gTU6c2tI9WCkXtJEC538Aa0qR1H89rQaTuA2mTP8Ho9fmWvWn2HPVJc\np23BYEobG1T1XO7LHRjecJQ7jZ4l1H/8aLRNmmFe+IBKdMFEADOcD3+pk32y\nhj64Q/OK8T0MuVnXZWazUwDuko48Xl5VCikNEBqthJ7GF4E9sPhW8ZnHpx4a\nP3jJ\r\n=nKfP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^8.12.0 || >=9.7.0"}, "gitHead": "e98561a71df16695f56700d7be406ec8fe41f0a3", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "12.11.1", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^7.0.0", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^3.0.0", "strip-final-newline": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/execa_2.1.0_1570612241889_0.2434126485284398", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "execa", "version": "3.0.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@3.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "dist": {"shasum": "f38e3c24932c2ee0f472383b0dcbd40aa42f61f5", "tarball": "https://registry.npmjs.org/execa/-/execa-3.0.0.tgz", "fileCount": 11, "integrity": "sha512-U3sotWWSGb19r7wVY3OcUgS/S/7VXbh3DQkYfPHaEKveYhgb+GqNN0YTkpnaeI7Ho2oIQ0Na6Mt98M93/wtqGw==", "signatures": [{"sig": "MEQCIFi6Av6fipcl7X9Kxnh2gayXHytrtoUJpJPp9EkipYdNAiBGmBfgPWIqvPpo9axYWANJtCpiHDcJo6VjTs6X/9lOSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49637, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdpIFRCRA9TVsSAnZWagAA8FcP/jd5RZ5TmbSC++FXpAw1\nLhkB29EG/swloWgkuJppTdYzEapz0TEYRescuThVojHY92sU4PEcyrA1TuYw\nNhxgHZu3fR/lb/5TFLLHWQKTAuHfTo3PZnHg+p97gPFK9l8xYMTthLHz/+3v\ngertpuCbY2jlZHLnesEvft2leUV9PYBzrdN27ZcUWnxBffLM4yYrGMp8La0j\n7seYeDzm6VYsOeAgM9EK7L4tr8A2hfwWidDy6bAUON2N7DPMl1i1Ltt0LlE1\nM+fxhULjgYJDrYzAnvE3Ak2YCAYyc/u20Oi5kzmjo1XiZLe5W0iJBp/o8wLQ\nfu1NTtKKSDQvtc4IMrWXKaMxuDaor0Bfa7b7+0Duyo0rFbz36LoB12po6M0p\nl+oPepubdnaeQFRG4C8ViACGIN1LXAwoBuK/Feu5KZmQ1KnE6u0TW43fG3xt\nSj2MhcQXSaEWsViCYQ1HFQ430AoyaR3DjD3z/0vj8oBmRAuerAGtcOHQn9Xm\nufcUoie+QeaI2MwdjmpkL/JZPgAh6QbDzHkILtRGEqYxeqm51NCw/CHrl3M7\n+X29TC+RTxsob8ERFKUqPrIEmov3bz1DJycKomP7Vy6pN1erPpWwJX2APcmt\njYP28U7MFL99+Piy9fPG1naX2VgfqzOSsTKU1ZWBUIV4koUlosshvQghlT31\nncSf\r\n=BWpf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^8.12.0 || >=9.7.0"}, "gitHead": "88dbeeeb5fca2c92b59ffebcb30f09db924f39df", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^7.0.0", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "strip-final-newline": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/execa_3.0.0_1571062096154_0.8706696268911771", "host": "s3://npm-registry-packages"}}, "3.1.0": {"name": "execa", "version": "3.1.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@3.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "dist": {"shasum": "d7a4a54b1ef3784c3d528c2bb7107f5720356929", "tarball": "https://registry.npmjs.org/execa/-/execa-3.1.0.tgz", "fileCount": 11, "integrity": "sha512-KcBxdjv1JlRiHMIRSDtvaGlUb6SQ4TLqxG9blJNTo6bzYYZZBHBZPKqMmK5Eftok7wl1iwDIRofxdu8tBlidQA==", "signatures": [{"sig": "MEQCIG7LMkge9PVGyqq2YYI886DwsPXrlfm8NcbMbG514IzEAiBfYzHHwL3RvkneSWSFmhbWfhzkKClVk3VoSYM3tq0WXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50553, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdphFQCRA9TVsSAnZWagAA9wIP/1W8RLdSU53HISnp9sqa\nXF6lUqHokkHESRBomDTaN9bAa9ys1yx5Ukq4AOuwu5G/3wvmKe4EjFumIAHz\n55fC/n/v35RcxhDHnWK4aOTMVmaJw7NUuSl7UaO2+2RyAY1Fma1TSMLTKpqH\nw5xO7kpGQp3/VUctCNKYnzehJn/9mY1G1uLsygaRBuEVYXkJYmX8BvIvAZdy\nJjsgL3fn3ArdG9dUpyKiyI7ydJax5qcBhKyV3JNraNHLIW4PG9wkrxpO2d+Y\nOJJ8BWVUCmX/BF4QapgzI/yG9uxOzYwHh/PioMIwIkY7DKGrnVnRrVNVzIbb\nzIXJXkQud/lNC5dhZa9etUI52Ohhauths3vMF6t6G3Rpp/yfgQ7fOrtiK+ql\nr9lMGzok+wUji3q6DHhtt3hbdRWHpwwYoCtzLOXR/yQP4KfxP4IRMeFzF6gm\n0aJJs9KZB6aKhrfxINakaOsSCh44Ci2xxC0irOQzbRSdaaH+iyTMJsFu/SM7\nRMuAYYVG1+2vT8UuOCTim5KB05ck6SXPfeWGgpRDz/QGPOq/8pVKbRjzKzgc\nlPhqDNnPcMoXpMlW8zGzTCkL58MVQyNxs61DH9Ub5zBhjHgjShQYx3TmjB9s\nwWwNLaLEddF7jiN5vozHQMaRCTnvkCz8Z3leUH1IexS2VUIJ0KyLp2/MKlsI\nD+DA\r\n=3056\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^8.12.0 || >=9.7.0"}, "gitHead": "cf32d160bc5838193b7814313098bf74615acc3e", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^7.0.0", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "strip-final-newline": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "get-node": "^5.0.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/execa_3.1.0_1571164495156_0.8650414182624948", "host": "s3://npm-registry-packages"}}, "3.2.0": {"name": "execa", "version": "3.2.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@3.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "dist": {"shasum": "18326b79c7ab7fbd6610fd900c1b9e95fa48f90a", "tarball": "https://registry.npmjs.org/execa/-/execa-3.2.0.tgz", "fileCount": 11, "integrity": "sha512-kJJfVbI/lZE1PZYDI5VPxp8zXPO9rtxOkhpZ0jMKha56AI9y2gGVC6bkukStQf0ka5Rh15BA5m7cCCH4jmHqkw==", "signatures": [{"sig": "MEUCICy4yPlkO2Ngdgb+NucxYFQs/7KYVXIDl+w4NYQEi5xsAiEAx6arcg3MazIN4XKoDVrf4OeSIeLCDQaRZaRRvBiyjzk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdqIS4CRA9TVsSAnZWagAAbN8QAKKrF1lkitIWmzp/qLOZ\nctI5Y6obSZkUuGSgO8BntnD7vhtwxdi8FI0dkJnhFqDst2qp+JXQDL9T03qC\noIWMxEqoxH4TSth1nCFRPT7zGa7z/csuWbhS82M5q9zs4Z2MYSa+jZ5TXUgI\n+9ssIlvmmLyk2GGiJnGoxmxURO+5ASstXQ75WAaDl+dJr0lFsfs9WfVCV4yS\nWW9w2JEXalcPk4geB0Sm3eTxU08hAd9GXEhXES9QhRqJRLp7kWFmazRB3yUn\nGgtBmYMsgNWRC7Nqdwk9kgiha98MtYMpmKaVbwJgiB4B9xHWN4qs+p96ZaHn\n8bI2+q+/uS2AXwq7LMiDSHisWj7DxJKonqGFdaH0S04ST3cMaNwvNsHqmWTx\n2rvWNvP5Ds/HVIlg30E16LXfKL+CrO5XWRbsSF2SP0Jj4FQrttMCiVoiXH7X\nFSENxApwome+9YTTsymyKhWrGPYzObeRymD4L5oFHl3ar/xRcjrOdOu4/ttO\nryCLGhzcCFDWpsy4JIb9GBg2tE2gEbWYXlDEZAC2tQmnehSx3AU96h1bTyQo\nMF8qejuphMKIuhfS7nsfCRP/MSgm7jGYniOZRnDRXe0pe44XfLaXVZE00YNK\n+UzRRKcEev3HNAL3s67mE9GTtfuLHNXF60VmC+fJt0Hx9yPZBP1B/GoBAFte\n3jBN\r\n=NMHO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^8.12.0 || >=9.7.0"}, "gitHead": "e003f9cde906d6095e176042147c31ececc02881", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "6.12.0", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "12.12.0", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^7.0.0", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "human-signals": "^1.1.1", "strip-final-newline": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "get-node": "^5.0.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/execa_3.2.0_1571325111773_0.23596713465151553", "host": "s3://npm-registry-packages"}}, "3.3.0": {"name": "execa", "version": "3.3.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@3.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "dist": {"shasum": "7e348eef129a1937f21ecbbd53390942653522c1", "tarball": "https://registry.npmjs.org/execa/-/execa-3.3.0.tgz", "fileCount": 11, "integrity": "sha512-j5Vit5WZR/cbHlqU97+qcnw9WHRCIL4V1SVe75VcHcD1JRBdt8fv0zw89b7CQHQdUHTt2VjuhcF5ibAgVOxqpg==", "signatures": [{"sig": "MEUCIClPZ/CuFoESVdQF2RXLiGH1t0pxloWLhyU6GlTkBQpVAiEA4+O60rw11z4oG6VMeXwETGlncMywuVmIGBWITsSz6+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52934, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdyoR2CRA9TVsSAnZWagAA8aQP/2xhVZOuxFLZpNM2PqFy\nDyxLqVh7MLfg55vN2a02iVQvalaC2O/XG9jRgsZYHLBnB9F7fIWV0xu3uh7w\nXJjK/X/LA6khMCGfuIBWXVE02EoMZgVrnh/NiqHCOz0CZWznvP0PhwYE6ntP\n9/KAaiLLa0b0MqwNztXHS5FPQFtmqC4yO+i3HLPKRT609PNMOthpPkBGcl0F\n4sJQTB9/fP90QX//vZ0H49Ql8+MHqmeMK8csb+G2m64LvzK/klXR3QsCXIMB\n+Q57lWke4OPRym0Zz8BT32psSSKWZ+l5fsfhELKLhzI+sgHaX/RmjmgQkp2x\n+gLNUF0W1QO7DiZB+dacLKarHGYzKQ4eA/xHStgM0qqIdAlXjylqIG0bgjuf\nv15zM2dDfUfAJNlbi/sJgf33oj6wlgJcUE4qtOC10W3sPiQgsYYQyO66rhW+\nM4aAPyvPxVA1PiV+a9U4SqsXU0sxmJi44Lflw5QWbFFFCxuViVLSsMlhfUpc\nmJdFaLxXfVpjnShLwEpn7syKToUvhovVRKPjA9bQXbFP3T7afXQbQbx4mBfi\nBePoVzG/9ue2xK1HVpOgLAk0TzFsFJ179mT1ULsxDpD/ltMCLeUK/EhWc2fx\nhAwdmp6LqhqV6uFrjHYd/zqMH+76oU3Wg+/xLeuRTogtBJVo5y6cpKraTnAQ\nvtUJ\r\n=Vcl9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^8.12.0 || >=9.7.0"}, "gitHead": "f1eda9c86b93d38978c939083983424149005bdf", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "6.13.0", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^7.0.0", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "human-signals": "^1.1.1", "strip-final-newline": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "get-node": "^5.0.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/execa_3.3.0_1573553270019_0.6605028401759876", "host": "s3://npm-registry-packages"}}, "3.4.0": {"name": "execa", "version": "3.4.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@3.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "dist": {"shasum": "c08ed4550ef65d858fac269ffc8572446f37eb89", "tarball": "https://registry.npmjs.org/execa/-/execa-3.4.0.tgz", "fileCount": 11, "integrity": "sha512-r9vdGQk4bmCuK1yKQu1KTwcT2zwfWdbdaXfCtAh+5nU/4fSX+JAb7vZGvI5naJrQlvONrEB20jeruESI69530g==", "signatures": [{"sig": "MEQCIE6ehcqhBg7BSUzrtYX0s4/IsvUqdbJI/jTQJ0MjbHFNAiBPCdbHchXCH4S4mjivcx27AudeVzfdhv3T/qJJNorw/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53960, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd26qrCRA9TVsSAnZWagAA9rUP/iesTqJ/YZ8RATHdbV8J\neugZ8+uVvcdEdC0Ch0R9YK/dNDhQjgCnd2gVXl/6eQ6yqrmDs6gHZq0yh0Az\n/ULbMpSVX846NMIDLzbiNB0hkTg0k0owmDwg/6VNpNehOqDOFoixYITT9Y5q\nB6lMhXKpbO0snAgw1lwUrPH+tZShGxdvuzlY5//XVBTVHUrWOiaC95avJ3xq\nQiVQ6OenGTwGbb+AUC8w/itFfILUZ1QiWLc5+ychKoBjvAlj0fngGwHUa5Lt\nTIwxOzkPn+5ifLKuMjTtwXODAODkIMVsCBi0A61BUdPulzTHQcHGqOyptkBV\nyld7kgGX8cYlOJSnW2HdlMHsdhyUYDgQayUTAL+QJys74rr1iq5OIcymXB2O\nOMvHh6+lzs1PCJzRJHbDT0eDuAPp5PflQ1df2fhxgNEBmCkc0ro5HFQ/aEa+\nMR6xV47ICFs/9MTbyLUBTarwEpOCILfQHFnKcEf7LMNIEqFUekDZko33DTI+\nX7WU5/+J4eLkebl0jefdOIxJEn2hluw/MWb6No5/TZcGZakxmHyQMa1OLaGg\nZzGEeyPeYzQfHa+fXeM2BOn3k5ww0irsF3gL64jwoq3C2mKZgYwBu9LzvPQB\nMmTbPb+nWm2mlzXunK/wtbghIr0Uj3vc9JcJmSm1hj0SxkGC88NzogaWHRLf\nlhud\r\n=1Rj9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^8.12.0 || >=9.7.0"}, "gitHead": "d50146bd4260a09e1dc4a54f02b05714678fc4a2", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "6.13.1", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "13.2.0", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "p-finally": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^7.0.0", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "human-signals": "^1.1.1", "strip-final-newline": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.24.0", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.7.3", "p-event": "^4.1.0", "get-node": "^5.0.0", "tempfile": "^3.0.0", "coveralls": "^3.0.4", "is-running": "^2.1.0", "@types/node": "^12.0.7"}, "_npmOperationalInternal": {"tmp": "tmp/execa_3.4.0_1574677163393_0.009077939891026343", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "execa", "version": "4.0.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@4.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "dist": {"shasum": "7f37d6ec17f09e6b8fc53288611695b6d12b9daf", "tarball": "https://registry.npmjs.org/execa/-/execa-4.0.0.tgz", "fileCount": 11, "integrity": "sha512-JbDUxwV3BoT5ZVXQrSVbAiaXhXUkIwvbhPIwZ0N13kX+5yCzOhUNdocxB/UQRuYOHRYYwAxKYwJYc0T4D12pDA==", "signatures": [{"sig": "MEQCIBW7t869WqX+12Jxofv7a276sYwAfPF+TFK8eI7DVhhpAiApH9KhIbxkgdBSa/OBzOjaECZdFDeDASAcOMFEjNVfZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55369, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+0e6CRA9TVsSAnZWagAAIz4P/0UOdh7Dhh5LNU/9xpxy\nIAmsSqqLKHVrMgCXbIQECc5UoZbEM0kOnqK2+sHJC/tTlCOC/cs6POq/85Rf\nZJXySK5Rro5w+EZIklaMQQjfNcc2GbsdRpDbTC1XWj0sNKk3fSHdX4UL4W2+\nrQXlL5MpnOaaXKsQ94e16a0VTq4CfhGC9TH9dUv4jlO12sciA+1sqjInewQ7\nZve6BsGlo3jnY0/dzst4oCOya3qKQdY0dAwhzJ/C+vQa8Aa5ibNCNBExQCCq\nu1V2cDU6OeR/Wmrd/84dBo3JuYxzbo/2gNTnXCpvKEM75J997DxK2iaEe59m\nv0mmkQnE8gc64gn+77rVhm+rikvNBcqG0EeEGhDLFuW0Wmd9S+ImILT6biu7\nXzOfu0rJ45/YmWP9R/WMjt3LvJO5XtgRO/iwK40O/0ITAQkoQIlQhLKtB6F+\nDjNIHP5lfwJG9krAkbPrteFcS6X/FpORDQdV+2JMb/WutstRLwvaTmZ7JBpp\nOq9bcXwLxbbcImQunnzlBInN48bY9R1ZlnD8orrdkaxB2Dd9WaAUXKUMHtmS\ndnV8Z36ahQzbW/R5O0D1qkHcmdEx85ul7Wf6n6aET/ZuqE2Rh7iiMX22xz61\nkf+7QLTki7xa6piV0iFNddRJ+9RB28CY+jt9IUPHYNBagNyUL0gvUymC8uPc\n7p6r\r\n=5H5j\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "****************************************", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "6.13.4", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "13.3.0", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^7.0.0", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "human-signals": "^1.1.1", "strip-final-newline": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.11.0", "p-event": "^4.1.0", "get-node": "^6.6.0", "tempfile": "^3.0.0", "coveralls": "^3.0.9", "is-running": "^2.1.0", "@types/node": "^12.12.18"}, "_npmOperationalInternal": {"tmp": "tmp/execa_4.0.0_1576748985916_0.9683761819540304", "host": "s3://npm-registry-packages"}}, "4.0.1": {"name": "execa", "version": "4.0.1", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@4.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "dist": {"shasum": "988488781f1f0238cd156f7aaede11c3e853b4c1", "tarball": "https://registry.npmjs.org/execa/-/execa-4.0.1.tgz", "fileCount": 11, "integrity": "sha512-SCjM/zlBdOK8Q5TIjOn6iEHZaPHFsMoTxXQ2nvUvtPnuohz3H2dIozSg+etNR98dGoYUp2ENSKLL/XaMmbxVgw==", "signatures": [{"sig": "MEUCIE2py7Col3ERnTN1bA5JWVE+ktdJCBA2Z09VM+lqbSFOAiEA9BgXu4UP96ZVRRRVR1qHnAOsqH9jL61IRTZKjbLEqhg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetWp/CRA9TVsSAnZWagAAm3AP/RXcr+oiTzm3HM5KvDhF\nJ3UeoIga0KLZQmyXPk2o6+/qLDkGin56fVfSIVjS9GZwr4ciksxyJ2ODsyOo\nsEkO6pP2DgyzAILiQ6bFL/c11mL9i7IMkP8U+0hQAZUuCWTNK3pcBVAKUVLe\nvViKyhbGZ0cr9NYCxPpxSlgJuFt0JvVZXadaDlyHSvZihQ7aTcoDF69x3Kde\nk5+spvLavSMYX1BHlJ9uRaJXbc2xl4fOw26ec+UVZF/kP0n6YKcQTUwudnHl\nN5x96N/R/JdIwkFCklILJ5YlfDN8kW+G9Ro7RBcXVK8qdaPazTzusFBySi6L\ng7D50NPVZXh+swqAMB4jAKwiC/Fc2cOTQQK2jJD1xTOoUVwaEpaFHW7+ZM1m\nlmCtNkQ9+5pVdDx7hWSXPvSXjHi/N7Ml2TnceS0R2yZ/aPdwI40su2pEoeom\nVT9lWOrPzdYgOU/RE2tCtXWtnHstfh1jvJtiSg9ZVmDxmF2yqldwBkwiCKbi\nqYbE1zavR53Bp2CCwKaNV/BNxEN3WbICn9lMPUkmn73NWehZZvcN3q9ndzoi\nqa3Xv5wXZEJh8Gy4BGOqm+s/IF+Hzd/XUchD4kFhSikzwkFggeFQkLUqsiMd\nbZLN2r6U9473aa5Lwf4KDY6I3Oi09LMvkCvni1TyerVMvBKH/S+fxMxqX4oi\n8f5w\r\n=sY1I\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "e22252608647c72ced33190f1edee29375c30e57", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "6.14.4", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "14.0.0", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^7.0.0", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "human-signals": "^1.1.1", "strip-final-newline": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.11.0", "p-event": "^4.1.0", "get-node": "^6.6.0", "tempfile": "^3.0.0", "coveralls": "^3.0.9", "is-running": "^2.1.0", "@types/node": "^12.12.18"}, "_npmOperationalInternal": {"tmp": "tmp/execa_4.0.1_1588947582702_0.4788909774300627", "host": "s3://npm-registry-packages"}}, "4.0.2": {"name": "execa", "version": "4.0.2", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@4.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "dist": {"shasum": "ad87fb7b2d9d564f70d2b62d511bee41d5cbb240", "tarball": "https://registry.npmjs.org/execa/-/execa-4.0.2.tgz", "fileCount": 11, "integrity": "sha512-QI2zLa6CjGWdiQsmSkZoGtDx2N+cQIGb3yNolGTdjSQzydzLgYYf8LRuagp7S7fPimjcrzUDSUFd/MgzELMi4Q==", "signatures": [{"sig": "MEUCIBIENiesA1VccWYy4lvkC0AuTyw2Skw0ObaK2RcJQn7OAiEA0NcSY+6pP66WKRots7t0cMbRYY3opWiNRiFyprJOQuQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55554, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJex9zlCRA9TVsSAnZWagAAV7gP/2k0BefUStZ1hRfyHXnY\n7UhjUGPqDWy+jSPDr2F+6pQmPRzmDOJd/6lMgMQQY1Ah3T3sXkrCZSwyYBkb\niH3D9ezvQlQndha1/wRMW5MYiVvwL8pvtpT9ZjDVsfhJt3xwkCxQ3ehZWnSZ\nEY9LQ7AN8BwokEQQtUc3q7vzFkIVKQGY8fB3D1UGvRG4EkzOiWYeHWti6Jnz\nh+sAni2+/tmCLpGGSIyPlFfBJdihFII5qzYfMBJe/ahsyJojDgL34M1GtTIw\np6wAyjLGRzGsjwZOJUG994+enb07fouxH0kPFBIU/+0DLo3NDPMDb+9Ei6XT\nxIgei8AlwLuPRZNhyLkR6Y8UuSuVaPXNVXUVZUI1bj34lbCJZaQlEGsE6pni\nCAIPTNfPVMloQFXcL8y2lWz36ioAwvHqLEyKRNP1YnFqiPNZGgPfIJDXaHPi\nIucoDnRkEvlziIpxnWcAcc7s0GYrhEYuSj+04PMNwlBIwgQh40ClpzlpZ/Br\nV53O7llwi8u1hgCTxzsnTFbYlLrbMR0z9gsjosfkfD+AEYKcG8S8JXaHdvBc\n1JVP9ZNK2yUBO0ZaO12HOziJpJ5Rmk7Ps9D706KIVqSGhbK5+DByD/DKusBJ\nNA3F8KH8E0wRlaYzulmhPAorC4GRr5ubsMKc5nvZjYJVkwVjFpOoUBA0NLuk\nxDYh\r\n=iTTF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "a827d82203a1440e585276bef5d399a5953801f1", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "14.2.0", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^7.0.0", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "human-signals": "^1.1.1", "strip-final-newline": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.11.0", "p-event": "^4.1.0", "get-node": "^6.6.0", "tempfile": "^3.0.0", "coveralls": "^3.0.9", "is-running": "^2.1.0", "@types/node": "^12.12.18"}, "_npmOperationalInternal": {"tmp": "tmp/execa_4.0.2_1590156516666_0.23777898193810376", "host": "s3://npm-registry-packages"}}, "4.0.3": {"name": "execa", "version": "4.0.3", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@4.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "dist": {"shasum": "0a34dabbad6d66100bd6f2c576c8669403f317f2", "tarball": "https://registry.npmjs.org/execa/-/execa-4.0.3.tgz", "fileCount": 11, "integrity": "sha512-WFDXGHckXPWZX19t1kCsXzOpqX9LWYNqn4C+HqZlk/V0imTkzJZqf87ZBhvpHaftERYknpk0fjSylnXVlVgI0A==", "signatures": [{"sig": "MEUCIQDIB8WtLwPnyK24B7Vm9IieCYCx5cH3r/k0Yb+84hE3FQIgTUeA5ebLSe09Vtqu19Z8mFhSAFX9DyBma/iRZ/peSGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBL+3CRA9TVsSAnZWagAAgCwP/R5Y6XRBOF143ZOJMkLE\nqLwjdm/hKqLlPNNLQ/ITuxk38b7m8BnugBNaetymk5lwWNDv4JJvQjSlptym\nWZT19WIherYlK7ZrZ/0qTvWkcrYpNLDioKXhJbYX/Lv/R6aGX7cl6ngWZWXF\n9mHJy8JDUKVhfHd8+1u3eTYr4gjKHAzacGU5IVMyy+yYA41vCVy/OhAcK7wH\n+LElkgTwyhGooTt3uMNlzefZ+K6M6icJVDtYPaNc+pP9yimChFMbPI6jtYSV\nKKDep6YWLtNS7Is9/cytMLMWVOl8473kaQBYQNXAcw6eSiFX3zfyxaxRhPpR\nFkhYWqnRpPhAEPi4yahlgQZuRLq8qNMe6t50MeERJTniKhF+M/WHenhNHpL4\nj5ukuv3jSDfMYCsOwcZaIRz5d3uqYLnCtciWSXZidEsWjqNwhH3uCAXuGKFZ\n4xA3t0vhT3cEmkS1i6n1B+lEZ+w5XAEFE7zfCfUKtHHAtDUUtGRxatMVfTvt\nTRoTbw2Pku+Po+GUqwF/vF86ClxHlgJE0OphYJpHJEIAmYA2WRKOsabXCRne\n7g24ZJxAAiOVEVHpUzXsHEWGjzdlhLo4iNJsctvlMuL0bJZfMv5bMpyU/8cy\nfQbi/EoYqKfKidTQPJysePOSZ+IeTU3PpmjP1XOC0R5/ozkS80aIff53NPJo\n/FPa\r\n=IppU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "26d6b0d59588794173882f78609844ea71944463", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "6.14.5", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^7.0.0", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "human-signals": "^1.1.1", "strip-final-newline": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.11.0", "p-event": "^4.1.0", "get-node": "^6.6.0", "tempfile": "^3.0.0", "coveralls": "^3.0.9", "is-running": "^2.1.0", "@types/node": "^12.12.18"}, "_npmOperationalInternal": {"tmp": "tmp/execa_4.0.3_1594146742981_0.3031261940122554", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "execa", "version": "4.1.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@4.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "dist": {"shasum": "4e5491ad1572f2f17a77d388c6c857135b22847a", "tarball": "https://registry.npmjs.org/execa/-/execa-4.1.0.tgz", "fileCount": 11, "integrity": "sha512-j5W0//W7f8UxAn8hXVnwG8tLwdiUy4FJLcSupCg6maBYZDpyBvTApK7KyuI4bKj8KOh1r2YH+6ucuYtJv1bTZA==", "signatures": [{"sig": "MEQCIEGa8ijl8B2HUnf51F9sliUETmYjthqkWLZVA5Awa/ywAiAh+l4tfTwoPoAQv5IQP6zW4HNJzBhKmBShbr1alxgKag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56040, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfmVZsCRA9TVsSAnZWagAAK/UQAJp35uVM0BD9V9c3xsHY\ngoq9gw2XDAJWsil24n8m08Ox8QxnVjnTjwcPFTXRHbcQ5cbn5quUsH0HyAvN\nxFWMn53Oh6ADkOx1DHBfwGjA7m7r23KjNiY4rhMgd/6H5S+/QE/vuF48BYsp\n19zHyI2uKkFWoVPw5DyWmJ2WP6OXcITaaYmoCiR/UiXx4y7/3G2oXICQWRhd\nK5jIcCGg7E8XMEvP0PNTAQSo2nY23sdor+e/AJCVkugEmjUyUztPmU0tH4ug\n8lvhC3baJjwYvRbJDvMXn0/OGiTYd7ELZ7nR7bSf/9Aa+5A7rUtE1BPlDIkA\ne9CzOQkAGo2DXd9JGM68kcNQnBUbpdlhvhqgFHSem0hNEoV29ZunH7XouA2B\nn3L5O6eC+5hj3Zvllj9J4sLifwCqr6vzbNlsZQexVbjPb3xU/tRQQaA+Igpg\n2BctAuCLoILSMzCos82LPTXVJsOgS/86YRAVMNsvBDbbF7YOdFDr0wHp6dph\nhErYI/GRcLOdbnjxdN2sg/R8ZOoNrHSB0Yr02wrX0v85lyc4hsejCUnfDIcH\nRrQtceM/fvLgSONPyDvEh0kVbn5xIE9QvwLXvpce4xls2DFt0CBKKka5Kpba\n+BKMUEMG9ov5LWg8XLgtBDAI6u3Fb1L0YPfZ/h6s85H2ezE9oJCKP/8Hgz7e\nSpdr\r\n=T/dZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "cae090f4eff220db9447de88aa05a8cfb3f89bb0", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "6.14.8", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "14.11.0", "dependencies": {"onetime": "^5.1.0", "is-stream": "^2.0.0", "get-stream": "^5.0.0", "cross-spawn": "^7.0.0", "signal-exit": "^3.0.2", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.0", "human-signals": "^1.1.1", "strip-final-newline": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.25.3", "ava": "^2.1.0", "nyc": "^14.1.1", "tsd": "^0.11.0", "p-event": "^4.1.0", "get-node": "^6.6.0", "tempfile": "^3.0.0", "coveralls": "^3.0.9", "is-running": "^2.1.0", "@types/node": "^12.12.18"}, "_npmOperationalInternal": {"tmp": "tmp/execa_4.1.0_1603884652223_0.43876507436388446", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "execa", "version": "5.0.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@5.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "dist": {"shasum": "4029b0007998a841fbd1032e5f4de86a3c1e3376", "tarball": "https://registry.npmjs.org/execa/-/execa-5.0.0.tgz", "fileCount": 11, "integrity": "sha512-ov6w/2LCiuyO4RLYGdpFGjkcs0wMTgGE8PrkTHikeUy5iJekXyPIKUjifk5CsE0pt7sMCrMZ3YNqoCj6idQOnQ==", "signatures": [{"sig": "MEUCIEXWMpYxrimvrLvM6fC5AXe8GAnSDbAUQA5KWsuLQkdqAiEAuU2lzGUGK3z8tWqeDvdjTzKs3A0DvxFBB+oURBzf03Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55471, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfyHV3CRA9TVsSAnZWagAA02QP/jwfskNDXh0ws36liVSJ\no7MF8kUEaFlbGazuTGZVkqr8D/5nMSf4SyMFS8i7RkcoXh6ektT+LKiJ21xD\n8lhUW0VHZfvGeIhTgAwFMQzKEJzaYxCsI4Rfmvducnv/JDGga9mG+ggsiTqV\nW6ysu06A2OI6kKkBk9WdT768qpCIEnFdRskGChNsssrz77nhdoJNZXpgaUUJ\nui8gzKPCY2T4t+2J+4bLjdy5S/eyg7xRfVbm3oaE04PGxpSIcvhqA07gxNYx\n1xmyVDuG5Rzc4OaNe4sy221IrYugO32fMpKPgYKcW85WwHsAOkAIZw8dvO4Z\nT7XfSpygAqPIaudRoZSY/vgkwGv7XK04YZqOPaKnvij4QU4DrmGhKk6hh/k8\njvAlME/ARw3xwbsSMyP7PkcJIbMrV2BB8IyTmwMHWiqWpMcjJWG0OQ4XLqvT\nni2lNU+Y8cmw7uLgzOphb6fo43ARCaAw+VN/oY8Rkfhp96AfeFE0EgTHlPth\ncTHyhCbJwj+o6y8cWRcC+MzP67fFNqiadEUqd+UMwaqPc1gorTRiBLntJwnW\ngV1l9iXW2QczP5irjXdmHgoPVTRi96o/+sDkETxTFnNOr5Mq+jnQxsyoEopk\nSe86wmUgddZ0yMVX4IAlD2Iu5Ie98MEfdr3znM8pt8ifxwTDCp4QUDq18u/0\n192v\r\n=uG3d\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "f0c1785df19ca2bf82c339f2d02bc8645070fcf2", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "6.14.9", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "10.22.1", "dependencies": {"onetime": "^5.1.2", "is-stream": "^2.0.0", "get-stream": "^6.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^3.0.3", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "human-signals": "^2.1.0", "strip-final-newline": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.35.0", "ava": "^2.4.0", "nyc": "^15.1.0", "tsd": "^0.13.1", "p-event": "^4.2.0", "get-node": "^11.0.1", "tempfile": "^3.0.0", "is-running": "^2.1.0", "@types/node": "^14.14.10"}, "_npmOperationalInternal": {"tmp": "tmp/execa_5.0.0_1606972790999_0.29844986618977276", "host": "s3://npm-registry-packages"}}, "5.0.1": {"name": "execa", "version": "5.0.1", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@5.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "dist": {"shasum": "aee63b871c9b2cb56bc9addcd3c70a785c6bf0d1", "tarball": "https://registry.npmjs.org/execa/-/execa-5.0.1.tgz", "fileCount": 11, "integrity": "sha512-4hFTjFbFzQa3aCLobpbPJR/U+VoL1wdV5ozOWjeet0AWDeYr9UFGM1eUFWHX+VtOWFq4p0xXUXfW1YxUaP4fpw==", "signatures": [{"sig": "MEUCIQDGnVRJfhXPDhXhr/0QXXz/3ukSyPCrjn0xI1rIoQdDhAIgcntS8Nz1/exYYQsGE8p/5FOLtus2SG2Q+ciglEZf5r8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgs88FCRA9TVsSAnZWagAAmb8P/AmRKws75AE9bQ48qhy/\nEJh0PovPPw7IpFczbPUPJkJpOhpOXPPNdda1jfP6xpYpKgnFrwFFhJhf32Bh\ntTvET2hOAMC5WdDoGi6Im/GTLDKeobFvMVK3BByUe7AGlVUu6gCMvUdMD2L5\n6iVJjMY2BEfI+gofR2nm5l1v1/L2OiEKu8MNVIvn8tMpeEG2HJYsZlVZtQ4q\nXFRiRYHyADXgTIomH7hs44J7IWbxsWivY4cYIXANy2ATeX5rHgwSBcq+7Xr7\nV7H0HofYVnvLEGEgfRr6bMPwT7jdcPQ2z2RM3DpCojDxeR/07+Jsg2yr60hw\nIsy8BMNFstXffJSFGM2xHAlKBknVA/RjoLyXmhoaUx5igtIZd9fNgduXgd+g\n+Mz0qcYm0ayhQiANjoexVK+mI34IeSUIHuu9W/Foq+O5zdAr9xSFcQNqn3xh\nqLnRNktuhc3eJpPHiHIw4hZq7iC5tIo+O3rbORJgDCmUdD1z7ap5JX5gxXeL\n5f1u79UfJz41ZD7pMPHYH2yCMz1hbAgSUbzMLAKKn8JjmwTaZazBcGJ6TpwM\nJ44ckytXt9mHwtKRYVZGgcxJYBmOQTwizcV9Et9+yCJx4acrPq1I2TwmvZba\na4x6gBkWP0l/s1lsj603StTPvV5Ts6Sfj41Yz4iUwr3f9OZJfzijLTY1uBSX\nMvB3\r\n=R8pU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "6bc7a1c4de033ac1889789ec7285725a68f1d302", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"onetime": "^5.1.2", "is-stream": "^2.0.0", "get-stream": "^6.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^3.0.3", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "human-signals": "^2.1.0", "strip-final-newline": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.35.0", "ava": "^2.4.0", "nyc": "^15.1.0", "tsd": "^0.13.1", "p-event": "^4.2.0", "get-node": "^11.0.1", "tempfile": "^3.0.0", "is-running": "^2.1.0", "@types/node": "^14.14.10"}, "_npmOperationalInternal": {"tmp": "tmp/execa_5.0.1_1622396676428_0.4276988657431404", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "execa", "version": "5.1.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@5.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "dist": {"shasum": "3ea50ee863d226bfa323528cce1684e7481dfe46", "tarball": "https://registry.npmjs.org/execa/-/execa-5.1.0.tgz", "fileCount": 11, "integrity": "sha512-CkdUB7s2y6S+d4y+OM/+ZtQcJCiKUCth4cNImGMqrt2zEVtW2rfHGspQBE1GDo6LjeNIQmTPKXqTCKjqFKyu3A==", "signatures": [{"sig": "MEQCIB8b3jmcpR6hnmJ4ZJYRGUD1R15zEG8nOkIqiDK5yNDNAiBmVTheoVNm1POqnE9rPsLnWcg7sj3d1OrXze+dg2UyUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57498, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgt8GyCRA9TVsSAnZWagAAWpkP/iA2TUH7t6wbXWaNs/cr\nkGfmDpgl6coHL5MLetRFjIc9kyAYPPBjkX9J6kPYd9POOk8z2iW8vnEapHqF\ny0ZVdvLsHwTF7J5Io0tqYPoJZX910k3BSbFgsUCZr9rCZ+1Rm3xbEclQX+j0\n0zxbg8VB/3xVVn4/OiDcmYWKY626Tlyh5RlZ4jXV/xGht58eY8eYTT/gvk2Q\ndbO3Cr0OXuVFiTVPDjyPnXCcs7UWfjkGUTIQyYEkmkVYxjJ4AoUTcj1u3jYI\nkufbxgCMf4nrA8BBzgc6/+kyIw0w9cN7Cp1Y4qWVyAMfnzBeChNBkfwIpr+x\nyEUb05T33ze4JEtZY9NRQe7khagc8JTQbJtfXYuH1zbn6bruUgz0HLL5Fi2d\nPdbQ3pwGAtZ/NbGbu6T1QuPvAQJICW3Xc5E9syRqdtb6jUv/DD8dbG1zJftz\nxgaD7aidIU01GfyRdAtH3HC+o9HRC4hiPsGXNCyHHDh2cJX6bUJ4WoNJQxO4\n5AEIiSxk0fwNA8/5k0VPC3buIbpVJQ+oanF2cVHBffU9czfkw+wfK2YizaMD\nHZENmGG6kCDjC6eK7hYHk9vR9UcPVAOu9QXSiPt4j0jSW9jlHLNHfBce3AML\nhJOtv2fwvvhaW87ijKSfJTSvWbQKzK20uIYVK64OfzL/nCKFOYqNY7PC4Pm0\nGpzJ\r\n=2Mm3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "b3e96b00ba130e54b54467583c92eb566e3805f1", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"onetime": "^5.1.2", "is-stream": "^2.0.0", "get-stream": "^6.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^3.0.3", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "human-signals": "^2.1.0", "strip-final-newline": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.35.0", "ava": "^2.4.0", "nyc": "^15.1.0", "tsd": "^0.13.1", "p-event": "^4.2.0", "get-node": "^11.0.1", "tempfile": "^3.0.0", "is-running": "^2.1.0", "@types/node": "^14.14.10"}, "_npmOperationalInternal": {"tmp": "tmp/execa_5.1.0_1622655410854_0.8339980856816889", "host": "s3://npm-registry-packages"}}, "5.1.1": {"name": "execa", "version": "5.1.1", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@5.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "nyc": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "dist": {"shasum": "f80ad9cbf4298f7bd1d4c9555c21e93741c411dd", "tarball": "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz", "fileCount": 11, "integrity": "sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==", "signatures": [{"sig": "MEQCIEOvNhvpEYmseTp1MjgvrSHXxciPcpZaOyAIgwIBxvfUAiA1Yz4bN6X5Vkngye2sOQLtzdKK4gSDjgnQbLd3Rso0hA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgulbzCRA9TVsSAnZWagAA5+oQAIeagfdu0+/+QExG/EcB\n0fkkzV4FkpGDGvn9jOEAx7lhJGTwWkBfJA1wWKtuCOHqJlW/ifzCi/FHEt1p\nmZwH4MIstuVHSXI7qLZuP7ZiOCdGcqnJgSaxTrN/9soYVerX0l2svSG69hae\nVXh3G4GDsSlTvHD/F74NF11RFKWZUWSfUsG0lBFDDscCd9L8HQL8Yhy98sFo\nxOdZuE+DulIrUAHSHbkXqrKpb6GcNajYtEpk+S07ddWtBaQEPQxpXdm2It7g\ne41AV46H9n8UN9YZxQJPGJNYhm5btM2Mtk2gcZDskqDqJaRFQHTo1vMCU5GQ\n7/k2D96Xe2SW5s9JiPIMZDrSi1tFh/WDHz8a1hbrp4K92aNEMC9j5SEh74mJ\ncwgbnqm7JlcutBeElhEUxwUnVZw/QEtfT6hhU/Yugj2bqzsg9salrcjaKPds\nHODWdShZ14dB90H4r2udlz6MVda05Dc0/Q3i3WjxHdrW+shnHo/2t6DAhmJZ\nVY1QuIj6SnBbi3W8DGZYaReuZDfRc0sbmdZ4wYZF5+c/fjHB45tqhd6/9cqL\navwxIlYlow5AoKWPIEyIGl4875A5d1Akr91FOpMD/0Va4UC7inbAJGoJ2cxA\neHthwi+2jiEYezbv2uKXA3YHRQWsbzwN7jMNosZwoCSnpW/OiUMJECr2PlAc\nlu/u\r\n=Je/C\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "9216ec8035f55a3ddcbf07de8667f9d9d5c40c84", "scripts": {"test": "xo && nyc ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "7.10.0", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "16.2.0", "dependencies": {"onetime": "^5.1.2", "is-stream": "^2.0.0", "get-stream": "^6.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^3.0.3", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "human-signals": "^2.1.0", "strip-final-newline": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"xo": "^0.35.0", "ava": "^2.4.0", "nyc": "^15.1.0", "tsd": "^0.13.1", "p-event": "^4.2.0", "get-node": "^11.0.1", "tempfile": "^3.0.0", "is-running": "^2.1.0", "@types/node": "^14.14.10"}, "_npmOperationalInternal": {"tmp": "tmp/execa_5.1.1_1622824691411_0.865549631969796", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "execa", "version": "6.0.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@6.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "c8": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "dist": {"shasum": "598b46f09ae44f5d8097a30cfb1681d0f0371503", "tarball": "https://registry.npmjs.org/execa/-/execa-6.0.0.tgz", "fileCount": 11, "integrity": "sha512-m4wU9j4Z9nXXoqT8RSfl28JSwmMNLFF69OON8H/lL3NeU0tNpGz313bcOfYoBBHokB0dC2tMl3VUcKgHELhL2Q==", "signatures": [{"sig": "MEQCIBKJwe0jKB3mZDu5TtHgw85p+nL61R6YSPUAMk3O88fbAiBrY9ty21tQuTKIpdAQRUQs3Hgz9YT/jpFq+IYUxQDmcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56711, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlOIcCRA9TVsSAnZWagAAQnwQAJ0kz6+UhCrqGVQJNcqI\nhZKvi7lNauo6+WXsNx7+ZN9JW8fv7v8EuGwzoZ2CJ599I465flTOIVMHWxjx\n9G5ckDD/lLWa3WTwwob3ytbvfZuEWiZ/86v37FDR9/5NUxoDTXGikrm3ooMb\n/2iPSZxBP4ffBx8s1yaCcQflf1fRVQs3ccW+EBmhrIjVZF3cKGGPtDOm4FEy\nQ6KnHKDdwM2OTfmLZAS0b/PtlGFtG6ztoLJzAOceIwdhzbZeJiyoFcw/ZgA2\nJdQEuLjG5K3bAZaK5mJb1z62N39oO4pNIU9TzuM4SoW+W2NaRmBKnBYs0/G7\n01e4oeTu7brSsIkczY9h1FINmLPkq1pBZGM1gFWbMzny/2ZUGUuHr3dxLnsC\njThT5te223qxlL4Mqt3F5clxOxv80iB2BLz/zM8RFpOCbSu+gqhOt7//I9vZ\nNmS0GkkhDOL8e3t2ybvabfw500DROiPZRB7UZ1n8ylHAq906WBd4BdBcqtHS\nLrFLnNIBxjO05LpOZ/da2kyDEJB+21UtQraZU3W7r1sc1+Z7Qme20aQvyF/u\nngk45BBDo3cdH8LrOFNvVO1zFJm6Hb0MePa0YQPG6za42oL+ZEi2DClvodiv\nWMrBvETkSG8SUuAf+lDQlG8BhYQ6l2eaBOk9E0woaQ1JZ/dL+1eadsChJ9+n\nAPn+\r\n=Rh+X\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "e4b929547ade335b0dd9d7afe1a35989e19fbc8a", "scripts": {"test": "xo && c8 ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "8.1.0", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "12.22.1", "dependencies": {"onetime": "^6.0.0", "is-stream": "^3.0.0", "get-stream": "^6.0.1", "cross-spawn": "^7.0.3", "signal-exit": "^3.0.5", "merge-stream": "^2.0.0", "npm-run-path": "^5.0.1", "human-signals": "^3.0.1", "strip-final-newline": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.10.0", "xo": "^0.46.4", "ava": "^3.15.0", "tsd": "^0.18.0", "p-event": "^5.0.1", "get-node": "^12.0.0", "tempfile": "^4.0.0", "is-running": "^2.1.0", "@types/node": "^16.11.7"}, "_npmOperationalInternal": {"tmp": "tmp/execa_6.0.0_1637147163857_0.2244496372027187", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "execa", "version": "6.1.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@6.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "c8": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/no-empty-file": "off", "@typescript-eslint/ban-types": "off"}}, "dist": {"shasum": "cea16dee211ff011246556388effa0818394fb20", "tarball": "https://registry.npmjs.org/execa/-/execa-6.1.0.tgz", "fileCount": 11, "integrity": "sha512-QVWlX2e50heYJcCPG0iWtf8r0xjEYfz/OYLGDYH+IyjWezzPNxz63qNFOu0l4YftGWuizFVZHHs8PrLU5p2IDA==", "signatures": [{"sig": "MEYCIQCaAgmCz9upmXsVXuaUq/Ngw6ut3cuixd4Wt7SfyH2LAAIhALwkghNuvZ9/A3kIDUIyo7bwsxjJGBQmOWjjlP24mf8s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58260, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCOAiCRA9TVsSAnZWagAAc6IP/jGVQnLw9+nxyx3Qsg/S\nydsFjtCm6ExdlNnzGDWVaH/qXjKPOk1MNkikyfx9ME8c2OIdMLOCTk4EIb9k\nU4a3xe/21I2G78wHqENCVSQocCSH55hPa5ZzQ71uGi0GEVY1CUt/UgrraDHx\nxN2gL5t+nNfsKTkbA1Iwp0CaK4zJ4j1AQazQ/wGuUKe1YeX5uFwR0UUm9flO\nXJejSoqudF3PPrwz4tR55AGWbFkUKdM5Bcd6Bls02f1GHO5uF5yk5XGffmCO\n26kolZm7fDrVz5psrQqCkcxRQilBN+4YRu4QwQMzeHr5uz5vYpr8lDxP3MjR\nMH1KlSL00tqELfFvejgaOOtfQCrcHR2jBCih4EbU6UpYyIzE6v8YazSa1Pcy\nkaBk0P4ChkBTMpw5XsO9CvtImr9L9Ir+44lCvMDZ8zZ+hk4jzNzsulnwBEgP\nuKF5k3c6Rfk16Jm3aQZKMaVfwi1H0ewxjnldaXBpaPlst2IcdE/QCDacZRN5\nO2fO83UEdhjvLlBV1jFFvamZNoozoC2H73IyxK3EXQJRjJ99P5UOMkUes3dg\nAaw24bYx2N4nI4JOyLyOxK5UEfqm9Pz9zvkxOvDjdvg/cD3oCaOIdAkCEz8F\nW3p2ovxyxXuBLXK6JgBcJv7teOj1ZuAPR6EHP+kn6lnw8FbcJnCdk/9QsmBY\nRIPF\r\n=YA6j\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "exports": "./index.js", "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "484f28de7c35da5150155e7a523cbb20de161a4f", "scripts": {"test": "xo && c8 ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "8.3.2", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "16.13.1", "dependencies": {"onetime": "^6.0.0", "is-stream": "^3.0.0", "get-stream": "^6.0.1", "cross-spawn": "^7.0.3", "signal-exit": "^3.0.7", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "human-signals": "^3.0.1", "strip-final-newline": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.11.0", "xo": "^0.48.0", "ava": "^4.0.1", "tsd": "^0.19.1", "semver": "^7.3.5", "p-event": "^5.0.1", "get-node": "^12.0.0", "tempfile": "^4.0.0", "is-running": "^2.1.0", "@types/node": "^17.0.17"}, "_npmOperationalInternal": {"tmp": "tmp/execa_6.1.0_1644748833856_0.021054747515238725", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "execa", "version": "7.0.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@7.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "c8": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/no-empty-file": "off", "@typescript-eslint/ban-types": "off"}}, "ava": {"workerThreads": false}, "dist": {"shasum": "2a44e20e73797f6c2df23889927972386157d7e4", "tarball": "https://registry.npmjs.org/execa/-/execa-7.0.0.tgz", "fileCount": 11, "integrity": "sha512-tQbH0pH/8LHTnwTrsKWideqi6rFB/QNUawEwrn+WHyz7PX1Tuz2u7wfTvbaNBdP5JD5LVWxNo8/A8CHNZ3bV6g==", "signatures": [{"sig": "MEUCIQDtU4E3tEf5e4DLvwqJg0dusQY0Dh1AeKTlu51/XhTY0QIgYFna3WZVKVp0YkjjmKfgD2JXfyGO6DAl6bacyitted0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58145, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5821ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp02g//cjityN/TxvgW9e05U2uxJMQUyBJZCkuU6rqmATQdIxlBFANp\r\n7zkFXKnu2dYCCNxValiBurCuqZf08XLk423h4+lwQZ6LGjoLNmPFUiNiXpiZ\r\n2J+YnMzN9JzWiBxRcq33G3os3U25GEd3BylkUl3b/Uex3VkTvkbkHRmZY0lc\r\n2WzCg0E5JM1Te3ZwmXX0lIytsiEub9GS/HcicT4zh97OMtSNcAmz7Ojt5+LK\r\nmqfbe7klXxIQ7vG+mGBofXivpeY+OTzS9cL5DPxl49ZXPuJ0VExLW4ec2Mze\r\nq0+JShK8+nIMQI8EZyF9UkYXYR1pXUyv4+q9cO0PZ2qAOtIJzziqVDJ3OJVi\r\nkAmgPiC5k5BpRN7adgIrX8RvWoFfCxbhNsj4Wqz4FNMrhZZfICIrwAig/njU\r\n6BgrzMOCM7dwoEfFBmdK19vQMRtWmlDjBHUy8/NG/hjL1FIY0z1BS9s1ruWp\r\nL5uH6b7Rwij9YqyF2vWtmXyzRzs2IujqsSPY7SyHmQuMzpCl5b15W/eg/jht\r\nG5nd1Bbihu/s6DqQVPlBxeUlsWCmGsGvr+YZN0TJe2te2Z+qrtlSAZpkDm58\r\ne3MeO0qVPQ7Shqv3XZv+xiCG4fKj3td3NRF4GitAF+5f4OP2LI+ChkntYS1Q\r\nRVM7GliNaSIzKUynA4dICHxWbw9UV9XXiSY=\r\n=vda6\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^14.18.0 || ^16.14.0 || >=18.0.0"}, "exports": "./index.js", "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "6bb4a6d3b00e464b8ea36382e94329ecf4e21ee2", "scripts": {"test": "xo && c8 ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "14.21.1", "dependencies": {"onetime": "^6.0.0", "is-stream": "^3.0.0", "get-stream": "^6.0.1", "cross-spawn": "^7.0.3", "signal-exit": "^3.0.7", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "human-signals": "^4.3.0", "strip-final-newline": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "xo": "^0.53.1", "ava": "^5.2.0", "tsd": "^0.25.0", "p-event": "^5.0.1", "get-node": "^13.5.0", "path-key": "^4.0.0", "tempfile": "^4.0.0", "is-running": "^2.1.0", "@types/node": "^18.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa_7.0.0_1676135861086_0.536111724854178", "host": "s3://npm-registry-packages"}}, "7.1.0": {"name": "execa", "version": "7.1.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@7.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "c8": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/no-empty-file": "off", "@typescript-eslint/ban-types": "off"}}, "ava": {"workerThreads": false}, "dist": {"shasum": "50c6f39438b7ce407e8c7a6829c72b074778238d", "tarball": "https://registry.npmjs.org/execa/-/execa-7.1.0.tgz", "fileCount": 13, "integrity": "sha512-T6nIJO3LHxUZ6ahVRaxXz9WLEruXLqdcluA+UuTptXmLM7nDAn9lx9IfkxPyzEL21583qSt4RmL44pO71EHaJQ==", "signatures": [{"sig": "MEYCIQDYrjWcU98RWlzMhFaf8uU07yqm9ZX8PBqL8wC+OOWtUQIhAJ7XEw04+O5L2rl0Ro9dxcN9sabsilqiLgXTvu3E8zoF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkDRVCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoeFQ//aa7S57uYBH+uv/HaZaDLdf5nqEbSmxONJpx1oRkJGCr0MiFi\r\nYSABNKzmQq86DygDrv73prwcDBcFho9hS6kWpSDr0izd3oakizO3h2yX7lBB\r\nSSzAHeoR759fj0LSSk4Nb5u5za3PMyUTP20W/kLFyXu0xe7HLDUhncZH9JJi\r\nzPgc/HMjcNS5O2ycLHc45Gxaxxgv9v/PzUA6/OG4L8FXQfqFPugfBERkqgQE\r\nZUwqCxZ/4hZSQ+t9EeN6aqI0LEiqWHfcu27v8DUHujqXLtL0/JbHQxQ6yZch\r\nccm1XnaI0Nf76QrGJebTsuxmT09Ek1PpAt7f8BgeGzGdGzexbZ0ewgrJPLhG\r\nlzCEH8xo8EL11SwR4bfOPxa/zt968Ehuwx3QreHw/nkTdsnXzcyKhDYeO1Sn\r\n+eb7+08KsI/OBqJVNSahcvHxQwxPJF0T5k99IT8nZlrzw81aCqJ9cna4piPu\r\n/rtM9oWst6+/m4flVWI6BxgSwkLObsdKdqFe+zz0qQ3Mz5Az3G+ZixvVbuSR\r\nLepb8CnxC5W1rzlhTa/IILYTriZSexZbc/RoC+uEWRQGngOT6OvVOUXathsl\r\nIafHfbCndwKJ+7fzwvrulzfGP4NJ2FXLT5A7phq2viLMOSglUsoWqZ8qp4XP\r\nE753eaKZgIxoOcc+sWBrmQGP7A6q6DQrh5I=\r\n=eviU\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^14.18.0 || ^16.14.0 || >=18.0.0"}, "exports": "./index.js", "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "950d1e6f218656f00744627ca56405b1f063d4d2", "scripts": {"test": "xo && c8 ava && tsd"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "9.4.2", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "19.6.0", "dependencies": {"onetime": "^6.0.0", "is-stream": "^3.0.0", "get-stream": "^6.0.1", "cross-spawn": "^7.0.3", "signal-exit": "^3.0.7", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "human-signals": "^4.3.0", "strip-final-newline": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "xo": "^0.53.1", "ava": "^5.2.0", "tsd": "^0.25.0", "p-event": "^5.0.1", "get-node": "^13.5.0", "path-key": "^4.0.0", "tempfile": "^4.0.0", "is-running": "^2.1.0", "@types/node": "^18.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa_7.1.0_1678579010148_0.8991989036495251", "host": "s3://npm-registry-packages"}}, "7.1.1": {"name": "execa", "version": "7.1.1", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@7.1.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "c8": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/no-empty-file": "off", "@typescript-eslint/ban-types": "off"}}, "ava": {"workerThreads": false}, "dist": {"shasum": "3eb3c83d239488e7b409d48e8813b76bb55c9c43", "tarball": "https://registry.npmjs.org/execa/-/execa-7.1.1.tgz", "fileCount": 13, "integrity": "sha512-wH0eMf/UXckdUYnO21+HDztteVv05rq2GXksxT4fCGeHkBhw1DROXh40wcjMcRqDOWE7iPJ4n3M7e2+YFP+76Q==", "signatures": [{"sig": "MEUCIDvkNF81qGZyMhbgI4YGp/Ye6GI9SqRqlb75QyM/5jkEAiEAiFU/5PGxGAIcczZNcPPLfPAR26v6eip3lRhMzBYelx8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77042, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEM6KACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmod7g//YHLkwkK9fSIEePP0pWCQCa+SNiCMHsbLJuLlAOkiVcKumpBZ\r\nYh4SfKrsBegqSrTSqg4DrJAtuv5q9x89TO2TAChNyanKpw/yYxD0jP4uJPiS\r\nBuRsxdIdXv4LjrMdCrHyT3JR/OIOkzmvJP44g2c4Nhop4dLumg2agohSWNDW\r\nEtg0xAX7Ql6sBsmXpOQfrsbWbr5STFF/rRwMdVQbicTxQ/ymS0Hjj4XapqRV\r\nZOJ0gk7PZctn7CMMC0gmxTSeTjBJVI6up+wKUaQ1p2XIsMyv7QjUQlDu09Ge\r\netsPMitwBeG6CBKJf1c+FHvSu2tAO1xbZiCiHNXrkcRyzt9lnXYq8WOZP8ms\r\nhHnGY0NOvsy99qpc7R+y0UB9Mch+RW5lkWnSqSBCus1H9FCgYXkoCL9vsdsz\r\nRP6ldfbfG0KKNAwVq+bM8M3921pQuQmukkQ02sbrohNwmgxZG1rV8W8PnN56\r\nkJ/7aB2oeu2KugkPYK1lw+RH+BLHnlcOTPThuJ6PZLCE+2dyxYmY7RLFcqVm\r\nFCh9IadsVjguhT79wOfNg77q8++S23W4qq+JoF9KTasgmfb9qbqLem9SWZ3H\r\nDO6BYpBDeQ9yRcLm9EzwTcbJAZAkCX7CCT4zSJ8birdv3ZnbXE04Kf/S000o\r\ntZTRhR8WkryPBpcHYSyPM5WkgSU1ifszG3g=\r\n=B+2S\r\n-----END PGP SIGNATURE-----\r\n"}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^14.18.0 || ^16.14.0 || >=18.0.0"}, "exports": "./index.js", "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "244aead98590be6464bd6c85387618bc12029d8b", "scripts": {"test": "xo && c8 ava && tsd"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "9.6.1", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "19.7.0", "dependencies": {"onetime": "^6.0.0", "is-stream": "^3.0.0", "get-stream": "^6.0.1", "cross-spawn": "^7.0.3", "signal-exit": "^3.0.7", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "human-signals": "^4.3.0", "strip-final-newline": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "xo": "^0.53.1", "ava": "^5.2.0", "tsd": "^0.25.0", "p-event": "^5.0.1", "get-node": "^13.5.0", "path-key": "^4.0.0", "tempfile": "^4.0.0", "is-running": "^2.1.0", "@types/node": "^18.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa_7.1.1_1678823050253_0.21653438578407602", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "execa", "version": "7.2.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local", "zx"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@7.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "c8": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/no-empty-file": "off", "@typescript-eslint/ban-types": "off"}}, "ava": {"workerThreads": false}, "dist": {"shasum": "657e75ba984f42a70f38928cedc87d6f2d4fe4e9", "tarball": "https://registry.npmjs.org/execa/-/execa-7.2.0.tgz", "fileCount": 13, "integrity": "sha512-UduyVP7TLB5IcAQl+OzLyLcS/l32W/GLg+AhHJ+ow40FOk2U3SAllPwR44v4vmdFwIWqpdwxxpQbF1n5ta9seA==", "signatures": [{"sig": "MEQCIBWOqaVPJqgyWgaalrMEEHdPl9aSQEZFMFJKX9Irydp+AiBd0vNLT5yogE7YwgjHQ+5ZVU/xp69hwh73W8ZZYai8+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78941}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^14.18.0 || ^16.14.0 || >=18.0.0"}, "exports": "./index.js", "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "db9ea8bb5f10c559f1a757fb6677c1db87cba61b", "scripts": {"test": "xo && c8 ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "9.2.0", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "20.3.1", "dependencies": {"onetime": "^6.0.0", "is-stream": "^3.0.0", "get-stream": "^6.0.1", "cross-spawn": "^7.0.3", "signal-exit": "^3.0.7", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "human-signals": "^4.3.0", "strip-final-newline": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^7.12.0", "xo": "^0.54.2", "ava": "^5.2.0", "tsd": "^0.25.0", "p-event": "^5.0.1", "get-node": "^13.5.0", "path-key": "^4.0.0", "tempfile": "^4.0.0", "is-running": "^2.1.0", "@types/node": "^18.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa_7.2.0_1690417308571_0.11091594400399596", "host": "s3://npm-registry-packages"}}, "8.0.0": {"name": "execa", "version": "8.0.0", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local", "zx"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@8.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "c8": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/no-empty-file": "off", "@typescript-eslint/ban-types": "off"}}, "ava": {"workerThreads": false}, "dist": {"shasum": "bed67705f9966469cd93e521617868c52a4df6e8", "tarball": "https://registry.npmjs.org/execa/-/execa-8.0.0.tgz", "fileCount": 13, "integrity": "sha512-CTNS0BcKBcoOsawKBlpcKNmK4Kjuyz5jVLhf+PUsHGMqiKMVTa4cN3U7r7bRY8KTpfOGpXMo27fdy0dYVg2pqA==", "signatures": [{"sig": "MEUCIQD1pihV1ONqC2OfkXjlY/qcSt2ThyrCbPtWq7BHkCxqkgIgUBT6EtGo+xDm9GoTMfETA1LGHaPTclq46Bmk5aIkr7k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79424}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=16.17"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "e7dee28c415fccfe2f06cf1794f8c84ecc549d46", "scripts": {"test": "xo && c8 ava && tsd"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "20.5.0", "dependencies": {"onetime": "^6.0.0", "is-stream": "^3.0.0", "get-stream": "^8.0.1", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "human-signals": "^5.0.0", "strip-final-newline": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^8.0.1", "xo": "^0.55.0", "ava": "^5.2.0", "tsd": "^0.28.1", "p-event": "^6.0.0", "get-node": "^14.2.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "@types/node": "^20.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa_8.0.0_1692404827407_0.36530748880830766", "host": "s3://npm-registry-packages"}}, "8.0.1": {"name": "execa", "version": "8.0.1", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local", "zx"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@8.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "c8": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/no-empty-file": "off", "@typescript-eslint/ban-types": "off"}}, "ava": {"workerThreads": false}, "dist": {"shasum": "51f6a5943b580f963c3ca9c6321796db8cc39b8c", "tarball": "https://registry.npmjs.org/execa/-/execa-8.0.1.tgz", "fileCount": 13, "integrity": "sha512-VyhnebXciFV2DESc+p6B+y0LjSm0krU4OgJN44qFAhBY0TJ+1V61tYD2+wHusZ6F9n5K+vl8k0sTy7PEfV4qpg==", "signatures": [{"sig": "MEUCIQDjbrEvFs/4bELHS5gR5MBYqah2IIYQn9l9hDYY7/kGFwIgeRu+DUJNO9ShPW/T6vVSf+2Sq+G5d7p+paxlEMFaBTQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80144}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=16.17"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "f4b8b3ab601c94d1503f1010822952758dcc6350", "scripts": {"test": "xo && c8 ava && tsd"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "9.8.1", "description": "Process execution for humans", "directories": {}, "_nodeVersion": "20.5.1", "dependencies": {"onetime": "^6.0.0", "is-stream": "^3.0.0", "get-stream": "^8.0.1", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "human-signals": "^5.0.0", "strip-final-newline": "^3.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^8.0.1", "xo": "^0.55.0", "ava": "^5.2.0", "tsd": "^0.28.1", "p-event": "^6.0.0", "get-node": "^14.2.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "@types/node": "^20.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa_8.0.1_1692459828958_0.7820898400564069", "host": "s3://npm-registry-packages"}}, "9.0.0": {"name": "execa", "version": "9.0.0", "keywords": ["exec", "child", "process", "subprocess", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local", "zx"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@9.0.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "c8": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/no-empty-file": "off", "@typescript-eslint/ban-types": "off"}}, "ava": {"timeout": "240s", "concurrency": 1, "workerThreads": false}, "dist": {"shasum": "51d54955c3bab43849e5fca1117aa6010bcba313", "tarball": "https://registry.npmjs.org/execa/-/execa-9.0.0.tgz", "fileCount": 125, "integrity": "sha512-YqlYPdTp6UMdt0WEM8QKeBR8ORtgxWP7ZB5NB9AMlfGWg32Fg48j6uZdKfhG2o6cNJBIRPF0Ok93R0vY37oBWQ==", "signatures": [{"sig": "MEUCIQCP8NVpDQbzxdCzr2NSB3+5QrT58kQb49YvBO5UGLnyegIgEIc+eytry57lQ6nPWCIzUsstXBEtyq9AZjNGiaVqG3Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 288824}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "8fbb643438839e5eb1c4d702909649c8a142cf51", "scripts": {"test": "xo && c8 ava && tsd"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Process execution for humans", "directories": {}, "sideEffects": false, "_nodeVersion": "22.1.0", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^5.2.0", "human-signals": "^7.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "xo": "^0.58.0", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^4.0.0", "p-event": "^6.0.0", "get-node": "^15.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "@types/node": "^20.8.9", "path-exists": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa_9.0.0_1715199875870_0.8889086542849043", "host": "s3://npm-registry-packages"}}, "9.0.1": {"name": "execa", "version": "9.0.1", "keywords": ["exec", "child", "process", "subprocess", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local", "zx"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@9.0.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "c8": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/no-empty-file": "off", "@typescript-eslint/ban-types": "off"}}, "ava": {"timeout": "240s", "concurrency": 1, "workerThreads": false}, "dist": {"shasum": "e1a0ea8896a8975e581e7f8a905e1f66a2232365", "tarball": "https://registry.npmjs.org/execa/-/execa-9.0.1.tgz", "fileCount": 117, "integrity": "sha512-U5ck8xJmf3sVebV1v+Hh436VWHVHUfzkdbKJynd3kCP9sQRDxCY5x2Tml5lGB7XM6lpj6ATfgWWqynDt2MBLJg==", "signatures": [{"sig": "MEQCIC4b4XD6YSmwBtAo8dhLvG+D20fb89AsuutXdNDBNSaRAiBREJ/DgLdQ+sp6AUKQ8toCZuuoQcanXPexNCuXwnSH3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257907}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "f768c1af13d31298ea4638515e06113ea2950cb6", "scripts": {"test": "xo && c8 ava && tsd"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "10.6.0", "description": "Process execution for humans", "directories": {}, "sideEffects": false, "_nodeVersion": "20.12.2", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^5.2.0", "human-signals": "^7.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "xo": "^0.58.0", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^4.0.0", "p-event": "^6.0.0", "get-node": "^15.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "@types/node": "^20.8.9", "path-exists": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa_9.0.1_1715274535815_0.17228189305207864", "host": "s3://npm-registry-packages"}}, "9.0.2": {"name": "execa", "version": "9.0.2", "keywords": ["exec", "child", "process", "subprocess", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local", "zx"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@9.0.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "c8": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/no-empty-file": "off", "@typescript-eslint/ban-types": "off"}}, "ava": {"timeout": "240s", "concurrency": 1, "workerThreads": false}, "dist": {"shasum": "9590a079f6632cc6381cb724cc03ae2489cbdea7", "tarball": "https://registry.npmjs.org/execa/-/execa-9.0.2.tgz", "fileCount": 129, "integrity": "sha512-oO281GF7ksH/Ogv1xyDf1prvFta/6/XkGKxRUvA3IB2MU1rCJGlFs86HRZhdooow1ISkR0Np0rOxUCIJVw36Rg==", "signatures": [{"sig": "MEUCIDoIm64xl4nolBzRThA6OatyL8Hg2rcBrn70dNGpRWYuAiEAovAkJES1AXKbZ9FI2wxE3yKP7RDHgI1732s2sqF3O7c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 310789}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "b8c131ce8ef2c3c1ec0c4dc3a7059c0d59ef28d9", "scripts": {"test": "xo && c8 ava && tsd && tsc"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Process execution for humans", "directories": {}, "sideEffects": false, "_nodeVersion": "22.1.0", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^5.2.0", "human-signals": "^7.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "xo": "^0.58.0", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^4.0.0", "p-event": "^6.0.0", "get-node": "^15.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "typescript": "^5.4.5", "@types/node": "^20.8.9", "path-exists": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa_9.0.2_1715321515822_0.7570155812001189", "host": "s3://npm-registry-packages"}}, "9.1.0": {"name": "execa", "version": "9.1.0", "keywords": ["exec", "child", "process", "subprocess", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local", "zx"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@9.1.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "c8": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/no-empty-file": "off", "@typescript-eslint/ban-types": "off"}}, "ava": {"timeout": "240s", "concurrency": 1, "workerThreads": false}, "dist": {"shasum": "c42845d2b079642b8e07d9de81db13cdb91e7a9b", "tarball": "https://registry.npmjs.org/execa/-/execa-9.1.0.tgz", "fileCount": 129, "integrity": "sha512-lSgHc4Elo2m6bUDhc3Hl/VxvUDJdQWI40RZ4KMY9bKRc+hgMOT7II/JjbNDhI8VnMtrCb7U/fhpJIkLORZozWw==", "signatures": [{"sig": "MEUCIDWT2OmV/bQow1mQ/KtgGp7lV8+gvG9dqW5YDcuo1UICAiEAz6mWXtCLw95/9JBf+RL4WjS2EsYpse24No97CSGlz1Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 311022}, "type": "module", "types": "./index.d.ts", "engines": {"node": ">=18"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "62d02af66940551bfb50699d7d02eed942453952", "scripts": {"lint": "xo", "test": "npm run lint && npm run unit && npm run type", "type": "tsd && tsc && npx --yes tsd@0.29.0 && npx --yes --package typescript@5.1 tsc", "unit": "c8 ava"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Process execution for humans", "directories": {}, "sideEffects": false, "_nodeVersion": "22.1.0", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^5.2.0", "human-signals": "^7.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "xo": "^0.58.0", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^4.0.0", "p-event": "^6.0.0", "get-node": "^15.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "typescript": "^5.4.5", "@types/node": "^20.8.9", "path-exists": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa_9.1.0_1715611293529_0.7093423551604487", "host": "s3://npm-registry-packages"}}, "9.2.0": {"name": "execa", "version": "9.2.0", "keywords": ["exec", "child", "process", "subprocess", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local", "zx"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@9.2.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "c8": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/no-empty-file": "off", "@typescript-eslint/ban-types": "off"}}, "ava": {"timeout": "240s", "concurrency": 1, "workerThreads": false}, "dist": {"shasum": "ec5e9de67a714d0f47ce073d37a851fbf0c2f688", "tarball": "https://registry.npmjs.org/execa/-/execa-9.2.0.tgz", "fileCount": 137, "integrity": "sha512-vpOyYg7UAVKLAWWtRS2gAdgkT7oJbCn0me3gmUmxZih4kd3MF/oo8kNTBTIbkO3yuuF5uB4ZCZfn8BOolITYhg==", "signatures": [{"sig": "MEUCIFoPpN0vGJ0t53br2Nd1+689TjnYRHPCuCGVAhhA8XvhAiEA+P1PE/wCZ77ezMPzMAFozO/9pbgVvCBEnUn9oM7t9mU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 314014}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^18.19.0 || >=20.5.0"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "cbe805c72ddcff932d8c37bb1910aa6864099cea", "scripts": {"lint": "xo", "test": "npm run lint && npm run unit && npm run type", "type": "tsd && tsc && npx --yes tsd@0.29.0 && npx --yes --package typescript@5.1 tsc", "unit": "c8 ava"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "10.7.0", "description": "Process execution for humans", "directories": {}, "sideEffects": false, "_nodeVersion": "22.1.0", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^5.2.0", "human-signals": "^7.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^9.1.0", "xo": "^0.58.0", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^4.0.0", "get-node": "^15.0.0", "is-in-ci": "^0.1.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "typescript": "^5.4.5", "@types/node": "^20.8.9", "path-exists": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa_9.2.0_1717695847019_0.8381508923629988", "host": "s3://npm-registry-packages"}}, "9.3.0": {"name": "execa", "version": "9.3.0", "keywords": ["exec", "child", "process", "subprocess", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local", "zx"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@9.3.0", "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "c8": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/no-empty-file": "off", "@typescript-eslint/ban-types": "off"}}, "ava": {"timeout": "240s", "concurrency": 1, "workerThreads": false}, "dist": {"shasum": "b10b70f52c1a978985e8492cc1fa74795c59963c", "tarball": "https://registry.npmjs.org/execa/-/execa-9.3.0.tgz", "fileCount": 141, "integrity": "sha512-l6JFbqnHEadBoVAVpN5dl2yCyfX28WoBAGaoQcNmLLSedOxTxcn2Qa83s8I/PA5i56vWru2OHOtrwF7Om2vqlg==", "signatures": [{"sig": "MEUCIH3QI/C3kOUBeWEnSyc7kbRh6mqGyu9PyvGy3tB9U/DMAiEAmn5vqoxhl0g7vSQzTMCKYa7WC+QWfvSAvAfmt+NFNds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 320815}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^18.19.0 || >=20.5.0"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "57658b0190dc0f0e5ed2c5984c9d9ea526e085b5", "scripts": {"lint": "xo", "test": "npm run lint && npm run unit && npm run type", "type": "tsd && tsc && npx --yes tsd@0.29.0 && npx --yes --package typescript@5.1 tsc", "unit": "c8 --merge-async ava"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "10.8.1", "description": "Process execution for humans", "directories": {}, "sideEffects": false, "_nodeVersion": "22.3.0", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^5.2.0", "human-signals": "^7.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.2", "xo": "^0.58.0", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^4.0.0", "get-node": "^15.0.0", "is-in-ci": "^0.1.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "typescript": "^5.4.5", "@types/node": "^20.8.9", "path-exists": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa_9.3.0_1718994141444_0.515128501523195", "host": "s3://npm-registry-packages"}}, "9.3.1": {"name": "execa", "version": "9.3.1", "keywords": ["exec", "child", "process", "subprocess", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local", "zx"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@9.3.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "c8": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/no-empty-file": "off", "@typescript-eslint/ban-types": "off"}}, "ava": {"timeout": "240s", "concurrency": 1, "workerThreads": false}, "dist": {"shasum": "09c86ab4dc2ef3de6d34f6568f4bad76ded4fded", "tarball": "https://registry.npmjs.org/execa/-/execa-9.3.1.tgz", "fileCount": 141, "integrity": "sha512-gdhefCCNy/8tpH/2+ajP9IQc14vXchNdd0weyzSJEFURhRMGncQ+zKFxwjAufIewPEJm9BPOaJnvg2UtlH2gPQ==", "signatures": [{"sig": "MEQCICkoAFRX7PjBYgB/x4pK+2U/GlPvZ5LG9qpB8e4eFbEPAiAoTvJ8+G5Y3CgCXfTVNCLagQx8NcxG1gs22Sp3Om5yLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 322819}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^18.19.0 || >=20.5.0"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "0a51f7cbef53e7290a3604e585e1b2e61da37367", "scripts": {"lint": "xo", "test": "npm run lint && npm run unit && npm run type", "type": "tsd && tsc && npx --yes tsd@0.29.0 && npx --yes --package typescript@5.1 tsc", "unit": "c8 --merge-async ava"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "10.8.2", "description": "Process execution for humans", "directories": {}, "sideEffects": false, "_nodeVersion": "22.6.0", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^5.2.0", "human-signals": "^8.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.2", "xo": "^0.59.3", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^4.0.0", "get-node": "^15.0.0", "is-in-ci": "^1.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "typescript": "^5.4.5", "@types/node": "^22.1.0", "path-exists": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa_9.3.1_1723665567340_0.8882255226059796", "host": "s3://npm-registry-packages"}}, "9.4.0": {"name": "execa", "version": "9.4.0", "keywords": ["exec", "child", "process", "subprocess", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local", "zx"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@9.4.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "c8": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/no-empty-file": "off", "@typescript-eslint/ban-types": "off"}}, "ava": {"timeout": "240s", "concurrency": 1, "workerThreads": false}, "dist": {"shasum": "071ff6516c46eb82af9a559dba3c891637a10f3f", "tarball": "https://registry.npmjs.org/execa/-/execa-9.4.0.tgz", "fileCount": 141, "integrity": "sha512-yKHlle2YGxZE842MERVIplWwNH5VYmqqcPFgtnlU//K8gxuFFXu0pwd/CrfXTumFpeEiufsP7+opT/bPJa1yVw==", "signatures": [{"sig": "MEUCIQCOLlqA9hmVV5KowNi+OePcKiy1Agqs0x3zlyteVE7p9wIgKDC2vQ1OCpumanB+NHzAgCXFcWTXzHSDglXYON0V0go=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 322959}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^18.19.0 || >=20.5.0"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "1b9b9bbf17705c28019f770cecd9920db206f824", "scripts": {"lint": "xo", "test": "npm run lint && npm run unit && npm run type", "type": "tsd && tsc && npx --yes tsd@0.29.0 && npx --yes --package typescript@5.1 tsc", "unit": "c8 --merge-async ava"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Process execution for humans", "directories": {}, "sideEffects": false, "_nodeVersion": "22.8.0", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^6.0.0", "human-signals": "^8.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.2", "xo": "^0.59.3", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^4.0.0", "get-node": "^15.0.0", "is-in-ci": "^1.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "typescript": "^5.4.5", "@types/node": "^22.1.0", "path-exists": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa_9.4.0_1726526466186_0.4063254652699726", "host": "s3://npm-registry-packages"}}, "9.4.1": {"name": "execa", "version": "9.4.1", "keywords": ["exec", "child", "process", "subprocess", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local", "zx"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@9.4.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "c8": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/no-empty-file": "off", "@typescript-eslint/ban-types": "off"}}, "ava": {"timeout": "240s", "concurrency": 1, "workerThreads": false}, "dist": {"shasum": "823c74244c55bca37d04c9bb996c397f4a1486a4", "tarball": "https://registry.npmjs.org/execa/-/execa-9.4.1.tgz", "fileCount": 141, "integrity": "sha512-5eo/BRqZm3GYce+1jqX/tJ7duA2AnE39i88fuedNFUV8XxGxUpF3aWkBRfbUcjV49gCkvS/pzc0YrCPhaIewdg==", "signatures": [{"sig": "MEYCIQCiJCgmf6by+EBp+TC2zC+2VWHRfMpDroxw9mCOOC4hrgIhAJA2mUue8WG1kz2nZUUNweAF2PeXkp7enS/tRrYRyuGe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 323477}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^18.19.0 || >=20.5.0"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "a4d13df33072740a3620ba35491199a5f9abda03", "scripts": {"lint": "xo", "test": "npm run lint && npm run unit && npm run type", "type": "tsd && tsc && npx --yes tsd@0.29.0 && npx --yes --package typescript@5.1 tsc", "unit": "c8 --merge-async ava"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Process execution for humans", "directories": {}, "sideEffects": false, "_nodeVersion": "22.9.0", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^6.0.0", "human-signals": "^8.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.2", "xo": "^0.59.3", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^4.0.0", "get-node": "^15.0.0", "is-in-ci": "^1.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "typescript": "^5.4.5", "@types/node": "^22.1.0", "path-exists": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa_9.4.1_1729105281741_0.45887881683722953", "host": "s3://npm-registry-packages"}}, "9.5.0": {"name": "execa", "version": "9.5.0", "keywords": ["exec", "child", "process", "subprocess", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local", "zx"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@9.5.0", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "c8": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/no-empty-file": "off", "@typescript-eslint/ban-types": "off"}}, "ava": {"timeout": "240s", "concurrency": 1, "workerThreads": false}, "dist": {"shasum": "b4437553fdd084f65184b5537a9bc38eac26c59a", "tarball": "https://registry.npmjs.org/execa/-/execa-9.5.0.tgz", "fileCount": 141, "integrity": "sha512-t7vvYt+oKnMbF3O+S5+HkylsPrsUatwJSe4Cv+4017R0MCySjECxnVJ2eyDXVD/Xpj5H29YzyYn6eEpugG7GJA==", "signatures": [{"sig": "MEUCIQC/SCF2ssr3Vzv9w9gSpPle5k9rLK+t3rJuwWi1VJvDwgIgOy6JvLILcoGlXqEL/w4wsKrOmICqpdZ38P+IPp369l0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 323685}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^18.19.0 || >=20.5.0"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "4d1e55a68d79f4ffc2b3393277ea366598807413", "scripts": {"lint": "xo", "test": "npm run lint && npm run unit && npm run type", "type": "tsd && tsc && npx --yes tsd@0.29.0 && npx --yes --package typescript@5.1 tsc", "unit": "c8 --merge-async ava"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Process execution for humans", "directories": {}, "sideEffects": false, "_nodeVersion": "22.9.0", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^6.0.0", "human-signals": "^8.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.2", "xo": "^0.59.3", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^4.0.0", "get-node": "^15.0.0", "is-in-ci": "^1.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "typescript": "^5.4.5", "@types/node": "^22.1.0", "path-exists": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa_9.5.0_1730015719852_0.0846563008671335", "host": "s3://npm-registry-packages"}}, "9.5.1": {"name": "execa", "version": "9.5.1", "keywords": ["exec", "child", "process", "subprocess", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local", "zx"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@9.5.1", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "c8": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/no-empty-file": "off", "@typescript-eslint/ban-types": "off"}}, "ava": {"timeout": "240s", "concurrency": 1, "workerThreads": false}, "dist": {"shasum": "ab9b68073245e1111bba359962a34fcdb28deef2", "tarball": "https://registry.npmjs.org/execa/-/execa-9.5.1.tgz", "fileCount": 141, "integrity": "sha512-QY5PPtSonnGwhhHDNI7+3RvY285c7iuJFFB+lU+oEzMY/gEGJ808owqJsrr8Otd1E/x07po1LkUBmdAc5duPAg==", "signatures": [{"sig": "MEUCIQDTa9GxeQK9wvYMUkPk+y4/SiW+W0NkzKLuAyLxoQ3+rAIgF8GkNWeMq/gzKLR8FDfGUDpFVDgu0ipyVkPueonRolI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 324065}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^18.19.0 || >=20.5.0"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "c8cff27a47b6e6f1cfbfec2bf7fa9dcd08cefed1", "scripts": {"lint": "xo", "test": "npm run lint && npm run unit && npm run type", "type": "tsd && tsc && npx --yes tsd@0.29.0 && npx --yes --package typescript@5.1 tsc", "unit": "c8 --merge-async ava"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "10.8.3", "description": "Process execution for humans", "directories": {}, "sideEffects": false, "_nodeVersion": "22.9.0", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^6.0.0", "human-signals": "^8.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.2", "xo": "^0.59.3", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^5.0.0", "get-node": "^15.0.0", "is-in-ci": "^1.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "typescript": "^5.4.5", "@types/node": "^22.1.0", "path-exists": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa_9.5.1_1730186824117_0.2875818587419192", "host": "s3://npm-registry-packages"}}, "9.5.2": {"name": "execa", "version": "9.5.2", "keywords": ["exec", "child", "process", "subprocess", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local", "zx"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@9.5.2", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "c8": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/no-empty-file": "off", "@typescript-eslint/ban-types": "off"}}, "ava": {"timeout": "240s", "concurrency": 1, "workerThreads": false}, "dist": {"shasum": "a4551034ee0795e241025d2f987dab3f4242dff2", "tarball": "https://registry.npmjs.org/execa/-/execa-9.5.2.tgz", "fileCount": 141, "integrity": "sha512-EHlpxMCpHWSAh1dgS6bVeoLAXGnJNdR93aabr4QCGbzOM73o5XmRfM/e5FUqsw3aagP8S8XEWUWFAxnRBnAF0Q==", "signatures": [{"sig": "MEYCIQC4ggedKyhEYtS/7Hie5RfwO5VVgWlkFBCXHkI+kgHgswIhAMK+UC06Ixd1jAP9tC7g03CApX4Akpb7i/JpmsnI9JLh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 324196}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^18.19.0 || >=20.5.0"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "99d1741d2525eca71b986282148bbf2983356428", "scripts": {"lint": "xo", "test": "npm run lint && npm run unit && npm run type", "type": "tsd && tsc && npx --yes tsd@0.29.0 && npx --yes --package typescript@5.1 tsc", "unit": "c8 --merge-async ava"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "10.9.1", "description": "Process execution for humans", "directories": {}, "sideEffects": false, "_nodeVersion": "23.3.0", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^6.0.0", "human-signals": "^8.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.2", "xo": "^0.59.3", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^5.0.0", "get-node": "^15.0.0", "is-in-ci": "^1.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "typescript": "^5.4.5", "@types/node": "^22.1.0", "path-exists": "^5.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/execa_9.5.2_1733613132725_0.5580664949557479", "host": "s3://npm-registry-packages"}}, "9.5.3": {"name": "execa", "version": "9.5.3", "keywords": ["exec", "child", "process", "subprocess", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local", "zx"], "author": {"url": "https://sindresorhus.com", "name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "execa@9.5.3", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/sindresorhus/execa#readme", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "c8": {"exclude": ["**/fixtures/**", "**/test.js", "**/test/**"], "reporter": ["text", "lcov"]}, "xo": {"rules": {"unicorn/no-empty-file": "off", "@typescript-eslint/ban-types": "off"}}, "ava": {"timeout": "240s", "concurrency": 1, "workerThreads": false}, "dist": {"shasum": "aa9b6e92ea6692b88a240efc260ca30489b33e2a", "tarball": "https://registry.npmjs.org/execa/-/execa-9.5.3.tgz", "fileCount": 142, "integrity": "sha512-QFNnTvU3UjgWFy8Ef9iDHvIdcgZ344ebkwYx4/KLbR+CKQA4xBaHzv+iRpp86QfMHP8faFQLh8iOc57215y4Rg==", "signatures": [{"sig": "MEUCICdaZVDXL5YOrjkQdnyXrcyojjP+3Utuqx8lkwmFilV2AiEAjxEIk9whm2T4ie+8kdsnKOIS9ViN5R4j4C/fWYCrFog=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 324313}, "type": "module", "types": "./index.d.ts", "engines": {"node": "^18.19.0 || >=20.5.0"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "gitHead": "0853f748ca58a1a2f65b4619f92145de9c9a7f0f", "scripts": {"lint": "xo", "test": "npm run lint && npm run unit && npm run type", "type": "tsd && tsc && npx --yes tsd@0.29.0 && npx --yes --package typescript@5.1 tsc", "unit": "c8 --merge-async ava"}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/sindresorhus/execa.git", "type": "git"}, "_npmVersion": "10.9.2", "description": "Process execution for humans", "directories": {}, "sideEffects": false, "_nodeVersion": "24.0.0", "dependencies": {"figures": "^6.1.0", "is-stream": "^4.0.1", "pretty-ms": "^9.0.0", "get-stream": "^9.0.0", "cross-spawn": "^7.0.3", "signal-exit": "^4.1.0", "yoctocolors": "^2.0.0", "is-plain-obj": "^4.1.0", "npm-run-path": "^6.0.0", "human-signals": "^8.0.0", "strip-final-newline": "^4.0.0", "@sindresorhus/merge-streams": "^4.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"c8": "^10.1.2", "xo": "^0.59.3", "ava": "^6.0.1", "tsd": "^0.31.0", "which": "^5.0.0", "get-node": "^15.0.0", "is-in-ci": "^1.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "is-running": "^2.1.0", "typescript": "^5.4.5", "@types/node": "^22.1.0", "path-exists": "^5.0.0", "log-process-errors": "^12.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/execa_9.5.3_1746735068373_0.655013874404536", "host": "s3://npm-registry-packages-npm-production"}}, "9.6.0": {"name": "execa", "version": "9.6.0", "description": "Process execution for humans", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/execa.git"}, "funding": "https://github.com/sindresorhus/execa?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": "^18.19.0 || >=20.5.0"}, "scripts": {"test": "npm run lint && npm run unit && npm run type", "lint": "xo", "unit": "c8 --merge-async ava", "type": "tsd && tsc && npx --yes tsd@0.29.0 && npx --yes --package typescript@5.1 tsc"}, "keywords": ["exec", "child", "process", "subprocess", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local", "zx"], "dependencies": {"@sindresorhus/merge-streams": "^4.0.0", "cross-spawn": "^7.0.6", "figures": "^6.1.0", "get-stream": "^9.0.0", "human-signals": "^8.0.1", "is-plain-obj": "^4.1.0", "is-stream": "^4.0.1", "npm-run-path": "^6.0.0", "pretty-ms": "^9.2.0", "signal-exit": "^4.1.0", "strip-final-newline": "^4.0.0", "yoctocolors": "^2.1.1"}, "devDependencies": {"@types/node": "^22.15.21", "ava": "^6.3.0", "c8": "^10.1.3", "get-node": "^15.0.3", "is-in-ci": "^1.0.0", "is-running": "^2.1.0", "log-process-errors": "^12.0.1", "path-exists": "^5.0.0", "path-key": "^4.0.0", "tempfile": "^5.0.0", "tsd": "^0.32.0", "typescript": "^5.8.3", "which": "^5.0.0", "xo": "^0.60.0"}, "c8": {"reporter": ["text", "lcov"], "exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "ava": {"workerThreads": false, "concurrency": 1, "timeout": "240s"}, "xo": {"rules": {"unicorn/no-empty-file": "off", "@typescript-eslint/ban-types": "off"}}, "_id": "execa@9.6.0", "gitHead": "a31fe55782993f2483d30955a8799ab88e20687c", "types": "./index.d.ts", "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "homepage": "https://github.com/sindresorhus/execa#readme", "_nodeVersion": "20.19.1", "_npmVersion": "10.9.2", "dist": {"integrity": "sha512-jpWzZ1ZhwUmeWRhS7Qv3mhpOhLfwI+uAX4e5fOcXqwMR7EcJ0pj2kV1CVzHVMX/LphnKWD3LObjZCoJ71lKpHw==", "shasum": "38665530e54e2e018384108322f37f35ae74f3bc", "tarball": "https://registry.npmjs.org/execa/-/execa-9.6.0.tgz", "fileCount": 142, "unpackedSize": 324314, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC8RivxXFB+oyg4qzLgptkowdR0W2p8q7NV8mXOGr9skwIhALBWYZqeNkUSeH4u7HHQODGTNRZWSRubgJ5YFKaH2vcQ"}]}, "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/execa_9.6.0_1748296764995_0.63673699338256"}, "_hasShrinkwrap": false}}, "time": {"created": "2015-12-05T23:03:32.752Z", "modified": "2025-05-26T21:59:25.326Z", "0.1.0": "2015-12-05T23:03:32.752Z", "0.1.1": "2015-12-07T10:22:42.263Z", "0.2.0": "2016-01-09T13:45:54.248Z", "0.2.1": "2016-01-09T13:59:27.544Z", "0.2.2": "2016-01-11T12:24:35.580Z", "0.3.0": "2016-04-22T21:13:14.859Z", "0.4.0": "2016-04-26T17:27:36.260Z", "0.5.0": "2016-10-05T08:28:19.328Z", "0.5.1": "2017-01-08T15:31:59.664Z", "0.6.0": "2017-01-09T09:37:57.298Z", "0.6.1": "2017-03-13T06:34:43.860Z", "0.6.2": "2017-03-20T05:57:19.501Z", "0.6.3": "2017-03-21T12:31:01.154Z", "0.7.0": "2017-06-09T21:50:42.038Z", "0.8.0": "2017-08-05T20:44:47.515Z", "0.9.0": "2018-01-11T23:29:49.655Z", "0.10.0": "2018-03-15T04:59:55.439Z", "0.11.0": "2018-08-20T07:57:57.953Z", "1.0.0": "2018-08-26T17:43:44.171Z", "2.0.0-alpha.0": "2019-06-18T17:47:06.429Z", "2.0.0": "2019-06-25T11:26:42.705Z", "2.0.1": "2019-06-26T10:13:29.987Z", "2.0.2": "2019-07-01T10:26:38.202Z", "2.0.3": "2019-07-05T08:35:52.894Z", "2.0.4": "2019-08-15T10:33:01.051Z", "2.0.5": "2019-10-04T08:05:55.676Z", "2.1.0": "2019-10-09T09:10:42.025Z", "3.0.0": "2019-10-14T14:08:16.289Z", "3.1.0": "2019-10-15T18:34:55.311Z", "3.2.0": "2019-10-17T15:11:51.894Z", "3.3.0": "2019-11-12T10:07:50.203Z", "3.4.0": "2019-11-25T10:19:23.519Z", "4.0.0": "2019-12-19T09:49:46.049Z", "4.0.1": "2020-05-08T14:19:42.795Z", "4.0.2": "2020-05-22T14:08:36.806Z", "4.0.3": "2020-07-07T18:32:23.136Z", "4.1.0": "2020-10-28T11:30:52.359Z", "5.0.0": "2020-12-03T05:19:51.155Z", "5.0.1": "2021-05-30T17:44:36.615Z", "5.1.0": "2021-06-02T17:36:50.972Z", "5.1.1": "2021-06-04T16:38:11.620Z", "6.0.0": "2021-11-17T11:06:04.038Z", "6.1.0": "2022-02-13T10:40:34.019Z", "7.0.0": "2023-02-11T17:17:41.259Z", "7.1.0": "2023-03-11T23:56:50.346Z", "7.1.1": "2023-03-14T19:44:10.464Z", "7.2.0": "2023-07-27T00:21:48.750Z", "8.0.0": "2023-08-19T00:27:07.586Z", "8.0.1": "2023-08-19T15:43:49.166Z", "9.0.0": "2024-05-08T20:24:36.054Z", "9.0.1": "2024-05-09T17:08:56.044Z", "9.0.2": "2024-05-10T06:11:55.996Z", "9.1.0": "2024-05-13T14:41:33.759Z", "9.2.0": "2024-06-06T17:44:07.194Z", "9.3.0": "2024-06-21T18:22:21.706Z", "9.3.1": "2024-08-14T19:59:27.851Z", "9.4.0": "2024-09-16T22:41:06.438Z", "9.4.1": "2024-10-16T19:01:22.009Z", "9.5.0": "2024-10-27T07:55:20.023Z", "9.5.1": "2024-10-29T07:27:04.290Z", "9.5.2": "2024-12-07T23:12:12.891Z", "9.5.3": "2025-05-08T20:11:08.632Z", "9.6.0": "2025-05-26T21:59:25.151Z"}, "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "license": "MIT", "homepage": "https://github.com/sindresorhus/execa#readme", "keywords": ["exec", "child", "process", "subprocess", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local", "zx"], "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/execa.git"}, "description": "Process execution for humans", "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "readme": "<picture>\n\t<source media=\"(prefers-color-scheme: dark)\" srcset=\"media/logo_dark.svg\">\n\t<img alt=\"execa logo\" src=\"media/logo.svg\" width=\"400\">\n</picture>\n<br>\n\n[![Coverage Status](https://codecov.io/gh/sindresorhus/execa/branch/main/graph/badge.svg)](https://codecov.io/gh/sindresorhus/execa)\n\n> Process execution for humans\n\n<br>\n\n---\n\n<div align=\"center\">\n\t<p>\n\t\t<p>\n\t\t\t<sup>\n\t\t\t\t<a href=\"https://github.com/sponsors/sindresorhus\">Sindre's open source work is supported by the community</a>\n\t\t\t</sup>\n\t\t</p>\n\t\t<sup>Special thanks to:</sup>\n\t\t<br>\n\t\t<br>\n\t\t<a href=\"https://coderabbit.ai?utm_source=sindre&utm_medium=execa\">\n\t\t\t<img width=\"300\" src=\"https://sindresorhus.com/assets/thanks/coderabbit-logo.png\" alt=\"CodeRabbit logo\">\n\t\t</a>\n\t\t<br>\n\t\t<br>\n\t</p>\n</div>\n\n---\n\n<br>\n\nExeca runs commands in your script, application or library. Unlike shells, it is [optimized](docs/bash.md) for programmatic usage. Built on top of the [`child_process`](https://nodejs.org/api/child_process.html) core module.\n\n## Features\n\n- [Simple syntax](#simple-syntax): promises and [template strings](docs/execution.md#template-string-syntax), like [`zx`](docs/bash.md).\n- [Script](#script) interface.\n- [No escaping](docs/escaping.md) nor quoting needed. No risk of shell injection.\n- Execute [locally installed binaries](#local-binaries) without `npx`.\n- Improved [Windows support](docs/windows.md): [shebangs](docs/windows.md#shebang), [`PATHEXT`](https://ss64.com/nt/path.html#pathext), [graceful termination](#graceful-termination), [and more](https://github.com/moxystudio/node-cross-spawn?tab=readme-ov-file#why).\n- [Detailed errors](#detailed-error), [verbose mode](#verbose-mode) and [custom logging](#custom-logging), for [debugging](docs/debugging.md).\n- [Pipe multiple subprocesses](#pipe-multiple-subprocesses) better than in shells: retrieve [intermediate results](docs/pipe.md#result), use multiple [sources](docs/pipe.md#multiple-sources-1-destination)/[destinations](docs/pipe.md#1-source-multiple-destinations), [unpipe](docs/pipe.md#unpipe).\n- [Split](#split-into-text-lines) the output into text lines, or [iterate](#iterate-over-text-lines) progressively over them.\n- Strip [unnecessary newlines](docs/lines.md#newlines).\n- Pass any [input](docs/input.md) to the subprocess: [files](#file-input), [strings](#simple-input), [`Uint8Array`s](docs/binary.md#binary-input), [iterables](docs/streams.md#iterables-as-input), [objects](docs/transform.md#object-mode) and almost any [other type](#any-input-type).\n- Return [almost any type](#any-output-type) from the subprocess, or redirect it to [files](#file-output).\n- Get [interleaved output](#interleaved-output) from `stdout` and `stderr` similar to what is printed on the terminal.\n- Retrieve the output [programmatically and print it](#programmatic--terminal-output) on the console at the same time.\n- [Transform or filter](#transformfilter-output) the input and output with [simple functions](docs/transform.md).\n- Pass [Node.js streams](docs/streams.md#nodejs-streams) or [web streams](#web-streams) to subprocesses, or [convert](#convert-to-duplex-stream) subprocesses to [a stream](docs/streams.md#converting-a-subprocess-to-a-stream).\n- [Exchange messages](#exchange-messages) with the subprocess.\n- Ensure subprocesses exit even when they [intercept termination signals](docs/termination.md#forceful-termination), or when the current process [ends abruptly](docs/termination.md#current-process-exit).\n\n## Install\n\n```sh\nnpm install execa\n```\n\n## Documentation\n\nExecution:\n- ▶️ [Basic execution](docs/execution.md)\n- 💬 [Escaping/quoting](docs/escaping.md)\n- 💻 [Shell](docs/shell.md)\n- 📜 [Scripts](docs/scripts.md)\n- 🐢 [Node.js files](docs/node.md)\n- 🌐 [Environment](docs/environment.md)\n- ❌ [Errors](docs/errors.md)\n- 🏁 [Termination](docs/termination.md)\n\nInput/output:\n- 🎹 [Input](docs/input.md)\n- 📢 [Output](docs/output.md)\n- 📃 [Text lines](docs/lines.md)\n- 🤖 [Binary data](docs/binary.md)\n- 🧙 [Transforms](docs/transform.md)\n\nAdvanced usage:\n- 🔀 [Piping multiple subprocesses](docs/pipe.md)\n- ⏳️ [Streams](docs/streams.md)\n- 📞 [Inter-process communication](docs/ipc.md)\n- 🐛 [Debugging](docs/debugging.md)\n- 📎 [Windows](docs/windows.md)\n- 🔍 [Difference with Bash and zx](docs/bash.md)\n- 🐭 [Small packages](docs/small.md)\n- 🤓 [TypeScript](docs/typescript.md)\n- 📔 [API reference](docs/api.md)\n\n## Examples\n\n### Execution\n\n#### Simple syntax\n\n```js\nimport {execa} from 'execa';\n\nconst {stdout} = await execa`npm run build`;\n// Print command's output\nconsole.log(stdout);\n```\n\n#### Script\n\n```js\nimport {$} from 'execa';\n\nconst {stdout: name} = await $`cat package.json`.pipe`grep name`;\nconsole.log(name);\n\nconst branch = await $`git branch --show-current`;\nawait $`dep deploy --branch=${branch}`;\n\nawait Promise.all([\n\t$`sleep 1`,\n\t$`sleep 2`,\n\t$`sleep 3`,\n]);\n\nconst directoryName = 'foo bar';\nawait $`mkdir /tmp/${directoryName}`;\n```\n\n#### Local binaries\n\n```sh\n$ npm install -D eslint\n```\n\n```js\nawait execa({preferLocal: true})`eslint`;\n```\n\n#### Pipe multiple subprocesses\n\n```js\nconst {stdout, pipedFrom} = await execa`npm run build`\n\t.pipe`sort`\n\t.pipe`head -n 2`;\n\n// Output of `npm run build | sort | head -n 2`\nconsole.log(stdout);\n// Output of `npm run build | sort`\nconsole.log(pipedFrom[0].stdout);\n// Output of `npm run build`\nconsole.log(pipedFrom[0].pipedFrom[0].stdout);\n```\n\n### Input/output\n\n#### Interleaved output\n\n```js\nconst {all} = await execa({all: true})`npm run build`;\n// stdout + stderr, interleaved\nconsole.log(all);\n```\n\n#### Programmatic + terminal output\n\n```js\nconst {stdout} = await execa({stdout: ['pipe', 'inherit']})`npm run build`;\n// stdout is also printed to the terminal\nconsole.log(stdout);\n```\n\n#### Simple input\n\n```js\nconst getInputString = () => { /* ... */ };\nconst {stdout} = await execa({input: getInputString()})`sort`;\nconsole.log(stdout);\n```\n\n#### File input\n\n```js\n// Similar to: npm run build < input.txt\nawait execa({stdin: {file: 'input.txt'}})`npm run build`;\n```\n\n#### File output\n\n```js\n// Similar to: npm run build > output.txt\nawait execa({stdout: {file: 'output.txt'}})`npm run build`;\n```\n\n#### Split into text lines\n\n```js\nconst {stdout} = await execa({lines: true})`npm run build`;\n// Print first 10 lines\nconsole.log(stdout.slice(0, 10).join('\\n'));\n```\n\n### Streaming\n\n#### Iterate over text lines\n\n```js\nfor await (const line of execa`npm run build`) {\n\tif (line.includes('WARN')) {\n\t\tconsole.warn(line);\n\t}\n}\n```\n\n#### Transform/filter output\n\n```js\nlet count = 0;\n\n// Filter out secret lines, then prepend the line number\nconst transform = function * (line) {\n\tif (!line.includes('secret')) {\n\t\tyield `[${count++}] ${line}`;\n\t}\n};\n\nawait execa({stdout: transform})`npm run build`;\n```\n\n#### Web streams\n\n```js\nconst response = await fetch('https://example.com');\nawait execa({stdin: response.body})`sort`;\n```\n\n#### Convert to Duplex stream\n\n```js\nimport {execa} from 'execa';\nimport {pipeline} from 'node:stream/promises';\nimport {createReadStream, createWriteStream} from 'node:fs';\n\nawait pipeline(\n\tcreateReadStream('./input.txt'),\n\texeca`node ./transform.js`.duplex(),\n\tcreateWriteStream('./output.txt'),\n);\n```\n\n### IPC\n\n#### Exchange messages\n\n```js\n// parent.js\nimport {execaNode} from 'execa';\n\nconst subprocess = execaNode`child.js`;\nawait subprocess.sendMessage('Hello from parent');\nconst message = await subprocess.getOneMessage();\nconsole.log(message); // 'Hello from child'\n```\n\n```js\n// child.js\nimport {getOneMessage, sendMessage} from 'execa';\n\nconst message = await getOneMessage(); // 'Hello from parent'\nconst newMessage = message.replace('parent', 'child'); // 'Hello from child'\nawait sendMessage(newMessage);\n```\n\n#### Any input type\n\n```js\n// main.js\nimport {execaNode} from 'execa';\n\nconst ipcInput = [\n\t{task: 'lint', ignore: /test\\.js/},\n\t{task: 'copy', files: new Set(['main.js', 'index.js']),\n}];\nawait execaNode({ipcInput})`build.js`;\n```\n\n```js\n// build.js\nimport {getOneMessage} from 'execa';\n\nconst ipcInput = await getOneMessage();\n```\n\n#### Any output type\n\n```js\n// main.js\nimport {execaNode} from 'execa';\n\nconst {ipcOutput} = await execaNode`build.js`;\nconsole.log(ipcOutput[0]); // {kind: 'start', timestamp: date}\nconsole.log(ipcOutput[1]); // {kind: 'stop', timestamp: date}\n```\n\n```js\n// build.js\nimport {sendMessage} from 'execa';\n\nconst runBuild = () => { /* ... */ };\n\nawait sendMessage({kind: 'start', timestamp: new Date()});\nawait runBuild();\nawait sendMessage({kind: 'stop', timestamp: new Date()});\n```\n\n#### Graceful termination\n\n```js\n// main.js\nimport {execaNode} from 'execa';\n\nconst controller = new AbortController();\nsetTimeout(() => {\n\tcontroller.abort();\n}, 5000);\n\nawait execaNode({\n\tcancelSignal: controller.signal,\n\tgracefulCancel: true,\n})`build.js`;\n```\n\n```js\n// build.js\nimport {getCancelSignal} from 'execa';\n\nconst cancelSignal = await getCancelSignal();\nconst url = 'https://example.com/build/info';\nconst response = await fetch(url, {signal: cancelSignal});\n```\n\n### Debugging\n\n#### Detailed error\n\n```js\nimport {execa, ExecaError} from 'execa';\n\ntry {\n\tawait execa`unknown command`;\n} catch (error) {\n\tif (error instanceof ExecaError) {\n\t\tconsole.log(error);\n\t}\n\t/*\n\tExecaError: Command failed with ENOENT: unknown command\n\tspawn unknown ENOENT\n\t\t\tat ...\n\t\t\tat ... {\n\t\tshortMessage: 'Command failed with ENOENT: unknown command\\nspawn unknown ENOENT',\n\t\toriginalMessage: 'spawn unknown ENOENT',\n\t\tcommand: 'unknown command',\n\t\tescapedCommand: 'unknown command',\n\t\tcwd: '/path/to/cwd',\n\t\tdurationMs: 28.217566,\n\t\tfailed: true,\n\t\ttimedOut: false,\n\t\tisCanceled: false,\n\t\tisTerminated: false,\n\t\tisMaxBuffer: false,\n\t\tcode: 'ENOENT',\n\t\tstdout: '',\n\t\tstderr: '',\n\t\tstdio: [undefined, '', ''],\n\t\tpipedFrom: []\n\t\t[cause]: Error: spawn unknown ENOENT\n\t\t\t\tat ...\n\t\t\t\tat ... {\n\t\t\terrno: -2,\n\t\t\tcode: 'ENOENT',\n\t\t\tsyscall: 'spawn unknown',\n\t\t\tpath: 'unknown',\n\t\t\tspawnargs: [ 'command' ]\n\t\t}\n\t}\n\t*/\n}\n```\n\n#### Verbose mode\n\n```js\nawait execa`npm run build`;\nawait execa`npm run test`;\n```\n\n<img alt=\"execa verbose output\" src=\"media/verbose.png\" width=\"603\">\n\n#### Custom logging\n\n```js\nimport {execa as execa_} from 'execa';\nimport {createLogger, transports} from 'winston';\n\n// Log to a file using Winston\nconst transport = new transports.File({filename: 'logs.txt'});\nconst logger = createLogger({transports: [transport]});\nconst LOG_LEVELS = {\n\tcommand: 'info',\n\toutput: 'verbose',\n\tipc: 'verbose',\n\terror: 'error',\n\tduration: 'info',\n};\n\nconst execa = execa_({\n\tverbose(verboseLine, {message, ...verboseObject}) {\n\t\tconst level = LOG_LEVELS[verboseObject.type];\n\t\tlogger[level](message, verboseObject);\n\t},\n});\n\nawait execa`npm run build`;\nawait execa`npm run test`;\n```\n\n## Related\n\n- [nano-spawn](https://github.com/sindresorhus/nano-spawn) - Like Execa but [smaller](docs/small.md)\n- [gulp-execa](https://github.com/ehmicky/gulp-execa) - Gulp plugin for Execa\n- [nvexeca](https://github.com/ehmicky/nvexeca) - Run Execa using any Node.js version\n\n## Maintainers\n\n- [Sindre Sorhus](https://github.com/sindresorhus)\n- [@ehmicky](https://github.com/ehmicky)\n", "readmeFilename": "readme.md", "users": {"l3au": true, "tg-z": true, "adius": true, "hdusy": true, "pftom": true, "timdp": true, "arttse": true, "d-band": true, "daizch": true, "garthk": true, "king.v": true, "chaoliu": true, "huhgawz": true, "liunian": true, "yanghcc": true, "abhisekp": true, "aidenzou": true, "alexwang": true, "thing772": true, "zuojiang": true, "antixrist": true, "bobxuyang": true, "reyronald": true, "shadowwzw": true, "tjfwalker": true, "xiechao06": true, "xudaolong": true, "yang.shao": true, "brainpoint": true, "eliverlara": true, "piecioshka": true, "pillar0514": true, "santi8ago8": true, "shuoshubao": true, "uxabdullah": true, "fengmiaosen": true, "flumpus-dev": true, "houzhanfeng": true, "kodekracker": true, "andrewjensen": true, "chocolateboy": true, "jamescostian": true, "tommytroylin": true, "serge-nikitin": true, "shervinafshar": true, "ricardogobbosouza": true}}